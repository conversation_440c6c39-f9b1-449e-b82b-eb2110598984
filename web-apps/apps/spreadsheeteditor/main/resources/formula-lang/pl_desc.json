{"DATE": {"a": "(rok; miesiąc; dzień)", "d": "Zwraca liczbę reprezentującą datę w kodzie data-godzina", "ad": "- liczba od 1900 lub 1904 (zależnie od systemu dat skoroszytu) do 9999!- liczba od 1 do 12 reprezentująca miesiąc roku!- liczba od 1 do 31 reprezentująca dzień miesiąca"}, "DATEDIF": {"a": "(data_początkowa; data_końcowa; jednostka)", "d": "Oblicza liczbę dni, mi<PERSON><PERSON><PERSON> lub lat między dwiema datami", "ad": "Data reprezentująca pierwszą lub początkową datę danego okresu!data reprezentująca ostatnią lub końcową datę okresu!Typ informacji, które mają zostać zwrócone"}, "DATEVALUE": {"a": "(data_tekst)", "d": "Konwertuje datę w postaci tekstu na liczbę reprezentującą datę w kodzie data-godzina", "ad": "- tekst reprezentujący datę w formacie daty programu Spreadsheet Editor z przedziału od 1.1.1900 lub 1.1.1904 (zależnie od systemu dat skoroszytu) do 31.12.9999"}, "DAY": {"a": "(kolejna_liczba)", "d": "Zwraca dzień miesiąca, liczbę od 1 do 31.", "ad": "- liczba w kodzie data-godzina używanym w programie Spreadsheet Editor"}, "DAYS": {"a": "(data_końcowa; data_początkowa)", "d": "Zwraca liczbę dni zawartych między dwiema datami.", "ad": "— data_początkowa i data_końcowa to dwie daty, dla których ma zostać określona liczba dni zawartych między nimi.!— data_początkowa i data_końcowa to dwie daty, dla których ma zostać określona liczba dni zawartych między nimi."}, "DAYS360": {"a": "(data_początkowa; data_końcowa; [metoda])", "d": "Oblicza liczbę dni zawartych między dwiema datami przyjmując rok liczący 360 dni (dwanaście 30-dniowych miesięcy)", "ad": "- data_początkowa i data_końcowa są dwiema datami, mi<PERSON><PERSON><PERSON> którymi wyznaczana jest liczba dni!- data_początkowa i data_końcowa są dwiema datami, międ<PERSON> którymi wyznaczana jest liczba dni!- określa metodę przeliczenia. Je<PERSON><PERSON> nie podano wartości albo jest nią FAŁSZ, użyta zostanie metoda USA (NASD); je<PERSON><PERSON> warto<PERSON> jest PRAWDA, użyta zostanie metoda europejska."}, "EDATE": {"a": "(data_pocz; miesiące)", "d": "Zwraca warto<PERSON>ć liczby seryjnej daty, przypadaj<PERSON><PERSON>j podaną liczbę miesięcy przed lub po dacie początkowej", "ad": "– liczba seryjna reprezentująca datę początkową!– liczba miesięcy przed lub po dacie początkowej"}, "EOMONTH": {"a": "(data_pocz; miesiące)", "d": "Zwraca warto<PERSON> liczby seryjnej daty ostatniego dnia miesiąca przed lub po podanej liczbie miesięcy", "ad": "– liczba seryjna reprezentująca datę początkową!– liczba miesięcy przed lub po dacie początkowej"}, "HOUR": {"a": "(kolejna_liczba)", "d": "Zwraca godzinę jako liczbę od 0 (0:00) do 23 (23:00).", "ad": "- liczba w kodzie data-godzin<PERSON> używanym w programie Spreadsheet Editor albo tekst w formacie godziny, ta<PERSON> jak 16:48:00 albo 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(data)", "d": "Zwraca dla danej daty numer tygodnia roku w formacie ISO.", "ad": "- kod data-godzina używany przez program Spreadsheet Editor do obliczania daty i godziny"}, "MINUTE": {"a": "(kolejna_liczba)", "d": "<PERSON><PERSON><PERSON><PERSON> min<PERSON>ę, <PERSON><PERSON><PERSON><PERSON> od 0 do 59.", "ad": "- liczba w kodzie data-godzina używanym w programie Spreadsheet Editor lub tekst w formacie godziny, na przykład 16:48:00 albo 4:48:00 PM"}, "MONTH": {"a": "(kolejna_liczba)", "d": "Zw<PERSON><PERSON>, liczbę od 1 (styczeń) do 12 (grudzień).", "ad": "- liczba w kodzie data-godzina używanym w programie Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(data_pocz; data_końc; [świ<PERSON>ta])", "d": "Zwraca liczbę dni roboczych pomiędzy dwiema datami", "ad": "– liczba seryjna reprezentująca datę początkową!– liczba seryjna reprezentująca datę końcową!– opcjonalna tablica jednej lub więcej liczb seryjnych daty do wyłączenia z kalendarza dni roboczych, jak np. święta państwowe lub kościelne"}, "NETWORKDAYS.INTL": {"a": "(data_pocz; data_końc; [weekend]; [świ<PERSON>ta])", "d": "Zwraca liczbę dni roboczych między dwiema datami z niestandardowymi parametrami dotyczącymi weekendów", "ad": "- liczba seryjna reprezentująca datę początkową!- liczba seryjna reprezentująca datę końcową!- liczba lub ciąg określający, kiedy występują weekendy!- opcjonalny zestaw zawierający jedną lub więcej liczb seryjnych dat do wykluczenia z kalendarza dni roboczych, na przykład święta państwowe lub święta o zmiennej dacie występowania"}, "NOW": {"a": "()", "d": "Zwraca bieżącą datę i godzinę sformatowane jako data i godzina.", "ad": ""}, "SECOND": {"a": "(kolejna_liczba)", "d": "Zw<PERSON><PERSON>ę, liczbę od 0 do 59.", "ad": "- liczba w kodzie data-godzina używanym w programie Spreadsheet Editor lub tekst w formacie godziny, na przykład 16:48:23 albo 4:48:47 PM"}, "TIME": {"a": "(god<PERSON>a; minuta; sekunda)", "d": "Konwer<PERSON><PERSON>ziny, minuty i sekundy dane jako liczby na liczby kolejne, sformatowane za pomocą formatu czasu", "ad": "- liczba od 0 do 23 reprezentująca godzinę!- liczba od 0 do 59 reprezentująca minutę!- liczba od 0 do 59 reprezentująca sekundę"}, "TIMEVALUE": {"a": "(godzin<PERSON>_tekst)", "d": "Konwertuje czas w formacie tekstowym na kolejną liczbę czasu: liczbę od 0 (00:00:00) do 0,999988426 (23:59:59). Liczbę należy formatować za pomocą formatu czasu po wprowadzeniu formuły", "ad": "- ciąg tekstowy podający czas w dowolnym formacie czasu programu Spreadsheet Editor (informacje o dacie w ciągu są ignorowane)"}, "TODAY": {"a": "()", "d": "Zwraca datę bieżącą sformatowaną jako datę.", "ad": ""}, "WEEKDAY": {"a": "(licz<PERSON>_kolejna; [zwracany_typ])", "d": "Zwraca liczbę od 1 do 7, określającą numer dnia tygodnia na podstawie daty.", "ad": "- liczba reprezentująca datę!- liczba: dla numeracji od niedzieli=1 do soboty=7 użyj 1; od poniedziałku=1 do niedzieli=7 użyj 2; od poniedziałku=0 do niedzieli=6 użyj 3"}, "WEEKNUM": {"a": "(licz<PERSON>_seryjna; [typ_wyniku])", "d": "Zwraca numer tygodnia w roku", "ad": "– liczba seryjna daty i czasy, używana przez program Spreadsheet Editor przy przeliczaniu daty i czasu!– liczba (1 lub 2) określająca typ zwracanej wartości"}, "WORKDAY": {"a": "(data_pocz; dni; [ś<PERSON><PERSON>ta])", "d": "Zwrac<PERSON> warto<PERSON> liczby seryjnej daty przed lub po podanej liczbie dni roboczych", "ad": "– liczba seryjna reprezentująca datę początkową!– liczba dni roboczych (nie koniec tyg. i nie święta) przed lub po dacie początkowej!– opcjonalna tablica jednej lub więcej liczb seryjnych daty do wyłączenia z kalendarza dni roboczych, jak np. święta państwowe lub kościelne"}, "WORKDAY.INTL": {"a": "(data_pocz; dni; [weekend]; [święta])", "d": "Zwraca liczbę seryjną daty przypadającej przed lub po określonej liczbie dni roboczych z niestandardowymi parametrami weekendów", "ad": "- liczba seryjna reprezentująca datę początkową!- liczba dni roboczych (nieprzypadających w weekendy i święta) przed datą data_pocz lub po tej dacie!- liczba lub ciąg ok<PERSON>, kiedy występują weekendy!- opcjonalna tablica zawierająca jedną lub więcej liczb seryjnych dat do wykluczenia z kalendarza dni roboczych, na przykład święta państwowe lub święta o zmiennej dacie występowania"}, "YEAR": {"a": "(kolejna_liczba)", "d": "Zwraca rok z daty, liczbę całkowitą z zakresu 1900-9999.", "ad": "- liczba w kodzie data-godzina używanym w programie Spreadsheet Editor"}, "YEARFRAC": {"a": "(data_pocz; data_końc; [podstawa])", "d": "<PERSON><PERSON><PERSON>, jak<PERSON> czę<PERSON>ć roku stanowi pełna liczba dni pomiędzy datą początkową i końcową", "ad": "– liczba seryjna reprezentująca datę początkową!– liczba seryjna reprezentująca datę końcową!– typ przyjętego systemu liczenia dni"}, "BESSELI": {"a": "(x; n)", "d": "Zwrac<PERSON> war<PERSON> zmodyfikowanej funkcji Bessela In(x)", "ad": "– <PERSON><PERSON><PERSON><PERSON>, przy której należy wyliczyć funkcję!– rząd funkcji Bessela"}, "BESSELJ": {"a": "(x; n)", "d": "Zwrac<PERSON> funkcji Bessela Jn(x)", "ad": "– <PERSON><PERSON><PERSON><PERSON>, przy której należy wyliczyć funkcję!– rząd funkcji Bessela"}, "BESSELK": {"a": "(x; n)", "d": "Zwrac<PERSON> zmodyfikowanej funkcji Kn(x)", "ad": "– <PERSON><PERSON><PERSON><PERSON>, przy której należy wyliczyć funkcję!– rząd funkcji Bessela"}, "BESSELY": {"a": "(x; n)", "d": "Zwrac<PERSON> funkcji Bessela Yn(x)", "ad": "– <PERSON><PERSON><PERSON><PERSON>, przy której należy wyliczyć funkcję!– rząd funkcji Bessela"}, "BIN2DEC": {"a": "(liczba)", "d": "Przekształca liczbę dwójkową na dziesiętną", "ad": "– liczba dwójkowa poddana przekształceniu"}, "BIN2HEX": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę dwójkową na liczbę w kodzie szesnastkowym", "ad": "– liczba dwójkowa poddana przekształceniu!– liczba znaków dla wartości wynikowej"}, "BIN2OCT": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę dwójkową na liczbę w kodzie ósemkowym", "ad": "– liczba dwójkowa poddana przekształceniu!– liczba znaków dla wartości wynikowej"}, "BITAND": {"a": "(liczba1; liczba2)", "d": "Zwraca wynik operacji bitowej AND (ORAZ) dwóch liczb.", "ad": "— reprezentacja dziesiętna liczby dwójkowej, która ma zostać wyznaczona.!— reprezentacja dziesiętna liczby dwójkowej, która ma zostać wyznaczona."}, "BITLSHIFT": {"a": "(liczba; liczba_przesunięć)", "d": "Zwraca liczbę przesuniętą w lewo o liczbę bitów liczba_przesunięć.", "ad": "— reprezentacja dziesiętna liczby dwójkowej, która ma zostać wyznaczona.!— liczba bitów, o jaką liczba ma zostać przesunięta w lewo."}, "BITOR": {"a": "(liczba1; liczba2)", "d": "Zwraca wynik operacji bitowej OR (LUB) dwóch liczb.", "ad": "— reprezentacja dziesiętna liczby dwójkowej, która ma zostać wyznaczona.!— reprezentacja dziesiętna liczby dwójkowej, która ma zostać wyznaczona."}, "BITRSHIFT": {"a": "(liczba; liczba_przesunięć)", "d": "Zwraca liczbę przesuniętą w prawo o liczbę bitów liczba_przesunięć.", "ad": "— reprezentacja dziesiętna liczby dwójkowej, która ma zostać wyznaczona.!— liczba bitów, o jaką liczba ma zostać przesunięta w prawo."}, "BITXOR": {"a": "(liczba1; liczba2)", "d": "Zwraca wynik operacji bitowej XOR (wyłączne LUB) dwóch liczb.", "ad": "— reprezentacja dziesiętna liczby dwójkowej, która ma zostać wyznaczona.!— reprezentacja dziesiętna liczby dwójkowej, która ma zostać wyznaczona."}, "COMPLEX": {"a": "(cz<PERSON><PERSON><PERSON>_rzecz; cz<PERSON><PERSON><PERSON>_uroj; [jednost<PERSON>_uroj])", "d": "Przekształca współczynniki rzeczywisty i urojony w liczbę zespoloną", "ad": "– cz<PERSON><PERSON><PERSON> rzeczywista liczby zespolonej!– część urojona liczby zespolonej!– symbol jednostki urojonej wykorzystywanej w zapisie liczb zespolonych (i lub j)"}, "CONVERT": {"a": "(l<PERSON><PERSON>; jednostka_we; jednostka_wy)", "d": "Zamienia liczbę z jednego systemu miar na inny", "ad": "– wartość w jednostkach wejściowych poddawana przekształceniu!– jednostka dla przekształcanej liczby!– jednostka dla wyniku"}, "DEC2BIN": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę dziesiętną na liczbę w kodzie dwójkowym", "ad": "– liczba dziesiętna poddana przekształceniu!– liczba znaków dla wartości wynikowej"}, "DEC2HEX": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę dziesiętną na liczbę w kodzie szesnastkowym", "ad": "– liczba dziesiętna poddana przekształceniu!– liczba znaków dla wartości wynikowej"}, "DEC2OCT": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę dziesiętną na liczbę w kodzie ósemkowym", "ad": "– liczba dziesiętna poddana przekształceniu!– liczba znaków dla wartości wynikowej"}, "DELTA": {"a": "(liczba1; [liczba2])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy dwie liczby są równe", "ad": "– pierwsza liczba!– druga liczba"}, "ERF": {"a": "(dolna_granica; [górna_granica])", "d": "Zwraca funk<PERSON>ję błędu", "ad": "– dolne ograniczenie przy wyznaczaniu ERF!– górne ograniczenie przy wyznaczaniu ERF"}, "ERF.PRECISE": {"a": "(X)", "d": "Zwraca funk<PERSON>ję błędu", "ad": "- dolne ograniczenie przy wyznaczaniu wartości FUNKCJA.BŁ.DOKŁ"}, "ERFC": {"a": "(x)", "d": "Zwraca komplementarną funk<PERSON>ję błędu", "ad": "– dolne ograniczenie przy wyznaczaniu ERF"}, "ERFC.PRECISE": {"a": "(X)", "d": "Zwraca komplementarną funk<PERSON>ję błędu", "ad": "- dolne ograniczenie przy wyznaczaniu wartości KOMP.FUNKCJA.BŁ.DOKŁ"}, "GESTEP": {"a": "(liczba; [próg])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy liczba jest większa niż podana wartość progowa", "ad": "– sprawd<PERSON>a warto<PERSON>!– wartość progowa"}, "HEX2BIN": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę szesnastkową na liczbę w kodzie dwójkowym", "ad": "– liczba szesnastkowa poddana przekształceniu!– liczba znaków dla wartości wynikowej"}, "HEX2DEC": {"a": "(liczba)", "d": "Przekształca liczbę szesnastkową na dziesiętną", "ad": "– liczba szesnastkowa poddana przekształceniu"}, "HEX2OCT": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę szesnastkową na liczbę w kodzie ósemkowym", "ad": "– liczba szesnastkowa poddana przekształceniu!– liczba znaków dla wartości wynikowej"}, "IMABS": {"a": "(liczba_zesp)", "d": "Zwrac<PERSON> warto<PERSON> bezwzględną (moduł) liczby zespolonej", "ad": "– liczba zespolona poddana działaniu"}, "IMAGINARY": {"a": "(liczba_zesp)", "d": "Zwraca część urojoną liczby zespolonej", "ad": "– liczba zespolona poddana działaniu"}, "IMARGUMENT": {"a": "(liczba_zesp)", "d": "Zwraca wartość argumentu liczby zespolonej, kąta wyrażonego w radianach", "ad": "– liczba zespolona poddana działaniu"}, "IMCONJUGATE": {"a": "(liczba_zesp)", "d": "Zwraca wartość sprzężoną liczby zespolonej", "ad": "– liczba zespolona poddana działaniu"}, "IMCOS": {"a": "(liczba_zesp)", "d": "Zwrac<PERSON> war<PERSON> cosinusa liczby zespolonej", "ad": "– liczba zespolona poddana działaniu"}, "IMCOSH": {"a": "(liczba_zespolona)", "d": "Zwraca cosinus hiperboliczny liczby zespolonej.", "ad": "– l<PERSON><PERSON> zespolona, dla której ma zostać obliczony cosinus hiperboliczny."}, "IMCOT": {"a": "(liczba_zespolona)", "d": "Zwraca cotangens liczby zespolonej.", "ad": "- l<PERSON><PERSON> zespolona, dla której ma zostać obliczony cotangens."}, "IMCSC": {"a": "(liczba_zespolona)", "d": "Zwraca cosecans liczby zespolonej.", "ad": "- l<PERSON><PERSON> zespolona, dla której ma zostać obliczony cosecans."}, "IMCSCH": {"a": "(liczba_zespolona)", "d": "Zwraca cosecans hiperboliczny liczby zespolonej.", "ad": "- l<PERSON><PERSON> zespolona, dla której ma zostać obliczony cosecans hiperboliczny."}, "IMDIV": {"a": "(liczba_zesp1; liczba_zesp2)", "d": "Zwraca iloraz dwóch liczb zespolonych", "ad": "– liczba zespolona stanowiąca dzielną!– liczba zespolona stanowiąca dzielnik"}, "IMEXP": {"a": "(liczba_zesp)", "d": "Zwraca wartość wykładniczą liczby zespolonej", "ad": "– liczba zespolona poddana działaniu"}, "IMLN": {"a": "(liczba_zesp)", "d": "Zwraca wartość logarytmu naturalnego liczby zespolonej", "ad": "– liczba zespolona poddana działaniu"}, "IMLOG10": {"a": "(liczba_zesp)", "d": "Zwraca wartość logarytmu dziesiętnego liczby zespolonej", "ad": "– liczba zespolona poddana działaniu"}, "IMLOG2": {"a": "(liczba_zesp)", "d": "Zwraca wartość logarytmu przy podstawie 2 z liczby zespolonej", "ad": "– liczba zespolona poddana działaniu"}, "IMPOWER": {"a": "(liczba_zesp; liczba)", "d": "Zwraca wartość liczby zespolonej podniesionej do potęgi całkowitej", "ad": "– liczba zespolona poddana działaniu!– wykładnik potęgi, do której zostanie podniesiona liczba zespolona"}, "IMPRODUCT": {"a": "(liczba_zespolona1; [liczba_zespolona2]; ...)", "d": "Zwraca iloczyn od 1 do 255 liczb zespolonych", "ad": "liczba_zespolona1, liczba_zespolona2,... jest sek<PERSON><PERSON><PERSON> od 1 do 255 liczb z<PERSON><PERSON><PERSON><PERSON>, dla k<PERSON><PERSON><PERSON><PERSON> należy obliczyć iloczyn."}, "IMREAL": {"a": "(liczba_zesp)", "d": "Zwraca część rzeczywistą liczby zespolonej", "ad": "– liczba zespolona poddana działaniu"}, "IMSEC": {"a": "(liczba_zespolona)", "d": "Zwraca secans liczby zespolonej.", "ad": "- l<PERSON><PERSON> zespolona, dla której ma zostać obliczony secans."}, "IMSECH": {"a": "(liczba_zespolona)", "d": "Zwraca secans hiperboliczny liczby zespolonej.", "ad": "- l<PERSON><PERSON> zespolona, dla której ma zostać obliczony secans hiperboliczny."}, "IMSIN": {"a": "(liczba_zesp)", "d": "Zwraca wartość sinusa liczby zespolonej", "ad": "– liczba zespolona poddana działaniu"}, "IMSINH": {"a": "(liczba_zespolona)", "d": "Zwraca sinus hiperboliczny liczby zespolonej.", "ad": "– l<PERSON><PERSON> zespolona, dla której ma zostać obliczony sinus hiperboliczny."}, "IMSQRT": {"a": "(liczba_zesp)", "d": "Zwraca warto<PERSON>ć pierwiastka kwadratowego liczby zespolonej", "ad": "– liczba zespolona poddana działaniu"}, "IMSUB": {"a": "(liczba_zesp1; liczba_zesp2)", "d": "Zwraca różnicę dwóch liczb zespolonych", "ad": "– liczba zespolona, od której zostanie odjęta liczba_zesp2!– liczba zespolona, która zostanie odjęta od liczby liczba_zesp1"}, "IMSUM": {"a": "(liczba_zespolona1; [liczba_zespolona2]; ...)", "d": "Zwraca sumę liczb zespolonych", "ad": "- sekwencja zawierająca od 1 do 255 liczb zespolonych, dla których należy obliczyć sumę"}, "IMTAN": {"a": "(liczba_zespolona)", "d": "Zwraca tangens liczby zespolonej.", "ad": "- l<PERSON><PERSON> zespolona, dla której ma zostać obliczony tangens."}, "OCT2BIN": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę ósemkową na liczbę w kodzie dwójkowym", "ad": "– liczba ósemkowa poddana przekształceniu!– liczba znaków dla wartości wynikowej"}, "OCT2DEC": {"a": "(liczba)", "d": "Przekształca liczbę ósemkową na dziesiętną", "ad": "– liczba ósemkowa poddana przekształceniu"}, "OCT2HEX": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę ósemkową na liczbę w kodzie szesnastkowym", "ad": "– liczba ósemkowa poddana przekształceniu!– liczba znaków dla wartości wynikowej"}, "DAVERAGE": {"a": "(baza_danych; pole; kryteria)", "d": "Oblicza wartość średnią w kolumnie listy lub bazy danych, która spełnia określone kryteria", "ad": "- zakres komórek składający się na listę lub bazę danych. Baza danych to lista powiązanych danych!- albo etykieta kolumny w podwójnym cudzysłowie, albo liczba reprezentująca pozycję kolumny na liście!- zakres komórek zawierający określone warunki. Zakres zawiera etykietę kolumny i jedną komórkę poniżej etykiety warunku"}, "DCOUNT": {"a": "(baza; pole; kryteria)", "d": "Zlicza komórki zawierające liczby we wskazanym polu (kolumnie) rekordów bazy danych, które spełniają podane warunki", "ad": "- zakres komórek składających się na listę lub bazę danych. Baza danych jest listą powiązanych danych.!- jest albo etykietą kolumny umieszczoną w podwójnym cudzysłowie, albo numerem reprezentującym położenie kolumny na liście!- zakres komórek zawierających podane warunki. Zakres zawiera etykietę kolumny i komórkę poniżej etykiety warunku"}, "DCOUNTA": {"a": "(baza; pole; kryteria)", "d": "Zwraca liczbę niepustych komórek w polu (kolumnie) rekordów bazy danych spełniających podane kryteria", "ad": "- zakres komórek składających się na listę lub bazę danych. Baza danych jest listą powiązanych danych!- jest albo etykietą kolumny umieszczoną w podwójnym cudzysłowie, albo numerem reprezentującym położenie kolumny na liście!- zakres komórek zawierających podane warunki. Zakres zawiera etykietę kolumny i komórkę poniżej etykiety warunku"}, "DGET": {"a": "(baza; pole; kryteria)", "d": "Wydziela z bazy danych pojedynczy rekord, spełniający podane kryteria", "ad": "- zakres komórek składających się na listę lub bazę danych. Baza danych jest listą powiązanych danych.!- jest albo etykietą kolumny umieszczoną w podwójnym cudzysłowie, albo numerem reprezentującym położenie kolumny na liście!- zakres komórek zawierających podane warunki. Zakres zawiera etykietę kolumny i komórkę poniżej etykiety warunku"}, "DMAX": {"a": "(baza_danych; pole; kryteria)", "d": "Zwraca największą liczbę w polu (kolumnie) rekordów bazy danych, które spełniają określone warunki", "ad": "- zakres komórek składający się na listę lub bazę danych. Baza danych to lista powiązanych danych!- albo etykieta kolumny w podwójnym cudzysłowie, albo liczba reprezentująca pozycję kolumny na liście!- zakres komórek zawierający określone warunki. Zakres zawiera etykietę kolumny i jedną komórkę poniżej etykiety warunku"}, "DMIN": {"a": "(baza; pole; kryteria)", "d": "Zwraca minimalną wartość z pola (kolumny) rekordów bazy danych, które spełniają podane kryteria", "ad": "- zakres komórek składających się na listę lub bazę danych. Baza danych jest listą powiązanych danych.!- jest albo etykietą kolumny umieszczoną w podwójnym cudzysłowie, albo numerem reprezentującym położenie kolumny na liście!- zakres komórek zawierających podane warunki. Zakres zawiera etykietę kolumny i komórkę poniżej etykiety warunku"}, "DPRODUCT": {"a": "(baza; pole; kryteria)", "d": "Mnoży wartości umieszczone w danym polu (kolumnie) tych rekordów w bazie danych, które spełniają podane kryteria", "ad": "- zakres komórek składających się na listę lub bazę danych. Baza danych jest listą powiązanych danych.!- jest albo etykietą kolumny umieszczoną w podwójnym cudzysłowie, albo numerem reprezentującym położenie kolumny na liście!- zakres komórek zawierających podane warunki. Zakres zawiera etykietę kolumny i komórkę poniżej etykiety warunku"}, "DSTDEV": {"a": "(baza; pole; kryteria)", "d": "Oblicza odchylenie standardowe próbki składającej się z zaznaczonych pozycji bazy danych", "ad": "- zakres komórek składających się na listę lub bazę danych. Baza danych jest listą powiązanych danych.!- jest albo etykietą kolumny umieszczoną w podwójnym cudzysłowie, albo numerem reprezentującym położenie kolumny na liście!- zakres komórek zawierających podane warunki. Zakres zawiera etykietę kolumny i komórkę poniżej etykiety warunku"}, "DSTDEVP": {"a": "(baza; pole; kryteria)", "d": "Oblicza odchylenie standardowe całej populacji składającej się z zaznaczonych pozycji bazy danych", "ad": "- zakres komórek składających się na listę lub bazę danych. Baza danych jest listą powiązanych danych.!- jest albo etykietą kolumny umieszczoną w podwójnym cudzysłowie, albo numerem reprezentującym położenie kolumny na liście!- zakres komórek zawierających podane warunki. Zakres zawiera etykietę kolumny i komórkę poniżej etykiety warunku"}, "DSUM": {"a": "(baza; pole; kryteria)", "d": "Dodaje liczby umieszczone w polach (kolumnie) tych rekordów bazy danych, które spełniają podane kryteria", "ad": "- zakres komórek składających się na listę lub bazę danych. Baza danych jest listą powiązanych danych.!- jest albo etykietą kolumny umieszczoną w podwójnym cudzysłowie, albo numerem reprezentującym położenie kolumny na liście!- zakres komórek zawierających podane warunki. Zakres zawiera etykietę kolumny i komórkę poniżej etykiety warunku"}, "DVAR": {"a": "(baza; pole; kryteria)", "d": "Oblicza wariancję próbki składającej się z zaznaczonych pozycji bazy danych", "ad": "- zakres komórek składających się na listę lub bazę danych. Baza danych jest listą powiązanych danych.!- jest albo etykietą kolumny umieszczoną w podwójnym cudzysłowie, albo numerem reprezentującym położenie kolumny na liście!- zakres komórek zawierających podane warunki. Zakres zawiera etykietę kolumny i komórkę poniżej etykiety warunku"}, "DVARP": {"a": "(baza; pole; kryteria)", "d": "Oblicza wariancję całej populacji składającej się z zaznaczonych pozycji bazy danych", "ad": "- zakres komórek składających się na listę lub bazę danych. Baza danych jest listą powiązanych danych.!- jest albo etykietą kolumny umieszczoną w podwójnym cudzysłowie, albo numerem reprezentującym położenie kolumny na liście!- zakres komórek zawierających podane warunki. Zakres zawiera etykietę kolumny i komórkę poniżej etykiety warunku"}, "CHAR": {"a": "(liczba)", "d": "Zwraca znak określony przez numer w kodzie zestawu znaków używanego w komputerze", "ad": "- liczba z zakresu od 1 do 255 określająca, który znak zostanie zwrócony"}, "CLEAN": {"a": "(tekst)", "d": "Usuwa z tekstu wszystkie znaki, które nie mogą by<PERSON> drukowane", "ad": "- dowolne informacje z arkusza, z których mają być usunięte niedrukowalne znaki"}, "CODE": {"a": "(tekst)", "d": "Zwraca kod liczbowy pierwszego znaku w tekście odpowiadający zestawowi znaków używanemu w komputerze", "ad": "- te<PERSON><PERSON>, z którego zostanie pobrany kod pierwszego znaku"}, "CONCATENATE": {"a": "(tekst1; [tekst2]; ...)", "d": "Łączy kilka ciągów tekstowych w jeden ciąg", "ad": "- od 1 do 255 ciąg<PERSON> tekstowych, kt<PERSON>re mają zostać połączone w jeden ciąg tekstowy; mogą to być ciągi tekstowe, liczby lub odwołania do pojedynczych komórek"}, "CONCAT": {"a": "(tekst1; ...)", "d": "Łącz<PERSON> listę lub zakres ciągów tekstowych", "ad": "— od 1 do 254 ciągów tekstowych lub zakresów, które mają zostać połączone w jeden ciąg tekstowy"}, "DOLLAR": {"a": "(l<PERSON>ba; [miej<PERSON>_d<PERSON><PERSON>])", "d": "Konwertuje liczbę na tekst, korzystając z formatu walutowego", "ad": "- l<PERSON><PERSON>, odwołanie do komórki zawierającej liczbę lub formuła dająca w wyniku liczbę!- liczba cyfr po przecinku. Liczba jest zaokrąglana zgodnie z wymaganiami; gdy jest pominięta, wynosi 2."}, "EXACT": {"a": "(tekst1; tekst2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy dwa ciągi tekstowe są identyczne, i zwraca wartość PRAWDA albo FAŁSZ. Funkcja PORÓWNAJ uwzględnia wielkość znaków", "ad": "- pier<PERSON>zy ciąg znaków!- drugi ciąg znaków"}, "FIND": {"a": "(szukany_tekst; w_tekście; [licz<PERSON>_pocz<PERSON>tkowa])", "d": "Zwraca pozycję początkową jednego ciągu tekstowego w drugim ciągu tekstowym. Funkcja ZNAJDŹ uwzględnia wielkość liter", "ad": "- tekst do znalezienia. Użyj podwójnego cudzysłowu (pusty tekst), aby dopas<PERSON>ć pierwszy znak w argumencie wewnątrz_tekst; symbole wieloznaczne są niedozwolone!- tekst zawierający szukany tekst!- ok<PERSON><PERSON><PERSON> znak, od którego ma się rozpocząć wyszukiwanie. Pier<PERSON>zy znak w argumencie wewnątrz_tekst to znak o numerze 1. <PERSON><PERSON><PERSON>, liczba_początkowa =1"}, "FINDB": {"a": "(szukany_tekst; w_tekście; [licz<PERSON>_pocz<PERSON>tkowa])", "d": "Lokalizują ciąg tekstowy wewnątrz innego ciągu tekstowego i zwracają pozycję początkową pierwszego ciągu, licząc od pierwszego znaku drugiego ciągu, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański", "ad": "- tekst do znalezienia. Użyj podwójnego cudzysłowu (pusty tekst), aby dopas<PERSON>ć pierwszy znak w argumencie wewnątrz_tekst; symbole wieloznaczne są niedozwolone!- tekst zawierający szukany tekst!- ok<PERSON><PERSON><PERSON> znak, od którego ma się rozpocząć wyszukiwanie. Pier<PERSON>zy znak w argumencie wewnątrz_tekst to znak o numerze 1. <PERSON><PERSON><PERSON>, liczba_początkowa =1"}, "FIXED": {"a": "(liczba; [mi<PERSON><PERSON>_d<PERSON><PERSON>]; [bez_<PERSON>rz<PERSON><PERSON><PERSON>])", "d": "Zaokrągla liczbę do określonej liczby miejsc po przecinku i zwraca wynik jako tekst ze spacjami lub bez", "ad": "- l<PERSON><PERSON>, kt<PERSON>ra ma być zaokrąglona i konwertowana na tekst!- liczba cyfr po prawej stronie przecinka dziesiętnego. <PERSON><PERSON><PERSON> jest pomini<PERSON>ta, równa się 2.!- war<PERSON><PERSON><PERSON> <PERSON>, czy w liczbie nie mają być wyświetlane separatory tysięcy = PRAWDA; lub czy mają być wyświetlane = FAŁSZ lub pominięte"}, "LEFT": {"a": "(tekst; [licz<PERSON>_znak<PERSON>])", "d": "Zwraca określoną liczbę znaków od początku ciągu tekstowego", "ad": "- ciąg tekstowy zawierający znaki do wyodrębnienia!- <PERSON><PERSON><PERSON><PERSON>, ile znaków ma być wyodrębnionych przez funkcję LEWY; j<PERSON><PERSON><PERSON> pominięto, 1"}, "LEFTB": {"a": "(tekst; [licz<PERSON>_znak<PERSON>])", "d": "Zwraca pierwsze znaki w ciągu tekstowym na podstawie określonej liczby znaków, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański", "ad": "- ciąg tekstowy zawierający znaki do wyodrębnienia!- <PERSON><PERSON><PERSON><PERSON>, ile znaków ma być wyodrębnionych przez funkcję LEFTB; je<PERSON><PERSON> pominięto, 1"}, "LEN": {"a": "(tekst)", "d": "Zwraca liczbę znaków w ciągu znaków", "ad": "- <PERSON><PERSON><PERSON>, którego długość ma zostać obliczona. Spacje liczą się za znaki"}, "LENB": {"a": "(tekst)", "d": "Zwraca liczbę bajtów reprezentujących znaki w ciągu tekstowym, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański", "ad": "- <PERSON><PERSON><PERSON>, którego długość ma zostać obliczona. Spacje liczą się za znaki"}, "LOWER": {"a": "(tekst)", "d": "Konwertuje wszystkie litery w ciągu tekstowym na małe litery", "ad": "- te<PERSON><PERSON>, kt<PERSON><PERSON> ma być konwertowany na małe litery. Znaki w tekście, które nie są literami, nie są zmieniane"}, "MID": {"a": "(tekst; liczba_początkowa; liczba_znaków)", "d": "Zwraca znaki ze środka ciągu tekstowego przy danej pozycji początkowej i długości", "ad": "- ciąg znaków, z którego mają być wyodrębnione znaki!- pozycja pierwszego znaku, który ma być wyodrębniony. Pierwszy znak w tekście to 1!- <PERSON><PERSON><PERSON><PERSON>, ile znaków ma zostać zwróconych z tekstu"}, "MIDB": {"a": "(tekst; liczba_początkowa; liczba_znaków)", "d": "Zwraca określoną liczbę znaków z ciągu tekstowego, począwszy od określonej pozycji, na podstawie podanej liczby bajtów, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański", "ad": "- ciąg znaków, z którego mają być wyodrębnione znaki!- pozycja pierwszego znaku, który ma być wyodrębniony. Pierwszy znak w tekście to 1!- <PERSON><PERSON><PERSON><PERSON>, ile znaków ma zostać zwróconych z tekstu"}, "NUMBERVALUE": {"a": "(tekst; [separator_d<PERSON><PERSON>ny]; [separator_grup])", "d": "Konwertuje tekst na liczbę w sposób niezależny od ustawień regionalnych.", "ad": "— ciąg reprezentuj<PERSON>cy liczbę, która ma zostać przekonwertowana.!— znak używany jako separator dziesiętny w ciągu.!— znak używany jako separator grup w ciągu."}, "PROPER": {"a": "(tekst)", "d": "Konwertuje ciąg tekstowy na litery właściwej wielkości; pierwszą literę w każdym wyrazie na wielką literę, a wszystkie inne litery na małe litery", "ad": "- tekst ujęty w znaki cudzysłowu, formuła zwracająca tekst albo odwołanie do komórki zawierającej tekst, którego litery mają być częściowo zamienione na wielkie litery"}, "REPLACE": {"a": "(stary_tekst; liczba_początkowa; liczba_znaków; nowy_tekst)", "d": "Zamienia część ciągu znaków innym ciągiem znaków", "ad": "- tekst, w którym chcesz zamienić niektóre znaki!- pozycja znaku w starym_tekście, który ma być zamieniony nowym_tekstem!- liczba znaków w starym_tekście, które mają być zamienione!- tekst, który zamieni znaki w starym_tekście"}, "REPLACEB": {"a": "(stary_tekst; liczba_początkowa; liczba_znaków; nowy_tekst)", "d": "Zamienia część ciągu tekstowego na inny ciąg tekstowy z uwzględnieniem określonej liczby bajtów, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański", "ad": "- tekst, w którym chcesz zamienić niektóre znaki!- pozycja znaku w starym_tekście, który ma być zamieniony nowym_tekstem!- liczba znaków w starym_tekście, które mają być zamienione!- tekst, który zamieni znaki w starym_tekście"}, "REPT": {"a": "(tekst; ile_razy)", "d": "Powtarza tekst podaną liczbę razy. Używaj funkcji POWT do wypełnienia komórki podaną liczbą wystąpień ciągu tekstowego", "ad": "- te<PERSON><PERSON>, kt<PERSON><PERSON> ma być powtarzany!- liczba dodatnia, określająca ile razy należy powtórzyć dany tekst"}, "RIGHT": {"a": "(tekst; [licz<PERSON>_znak<PERSON>])", "d": "Zwraca określoną liczbę znaków od końca ciągu tekstowego", "ad": "- tekst zawierający znaki do wyodrębnienia!ok<PERSON><PERSON><PERSON>, ile znaków ma być wyodręb<PERSON>nych; je<PERSON><PERSON> pomini<PERSON>, 1"}, "RIGHTB": {"a": "(tekst; [licz<PERSON>_znak<PERSON>])", "d": "Zwraca ostatnie znaki w ciągu tekstowym, na podstawie określonej liczby bajtów, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański", "ad": "- tekst zawierający znaki do wyodrębnienia!ok<PERSON><PERSON><PERSON>, ile znaków ma być wyodręb<PERSON>nych; je<PERSON><PERSON> pomini<PERSON>, 1"}, "SEARCH": {"a": "(szukany_tekst; obejmuj<PERSON>cy_tekst; [licz<PERSON>_pocz<PERSON>tkowa])", "d": "Zwraca numer znaku, w którym jeden ciąg znaków został znaleziony po raz pierwszy w drugim, począwszy od lewej strony (nie rozróżniając liter małych i dużych)", "ad": "jest tekstem, k<PERSON><PERSON><PERSON> ch<PERSON>. <PERSON>ż<PERSON>z użyć symboli wieloznacznych ? i *; uż<PERSON>j kombinacji ~? i ~*, aby znale<PERSON> znaki ? i *!- tekst, w którym ma nastąpić szukanie ciągu szukany_tekst!- numer znaku liczony od lewej strony w obejmujący_tekst, ustal<PERSON><PERSON><PERSON> punkt, od którego rozpocznie się poszukiwanie. Je<PERSON><PERSON> pominięto używany jest numer 1"}, "SEARCHB": {"a": "(szukany_tekst; obejmuj<PERSON>cy_tekst; [licz<PERSON>_pocz<PERSON>tkowa])", "d": "Służy do odnajdywania jednego ciągu tekstowego wewnątrz innego ciągu tekstowego i zwracania pozycji początkowej szukanego tekstu liczonej od pierwszego znaku tekstu przeszukiwanego, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański", "ad": "jest tekstem, k<PERSON><PERSON><PERSON> ch<PERSON>. <PERSON>ż<PERSON>z użyć symboli wieloznacznych ? i *; uż<PERSON>j kombinacji ~? i ~*, aby znale<PERSON> znaki ? i *!- tekst, w którym ma nastąpić szukanie ciągu szukany_tekst!- numer znaku liczony od lewej strony w obejmujący_tekst, ustal<PERSON><PERSON><PERSON> punkt, od którego rozpocznie się poszukiwanie. Je<PERSON><PERSON> pominięto używany jest numer 1"}, "SUBSTITUTE": {"a": "(tekst; stary_tekst; nowy_tekst; [wystapienie_liczba])", "d": "Zamienia istniejący tekst w ciągu nowym tekstem", "ad": "- tekst lub odwołanie do komórki zawierającej tekst, w którym nastąpi podstawienie znaków!- tekst, kt<PERSON>ry ma zostać zamieniony. Je<PERSON><PERSON> wielkość liter starego_tekstu nie odpowiada wielkości liter danego tekstu, tekst nie zostanie zamieniony!- tekst, który ma zastąpić stary_tekst!- <PERSON><PERSON><PERSON><PERSON>, które wystąpienie starego_tekstu ma zostać zamienione przez nowy_tekst. <PERSON><PERSON><PERSON> pomi<PERSON>, zamienione zostanie każde wystąpienie"}, "T": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> war<PERSON> to tekst i zwraca ten tekst, g<PERSON> warto<PERSON> jest tekstem, albo podwójny cudzysłów (pusty tekst), je<PERSON><PERSON> warto<PERSON> nie jest tekstem", "ad": "- wartość do testowania"}, "TEXT": {"a": "(wartość; format_tekst)", "d": "Konwertuje wartość na tekst w podanym formacie liczbowym", "ad": "- warto<PERSON><PERSON> liczbowa, formuła zwracająca wartość liczbową lub odwołanie do komórki zawierającej wartość liczbową!- format liczbowy w postaci tekstowej z pola Kategoria na karcie Liczby okna dialogowego Formatuj komórki"}, "TEXTJOIN": {"a": "(og<PERSON><PERSON><PERSON>; ignoruj_puste; tekst1; ...)", "d": "Łą<PERSON><PERSON> listę lub zakres ciągów tekstowych przy użyciu ogranicznika", "ad": " - znak lub ciąg, kt<PERSON><PERSON> chcesz wstawić między każdy element tekstowy! - w przypadku wartości PRAWDA (domyślnie) puste komórki będą ignorowane! - od 1 do 252 ciągów tekstowych lub zakresów do złączenia"}, "TRIM": {"a": "(tekst)", "d": "Usuwa wszystkie spacje z podanego tekstu poza pojedynczymi spacjami rozdzielającymi słowa", "ad": "- te<PERSON><PERSON>, z którego mają zostać usunięte odstępy"}, "UNICHAR": {"a": "(liczba)", "d": "Zwraca znak Unicode, do którego odwołuje się dana wartoś<PERSON> liczbowa.", "ad": "- wartość Unicode reprezentująca znak."}, "UNICODE": {"a": "(tekst)", "d": "Zwraca liczbę (punkt kodowy) odpowiadającą pierwszemu znakowi tekstu.", "ad": "- <PERSON><PERSON><PERSON>, dla którego ma zostać określona wartość Unicode."}, "UPPER": {"a": "(tekst)", "d": "Konwertuje ciąg tekstowy na wielkie litery", "ad": "- te<PERSON><PERSON>, kt<PERSON>ry ma zostać konwertowany na wielkie litery, odwołanie lub ciąg tekstowy"}, "VALUE": {"a": "(tekst)", "d": "Konwertuje ciąg tekstowy reprezentujący liczbę na liczbę", "ad": "- tekst zawarty w cudzysłowie, lub odwołanie do komórki zawierającej tekst, który ma zostać poddany konwersji"}, "AVEDEV": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca odchylenie średnie (średnia z odchyleń bezwzględnych) punktów danych od ich wartości średniej. Argumentami mogą być liczby lub nazwy, tablice albo odwołania zawierające liczby", "ad": "- od 1 do 255 <PERSON><PERSON>, dla kt<PERSON><PERSON>ch ma zostać obliczona średnia z odchyleń bezwzględnych"}, "AVERAGE": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwrac<PERSON> warto<PERSON> śred<PERSON> (śred<PERSON>ą arytmetyczną) podanych argumentów, które mogą być liczbami lub naz<PERSON>, tablicami albo odwołaniami zawierającymi liczby", "ad": "- od 1 do 255 <PERSON><PERSON>, dla któ<PERSON>ch zostanie wyznaczona wartość średnia"}, "AVERAGEA": {"a": "(wartość1; [wartość2]; ...)", "d": "Zwraca wartość średniej arytmetycznej argumentów. Tekst i wartości logiczne FAŁSZ są przyjmowane jako 0; wartości logiczne PRAWDA są przyjmowane jako 1. Argumenty mogą by<PERSON>, na<PERSON><PERSON>, tablicami lub od<PERSON>", "ad": "od 1 do 255 <PERSON><PERSON>, dla k<PERSON><PERSON><PERSON><PERSON> ma być obliczona średnia"}, "AVERAGEIF": {"a": "(zakres; kryteria; [średnia_zakres])", "d": "Oblicza średnią arytmetyczną dla komórek spełniających podany warunek lub kryteria", "ad": " - z<PERSON><PERSON> komórek, dla kt<PERSON><PERSON>ch należy wykonać obliczenia! - warunek lub kryteria w postaci licz<PERSON>, wyrażenia lub tekstu określające komórki, które zostaną użyte do obliczenia średniej! - komórki faktycznie używane do obliczenia średniej. <PERSON><PERSON><PERSON>, używane są komórki w zakresie"}, "AVERAGEIFS": {"a": "(średnia_zakres; kryteria_zakres; kryteria; ...)", "d": "Znajduje średnią arytmetyczną dla komórek spełniających podany zestaw warunków lub kryteriów", "ad": "- komórki faktycznie używane do obliczenia średniej.!- z<PERSON><PERSON> komórek, dla któ<PERSON>ch należy sprawdzić określony warunek!- warunek lub kryteria określające komórki używane do obliczenia średniej podane w postaci liczby, wyrażenia lub tekstu"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Zwraca funkcję gęstości skumulowanego rozkładu beta", "ad": "- punkt pomiędzy A i B, dla którego ma zostać wyznaczona wartość funkcji!- parametr rozkładu, musi być większy od 0!- parametr rozkładu, musi być większy od 0!- opcjonalne dolne ograniczenie przedziału wartości x. <PERSON><PERSON><PERSON> pomi<PERSON>, A = 0!- opcjonalne górne ograniczenie przedziału wartości x. <PERSON><PERSON><PERSON> pomini<PERSON>, B = 1"}, "BETAINV": {"a": "(prawdopodobieństwo; alfa; beta; [A]; [B])", "d": "Zwraca odwrotność funkcji gęstości skumulowanego rozkładu beta (ROZKŁAD.BETA)", "ad": "- prawdopodobieństwo skojarzone z danym rozkładem beta!- parametr rozkładu, musi być większy od 0!- parametr rozkładu, musi być większy od 0!- opcjonalne dolne ograniczenie przedziału wartości x. <PERSON><PERSON><PERSON> pomi<PERSON>, A = 0!- opcjonalne górne ograniczenie przedziału wartości x. <PERSON><PERSON><PERSON> pomi<PERSON>, B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; skumulowany; [A]; [B])", "d": "Zwraca funkcję rozkładu prawdopodobieństwa beta", "ad": "- wartość między A i B, dla której ma zostać obliczona wartość funkcji!- parametr rozkładu (musi być więks<PERSON> niż 0)!- parametr rozkładu (musi być większy niż 0)!- wartość logiczna: dla funkcji rozkładu skumulowanego należy użyć wartości PRAWDA; dla funkcji gęstości prawdopodobieństwa należy użyć wartości FAŁSZ!- opcjonalne dolne ograniczenie przedziału wartości x. <PERSON><PERSON><PERSON> pomini<PERSON>, to A = 0!- opcjonalne górne ograniczenie przedziału wartości x. <PERSON><PERSON><PERSON> pomi<PERSON>, to B = 1"}, "BETA.INV": {"a": "(prawdopodobieństwo; alfa; beta; [A]; [B])", "d": "Zwraca odwrotność funkcji gęstości prawdopodobieństwa skumulowanego rozkładu beta (ROZKŁ.BETA)", "ad": "- prawdopodobieństwo skojarzone z rozkładem beta!- parametr rozkładu (musi być większy niż 0)!- parametr rozkładu (musi być większy niż 0)!- opcjonalne dolne ograniczenie przedziału wartości x. <PERSON><PERSON><PERSON> pomi<PERSON>, to A = 0!- opcjonalne górne ograniczenie przedziału wartości x. <PERSON><PERSON><PERSON> pomi<PERSON>, to B = 1"}, "BINOMDIST": {"a": "(liczba_s; próby; prawdopodob_s; sku<PERSON><PERSON><PERSON>)", "d": "Zwraca pojedynczy składnik dwumianowego rozkładu prawdopodobieństwa", "ad": "- liczba sukcesów w pró<PERSON>!- liczba niezależnych prób!- prawdopodobieństwo sukcesu w pojedynczej próbie!- wartość logiczna: dla funkcji rozkładu skumulowanego użyj wartości PRAWDA; dla funkcji masy prawdopodobieństwa użyj wartości FAŁSZ"}, "BINOM.DIST": {"a": "(liczba_s; próby; prawdopodob_s; sku<PERSON><PERSON><PERSON>)", "d": "Zwraca pojedynczy składnik dwumianowego rozkładu prawdopodobieństwa", "ad": "- liczba sukcesów w pró<PERSON>!- liczba niezależnych prób!- prawdopodobieństwo sukcesu w pojedynczej próbie!- wartość logiczna: dla funkcji rozkładu <PERSON>go, użyj wartości PRAWDA; dla funkcji gęstości prawdopodobieństwa użyj wartości FAŁSZ"}, "BINOM.DIST.RANGE": {"a": "(próby; prawdopodob_s; liczba_s; [liczba_s2])", "d": "Zwraca prawdopodobieństwo wyniku próby przy użyciu rozkładu dwumianowego.", "ad": "— liczba niezależnych prób.!— prawdopodobieństwo sukcesu w pojedynczej próbie.!— liczba sukcesów w próbach.!W przypadku podania ta funkcja zwraca prawdopodobieństwo tego, że liczba pomyślnych prób będzie zawierać się między liczbami liczba_s i liczba_s2."}, "BINOM.INV": {"a": "(próby; prawdopodob_s; alfa)", "d": "Zwraca najmnie<PERSON><PERSON><PERSON>, dla której skumulowany rozkład dwumianowy jest większy lub równy podanej wartości progowej", "ad": "- liczba prób <PERSON>lliego!- prawdopodobieństwo sukcesu w pojedynczej próbie, liczba większa od 0 i mniejsza od 1!- <PERSON><PERSON><PERSON><PERSON>gowa, liczba większa od 0 i mniejsza lub równa 1"}, "CHIDIST": {"a": "(x; stopnie_swobody)", "d": "Zwraca prawostronne prawdopodobieństwo rozkładu chi-kwadrat", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać oszacowany rozkład (liczba nieujemna)!- liczba stopni swobody z przedziału od 1 do 10^10 (z wyłączeniem wartości 10^10)"}, "CHIINV": {"a": "(prawdopodobieństwo; stopnie_swobody)", "d": "Zwraca odwrot<PERSON>ść prawostronnego prawdopodobieństwa rozkładu chi-kwadrat", "ad": "- prawdopodobieństwo skojarzone z rozkładem chi-kwadrat (wartość z przedziału od 0 do 1 włącznie)!- liczba stopni swobody (liczba z przedziału od 1 do 10^10 z wyłączeniem wartości 10^10)"}, "CHITEST": {"a": "(zakres_bieżący; zakres_przewidywany)", "d": "Zwraca test na niezależność: warto<PERSON><PERSON> z rozkładu chi-kwadrat dla statystyki i odpowiednich stopni swobody", "ad": "- zakres danych zawierający wartości zaobserwowane, które mają zostać porównane z wartościami oczekiwanymi!- zakres danych zawierający stosunek iloczynu sum wierszy i sum kolumn do sumy końcowej"}, "CHISQ.DIST": {"a": "(x; stopnie_swobody; skumulowany)", "d": "Zwraca lewostronne prawdopodobieństwo rozkładu chi-kwadrat", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać oszacowany rozkład (liczba nieujemna)!- liczba stopni swobody z zakresu od 1 do 10^10 (z wyłączeniem wartości 10^10)!- warto<PERSON><PERSON> logiczna określająca zwracaną funkcję: funkcja rozkładu skumulowanego = PRAWDA; funkcja gęstości prawdopodobieństwa = FAŁSZ"}, "CHISQ.DIST.RT": {"a": "(x; stopnie_swobody)", "d": "Zwraca prawostronne prawdopodobieństwo rozkładu chi-kwadrat", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać oszacowany rozkład (liczba nieujemna)!- liczba stopni swobody z zakresu od 1 do 10^10 (z wyłączeniem wartości 10^10)"}, "CHISQ.INV": {"a": "(prawdopodobieństwo; stopnie_swobody)", "d": "Zwraca odw<PERSON><PERSON>ć lewostronnego prawdopodobieństwa rozkładu chi-kwadrat", "ad": "- prawdopodobieństwo skojarzone z rozkładem chi-kwadrat (wartość z zakresu od 0 do 1 włącznie)!- liczba stopni swobody (liczba z zakresu od 1 do 10^10, z wyłączeniem wartości 10^10)"}, "CHISQ.INV.RT": {"a": "(prawdopodobieństwo; stopnie_swobody)", "d": "Zwraca odwrot<PERSON>ść prawostronnego prawdopodobieństwa rozkładu chi-kwadrat", "ad": "- prawdopodobieństwo skojarzone z rozkładem chi-kwadrat (wartość z zakresu od 0 do 1 włącznie)!- liczba stopni swobody (liczba z zakresu od 1 do 10^10, z wyłączeniem wartości 10^10)"}, "CHISQ.TEST": {"a": "(zakres_bieżący; zakres_przewidywany)", "d": "Zwraca test na niezależność: warto<PERSON><PERSON> z rozkładu chi-kwadrat dla statystyki i odpowiednich stopni swobody", "ad": "- zakres danych zawierający wartości zaobserwowane, które mają zostać porównane z wartościami oczekiwanymi!- zakres danych zawierający stosunek iloczynu sum wierszy i sum kolumn do sumy końcowej"}, "CONFIDENCE": {"a": "(alfa; odchylenie_std; rozmiar)", "d": "Zwraca przedział ufności dla średniej populacji, używając rozkładu normalnego", "ad": "- poziom istotności używany do obliczenia poziomu ufności (liczba większa niż 0 i mniejsza niż 1)!- odchylenie standardowe populacji dla zakresu danych (z<PERSON><PERSON><PERSON> si<PERSON>, że jest znane). Parametr odchylenie_std musi być większy niż 0!- w<PERSON><PERSON><PERSON><PERSON> próbki"}, "CONFIDENCE.NORM": {"a": "(alfa; odchylenie_std; rozmiar)", "d": "Zwraca przedział ufności dla średniej populacji, używając rozkładu normalnego", "ad": "- poziom istotności używany do obliczenia poziomu ufności (liczba większa niż 0 i mniejsza niż 1)!- odchylenie standardowe populacji dla zakresu danych (z<PERSON><PERSON><PERSON> si<PERSON>, że jest znane). Parametr odchylenie_std musi być większy niż 0!- w<PERSON><PERSON><PERSON><PERSON> próbki"}, "CONFIDENCE.T": {"a": "(alfa; odchylenie_std; rozmiar)", "d": "Zwraca przedział ufności dla średniej populacji, używając rozkładu t-Studenta", "ad": "- poziom istotności używany do obliczenia poziomu ufności (liczba większa niż 0 i mniejsza niż 1)!- odchylenie standardowe populacji dla zakresu danych (z<PERSON><PERSON><PERSON> si<PERSON>, że jest znane). Parametr odchylenie_std musi być większy niż 0!- w<PERSON><PERSON><PERSON><PERSON> próbki"}, "CORREL": {"a": "(tablica1; tablica2)", "d": "Oblicza współczynnik korelacji pomiędzy dwoma zbiorami danych", "ad": "- zakres komórek warto<PERSON>. Wartości powinny być l<PERSON>, naz<PERSON>, tablicami lub odwołaniami zawierającymi liczby!- drugi zakres komórek wartości. Wartości powinny być l<PERSON>, naz<PERSON>, tablicami lub odwoła<PERSON><PERSON> zawierającymi liczby"}, "COUNT": {"a": "(wartość1; [wartość2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, ile komórek w zakresie zawiera liczby", "ad": "- od 1 do 255 <PERSON><PERSON>, które mogą zawierać lub odwoływać się do różnych typów danych, przy czym zliczane będą tylko liczby"}, "COUNTA": {"a": "(wartość1; [wartość2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, ile niepustych komórek w zakresie ", "ad": "- od 1 do 255 argument<PERSON> reprezentujących zliczane wartości. Wartości mogą być dowolnego typu"}, "COUNTBLANK": {"a": "(zakres)", "d": "Zlicza liczbę pustych komórek w określonym zakresie komórek", "ad": "- <PERSON><PERSON><PERSON>, w którym zostaną zliczone puste komórki"}, "COUNTIF": {"a": "(zakres; kryteria)", "d": "Oblicza liczbę komórek we wskazanym zakresie spełniających podane kryteria", "ad": "- <PERSON><PERSON><PERSON> komórek, w którym będą zliczane niepuste komórki!- kryteria podane w formie liczby, wyrażenia lub tekstu, <PERSON><PERSON>ś<PERSON><PERSON><PERSON><PERSON>, które komórki będą uwzględniane przy zliczaniu"}, "COUNTIFS": {"a": "(kryteria_zak<PERSON>; kryteria; ...)", "d": "Oblicza liczbę komórek spełniających podany zestaw warunków lub kryteriów", "ad": "- <PERSON><PERSON><PERSON> kom<PERSON>, dla k<PERSON><PERSON><PERSON>ch należy sprawdzić określony warunek!- warunek określający zliczane komórki, podany w postaci liczby, wyrażenia lub tekstu"}, "COVAR": {"a": "(tablica1; tablica2)", "d": "Zwraca kowarian<PERSON>ję, średnią z iloczynów odchyleń dla każdej pary punktów w dwóch zbiorach", "ad": "- pier<PERSON>zy zakres komórek zawierających liczby całkowite, musz<PERSON> być to liczby, tablice lub odwołania zawierające liczby!- drugi zakres komórek zawierających liczby całkowite, muszą być to liczby, tablice lub odwołania zawierające liczby"}, "COVARIANCE.P": {"a": "(tablica1; tablica2)", "d": "Zwraca kowariancj<PERSON> pop<PERSON>, czyli średnią iloczynów odchyleń dla każdej pary punktów danych w dwóch zbiorach danych", "ad": "- pier<PERSON>zy zakres komórek zawierających liczby całkowite (muszą to być liczby, tablice lub odwołania zawierające liczby)!- drugi zakres komórek zawierających liczby całkowite (muszą to być liczby, tablice lub odwołania zawierające liczby)"}, "COVARIANCE.S": {"a": "(tablica1; tablica2)", "d": "Zwraca kowariancję <PERSON>, czyli średnią iloczynów odchyleń dla każdej pary punktów danych w dwóch zbiorach danych", "ad": "- pier<PERSON>zy zakres komórek zawierających liczby całkowite (muszą to być liczby, tablice lub odwołania zawierające liczby)!- drugi zakres komórek zawierających liczby całkowite (muszą to być liczby, tablice lub odwołania zawierające liczby)"}, "CRITBINOM": {"a": "(próby; prawdopodob_s; alfa)", "d": "Zwraca najmnie<PERSON><PERSON><PERSON>, dla której skumulowany rozkład dwumianowy jest większy lub równy podanej wartości progowej", "ad": "- liczba prób <PERSON>!- prawdopodobieństwo sukcesu w pojedynczej próbie, liczba z przedziału od 0 do 1 włącznie!- <PERSON><PERSON><PERSON><PERSON> progowa, liczba z przedziału od 0 do 1 włącznie"}, "DEVSQ": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca sumę kwadratów odchyleń punktów danych od średniej arytmetycznej z próbki", "ad": "- od 1 do 255 <PERSON><PERSON>, lub tablica albo odwołanie do tablicy, dla której ma zostać obliczona suma kwadratów odchyleń"}, "EXPONDIST": {"a": "(x; lambda; skumu<PERSON>any)", "d": "Zwraca rozkład wykładniczy", "ad": "- warto<PERSON><PERSON> funk<PERSON>, liczba nieujemna!- war<PERSON><PERSON><PERSON> parametru, liczba dodatnia!- war<PERSON><PERSON><PERSON>, kt<PERSON>r<PERSON> funkcja ma zwrócić: funkcja rozkładu skumulowanego = PRAWDA; funkcja gęstości prawdopodobieństwa = FAŁSZ"}, "EXPON.DIST": {"a": "(x; lambda; skumu<PERSON>any)", "d": "Zwraca rozkład wykładniczy", "ad": "- warto<PERSON><PERSON> funk<PERSON>, liczba nieujemna!- war<PERSON><PERSON><PERSON> parametru, liczba dodatnia!- war<PERSON><PERSON><PERSON>, kt<PERSON>r<PERSON> funkcja ma zwrócić: funkcja rozkładu skumulowanego = PRAWDA; funkcja gęstości prawdopodobieństwa = FAŁSZ"}, "FDIST": {"a": "(x; stopnie_swobody1; stopnie_swobody2)", "d": "Zwraca (prawostronny) rozkład F prawdopodobieństwa (stopień zróżnicowania) dla dwóch zbiorów danych", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać wyznaczona funkcja, liczba nieujemna!- warto<PERSON><PERSON> stopni swobody w liczniku, liczba z przedziału od 1 do 10^10 z wyłączeniem 10^10!- warto<PERSON><PERSON> stopni swobody w mianowniku, liczba z przedziału od 1 do 10^10 z wyłączeniem 10^10"}, "FINV": {"a": "(prawdopodobieństwo; stopnie_swobody1; stopnie_swobody2)", "d": "Zwraca odw<PERSON> (prawostronnego) rozkładu F prawdopodobieństwa: jeśli p = ROZKŁAD.F(x,...), wówczas ROZKŁAD.F.ODW(p,...) = x", "ad": "- prawdopodobieństwo skojarzone ze skumulowanym rozkładem F, liczba z przedziału od 0 do 1 włącznie!- stopnie swobody licznika, liczba między 1 a 10^10 z wyłączeniem 10^10!- wartość stopni swobody mianownika, liczba między 1 a 10^10 z wyłączeniem 10^10"}, "FTEST": {"a": "(tablica1; tablica2)", "d": "Zwraca wynik testu F, d<PERSON><PERSON><PERSON><PERSON> prawdopodobieństwa, że wariancje w Tablicy1 i Tablicy2 nie są istotnie różne", "ad": "- pierwsza tablica lub zak<PERSON> danych, moż<PERSON> zaw<PERSON> liczby lub nazwy, tablice albo odwołania zawierające liczby (puste są ignorowane)!- druga tablica lub zakres danych, może zawierać liczby lub nazwy, tablice albo odwołania zawierające liczby (puste są ignorowane)"}, "F.DIST": {"a": "(x; stopnie_swobody1; stopnie_swobody2; skumu<PERSON>any)", "d": "Zwraca lewostronny rozkład F prawdopodobieństwa (stopień zróżnicowania) dla dwóch zbiorów danych", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać oszacowana funkcja (liczba nieujemna)!- stopnie swobody licznika (liczba z zakresu od 1 do 10^10, z wyłączeniem wartości 10^10)!- stopnie swobody mianownika (liczba z zakresu od 1 do 10^10, z wyłączeniem wartości 10^10)!- wartość logiczna określająca zwracaną funkcję: funkcja rozkładu skumulowanego = PRAWDA; funkcja gęstości prawdopodobieństwa = FAŁSZ"}, "F.DIST.RT": {"a": "(x; stopnie_swobody1; stopnie_swobody2)", "d": "Zwraca prawostronny rozkład F prawdopodobieństwa (stopień zróżnicowania) dla dwóch zbiorów danych", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać oszacowana funkcja (liczba nieujemna)!- stopnie swobody licznika (liczba z zakresu od 1 do 10^10, z wyłączeniem wartości 10^10)!- stopnie swobody mianownika (liczba z zakresu od 1 do 10^10, z wyłączeniem wartości 10^10)"}, "F.INV": {"a": "(prawdopodobieństwo; stopnie_swobody1; stopnie_swobody2)", "d": "Zwraca odwrotność lewostronnego rozkładu F prawdopodobieństwa: jeśli p = ROZKŁ.F(x,...), wówczas ROZKŁ.F.ODW(p,...) = x", "ad": "- prawdopodobieństwo skojarzone ze skumulowanym rozkładem F (liczba z zakresu od 0 do 1 włącznie)!- stopnie swobody licznika (liczba z zakresu od 1 do 10^10, z wyłączeniem wartości 10^10)!- stopnie swobody mianownika (liczba z zakresu od 1 do 10^10, z wyłączeniem wartości 10^10)"}, "F.INV.RT": {"a": "(prawdopodobieństwo; stopnie_swobody1; stopnie_swobody2)", "d": "Zwraca odwrotność prawostronnego rozkładu F prawdopodobieństwa: jeśli p = ROZKŁ.F.PS(x,...), wówczas ROZKŁ.F.ODWR.PS(p,...) = x", "ad": "- prawdopodobieństwo skojarzone ze skumulowanym rozkładem F (liczba z zakresu od 0 do 1 włącznie)!- stopnie swobody licznika (liczba z zakresu od 1 do 10^10, z wyłączeniem wartości 10^10)!- stopnie swobody mianownika (liczba z zakresu od 1 do 10^10, z wyłączeniem wartości 10^10)"}, "F.TEST": {"a": "(tablica1; tablica2)", "d": "Zwraca wynik testu F, d<PERSON><PERSON><PERSON><PERSON> prawdopodobieństwa, że wariancje w Tablicy1 i Tablicy2 nie są istotnie różne", "ad": "- pierwsza tablica lub zak<PERSON> danych, moż<PERSON> zaw<PERSON> liczby lub nazwy, tablice albo odwołania zawierające liczby (puste są ignorowane)!- druga tablica lub zakres danych, może zawierać liczby lub nazwy, tablice albo odwołania zawierające liczby (puste są ignorowane)"}, "FISHER": {"a": "(x)", "d": "Zwraca transformatę Fishera", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać obliczona transformata, liczba między -1 a 1 z wyłączeniem -1 i 1"}, "FISHERINV": {"a": "(y)", "d": "Zwraca odwrotną transformatę Fishera: jeśli y = ROZKŁAD.FISHER(x), wówczas ROZKŁAD.FISHER.ODW(y) = x", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać wykonana odwrotna transformata"}, "FORECAST": {"a": "(x; znane_y; znane_x)", "d": "Oblicza lub przewid<PERSON><PERSON> warto<PERSON>ć przyszłą przy założeniu trendu liniowego i przy użyciu istniejących wartości", "ad": "— <PERSON><PERSON> da<PERSON>, dla którego ma zostać otrzymana wartość prognozy, musi być wartością liczbową!— dane zależne w postaci tablicy lub zakresu danych liczbowych!— dane niezależne w postaci tablicy lub zakresu danych liczbowych. Wariancja zbioru Znane_x musi być niezerowa"}, "FORECAST.ETS": {"a": "(data_docelowa; warto<PERSON>ci; oś_czasu; [se<PERSON><PERSON><PERSON><PERSON><PERSON>]; [komplet<PERSON><PERSON><PERSON>_danych]; [agrega<PERSON><PERSON>])", "d": "Zwraca prognozowaną wartość dla konkretnej daty docelowej w przyszłości przy użyciu wykładniczej metody wygładzania.", "ad": "punkt danych, dla którego program Spreadsheet Editor przew<PERSON><PERSON><PERSON> warto<PERSON>. Powinien być częścią wzorca wartości na osi czasu.!tablica lub zakres przewidywanych danych liczbowych.!niezależna tablica lub niezależny zakres danych liczbowych. Daty na osi czasu muszą mieć spójną odległość między sobą i nie mogą być zerami.!opcjonalna wartość liczbowa wskazująca długość wzorca sezonowego. Wartość domyślna 1 oznacza, że sezonowość jest wykrywana automatycznie.!opcjonalna wartość na potrzeby obsługi brakujących wartości. Wartość domyślna 1 powoduje zamianę brakujących wartości przez interpolację, a wartość 0 powoduje ich zamianę na zera.!opcjonalna wartość liczbowa na potrzeby agregowania wielu wartości o tej samej sygnaturze czasowej. <PERSON><PERSON><PERSON> jest pusta, program Spreadsheet Editor oblicza średnią wartości."}, "FORECAST.ETS.CONFINT": {"a": "(data_docelowa; wartości; oś_czasu; [poziom_uf<PERSON>]; [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]; [komple<PERSON><PERSON><PERSON><PERSON>_danych]; [agrega<PERSON><PERSON>])", "d": "Zwraca przedział ufności dla wartości prognozy dla określonej daty docelowej.", "ad": "punkt danych, dla którego program Spreadsheet Editor przew<PERSON><PERSON><PERSON>. Powinien być częścią wzorca wartości na osi czasu.!tablica lub zakres przewidywanych danych liczbowych.!niezależna tablica lub niezależny zakres danych liczbowych. Daty na osi czasu muszą mieć spójną odległość między sobą i nie mogą być zerami.!liczba od 0 do 1, która pokazuje poziom ufności dla obliczanego interwału ufności. Wartość domyślna to 0,95.!opcjonalna wartość liczbowa wskazująca długość wzorca sezonowego. Wartość domyślna 1 oznacza, że sezonowość jest wykrywana automatycznie.!opcjonalna wartość na potrzeby obsługi brakujących wartości. Wartość domyślna 1 powoduje zamianę brakujących wartości przez interpolację, a wartość 0 powoduje ich zamianę na zera.!opcjonalna wartość liczbowa na potrzeby agregowania wielu wartości o tej samej sygnaturze czasowej. Jeśli jest pusta, program Spreadsheet Editor oblicza średnią wartości."}, "FORECAST.ETS.SEASONALITY": {"a": "(war<PERSON><PERSON><PERSON>; oś_czasu; [komplet<PERSON><PERSON><PERSON>_danych]; [agrega<PERSON><PERSON>])", "d": "Zwraca długość powtarzającego się wzorca wykrywanego przez program dla określonego szeregu czasowego.", "ad": "tablica lub zakres przewidywanych danych liczbowych.!niezależna tablica lub niezależny zakres danych liczbowych. Daty na osi czasu muszą mieć spójną odległość między sobą i nie mogą być zerami.!opcjonalna wartość na potrzeby obsługi brakujących wartości. Wartość domyślna 1 zamienia brakujące wartości przez interpolację, a wartość 0 zamienia je na zera.!opcjonalna wartość liczbowa na potrzeby agregowania wielu wartości o tej samej sygnaturze czasowej. Je<PERSON><PERSON> jest pusta, program Spreadsheet Editor oblicza średnią wartości."}, "FORECAST.ETS.STAT": {"a": "(war<PERSON><PERSON><PERSON>; oś_czasu; typ_statystyki; [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]; [komplet<PERSON><PERSON><PERSON>_danych]; [agrega<PERSON><PERSON>])", "d": "Zwraca żądaną statystykę dla prognozy.", "ad": "tablica lub zakres przewidywanych danych liczbowych.!niezależna tablica lub niezależny zakres danych liczbowych. Daty na osi czasu muszą mieć spójną odległość między sobą i nie mogą być zerami.!liczba z przedziału od 1 do 8 wskazująca, które statystyki program Spreadsheet Editor zwróci dla obliczanej prognozy.!opcjonalna wartość liczbowa wskazująca długość wzorca sezonowego. Wartość domyślna 1 oznacza, że sezonowość jest wykrywana automatycznie.!opcjonalna wartość na potrzeby obsługi brakujących wartości. Wartość domyślna 1 zamienia brakujące wartości przez interpolację, a wartość 0 zamienia je na zera.!opcjonalna wartość liczbowa na potrzeby agregowania wielu wartości o tej samej sygnaturze czasowej. <PERSON><PERSON><PERSON> jest pusta, program Spreadsheet Editor oblicza średnią wartości."}, "FORECAST.LINEAR": {"a": "(x; znane_y; znane_x)", "d": "Oblicza lub przewid<PERSON><PERSON> warto<PERSON>ć przyszłą przy założeniu trendu liniowego i przy użyciu istniejących wartości", "ad": "— <PERSON><PERSON> da<PERSON>, dla którego ma zostać otrzymana wartość prognozy, musi być wartością liczbową!— dane zależne w postaci tablicy lub zakresu danych liczbowych!— dane niezależne w postaci tablicy lub zakresu danych liczbowych. Wariancja zbioru Znane_x musi być niezerowa"}, "FREQUENCY": {"a": "(tablica_dane; tablica_przedziały)", "d": "Oblicza rozkład częstości występowania wartości w zakresie wartości i zwraca w postaci pionowej tablicy liczby, które mają o jeden element więcej niż tablica_bin", "ad": "- tablica lub odwołanie do z<PERSON>ru warto<PERSON>, dla kt<PERSON><PERSON><PERSON> będą obliczane c<PERSON>ę<PERSON> (komórki puste i tekst są ignorowane)!- tablica lub odwołanie do zbioru przedziałów, w których mają być grupowane wartości ze zbioru tablica_dane"}, "GAMMA": {"a": "(x)", "d": "Zwraca wartość funkcji Gamma.", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać obliczona funkcja Gamma."}, "GAMMADIST": {"a": "(x; alfa; beta; skumulowany)", "d": "Zwraca rozkład gamma", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać oszacowany rozkład, liczba nieujemna!- parametr rozkładu, liczba dodatnia!- parametr rozkładu, liczba dodatnia. <PERSON><PERSON><PERSON> beta = 1, funkcja ROZKŁAD.GAMMA zwraca standardowy rozkład gamma!- wartość logiczna: zwraca funkcję rozkładu skumulowanego = PRAWDA; zwraca funkcję masy prawdopodobień<PERSON>wa = FAŁSZ lub pominięta"}, "GAMMA.DIST": {"a": "(x; alfa; beta; skumulowany)", "d": "Zwraca rozkład gamma", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać oszacowany rozkład (liczba nieujemna)!- parametr rozkładu (liczba dodatnia)!- parametr rozkładu (liczba dodatnia). Je<PERSON><PERSON> parametr beta = 1, funk<PERSON>ja ROZKŁ.GAMMA zwraca standardowy rozkład gamma!- wartość logiczna: zwrócenie funkcji rozkładu skumulowanego = PRAWDA; zwrócenie funkcji gęstości prawdopodobieństwa = FAŁSZ lub pominięta"}, "GAMMAINV": {"a": "(prawdopodobieństwo; alfa; beta)", "d": "Zwraca odwrotność skumulowanego rozkładu gamma: jeśli p = ROZKŁAD.GAMMA(x,...), wówczas ROZKŁAD.GAMMA.ODW(p,...) = x", "ad": "- prawdopodobieństwo skojarzone z danym rozkładem gamma, liczba z przedziału od 0 do 1 włącznie!- parametr rozkładu, liczba dodatnia!- parametr rozkładu, liczba dodatnia. <PERSON><PERSON><PERSON> beta = 1, funkcja ROZKŁAD.GAMMA.ODW zwraca odwrotność standardowego rozkładu gamma"}, "GAMMA.INV": {"a": "(prawdopodobieństwo; alfa; beta)", "d": "Zwraca odwrotność skumulowanego rozkładu gamma: jeśli p = ROZKŁ.GAMMA(x,...), to ROZKŁ.GAMMA.ODW(p,...) = x", "ad": "- prawdopodobieństwo skojarzone z rozkładem gamma (liczba z zakresu od 0 do 1 włącznie)!- parametr rozkładu (liczba dodatnia)!- parametr rozkładu (liczba dodatnia). <PERSON><PERSON><PERSON> parametr beta = 1, funkcja ROZKŁ.GAMMA.ODW zwraca odwrotność standardowego rozkładu gamma"}, "GAMMALN": {"a": "(x)", "d": "Zwraca logarytm naturalny funkcji gamma", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla kt<PERSON><PERSON>j będzie obliczana funkcja ROZKŁAD.LIN.GAMMA, liczba dodatnia"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Zwraca logarytm naturalny funkcji gamma", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla kt<PERSON><PERSON>j będzie obliczana funkcja ROZKŁAD.LIN.GAMMA.DOKŁ, liczba dodatnia"}, "GAUSS": {"a": "(x)", "d": "Zwraca rozkład prawdopodobieństwa o 0,5 mniejszy od standardowego skumulowanego rozkładu normalnego.", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać obliczony rozkład."}, "GEOMEAN": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwrac<PERSON> warto<PERSON> średniej <PERSON>cznej dla tablicy lub zakresu dodatnich danych liczbowych", "ad": "- od 1 do 255 liczb lub nazw, tablic albo od<PERSON><PERSON><PERSON>, które zawierają uśredniane liczby"}, "GROWTH": {"a": "(znane_y; [znane_x]; [nowe_x]; [stała])", "d": "Zwraca liczby wykładniczego trendu wzrostu, dopasowane do znanych punktów danych", "ad": "— zbiór już znanych wartości y w relacji y = b*m^x, tablica lub zakres liczb dodatnich!— opcjonalny zbiór wartości x, już znanych w relacji y = b*m^x, tablica lub zakres o tym samym rozmiarze, co znane_y!— nowe wartości x, dla któ<PERSON>ch mają zostać zwrócone przez funkcję REGEXPW odpowiadające im wartości y!— wartość logiczna: stała b jest obliczana normalnie, je<PERSON><PERSON> stała = PRAWDA; stała b jest ustawiana jako równa 1, je<PERSON><PERSON> stała = FAŁSZ lub jest pominięta"}, "HARMEAN": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca wartość średnią harmoniczną zbioru danych liczb dodatnich: odwrot<PERSON>ść średniej arytmetycznej odwrotności", "ad": "- od 1 do 255 liczb lub nazw, tablic albo od<PERSON><PERSON><PERSON>, które zawierają uśredniane liczby"}, "HYPGEOM.DIST": {"a": "(próbka_s; wielk_próbki; populacja_s; wielk_populacji; skumulowany)", "d": "Zwraca rozkład hipergeometryczny", "ad": "- liczba sukcesów w próbce!- wiel<PERSON><PERSON><PERSON> próbki!- liczba sukcesów w populacji!- rozmiar populacji!- wartość logiczna: dla funkcji rozkładu skumulowanego należy użyć wartości PRAWDA; dla funkcji gęstości prawdopodobieństwa należy użyć wartości FAŁSZ"}, "HYPGEOMDIST": {"a": "(próbka_s; wielk_próbki; populacja_s; wielk_populacji)", "d": "Wyznacza rozkład hipergeometryczny", "ad": "- liczba sukcesów w próbce!- wiel<PERSON>ść próbki!- liczba sukcesów w populacji!- rozmiar populacji"}, "INTERCEPT": {"a": "(znane_y; znane_x)", "d": "Oblicza miejsce przecięcia się linii z osią y, używając linii najlepszego dopasowania przechodzącej przez znane wartości x i y.", "ad": "— zbiór danych lub obserwac<PERSON> zależnych, może zawiera<PERSON> liczby lub nazwy, tablice albo odwołania zawierające liczby!— zbiór danych lub obserwacji niezależnych, może zawierać liczby lub nazwy, tablice albo odwołania zawierające liczby"}, "KURT": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca kurtozę zbioru da<PERSON>ch", "ad": "- od 1 do 255 liczb lub nazw, tablic albo od<PERSON>ła<PERSON> zawieraj<PERSON><PERSON>ch liczby, dla kt<PERSON><PERSON>ch ma być obliczona kurtoza"}, "LARGE": {"a": "(tablica; k)", "d": "Zwraca k-tą najwięks<PERSON><PERSON> wartość w zbiorze danych, na przykład piątą najwięks<PERSON><PERSON> wartość", "ad": "- tablica lub zak<PERSON> danych, których k-ta najwięks<PERSON> wartość ma być określona!- pozycja (licząc od największej wartości) w tablicy lub zakresie komó<PERSON> war<PERSON>, która ma być zwrócona"}, "LINEST": {"a": "(znane_y; [znane_x]; [stała]; [statystyka])", "d": "Zwraca statystykę opisującą trend liniowy, dopasowany do znanych punktów danych, dopasowując linię prostą przy użyciu metody najmniejszych kwadratów", "ad": "— zbiór już znanych wartości y w relacji y = mx + b!— opcjonalny zbiór już znanych wartości x w relacji y = mx + b!— wartość logiczna: stała b jest oblic<PERSON>a normalnie, je<PERSON><PERSON> s<PERSON>ła = PRAWDA lub jest pominięta; stała b jest ustawiana jako równa 0, je<PERSON><PERSON> stała = FAŁSZ!— wartość logiczna: zwraca dodatkowe statystki regresji = PRAWDA; zwraca współczynniki m i stałą b = FAŁSZ lub pominięta"}, "LOGEST": {"a": "(znane_y; [znane_x]; [stała]; [statystyka])", "d": "Zwraca statystykę, która opisuje krzywą wykładniczą dopasowaną do znanych punktów danych", "ad": "— zbiór już znanych wartości y w relacji y = b*m^x!— opcjonalny zbiór już znanych wartości x w relacji y = b*m^x!— wartość logiczna: stała b jest ob<PERSON><PERSON>a normalnie, j<PERSON><PERSON><PERSON> s<PERSON> = PRAWDA lub jest pominięta; stała b jest ustawiana jako równa 1, je<PERSON><PERSON> stała = FAŁSZ!— wartość logiczna: zwraca dodatkowe statystki regresji = PRAWDA; zwraca współczynniki m i stałą b = FAŁSZ lub pominięta"}, "LOGINV": {"a": "(prawdopodobieństwo; średnia; odchylenie_std)", "d": "Zwraca odwrotność skumulowanego rozkładu logarytmiczno-normalnego x, gdzie ln(x) ma rozkład normalny o parametrach Średnia i Odchylenie_std", "ad": "- prawdopodobieństwo skojarzone z danym rozkładem logarytmiczno-normalnym, liczba z przedziału od 0 do 1 włącznie!- wartość średnia dla ln(x)!- odchylenie standardowe dla ln(x), liczba dodatnia"}, "LOGNORM.DIST": {"a": "(x; średnia; odchylenie_std; skumulowany)", "d": "Zwraca rozkład logarytmiczno-normalny dla wartości x, gdzie ln(x) ma rozkład normalny o parametrach Średnia i Odchylenie_std", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać oszacowana funkcja (liczba dodatnia)!- wartość średnia dla ln(x)!- odchylenie standardowe dla ln(x) (liczba dodatnia)!- wartość logiczna: dla funkcji rozkładu skumulowanego należy użyć wartości PRAWDA; dla funkcji gęstości prawdopodobieństwa należy użyć wartości FAŁSZ"}, "LOGNORM.INV": {"a": "(prawdopodobieństwo; średnia; odchylenie_std)", "d": "Zwraca odwrotność skumulowanego rozkładu logarytmiczno-normalnego x, gdzie ln(x) ma rozkład normalny o parametrach Średnia i Odch_stand", "ad": "- prawdopodobieństwo skojarzone z danym rozkładem logarytmiczno-normalnym, liczba między 0 i 1 włącznie!- wartość średnia dla ln(x)!- odchylenie standardowe dla ln(x), liczba dodatnia"}, "LOGNORMDIST": {"a": "(x; średnia; odchylenie_std)", "d": "Zwraca skumulowany rozkład logarytmiczno-normalny x, gdzie ln(x) ma rozkład normalny o parametrach Średnia i Odchylenie_std", "ad": "- <PERSON><PERSON>, w kt<PERSON><PERSON>m ma zostać wyznaczona wartość funkcji, liczba dodatnia!- wartość średnia dla ln(x)!- odchylenie standardowe dla ln(x), liczba dodatnia"}, "MAX": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca największą wartość ze zbioru wartości. Ignoruje wartości logiczne i tekst", "ad": "- od 1 do 255 liczb, pu<PERSON><PERSON> komórek, wartości logicznych lub liczb w postaci tekstowej, k<PERSON><PERSON><PERSON><PERSON> maksimum chcesz znaleźć"}, "MAXA": {"a": "(wartość1; [wartość2]; ...)", "d": "Zwraca największą wartość ze zbioru wartości. Nie pomija wartości logicznych i tekstu", "ad": "- od 1 do 255 liczb, pu<PERSON><PERSON> komórek, wartości logicznych lub liczb w postaci tekstowej, k<PERSON><PERSON><PERSON><PERSON> maksimum chcesz znaleźć"}, "MAXIFS": {"a": "(zakres_maks; zakres_kryteriów; kryteria; ...)", "d": "Zwraca warto<PERSON>ć maksymalną wśród komórek spełniających podany zestaw warunków lub kryteriów", "ad": " - k<PERSON><PERSON><PERSON><PERSON>, dla których ma zostać ustalona wartość maksymalna! - zak<PERSON> komórek, które chcesz ocenić pod kątem danego warunku! - warunek lub kryteria w postaci liczby, wyrażenia lub tekstu określające komórki, które chcesz uwzględnić przy określaniu wartości maksymalnej"}, "MEDIAN": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca medianę lub liczbę w środku zbioru podanych liczb", "ad": "- od 1 do 255 liczb lub nazw, tablic albo od<PERSON><PERSON><PERSON> zawieraj<PERSON><PERSON>ch liczby, dla któ<PERSON>ch ma zostać wyznaczona mediana"}, "MIN": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca najmniejszą wartość ze zbioru wartości. Ignoruje wartości logiczne i tekst", "ad": "- od 1 do 255 liczb, pu<PERSON><PERSON> komórek, wartości logicznych lub liczb w postaci tekstowej, w<PERSON>r<PERSON>d których nastąpi wyszukanie liczby najmniejszej"}, "MINA": {"a": "(wartość1; [wartość2]; ...)", "d": "Zwraca najmniejszą wartość ze zbioru wartości. Nie pomija wartości logicznych i tekstu", "ad": "- od 1 do 255 liczb, pu<PERSON><PERSON> komórek, wartości logicznych lub liczb w postaci tekstowej, w<PERSON>r<PERSON>d których nastąpi wyszukanie liczby najmniejszej"}, "MINIFS": {"a": "(zakres_min; zakres_kryteriów; kryteria; ...)", "d": "Zwraca wartość minimalną wśród komórek spełniających podany zestaw warunków lub kryteriów", "ad": " - k<PERSON><PERSON><PERSON><PERSON>, dla których ma zostać ustalona wartość minimalna! - zak<PERSON> komórek, które chcesz ocenić pod kątem danego warunku! - warunek lub kryteria w postaci licz<PERSON>, wyrażenia lub tekstu określające komórki, które chcesz uwzględnić przy określaniu wartości minimalnej"}, "MODE": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca najczęściej występującą lub powtarzaj<PERSON><PERSON>ą się wartoś<PERSON> w tablicy albo zakresie danych", "ad": "- od 1 do 255 liczb, nazw, tablic albo od<PERSON><PERSON><PERSON> zawieraj<PERSON><PERSON>ch liczby, dla kt<PERSON><PERSON>ch ma być obliczona funkcja WYST.NAJCZĘŚCIEJ"}, "MODE.MULT": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca pionową tablicę zawierającą najczęściej występujące lub powtarzające się wartości w tablicy lub zakresie danych. W przypadku tablicy poziomej należy użyć funkcji =TRANSPONUJ(WYST.NAJCZĘŚCIEJ.TABL(liczba1;liczba2;...))", "ad": "- od 1 do 255 liczb lub nazw albo odwołań zawierają<PERSON>ch liczby, dla kt<PERSON><PERSON>ch ma być obliczona funkcja WYST.NAJCZĘŚCIEJ.TABL"}, "MODE.SNGL": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca najczęściej występującą lub powtarzaj<PERSON><PERSON>ą się wartoś<PERSON> w tablicy albo zakresie danych", "ad": "- od 1 do 255 liczb lub nazw albo odwołań zawierają<PERSON>ch liczby, dla kt<PERSON><PERSON>ch ma być obliczona funkcja WYST.NAJCZĘŚCIEJ"}, "NEGBINOM.DIST": {"a": "(liczba_p; liczba_s; prawdopodobieństwo_s; sku<PERSON>lowany)", "d": "Zwraca ujemny rozkład dwu<PERSON>owy (prawdopodobieństwo, że wystąpi Liczba_p porażek przed sukcesem o numerze Liczba_s, z prawdopodobieństwem sukcesu równym Prawdopodobieństwo_s)", "ad": "- liczba porażek!- wartość progowa liczby sukcesów!- prawdopodobieństwo sukcesu (liczba z zakresu od 0 do 1)!- wartość logiczna: w przypadku funkcji rozkładu skumulowanego należy użyć wartości PRAWDA; w przypadku funkcji masy prawdopodobieństwa należy użyć wartości FAŁSZ"}, "NEGBINOMDIST": {"a": "(liczba_p; liczba_s; prawdopodob_s)", "d": "Zwraca rozkład dwumianowy ujemny, prawdopodobieństwo, że wystąpi Liczba_p porażek przed sukcesem nr Liczba_s z prawdopodobieństwem sukcesu Prawdopodob_s", "ad": "- liczba porażek!- wartość progowa liczby sukcesów!- prawdopodobieństwo sukcesu; liczba między 0 a 1"}, "NORM.DIST": {"a": "(x; średnia; odchylenie_std; skumulowany)", "d": "Zwraca rozkład normalny dla podanej średniej i odchylenia standardowego", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać obliczony rozkład!- średnia arytmetyczna rozkładu!- odchylenie standardowe rozkładu (liczba dodatnia)!- wartość logiczna: dla funkcji rozkładu skumulowanego należy użyć wartości PRAWDA; dla funkcji gęstości prawdopodobieństwa należy użyć wartości FAŁSZ"}, "NORMDIST": {"a": "(x; średnia; odchylenie_std; skumulowany)", "d": "Zwraca skumulowany rozkład normalny dla podanej średniej i odchylenia standardowego", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać obliczony rozkład!- średnia arytmetyczna danego rozkładu!- odchylenie standardowe danego rozkładu, liczba dodatnia!- wartość logiczna: dla funkcji rozkładu skumulowanego użyj wartości PRAWDA; dla funkcji gęstości prawdopodobieństwa użyj wartości FAŁSZ"}, "NORM.INV": {"a": "(prawdopodobieństwo; średnia; odchylenie_std)", "d": "Zwraca odwrot<PERSON>ść skumulowanego rozkładu normalnego dla podanej średniej i odchylenia standardowego", "ad": "- prawdopodobieństwo dotyczące danego rozkładu normalnego, liczba z przedziału 0 i 1, włącznie!- średnia arytmetyczna danego rozkładu!- odchylenie standardowe danego rozkładu, liczba dodatnia"}, "NORMINV": {"a": "(prawdopodobieństwo; średnia; odchylenie_std)", "d": "Zwraca odwrot<PERSON>ść skumulowanego rozkładu normalnego dla podanej średniej i odchylenia standardowego", "ad": "- prawdopodobieństwo dotyczące danego rozkładu normalnego, liczba z przedziału od 0 do 1 włącznie!- średnia arytmetyczna danego rozkładu!- odchylenie standardowe danego rozkładu, liczba dodatnia"}, "NORM.S.DIST": {"a": "(z; sku<PERSON><PERSON><PERSON>)", "d": "Zwraca standardowy rozkład normalny (o średniej zero i odchyleniu standardowym jeden)", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać wyznaczony rozkład!- wartość logiczna określająca zwracaną funkcję: funkcja rozkładu skumulowanego = PRAWDA; funkcja gęstości prawdopodobieństwa = FAŁSZ"}, "NORMSDIST": {"a": "(z)", "d": "Zwraca standardowy skumulowany rozkład normalny (o średniej zero i odchyleniu standardowym jeden)", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać obliczony rozkład"}, "NORM.S.INV": {"a": "(prawdopodobieństwo)", "d": "Zwraca odwrotność standardowego skumulowanego rozkładu normalnego (o średniej zero i odchyleniu standardowym jeden)", "ad": "- prawdopodobieństwo dotyczące danego rozkładu normalnego, liczba z przedziału 0 i 1, wł<PERSON><PERSON><PERSON>"}, "NORMSINV": {"a": "(prawdopodobieństwo)", "d": "Zwraca odwrotność standardowego skumulowanego rozkładu normalnego (o średniej zero i odchyleniu standardowym jeden)", "ad": "- prawdopodobieństwo dotyczące danego rozkładu normalnego, liczba z przedziału od 0 do 1 włącznie"}, "PEARSON": {"a": "(tablica1; tablica2)", "d": "Zwraca współczynnik korelacji momentów iloczynu Pearsona, r", "ad": "- zbiór wartości niezależnych!- zbiór wartości zależnych"}, "PERCENTILE": {"a": "(tablica; k)", "d": "Wyznacza k-ty percentyl wartości w zakresie", "ad": "- tablica lub zakres danych definiujących pozycyjność względną!- wartość percentylu z przedziału od 0 do 1 włącznie"}, "PERCENTILE.EXC": {"a": "(tablica; k)", "d": "Zwraca k-ty percentyl wartości w zakresie, gdzie k należy do zakresu od 0 do 1 (bez wartości granicznych)", "ad": "- tablica lub zakres danych definiujących pozycyjność względną!- war<PERSON><PERSON><PERSON> percentylu, należąca do zakresu od 0 do 1 (bez wartości granicznych)"}, "PERCENTILE.INC": {"a": "(tablica; k)", "d": "Zwraca k-ty percentyl wartości w zakresie, gdzie k należy do zakresu od 0 do 1 włącznie", "ad": "- tablica lub zakres danych definiujących pozycyjność względną!- war<PERSON>ść percentylu, należąca do zakresu od 0 do 1 włącznie"}, "PERCENTRANK": {"a": "(tablica; x; [isto<PERSON><PERSON><PERSON><PERSON>])", "d": "Wyznacza pozycję procentową wartości w zbiorze danych", "ad": "- tablica lub zakres danych o wartościach liczbowych definiujących pozycyjność względną!- <PERSON><PERSON><PERSON><PERSON>, której pozycja ma być znaleziona!- opcjonalna wartość określająca liczbę cyfr znaczących otrzymanej wartości procentowej, trzy cyfry jeśli pomi<PERSON> (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(tablica; x; [isto<PERSON><PERSON><PERSON><PERSON>])", "d": "Zwraca pozycję procentową wartości w zbiorze danych, należącą do zakresu od 0 do 1 (bez wartości granicznych)", "ad": "jest tablicą lub zakresem danych o wartościach liczbowych definiujących pozycyjność względną!jest wartością, której pozycja ma zostać znaleziona!jest opcjonalną wartością określającą liczbę cyfr znaczących zwracanej wartości procentowej, trzy cy<PERSON><PERSON>, je<PERSON><PERSON> p<PERSON> (0,xxx%)"}, "PERCENTRANK.INC": {"a": "(tablica; x; [isto<PERSON><PERSON><PERSON><PERSON>])", "d": "Zwraca pozycję procentową wartości w zbiorze danych, należącą do zakresu od 0 do 1 włącznie", "ad": "jest tablicą lub zakresem danych o wartościach liczbowych definiujących pozycyjność względną!jest wartością, której pozycja ma zostać znaleziona!jest opcjonalną wartością określającą liczbę cyfr znaczących zwracanej wartości procentowej, trzy cy<PERSON><PERSON>, je<PERSON><PERSON> p<PERSON> (0,xxx%)"}, "PERMUT": {"a": "(liczba; wybór_liczba)", "d": "Zwraca liczbę permutacji dla podanej liczby obiektów, które można wybrać ze wszystkich obiektów", "ad": "- całkowita liczba obiektów!- wartoś<PERSON> całkowita określająca liczbę elementów w każdej permutacji"}, "PERMUTATIONA": {"a": "(liczba; liczba_wybrana)", "d": "Zwraca liczbę permutacji dla podanej liczby obiektów (z powtórzeniami), które można wybrać ze wszystkich obiektów.", "ad": "— całkowita liczba obiektów.!— liczba obiektów w każdej permutacji."}, "PHI": {"a": "(x)", "d": "Zwraca wartość funkcji gęstości dla standardowego rozkładu normalnego.", "ad": "- <PERSON><PERSON><PERSON>, dla której ma zostać obliczona gęstość standardowego rozkładu normalnego."}, "POISSON": {"a": "(x; średnia; skumulowany)", "d": "Zwraca rozkład <PERSON>", "ad": "- liczba zdarzeń!- oczek<PERSON><PERSON> wartoś<PERSON> l<PERSON>a, liczba dodatnia!- warto<PERSON>ć logiczna: dla skumulowanego prawdopodobieństwa Poissona użyj wartości PRAWDA; dla funkcji masy prawdopodobieństwa Poissona użyj wartości FAŁSZ"}, "POISSON.DIST": {"a": "(x; średnia; skumulowany)", "d": "Zwraca rozkład <PERSON>", "ad": "- liczba zdarzeń!- oczek<PERSON><PERSON> wartoś<PERSON> l<PERSON>, liczba dodatnia!- wartość logiczna: dla skumulowanego rozkładu Poissona, użyj wartości PRAWDA; dla funkcji rozkładu Poissona użyj wartości FAŁSZ"}, "PROB": {"a": "(zakres_x; zakres_prawdop; dolna_granica; [górna_granica])", "d": "Zwraca prawdopodobieństwo, że wartości w zakresie znajdują się między dwoma granicami lub są równe granicy dolnej", "ad": "- zakres wartości liczbowych zmiennej x, z którymi powiązane są odpowiednie prawdopodobieństwa!- zbiór prawdopodobieństw powiązanych z wartościami w ciągu zakres_x, wartości między 0 i 1 z wyłączeniem 0!- dolna granica przedziału wartości, dla której ma zostać wyznaczone prawdopodobieństwo!- opcjonalna górna granica wartości. <PERSON><PERSON><PERSON>, PROB zwraca prawdopodobieństwo, że wartości x_zakres są równe dolnej_granicy"}, "QUARTILE": {"a": "(tablica; kwarty)", "d": "Wyznacza kwartyl zbioru da<PERSON>ch", "ad": "- tablica lub zak<PERSON> komórek zaw<PERSON> warto<PERSON> liczbowe, dla kt<PERSON><PERSON>ch ma być znaleziony kwartyl!- liczba: warto<PERSON><PERSON> minimalna = 0; 1. kwartyl = 1; warto<PERSON><PERSON> mediany = 2; 3. kwart<PERSON> =3; warto<PERSON><PERSON> maksymalna = 4"}, "QUARTILE.INC": {"a": "(tablica; kwartyl)", "d": "Zwraca kwartyl zbioru danych na podstawie wartości percentylu z zakresu od 0 do 1 włącznie", "ad": "- tablica lub zak<PERSON> komórek zawierających wartości liczbowe, dla których ma zostać znaleziona wartość kwartylu!- liczba: warto<PERSON><PERSON> minimalna = 0; 1. kwartyl = 1; warto<PERSON><PERSON> mediany = 2; 3. kwart<PERSON> = 3; warto<PERSON><PERSON> maksymalna = 4"}, "QUARTILE.EXC": {"a": "(tablica; kwartyl)", "d": "Zwraca kwartyl zbioru danych na podstawie wartości percentylu z zakresu od 0 do 1 (bez wartości granicznych)", "ad": "- tablica lub zak<PERSON> komórek zawierających wartości liczbowe, dla których ma zostać znaleziona wartość kwartylu!- liczba: warto<PERSON><PERSON> minimalna = 0; 1. kwartyl = 1; warto<PERSON><PERSON> mediany = 2; 3. kwart<PERSON> = 3; warto<PERSON><PERSON> maksymalna = 4"}, "RANK": {"a": "(liczba; lista; [lp])", "d": "Zwraca pozycję liczby na liście liczb: jej rozmiar względem innych wartości na liście", "ad": "- l<PERSON><PERSON>, której pozycję chcesz znaleźć!- tablica lub adres odnoszący się do listy z liczbami. Wartości nieliczbowe są ignorowane!- liczba: pozycja na liście sortowanej malejąco = 0 lub pominięcie; pozycja na liście sortowanej rosnąco = dowolna wartość niezerowa"}, "RANK.AVG": {"a": "(l<PERSON><PERSON>; odwołanie; [lp])", "d": "Zwraca pozycję liczby na liście liczb: jej wiel<PERSON> względem innych wartości na liście; jeśli więcej niż jedna wartość ma taką samą pozycję, jest zwracana średnia pozycja", "ad": "- l<PERSON><PERSON>, której pozycję należy znaleźć!- tablica lub odwołanie do listy liczb. Wartości nieliczbowe są ignorowane!- liczba: pozycja na liście sortowanej malejąco = 0 lub pominięcie; pozycja na liście sortowanej rosnąco = dowolna wartość niezerowa"}, "RANK.EQ": {"a": "(l<PERSON><PERSON>; odwołanie; [lp])", "d": "Zwraca pozycję liczby na liście liczb: jej wiel<PERSON> względem innych wartości na liście; jeśli więcej niż jedna wartość ma taką samą pozycję, jest zwracana najwyższa pozycja zbioru warto<PERSON>ci", "ad": "- l<PERSON><PERSON>, której pozycję należy znaleźć!- tablica lub odwołanie do listy liczb. Wartości nieliczbowe są ignorowane!- liczba: pozycja na liście sortowanej malejąco = 0 lub pominięcie; pozycja na liście sortowanej rosnąco = dowolna wartość niezerowa"}, "RSQ": {"a": "(znane_y; znane_x)", "d": "Zwraca kwadrat współczynnika Pearsona korelacji iloczynu momentów dla zadanych punktów danych", "ad": "— tablica lub zakres punktów danych, może zawierać liczby lub nazwy, tablice albo odwołania zawierające liczby!— tablica lub zakres punktów danych, może zawierać liczby lub nazwy, tablice albo odwołania zawierające liczby"}, "SKEW": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca skośność rozkładu prawdopodobieństwa: charakteryzują<PERSON>ą stopień asymetrii rozkładu wokół średniej", "ad": "- od 1 do 255 liczb lub nazw, tablic albo od<PERSON><PERSON>ń, dla k<PERSON><PERSON><PERSON><PERSON> ma <PERSON>ć oblic<PERSON>a s<PERSON>"}, "SKEW.P": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca skośność rozkładu prawdopodobieństwa na podstawie populacji: charakteryzującą stopień asymetrii rozkładu wokół średniej.", "ad": "- od 1 do 254 liczb lub nazw, tablic albo od<PERSON><PERSON><PERSON> zawieraj<PERSON><PERSON><PERSON> liczby, dla któ<PERSON>ch ma zostać obliczona skośność populacji."}, "SLOPE": {"a": "(znane_y; znane_x)", "d": "Zwraca nachylenie wykresu regresji liniowej przez zadane punkty danych", "ad": "— tablica lub zakres komórek zależnych punktów danych liczbowych, może zawierać liczby lub nazwy, tablice albo odwołania zawierające liczby!— tablica lub zakres komórek niezależnych punktów danych liczbowych, może zawierać liczby lub nazwy, tablice albo odwołania zawierające liczby"}, "SMALL": {"a": "(tablica; k)", "d": "Zwraca k-tą najmniej<PERSON><PERSON> warto<PERSON> w zbiorze danych, na przykład piątą najmniejszą liczbę", "ad": "- tablica lub zakres danych liczbowych, których k-ta najmniejsza wartość ma być określona!- pozycja (licząc od najmniejszej warto<PERSON>ci) w tablicy lub zakresie komó<PERSON> war<PERSON>, która ma być zwrócona"}, "STANDARDIZE": {"a": "(x; średnia; odchylenie_std)", "d": "Zwraca wartość znormalizowaną z rozkładu scharakteryzowanego przez średnią i odchylenie standardowe", "ad": "- <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> ch<PERSON>z znormalizować!- średnia arytmetyczna danego rozkładu!- odchylenie standardowe danego rozkładu, liczba dodatnia"}, "STDEV": {"a": "(liczba1; [liczba2]; ...)", "d": "Dokonuje oszacowania odchylenia standardowego dla podanej próbki (pomija wartości logiczne i tekstowe w próbce)", "ad": "- od 1 do 255 liczb odpowiadających próbce populacji, mog<PERSON> by<PERSON> to liczby lub odwołania zawierające liczby"}, "STDEV.P": {"a": "(liczba1; [liczba2]; ...)", "d": "Oblicza odchylenie standardowe w oparciu o całą populację zadaną jako argument (pomija wartości logiczne i tekstowe)", "ad": "- od 1 do 255 liczb odpowiadających populacji, mog<PERSON> być to liczby lub odwołania zawierające liczby"}, "STDEV.S": {"a": "(liczba1; [liczba2]; ...)", "d": "Dokonuje oszacowania odchylenia standardowego dla podanej próbki (pomija wartości logiczne i tekstowe w próbce)", "ad": "- od 1 do 255 liczb odpowiadających próbce populacji, mog<PERSON> by<PERSON> to liczby lub odwołania zawierające liczby"}, "STDEVA": {"a": "(wartość1; [wartość2]; ...)", "d": "Szacuje odchylenie standardowe na podstawie próbki uwzględniając wartości logiczne oraz tekst. Wartość logiczna FAŁSZ i wartości tekstowe są traktowane jako 0, a wartość logiczna PRAWDA jako 1.", "ad": "- od 1 do 255 wartości odpowiadających próbce populacji; mogą to by<PERSON> warto<PERSON>, nazwami i odwołaniami do adresów zawierających wartości"}, "STDEVP": {"a": "(liczba1; [liczba2]; ...)", "d": "Oblicza odchylenie standardowe na podstawie całej populacji zadanej jako argument (pomija wartości logiczne i tekstowe)", "ad": "- od 1 do 255 liczb odpowiadających populacji, mog<PERSON> być to liczby lub odwołania zawierające liczby"}, "STDEVPA": {"a": "(wartość1; [wartość2]; ...)", "d": "Oblicza odchylenie standardowe w oparciu o całą populację, włącznie z wartościami logicznymi i tekstem. Teksty i wartości logiczne FAŁSZ są traktowane jako 0; <PERSON><PERSON><PERSON> wartość PRAWDA jest traktowana jako 1", "ad": "od 1 do 255 wartości odpowiadających populacji; mogą <PERSON><PERSON> warto<PERSON>, nazwami i odwołaniami do adresów zawierających wartości"}, "STEYX": {"a": "(znane_y; znane_x)", "d": "Zwraca błąd standardowy przewidywanej wartości y dla każdej wartości x w regresji", "ad": "— tablica lub zakres zależnych punktów danych, może zawierać liczby lub nazwy, tablice albo odwołania zawierające liczby!— tablica lub zakres niezależnych punktów danych, może zawierać liczby lub nazwy, tablice albo odwołania zawierające liczby"}, "TDIST": {"a": "(x; stopnie_swobody; strony)", "d": "Zwraca rozkład t-Studenta", "ad": "- war<PERSON><PERSON><PERSON> licz<PERSON>a, dla której ma zostać wyznaczony rozkład!- liczba całkowita reprezentująca liczbę stopni swobody charakteryzujących rozkład!- określa liczbę stron rozkładu: rozkład jednostronny = 1; rozkład dwustronny = 2"}, "TINV": {"a": "(prawdopodobieństwo; stopnie_swobody)", "d": "Zwraca dwustronną odwrotność rozkładu t-Studenta", "ad": "- prawdopodobieństwo skojarzone z dwustronnym rozkładem t-Studenta, liczba z przedziału od 0 do 1 włącznie!- dodatnia liczba całkowita wskazująca liczbę stopni swobody charakteryzujących rozkład"}, "T.DIST": {"a": "(x; stopnie_swobody; skumulowany)", "d": "Zwraca lewostronny rozkład t-Studenta", "ad": "- warto<PERSON><PERSON> licz<PERSON>a, dla której ma zostać oszacowany rozkład!- liczba całkowita wskazująca liczbę stopni swobody charakteryzujących rozkład!- wartość logiczna: dla funkcji rozkładu skumulowanego należy użyć wartości PRAWDA; dla funkcji gęstości prawdopodobieństwa należy użyć wartości FAŁSZ"}, "T.DIST.2T": {"a": "(x; stopnie_swobody)", "d": "Zwraca dwustronny rozkład t-Studenta", "ad": "- war<PERSON><PERSON><PERSON> licz<PERSON>a, dla której ma zostać oszacowany rozkład!- liczba całkowita wskazująca liczbę stopni swobody charakteryzujących rozkład"}, "T.DIST.RT": {"a": "(x; stopnie_swobody)", "d": "Zwraca prawostronny rozkład t-Studenta", "ad": "- war<PERSON><PERSON><PERSON> licz<PERSON>a, dla której ma zostać oszacowany rozkład!- liczba całkowita wskazująca liczbę stopni swobody charakteryzujących rozkład"}, "T.INV": {"a": "(prawdopodobieństwo; stopnie_swobody)", "d": "Zwraca lewostronną odwrotność rozkładu t-Studenta", "ad": "- prawdopodobieństwo skojarzone z dwustronnym rozkładem t-Studenta (liczba z zakresu od 0 do 1 włącznie)!- dodatnia liczba całkowita wskazująca liczbę stopni swobody charakteryzujących rozkład"}, "T.INV.2T": {"a": "(prawdopodobieństwo; stopnie_swobody)", "d": "Zwraca dwustronną odwrotność rozkładu t-Studenta", "ad": "- prawdopodobieństwo skojarzone z dwustronnym rozkładem t-Studenta (liczba z zakresu od 0 do 1 włącznie)!- dodatnia liczba całkowita wskazująca liczbę stopni swobody charakteryzujących rozkład"}, "T.TEST": {"a": "(tablica1; tablica2; strony; typ)", "d": "Zwraca prawdopodobieństwo związane z testem t-Studenta", "ad": "- pier<PERSON>zy zbiór danych!- drugi zbiór danych!- ok<PERSON><PERSON><PERSON> liczbę stron rozkładu: rozkład jednostronny = 1; rozkład dwustronny = 2!- ok<PERSON><PERSON><PERSON> rodzaj testu t: sparowany = 1, z dwiema próbkami o równej wariancji = 2, z dwiema próbkami o nierównej wariancji = 3"}, "TREND": {"a": "(znane_y; [znane_x]; [nowe_x]; [stała])", "d": "Zwraca liczby trendu liniowego dopasowane do znanych punktów danych przy użyciu metody najmniejszych kwadratów", "ad": "— zakres lub tablica już znanych wartości y w relacji y = mx + b!— opcjonalny zakres lub tablica już znanych wartości x w relacji y = mx + b, tablica o tym samym rozmiarze co znane_y!— zakres lub tablica nowych wartości x, dla kt<PERSON><PERSON>ch mają być zwrócone odpowiadające im wartości y!— wartość logiczna: stała b jest obliczana normalnie, je<PERSON><PERSON> stała = PRAWDA lub jest pominięta; stała b jest ustawiana jako równa 0, je<PERSON><PERSON> stała = FAŁSZ"}, "TRIMMEAN": {"a": "(tablica; procent)", "d": "Zwraca wartość średnią z wewnętrznej części zbioru wartości danych", "ad": "- tablica lub zakres wartości do zawężenia i obliczenia średniej!- liczba ułamkowa, określająca, jaka czę<PERSON><PERSON> punktów danych od góry i od dołu nie zostanie wykluczona podczas obliczeń"}, "TTEST": {"a": "(tablica1; tablica2; strony; typ)", "d": "Zwraca prawdopodobieństwo związane z testem t-Studenta", "ad": "- pier<PERSON>zy zbiór danych!- drugi zbiór danych!- ok<PERSON><PERSON><PERSON> liczbę stron rozkładu: rozkład jednostronny = 1; rozkład dwustronny = 2!- ok<PERSON><PERSON><PERSON> rodzaj testu t: sparowany = 1, z dwiema próbkami o równej wariancji = 2, z dwiema próbkami o nierównej wariancji = 3"}, "VAR": {"a": "(liczba1; [liczba2]; ...)", "d": "Wyznacza wariancję na podstawie próbki (pomija wartości logiczne i tekstowe w próbce)", "ad": "- od 1 do 255 wartości liczbowych odpowiadających próbce populacji"}, "VAR.P": {"a": "(liczba1; [liczba2]; ...)", "d": "Oblicza wariancję na podstawie całej populacji (pomija wartości logiczne i tekstowe w próbce)", "ad": "- od 1 do 255 wartości liczbowych odpowiadających populacji"}, "VAR.S": {"a": "(liczba1; [liczba2]; ...)", "d": "Wyznacza wariancję na podstawie próbki (pomija wartości logiczne i tekstowe w próbce)", "ad": "- od 1 do 255 wartości liczbowych odpowiadających próbce populacji"}, "VARA": {"a": "(wartość1; [wartość2]; ...)", "d": "Szacuje wariancję na podstawie próbki uwzględniając wartości logiczne oraz tekst. Wartość logiczna FAŁSZ i wartości tekstowe są traktowane jako 0, a wartość logiczna PRAWDA jako 1.", "ad": "od 1 do 255 argumentów odpowiadających próbce populacji"}, "VARP": {"a": "(liczba1; [liczba2]; ...)", "d": "Oblicza wariancję na podstawie całej populacji (pomija wartości logiczne i tekstowe w próbce)", "ad": "- od 1 do 255 wartości liczbowych odpowiadających populacji"}, "VARPA": {"a": "(wartość1; [wartość2]; ...)", "d": "Oblicza wariancję w oparciu o całą populację, włącznie z wartościami logicznymi i tekstem. Teksty i wartości logiczne FAŁSZ są traktowane jako 0; <PERSON><PERSON><PERSON> wartość PRAWDA jest traktowana jako 1", "ad": "- od 1 do 255 argumentów odpowiadających populacji"}, "WEIBULL": {"a": "(x; alfa; beta; skumulowany)", "d": "Zwraca r<PERSON><PERSON><PERSON><PERSON>", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma być o<PERSON> funk<PERSON>ja, liczba nieujemna!- parametr rozkła<PERSON>, liczba dodatnia!- parametr rozkładu, liczba dodatnia!- wartość logiczna: w przypadku funkcji rozkładu skumulowanego użyj wartości PRAWDA; w przypadku funkcji masy prawdopodobieństwa użyj wartości FAŁSZ"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; skumulowany)", "d": "Zwraca r<PERSON><PERSON><PERSON><PERSON>", "ad": "- <PERSON><PERSON><PERSON><PERSON>, dla której ma być o<PERSON> funkcja, liczba nieujemna!- parametr rozkładu, liczba dodatnia!- parametr rozkładu, liczba dodatnia!- wartość logiczna: w przypadku funkcji rozkładu skumulowanego użyj wartości PRAWDA; w przypadku funkcji gęstości prawdopodobieństwa użyj wartości FAŁSZ"}, "Z.TEST": {"a": "(tablica; x; [sigma])", "d": "Zwraca wartość P o jednej stronie oraz test z", "ad": "- tablica lub z<PERSON><PERSON> da<PERSON>ch, w oparciu o które będzie testowana wartość x!- <PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON> ch<PERSON>z poddać testowi!- warto<PERSON><PERSON> odchylenia standardowego w populacji (znanej). <PERSON><PERSON><PERSON>, używane jest odchylenie standardowe próbki"}, "ZTEST": {"a": "(tablica; x; [sigma])", "d": "Zwraca wartość P o jednej stronie oraz test z", "ad": "- tablica lub z<PERSON><PERSON> da<PERSON>ch, na podstawie któ<PERSON>ch będzie testowana wartość x!- <PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON> ch<PERSON>z poddać testowi!- warto<PERSON><PERSON> odchylenia standardowego w populacji (znanej). <PERSON><PERSON><PERSON>, używane jest odchylenie standardowe próbki"}, "ACCRINT": {"a": "(em<PERSON><PERSON>; pier<PERSON><PERSON>_ods<PERSON>ki; roz<PERSON><PERSON><PERSON>; stopa; cena_nom; c<PERSON><PERSON><PERSON><PERSON><PERSON>; [podstawa]; [metoda_obliczeń])", "d": "Oblicza naliczone odsetki dla papieru wartościowego oprocentowanego okresowo", "ad": "- data emisji papieru wartościowego podana jako liczba seryjna!- data pierwszego naliczenia odsetek od papieru wartościowego, podana jako liczba seryjna!- data rozliczenia papieru wartościowego, podana jako liczba seryjna!- roczna stopa procentowa kuponu papieru wartościowego!- wartość nominalna papieru wartościowego!- liczba wypłat odsetek w roku!- rodzaj podstawy wyliczania dni!- wartość logiczna: naliczone odsetki od daty emisji = PRAWDA lub wartość pominięta; obliczenia od daty ostatniej wypłaty odsetek = FAŁSZ"}, "ACCRINTM": {"a": "(emis<PERSON>; roz<PERSON><PERSON><PERSON>; stopa; cena_nom; [podstawa])", "d": "Zwrac<PERSON> warto<PERSON>ć procentu składanego dla papieru wartościowego oprocentowanego przy wykupie", "ad": "– data emisji papieru warto<PERSON>ciowego, podana jako liczba seryjna!– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– roczne oprocentowanie kuponu papieru wartościowego!– wartość nominalna papieru wartościowego!– rodzaj podstawy wyliczania dni"}, "AMORDEGRC": {"a": "(cena; data_zakupu; pierwszy_okres; odzysk; okres; stopa; [podstawa])", "d": "Zwrac<PERSON> war<PERSON> amortyzacji dla każdego okresu rozliczeniowego", "ad": "– cena zakupu środka trwałego!– data zakupu środka trwałego!– data kończąca pierwszy okres!– wartość odzysku na koniec okresu amortyzacji!– okres amortyzacji!– stopa amortyzacji!– rodzaj podstawy wyliczania dni"}, "AMORLINC": {"a": "(cena; data_zakupu; pierwszy_okres; odzysk; okres; stopa; [podstawa])", "d": "Zwrac<PERSON> war<PERSON> amortyzacji dla każdego okresu rozliczeniowego", "ad": "– cena zakupu środka trwałego!– data zakupu środka trwałego!– data kończąca pierwszy okres!– wartość odzysku na koniec okresu amortyzacji!– okres amortyzacji!– stopa amortyzacji!– rodzaj podstawy wyliczania dni"}, "COUPDAYBS": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Oblicza liczbę dni od początku okresu dywidendy do daty rozliczenia", "ad": "- data rozliczenia papieru wartościowego, podana jako liczba seryjna!- data spłaty papieru wartościowego, podana jako liczba seryjna!- liczba realizacji kuponów w roku!- określenie typu płatności"}, "COUPDAYS": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Oblicza liczbę dni w okresie dywidendy obejmującym datę rozliczenia", "ad": "- data rozliczenia papieru wartościowego, podana jako liczba seryjna!- data spłaty papieru wartościowego, podana jako liczba seryjna!- liczba realizacji kuponów w roku!- określenie typu płatności"}, "COUPDAYSNC": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca liczbę dni do daty następnej dywidendy", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– liczba realizacji kuponów w roku!– określenie typu płatności"}, "COUPNCD": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca datę następnej dywidendy po dacie roz<PERSON>ia", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– liczba realizacji kuponów w roku!– określenie sposobu płatności"}, "COUPNUM": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": " Zwraca liczbę wypłacanych dywidend między datą rozliczenia i datą spłaty", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– liczba realizacji kuponów w roku!– określenie typu płatności"}, "COUPPCD": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Oblicza datę dywidendy poprzedzającej datę rozliczenia", "ad": "- data rozliczenia papieru wartościowego, podana jako liczba seryjna!- data spłaty papieru wartościowego, podana jako liczba seryjna!- liczba realizacji kuponów w roku!- określenie typu płatności"}, "CUMIPMT": {"a": "(stopa; liczba_okresów; wb; okres_pocz; okres_końc; typ)", "d": "Zwraca wartość skumulowanych odsetek zapłaconych w czasie między podanymi okresami", "ad": "– stopa procentowa!– całkowita liczba okresów płatności!– wartość bieżąca!– pierwszy okres w wyliczeniu!– ostatni okres w wyliczeniu!– określenie typu płatności"}, "CUMPRINC": {"a": "(stopa; liczba_okresów; wb; okres_pocz; okres_końc; typ)", "d": "Zwraca wartość skumulowanych odsetek zapłaconych w czasie między podanymi okresami", "ad": "- stopa procentowa!- całkowita liczba okresów płatności!- wartość bieżąca!- pierwszy okres w wyliczeniu!- ostatni okres w wyliczeniu!- określenie typu płatności"}, "DB": {"a": "(koszt; odzysk; czas_życia; okres; [mi<PERSON><PERSON><PERSON>])", "d": "Zwraca amortyzację środka trwałego za podany okres metodą równomiernie malejącego salda", "ad": "- warto<PERSON><PERSON> początkowa (koszt) środka trwałego!- wartość środka trwałego po całkowitym czasie amortyzacji!- liczba okresów stanowiących całkowity czas amortyzacji środka trwałego (nazywana również czasem życia zasobu)!- okres, dla którego obliczana jest amortyzacja. Okres musi być wyrażony w tych samych jednostkach co czas_życia!- liczba miesięcy w pierwszym roku. Przy pominięciu przyjmuje się wartość 12."}, "DDB": {"a": "(koszt; odzysk; czas_życia; okres; [współczynnik])", "d": "Zwraca amortyzację środka trwałego za podany okres obliczoną metodą podwójnego spadku lub inną metodą określoną przez użytkownika", "ad": "- warto<PERSON><PERSON> początkowa (koszt) środka trwałego!- warto<PERSON>ć środka trwałego po całkowitym czasie amortyzacji!- liczba okresów stanowiących całkowity czas amortyzacji środka trwałego (nazywana również czasem życia środka trwałego)!- okres, dla którego obliczana jest amortyzacja. Okres musi być wyrażony w tych samych jednostkach co czas_życia!- warto<PERSON><PERSON> sterująca szyb<PERSON>ścią, z jaką ma maleć saldo. Przy pominięciu przyjmuje się wartość 2 (metoda podwójnego spadku)"}, "DISC": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; kwota; wykup; [podstawa])", "d": "Zwrac<PERSON> warto<PERSON> stopy dyskontowej papieru wartościowego", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– cena papieru wartościowego za 100 jednostek wartości nominalnej!– wartość wykupu papieru wartościowego przypadająca na 100 jednostek wartości nominalnej!– rodzaj podstawy wyliczania dni"}, "DOLLARDE": {"a": "(wartoś<PERSON>_ułamkowa; ułamek)", "d": "Zamienia cenę w postaci ułamkowej na cenę w postaci dziesiętnej", "ad": "– liczba wyrażona jako ułamek!– liczba całkowita używana jako mianownik ułamka"}, "DOLLARFR": {"a": "(wartość_dziesiętna; ułamek)", "d": "Zamienia cenę w postaci dziesiętnej na cenę w postaci ułamkowej", "ad": "– liczba dziesiętna!– liczba całkowita używana jako mianownik ułamka"}, "DURATION": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; kupon; rent<PERSON>; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca wartość rocznego przychodu z papieru wartościowego o okresowych wypłatach odsetek", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– roczne oprocentowanie kuponu papieru wartościowego!– roczna rentowność papieru wartościowego!– liczba kuponów płatnych w ciągu roku!– rodzaj podstawy wyliczania dni"}, "EFFECT": {"a": "(stopa_nominalna; okresy)", "d": "Zwraca wartość efektywnej rocznej stopy oprocentowania", "ad": "– nominalna stopa procentowa!– liczba okresów składanych w roku"}, "FV": {"a": "(stopa; liczba_okresów; p<PERSON><PERSON><PERSON><PERSON><PERSON>; [wb]; [typ])", "d": "Oblicza przyszłą wartość inwestycji na podstawie okresowych, stałych płatności i stałej stopy procentowej", "ad": "- stopa procentowa dla okresu. Np. uż<PERSON>j stopy 6%/4 dla płatności kwartalnych w przypadku 6% stopy rocznej!- liczba wszystkich okresów płatności w czasie inwestycji!- p<PERSON><PERSON><PERSON><PERSON><PERSON> okres<PERSON>, niez<PERSON>nna przez cały czas trwania inwestycji!- warto<PERSON><PERSON> bieżąca inwestycji, teraźniejsza łączna wartość serii przyszłych płatności. Je<PERSON><PERSON> pominięta, wb = 0!- warto<PERSON><PERSON> określająca czas dokonywania płatności: pła<PERSON><PERSON><PERSON>ć na początek okresu = 1; pła<PERSON><PERSON><PERSON>ć na koniec okresu = 0 albo pominięta"}, "FVSCHEDULE": {"a": "(kapitał; stopy)", "d": "Zwraca wartość przyszłą kapitału początkowego wraz z szeregiem rat procentu składanego", "ad": "– wartość obecna kapitału!– tablica stóp procentowych"}, "INTRATE": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; lokata; wykup; [podstawa])", "d": "Zwraca warto<PERSON>ć stopy procentowej papieru wartościowego całkowicie ulokowanego", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– kwota zainwestowana w papier wartościowy!– kwota, kt<PERSON><PERSON><PERSON> otrzymamy w dniu płatności!– rodzaj podstawy wyliczania dni"}, "IPMT": {"a": "(stopa; okres; liczba_okresów; wb; [wp]; [typ])", "d": "Oblicza wysokość odsetek z inwestycji w danym okresie przy założeniu okresowych, stałych płatności i stałej stopy procentowej", "ad": "- stopa oprocentowania dla okresu. Np. użyj stopy 6%/4 dla kwartalnych płatności w przypadku 6% stopy rocznej!- okres, w którym mają być nalic<PERSON> ods<PERSON>ki; musi zawierać się między 1 a liczbą okresów!- liczba wszystkich okresów płatności w całym okresie inwestycji!- wartość bieżąca inwestycji, teraźniejsza łączna wartość serii przyszłych płatności!- warto<PERSON><PERSON> przyszła lub saldo kasowe, jakie ch<PERSON><PERSON> uzyskać po dokonaniu ostatniej płatności. Je<PERSON><PERSON> pominięta, pw = 0!- wartość logiczna określająca sposób dokonywania płatności: na koniec okresu = 0 lub pominięta; na początku okresu = 1"}, "IRR": {"a": "(war<PERSON><PERSON><PERSON>; [wynik])", "d": "Oblicza wewnętrzną stopę zwrotu dla przepływów gotówkowych", "ad": "- tablica lub odwołanie do komórek zawierających wartości do obliczenia wewnętrznej stopy zwrotu!- jest l<PERSON><PERSON><PERSON>, kt<PERSON><PERSON> jak <PERSON>, jest bliska wynikowi funkcji IRR; 0,1 (10 procent, je<PERSON><PERSON> pomi<PERSON>)"}, "ISPMT": {"a": "(stopa; okres; liczba_okresów; wb)", "d": "Oblicza wartość odsetek zapłaconych w trakcie określonego okresu inwestycji", "ad": "- stopa procentowa dla okresu. Np. użyj stopy 6%/4 dla kwartalnych płatności w przypadku 6% stopy rocznej!- okres, dla którego mają być obliczone odsetki!- liczba okresów płatności w czasie inwestycji!- teraźniejsza łączna wartość serii przyszłych płatności"}, "MDURATION": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; kupon; rent<PERSON>; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca warto<PERSON>ć zmodyfikowanego okresu Macauleya dla papieru wartościowego o założonej wartości 100 jednostek", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– roczne oprocentowanie kuponu papieru wartościowego!– roczna rentowność papieru wartościowego!– liczba kuponów płatnych w ciągu roku!– rodzaj podstawy wyliczania dni"}, "MIRR": {"a": "(warto<PERSON><PERSON>; stopa_finansowa; stopa_reinwestycji)", "d": "Oblicza wewnętrzną stopę zwrotu dla serii okresowych przepływów gotówkowych przy uwzględnieniu kosztu inwestycji i stopy procentowej reinwestycji gotówki", "ad": "- jest tablicą lub odwołaniem do komórek zawieraj<PERSON> liczby, które reprezentują serie płatności (ujemne) i dochodów (dodatnie) w regularnych okresach!- jest oprocentowaniem płaconym za pieniądze używane w przepływach gotówkowych!- jest oprocentowaniem uzyskiwanym z przepływów gotówkowych, gdy je reinwestujesz"}, "NOMINAL": {"a": "(stopa_efektywna; okresy)", "d": "Zwraca warto<PERSON>ć minimalnej rocznej stopy oprocentowania", "ad": "– efektywna stopa procentowa!– liczba okresów składanych w roku"}, "NPER": {"a": "(stopa; płat<PERSON><PERSON><PERSON>; wb; [wp]; [typ])", "d": "Oblicza liczbę okresów dla inwestycji opartej na okresowych, stałych płatnościach przy stałym oprocentowaniu", "ad": "- stopa procentowa dla okresu. Np. u<PERSON><PERSON>j stopy 6%/4 dla płatności kwartalnych w przypadku 6% stopy rocznej!- p<PERSON><PERSON><PERSON><PERSON><PERSON> okres<PERSON>, niezmienna przez cały czas trwania inwestycji!- warto<PERSON><PERSON> bieżąca inwestycji, teraźniejsza łączna wartość serii przyszłych płatności!- warto<PERSON><PERSON> przyszła lub saldo kasowe, jakie ch<PERSON><PERSON> uzyskać po dokonaniu ostatniej płatności. <PERSON><PERSON><PERSON> pomi<PERSON>, używane jest zero!- warto<PERSON>ć logiczna: płatność na początku okresu = 1; p<PERSON><PERSON><PERSON><PERSON>ć na końcu okresu = 0 lub pominięta"}, "NPV": {"a": "(stopa; wartość1; [wartość2]; ...)", "d": "Oblicza wartość bieżącą netto inwestycji w oparciu o okresowe przepływy środków pieniężnych przy określonej stopie dyskontowej oraz serii przyszłych płatności (wartości ujemne) i wpływów (wartości dodatnie)", "ad": "- stopa dyskontowa stała w danym okresie!- od 1 do 254 argumentów reprezentujących płatności i wpływy równo rozłożone w czasie i występujące na końcu każdego okresu"}, "ODDFPRICE": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; emisja; pierwszy_kupon; stopa; rent<PERSON>; wykup; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca cenę za 100 jednostek wartości nominalnej papieru wartościowego z nietypowym pierwszym okresem", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– data emisji papieru wartościowego, podana jako liczba seryjna!– data realizacji pierwszego kuponu, podana jako liczba seryjna!– oprocentowanie papieru wartościowego!– roczna rentowność papieru wartościowego!– wartość wykupu papieru wartościowego przypadająca na 100 jednostek wartości nominalnej!– liczba kuponów płatnych w ciągu roku!– rodzaj podstawy wyliczania dni"}, "ODDFYIELD": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; emisja; pierwszy_kupon; stopa; kwota; wykup; c<PERSON><PERSON><PERSON><PERSON><PERSON>; [podstawa])", "d": "Oblicza rentowność papieru wartościowego z nietypowym pierwszym okresem", "ad": "- data rozliczenia papieru wartościowego, podana jako liczba seryjna!- data spłaty papieru warto<PERSON>ciowego, podana jako liczba seryjna!- data emisji papieru wartościowego, podana jako liczba seryjna!- data realizacji pierwszego kuponu, podana jako liczba seryjna!- oprocentowanie papieru wartościowego!- cena papieru wartościowego!- warto<PERSON>ć wykupu papieru wartościowego przypadająca na 100 jednostek wartości nominalnej!- liczba kuponów płatnych w ciągu roku!- rodzaj podstawy wyliczania dni"}, "ODDLPRICE": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; ostatnia_wypłata; stopa; rent<PERSON>; wykup; c<PERSON><PERSON><PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca cenę za 100 jednostek wartości nominalnej papieru wartościowego z nietypowym ostatnim okresem", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– data realizacji ostatniego kuponu, podana jako liczba seryjna!– oprocentowanie papieru wartościowego!– roczna rentowność papieru wartościowego!– wartość wykupu papieru wartościowego przypadająca na 100 jednostek wartości nominalnej!– liczba kuponów płatnych w ciągu roku!– rodzaj podstawy wyliczania dni"}, "ODDLYIELD": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; ostatnia_wypłata; stopa; kwota; wykup; c<PERSON><PERSON><PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca rentowno<PERSON>ć papieru wartościowego z nietypowym ostatnim okresem", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– data realizacji ostatniego kuponu, podana jako liczba seryjna!– oprocentowanie papieru wartościowego!– cena papieru wartościowego!– wartość wykupu papieru wartościowego przypadająca na 100 jednostek wartości nominalnej!– liczba kuponów płatnych w ciągu roku!– rodzaj podstawy wyliczania dni"}, "PDURATION": {"a": "(stopa; wb; wp)", "d": "Zwraca liczbę okresów wymaganych przez inwestycję do osiągnięcia określonej wartości.", "ad": "— stopa procentowa dla okresu.!— wartość bieżąca inwestycji.!— żądana przyszła wartość inwestycji."}, "PMT": {"a": "(stopa; liczba_okresów; wb; [wp]; [typ])", "d": "Oblicza ratę spłaty pożyczki opartej na stałych ratach i stałym oprocentowaniu", "ad": "- stopa oprocentowania dla okresu pożyczki. Np. uż<PERSON>j stopy 6%/4 dla płatności kwartalnych w przypadku 6% stopy rocznej!- liczba wszystkich rat pożyczki!- war<PERSON><PERSON><PERSON>, c<PERSON><PERSON> całkowita obecna wartość serii przyszłych płatności!- warto<PERSON><PERSON> przyszła lub saldo kasowe, jakie ch<PERSON><PERSON>zy<PERSON> po dokonaniu ostatniej pła<PERSON>, 0 (zero), jeśli pomini<PERSON>ta!- wartość logiczna: płatność na początku okresu = 1; płatność na końcu okresu = 0 lub pominięta"}, "PPMT": {"a": "(stopa; okres; liczba_okresów; wb; [wp]; [typ])", "d": "O<PERSON>lic<PERSON> wartość spłaty kapitału dla danej inwestycji przy założeniu okresowych, stałych płatności i stałego oprocentowania", "ad": "- stopa procentowa dla okresu. Np. użyj stopy 6%/4 dla kwartalnych płatności w przypadku 6% stopy rocznej!- oznacza okres i musi zawierać się w zakresie między 1 a liczbą okresów!- liczba wszystkich okresów płatności w całym czasie inwestycji!- warto<PERSON><PERSON> bieżąca, c<PERSON>li całkowita obecna wartość serii przyszłych płatności!- warto<PERSON><PERSON> przyszła lub saldo kasowe, jakie chcesz uzyskać po dokonaniu ostatniej płatności!- wartość logiczna: płatność na początku okresu = 1; płatność na końcu okresu = 0 lub pominięta"}, "PRICE": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; stopa; rentown<PERSON>; wykup; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca cenę za 100 jednostek wartości nominalnej papieru wartościowego o okresowym oprocentowaniu", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– roczne oprocentowanie kuponu papieru wartościowego!– roczna rentowność papieru wartościowego!– wartość wykupu papieru wartościowego przypadająca na 100 jednostek wartości nominalnej!– liczba kuponów płatnych w ciągu roku!– rodzaj podstawy wyliczania dni"}, "PRICEDISC": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; dyskonto; wykup; [podstawa])", "d": "Zwraca cenę za 100 jednostek wartości nominalnej papieru wartościowego zdyskontowanego", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– stopa dyskontowa papieru wartościowego!– wartość wykupu papieru wartościowego przypadająca na 100 jednostek wartości nominalnej!– rodzaj podstawy wyliczania dni"}, "PRICEMAT": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; emisja; stopa; rentown<PERSON>; [podstawa])", "d": "Zwraca cenę za 100 jednostek wartości nominalnej papieru wartościowego oprocentowanego przy wykupie", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– data emisji papieru wartościowego, podana jako liczba seryjna!– stopa procentowa papieru wartościowego w dniu emisji!– roczna rentowność papieru wartościowego!– rodzaj podstawy wyliczania dni"}, "PV": {"a": "(stopa; liczba_okresów; p<PERSON><PERSON><PERSON><PERSON><PERSON>; [wp]; [typ])", "d": "Oblic<PERSON> wartość bieżącą inwestycji — całkowitą obecną wartość serii przyszłych pła<PERSON>ci", "ad": "- stopa procentowa dla okresu. Np. użyj stopy 6%/4 dla płatności kwartalnych w przypadku 6% stopy rocznej!- liczba wszystkich okresów płatności inwestycji!- p<PERSON><PERSON><PERSON><PERSON><PERSON> okres<PERSON>, niez<PERSON><PERSON> przez cały czas trwania inwestycji!- war<PERSON><PERSON><PERSON> przyszła lub saldo kasowe, jakie ch<PERSON><PERSON>zy<PERSON> po dokonaniu ostatniej płatności!- wartość logiczna: płatność na początku okresu = 1; płatność na końcu okresu = 0 lub pominięta"}, "RATE": {"a": "(liczba_okresów; p<PERSON><PERSON><PERSON><PERSON><PERSON>; wb; [wp]; [typ]; [wynik])", "d": "Oblicza stopę procentową dla okresu pożyczki lub inwestycji. Np. użyj stopy 6%/4 dla płatności kwartalnych w przypadku 6% stopy rocznej", "ad": "- liczba wszystkich okresów płatności pożyczki lub inwestycji!- p<PERSON><PERSON><PERSON><PERSON><PERSON> okresowa, niez<PERSON>nna przez cały czas pożyczki lub inwestycji!- warto<PERSON><PERSON> bi<PERSON>, c<PERSON><PERSON> całkowita obecna wartość serii przyszłych płatności!- warto<PERSON><PERSON> przyszła lub saldo kasowe, jakie ch<PERSON>z uzyskać po dokonaniu ostatniej płatności. <PERSON><PERSON><PERSON> pomi<PERSON>, używana jest wartość wp = 0!- wartość logiczna: płatność na początku okresu = 1; płatność na końcu okresu = 0 lub pominięta!- przypuszczalna stopa procentowa; jeśli pominięta, wynik = 0,1 (10 procent)"}, "RECEIVED": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; lokata; dyskonto; [podstawa])", "d": "Zwraca wartość kapitału otrzymanego przy wykupie papieru wartościowego całkowicie ulokowanego", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– kwota zainwestowana w papier wartościowy!– stopa dyskontowa papieru wartościowego!– rodzaj podstawy wyliczania dni"}, "RRI": {"a": "(liczba_rat; wb; wp)", "d": "Zwraca równoważną stopę procentową dla wzrostu inwestycji.", "ad": "— liczba okresów dla inwestycji.!— bieżąca wartość inwestycji.!— p<PERSON><PERSON><PERSON><PERSON> wartość inwestycji."}, "SLN": {"a": "(koszt; odzysk; czas_życia)", "d": "Zwraca amortyzację środka trwałego za pojedynczy okres metodą liniową", "ad": "- warto<PERSON><PERSON> początkowa (koszt) środka trwałego!- wartość środka trwałego po całkowitym czasie amortyzacji!- liczba okresów stanowiących całkowity czas amortyzacji środka trwałego (nazywana również czasem życia środka trwałego)"}, "SYD": {"a": "(koszt; odzysk; czas_życia; okres)", "d": "Oblicza amortyzację środka trwałego za podany okres metodą sumy cyfr wszystkich lat amortyzacji", "ad": "- warto<PERSON><PERSON> początkowa (koszt) środka trwałego!- wartość środka trwałego po całkowitym czasie amortyzacji!- liczba okresów stanowiących całkowity czas amortyzacji środka trwałego (nazywana również czasem życia środka trwałego)!- okres, dla którego obliczana jest amortyzacja. Okres musi być wyrażony w tych samych jednostkach co czas_życia"}, "TBILLEQ": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; dyskonto)", "d": "Zwrac<PERSON> ekwiwalentu obligacji dla bonu skarbowego", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru warto<PERSON>ciowego, podana jako liczba seryjna!– stopa dyskontowa bonu skarbowego"}, "TBILLPRICE": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; dyskonto)", "d": "Zwraca cenę za 100 jednostek wartości nominalnej bonu skar<PERSON>ego", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru warto<PERSON>ciowego, podana jako liczba seryjna!– stopa dyskontowa bonu skarbowego"}, "TBILLYIELD": {"a": "(roz<PERSON>zenie; data_spłaty; kwota)", "d": "<PERSON><PERSON><PERSON><PERSON> bonu skar<PERSON>", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru warto<PERSON>ciowego, podana jako liczba seryjna!– cena za 100 jednostek wartości nominalnej bonu skarbowego"}, "VDB": {"a": "(koszt; odzysk; czas_życia; początek; koniec; [współczynnik]; [bez_prz<PERSON>ł<PERSON><PERSON><PERSON>])", "d": "Zwraca amortyzację środka trwałego za podany okres lub jego część obliczoną metodą podwójnie malejącego salda lub inną podaną metodą", "ad": "- warto<PERSON><PERSON> poc<PERSON><PERSON> (koszt) środka trwałego!- warto<PERSON><PERSON> środka trwałego po całkowitym czasie amortyzacji!- liczba okresów stanowiących całkowity czas amortyzacji środka trwałego (nazywana również czasem życia środka trwałego)!- okres początkowy, w którym ma być obliczona wartość amortyzacji, w tych samych jednostkach co czas_życia!- okres końcowy, w którym ma być obliczona wartość amortyzacji, w tych samych jednostkach co czas_życia!- warto<PERSON><PERSON> sterująca szybkością, z jaką ma maleć saldo, jeśli pomini<PERSON>ta, 2 (metoda podwójnie malejącego salda)!- warto<PERSON><PERSON> <PERSON>zna określająca, czy przej<PERSON><PERSON> do metody liniowej, jeśli amortyzacja okaże się wię<PERSON><PERSON>, niż obliczona metodą malejącego salda = FAŁSZ lub pominięta; czy nie przechodzić = PRAWDA"}, "XIRR": {"a": "(war<PERSON><PERSON><PERSON>; daty; [wynik])", "d": "O<PERSON>lic<PERSON> warto<PERSON>ć wewnętrznej stopy zwrotu dla serii rozłożonych w czasie przepływów gotówkowych", "ad": "- seria przepływów gotówkowych odpowiadająca zestawieniu płatności wg dat!- zestawienie dat płatności odpowiadających przepływom gotówkowym!- przewidywany wynik funkcji XIRR"}, "XNPV": {"a": "(stopa; wartości; daty)", "d": "Oblicza wartość bieżącą netto serii rozłożonych w czasie przepływów gotówkowych", "ad": "- stopa dyskontowa stosowana dla przepływów gotówkowych!- seria przepływów gotówkowych odpowiadająca zestawieniu płatności wg dat!- zestawienie dat płatności odpowiadających przepływom gotówkowym"}, "YIELD": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; stopa; kwota; wykup; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca rentown<PERSON>ć papieru wartościowego o okresowym oprocentowaniu", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– roczne oprocentowanie kuponu papieru wartościowego!– cena papieru wartościowego za 100 jednostek wartości nominalnej!– wartość wykupu papieru wartościowego przypadająca na 100 jednostek wartości nominalnej!– liczba kuponów płatnych w ciągu roku!– rodzaj podstawy wyliczania dni"}, "YIELDDISC": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; kwota; wykup; [podstawa])", "d": "Zwraca roczną rent<PERSON> zdyskontowanego papieru war<PERSON>ściowego, np. bonu skarbowego", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– cena papieru wartościowego za 100 jednostek wartości nominalnej!– wartość wykupu papieru wartościowego przypadająca na 100 jednostek wartości nominalnej!– rodzaj podstawy wyliczania dni"}, "YIELDMAT": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; emisja; stopa; kwota; [podstawa])", "d": "Zwraca roczną rentown<PERSON> papieru wartościowego oprocentowanego przy wykupie", "ad": "– data rozliczenia papieru wartościowego, podana jako liczba seryjna!– data spłaty papieru wartościowego, podana jako liczba seryjna!– data emisji papieru wartościowego, podana jako liczba seryjna!– stopa procentowa papieru wartościowego w dniu emisji!– cena papieru wartościowego za 100 jednostek wartości nominalnej!– rodzaj podstawy wyliczania dni"}, "ABS": {"a": "(liczba)", "d": "Zwraca wartość bezwzględną liczby, warto<PERSON><PERSON> bez znaku", "ad": "- liczba rzeczywista, kt<PERSON><PERSON><PERSON> bezwzględna zostanie obliczona"}, "ACOS": {"a": "(liczba)", "d": "Zwraca arcus cosinus liczby w radianach w zakresie od 0 do Pi. Arcus cosinus jest kątem, którego cosinus daje liczbę", "ad": "- cosinus szukanego kąta; musi się zawierać w przedziale od -1 do 1"}, "ACOSH": {"a": "(liczba)", "d": "<PERSON>wrac<PERSON> arcus cosinus hip<PERSON> liczby", "ad": "- dowolna liczba rzeczywista równa lub większa od 1"}, "ACOT": {"a": "(liczba)", "d": "Zwraca arcus cotangens liczby w radianach w zakresie od 0 do Pi.", "ad": "- cotangens żądanego kąta."}, "ACOTH": {"a": "(liczba)", "d": "Zwraca arcus cotangens hiperboliczny liczby.", "ad": "- cotangens hiperboliczny żądanego kąta."}, "AGGREGATE": {"a": "(nr_<PERSON><PERSON><PERSON>; opcje; odw1; ...)", "d": "Zwrac<PERSON> war<PERSON> zagregowaną z listy lub bazy danych", "ad": "- liczba z zakresu od 1 do 19, która określa funkcję podsumowania dla wartości zagregowanej!- liczba z zakresu od 0 do 7, która określa wartości do zignorowania dla wartości zagregowanej!- tablica lub zakres danych liczbowych używanych do obliczania wartości zagregowanej!- wskazuje pozycję w tablicy; jest k-tą największą, k-tą najmniejszą, k-tym percentylem lub k-tym kwartylem!- liczba z zakresu od 1 do 19, która określa funkcję podsumowania dla wartości zagregowanej!- liczba z zakresu od 0 do 7, która określa wartości do zignorowania dla wartości zagregowanej!- od 1 do 253 zakresów lub odwołań, dla kt<PERSON><PERSON>ch będzie obliczana wartość zagregowana"}, "ARABIC": {"a": "(tekst)", "d": "Konwertuje liczbę rzymską na arabską.", "ad": "- liczba rzymska, która ma zostać przekonwertowana."}, "ASC": {"a": "(tekst)", "d": "W językach korzystających z dwubajtowego zestawu znaków (DBCS) funkcja zmienia znaki o pełnej szerokości (dwubajtowe) na znaki o połówkowej szerokości (jednobajtowe)", "ad": "- te<PERSON><PERSON>, kt<PERSON>ry ma zostać zmieniony."}, "ASIN": {"a": "(liczba)", "d": "Zwraca arcus sinus liczby w radianach w zakresie od -Pi/2 do Pi/2", "ad": "- sinus szukanego kąta; musi zawierać się w przedziale od -1 do 1"}, "ASINH": {"a": "(liczba)", "d": "Zwraca arcus sinus hiperboliczny liczby", "ad": "- dowolna liczba rzeczywista równa lub większa od 1"}, "ATAN": {"a": "(liczba)", "d": "Zwraca arcus tangens liczby w radianach w zakresie od -Pi/2 do Pi/2", "ad": "- tangens żądanego kąta"}, "ATAN2": {"a": "(x_liczba; y_liczba)", "d": "Zwraca na podstawie współrzędnych x i y arcus tangens wyrażony w radianach w zakresie od -Pi do Pi z wyłączeniem -Pi", "ad": "- współrzędna x danego punktu!- współrzędna y danego punktu"}, "ATANH": {"a": "(liczba)", "d": "Zwraca arcus tangens hiperboliczny liczby", "ad": "- liczba rzeczywista z przedziału od -1 do 1, bez -1 i 1 "}, "BASE": {"a": "(l<PERSON><PERSON>; podstawa; [dług<PERSON><PERSON><PERSON>_min])", "d": "Konwertuje liczbę na reprezentację tekstową o podanej podstawie.", "ad": "— l<PERSON><PERSON>, która ma zostać przekonwertowana.!— podstawa, do której liczba ma zostać przekonwertowana.!— <PERSON>na długość zwracanego ciągu. <PERSON><PERSON><PERSON>, wiodące zera nie zostaną dodane."}, "CEILING": {"a": "(l<PERSON><PERSON>; istotność)", "d": "Zaokrągla liczbę w górę do najbliższej wielokrotności podanej istotności", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ra ma być zaokrąglona!- parametr określający wielokrotność zaokrąglenia"}, "CEILING.MATH": {"a": "(liczba; [isto<PERSON><PERSON><PERSON><PERSON>]; [tryb])", "d": "Zaokrągla liczbę w górę do najbliższej liczby całkowitej lub najbliż<PERSON>ej wielokrotności istotności.", "ad": "<PERSON> <PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> ma zostać zaokrąglona.!— <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, do jakiej nastą<PERSON> zaokrąglanie.!<PERSON><PERSON> podano warto<PERSON> ni<PERSON>, ta funkcja dokona zaokrąglenia w kierunku od zera."}, "CEILING.PRECISE": {"a": "(l<PERSON><PERSON>; [isto<PERSON><PERSON><PERSON><PERSON>])", "d": "Zaokrągla liczbę w górę do najbliższ<PERSON> wartości całkowitej lub wielokrotności podanej istotności", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ra ma być zaokrąglona!- parametr określający wielokrotność zaokrąglenia"}, "COMBIN": {"a": "(liczba; liczba_wybrana)", "d": "Zwraca liczbę kombinacji dla danej liczby elementów", "ad": "- łączna liczba elementów!- liczba elementów w każdej kombinacji"}, "COMBINA": {"a": "(liczba; liczba_wybrana)", "d": "Zwraca liczbę kombinacji z powtórzeniami dla podanej liczby elementów.", "ad": "— całkowita liczba elementów.!— liczba elementów w każdej kombinacji."}, "COS": {"a": "(liczba)", "d": "Zwraca cosinus k<PERSON>ta", "ad": "- kąt podany w radia<PERSON><PERSON>, dla którego zostanie wyznaczony cosinus"}, "COSH": {"a": "(liczba)", "d": "Zwraca cosinus hip<PERSON>bolic<PERSON> l<PERSON>by", "ad": "- dowolna liczba rzeczywista"}, "COT": {"a": "(liczba)", "d": "Zwraca cotangens kąta.", "ad": "- kąt podany w radianach, dla którego ma zostać obliczony cotangens."}, "COTH": {"a": "(liczba)", "d": "Zwraca cotangens hiperboliczny liczby.", "ad": "- kąt podany w radia<PERSON>h, dla którego ma zostać obliczony cotangens hiperboliczny."}, "CSC": {"a": "(liczba)", "d": "Zwraca cosecans kąta.", "ad": "- kąt podany w radianach, dla którego ma zostać obliczony cosecans."}, "CSCH": {"a": "(liczba)", "d": "Zwraca cosecans hiperboliczny kąta.", "ad": "- kąt podany w radia<PERSON>h, dla którego ma zostać obliczony cosecans hiperboliczny."}, "DECIMAL": {"a": "(liczba; podstawa)", "d": "Konwertuje reprezentację tekstową liczby o podanej podstawie na liczbę dziesiętną.", "ad": "— l<PERSON><PERSON>, która ma zostać przekonwertowana.!— podstawa konwertowanej liczby."}, "DEGREES": {"a": "(kąt)", "d": "Konwertuje radiany na stopnie", "ad": "- kąt podany w radianach, kt<PERSON><PERSON> ma być poddany konwersji"}, "ECMA.CEILING": {"a": "(l<PERSON><PERSON>; istotność)", "d": "Zaokrągla liczbę w górę do najbliższej wielokrotności podanej istotności", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ra ma być zaokrąglona!- parametr określający wielokrotność zaokrąglenia"}, "EVEN": {"a": "(liczba)", "d": "Zaokrągla liczbę dodatnią w górę, a liczbę ujemną w dół do najbliższej parzystej liczby całkowitej", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ra ma <PERSON>ć zaokrąglon<PERSON>"}, "EXP": {"a": "(liczba)", "d": "Oblicza wartość liczby e podniesionej do potęgi określonej przez podaną liczbę", "ad": "- jest w<PERSON><PERSON><PERSON><PERSON><PERSON>, do którego podnoszona jest liczba e. Stała e jest równa 2,71828182845904 i jest podstawą logarytmów naturalnych"}, "FACT": {"a": "(liczba)", "d": "Oblicza silnię podanej liczby równą 1*2*3...* Liczba", "ad": "- licz<PERSON> nieu<PERSON>, której silnia ma zostać obliczona"}, "FACTDOUBLE": {"a": "(liczba)", "d": "Zwraca dwukrotną silnię liczby", "ad": "<PERSON> <PERSON><PERSON><PERSON><PERSON>, dla której ma zostać obliczona dwukrotna silnia"}, "FLOOR": {"a": "(l<PERSON><PERSON>; istotność)", "d": "Zaokrągla liczbę w dół do najbliższej wielokrotności podanej istotności", "ad": "- <PERSON><PERSON><PERSON><PERSON> l<PERSON>, która ma zostać zaokrąglona!- <PERSON><PERSON><PERSON><PERSON> wielokrot<PERSON>, do jakiej nastąpi zaokrąglanie. Liczba i Istotność muszą być albo dodatnie, albo obie ujemne"}, "FLOOR.PRECISE": {"a": "(l<PERSON><PERSON>; [isto<PERSON><PERSON><PERSON><PERSON>])", "d": "Zaokrągla liczbę w dół do najbli<PERSON><PERSON><PERSON> wartości całkowitej lub wielokrotności podanej istotności", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ra ma być zaokrąglona!- parametr określający wielokrotność zaokrąglenia"}, "FLOOR.MATH": {"a": "(liczba; [isto<PERSON><PERSON><PERSON><PERSON>]; [tryb])", "d": "Zaokrągla liczbę w dół do najbliższej liczby całkowitej lub najbliż<PERSON>ej wielokrotności istotności.", "ad": "<PERSON> <PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> ma zostać zaokrąglona.!— <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, do jakiej nastą<PERSON> zaokrąglanie.!<PERSON><PERSON> podano warto<PERSON> ni<PERSON>, ta funkcja dokona zaokrąglenia w kierunku zera."}, "GCD": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca największy wspólny dzielnik", "ad": "- sekwencja od 1 do 255 wartości"}, "INT": {"a": "(liczba)", "d": "Zaokrągla liczbę w dół do najbliższej liczby całkowitej", "ad": "- liczba rzeczywista, która ma zostać zaokrąglona w dół do liczby całkowitej"}, "ISO.CEILING": {"a": "(l<PERSON><PERSON>; [isto<PERSON><PERSON><PERSON><PERSON>])", "d": "Zaokrągla liczbę w górę do najb<PERSON><PERSON>sz<PERSON> wartości całkowitej lub wielokrotności podanej istotności. Zaokrąglenie następuje w górę niezależnie od znaku liczby. <PERSON><PERSON><PERSON> liczba lub isto<PERSON><PERSON>ć wynosi zero, jest zwracana wartość zero.", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ra ma być zaokrąglona!- parametr określający wielokrotność zaokrąglenia"}, "LCM": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca najmniejszą wspólną wielokrotność", "ad": "- sekwencja od 1 do 255 liczb, dla k<PERSON><PERSON><PERSON>ch należy obliczyć najmniejszą wspólną wielokrotność"}, "LN": {"a": "(liczba)", "d": "Zwraca logarytm naturalny podanej liczby", "ad": "- dodatnia liczba rzeczywista, której logarytm naturalny ma zostać obliczony"}, "LOG": {"a": "(liczba; [podstawa])", "d": "Zwraca logarytm liczby przy podanej podstawie", "ad": "- dodatnia liczba rzeczywista, której logarytm ma zostać obliczony!- podstawa logarytmu; j<PERSON><PERSON><PERSON>, 10"}, "LOG10": {"a": "(liczba)", "d": "Oblicza logarytm dziesiętny podanej liczby", "ad": "- dodatnia liczba rzeczywista, której logarytm dziesiętny ma zostać obliczony"}, "MDETERM": {"a": "(tablica)", "d": "Zwraca wyznacznik podanej tablicy", "ad": "- tablica liczbowa o równej liczbie wierszy i kolumn, albo zakres komórek lub stała tablicowa"}, "MINVERSE": {"a": "(tablica)", "d": "Zwraca macierz odwrotną do macierzy przechowywanej w tablicy", "ad": "- tablica liczbowa o równej liczbie wierszy i kolumn, albo zakres komórek lub stała tablicowa"}, "MMULT": {"a": "(tablica1; tablica2)", "d": "Zwraca iloczyn dwóch tablic, tablica o tej samej liczbie wierszy, co tablica1 i tej samej liczbie kolumn, co tablica2", "ad": "- pierwsza tablica liczb do przemnożenia, musi mieć tę samą liczbę kolumn, co liczba wierszy Tablicy2"}, "MOD": {"a": "(liczba; dzielnik)", "d": "Zwraca resztę z dzielenia", "ad": "- l<PERSON><PERSON>, dla której chcesz znaleźć resztę z dzielenia!- l<PERSON><PERSON>, przez którą chcesz podzielić Liczbę"}, "MROUND": {"a": "(l<PERSON><PERSON>; wielokrotność)", "d": "Zwraca wartość liczby zaokrąglonej do podanej wielokrotności", "ad": "– <PERSON><PERSON><PERSON><PERSON>, która będzie zaokrąglana!– w<PERSON><PERSON><PERSON><PERSON><PERSON>ć, do której należy zaokrąglić liczbę"}, "MULTINOMIAL": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca wielomian dla zestawu liczb", "ad": "- sekwencja od 1 do 255 <PERSON><PERSON><PERSON><PERSON>, d<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> należy utworzyć wielomian"}, "MUNIT": {"a": "(wymiar)", "d": "Zwraca macierz jednostkową dla określonego wymiaru.", "ad": "- liczba całkowita określająca wymiar macierzy jednostkowej, która ma zostać zwrócona."}, "ODD": {"a": "(liczba)", "d": "Zaokrągla liczbę dodatnią w górę, a liczbę ujemną w dół do najbliższej liczby nieparzystej całkowitej", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ra ma <PERSON>ć zaokrąglon<PERSON>"}, "PI": {"a": "()", "d": "Z<PERSON><PERSON><PERSON> liczby Pi, 3,14159265358979 z dokładnością do 15 cyfr po przecinku", "ad": ""}, "POWER": {"a": "(liczba; potęga)", "d": "Zwraca liczbę podniesioną do potęgi", "ad": "- podstawa potęgi, dowolna liczba rzeczywista!- w<PERSON><PERSON><PERSON><PERSON>, do którego zostanie podniesiona podstawa"}, "PRODUCT": {"a": "(liczba1; [liczba2]; ...)", "d": "Mnoży wszystkie liczby dane jako argumenty", "ad": "- od 1 do 255 liczb, wartości logicznych lub tekstowej reprezentacji liczb, kt<PERSON>re mają by<PERSON> mnożone"}, "QUOTIENT": {"a": "(dzielna; dzielnik)", "d": "Zwraca część całkowitą z dzielenia", "ad": "– wartość dzielnej!– wartość dzielnika"}, "RADIANS": {"a": "(kąt)", "d": "Konwertuje stopnie na radiany", "ad": "- kąt podany w stopniach, kt<PERSON><PERSON> ma być poddany konwersji"}, "RAND": {"a": "()", "d": "Zwraca liczbę losową o równomiernym rozkładzie, która jest większa lub równa 0 i mniejsza niż 1 (zmienia się przy ponownym obliczaniu)", "ad": ""}, "RANDARRAY": {"a": "([wiersze]; [kolumny]; [min]; [maks]; [licz<PERSON>_całkowita])", "d": "Zwraca tablicę liczb losowych", "ad": "liczba wierszy w zwróconej tablicy!liczba kolumn w zwróconej tablicy!minimalna liczba, która ma być zwrócona!maksymalna liczba, która ma być zwrócona!zwraca liczbę całkowitą lub wartość dziesiętną. Wartość TRUE dla liczby całkowitej, FALSE dla liczby dziesiętnej"}, "RANDBETWEEN": {"a": "(dół; góra)", "d": "Zwraca liczbę losową z przedziału pomiędzy podanymi wartościami", "ad": "– najmniejsza liczba całkowita, jak może zostać podana przez funkcję!– największa liczba całkowita, jak może zostać podana przez funkcję"}, "ROMAN": {"a": "(liczba; [forma])", "d": "Konwertuje liczbę arabską na rzymską jako tekst", "ad": "- liczba arabska, która ma być konwertowana!- liczba określająca żądany typ zapisu rzymskiego."}, "ROUND": {"a": "(liczba; liczba_cyfr)", "d": "Zaokrągla liczbę do określonej liczby cyfr", "ad": "- l<PERSON><PERSON>, kt<PERSON>ra ma być zaokrąglona!- ok<PERSON><PERSON><PERSON> liczbę cyfr, do której ma zostać zaokrąglona dana liczba. Ujemne liczby oznaczają zaokrąglenia do miejsc po lewej stronie przecinka; zero najbliższą liczbę całkowitą"}, "ROUNDDOWN": {"a": "(liczba; liczba_cyfr)", "d": "Zaokrągla liczbę w dół (w kierunku: do zera)", "ad": "- dowolna liczba rzeczywista, która ma zostać zaokrąglona w dół!- liczba cyfr, do której ma zostać zaokrąglona liczba. Wartość ujemna powoduje zaokrąglanie do miejsc po lewej stronie przecinka dziesiętnego; zero lub wartość pominięta oznacza zaokrąglenie do najbliższej liczby całkowitej"}, "ROUNDUP": {"a": "(liczba; liczba_cyfr)", "d": "Zaokrągla liczbę w górę (w kierunku: od zera)", "ad": "- dowolna liczba rzeczywista, która ma być zaokrąglona w górę!- liczba cyfr, do której ma zostać zaokrąglona liczba. Wartość ujemna powoduje zaokrąglanie do miejsc po lewej stronie przecinka dziesiętnego; zero lub wartość pominięta oznacza zaokrąglenie do najbliższej liczby całkowitej"}, "SEC": {"a": "(liczba)", "d": "Zwraca secans kąta.", "ad": "- kąt podany w radianach, dla którego ma zostać obliczony secans."}, "SECH": {"a": "(liczba)", "d": "Zwraca secans hiperboliczny kąta.", "ad": "- kąt podany w radia<PERSON>h, dla którego ma zostać obliczony secans hiperboliczny."}, "SERIESSUM": {"a": "(x; n; m; współcz<PERSON>iki)", "d": "Oblicza sumę szeregu potęgowego wg odpowiedniego wzoru", "ad": "– wartość początkowa dla szeregu potęgowego!– potęga początkowa, do której ma być podniesiona wartość x!– krok, o który wzrasta n dla każdego składnika w szeregu!– zbiór współczynników, przez które mnoży się każdą kolejną potęgę x"}, "SIGN": {"a": "(liczba)", "d": "Zwraca znak podanej liczby: 1, je<PERSON><PERSON> liczba jest doda<PERSON>nia, zero, jeśli jest równa zero lub -1, je<PERSON>li jest ujemna", "ad": "- dowolna liczba rzeczywista"}, "SIN": {"a": "(liczba)", "d": "Zwraca sinus kąta", "ad": "- kąt podany w radianach, którego sinus ma zostać obliczony. Stopnie * PI()/180 = radiany"}, "SINH": {"a": "(liczba)", "d": "Oblicza sinus hiperboliczny liczby", "ad": "- dowolna liczba rzeczywista"}, "SQRT": {"a": "(liczba)", "d": "Zwraca pierwiastek kwadratowy liczby", "ad": "- <PERSON><PERSON><PERSON>, dla której chcesz uzyskać pierwiastek kwadratowy"}, "SQRTPI": {"a": "(liczba)", "d": "Zwraca pierwiastek kwadratowy z wartości (liczba * pi)", "ad": "– liczba mnożona przez pi"}, "SUBTOTAL": {"a": "(funkcja_nr; adres1; ...)", "d": "Oblicza sumę częściową listy lub bazy danych", "ad": "- liczba z zakresu od 1 do 11 określająca, która funkcja zostanie użyta do obliczenia sum częściowych.!- od 1 do 254 zakresów lub od<PERSON>łań, dla kt<PERSON><PERSON>ch ma być obliczona suma częściowa"}, "SUM": {"a": "(liczba1; [liczba2]; ...)", "d": "Dodaje wszystkie liczby w zakresie komórek", "ad": "- od 1 do 255 argumentów, kt<PERSON>re zostaną zsumowane. Wartości logiczne i tekst w komórkach są ignorowane, a uwzględniane, jeś<PERSON> są wpisane jako argumenty"}, "SUMIF": {"a": "(zakres; kryteria; [suma_zakres])", "d": "Dodaje komórki spełniające podane warunki lub kryteria", "ad": "- z<PERSON><PERSON> komórek, kt<PERSON>re mają zostać obliczone!- warunek lub kryteria określające, które komórki zostaną dodane, podane w postaci liczby, wyrażenia lub tekstu!- faktycznie sumowane komórki. <PERSON><PERSON><PERSON>, używane są komórki w zakresie"}, "SUMIFS": {"a": "(suma_zakres; kryteria_zakres; kryteria; ...)", "d": "Oblicza sumę komórek spełniających dany zestaw warunków lub kryteriów", "ad": "- faktycznie sumowane komórki.!- <PERSON><PERSON><PERSON> komórek, dla kt<PERSON><PERSON>ch należy sprawdzić określony warunek!- warunek lub kryteria określające sumowane komórki, podane w postaci liczby, wyrażenia lub tekstu"}, "SUMPRODUCT": {"a": "(tablica1; [tablica2]; [tablica3]; ...)", "d": "Zwraca sumę iloczynów odpowiadających sobie zakresów lub tablic", "ad": "- od 2 do 255 tablic, których składniki mają zostać pomnożone, a następnie dodane. Wszystkie tablice muszą mieć te same wymiary"}, "SUMSQ": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca sumę kwadratów argumentów. Argumenty mogą by<PERSON> l<PERSON>, tab<PERSON>mi, nazwami lub odwołaniami do komórek zawierających liczby", "ad": "- od 1 do 255 liczb lub nazw, tablic albo od<PERSON><PERSON>ń, dla k<PERSON><PERSON><PERSON><PERSON> ma być obliczona suma kwadratów "}, "SUMX2MY2": {"a": "(tablica_x; tablica_y)", "d": "Sumuje różnice między kwadratami dwóch odpowiadających sobie zakresów lub tablic", "ad": "- pierwsza tablica lub zak<PERSON> warto<PERSON>, może to być liczba lub nazwa, tablica lub odwołanie zawierające liczby!- drugi zakres lub tablica liczb, może to być liczba lub nazwa, tablica lub odwołanie zawierające liczby"}, "SUMX2PY2": {"a": "(tablica_x; tablica_y)", "d": "Zwraca sumę końcową sum kwadratów liczb w dwóch odpowiadających sobie zakresach lub tablicach", "ad": "- pierwsza tablica lub zakres liczb, może to być liczba lub nazwa, tablica lub odwołanie zawierające liczby!- drugi zakres lub tablica liczb, może to być liczba lub nazwa, tablica lub odwołanie zawierające liczby"}, "SUMXMY2": {"a": "(tablica_x; tablica_y)", "d": "Sumuje kwadraty różnic w dwóch odpowiadających sobie zakresach lub tablicach", "ad": "- pierwsza tablica lub zakres wartości, może to być liczba lub nazwa, tablica lub odwołanie zawierające liczby!- drugi zakres lub tablica wartości, może to być liczba lub nazwa, tablica lub odwołanie zawierające liczby"}, "TAN": {"a": "(liczba)", "d": "Zwraca tangens kąta", "ad": "- kąt podany w radianach, którego tangens ma być obliczony. Stopnie*PI()/180 = radiany"}, "TANH": {"a": "(liczba)", "d": "Zwraca tangens hiperboliczny liczby", "ad": "- dowolna liczba rzeczywista"}, "TRUNC": {"a": "(liczba; [liczba_cyfr])", "d": "Obcina liczbę do liczby całkowitej, usuwając część dziesiętną lub ułamkową", "ad": "- l<PERSON><PERSON>, która ma zostać obcięta!- liczba określająca dokładność obcięcia, 0 (zero) jeśli pominięta"}, "ADDRESS": {"a": "(nr_w<PERSON><PERSON>; nr_kolumny; [typ_adresu]; [a1]; [tekst_a<PERSON><PERSON><PERSON>])", "d": "Tworzy tekst odwołania do komórki z podanego numeru wiersza i numeru komórki", "ad": "- numer wiersza używany w odwołaniu do komórki: Numer_wiersza = 1 dla wiersza 1!- numer kolumny używany w odwołaniu do komórki. Na przykład Numer_kolumny =4 dla kolumny D!określa typ odwołania: bezwzględne = 1; bezwzględny wiersz/względna kolumna = 2; względny wiersz/bezwzględna kolumna = 3; względne = 4!- wartość logiczna określająca styl odwołań: styl A1 = 1 lub PRAWDA; STYL W1K1 = 0 lub FAŁSZ!- tekst określający nazwę arkusza używanego w odwołaniach zewnętrznych"}, "CHOOSE": {"a": "(nr_arg; wartość1; [wartość2]; ...)", "d": "Wybiera z listy wartość lub czynność do wykonania na podstawie numeru wskaźnika.", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ry argument zostanie wybrany. Num_indeksu musi się zawierać w przedziale między 1 i 254 lub być formułą albo odwołaniem do liczby między 1 i 254!- od 1 do 254 liczb, od<PERSON><PERSON><PERSON> do komórek, zdefiniowanych nazw, formuł lub tekstów, z których funkcja WYBIERZ może wybierać"}, "COLUMN": {"a": "([odwo<PERSON>nie])", "d": "Zwraca numer kolumny odpowiadający podanemu odwołaniu", "ad": "- komórka lub zak<PERSON> komórek, kt<PERSON>rych numer kolumny zostanie wyznaczony. <PERSON><PERSON><PERSON>, używana jest komórka zawierająca funkcję KOLUMNA"}, "COLUMNS": {"a": "(tablica)", "d": "Zwraca liczbę kolumn w tablicy lub odwołaniu", "ad": "- tablica, formuła zwracająca tablicę lub odwołanie do zakresu komórek, dla kt<PERSON><PERSON>ch zostanie wyznaczona liczba kolumn"}, "FORMULATEXT": {"a": "(odwołanie)", "d": "Zwraca formułę jako ciąg.", "ad": "- odwołanie do formuły."}, "HLOOKUP": {"a": "(odniesienie; tablica; nr_wiersza; [wiersz])", "d": "<PERSON>yszu<PERSON><PERSON> wartość w górnym wierszu tabeli lub tablicy wartości i zwraca wartość z tej samej kolumny ze wskazanego wiersza", "ad": "- wartość do znalezienia w  pierwszym wierszu tabeli, mo<PERSON><PERSON> być wartości<PERSON>, odwołaniem lub ciągiem znaków!- tabela tekstowa albo tabela wartości liczbowych lub logicznych, w której będą wyszukiwane dane. Tabela_tablica może być odwołaniem do zakresu lub nazwą zakresu!- numer wiersza w tabeli_tablicy, z którego ma zostać zwrócona pasująca wartość. Pierwszym wierszem wartości w tabeli jest wiersz 1!- wartość logiczna: aby znaleźć najlepsze dopasowanie w górnym wierszu (posortowanym w kolejności rosnącej) = PRAWDA lub pominięte; aby znaleźć dokładne dopasowanie = FAŁSZ"}, "HYPERLINK": {"a": "(łącze_lokalizacja; [przy<PERSON>zna_nazwa])", "d": "Tworzy skrót lub skok, który otwiera dokument przechowywany na dysku twardym, serwerze sieciowym lub w Internecie", "ad": "- tekst określający ścieżkę i nazwę pliku dokumentu, kt<PERSON>ry ma by<PERSON>, nazwę dysku twardego, adres UNC, lub ścieżkę URL!- jest tekstem lub liczbą wyświetlaną w komórce. Je<PERSON><PERSON> zostanie pominięte, to w komórce będzie wyświetlany tekst Łącze_lokalizacja"}, "INDEX": {"a": "(tablica; nr_wiersza; [nr_kolumny]!odwołanie; nr_wiersza; [nr_kolumny]; [nr_obszaru])", "d": "Zwraca wartość lub odwołanie do komórki na przecięciu określonego wiersza i kolumny w danym zakresie", "ad": "- zakres komórek lub stała tablicowa.!- zaznacza wiersz w tablicy lub odwoła<PERSON>u, z którego ma zostać zwrócona wartość. W przypadku pominięcia wymagany jest argument nr_kolumny!- zaznacza kolumnę w tablicy lub odwołaniu, z której ma zostać zwrócona wartość. W przypadku pominięcia wymagany jest argument nr_wiersza!- odwołanie do jednego lub kilku zakresów komórek!- zaznacza wiersz w tablicy lub odwołaniu, z którego ma zostać zwrócona wartość. W przypadku pominięcia wymagany jest argument nr_kolumny!- zaznacza kolumnę w tablicy lub odwołaniu, z której ma zostać zwrócona wartość. W przypadku pominięcia wymagany jest argument nr_wiersza!- zaznacza zakres w odwołaniu, z którego ma zostać zwrócona wartość. Pierwszy zaznaczony lub wprowadzony obszar to obszar 1, drugi obszar to obszar 2 i tak dalej"}, "INDIRECT": {"a": "(adres_tekst; [a1])", "d": "Zwraca adres wskazany przez wartość tekstową", "ad": "- adres komórki zawierającej adres w trybie adresowania A1 lub W1K1 lub nazwa zdefiniowana jako adres lub odwołanie do komórki jako ciągu tekstowego!- <PERSON><PERSON><PERSON><PERSON>, która określa typ odwołania w argumencie tekst_odw: styl W1K1 = FAŁSZ; styl A1 = PRAWDA lub pominięta"}, "LOOKUP": {"a": "(szukana_warto<PERSON><PERSON>; prz<PERSON><PERSON><PERSON><PERSON>_wektor; [wektor_wyniko<PERSON>]!szukana_wartość; tablica)", "d": "Wyszuku<PERSON> wartość z zakresu jednowierszowego lub jednokolumnowego albo z tablicy. Zapewnia zgodność z poprzednimi wersjami", "ad": "- warto<PERSON>ć wyszukiwana przez funkcję WYSZUKAJ w argumencie przeszukiwany_wektor, może to być liczba, tekst, wartość logiczna albo nazwa lub odwołanie do wartości!- zak<PERSON> zawierający tylko jeden wiersz lub jedną kolumnę tekstu, liczb lub wartości logicznych, umieszczonych w kolejności rosnącej!- zakres zawierający tylko jeden wiersz lub kolumnę, o tym samym rozmiarze, co przeszukiwany_wektor!- wartość wyszukiwana przez funkcję WYSZUKAJ w tablicy, może to być liczba, tekst, wartość logiczna lub nazwa albo odwołanie do wartości!- zak<PERSON> kom<PERSON>rek zawierający tekst, liczby lub wartości logiczne, które mają być porównywane z argumentem szukana_warto<PERSON><PERSON>"}, "MATCH": {"a": "(s<PERSON><PERSON>_war<PERSON>; przes<PERSON>wana_tab; [typ_porównania])", "d": "Zwraca względną pozycję elementu w tablicy, odpowiadającą określonej wartości przy podanej kolejności", "ad": "- wartość używana do znalezienia żądanej wartości w tablicy, licz<PERSON>, tekst lub wartość logiczna albo odwołanie do jednej z takich wartości!- ciągły zakres komórek zawierający możliwe wartości wyszukiwania, tablica wartości lub odwołanie do tablicy!- liczba 1, 0 albo -1 wskazująca, która wartość ma być zwrócona."}, "OFFSET": {"a": "(od<PERSON><PERSON><PERSON>; wiersze; kolumny; [wys<PERSON><PERSON><PERSON>]; [szer<PERSON><PERSON><PERSON>])", "d": "Zwraca odwołanie do zakresu, który jest daną liczbą wierszy lub kolumn z danego odwołania", "ad": "- <PERSON><PERSON><PERSON><PERSON><PERSON>, od którego określane jest przes<PERSON>ęcie, odwołanie do komórki lub zakresu sąsiadujących komórek!- liczba wierszy w dół lub w gór<PERSON>, do których ma się odwoływać lewa górna komórka wyniku!- liczba kolumn w lewo lub w prawo, do których ma się odwoływać lewa górna komórka wyniku!- wys<PERSON><PERSON><PERSON> mierzona liczbą wierszy, którą ma mieć wynik, taka sama jak odwołanie, jeśli została pominięta!- szeroko<PERSON><PERSON> mierzona liczbą kolumn, kt<PERSON>r<PERSON> ma mieć wynik, taka sama jak odwołanie, jeśli została pominięta"}, "ROW": {"a": "([odwo<PERSON>nie])", "d": "Zwraca numer wiersza odpowiadający podanemu odwołaniu", "ad": "- komórka lub po<PERSON><PERSON><PERSON><PERSON> z<PERSON> komórek, k<PERSON><PERSON><PERSON><PERSON> numer wiersza zostanie wyznaczony; je<PERSON><PERSON>, zwraca komórkę zawierającą funkcję WIERSZ"}, "ROWS": {"a": "(tablica)", "d": "Zwraca liczbę wierszy odpowiadających podanemu odwołaniu lub tablicy", "ad": "- tablica, formuła zwracająca tablicę lub odwołanie do zakresu komórek, dla kt<PERSON><PERSON>ch zostanie wyznaczona liczba wierszy"}, "TRANSPOSE": {"a": "(tablica)", "d": "Konwertuje pionowy zakres komórek do zakresu poziomego lub na odwrót", "ad": "- zakres komórek w arkus<PERSON> lub tablica wartości, która ma być transponowana"}, "UNIQUE": {"a": "(Tablica; [by_col]; [exactly_once])", "d": " Zwraca wartości unikatowe z zakresu lub tablicy.", "ad": "zakres lub tablica, z której mają być zwracane unikatowe wiersze lub kolumny!wartość logiczna: porównaj wiersze względem siebie i zwróć unikatowe wiersze = FAŁSZ lub pominięte; porównaj kolumny względem siebie i zwróć unikatowe kolumny = PRAWDA!wartość logiczna: zwr<PERSON><PERSON> wiersze lub kolumny, które występują dokładnie raz z tablicy = PRAWDA; zwróć wszystkie unikatowe wiersze lub kolumny z tablicy = FAŁSZ lub pominięte"}, "VLOOKUP": {"a": "(s<PERSON><PERSON>_war<PERSON>; tabela_tablica; nr_indeksu_kolumny; [przes<PERSON><PERSON><PERSON>_zak<PERSON>])", "d": "Wyszu<PERSON><PERSON> wartość w pierwszej od lewej kolumnie tabeli i zwraca wartość z tego samego wiersza w kolumnie określonej przez użytkownika. Domyślnie tabela musi być sortowana w kolejności rosnącej", "ad": "- wartość do znalezienia w pierwszej kolumnie tabeli, mo<PERSON><PERSON> by<PERSON> warto<PERSON>, odwołaniem lub ciągiem tekstowym!- tabela teksto<PERSON>, liczbowa lub wartości logicznych, z której są pobierane dane. Argument tabela_tablica może być odwołaniem do zakresu lub nazwą zakresu!- numer kolumny w argumencie tabela_tablica, z której ma zostać pobrana zwracana pasująca wartość. Pierwsza kolumna wartości w tabeli to kolumna 1!- wartość logiczna: aby znaleźć najlepsze dopasowanie w pierwszej kolumnie (sortowanej w kolejności rosnącej) = PRAWDA lub pominięta; aby znaleźć dokładny odpowiednik = FAŁSZ"}, "XLOOKUP": {"a": "(szuka<PERSON>_war<PERSON><PERSON><PERSON>; szukana_tablica; zwracana_tablica; [je<PERSON><PERSON>_nie_znaleziono]; [tryb_dopasowywania]; [tryb_wyszukiwania])", "d": "Przeszukuje zakres lub tablicę pod kątem dopasowania i zwraca odpowiedni element z drugiego zakresu lub drugiej tablicy. Domyślnie jest używane dokładne dopasowanie", "ad": "— wartość do wyszukania!— tablica lub zakres wyszukiwania!— tablica lub zakres do zwrócenia!— z<PERSON><PERSON><PERSON>, jeśli nie znaleziono żadnego dopasowania!Określ sposób dopasowania elementu szukana_wartość do wartości w elemencie szukana_tablica!Określ tryb wyszukiwania do użycia. Domyślnie będzie używane wyszukiwanie od pierwszego do ostatniego elementu"}, "CELL": {"a": "(typ_info; [odwołanie])", "d": "Zwraca informacje o formatowaniu, położeniu lub zawartości komórki", "ad": "- jest to war<PERSON><PERSON><PERSON> te<PERSON>, określająca żądany typ informacji o komórce!- jest to kom<PERSON><PERSON>, o której chcesz uzyskać informacje"}, "ERROR.TYPE": {"a": "(bł<PERSON><PERSON>_war<PERSON><PERSON>)", "d": "Zwraca numer odpowiadający jednej z wartości błędu.", "ad": "- war<PERSON><PERSON><PERSON> błędu, którego numer identyfikacyjny chcesz uzyskać; może to być rzeczywista wartość błędu lub odwołanie do komórki zawierającej wartość błędu"}, "ISBLANK": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy odwołanie następuje do pustej komórki i zwraca wartość PRAWDA albo FAŁSZ", "ad": "- komórka lub nazwa odwołująca się do komórki, kt<PERSON><PERSON>ą ch<PERSON>z testować"}, "ISERR": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> to błąd inny niż #N/D, i zwraca wartość PRAWDA albo FAŁSZ", "ad": "- <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> ch<PERSON>z testować. <PERSON><PERSON><PERSON><PERSON> może odwoływać się do komórki, formuły lub nazwy, która odwołuje się do komórki, formuły lub warto<PERSON>ci"}, "ISERROR": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON>wdza, c<PERSON> warto<PERSON> to błąd, i zwraca wartość PRAWDA albo FAŁSZ", "ad": "- <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> ch<PERSON>z testować. <PERSON><PERSON><PERSON><PERSON> może odwoływać się do komórki, formuły lub nazwy, która odwołuje się do komórki, formuły lub warto<PERSON>ci"}, "ISEVEN": {"a": "(liczba)", "d": "Zw<PERSON><PERSON> PRAWDA, je<PERSON><PERSON> liczba jest parzysta", "ad": "<PERSON> test<PERSON><PERSON>"}, "ISFORMULA": {"a": "(odwołanie)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy odwołanie jest odwołaniem do komórki zawierającej formułę, i zwraca wartość PRAWDA albo FAŁSZ.", "ad": "- odwołanie do komórki, która ma być testowana. Odwołanie może być odwołaniem do komórki, formułą lub nazwą, która odwołuje się do komórki."}, "ISLOGICAL": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c<PERSON> warto<PERSON> jest wartością logiczną (PRAWDA albo FAŁSZ) i zwraca wartość PRAWDA albo FAŁSZ", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ra ma być testowana. <PERSON><PERSON><PERSON><PERSON> może się odwoływać do komórki, formuły lub nazwy, która odwołuje się do komórki, formuły lub warto<PERSON>ci"}, "ISNA": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> warto<PERSON> to #N/D i zwraca wartość PRAWDA albo FAŁSZ", "ad": "- <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> ch<PERSON>z testować. <PERSON><PERSON><PERSON><PERSON> może odwoływać się do komórki, formuły lub nazwy, która odwołuje się do komórki, formuły lub warto<PERSON>ci"}, "ISNONTEXT": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c<PERSON> warto<PERSON> nie jest tekstem (puste komórki nie są tekstem) i zwraca wartość PRAWDA albo FAŁSZ", "ad": "- <PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> ma być testowana: k<PERSON><PERSON><PERSON>, formu<PERSON>, lub nazwa odwołująca się do komórki, formuły lub warto<PERSON>ci"}, "ISNUMBER": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c<PERSON> warto<PERSON> to liczba i zwraca wartość PRAWDA albo FAŁSZ", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ra ma być testowana. <PERSON><PERSON><PERSON><PERSON> może odwoływać się do komórki, formuły lub nazwy, która odwołuje się do komórki, formuły lub warto<PERSON>ci"}, "ISODD": {"a": "(liczba)", "d": "Zw<PERSON><PERSON> PRAWDA, je<PERSON><PERSON> liczba jest nieparzysta", "ad": "<PERSON> test<PERSON><PERSON>"}, "ISREF": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c<PERSON> warto<PERSON> jest odwołaniem i zwraca wartość PRAWDA albo FAŁSZ", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ra ma być testowana. <PERSON><PERSON><PERSON><PERSON> może się odwoływać do komórki, formuły lub nazwy, która odwołuje się do komórki, formuły lub warto<PERSON>ci"}, "ISTEXT": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c<PERSON> warto<PERSON> to tekst i zwraca wartość PRAWDA albo FAŁSZ", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ra ma być testowana. <PERSON><PERSON><PERSON><PERSON> może odwoływać się do komórki, formuły lub nazwy, która odwołuje się do komórki, formuły lub warto<PERSON>ci"}, "N": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Konwertuje wartości nieliczbowe na liczby, daty na liczby kolejne, wartość PRAWDA na 1, wszystko inne na 0 (zero)", "ad": "- <PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> ma <PERSON><PERSON> konwer<PERSON>"}, "NA": {"a": "()", "d": "Zwraca wartość błędu #N/D (wartość niedostępna)", "ad": ""}, "SHEET": {"a": "([war<PERSON><PERSON><PERSON>])", "d": "Zwraca numer arkusza, którego dotyczy odwołanie.", "ad": "- nazwa arkusza lub o<PERSON>, dla którego ma zostać określony numer arkusza. <PERSON><PERSON><PERSON>, zwracany jest numer arkusza zawierającego funkcję."}, "SHEETS": {"a": "([odwo<PERSON>nie])", "d": "Zwraca liczbę arkuszy w odwołaniu.", "ad": "- odwołanie zawieraj<PERSON> a<PERSON>ze, dla którego ma zostać określona ich liczba. <PERSON><PERSON><PERSON>, zwracana jest liczba arkuszy skoroszytu zawierającego funk<PERSON>ję."}, "TYPE": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Zwraca liczbę całkowitą reprezentującą typ danych wartości: liczba = 1; tekst = 2; warto<PERSON><PERSON> logicz<PERSON> = 4; warto<PERSON><PERSON> błędu = 16; tablica = 64; dane <PERSON>ł<PERSON>ż<PERSON> = 128", "ad": "może mieć dowolną warto<PERSON>ć"}, "AND": {"a": "(logiczna1; [logiczna2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy wszystkie argumenty mają wartość PRAWDA, i zwraca wartość PRAWDA, jeśli wszystkie argumenty mają wartość PRAWDA", "ad": "- od 1 do 255 testowa<PERSON>ch warunków, które mogą mieć wartość PRAWDA albo FAŁSZ i są wartościami logicznymi, tablicami lub odwołaniami"}, "FALSE": {"a": "()", "d": "Zwraca wartość logiczną FAŁSZ", "ad": ""}, "IF": {"a": "(test_logiczny; [warto<PERSON><PERSON>_je<PERSON><PERSON>_prawda]; [warto<PERSON><PERSON>_je<PERSON><PERSON>_fałsz])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c<PERSON> warunek jest speł<PERSON>, i zwraca jedn<PERSON> war<PERSON>, jeśli PRAWDA, a <PERSON><PERSON> warto<PERSON>, jeśli FAŁSZ", "ad": "- dowo<PERSON>a wartoś<PERSON> lub w<PERSON>, kt<PERSON>re można <PERSON> jako wartości PRAWDA albo FAŁSZ!- warto<PERSON><PERSON> zwracana, gdy test_logiczny ma wartość PRAWDA. W przypadku pominięcia zwracana jest wartość PRAWDA. Można zagnieździć do siedmiu funkcji JEŻELI!- warto<PERSON>ć zwracana, gdy test_logiczny ma wartość FAŁSZ. W przypadku pominięcia zwracana jest wartość FAŁSZ"}, "IFS": {"a": "(test_logiczny; war<PERSON><PERSON><PERSON>_jeś<PERSON>_prawda; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy jest spełniony co najmniej jeden warunek, i zwraca wartość odpowiadającą pierwszemu spełnionemu warunkowi", "ad": "— dowolna wartość lub w<PERSON>, dla k<PERSON><PERSON><PERSON><PERSON> można wyznaczyć wartość PRAWDA lub FAŁSZ!— warto<PERSON><PERSON> zwracana, jeśli argument test_logiczny ma wartość PRAWDA"}, "IFERROR": {"a": "(warto<PERSON><PERSON>; warto<PERSON><PERSON>_je<PERSON><PERSON>_błąd)", "d": "<PERSON><PERSON><PERSON><PERSON> warto<PERSON> warto<PERSON>_je<PERSON><PERSON>_b<PERSON><PERSON><PERSON>, je<PERSON><PERSON> wyrażenie jest błędne, l<PERSON> warto<PERSON><PERSON> wyrażenia w przeciwnym razie", "ad": "- dowo<PERSON>a warto<PERSON>, wyrażenie lub odwołanie!- dowolna warto<PERSON>, wyrażenie lub odwołanie"}, "IFNA": {"a": "(warto<PERSON><PERSON>; warto<PERSON><PERSON>_je<PERSON>eli_nd)", "d": "Zwraca okreś<PERSON>, je<PERSON><PERSON> rozpoznawanie wyrażenia zakończy się błędem #N/D. W przeciwnym razie zostanie zwrócony wynik wyrażenia.", "ad": "— dowolna warto<PERSON>, wyrażenie lub odwołanie.!— dowolna warto<PERSON>, wyrażenie lub odwołanie."}, "NOT": {"a": "(logicz<PERSON>)", "d": "Zmienia wartość FAŁSZ na PRAWDA albo wartość PRAWDA na FAŁSZ", "ad": "- war<PERSON><PERSON><PERSON> lub w<PERSON>, które można oszacować jako PRAWDA albo FAŁSZ"}, "OR": {"a": "(logiczna1; [logiczna2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy którykolwiek z argumentów ma wartość PRAWDA i zwraca wartość PRAWDA albo FAŁSZ. Zwraca wartość FAŁSZ tylko wówczas, gdy wszystkie argumenty mają wartość FAŁSZ", "ad": "- od 1 do 255 testowanych warunków logicznych mogących mieć warto<PERSON> albo PRAWDA, albo FAŁSZ"}, "SWITCH": {"a": "(wyra<PERSON><PERSON><PERSON>; wartość1; wynik1; [domy<PERSON>lne_lub_wartość2]; [wynik2]; ...)", "d": "Ocenia wyrażenie względem listy wartości i zwraca wynik odpowiadający pierwszej pasującej wartości. W razie braku dopasowania zwracana jest opcjonalna wartość domyślna", "ad": "— wyrażenie do oceny!— wartość do porównania z wyrażeniem!— wynik do zwrócenia, jeśli odpowiadająca wartość spełnia wyrażenie"}, "TRUE": {"a": "()", "d": "Zwraca wartość logiczną PRAWDA", "ad": ""}, "XOR": {"a": "(logiczna1; [logiczna2]; ...)", "d": "Zwraca wartość logiczną XOR (wyłączne LUB) wszystkich argumentów.", "ad": "- od 1 do 254 testowa<PERSON>ch warunków, które mogą mieć wartość PRAWDA albo FAŁSZ, mogą by<PERSON> wartościami logicznymi, tablicami lub od<PERSON>ła<PERSON>."}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Zwraca tekst, który znajduje się przed znakami ograniczającymi.", "ad": "<PERSON><PERSON><PERSON>, kt<PERSON><PERSON> ch<PERSON>z wyszukać dla ogranicznika.!Znak lub ciąg, kt<PERSON>ry ma być używany jako ogranicznik.!Żądane wystąpienie ogranicznika. Wartoś<PERSON> domyślna to 1. Liczba ujemna spowoduje rozpoczęcie wyszukiwania od końca.!Przeszukuje tekst w celu dopasowania ogranicznika. Domyślnie jest wykonywane dopasowanie z uwzględnieniem wielkości liter.!Określa, czy dopasować ogranicznik do końca tekstu. Domyślnie nie są one dopasowane.!<PERSON><PERSON><PERSON><PERSON> zwracana, jeśli nie znaleziono dopasowania. Domyślnie zwracana jest wartość #N/A."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Zwraca tekst, który znajduje się po znakach ograniczających.", "ad": "<PERSON><PERSON><PERSON>, kt<PERSON><PERSON> ch<PERSON>z wyszukać dla ogranicznika.!Znak lub ciąg, kt<PERSON>ry ma być używany jako ogranicznik.!Żądane wystąpienie ogranicznika. Wartoś<PERSON> domyślna to 1. Liczba ujemna spowoduje rozpoczęcie wyszukiwania od końca.!Przeszukuje tekst w celu dopasowania ogranicznika. Domyślnie jest wykonywane dopasowanie z uwzględnieniem wielkości liter.!Ok<PERSON>śla, czy dopasować ogranicznik do końca tekstu. Domyślnie nie są one dopasowane.!<PERSON><PERSON><PERSON><PERSON> zwracana, jeśli nie znaleziono dopasowania. Domyślnie zwracana jest wartość #N/A"}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Dzieli tekst na wiersze lub kolumny przy użyciu ograniczników.", "ad": "Tekst do podzielenia!Znak lub ciąg dzielący kolumny.!Znak lub ciąg dzielący wiersze.!<PERSON><PERSON><PERSON><PERSON>, czy puste komórki są ignorowane. Wartoś<PERSON> domyślna to FAŁSZ.!Przeszukuje tekst w celu dopasowania ogranicznika. Domyślnie jest wykonywane dopasowanie z uwzględnieniem wielkości liter.!Wartość używana do dopełniania. Domyślnie jest to wartość #N/D."}, "WRAPROWS": {"a": "(wekt<PERSON>, wrap_count, [pad_with])", "d": " Zawija wektor wiersza lub kolumny po określonej liczbie wartości. Wektor lub odwołanie do zawijania.", "ad": " Maksymalna liczba wartości na wiersz.!<PERSON><PERSON><PERSON><PERSON>, za pomocą której ma zostać podane uzupełnienie.!Wartość domyślna to #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": " Układa tablice w pionie tworząc jedną tablicę.", "ad": " Tablica lub odwołanie do skumulowania."}, "HSTACK": {"a": "(tablica1, [tablica2], ...)", "d": " Układa tablice w poziomie w jedną tablicę.", "ad": " Tablica lub odwołanie do skumulowania."}, "CHOOSEROWS": {"a": "(tablica, row_num1, [row_num2], ...)", "d": " Zwraca wiersze z tablicy lub odwołania.", "ad": " Tablica lub odwołanie zawierające wiersze do zwrócenia.! Liczba wierszy do zwrócenia."}, "CHOOSECOLS": {"a": "(tablica, col_num1, [col_num2], ...)", "d": " Zwraca kolumny z tablicy lub odwołania.", "ad": " Tablica lub odwołanie zawierające kolumny do zwrócenia.! Liczba kolumn, która ma zostać zwrócona."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": " Zwraca tablicę jako jedną kolumnę.", "ad": " Tablica lub odwołanie do zwrócenia jako kolumna.! <PERSON><PERSON><PERSON><PERSON>, czy z<PERSON><PERSON> pewne typy wartości. Domyślnie żadne wartości nie są ignorowane.! Skanuj tablicę według kolumny. Domyślnie tablica jest skanowana według wiersza."}, "TOROW": {"a": "(tablica, [ignoruj], [skanuj_według_kolumn])", "d": "Zwraca tablicę jako jeden w<PERSON>z.", "ad": "Tablica lub odwołanie do zwrócenia jako wiersz.!<PERSON><PERSON><PERSON><PERSON>, c<PERSON> zign<PERSON> określone typy wartości. Domyślnie żadne wartości nie są ignorowane.!Skanuj tablicę według kolumn. Domyślnie tablica jest skanowana według wierszy."}, "WRAPCOLS": {"a": "(wekt<PERSON>, wrap_count, [pad_with])", "d": " Zawija wektor wiersza lub kolumny po określonej liczbie wartości.", "ad": "Wektor lub odwołanie do zawijania.!Maksymalna liczba wartości na kolumnę.!<PERSON><PERSON><PERSON><PERSON>, za pomocą której ma zostać podane uzupełnienie. Wartość domyślna to #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": " Zwraca wiersze lub kolumny z początku lub końca tablicy.", "ad": " Tablica, z której mają zostać pobrane wiersze lub kolumny.! Liczba wierszy do wykonania. Wartość ujemna pobiera z końca tablicy.! Liczba kolumn do wykonania. Wartość ujemna pobiera z końca tablicy."}, "DROP": {"a": "(array, rows, [columns])", "d": " Porzuca wiersze lub kolumny z początku lub końca tablicy.", "ad": " Tablica, z której mają zostać porzucone wiersze lub kolumny.! Liczba wierszy do porzucenia. Wartość ujemna porzuca z końca tablicy.! Liczba kolumn do porzucenia. Wartość ujemna porzuca z końca tablicy."}, "SEQUENCE": {"a": "(w<PERSON>ze, [kolumny], [początek], [krok])", "d": "Zwraca sekwen<PERSON> liczb", "ad": "Liczba wierszy do zwrócenia!Liczba kolumn do zwrócenia!Pierwsza liczba w sekwencji!Liczba, o którą ma być zwiększana każda kolejna wartość w sekwencji"}, "EXPAND": {"a": "(tablica, wiersze, [kolumny], [dopełnij])", "d": "Rozwija tablicę do określonych wymiarów.", "ad": " Tablica do rozwinięcia.! Liczba wierszy w rozwiniętej tablicy. Je<PERSON><PERSON> ich brakuje, wiersze nie zostaną rozwinięte.! Liczba kolumn w rozwiniętej tablicy. Je<PERSON><PERSON> ich brakuje, kolumny nie zostaną rozwinięte.! <PERSON><PERSON><PERSON><PERSON>, która ma zostać użyta do uzupełnienia. Wartość domyślna to #N/D."}, "XMATCH": {"a": "(szuka<PERSON>_<PERSON><PERSON><PERSON>, szukana_tablica, [tryb_dopasowywania], [tryb_wyszukiwania])", "d": "Zwraca względne położenie elementu w tablicy. Domyślnie jest wymagane dokładne dopasowanie", "ad": " - wartość do wyszukania! - tablica lub zakres do wyszukiwania!Określ sposób dopasowania elementu szukana_wartość do wartości w elemencie szukana_tablica!Określ tryb wyszukiwania do użycia. Domyślnie będzie używane wyszukiwanie od pierwszego do ostatniego elementu"}, "FILTER": {"a": "(tablica, uwzględnienie, [jeśli_puste])", "d": "Filtruj zakres lub tablicę", "ad": "— zakres lub tablica do przefiltrowania!— tablica wartości logicznych, gdzie wartość PRAWDA odzwierciedla wiersz lub kolumnę do zachowania!— wartość zwracana w przypadku braku zachowanych elementów"}, "ARRAYTOTEXT": {"a": "(tablica, [format])", "d": "Zwraca reprezentację tekstową tablicy", "ad": "— tab<PERSON>, która ma być reprezentowana jako tekst!— format tekstu"}, "SORT": {"a": "(tablica, [indek<PERSON>_sortowania], [k<PERSON><PERSON><PERSON><PERSON><PERSON>_sortowania], [według_kolumny])", "d": "Sort<PERSON>je zakres lub tablicę", "ad": "Zakres lub tablica do posortowania!Liczba wskazująca wiersz lub kolumnę, według których ma być sortowana zawartość!Liczba wskazująca odpowiednią kolejność sortowania; wartość 1 oznacza kolejność rosnącą (ustawienie domyślne), a wartość -1 oznacza kolejność malejącą!Wartość logiczna wskazująca odpowiedni kierunek sortowania: wartość FAŁSZ umożliwia sortowanie według wierszy (ustawienie domyślne), a wartość TRUE umożliwia sortowanie według kolumn"}, "SORTBY": {"a": "(tablica, według_tablicy, [k<PERSON><PERSON><PERSON><PERSON><PERSON>_sortowania], ...)", "d": "Sortuje zakres lub tablicę na podstawie wartości w odpowiednim zakresie lub odpowiedniej tablicy", "ad": "Zakres lub tablica do posortowania!Zakres lub tablica sortowania!Liczba określająca odpowiednią kolejność sortowania; 1 oznacza kolejność rosnącą (domyślnie), a -1 oznacza kolejność malejącą"}, "GETPIVOTDATA": {"a": "(pole_danych; tabela_przestawna; [pole]; [element]; ...)", "d": "Wyodrębnia dane przechowywane w tabeli przestawnej.", "ad": "- nazwa pola danych, z którego mają być wyodrębnione dane!- odwołanie do komórki lub zakresu komórek w tabeli przestawnej zawierające dane, które mają być pobrane!pole, do którego następuje odwołanie!element pola, do którego następuje odwołanie"}, "IMPORTRANGE": {"a": "(url_arkus<PERSON>; ciąg_zakresu)", "d": "Importuje zakres komórek z określonego arkusza kalkulacyjnego.", "ad": "— adres URL arkusza kalkulacyjnego, z którego będą importowane dane!— zakres do zaimportowania"}}