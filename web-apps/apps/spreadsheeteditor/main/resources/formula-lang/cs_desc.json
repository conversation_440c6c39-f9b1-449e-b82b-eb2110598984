{"DATE": {"a": "(year; month; day)", "d": "<PERSON>rá<PERSON><PERSON>, které představuje datum v kódu pro datum a čas.", "ad": "je číslo od 1900 nebo 1904 (v závislosti na kalendářním systému sešitu) do 9999.!je číslo od 1 do 12, které představuje měsíc v roce.!je číslo od 1 do 31, které představuje den v měsíci."}, "DATEDIF": {"a": "(po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_datum; konncové_datum; jed<PERSON><PERSON>)", "d": "Vypočítá počet dnů, m<PERSON><PERSON><PERSON><PERSON><PERSON> nebo roků mezi dvěma daty", "ad": "<PERSON><PERSON>, které představuje první nebo počáteční datum daného období!<PERSON><PERSON>, které představuje poslední nebo koncové datum období!Typ informací, které chcete vrátit"}, "DATEVALUE": {"a": "(datum)", "d": "Převede datum ve formátu textu na číslo, které představuje datum v kódu pro datum a čas.", "ad": "je text, který představuje datum ve formátu data aplikace Spreadsheet Editor v rozsahu od 1/1/1900 nebo 1/1/1904 (v závislosti na kalendářním systému sešitu) do 12/31/9999."}, "DAY": {"a": "(po<PERSON><PERSON><PERSON><PERSON>_číslo)", "d": "Vrátí den v měsíci, číslo od 1 do 31.", "ad": "je číslo v kódu aplikace Spreadsheet Editor pro datum a čas."}, "DAYS": {"a": "(konec; začátek)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> počet dní mezi dvěma daty.", "ad": "začátek a konec jsou dvě kaledářní data, mezi kterými chcete zjistit počet dní!začátek a konec jsou dvě kalendářní data, mezi kterými chcete zjistit počet dní"}, "DAYS360": {"a": "(start; konec; [metoda])", "d": "Vrátí počet dnů mezi dvěma daty na základě roku s 360 dny (d<PERSON><PERSON><PERSON> mě<PERSON><PERSON><PERSON> s 30 dny).", "ad": "Argumenty Start a Konec jsou data vymezující interval, jeho<PERSON> délku chcete určit.!Argumenty Start a Konec jsou data vymezující interval, jehož délku chcete určit.!je logická hodnota určující metodu výpočtu: US (NASD) = NEPRAVDA nebo bez zadání, evropská = PRAVDA."}, "EDATE": {"a": "(za<PERSON><PERSON><PERSON>k; měsíce)", "d": "Vrátí pořadové číslo data, což je určený počet měs<PERSON><PERSON>ů před nebo po počátečním datu.", "ad": "je poř<PERSON><PERSON><PERSON>, které představuje počáteční datum.!je počet měsíců před nebo po počátečním datu zadaném v argumentu Začátek."}, "EOMONTH": {"a": "(za<PERSON><PERSON><PERSON>k; měsíce)", "d": "Vrátí pořadové číslo posledního dne měsíce před nebo po určeném počtu mě<PERSON>.", "ad": "je poř<PERSON><PERSON><PERSON>, které představuje počáteční datum.!je počet měsíců před nebo po počátečním datu zadaném v argumentu Začátek."}, "HOUR": {"a": "(po<PERSON><PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON>rá<PERSON><PERSON> hodiny jako číslo od 0 (12:00 dop.) do 23 (11:00 odp.).", "ad": "je číslo v kódu aplikace Spreadsheet Editor pro datum a čas nebo text ve formátu času, například 16:48:00 nebo 4:48:00 odp."}, "ISOWEEKNUM": {"a": "(datum)", "d": "Vrátí číslo týdne ISO v roce pro dané datum.", "ad": "je kód pro datum a čas, který Spreadsheet Editor používá pro výpočet data a času."}, "MINUTE": {"a": "(po<PERSON><PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, číslo od 0 do 59.", "ad": "je číslo v kódu aplikace Spreadsheet Editor pro datum a čas nebo text ve formátu času, například 16:48:00 nebo 4:48:00 odp."}, "MONTH": {"a": "(po<PERSON><PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, číslo od 1 (leden) do 12 (prosinec).", "ad": "je číslo v kódu aplikace Spreadsheet Editor pro datum a čas."}, "NETWORKDAYS": {"a": "(začátek; konec; [svátky])", "d": "Vrátí počet celých pracovních dnů mezi dvěma zadanými daty.", "ad": "je pořado<PERSON><PERSON>, které představuje počáteční datum.!je pořadové č<PERSON>, které představuje koncové datum.!je volitelná množina jednoho nebo více pořadov<PERSON>ch čísel dat, kter<PERSON> chcete vyloučit z kalendáře pracovních dnů, např<PERSON><PERSON> státní nebo pohyblivé svátky."}, "NETWORKDAYS.INTL": {"a": "(začátek; konec; [vík<PERSON>]; [svátky])", "d": "Vrátí počet celých pracovních dní mezi dvěma daty s vlastními parametry víkendu.", "ad": "je pořado<PERSON><PERSON>, které představuje počáteční datum.!je pořadové číslo představující koncové datum.!je číslo nebo řetězec určuj<PERSON>cí, které dny jsou považovány za víkendové.!je volitelná sada jednoho nebo více pořadových čísel dat, kter<PERSON> chcete vyloučit z kalendáře pracovních dnů, například státní nebo pohyblivé svátky."}, "NOW": {"a": "()", "d": "Vrátí aktuální datum a čas formátované jako datum a čas.", "ad": ""}, "SECOND": {"a": "(po<PERSON><PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, číslo od 0 do 59.", "ad": "je číslo v kódu aplikace Spreadsheet Editor pro datum a čas nebo text ve formátu času, například 16:48:23 nebo 4:48:47 odp."}, "TIME": {"a": "(hodina; minuta; sekunda)", "d": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>, minuty a sekundy zadané jako čísla na pořadové číslo formátované pomocí formátu času.", "ad": "je č<PERSON>lo od 0 do 23, kter<PERSON> představuje hodiny.!je číslo od 0 do 59, které představuje minuty.!je č<PERSON>lo od 0 do 59, kter<PERSON> představuje sekundy."}, "TIMEVALUE": {"a": "(čas)", "d": "Převede čas ve formě textového řetězce na pořadové číslo vyjadřují<PERSON><PERSON> č<PERSON>, číslo od 0 (12:00:00 dop.) do 0,999988426 (11:59:59 odp.). Po zadání vzorce číslo zformátujte pomocí formátu času.", "ad": "je textový řetězec, který představuje čas v libovolném formátu času aplikace Spreadsheet Editor (informace o datech v řetězci jsou přeskočeny)."}, "TODAY": {"a": "()", "d": "Vrátí aktuální datum formátované jako datum.", "ad": ""}, "WEEKDAY": {"a": "(po<PERSON><PERSON><PERSON><PERSON>; [typ])", "d": "Vrátí číslo od 1 do 7 určující den v týdnu kalendářního data.", "ad": "je <PERSON><PERSON><PERSON>, kter<PERSON> představuje datum!je číslo: pro týden od neděle = 1 do soboty = 7 použijte číslo 1, pro týden od pondělka = 1 do neděle = 7 použijte číslo 2, pro týden od pondělka = 0 do neděle = 6 použijte číslo 3"}, "WEEKNUM": {"a": "(po<PERSON><PERSON>_číslo; [typ])", "d": "Vrátí číslo týdne v roce.", "ad": "je kód aplikace Spreadsheet Editor pro datum a čas používaný k výpočtu data a času.!je číslo (1 nebo 2) určující typ návratové hodnoty."}, "WORKDAY": {"a": "(za<PERSON><PERSON><PERSON>k; dny; [svátky])", "d": "Vrátí pořadové číslo data před nebo po zadaném počtu pracovních dnů.", "ad": "je poř<PERSON><PERSON><PERSON>, kter<PERSON> představuje počáteční datum.!je počet pracovních dnů před nebo po datu zadaném v argumentu Začátek.!je volitelná řada jednoho nebo více pořadových čísel dat, kter<PERSON> chcete vyloučit z kalendáře pracovních dnů, např<PERSON><PERSON> státní nebo pohyblivé svátky."}, "WORKDAY.INTL": {"a": "(za<PERSON><PERSON><PERSON>k; dny; [vík<PERSON>]; [svátky])", "d": "Vrátí pořadové číslo data před nebo po zadaném počtu pracovních dnů s vlastními parametry víkendu.", "ad": "je pořado<PERSON><PERSON>, které představuje počáteční datum.!je počet dnů kromě víkendu a svátků před nebo po datu zadaném v argumentu začátek.!je číslo nebo řetězec určující víkendy!je volitelná matice jednoho nebo více pořadových čísel dat, kter<PERSON> chcete vyloučit z kalendáře pracovních dnů, například státní nebo pohyblivé svátky."}, "YEAR": {"a": "(po<PERSON><PERSON><PERSON><PERSON>_číslo)", "d": "Vrátí rok kalendářního data, celé číslo v rozsahu od 1900 do 9999.", "ad": "je číslo v kódu aplikace Spreadsheet Editor pro datum a čas."}, "YEARFRAC": {"a": "(za<PERSON><PERSON>tek; konec; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí část roku vyjádřenou zlomkem a představující počet celých dnů mezi počátečním a koncovým datem.", "ad": "je pořado<PERSON><PERSON>, které představuje počáteční datum.!je pořadové č<PERSON>lo, které představuje koncové datum.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "BESSELI": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> mod<PERSON><PERSON><PERSON> In(x).", "ad": "je hodn<PERSON>, pro kterou chcete funkci vyhodnotit.!je ř<PERSON>d <PERSON>ovy funkce."}, "BESSELJ": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> funkci Jn(x).", "ad": "je hodn<PERSON>, pro kterou chcete funkci vyhodnotit.!je ř<PERSON>d <PERSON>ovy funkce."}, "BESSELK": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> mod<PERSON><PERSON><PERSON> funkci Kn(x).", "ad": "je hodnota, pro kterou chcete funkci vyhodnotit.!je ř<PERSON>d funkce."}, "BESSELY": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> Y<PERSON>(x).", "ad": "je hodnota, pro kterou chcete funkci vyhodnotit.!je ř<PERSON>d funkce."}, "BIN2DEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Převede binární číslo na dekadické.", "ad": "je <PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>."}, "BIN2HEX": {"a": "(č<PERSON>lo; [místa])", "d": "Převede binární číslo na hexadecimální.", "ad": "je <PERSON><PERSON><PERSON><PERSON>, které chcete převést.!je počet znak<PERSON>, které chcete použít u výsledku."}, "BIN2OCT": {"a": "(č<PERSON>lo; [místa])", "d": "Převede binární číslo na osmičkové.", "ad": "je <PERSON><PERSON><PERSON><PERSON>, které chcete převést.!je počet znak<PERSON>, které chcete použít u výsledku."}, "BITAND": {"a": "(číslo1; číslo2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> „A“ dvou čísel", "ad": "je desítkové vyjád<PERSON><PERSON><PERSON> biná<PERSON><PERSON><PERSON>, které chcete vyhodnotit!je desítkové vyjádření binárn<PERSON><PERSON>, které chcete vyhodnotit"}, "BITLSHIFT": {"a": "(č<PERSON>lo; velikost_posunu)", "d": "Vrátí číslo posunuté doleva o hodnotu velikost_posunu v bitech", "ad": "je desítkové vyjá<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, které chcete vyhodnotit!je počet bitů, o které chcete číslo posunout doleva"}, "BITOR": {"a": "(číslo1; číslo2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> „nebo“ ze dvou čísel", "ad": "je desítkové vyjád<PERSON><PERSON><PERSON> biná<PERSON><PERSON><PERSON>, které chcete vyhodnotit!je desítkové vyjádření binárn<PERSON><PERSON>, které chcete vyhodnotit"}, "BITRSHIFT": {"a": "(č<PERSON>lo; velikost_posunu)", "d": "Vrátí číslo posunuté doprava o hodnotu velikost_posunu v bitech", "ad": "je desítkové vyjá<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, které chcete vyhodnotit!je počet bitů, o které chcete číslo posunout doprava"}, "BITXOR": {"a": "(číslo1; číslo2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> „výhradní nebo“ ze dvou čísel", "ad": "je desítkové vyjád<PERSON><PERSON><PERSON> biná<PERSON><PERSON><PERSON>, které chcete vyhodnotit!je desítkové vyjádření binárn<PERSON><PERSON>, které chcete vyhodnotit"}, "COMPLEX": {"a": "(re<PERSON>l; imag; [p<PERSON><PERSON><PERSON><PERSON>])", "d": "Převede reálnou a imaginární část na komplexní číslo", "ad": "je reálná část komplexního čísla.!je imaginární část komplexního čísla.!je označení imaginární části komplexního čísla."}, "CONVERT": {"a": "(číslo; z; do)", "d": "Převede číslo do jiného jed<PERSON>kového měrného systému.", "ad": "je hodnota v jednotkách argumentu z, kterou chcete převést.!jsou jednotky čísla.!jsou jednotky výsledku."}, "DEC2BIN": {"a": "(č<PERSON>lo; [místa])", "d": "Převede dekadické číslo na binární.", "ad": "je deka<PERSON>é cel<PERSON>, které chcete převést.!je počet znak<PERSON>, které chcete použít u výsledku."}, "DEC2HEX": {"a": "(č<PERSON>lo; [místa])", "d": "Převede dekadické číslo na hexadecimální.", "ad": "je deka<PERSON>é cel<PERSON>, které chcete převést.!je počet znak<PERSON>, které chcete použít u výsledku."}, "DEC2OCT": {"a": "(č<PERSON>lo; [místa])", "d": "Převede dekadické číslo na osmičkové.", "ad": "je deka<PERSON>é cel<PERSON>, které chcete převést.!je počet znak<PERSON>, které chcete použít u výsledku."}, "DELTA": {"a": "(číslo1; [číslo2])", "d": "Testuje rovnost dvou čísel.", "ad": "je první číslo.!je druhé č<PERSON>lo."}, "ERF": {"a": "(dolní_limit; [horní_limit])", "d": "Vrátí chybovou funkci.", "ad": "je dolní mez pro integraci funkce ERF.!je horní mez pro integraci funkce ERF."}, "ERF.PRECISE": {"a": "(x)", "d": "Vrátí chybovou funkci.", "ad": "je dolní mez pro integraci funkce ERF.PRECISE"}, "ERFC": {"a": "(x)", "d": "Vrátí doplňkovou chybovou funkci.", "ad": "je dolní mez pro integraci funkce ERF."}, "ERFC.PRECISE": {"a": "(x)", "d": "Vrátí doplňkovou chybovou funkci.", "ad": "je dolní mez pro integraci funkce ERFC.PRECISE"}, "GESTEP": {"a": "(<PERSON><PERSON><PERSON>; [co])", "d": "<PERSON><PERSON><PERSON>, zda je číslo větší než mezní hodnota.", "ad": "je testovaná hodnota.!je mezní hodnota."}, "HEX2BIN": {"a": "(č<PERSON>lo; [místa])", "d": "Převede hexadecimální číslo na binární.", "ad": "je hexadecim<PERSON><PERSON><PERSON>, které chcete převést.!je počet znak<PERSON>, které chcete použít u výsledku."}, "HEX2DEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Převede hexadecimální číslo na dekadické.", "ad": "je hexadecim<PERSON><PERSON><PERSON>, k<PERSON><PERSON>."}, "HEX2OCT": {"a": "(č<PERSON>lo; [místa])", "d": "Převede hexadecimální číslo na osmičkové.", "ad": "je hexadecim<PERSON><PERSON><PERSON>, které chcete převést.!je počet znak<PERSON>, které chcete použít u výsledku."}, "IMABS": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí absolutní hodnotu komplexního č<PERSON>la.", "ad": "je kom<PERSON><PERSON><PERSON>, jeh<PERSON><PERSON> absolutní hodnotu ch<PERSON>te zji<PERSON>it."}, "IMAGINARY": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí imaginární část komplexního čísla.", "ad": "je kom<PERSON><PERSON><PERSON>, jeh<PERSON><PERSON> imaginární č<PERSON>t chcete nalézt."}, "IMARGUMENT": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí argument q (úhel v radiánech).", "ad": "je kom<PERSON><PERSON><PERSON>, pro které chcete nalézt argument."}, "IMCONJUGATE": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí komplexně sdružené číslo ke komplexnímu č<PERSON>lu.", "ad": "je komplex<PERSON><PERSON>, ke kterému chcete nalézt komplexně sdru<PERSON>en<PERSON>."}, "IMCOS": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí kosinus komplexního <PERSON>.", "ad": "je kom<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> kos<PERSON> ch<PERSON>te nalézt."}, "IMCOSH": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický kosinus komplexního <PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> hyperbolic<PERSON>ý kosinus ch<PERSON>te z<PERSON>"}, "IMCOT": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí kotangens komplexního čísla", "ad": "je kom<PERSON><PERSON><PERSON>, pro které chcete získat kotangens"}, "IMCSC": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí kosekans komplexního č<PERSON>la", "ad": "je kom<PERSON><PERSON><PERSON>, pro které chcete zís<PERSON> kosekans"}, "IMCSCH": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický kosekans komplexního <PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, pro které chcete získat hyperbolický kosekans"}, "IMDIV": {"a": "(ičíslo1; ičíslo2)", "d": "Vrátí podíl dvou komplexních č<PERSON>el.", "ad": "je komplexní čitatel nebo dělenec.!je komplexní jmenovatel nebo dělitel."}, "IMEXP": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí exponenciální tvar komplexního č<PERSON>.", "ad": "je kom<PERSON><PERSON><PERSON>, jeh<PERSON><PERSON> exponenciální tvar chcete nalézt."}, "IMLN": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí přirozený logaritmus komplexního č<PERSON>la.", "ad": "je kom<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> přirozený logaritmus chcete nalézt."}, "IMLOG10": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí de<PERSON>dický logaritmus komplexního č<PERSON>la.", "ad": "je kom<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> logaritmus chcete nalézt."}, "IMLOG2": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí logaritmus komplexního čísla při základu 2.", "ad": "je kom<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> logaritmus při základu 2 chcete nalézt."}, "IMPOWER": {"a": "(i<PERSON><PERSON><PERSON>; číslo)", "d": "Vrátí komplexní číslo umocněné na celé číslo.", "ad": "je komplexn<PERSON>lo, k<PERSON><PERSON> chcete umocnit.!je mocnina, na kterou chcete komplexní číslo umocnit."}, "IMPRODUCT": {"a": "(ič<PERSON>lo1; [ič<PERSON>lo2]; ...)", "d": "Vrátí součin 1 až 255 komplexních čísel.", "ad": "Hodnoty ičíslo1, ič<PERSON>lo2,... představují 1 až 255 komplexních č<PERSON>el, k<PERSON><PERSON> chcete násobit."}, "IMREAL": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí reálnou část komplexního čísla.", "ad": "je kom<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> chcete nalézt."}, "IMSEC": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí sekans komplexního <PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, pro které chcete získat sekans"}, "IMSECH": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický sekans komplexního <PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, pro které chcete získat hyperbolický sekans"}, "IMSIN": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí sinus komplexního č<PERSON>la.", "ad": "je kom<PERSON><PERSON><PERSON>, jeho<PERSON> sinus chcete nalézt."}, "IMSINH": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický sinus komplexního č<PERSON>la", "ad": "je kom<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> hyperbolický sinus chcete získat"}, "IMSQRT": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí d<PERSON>hou od<PERSON>cninu komplexního <PERSON>.", "ad": "je kom<PERSON><PERSON><PERSON>, jeh<PERSON><PERSON> d<PERSON><PERSON> od<PERSON>u chcete nalézt."}, "IMSUB": {"a": "(ičíslo1; ičíslo2)", "d": "Vrátí rozdíl dvou komplexních <PERSON>.", "ad": "je komplex<PERSON><PERSON>, od kter<PERSON>ho chcete odečíst argument ičíslo2.!je komplexní č<PERSON>lo, které chcete odečíst od argumentu ičíslo1."}, "IMSUM": {"a": "(ič<PERSON>lo1; [ič<PERSON>lo2]; ...)", "d": "Vrátí součet komplexních čísel.", "ad": "představují 1 až 255 komplexních čísel, k<PERSON><PERSON> chcete sečíst."}, "IMTAN": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí tangens komplexního čísla", "ad": "je kom<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> tangens chcete získat"}, "OCT2BIN": {"a": "(č<PERSON>lo; [místa])", "d": "Převede osmičkové číslo na binární.", "ad": "je osmi<PERSON><PERSON><PERSON>, které chcete převést.!je počet znak<PERSON>, které chcete použít u výsledku."}, "OCT2DEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Převede osmičkové číslo na dekadické.", "ad": "je o<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>."}, "OCT2HEX": {"a": "(č<PERSON>lo; [místa])", "d": "Převede osmičkové číslo na hexadecimální.", "ad": "je osmi<PERSON><PERSON><PERSON>, které chcete převést.!je počet znak<PERSON>, které chcete použít u výsledku."}, "DAVERAGE": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí pr<PERSON><PERSON><PERSON><PERSON> hodnot ve sloupci seznamu nebo <PERSON>, kter<PERSON> spl<PERSON><PERSON><PERSON><PERSON> zadaná kritéria.", "ad": "je oblast buněk, která tvoří seznam nebo databázi. Databáze je seznam vzájemně propojených dat.!je popisek sloupce v uvozovkách nebo číslo, které představuje umístění sloupce v seznamu.!je oblast buněk, která obsahuje zadaná kritéria. Oblast zahrnuje popisek sloupce a jednu buňku pod popiskem pro kritérium."}, "DCOUNT": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí počet buněk obsahujících čísla v poli (sloupci) záznamů databáze, které splňují zadaná kritéria.", "ad": "je oblast buněk, která tvoří seznam nebo databázi. Databáze je seznam vzájemně propojených dat.!je popisek sloupce v uvozovkách nebo číslo, které představuje umístění sloupce v seznamu.!je oblast buněk, která obsahuje zadaná kritéria. Oblast zahrnuje popisek sloupce a jednu buňku pod popiskem pro kritérium."}, "DCOUNTA": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí počet neprázdných buněk v poli (sloupci) záznamů databáze, které splňují zadaná kritéria.", "ad": "je oblast buněk, která tvoří seznam nebo databázi. Databáze je seznam vzájemně propojených dat.!je popisek sloupce v uvozovkách nebo číslo, které představuje umístění sloupce v seznamu.!je oblast buněk, která obsahuje zadaná kritéria. Oblast zahrnuje popisek sloupce a jednu buňku pod popiskem pro kritérium."}, "DGET": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vybere z databáze jeden z<PERSON>nam, který splňuje zadaná kritéria.", "ad": "je oblast buněk, která tvoří seznam nebo databázi. Databáze je seznam vzájemně propojených dat.!je popisek sloupce v uvozovkách nebo číslo, které představuje umístění sloupce v seznamu.!je oblast buněk, která obsahuje zadaná kritéria. Oblast zahrnuje popisek sloupce a jednu buňku pod popiskem pro kritérium."}, "DMAX": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí maximální hodnotu v poli (sloupci) záznamů databáze, která splňuje zadaná kritéria.", "ad": "je oblast buněk, která tvoří seznam nebo databázi. Databáze je seznam vzájemně propojených dat.!je popisek sloupce v uvozovkách nebo číslo, které představuje umístění sloupce v seznamu.!je oblast buněk, která obsahuje zadaná kritéria. Oblast zahrnuje popisek sloupce a jednu buňku pod popiskem pro kritérium."}, "DMIN": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí minimální hodnotu v poli (sloupci) záznamů databáze, která splňuje zadaná kritéria.", "ad": "je oblast buněk, která tvoří seznam nebo databázi. Databáze je seznam vzájemně propojených dat.!je popisek sloupce v uvozovkách nebo číslo, které představuje umístění sloupce v seznamu.!je oblast buněk, která obsahuje zadaná kritéria. Oblast zahrnuje popisek sloupce a jednu buňku pod popiskem pro kritérium."}, "DPRODUCT": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vynásobí hodnoty v poli (sloupci) záznamů databáze, které splňují zadaná kritéria.", "ad": "je oblast buněk, která tvoří seznam nebo databázi. Databáze je seznam vzájemně propojených dat.!je popisek sloupce v uvozovkách nebo číslo, které představuje umístění sloupce v seznamu.!je oblast buněk, která obsahuje zadaná kritéria. Oblast zahrnuje popisek sloupce a jednu buňku pod popiskem pro kritérium."}, "DSTDEV": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Odhadne směrodatnou odchylku výběru vybraných položek databáze.", "ad": "je oblast buněk, která tvoří seznam nebo databázi. Databáze je seznam vzájemně propojených dat.!je popisek sloupce v uvozovkách nebo číslo, které představuje umístění sloupce v seznamu.!je oblast buněk, která obsahuje zadaná kritéria. Oblast zahrnuje popisek sloupce a jednu buňku pod popiskem pro kritérium."}, "DSTDEVP": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí směrodatnou odchylku základního souboru vybraných položek databáze.", "ad": "je oblast buněk, která tvoří seznam nebo databázi. Databáze je seznam vzájemně propojených dat.!je popisek sloupce v uvozovkách nebo číslo, které představuje umístění sloupce v seznamu.!je oblast buněk, která obsahuje zadaná kritéria. Oblast zahrnuje popisek sloupce a jednu buňku pod popiskem pro kritérium."}, "DSUM": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Sečte čísla v poli (sloupci) záznamů databáze, které splňují zadaná kritéria.", "ad": "je oblast buněk, která tvoří seznam nebo databázi. Databáze je seznam vzájemně propojených dat.!je popisek sloupce v uvozovkách nebo číslo, které představuje umístění sloupce v seznamu.!je oblast buněk, která obsahuje zadaná kritéria. Oblast zahrnuje popisek sloupce a jednu buňku pod popiskem pro kritérium."}, "DVAR": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Odhadne rozptyl výběru vybraných položek databáze.", "ad": "je oblast buněk, která tvoří seznam nebo databázi. Databáze je seznam vzájemně propojených dat.!je popisek sloupce v uvozovkách nebo číslo, které představuje umístění sloupce v seznamu.!je oblast buněk, která obsahuje zadaná kritéria. Oblast zahrnuje popisek sloupce a jednu buňku pod popiskem pro kritérium."}, "DVARP": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí rozptyl základního souboru vybraných položek databáze.", "ad": "je oblast buněk, která tvoří seznam nebo databázi. Databáze je seznam vzájemně propojených dat.!je popisek sloupce v uvozovkách nebo číslo, které představuje umístění sloupce v seznamu.!je oblast buněk, která obsahuje zadaná kritéria. Oblast zahrnuje popisek sloupce a jednu buňku pod popiskem pro kritérium."}, "CHAR": {"a": "(kód)", "d": "Vrátí znak určený číslem kódu ze znakové sady definované v používaném počítači.", "ad": "je číslo od 1 do 255, k<PERSON><PERSON> ur<PERSON> p<PERSON>žadovaný znak."}, "CLEAN": {"a": "(text)", "d": "Odstraní z textu všechny netisknutelné znaky.", "ad": "je libovolná informace na listu, ze které chcete odstranit netisknutelné znaky."}, "CODE": {"a": "(text)", "d": "Vrátí číselný kód prvního znaku textového řetězce ze znakové sady definované v používaném počítači.", "ad": "je text<PERSON><PERSON>, pro který chcete najít kód prvního znaku."}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Sloučí několik textových řetězců do jednoho.", "ad": "je 1 až 255 textových řetězců, k<PERSON><PERSON> chcete sloučit do jediného textového řetězce. Mohou to být textové řetězce, čísla nebo odkazy na jedinou buňku."}, "CONCAT": {"a": "(text1; ...)", "d": "Zřetězí seznam nebo oblast textových řetězců.", "ad": "je 1 až 254 textových řetězců nebo oblastí, kter<PERSON> chcete sloučit do jednoho textového řetězce."}, "DOLLAR": {"a": "(č<PERSON>lo; [desetiny])", "d": "Převede číslo na text ve formátu měny.", "ad": "je <PERSON><PERSON><PERSON>, od<PERSON><PERSON> na buňku s číslem nebo vzorec, jeh<PERSON><PERSON> výsledkem je číslo.!je počet desetinných míst vpravo od desetinné čárky. <PERSON><PERSON>lo je podle potřeby zaokrouhleno. Jest<PERSON>že argument Desetiny nezadáte, bude jeho hodnota 2."}, "EXACT": {"a": "(text1; text2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda jsou dva textové řetězce stejné a vrátí hodnotu PRAVDA nebo NEPRAVDA. Tato funkce rozlišuje malá a velká písmena.", "ad": "je první textový řetězec.!je druhý textový řetězec."}, "FIND": {"a": "(co; kde; [start])", "d": "Vrátí počáteční pozici jednoho textového řetězce v jiném textovém řetězci. Tato funkce rozlišuje malá a velká písmena.", "ad": "je text, který chcete nalézt. Použ<PERSON><PERSON><PERSON> uvozovky (prázdný text) k vyhledání prvního odpovídajícího znaku argumentu Kde. Zástupné znaky nejsou povoleny.!je text obsahující hledaný text.!ur<PERSON><PERSON><PERSON> znak, ve kterém začne vyhledávání. První znak argumentu Kde je znak číslo 1. Je<PERSON><PERSON>že argument Start nezadáte, bude jeho hodnota 1. "}, "FINDB": {"a": "(co; kde; [start])", "d": "Vyhledají jeden textový řetězec v druhém textovém řetězci a vrátí číslo počáteční pozice prvního textového řetězce od prvního znaku druhého textového řetězce, je určena pro jazyky používající dvoubaj<PERSON> znakov<PERSON> sadu (DBCS) - japonština, čínština a korejština", "ad": "je text, který chcete nalézt. Použ<PERSON><PERSON><PERSON> uvozovky (prázdný text) k vyhledání prvního odpovídajícího znaku argumentu Kde. Zástupné znaky nejsou povoleny.!je text obsahující hledaný text.!ur<PERSON><PERSON><PERSON> znak, ve kterém začne vyhledávání. První znak argumentu Kde je znak číslo 1. Je<PERSON><PERSON>že argument Start nezadáte, bude jeho hodnota 1. "}, "FIXED": {"a": "(č<PERSON>lo; [desetiny]; [bez_č<PERSON>rky])", "d": "Zaokrouhlí číslo na zadaný počet desetinných míst a vrátí výsledek ve formátu textu s čárkami nebo bez čárek.", "ad": "je <PERSON><PERSON><PERSON>, k<PERSON><PERSON> chcete zaokrouhlit a převést na text.!je počet desetinných míst vpravo od desetinné čárky. Jestliže argument Desetiny nezadáte, bude jeho hodnota 2.!je logická hodnota: nezobrazovat čárky ve vráceném textu = PRAVDA, zobrazit čárky ve vráceném textu = NEPRAVDA nebo bez zadání."}, "LEFT": {"a": "(text; [znaky])", "d": "Vrátí zadaný počet znaků od počátku textového řetězce.", "ad": "je textový řetězec obsahu<PERSON><PERSON><PERSON><PERSON>, kter<PERSON> chcete extrahovat.!určuje počet znaků, které budou pomocí funkce ZLEVA extrahovány. Jestliže tento argument nezadá<PERSON>, bude jeho hodnota 1."}, "LEFTB": {"a": "(text; [znaky])", "d": "Vrátí první znak nebo znaky v textovém řetězci na základě zadaného počtu bajtů, je určena pro jazyky používající dvoubajtov<PERSON> sadu (DBCS) - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>š<PERSON> a korejština", "ad": "je textový řetězec obsahu<PERSON><PERSON><PERSON><PERSON>, kter<PERSON> chcete extrahovat.!určuje počet znaků, které budou pomocí funkce ZLEVA extrahovány. Jestliže tento argument nezadá<PERSON>, bude jeho hodnota 1."}, "LEN": {"a": "(text)", "d": "Vrátí počet znaků textového řetězce.", "ad": "je text, jeh<PERSON><PERSON> chcete zjistit. Mezery jsou počítány jako znaky."}, "LENB": {"a": "(text)", "d": "Vrátí po<PERSON>et baj<PERSON>, k<PERSON><PERSON> představují znaky v textovém řetězci, je určena pro jazyky používající dvoubajtovou znakov<PERSON> sadu (DBCS) - <PERSON><PERSON><PERSON><PERSON>, čínština a korejština", "ad": "je text, jeh<PERSON><PERSON> chcete zjistit. Mezery jsou počítány jako znaky."}, "LOWER": {"a": "(text)", "d": "Převede všechna písmena textového řetězce na malá.", "ad": "je text, který chcete převést na malá písmena. Znaky v argumentu Text, které nejsou písmena, se nezmění."}, "MID": {"a": "(text; start; počet_znaků)", "d": "Vrátí znaky z textového řetězce, je-li zadána počáteční pozice a počet znaků.", "ad": "je textový řetězec, ze kterého chcete extrahovat požadované znaky.!je pozice prvního znaku, který chcete extrahovat. První znak argumentu Text je číslo 1.!určí počet znaků argumentu Text, kter<PERSON> mají být extrahovány."}, "MIDB": {"a": "(text; start; počet_znaků)", "d": "Vrátí určitý počet znaků (na základě zadaného počtu bajtů) z textového řetězce od zadané pozice, je určena pro jazyky používající dvoubajtov<PERSON> zna<PERSON> sadu (DBCS) - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>š<PERSON> a korejština", "ad": "je textový řetězec, ze kterého chcete extrahovat požadované znaky.!je pozice prvního znaku, který chcete extrahovat. První znak argumentu Text je číslo 1.!určí počet znaků argumentu Text, kter<PERSON> mají být extrahovány."}, "NUMBERVALUE": {"a": "(text; [odd<PERSON><PERSON><PERSON><PERSON>_desetin]; [odd<PERSON><PERSON><PERSON><PERSON>_skupin])", "d": "Převede text na číslo nezávisle na prostředí", "ad": "je řet<PERSON><PERSON><PERSON> představující <PERSON>, kter<PERSON> chcete převést!je znak použitý jako oddělovač desetinných míst v řetězci!je znak použitý jako oddělovač skupin v řetězci"}, "PROPER": {"a": "(text)", "d": "Převede textový řetězec na formát, kdy jsou první písmena všech slov velká a ostatní písmena malá.", "ad": "je text v uvozovkách, vzorec, jeh<PERSON><PERSON> výsledkem je text, nebo odkaz na buňku s textem, ve kterém chcete převést první písmena slov na velká."}, "REPLACE": {"a": "(starý; start; znaky; nový)", "d": "Nahradí č<PERSON>t textového řetězce jiným textovým řetězcem.", "ad": "je text, ve kterém chcete zaměnit některé znaky.!je pozice znaku v textu argumentu Starý, který chcete nahradit textem argumentu Nový.!je počet znaků textu argumentu Starý, které chcete nahradit.!je nový text , kterým nahradíte text argumentu Starý."}, "REPLACEB": {"a": "(starý; start; znaky; nový)", "d": "Nahradí na základě zadaného počtu bajtů část textového řetězce jiným textovým řetězcem, je určena pro jazyky používající dvoubajtov<PERSON> sadu (DBCS) - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> a korejština", "ad": "je text, ve kterém chcete zaměnit některé znaky.!je pozice znaku v textu argumentu Starý, který chcete nahradit textem argumentu Nový.!je počet znaků textu argumentu Starý, které chcete nahradit.!je nový text , kterým nahradíte text argumentu Starý."}, "REPT": {"a": "(text; počet)", "d": "Několikrát zopakuje zadaný text. Použijte funkci OPAKOVAT, chcete-li vyplnit buňku určitým počtem výskytů textového řetězce.", "ad": "je text, který chcete opakovat.!je kladné č<PERSON>lo určuj<PERSON>c<PERSON>, kolik<PERSON><PERSON>t chcete text opakovat."}, "RIGHT": {"a": "(text; [znaky])", "d": "Vrátí zadaný počet znaků od konce textového řetězce.", "ad": "je textový řetězec obsahu<PERSON><PERSON><PERSON><PERSON>, které chcete extrahovat.!určuje počet znaků, které chcete extrahovat. Jestliže tento argument nezadáte, bude jeho hodnota 1."}, "RIGHTB": {"a": "(text; [znaky])", "d": "Vrátí zadaný počet bajtů od konce textov<PERSON>ho řet<PERSON>zce, je určena pro jazyky používající dvoubajtov<PERSON> sadu (DBCS) - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>š<PERSON> a korejština", "ad": "je textový řetězec obsahu<PERSON><PERSON><PERSON><PERSON>, které chcete extrahovat.!určuje počet znaků, které chcete extrahovat. Jestliže tento argument nezadáte, bude jeho hodnota 1."}, "SEARCH": {"a": "(co; kde; [start])", "d": "Vrátí číslo prvního nalezeného výskytu znaku nebo textového řetězce. Směr hledání je zleva doprava. Velká a malá písmena nejsou rozlišována.", "ad": "je text, který chcete nalézt. Můžete použít zástupné znaky * a ?. Znaky * a ? naleznete pomocí řetěz<PERSON>ů ~? a ~* .!je text, ve kterém chcete hledat znak nebo textový řetězec argumentu Co.!je číslo znaku v argumentu Kde (počítáno zleva), ve kterém chcete začít vyhledávání. Jestliže tento argument nezadáte, bude jeho hodnota 1."}, "SEARCHB": {"a": "(co; kde; [start])", "d": "Vyhledají jeden textový řetězec v rámci druhého textového řetězce a vrátí číslo počáteční pozice prvního textového řetězce od prvního znaku druhého textového řetězce, je určena pro jazyky používající dvoubajtovou znakovou sadu (DBCS) - japonština, čínština a korejština", "ad": "je text, který chcete nalézt. Můžete použít zástupné znaky * a ?. Znaky * a ? naleznete pomocí řetěz<PERSON>ů ~? a ~* .!je text, ve kterém chcete hledat znak nebo textový řetězec argumentu Co.!je číslo znaku v argumentu Kde (počítáno zleva), ve kterém chcete začít vyhledávání. Jestliže tento argument nezadáte, bude jeho hodnota 1."}, "SUBSTITUTE": {"a": "(text; starý; nový; [instance])", "d": "Nahradí existující text novým textem v textovém řetězci.", "ad": "je text nebo odkaz na buňku obsahující text, ve kterém chcete zaměnit znaky.!je existující text, který chcete zaměnit. Jestliže malá a velká písmena v textu argumentu Starý nejsou shodná s malými a velkými písmeny v textu argumentu Text, funkce DOSADIT text nezamění.!je text, kterým chcete nahradit text argumentu Starý.!ur<PERSON><PERSON><PERSON>, který výskyt textu argumentu Starý chcete nahradit. Jestliže tento argument nezadáte, bude nahrazen každý výskyt textu argumentu Starý."}, "T": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je argument Hodnota text. <PERSON><PERSON><PERSON><PERSON><PERSON> ano, v<PERSON><PERSON><PERSON><PERSON> text, jest<PERSON><PERSON><PERSON> ne, vr<PERSON><PERSON><PERSON> uvo<PERSON> (pr<PERSON><PERSON><PERSON><PERSON> text).", "ad": "je testova<PERSON> ho<PERSON>."}, "TEXT": {"a": "(hodnota; formát)", "d": "Převede hodnotu na text v určitém formátu.", "ad": "je <PERSON><PERSON><PERSON>, v<PERSON><PERSON>, jeh<PERSON><PERSON> výsledek je číselná hodnota, nebo odkaz na buňku obsahující číselnou hodnotu.!je číselný formát ve formě textu vybraný v seznamu Druh na kartě Číslo dialogového okna Formát buněk"}, "TEXTJOIN": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; ignorovat_prázdné; text1; ...)", "d": "Zřetězí seznam nebo oblast textových řetězců pomoc<PERSON>.", "ad": "Znak nebo řetězec, který se vloží mezi jednotlivé položky textu!pokud má hodnotu PRAVDA (výchozí), ignoruje prázdné buňky.!1 až 252 textových řetězců nebo oblastí, kter<PERSON> chcete sloučit"}, "TRIM": {"a": "(text)", "d": "Odstraní všechny mezery z textového řetězce kromě jednotlivých mezer mezi slovy.", "ad": "je text, ze kterého chcete odstranit mezery."}, "UNICHAR": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí znak Unicode vztažený k dané č<PERSON>elné hodnotě", "ad": "je číslo Unicode představující znak"}, "UNICODE": {"a": "(text)", "d": "Vrá<PERSON><PERSON> (bod kódu) odpovídající prvnímu znaku textu", "ad": "je z<PERSON><PERSON>, pro který chcete zjistit hodnotu kódu Unicode"}, "UPPER": {"a": "(text)", "d": "Převede všechna písmena textového řetězce na velká.", "ad": "je text, kter<PERSON> chcete převést na velká písmena. <PERSON><PERSON><PERSON><PERSON> to být odkaz nebo textový řetězec."}, "VALUE": {"a": "(text)", "d": "Převede textový řetězec představující číslo na číslo.", "ad": "je text v uvozovkách nebo odkaz na buňku obsahující text, kter<PERSON> chcete převést."}, "AVEDEV": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí průměrnou hodnotu absolutních odchylek datových bodů od jejich střední hodnoty. Argumenty mohou být čís<PERSON>, matice nebo odkazy obsahující č<PERSON>.", "ad": "je 1 až 255 argumentů, pro kter<PERSON> chcete zjistit průměrnou hodnotu absolutních odchylek."}, "AVERAGE": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí prů<PERSON><PERSON><PERSON><PERSON> hodnot<PERSON> (aritmetický průměr) argumentů. Argumenty mohou být č<PERSON>, matice ne<PERSON>, k<PERSON><PERSON> o<PERSON>ahu<PERSON>.", "ad": "je 1 až 255 čís<PERSON>, jej<PERSON><PERSON> prů<PERSON><PERSON><PERSON><PERSON> hodnotu chcete zjistit."}, "AVERAGEA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vrátí prů<PERSON>ě<PERSON><PERSON> hodnotu (aritmetický průměr) argumentů. Text a logická hodnota NEPRAVDA mají hodnotu 0, logická hodnota PRAVDA má hodnotu 1. <PERSON><PERSON><PERSON><PERSON><PERSON> mohou b<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, matice nebo o<PERSON>.", "ad": "je 1 až <PERSON> argumentů, j<PERSON><PERSON><PERSON> pr<PERSON><PERSON><PERSON><PERSON> chcete zji<PERSON>it."}, "AVERAGEIF": {"a": "(oblast; kritérium; [oblast_pro_průměr])", "d": "Zjistí prů<PERSON><PERSON><PERSON><PERSON> hodn<PERSON> (aritmetický průměr) buněk určených danou podmínkou nebo kritériem.", "ad": "představuje oblast buněk, kter<PERSON> chcete vyhodnotit.!je podmínka nebo kritérium v podobě čísla, výrazu nebo textu definuj<PERSON><PERSON><PERSON><PERSON> bu<PERSON>, jej<PERSON><PERSON> prů<PERSON>ěr chcete zjistit.!jsou v<PERSON><PERSON>í b<PERSON>, jej<PERSON><PERSON> průměr bude zjištěn. Pokud parametr vynecháte, budou použity buňky v dané oblasti"}, "AVERAGEIFS": {"a": "(oblast_pro_průměr; oblast_kritérií; kritérium; ...)", "d": "Zjistí prů<PERSON><PERSON><PERSON><PERSON> ho<PERSON> (aritmetický průměr) buněk určených danou sadou podmínek nebo kritérií.", "ad": "jsou v<PERSON><PERSON><PERSON> b<PERSON>, j<PERSON><PERSON><PERSON> průměr bude zjištěn.!představuje oblast buněk, které chcete vyhodnotit na základě určené podmínky.!je podmínka nebo kritérium v podobě čísla, výrazu nebo textu definu<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, jej<PERSON><PERSON> průměr bude zjištěn."}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Vrátí hodnotu kumulativní funkce hustoty pravděpodobnosti beta rozdělení.", "ad": "je hodnota mezi hodnotami argumentů A a B, pro kterou chcete zjistit hodnotu funkce.!je parametr rozd<PERSON>, který musí být vět<PERSON><PERSON> než 0.!je parametr rozd<PERSON>í, který musí být větší než 0.!je volitelná dolní mez intervalu hodnot x. Je<PERSON><PERSON>že argument A nezadáte, bude jeho hodnota 0.!je volitelná horní mez intervalu hodnot x. Je<PERSON><PERSON><PERSON><PERSON> argument B nezadáte, bude jeho hodnota 1."}, "BETAINV": {"a": "(pravdě<PERSON>dobnost; alfa; beta; [A]; [B])", "d": "Vrátí inverzní hodnotu kumulativní funkce hustoty pravděpodobnosti beta rozdělení (inverzní funkce k funkci BETADIST).", "ad": "je pravděpodobnost beta rozdělení.!je parametr rozdělení, který musí být větš<PERSON> než 0.!je parametr rozdělení, který musí být větší než 0.!je volitelná dolní mez intervalu hodnot x. Je<PERSON><PERSON><PERSON>e argument A nezadáte, bude jeho hodnota 0.!je volitelná horní mez intervalu hodnot x. Je<PERSON><PERSON>že argument B nezadáte, bude jeho hodnota 1."}, "BETA.DIST": {"a": "(x; alfa; beta; kumulativní; [A]; [B])", "d": "Vrátí funkci rozdělení pravděpodobnosti beta.", "ad": "je hodnota mezi hodnotami argumentů A a B, pro kterou chcete zjistit hodnotu funkce.!je parametr rozd<PERSON>, který musí být vět<PERSON><PERSON> než 0.!je parametr rozd<PERSON>, který musí být větší než 0.!je logická hodnota: kumulativní distribuční funkce = PRAVDA; funkce hustoty pravděpodobnosti = NEPRAVDA.!je nepovinná dolní mez intervalu hodnot x. Jestliže argument A nezadáte, bude jeho hodnota 0.!je nepovinná horní mez intervalu hodnot x. Je<PERSON><PERSON>že argument B nezadáte, bude jeho hodnota 1."}, "BETA.INV": {"a": "(pravdě<PERSON>dobnost; alfa; beta; [A]; [B])", "d": "Vrátí inverzní hodnotu kumulativní funkce hustoty pravděpodobnosti beta rozdělení (BETA.DIST).", "ad": "je pravděpodobnost rozdělení beta.!je parametr rozdělení, který musí být vět<PERSON><PERSON> než 0.!je parametr rozdělení, který musí být větš<PERSON> než 0.!je nepovinná dolní mez intervalu hodnot x. Je<PERSON><PERSON><PERSON>e argument A nezadáte, bude jeho hodnota 0.!je nepovinná horní mez intervalu hodnot x. Je<PERSON><PERSON>že argument B nezadáte, bude jeho hodnota 1."}, "BINOMDIST": {"a": "(poč<PERSON>_úspěchů; pokusy; pravděpodobnost_úspěchu; kumulativní)", "d": "Vrátí hodnotu binomického rozdělení pravděpodobnosti jednotlivých veli<PERSON>.", "ad": "je počet úspěšných pokusů.!je počet nezávislých pokusů.!je pravděpodobnost každého úspěšného pokusu.!je logická hodnota: kumulativní distribuční funkce = PRAVDA, hromadná pravděpodobnostní funkce = NEPRAVDA."}, "BINOM.DIST": {"a": "(poč<PERSON>_úspěchů; pokusy; pravděpodobnost_úspěchu; kumulativní)", "d": "Vrátí hodnotu binomického rozdělení pravděpodobnosti jednotlivých veli<PERSON>.", "ad": "je počet úspěšných pokusů.!je počet nezávislých pokusů.!je pravděpodobnost každého úspěšného pokusu.!je logická hodnota: kumulativní distribuční funkce = PRAVDA, hromadná pravděpodobnostní funkce = NEPRAVDA."}, "BINOM.DIST.RANGE": {"a": "(poku<PERSON>; pravděpodobnost_úspěchu; počet_úspěchů; [počet_úspěchů2])", "d": "Vrátí pravděpodobnost výsledku pokusu pomocí binomického rozdělení", "ad": "je počet nezávislých pokusů!je pravděpodobnost úspěchu v každém pokusu!je počet úspěchů v pokusech!pokud je zadaná, vr<PERSON><PERSON><PERSON> tato funkce pravděpodobnost, že počet úspěšných pokusů bude ležet mezi hodnotami počet_úspěchů a počet_úspěchů2"}, "BINOM.INV": {"a": "(pokusy; pravděpodobnost_úspěchu; alfa)", "d": "Vrátí nej<PERSON> hodnot<PERSON>, pro kterou má kumulativní binomické rozdělení hodnotu větší nebo rovnu hodnotě kritéria.", "ad": "je počet Bernoulliho pokusů.!je pravděpodobnost každého úspěšného pokusu. Argument je číslo mezi 0 a 1 včetně.!je hodnota kritéria, číslo mezi 0 a 1 včetně."}, "CHIDIST": {"a": "(x; volnost)", "d": "Vrátí pravděpodobnost (pravý chvost) chí-kvadrát rozdělení.", "ad": "je ho<PERSON><PERSON> (nezáporné <PERSON>), pro kterou chcete zjistit pravděpodobnost rozdělení.!je počet stupňů volnosti. Argument je číslo mezi 1 a 10^10 kromě čísla 10^10."}, "CHIINV": {"a": "(pravděpodobnost; volnost)", "d": "Vrátí inverzní hodnotu pravděpodobnosti (pravý chvost) chí-kvadrát rozdělení.", "ad": "je pravděpodobnost chí-kvadrát rozdělení. Argument je číslo mezi 0 a 1 (včetně).!je počet stupňů volnosti. Argument je číslo mezi 1 a 10^10 kromě čísla 10^10."}, "CHITEST": {"a": "(akt<PERSON><PERSON><PERSON><PERSON>; očekávané)", "d": "Vrátí test nezávislosti: hodnota chí-kvadr<PERSON>t rozdělení pro statistické jednotky a příslušné stupně volnosti.", "ad": "je oblast dat obsahu<PERSON><PERSON><PERSON><PERSON> p<PERSON>, k<PERSON><PERSON> chcete testovat a srovnávat s předpokládanými hodnotami.!je oblast dat obsahující podíl součinu součtů řádků a sloupců a celkového součtu."}, "CHISQ.DIST": {"a": "(x; volnost; kumulativní)", "d": "Vrátí le<PERSON>trann<PERSON> pravděpodobnost rozdělení chí-kvadrát.", "ad": "je hodnota, pro kterou chcete zjistit pravděpodobnost rozdělení. Argument musí být nezáporné číslo.!je počet stupňů volnosti. Argument je číslo mezi 1 a 10^10 kromě čísla 10^10.!je logick<PERSON> hodnota, kterou funkce vrátí: kumulativní distribuční funkce = PRAVDA, funkce hustoty pravděpodobnosti = NEPRAVDA."}, "CHISQ.DIST.RT": {"a": "(x; volnost)", "d": "Vrátí pravostrannou pravděpodobnost rozdělení chí-kvadrát.", "ad": "je ho<PERSON><PERSON>, pro kterou chcete zjistit pravděpodobnost rozdělení. Argument musí být nezáporné číslo.!je počet stupňů volnosti. Argument je číslo mezi 1 a 10^10 kromě čísla 10^10."}, "CHISQ.INV": {"a": "(pravděpodobnost; volnost)", "d": "Vrátí hodnotu funkce inverzní k distribuční funkci levostranné pravděpodobnosti rozdělení chí-kvadrát.", "ad": "je pravděpodobnost rozdělení chí-kvadrát. Argument je hodnota z uzavřeného intervalu 0 až 1.!je počet stupňů volnosti. Argument je číslo mezi 1 a 10^10 kromě čísla 10^10."}, "CHISQ.INV.RT": {"a": "(pravděpodobnost; volnost)", "d": "Vrátí hodnotu funkce inverzní k distribuční funkci pravostranné pravděpodobnosti rozdělení chí-kvadrát.", "ad": "je pravděpodobnost rozdělení chí-kvadrát. Argument je hodnota z uzavřeného intervalu 0 až 1.!je počet stupňů volnosti. Argument je číslo mezi 1 a 10^10 kromě čísla 10^10."}, "CHISQ.TEST": {"a": "(akt<PERSON><PERSON><PERSON><PERSON>; očekávané)", "d": "Vrátí test nezávislosti: hodnota ze statistického rozdělení chí-kvadrát a příslušné stupně volnosti.", "ad": "je oblast dat obsahu<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> ch<PERSON>te testovat a srovnávat s předpokládanými hodnotami.!je oblast dat obsahující podíl součinu součtů řádků a sloupců a celkového součtu."}, "CONFIDENCE": {"a": "(alfa; sm_odch; velikost)", "d": "Vrátí interval spolehlivosti pro střední hodnotu základního souboru pomocí normálního rozdělení.", "ad": "je hladina vý<PERSON>ti, pomo<PERSON><PERSON> které je vypočítána hladina spolehlivosti. Argument je číslo větší než 0 a menší než 1.!je známá směrodatná odchylka základního souboru pro oblast dat. Argument sm_odch musí být větší než 0.!je velikost výběru."}, "CONFIDENCE.NORM": {"a": "(alfa; sm_odch; velikost)", "d": "Vrátí interval spolehlivosti pro střední hodnotu základního souboru pomocí normálního rozdělení.", "ad": "je hladina vý<PERSON>ti, pomo<PERSON><PERSON> které je vypočítána hladina spolehlivosti. Argument je číslo větší než 0 a menší než 1.!je známá směrodatná odchylka základního souboru pro oblast dat. Argument sm_odch musí být větší než 0.!je velikost výběru."}, "CONFIDENCE.T": {"a": "(alfa; sm_odch; velikost)", "d": "Vrátí interval spolehlivosti pro střední hodnotu základního souboru pomocí <PERSON> t-rozdělení.", "ad": "je hladina vý<PERSON>ti, pomo<PERSON><PERSON> které je vypočítána hladina spolehlivosti. Argument je číslo větší než 0 a menší než 1.!je známá směrodatná odchylka základního souboru pro oblast dat. Argument sm_odch musí být větší než 0.!je velikost výběru."}, "CORREL": {"a": "(matice1; matice2)", "d": "Vrátí k<PERSON>lační koeficient mezi dvěma množinami dat.", "ad": "je oblast buněk s hodnotami. Hodnoty mohou být <PERSON>, <PERSON><PERSON><PERSON><PERSON>, matice nebo odkazy obsahující čísla.!je druhá oblast buněk s hodnotami. Hodnoty mohou být <PERSON>, n<PERSON><PERSON><PERSON>, matice nebo odkazy obsahujíc<PERSON> č<PERSON>la."}, "COUNT": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vrátí počet buněk v oblasti obsahujících čísla.", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON> obsahuj<PERSON> nebo odkazují na různé typy dat, spo<PERSON><PERSON><PERSON><PERSON><PERSON> budou však pouze čísla."}, "COUNTA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vrátí počet buněk v oblasti, které nejsou prázdné.", "ad": "je 1 až 255 argumentů představujících hodnoty a bu<PERSON>ky, kter<PERSON> chcete spočítat. Hodnoty mohou představovat jakýkoli typ informací."}, "COUNTBLANK": {"a": "(oblast)", "d": "Vrátí počet prázdných buněk v zadané oblasti buněk.", "ad": "je oblast buněk, ve které chcete spočítat práz<PERSON>é b<PERSON>."}, "COUNTIF": {"a": "(oblast; kritérium)", "d": "Vrátí počet buněk v zadané oblasti, které splňují požadované kritérium.", "ad": "je oblast buněk, ve které chcete spočítat neprázdné buň<PERSON>.!jsou kritéria ve formě č<PERSON>la, výrazu nebo textu definu<PERSON><PERSON><PERSON><PERSON> b<PERSON>, kter<PERSON> budou spo<PERSON>."}, "COUNTIFS": {"a": "(oblast_kritérií; kritérium; ...)", "d": "Určí počet buněk na základě dané sady podmínek nebo kritérií.", "ad": "představuje oblast buněk, které chcete vyhodnotit na základě určené podmínky.!je podmínka v podobě čísla, výrazu nebo textu definuj<PERSON><PERSON><PERSON> b<PERSON>, jej<PERSON><PERSON> počet bude určen."}, "COVAR": {"a": "(matice1; matice2)", "d": "<PERSON>r<PERSON><PERSON><PERSON> hodnotu k<PERSON>, pr<PERSON><PERSON><PERSON><PERSON><PERSON> hodnotu součinů odchylek pro každou dvojici datových bodů ve dvou množinách dat.", "ad": "je první oblast buněk obsahující celá čísla. Hodnoty argumentu musí být č<PERSON>, matice nebo odkazy obsahující čísla.!je druhá oblast buněk obsahující celá čísla. Hodnoty argumentu musí být č<PERSON>, matice nebo odkazy obsahující čísla."}, "COVARIANCE.P": {"a": "(matice1; matice2)", "d": "Vrá<PERSON>í hodnotu kovariance základ<PERSON><PERSON>, pr<PERSON><PERSON><PERSON><PERSON>u hodnotu součinů odchylek pro každou dvojici datových bodů ve dvou množinách dat.", "ad": "je první oblast buněk obsahující celá čísla. Hodnoty argumentu musí být č<PERSON>, matice nebo odkazy obsahující čísla.!je druhá oblast buněk obsahující celá čísla. Hodnoty argumentu musí být č<PERSON>, matice nebo odkazy obsahující čísla."}, "COVARIANCE.S": {"a": "(matice1; matice2)", "d": "Vrátí hodnotu kovariance výběru, pr<PERSON><PERSON><PERSON><PERSON>u hodnotu součinů odchylek pro každou dvojici datových bodů ve dvou množinách dat.", "ad": "je první oblast buněk obsahující celá čísla. Hodnoty argumentu musí být č<PERSON>, matice nebo odkazy obsahující čísla.!je druhá oblast buněk obsahující celá čísla. Hodnoty argumentu musí být č<PERSON>, matice nebo odkazy obsahující čísla."}, "CRITBINOM": {"a": "(pokusy; pravděpodobnost_úspěchu; alfa)", "d": "Vrátí nej<PERSON> hodnot<PERSON>, pro kterou má kumulativní binomické rozdělení hodnotu větší nebo rovnu hodnotě kritéria.", "ad": "je počet Bernoulliho pokusů.!je pravděpodobnost každého úspěšného pokusu. Argument je číslo mezi 0 a 1 (včetně).!je hodnota kritéria, číslo mezi 0 a 1 (včetně)."}, "DEVSQ": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí součet druhých mocnin odchylek datových bodů od jejich střední hodnoty výběru.", "ad": "je 1 až 255 argumentů nebo matice či odkaz na matici, pro které chcete vypočítat výsledek funkce DEVSQ."}, "EXPONDIST": {"a": "(x; lambda; kumulativní)", "d": "Vrátí hodnotu exponenciálního rozdělení.", "ad": "je hodnota funkce, ne<PERSON><PERSON><PERSON><PERSON><PERSON> č<PERSON>lo.!je hodnota parametru, kladn<PERSON> č<PERSON>lo.!je logická hodnota pro funkci, kterou chcete vrátit: kumulativní distribuční funkce = PRAVDA, funkce hustoty pravděpodobnosti = NEPRAVDA."}, "EXPON.DIST": {"a": "(x; lambda; kumulativní)", "d": "Vrátí hodnotu exponenciálního rozdělení.", "ad": "je hodnota funkce, ne<PERSON><PERSON><PERSON><PERSON><PERSON> č<PERSON>lo.!je hodnota parametru, kladn<PERSON> č<PERSON>lo.!je logická hodnota pro funkci, kterou chcete vrátit: kumulativní distribuční funkce = PRAVDA, funkce hustoty pravděpodobnosti = NEPRAVDA."}, "FDIST": {"a": "(x; volnost1; volnost2)", "d": "Vrátí hodnotu F rozdělení (stupeň nonekvivalence) pravděpodobnosti (pravý chvost) pro dvě množiny dat.", "ad": "je ho<PERSON><PERSON> (nezá<PERSON><PERSON><PERSON>), pro kterou chcete vypočítat danou funkci.!je počet stupňů volnosti v čitateli, č<PERSON>lo mezi 1 a 10^10 kromě čísla 10^10.!je počet stupňů volnosti ve jmenovateli, č<PERSON>lo mezi 1 a 10^10 kromě čísla 10^10."}, "FINV": {"a": "(pravděpodobnost; volnost1; volnost2)", "d": "Vrátí hodnotu inverzní funkce k funkci F rozdělení pravděpodobnosti (pravý chvost): Jestliže p = FDIST(x,...), pak FINV(p,...) = x.", "ad": "je pravděpodobnost kumulativního F rozdělení, č<PERSON>lo mezi 0 a 1 (vč<PERSON>ně).!je počet stupňů volnosti v čitateli, číslo mezi 1 a 10^10 kromě čísla 10^10.!je počet stupňů volnosti ve jmenovateli, č<PERSON>lo mezi 1 a 10^10 kromě čísla 10^10."}, "FTEST": {"a": "(matice1; matice2)", "d": "Vrátí výsledek F-testu, tj. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (dva chvosty), že se rozptyly v argumentech matice1 a matice2 v<PERSON><PERSON>n<PERSON> neliš<PERSON>.", "ad": "je první matice nebo oblast dat. Hodnoty argumentu mohou být <PERSON>, matice nebo odkazy obsahující čísla. (Prázdné buňky jsou ignorovány.)!je druhá matice nebo oblast dat. Hodnoty argumentu mohou být <PERSON>, matice nebo odkazy obsahující čísla. (Prázdné buňky jsou ignorovány.)"}, "F.DIST": {"a": "(x; volnost1; volnost2; kumulativní)", "d": "Vrá<PERSON>í ho<PERSON> (levostranného) rozdělení pravděpodobnosti F (stupeň nonekvivalence) pro dvě množiny dat.", "ad": "je ho<PERSON><PERSON>, pro kterou chcete zjistit rozdělení pravděpodobnosti. Argument musí být nezáporné číslo.!je počet stupňů volnosti v čitateli, č<PERSON>lo mezi 1 a 10^10 kromě čísla 10^10.!je počet stupňů volnosti ve jmenovateli, č<PERSON>lo mezi 1 a 10^10 kromě čísla 10^10.!je logic<PERSON><PERSON> hodn<PERSON>, kterou funkce vrátí: kumulativní distribuční funkce = PRAVDA, funkce hustoty pravděpodobnosti = NEPRAVDA."}, "F.DIST.RT": {"a": "(x; volnost1; volnost2)", "d": "Vrá<PERSON>í ho<PERSON> (pravostranného) rozdělení pravděpodobnosti F (stupeň nonekvivalence) pro dvě množiny dat.", "ad": "je ho<PERSON><PERSON>, pro kterou chcete zjistit rozdělení pravděpodobnosti. Argument musí být nezáporné číslo.!je počet stupňů volnosti v čitateli, č<PERSON>lo mezi 1 a 10^10 kromě čísla 10^10.!je počet stupňů volnosti ve jmenovateli, č<PERSON>lo mezi 1 a 10^10 kromě čísla 10^10."}, "F.INV": {"a": "(pravděpodobnost; volnost1; volnost2)", "d": "Vrátí hodnotu inverzní funkce k distribuční funkci (levostranného) rozdělení pravděpodobnosti F: jestliže p = F.DIST(x,...), F.INV(p,...) = x.", "ad": "je pravděpodobnost kumulativního rozdělení F, č<PERSON>lo mezi 0 a 1 včetně.!je počet stupňů volnosti v čitateli, číslo mezi 1 a 10^10 kromě čísla 10^10.!je počet stupňů volnosti ve jmenovateli, č<PERSON>lo mezi 1 a 10^10 kromě čísla 10^10."}, "F.INV.RT": {"a": "(pravděpodobnost; volnost1; volnost2)", "d": "Vrátí hodnotu inverzní funkce k distribuční funkci (pravostranného) rozdělení pravděpodobnosti F: jestliže p = F.DIST.RT(x,...), F.INV.RT(p,...) = x.", "ad": "je pravděpodobnost kumulativního rozdělení F, č<PERSON>lo mezi 0 a 1 včetně.!je počet stupňů volnosti v čitateli, číslo mezi 1 a 10^10 kromě čísla 10^10.!je počet stupňů volnosti ve jmenovateli, č<PERSON>lo mezi 1 a 10^10 kromě čísla 10^10."}, "F.TEST": {"a": "(matice1; matice2)", "d": "Vrátí výsledek F-testu, tj. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (dva chvosty), že se rozptyly v argumentech matice1 a matice2 v<PERSON><PERSON>n<PERSON> neliš<PERSON>.", "ad": "je první matice nebo oblast dat. Hodnoty argumentu mohou být <PERSON>, matice nebo odkazy obsahující čísla. (Prázdné buňky jsou ignorovány.)!je druhá matice nebo oblast dat. Hodnoty argumentu mohou být <PERSON>, matice nebo odkazy obsahující čísla. (Prázdné buňky jsou ignorovány.)"}, "FISHER": {"a": "(x)", "d": "Vrátí hodnotu Fisherovy transformace.", "ad": "je hodnota, pro kterou chcete zjistit hodnotu transformace. Argument je číslo mezi -1 a 1 kromě čísel -1 a 1."}, "FISHERINV": {"a": "(y)", "d": "Vrátí hodnotu inverzní funkce k Fisherově transformaci: jestliže y = FISHER(x), FISHERINV(y) = x.", "ad": "je hodnota, pro kterou chcete zjistit hodnotu inverzní transformace."}, "FORECAST": {"a": "(x; pole_y; pole_x)", "d": "Vy<PERSON>čte (předpoví) budoucí hodnotu lineárního trendu pomocí existují<PERSON><PERSON>ch hodnot.", "ad": "je da<PERSON><PERSON> bod, pro který chcete odhadnout hodnotu. Musí to být číselná hodnota.!je závislá matice nebo oblast číselných dat.!je nezávislá matice nebo oblast číselných dat. Rozptyl argumentu x se nesmí rovnat nule."}, "FORECAST.ETS": {"a": "(c<PERSON><PERSON><PERSON>_datum; hodnoty; čas<PERSON>_osa; [sez<PERSON>nost]; [dokončení_dat]; [agregace])", "d": "Vrátí prognózu hodnoty k zadanému budoucímu cílovému datu pomocí metody exponenciálního vyhlazování.", "ad": "je da<PERSON><PERSON> bod, pro který Spreadsheet Editor předpovídá hodnotu. Měl by v sobě nést vzorek hodnot na časové ose.!je matice nebo oblast číselných dat, kter<PERSON> předpovídáte.!je nezávislá matice nebo oblast číselných dat. Data na časové ose musí být konzistentně krokovaná a nesmí být nulová.!je volitelná číselná hodnota označující délku sezónního opakování. Výchozí hodnota 1 označuje automatické detekování sezónnosti.!je volitelná hodnota pro zpracování chybějících hodnot. Výchozí hodnota 1 nahrazuje chybějící hodnoty interpolací, hodnota 0 je nahrazuje nulami.!je volitelná číselná hodnota pro agregování několika hodnot se stejným časovým razítkem. Pokud je tento parametr pr<PERSON>, Spreadsheet Editor vypočítá průměr hodnot."}, "FORECAST.ETS.CONFINT": {"a": "(c<PERSON><PERSON><PERSON>_datum; hodnoty; č<PERSON><PERSON>_osa; [úrove<PERSON>_spolehlivosti]; [sezónnost]; [dokončení_dat]; [agregace])", "d": "Vrátí interval spolehlivosti pro prognózu hodnoty k zadanému cílovému datu.", "ad": "je da<PERSON><PERSON> bod, pro který Spreadsheet Editor předpovídá hodnotu. Měl by v sobě nést vzorek hodnot na časové ose.!je matice nebo oblast číselných dat, kter<PERSON> předpovídáte.!je nezávislá matice nebo oblast číselných dat. Data na časové ose musí být konzistentně krokovaná a nesmí být nulová.!je číslo mezi 0 a 1 označující úroveň spolehlivosti pro vypočítaný interval spolehlivosti. Výchozí hodnota je 0,95.!je volitelná číselná hodnota označující délku sezónního opakování. Výchozí hodnota 1 označuje automatické detekování sezónnosti.!je volitelná hodnota pro zpracování chybějících hodnot. Výchozí hodnota 1 nahrazuje chybějící hodnoty interpolací, hodnota 0 je nahrazuje nulami.!je volitelná číselná hodnota pro agregování několika hodnot se stejným časovým razítkem. Pokud je tento parametr prázdný, Spreadsheet Editor vypočítá průměr hodnot."}, "FORECAST.ETS.SEASONALITY": {"a": "(hodnoty; čas<PERSON>_osa; [dokončen<PERSON>_dat]; [agregace])", "d": "Vrátí délku opakujícího se vzorku, který aplikace detekuje u zadané časové řady.", "ad": "je matice nebo oblast číselných dat, k<PERSON><PERSON> předpovídáte.!je nezávislá matice nebo oblast číselných dat. Data na časové ose musí být konzistentně krokovaná a nesmí být nulová.!je volitelná hodnota pro zpracování chybějících hodnot. Výchozí hodnota 1 nahrazuje chybějící hodnoty interpolací, hodnota 0 je nahrazuje nulami.!je volitelná číselná hodnota pro agregování několika hodnot se stejným časovým razítkem. Pokud je tento parametr pr<PERSON>ý, Spreadsheet Editor vypočítá průměr hodnot."}, "FORECAST.ETS.STAT": {"a": "(hodnoty; čas<PERSON>_osa; typ_statistiky; [sez<PERSON>nost]; [dokon<PERSON>en<PERSON>_dat]; [agregace])", "d": "Vrátí statistická data prognózy.", "ad": "je matice nebo oblast číselných dat, k<PERSON><PERSON> předpovídáte.!je nezávislá matice nebo oblast číselných dat. Data na časové ose musí být konzistentně krokovaná a nesmí být nulová.!je číslo mezi 1 a 8 označující statistiku, kterou bude Spreadsheet Editor vracet pro prognózu.!je volitelná číselná hodnota označující délku sezónního opakování. Výchozí hodnota 1 označuje automatické detekování sezónnosti.!je volitelná hodnota pro zpracování chybějících hodnot. Výchozí hodnota 1 nahrazuje chybějící hodnoty interpolací, hodnota 0 je nahrazuje nulami.!je volitelná číselná hodnota pro agregování několika hodnot se stejným časovým razítkem. Pokud je tento parametr p<PERSON>, Spreadsheet Editor vypočítá průměr hodnot."}, "FORECAST.LINEAR": {"a": "(x; pole_y; pole_x)", "d": "Vy<PERSON>čte (předpoví) budoucí hodnotu lineárního trendu pomocí existují<PERSON><PERSON>ch hodnot.", "ad": "je da<PERSON><PERSON> bod, pro který chcete odhadnout hodnotu. Musí to být číselná hodnota.!je závislá matice nebo oblast číselných dat.!je nezávislá matice nebo oblast číselných dat. Rozptyl argumentu x se nesmí rovnat nule."}, "FREQUENCY": {"a": "(data; hodnoty)", "d": "Vypočte počet výskytů hodnot v oblasti hodnot a vrátí vertikální matici čísel, která má o jeden prvek více než argument Hodnoty.", "ad": "je matice nebo odkaz na množinu hodnot, pro které chcete zjistit počet výskytů (prázdn<PERSON> buňky a text jsou přeskočeny).!je matice nebo odkaz na intervaly, do kterých chcete seskupit hodnoty argumentu Data."}, "GAMMA": {"a": "(x)", "d": "Vrátí hodnotu funkce gama", "ad": "je hodn<PERSON>, pro kterou chcete vypočítat gama"}, "GAMMADIST": {"a": "(x; alfa; beta; kumulativní)", "d": "Vrátí hodnotu gama rozdělení.", "ad": "je hodn<PERSON> (nezá<PERSON><PERSON><PERSON>), pro kterou chcete zjistit hodnotu rozdělení.!je parametr rozd<PERSON>lení, kladn<PERSON> č<PERSON>.!je parametr rozdělení, kladn<PERSON>. Pokud beta = 1, vr<PERSON><PERSON><PERSON> funkce GAMMADIST hodnotu standardního gama rozdělení.!je logická hodnota: vrácení kumulativní distribuční funkce = PRAVDA; vrácení hromadné pravděpodobnostní funkce = NEPRAVDA nebo bez zadání."}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulativní)", "d": "Vrátí hodnotu gama rozdělení.", "ad": "je hodn<PERSON> (nezá<PERSON><PERSON><PERSON>), pro kterou chcete zjistit hodnotu rozdělení.!je parametr rozdělení, kladn<PERSON>.!je parametr rozdělení, kladn<PERSON>. Pokud beta = 1, vr<PERSON><PERSON><PERSON> funkce GAMMA.DIST hodnotu standardního gama rozdělení.!je logická hodnota: vrácení kumulativní distribuční funkce = PRAVDA; vrácení hromadné pravděpodobnostní funkce = NEPRAVDA nebo bez zadání."}, "GAMMAINV": {"a": "(pravdě<PERSON>dobnost; alfa; beta)", "d": "Vrátí hodnotu inverzní funkce ke kumulativnímu gama rozdělení: Jestliže p = GAMMADIST(x,...), pak GAMMAINV(p,...) = x.", "ad": "je pravděpodobnost gama rozdělení, <PERSON><PERSON><PERSON> mezi 0 a 1 (v<PERSON><PERSON><PERSON><PERSON>).!je parametr rozdělení, kladn<PERSON> č<PERSON>.!je parametr rozdělení, kladn<PERSON>. Pokud beta = 1, vrátí funkce GAMMAINV inverzní funkci ke standardnímu gama rozdělení."}, "GAMMA.INV": {"a": "(pravdě<PERSON>dobnost; alfa; beta)", "d": "Vrátí hodnotu inverzní funkce k distribuční funkci kumulativního rozdělení gama: jestliže p = GAMMA.DIST(x,...), potom GAMMA.INV(p,...) = x.", "ad": "je pravděpodobnost rozdělení gama, <PERSON><PERSON><PERSON> mezi 0 a 1 včetně.!je parametr rozdělení, kladn<PERSON> č<PERSON>lo.!je parametr rozdělení, kladn<PERSON>. Je-li argument beta roven 1, funkce GAMMA.INV vrátí inverzní funkci ke standardní distribuční funkci rozdělení gama."}, "GAMMALN": {"a": "(x)", "d": "Vrátí přirozený logaritmus funkce gama.", "ad": "je hodn<PERSON>, pro kterou chcete vypočítat hodnotu funkce GAMMALN. Argument musí být kladné <PERSON>."}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Vrátí přirozený logaritmus funkce gama.", "ad": "je hodn<PERSON>, pro kterou chcete vypočítat hodnotu funkce GAMMALN.PRECISE. Argument musí být kladn<PERSON>."}, "GAUSS": {"a": "(x)", "d": "Vrátí hodnotu 0,5 menší než u standardního normálního součtového rozdělení", "ad": "je ho<PERSON>, pro kterou chcete získat rozdělení"}, "GEOMEAN": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí <PERSON>ý průměr matice nebo oblasti kladných číselných dat.", "ad": "je 1 až <PERSON> čís<PERSON> nebo n<PERSON>, matic či od<PERSON><PERSON> obs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jej<PERSON><PERSON> průměr chcete zjistit."}, "GROWTH": {"a": "(pole_y; [pole_x]; [nová_x]; [b])", "d": "Vrátí hodnoty trendu exponenciálního růstu odpovídajícího známým datovým bodům.", "ad": "je množina hodnot y počítaných z rovnice y = b*m^x. <PERSON><PERSON><PERSON><PERSON> to být matice nebo oblast kladných čísel.!je volitelná množina hodnot x počítaných z rovnice y = b*m^x. <PERSON><PERSON><PERSON><PERSON> to být matice nebo oblast stejné velikosti jako oblast argumentu Pole_y.!jsou nové hodnoty x, pro které chcete zjistit odpovídající hodnoty y pomocí funkce LOGLINTREND.!je logická hodnota: konstanta b se vypočítá, pokud argument b = PRAVDA, konstanta b je rovna 1, pokud je argument b = NEPRAVDA nebo vynechaný."}, "HARMEAN": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí harmonic<PERSON>ý průměr množiny kladných čísel: reciproční hodnota aritmetického průměru recipročn<PERSON><PERSON> čísel.", "ad": "je 1 až <PERSON> čís<PERSON> nebo n<PERSON>, matic či od<PERSON><PERSON> obs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jej<PERSON><PERSON> průměr chcete zjistit."}, "HYPGEOM.DIST": {"a": "(úspěch; celkem; zák<PERSON>_úspěch; základ_celkem; kumulativní)", "d": "Vrátí hodnotu hypergeometrického rozdělení.", "ad": "je počet úspěšných pokusů ve výběru.!je velikost výběru.!je počet úspěšných pokusů v základním souboru.!je velikost základního souboru.!je logická hodnota: kumulativní distribuční funkce = PRAVDA, funkce hustoty pravděpodobnosti = NEPRAVDA."}, "HYPGEOMDIST": {"a": "(ú<PERSON><PERSON><PERSON>; celkem; zák<PERSON>_úspěch; základ_celkem)", "d": "Vrátí hodnotu hypergeometrického rozdělení.", "ad": "je počet úspěšných pokusů ve výběru.!je velikost výběru.!je počet úspěšných pokusů v základním souboru.!je velikost základního souboru."}, "INTERCEPT": {"a": "(pole_y; pole_x)", "d": "Vypočte souřadnice bodu, ve kterém čára protne osu y, pomocí proložení nejlepší regresní čáry známými hodnotami x a y.", "ad": "je závislá množina pozorování nebo dat. Hodnoty argumentu mohou být čísla nebo názvy, matice nebo odkazy obsahující čísla.!je nezávislá množina pozorování nebo dat. Hodnoty argumentu mohou být čísla nebo názvy, matice nebo odkazy obsahující čísla."}, "KURT": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí hodnotu excesu množiny dat.", "ad": "je 1 až <PERSON> č<PERSON> nebo <PERSON>, matic či od<PERSON><PERSON> obs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pro kter<PERSON> chcete zjistit exces."}, "LARGE": {"a": "(pole; k)", "d": "Vrátí k-tou největší hodnotu množ<PERSON>y dat, nap<PERSON><PERSON><PERSON> pá<PERSON> největ<PERSON><PERSON>.", "ad": "je matice nebo oblast dat, pro kterou chcete určit k-tou největší hodnotu.!je pozice hledané hodnoty (počítáno od největší hodnoty) v matici nebo oblasti buněk."}, "LINEST": {"a": "(pole_y; [pole_x]; [b]; [stat])", "d": "Vrátí statistiku popisující lineární trend odpovídající známým datovým bodům proložením přímky vypočtené metodou nejmenš<PERSON>ch <PERSON>t<PERSON>ů.", "ad": "je množina hodnot y počítaných pomocí rovnice y = mx + b.!je množina hodnot x počítaných pomocí rovnice y = mx + b.!je logická hodnota: konstanta b se vypočítá, pokud je argument b = PRAVDA nebo vynechán, konstanta b je rovna 0, pokud je argument b = NEPRAVDA.!je logická hodnota: dal<PERSON><PERSON> návratová regresní statistika = PRAVDA, návratové koeficienty m a konstanta b = NEPRAVDA nebo bez zadání."}, "LOGEST": {"a": "(pole_y; [pole_x]; [b]; [stat])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> statist<PERSON>, která popisuje exponenciální křivku odpovídající známým datovým bodům.", "ad": "je množina hodnot y počítaných pomocí rovnice y = b*m^x.!je množina hodnot x počítaných pomocí rovnice y = b*m^x.!je logická hodnota: konstanta b je vy<PERSON>čítán<PERSON>, pokud je argument b = PRAVDA nebo vynechaný, konstanta b je rovna 1, pokud je argument b = NEPRAVDA.!je logická hodnota: dal<PERSON><PERSON> návratová regresní statistika = PRAVDA, návratové koeficienty m a konstanta b = NEPRAVDA nebo bez zadání."}, "LOGINV": {"a": "(pravdě<PERSON>dobnost; stř_hodn; sm_odch)", "d": "Vrátí inverzní funkci ke kumulativní distribuční funkci logaritmicko-normálního rozdělení hodnot x, kde funkce ln(x) má normální rozdělení s parametry stř_hodn a sm_odch.", "ad": "je pravděpodobnost logaritmicko-normálního rozdělení, <PERSON><PERSON>lo mezi 0 a 1 (v<PERSON><PERSON><PERSON>ě).!je střední hodnota funkce ln(x).!je směrodatná odchylka funkce ln(x), klad<PERSON><PERSON>."}, "LOGNORM.DIST": {"a": "(x; středn<PERSON>; sm_odchy<PERSON>a; kumulativní)", "d": "Vrátí hodnotu logaritmicko-normálního rozdělení hodnot x, kde funkce ln(x) má normální rozdělení s parametry Střední a Sm_odch.", "ad": "je hodnota, pro kterou chcete zjistit hodnotu rozdělení. Argument je kladné č<PERSON>lo.!je střední hodnota funkce ln(x).!je směrodatná odchylka funkce ln(x), kladn<PERSON> č<PERSON>.!je logická hodnota: kumulativní distribuční funkce = PRAVDA, funkce hustoty pravděpodobnosti = NEPRAVDA."}, "LOGNORM.INV": {"a": "(pravdě<PERSON>dobnost; stř_hodn; sm_odch)", "d": "Vrátí inverzní funkci ke kumulativní distribuční funkci logaritmicko-normálního rozdělení hodnot x, kde funkce ln(x) má normální rozdělení s parametry Stř_hodn a Sm_odch.", "ad": "je pravděpodobnost logaritmicko-normálního rozdělení, č<PERSON>lo mezi 0 a 1 včetně.!je střední hodnota funkce ln(x).!je směrodatná odchylka funkce ln(x), klad<PERSON><PERSON>."}, "LOGNORMDIST": {"a": "(x; střední; sm_odch)", "d": "Vrátí hodnotu kumulativního logaritmicko-normálního rozdělení hodnot x, kde funkce ln(x) má normální rozdělení s parametry střední a sm_odch.", "ad": "je ho<PERSON><PERSON> (k<PERSON><PERSON><PERSON>), pro kterou chcete zjistit rozdělení.!je střední hodnota funkce ln(x).!je směrodatná odchylka funkce ln(x), klad<PERSON><PERSON>."}, "MAX": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí maximální hodnotu množiny hodnot. Přeskočí logické hodnoty a text.", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> hodnot nebo čísel ve formátu textu, j<PERSON><PERSON><PERSON> maximální hodnotu chcete nalézt."}, "MAXA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vrátí maximální hodnotu v množině hodnot. Nepřeskočí logické hodnoty a text.", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> hodnot nebo čísel ve formátu textu, pro kter<PERSON> chcete zjistit maximální hodnotu."}, "MAXIFS": {"a": "(max_oblast; kritéria_oblast; kritéria; ...)", "d": "Vrátí maximální hodnotu z buněk určených sadou podmínek nebo kritérií.", "ad": "<PERSON><PERSON><PERSON><PERSON>, ve kterých chcete zjistit maximální hodnotu!je oblast buněk, ve kterých chcete vyhodnotit určitou podmínku.!je podmínka nebo kritérium jako <PERSON>, v<PERSON><PERSON> nebo text, k<PERSON><PERSON> definuje, kter<PERSON> buň<PERSON> se budou počítat při určování maximální hodnoty."}, "MEDIAN": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, st<PERSON>ed<PERSON><PERSON> hodnotu množiny zadaných <PERSON>.", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, matic nebo o<PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pro k<PERSON><PERSON> chcete nalézt medián."}, "MIN": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí minimální hodnotu množiny hodnot. Přeskočí logické hodnoty a text.", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> hodnot nebo čísel ve formátu textu, jej<PERSON><PERSON> minimální hodnotu chcete nalézt."}, "MINA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vrátí minimální hodnotu v množině hodnot. Nepřeskočí logické hodnoty a text.", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> hodnot nebo čísel ve formátu textu, pro které chcete zjistit minimální hodnotu."}, "MINIFS": {"a": "(min_oblast; kritéria_oblast; kritéria; ...)", "d": "Vrátí minimální hodnotu z buněk určených sadou podmínek nebo kritérií.", "ad": "<PERSON><PERSON><PERSON><PERSON>, ve kterých chcete zjistit minimální hodnotu!je oblast buněk, ve kterých chcete vyhodnotit určitou podmínku.!je podmínka nebo kritérium jako <PERSON>, v<PERSON>raz nebo text, k<PERSON><PERSON> definuje, kter<PERSON> buň<PERSON> se budou počítat při určování minimální hodnoty."}, "MODE": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí hodnotu, která se v matici nebo v oblasti dat vyskytuje nejčastěji.", "ad": "je 1 až <PERSON> čís<PERSON> nebo <PERSON>, matic <PERSON>i od<PERSON><PERSON> obs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jej<PERSON><PERSON> modus chcete zjistit."}, "MODE.MULT": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí vertikální matici nejčastěji se vyskytujících (opakovaných) hodnot v matici nebo oblasti dat. Chcete-li získat horizontální matici, použijte vzorec =TRANSPOZICE(MODE.MULT(číslo1;číslo2;...))", "ad": "je 1 až <PERSON> čís<PERSON> nebo <PERSON>, matic <PERSON>i od<PERSON><PERSON> obs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jej<PERSON><PERSON> modus chcete zjistit."}, "MODE.SNGL": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí hodnotu, která se v matici nebo v oblasti dat vyskytuje nejčastěji.", "ad": "je 1 až <PERSON> čís<PERSON> nebo <PERSON>, matic <PERSON>i od<PERSON><PERSON> obs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jej<PERSON><PERSON> modus chcete zjistit."}, "NEGBINOM.DIST": {"a": "(počet_neúspěchů; počet_úspěchů; pravděpodobnost_úspěchu; kumulativní)", "d": "Vrátí hodnotu negativního binomick<PERSON>ho rozd<PERSON>lení, tj. <PERSON>ra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že neúspěchy argumentu počet_neúspěchů nastanou dříve než úspěch argumentu počet_úspěchů s pravděpodobností určenou argumentem pravděpodobnost_úspěchu.", "ad": "je počet neúspěšných pokusů.!je mezní počet úspěšných pokusů.!je pravděpodobnost úspěchu, č<PERSON>lo mezi 0 a 1.!je logická hodnota: kumulativní distribuční funkce = PRAVDA, hromadná pravděpodobnostní funkce = NEPRAVDA."}, "NEGBINOMDIST": {"a": "(počet_neúspěchů; počet_úspěchů; pravděpodobnost_úspěchu)", "d": "Vrátí hodnotu negativního binomick<PERSON>ho rozdělení, tj. <PERSON>ra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že počet neúspěchů určený argumentem počet_neúspěchů nastane dříve než počet úspěchů určených argumentem počet_úspěchů s pravděpodobností určenou argumentem pravděpodobnost_úspěchu.", "ad": "je počet neúspěšných pokusů.!je mezní počet úspěšných pokusů.!je pravděpodobnost úspěchu, číslo mezi 0 a 1."}, "NORM.DIST": {"a": "(x; střed_hodn; sm_odch; kumulativní)", "d": "Vrátí hodnotu normálního rozdělení pro zadanou střední hodnotu a směrodatnou odchylku.", "ad": "je hodnota, pro kterou chcete zjistit rozdělení.!je aritmetická střední hodnota rozdělení.!je směrodatná odchylka rozdělení, kladn<PERSON>.!je logická hodnota: kumulativní distribuční funkce = PRAVDA, funkce hustoty pravděpodobnosti = NEPRAVDA."}, "NORMDIST": {"a": "(x; střed_hodn; sm_odch; kumulativní)", "d": "Vrátí hodnotu normálního kumulativního rozdělení pro zadanou střední hodnotu a směrodatnou odchylku.", "ad": "je hodnota, pro kterou chcete zjistit rozdělení.!je aritmetická střední hodnota rozdělení.!je směrodatná odchylka rozdělení, kladn<PERSON>.!je logická hodnota: kumulativní distribuční funkce = PRAVDA, funkce hustoty pravděpodobnosti = NEPRAVDA."}, "NORM.INV": {"a": "(pravdě<PERSON>dobnost; střední; sm_odch)", "d": "Vrátí inverzní funkci k distribuční funkci normálního kumulativního rozdělení pro zadanou střední hodnotu a směrodatnou odchylku.", "ad": "je pravděpodobnost normálního rozdělení, č<PERSON>lo mezi 0 a 1 včetně.!je aritmetická střední hodnota rozdělení.!je směrodatná odchylka rozdělení, kladn<PERSON>."}, "NORMINV": {"a": "(pravdě<PERSON>dobnost; střední; sm_odch)", "d": "Vrátí inverzní funkci k normálnímu kumulativnímu rozdělení pro zadanou střední hodnotu a směrodatnou odchylku.", "ad": "je pravděpodobnost normálního rozdělení, <PERSON><PERSON><PERSON>zi 0 a 1 (vč<PERSON>ně).!je aritmetická střední hodnota rozdělení.!je směrodatná odchylka rozdělení, kladn<PERSON>."}, "NORM.S.DIST": {"a": "(z; kumulativní)", "d": "Vrátí standardní normá<PERSON>í rozd<PERSON> (má střední hodnotu nula a směrodatnou odchylku jedna).", "ad": "je hodnota, pro kterou chcete zjistit rozdělení.!je logická hodnota, kterou funkce vrátí: kumulativní distribuční funkce = TRUE, funkce hustoty pravděpodobnosti = NEPRAVDA."}, "NORMSDIST": {"a": "(z)", "d": "Vrátí hodnotu standardního normálního kumulativního rozdělení. (Střední hodnota daného rozdělení je rovna 0 a jeho směrodatná odchylka je rovna 1.)", "ad": "je ho<PERSON>, pro kterou chcete zjistit rozdělení."}, "NORM.S.INV": {"a": "(pravděpodobnost)", "d": "Vrátí inverzní funkci k distribuční funkci standardního normálního kumulativního rozdělení (které má střední hodnotu rovnou 0 a směrodatnou odchylku 1).", "ad": "je pravděpodobnost normálního rozdělení, číslo mezi 0 a 1 včetně."}, "NORMSINV": {"a": "(pravděpodobnost)", "d": "Vrátí inverzní funkci ke standardnímu normálnímu kumulativnímu rozdělení. (Střední hodnota daného rozdělení je rovna 0 a jeho směrodatná odchylka je rovna 1).", "ad": "je pravděpodobnost normálního rozdělení, č<PERSON>lo <PERSON>zi 0 a 1 (včetně)."}, "PEARSON": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>v výsledný momentový korelační koe<PERSON> r.", "ad": "je množina nezávislých hodnot.!je množina závislých hodnot."}, "PERCENTILE": {"a": "(matice; k)", "d": "Vrátí hodnotu k-tého percentilu hodnot v oblasti.", "ad": "je matice nebo oblast dat, která definuje relativní umístění.!je hodnota percentilu, která se nachází mezi 0 a 1 (v<PERSON><PERSON>ně)."}, "PERCENTILE.EXC": {"a": "(matice; k)", "d": "Vrátí hodnotu k-tého percentilu hodnot v oblasti, kde hodnota k spadá do oblasti 0..1 (s vyloučením hodnot 0 a 1).", "ad": "je matice nebo oblast dat, která definuje relativní umístění.!je hodnota percentilu, která se nachází mezi 0 a 1 (v<PERSON><PERSON>ně)."}, "PERCENTILE.INC": {"a": "(matice; k)", "d": "Vrátí hodnotu k-tého percentilu hodnot v oblasti, kde hodnota k spadá do oblasti 0..1 (včetně).", "ad": "je matice nebo oblast dat, která definuje relativní umístění.!je hodnota percentilu, která se nachází mezi 0 a 1 (v<PERSON><PERSON>ně)."}, "PERCENTRANK": {"a": "(matice; x; [významnost])", "d": "Vrátí pořadí hodnoty v množině dat vyjádřené procentuální částí množiny dat.", "ad": "je matice nebo oblast dat s číselnými hodnotami, kter<PERSON> definuje relativní umístění.!je hodnota, pro kterou chcete zjistit pořadí.!je volitelná hodnota, která určuje počet významných desetinných míst výsledné procentuální hodnoty. Jestliže tento argument nezadáte, bude jeho hodnota 3 (0,xxx %)."}, "PERCENTRANK.EXC": {"a": "(matice; x; [významnost])", "d": "Vrátí pořadí hodnoty v množině dat vyjádřené procentuální částí (0..1, s vyloučením hodnot 0 a 1) množiny dat.", "ad": "je pole nebo oblast dat s číselnými hodnotami, kter<PERSON> definují relativní umístění!je hodnota, pro kterou chcete znát pořadí!je volitelná hodnota, kter<PERSON> určuje počet platných číslic vráceného procenta, tři číslice v případě vynechání (0,xxx %)"}, "PERCENTRANK.INC": {"a": "(matice; x; [významnost])", "d": "Vrátí pořadí hodnoty v množině dat vyjádřené procentuální částí (0..1, v<PERSON><PERSON><PERSON>ě) množiny dat.", "ad": "je pole nebo oblast dat s číselnými hodnotami, kter<PERSON> definují relativní umístění!je hodnota, pro kterou chcete znát pořadí!je volitelná hodnota, kter<PERSON> určuje počet platných číslic vráceného procenta, tři číslice v případě vynechání (0,xxx %)"}, "PERMUT": {"a": "(počet; permutace)", "d": "Vrátí počet permutací pro zadaný počet objektů, které lze vybrat z celkového počtu objektů.", "ad": "je celkový počet objektů.!je počet objektů v každé permutaci."}, "PERMUTATIONA": {"a": "(počet; permutace)", "d": "Vrátí počet permutací pro zadaný počet objektů (s opakováním) které je možné vybrat z celkového počtu objektů", "ad": "je počet objektů! v každé permutaci"}, "PHI": {"a": "(x)", "d": "Vrátí hodnotu funkce hustoty pro standardní normální rozdělení", "ad": "<PERSON><PERSON> <PERSON><PERSON>, pro které chcete zjistit hustotu standardn<PERSON><PERSON> norm<PERSON>lního rozdělení"}, "POISSON": {"a": "(x; středn<PERSON>; kumulativní)", "d": "Vrátí hodnotu Poissonova rozdělení.", "ad": "je počet událostí.!je předpokládaná číselná hodnota, kladn<PERSON> číslo.!je logická hodnota: kumulativní distribuční funkce Poissonova rozdělení pravděpodobnosti = PRAVDA, hromadná pravděpodobnostní funkce Poissonova rozdělení = NEPRAVDA."}, "POISSON.DIST": {"a": "(x; středn<PERSON>; kumulativní)", "d": "Vrátí hodnotu Poissonova rozdělení.", "ad": "je počet událostí.!je předpokládaná číselná hodnota, kladn<PERSON> číslo.!je logická hodnota: kumulativní distribuční funkce Poissonova rozdělení pravděpodobnosti = PRAVDA, hromadná pravděpodobnostní funkce Poissonova rozdělení = NEPRAVDA."}, "PROB": {"a": "(x_oblast; prst_oblast; dolní_limit; [horní_limit])", "d": "Vrátí pravděpodobnost toho, že hodnoty v oblasti leží mezi dvěma mezemi nebo jsou rovny dolní mezi.", "ad": "je oblast číselných hodnot x, pro které chcete zjistit požadovanou pravděpodobnost.!je množina hodnot pravděpodobností spojených s hodnotami argumentu X_oblast. Jsou to čísla mezi 0 a 1 kromě čísla 0.!je dolní mez hodnot, pro které chcete zjistit požadovanou pravděpodobnost.!je volitelná horní mez hodnot. Jest<PERSON>že tento argument nezadáte, funkce PROB vrátí pravděpodobnost toho, že hodnoty argumentu X_oblast jsou rovny argumentu Dolní_limit."}, "QUARTILE": {"a": "(matice; kvar<PERSON>)", "d": "Vrátí hodnotu kvartilu množiny dat.", "ad": "je matice nebo oblast buněk s číselnými hodnotami, pro které chcete zjistit hodnotu kvartilu.!je číslo: minimální hodnota = 0, prv<PERSON><PERSON> kvartil = 1, medi<PERSON> = 2, tř<PERSON><PERSON> kvartil = 3, maximální hodnota = 4."}, "QUARTILE.INC": {"a": "(matice; kvar<PERSON>)", "d": "Vrátí hodnotu kvartilu množiny dat na základě hodnot percentilu z oblasti 0..1 (vč<PERSON><PERSON>ě).", "ad": "je matice nebo oblast buněk s číselnými hodnotami, pro které chcete zjistit hodnotu kvartilu.!je číslo: minimální hodnota = 0, prv<PERSON><PERSON> kvartil = 1, medi<PERSON> = 2, tř<PERSON><PERSON> kvartil = 3, maximální hodnota = 4."}, "QUARTILE.EXC": {"a": "(matice; kvar<PERSON>)", "d": "Vrátí hodnotu kvartilu množiny dat na základě hodnot percentilu z oblasti 0..1 (s vyloučením hodnot 0 a 1).", "ad": "je matice nebo oblast buněk s číselnými hodnotami, pro které chcete zjistit hodnotu kvartilu.!je číslo: minimální hodnota = 0, prv<PERSON><PERSON> kvartil = 1, medi<PERSON> = 2, tř<PERSON><PERSON> kvartil = 3, maximální hodnota = 4."}, "RANK": {"a": "(č<PERSON>lo; odkaz; [pořad<PERSON>])", "d": "Vrátí pořadí čísla v seznamu čísel: jeho relativní velikost vzhledem k hodnotám v seznamu.", "ad": "je č<PERSON><PERSON>, jeho<PERSON> pořadí chcete zjistit.!je matice nebo odkaz na seznam čísel. Nečíselné hodnoty jsou ignorovány.!je číslo: pořadí v seznamu seřazeném sestupně = 0 nebo bez zadání, pořad<PERSON> v seznamu seřazeném vzestupně = libovolná hodnota různá od nuly."}, "RANK.AVG": {"a": "(č<PERSON>lo; odkaz; [pořad<PERSON>])", "d": "Vrátí pořadí čísla v seznamu čísel: jeho relativní velikost vzhledem k ostatním hodnotám v seznamu. Má-li stejné pořadí více než jedna hodnota, bude vráceno průměrné pořadí.", "ad": "je č<PERSON><PERSON>, jeho<PERSON> pořadí chcete zjistit.!je matice nebo odkaz na seznam čísel. Nečíselné hodnoty jsou přeskočeny.!je číslo: pořadí v seznamu seřazeném sestupně = 0 nebo bez zadání, pořad<PERSON> v seznamu seřazeném vzestupně = libovolná hodnota různá od nuly."}, "RANK.EQ": {"a": "(č<PERSON>lo; odkaz; [pořad<PERSON>])", "d": "Vrátí pořadí čísla v seznamu čísel: jeho relativní velikost vzhledem k ostatním hodnotám v seznamu. Má-li stejné pořadí více než jedna hodnota, bude vráceno nejvyšší pořadí dané množiny hodnot.", "ad": "je č<PERSON><PERSON>, jeho<PERSON> pořadí chcete zjistit.!je matice nebo odkaz na seznam čísel. Nečíselné hodnoty jsou přeskočeny.!je číslo: pořadí v seznamu seřazeném sestupně = 0 nebo bez zadání, pořad<PERSON> v seznamu seřazeném vzestupně = libovolná hodnota různá od nuly."}, "RSQ": {"a": "(pole_y; pole_x)", "d": "Vrá<PERSON><PERSON> dru<PERSON> mocninu <PERSON> výsledného momentového korelačního koeficientu pomocí zadan<PERSON>ch da<PERSON>ch bod<PERSON>.", "ad": "je matice nebo oblast datových bodů. <PERSON><PERSON><PERSON><PERSON> to být čísla nebo názvy, matice nebo odkazy obsahující čísla.!je matice nebo oblast datových bodů. <PERSON><PERSON><PERSON><PERSON> to být čísla nebo názvy, matice nebo odkazy obsahující čísla."}, "SKEW": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí zešikmení rozdělení: charakteristika stupně asymetrie rozdělení kolem jeho střední hodnoty.", "ad": "je 1 až <PERSON> čís<PERSON> nebo n<PERSON>, matic či od<PERSON><PERSON> obs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pro kter<PERSON> chcete zjistit zešikmení."}, "SKEW.P": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí zešikmení rozdělení založené na základním souboru: charakteristika stupně asymetrie rozdělení kolem jeho střední hodnoty", "ad": "je 1 až 254 č<PERSON> nebo n<PERSON>, matic nebo od<PERSON><PERSON><PERSON> obs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pro kter<PERSON> chcete zjistit zešikmení"}, "SLOPE": {"a": "(pole_y; pole_x)", "d": "Vrátí směrnici lineární regresní čáry pro<PERSON>é zadanými datovými body.", "ad": "je matice nebo oblast buněk závislých číselných datových bodů. <PERSON><PERSON> to být čísla nebo názvy, matice nebo odkazy obsahující čísla.!je množina nezávislých datových bodů. <PERSON><PERSON> to být čísla nebo názvy, matice nebo odkazy obsahující čísla."}, "SMALL": {"a": "(pole; k)", "d": "Vrátí k-tou nejmenší hodnotu v množině dat, například páté nejmenší číslo.", "ad": "je matice nebo oblast číselných dat, pro kterou chcete určit k-tou nejmenší hodnotu.!je pozice hledané hodnoty (počítáno od nejmenší hodnoty) v matici nebo oblasti buněk."}, "STANDARDIZE": {"a": "(x; střed_hodn; sm_odch)", "d": "Vrátí normalizovanou hodnotu z rozdělení určeného střední hodnotou a směrodatnou odchylkou.", "ad": "je hodnota, k<PERSON>u chcete normalizovat.!je aritmetická střední hodnota rozdělení.!je směrodatná odchylka rozdělení, klad<PERSON><PERSON>."}, "STDEV": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne směrodatnou odchylku výběru (přeskočí logické hodnoty a text ve výběru).", "ad": "je 1 až 255 čísel nebo od<PERSON><PERSON><PERSON> obs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> odpovídají výběru ze základního souboru."}, "STDEV.P": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočte směrodatnou odchylku základ<PERSON>, k<PERSON><PERSON> byl zadán jako argument (přeskočí logické hodnoty a text).", "ad": "je 1 až 255 čísel nebo od<PERSON><PERSON><PERSON> obs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> odpovídají základn<PERSON><PERSON> souboru."}, "STDEV.S": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne směrodatnou odchylku výběru (přeskočí logické hodnoty a text ve výběru).", "ad": "je 1 až 255 čísel nebo od<PERSON><PERSON><PERSON> obs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> odpovídají výběru ze základního souboru."}, "STDEVA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Odhadne směrodatnou odchylku výběru. Nepřeskočí logické hodnoty a text. Text a logická hodnota NEPRAVDA mají hodnotu 0, logická hodnota PRAVDA má hodnotu 1.", "ad": "je 1 až 255 hodnot odpovídajících výběru ze základního souboru. <PERSON><PERSON> to b<PERSON><PERSON> hodnoty, n<PERSON><PERSON><PERSON> nebo od<PERSON>, <PERSON><PERSON><PERSON> obs<PERSON> hodnoty."}, "STDEVP": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočte směrodatnou odchylku základ<PERSON>, k<PERSON><PERSON> byl zadán jako argument (přeskočí logické hodnoty a text).", "ad": "je 1 až 255 čísel nebo od<PERSON><PERSON><PERSON> obs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> odpovídají základn<PERSON><PERSON> souboru."}, "STDEVPA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vypočte směrodatnou odchylku základního souboru. Nepřeskočí logické hodnoty a text. Text a logická hodnota NEPRAVDA mají hodnotu 0, logická hodnota PRAVDA má hodnotu 1.", "ad": "je 1 až 255 hodnot odpovídajících základnímu so<PERSON>. <PERSON><PERSON> to b<PERSON><PERSON> hodnot<PERSON>, <PERSON><PERSON><PERSON><PERSON>, matice nebo od<PERSON>, <PERSON><PERSON><PERSON> obsahu<PERSON> hodnoty."}, "STEYX": {"a": "(pole_y; pole_x)", "d": "Vrátí standardní chybu předpovězené hodnoty y pro každou hodnotu x v regresi.", "ad": "je matice nebo oblast závislých datových bodů. <PERSON><PERSON> to být čísla nebo názvy, matice nebo odkazy obsahující čísla.!je matice nebo oblast nezávislých datových bodů. <PERSON><PERSON> to být čísla nebo názvy, matice nebo odkazy obsahující čísla."}, "TDIST": {"a": "(x; volnost; chvosty)", "d": "Vrá<PERSON>í hodnotu Studentova t-rozdělení.", "ad": "je č<PERSON><PERSON><PERSON> hodnota, pro kterou chcete zjistit rozdělení.!je celé číslo představující počet stupňů volnosti, které určují rozdělení.!určuje počet chvostů rozdělení, které chcete vrátit: jeden chvost rozdělení = 1, dva chvosty rozdělení = 2."}, "TINV": {"a": "(pravděpodobnost; volnost)", "d": "Vrátí in<PERSON><PERSON><PERSON><PERSON> (dva chvosty) ke Studentovu t-rozdělení.", "ad": "je pravděpodobnost Studentova t-rozdělení (dva chvosty). <PERSON><PERSON><PERSON><PERSON> to být číslo mezi 0 a 1 (včetně).!je kladné celé číslo představující počet stupňů volnosti, k<PERSON><PERSON> určují rozdělení."}, "T.DIST": {"a": "(x; volnost; kumulativní)", "d": "Vrá<PERSON><PERSON> hodnotu levostranného <PERSON>ova t-rozdělení.", "ad": "je č<PERSON><PERSON><PERSON> hodnota, pro kterou chcete zjistit hodnotu rozdělení.!je celé č<PERSON>lo představující počet stupňů volnosti, které určují rozdělení.!je logická hodnota: kumulativní distribuční funkce = PRAVDA, funkce hustoty pravděpodobnosti = NEPRAVDA."}, "T.DIST.2T": {"a": "(x; volnost)", "d": "Vrátí hodnotu oboustranného <PERSON>ova t-rozdělení.", "ad": "je č<PERSON><PERSON><PERSON> hodnota, pro kterou chcete zjistit hodnotu rozdělení.!je celé číslo představující počet stup<PERSON>, kter<PERSON> určují rozdělení."}, "T.DIST.RT": {"a": "(x; volnost)", "d": "Vrátí hodnotu pravostranného Studentova t-rozdělení.", "ad": "je č<PERSON><PERSON><PERSON> hodnota, pro kterou chcete zjistit hodnotu rozdělení.!je celé číslo představující počet stup<PERSON>, kter<PERSON> určují rozdělení."}, "T.INV": {"a": "(pravděpodobnost; volnost)", "d": "Vrátí le<PERSON>trannou inverzní funkci k distribuční funkci Studentova t-rozdělení.", "ad": "je pravděpodobnost oboustranného Studentova t-rozdělení. M<PERSON><PERSON><PERSON> to být číslo mezi 0 a 1 včetně.!je kladné celé číslo představující počet stupňů volnosti, kter<PERSON> určují rozdělení."}, "T.INV.2T": {"a": "(pravděpodobnost; volnost)", "d": "Vrátí oboustrannou inverzní funkci k distribuční funkci Studentova t-rozdělení.", "ad": "je pravděpodobnost oboustranného Studentova t-rozdělení. M<PERSON><PERSON><PERSON> to být číslo mezi 0 a 1 včetně.!je kladné celé číslo představující počet stupňů volnosti, kter<PERSON> určují rozdělení."}, "T.TEST": {"a": "(matice1; matice2; chvosty; typ)", "d": "Vrátí pravděpodobnost odpovídající Studentovu t-testu.", "ad": "je první množina dat.!je druhá množina dat.!určuje počet chvostů rozdělení, kter<PERSON> chcete vrátit: jeden chvost rozdělení = 1, dva chvosty rozdělení = 2.!je typ t-testu: sp<PERSON><PERSON><PERSON><PERSON> vý<PERSON>ě<PERSON> = 1, dva výběry se shodným rozptylem = 2, dva výběry s různým rozptylem = 3."}, "TREND": {"a": "(pole_y; [pole_x]; [nová_x]; [b])", "d": "Vrátí hodnoty lineárního trendu odpovídajícího známým datovým bodům pomocí metody nejmenších čtver<PERSON>ů.", "ad": "je oblast nebo matice hodnot y určených z rovnice y = mx + b.!je volitelná oblast nebo matice hodnot x určených z rovnice y = mx + b. Matice musí být stejného typu jako matice argumentu Pole_y.!je oblast nebo matice nových hodnot x, pro které chcete zjistit odpovídající hodnoty y pomocí funkce LINTREND.!je logická hodnota: konstanta b je vypočítána, pokud je argument b = PRAVDA nebo vynechán, konstanta b je rovna 0, pokud je argument b = NEPRAVDA."}, "TRIMMEAN": {"a": "(pole; procenta)", "d": "Vrátí průměrnou hodnotu vnitřní části množiny datových hodnot.", "ad": "je oblast nebo matice hodnot, kterou chcete oříznout a pro kterou chcete vypočítat průměr zbývajících hodnot.!je zlomek udávající počet datových bodů, které chcete vyloučit z horní a dolní části množiny dat."}, "TTEST": {"a": "(matice1; matice2; chvosty; typ)", "d": "Vrátí pravděpodobnost odpovídající Studentovu t-testu.", "ad": "je první množina dat.!je druhá množina dat.!určuje počet chvostů rozdělení, kter<PERSON> chcete vrátit: jeden chvost rozdělení = 1, dva chvosty rozdělení = 2.!je typ t-testu: sp<PERSON><PERSON><PERSON><PERSON> vý<PERSON>ě<PERSON> = 1, dva výběry se shodným rozptylem = 2, dva výběry s různým rozptylem = 3."}, "VAR": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne rozptyl výběru (přeskočí logické hodnoty a text ve výběru).", "ad": "je 1 až 255 číselných argumentů odpovídajících výběru ze základního souboru."}, "VAR.P": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočte rozptyl celého základního souboru (přeskočí logické hodnoty a text v základním souboru).", "ad": "je 1 až 255 číselných argumentů odpovídajících základnímu soub<PERSON>u."}, "VAR.S": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne rozptyl výběru (přeskočí logické hodnoty a text ve výběru).", "ad": "je 1 až 255 numerických argumentů odpovídajících výběru ze základního souboru."}, "VARA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Odhadne rozptyl výběru. Nepřeskočí logické hodnoty a text. Text a logická hodnota NEPRAVDA mají hodnotu 0, logická hodnota PRAVDA má hodnotu 1.", "ad": "je 1 až 255 hodnot argumentů odpovídajících výběru ze základního souboru."}, "VARP": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočte rozptyl základního souboru (přeskočí logické hodnoty a text v základním souboru).", "ad": "je 1 až 255 číselných argumentů odpovídajících základnímu soub<PERSON>u."}, "VARPA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vypočte rozptyl základního souboru. Nepřeskočí logické hodnoty a text. Text a logická hodnota NEPRAVDA mají hodnotu 0, logická hodnota PRAVDA má hodnotu 1.", "ad": "je 1 až 255 hodnot argumentů odpovídajících základnímu so<PERSON>."}, "WEIBULL": {"a": "(x; alfa; beta; kumulativní)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> hodnotu <PERSON>ova rozdělení.", "ad": "je hodn<PERSON> (nezá<PERSON><PERSON><PERSON>), pro kterou chcete zjistit rozdělení.!je parametr rozdělení, kladn<PERSON> č<PERSON>.!je parametr rozdělení, kladn<PERSON> č<PERSON>.!je logická hodnota: kumulativní distribuční funkce = PRAVDA, hromadná pravděpodobnostní funkce = NEPRAVDA."}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kumulativní)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> hodnotu <PERSON>ova rozdělení.", "ad": "je hodn<PERSON> (nezá<PERSON><PERSON><PERSON>), pro kterou chcete zjistit rozdělení.!je parametr rozdělení, kladn<PERSON> č<PERSON>.!je parametr rozdělení, kladn<PERSON> č<PERSON>.!je logická hodnota: kumulativní distribuční funkce = PRAVDA, hromadná pravděpodobnostní funkce = NEPRAVDA."}, "Z.TEST": {"a": "(matice; x; [sigma])", "d": "Vrátí P<PERSON>hodnotu (jeden ch<PERSON>t) z-testu.", "ad": "je matice nebo oblast dat, kterou chcete použít k testování argumentu x.!je testovaná hodnota.!je známá směrodatná odchylka základního souboru. Jest<PERSON>že tento argument nezadáte, bude použita směrodatná odchylka výběru."}, "ZTEST": {"a": "(matice; x; [sigma])", "d": "Vrátí P<PERSON>hodnotu (jeden ch<PERSON>t) z-testu.", "ad": "je matice nebo oblast dat, kterou chcete použít k testování argumentu x.!je testovaná hodnota.!je známá směrodatná odchylka základního souboru. Jest<PERSON>že tento argument nezadáte, bude použita směrodatná odchylka výběru."}, "ACCRINT": {"a": "(emise; prvn<PERSON>_<PERSON>; vypořádání; sazba; nom_hodnota; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON>na]; [metoda_výpočtu])", "d": "Vrátí nahromaděný úrok z cenného papíru, ze kterého je úrok placen pravidelně.", "ad": "je datum emise cenného papíru vyjádřené pořadovým číslem.!je datum prvního úroku cenného papíru vyjádřené pořadovým číslem.!představuje datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je roční kupónová sazba cenného papíru.!je nominální hodnota cenného papíru.!je počet kupónových plateb za rok.!je typ výpočtu určující počet dnů v měsíci, který chcete použít.!představuje logickou hodnotu: pro nahromaděný úrok od data emise = PRAVDA nebo vynechat; pro výpočet od posledního data kupónové platby = NEPRAVDA."}, "ACCRINTM": {"a": "(emise; vypořádání; sazba; nom_hodnota; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí nahromaděný úrok z cenného papíru, ze kterého je úrok placen k datu splatnosti.", "ad": "je datum emise cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je roční kupónová sazba cenného papíru.!je nominální hodnota cenného papíru.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "AMORDEGRC": {"a": "(nák<PERSON>; nákup; prvn<PERSON>_období; z<PERSON><PERSON><PERSON>; obdo<PERSON><PERSON>; sazba; [zák<PERSON>na])", "d": "Vrátí kalk<PERSON>čně rozvržený lineární odpis aktiva pro každé účetní období.", "ad": "je pořizovací cena aktiva.!je datum pořízení aktiva.!je datum konce prvního období.!je zůstatková hodnota na konci životnosti aktiva.!je období.!je odpisová sazba.!Typ úročení: 0 = 360 dnů, 1 = skutečný stav, 3 = 365 dnů."}, "AMORLINC": {"a": "(nák<PERSON>; nákup; prvn<PERSON>_období; z<PERSON><PERSON><PERSON>; obdo<PERSON><PERSON>; sazba; [zák<PERSON>na])", "d": "Vrátí kalk<PERSON>čně rozvržený lineární odpis aktiva pro každé účetní období.", "ad": "je pořizovací cena aktiva.!je datum pořízení aktiva.!je datum konce prvního období.!je zůstatková hodnota na konci životnosti aktiva.!je období.!je odpisová sazba.!Typ úročení: 0 = 360 dnů, 1 = skutečný stav, 3 = 365 dnů."}, "COUPDAYBS": {"a": "(vypořádání; splatnost; po<PERSON>et_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí počet dnů od začátku období placení kupónů do data splatnosti.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "COUPDAYS": {"a": "(vypořádání; splatnost; po<PERSON>et_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí počet dnů v období placení kupónů, které obsahuje den zúčtování.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "COUPDAYSNC": {"a": "(vypořádání; splatnost; po<PERSON>et_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí počet dnů od data zúčtování do následujícího data placení kupónu.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "COUPNCD": {"a": "(vypořádání; splatnost; po<PERSON>et_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí následující datum placení kupónu po datu zúčtování.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "COUPNUM": {"a": "(vypořádání; splatnost; po<PERSON>et_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí počet kupónů splatných mezi datem zúčtování a datem splatnosti.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "COUPPCD": {"a": "(vypořádání; splatnost; po<PERSON>et_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí předchozí datum placení kupónu před datem zúčtování.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "CUMIPMT": {"a": "(<PERSON><PERSON>; obdob<PERSON>; p<PERSON>jč<PERSON>; začátek; konec; typ)", "d": "Vrátí kumulativní úrok splacený mezi dvěma obdobími.", "ad": "je ú<PERSON> sazba.!je celkový počet platebních období.!je současná hodnota.!je počáteční období ve výpočtu.!je koncové období ve výpočtu.!je časování plateb."}, "CUMPRINC": {"a": "(<PERSON><PERSON>; obdob<PERSON>; p<PERSON>jč<PERSON>; začátek; konec; typ)", "d": "Vrátí kumulativní jistinu splacenou mezi dvěma období<PERSON>.", "ad": "je ú<PERSON> sazba.!je celkový počet platebních období.!je současná hodnota.!je počáteční období ve výpočtu.!je koncové období ve výpočtu.!je časování plateb."}, "DB": {"a": "(n<PERSON><PERSON><PERSON>; zůstatek; životnost; období; [mě<PERSON><PERSON><PERSON>])", "d": "Vypočítá odpis aktiva za určité období pomocí degresivní metody odpisu s pevným zůstatkem.", "ad": "je pořizovací cena aktiva.!je zůstatková cena na konci období životnosti aktiva.!je počet období, po které je aktivum odepisováno (někdy se nazývá životnost aktiva).!je ob<PERSON><PERSON><PERSON>, za které chcete vypočítat odpis. Argument Období musí být ve stejných jednotkách jako argument Životnost.!je počet měsíců v prvním roce odepisování. Jestliže argument <PERSON>ěs<PERSON><PERSON> vynechá<PERSON>, bude jeho hodnota 12."}, "DDB": {"a": "(n<PERSON><PERSON><PERSON>; zůstatek; životnost; období; [faktor])", "d": "Vypočítá odpis aktiva za určité období pomocí dvojité degresivní metody odpisu nebo jiné metody, k<PERSON><PERSON>.", "ad": "je pořizovací cena aktiva.!je zůstatková cena na konci období životnosti aktiva.!je počet obdo<PERSON>í, po které je aktivum odepisováno (někdy se nazývá životnost aktiva).!je ob<PERSON><PERSON><PERSON>, za které chcete vypočítat odpis. Argument Období musí být ve stejných jednotkách jako argument Životnost.!je míra poklesu zůstatku. Jest<PERSON>že argument <PERSON><PERSON><PERSON> v<PERSON>, bude jeho hodnota 2 (dvojitá degresivní metoda odpisu)."}, "DISC": {"a": "(vypoř<PERSON>d<PERSON><PERSON>; splatnost; cena; zaru<PERSON>_cena; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí diskontní sazbu cenného papíru.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je cena cenného papíru o nominální hodnotě 100 Kč.!je výkupní hodnota cenného papíru o nominální hodnotě 100 Kč.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "DOLLARDE": {"a": "(zlomková_koruna; zlomek)", "d": "Převede částku v korunách vyjádřenou zlomkem na částku v korunách vyjádřenou desetinným číslem.", "ad": "je č<PERSON>lo vyjádřené zlomkem.!je celé č<PERSON>lo, které chcete použít jako jmenovatel zlomku."}, "DOLLARFR": {"a": "(desetinná_koruna; zlomek)", "d": "Převede částku v korunách vyjádřenou desetinným číslem na částku v korunách vyjádřenou zlomkem.", "ad": "je desetinné č<PERSON>lo.!je celé č<PERSON>, které ch<PERSON>te p<PERSON>t jako j<PERSON>tel zlomku."}, "DURATION": {"a": "(vypořádání; splatnost; kupón; výnos; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON>na])", "d": "Vrátí roční dobu cenného papíru s pravidelnými úrokovými sazbami.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je roční kupónová sazba cenného papíru.!je roční výnos cenného papíru.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "EFFECT": {"a": "(<PERSON><PERSON>; období)", "d": "Vrátí efektivní roční úrokovou sazbu.", "ad": "je nominální úroková sazba!je počet úročených období za rok"}, "FV": {"a": "(sazba; pper; splátka; [souč_hod]; [typ])", "d": "Vrátí budoucí hodnotu investice vypočtenou na základě pravidelných konstantních splátek a konstantní úrokové sazby.", "ad": "je úroková sazba vztažená na jedno období. Chcete-li například zadat čtvrtletní splátky s 6% ro<PERSON><PERSON><PERSON>rokovo<PERSON> mírou, použ<PERSON><PERSON><PERSON> 6%/4.!je celkový počet platebních období investice.!je platba provedená v každém období. Po dobu životnosti investice ji nelze měnit.!je současná hodnota nebo celková částka určující současnou hodnotu série budoucích plateb. Jestliže argument souč_hod nezadáte, bude jeho hodnota rovna 0.!je hodnota, která představuje termín splátky: splátka na začátku období = 1, splátka na konci období = 0 nebo bez zadání."}, "FVSCHEDULE": {"a": "(hodnota; sazby)", "d": "Vrátí budo<PERSON>í hodnotu počáteční jistiny po použití série sazeb složitého <PERSON>.", "ad": "je současná hodnota.!řada úrokových sazeb, k<PERSON><PERSON> p<PERSON>."}, "INTRATE": {"a": "(vypořádání; splatnost; investice; zaru<PERSON>_cena; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí <PERSON> sazbu plně investovaného cenného papíru.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je částka investovaná do cenného papíru.!je <PERSON><PERSON><PERSON><PERSON>, kterou obdržíte k datu splatnosti.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "IPMT": {"a": "(sazba; za; pper; souč_hod; [bud_hod]; [typ])", "d": "Vrátí výšku úroku v určitém úrokovém období vypočtenou na základě pravidelných konstantních splátek a konstantní úrokové sazby.", "ad": "je <PERSON><PERSON>ová sazba vztažená na jedno období. Chcete-li například zadat čtvrtletní splátky realizované 6% APR, použijte 6%/4.!je o<PERSON><PERSON><PERSON><PERSON>, pro které chcete vypočítat úrok. Tato hodnota musí ležet v intervalu od 1 do Pper.!je celkový počet platebních období investice.!je současná hodnota nebo celková částka určující současnou hodnotu série budoucích plateb.!je budoucí hodnota nebo hotovostní bilance, kterou chcete dosáhnout po splacení poslední platby. Jest<PERSON>že argument Bud_hod nezadáte, bude jeho hodnota 0.!je logick<PERSON> hodnota, kter<PERSON> představuje termín splátky: splátka na konci období = 0 nebo bez zadání, splátka na začátku období = 1"}, "IRR": {"a": "(hodnoty; [odhad])", "d": "Vrátí vnitřní výnosové procento série peněžních to<PERSON>ů.", "ad": "je matice nebo odkaz na buňky obsahu<PERSON><PERSON><PERSON><PERSON>, pro které chcete vypočítat vnitřní výnosové procento.!je č<PERSON>lo, které představuje odhad blízký výsledku funkce MÍRA VÝNOSNOSTI. Jest<PERSON>že tento argument nezad<PERSON>te, bude mít hodnotu 0,1 (10 procent)."}, "ISPMT": {"a": "(sazba; za; pper; souč)", "d": "Vrátí výšku úroku zaplaceného za určité období investice.", "ad": "je úroková sazba vztažená na jedno období. Chcete-li například zadat čtvrtletní splátky realizované 6% APR, použijte 6%/4.!je o<PERSON><PERSON><PERSON><PERSON>, pro které chcete zjistit výšku úroku.!je počet platebních období investice.!je celková částka určující současnou hodnotu série budoucích plateb."}, "MDURATION": {"a": "(vypořádání; splatnost; kupón; výnos; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON>na])", "d": "Vrátí <PERSON>ho modifikovanou dobu cenného papíru o nominální hodnotě 100 Kč.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je roční kupónová sazba cenného papíru.!je roční výnos cenného papíru.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "MIRR": {"a": "(hodnoty; finance; investice)", "d": "Vrátí vnitřní sazbu výnosu pravidelných peněžních příjmů. Zohledňuje jak náklady na investice, tak úrok z reinvestic získaných peněžních prostředků.", "ad": "je matice nebo odkaz na buňky obsahuj<PERSON><PERSON><PERSON>, která představují splátky (záporná hodnota) a příjmy (kladná hodnota) v pravidelných obdobích.!je ú<PERSON> sazba, kterou zaplatíte za použití peněžních příjmů.!je úroková sazba, kterou získáte z reinvestic peněžních příjmů."}, "NOMINAL": {"a": "(<PERSON><PERSON>; období)", "d": "Vrátí nominální roční úrokovou sazbu.", "ad": "je efektivní úroková sazba.!je počet úročených období za rok."}, "NPER": {"a": "(sazba; splátka; souč_hod; [bud_hod]; [typ])", "d": "Vrátí počet období pro investici vypočítaný na základě pravidelných konstantních splátek a konstantní úrokové sazby. Chcete-li například zadat čtvrtletní splátky realizované 6. <PERSON><PERSON>, pou<PERSON><PERSON><PERSON><PERSON> 6%/4.", "ad": "je úroková sazba vztažená na jedno období.!je platba provedená v každém období. Během období životnosti investice ji nelze změnit.!je současná hodnota nebo celková částka určující současnou hodnotu série budoucích plateb.!je budoucí hodnota nebo hotovostní bilance, kterou chcete dosáhnout po splacení poslední platby. Jestliže argument nezadáte, bude jeho hodnota 0.!je logická hodnota: splátka na konci období = 0 nebo bez zadání, splátka na začátku období = 1."}, "NPV": {"a": "(sazba; hodnota1; [hodnota2]; ...)", "d": "Vrátí čistou současnou hodnotu investice vypočítanou na základě diskontní sazby a série budouc<PERSON>ch plateb (záporné hodnoty) a příjmů (kladné hodnoty).", "ad": "je diskontní sazba vztažená na délku jednoho období.!je 1 až 254 plateb a příjmů rovnoměrně rozdělených v čase a vyskytujících se na konci každého období."}, "ODDFPRICE": {"a": "(vypoř<PERSON><PERSON><PERSON><PERSON>; splatnost; emise; prvn<PERSON>_<PERSON>rok; sazba; výnos; zaru<PERSON>_cena; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí cenu cenného papíru o nominální hodnotě 100 Kč s odlišným prvním obdobím.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je datum emise cenného papíru vyjádřené pořadovým číslem.!je datum první platby kupónu cenného papíru vyjádřené pořadovým číslem.!je úroková sazba cenného papíru.!je roční výnos cenného papíru.!je výkupní hodnota cenného papíru o nominální hodnotě 100 Kč.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "ODDFYIELD": {"a": "(vypoř<PERSON><PERSON><PERSON><PERSON>; splatnost; emise; prvn<PERSON>_úrok; sazba; cena; zaru<PERSON>_cena; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí výnos cenného papíru s odlišným prvním obdobím.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je datum emise cenného papíru vyjádřené pořadovým číslem.!je datum první platby kupónu cenného papíru vyjádřené pořadovým číslem.!je úroková sazba cenného papíru.!je cena cenného papíru.!je výkupní hodnota cenného papíru o nominální hodnotě 100 Kč.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "ODDLPRICE": {"a": "(vypoř<PERSON>dán<PERSON>; splatnost; posledn<PERSON>_úrok; sazba; výnos; zaru<PERSON>_cena; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí cenu cenného papíru o nominální hodnotě 100 Kč s odlišným posledním obdobím.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je datum poslední kupónové platby vyjádřené pořadovým číslem.!je úroková sazba cenného papíru.!je roční výnos cenného papíru.!je výkupní hodnota cenného papíru o nominální hodnotě 100 Kč.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "ODDLYIELD": {"a": "(vypoř<PERSON>dán<PERSON>; splatnost; posledn<PERSON>_úrok; sazba; cena; zaru<PERSON>_cena; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí výnos cenného papíru s odlišným posledním obdobím.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je datum poslední kupónové platby vyjádřené pořadovým číslem.!je úroková sazba cenného papíru.!je cena cenného papíru.!je výkupní hodnota cenného papíru o nominální hodnotě 100 Kč.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "PDURATION": {"a": "(sazba; souč_hod; bud_hod)", "d": "Vrátí počet období požadovaných investicí k tomu, aby <PERSON><PERSON><PERSON>a zadané hodnoty", "ad": "je <PERSON>roková sazba za období.!je současná hodnota investice!je požadovaná budoucí hodnota investice"}, "PMT": {"a": "(sazba; pper; souč_hod; [bud_hod]; [typ])", "d": "Vypočte splátku půjčky na základě konstantních splátek a konstantní úrokové sazby.", "ad": "je úroková sazba půjčky vztažená na jedno období. Chcete-li například zadat čtvrtletní splátky realizované 6% APR, použijte 6%/4.!je celkový počet splátek půjčky.!je současná hodnota: celková hodnota série budoucích plateb.! je budoucí hodnota nebo hotovostní bilance, kterou chcete dosáhnout po splacení poslední platby. Jestliže tento argument nezadáte, bude jeho hodnota 0 (nula).!je logická hodnota: splátka na konci období = 0 nebo bez zadání, splátka na začátku období = 1."}, "PPMT": {"a": "(sazba; za; pper; souč_hod; [bud_hod]; [typ])", "d": "Vrátí hodnotu splátky jistiny pro zadanou investici vypočtenou na základě pravidelných konstantních splátek a konstantní úrokové sazby.", "ad": "je úroková sazba vztažená na jedno období. Chcete-li například zadat čtvrtletní splátky realizované 6% APR, použijte 6%/4.!ur<PERSON><PERSON><PERSON> období. Musí nabývat hodnot od 1 do Pper.!je celkový počet platebních období investice.!je současná hodnota: celková hodnota série budoucích plateb.!je budoucí hodnota nebo hotovostní bilance, kterou chcete dosáhnout po splacení poslední platby.!je logická hodnota: splátka na konci období = 0 nebo bez zadání, splátka na začátku období = 1"}, "PRICE": {"a": "(vypoř<PERSON>dán<PERSON>; splatnost; sazba; výnos; zaru<PERSON>_cena; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí cenu cenného papíru o nominální hodnotě 100 Kč, ze kterého je úrok placen v pravidelných termínech.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je roční kupónová sazba cenného papíru.!je roční výnos cenného papíru.!je výkupní hodnota cenného papíru o nominální hodnotě 100 Kč.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "PRICEDISC": {"a": "(vypoř<PERSON>dán<PERSON>; splatnost; diskont_sazba; zaru<PERSON>_cena; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí cenu diskontního cenného papíru o nominální hodnotě 100 Kč.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je diskontní sazba cenného papíru.!je výkupní hodnota cenného papíru o nominální hodnotě 100 Kč.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "PRICEMAT": {"a": "(vypoř<PERSON><PERSON><PERSON><PERSON>; splatnost; emise; sazba; výnos; [z<PERSON><PERSON>na])", "d": "Vrátí cenu cenného papíru o nominální hodnotě 100 Kč, ze kterého je úrok placen k datu splatnosti.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je datum emise cenného papíru vyjádřené pořadovým číslem.!je úroková sazba cenného papíru k datu emise.!je roční výnos cenného papíru.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "PV": {"a": "(sazba; pper; spl<PERSON>tka; [bud_hod]; [typ])", "d": "Vrátí současnou hodnotu investice: celkovou hodnotu série budoucích plateb.", "ad": "je úroková sazba vztažená na jedno období. Chcete-li například zadat čtvrtletní splátky realizované 6% APR, použijte 6%/4.!je celkový počet platebních období investice.!je splátka provedená v každém období. Během období životnosti investice ji nelze měnit.!je budoucí hodnota nebo hotovostní bilance, kterou chcete dosáhnout po splacení poslední platby.!je logická hodnota: splátka na konci období = 0 nebo bez zadání, splátka na začátku období = 1."}, "RATE": {"a": "(pper; spl<PERSON>tka; souč_hod; [bud_hod]; [typ]; [odhad])", "d": "Vrátí úrokovou sazbu vztaženou na období půjčky nebo investice. Chcete-li například zadat čtvrtletní splátky realizované 6. <PERSON><PERSON>, pou<PERSON><PERSON><PERSON><PERSON> 6%/4.", "ad": "je celkový počet platebních období půjčky nebo investice.!je splátka provedená v každém období. Během období životnosti půjčky nebo investice ji nelze měnit.!je současná hodnota: celková hodnota série budoucích plateb.!je budoucí hodnota nebo hotovostní bilance, kterou chcete dosáhnout po splacení poslední platby. Jestliže argument Bud_hod nezadáte, bude jeho hodnota 0.!je logická hodnota: splátka na začátku období = 1; splátka na konci období = 0 nebo bez zadání.!je předpokládaný odhad sazby. Jestliže argument Odhad nezadáte, bude jeho hodnota 0,1 (10 procent)."}, "RECEIVED": {"a": "(vypořádání; splatnost; investice; diskont_sazba; [z<PERSON><PERSON>na])", "d": "Vrátí částku obdrženou k datu splatnosti plně investovaného cenného papíru.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je částka investovaná do cenného papíru.!je diskontní sazba cenného papíru.!je typ výpočtu určující počet dnů v měsíci, kter<PERSON> chcete použít."}, "RRI": {"a": "(nper; souč_hod; bud_hod)", "d": "Vrátí odpovídající úrokovou sazbu pro růst investic", "ad": "je počet období pro investici!je současná hodnota investice!je budoucí hodnota investice"}, "SLN": {"a": "(náklady; zůstatek; životnost)", "d": "Vrátí přímé odpisy aktiva pro jedno období.", "ad": "je pořizovací cena aktiva.!je zůstatková cena na konci období životnosti aktiva.!je počet období, ve kterých je aktivum odepisováno (někdy se nazývá životnost aktiva)."}, "SYD": {"a": "(náklady; zůstatek; životnost; za)", "d": "Vrátí směrné číslo ročních odpisů aktiva pro zadané období.", "ad": "je pořizovací cena aktiva.!je zůstatková cena na konci období životnosti aktiva.!je počet obdo<PERSON>í, ve kterých je aktivum odepisováno (někdy se nazývá životnost aktiva).!je ob<PERSON><PERSON><PERSON>, které musí být vyjád<PERSON><PERSON> ve stejných jednotkách jako argument Životnost."}, "TBILLEQ": {"a": "(vypořádání; splatnost; diskont_sazba)", "d": "Vrátí výnos směnky státní pokladny ekvivalentní výnosu obligace.", "ad": "je datum zúčtování směnky státní pokladny vyjádřené pořadovým číslem.!je datum splatnosti směnky státní pokladny vyjádřené pořadovým číslem.!je diskontní sazba směnky státní pokladny."}, "TBILLPRICE": {"a": "(vypořádání; splatnost; diskont_sazba)", "d": "Vrátí cenu směnky státní pokladny o nominální hodnotě 100 Kč.", "ad": "je datum zúčtování směnky státní pokladny vyjádřené pořadovým číslem.!je datum splatnosti směnky státní pokladny vyjádřené pořadovým číslem.!je diskontní sazba směnky státní pokladny."}, "TBILLYIELD": {"a": "(vypořádání; splatnost; cena)", "d": "Vrátí výnos směnky státní pokladny.", "ad": "je datum zúčtování směnky státní pokladny vyjádřené pořadovým číslem.!je datum splatnosti směnky státní pokladny vyjádřené pořadovým číslem.!je cena směnky státní pokladny o nominální hodnotě 100 Kč."}, "VDB": {"a": "(cena; zůstatek; životnost; začátek; konec; [faktor]; [nep<PERSON><PERSON><PERSON><PERSON>])", "d": "Vypočte odpisy aktiva pro každé zadané obdo<PERSON>í, vč<PERSON>ně neukončených období, pomocí dvojité degresivní metody odpisu nebo jiné metody, k<PERSON><PERSON>.", "ad": "je pořizovací cena aktiva.!je zbytková hodnota aktiva na konci období životnosti aktiva.!je počet obdo<PERSON>í, ve kterých jsou aktiva odepisována (někdy se nazývá životnost aktiva).!je poč<PERSON>te<PERSON>í o<PERSON>, pro které chcete vypočítat odpisy, ve stejn<PERSON>ch jednotkách jako argument Životnost.!je konečné obdo<PERSON>í, po které chcete vypočítat odpisy, ve stejných jednotkách jako argument Životnost.!je míra poklesu zůstatku. Jestliže tento argument nezadáte, bude mít hodnotu 2 (dvojitá degresivní metoda odpisu).!je logická hodnota: přej<PERSON><PERSON> na přímé odpisy, pokud je hodnota odpisu větší než klesající zůstatek = NEPRAVDA nebo bez zadání, nepřecházet na přímé odpisy = PRAVDA."}, "XIRR": {"a": "(hodnoty; data; [odhad])", "d": "Vrátí vnitřní výnosnost pro harmonogram pen<PERSON>ž<PERSON><PERSON><PERSON> tok<PERSON>.", "ad": "je posloupnost peněžních toků odpovídajících harmonogramu dat plateb.!je harmonogram dat plateb odpovídající platbám peněžních toků. !je odhad výsledku funkce XIRR."}, "XNPV": {"a": "(sazba; hodnoty; data)", "d": "Vrátí <PERSON> současnou hodnotu pro harmonogram pen<PERSON><PERSON><PERSON><PERSON><PERSON> tok<PERSON>.", "ad": "je diskontní sazba pro peněžní toky.!je posloupnost peněžních toků odpovídajících harmonogramu dat plateb.!je harmonogram plateb odpovídající platbám peněžních toků."}, "YIELD": {"a": "(vypořádání; splatnost; sazba; cena; zaru<PERSON>_cena; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí výnos cenného papíru, ze kterého je úrok placen v pravidelných termínech.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je roční kupónová sazba cenného papíru!je cena cenného papíru o nominální hodnotě 100 Kč.!je výkupní hodnota cenného papíru o nominální hodnotě 100 Kč.!je počet kupónových plateb v roce.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "YIELDDISC": {"a": "(vypoř<PERSON>d<PERSON><PERSON>; splatnost; cena; zaru<PERSON>_cena; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí roční výnos diskontního cenného p<PERSON>, například směnky státní pokladny.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je cena cenného papíru o nominální hodnotě 100 Kč.!je výkupní hodnota cenného papíru o nominální hodnotě 100 Kč.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "YIELDMAT": {"a": "(vypoř<PERSON>dání; splatnost; emise; sazba; cena; [z<PERSON><PERSON>na])", "d": "Vrátí roční výnos cenného <PERSON>í<PERSON>, ze kterého je úrok placen k datu splatnosti.", "ad": "je datum zúčtování cenného papíru vyjádřené pořadovým číslem.!je datum splatnosti cenného papíru vyjádřené pořadovým číslem.!je datum emise cenného papíru vyjádřené pořadovým číslem.!je úroková sazba cenného papíru k datu emise.!je cena cenného papíru o nominální hodnotě 100 Kč.!je typ výpočtu určující počet dnů v měsíci, který chcete použít."}, "ABS": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí absolutní hodnotu čísla. Výsledek je číslo bez znaménka.", "ad": "je re<PERSON><PERSON><PERSON>, jeh<PERSON><PERSON> absolutní hodnotu chcete zji<PERSON>it."}, "ACOS": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hodnotu arkuskosinu čísla. Výsledek je v radiánech v rozsahu 0 až pí. Arkuskosinus je úhel, jeh<PERSON><PERSON> kosinus je argument Číslo.", "ad": "je hodnota kosinu požado<PERSON>ho <PERSON>, která musí ležet v intervalu -1 až 1."}, "ACOSH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hodnotu hyperbolického arkuskosinu čísla.", "ad": "je libovolné reálné číslo větší nebo rovné 1."}, "ACOT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí arkuskotangens čísla. Výsledek je v radiánech v rozsahu 0 až pí.", "ad": "je kotangens požadovaného <PERSON>"}, "ACOTH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí inverzní hyperbolický kotangens čísla", "ad": "je hyperbolický kotangens úhlu, k<PERSON><PERSON> chcete získat"}, "AGGREGATE": {"a": "(<PERSON><PERSON>; mož<PERSON>ti; odkaz1; ...)", "d": "Vrátí agregaci v seznamu nebo databázi.", "ad": "je číslo od 1 do 19 určující souhrnnou funkci použitou pro agregaci.!je číslo 0 až 7 určuj<PERSON><PERSON><PERSON> hodnoty, které mají být ignorovány pro agregaci!je matice nebo oblast číselných dat, z níž se má vypočítat agregace.!označuje pozici v rámci matice; jedná se o k-tou největší, k-tou nej<PERSON>ší, k-tý percentil nebo k-tý kvartil.!je číslo od 1 do 19 určující souhrnnou funkci použitou pro agregaci.!je číslo 0 až 7 určující hodnoty, které mají být pro agregaci ignorovány!je 1 až 253 oblastí nebo odkazů, pro které chcete vypočítat agregaci."}, "ARABIC": {"a": "(text)", "d": "Převede římskou číslici na arabskou.", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>."}, "ASC": {"a": "(text)", "d": "U jazyků využívajících dvoubajtové znakové sady (DBCS) změní znaky s plnou šířkou (dvoubajtové) na znaky s poloviční šířkou (jednobajtové)", "ad": "je text, k<PERSON><PERSON> chcete změnit."}, "ASIN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí arkussinus čísla. Výsledek je v radiánech v rozsahu -pí/2 až pí/2.", "ad": "je hodnota sinu požadovan<PERSON>ho <PERSON>, která musí ležet v intervalu -1 až 1."}, "ASINH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický arkussinus čísla.", "ad": "je libovolné reálné číslo větší nebo rovné 1."}, "ATAN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí arkustangens čísla. Výsledek je v radiánech v rozsahu -pí/2 až pí/2.", "ad": "je tangens p<PERSON>žadovan<PERSON>."}, "ATAN2": {"a": "(x_č<PERSON>lo; y_č<PERSON>lo)", "d": "Vrátí arkustangens zadané x-ové a y-ové souřadnice. Výsledek je v radiánech v rozsahu -pí až pí kromě hodnoty -pí.", "ad": "je x-ová souřadnice bodu.!je y-ová souřadnice bodu."}, "ATANH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický arkustangens čísla.", "ad": "je libovolné reálné číslo mezi -1 a 1 kromě čísel -1 a 1."}, "BASE": {"a": "(č<PERSON>lo; radix; [min_length])", "d": "Převede číslo na textové vyjádření s danou číselnou základnou.", "ad": "je <PERSON><PERSON><PERSON>, kter<PERSON> chcete převést.!je číselná základna, na kterou chcete číslo převést.!je minimální délka vráceného řetězce. Není-li <PERSON>, nepřidají se počáteční nuly"}, "CEILING": {"a": "(číslo; významnost)", "d": "Zaokrouhlí číslo nahoru na nejbližší násobek zadané hodnoty významnosti.", "ad": "je hodn<PERSON>, k<PERSON><PERSON> chcete zaokrouhlit.!je násobek, na který chcete číslo zaokrouhlit."}, "CEILING.MATH": {"a": "(č<PERSON>lo; [významnost]; [rež<PERSON>])", "d": "Zaokrouhlí číslo nahoru na nejbližší celé číslo nebo na nejbližší násobek zadané hodnoty významnosti", "ad": "je hodnota, k<PERSON><PERSON> chcete zaokrouhlit!je násobek, na který chcete číslo zaokrouhlit!pokud je zadaná a hodnota není rovná nula, bude tato funkce zaokrouhlovat směrem od nuly"}, "CEILING.PRECISE": {"a": "(č<PERSON>lo; [významnost])", "d": "Vrátí číslo zaokrouhlené nahoru na nejbližší celé číslo nebo na nejbližší násobek zadané hodnoty", "ad": "je hodn<PERSON>, k<PERSON><PERSON> chcete zaokrouhlit.!je násobek, na který chcete číslo zaokrouhlit."}, "COMBIN": {"a": "(počet; kombinace)", "d": "Vrátí počet kombinací pro zadaný počet položek.", "ad": "je celkový počet položek.!je počet položek v každé kombinaci."}, "COMBINA": {"a": "(počet; kombinace)", "d": "Vrátí počet kombinací s opakováním pro daný počet položek", "ad": "je celkový počet položek!je počet položek v každé kombinaci"}, "COS": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrá<PERSON><PERSON> k<PERSON>.", "ad": "je úhel v radiánech, jeh<PERSON><PERSON> kosinus chcete určit."}, "COSH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický kosinus <PERSON>.", "ad": "je libovolné reáln<PERSON>."}, "COT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí kotangens úhlu", "ad": "je úhel v radiánech, pro který chcete získat kotangens"}, "COTH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický kotangens čísla", "ad": "je úhel v radiánech, pro který chcete získat hyperbolický kotangens"}, "CSC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON>rá<PERSON><PERSON> k<PERSON>", "ad": "je úhel v radiánech, pro který chcete získat kosekans"}, "CSCH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický kosekans úhlu", "ad": "je úhel v radiánech, pro který chcete získat hyperbolický kosekans"}, "DECIMAL": {"a": "(<PERSON><PERSON><PERSON>; zák<PERSON>)", "d": "Převede textové vyjádření čísla v daném základu na desítkové číslo", "ad": "je <PERSON><PERSON><PERSON>, které chcete přev<PERSON>t!je základ č<PERSON>elné soustav<PERSON>, které přev<PERSON>"}, "DEGREES": {"a": "(<PERSON><PERSON>)", "d": "Převede radiány na stupně.", "ad": "je úhel v radiánech, který chcete převést."}, "ECMA.CEILING": {"a": "(číslo; významnost)", "d": "Zaokrouhlí číslo nahoru na nejbližší násobek zadané hodnoty významnosti", "ad": "je hodn<PERSON>, k<PERSON><PERSON> chcete zaokrouhlit.!je násobek, na který chcete číslo zaokrouhlit."}, "EVEN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Zaokrouhlí kladné číslo nahoru a záporné číslo dolů na nejbližší sudé celé číslo.", "ad": "je <PERSON>, k<PERSON><PERSON>te <PERSON>."}, "EXP": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí základ přirozeného logaritmu umocněný na zadané číslo.", "ad": "je exponent p<PERSON><PERSON><PERSON><PERSON> u základu e. Konstanta e je základ přirozeného logaritmu a je rovna hodnotě 2,71828182845904."}, "FACT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí faktoriál čísla. Výsledek se rovná hodnotě 1*2*3*...*Číslo.", "ad": "je ne<PERSON><PERSON><PERSON><PERSON><PERSON>, jeh<PERSON><PERSON> faktor<PERSON><PERSON>l chcete vypočítat."}, "FACTDOUBLE": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí d<PERSON>ý faktor<PERSON>.", "ad": "je ho<PERSON>, <PERSON><PERSON><PERSON><PERSON> d<PERSON> faktor<PERSON>l chcete vypočítat."}, "FLOOR": {"a": "(číslo; významnost)", "d": "Zaokrouhlí číslo dolů na nejbližší násobek zadané hodnoty významnosti.", "ad": "je č<PERSON><PERSON><PERSON> hodnota, k<PERSON>u chcete zaokrouhlit.!je násobek, na který chcete číslo zaokrouhlit. Argumenty číslo a významnost musí být zároveň buď kladná, nebo záporná čísla."}, "FLOOR.PRECISE": {"a": "(č<PERSON>lo; [významnost])", "d": "Vrátí číslo zaokrouhlené dolů na nejbližší celé číslo nebo na nejbližší násobek zadané hodnoty", "ad": "je hodn<PERSON>, k<PERSON><PERSON> chcete zaokrouhlit.!je násobek, na který chcete číslo zaokrouhlit."}, "FLOOR.MATH": {"a": "(č<PERSON>lo; [významnost]; [rež<PERSON>])", "d": "Zaokrouhlí číslo dolů na nejbližší celé číslo nebo na nejbližší násobek zadané hodnoty významnosti", "ad": "je hodnota, k<PERSON><PERSON> chcete zaokrouhlit!je násobek, na který chcete číslo zaokrouhlit!pokud je zadaná a hodnota není rovná nula, bude tato funkce zaokrouhlovat směrem k nule"}, "GCD": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí nejvě<PERSON>ší s<PERSON>čný dě<PERSON>l.", "ad": "představují 1 až 255 hodnot."}, "INT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Zaokrouhlí číslo dolů na nejbližší celé číslo.", "ad": "je re<PERSON><PERSON><PERSON>, k<PERSON><PERSON> ch<PERSON>te zaokrouhlit dolů na celé číslo."}, "ISO.CEILING": {"a": "(č<PERSON>lo; [významnost])", "d": "Vrátí číslo zaokrouhlené nahoru na nejbližší celé číslo nebo na nejbližší násobek zadané hodnoty. <PERSON><PERSON>lo bude zaokrouhleno nahoru bez ohledu na jeho znaménko. Pokud je však zadáno číslo nula nebo násobek nuly, bude vrácena nula.", "ad": "je hodn<PERSON>, k<PERSON><PERSON> chcete zaokrouhlit.!je násobek, na který chcete číslo zaokrouhlit."}, "LCM": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí nejmenší společný násobek.", "ad": "představují 1 až 255 hodnot, pro které chcete určit nejmenší společný násobek."}, "LN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí přirozený logaritmus čísla.", "ad": "je k<PERSON><PERSON><PERSON>, jeh<PERSON><PERSON> př<PERSON>zený logaritmus chcete získat."}, "LOG": {"a": "(<PERSON><PERSON><PERSON>; [z<PERSON><PERSON>])", "d": "Vrátí logaritmus čísla při zadaném základu.", "ad": "je klad<PERSON><PERSON>, jeho<PERSON> logaritmus chcete získat.!je základ logaritmu. Jestliže tento argument nezad<PERSON>, bude jeho hodnota 10."}, "LOG10": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí de<PERSON>dický logaritmus čísla.", "ad": "je k<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> logaritmus chcete získat."}, "MDETERM": {"a": "(pole)", "d": "Vrátí determinant matice.", "ad": "je číselná matice se stejným počtem řádků a sloupců. <PERSON><PERSON><PERSON><PERSON> to být oblast buněk nebo maticová konstanta."}, "MINVERSE": {"a": "(pole)", "d": "Vrátí inverzní matici k matici, která je uložena v oblasti definované jako matice.", "ad": "je číselná matice se stejným počtem řádků a sloupců. <PERSON><PERSON><PERSON><PERSON> to být oblast buněk nebo maticová konstanta."}, "MMULT": {"a": "(pole1; pole2)", "d": "Vrátí součin dvou matic, matici se stejným počtem řádků jako matice argumentu Pole1 a stejným počtem sloupců jako matice argumentu Pole2.", "ad": "je první matice čísel, k<PERSON>u chcete násobit. Počet sloupců musí být stejný jako počet řádků matice argumentu Pole2."}, "MOD": {"a": "(<PERSON><PERSON><PERSON>; dělitel)", "d": "Vrátí zbytek po dělení čísla.", "ad": "je <PERSON><PERSON><PERSON>, pro které chcete najít zbytek při dělení.!je <PERSON><PERSON><PERSON>, kterým chcete dělit argument Číslo."}, "MROUND": {"a": "(číslo; násobek)", "d": "Vrátí číslo zaokrouhlené na požadovaný násobek.", "ad": "je hodn<PERSON>, k<PERSON><PERSON> chcete zaokrouhlit.!je násobek, na který chcete číslo zaokrouhlit."}, "MULTINOMIAL": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí mnohočlen sady čísel.", "ad": "představují 1 až 255 hodnot, pro které chcete určit mnohočlen."}, "MUNIT": {"a": "(dimenze)", "d": "Vrátí matici jednotek pro zadanou dimenzi", "ad": "je celé <PERSON> určující dimenzi matice jed<PERSON>, k<PERSON><PERSON><PERSON>te vrátit"}, "ODD": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Zaokrouhlí kladné číslo nahoru a záporné číslo dolů na nejbližší liché celé číslo.", "ad": "je hodnota, k<PERSON>u ch<PERSON>te z<PERSON>."}, "PI": {"a": "()", "d": "Vrátí hodnotu čísla pí s přesností na 15 číslic. Výsledek je hodnota 3.14159265358979.", "ad": ""}, "POWER": {"a": "(č<PERSON><PERSON>; exponent)", "d": "Umocní číslo na zadaný exponent.", "ad": "je základ mocnin<PERSON>, k<PERSON><PERSON><PERSON> mů<PERSON>e být libovolné reálné číslo.!je exponent, na který chcete základ umocnit."}, "PRODUCT": {"a": "(číslo1; [číslo2]; ...)", "d": "Vynásobí všechna čísla zadaná jako argumenty.", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON><PERSON> hodnot nebo čísel ve formátu textu, k<PERSON><PERSON> chcete vynásobit."}, "QUOTIENT": {"a": "(numer<PERSON>tor; denominátor)", "d": "Vrá<PERSON><PERSON> celou <PERSON> d<PERSON>.", "ad": "je dělene<PERSON>.!je dělite<PERSON>."}, "RADIANS": {"a": "(<PERSON><PERSON>)", "d": "Převede stupně na radiány.", "ad": "je <PERSON>hel ve stu<PERSON>, k<PERSON><PERSON> p<PERSON>."}, "RAND": {"a": "()", "d": "Vrátí náhodné číslo větší nebo rovné 0 a menší než 1 určené na základě spojité distribuční funkce (změní se při každém přepočítání listu).", "ad": ""}, "RANDARRAY": {"a": "([<PERSON><PERSON><PERSON><PERSON><PERSON>]; [sloup<PERSON><PERSON>]; [min]; [max]; [celé_č<PERSON>lo])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> n<PERSON><PERSON><PERSON><PERSON><PERSON>", "ad": "počet řádků ve vráceném poli!počet sloupců ve vráceném poli!minimální počet čísel, které chcete vrátit!maximální počet čísel, které chcete vrátit!vrátit celé číslo nebo desetinné číslo. PRAVDA pro celé číslo, NEPRAVDA pro desetinné číslo"}, "RANDBETWEEN": {"a": "(dolní; horní)", "d": "Vrátí náhodné číslo mezi zadanými č<PERSON>.", "ad": "je nejmen<PERSON><PERSON> celé <PERSON>, které funkce RANDBETWEEN vrátí.!je největší celé č<PERSON>lo, které funkce RANDBETWEEN vrátí."}, "ROMAN": {"a": "(číslo; [forma])", "d": "Převede číslo nap<PERSON>é pomocí arabských číslic na římské číslice ve formátu textu.", "ad": "je číslo ve formě arabských <PERSON>, kter<PERSON> chcete převést.!je číslo určující typ požadovaných římských číslic."}, "ROUND": {"a": "(číslo; číslice)", "d": "Zaokrouhlí číslo na zadaný počet číslic.", "ad": "je <PERSON><PERSON><PERSON>, které chcete zaokrouhlit.!je počet č<PERSON>, na které chcete požadované číslo zaokrouhlit. Jestliže zadáte záporné č<PERSON>lo, bude zadané číslo zaokrouhleno směrem doleva od desetinné čárky. Pokud je hodnota argumentu nula, bude zadané číslo zaokrouhleno na nejbližší celé číslo."}, "ROUNDDOWN": {"a": "(číslo; číslice)", "d": "Zaokrouhlí číslo dolů směrem k nule.", "ad": "je libovolné reá<PERSON>, které chcete zaokrouhlit dolů.!je počet č<PERSON>, na které chcete číslo zaokrouhlit. Má-li tento argument zápornou hodnotu, bude zadané číslo zao<PERSON>rouhleno směrem doleva od desetinné č<PERSON>. Pokud je roven nule nebo vynechán, bude zadané číslo zaokrouhleno na nejbližší celé číslo."}, "ROUNDUP": {"a": "(číslo; číslice)", "d": "Zaokrouhlí č<PERSON>lo nahoru směrem od nuly.", "ad": "je libovolné reáln<PERSON>, které chcete zaokrouhlit nahoru.!je počet č<PERSON>, na které chcete číslo zaokrouhlit. Má-li tento argument zápornou hodnotu, bude zadané číslo zaokrouhleno směrem doleva od desetinné <PERSON>. Pokud má hodnotu nula nebo je vyne<PERSON>, bude zadané číslo zaokrouhleno na nejbližší celé číslo."}, "SEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>", "ad": "je úhel v radiánech, pro který chcete získat sekans"}, "SECH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický sekans <PERSON>", "ad": "je úhel v radiánech, pro který chcete získat hyperbolický sekans"}, "SERIESSUM": {"a": "(x; n; m; koeficient)", "d": "Vrátí součet mocninné řady určené podle vzorce.", "ad": "je počáteční hodnota mocninné řady.!je počáteční mocnina, na kterou chcete umocnit argument x.!je hodnota, o kterou chcete zvyšovat argument n pro každý člen řady.!je mno<PERSON><PERSON> koe<PERSON>, kter<PERSON>mi je každá následující mocnina argumentu x vynásobena."}, "SIGN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí znaménko čísla: číslo 1 pro kladné číslo, 0 pro nulu nebo -1 pro záporné číslo.", "ad": "je libovolné reáln<PERSON>."}, "SIN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí sinus úhlu.", "ad": "je úhel v radiánech, jeh<PERSON>ž sinus chcete zjistit. Stupně*PI()/180 = radiány."}, "SINH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický sinus čísla.", "ad": "je libovolné reáln<PERSON>."}, "SQRT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí d<PERSON>hou od<PERSON>cninu čísla.", "ad": "je <PERSON><PERSON>, jeh<PERSON><PERSON> od<PERSON>u chcete zjistit."}, "SQRTPI": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrá<PERSON><PERSON> d<PERSON>hou od<PERSON> výrazu (číslo * pí).", "ad": "je č<PERSON>lo, k<PERSON><PERSON><PERSON> je číslo pí násobeno."}, "SUBTOTAL": {"a": "(funkce; odkaz1; ...)", "d": "Vrátí souhrn na listu nebo v databázi.", "ad": "je číslo od 1 do 11 určující souhrnnou funkci použitou pro souhrn.!je 1 až 254 oblastí nebo od<PERSON><PERSON>ů, pro kter<PERSON> chcete vypočítat souhrn."}, "SUM": {"a": "(číslo1; [číslo2]; ...)", "d": "Sečte všechna čísla v oblasti buněk.", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON> se<PERSON>. Logické hodnoty a text budou v buňkách přeskočeny. Pokud jsou však zadány jako argumenty, budou zahrn<PERSON>y."}, "SUMIF": {"a": "(oblast; kritéria; [součet])", "d": "Sečte buňky vybrané podle zadaných kritérií.", "ad": "je oblast buněk, které chcete sečíst.!je kritérium ve formě čísla, výrazu nebo textu určující, které buňky budou sečteny.!jsou skute<PERSON><PERSON> bu<PERSON>, které budou sečteny. Jestliže tento argument nezadáte, budou použity buňky oblasti. "}, "SUMIFS": {"a": "(oblast_součtu; oblast_kritérií; kritérium; ...)", "d": "Sečte buňky určené danou sadou podmínek nebo kritérií.", "ad": "jsou vlastní buňky určené k součtu.!představuje oblast buněk, které chcete vyhodnotit na základě určené podmínky.!je podmínka nebo kritérium v podobě čísla, výrazu nebo textu definu<PERSON><PERSON><PERSON><PERSON><PERSON>, kter<PERSON> chcete se<PERSON>."}, "SUMPRODUCT": {"a": "(pole1; [pole2]; [pole3]; ...)", "d": "Vrátí součet součinů odpovídajících oblastí nebo matic.", "ad": "je 2 a<PERSON> <PERSON> matic, j<PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON><PERSON><PERSON> položky chcete násobit a sečíst. U všech matice musí být zadány stejné dimenze."}, "SUMSQ": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí součet druhých mocnin argumentů. Argumenty mohou představovat čísla, matice, názvy nebo odkazy na buňky obsahující čísla.", "ad": "je 1 až <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> nebo odka<PERSON> na matice, pro které chcete zjistit součet mocnin."}, "SUMX2MY2": {"a": "(pole_x; pole_y)", "d": "Vypočte součet rozdílů čtverců dvou odpovídajících oblastí nebo polí.", "ad": "je první oblast nebo matice čísel. <PERSON><PERSON><PERSON><PERSON> to být číslo nebo název, matice nebo odkaz obsahující čísla.!je druhá oblast nebo matice čísel. <PERSON><PERSON><PERSON><PERSON> to být číslo nebo název, matice nebo odkaz obsahující čísla."}, "SUMX2PY2": {"a": "(pole_x; pole_y)", "d": "Vrátí celkový součet součtů čtverců čísel ve dvou odpovídajících oblastech nebo maticích.", "ad": "je první oblast nebo matice čísel. <PERSON><PERSON><PERSON><PERSON> to být číslo nebo název, matice nebo odkaz obsahující čísla.!je druhá oblast nebo matice čísel. <PERSON><PERSON><PERSON><PERSON> to být číslo nebo název, matice nebo odkaz obsahující čísla."}, "SUMXMY2": {"a": "(pole_x; pole_y)", "d": "Vypočte součet čtverců rozdílů dvou odpovídajících oblastí nebo matic.", "ad": "je první oblast nebo matice hodnot. <PERSON><PERSON><PERSON><PERSON> to být číslo nebo název, matice nebo odkaz obsahující čísla.!je druhá oblast nebo matice hodnot. <PERSON><PERSON><PERSON><PERSON> to být číslo nebo název, matice nebo odkaz obsahující čísla."}, "TAN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrá<PERSON>í tangens úhlu.", "ad": "je úhel v radiánech, jeh<PERSON>ž tangens chcete zjistit. Stupně * PI()/180 = radiány."}, "TANH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický tangens čísla.", "ad": "je libovolné reáln<PERSON>."}, "TRUNC": {"a": "(č<PERSON>lo; [desetiny])", "d": "Zkrátí číslo na celé číslo odstraněním desetinné nebo zlomkové části čísla.", "ad": "je <PERSON><PERSON><PERSON>, k<PERSON><PERSON> chcete zkrátit.!je číslo určující přesnost zkrácení. Jestliže tento argument nezadá<PERSON>, bude jeho hodnota 0."}, "ADDRESS": {"a": "(<PERSON><PERSON><PERSON>; sloupec; [typ]; [a1]; [list])", "d": "Vytvoří textový odkaz na buňku po zadání čísla řádku a sloupce.", "ad": "je číslo řádku použité v odkazu na buňku: argument Řádek = 1 pro řádek číslo 1.!je číslo sloupce použité v odkazu na buňku: argument Sloupec = 4 pro sloupec D.!určuje typ odkazu: absolutní = 1, absolutn<PERSON>/relativní sloupec = 2, relativn<PERSON>/absolutní sloupec = 3, relativní = 4.!je logická hodnota určující styl odkazu: styl A1 = 1 nebo PRAVDA, styl R1C1 = 0 nebo NEPRAVDA.!je text ur<PERSON><PERSON>j<PERSON><PERSON><PERSON> název listu, který bude použit jako externí odkaz."}, "CHOOSE": {"a": "(index; hodnota1; [hodnota2]; ...)", "d": "Zvolí hodnotu nebo a<PERSON>, k<PERSON><PERSON> má být <PERSON>, ze seznamu hodnot na základě zadaného argumentu Index.", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> argument hodnoty je vybrán. Argument Index musí náležet do rozsahu 1 až 254 nebo představovat vzorec či odkaz na číslo v rozsahu 1 až 254.!je 1 až 254 hodnot, <PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, def<PERSON><PERSON><PERSON><PERSON>vů, <PERSON><PERSON><PERSON><PERSON>, funkcí nebo textových argumentů, z nichž vybírá funkce ZVOLIT."}, "COLUMN": {"a": "([odkaz])", "d": "Vrátí č<PERSON>lo s<PERSON>ce odkazu.", "ad": "je buňka nebo souvislá oblast buněk, j<PERSON><PERSON><PERSON> slou<PERSON>ce hledáte. Jestliže tento argument nezadáte, bude použita buňka obsahující funkci SLOUPEC."}, "COLUMNS": {"a": "(pole)", "d": "Vrátí počet sloupců v matici nebo odkazu.", "ad": "je matice, maticový vzorec nebo odkaz na oblast buněk, pro k<PERSON><PERSON> hled<PERSON>te počet slou<PERSON>."}, "FORMULATEXT": {"a": "(odkaz)", "d": "Vrátí vzorec jako ř<PERSON>", "ad": "je odkaz na vzorec"}, "HLOOKUP": {"a": "(hledat; tabulka; <PERSON><PERSON><PERSON>; [typ])", "d": "Prohledá horní řádek tabulky nebo matice hodnot a vrátí hodnotu ze zadaného řádku obsaženou ve stejném sloupci.", "ad": "je hodnota, kterou chcete vyhledat v prvním řádku tabulky. <PERSON><PERSON><PERSON><PERSON> to být hodnota, odkaz nebo textový řetězec.!je prohledávaná tabulka obsahující text, čísla nebo logické hodnoty. Argument Tabulka může být odkaz na oblast nebo název oblasti.!je číslo řádku v argumentu Tabulka, ze kterého bude vrácena odpovídající hodnota. První řádek hodnot tabulky je řádek číslo 1.!je logická hodnota: najít nejbližší odpovídající hodnotu v horním řádku (hodnoty jsou seřazeny vzestupně) = PRAVDA nebo bez zadání, najít přesnou odpovídající hodnotu = NEPRAVDA."}, "HYPERLINK": {"a": "(umístění; [název])", "d": "Vytvoří zástupce nebo odkaz, kter<PERSON> otevře dokument uložený na pevném disku, síťovém serveru nebo na síti Internet.", "ad": "je text představující cestu a název souboru s dokumentem, kter<PERSON> chcete otevřít, umístění na pevném disku, adresu UNC nebo cestu URL.!je text nebo číslo, které bude zobrazeno v buňce. Jestliže argument Název nezadáte, objeví se v buňce text argumentu Umístění."}, "INDEX": {"a": "(pole; <PERSON><PERSON><PERSON>; [sloupec]!od<PERSON><PERSON>; <PERSON><PERSON><PERSON>; [sloupec]; [oblast])", "d": "Vrátí hodnotu nebo odkaz na buňku v určitém řádku a sloupci v dané oblasti.", "ad": "je oblast buněk nebo maticová konstanta.!vybere řádek v argumentu Pole nebo Odkaz, ze kterého bude vrácena hodnota. Jestliže tento argument nezadáte, musíte zadat argument Sloupec.!vybere sloupec v argumentu Pole nebo Odkaz, ze kterého bude vrácena hodnota. Jestliže tento argument nezadáte, musíte zadat argument Řádek.!je odkaz na jednu nebo více oblastí buněk.!vybere řádek v argumentu Pole nebo Odkaz, ze kterého bude vrácena hodnota. Jestliže tento argument nezadáte, musíte zadat argument Sloupec.!vybere sloupec v argumentu Pole nebo Odkaz, ze kterého bude vrácena hodnota. Jestliže tento argument nezadáte, musíte zadat argument Řádek.!vybere oblast v argumentu Odkaz, ze které bude vrácena hodnota. První vybraná nebo zadaná oblast je oblast číslo 1, druhá oblast je oblast číslo 2, atd."}, "INDIRECT": {"a": "(odkaz; [a1])", "d": "Vrátí odkaz určený textovým řetězcem.", "ad": "je odkaz na buňku obsahující odkaz ve stylu A1 nebo R1C1, n<PERSON><PERSON><PERSON> definovaný jako odkaz nebo odkaz na buňku ve formátu textového řetězce.!je logick<PERSON> hodn<PERSON>, která určuje styl odkazu v argumentu Odkaz: styl R1C1 = NEPRAVDA, styl A1 = PRAVDA nebo bez zadání."}, "LOOKUP": {"a": "(co; hledat; [v<PERSON><PERSON><PERSON>]!co; pole)", "d": "Vyhledá požadovanou hodnotu v matici nebo v oblasti obsahující jeden řádek nebo jeden sloupec. Funkce je poskytnuta k zajištění zpětné kompatibility", "ad": "je hodnota vyhledávaná pomocí funkce VYHLEDAT v argumentu Hledat. <PERSON><PERSON><PERSON><PERSON> to být číslo, text, logick<PERSON> hodnota, název nebo odkaz na hodnotu.!je oblast obsahující pouze jeden řádek nebo jeden sloupec se vzestupně seřazeným textem, čísly nebo logickými hodnotami.!je oblast obsahující pouze jeden řádek nebo sloupec. Velikost oblasti je stejná jako velikost oblasti argumentu Hledat.!je hodnota vyhledávaná pomocí funkce VYHLEDAT v argumentu Pole. Můž<PERSON> to být číslo, text, logická hodnota, název nebo odkaz na hodnotu.!je oblast buněk obsahující text, čísla nebo logické hodnoty, které chcete srovnávat s hodnotami argumentu Co."}, "MATCH": {"a": "(co; prohledat; [shoda])", "d": "Vrátí relativní polohu položky matice, která odpovídá určené hodnotě v určeném pořadí.", "ad": "je hodnota, kter<PERSON> bude použita k vyhledání požadované hodnoty v matici. <PERSON><PERSON><PERSON><PERSON> to b<PERSON>t <PERSON>, text nebo logická hodnota nebo odkaz na jednu z uvedených položek.!je souvislá oblast buněk obsahující hledané hodnoty, matice hodnot nebo odkaz na matici.!je číslo 1, 0 nebo -1 ur<PERSON><PERSON>j<PERSON><PERSON>í, kter<PERSON> hodnota bude vrácena."}, "OFFSET": {"a": "(odkaz; řádky; sloupce; [výška]; [ší<PERSON>ka])", "d": "Vrátí odkaz na oblast, která představuje daný počet řádků a sloupců z daného odkazu.", "ad": "je odkaz na buňku nebo oblast buněk, od které chcete změřit odsazení.!je počet <PERSON>, nahoru nebo do<PERSON>, na které se má horní levá buňka výsledku odkazovat.!je počet sloup<PERSON>, vpravo nebo vlevo, na které se má horní levá buňka výsledku odkazovat.!je požadovaná výška výsledku vyjádřená počtem řádků. Jestliže tento argument nezadáte, bude jeho hodnota stejná jako hodnota argumentu Odkaz.!je požadovaná šířka výsledku vyjádřená počtem sloupců. Jestliže tento argument nezadáte, bude jeho hodnota stejná jako hodnota argumentu Odkaz."}, "ROW": {"a": "([odkaz])", "d": "Vrátí č<PERSON>lo <PERSON>d<PERSON> od<PERSON>zu.", "ad": "je buňka nebo jediná oblast buněk, pro kterou chcete nalézt číslo řádku. Jestliže tento argument nezadáte, bude jeho hodnota buňka obsahující funkci Řádek."}, "ROWS": {"a": "(pole)", "d": "Vrátí počet řádků v odkazu nebo matici.", "ad": "je matice, maticový vzorec nebo odkaz na oblast buněk, pro kterou chcete nalézt počet řádků."}, "TRANSPOSE": {"a": "(pole)", "d": "Převede vodorovnou oblast buněk na svislou nebo naopak.", "ad": "je oblast buněk listu nebo matice hodnot, kterou chcete transponovat."}, "UNIQUE": {"a": "(pole; [sloup<PERSON>]; [pr<PERSON><PERSON><PERSON>_jed<PERSON><PERSON>])", "d": "Vrátí jedinečné hodnoty z rozsahu nebo pole.", "ad": "Rozsah nebo pole, ze kterého se mají vrátit jedinečné řádky nebo sloupce!Logická hodnota: NEPRAVDA v případě, že chcete navzájem porovnat řádky a vrátit jedinečné <PERSON>; PRAVDA v případě, že chcete navzájem porovnat sloupce a vrátit jedinečné sloupce!Logická hodnota: PRAVDA v případě, že chcete vrátit řádky a sloupce, které se v rozsahu nebo poli vyskytují právě jednou; NEPRAVDA v případě, že chcete z rozsahu nebo pole vrátit všechny různé řádky a sloupce; parametr je možné i vynechat"}, "VLOOKUP": {"a": "(hledat; tabulka; sloupec; [typ])", "d": "Vyhledá hodnotu v krajním levém sloupci tabulky a vrátí hodnotu ze zadaného sloupce ve stejném řádku. Tabulka musí být standardně seřazena vzestupně.", "ad": "je hodnota hledaná v prvním sloupci tabulky. <PERSON><PERSON><PERSON><PERSON> to být hodnota, odkaz nebo textový řetězec.!je prohledávaná tabulka s textem, č<PERSON>ly nebo logickými hodnotami. Argument Tabulka může být odkaz na oblast nebo název oblasti.!je číslo sloupce v argumentu Tabulka, ve kterém chcete vyhledat odpovídající hodnotu. První sloupec hodnot v tabulce je sloupec číslo 1.!je logická hodnota: nalézt nejbližší odpovídající hodnotu v prvním sloupci (seřazeném vzestupně) = PRAVDA nebo bez zadání, nalézt přesnou odpovídající hodnotu = NEPRAVDA."}, "XLOOKUP": {"a": "(co; prohledat; vr<PERSON><PERSON>t; [pokud_ne<PERSON><PERSON>o]; [rež<PERSON>_shody]; [režim_vyhled<PERSON>í])", "d": "Vyhledá požadovanou hodnotu v oblasti nebo poli a vrátí odpovídající položku z jiné oblasti nebo pole. Ve výchozím nastavení se vyžaduje přesná shoda.", "ad": "je hodnota, kter<PERSON> se bude hledat.!je pole nebo oblast, kter<PERSON> se bude prohledávat.!je pole nebo oblast, ze které se vrátí odpovídající hodnota.!je hodnota, kter<PERSON> se vrátí v př<PERSON>padě, že se shoda nenajde.!u<PERSON><PERSON><PERSON><PERSON>, jak<PERSON><PERSON> způsobem se mají shodovat hodnota argumentu co a hodnoty v prohledávaném poli nebo oblasti.!určuje režim vyhledávání, který se má použít. Ve výchozím nastavení se použije vyhledávání od první hodnoty po poslední."}, "CELL": {"a": "(informace; [odkaz])", "d": "Vrátí informace o formátování, umístění nebo obsahu bu<PERSON>", "ad": "text<PERSON>, k<PERSON><PERSON> určuje typ informací o buňce, které mají být vráceny!ad<PERSON><PERSON> bu<PERSON>, o které chcete získat informace"}, "ERROR.TYPE": {"a": "(chyba)", "d": "Vrátí číslo odpovídající chybové hodnotě.", "ad": "je chybov<PERSON> hodnota, pro kterou chcete zjistit identifikační číslo. <PERSON><PERSON><PERSON><PERSON> to být aktuální chybová hodnota nebo odkaz na buňku obsahující chybovou hodnotu."}, "ISBLANK": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda odkaz v argumentu Hodnota odkazuje na prázdnou buňku a vrátí hodnotu PRAVDA nebo NEPRAVDA.", "ad": "je testovaná buňka nebo n<PERSON>zev, k<PERSON><PERSON> se vztahuje k testované buň<PERSON>."}, "ISERR": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jest<PERSON> argument Hodnota představuje jinou chybu než #NENÍ_K_DISPOZICI, a vrátí hodnotu PRAVDA nebo NEPRAVDA.", "ad": "je hodnota, k<PERSON><PERSON> chcete testovat. Argument Hodnota se může vztahovat k buňce, vzorci nebo názvu odkazujícímu se na buňku, vzorec nebo hodnotu."}, "ISERROR": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jest<PERSON> <PERSON> Hodnota představuje chybu, a vrátí hodnotu PRAVDA nebo NEPRAVDA.", "ad": "je hodnota, k<PERSON><PERSON> chcete testovat. Argument Hodnota se může vztahovat k buňce, vzorci nebo názvu odkazujícímu se na buňku, vzorec nebo hodnotu."}, "ISEVEN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí logickou hodnotu PRAVDA, pokud je číslo sudé.", "ad": "je testova<PERSON> ho<PERSON>."}, "ISFORMULA": {"a": "(odkaz)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jestli odkaz odkazuje na buňku obsahující vzorec, a vrátí hodnotu PRAVDA nebo NEPRAVDA", "ad": "je odkaz na buňku, k<PERSON><PERSON> chcete testovat. Odkaz může být odkaz na buňku, vzorec nebo název odkazující na buňku"}, "ISLOGICAL": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda argument Hodnota je logická hodnota (PRAVDA nebo NEPRAVDA) a vrátí hodnotu PRAVDA nebo NEPRAVDA.", "ad": "je hodnota, k<PERSON><PERSON> chcete testovat. Argument Hodnota se může vztahovat k buňce, vzorci nebo názvu odkazujícímu na buňku, vzorec nebo hodnotu."}, "ISNA": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je argument Hodnota chybová hodnota #NENÍ_K_DISPOZICI, a vrátí hodnotu PRAVDA nebo NEPRAVDA.", "ad": "je hodnota, k<PERSON><PERSON> chcete testovat. Argument Hodnota se může vztahovat k buňce, vzorci nebo názvu odkazujícímu se na buňku, vzorec nebo hodnotu."}, "ISNONTEXT": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda argument Hodnota není text (prázdné buňky nejsou text) a vrátí hodnotu PRAVDA nebo NEPRAVDA.", "ad": "<PERSON>e ho<PERSON><PERSON>, k<PERSON><PERSON> ch<PERSON>te testovat. <PERSON><PERSON><PERSON><PERSON> to b<PERSON><PERSON> bu<PERSON>, vzorec nebo název odkazující na buňku, vzorec nebo hodnotu."}, "ISNUMBER": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je argument Hodnota číslo a vrátí hodnotu PRAVDA nebo NEPRAVDA.", "ad": "je hodnota, k<PERSON><PERSON> chcete testovat. Argument Hodnota se může vztahovat k buňce, vzorci nebo názvu odkazujícímu na buňku, vzorec nebo hodnotu."}, "ISODD": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí logickou hodnotu PRAVDA, pokud je číslo lich<PERSON>.", "ad": "je testova<PERSON> ho<PERSON>."}, "ISREF": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je argument Hodnota odkaz a vrátí hodnotu PRAVDA nebo NEPRAVDA.", "ad": "je hodnota, k<PERSON><PERSON> chcete testovat. Argument Hodnota se může vztahovat k buňce, vzorci nebo názvu odkazujícímu se na buňku, vzorec nebo hodnotu."}, "ISTEXT": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je argument Hodnota text a vrátí hodnotu PRAVDA nebo NEPRAVDA.", "ad": "je hodnota, k<PERSON><PERSON> chcete testovat. Argument Hodnota se může vztahovat k buňce, vzorci nebo názvu odkazujícímu na buňku, vzorec nebo hodnotu."}, "N": {"a": "(hodnota)", "d": "Převede nečíselnou hodnotu na číslo, kalendářní data na pořadová čísla, hodnotu PRAVDA na číslo 1 a všechny ostatní výrazy na číslo 0 (nula).", "ad": "je hodn<PERSON>, k<PERSON><PERSON><PERSON> p<PERSON>."}, "NA": {"a": "()", "d": "Vrátí chybovou hodnotu #NENÍ_K_DISPOZICI (hodnota nedostupná).", "ad": ""}, "SHEET": {"a": "([hodnota])", "d": "Vrátí číslo listu odkazovaného listu", "ad": "je název listu nebo o<PERSON>, pro který chcete zjistit číslo listu. Pokud ho v<PERSON>, vrátí číslo listu obsahujíc<PERSON><PERSON>"}, "SHEETS": {"a": "([odkaz])", "d": "Vrátí počet listů v odkazu", "ad": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, pro který chcete zjistit počet listů, kter<PERSON> obsahuje. Pokud ho vynechá<PERSON>, vrátí počet listů v sešitu obsahujícím funkci"}, "TYPE": {"a": "(hodnota)", "d": "Vrátí celé číslo představující datový typ hodnoty: č<PERSON>lo = 1, text = 2, <PERSON><PERSON><PERSON> hodnota = 4, chybo<PERSON><PERSON> hodnota = 16, matice = 64, slo<PERSON><PERSON><PERSON> datový typ = 128.", "ad": "je libovolná hodnota."}, "AND": {"a": "(logická1; [logická2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda mají všechny argumenty hodnotu PRAVDA, a v takovém případě vrátí hodnotu PRAVDA.", "ad": "je 1 až <PERSON> test<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> mohou mít hodnotu PRAVDA nebo NEPRAVDA. <PERSON><PERSON> to b<PERSON>t <PERSON><PERSON><PERSON>, matice nebo od<PERSON>."}, "FALSE": {"a": "()", "d": "Vrátí logickou hodnotu NEPRAVDA.", "ad": ""}, "IF": {"a": "(podm<PERSON>ka; [ano]; [ne])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je podmínka splněna, a v<PERSON>á<PERSON><PERSON> jednu hodnotu, jest<PERSON><PERSON>e je výsledkem hodnota PRAVDA, a jinou hodnotu, pokud je výsledkem hodnota NEPRAVDA.", "ad": "je libovolná hodnota nebo výraz, k<PERSON><PERSON><PERSON> mů<PERSON><PERSON> být přiřazena logická hodnota PRAVDA nebo NEPRAVDA.!je hodnota vrácená, je-li hodnota argumentu Podmínka PRAVDA. Jest<PERSON>ž<PERSON> ji nezadá<PERSON>, bude vrácena hodnota PRAVDA. Můžete vnořit až sedm funkcí KDYŽ.!je hodnota vrácená, je-li hodnota argumentu Podmínka NEPRAVDA. <PERSON><PERSON><PERSON><PERSON><PERSON> ji nezadá<PERSON>, bude vrácena hodnota NEPRAVDA."}, "IFS": {"a": "(logický_test; podmínka; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jest<PERSON> je splněná jedna nebo ví<PERSON>, a vrátí hodnotu odpovídající první pravdivé podmínce.", "ad": "je libovolná hodnota nebo výraz, k<PERSON><PERSON> mů<PERSON><PERSON> být vyhodnocený jako PRAVDA nebo NEPRAVDA.!je hodnota vrácená, pokud argument logický_test je PRAVDA."}, "IFERROR": {"a": "(hodnota; hodnota_v_případě_chyby)", "d": "Pokud je výraz chy<PERSON>ný, vr<PERSON>t<PERSON> hodnotu hodnota_v_případě_chyby. V opačném případě vrátí vlastní hodnotu výrazu.", "ad": "je libovolná hodnota, výraz či odkaz.!je libovolná hodnota, výraz či odkaz."}, "IFNA": {"a": "(hodnota; hodnota_pokud_na)", "d": "Vrátí zada<PERSON>u hodnotu, pokud je výsledkem výrazu hodnota #N/A, v opačném případě vrátí výsledek výrazu", "ad": "je jakákoli hodnota nebo výraz nebo odkaz!je jakákoli hodnota nebo výraz nebo odkaz"}, "NOT": {"a": "(loghod)", "d": "Změní hodnotu NEPRAVDA na PRAVDA nebo naopak.", "ad": "je hodnota nebo výraz, k<PERSON><PERSON> mů<PERSON> být PRAVDA nebo NEPRAVDA."}, "OR": {"a": "(logická1; [logická2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je nejméně jeden argument roven hodnotě PRAVDA, a vrátí hodnotu PRAVDA nebo NEPRAVDA. Vrátí hodnotu NEPRAVDA pouze v případě, že všechny argumenty jsou rovny hodnotě NEPRAVDA.", "ad": "je 1 a<PERSON> <PERSON>, kter<PERSON> chcete testovat a které mohou mít hodnotu PRAVDA nebo NEPRAVDA."}, "SWITCH": {"a": "(výraz; hodnota1; výsledek1; [vý<PERSON><PERSON><PERSON>_nebo_hodnota2]; [výsledek2]; ...)", "d": "Vyhodnocuje výraz oproti seznamu hodnot a vrací výsledek odpovídající první shodné hodnotě. Pokud se neshoduje žádn<PERSON> hodnota, vrátí volitelnou výchozí hodnotu.", "ad": "je vyhodnocovaný výraz.!je hodnota porovnávaná s výrazem.!je v<PERSON><PERSON><PERSON>, který se vrátí v případě, že se příslušná hodnota shoduje s výrazem."}, "TRUE": {"a": "()", "d": "Vrátí logickou hodnotu PRAVDA.", "ad": ""}, "XOR": {"a": "(logická1; [logická2]; ...)", "d": "Vrátí logickou hodnotu Výhradní nebo všech argumentů", "ad": "je 1 a<PERSON> <PERSON>, kter<PERSON> chcete testovat a které mohou mít hodnotu PRAVDA nebo NEPRAVDA a mohou být logic<PERSON> hodn<PERSON>, matice nebo od<PERSON>zy"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Vrát<PERSON> text, kter<PERSON> je před oddělov<PERSON>č<PERSON> znaků.", "ad": "Text, který chcete vyhledat jako oddělovač.!Znak nebo řetězec, který se má použít jako oddělovač.!Požadovaný výskyt oddělovače. Výchozí hodnota je 1. Záporné číslo hledá od konce.!Vyhledá shodu oddělovače v textu. Ve výchozím nastavení je provedena shoda s rozlišováním malých a velkých písmen.!Jestli se má oddělovač shodovat s koncem textu. Ve výchozím nastavení se neshodují.!<PERSON><PERSON><PERSON><PERSON>no, pokud nebyla nalezena žádná shoda. Ve výchozím nastavení je vrácen #N/A."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Vrátí text, který je po oddělovači znaků.", "ad": "Text, který chcete vyhledat jako oddělovač.!Znak nebo řetězec, který se má použít jako oddělovač.!Požadovaný výskyt oddělovače. Výchozí hodnota je 1. Záporné číslo hledá od konce.!Vyhledá shodu oddělovače v textu. Ve výchozím nastavení je provedena shoda s rozlišováním malých a velkých písmen.!Jestli se má oddělovač shodovat s koncem textu. Ve výchozím nastavení se neshodují.!<PERSON><PERSON><PERSON><PERSON>no, pokud nebyla nalezena žádná shoda. Ve výchozím nastavení je vrácen #N/A."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Rozdělí text na řádky nebo sloupce pomocí <PERSON>.", "ad": "Text, který se má rozdělit!<PERSON>na<PERSON> nebo řetězec, podle kterého se mají rozdělit sloupce.!Znak nebo řetězec, podle kterého se mají rozdělit řádky.!Jestli se mají ignorovat prázdné buňky. Výchozí hodnota je NEPRAVDA.!Vyhledá shodu oddělovače v textu. Ve výchozím nastavení je provedena shoda s rozlišováním malých a velkých písmen.!Hodnota, která se má použít pro odsazení. Ve výchozím nastavení se používá #N/A."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Zalomí vektor řádku nebo sloupce po zadaném po<PERSON><PERSON> hodnot.", "ad": " Vektor nebo od<PERSON>, který se má zalomit.!Maximální počet hodnot na řádek.!<PERSON><PERSON><PERSON>, se kterou se má pole zapisovat. Výchozí hodnota je #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": " <PERSON><PERSON><PERSON><PERSON><PERSON> pole do j<PERSON><PERSON><PERSON> pole.", "ad": "Pole nebo o<PERSON><PERSON><PERSON>, k<PERSON><PERSON> se mají s<PERSON>"}, "HSTACK": {"a": "(array1, [array2], ...)", "d": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON> pole do jednoho pole.", "ad": "Pole nebo o<PERSON><PERSON><PERSON>, k<PERSON><PERSON> se mají s<PERSON>"}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Vrátí <PERSON>ádky z pole nebo odkazu.", "ad": "Pole nebo odkaz o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, které se mají vrátit.!<PERSON><PERSON><PERSON>, který se má vrátit."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Vrátí sloupce z pole nebo odkazu.", "ad": "Pole nebo odkaz obs<PERSON><PERSON><PERSON><PERSON><PERSON> slou<PERSON>, které se mají vrátit.!<PERSON><PERSON><PERSON> sloupce, který se má vrátit"}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON>rá<PERSON><PERSON> pole jako jeden sloupec.", "ad": "Pole nebo od<PERSON><PERSON>, kter<PERSON> se mají vrátit jako sloupec.!<PERSON><PERSON><PERSON><PERSON><PERSON>, jestli se mají ignorovat určité typy hodnot. Ve výchozím nastavení nejsou ignorovány žádné hodnoty.!Prohledá pole podle sloupce. Ve výchozím nastavení se pole prohledá podle řádku."}, "TOROW": {"a": "(pole, [ignorovat], [pro<PERSON><PERSON><PERSON><PERSON>_podle_sloupce])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> pole jako jede<PERSON>. ", "ad": "Pole nebo od<PERSON><PERSON>, kter<PERSON> se mají vrá<PERSON>t jako <PERSON>.!<PERSON><PERSON><PERSON><PERSON><PERSON>, jestli se mají ignorovat určité typy hodnot. Ve výchozím nastavení nejsou ignorovány žádné hodnoty.! Prohledá pole podle sloupce. Ve výchozím nastavení se pole prohledá podle řádku."}, "WRAPCOLS": {"a": "(vektor, wrap_count, [pad_with])", "d": " Zabalí vektor řádku nebo sloupce po zadaném po<PERSON><PERSON> hodnot.", "ad": " Vektor nebo od<PERSON>, který se má zalomit.! Maximální počet hodnot na sloupec.! <PERSON><PERSON><PERSON>, se kterou se má pole zapisovat. Výchozí hodnota je #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Vrátí řádky nebo sloupce ze začátku nebo konce pole.", "ad": "<PERSON>, ze kterého chcete vzít řádky nebo sloupce.!Počet řádků, které se mají vzít. Záporná hodnota vezme od konce pole.!<PERSON><PERSON><PERSON> sloupců, které se mají přemístit. Záporná hodnota přemístí od konce pole."}, "DROP": {"a": "(array, rows, [columns])", "d": "Přemístí řádky nebo sloupce ze začátku nebo konce pole.", "ad": "<PERSON>, ze kterého chcete přemístit řádky nebo sloupce.!Počet řádků, které se mají přemístit. Záporná hodnota přemístí od konce pole.!<PERSON><PERSON><PERSON> sloupců, které se mají přemístit. Záporná hodnota přemístí od konce pole."}, "SEQUENCE": {"a": "(<PERSON><PERSON><PERSON><PERSON>, [sloup<PERSON>], [za<PERSON><PERSON><PERSON><PERSON>], [krok])", "d": "Vrátí aritmetickou posloupnost.", "ad": "je počet <PERSON>, které se mají vrátit.!je počet sloupců, které se mají vrátit.!je první číslo v posloupnosti.!je hodnota diference každé další hodnoty v posloupnosti."}, "EXPAND": {"a": "(matice, <PERSON><PERSON><PERSON><PERSON>, [sloup<PERSON>], [výplň])", "d": "Rozbalí pole na zadané dimenze.", "ad": "<PERSON>, které se má rozbalit.!Počet řádků v rozbaleném poli Pokud chybí, řádky nebudou rozbaleny.!Počet sloupců v rozbaleném poli Pokud chybí, sloupce nebudou rozbaleny.!<PERSON><PERSON><PERSON>, se kterou se má pole zapisovat Výchozí hodnota je #N/A."}, "XMATCH": {"a": "(hodnota_v<PERSON><PERSON>ed<PERSON>vání, pole_vyhled<PERSON>í, [rež<PERSON>_shody], [rež<PERSON>_v<PERSON><PERSON><PERSON>])", "d": "Vrátí relativní polohu prvku pole. Ve výchozím nastavení se vyžaduje přesná shoda.", "ad": "je hodnota, kter<PERSON> se bude hledat.!je pole nebo oblast, kter<PERSON> se bude prohledávat.!<PERSON><PERSON><PERSON><PERSON><PERSON>, jak<PERSON>m způsobem se mají shodovat argumenty hodnota_vyhledávání a vyhledávácí_pole.!určuje rež<PERSON> v<PERSON>, který se má použít. Ve výchozím nastavení se použije vyhledávání od první hodnoty po poslední."}, "FILTER": {"a": "(matice, zahrnuje, [pokud_prázdné])", "d": "Filtruje oblast nebo matici.", "ad": "oblast nebo matice, kter<PERSON> se mají filtrovat!matice logic<PERSON><PERSON>ch hodnot, kde PRAVDA představuje řádek nebo sloupec, kter<PERSON> chcete zachovat!hodnota vrácená, pokud nejsou zachovány žádné <PERSON>"}, "ARRAYTOTEXT": {"a": "(pole, [form<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> text<PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON> pole.", "ad": "pole, které má být vyjádř<PERSON> v textové podobě!formát textu"}, "SORT": {"a": "(pole, [index_řazení], [po<PERSON><PERSON>í_řazení], [pod<PERSON>_slou<PERSON><PERSON>])", "d": "Seřadí oblast nebo pole.", "ad": "Oblast nebo pole, které se mají seřadit!<PERSON><PERSON>lo určující řádek nebo sloupec, podle kterého se mají hodnoty seřadit!Číslo určující požadované pořadí řazení; 1 pro vzestupné řazení (výchozí), -1 pro sestupné řazení!Logická hodnota určující požadovaný směr řazení: FALSE znamená řazení podle řádku (výchozí), TRUE znamená řazení podle sloupce."}, "SORTBY": {"a": "(matice, podle_matice, [po<PERSON><PERSON><PERSON>_řazení], ...)", "d": "Seřadí oblast nebo matici na základě hodnot v odpovídající oblasti nebo matici.", "ad": "je oblast nebo matice, kter<PERSON> se bude řadit.!je oblast nebo matice, podle které se hodnoty budou řadit.!je hodnota určující požadované pořadí řazení: 1 pro řazení vzestupně (výchozí), -1 pro řazení sestupně."}, "GETPIVOTDATA": {"a": "(datové_pole; kontingenční_tabulka; [pole]; [položka]; ...)", "d": "Extrahuje data uložená v kontingenční tabulce.", "ad": "je n<PERSON><PERSON><PERSON> da<PERSON><PERSON>ho pole, ze kter<PERSON>ho mají být data extrahována.!je odkaz na buňku nebo oblast buněk v kontingenční tabulce obsahující data, kter<PERSON> chcete načíst.!pole, na které chcete odkazovat.!polo<PERSON><PERSON> pole, na n<PERSON><PERSON> chcete odkazovat."}, "IMPORTRANGE": {"a": "(url_tabulky; řetězec_rozsahu)", "d": "Importuje rozsah buněk ze zadané tabulky.", "ad": "adresa URL tabulky, ze které budou importována data!rozsah, který se má importovat"}}