{"DATE": {"a": "(год; месяц; день)", "d": "Возвращает число, соответствующее дате в коде даты-времени", "ad": "число от 1900 или 1904 (в зависимости от системы дат книги) до 9999!число от 1 до 12, соответствующее месяцу года!число от 1 до 31, соответствующее дню месяца"}, "DATEDIF": {"a": "(нач_дата;кон_дата;единица)", "d": "Возвращает разницу между двумя датами (начальной и конечной) согласно заданному интервалу (единице)", "ad": "дата, представляющая первую или начальную дату заданного периода!дата окончания периода!тип возвращаемых сведений"}, "DATEVALUE": {"a": "(дата_как_текст)", "d": "Преобразует дату из текстового формата в числовой в коде даты-времени", "ad": "строка, содержащая дату в формате даты Редактора таблиц в диапазоне от 01.01.1900 или 01.01.1904 (в зависимости от системы дат книги) до 31.12.9999"}, "DAY": {"a": "(дата_в_числовом_формате)", "d": "Возвращает число месяца - число от 1 до 31.", "ad": "число в коде даты-времени, используемом в Редакторе таблиц"}, "DAYS": {"a": "(кон_дата; нач_дата)", "d": "Возвращает количество дней между двумя датами", "ad": "нач_дата и кон_дата — две даты, количество дней между которыми необходимо определить!нач_дата и кон_дата — две даты, количество дней между которыми необходимо определить"}, "DAYS360": {"a": "(нач_дата; кон_дата; [метод])", "d": "Вычисляет количество дней между двумя датами на основе 360-дневного года (двенадцать месяцев по 30 дней)", "ad": "'нач_дата' и 'кон_дата' - это даты, количество дней между которыми требуется определить!'нач_дата' и 'кон_дата' - это даты, количество дней между которыми требуется определить!логическое значение, определяющее используемый в вычислениях метод: европейский (ИСТИНА) или американский (ЛОЖЬ или отсутствие значения)."}, "EDATE": {"a": "(нач_дата; число_месяцев)", "d": "Возвращает порядковый номер даты, отстоящей на заданное число месяцев вперед или назад от заданной даты (нач_дата)", "ad": "порядковый номер начальной даты!количество месяцев до или после начальной даты"}, "EOMONTH": {"a": "(нач_дата; число_месяцев)", "d": "Возвращает порядковый номер последнего дня месяца, отстоящего на заданное число месяцев вперед или назад от заданной даты (нач_дата)", "ad": "порядковый номер начальной даты!количество месяцев до или после начальной даты"}, "HOUR": {"a": "(время_в_числовом_формате)", "d": "Возвращает часы в виде числа от 0 до 23.", "ad": "число в коде даты-времени, используемом в Редакторе таблиц, или текст в формате времени, например 16:48:00"}, "ISOWEEKNUM": {"a": "(дата)", "d": "Возвращает номер недели в году по стандарту ISO для указанной даты", "ad": "код даты-времени, используемый в Редакторе таблиц для вычислений со значениями этого типа"}, "MINUTE": {"a": "(время_в_числовом_формате)", "d": "Возвращает минуты в виде числа от 0 до 59.", "ad": "число в коде даты-времени, используемом в Редакторе таблиц, или текст в формате времени, например 16:48:00"}, "MONTH": {"a": "(дата_в_числовом_формате)", "d": "Возвращает месяц - число от 1 (январь) до 12 (декабрь).", "ad": "число в коде даты-времени, используемом в Редакторе таблиц"}, "NETWORKDAYS": {"a": "(нач_дата; кон_дата; [праздники])", "d": "Возвращает количество полных рабочих дней между двумя датами", "ad": "порядковый номер начальной даты!порядковый номер конечной даты!необязательный список из одной или нескольких заданных порядковыми номерами дат для исключения из рабочего календаря, таких как государственные праздники"}, "NETWORKDAYS.INTL": {"a": "(нач_дата; кон_дата; [выходные]; [праздники])", "d": "Возвращает количество полных рабочих дней между двумя датами с настраиваемыми параметрами выходных", "ad": "порядковый номер начальной даты!порядковый номер конечной даты!число или строка, указывающая на выходные дни!необязательный список из одной или нескольких заданных порядковыми номерами дат для исключения из рабочего календаря, таких как государственные праздники"}, "NOW": {"a": "()", "d": "Возвращает текущую дату и время в формате даты и времени.", "ad": ""}, "SECOND": {"a": "(время_в_числовом_формате)", "d": "Возвращает секунды в виде числа от 0 до 59.", "ad": "число в коде даты-времени, используемом в Редакторе таблиц, или текст в формате времени, например 16:48:23"}, "TIME": {"a": "(часы; минуты; секунды)", "d": "Преобразует заданные в виде чисел часы, минуты и секунды в число в коде времени", "ad": "число от 0 до 23, представляющее час!число от 0 до 59, представляющее минуту!число от 0 до 59, представляющее секунду"}, "TIMEVALUE": {"a": "(время_как_текст)", "d": "Преобразует время из текстового формата в число, представляющее время - число от 0 (0:00:00) до 0,999988426 (23:59:59). Введя формулу, задайте для ячейки тип \"Время\"", "ad": "строка текста, содержащая время в любом из форматов времени Редакторе таблиц (сведения о дате игнорируются)"}, "TODAY": {"a": "()", "d": "Возвращает текущую дату в формате даты.", "ad": ""}, "WEEKDAY": {"a": "(дата_в_числовом_формате; [тип])", "d": "Возвращает число от 1 до 7, соответствующее номеру дня недели для заданной даты.", "ad": "число, представляющее дату!числ<PERSON> (1,2 или 3), определяющее тип отсчета недели (с Вс=1 до Сб=7; с Пн=1 до Вс=7 или с Пн=0 до Вс=6 соответственно)"}, "WEEKNUM": {"a": "(пор_номер_даты; [тип_возвр])", "d": "Возвращает номер недели года", "ad": "код, используемый Редактором таблиц для расчета даты и времени!число (1 или 2), определяющее тип возвращаемого значения"}, "WORKDAY": {"a": "(нач_дата; число_дней; [праздники])", "d": "Возвращает порядковый номер даты, отстоящей на заданное число рабочих дней вперед или назад от начальной даты", "ad": "начальная дата, заданная порядковым номером!количество не выходных и не праздничных дней до или после начальной даты!необязательный список из одной или нескольких заданных порядковыми номерами дат для исключения из рабочего календаря, таких как государственные праздники"}, "WORKDAY.INTL": {"a": "(нач_дата; число_дней; [выходные]; [праздники])", "d": "Возвращает порядковый номер даты, отстоящей на заданное число рабочих дней вперед или назад от начальной даты с настраиваемыми параметрами выходных дней", "ad": "начальная дата, заданная порядковым номером!количество не выходных и не праздничных дней до или после начальной даты!число или строка, указывающая на выходные дни!необязательный список из одной или нескольких заданных порядковыми номерами дат для исключения из рабочего календаря, таких как государственные праздники"}, "YEAR": {"a": "(дата_в_числовом_формате)", "d": "Возвращает год - целое число от 1900 до 9999.", "ad": "число в коде даты-времени, используемом в Редакторе таблиц"}, "YEARFRAC": {"a": "(нач_дата; кон_дата; [базис])", "d": "Возвращает долю года, которую составляет количество дней между двумя датами (начальной и конечной)", "ad": "порядковый номер начальной даты!порядковый номер конечной даты!используемый способ вычисления дня"}, "BESSELI": {"a": "(x; n)", "d": "Возвращает модифицированную функцию Бесселя In(x)", "ad": "значение, для которого вычисляется функция!порядок функции Бесселя"}, "BESSELJ": {"a": "(x; n)", "d": "Возвращает функцию Бесселя Jn(x)", "ad": "значение, для которого вычисляется функция!порядок функции Бесселя"}, "BESSELK": {"a": "(x; n)", "d": "Возвращает функцию Бесселя Kn(x)", "ad": "значение, для которого вычисляется функция!порядок функции"}, "BESSELY": {"a": "(x; n)", "d": "Возвращает функцию Бесселя Yn(x)", "ad": "значение, для которого вычисляется функция!порядок функции"}, "BIN2DEC": {"a": "(число)", "d": "Преобразует двоичное число в десятичное", "ad": "двоичное число, которое требуется преобразовать"}, "BIN2HEX": {"a": "(число; [разрядность])", "d": "Преобразует двоичное число в шестнадцатеричное", "ad": "двоичное число, которое требуется преобразовать!количество знаков для использования"}, "BIN2OCT": {"a": "(число; [разрядность])", "d": "Преобразует двоичное число в восьмеричное", "ad": "двоичное число, которое требуется преобразовать!количество знаков для использования"}, "BITAND": {"a": "(число1; число2)", "d": "Возвращает побитовое \"и\" двух чисел", "ad": "десятичное представление двоичного числа, для которого требуется выполнить вычисление!десятичное представление двоичного числа, для которого требуется выполнить вычисление"}, "BITLSHIFT": {"a": "(число; сдвиг)", "d": "Возвращает значение числа, сдвинутое влево на число бит, которое задано параметром \"сдвиг\"", "ad": "десятичное представление двоичного числа, для которого требуется выполнить вычисление!количество битов, на которое требуется сдвинуть число влево"}, "BITOR": {"a": "(число1; число2)", "d": "Возвращает побитовое \"или\" двух чисел", "ad": "десятичное представление двоичного числа, для которого требуется выполнить вычисление!десятичное представление двоичного числа, для которого требуется выполнить вычисление"}, "BITRSHIFT": {"a": "(число; сдвиг)", "d": "Возвращает значение числа, сдвинутое вправо на число бит, которое задано параметром \"сдвиг\"", "ad": "десятичное представление двоичного числа, для которого требуется выполнить вычисление!количество битов, на которое требуется сдвинуть число вправо"}, "BITXOR": {"a": "(число1; число2)", "d": "Возвращает побитовое \"исключающее или\" двух чисел", "ad": "десятичное представление двоичного числа, для которого требуется выполнить вычисление!десятичное представление двоичного числа, для которого требуется выполнить вычисление"}, "COMPLEX": {"a": "(действительная_часть; мнимая_часть; [мнимая_единица])", "d": "Преобразует коэффициенты при вещественной и мнимой частях комплексного числа в комплексное число", "ad": "действительная часть комплексного числа!мнимая часть комплексного числа!обозначение мнимой единицы в комплексном числе"}, "CONVERT": {"a": "(число; исх_ед_изм; кон_ед_изм)", "d": "Преобразует значение из одной системы мер в другую", "ad": "преобразуемое значение в исходных единицах измерения!единицы измерения для аргумента \"число\"!единицы измерения для результата"}, "DEC2BIN": {"a": "(число; [разрядность])", "d": "Преобразует десятичное число в двоичное", "ad": "десятичное целое число, которое требуется преобразовать!количество знаков для использования"}, "DEC2HEX": {"a": "(число; [разрядность])", "d": "Преобразует десятичное число в шестнадцатеричное", "ad": "десятичное целое число, которое требуется преобразовать!количество знаков для использования"}, "DEC2OCT": {"a": "(число; [разрядность])", "d": "Преобразует десятичное число в восьмеричное", "ad": "десятичное целое число, которое требуется преобразовать!количество знаков для использования"}, "DELTA": {"a": "(число1; [число2])", "d": "Проверяет равенство двух чисел", "ad": "первое число!второе число"}, "ERF": {"a": "(нижний_предел; [верхний_предел])", "d": "Возвращает функцию ошибки", "ad": "нижний предел интегрирования ФОШ!верхний предел интегрирования ФОШ"}, "ERF.PRECISE": {"a": "(X)", "d": "Возвращает функцию ошибки", "ad": "нижний предел интегрирования ФОШ.ТОЧН"}, "ERFC": {"a": "(x)", "d": "Возвращает дополнительную функцию ошибки", "ad": "нижний предел интегрирования ФОШ"}, "ERFC.PRECISE": {"a": "(X)", "d": "Возвращает дополнительную функцию ошибки", "ad": "нижний предел интегрирования ДФОШ.ТОЧН"}, "GESTEP": {"a": "(число; [порог])", "d": "Проверяет, превышает ли число пороговое значение", "ad": "проверяемое значение!пороговое значение"}, "HEX2BIN": {"a": "(число; [разрядность])", "d": "Преобразует шестнадцатеричное число в двоичное", "ad": "шестнадцатеричное число, которое требуется преобразовать!количество знаков для использования"}, "HEX2DEC": {"a": "(число)", "d": "Преобразует шестнадцатеричное число в десятичное", "ad": "шестнадцатеричное число, которое требуется преобразовать"}, "HEX2OCT": {"a": "(число; [разрядность])", "d": "Преобразует шестнадцатеричное число в восьмеричное", "ad": "шестнадцатеричное число, которое требуется преобразовать!количество знаков для использования"}, "IMABS": {"a": "(компл_число)", "d": "Возвращает абсолютную величину (модуль) комплексного числа", "ad": "комплексное число, абсолютную величину которого требуется получить"}, "IMAGINARY": {"a": "(компл_число)", "d": "Возвращает коэффициент при мнимой части комплексного числа", "ad": "комплексное число, для которого определяется коэффициент при мнимой части"}, "IMARGUMENT": {"a": "(компл_число)", "d": "Возвращает аргумент q, то есть угол, выраженный в радианах", "ad": "комплексное число, для которого определяется аргумент"}, "IMCONJUGATE": {"a": "(компл_число)", "d": "Возвращает комплексно-сопряженное комплексного числа", "ad": "комплексное число, для которого определяется сопряженное комплексное число"}, "IMCOS": {"a": "(компл_число)", "d": "Возвращает косинус комплексного числа", "ad": "комплексное число, для которого вычисляется косинус"}, "IMCOSH": {"a": "(компл_число)", "d": "Возвращает гиперболический косинус комплексного числа", "ad": "комплексное число, для которого требуется найти гиперболический косинус"}, "IMCOT": {"a": "(компл_число)", "d": "Возвращает котангенс комплексного числа", "ad": "комплексное число, для которого требуется найти котангенс"}, "IMCSC": {"a": "(компл_число)", "d": "Возвращает косеканс комплексного числа", "ad": "комплексное число, для которого требуется найти косеканс"}, "IMCSCH": {"a": "(компл_число)", "d": "Возвращает гиперболический косеканс комплексного числа", "ad": "комплексное число, для которого требуется найти гиперболический косеканс"}, "IMDIV": {"a": "(компл_число1; компл_число2)", "d": "Возвращает частное двух комплексных чисел", "ad": "комплексный числитель или делимое!комплексный знаменатель или делитель"}, "IMEXP": {"a": "(компл_число)", "d": "Возвращает экспоненту комплексного числа", "ad": "комплексное число, для которого вычисляется экспонента"}, "IMLN": {"a": "(компл_число)", "d": "Возвращает натуральный логарифм комплексного числа", "ad": "комплексное число, для которого вычисляется натуральный логарифм"}, "IMLOG10": {"a": "(компл_число)", "d": "Возвращает десятичный логарифм комплексного числа", "ad": "комплексное число, для которого вычисляется десятичный логарифм"}, "IMLOG2": {"a": "(компл_число)", "d": "Возвращает двоичный логарифм комплексного числа", "ad": "комплексное число, для которого вычисляется двоичный логарифм"}, "IMPOWER": {"a": "(компл_число; число)", "d": "Возвращает комплексное число, возведенное в степень с целочисленным показателем", "ad": "комплексное число, возводимое в степень!степень, в которую возводится комплексное число"}, "IMPRODUCT": {"a": "(компл_число1; [компл_число2]; ...)", "d": "Возвращает произведение от 1 до 255 комплексных чисел", "ad": "компл_число1, компл_число2,... — от 1 до 255 перемножаемых комплексных чисел."}, "IMREAL": {"a": "(компл_число)", "d": "Возвращает коэффициент при вещественной (действительной) части комплексного числа", "ad": "комплексное число, для которого определяется коэффициент при вещественной (действительной) части"}, "IMSEC": {"a": "(компл_число)", "d": "Возвращает секанс комплексного числа", "ad": "комплексное число, для которого требуется найти секанс"}, "IMSECH": {"a": "(компл_число)", "d": "Возвращает гиперболический секанс комплексного числа", "ad": "комплексное число, для которого требуется найти гиперболический секанс"}, "IMSIN": {"a": "(компл_число)", "d": "Возвращает синус комплексного числа", "ad": "комплексное число, для которого вычисляется синус"}, "IMSINH": {"a": "(компл_число)", "d": "Возвращает гиперболический синус комплексного числа", "ad": "комплексное число, для которого требуется найти гиперболический синус"}, "IMSQRT": {"a": "(компл_число)", "d": "Возвращает значение квадратного корня комплексного числа", "ad": "комплексное число, для которого вычисляется квадратный корень"}, "IMSUB": {"a": "(компл_число1; компл_число2)", "d": "Возвращает разность двух комплексных чисел", "ad": "уменьшаемое комплексное число!вычитаемое комплексное число"}, "IMSUM": {"a": "(компл_число1; [компл_число2]; ...)", "d": "Возвращает сумму комплексных чисел", "ad": "от 1 до 255 суммируемых комплексных чисел"}, "IMTAN": {"a": "(компл_число)", "d": "Возвращает тангенс комплексного числа", "ad": "комплексное число, для которого требуется найти тангенс"}, "OCT2BIN": {"a": "(число; [разрядность])", "d": "Преобразует восьмеричное число в двоичное", "ad": "восьмеричное число, которое требуется преобразовать!количество знаков для использования"}, "OCT2DEC": {"a": "(число)", "d": "Преобразует восьмеричное число в десятичное", "ad": "восьмеричное число, которое требуется преобразовать"}, "OCT2HEX": {"a": "(число; [разрядность])", "d": "Преобразует восьмеричное число в шестнадцатеричное", "ad": "восьмеричное число, которое требуется преобразовать!количество знаков для использования"}, "DAVERAGE": {"a": "(база_данных; поле; критерий)", "d": "Возвращает среднее всех значений столбца списка или базы данных, которые удовлетворяют заданным условиям", "ad": "диапаз<PERSON><PERSON> ячеек, формирующих список или базу данных. База данных представляет собой список связанных данных!заголовок столбца в двойных кавычках или число, представляющее номер столбца в списке!диапазон, содержащий задаваемые условия. Диапазон включает заголовок столбца и одну ячейку с условием под заголовком"}, "DCOUNT": {"a": "(база_данных; поле; критерий)", "d": "Подсчитывает количество числовых ячеек в выборке из заданной базы данных по заданному критерию", "ad": "диапазон, содержащий базу данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовки столбцов и одну ячейку с условием."}, "DCOUNTA": {"a": "(база_данных; поле; критерий)", "d": "Подсчитывает количество непустых ячеек в выборке из заданной базы данных по заданному критерию", "ad": "диапазон, составляющий базу данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку  условием."}, "DGET": {"a": "(база_данных; поле; критерий)", "d": "Извлекает из базы данных одну запись, удовлетворяющую заданному критерию", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DMAX": {"a": "(база_данных; поле; критерий)", "d": "Возвращает максимальное значение поля (столбца) записей базы данных, удовлетворяющих указанным условиям", "ad": "диапазон ячеек, составляющих список или базу данных. База данных представляет собой список связанных данных!заголовок столбца в двойных кавычках или число, представляющее номер столбца в списке!диапазон, содержащий задаваемые условия. Диапазон включает заголовок столбца и одну ячейку с условием под заголовком"}, "DMIN": {"a": "(база_данных; поле; критерий)", "d": "Возвращает минимальное значение среди выделенных фрагментов базы данных", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DPRODUCT": {"a": "(база_данных; поле; критерий)", "d": "Перемножает значения определенных полей записей базы данных, удовлетворяющих критерию", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DSTDEV": {"a": "(база_данных; поле; критерий)", "d": "Оценивает стандартное отклонение по выборке из выделенной части базы данных", "ad": "диапазон базы данных . База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DSTDEVP": {"a": "(база_данных; поле; критерий)", "d": "Вычисляет стандартное отклонение по генеральной совокупности из выделенной части базы данных", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DSUM": {"a": "(база_данных; поле; критерий)", "d": "Суммирует числа в поле (столбце) записей базы данных, удовлетворяющих условию", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DVAR": {"a": "(база_данных; поле; критерий)", "d": "Оценивает дисперсию по выборке из выделенной части базы данных", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DVARP": {"a": "(база_данных; поле; критерий)", "d": "Вычисляет дисперсию по генеральной совокупности из выделенной части базы данных", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "CHAR": {"a": "(число)", "d": "Возвращает символ с заданным кодом", "ad": "число в интервале от 1 до 255, указывающее нужный символ"}, "CLEAN": {"a": "(текст)", "d": "Удаляет все непечатаемые знаки из текста", "ad": "любая информация на рабочем листе, из которой удаляются непечатаемые знаки"}, "CODE": {"a": "(текст)", "d": "Возвращает числовой код первого символа в текстовой строке", "ad": "текст, в котором требуется узнать код первого символа"}, "CONCATENATE": {"a": "(текст1; [текст2]; ...)", "d": "Объединяет несколько текстовых строк в одну", "ad": "от 1 до 255 текстовых строк, которые следует объединить в одну строку; могут быть строками, числами или ссылками на отдельные ячейки"}, "CONCAT": {"a": "(текст1; ...)", "d": "Объединяет список или диапазон строк текста", "ad": "От 1 до 254 текстовых строк или диапазонов можно объединить в одну строку"}, "DOLLAR": {"a": "(число; [число_знаков])", "d": "Преобразует число в текст, используя денежный формат", "ad": "число либо ссылка на ячейку, содержащую число, либо формула, вычисление которой дает число!число цифр справа от десятичной запятой. При необходимости округляется; если опущено, число знаков после запятой равно 2"}, "EXACT": {"a": "(текст1; текст2)", "d": "Проверяет идентичность двух строк текста и возвращает значение ИСТИНА или ЛОЖЬ. Прописные и строчные буквы различаются", "ad": "первая текстовая строка!вторая текстовая строка"}, "FIND": {"a": "(искомый_текст; просматриваемый_текст; [нач_позиция])", "d": "Возвращает позицию начала искомой строки текста в содержащей ее строке текста. Прописные и строчные буквы различаются", "ad": "строка, которую требуется найти. Для поиска первого знака укажите пустую строку (две двойных кавычки); использование знаков подстановки не допускается!строка, содержащая искомый текст!позиция, с которой нужно начать поиск. Первый знак в параметре 'просматриваемый_текст' имеет позицию номер 1. Если значение не указано, начальная позиция принимается равной 1"}, "FINDB": {"a": "(искомый_текст;просматриваемый_текст;[нач_позиция])", "d": "Находит заданную подстроку (искомый_текст) внутри строки (просматриваемый_текст), предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "строка, которую требуется найти. Для поиска первого знака укажите пустую строку (две двойных кавычки); использование знаков подстановки не допускается!строка, содержащая искомый текст!позиция, с которой нужно начать поиск. Первый знак в параметре 'просматриваемый_текст' имеет позицию номер 1. Если значение не указано, начальная позиция принимается равной 1"}, "FIXED": {"a": "(число; [чис<PERSON><PERSON>_знаков]; [без_разделителей])", "d": "Форматирует число и преобразует его в текст с заданным числом десятичных знаков", "ad": "число, которое округляется и преобразуется в текст!число цифр справа от десятичной запятой. По умолчанию принимается равным 2!логическое значение, определяющее, должны (ИСТИНА) или не должны (ЛОЖЬ) разделители разрядов присутствовать в результате"}, "LEFT": {"a": "(текст; [количество_знаков])", "d": "Возвращает указанное количество знаков с начала строки текста", "ad": "строка текста, содержащая знаки, которые нужно извлечь!количество знаков, которое нужно извлечь; если не указано, принимается равным 1"}, "LEFTB": {"a": "(текст;[число_знаков])", "d": "Извлекает подстроку из заданной строки, начиная с левого символа, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "строка текста, содержащая знаки, которые нужно извлечь!количество знаков, которое нужно извлечь; если не указано, принимается равным 1"}, "LEN": {"a": "(текст)", "d": "Возвращает количество знаков в текстовой строке", "ad": "строка, длину которой следует определить. Пробелы считаются знаками"}, "LENB": {"a": "(текст)", "d": "Анализирует заданную строку и возвращает количество символов, которые она содержит, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "строка, длину которой следует определить. Пробелы считаются знаками"}, "LOWER": {"a": "(текст)", "d": "Делает все буквы в строке текста строчными", "ad": "строка, буквы которой требуется преобразовать в строчные. Знаки, не являющиеся буквами, не изменяются"}, "MID": {"a": "(текст; начальная_позиция; количество_знаков)", "d": "Возвращает заданное число знаков из строки текста, начиная с указанной позиции", "ad": "текстовая строка, из которой следует извлечь знаки!позиция, начиная с которой следует извлечь знаки. Первый знак в тексте имеет позицию 1!количество знаков, которое следует извлечь из текста"}, "MIDB": {"a": "(текст;начальная_позиция;число_знаков)", "d": "Извлекает символы из заданной строки, начиная с любого места, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "текстовая строка, из которой следует извлечь знаки!позиция, начиная с которой следует извлечь знаки. Первый знак в тексте имеет позицию 1!количество знаков, которое следует извлечь из текста"}, "NUMBERVALUE": {"a": "(текст; [десятичный_разделитель]; [разделитель_разрядов])", "d": "Преобразует текст в число без учета языкового стандарта", "ad": "строка, представляющая число, которое требуется преобразовать!знак, используемый в строке в качестве десятичного разделителя!знак, используемый в строке в качестве разделителя групп разрядов"}, "PROPER": {"a": "(текст)", "d": "Начинает текстовую строку с заглавной буквы; делает прописной первую букву в каждом слове текста, преобразуя все другие буквы в строчные", "ad": "текстовая строка, заключенная в кавычки, формула, возвращающая текст, либо ссылка на ячейку, содержащую текст, в котором некоторые буквы заменяются на прописные"}, "REPLACE": {"a": "(старый_текст; нач_поз; число_знаков; новый_текст)", "d": "Заменяет часть строки текста на другую строку", "ad": "строка, в которой нужно заменить некоторые знаки!позиция знака в строке 'старый_текст', начиная с которого нужно заменить часть этой строки на 'новый_текст'!число знаков в строке 'старый_текст', которое нужно заменить на знаки строки 'новый_текст'!строка, которая заменит соответствующую подстроку строки 'старый_текст'"}, "REPLACEB": {"a": "(стар_текст;начальная_позиция;число_знаков;нов_текст)", "d": "Заменяет ряд символов на новый, с учетом заданного количества символов и начальной позиции, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "строка, в которой нужно заменить некоторые знаки!позиция знака в строке 'стар_текст', начиная с которого нужно заменить часть этой строки на 'нов_текст'!число знаков в строке 'стар_текст', которое нужно заменить на знаки строки 'нов_текст'!строка, которая заменит соответствующую подстроку строки 'стар_текст'"}, "REPT": {"a": "(текст; число_повторений)", "d": "Повторяет текст заданное число раз", "ad": "повторяемый текст!положительное число, задающее количество требуемых повторений текста"}, "RIGHT": {"a": "(текст; [число_знаков])", "d": "Возвращает указанное число знаков с конца строки текста", "ad": "строка текста, содержащая знаки, которые нужно извлечь!число знаков, которое нужно извлечь; если не указано, принимается равным 1"}, "RIGHTB": {"a": "(текст;[число_знаков])", "d": "Извлекает подстроку из заданной строки, начиная с крайнего правого символа, согласно заданному количеству символов, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "строка текста, содержащая знаки, которые нужно извлечь!число знаков, которое нужно извлечь; если не указано, принимается равным 1"}, "SEARCH": {"a": "(искомый_текст; текст_для_поиска; [нач_позиция])", "d": "Возвращает позицию первого вхождения знака или строки текста при чтении слева направо; прописные и строчные буквы не различаются", "ad": "строка, которую требуется найти. Допускается использование знаков  ? и *; для поиска самих знаков * и ? используйте синтаксис ~? и ~*!строка, в которой нужно найти искомый текст!позиция в тексте для поиска (считая слева), с которой следует начать поиск. Если не указана, используется значение 1"}, "SEARCHB": {"a": "(искомый_текст;просматриваемый_текст;[начальная_позиция])", "d": "Возвращает местоположение заданной подстроки в строке, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "строка, которую требуется найти. Допускается использование знаков  ? и *; для поиска самих знаков * и ? используйте синтаксис ~? и ~*!строка, в которой нужно найти искомый текст!позиция в тексте для поиска (считая слева), с которой следует начать поиск. Если не указана, используется значение 1"}, "SUBSTITUTE": {"a": "(текст; стар_текст; нов_текст; [номер_вхождения])", "d": "Заменяет новым текстом старый текст в текстовой строке", "ad": "либо текст, либо ссылка на ячейку, содержащую текст, в котором подставляются знаки!заменяемый текст, с учетом регистра знаков!строка, которой заменяется старый_текст!номер вхождения стар_текст, который следует заменить на нов_текст. Если опущено, заменяется каждое вхождение стар_текст"}, "T": {"a": "(значение)", "d": "Проверяет, является ли значение текстовым, и возвращает его текст, если да, либо две кавычки (пустой текст), если нет", "ad": "проверяемое значение"}, "TEXT": {"a": "(значение; format_text)", "d": "Преобразует значение в текст в определенном формате", "ad": "число, формула, определяющая числовое значение, или ссылка на ячейку, содержащая числовое значение!числовой формат в текстовом формате из поля \"Категория\" на вкладке \"число\" в диалоговом окне \"формат ячеек\""}, "TEXTJOIN": {"a": "(разделитель; пропускать_пустые; текст1; ...)", "d": "Объединяет список или диапазон строк текста с помощью разделителя", "ad": "Символ или строку необходимо вставлять между всеми текстовыми элементами!При выборе значения ИСТИНА (по умолчанию) пустые ячейки пропускаются!Объединить можно от 1 до 252 текстовых строк или диапазонов"}, "TRIM": {"a": "(текст)", "d": "Удаляет из текста лишние пробелы (кроме одиночных пробелов между словами)", "ad": "текст, из которого удаляются пробелы"}, "UNICHAR": {"a": "(число)", "d": "Возвращает знак Юникода, соответствующий указанному числу", "ad": "число, соответствующее знаку Юникода"}, "UNICODE": {"a": "(текст)", "d": "Возвращает число (код знака), соответствующее первому знаку в тексте", "ad": "знак, для которого требуется получить значение Юникода"}, "UPPER": {"a": "(текст)", "d": "Делает все буквы в строке текста прописными", "ad": "строка, буквы которой требуется преобразовать в прописные; может быть ссылкой или строкой текста"}, "VALUE": {"a": "(текст)", "d": "Преобразует текстовый аргумент в число", "ad": "текст в кавычках или ссылка на ячейку, содержащую текст который нужно преобразовать"}, "AVEDEV": {"a": "(число1; [число2]; ...)", "d": "Возвращает среднее абсолютных значений отклонений точек данных от среднего. Аргументами могут являться числа, имена, массивы или ссылки на числовые значения", "ad": "от 1 до 255 аргументов, для которых определяется среднее абсолютных отклонений"}, "AVERAGE": {"a": "(число1; [число2]; ...)", "d": "Возвращает среднее арифметическое своих аргументов, которые могут быть числами, именами, массивами или ссылками на ячейки с числами", "ad": "от 1 до 255 числовых аргументов, для которых вычисляется среднее"}, "AVERAGEA": {"a": "(значение1; [значение2]; ...)", "d": "Возвращает среднее арифметическое указанных аргументов. При этом текстовые и ложные логические значения считаются равными 0, а истинные логические значения считаются равными 1. В качестве аргументов могут быть указаны числа, имена, массивы или ссылки", "ad": "от 1 до 255 аргументов, для которых требуется определить среднее"}, "AVERAGEIF": {"a": "(диапазон; условие; [диапазон_усреднения])", "d": "Вычисляет среднее (арифметическое) для ячеек, заданных указанным условием", "ad": "диапазон обрабатываемых ячеек!условие в форме числа, выражения или текста, определяющее суммируемые ячейки!фактические ячейки для расчета среднего значения. Если этот аргумент опущен, будут использоваться ячейки, заданные аргументом \"диапазон\""}, "AVERAGEIFS": {"a": "(диапазон_усреднения; диапазон_условия; условие; ...)", "d": "Вычисляет среднее (арифметическое) для ячеек, удовлетворяющие заданному набору условий", "ad": "фактические ячейки, используемые для расчета среднего значения!диапазон ячеек, проверяемых на соответствие определенному условию!условие в форме числа, выражения или текста, определяющее ячейки, по которым будет выполняться расчет среднего значения"}, "BETADIST": {"a": "(x; альфа; бета; [А]; [B])", "d": "Возвращает интегральную функцию плотности бета-вероятности", "ad": "значение в интервале между A и B, для которого вычисляется функция!параметр распределения; должен быть положительной величиной!параметр распределения; должен быть положительной величиной!необязательная нижняя граница интервала изменения x. Если опущено, A = 0!необязательная верхняя граница интервала изменения x. Если опущено, В = 1"}, "BETAINV": {"a": "(вероятность; альфа; бета; [А]; [B])", "d": "Возвращает обратную функцию к интегральной функции плотности бета-вероятности", "ad": "вероятность, связанная с бета распределением!параметр распределения; должен быть положительной величиной!параметр распределения; должен быть положительной величиной!необязательная нижняя граница интервала изменения x. Если опущено, A = 0!необязательная верхняя граница интервала изменения x. Если опущено, В = 1"}, "BETA.DIST": {"a": "(x; альфа; бета; интегральная; [А]; [B])", "d": "Возвращает функцию плотности бета-вероятности", "ad": "значение в интервале между A и B, для которого вычисляется функция!параметр распределения; должен быть положительной величиной!параметр распределения; должен быть положительной величиной!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)!необязательная нижняя граница интервала изменения x. Если опущено, A = 0!необязательная верхняя граница интервала изменения x. Если опущено, В = 1"}, "BETA.INV": {"a": "(вероятность; альфа; бета; [А]; [B])", "d": "Возвращает обратную функцию к интегральной функции плотности бета-вероятности (БЕТА.РАСП)", "ad": "вероятность, связанная с бета распределением!параметр распределения; должен быть положительной величиной!параметр распределения; должен быть положительной величиной!необязательная нижняя граница интервала изменения x. Если опущено, A = 0!необязательная верхняя граница интервала изменения x. Если опущено, В = 1"}, "BINOMDIST": {"a": "(число_успехов; число_испытаний; вероятность_успеха; интегральная)", "d": "Возвращает отдельное значение биномиального распределения", "ad": "число успешных испытаний!число независимых испытаний!вероятность успеха каждого испытания!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "BINOM.DIST": {"a": "(число_успехов; число_испытаний; вероятность_успеха; интегральная)", "d": "Возвращает отдельное значение биномиального распределения", "ad": "число успешных испытаний!число независимых испытаний!вероятность успеха каждого испытания!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "BINOM.DIST.RANGE": {"a": "(испытания; вероятность_успеха; число_успехов; [число_успехов2])", "d": "Возвращает вероятность результата испытания с использованием биномиального распределения", "ad": "число независимых испытаний!вероятность успеха в каждом испытании!количество успешных испытаний!если этот параметр указан, функция возвращает вероятность того, что число успешных испытаний будет находиться в диапазоне между значениями число_успехов и число_успехов2"}, "BINOM.INV": {"a": "(число_испытаний; вероятность_успеха; альфа)", "d": "Возвращает наименьшее значение, для которого биномиальная функция распределения больше или равна заданного значения", "ad": "число испытаний Бернулли!вероятность успеха в каждом испытании!значение критерия, число в диапазоне от 0 до 1"}, "CHIDIST": {"a": "(x; степени_свободы)", "d": "Возвращает одностороннюю вероятность распределения хи-квадрат", "ad": "значение, для которого требуется вычислить распределение, неотрицательное число!число степеней свободы - число от 1 до 10^10, исключая 10^10"}, "CHIINV": {"a": "(вероятность; степени_свободы)", "d": "Возвращает значение обратное к односторонней вероятности распределения хи-квадрат", "ad": "вероятность, связанная с распределением хи-квадрат, значение в диапазоне от 0 до 1!число степеней свободы - число от 1 до 10^10, исключая 10^10"}, "CHITEST": {"a": "(фактический_интервал; ожидаемый_интервал)", "d": "Возвращает тест на независимость: значение распределения хи-квадрат для статистического распределения и соответствующего числа степеней свободы", "ad": "диапазон, содержащий наблюдения, подлежащие сравнению с ожидаемыми значениями!диапазон, содержащий отношение произведений итогов по строкам и столбцам к общему итогу"}, "CHISQ.DIST": {"a": "(x; степени_свободы; интегральная)", "d": "Возвращает левостороннюю вероятность распределения хи-квадрат", "ad": "значение, для которого требуется вычислить распределение, неотрицательное число!число степеней свободы - число от 1 до 10^10, исключая 10^10!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "CHISQ.DIST.RT": {"a": "(x; степени_свободы)", "d": "Возвращает правостороннюю вероятность распределения хи-квадрат", "ad": "значение, для которого требуется вычислить распределение, неотрицательное число!число степеней свободы - число от 1 до 10^10, исключая 10^10"}, "CHISQ.INV": {"a": "(вероятность; степени_свободы)", "d": "Возвращает значение, обратное к левосторонней вероятности распределения хи-квадрат", "ad": "вероятность, связанная с распределением хи-квадрат, значение в диапазоне от 0 до 1!число степеней свободы - число от 1 до 10^10, исключая 10^10"}, "CHISQ.INV.RT": {"a": "(вероятность; степени_свободы)", "d": "Возвращает значение, обратное к правосторонней вероятности распределения хи-квадрат", "ad": "вероятность, связанная с распределением хи-квадрат, значение в диапазоне от 0 до 1!число степеней свободы - число от 1 до 10^10, исключая 10^10"}, "CHISQ.TEST": {"a": "(фактический_интервал; ожидаемый_интервал)", "d": "Возвращает тест на независимость: значение распределения хи-квадрат для статистического распределения и соответствующего числа степеней свободы", "ad": "диапазон, содержащий наблюдения, подлежащие сравнению с ожидаемыми значениями!диапазон, содержащий отношение произведений итогов по строкам и столбцам к общему итогу"}, "CONFIDENCE": {"a": "(альфа; станд_откл; размер)", "d": "Возвращает доверительный интервал для среднего генеральной совокупности, используя нормальное распределение", "ad": "уровень значимости, используемый для вычисления доверительного уровня - число, большее 0 и меньшее 1!стандартное отклонение генеральной совокупности для интервала данных (предполагается известным). Должно быть больше нуля!размер выборки"}, "CONFIDENCE.NORM": {"a": "(альфа; станд_откл; размер)", "d": "Возвращает доверительный интервал для среднего генеральной совокупности с использованием нормального распределения", "ad": "уровень значимости, используемый для вычисления доверительного уровня - число, большее 0 и меньшее 1!стандартное отклонение генеральной совокупности для интервала данных (предполагается известным). Должно быть больше нуля!размер выборки"}, "CONFIDENCE.T": {"a": "(альфа; станд_откл; размер)", "d": "Возвращает доверительный интервал для среднего генеральной совокупности с использованием распределения Стьюдента", "ad": "уровень значимости, используемый для вычисления доверительного уровня - число, большее 0 и меньшее 1!стандартное отклонение генеральной совокупности для интервала данных (предполагается известным). Должно быть больше нуля!размер выборки"}, "CORREL": {"a": "(массив1; массив2)", "d": "Возвращает коэффициент корреляции между двумя множествами данных", "ad": "первый диапазон значений. Значениями могут быть числа, имена, массивы или ссылки с именами!второй диапазон значений. Значениями могут быть числа, имена, массивы или ссылки с именами"}, "COUNT": {"a": "(значение1; [значение2]; ...)", "d": "Подсчитывает количество ячеек в диапазоне, который содержит числа", "ad": "от 1 до 255 аргументов, которые могут содержать или ссылаться на данные различных типов, но учитываются только числовые значения"}, "COUNTA": {"a": "(значение1; [значение2]; ...)", "d": "Подсчитывает количество непустых ячеек в диапазоне", "ad": "от 1 до 255 аргументов любого типа, количество которых требуется определить"}, "COUNTBLANK": {"a": "(диапазон)", "d": "Подсчитывает количество пустых ячеек в диапазоне", "ad": "диапазон, в котором требуется определить количество пустых ячеек"}, "COUNTIFS": {"a": "(диапазон_условия; условие; ...)", "d": "Подсчитывает количество ячеек, удовлетворяющих заданному набору условий", "ad": "диапазон ячеек, проверяемый на соответствие определенному условию!условие в форме числа, выражения или текста, определяющее подсчитываемые ячейки"}, "COUNTIF": {"a": "(диа<PERSON><PERSON><PERSON><PERSON>н; критерий)", "d": "Подсчитывает количество непустых ячеек в диапазоне, удовлетворяющих заданному условию", "ad": "диапазон, в котором подсчитывается количество непустых ячеек!условие в форме числа, выражения или текста, который определяет, какие ячейки надо подсчитывать"}, "COVAR": {"a": "(массив1; массив2)", "d": "Возвращает ковариацию, среднее попарных произведений отклонений", "ad": "первый диапазон целых чисел - числа, массивы или ссылки на ячейки, содержащие числа!второй диапазон целых чисел - числа, массивы или ссылки на ячейки, содержащие числа"}, "COVARIANCE.P": {"a": "(массив1; массив2)", "d": "Возвращает ковариацию генеральной совокупности, среднее попарных произведений отклонений", "ad": "первый диапазон целых чисел - числа, массивы или ссылки на ячейки, содержащие числа!второй диапазон целых чисел - числа, массивы или ссылки на ячейки, содержащие числа"}, "COVARIANCE.S": {"a": "(массив1; массив2)", "d": "Возвращает ковариацию выборки, среднее попарных произведений отклонений", "ad": "первый диапазон целых чисел - числа, массивы или ссылки на ячейки, содержащие числа!второй диапазон целых чисел - числа, массивы или ссылки на ячейки, содержащие числа"}, "CRITBINOM": {"a": "(число_испытаний; вероятность_успеха; альфа)", "d": "Возвращает наименьшее значение, для которого биномиальная функция распределения больше или равна заданного значения", "ad": "число испытаний Бернулли!вероятность успеха в каждом испытании!значение критерия, число в диапазоне от 0 до 1"}, "DEVSQ": {"a": "(число1; [число2]; ...)", "d": "Возвращает сумму квадратов отклонений точек данных от среднего по выборке", "ad": "от 1 до 255 аргум<PERSON>нтов, массивов или ссылок на массивы, для которых вычисляется сумма квадратов отклонений"}, "EXPONDIST": {"a": "(x; лямбда; интегральная)", "d": "Возвращает экспоненциальное распределение", "ad": "значение функции, неотрицательное число!значение параметра, положительное число!логическое значение, определяющее возвращаемую функцию: интегральную функцию распределения, если ИСТИНА; функцию плотности распределения вероятности, если ЛОЖЬ"}, "EXPON.DIST": {"a": "(x; лямбда; интегральная)", "d": "Возвращает экспоненциальное распределение", "ad": "значение функции, неотрицательное число!значение параметра, положительное число!логическое значение, определяющее возвращаемую функцию: интегральную функцию распределения, если ИСТИНА; функцию плотности распределения вероятности, если ЛОЖЬ"}, "FDIST": {"a": "(x; степени_свободы1; степени_свободы2)", "d": "Возвращает одностороннее F-распределение вероятности (степень отклонения) для двух наборов данных", "ad": "значение, для которого вычисляется функция, неотрицательное число!число степеней свободы - число от 1 до 10^10, исключая 10^10!знаменатель степеней свободы - число от 1 до 10^10, исключая 10^10"}, "FINV": {"a": "(вероятность; степени_свободы1; степени_свободы2)", "d": "Возвращает обратное значение для одностороннего F-распределения вероятностей: если p = FРАСП(x,...), то FРАСПОБР(p,...) = x", "ad": "вероятность, связанная с F-интегральным распределением, число в диапазоне от 0 до 1 включительно!числитель степеней свободы - число от 1 до 10^10, исключая 10^10!знаменатель степеней свободы - число от 1 до 10^10, исключая 10^10"}, "FTEST": {"a": "(массив1; массив2)", "d": "Возвращает результат F-теста, двустороннюю вероятность сходства двух совокупностей", "ad": "первый массив или диапазон - числа, массивы или ссылки на ячейки, содержащие числа (пробелы игнорируются)!второй массив или диапазон - числа, массивы или ссылки на ячейки, содержащие числа (пробелы игнорируются)"}, "F.DIST": {"a": "(x; степени_свободы1; степени_свободы2; интегральная)", "d": "Возвращает (левостороннее) F-распределение вероятности (степень отклонения) для двух наборов данных", "ad": "значение, для которого вычисляется функция, неотрицательное число!число степеней свободы - число от 1 до 10^10, исключая 10^10!знаменатель степеней свободы - число от 1 до 10^10, исключая 10^10!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "F.DIST.RT": {"a": "(x; степени_свободы1; степени_свободы2)", "d": "Возвращает (правостороннее) F-распределение вероятности (степень отклонения) для двух наборов данных", "ad": "значение, для которого вычисляется функция, неотрицательное число!число степеней свободы - число от 1 до 10^10, исключая 10^10!знаменатель степеней свободы - число от 1 до 10^10, исключая 10^10"}, "F.INV": {"a": "(вероятность; степени_свободы1; степени_свободы2)", "d": "Возвращает обратное значение для (левостороннего) F-распределения вероятностей: если p = F.РАСП(x,...), то F.ОБР(p,...) = x", "ad": "вероятность, связанная с F-интегральным распределением, число в диапазоне от 0 до 1 включительно!числитель степеней свободы - число от 1 до 10^10, исключая 10^10!знаменатель степеней свободы - число от 1 до 10^10, исключая 10^10"}, "F.INV.RT": {"a": "(вероятность; степени_свободы1; степени_свободы2)", "d": "Возвращает обратное значение для (правостороннего) F-распределения вероятностей: если p = F.РАСП.ПХ(x,...), то F.ОБР(p,...) = x", "ad": "вероятность, связанная с F-интегральным распределением, число в диапазоне от 0 до 1 включительно!числитель степеней свободы - число от 1 до 10^10, исключая 10^10!знаменатель степеней свободы - число от 1 до 10^10, исключая 10^10"}, "F.TEST": {"a": "(массив1; массив2)", "d": "Возвращает результат F-теста, двустороннюю вероятность сходства двух совокупностей", "ad": "первый массив или диапазон - числа, массивы или ссылки на ячейки, содержащие числа (пробелы игнорируются)!второй массив или диапазон - числа, массивы или ссылки на ячейки, содержащие числа (пробелы игнорируются)"}, "FISHER": {"a": "(x)", "d": "Возвращает преобразование Фишера", "ad": "числовое значение, которое требуется преобразовать, целое число в интервале от -1 до 1, исключая концы"}, "FISHERINV": {"a": "(y)", "d": "Возвращает обратное преобразование Фишера: если y = ФИШЕР(x), то ФИШЕРОБР(y) = x", "ad": "значение, для которого производится обратное преобразование"}, "FORECAST": {"a": "(x; известные_значения_y; известные_значения_x)", "d": "Рассчитывает (или прогнозирует) будущее значение на линейном тренде на основании имеющихся значений", "ad": "точка данных, для которой необходимо получить прогнозное значение. Она должна быть числовым значением!зависимый массив или диапазон числовых данных!независимый массив или диапазон числовых данных. Дисперсия значений \"Известное_значение_x\" не должна быть равна нулю"}, "FORECAST.ETS": {"a": "(целевая_дата; значения; временная_шкала; [сезонность]; [заполнение_данных]; [агрегирование])", "d": "Возвращает прогнозируемое значение на определенную целевую дату в будущем методом экспоненциального сглаживания.", "ad": "точка данных, для которой Редактор таблиц прогнозирует значение. Должна соответствовать значениям на временной шкале.!массив или диапазон прогнозируемых числовых данных.!независимый массив или диапазон числовых данных. Интервалы между датами (которые не могут быть нулевыми) на временной шкале должны быть равными.!является необязательным числовым значением, которое указывает на сезонный шаблон. Значение по умолчанию, которое равно 1, указывает на сезонность и определяется автоматически.!необязательное значение для обработки недостающих значений. Значение по умолчанию, равное 1, заменяет недостающие значения соседними, а равное 0 — нулями.!необязательное значение для агрегации нескольких значений с одинаковой меткой времени. Если значение не указано, Редактор таблиц использует средние значения."}, "FORECAST.ETS.CONFINT": {"a": "(целевая_дата; значения; временная_шкала; [вероятность]; [сезонность]; [заполнение_данных]; [агрегирование])", "d": "Возвращает доверительный интервал прогнозируемого значения на определенную целевую дату.", "ad": "точка данных, для которой Редактор таблиц прогнозирует значение. Должна соответствовать значениям на временной шкале.!массив или диапазон прогнозируемых числовых данных.!независимый массив или диапазон числовых данных. Интервалы между датами (которые не могут быть нулевыми) на временной шкале должны быть равными.!число от 0 до 1, соответствующее вероятности для вычисленного доверительного интервала. Значение по умолчанию .95.!является необязательным числовым значением, которое указывает на сезонный шаблон. Значение по умолчанию, которое равно 1, указывает на сезонность и определяется автоматически.!необязательное значение для обработки недостающих значений. Значение по умолчанию, равное 1, заменяет недостающие значения соседними, а равное 0 — нулями.!необязательное значение для агрегации нескольких значений с одинаковой меткой времени. Если значение не указано, Редактор таблиц использует средние значения."}, "FORECAST.ETS.SEASONALITY": {"a": "(значения; временная_шкала; [заполнение_данных]; [агрегирование])", "d": "Возвращает продолжительность повторяющегося фрагмента, выявленного приложением в указанном временном ряду.", "ad": "массив или диапазон прогнозируемых числовых данных!независимый массив или диапазон числовых данных. Интервалы между датами (которые не могут быть нулевыми) на временной шкале должны быть равными.!необязательное значение для обработки недостающих значений. Значение по умолчанию, равное 1, заменяет недостающие значения соседними, а равное 0 — нулями.!необязательное числовое значение для агрегирования значений с одинаковой меткой времени. Если оно не указано, Редактор таблиц использует среднее значение."}, "FORECAST.ETS.STAT": {"a": "(значения; временная_шкала; тип_статистики; [сезонность]; [заполнение_данных]; [агрегирование])", "d": "Возвращает запрошенную статистику для прогноза.", "ad": "массив или диапазон прогнозируемых числовых данных.!независимый массив или диапазон числовых данных. Интервалы между датами (которые не могут быть нулевыми) на временной шкале должны быть равными.!число от 1 до 8, указывающее на статистику, которую Редакторе таблиц вернет для вычисляемого прогноза. !необязательное числовое значение, указывающее на сезонную продолжительность. Значение по умолчанию, равное 1, указывает на сезонность и определяется автоматически.!необязательное числовое значение для обработки недостающих значений. Значение по умолчанию, равное 1, заменяет недостающие значения на соседние. Значение, равное 0, заменяет значения нулями.!необязательное числовое значение для агрегирования нескольких значений с одинаковой меткой времени. Если значение не указано, Редактор таблиц использует средние значения."}, "FORECAST.LINEAR": {"a": "(x; известные_значения_y; известные_значения_x)", "d": "Вычисляет или прогнозирует будущее значение линейного тренда, используя имеющиеся значения", "ad": "элемент данных, для которого прогнозируется значение (должен быть числовым значением)!зависимый массив или диапазон числовых данных!независимый массив или диапазон числовых данных. Дисперсия известных значений x не должна быть нулевой"}, "FREQUENCY": {"a": "(массив_данных; массив_интервалов)", "d": "Вычисляет распределение значений по интервалам и возвращает вертикальный массив, содержащий на один элемент больше, чем массив интервалов", "ad": "массив или ссылка на множество данных, для которых вычисляются частоты (пробелы и текст не учитываются)!массив интервалов или ссылка на интервалы, в которых группируются значения из массива данных"}, "GAMMA": {"a": "(x)", "d": "Возвращает значение гамма-функции", "ad": "значение, для которого требуется вычислить гамма-функцию"}, "GAMMADIST": {"a": "(x; альфа; бета; интегральная)", "d": "Возвращает гамма-распределение", "ad": "значение, для которого требуется вычислить распределение, неотрицательное число!параметр распределения, положительное число!параметр распределения, положительное число. Если бета=1, то ГАММАРАСП возвращает стандартное гамма распределение!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ или отсутствие значения)"}, "GAMMA.DIST": {"a": "(x; альфа; бета; интегральная)", "d": "Возвращает гамма-распределение", "ad": "значение, для которого требуется вычислить распределение, неотрицательное число!параметр распределения, положительное число!параметр распределения, положительное число. Если бета=1, функция ГАММА.РАСП возвращает стандартное гамма распределение!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ или отсутствие значения)"}, "GAMMAINV": {"a": "(вероятность; альфа; бета)", "d": "Возвращает обратное гамма-распределение", "ad": "вероятность, связанная с гамма-распределением, число в диапазоне от 0 до 1, включительно!параметр распределения, положительное число!параметр распределения, положительное число. Если бета=1, то ГАММАОБР возвращает стандартное гамма-распределение"}, "GAMMA.INV": {"a": "(вероятность; альфа; бета)", "d": "Возвращает обратное интегральное гамма-распределение: если p = ГАММА.РАСП(x,...), то ГАММА.ОБР(p,...) = x", "ad": "вероятность, связанная с гамма-распределением, число в диапазоне от 0 до 1 включительно!параметр распределения, положительное число!параметр распределения, положительное число. Если бета=1, то ГАММА.ОБР возвращает обратное стандартное гамма-распределение"}, "GAMMALN": {"a": "(x)", "d": "Возвращает натуральный логарифм гамма-функции", "ad": "значение, для которого вычисляется ГАММАНЛОГ, положительное число"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Возвращает натуральный логарифм гамма-функции", "ad": "значение, для которого вычисляется ГАММАНЛОГ.ТОЧН, положительное число"}, "GAUSS": {"a": "(x)", "d": "Возвращает число, на 0,5 меньшее, чем стандартное нормальное интегральное распределение", "ad": "значение, для которого строится распределение"}, "GEOMEAN": {"a": "(число1; [число2]; ...)", "d": "Возвращает среднее геометрическое для массива или диапазона из положительных чисел", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых вычисляется среднее"}, "GROWTH": {"a": "(известные_значения_y; [известные_значения_x]; [новые_значения_x]; [конст])", "d": "Возвращает значения в соответствии с экспоненциальным трендом", "ad": "множество значений y, которые уже известны для соотношения y=b*m^x, массив или диапазон положительных чисел!необязательное множество значений x, для которых, возможно, уже известно соотношение y = b*m^x, массив или диапазон того же размера, что и для y!новые значения x, для которых РОСТ возвращает соответствующие значения y!логическое значение: константа b вычисляется обычным образом при значении ИСТИНА и равна 1 при значении ЛОЖЬ или отсутствии значения"}, "HARMEAN": {"a": "(число1; [число2]; ...)", "d": "Возвращает среднее гармоническое для множества положительных чисел - величину, обратную среднему арифметическому обратных величин", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых вычисляется среднее гармоническое"}, "HYPGEOM.DIST": {"a": "(усп_выб; размер_выб; усп_сов; размер_сов; интегральная)", "d": "Возвращает гипергеометрическое распределение числа успехов в выборке", "ad": "число успехов в выборке!размер выборки!число успехов в совокупности!размер совокупности!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "HYPGEOMDIST": {"a": "(число_успехов_в_выборке; размер_выборки; число_успехов_в_совокупности; размер_совокупности)", "d": "Возвращает гипергеометрическое распределение", "ad": "число успешных испытаний в выборке!размер выборки!число успешных испытаний в генеральной совокупности!размер генеральной совокупности"}, "INTERCEPT": {"a": "(известные_значения_y; известные_значения_x)", "d": "Возвращает отрезок, отсекаемый на оси линией линейной регрессии", "ad": "зависимое множество наблюдений или данных — числа, массивы или ссылки на ячейки, содержащие числа!независимое множество наблюдений или данных — числа, массивы или ссылки на ячейки, содержащие числа"}, "KURT": {"a": "(число1; [число2]; ...)", "d": "Возвращает эксцесс множества данных", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых вычисляется эксцесс"}, "LARGE": {"a": "(массив; k)", "d": "Возвращает k-ое наибольшее значение в множестве данных (например, пятое наибольшее)", "ad": "массив или диапазон, для которых определяется k-ое наибольшее значение!позиция (начиная с наибольшей) в массиве или диапазоне"}, "LINEST": {"a": "(известные_значения_y; [известные_значения_x]; [конст]; [статистика])", "d": "Возвращает параметры линейного приближения по методу наименьших квадратов", "ad": "множество значений y, для которых уже известно соотношение y = mx + b!необязательное множество значений x, для которых, возможно, уже известно соотношение y = mx + b!логическое значение: константа b вычисляется обычным образом при значении ИСТИНА или отсутствии значения и равна 0 при значении ЛОЖЬ!логическое значение, которое указывает, требуется ли вернуть дополнительную статистику по регрессии (ИСТИНА) или только коэффициенты m и константу b (ЛОЖЬ или отсутствие значения)"}, "LOGEST": {"a": "(известные_значения_y; [известные_значения_x]; [конст]; [статистика])", "d": "Возвращает параметры экспоненциального приближения", "ad": "множество значений y, которые уже известны для соотношения y=b*m^x!необязательное множество значений x, для которых, возможно, уже известно соотношение y = b*m^x!логическое значение: константа b вычисляется обычным образом при значении ИСТИНА или отсутствии значения и равна 1 при значении ЛОЖЬ!логическое значение, которое указывает, требуется ли вернуть дополнительную статистику по регрессии (ИСТИНА) или только коэффициенты m и константу b (ЛОЖЬ или отсутствие значения)"}, "LOGINV": {"a": "(вероятность; среднее; стандартное_отклонение)", "d": "Возвращает обратное логарифмическое нормальное распределение, где ln(x) представляет собой нормальное распределение", "ad": "вероятность, связанная с нормальным логарифмическим распределением, число в диапазоне от 0 до 1 включительно!среднее ln(x)!стандартное отклонение ln(x), положительное число"}, "LOGNORM.DIST": {"a": "(x; среднее; стандартное_откл; интегральная)", "d": "Возвращает логнормальное распределение, где ln(x) представляет собой нормальное распределение", "ad": "значение, для которого вычисляется функция, положительное число!среднее ln(x)!стандартное отклонение ln(x), положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "LOGNORM.INV": {"a": "(вероятность; среднее; стандартное_отклонение)", "d": "Возвращает обратное логарифмическое нормальное распределение, где ln(x) представляет собой нормальное распределение", "ad": "вероятность, связанная с нормальным логарифмическим распределением, число в диапазоне от 0 до 1 включительно!среднее ln(x)!стандартное отклонение ln(x), положительное число"}, "LOGNORMDIST": {"a": "(x; среднее; стандартное_откл)", "d": "Возвращает интегральное логнормальное распределение, где ln(x) представляет собой нормальное распределение", "ad": "значение, для которого вычисляется функция, положительное число!среднее ln(x)!стандартное отклонение ln(x), положительное число"}, "MAX": {"a": "(число1; [число2]; ...)", "d": "Возвращает наибольшее значение из списка аргументов. Логические и текстовые значения игнорируются", "ad": "от 1 до 255 чисел, пустых ячеек, логических или текстовых значений, среди которых ищется наибольшее значение"}, "MAXA": {"a": "(значение1; [значение2]; ...)", "d": "Возвращает наибольшее значение из набора значений. Учитываются логические и текстовые значения", "ad": "от 1 до 255 чисел, пустых ячеек, логических значений или чисел в текстовом виде, среди которых требуется определить наибольшее"}, "MAXIFS": {"a": "(максимальный_диапазон; диапазон_условий; условия; ...)", "d": "Возвращает максимальное значение ячеек, заданных набором условий или критериев", "ad": "Яч<PERSON>йки, для которых нужно определить максимальное значение!Диапазон ячеек, которые нужно проверить на соответствие определенному условию!Условие или критерий, связанные с форматом числа, выражения или текста, или текст, который определяет, какие ячейки будут включены в диапазон при определении максимального значения"}, "MEDIAN": {"a": "(число1; [число2]; ...)", "d": "Возвращает медиану исходных чисел", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых определяется медиана"}, "MIN": {"a": "(число1; [число2]; ...)", "d": "Возвращает наименьшее значение из списка аргументов. Логические и текстовые значения игнорируются", "ad": "от 1 до 255 чисел, пустых ячеек, логических или текстовых значений, среди которых ищется наименьшее значение"}, "MINA": {"a": "(значение1; [значение2]; ...)", "d": "Возвращает наименьшее значение из набора значений. Учитываются логические и текстовые значения", "ad": "от 1 до 255 чисел, пустых ячеек, логических значений или чисел в текстовом виде, среди которых требуется определить наименьшее"}, "MINIFS": {"a": "(минимальный_диапазон; диапазон_условий; условия; ...)", "d": "Возвращает минимальное значение ячеек, заданных набором условий или критериев", "ad": "Яч<PERSON><PERSON>к<PERSON>, для которых нужно определить минимальное значение!Диапазон ячеек, которые нужно проверить на соответствие определенному условию!Условие или критерий, связанные с форматом числа, выражения или текста, или текст, который определяет, какие ячейки будут включены в диапазон при определении минимального значения"}, "MODE": {"a": "(число1; [число2]; ...)", "d": "Возвращает значение моды для массива или диапазона значений", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых вычисляется мода"}, "MODE.MULT": {"a": "(число1; [число2]; ...)", "d": "Возвращает вертикальный массив наиболее часто встречающихся (повторяющихся) значений в массиве или диапазоне значений. Для горизонтального массива используйте выражение =ТРАНСП(МОДА.НСК(число1,число2,...))", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых вычисляется мода"}, "MODE.SNGL": {"a": "(число1; [число2]; ...)", "d": "Возвращает значение моды для массива или диапазона значений", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых вычисляется мода"}, "NEGBINOM.DIST": {"a": "(число_неудач; число_успехов; вероятность_успеха; интегральная)", "d": "Возвращает отрицательное биномиальное распределение - вероятность возникновения определенного числа неудач до указанного количества успехов, с данной вероятностью успеха", "ad": "число неудачных испытаний!пороговое значение числа успешных испытаний!вероятность успеха - число в интервале от 0 до 1!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "NEGBINOMDIST": {"a": "(число_неудач; число_успехов; вероятность_успеха)", "d": "Возвращает отрицательное биномиальное распределение - вероятность возникновения определенного числа неудач до указанного количества успехов, с данной вероятностью успеха", "ad": "число неудачных испытаний!пороговое значение числа успешных испытаний!вероятность успеха - число в интервале от 0 до 1"}, "NORM.DIST": {"a": "(x; среднее; стандартное_откл; интегральная)", "d": "Возвращает нормальную функцию распределения", "ad": "значение, для которого строится распределение!арифметическое среднее распределения!стандартное отклонение распределения, положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "NORMDIST": {"a": "(x; среднее; стандартное_откл; интегральная)", "d": "Возвращает нормальную функцию распределения", "ad": "значение, для которого строится распределение!арифметическое среднее распределения!стандартное отклонение распределения, положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "NORM.INV": {"a": "(вероятность; среднее; стандартное_откл)", "d": "Возвращает обратное нормальное распределение", "ad": "вероятность, соответствующая нормальному распределению, число в диапазоне от 0 до 1 включительно!арифметическое среднее распределения!стандартное отклонение распределения, положительное число"}, "NORMINV": {"a": "(вероятность; среднее; стандартное_откл)", "d": "Возвращает обратное нормальное распределение", "ad": "вероятность, соответствующая нормальному распределению, число в диапазоне от 0 до 1 включительно!арифметическое среднее распределения!стандартное отклонение распределения, положительное число"}, "NORM.S.DIST": {"a": "(z; интегральная)", "d": "Возвращает стандартное нормальное интегральное распределение", "ad": "значение, для которого строится распределение!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "NORMSDIST": {"a": "(z)", "d": "Возвращает стандартное нормальное интегральное распределение", "ad": "значение, для которого строится распределение"}, "NORM.S.INV": {"a": "(вероятность)", "d": "Возвращает обратное значение стандартного нормального распределения", "ad": "вероятность, соответствующая нормальному распределению"}, "NORMSINV": {"a": "(вероятность)", "d": "Возвращает обратное значение стандартного нормального распределения", "ad": "вероятность, соответствующая нормальному распределению"}, "PEARSON": {"a": "(массив1; массив2)", "d": "Возвращает коэффициент корреляции Пирсона, r", "ad": "множество независимых значений!множество зависимых значений"}, "PERCENTILE": {"a": "(массив; k)", "d": "Возвращает k-й процентиль для значений диапазона", "ad": "массив или диапазон с численными значениями, который определяет относительное положение!значение процентиля в интервале от 0 до 1 включительно"}, "PERCENTILE.EXC": {"a": "(массив; k)", "d": "Возвращает k-й процентиль для значений диапазона (k от 0 до 1 не включительно)", "ad": "массив или диапазон с численными значениями, который определяет относительное положение!значение процентиля в интервале от 0 до 1 включительно"}, "PERCENTILE.INC": {"a": "(массив; k)", "d": "Возвращает k-й процентиль для значений диапазона, при k от 0 до 1 включительно", "ad": "массив или диапазон с численными значениями, который определяет относительное положение!значение процентиля в интервале от 0 до 1 включительно"}, "PERCENTRANK": {"a": "(массив; x; [разрядность])", "d": "Возвращает процентную норму значения в множестве данных", "ad": "массив или диапазон с численными значениями, который определяет относительное положение!значение, для которого определяется ранг!необязательное значение, определяющее количество значащих цифр в возвращаемом значении процентного содержания; по умолчанию принимается равным 3 (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(массив; x; [разрядность])", "d": "Возвращает процентную норму значения в множестве данных (от 0 до 1 не включительно)", "ad": "массив или диапазон с числовыми значениями, определяющий относительное положение!значение, для которого определяется ранг!необязательное значение, определяющее количество значимых цифр в возвращаемом процентном значении; если не указано, возвращается значение с тремя цифрами (0,xxx%)"}, "PERCENTRANK.INC": {"a": "(массив; x; [разрядность])", "d": "Возвращает процентную норму значения в множестве данных (от 0 до 1 включительно)", "ad": "массив или диапазон с числовыми значениями, определяющий относительное положение!значение, для которого определяется ранг!необязательное значение, определяющее количество значащих цифр в возвращаемом процентном значении; если не указано, возвращается значение с тремя цифрами (0,xxx%)"}, "PERMUT": {"a": "(число; число_выбранных)", "d": "Возвращает количество перестановок  заданного числа объектов, которые выбираются из общего числа объектов", "ad": "целое число, задающее общее количество объектов!целое число, задающее количество объектов в каждой перестановке"}, "PERMUTATIONA": {"a": "(число; число_выбранных)", "d": "Возвращает количество перестановок для заданного числа объектов (с повторами), которые выбираются из общего числа объектов", "ad": "общее число объектов!число объектов в каждой перестановке"}, "PHI": {"a": "(x)", "d": "Возвращает значение плотности стандартного нормального распределения", "ad": "число, для которого требуется найти плотность стандартного нормального распределения"}, "POISSON": {"a": "(x; среднее; интегральная)", "d": "Возвращает распределение Пуассона", "ad": "количество событий!ожидаемое числовое значение, положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "POISSON.DIST": {"a": "(x; среднее; интегральная)", "d": "Возвращает распределение Пуассона", "ad": "количество событий!ожидаемое числовое значение, положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "PROB": {"a": "(x_интервал; интервал_вероятностей; нижний_предел; [верхний_предел])", "d": "Возвращает вероятность того, что значения диапазона находятся внутри заданных пределов", "ad": "интервал числовых значений x, с которыми связаны вероятности!множество вероятностей, соответствующих значениям в аргументе 'x_интервал'; значения лежат в интервале между 0 и 1, исключая 0!нижняя граница значения, для которого вычисляется вероятность!необязательная верхняя граница значения, для которого вычисляется вероятность. Если опущена, возвращается вероятность того, что значения в аргументе 'x_интервал' равны нижнему пределу"}, "QUARTILE": {"a": "(массив; часть)", "d": "Возвращает квартиль множества данных", "ad": "массив или диапазон ячеек с числовыми значениями, для которых определяется значение квартиля!значение: минимальное = 0; первый квартиль = 1; медиана = 2; третий квартиль = 3; максимальное значение = 4"}, "QUARTILE.INC": {"a": "(массив; часть)", "d": "Возвращает квартиль множества данных по значениям процентиля от 0 до 1 включительно", "ad": "массив или диапазон ячеек с числовыми значениями, для которых определяется значение квартиля!значение: минимальное = 0; первый квартиль = 1; медиана = 2; третий квартиль = 3; максимальное значение = 4"}, "QUARTILE.EXC": {"a": "(массив; часть)", "d": "Возвращает квартиль множества данных по значениям процентиля от 0 до 1 не включительно", "ad": "массив или диапазон ячеек с числовыми значениями, для которых определяется значение квартиля!значение: минимальное = 0; первый квартиль = 1; медиана = 2; третий квартиль = 3; максимальное значение = 4"}, "RANK": {"a": "(число; ссылка; [порядок])", "d": "Возвращает ранг числа в списке чисел: его порядковый номер относительно других чисел в списке", "ad": "число, для которого определяется ранг!массив или ссылка на список чисел. Нечисловые значения игнорируются!число, определяющее способ округления"}, "RANK.AVG": {"a": "(число; ссылка; [порядок])", "d": "Возвращает ранг числа в списке чисел: его порядковый номер относительно других чисел в списке; если несколько значений имеет одинаковый ранг, возвращается средний ранг", "ad": "число, для которого определяется ранг!массив или ссылка на список чисел. Нечисловые значения в ссылке игнорируются!число: опущено или 0 - сортировка рангов в списке по убыванию; любое ненулевое значение - сортировка рангов в списке по возрастанию"}, "RANK.EQ": {"a": "(число; ссылка; [порядок])", "d": "Возвращает ранг числа в списке чисел: его порядковый номер относительно других чисел в списке; если несколько значений имеет одинаковый ранг, возвращается высший ранг из этого набора значений", "ad": "число, для которого определяется ранг!массив или ссылка на список чисел. Нечисловые значения в ссылке игнорируются!число: опущено или 0 - сортировка рангов в списке по убыванию; любое ненулевое значение - сортировка рангов в списке по возрастанию"}, "RSQ": {"a": "(известные_значения_y; известные_значения_x)", "d": "Возвращает квадрат коэффициента корреляции Пирсона по данным точкам", "ad": "массив или диапазон, который может включать числа или имена, массивы или ссылки на ячейки с числами!массив или диапазон, который может включать числа или имена, массивы или ссылки на ячейки с числами"}, "SKEW": {"a": "(число1; [число2]; ...)", "d": "Возвращает асимметрию распределения относительно среднего", "ad": "от 1 до 255 числовых значений, массивов чисел или ссылок на числовые значения, для которых вычисляется асимметричность"}, "SKEW.P": {"a": "(число1; [число2]; ...)", "d": "Возвращает асимметрию распределения по генеральной совокупности относительно среднего", "ad": "от 1 до 254 числовых значений или имен, массивов чисел или ссылок на числовые значения, для которых вычисляется асимметричность"}, "SLOPE": {"a": "(известные_значения_y; известные_значения_x)", "d": "Возвращает наклон линии линейной регрессии", "ad": "массив или диапазон, содержащий числовые зависимые элементы данных!множество независимых элементов данных — имена, массивы или ссылки на ячейки, содержащие числа"}, "SMALL": {"a": "(массив; k)", "d": "Возвращает k-ое наименьшее значение в множестве данных (например, пятое наименьшее)", "ad": "массив или диапазон числовых данных, для которых определяется k-ое наименьшее значение!позиция (начиная с наименьшей) в массиве или диапазоне"}, "STANDARDIZE": {"a": "(x; среднее; стандартное_откл)", "d": "Возвращает нормализованное значение", "ad": "нормализуемое значение!арифметическое среднее распределения!стандартное отклонение распределения, положительное число"}, "STDEV": {"a": "(число1; [число2]; ...)", "d": "Оценивает стандартное отклонение по выборке. Логические и текстовые значения игнорируются", "ad": "от 1 до 255 значений, составляющих выборку из генеральной совокупности; допускаются числовые значения и ссылки на числовые значения"}, "STDEV.P": {"a": "(число1; [число2]; ...)", "d": "Вычисляет стандартное отклонение по генеральной совокупности. Логические и текстовые значения игнорируются", "ad": "от 1 до 255 значений, составляющих генеральную совокупность; допускаются числовые значения и ссылки на числовые значения"}, "STDEV.S": {"a": "(число1; [число2]; ...)", "d": "Оценивает стандартное отклонение по выборке. Логические и текстовые значения игнорируются", "ad": "от 1 до 255 значений, составляющих выборку из генеральной совокупности; допускаются числовые значения и ссылки на числовые значения"}, "STDEVA": {"a": "(значение1; [значение2]; ...)", "d": "Вычисляет стандартное отклонение по выборке с учетом логических и текстовых значений. При этом текстовые и ложные логические значения считаются равными 0, а истинные логические значения считаются равными 1", "ad": "от 1 до 255 значений, составляющих выборку из генеральной совокупности; допускаются числовые значения, имена и ссылки на числовые значения"}, "STDEVP": {"a": "(число1; [число2]; ...)", "d": "Вычисляет стандартное отклонение по генеральной совокупности. Логические и текстовые значения игнорируются", "ad": "от 1 до 255 значений, составляющих генеральную совокупность; допускаются числовые значения и ссылки на числовые значения"}, "STDEVPA": {"a": "(значение1; [значение2]; ...)", "d": "Вычисляет стандартное отклонение по генеральной совокупности с учетом логических и текстовых значений. При этом текстовые и ложные логические значения считаются равными 0, а истинные логические значения считаются равными 1", "ad": "от 1 до 255 значений, составляющих генеральную совокупность; допускаются числовые значения, имена, массивы и ссылки на числовые значения"}, "STEYX": {"a": "(известные_значения_y; известные_значения_x)", "d": "Возвращает стандартную ошибку предсказанных значений y для каждого значения x в регрессии", "ad": "массив или диапазон зависимых точек данных — числа, массивы или ссылки на ячейки, содержащие числа!массив или диапазон независимых точек данных — числа, массивы или ссылки на ячейки, содержащие числа"}, "TDIST": {"a": "(x; степени_свободы; хвосты)", "d": "Возвращает t-распределение Стьюдента", "ad": "числовое значение, для которого требуется вычислить распределение!целое, указывающее количество степеней свободы!число возвращаемых хвостов распределения (1 или 2)"}, "TINV": {"a": "(вероятность; степени_свободы)", "d": "Возвращает двустороннее обратное распределение Стьюдента", "ad": "вероятность, связанная с двусторонним t-распределением Стьюдента!положительное целое число степеней свободы, характеризующее распределение"}, "T.DIST": {"a": "(x; степени_свободы; интегральная)", "d": "Возвращает левостороннее t-распределение Стьюдента", "ad": "числовое значение, для которого требуется вычислить распределение!целое, указывающее количество степеней свободы!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "T.DIST.2T": {"a": "(x; степени_свободы)", "d": "Возвращает двустороннее t-распределение Стьюдента", "ad": "числовое значение, для которого требуется вычислить распределение!целое, указывающее количество степеней свободы"}, "T.DIST.RT": {"a": "(x; степени_свободы)", "d": "Возвращает правостороннее t-распределение Стьюдента", "ad": "числовое значение, для которого требуется вычислить распределение!целое, указывающее количество степеней свободы"}, "T.INV": {"a": "(вероятность; степени_свободы)", "d": "Возвращает левостороннее обратное распределение Стьюдента", "ad": "вероятность, связанная с двусторонним t-распределением Стьюдента, число от 0 до 1 включительно!положительное целое число степеней свободы, характеризующее распределение"}, "T.INV.2T": {"a": "(вероятность; степени_свободы)", "d": "Возвращает двустороннее обратное распределение Стьюдента", "ad": "вероятность, связанная с двусторонним t-распределением Стьюдента, число от 0 до 1 включительно!положительное целое число степеней свободы, характеризующее распределение"}, "T.TEST": {"a": "(массив1; массив2; хвосты; тип)", "d": "Возвращает вероятность, соответствующую t-тесту Стьюдента", "ad": "первый набор данных!второй набор данных!число хвостов распределения (1 или 2)!вид t-test: парный = 1, двухпарный = 2, двухпарный с неравным отклонением = 3"}, "TREND": {"a": "(известные_значения_y; [известные_значения_x]; [новые_значения_x]; [конст])", "d": "Возвращает значения в соответствии с линейной аппроксимацией по методу наименьших квадратов", "ad": "множество значений y, для которых уже известно соотношение y = mx + b!необязательное множество значений x, для которых, возможно, уже известно соотношение y = mx + b или массив имеет тот же размер, что и известные значения y!новые значения x, для которых ТРЕНД возвращает соответствующие значения y!логическое значение: константа b вычисляется обычным образом при значении ИСТИНА или отсутствии значения и равна 0 при значении ЛОЖЬ"}, "TRIMMEAN": {"a": "(массив; доля)", "d": "Возвращает среднее внутренней части множества данных", "ad": "массив или диапазон усредняемых значений!дробное число точек данных, исключаемых из вычислений"}, "TTEST": {"a": "(массив1; массив2; хвосты; тип)", "d": "Возвращает вероятность, соответствующую t-тесту Стьюдента", "ad": "первый набор данных!второй набор данных!число хвостов распределения (1 или 2)!вид t-test: парный = 1, двухпарный = 2, двухпарный с неравным отклонением = 3"}, "VAR": {"a": "(число1; [число2]; ...)", "d": "Оценивает дисперсию по выборке. Логические и текстовые значения игнорируются", "ad": "от 1 до 255 значений, составляющих выборку из генеральной совокупности"}, "VAR.P": {"a": "(число1; [число2]; ...)", "d": "Вычисляет дисперсию для генеральной совокупности. Логические и текстовые значения игнорируются", "ad": "от 1 до 255 числовых аргументов, составляющих генеральную совокупность"}, "VAR.S": {"a": "(число1; [число2]; ...)", "d": "Оценивает дисперсию по выборке. Логические и текстовые значения игнорируются", "ad": "от 1 до 255 значений, составляющих выборку из генеральной совокупности"}, "VARA": {"a": "(значение1; [значение2]; ...)", "d": "Вычисляет дисперсию по выборке с учетом логических и текстовых значений. При этом текстовые и ложные логические значения считаются равными 0, а истинные логические значения считаются равными 1", "ad": "от 1 до 255 числовых аргументов, составляющих выборку из генеральной совокупности"}, "VARP": {"a": "(число1; [число2]; ...)", "d": "Вычисляет дисперсию для генеральной совокупности. Логические и текстовые значения игнорируются", "ad": "от 1 до 255 числовых аргументов, составляющих генеральную совокупность"}, "VARPA": {"a": "(значение1; [значение2]; ...)", "d": "Вычисляет дисперсию по генеральной совокупности с учетом логических и текстовых значений. При этом текстовые и ложные логические значения считаются равными 0, а истинные логические значения считаются равными 1", "ad": "от 1 до 255 числовых аргументов, составляющих генеральную совокупность"}, "WEIBULL": {"a": "(x; альфа; бета; интегральная)", "d": "Возвращает распределение Вейбулла", "ad": "значение, для которого вычисляется функция, неотрицательное число!параметр распределения, положительное число!параметр распределения, положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "WEIBULL.DIST": {"a": "(x; альфа; бета; интегральная)", "d": "Возвращает распределение Вейбулла", "ad": "значение, для которого вычисляется функция, неотрицательное число!параметр распределения, положительное число!параметр распределения, положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "Z.TEST": {"a": "(массив; x; [сигма])", "d": "Возвращает одностороннее P-значение z-теста", "ad": "массив или диапазон, с которыми сравнивается x!проверяемое значение!известное стандартное отклонение генеральной совокупности"}, "ZTEST": {"a": "(массив; x; [сигма])", "d": "Возвращает одностороннее P-значение z-теста", "ad": "массив или диапазон, с которыми сравнивается x!проверяемое значение!известное стандартное отклонение генеральной совокупности"}, "ACCRINT": {"a": "(дата_выпуска; первый_доход; дата_согл; ставка; номинал; частота; [базис]; [способ_расчета])", "d": "Возвращает накопленный процент по ценным бумагам с периодической выплатой процентов.", "ad": "дата выпуска ценных бумаг, заданная порядковым номером!дата первой выплаты процентов по ценным бумагам, заданная порядковым номером!дата расчета за ценные бумаги, заданная порядковым номером!годовая процентная ставка для купонов по ценным бумагам!номинальная стоимость ценных бумаг!количество купонных выплат за год!используемый способ вычисления дня!логическое значение: для расчета с даты выпуска = ИСТИНА или опущено; для расчета с даты последнего купона = ЛОЖЬ"}, "ACCRINTM": {"a": "(дата_выпуска; дата_согл; ставка; номинал; [базис])", "d": "Возвращает накопленный процент по ценным бумагам, процент по которым выплачивается в срок погашения", "ad": "дата выпуска ценных бумаг, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!годовая процентная ставка для купонов по ценным бумагам!номинальная стоимость ценных бумаг!используемый способ вычисления дня"}, "AMORDEGRC": {"a": "(стоимость; дата_приобр; первый_период; остаточная_стоимость; период; ставка; [базис])", "d": "Возвращает величину пропорционально распределенной амортизации актива для каждого учетного периода.", "ad": "затраты на приобретение актива!дата приобретения актива!дата окончания первого периода!остаточная стоимость в конце времени эксплуатации актива.!период!ставка амортизации!годовой базис: 0 — 360 дней, 1 — фактический, 3 — 365 дней."}, "AMORLINC": {"a": "(стоимость; дата_приобр; первый_период; остаточная_стоимость; период; ставка; [базис])", "d": "Возвращает величину пропорционально распределенной амортизации актива для каждого учетного периода.", "ad": "затраты на приобретение актива!дата приобретения актива!дата окончания первого периода!остаточная стоимость в конце времени эксплуатации актива.!период!ставка амортизации!годовой базис: 0 — 360 дней, 1 — фактический, 3 — 365 дней."}, "COUPDAYBS": {"a": "(дата_согл; дата_вступл_в_силу; частота; [базис])", "d": "Возвращает количество дней от начала действия купона до даты расчета", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!количество купонных выплат за год!используемый способ вычисления дня"}, "COUPDAYS": {"a": "(дата_согл; дата_вступл_в_силу; частота; [базис])", "d": "Возвращает количество дней в периоде купона, который содержит дату расчета", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!количество купонных выплат за год!используемый способ вычисления дня"}, "COUPDAYSNC": {"a": "(дата_согл; дата_вступл_в_силу; частота; [базис])", "d": "Возвращает количество дней от даты расчета до срока следующего купона", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!количество купонных выплат за год!используемый способ вычисления дня"}, "COUPNCD": {"a": "(дата_согл; дата_вступл_в_силу; частота; [базис])", "d": "Возвращает дату следующего купона после даты расчета", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!количество купонных выплат за год!используемый способ вычисления дня"}, "COUPNUM": {"a": "(дата_согл; дата_вступл_в_силу; частота; [базис])", "d": "Возвращает количество купонов между датой расчета и сроком погашения ценных бумаг", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!количество купонных выплат за год!используемый способ вычисления дня"}, "COUPPCD": {"a": "(дата_согл; дата_вступл_в_силу; частота; [базис])", "d": "Возвращает дату предыдущего купона до даты расчета", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!количество купонных выплат за год!используемый способ вычисления дня"}, "CUMIPMT": {"a": "(ставка; кол_пер; нз; нач_период; кон_период; тип)", "d": "Возвращает кумулятивную (нарастающим итогом) величину процентов, выплачиваемых по займу в промежутке между двумя периодами выплат", "ad": "процентная ставка!общее число периодов выплат!текущая стоимость инвестиции!номер первого периода, включенного в вычисления!номер последнего периода, включенного в вычисления!выбор времени платежа"}, "CUMPRINC": {"a": "(ставка; кол_пер; нз; нач_период; кон_период; тип)", "d": "Возвращает кумулятивную (нарастающим итогом) сумму, выплачиваемую в погашение основной суммы займа в промежутке между двумя периодами", "ad": "процентная ставка!общее число периодов выплат!текущая стоимость инвестиции!номер первого периода, включенного в вычисления!номер последнего периода, включенного в вычисления!выбор времени платежа"}, "DB": {"a": "(нач_стоимость; ост_стоимость; время_эксплуатации; период; [месяцы])", "d": "Возвращает величину амортизации актива для заданного периода, рассчитанную методом фиксированного уменьшения остатка", "ad": "начальная стоимость актива!остаточная стоимость актива в конце времени эксплуатации!число периодов амортизации актива (иногда называется временем нормальной эксплуатации актива)!период, для которого нужно вычислить амортизацию, в тех же единицах, что и время_эксплуатации!число месяцев в первом году. Если не указано, принимается равным 12"}, "DDB": {"a": "(нач_стоимость; ост_стоимость; время_эксплуатации; период; [коэффициент])", "d": "Возвращает значение амортизации актива за данный период, используя метод двойного уменьшения остатка или иной явно указанный метод", "ad": "начальная стоимость актива!остаточная стоимость в конце времени эксплуатации актива!число периодов амортизации актива (иногда называется временем нормальной эксплуатации актива)!период, для которого нужно вычислить амортизацию; должен указываться в тех же единицах, что и время_эксплуатации!коэффициент уменьшения остатка. Если коэффициент не указан, он принимается равным 2 (метод двукратного уменьшения остатка)"}, "DISC": {"a": "(дата_согл; дата_вступл_в_силу; цена; погашение; [базис])", "d": "Возвращает ставку дисконтирования для ценных бумаг", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!цена за 100 рублей номинальной стоимости ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!используемый способ вычисления дня"}, "DOLLARDE": {"a": "(дроб_руб; дроб)", "d": "Преобразует цену в рублях, выраженную в виде дроби, в цену в рублях, выраженную десятичным числом", "ad": "число, выраженное в виде дроби!целое число для использования в знаменателе дроби"}, "DOLLARFR": {"a": "(дроб_руб; дроб)", "d": "Преобразует цену в рублях, выраженную десятичным числом, в цену в рублях, выраженную в виде дроби", "ad": "десятичное число!целое число для использования в знаменателе дроби"}, "DURATION": {"a": "(дата_согл; дата_вступл_в_силу; купон; доход; частота; [базис])", "d": "Возвращает дюрацию для ценных бумаг, по которым выплачивается периодический процент", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!годовая купонная ставка ценных бумаг!годовая доходность ценных бумаг!количество купонных выплат за год!используемый способ вычисления дня"}, "EFFECT": {"a": "(номинальная_ставка; кол_пер)", "d": "Возвращает фактическую (эффективную) годовую процентную ставку", "ad": "номинальная процентная ставка!количество периодов в году, за которые начисляются сложные проценты"}, "FV": {"a": "(ставка; кпер; плт; [пс]; [тип])", "d": "Возвращает будущую стоимость инвестиции на основе периодических постоянных (равных по величине сумм) платежей и постоянной процентной ставки", "ad": "процентная ставка за период. Например, при годовой процентной ставке в 6% для квартальной ставки используйте значение 6%/4!общее число периодов выплат инвестиции!выплата, производимая в каждый период и не меняющаяся за все время выплаты!приведенная (нынешняя) стоимость, то есть общая сумма, которая на настоящий момент равноценна ряду будущих выплат. Если значение не указано, оно принимается равным 0!значение 0 или 1, обозначающее, должна ли выплата производиться в начале периода (1) или же в его конце (0 или отсутствие значения)"}, "FVSCHEDULE": {"a": "(первичное; план)", "d": "Возвращает будущее значение первоначальной основной суммы после применения ряда (плана) ставок сложных процентов", "ad": "стоимость инвестиции на текущий момент!массив применяемых процентных ставок"}, "INTRATE": {"a": "(дата_согл; дата_вступл_в_силу; инвестиция; погашение; [базис])", "d": "Возвращает процентную ставку для полностью инвестированных ценных бумаг", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!сумма, инвестированная в ценные бумаги!сумма, которая должна быть получена при погашении ценных бумаг!используемый способ вычисления дня"}, "IPMT": {"a": "(ставка; период; кпер; пс; [бс]; [тип])", "d": "Возвращает сумму платежей процентов по инвестиции за данный период на основе постоянства сумм периодических платежей и процентной ставки", "ad": "процентная ставка за период. Например, при годовой процентной ставке в 6% используйте для квартальной процентной ставки значение 6%/4!период, для которого нужно определить сумму выплаты; должен быть в диапазоне от 1 до кпер!общее число периодов выплат инвестиции!приведенная (нынешняя) стоимость, то есть общая сумма, равноценная на данный момент ряду будущих выплат!будущая стоимость, то есть баланс, которого нужно достичь после последней выплаты. Если значение не указано, оно принимается равным 0!логическое значение, указывающее, должен ли платеж выполняться в конце периода (0 или отсутствие значения) или же в его начале (1)"}, "IRR": {"a": "(значения; [предположение])", "d": "Возвращает внутреннюю ставку доходности для ряда потоков денежных средств, представленных численными значениями", "ad": "массив или ссылка на ячейки, содержащие числа, по которым нужно вычислить внутреннюю ставку доходности!предполагаемая величина, близкая к результату ВСД; если не указана, принимается равной 0,1 (10 процентов)"}, "ISPMT": {"a": "(ставка; период; кпер; пс)", "d": "Вычисляет проценты, выплачиваемые за определенный инвестиционный период", "ad": "процентная ставка за период. Например, при годовой процентной ставке в 6% используйте для квартальной процентной ставки значение 6%/4!период, для которого нужно определить процент!общее число периодов выплат инвестиции!приведенная (нынешняя) стоимость, то есть общая сумма, равноценная на данный момент ряду будущих выплат"}, "MDURATION": {"a": "(дата_согл; дата_вступл_в_силу; купон; доход; частота; [базис])", "d": "Возвращает модифицированную дюрацию для ценных бумаг с предполагаемой номинальной стоимостью 100 рублей", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!годовая купонная ставка ценных бумаг!годовая доходность ценных бумаг!количество купонных выплат за год!используемый способ вычисления дня"}, "MIRR": {"a": "(значения; ставка_финанс; ставка_реинвест)", "d": "Возвращает внутреннюю ставку доходности для ряда периодических денежных потоков, учитывая как затраты на привлечение инвестиции, так и процент, получаемый от реинвестирования денежных средств", "ad": "массив или ссылка на ячейки, содержащие числа, которые представляют серии платежей (отрицательные числа) и доходов (положительные числа), приходящихся на одинаковые по продолжительности периоды!процентная ставка, выплачиваемая за средства, которые находятся в обороте!процентная ставка, получаемая за средства, которые находятся в обороте, при реинвестировании"}, "NOMINAL": {"a": "(факт_ставка; кол_пер)", "d": "Возвращает номинальную годовую процентную ставку", "ad": "фактическая процентная ставка!количество периодов в году, за которые начисляются сложные проценты"}, "NPER": {"a": "(ставка; плт; пс; [бс]; [тип])", "d": "Возвращает общее количество периодов выплаты для инвестиции на основе периодических постоянных выплат и постоянной процентной ставки", "ad": "процентная ставка за период. Например, при годовой процентной ставке в 6% для квартальной ставки используйте значение 6%/4!выплата, производимая в каждый период; не может изменяться в течение времени выплаты инвестиции!приведенная (нынешняя) стоимость, то есть общая сумма, равноценная на данный момент ряду будущих выплат!будущая стоимость, то есть баланс, которого нужно достичь после последней выплаты. Если значение не указано, оно принимается равным 0!логическое значение (0 или 1), указывающее, должна ли выплата производиться в конце периода (0 или отсутствие значения) или же в его начале периода (1)"}, "NPV": {"a": "(ставка; значение1; [значение2]; ...)", "d": "Возвращает величину чистой приведенной стоимости инвестиции, используя ставку дисконтирования и стоимости будущих выплат (отрицательные значения) и поступлений (положительные значения)", "ad": "ставка дисконтирования на один период!от 1 до 254 выплат и поступлений, равноотстоящих друг от друга по времени и происходящих в конце каждого периода"}, "ODDFPRICE": {"a": "(дата_согл; дата_вступл_в_силу; дата_выпуска; первый_купон; ставка; доход; погашение; частота; [базис])", "d": "Возвращает цену за 100 рублей номинальной стоимости ценных бумаг с нерегулярным (коротким или длинным) первым периодом купона", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!дата выпуска ценных бумаг, заданная порядковым номером!дата первого купона для ценных бумаг, заданная порядковым номером!процентная ставка для ценных бумаг!годовая доходность ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!количество купонных выплат за год!используемый способ вычисления дня"}, "ODDFYIELD": {"a": "(дата_согл; дата_вступл_в_силу; дата_выпуска; первый_купон; ставка; цена; погашение; частота; [базис])", "d": "Возвращает доход по ценным бумагам с нерегулярным (коротким или длинным) первым периодом купона", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!дата выпуска ценных бумаг, заданная порядковым номером!дата первого купона для ценных бумаг, заданная порядковым номером!процентная ставка для ценных бумаг!стоимость ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!количество купонных выплат за год!используемый способ вычисления дня"}, "ODDLPRICE": {"a": "(дата_согл; дата_вступл_в_силу; посл_купон; ставка; доход; погашение; частота; [базис])", "d": "Возвращает цену за 100 рублей номинальной стоимости ценных бумаг с нерегулярным (коротким или длинным) последним периодом купона", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!дата последнего купона для ценных бумаг, заданная порядковым номером!процентная ставка для ценных бумаг!годовая доходность ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!количество купонных выплат за год!используемый способ вычисления дня"}, "ODDLYIELD": {"a": "(дата_согл; дата_вступл_в_силу; посл_купон; ставка; цена; погашение; частота; [базис])", "d": "Возвращает доход по ценным бумагам с нерегулярным (коротким или длинным) последним периодом купона", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!дата последнего купона для ценных бумаг, заданная порядковым номером!процентная ставка для ценных бумаг!стоимость ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!количество купонных выплат за год!используемый способ вычисления дня"}, "PDURATION": {"a": "(ставка; тс; бс)", "d": "Возвращает число периодов, необходимое для достижения указанной стоимости инвестиции", "ad": "процентная ставка за период!текущая стоимость инвестиции!требуемая будущая стоимость инвестиции"}, "PMT": {"a": "(ставка; кпер; пс; [бс]; [тип])", "d": "Возвращает сумму периодического платежа для займа на основе постоянства сумм платежей и процентной ставки", "ad": "процентная ставка за период займа. Например, при годовой процентной ставке в 6% для квартальной ставки используйте значение 6%/4!общее число периодов выплат по займу!приведенная (нынешняя) стоимость, то есть общая сумма, на настоящий момент равноценная ряду будущих выплат!будущая стоимость, то есть баланс, которого нужно достичь после последней выплаты. Если значение не указано, оно принимается равным 0!логическое значение (0 или 1), обозначающее, должна ли выплата производиться в конце периода (0 или отсутствие значения) или же в его начале (1)"}, "PPMT": {"a": "(ставка; период; кпер; пс; [бс]; [тип])", "d": "Возвращает величину платежа в погашение основной суммы по инвестиции за данный период на основе постоянства периодических платежей и процентной ставки", "ad": "процентная ставка за период. Например, при годовой процентной ставке в 6% используйте для квартальной процентной ставки значение 6%/4!период; должен быть в диапазоне от 1 до кпер!общее число периодов выплат инвестиции!приведенная (нынешняя) стоимость, то есть общая сумма, равноценная на данный момент ряду будущих выплат!будущая стоимость, то есть баланс, которого нужно достичь после последней выплаты!логическое значение, указывающее, должен ли платеж выполняться в конце периода (0 или отсутствие значения) или же в его начале (1)"}, "PRICE": {"a": "(дата_согл; дата_вступл_в_силу; ставка; доход; погашение; частота; [базис])", "d": "Возвращает цену за 100 рублей номинальной стоимости ценных бумаг, по которым выплачивается периодический процент", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!годовая купонная ставка ценных бумаг!годовая доходность ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!количество купонных выплат за год!используемый способ вычисления дня"}, "PRICEDISC": {"a": "(дата_согл; дата_вступл_в_силу; дисконт; погашение; [базис])", "d": "Возвращает цену за 100 рублей номинальной стоимости ценных бумаг с дисконтом", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!ставка дисконтирования ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!используемый способ вычисления дня"}, "PRICEMAT": {"a": "(дата_согл; дата_вступл_в_силу; дата_выпуска; ставка; доходность; [базис])", "d": "Возвращает цену за 100 рублей номинальной стоимости ценных бумаг, по которым процент выплачивается в срок погашения", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!дата выпуска ценных бумаг, заданная порядковым номером!процентная ставка дохода по ценным бумагам на дату выпуска!годовая доходность ценных бумаг!используемый способ вычисления дня"}, "PV": {"a": "(ставка; кпер; плт; [бс]; [тип])", "d": "Возвращает приведенную (к текущему моменту) стоимость инвестиции — общую сумму, которая на настоящий момент равноценна ряду будущих выплат", "ad": "процентная ставка за период. Например, при годовой процентной ставке в 6% для квартальной ставки используйте значение 6%/4!общее число периодов выплат инвестиции!выплата, производимая в каждый период и не меняющаяся за все время выплаты инвестиции!будущая стоимость, то есть баланс, которого нужно достичь после последней выплаты!логическое значение (0 или 1), обозначающее, должна ли выплата производиться в конце периода (0 или отсутствие значения) или же в его начале (1)"}, "RATE": {"a": "(кпер; плт; пс; [бс]; [тип]; [предположение])", "d": "Возвращает процентную ставку по аннуитету за один период. Например, при годовой процентной ставке в 6% для квартальной ставки используется значение 6%/4", "ad": "общее число периодов выплат займа или инвестиции!выплата, производимая в каждый период и не меняющаяся за все время выплаты займа или инвестиции!приведенная (нынешняя) стоимость, то есть общая сумма, равноценная на настоящий момент ряду будущих платежей!будущая стоимость, то есть баланс, которого нужно достичь после последней выплаты (если значение не задано, оно принимается равным 0)!логическое значение (0 или 1), обозначающее, должна ли выплата производиться в конце периода (0 или отсутствие значения) или же в его начале (1)!предполагаемая величина ставки; если значение не указано, то оно принимается равным 0,1 (10%)"}, "RECEIVED": {"a": "(дата_согл; дата_вступл_в_силу; инвестиция; дисконт; [базис])", "d": "Возвращает сумму, полученную к сроку погашения полностью инвестированных ценных бумаг", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!сумма, инвестированная в ценные бумаги!ставка дисконтирования ценных бумаг!используемый способ вычисления дня"}, "RRI": {"a": "(кпер; тс; бс)", "d": "Возвращает эквивалентную процентную ставку для заданного роста инвестиции", "ad": "число периодов для инвестиции!текущая стоимость инвестиции!будущая стоимость инвестиции"}, "SLN": {"a": "(нач_стоимость; ост_стоимость; время_эксплуатации)", "d": "Возвращает величину амортизации актива за один период, рассчитанную линейным методом", "ad": "начальная стоимость актива!остаточная стоимость в конце времени эксплуатации актива!число периодов амортизации актива (иногда называется временем полезной службы актива)"}, "SYD": {"a": "(нач_стоимость; ост_стоимость; время_эксплуатации; период)", "d": "Возвращает величину амортизации актива за данный период, рассчитанную методом суммы годовых чисел", "ad": "начальная стоимость актива!остаточная стоимость в конце времени эксплуатации актива!число периодов амортизации актива (иногда называется временем полезной службы актива)!период; должен указываться в тех же единицах, что и время_эксплуатации"}, "TBILLEQ": {"a": "(дата_согл; дата_вступл_в_силу; скидка)", "d": "Возвращает эквивалентный облигации доход по казначейскому векселю", "ad": "дата расчета за казначейский вексель, заданная порядковым номером!дата погашения казначейского векселя, заданная порядковым номером!ставка дисконтирования казначейского векселя"}, "TBILLPRICE": {"a": "(дата_согл; дата_вступл_в_силу; скидка)", "d": "Возвращает цену за 100 рублей номинальной стоимости для казначейского векселя", "ad": "дата расчета за казначейский вексель, заданная порядковым номером!дата погашения казначейского векселя, заданная порядковым номером!ставка дисконтирования казначейского векселя"}, "TBILLYIELD": {"a": "(дата_согл; дата_вступл_в_силу; цена)", "d": "Возвращает доходность по казначейскому векселю", "ad": "дата расчета за казначейский вексель, заданная порядковым номером!дата погашения казначейского векселя, заданная порядковым номером!цена за 100 рублей номинальной стоимости казначейского векселя"}, "VDB": {"a": "(нач_стоимость; ост_стоимость; время_эксплуатации; нач_период; кон_период; [коэффициент]; [без_переключения])", "d": "Возвращает величину амортизации актива для любого выбранного периода, в том числе для частичных периодов, с использованием метода двойного уменьшения остатка или иного указанного метода", "ad": "начальная стоимость актива!остаточная стоимость актива в конце времени эксплуатации!число периодов амортизации актива (иногда называется временем нормальной эксплуатации актива)!начальный период, для которого нужно вычислить амортизацию, в тех же единицах, что и время_эксплуатации!конечный период, для которого нужно вычислить амортизацию, в тех же единицах, что и время_эксплуатации!коэффициент уменьшения остатка; если не указан, принимается равным 2 (двукратное уменьшение остатка)!значение, определяющее, следует ли переключаться на использование прямолинейной амортизации, когда амортизация превышает уменьшающийся остаток: переключаться, если ЛОЖЬ или значение не указано; не переключаться, если ИСТИНА"}, "XIRR": {"a": "(значения; даты; [предп])", "d": "Возвращает внутреннюю ставку доходности для графика денежных потоков", "ad": "ряд денежных потоков, которой соответствует графику платежей, приведенному в аргументе \"даты\"!расписание дат платежей, соответствующее ряду денежных потоков!предполагаемое значение результата функции ЧИСТВНДОХ"}, "XNPV": {"a": "(ставка; значения; даты)", "d": "Возвращает чистую приведенную стоимость для графика денежных потоков", "ad": "ставка дисконтирования, применяемая к денежным потокам!ряд денежных потоков, который соответствует графику платежей, приведенному в аргументе \"даты\"!расписание дат платежей, соответствующее ряду денежных потоков"}, "YIELD": {"a": "(дата_согл; дата_вступл_в_силу; ставка; цена; погашение; частота; [базис])", "d": "Возвращает доходность ценных бумаг, по которым выплачивается периодический процент", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!годовая купонная ставка ценных бумаг!цена ценных бумаг за 100 рублей номинальной стоимости!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!количество купонных выплат за год!используемый способ вычисления дня"}, "YIELDDISC": {"a": "(дата_согл; дата_вступл_в_силу; цена; погашение; [базис])", "d": "Возвращает годовую доходность по ценным бумагам с дисконтом, например по казначейским векселям", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!цена за 100 рублей номинальной стоимости ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!используемый способ вычисления дня"}, "YIELDMAT": {"a": "(дата_согл; дата_вступл_в_силу; дата_выпуска; ставка; цена; [базис])", "d": "Возвращает годовую доходность ценных бумаг, по которым процент выплачивается в срок погашения", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!дата выпуска ценных бумаг, заданная порядковым номером!процентная ставка дохода по ценным бумагам на дату выпуска!цена за 100 рублей номинальной стоимости ценных бумаг!используемый способ вычисления дня"}, "ABS": {"a": "(число)", "d": "Возвращает модуль (абсолютную величину) числа", "ad": "действительное число, абсолютную величину которого требуется найти"}, "ACOS": {"a": "(число)", "d": "Возвращает арккосинус числа в радианах, в диапазоне от 0 до Пи. Арккосинус числа есть угол, косинус которого равен числу.", "ad": "косинус искомого угла (значение в интервале от  -1 до 1)"}, "ACOSH": {"a": "(число)", "d": "Возвращает гиперболический арккосинус числа", "ad": "Любое действительное число, большее или равное 1"}, "ACOT": {"a": "(число)", "d": "Возвращает арккотангенс числа в радианах от 0 до пи", "ad": "котангенс искомого угла"}, "ACOTH": {"a": "(число)", "d": "Возвращает обратный гиперболический котангенс числа", "ad": "гиперболический котангенс искомого угла"}, "AGGREGATE": {"a": "(номер_функции; параметры; ссылка1; ...)", "d": "Возвращает сводное значение в списке или базе данных", "ad": "число от 1 до 19, которое указывает используемую функцию сведения.!число от 0 до 7, которое указывает значения, пропускаемые при сведении!массив или диапазон числовых данных для вычисления сводного значения!указывает позицию в массиве; k-е наибольшее, k-е наименьшее, k-й процентиль или k-й квартиль.!число от 1 до 19, которое указывает используемую функцию сведения.!число от 0 до 7, которое указывает значения, пропускаемые при сведении!от 1 до 253 диапазонов или ссылок, для которых требуется вычислить сводное значение"}, "ARABIC": {"a": "(текст)", "d": "Преобразует римское число в арабское", "ad": "римское число, которое требуется преобразовать"}, "ASC": {"a": "(текст)", "d": "Для языков с двухбайтовой кодировкой (DBCS) преобразует полноширинные (двухбайтовые) знаки в полуширинные (однобайтовые)", "ad": "текст, который необходимо изменить"}, "ASIN": {"a": "(число)", "d": "Возвращает арксинус числа в радианах, в диапазоне от -Пи/2 до Пи/2", "ad": "синус искомого угла (значение в диапазоне от -1 до 1)"}, "ASINH": {"a": "(число)", "d": "Возвращает гиперболический арксинус числа", "ad": "любое действительное число, большее или равное 1"}, "ATAN": {"a": "(число)", "d": "Возвращает арктангенс числа в радианах, в диапазоне от -Пи/2 до Пи/2", "ad": "Тангенс искомого угла"}, "ATAN2": {"a": "(x; y)", "d": "Возвращает арктангенс для заданных координат x и y, в радианах между -Пи и Пи, исключая -Пи", "ad": "координата X точки!координата Y точки"}, "ATANH": {"a": "(число)", "d": "Возвращает гиперболический арктангенс числа", "ad": "любое действительное число в диапазоне от -1 до 1, исключая -1 и 1"}, "BASE": {"a": "(число; основание; [мин_длина])", "d": "Преобразует число в текстовое представление в системе счисления с заданным основанием", "ad": "число, которое требуется преобразовать!основание системы счисления, в которую требуется преобразовать число!минимальная длина возвращаемой строки (если этот параметр опущен, нули в начале не добавляются)"}, "CEILING": {"a": "(число; точность)", "d": "Округляет число до ближайшего большего по модулю целого, кратного указанному значению", "ad": "округляемое значение!кратное, до которого требуется округлить"}, "CEILING.MATH": {"a": "(число; [точность]; [режим])", "d": "Округляет число вверх до ближайшего целого или ближайшего кратного указанной точности", "ad": "округляемое значение!кратное, до которого требуется округлить!если этот параметр задан и отличен от нуля, функция округляет число в направлении от нуля"}, "CEILING.PRECISE": {"a": "(x;[точность])", "d": "Округляет число вверх до ближайшего целого или до ближайшего кратного указанному значению", "ad": "округляемое значение!кратное, до которого требуется округлить"}, "COMBIN": {"a": "(число; число_выбранных)", "d": "Возвращает количество комбинаций для заданного числа элементов", "ad": "число элементов!число элементов в каждой комбинации"}, "COMBINA": {"a": "(число; число_выбранных)", "d": "Возвращает количество комбинаций с повторами для заданного числа элементов", "ad": "общее число элементов!число элементов в каждой комбинации"}, "COS": {"a": "(число)", "d": "Возвращает косинус угла", "ad": "угол в радианах, косинус которого требуется определить"}, "COSH": {"a": "(число)", "d": "Возвращает гиперболический косинус числа", "ad": "любое действительное число"}, "COT": {"a": "(число)", "d": "Возвращает котангенс угла", "ad": "угол в радианах, для которого требуется найти котангенс"}, "COTH": {"a": "(число)", "d": "Возвращает гиперболический котангенс угла", "ad": "угол в радианах, для которого требуется найти гиперболический котангенс"}, "CSC": {"a": "(число)", "d": "Возвращает косеканс угла", "ad": "угол в радианах, для которого требуется найти косеканс"}, "CSCH": {"a": "(число)", "d": "Возвращает гиперболический косеканс угла", "ad": "угол в радианах, для которого требуется найти гиперболический косеканс"}, "DECIMAL": {"a": "(число; основание)", "d": "Преобразует текстовое представление числа в системе счисления с заданным основанием в десятичное значение", "ad": "число, которое требуется преобразовать!основание системы счисления преобразуемого числа"}, "DEGREES": {"a": "(угол)", "d": "Преобразует радианы в градусы", "ad": "угол в радианах, преобразуемый в градусы"}, "ECMA.CEILING": {"a": "(x;точность)", "d": "Округляет число в большую сторону до ближайшего числа, кратного заданной значимости", "ad": "округляемое значение!кратное, до которого требуется округлить"}, "EVEN": {"a": "(число)", "d": "Округляет число до ближайшего четного целого. Положительные числа округляются в сторону увеличения, отрицательные - в сторону уменьшения", "ad": "округляемое значение"}, "EXP": {"a": "(число)", "d": "Возвращает экспоненту заданного числа", "ad": "степень, в которую возводится основание e. Величина e, основание натурального логарифма, приблизительно равна 2.71828182845904"}, "FACT": {"a": "(число)", "d": "Возвращает факториал числа, равный 1*2*3*..*число", "ad": "неотрицательное число, факториал которого вычисляется"}, "FACTDOUBLE": {"a": "(число)", "d": "Возвращает двойной факториал числа", "ad": "число, для которого требуется вычислить двойной факториал"}, "FLOOR": {"a": "(число; точность)", "d": "Округляет число до ближайшего меньшего по модулю целого, кратного указанному значению", "ad": "округляемое числовое значение!кратное, до которого требуется округлить. Оба значения должны иметь одинаковый знак"}, "FLOOR.PRECISE": {"a": "(x;[точность])", "d": "Возвращает число, округленное с недостатком до ближайшего целого или до ближайшего кратного разрядности", "ad": "округляемое значение!кратное, до которого требуется округлить"}, "FLOOR.MATH": {"a": "(число; [точность]; [режим])", "d": "Округляет число вниз до ближайшего целого или ближайшего кратного указанной точности", "ad": "округляемое значение!кратное, до которого требуется округлить!если этот параметр задан и отличен от нуля, функция округляет в направлении к нулю"}, "GCD": {"a": "(число1; [число2]; ...)", "d": "Возвращает наибольший общий делитель", "ad": "от 1 до 255 значений"}, "INT": {"a": "(число)", "d": "Округляет число до ближайшего меньшего целого", "ad": "действительное число, округляемое до ближайшего меньшего целого"}, "ISO.CEILING": {"a": "(число;[точность])", "d": "Округляет число вверх до ближайшего целого или до ближайшего кратного указанному значению вне зависимости от его знака; если в качестве точности указан нуль, возвращается нуль", "ad": "округляемое значение!кратное, до которого требуется округлить"}, "LCM": {"a": "(число1; [число2]; ...)", "d": "Возвращает наименьшее общее кратное", "ad": "от 1 до 255 значений, для которых определяется наименьшее общее кратное"}, "LN": {"a": "(число)", "d": "Возвращает натуральный логарифм числа", "ad": "положительное действительное число, для которого вычисляется натуральный логарифм"}, "LOG": {"a": "(число; [основание])", "d": "Возвращает логарифм числа по заданному основанию", "ad": "положительное действительное число, для которого вычисляется логарифм!основание логарифма; 10 если опущено"}, "LOG10": {"a": "(число)", "d": "Возвращает десятичный логарифм числа", "ad": "положительное действительное число, для которого вычисляется десятичный логарифм"}, "MDETERM": {"a": "(массив)", "d": "Возвращает определитель матрицы (матрица хранится в массиве)", "ad": "числовой массив с равным количеством строк и столбцов, диапазон ячеек или массив"}, "MINVERSE": {"a": "(массив)", "d": "Возвращает обратную матрицу (матрица хранится в массиве)", "ad": "числовой массив с равным количеством строк и столбцов, либо диапазон или массив"}, "MMULT": {"a": "(массив1; массив2)", "d": "Возвращает матричное произведение двух массивов; результат имеет то же число строк, что и первый массив, и то же число столбцов, что и второй массив", "ad": "первый из перемножаемых массивов, число столбцов в нем должно равняться числу строк во втором массиве"}, "MOD": {"a": "(число; делитель)", "d": "Возвращает остаток от деления", "ad": "число, остаток от деления которого определяется!число, на которое нужно разделить (делитель)"}, "MROUND": {"a": "(число; точность)", "d": "Возвращает число, округленное с желаемой точностью", "ad": "округляемое значение!точность, с которой требуется округлить число"}, "MULTINOMIAL": {"a": "(число1; [число2]; ...)", "d": "Возвращает отношение факториала суммы значений к произведению факториалов значений", "ad": "от 1 до 255 значений"}, "MUNIT": {"a": "(размер)", "d": "Возвращает единичную матрицу указанного размера", "ad": "целое число, задающее размер единичной матрицы, которую требуется получить"}, "ODD": {"a": "(число)", "d": "Округляет число до ближайшего нечетного целого: положительное - в сторону увеличения, отрицательное - в сторону уменьшения", "ad": "округляемое значение"}, "PI": {"a": "()", "d": "Возвращает округленное до 15 знаков после запятой число Пи (значение 3,14159265358979)", "ad": ""}, "POWER": {"a": "(число; степень)", "d": "Возвращает результат возведения в степень", "ad": "номер основания - любое действительное число!показатель степени, в которую возводится основание"}, "PRODUCT": {"a": "(число1; [число2]; ...)", "d": "Возвращает произведение аргументов", "ad": "от 1 до 255 перемножаемых чисел, логических значений или чисел, представленных в текстовом виде"}, "QUOTIENT": {"a": "(числитель; знаменатель)", "d": "Возвращает целую часть результата деления с остатком", "ad": "делимое!делитель"}, "RADIANS": {"a": "(угол)", "d": "Преобразует градусы в радианы", "ad": "угол в градусах, который нужно преобразовать"}, "RAND": {"a": "()", "d": "Возвращает равномерно распределенное случайное число большее или равное 0 и меньшее 1 (изменяется при пересчете)", "ad": ""}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "Возвращает массив случайных чисел", "ad": "количество строк в возвращаемом массиве!количество столбцов в возвращенном массиве!минимальное число_ которое должно возвратиться!максимальное число, которое должно возвратиться!возвращают целое или десятичное число. TRUE — для целого, FALSE — для десятичного числа"}, "RANDBETWEEN": {"a": "(нижн_граница; верхн_граница)", "d": "Возвращает случайное число между двумя заданными числами", "ad": "наименьшее целое число, которое возвращает функция СЛУЧМЕЖДУ!наибольшее целое число, которое возвращает функция СЛУЧМЕЖДУ"}, "ROMAN": {"a": "(число; [форма])", "d": "Преобразует арабские числа в римские, в текстовом формате", "ad": "число в арабской записи, которое требуется преобразовать!число, указывающее желаемый тип числа в римской записи."}, "ROUND": {"a": "(число; число_разрядов)", "d": "Округляет число до указанного количества десятичных разрядов", "ad": "округляемое число!количество десятичных разрядов, до которого нужно округлить число. Отрицательные значения вызывают округление целой части, ноль - округление до ближайшего целого числа"}, "ROUNDDOWN": {"a": "(число; число_разрядов)", "d": "Округляет число до ближайшего меньшего по модулю", "ad": "любое действительное число, которое нужно округлить!количество разрядов, до которого округляется число. Отрицательные значения вызывают округление целой части, ноль или отсутствие значения - до ближайшего целого числа"}, "ROUNDUP": {"a": "(число; число_разрядов)", "d": "Округляет число до ближайшего большего по модулю", "ad": "любое действительное число, которое нужно округлить!количество разрядов, до которого округляется число. Отрицательные значения вызывают округление целой части, ноль или отсутствие значения - до ближайшего целого числа"}, "SEC": {"a": "(число)", "d": "Возвращает секанс угла", "ad": "угол в радианах, для которого требуется найти секанс"}, "SECH": {"a": "(число)", "d": "Возвращает гиперболический секанс угла", "ad": "угол в радианах, для которого требуется найти гиперболический секанс"}, "SERIESSUM": {"a": "(x; n; m; коэффициенты)", "d": "Возвращает сумму степенного ряда, вычисленную по формуле", "ad": "переменная степенного ряда!показатель степени x для первого элемента степенного ряда!шаг увеличения n для каждого следующего элемента степенного ряда!набор коэффициентов при соответствующих степенях x"}, "SIGN": {"a": "(число)", "d": "Возвращает знак числа: 1 - если число положительное, 0 - если оно равно нулю и -1 - если число отрицательное", "ad": "любое действительное число"}, "SIN": {"a": "(число)", "d": "Возвращает синус угла", "ad": "угол в радианах, синус которого требуется определить. Градусы*ПИ()/180=радианы"}, "SINH": {"a": "(число)", "d": "Возвращает гиперболический синус числа", "ad": "любое действительное число"}, "SQRT": {"a": "(число)", "d": "Возвращает значение квадратного корня", "ad": "число, для которого вычисляется квадратный корень"}, "SQRTPI": {"a": "(число)", "d": "Возвращает квадратный корень из значения выражения (число * ПИ)", "ad": "число, которое умножается на число пи"}, "SUBTOTAL": {"a": "(номер_функции; ссылка1; ...)", "d": "Возвращает промежуточные итоги в список или базу данных", "ad": "число от 1 до 11, которое указывает, какую функцию следует использовать при вычислении промежуточных итогов!от 1 до 254 диапазонов или ссылок, для которых требуется вычислить промежуточные итоги"}, "SUM": {"a": "(число1; [число2]; ...)", "d": "Суммирует аргументы", "ad": "от 1 до 255 аргументов, которые суммируются. Логические и текстовые значения игнорируются"}, "SUMIF": {"a": "(диа<PERSON>аз<PERSON><PERSON>; критер<PERSON>; [диапазон_суммирования])", "d": "Суммирует ячейки, заданные указанным условием", "ad": "диапазон проверяемых ячеек!условие в форме числа, выражения или текста, определяющее суммируемые ячейки!фактические ячейки для суммирования. Если диапазон суммирования не указан, будут использоваться ячейки, задаваемые параметром 'диапазон'"}, "SUMIFS": {"a": "(диапазон_суммирования; диапазон_условия; условие; ...)", "d": "Суммир<PERSON><PERSON>т ячейки, удовлетворяющие заданному набору условий", "ad": "фактически суммируемые ячейки!диапазон ячеек, проверяемых на соответствие определенному условию!условие в форме числа, выражения или текста, определяющее суммируемые ячейки"}, "SUMPRODUCT": {"a": "(массив1; [массив2]; [массив3]; ...)", "d": "Возвращает сумму произведений диапазонов или массивов", "ad": "от 2 до 255 массивов, соответствующие компоненты которых нужно сначала перемножить, а затем сложить полученные произведения. Все массивы должны иметь одинаковую размерность"}, "SUMSQ": {"a": "(число1; [число2]; ...)", "d": "Возвращает сумму квадратов аргументов. Аргументами могут являться числа, массивы, имена или ссылки на числовые значения", "ad": "от 1 до 255 чисел, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, имен или ссылок на массивы, для которых вычисляется сумма квадратов"}, "SUMX2MY2": {"a": "(массив_x; массив_y)", "d": "Возвращает сумму разностей квадратов соответствующих значений в двух массивах", "ad": "первый диапазон или массив - число, имя, массив или ссылка на диапазон с числами!второй диапазон или массив - число, имя, массив или ссылка на диапазон с числами"}, "SUMX2PY2": {"a": "(массив_x; массив_y)", "d": "Возвращает сумму сумм квадратов соответствующих элементов двух массивов", "ad": "первый массив или диапазон - число, имя, массив или ссылка на диапазон с числами!второй массив или диапазон - число, имя, массив или ссылка на диапазон с числами"}, "SUMXMY2": {"a": "(массив_x; массив_y)", "d": "Возвращает сумму квадратов разностей соответствующих значений в двух массивах", "ad": "первый диапазон или массив - число, имя, массив или ссылка на диапазон с числами!второй диапазон или массив - число, имя, массив или ссылка на диапазон с числами"}, "TAN": {"a": "(число)", "d": "Возвращает тангенс угла", "ad": "угол в радианах, тангенс которого требуется определить. Градусы * ПИ()/180 = радианы"}, "TANH": {"a": "(число)", "d": "Возвращает гиперболический тангенс числа", "ad": "любое действительное число"}, "TRUNC": {"a": "(число; [число_разрядов])", "d": "Отбрасывает дробную часть числа, так что остается целое число", "ad": "усекаемое число!число, определяющее точность усечения. По умолчанию используется значение 0"}, "ADDRESS": {"a": "(номер_строки; номер_столбца; [тип_ссылки]; [а1]; [имя_листа])", "d": "Возвращает ссылку на одну ячейку в рабочем листе в виде текста", "ad": "Номер строки, используемый в ссылке ячейки; для строки 1 номер строки равен 1!Номер столбца, используемый в ссылке ячейки; для столбца D номер столбца равен 4!Задание типа возвращаемой ссылки (типы ссылки пронумерованы от 1 до 4)!логическое значение, определяющее стиль ссылок: A1 (1 или ИСТИНА) или R1C1 (0 или ЛОЖЬ)!строка, задающая имя листа, который используется как внешняя ссылка"}, "CHOOSE": {"a": "(номер_индекса; значение1; [значение2]; ...)", "d": "Выбирает значение или действие из списка значений по номеру индекса", "ad": "указывает, какой аргумент должен быть выбран. Допустимые значения: число от 1 до 254, ссылка на число от 1 до 254 или формула, результатом которой является число от 1 до 254!от 1 до 254 чисел, ссылок на ячейки, определенных имен, формул, функций или текстовых аргументов, из которых производится выбор"}, "COLUMN": {"a": "([ссылка])", "d": "Возвращает номер столбца, на который указывает ссылка", "ad": "ячейка или диапазон, для которых определяется номер столбца. Если опущено, ячейка содержит функцию столбца"}, "COLUMNS": {"a": "(массив)", "d": "Возвращает количество столбцов в массиве или ссылке", "ad": "массив либо формула, возвращающая массив, либо ссылка на диапазон, в котором определяется количество столбцов"}, "FORMULATEXT": {"a": "(ссылка)", "d": "Возвращает формулу в виде строки", "ad": "ссылка на формулу"}, "HLOOKUP": {"a": "(искомое_значение; таблица; номер_строки; [интервальный_просмотр])", "d": "Ищет значение в верхней строке таблицы и возвращает значение ячейки, находящейся в указанной строке того же столбца", "ad": "значение, которое требуется найти в первой строке таблицы. Искомое_значение может быть значением, ссылкой или текстовой строкой!таблица с текстом, числами или логическими значениями, в которой производится поиск данных; может быть ссылкой или именем диапазона!номер строки в таблице, из которой должно быть возвращено сопоставляемое значение. Первая строка значений таблицы имеет номер 1!логическое значение, определяющее, точно (ЛОЖЬ) или приближенно (ИСТИНА или отсутствие значения) должен производиться поиск в верхней строке (отсортированной по возрастанию)"}, "HYPERLINK": {"a": "(адрес; [имя])", "d": "Создает ссылку, открывающую документ, находящийся на жестком диске, сервере сети или в Интернете.", "ad": "путь и имя файла документа (полный путь, адрес UNC или URL).!текст или число, отображаемое в ячейке. Если этот параметр не задан, в ячейке отображается значение параметра 'адрес'"}, "INDEX": {"a": "(массив; номер_строки; [номер_столбца]!ссылка; номер_строки; [номер_столбца]; [номер_области])", "d": "Возвращает значение или ссылку на ячейку на пересечении конкретных строки и столбца, в данном диапазоне", "ad": "диапазон ячеек или константа массива.!строка в массиве, из которой нужно возвращать значение; если опущена, требуется указание номера столбца!столбец в массиве, из которого нужно возвращать значение; если опущен - требуется указание номера строки!ссылка на один или несколько диапазонов!строка в массиве, из которой нужно возвращать значение; если опущена, требуется указание номера столбца!столбец в массиве, из которого нужно возвращать значение; если опущен - требуется указание номера строки!диапазон ссылки, из которого надо возвращать значения. Первая выделенная или введенная область - это область 1, вторая - область 2 и т.д."}, "INDIRECT": {"a": "(ссылка_на_ячейку; [а1])", "d": "Возвращает ссылку, заданную текстовой строкой", "ad": "ссылка на ячейку, которая содержит либо ссылку в стиле А1, либо ссылку в стиле R1C1, либо имя, определенное как ссылка, либо ссылку на ячейку в виде текстовой строки!логическое значение, указывающее, какого типа ссылка содержится в ячейке, задаваемой аргументом 'ссылка_на_ячейку': R1C1 = ЛОЖЬ; A1 = ИСТИНА или опущено"}, "LOOKUP": {"a": "(искомое_значение; просматриваемый_вектор; [вектор_результатов]!искомое_значение; массив)", "d": "Ищет значения в одной строке, одном столбце или массиве. Включен для обеспечения обратной совместимости", "ad": "Значение, которое ПРОСМОТР ищет в векторе просмотра; значение может быть числом, текстом, логическим значением, именем или ссылкой на значение!диапазон, содержащий только одну строку или один столбец с текстом, числами или логическими значениями, расположенными в порядке возрастания!диапазон, содержащий только одну строку или один столбец того же размера, что и просматриваемый вектор!значение, которое ПРОСМОТР ищет в массиве; оно может быть числом, строкой, именем или ссылкой на значение!диапазон ячеек, содержащий текст, числа или логические значения, которые нужно сравнивать с искомым значением"}, "MATCH": {"a": "(искомое_значение; просматриваемый_массив; [тип_сопоставления])", "d": "Возвращает относительную позицию в массиве элемента, соответствующего указанному значению с учетом указанного порядка", "ad": "значение, используемое при поиске нужного значения в массиве - может быть числом, текстом или логическим значением, либо ссылкой на один из этих типов!непрерывный диапазон ячеек, просматриваемый в поиске искомого значения - может быть диапазоном значений или ссылкой на диапазон!число (1, 0 или -1), определяющее возвращаемое значение."}, "OFFSET": {"a": "(ссылка; смещ_по_строкам; смещ_по_столбцам; [высота]; [ширина])", "d": "Возвращает ссылку на диапазон, смещенный относительно заданной ссылки на указанное число строк и столбцов", "ad": "ссылка, от которой отсчитывается смещение - ссылка на ячейку или диапазон смежных ячеек!количество строк вниз или вверх, на которое диапазон результирующей ссылки смещен относительно диапазона исходной ссылки!количество столбцов вправо или влево, на которое диапазон результирующей ссылки смещен относительно диапазона исходной ссылки!высота, в строках, диапазона результирующей ссылки; если не указана, то равна высоте диапазона исходной ссылки!ширина, в столбцах, диапазона результирующей ссылки; если не указана, то равна ширине диапазона исходной ссылки"}, "ROW": {"a": "([ссылка])", "d": "Возвращает номер строки, определяемой ссылкой", "ad": "ячейка или диапазон, для которых определяется номер строки; если опущено, возвращает ячейку с функцией СТРОКА"}, "ROWS": {"a": "(массив)", "d": "Возвращает количество строк в ссылке или массиве", "ad": "массив или формула, выдающая массив, либо ссылка на диапазон, для которых определяется количество строк"}, "TRANSPOSE": {"a": "(массив)", "d": "Преобразует вертикальный диапазон ячеек в горизонтальный, или наоборот", "ad": "диа<PERSON>азон ячеек на листе или массив значений, который нужно транспонировать"}, "UNIQUE": {"a": "(массив; [по_столбцам]; [только_один_раз])", "d": "Возвращает уникальные значения из диапазона или массива.", "ad": "диапазон или массив, из которого возвращаются уникальные строки или столбцы!логическое значение: сравнение строк с другими и возврат уникальных строк (ЛОЖЬ или отсутствие значения); сравнение столбцов с другими и возврат уникальных столбцов (ИСТИНА)!логическое значение: возврат строк или столбцов, которые встречаются в массиве только один раз (ИСТИНА); возврат всех отдельных строк или столбцов в массиве (ЛОЖЬ или отсутствие значения)"}, "VLOOKUP": {"a": "(искомое_значение; таблица; номер_столбца; [интервальный_просмотр])", "d": "Ищет значение в крайнем левом столбце таблицы и возвращает значение ячейки, находящейся в указанном столбце той же строки. По умолчанию таблица должна быть отсортирована по возрастанию", "ad": "значение, которое должно быть найдено в первом столбце массива (значение, ссылка или строка текста)!таблица с текстом, числами или логическими значениями, в которой производится поиск данных; может быть ссылкой или именем диапазона!номер столбца в таблице, из которого нужно вернуть значение. Первый столбец значений таблицы имеет номер 1!логическое значение, определяющее, точно (ЛОЖЬ) или приближенно (ИСТИНА или отсутствие значения) должен производиться поиск в первом столбце (отсортированном по возрастанию)"}, "XLOOKUP": {"a": "(искомое_значение; просматриваемый_массив; возращаемый_массив; [если_ничего_не_найдено]; [режим_сопоставления]; [режим_поиска])", "d": "Ищет совпадение в диапазоне или массиве и возвращает соответствующий элемент из второго диапазона или массива. По умолчанию используется точное соответствие", "ad": "значение, которое требуется найти!массив или диапазон, в котором выполняется поиск!массив или диапазон, из которого возвращается значение!значение, которое возвращается, если совпадение не найдено!укажите, каким образом искомое_значение сопоставляется со значениями в массиве просматриваемый_массив!укажите используемый режим поиска. По умолчанию выполняется поиск с первого до последнего элемента"}, "CELL": {"a": "(тип_сведений; [ссылка])", "d": "Возвращает сведения о форматировании, расположении или содержимом ячейки", "ad": "текстовое значение, задающее тип сведений о ячейке при возвращении!ячейка, сведения о которой требуется получить"}, "ERROR.TYPE": {"a": "(значение_ошибки)", "d": "Возвращает код ошибки, соответствующий ее значению.", "ad": "значение ошибки, для которой нужно найти код. Может быть фактическим значением ошибки или ссылкой на ячейку, содержащую ошибку"}, "ISBLANK": {"a": "(значение)", "d": "Проверяет, ссылается ли данная ссылка на пустую ячейку, и возвращает значение ИСТИНА или ЛОЖЬ", "ad": "проверяемая ячейка или ссылающееся на ее имя"}, "ISERR": {"a": "(значение)", "d": "Проверяет, отличается ли значение от #Н/Д, и возвращает значение ИСТИНА или ЛОЖЬ", "ad": "проверяемое значение. Оно может содержать ссылку на ячейку, формулу или имя ячейки, формулы или значения"}, "ISERROR": {"a": "(значение)", "d": "Проверяет, является ли значение ошибкой, и возвращает значение ИСТИНА или ЛОЖЬ", "ad": "проверяемое значение. Оно может содержать ссылку на ячейку, формулу или имя ячейки, формулы или значения"}, "ISEVEN": {"a": "(число)", "d": "Возвращает значение ИСТИНА, если число четное", "ad": "проверяемое значение"}, "ISFORMULA": {"a": "(ссылка)", "d": "Проверяет, содержится ли формула в ячейке, на которую указывает ссылка, и возвращает значение ИСТИНА или ЛОЖЬ", "ad": "ссылка на ячейку, которую требуется проверить (может быть ссылкой на ячейку, формулой или именем, указывающим на ячейку)"}, "ISLOGICAL": {"a": "(значение)", "d": "Проверяет, является ли значение логическим (ИСТИНА или ЛОЖЬ), и возвращает ИСТИНА или ЛОЖЬ", "ad": "проверяемое значение - ячейка, формула или имя ячейки, формулы или значения"}, "ISNA": {"a": "(значение)", "d": "Проверяет, является ли значение недоступным (#Н/Д), и возвращает значение ИСТИНА или ЛОЖЬ", "ad": "проверяемое значение. Значение может содержать ссылку на ячейку, формулу или имя ячейки, формулы или значения"}, "ISNONTEXT": {"a": "(значение)", "d": "Возвращает ИСТИНА, если значение не является текстовым, и ЛОЖЬ в противном случае. Пустые ячейки не являются текстовыми", "ad": "проверяемое значение: ячейка, формула или имя ячейки, формулы или значения"}, "ISNUMBER": {"a": "(значение)", "d": "Проверяет, является ли значение числом, и возвращает значение ИСТИНА или ЛОЖЬ", "ad": "проверяемое значение. Значение может содержать ссылку на ячейку, формулу или имя ячейки, формулы или значения"}, "ISODD": {"a": "(число)", "d": "Возвращает значение ИСТИНА, если число нечетное", "ad": "проверяемое значение"}, "ISREF": {"a": "(значение)", "d": "Проверяет, является ли значение ссылкой, и возвращает значение ИСТИНА или ЛОЖЬ", "ad": "проверяемое значение. Значение может содержать ссылку на ячейку, формулу или имя ячейки, формулы или значения"}, "ISTEXT": {"a": "(значение)", "d": "Проверяет, является ли значение текстом, и возвращает значение ИСТИНА или ЛОЖЬ", "ad": "проверяемое значение. Значение может содержать ссылку на ячейку, формулу или имя ячейки, формулы или значения"}, "N": {"a": "(значение)", "d": "Преобразует нечисловые значения в числа, даты - в даты, представленные числами, значения ИСТИНА в 1, все остальные значения - в 0 (ноль)", "ad": "преобразуемое значение"}, "NA": {"a": "()", "d": "Возвращает неопределенное значение #Н/Д (значение недоступно)", "ad": ""}, "SHEET": {"a": "([значение])", "d": "Возвращает номер указанного листа", "ad": "имя листа или ссылка на лист, номер которого требуется получить (если этот параметр опущен, возвращается номер листа, содержащего функцию)"}, "SHEETS": {"a": "([ссылка])", "d": "Возвращает число листов в ссылке", "ad": "ссылка, для которой требуется определить число содержащихся в ней листов (если этот параметр опущен, возвращается число листов в книге, содержащей функцию)"}, "TYPE": {"a": "(value)", "d": "Возвращает целое число, обозначающее тип данных указанного значения: число = 1; строка = 2; логическое значение = 4; ошибка = 16; массив = 64; составные данные = 128", "ad": "любое допустимое значение"}, "AND": {"a": "(логическое_значение1; [логическое_значение2]; ...)", "d": "Проверяет, все ли аргументы имеют значение ИСТИНА, и возвращает значение ИСТИНА, если истинны все аргументы", "ad": "от 1 до 255 проверяемых условий, которые могут принимать значение ИСТИНА либо ЛОЖЬ; они могут быть логическими значениями, массивами или ссылками"}, "FALSE": {"a": "()", "d": "Возвращает логическое значение ЛОЖЬ", "ad": ""}, "IF": {"a": "(лог_выражение; [значение_если_истина]; [значение_если_ложь])", "d": "Проверяет, выполняется ли условие, и возвращает одно значение, если оно выполняется, и другое значение, если нет", "ad": "любое значение или выражение, которое при вычислении дает значение ИСТИНА или ЛОЖЬ!значение, которое возвращается, если 'лог_выражение' имеет значение ИСТИНА. Если не указано, возвращается значение ИСТИНА. Допустимая глубина вложенности - семь!значение, которое возвращается, если 'лог_выражение' имеет значение ЛОЖЬ. Если не указано, возвращается значение ЛОЖЬ"}, "IFS": {"a": "(логическая_проверка; если_значение_истина; ...)", "d": "Проверяет соответствие одному или нескольким условиям и возвращает значение для первого условия со значением ИСТИНА", "ad": "Должно быть значением или выражением, которое можно оценить как ИСТИНА или ЛОЖЬ!Значение возвращается, если в результате логической проверки будет получено значение ИСТИНА"}, "IFERROR": {"a": "(значение; значение_если_ошибка)", "d": "Возвращает значение_если_ошибка, если выражение ошибочно; в противном случае возвращает само выражение", "ad": "любое значение, выражение или ссылка!любое значение, выражение или ссылка"}, "IFNA": {"a": "(значение; значение_если_нд)", "d": "Возвращает указанное значение, если выражение дает результат #Н/Д; в противном случае возвращает результат выражения", "ad": "любое значение, выражение или ссылка!любое значение, выражение или ссылка"}, "NOT": {"a": "(логическое_значение)", "d": "Изменяет значение ЛОЖЬ на ИСТИНА, а ИСТИНА на ЛОЖЬ", "ad": "значение или выражение, которое может принимать значение либо ИСТИНА, либо ЛОЖЬ"}, "OR": {"a": "(логическое_значение1; [логическое_значение2]; ...)", "d": "Проверяет, имеет ли хотя бы один из аргументов значение ИСТИНА, и возвращает значение ИСТИНА или ЛОЖЬ. Значение ЛОЖЬ возвращается только в том случае, если все аргументы имеют значение ЛОЖЬ", "ad": "от 1 до 255 проверяемых условий, которые могут принимать значение ИСТИНА либо ЛОЖЬ"}, "SWITCH": {"a": "(выражение; значение1; результат1; [по_умолчанию_или_значение2]; [результат2]; ...)", "d": "Вычисляет выражение на основе списка значений и возвращает результат, соответствующий первому совпавшему значению. Если совпадение отсутствует, возвращается необязательное значение по умолчанию", "ad": "выражение для оценки!значение для сравнения с выражением! результат возвращается, если соответствующее значение соответствует выражению"}, "TRUE": {"a": "()", "d": "Возвращает логическое значение ИСТИНА", "ad": ""}, "XOR": {"a": "(логическое_значение1; [логическое_значение2]; ...)", "d": "Возвращает логическое \"исключающее или\" всех аргументов", "ad": "от 1 до 254 проверяемых условий, которые могут принимать значения ИСТИНА или ЛОЖЬ и могут быть логическими значениями, массивами или ссылками"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Возвращает текст перед символами-разделителями.", "ad": "Текст, который вы хотите найти по разделителю.!Символ или строка для использования в качестве разделителя.!Желаемая встречаемость разделителя. По умолчанию 1. Отрицательное число ищет с конца.!Ищет в тексте сопоставление разделителя. По умолчанию выполняется сопоставление с учетом регистра.!Сопоставлять ли разделитель с концом текста. По умолчанию они не сопоставляются.!Возвращается, если сопоставлений не найдено. По умолчанию возвращается #Н/Д."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Возвращает текст после символов-разделителей.", "ad": "Текст, который вы хотите найти по разделителю.!Символ или строка для использования в качестве разделителя.!Желаемая встречаемость разделителя. По умолчанию 1. Отрицательное число ищет с конца!Ищет в тексте сопоставление разделителя. По умолчанию выполняется совпадение с учетом регистра.!Сопоставлять ли разделитель с концом текста. По умолчанию они не сопоставляются.!Возвращается, если совпадений не найдено.По умолчанию возвращается #Н/Д."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Разбивает текст на строки или столбцы с помощью разделителей.", "ad": "Текст для разделения!Символ или строка для разделения столбцов.!Символ или строка для разделения строк.!Игнорировать ли пустые ячейки. По умолчанию FALSE.!Ищет в тексте совпадение разделителя. По умолчанию учитывается совпадение с учетом регистра.!Значение, используемое для заполнения. По умолчанию используется #N/A."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Переносит вектор строки или столбца после указанного числа значений.", "ad": "Вектор или ссылка для переноса.!Максимальное число значений в строке.!Значение для заполнения. По умолчанию — #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Вертикально собирает массивы в один массив.", "ad": "Массив или ссылка для расположения стопкой."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Горизонтально собирает массивы в один массив.", "ad": "Массив или ссылка для расположения стопкой."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Возвращает строки из массива или ссылки.", "ad": "Массив или ссылка, содержащие возвращаемые строки.!Номер возвращаемой строки."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Возвращает столбцы из массива или ссылки.", "ad": "Массив или ссылка, содержащие возвращаемые столбцы.!Номер возвращаемого столбца."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Возвращает массив в виде одного столбца.", "ad": "Массив или ссылка, возвращаемые как столбец.!Следует ли игнорировать определенные типы значений. По умолчанию значения не игнорируются.!Сканирование массива по столбцам. По умолчанию массив сканируется по строкам."}, "TOROW": {"a": "(мас<PERSON>ив, [игнорировать], [сканировать_по_столбцам])", "d": "Возвращает массив в виде одной строки.", "ad": "Массив или ссылка, возвращаемые как строка.!Следует ли игнорировать определенные типы значений. По умолчанию значения не игнорируются.!Сканирование массива по столбцам. По умолчанию массив сканируется по строкам."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Переносит вектор строки или столбца после указанного числа значений.", "ad": "Вектор или ссылка для переноса.!Максимальное число значений в столбце.!Значение для заполнения. По умолчанию — #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Возвращает строки или столбцы из начала или конца массива.", "ad": "Мас<PERSON>и<PERSON>, из которого будут приниматься строки или столбцы.!Количество принимаемых строк. Отрицательное значение принимается с конца массива.!Количество принимаемых столбцов. Отрицательное значение принимается с конца массива."}, "DROP": {"a": "(array, rows, [columns])", "d": "Удаляет строки или столбцы из начала или конца массива.", "ad": "Массив для удаления строк или столбцов.!Количество удаляемых строк. Отрицательное значение удаляется с конца массива.!Количество удаляемых столбцов. Отрицательное значение удаляется с конца массива."}, "SEQUENCE": {"a": "(строки, [столбцы], [начало], [шаг])", "d": "Возвращает последовательность чисел.", "ad": "количество возвращаемых строк!количество возвращаемых столбцов!первое число в последовательности!величина приращения для получения каждого последующего значения в последовательности"}, "EXPAND": {"a": "(мас<PERSON>ив, строки, [столбцы], [заполняющее_значение])", "d": "Развертывает массив до указанных размеров.", "ad": "Массив для развертывания.!Количество строк в развернутом массиве. Если значение отсутствует, строки не будут развернуты.!Количество столбцов в развернутом массиве. Если значение отсутствует, столбцы не будут развернуты.!Значение, используемое для заполнения. Значение по умолчанию: #Н/Д."}, "XMATCH": {"a": "(искомое_значение, просматриваемый_массив, [режим_сопоставления], [режим_поиска])", "d": "Возвращает относительное положение элемента в массиве. По умолчанию требуется точное совпадение", "ad": " — значение, которое требуется найти! — массив или диапазон, в котором выполняется поиск!укажите, каким образом искомое_значение сопоставляется со значениями в массиве просматриваемый_массив!укажите используемый режим поиска. По умолчанию выполняется поиск с первого до последнего элемента"}, "FILTER": {"a": "(массив, включить, [если_пусто])", "d": "Фильтрация диапазона или массива", "ad": "диапазон или массив для фильтрации!массив логических значений, где значение ИСТИНА представляет сохраняемые строку или столбец!возвращается в случае, если ничего не сохранено"}, "ARRAYTOTEXT": {"a": "(массив, [формат])", "d": "Возвращает текстовое представление массива", "ad": "массив для представления в виде текста!формат текста"}, "SORT": {"a": "(массив, [индекс_сортировки], [порядок_сортировки], [по_столбцу])", "d": "Сортирует диапазон или массив", "ad": "диапазон или массив для сортировки!число, указывающее номер строки или столбца, по которым должна быть выполнена сортировка!число, указывающее порядок сортировки: 1 — по возрастанию (по умолчанию), -1 — по убыванию!логическое значение, указывающее направление сортировки: FALSE — по строке (по умолчанию), TRUE — по столбцу"}, "SORTBY": {"a": "(ма<PERSON><PERSON>и<PERSON>, ключевой_массив, [порядок_сортировки], ...)", "d": "Сортирует диапазон или массив на основе значений в сопоставленном с ним диапазоне или массиве", "ad": "сортируемый диапазон или массив!диапазон или массив, по которому выполняется сортировка!число, указывающее требуемый порядок сортировки: 1 — по возрастанию (по умолчанию), -1 — по убыванию"}, "GETPIVOTDATA": {"a": "(поле_данных; сводная_таблица; [поле]; [элемент]; ...)", "d": "Извлекает данные, хранящиеся в сводной таблице", "ad": "имя поля данных, из которого следует извлечь данные!ссылка на ячейку или диапазон ячеек сводной таблицы, где содержатся данные, которые нужно извлечь!поле!элемент поля"}, "IMPORTRANGE": {"a": "(url_таблицы, диапазон)", "d": "Импортирует диапазон ячеек из одной электронной таблицы в другую.", "ad": "URL таблицы, из которой импортируются данные!диапазон, который нужно импортировать"}}