{"DATE": {"a": "(年; 月; 日)", "d": "日付/時刻コードで指定した日付を表す数値を返します。", "ad": "には 1900 または 1904 (ブックの日付システムに応じて異なる) から 9999 までの数値を指定します。!には月を表す数値 (1～12) を指定します。!には日を表す数値 (1 ～ 31) を指定します。"}, "DATEDIF": {"a": "(開始日; 終了日; 単位)", "d": "2 つの日付の間の日数、月数、または年数を計算します。", "ad": "指定した期間の最初の日付または開始日を表す日付!期間の最後の日付または終了日を表す日付!返される情報の種類"}, "DATEVALUE": {"a": "(日付文字列)", "d": "文字列の形式で表された日付を、組み込みの日付表示形式で数値に変換して返します。", "ad": "には日付を表す文字列を、Spreadsheet Editor の組み込みの日付表示形式で指定します。1900 年 1 月 1 日 または 1904 年 1 月 1 日 (ブックの日付システムに応じて異なる) ～ 9999 年 12 月 31 日の範囲にある日付を指定します。"}, "DAY": {"a": "(シリアル値)", "d": "シリアル値に対応する日を 1 から 31 までの整数で返します。", "ad": "には Spreadsheet Editor で日付や時間の計算に使用される日付コードを指定します。"}, "DAYS": {"a": "(終了日; 開始日)", "d": "2 つの日付の間の日数を返します。", "ad": "開始日と終了日は日数を求める基準となる 2 つの日付です。!開始日と終了日は日数を求める基準となる 2 つの日付です。"}, "DAYS360": {"a": "(開始日; 終了日; [方式])", "d": "1 年を 360 日として、指定した 2 つの日付の間の日数を返します。", "ad": "間隔を求めたい 2 つの日付を指定します。!間隔を求めたい 2 つの日付を指定します。!には計算にヨーロッパ方式 (TRUE) と米国 NASD 方式 (FALSE) のどちらを使用するかを、論理値で指定します。"}, "EDATE": {"a": "(開始日; 月)", "d": "開始日から起算して、指定した月だけ前あるいは後の日付に対応するシリアル値を計算します。", "ad": "には、計算の起算日となる日付のシリアル値を指定します。!には、開始日から起算した月数を指定します。"}, "EOMONTH": {"a": "(開始日; 月)", "d": "開始日から起算して、指定した月だけ前あるいは後の月の最終日に対応するシリアル値を計算します。", "ad": "には、計算の起算日となる日付のシリアル値を指定します。!には、開始日から起算した月数を指定します。"}, "HOUR": {"a": "(シリアル値)", "d": "時刻を 0 (午前 0 時) ～ 23 (午後 11 時) の範囲の整数で返します。", "ad": "には、Spreadsheet Editor で使用される日付/時刻コードか、または 16:48:00 や 4:48:00 PM のような時刻形式のテキストを指定します。"}, "ISOWEEKNUM": {"a": "(日付)", "d": "指定された日付のその年における ISO 週番号を返します。", "ad": "には Spreadsheet Editor で日付や時刻の計算に使用される日付/時刻コードを指定します。"}, "MINUTE": {"a": "(シリアル値)", "d": "分を 0 ～ 59 の範囲の整数で返します。", "ad": "には、Spreadsheet Editor で使用される日付/時刻コードか、または 16:48:00 や 4:48:00 PM のような時刻形式のテキストを指定します。"}, "MONTH": {"a": "(シリアル値)", "d": "月を 1 (1 月) ～ 12 (12 月) の範囲の整数で返します。", "ad": "には Spreadsheet Editor で使用される日付/時刻コードを指定します。"}, "NETWORKDAYS": {"a": "(開始日; 終了日; [祭日])", "d": "開始日と終了日の間にある週日の日数を計算します。", "ad": "には、対象となる期間の初日となる日付のシリアル値を指定します。!には、対象となる期間の最終日となる日付のシリアル値を指定します。!は省略可能な引数で、国民の祝日などの日数を計算にいれないため対応する日付のシリアル値を指定します。"}, "NETWORKDAYS.INTL": {"a": "(開始日; 終了日; [週末]; [祭日])", "d": "ユーザー設定の週末パラメーターを使用して、開始日と終了日の間にある週日の日数を計算します。", "ad": "には、期間の開始日となる日付のシリアル値を指定します。!には、期間の終了日となる日付のシリアル値を指定します。!は週末の開始を指定する番号または文字列です。!は省略可能な引数で、国民の祝日などの日数を計算にいれないため対応する日付のシリアル値を指定します。"}, "NOW": {"a": "()", "d": "現在の日付と時刻を表すシリアル値を返します。", "ad": ""}, "SECOND": {"a": "(シリアル値)", "d": "秒を 0 ～ 59 の範囲の整数で返します。", "ad": "には、Spreadsheet Editor で使用される日付/時刻コードか、または 16:48:23 や 4:48:47 PM のような時刻形式のテキストを指定します。"}, "TIME": {"a": "(時; 分; 秒)", "d": "指定した時刻を表すシリアル値 (0:00:00 (午前 12:00:00) から 23:59:59 (午後 11:59:59) までを表す 0 から 0.9999999 の範囲の小数値) を返します。", "ad": "は、時間を表す 0 から 23 までの数値です!分を表す 0 ~ 59 の数値です!秒を表す 0 ~ 59 の数値です"}, "TIMEVALUE": {"a": "(時刻文字列)", "d": "文字列で表された時刻を、シリアル値 (0 (午前 0 時) から 0.999988426 (午後 11 時 59 分 59 秒) までの数値) に変換します。数式の入力後に、数値を時刻表示形式に設定します。", "ad": "には時刻を表す文字列を、Spreadsheet Editor の組み込みの時刻表示形式で指定します。日付の情報は無視されます。"}, "TODAY": {"a": "()", "d": "現在の日付を表すシリアル値 (日付や時刻の計算で使用されるコード) を返します。", "ad": ""}, "WEEKDAY": {"a": "(シリアル値; [種類])", "d": "日付に対応する曜日を 1 から 7 までの整数で返します。", "ad": "は日付を表す数値です。!には戻り値の種類を表す 1 (日曜 = 1 ～ 土曜 = 7)、2 (月曜 = 1 ～ 日曜 = 7)、3 (月曜 = 0 ～ 日曜 = 6) のいずれかの数字を指定します。"}, "WEEKNUM": {"a": "(シリアル値; [週の基準])", "d": "日付がその年の第何週目に当たるかを返します。", "ad": "Spreadsheet Editor で日付や時刻の計算に使用される日付/時刻コードを指定します。!週の始まりを何曜日とするかを 1 か 2 の数値で指定します。"}, "WORKDAY": {"a": "(開始日; 日数; [祭日])", "d": "開始日から起算して日数で指定した日数だけ前あるいは後の日付に対応するシリアル値を計算します。", "ad": "対象となる期間の初日となる日付のシリアル値を指定します。!開始日から起算して、週末あるいは祭日を除く週日の日数を指定します。!省略可能な引数で、国民の祝日などの日数を計算にいれないため対応する日付のシリアル値を指定します。"}, "WORKDAY.INTL": {"a": "(開始日; 日数; [週末]; [祭日])", "d": "ユーザー定義の週末パラメーターを使用して、指定した日数だけ前あるいは後の日付に対応するシリアル値を計算します。", "ad": "は期間の開始日となる日付のシリアル値を指定します。!は開始日から起算して、週末あるいは祭日を除く週日の日数を指定します。!は週末の開始を指定する週末の番号または文字列です。!は省略可能な引数で、国民の祝日などの日数を計算にいれないため対応する日付のシリアル値を指定します。"}, "YEAR": {"a": "(シリアル値)", "d": "年を 1900 ～ 9999 の範囲の整数で返します。", "ad": "には Spreadsheet Editor で使用される日付/時刻コードを指定します。"}, "YEARFRAC": {"a": "(開始日; 終了日; [基準])", "d": "開始日から終了日までの間の日数を、年を単位とする数値で表します。", "ad": "には、対象となる期間の初日となる日付のシリアル値を指定します。!には、対象となる期間の最終日となる日付のシリアル値を指定します。!には、日数の計算方法を数値で指定します。"}, "BESSELI": {"a": "(x; n)", "d": "修正ベッセル関数 In(x) を返します", "ad": "には、関数に代入する値を指定します!には、ベッセル関数の次数を指定します"}, "BESSELJ": {"a": "(x; n)", "d": "ベッセル関数 Jn(x) を返します", "ad": "には、関数に代入する値を指定します!には、ベッセル関数の次数を指定します"}, "BESSELK": {"a": "(x; n)", "d": "修正ベッセル関数 Kn(x) を返します", "ad": "には、関数に代入する値を指定します!には、ベッセル関数の次数を指定します"}, "BESSELY": {"a": "(x; n)", "d": "ベッセル関数 Yn(x) を返します", "ad": "には、関数に代入する値を指定します!には、ベッセル関数の次数を指定します"}, "BIN2DEC": {"a": "(数値)", "d": "2 進数を 10 進数に変換します。", "ad": "変換したい 2 進数を指定します。"}, "BIN2HEX": {"a": "(数値; [桁数])", "d": "2 進数を 16 進数に変換します。", "ad": "変換したい 2 進数を指定します。!使用する文字数 (桁数) を指定します。"}, "BIN2OCT": {"a": "(数値; [桁数])", "d": "2 進数を 8 進数に変換します。", "ad": "変換したい 2 進数を指定します。!使用する文字数 (桁数) を指定します。"}, "BITAND": {"a": "(数値1; 数値2)", "d": "2 つの数値のビット単位の 'And' を返します", "ad": "には値を求める 2 進数の 10 進表現を指定します!には値を求める 2 進数の 10 進表現を指定します"}, "BITLSHIFT": {"a": "(数値; 移動数)", "d": "左に移動数ビット移動する数値を返します", "ad": "には値を求める 2 進数の 10 進表現を指定します!には数値を左に移動するビット数を指定します"}, "BITOR": {"a": "(数値1; 数値2)", "d": "2 つの数値のビット単位の 'Or' を返します", "ad": "には値を求める 2 進数の 10 進表現を指定します!には値を求める 2 進数の 10 進表現を指定します"}, "BITRSHIFT": {"a": "(数値; 移動数)", "d": "右に移動数ビット移動する数値を返します", "ad": "には値を求める 2 進数の 10 進表現を指定します!には数値を右に移動するビット数を指定します"}, "BITXOR": {"a": "(数値1; 数値2)", "d": "2 つの数値のビット単位の 'Exclusive Or' を返します", "ad": "には値を求める 2 進数の 10 進表現を指定します!には値を求める 2 進数の 10 進表現を指定します"}, "COMPLEX": {"a": "(実数; 虚数; [虚数単位])", "d": "指定した実数係数および虚数係数を複素数に変換します。", "ad": "には、複素数の実数係数を指定します。!には、複素数の虚数係数を指定します。!には、複素数の虚数単位を表す文字を指定します。"}, "CONVERT": {"a": "(数値; 変換前単位; 変換後単位)", "d": "数値の単位を変換します。", "ad": "には、変換前の単位で数値を指定します。!には、変換前の単位を指定します。!には、変換後の単位を指定します。"}, "DEC2BIN": {"a": "(数値; [桁数])", "d": "10 進数を 2 進数に変換します。", "ad": "変換したい 10 進数を指定します。!使用する文字数 (桁数) を指定します。"}, "DEC2HEX": {"a": "(数値; [桁数])", "d": "10 進数を 16 進数に変換します。", "ad": "変換したい 16 進数を指定します。!使用する文字数 (桁数) を指定します。"}, "DEC2OCT": {"a": "(数値; [桁数])", "d": "10 進数を 8 進数に変換します。", "ad": "変換したい 8 進数を指定します。!使用する文字数 (桁数) を指定します。"}, "DELTA": {"a": "(数値1; [数値2])", "d": "2 つの数値が等しいかどうかを判別します。", "ad": "には、一方の数値を指定します。!には、もう一方の数値を指定します。"}, "ERF": {"a": "(下限; [上限])", "d": "誤差関数の積分値を返します。", "ad": "には、誤差関数を積分するときの下限値を指定します。!には、誤差関数を積分するときの上限値を指定します。"}, "ERF.PRECISE": {"a": "(X)", "d": "誤差関数の積分値を返します。", "ad": "には、誤差関数を積分するときの下限値を指定します。"}, "ERFC": {"a": "(x)", "d": "相補誤差関数の積分値を返します。", "ad": "には、誤差関数を積分するときの下限値を指定します。"}, "ERFC.PRECISE": {"a": "(X)", "d": "相補誤差関数の積分値を返します。", "ad": "には、誤差関数を積分するときの下限値を指定します。"}, "GESTEP": {"a": "(数値; [しきい値])", "d": "しきい値より大きいか小さいかの判定をします。", "ad": "には、しきい値との大小を比較する数値を指定します。!には、しきい値となる数値を指定します。"}, "HEX2BIN": {"a": "(数値; [桁数])", "d": "16 進数を 2 進数に変換します。", "ad": "変換したい 16 進数を指定します。!使用する文字数 (桁数) を指定します。"}, "HEX2DEC": {"a": "(数値)", "d": "16 進数を 10 進数に変換します。", "ad": "変換したい 16 進数を指定します。"}, "HEX2OCT": {"a": "(数値; [桁数])", "d": "16 進数を 8 進数に変換します。", "ad": "変換したい 16 進数を指定します。!使用する文字数 (桁数) を指定します。"}, "IMABS": {"a": "(複素数)", "d": "複素数の絶対値を計算します。", "ad": "には、計算の対象となる複素数を指定します。"}, "IMAGINARY": {"a": "(複素数)", "d": "複素数の虚部の係数を返します。", "ad": "には、計算の対象となる複素数を文字列として指定します。"}, "IMARGUMENT": {"a": "(複素数)", "d": "複素数を極形式で表現した場合の偏角θの値をラジアンを単位として計算します。", "ad": "には、計算の対象となる複素数を文字列として指定します。"}, "IMCONJUGATE": {"a": "(複素数)", "d": "複素数の共役複素数を文字列として返します。", "ad": "には、計算の対象となる複素数を文字列として指定します。"}, "IMCOS": {"a": "(複素数)", "d": "複素数のコサインを返します。", "ad": "には、コサインを求める複素数を指定します。"}, "IMCOSH": {"a": "(複素数)", "d": "複素数の双曲線余弦を返します。", "ad": "には双曲線余弦を求める複素数を指定します。"}, "IMCOT": {"a": "(複素数)", "d": "複素数の余接を返します。", "ad": "には余接を求める複素数を指定します。"}, "IMCSC": {"a": "(複素数)", "d": "複素数の余割を返します。", "ad": "には余割を求める複素数を指定します。"}, "IMCSCH": {"a": "(複素数)", "d": "複素数の双曲線余割を返します。", "ad": "には双曲線余割を求める複素数を指定します。"}, "IMDIV": {"a": "(複素数1; 複素数2)", "d": "2 つの複素数を割り算しその商を返します。", "ad": "には、割り算の分子となる複素数を指定します。!には、割り算の分母となる複素数を指定します。"}, "IMEXP": {"a": "(複素数)", "d": "複素数のべき乗を返します。", "ad": "には、べき乗を求める複素数を指定します。"}, "IMLN": {"a": "(複素数)", "d": "複素数の自然対数 (e を底とする対数) を計算します。", "ad": "には、計算の対象となる複素数を文字列として指定します。"}, "IMLOG10": {"a": "(複素数)", "d": "複素数の 10 を底とする対数を返します。", "ad": "には、常用対数を求める複素数を指定します。"}, "IMLOG2": {"a": "(複素数)", "d": "複素数の 2 を底とする対数を返します。", "ad": "には、2 を底とする対数を求める複素数を指定します。"}, "IMPOWER": {"a": "(複素数; 数値)", "d": "複素数を底として複素数の整数乗を計算します。", "ad": "には、計算の対象となる複素数を文字列として指定します。!には、複素数を底として何乗するかを整数で指定します。"}, "IMPRODUCT": {"a": "(複素数1; [複素数2]; ...)", "d": "1 ～ 255 個の複素数の積を計算します。", "ad": "複素数1   ,複素数2,... 積を求める複素数を 1 ～ 255 個までの範囲で指定します。"}, "IMREAL": {"a": "(複素数)", "d": "複素数の実数係数を返します。", "ad": "には、実数係数を求める複素数を指定します。"}, "IMSEC": {"a": "(複素数)", "d": "複素数の正割を返します。", "ad": "には正割を求める複素数を指定します。"}, "IMSECH": {"a": "(複素数)", "d": "複素数の双曲線正割を返します。", "ad": "には双曲線正割を求める複素数を指定します。"}, "IMSIN": {"a": "(複素数)", "d": "複素数のサインを返します。", "ad": "には、サインを求める複素数を指定します。"}, "IMSINH": {"a": "(複素数)", "d": "複素数の双曲線正弦を返します。", "ad": "には双曲線正弦を求める複素数を指定します。"}, "IMSQRT": {"a": "(複素数)", "d": "複素数の平方根を返します。", "ad": "には、平方根を求める複素数を指定します。"}, "IMSUB": {"a": "(複素数1; 複素数2)", "d": "2 つの複素数の差を返します。", "ad": "には、複素数2 を減算する元の複素数を指定します。!には、複素数1 から減算する複素数を指定します。"}, "IMSUM": {"a": "(複素数1; [複素数2]; ...)", "d": "2 つ以上の複素数の和を計算します。", "ad": "には、計算の対象となる複素数を 1 から 255 個までの範囲で指定します。"}, "IMTAN": {"a": "(複素数)", "d": "複素数の正接を返します。", "ad": "には正接を求める複素数を指定します。"}, "OCT2BIN": {"a": "(数値; [桁数])", "d": "8 進数を 2 進数に変換します。", "ad": "には、変換したい 8 進数を指定します。!には、2 進表記するときに使用する文字数を (桁数) 指定します。"}, "OCT2DEC": {"a": "(数値)", "d": "8 進数を 10 進数に変換します。", "ad": "には、変換したい 8 進数を指定します。"}, "OCT2HEX": {"a": "(数値; [桁数])", "d": "8 進数を 16 進数に変換します。", "ad": "には、変換したい 8 進数を指定します。!には、16 進表記するときに使用する文字数 (桁数) を指定します。"}, "DAVERAGE": {"a": "(データベース; フィールド; 条件)", "d": "データベースの指定された列を検索し、条件を満たすレコードの平均値を返します。", "ad": "にはリストまたはデータベースを構成するセル範囲を指定します。データベースはデータを関連付けたリストです。!には二重引用符 (\") で囲んだ列ラベルか、リスト内の列の位置を示す番号を指定します。!には指定した条件が設定されているセル範囲を指定します。範囲は、少なくとも列ラベルとその 1 つ下の検索条件を指定するセルを含む必要があります。"}, "DCOUNT": {"a": "(データベース; フィールド; 条件)", "d": "データベースの指定された列を検索し、条件を満たすレコードの中で数値が入力されているセルの個数を返します。", "ad": "にはリストまたはデータベースを構成するセル範囲を指定します。データベースはデータを関連付けたリストです。!には二重引用符 (\") で囲んだ列ラベルか、リスト内の列の位置を示す番号を指定します。!には指定した条件が設定されているセル範囲を指定します。範囲には、列ラベルとその 1 つ下の検索条件を指定するセルを選択します。"}, "DCOUNTA": {"a": "(データベース; フィールド; 条件)", "d": "条件を満たすレコードの中の空白でないセルの個数を返します。", "ad": "にはリストまたはデータベースを構成するセル範囲を指定します。データベースはデータを関連付けたリストです。!には二重引用符 (\") で囲んだ列ラベルか、リスト内の列の位置を示す番号を指定します。!には指定した条件が設定されているセル範囲を指定します。範囲には、列ラベルとその 1 つ下の検索条件を指定するセルを選択します。"}, "DGET": {"a": "(データベース; フィールド; 条件)", "d": "データベースの列から指定された条件を満たす 1 つのレコードを抽出します。", "ad": "にはリストまたはデータベースを構成するセル範囲を指定します。データベースはデータを関連付けたリストです。!には二重引用符 (\") で囲んだ列ラベルか、リスト内の列の位置を示す番号を指定します。!には指定した条件が設定されているセル範囲を指定します。範囲には、列ラベルとその 1 つ下の検索条件を指定するセルを選択します。"}, "DMAX": {"a": "(データベース; フィールド; 条件)", "d": "データベースの指定された列を検索し、条件を満たすレコードの最大値を返します。", "ad": "にはリストまたはデータベースを構成するセル範囲を指定します。データベースはデータを関連付けたリストです。!には二重引用符 (\") で囲んだ列ラベルか、リスト内の列の位置を示す番号を指定します。!には指定した条件が設定されているセル範囲を指定します。範囲には、列ラベルとその 1 つ下の検索条件を指定するセルを選択します。"}, "DMIN": {"a": "(データベース; フィールド; 条件)", "d": "データベースの指定された列を検索し、条件を満たすレコードの最小値を返します。", "ad": "にはリストまたはデータベースを構成するセル範囲を指定します。データベースはデータを関連付けたリストです。!には二重引用符 (\") で囲んだ列ラベルか、リスト内の列の位置を示す番号を指定します。!には指定した条件が設定されているセル範囲を指定します。範囲には、列ラベルとその 1 つ下の検索条件を指定するセルを選択します。"}, "DPRODUCT": {"a": "(データベース; フィールド; 条件)", "d": "条件を満たすデータベース レコードの指定したフィールドに入力されている数値の積を返します。", "ad": "にはリストまたはデータベースを構成するセル範囲を指定します。データベースはデータを関連付けたリストです。!には二重引用符 (\") で囲んだ列ラベルか、リスト内の列の位置を示す番号を指定します。!には指定した条件が設定されているセル範囲を指定します。範囲には、列ラベルとその 1 つ下の検索条件を指定するセルを選択します。"}, "DSTDEV": {"a": "(データベース; フィールド; 条件)", "d": "選択したデータベース レコードの標本を基に標準偏差を返します。", "ad": "にはリストまたはデータベースを構成するセル範囲を指定します。データベースはデータを関連付けたリストです。!には二重引用符 (\") で囲んだ列ラベルか、リスト内の列の位置を示す番号を指定します。!には指定した条件が設定されているセル範囲を指定します。範囲には、列ラベルとその 1 つ下の検索条件を指定するセルを選択します。"}, "DSTDEVP": {"a": "(データベース; フィールド; 条件)", "d": "条件を満たすレコードを母集団全体と見なして、母集団の標準偏差を返します。", "ad": "にはリストまたはデータベースを構成するセル範囲を指定します。データベースはデータを関連付けたリストです。!には二重引用符 (\") で囲んだ列ラベルか、リスト内の列の位置を示す番号を指定します。!には指定した条件が設定されているセル範囲を指定します。範囲には、列ラベルとその 1 つ下の検索条件を指定するセルを選択します。"}, "DSUM": {"a": "(データベース; フィールド; 条件)", "d": "データベースの指定された列を検索し、条件を満たすレコードの合計を返します。", "ad": "にはリストまたはデータベースを構成するセル範囲を指定します。データベースはデータを関連付けたリストです。!には二重引用符 (\") で囲んだ列ラベルか、リスト内の列の位置を示す番号を指定します。!には指定した条件が設定されているセル範囲を指定します。範囲には、列ラベルとその 1 つ下の検索条件を指定するセルを選択します。"}, "DVAR": {"a": "(データベース; フィールド; 条件)", "d": "条件を満たすデータベース レコードの指定したフィールドに入力した値を母集団の標本とみなして、母集団の分散を返します。", "ad": "にはリストまたはデータベースを構成するセル範囲を指定します。データベースはデータを関連付けたリストです。!には二重引用符 (\") で囲んだ列ラベルか、リスト内の列の位置を示す番号を指定します。!には指定した条件が設定されているセル範囲を指定します。範囲には、列ラベルとその 1 つ下の検索条件を指定するセルを選択します。"}, "DVARP": {"a": "(データベース; フィールド; 条件)", "d": "条件を満たすレコードを母集団全体と見なして、母集団の分散を返します。", "ad": "にはリストまたはデータベースを構成するセル範囲を指定します。データベースはデータを関連付けたリストです。!には二重引用符 (\") で囲んだ列ラベルか、リスト内の列の位置を示す番号を指定します。!には指定した条件が設定されているセル範囲を指定します。範囲には、列ラベルとその 1 つ下の検索条件を指定するセルを選択します。"}, "CHAR": {"a": "(数値)", "d": "使っているコンピューターの文字セットから、そのコード番号に対応する文字を返します。", "ad": "には変換する文字を表す 1 ～ 255 の範囲の数値を指定します。"}, "CLEAN": {"a": "(文字列)", "d": "印刷できない文字を文字列から削除します。", "ad": "には印刷できない文字を削除するワークシートの文字データを指定します。"}, "CODE": {"a": "(文字列)", "d": "文字列の先頭文字を表す数値コードを返します。", "ad": "には先頭文字の数値コードを調べたい文字列を指定します。"}, "CONCATENATE": {"a": "(文字列1; [文字列2]; ...)", "d": "複数の文字列を結合して 1 つの文字列にまとめます。", "ad": "には 1 つにまとめる 1 ～ 255 個までの文字列を指定できます。引数には文字列、数値、または単一セルの参照を指定します。"}, "CONCAT": {"a": "(テキスト1; ...)", "d": " テキスト文字列の一覧または範囲を連結します", "ad": " は単一のテキスト文字列に結合される 1 から 254 までのテキスト文字列です"}, "DOLLAR": {"a": "(数値; [桁数])", "d": "数値を四捨五入し、通貨書式を設定した文字列に変換します", "ad": "には数値、数値を含むセルの参照、または結果が数値となる数式を指定します!には小数点以下の桁数を指定します。数値は必要に応じて四捨五入されます。桁数を省略すると、2 を指定したと見なされます"}, "EXACT": {"a": "(文字列1; 文字列2)", "d": "2 つの文字列を比較し、同じものであれば TRUE、異なれば FALSE を返します。EXACT 関数では、英字の大文字と小文字は区別されます。", "ad": "には 1 つめ文字列を指定します。!には 2 つめ文字列を指定します。"}, "FIND": {"a": "(検索文字列; 対象; [開始位置])", "d": "文字列が他の文字列内で最初に現れる位置を検索します。大文字と小文字は区別されます。", "ad": "には検索する文字列を指定します。空文字列 (\"\") を指定した場合、対象の先頭文字と一致したものと見なされます。ワイルドカード文字は使用できません。!には検索文字列を含む文字列を指定します。!には検索を開始する位置を指定します。対象の先頭文字から検索を開始するときは 1 を指定します。開始位置を省略すると、1 を指定したと見なされます。"}, "FINDB": {"a": "(検索文字列; 対象; [開始位置])", "d": "指定された文字列を他の文字列の中で検索し、その文字列が最初に現れる位置を左端から数え、その番号を返します。関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。", "ad": "には検索する文字列を指定します。空文字列 (\"\") を指定した場合、対象の先頭文字と一致したものと見なされます。ワイルドカード文字は使用できません。!には検索文字列を含む文字列を指定します。!には検索を開始する位置を指定します。対象の先頭文字から検索を開始するときは 1 を指定します。開始位置を省略すると、1 を指定したと見なされます。"}, "FIXED": {"a": "(数値; [桁数]; [桁区切り])", "d": "数値を指定された小数点で四捨五入し、コンマ (,) を使って、または使わずに書式設定した文字列に変換します。", "ad": "には四捨五入して、文字列に変換したい数値を指定します。!には小数点以下の桁数を指定します。桁数を省略すると、2 を指定したと見なされます。!には計算結果をコンマ ( , ) で桁区切りするかどうかを、論理値 (コンマで桁区切りしない = TRUE、コンマで桁区切りする = FALSE) で指定します。"}, "LEFT": {"a": "(文字列; [文字数])", "d": "文字列の先頭から指定された数の文字を返します。", "ad": "には取り出す文字を含む文字列を指定します。!には取り出す文字数を指定します。省略すると、1 を指定したと見なされます。"}, "LEFTB": {"a": "(文字列; [文字数])", "d": "関数は、文字列の先頭から指定されたバイト数の文字を返します。関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。", "ad": "には取り出す文字を含む文字列を指定します。!には取り出す文字数を指定します。省略すると、1 を指定したと見なされます。"}, "LEN": {"a": "(文字列)", "d": "文字列の長さ (文字数) を返します。半角と全角の区別なく、1 文字を 1 として処理します。", "ad": "には長さを求めたい文字列を指定します。"}, "LENB": {"a": "(文字列)", "d": "関数は、文字列のバイト数を返します。関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。", "ad": "には長さを求めたい文字列を指定します。"}, "LOWER": {"a": "(文字列)", "d": "文字列に含まれる英字をすべて小文字に変換します。", "ad": "には小文字に変換する文字列を指定します。それ以外の文字は変換されません。"}, "MID": {"a": "(文字列; 開始位置; 文字数)", "d": "文字列の指定された位置から、指定された数の文字を返します。半角と全角の区別なく、1 文字を 1 として処理します。", "ad": "には検索の対象となる文字を含む文字列を指定します。!には抜き出したい文字列の先頭文字の位置を指定します。!には文字列から抜き出す文字の数を指定します。"}, "MIDB": {"a": "(文字列; 開始位置; 文字数)", "d": "関数は、文字列の任意の位置から指定されたバイト数の文字を返します。関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。", "ad": "には検索の対象となる文字を含む文字列を指定します。!には抜き出したい文字列の先頭文字の位置を指定します。!には文字列から抜き出す文字の数を指定します。"}, "NUMBERVALUE": {"a": "(文字列; [小数点記号]; [桁区切り記号])", "d": "文字列をロケールに依存しない方法で数値に変換します。", "ad": "には変換する数値を表す文字列を指定します。!には文字列で小数点記号として使用する文字を指定します。!には文字列でグループ区切り記号として使用する文字を指定します。"}, "PROPER": {"a": "(文字列)", "d": "文字列中の各単語の先頭文字を大文字に変換した結果を返します。", "ad": "には二重引用符で囲んだ文字列、文字列を返す数式、または先頭を大文字にしたい文字列が入力されているセルへの参照を指定します。"}, "REPLACE": {"a": "(文字列; 開始位置; 文字数; 置換文字列)", "d": "文字列中の指定した位置の文字列を置き換えた結果を返します。半角と全角の区別なく、1 文字を 1 として処理します。", "ad": "には置き換えたい文字列が含まれる文字列を指定します。!には <置換文字列> で置き換えたい文字列の先頭文字の位置を指定します。!には <置換文字列> で置き換えたい文字列の文字数を指定します。!には置き換え後の文字列を指定します。"}, "REPLACEB": {"a": "(文字列; 開始位置; 文字数; 置換文字列)", "d": "関数は、文字列中の指定されたバイト数の文字を別の文字に置き換えます。関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。", "ad": "には置き換えたい文字列が含まれる文字列を指定します。!には <置換文字列> で置き換えたい文字列の先頭文字の位置を指定します。!には <置換文字列> で置き換えたい文字列の文字数を指定します。!には置き換え後の文字列を指定します。"}, "REPT": {"a": "(文字列; 繰り返し回数)", "d": "文字列を指定された回数だけ繰り返して表示します。この関数を使用して、セル幅全体に文字列を表示することができます。", "ad": "には繰り返す文字列を指定します。!には文字列を繰り返す回数を、正の数値で指定します。"}, "RIGHT": {"a": "(文字列; [文字数])", "d": "文字列の末尾から指定された文字数の文字を返します。", "ad": "には取り出す文字が含まれる文字列を指定します。!には取り出す文字数を指定します。省略すると、1 を指定したと見なされます。"}, "RIGHTB": {"a": "(文字列; [バイト数])", "d": "関数は、文字列の末尾 (右端) から指定されたバイト数の文字を返します。関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。", "ad": "には取り出す文字が含まれる文字列を指定します。!には取り出す文字数を指定します。省略すると、1 を指定したと見なされます。"}, "SEARCH": {"a": "(検索文字列; 対象; [開始位置])", "d": "文字列が最初に現れる位置の文字番号を返します。大文字、小文字は区別されません。", "ad": "には検索する文字列を指定します。半角の疑問符 (?) または半角のアスタリスク (*) をワイルドカード文字として使用することができます。!には検索文字列を含む文字列を指定します。!には検索を開始する位置を、文字列の左から数えた文字数で指定します。省略すると、1 を指定したと見なされます。 "}, "SEARCHB": {"a": "(検索文字列,対象,[開始位置])", "d": "関数は、指定された文字列を他の文字列の中で検索し、その文字列が最初に現れる位置を左端から数え、その番号を返します。 関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。", "ad": "には検索する文字列を指定します。半角の疑問符 (?) または半角のアスタリスク (*) をワイルドカード文字として使用することができます。!には検索文字列を含む文字列を指定します。!には検索を開始する位置を、文字列の左から数えた文字数で指定します。省略すると、1 を指定したと見なされます。 "}, "SUBSTITUTE": {"a": "(文字列; 検索文字列; 置換文字列; [置換対象])", "d": "文字列中の指定した文字を新しい文字で置き換えます。", "ad": "には置き換える文字を含む文字列、または目的の文字列が入力されたセル参照を指定します。!には置き換え前の文字列を指定します。検索文字列と置換文字列の大文字小文字の表記が異なる場合、文字列は置換されません。!には置き換え後の文字列を指定します。!には文字列に含まれるどの検索文字列を置換文字列に置き換えるかを指定します。省略された場合は、文字列中のすべての検索文字列が置き換えの対象となります。"}, "T": {"a": "(値)", "d": "値が文字列を参照する場合はその文字列を返し、文字列以外のデータを参照する場合は、空文字列 (\"\") を返します。", "ad": "には文字列に変換する値を指定します。"}, "TEXT": {"a": "(値; 表示形式)", "d": "数値に指定した書式を設定し、文字列に変換した結果を返します。", "ad": "には数値、結果が数値となる数式、または数値が入力されているセルへの参照を指定します。!には [セルの書式設定] ダイアログ ボックスの [表示形式] タブの [分類] ボックスに表示されている数値形式を、文字列として指定します"}, "TEXTJOIN": {"a": "(区切り文字; 空のセルは無視; テキスト1; ...)", "d": " 区切り文字を使用してテキスト文字列の一覧または範囲を連結します", "ad": " 各テキスト アイテム間に挿入する文字または文字列!真 (既定) の場合、空のセルは無視されます! は結合される 1 から 252 のテキスト文字列または範囲です"}, "TRIM": {"a": "(文字列)", "d": "単語間のスペースを 1 つずつ残して、不要なスペースをすべて削除します。", "ad": "には余分なスペースを削除する文字列を指定します。"}, "UNICHAR": {"a": "(数値)", "d": "指定された数値により参照される Unicode 文字を返します。", "ad": "には文字を表す Unicode 番号を指定します。"}, "UNICODE": {"a": "(文字列)", "d": "文字列の最初の文字に対応する番号 (コード ポイント) を返します。", "ad": "には Unicode 値を求める文字を指定します。"}, "UPPER": {"a": "(文字列)", "d": "文字列に含まれる英字をすべて大文字に変換します。", "ad": "には大文字に変換する文字列を指定します。文字列には、目的の文字列が入力されたセル参照を指定することもできます。"}, "VALUE": {"a": "(文字列)", "d": "文字列として入力されている数字を数値に変換します。", "ad": "には文字列を半角の二重引用符 (\") で囲んで指定するか、または変換する文字列を含むセル参照を指定します。"}, "AVEDEV": {"a": "(数値1; [数値2]; ...)", "d": "データ全体の平均値に対するそれぞれのデータの絶対偏差の平均を返します。引数には、数値、数値を含む名前、配列、セル参照を指定できます。", "ad": "には絶対偏差の平均を求めたい引数を、1 ～ 255 個まで指定できます。"}, "AVERAGE": {"a": "(数値1; [数値2]; ...)", "d": "引数の平均値を返します。引数には、数値、数値を含む名前、配列、セル参照を指定できます。", "ad": "には平均を求めたい数値を、1 から 255 個まで指定します。"}, "AVERAGEA": {"a": "(値1; [値2]; ...)", "d": "引数の平均値を返します。引数の文字列および FALSE は 0、TRUE は 1 と見なします。引数には、数値、名前、配列、参照を含むことができます", "ad": "には平均を算出する値を、1 から 255 個まで指定します"}, "AVERAGEIF": {"a": "(範囲; 条件; [平均対象範囲])", "d": "特定の条件に一致する数値の平均 (算術平均) を計算します", "ad": "には、評価の対象となるセル範囲を指定します。!には、平均の計算対象となるセルを定義する条件を、数値、式、または文字列で指定します。!には、実際に平均を求めるのに使用されるセルを指定します。省略した場合、範囲内のセルが使用されます。"}, "AVERAGEIFS": {"a": "(平均対象範囲; 条件範囲; 条件; ...)", "d": "特定の条件に一致する数値の平均 (算術平均) を計算します", "ad": "には、実際に平均を求めるのに使用するセルを指定します!には、特定の条件で値を求める対象となるセル範囲を指定します!には、平均を求めるのに使用するセルを定義する条件を、数値、式、または文字列で指定します"}, "BETADIST": {"a": "(x; α; β; [A]; [B])", "d": "累積β確率密度関数を返します", "ad": "には区間 A から B の範囲で、関数に代入する値を指定します!には確率分布に対するパラメーターを指定します。0 より大きい値を指定する必要があります!には確率分布に対するパラメーターを指定します。0 より大きい値を指定する必要があります!には x の区間の下限値を指定します。この引数を省略すると A = 0 として計算されます!には x の区間の上限値を指定します。この引数を省略すると B = 1 として計算されます"}, "BETAINV": {"a": "(確率; α; β; [A]; [B])", "d": "累積β確率密度関数の逆関数を返します。", "ad": "にはβ確率分布における確率を指定します。!には確率分布のパラメーターを指定します。0 より大きい値を指定する必要があります。!には確率分布のパラメーターを指定します。0 より大きい値を指定する必要があります。!には x の区間の下限値を指定します。この引数を省略すると A = 0 として計算されます。!には x の区間の上限値を指定します。この引数を省略すると B = 1 として計算されます。"}, "BETA.DIST": {"a": "(x; α; β; 関数形式; [A]; [B])", "d": "β確率分布関数を返します", "ad": "には区間 A から B の範囲で、関数に代入する値を指定します!には分布に対するパラメーターを指定します。0 より大きい値を指定する必要があります!には分布に対するパラメーターを指定します。0 より大きい値を指定する必要があります!には論理値を指定します。TRUE を指定した場合は累積分布関数が返され、FALSE を指定した場合は確率密度関数が返されます!には x の区間の下限値を指定します。この引数を省略すると A = 0 として計算されます!には x の区間の上限値を指定します。この引数を省略すると B = 1 として計算されます"}, "BETA.INV": {"a": "(確率; α; β; [A]; [B])", "d": "累積β確率密度関数の逆関数 (BETA.DIST) を返します。", "ad": "にはβ確率分布における確率を指定します。!には確率分布のパラメーターを指定します。0 より大きい値を指定する必要があります。!には確率分布のパラメーターを指定します。0 より大きい値を指定する必要があります。!には x の区間の下限値を指定します。この引数を省略すると A = 0 として計算されます。!には x の区間の下限値を指定します。この引数を省略すると B = 1 として計算されます。"}, "BINOMDIST": {"a": "(成功数; 試行回数; 成功率; 関数形式)", "d": "二項分布の確率を返します。", "ad": "には試行回数中の成功の回数を指定します。!には独立試行の回数を指定します。!には試行 1 回あたりの成功率を指定します。!には関数の形式を表す論理値を指定します。TRUE を指定した場合、累積分布関数が返されます。FALSE を指定した場合、確率密度関数が返されます。"}, "BINOM.DIST": {"a": "(成功数; 試行回数; 成功率; 関数形式)", "d": "二項分布の確率を返します。", "ad": "には試行回数中の成功の回数を指定します。!には独立試行の回数を指定します。!には試行 1 回あたりの成功率を指定します。!には関数を示す論理値を指定します。TRUE を指定した場合は累積分布関数が返され、FALSE を指定した場合は確率密度関数が返されます。"}, "BINOM.DIST.RANGE": {"a": "(試行回数; 成功率; 成功数; [成功数2])", "d": "二項分布を使用した試行結果の確率を返します。", "ad": "には独立試行の回数を指定します。!には試行 1 回あたりの成功率を指定します。!には試行回数中の成功の回数を指定します。!この関数を指定した場合、成功試行数が成功数と成功数2 の間になる確率を返します。"}, "BINOM.INV": {"a": "(試行回数; 成功率; α)", "d": "累積二項分布の値が基準値以上になるような最小の値を返します。", "ad": "にはベルヌーイ試行の回数を指定します。!には試行 1 回あたりの成功率 (0 ～ 1 の数値) を指定します。!基準値 (0 ～ 1 の数値) を指定します。"}, "CHIDIST": {"a": "(x; 自由度)", "d": "カイ 2 乗分布の右側確率の値を返します", "ad": "には分布を計算する値 (正の数値) を指定します!には自由度 (10^10 を除く 1 ～ 10^10 の間の数値) を指定します"}, "CHIINV": {"a": "(確率; 自由度)", "d": "カイ 2 乗分布の右側確率の逆関数の値を返します。", "ad": "にはカイ 2 乗分布における確率 (0 ～ 1 の値) を指定します。!には自由度 (10^10 を除く 1 ～ 10^10 の間の数値) を指定します。"}, "CHITEST": {"a": "(実測値範囲; 期待値範囲)", "d": "統計と自由度に対するカイ 2 乗分布から値を抽出して返します。", "ad": "には期待値に対して検定される実測値が入力した範囲を指定します。!には総計に対する行の合計と列の合計の積の割合が入力されている範囲を指定します。"}, "CHISQ.DIST": {"a": "(x; 自由度; 関数形式)", "d": "カイ 2 乗分布の左側確率の値を返します", "ad": "には分布を計算する値 (正の数値) を指定します!には自由度 (10^10 を除く 1 ～ 10^10 の間の数値) を指定します!には計算に使用する関数の形式を表す論理値を指定します。TRUE の場合は累積分布関数、FALSE の場合は確率密度関数が返されます"}, "CHISQ.DIST.RT": {"a": "(x; 自由度)", "d": "カイ 2 乗分布の右側確率の値を返します", "ad": "には分布を評価する値 (正の数値) を指定します!には自由度 (10^10 を除く 1 ～ 10^10 の間の数値) を指定します"}, "CHISQ.INV": {"a": "(確率; 自由度)", "d": "カイ 2 乗分布の左側確率の逆関数の値を返します。", "ad": "にはカイ 2 乗分布における確率 (0 ～ 1 の値) を指定します。!には自由度 (10^10 を除く 1 ～ 10^10 の間の数値) を指定します。"}, "CHISQ.INV.RT": {"a": "(確率; 自由度)", "d": "カイ 2 乗分布の右側確率の逆関数の値を返します。", "ad": "にはカイ 2 乗分布における確率 (0 ～ 1 の値) を指定します。!には自由度 (10^10 を除く 1 ～ 10^10 の間の数値) を指定します。"}, "CHISQ.TEST": {"a": "(実測値範囲; 期待値範囲)", "d": "統計と自由度に対するカイ 2 乗分布から値を抽出して返します。", "ad": "には期待値に対して検定される実測値が入力されている範囲を指定します。!には総計に対する行の合計と列の合計の積の割合が入力されている範囲を指定します。"}, "CONFIDENCE": {"a": "(α; 標準偏差; 標本数)", "d": "正規分布を使用して、母集団の平均に対する信頼区間を求めます。", "ad": "には信頼度を計算するために使用する有意水準 (0 より大きく 1 未満の数値) を指定します。!にはデータ範囲に対する母集団の標準偏差を指定します。これは、既知であると仮定されます。標準偏差は 0 より大きい値である必要があります。!には標本数を指定します。"}, "CONFIDENCE.NORM": {"a": "(α; 標準偏差; 標本数)", "d": "正規分布を使用して、母集団の平均に対する信頼区間を求めます。", "ad": "には信頼度を計算するために使用する有意水準 (0 より大きく 1 未満の数値) を指定します。!にはデータ範囲に対する母集団の標準偏差を指定します。これは、既知であると仮定されます。!には標本数を指定します。"}, "CONFIDENCE.T": {"a": "(α; 標準偏差; 標本数)", "d": "スチューデントの T 分布を使用して、母集団の平均に対する信頼区間を求めます。", "ad": "には信頼度を計算するために使用する有意水準 (0 より大きく 1 未満の数値) を指定します。!にはデータ範囲に対する母集団の標準偏差を指定します。これは、既知であると仮定されます。!には標本数を指定します。"}, "CORREL": {"a": "(配列1; 配列2)", "d": "2 つの配列の相関係数を返します。", "ad": "には値 (数値、名前、配列、数値を含むセル参照) のセル範囲を指定します。!には値 (数値、名前、配列、数値を含むセル参照) の 2 番目のセル範囲を指定します。"}, "COUNT": {"a": "(値1; [値2]; ...)", "d": "範囲内の、数値が含まれるセルの個数を返します。", "ad": "にはデータまたはデータが入力したセルの参照を 1 から 255 個まで指定します。数値データだけがカウントされます。"}, "COUNTA": {"a": "(値1; [値2]; ...)", "d": "範囲内の、空白でないセルの個数を返します。", "ad": "にはカウントしたい値およびセルを表す引数を 1 ～ 255 個まで指定します。すべてのデータ型の値が計算の対象となります。"}, "COUNTBLANK": {"a": "(範囲)", "d": "範囲に含まれる空白セルの個数を返します。", "ad": "には空白セルの個数を求めたいセル範囲を指定します。"}, "COUNTIF": {"a": "(範囲; 検索条件)", "d": "指定された範囲に含まれるセルのうち、検索条件に一致するセルの個数を返します。", "ad": "には空白でないセルの個数を求めるセル範囲を指定します。!には計算の対象となるセルを定義する条件を、数値、式、または文字列で指定します。"}, "COUNTIFS": {"a": "(検索条件範囲; 検索条件; ...)", "d": "特定の条件に一致するセルの個数を返します", "ad": "には、特定の条件で値を求める対象となるセル範囲を指定します!には、値を求める対象となるセルを定義する条件を、数値、式、または文字列で指定します"}, "COVAR": {"a": "(配列1; 配列2)", "d": "共分散を返します。共分散とは、2 組の対応するデータ間での標準偏差の積の平均値です。", "ad": "には整数のデータが入力されている最初のセル範囲を指定します。引数には数値、配列、または数値を含む参照を指定します。!には整数のデータが入力されている 2 番目のセル範囲を指定します。引数には数値、配列、または数値を含む参照を指定します。"}, "COVARIANCE.P": {"a": "(配列1; 配列2)", "d": "母集団の共分散を返します。共分散とは、2 組の対応するデータ間での標準偏差の積の平均値です。", "ad": "には整数のデータが入力されている最初のセル範囲を指定します。引数には数値、配列、または数値を含む参照を指定します。!には整数のデータが入力されている 2 番目のセル範囲を指定します。引数には数値、配列、または数値を含む参照を指定します。"}, "COVARIANCE.S": {"a": "(配列1; 配列2)", "d": "標本の共分散を返します。共分散とは、2 組の対応するデータ間での標準偏差の積の平均値です。", "ad": "には整数のデータが入力されている最初のセル範囲を指定します。引数には数値、配列、または数値を含む参照を指定します。!には整数のデータが入力されている 2 番目のセル範囲を指定します。引数には数値、配列、または数値を含む参照を指定します。"}, "CRITBINOM": {"a": "(試行回数; 成功率; α)", "d": "累積二項分布の値が基準値以上になるような最小の値を返します。", "ad": "にはベルヌーイ試行の回数を指定します。!には試行 1 回あたりの成功率 (0 ～ 1 の数値) を指定します。!には基準値 (0 ～ 1 の数値) を指定します。"}, "DEVSQ": {"a": "(数値1; [数値2]; ...)", "d": "標本の平均値に対する各データの偏差の平方和を返します。", "ad": "には偏差の平方和を求めたい引数、または配列、配列参照を、1 ～ 255 個まで指定できます。"}, "EXPONDIST": {"a": "(x; λ; 関数形式)", "d": "指数分布関数を返します。", "ad": "には関数に代入する負でない値を指定します。!には正の数値のパラメーターを指定します。!には計算に使用する指数関数の形式を表す論理値を指定します。TRUE の場合、戻り値は累積分布関数となり、FALSE の場合は、確率密度関数が返されます。"}, "EXPON.DIST": {"a": "(x; λ; 関数形式)", "d": "指数分布関数を返します。", "ad": "には関数に代入する負でない値を指定します。!には正の数値のパラメーターを指定します。!には計算に使用する指数関数の形式を表す論理値を指定します。TRUE の場合は累積分布関数、FALSE の場合は確率密度関数が返されます。"}, "FDIST": {"a": "(x; 自由度1; 自由度2)", "d": "2 つのデータ セットの (右側) F 確率分布を返します", "ad": "には関数に代入する負でない数値を指定します!には分子の自由度 (10^10 を除く 1 ～ 10^10 の間の数値) を指定します!には分母の自由度 (10^10 を除く 1 ～ 10^10 の間の数値) を指定します"}, "FINV": {"a": "(確率; 自由度1; 自由度2)", "d": "F 確率分布の逆関数を返します。つまり、確率 = FDIST(x,...) であるとき、FINV(確率,...) = x となるような x の値を返します。", "ad": "には F 累積分布における確率 (0 ～ 1 の数値) を指定します。!には分子の自由度 (10^10 を除く 1 ～ 10^10 の間の数値) を指定します。!には分母の自由度 (10^10 を除く 1 ～ 10^10 の間の数値) を指定します。"}, "FTEST": {"a": "(配列1; 配列2)", "d": "F-検定の結果を返します。F-検定により、配列 1 と配列 2 とのデータのばらつきに有意な差が認められない両側確率が返されます。", "ad": "には比較対象となる一方のデータ (数値、名前、配列、数値を含むセル参照) を含む配列またはセル範囲を指定します。空白は無視されます。!には比較対象となるもう一方のデータ (数値、名前、配列、数値を含むセル参照) を含む配列またはセル範囲を指定します。空白は無視されます。"}, "F.DIST": {"a": "(x; 自由度1; 自由度2; 関数形式)", "d": "(左側) F 確率分布を返します", "ad": "には関数に代入する負でない数値を指定します!には分子の自由度 (10^10 を除く 1～ 10^10 の間の数値) を指定します!には分母の自由度 (10^10 を除く 1～ 10^10 の間の数値) を指定します!には計算に使用する関数の形式を表す論理値を指定します。TRUE の場合は累積分布関数、FALSE の場合は確率密度関数が返されます"}, "F.DIST.RT": {"a": "(x; 自由度1; 自由度2)", "d": "(右側) F 確率分布を返します", "ad": "には関数に代入する負でない数値を指定します!には分子の自由度 (10^10 を除く 1～ 10^10 の間の数値) を指定します!には分母の自由度 (10^10 を除く 1～ 10^10 の間の数値) を指定します"}, "F.INV": {"a": "(確率; 自由度1; 自由度2)", "d": "(左側) F 確率分布の逆関数を返します。", "ad": "には F 累積分布における確率 (0～1 の数値) を指定します。!には分子の自由度 (10^10 を除く 1～ 10^10 の間の数値) を指定します。!には分母の自由度 (10^10 を除く 1～ 10^10 の間の数値) を指定します。"}, "F.INV.RT": {"a": "(確率; 自由度1; 自由度2)", "d": "(右側) F 確率分布の逆関数を返します。", "ad": "には F 累積分布における確率 (0～1 の数値) を指定します。!には分子の自由度 (10^10 を除く 1～ 10^10 の間の数値) を指定します。!には分母の自由度 (10^10 を除く 1～ 10^10 の間の数値) を指定します。"}, "F.TEST": {"a": "(配列1; 配列2)", "d": "F-検定の結果を返します。F-検定により、配列 1 と配列 2 とのデータのばらつきに有意な差が認められない両側確率が返されます。", "ad": "には比較対象となる一方のデータ (数値、名前、配列、数値を含むセル参照) を含む配列またはセル範囲を指定します。空白は無視されます。!には比較対象となるもう一方のデータ (数値、名前、配列、数値を含むセル参照) を含む配列またはセル範囲を指定します。空白は無視されます。"}, "FISHER": {"a": "(x)", "d": "フィッシャー変換の結果を返します。", "ad": "には関数に代入する値 (-1 および 1 を除く -1～1 の数値) を指定します。"}, "FISHERINV": {"a": "(y)", "d": "フィッシャー変換の逆関数を返します。y = FISHER(x) であるとき、FISHERINV(y) = x という関係が成り立ちます。", "ad": "には逆変換の対象となる値を指定します。"}, "FORECAST": {"a": "(x; 既知のy; 既知のx)", "d": "既知の値を使用し、線形トレンドに沿って将来の値を予測します。", "ad": "には値を予測する対象となるデータ ポイントを数値で指定します。!には独立した配列、またはデータの範囲を指定します。!には独立した配列、または数値データの範囲を指定します。既知の x の値の分散が 0 以外である必要があります。"}, "FORECAST.ETS": {"a": "(目標期日; 値; タイムライン; [季節性]; [データ補間]; [集計])", "d": "指数平滑化法を使用して、今後の指定の目標期日における予測値を返します。", "ad": " は Spreadsheet Editor が値を予測するデータ要素です。タイムラインで値のパターンを続ける必要があります! は予測中の数値データの配列または範囲です! は数値データの独立した配列または範囲です。タイムラインの日付には、ゼロ以外の一定の間隔が必要です! は季節パターンの長さを示すオプションの数値です。既定値の 1 は季節性が自動的に検出されることを示します! は不足している値を処理するオプションの値です。既定値の 1 では不足している値が補間により置き換わり、0 ではゼロで置き換わります! は同じタイム スタンプの複数の値を集計するためのオプションの数値です。空白の場合、Spreadsheet Editor は値の平均値を返します"}, "FORECAST.ETS.CONFINT": {"a": "(目標期日; 値; タイムライン; [信頼レベル]; [季節性]; [データ補間]; [集計])", "d": "指定の目標期日における予測値の信頼区間を返します", "ad": " は Spreadsheet Editor が値を予測するデータ要素です。タイムラインで値のパターンを続ける必要があります! は予測中の数値データの配列または範囲です! は数値データの独立した配列または範囲です。タイムラインの日付には、ゼロ以外の一定の間隔が必要です! は 0 から 1 の数値であり、計算された信頼区間の信頼度を示します。既定値は .95 です! は季節パターンの長さを示すオプションの数値です。既定値の 1 は季節性が自動的に検出されることを示します! は不足している値を処理するオプションの値です。既定値の 1 では不足している値が補間により置き換わり、0 ではゼロで置き換わります! は同じタイム スタンプの複数の値を集計するためのオプションの数値です。空白の場合、Spreadsheet Editor は値の平均値を返します"}, "FORECAST.ETS.SEASONALITY": {"a": "(値; タイムライン; [データ補間]; [集計])", "d": "アプリが指定の時系列に対して検出する繰り返しパターンの長さを返します。", "ad": "には、予測中の数値データの配列または範囲を指定します。!には、数値データの独立した配列または範囲を指定します。タイムラインの日付には、ゼロ以外の一定の間隔が必要です。!は、不足している値を処理するオプションの値です。既定値の 1 では、補間により、不足している値が置き換わり、0 では、不足している値がゼロで置き換わります。!は、同じタイム スタンプの複数の値を合計するオプションの数値です。空白にすると、Spreadsheet Editor は値の平均を出します。"}, "FORECAST.ETS.STAT": {"a": "(値; タイムライン; 統計の種類; [季節性]; [データ補間]; [集計])", "d": "予測のために要求された統計を返します。", "ad": "には、予測中の数値データの配列または範囲を指定します。!には、数値データの独立した配列または範囲を指定します。タイムラインの日付には、ゼロ以外の一定の間隔が必要です。!は 1 から 8 の数値であり、計算された予測のために返される統計 Spreadsheet Editor を示します。!は、季節のパターンの長さを示すオプションの数値です。既定値の 1 は、季節性が自動的に検出されることを示します。!は、不足している値を処理するオプションの値です。既定値の 1 では、補間により、不足している値が置き換わり、0 では、不足している値がゼロで置き換わります。!は、同じタイム スタンプの複数の値を合計するオプションの数値です。空白にすると、Spreadsheet Editor は値の平均を出します。"}, "FORECAST.LINEAR": {"a": "(x; 既知のy; 既知のx)", "d": "既知の値を使用し、線形トレンドに沿って将来の値を予測します。", "ad": "には値を予測する対象となるデータ ポイントを数値で指定します。!には独立した配列、またはデータの範囲を指定します。!には独立した配列、または数値データの範囲を指定します。既知の x の値の分散が 0 以外である必要があります。"}, "FREQUENCY": {"a": "(データ配列; 区間配列)", "d": "範囲内でのデータの度数分布を、垂直配列で返します。返された配列要素の個数は、区間配列の個数より 1 つだけ多くなります。", "ad": "には度数分布を求めたい値の配列、または参照を指定します。空白セルおよび文字列は無視されます。!にはデータ配列で指定したデータをグループ化するため、値の間隔を配列または参照として指定します。"}, "GAMMA": {"a": "(x)", "d": "ガンマ関数値を返します。", "ad": "にはガンマを計算する値を指定します。"}, "GAMMADIST": {"a": "(x; α; β; 関数形式)", "d": "γ分布関数の値を返します", "ad": "には関数に代入する負でない数値を指定します!には正の数値の確率分布のパラメーターを指定します!には正の数値の確率分布のパラメーターを指定します。1 を指定した場合、標準γ分布の値が返されます!関数の形式を表す論理値を指定します。TRUE を指定した場合、累積分布関数が返されます。FALSE を指定した場合、または省略した場合、確率量関数が返されます"}, "GAMMA.DIST": {"a": "(x; α; β; 関数形式)", "d": "γ分布関数の値を返します", "ad": "には分布を評価する値 (正の数値) を指定します!には正の数値の確率分布のパラメーターを指定します!には正の数値の確率分布のパラメーターを指定します。1 を指定した場合、標準γ分布の値が返されます!には関数の形式を表す論理値を指定します。TRUE を指定した場合は、累積分布関数が返されます。FALSE を指定した場合または省略した場合は、確率量関数が返されます"}, "GAMMAINV": {"a": "(確率; α; β)", "d": "γ累積分布の逆関数の値を返します。つまり、確率 = GAMMADIST(x,...) であるとき、GAMMAINV(確率,...) = x となるような x の値を返します。", "ad": "にはガンマ確率分布における確率 (0 ～ 1 の数値) を指定します。!には確率分布のパラメーター (正の数値) を指定します。!には確率分布のパラメーター (正の数値) を指定します。1 を指定した場合、標準γ分布の値が返されます。"}, "GAMMA.INV": {"a": "(確率; α; β)", "d": "γ累積分布の逆関数の値を返します。つまり、確率 = GAMMA.DIST(x,...) であるとき、GAMMA.INV(確率,...) = x となるような x の値を返します。", "ad": "にはガンマ確率分布における確率 (0 ～ 1 の数値) を指定します。!には確率分布のパラメーター (正の数値) を指定します。!には確率分布のパラメーター (正の数値) を指定します。1 を指定した場合、標準γ分布の値が返されます。"}, "GAMMALN": {"a": "(x)", "d": "γ関数 G(x) の自然対数を返します。", "ad": "には関数に代入する値を正の数値で指定します。"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "γ関数 G(x) の自然対数を返します。", "ad": "には関数に代入する値を正の数値で指定します。"}, "GAUSS": {"a": "(x)", "d": "標準正規分布の累積分布関数より小さい 0.5 を返します。", "ad": "には分布を求める値を指定します。"}, "GEOMEAN": {"a": "(数値1; [数値2]; ...)", "d": "正の数からなる配列またはセル範囲のデータの幾何平均を返します。", "ad": "には幾何平均を求めたい数値、または数値を含む名前、配列、セル参照を 1 ～ 255 個まで指定できます。"}, "GROWTH": {"a": "(既知のy; [既知のx]; [新しいx]; [定数])", "d": "既知のデータ ポイントに対応する指数トレンドの数値を返します。", "ad": "には y = b*m^x となる、既にわかっている y 値の系列 (正の整数の配列または範囲) を指定します。!には y = b*m^x となる、既にわかっている x 値の系列 (既知のy と同じサイズの配列または範囲) を指定します。この値は省略可能です。!には GROWTH 関数を利用して、対応する y の値を計算する新しい x の値を指定します。!には定数 b を 1 にするかどうかを、論理値で指定します。TRUE に指定すると、b の値も計算され、FALSE を指定するか省略すると、b の値が 1 に設定されます。"}, "HARMEAN": {"a": "(数値1; [数値2]; ...)", "d": "正の数からなるデータの調和平均を返します。調和平均は、逆数の算術平均 (相加平均) に対する逆数として定義されます。", "ad": "には調和平均を求めたい数値、または数値を含む名前、配列、セル参照を 1 ～ 255 個まで指定できます。"}, "HYPGEOM.DIST": {"a": "(標本の成功数; 標本数; 母集団の成功数; 母集団の大きさ; 関数形式)", "d": "超幾何分布を返します。", "ad": "には標本内で成功するものの数を指定します。!には標本数を指定します。!には母集団内で成功するものの数を指定します。!には母集団全体の数を指定します。!には論理値を指定します。TRUE を指定した場合は累積分布関数、FALSE を指定した場合は確率密度関数が返されます。"}, "HYPGEOMDIST": {"a": "(標本の成功数; 標本数; 母集団の成功数; 母集団の大きさ)", "d": "超幾何分布を返します。", "ad": "には標本内で成功するものの数を指定します。!には標本数を指定します。!には母集団内で成功するものの数を指定します。!には母集団全体の数を指定します。"}, "INTERCEPT": {"a": "(既知のy; 既知のx)", "d": "既知の x と既知の y を通過する線形回帰直線の切片を計算します。", "ad": "には 1 組の従属変数の値を指定します。引数には、数値、名前、配列、または数値を含むセル参照を指定します。!には 1 組の独立変数の値を指定します。引数には、数値、名前、配列、または数値を含むセル参照を指定します。"}, "KURT": {"a": "(数値1; [数値2]; ...)", "d": "引数として指定したデータの尖度を返します。", "ad": "には尖度を求めたい数値、または数値を含む名前、配列、セル参照を 1 ～ 255 個まで指定できます。"}, "LARGE": {"a": "(配列; 順位)", "d": "データの中から、指定した順位番目に大きな値を返します。", "ad": "には対象となるデータが入力されている配列、またはセル範囲を指定します。!には抽出する値の、大きい方から数えた順位を数値で指定します。"}, "LINEST": {"a": "(既知のy; [既知のx]; [定数]; [補正])", "d": "最小二乗法を使って直線を当てはめることで、既知のデータ ポイントに対応する線形トレンドを表す補正項を計算します。", "ad": "には y = mx + b となる、既にわかっている y 値の系列を指定します。!には y = mx + b となる、既にわかっている x 値の系列を指定します。この値は省略可能です。!には定数 b を 0 にするかどうかを表す論理値を指定します。TRUE に指定するか省略すると、b の値も計算されます。FALSE を指定すると、b の値が 0 に設定されます。!には回帰指数曲線の補正項を追加情報として返すかどうかを論理値で指定します。TRUE を指定すると、回帰指数曲線の補正項が返され、FALSE を指定するか省略すると、m 係数と定数 b のみが返されます。"}, "LOGEST": {"a": "(既知のy; [既知のx]; [定数]; [補正])", "d": "既知のデータ ポイントに対応する指数曲線を表す補正項を計算します。", "ad": "には y = b*m^x となる、既にわかっている y 値の系列を指定します。!には y = b*m^x となる、既にわかっている x 値の系列を指定します。この値は省略可能です。!には定数 b に 1 を指定するかどうかを表す論理値を指定します。TRUE に指定するか省略すると、b の値も計算されます。FALSE を指定すると、b の値が 1 に設定されます。!には回帰指数曲線の補正項を追加情報として返すかどうかを、論理値で指定します。TRUE を指定すると、回帰指数曲線の補正項が返され、FALSE を指定するか省略すると、m 係数と定数 b のみが返されます。"}, "LOGINV": {"a": "(確率; 平均; 標準偏差)", "d": "x の対数正規型の累積分布関数の逆関数の値を返します。ln(x) は平均と標準偏差を引数にする正規型分布になります。", "ad": "には対数正規分布における確率 (0 ～ 1 の数値) を指定します。!には ln(x) の平均値を指定します。!には ln(x) の標準偏差 (正の数値) を指定します。"}, "LOGNORM.DIST": {"a": "(x; 平均; 標準偏差; 関数形式)", "d": "x の対数正規分布の確率を返します。ln(x)は、平均と標準偏差を引数にする正規型分布になります", "ad": "には正の数値の関数に代入する値を指定します!には ln(x) の平均値を指定します!には ln(x) の標準偏差 (正の数値) を指定します!には関数を示す論理値を指定します。TRUE を指定した場合は累積分布関数、FALSE を指定した場合は確率密度関数が返されます"}, "LOGNORM.INV": {"a": "(確率; 平均; 標準偏差)", "d": "x の対数正規型の累積分布関数の逆関数の値を返します。ln(x) は平均と標準偏差を引数にする正規型分布になります。", "ad": "には対数正規分布における確率 (0 ～ 1 の数値) を指定します。!には ln(x) の平均値を指定します。!には ln(x) の標準偏差 (正の数値) を指定します。"}, "LOGNORMDIST": {"a": "(x; 平均; 標準偏差)", "d": "x の対数正規分布の確率を返します。ln(x) は、平均と標準偏差を引数にする正規型分布になります", "ad": "には正の数値の関数に代入する値を指定します!には ln(x) の平均値を指定します!には ln(x) の標準偏差 (正の数値) を指定します"}, "MAX": {"a": "(数値1; [数値2]; ...)", "d": "引数の最大値を返します。論理値および文字列は無視されます。", "ad": "には最大値を求めたい数値、空白セル、論理値、または文字列番号を、1 ～ 255 個まで指定できます"}, "MAXA": {"a": "(値1; [値2]; ...)", "d": "引数の最大値を返します。論理値や文字列も対象となります。", "ad": "には最大値を求めたい数値、空白セル、論理値、または文字列番号を、1 ～ 255 個まで指定できます。"}, "MAXIFS": {"a": "(最大範囲; 条件範囲; 条件; ...)", "d": "所定の条件または基準で指定したセル間の最大値を返します", "ad": "最大値を求めるセル!は特定の条件について値を求めるセルの範囲です!は最大値を求めるときに含めるセルを定義する数値、式、またはテキストの形式の条件または基準です"}, "MEDIAN": {"a": "(数値1; [数値2]; ...)", "d": "引数リストに含まれる数値のメジアン (中央値) を返します。", "ad": "にはメジアンを求めたい数値、または数値を含む名前、配列、セル参照を 1 ～ 255 個まで指定できます。"}, "MIN": {"a": "(数値1; [数値2]; ...)", "d": "引数の最小値を返します。論理値および文字列は無視されます。", "ad": "には最小値を求めたい数値、空白セル、論理値、または文字列番号を、1 ～ 255 個まで指定できます。"}, "MINA": {"a": "(値1; [値2]; ...)", "d": "引数の最小値を返します。論理値や文字列も対象となります。", "ad": "には最小値を求めたい数値、空白セル、論理値、または文字列番号を、1 ～ 255 個まで指定できます。"}, "MINIFS": {"a": "(最小範囲; 条件範囲; 条件; ...)", "d": "所定の条件または基準で指定したセル間の最小値を返します", "ad": "最小値を求めるセル!は特定の条件について値を求めるセルの範囲です!は最小値を求めるときに含めるセルを定義する数値、式、またはテキストの形式の条件または基準です"}, "MODE": {"a": "(数値1; [数値2]; ...)", "d": "配列またはセル範囲として指定されたデータの中で、最も頻繁に出現する値 (最頻値) を返します。", "ad": "には最頻値を求める数値、または数値を含む名前、配列、セル参照を 1 ～ 255 個まで指定できます。"}, "MODE.MULT": {"a": "(数値1; [数値2]; ...)", "d": "最も頻繁に出現する垂直配列、または指定の配列かデータ範囲内で反復的に出現する値を返します。水平配列の場合は、=TRANSPOSE(MODE.MULT(数値1,数値2,...)) を使用します。", "ad": "には、最頻値を求めたい数値、または数値を含む名前、配列、セル参照を 1 ～ 255 個まで指定できます。"}, "MODE.SNGL": {"a": "(数値1; [数値2]; ...)", "d": "配列またはセル範囲として指定されたデータの中で、最も頻繁に出現する値 (最頻値) を返します。", "ad": "には最頻値を求めたい数値、または数値を含む名前、配列、セル参照を 1 ～ 255 個まで指定できます。"}, "NEGBINOM.DIST": {"a": "(失敗数; 成功数; 成功率; 関数形式)", "d": "負の二項分布の確率関数の値を返します。試行の成功率が一定のとき、成功数で指定した回数の試行が成功する前に、失敗数で指定した回数の試行が失敗する確率です。", "ad": "には試行が失敗する回数を指定します。!には分析のしきい値となる、試行が成功する回数を指定します。!には試行が成功する確率を 0 ～ 1 までの数値で指定します。!には関数を示す論理値を指定します。TRUE を指定した場合は累積分布関数、FALSE を指定した場合は確率密度関数が返されます。"}, "NEGBINOMDIST": {"a": "(失敗数; 成功数; 成功率)", "d": "負の二項分布の確率関数の値を返します。試行の成功率が一定のとき、成功数で指定した回数の試行が成功する前に、失敗数で指定した回数の試行が失敗する確率です。", "ad": "には試行が失敗する回数を指定します。!には分析のしきい値となる、試行が成功する回数を指定します。!には試行が成功する確率を 0 ～ 1 までの数値で指定します。"}, "NORM.DIST": {"a": "(x; 平均; 標準偏差; 関数形式)", "d": "指定した平均と標準偏差に対する正規分布の値を返します。", "ad": "には関数に代入する数値を指定します。!には分布の算術平均を指定します。!には分布の標準偏差を正の数値で指定します。!には関数の形式を表す論理値を指定します。TRUE を指定した場合は累積分布関数が返され、FALSE を指定した場合は確率密度関数が返されます。"}, "NORMDIST": {"a": "(x; 平均; 標準偏差; 関数形式)", "d": "指定した平均と標準偏差に対する正規分布関数の値を返します。", "ad": "には関数に代入する数値を指定します。!には分布の算術平均を指定します。!には分布の標準偏差を正の数値で指定します。!には関数の形式を表す論理値を指定します。TRUE を指定した場合は、累積分布関数が返され、FALSE を指定した場合は、確率密度関数が返されます。"}, "NORM.INV": {"a": "(確率; 平均; 標準偏差)", "d": "指定した平均と標準偏差に対する正規分布の累積分布関数の逆関数の値を返します。", "ad": "には正規分布における確率 (0 ～ 1の数値) を指定します。!には対象となる分布の算術平均を指定します。!には対象となる分布の標準偏差 (正の数値) を指定します。"}, "NORMINV": {"a": "(確率; 平均; 標準偏差)", "d": "指定した平均と標準偏差に対する正規分布の累積分布関数の逆関数の値を返します。", "ad": "には正規分布における確率 (0 ～ 1 の数値) を指定します。!には対象となる分布の算術平均を指定します。!には対象となる分布の標準偏差 (正の数値) を指定します。"}, "NORM.S.DIST": {"a": "(z; 関数形式)", "d": "標準正規分布を返します。この分布は、平均が 0 で標準偏差が 1 である正規分布に対応します。", "ad": "には関数に代入する値を指定します。!には計算に使用する関数の形式を表す論理値を指定します。TRUE の場合は累積分布関数、FALSE の場合は確率密度関数が返されます。"}, "NORMSDIST": {"a": "(z)", "d": "標準正規分布の累積分布関数の値を返します。この分布は、平均が 0 で標準偏差が 1 である正規分布に対応します。", "ad": "には関数に代入する値を指定します。"}, "NORM.S.INV": {"a": "(確率)", "d": "標準正規分布の累積分布関数の逆関数の値を返します。この分布は、平均が 0 で標準偏差が 1 である正規分布に対応します。", "ad": "には正規分布における確率 (0 ～ 1 の数値) を指定します。"}, "NORMSINV": {"a": "(確率)", "d": "標準正規分布の累積分布関数の逆関数の値を返します。この分布は、平均が 0 で標準偏差が 1 である正規分布に対応します。", "ad": "には正規分布における確率 (0 ～ 1 の数値) を指定します。"}, "PEARSON": {"a": "(配列1; 配列2)", "d": "ピアソンの積率相関係数 r の値を返します。", "ad": "には複数の独立変数の値を指定します。!には複数の従属変数の値を指定します。"}, "PERCENTILE": {"a": "(配列; 率)", "d": "配列に含まれる値の k 番目の百分位を返します。", "ad": "には相対的な位置を決定する数値データが含まれる配列、または範囲を指定します。!には百分位の値を 0 ～ 1 で指定します。"}, "PERCENTILE.EXC": {"a": "(配列; 率)", "d": "配列に含まれる値の k 番目の百分位を返します。k には、0 より大きく 1 より小さい値を指定します。", "ad": "には相対的な位置を決定する数値データが含まれる配列、または範囲を指定します。!には百分位の値を、0 以上 1 以下の範囲で指定します。"}, "PERCENTILE.INC": {"a": "(配列; 率)", "d": "配列に含まれる値の k 番目の百分位を返します。k には、0 以上 1 以下の値を指定します。", "ad": "には相対的な位置を決定する数値データが含まれる配列、または範囲を指定します。!には百分位の値を、0 以上 1 以下の範囲で指定します。"}, "PERCENTRANK": {"a": "(配列; x; [有効桁数])", "d": "値 x の配列内での順位を百分率で表した値を返します。", "ad": "には相対的な位置を決定する数値データが含まれる配列、または範囲を指定します。!には相対的な順位を調べる値を指定します。!には結果として返される百分率の有効桁数を指定します。省略すると、小数点以下第三位 (0.xxx%) まで計算されます。"}, "PERCENTRANK.EXC": {"a": "(配列; x; [有効桁数])", "d": "値 x の配列内での順位を百分率 (0 より大きく 1 より小さい) で表した値を返します。", "ad": "には相対的な位置を決定する数値データが含まれる配列、または範囲を指定します!には相対的な順位を調べる値を指定します!には結果として返される百分率の有効桁数を指定し、省略すると、小数点以下第三位 (0.xxx%) まで計算されます"}, "PERCENTRANK.INC": {"a": "(配列; x; [有効桁数])", "d": "値 x の配列内での順位を百分率 (0 以上 1 以下) で表した値を返します。", "ad": "には相対的な位置を決定する数値データが含まれる配列、または範囲を指定します!には相対的な順位を調べる値を指定します。!には結果として返される百分率の有効桁数を指定し、省略すると、小数点以下第三位 (0.xxx%) まで計算されます"}, "PERMUT": {"a": "(標本数; 抜き取り数)", "d": "指定した数の対象から、指定された数だけ抜き取る場合の順列の数を返します。", "ad": "には対象の数を整数で指定します。!には順列 1 つに含まれる対象の数を整数で指定します。"}, "PERMUTATIONA": {"a": "(数値; 抜き取り数)", "d": "指定した数の対象 (反復あり) から、指定された数だけ抜き取る場合の順列の数を返します。", "ad": "には対象の数を整数で指定します。!には順列 1 つに含まれる対象の数を整数で指定します。"}, "PHI": {"a": "(x)", "d": "標準正規分布の密度関数の値を返します。", "ad": "には標準正規分布の密度を求める数値を指定します。"}, "POISSON": {"a": "(イベント数; 平均; 関数形式)", "d": "ポワソン分布の値を返します。", "ad": "にはイベント数を指定します。!には一定の時間内に起こるイベント数の平均値を正の数値で指定します。!には返される確率分布の形式を表す論理値を指定します。TRUE を指定した場合は、累積ポワソン確率が計算され、FALSE を指定した場合は、ポワソン確率が計算されます。"}, "POISSON.DIST": {"a": "(イベント数; 平均; 関数形式)", "d": "ポワソン分布の値を返します。", "ad": "にはイベント数を指定します。!には一定の時間内に起こるイベント数の平均値を正の数値で指定します。!には返される確率分布の形式を表す論理値を指定します。TRUE を指定した場合は累積ポワソン確率が計算され、FALSE を指定した場合はポワソン確率が計算されます。"}, "PROB": {"a": "(x範囲; 確率範囲; 下限; [上限])", "d": "指定した範囲内の値が、上限と下限で指定される範囲に含まれる確率を返します。", "ad": "には確率範囲と対応関係にある数値 x 含む配列またはセル範囲を指定します。!には x 範囲に含まれるそれぞれの数値に対応する確率 (0 ～ 1 の数値) を指定します。!には対象となる数値の下限値を指定します。!には対象となる数値の上限値を指定します (省略可能)。省略すると、x 範囲に含まれる数値が下限の値に等しくなる確率が計算されます。"}, "QUARTILE": {"a": "(配列; 戻り値)", "d": "配列に含まれるデータから四分位数を返します。", "ad": "には対象となる数値データを含む配列、またはセル範囲を指定します。!には戻り値を表す数値 (最小値 = 0、25% = 1、50% = 2、75% = 3、最大値 = 4) を指定します。"}, "QUARTILE.INC": {"a": "(配列; 戻り値)", "d": "0 以上 1 以下の百分位値に基づいて、配列に含まれるデータから四分位数を返します。", "ad": "には対象となる数値データを含む配列、またはセル範囲を指定します。!には戻り値を表す数値 (最小値 = 0、25% = 1、50% = 2、75% = 3、最大値 = 4) を指定します。"}, "QUARTILE.EXC": {"a": "(配列; 戻り値)", "d": "0 より大きく 1 より小さい百分位値に基づいて、配列に含まれるデータから四分位数を返します。", "ad": "には対象となる数値データを含む配列、またはセル範囲を指定します。!には戻り値を表す数値 (最小値 = 0、25% = 1、50% = 2、75% = 3、最大値 = 4) を指定します。"}, "RANK": {"a": "(数値; 参照; [順序])", "d": "順序に従って範囲内の数値を並べ替えたとき、数値が何番目に位置するかを返します。", "ad": "には順位を調べる数値を指定します。!には数値を含むセル範囲の参照、または配列を指定します。数値以外の値は無視されます。!には範囲内の数値を並べ替える方法を表す数値を指定します。順序に 0 を指定するか省略すると、降順で並べ替えられ、0 以外の数値を指定すると、昇順で並べ替えられます。"}, "RANK.AVG": {"a": "(数値; 参照; [順序])", "d": "順序に従って範囲内の数値を並べ替えたとき、数値が何番目に位置するかを返します。複数の数値が同じ順位にある場合は、順位の平均を返します。", "ad": "には順位を調べる数値を指定します。!には数値を含むセル範囲の参照、または配列を指定します。数値以外の値は無視されます。!には範囲内の数値を並べ替える方法を表す数値を指定します。順序に 0 を指定するか省略すると、降順で並べ替えられ、0 以外の数値を指定すると、昇順で並べ替えられます。"}, "RANK.EQ": {"a": "(数値; 参照; [順序])", "d": "順序に従って範囲内の数値を並べ替えたとき、数値が何番目に位置するかを返します。複数の数値が同じ順位にある場合は、その値の中の最上位を返します。", "ad": "には順位を調べる数値を指定します。!には数値を含むセル範囲の参照、または配列を指定します。数値以外の値は無視されます。!には範囲内の数値を並べ替える方法を表す数値を指定します。順序に 0 を指定するか省略すると、降順で並べ替えられ、0 以外の数値を指定すると、昇順で並べ替えられます。"}, "RSQ": {"a": "(既知のy; 既知のx)", "d": "指定されたデータ ポイントからピアソンの積率相関係数の 2 乗を返します。", "ad": "にはデータ ポイントの配列、または範囲を指定します。引数には、数値、名前、配列、または数値を含むセル参照を指定します。!にはデータ ポイントの配列、または範囲を指定します。引数には、数値、名前、配列、または数値を含むセル参照を指定します。"}, "SKEW": {"a": "(数値1; [数値2]; ...)", "d": "分布の歪度 (ひずみ) を返します。歪度とは、分布の平均値周辺での両側の非対称度を表す値です。", "ad": "には歪度を求めたい数値、または数値を含む名前、配列、セル参照を 1 ～ 255 個まで指定できます。"}, "SKEW.P": {"a": "(数値1; [数値2]; ...)", "d": "人口に基づく分布の歪度 (ひずみ) を返します。歪度とは、分布の平均値周辺での両側の非対称度を表す値です。", "ad": "には歪度を求めたい数値、または数値を含む名前、配列、セル参照を 1 ～ 255 個まで指定します。"}, "SLOPE": {"a": "(既知のy; 既知のx)", "d": "指定されたデータ ポイントから線形回帰直線の傾きを返します。", "ad": "には従属変数の値を含む数値配列、またはセル範囲を指定します。引数には、数値、名前、配列、または数値を含むセル参照を指定します。!には独立変数の値を含む数値配列、あるいはセル範囲を指定します。引数には、数値、名前、配列、または数値を含むセル参照を指定します。"}, "SMALL": {"a": "(配列; 順位)", "d": "データの中から、指定した順位番目に小さな値を返します。", "ad": "には対象となるデータが入力されている配列、またはセル範囲を指定します。!には抽出する値の小さい方から数えた順位を数値で指定します。"}, "STANDARDIZE": {"a": "(x; 平均; 標準偏差)", "d": "平均と標準偏差で決定される分布を対象に、正規化された値を返します。", "ad": "には正規化する値を指定します。!には分布の算術平均を指定します。!には分布の標準偏差を正の数値で指定します。"}, "STDEV": {"a": "(数値1; [数値2]; ...)", "d": "標本に基づいて予測した標準偏差を返します。標本内の論理値と文字列は無視されます。", "ad": "には母集団の標本に対応する数値、または数値を含む参照を 1 ～ 255 個まで指定できます。"}, "STDEV.P": {"a": "(数値1; [数値2]; ...)", "d": "引数を母集団全体であると見なして、母集団の標準偏差を返します。論理値、および文字列は無視されます。", "ad": "には母集団に対応する数値、または数値を含む参照を、1 ～ 255 個まで指定できます。"}, "STDEV.S": {"a": "(数値1; [数値2]; ...)", "d": "標本に基づいて予測した標準偏差を返します。標本内の論理値、および文字列は無視されます。", "ad": "には母集団の標本に対応する数値、または数値を含む参照を、1 ～ 255 個まで指定できます。"}, "STDEVA": {"a": "(値1; [値2]; ...)", "d": "論理値や文字列を含む標本に基づいて、予測した標準偏差を返します。文字列および論理値 FALSE は値 0、論理値 TRUE は 1 と見なされます。", "ad": "には母集団の標本に対応する値、名前、または値への参照を、1 ～ 255 個まで指定できます。"}, "STDEVP": {"a": "(数値1; [数値2]; ...)", "d": "引数を母集団全体であると見なして、母集団の標準偏差を返します。論理値、および文字列は無視されます。", "ad": "には母集団に対応する数値、または数値を含む参照を 1 ～ 255 個まで指定できます。"}, "STDEVPA": {"a": "(値1; [値2]; ...)", "d": "論理値や文字列を含む引数を母集団全体と見なして、母集団の標準偏差を返します。文字列および論理値 FALSE は値 0、論理値 TRUE は値 1 と見なされます。", "ad": "には母集団に対応する値、または値を含む名前、配列、参照を、1 ～ 255 個まで指定できます。"}, "STEYX": {"a": "(既知のy; 既知のx)", "d": "回帰において、x に対して予測された値 y の標準誤差を返します。", "ad": "には従属変数の値を含む数値配列またはセル範囲を指定します。引数には、数値、名前、配列、または数値を含むセル参照を指定します。!には独立変数の値を含む数値配列またはセル範囲を指定します。引数には、数値、名前、配列、または数値を含むセル参照を指定します。"}, "TDIST": {"a": "(x; 自由度; 分布の指定)", "d": "スチューデントの t-分布を返します", "ad": "には分布を計算する数値を指定します!には自由度を表す整数値を指定します!には片側確率を求める場合は 1、両側確率を求める場合は 2 を指定します"}, "TINV": {"a": "(確率; 自由度)", "d": "スチューデントの t-分布の両側逆関数を返します。", "ad": "にはスチューデントの t-分布の両側確率を 0 ～ 1 の数値で 指定します。!には分布の自由度を示す正の整数を指定します。"}, "T.DIST": {"a": "(x; 自由度; 関数形式)", "d": "左側のスチューデントの t-分布を返します", "ad": "には分布を計算する数値を指定します!には自由度を表す整数値を指定します!には関数の形式を表す論理値を指定します。TRUE を指定した場合は累積分布関数が返され、FALSE を指定した場合は確率密度関数が返されます"}, "T.DIST.2T": {"a": "(x; 自由度)", "d": "両側のスチューデントの t-分布を返します", "ad": "には分布を計算する数値を指定します!には自由度を表す整数値を指定します"}, "T.DIST.RT": {"a": "(x; 自由度)", "d": "右側のスチューデントの t-分布を返します", "ad": "には分布を計算する数値を指定します!には自由度を表す整数値を指定します"}, "T.INV": {"a": "(確率; 自由度)", "d": "スチューデントの t-分布の左側逆関数を返します。", "ad": "にはスチューデントの t-分布の両側確率を 0 ～ 1 の数値で 指定します。!には分布の自由度を示す正の整数を指定します。"}, "T.INV.2T": {"a": "(確率; 自由度)", "d": "スチューデントの t-分布の両側逆関数を返します。", "ad": "にはスチューデントの t-分布の両側確率を 0 ～ 1 の数値で 指定します。!には分布の自由度を示す正の整数を指定します。"}, "T.TEST": {"a": "(配列1; 配列2; 検定の指定; 検定の種類)", "d": "スチューデントの t 検定に関連する確率を返します。", "ad": "には一方のデータ配列を指定します。!にはもう一方のデータ配列を指定します。!には片側検定の場合は 1、両側検定の場合は 2 を指定します。!には実行する t 検定の種類を指定します。対応のある検定の場合は 1、2 標本の等分散が仮定できる場合は 2、2 標本が非等分散の場合は 3 を指定します。"}, "TREND": {"a": "(既知のy; [既知のx]; [新しいx]; [定数])", "d": "最小二乗法を使用することで、既知のデータ ポイントに対応する線形トレンドの数値を返します。", "ad": "には y = mx + b となる、既にわかっている y 値の範囲または配列を指定します。!には y = mx + b となる、既にわかっている x 値の範囲または配列 (既知のy と同じサイズの配列) を指定します。この値は省略可能です。!には TREND 関数を利用して、対応する y の値を計算する新しい x の値の範囲または配列を指定します。!には定数 b を 0 にするかどうかを論理値で指定します。TRUE に指定するか省略すると、b の値も計算され、FALSE を指定すると、b の値が 0 に設定されます。"}, "TRIMMEAN": {"a": "(配列; 割合)", "d": "データ全体の上限と下限から一定の割合のデータを切り落とし、残りの項の平均値を返します。", "ad": "には関数の対象となる数値を含む配列、または範囲を指定します。!には平均値の計算から排除するデータの割合を小数で指定します。"}, "TTEST": {"a": "(配列1; 配列2; 検定の指定; 検定の種類)", "d": "スチューデントの t 検定に関連する確率を返します。", "ad": "には一方のデータ配列を指定します。!にはもう一方のデータ配列を指定します。!には片側検定の場合は 1、両側検定の場合は 2 を指定します。!には実行する t 検定の種類を指定します。対応のある検定の場合は 1、2 標本の等分散が仮定できる場合は 2、2 標本が非等分散の場合は 3 を指定します。"}, "VAR": {"a": "(数値1; [数値2]; ...)", "d": "標本に基づいて母集団の分散の推定値 (不偏分散) を返します。標本内の論理値と文字列は無視されます。", "ad": "には母集団の標本に対応する数値を 1 ～ 255 個まで指定できます。"}, "VAR.P": {"a": "(数値1; [数値2]; ...)", "d": "引数を母集団全体と見なし、母集団の分散 (標本分散) を返します。論理値、および文字列は無視されます。", "ad": "には母集団に対応する数値を 1 ～ 255 個まで指定できます。"}, "VAR.S": {"a": "(数値1; [数値2]; ...)", "d": "標本に基づいて母集団の分散の推定値 (不偏分散) を返します。標本内の論理値、および文字列は無視されます。", "ad": "には母集団の標本に対応する数値を 1 ～ 255 個まで指定できます。"}, "VARA": {"a": "(値1; [値2]; ...)", "d": "標本に基づく、分散の予測値を返します。文字列および論理値 FALSE は値 0、論理値 TRUE は値 1 と見なされます。", "ad": "には母集団の標本に対応する値を 1 から 255 個まで指定できます。"}, "VARP": {"a": "(数値1; [数値2]; ...)", "d": "引数を母集団全体と見なし、母集団の分散 (標本分散) を返します。論理値、および文字列は無視されます。", "ad": "には母集団に対応する数値を 1 ～ 255 個まで指定できます。"}, "VARPA": {"a": "(値1; [値2]; ...)", "d": "母集団全体に基づく分散を返します。文字列および論理値 FALSE は値 0、論理値 TRUE は 1 と見なされます。", "ad": "には母集団に対応する値を 1 から 255 個まで指定できます。"}, "WEIBULL": {"a": "(x; α; β; 関数形式)", "d": "ワイブル分布の値を返します", "ad": "には関数に代入する値を負でない数値で指定します!には確率分布のパラメーターを正の数値で指定します!には確率分布のパラメーターを正の数値で指定します!には論理値を指定します。TRUE を指定した場合は、累積分布関数が返され、FALSE を指定した場合は、確率量関数が返されます"}, "WEIBULL.DIST": {"a": "(x; α; β; 関数形式)", "d": "ワイブル分布の値を返します", "ad": "には関数に代入する値を負でない数値で指定します!には確率分布のパラメーターを正の数値で指定します!には確率分布のパラメーターを正の数値で指定します!には関数を示す論理値を指定します。TRUE を指定した場合は累積分布関数が返され、FALSE を指定した場合は確率量関数が返されます"}, "Z.TEST": {"a": "(配列; x; [σ])", "d": "z 検定の片側確率の P 値を返します。", "ad": "には x の検定対象となる配列またはデータ範囲を指定します。!には検定する値を指定します。!には母集団全体に基づく標準偏差を指定します。省略すると、標本に基づく標準偏差が使用されます。"}, "ZTEST": {"a": "(配列; x; [σ])", "d": "z 検定の片側確率の P 値を返します。", "ad": "には x の検定対象となる配列またはデータ範囲を指定します。!には検定する値を指定します。!には母集団全体に基づく標準偏差を指定します。省略すると、標本に基づく標準偏差が使用されます。"}, "ACCRINT": {"a": "(発行日; 最初の利払日; 受渡日; 利率; 額面; 頻度; [基準]; [計算方式])", "d": "利息が定期的に支払われる有価証券に対する未収利息を計算します。", "ad": "には、証券の発行日を日付のシリアル値で指定します。!には、証券の初回利払日を日付のシリアル値で指定します。!には、証券の受渡日を日付のシリアル値で指定します。!には、証券の年利を指定します。!には、証券の額面価格を指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。!には、未収利息を発行日から計算するか (TRUE)、最後の利払日から計算するか (FALSE) を、論理値で指定します。"}, "ACCRINTM": {"a": "(発行日; 受渡日; 利率; 額面; [基準])", "d": "利息が満期日に支払われる有価証券に対する未収利息を計算します。", "ad": "には、証券の発行日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券の年利を指定します。!には、証券の額面価格を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "AMORDEGRC": {"a": "(取得価額; 購入日; 開始期; 残存価額; 期; 率; [年の基準])", "d": "各会計期における減価償却費を返します", "ad": "には、資産を購入した時点での価格を指定します!には、資産を購入した日付を指定します!には、最初の会計期が終了する日付を指定します!には、耐用年数が終了した時点での資産の価格を指定します!には、会計期 (会計年度) を指定します!には、減価償却率を指定します!には、1 年を何日として計算するかを数値 (0 = 360 日、1 = 実際の日数、3 = 365 日) で指定します"}, "AMORLINC": {"a": "(取得価額; 購入日; 開始期; 残存価額; 期; 率; [年の基準])", "d": "各会計期における減価償却費を返します", "ad": "には、資産を購入した時点での価格を指定します!には、資産を購入した日付を指定します!には、最初の会計期が終了する日付を指定します!には、耐用年数が終了した時点での資産の価格を指定します!には、会計期 (会計年度) を指定します!には、減価償却率を指定します!には、1 年を何日として計算するかを数値 (0 = 360 日、1 = 実際の日数、3 = 365 日) で指定します"}, "COUPDAYBS": {"a": "(受渡日; 満期日; 頻度; [基準])", "d": "利払期間の第 1 日目から受渡日までの日数を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "COUPDAYS": {"a": "(受渡日; 満期日; 頻度; [基準])", "d": "受渡日を含む利払期間の日数を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "COUPDAYSNC": {"a": "(受渡日; 満期日; 頻度; [基準])", "d": "受渡日から次の利払日までの日数を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "COUPNCD": {"a": "(受渡日; 満期日; 頻度; [基準])", "d": "受渡日後の次の利払日を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "COUPNUM": {"a": "(受渡日; 満期日; 頻度; [基準])", "d": "受渡日と満期日の間の利息支払回数を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "COUPPCD": {"a": "(受渡日; 満期日; 頻度; [基準])", "d": "受渡日の前の最後の利払日を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "CUMIPMT": {"a": "(利率; 期間; 現在価値; 開始期; 終了期; 支払期日)", "d": "開始から終了までの貸付期間内で支払われる利息の累計額を計算します", "ad": "には、利率を指定します!には、支払期間の合計を指定します!には、現在の貸付け額を指定します!には、計算の対象となる最初の期を指定します!には、計算の対象となる最後の期を指定します!には、支払時期の指定をします"}, "CUMPRINC": {"a": "(利率; 期間; 現在価値; 開始期; 終了期; 支払期日)", "d": "開始から終了までの貸付期間内で支払われる元金の累計額を計算します", "ad": "には、利率の指定をします!には、支払期間の合計を指定します!には、現在の貸付け額を指定します!には、計算の対象となる最初の期を指定します!には、計算の対象となる最後の期を指定します!には、支払時期の指定をします"}, "DB": {"a": "(取得価額; 残存価額; 耐用年数; 期; [月])", "d": "定率法を使って計算した資産の減価償却を返します。", "ad": "には資産を購入した時点での価格を指定します。!には耐用年数を経た後での資産の価格を指定します。!には資産を使用できる年数、つまり償却の対象となる資産の寿命年数を指定します。!には減価償却費を計算したい期間を、<耐用年数> と同じ単位で指定します。!には初年度の月数を指定します。省略した場合、12 が指定されたものと見なされます。"}, "DDB": {"a": "(取得価額; 残存価額; 耐用年数; 期; [率])", "d": "倍率逓減法または指定したその他の方式を使って、計算した資産の減価償却を返します。", "ad": "には資産を購入した時点での価格を指定します。!には耐用年数を経た後での資産の価格を指定します。!には資産を使用できる年数、つまり償却の対象となる資産の寿命年数を指定します。!には減価償却費を計算したい期間を、<耐用年数> と同じ単位で指定します。!には減価償却費を計算するために使用する償却率を指定します。省略した場合、2 が指定されたものと見なされ、倍率逓減法で計算されます。"}, "DISC": {"a": "(受渡日; 満期日; 現在価値; 償還価額; [基準])", "d": "証券に対する割引率を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、額面価格 $100 に対する証券の価格を指定します。!には、額面価格 $100 に対する証券の償還価額を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "DOLLARDE": {"a": "(整数部と分子部; 分母)", "d": "分数として表現されているドル単位の価格を、10 進数を使った数値に変換します。", "ad": "には、分数として表現されている数値を指定します。!には、分数の分母となる整数を指定します。"}, "DOLLARFR": {"a": "(小数値; 分母)", "d": "10 進数として表現されているドル単位の価格を、分数を使った数値に変換します。", "ad": "には、10 進数として表現された数値を指定します。!には、分数の分母となる整数を指定します。"}, "DURATION": {"a": "(受渡日; 満期日; 利率; 利回り; 頻度; [基準])", "d": "定期的に利子が支払われる証券の年間のマコーレー係数を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券の年利を指定します。!には、証券の年間の利回りを指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "EFFECT": {"a": "(名目利率; 複利計算期間)", "d": "実質金利の計算をします。", "ad": "名目上の年利を指定します。!1 年当たりの複利利息の支払回数を指定します。"}, "FV": {"a": "(利率; 期間; 定期支払額; [現在価値]; [支払期日])", "d": "一定利率の支払いが定期的に行われる場合の、投資の将来価値を返します", "ad": "には 1 期間あたりの利率を指定します。たとえば、年率 6% のローンを四半期払いで返済する場合、利率には 6%/4 を指定します!には投資期間全体での支払回数の合計を指定します!には 1 期間あたりの支払額を指定します。投資期間内に支払額を変更することはできません!には投資の将来価値、つまり最後の支払いを行った後に残る現金の収支を指定します。将来価値を省略すると、0 を指定したと見なされます!には支払いがいつ行われるかを表す論理値 (期首払い = 1、期末払い = 0 または省略) を指定します"}, "FVSCHEDULE": {"a": "(元金; 利率配列)", "d": "投資期間内の一連の金利を複利計算することにより、初期投資の元金の将来価値を計算します。", "ad": "投資の現在価値を指定します。!投資期間内の金利変動を配列として指定します。"}, "INTRATE": {"a": "(受渡日; 満期日; 投資額; 償還価額; [基準])", "d": "全額投資された証券を対象に、その利率を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券への投資額を指定します。!には、満期日における証券の償還価額を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "IPMT": {"a": "(利率; 期; 期間; 現在価値; [将来価値]; [支払期日])", "d": "一定利率の支払いが定期的に行われる場合の、投資期間内の指定された期に支払われる金利を返します", "ad": "には 1 期間あたりの利率を指定します。たとえば、年率 6% のローンを四半期払いで返済する場合、利率には 6%/4 を指定します!には金利支払額を求めたい投資期間内の特定の期を、1 から <期間> の範囲内で指定します!には投資期間全体での支払回数の合計を指定します!には投資の現在価値、つまり、将来行われる一連の支払いを現時点で一括払いした場合の合計金額を指定します!には投資の将来価値、つまり最後の支払いを行った後に残る現金の収支を指定します。将来価値を省略すると、0 を指定したと見なされます!には支払いがいつ行われるかを表す論理値 (期首払い = 1、期末払い = 0 または省略) を指定します"}, "IRR": {"a": "(範囲; [推定値])", "d": "一連の定期的なキャッシュ フローに対する内部収益率を返します。", "ad": "には、内部利益率を計算するための数値が入力されている配列またはセル参照を指定します。!には、IRR 関数が計算する内部利益率に近いと推定される数値を指定します。"}, "ISPMT": {"a": "(利率; 期; 期間; 現在価値)", "d": "投資期間内の指定された期に支払われる金利を返します", "ad": "には 1 期間あたりの利率を指定します。たとえば、年率 6% のローンを四半期払いで返済する場合、利率には 6%/4 を指定します!には金利支払額を求めたい投資期間内の特定の期を指定します!投資期間全体での支払回数の合計を指定します!には将来行われる一連の支払いを現時点で一括払いした場合の合計金額を指定します"}, "MDURATION": {"a": "(受渡日; 満期日; 利率; 利回り; 頻度; [基準])", "d": "額面価格を $100 と仮定して、証券に対する修正マコーレー係数を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券の年利を指定します。!には、証券の年間の利回りを指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "MIRR": {"a": "(範囲; 安全利率; 危険利率)", "d": "投資原価と現金の再投資に対する受取利率 (危険利率) の両方を考慮して、一連の定期的なキャッシュ フローに対する内部収益率を返します", "ad": "には数値を含む配列またはセル参照を指定します。これらの数値は、定期的に発生する一連の支払い (負の値) と収益 (正の値) に対応します!には投資額 (負のキャッシュ フロー) に対する利率を指定します!には収益額 (正のキャッシュ フロー) に対する利率を指定します"}, "NOMINAL": {"a": "(実効利率; 複利計算期間)", "d": "預金などの名目上の年利を計算します。", "ad": "実質年利を指定します。!1 年当たりの複利利息の支払回数を指定します。"}, "NPER": {"a": "(利率; 定期支払額; 現在価値; [将来価値]; [支払期日])", "d": "一定利率の支払いが定期的に行われる場合の、ローンの支払回数を返します", "ad": "には 1 期間あたりの利率を指定します。たとえば、年率 6% のローンを四半期払いで返済する場合、利率には 6%/4 を指定します!には毎回の支払額を指定します。投資期間内に支払額を変更することはできません!には投資の現在価値、つまり、将来行われる一連の支払いを現時点で一括払いした場合の合計金額を指定します!には投資の将来価値、つまり最後の支払いを行った後に残る現金の収支を指定します。将来価値を省略すると、0 を指定したと見なされます!には支払いがいつ行われるかを表す論理値 (期首払い = 1、期末払い = 0 または省略) を指定します"}, "NPV": {"a": "(割引率; 値1; [値2]; ...)", "d": "投資の正味現在価値を、割引率、将来行われる一連の支払い (負の値)、およびその収益 (正の値) を使って算出します", "ad": "には投資期間を通じて一定の割引率を指定します!には支払額と収益額を表す 1 ～ 254 個の引数を指定します"}, "ODDFPRICE": {"a": "(受渡日; 満期日; 発行日; 初回利払日; 利率; 利回り; 償還価額; 頻度; [基準])", "d": "投資期間の第 1 期が半端な日数のとき、対象となる証券の額面 $100 に対する価格を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券の発行日を日付のシリアル値で指定します。!には、証券の最初の利払日を日付のシリアル値で指定します。!には、証券の利率を指定します。!には、証券の年間の利回りを指定します。!には、額面 $100 に対する証券の償還価額を指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "ODDFYIELD": {"a": "(受渡日; 満期日; 発行日; 初回利払日; 利率; 現在価値; 償還価額; 頻度; [基準])", "d": "投資期間の第 1 期が半端な日数のとき、対象となる証券の利回りを計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券の発行日を日付のシリアル値で指定します。!には、証券の最初の利払日を日付のシリアル値で指定します。!には、証券の利率を指定します。!には、証券の価格を指定します。!には、額面 $100 に対する証券の償還価額を指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "ODDLPRICE": {"a": "(受渡日; 満期日; 最終利払日; 利率; 利回り; 償還価額; 頻度; [基準])", "d": "投資期間の最終期が半端な日数のとき、対象となる証券の額面 $100 に対する価格を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券の最後の利払日を日付のシリアル値で指定します。!には、証券の利率を指定します。!には、証券の年間の利回りを指定します。!には、額面 $100 に対する証券の償還価額を指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "ODDLYIELD": {"a": "(受渡日; 満期日; 最終利払日; 利率; 現在価値; 償還価額; 頻度; [基準])", "d": "投資期間の最終期が半端な日数のとき、対象となる証券の利回りを計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券の最後の利払日を日付のシリアル値で指定します。!には、証券の利率を指定します。!には、証券の価格を指定します。!には、額面 $100 に対する証券の償還価額を指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "PDURATION": {"a": "(利率; 現在価値; 将来価値)", "d": "投資が指定した価値に達するまでの投資期間を返します。", "ad": "には投資期間内の利率を示します。!には投資の現在価値を指定します。!には投資の将来価値を指定します。"}, "PMT": {"a": "(利率; 期間; 現在価値; [将来価値]; [支払期日])", "d": "一定利率の支払いが定期的に行われる場合の、ローンの定期支払額を算出します", "ad": "にはローンの 1 期間あたりの利率を指定します。たとえば、年率 6% のローンを四半期払いで返済する場合、利率には 6%/4 を指定します!にはローン期間全体での支払回数の合計を指定します!には投資の現在価値、つまり、将来行われる一連の支払いを現時点で一括払いした場合の合計金額、または元金を指定します!には投資の将来価値、つまり最後の支払いを行った後に残る現金の収支を指定します。将来価値を省略すると、0 を指定したと見なされます!には支払いがいつ行われるかを表す論理値 (期首払い = 1、期末払い = 0 または省略) を指定します"}, "PPMT": {"a": "(利率; 期; 期間; 現在価値; [将来価値]; [支払期日])", "d": "一定利率の支払いが定期的に行われる場合の、投資の指定した期に支払われる元金を返します", "ad": "には 1 期間あたりの利率を指定します。たとえば、年率 6% のローンを四半期払いで返済する場合、利率には 6%/4 を指定します!には元金支払額を求めたい投資期間内の特定の期を、1 から <期間> の範囲内で指定します!には投資期間全体での支払回数の合計を指定します!には投資の現在価値、つまり、将来行われる一連の支払いを現時点で一括払いした場合の合計金額を指定します!には投資の将来価値、つまり最後の支払いを行った後に残る現金の収支を指定します。将来価値を省略すると、0 を指定したと見なされます!には支払いがいつ行われるかを表す論理値 (期首払い = 1、期末払い = 0 または省略) を指定します"}, "PRICE": {"a": "(受渡日; 満期日; 利率; 利回り; 償還価額; 頻度; [基準])", "d": "定期的に利息が支払われる証券の額面 $100 に対する価格を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券の利率を指定します。!には、証券の年間の利回りを指定します。!には、額面 $100 に対する証券の償還価額を指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "PRICEDISC": {"a": "(受渡日; 満期日; 割引率; 償還価額; [基準])", "d": "割引証券の額面 $100 に対する価格を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券の割引率を指定します。!には、額面 $100 に対する証券の償還価額を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "PRICEMAT": {"a": "(受渡日; 満期日; 発行日; 利率; 利回り; [基準])", "d": "受渡日に利息が支払われる証券の額面 $100 に対する価格を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券の発行日を日付のシリアル値で指定します。!には、発行時点での証券の利率を指定します。!には、証券の年間の利回りを指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "PV": {"a": "(利率; 期間; 定期支払額; [将来価値]; [支払期日])", "d": "投資の現在価値を返します。現在価値とは、将来行われる一連の支払いを、現時点で一括払いした場合の合計金額のことをいいます", "ad": "には 1 期間あたりの利率を指定します。たとえば、年率 6% のローンを四半期払いで返済する場合、利率には 6%/4 を指定します!には投資期間全体での支払回数の合計を指定します!には 1 期間あたりの支払額を指定します。投資期間内に支払額を変更することはできません!には投資の将来価値、つまり最後の支払いを行った後に残る現金の収支を指定します!には支払いがいつ行われるかを表す論理値 (期首払い = 1、期末払い = 0 または省略) を指定します"}, "RATE": {"a": "(期間; 定期支払額; 現在価値; [将来価値]; [支払期日]; [推定値])", "d": "ローンまたは投資の 1 期間あたりの利率を指定します。たとえば、年率 6% のローンを四半期払いで返済する場合、利率には 6%/4 = 1.5 (%) を指定します", "ad": "にはローンまたは投資期間全体での支払回数の合計を指定します!には毎回の支払額を指定します。ローンまたは投資期間内に支払額を変更することはできません!には投資の現在価値、つまり、将来行われる一連の支払いを現時点で一括払いした場合の合計金額、または元金を指定します!には投資の将来価値、つまり最後の支払いを行った後に残る現金の収支を指定します。将来価値を省略すると、0 を指定したと見なされます!には支払いがいつ行われるかを表す論理値 (期首払い = 1、期末払い = 0 または省略) を指定します!には利率がおよそどれくらいになるかを推定した値を指定します。推定値を省略すると、0.1 (10%) が計算に使用されます"}, "RECEIVED": {"a": "(受渡日; 満期日; 投資額; 割引率; [基準])", "d": "全額投資された証券を対象に、満期日における償還価額を計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券への投資額を指定します。!には、証券の割引率を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "RRI": {"a": "(期間; 現在価値; 将来価値)", "d": "投資の成長に対する等価利率を返します。", "ad": "には投資の期間を指定します。!には投資の現在価値を指定します。!には投資の将来価値を指定します。"}, "SLN": {"a": "(取得価額; 残存価額; 耐用年数)", "d": "資産に対する減価償却を定額法を使って計算し、その結果を返します。", "ad": "には資産を購入した時点での価格を指定します。!には耐用年数を経た後での資産の価格を指定します。!には資産を使用できる年数 (償却の対象となる資産の寿命年数) を指定します。"}, "SYD": {"a": "(取得価額; 残存価額; 耐用年数; 期)", "d": "資産に対する減価償却を級数法を使って計算し、その結果を返します。", "ad": "には資産を購入した時点での価格を指定します。!には耐用年数を経た後での資産の価格を指定します。!には資産を使用できる年数、つまり償却の対象となる資産の寿命年数を指定します。!には減価償却費を計算する期を指定します。"}, "TBILLEQ": {"a": "(受渡日; 満期日; 割引率)", "d": "米国財務省短期証券 (TB) の債券相当の利回りを計算します。", "ad": "には、財務省短期証券の受渡日を日付のシリアル値で指定します。!には、財務省短期証券の満期日を日付のシリアル値で指定します。!には、財務省短期証券の割引率を指定します。"}, "TBILLPRICE": {"a": "(受渡日; 満期日; 割引率)", "d": "米国財務省短期証券 (TB) の額面価格 $100 に対する価格を計算します。", "ad": "には、財務省短期証券の受渡日を日付のシリアル値で指定します。!には、財務省短期証券の満期日を日付のシリアル値で指定します。!には、財務省短期証券の割引率を指定します。"}, "TBILLYIELD": {"a": "(受渡日; 満期日; 現在価格)", "d": "米国財務省短期証券 (TB) の利回りを計算します。", "ad": "には、財務省短期証券の受渡日を日付のシリアル値で指定します。!には、財務省短期証券の満期日を日付のシリアル値で指定します。!には、財務省短期証券の額面価格 $100 に対する価格を指定します。"}, "VDB": {"a": "(取得価額; 残存価額; 耐用年数; 開始期; 終了期; [率]; [切り替えなし])", "d": "倍額定率法または指定された方法を使用して、特定の期における資産の減価償却費を返します。", "ad": "には資産を購入した時点での価格を指定します。!には耐用年数が終了した時点での資産の価格を指定します。!には償却の対象となる資産の寿命年数を指定します。(耐用年数)!には減価償却費の計算の対象となる最初の期を、耐用年数と同じ単位で指定します。!には減価償却費の対象となる最後の期を、耐用年数と同じ単位で指定します。!には減価償却率を指定します。率を省略すると、2 を指定したと見なされ、倍額定率法で計算が行われます。!には減価償却費が倍率法による計算結果より大きくなったときに、自動的に定額法に切り替えるかどうかを、論理値 (切り替え = FALSE または省略、切り替えなし = TRUE) で指定します。"}, "XIRR": {"a": "(範囲; 日付; [推定値])", "d": "一連のキャッシュフロー (投資と収益の金額) に基づいて、投資の内部利益率を計算します", "ad": "には、日付で指定されるスケジュールに対応した一連のキャッシュフローを数値配列として指定します!には、キャッシュフローに対応する支払日のスケジュールを数値配列として指定します!は、省略可能な引数で、XIRR 関数の計算結果に近いと思われる数値を指定します"}, "XNPV": {"a": "(割引率; キャッシュフロー; 日付)", "d": "一連のキャッシュフロー (投資と収益の金額) に基づいて、投資の正味現在価値を計算します", "ad": "には、対象となるキャッシュフローに適用する割引率を指定します!には、日付で指定されるスケジュールに対応した一連のキャッシュフローを数値配列として指定します!には、キャッシュフローに対応する支払日のスケジュールを数値配列として指定します"}, "YIELD": {"a": "(受渡日; 満期日; 利率; 現在価値; 償還価額; 頻度; [基準])", "d": "定期的に利息が支払われる証券の利回りを計算します。", "ad": "には、証券の受渡日の日付のシリアル値で指定します。!には、証券の年利を指定します。!には、額面 $100 に対する証券の価格を指定します。!には、額面 $100 に対する証券の価格を指定します。!には、額面 $100 に対する証券の償還価額を指定します。!には、年間の利息支払回数を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "YIELDDISC": {"a": "(受渡日; 満期日; 現在価値; 償還価額; [基準])", "d": "米国財務省短期証券 (TB) などの割引債の年利回りを計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、額面 $100 に対する証券の価格を指定します。!には、額面 $100 に対する証券の償還価額を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "YIELDMAT": {"a": "(受渡日; 満期日; 発行日; 利率; 現在価値; [基準])", "d": "満期日に利息が支払われる証券を対象に、年利回りを計算します。", "ad": "には、証券の受渡日を日付のシリアル値で指定します。!には、証券の満期日を日付のシリアル値で指定します。!には、証券の発行日を日付のシリアル値で指定します。!には、発行時点での証券の利率を指定します。!には、額面 $100 に対する証券の価格を指定します。!には、利息計算の基礎となる日数の計算方法を数値で指定します。"}, "ABS": {"a": "(数値)", "d": "数値から符号 (+、-) を除いた絶対値を返します。", "ad": "には絶対値を求める実数を指定します。"}, "ACOS": {"a": "(数値)", "d": "数値のアークコサインを返します。戻り値の角度は、0 (ゼロ)  ～ PI の範囲のラジアンとなります。アークコサインとは、そのコサインが数値であるような角度のことです。", "ad": "には求める角度のコサインの値を -1 ～ 1 の範囲で指定します。"}, "ACOSH": {"a": "(数値)", "d": "数値の双曲線逆余弦を返します。", "ad": "には 1 以上の実数を指定します。"}, "ACOT": {"a": "(数値)", "d": "数値の逆余接を返します。戻り値の角度は、0 ～ Pi の範囲のラジアンとなります。", "ad": "には、求める角度の余接の値を指定します。"}, "ACOTH": {"a": "(数値)", "d": "数値の逆双曲線余接を返します。", "ad": "には、求める角度の逆双曲線余接の値を指定します。"}, "AGGREGATE": {"a": "(集計方法; オプション; 参照1; ...)", "d": "リストまたはデータベースの集計値を返します。", "ad": "には、集計に使用する関数を 1 ～ 19 の数値で指定します。!には集計で無視する値を 1 ～ 7 の数値で指定します。!には、集計する配列またはセル範囲を指定します。!は、配列内の位置を示します。k 番目に大きな値、k 番目に小さな値、k 番目の百分位、または k 番目の四分位となります。!には、集計に使用する関数を 1 ～ 19 の数値で指定します。!には集計で無視する値を 1 ～ 7 の数値で指定します。!には、集計する範囲または参照を 1 ～ 253 個まで指定します。"}, "ARABIC": {"a": "(文字列)", "d": "ローマ数字をアラビア数字に変換します。", "ad": "には変換するローマ数字を指定します。"}, "ASC": {"a": "(文字列)", "d": "2 バイト文字セット (DBCS) 言語の場合、全角 (2 バイト) 文字を半角 (1 バイト) 文字に変更します。", "ad": "文字列または変換する文字列を含むセルの参照を指定します。"}, "ASIN": {"a": "(数値)", "d": "数値のアークサインを返します。戻り値の角度は、-PI/2 ～ PI/2 の範囲のラジアンとなります。", "ad": "には求める角度のサインの値を -1 ～ 1 の範囲で指定します。"}, "ASINH": {"a": "(数値)", "d": "数値の双曲線逆正弦を返します。", "ad": "には 1 以上の実数を指定します。"}, "ATAN": {"a": "(数値)", "d": "数値のアークタンジェントを返します。戻り値の角度は、-PI/2 ～ PI/2 の範囲のラジアンとなります。", "ad": "には求める角度のタンジェントの値を指定します。"}, "ATAN2": {"a": "(x座標; y座標)", "d": "指定された x-y 座標のアークタンジェントを返します。戻り値の角度は、-PI から PI (ただし -PI を除く) の範囲のラジアンとなります。", "ad": "には x 座標を指定します。!には y 座標を指定します。"}, "ATANH": {"a": "(数値)", "d": "数値の双曲線逆正接を返します。", "ad": "には -1 より大きく 1 より小さい実数を指定します。"}, "BASE": {"a": "(数値; 基数; [最小長])", "d": "数値を特定の基数 (底) を持つテキスト表現に変換します。", "ad": "には変換する数値を指定します。!には数値を変換する基数を指定します。!には返される文字列の最小長を指定します。先頭の 0 が省略された場合は追加されません。"}, "CEILING": {"a": "(数値; 基準値)", "d": "指定された基準値の倍数のうち、最も近い値に数値を切り上げます。", "ad": "には対象となる数値を指定します。!には倍数の基準となる数値を指定します。"}, "CEILING.MATH": {"a": "(数値; [基準値]; [モード])", "d": "数値を最も近い整数、または最も近い基準値の倍数に切り上げます。", "ad": "には対象となる値を指定します。!には倍数の基準となる数値を指定します。!指定され、かつ 0 以外の場合、この関数は 0 とは逆の方向に切り上げます。"}, "CEILING.PRECISE": {"a": "(数値; [基準値])", "d": "最も近い整数に切り上げた値、または、指定された基準値の倍数のうち最も近い値を返します。", "ad": "には対象となる数値を指定します。!には倍数の基準となる数値を指定します。"}, "COMBIN": {"a": "(総数; 抜き取り数)", "d": "すべての項目から指定された個数を選択するときの組み合わせの数を返します。", "ad": "には抜き取る対象の全体の数を指定します。!には抜き取る組み合わせ 1 組に含まれる項目の数を指定します。"}, "COMBINA": {"a": "(数値; 抜き取り数)", "d": "すべての項目から指定された個数を選択するときの組み合わせ (反復あり) の数を返します。", "ad": "には抜き取る対象の全体の数を指定します。!には抜き取る組み合わせ 1 組に含まれる項目の数を指定します。"}, "COS": {"a": "(数値)", "d": "角度のコサインを返します。", "ad": "にはコサインを求める角度をラジアンを単位として指定します。"}, "COSH": {"a": "(数値)", "d": "数値の双曲線余弦を返します。", "ad": "には実数を指定します。"}, "COT": {"a": "(数値)", "d": "角度の余接を返します。", "ad": "には、余接を求める角度をラジアンを単位として指定します。"}, "COTH": {"a": "(数値)", "d": "数値の双曲線余接を返します。", "ad": "には、双曲線余接を求める角度をラジアンを単位として指定します。"}, "CSC": {"a": "(数値)", "d": "角度の余割を返します。", "ad": "には、余割を求める角度をラジアンを単位として指定します。"}, "CSCH": {"a": "(数値)", "d": "角度の双曲線余割を返します。", "ad": "には、双曲線余割を求める角度をラジアンを単位として指定します。"}, "DECIMAL": {"a": "(数値; 基数)", "d": "指定された底の数値のテキスト表現を 10 進数に変換します。", "ad": "には変換する数値を指定します。!には変換する数値の基数を指定します。"}, "DEGREES": {"a": "(角度)", "d": "ラジアンで表された角度を度に変更します。", "ad": "には変換したい角度をラジアン単位で指定します。"}, "ECMA.CEILING": {"a": "(数値; 基準値)", "d": "指定された基準値の倍数のうち、最も近い値に数値を切り上げます。", "ad": "には対象となる数値を指定します。!には倍数の基準となる数値を指定します。"}, "EVEN": {"a": "(数値)", "d": "指定した数値をもっとも近い偶数に切り上げた値を返します。", "ad": "には切り上げたい数値を指定します。"}, "EXP": {"a": "(数値)", "d": "e を底とする数値のべき乗を返します。", "ad": "には e を底とするべき乗の指数を指定します。定数 e は自然対数の底で、e = 2.71828182845904 となります。"}, "FACT": {"a": "(数値)", "d": "数値の階乗を返します。数値の階乗は、1 ～ 数値の範囲にある整数の積です。", "ad": "には階乗を求める正の数値を指定します。"}, "FACTDOUBLE": {"a": "(数値)", "d": "数値の二重階乗を計算します。", "ad": "には、二重階乗する数値を指定します。"}, "FLOOR": {"a": "(数値; 基準値)", "d": "指定された基準値の倍数のうち、最も近い値に数値を切り捨てます。", "ad": "には対象となる数値を指定します。!には倍数の基準となる数値を指定します。数値と基準値の正負の符号が同じである必要があります。"}, "FLOOR.PRECISE": {"a": "(数値; [基準値])", "d": "最も近い整数、または最も近い基準値の倍数に切り捨てる数値を返します。", "ad": "には対象となる数値を指定します。!には倍数の基準となる数値を指定します。"}, "FLOOR.MATH": {"a": "(数値; [基準値]; [モード])", "d": "数値を最も近い整数、または最も近い基準値の倍数に切り下げます。", "ad": "には対象となる値を指定します。!には倍数の基準となる数値を指定します。!指定され、かつ 0 以外の場合、この関数は 0 の方向に切り下げます。"}, "GCD": {"a": "(数値1; [数値2]; ...)", "d": "指定した数値の最大公約数を計算します。", "ad": "には、最大 255 個までの数値を指定できます。"}, "INT": {"a": "(数値)", "d": "切り捨てて整数にした数値を返します。", "ad": "には切り捨てて整数にする実数を指定します。"}, "ISO.CEILING": {"a": "(数値, [基準値])", "d": "最も近い整数に切り上げた値、または、指定された基準値の倍数のうち最も近い値を返します。 数値は正負に関係なく切り上げられます。 ただし、数値または基準値が 0 の場合は 0 が返されます。", "ad": "には対象となる数値を指定します。!には倍数の基準となる数値を指定します。"}, "LCM": {"a": "(数値1; [数値2]; ...)", "d": "指定した整数の最小公倍数を計算します。", "ad": "には、最大 255 個までの数値を指定できます。"}, "LN": {"a": "(数値)", "d": "数値の自然対数を返します。", "ad": "には自然対数を求めたい、正の実数値を指定します。"}, "LOG": {"a": "(数値; [底])", "d": "指定された数を底とする数値の対数を返します。", "ad": "には対数を求めたい、正の実数値を指定します。!には対数の底を指定します。底を省略すると、10 を指定したと見なされます。"}, "LOG10": {"a": "(数値)", "d": "引数の常用対数を返します。", "ad": "には常用対数を求めたい、正の実数値を指定します。"}, "MDETERM": {"a": "(配列)", "d": "配列の行列式を返します。", "ad": "には行数と列数が等しい数値配列 (正方行列) を指定します。セル範囲かまたは配列定数のいずれかを指定します。"}, "MINVERSE": {"a": "(配列)", "d": "配列の逆行列を返します。", "ad": "には行数と列数が等しい数値配列 (正方行列) を指定します。セル範囲かまたは配列定数のいずれかを指定します。"}, "MMULT": {"a": "(配列1; 配列2)", "d": "2 つの配列の積を返します。計算結果は、行数が配列 1 と同じで、列数が配列 2 と同じ配列になります。", "ad": "には行列積を求める最初の配列を指定します。配列 1 の列数は、配列 2 の行数と等しくなければなりません。"}, "MOD": {"a": "(数値; 除数)", "d": "数値を除算した剰余を返します。", "ad": "には除算の分子となる数値を指定します。!には除算の分母となる数値を指定します。"}, "MROUND": {"a": "(数値; 倍数)", "d": "指定した値の倍数になるように数値の切り上げあるいは切り捨てを行います。", "ad": "には、切り上げあるいは切り捨ての対象となる数値を指定します。!には、切り上げあるいは切り捨てられた数値が、その倍数となるような数値を指定します。"}, "MULTINOMIAL": {"a": "(数値1; [数値2]; ...)", "d": "指定された数値の和の階乗と、指定された数値の階乗の積との比を計算します。", "ad": "には、計算の対象となる数値を最大 255 個まで指定できます。"}, "MUNIT": {"a": "(次元)", "d": "指定された次元の単位行列を返します。", "ad": "には返す単位行列の次元を指定する整数を指定します。"}, "ODD": {"a": "(数値)", "d": "正の数値を切り上げ、負の数値を切り捨てて、最も近い奇数にします。", "ad": "には対象となる数値を指定します。"}, "PI": {"a": "()", "d": "円周率π (3.14159265358979) を返します。", "ad": ""}, "POWER": {"a": "(数値; 指数)", "d": "数値を累乗した値を返します。", "ad": "にはべき乗の底を指定します。任意の実数を指定できます。!には <数値> を底とするべき乗の指数を指定します。"}, "PRODUCT": {"a": "(数値1; [数値2]; ...)", "d": "引数の積を返します。", "ad": "には積を求めたい数値、論理値、または数値を表す文字列を、1 ～ 255 個まで指定できます。"}, "QUOTIENT": {"a": "(分子; 分母)", "d": "除算の商の整数部を返します。", "ad": "には、被除数 (割られる数) を指定します。!には、除数 (割る数) を指定します。"}, "RADIANS": {"a": "(角度)", "d": "度単位で表された角度をラジアンに変換した結果を返します。", "ad": "には変換したい角度を度単位で指定します。"}, "RAND": {"a": "()", "d": "0 以上で 1 より小さい乱数を発生させます。再計算されるたびに、新しい乱数が返されます。", "ad": ""}, "RANDARRAY": {"a": "([行]; [列]; [最小]; [最大]; [整数])", "d": "乱数の配列を返します。", "ad": "には返される配列の行数を指定します。!には返される配列の列数を指定します。!には返される最小数値を指定します。!には返される最大数値を指定します。!では、整数値または小数値が返されることを指定します。整数値の場合は TRUE、小数値の場合は FALSE を指定します。"}, "RANDBETWEEN": {"a": "(最小値; 最大値)", "d": "指定された範囲で一様に分布する整数の乱数を返します。", "ad": "乱数の最小値を整数で指定します。!乱数の最大値を整数で指定します。"}, "ROMAN": {"a": "(数値; [書式])", "d": "アラビア数字を、ローマ数字を表す文字列に変換します。", "ad": "には変換したいアラビア数字を指定します。!にはローマ数字の書式を数値で指定します。"}, "ROUND": {"a": "(数値; 桁数)", "d": "数値を指定した桁数に四捨五入した値を返します。", "ad": "には四捨五入の対象となる数値を指定します。!には四捨五入する桁数を指定します。桁数に負の数を指定すると、小数点の左側 (整数部分) の指定した桁 (1 の位を 0 とする) に、0 を指定すると、最も近い整数として四捨五入されます。"}, "ROUNDDOWN": {"a": "(数値; 桁数)", "d": "数値を切り捨てます。", "ad": "には切り捨ての対象となる実数値を指定します。!には数値を切り捨てた結果の桁数を指定します。桁数に負の数を指定すると、数値は小数点の左 (整数部分) の指定した桁 (1 の位を 0 とする) に切り捨てられ、 0 を指定するかまたは省略されると、最も近い整数に切り捨てられます。"}, "ROUNDUP": {"a": "(数値; 桁数)", "d": "数値を切り上げます。", "ad": "には切り上げの対象となる実数値を指定します。!には数値を切り上げた結果の桁数を指定します。桁数に負の数を指定すると、数値は小数点の左 (整数部分) の指定した桁 (1 の位を 0 とする) に切り上げられ、 0 を指定するかまたは省略されると、最も近い整数に切り上げられます。"}, "SEC": {"a": "(数値)", "d": "角度の正割を返します。", "ad": "には、正割を求める角度をラジアンを単位として指定します。"}, "SECH": {"a": "(数値)", "d": "角度の双曲線正割を返します。", "ad": "には、双曲線正割を求める角度をラジアンを単位として指定します。"}, "SERIESSUM": {"a": "(x; n; m; 係数)", "d": "べき級数の和を計算します。", "ad": "には、べき級数に代入する値を指定します。!には、X のべき乗の初期値を指定します。!には、級数の各項ごとに n を増加させる値を指定します。!には、X のべき乗である各項にかける一連の係数を指定します。"}, "SIGN": {"a": "(数値)", "d": "数値の正負を返します。戻り値は、数値が正の数のときは 1、0 のときは 0、負の数のときは -1 となります。", "ad": "には実数を指定します。"}, "SIN": {"a": "(数値)", "d": "角度のサインを返します。", "ad": "にはサインを求める角度をラジアンを単位として指定します。角度が度を単位として表されている場合は、PI()/180 を掛けるとラジアンに変換されます。"}, "SINH": {"a": "(数値)", "d": "数値の双曲サインを返します。", "ad": "には実数を指定します。"}, "SQRT": {"a": "(数値)", "d": "数値の正の平方根を返します。", "ad": "には平方根を求めたい数値を指定します。"}, "SQRTPI": {"a": "(数値)", "d": "数値 x πの平方根の値を計算します。", "ad": "には、πとかけ算する数値を指定します。"}, "SUBTOTAL": {"a": "(集計方法; 参照1; ...)", "d": "リストまたはデータベースの集計値を返します。", "ad": "にはリストの集計に使用する関数を、1 ～ 11 の番号で指定します。!には集計するリストの範囲または参照を 1 ～ 254 個まで指定します。"}, "SUM": {"a": "(数値1; [数値2]; ...)", "d": "セル範囲に含まれる数値をすべて合計します。", "ad": "には合計を求めたい数値を 1 ～ 255 個まで指定できます。論理値および文字列は無視されますが、引数として入力されていれば計算の対象となります。"}, "SUMIF": {"a": "(範囲; 検索条件; [合計範囲])", "d": "指定した検索条件に一致するセルの値を合計します", "ad": "には値を求める対象となるセル範囲を指定します!には計算の対象となるセルを定義する条件を数値、式、または文字列で指定します!には実際に計算の対象となるセル範囲を指定します。合計範囲を省略すると、範囲内で検索条件を満たすセルが合計されます"}, "SUMIFS": {"a": "(合計対象範囲; 条件範囲; 条件; ...)", "d": "特定の条件に一致する数値の合計を求めます", "ad": "には合計対象の実際のセルを指定します!には、特定の条件で値を求める対象となるセル範囲を指定します!には、計算の対象となるセルを定義する条件を数値、式、または文字列で指定します"}, "SUMPRODUCT": {"a": "(配列1; [配列2]; [配列3]; ...)", "d": "範囲または配列の対応する要素の積を合計した結果を返します。", "ad": "には要素の積の合計を求めたい配列を 2 ～ 255 個まで指定できます。引数となる配列は、行数と列数が等しい配列である必要があります。"}, "SUMSQ": {"a": "(数値1; [数値2]; ...)", "d": "引数の 2 乗の和 (平方和) を返します。引数には、数値、数値を含む名前、配列、セル参照を指定できます。", "ad": "には平方和を求めたい数値、または数値を含む名前、配列、セル参照を 1 ～ 255 個まで指定できます。"}, "SUMX2MY2": {"a": "(配列1; 配列2)", "d": "2 つの配列で対応する配列要素の平方差を合計します。", "ad": "には最初の範囲、または値の配列を指定します。引数には、数値、名前、配列、あるいは数値を含むセル参照を指定します。!には 2 番めの範囲、または値の配列を指定します。引数には、数値、名前、配列、あるいは数値を含むセル参照を指定します。"}, "SUMX2PY2": {"a": "(配列1; 配列2)", "d": "2 つの配列の対応する値の積を合計した結果を返します。", "ad": "には先頭の範囲、または値の配列を指定します。!には 2 番めの範囲、または値の配列を指定します。"}, "SUMXMY2": {"a": "(配列1; 配列2)", "d": "2 つの配列で対応する配列要素の差を 2 乗し、さらにその合計を返します。", "ad": "には対象となる一方の範囲、または数値の配列を指定します。引数には、数値、名前、配列、または数値を含む参照を指定します。!には対象となるもう一方の範囲、または数値の配列を指定します。引数には、数値、名前、配列、または数値を含む参照を指定します。"}, "TAN": {"a": "(数値)", "d": "角度のタンジェントを返します。", "ad": "にはタンジェントを求めたい角度をラジアンを単位として指定します。"}, "TANH": {"a": "(数値)", "d": "数値の双曲タンジェントを返します。", "ad": "には実数を指定します。"}, "TRUNC": {"a": "(数値; [桁数])", "d": "数値の小数部を切り捨てて、整数または指定した桁数に変換します。", "ad": "には小数部を切り捨てる数値を指定します。!には切り捨てを行った後の桁数を指定します。桁数の既定値は 0 (ゼロ) です。"}, "ADDRESS": {"a": "(行番号; 列番号; [参照の種類]; [参照形式]; [シート名])", "d": "指定したセルの参照を文字列の形式で返します。", "ad": "にはセル参照に使用する行番号を指定します。たとえば、1 行目には 1 を指定します。!にはセル参照に使用する列番号を指定します。たとえば、D 列には 4 を指定します。!にはセル参照の種類を指定します。絶対参照の場合は 1、行が絶対参照で列が相対参照の場合は 2、行が相対参照で列が絶対参照の場合は 3、相対参照の場合は 4 を指定します。!セル参照を A1 形式にするか R1C1 形式にするかを論理値で指定します。参照形式に TRUE を指定する、または省略すると、A1 形式のセル参照が返され、FALSE を指定すると、R1C1 形式のセル参照が返されます。!には外部参照として使用するワークシートの名前を文字列で指定します。"}, "CHOOSE": {"a": "(インデックス; 値1; [値2]; ...)", "d": "インデックスを使って、引数リストから特定の値または動作を 1 つ選択します。", "ad": "には引数リストの何番目の値を選択するかを指定します。インデックスには、1  ～ 254 までの数値、または 1 ～ 254 までの数値を返す数式またはセル参照を指定します。!には 1 ～ 254 個の引数 (数値、セル参照、名前、数式、関数、文字列) を指定します。ここからインデックスで指定した値が返されます。"}, "COLUMN": {"a": "([参照])", "d": "参照の列番号を返します。", "ad": "には列番号を調べるセルまたはセル範囲を指定します。範囲を省略すると、COLUMN 関数が入力されているセルの列番号が返されます。"}, "COLUMNS": {"a": "(配列)", "d": "配列または参照の列数を返します。", "ad": "には列数を計算する配列、配列数式またはセル範囲の参照を指定します。"}, "FORMULATEXT": {"a": "(参照)", "d": "数式を文字列として返します。", "ad": "には数式への参照を指定します。"}, "HLOOKUP": {"a": "(検索値; 範囲; 行番号; [検索方法])", "d": "指定したテーブルまたは配列の先頭行で特定の値を検索し、指定した列と同じ行にある値を返します。", "ad": "には範囲の先頭行で検索する値を指定します。検索値には、値、セル参照、または文字列を指定します。!には目的のデータが含まれる文字列、数値、または論理値のテーブルを指定します。セル範囲の参照、またはセル範囲名を指定します。!には範囲の行番号を指定します。ここで指定された行で一致する値が返されます。範囲の先頭行には 1 を指定します。!には検索値と完全に一致する値だけを検索するか、その近似値を含めて検索するかを、論理値 (近似値を含めて検索 = TRUE または省略、完全一致の値を検索 = FALSE) で指定します。"}, "HYPERLINK": {"a": "(リンク先; [別名])", "d": "ハード ディスク、ネットワーク サーバー、またはインターネット上に格納されているドキュメントを開くために、ショートカットまたはジャンプを作成します。", "ad": "にはドキュメントを開くためのパスおよびファイル名、ハード ディスクの位置、UNC アドレス、または URL パスを指定します。!にはセルに表示する文字列または数値を指定します。"}, "INDEX": {"a": "(配列; 行番号; [列番号]!参照; 行番号; [列番号]; [領域番号])", "d": "指定された行と列が交差する位置にある値またはセルの参照を返します。", "ad": "にはセル範囲または配列定数を指定します。!には配列または参照の中にあり、値を返す行を数値で指定します。省略した場合は、必ず列番号を指定する必要があります。!には配列または参照の中にあり、値を返す列を数値で指定します。省略した場合は、必ず行番号を指定する必要があります。!には 1 つ、または複数のセル範囲への参照を指定します。!には配列または参照の中にあり、値を返す行を数値で指定します。省略した場合は、必ず列番号を指定する必要があります。!には配列または参照の中にあり、値を返す列を数値で指定します。省略した場合は、必ず行番号を指定する必要があります。!参照内の範囲を指定します。指定した範囲の中から値が返されます。最初に選択または入力された領域の領域番号が 1 となり、以下、2 番目の領域は 2 と続きます。"}, "INDIRECT": {"a": "(参照文字列; [参照形式])", "d": "指定される文字列への参照を返します。", "ad": "には A1 形式、R1C1 形式の参照、参照として定義されている名前が入力されているセルへの参照、または文字列としてのセルへの参照を指定します。!には参照文字列で指定されたセルに含まれる参照の種類を、論理値 (A1 形式 = TRUE または省略、R1C1 形式 = FALSE) で指定します。"}, "LOOKUP": {"a": "(検査値; 検査範囲; [対応範囲]!検査値; 配列)", "d": "1 行または 1 列のみのセル範囲、または配列に含まれる値を返します。この関数は旧バージョンとの互換性を維持するためのものです。", "ad": "にはベクトルで検索する値を指定します。検査値には、数値、文字列、論理値、または値を参照する名前やセル参照を指定できます。!には 1 行または 1 列からのみ成り立つ範囲を指定します。検査範囲には、昇順に配置されている文字列、数値、または論理値を指定できます。!には 1 行または 1 列からのみ成り立つ範囲を指定します。対応範囲は検査範囲と同じサイズあることが必要です。!には配列で検索する値を指定します。検査値には、数値、文字列、論理値、または値を参照する名前やセル参照を指定できます。!には検査値と比較する文字列、数値、または論理値を含むセル範囲を指定します。"}, "MATCH": {"a": "(検査値; 検査範囲; [照合の種類])", "d": "指定された照合の種類に従って検査範囲内を検索し、検査値と一致する要素の、配列内での相対的な位置を表す数値を返します。", "ad": "には、配列、数値、文字列、論理値、またはこれらの値への参照の中で必要な項目を検索するために使用する値を指定します。!には、検査値が入力されている連続したセル範囲、値の配列、または配列への参照を指定します。!には 1、0、または -1 の数値のいずれかを指定し、検査値を検索する方法を指定します。"}, "OFFSET": {"a": "(参照; 行数; 列数; [高さ]; [幅])", "d": "指定した参照から指定した行数、列数の範囲への参照を返します。", "ad": "にはオフセットの基準となる参照を指定します。セルまたは隣接するセル範囲を参照する必要があります。!には基準の左上隅のセルを上方向または下方向へシフトする距離を行数単位で指定します。!には基準の左上隅のセルを左方向または右方向へシフトする距離を列数単位で指定します。!には設定したい高さを行数で指定します。高さを省略すると、基準の参照と同じ行数であると見なされます。!には設定したい幅を列数で指定します。幅を省略すると、基準の参照と同じ列数であると見なされます。"}, "ROW": {"a": "([参照])", "d": "参照の行番号を返します。", "ad": "には行番号を調べるセルまたはセル範囲を指定します。範囲を省略すると、ROW 関数が入力されているセルの行番号が返されます。"}, "ROWS": {"a": "(配列)", "d": "参照、または配列に含まれる行数を返します。", "ad": "には行数を求めたい配列、配列数式、またはセル範囲への参照を指定します。"}, "TRANSPOSE": {"a": "(配列)", "d": "配列の縦方向と横方向のセル範囲の変換を行います。", "ad": "には行列変換を行うワークシートのセル範囲または値の配列を指定します。"}, "UNIQUE": {"a": "(配列; [列の比較]; [回数指定])", "d": "範囲または配列から一意の値を返します", "ad": "は一意の行または列を返す範囲または配列です!は論理値です: 行同士を比較して一意の行を返す = FALSE または省略、列同士を比較して一意の列を返す = TRUE!は論理値です: 1 回だけ出現する行または列を配列から返す = TRUE; 配列から個別の行または列をすべて返す = FALSE または省略"}, "VLOOKUP": {"a": "(検索値; 範囲; 列番号; [検索方法])", "d": "指定された範囲の 1 列目で特定の値を検索し、指定した列と同じ行にある値を返します。テーブルは昇順で並べ替えておく必要があります。", "ad": "には範囲の先頭列で検索する値を指定します。検索値には、値、セル参照、または文字列を指定します。!には目的のデータが含まれる文字列、数値、または論理値のテーブルを指定します。セル範囲の参照、またはセル範囲名を指定します。!は範囲の列番号を指定します。ここで指定された列で一致する値が返されます。範囲の先頭列には 1 を指定します。!には検索値と完全に一致する値だけを検索するか、その近似値を含めて検索するかを、論理値 (近似値を含めて検索 = TRUE または省略、完全一致の値を検索 = FALSE) で指定します。"}, "XLOOKUP": {"a": "(検索値; 検索範囲; 戻り範囲; [見つからない場合]; [一致モード]; [検索モード])", "d": "範囲または配列で一致の検索を行い、2 つめの範囲または配列から対応する項目を返します。既定では、完全一致が使用されます", "ad": "には検索する値を指定します!には検索対象の配列または範囲を指定します!には戻り値の配列または範囲を指定します!一致が見つからない場合に返されます!には検索範囲内の値に対する検索値の一致方法を指定します!には使用する検索モードを指定します。既定では、先頭から末尾への検索が使用されます"}, "CELL": {"a": "(検査の種類; [対象範囲])", "d": "セルの書式、位置、または内容に関する情報を返します。", "ad": "返すセル情報の種類を指定するテキスト値。!情報が必要なセルを指定します。"}, "ERROR.TYPE": {"a": "(エラー値)", "d": "エラー値に対応する数値を返します。", "ad": "にはエラー識別番号を調べるエラー値を指定します。実際のエラー値、またはエラー値を含むセルの参照を指定します。"}, "ISBLANK": {"a": "(テストの対象)", "d": "セルの内容が空白の場合に TRUE を返します。", "ad": "にはテストしたいセル、またはセルを参照する名前を指定します。"}, "ISERR": {"a": "(テストの対象)", "d": "セルの内容が #N/A 以外のエラー値の場合に TRUE を返します。", "ad": "にはテストするデータを指定します。引数には、セル、数式、またはセル、数式、値を参照する名前を指定することができます。"}, "ISERROR": {"a": "(テストの対象)", "d": "セルの内容がエラー値の場合に TRUE を返します。", "ad": "にはテストするデータを指定します。引数には、セル、数式、またはセル、数式、値を参照する名前を指定することができます。"}, "ISEVEN": {"a": "(数値)", "d": "引き数に指定した数値が偶数のとき TRUE を返し、奇数のとき FALSE を返します。", "ad": "には、対象となる数値を指定します。"}, "ISFORMULA": {"a": "(参照)", "d": "参照が数式を含むセルに対するものかどうかを確認し、TRUE または FALSE を返します。", "ad": "にはテストするセルへの参照を指定します。参照には、セル参照、数式、またはセルを参照する名前を指定できます。"}, "ISLOGICAL": {"a": "(テストの対象)", "d": "セルの内容が論理値 (TRUE または FALSE) の場合に TRUE を返します。", "ad": "にはテストするデータを指定します。引数には、セル、数式、またはセル、数式、値を参照する名前を指定することができます。"}, "ISNA": {"a": "(テストの対象)", "d": "セルの内容がエラー値 #N/A の場合に TRUE を返します。", "ad": "にはテストするデータを指定します。引数には、セル、数式、またはセル、数式、値を参照する名前を指定することができます。"}, "ISNONTEXT": {"a": "(テストの対象)", "d": "セルの内容が文字列以外の値 (空白セルも対象) である場合に TRUE を返します。", "ad": "には テストするデータを指定します。引数には、セル、数式、またはセル、数式、値を参照する名前を指定することができます。"}, "ISNUMBER": {"a": "(テストの対象)", "d": "セルの内容が数値の場合に TRUE を返します。", "ad": "にはテストするデータを指定します。引数には、セル、数式、またはセル、数式、値を参照する名前を指定することができます。"}, "ISODD": {"a": "(数値)", "d": "引き数に指定した数値が奇数のとき TRUE を返し、偶数のとき FALSE を返します。", "ad": "には、対象となる数値を指定します。"}, "ISREF": {"a": "(テストの対象)", "d": "セルの内容が参照である場合に TRUE を返します。", "ad": "にはテストするデータを指定します。引数には、セル、数式、またはセル、数式、値を参照する名前を指定することができます。"}, "ISTEXT": {"a": "(テストの対象)", "d": "セルの内容が文字列である場合に TRUE を返します。", "ad": "にはテストするデータを指定します。引数には、セル、数式、またはセル、数式、値を参照する名前を指定することができます。"}, "N": {"a": "(値)", "d": "非数値を数値に、日付をシリアル値に、TRUE の場合は 1 に、それ以外の場合は 0 に変換します。", "ad": "には、変換する値を指定します。"}, "NA": {"a": "()", "d": "エラー値 #N/A (値が無効) を返します。", "ad": ""}, "SHEET": {"a": "([値])", "d": "参照されるシートのシート番号を返します。", "ad": "にはシート番号を求めるシートまたは参照の名前を指定します。省略した場合、関数を含むシート番号が返されます。"}, "SHEETS": {"a": "([参照])", "d": "参照内のシート数を返します。", "ad": "には含まれているシート数を求める参照を指定します。省略した場合、関数を含む、ブック内のシート数が返されます。"}, "TYPE": {"a": "(値)", "d": "値のデータ型を表す整数 (数値 = 1、文字列 = 2、論理値 = 4、エラー値 = 16、配列 = 64、複合データ = 128) を返します。", "ad": "数値、文字列、論理値など Excel で処理できる値であれば、何でも指定できます。"}, "AND": {"a": "(論理式1; [論理式2]; ...)", "d": "すべての引数が TRUE のとき、TRUE を返します。", "ad": "には結果が TRUE または FALSE になる、1 ～ 255 個の論理式を指定できます。引数には論理値、配列、または参照を指定します。 "}, "FALSE": {"a": "()", "d": "論理値 FALSE を返します。", "ad": ""}, "IF": {"a": "(論理式; [値が真の場合]; [値が偽の場合])", "d": "論理式の結果 (真または偽) に応じて、指定された値を返します", "ad": "には結果が真または偽になる値、もしくは数式を指定します!には論理式の結果が真であった場合に返される値を指定します。省略した場合、TRUE が返されます。最大 7 つまでの IF 関数をネストすることができます!には論理式の結果が偽であった場合に返される値を指定します。省略した場合、FALSE が返されます"}, "IFS": {"a": "(論理式; 値が真の場合; ...)", "d": "1 つ以上の条件が満たされるかどうかを確認し、最初の真条件に対応する値を返します", "ad": "は 真または偽と計算できる値または式です!は論理式が真の場合に返される値です"}, "IFERROR": {"a": "(値; エラーの場合の値)", "d": "式がエラーの場合は、エラーの場合の値を返します。エラーでない場合は、式の値自体を返します。", "ad": "には、任意の値、式、または参照を指定します。!には、任意の値、式、または参照を指定します。"}, "IFNA": {"a": "(値; NAの場合の値)", "d": "式が #N/A に解決される場合に指定する値を返します。それ以外の場合は、式の結果を返します。", "ad": "には任意の値または式または参照を返します。!には任意の値または式または参照を返します。"}, "NOT": {"a": "(論理式)", "d": "引数が FALSE の場合は TRUE、TRUE の場合は FALSE を返します", "ad": "には結果が TRUE または FALSE になる値、または数式を指定します"}, "OR": {"a": "(論理式1; [論理式2]; ...)", "d": "いずれかの引数が TRUE のとき、TRUE を返します。引数がすべて FALSE である場合は、FALSE を返します。", "ad": "には結果が TRUE または FALSE になる、1 ～ 255 個の論理式を指定します。"}, "SWITCH": {"a": "(式; 値1; 結果1; [既定または値2]; [結果2]; ...)", "d": " 値の一覧で式を計算し、最初に一致する値に対応する結果が返されます。一致しない場合は、任意の既定値が返されます", "ad": "は計算の対象の式で!式と比較される値となり!その結果、対応する値が式に一致するかどうかが返されます"}, "TRUE": {"a": "()", "d": "論理値 TRUE を返します。", "ad": ""}, "XOR": {"a": "(論理式1; [論理式2]; ...)", "d": "すべての引数の排他的論理和を返します。", "ad": "には結果が TRUE または FALSE になる、1 ～ 254 の論理式を指定します。引数には論理値、配列、または参照を指定します。"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "文字を区切る前のテキストを返します。", "ad": "区切り記号を検索するテキスト。!区切り記号として使用する文字または文字列。!区切り記号が必要です。!既定値は 1 です。末尾からの負の数の検索です。区切り記号の一致をテキストで検索します。!既定では、大文字と小文字を区別する一致が行われます。区切り記号をテキストの末尾と一致させるかどうかを指定します。既定では、一致しません。!一致するものが見つからない場合に返されます。既定では、#N/A が返されます。"}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "文字を区切った後のテキストを返します。", "ad": "区切り記号を検索するテキスト。!区切り記号として使用する文字または文字列。!区切り記号が必要です。既定値は 1 です。末尾からの負の数の検索です。!区切り記号の一致をテキストで検索します。既定では、大文字と小文字を区別する一致が行われます。!区切り記号をテキストの末尾と一致させるかどうかを指定します。既定では、一致しません。!一致するものが見つからない場合に返されます。既定では、#N/A が返されます。"}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "区切り記号を使用してテキストを行または列に分割。", "ad": "分割するテキスト。!列を分割する文字または文字列.!行を分割する文字または文字列.!空のセルを無視するかどうか。既定値は FALSE。!規定では、大文字と小文字の区別を実行します。!パディングに使用する値。既定では、#N/A が使用される。"}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "指定した数の値の後に行または列ベクトルを折り返します。", "ad": "折り返すベクターまたは参照。!1 行あたりの値の最大数。!埋め込む値。既定値は #N/A。"}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "垂直方向に配列を 1 つの配列にスタックします。", "ad": "スタックされる配列または参照。"}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "水平方向に配列を 1 つの配列に水にスタックします。", "ad": "スタックされる配列または参照。"}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "配列または参照から行を返します。", "ad": "返される行を含む配列または参照です。!返される行の数です。"}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "配列または参照から列を返します。", "ad": "返される列を含む配列または参照です。!返される列の数です。"}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "配列を 1 つの列として返します。", "ad": "列として返す配列または参照。!特定の種類の値を無視するかどうか。既定では、無視される値はありません。!列で配列をスキャンします。既定では、配列は行ごとにスキャンされます。"}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "配列を 1 行として返します。", "ad": "行として返す配列または参照。!特定の種類の値を無視するかどうか。既定では、無視される値はありません。!列で配列をスキャンします。既定では、配列は行ごとにスキャンされます。"}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "指定した数の値の後に行または列のベクトルをラップする。", "ad": "ラップするベクトルまたは参照。!列ごとの値の最大数。!埋め込む値。既定値は #N/A。"}, "TAKE": {"a": "(array, rows, [columns])", "d": "配列の開始または終了から行または列を返します。", "ad": "行または列の取得元の配列。!取得する行数。負の値は、配列の末尾から取得されます。!取得する列の数。負の値の場合は配列の末尾から取得されます。"}, "DROP": {"a": "(array, rows, [columns])", "d": "配列の先頭または末尾から行または列を削除します。", "ad": "行または列の削除元の配列。!削除する行数。負の値の場合は配列の末尾から削除されます。!削除する列の数。負の値の場合は配列の末尾から削除されます。"}, "SEQUENCE": {"a": "(行, [列], [開始], [目盛り])", "d": "数列を返します。", "ad": "には返される行数を指定します。!には返される列数を指定します。!には数列の最初の数値を指定します。!には数列の後続の各値の増分量を指定します。"}, "EXPAND": {"a": "(配列, 行, [列], [代替文字])", "d": "指定した次元に配列を展開します。", "ad": "展開する配列。!展開された配列内の行数。見つからない場合、行は展開されません。!展開された配列内の列の数。見つからない場合、列は展開されません。!パディングする値。既定値は #N/A です。"}, "XMATCH": {"a": "(検索値, 検索範囲, [一致モード], [検索モード])", "d": "配列内での項目の相対的な位置を返します。既定では、完全一致が必要です。", "ad": "には検索する値を指定します。!には検索対象の配列または範囲を指定します。!には検索範囲内の値に対する検索値の一致方法を指定します。!には使用する検索モードを指定します。既定では、先頭から末尾への検索が使用されます。"}, "FILTER": {"a": "(配列, 含む, [空の場合])", "d": "範囲または配列をフィルターします", "ad": "はフィルターする範囲または配列です!は Bool 値の配列で、TRUE の場合は保持する行または列を表します!アイテムを何も保持しない場合に返されます"}, "ARRAYTOTEXT": {"a": "(配列, [書式])", "d": "配列のテキスト表現を返します", "ad": "テキストとして表す配列を指定します!テキストの書式を指定します"}, "SORT": {"a": "(配列, [並べ替えインデックス], [並べ替え順序], [並べ替え基準])", "d": "範囲または配列を並べ替えます", "ad": "並べ替える範囲または配列です!並べ替える列または行を指示する数字です!目的の並べ替え順序を示す数字です。1 は昇順です (既定)。-1 は降順です!は並べ替えの方向を示す論理値です。FALSE は行で並べ替えます (既定)。TRUE は列で並べ替えます"}, "SORTBY": {"a": "(配列, 基準配列, [並べ替え順序], ...)", "d": "範囲または配列を、対応する範囲または配列の値に基づいて並べ替えます。", "ad": "には並べ替える範囲または配列を指定します。!には並べ替えの基準となる範囲または配列を指定します。!には目的の並べ替え順序を示す数値を指定します。1 は昇順 (既定)、-1 は降順です。"}, "GETPIVOTDATA": {"a": "(データフィールド; ピボットテーブル; [フィールド]; [アイテム]; ...)", "d": "ピボットテーブルに保存されているデータを取得します。", "ad": "には、データを取得するデータ フィールドの名前を指定します。!には、取得するデータが含まれているピボットテーブル内のセルまたはセル範囲への参照を指定します。!には、参照するフィールドを指定します。!には、参照するフィールド アイテムを指定します。"}, "IMPORTRANGE": {"a": "(スプレッドシートの URL, 範囲の文字列)", "d": "指定したスプレッドシートからセルの範囲を読み込みます。", "ad": "データの読み込み元となるスプレッドシートの URL を指定します。!読み込む範囲を指定する文字列で"}}