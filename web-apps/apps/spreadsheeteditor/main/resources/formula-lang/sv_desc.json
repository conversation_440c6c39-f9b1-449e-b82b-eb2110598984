{"DATE": {"a": "(<PERSON><PERSON>; m<PERSON>na<PERSON>; dag)", "d": "Return<PERSON>r numret som representerar datumet i datum-tidkoden", "ad": "är ett tal mellan 1900 eller 1904 (beroende på datumsystemet i arbetsboken) till 9999!är ett tal från 1 till 12 som representerar månaden på året!är ett tal från 1 till 31 som representerar dagen i månaden"}, "DATEDIF": {"a": "(startdatum; stoppdatum; enhet)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> ant<PERSON>, m<PERSON><PERSON><PERSON> eller år mellan två datum", "ad": "Ett datum som representerar det första eller det första startdatumet för en viss period!Ett datum som representerar det sista datumet i perioden!Den typ av information som du vill returnera"}, "DATEVALUE": {"a": "(datumtext)", "d": "Konverterar ett datum i form av text till ett tal som representerar datumet i datum-tidskod", "ad": "är text som representerar ett datum i datumformat för Spreadsheet Editor, mellan 1900-01-01  eller 1904-01-01 (beroende på arbetsboken datumsystem) och 9999-12-31"}, "DAY": {"a": "(serienummer)", "d": "<PERSON><PERSON><PERSON> dagen i m<PERSON>, ett tal mellan 1 och 31.", "ad": "är ett tal i datum-tidkoden som används av Spreadsheet Editor"}, "DAYS": {"a": "(stoppdatum; startdatum)", "d": "<PERSON><PERSON><PERSON> antalet dagar mellan två datum.", "ad": "startdatum och slutdatum är de två datum för vilka du vill veta antalet mellanliggande dagar.!startdatum och slutdatum är de två datum för vilka du vill veta antalet mellanliggande dagar."}, "DAYS360": {"a": "(startdatum; stoppdatum; [metod])", "d": "<PERSON><PERSON><PERSON> antalet dagar mellan två datum baserat på ett år med 360 dagar (tolv 30-da<PERSON><PERSON> månader).", "ad": "startdatum och slutdatum är de två datum för vilka du vill veta antalet mellanliggande dagar.!startdatum och slutdatum är de två datum för vilka du vill veta antalet mellanliggande dagar.!är ett logiskt värde som anger beräkningsmetoden: U.S. (NASD) = FALSKT eller utelämnad; Europeisk = SANT."}, "EDATE": {"a": "(start<PERSON>tum; månader)", "d": "<PERSON><PERSON><PERSON> se<PERSON>ret till det datum som ligger angivet antal månader innan eller efter startdatumet", "ad": "är ett datumserienummer som representerar startdatum!är antalet månader före eller efter startdatumet"}, "EOMONTH": {"a": "(start<PERSON>tum; månader)", "d": "Returnerar ett serienummer till den sista dagen i månaden före eller efter ett angivet antal månader", "ad": "är ett datumserienummer som representerar startdatum!är antalet månader före eller efter startdatumet"}, "HOUR": {"a": "(serienummer)", "d": "<PERSON><PERSON><PERSON> timmen som ett tal mellan 0 (12:00) och 23 (11:00).", "ad": "är ett tal i datum-tidkoden som används av Spreadsheet Editor, eller text i tidsformat, till exempel 16:48:00"}, "ISOWEEKNUM": {"a": "(date)", "d": "<PERSON><PERSON><PERSON> (ISO) för ett visst datum", "ad": "är datum/tid-koden som används i Spreadsheet Editor för datum- och tidsberäkningar"}, "MINUTE": {"a": "(serienummer)", "d": "<PERSON><PERSON><PERSON> minuten, ett tal från 0 till 59.", "ad": "är ett tal i datum-tidkoden som används av Spreadsheet Editor eller text i tidsformat, till exempel 16:48:00"}, "MONTH": {"a": "(serienummer)", "d": "<PERSON><PERSON><PERSON>, ett tal mellan 1 (januari) och 12 (december).", "ad": "är ett tal i datum-tidkoden som används av Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(startdatum; stoppdatum; [lediga])", "d": "<PERSON><PERSON><PERSON> antalet hela arb<PERSON>gar mellan två datum", "ad": "är ett datumserienummer som representerar startdatum!är ett datumserienummer som representerar slutdatum!är en valfri mängd av en eller flera seriedatumnummer som inte ska räknas som arbetsdagar, t.ex. högtider."}, "NETWORKDAYS.INTL": {"a": "(startdatum; stoppdatum; [helg]; [lediga])", "d": "<PERSON><PERSON>r antalet hela arb<PERSON>gar mellan två datum med egna helgparametrar.", "ad": "är ett datumserienummer som representerar startdatum.!är ett datumserienummer som representerar slutdatum.!är ett nummer eller en sträng som anger när helger infaller.!är en valfri mängd om en eller flera datumserienummer som inte ska räknas som arbetsdagar, till exempel högtider."}, "NOW": {"a": "()", "d": "<PERSON><PERSON>r dagens datum och aktuell tid formaterat som datum och tid.", "ad": ""}, "SECOND": {"a": "(serienummer)", "d": "<PERSON><PERSON><PERSON>, ett tal från 0 till 59.", "ad": "är ett tal i datum-tidkoden som används av  Spreadsheet Editor eller text i tidsformat, till exempel 16:48:23"}, "TIME": {"a": "(timme; minut; sekund)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> timmar, minuter och sekunder angivna som tal till serienummer, formaterade med tidsformat.", "ad": "är ett tal mellan 0 och 23 som representerar timmar.!är ett tal mellan 0 och 59 som representerar minuter.!är ett tal mellan 0 och 59 som representerar sekunder."}, "TIMEVALUE": {"a": "(tidstext)", "d": "Konverterar en texttid till ett serienummer för en tid, ett tal från 0 (00:00:00) till  0,999988426 (23:59:59). Formatera talet med ett tidsformat när du angett formeln", "ad": "är en sträng som anger en tid i något av tidsformaten i Spreadsheet Editor (datuminformation i strängen ignoreras)"}, "TODAY": {"a": "()", "d": "Returnerar dagens datum formaterat som ett datum.", "ad": ""}, "WEEKDAY": {"a": "(serienummer; [returtyp])", "d": "Returnerar ett tal mellan 1 och 7 som identifierar veckodagen för ett datum.", "ad": "är ett tal som representerar ett datum.!är ett tal: för söndag=1 till lördag=7, använd 1; för måndag=1 till söndag=7, använd 2; för måndag=0 till söndag=6, använd 3."}, "WEEKNUM": {"a": "(tal; [returtyp])", "d": "Omvandlar ett serienummer till ett veckonummer", "ad": "är datum/tid-koden som används av Spreadsheet Editor för datum- och tidsberäkningar!är ett tal (1, 2 eller 3) som bestämmer typen på returvärdet"}, "WORKDAY": {"a": "(startdatum; dagar; [lediga])", "d": "<PERSON><PERSON>r serienumret till datumet före eller efter ett givet antal arbetsdagar", "ad": "är ett datumserienummer som representerar startdatum!är antalet icke-helg<PERSON>gar före eller efter startdatumet!är en valfri matris med en eller flera datumserienummer som ska uteslutas från arbetskalendern som till exempel nationella högtidsdagar"}, "WORKDAY.INTL": {"a": "(startdatum; dagar; [helg]; [lediga])", "d": "Returnerar datumets serienummer före eller efter ett angivet antal arbetsdagar med egna helgparametrar.", "ad": "är ett datumserienummer som representerar startdatum.!är antalet icke-helgdagar och icke lediga dagar före eller efter startdatum.!är ett nummer eller en sträng som anger när helger infaller.!är en frivillig matris med ett eller flera datumserienummer som inte ska räknas som arbetsdagar, till exempel högtider."}, "YEAR": {"a": "(serienummer)", "d": "<PERSON><PERSON><PERSON> <PERSON><PERSON> för ett datum, ett heltal mellan 1900-9999.", "ad": "är ett tal i datum-tid-koden som används av Spreadsheet Editor"}, "YEARFRAC": {"a": "(startdatum; stoppdatum; [bas])", "d": "Returnerar ett tal som representerar antal hela dagar av ett år mellan startdatum och stoppdatum ", "ad": "är ett datumserienummer som representerar startdatum!är ett datumserienummer som representerar slutdatum!är bastyp för antal dagar som används"}, "BESSELI": {"a": "(x; n)", "d": "Returnerar den modifierade Bessel-funktionen", "ad": "är värdet som du vill beräkna funktionen för!är ordningstalet för Bessel-funktionen"}, "BESSELJ": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON>", "ad": "är värdet som du vill beräkna funktionen för!är ordningstalet för Bessel-funktionen"}, "BESSELK": {"a": "(x; n)", "d": "Returnerar den modifierade Bessel-funktionen", "ad": "är värdet som du vill beräkna funktionen för!är ordningstalet för Bessel-funktionen"}, "BESSELY": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON>", "ad": "är värdet som du vill beräkna funktionen för!är ordningstalet för Bessel-funktionen"}, "BIN2DEC": {"a": "(tal)", "d": "Konverterar ett binärt tal till ett decimalt", "ad": "är det binära talet som du vill konvertera"}, "BIN2HEX": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett binärt tal till ett hexadecimalt", "ad": "är det binära talet som du vill konvertera!är det antal tecken du vill använda"}, "BIN2OCT": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett binärt tal till ett oktalt", "ad": "är det binära talet som du vill konvertera!är det antal tecken du vill använda"}, "BITAND": {"a": "(tal1; tal2)", "d": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>' bitvis för två tal.", "ad": "är det binära talet i decimalform som du vill beräkna.!är det binära talet i decimalform som du vill beräkna."}, "BITLSHIFT": {"a": "(tal; flytta_antal)", "d": "Return<PERSON>r ett tal som flyttats till vänster med angivet flytta_antal bitar.", "ad": "är decimalformen för det binära talet som du vill beräkna.!är antalet bitar du vill flytta talet till vänster med."}, "BITOR": {"a": "(tal1; tal2)", "d": "<PERSON><PERSON><PERSON> '<PERSON><PERSON>' bitvis för två tal.", "ad": "är det binära talet i decimalform som du vill beräkna.!är det binära talet i decimalform som du vill beräkna."}, "BITRSHIFT": {"a": "(tal; flytta_antal)", "d": "<PERSON><PERSON>r ett tal som flyttats till höger med angivet flytta_antal bitar.", "ad": "är decimalformen för det binära talet som du vill beräkna.!är antalet bitar du vill flytta talet till höger med."}, "BITXOR": {"a": "(tal1; tal2)", "d": "Returnerar '<PERSON>k<PERSON><PERSON><PERSON> eller' bitvis för två tal.", "ad": "är det binära talet i decimalform som du vill beräkna.!är det binära talet i decimalform som du vill beräkna."}, "COMPLEX": {"a": "(realdel; imaginärdel; [suffix])", "d": "Konverterar en real- och en imaginärkoefficient till ett komplext tal", "ad": "är den reella koefficienten till det komplexa talet!är den imaginära koefficienten till det komplexa talet!är suffixet till den imaginära komponenten i det komplexa talet"}, "CONVERT": {"a": "(tal; ursprungsenhet; ny_enhet)", "d": "Konverterar ett tal från en enhet till en annan", "ad": "är värdet i ursprungsenheten som ska konverteras!är enheten för talet!är enheten för resultatet"}, "DEC2BIN": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett decimalt tal till ett binärt", "ad": "är det decimala heltal som du vill konvertera!är det antal tecken som används"}, "DEC2HEX": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett decimalt tal till ett hexadecimalt", "ad": "är det decimala heltal som du vill konvertera!är det antal tecken som används"}, "DEC2OCT": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett decimalt tal till oktalt", "ad": "är det decimala heltal som du vill konvertera!är det antal tecken du vill använda"}, "DELTA": {"a": "(tal1; [tal2])", "d": "Testar om två värden är lika", "ad": "är det första talet!är det andra talet"}, "ERF": {"a": "(undre_gräns; [övre_gräns])", "d": "<PERSON><PERSON><PERSON> f<PERSON>en", "ad": "är den undre integrationsgränsen för ERF!är den övre integrationsgränsen för ERF"}, "ERF.PRECISE": {"a": "(X)", "d": "<PERSON><PERSON><PERSON> f<PERSON>en", "ad": "är den undre integrationsgränsen för FELF.EXAKT"}, "ERFC": {"a": "(x)", "d": "Returnerar den komplimenterande felfunktionen", "ad": "är den undre integrationsgränsen för ERFC"}, "ERFC.PRECISE": {"a": "(X)", "d": "Returnerar den komplimenterande felfunktionen", "ad": "är den undre integrationsgränsen för FELFK.EXAKT"}, "GESTEP": {"a": "(tal; [steg])", "d": "Testar om ett tal är större än ett gränsvärde", "ad": "är värdet att testa mot steg!är tröskelvärdet"}, "HEX2BIN": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett hexadecimalt tal till ett binärt", "ad": "är det hexadecimala tal som du vill konvertera!är antalet tecken som du vill använda"}, "HEX2DEC": {"a": "(tal)", "d": "Konverterar ett hexadecimalt tal till ett decimalt", "ad": "är det hexadecimala talet som du vill konvertera"}, "HEX2OCT": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett hexadecimalt tal till ett oktalt", "ad": "är det hexadecimala tal som du vill konvertera!är antalet tecken som du vill använda"}, "IMABS": {"a": "(ital)", "d": "Returnerar absolutvärdet av ett komplext tal", "ad": "är ett komplext tal som du vill veta absolutvärdet för"}, "IMAGINARY": {"a": "(ital)", "d": "Returnerar den imaginära koefficienten av ett komplext tal", "ad": "är ett komplext tal som du vill veta den imaginära koefficienten för"}, "IMARGUMENT": {"a": "(ital)", "d": "Returnerar argumentet theta, en vinkel uttryckt i radianer", "ad": "är ett komplext tal som du vill veta argumentet för"}, "IMCONJUGATE": {"a": "(ital)", "d": "Returnerar det komplexa konjugatet till ett komplext tal", "ad": "är ett komplext tal som du vill veta konjugatet för"}, "IMCOS": {"a": "(ital)", "d": "Returnerar cosinus av ett komplext tal", "ad": "är ett komplext tal som du vill veta cosinus för"}, "IMCOSH": {"a": "(tal)", "d": "Returnerar hyperbolisk cosinus för ett komplext tal.", "ad": "är ett komplext tal som du vill veta hyperbolisk cosinus för."}, "IMCOT": {"a": "(tal)", "d": "Returnerar cotangens för ett komplext tal.", "ad": "är ett komplext tal som du vill veta cotangensen för."}, "IMCSC": {"a": "(tal)", "d": "<PERSON><PERSON><PERSON> cosekant för ett komplext tal.", "ad": "är ett komplext tal som du vill veta cosekanten för."}, "IMCSCH": {"a": "(tal)", "d": "Returnerar hyperbolisk cosekant för ett komplext tal.", "ad": "är ett komplext tal som du vill veta hyperbolisk cosekant för."}, "IMDIV": {"a": "(ital1; ital2)", "d": "Returnerar kvoten av två komplexa tal", "ad": "är det komplexa talet i täljaren!är det komplexa talet i täljaren"}, "IMEXP": {"a": "(ital)", "d": "Returnerar e upphöjt till ett komplext tal", "ad": "är ett komplext tal som du vill upphöja e till"}, "IMLN": {"a": "(ital)", "d": "Returnerar den naturliga logaritmen av ett komplext tal", "ad": "är ett komplext tal som du vill veta den naturliga logaritmen för"}, "IMLOG10": {"a": "(ital)", "d": "Returnerar 10-logarit<PERSON> av ett komplext tal", "ad": "är ett komplext tal som du vill veta 10-logarit<PERSON> för"}, "IMLOG2": {"a": "(ital)", "d": "Returnerar 2-logaritmen av ett komplext tal", "ad": "är ett komplext tal som du vill veta 2-logarit<PERSON> för"}, "IMPOWER": {"a": "(ital; tal)", "d": "Returnerar ett komplext tal upphöjt till en heltalsexponent", "ad": "är ett komplext tal som du vill upphöja till en potens!är potensen som du vill upphöja det komplexa talet till"}, "IMPRODUCT": {"a": "(ital1; [ital2]; ...)", "d": "Returnerar produkten av 1 till 255 komplexa tal", "ad": "Ital1, Ital2,... är från 1 till 255 komplexa tal som du vill multiplicera."}, "IMREAL": {"a": "(ital)", "d": "Returnerar realkoefficienten av ett komplext tal", "ad": "är ett komplext tal som du vill veta realkoefficienten för"}, "IMSEC": {"a": "(tal)", "d": "<PERSON><PERSON><PERSON> sekant för ett komplext tal.", "ad": "är ett komplext tal som du vill veta sekanten för."}, "IMSECH": {"a": "(tal)", "d": "Returnerar hyperbolisk sekant för ett komplext tal.", "ad": "är ett komplext tal som du vill veta hyperbolisk sekant för."}, "IMSIN": {"a": "(ital)", "d": "Returnerar sinus av ett komplext tal", "ad": "är ett komplext tal som du vill veta sinus för"}, "IMSINH": {"a": "(tal)", "d": "Returnerar hyperbolisk sinus för ett komplext tal.", "ad": "är ett komplext tal som du vill veta hyperbolisk sinus för."}, "IMSQRT": {"a": "(ital)", "d": "Returnerar kvadratroten av ett komplext tal", "ad": "är ett komplext tal som du vill veta kvadratroten för"}, "IMSUB": {"a": "(ital1; ital2)", "d": "<PERSON><PERSON><PERSON> differensen mellan två komplexa tal", "ad": "är det komplexa tal som du vill subtrahera ital2 ifrån!är det komplexa tal som du subtraherar från ital1"}, "IMSUM": {"a": "(ital1; [ital2]; ...)", "d": "<PERSON><PERSON>r summan av komplexa tal", "ad": "är från 1 till 255 komplexa tal som du vill addera"}, "IMTAN": {"a": "(tal)", "d": "Returnerar tangens för ett komplext tal.", "ad": "är ett komplext tal som du vill veta tangensen för."}, "OCT2BIN": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett oktalt tal till ett binärt", "ad": "är det oktala tal som du vill konvertera!är det antal tecken du vill använda"}, "OCT2DEC": {"a": "(tal)", "d": "Konverterar ett oktalt tal till ett decimalt", "ad": "är det oktala tal som du vill konvertera"}, "OCT2HEX": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett oktalt tal till ett hexadecimalt", "ad": "är det oktala tal som du vill konvertera!är det antal tecken du vill använda"}, "DAVERAGE": {"a": "(databas; fält; villkorsområde)", "d": "Beräknar medelvärdet för en kolumns värden i en lista eller databas enligt de villkor du angivit.", "ad": "är ett område med celler som utgör listan eller databasen. En databas är en lista med relaterade data.!är antingen kolumnens etikett inom citattecken eller ett tal som representerar kolumnens position i listan.!är cellområdet som innehåller de villkor du anger. Området inkluderar en kolumnetikett och en cell, under etike<PERSON>, för ett villkor."}, "DCOUNT": {"a": "(databas; fält; villkorsområde)", "d": "Räknar antalet celler i fältet (kolumnen) med poster i databasen som innehåller tal som matchar de villkor du angivit.", "ad": "är det område med celler som utgör listan eller databasen. En databas är en lista med relaterade data.!är antingen kolumnens etikett mellan citationstecken eller ett tal som representerar kolumnens position i listan.!är cellområdet som innehåller de villkor du anger. Området inkluderar en kolumnetikett och en cell, under etike<PERSON>, för ett villkor."}, "DCOUNTA": {"a": "(databas; fält; villkorsområde)", "d": "R<PERSON>k<PERSON> icketomma celler i fältet (kolumnen) med poster i databasen som matchar villkoren du anger.", "ad": "är det område med celler som utgör databasen. En databas är en lista med relaterade data.!är antingen kolumnens etikett inom citattecken eller ett tal som representerar kolumnens position i listan.!är cellområdet som innehåller de villkor du anger. Området inkluderar en kolumnetikett och en cell, under etiketten, för ett villkor."}, "DGET": {"a": "(databas; fält; villkorsområde)", "d": "Tar fram en enda post ur en databas enligt de villkor du anger.", "ad": "är det område med celler som utgör databasen. en databas är en lista med relaterade data.!är antingen kolumnens etikett inom citattecken eller ett tal som representerar kolumnens position i listan.!är cellområdet som innehåller de villkor du anger. Området inkluderar en kolumnetikett och en cell, under etiketten, för ett villkor."}, "DMAX": {"a": "(databas; fält; villkorsområde)", "d": "<PERSON><PERSON>r det största talet i ett fält (en kolumn) med poster, i från databasen, som stämmer överens med de villkor du angav.", "ad": "är det område med celler som utgör databasen. En databas är en lista med relaterade data.!är antingen kolumnens etikett inom citattecken eller ett tal som representerar kolumnens position i listan.!är cellområdet som innehåller de villkor du anger. Området inkluderar en kolumnetikett och en cell, under etiketten, för ett villkor."}, "DMIN": {"a": "(databas; fält; villkorsområde)", "d": "<PERSON><PERSON>r det minsta talet i ett fält (kolumn) med poster, i från databasen, som stämmer överens med de villkor du angav.", "ad": "är det område med celler som utgör databasen. En databas är en lista med relaterade data.!är antingen kolumnens etikett inom citattecken eller ett tal som representerar kolumnens position i listan.!är cellområdet som innehåller de villkor du anger. Området inkluderar en kolumnetikett och en cell, under etiketten, för ett villkor."}, "DPRODUCT": {"a": "(databas; fält; villkorsområde)", "d": "Multiplicerar värdena i fältet (kolumnen), med poster, i databasen som matchar de villkor du angav.", "ad": "är det område med celler som utgör databasen. En databas är en lista med relaterade data.!är antingen kolumnens etikett inom citattecken eller ett tal som representerar kolumnens position i listan.!är cellområdet som innehåller de villkor du anger. Området inkluderar en kolumnetikett och en cell, under etiketten, för ett villkor."}, "DSTDEV": {"a": "(databas; fält; villkorsområde)", "d": "Uppskattar standardavvikelsen baserad på ett sampel från valda databasposter.", "ad": "är det område med celler som utgör databasen. En databas är en lista med relaterade data.!är antingen kolumnens etikett inom citattecken eller ett tal som representerar kolumnens position i listan.!är cellområdet som innehåller de villkor du anger. Området inkluderar en kolumnetikett och en cell, under etiketten, för ett villkor."}, "DSTDEVP": {"a": "(databas; fält; villkorsområde)", "d": "Beräknar standardavvikelsen baserad på hela populationen av valda databasposter.", "ad": "är ett område med celler som utgör listan eller databasen. En databas är en lista med relaterade data.!är antingen kolumnens etikett inom citattecken eller ett tal som representerar kolumnens position i listan.!är cellområdet som innehåller de villkor du anger. Området inkluderar en kolumnetikett och en cell, under etike<PERSON>, för ett villkor."}, "DSUM": {"a": "(databas; fält; villkorsområde)", "d": "Adderar talen i fältet (kolumnen) av poster i en databas som matchar villkoren du anger.", "ad": "är ett område med celler som utgör databasen. En databas är en lista med relaterade data.!är antingen kolumnens etikett inom citattecken eller ett tal som representerar kolumnens position i listan.!är cellområdet som innehåller de villkor du anger. Området inkluderar en kolumnetikett och en cell, under etiketten, för ett villkor."}, "DVAR": {"a": "(databas; fält; villkorsområde)", "d": "Uppskattar variansen baserad på ett sampel från valda databasposter.", "ad": "är ett område med celler som utgör databasen. En databas är en lista med relaterade data.!är antingen kolumnens etikett inom citattecken eller ett tal som representerar kolumnens position i listan.!är cellområdet som innehåller de villkor du anger. Området inkluderar en kolumnetikett och en cell, under etiketten, för ett villkor."}, "DVARP": {"a": "(databas; fält; villkorsområde)", "d": "Beräknar variansen på hela populationen av valda databasposter.", "ad": "är ett område med celler som utgör databasen. En databas är en lista med relaterade data.!är antingen kolumnens etikett inom citattecken eller ett tal som representerar kolumnens position i listan.!är cellområdet som innehåller de villkor du anger. Området inkluderar en kolumnetikett och en cell, under etiketten, för ett villkor."}, "CHAR": {"a": "(tal)", "d": "<PERSON><PERSON>r tecknet som anges av koden från datorns teckenuppsättning.", "ad": "är ett tal mellan 1 och 255 som anger vilket tecken du vill ha."}, "CLEAN": {"a": "(text)", "d": "Tar bort alla icke-utskrivbara tecken från texten.", "ad": "är information från ett kalkylblad som du vill ta bort icke-utskrivbara tecken från."}, "CODE": {"a": "(text)", "d": "Returnerar en numerisk kod för det första tecknet i en textsträng, i teckenuppsättningen som används av din dator.", "ad": "är texten vars första tecken returneras som kod."}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Sammanfogar flera textsträngar till en.", "ad": "är 1 till 255 textsträngar som ska sammanfogas till en enda textsträng och kan vara textsträngar, tal eller cellreferenser till en enskild cell."}, "CONCAT": {"a": "(text1; ...)", "d": "Sammanfogar en lista eller ett intervall av textsträngar", "ad": "är 1 till 254 textsträ<PERSON>r eller intervall som ska sammanfogas till en enda textsträng"}, "DOLLAR": {"a": "(tal; [decimaler])", "d": "Konverterar ett tal till text med valutaformat.", "ad": "är ett tal, en referens till en cell som innehåller ett tal eller en formel som kan beräknas till ett tal.!är antalet siffror till höger om decimalkommat. Talet rundas av som det behövs; om det utelämnas, Decimaler = 2."}, "EXACT": {"a": "(text1; text2)", "d": "Kontrollerar om två textsträngar är exakt likadana och returnerar värdet SANT eller FALSKT. EXAKT är skifteslägeskänsligt.", "ad": "är den första textsträngen.!är den andra textsträngen."}, "FIND": {"a": "(sök; inom_text; [startpos])", "d": "Returnerar startpositionen för en textsträng inom en annan textsträng. SÖK är skiftlägeskänsligt.", "ad": "är texten du söker efter. Om du vill matcha de första bokstäverna i inom_text använder du dubbla citattecken (ingen text); jokertecken tillåts inte.!är den text som innehåller den text som du vill hitta.!anger vid vilken bokstav sökningen ska börja. Första bokstaven i inom_text är bokstav nummer 1. Om den utelämnas så är startpos = 1."}, "FINDB": {"a": "(sök; inom_text; [startpos])", "d": "<PERSON><PERSON><PERSON> efter en textsträng i en annan textsträng och returnerar numret på startpositionen för den första strängen från det första tecknet i den andra textsträngen, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska", "ad": "är texten du söker efter. Om du vill matcha de första bokstäverna i inom_text använder du dubbla citattecken (ingen text); jokertecken tillåts inte.!är den text som innehåller den text som du vill hitta.!anger vid vilken bokstav sökningen ska börja. Första bokstaven i inom_text är bokstav nummer 1. Om den utelämnas så är startpos = 1."}, "FIXED": {"a": "(tal; [decimaler]; [ej_komma])", "d": "<PERSON>dar av ett tal till det angivna antalet decimaler och returnerar resultatet som text med eller utan kommatecken.", "ad": "är talet som du vill runda av och konvertera till text.!är antalet siffror till höger om decimalkommat. Om det utelämnas, Decimaler = 2.!är ett logiskt värde: visa inte kommatecken i returnerad text = SANT; visa kommatecken i returnerad text = FALSKT eller utelämnad."}, "LEFT": {"a": "(text; [antal_tecken])", "d": "<PERSON><PERSON><PERSON> det angivna antalet tecken från början av en textsträng.", "ad": "är den textsträng som innehåller tecknen du vill ha fram.!anger hur många tecken du vill att VÄNSTER ska extrahera; 1 om det utelämnas."}, "LEFTB": {"a": "(text; [antal_tecken])", "d": "Returnerar det första tecknet eller tecknen i en textsträng, baserat på antal angivna byte, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska", "ad": "är den textsträng som innehåller tecknen du vill ha fram.!anger hur många tecken du vill att LEFTB ska extrahera; 1 om det utelämnas."}, "LEN": {"a": "(text)", "d": "Return<PERSON>r antalet tecken i en textsträng.", "ad": "är den text vars längd du vill veta. Blanksteg räknas som tecken."}, "LENB": {"a": "(text)", "d": "Returnerar antalet byte som används för att representera tecknen i en textsträng, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska", "ad": "är den text vars längd du vill veta. Blanksteg räknas som tecken."}, "LOWER": {"a": "(text)", "d": "Konverterar samtliga bokstäver i en text till gemener.", "ad": "är textsträngen som du vill konvertera till gemener. Tecken i texten som inte är bokstäver påverkas inte."}, "MID": {"a": "(text; startpos; antal_tecken)", "d": "<PERSON><PERSON>r tecknen från mitten av en textsträng med en startposition och längd som du anger.", "ad": "är texten som innehåller de tecken du vill ta fram.!är positionen för det första tecknet du vill ta fram. Första tecknet i Texten är 1.!anger hur många tecken som ska returneras från texten."}, "MIDB": {"a": "(text; startpos; antal_tecken)", "d": "Returnerar ett angivet antal tecken ur en textsträng med bö<PERSON><PERSON> från en angiven position, baserat på antalet byte du anger, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska", "ad": "är texten som innehåller de tecken du vill ta fram.!är positionen för det första tecknet du vill ta fram. Första tecknet i Texten är 1.!anger hur många tecken som ska returneras från texten."}, "NUMBERVALUE": {"a": "(text; [decimaltecken]; [tusentalsavgränsare])", "d": "Konverterar text till ett tal oberoende av språk.", "ad": "är den sträng som innehåller talet du vill konvertera.!är det tecken som används som decimaltecken i strängen.!är det tecken som används som tusentalsavgränsare i strängen."}, "PROPER": {"a": "(text)", "d": "Konverterar en textsträng: ändrar första bokstaven i varje ord till versal och konverterar alla andra bokstäver till gemener", "ad": "är en text innesluten i citattecken, en formel som returnerar text eller en referens till en cell som innehåller text som delvis ska ha versaler"}, "REPLACE": {"a": "(gammal_text; startpos; antal_tecken; ny_text)", "d": "<PERSON><PERSON><PERSON><PERSON> del av textsträng med en annan.", "ad": "är texten som du vill ersätta vissa tecken i.!är positionen för det tecken i Gammal_text som du vill ersätta med Ny_text.!är antalet tecken i Gammal_text som du vill ersätta.!är texten som ersätter tecken i Gammal_text."}, "REPLACEB": {"a": "(gammal_text; startpos; antal_tecken; ny_text)", "d": "<PERSON><PERSON><PERSON><PERSON> en del av en textsträng, baserat på det antal tecken du anger, med en annan textsträng, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska", "ad": "är texten som du vill ersätta vissa tecken i.!är positionen för det tecken i Gammal_text som du vill ersätta med Ny_text.!är antalet tecken i Gammal_text som du vill ersätta.!är texten som ersätter tecken i Gammal_text."}, "REPT": {"a": "(text; antal_gånger)", "d": "Upprepar en text ett bestämt antal gånger. Använd REP för att fylla en cell med samma textsträng flera gånger.", "ad": "är texten som du vill upprepa.!är ett positivt tal som anger antal gånger texten ska upprepas."}, "RIGHT": {"a": "(text; [antal_tecken])", "d": "<PERSON><PERSON>r det angivna antalet tecken från slutet av en textsträng.", "ad": "är textsträngen som innehåller de tecken du vill ta fram.!anger hur många tecken du vill ta fram, 1 om utelämnat."}, "RIGHTB": {"a": "(text; [antal_tecken])", "d": "Return<PERSON>r det eller de sista tecknen i en texts<PERSON>äng, baserat på det antal byte du anger, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska", "ad": "är textsträngen som innehåller de tecken du vill ta fram.!anger hur många tecken du vill ta fram, 1 om utelämnat."}, "SEARCH": {"a": "(sök; inom; [startpos])", "d": "Returnerar antalet tecken vilka ett givet tecken eller textsträng söker efter först, läser från höger till vänster (ej skiftlägeskänslig).", "ad": "är texten som du vill hitta. Du kan använda jokertecknen ? och *; använd ~? och ~* om du vill hitta tecknen ? och *.!är texten som du vill söka efter i söktext.!är antalet tecken i inom_text, från vänster räknat, som du vill starta sökning vid. Om den utelämnas används 1."}, "SEARCHB": {"a": "(sök; inom; [startpos])", "d": "<PERSON><PERSON><PERSON> efter en textsträng i en annan textsträng och returnerar numret på startpositionen för den första strängen från det första tecknet i den andra textsträngen, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska", "ad": "är texten som du vill hitta. Du kan använda jokertecknen ? och *; använd ~? och ~* om du vill hitta tecknen ? och *.!är texten som du vill söka efter i söktext.!är antalet tecken i inom_text, från vänster räknat, som du vill starta sökning vid. Om den utelämnas används 1."}, "SUBSTITUTE": {"a": "(text; gammal_text; ny_text; [antal_förekomster])", "d": "<PERSON><PERSON><PERSON><PERSON> gammal text med ny text i en textsträng.", "ad": "är en text eller en referens till en cell med textinnehåll som du vill ersätta tecken i.!är den befintliga texten som du vill ersätta. Om gammal_text inte stämmer överens med text kommer BYT.UT att ersätta texten.!är texten som du vill ersätta gammal_text med.!anger vilken förekomst av gammal_text som du vill ersätta. Om utelämnad ersätts alla förekomster av gammal_text."}, "T": {"a": "(värde)", "d": "Kontrollerar om ett värde är text och returnerar texten om det är det, annars returneras dubbla citattecken (ingen text).", "ad": "är värdet som ska testas."}, "TEXT": {"a": "(värde; format)", "d": "Konverterar ett värde till text i ett specifikt talformat.", "ad": "är ett tal, en formel som kan beräknas till ett numerisk värde eller en referens till en cell som innehåller ett numeriskt värde.!är ett tal i textformat från Kategori-rutan under Tal-fliken i dialogrutan Formatera celler"}, "TEXTJOIN": {"a": "(avg<PERSON><PERSON><PERSON><PERSON>; ignorera_tom; text1; ...)", "d": "En avgränsare används för att sammanfoga en lista eller ett intervall av textsträngar", "ad": "<PERSON><PERSON>n eller sträng som infogas mellan varje textobjekt!om SANT(standard), ignorerar tomma celler!är 1 till 252 textsträngar eller intervall som ska sammanfogas"}, "TRIM": {"a": "(text)", "d": "Tar bort alla blanksteg från en textsträng förutom enkla blanksteg mellan ord.", "ad": "är texten som du vill ta bort blanksteg från."}, "UNICHAR": {"a": "(tal)", "d": "Returnerar Unicode-tecknet som det numeriska värdet refererar till.", "ad": "är det Unicode-tal som motsvarar tecknet."}, "UNICODE": {"a": "(text)", "d": "<PERSON><PERSON><PERSON> talet (kodpunkten) som motsvarar det första tecknet i texten.", "ad": "är tecknet som du vill ha Unicode-värdet för."}, "UPPER": {"a": "(text)", "d": "Konverterar text till versaler.", "ad": "är texten som du vill konvertera till versaler. Kan vara en referens eller en textsträng."}, "VALUE": {"a": "(text)", "d": "Konverterar en textsträng som representerar ett tal till ett tal", "ad": "är en text inom citattecken eller en referens till en cell som innehåller texten som du vill konvertera."}, "AVEDEV": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar medelvärdet från de absoluta avvikelsernas datapunkter från deras medelvärde. Argument kan vara tal eller namn, matriser eller referenser som innehåller tal.", "ad": "är 1 till 255 argument som du vill beräkna medelvärdet för från de absoluta avvikelserna."}, "AVERAGE": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON>r medelvärdet av argumenten som kan vara tal, namn, matriser eller referenser som innehåller tal.", "ad": "<PERSON>r mellan 1 och 255 numeriska argument som du vill ha medelvärdet av."}, "AVERAGEA": {"a": "(värde1; [värde2]; ...)", "d": "Returnerar medelvärdet (aritmetiskt medelvärde) av argumenten, ber<PERSON><PERSON><PERSON> text och FALSKT i argument som 0; SANT utvärderas som 1. Argument kan vara tal, namn, matriser eller referenser.", "ad": "är mellan 1 och 255 argument som du vill ha medelvärdet för."}, "AVERAGEIF": {"a": "(o<PERSON>r<PERSON><PERSON>; vill<PERSON>; [medel<PERSON><PERSON><PERSON><PERSON>])", "d": "Hittar medelvärdet (aritmetiskt medelvärde) för de celler som anges av en given uppsättning villkor", "ad": "är cellområdet som du vill värdera!är villkoret i form av ett tal, uttry<PERSON> eller text som definierar vilka celler som används för att hitta medelvärdet!är de celler som används för att hitta medelvärdet. Om de utelämnas används cellerna i området"}, "AVERAGEIFS": {"a": "(medel<PERSON><PERSON><PERSON><PERSON>; villkor<PERSON>mr<PERSON>de; villkor; ...)", "d": "Hittar medelvärdet (aritmetiskt medelvärde) för de celler som anges av en given uppsättning villkor", "ad": "är de celler som används för att hitta medelvärdet.!är cellområdet som du vill beräkna för det specifika villkoret!är villkoret i form av ett tal, utt<PERSON><PERSON> eller text som definierar vilka celler som används för att hitta medelvärdet"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Returnera den kumulativa betafördelningsfunktionen.", "ad": "är värdet mellan A och B där funktionen ska beräknas!är en parameter till fördelningen och måste vara större än 0!är en parameter till fördelningen och måste vara större än 0!är ett frivilligt undre gränsvärde till intervallet för x. Om det utelämnas är A = 0.!är ett frivilligt övre gränsvärde till intervallet för x. Om det utelämnas är B = 1"}, "BETAINV": {"a": "(sannolikhet; alfa; beta; [A]; [B])", "d": "Returnera inversen till den kumulativa betafördelningsfunktionen (BETAFÖRD)", "ad": "är sannolikheten associerad med betafördelningen!är en parameter till fördelningen och måste vara större än 0!är en parameter till fördelningen och måste vara större än 0!är ett frivilligt undre gränsvärde till intervallet för x. Om det utelämnas är A = 0.!är ett frivilligt övre gränsvärde till intervallet för x. Om det utelämnas är B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; kumulativ; [A]; [B])", "d": "Returnerar funktionen för <PERSON>tsfördelning.", "ad": "är värdet mellan A och B där du vill beräkna funktionen.!är en parameter till fördelningen och måste vara större än 0.!är en parameter till fördelningen och måste vara större än 0.!är ett logiskt värde: använd SANT för den kumulativa fördelningsfunktionen och använd FALSKT för sannolikhetsfunktionen.!är en frivillig nedre gräns för intervallet x. Om den utelämnas är A = 0.!är en frivillig övre gräns för intervallet x. Om den utelämnas är B = 1."}, "BETA.INV": {"a": "(Sannolikhet; alfa; beta; [A]; [B])", "d": "Returnerar inversen till den kumulativa betafördelningsfunktionen (BETA.FÖRD).", "ad": "är sannolikheten som associeras med betafördelningen.!är en parameter till fördelningen och måste vara större än 0.!är en parameter till fördelningen och måste vara större än 0.!är en frivillig nedre gräns för intervallet x. Om den utelämnas är A = 0.!är en frivillig övre gräns för intervallet x. Om den utelämnas är B = 1."}, "BINOMDIST": {"a": "(antal_l; försök; sannolikhet_l; kumulativ)", "d": "Returnera den individuella binomialfördelningen", "ad": "är antalet lyckade försök!är antalet oberoende försök!är sannolikheten att lyckas för varje försök!är ett logiskt värde: använd SANT för den kumulativa fördelningsfunktionen och FALSKT för sannolikhetsfunktionen för massa"}, "BINOM.DIST": {"a": "(antal_l; försök; sannolikhet_l; kumulativ)", "d": "Returnerar den individuella binomialfördelningen.", "ad": "är antalet lyckade försök.!är antalet oberoende försök.!är sannolikheten att lyckas för varje försök.!är ett logiskt värde: använd SANT för den kumulativa fördelningsfunktionen och FALSKT för sannolikhetsfunktionen för massa."}, "BINOM.DIST.RANGE": {"a": "(f<PERSON><PERSON><PERSON><PERSON>; sannolikhet_l; antal_l; [antal_l2])", "d": "Return<PERSON>r sannolikheten för ett testresultat med en binomialfördelning.", "ad": "är antalet oberoende försök.!är sannolikheten att lyckas för varje försök.!är antalet lyckade försök.!den här funktionen returnerar sannolikheten för att antalet lyckade försök kommer att ligga mellan antal_l och antal_l2."}, "BINOM.INV": {"a": "(försök; sannolikhet_l; alfa)", "d": "Returnerar det minsta värdet för vilket den kumulativa binomialfördelningen är större än eller lika med ett villkorsvärde.", "ad": "är ant<PERSON>-försök.!är sannolikheten att lyckas för varje försök, ett tal mellan 0 och 1 inklusiv.!är villkorsvärdet, ett tal mellan 0 och 1 inklusiv."}, "CHIDIST": {"a": "(x; frihetsgrader)", "d": "Returnera den ensidiga sannolikheten av chi2-fördelningen", "ad": "är värdet där du vill beräkna fördelningen, ett icke-negativt tal!är antalet frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10"}, "CHIINV": {"a": "(sannolikhet; frihetsgrader)", "d": "Returnera inversen till chi2-fördelningen", "ad": "är sannolikheten associerad med chi2-f<PERSON><PERSON><PERSON><PERSON>, ett värde mellan 0 och 1 inklusiv!är antalet frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10"}, "CHITEST": {"a": "(observerat_omr; förväntat_omr)", "d": "Returnerar oberoendetesten: värdet från chi2-fördelningen för statistiken och lämpligt antal frihetsgrader", "ad": "är dataområdet som innehåller observationer att testa mot förväntade värden!är dataområdet som innehåller förhållandet mellan produkten av rad- och kolumnsummor och totalsumman"}, "CHISQ.DIST": {"a": "(x; frihetsgrad; kumulativ)", "d": "Returnerar den vänstersidiga sannolikheten för chi2-fördelningen.", "ad": "är värdet där du vill beräkna fördelningen, ett icke-negativt tal.!är antalet frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10.!är ett logiskt värde som funktionen ska returnera: kumulativa fördelningsfunktionen = SANT, sannolikhetsfunktionen = FALSKT."}, "CHISQ.DIST.RT": {"a": "(x; fri<PERSON>tsgrad)", "d": "Returnerar den högersidiga sannolikheten för chi2-fördelningen.", "ad": "är värdet där du vill beräkna fördelningen, ett icke-negativt tal.!är antalet frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10."}, "CHISQ.INV": {"a": "(sannolikhet; frihetsgrad)", "d": "Returnerar inversen till den vänstersidiga sannolikheten för chi2-fördelningen.", "ad": "är en sannolikhet som är associerad med chi2-fördelningen, ett värde mellan 0 och 1 inklusiv.!är antalet frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10."}, "CHISQ.INV.RT": {"a": "(sannolikhet; frihetsgrad)", "d": "Returnerar inversen till den högersidiga sannolikheten för chi2-fördelningen.", "ad": "är en sannolikhet som är associerad med chi2-fördelningen, ett värde mellan 0 och 1 inklusiv.!är antalet frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10."}, "CHISQ.TEST": {"a": "(observerat_omr; förväntat_omr)", "d": "Returnerar oberoendetesten: värdet från chi2-fördelningen för statistiken och lämpligt antal frihetsgrader.", "ad": "är dataområdet som innehåller observationer att testa mot förväntade värden.!är dataområdet som innehåller förhållandet mellan produkten av rad- och kolumnsummor och totalsumman."}, "CONFIDENCE": {"a": "(alfa; standardavvikelse; storlek)", "d": "Returnera konfidensintervallet för en populations medelvärde med en normalfördelning", "ad": "är signifikansnivån som används för att beräkna konfidensnivån, ett tal som är större än 0 och mindre än 1!är populationens standardavvikelse för dataområdet och den förväntas vara känd. Standardavvikelse måste vara större än 0.!är sampelstorleken"}, "CONFIDENCE.NORM": {"a": "(alfa; standardavvikelse; storlek)", "d": "Returnerar konfidensintervallet för en populations medelvärde, med normalfördelning.", "ad": "är signifikansnivån som används för att beräkna konfidensnivån, ett tal som är större än 0 och mindre än 1.!är populationens standardavvikelse för dataområdet och den förväntas vara känd. Standardavvikelse måste vara större än 0.!är sampelstorleken"}, "CONFIDENCE.T": {"a": "(alfa; standardavvikelse; storlek)", "d": "<PERSON><PERSON><PERSON> konfidensintervallet för en <PERSON> medel<PERSON>rde, med students T-fördelning", "ad": "är signifikansnivån som används för att beräkna konfidensnivån, ett tal som är större än 0 och mindre än 1!är populationens standardavvikelse för dataområdet och den förväntas vara känd. Standardavvikelse måste vara större än 0.!är sampelstorleken"}, "CORREL": {"a": "(matris1; matris2)", "d": "Returnerar korrelationskoefficienten mellan två datamängder.", "ad": "är ett cellområde med värden. Värdena ska vara tal, namn, matriser eller referenser som innehåller tal.!är ett andra cellområde med värden. Värdena ska vara tal, namn, matriser eller referenser som innehåller tal."}, "COUNT": {"a": "(värde1; [värde2]; ...)", "d": "Räk<PERSON> antalet celler i ett område som innehåller tal", "ad": "är mellan 1 och 255 argument som kan innehålla eller referera till olika datatyper men endast tal räknas."}, "COUNTA": {"a": "(värde1; [värde2]; ...)", "d": "Räk<PERSON> antalet celler i ett område som inte är tomma.", "ad": "är mellan 1 och 255 argument som representerar värden som du vill räkna. Värden kan vara vilken typ av information som helst."}, "COUNTBLANK": {"a": "(område)", "d": "<PERSON><PERSON><PERSON><PERSON> antal tomma celler i ett angivet område.", "ad": "är det område som du vill räkna antalet tomma celler i."}, "COUNTIF": {"a": "(omr<PERSON><PERSON>; villkor)", "d": "<PERSON><PERSON><PERSON><PERSON> antalet celler som motsvarar givet villkor i ett område.", "ad": "är det cellområde där du vill räkna antalet ej tomma celler.!är villkoret i form av ett tal, utt<PERSON><PERSON> eller text, som definierar vilka celler som kommer att räknas."}, "COUNTIFS": {"a": "(vill<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; vill<PERSON>; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> antalet celler som anges av en given uppsättning villkor", "ad": "är cellområdet som du vill beräkna för det specifika villkoret!är villkoret i form av ett tal, utt<PERSON><PERSON> eller text som definierar vilka celler som ska räknas"}, "COVAR": {"a": "(matris1; matris2)", "d": "<PERSON><PERSON><PERSON>, medelvärdet av produkterna av avvikelser för varje datapunktspar i två datamängder", "ad": "är det första cellområdet med heltal och måste vara tal, matriser eller referenser som innehåller tal!är det andra cellområdet med heltal och måste vara tal, matriser eller referenser som innehåller tal"}, "COVARIANCE.P": {"a": "(matris1; matris2)", "d": "Returnerar populationens kovarians, medelvärdet av produkterna av avvikelser för varje datapunktspar i två datamängder.", "ad": "är det första cellområdet med heltal och måste vara tal, matriser eller referenser som innehåller tal.!är det andra cellområdet med heltal och måste vara tal, matriser eller referenser som innehåller tal."}, "COVARIANCE.S": {"a": "(matris1; matris2)", "d": "Returnerar samplets kovarians, medelvärdet av produkterna av avvikelser för varje datapunktspar i två datamängder.", "ad": "är det första cellområdet med heltal och måste vara tal, matriser eller referenser som innehåller tal.!är det andra cellområdet med heltal och måste vara tal, matriser eller referenser som innehåller tal."}, "CRITBINOM": {"a": "(försök; sannolikhet_l; alfa)", "d": "Returnera det minsta värdet för vilket den kumulativa binomialfördelningen är större än eller lika med villkorsvärdet", "ad": "är antalet <PERSON>-försök!är sannolikheten att lyckas för varje försök, ett tal mellan 0 och 1 inkluderat!är villkorsvärdet, ett tal mellan 0 och 1 inkluderat"}, "DEVSQ": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar summan av kvadratavvikelserna av datapunkter från deras sampel medelvärde.", "ad": "<PERSON>r mellan 1 och 255 argument, matriser el<PERSON> matrisreferenser, på vilka du vill KVADAVV ska beräkna."}, "EXPONDIST": {"a": "(x; lambda; kumulativ)", "d": "Returnera exponentialfördelningen", "ad": "är värdet av funktionen, ett icke-negativt tal!är parametervärdet, ett positivt tal!är ett logiskt värde som funktionen ska returnera: den kumulativa fördelningsfunktionen = SANT; sannolikhetsfunktionen = FALSKT"}, "EXPON.DIST": {"a": "(x; lambda; kumulativ)", "d": "Returnerar exponentialfördelningen.", "ad": "är värdet av funktionen, ett icke-negativt tal.!är parametervärdet, ett positivt tal.!är ett logiskt värde som funktionen ska returnera: den kumulativa fördelningsfunktionen = SANT; sannolikhetsfunktionen = FALSKT."}, "FDIST": {"a": "(x; frihetsgrader1; frihetsgrader2)", "d": "Returnera den högersidiga F-sannolikhetsfördelningen (grad av skillnad) för två datamängder", "ad": "är värdet där du vill beräkna funktionen, ett icke-negativt tal!är täljarens antal frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10.!är nämnarens antal frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10"}, "FINV": {"a": "(sannolikhet; frihetsgrader1; frihetsgrader2)", "d": "Returnera inversen till den högersidiga F-sannolikhetsfördelningen: om p = FFÖRD(x,...), då FINV(p,...) = x.", "ad": "är sannolikheten associerad med den kumulativa F-fördelningen, ett tal mellan 0 och 1 inkluderat!är täljarens antal frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10!är nämnarens antal frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10"}, "FTEST": {"a": "(matris1; matris2)", "d": "Returnerar resultatet av ett F-test, den tvåsidiga sannolikheten att varianserna i Matris1 och Matris2 inte skiljer sig åt markant", "ad": "är den första matrisen eller det första dataområdet och kan vara tal, namn, matriser eller referenser som innehåller tal (tomma celler ignoreras)!är den andra matrisen eller det andra dataområdet och kan vara tal, namn, matriser eller referenser som innehåller tal (tomma celler ignoreras)"}, "F.DIST": {"a": "(x; frihetsgrad1; frihetsgrad2; kumulativ)", "d": "Returnerar den vänstersidiga F-sannolikhetsfördelningen (grad av skillnad) för två datamängder.", "ad": "är värdet där du vill beräkna funktionen, ett icke-negativt tal.!är täljarens frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10.!är nämnarens frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10.!är ett logiskt värde som funktionen ska returnera: den kumulativa fördelningsfunktionen = SANT, sannolikhetsfunktionen = FALSKT."}, "F.DIST.RT": {"a": "(x; frihetsgrad1; frihetsgrad2)", "d": "Returnerar den högersidiga F-sannolikhetsfördelningen (grad av skillnad) för två datamängder.", "ad": "är värdet där du vill beräkna funktionen, ett icke-negativt tal.!är täljarens frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10.!är nämnarens frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10."}, "F.INV": {"a": "(sannolikhet; frihetsgrad1; frihetsgrad2)", "d": "Returnerar inversen till den vänstersidiga F-sannolikhetsfördelningen: om p = F.FÖRD(x,...), då F.INV(p,...) = x.", "ad": "är en sannolikhet som är associerad med F-sannolikhetsfördelningen, ett tal mellan 0 och 1 inklusiv.!är täljarens frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10.!är nämnarens frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10."}, "F.INV.RT": {"a": "(sannolikhet; frihetsgrad1; frihetsgrad2)", "d": "Returnerar inversen till den högersidiga F-sannolikhetsfördelningen: om p = F.FÖRD.RT(x,...), då F.INV.RT(p,...) = x.", "ad": "är en sannolikhet som är associerad med F-sannolikhetsfördelningen, ett tal mellan 0 och 1 inklusiv.!är täljarens frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10.!är nämnarens frihetsgrader, ett tal mellan 1 och 10^10, exklusive 10^10."}, "F.TEST": {"a": "(matris1; matris2)", "d": "Returnerar resultatet av en F-test, den tvåsidiga sannolikheten att varianserna i Matris1 och Matris2 inte är markant olika.", "ad": "är den första matrisen eller dataområdet och kan vara tal, namn, matriser eller referenser som innehåller tal (tomma celler ignoreras).!är den andra matrisen eller dataområdet och kan vara tal, namn, matriser eller referenser som innehåller tal (tomma celler ignoreras)."}, "FISHER": {"a": "(x)", "d": "<PERSON><PERSON><PERSON>.", "ad": "är det värde som du vill ha <PERSON>en för, ett tal mellan -1 och 1, utom -1 och 1."}, "FISHERINV": {"a": "(y)", "d": "Returnerar inversen till Fisher-transformationen: om y = FISHER(x), då är FISHERINV(y) = x.", "ad": "är värdet som du vill utföra den inversa transformationen på."}, "FORECAST": {"a": "(x; kända_y; kända_x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> f<PERSON>, ett framtida värde längs en linjär trend genom att använda befintliga värden", "ad": "är datapunkten där du vill förutsäga värdet och det måste vara ett numeriskt värde!är den beroende matrisen eller området av numeriska data!är den underordnande matrisen eller området av numeriska data. Variansen av kända_x får inte vara noll"}, "FORECAST.ETS": {"a": "(m<PERSON><PERSON>tum; värden; tidslinje; [säsongsberoende]; [färdigställande_av_data]; [aggregering])", "d": "Returnerar det beräknade värdet för ett specifikt framtida måldatum med exponentiell utjämning.", "ad": "är datapunkten som ett värde förutsägs för via Spreadsheet Editor. Det bör baseras på mönster av värden på tidslinjen.!är matrisen eller intervallet med numeriska data som du förutsäger.! är den oberoende matrisen eller det oberoende intervallet med numeriska data. Det måste finnas ett regelbundet mellanrum mellan datumen på tidslinjen och det får inte vara noll.!är ett valfritt numeriskt värde som indikerar längden på det säsongsberoende mönstret. Standardvärdet 1 indikerar att säsongsberoende identifieras automatiskt.!är ett valfritt värde för hantering av saknade värden. Standardvärdet 1 ersätter saknade värden genom interpolation, och 0 ersätter dem med nollor.!är ett valfritt numeriskt värde för aggregering av flera värden med samma tidsstämpel. Om tomt beräknar Spreadsheet Editor medelvärdet av värdena."}, "FORECAST.ETS.CONFINT": {"a": "(m<PERSON><PERSON><PERSON>; värden; tidslinje; [konfidensnivå]; [säsongsberoende]; [färdigställande_av_data]; [aggregering])", "d": "Returnerar ett konfidensintervall för prognosvärdet vid angivet måldatum.", "ad": "är datapunkten som ett värde förutsägs för via Spreadsheet Editor. Det bör baseras på mönster av värden på tidslinjen.!är matrisen eller intervallet med numeriska data som du förutsäger.! är den oberoende matrisen eller det oberoende intervallet med numeriska data. Det måste finnas ett regelbundet mellanrum mellan datumen på tidslinjen och det får inte vara noll.!är ett tal mellan 0 och 1 som anger konfidensnivån för det beräknade konfidensintervallet. Standardvärdet är 0,95.!är ett valfritt numeriskt värde som indikerar längden på det säsongsberoende mönstret. Standardvärdet 1 indikerar att säsongsberoende identifieras automatiskt.!är ett valfritt värde för hantering av saknade värden. Standardvärdet 1 ersätter saknade värden genom interpolation, och 0 ersätter dem med nollor.!är ett valfritt numeriskt värde för aggregering av flera värden med samma tidsstämpel. Om tomt beräknar Spreadsheet Editor medelvärdet av värdena."}, "FORECAST.ETS.SEASONALITY": {"a": "(värden; tidslinje; [färdigställande_av_data]; [aggregering])", "d": "Returnerar längden på det repetitiva mönster som identifieras för de angivna tidsserierna.", "ad": "är matrisen eller intervallet med numeriska data du förutsäger.!är den oberoende matrisen eller det oberoende intervallet med numeriska data. Det måste finnas konsekventa steg mellan datumen på tidslinjen och de får inte vara noll.!är ett valfritt värde för hantering av saknade värden. Standardvärdet 1 ersätter saknade värden genom interpolation, och 0 ersätter dem med nollor.!är ett valfritt numeriskt värde för aggregering av flera värden med samma tidsstämpel. Om tomt beräknar Spreadsheet Editor medelvärdet av värdena."}, "FORECAST.ETS.STAT": {"a": "(värden; tidslinje; statistisk_typ; [säsongsberoende]; [färdigställande_av_data]; [aggregering])", "d": "<PERSON><PERSON><PERSON> begärd statistik för prognosen.", "ad": "är matrisen eller intervallet med numeriska data som du förutsäger.!är matrisen eller intervallet med numeriska data. Det måste finnas konsekventa steg mellan datumen på tidslinjen och de får inte vara noll.!är ett nummer mellan 1 och 8, som indikerar vilken statistik Spreadsheet Editor returnerar för den beräknade prognosen.! är ett valfritt numeriskt värde som indikerar längden på det säsongsberoende mönstret. Standardvärdet 1 indikerar att säsongsberoende identifieras automatiskt.!är ett valfritt värde för hantering av saknade värden. Standardvärdet 1 ersätter saknade värden genom interpolation, och 0 ersätter dem med nollor.!är ett valfritt numeriskt värde för aggregering av flera värden med samma tidsstämpel. Om tomt beräknar Spreadsheet Editor medelvärdet av värdena"}, "FORECAST.LINEAR": {"a": "(x; kända_y; kända_x)", "d": "Beräknar eller förutsäger ett framtida värde längs en linjär trendlinje genom att använda redan existerande värden.", "ad": "är datapunkten där du vill förutsäga värdet och måste vara ett numeriskt värde.!är den underordnade matrisen eller området med numeriska data.!är den oberoende matrisen eller området med numeriska data. Variansen av kända_x måste vara noll"}, "FREQUENCY": {"a": "(datamatris; fackmatris)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> hur ofta värden uppstår inom ett område med värden och returnerar en vertikal matris med tal som har ett element mer än en fackmatris.", "ad": "är en matris eller en referens till ett område med värden för vilka du vill ha beräkningsfrekvensen (mellanslag och text ignoreras).!är en matris eller en referens till intervaller i vilka du vill gruppera värdena i en datamatris."}, "GAMMA": {"a": "(x)", "d": "Returnerar värdet för gammafunktionen.", "ad": "är värdet som du vill beräkna gamma för."}, "GAMMADIST": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnerar gammafördelningen", "ad": "är värdet där du vill beräkna fördelningen, ett icke-negativt tal!är en parameter till fördelningen, ett positivt tal!är en parameter till fördelningen, ett positivt tal. Om beta = 1 returnerar GAMMAFÖRD standardgammafördelningen!är ett logiskt värde: returnera den kumulativa fördelningsfunktionen = SANT; returnera sannolikhetsfunktionen för massa = FALSKT eller utelämnad"}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnerar gammafördelningen.", "ad": "är värdet där du vill beräkna fördelningen, ett icke-negativt tal.!är en parameter till fördelningen, ett positivt tal.!är en parameter till fördelningen, ett positivt tal. Om beta = 1 returnerar GAMMA.FÖRD standardgammafördelningen.!är ett logiskt värde: returnera den kumulativa fördelningsfunktionen = SANT; returnera sannolikhetsfunktionen för massa = FALSKT eller utelämnad"}, "GAMMAINV": {"a": "(sannolikhet; alfa; beta)", "d": "Returnera inversen till den kumulativa gammafördelningen: om p = GAMMAFÖRD(x,...), då GAMMAINV(p,...) = x", "ad": "är sannolikheten associerad med gammafördelningen, ett tal mellan 0 och 1 inkluderat!är en parameter till fördelningen. ett positivt tal!är en parameter till fördelningen. Om beta = 1 returnerar GAMMAINV standardgammafördelningen"}, "GAMMA.INV": {"a": "(sannolikhet; alfa; beta)", "d": "Returnerar inversen till den kumulativa gammafördelningen: om p = GAMMA.FÖRD(x,...), då GAMMA.INV(p,...) = x.", "ad": "är sannolikheten associerad med gammafördelningen, ett tal mellan 0 och 1 inklusiv.!är en parameter till fördelningen, ett positivt tal.!är en parameter till fördelningen, ett positivt tal. Om beta = 1 returnerar GAMMA.INV inversen till standardgammafördelningen."}, "GAMMALN": {"a": "(x)", "d": "Returnerar den naturliga logaritmen för gammafunktionen.", "ad": "är värdet som du vill beräkna GAMMALN för, ett positivt tal."}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Returnerar den naturliga logaritmen för gammafunktionen.", "ad": "är värdet som du vill beräkna GAMMALN.EXAKT för, ett positivt tal."}, "GAUSS": {"a": "(x)", "d": "Returnerar 0,5 mindre än den kumulativa standardnormalfördelningen.", "ad": "är värdet där du vill beräkna fördelningen."}, "GEOMEAN": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar det geometriska medelvärdet av en matris eller av ett område positiva numeriska data.", "ad": "är från 1 till 255 tal, namn, matriser eller referenser som innehåller tal, vars medelvärde du vill beräkna."}, "GROWTH": {"a": "(kända_y; [kända_x]; [nya_x]; [konst])", "d": "Returnerar tal i en exponentiell tillväxttrend som matchar kända datapunkter", "ad": "är den mängd y-värden som är kända i förhållandet y = b*m^x, en matris eller ett område med positiva tal!är en alternativ mängd x-värden som är kända i förhållandet y = b*m^x, en matris eller ett område med samma storlek som kända_y!är de nya x-värden som du vill att EXPTREND ska returnera motsvarande y-värden för!är ett logiskt värde: konstanten b beräknas normalt om konst = SANT, och sätts till 1 om konst = FALSKT eller utelämnas"}, "HARMEAN": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar det harmoniska medelvärdet av ett dataområde med positiva tal: motsvarigheten av det aritmetiska medelvärdet av motsvarigheter.", "ad": "är mellan 1 och 255 tal eller namn, matriser eller referenser som innehåller tal, vars harmoniska medelvärde du vill beräkna."}, "HYPGEOM.DIST": {"a": "(sampel; antal_sampel; population; antal_population; kumulativ)", "d": "Returnerar den hypergeometriska fördelningen.", "ad": "är antal lyckade försök i samplet.!är samplets storlek.!är antal lyckade försök i populationen.!är populationens storlek.!är ett logiskt värde: använd SANT för den kumulativa fördelningsfunktionen, använd FALSKT för sannolikhetsfunktionen."}, "HYPGEOMDIST": {"a": "(sampel; antal_sampel; population; antal_population)", "d": "Returnera den hypergeometriska fördelningen", "ad": "är antal lyckade försök i samplet!är samplets storlek!är antal lyckade försök i populationen!är populationens storlek"}, "INTERCEPT": {"a": "(kända_y; kända_x)", "d": "Beräknar punkten där en linje korsar y-axeln genom att använda en regressionslinje ritad genom de kända x- och y-värdena", "ad": "är den beroende mängden observationer eller data och kan vara tal, namn, matriser eller referenser som innehåller tal!är den oberoende mängden observationer eller data och kan vara tal, namn, matriser eller referenser som innehåller tal"}, "KURT": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar en datamängds fördelning.", "ad": "är 1 till 255 tal, namn, matriser eller referenser som innehåller tal, vars fördelning du vill beräkna."}, "LARGE": {"a": "(matris; n)", "d": "Returnerar det n:te största värdet i en datamängd, t ex det femte största talet.", "ad": "är matrisen eller området med data som du vill bestämma det n:te största värdet ur.!är den position (från det största värdet) i matrisen eller dataområdet som ska returneras."}, "LINEST": {"a": "(kända_y; [kända_x]; [konst]; [statistik])", "d": "Returnerar statistik som beskriver en linjär trend som matchar kända datapunkter, genom att passa in en rak linje och använda minsta kvadrat-metoden", "ad": "är den mängd y-värden som är kända i förhållandet y = mx + b!är en valfri mängd x-värden som kanske redan är kända i förhållandet y = mx + b!är ett logiskt värde: konstanten b beräknas normalt om konst = SANT eller utesluten; konstanten b ska vara 0 om konst = FALSKT!är ett logiskt värde: returnera extra regressionsstatistik = SANT; returnera m-koefficienter och konstanten b = FALSKT eller utelämnad"}, "LOGEST": {"a": "(kända_y; [kända_x]; [konst]; [statistik])", "d": "Returnerar statistik som beskriver en exponentiell kurva som matchar kända datapunkter", "ad": "är den mängd y-värden som är kända i förhållandet y = b*m^x!är en frivillig mängd x-värden som är kända i förhållandet y = b*m^x!är ett logiskt värde: konstanten b beräknas normalt om konst = SANT eller utesluten; konstanten b ska vara 1 om konst = FALSKT!är ett logiskt värde: returnera extra regressionsstatistik = SANT; returnera m-koefficienten och konstanten b = FALSKT eller utelämnad"}, "LOGINV": {"a": "(sannolikhet; medelvärde; standardavvikelse)", "d": "Returnera inversen till den kumulativa lognormalfördelningsfunktionen av x, där ln(x) normalfördelas med parametrarna Medelvärde och Standardavvikelse", "ad": "är sannolikheten associerad med lognormalfördelningen, ett tal mellan 0 och 1 inklusiv!är medelvärdet av ln(x)!är standardavvikelsen för ln(x), ett positivt tal"}, "LOGNORM.DIST": {"a": "(x; medel<PERSON><PERSON><PERSON>; standardavvikelse; kumulativ)", "d": "Returnerar lognormalfördelningen av x, där ln(x) normalfördelas med parametrarna Medelvärde och Standardavvikelse.", "ad": "är värdet där du vill beräkna funktionen, ett positivt tal.!är medelvärdet av ln(x).!är standardavvikelsen av ln(x), ett positivt tal.!är ett logiskt värde: använd SANT för den kumulativa fördelningsfunktionen och använd FALSKT för sannolikhetsfunktionen."}, "LOGNORM.INV": {"a": "(sannolikhet; medelvärde; standardavvikelse)", "d": "Returnerar den kumulativa lognormalfördelningsfunktionen av x, där ln(x) normalfördelas med parametrarna Medelvärde och Standardavvikelse.", "ad": "är sannolikheten associerad med lognormalfördelningen, ett tal mellan 0 och 1 inklusiv.!är medelvärdet av ln(x).!är standardavvikelsen för ln(x), ett positivt tal."}, "LOGNORMDIST": {"a": "(x; medel<PERSON><PERSON><PERSON>; standardavvikelse)", "d": "Returnera den kumulativa lognormalfördelningen av x, där ln(x) normalfördelas med parametrarna Medelvärde och Standardavvikelse", "ad": "är värdet där du vill beräkna funktionen, ett positivt tal!är medelvärdet av ln(x)!är standardavvikelsen för ln(x), ett positivt tal"}, "MAX": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar det maximala värdet i en lista av argument. Ignorerar logiska värden och text.", "ad": "är mellan 1 och 255 tal, to<PERSON> celler, logiska värden eller texttal ur vilka du vill hitta det maximala värdet."}, "MAXA": {"a": "(värde1; [värde2]; ...)", "d": "Returnerar det högsta värdet i en mängd värden. Ignorerar inte logiska värden och text.", "ad": "<PERSON>r mellan 1 och 255, to<PERSON> celler, logiska värden eller texttal för vilka du vill ha maxvärdet."}, "MAXIFS": {"a": "(max_intervall; kriterie_intervall; kriterier; ...)", "d": "<PERSON><PERSON><PERSON> det högsta värdet bland celler som anges av en given uppsättning villkor eller kriterier", "ad": "celler som används för att fastställa det högsta värdet!är cellområdet som du vill beräkna för det specifika villkoret!är villkoret eller kriteriet i form av tal, uttry<PERSON> eller text som definierar vilka celler som ska tas med när det högsta värdet fastställs"}, "MEDIAN": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON><PERSON> medianen eller talet i mitten av de angivna talen.", "ad": "är mellan 1 och 255 tal eller namn, matriser eller referenser som innehåller tal ur vilka du vill veta medianen."}, "MIN": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar det minsta värdet i en lista av argument. Ignorerar logiska värden och text.", "ad": "är mellan 1 och 255 tal, to<PERSON> celler, logiska värden eller texttal ur vilka du vill veta minimivärdet."}, "MINA": {"a": "(värde1; [värde2]; ...)", "d": "Returnerar det lägsta värdet i en mängd värden. Ignorerar inte logiska värden och text.", "ad": "är mellan 1 och 255 tal, to<PERSON> celler, logiska värden eller texttal ur vilka du vill veta minimivärdet."}, "MINIFS": {"a": "(minsta_intervall; kriterie_intervall; kriterier; ...)", "d": "<PERSON><PERSON>r det minsta värdet bland celler som anges av en given uppsättning villkor eller kriterier", "ad": "celler som används för att fastställa det minsta värdet!är cellområdet som du vill utvärdera för det specifika villkoret!är villkoret eller kriteriet i form av tal, uttry<PERSON> eller text som definierar vilka celler som ska tas med när det minsta värdet fastställs"}, "MODE": {"a": "(tal1; [tal2]; ...)", "d": "Return<PERSON>r det vanligast förekommande eller mest repeterade värdet i en matris eller ett dataområde", "ad": "är 1 till 255 tal, namn, matriser eller referenser som innehåller tal som du vill beräkna typvärdet för"}, "MODE.MULT": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar en vertikal matris med de vanligast förekommande eller mest repeterade värdena i en matris eller ett dataområde. För en horisontell matris använder du =TRANSPONERA(TYPVÄRDE.FLERA(tal1,tal2,...)).", "ad": "är 1 till 255 tal, namn, matriser eller referenser som innehåller tal vars typvärde du vill beräkna."}, "MODE.SNGL": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar det vanligast förekommande eller mest repeterade värdet i en matris eller ett dataområde.", "ad": "är 1 till 255 tal, namn, matriser eller referenser som innehåller tal vars typvärde du vill beräkna."}, "NEGBINOM.DIST": {"a": "(antal_m; antal_l; sannolikhet_l; kumulativ)", "d": "Returnerar den negativa binomialfördelningen, sannolikheten att Antal_M försök ska misslyckas innan Antal_L lyckas, med Sannolikhet_Ls sannolikhet att lyckas.", "ad": "är antalet misslyckade försök.!är tröskelvärdet för antalet lyckade försök.!är sannolikheten att lyckas, ett tal mellan 0 och 1.!är ett logiskt värde: använd SANT för den kumulativa fördelningsfunktionen och använd FALSKT för sannolikhetsfunktionen."}, "NEGBINOMDIST": {"a": "(antal_m; antal_l; sannolikhet_l)", "d": "Returnera den negativa binomialfördelningen, sannolikheten att Antal_M försök ska misslyckas innan Antal_L lyckas, med Sannolikhet_Ls sannolikhet att lyckas.", "ad": "är antalet misslyckade försök.!är tröskelvärdet för antalet lyckade försök.!är sannolikheten att lyckas, ett tal mellan 0 och 1."}, "NORM.DIST": {"a": "(x; medel<PERSON><PERSON><PERSON>; standardavvikelse; kumulativ)", "d": "Returnerar normalfördelningen för det angivna medelvärdet och standardavvikelsen.", "ad": "är värdet där du vill beräkna fördelningen.!är det aritmetiska medelvärdet av fördelningen.!är standardavvikelsen för fördelningen, ett positivt tal.!är ett logiskt värde: använd SANT för den kumulativa fördelningsfunktionen och FALSKT för sannolikhetsfunktionen."}, "NORMDIST": {"a": "(x; medel<PERSON><PERSON><PERSON>; standardavvikelse; kumulativ)", "d": "Returnerar den kumulativa normalfördelningen för det angivna medelvärdet och standardavvikelsen", "ad": "är värdet där du vill beräkna fördelningen!är det aritmetiska medelvärdet av fördelningen!är standardavvikelsen för fördelningen, ett positivt tal!är ett logiskt värde: använd SANT för den kumulativa fördelningsfunktionen och FALSKT för sannolikhetsfunktionen"}, "NORM.INV": {"a": "(sannolikhet; medelvärde; standardavvikelse)", "d": "Returnerar inversen till den kumulativa normalfördelningen för det angivna medelvärdet och standardavvikelsen.", "ad": "är ett sannolikhetsvärde som motsvarar normalfördelningen, ett tal mellan 0 och 1 inklusiv.!är det aritmetiska medelvärdet av fördelningen.!är standardavvikelsen för fördelningen, ett positivt tal."}, "NORMINV": {"a": "(sannolikhet; medelvärde; standardavvikelse)", "d": "Returnerar inversen till den kumulativa normalfördelningen för det angivna medelvärdet och standardavvikelsen", "ad": "är ett sannolikhetsvärde som motsvarar normalfördelningen, ett tal mellan 0 och 1 inklusiv!är det aritmetiska medelvärdet av fördelningen!är standardavvikelsen för fördelningen, ett positivt tal"}, "NORM.S.DIST": {"a": "(z; kumulativ)", "d": "Returnerar standardnormalfördelningen (har ett medelvärde på noll och en standardavvikelse på ett).", "ad": "är värdet där du vill beräkna fördelningen.!är ett logiskt värde som funktionen ska returnera: den kumulativa fördelningsfunktionen = SANT, sannolikhetsfunktionen = FALSKT."}, "NORMSDIST": {"a": "(z)", "d": "Returnerar den kumulativa standardnormalfördelningen (har ett medelvärde på noll och en standardavvikelse på ett)", "ad": "är värdet där du vill beräkna fördelningen"}, "NORM.S.INV": {"a": "(sannolikhet)", "d": "Returnerar inversen till den kumulativa standardnormalfördelningen (har ett medelvärde på noll och en standardavvikelse på ett).", "ad": "är ett sannolikhetsvärde som motsvarar normalfördelningen, ett tal mellan 0 och 1 inklusiv."}, "NORMSINV": {"a": "(sannolikhet)", "d": "Returnerar inversen till den kumulativa standardnormalfördelningen (har ett medelvärde på noll och en standardavvikelse på ett)", "ad": "är ett sannolikhetsvärde som motsvarar normalfördelningen, ett tal mellan 0 och 1 inklusiv"}, "PEARSON": {"a": "(matris1; matris2)", "d": "<PERSON><PERSON><PERSON> k<PERSON>ficienten till <PERSON><PERSON>, r.", "ad": "är mängden av oberoende värden.!är mängden av beroende värden."}, "PERCENTILE": {"a": "(matris; n)", "d": "Returnerar den n:te percentilen av värden i ett område", "ad": "är matrisen eller dataområdet som definierar relativ position!är percentilvärdet mellan 0 och 1 inkluderat"}, "PERCENTILE.EXC": {"a": "(matris; n)", "d": "Returnerar den n:te percentilen av värden i ett område, där n är i intervallet 0..1 exklusiv.", "ad": "är matrisen eller dataområdet som definierar relativ position.!är percentilvärdet mellan 0 och 1 inklusiv."}, "PERCENTILE.INC": {"a": "(matris; n)", "d": "Returnerar den n:te percentilen av värden i ett område, där n är i intervallet 0..1 inklusiv.", "ad": "är matrisen eller dataområdet som definierar relativ position !är percentilvärdet mellan 0 och 1 inklusiv."}, "PERCENTRANK": {"a": "(matris; x; [signifikans])", "d": "Returnerar rangen för ett värde i en datamängd i procent av datamängden", "ad": "är matrisen eller dataområdet med numeriska värden som definierar relativ position!är det värde som du vill veta rangen för!är ett frivilligt värde som anger antal signifikanta siffror i det returnerade procentvärdet. Om det utelämnas används tre siffror (0,xxx %)"}, "PERCENTRANK.EXC": {"a": "(matris; x; [signifikans])", "d": "Returnerar rangen för ett värde i en datamängd som en andel i procent (0..1 exklusiv) av datamängden.", "ad": "är matrisen eller dataområdet med numeriska värden som definierar relativ position.!är det värde som du vill veta rangen för.!är ett frivilligt värde som anger antal signifikanta siffror i det returnerade procentvärdet. Om det utelämnas används tre siffror (0,xxx %)."}, "PERCENTRANK.INC": {"a": "(matris; x; [signifikans])", "d": "Returnerar rangen för ett värde i en datamängd som en andel i procent (0..1 inklusiv) av datamängden.", "ad": "är matrisen eller dataområdet med numeriska värden som definierar relativ position.!är det värde som du vill veta rangen för.!är ett frivilligt värde som anger antal signifikanta siffror i det returnerade procentvärdet. Om det utelämnas används tre siffror (0,xxx %)."}, "PERMUT": {"a": "(tal; valt_tal)", "d": "Returnerar antal permutationer för ett givet antal objekt som kan väljas från de totala antalet objekt.", "ad": "är det totala antalet objekt.!är antalet objekt i varje permutation."}, "PERMUTATIONA": {"a": "(tal; valt_tal)", "d": "Returnerar antal permutationer för ett givet antal objekt (med repetitioner) som kan väljas från det totala antalet objekt.", "ad": "är det totala antalet objekt.!är antalet objekt i varje permutation."}, "PHI": {"a": "(x)", "d": "Returnerar värdet för densitetsfunktionen för en standardnormalfördelning.", "ad": "är värdet där du vill beräkna densiteten för standardnormalfördelningen."}, "POISSON": {"a": "(x; medel<PERSON><PERSON><PERSON>; kumulativ)", "d": "<PERSON><PERSON><PERSON>-fördeln<PERSON>", "ad": "är antalet händelser!är det förväntade numeriska värdet, ett positivt tal!är ett logiskt värde: använd SANT för den kumulativa Poisson-sannolikheten och FALSKT för Poisson-sannolikhetsfunktionens massa"}, "POISSON.DIST": {"a": "(x; medel<PERSON><PERSON><PERSON>; kumulativ)", "d": "<PERSON><PERSON><PERSON>fördeln<PERSON>.", "ad": "är antalet händelser.!är det förväntade numeriska värdet, ett positivt tal.!är ett logiskt värde: använd SANT för den kumulativa Poisson-sannolikheten och FALSKT för Poisson-sannolikhetsfunktionens massa."}, "PROB": {"a": "(x_omr<PERSON><PERSON>; sannolikhetsområde; undre_gräns; [övre_gräns])", "d": "Return<PERSON>r sannolikheten att värden i ett område ligger mellan två gränser eller är lika med en lägre gräns.", "ad": "är det intervall med numeriska värden på x till vilka det finns motsvarande sannolikheter.!är ett antal sannolikheter som är associerade med värden i X-området, värden mellan 0 och 1 och exkluderat 0.!är det undre gränsvärdet på värdet som du vill ha sannolikheter till.!är ett frivilligt övre gränsvärde på värdena. Om utelämnad, så returnerar SANNOLIKHET sannolikheten att värdena i X_området är lika med undre_gräns."}, "QUARTILE": {"a": "(matris; k<PERSON><PERSON>)", "d": "Returnerar kvartilen av en datamängd", "ad": "är matrisen eller cellområdet med numeriska värden som du vill ha kvartilvärdet för!är ett tal: minimivärde = 0; 1:a kvartilen = 1; medianvärde = 2; 3:e kvartilen = 3; maxvärde = 4"}, "QUARTILE.INC": {"a": "(matris; k<PERSON><PERSON>)", "d": "Returnerar kvartilen av en datamängd, utifrån percentilvärden från 0..1 inklusiv.", "ad": "är matrisen eller cellområdet med numeriska värden som du vill ha kvartilvärdet för.!är ett tal: minimivärde = 0; 1:a kvartilen = 1; medianvärde = 2; 3:e kvartilen = 3; maximivärde = 4."}, "QUARTILE.EXC": {"a": "(matris; k<PERSON><PERSON>)", "d": "Returnerar kvartilen av en datamängd, utifrån percentilvärden från 0..1 exklusiv.", "ad": "är matrisen eller cellområdet med numeriska värden som du vill ha kvartilvärdet för.!är ett tal: minimivärde = 0; 1:a kvartilen = 1; medianvärde = 2; 3:e kvartilen = 3; maximivärde = 4."}, "RANK": {"a": "(tal; ref; [ordning])", "d": "Returnera rangordningen för ett tal i en lista med tal: dess storlek i relation till andra värden i listan", "ad": "är talet vars rang du vill veta!är en matris av eller en referens till en lista med tal. Icke-numeriska värden ignoreras!är ett tal: rangordning i listan som är sorterad i fallande ordning = 0 eller utelämnat; rangordning i listan som är sorterad i stigande ordning = vilket icke-nollvärde som helst"}, "RANK.AVG": {"a": "(tal; ref; [ordning])", "d": "Returnerar rangordningen för ett tal i en lista med tal: dess storlek i relation till andra värden i listan; om fler än ett värde har samma rang returneras medelvärdet.", "ad": "är talet vars rang du vill veta.!är en matris av eller en referens till en lista med tal. Icke-numeriska värden ignoreras.!är ett tal: rangordning i listan som är sorterad i fallande ordning = 0 eller utelämnat; rangordning i listan som är sorterad i stigande ordning = alla icke-nollvärden."}, "RANK.EQ": {"a": "(tal; ref; [ordning])", "d": "Returnerar rangordningen för ett tal i en lista med tal: dess storlek i relation till andra värden i listan; om fler än ett värde har samma rang returneras den högsta rangen i den mängden.", "ad": "är talet vars rang du vill veta.!är en matris av eller en referens till en lista med tal. Icke-numeriska värden ignoreras.!är ett tal: rangordning i listan som är sorterad i fallande ordning = 0 eller utelämnat; rangordning i listan som är sorterad i stigande ordning = alla icke-nollvärden."}, "RSQ": {"a": "(kända_y; kända_x)", "d": "Returnerar korrelationskoefficienten till Pearsons momentprodukt genom de givna datapunkterna", "ad": "är en matris eller ett område med datapunkter och kan vara tal, namn, matriser eller referenser som innehåller tal!är en matris eller ett område med datapunkter och kan vara tal, namn, matriser eller referenser som innehåller tal"}, "SKEW": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar snedheten i en fördelning, d.v.s. graden av asymmetri kring en fördelnings medelvärde.", "ad": "är från 1 till 255 tal, namn, matriser eller referenser som innehåller tal, vars snedhet du vill beräkna."}, "SKEW.P": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar snedheten i en fördelning baserat på en population, d.v.s. graden av asymmetri kring en fördelnings medelvärde.", "ad": "är från 1 till 254 tal, namn, matriser eller referenser som innehåller tal som du vill beräkna populationens snedhet för."}, "SLOPE": {"a": "(kända_y; kända_x)", "d": "Returnerar lutningen av en linjär regressionslinje genom de angivna datapunkterna", "ad": "är en matris eller ett cellområde med beroende numeriska datapunkter och kan vara tal, namn, matriser eller referenser som innehåller tal!är en mängd av oberoende datapunkter och kan vara tal, namn, matriser eller referenser som innehåller tal"}, "SMALL": {"a": "(matris; n)", "d": "Returnerar det n:te minsta värdet i en datamängd, t ex det femte minsta talet.", "ad": "är en matris eller ett område av numeriska värde som du vill bestämma det n:te minsta värdet ur.!är den position (från det minsta värdet) i matrisen eller dataområdet som ska returneras."}, "STANDARDIZE": {"a": "(x; medel<PERSON><PERSON><PERSON>; standardavvikelse)", "d": "Returnerar ett normaliserat värde från en fördelning karaktäriserad av medelvärden och standardavvikelser.", "ad": "är det värde som du vill normalisera.!är det aritmetiska medelvärdet av fördelningen.!är standardavvikelsen för fördelningen, ett positivt tal."}, "STDEV": {"a": "(tal1; [tal2]; ...)", "d": "Beräknar standardavvikelsen utifrån ett exempel (logiska värden och text i exemplet ignoreras)", "ad": "är 1 till 255 tal som motsvarar ett exempel i en population, och kan vara tal eller hänvisningar som innehåller tal"}, "STDEV.P": {"a": "(tal1; [tal2]; ...)", "d": "Beräknar standardavvikelsen baserad på hela populationen angiven som argument (ignorerar logiska värden och text).", "ad": "är 1 till 255 tal som motsvarar en population och kan vara tal eller referenser som innehåller tal."}, "STDEV.S": {"a": "(tal1; [tal2]; ...)", "d": "Uppskattar standardavvikelsen baserad på ett sampel (ignorerar logiska värden och text i samplet).", "ad": "är 1 till 255 tal som motsvarar ett sampel i en population och kan vara tal eller referenser som innehåller tal."}, "STDEVA": {"a": "(värde1; [värde2]; ...)", "d": "Uppskattar standardavvikelsen baserad på ett sampel inklusive logiska värden och text. Text och det logiska värdet FALSKT har värdet 0; det logiska värdet SANT har värdet 1.", "ad": "är mellan 1 och 255 värden som motsvarar ett sampel i en population och kan vara värden, namn eller referenser till värden."}, "STDEVP": {"a": "(tal1; [tal2]; ...)", "d": "Beräk<PERSON> standardavvikelsen utifrån hela populationen angiven som argument (logiska värden och text ignoreras)", "ad": "är 1 till 255 tal som motsvarar en population, och kan vara tal eller referenser som innehåller tal"}, "STDEVPA": {"a": "(värde1; [värde2]; ...)", "d": "Beräknar standard avvikelsen baserad på hela populationen, inklusive logiska värden och text. Text och det logiska värdet FALSKT har värdet 0; det logiska värdet SANT har värdet 1.", "ad": "är 1 till 255 värden som motsvarar en population och kan vara värden, namn, matriser eller referenser som innehåller värden."}, "STEYX": {"a": "(kända_y; kända_x)", "d": "Returnerar standardfelet för ett förutspått y-värde för varje x-värde i en regression", "ad": "är en matris eller ett område med beroende datapunkter och kan vara tal, namn, matriser eller referenser som innehåller tal!är en matris eller ett område med oberoende datapunkter och kan vara tal, namn, matriser eller referenser som innehåller tal"}, "TDIST": {"a": "(x; frihetsgrader; sidor)", "d": "Returnera studentens t-fördelning", "ad": "är ett numeriskt värde där du vill beräkna fördelningen!är ett heltal som anger det antal frihetsgrader som karaktäriserar fördelningen!anger antalet fördelningssidor som ska returneras: ensidig fördelning = 1; tvåsidig fördelning = 2"}, "TINV": {"a": "(sannolikhet; frihetsgrader)", "d": "Returnerar den tvåsidiga inversen till studentens t-fördelning", "ad": "är sannolikheten associerad med studentens tvåsidiga t-fördelning, ett tal mellan 0 och 1 inklusiv!är ett positivt heltal som visar antalet frihetsgrader som karaktäriserar fördelningen"}, "T.DIST": {"a": "(x; frihetsgrader; kumulativ)", "d": "Returnerar vänstersidig students t-fördelning.", "ad": "är det numeriska värdet där du vill beräkna fördelningen.!är ett heltal som anger det antal frihetsgrader som karaktäriserar fördelningen.!är ett logiskt värde: använd SANT för den kumulativa fördelningsfunktionen och FALSKT för sannolikhetsfunktionen."}, "T.DIST.2T": {"a": "(x; frihetsgrader)", "d": "Returnerar tvåsidig students t-fördelning.", "ad": "är det numeriska värdet där du vill beräkna fördelningen.!är ett heltal som anger antalet frihetsgrader som karaktäriserar fördelningen."}, "T.DIST.RT": {"a": "(x; frihetsgrader)", "d": "Returnerar högersidig students t-fördelning.", "ad": "är det numeriska värdet där du vill beräkna fördelningen.!är ett heltal som anger antalet frihetsgrader som karaktäriserar fördelningen."}, "T.INV": {"a": "(sannolikhet; frihetsgrader)", "d": "Returnerar den vänstersidiga inversen till students t-fördelning.", "ad": "är sannolikheten associerad med students tvåsidiga t-fördelning, ett tal mellan 0 och 1 inklusiv.!är ett positivt heltal som anger antalet frihetsgrader som karaktäriserar fördelningen."}, "T.INV.2T": {"a": "(sannolikhet; frihetsgrader)", "d": "Returnerar den tvåsidiga inversen till students t-fördelning.", "ad": "är sannolikheten associerad med students tvåsidiga t-fördelning, ett tal mellan 0 och 1 inklusiv.!är ett positivt heltal som anger antalet frihetsgrader som karaktäriserar fördelningen."}, "T.TEST": {"a": "(matris1; matris2; sidor; typ)", "d": "<PERSON><PERSON><PERSON> sann<PERSON>n associerad med students t-test.", "ad": "är den första datamängden.!är den andra datamängden.!anger antalet fördelningssidor som ska returneras: ensidig fördelning = 1; tvåsidig fördelning = 2.!är typen av t-test: parat = 1, två sampel med lika varians (homoscedastisk) = 2, två sampel med olika varians = 3."}, "TREND": {"a": "(kända_y; [kända_x]; [nya_x]; [konst])", "d": "Returnerar tal i en linjär trend som matchar kända datapunkter genom att använda minsta kvadrat-metoden", "ad": "är ett område eller en matris med y-värden som är kända i förhållandet y = mx + b!är ett valfritt område eller en matris med x-värden som är kända i förhållandet y = mx + b, en matris med samma storlek som kända_y!är ett område eller en matris med nya x-värden för vilka du vill att TREND ska returnera motsvarande y-värden!är ett logiskt värde: konstanten b beräknas normalt om konst = SANT eller utesluten; konstanten b ska vara 0 om konst = FALSKT"}, "TRIMMEAN": {"a": "(matris; procent)", "d": "Returnerar medelvärdet av mittenpunkterna i en mängd data.", "ad": "är matrisen eller området som ska behandlas.!anger hur många datavärden som ska tas bort från var ända."}, "TTEST": {"a": "(matris1; matris2; sidor; typ)", "d": "Returnera sannolikheten associerad med en students t-test", "ad": "är den första datamängden!är den andra datamängden!anger antalet fördelningssidor som ska returneras: ensidig fördelning = 1; tvåsidig fördelning = 2.!är typen av t-test: parat = 1, två sampel med samma varians (homoscedastisk) = 2, två sampel med olika varians = 3"}, "VAR": {"a": "(tal1; [tal2]; ...)", "d": "Beräk<PERSON> variansen utifrån ett exempel (logiska värden och text i exemplet ignoreras)", "ad": "är 1 till 255 numeriska argument som motsvarar ett exempel i en population"}, "VAR.P": {"a": "(tal1; [tal2]; ...)", "d": "Beräknar variansen baserad på hela populationen (ignorerar logiska värden och text i populationen).", "ad": "är mellan 1 och 255 numeriska argument som motsvarar en population."}, "VAR.S": {"a": "(tal1; [tal2]; ...)", "d": "Uppskattar variansen baserad på ett sampel (ignorerar logiska värden och text i samplet).", "ad": "är från 1 till 255 numeriska argument som motsvarar ett sampel i en population."}, "VARA": {"a": "(värde1; [värde2]; ...)", "d": "Uppskattar variansen baserad på ett sampel, inklusive logiska värden och text. Text och det logiska värdet FALSKT har värdet 0; det logiska värdet SANT har värdet 1.", "ad": "är mellan 1 och 255 värdeargument som motsvarar ett sampel i en population."}, "VARP": {"a": "(tal1; [tal2]; ...)", "d": "Beräknar variansen utifrån hela populationen (logiska värden och text i populationen ignoreras)", "ad": "är 1 till 255 numeriska argument som motsvarar en population"}, "VARPA": {"a": "(värde1; [värde2]; ...)", "d": "Beräknar variansen baserad på hela populationen, inklusive logiska värden och text. Text och det logiska värdet FALSKT har värdet 0; det logiska värdet SANT har värdet 1.", "ad": "är 1 till 255 argument som motsvarar en population."}, "WEIBULL": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnera <PERSON>-fördelningen", "ad": "är värdet där du vill beräkna funktionen, ett icke-negativt tal!är en parameter till fördelningen, ett positivt tal!är en parameter till fördelningen, ett positivt tal!är ett logiskt värde: använd SANT för den kumulativa fördelningsfunktionen och FALSKT för sannolikhetsfunktionen för massa"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kumulativ)", "d": "<PERSON><PERSON><PERSON>fördeln<PERSON>.", "ad": "är värdet där du vill beräkna funktionen, ett icke-negativt tal.!är en parameter till fördelningen, ett positivt tal.!är en parameter till fördelningen, ett positivt tal.!är ett logiskt värde: använd SANT för den kumulativa fördelningsfunktionen och FALSKT för sannolikhetsfunktionen för massa."}, "Z.TEST": {"a": "(matris; x; [sigma])", "d": "Returnerar det ensidiga P-värdet av en z-test.", "ad": "är den matris eller det område med data som x ska testas mot.!är värdet som ska testas.!är den kända populations standardavvikelse. Om den utelämnas kommer sampelstandardavvikelsen att användas."}, "ZTEST": {"a": "(matris; x; [sigma])", "d": "Returnerar det ensidiga P-värdet av en z-test", "ad": "är den matris eller det område med data som x ska testas mot!är värdet som ska testas!är känd populations standardavvikelse. Om den utelämnas används sampelstandardavvikelsen"}, "ACCRINT": {"a": "(utgiv<PERSON>; första_ränta; betalning; kupongränta; nominellt; frekvens; [bastyp]; [beräkningsmetod])", "d": "Return<PERSON><PERSON> den upplupna räntan för värdepapper som ger periodisk ränta.", "ad": "är värdepapperets utgivningsdag uttryckt som ett datumserienummer!är värdepapperets första räntedatum, uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets årliga kupongränta!är värdepapperets nominella värde!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastyp för antal dagar som ska användas!är ett logiskt värde: beräkna upplupen ränta från utgivningsdagen = SANT eller utesluten; beräkna från senaste kupongutbetalnings-/ränteutbetalningsdag = FALSKT"}, "ACCRINTM": {"a": "(utgi<PERSON><PERSON>; fö<PERSON>all; kupongrä<PERSON>; nominellt; [bas])", "d": "Return<PERSON>r den upplupna räntan för värdepapper som ger avkastning på förfallodagen", "ad": "är värdepapperets utgivningsdag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets årliga kupongränta!är värdepapperets nominella värde!är bastyp för antal dagar som ska användas"}, "AMORDEGRC": {"a": "(kostnad; inköpsdatum; första_perioden; restvärde; period; takt; [bas])", "d": "Returnerar den linjära avskrivningen för en tillgång för varje redovisningsperiod.", "ad": "är tillgångens kostnad!är det datum då tillgången köptes in!är det datum då första perioden slutar!är restvärdet i slutet av tillgångens livscykel.!är perioden!är avskrivningstakten!årsbasis: 0 för år med 360 dagar, 1 för faktiskt, 3 för år med 365 dagar."}, "AMORLINC": {"a": "(kostnad; inköpsdatum; första_perioden; restvärde; period; takt; [bas])", "d": "Returnerar den linjära avskrivningen för en tillgång för varje redovisningsperiod.", "ad": "är tillgångens kostnad!är det datum då tillgången köptes in!är det datum då första perioden slutar!är restvärdet i slutet av tillgångens livscykel.!är perioden!är avskrivningstakten!årsbasis: 0 för år med 360 dagar, 1 för faktiskt, 3 för år med 365 dagar."}, "COUPDAYBS": {"a": "(betalning; förfall; frekvens; [bas])", "d": "<PERSON><PERSON><PERSON> antal dagar från början av kupongperioden till likviddagen", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodatum uttryckt som ett datumserienummer!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastyp för antal dagar som ska användas"}, "COUPDAYS": {"a": "(betalning; förfall; frekvens; [bas])", "d": "Returnerar antal dagar i kupongperioden som innehåller likviddagen", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastyp för antal dagar som ska användas"}, "COUPDAYSNC": {"a": "(betalning; förfall; frekvens; [bas])", "d": "<PERSON><PERSON>r antal dagar från likviddagen till nästa kupongdatum", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastyp för antal dagar som ska användas"}, "COUPNCD": {"a": "(betalning; förfall; frekvens; [bas])", "d": "Returnerar nästa kupongdatum efter likviddagen", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastyp för antal dagar som ska användas"}, "COUPNUM": {"a": "(betalning; förfall; frekvens; [bas])", "d": "<PERSON><PERSON><PERSON> antalet kuponger som ska betalas mellan likviddag och förfallodag", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastyp för antal dagar som ska användas"}, "COUPPCD": {"a": "(betalning; förfall; frekvens; [bas])", "d": "<PERSON><PERSON><PERSON> se<PERSON>e k<PERSON> före likviddagen", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastyp för antal dagar som ska användas"}, "CUMIPMT": {"a": "(ränta; antal_perioder; nuvärde; startperiod; slutperiod; typ)", "d": "<PERSON><PERSON><PERSON> den ackumulerade räntan som betalats mellan två perioder", "ad": "är räntesatsen!är det totala antalet betalningsperioder!är nuvärdet!är första perioden i beräkningen!är sista perioden i beräkningen!är val av tidpunkt för inbetalningar"}, "CUMPRINC": {"a": "(ränta; antal_perioder; nuvärde; startperiod; slutperiod; typ)", "d": "<PERSON><PERSON><PERSON> det ackumulerade kapitalbeloppet som har betalats på ett lån mellan två perioder", "ad": "är räntesatsen!är det totala antalet betalningsperioder!är nuvärdet!är första perioden i beräkningen!är sista perioden i beräkningen!är val av tidpunkt för inbetalningar"}, "DB": {"a": "(kostnad; restvärde; livslängd; period; [måna<PERSON>])", "d": "Returnerar avskrivningen för en tillgång under en angiven tid enligt metoden för fast degressiv avskrivning.", "ad": "är initialkostnaden för tillgången.!är värdet vid avskrivningstidens slut.!är antalet perioder under den tid då tillgången skrivs av och minskar i värde. (Avskrivningstid)!är den period för vilken du vill beräkna värdeminskningen. Period måsta anges i samma enhet som Livslängd.!är antal månader det första året. Om månader är utelämnat antas de vara 12."}, "DDB": {"a": "(kostnad; restvärde; livslängd; period; [faktor])", "d": "Returnerar en tillgångs värdeminskning under en viss period med hjälp av dubbel degressiv avskrivning eller någon annan metod som du anger.", "ad": "är initialkostnaden för tillgången.!är värdet efter avskrivningen.!är antalet perioder under den tid då tillgången skrivs av och minskar i värde. (Avskrivningstid)!är perioden för vilken du vill beräkna värdeminskningen. Argumentet Period måste anges i samma enhet som Livslängd.!är måttet för hur fort tillgången avskrivs. Om Faktor utelämnas, används värdet 2 (dubbel degressiv avskrivning)."}, "DISC": {"a": "(beta<PERSON><PERSON>; fö<PERSON><PERSON>; pris; in<PERSON><PERSON><PERSON>; [bas])", "d": "Returnerar diskonteringsräntan för ett värdepapper", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets pris per 1 000 kr nominellt värde!är värdepapperets inlösningsvärde per 1 000 kr nominellt värde!är bastyp för antal dagar som ska användas"}, "DOLLARDE": {"a": "(br<PERSON><PERSON><PERSON>; heltal)", "d": "Omvandlar ett pris uttryckt som ett bråk till ett decimaltal", "ad": "är ett tal uttryckt som ett bråk!är heltalet som kommer att användas som nämnare i bråket"}, "DOLLARFR": {"a": "(decimaltal; heltal)", "d": "Omvandlar ett tal uttryckt som ett decimaltal till ett tal uttryckt som ett bråk", "ad": "är ett decimaltal!är heltalet som kommer att användas som nämnare i bråket"}, "DURATION": {"a": "(betalning; förfall; kupong; avkastning; frekvens; [bas])", "d": "Returnerar den årliga löptiden för ett värdepapper med periodiska räntebetalningar", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets årliga kupongränta!är värdepapperets årliga avkastning!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastyp för antal dagar som ska användas"}, "EFFECT": {"a": "(nominalränta; antal_perioder)", "d": "Returnerar den effektiva årsräntan", "ad": "är den nominella räntesatsen!är antalet ränteperioder per år"}, "FV": {"a": "(ränta; periodantal; betalning; [nuvärde]; [typ])", "d": "Return<PERSON>r det framtida värdet av en investering, baserat på en periodisk, konstant betalning och en konstant ränta.", "ad": "är räntan per period. Använd t.ex. 6 %/4 för kvartalsvisa betalningar med 6 % ränta.!är det totala antalet betalningsperioder i investeringen.!är den betalning som görs varje period. Den kan inte ändras under investeringens giltighet.!är nuvärdet eller det nuvarande samlade värdet av en serie framtida betalningar. Om det utelämnas är Pv = 0.!är ett värde som representerar när betalningen sker: en betalning i början av perioden = 1; en betalning i slutet av perioden = 0 eller utelämnad."}, "FVSCHEDULE": {"a": "(kapital; räntor)", "d": "Returnerar ett framtida värde av ett begynnelsekapital beräknat på flera olika räntenivåer", "ad": "är nuvärdet!är en matris över de räntesatser som ska gälla"}, "INTRATE": {"a": "(betalning; förfall; investering; in<PERSON><PERSON>sen; [bas])", "d": "Returnerar räntesatsen för ett fullinvesterat värdepapper", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är det investerade beloppet !är beloppet som utbetalas på förfallodagen!är bastypen för antal dagar som ska användas"}, "IPMT": {"a": "(ränta; period; periodantal; nuvärde; [slutvärde]; [typ])", "d": "Returnerar ränteinbetalningen för en investering och vald period, baserat på periodiska, konstanta betalningar och en konstant ränta.", "ad": "är räntan per period. Använd t.ex. 6 %/4 för kvartalsvisa betalningar med 6 % ränta.!är perioden som räntan ska beräknas för och måste ligga i intervallet 1 till periodantal.!är det totala antalet betalningsperioder i en investering.!är nuvärdet eller det nuvarande samlade värdet av en serie framtida betalningar.!är det framtida värde eller det saldo som du vill uppnå efter att sista betalningen har gjorts. Om det utesluts är Fv = 0.!är ett logiskt värde som representerar när betalningar ska ske: slutet av perioden = 0 eller utelämnad, i början av perioden = 1."}, "IRR": {"a": "(v<PERSON><PERSON>; [gissning])", "d": "Returnerar avkastningsgraden för en serie penningflöden.", "ad": "är en matris eller en referens till celler som innehåller tal som du vill beräkna avkastningsgraden för.!är ett tal som du tror ligger nära resultatet för IR; 0,1 (10 procent) om den utelämnas."}, "ISPMT": {"a": "(ränta; period; periodantal; nuvärde)", "d": "Returnerar den ränta som betalats under en viss period för en investering.", "ad": "räntesats per period. Använd t.ex. 6 %/4 för kvartalsvisa betalningar med 6 % ränta.!period för vilken du vill ta reda på räntan.!antal betalningsperioder i en investering.!klumpsumma som motsvarar nuvärdet av en serie framtida betalningar."}, "MDURATION": {"a": "(betalning; förfall; kupong; avkastning; frekvens; [bas])", "d": "<PERSON><PERSON><PERSON> den ändrade <PERSON>-löptiden för ett värdepapper med ett nominellt värde på 1 000 kr.", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets årliga kupongränta!är värdepapperets årliga avkastning!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastypen för antal dagar som ska användas"}, "MIRR": {"a": "(värden; kapitalränta; återinvesteringsränta)", "d": "Returnerar avkastningsgraden för en serie periodiska penningflöden med tanke på både investeringskostnader och räntan på återinvesteringen av pengar.", "ad": "är en matris eller en referens till celler som innehåller tal som representerar en serie betalningar (negativa) och inkomster (positiva) under regelbundna perioder.!är den ränta du betalar för pengar som används i betalningsströmmarna.!är den ränta du får på betalningsströmmarna när du återinvesterar dem."}, "NOMINAL": {"a": "(effektiv_ränta; antal_perioder)", "d": "Returnerar den årliga nominella räntesatsen", "ad": "är den effektiva räntesatsen!är antalet ränteperioder per år"}, "NPER": {"a": "(ränta; betalning; nuvärde; [slutvärde]; [typ])", "d": "<PERSON><PERSON>r antalet perioder för en investering baserad på periodisk betalning och en konstant ränta.", "ad": "är räntan per period. Använd t.ex. 6 %/4 för kvartalsvisa betalningar med 6 % ränta.!är den betalning som görs varje period. Den kan inte ändras under investeringens giltighet.!är nuvärdet eller det nuvarande samlade värdet av en serie framtida betalningar.!är det framtida värde eller det saldo som du vill uppnå efter att sista betalningen har gjorts. Om det utesluts används värdet noll.!är ett logiskt värde: betalning i början av perioden = 1; betalning i slutet av perioden = 0 eller utelämnat."}, "NPV": {"a": "(ränta; värde1; [värde2]; ...)", "d": "Returnerar nuvärdet av en serie betalningar baserad på en diskonteringsränta och serier med framtida betalningar (negativa värden) och inkomster (positiva värden).", "ad": "är räntesatsen per period.!är från 1 till 254 betalningar och inkomster jämt fördelade i tiden och förekommande i slutet av varje period."}, "ODDFPRICE": {"a": "(betalning; förfall; utgivning; kup_dag_1; ränta; avkastning; inlösen; frekvens; [bas])", "d": "Returnerar priset per 1 000 kr nominellt värde för ett värdepapper med en udda första period", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets utgivningsdag uttryckt som ett datumserienummer!är värdepapperets första kupongdatum uttryckt som ett datumserienummer!är värdepapperets räntesats!är värdepapperets årliga avkastning!är värdepapperets inlösningsvärde per 1 000 kr nominellt värde!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastypen för antal dagar som ska användas"}, "ODDFYIELD": {"a": "(betalning; förfall; utgivning; kup_dag_1; ränta; pris; inl<PERSON>sen; frekvens; [bas])", "d": "Returnerar avkastningen för ett värdepapper med en udda första period", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets utgivningsdag uttryckt som ett datumserienummer!är värdepapperets första kupongdatum uttryckt som ett datumserienummer!är värdepapperets räntesats!är värdepapperets pris!är värdepapperets inlösningsvärde per 1 000 kr nominellt värde!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastypen för antal dagar som ska användas"}, "ODDLPRICE": {"a": "(betalning; förfall; sista_kup_dag; ränta; avkastning; inlösen; frekvens; [bas])", "d": "Returnerar priset per 1 000 kr nominellt värde för ett värdepapper med en udda sista period", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets sista kupongdatum uttryckt som ett datumserienummer!är värdepapperets räntesats!är värdepapperets årliga avkastning!är värdepapperets inlösningsvärde per 1 000 kr nominellt värde!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastypen för antal dagar som ska användas"}, "ODDLYIELD": {"a": "(betalning; förfall; sista_kup_dag; ränta; pris; in<PERSON><PERSON>sen; frekvens; [bas])", "d": "Returnerar avkastningen för ett värdepapper med en udda sista period", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets sista kupongdatum uttryckt som ett datumserienummer!är värdepapperets räntesats!är värdepapperets pris!är värdepapperets inlösningsvärde per 1 000 kr nominellt värde!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastypen för antal dagar som ska användas"}, "PDURATION": {"a": "(ränta; nuvärde; slutvärde)", "d": "Return<PERSON>r antalet perioder som krävs för en investering att uppnå ett visst värde.", "ad": "är räntan per period.!är nuvärdet för investeringen.!är det önskade framtida värdet på investeringen."}, "PMT": {"a": "(ränta; periodantal; nuvärde; [slutvärde]; [typ])", "d": "Beräknar betalningen av ett lån baserat på regelbundna betalningar och en konstant ränta.", "ad": "är räntan per period på lånet. Använd t.ex. 6 %/4 för kvartalsvisa betalningar med 6 % ränta.!är det totala antalet betalningsperioder för lånet.!är nuvärdet: den totala summan som en serie framtida betalningar är värda just nu.!är det framtida värde eller det saldo som du vill uppnå efter att sista betalningen har gjorts. Om det utelämnas används värdet 0 (noll).!är ett logiskt värde: betalning i början av perioden = 1; betalning i slutet av perioden = 0 eller utelämnat."}, "PPMT": {"a": "(ränta; period; periodantal; nuvärde; [slutvärde]; [typ])", "d": "Returnerar amorteringsinbetalningen för en investering baserat på periodiska, konstanta betalningar och en konstant ränta.", "ad": "är räntan per period. Använd t.ex. 6 %/4 för kvartalsvisa betalningar med 6 % ränta.!anger perioden och måste vara i intervallet 1 till periodantal.!är det totala antalet betalningsperioder i en investering.!är nuvärdet: den totala summan som en serie framtida betalningar är värda just nu.!är det framtida värde eller saldo som du vill uppnå efter att sista betalningen har gjorts.!är ett logiskt värde: betalning i början av perioden = 1; betalning i slutet av perioden = 0 eller utelämnat."}, "PRICE": {"a": "(beta<PERSON><PERSON>; förfall; ränta; avkastning; inlösen; frekvens; [bas])", "d": "Returnerar priset per 1 000 kr nominellt värde för ett värdepapper som ger periodisk ränta", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets årliga kupongränta!är värdepapperets årliga avkastning!är värdepapperets inlösningsvärde per 1 000 kr nominellt värde!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastypen för antal dagar som ska användas"}, "PRICEDISC": {"a": "(beta<PERSON><PERSON>; fö<PERSON><PERSON>; rä<PERSON>; inl<PERSON>sen; [bas])", "d": "Returnerar priset per 1 000 kr nominellt värde för ett diskonterat värdepapper", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets diskonteringsränta!är värdepapperets inlösningsvärde per 1 000 kr nominellt värde!är bastypen för antal dagar som ska användas"}, "PRICEMAT": {"a": "(betalning; förfall; utgivning; ränta; avkastning; [bas])", "d": "Returnerar priset per 1 000 kr nominellt värde för ett värdepapper som ger avkastning på förfallodagen", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodatum uttryckt som ett datumserienummer!är värdepapperets utgivningsdag uttryckt som ett datumserienummer!är värdepapperets räntesats vid utgivningsdagen!är värdepapperets årliga avkastning!är bastyp för antal dagar som ska användas"}, "PV": {"a": "(ränta; periodantal; betalning; [slutvärde]; [typ])", "d": "Returnerar nuvärdet av en investering: den totala summan som en serie med framtida betalningar är värd nu.", "ad": "är räntan per period. Använd t.ex. 6 %/4 för kvartalsvisa betalningar med 6 % ränta.!är det totala antalet betalningsperioder i en investering.!är den betalning som görs över varje period och den kan inte ändras under investeringens giltighet.!är det framtida värde eller det saldo som du vill uppnå efter att sista betalningen har gjorts.!är ett logiskt värde: betalning i början av perioden = 1; betalning i slutet av perioden = 0 eller utelämnat."}, "RATE": {"a": "(periodantal; betalning; nuvärde; [slutvärde]; [typ]; [gissning])", "d": "Returnerar räntesatsen per period för ett lån eller en investering. Använd t.ex. 6 %/4 för kvartalsvisa betalningar med 6 % ränta.", "ad": "är det totala antalet betalningsperioder för lånet eller investeringen.!är den betalning som görs varje period och den kan inte ändras under lå<PERSON> eller investeringens giltighet.!är nuvärdet eller summan av det nuvarande värdet av ett antal framtida betalningar.!är det framtida värdet eller det saldo som du vill uppnå efter att sista betalningen har gjorts. Om det utelämnas används Fv = 0.!är ett logiskt värde: betalning i början av perioden = 1; betalning i slutet av perioden = 0 eller utelämnat.!är din gissning vad räntan kommer att bli; om utelämnat sätts värdet Gissa = 0,1 (10 procent)"}, "RECEIVED": {"a": "(betalning; förfall; investering; ränta; [bas])", "d": "Returnerar beloppet som utbetalas på förfallodagen för ett betalt värdepapper", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är det investerade beloppet !är värdepapperets diskonteringsränta!är bastypen för antal dagar som ska användas"}, "RRI": {"a": "(periodantal; nuvärde; slutvärde)", "d": "<PERSON><PERSON><PERSON> motsvarande ränta för en investerings tillväxt.", "ad": "är antalet perioder för investeringen.!är nuvärdet på investeringen.!är det framtida värdet på investeringen."}, "SLN": {"a": "(kostnad; restvärde; livslängd)", "d": "Returnerar den linjära avskrivningen för en tillgång under en period.", "ad": "är initialkostnaden för tillgången.!är värdet efter avskrivningen.!är antalet perioder tillgången minskar i värde (kallas ibland för en tillgångs avskrivningstid)."}, "SYD": {"a": "(kostnad; restvärde; livslängd; period)", "d": "Returnerar den årliga avskrivningssumman för en tillgång under en angiven period.", "ad": "är initialkostnaden för tillgången.!är värdet efter avskrivningen.!är antalet perioder som tillgången minskar i värde (avskrivningstid).!är perioden. Måste anges i samma enhet som Livslängd."}, "TBILLEQ": {"a": "(beta<PERSON><PERSON>; fö<PERSON><PERSON>; rä<PERSON>)", "d": "Returnerar avkastningen motsvarande obligationsräntan för statsskuldväxlar", "ad": "är likviddagen för statsskuldväxlar uttryckt som ett datumserienummer!är förfallodag för statsskuldväxlar uttryckt som ett datumserienummer!är diskonteringsräntan för statsskuldväxlar"}, "TBILLPRICE": {"a": "(beta<PERSON><PERSON>; fö<PERSON><PERSON>; rä<PERSON>)", "d": "Returnerar priset per 1 000 kr nominellt värde för en statsskuldväxel", "ad": "är likviddagen för statsskuldväxlar uttryckt som ett datumserienummer!är förfallodag för statsskuldväxlar uttryckt som ett datumserienummer!är diskonteringsräntan för statsskuldväxlar"}, "TBILLYIELD": {"a": "(beta<PERSON>ing; fö<PERSON>all; pris)", "d": "Returnerar avkastningen för en statsskuldväxel", "ad": "är likviddagen för statsskuldväxlar uttryckt som ett datumserienummer!är förfallodag för statsskuldväxlar uttryckt som ett datumserienummer!är priset på statsskuldväxlar per 1 000 kr nominellt värde"}, "VDB": {"a": "(kostnad; restvärde; livslängd; startperiod; slutperiod; [faktor]; [inget_byte])", "d": "Returnerar avskrivningen för en tillgång under en angiven period eller del av period genom dubbel degressiv avskrivning eller annan metod som du anger.", "ad": "är initialkostnaden för tillgången.!är värdet efter avskrivningen.!är antalet perioder under den tid då tillgången skrivs av och minskar i värde. (Avskrivningstid)!är startperioden för beräkningen av avskrivningen, i samma enheter som Livslängd.!är slutperioden för beräkningen av avskrivningen, i samma enheter som Livslängd.!är måttet för hur fort tillgången avskrivs. Om det utelämnas används värdet 2 (dubbel degressiv avskrivning).!Växla till linjär avskrivning när sådan avskrivning är större än den degressiva avskrivningen = FALSKT eller utelämnad; växla inte = SANT."}, "XIRR": {"a": "(v<PERSON><PERSON>; datum; [gissning])", "d": "Returnerar internräntan för ett schema över betalningsströmmar som inte nödvändigtvis är periodiska", "ad": "är en serie betalningsströmmar som motsvarar ett schema över betalningsdatum!är ett schema över betalningsdatum som motsvarar betalningsströmmarna!är det tal du antar ligger nära resultatet av XIRR"}, "XNPV": {"a": "(rä<PERSON>; värden; datum)", "d": "Return<PERSON><PERSON> det diskonterade nuvärdet för ett schema över betalningsströmmar som inte nödvändigtvis är periodiska", "ad": "är diskonteringsräntan som gäller för betalningsströmmarna!är en serie betalningsströmmar som motsvarar ett schema över betalningsdatum!är ett schema över betalningsdatum som motsvarar betalningsströmmarna"}, "YIELD": {"a": "(beta<PERSON><PERSON>; fö<PERSON><PERSON>; rä<PERSON>; pris; in<PERSON><PERSON><PERSON>; frek<PERSON><PERSON>; [bas])", "d": "Returnerar avkastningen för ett värdepapper som betalar periodisk ränta", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets årliga kupongränta!är värdepapperets pris per 1 000 kr nominellt värde!är värdepapperets inlösningsvärde per 1 000 kr nominellt värde!är antalet kupongutbetalningar/ränteutbetalningar per år!är bastypen för antal dagar som ska användas"}, "YIELDDISC": {"a": "(beta<PERSON><PERSON>; fö<PERSON><PERSON>; pris; in<PERSON><PERSON><PERSON>; [bas])", "d": "Returnerar den årliga avkastningen för diskonterade värdepapper", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets pris per 1 000 kr nominellt värde!är värdepapperets inlösningsvärde per 1 000 kr nominellt värde!är bastypen för antal dagar som ska användas"}, "YIELDMAT": {"a": "(beta<PERSON><PERSON>; fö<PERSON>all; utgiv<PERSON>; rä<PERSON>; pris; [bas])", "d": "Returnerar den årliga avkastningen för ett värdepapper som ger ränta på förfallodagen", "ad": "är värdepapperets likviddag uttryckt som ett datumserienummer!är värdepapperets förfallodag uttryckt som ett datumserienummer!är värdepapperets utgivningsdag uttryckt som ett datumserienummer!är värdepapperets räntesats på utgivningsdagen!är värdepapperets pris per 1 000 kr nominellt värde!är bastypen för antal dagar som ska användas"}, "ABS": {"a": "(tal)", "d": "Returnerar absolutvärdet av ett tal. Ett tal utan tecken.", "ad": "är ett reellt tal som du vill beräkna absolutvärdet på."}, "ACOS": {"a": "(tal)", "d": "Returnerar arcus cosinus för ett tal, i radianer i intervallet 0 till <PERSON>. Arcus cosinus är vinkeln vars cosinus är Tal.", "ad": "är cosinusvärdet av vinkeln du vill ha och måste ligga mellan -1 och 1."}, "ACOSH": {"a": "(tal)", "d": "Returnerar inverterad hyperbolisk cosinus för ett tal.", "ad": "är ett reellt tal som är större än eller lika med 1."}, "ACOT": {"a": "(tal)", "d": "<PERSON><PERSON>r arcus cotangens för ett tal, i radianer i intervallet 0 till Pi.", "ad": "är cotangensvärdet av vinkeln du vill ha."}, "ACOTH": {"a": "(tal)", "d": "Returnerar inverterad hyperbolisk cotangens för ett tal.", "ad": "är det hyperboliska cotangensvärdet av vinkeln du vill ha."}, "AGGREGATE": {"a": "(funktion; alternativ; ref1; ...)", "d": "Returnerar en mängdfunktion i en lista eller databas.", "ad": "är talet 1 till 19 som anger summeringsfunktionen för mängdfunktionen.!är numret 0 till 7 som anger de värden som ska ignoreras för mängden.!är matrisen eller området med numeriska data där mängdfunktionen ska beräknas.!anger positionen i matrisen; den är n:te störst, n:te minst, n:te percentil eller n:te kvartil.!är talet 1 till 19 som anger summeringsfunktionen för mängdfunktionen.!är numret 0 till 7 som anger de värden som ska ignoreras för mängden.!är 1 till 253 områden eller referenser för vilka du vill ha mängdfunktionen."}, "ARABIC": {"a": "(text)", "d": "Konverterar romerska siffror till arabiska.", "ad": "är det tal med romerska siffror som du vill konvertera."}, "ASC": {"a": "(text)", "d": "<PERSON><PERSON><PERSON> med DBCS-teckenuppsättningar (teckenuppsättningar med dubbla byte) ändrar den gär funktionen tecken med hel bredd (två byte) till halvbreddstecken (en byte)", "ad": "är det text som du vill ändra."}, "ASIN": {"a": "(tal)", "d": "Returnerar arcus sinus för ett tal i radianer, i intervallet -Pi/2 till Pi/2.", "ad": "är sinusvärdet av vinkeln du vill ha och måste ligga mellan -1 och 1."}, "ASINH": {"a": "(tal)", "d": "Returnerar hyperbolisk arcus sinus för ett tal.", "ad": "är ett reellt tal som är större än eller lika med 1."}, "ATAN": {"a": "(tal)", "d": "<PERSON><PERSON>r arcus tangens för ett tal i radianer, i intervallet -Pi/2 till Pi/2.", "ad": "är tangensvärdet för vinkeln som du vill ha."}, "ATAN2": {"a": "(x; y)", "d": "Return<PERSON>r arcus tangens för de angivna x- och y-koordinaterna, i radianer mellan -Pi och Pi, vilket exkluderar -Pi.", "ad": "är punktens x-koordinat.!är punktens y-koordinat."}, "ATANH": {"a": "(tal)", "d": "Returnerar inverterad hyperbolisk tangens för ett tal.", "ad": "<PERSON>r ett reellt tal mellan -1 och 1, förutom -1 och 1."}, "BASE": {"a": "(tal; talbas; [minimilängd])", "d": "Konverterar ett tal till textformat med en given talbas.", "ad": "är talet du vill konvertera.!är talbasen du vill konvertera talet till.!är minimilängden på strängen som returneras. Om det utelämnas läggs inga inledande nollor till."}, "CEILING": {"a": "(tal; signifikans)", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal uppåt, till närmaste signifikanta multipel.", "ad": "är värdet som du vill runda av.!är den multipel som du vill avrunda till."}, "CEILING.MATH": {"a": "(tal; [signifikans]; [typvärde])", "d": "A<PERSON><PERSON><PERSON> ett tal uppåt till närmaste heltal eller till närmaste signifikanta multipel.", "ad": "är värdet som du vill avrunda.!är den multipel som du vill avrunda till.!den här funktionen avrundar uppåt från noll när den har angivits och inte är noll."}, "CEILING.PRECISE": {"a": "(tal; [signifikans])", "d": "Return<PERSON>r ett tal som har rundats upp till närmaste heltal eller närmaste signifikanta multipel", "ad": "är värdet som du vill runda av.!är den multipel som du vill avrunda till."}, "COMBIN": {"a": "(antal; valt_antal)", "d": "<PERSON><PERSON>r antalet kombinationer för ett givet antal objekt.", "ad": "är antalet objekt.!är antalet objekt i varje kombination."}, "COMBINA": {"a": "(tal; valt_tal)", "d": "<PERSON><PERSON>r antalet kombinationer med repetitioner för ett givet antal objekt.", "ad": "är antalet objekt.!är antalet objekt i varje kombination."}, "COS": {"a": "(tal)", "d": "<PERSON><PERSON><PERSON> cosinus för en vinkel.", "ad": "är vinkeln i radianer som du vill ha cosinus för"}, "COSH": {"a": "(tal)", "d": "Return<PERSON>r hyperboliskt cosinus för ett tal.", "ad": "är ett reellt tal."}, "COT": {"a": "(tal)", "d": "Returnerar cotangens för en vinkel.", "ad": "är vinkeln i radianer som du vill ha cotangens för."}, "COTH": {"a": "(tal)", "d": "Returnerar hyperbolisk cotangens för ett tal.", "ad": "är vinkeln i radianer som du vill ha hyperbolisk cotangens för."}, "CSC": {"a": "(tal)", "d": "<PERSON><PERSON><PERSON> cosekant för en vinkel.", "ad": "är vinkeln i radianer som du vill ha cosekant för."}, "CSCH": {"a": "(tal)", "d": "Returnerar hyperbolisk cosekant för en vinkel.", "ad": "är vinkeln i radianer som du vill ha hyperbolisk cosekant för."}, "DECIMAL": {"a": "(tal; talbas)", "d": "Konverterar ett tal i textformat i en given bas till ett decimaltal.", "ad": "är talet du vill konvertera.!är talbasen för det tal du konverterar."}, "DEGREES": {"a": "(vinkel)", "d": "Konverterar radianer till grader.", "ad": "är vinkeln i radianer som du vill konvertera."}, "ECMA.CEILING": {"a": "(tal; signifikans)", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal uppåt, till närmaste signifikanta multipel", "ad": "är värdet som du vill runda av.!är den multipel som du vill avrunda till."}, "EVEN": {"a": "(tal)", "d": "Rundar av ett positivt tal uppåt, och ett negativt tal nedåt, till närmaste jämna heltal.", "ad": "är värdet som du vill avrunda."}, "EXP": {"a": "(tal)", "d": "Returnerar e upphöjt till ett angivet tal.", "ad": "är exponenten som används tillsammans med basen e. Konstanten e är lika med 2.71828182845904, basen i en naturlig logaritm."}, "FACT": {"a": "(tal)", "d": "Returnerar ett tals fakultet. D.v.s. produkten av 1*2*3*...*tal.", "ad": "är det icke-negativa tal som du vill ha fakulteten av."}, "FACTDOUBLE": {"a": "(tal)", "d": "Return<PERSON>r dubbelfakulteten för ett tal", "ad": "är det värde som den dubbla fakulteten ska returneras för"}, "FLOOR": {"a": "(tal; signifikans)", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal nedåt, till närmaste signifikanta multipel.", "ad": "är det numeriska värde som du vill avrunda.!är den multipel som du vill avrunda till. tal och Signifikans måste båda vara antingen negativa eller positiva."}, "FLOOR.PRECISE": {"a": "(tal; [signifikans])", "d": "Return<PERSON>r ett tal som har rundats av nedåt till närmaste heltal eller närmaste signifikanta multipel", "ad": "är värdet som du vill runda av.!är den multipel som du vill avrunda till."}, "FLOOR.MATH": {"a": "(tal; [signifikans]; [typvärde])", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal nedåt till närmaste heltal eller närmaste signifikanta multipel.", "ad": "är värdet som du vill avrunda.!är den multipel som du vill avrunda till.!den här funktionen avrundar nedåt mot noll när den har angivits och inte är noll."}, "GCD": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar den största gemensamma nämnaren", "ad": "är 1 till 255 värden"}, "INT": {"a": "(tal)", "d": "Rundar av ett tal till närmaste heltal.", "ad": "är det reella talet du vill runda av, ner<PERSON><PERSON>, till närmaste heltal."}, "ISO.CEILING": {"a": "(tal; [signifikans])", "d": "Returnerar ett tal som har rundats upp till närmaste heltal eller närmaste signifikanta multipel. Oavsett talets tecken rundas talet uppåt. Om talet eller signifikansen är noll returneras dock noll.", "ad": "är värdet som du vill runda av.!är den multipel som du vill avrunda till."}, "LCM": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON>r minsta gemensamma multipel", "ad": "är 1 till 255 värden som du vill ha minsta gemensamma multipel för"}, "LN": {"a": "(tal)", "d": "Returnerar den naturliga logaritmen för ett tal.", "ad": "är det positiva reella tal som du vill ha den naturliga logaritmen för."}, "LOG": {"a": "(tal; [bas])", "d": "Returnerar logaritmen av ett tal för basen som du anger.", "ad": "är det positiva reella tal som du vill veta logaritmen för.!är logaritmens bas. Sätts till 10 om den inte anges."}, "LOG10": {"a": "(tal)", "d": "Returnerar 10-<PERSON><PERSON><PERSON><PERSON> för ett tal.", "ad": "är det positiva reella tal som du vill ha 10-logaritmen för."}, "MDETERM": {"a": "(matris)", "d": "<PERSON><PERSON>r matrisen som är avgörandet av en matris.", "ad": "är en numerisk matris med lika antal rader och kolumner, antingen ett cellområde eller en matriskonstant."}, "MINVERSE": {"a": "(matris)", "d": "<PERSON><PERSON>r en invers av en matris för matrisen som är lagrad i en matris.", "ad": "är en numerisk matris med lika antal rader och kolumner, antingen ett cellområde eller en konstant i en matris."}, "MMULT": {"a": "(matris1; matris2)", "d": "Returnerar matrisprodukten av två matriser, en matris med samma antal rader som matris1 och samma antal kolumner som matris2.", "ad": "är första matrisen med tal som ska multipliceras och måste därför ha lika många kolumner som Matris2 har rader."}, "MOD": {"a": "(tal; divisor)", "d": "Returnerar resten efter att ett tal har dividerats med en divisor.", "ad": "är talet som du vill ha resten av efter att divisionen utförts.!är talet som du dividerar det andra talet med."}, "MROUND": {"a": "(tal; multipel)", "d": "<PERSON><PERSON>r ett tal avrundat till en given multipel", "ad": "är värdet som du vill avrunda!är den multipel som du vill runda till"}, "MULTINOMIAL": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar multinomialen för en uppsättning tal", "ad": "är 1 till 255 värden som du vill ha multinomialen för"}, "MUNIT": {"a": "(dimension)", "d": "<PERSON><PERSON>r enhetsmatrisen för den angivna dimensionen.", "ad": "är ett heltal som anger dimensionen på den enhetsmatris du vill returnera."}, "ODD": {"a": "(tal)", "d": "Rundar av ett positivt tal uppåt, och ett negativt nedåt, till närmaste udda heltal.", "ad": "är värdet som du vill avrunda."}, "PI": {"a": "()", "d": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> pi, 3,14159265358979, med 15 decimaler.", "ad": ""}, "POWER": {"a": "(tal; exponent)", "d": "Returnerar resultatet av ett tal upphöjt till en exponent.", "ad": "är basen, vilket reellt tal som helst.!är exponenten som basen upphöjs till."}, "PRODUCT": {"a": "(tal1; [tal2]; ...)", "d": "Multiplicerar alla tal angivna som argument.", "ad": "är mellan 1 och 255 tal, logiska värden eller texttal som du vill multiplicera."}, "QUOTIENT": {"a": "(tä<PERSON><PERSON><PERSON>; nämnare)", "d": "Returnerar heltalsdelen av en division", "ad": "är täljaren!är nämnaren"}, "RADIANS": {"a": "(vinkel)", "d": "Konverterar grader till radianer.", "ad": "är en vinkel, i grader, som du vill konvertera."}, "RAND": {"a": "()", "d": "Returnerar ett slumptal större än eller lika med 0 och mindre än 1 (ändringar sker vid omberäkning).", "ad": ""}, "RANDARRAY": {"a": "([rader]; [kolumner]; [min]; [max]; [heltal])", "d": "Returnerar en matris med slumptal", "ad": "antalet rader i den returnerade matrisen!antalet kolumner i den returnerade matrisen!det minsta antal som ska returneras!det högsta antalet som ska returneras!returnerar ett heltal eller ett decimaltal. SANT för ett heltal, FALSKT för ett decimaltal"}, "RANDBETWEEN": {"a": "(nedre; övre)", "d": "<PERSON><PERSON><PERSON> ett slumptal mellan de tal som du anger", "ad": "är det minsta heltal som Slump.mellan kommer att returnera!är det största heltal som Slump.mellan kommer att returnera"}, "ROMAN": {"a": "(tal; [format])", "d": "Konverterar arabiska siffror till romerska, som text.", "ad": "är det tal med arabiska siffror som du vill konvertera.!är den siffra som anger vilken typ av romersk siffra du vill ha."}, "ROUND": {"a": "(tal; decimaler)", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal till ett angivet antal decimaler.", "ad": "är talet som du vill avrunda.!är det antal decimaler som du vill avrunda till. Negativa avrundningar; noll till närmaste heltal."}, "ROUNDDOWN": {"a": "(tal; decimaler)", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal nedåt mot noll.", "ad": "är ett reellt tal som du vill avrunda nedåt.!är ett antal siffror som du vill runda av. Negativa avrundningar till vänster om decimalkommat; noll eller utelämnade, till närmaste heltal."}, "ROUNDUP": {"a": "(tal; decimaler)", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal uppåt från noll.", "ad": "är ett reellt tal som du vill avrunda uppåt.!är ett antal siffror som du vill runda av. Negativa avrundningar till vänster om decimalkommat; noll eller utelämnade, till närmaste heltal."}, "SEC": {"a": "(tal)", "d": "<PERSON><PERSON><PERSON> sekant för en vinkel.", "ad": "är vinkeln i radianer som du vill ha sekant för."}, "SECH": {"a": "(tal)", "d": "Returnerar hyperbolisk sekant för en vinkel.", "ad": "är vinkeln i radianer som du vill ha hyperbolisk sekant för."}, "SERIESSUM": {"a": "(x; n; m; koefficienter)", "d": "Returnerar summan av en potensserie baserad på formeln", "ad": "är indatavärdet till potensserien!är initialpotensen som du vill upphöja x till!anger hur mycket n ska ökas för varje term i potensserien!är en mängd av koefficienter som varje efterföljande potens av x ska multipliceras med"}, "SIGN": {"a": "(tal)", "d": "Return<PERSON>r ett tals tecken: 1 om talet är positivt, noll om talet är noll eller -1 om talet är negativt.", "ad": "är ett reellt tal."}, "SIN": {"a": "(tal)", "d": "Returnerar sinus för en vinkel.", "ad": "är vinkeln i radianer som du vill ha sinus för. Grader * PI()/180 = radianer."}, "SINH": {"a": "(tal)", "d": "Returnerar hyperbolisk sinus för ett tal.", "ad": "är ett reellt tal."}, "SQRT": {"a": "(tal)", "d": "Returnerar ett tals kvadratrot.", "ad": "är talet som du vill ha k<PERSON>ten av."}, "SQRTPI": {"a": "(tal)", "d": "Return<PERSON>r k<PERSON> av (tal * pi)", "ad": "är det tal som pi multipliceras med"}, "SUBTOTAL": {"a": "(funktionsnr; ref1; ...)", "d": "Returnerar en delsumma i en lista eller databas.", "ad": "är ett tal mellan 1 och 11 som anger summeringsfunktionen för delsumman.!är 1 till 254 områden eller referenser för vilka du vill ha delsumman."}, "SUM": {"a": "(tal1; [tal2]; ...)", "d": "Adderar samtliga tal i ett cellområde.", "ad": "är mellan 1 och 255 tal som ska summeras. Logiska värden och text ignoreras i cellerna, men inkluderas om de skrivs som argument."}, "SUMIF": {"a": "(o<PERSON>r<PERSON><PERSON>; vill<PERSON>; [summaområ<PERSON>])", "d": "<PERSON><PERSON><PERSON> celler enligt ett angivet villkor.", "ad": "är det cellområde som du vill beräkna.!är det villkor i form av ett tal, utt<PERSON><PERSON> eller text som definierar vilka celler som ska adderas.!är de celler som ska summeras. Om de utelämnas används cellerna i området."}, "SUMIFS": {"a": "(summa<PERSON><PERSON><PERSON><PERSON>; vill<PERSON><PERSON>mr<PERSON>de; villkor; ...)", "d": "<PERSON><PERSON><PERSON> de celler som anges av en given uppsättning villkor", "ad": "är de celler som ska adderas.!är cellområdet som du vill beräkna för det specifika villkoret!är villkoret i form av ett tal, uttry<PERSON> eller text som definierar vilka celler som ska adderas"}, "SUMPRODUCT": {"a": "(matris1; [matris2]; [matris3]; ...)", "d": "Returnerar summan av produkter av korresponderande områden eller matriser.", "ad": "är 2 till 255 matriser för vilka du vill multiplicera och sedan addera komponenter. Alla matriser måste ha samma dimensioner."}, "SUMSQ": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar summan av argumentens kvadrater. Argumenten kan vara tal, matriser, namn eller referenser till celler som innehåller tal.", "ad": "är från 1 till 255 tal, matriser, namn eller referenser till matriser, som du vill ha summan av kvadraterna av."}, "SUMX2MY2": {"a": "(xmatris; ymatris)", "d": "Summerar skillnaderna mellan kvadraterna i två motsvarande områden eller matriser.", "ad": "är den första matrisen eller området med värden och kan vara ett tal eller namn, en matris eller referens som innehåller tal.!är den andra matrisen eller området med värden och kan vara ett tal eller namn, en matris eller referens som innehåller tal."}, "SUMX2PY2": {"a": "(xmatris; ymatris)", "d": "<PERSON><PERSON><PERSON>man av summan av kvadraterna på tal i två motsvarande områden eller matriser.", "ad": "är den första matrisen eller området med värden och kan vara ett tal eller namn, en matris eller referens som innehåller tal.!är den andra matrisen eller området med värden och kan vara ett tal eller namn, en matris eller referens som innehåller tal."}, "SUMXMY2": {"a": "(xmatris; ymatris)", "d": "Summerar kvadraten på skillnaderna mellan två motsvarande områden eller matriser.", "ad": "är den första matrisen eller området med värden och kan vara ett tal eller namn, en matris eller referens som innehåller tal.!är den andra matrisen eller område med värden och kan vara ett tal eller namn, en matris eller referens som innehåller tal."}, "TAN": {"a": "(tal)", "d": "<PERSON><PERSON>r en vinkels tangent.", "ad": "är den vinkel i radianer som du vill ha tangens för. Grader * PI()/180 = radianer."}, "TANH": {"a": "(tal)", "d": "Returnerar hyperbolisk tangens för ett tal.", "ad": "är ett reellt tal."}, "TRUNC": {"a": "(tal; [antal_siffror])", "d": "Avkortar ett tal till ett heltal genom att ta bort decimaler.", "ad": "är talet som du vill avkorta.!är ett tal som anger precisionen på avkortningen. Om det utelämnas används 0 (noll)."}, "ADDRESS": {"a": "(rad; kolumn; [abs]; [a1]; [bladnamn])", "d": "Skapar en cellreferens som text med ett angivet antal rader och kolumner.", "ad": "är radnumret som används till cellreferensen: radnummer = 1 för rad 1.!är kolumnnumret som används till cellreferensen. T ex, kolumnnummer = 4 för kolumn D.!anger referenstypen: absolut = 1; absolut rad/relativ kolumn = 2; relativ rad/absolut kolumn = 3; relativ =4.!är ett logiskt värde som anger referenstypen: A1 typ = 1 eller SANT; R1C1 typ = 0 eller FALSKT.!är text som anger namnet på det kalkylblad som ska användas som externreferens"}, "CHOOSE": {"a": "(index; värde1; [värde2]; ...)", "d": "Väljer ett värde eller en åtgärd som ska utföras från en lista av värden, baserad på ett indexnummer.", "ad": "anger vilket argument som väljs. Index_num måste vara mellan 1 och 254, en formel eller en referens till ett tal mellan 1 och 254.!är från 1 till 254 tal, <PERSON><PERSON><PERSON><PERSON><PERSON>, def<PERSON><PERSON><PERSON> namn, formler, funktion<PERSON> eller text argument som VÄLJ väljer från."}, "COLUMN": {"a": "([ref])", "d": "<PERSON><PERSON><PERSON> kolum<PERSON>et för en referens.", "ad": "är cellen eller området med celler som du vill infoga kolumntal i. Om det utelämnas kommer cellen som innehåller funktionen KOLUMN att användas."}, "COLUMNS": {"a": "(matris)", "d": "Return<PERSON>r antalet kolumner i en matris eller en referens.", "ad": "är en matris, en matrisformel eller en referens till ett cellområde som du vill ha reda på antal kolumner i."}, "FORMULATEXT": {"a": "(ref)", "d": "Return<PERSON>r en formel som en sträng.", "ad": "är en referens till en formel."}, "HLOOKUP": {"a": "(letauppvärde; tabell; radindex; [ungefärlig])", "d": "Letar i översta raden av en tabell eller matris med värden och returnerar värdet i samma kolumn från en rad som du anger.", "ad": "är värdet som ska hittas i första raden i en tabell och kan vara ett värde, en referens eller en textsträng.!är en tabell innehållande, text, tal eller logiska värden där data kommer att sökas efter. Tabell_matris kan vara en referens till ett område eller ett områdesnamn.!är antalet rader i tabellmatrisen som det matchande värdet ska returneras från. Rad 1 är första raden med värden i tabellen.!är ett logiskt värde: för att hitta det som bäst stämmer överens i översta raden (sorterat i stigande ordning) = SANT eller utelämnad; vid identisk matchning = FALSKT."}, "HYPERLINK": {"a": "(Lä<PERSON>_placering; [vänligt_namn])", "d": "Skapar en genväg eller ett hopp som öppnar ett dokument som är lagrat på din hårddisk, en server på nätverket eller på Internet.", "ad": "är texten som ger sökvägen och filnamnet på dokumentet som ska öpp<PERSON>, en plats på hårddis<PERSON>, en UNC-adress eller en URL-sökväg.!är texten eller talet som visas i cellen. Om det utelämnas visar cellen namnet på länken."}, "INDEX": {"a": "(matris; rad; [kolumn]!ref; rad; [kolumn]; [omr<PERSON><PERSON>])", "d": "Returnerar ett värde eller referens för cellen vid skärningen av en viss rad och kolumn i ett givet område.", "ad": "är ett område med celler eller en matriskonstant.!markerar raderna i Matris eller Referens som ska returnera ett värde. Om det utelämnas krävs Kolumn_num.!markerar kolumnerna i Matris eller Referens som ska returnera ett värde. Om det utelämnas krävs Rad_num.!är en referens till ett eller flera cellområden.!markerar raderna i Matris eller Referens som ska returnera ett värde. Om det utelämnas krävs Kolumn_num.!markerar kolumnerna i Matris eller Referens som ska returnera ett värde. Om det utelämnas krävs Rad_num.!markerar ett område i referensen som returnerar ett värde. Första området som markeras eller som skrivs in är område 1, det andra området är område 2 o.s.v."}, "INDIRECT": {"a": "(reftext; [a1])", "d": "<PERSON><PERSON><PERSON> referensen angiven av en textsträng.", "ad": "är en referens till en cell som innehåller en referens av typen A1 eller R1C1, ett namn definierat som en referens eller en referens till en cell som en textsträng.!är ett logiskt värde som anger referenstypen i Ref_text: R1C1-typ = FALSKT; A1-typ = SANT eller utelämnad."}, "LOOKUP": {"a": "(letauppvärde; letauppvektor; [resultatvektor]!letauppvärde; matris)", "d": "Returnerar ett värde antingen från ett enrads- eller enkolumnsområde eller från en matris. Finns med för bakåtkompatibilitet.", "ad": "är det värde som LETAUPP söker efter i Letaupp_matrisen och kan vara ett tal, text, ett logiskt värde eller en referens till ett värde.!är ett område som innehåller endast en rad eller kolumn med text, tal eller logiska värden placerade i stigande ordning.!är ett område som innehåller endast en rad eller kolumn, i samma storlek som Letaupp_matrisen.!är ett värde som LETAUPP söker efter i matrisen och kan vara ett tal, text, ett logiskt värde, namn på ett värde eller en referens till ett värde.!är ett område av celler som innehåller text, tal eller logiska värden som du vill jämföra med Leta_upp_värdet."}, "MATCH": {"a": "(letauppvärde; letauppvektor; [typ])", "d": "Returnerar elementets relativa position i en matris som matchar ett angivet värde i en angiven ordning.", "ad": "är värdet som du använder för att hitta värdet du vill ha i matrisen, t ex ett tal, text, ett logiskt värde eller en referens till något av dessa.!är ett kontinuerligt område av celler som innehåller möjliga sökvärden, som t ex ett matrisvärde eller en referens till en matris.!är ett tal 1, 0 eller -1 som anger vilket värde som ska returneras."}, "OFFSET": {"a": "(ref; rader; kolumner; [höjd]; [bredd])", "d": "Returnerar en referens till ett område som är ett givet antal rader och kolumner från en given referens.", "ad": "är referensen som anger utgångspunkt för förskjutningen, en referens till en cell eller ett område av intilliggande celler.!är antal rader ner eller upp som du vill att övre vänstra cellen i resultatet ska referera till.!är antal kolumner till vänster eller höger som du vill att övre vänstra cellen i resultatet ska referera till.!är höjden i antal rader som du vill att resultatet ska vara, om det utelämnas så används samma höjd som Referens.!är bredden i antal kolumner som du vill att resultatet ska vara, om det utelämnas så används samma bredd som Referens."}, "ROW": {"a": "([ref])", "d": "Returnerar en referens radnummer.", "ad": "är cellen eller ett enkelt område av celler som du vill ha radnumret från; om det utelämnas returneras cellen som innehåller RAD-funktionen."}, "ROWS": {"a": "(matris)", "d": "<PERSON><PERSON>r antal rader i en referens eller matris.", "ad": "är en matris, en matrisformel eller en referens till ett område med celler som du vill veta antalet rader i."}, "TRANSPOSE": {"a": "(matris)", "d": "Konverterar ett vertikalt cellområde till ett horisontellt och tvärtom.", "ad": "är ett cellområde i ett kalkylblad eller en matris med värden som du vill transponera."}, "UNIQUE": {"a": "(matris; [by_col]; [exactly_once])", "d": " Returnerar unika värden från ett intervall eller en matris.", "ad": "området eller matrisen som unika rader eller kolumner ska returneras från.!är ett logiskt värde: jämför rader med varandra och returnera de unika raderna = FALSKT eller utelämnat; jämför kolumner med varandra och returnera de unika kolumnerna = SANT!är ett logiskt värde: returnera rader eller kolumner som förekommer exakt en gång från matrisen = SANT; returnera alla distinkta rader eller kolumner från matrisen = FALSKT eller utelämnat"}, "VLOOKUP": {"a": "(letau<PERSON><PERSON><PERSON><PERSON>; tabell<PERSON>ris; kolumnindex; [ungefärlig])", "d": "Söker efter ett värde i den vänstra kolumnen i tabellen och returnerar sedan ett värde från en kolumn som du anger i samma rad. Som standard måste tabellen vara sorterad i stigande ordning.", "ad": "är värdet som ska letas efter i första kolumnen i tabellen och kan vara ett värde, en referens eller en textsträng.!är en tabell innehållande text, tal eller logiska värden i vilken data tas emot. Tabellmatris kan referera till ett område eller ett områdesnamn.!är kolumnnumret i tabellmatris varifrån matchande värden ska returneras. Första kolumnen med värden i tabellen är kolumn 1.!är ett logiskt värde: sök efter det som matchar bäst i den första kolumnen (sorterat i stigande ordning) = SANT eller utelämnad; sök efter exakt matchning = FALSKT."}, "XLOOKUP": {"a": "(lookup_value; lookup_array; return_array; [if_not_found]; [match_mode]; [search_mode])", "d": "s<PERSON><PERSON> efter ett intervall eller en matris för en matchning och returnerar motsvarande objekt från ett andra intervall eller en andra matris. Som standard används en exakt matchning", "ad": "är värdet att söka efter!är matrisen eller intervallet att söka i!är matrisen eller intervallet att returnera!returneras om ingen matchning hittas!ange hur letaupp_värde ska matchas mot värdena i letaupp_matris!ange sökläget som ska användas. Som standard används sökning från första till sista."}, "CELL": {"a": "(infotyp; [referens])", "d": "Returnerar information om formatering, placering eller innehåll för en cell", "ad": "ett textvärde som anger vilken typ av cellinformation du vill returnera!den cell du vill ha information om"}, "ERROR.TYPE": {"a": "(felvärde)", "d": "<PERSON><PERSON><PERSON> ett tal som motsvarar ett felvärde.", "ad": "är felvärdet som du vill ha identifieringsnummer för, och kan vara ett faktiskt felvärde eller en referens till en cell som innehåller ett felvärde."}, "ISBLANK": {"a": "(värde)", "d": "Kontrollerar om värdet refererar till en tom cell och returnerar SANT eller FALSKT.", "ad": "är cellen eller ett namn som refererar till den cell som du vill testa."}, "ISERR": {"a": "(värde)", "d": "Kontrollerar om ett värde är ett annat fel än #Saknas (otillgängligt värde) och returnerar SANT eller FALSKT", "ad": "är värdet du vill testa. Värde kan referera till en cell, en formel eller ett namn som refererar till en cell, en formel eller ett värde"}, "ISERROR": {"a": "(värde)", "d": "Kontrollerar om ett värde är ett fel och returnerar SANT eller FALSKT", "ad": "är värdet du vill testa. Värde kan referera till en cell, en formel eller ett namn som refererar till en cell, en formel eller ett värde"}, "ISEVEN": {"a": "(tal)", "d": "Returnerar SANT om talet är jämt", "ad": "är värdet som ska testas"}, "ISFORMULA": {"a": "(ref)", "d": "Kontrollerar om värdet refererar till en cell som innehåller en formel och returnerar SANT eller FALSKT.", "ad": "är en referens till cellen som du vill testa. Referensen kan vara en cellreferens, en formel eller ett namn som refererar till en cell."}, "ISLOGICAL": {"a": "(värde)", "d": "Kontrollerar om ett värde är ett logiskt värde (SANT eller FALSKT) och returnerar SANT eller FALSKT.", "ad": "är värdet du vill testa. Värde kan referera till en cell, formel eller ett namn som refererar till en cell, formel eller ett värde."}, "ISNA": {"a": "(värde)", "d": "Kontrollerar om ett värde är #Saknas (otillgängligt värde) och returnerar SANT eller FALSKT", "ad": "är värdet du vill testa. Värde kan referera till en cell, formel eller ett namn som refererar till en cell, formel eller värde"}, "ISNONTEXT": {"a": "(värde)", "d": "Kontrollerar om ett värde inte är text (tomma celler är inte text) och returnerar SANT eller FALSKT.", "ad": "är värdet du vill testa: en cell; formel; eller ett namn som refererar till en cell, formel eller ett värde."}, "ISNUMBER": {"a": "(värde)", "d": "Kontrollerar om ett värde är ett tal och returnerar SANT eller FALSKT.", "ad": "är värdet du vill testa. Värde kan referera till en cell, formel eller ett namn som refererar till en cell, formel eller ett värde."}, "ISODD": {"a": "(tal)", "d": "Returnerar SANT om talet är udda", "ad": "är värdet som ska testas"}, "ISREF": {"a": "(värde)", "d": "Kontrollerar om ett värde är en referens och returnerar SANT eller FALSKT.", "ad": "är värdet du vill testa. Ett värde kan referera till en cell, formel eller ett namn som refererar till en cell, formel eller ett värde."}, "ISTEXT": {"a": "(värde)", "d": "Kontrollerar om ett värde är text och returnerar SANT eller FALSKT.", "ad": "är värdet du vill testa. Värde kan referera till en cell, formel eller ett namn som refererar till en cell, formel eller ett värde."}, "N": {"a": "(värde)", "d": "Konverterar värden som inte är tal till tal, datum till serienummer, SANT till 1 och allt annat till 0 (noll).", "ad": "är värdet som du vill konvertera."}, "NA": {"a": "()", "d": "Returnerar felvärdet #Saknas (värdet är inte tillgängligt)", "ad": ""}, "SHEET": {"a": "([värde])", "d": "<PERSON><PERSON><PERSON> bladnummer för bladet som refereras.", "ad": "är namnet på bladet eller en referens som du vill ha bladnumret för. Om det utelämnas returneras numret på det blad som innehåller funktionen."}, "SHEETS": {"a": "([ref])", "d": "<PERSON><PERSON>r antalet blad i en referens.", "ad": "är en referens som du vill veta hur många blad den innehåller. Om det utelämnas returneras antalet blad i arbetsboken som innehåller funktionen."}, "TYPE": {"a": "(värde)", "d": "Returnerar ett heltal som anger ett värdes datatyp: tal=1; text=2; logiskt värde=4; felvärde=16; matris=64; sammansatta data = 128", "ad": "kan vara valfritt värde"}, "AND": {"a": "(logisk1; [logisk2]; ...)", "d": "Kontrollerar om alla argument utvärderas till SANT och returnerar SANT om alla dess argument är lika med SANT.", "ad": "är 1 till 255 villkor du vill testa som kan ha värdet SANT eller FALSKT och som kan vara logiska värden, matriser eller referenser."}, "FALSE": {"a": "()", "d": "Returnerar det logiska värdet FALSKT", "ad": ""}, "IF": {"a": "(logiskt_test; [värde_om_sant]; [värde_om_falskt])", "d": "Kontrollerar om ett villkor uppfylls och returnerar ett värde om ett villkor beräknas till SANT och ett annat värde om det beräknas till FALSKT.", "ad": "är ett värde eller ett uttryck som kan beräknas till SANT eller FALSKT.!är värdet som returneras om Logiskt_test är SANT. Om det utelämnas returneras SANT. Du kan kapsla upp till sju OM-funktioner.!är värdet som returneras om Logiskt_test är FALSKT. Om det utelämnas returneras FALSKT"}, "IFS": {"a": "(logiskt_test; värde_om_sant; ...)", "d": "Kontrollerar om ett eller flera villkor uppfylls och returnerar ett värde som motsvarar det första villkoret som är SANT", "ad": "är alla värden eller uttryck som kan beräknas som SANT eller FALSKT!är värdet som returneras om Logiskt_test är SANT"}, "IFERROR": {"a": "(värde; värde_om_fel)", "d": "Return<PERSON>r värde_om_fel om uttrycket är ett fel, i annat fall värdet för uttrycket", "ad": "är ett värde, ett uttryck eller en referens!är ett värde, ett uttryck eller en referens"}, "IFNA": {"a": "(värde; värde_om_saknas)", "d": "Returnerar ett angivet värde om uttrycket ger #SAKNAS, annars returneras resultatet av uttrycket.", "ad": "är ett värde, ett uttryck eller en referens.!är ett värde, ett uttryck eller en referens."}, "NOT": {"a": "(logisk)", "d": "Ändrar FALSKT till SANT eller SANT till FALSKT.", "ad": "är ett värde eller uttryck som kan beräknas till SANT eller FALSKT."}, "OR": {"a": "(logisk1; [logisk2]; ...)", "d": "Kontrollerar om något av argumenten har värdet SANT och returnerar SANT eller FALSKT. Returnerar FALSKT bara om alla argument har värdet FALSKT.", "ad": "är 1 till 255 villkor du vill testa som kan ha värdet SANT eller FALSKT."}, "SWITCH": {"a": "(uttryck; värde1; resultat1; [standard_eller_värde2]; [resultat2]; ...)", "d": "Beräknar ett uttryck mot en lista med värden och returnerar resultatet som motsvarar det första matchande värdet. Om inget matchar returneras ett valfritt standardvärde", "ad": "är ett uttryck som ska utvärderas!är ett värde som ska jämföras med uttrycket!är ett resultat som ska returneras om motsvarande värde matchar uttrycket"}, "TRUE": {"a": "()", "d": "Returnerar det logiska värdet SANT.", "ad": ""}, "XOR": {"a": "(logisk1; [logisk2]; ...)", "d": "Return<PERSON>r ett logiskt 'Exklusivt eller' för alla argument.", "ad": "är 1 till 254 villkor du vill testa som kan ha värdet SANT eller FALSKT och kan vara logiska värden, matriser eller referenser."}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Returnerar text som är före avgränsande tecken.", "ad": "Texten som du vill söka efter avgränsare.!<PERSON><PERSON><PERSON> eller strängen som ska användas som avgränsare.!Önskad förekomst av avgränsare. Standardvärdet är 1. Ett negativt tal söker från slutet.!Söker i texten efter en avgränsarmatchning. Som standard görs en skiftlägeskänslig matchning.!Om avgränsare ska matchas mot slutet av texten. Som standard matchas de inte.!Returneras om ingen matchning hittas. Som standard returneras #N/A."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Returnerar text som är efter avgränsande tecken.", "ad": "Texten som du vill söka efter avgränsare.!<PERSON><PERSON><PERSON> eller strängen som ska användas som avgränsare.!Önskad förekomst av avgränsare. Standardvärdet är 1. Ett negativt tal söker från slutet.!Söker i texten efter en avgränsarmatchning. Som standard görs en skiftlägeskänslig matchning.!Om avgränsare ska matchas mot slutet av texten. Som standard matchas de inte.!Returneras om ingen matchning hittas. Som standard returneras #N/A."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Delar upp text i rader eller kolumner med avgränsare.", "ad": "Texten som ska delas.!Tecken eller sträng att dela kolumner efter.!Tecken eller sträng att dela rader efter.!Anger om tomma celler ska ignoreras. Standardvärdet är FALSE.!Söker i texten efter en avgränsarmatchning. Som standard görs en skiftlägeskänslig matchning.!Värdet som ska användas för utfyllnad. Som standard används #N/A."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Radbryter en rad- eller kolumnvektor efter angivet antal värden.", "ad": "Vektorn eller referensen som ska radbrytas.!Maximalt antal värden per rad.!Värdet som ska fyllas i. Standardvärdet är #N/A."}, "VSTACK": {"a": "(matris1, [matris2], ...)", "d": "Staplar matriser lo<PERSON>t i en matris.", "ad": "En matris eller referens som ska staplas."}, "HSTACK": {"a": "(matris1, [matris2], ...)", "d": "Staplar matriser vågrätt i en matris.", "ad": "En matris eller referens som ska staplas."}, "CHOOSEROWS": {"a": "(matris, rad1, [rad2], ...)", "d": "<PERSON><PERSON><PERSON> raderna i en matris eller referens.", "ad": "<PERSON><PERSON><PERSON> eller referensen som innehåller raderna som ska returneras.!Antalet rader som ska returneras."}, "CHOOSECOLS": {"a": "(matris, kolumn1, [kolumn2], ...)", "d": "<PERSON><PERSON>r kolumnerna i en matris eller referens.", "ad": "<PERSON><PERSON><PERSON> eller referensen som innehåller kolumnerna som ska returneras.!Antalet kolumner som ska returneras."}, "TOCOL": {"a": "(matris, [ignorera], [scan_by_column])", "d": "Returnerar matrisen som en kolumn.", "ad": "Matrisen eller referensen som ska returneras som en kolumn.!Anger om vissa typer av värden ska ignoreras. Som standard ignoreras inga värden.!Skanna matrisen efter kolumn. Matrisen genomsöks som standard efter rad."}, "TOROW": {"a": "(matris, [ignorera], [scan_by_column])", "d": " <PERSON><PERSON>r matrisen som en rad.", "ad": " Matrisen eller referensen som ska returneras som en rad.! Anger om vissa typer av värden ska ignoreras. Som standard ignoreras inga värden.! Skanna matrisen efter kolumn. Matrisen genomsöks som standard efter rad."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Radbryter en rad- eller kolumnvektor efter angivet antal värden.", "ad": "Vektorn eller referensen som ska radbrytas.!Maximalt antal värden per kolumn.!Värdet som ska fyllas i. Standardvärdet är #N/A."}, "TAKE": {"a": "(matris, rader, [kolumner])", "d": "Returnerar rader eller kolumner fr<PERSON>n matrisens start eller slut.", "ad": "Matrisen som rader eller kolumner ska hämtas från.!Antalet rader att ta. Ett negativt värde tar från slutet av matrisen.!Antalet kolumner att ta. Ett negativt värde tar från slutet av matrisen."}, "DROP": {"a": "(matris, rader, [kolumner])", "d": "Tar bort rader eller kolumner fr<PERSON>n matrisens start eller slut.", "ad": "Matrisen som rader eller kolumner ska tas bort från.!Antalet rader att ta. Ett negativt värde sjunker från slutet av matrisen.!Antalet kolumner som ska tas bort. Ett negativt värde sjunker från slutet av matrisen."}, "SEQUENCE": {"a": "(rader, [kolumner], [start], [steg])", "d": "Returnerar en sekvens med tal", "ad": "antalet rader som returneras!antalet kolumner som returneras!det första talet i sekvensen!mängden som varje efterföljande värde ska ökas i sekvensen"}, "EXPAND": {"a": "(matris, rader, [kolumner], [fyll_med])", "d": "Expanderar en matris till de angivna dimensionerna.", "ad": " Matrisen som ska expanderas.!Antalet rader i den expanderade matrisen. Om det saknas expanderas inte rader.!Antalet kolumner i den expanderade matrisen. Om det saknas expanderas inte kolumner.!Värdet att fylla med. Standardvärdet är #N/A."}, "XMATCH": {"a": "(letaupp<PERSON><PERSON><PERSON>, letauppvektor, [matchningsläge], [sökläge])", "d": "Returnerar den relativa positionen för ett objekt i en matris. Som standard krävs en exakt matchning", "ad": "är värdet att söka efter!är matrisen eller intervallet för sökning!ange hur letauppvärde ska matchas mot värdena i letauppvektor!ange sökläget som ska användas. Som standard används första till sista-sökning"}, "FILTER": {"a": "(array, include, [if_empty])", "d": "Filtrera ett intervall eller en matris", "ad": "intervallet eller matrisen som ska filtreras!en matris med booleska värden där TRUE representerar en rad eller en kolumn som ska behållas!returneras om inga objekt behålls"}, "ARRAYTOTEXT": {"a": "(matris, [format])", "d": "Returnerar en textrepresentation av en matris", "ad": "matrisen som ska representeras som text!textens format"}, "SORT": {"a": "(matris, [sortera_index], [sorteringsordning], [efter_kol])", "d": "Sorterar ett intervall eller en matris", "ad": "intervallet eller matrisen att sortera!ett tal som ange raden eller kolumnen att sortera efter!ett tal som anger önskad sorteringsordning; 1 för stigande ordning (standard), -1 för fallande ordning!ett logiskt värde som anger den önskade sorteringsriktningen: FALSKT för att sortera efter rad (standard), SANT för att sortera efter kolumn"}, "SORTBY": {"a": "(matris, efter_matris, [sorteringsordning], ...)", "d": "Sorterar ett intervall eller en matris utifrån värdena i ett motsvarande intervall eller en motsvarande matris", "ad": "intervallet eller matrisen som ska sorteras!intervallet eller matrisen som ska sorteras efter!ett tal som anger den önskade sorteringsordningen; 1 för stigande (standard), -1 för fallande"}, "GETPIVOTDATA": {"a": "(datafält; pivottabell; [fält]; [objekt]; ...)", "d": "Extraherar data som sparats i en pivottabell.", "ad": "är namnet på det datafält som du vill extrahera data från!är en referens till en cell eller ett område av celler i pivottabellen som innehåller de data du vill hämta.!fält som hänvisas till.!fältobjekt som hänvisas till."}, "IMPORTRANGE": {"a": "(kalkylarkets_webbadress; intervallsträng)", "d": "Importerar ett cellområde från ett angivet kalkylark.", "ad": "webbadressen till kalkylarket som data importeras från!intervallet som ska importeras"}}