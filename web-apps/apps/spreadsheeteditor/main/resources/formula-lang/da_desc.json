{"DATE": {"a": "(år; måned; dag)", "d": "Returnerer det tal, der repræsenterer datoen i dato- og klokkeslætskoden", "ad": "er et tal mellem 1900 eller 1904 (afhængigt af datosystemet i projektmappen) og 9999!er et tal mellem 1 og 12, der repræsenterer måneden i året!er et tal mellem 1 og 31, der repræsenterer dagen i måneden"}, "DATEDIF": {"a": "(startdato; slutdato; enhed)", "d": "<PERSON><PERSON><PERSON><PERSON> antallet af dage, måneder eller år mellem to datoer", "ad": "En dato, der repræsenterer den første dato eller startdatoen for en given periode!En dato, der repræsenterer den sidste dato, eller slutdato<PERSON>, i perioden!Den type oplysninger, der skal returneres"}, "DATEVALUE": {"a": "(datotekst)", "d": "Konverterer en dato i form af tekst til et tal, der repræsenterer datoen i dato/klokkeslætskoden", "ad": "er tekst, der repræsenterer en dato i et Spreadsheet Editor-datoformat mellem 1/1/1900 eller 1/1/1904 (afhængigt af projektmappens datosystem) og 12/31/9999"}, "DAY": {"a": "(serienr)", "d": "Returnerer dagen i måneden, et tal mellem 1 og 31.", "ad": "er et tal i den dato- og klokkeslætskode, der anvendes af Spreadsheet Editor"}, "DAYS": {"a": "(slutdato; startdato)", "d": "Returnerer antal dage mellem de to datoer.", "ad": "startdato og slutdato er de to datoer, du vil finde antal dage mellem!startdato og slutdato er de to datoer, du vil finde antal dage mellem"}, "DAYS360": {"a": "(startdato; slutdato; [metode])", "d": "<PERSON><PERSON><PERSON><PERSON> antallet af dage mellem to datoer på baggrund af et år på 360 dage (12 måneder à 30 dage)", "ad": "startdato og slutdato er de to datoer, som du vil have oplyst antallet af dage imellem!startdato og slutdato er de to datoer, som du vil have oplyst antallet af dage imellem!er en logisk værdi, der angiver beregningsmetoden: U.S. (NASD) = FALSK eller udeladt; Europæisk = SAND."}, "EDATE": {"a": "(<PERSON><PERSON><PERSON>; må<PERSON>er)", "d": "Returnerer serienum<PERSON>t for da<PERSON><PERSON>, der er det angivne antal måneder før eller efter startdatoen", "ad": "er et serielt dato<PERSON>, der repræsenterer startdatoen!er antal måneder før eller efter startdatoen"}, "EOMONTH": {"a": "(<PERSON><PERSON><PERSON>; må<PERSON>er)", "d": "Returnerer serienummeret på den sidste dag i måneden, før eller efter et specificeret antal måneder", "ad": "er et serielt dato<PERSON>, der repræsenterer startdatoen!er antal måneder før eller efter startdatoen"}, "HOUR": {"a": "(serienr)", "d": "Returnerer timen som et tal mellem 0 (24:00) og 23 (23:00).", "ad": "er et tal i den dato- og klokkeslætskode, der anvendes af Spreadsheet Editor, eller tekst i klokkeslætsformat, f.eks. 16:48:00 eller 04:48:00 PM"}, "ISOWEEKNUM": {"a": "(dato)", "d": "Returnerer tallet for ISO-ugenummeret i året for en given dato", "ad": "er den dato/klokkeslætskode, der bruges til beregning af dato og klokkeslæt i Spreadsheet Editor"}, "MINUTE": {"a": "(serienr)", "d": "Returnerer minuttet, et tal mellem 0 og 59.", "ad": "er et tal i den dato- og klokkeslætskode, der anvendes af Spreadsheet Editor, eller tekst i klokkeslætsformat, f.eks. 16:48:00 eller 04:48:00 PM"}, "MONTH": {"a": "(serienr)", "d": "Returnerer m<PERSON><PERSON><PERSON>, et tal mellem 1 (januar) og 12 (december).", "ad": "er et tal i den dato- og klokkeslætskode, der anvendes af Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(startdato; slutdato; [feriedage])", "d": "Returnerer antal hele arbejdsdage mellem to datoer", "ad": "er et serielt datotal, der angiver startdatoen!er et serielt datotal, der angiver slutdatoen!er et valgfrit antal serielle datotal, der skal udelades fra beregningen, f.eks. helligdage"}, "NETWORKDAYS.INTL": {"a": "(startdato; slutdato; [weekend]; [feriedage])", "d": "Returnerer antallet af hele arbejdsdage mellem to datoer med brugerdefinerede weekendparametre", "ad": "er et serielt datotal, som repræsenterer startdatoen!er et serielt datotal, som repræsenterer slutdatoen!er et tal eller en streng, der angiver, hvornår weekender forekommer!er et valgfrit sæt af et eller flere serielle datotal, der skal udelades fra arbejdskalenderen, som f.eks. offentlige helligdage og flydende helligdage"}, "NOW": {"a": "()", "d": "Returnerer den aktuelle dato og det aktuelle klokkeslæt formateret som dato og klokkeslæt.", "ad": ""}, "SECOND": {"a": "(serienr)", "d": "Returnerer sekundet, et tal mellem 0 og 59.", "ad": "er et tal i den dato- og klokkeslætskode, der anvendes af Spreadsheet Editor, eller tekst i klokkeslætsformat, f.eks. 16:48:23 eller 04:48:47 PM"}, "TIME": {"a": "(time; minut; sekund)", "d": "<PERSON>n<PERSON><PERSON> timer, minutter og sekunder angivet som tal i et serienummer, der er formateret med et klokkeslætsformat", "ad": "er et tal mellem 0 og 23, der repræsenterer timen!er et tal mellem 0 og 59, der repræsenterer minuttet!er et tal mellem 0 og 59, der repræsenterer sekundet"}, "TIMEVALUE": {"a": "(tid)", "d": "Konverterer et klokkeslæt i form af tekst til et serienummer for et klokkeslæt, et tal mellem 0 (12:00:00 AM) og 0,999988426 (11:59:59 PM). Formatér tallet med et klokkeslætsformat efter angivelse af formlen", "ad": "er en tekststreng, der indeholder et klokkeslæt i et af de klokkeslætsformater, der findes i Spreadsheet Editor (datooplysninger i strengen ignoreres)"}, "TODAY": {"a": "()", "d": "Returnerer dags dato formateret som en dato.", "ad": ""}, "WEEKDAY": {"a": "(serienr; [type])", "d": "Returnerer et tal mellem 1 og 7, som repræsenterer ugedagen i datoen.", "ad": "er et tal, der repræsenterer en dato!er et tal: for søndag=1 til og med lørdag=7 bruges 1; for mandag=1 til og med søndag=7 bruges 2; for mandag=0 til og med søndag=6 bruges 3"}, "WEEKNUM": {"a": "(serienr; [returtype])", "d": "Konverterer ugenummeret i året", "ad": "er den dato-og klokkeslætskode, der bruges af Spreadsheet Editor til dato- og tidsberegninger!er et tal (1 eller 2), som bestemmer returværdiens type"}, "WORKDAY": {"a": "(startdato; dage; [feriedage])", "d": "Returnerer det serielle datotal for dagen før eller efter et specifikt antal arbejdsdage", "ad": "er et serielt datotal, der angiver startdatoen!er antallet af ikke-weekender og ikke-feriedage før eller efter startdatoen!er en valgfri matrix af et eller flere serielle datotal, der skal udelades fra arbejdskalenderen, f.eks. offentlige helligdage og flydende helligdage"}, "WORKDAY.INTL": {"a": "(startdato; dage; [weekend]; [feriedage])", "d": "Returnerer det se<PERSON>le da<PERSON><PERSON> for dagen før eller efter det angivne antal arbejdsdage med brugerdefinerede  weekendparametre", "ad": "er et serielt datotal, som repræsenterer startdatoen!er antallet af ikke-weekenddage og ikke-feriedage før eller efter startato!er et tal eller en streng, der angiver, hvorn<PERSON>r det er weekend!er en valgfri matrix af et eller flere serielle datotal, der skal udelades af arbejdskalenderen, som f.eks. offentlige helligdage og flydende helligdage"}, "YEAR": {"a": "(serienr)", "d": "Returnerer året i en dato, et heltal mellem 1900 og 9999.", "ad": "er et tal i den dato- og klokkeslætskode, der bruges af Spreadsheet Editor"}, "YEARFRAC": {"a": "(startdato; slutdato; [datotype])", "d": "Returnerer <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, der repræsenterer antal hele dage mellem startdato og slutdato", "ad": "er et serielt datotal, der angiver startdatoen!er et serielt datotal, der angiver slutdatoen!er den datotype, der skal anvendes"}, "BESSELI": {"a": "(x; n)", "d": "Returnerer den modificerede Bessel-funktion In(x)", "ad": "er den værdi, hvor funktionen skal evalueres!er rækkefølgen for Bessel-funktionen"}, "BESSELJ": {"a": "(x; n)", "d": "Returner <PERSON><PERSON>-funktionen Jn(x)", "ad": "er den værdi, hvor funktionen skal evalueres!er rækkefølgen for Bessel-funktionen"}, "BESSELK": {"a": "(x; n)", "d": "Returnerer den modificerede Bessel-funktion Kn(x)", "ad": "er den værdi, hvor funktionen skal evalueres!er rækkefølgen for funktionen"}, "BESSELY": {"a": "(x; n)", "d": "Returner <PERSON><PERSON>-funk<PERSON><PERSON> Yn(x)", "ad": "er den værdi, hvor funktionen skal evalueres!er rækkefølgen for funktionen"}, "BIN2DEC": {"a": "(tal)", "d": "Konverterer et binært tal til et decimaltal", "ad": "er det binære tal, der skal konverteres"}, "BIN2HEX": {"a": "(tal; [pladser])", "d": "Konvertér et binært tal til et hexadecimaltal", "ad": "er det binære tal, der skal konverteres!er det antal tegn, der skal anvendes"}, "BIN2OCT": {"a": "(tal; [pladser])", "d": "Konvertér et binært tal til et oktaltal", "ad": "er det binære tal, der skal konverteres!er det antal tegn, der skal anvendes"}, "BITAND": {"a": "(tal1; tal2)", "d": "Returnerer et bitbaseret 'Og' af to tal", "ad": "er den decimale repræsentation af det binære tal, du vil evaluere!er den decimale repræsentation af det binære tal, du vil evaluere"}, "BITLSHIFT": {"a": "(tal; forskydning)", "d": "Returnerer et tal forskudt til venstre med forskydning bit", "ad": "er decimaltallet af det binære tal, som du vil evaluere!er det antal bit, du vil forskyde tal til venstre med"}, "BITOR": {"a": "(tal1; tal2)", "d": "Returnerer et bitbaseret 'Eller' af to tal", "ad": "er den decimale repræsentation af det binære tal, du vil evaluere!er den decimale repræsentation af det binære tal, du vil evaluere"}, "BITRSHIFT": {"a": "(tal; forskydning)", "d": "Returnerer et tal forskudt til højre med forskydning bit", "ad": "er decimaltallet af det binære tal, som du vil evaluere!er det antal bit, du vil forskyde tal til højre med"}, "BITXOR": {"a": "(tal1; tal2)", "d": "Returnerer et bitbaseret 'Eksklusivt eller' af to tal", "ad": "er den decimale repræsentation af det binære tal, du vil evaluere!er den decimale repræsentation af det binære tal, du vil evaluere"}, "COMPLEX": {"a": "(reel_koefficient; imag_koefficient; [suffiks])", "d": "Konverterer reelle og imaginære koefficienter til komplekse tal", "ad": "er den reelle koefficient for det komplekse tal!er den imaginære koefficient for det komplekse tal!er det imaginære elements suffiks for det komplekse tal"}, "CONVERT": {"a": "(tal; fra_enhed; til_enhed)", "d": "Konverterer et tal fra en måleenhed til en anden", "ad": "er den værdi, der skal konverteres fra!er enheden for tal!er enheden for resultatet"}, "DEC2BIN": {"a": "(tal; [pladser])", "d": "Konverterer et decimaltal til et binært tal", "ad": "er det decimal<PERSON><PERSON>, der skal konverteres!er det antal tegn, der skal anvendes"}, "DEC2HEX": {"a": "(tal; [pladser])", "d": "Konverterer et decimaltal til et hexadecimaltal", "ad": "er det decimal<PERSON><PERSON>, der skal konverteres!er det antal tegn, der skal anvendes"}, "DEC2OCT": {"a": "(tal; [pladser])", "d": "Konverterer et decimaltal til et oktaltal", "ad": "er det decimal<PERSON><PERSON>, der skal konverteres!er det antal tegn, der skal anvendes"}, "DELTA": {"a": "(tal1; [tal2])", "d": "<PERSON><PERSON><PERSON><PERSON>, om to værdier er ens", "ad": "er det første tal!er det andet tal"}, "ERF": {"a": "(nedre_grænse; [øvre_grænse])", "d": "Returnerer fejlfunktionen", "ad": "er nederste grænse for integrering af FEJLFUNK!er øverste grænse for integrering af FEJLFUNK"}, "ERF.PRECISE": {"a": "(X)", "d": "Returnerer fejlfunktionen", "ad": "er den nedre grænse for integrering af ERF.PRECISE"}, "ERFC": {"a": "(x)", "d": "Returnerer den komplementære fejlfunktion", "ad": "er nederste grænse for integrering af FEJLFUNK"}, "ERFC.PRECISE": {"a": "(X)", "d": "Returnerer den komplementære fejlfunktion", "ad": "er den nedre grænse for integrering af ERFC.PRECISE"}, "GESTEP": {"a": "(tal; [trin])", "d": "<PERSON><PERSON><PERSON><PERSON>, om et tal er større end en tærskelværdi", "ad": "er den værdi, der sammenlignes med trin!er tærskelværdien"}, "HEX2BIN": {"a": "(tal; [pladser])", "d": "Konverterer et hexadecimaltal til et binært tal", "ad": "er det hexadecimaltal, der skal konverteres!er det antal tegn, der skal anvendes"}, "HEX2DEC": {"a": "(tal)", "d": "Konverterer et hexadecimaltal til et decimaltal", "ad": "er det hexadecimaltal, der skal konverteres"}, "HEX2OCT": {"a": "(tal; [pladser])", "d": "Konverterer et hexadecimaltal til et oktaltal", "ad": "er det hexadecimaltal, der skal konverteres!er det antal tegn, der skal anvendes"}, "IMABS": {"a": "(ital)", "d": "Returnerer den absolutte værdi (modulus) af et komplekst tal", "ad": "er et komplekst tal, for hvilket den absolutte værdi ønskes"}, "IMAGINARY": {"a": "(ital)", "d": "Returnerer den imaginære koefficient af et komplekst tal", "ad": "er et komplekst tal, for hvilket den imaginære koefficient ønskes"}, "IMARGUMENT": {"a": "(ital)", "d": "Returnerer argumentet q udtrykt i radianer", "ad": "er et komplekst tal, for hvilket argumentet ønskes"}, "IMCONJUGATE": {"a": "(ital)", "d": "Returnerer den komplekst konjugerede af et komplekst tal", "ad": "er et komplekst tal, for hvilket den konjugerede ønskes"}, "IMCOS": {"a": "(ital)", "d": "Returnerer et komplekst tals cosinus", "ad": "er et komplekst tal, for hvilket cosinus ønskes"}, "IMCOSH": {"a": "(ital)", "d": "Returnerer den hyperbolske cosinus af et komplekst tal", "ad": "er et komplekst tal, du vil finde den hyperbolske cosinus for"}, "IMCOT": {"a": "(ital)", "d": "Returnerer cotangens af et komplekst tal", "ad": "er et komplekst tal, du vil have cotangens af"}, "IMCSC": {"a": "(ital)", "d": "Returnerer cosekanten af et komplekst tal", "ad": "er et komplekst tal, du vil have cosekanten af"}, "IMCSCH": {"a": "(ital)", "d": "Returnerer den hyperbolske cosekant af et komplekst tal", "ad": "er et komplekst tal, du vil have den hyperbolske cosekant af"}, "IMDIV": {"a": "(ital1; ital2)", "d": "Returnerer kvo<PERSON>ten af to komplekse tal", "ad": "er den komplekse tæller eller dividend!er den komplekse nævner eller divisor"}, "IMEXP": {"a": "(ital)", "d": "Returnerer et komplekst tals eksponentialfunktion", "ad": "er et komplekst tal, for hvilket eksponentialfunktionen ønskes"}, "IMLN": {"a": "(ital)", "d": "Returnerer et komplekst tals naturlige logaritme", "ad": "er et komplekst tal, for hvilket den naturlige logaritme ønskes"}, "IMLOG10": {"a": "(ital)", "d": "Returnerer et komplekst tals 10-tals logaritme", "ad": "er et komplekst tal, for hvilket 10-tals logaritmen ønskes"}, "IMLOG2": {"a": "(ital)", "d": "Returnerer et komplekst tals 2-tals logaritme", "ad": "er et komplekst tal, for hvilket 2-tals logaritmen ønskes"}, "IMPOWER": {"a": "(ital; tal)", "d": "Returnerer et komplekst tal opløftet i en heltalspotens", "ad": "er et komplekst tal, der ønskes opløftet i potens!er potensen, som det komplekse tal skal opløftes i"}, "IMPRODUCT": {"a": "(ital1; [ital2]; ...)", "d": "Returnerer produktet af 1 til 255 komplekse tal", "ad": "Ital1, <PERSON>al2,... er 1 til 255 komplekse tal, der skal multipliceres."}, "IMREAL": {"a": "(ital)", "d": "Returnerer den reelle koefficient af et komplekst tal", "ad": "er et komplekst tal, for hvilket den reelle koefficient ønskes"}, "IMSEC": {"a": "(ital)", "d": "Returnerer sekanten af et komplekst tal", "ad": "er et komplekst tal, du vil have sekanten af"}, "IMSECH": {"a": "(ital)", "d": "Returnerer den hyperbolske sekant af et komplekst tal", "ad": "er et komplekst tal, du vil have den hyperbolske sekant af"}, "IMSIN": {"a": "(ital)", "d": "Returnerer et komplekst tals sinus", "ad": "er et komplekst tal, for hvilket sinus ønskes"}, "IMSINH": {"a": "(ital)", "d": "Returnerer den hyperbolske sinus af et komplekst tal", "ad": "er et komplekst tal, du vil finde den hyperbolske sinus for"}, "IMSQRT": {"a": "(ital)", "d": "Returnerer kvadratroden af et komplekst tal", "ad": "er et komplekst tal, for hvilket kvadratroden ønskes"}, "IMSUB": {"a": "(ital1; ital2)", "d": "Returnerer for<PERSON><PERSON> mellem 2 komplekse tal", "ad": "er det komplekse tal, hvorfra ital2 skal trækkes fra!er det komplekse tal, der skal trækkes fra ital1"}, "IMSUM": {"a": "(ital1; [ital2]; ...)", "d": "Returnerer summen af komplekse tal", "ad": "er 1 til 255 komplekse tal, der skal tilføjes"}, "IMTAN": {"a": "(ital)", "d": "Returnerer tangens af et komplekst tal", "ad": "er et komplekst tal, du vil have tangens af"}, "OCT2BIN": {"a": "(tal; [pladser])", "d": "Konverterer et oktaltal til et binært tal", "ad": "er det oktaltal, der skal konverteres!er det antal tegn, der skal anvendes"}, "OCT2DEC": {"a": "(tal)", "d": "Konverterer et oktaltal til et decimaltal", "ad": "er det oktaltal, der skal konverteres"}, "OCT2HEX": {"a": "(tal; [pladser])", "d": "Konverterer et oktaltal til et hexadecimaltal", "ad": "er det oktaltal, der skal konverteres!er det antal tegn, der skal anvendes"}, "DAVERAGE": {"a": "(database; felt; kriterier)", "d": "Be<PERSON>gner gennemsnittet af værdierne i en kolonne på en liste eller i en database, der svarer til de betingelser, du angiver", "ad": "er det celleo<PERSON><PERSON><PERSON><PERSON>, listen eller databasen består af. En database er en liste over beslægtede data!er enten kolonnenavnene med dobbelte anførselstegn eller et tal, der repræsenterer kolonnens placering på listen!er det celleomr<PERSON><PERSON>, der indeholder de kriterier, du angiver. Området omfatter et kolonnenavn og en celle under mærkaten på et kriterium"}, "DCOUNT": {"a": "(database; felt; kriterier)", "d": "<PERSON><PERSON><PERSON> de <PERSON>, der indeholder tal, i feltet (kolonnen) med dokumenter i den database, der svarer til de angivne kriterier", "ad": "er det celleomr<PERSON><PERSON>, der udgør listen eller databasen. En database er en liste over beslægtede data!er enten kolonnemærkaterne i dobbelte anførselstegn eller et tal, der repræsenterer kolonnens placering på listen!er celleo<PERSON><PERSON><PERSON><PERSON>, er indeholder de kriterier, som du angiver. Området omfatter en kolonnemærkat og én celle under mærkaten for en betingelse"}, "DCOUNTA": {"a": "(database; felt; kriterier)", "d": "<PERSON><PERSON>ller udfyldte celler i feltet (kolonnen) med poster i den database, der svarer til de kriterier, du angiver", "ad": "er det celleomr<PERSON><PERSON>, listen eller databasen består af. En database er en liste over beslægtede data!angiver enten kolonnemærkaten sat i dobbelt anførselstegn eller et tal, der repræsenterer kolonnens placering på listen!angiver et celleområde, der indeholder de kriterier, du angiver. Området omfatter et kolonnenavn og én celle under mærkaten på et kriterium"}, "DGET": {"a": "(database; felt; kriterier)", "d": "Uddrager en enkelt post fra en database, der opfylder de angivne betingelser", "ad": "er det celleomr<PERSON><PERSON>, listen eller databasen består af. En database er en liste over beslægtede data!angiver enten kolonnemærkaten i dobbelt anførselstegn eller et tal, der repræsenterer kolonnernes placering på listen!angiver celleområdet med de kriterier, du angiver. Området omfatter et kolonnemærkaten og en celle under mærkaten på et kriterium"}, "DMAX": {"a": "(database; felt; kriterier)", "d": "Returnerer den største værdi i feltet (kolonnen) med dokumenter i den database, der svarer til de kriterier, du angiver", "ad": "er det celleomr<PERSON><PERSON>, der udgør listen eller databasen. En database er en liste over beslægtede data!angiver enten kolonnemærkaten i dobbelte anførseltegn eller et tal, der repræsenterer kolonnens placering på listen!angiver et celleområde, der indeholder de kriterier, du angiver. Området indeholder et kolonnenavn og én celle under mærkaten på et kriterium"}, "DMIN": {"a": "(database; felt; kriterier)", "d": "Returnerer den mindste værdi blandt markerede databaseposter, der svarer til de kriterier, du angiver", "ad": "er det celleområ<PERSON>, der udgør listen eller databasen. En database er en liste over beslægtede data!angiver enten kolonnemærkaten i dobbelt anførselstegn eller et tal, der repræsenterer kolonnens placering på listen!angiver et celleområde, der indeholder de kriterier, du angiver. Celleområdet indeholder et kolonnenavn og én celle under mærkaten på et krierium"}, "DPRODUCT": {"a": "(database; felt; kriterier)", "d": "Multiplicerer værdierne i feltet (kolonnen) med poster i databasen, der opfylder de betingelser, du har angivet", "ad": "er det celleomr<PERSON><PERSON>, der udgør listen eller databasen. En database er en liste over beslægtede data!angiver enten kolonnemærkaten i dobbelt anførselstegn eller et tal, der repræsenterer kolonnens placering på listen!angiver celleområdet med de kriterier, du angiver. Området omfatter et kolonnemærkaten og en celle under mærkaten på et kriterium"}, "DSTDEV": {"a": "(database; felt; kriterier)", "d": "Beregner et skøn over standardafvigelsen baseret på en stikprøve af markerede databaseposter", "ad": "er det celleomr<PERSON><PERSON>, der udgør listen eller databasen. En database er en liste over beslægtede data!angiver enten kolonnemærkaten i dobbelt anførselstegn eller et tal, der repræsenterer kolonnens placering på listen!angiver celleområdet med de kriterier, du angiver. Området omfatter et kolonnenavn og en celle under mærkaten på et kriterium"}, "DSTDEVP": {"a": "(database; felt; kriterier)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>sen baseret på hele populationen af markerede databaseposter", "ad": "er det celleomr<PERSON><PERSON>, der udgør listen eller databasen. En database er en liste over beslægtede data!Angiver kolonnemærkaten i dobbelt anførselstegn eller et tal, der repræsenterer kolonnens placering på listen!angiver celleområdet med de kriterier, du specificerer. Området omfatter et kolonnenavn og én celle under mærkaten på et kriterium"}, "DSUM": {"a": "(database; felt; kriterier)", "d": "<PERSON><PERSON><PERSON> de tal i feltet (kolonnen) med poster i databasen, der opfylder de angivne betingelser sammen", "ad": "er det celleområ<PERSON>, der udgør listen eller databasen. En database er en liste over beslægtede data!angiver kolonnemærkaten i dobbelt anførselstegn eller et tal, der repræsenterer kolonnens placering på listen!angiver et celleområde med de kriterier, du angiver. Området omfatter et kolonnenavn og én celle under mærkaten på et kriterium"}, "DVAR": {"a": "(database; felt; kriterier)", "d": "<PERSON><PERSON><PERSON><PERSON> et skøn over variansen baseret på en stikprøve af markerede databaseposter", "ad": "er det celleomr<PERSON><PERSON>, der udgør listen eller databasen. En database er en liste over beslægtede data!angiver enten kolonnemærkaten i dobbelt anførselstegn eller et tal, der repræsenterer kolonnernes placering på listen!angiver et celleområde med de kriterier, du angiver. Området omfatter et kolonnenavn og én celle under mærkaten på et kriterium"}, "DVARP": {"a": "(database; felt; kriterier)", "d": "Beregner varians baseret på hele populationen af markerede databaseposter", "ad": "er det celleomr<PERSON><PERSON>, der udgør listen eller databasen. En database er en liste over beslægtede data!er enten kolonnenavnene i dobbelte anførselstegn eller et tal, der repræsenterer kolonnens placering på listen!angiver et celleområde med de kriterier, du angiver. Området omfatter et kolonnenavn og en celle under mærkaten på et kriterium"}, "CHAR": {"a": "(tal)", "d": "Returnerer det tegn fra computerens tegnsæt, som kodenummeret angiver", "ad": "er et tal mellem 1 og 255, som angiver, hvilket tegn der ønskes"}, "CLEAN": {"a": "(tekst)", "d": "<PERSON><PERSON><PERSON> alle tegn, der ikke kan udskrives, fra tekst", "ad": "er alle regnearksoplysninger, h<PERSON><PERSON> tegn, der ikke kan udskrive<PERSON>, skal fjernes"}, "CODE": {"a": "(tekst)", "d": "Returnerer en numerisk kode fra computerens tegnsæt for det første tegn i en tekststreng", "ad": "er den tekst, hvor du vil have oplyst koden for det første tegn"}, "CONCATENATE": {"a": "(tekst1; [tekst2]; ...)", "d": "Sammenkæder flere tekststrenge til én tekststreng", "ad": "er 1-255 tekststre<PERSON>, der skal kædes sammen til én tekststreng. Det kan være tekststrenge, tal eller referencer til enkelte celler"}, "CONCAT": {"a": "(tekst1; ...)", "d": "Sammenkæder en liste eller et område af tekststrenge", "ad": "er 1 til 254 tekststrenge eller o<PERSON>, der skal kædes sammen til én tekststreng"}, "DOLLAR": {"a": "(tal; [decimaler])", "d": "Konverterer et tal til tekst vha. valutaformat", "ad": "er et tal, en reference til en celle, der indeholder et tal, eller en formel, der evalueres som et tal!angiver antallet af cifre til højre for decimaltegnet. Tallet afrundes efter behov. Hvis udeladt er decimaler = 2"}, "EXACT": {"a": "(tekst1; tekst2)", "d": "<PERSON><PERSON><PERSON><PERSON>, om to tekststrenge er helt identiske, og returnerer SAND eller FALSK. EKSAKT skelner mellem store og små bogstaver", "ad": "er den første tekststreng!er den anden tekststreng"}, "FIND": {"a": "(find_tekst; i_tekst; [start_ved])", "d": "Returnerer startpositionen for en tekststreng i en anden tekststreng. FIND skelner mellem store og små bogstaver", "ad": "er den tekst, der skal findes. Brug dobbelte anførselstegn (tom tekst) for at opfylde kriteriet for det første tegn i I_tekst; jokertegn er ikke tilladt!er den tekst, der indeholder den tekst, der skal findes!angiver det tegn, som søgningen starter med. Første tegn i I_teksten er tegn nummer 1. <PERSON><PERSON> det udelades, Start_ved = 1"}, "FINDB": {"a": "(find_tekst; i_tekst; [start_ved])", "d": "Finder én tekststreng inden i en anden tekststreng og returnerer nummeret på den første strengs startposition fra det første tegn i den anden tekststreng, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk", "ad": "er den tekst, der skal findes. Brug dobbelte anførselstegn (tom tekst) for at opfylde kriteriet for det første tegn i I_tekst; jokertegn er ikke tilladt!er den tekst, der indeholder den tekst, der skal findes!angiver det tegn, som søgningen starter med. Første tegn i I_teksten er tegn nummer 1. <PERSON><PERSON> det udelades, Start_ved = 1"}, "FIXED": {"a": "(tal; [decimaler]; [ingen_punktummer])", "d": "<PERSON><PERSON><PERSON><PERSON> et tal til det angivne antal decimaler og returnerer resultatet som tekst med eller uden kommaer", "ad": "er det tal, du vil afrunde og konvertere til tekst!er antallet af cifre til højre for decimaltegnet. Hvis feltet ikke udfylde<PERSON>, sættes Decimaler = 2!er en logisk værdi: Vis ikke kommaerne i den returnerede tekst = SAND; vis ikke kommaerne i den returnerede tekst = FALSK eller udelades"}, "LEFT": {"a": "(tekst; [antal_tegn])", "d": "Returnerer det angivne antal tegn fra begyndelsen af en tekststreng", "ad": "er den tekststreng, der indeholder de tegn, du vil uddrage!angiver, hvor mange tegn VENSTRE skal uddrage. Sættes til 1, hvis feltet ikke udfyldes"}, "LEFTB": {"a": "(tekst; [antal_tegn])", "d": "Returnerer det eller de første tegn i en tekststreng baseret på det antal byte, du angiver, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk", "ad": "er den tekststreng, der indeholder de tegn, du vil uddrage!angiver, hvor mange tegn LEFTB skal uddrage. Sættes til 1, hvis feltet ikke udfyldes"}, "LEN": {"a": "(tekst)", "d": "Returnerer antallet af tegn i en tekststreng", "ad": "er den tekst, som du vil finde længden på. Mellemrum tæller som tegn"}, "LENB": {"a": "(tekst)", "d": "Returnerer det antal byte, der bruges til at repræsentere tegnene i en tekststreng, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk", "ad": "er den tekst, som du vil finde længden på. Mellemrum tæller som tegn"}, "LOWER": {"a": "(tekst)", "d": "Konverterer tekst til små bogstaver", "ad": "er den tekst, der skal konverteres til små bogstaver. <PERSON>gn, som ikke er bogstaver, konverteres ikke"}, "MID": {"a": "(tekst; start_ved; antal_tegn)", "d": "Returnerer tegnene fra midten af en tekststreng ved angivelse af startposition og længde", "ad": "er den tekststreng, du vil uddrage tegnene fra!angiver positionen for det første tegn, der skal uddrages. Det første tegn i Tekst er 1!angiver, hvor mange tegn der skal returneres fra Tekst"}, "MIDB": {"a": "(tekst; start_ved; antal_tegn)", "d": "Returnerer et bestemt antal tegn fra en tekststreng fra og med den startposition, du angiver, og på basis af det antal byte, du angiver, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk", "ad": "er den tekststreng, du vil uddrage tegnene fra!angiver positionen for det første tegn, der skal uddrages. Det første tegn i Tekst er 1!angiver, hvor mange tegn der skal returneres fra Tekst"}, "NUMBERVALUE": {"a": "(tekst; [decimaltegn]; [gruppeseparator])", "d": "Konverterer tekst til tal ifølge landestandarden", "ad": "er den streng, som repræsenterer det tal, du vil konvertere!er det tegn, der bruges som decimaltegn i strengen!er det tegn, der bruges som tusindtalsseparator i strengen"}, "PROPER": {"a": "(tekst)", "d": "Konverterer første bogstav i hvert ord til stort og resten af teksten til små bogstaver", "ad": "er tekst i anførselstegn, en formel, der returnerer tekst, eller en reference til en celle, der indeholder den tekst, som delvist skal skrives med stort"}, "REPLACE": {"a": "(gammel_tekst; start_ved; antal_tegn; ny_tekst)", "d": "Erstatter en del af en tekststreng med en anden tekststreng", "ad": "er den tekst, hvor der skal udskiftes et antal tegn!er positionen for det tegn i gammel_tekst, der skal erstattes med ny_tekst!er det antal tegn i gammel_tekst, der skal erstattes!er den tekst, der skal erstatte tegn i gammel_tekst"}, "REPLACEB": {"a": "(gammel_tekst; start_ved; antal_tegn; ny_tekst)", "d": "Erstatter en del af en tekststreng med en anden tekststreng baseret på det antal byte, du angiver, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk", "ad": "er den tekst, hvor der skal udskiftes et antal tegn!er positionen for det tegn i gammel_tekst, der skal erstattes med ny_tekst!er det antal tegn i gammel_tekst, der skal erstattes!er den tekst, der skal erstatte tegn i gammel_tekst"}, "REPT": {"a": "(tekst; antal_gange)", "d": "Gentager tekst et givet antal gange. Brug GENTAG til at fylde en celle med et antal forekomster af en tekststreng", "ad": "er den tekst, der skal gentages!er et positivt tal, der specificerer det antal gange, tekst skal gentages"}, "RIGHT": {"a": "(tekst; [antal_tegn])", "d": "Returnerer det angivne antal tegn fra slutningen af en tekststreng", "ad": "er den tekststreng, der indeholder de tegn, der skal uddrages!angiver, hvor mange tegn der skal uddrages. Sættes til 1, hvis feltet ikke udfyldes"}, "RIGHTB": {"a": "(tekst; [antal_tegn])", "d": "Returnerer det eller de sidste tegn i en tekststreng baseret på det antal bytes, du angiver, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk", "ad": "er den tekststreng, der indeholder de tegn, der skal uddrages!angiver, hvor mange tegn der skal uddrages. Sættes til 1, hvis feltet ikke udfyldes"}, "SEARCH": {"a": "(find_tekst; i_tekst; [start_ved])", "d": "Returnerer et tal, der repræsenterer placeringen af et tegn eller en tekststreng i en anden tekststreng, læst fra venstre mod højre (skelner ikke mellem store og små bogstaver)", "ad": "er den tekst, der skal findes. Du kan bruge jokertegnene ? og  *. Brug ~? og ~* til at finde tegnene ? og *!er den tekst, der skal søges i efter find_tekst!er placeringen af det tegn i i_tekst (talt fra venstre), hvor søgningen skal begynde. Sættes til1, hvis det udelades"}, "SEARCHB": {"a": "(find_tekst; i_tekst; [start_ved])", "d": "Finder én tekststreng inden i en anden tekststreng og returnerer nummeret på den første strengs startposition fra det første tegn i den anden tekststreng, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk", "ad": "er den tekst, der skal findes. Du kan bruge jokertegnene ? og  *. Brug ~? og ~* til at finde tegnene ? og *!er den tekst, der skal søges i efter find_tekst!er placeringen af det tegn i i_tekst (talt fra venstre), hvor søgningen skal begynde. Sættes til1, hvis det udelades"}, "SUBSTITUTE": {"a": "(tekst; gammel_tekst; ny_tekst; [forekomst])", "d": "Erstatter gammel tekst med ny tekst i en tekststreng", "ad": "er den tekst eller den reference til en celle, der indeholder den tekst, hvor der skal udskiftes tegn!er den eksisterende tekst, du vil erstatte. Hvis fordelingen af store og små bogstaver ikke er den samme i Gammel_tekst og tekst, vil UDSKIFT ikke erstatte teksten!er den tekst, der skal indsættes i stedet for gammel_tekst!angiver, hvilken forekomst af gammel_tekst der skal udskiftes. Hvis det ikke udfyldes, bliver alle forekomster af gammel_tekst udskiftet"}, "T": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er tekst, og returnerer teksten, hvis dette er tilfældet, eller returnerer dobbelte anførselstegn (en tom streng), hvis det ikke er tilfældet", "ad": "er den værdi, der skal testes"}, "TEXT": {"a": "(værdi; format)", "d": "Konverterer en værdi til tekst i et specifikt talformat", "ad": "er et tal, en formel, der evalueres til en numerisk værdi, eller en reference til en celle, der indeholder en numerisk værdi!er et talformat i form af tekst fra feltet Kategori under fanen Tal i dialogboksen Formatér celler (ikke Standard)"}, "TEXTJOIN": {"a": "(skilletegn; ignorer_tomme; tekst1; ...)", "d": "Sammenkæder en liste eller et område af tekststrenge ved hjælp af en afgrænser", "ad": "Tegn eller en streng, der skal indsættes mellem hvert enkelt tekstelement!Hvis SAND (standard) ignoreres tomme celler!er 1 til 252 tekststrenge eller om<PERSON>, der skal kædes sammen"}, "TRIM": {"a": "(tekst)", "d": "Fjerner alle mellemrum fra en tekststreng, undtagen enkeltmellemrum mellem ord", "ad": "er den tekst, som mellemrum skal fjernes fra"}, "UNICHAR": {"a": "(tal)", "d": "Returnerer det Unicode-tegn, der refereres til med den givne numeriske værdi", "ad": "er det Unicode-tal, der repræsenterer et tegn"}, "UNICODE": {"a": "(tekst)", "d": "Returnerer det tal (tegnværdi), der svarer til det første tegn i teksten", "ad": "er det tegn, du vil have Unicode-værdien for"}, "UPPER": {"a": "(tekst)", "d": "Konverterer tekst til store bogstaver", "ad": "er den tekst, der skal konverteres til store bogstaver, en reference eller en tekststreng"}, "VALUE": {"a": "(tekst)", "d": "Konverterer en tekststreng til et tal", "ad": "er teksten i anførselstegn eller en reference til en celle, der indeholder den tekst, der skal konverteres"}, "AVEDEV": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den gennemsnitlige absolutte afvigelse af datapunkter fra deres middelværdi. Argumenter kan være tal, navne, matrixer eller referencer, der indeholder tal", "ad": "er 1-255 argumenter, som den gennemsnitlige absolutte afvigelse skal beregnes for"}, "AVERAGE": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer middelvæ<PERSON>en af argumenterne, som kan være tal, nav<PERSON>, matrixer eller referencer, der indeholder tal", "ad": "er 1-255 numeriske argumenter, som du vil beregne middelværdien for"}, "AVERAGEA": {"a": "(værdi1; [værdi2]; ...)", "d": "Returnerer middelværdien af argumenterne, hvor tekst og FALSK evalueres som 0, og SAND evalueres som 1. Argumenter kan være tal, navne, matrixer eller referencer", "ad": "er 1-255 argumenter, som du vil beregne middelværdien af"}, "AVERAGEIF": {"a": "(o<PERSON><PERSON><PERSON><PERSON>; kriterier; [middel<PERSON><PERSON><PERSON><PERSON>])", "d": "Finder middel<PERSON><PERSON><PERSON>en af cellerne ud fra en given betingelse eller et givet kriterium", "ad": "er det celleomr<PERSON><PERSON>, der skal evalueres!er betingelsen eller kriteriet i form af et tal, et udtryk eller tekst, der definerer de celler, der skal bruges til at finde middelværdien!er de faktiske celler, der skal bruges til at finde middelværdien. Hvis intet angives, bruges cellerne i området"}, "AVERAGEIFS": {"a": "(mid<PERSON><PERSON><PERSON><PERSON><PERSON>; kriterieområde; kriterier; ...)", "d": "<PERSON>er middelvæ<PERSON>en af de celler, der er angivet med et sæt betingelser eller kriterier", "ad": "er de faktiske celler, der skal bruges til at finde middelværdien!er det celleområde, der skal evalueres i forhold til den konkrete betingelse!er betingelsen eller kriteriet i form af et tal, et udtryk eller tekst, der definerer de celler, der skal bruges til at finde middelværdien"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "Returnerer fordelingsfunktionen for betafordelingen", "ad": "er den værdi i intervallet fra A til B, som funktionen skal evalueres for!er en parameter til fordelingen, som skal være større end 0!er en parameter til fordelingen, som skal være større end 0!er en valgfri nedre grænse i intervallet for x. Hvis grænsen ikke angives, sættes A = 0!er en valgfri øvre grænse i intervallet for x. Hvis grænsen ikke angives, sættes B = 1"}, "BETAINV": {"a": "(sandsynlighed; alpha; beta; [A]; [B])", "d": "Returnerer den inverse fordelingsfunktion for betafordelingen (BETADIST)", "ad": "er sandsynligheden knyttet til betafordelingen!er en parameter til fordelingen, som skal være større end 0!er en parameter til fordelingen, som skal være større end 0!er en valgfri nedre grænse i intervallet for x. Hvis grænsen ikke angives, sættes A = 0!er en valgfri øvre grænse i intervallet for x. Hvis grænsen ikke angives, sættes B= 1"}, "BETA.DIST": {"a": "(x; alpha; beta; kumulativ; [A]; [B])", "d": "Returnerer fordelingsfunktionen for betafordelingen", "ad": "er den værdi i intervallet fra A til B, som funktionen skal evalueres for!er en parameter til fordelingen, som skal være større end 0!er en parameter til fordelingen, som skal være større end 0!er en logisk værdi. Hvis SAND returneres fordelingsfunktionen. Hvis FALSK returneres punktsandsynligheden!er en valgfri nedre grænse i intervallet for x. Hvis grænsen ikke angives, sættes A = 0!er en valgfri øvre grænse i intervallet for x. Hvis grænsen ikke angives, sættes B= 0"}, "BETA.INV": {"a": "(sandsynlighed; alpha; beta; [A]; [B])", "d": "Returnerer den inverse fordelingsfunktion for betafordelingen (BETA.FORDELING)", "ad": "er sandsynligheden knyttet til betafordelingen!er en parameter til fordelingen, som skal være større end 0!er en parameter til fordelingen, som skal være større end 0!er en valgfri nedre grænse i intervallet for x. Hvis grænsen ikke angives, sættes A = 0!er en valgfri øvre grænse i intervallet for x. Hvis grænsen ikke angives, sættes B= 1"}, "BINOMDIST": {"a": "(tal_s; forsøg; sandsynlighed_s; kumulativ)", "d": "Returnerer punktsandsynligheden for binomialfordelingen", "ad": "er antallet af gunstige udfald af forsøgene!er antallet af uafhængige forsøg!er sandsynligheden for et gunstigt udfald ved hvert forsøg!er en logisk værdi. Hvis SAND, returneres den kumulative fordelingsfunktion. Hvis FALSK, returneres punktsandsynligheden"}, "BINOM.DIST": {"a": "(tal_s; forsøg; sandsynligheder; akkumuleret)", "d": "Returnerer punktsandsynligheden for binomialfordelingen", "ad": "er antallet af gunstige udfald af forsøgene!er antallet af uafhængige forsøg!er sandsynligheden for gunstigt udfald af hvert forsøg!er en logisk værdi. Hvis SAND returneres fordelingsfunktionen. Hvis FALSK returneres punktsandsynligheden"}, "BINOM.DIST.RANGE": {"a": "(forsøg; sandsynlighed_s; tal_s; [tal_s2])", "d": "Returnerer sandsynlighed for et forsøgsresultat ved hjælp af binomial fordeling", "ad": "er antal uafhængige forsøg!er sandsynligheden for succes for hvert forsøg!er antal vellykkede forsøg!hvis angivet, vil funktionen returnere sandsynligheden for, at det vellykkede antal forsøg er mellem tal_s og tal_s2"}, "BINOM.INV": {"a": "(fors<PERSON>g; sandsynligheder; alpha)", "d": "Returnerer den mindste værdi, for hvilken den akkumulerede binomialfordeling er større end eller lig med en kriterieværdi", "ad": "er antallet af <PERSON>-forsøg!er sandsynligheden for et gunstigt udfald af hvert forsøg, som er et tal mellem 0 og 1 (inklusive) !er kriterieværdien, som er et tal mellem 0 og 1 (inklusive)"}, "CHIDIST": {"a": "(x; frihedsgrader)", "d": "Returnerer den højresidede sandsynlighed for en chi2-fordeling", "ad": "er den værdi (et ikke-negativt tal), du vil evaluere fordelingen for!er antallet af frihedsgrader - et tal mellem 1 og 10^10, bortset fra 10^10"}, "CHIINV": {"a": "(sandsynlighed; frihedsgrader)", "d": "Returnerer den inverse af den højresidede sandsynlighed for chi2-fordelingen", "ad": "er sandsynligheden knyttet til chi2-fordelingen. Det er en værdi mellem 0 og 1!er antallet af frihedsgrader - et tal mellem 1 og 10^10, bortset fra 10^10"}, "CHITEST": {"a": "(observeret_værdi; forventet_værdi)", "d": "Returnerer testen for u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dvs. værdien fra chi2-fordelingen for den statistiske funktion og det passende antal frihedsgrader", "ad": "er det dataområ<PERSON>, som indeholder de observerede værdier, der skal testes mod de forventede værdier!er det dataområde, der indeholder produktet af rækketotalerne og kolonnetotalerne i forhold til hovedtotalen"}, "CHISQ.DIST": {"a": "(x; fri<PERSON>sgrader; kumulativ)", "d": "Returnerer den venstresidede sandsynlighed for en chi2-fordeling", "ad": "er den værdi (et ikke-negativt tal), du vil evaluere distributionen for!er antallet af frihedsgrader- et tal mellem 1 og 10^10, bortset fra 10^10!er en logisk værdi, der, hvis kumulativ er SAND, returnerer fordelingsfunktionen, og som, hvis kumulativ er FALSK, returnerer tæthedsfunktionen"}, "CHISQ.DIST.RT": {"a": "(x; frihedsgrader)", "d": "Returnerer frak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for en chi2-fordeling", "ad": "er den værdi (et ikke-negativt tal), du vil evaluere distributionen for!er antallet af frihedsgrader- et tal mellem 1 og 10^10, bortset fra 10^10"}, "CHISQ.INV": {"a": "(sandsynlighed; frihedsgrader)", "d": "Returnerer den inverse venstresidede sandsynlighed for en chi2-fordeling", "ad": "er sandsynligheden knyttet til chi2-fordelingen. Det er en værdi mellem 0 og 1!er antallet af frihedsgrader- et tal mellem 1 og 10^10, bortset fra 10^10"}, "CHISQ.INV.RT": {"a": "(sandsynlighed; frihedsgrader)", "d": "Returnerer den inverse fraktilsandsynlighed for chi2-fordelingen", "ad": "er sandsynligheden knyttet til chi2-fordelingen. Det er en værdi mellem 0 og 1!er antallet af frihedsgrader- et tal mellem 1 og 10^10, bortset fra 10^10"}, "CHISQ.TEST": {"a": "(observeret_værdi; forventet_værdi)", "d": "Returnerer testen for u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dvs. værdien fra chi2- fordelingen for den statistiske og den passende uafhængighed", "ad": "er det dataområ<PERSON>, som indeholder de observerede værdier, der skal testes mod de forventede værdier!er det dataområde, der indeholder produktet af rækketotalerne og kolonnetotalerne i forhold til hovedtotalen"}, "CONFIDENCE": {"a": "(alpha; standardafv; størrelse)", "d": "Returnerer tillidsintervallet for middelværdien i en population", "ad": "er det signifikansniveau, der bruges til at beregne tillidsniveauet. Det er et tal større end 0 og mindre end 1!er populationens standardafvigelse for dataintervallet og antages at være kendt. Standardafv skal være større end 0!er stikprøvestørrelsen"}, "CONFIDENCE.NORM": {"a": "(alpha; standardafv; størrelse)", "d": "Returnerer tillidsintervallet for middelværdien i en population ved hjælp af en normalfordeling", "ad": "er det signifikansniveau, der bruges til at beregne tillidsniveauet og er et tal, der er større end 0 og mindre end 1!er populationens standardafvigelse for dataområdet og antages at være kendt. Standardafv skal være større end 0!er stikprøvens størrelse"}, "CONFIDENCE.T": {"a": "(alpha; standardafv; størrelse)", "d": "Returnerer tillidsintervallet for middelværdien i en population ved hjælp af t-fordelingen for en student", "ad": "er det signifikansniveau, der bruges til at beregne tillidsniveauet og er et tal, der er større end 0 og mindre end 1!er populationens standardafvigelse for dataområdet og antages at være kendt. Standardafv skal være større end 0!er stikprøvens størrelse"}, "CORREL": {"a": "(vektor1; vektor2)", "d": "Returnerer korrelationskoefficienten mellem to datasæt", "ad": "er et celleområde indeholdende værdier. Værdierne er tal, navne, vektorer eller referencer, der indeholder tal!er et andet celleområde indeholdende værdier. Værdierne er tal, navne, vektorer eller referencer, der indeholder tal"}, "COUNT": {"a": "(værdi1; [værdi2]; ...)", "d": "<PERSON><PERSON><PERSON> antallet af celler i et område, der indeholder tal", "ad": "er 1-255-argumenter, der kan indeholde eller referere til flere forskellige datatyper, men kun tal tælles"}, "COUNTA": {"a": "(værdi1; [værdi2]; ...)", "d": "<PERSON><PERSON><PERSON> antallet af celler i et område der ikke er tomme", "ad": "er 1-255-argumenter, der repræsenterer de værdier og celler, du vil tælle. Værdier kan være alle typer af oplysninger"}, "COUNTBLANK": {"a": "(område)", "d": "<PERSON><PERSON><PERSON> antallet af tomme celler i et angivet område", "ad": "er det o<PERSON><PERSON><PERSON><PERSON>, hvor du vil have oplyst antallet af tomme celler"}, "COUNTIF": {"a": "(omr<PERSON><PERSON>; kriterier)", "d": "<PERSON><PERSON><PERSON> antallet af celler i et område, der svarer til de givne betinge<PERSON>er", "ad": "er det o<PERSON><PERSON><PERSON><PERSON>, hvor du vil finde antallet af ikke-tomme celler!er betingelsen i form af et tal, et udtryk eller tekst, der angiver, hvilke celler der tælles"}, "COUNTIFS": {"a": "(kriterie<PERSON><PERSON><PERSON><PERSON>; kriterier; ...)", "d": "<PERSON><PERSON><PERSON> antallet af celler i et givet sæt betingelser eller kriterier", "ad": "er det celleomr<PERSON><PERSON>, der skal evalueres i forhold til den konkrete betingelse!er betingelsen i form af et tal, et udtryk eller tekst, der definerer de celler, som skal tælles"}, "COVAR": {"a": "(vektor1; vektor2)", "d": "<PERSON><PERSON><PERSON><PERSON>, dvs. gennemsnittet af produktet af afvigelser for hvert datapar i to datasæt", "ad": "er det første celleområde indeholdende heltalsværdier, som kan være tal, vektorer eller referencer, der indeholder tal!er det andet celleområde indeholdende heltalsværdier, som kan være tal, vektorer eller referencer, der indeholder tal"}, "COVARIANCE.P": {"a": "(matrix1; matrix2)", "d": "Returnerer kovariansen i populationen, dvs. gennemsnittet af produktet af standardafvigelsen for hvert datapar i to datasæt", "ad": "er det første celleområde indeholdende heltalsværdier, som kan være tal, vektorer eller referencer, der indeholder tal!er det andet celleområde indeholdende heltalsværdier, som kan være tal, vektorer eller referencer, der indeholder tal"}, "COVARIANCE.S": {"a": "(matrix1; matrix2)", "d": "Returnerer kovariansen i stikprøven, dvs. gennemsnittet af produktet af afvigelsen for hvert datapar i to datasæt", "ad": "er det første celleområde indeholdende heltalsværdier, som kan være tal, vektorer eller referencer, der indeholder tal!er det andet celleområde indeholdende heltalsværdier, som kan være tal, vektorer eller referencer, der indeholder tal"}, "CRITBINOM": {"a": "(forsøg; sandsynlighed_s; alpha)", "d": "Returnerer den mindste værdi, som det gælder for, at den kumulative binomiale fordeling er større end eller lig med en kriterieværdi", "ad": "er antallet af <PERSON>-forsøg!er sandsynligheden for et gunstigt udfald for hvert forsøg. Det er et tal mellem 0 og 1!er kriterieværdien. Det er et tal mellem 0 og 1"}, "DEVSQ": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer summen af datapunkternes kvadrerede afvigelser fra stikprø<PERSON>s middelværdi", "ad": "er 1-255 argumenter, en matrix eller en reference til en matrix, som SAK skal beregnes på"}, "EXPONDIST": {"a": "(x; lambda; kumulativ)", "d": "Returnerer fordelingsfunktionen for eksponentialfordelingen", "ad": "er funktionens værdi (et ikke-negativt tal)!er parameterværdien (et positivt tal)!er en logisk værdi. Hvis den er SAND, returneres den kumulative fordelingsfunktion. Hvis den er FALSK, returneres tæthedsfunktionen"}, "EXPON.DIST": {"a": "(x; lambda; kumulativ)", "d": "Returnerer eksponentialfordelingen", "ad": "er funktionens værdi (et ikke-negativt tal)!er parameterværdien (et positivt tal)!er en logisk værdi er en logisk værdi, der, hvis kumulativ er SAND, returnerer fordelingsfunktionen, og som, hvis kumulativ er FALSK, returnerer tæthedsfunktionen"}, "FDIST": {"a": "(x; frihedsgrader1; frihedsgrader2)", "d": "Returnerer fraktilsandsynligheden (højresidet) for F-fordelingen (afvigelsesgraden) for to datasæt", "ad": "er den værdi (et ikke-negativt tal), funktionen skal evalueres for!er frihedsgrader til tælleren - et tal mellem 1 og 10^10, bortset fra 10^10!er frihedsgrader til nævneren - et tal mellem 1 og 10^10, bortset fra 10^10"}, "FINV": {"a": "(sandsynlighed; frihedsgrader1; frihedsgrader2)", "d": "Returnerer den inverse (højresidede) F-fordeling: hvis p = FFORDELING(x;...), så FINV(p;...) = x", "ad": "er sandsyn<PERSON>gheden, der knytter sig til den kumulative F-fordeling. Det er et tal mellem 0 og 1!er frihedsgrader til tælleren - et tal mellem 1 og 10^10, bortset fra 10^10!er frihedsgrader til nævneren - et tal mellem 1 og 10^10, bortset fra 10^10"}, "FTEST": {"a": "(vektor1; vektor2)", "d": "Returnerer resultatet af en F-test, dvs. den tosidige sandsynlighed for, at varianserne i Vektor1 og Vektor2 ikke er signifikant forskellige", "ad": "er den første matrix eller det første dataområde. Det kan bestå af tal eller navne, matrixer eller referencer, der indeholder tal (blanktegn ignoreres)!er den anden matrix eller det andet dataområde. Det kan bestå af tal eller navne, matrixer eller referencer, der indeholder tal (blanktegn ignoreres)"}, "F.DIST": {"a": "(x; frihedsgrader1; frihedsgrader2; kumulativ)", "d": "Returnerer fraktilsandsynligheden for F-fordelingen (afvigelsesgraden) for to datasæt", "ad": "er den værdi (et ikke-negativt tal), funktionen skal evalueres for!er frihedsgrader til tælleren - et tal mellem 1 og 10^10, bortset fra 10^10!er frihedsgrader til nævneren - et tal mellem 1 og 10^10, bortset fra 10^10!er en logisk værdi, der, hvis kumulativ er SAND, returnerer fordelingsfunktionen, og som, hvis kumulativ er FALSK, returnerer tæthedsfunktionen"}, "F.DIST.RT": {"a": "(x; frihedsgrader1; frihedsgrader2)", "d": "Returnerer fraktilsandsynligheden for F-fordelingen (afvigelsesgraden) for to datasæt", "ad": "er den værdi (et ikke-negativt tal), funktionen skal evalueres for!er frihedsgrader til tælleren - et tal mellem 1 og 10^10, bortset fra 10^10!er frihedsgrader til nævneren - et tal mellem 1 og 10^10, bortset fra 10^10"}, "F.INV": {"a": "(sandsynlighed; frihedsgrader1; frihedsgrader2)", "d": "Returnerer den inverse fraktilsandsynlighed for F-fordelingen. Hvis p = F.FORDELING(x,...), så er F.FORDELING(p,...) = x", "ad": "er sandsyn<PERSON>gh<PERSON>n, der knytter sig til F-fordelingen. Det er et tal mellem 0 og 1!er frihedsgrader til tælleren - et tal mellem 1 og 10^10, bortset fra 10^10!er frihedsgrader til nævneren - et tal mellem 1 og 10^10, bortset fra 10^10"}, "F.INV.RT": {"a": "(sandsynlighed; frihedsgrader1; frihedsgrader2)", "d": "Returnerer den inverse fraktilsandsynlighed for F-fordeling. Hvis p = F.FORDELING.RT(x,...), så er FINV.RT(p,...) = x", "ad": "er sandsyn<PERSON>gh<PERSON>n, der knytter sig til F-fordelingen. Det er et tal mellem 0 og 1!er frihedsgrader til tælleren - et tal mellem 1 og 10^10, bortset fra 10^10!er frihedsgrader til nævneren - et tal mellem 1 og 10^10, bortset fra 10^10"}, "F.TEST": {"a": "(matrix1; matrix2)", "d": "Returnerer resultatet af en F-test, dvs. den tosidige sandsynlighed for at varianserne til Matrix1 og Matrix2 ikke er tydeligt forskellige", "ad": "er den første matrix eller det første dataområde. Det kan bestå af tal, navne, matrixer eller referencer, der indeholder tal (blanktegn ignoreres)!er den anden matrix eller det andet dataområde. Det kan bestå af tal, navne, matrix eller referencer, der indeholder tal (blanktegn ignoreres)"}, "FISHER": {"a": "(x)", "d": "Returnerer Fisher-<PERSON>en", "ad": "er den værdi, <PERSON>en skal udføres på. Det er et tal mellem -1 og 1, bortset fra -1 og 1"}, "FISHERINV": {"a": "(y)", "d": "Returnerer den inverse Fisher-transformation: hvis y = FISHER(x), så FISHERINV(y) = x", "ad": "er den værdi, som den inverse Fisher-transformation skal udføres på"}, "FORECAST": {"a": "(x; kendte_y'er; kendte_x'er)", "d": "<PERSON><PERSON><PERSON><PERSON>, eller for<PERSON><PERSON> en fremtidig værdi baseret på lineær regression vha. eksisterende værdier", "ad": "er det datapunkt, som du vil forudsige en værdi for og skal være en numerisk værdi!er den afhængige matrix eller et interval af numeriske data!er den uafhængige matrix eller et interval af numeriske data. Variansen af kendte_x 'er må ikke være nul"}, "FORECAST.ETS": {"a": "(mål_dato; væ<PERSON><PERSON>; tidslinje; [sæson<PERSON>ving]; [data_completion]; [sammenlægning])", "d": "Returnerer prognoseværdien for en bestemt fremtidig dato vha. en eksponentiel udjævningsmetode.", "ad": "er det datapunkt, som Spreadsheet Editor forudsiger en værdi for. Det skal overføre værdimønsteret til tidslinjen.!er den matrix eller det område af numeriske data, du er ved at forudsige.!er den uafhængige matrix eller området af numeriske data. Datoer på tidslinjen skal have et ensartet trin mellem dem og kan ikke være nul.!er en valgfri numerisk værdi, der angiver længden af det sæsonmæssige mønster. Standardværdien på 1 angiver, at sæsonudsving registreres automatisk.!er en valgfri værdi til håndtering af manglende værdier. Standardværdien 1 erstatter manglende værdier ved interpolering, og 0 erstatter dem med nuller.!er en valgfri numerisk værdi til sammenlægning af flere værdier med samme tidsstempel. H<PERSON> feltet er tomt, beregner Spreadsheet Editor gennemsnittet af værdierne."}, "FORECAST.ETS.CONFINT": {"a": "(mål_dato; væ<PERSON><PERSON>; tidslinje; [till<PERSON>_niveau]; [sæ<PERSON><PERSON><PERSON>]; [data_fuldførelse]; [sammenlægning])", "d": "Returnerer et tillidsinterval for prognoseværdien på den angivne måldato.", "ad": "er det datapunkt, som Spreadsheet Editor forudsiger en værdi for. Det skal overføre værdimønsteret til tidslinjen.!er den matrix eller det område af numeriske data, du er ved at forudsige.!er den uafhængige matrix eller området af numeriske data. Datoer på tidslinjen skal have et ensartet trin mellem dem og kan ikke være nul.!er et tal mellem 0 og 1, der viser tillidsniveauet for det beregnede tillidsinterval. Standardværdien er 0,95.!er en valgfri numerisk værdi, der angiver længden af det sæsonmæssige mønster. Standardværdien på 1 angiver, at sæsonudsving registreres automatisk.!er en valgfri værdi til håndtering af manglende værdier. Standardværdien 1 erstatter manglende værdier ved interpolering, og 0 erstatter dem med nuller.!er en valgfri numerisk værdi til sammenlægning af flere værdier med samme tidsstempel. <PERSON><PERSON> feltet er tomt, beregner Spreadsheet Editor gennemsnittet af værdierne."}, "FORECAST.ETS.SEASONALITY": {"a": "(væ<PERSON><PERSON>; tidslinje; [data_completion]; [sammenlægning])", "d": "Returnerer længden på det gentagne mø<PERSON>, som en applikation registrerer for den angivne tidsserie.", "ad": "er den matrix eller det celleområde af numeriske data, du er ved at forudsige.!er den uafhængige matrix eller celleområdet af numeriske data. Datoer på tidslinjen skal have et ensartet trin mellem dem og må ikke være nul.!er en valgfri værdi for håndtering af manglende værdier. Standardværdien 1 erstatter manglende værdier ved interpolering, og 0 erstatter dem med nuller.!er en valgfri numerisk værdi til sammenlægning af flere værdier med samme tidsstempel. Hvis feltet er tomt, beregner Spreadsheet Editor gennemsnittet af værdierne."}, "FORECAST.ETS.STAT": {"a": "(væ<PERSON><PERSON>; tidslinje; statistik_type; [sæsonudsving]; [data_completion]; [sammenlægning])", "d": "Returnerer den ønskede statistik for prognosen.", "ad": "er den matrix eller det celleområde af numeriske data, du er ved at forudsige.!er den uafhængige matrix eller celleområdet af numeriske data. Datoerne på tidslinjen skal have et ensartet trin mellem dem og må ikke være nul.!er et tal mellem 1 og 8, der angiver, hvilken statistik Spreadsheet Editor returnerer for den beregnede prognose.!er en valgfri numerisk værdi, der angiver længden af det sæsonmæssige mønster. Standardværdien på 1 angiver, at sæsonudsving registreres automatisk.!er en valgfri værdi til håndtering af manglende værdier. Standardværdien 1 erstatter manglende værdier ved interpolering, og 0 erstatter dem med nuller.!er en valgfri numerisk værdi til sammenlægning af flere værdier med samme tidsstempel. H<PERSON> feltet er tomt, beregner Spreadsheet Editor gennemsnittet af værdierne."}, "FORECAST.LINEAR": {"a": "(x; kendte_y'er; kendte_x'er)", "d": "<PERSON><PERSON><PERSON><PERSON> eller forudsiger en fremtidig værdi baseret på lineær regression vha. eksisterende værdier", "ad": "er det datapunkt, som du vil forudsige en værdi for og skal være en numerisk værdi!er den afhængige matrix eller et interval af numeriske data!er den uafhængige matrix eller et interval af numeriske data. Variansen af kendte_x 'er må ikke være nul"}, "FREQUENCY": {"a": "(datavektor; intervalvektor)", "d": "<PERSON><PERSON><PERSON>r hvor ofte en værdi forekommer indenfor et værdiområde og returnerer en lodret matrix af tal, der har ét element mere end Intervalmatrix", "ad": "er en matrix eller en reference til et sæt af data, som du vil beregne hyppigheden af (blanktegn og tekst ignoreres)!er en matrix af intervaller eller en reference til intervaller,  hvor værdierne fra datamatrix skal grupperes"}, "GAMMA": {"a": "(x)", "d": "Returnerer gammafunktionsværdien", "ad": "er den værdi, du vil beregne gamma for"}, "GAMMADIST": {"a": "(x; alpha; beta; kumulativ)", "d": "Returnerer gammafordelingen", "ad": "er den værdi (et ikke-negativt tal), du vil evaluere distributionen for!er en parameter til fordelingen (et positivt tal)!er en parameter til fordelingen (et positivt tal). Hvis beta = 1, returnerer GAMMADIST standardgammafordelingen!er en logisk værdi. Hvis den er SAND, returneres den kumulative fordelingsfunktion. Hvis den er FALSK eller udeladt, returneres sandsynlighedsfunktionen"}, "GAMMA.DIST": {"a": "(x; alpha; beta; kumulativ)", "d": "Returnerer gammafordelingen", "ad": "ier den værdi (et ikke-negativt tal), du vil evaluere distributionen for!er en parameter til fordelingen (et positivt tal)!er en parameter til fordelingen (et positivt tal). Hvis beta = 1, returnerer GAMMA.FORDELING standardgammafordelingen!er en logisk værdi, der, hvis kumulativ er SAND, returnerer fordelingsfunktionen, og som, hvis kumulativ er FALSK, returnerer sandsynlighedsfunktionen"}, "GAMMAINV": {"a": "(sandsynlighed; alpha; beta)", "d": "Returnerer den inverse kumulative fordeling for gammafordelingen: hvis p = GAMMAFORDELING(x,...), så GAMMAINV(p,...) = x", "ad": "er sandsy<PERSON><PERSON><PERSON><PERSON><PERSON>, der knytter sig til gammafordelingen. Det er et tal mellem 0 og 1!er en parameter til fordelingen (et positivt tal)!er en parameter til fordelingen (et positivt tal). Hvis beta = 1, returnerer GAMMAINV den inverse standardgammafordeling"}, "GAMMA.INV": {"a": "(sandsynlighed; alpha; beta)", "d": "Returnerer den inverse fordelingsfunktion for gammafordelingen: if p = GAMMA.FORDELING(x,...), then GAMMA.INV(p,...) = x", "ad": "er sandsyn<PERSON><PERSON><PERSON>n, der knytter sig til gammafordelingen. Det er et tal mellem 0 og 1!er en parameter til fordelingen (et positivt tal)!er en parameter (et positivt tal) til fordelingen. Hvis beta = 1, returnerer GAMMA.INV den inverse standardgammafordeling"}, "GAMMALN": {"a": "(x)", "d": "Returnerer den naturlige logaritme til gammafordelingen", "ad": "er den værdi (et positivt tal), du vil beregne funktionen GAMMALN for"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Returnerer den naturlige logaritme til gammafordelingen", "ad": "er den værdi (et positivt tal), du vil beregne funktionen GAMMALN.PRECISE for"}, "GAUSS": {"a": "(x)", "d": "Returnerer 0,5 mindre end fordelingsfunktionen for standardnormalfordelingen", "ad": "er den værdi, du vil finde fordelingen for"}, "GEOMEAN": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den geometriske middelværdi af en matrix eller et område med positive numeriske data", "ad": "er 1-255 tal, nav<PERSON>, matrixer el<PERSON> referencer, der indeholder tal, som middelværdien skal beregnes for"}, "GROWTH": {"a": "(kendte_y'er; [kendte_x'er]; [nye_x'er]; [konstant])", "d": "Returnerer tal i en eksponentiel væksttendens svarende til kendte datapunkter", "ad": "er mængden af y-værdier, du allerede kender, i forholdet y = b*m^x, en matrix eller et område med positive tal!er en mængde x-værdier, som du muligvis kender, i forholdet y = b*m^x, en matrix eller et område med samme størrelse som Kendte_y'er!er nye x-værdier, som FORØGELSE skal returnere tilsvarende y-værdier for!angiver en logisk værdi: Konstanten b beregnes normalt, hvis <PERSON>nst = SAND; b sættes til lig 1, hvis <PERSON> = FALSK eller udelades"}, "HARMEAN": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den harmoniske middelværdi af et datasæt bestående af positive tal, dvs. det reciprokke tal til middelværdien af de reciprokke værdier", "ad": "er 1-255 tal, nav<PERSON>, matrixer el<PERSON> referencer, der indeholder tal, som den harmoniske middelværdi skal beregnes for"}, "HYPGEOM.DIST": {"a": "(udfald_s; størrelse; population_s; populationsstørrelse; kumulativ)", "d": "Returnerer punktsandsynligheden i en hypergeometrisk fordeling", "ad": "er antal gunstige udfald i stikprøven!er stikprøvestørrelsen!er antal gunstige udfald i populationen!er populationsstørrelsen!er en logisk værd. Hvis SAND, returneres fordelingsfunktionen. Hvis FALSK, returneres sandsynlighedsfunktionen"}, "HYPGEOMDIST": {"a": "(udfald_s; størrelse; population_s; populationsstørrelse)", "d": "Returnerer den hypergeometriske fordeling", "ad": "er antal gunstige udfald i stikprøven!er stikprøvestørrelsen!er antal gunstige udfald i populationen!er populationens størrelse"}, "INTERCEPT": {"a": "(kendte_y'er; kendte_x'er)", "d": "<PERSON><PERSON><PERSON><PERSON> det <PERSON>, hvor en linje skærer y-aksen ved hjælp af en tilpasset regressionslinje, der er afbildet gennem kendte x-værdier og y-værdier", "ad": "er afhængige sæt observationer eller data og kan være tal eller navne, matrixer eller referencer, der indeholder tal!er uafhængigt sæt observationer eller data og kan være tal eller navne, matrixer eller referencer, der indeholder tal"}, "KURT": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer kurtosisværdien af et datasæt", "ad": "er 1-255 tal, nav<PERSON>, matrixer el<PERSON> referencer, der indeholder tal, som kurtosisværdien skal beregnes for"}, "LARGE": {"a": "(matrix; k)", "d": "Returnerer den k'te-største værdi i et datasæt. For eksempel det femtestørste tal", "ad": "er den matrix eller det datainterval, som den k'te-største værdi skal bestemmes for!er den position (regnet fra største værdi) i matrixen eller celleområdet, hvorfra værdien skal returneres"}, "LINEST": {"a": "(kendte_y'er; [kendte_x'er]; [konstant]; [statistik])", "d": " Returnerer en statistik, som beskriver en lineær tendens, der svarer til kendte datapunkter ved at placere en lige linje vha. de mindste kvadraters metode", "ad": "er sæt af y-værdier, som du allerede kender i forholdet y = mx + b!er et valgfrit sæt x-værdier, som du måske allerede kender i forholdet y = mx + b!er en logisk værdi: konstanten b bere<PERSON><PERSON> normalt, hvis <PERSON> = SAND eller udeladt; b sættes til lig 0, hvis <PERSON>nst = FALSK! er en logisk værdi: returneres andre regressionsspecifikke stikprøvefunktioner = SAND; returneres m-koefficienterne og konstanten b = FALSK eller udelades"}, "LOGEST": {"a": "(kendte_y'er; [kendte_x'er]; [konstant]; [statistik])", "d": "Returnerer en statistik, der beskriver en eksponentialkurve svarende til kendte datapunkter", "ad": "er sæt af y-værdier, som du allerede kender i forholdet y = b * m ^ x!er et valgfrit sæt af x-værdier, som du måske allerede kender i forholdet y = b * m ^ x!er en logisk værdi: konstanten b be<PERSON><PERSON><PERSON> normalt, hvis <PERSON> = SAND eller udeladt; b sættes til lig 1, hvis <PERSON> = FALSK!er en logisk værdi: returneres andre regressionsspecifikke stikprøvefunktioner = SAND; returneres m-koefficienterne og konstanten b = FALSK eller udelades"}, "LOGINV": {"a": "(sandsynlighed; middelværdi; standardafv)", "d": "Returnerer den inverse kumulative fordelingsfunktion for lognormalfordelingen til x, hvor ln(x) er normalfordelt med parametrene Middelværdi og Standardafv", "ad": "er sandsynligheden, der knytter sig til lognormalfordelingen. Det er et tal mellem 0 og 1!er middelværdien for ln(x)!er standardafvigelsen (et positivt tal) for ln(x)"}, "LOGNORM.DIST": {"a": "(x; middelvæ<PERSON>; standardafv; kumulativ)", "d": "Returnerer fordelingsfunktionen for lognormalfordelingen af x, hvor In(x) normalt distribueres med parametrene Middelværdi og Standardafv", "ad": "er den værdi (et positivt tal), som funktionen skal evalueres for!er middelværdien for ln(x)!er standardafvigelsen (et positivt tal) for ln(x)!er en logisk værd. Hvis SAND, returneres fordelingsfunktionen. Hvis FALSK, returneres sandsynlighedsfunktionen"}, "LOGNORM.INV": {"a": "(sandsynlighed; middelværdi; standardafv)", "d": "Returnerer den inverse fordelingsfunktion for lognormalfordelingen af x, hvor In(x) normalt distribueres med parametrene Middelværdi og Standardafv", "ad": "er sandsynligheden, der knytter sig til lognormalfordelingen. Det er et tal mellem 0 og 1!er middelværdien for ln(x)!er standardafvigelsen (et positivt tal) for ln(x)"}, "LOGNORMDIST": {"a": "(x; middelværdi; standardafv)", "d": "Returnerer fordelingsfunktionen for lognormalfordelingen af x, hvor ln(x) er normalfordelt med parametrene Middelværdi og Standardafv", "ad": "er den værdi (et positivt tal), som funktionen skal evalueres for!er middelværdien for ln(x)!er standardafvigelsen (et positivt tal) for ln(x)"}, "MAX": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den største værdi fra et datasæt. Ignorerer logiske værdier og tekst", "ad": "er 1-255 tal, tomme celler, logiske værdier eller tal i bogstavformat, som du vil finde den største værdi for"}, "MAXA": {"a": "(værdi1; [værdi2]; ...)", "d": "Returnerer den største værdi fra et værdisæt. Ignorerer ikke logiske værdier og tekst", "ad": "er 1-255 tal, tomme celler, logiske værdier eller tal i bogstavformat, som du vil finde den maksimale værdi for"}, "MAXIFS": {"a": "(maks_omr<PERSON><PERSON>; kriterieområde; kriterier; ...)", "d": "Returnerer den største værdi blandt celler, der er angivet med et givet sæt betingelser eller kriterier", "ad": " celler til at bestemme den maksimale værdi! er celleområdet, du vil evaluere for den konkrete betingelse! er betingelsen eller kriteriet i form af et tal, et udtryk eller tekst, der definerer, hvilke celler der skal medtages ved bestemmelse af den maksimale værdi"}, "MEDIAN": {"a": "(tal1; [tal2]; ...)", "d": "Return<PERSON> median<PERSON>, eller den midterste værdi, for det givne talsæt", "ad": "er 1-255 tal, nav<PERSON>, matrixer eller referencer indeholdende tal, som du vil finde medianen for"}, "MIN": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer det mindste tal fra et værdisæt. Ignorerer logiske værdier og tekst", "ad": "er 1-255 tal, tomme celler eller logiske værdier, som du vil finde den mindste værdi for"}, "MINA": {"a": "(værdi1; [værdi2]; ...)", "d": "Returnerer den mindste værdi fra et værdisæt. Logiske værdier og tekst ignoreres ikke", "ad": "er 1-255 tal, tomme celler, logiske værdier eller tal i bogstavformat, som du vil finde den mindste værdi for"}, "MINIFS": {"a": "(min_omr<PERSON><PERSON>; kriterieområde; kriterier; ...)", "d": "Returnerer den mindste værdi blandt celler, der er angivet med et givet sæt betingelser eller kriterier", "ad": "celler til at bestemme den minimale værdi! er celleområdet, du vil evaluere for den konkrete betingelse! er betingelsen eller kriteriet i form af et tal, et udtryk eller tekst, der definerer, hvilke celler der skal medtages ved bestemmelse af den minimale værdi"}, "MODE": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den hyppigst forekommende værdi i en matrix eller et datainterval", "ad": "er 1-255 tal eller navne, matrixer eller referencer, der indeholder tal, som du vil finde modus for"}, "MODE.MULT": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer en lodret matrix med de hyppigst forekommende værdier i en matrix eller et datainterval. Brug = TRANSPOSE(MODE.MULT(tal1,tal2,...)) til en vandret matrix", "ad": "er 1-255 tal, nav<PERSON>, matrixer el<PERSON> referencer, der indeholder tal, som du vil finde modus for"}, "MODE.SNGL": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den hyppigst forekommende værdi i en matrix eller et datainterval", "ad": "er 1-255 ta, nav<PERSON>, matrixer el<PERSON> referencer, der indeholder tal, som du vil finde modus for"}, "NEGBINOM.DIST": {"a": "(tal_f; tal_s; sandsynlighed_s; kumulativ)", "d": "Returnerer punktsandsynligheden for den negative binomialfordeling, dvs. sandsynligheden for, at der vil være tal_f mislykkede forsøg, før det lykkes i forsøg tal_s med sandsynligheden sandsynlighed_s for, at et forsøg lykkes", "ad": "er antallet af mislykkede forsøg!er det ønskede antal gunstige udfald!er sandsynligheden for et gunstigt udfald, dvs. et tal mellem 0 og 1!er en logisk værdi, der, hvis kumulativ er SAND, returnerer fordelingsfunktionen, og som, hvis kumulativ er FALSK, returnerer tæthedsfunktionen"}, "NEGBINOMDIST": {"a": "(tal_f; tal_s; sandsynlighed_s)", "d": "Returnerer punktsandsynligheden for den negative binomialfordeling, dvs. sandsynligheden for, at der vil være tal_f mislykkede forsøg før det lykkedes i forsøg tal_s med sandsynligheden sandsynlighed_s for at et forsøg lykkes", "ad": "er antallet af mislykkede forsøg!er det ønskede antal gunstige udfald!er sandsynligheden for et gunstigt udfald, dvs. et tal mellem 0 og 1"}, "NORM.DIST": {"a": "(x; middelvæ<PERSON>; standardafv; kumulativ)", "d": "Returnerer normalfordelingen for den angivne middelværdi og standardafvigelse", "ad": "er den værdi, fordelingen skal beregnes for!er middelværdien for fordelingen!er standardafvigelsen (et positivt tal) for fordelingen!er en logisk værdi. Hvis SAND returneres fordelingsfunktionen. Hvis FALSK returneres punktsandsynligheden"}, "NORMDIST": {"a": "(x; middelvæ<PERSON>; standardafv; kumulativ)", "d": "Returnerer normalfordelingen for den angivne middelværdi og standardafvigelse", "ad": "er den værdi, fordelingen skal beregnes for!er middelværdien for fordelingen!er standardafvigelsen (et positivt tal) for fordelingen!er en logisk værdi. Hvis SAND, returneres fordelingsfunktionen. Hvis FALSK, returneres punktsandsynligheden"}, "NORM.INV": {"a": "(sandsynlighed; middelværdi; standardafv)", "d": "Returnerer normalfordelingen for den angivne middelværdi og standardafvigelse", "ad": "er sandsynligh<PERSON>n, der knytter sig til normalfordelingen, et tal større end eller lig med 0 og mindre end eller lig med 1!er fordelingens middelværdi!er standardafvigelsen for fordelingen, et positivt tal"}, "NORMINV": {"a": "(sandsynlighed; middelværdi; standardafv)", "d": "Returnerer den inverse kumulative fordelingsfunktion for normalfordelingen for den angivne middelværdi og standardafvigelse", "ad": "er sandsynligheden, der knytter sig til normalfordelingen. Det er et tal større end eller lig med 0 og mindre end eller lig med1!er fordelingens aritmetiske middelværdi!er standardafvigelsen (et positivt tal) for fordelingen"}, "NORM.S.DIST": {"a": "(z; kumulativ)", "d": "Returnerer fordelingsfunktionen for standardnormalfordelingen (har en middelværdi på nul og en standardafvigelse på en)", "ad": "er den værdi, fordelingen skal beregnes for!er en logisk værdi, der, hvis kumulativ er SAND, returnerer fordelingsfunktionen, og som, hvis kumulativ er FALSK, returnerer tæthedsfunktionen"}, "NORMSDIST": {"a": "(z)", "d": "Returnerer den kumulative fordeling for standardnormalfordelingen (har en middelværdi på nul og en standardafvigelse på en)", "ad": "er den værdi, fordelingen skal beregnes for"}, "NORM.S.INV": {"a": "(sandsynlighed)", "d": "Returnerer den inverse fordelingsfunktion for standardnormalfordelingen (den har en middelværdi på nul og en standardafvigelse på en)", "ad": "er sandsynli<PERSON><PERSON>n, der knytter sig til normalfordelingen. Tallet er større end eller lig med 0 og mindre end eller lig med 1"}, "NORMSINV": {"a": "(sandsynlighed)", "d": "Returnerer den inverse kumulative fordeling for standardnormalfordelingen (den har en middelværdi på nul og en standardafvigelse på en)", "ad": "er sandsynli<PERSON><PERSON>n, der knytter sig til normalfordelingen. Tallet er større end eller lig med 0 og mindre end eller lig med 1"}, "PEARSON": {"a": "(matrix1; matrix2)", "d": "Returnerer <PERSON><PERSON>, r", "ad": "er en mængde uafhængige værdier!er en mængde afhængige værdier"}, "PERCENTILE": {"a": "(vektor; k)", "d": "Returnerer den k'te fraktil for værdier i et interval", "ad": "er den vektor eller det datainterval, som definerer den relative status!er en fraktilværdi i intervallet 0 til 1"}, "PERCENTILE.EXC": {"a": "(matrix; k)", "d": "Returnerer den k'te fraktil for værdier i et interval, hvor k ligger i intervallet fra 0 til 1", "ad": "er den matrix eller det datainterval, som definerer den relative status!er en fraktilværdi i intervallet fra 0 til og med 1"}, "PERCENTILE.INC": {"a": "(matrix; k)", "d": "Returnerer den k'te fraktil for værdier i et interval, hvor k ligger i intervallet fra 0 til og med 1", "ad": "er den matrix eller det datainterval, som definerer den relative status!er en fraktilværdi i intervallet fra 0 til og med 1"}, "PERCENTRANK": {"a": "(vektor; x; [signifikans])", "d": "Returnerer den procentuelle rang for en given værdi i et datasæt", "ad": "er den vektor eller det datainterval med numeriske værdier, der definerer den relative status!er den værdi, hvis rang ønskes bestemt!er en valg<PERSON>ri værdi, der angiver antallet af betydende cifre for den returnerede procentdel - tre cifre, hvis det udelades (0.xxx%)"}, "PERCENTRANK.EXC": {"a": "(matrix; x; [signifikans])", "d": "Returnerer rangen for en værdi i et datasæt som en procentdel (fra 0 til 1) af datasættet", "ad": "er den matrix eller det numeriske datainterval, der definerer den relative status!er den værdi, hvis rang ønskes bestemt!er en valgfri specifikation af betydende cifre ved beregning af procentvis rang. Sættes til 3 cifre, hvis det udelades (0,xxx%)"}, "PERCENTRANK.INC": {"a": "(matrix; x; [signifikans])", "d": "Returnerer rangen for en værdi i et datasæt som en procentdel (fra 0 til og med 1) af datasættet", "ad": "er den matrix eller det numeriske datainterval, der definerer den relative status!er den værdi, hvis rang ønskes bestemt!er en valgfri specifikation af betydende cifre ved beregning af procentvis rang. Sættes til 3 cifre, hvis det udelades (0,xxx%)"}, "PERMUT": {"a": "(tal; tal_valgt)", "d": "Returnerer antallet af permutationer for et givet antal af objekter, der kan  vælges fra det totale antal objekter", "ad": "er det samlede antal objekter!er antallet af objekter i hver permutation"}, "PERMUTATIONA": {"a": "(tal; tal_valgt)", "d": "Returnerer antal permutationer for et givet antal objekter (med gentagelser), der kan vælges ud fra det samlede antal objekter", "ad": "er det samlede antal objekter!er antal objekter i hver permutation"}, "PHI": {"a": "(x)", "d": "Returnerer værdien af tæthedsfunktionen for en standardnormalfordeling", "ad": "er det tal, du vil finde tætheden af standardnormalfordelingen for"}, "POISSON": {"a": "(x; mid<PERSON><PERSON><PERSON><PERSON>; kumulativ)", "d": "Returnerer Poisson-fordelingen", "ad": "er antallet af hændelser!er den forventede numeriske værdi (et positivt tal)!er en logisk værdi. Hvis den er SAND, returneres den kumulative Poisson-fordeling, og hvis den FALSK, returneres Poisson-punktsandsynligheden"}, "POISSON.DIST": {"a": "(x; mid<PERSON><PERSON><PERSON><PERSON>; kumulativ)", "d": "Returnerer Poisson-fordelingen", "ad": "er antallet af hændelser!er den forventede numeriske værdi (et positivt tal)!er en logisk værdi. Hvis kumulativ er SAND, returneres fordelingsfunktionen, og hvis FALSK, returneres punktsandsynligheden"}, "PROB": {"a": "(x_<PERSON><PERSON><PERSON><PERSON><PERSON>; sandsyn<PERSON>gheder; nedre_grænse; [øvre_grænse])", "d": "Returnerer sand<PERSON><PERSON><PERSON><PERSON><PERSON>n for at værdier i et interval er mellem to græ<PERSON><PERSON> eller lig med en nedre grænse", "ad": "er udfaldsrummet for x med tilknyttede punktsandsynligheder!punktsandsynligheder for de mulige udfald for x. Er værdier mellem 0 og 1 bortset fra 0!er den nedre grænse for den værdi, du vil finde sandsynligheden for!er en valgfri øvre grænse for værdien. <PERSON><PERSON> den udelades, returnerer SANDSYNLIGHED sandsynligheden for at en x-værdi er lig med nedre_grænse"}, "QUARTILE": {"a": "(vektor; kvart)", "d": "Returnerer kvartilen i et givet datasæt", "ad": "er den matrix eller det celleområde med numeriske værdier, som kvartilen skal beregnes for!er et tal: minimumværdi = 0, 1. kvar<PERSON> = 1, middel<PERSON><PERSON>rdi = 2, 3. kvar<PERSON> = 3, maks<PERSON><PERSON><PERSON><PERSON>rdi = 4"}, "QUARTILE.INC": {"a": "(matrix; k<PERSON><PERSON>)", "d": "Returnerer kvartilen for et datasæt baseret på fraktilværdier fra 0..1 inklusive", "ad": "er den matrix eller det celleområde af numeriske værdier, som du ønsker kvartilværdien for!er et tal. minimumværdien = 0; første1. kvartil = 1; middelværdien = 2; 3. kvar<PERSON> = 3; maks<PERSON>umværdien = 4"}, "QUARTILE.EXC": {"a": "(matrix; k<PERSON><PERSON>)", "d": "Returnerer kvartilen for et datasæt baseret på fraktilværdier fra 0..1 eksklusive", "ad": "er den matrix eller det celleområde af numeriske værdier, som kvartilværdien skal beregnes for!ier et tal: minimumværdien = 0; 1. kvartil = 1; middelværdien = 2; 3. kvartil = 3; maksimumværdien = 4"}, "RANK": {"a": "(tal; reference; [ræk<PERSON><PERSON><PERSON><PERSON>])", "d": "Returnerer rangen for et tal på en liste med tal, dvs. tallets størrelse i forhold til de andre værdier på listen", "ad": "er det tal, du vil finde rangen for!er en matrix med tal eller en reference til en liste med tal. Ikke-numeriske værdier ignoreres!er et tal: rangen på listen, hvis den er sorteret faldende = 0 eller udeladt, rangen på listen, hvis den er sorteret stigende = en vilkårlig værdi forskellig fra nul"}, "RANK.AVG": {"a": "(tal; reference; [ræk<PERSON><PERSON><PERSON><PERSON>])", "d": "Returnerer rangen for et tal i en liste med tal. Dets størrel<PERSON> i forhold til andre væ<PERSON>er på listen. <PERSON>vis mere end et tal har den samme rang, returneres den gennemsnitlige rang", "ad": "er det tal, du ø<PERSON><PERSON> at finde rangen for!er en matrix for eller en reference til en liste med tal. Ikke-numeriske værdier ignoreres!er et tal. Rangen på listen er sorteret i faldende rækkefølge = 0 eller udeladt. Rangen på listen er sorteret i stigende rækkefølge = enhver anden værdi end nul"}, "RANK.EQ": {"a": "(tal; reference; [ræk<PERSON><PERSON><PERSON><PERSON>])", "d": "Returnerer rangen for et tal i en liste med tal. Dets størrel<PERSON> i forhold til andre værdier på listen. Hvis mere end en værdi har den samme rang, returneres den øverste rang for dette værdi<PERSON>t", "ad": "er det tal, du ø<PERSON><PERSON> at finde rangen for!er en matrix for eller en reference til en liste med tal. Ikke-numeriske værdier ignoreres!er et tal. Rangen på listen er sorteret i faldende rækkefølge = 0 eller udeladt. Rangen på listen er sorteret i stigende rækkefølge = enhver anden værdi end nul"}, "RSQ": {"a": "(kendte_y'er; kendte_x'er)", "d": "Returnerer kvadratet på Pearsons korrelationskoefficient gennem de givne datapunkter", "ad": "er en matrix eller et område af datapunkter og kan være tal eller navne, matrixer eller referencer, der indeholder tal!er en matrix eller et område af datapunkter og kan være tal eller navne, matrixer eller referencer, der indeholder tal"}, "SKEW": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer skævheden af en distribution, dvs. en karakteristik af graden af asymmetri for en distribution omkring dens middelværdi", "ad": "er 1-255 tal, nav<PERSON>, matrixer el<PERSON> referencer, der indeholder tal, som skævheden skal beregnes for"}, "SKEW.P": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer skævheden af en distribution baseret på en population: en karakteristik af graden af asymmetri for en distribution omkring dens middelværdi", "ad": "er 1 til 254 tal eller navne, matrixer eller referencer, der indeholde de tal, du vil finde populationens skævhed for"}, "SLOPE": {"a": "(kendte_y'er; kendte_x'er)", "d": "Returnerer estimatet på hældningen fra en lineær regressionslinje gennem de givne datapunkter", "ad": "er en matrix eller et celleområde af numeriske afhængige datapunkter og kan være tal eller navne, matrixer eller referencer, der indeholder tal!er sæt af uafhængige datapunkter og kan være tal eller navne, matrixer eller referencer, der indeholder tal"}, "SMALL": {"a": "(matrix; k)", "d": "Returnerer den k'te-mindste værdi i et datasæt. For eksempel det femtemindste tal", "ad": "er en matrix eller et interval af numeriske data, som den k'te-mindste værdi skal bestemmes for!er den position (regnet fra mindste værdi), h<PERSON><PERSON> værdien skal returneres"}, "STANDARDIZE": {"a": "(x; middelværdi; standardafv)", "d": "Returnerer en standardiseret værdi fra en distribution, karakteriseret ved en middelværdi og en standardafvigelse", "ad": "er den værdi, der skal normaliseres!er middelværdien for den stokastiske variabel!er standardafvigelsen (et positivt tal) for den stokastiske variabel"}, "STDEV": {"a": "(tal1; [tal2]; ...)", "d": "Beregner standardafvigelsen på basis af en stikprøve (ignorerer logiske værdier og tekst i stikprøven)", "ad": "er 1-255 tal, der svarer til en stikprøve fra en population, og kan være tal eller referencer, som indeholder tal"}, "STDEV.P": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>af<PERSON><PERSON>sen baseret på hele populationen, der er givet som argumenter (ignorerer logiske værdier og tekst)", "ad": "er 1 til 255 tal, der svarer til en  population og kan være tal eller referencer, der indeholder tal"}, "STDEV.S": {"a": "(tal1; [tal2]; ...)", "d": "Estimerer standardafvigelsen baseret på en stikprøve (ignorerer logiske værdier og tekst i stikprøven)", "ad": "er 1 til 255 tal, der svarer til en stikprøve af en population og kan være tal eller referencer, der indeholder tal"}, "STDEVA": {"a": "(værdi1; [værdi2]; ...)", "d": "Beregner standardafvigelsen på basis af en stikprøve, herunder logiske værdier og tekst. Tekst og den logiske værdi FALSK har værdien 0, og den logiske værdi SAND har værdien 1", "ad": "er 1-255 værdier svarende til en stikprøve fra en population. Det kan være værdier, navne eller referencer til værdier"}, "STDEVP": {"a": "(tal1; [tal2]; ...)", "d": "Beregner standardafvigelsen på basis af en hel population givet som argumenter (ignorerer logiske værdier og tekst)", "ad": "er 1-255 tal, der svarer til en hel population. Det kan være tal eller referencer, som indeholder tal"}, "STDEVPA": {"a": "(værdi1; [værdi2]; ...)", "d": "Beregner standardafvigelsen på basis af en hel population, herunder logiske værdier og tekst.  Tekst og den logiske værdi FALSK har værdien 0, og den logiske værdi SAND har værdien 1", "ad": "er 1-255 værdier svarende til en hel population. Det kan være værdier, tal, navne, matrixer eller referencer, der indeholder værdier"}, "STEYX": {"a": "(kendte_y'er; kendte_x'er)", "d": "Returnerer standard<PERSON><PERSON><PERSON> på de estimerede y-værdier for hvert x i en regression", "ad": "er en matrix eller et interval af afhængige datapunkter, og kan være tal eller navne, matrixer eller referencer, der indeholder tal!er en række af uafhængige datapunkter og kan være tal eller navne, matrixer eller referencer, der indeholder tal"}, "TDIST": {"a": "(x; fri<PERSON>sgrader; haler)", "d": "Returnerer Student t-fordelingen", "ad": "er den numeriske værdi, som fordelingen skal evalueres for!er et heltal, der angiver det antal frihedsgrader, der karakteriserer fordelingen!angiver, om sandsynligheden skal bestemmes en- eller tosidet: ensidet = 1; tosidet = 2"}, "TINV": {"a": "(sandsynlighed; frihedsgrader)", "d": "Returnerer den tosidede inverse fordelingsfunktion for Students t-fordeling", "ad": "er den sandsynlighed, der knytter sig til den tosidede t-fordeling. Det er et tal mellem 0 og 1!er et positivt heltal, der angiver antallet af frihedsgrader til at karakterisere fordelingen"}, "T.DIST": {"a": "(x; fri<PERSON>sgrader; kumulativ)", "d": "Returnerer den venstresidede fordelingsfunktion for Students t-fordeling", "ad": "er den numeriske værdi, som fordelingen skal evalueres for!er et heltal, der angiver antallet af frihedsgrader, som kendetegner fordelingen!er en logisk værdi. Hvis SAND returneres fordelingsfunktionen. Hvis FALSK returneres punktsandsynligheden"}, "T.DIST.2T": {"a": "(x; frihedsgrader)", "d": "Returnerer den tosidede fordelingsfunktion for Students t-fordeling", "ad": "er den numeriske værdi, som fordelingen skal evalueres for!er et heltal, der angiver antallet af frihedsgrader, som kendetegner fordelingen"}, "T.DIST.RT": {"a": "(x; frihedsgrader)", "d": "Returnerer den højresidede fordelingsfunktion for Students t-fordeling", "ad": "er den numeriske værdi, som fordelingen skal evalueres for!er et heltal, der angiver antallet af frihedsgrader, som kendetegner fordelingen"}, "T.INV": {"a": "(sandsynlighed; frihedsgrader)", "d": "Returnerer den venstresidede inverse fordelingsfunktion for Students t-fordeling", "ad": "er den sandsynlighed, der knytter sig til den tosidede t-fordeling. Det er et tal mellem 0 og 1!ier et positivt heltal, der angiver antallet af frihedsgrader, som kendetegner fordelingen"}, "T.INV.2T": {"a": "(sandsynlighed; frihedsgrader)", "d": "Returnerer den tosidede inverse fordelingsfunktion for Students t-fordeling", "ad": "er den sandsynlighed, der knytter sig til den tosidede t-fordeling. Det er et tal mellem 0 og 1!er et positivt heltal, der angiver antallet af frihedsgrader, som kendetegner fordelingen"}, "T.TEST": {"a": "(matrix1; matrix2; haler; type)", "d": "Returnerer den sandsynlighed, der knytter sig til en Students t-test", "ad": "er det første datasæt!er det andet datasæt!angiver om sandsynligheden skal bestemmes en- eller tosidet: 1 = ensidet; 2 = tosidet!er typen af t-test: 1 = parvis, 2 = dobbelt stikprøve med ens varians, 3 = dobbelt stikprøve med forskellig varians"}, "TREND": {"a": "(kendte_y'er; [kendte_x'er]; [nye_x'er]; [konstant])", "d": "Returnerer værdier i en lineær tendens svarende til kendte datapunkter med brug af de mindste kvadraters metode", "ad": "er et område eller en matrix med y-værdier, du allerede kender, i forholdet y = mx + b!er et område eller en matrix med x-værdier, du allerede kender,  i forholdet y = mx + b, dvs. en matrix af samme størrelse som Kendte_y'er!er et område eller en matrix med nye x-værdier, som TENDENS skal returnere tilsvarende y-værdier for!er en logisk værdi: konstanten b beregnes normalt, hvis Konst = SAND eller udeladt; b sættes til lig 0, hvis <PERSON>nst = FALSK"}, "TRIMMEAN": {"a": "(vektor; procent)", "d": "Returnerer det trimmede gennemsnit for et datasæt", "ad": "er det interval eller den vektor, som det trimmede gennemsnit skal findes for!er den procentvise andel af data, som skal ekskluderes fra toppen og bunden af datasættet inden beregning"}, "TTEST": {"a": "(vektor1; vektor2; haler; type)", "d": "Returnerer den sandsynlighed, der knytter sig til en Student t-test", "ad": "er det første datasæt!er det andet datasæt!angiver, om sandsynligheden skal bestemmes en- eller tosidet: ensidet = 1, tosidet = 2!er typen af t-test: parvis = 1, dobbelt stikprøve med ens varians = 2, dobbelt stikprøve med forskellig varians = 3"}, "VAR": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON><PERSON>r variansen baseret på en stikprøve (ignorerer logiske værdier og tekst i stikprøven)", "ad": "er 1-255 numeriske argumenter, svarende til en stikprøve fra populationen"}, "VAR.P": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON>gner variansen baseret på hele populationen (ignorerer logiske værdier og tekst i populationen)", "ad": "er mellem 1 og 255 numeriske argumenter, der svarer til en population"}, "VAR.S": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON><PERSON>r variansen baseret på en stikprøve (ignorerer logiske værdier og tekst i stikprøven)", "ad": "er mellem 1 og 255 numeriske argumenter, der svarer til en stikprøve fra en population"}, "VARA": {"a": "(værdi1; [værdi2]; ...)", "d": "<PERSON><PERSON><PERSON>r variansen baseret på en stikprøve, herunder logiske værdier og tekst. Tekst og den logiske værdi FALSK har værdien 0, og den logiske værdi SAND har værdien 1", "ad": "er 1-255 værdiargumenter svarende til en stikprøve fra en population"}, "VARP": {"a": "(tal1; [tal2]; ...)", "d": "Beregner variansen baseret på en hel population (ignorerer logiske værdier og tekst i populationen)", "ad": "er 1-255 numeriske argumenter, der svarer til en hel population"}, "VARPA": {"a": "(værdi1; [værdi2]; ...)", "d": "Beregner variansen baseret på en hel population,  herunder logiske værdier og tekst. Tekst og den logiske værdi FALSK har værdien 0, og den logiske værdi SAND har værdien 1", "ad": "er 1-255 værdiargumenter svarende til en hel population"}, "WEIBULL": {"a": "(x; alpha; beta; kumulativ)", "d": "Returnerer <PERSON><PERSON>-fordelingen", "ad": "er den værdi (et ikke-negativt tal), funktionen skal evalueres for!er en parameter til fordelingen (et positivt tal)!er en parameter til fordelingen (et positivt tal)!er en logisk værdi. Hvis SAND, returneres den kumulative fordelingsfunktion. Hvis FALSK, returneres sandsynlighedsfunktionen"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; kumulativ)", "d": "Returnerer <PERSON><PERSON>-fordelingen", "ad": "er den værdi (et ikke-negativt tal), funktionen skal evalueres for!er en parameter til fordelingen (et positivt tal)!er en parameter til fordelingen (et positivt tal)!Hvis SAND, returneres fordelingsfunktionen. Hvis FALSK, returneres sandsynlighedsfunktionen"}, "Z.TEST": {"a": "(matrix; x; [sigma])", "d": "Returnerer den ensidige P-værdi for en z-test", "ad": "er matrixen eller dataområdet, som X skal testes i forhold til!er den værdi, der skal testes!er standardafvigelsen for populationen (kendt). <PERSON><PERSON> den ikke angives, bruges standardafvigelsen for stikprøven"}, "ZTEST": {"a": "(vektor; x; [sigma])", "d": "Returnerer den tosidede P-værdi til en z-test", "ad": "er den matrix eller det dataområ<PERSON>, som X skal testes mod!er den værdi, der skal testes!er populationens standardafvigelse, der forudsættes kendt. <PERSON><PERSON> den udelades, benyttes stikprøvens standardafvigelse"}, "ACCRINT": {"a": "(udstedelsesdato; første_rente; afregningsdato; rente; nominel; frekvens; [datotype]; [beregningsmetode])", "d": "Returnerer den påløbne rente for et værdipapir med periodisk renteudbetaling.", "ad": "er værdipapirets udstedelsesdato, angivet som et serielt datotal!er værdipapirets første rentedato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets årlige kuponrente!er værdipapirets nominelle værdi!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes!er en logisk værdi: Hvis værdien er SAND eller udeladt, beregnes påløbne renter fra udstedelsesdatoen. Hvis værdien er FALSK, foretages beregning fra seneste kuponbetalingsdato"}, "ACCRINTM": {"a": "(udstedelsesdato; afregningsdato; rente; nominel; [datotype])", "d": "Returnerer den påløbne rente for et værdipapir med renteudbetaling ved udløb", "ad": "er værdipapirets udstedelsesdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets årlige kuponrente!er værdipapirets nominelle værdi!er den datotype, der skal anvendes"}, "AMORDEGRC": {"a": "(kø<PERSON>p<PERSON>; købsdato; første_periode; restværdi; periode; sats; [datotype])", "d": "Returnerer den forholdsmæssige lineære afskrivning af et aktiv for hver regnskabsperiode", "ad": "er aktivets købspris!er aktivets købsdato!er slutdatoen for første regnskabsperiode!er aktivets restværdi i slutningen af dets levetid!er perioden!er den sats, som aktivet afskrives med!årsbasis: 0 for et år på 360 dage, 1 for faktisk, 3 for et år på 365 dage."}, "AMORLINC": {"a": "(kø<PERSON>p<PERSON>; købsdato; første_periode; restværdi; periode; sats; [datotype])", "d": "Returnerer den forholdsmæssige lineære afskrivning af et aktiv for hver regnskabsperiode", "ad": "er aktivets købspris!er aktivets købsdato!er slutdatoen for første regnskabsperiode!er aktivets restværdi i slutningen af dets levetid!er perioden!er den sats, som aktivet afskrives med!årsbasis: 0 for et år på 360 dage, 1 for faktisk, 3 for et år på 365 dage."}, "COUPDAYBS": {"a": "(afregningsdato; udlø<PERSON><PERSON><PERSON>; hyppighed; [datotype])", "d": "Returnerer antal dage fra starten af kuponperioden til afregningsdatoen", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "COUPDAYS": {"a": "(afregningsdato; udlø<PERSON><PERSON><PERSON>; hyppighed; [datotype])", "d": "Returnerer antal dage i den kuponperiode, der indeholder udløbsdatoen", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "COUPDAYSNC": {"a": "(afregningsdato; udlø<PERSON><PERSON><PERSON>; hyppighed; [datotype])", "d": "Returnerer antal dage fra afregningsdatoen til næste kupondato", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "COUPNCD": {"a": "(afregningsdato; udlø<PERSON><PERSON><PERSON>; hyppighed; [datotype])", "d": "Returnerer den næste kupondato efter afregningsdatoen", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "COUPNUM": {"a": "(afregningsdato; udlø<PERSON><PERSON><PERSON>; hyppighed; [datotype])", "d": "Returnerer antal kuponbetalinger mellem afregnings- og udløbsdatoen", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "COUPPCD": {"a": "(afregningsdato; udlø<PERSON><PERSON><PERSON>; hyppighed; [datotype])", "d": "Returnerer den forrige kupondato før afregningsdatoen", "ad": "er værdipapirets afregningsdato, angivet som et serielt datottal!er værdipapirets udløbsdato, angivet som et serielt datotal!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "CUMIPMT": {"a": "(rente; nper; nv; startperiode; slutperiode; betalingstype)", "d": "Returnerer den akkumulerede rente, betalt mellem to perioder", "ad": "er rentefoden!er det samlede antal ydelsesperioder!er nutidsværdien!er den første periode i beregningen!er den sidste periode i beregningen!er tidspunktet for ydelsen"}, "CUMPRINC": {"a": "(rente; nper; nv; startperiode; slutperiode; betalingstype)", "d": "Returnerer den akkumulerede ho<PERSON>, betalt på et lån mellem to perioder", "ad": "er rentefoden!er det samlede antal ydelsesperioder!er nutidsværdien!er den første periode i beregningen!er den sidste periode i beregningen!er tidspunktet for ydelsen"}, "DB": {"a": "(kø<PERSON><PERSON><PERSON>; restværdi; levetid; periode; [måned])", "d": "Returnerer afskrivningsbeløbet for et aktiv for en given periode vha. saldometoden", "ad": "er aktivets kostpris!er aktivets værdi ved afskrivningens afslutning!angiver antallet af afskrivningsperioder, som aktivet afskrives over (aktivets levetid)!er den periode, afskrivningen skal beregnes for. Periode skal anvende samme enheder som levetid!er antallet af måneder det første år. <PERSON><PERSON> måned udelades, forudsættes værdien at være 12"}, "DDB": {"a": "(kø<PERSON><PERSON><PERSON>; restværdi; levetid; periode; [faktor])", "d": "Returnerer afskrivningsbeløbet for et aktiv for en given periode vha. dobbeltsaldometoden eller en anden angivet afskrivningsmetode", "ad": "er aktivets kostpris!er aktivets værdi ved afskrivningens afslutning!angiver antallet af perioder, aktivet afskrives over (aktivets levetid)!er den periode, afskrivningen skal beregnes for. Periode skal anvende samme enheder som levetid!angiver den sats, som saldoen falder med. <PERSON><PERSON> faktor udela<PERSON>, forudsættes den at være 2 (dobbeltsaldometoden)"}, "DISC": {"a": "(afregningsdato; udløbsdato; kurs; indløsningskurs; [datotype])", "d": "Returnerer et værdipapirs diskonto", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets kurs pr. 100 kr. i pålydende værdi!er værdipapirets indløsningsværdi pr. 100 kr. i pålydende værdi!er den datotype, der skal anvendes"}, "DOLLARDE": {"a": "(brøkdel_kr; brøkdel)", "d": "Konverterer en kronepris, udtrykt som brøk, til en kronepris udtrykt som decimaltal", "ad": "er et tal, der er udtrykt som en brøk!er heltallet, der skal anvendes som nævner i brøken"}, "DOLLARFR": {"a": "(decimal_kr; brøkdel)", "d": "Konverterer en kronepris, udtrykt som et decimaltal, til en kronepris udtrykt som brøk", "ad": "er et decimaltal!er heltallet, der skal anvendes som nævner i brøken"}, "DURATION": {"a": "(afregningsdato; udløbsdato; kupon; afkast; hyppighed; [datotype])", "d": "Returnerer den årlige varighed af et værdipapir med periodiske rentebetalinger", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets årlige kuponrente!er værdipapirets årlige afkast!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "EFFECT": {"a": "(nominel_rente; nperår)", "d": "Returnerer den årlige effektive rente", "ad": "er den nominelle rente!er antal sammensatte perioder pr. år"}, "FV": {"a": "(rente; nper; ydelse; [nv]; [type])", "d": "Returnerer den fremtidige værdi af en investering på baggrund af periodiske, konstante ydelser og en konstant rentesats", "ad": "er rentesatsen i hver periode. Brug for eksempel 6 %/4 om kvartårlige ydelser på 6 % APR!er det samlede antal ydelsesperioder i investeringen!er ydelsen i hver periode. Ydelsen kan ikke ændres i investeringens løbetid!er nutidsværdien eller den samlede værdi, som en række fremtidige ydelser er værd nu. Hvis den udelades, er Pv = 0!er en værdi, der angiver, hvornår ydelserne forfalder: ydelse i begyndelsen af perioden = 1; ydelse i slutningen af perioden = 0 eller udeladt"}, "FVSCHEDULE": {"a": "(hovedstol; tabel)", "d": "Returnerer den fremtidige værdi af en hovedstol efter at have anvendt en række sammensatte renter", "ad": "er nutidsværdien!er en matrix af rentesatser, der skal anvendes"}, "INTRATE": {"a": "(afregningsdato; udløbsdato; investering; indløsningskurs; [datotype])", "d": "Returnerer renten på et fuldt ud investeret værdipapir", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er det beløb, der er investeret i værdipapiret!er det beløb, der modtages ved værdipapirets udløb!er den datotype, der skal anvendes"}, "IPMT": {"a": "(rente; periode; nper; nv; [fv]; [type])", "d": "Returnerer rentedelen af en ydelse for en investering i en given periode, baseret på konstante periodiske ydelser og en konstant rente", "ad": "er rentesatsen i hver periode. Brug for eksempel 6 %/4 om kvartårlige ydelser på 6 % APR!er den periode, renten skal beregnes for. Den skal være mellem 1 og nper!er det samlede antal ydelsesperioder i en investering!er nutidsværdien eller den samlede værdi, som en række fremtidige ydelser er værd nu!er den fremtidige værdi eller den kassebalance, der ønskes opnået, når den sidste ydelse er betalt. Sættes til Fv = 0, hvis intet angives!er en logisk værdi, der repræsenterer, hvornår ydelserne forfalder: 1 = i begyndelsen af perioden, 0 eller ikke udfyldt = i slutningen af perioden"}, "IRR": {"a": "(værdier; [gæt])", "d": "Returnerer det interne afkast for en række pengestrømme", "ad": "er en matrix eller en reference til celler, der indeholder tal, som det interne afkast skal beregnes for!er et tal, der skønnes at ligge tæt på resultatet af IA-beregningen. Sættes til 0,1 (10 procent), hvis feltet ikke udfyldes"}, "ISPMT": {"a": "(rente; periode; nper; nv)", "d": "Returnerer den rente, der er betalt i en angivet investeringsperiode", "ad": "er rentesatsen i hver periode. Brug for eksempel 6 %/4 om kvartårlige ydelser på 6 % APR!er den periode, for hvilken renten ønskes beregnet!er det samlede antal ydelsesperioder i en investering!er den samlede værdi, som en række fremtidige ydelser er værd nu"}, "MDURATION": {"a": "(afregningsdato; udløbsdato; kupon; afkast; hyppighed; [datotype])", "d": "Returnerer <PERSON><PERSON>'s modifice<PERSON>e varighed af et værdipapir med en formodet pari på 100 kr.", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets årlige kuponrente!er værdipapirets årlige afkast!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "MIRR": {"a": "(værdier; finansrente; investeringsrente)", "d": "Returnerer den interne forrentningprocent for en række periodiske pengestrømme, hvor både investeringsudgifter og renteindtægter ved geninvestering tages i betragtning", "ad": "er en matrix eller en reference til celler, der indeholder tal, som repræsenterer en serie af ydelser (negative) og indkomster (positive) i en given periode!er den rente, du betaler på de beløb, der anvendes i pengestrømme!er den rente, der fås på pengestrømme, efterhånden som de geninvesteres"}, "NOMINAL": {"a": "(effektiv_rente; nperår)", "d": "Returnerer den årlige nominelle rente", "ad": "er den effektive rente!er antal sammensatte perioder pr. år"}, "NPER": {"a": "(rente; ydelse; nv; [fv]; [type])", "d": "Returnerer antallet af perioder for en investering på baggrund af periodiske, konstante ydelser og en konstant rentesats", "ad": "er rentesatsen i hver periode. Brug for eksempel 6 %/4 om kvartårlige ydelser på 6 % APR!er ydelsen i hver periode. Ydelsen kan ikke ændres i investeringens løbetid!er nutidsværdien eller den samlede værdi, som en række fremtidige ydelser er værd nu!er den fremtidige værdi eller den kassebalance, der ønskes opnået, når den sidste ydelse er betalt. Sættes til nul, hvis den udelades!er en logisk værdi: ydelse i begyndelsen af perioden = 1; ydelse i slutningen af perioden = 0 eller udeladt"}, "NPV": {"a": "(rente; værdi1; [værdi2]; ...)", "d": "Returnerer den aktuelle nettoværdi af en investering på baggrund af en diskontosats og en serie fremtidige betalinger (negative værdier) og indkomst (positive værdier)", "ad": "er diskonteringssatsen for en hel periode!er 1-254 regelmæssige ydelser og indtægter i slutningen af hver periode"}, "ODDFPRICE": {"a": "(afregningsdato; udløbsdato; udstedelsesdato; første_kupon; rente; afkast; indløsningskurs; hyppighed; [datotype])", "d": "Returnerer kursen pr. 100 kr. nominel værdi for et værdipapir med en ulige første periode", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets udstedelsesdato, angivet som et serielt datotal!er værdipapirets første kupondato, angivet som et serielt datotal!er værdipapirets rente!er værdipapirets årlige afkast!er værdipapirets indløsningskurs pr. 100 kr. nominel værdi!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "ODDFYIELD": {"a": "(afregningsdato; udløbsdato; udstedelsesdato; første_kupon; rente; kurs; indløsningskurs; hyppighed; [datotype])", "d": "Returnerer afkastet af et værdipapir med ulige første periode", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets udstedelsesdato, angivet som et serielt datotal!er værdipapirets første kupondato, angivet som et serielt datotal!er værdipapirets rente!er værdipapirets kurs!er værdipapirets indløsningsværdi pr. 100 kr. nominel værdi!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "ODDLPRICE": {"a": "(afregningsdato; udløbsdato; sidste_rente; rente; afkast; indløsningskurs; hyppighed; [datotype])", "d": "Returnerer prisen pr. 100 kr. nominel værdi, for et værdipapir med ulige sidste periode", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets sidste udbyttedato, angivet som et serielt datotal!er værdipapirets rente!er værdipapirets årlige afkast!er værdipapirets indløsningsværdi pr. 100 kr. i pålydende værdi!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "ODDLYIELD": {"a": "(afregningsdato; udløbsdato; sidste_rente; rente; kurs; indløsningskurs; hyppighed; [datotype])", "d": "Returnerer afkastet for et værdipapir med ulige sidste periode", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets sidste udbyttedato, angivet som et serielt datotal!er værdipapirets rente!er værdipapirets kurs!er værdipapirets indløsningsværdi pr. 100 kr. i pålydende værdi!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "PDURATION": {"a": "(rente; nv; fv)", "d": "Returnerer det på<PERSON>r<PERSON><PERSON>e antal perioder, før en investering når den angivne værdi", "ad": "er renten pr. periode.!er nutidsværdien af investeringen!er den ønskede fremtidige værdi af investeringen"}, "PMT": {"a": "(rente; nper; nv; [fv]; [type])", "d": "<PERSON><PERSON>gner ydelsen på et lån baseret på konstante ydelser og en konstant rentesats", "ad": "er rentesatsen i hver periode. Brug for eksempel 6 %/4 om kvartårlige ydelser på 6 % APR!er det samlede antal ydelser på lånet!er nutidsværdien, dvs. den samlede værdi, som en række fremtidige ydelser er værd nu!er den fremtidige værdi eller den kassebalance, der ønskes opnået, når den sidste ydelse er betalt. Sættes til 0 (nul), hvis den udelades!er en logisk værdi: ydelse i begyndelsen af perioden = 1; ydelse i slutningen af perioden = 0 eller udeladt"}, "PPMT": {"a": "(rente; periode; nper; nv; [fv]; [type])", "d": "Returnerer afdragsdelen på ydelsen for en given investering baseret på konstante periodiske ydelser og en konstant rentesats", "ad": "er rentesatsen i hver periode. Brug for eksempel 6 %/4 om kvartårlige ydelser på 6 % APR!angiver perioden og skal være mellem 1 og nper!er det samlede antal ydelsesperioder i en investering!er nutidsværdien, dvs. den samlede værdi, som en række fremtidige ydelser er værd nu!er den fremtidige værdi eller den kassebalance, der ønskes opnået, når den sidste ydelse er betalt!er en logisk værdi: ydelse i begyndelsen af perioden = 1; ydelse i slutningen af perioden = 0 eller udeladt"}, "PRICE": {"a": "(afregningsdato; udløbsdato; rente; afkast; indløsningskurs; hyppighed; [datotype])", "d": "Returnerer kursen pr. 100 kr. nominel værdi for et værdipapir med periodiske renteudbetalinger", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets årlige kuponrente!er værdipapirets årlige afkast!er værdipapirets indløsningskurs pr. 100 kr. nominel værdi!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "PRICEDISC": {"a": "(afregningsdato; udløbsdato; diskonto; indløsningskurs; [datotype])", "d": "Returnerer kursen pr. 100 kr. nominel værdi for et diskonteret værdipapir", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets diskontosats!er værdipapirets indløsningskurs pr. 100 kr. i pålydende værdi!er den datotype, der skal anvendes"}, "PRICEMAT": {"a": "(afregningsdato; udløbsdato; udstedelsesdato; rente; afkast; [datotype])", "d": "Returnerer kursen pr. 100 kr. nominel værdi for et værdipapir, der udbetaler rente ved udløb", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets udstedelsesdato, angivet som et serielt datotal!er værdipapirets rente på udstedelsesdatoen!er værdipapirets årlige afkast!er den datotype, der skal anvendes"}, "PV": {"a": "(rente; nper; ydelse; [fv]; [type])", "d": "Returnerer nutidsværdien for en investering: det totale beløb, som en række fremtidige ydelser er værd nu", "ad": "er rentesatsen i hver periode. Brug for eksempel 6 %/4 om kvartårlige ydelser på 6 % APR!er det samlede antal ydelsesperioder i en investering!er den ydelse, der betales hver periode, og som ikke kan ændres i investeringens løbetid!er den fremtidige værdi eller den kassebalance, der ønskes opnået, når den sidste ydelse er betalt!er en logisk værdi: ydelse i begyndelsen af perioden = 1; ydelse i slutningen af perioden = 0 eller udeladt"}, "RATE": {"a": "(nper; ydelse; nv; [fv]; [type]; [gæt])", "d": "Returnerer renten i hver periode for et lån eller en investering. Brug for eksempel 6 %/4 om kvartårlige ydelser på 6 % APR", "ad": "er det samlede antal ydelsesperioder for et lån eller en investering!er den ydelse, der betales hver periode. Ydelsen kan ikke ændres i lånets eller investeringens løbetid!er nutidsværdien, dvs. den samlede værdi, som en række fremtidige ydelser er værd nu!er den fremtidige værdi eller den kassebalance, der ønskes opnået, når den sidste ydelse er betalt. Hvis den udelades, sættes Fv = 0!er en logisk værdi: ydelse i begyndelsen af perioden = 1, ydelse i slutningen af perioden = 0 eller udeladt!er et skøn over rentefodens størrelse. Hvis feltet ikke udfyldes, sættes Gæt = 0,1 (10 procent)"}, "RECEIVED": {"a": "(afregningsdato; udløbsdato; investering; diskonto; [datotype])", "d": "Returnerer beløbet modtaget ved udløbet af et værdipapir", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er det beløb, der er investeret i værdipapiret!er værdipapirets diskontosats!er den datotype, der skal anvendes"}, "RRI": {"a": "(nper; nv; fv)", "d": "Returnerer en ækvivalent rente for væksten i en investering", "ad": "er antal perioder for investeringen!er nutidsværdien af investeringen!er fremtidsværdien af investeringen"}, "SLN": {"a": "(kø<PERSON><PERSON><PERSON>; restvæ<PERSON>; levetid)", "d": "Returnerer den lineære afskrivning for et aktiv i en enkelt periode", "ad": "er aktivets kostpris!er aktivets værdi ved afskrivningens afslutning!er antallet af afskrivningsperioder (aktivets levetid)"}, "SYD": {"a": "(kø<PERSON><PERSON><PERSON>; restværdi; levetid; periode)", "d": "Returnerer den årlige afskrivning på et aktiv i en bestemt periode", "ad": "er aktivets kostpris!er aktivets værdi ved afskrivningens afslutning!er antallet af afskrivningsperioder (aktivets levetid)!er perioden. Periode skal anvende samme enheder som leveid"}, "TBILLEQ": {"a": "(afregningsdato; udløbsdato; diskonto)", "d": "Returnerer det obligationsækvivalente afkast for en statsobligation", "ad": "er statsobligationens afregningsdato, angivet som et serielt datotal!er statsobligationens udløbsdato, angivet som et serielt datotal!er statsobligationens diskontosats"}, "TBILLPRICE": {"a": "(afregningsdato; udløbsdato; diskonto)", "d": "Returnerer kursen pr. kr. 100 nominel værdi for en statsobligation", "ad": "er statsobligationens afregningsdato, angivet som et serielt datotal!er statsobligationens udløbsdato, angivet som et serielt datotal!er statsobligationens diskontosats"}, "TBILLYIELD": {"a": "(afregningsdato; udløbsdato; kurs)", "d": "Returnerer statsobligationens afkast", "ad": "er statsobligationens afregningsdato, angivet som et serielt datotal!er statsobligationens udløbsdato, angivet som et serielt datotal!er statsobligationens kurs pr. kr. 100 i pålydende værdi"}, "VDB": {"a": "(kø<PERSON>p<PERSON>; restværdi; levetid; startperiode; slutperiode; [faktor]; [ingen_skift])", "d": "Returnerer afskrivningen på et aktiv i en specificeret periode, herund<PERSON> delper<PERSON>, vha. <PERSON><PERSON><PERSON><PERSON><PERSON>, eller en anden metode, du angiver", "ad": "er aktivets kostpris!er aktivets værdi ved afskrivningens afslutning!er antallet af afskrivningsperioder (aktivets levetid)!er startperioden, for hvilken afskrivningen skal beregnes, med de samme enheder som levetid!er slutperioden, for hvilken afskrivningen skal beregnes, med de samme enheder som levetid!er satsen, med hvilken restbeløbet afskrives. Hvis intet anføres, benyttes dobbeltsaldometoden!FALSK eller udeladt = skift til lineær afskrivning, hvis afskrivningen er større end saldoen; SAND = undlad at skifte"}, "XIRR": {"a": "(værdier; datoer; [gæt])", "d": "Returnerer den interne rente for en pengestrømsplan", "ad": "er en serie pengestrømme, der stemmer overens med en betalingsplan med datoer!er en plan over betalingsdatoer, der stemmer overens med pengestrømsbetalingerne!er et tal, som, du gætter på, er tæt på resultatet af INTERN.RENTE"}, "XNPV": {"a": "(rente; værdier; datoer)", "d": "Returnerer nutidsværdien af en pengestrømsplan", "ad": "er disk<PERSON><PERSON><PERSON><PERSON>, der gælder for pengestrømmene!er en serie af pengestrømme, der stemmer overens med en betalingsplan med datoer!er en plan over betalingsdatoer, der stemmer overens med pengestrømsbetalingerne"}, "YIELD": {"a": "(afregningsdato; udløbsdato; rente; kurs; indløsningskurs; hyppighed; [datotype])", "d": "Returnerer afkastet for et værdipapir med periodiske renteudbetalinger", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets årlige kuponrente!er værdipapirets kurs pr. 100 kr. nominel værdi!er værdipapirets indløsningsværdi pr. 100 kr. nominel værdi!er antal kuponbetalinger pr. år!er den datotype, der skal anvendes"}, "YIELDDISC": {"a": "(afregningsdato; udløbsdato; kurs; indløsningskurs; [datotype])", "d": "Returnerer det årlige afkast for et diskonteret værdipapir, f.eks. en statsobligation", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets kurs pr. 100 kr. i pålydende værdi!er værdipapirets indløsningskurs pr. 100 kr. i pålydende værdi!er den datotype, der skal anvendes"}, "YIELDMAT": {"a": "(afregningsdato; udløbsdato; udstedelsesdato; rente; kurs; [datotype])", "d": "Returnerer det årlige afkast for et værdipapir med renteudbetaling ved udløb", "ad": "er værdipapirets afregningsdato, angivet som et serielt datotal!er værdipapirets udløbsdato, angivet som et serielt datotal!er værdipapirets udstedelsesdato, angivet som et serielt datotal!er værdipapirets rente på udstedelsesdatoen!er værdipapirets kurs pr. 100 kr. i pålydende værdi!er den datotype, der skal anvendes"}, "ABS": {"a": "(tal)", "d": "Returnerer den absolutte værdi af et tal, dvs. tallet uden fortegn", "ad": "er det reelle tal, som du vil have oplyst den absolutte værdi af"}, "ACOS": {"a": "(tal)", "d": "Returnerer arcus cosinus til et tal, i radianer i intervallet 0 til pi. Arcus cosinus er den vinkel, hvis cosinus er Tal", "ad": "er den ønskede vinkels cosinus og skal være et tal mellem -1 og 1"}, "ACOSH": {"a": "(tal)", "d": "Returnerer den inverse hyperbolske cosinus til et tal", "ad": "er et vilkårligt reelt tal lig med eller større end 1"}, "ACOT": {"a": "(tal)", "d": "Returnerer arcus cotangens til et tal i radianer i intervallet 0 til Pi.", "ad": "er cotangens til den ønskede vinkel"}, "ACOTH": {"a": "(tal)", "d": "Returnerer den inverse hyperbolske cotangens af et tal", "ad": "er den hyperbolske cotangens af den ønskede vinkel"}, "AGGREGATE": {"a": "(funktion; indstillinger; ref1; ...)", "d": "Returnerer en aggregering på en liste eller en database", "ad": "er tallene 1 til 19, der angiver oversigtsfunktionen for aggregeringen.!er det tal mellem 0 og 7, der angiver de værdier, der skal ignoreres for aggregeringen!er den matrix eller det område af numeriske data, som aggregeringen skal beregnes på baggrund af!angiver positionen i matrixen. Det er k'te største, k'te mindste, k'te fraktil eller k'te kvartil.!er tallene 1 til 19, der angiver oversigtsfunktionen for aggregeringen.!er det tal mellem 0 og 7, der angiver de værdier, der skal ignoreres for aggregeringen!er 1 til 253 områder eller referencer, for hvilke du ønsker aggregeringen"}, "ARABIC": {"a": "(tekst)", "d": "Konverterer et romertal til arabisk", "ad": "er det romertal, du vil konvertere"}, "ASC": {"a": "(tekst)", "d": "For dobbeltbytetegnsætsprog ændrer funktionen tegn i fuld bredde (dobbeltbyte) til tegn i halv bredde (enkeltbyte)", "ad": "tekst, du ønsker at ændre"}, "ASIN": {"a": "(tal)", "d": "Returnerer arcus sinus til et tal, i radianer i intervallet -pi/2 til pi/2", "ad": "er den ønskede vinkels sinus og skal være et tal mellem -1 og 1"}, "ASINH": {"a": "(tal)", "d": "Returnerer den inverse hyperbolske sinus til et tal", "ad": "er et vilkårligt reelt tal lig med eller større end 1"}, "ATAN": {"a": "(tal)", "d": "Returnerer arcus tangens til et tal, i radianer i intervallet -pi/2 til pi/2", "ad": "er tangens for den ønskede vinkel"}, "ATAN2": {"a": "(x_koordinat; y_koordinat)", "d": "Returnerer de specificerede  x- og y-koordinaters arcus tangens, i radianer mellem -pi og pi, forskellig fra -pi", "ad": "er punktets x-koordinat!er punktets y-koordinat"}, "ATANH": {"a": "(tal)", "d": "Returnerer den inverse hyperbolske tangens til et tal", "ad": "er et vilkårligt reelt tal mellem -1 og 1, for<PERSON><PERSON>gt fra -1 og 1"}, "BASE": {"a": "(tal; radikand; [min_længde])", "d": "Konverterer et tal til en tekstrepræsentation med en given radikand (rod)", "ad": "er det tal, du vil konvertere!er den rodradikand, du vil konvertere tallet til!er <PERSON><PERSON><PERSON>ng<PERSON> for den returnerede streng. <PERSON><PERSON> denne <PERSON>, tilfø<PERSON><PERSON> der ikke foranstillede nuller"}, "CEILING": {"a": "(tal; betydning)", "d": "Runder et tal op til nærmeste multiplum af betydning", "ad": "er den værdi, der skal afrundes!er det multiplum, der skal afrundes til"}, "CEILING.MATH": {"a": "(tal; [betydning]; [tilstand])", "d": "<PERSON>der et tal op til det nærmeste heltal eller til det nærmeste betydende multiplum", "ad": "er den værdi, du vil afrunde!er det multiplum, du vil afrunde til!hvis dette angives, og værdien ikke er nul, afrundes der væk fra nul"}, "CEILING.PRECISE": {"a": "(tal; [betydning])", "d": "Returnerer et tal, der rundes op til nærmeste heltal eller til nærmeste multiplum af signifikans", "ad": "er den værdi, der skal afrundes!er det multiplum, der skal afrundes til"}, "COMBIN": {"a": "(tal; tal_valgt)", "d": "Returnerer antallet af kombinationer for et givet antal elementer", "ad": "er det samlede antal elementer!er antallet af elementer i hver kombination"}, "COMBINA": {"a": "(tal; tal_valgt)", "d": "Returnerer antal kombinationer med gentagelser for et givet antal elementer", "ad": "er det samlede antal elementer!er antal elementer i hver kombination"}, "COS": {"a": "(tal)", "d": "Returnerer cosinus til en vinkel", "ad": "er den vinkel i radianer, som du vil have cosinus for"}, "COSH": {"a": "(tal)", "d": "Returnerer den hyperbolske cosinus til et tal", "ad": "er et vilkårligt reelt tal"}, "COT": {"a": "(tal)", "d": "Returnerer cotangens af en vinkel", "ad": "er den vinkel i radianer, du vil have cotangens af"}, "COTH": {"a": "(tal)", "d": "Returnerer den hyperbolske cotagens af et tal", "ad": "er den vinkel i radianer, du vil have den hyperbolske cotangens af"}, "CSC": {"a": "(tal)", "d": "Returnerer cosekanten af en vinkel", "ad": "er den vinkel i radianer, du vil have cosekanten af"}, "CSCH": {"a": "(tal)", "d": "Returnerer den hyperbolske cosekant af en vinkel", "ad": "er den vinkel i radianer, du vil have den hyperbolske cosekant for"}, "DECIMAL": {"a": "(tal; radikand)", "d": "Konverterer tekstrepræsentationen af et tal i en given rod til et decimaltal", "ad": "er det tal, du vil konvertere!er rodradikanden for det tal, du konverterer"}, "DEGREES": {"a": "(vinkel)", "d": "Konverterer radianer til grader", "ad": "er den vinkel i radianer, der skal konverteres"}, "ECMA.CEILING": {"a": "(tal; betydning)", "d": "Runder et tal op til nærmeste multiplum af betydning", "ad": "er den værdi, der skal afrundes!er det multiplum, der skal afrundes til"}, "EVEN": {"a": "(tal)", "d": "Runder positive tal op og negative tal ned til nærmeste lige heltal", "ad": "er den værdi, der skal afrundes"}, "EXP": {"a": "(tal)", "d": "Returnerer e opløftet til en potens af et givet tal", "ad": "er den eksponent, der anvendes sammen med grundtalle e. Konstanten e er lig med 2,71828182845904., den naturlige logaritmes grundtal"}, "FACT": {"a": "(tal)", "d": "Returnerer et tals fakultet, svar<PERSON><PERSON> til 1*2*3*...* Tal", "ad": "er det positive tal, du vil beregne fakultetet af"}, "FACTDOUBLE": {"a": "(tal)", "d": "Returnerer et tals dobbelte fakultet", "ad": "er den værdi, for hvilken det dobbelte fakultet skal returneres"}, "FLOOR": {"a": "(tal; betydning)", "d": "Runder et tal ned til det nærmeste multiplum af betydning", "ad": "er den numeriske værdi, der skal afrundes!er det multiplum, der skal afrundes til. Tal og signifikans skal enten begge være positive eller begge være negative"}, "FLOOR.PRECISE": {"a": "(tal; [betydning])", "d": "Returnerer et tal, der rundes ned til nærmeste heltal eller til nærmeste multiplum af signifikans", "ad": "er den værdi, der skal afrundes!er det multiplum, der skal afrundes til"}, "FLOOR.MATH": {"a": "(tal; [betydning]; [tilstand])", "d": "<PERSON>der et tal ned til det nærmeste heltal eller til det nærmeste betydende multiplum", "ad": "er den værdi, du vil afrunde!er det multiplum, du vil afrunde til!hvis dette angives, og værdien ikke er nul, afrundes der væk fra nul"}, "GCD": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den største fælles divisor", "ad": "er 1 til 255 værdier"}, "INT": {"a": "(tal)", "d": "<PERSON>der et tal ned til nærmeste heltal", "ad": "er det reelle tal, du vil runde ned til et heltal"}, "ISO.CEILING": {"a": "(tal; [betydning])", "d": "Returnerer et tal, der rundes op til nærmeste heltal eller til nærmeste multiplum af signifikans. U<PERSON>et tallets fortegn, rundes tallet op. Men hvis tallet eller signifikansen er nul, returneres nul.", "ad": "er den værdi, der skal afrundes!er det multiplum, der skal afrundes til"}, "LCM": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer det mindste fælles multiplum", "ad": "er 1 til 255 væ<PERSON><PERSON>, hvis mindste fælles multiplum skal beregnes"}, "LN": {"a": "(tal)", "d": "Returnerer et tals naturlige logaritme", "ad": "er det positive reelle tal, den naturlige logaritme skal beregnes for"}, "LOG": {"a": "(tal; [grundtal])", "d": "Returnerer et tals logaritme på grundlag af et angivet grundtal", "ad": "er det positive reelle tal, logaritmen skal beregnes for!er logaritmens grundtal. Sættes til 10, hvis feltet ikke udfyldes"}, "LOG10": {"a": "(tal)", "d": "Returnerer et tals titals logaritme", "ad": "er det positive reelle tal, 10-tals logaritmen skal beregnes for"}, "MDETERM": {"a": "(matrix)", "d": "Returnerer determinanten for en matrix", "ad": "er en numerisk matrix med det samme antal rækker og kolonner, enten et celleområde eller en matrixkonstant"}, "MINVERSE": {"a": "(matrix)", "d": "Returnerer den inverse matrix for en matrix", "ad": "er en numerisk matrix med det samme antal rækker og kolonner, enten et celleområde eller en matrixkonstant"}, "MMULT": {"a": "(matrix1; matrix2)", "d": "Returnerer matrixproduktet af to matrixer, dvs. en matrix med samme antal rækker som matrix1 og samme antal kolonner som matrix2", "ad": "er den første matrix af tal, der skal ganges, hvor antallet af kolonner skal være lig med antallet af rækker i Matrix 2"}, "MOD": {"a": "(tal; divisor)", "d": "Returnerer restværdien ved en division", "ad": "er det tal, resten efter en division skal findes for!er det tal, som tal skal divideres med"}, "MROUND": {"a": "(tal; multiplum)", "d": "Returnerer et tal afrundet til det ønskede multiplum", "ad": "er den værdi, der skal afrundes!er det multiplum, hvortil tallet skal afrundes"}, "MULTINOMIAL": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer polynomiet af en række tal", "ad": "er 1 til 255 væ<PERSON><PERSON>, hvis polynomium skal beregnes"}, "MUNIT": {"a": "(dimension)", "d": "Returnerer enhedsmatrixen for den angivne dimension", "ad": "er et heltal, der specificerer dimensionen af den enhedsmatrix, du vil returnere"}, "ODD": {"a": "(tal)", "d": "Runder positive tal op og negative tal ned til nærmeste ulige heltal", "ad": "er den værdi, der skal afrundes"}, "PI": {"a": "()", "d": "Returnerer værdien af pi  (3.14159265358979) med 15 decimalers nøjagtighed", "ad": ""}, "POWER": {"a": "(tal; potens)", "d": "Returnerer resultatet af et tal opløftet til en potens", "ad": "er grundtallet, som kan være et vilkårligt reelt tal!er den eksponent, som grundtallet skal opløftes til"}, "PRODUCT": {"a": "(tal1; [tal2]; ...)", "d": "Multiplice<PERSON> de tal, der er givet som argumenter", "ad": "er 1-255 tal, logiske værdier eller tal i bogstavformat, der skal multipliceres med hinanden"}, "QUOTIENT": {"a": "(tæller; nævner)", "d": "Returnerer heltalsdelen af en division", "ad": "er dividenden!er divisoren"}, "RADIANS": {"a": "(vinkel)", "d": "Konverterer grader til radianer", "ad": "er en vinkel i grader, som du vil konvertere"}, "RAND": {"a": "()", "d": "Returnerer et tilfældigt tal mellem 0 og 1, jæ<PERSON><PERSON> fordelt (ændres ved ny beregning)", "ad": ""}, "RANDARRAY": {"a": "([rækker]; [kolonner]; [min]; [maks]; [heltal])", "d": "Returnerer en matrix af tilfældige tal", "ad": "antallet af rækker i den returnerede matrix!antallet af kolonner i den returnerede matrix!det minimale antal, du vil have returneret!det maksimale antal, du vil have returneret!returner et heltal eller et decimaltal.SAND for et heltal,FALSK for et decimaltal"}, "RANDBETWEEN": {"a": "(<PERSON><PERSON>; stø<PERSON>)", "d": "Returnerer et tilfældigt tal mellem de tal, der angives", "ad": "er det mindste heltal, der kan returneres!er det største heltal, der kan returneres"}, "ROMAN": {"a": "(tal; [format])", "d": "Konverterer et arabertal til et romertal, som tekst", "ad": "er det arabertal, der skal konverteres!er et tal, der angiver, den ønskede type romertal."}, "ROUND": {"a": "(tal; antal_cifre)", "d": "<PERSON><PERSON><PERSON><PERSON> et tal til et angivet antal decimaler", "ad": "er det tal, der skal afrundes!er det antal decimaler, der skal afrundes til. Negative tal afrundes til venstre for kommaet, nul til det nærmeste heltal"}, "ROUNDDOWN": {"a": "(tal; antal_cifre)", "d": "<PERSON><PERSON> et tal ned (mod nul)", "ad": "er et vilkårligt reelt tal, der skal nedrundes!er det antal decimaler, tallet skal afrundes til. Hvis det er negativt, skal der afrundes til venstre for kommaet, hvis det udelades eller er nul, skal der afrundes til det nærmeste heltal"}, "ROUNDUP": {"a": "(tal; antal_cifre)", "d": "<PERSON>der et tal op (væk fra nul)", "ad": "er et vilkårligt reelt tal, der skal rundes op!er det antal decimaler, tallet skal afrundes til. Hvis det er negativt, skal der afrundes til venstre for kommaet, hvis det udelades eller er nul, skal der afrundes til det nærmeste heltal"}, "SEC": {"a": "(tal)", "d": "Returnerer sekanten af en vinkel", "ad": "er den vinkel i radianer, du vil have sekanten af"}, "SECH": {"a": "(tal)", "d": "Returnerer den hyperbolske sekant af en vinkel", "ad": "er den vinkel i radianer, du vil have den hyperbolske sekant af"}, "SERIESSUM": {"a": "(x; n; m; koefficienter)", "d": "Returnerer summen af potensserie, baseret på formlen", "ad": "er inputværdien for potensserien!er den oprindelige potens, x skal opløftes til!er den trinværdi n skal øges med for hvert udtryk i serien!er et sæt koefficienter, som hver efterfølgende potens af x ganges med"}, "SIGN": {"a": "(tal)", "d": "Returnerer et tals fortegn: 1, hvis tallet er positivt, nul, hvis tallet er nul og -1, hvis tallet er negativt", "ad": "er et vilkårligt reelt tal"}, "SIN": {"a": "(tal)", "d": "Returnerer sinus til en vinkel", "ad": "er den vinkel i radianer, som sinus skal beregnes til. Grader * PI()/180 = radianer"}, "SINH": {"a": "(tal)", "d": "Returnerer den hyperbolske sinus til et tal", "ad": "er et vilkårligt reelt tal"}, "SQRT": {"a": "(tal)", "d": "Returnerer kvadratroden af et tal", "ad": "er det tal, kvadratroden skal beregnes af"}, "SQRTPI": {"a": "(tal)", "d": "Returnerer kva<PERSON><PERSON>den af (tal * pi)", "ad": "er det tal, der skal ganges med pi"}, "SUBTOTAL": {"a": "(funktion; reference1; ...)", "d": "Returnerer en subtotal på en liste eller i en database", "ad": "er et tal fra 1-11, der angiver, hvilken sumfunktion der skal bruges til at beregne subtotalen.!er 1 til 254 områder eller referencer, som subtotalen skal beregnes for"}, "SUM": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON><PERSON> alle tal i et celleområde sammen", "ad": "er 1-255 tal, der skal lægges sammen. Logiske værdier og tekst ignoreres i cellerne, også hvis de indtastes som argumenter"}, "SUMIF": {"a": "(områ<PERSON>; kriterier; [sum_område])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, der er specificeret af en given betingelse eller et givet kriterium", "ad": "er det celleomr<PERSON><PERSON>, der skal evalueres!er betingelsen eller kriteriet i form af et tal, et udtryk eller tekst, der angiver, hvilke celler der summeres!er de celler, der skal summeres. <PERSON><PERSON> feltet ikke udfyldes, benyttes cellerne i området"}, "SUMIFS": {"a": "(sum<PERSON><PERSON><PERSON><PERSON>; kriterieomr<PERSON><PERSON>; kriterier; ...)", "d": "<PERSON><PERSON><PERSON> de cell<PERSON>, der er angivet med et givet sæt betingelser eller kriterier", "ad": "er de faktiske celler, der skal adderes.!er det celleområ<PERSON>, der skal evalueres i forhold til den konkrete betingelse!er betingelsen eller kriteriet i form af et tal, et udtryk eller tekst, der definerer de celler, som skal  adderes"}, "SUMPRODUCT": {"a": "(matrix1; [matrix2]; [matrix3]; ...)", "d": "Returnerer summen af produkterne af tilsvarende områder eller matrixer", "ad": "er 2-255 matrixer, hvis komponenter ønskes ganget og derefter lagt sammen. Alle matrixer skal have de samme dimensioner"}, "SUMSQ": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer summen af kva<PERSON>ter for argumenterne. Argumenterne kan være tal, matrixer, navne eller referencer til celler, der indeholder tal", "ad": "er 1-255 tal, matrixer, navne eller referencer til matrixer, som summen af kvadrater skal beregnes for"}, "SUMX2MY2": {"a": "(matrix_x; matrix_y)", "d": "Opsummerer forskellene mellem kvadraterne af to tilsvarende områder eller matrixer", "ad": "er den første matrix eller det første værdinterval. Det kan være et tal, et navn, en matrix eller en reference, der indeholder tal!er den anden matrix eller det andet værdiinterval. Det kan være et tal, et navn, en matrix eller en reference, der indeholder tal"}, "SUMX2PY2": {"a": "(matrix_x; matrix_y)", "d": "Returnerer summen af summen af kvadraterne af værdier i to tilsvarende områder eller matrixer", "ad": "er den første matrix eller det første værdinterval. Det kan være et tal, et navn, en matrix eller en reference, der indeholder tal!er den anden matrix eller det andet værdiinterval. Det kan være et tal, et navn, en matrix eller en reference, der indeholder tal"}, "SUMXMY2": {"a": "(matrix_x; matrix_y)", "d": "Opsummerer kvadraterne af forskellene i to tilsvarende områder eller matrixer", "ad": "er den første matrix eller det første værdiinterval. Det kan være et tal, et navn, en matrix eller en reference, der indeholder tal!er den anden matrix eller det andet værdiinterval. Det kan være et tal, et navn, en matrix eller en reference, der indeholder tal"}, "TAN": {"a": "(tal)", "d": "Returnerer tangens til en vinkel", "ad": "er den vinkel i radianer, som tangens skal beregnes til. Grader * PI()/180 = radianer"}, "TANH": {"a": "(tal)", "d": "Returnerer hyperbolsk tangens til et tal", "ad": "er et vilkårligt reelt tal"}, "TRUNC": {"a": "(tal; [antal_cifre])", "d": "Afkorter et tal til et heltal ved at fjerne decimal- eller procentdelen af tallet", "ad": "er det tal, der skal afkortes!er et tal, der angiver, hvor nøjagtig afkortelsen skal være. Sættes til 0 (nul), hvis intet angives"}, "ADDRESS": {"a": "(række; kolonne; [abs_nr]; [a1]; [arknavn])", "d": "Opretter en cellereference som tekst ud fra angivne række- og kolonnenumre", "ad": "er det rækkenummer, der skal bruges i cellereferencen: Rækkenummer = 1 for række 1!er det kolonnenummer, der skal bruges i cellereferencen. Kolonnenummeret er f.eks.  4 for kolonne D!specificerer referencetypen: absolut = 1; absolut række/relativ kolonne = 2; relativ række/absolut kolonne = 3; relativ = 4!er en logisk værdi, der angiver  referencemåden: type A1 = 1 eller SAND; type R1C1 = 0 eller FALSK!er den tekst, der angiver navnet på det regneark, der skal bruges som ekstern reference"}, "CHOOSE": {"a": "(indeksnr; værdi1; [værdi2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> en værdi eller en handling fra en liste over værdier baseret på et indeksnummer", "ad": "angiver, hvilket værdiargument der skal vælges. Indeksnr. skal enten være mellem 1 og 254, være en formel eller være en reference til et tal mellem 1 og 254!er 1-254 tal, cellere<PERSON><PERSON>, <PERSON><PERSON><PERSON> nav<PERSON>, formler, funk<PERSON><PERSON> eller tekst, som VÆLG vælger fra"}, "COLUMN": {"a": "([reference])", "d": "Returnerer kolonnenummeret på en reference", "ad": "er den celle eller det celleo<PERSON>r<PERSON><PERSON>, som du vil have oplyst kolonnenummeret for. <PERSON><PERSON> det ikke udfy<PERSON>, ben<PERSON><PERSON> den celle, der indeholder funktionen KOLONNE"}, "COLUMNS": {"a": "(matrix)", "d": "Returnerer antallet af kolonner i en matrix eller en reference", "ad": "er en matrix, en matrixformel eller en reference til et celleområde, hvor du vil have oplyst antallet af kolonner"}, "FORMULATEXT": {"a": "(reference)", "d": "Returnerer en formel som en streng", "ad": "er en reference til en formel"}, "HLOOKUP": {"a": "(opslagsværdi; tabelmatrix; rækkeindeks; [intervalopslag])", "d": "<PERSON><PERSON><PERSON> efter en bestemt værdi i øverste række af en tabel eller i en matrix og returnerer værdien i den samme kolonne fra en række, som du angiver", "ad": "er værdien i første række af tabellen og kan være en værdi, en reference eller en tekststreng!angiver en tabel med tekst, tal eller logiske værdier, hvor man kan slå data op. Tabelmatrix kan være en reference til et område eller områdenavn!angiver rækkenummeret i en tabel_matrix, hvorfra den tilsvarende værdi skal returneres. Den første række med værdier i tabellen er række 1!angiver en logisk værdi: find det nærmeste match i den øverste række (sorteret i stigende rækkefølge) = SAND eller udelades; find er et nøjagtigt match = FALSK"}, "HYPERLINK": {"a": "(linkplacering; [fuldt_navn])", "d": "Opretter en genvej eller et jump, der åbner et dokument, der er lagret på harddisken, en netserver eller på internettet", "ad": "er den tekst, der leverer sti- eller filnavnet til det dokument, der skal åbnes, harddiskplacering, UNC-adresse eller URL-sti!er tekst eller et tal, der vises i cellen. <PERSON><PERSON> udeladt, viser cellen Link_placeringsteksten"}, "INDEX": {"a": "(matrix; række; [kolonne]!reference; række; [kolonne]; [omr<PERSON><PERSON>])", "d": "Returnerer en værdi fra eller reference til en celle ved skæringspunktet mellem en bestemt række og kolonne i et givet område", "ad": "er et celleområde eller en matrixkonstant.!markerer den række i matrixen eller referencen, hvorfra der skal returneres en værdi. Hvis der ikke markeres en række, skal kolonnenummeret angives!markerer den kolonne i matrixen eller referencen, hvorfra der skal returneres en værdi. Hvis der ikke markeres en kolonne, skal rækkenummeret angives!er en reference til et eller flere celleområder!markerer den række i matrixen eller referencen, hvorfra der skal returneres en værdi. Hvis der ikke markeres en række, skal kolonnenummeret angives!markerer den kolonne i matrixen eller referencen, hvorfra der skal returneres en værdi. Hvis der ikke markeres en kolonne, skal rækkenummeret angives!markerer et område i referencen, hvorfra der skal returneres en værdi. Det første område, der markeres eller indtastes, kaldes område 1, det næste område 2"}, "INDIRECT": {"a": "(reference; [a1])", "d": "Returnerer den reference, der specificeres af en tekststreng", "ad": "er en reference til en celle, som indeholder en reference af typen A1- eller R1C1, et navn defineret som en reference eller en tekstreference til en celle!er en logisk værdi, der specificerer referencetypen i Ref_tekst: R1C1-type = FALSE; A1-type = TRUE eller udeladt"}, "LOOKUP": {"a": "(opslagsværdi; opslagsvektor; [resultatvektor]!opslagsværdi; matrix)", "d": "<PERSON><PERSON><PERSON> efter værdier i en række, en kolonne eller en matrix. Sikrer kompatibilitet med ældre versioner", "ad": "er en værdi, som SLÅ.OP søger efter i opslagsvektoren, og som kan være et tal, tekst, en logisk værdi eller et navn på eller en reference til en værdi!er et område, der kun indeholder én række eller én kolonne med tekst, tal eller logiske værdier anbragt i stigende rækkefølge!er et område, der kun indeholder én række eller én kolonne i samme størrelse som opslagsvektoren!er en værdi, som SLÅ.OP søger efter i en matrix, og som kan være et tal, tekst, en logisk værdi eller et navn på eller en reference til en værdi!er et celleområde, der indeholder tekst, tal eller logiske værdier, der skal sammenlignes med Opslagsværdi"}, "MATCH": {"a": "(opslagsværdi; opslagsmatrix; [sammenligningstype])", "d": "Returnerer den relative placering af et element i en matrix, som svarer til en angivet værdi i en angivet rækkefølge", "ad": "er den værdi, der skal bruges til at finde den ønskede værdi i matrixen, et tal, tekst, en logisk værdi eller en reference!er et sammenhængende celleområde, der indeholder mulige opslagsværdier, en matrix med værdier eller en reference til en matrix!er et af tallene 1, 0 eller -1, som angiver, hvilken værdi der skal returneres."}, "OFFSET": {"a": "(reference; rækker; kolonner; [højde]; [bredde])", "d": "Returnerer en reference til et område, der er et givet antal rækker og kolonner fra en given reference", "ad": "er den reference, som forskydningen, en cellereference eller et celleområde skal baseres på!er det antal rækker i opad- eller nedadgående retning, som den øverste venstre celle skal referere til!er det antal kolonner til venstre eller højre, som resultatets øverste venstre celle skal referere til!er den ønskede højde i antal rækker for den returnerede reference. Den samme højde som Reference, hvis den udelades!er den ønskede bredde i antal kolonner for den returnerede reference. Den samme bredde som Reference, hvis den udelades"}, "ROW": {"a": "([reference])", "d": "Returnerer ræk<PERSON><PERSON>meret for en reference", "ad": "er den celle eller det celleo<PERSON>r<PERSON><PERSON>, som du vil have oplyst rækkenummeret for. <PERSON><PERSON> det udela<PERSON>, <PERSON><PERSON><PERSON> den celle, der indeholder funktionen RÆKKE"}, "ROWS": {"a": "(matrix)", "d": "Returnerer antallet af rækker i en reference eller en matrix", "ad": "er en matrix, en matrixformel eller en reference til et celleområde, hvor du vil have oplyst antallet af rækker"}, "TRANSPOSE": {"a": "(matrix)", "d": "Konverterer et lodret celleområde til et vandret område eller omvendt", "ad": "er et celleområde i et regneark eller en matrix, som skal transponeres"}, "UNIQUE": {"a": "(matrix; [efter_kol]; [præcis_en_gang])", "d": "Returnerer de entydige værdier fra et interval eller en matrix.", "ad": "det interval eller den matrix, du vil returnere entydige rækker eller kolonner fra!er en logisk værdi: sammenlign rækker med hinanden, og returner de entydige rækker = FALSE eller udeladt; sammenlign kolonner med hinanden, og returner de entydige kolonner = TRUE!er en logisk værdi: returner de rækker eller kolonner, der findes præcis én gang i matrixen = TRUE; returner alle entydige rækker eller kolonner fra matrixen = FALSE eller udeladt"}, "VLOOKUP": {"a": "(opslagsværdi; tabelmatrix; kolonneindeks_nr; [intervalopslag])", "d": "<PERSON><PERSON><PERSON> efter en værdi i den første kolonne i en tabel og returnerer en værdi i den samme række fra en anden kolonne, du har angivet. Tabellen skal som standard sorteres i stigende rækkefølge", "ad": "er den værdi, der skal findes i matrixens første kolonne. Det kan være en værdi, en reference eller en tekststreng!er en tabel med tekst, tal eller logiske værdier, hvor dataene findes. Tabelmatrix kan være en reference til et område eller et områdenavn!er kolonnenummeret i tabelmatrixen, hvorfra den tilsvarende værdi skal returneres. Den første kolonne med værdier i tabellen er kolonne 1!angiver en logisk værdi: find det nærmeste match i den første kolonne = SAND eller udeladt (sorteret i stigende rækkefølge); find et nøjagtigt match = FALSK"}, "XLOOKUP": {"a": "(opslagsværdi; opslagsmatrix; returmatrix; [hvis_ikke_fundet]; [matchtilstand]; [søgetilstand])", "d": "sø<PERSON> efter et match i et område eller en matrix og returnerer det tilsvarende element fra et andet område eller en anden matrix. Som standard bruges et nøjagtigt match", "ad": "er værdien, der skal søges efter!er matrixen eller området, der søges i!er matrixen eller området, der returneres!returneres, hvis der ikke findes et match!angiver, hvordan opslagsværdien matches mod værdierne i opslagsmatrixen!angiv den søgetilstand, der skal bruges. Som standard bruges en første til sidste-søgning"}, "CELL": {"a": "(info; [reference])", "d": "Returnerer oplysninger om formatering, placering eller indholdet af en celle", "ad": "en tekstværdi, der angiver, hvilken type celleoplysninger du vil returnere!den celle, du vil have oplysninger om"}, "ERROR.TYPE": {"a": "(fej<PERSON><PERSON><PERSON><PERSON>)", "d": "Returnerer et tal, der svarer til en fejlværdi.", "ad": "er den fejlværdi, som du vil have id-nummeret til, og kan være en faktisk fejlvæ<PERSON> eller en reference til en celle, der indeholder en fejlværdi"}, "ISBLANK": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en reference refererer til en tom celle, og returnerer SAND eller FALSK", "ad": "er den celle, eller et navn, der refererer til den celle, der øns<PERSON> testet"}, "ISERR": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er en fejl foruden #I/T, og returnerer SAND eller FALSK", "ad": "er den værdi, der ønskes testet. Værdien kan henvise til en celle, en formel eller til et navn, som henviser til en celle, formel eller værdi"}, "ISERROR": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er en fejl, og returnerer SAND eller FALSK", "ad": "er den værdi, der ønskes testet. Værdien kan henvise til en celle, en formel eller til et navn, som henviser til en celle, formel eller værdi"}, "ISEVEN": {"a": "(tal)", "d": "Returnerer SAND, hvis tallet er lige", "ad": "er den værdi, der skal testes"}, "ISFORMULA": {"a": "(reference)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en reference er til en celle, der indeholder en formel, og returnerer SAND eller FALSK", "ad": "er en reference til den celle, du vil kontrollere. Referencen kan være en cellereference, en formel eller et navn, der refererer til en celle"}, "ISLOGICAL": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er en logisk værdi (SAND eller FALSK), og returnerer SAND eller FALSK", "ad": "er den værdi, der øns<PERSON> testet. Værdien kan referere til en celle, en formel eller til et navn, som refererer til en celle, formel eller værdi"}, "ISNA": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er #I/T, og returnerer SAND eller FALSK", "ad": "er den værdi, der øns<PERSON> testet. Værdien kan referere til en celle, en formel eller til et navn, som refererer til en celle, formel eller værdi"}, "ISNONTEXT": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi ikke er tekst (tomme celler er ikke tekst), og returnerer SAND eller FALSK", "ad": "er den værdi, der ønskes testet: En celle, en formel eller et navn, som refererer til en celle, formel eller værdi"}, "ISNUMBER": {"a": "(værdi)", "d": "Undersøger om en værdi er et tal, og returnerer SAND eller FALSK", "ad": "er den værdi, der øns<PERSON> testet. Værdien kan referere til en celle, en formel eller til et navn, som refererer til en celle, formel eller værdi"}, "ISODD": {"a": "(tal)", "d": "Returnerer SAND, hvis tallet er ulige", "ad": "er den værdi, der skal testes"}, "ISREF": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er en reference, og returnerer SAND eller FALSK", "ad": "er den værdi, der øns<PERSON> testet. Værdien kan referere til en celle, en formel eller til et navn, som refererer til en celle, formel eller værdi"}, "ISTEXT": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er tekst, og returnerer SAND eller FALSK", "ad": "er den værdi, der øns<PERSON> testet. Værdien kan referere til en celle, en formel eller til et navn, som refererer til en celle, formel eller værdi"}, "N": {"a": "(værdi)", "d": "Konverterer en ikke-numerisk værdi til et tal. Datoer konverteres til serienumre, SAND til 1, og alt andet til 0 (nul)", "ad": "er den værdi, der skal konverteres"}, "NA": {"a": "()", "d": "Returnerer fejlværdien #I/T (værdien er ikke tilgængelig)", "ad": ""}, "SHEET": {"a": "([værdi])", "d": "Returnerer arknummeret for det ark, der refereres til", "ad": "er navnet på et ark eller en reference, du vil have arknummeret for. <PERSON><PERSON> dette udela<PERSON>, returneres det antal ark, som indeholder funktionen"}, "SHEETS": {"a": "([reference])", "d": "Returnerer antal ark i en reference", "ad": "er en reference, hvor du vil vide, hvor mange ark den indeholder. <PERSON><PERSON> de<PERSON> udela<PERSON>, returneres det antal ark i projektmappen, som indeholder funktionen"}, "TYPE": {"a": "(værdi)", "d": "Returnerer et heltal, der repræsenterer datatypen for en værdi: tal = 1; tekst = 2; logisk værdi = 4; fejlværdi = 16; matrix = 64; sammensat data = 128", "ad": "kan være enhver værdi"}, "AND": {"a": "(logisk1; [logisk2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, om alle argumenter er SAND, og returnerer SAND, hvis alle argumenter er SAND", "ad": "er 1-255 <PERSON><PERSON><PERSON><PERSON>, du vil teste, som kan være enten SAND eller FALSK, og som kan være logiske værdier, matrixer eller referencer"}, "FALSE": {"a": "()", "d": "Returnerer den logiske værdi FALSK", "ad": ""}, "IF": {"a": "(logisk_test; [værdi_hvis_sand]; [værdi_hvis_falsk])", "d": "<PERSON><PERSON><PERSON><PERSON>, om et kriterium er opfyldt, og returnerer en værdi, hvis SAND, og en anden værdi, hvis FALSK", "ad": "er en vilkårlig værdi eller et vilkårligt udtryk, der kan evalueres som SAND eller FALSK!er den værdi, der returneres, hvis logisk_test er SAND. Hvis intet angives, returneres SAND. Du kan indlejre op til syv HVIS-funktioner!er den værdi, der returneres, hvis logisk_test er FALSK. Hvis intet angives, returneres FALSK"}, "IFS": {"a": "(logisk_test; værdi_hvis_sand; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en eller flere betingelser er opfyldt, og returnerer en værdi, der svarer til den første SAND-betingelse", "ad": "er en hvilken som helst værdi eller et hvilket som helst udtryk, der kan evalueres som SAND eller FALSK!er den værdi, der returneres, hvis logisk_test er SAND"}, "IFERROR": {"a": "(værdi; værdi_hvis_fejl)", "d": "Returnerer udtrykket værdi_hvis_fejl, hvis udtrykket er en fejl, og returnerer ellers værdien af selve udtrykket", "ad": "er en vilkårlig værdi et vilkårligt udtryk eller en vilkårlig reference!er en vilkårlig værdi et vilkårligt udtryk eller en vilkårlig reference"}, "IFNA": {"a": "(værdi; værdi_hvis_it)", "d": "Returnerer den angivne værdi, hvis udtrykket evalueres til #I/T. Ellers returneres resultatet af udtrykket", "ad": "er en værdi, et udtryk eller en reference!er en værdi, et udtryk eller en reference"}, "NOT": {"a": "(logisk)", "d": "Ændrer FALSK til SAND eller SAND til FALSK", "ad": "er en værdi eller et udtryk, der kan evalueres som SAND eller FALSK"}, "OR": {"a": "(logisk1; [logisk2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, om nogle af argumenterne er SAND, og returnerer SAND eller FALSK. Returnerer kun FALSK, hvis alle argumenter er FALSK", "ad": "er 1-255 <PERSON><PERSON><PERSON><PERSON>, du vil teste, som kan være enten SAND eller FALSK"}, "SWITCH": {"a": "(udtryk; værdi1; resultat1; [standard_eller_værdi2]; [resultat2]; ...)", "d": "Evaluerer et udtryk i forhold til en liste med værdier og returnerer resultatet, der svarer til den første tilsvarende værdi. Hvis der ikke er et match, returneres en valgfri standardværdi", "ad": "er et udtryk, der skal evalueres!er en værdi, der sammenlignes med udtrykket!er et resultat, der skal returneres, hvis den tilsvarende værdi svarer til udtrykket"}, "TRUE": {"a": "()", "d": "Returnerer den logiske værdi SAND", "ad": ""}, "XOR": {"a": "(logisk1; [logisk2]; ...)", "d": "Returnerer et logisk 'Eksklusivt eller' for alle argumenterne", "ad": "er 1 til 254 bet<PERSON><PERSON><PERSON>, du vil teste, og som kan være enten SAND eller FALSK og kan være logiske værdier, matrixer eller referencer"}, "TEXTBEFORE": {"a": "(tekst, afgrænser, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Returnerer tekst, der er før afgrænsende tegn.", "ad": " <PERSON> te<PERSON>, du vil søge efter afgrænseren.! <PERSON><PERSON><PERSON> eller strengen, der skal bruges som afgrænsning.! Den ønskede forekomst af afgrænsningstegn. Standarden er 1. Et negativt tal søger fra slutningen.! Søger i teksten efter et afgrænsningsmatch. Som standard udføres et match, der skelner mellem store og små bogstaver.! Om afgrænsningen skal matches mod slutningen af teksten. Som standard matches de ikke.! Returneres, hvis der ikke findes noget match. Som standard returneres #N/A"}, "TEXTAFTER": {"a": "(tekst, afgrænser, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Returnerer tekst, der er efter afgrænsende tegn.", "ad": " <PERSON> te<PERSON>, du vil søge efter afgrænseren.! <PERSON><PERSON><PERSON> eller strengen, der skal bruges som afgrænsning.! Den ønskede forekomst af afgrænsningstegn. Standarden er 1. Et negativt tal søger fra slutningen.! Søger i teksten efter et afgrænsningsmatch. Som standard udføres et match, der skelner mellem store og små bogstaver.! Om afgrænsningen skal matches mod slutningen af teksten. Som standard matches de ikke.! Returneres, hvis der ikke findes noget match. Som standard returneres #N/A"}, "TEXTSPLIT": {"a": "(tekst, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": " Opdeler tekst i rækker eller kolonner ved hjælp af afgrænsere.", "ad": " Den tekst, der skal opdeles! Tegn eller streng, der skal opdeles kolonner efter.! Tegn eller streng, der skal opdeles rækker efter.! Om tomme celler skal ignoreres. Er som standard FALSK.! <PERSON><PERSON>ger efter et afgrænsningsmatch i teksten. Som standard udføres et match, der skelner mellem store og små bogstaver.! Den værdi, der skal bruges til udfyldning. Som standard bruges #N/A."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Ombryd en række- eller kolonnevektor efter et angivet antal værdier.", "ad": "Vektoren eller <PERSON>n, der skal ombrydes.!Det maksimale antal værdier pr. række.!Den værdi, der skal føjes til. Standarden er #N/A."}, "VSTACK": {"a": "(matrix1, [matrix2], ...)", "d": "Stabler matrixer lodret i én matrix.", "ad": "En matrix eller reference, der skal stables."}, "HSTACK": {"a": "(matrix1, [matrix2], ...)", "d": "St<PERSON>r matrixer vandret i én matrix.", "ad": "En matrix eller reference, der skal stables."}, "CHOOSEROWS": {"a": "(matrix, row_num1, [row_num2], ...)", "d": " Returnerer rækker fra en matrix eller reference.", "ad": "<PERSON><PERSON>, der indeholder de ræ<PERSON>, der skal returneres.!Nummeret på den række, der skal returneres."}, "CHOOSECOLS": {"a": "(matrix, col_num1, [col_num2], ...)", "d": " Returnerer kolonner fra en matrix eller en reference.", "ad": "<PERSON><PERSON> eller referencen, der indeholder de kolonner, der skal returneres.!Nummeret på den kolonne, der skal returneres."}, "TOCOL": {"a": "(matrix, [ignorer], [scan_by_column])", "d": "Returnerer matrixen som én kolonne.", "ad": "Matrixen eller referencen, der skal returneres som en kolonne.!Om visse typer værdier skal ignoreres. Som standard ignoreres ingen værdier.!Scan matrixen efter kolonne. Som standard scannes matrixen efter række."}, "TOROW": {"a": "(matrix, [ignorer], [scan_by_column])", "d": "Returnerer matrixen som én række.", "ad": "Matrixen eller referencen, der skal returneres som en række.!Om visse typer værdier skal ignoreres. Som standard ignoreres ingen værdier.!Scan matrixen efter kolonne. Som standard scannes matrixen efter række."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Ombryd en række- eller kolonnevektor efter et angivet antal værdier.", "ad": "Vektoren eller <PERSON>n, der skal ombrydes.!Det maksimale antal værdier pr. kolonne.!Den værdi, der skal føjes til. Standarden er #N/A."}, "TAKE": {"a": "(matrix, rækker, [kolonne])", "d": "Returnerer rækker eller kolonner fra matrixens start eller slutning.", "ad": "<PERSON><PERSON>, h<PERSON><PERSON> rækker eller kolonner skal tages.!Antallet af rækker, der skal tages. En negativ værdi tager fra slutningen af matrixen.!Antallet af kolonner, der skal tages. En negativ værdi tager fra slutningen af matrixen."}, "DROP": {"a": "(matrix, rækker, [kolo<PERSON>])", "d": "Sletter rækker eller kolonner fra matrixens start eller slutning.", "ad": "<PERSON><PERSON>, h<PERSON><PERSON> rækker eller kolonner skal slippes.!Antallet af rækker, der skal slippes. En negativ værdi falder fra slutningen af matrixen.!Antallet af kolonner, der skal slippes. En negativ værdi falder fra slutningen af matrixen."}, "SEQUENCE": {"a": "(ræ<PERSON>, [kolonner], [start], [trin])", "d": "Returnerer en sekvens af tal", "ad": "antallet af rækker, der returneres!antallet af kolonner, der returneres!det første tal i sekvensen!mængden, hver efterfølgende værdi i rækken stiger med"}, "EXPAND": {"a": "(matrix, rækker, [kolo<PERSON>], [udfyld_med])", "d": "Udvider en matrix til de angivne dimensioner.", "ad": "<PERSON><PERSON>, der skal udvides.!Antallet af rækker i den udvidede matrix. <PERSON><PERSON> der mangler rækker, udvides de ikke.!Antallet af kolonner i den udvidede matrix. H<PERSON> der mangler kolonner, udvides de ikke.!<PERSON> værdi, der skal udfyldes med. Standard er #N/A."}, "XMATCH": {"a": "(opslagsværdi, opslagsmatrix, [matchtilstand], [søgetilstand])", "d": "Returnerer den relative placering af et element i en matrix. Som standard kræves en nøjagtig match", "ad": "er værdien, der skal søges efter!er matrixen eller området, der skal søges!angiver, hvordan opslagsværdien matches mod værdierne i opslagsmatrix!angiver søgetilstanden at bruge. Som standard bruges første til sidste søgning"}, "FILTER": {"a": "(matrix, inkluder, [hvis_tom])", "d": "Filtrer et område eller en matrix", "ad": "det områ<PERSON> eller den matrix, der skal filtreres!en matrix af booleske værdier, hvor SAND repræsenterer en række eller kolonne, der skal bevares!returneres, hvis ingen elementer bevares"}, "ARRAYTOTEXT": {"a": "(array, [format])", "d": "Returnerer en tekstrepræsentation af arrayet", "ad": "arrayet, der skal repræsenteres som tekst!tekstens format"}, "SORT": {"a": "(matrix, [sort<PERSON><PERSON>_indeks], [sort<PERSON><PERSON>r<PERSON>kkefø<PERSON>], [efter_kolonne])", "d": "<PERSON><PERSON><PERSON> et område eller en matrix", "ad": "området eller matrixen, der skal sorteres!et tal, der angiver den række eller kolonne, der skal sorteres efter!et tal, der angiver den ønskede sorteringsrækkefølge; 1 for stigende rækkefølge (standard), -1 for faldende rækkefølge!en logisk værdi, der angiver den ønskede sorteringsretning: FALSK for at sortere efter række (standard), SAND for at sortere efter kolonne"}, "SORTBY": {"a": "(matrix, efter_matrix, [sorteringsrækkefølge], ...)", "d": "Sorter<PERSON> et område eller en matrix baseret på værdierne i et tilsvarende område eller en tilsvarende matrix", "ad": "området eller matrixen, der sorteres!området eller matrixen, der sorteres efter!et tal, der angiver den ønskede sorteringsrækkefølge; 1 for stigende rækkefølge (standard), -1 for faldende rækkefølge"}, "GETPIVOTDATA": {"a": "(datafelt; pivottabel; [felt]; [element]; ...)", "d": "Uddrager data, der er gemt i en pivottabel", "ad": "er navnet på det datafelt, som dataene uddrages fra!er en reference til en celle eller et celleområde i pivottabellen med de data, du vil hente!felt, der refereres til!feltelement, der refereres til"}, "IMPORTRANGE": {"a": "(re<PERSON><PERSON>_webadresse, områ<PERSON>_streng)", "d": "Importerer et celleområde fra et angivet regneark.", "ad": "webadressen til regnearket, hvorfra data importeres!område, der skal importeres"}}