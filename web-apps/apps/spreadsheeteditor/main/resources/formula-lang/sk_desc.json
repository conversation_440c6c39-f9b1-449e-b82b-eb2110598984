{"DATE": {"a": "(rok; mesiac; deň)", "d": "<PERSON><PERSON><PERSON><PERSON>, ktoré v kóde pre dátum a čas predstavuje dátum", "ad": "je číslo od 1900 alebo 1904 (v závislosti od systému dátumov používaného v zošite) do 9999!je číslo od 1 do 12 označujúce mesiac v roku!je číslo od 1 do 31 označujúce deň v mesiaci"}, "DATEDIF": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dátum; kon<PERSON><PERSON><PERSON>_dátum; j<PERSON><PERSON><PERSON>)", "d": "Vypočíta po<PERSON> dní, mesiacov alebo rokov medzi dvomi d<PERSON>ami", "ad": "<PERSON><PERSON><PERSON>, ktorý predstavuje prvý alebo počiatočný dátum daného obdobia!D<PERSON>tum, ktorý predstavuje koncový dátum príslušného obdobia!Typ informácií, ktor<PERSON> chcete vrátiť"}, "DATEVALUE": {"a": "(text_dátumu)", "d": "Konvertuje dátum v textovom formáte na číslo, ktoré v kóde pre dátum a čas predstavuje dátum", "ad": "je text predstavujúci dátum vo formáte programu Spreadsheet Editor pre dátum, ktorý musí byť v rozsahu od 1.1.1900 alebo 1.1.1904 (v závislosti od systému dátumov použitého v zošite) do 31.12.9999"}, "DAY": {"a": "(pora<PERSON><PERSON><PERSON>_číslo)", "d": "Vráti <PERSON>ň v mesiaci, číslo od 1 do 31.", "ad": "je číslo v kóde programu Spreadsheet Editor pre dátum a čas"}, "DAYS": {"a": "(end_date; start_date)", "d": "<PERSON><PERSON><PERSON><PERSON> poč<PERSON> dní medzi dvomi dá<PERSON>ami", "ad": "po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dátum a koncový_dátum sú dva dátumy, medzi ktorými chcete spočítať počet dní!počiatočný_dátum a koncový_dátum sú dva dátumy, medzi ktorými chcete spočítať počet dní"}, "DAYS360": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dátum; konco<PERSON><PERSON>_dátum; [metóda])", "d": "<PERSON><PERSON><PERSON><PERSON> počet dní medzi dvoma dátumami na základe roka s 360 dňami (12 mesiacov po 30 dní)", "ad": "Argumenty počiatočný_dátum a koncový_dátum sú dátumy ohraničujúce interval, ktorého dĺžku (v dňoch) chcete určiť!Argumenty počiatočný_dátum a koncový_dátum sú dátumy ohraničujúce interval, ktorého dĺžku (v dňoch) chcete určiť!je logická hodnota udávajúca metódu výpočtu: americká (NASD) = FALSE alebo nie je zadaná; európska = TRUE."}, "EDATE": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_d<PERSON>; mesiace)", "d": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON>, ktor<PERSON> predstavuje vyznačený počet mesiacov pred alebo po počiatočnom dátume", "ad": "je s<PERSON>rio<PERSON><PERSON>, ktoré predstavuje počiatočný dátum!je počet mesiacov pred alebo po počiatočnom dátume"}, "EOMONTH": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_d<PERSON>; mesiace)", "d": "<PERSON><PERSON><PERSON><PERSON> pora<PERSON> č<PERSON> posledného dňa mesiaca pred alebo po zadanom počte mesiacov", "ad": "je s<PERSON>rio<PERSON><PERSON>, ktoré predstavuje počiatočný dátum!je počet mesiacov pred alebo po počiatočnom dátume"}, "HOUR": {"a": "(pora<PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> od 0 (12:00 dop.) do 23 (11:00 odp.).", "ad": "je číslo v kóde programu Spreadsheet Editor pre dátum a čas alebo text vo formáte času, napríklad 16:48:00 alebo 4:48:00 odp."}, "ISOWEEKNUM": {"a": "(date)", "d": "Vráti číslo týždňa v roku pre daný dátum podľa normy ISO. idate", "ad": "je kód dátumu a času, ktorý sa v programe Spreadsheet Editor používa vo výpočtoch dátumu a času"}, "MINUTE": {"a": "(pora<PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON>, č<PERSON>lo od 0 do 59.", "ad": "je číslo v kóde programu Spreadsheet Editor pre dátum a čas alebo text vo formáte času, napríklad 16:48:00 alebo 4:48:00 odp."}, "MONTH": {"a": "(pora<PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON>, číslo od 1 (január) do 12 (december).", "ad": "je číslo v kóde programu Spreadsheet Editor pre dátum a čas"}, "NETWORKDAYS": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_d<PERSON><PERSON>; dá<PERSON>_ukončenia; [sviatky])", "d": "<PERSON><PERSON><PERSON><PERSON> počet celých pracovných dní medzi dvomi d<PERSON>", "ad": "je sériové č<PERSON> d<PERSON>, ktoré predstavuje počiatočný dátum!je sériové č<PERSON>lo dátum<PERSON>, ktoré predstavuje koncový dátum!je voliteľná množina sériových čísel dátumu, ktor<PERSON> možno vyňať z pracovného kalendára, napríklad štátne sviatky a pohyblivé dni voľna"}, "NETWORKDAYS.INTL": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dá<PERSON>; dá<PERSON>_ukončenia; [vík<PERSON>]; [sviatky])", "d": "<PERSON><PERSON><PERSON><PERSON> počet celých pracovných dní medzi dvoma dátumami s vlastnými parametrami víkendov", "ad": "je sériové č<PERSON> d<PERSON>, ktoré predstavuje počiatočný dátum!je sériové číslo dátum<PERSON>, ktoré predstavuje koncový dátum!je číslo alebo reťazec oz<PERSON>čuj<PERSON>, kedy sa vyskytnú víkendy!je voliteľná množina sériových čísel dátumov, ktoré sa majú vylúčiť z pracovného kalendára, ako sú napríklad štátne sviatky a pohyblivé sviatky"}, "NOW": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> aktuálny dátum a čas vo formáte dátumu a času.", "ad": ""}, "SECOND": {"a": "(pora<PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON>, č<PERSON>lo od 0 do 59.", "ad": "je číslo v kóde programu Spreadsheet Editor pre dátum a čas alebo text vo formáte času, napríklad 16:48:23 alebo 4:48:47 odp."}, "TIME": {"a": "(hodina; minúta; sekunda)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON>, min<PERSON><PERSON> a sekundy zadané ako čísla na poradové číslo vo formáte času", "ad": "je číslo 0 až 23, ktor<PERSON> predstavuje hodinu!je číslo 0 až 59, ktoré predstavuje minútu!je číslo 0 až 59, ktor<PERSON> predstavuje sekundu"}, "TIMEVALUE": {"a": "(text_času)", "d": "Konvertuje čas vo formáte textového reťazca na poradové číslo pre čas, číslo od 0 (12:00:00 dop.) do 0,999988426 (11:59:59 odp.). Po zadaní vzorca číslo sformátujte do formátu času", "ad": "je textový reťazec, ktorý predstavuje čas v ľubovoľnom formáte programu Spreadsheet Editor pre čas (informácie o dátume sa v reťazci ignorujú)"}, "TODAY": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>u<PERSON>lny dátum vo formáte dátumu.", "ad": ""}, "WEEKDAY": {"a": "(pora<PERSON><PERSON><PERSON>_č<PERSON>lo; [vr<PERSON><PERSON><PERSON>_číslo])", "d": "Vráti číslo 1 až 7, ktoré v dátume určuje deň v týždni.", "ad": "je číslo reprezentujúce dátum!je číslo: v prípade týždňa od nedele = 1 do soboty = 7 použite číslo 1, v prípade týždňa od pondelka = 1 do nedele = 7 použite číslo 2 a v prípade týždňa od pondelka = 0 do nedele = 6 použite číslo 3"}, "WEEKNUM": {"a": "(pora<PERSON><PERSON><PERSON>_č<PERSON>lo; [vr<PERSON><PERSON><PERSON>_typ])", "d": "Vráti číselné označenie týždňa v roku", "ad": "je kód dátumu a času, ktorý používa program Spreadsheet Editor na výpočty dátumu a času!je číslo (1 alebo 2), ktor<PERSON> určuje typ vrátenej hodnoty"}, "WORKDAY": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dátum; dni; [sviatky])", "d": "<PERSON><PERSON><PERSON><PERSON> pora<PERSON> d<PERSON> pred alebo po zadanom počte pracovných dní", "ad": "je sériov<PERSON>, ktoré predstavuje počiatočný dátum!je počet dní, ktoré nepripadajú na víkend a ani voľno pred alebo po počiatočnom_dátume!je voliteľné pole sériových čísel dátumu, ktoré možno vyňať z pracovného kalendára, napríklad štátne sviatky a pohyblivé dni voľna"}, "WORKDAY.INTL": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dátum; dni; [vík<PERSON>]; [sviatky])", "d": "<PERSON><PERSON><PERSON><PERSON> pora<PERSON> d<PERSON> pred zadaným počtom pracovných dní alebo po zadanom počte pracovných dní s vlastnými parametrami víkendov", "ad": "je sériové č<PERSON>, ktoré predstavuje počiatočný dátum!je počet dní pred počiatočným dátumom alebo po ňom, ktoré nepripadaj<PERSON> na víkendy alebo na sviatky!je číslo alebo reťazec označujúci, kedy sa vyskytnú víkendy!je voliteľné pole sériových čísel dátumov, ktoré sa majú vylúčiť z pracovného kalendára, ako sú napríklad štátne sviatky a pohyblivé sviatky"}, "YEAR": {"a": "(pora<PERSON><PERSON><PERSON>_číslo)", "d": "Vráti rok dátumu, celé číslo v rozsahu od 1900 do 9999.", "ad": "je číslo v kóde programu Spreadsheet Editor pre dátum a čas"}, "YEARFRAC": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dá<PERSON>; dá<PERSON>_ukončenia; [z<PERSON><PERSON>])", "d": "Vráti zlomok roka predstavujúci počet celých dní medzi počiatočným dátumom a dátumom ukončenia", "ad": "je sériové č<PERSON> d<PERSON>, ktoré predstavuje počiatočný dátum!je sériové č<PERSON>lo dátum<PERSON>, ktoré predstavuje koncový dátum!je typ den<PERSON><PERSON><PERSON>, ktorý chcete použ<PERSON>"}, "BESSELI": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON>ovu funkciu In(x)", "ad": "je hodnota, pri ktorej sa funkcia vyhodnotí!je poradie Besselovej funkcie"}, "BESSELJ": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON> funkciu <PERSON>n(x)", "ad": "je hodnota, pri ktorej sa funkcia hodnotí!je poradie Besselovej funkcie"}, "BESSELK": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON> Besselovu funkciu Kn(x)", "ad": "je hodnota, pri ktorej sa funkcia vyhodnotí!je poradie funkcie"}, "BESSELY": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON>(x)", "ad": "je hodnota, pri ktorej sa funkcia vyhodnotí!je poradie funkcie"}, "BIN2DEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Skonvertuje binárne číslo na desiatkové číslo", "ad": "je <PERSON><PERSON><PERSON>, k<PERSON><PERSON>"}, "BIN2HEX": {"a": "(číslo; [miesta])", "d": "Skonvertuje binárne číslo na šestnástkové číslo", "ad": "je <PERSON><PERSON><PERSON>, ktoré chcete skonvertovať!je počet <PERSON>, ktoré sa majú použiť"}, "BIN2OCT": {"a": "(číslo; [miesta])", "d": "Skonvertuje binárne číslo na osmičkové číslo", "ad": "je <PERSON><PERSON><PERSON>, ktoré chcete skonvertovať!je počet <PERSON>, ktoré sa majú použiť"}, "BITAND": {"a": "(number1; number2)", "d": "<PERSON><PERSON><PERSON><PERSON>ý operátor AND dvoch čísel", "ad": "je desiatkovým zápisom binárneho čísla, ktoré chcete vyhodnotiť!je desiatkovým zápisom binárneho čísla, ktoré chcete vyhodnotiť"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "<PERSON><PERSON><PERSON><PERSON>lo posunuté doľava o počet bitov určený argumentom shift_amount", "ad": "je desiatkovým zápisom binárneho č<PERSON>, ktoré chcete vyhodnotiť!je počet bitov, o ktoré chcete posunúť číslo doľava"}, "BITOR": {"a": "(number1; number2)", "d": "<PERSON><PERSON><PERSON><PERSON> operátor OR dvoch čísel", "ad": "je desiatkovým zápisom binárneho čísla, ktoré chcete vyhodnotiť!je desiatkovým zápisom binárneho čísla, ktoré chcete vyhodnotiť"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "<PERSON><PERSON><PERSON><PERSON>lo posunuté doprava o počet bitov určený argumentom shift_amount", "ad": "je desiatkovým zápisom binárneho č<PERSON>, ktoré chcete vyhodnotiť!je počet bitov, o ktoré chcete posunúť číslo doprava"}, "BITXOR": {"a": "(číslo1; číslo2)", "d": "<PERSON><PERSON><PERSON><PERSON> operátor Exclusive Or dvoch čísel", "ad": "je desiatkovým zápisom binárneho čísla, ktoré chcete vyhodnotiť!je desiatkovým zápisom binárneho čísla, ktoré chcete vyhodnotiť"}, "COMPLEX": {"a": "(re<PERSON><PERSON><PERSON>_č<PERSON>lo; i_číslo; [pr<PERSON><PERSON><PERSON>])", "d": "Skonvertuje reálny a imaginárny koeficient na komplexné číslo", "ad": "je reálny koeficient komplexného čísla!je imaginárny koeficient komplexného čísla!je prípona imaginárnej súčasti komplexného čísla"}, "CONVERT": {"a": "(<PERSON><PERSON><PERSON>; z_jednotky; na_jednotku)", "d": "Skonvertuje číslo z jedného systému merania na iný", "ad": "je hodnota z_jednotiek, ktorá sa má konvertovať!je jednotka pre číslo!je jednotka pre výsledok"}, "DEC2BIN": {"a": "(číslo; [miesta])", "d": "Skonvertuje desiatkové číslo na binárne číslo", "ad": "je desiatkové celé <PERSON>, ktoré chcete skonvert<PERSON>!je počet z<PERSON>, ktoré sa majú p<PERSON>"}, "DEC2HEX": {"a": "(číslo; [miesta])", "d": "Skonvertuje desiatkové číslo na šestnástkové číslo", "ad": "je desiatkové celé <PERSON>, ktoré chcete skonvert<PERSON>!je počet z<PERSON>, ktoré sa majú p<PERSON>"}, "DEC2OCT": {"a": "(číslo; [miesta])", "d": "Skonvertuje desiatkové číslo na osmičkové číslo", "ad": "je desiatkové celé <PERSON>, ktoré chcete skonvert<PERSON>!je počet z<PERSON>, ktoré sa majú p<PERSON>"}, "DELTA": {"a": "(číslo1; [číslo2])", "d": "Test<PERSON>je z<PERSON>du d<PERSON> čísel", "ad": "je prvé číslo!je druhé č<PERSON>lo"}, "ERF": {"a": "(dolný_limit; [horný_limit])", "d": "<PERSON><PERSON><PERSON><PERSON> funkciu", "ad": "je dolné pásmo integrácie ERF!je horné pásmo integrácie ERF"}, "ERF.PRECISE": {"a": "(X)", "d": "<PERSON><PERSON><PERSON><PERSON> funkciu", "ad": "je dolné pásmo integrácie ERF.PRECISE"}, "ERFC": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>by", "ad": "je dolné pásmo integrácie ERF"}, "ERFC.PRECISE": {"a": "(X)", "d": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>bovú funkciu", "ad": "je dolné pásmo integrácie ERFC.PRECISE"}, "GESTEP": {"a": "(číslo; [krok])", "d": "<PERSON><PERSON><PERSON>, či je číslo väčšie ako prahová hodnota", "ad": "je hodnota, k<PERSON>á sa testuje v krokoch!je prahová hodnota"}, "HEX2BIN": {"a": "(číslo; [miesta])", "d": "Skonvertuje šestnástkové číslo na binárne číslo", "ad": "je š<PERSON><PERSON><PERSON><PERSON><PERSON>, ktoré chcete skonvertovať!je počet z<PERSON>, ktoré sa majú použiť"}, "HEX2DEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Skonvertuje šestnástkové číslo na desiatkové číslo", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>"}, "HEX2OCT": {"a": "(číslo; [miesta])", "d": "Skonvertuje šestnástkové číslo na osmičkové číslo", "ad": "je š<PERSON><PERSON><PERSON><PERSON><PERSON>, ktoré chcete skonvertovať!je počet z<PERSON>, ktoré sa majú použiť"}, "IMABS": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ol<PERSON> hodnot<PERSON> (modul) komplexného čísla", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> absolútnu hodnotu chcete získať"}, "IMAGINARY": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> imaginárny koeficient komplexného č<PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> imaginárny koe<PERSON> chcete zís<PERSON>ť"}, "IMARGUMENT": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vráti argument q, čiže uhol vyjadrený v radiánoch", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> chcete zís<PERSON>"}, "IMCONJUGATE": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vráti komplexne združené číslo ku komplexnému <PERSON>", "ad": "je komplex<PERSON><PERSON>, ku ktoré<PERSON> chcete vypočítať komplexne združené <PERSON>"}, "IMCOS": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> komplexného <PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> kos<PERSON>"}, "IMCOSH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> hyper<PERSON>ký kosínus komplexného č<PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> hyperbolický kosínus chcete zistiť"}, "IMCOT": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> kotangens komplexného čísla", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> kotangens chcete zistiť"}, "IMCSC": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> komplexného č<PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> kosekan<PERSON> ch<PERSON>te zisti<PERSON>"}, "IMCSCH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> hyperbolický kosekans komplexného <PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> hyperbolický kosekans chcete zistiť"}, "IMDIV": {"a": "(ičíslo1; ičíslo2)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> dvoch komplexných čísel", "ad": "je komplexný čitateľ alebo delenec!je komplexný menovateľ alebo deliteľ"}, "IMEXP": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON>ti exponenciálu komplexného čísla", "ad": "je kom<PERSON><PERSON><PERSON>, ktor<PERSON><PERSON> exponenciálu chcete získať"}, "IMLN": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "V<PERSON><PERSON>ti prirodzený logaritmus komplexného čísla", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> prirodzený logaritmus chcete získať"}, "IMLOG10": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> logaritmus komplexného č<PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>ý logaritmus chcete získa<PERSON>"}, "IMLOG2": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>y logaritmus komplexného č<PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> binárny logaritmus chcete zís<PERSON>"}, "IMPOWER": {"a": "(i<PERSON><PERSON><PERSON>; číslo)", "d": "Vráti komplexné číslo umocnené na celočíselnú mocninu", "ad": "je komplexn<PERSON>, ktoré chcete umocniť!je mocnina, na ktorú chcete umocniť komplexné <PERSON>lo"}, "IMPRODUCT": {"a": "(ič<PERSON>lo1; [ič<PERSON>lo2]; ...)", "d": "Vráti súčin 1 až 255 komplexných čísel", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,... je 1 až 255 kom<PERSON><PERSON><PERSON><PERSON>, ktor<PERSON> možno vynásobi<PERSON>."}, "IMREAL": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> koeficient komplexného čísla", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>lny koeficient chcete zís<PERSON>"}, "IMSEC": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> komplexného <PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> se<PERSON> ch<PERSON>te zisti<PERSON>"}, "IMSECH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> hyper<PERSON> sekans komplexného <PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> hyperbolický sekans chcete zisti<PERSON>"}, "IMSIN": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> komplexného <PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> s<PERSON>"}, "IMSINH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> hyper<PERSON>ký sínus komplexného č<PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> hyperbolický sínus chcete zistiť"}, "IMSQRT": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> d<PERSON> odmocninu komplexného č<PERSON>", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> d<PERSON> odmocninu chcete získať"}, "IMSUB": {"a": "(ičíslo1; ičíslo2)", "d": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>l dvoch komplexných č<PERSON>el", "ad": "je komplexn<PERSON>, od ktor<PERSON>ho chcete odrátať komplexné číslo2!je komplexné <PERSON>, ktoré chcete odrátať od komplexného čísla1"}, "IMSUM": {"a": "(ič<PERSON>lo1; [ič<PERSON>lo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> komplexných čísel", "ad": "je 1 až <PERSON> kom<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> možno pridať"}, "IMTAN": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> tangens komplexného čísla", "ad": "je kom<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> tangens chcete zistiť"}, "OCT2BIN": {"a": "(číslo; [miesta])", "d": "Skonvertuje osmičkové číslo na binárne číslo", "ad": "je osmi<PERSON><PERSON><PERSON>, ktoré chcete skonvertovať!je počet z<PERSON>, ktoré sa majú použiť"}, "OCT2DEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Skonvertuje osmičkové číslo na binárne číslo", "ad": "je o<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>"}, "OCT2HEX": {"a": "(číslo; [miesta])", "d": "Skonvertuje osmičkové číslo na šestnástkové číslo", "ad": "je osmi<PERSON><PERSON><PERSON>, ktoré chcete skonvertovať!je počet z<PERSON>, ktoré sa majú použiť"}, "DAVERAGE": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> hodn<PERSON> v stĺpci zoznamu alebo v databáze, ktoré spĺňajú zadané podmienky", "ad": "je rozsah buniek tvoriacich zoznam alebo databázu. Databáza je zoznam vzájomne prepojených údajov!je označenie stĺpca v úvodzovkách alebo číslo označujúce poradie stĺpca v zozname!je rozsah buniek spĺňajúci zadané podmienky. Rozsah zahŕňa označenie stĺpca a jednu bunku pod označením pre podmienku"}, "DCOUNT": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Spočíta bunky obsahujúce čísla v poli (stĺpci) záznamov databázy, ktoré spĺňajú zadané podmienky", "ad": "je rozsah buniek tvoriacich zoznam alebo databázu. Databáza je zoznam vzájomne prepojených údajov!je označenie stĺpca v úvodzovkách alebo číslo označujúce poradie stĺpca v zozname!je rozsah buniek spĺňajúci zadané podmienky. Rozsah zahŕňa označenie stĺpca a jednu bunku pod označením pre podmienku"}, "DCOUNTA": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> bunk<PERSON>, ktor<PERSON> nie sú prázdne, v poli (stĺpci) záznamov databázy, ktoré spĺňajú zadané podmienky", "ad": "je rozsah buniek tvoriacich zoznam alebo databázu. Databáza je zoznam vzájomne prepojených údajov!je označenie stĺpca v úvodzovkách alebo číslo označujúce poradie stĺpca v zozname!je rozsah buniek spĺňajúci zadané podmienky. Rozsah zahŕňa označenie stĺpca a jednu bunku pod označením pre podmienku"}, "DGET": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Vyberie z databázy jeden <PERSON>, ktor<PERSON> spĺňa zadané podmienky", "ad": "je rozsah buniek tvoriacich zoznam alebo databázu. Databáza je zoznam vzájomne prepojených údajov!je označenie stĺpca v úvodzovkách alebo číslo označujúce poradie stĺpca v zozname!je rozsah buniek spĺňajúci zadané podmienky. Rozsah zahŕňa označenie stĺpca a jednu bunku pod označením pre podmienku"}, "DMAX": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Vráti maximálnu hodnotu v poli (stĺpci) záznamov databázy, ktorá spĺňa zadané podmienky", "ad": "je rozsah buniek tvoriacich zoznam alebo databázu. Databáza je zoznam vzájomne prepojených údajov!je označenie stĺpca v úvodzovkách alebo číslo označujúce poradie stĺpca v zozname!je rozsah buniek spĺňajúci zadané podmienky. Rozsah zahŕňa označenie stĺpca a jednu bunku pod označením pre podmienku"}, "DMIN": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "<PERSON><PERSON><PERSON>ti minimálnu hodnotu v poli (stĺpci) záznamov databázy, ktorá spĺňa zadané podmienky", "ad": " je rozsah buniek tvoriacich zoznam alebo databázu. Databáza je zoznam vzájomne prepojených údajov!je označenie stĺpca v úvodzovkách alebo číslo označujúce poradie stĺpca v zozname!je rozsah buniek spĺňajúci zadané podmienky. Rozsah zahŕňa označenie stĺpca a jednu bunku pod označením pre podmienku"}, "DPRODUCT": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Vynásobí hodnoty v poli (stĺpci) záznamov databázy, ktoré spĺňajú zadané podmienky", "ad": "je rozsah buniek tvoriacich zoznam alebo databázu. Databáza je zoznam vzájomne prepojených údajov!je označenie stĺpca v úvodzovkách alebo číslo označujúce poradie stĺpca v zozname!je rozsah buniek spĺňajúci zadané podmienky. Rozsah zahŕňa označenie stĺpca a jednu bunku pod označením pre podmienku"}, "DSTDEV": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Odhadne smerodajnú odchýlku podľa vzorky vybratých položiek databázy", "ad": "je rozsah buniek tvoriacich zoznam alebo databázu. Databáza je zoznam vzájomne prepojených údajov!je označenie stĺpca v úvodzovkách alebo číslo označujúce poradie stĺpca v zozname!je rozsah buniek spĺňajúci zadané podmienky. Rozsah zahŕňa označenie stĺpca a jednu bunku pod označením pre podmienku"}, "DSTDEVP": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Vypočíta smerodajnú odchýlku podľa celého súboru vybratých položiek databázy", "ad": "je rozsah buniek tvoriacich zoznam alebo databázu. Databáza je zoznam vzájomne prepojených údajov!je označenie stĺpca v úvodzovkách alebo číslo označujúce poradie stĺpca v zozname!je rozsah buniek spĺňajúci zadané podmienky. Rozsah zahŕňa označenie stĺpca a jednu bunku pod označením pre podmienku"}, "DSUM": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Spočíta čísla v poli (stĺpci) záznamov databázy, ktoré spĺňajú zadané podmienky", "ad": "je rozsah buniek tvoriacich zoznam alebo databázu. Databáza je zoznam vzájomne prepojených údajov!je označenie stĺpca v úvodzovkách alebo číslo označujúce poradie stĺpca v zozname!je rozsah buniek spĺňajúci zadané podmienky. Rozsah zahŕňa označenie stĺpca a jednu bunku pod označením pre podmienku"}, "DVAR": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Odhadne rozptyl vzorky vybratých položiek databázy", "ad": "je rozsah buniek tvoriacich zoznam alebo databázu. Databáza je zoznam vzájomne prepojených údajov!je označenie stĺpca v úvodzovkách alebo číslo označujúce poradie stĺpca v zozname!je rozsah buniek spĺňajúci zadané podmienky. Rozsah zahŕňa označenie stĺpca a jednu bunku pod označením pre podmienku"}, "DVARP": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Vypočíta rozptyl podľa celého súboru vybratých položiek databázy", "ad": "je rozsah buniek tvoriacich zoznam alebo databázu. Databáza je zoznam vzájomne prepojených údajov!je označenie stĺpca v úvodzovkách alebo číslo označujúce poradie stĺpca v zozname!je rozsah buniek spĺňajúci zadané podmienky. Rozsah zahŕňa označenie stĺpca a jednu bunku pod označením pre podmienku"}, "CHAR": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vráti znak určený číslom kódu z tabuľky znakov používanej v danom počítači", "ad": "je číslo v intervale 1 až 255 určujúce požadovaný znak"}, "CLEAN": {"a": "(text)", "d": "Odstráni z textu všetky znaky, ktoré nemožno vytlačiť", "ad": "je ľubovoľná informácia z hárka, z ktorej chcete odstrániť všetky znaky, ktor<PERSON> ne<PERSON> vytlačiť"}, "CODE": {"a": "(text)", "d": "<PERSON><PERSON><PERSON><PERSON>elný kód prvého znaku textového reťazca z tabuľky znakov na danom počítači", "ad": "je text<PERSON><PERSON>, pre ktorý chcete nájsť kód prvého znaku"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Zlúči niekoľko textových reťazcov do jedného", "ad": "je 1 až 255 text<PERSON><PERSON><PERSON> reťazcov, k<PERSON><PERSON> ch<PERSON>te zlúčiť do jedného reťazca. M<PERSON><PERSON>u to byť textové reťazce, <PERSON><PERSON>la alebo odkazy na jednu bunku"}, "CONCAT": {"a": "(text1; ...)", "d": "Zreťazí zoznam alebo rozsah textových reťazcov", "ad": "predstavuje od 1 do 254 textových reťazcov alebo roz<PERSON>, ktor<PERSON> sa majú zlúčiť do jedného textového reťazca"}, "DOLLAR": {"a": "(č<PERSON>lo; [desatinné_miesta])", "d": "Konvertuje číslo na text vo formáte meny", "ad": "je <PERSON><PERSON><PERSON>, od<PERSON><PERSON> na bunku s číslom alebo vzorec, ktor<PERSON>ho výsledkom je číslo!je počet desatinných miest. <PERSON><PERSON>lo je podľa potreby zaokrúhlené. Ak argument desatinné_miesta nezadáte, bude jeho hodnota 2"}, "EXACT": {"a": "(text1; text2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či sú dva textové reťazce identické, a vr<PERSON>ti hodnotu TRUE alebo FALSE. Funkcia EXACT rozlišuje malé a veľké písmená", "ad": "je prvý textový reťazec!je druhý textový reťazec"}, "FIND": {"a": "(nájsť_text; v_texte; [po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_číslo])", "d": "Vráti počiatočnú pozíciu textového reťazca v rámci iného textového reťazca. Táto funkcia rozlišuje malé a veľké písmená", "ad": "je text, ktorý hľadáte. Použite úvodzovky (bez textu) na hľadanie prvého zodpovedajúceho znaku argumentu V_texte. Zástupné znaky nie sú povolené!je text obsahujúci hľadaný text!ur<PERSON><PERSON><PERSON> znak, od ktorého začne hľadanie. Prvý znak argumentu V_texte je číslo znaku 1. Ak argument Počiatočné_číslo nezadáte, bude jeho hodnota 1"}, "FINDB": {"a": "(nájsť_text; v_texte; [po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_číslo])", "d": "<PERSON><PERSON><PERSON><PERSON> jeden textový reťazec vo vnútri druhého reťazca a vracajú počiatočnú pozíciu prvého reťazca od prvého znaku druhého reťazca, je určená pre jazyky, k<PERSON><PERSON> dvojbajtov<PERSON> tabu<PERSON> (DBCS) - japon<PERSON><PERSON>, čínština a kórejčina", "ad": "je text, ktorý hľadáte. Použite úvodzovky (bez textu) na hľadanie prvého zodpovedajúceho znaku argumentu V_texte. Zástupné znaky nie sú povolené!je text obsahujúci hľadaný text!ur<PERSON><PERSON><PERSON> znak, od ktorého začne hľadanie. Prvý znak argumentu V_texte je číslo znaku 1. Ak argument Počiatočné_číslo nezadáte, bude jeho hodnota 1"}, "FIXED": {"a": "(č<PERSON>lo; [desatinné_miesta]; [bez_č<PERSON><PERSON>])", "d": "Zaokrúhli číslo na daný počet desatinných miest a vráti výsledok vo forme textu s alebo bez čiarok", "ad": "je <PERSON><PERSON><PERSON>, k<PERSON><PERSON> chcete zaokrúhliť a zobraziť vo forme textu!je počet desatinných miest. Ak argument desatinné_miesta nezadáte, bude jeho hodnota 2!je logická hodnota: nezobrazovať čiarky vo vrátenom texte = TRUE; zobrazovať čiarky vo vrátenom texte = FALSE alebo nie je zadaná"}, "LEFT": {"a": "(text; [po<PERSON><PERSON>_z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> zadaný počet znakov od začiatku textového reťazca", "ad": "je textový reťazec obsahuj<PERSON><PERSON> z<PERSON>, k<PERSON><PERSON> chcete extrahovať!<PERSON><PERSON><PERSON><PERSON><PERSON>, ko<PERSON><PERSON> znakov má funkcia LEFT extrahovať. Ak tento argument nezad<PERSON>, bude jeho hodnota 1"}, "LEFTB": {"a": "(text; [po<PERSON><PERSON>_z<PERSON><PERSON>])", "d": "Vráti prvý znak alebo prvé znaky v textovom reťazci, pričom ich počet je určený veľkosťou v bajtoch, je určená pre jazyky, ktor<PERSON> pou<PERSON>jú dvojbajtovú tabuľku z<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON>, čínš<PERSON> a kórejčina", "ad": "je textový reťazec obsahuj<PERSON><PERSON> z<PERSON>, k<PERSON><PERSON> chcete extrahovať!<PERSON><PERSON><PERSON><PERSON><PERSON>, ko<PERSON><PERSON> znakov má funkcia LEFTB extrahovať. Ak tento argument nezad<PERSON>, bude jeho hodnota 1"}, "LEN": {"a": "(text)", "d": "<PERSON><PERSON><PERSON><PERSON> počet znakov textového reťazca", "ad": "je text, ktorého dĺžku chcete zistiť. Medzery sú považované za znaky"}, "LENB": {"a": "(text)", "d": "Vráti počet bajtov použitých pre zobrazenie znakov v textovom reťazci, je určená pre jazyky, ktoré používajú dvojbajtovú tabuľku znakov (DBCS) - japončina, čínština a kórejčina", "ad": "je text, ktorého dĺžku chcete zistiť. Medzery sú považované za znaky"}, "LOWER": {"a": "(text)", "d": "Konvertuje všetky písmená v textovom reťazci na malé", "ad": "je text, ktor<PERSON> chcete konvertovať na malé písmená. Znaky v argumente Text, ktoré nie sú písmená, sa nemenia"}, "MID": {"a": "(text; po<PERSON><PERSON><PERSON><PERSON><PERSON>_pozícia; po<PERSON><PERSON>_<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> z textového reťazca, ak je zadaná počiatočná pozícia a dĺžka", "ad": "je textový reťazec, z ktorého chcete znaky extrahovať!je pozícia prvého znaku, ktorý chcete extrahovať. Prvý znak v texte je 1!ur<PERSON><PERSON> počet znakov textu, ktoré sa majú vrátiť"}, "MIDB": {"a": "(text; po<PERSON><PERSON><PERSON><PERSON><PERSON>_pozícia; po<PERSON><PERSON>_<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON>ti č<PERSON>ť textového reťazca od zadanej pozície a podľa zadaného poč<PERSON> baj<PERSON>, je určená pre jazyky, ktor<PERSON> pou<PERSON>j<PERSON> dvojbajtovú tabuľ<PERSON> z<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON>, <PERSON>ínš<PERSON> a kórejčina", "ad": "je textový reťazec, z ktorého chcete znaky extrahovať!je pozícia prvého znaku, ktorý chcete extrahovať. Prvý znak v texte je 1!ur<PERSON><PERSON> počet znakov textu, ktoré sa majú vrátiť"}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "Konvertuje text na číslo spôsobom, k<PERSON><PERSON> nie je závislý od miestnych nastavení", "ad": "je reťazec predstavujúci číslo, ktor<PERSON> chcete skonvertovať!je znak, ktorý sa v reťazci používa ako oddeľovač desatinných miest!je znak, ktorý sa v reťazci používa ako oddeľovač skupín"}, "PROPER": {"a": "(text)", "d": "Písmená v textovom reťazci konvertuje do riadneho formátu, prvé písmeno každého slova na veľké a všetky ostatné písmená na malé", "ad": "je text v úvodzovkách, vzorec, ktorého výsledkom je text, alebo odkaz na bunku obsahujúcu text, v ktorom chcete zmeniť prvé písmená slov na veľké"}, "REPLACE": {"a": "(starý_text; počiatočné_číslo; poč<PERSON>_znakov; nový_text)", "d": "Nahradí časť textového reťazce iným textovým reťazcom", "ad": "je text, v ktorom chcete nahradiť niektoré znaky!je pozícia znaku v texte označenom argumentom Starý_text, ktorý chcete zameniť za text označený argumentom Nový_text!je počet znakov textu argumentu Starý_text, ktoré chcete nahradiť!je text, ktorý nahradí znaky v texte označenom argumentom Starý_text"}, "REPLACEB": {"a": "(starý_text; počiatočné_číslo; poč<PERSON>_znakov; nový_text)", "d": "Nahradí časť textového reťazca so zadaným počtom bajtov odlišným textovým reťazcom, je určená pre jazyky, ktor<PERSON> pou<PERSON> dvojbajtovú tabuľ<PERSON> z<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON>, <PERSON>ínš<PERSON> a kórejčina", "ad": "je text, v ktorom chcete nahradiť niektoré znaky!je pozícia znaku v texte označenom argumentom Starý_text, ktorý chcete zameniť za text označený argumentom Nový_text!je počet znakov textu argumentu Starý_text, ktoré chcete nahradiť!je text, ktorý nahradí znaky v texte označenom argumentom Starý_text"}, "REPT": {"a": "(text; počet_opakovaní)", "d": "Text sa zopakuje podľa zadaného počtu opakovaní. Ak chcete bunku vyplniť určitým počtom opakovaní textového reťazca, použite funkciu REPT", "ad": "je text, ktorý chcete opakovať!je kladné <PERSON>, ktoré určuje počet opakovaní textu"}, "RIGHT": {"a": "(text; [po<PERSON><PERSON>_z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>ý počet znakov od konca textového reťazca", "ad": "je textový reťazec obsahuj<PERSON> z<PERSON>ky, ktoré chcete extrahovať!určuje počet znakov, ktoré chcete extrahovať. Ak tento argument nezad<PERSON>, bude jeho hodnota 1"}, "RIGHTB": {"a": "(text; [po<PERSON><PERSON>_z<PERSON><PERSON>])", "d": "Vráti znaky z konca textového reťazca, pričom dĺžka výsledku je zadaná v bajtoch, je určená pre jazyky, ktor<PERSON> použ<PERSON>jú dvojbajtovú tabuľku znakov (DBCS) - <PERSON><PERSON><PERSON><PERSON>, čínština a kórejčina", "ad": "je textový reťazec obsahuj<PERSON> z<PERSON>ky, ktoré chcete extrahovať!určuje počet znakov, ktoré chcete extrahovať. Ak tento argument nezad<PERSON>, bude jeho hodnota 1"}, "SEARCH": {"a": "(nájsť_text; v_texte; [pozícia_začiatku])", "d": "<PERSON><PERSON><PERSON><PERSON>lo prvého výskytu daného znaku alebo textového reťazca. Program hľadá zľava doprava a nerozlišuje veľké a malé písmená", "ad": "je text, ktorý chcete nájsť. Môžete použiť zástupné znaky ? a *. Nájdete ich pomocou reťazcov ~? a ~*!je text, v ktorom chcete vyhľadať znak alebo textový reťazec!Je číslo znaku (zľava) v argumente V_texte, pri ktorom chcete začať hľadať. Ak tento argument nezadáte, bude jeho hodnota 1"}, "SEARCHB": {"a": "(nájsť_text; v_texte; [pozícia_začiatku])", "d": "Vyhľadávajú jeden textový reťazec v rámci druhého textového reťazca a vrátia číslo počiatočnej pozície prvého textového reťazca od prvého znaku druhého textového reťazca, je určená pre jazyky, k<PERSON><PERSON> použ<PERSON>jú dvojbajtov<PERSON> tabuľku znakov (DBCS) - japončina, čínština a kórejčina", "ad": "je text, ktorý chcete nájsť. Môžete použiť zástupné znaky ? a *. Nájdete ich pomocou reťazcov ~? a ~*!je text, v ktorom chcete vyhľadať znak alebo textový reťazec!Je číslo znaku (zľava) v argumente V_texte, pri ktorom chcete začať hľadať. Ak tento argument nezadáte, bude jeho hodnota 1"}, "SUBSTITUTE": {"a": "(text; starý_text; nový_text; [č<PERSON>lo_inštancie])", "d": "Nahradí existujúci text v textovom reťazci novým textom", "ad": "je text alebo odkaz na bunku obsahujúcu text, v ktorom chcete zameniť znaky!je existujúci text, ktorý chcete nahradiť. Ak malé a veľké písmená v texte argumentu Starý_text sa nezhodujú s malými a veľkými písmenami v texte argumentu Text, funkcia SUBSTITUTE text nenahradí!je text, ktorý má nahradiť text argumentu Starý_text!ur<PERSON>uje, ktorý výskyt textu argumentu Starý_text chcete nahradiť. Ak tento argument nezadáte, bude každý výskyt textu argumentu Starý_text nahradený"}, "T": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je argument textovou hodnotou. <PERSON><PERSON>, vr<PERSON><PERSON> text, v opačnom prípade vr<PERSON><PERSON> (bez textu)", "ad": "je hodn<PERSON>, k<PERSON><PERSON> o<PERSON>"}, "TEXT": {"a": "(hodnota; formát_text)", "d": "Konvertuje hodnotu na text v špeciálnom číselnom formáte", "ad": "je <PERSON><PERSON><PERSON>, v<PERSON><PERSON>, k<PERSON><PERSON><PERSON> výsledkom je číselná hodnota, alebo odkaz na bunku obsahujúcu číselnú hodnotu!je číselný formát vo forme textu vybratý zo zoznamu Kategória na karte Číslo v dialógovom okne Formát buniek"}, "TEXTJOIN": {"a": "(odd<PERSON><PERSON><PERSON><PERSON>; ignorovať_prázdne; text1; ...)", "d": "Zreťazí zoznam alebo rozsah textových reťazcov použitím oddeľovačov", "ad": "Znak alebo reťazec, ktorý sa vkladá medzi jednotlivé textové položky!v prípade hodnoty TRUE (predvolené) ignoruje prázdne bunky!pridať sa majú textové reťazce alebo rozsahy s dĺžkou od 1 do 252 znakov"}, "TRIM": {"a": "(text)", "d": "Odstráni všetky medzery z textového reťazca okrem jednotlivých medzier medzi slovami", "ad": "je text, z ktorého chcete odstrániť medzery"}, "UNICHAR": {"a": "(number)", "d": "Vráti znak Unicode, na ktorý odkazuje daná číselná hodnota", "ad": "je <PERSON><PERSON>lo kódu Unicode zastupujúce znak"}, "UNICODE": {"a": "(text)", "d": "<PERSON><PERSON><PERSON><PERSON> (bod kódu) zodpovedajúce prvému znaku textu", "ad": "je znak, k<PERSON><PERSON><PERSON> hodnotu Unicode chcete zistiť"}, "UPPER": {"a": "(text)", "d": "Konvertuje všetky písmená v textovom reťazci na veľké", "ad": "je text, ktorý chcete konvertovať na veľké písmená. <PERSON><PERSON>ž<PERSON> to byť odkaz alebo textový reťazec"}, "VALUE": {"a": "(text)", "d": "Konvertuje textový reťazec predstavujúci číslo na číslo", "ad": "je text v úvodzovkách alebo odkaz na bunku obsahujúcu text, k<PERSON><PERSON> chcete konvertovať"}, "AVEDEV": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON>ráti priemernú hodnotu absolútnych odchýlok údajových bodov od ich priemeru. Argumenty môžu byť čísla alebo názvy, polia alebo odkazy obsahujúce čísla", "ad": "je 1 a<PERSON> <PERSON>, pre k<PERSON><PERSON> chcete zistiť priemernú hodnotu absolútnych odchýlok"}, "AVERAGE": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> prie<PERSON> hodnot<PERSON> (aritmetický priemer), pri<PERSON><PERSON> to mô<PERSON><PERSON> byť čísla alebo názvy, polia alebo odkazy obsahuj<PERSON><PERSON> čísla", "ad": "je 1 až <PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> priemernú hodnotu chcete zistiť"}, "AVERAGEA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> prie<PERSON> hodnotu (aritmet<PERSON><PERSON> priemer) argumentov. Text a logická hodnota FALSE = 0; TRUE = 1. Argumenty m<PERSON> by<PERSON> č<PERSON>, n<PERSON><PERSON><PERSON>, polia alebo odkazy", "ad": "je 1 a<PERSON> <PERSON>, k<PERSON><PERSON><PERSON> priemer chcete zistiť"}, "AVERAGEIF": {"a": "(rozsah; krit<PERSON><PERSON><PERSON>; [prie<PERSON><PERSON><PERSON>_rozsah])", "d": "Vyhľ<PERSON><PERSON> priemer (aritmetick<PERSON> priemer) b<PERSON><PERSON> stano<PERSON>ch podľa zadanej podmienky alebo kritéria", "ad": "je roz<PERSON><PERSON> b<PERSON>, ktor<PERSON> chcete hodnotiť!je podmienka alebo kritérium vo forme čísla, výrazu alebo textu definujú<PERSON><PERSON> bunky, ktoré sa použijú na vyhľadanie priemeru!sú skutočné bunky, ktoré sa použijú na vyhľadanie priemeru. Ak sa táto hodnota vynechá, použijú sa bunky v rozsahu"}, "AVERAGEIFS": {"a": "(prie<PERSON><PERSON><PERSON>_rozsah; rozsah_kritérií; kritéri<PERSON>; ...)", "d": "Vyhľ<PERSON><PERSON> priemer (aritmetick<PERSON> priemer) b<PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON> zadanou množinou podmienok alebo kritérií", "ad": "sú skutočné bunky, ktoré sa použijú na vyhľadanie priemeru.!je roz<PERSON>h buni<PERSON>, ktoré chcete zhodnotiť podľa konkrétnej podmienky!je podmienka alebo kritérium vo forme čísla, výrazu alebo textu definuj<PERSON><PERSON>ho bunky, ktoré sa použijú na vyhľadanie priemeru"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie rozdelenia pravdepodobnosti beta", "ad": "je hodnota medzi hodnotami argumentov A a B, pre ktorú chcete zistiť hodnotu funkcie!je parameter rozdelenia, ktorý musí byť väčší ako 0!je parameter rozdelenia, ktorý musí byť väčší ako 0!je voliteľná dolná hranica pre hodnoty x. Ak argument A nezadáte, jeho hodnota bude 0!je voliteľná horná hranica pre hodnoty x. Ak argument B nezadáte, jeho hodnota bude 1"}, "BETAINV": {"a": "(pravdepodobnosť; alfa; beta; [A]; [B])", "d": "Vráti inverznú hodnotu súčtovej hustoty rozdelenia pravdepodobnosti beta (inverzná funkcia k funkcii BETADIST)", "ad": "je pravdepodobnosť rozdelenia beta!je parameter rozdelenia, ktorý musí byť väčší ako 0!je parameter rozdelenia, ktorý musí byť väčší ako 0!je voliteľná dolná hranica pre hodnoty x. Ak argument A nezadáte, jeho hodnota bude 0!je voliteľná horná hranica pre hodnoty x. Ak argument B nezadáte, jeho hodnota bude 1"}, "BETA.DIST": {"a": "(x; alfa; beta; kumulatívne; [A]; [B])", "d": "<PERSON><PERSON><PERSON><PERSON> rozdelenia pravdepodobnosti beta", "ad": "je hodnota medzi hodnotami A a B, pri ktorej sa má funkcia vyhodnocovať!je parameter rozdelenia a musí byť väčší ako 0!je parameter rozdelenia a musí byť väčší ako 0!je logická hodnota: ak ide o funkciu kumulatívneho rozdelenia, použite hodnotu TRUE a ak ide o funkciu hustoty pravdepodobnosti, použite hodnotu FALSE!je voliteľná dolná hranica intervalu x. Ak sa vynechá, A = 0!je voliteľná horná hranica intervalu x. Ak sa vynechá, B = 1"}, "BETA.INV": {"a": "(pravdepodobnosť; alfa; beta; [A]; [B])", "d": "<PERSON><PERSON>áti inverznú hodnotu funkcie kumulatívnej hustoty pravdepodobnosti beta (BETA.DIST)", "ad": "je pravdepodobnosť priradená rozdeleniu beta!je parameter rozdelenia a musí byť väčší ako 0!je parameter rozdelenia a musí byť väčší ako 0!je voliteľná dolná hranica intervalu x. Ak sa vynechá, A = 0!je voliteľná horná hranica intervalu x. Ak sa vynechá, B = 1"}, "BINOMDIST": {"a": "(počet_s; pokusy; pravdepodobnosť_s; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu binomického rozdelenia pravdepodobnosti jednotlivých veličín", "ad": "je počet úspešných pokusov!je počet nezávislých pokusov!je pravdepodobnosť úspešného pokusu!je logická hodnota: distribučná funkcia = TRUE; funkcia pravdepodobnosti = FALSE"}, "BINOM.DIST": {"a": "(číslo_s; pokusy; pravdepodobnosť_s; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu binomického rozdelenia pravdepodobnosti jednotlivých veličín", "ad": "je počet úspešných pokusov!je počet nezávislých pokusov!je pravdepodobnosť úspešného pokusu!je logická hodnota: distribučná funkcia = TRUE; funkcia pravdepodobnosti = FALSE"}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "<PERSON><PERSON><PERSON><PERSON> pravdepodobnosť skúšobného výsledku pomocou binomického rozdelenia", "ad": "je počet nezávislých pokusov!je pravdepodobnosť úspešnosti jednotlivých pokusov!je počet úspešných pokusov!ak je zadaný, vr<PERSON><PERSON> pravdepodobnosť, že počet úspešných pokusov sa bude nachádzať medzi hodnotou argumentov number_s a number_s2"}, "BINOM.INV": {"a": "(pokusy; pravdepodobnosť_s; alfa)", "d": "<PERSON><PERSON><PERSON><PERSON> na<PERSON><PERSON> hodnot<PERSON>, pre ktorú má distribučná funkcia binomického rozdelenia hodnotu väčšiu alebo rovnajúcu sa hodnote kritéria", "ad": "je počet Bernoulliho pokusov!je pravdepodobnosť úspešného pokusu. Argument je číslo od 0 do 1 vrátane oboch hodnôt!je hodnota kritéria. Je to číslo od 0 do 1 vrátane oboch hodnôt"}, "CHIDIST": {"a": "(x; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> sprava ohraničenú pravdepodobnosť pre rozdelenie chí-kvadrát", "ad": "je ho<PERSON><PERSON>, pre ktor<PERSON> chcete zistiť pravdepodobnosť. Musí to byť nezáporné číslo!je počet stupňov voľnosti, ktor<PERSON>m môže byť číslo medzi 1 a 10^10 okrem čísla 10^10"}, "CHIINV": {"a": "(pravdepodobnosť; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu funkcie inverznej k sprava ohraničenej pravdepodobnosti rozdelenia chí-kvadrát", "ad": "je pravdepodobnosť rozdelenia chí-kvadrát. Argument je hodnota medzi 0 a 1 vrátane oboch hodnôt!je počet stupňov voľnosti, k<PERSON><PERSON><PERSON> môže byť číslo medzi 1 a 10^10 okrem čísla 10^10"}, "CHITEST": {"a": "(skuto<PERSON><PERSON><PERSON>_rozsah; očaká<PERSON>ý_rozsah)", "d": "Počíta test nezávislosti: hodnotu rozdelenia chí-kvadrát pre štatistiku a príslušný počet stupňov voľnosti", "ad": "je rozsah údajov obsahuj<PERSON><PERSON>ch pozorovania, k<PERSON><PERSON> chcete testovať a porovnávať s predpokladanými hodnotami!je rozsah údajov obsahujúcich podiel súčinu počtu riadkov a stĺpcov a celkového počtu"}, "CHISQ.DIST": {"a": "(x; stupeň_voľnosti; kumulatívne)", "d": "Vráti zľava ohraničenú pravdepodobnosť rozdelenia chí-kvadrát", "ad": "je hodnota, pri ktorej chcete hodnotiť rozdelenie, a ide o nezáporné číslo!je počet stup<PERSON> voľnosti, č<PERSON>lo od 1 do 10^10 a menšie než 10^10!je logická hodnota pre funkciu, ktor<PERSON> sa má vrátiť: ak ide o funkciu kumulatívneho rozdelenia, t<PERSON><PERSON> hodnota = TRUE a ak ide o funkciu hustoty pravdepodobnosti, t<PERSON><PERSON> hodnota = FALSE"}, "CHISQ.DIST.RT": {"a": "(x; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> sprava ohraničenú pravdepodobnosť rozdelenia chí-kvadrát", "ad": "je hodn<PERSON>, pri ktorej chcete hodnotiť rozdelenie, a ide o nezáporné číslo!je počet stu<PERSON> voľnosti, číslo od 1 do 10^10 a menšie než 10^10"}, "CHISQ.INV": {"a": "(pravdepodobnosť; stupeň_voľnosti)", "d": "Vráti inverznú hodnotu zľava ohraničenej pravdepodobnosti rozdelenia chí-kvadrát", "ad": "je pravdepodobnosť priradená k rozdeleniu chí-k<PERSON><PERSON><PERSON>, čo je hodnota od 0 do 1 vrátane!je počet stu<PERSON>ov voľnosti, číslo od 1 do 10^10 a menšie než 10^10"}, "CHISQ.INV.RT": {"a": "(pravdepodobnosť; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON>ti inverznú hodnotu sprava ohraničenej pravdepodobnosti rozdelenia chí-kvadrát", "ad": "je pravdepodobnosť priradená k rozdeleniu chí-k<PERSON><PERSON><PERSON>, čo je hodnota od 0 do 1 vrátane!je počet stu<PERSON>ov voľnosti, číslo od 1 do 10^10 a menšie než 10^10"}, "CHISQ.TEST": {"a": "(skuto<PERSON><PERSON><PERSON>_rozsah; očaká<PERSON>ý_rozsah)", "d": "Počíta test nezávislosti: hodnotu rozdelenia chí-kvadrát pre štatistiku a príslušný počet stupňov voľnosti", "ad": "je rozsah údajov obsahuj<PERSON><PERSON>ch pozorovania, k<PERSON><PERSON> chcete testovať a porovnávať s predpokladanými hodnotami!je rozsah údajov obsahujúcich podiel súčinu počtu riadkov a stĺpcov a celkového počtu"}, "CONFIDENCE": {"a": "(alfa; smerodajná_odch; veľkosť)", "d": "Vráti interval spoľahlivosti pre strednú hodnotu základného súboru s použitím normálneho rozloženia", "ad": "je hladina významnosti, pomocou ktorej sa vypočíta hladina spoľahlivosti. Argument je číslo väčšie ako 0 a menšie ako 1!je známa smerodajná odchýlka základného súboru pre rozsah údajov. Hodnota argumentu Smerodajná_odch musí byť väčšia ako 0!je veľkosť vzorky"}, "CONFIDENCE.NORM": {"a": "(alfa; smerodajná_odch; veľkosť)", "d": "Vráti interval spoľahlivosti pre strednú hodnotu súboru s použitím normálneho rozloženia", "ad": "je hladina významnosti použitá na výpočet úrovne spoľahlivosti, číslo väčšie ako 0 a menšie ako 1!je štandardná odchýlka súboru pre rozsah údajov a predpokladá sa, že je z<PERSON>. Hodnota Smerodajná_odch musí byť väčšia než 0!je veľkosť vzorky"}, "CONFIDENCE.T": {"a": "(alfa; smerodajná_odch; veľkosť)", "d": "Vráti interval spoľahlivosti pre strednú hodnotu súboru s použitím Studentovho T-rozdelenia", "ad": "je hladina významnosti použitá na výpočet úrovne spoľahlivosti, číslo väčšie ako 0 a menšie ako 1!je štandardná odchýlka súboru pre rozsah údajov a predpokladá sa, že je z<PERSON>. Hodnota Smerodajná_odch musí byť väčšia než 0!je veľkosť vzorky"}, "CORREL": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> koeficient pre dva súbory údajov", "ad": "je rozsah buniek s hodnotami. Hodnoty môžu byť čísla, n<PERSON><PERSON><PERSON>, polia alebo odkazy obsahujúce čísla!je druhý rozsah buniek s hodnotami. Hodnoty môžu byť čísla, n<PERSON><PERSON><PERSON>, polia alebo odkazy obsahujúce čísla"}, "COUNT": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Spočíta počet buniek v rozsahu, ktorý obsahuje čísla", "ad": "je 1 a<PERSON> <PERSON>, k<PERSON><PERSON> môžu obsahovať alebo odkazovať na rôzne typy údajov, počítajú sa však iba čísla"}, "COUNTA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Spočíta bunky v rozsahu, ktoré nie sú prázdne", "ad": "je 1 až <PERSON> argumentov predstavujúcich hodnoty a bunky, ktor<PERSON> chcete spočítať. Hodnoty môžu predstavovať ľubovoľný typ informácií"}, "COUNTBLANK": {"a": "(rozsah)", "d": "Vráti počet prázdnych buniek v danom rozsahu", "ad": "je roz<PERSON><PERSON> b<PERSON>, v ktorom chcete spočítať prázdne bunky"}, "COUNTIF": {"a": "(rozsah; kritériá)", "d": "Spočíta bunky v danom rozsahu, ktoré spĺňajú zadanú podmienku", "ad": "je r<PERSON><PERSON><PERSON> b<PERSON>, v ktorom chcete spočítať bunky, ktoré nie sú prázdne!je podmienka vo forme čísla, v<PERSON>razu alebo textu, ktor<PERSON> presne definuje bunky, ktor<PERSON> sa spočítajú"}, "COUNTIFS": {"a": "(rozsah_kritérií; kritériá; ...)", "d": "Spočíta počet buniek podľa zadanej množiny podmienok alebo kritérií", "ad": "je r<PERSON><PERSON><PERSON>, ktor<PERSON> chcete hodnotiť podľa konkrétnych podmienok!je podmienka vo forme čísla, výrazu alebo textu definuj<PERSON><PERSON><PERSON> bunky, ktor<PERSON> sa budú poč<PERSON>ta<PERSON>"}, "COVAR": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu k<PERSON>, priemernú hodnotu súčinu odchýlok pre každú dvojicu údajových bodov v dvoch množinách údajov", "ad": "je prvý rozsah buniek obsahujúcich celé čísla. Hodnoty argumentu musia byť čísla, polia alebo odkazy obsahujúce čísla!je druhý rozsah buniek obsahujúcich celé čísla. Hodnoty argumentu musia byť čísla, polia alebo odkazy obsahujúce čísla"}, "COVARIANCE.P": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON>, prie<PERSON> sú<PERSON>inov odchýlok pre každý pár údajových bodov v dvoch množinách údajov", "ad": "je prvý rozsah buniek celých čísel a musia to byť čísla, polia alebo odkazy obsahujúce čísla!je druhý rozsah buniek celých čísel a musia to byť čísla, polia alebo odkazy obsahujúce čísla"}, "COVARIANCE.S": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>, prie<PERSON> súčinov odchýlok pre každý pár údajových bodov v dvoch množinách údajov", "ad": "je prvý rozsah buniek celých čísel a musia to byť čísla, polia alebo odkazy obsahujúce čísla!je druhý rozsah buniek celých čísel a musia to byť čísla, polia alebo odkazy obsahujúce čísla"}, "CRITBINOM": {"a": "(pokusy; pravdepodobnosť_s; alfa)", "d": "<PERSON><PERSON><PERSON><PERSON> na<PERSON><PERSON> hodnot<PERSON>, pre ktorú má distribučná funkcia binomického rozdelenia hodnotu väčšiu alebo rovnajúcu sa hodnote kritéria", "ad": "je počet Bernoulliho pokusov!je pravdepodobnosť úspešného pokusu. Argument je číslo od 0 do 1 vrátane oboch hodnôt!je hodnota kritéria. Je to číslo od 0 do 1 vrátane oboch hodnôt"}, "DEVSQ": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> druhých mocnín odchýlok údajových bodov od strednej hodnoty vzorky", "ad": "je 1 až <PERSON> argumentov alebo pole či odkaz na pole, pre ktor<PERSON> chcete vypočítať výsledok funkcie DEVSQ"}, "EXPONDIST": {"a": "(x; lambda; kumulat<PERSON>vne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie alebo hustoty exponenciálneho rozdelenia", "ad": "je hodnota funkcie. Musí to byť nezáporné číslo!je hodnota parametra. Musí to byť kladné číslo!je logická hodnota pre funkciu, ktorú chcete vrátiť: distribučná funkcia = TRUE; hustota rozdelenia pravdepodobnosti = FALSE"}, "EXPON.DIST": {"a": "(x; lambda; kumulat<PERSON>vne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie alebo hustoty exponenciálneho rozdelenia", "ad": "je hodnota funkcie. Musí to byť nezáporné číslo!je hodnota parametra. Musí to byť kladné číslo!je logická hodnota pre funkciu, ktorú chcete vrátiť: distribučná funkcia = TRUE; hustota rozdelenia pravdepodobnosti = FALSE"}, "FDIST": {"a": "(x; stupeň_voľnosti1; stupeň_voľnosti2)", "d": "<PERSON><PERSON><PERSON><PERSON> (sprava ohraničeného) rozdelenia pravdepodobnosti F (stupeň odlišnosti) pre dve množiny údajov", "ad": "je ho<PERSON><PERSON>, pre ktor<PERSON> chcete vyhodnotiť funkciu. Musí to byť nezáporné číslo!je počet stupňov voľnosti čitateľa, ktorým môže byť číslo medzi 1 a 10^10 okrem čísla 10^10!je počet stupňov voľnosti menovateľa, ktorým je číslo medzi 1 a 10^10 okrem čísla 10^10"}, "FINV": {"a": "(pravdepodobnosť; stupeň_voľnosti1; stupeň_voľnosti2)", "d": "Vráti hodnotu inverznej funkcie k (sprava ohraničenej) distribučnej funkcii rozdelenia pravdepodobnosti F: ak p = FDIST(x,...), potom FINV(p,...) = x", "ad": "je hodnota distribučnej funkcie rozdelenia F, ktorou je číslo medzi 0 a 1 vrátane oboch hodnôt!je počet stupňov voľnosti čitateľa, ktor<PERSON>m je byť číslo medzi 1 a 10^10 okrem čísla 10^10!je počet stupňov voľnosti menovateľa, ktorým je číslo medzi 1 a 10^10 okrem čísla 10^10"}, "FTEST": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> výsledok F-testu, dvojstrannú pravdepodobnosť, že rozptyly v argumentoch Pole1 a Pole2 nie sú výrazne odlišné", "ad": "je prvé pole alebo rozsah údajov. Hodnoty argumentu môžu byť čísla, názvy, polia alebo odkazy obsahujúce čísla (prázdne bunky sa neberú do úvahy)!je druhé pole alebo rozsah údajov. Hodnoty argumentu môžu byť čísla, názvy, polia alebo odkazy obsahujúce čísla (prázdne bunky sa neberú do úvahy)"}, "F.DIST": {"a": "(x; stupeň_voľnosti1; stupeň_voľnosti2; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> (zľava ohraničené) F rozdelenie pravdepodobnosti (miera rozličnosti) pre dve množiny údajov", "ad": "je hodnota, pri ktorej sa má funkcia hodnotiť, a predstavuje nezáporné číslo!je čitateľ počtu stupňov vo<PERSON><PERSON><PERSON>, čiže číslo od 1 do 10^10, a menšie než 10^10!je menovateľ počtu stupňov voľ<PERSON>ti, čiže číslo od 1 do 10^10 a menšie než 10^10!je logická hodnota pre funkciu, ktorá sa má vrátiť: ak ide o funkciu kumulatívneho rozdelenia, hodnota je TRUE a ak ide o funkciu hustoty pravdepodobnosti, hodnota je FALSE"}, "F.DIST.RT": {"a": "(x; stupeň_voľnosti1; stupeň_voľnosti2)", "d": "<PERSON><PERSON><PERSON><PERSON> (sprava ohraničené) F rozdelenie pravdepodobnosti (miera rozličnosti) pre dve množiny údajov", "ad": "je hodnota, pri ktorej sa má funkcia hodnotiť, a predstavuje nezáporné číslo!je čitateľ počtu stupňov voľ<PERSON>ti, čiže číslo od 1 do 10^10, a menšie než 10^10!je menovateľ počtu stupňov voľ<PERSON>ti, čiže číslo od 1 do 10^10 a menšie než 10^10"}, "F.INV": {"a": "(pravdepodobnosť; stupeň_voľnosti1; stupeň_voľnosti2)", "d": "<PERSON><PERSON><PERSON><PERSON> in<PERSON> (ľavostranného) rozdelenia pravdepodobnosti F: ak p = F.DIST(x,...), potom F.INV(p,...) = x", "ad": "je pravdepodobnosť kumulatívneho rozdelenia F, číslo od 0 do 1 (vr<PERSON><PERSON><PERSON>)!je počet stupňov voľnosti v čitateli, číslo od 1 do 10^10 okrem čísla 10^10!je počet stupňov voľnosti v menovateli, číslo od 1 do 10^10 okrem čísla 10^10"}, "F.INV.RT": {"a": "(pravdepodobnosť; stupeň_voľnosti1; stupeň_voľnosti2)", "d": "<PERSON><PERSON><PERSON><PERSON> inverzn<PERSON> hodnotu (sprava ohraničeného) F rozdelenia pravdepodobnosti: ak p = F.DIST.RT(x,...), potom F.INV.RT(p,...) = x", "ad": "je pravdepodobnosť priradená ku kumulatívnemu F rozdeleniu, čo je číslo od 0 do 1 vrátane!je čitateľ počtu stupňov voľ<PERSON>ti, čiže číslo od 1 do 10^10, a menšie než 10^10!je menovateľ počtu stupňov voľnosti, čiže číslo od 1 do 10^10 a menšie než 10^10"}, "F.TEST": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> výsledok F-testu, dvojstrannú pravdepodobnosť, že rozptyly v argumentoch Pole1 a Pole2 nie sú výrazne odlišné", "ad": "je prvé pole alebo rozsah údajov. Hodnoty argumentu môžu byť čísla, názvy, polia alebo odkazy obsahujúce čísla (prázdne bunky sa neberú do úvahy)!je druhé pole alebo rozsah údajov. Hodnoty argumentu môžu byť čísla, názvy, polia alebo odkazy obsahujúce čísla (prázdne bunky sa neberú do úvahy)"}, "FISHER": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON> Fisherovej transformácie", "ad": "je hodn<PERSON>, pre ktor<PERSON> chcete zistiť hodnotu transformácie, ktorou je číslo medzi -1 a 1 okrem čísel -1 a 1"}, "FISHERINV": {"a": "(y)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu inverznej funkcie k Fisherovej transformácii: ak y = FISHER(x), potom FISHERINV(y) = x", "ad": "je hodnota, pre ktor<PERSON> chcete zistiť hodnotu inverznej transformácie"}, "FORECAST": {"a": "(x; známe_ys; známe_xs)", "d": "Vypočíta alebo odhadne bud<PERSON> hodnotu lineárneho trendu pomocou existuj<PERSON><PERSON><PERSON> hodn<PERSON>t", "ad": "je údajov<PERSON> bod, pre ktorý chcete odhadnúť hodnotu. Musí to byť číselná hodnota!je závislé pole alebo rozsah číselných údajov!je nezávislé pole alebo rozsah číselných údajov. Odchýlka argumentu Známe_x sa nesmie rovnať nule"}, "FORECAST.ETS": {"a": "(cie<PERSON><PERSON><PERSON>_d<PERSON><PERSON>; hodnoty; čas<PERSON>_os; [sez<PERSON><PERSON><PERSON>]; [dopĺňanie_údajov]; [agreg<PERSON>cia])", "d": "<PERSON><PERSON><PERSON><PERSON> predpoved<PERSON>ú hodnotu pre konkrétny budúci cieľový dátum pomocou metódy exponenciálneho vyrovnávania.", "ad": "je úda<PERSON><PERSON><PERSON> bod, pre ktorý Spreadsheet Editor predpovedá hodnotu. Mal by p<PERSON><PERSON><PERSON><PERSON><PERSON> vo vzore hodnôt na časovej osi.!je pole alebo rozsah číseln<PERSON>ch údajov, ktor<PERSON> predpovedáte.!je nezávislé pole alebo rozsah číselných údajov. Medzi dátumami na časovej osi musí byť konzistentný krok a tieto dátumy nesmú mať nulovú hodnotu.!je voliteľná číselná hodnota označujúca dĺžku vzoru sezónnosti. Predvolená hodnota 1 znamená, že sezónnosť sa zisťuje automaticky.!je voliteľná hodnota na zaobchádzanie s chýbajúcimi hodnotami. Predvolená hodnota 1 nahradí chýbajúce hodnoty pomocou interpolácie, v prípade hodnoty 0 sa nahradia nulami.!je voliteľná číselná hodnota na agregáciu viacerých hodnôt s rovnakou časovou pečiatkou. Ak je táto možnosť prázdna, Spreadsheet Editor vypočíta priemer hodnôt."}, "FORECAST.ETS.CONFINT": {"a": "(cie<PERSON><PERSON><PERSON>_d<PERSON><PERSON>; hodnoty; čas<PERSON>_os; [úrove<PERSON>_spoľahlivosti]; [sez<PERSON><PERSON><PERSON>]; [dopĺňanie_údajov]; [agreg<PERSON>cia])", "d": "Vráti interval spoľahlivosti pre predpovedanú hodnotu v zadanom cieľovom dátume.", "ad": "je ú<PERSON><PERSON><PERSON><PERSON> bod, pre ktorý Spreadsheet Editor predpovedá hodnotu. Mal by p<PERSON><PERSON><PERSON><PERSON><PERSON> vo vzore hodnôt na časovej osi.!je pole alebo rozsah číseln<PERSON>ch údajov, ktor<PERSON> predpovedáte.!je nezávislé pole alebo rozsah číselných údajov. Medzi dátumami na časovej osi musí byť konzistentný krok a tieto dátumy nesmú mať nulovú hodnotu.!je číslo v rozsahu 0 až 1, ktoré označuje úroveň spoľahlivosti vypočítaného intervalu spoľahlivosti. Predvolená hodnota je 0,95.!je voliteľná číselná hodnota označujúca dĺžku vzoru sezónnosti. Predvolená hodnota 1 znamená, že sezónnosť sa zisťuje automaticky.!je voliteľná hodnota na zaobchádzanie s chýbajúcimi hodnotami. Predvolená hodnota 1 nahradí chýbajúce hodnoty pomocou interpolácie, v prípade hodnoty 0 sa nahradia nulami.!je voliteľná číselná hodnota na agregáciu viacerých hodnôt s rovnakou časovou pečiatkou. Ak je táto možnosť prázdna, Spreadsheet Editor vypočíta priemer hodnôt."}, "FORECAST.ETS.SEASONALITY": {"a": "(hodnoty; časová_os; [dopĺňanie_údajov]; [agreg<PERSON><PERSON>])", "d": "Vráti dĺžku opakujúceho sa vzoru, ktorý aplikácia zistí pre zadaný časový rad.", "ad": "je pole alebo rozsah <PERSON><PERSON>, k<PERSON><PERSON> pred<PERSON>.!je nezávislé pole alebo rozsah číselných údajov. Medzi dátumami na časovej osi musí byť konzistentný krok a tieto dátumy nesmú mať nulovú hodnotu.!je voliteľná hodnota na zaobchádzanie s chýbajúcimi hodnotami. Predvolená hodnota 1 nahradí chýbajúce hodnoty pomocou interpolácie, v prípade hodnoty 0 sa nahradia nulami.!je voliteľná číselná hodnota na agregáciu viacerých hodnôt s rovnakou časovou pečiatkou. Ak je táto možnosť prázdna, Spreadsheet Editor vypočíta priemer hodnôt."}, "FORECAST.ETS.STAT": {"a": "(hodnoty; časová_os; typ_štatistiky; [sez<PERSON><PERSON><PERSON>]; [dopĺňanie_údajov]; [agreg<PERSON><PERSON>])", "d": "Vrá<PERSON> p<PERSON>žadovanú štatistiku pre prognózu.", "ad": "je pole alebo rozsah č<PERSON>el<PERSON><PERSON><PERSON> ú<PERSON>jov, k<PERSON><PERSON> pred<PERSON>.!je nezávislé pole alebo rozsah číselných údajov. Medzi dátumami na časovej osi musí byť konzistentný krok a tieto dátumy nesmú mať nulovú hodnotu.!je číslo v rozsahu 1 až 8 označujúce typ štatistiky, ktorú Spreadsheet Editor vráti pre vypočítanú prognózu.!je voliteľná číselná hodnota označujúca dĺžku vzoru sezónnosti. Predvolená hodnota 1 znamená, že sezónnosť sa zisťuje automaticky.!je voliteľná hodnota na zaobchádzanie s chýbajúcimi hodnotami. Predvolená hodnota 1 nahradí chýbajúce hodnoty pomocou interpolácie, v prípade hodnoty 0 sa nahradia nulami.!je voliteľná číselná hodnota na agregáciu viacerých hodnôt s rovnakou časovou pečiatkou. Ak je táto možnosť prázdna, Spreadsheet Editor vypočíta priemer hodnôt."}, "FORECAST.LINEAR": {"a": "(x; známe_y; známe_x)", "d": "Vypočíta alebo odhadne bud<PERSON> hodnotu lineárneho trendu pomocou existuj<PERSON><PERSON><PERSON> hodn<PERSON>t", "ad": "je údajov<PERSON> bod, pre ktorý chcete odhadnúť hodnotu. Musí to byť číselná hodnota!je závislé pole alebo rozsah číselných údajov!je nezávislé pole alebo rozsah číselných údajov. Odchýlka argumentu Známe_x sa nesmie rovnať nule"}, "FREQUENCY": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>_pole; <PERSON><PERSON><PERSON>_pole)", "d": "Vytvorí frekvenčnú tabuľku, čiže zvislé pole čísel s počtami výskytov hodnôt v jednotlivých rozsahoch", "ad": "je pole alebo odkaz na množinu hodnôt, pre ktor<PERSON> chcete zistiť počet výskytov (ignoruje prázdne bunky a text)!je pole alebo odkaz na intervaly, do ktorých chcete zoskupiť hodnoty argumentu údajové_pole"}, "GAMMA": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnot<PERSON> funkcie Gamma", "ad": "je hodn<PERSON>, pre k<PERSON><PERSON> chcete vypočítať funkciu Gamma"}, "GAMMADIST": {"a": "(x; alfa; beta; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie alebo hustoty rozdelenia gama", "ad": "je hodnota, pre ktorú chcete zistiť hodnotu rozdelenia. Musí to byť nezáporné číslo!je parameter rozdelenia, ktorý musí byť kladné číslo!je parameter rozdelenia, ktorý musí byť kladné číslo. Ak beta = 1, funkcia GAMMADIST vráti hodnotu štandardného rozdelenia gama!je logická funkcia: vráti distribučnú funkciu = TRUE; vráti hustotu rozdelenia pravdepodobnosti = FALSE, alebo nie je zadaná"}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> gama", "ad": "je hodnota, pri ktorej chcete hodnotiť rozdelenie, je to nezáporné číslo!je parameter rozdelenia a je to kladné číslo!je parameter rozdelenia a je to kladné číslo. Ak hodnota beta = 1, funkcia GAMMA.DIST vráti štandardné rozdelenie gama!je logická hodnota: ak vráti funkciu kumulatívneho rozdelenia, hodnota = TRUE, a ak vráti funkciu hromadnej pravdepodobnosti, hodnota = FALSE alebo sa vynechá"}, "GAMMAINV": {"a": "(pravdepodobnosť; alfa; beta)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu inverznej funkcie k distribučnej funkcii gama rozdelenia: ak p = GAMMADIST(x,...), potom GAMMAINV(p,...) = x", "ad": "je hodnota pravdepodobnosti gama rozdelenia, ktorou je číslo medzi 0 a 1 vrátane obidvoch hodnôt!je parameter rozdelenia, ktorý musí byť kladné číslo!je parameter rozdelenia, ktorý musí byť kladné číslo. Ak beta = 1, GAMMAINV vráti inverznú funkciu k štandardnému gama rozdeleniu"}, "GAMMA.INV": {"a": "(pravdepodobnosť; alfa; beta)", "d": "<PERSON><PERSON><PERSON><PERSON> inverznú hodnotu kumulatívneho rozdelenia gama: ak p = GAMMA.DIST(x,...), potom GAMMA.INV(p,...) = x", "ad": "je pravdepodobnosť priradená k rozdeleniu gama a je to číslo v rozsahu od 0 do 1 vrátane!je parameter rozdelenia a je to kladné číslo!je parameter rozdelenia a je to kladné číslo. Ak hodnota beta = 1, funkcia GAMMA.INV vráti inverznú hodnotu štandardného rozdelenia gama"}, "GAMMALN": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> prirodzený logaritmus funkcie gama", "ad": "je hodnota, pre ktorú chcete vypočítať hodnotu funkcie GAMMALN. Argument musí byť kladn<PERSON>"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> prirodzený logaritmus funkcie gama", "ad": "je hodn<PERSON>, pre ktor<PERSON> chcete vypočítať hodnotu funkcie GAMMALN.PRECISE. Argument musí byť kladn<PERSON>"}, "GAUSS": {"a": "(x)", "d": "Vráti hodnotu o 0,5 ni<PERSON>šiu než štandardné normálne kumulatívne rozdelenie", "ad": "je hodn<PERSON>, pre k<PERSON><PERSON> chcete vypočítať rozdelenie"}, "GEOMEAN": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> priemer poľa alebo rozsahu kladn<PERSON>ch číselných údajov", "ad": "je 1 až 255 čísel alebo názvov, polí alebo odkazov obs<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>ký priemer chcete zistiť"}, "GROWTH": {"a": "(známe_ys; [známe_xs]; [nové_xs]; [konštanta])", "d": "<PERSON><PERSON><PERSON><PERSON> hodn<PERSON> trendu exponenciálneho rastu, ktor<PERSON> zodpovedá známym údajovým bodom", "ad": "je množina hodnôt y známych vo vzťahu y = b*m^x. <PERSON><PERSON><PERSON><PERSON> to byť pole alebo rozsah kladných čísel!je voliteľná množina hodnôt x známych vo vzťahu y = b*m^x. <PERSON><PERSON><PERSON><PERSON> to byť pole alebo rozsah rovnakej veľkosti ako argument Známe_y!sú nové hodnoty x, pre ktoré má funkcia GROWTH vrátiť zodpovedajúce hodnoty y!je logická hodnota: konštanta b sa vypočíta štandardne, ak argument b = TRUE; konštanta b sa rovná 1, ak argument b = FALSE alebo nie je zadaný"}, "HARMEAN": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> priemer množiny kladných číselných údajov: prevrátená hodnota aritmetického priemeru prevr<PERSON>ten<PERSON>ch hodnôt", "ad": "je 1 až 255 čísel alebo názvov, polí alebo odkazov obs<PERSON><PERSON><PERSON><PERSON><PERSON>, ktor<PERSON>ch harmonický priemer chcete zistiť"}, "HYPGEOM.DIST": {"a": "(vzorka_s; veľkosť_vzorky; populácia_s; počet_pop; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hyperge<PERSON><PERSON><PERSON> roz<PERSON>", "ad": "je počet úspechov v rámci vzorky!je veľkosť vzorky!je počet úspechov v rámci súboru!je veľkosť súboru!je logická hodnota: ak ide o funkciu kumulatívneho rozdelenia, použije sa hodnota TRUE a ak ide o funkciu hustoty rozdelenia, použije sa hodnota FALSE"}, "HYPGEOMDIST": {"a": "(vzorka_s; počet_vzorka; populácia_s; počet_pop)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu hypergeometrického rozdelenia.", "ad": "je počet úspešných pokusov vo výbere!je veľkosť výberu !je počet úspešných pokusov v základnom súbore!je veľkosť základného súboru"}, "INTERCEPT": {"a": "(známe_ys; známe_xs)", "d": "Vypočíta súradnice bodu, v ktorom čiara pretína os y. Výpočet sa robí preložením najlepšej regresnej čiary známymi hodnotami x a y", "ad": "je závislý súbor pozorovaní alebo údajov. Hodnoty argumentu môžu byt čísla alebo názvy, polia alebo odkazy obsahujúce čísla!je nezávislý súbor pozorovaní alebo údajov. Hodnoty argumentu môžu byt čísla alebo názvy, polia alebo odkazy obsahujúce čísla"}, "KURT": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> množiny údajov", "ad": "je 1 až 255 čísel alebo názvov, polí alebo odkazov obs<PERSON><PERSON><PERSON><PERSON><PERSON>, pre ktor<PERSON> chcete vypočítať špicatosť"}, "LARGE": {"a": "(pole; k)", "d": "Vráti k-tu najväčšiu hodnotu v množine údajov, napríklad piate najväčšie číslo", "ad": "je pole alebo roz<PERSON><PERSON>, pre ktoré chcete určiť k-tu najväčšiu hodnotu!je pozícia hľadanej hodnoty (počítaná od najväčšej) v poli alebo rozsahu buniek"}, "LINEST": {"a": "(známe_ys; [známe_xs]; [kon<PERSON>tanta]; [štatist<PERSON>])", "d": "Vráti štatistiku popisujúcu lineárny trend zodpovedajúci známym údajovým bodom aproximáciou priamky metódou naj<PERSON>ších <PERSON>tvorcov", "ad": "je mno<PERSON>ina hodnôt y známych vo vzťahu y = mx + b!je voliteľná množina hodnôt x známych vo vzťahu y = mx + b!je logická hodnota: konštanta b sa vypočíta štandardne, ak argument b = TRUE alebo nie je zadaný; konštanta b sa rovná 0, ak argument b = FALSE!je logická hodnota: vráti dodatočnú regresnú štatistiku = TRUE; vráti koeficienty m a konštantu b = FALSE alebo nie je zadaná"}, "LOGEST": {"a": "(známe_ys; [známe_xs]; [kon<PERSON>tanta]; [štatist<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> štatistiku popisujúcu exponenciálnu krivku zodpovedajúcu známym údajovým bodom", "ad": "je mno<PERSON>ina hodnôt y známych vo vzťahu y = b*m^x!je voliteľná množina hodnôt x známych vo vzťahu y = b*m^x!je logická hodnota: konštanta b sa vypočíta štandardne, ak argument b = TRUE alebo nie je zadaný; konštanta b sa rovná 1, ak argument b = FALSE!je logická hodnota: vráti dodatočnú regresnú štatistiku = TRUE; vr<PERSON>ti koeficienty m a konštantu b = FALSE alebo nie je zadaná"}, "LOGINV": {"a": "(pravde<PERSON><PERSON>bnosť; stred; smerodajná_odch)", "d": "Vráti inverznú funkciu k distribučnej funkcii lognormálneho rozdelenia hodnôt x, kde funkcia ln(x) má normálne rozdelenie s parametrami Stred a Smerodajná_odch", "ad": "je pravdepodobnosť lognormálneho rozdelenia, ktorá je číslo medzi 0 a 1 vrátane obidvoch hodnôt!je stredná hodnota funkcie ln(x)!je smerodajná odchýlka funkcie ln(x), k<PERSON><PERSON> musí byť kladné č<PERSON>lo"}, "LOGNORM.DIST": {"a": "(x; stred; smer<PERSON><PERSON><PERSON>_odch; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> rozdelenie prirodzeného logaritmu x, kde ln(x) je normálne rozdelený s parametrami Stred a Smerodajná_odch", "ad": "je hodnota, pri ktorej sa má hodnotiť funkcia, a je to kladné číslo!je stredná hodnota ln(x)!je štandardná odchýlka ln(x) a je to kladné číslo!je logická hodnota: ak ide o funkciu kumulatívneho rozdelenia, použite hodnotu TRUE a ak ide o funkciu hustoty pravdepodobnosti, použite hodnotu FALSE"}, "LOGNORM.INV": {"a": "(pravde<PERSON><PERSON>bnosť; stred; smerodajná_odch)", "d": "Vráti inverznú funkciu k distribučnej funkcii lognormálneho rozdelenia hodnôt x, kde funkcia ln(x) má normálne rozdelenie s parametrami Stred a Smerodajná_odch", "ad": "je pravdepodobnosť lognormálneho rozdelenia, ktorá je číslo medzi 0 a 1 vrátane obidvoch hodnôt!je stredná hodnota funkcie ln(x)!je smerodajná odchýlka funkcie ln(x), k<PERSON><PERSON> musí byť kladné č<PERSON>lo"}, "LOGNORMDIST": {"a": "(x; stred; smerodajná_odch)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie lognormálneho rozdelenia hodnôt x, kde funkcia ln(x) má normálne rozdelenie s parametrami Stred a Smerodajná_odch", "ad": "je hodnota, pre ktor<PERSON> chcete zistiť hodnotu rozdelenia. Argument musí byť kladné č<PERSON>lo.!je stredná hodnota funkcie ln(x)!je smerodajná odchýlka funkcie ln(x), ktor<PERSON> musí byť kladné č<PERSON>lo"}, "MAX": {"a": "(číslo1; [číslo2]; ...)", "d": "Vráti najvyš<PERSON> hodnotu z množiny hodnôt. Ignoruje logické hodnoty a text", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> hodnôt alebo čísel v textovom formáte, ktorých maximálnu hodnotu chcete zistiť"}, "MAXA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vráti najväčšiu hodnotu v množine hodnôt. Neignoruje logické hodnoty a text", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> hodnôt alebo čísel v textovom formáte, ktorých maximálnu hodnotu chcete zistiť"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "Vráti maximálnu hodnotu nachádzajúcu sa v bunkách špecifikovaných určenou skupinou podmienok alebo kritérií", "ad": "bunky, v rámci ktorých sa určuje maximálna hodnota!je roz<PERSON><PERSON> buniek, ktor<PERSON> chcete vyhodnotiť v súvislosti s konkrétnou podmienkou!je podmienka alebo kritérium vo formáte čísla, v<PERSON>razu alebo textu, k<PERSON><PERSON> definuje, ktor<PERSON> bunky budú zahrnuté pri určovaní maximálnej hodnoty"}, "MEDIAN": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, čiže hodnotu v strede množiny daných čísel", "ad": "je 1 až 255 čísel alebo názvov, polí alebo odkazov obs<PERSON><PERSON><PERSON><PERSON><PERSON> , k<PERSON><PERSON><PERSON> medián chcete zistiť"}, "MIN": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> najnižšie číslo z množiny hodnôt. Ignoruje logické hodnoty a text", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>k<PERSON><PERSON> hodnôt alebo čísel v textovom formáte, ktorých minimálnu hodnotu chcete zistiť"}, "MINA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vráti najmenšiu hodnotu v množine hodnôt. Neignoruje logické hodnoty a text", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>k<PERSON><PERSON> hodnôt alebo čísel v textovom formáte, ktorých minimálnu hodnotu chcete zistiť"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "Vráti minimálnu hodnotu nachádzajúcu sa v bunkách špecifikovaných určenou skupinou podmienok alebo kritérií", "ad": "bunky, v rámci ktorých sa určuje minimálna hodnota!je roz<PERSON><PERSON> buniek, ktor<PERSON> chcete vyhodnotiť v súvislosti s konkrétnou podmienkou!je podmienka alebo kritérium vo formáte čísla, v<PERSON>razu alebo textu, k<PERSON><PERSON> definuje, ktor<PERSON> bunky budú zahrnuté pri určovaní minimálnej hodnoty"}, "MODE": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> modus, <PERSON><PERSON><PERSON><PERSON> hodnotu, ktorá sa v poli alebo rozsahu údajov vyskytuje alebo opakuje najčastejšie", "ad": "je 1 až 255 čísel alebo názvov, polí alebo odkazov obs<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> modus chcete zistiť"}, "MODE.MULT": {"a": "(číslo1; [číslo2]; ...)", "d": "Vráti z<PERSON> pole najčastejšie sa vyskytujúcich (alebo opakujúcich sa) hodnôt v poli alebo rozsahu údajov. V prípade vodorovného poľa použite funkciu =TRANSPOSE(MODE.MULT(číslo1,číslo2,...))", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, polí alebo odkazov obs<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> modus chcete zistiť"}, "MODE.SNGL": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> modus, <PERSON><PERSON><PERSON><PERSON> hodnotu, ktorá sa v poli alebo rozsahu údajov vyskytuje alebo opakuje najčastejšie", "ad": "je 1 až 255 čísel alebo názvov, polí alebo odkazov obs<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> modus chcete zistiť"}, "NEGBINOM.DIST": {"a": "(počet_f; počet_s; pravdepodobnosť_s; kumulatívne)", "d": "Záporné binomi<PERSON> r<PERSON>, pravdepodobnosť počet_f zlyhaní pred počet_s-tym úspechom pri pravdepodobnosti Pravdepodobnosť_s", "ad": "=počet zlyhaní!=prahový počet úspechov!=pravdepodobnosť úspechu (0 až 1)!=logická hodnota: kumulatívne rozdelenie = TRUE, hromadná pravdepodobnosť = FALSE"}, "NEGBINOMDIST": {"a": "(počet_f; počet_s; pravdepodobnosť_s)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu záporného binomického rozdelenia (pravdepodobnosť Počet_f neúspešných pokusov po Počet_s úspešných pri pravdepodobnosti Pravdepodobnosť_s", "ad": "=neúspešné pokusy!=úspešné pokusy!=pravdepodobnosť úspechu (od 0 do 1)"}, "NORM.DIST": {"a": "(x; stred; smer<PERSON><PERSON><PERSON>_odch; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> rozdelenie pre zadanú strednú hodnotu a štandardnú odchýlku", "ad": "je hodnota, k<PERSON>j rozdelenie chcete zistiť!je aritmetická stredná hodnota rozdelenia!je štandardná odchýlka rozdelenia a je to kladné číslo!je logická hodnota: ak ide o funkciu kumulatívnej distribúcie, použije sa hodnota TRUE a v prípade funkcie hustoty pravdepodobnosti sa použije hodnota FALSE"}, "NORMDIST": {"a": "(x; stred; smer<PERSON><PERSON><PERSON>_odch; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie alebo hustoty normálneho rozdelenia pre zadanú strednú hodnotu a smerodajnú odchýlku", "ad": "je hodnota, pre ktorú chcete zistiť hodnotu rozdelenia!je aritmetická stredná hodnota rozdelenia!je smerodajná odchýlka rozdelenia, ktorá musí byť kladné číslo!je logická hodnota: distribučná funkcia = TRUE; hustota rozdelenia pravdepodobnosti = FALSE"}, "NORM.INV": {"a": "(pravde<PERSON><PERSON>bnosť; stred; smerodajná_odch)", "d": "Vráti inverznú funkciu k distribučnej funkcii normálneho rozdelenia pre zadanú strednú hodnotu a smerodajnú odchýlku", "ad": "je pravdepodobnosť normálneho rozdelenia, ktorej hodnota musí byť číslo medzi 0 a 1 vrátane oboch krajných hodnôt!je aritmetická stredná hodnota rozdelenia!je smerodajná odchýlka rozdelenia, ktorá musí byť kladné číslo"}, "NORMINV": {"a": "(pravde<PERSON><PERSON>bnosť; stred; smerodajná_odch)", "d": "Vráti inverznú funkciu k distribučnej funkcii normálneho rozdelenia pre zadanú strednú hodnotu a smerodajnú odchýlku", "ad": "je pravdepodobnosť normálneho rozdelenia, ktorej hodnota musí byť číslo medzi 0 a 1 vrátane oboch krajných hodnôt!je aritmetická stredná hodnota rozdelenia!je smerodajná odchýlka rozdelenia, ktorá musí byť kladné číslo"}, "NORM.S.DIST": {"a": "(z; kumulat<PERSON>vne)", "d": "<PERSON><PERSON><PERSON><PERSON> normálne rozdelenie (so strednou hodnotou nula a štandardnou odchýlk<PERSON> jeden)", "ad": "je hodnota, pre ktorú chcete rozdelenie!je logická hodnota pre funkciu, ktorá sa má vrátiť: funkcia kumulatívneho rozdelenia = TRUE; funkcia hustoty pravdepodobnosti = FALSE"}, "NORMSDIST": {"a": "(z)", "d": "Vráti hodnotu distribučnej funkcie štandardného normálneho rozdelenia. Toto rozdelenie má strednú hodnotu 0 a smerodajnú odchýlku 1.", "ad": "je hodn<PERSON>, k<PERSON><PERSON> rozdelenie chcete zistiť"}, "NORM.S.INV": {"a": "(pravdepodobnosť)", "d": "Vráti inverznú funkciu k distribučnej funkcii štandardného normálneho rozdelenia. Toto rozdelenie má strednú hodnotu 0 a smerodajnú odchýlku 1.", "ad": "je pravdepodobnosť normálneho rozdelenia, ktorej hodnota musí byť číslo medzi 0 a 1 vrátane oboch krajných hodnôt"}, "NORMSINV": {"a": "(pravdepodobnosť)", "d": "Vráti inverznú funkciu k distribučnej funkcii štandardného normálneho rozdelenia. Toto rozdelenie má strednú hodnotu 0 a smerodajnú odchýlku 1.", "ad": "je pravdepodobnosť normálneho rozdelenia, ktorej hodnota musí byť číslo medzi 0 a 1 vrátane oboch krajných hodnôt"}, "PEARSON": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> koe<PERSON> korelácie r", "ad": "je množina nezávislých hodnôt!je množina závislých hodnôt"}, "PERCENTILE": {"a": "(pole; k)", "d": "Vráti k-ty percentil hodnôt v rozsahu", "ad": "je pole alebo r<PERSON><PERSON><PERSON>, ktor<PERSON> definuje vzájomnú polohu!je hodnota percentilu, k<PERSON><PERSON> lež<PERSON> me<PERSON> 0 a 1 vrátane"}, "PERCENTILE.EXC": {"a": "(pole; k)", "d": "Vráti k-ty percentil hodnôt v rozsahu, kde k je z rozsahu čísel väčších ako 0 a menších ako 1", "ad": "je pole alebo r<PERSON><PERSON><PERSON>, k<PERSON><PERSON> definujú relatívne postavenie!je hodnota percentilu z rozsahu od 0 do 1 vrátane"}, "PERCENTILE.INC": {"a": "(pole; k)", "d": "Vráti k-ty percentil hodnôt v rozsahu, kde k je z rozsahu čísel od 0 do 1 vrátane", "ad": "je pole alebo r<PERSON><PERSON><PERSON>, k<PERSON><PERSON> definujú relatívne postavenie!je hodnota percentilu z rozsahu od 0 do 1 vrátane"}, "PERCENTRANK": {"a": "(pole; x; [význam<PERSON>ť])", "d": "Vráti poradie hodnoty v množine údajov vyjadrené percentuálnou časťou množiny údajov", "ad": "je pole alebo roz<PERSON>h ú<PERSON>jov s č<PERSON>elnými hodnotami, ktor<PERSON> definuje vzájomnú polohu!je hodnota, ktorej poradie chcete zistiť!je voliteľná hodnota, ktorá určuje počet platných číslic výslednej percentuálnej hodnoty. Ak sa nezadá, použijú sa tri číslice (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(pole; x; [význam<PERSON>ť])", "d": "Vráti pozíciu hodnoty v množine údajov ako percentuálnu hodnotu (hodnotu väčšiu ako 0 a menšiu ako 1) množiny údajov", "ad": "je pole alebo roz<PERSON>h ú<PERSON>v s č<PERSON><PERSON>mi hodnotami, ktor<PERSON> definujú relatívne postavenie!je hodnota, ktorej pozíciu chcete zistiť!je voliteľná hodnota, ktorá určuje počet platných číslic pre vrátenú percentuálnu hodnotu, ak sa vynechá, použijú sa tri číslice (0,xxx %)"}, "PERCENTRANK.INC": {"a": "(pole; x; [význam<PERSON>ť])", "d": "Vráti pozíciu hodnoty v množine údajov ako percentuálnu hodnotu množiny údajov od 0 do 1 vrátane", "ad": "je pole alebo roz<PERSON>h ú<PERSON>v s č<PERSON><PERSON>mi hodnotami, ktor<PERSON> definujú relatívne postavenie!je hodnota, ktorej pozíciu chcete zistiť!je voliteľná hodnota, ktorá určuje počet platných číslic pre vrátenú percentuálnu hodnotu, ak sa vynechá, použijú sa tri číslice (0,xxx %)"}, "PERMUT": {"a": "(počet; vybratý_počet)", "d": "Vracia počet permutácií pre daný počet objektov, ktoré možno vybrať z celkového počtu objektov", "ad": "je celkový počet objektov!je počet objektov v každej permutácii"}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "<PERSON><PERSON><PERSON><PERSON> počet permutácií pre daný počet objektov (s opakovaniami), k<PERSON><PERSON> mô<PERSON> byť vybraté z celkového počtu objektov", "ad": "je celkový počet objektov!je počet objektov v každej permutácii"}, "PHI": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu funkcie hustoty pre štandardné normálne rozdelenie", "ad": "je <PERSON><PERSON><PERSON>, pre ktoré chcete vypočítať hustotu štandardného normálneho rozdelenia"}, "POISSON": {"a": "(x; stred; kumula<PERSON><PERSON><PERSON>ne)", "d": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON> Poissonovho rozdelenia", "ad": "je počet udalostí!je očakávaná číselná hodnota, ktorou je kladné číslo!je logická hodnota: distribučná funkcia Poissonovho rozdelenia = TRUE, rozdelenie pravdepodobnosti Poissonovho rozdelenia = FALSE"}, "POISSON.DIST": {"a": "(x; stred; kumula<PERSON><PERSON><PERSON>ne)", "d": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON> Poissonovho rozdelenia", "ad": "je počet udalostí!je očakávaná číselná hodnota, ktorou je kladné číslo!je logická hodnota: distribučná funkcia Poissonovho rozdelenia = TRUE, rozdelenie pravdepodobnosti Poissonovho rozdelenia = FALSE"}, "PROB": {"a": "(x_rozsah; rozsah_pravdepodobnosti; dolný_limit; [horný_limit])", "d": "Vráti pravdepodobnosť, že hodnoty v rozsahu sú medzi dvoma hranicami, alebo sa rovnajú dolnej hranici", "ad": "je rozsah číselných hodnôt x, ku ktorým existujú zodpovedajúce pravdepodobnosti!je množina pravdepodobností zodpovedajúcich hodnotám v argumente X_rozsah, hodnotám medzi 0 a 1 vynímajúc 0!je dolná hranica hodnôt, pre ktoré chcete zistiť pravdepodobnosť!je voliteľná horná hranica hodnôt. Ak sa vynechá, funkcia PROB vráti pravdepodobnosť, že sa hodnoty argumentu X_rozsah rovnajú argumentu Dolný_limit"}, "QUARTILE": {"a": "(pole; kvart)", "d": "<PERSON><PERSON><PERSON><PERSON>", "ad": "je pole alebo rozsah buniek s číselnými hodnotami, pre ktoré chcete vypočítať hodnotu kvartilu!je číslo: najmenšia hodnota = 0, prv<PERSON> kvartil = 1, medi<PERSON> = 2, t<PERSON><PERSON> kvartil = 3, najv<PERSON>čšia hodnota = 4"}, "QUARTILE.INC": {"a": "(pole; kvart)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> množiny údajov na základe hodnôt percentilov od 0 do 1 vrátane", "ad": "je pole alebo rozsah buniek s <PERSON><PERSON><PERSON> hodnotami, pre ktor<PERSON> chcete zistiť hodnotu kvartilu!je číslo: minimálna hodnota = 0; 1. kvar<PERSON> = 1; hodnota mediánu = 2; 3. kvar<PERSON> = 3; maximálna hodnota = 4"}, "QUARTILE.EXC": {"a": "(pole; kvart)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>til množiny údajov na základe hodnôt percentilov, ktoré sú väčšie ako 0 a menšie ako 1", "ad": "je pole alebo rozsah buniek s <PERSON><PERSON><PERSON> hodnotami, pre ktor<PERSON> chcete zistiť hodnotu kvartilu!je číslo: minimálna hodnota = 0; 1. kvar<PERSON> = 1; hodnota mediánu = 2; 3. kvar<PERSON> = 3; maximálna hodnota = 4"}, "RANK": {"a": "(č<PERSON>lo; odkaz; [poradie])", "d": "Vráti relatívnu veľkosť čísla v zozname čísel vzhľadom na hodnoty v zozname", "ad": "je <PERSON><PERSON><PERSON>, k<PERSON><PERSON>ho relatívnu veľkosť chcete zistiť!je pole alebo odkaz na zoznam čísel. Ignoruje nečíselné hodnoty!je číslo: poradie v zozname usporiadanom zostupne = 0 alebo nie je zadané; poradie v zozname usporiadanom vzostupne = každá hodnota okrem hodnoty 0"}, "RANK.AVG": {"a": "(č<PERSON>lo; odkaz; [poradie])", "d": "Vráti pozíciu čísla v zozname čísel: jeho veľkosť vo vzťahu k ostatným hodnotám v zozname; ak má viacero hodnôt rovnakú pozíciu, vráti sa priemerná pozícia", "ad": "je <PERSON><PERSON><PERSON>, ktor<PERSON><PERSON> pozíciu chcete zistiť!je pole zoznamu čísel alebo odkaz na zoznam čísel. <PERSON><PERSON><PERSON><PERSON>, ktoré nie sú číselné, sa ignorujú!je číslo: pozícia v zostupne zoradenom zozname = 0 alebo sa vynechá; pozícia vo vzostupne zoradenom zozname = ľubovoľná nenulová hodnota"}, "RANK.EQ": {"a": "(č<PERSON>lo; odkaz; [poradie])", "d": "Vráti pozíciu čísla v zozname čísel: jeho veľkosť vo vzťahu k ostatným hodnotám v zozname; ak má viacero hodnôt rovnakú pozíciu, vráti sa najvyššia pozícia tejto množiny hodnôt", "ad": "je <PERSON><PERSON><PERSON>, ktor<PERSON><PERSON> pozíciu chcete zistiť!je pole zoznamu čísel alebo odkaz na zoznam čísel. <PERSON><PERSON><PERSON><PERSON>, ktoré nie sú číselné, sa ignorujú!je číslo: pozícia v zostupne zoradenom zozname = 0 alebo sa vynechá; pozícia vo vzostupne zoradenom zozname = ľubovoľná nenulová hodnota"}, "RSQ": {"a": "(známe_ys; známe_xs)", "d": "<PERSON><PERSON><PERSON><PERSON> d<PERSON> mocninu <PERSON> kore<PERSON>čného koe<PERSON>u dan<PERSON><PERSON> údajov<PERSON> bodov", "ad": "je pole alebo rozsah údajových bodov. Hodnoty argumentu môžu byť čísla alebo názvy, polia alebo odkazy obsahujúce čísla!je pole alebo rozsah údajových bodov. Hodnoty argumentu môžu byť čísla alebo názvy, polia alebo odkazy obsahujúce čísla"}, "SKEW": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> rozdelenia: charakteristika stupňa asymetrie rozdelenia okolo strednej hodnoty", "ad": "je 1 až 255 čísel alebo názvov, polí alebo odkazov obs<PERSON><PERSON><PERSON><PERSON><PERSON>, pre ktor<PERSON> chcete vypočítať šikmosť"}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> rozdelenia na základe populácie: charakteristika stupňa asymetrie rozdelenia okolo strednej hodnoty", "ad": "sú 1 až 254 čísel alebo názvov, polí alebo odkazov obs<PERSON><PERSON><PERSON><PERSON><PERSON>, pre ktor<PERSON> chcete zistiť šikmosť populácie"}, "SLOPE": {"a": "(známe_ys; známe_xs)", "d": "Vráti gradient lineárnej regresnej čiary daných údajových bodov", "ad": "je pole alebo rozsah buniek č<PERSON>elných závislých údajových bodov. Hodnoty argumentu môžu byť čísla alebo názvy, polia alebo odkazy obsahujúce čísla!je množina nezávislých údajových bodov. Hodnoty argumentu môžu byť čísla alebo názvy, polia alebo odkazy obsahujúce čísla"}, "SMALL": {"a": "(pole; k)", "d": "Vráti k-tu najmenšiu hodnotu v množine údajov, napríklad piate najmenšie číslo", "ad": "je pole alebo roz<PERSON>h <PERSON><PERSON>, pre ktoré chcete určiť k-tu najmenšiu hodnotu!je pozícia hľadanej hodnoty (počítaná od najmenšej) v poli alebo rozsahu"}, "STANDARDIZE": {"a": "(x; stred; smerodajná_odch)", "d": "Vracia normalizovanú hodnotu z rozdelenia určeného strednou hodnotou a smerodajnou odchýlkou ", "ad": "je hodnota, ktor<PERSON> chcete normalizovať!je aritmetická stredná hodnota rozdelenia!je smerodajná odchýlka rozdelenia, ktorá musí byť kladné <PERSON>lo"}, "STDEV": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne smerodajnú odchýlku na základe vzorky (ignoruje logické hodnoty a text vo vzorke)", "ad": "je 1 až 255 čísel korešpondujúcich so vzorkou výberu. Môžu to byť čísla alebo odkazy obsahuj<PERSON>ce čísla"}, "STDEV.P": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočíta s<PERSON> odchý<PERSON> cel<PERSON>, k<PERSON><PERSON> bol zadaný ako argument (ignoruje logické hodnoty a text)", "ad": "je 1 až 255 čísel alebo odkazov obsahuj<PERSON><PERSON>ch čísla, k<PERSON><PERSON> zodpovedajú základnému sú<PERSON>u. Môžu to byť čísla alebo odkazy obsahujúce čísla"}, "STDEV.S": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne smerodajnú odchýlku na základe vzorky (ignoruje logické hodnoty a text vo vzorke)", "ad": "je 1 až 255 čísel korešpondujúcich so vzorkou výberu. Môžu to byť čísla alebo odkazy obsahuj<PERSON>ce čísla"}, "STDEVA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Odhadne smerodajnú odchýlku podľa výberového súboru vrátane logických hodnôt a textu. Text a logická hodnota FALSE = 0; logická hodnota TRUE = 1", "ad": "je 1 až 255 hodnôt zodpovedajúcich výberovému súboru. Môžu to byť hodnoty, názvy alebo odkazy na hodnoty"}, "STDEVP": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočíta s<PERSON> odchý<PERSON> cel<PERSON>, k<PERSON><PERSON> bol zadaný ako argument (ignoruje logické hodnoty a text)", "ad": "je 1 až 255 čísel alebo odkazov obsahuj<PERSON><PERSON>ch čísla, k<PERSON><PERSON> zodpovedajú základnému sú<PERSON>u. Môžu to byť čísla alebo odkazy obsahujúce čísla"}, "STDEVPA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vypočíta smerodajnú odchýlku základného súboru vrátane logických hodnôt a textu. Text a logická hodnota FALSE = 0; logická hodnota TRUE = 1", "ad": "je 1 až 255 hodnôt zodpovedajúcich základnému sú<PERSON>u. Môžu to byť hodnoty, názvy, polia alebo odkazy obsahujúce hodnoty"}, "STEYX": {"a": "(známe_ys; známe_xs)", "d": "<PERSON><PERSON><PERSON><PERSON> chybu predpokladanej hodnoty y pre každé x regresie", "ad": "je pole alebo rozsah závislých údajových bodov. Hodnoty argumentu môžu byť čísla alebo názvy, polia alebo odkazy obsahujúce čísla!je pole alebo rozsah nezávislých údajových bodov. Hodnoty argumentu môžu byť čísla alebo názvy, polia alebo odkazy obsahujúce čísla"}, "TDIST": {"a": "(x; stupeň_voľnosti; strany)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnot<PERSON>ovho t-rozdelenia", "ad": "je č<PERSON><PERSON><PERSON> hodnota, pre ktorú chcete zistiť hodnotu rozdelenia!je celé číslo predstavujúce počet stup<PERSON>ov voľnosti, ktoré určujú rozdelenie!u<PERSON><PERSON><PERSON>, či sa jedná o jednostranné alebo obojstranné rozdelenie: jednostranné rozdelenie = 1; obojstrann<PERSON> rozdelenie = 2"}, "TINV": {"a": "(pravdepodobnosť; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> inverznú funkciu k funkcii Studentovho t-rozdelenia", "ad": "je pravdepodobnosť zodpovedajúca obojstrannému Studentovmu t-rozdeleniu, <PERSON><PERSON>lo medzi 0 a 1 vrátane!je kladné celé číslo predstavujúce počet stu<PERSON>ov voľ<PERSON>ti, ktor<PERSON> ur<PERSON>j<PERSON> rozdelenie"}, "T.DIST": {"a": "(x; stupeň_voľnosti; kumulatívne)", "d": "Vráti zľava ohraničené Studentovo t-rozdelenie", "ad": "je č<PERSON>elná hodnota, pri ktorej sa hodnotí rozdelenie!je celé číslo označujúce počet stupňov voľnosti, ktoré charakterizuje rozdelenie!je logická hodnota: ak ide o funkciu kumulatívneho rozdelenia, použije sa hodnota TRUE a ak ide o funkciu hustoty pravdepodobnosti, použite hodnotu FALSE"}, "T.DIST.2T": {"a": "(x; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> ohraničené Studentovo t-rozdelenie", "ad": "je č<PERSON>elná hodnota, pri ktorej sa hodnotí rozdelenie!je celé číslo označujúce počet stupňov voľnosti, ktoré charakterizuje rozdelenie"}, "T.DIST.RT": {"a": "(x; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> sprava ohraničené Studentovo t-rozdelenie", "ad": "je č<PERSON>elná hodnota, pri ktorej sa hodnotí rozdelenie!je celé číslo označujúce počet stupňov voľnosti, ktoré charakterizuje rozdelenie"}, "T.INV": {"a": "(pravdepodobnosť; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>ava ohraničenú hodnotu Studentovho t-rozdelenia", "ad": "je pravdepodobnosť priradená k obojstranne ohraničenému Studentovmu t-rozdeleniu a je to číslo v rozsahu od 0 do 1 vrátane!je kladné celé číslo označujúce počet stupňov voľnosti, ktorý charakterizuje rozdelenie"}, "T.INV.2T": {"a": "(pravdepodobnosť; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> ohraničenú hodnotu Studentovho t-rozdelenia", "ad": "je pravdepodobnosť priradená k obojstranne ohraničenému Studentovmu t-rozdeleniu a je to číslo v rozsahu od 0 do 1 vrátane!je kladné celé číslo označujúce počet stupňov voľnosti, ktorý charakterizuje rozdelenie"}, "T.TEST": {"a": "(pole1; pole2; strany; typ)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu Studentovho t-testu", "ad": "je prvá množina údajov!je druhá množina údajov!ur<PERSON><PERSON>, či sa jedná o jednostrannú alebo obojstrannú distribúciu: jednostranná distribúcia = 1; obojstranná distribúcia = 2!je druh t-testu: p<PERSON>rov<PERSON> = 1, dvojvýberový s rovnakým rozptylom = 2, dvojvýberový s nerovnakým rozptylom = 3"}, "TREND": {"a": "(známe_ys; [známe_xs]; [nové_xs]; [konštanta])", "d": "<PERSON><PERSON><PERSON><PERSON> lineárneho trendu zodpovedajúce známym údajovým bodom pomocou metódy naj<PERSON>š<PERSON>vor<PERSON>v", "ad": "je rozsah alebo pole hodnôt y známych vo vzťahu y = mx + b!je voliteľný rozsah alebo pole hodnôt x známych vo vzťahu y = mx + b. Pole musí mať rovnakú veľkosť ako pole argumentu Známe_y!je rozsah alebo pole nových hodnôt x, pre ktoré má funkcia TREND zistiť zodpovedajúce hodnoty y!je logická hodnota: konštanta b sa vypočíta štandardne, ak argument b = TRUE alebo nie je zadaný; konštanta b sa rovná 0, ak argument b = FALSE"}, "TRIMMEAN": {"a": "(pole; percento)", "d": "<PERSON>ráti priemernú hodnotu vnútornej časti množiny hodnôt údajov", "ad": "je rozsah alebo pole hodnôt, ktoré sa má orezať a zo zvyšných hodnôt vypočítať priemer!je zlomok udávajúci počet údajových bodov, ktor<PERSON> chcete vylúčiť z hornej a dolnej časti množiny údajov"}, "TTEST": {"a": "(pole1; pole2; strany; typ)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu Studentovho t-testu", "ad": "je prvá množina údajov!je druhá množina údajov!ur<PERSON><PERSON>, či sa jedná o jednostrannú alebo obojstrannú distribúciu: jednostranná distribúcia = 1; obojstranná distribúcia = 2!je druh t-testu: p<PERSON>rov<PERSON> = 1, dvojvýberový s rovnakým rozptylom = 2, dvojvýberový s nerovnakým rozptylom = 3"}, "VAR": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne rozptyl na základe vzorky (ignoruje logické hodnoty a text vo vzorke)", "ad": "je 1 až <PERSON><PERSON><PERSON>, k<PERSON><PERSON> vzorke výberu"}, "VAR.P": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočíta rozptyl základného súboru (ignoruje logické hodnoty a text v súbore)", "ad": "je 1 až <PERSON><PERSON><PERSON>, k<PERSON><PERSON> z<PERSON><PERSON><PERSON><PERSON>"}, "VAR.S": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne rozptyl na základe vzorky (vo vzorke ignoruje logické hodnoty a text)", "ad": "je 1 až <PERSON><PERSON><PERSON>, k<PERSON><PERSON> výberovému s<PERSON>"}, "VARA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Odhadne rozptyl podľa výberového súboru vrátane logických hodnôt a textu. Text a logická hodnota FALSE = 0; logická hodnota TRUE = 1", "ad": "je 1 až <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> výberovému <PERSON>"}, "VARP": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočíta rozptyl základného súboru (ignoruje logické hodnoty a text v súbore)", "ad": "je 1 až <PERSON><PERSON><PERSON>, k<PERSON><PERSON> z<PERSON><PERSON><PERSON><PERSON>"}, "VARPA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vypočíta rozptyl základného súboru vrátane logických hodnôt a textu. Text a logická hodnota FALSE = 0; logická hodnota TRUE = 1", "ad": "je 1 a<PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> z<PERSON><PERSON><PERSON><PERSON>"}, "WEIBULL": {"a": "(x; alfa; beta; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie alebo hustoty Weibullovho rozdelenia", "ad": "je hodnota, pre ktor<PERSON> chcete zistiť hodnotu rozdelenia. Musí to byť nezáporné číslo!je parameter rozdelenia, ktorý musí byť kladné číslo!je parameter rozdelenia, ktorý musí byť kladné číslo!je logická hodnota: distribučná funkcia = TRUE; hustota rozdelenia pravdepodobnosti = FALSE"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie alebo hustoty Weibullovho rozdelenia", "ad": "je hodnota, pre ktor<PERSON> chcete zistiť hodnotu rozdelenia. Musí to byť nezáporné číslo!je parameter rozdelenia, ktorý musí byť kladné číslo!je parameter rozdelenia, ktorý musí byť kladné číslo!je logická hodnota: distribučná funkcia = TRUE; hustota rozdelenia pravdepodobnosti = FALSE"}, "Z.TEST": {"a": "(pole; x; [sigma])", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>nnú P-hodnotu z-testu", "ad": "je pole alebo r<PERSON><PERSON><PERSON>, k<PERSON><PERSON> maj<PERSON> byť použité na testovanie argumentu X!je testovaná hodnota!je známa smerodajná odchýlka základného súboru. Ak sa vynechá, použije sa smerodajná odchýlka vzorky"}, "ZTEST": {"a": "(pole; x; [sigma])", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>nnú P-hodnotu z-testu", "ad": "je pole alebo r<PERSON><PERSON><PERSON>, k<PERSON><PERSON> maj<PERSON> byť použité na testovanie argumentu X!je testovaná hodnota!je známa smerodajná odchýlka základného súboru. Ak sa vynechá, použije sa smerodajná odchýlka vzorky"}, "ACCRINT": {"a": "(emisia; prv<PERSON>_úrok; vyrovnanie; sadzba; por; frekvencia; [z<PERSON><PERSON>]; [met<PERSON><PERSON>_výpo<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON>ý úrok cenného papiera, z ktorého sa pravidelne platí úrok.", "ad": "je dátum emisie cenn<PERSON>ho papiera, vyjadrený ako poradové číslo dátumu!je prvý dátum splatnosti úroku cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je ročná kupónová sadzba cenného papiera!je nominálna hodnota cenného papiera!je počet kupónových splátok za rok!je typ denného základu, ktorý chcete použiť!je logická hodnota: akumulácia úroku od dátumu emisie = TRUE alebo sa hodnota vynechá; výpočet od posledného dátumu splátky úroku = FALSE"}, "ACCRINTM": {"a": "(emisia; vyrovnanie; sadz<PERSON>; por; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> akumulovaný úrok cenného papiera, za ktorý sa platí úrok k dátumu splatnosti", "ad": "je dátum emisie cenn<PERSON>ho papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je ročná kupónová sadzba cenného papiera!je nominálna hodnota cenného papiera!je typ denn<PERSON>ho z<PERSON>ladu, ktor<PERSON> chcete použiť"}, "AMORDEGRC": {"a": "(cena; dá<PERSON>_nákupu; prv<PERSON>_obdobie; zost<PERSON><PERSON>; obdobie; sadzba; [z<PERSON><PERSON>])", "d": "Vráti pomernú časť lineárneho odpisu majetku za každé účtovné obdobie.", "ad": "je vstupná cena majetku!je dátum obstarania majetku!je dátum konca prvého obdobia!je zostatková cena na konci životnosti majetku.!je obdobie!je odpisová sadzba!ro<PERSON><PERSON>ý_základ: 0 pre rok s 360 dňami, 1 pre aktuálny základ, 3 pre rok s 365 dňami."}, "AMORLINC": {"a": "(cena; dá<PERSON>_nákupu; prv<PERSON>_obdobie; zost<PERSON><PERSON>; obdobie; sadzba; [z<PERSON><PERSON>])", "d": "Vráti pomernú časť lineárneho odpisu majetku za každé účtovné obdobie.", "ad": "je vstupná cena majetku!je dátum obstarania majetku!je dátum konca prvého obdobia!je zostatková cena na konci životnosti majetku.!je obdobie!je odpisová sadzba!ro<PERSON><PERSON>ý_základ: 0 pre rok s 360 dňami, 1 pre aktuálny základ, 3 pre rok s 365 dňami."}, "COUPDAYBS": {"a": "(vyrovnanie; splatnosť; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON>rá<PERSON> počet dní od začiatku kupónového obdobia až po dátum vyrovnania", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je počet kupónových splátok za rok!je typ denného základu, ktor<PERSON> chcete použiť"}, "COUPDAYS": {"a": "(vyrovnanie; splatnosť; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> po<PERSON> dn<PERSON> obdobia, k<PERSON><PERSON> obsahuj<PERSON> dá<PERSON> vyrovnania", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je počet kupónových splátok za rok!je typ denného základu, ktor<PERSON> chcete použiť"}, "COUPDAYSNC": {"a": "(vyrovnanie; splatnosť; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> počet dní od dátumu vyrovnania po ďalší dátum splatnosti kupónu", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je počet kupónových splátok za rok!je typ denného základu, ktor<PERSON> chcete použiť"}, "COUPNCD": {"a": "(vyrovnanie; splatnosť; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> dátum splatnosti kupónu dátume vyrovnania", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je počet kupónových splátok za rok!je typ denného základu, ktor<PERSON> chcete použiť"}, "COUPNUM": {"a": "(vyrovnanie; splatnosť; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> počet kupónových splátok medzi dátumom vyrovnania a dátumom splatnosti", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je počet kupónových splátok za rok!je typ denného základu, ktor<PERSON> chcete použiť"}, "COUPPCD": {"a": "(vyrovnanie; splatnosť; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> dá<PERSON> sp<PERSON>, k<PERSON><PERSON> predch<PERSON><PERSON> dátumu vyrovnania", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je počet kupónových splátok za rok!je typ denného základu, ktor<PERSON> chcete použiť"}, "CUMIPMT": {"a": "(sadz<PERSON>; pobd; sh; po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_obdobie; koncové_obdobie; typ)", "d": "<PERSON><PERSON><PERSON><PERSON> kumulatívny vyplatený úrok medzi dvomi obdobiami", "ad": "je úrok<PERSON> sadzba!je celkový počet období splátok!je súčasná hodnota!je prvé obdobie výpočtu!je posledné obdobie výpočtu!je načasovanie splátky"}, "CUMPRINC": {"a": "(sadz<PERSON>; pobd; sh; po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_obdobie; koncové_obdobie; typ)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> vyplatenú istinu z úveru medzi dvomi obdobiami", "ad": "je úrok<PERSON> sadzba!je celkový počet období splátok!je súčasná hodnota!je prvé obdobie výpočtu!je posledné obdobie výpočtu!je načasovanie splátky"}, "DB": {"a": "(cena; zostatok; životnosť; obdobie; [mesiac])", "d": "Vypočíta odpis majetku za zadané obdobie podľa metódy klesajúceho zostatku s pevným koeficientom.", "ad": "je vstupná cena majetku!je zostatková cena na konci životnosti majetku!je počet období, v ktorých sa majetok odpisuje (tzv. životnosť majetku)!je ob<PERSON><PERSON>, za ktoré chcete vypočítať odpis. Argument Obdobie musí byť zadaný v rovnakých jednotkách ako argument Životnosť!je počet mesiacov v prvom roku odpisu. Ak tento argument nezadáte, použije sa hodnota 12"}, "DDB": {"a": "(cena; zostatok; životnosť; obdo<PERSON>; [faktor])", "d": "Vypočíta odpis majetku za zadané obdobie podľa metódy dvojnásobného odpisovania z klesajúceho zostatku alebo inej zadanej metódy", "ad": "je vstupná cena majetku!je zostatková cena na konci životnosti majetku!je počet období, v ktorých sa majetok odpisuje (tzv. životnosť majetku)!je ob<PERSON><PERSON>, za ktoré chcete vypočítať odpis. Argument Obdobie musí byť zadaný v rovnakých jednotkách ako argument Životnosť!je miera poklesu zostatku. Ak tento argument nezadáte, použije sa hodnota 2 (metóda dvojnásobného odpisovania z klesajúceho zostatku)"}, "DISC": {"a": "(vyrovnanie; splatnosť; ss; vy<PERSON>nie; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON>zbu cenného papiera", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je cena cenného papiera za každých 100 USD nominálnej hodnoty!je hodnota odkúpenia cenného papiera za každých 100 USD nominálnej hodnoty!je typ denného základu, ktorý chcete použiť"}, "DOLLARDE": {"a": "(z<PERSON><PERSON><PERSON><PERSON>_<PERSON>; z<PERSON><PERSON><PERSON>)", "d": "Skonvertuje cenu dolára vyjadrenú zlomkom na cenu dolára vyjadrenú desatinným číslom", "ad": "je číslo vyjadrené ako zlomok!je celé číslo, ktoré sa použije v menovateli zlomku"}, "DOLLARFR": {"a": "(desatinn<PERSON>_do<PERSON>; <PERSON><PERSON><PERSON><PERSON>)", "d": "Skonvertuje cenu dolára vyjadrenú desatinným číslom na cenu dolára vyjadrenú zlomkom", "ad": "je desatinné číslo!je celé číslo, ktoré sa použije v menovateli zlomku"}, "DURATION": {"a": "(vyrovnanie; splatnosť; kupón; výnos; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> ročné trvanie cenného papiera s pravidelnými splátkami úroku", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je ročná kupónová sadzba cenného papiera!je ročný výnos cenného papiera!je počet kupónových splátok za rok!je typ denn<PERSON>ho základu, ktorý chcete použiť"}, "EFFECT": {"a": "(nominálna_sadzba; pzobd)", "d": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> roč<PERSON>ú <PERSON> mieru", "ad": "je nominálna úroková miera!je počet zlučovaných období za rok"}, "FV": {"a": "(sad<PERSON><PERSON>; počet_období; spl<PERSON>tka; [s<PERSON><PERSON><PERSON><PERSON>_hodnota]; [typ])", "d": "Vypočíta budúcu hodnotu investície pri pravidelných a konštantných platbách a konštantnej úrokovej sadzbe", "ad": "je úroková sadzba za obdobie. Ak chcete napríklad zadať štvrťročné platby pri ročnej percentuálnej sadzbe 6 %, použite zápis 6%/4!je celkový počet platobných období investície!je platba uskutočnená v jednotlivých obdobiach, ktorá sa nemení po celú dobu životnosti investície!je súčasná hodnota alebo celková čiastka určujúca súčasnú hodnotu budúcich platieb. Ak tento argument nezadáte, použije sa hodnota 0!je hodnota predstavujúca termín platby: platba na začiatku obdobia = 1; platba na konci obdobia = 0 alebo nie je zadaná"}, "FVSCHEDULE": {"a": "(istina; plán)", "d": "<PERSON><PERSON><PERSON><PERSON> bud<PERSON><PERSON> hodnotu úvodnej istiny po použití série zložených úrokových mier", "ad": "je súčasná hodnota!je pole úrokových mier, k<PERSON><PERSON>te p<PERSON>"}, "INTRATE": {"a": "(vyrovnanie; splatnosť; spl<PERSON>tka; vy<PERSON>nie; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> mieru za úplne investovaný cenný papier", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je suma investovaná do cenného papiera!je suma, ktorá sa má prijať k dátumu splatnosti!je typ denn<PERSON>ho základu, ktor<PERSON> chcete použiť"}, "IPMT": {"a": "(sadzba; obd; pobd; sh; [bh]; [typ])", "d": "Vypočíta výšku platby úroku v určitom úrokovom období pri pravidelných konštantných splátkach a konštantnej úrokovej sadzbe", "ad": "je ú<PERSON>ová sadzba za obdobie. Ak chcete napríklad zadať štvrťročné platby pri ročnej percentuálnej sadzbe 6 %, pou<PERSON><PERSON> zápis 6%/4!je ob<PERSON><PERSON>, pre ktoré chcete vypočítať úrok. Jeho hodnota musí byť v intervale od 1 do hodnoty argumentu Pobd!je celkový počet platobných období investície!je súčasná hodnota alebo celková čiastka určujúca súčasnú hodnotu budúcich platieb!je budúca hodnota alebo hotovostný zostatok, ktorý chcete dosiahnuť po zaplatení poslednej platby. Ak tento argument nezadáte, použije sa hodnota 0!je logická hodnota predstavujúca termín platby: platba na konci obdobia = 0 alebo nie je zada<PERSON>, platba na začiatku obdobia = 1"}, "IRR": {"a": "(hodnoty; [odhad])", "d": "Vypočíta vnútornú mieru návratnosti pre sériu hotovostn<PERSON> tokov", "ad": "je pole alebo odkaz na bunky obsahu<PERSON><PERSON><PERSON> čísla, ktor<PERSON>ch vnútornú mieru návratnosti chcete zistiť!je číslo, ktoré je vaším odhadom výsledku funkcie IRR. Ak tento argument nezadáte, použije sa hodnota 0,1 (10 percent)"}, "ISPMT": {"a": "(sadzba; obd; pobd; sh)", "d": "Vypočíta úrok zaplatený v zadanom období investície", "ad": "úroková sadzba za obdobie. Ak chcete napríklad zadať štvrťročné platby pri ročnej percentuálnej sadzbe 6 %, pou<PERSON><PERSON> zápis 6%/4!je obdobie, pre ktoré chcete zistiť výšku úroku!počet platobných období investície!celková čiastka určujúca súčasnú hodnotu série budúcich platieb"}, "MDURATION": {"a": "(vyrovnanie; splatnosť; kupón; výnos; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> upravené trvanie za cenný papier s predpokladanou nominálnou hodnotou 100 USD", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je ročná kupónová sadzba cenného papiera!je ročný výnos cenného papiera!je počet kupónových splátok za rok!je typ denn<PERSON>ho základu, ktorý chcete použiť"}, "MIRR": {"a": "(hodnoty; finančná_sadzba; reinvestičná_sadzba)", "d": "Vypočíta vnútornú mieru návratnosti pre sériu pravidelných hotovostných tokov. Zohľadňuje pritom náklady na investície, ako aj úrok z opätovnej investície získaných prostriedkov", "ad": "je pole alebo odkaz na bunky obsahu<PERSON><PERSON><PERSON> čísla, k<PERSON><PERSON> predstavujú splá<PERSON>ky (záporná hodnota) a príjmy (kladná hodnota) v pravidelných obdobiach!je úroková sadzba platená za používané peniaze!je úroková sadzba získaná z reinvestovaných peňazí"}, "NOMINAL": {"a": "(účinn<PERSON>_sadzba; pzobd)", "d": "<PERSON><PERSON><PERSON><PERSON> roč<PERSON>ú nominálnu úrokovú mieru", "ad": "je platná úroková miera!je počet zlučovaných období za rok"}, "NPER": {"a": "(sadzba; plt; sh; [bh]; [typ])", "d": "Vypočíta počet období pre investíciu pri pravidelných konštantných platbách a konštantnej úrokovej sadzbe", "ad": "je úroková sadzba za obdobie. Ak chcete napríklad zadať štvrťročné platby pri ročnej percentuálnej sadzbe 6 %, použite zápis 6%/4!je platba uskutočnená v jednotlivých obdobiach, ktorá sa nemení po celú dobu životnosti investície!je súčasná hodnota alebo celková čiastka určujúca súčasnú hodnotu série budúcich platieb!je budúca hodnota alebo hotovostný zostatok, ktorý chcete dosiahnuť po zaplatení poslednej platby. Ak tento argument nezadáte, použije sa hodnota 0!je logická hodnota: platba na začiatku obdobia = 1; platba na konci obdobia = 0 alebo nie je zadaná"}, "NPV": {"a": "(sadz<PERSON>; hodnota1; [hodnota2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>časnú hodnotu investície vypočítanú na základe diskontnej sadzby, série bud<PERSON><PERSON><PERSON> spl<PERSON> (záporná hodnota) a príjmov (kladná hodnota)", "ad": "je diskontná sadzba za jedno obdobie!je 1 až 254 platieb a príjmov rovnomerne rozložených v čase a vyskytujúcich sa na konci každého obdobia"}, "ODDFPRICE": {"a": "(vyrovnanie; splatnosť; emisia; prv<PERSON>_kup<PERSON>; sadzba; výnos; vyplatenie; frekvencia; [z<PERSON><PERSON>])", "d": "Vráti cenu za každých 100 USD nominálnej hodnoty cenného papiera s nepárnym prvým obdobím", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum emisie cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum prvej splatnosti kupónu cenného papiera, vyjadrený ako poradové číslo dátumu!je úroková miera cenného papiera!je ročný výnos cenného papiera!je hodnota odkúpenia cenného papiera za každých 100 USD nominálnej hodnoty!je počet kupónových splátok za rok!je typ denného základu, ktorý chcete použiť"}, "ODDFYIELD": {"a": "(vyrovnanie; splatnosť; emisia; prv<PERSON>_kup<PERSON>; úrok; po<PERSON>et; vyplatenie; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> vý<PERSON> cenného papiera s prvým nepárnym obdobím", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum emisie cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum prvej splatnosti kupónu cenného papiera, vyjadrený ako poradové číslo dátumu!je úroková miera cenného papiera!je cena cenného papiera!je hodnota odkúpenia cenného papiera za každých 100 USD nominálnej hodnoty!je počet kupónových splátok za rok!je typ denného základu, ktor<PERSON> chcete použiť"}, "ODDLPRICE": {"a": "(vyrovnanie; splatnosť; pos<PERSON>n<PERSON>_úrok; sadzba; výnos; vyplatenie; frekvencia; [z<PERSON><PERSON>])", "d": "Vráti cenu za každých 100 USD nominálnej hodnoty cenného papiera s nepárnym posledným obdobím", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako dátum poradové číslo dátumu!je posledný dátum splatnosti kupónu cenného papiera, vyjadrený ako poradové číslo dátumu!je úroková miera cenného papiera!je ročný výnos cenného papiera!je hodnota odkúpenia cenného papiera za každých 100 USD nominálnej hodnoty!je počet kupónových splátok za rok!je typ denného základu, ktorý chcete použiť"}, "ODDLYIELD": {"a": "(vyrovnanie; splatnosť; pos<PERSON>n<PERSON>_úrok; sadzba; ss; vyplatenie; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> vý<PERSON> cenného papiera s nepárnym posledným obdobím", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je posledný dátum splatnosti kupónu cenného papiera, vyjadrený ako poradové číslo dátumu!je úroková miera cenného papiera!je cena cenného papiera!je hodnota odkúpenia cenného papiera za každých 100 USD nominálnej hodnoty!je počet kupónových splátok za rok!je typ denn<PERSON>ho základu, ktor<PERSON> chcete použiť"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> o<PERSON>, ktoré sú potrebné na dosiahnutie zadanej hodnoty investície", "ad": "je <PERSON><PERSON>ová sadzba za dané obdobie!je súčasná hodnota investície!je požadovaná budúca hodnota investície"}, "PMT": {"a": "(sadzba; pobd; sh; [bh]; [typ])", "d": "Vypočíta splátku pôžičky pri konštantných platbách a konštantnej úrokovej sadzbe", "ad": "je úroková sadzba pre pôžičku za obdobie. Ak chcete napríklad zadať štvrťročné platby pri ročnej percentuálnej sadzbe 6 %, použite zápis 6%/4!je celkový počet splátok pôžičky!je súčasná hodnota: súčasná celková hodnota série budúcich platieb!je budúca hodnota alebo hotovostný zostatok, ktorý chcete dosiahnuť po zaplatení poslednej splátky. Ak tento argument nezadáte, použije sa hodnota 0!je logická hodnota: splátka na začiatku obdobia = 1; splátka na konci obdobia = 0 alebo nie je zadaná"}, "PPMT": {"a": "(sadzba; obd; pobd; sh; [bh]; [typ])", "d": "Vypočíta hodnotu splátky istiny pre zadanú investíciu pri pravidelných konštantných platbách a konštantnej úrokovej sadzbe", "ad": "je úroková sadzba na obdobie. Ak chcete napríklad zadať štvrťročné splátky pri ročnej percentuálnej sadzbe 6 %, použite zápis 6%/4!určuje obdobie a hodnota musí byť v rozsahu od 1 do hodnoty argumentu Pobd!je celkový počet platobných období investície!je súčasná hodnota: celková čiastka určujúca súčasnú hodnotu série budúcich platieb!je budúca hodnota alebo hotovostný zostatok, ktorý chcete dosiahnuť po zaplatení poslednej splátky!je logická hodnota: splátka na začiatku obdobia = 1; splátka na konci obdobia = 0 alebo nie je zadaná"}, "PRICE": {"a": "(vyrovnanie; splatnosť; sadzba; výnos; vy<PERSON>nie; frekvencia; [z<PERSON><PERSON>])", "d": "Vráti cenu za každých 100 USD nominálnej hodnoty cenného papiera, z ktorého sa platí pravidelný úrok", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je ročná kupónová sadzba cenného papiera!je ročný výnos cenného papiera!je hodnota odkúpenia cenného papiera za každých 100 USD nominálnej hodnoty!je počet kupónových splátok za rok!je typ denného základu, ktorý chcete použiť"}, "PRICEDISC": {"a": "(vyrovnanie; splatnosť; zľava; vyplatenie; [z<PERSON><PERSON>])", "d": "V<PERSON>á<PERSON> cenu za každých 100 USD diskontovaného cenného papiera", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je diskontná sadzba cenného papiera!je hodnota odkúpenia cenného papiera za každých 100 USD nominálnej hodnoty!je typ denného základu, ktor<PERSON> chcete použiť"}, "PRICEMAT": {"a": "(vyrovnanie; splatnosť; emisia; sadzba; výnos; [z<PERSON><PERSON>])", "d": "Vráti cenu za každých 100 USD nominálnej hodnoty cenného papiera, z ktorého sa k dátumu splatnosti platí úrok", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum emisie cenného papiera, vyjadrený ako poradové číslo dátumu!je úroková miera cenného papiera k dátumu emisie!je ročný výnos cenného papiera!je typ denn<PERSON>ho z<PERSON>ladu, ktor<PERSON> chcete použiť"}, "PV": {"a": "(sadzba; pobd; plt; [bh]; [typ])", "d": "Vypočíta súčasnú hodnotu investície: s<PERSON>č<PERSON>nú celkovú hodnotu série budúcich platieb", "ad": "je ú<PERSON>ová sadzba za obdobie. Ak chcete napríklad zadať štvrťročné platby pri ročnej percentuálnej sadzbe 6 %, použite zápis 6%/4!je celkový počet platobných období investície!je platba uskutočnená v jednotlivých obdobiach, ktorá sa nemení po celú dobu životnosti investície!je budúca hodnota alebo hotovostný zostatok, ktorý chcete dosiahnuť po zaplatení poslednej platby!je logická hodnota: platba na začiatku obdobia = 1; platba na konci obdobia = 0 alebo nie je zadaná"}, "RATE": {"a": "(pobd; plt; sh; [bh]; [typ]; [odhad])", "d": "Vypočíta úrokovú sadzbu za obdobie pôžičky alebo investície. Ak chcete napríklad zadať štvrťročné platby pri ročnej percentuálnej sadzbe 6 %, použite zá<PERSON> 6%/4", "ad": "je celkový počet platobných období pôžičky alebo investície!je platba uskutočnená v jednotlivých obdobiach, ktorá sa nemení po celú dobu životnosti pôžičky alebo investície!je súčasná hodnota: súčasná celková hodnota série budúcich platieb!je budúca hodnota alebo hotovostný zostatok, ktorý chcete dosiahnuť po zaplatení poslednej splátky. Ak tento argument nezadáte, použije sa hodnota 0!je logická hodnota: platba na začiatku obdobia = 1; platba na konci obdobia = 0 alebo nie je zadaná!je váš odhad sadzby; Ak tento argument nezadáte, jeho hodnota bude 0,1 (10 percent)"}, "RECEIVED": {"a": "(vyrovnanie; splatnosť; spl<PERSON>tka; zľava; [z<PERSON><PERSON>])", "d": "Vráti sumu prijatú k dátumu splatnosti za úplne investovaný cenný papier", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je suma investovaná do cenného papiera!je diskontná sadzba cenného papiera!je typ denn<PERSON>ho z<PERSON>ladu, ktor<PERSON> chcete použiť"}, "RRI": {"a": "(nper; pv; fv)", "d": "<PERSON><PERSON><PERSON><PERSON>zbu pre rast investície", "ad": "je počet období pre investíciu!je súčasná hodnota investície!je budúca hodnota investície"}, "SLN": {"a": "(cena; zostatok; životnosť)", "d": "Vypočíta odpis majetku za jedno obdobie pri rovnomernom odpisovaní", "ad": "je vstupná cena majetku!je zostatková cena na konci životnosti majetku!je počet období, v ktorých sa majetok odpisuje (tzv. životnosť majetku)"}, "SYD": {"a": "(cena; zostatok; životnosť; obd)", "d": "Vypočíta odpis majetku za zadané obdobie podľa metódy odpisovania s faktorom súčtu čísel rokov.", "ad": "je vstupná cena majetku!je zostatková cena na konci životnosti majetku!je počet období, v ktorých sa majetok odpisuje (tzv. životnosť majetku)!je o<PERSON><PERSON><PERSON>, ktor<PERSON> mus<PERSON> byť vyjadrené v rovnakých jednotkách ako argument Životnosť"}, "TBILLEQ": {"a": "(vyrovnanie; splatnosť; zľava)", "d": "Vráti výnos ekvivalentný obligácii za štátnu pokladničnú poukážku", "ad": "je dátum vyrovnania štátnej pokladničnej pouk<PERSON>žky, vyjadrený ako poradové číslo dátumu!je dátum splatnosti štátnej pokladničnej pouk<PERSON>, vyjadrený ako poradové číslo dátumu!je diskontná sadzba štátnej pokladničnej poukážky"}, "TBILLPRICE": {"a": "(vyrovnanie; splatnosť; zľava)", "d": "Vráti cenu za každých 100 USD nominálnej hodnoty štátnej pokladničnej poukážky", "ad": "je dátum vyrovnania štátnej pokladničnej pouk<PERSON>žky, vyjadrený ako poradové číslo dátumu!je dátum splatnosti štátnej pokladničnej pouk<PERSON>, vyjadrený ako poradové číslo dátumu!je diskontná sadzba štátnej pokladničnej poukážky"}, "TBILLYIELD": {"a": "(vyrovnanie; splatnosť; ss)", "d": "Vráti výnos štátnej pokladničnej poukážky", "ad": "je dátum vyrovnania štátnej pokladničnej poukážky, vyjadrený ako poradové číslo dátumu!je dátum splatnosti štátnej pokladničnej poukážky, vyjadrený ako poradové číslo dátumu!je cena štátnej pokladničnej poukážky za každých 100 USD nominálnej hodnoty"}, "VDB": {"a": "(cena; zostatok; životnosť; počiatočné_obdobie; koncové_obdobie; [faktor]; [bez_z<PERSON>y])", "d": "Vypočíta odpisy majetku za zadané obdobia vrátane neukončených období podľa metódy dvojnásobného odpisovania z klesajúceho zostatku alebo inej zadanej metódy", "ad": "je vstupná cena majetku!je zostatková cena na konci životnosti majetku!je počet období, v ktorých sa majetok odpisuje (tzv. životnosť majetku)!je za<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> obdo<PERSON>, za ktoré chcete vypočítať odpis. Počíta sa v rovnakých jednotkách ako argument Životnosť!je koncové obdobie, za ktoré chcete vypočítať odpis. Počíta sa v rovnakých jednotkách ako argument Životnosť!je miera poklesu zostatku. Ak tento argument nezadáte, použije sa hodnota 2 (metóda dvojnásobného odpisovania z klesajúceho zostatku)!je logická hodnota: prejsť na rovnomerné odpisovanie, ak je hodnota odpisu väčšia ako klesajúci zostatok = FALSE alebo nie je zadaná; neprejsť na rovnomerné odpisovanie = TRUE"}, "XIRR": {"a": "(hodnoty; dátumy; [odhad])", "d": "Vráti vnútornú mieru návratnosti pre plán hotovostných tokov", "ad": "je rad hotovost<PERSON><PERSON><PERSON> tokov, ktorý zodpovedá rozvrhu platieb k jednotlivým dátumom!je rozvrh dátumov platieb, ktorý zodpovedá platbám hotovostných tokov!je <PERSON><PERSON><PERSON>, o ktorom sa predpokladá, že sa blíži výsledku XIRR"}, "XNPV": {"a": "(sadz<PERSON>; hodnoty; d<PERSON><PERSON>y)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ú hodnotu plánu hotovostn<PERSON>ch tokov", "ad": "je disk<PERSON><PERSON> sadz<PERSON>, ktor<PERSON> sa má použiť na hotovostné toky!je rad hotovostn<PERSON>ch tokov, ktorý zodpovedá rozvrhu platieb k jednotlivým dátumom!je rozvrh dá<PERSON>ov platieb, ktorý zodpovedá platbám hotovostných tokov"}, "YIELD": {"a": "(vyrovnanie; splatnosť; sadzba; ss; vy<PERSON>nie; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> výnos cenného papiera, z ktorého sa platí pravidelný úrok", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je ročná kupónová sadzba cenného papiera!je cena cenného papiera za každých 100 USD nominálnej hodnoty!je hodnota odkúpenia cenného papiera za každých 100 USD nominálnej hodnoty!je počet kupónových splátok za rok!je typ denn<PERSON>ho základu, ktor<PERSON> chcete použiť"}, "YIELDDISC": {"a": "(vyrovnanie; splatnosť; ss; vy<PERSON>nie; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> roč<PERSON>ý výnos diskontovaného cenného papiera. Ide napríklad o štátne pokladničné p<PERSON>", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je cena cenného papiera za každých 100 USD nominálnej hodnoty!je hodnota odkúpenia cenného papiera za každých 100 USD nominálnej hodnoty!je typ denného základu, ktorý chcete použiť"}, "YIELDMAT": {"a": "(vyrovnanie; splatnosť; emisia; sadzba; ss; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> ročný výnos cenného papiera, z ktorého sa platí úrok k dátumu splatnosti", "ad": "je dátum vyrovnania cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum splatnosti cenného papiera, vyjadrený ako poradové číslo dátumu!je dátum emisie cenného papiera, vyjadrený ako poradové číslo dátumu!je úroková miera cenného papiera k dátumu emisie!je cena cenného papiera za každých 100 USD nominálnej hodnoty!je typ denn<PERSON>ho základu, ktorý chcete použiť"}, "ABS": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ol<PERSON> hodn<PERSON>, t.j. <PERSON><PERSON><PERSON> be<PERSON> znamienka + alebo -.", "ad": "je re<PERSON><PERSON>e <PERSON>, absolútnu hodnotu ktorého chcete zistiť"}, "ACOS": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vráti hodnotu arkus kosínusu čísla v radiánoch v intervale 0 až pí. Arkus kosínus je uhol, ktorého kosínus je argument Číslo", "ad": "je hodnota kosínusu <PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> musí byť v intervale -1 až 1"}, "ACOSH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu hyperbolického arkus kosínusu <PERSON>", "ad": "je ľ<PERSON><PERSON><PERSON><PERSON><PERSON>, ktoré sa rovná alebo je väčšie ako 1"}, "ACOT": {"a": "(number)", "d": "Vráti hodnotu arkuskotangensu čísla, v radiánoch v intervale od 0 do pí", "ad": "je kotangens požadovan<PERSON><PERSON>"}, "ACOTH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON>ti inverzný hyperbolický kotangens čísla", "ad": "je hyperbolický kotangens požadovaného <PERSON>"}, "AGGREGATE": {"a": "(č<PERSON><PERSON>_funkcie; možnosti; odk1; ...)", "d": "Vráti agregovanú hodnotu v zozname alebo v databáze", "ad": "je číslo od 1 do 19, ktoré určuje súhrnnú funkciu pre agregovanú hodnotu.!je číslo od 0 do 7, ktoré určuje hodnoty ignorované pri výpočte agregovanej hodnoty!je pole alebo rozsah číselných údajov pre výpočet agregovanej hodnoty!označuje pozíciu v poli; ide o k-tu najväčšiu hodnotu, k-tu najmen<PERSON>iu hodnotu, k-ty percentil alebo k-ty kvartil.!je číslo od 1 do 19, ktoré určuje súhrnnú funkciu pre agregovanú hodnotu.!je číslo od 0 do 7, ktoré určuje hodnoty ignorované pri výpočte agregovanej hodnoty!je 1 až 253 rozsahov alebo odkazov, pre ktoré chcete vypočítať agregovanú hodnotu"}, "ARABIC": {"a": "(text)", "d": "Konvertuje rímske číslice na arabské", "ad": "je r<PERSON><PERSON><PERSON> čís<PERSON>, k<PERSON><PERSON> ch<PERSON>te konvertovať"}, "ASC": {"a": "(text)", "d": "Pre jazyky s dvojbajtovými tabuľ<PERSON><PERSON> (DBCS) sa menia znaky s plnou šírkou (dvojbajtové) na znaky s polovičnou šírkou (jednobajtové)", "ad": "je text, ktorý chcete zmeniť"}, "ASIN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vráti arkus sínus čísla v radiánoch v intervale -pí/2 až pí/2", "ad": "je hodnota sínusu <PERSON><PERSON><PERSON>, k<PERSON>á musí byť v intervale -1 až 1"}, "ASINH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu hyperbolického arkus sínusu <PERSON>", "ad": "je ľ<PERSON><PERSON><PERSON><PERSON><PERSON>, ktoré sa rovná alebo je väčšie ako 1"}, "ATAN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vráti arkus tangens čísla v radiánoch v intervale -pí/2 až pí/2", "ad": "je tangens dan<PERSON><PERSON>"}, "ATAN2": {"a": "(x_č<PERSON>lo; y_č<PERSON>lo)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu arkus tangens danej x-ovej a y-ovej súradnice. Výsledok je v radiánoch v rozsahu od -pí do pí okrem hodnoty -pí", "ad": "je x-ová súradnica bodu!je y-ová súradnica bodu"}, "ATANH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu hyperbolického arkustangens čísla", "ad": "je ľubovoľné číslo v intervale -1 až 1, s výnimkou čísel -1 a 1"}, "BASE": {"a": "(number; radix; [min_length])", "d": "Konvertuje číslo na textové vyjadrenie s daným základom sústavy (základ)", "ad": "je <PERSON><PERSON><PERSON>, k<PERSON><PERSON> chcete konvertovať!z<PERSON><PERSON> s<PERSON>, na ktorý chcete skonvertovať číslo!je minimálna dĺžka vráteného reťazca. Ak sa vynechá, počiatočné nuly sa nepridajú"}, "CEILING": {"a": "(číslo; významnosť)", "d": "Zaokrúhli číslo smerom nahor na najbližší násobok zadanej hodnoty", "ad": "je ho<PERSON><PERSON>, ktor<PERSON> chcete zaokrúhliť!je násobok, na ktorý chcete číslo zaokrúhliť"}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "Zaokrúhli číslo nahor na najbližšie celé číslo alebo na najbližší násobok zadanej hodnoty", "ad": "je hodnota, k<PERSON><PERSON> chcete zaokrúhliť!je násobok, na ktorý chcete číslo zaokrúhliť!keď je zadaná nenulov<PERSON> hodnota, táto funkcia zaokrúhli číslo smerom od nuly"}, "CEILING.PRECISE": {"a": "(č<PERSON>lo; [významnosť])", "d": "<PERSON><PERSON><PERSON><PERSON>lo zaokrúhlené na najbližšie celé číslo alebo na najbližší násobok zadanej hodnoty", "ad": "je ho<PERSON><PERSON>, ktor<PERSON> chcete zaokrúhliť!je násobok, na ktorý chcete číslo zaokrúhliť"}, "COMBIN": {"a": "(počet; vybratý_počet)", "d": "Vráti počet kombinácií pre zadaný počet položiek", "ad": "je celkový počet položiek!je počet položiek v každej kombinácii"}, "COMBINA": {"a": "(number; number_chosen)", "d": "Vráti počet kombinácií s opakovaniami pre daný počet položiek", "ad": "je celkový počet položiek!je počet položiek v jednotlivých kombináciách"}, "COS": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON>", "ad": "je uhol v radiánoch, ktorého kosínus chcete zistiť"}, "COSH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hyperbolický kosínus č<PERSON>", "ad": "je ľ<PERSON><PERSON><PERSON><PERSON><PERSON> reálne <PERSON>"}, "COT": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>la", "ad": "je uhol v radiánoch, ktorého kosínus chcete zistiť"}, "COTH": {"a": "(nuber)", "d": "V<PERSON><PERSON>ti hyperbolický kotangens čísla", "ad": "je uhol v radiánoch, ktorého hyperbolický kotangens chcete zistiť"}, "CSC": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "ad": "je uhol v radiánoch, ktorého kosekans chcete zistiť"}, "CSCH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON> hyper<PERSON>k<PERSON> kosekans uhla", "ad": "je uhol v radiánoch, ktorého hyperbolický kosekans chcete zistiť"}, "DECIMAL": {"a": "(number; radix)", "d": "Konvertuje textové vyjadrenie čísla v danom základe na desatinné číslo", "ad": "je <PERSON><PERSON><PERSON>, ktoré ch<PERSON>te skonvertovať!je základ s<PERSON>ta<PERSON>, ktoré konvertujete"}, "DEGREES": {"a": "(uhol)", "d": "Konvertuje radiány na stupne", "ad": "je uhol v radiánoch, ktorý chcete konvertovať"}, "ECMA.CEILING": {"a": "(číslo; významnosť)", "d": "Zaokrúhli číslo smerom nahor na najbližší násobok zadanej hodnoty", "ad": "je ho<PERSON><PERSON>, ktor<PERSON> chcete zaokrúhliť!je násobok, na ktorý chcete číslo zaokrúhliť"}, "EVEN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Zaokrúhli kladné číslo nahor a záporné číslo smerom nadol na najbližšie párne celé číslo", "ad": "je hodn<PERSON>, k<PERSON><PERSON>te zaokrúhli<PERSON>"}, "EXP": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vráti e (základ prirodzeného logaritmu) umocnené na zadané číslo", "ad": "je exponent pou<PERSON><PERSON><PERSON> na základ e. Konštanta e je základ prirodzeného logaritmu a má hodnotu 2,71828182845904"}, "FACT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> faktori<PERSON>l čísla. Výsledok sa rovná hodnote 1*2*3*...* Č<PERSON>lo", "ad": "je ne<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> faktor<PERSON><PERSON><PERSON>te v<PERSON>počítať"}, "FACTDOUBLE": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> fak<PERSON><PERSON><PERSON>", "ad": "je ho<PERSON>, k<PERSON><PERSON> d<PERSON> faktori<PERSON>l chcete zís<PERSON>"}, "FLOOR": {"a": "(číslo; významnosť)", "d": "Zaokrúhli číslo nadol na najbližší násobok zadanej hodnoty", "ad": "je č<PERSON><PERSON><PERSON> hodnota, ktor<PERSON> chcete zaokrúhliť!je násobok, na ktorý chcete číslo zaokrúhliť. Argumenty Číslo a Významnosť musia byť oba buď kladné, alebo záporné č<PERSON>la"}, "FLOOR.PRECISE": {"a": "(č<PERSON>lo; [významnosť])", "d": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> je zaokrúhlené nadol na najbližšie celé číslo alebo na násobok zadanej hodnoty", "ad": "je ho<PERSON><PERSON>, ktor<PERSON> chcete zaokrúhliť!je násobok, na ktorý chcete číslo zaokrúhliť"}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "Zaokrúhli číslo nadol na najbližšie celé číslo alebo na najbližší násobok zadanej hodnoty", "ad": "je hodnota, k<PERSON><PERSON> chcete zaokrúhliť!je násobok, na ktorý chcete hodnotu zaokrúhliť!pri zadaní nenulovej hodnoty bude táto funkcia zaokrúhľovať smerom k nule"}, "GCD": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON>ti najv<PERSON>čší spoločný deliteľ", "ad": "je 1 až 255 ho<PERSON><PERSON>t"}, "INT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Zaokrúhli číslo nadol na najbližšie celé číslo", "ad": "je re<PERSON><PERSON><PERSON>, k<PERSON><PERSON>te zaokrúhliť nadol na celé číslo"}, "ISO.CEILING": {"a": "(č<PERSON>lo; [významnosť])", "d": "Vr<PERSON>ti č<PERSON>lo zaokrúhlené na najbližšie celé číslo alebo na najbližší násobok zadanej hodnoty. Číslo sa bez ohľadu na znamienko zaokrúhli nahor. Ak je však dané číslo alebo násobok nula, vr<PERSON>ti sa nula.", "ad": "je ho<PERSON><PERSON>, ktor<PERSON> chcete zaokrúhliť!je násobok, na ktorý chcete číslo zaokrúhliť"}, "LCM": {"a": "(číslo1; [číslo2]; ...)", "d": "Vráti najmenší spoločný násobok", "ad": "je 1 až <PERSON>, za k<PERSON><PERSON> chcete zistiť najmenší spoločný násobok"}, "LN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> prirodzený logaritmus daného <PERSON>", "ad": "je klad<PERSON><PERSON>, k<PERSON><PERSON>ho prirodzený logaritmus chcete zistiť"}, "LOG": {"a": "(<PERSON><PERSON><PERSON>; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON>ti logaritmus čísla pri určenom základe", "ad": "je klad<PERSON><PERSON>, k<PERSON><PERSON>ho logaritmus chcete zistiť!je základ logaritmu. Ak tento argument nezad<PERSON>, bude jeho hodnota 10"}, "LOG10": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON>ý logaritmus čísla", "ad": "je k<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>ý logaritmus chcete zistiť"}, "MDETERM": {"a": "(pole)", "d": "<PERSON><PERSON><PERSON><PERSON> determinant matice poľa", "ad": "je č<PERSON><PERSON><PERSON>é pole s rovnak<PERSON><PERSON> po<PERSON><PERSON> riadkov a stĺpcov predstavujúce rozsah bunky alebo konštantu poľa"}, "MINVERSE": {"a": "(pole)", "d": "Vráti inverznú maticu matice uloženej v poli", "ad": "je č<PERSON><PERSON><PERSON>é pole s rovnak<PERSON><PERSON> po<PERSON><PERSON> riadkov a stĺpcov predstavujúce rozsah bunky alebo konštantu poľa"}, "MMULT": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> mat<PERSON> so s<PERSON><PERSON><PERSON><PERSON> d<PERSON> polí, k<PERSON><PERSON> obsahuje rovnaký počet riadkov ako pole1 a rovnaký počet stĺpcov ako pole2", "ad": "je prvé pole <PERSON>, ktor<PERSON> chcete násobiť. Počet stĺpcov musí byť rovnaký ako počet riadkov Poľa2"}, "MOD": {"a": "(č<PERSON>lo; deliteľ)", "d": "Vráti zvyšok po delení čísla deliteľom", "ad": "je <PERSON><PERSON><PERSON>, pre ktoré chcete nájsť zvyšok po delení!je č<PERSON><PERSON>, ktorým chcete deliť dan<PERSON>"}, "MROUND": {"a": "(číslo; násobok)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu zaokrúhlenú na požadovaný násobok", "ad": "je ho<PERSON><PERSON>, ktor<PERSON> chcete zaokrúhliť!je násobok, na ktorý chcete číslo zaokrúhliť"}, "MULTINOMIAL": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> polyno<PERSON> množinu čísel", "ad": "je 1 až <PERSON> ho<PERSON>, k<PERSON><PERSON><PERSON> polynomickú hodnotu chcete zistiť"}, "MUNIT": {"a": "(dimension)", "d": "<PERSON><PERSON><PERSON><PERSON> mat<PERSON>u j<PERSON> pre zadanú dimenziu", "ad": "je celé <PERSON> u<PERSON> dimenziu matice j<PERSON>, k<PERSON><PERSON> má funkcia vrátiť"}, "ODD": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Zaokrúhli kladné číslo nahor a záporné číslo nadol na najbližšie nepárne celé číslo", "ad": "je hodn<PERSON>, k<PERSON><PERSON>te zaokrúhli<PERSON>"}, "PI": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu pí s presnosťou na 15 číslic. Výsledkom je hodnota 3,14159265358979", "ad": ""}, "POWER": {"a": "(<PERSON><PERSON><PERSON>; mocnina)", "d": "Umocní číslo na zadanú mocninu", "ad": "je základ mocnin<PERSON>, ľubovoľné reálne číslo!je exponent, na ktorý chcete základ umocniť"}, "PRODUCT": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vynásobí všetky čísla zadané ako argumenty", "ad": "je 1 až <PERSON>, <PERSON><PERSON><PERSON><PERSON> hodnôt alebo čísel v textovom formáte, ktor<PERSON> chcete vynásobiť"}, "QUOTIENT": {"a": "(čitate<PERSON>; menovateľ)", "d": "<PERSON><PERSON><PERSON><PERSON>íselnú časť delenia", "ad": "je delenec!je deliteľ"}, "RADIANS": {"a": "(uhol)", "d": "Konvertuje stupne na radiány", "ad": "je uhol v stupňoch, ktorý chcete konvertovať"}, "RAND": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON>, ktoré je väčšie alebo rovné 0 a menšie než 1 (zmení sa pri každom prepočítaní)", "ad": ""}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "<PERSON><PERSON><PERSON><PERSON> náhodných čísiel", "ad": "počet riadkov vo vrátenom poli!počet stĺpcov vo vrátenom poli!minimálny počet, ktorý požadujete vrátiť!maximálny počet, ktorý požadujete vrátiť!vrátiť celé číslo alebo desatinnú hodnotu. TRUE pre celé číslo, FALSE pre desatinné číslo"}, "RANDBETWEEN": {"a": "(najn<PERSON><PERSON><PERSON><PERSON>; najvyššie)", "d": "<PERSON><PERSON><PERSON><PERSON> náhodne vybraté číslo medzi zadanými číslami", "ad": "je najmen<PERSON>ie celé <PERSON>, ktoré vráti funkcia RANDBETWEEN!je najväčšie celé č<PERSON>, ktoré vráti funkcia RANDBETWEEN"}, "ROMAN": {"a": "(číslo; [forma])", "d": "Konvertuje číslo napísané arabskými číslicami na rímske číslice v textovom formáte", "ad": "je číslo nap<PERSON>é a<PERSON>skými číslicami, ktoré chcete konvertovať!je číslo určujúce typ požadovaných rímskych číslic."}, "ROUND": {"a": "(č<PERSON>lo; počet_číslic)", "d": "Zaokrúhli číslo na daný počet číslic", "ad": "je <PERSON><PERSON><PERSON>, ktoré chcete zaokrúhliť!je počet č<PERSON>, na ktoré chcete dané č<PERSON>lo zaokrúhliť. Ak zadáte záporné číslo, bude dané č<PERSON>lo zaokrúhlené doľava od desatinnej čiarky. Ak je hodnota argumentu 0, bude dané číslo zaokrúhlené na najbližšie celé číslo"}, "ROUNDDOWN": {"a": "(č<PERSON>lo; počet_číslic)", "d": "Zaokrúhli číslo smerom nadol k nule", "ad": "je ľubovoľné reálne <PERSON>, ktoré chcete zaokrúhliť nadol!je počet č<PERSON>, na ktoré chcete číslo zaokrúhliť. Ak má tento argument zápornú hodnotu, bude dané <PERSON>lo zaokrúhlené naľavo od desatinnej čiarky. A sa jeho hodnota rovná nule alebo nie je zadaná, bude dané číslo zaokrúhlené na najbližšie celé číslo"}, "ROUNDUP": {"a": "(č<PERSON>lo; počet_číslic)", "d": "Zaokrú<PERSON><PERSON>, smerom od nuly", "ad": "je ka<PERSON><PERSON><PERSON> re<PERSON>, ktoré chcete zaokrúhliť nahor!je počet č<PERSON>, na ktoré chcete číslo zaokrúhliť. Ak má tento argument zápornú hodnotu, bude dané č<PERSON>lo zaokrúhlené naľavo od desatinnej čiarky. Ak sa jeho hodnota rovná nule alebo nie je zadaná, bude dané číslo zaokrúhlené na najbližšie celé číslo"}, "SEC": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON>", "ad": "je uhol v radiánoch, ktorého sekans chcete zistiť"}, "SECH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON> hyper<PERSON> se<PERSON>la", "ad": "je uhol v radiánoch, ktorého hyperbolický sekans chcete zistiť"}, "SERIESSUM": {"a": "(x; n; m; koe<PERSON>y)", "d": "<PERSON><PERSON>áti súčet mocninových radov na základe vzorca", "ad": "je vstupná hodnota mocninového radu!je úvodná mocnina, ktorou chcete umocniť x!je krok, o ktorý chcete zvýšiť hodnotu parametra n každého člena radu!je mno<PERSON><PERSON> koe<PERSON>, ktorou sa vynásobí každá nasledujúca mocnina x"}, "SIGN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vráti znamienko čísla: 1 pri kladnom čísle, 0 pri nule alebo -1 pri zápornom čísle", "ad": "je ľ<PERSON><PERSON><PERSON><PERSON><PERSON> reálne <PERSON>"}, "SIN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON>", "ad": "je uhol v radiánoch, ktorého sínus chcete zistiť. Stupne * PI()/180 = radiány"}, "SINH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hyper<PERSON>k<PERSON> sínus <PERSON>", "ad": "je ľ<PERSON><PERSON><PERSON><PERSON><PERSON> reálne <PERSON>"}, "SQRT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> d<PERSON> odmocninu čís<PERSON>", "ad": "je <PERSON><PERSON>, k<PERSON><PERSON><PERSON> dru<PERSON> odmocninu chcete zistiť"}, "SQRTPI": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> (číslo * pí)", "ad": "je <PERSON><PERSON>, k<PERSON><PERSON>m sa násobí parameter p"}, "SUBTOTAL": {"a": "(č<PERSON><PERSON>_funkcie; odk1; ...)", "d": "Vráti medzisúčet v zozname alebo v databáze", "ad": "je číslo 1 až 11, k<PERSON><PERSON> určuje typ súhrnnej funkcie použitej pre výpočet medzisúčtu.!je 1 až 254 rozsahov alebo od<PERSON><PERSON>v, k<PERSON><PERSON><PERSON> medzisúčet chcete zistiť"}, "SUM": {"a": "(číslo1; [číslo2]; ...)", "d": "Spočíta všetky čísla v rozsahu buniek", "ad": "je 1 až <PERSON>, k<PERSON><PERSON> ch<PERSON>te spočí<PERSON>. Logické hodnoty a text sa v bunk<PERSON>ch ignorujú, ak budú zadané ako argumenty, tak sa zahrnú"}, "SUMIF": {"a": "(rozsah; kritéri<PERSON>; [rozsah_súhrnu])", "d": "Spočíta bunky vybraté podľa zadanej podmienky alebo kritéria", "ad": "je roz<PERSON><PERSON> buniek, ktor<PERSON> chcete vyhodnotiť!je podmienka alebo kritérium vo forme čísla, výrazu alebo textu, ktorý určuje bunky na sčítanie!sú skutočné bunky, ktoré sa sčítajú. Ak tento argument nezadáte, použije sa daný rozsah buniek"}, "SUMIFS": {"a": "(rozsah_súčtu; rozsah_kritérií; kritériá; ...)", "d": "Pripočíta bunky určené podľa zadanej množiny podmienok alebo kritérií", "ad": "s<PERSON> skutoč<PERSON><PERSON> bunky, ktor<PERSON> sa majú spočítať.!je roz<PERSON><PERSON> buni<PERSON>, ktoré chcete hodnotiť podľa konkrétnej podmienky!je podmienka alebo kritérium vo forme čísla, výrazu alebo textu definuj<PERSON><PERSON><PERSON> bunky, ktor<PERSON> chcete pripočí<PERSON>"}, "SUMPRODUCT": {"a": "(pole1; [pole2]; [pole3]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> súčinov zodpovedajúcich rozsahov alebo polí", "ad": "je 2 až <PERSON> polí, k<PERSON><PERSON> chcete vynásobiť a tieto položky sčítať. Všetky polia musia byť rovnakého typu"}, "SUMSQ": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> druhých mocnín argumentov. Argumentmi mô<PERSON>u byť čísla, polia, názvy alebo odkazy na bunky obsahujúce čísla", "ad": "je 1 až <PERSON>, pol<PERSON>, názvov alebo odkazov na polia, pre ktoré chcete zistiť súčet druhých mocnín"}, "SUMX2MY2": {"a": "(pole_x; pole_y)", "d": "Vypočíta sú<PERSON> rozdielov druh<PERSON>ch mocnín hodn<PERSON>t dvoch zodpovedajúcich rozsahov alebo polí", "ad": "je prvý rozsah alebo pole čísel, ktor<PERSON><PERSON> môže byť číslo alebo názov, pole alebo odkaz obsahujúci čísla!je druhý rozsah alebo pole čísel, ktorým môže byť číslo alebo názov, pole alebo odkaz obsahujúci čísla"}, "SUMX2PY2": {"a": "(pole_x; pole_y)", "d": "<PERSON><PERSON><PERSON>ti celkový súčet súčtov druhých mocnín čísel v dvoch zodpovedajúcich rozsahoch alebo poliach", "ad": "je prvý rozsah alebo pole čísel, ktor<PERSON><PERSON> môže byť číslo alebo názov, pole alebo odkaz obsahujúci čísla!je druhý rozsah alebo pole čísel, ktorým môže byť číslo alebo názov, pole alebo odkaz obsahujúci čísla"}, "SUMXMY2": {"a": "(pole_x; pole_y)", "d": "Vypočíta súč<PERSON> druhých mocnín roz<PERSON>lov hodn<PERSON>t dvoch zodpovedajúcich rozsahov alebo polí", "ad": "je prvý rozsah alebo pole hodnôt. <PERSON><PERSON><PERSON><PERSON> to byť číslo alebo názov, pole alebo odkaz obsahujúci čísla!je druhý rozsah alebo pole hodnôt. <PERSON><PERSON><PERSON><PERSON> to byť číslo alebo názov, pole alebo odkaz obsahujúci čísla"}, "TAN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> tangens uhla", "ad": "je uhol v radiánoch, ktorého tangens chcete zistiť. Stupne * PI()/180 = radiány"}, "TANH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hyperbolický tangens čísla", "ad": "je ľ<PERSON><PERSON><PERSON><PERSON><PERSON> reálne <PERSON>"}, "TRUNC": {"a": "(číslo; [počet_číslic])", "d": "Skráti číslo na celé číslo odstránením desatinnej alebo zlomkovej časti čísla", "ad": "je <PERSON><PERSON><PERSON>, k<PERSON><PERSON> chcete skrátiť!je číslo určujúce presnosť skrátenia. Ak tento argument ne<PERSON>, bude jeho hodnota 0"}, "ADDRESS": {"a": "(číslo_riadka; číslo_stĺpca; [abs_číslo]; [a1]; [text_hárka])", "d": "Po zadaní čísla riadka a stĺpca vytvorí textový odkaz na bunku", "ad": "je číslo riadka použité v odkaze na bunku: Číslo_riadka = 1 pre riadok číslo 1!je číslo stĺpca použité v odkaze na bunku: Číslo_stĺpca = 4 pre stĺpec D!určuje typ odkazu: absolútny = 1; absolútny riadok/relatívny stĺpec = 2; relatívny riadok/absolútny stĺpec = 3; relatívny = 4!je logická hodnota určujúca typ odkazu: typ A1 = 1 alebo TRUE; typ R1C1 = 0 alebo FALSE!je text špecifikuj<PERSON><PERSON> hárok (jeho názov), ktorý sa má použiť ako externý odkaz"}, "CHOOSE": {"a": "(index_číslo; hodnota1; [hodnota2]; ...)", "d": "Zo zoznamu hodnôt zvolí na základe zadaného čísla hodnotu alebo akciu, ktor<PERSON> sa má vykonať", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> hodnota argumentu bola vybratá. Hodnota argumentu Index_číslo musí byť medzi 1 a 254, alebo vzorec či odkaz na číslo medzi 1 a 254!je 1 až 254 hodn<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> na bunky, def<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, funkci<PERSON> alebo text<PERSON> argumentov, ktor<PERSON> sú hodnotami funkcie CHOOSE"}, "COLUMN": {"a": "([odkaz])", "d": "<PERSON><PERSON><PERSON><PERSON> stĺpca odkazu", "ad": "je bunka alebo sú<PERSON><PERSON><PERSON> r<PERSON><PERSON>, ktorej alebo ktorého číslo stĺpca chcete zistiť. Ak tento argument nezadáte, použije sa bunka obsahujúca funkciu COLUMN"}, "COLUMNS": {"a": "(pole)", "d": "Vráti počet stĺpcov v poli alebo odkaze", "ad": "je pole, vzorec poľa alebo odkaz na rozsah buniek, pre ktorý chcete zistiť počet stĺpcov"}, "FORMULATEXT": {"a": "(reference)", "d": "Vráti vzorec ako reťazec", "ad": "je odkaz na vzorec"}, "HLOOKUP": {"a": "(vyhľadávaná_hodnota; pole_tabuľky; číslo_indexu_riadka; [vyhľadávanie_rozsah])", "d": "Prehľadá horný riadok tabuľky alebo pole hodnôt a vr<PERSON>ti hodnotu zo zadaného riadka obsiahnutú v rovnakom stĺpci", "ad": "je hodnota, k<PERSON><PERSON> chcete vyhľadať v prvom riadku tabuľky. <PERSON><PERSON><PERSON><PERSON> to byť hodnota, odkaz alebo textový reťazec!je prehľadávaná tabuľka obsahujúca text, čísla alebo logické hodnoty. Argument Pole_tabuľky môže byť odkaz na rozsah alebo názov rozsahu!je číslo riadka v argumente Pole_tabuľky, z ktorého sa vráti zodpovedajúca hodnota. Prvý riadok hodnôt tabuľky je riadok číslo 1!je logická hodnota: nájsť najbližšiu zodpovedajúcu hodnotu v hornom riadku (hodnoty sú zoradené vzostupne) = TRUE alebo nie je zadaná; nájsť presne zodpovedajúcu hodnotu = FALSE"}, "HYPERLINK": {"a": "(umiestnenie_prepojenia; [priateľ<PERSON><PERSON>_názov])", "d": "Vytvorí odkaz alebo skok, ktorý otvorí dokument uložený na pevnom disku, sieťovom serveri alebo na Internete", "ad": "je text určujúci cestu a názov súboru s dokumentom, ktor<PERSON> chcete otvoriť, umiestnenie na pevnom disku, adresa UNC alebo cesta URL!je text alebo číslo, ktoré sa zobrazí v bunke. Ak ho nezadáte, v bunke sa zobrazí text prepojenia"}, "INDEX": {"a": "(pole; číslo_riadka; [číslo_stĺpca]!odkaz; číslo_riadka; [číslo_stĺpca]; [číslo_plochy])", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu alebo odkaz na bunku v určitom riadku a stĺpci v danom rozsahu", "ad": "je rozsah buniek alebo konštanta poľa.!vyberie riadok v argumente Pole alebo Odkaz, z ktorého sa vráti hodnota. Ak tento argument vynecháte, je nutné zadať argument Číslo_stĺpca!vyberie stĺpec v argumente Pole alebo Odkaz, z ktorého sa vráti hodnota. Ak tento argument vynecháte, je nutné zadať argument Číslo_riadka!je odkaz na jeden alebo viac rozsahov buniek!vyberie riadok v argumente Pole alebo Odkaz, z ktorého sa vráti hodnota. Ak tento argument vynecháte, je nutné zadať argument Číslo_stĺpca!vyberie stĺpec v argumente Pole alebo Odkaz, z ktorého sa vráti hodnota. Ak tento argument vynecháte, je nutné zadať argument Číslo_riadka!vyberie rozsah v argumente Odkaz, z ktorého sa vráti hodnota. Prvá vybratá alebo zadaná oblasť je oblasť číslo 1, d<PERSON><PERSON>á oblasť je oblasť číslo 2, atď."}, "INDIRECT": {"a": "(text_odkazu; [a1])", "d": "Vráti odkaz určený textovým reťazcom", "ad": "je odkaz na bunku obsahujúcu odkaz na štýl A1 alebo R1C1, názov definovaný ako odkaz alebo odkaz na bunku ako textový reťazec!je logická hodnota určujúca typ odkazu v argumente Text_odkazu: štýl R1C1 = FALSE; štýl A1 = TRUE alebo nie je zadaná"}, "LOOKUP": {"a": "(vyhľad<PERSON>vaná_hodnota; vektor_vyhľadávania; [vektor_v<PERSON><PERSON><PERSON><PERSON>]!vyhľadávaná_hodnota; pole)", "d": "Vyhľadá hodnotu z poľa alebo z rozsahu jedného riadka alebo jedného stĺpca. Funkcia je poskytovaná s cieľom spätnej kompatibility", "ad": "je hodnota vyhľadávaná v argumente vektor_vyhľadávania. Môže to byť číslo, text, logická hodnota, názov alebo odkaz na hodnotu!je rozsah pozostávajúci z jedného riadka alebo stĺpca, v ktorom je vzostupne usporiadaný text, čísla alebo logické hodnoty!je rozsah pozostávajúci z jedného riadka alebo stĺpca, ktorý má rovnakú veľkosť ako veľkosť argumentu vektor_vyhľadávania!je hodnota vyhľadávaná v argumente pole pomocou funkcie LOOKUP. Môže to byť číslo, text, logická hodnota, názov alebo odkaz na hodnotu!je rozsah buniek obsahujúcich text, číslo alebo logické hodnoty, ktoré chcete porovnať s hodnotami argumentu Vyhľadávaná_hodnota"}, "MATCH": {"a": "(vyhľadávaná_hodnota; pole_vyhľadávania; [typ_zhody])", "d": "Vráti relatívnu pozíciu položky poľa, ktorá zodpovedá danej hodnote v danom poradí", "ad": "je hodnota, ktor<PERSON> sa použije na vyhľadanie požadovanej hodnoty v poli. Môž<PERSON> to byť číslo, text, logick<PERSON> hodnota, názov alebo odkaz na hodnotu!je súvislý rozsah buniek obsahujúci hľadané hodnoty, pole hodnôt alebo odkaz na pole!je číslo 1, 0 alebo -1, k<PERSON><PERSON>, ak<PERSON> hodnota bude vrátená."}, "OFFSET": {"a": "(odkaz; riadky; stĺpce; [výška]; [šírka])", "d": "Vráti odkaz na rozsah, ktorý predstavuje určený počet riadkov a stĺpcov z daného odkazu", "ad": "je odkaz na bunku alebo súvislý rozsah buniek, od ktorej chcete zmeniť odsadenie!je počet riadkov nahor alebo nadol, na ktoré má ľavá horná bunka výsledku odkazovať!je počet stĺpcov vpravo alebo vľavo, na ktoré má ľavá horná bunka výsledku odkazovať!je požadovaná výška výsledku vyjadrená počtom riadkov. Ak tento argument nezadáte, bude jeho hodnota rovnaká ako hodnota argumentu Odkaz!je požadovaná šírka výsledku vyjadrená počtom stĺpcov. Ak tento argument nezadáte, bude jeho hodnota rovnaká ako hodnota argumentu Odkaz"}, "ROW": {"a": "([odkaz])", "d": "<PERSON><PERSON><PERSON><PERSON> riadka od<PERSON>", "ad": "je bunka alebo jeden r<PERSON><PERSON>, pre ktor<PERSON> chcete zistiť číslo riadka. Ak tento argument nezadá<PERSON>, vr<PERSON><PERSON> bunku obsahujúcu funkciu ROW"}, "ROWS": {"a": "(pole)", "d": "Vráti počet riadkov v odkaze alebo poli", "ad": "je pole, vzorec poľa alebo odkaz na rozsah buniek, pre ktor<PERSON> chcete zistiť počet riadkov"}, "TRANSPOSE": {"a": "(pole)", "d": "Konvertuje vodorovný rozsah buniek na zvislý alebo naopak", "ad": "je roz<PERSON>h buniek hárka alebo pole hodn<PERSON>, k<PERSON><PERSON> ch<PERSON>te transponovať"}, "UNIQUE": {"a": "(pole; [podľa_stĺpca]; [presne_raz])", "d": "<PERSON><PERSON><PERSON><PERSON> jedinečné hodnoty v danom rozsahu alebo poli.", "ad": "roz<PERSON>h alebo pole, z ktorého sa majú vrátiť jedinečné riadky alebo stĺpce!je logická hodnota: porovnajte riadky medzi sebou a vráťte jedinečné riadky = FALSE alebo vynechané; porovnajte stĺpce medzi sebou a vráťte jedinečné stĺpce = TRUE!je logická hodnota: vráťte riadky alebo stĺpce, ktoré sa vyskytujú presne raz z poľa = TRUE; vráťte všetky odlišné riadky alebo stĺpce z poľa = FALSE alebo vynechané"}, "VLOOKUP": {"a": "(vyhľadávan<PERSON>_hodnota; pole_tabuľky; číslo_indexu_stĺpca; [vyhľadávanie_rozsahu])", "d": "Hľadá hodnotu v ľavom krajnom stĺpci tabuľky a vr<PERSON>ti hodnotu zo zadaného stĺpca v tom istom riadku. Predvolené zoradenie tabuľky je vzostupné", "ad": "je hodnota hľadaná v prvom stĺpci tabuľky. M<PERSON><PERSON><PERSON> to byť hodnota, odkaz alebo textový reťazec!je prehľadávaná tabuľka s textom, číslami alebo logickými hodnotami. Argument Pole_tabuľky môže byť odkaz na rozsah alebo názov rozsahu!je číslo stĺpca v argumente Pole_tabuľky, z ktorého sa má vrátiť zodpovedajúca hodnota. Prvý stĺpec hodnôt v tabuľke je stĺpec číslo 1!je logická hodnota: nájsť najbližšiu zodpovedajúcu hodnotu v prvom stĺpci (zoradenom vzostupne) = TRUE alebo nie je zadaná; nájsť presne zodpovedajúcu hodnotu = FALSE"}, "XLOOKUP": {"a": "(vyhľad<PERSON>vaná_hodnota; pole_vyhľadávania; pole_vrátenia; [ak_sa_ne<PERSON><PERSON>]; [rež<PERSON>_z<PERSON><PERSON>]; [režim_vyhľadávania])", "d": "V danom rozsahu alebo poli hľadá zhodu a vráti zodpovedajúcu položku z druhého rozsahu alebo poľa. Predvolene sa použije presná zhoda", "ad": "je hľadaná hodnota!je pole alebo rozsah, ktorý sa prehľadáva!je pole alebo rozsah, ktor<PERSON> sa majú vrátiť!vr<PERSON><PERSON>á, ak sa nenašla žiadna zhoda!ur<PERSON><PERSON><PERSON>, ako sa má zhodovať vyhľadávaná_hodnota vzhľadom na hodnoty v poli_vyhľadávania!určuje režim vyhľadávania, ktorý sa má použiť. Predvolene sa použije prehľadávanie od prvej po poslednú položku"}, "CELL": {"a": "(typ_informácií; [odkaz])", "d": "<PERSON><PERSON><PERSON><PERSON> o formátovaní, umiestnení alebo obsahu bunky", "ad": "text<PERSON> ho<PERSON>, ktorá určuje typ požadovanej informácie!bunka, o ktorej chcete získať informácie"}, "ERROR.TYPE": {"a": "(hodnota_chyby)", "d": "<PERSON><PERSON><PERSON><PERSON>odpovedajúce chybovej hodnote.", "ad": "je chybová hodnota, ktorej identifikačné číslo chcete zistiť. Môže to byť skutočná chybová hodnota alebo odkaz na bunku obsahujúcu chybovú hodnotu"}, "ISBLANK": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je argument odkazom na prázdnu bunku a vráti hodnotu TRUE alebo FALSE", "ad": "je testovaná bunka alebo názov oz<PERSON>ču<PERSON><PERSON><PERSON> bunku, k<PERSON><PERSON> testova<PERSON>"}, "ISERR": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je hodnota chybou inou ako #NEDOSTUPNÝ a vr<PERSON>ti hodnotu TRUE alebo FALSE", "ad": "je hodnota, k<PERSON><PERSON>te testovať. Hodnota sa môže vzťahovať na bunku, vzorec alebo názov odkazujúci na bunku, vzorec alebo hodnotu"}, "ISERROR": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je hodnota chybou a vr<PERSON>ti hodnotu TRUE alebo FALSE", "ad": "je hodnota, k<PERSON><PERSON>te testovať. Hodnota sa môže vzťahovať na bunku, vzorec alebo názov odkazujúci na bunku, vzorec alebo hodnotu"}, "ISEVEN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu TRUE, ak je číslo párne", "ad": "je hodn<PERSON>, k<PERSON><PERSON>te testova<PERSON>"}, "ISFORMULA": {"a": "(reference)", "d": "Overí, či odkaz smeruje na bunku obsahujúcu vzorec a vráti hodnotu TRUE alebo FALSE", "ad": "je odkaz na bunku, k<PERSON><PERSON> chcete testovať. Odkazom môže byť odkaz na bunku, vzorec alebo názov, ktorý odkazuje na bunku"}, "ISLOGICAL": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je argument logickou hodnotou (TRUE alebo FALSE) a vr<PERSON>ti hodnotu TRUE alebo FALSE", "ad": "je hodnota, k<PERSON><PERSON>te testovať. Hodnota sa môže vzťahovať na bunku, vzorec alebo názov odkazujúci na bunku, vzorec alebo hodnotu"}, "ISNA": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či hodnota je #NEDOSTUPNÝ a vr<PERSON>ti hodnotu TRUE alebo FALSE", "ad": "je hodnota, k<PERSON><PERSON>te testovať. Hodnota sa môže vzťahovať na bunku, vzorec alebo názov odkazujúci na bunku, vzorec alebo hodnotu"}, "ISNONTEXT": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či argument nie je textovou hodnotou (prázdne bunky nie sú text) a vr<PERSON><PERSON> hodnotu TRUE alebo FALSE", "ad": "<PERSON>e ho<PERSON><PERSON>, k<PERSON><PERSON> chcete testovať: bunka; vzorec; alebo názov odkazujúci na bunku, vzorec alebo hodnotu"}, "ISNUMBER": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je argument číselnou hodnotou a vr<PERSON>ti hodnotu TRUE alebo FALSE", "ad": "je hodnota, k<PERSON><PERSON>te testovať. Hodnota sa môže vzťahovať na bunku, vzorec alebo názov odkazujúci na bunku, vzorec alebo hodnotu"}, "ISODD": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu TRUE, ak je číslo nepárne", "ad": "je hodn<PERSON>, k<PERSON><PERSON>te testova<PERSON>"}, "ISREF": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je argument odkazom a vr<PERSON><PERSON> hodnotu TRUE alebo FALSE", "ad": "je hodnota, k<PERSON><PERSON>te testovať. Hodnota sa môže vzťahovať na bunku, vzorec alebo názov odkazujúci na bunku, vzorec alebo hodnotu"}, "ISTEXT": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je argument textovou hodnotou a vr<PERSON>ti hodnotu TRUE alebo FALSE", "ad": "je hodnota, k<PERSON><PERSON>te testovať. Hodnota sa môže vzťahovať na bunku, vzorec alebo názov odkazujúci na bunku, vzorec alebo hodnotu"}, "N": {"a": "(hodnota)", "d": "Konvertuje nečíselnú hodnotu na číslo, dátumy na poradové čísla, hodnotu TRUE na číslo 1, všetky ostatné výrazy na číslo 0 (nula)", "ad": "je hodn<PERSON>, k<PERSON><PERSON> ch<PERSON>te kon<PERSON>"}, "NA": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON> hodnotu #NEDOSTUPNÝ (hodnota nie je dostup<PERSON>)", "ad": ""}, "SHEET": {"a": "([value])", "d": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "ad": "je názov hárka alebo od<PERSON>, pre ktorý chcete zistiť číslo hárku. Ak ho vynechá<PERSON>, vr<PERSON>ti sa číslo hárka obsahujúceho funkciu"}, "SHEETS": {"a": "([reference])", "d": "Vráti počet hárkov v odkaze", "ad": "je o<PERSON><PERSON><PERSON>, o ktorom chcete zistiť, koľko hárkov obsahuje. Ak ho vynecháte, vráti sa počet hárkov v zošite, ktoré obsahujú funkciu"}, "TYPE": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>, k<PERSON><PERSON> predstavuje typ údajov hodnoty: číslo = 1; text = 2; <PERSON><PERSON><PERSON> hodnota = 4; chybo<PERSON><PERSON> hodnota = 16; pole = 64; <PERSON><PERSON><PERSON><PERSON><PERSON> = 128", "ad": "môže byť ľubovoľná hodnota"}, "AND": {"a": "(logická_hodnota1; [logická_hodnota2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či vš<PERSON>ky argumenty majú hodnotu TRUE, a v <PERSON>r<PERSON><PERSON><PERSON>, že to tak je, vr<PERSON><PERSON> hodnotu TRUE", "ad": "je 1 až <PERSON> test<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> môžu mať hodnotu TRUE alebo FALSE, pri<PERSON><PERSON> to môžu byť logick<PERSON> hodnot<PERSON>, polia alebo odkazy"}, "FALSE": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu FALSE", "ad": ""}, "IF": {"a": "(logický_test; [hodnota_ak_pravda]; [hodnota_ak_nepravda])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je podmienka splnená, a vr<PERSON><PERSON> jednu hodnotu, ak je výsledkom TRUE, a inú hodnotu, ak je výsledkom FALSE", "ad": "je ľubovoľná hodnota alebo výraz, k<PERSON><PERSON><PERSON> môže byť priradená logická hodnota TRUE alebo FALSE!je hodnota, ktor<PERSON> bude vrátená, ak je hodnota argumentu logický_test TRUE. Ak argument vyne<PERSON><PERSON><PERSON>, vráti sa hodnota TRUE. Môžete navrstviť až sedem funkcií IF!je hodnota, ktorá bude vrátená, ak je hodnota argumentu logický_test FALSE. Ak argument vynecháte, vráti sa hodnota FALSE"}, "IFS": {"a": "(logický_test; hodnota_ak_pravda; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je splnená minimálne jedna podmienka, a vr<PERSON><PERSON> hodnotu zodpovedajúcu prvej podmienke s výsledkom TRUE", "ad": "je akákoľvek hodnota alebo výraz, ktor<PERSON> je možné vyhodnotiť ako TRUE alebo FALSE!je hodnota, ktor<PERSON> sa vr<PERSON>ti, ak je výsledkom funkcie logický_test hodnota TRUE"}, "IFERROR": {"a": "(hodnota; hodnota_ak_chyba)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu argumentu hodnota_ak_chyba, ak je zadaný výraz chybou; v opačnom prípade vráti výraz", "ad": "je akákoľvek hodnota, výraz alebo odkaz!je akákoľvek hodnota, výraz alebo odkaz"}, "IFNA": {"a": "(hodnota; hodnota_ak_na)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu, ak je výsledkom výrazu hodnota #N/A, v opačnom prípade vráti výsledok výrazu", "ad": "je ľubovoľná hodnota alebo výraz alebo odkaz!je ľubovoľná hodnota alebo výraz alebo odkaz"}, "NOT": {"a": "(logická_hodnota)", "d": "Zmení hodnotu FALSE na TRUE alebo hodnotu TRUE na FALSE", "ad": "je hodnota alebo výraz, ktorý môže byť TRUE alebo FALSE"}, "OR": {"a": "(logická_hodnota1; [logická_hodnota2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či existujú argumenty s hodnotou TRUE, a vr<PERSON>ti hodnotu TRUE alebo FALSE. Vr<PERSON>ti hodnotu FALSE len v prípade, že všetky argumenty majú hodnotu FALSE", "ad": "je 1 až <PERSON> test<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> môžu mať hodnotu TRUE alebo FALSE"}, "SWITCH": {"a": "(výraz; hodnota1; výsledok1; [predvolené_alebo_hodnota2]; [výsledok2]; ...)", "d": "Vyhodnotí výraz vzhľadom k zoznamu hodnôt a vráti výsledok, ktorý korešponduje s prvou zodpovedajúcou hodnotou. Ak sa nenájde žiadna zhoda, vráti sa voliteľná predvolená hodnota", "ad": "je výraz, ktor<PERSON> sa vyhodnocuje!je hodnota, ktor<PERSON> sa porovnáva s výrazom!je výsledok, ktorý sa vráti v prípade, že zodpovedajúca hodnota sa zhoduje s výrazom"}, "TRUE": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu TRUE", "ad": ""}, "XOR": {"a": "(logická_hodnota1; [logická_hodnota2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>ký operátor Exclusive Or všetkých argumentov", "ad": "je 1 až <PERSON> pod<PERSON>, ktor<PERSON> otest<PERSON>ť a ktoré môžu byť TRUE alebo FALSE a môžu byť logick<PERSON>mi hodnotami, poľami alebo odkazmi"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON><PERSON><PERSON> text, ktorý sa nachádza pred oddeľovacími znakmi.", "ad": "Text, v ktorom chcete hľadať oddeľovač.!Znak alebo reťazec, ktorý sa má použiť ako oddeľovač.!Požadovaný výskyt oddeľovača. Predvolená hodnota je 1. Záporné číslo sa hľadá od konca.!Vyhľadá text na zhodu s oddeľovačom. V predvolenom nastavení sa vykoná porovnávanie podľa veľkosti písmen.!Či sa má oddeľovač porovnať s koncom textu. V predvolenom nastavení sa nezhodujú.!Vráti sa, ak sa nenájde žiadna zhoda. V predvolenom nastavení sa vráti #N/A."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON><PERSON><PERSON> text, ktorý sa nachádza za oddeľovacími znakmi.", "ad": "Text, v ktorom chcete hľadať oddeľovač.!Znak alebo reťazec, ktorý sa má použiť ako oddeľovač.!Požadovaný výskyt oddeľovača. Predvolená hodnota je 1. Záporné číslo sa hľadá od konca.!Vyhľadá text na zhodu s oddeľovačom. V predvolenom nastavení sa vykoná porovnávanie podľa veľkosti písmen.!Či sa má oddeľovač porovnať s koncom textu. V predvolenom nastavení sa nezhodujú.!Vráti sa, ak sa nenájde žiadna zhoda. V predvolenom nastavení sa vráti #N/A."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Rozdelí text do riadkov alebo stĺpcov pomocou oddeľ<PERSON>čov.", "ad": "Text, ktorý sa má rozdeliť.!Znak alebo reťazec, podľa ktorého sa majú rozdeliť stĺpce!Znak alebo reťazec, podľa ktorého sa majú rozdeliť riadky.!Či sa majú ignorovať prázdne bunky. Predvolená hodnota je FALSE.!Vyhľadá text na zhodu s oddeľovačom. V predvolenom nastavení sa porovnávajú veľké a malé písmená.!Hodnota, ktorá sa použije na vypĺňanie. V predvolenom nastavení sa používa #N/A."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Zalomí vektor riadka alebo stĺpca za zadaný počet hodn<PERSON>.", "ad": " Vektor alebo od<PERSON>, ktorý sa má zalomiť.!Maximálny počet hodnôt na riadok.!<PERSON><PERSON><PERSON>, s ktorou sa má pole zapisovať. Predvolená hodnota je #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Zvislo navrství polia do jedného poľa.", "ad": "Pole alebo od<PERSON>z, ktorý sa má navrstviť."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Vodorovne navrství polia do jedného poľa.", "ad": "Pole alebo od<PERSON>z, ktorý sa má navrstviť."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "<PERSON><PERSON><PERSON><PERSON> riadky z poľa alebo odkazu.", "ad": "Pole alebo od<PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON><PERSON>, ktoré sa majú vrátiť.!<PERSON><PERSON>lo riadku, ktorý sa má vrátiť."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Vrá<PERSON> stĺpce z poľa alebo odkazu.", "ad": "Pole alebo odkaz obsahu<PERSON> stĺpce, ktoré sa majú vrátiť.!Číslo stĺpca, ktorý sa má vrátiť."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON><PERSON><PERSON><PERSON> pole ako jeden stĺpec.", "ad": "Pole alebo od<PERSON><PERSON>, ktorý sa má vrátiť ako stĺpec!Či sa majú ignorovať určité typy hodnôt. Predvolene sa neignorujú žiadne hodnoty.!Prehľadávanie poľa podľa stĺpcov. Predvolene sa pole prehľadáva po riadkoch."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON><PERSON><PERSON><PERSON> pole ako jeden ria<PERSON>. ", "ad": "Pole alebo od<PERSON><PERSON>, ktorý sa má vrátiť ako riadok!Či sa majú ignorovať určité typy hodnôt. V predvolenom nastavení sa neignorujú žiadne hodnoty.!Prehľadávanie poľa podľa stĺpcov. V predvolenom nastavení sa pole prehľadáva po riadkoch."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Zalomí vektor riadka alebo stĺpca za zadaný počet hodn<PERSON>.", "ad": " Vektor alebo od<PERSON>, ktorý sa má zalomiť.!Maximálny počet hodnôt na stĺpec.!<PERSON><PERSON><PERSON>, s ktorou sa má pole zapisovať. Predvolená hodnota je #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": "<PERSON><PERSON><PERSON><PERSON> riadky alebo stĺpce zo začiatku alebo konca poľa.", "ad": "<PERSON>, z ktorého sa majú vziať riadky alebo stĺpce.!<PERSON><PERSON><PERSON> riadkov, ktoré sa majú vziať. Záporná hodnota berie z konca poľa.!Počet stĺpcov, ktoré sa majú vziať. Záporná hodnota berie z konca poľa."}, "DROP": {"a": "(array, rows, [columns])", "d": "Vypustí riadky alebo stĺpce zo začiatku alebo konca poľa.", "ad": "<PERSON>, z ktorého sa majú vypustiť riadky alebo stĺpce.!<PERSON><PERSON><PERSON> riadkov, ktoré sa majú vypustiť. Záporná hodnota vypustí z konca poľa.!Počet stĺpcov, ktoré sa majú vypustiť. Záporná hodnota vypustí z konca poľa."}, "SEQUENCE": {"a": "(riad<PERSON>, [stĺpce], [začiatok], [krok])", "d": "<PERSON><PERSON><PERSON><PERSON> čísel", "ad": "počet riadkov, ktoré sa majú vrátiť!počet stĺpcov, ktoré sa majú vrátiť!prvé číslo v postupnosti!hodnota prírastku každej nasledujúcej hodnoty v postupnosti"}, "EXPAND": {"a": "(pole, riadky, [stĺpce], [pad_with])", "d": "Rozšíri pole na zadané rozmery.", "ad": "<PERSON>, ktoré sa má rozšíriť.!Počet riadkov v rozšírenom poli. Ak chýba, riadky sa nerozšíria.!Počet stĺpcov v rozšírenom poli. Ak chýba, stĺpce sa nerozšíria.!Hodnota, ktorou sa má pole vyplniť. Predvolená hodnota je #N/A."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "<PERSON><PERSON>áti relatívnu pozíciu položky poľa. Štandardne sa vyžaduje presná zhoda.", "ad": "je hľadaná hodnota!je pole alebo roz<PERSON>, ktorý sa prehľadáva!ur<PERSON><PERSON><PERSON>, ako sa má zhodovať vyhľadávaná_hodnota vzhľadom na hodnoty v poli_vyhľadávania!určuje režim vyhľadávania, ktorý sa má použiť. Štandardne sa použije prehľadávanie od prvej po poslednú položku"}, "FILTER": {"a": "(pole, zahrn<PERSON><PERSON>, [ak_prázdne])", "d": "<PERSON>lt<PERSON><PERSON> rozsah alebo pole", "ad": "r<PERSON><PERSON><PERSON> po<PERSON>, ktorý sa má filtrovať!pole booleovských hodnôt, v ktorých hodnota TRUE predstavuje riadok alebo stĺpec, ktorý sa má ponechať!vrátené, ak sa nezachovali žiadne položky"}, "ARRAYTOTEXT": {"a": "(pole, [form<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON>ov<PERSON> znázornenie poľa", "ad": "pole znázornené ako text!formát textu"}, "SORT": {"a": "(pole, [index_zoradenia], [poradie_zoradenia], [podľa_stĺpca])", "d": "Zorad<PERSON> rozsah alebo pole", "ad": "roz<PERSON>h alebo pole, ktoré sa má zoradiť!č<PERSON>lo označujúce riadok alebo stĺpec, podľa ktorého sa má zoraďovať!číslo označujúce požadované poradie zoradenia;1 označuje vzostupné poradie (predvolené), -1 označuje zostupné poradie!logická hodnota, ktorá označuje požadovaný smer zoradenia: FALSE na zoradenie podľa riadka (predvolené), TRUE na zoradenie podľa stĺpca"}, "SORTBY": {"a": "(pole, podľa_poľa, [poradie_zoradenia], ...)", "d": "Zoradí rozsah alebo pole podľa hodnôt v zodpovedajúcom rozsahu alebo poli", "ad": "rozsah alebo pole, ktor<PERSON> sa majú zoradiť!rozsah alebo pole, podľa ktorého sa majú hodnoty zoradiť!číslo určujúce spôsob zoradenia; 1 pre stúpajúce hodnoty (predvolené), -1 pre klesajúce hodnoty"}, "GETPIVOTDATA": {"a": "(údajov<PERSON>_pole; kont<PERSON>čná_tabuľka; [pole]; [položka]; ...)", "d": "Extrahuje údaje uložené v kontingenčnej tabuľke", "ad": "je názov údajového poľa, z ktorého sa majú extrahovať údaje!je odkaz na bunku alebo rozsah buniek v kontingenčnej tabuľke obsahujúcej požadované údaje!pole, na ktoré sa odkazuje!položka poľa, na ktorú sa odkazuje"}, "IMPORTRANGE": {"a": "(webová_adresa_tabu<PERSON>, reťazec_rozsahu)", "d": "Importuje rozsah buniek zo zadanej tabuľky."}}