{"DATE": {"a": "(year; month; day)", "d": "날짜-시간 코드에서 날짜를 나타내는 수를 구합니다.", "ad": "은(는) 통합 문서의 날짜 체계에 따른 1900 또는 1904부터 9999까지의 숫자입니다.!은(는) 해당 연도의 달을 나타내는 1부터 12까지의 숫자입니다.!은(는) 해당 월의 날짜를 나타내는 1부터 31까지의 숫자입니다."}, "DATEDIF": {"a": "(start-date; end-date; unit)", "d": "두 날짜 사이의 일, 월 또는 연도 수를 계산합니다.", "ad": "지정된 기간의 첫 번째 또는 시작 날짜를 나타내는 날짜입니다!기간의 마지막 날짜나 종료 날짜를 나타내는 날짜입니다!반환하려는 정보 유형입니다"}, "DATEVALUE": {"a": "(date_text)", "d": "날짜-시간 코드에서 텍스트 형태의 날짜를 숫자 값으로 변환합니다.", "ad": "은(는) Spreadsheet Editor 날짜 형식으로 1990/1/1 또는 1904/1/1(통합 문서의 날짜 체계에 따라 다름)부터 9999/12/31까지의 날짜를 표시한 텍스트입니다."}, "DAY": {"a": "(serial_number)", "d": "주어진 달의 날짜를 1에서 31 사이의 숫자로 구합니다.", "ad": "은(는) Spreadsheet Editor에서 사용하는 날짜-시간 코드 형식의 수입니다."}, "DAYS": {"a": "(end_date; start_date)", "d": "두 날짜 사이의 일 수를 반환합니다.", "ad": "start_date와 end_date는 날짜 수를 계산하고자 하는 시작 날짜와 끝 날짜입니다.!start_date와 end_date는 날짜 수를 계산하고자 하는 시작 날짜와 끝 날짜입니다."}, "DAYS360": {"a": "(start_date; end_date; [method])", "d": "1년을 360일(30일 기준의 12개월)로 하여, 두 날짜 사이의 날짜 수를 계산합니다.", "ad": "start_date와 end_date는 날짜 수를 계산하고자 하는 시작 날짜와 끝 날짜입니다.!start_date와 end_date는 날짜 수를 계산하고자 하는 시작 날짜와 끝 날짜입니다.!은(는) 계산 방법을 지정하는 논리값입니다. FALSE로 설정하거나 생략하면 U.S.(NASD)식을 사용하며, TRUE로 설정하면 유럽식을 사용합니다."}, "EDATE": {"a": "(start_date; months)", "d": "지정한 날짜 전이나 후의 개월 수를 나타내는 날짜의 일련 번호를 반환합니다.", "ad": "은(는) 시작 날짜입니다.!은(는) start_date 전이나 후의 개월 수입니다."}, "EOMONTH": {"a": "(start_date; months)", "d": "지정된 달 수 이전이나 이후 달의 마지막 날의 날짜 일련 번호를 반환합니다.", "ad": "은(는) 시작 날짜입니다.!은(는) start_date 전이나 후의 개월 수입니다."}, "HOUR": {"a": "(serial_number)", "d": "시간을 0(오전 12:00)부터 23(오후 11:00)까지의 수로 구합니다.", "ad": "은(는) Spreadsheet Editor에서 사용하는 날짜-시간 코드 형식의 수이거나 16:48:00 또는 오후 4:48:00 같은 시간 형식의 텍스트입니다."}, "ISOWEEKNUM": {"a": "(date)", "d": "지정된 날짜에 따른 해당 연도의 ISO 주를 반환합니다.", "ad": "은(는) Spreadsheet Editor에서 날짜 및 시간 계산에 사용하는 날짜-시간 코드입니다."}, "MINUTE": {"a": "(serial_number)", "d": "분을 0부터 59까지의 수로 구합니다.", "ad": "은(는) Spreadsheet Editor에서 사용하는 날짜-시간 코드 형식의 수이거나 16:48:00 또는 오후 4:48:00 같은 시간 형식의 텍스트입니다."}, "MONTH": {"a": "(serial_number)", "d": "1(1월)에서 12(12월) 사이의 숫자로 해당 월을 구합니다.", "ad": "은(는) Spreadsheet Editor에서 사용하는 날짜-시간 코드 형식의 수입니다."}, "NETWORKDAYS": {"a": "(start_date; end_date; [holidays])", "d": "두 날짜 사이의 전체 작업 일수를 반환합니다.", "ad": "은(는) 시작 날짜입니다.!은(는) 마지막 날짜입니다.!은(는) 국경일, 공휴일, 임시 휴일 등 작업 일수에서 제외되는 한 개 이상의 날짜 목록이며 선택 사항입니다."}, "NETWORKDAYS.INTL": {"a": "(start_date; end_date; [weekend]; [holidays])", "d": "사용자 지정 weekend 매개 변수를 사용하여 두 날짜 사이의 전체 작업 일수를 반환합니다.", "ad": "은(는) 시작 날짜입니다.!은(는) 마지막 날짜입니다.!은(는) 주말을 나타내는 숫자 또는 문자열입니다.!은(는) 국경일, 공휴일, 임시 휴일 등 작업 일수에서 제외되는 한 개 이상의 날짜 목록이며 선택 사항입니다."}, "NOW": {"a": "()", "d": "현재의 날짜와 시간을 날짜와 시간 형식으로 구합니다.", "ad": ""}, "SECOND": {"a": "(serial_number)", "d": "초를 0부터 59까지의 수로 구합니다.", "ad": "은(는) Spreadsheet Editor에서 사용하는 날짜-시간 코드 형식의 수이거나 16:48:23 또는 오후 4:48:47 같은 시간 형식의 텍스트입니다."}, "TIME": {"a": "(hour; minute; second)", "d": "수치로 주어진 시간, 분, 초를 시간 형식의 엑셀 일련 번호로 변환합니다.", "ad": "은(는) 시간을 나타내는 0부터 23까지의 수입니다.!은(는) 분을 나타내는 0에서 59까지의 수입니다.!은(는) 초를 나타내는 0에서 59까지의 수입니다."}, "TIMEVALUE": {"a": "(time_text)", "d": "텍스트 형태의 시간을 일련 번호로 표현한 시간으로 변환합니다. 값은 0(오전 12:00)부터 0.999988426(오후 11:59:59)까지입니다. 수식을 입력한 후 수를 시간 형식으로 지정하십시오.", "ad": "은(는) Spreadsheet Editor의 시간 형식 중 하나로 시간을 표시한 텍스트 문자열입니다. 문자열 내의 날짜 정보는 무시됩니다."}, "TODAY": {"a": "()", "d": "현재 날짜를 날짜 서식으로 표시합니다.", "ad": ""}, "WEEKDAY": {"a": "(serial_number; [return_type])", "d": "일정 날짜의 요일을 나타내는 1에서 7까지의 수를 구합니다.", "ad": "은(는) 날짜를 나타내는 수입니다.!은(는) 결과값의 유형을 결정하는 수(1, 2 또는 3)입니다. 일요일(1)에서 토요일(7)까지의 유형은 1을, 월요일(1)에서 일요일(7)까지의 유형은 2를, 월요일(0)에서 일요일(6)까지의 유형은 3을 쓰십시오."}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "지정한 주가 일 년 중 몇째 주인지를 나타내는 숫자를 반환합니다.", "ad": "은(는) Spreadsheet Editor에서 날짜 및 시간 계산에 사용하는 날짜 시간 코드입니다.!은(는) 반환 값 유형을 결정하는 숫자로 1 또는 2입니다."}, "WORKDAY": {"a": "(start_date; days; [holidays])", "d": "특정 일(시작 날짜)의 전이나 후의 날짜 수에서 주말이나 휴일을 제외한 날짜 수, 즉 평일 수를 반환합니다.", "ad": "은(는) 시작 날짜입니다.!은(는) start_date 전이나 후의 주말이나 휴일을 제외한 날짜 수입니다.!은(는) 국경일, 공휴일, 임시 공휴일과 같이 작업 일수에서 제외되는 날짜 목록이며 선택 사항입니다."}, "WORKDAY.INTL": {"a": "(start_date; days; [weekend]; [holidays])", "d": "사용자 지정 weekend 매개 변수를 사용하여 특정 일(시작 날짜)의 전이나 후의 날짜 수에서 주말이나 휴일을 제외한 날짜 수, 즉 평일 수를 반환합니다.", "ad": "은(는) 시작 날짜입니다.!은(는) start_date 전이나 후의 주말이나 휴일을 제외한 날짜 수입니다.!은(는) 주말을 나타내는 숫자 또는 문자열입니다.!은(는) 국경일, 공휴일, 임시 공휴일과 같이 작업 일수에서 제외되는 날짜 목록이며 선택 사항입니다."}, "YEAR": {"a": "(serial_number)", "d": "1900에서 9999 사이의 정수로 일정 날짜의 연도를 구합니다.", "ad": "은(는) Spreadsheet Editor에서 사용하는 날짜-시간 코드 형식의 수입니다."}, "YEARFRAC": {"a": "(start_date; end_date; [basis])", "d": "start_date와 end_date 사이의 날짜 수가 일 년 중 차지하는 비율을 반환합니다.", "ad": "은(는) 시작 날짜입니다.!은(는) 끝 날짜입니다.!은(는) 날짜 계산 기준입니다."}, "BESSELI": {"a": "(x; n)", "d": "수정된 Bessel 함수 In(x) 값을 반환합니다.", "ad": "은(는) 함수를 계산할 값입니다.!은(는) Bessel 함수의 차수입니다."}, "BESSELJ": {"a": "(x; n)", "d": "Bessel 함수 Jn(x) 값을 반환합니다.", "ad": "은(는) 함수를 계산할 값입니다.!은(는) Bessel 함수의 차수입니다."}, "BESSELK": {"a": "(x; n)", "d": "수정된 Bessel 함수 Kn(x) 값을 반환합니다.", "ad": "은(는) 함수를 계산할 값입니다.!은(는) Bessel 함수의 차수입니다."}, "BESSELY": {"a": "(x; n)", "d": "Bessel 함수 Yn(x) 값을 반환합니다.", "ad": "은(는) 함수를 계산할 값입니다.!은(는) Bessel 함수의 차수입니다."}, "BIN2DEC": {"a": "(number)", "d": "2진수를 10진수로 변환합니다.", "ad": "은(는) 변환할 2진수입니다."}, "BIN2HEX": {"a": "(number; [places])", "d": "2진수를 16진수로 변환합니다.", "ad": "은(는) 변환할 2진수입니다.!은(는) 사용할 자릿수입니다."}, "BIN2OCT": {"a": "(number; [places])", "d": "2진수를 8진수로 변환합니다.", "ad": "은(는) 변환할 2진수입니다.!은(는) 사용할 자릿수입니다."}, "BITAND": {"a": "(number1; number2)", "d": "두 수의 비트 'And' 값을 구합니다.", "ad": "은(는) 계산하려는 이진수의 십진수 표현입니다.!은(는) 계산하려는 이진수의 십진수 표현입니다."}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "shift_amount 비트만큼 왼쪽으로 이동된 숫자를 구합니다.", "ad": "은(는) 계산하려는 이진수의 십진수 표현입니다.!은(는) Number를 왼쪽으로 이동할 비트 수입니다."}, "BITOR": {"a": "(number1; number2)", "d": "두 수의 비트 'Or' 값을 구합니다.", "ad": "은(는) 계산하려는 이진수의 십진수 표현입니다.!은(는) 계산하려는 이진수의 십진수 표현입니다."}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "shift_amount 비트만큼 오른쪽으로 이동된 숫자를 구합니다.", "ad": "은(는) 계산하려는 이진수의 십진수 표현입니다.!은(는) Number를 오른쪽으로 이동할 비트 수입니다."}, "BITXOR": {"a": "(number1; number2)", "d": "두 수의 비트 '배타적 Or' 값을 구합니다.", "ad": "은(는) 계산하려는 이진수의 십진수 표현입니다.!은(는) 계산하려는 이진수의 십진수 표현입니다."}, "COMPLEX": {"a": "(real_num; i_num; [suffix])", "d": "실수부와 허수부의 계수를 복소수로 변환합니다.", "ad": "은(는) 복소수의 실수부 계수입니다.!은(는) 복소수의 허수부 계수입니다.!은(는) 복소수의 허수 표시 접미사입니다."}, "CONVERT": {"a": "(number; from_unit; to_unit)", "d": "다른 단위 체계의 숫자로 변환합니다.", "ad": "은(는) 변환할 from_units의 값입니다.!은(는) number의 단위입니다.!은(는) 결과 단위입니다."}, "DEC2BIN": {"a": "(number; [places])", "d": "10진수를 2진수로 변환합니다.", "ad": "은(는) 변환할 10진수입니다.!은(는) 사용할 자릿수입니다."}, "DEC2HEX": {"a": "(number; [places])", "d": "10진수를 16진수로 변환합니다.", "ad": "은(는) 변환할 10진수입니다.!은(는) 사용할 자릿수입니다."}, "DEC2OCT": {"a": "(number; [places])", "d": "10진수를 8진수로 변환합니다.", "ad": "은(는) 변환할 10진수입니다.!은(는) 사용할 자릿수입니다."}, "DELTA": {"a": "(number1; [number2])", "d": "두 값이 같은지 여부를 검사합니다.", "ad": "은(는) 첫째 수입니다.!은(는) 둘째 수입니다."}, "ERF": {"a": "(lower_limit; [upper_limit])", "d": "오차 함수를 구합니다.", "ad": "은(는) ERF 적분의 하한값입니다.!은(는) ERF 적분의 상한값입니다."}, "ERF.PRECISE": {"a": "(X)", "d": "오차 함수를 구합니다.", "ad": "은(는) ERF.PRECISE 적분의 하한값입니다."}, "ERFC": {"a": "(x)", "d": "ERF 함수의 여값을 반환합니다.", "ad": "은(는) ERF 적분의 하한값입니다."}, "ERFC.PRECISE": {"a": "(X)", "d": "ERF 함수의 여값을 반환합니다.", "ad": "은(는) ER.PRECISEF 적분의 하한값입니다."}, "GESTEP": {"a": "(number; [step])", "d": "숫자가 임계값보다 큰지 여부를 검사합니다.", "ad": "은(는) step과 비교할 값입니다.!은(는) 임계값입니다."}, "HEX2BIN": {"a": "(number; [places])", "d": "16진수를 2진수로 변환합니다.", "ad": "은(는) 변환할 16진수입니다.!은(는) 사용할 자릿수입니다."}, "HEX2DEC": {"a": "(number)", "d": "16진수를 10진수로 변환합니다.", "ad": "은(는) 변환할 16진수입니다."}, "HEX2OCT": {"a": "(number; [places])", "d": "16진수를 8진수로 변환합니다.", "ad": "은(는) 변환할 16진수입니다.!은(는) 사용할 자릿수입니다."}, "IMABS": {"a": "(inumber)", "d": "복소수의 절대값을 구합니다.", "ad": "은(는) 절대값을 구할 복소수입니다."}, "IMAGINARY": {"a": "(inumber)", "d": "복소수의 허수부 계수를 구합니다.", "ad": "은(는) 허수부 계수를 구할 복소수입니다."}, "IMARGUMENT": {"a": "(inumber)", "d": "각도가 라디안으로 표시되는 인수 q를 구합니다.", "ad": "은(는) 인수 q를 구할 복소수입니다."}, "IMCONJUGATE": {"a": "(inumber)", "d": "복소수의 켤레 복소수를 구합니다.", "ad": "은(는) 켤레 복소수를 구할 복소수입니다."}, "IMCOS": {"a": "(inumber)", "d": "복소수의 코사인 값을 구합니다.", "ad": "은(는) 코사인 값을 구할 복소수입니다."}, "IMCOSH": {"a": "(inumber)", "d": "복소수의 하이퍼볼릭 코사인 값을 구합니다.", "ad": "은(는) 하이퍼볼릭 코사인 값을 구하려는 복소수입니다."}, "IMCOT": {"a": "(inumber)", "d": "복소수의 코탄젠트 값을 구합니다.", "ad": "은(는) 코탄젠트를 구할 복소수입니다."}, "IMCSC": {"a": "(inumber)", "d": "복소수의 코시컨트 값을 구합니다.", "ad": "은(는) 코시컨트를 구할 복소수입니다."}, "IMCSCH": {"a": "(inumber)", "d": "복소수의 하이퍼볼릭 코시컨트 값을 구합니다.", "ad": "은(는) 하이퍼볼릭 코시컨트를 구할 복소수입니다."}, "IMDIV": {"a": "(inumber1; inumber2)", "d": "두 복소수의 나눗셈 몫을 반환합니다.", "ad": "은(는) 분자 또는 피제수인 복소수입니다.!은(는) 분모 또는 제수인 복소수입니다."}, "IMEXP": {"a": "(inumber)", "d": "복소수의 지수를 구합니다.", "ad": "은(는) 지수를 구할 복소수입니다."}, "IMLN": {"a": "(inumber)", "d": "복소수의 자연 로그값을 구합니다.", "ad": "은(는) 자연 로그값을 구할 복소수입니다."}, "IMLOG10": {"a": "(inumber)", "d": "복소수의 밑이 10인 로그값을 구합니다.", "ad": "은(는) 상용 로그값을 구할 복소수입니다."}, "IMLOG2": {"a": "(inumber)", "d": "복소수의 밑이 2인 로그값을 구합니다.", "ad": "은(는) 밑이 2인 로그값을 구할 복소수입니다."}, "IMPOWER": {"a": "(inumber; number)", "d": "복소수의 멱을 반환합니다.", "ad": "은(는) 멱을 구할 복소수입니다.!은(는) 멱의 지수입니다."}, "IMPRODUCT": {"a": "(inumber1; [inumber2]; ...)", "d": "복소수 1개부터 255개까지의 곱을 반환합니다.", "ad": "Inumber1, Inumber2,...은(는) 곱할 복소수로 1개부터 255개까지 사용할 수 있습니다."}, "IMREAL": {"a": "(inumber)", "d": "복소수의 실수부 계수를 구합니다.", "ad": "은(는) 실수부 계수를 구할 복소수입니다."}, "IMSEC": {"a": "(inumber)", "d": "복소수의 시컨트 값을 구합니다.", "ad": "은(는) 시컨트를 구할 복소수입니다."}, "IMSECH": {"a": "(inumber)", "d": "복소수의 하이퍼볼릭 시컨트 값을 구합니다.", "ad": "은(는) 하이퍼볼릭 시컨트를 구할 복소수입니다."}, "IMSIN": {"a": "(inumber)", "d": "복소수의 사인 값을 구합니다.", "ad": "은(는) 사인 값을 구할 복소수입니다."}, "IMSINH": {"a": "(inumber)", "d": "복소수의 하이퍼볼릭 사인 값을 구합니다.", "ad": "은(는) 하이퍼볼릭 사인 값을 구하려는 복소수입니다."}, "IMSQRT": {"a": "(inumber)", "d": "복소수의 제곱근을 구합니다.", "ad": "은(는) 제곱근을 구할 복소수입니다."}, "IMSUB": {"a": "(inumber1; inumber2)", "d": "두 복소수의 차를 반환합니다.", "ad": "은(는) 피감수인 복소수입니다.!은(는) 감수인 복소수입니다."}, "IMSUM": {"a": "(inumber1; [inumber2]; ...)", "d": "복소수의 합을 구합니다.", "ad": "은(는) 더할 복소수로 1개부터 255개까지 사용할 수 있습니다."}, "IMTAN": {"a": "(inumber)", "d": "복소수의 탄젠트 값을 구합니다.", "ad": "은(는) 탄젠트를 구할 복소수입니다."}, "OCT2BIN": {"a": "(number; [places])", "d": "8진수를 2진수로 변환합니다.", "ad": "은(는) 변환할 8진수입니다.!은(는) 사용할 자릿수입니다."}, "OCT2DEC": {"a": "(number)", "d": "8진수를 10진수로 변환합니다.", "ad": "은(는) 변환할 8진수입니다."}, "OCT2HEX": {"a": "(number; [places])", "d": "8진수를 16진수로 변환합니다.", "ad": "은(는) 변환할 8진수입니다.!은(는) 사용할 자릿수입니다."}, "DAVERAGE": {"a": "(database; field; criteria)", "d": "지정한 조건에 맞는 데이터베이스나 목록에서 열의 평균을 구합니다.", "ad": "은(는) 데이터베이스나 목록으로 지정할 셀 범위입니다. 데이터베이스는 관련 데이터의 목록입니다.!은(는) 목록에서 열의 위치를 나타내는 숫자나 따옴표로 묶인 열 레이블입니다.!은(는) 지정한 조건이 있는 셀 범위입니다. 이 범위는 열 레이블과 조건 레이블 아래의 셀을 포함합니다."}, "DCOUNT": {"a": "(database; field; criteria)", "d": "지정한 조건에 맞는 데이터베이스의 필드에서 숫자를 포함한 셀의 수를 구합니다.", "ad": "은(는) 데이터베이스나 목록으로 지정할 셀 범위입니다. 데이터베이스는 관련 데이터 목록입니다.!은(는) 목록에서 열의 위치를 나타내는 숫자나 열 레이블입니다.!은(는) 찾을 조건이 있는 셀 범위입니다. 이 범위는 열 레이블과 조건 레이블 아래의 셀을 포함합니다."}, "DCOUNTA": {"a": "(database; field; criteria)", "d": "조건에 맞는 데이터베이스의 필드에서 비어 있지 않은 셀의 수를 구합니다.", "ad": "은(는) 데이터베이스나 목록으로 지정할 셀 범위입니다. 데이터베이스는 관련 데이터 목록입니다.!은(는) 목록에서 열 위치를 나타내는 숫자나 열 레이블입니다.!은(는) 찾을 조건이 있는 셀 범위입니다. 이 범위는 열 레이블과 조건 레이블 아래의 셀을 포함합니다."}, "DGET": {"a": "(database; field; criteria)", "d": "데이터베이스에서 찾을 조건에 맞는 레코드가 하나인 경우 그 레코드를 추출합니다.", "ad": "은(는) 데이터베이스나 목록으로 지정할 셀 범위입니다. 데이터베이스는 관련 데이터 목록입니다.!은(는) 목록에서 열 위치를 나타내는 숫자나 열 레이블입니다.!은(는) 찾을 조건이 있는 셀 범위입니다. 이 범위는 열 레이블과 조건 레이블 아래의 셀을 포함합니다."}, "DMAX": {"a": "(database; field; criteria)", "d": "지정한 조건에 맞는 데이터베이스의 필드 값 중 가장 큰 수를 구합니다.", "ad": "은(는) 데이터베이스나 목록으로 지정할 셀 범위입니다. 데이터베이스는 관련 데이터 목록입니다.!은(는) 목록에서 열 위치를 나타내는 숫자나 따옴표로 묶인 열 레이블입니다.!은(는) 지정한 조건이 있는 셀 범위입니다. 이 범위는 열 레이블과 조건 레이블 아래의 셀을 포함합니다."}, "DMIN": {"a": "(database; field; criteria)", "d": "지정한 조건에 맞는 데이터베이스의 필드 값 중 가장 작은 수를 구합니다.", "ad": "은(는) 데이터베이스나 목록으로 지정할 셀 범위입니다. 데이터베이스는 관련 데이터 목록입니다.!은(는) 목록에서 열 위치를 나타내는 숫자나 따옴표로 묶인 열 레이블입니다.!은(는) 지정한 조건이 있는 셀 범위입니다. 이 범위는 열 레이블과 조건 레이블 아래의 셀을 포함합니다."}, "DPRODUCT": {"a": "(database; field; criteria)", "d": "지정한 조건에 맞는 데이터베이스에서 필드 값들의 곱을 구합니다.", "ad": "은(는) 데이터베이스나 목록으로 지정할 셀 범위입니다. 데이터베이스는 관련 데이터 목록입니다.!은(는) 목록에서 열 위치를 나타내는 숫자나 열 레이블입니다.!은(는) 찾을 조건이 있는 셀 범위입니다. 이 범위는 열 레이블과 조건 레이블 아래의 셀을 포함합니다."}, "DSTDEV": {"a": "(database; field; criteria)", "d": "데이터베이스 필드 값들로부터 표본 집단의 표준 편차를 구합니다.", "ad": "은(는) 데이터베이스나 목록으로 지정할 셀 범위입니다. 데이터베이스는 관련 데이터 목록입니다.!은(는) 목록에서 열 위치를 나타내는 숫자나 열 레이블입니다.!은(는) 찾을 조건이 있는 셀 범위입니다. 이 범위는 열 레이블과 조건 레이블 아래의 셀을 포함합니다."}, "DSTDEVP": {"a": "(database; field; criteria)", "d": "데이터베이스 필드 값들로부터 모집단의 표준 편차를 구합니다.", "ad": "은(는) 데이터베이스나 목록으로 지정할 셀 범위입니다. 데이터베이스는 관련 데이터 목록입니다.!은(는) 목록에서 열 위치를 나타내는 숫자나 열 레이블입니다.!은(는) 찾을 조건이 있는 셀 범위입니다. 이 범위는 열 레이블과 조건 레이블 아래의 셀을 포함합니다."}, "DSUM": {"a": "(database; field; criteria)", "d": "지정한 조건에 맞는 데이터베이스에서 필드 값들의 합을 구합니다.", "ad": "은(는) 데이터베이스나 목록으로 지정할 셀 범위입니다. 데이터베이스는 관련 데이터 목록입니다.!은(는) 목록에서 열 위치를 나타내는 숫자나 열 레이블입니다.!은(는) 찾을 조건이 있는 셀 범위입니다. 이 범위는 열 레이블과 조건 레이블 아래의 셀을 포함합니다."}, "DVAR": {"a": "(database; field; criteria)", "d": "데이터베이스 필드 값들로부터 표본 집단의 분산을 구합니다.", "ad": "은(는) 데이터베이스나 목록으로 지정할 셀 범위입니다. 데이터베이스는 관련 데이터 목록입니다.!은(는) 목록에서 열 위치를 나타내는 숫자나 열 레이블입니다.!은(는) 찾을 조건이 있는 셀 범위입니다. 이 범위는 열 레이블과 조건 레이블 아래의 셀을 포함합니다."}, "DVARP": {"a": "(database; field; criteria)", "d": "데이터베이스 필드 값들로부터 모집단의 분산을 구합니다.", "ad": "은(는) 데이터베이스나 목록으로 지정할 셀 범위입니다. 데이터베이스는 관련 데이터 목록입니다.!은(는) 목록에서 열 위치를 나타내는 숫자나 열 레이블입니다.!은(는) 찾을 조건이 있는 셀 범위입니다. 이 범위는 열 레이블과 조건 레이블 아래의 셀을 포함합니다."}, "CHAR": {"a": "(number)", "d": "시스템의 문자 세트에 대한 코드 번호에 해당하는 문자를 구합니다.", "ad": "은(는) 원하는 문자를 지정하는 1에서 255 사이의 수입니다."}, "CLEAN": {"a": "(text)", "d": "인쇄할 수 없는 모든 문자들을 텍스트에서 제거합니다.", "ad": "은(는) 인쇄할 수 없는 문자를 제거할 데이터입니다."}, "CODE": {"a": "(text)", "d": "텍스트 문자열의 첫째 문자를 나타내는 코드값을 시스템에서 사용되는 문자 집합에서 구합니다.", "ad": "은(는) 첫째 문자의 코드값을 구하려는 텍스트입니다."}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "여러 텍스트를 한 텍스트로 조인시킵니다.", "ad": "은(는) 하나로 조인할 텍스트들로서 255개까지 지정할 수 있습니다."}, "CONCAT": {"a": "(text1; ...)", "d": "텍스트 문자열의 목록 또는 범위를 연결합니다.", "ad": "은(는) 1-254 사이의 텍스트 문자열 또는 단일 텍스트 문자열에 연결할 범위입니다."}, "DOLLAR": {"a": "(number; [decimals])", "d": "통화 표시 형식을 사용하여 숫자를 텍스트로 변환합니다.", "ad": "은(는) 수, 수를 포함하는 셀에 대한 참조 또는 수로 계산하는 수식입니다.!은(는) 소수점 이하 자릿수입니다. 필요한 경우에는 반올림됩니다. 생략하면 Decimals은 2가 됩니다."}, "EXACT": {"a": "(text1; text2)", "d": "두 텍스트 값이 같은지 검사하여 같으면 TRUE를 돌려주고, 다르면 FALSE를 돌려줍니다. 대/소문자를 구분합니다.", "ad": "은(는) 첫째 텍스트입니다.!은(는) 둘째 텍스트입니다."}, "FIND": {"a": "(find_text; within_text; [start_num])", "d": "지정한 텍스트를 다른 텍스트 내에서 찾아 해당 문자의 시작 위치를 나타냅니다. 대/소문자를 구분합니다.", "ad": "은(는) 찾으려는 텍스트입니다. Within_text의 첫 문자가 일치하는 경우를 찾으려면 큰따옴표를 사용하십시오. 와일드카드 문자는 사용할 수 없습니다.!은(는) 찾으려는 텍스트가 포함된 텍스트입니다.!은(는) 찾기 시작할 문자의 위치입니다. within_text의 첫 문자는 1이며, 생략하면 start_num을 1로 지정합니다."}, "FINDB": {"a": "(find_text; within_text; [start_num])", "d": "는 두 번째 텍스트 문자열에서 지정된 텍스트 문자열을 검색하고, 두 번째 텍스트 문자열의 첫 문자를 기준으로 첫 번째 문자열이 시작하는 위치에 해당하는 숫자를 반환합니다. 는 DBCS(더블바이트 문자 집합)를 사용하는 언어에 사용합니다. DBCS를 지원하는 언어로는 한국어, 일본어, 중국어.", "ad": "은(는) 찾으려는 텍스트입니다. Within_text의 첫 문자가 일치하는 경우를 찾으려면 큰따옴표를 사용하십시오. 와일드카드 문자는 사용할 수 없습니다.!은(는) 찾으려는 텍스트가 포함된 텍스트입니다.!은(는) 찾기 시작할 문자의 위치입니다. within_text의 첫 문자는 1이며, 생략하면 start_num을 1로 지정합니다."}, "FIXED": {"a": "(number; [decimals]; [no_commas])", "d": "수를 고정 소수점 형식의 텍스트로 바꿉니다.", "ad": "은(는) 반올림하여 텍스트로 바꾸려는 수입니다.!은(는) 소수점 이하의 자릿수입니다. 생략하면 decimals은 2가 됩니다.!은(는) 수에 쉼표를 표시할지 지정하는 논리값입니다. 쉼표가 없으면 TRUE이고 쉼표가 표시되면 FALSE입니다."}, "LEFT": {"a": "(text; [num_chars])", "d": "텍스트 문자열의 시작 지점부터 지정한 수만큼의 문자를 반환합니다.", "ad": "은(는) 추출하려는 문자가 들어 있는 텍스트 문자열입니다.!은(는) 왼쪽에서부터 추출할 문자 수를 지정합니다. 생략하면 1이 됩니다."}, "LEFTB": {"a": "(text; [num_chars])", "d": "는 텍스트 문자열의 첫 번째 문자부터 시작하여 지정한 바이트 수만큼 문자를 반환합니다. 는 DBCS(더블바이트 문자 집합)를 사용하는 언어에 사용합니다. DBCS를 지원하는 언어로는 한국어, 일본어, 중국어.", "ad": "은(는) 추출하려는 문자가 들어 있는 텍스트 문자열입니다.!은(는) 왼쪽에서부터 추출할 문자 수를 지정합니다. 생략하면 1이 됩니다."}, "LEN": {"a": "(text)", "d": "텍스트 문자열 내의 문자 개수를 구합니다.", "ad": "은(는) 사용자가 찾는 개수의 문자가 들어 있는 텍스트입니다. 공백도 문자 개수에 포함됩니다."}, "LENB": {"a": "(text)", "d": "는 텍스트 문자열의 문자를 나타내는 데 사용되는 바이트 수를 반환합니다. 는 DBCS(더블바이트 문자 집합)를 사용하는 언어에 사용합니다. DBCS를 지원하는 언어로는 한국어, 일본어, 중국어.", "ad": "은(는) 사용자가 찾는 개수의 문자가 들어 있는 텍스트입니다. 공백도 문자 개수에 포함됩니다."}, "LOWER": {"a": "(text)", "d": "텍스트 문자열의 모든 문자를 소문자로 변환합니다.", "ad": "은(는) 소문자로 바꾸려는 텍스트입니다. 문자가 아닌 경우는 변환되지 않습니다."}, "MID": {"a": "(text; start_num; num_chars)", "d": "문자열의 지정 위치에서 문자를 지정한 개수만큼 돌려줍니다.", "ad": "은(는) 돌려줄 문자들이 포함된 문자열입니다.!은(는) 돌려줄 문자열에서 첫째 문자의 위치입니다. Text에서 첫 문자는 1이 됩니다.!은(는) Text에서 돌려줄 문자 개수를 지정합니다."}, "MIDB": {"a": "(text; start_num; num_chars)", "d": "는 지정한 바이트 수에 따라 문자열의 지정한 위치로부터 지정한 개수의 문자를 표시합니다. 는 DBCS(더블바이트 문자 집합)를 사용하는 언어에 사용합니다. DBCS를 지원하는 언어로는 한국어, 일본어, 중국어.", "ad": "은(는) 돌려줄 문자들이 포함된 문자열입니다.!은(는) 돌려줄 문자열에서 첫째 문자의 위치입니다. Text에서 첫 문자는 1이 됩니다.!은(는) Text에서 돌려줄 문자 개수를 지정합니다."}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "로캘 독립적 방식으로 텍스트를 숫자로 변환합니다.", "ad": "은(는) 변환할 숫자를 나타내는 문자열입니다.!은(는) 문자열에서 소수 구분 기호로 사용되는 문자입니다.!은(는) 문자열에서 그룹 구분 기호로 사용되는 문자입니다."}, "PROPER": {"a": "(text)", "d": "각 단어의 첫째 문자를 대문자로 변환하고 나머지 문자는 소문자로 변환합니다.", "ad": "은(는) 인용 부호로 둘러싸인 텍스트, 텍스트를 출력하는 수식 또는 텍스트가 있는 셀의 주소입니다."}, "REPLACE": {"a": "(old_text; start_num; num_chars; new_text)", "d": "텍스트의 일부를 다른 텍스트로 바꿉니다.", "ad": "은(는) 일부분을 바꾸려는 텍스트입니다.!은(는) old_text에서 바꾸기를 시작할 위치로서, 문자 단위로 지정합니다.!은(는) old_text에서 바꾸려는 문자의 개수입니다.!은(는) old_text의 일부를 대체할 새 텍스트입니다."}, "REPLACEB": {"a": "(old_text; start_num; num_chars; new_text)", "d": "는 지정한 바이트 수에 따라 텍스트 문자열의 일부를 다른 텍스트 문자열로 바꿉니다. 는 DBCS(더블바이트 문자 집합)를 사용하는 언어에 사용합니다. DBCS를 지원하는 언어로는 한국어, 일본어, 중국어.", "ad": "은(는) 일부분을 바꾸려는 텍스트입니다.!은(는) old_text에서 바꾸기를 시작할 위치로서, 문자 단위로 지정합니다.!은(는) old_text에서 바꾸려는 문자의 개수입니다.!은(는) old_text의 일부를 대체할 새 텍스트입니다."}, "REPT": {"a": "(text; number_times)", "d": "텍스트를 지정한 횟수만큼 반복합니다.", "ad": "은(는) 반복하려는 텍스트입니다.!은(는) 0보다 큰 숫자로 반복 횟수를 지정합니다."}, "RIGHT": {"a": "(text; [num_chars])", "d": "텍스트 문자열의 끝 지점부터 지정한 수만큼의 문자를 반환합니다.", "ad": "은(는) 추출하려는 문자가 들어 있는 텍스트 문자열입니다.!은(는) 추출할 문자 수를 지정합니다. 생략하면 1이 됩니다."}, "RIGHTB": {"a": "(text; [num_chars])", "d": "는 지정한 바이트 수에 따라 텍스트 문자열의 마지막 문자부터 지정된 길이의 문자를 반환합니다. 는 DBCS(더블바이트 문자 집합)를 사용하는 언어에 사용합니다. DBCS를 지원하는 언어로는 한국어, 일본어, 중국어.", "ad": "은(는) 추출하려는 문자가 들어 있는 텍스트 문자열입니다.!은(는) 추출할 문자 수를 지정합니다. 생략하면 1이 됩니다."}, "SEARCH": {"a": "(find_text; within_text; [start_num])", "d": "왼쪽에서 오른쪽으로 검색하여 지정한 문자 또는 텍스트 스트링이 처음 발견되는 곳에서의 문자 개수를 구합니다(대/소문자 구분 안 함).", "ad": "은(는) 찾으려는 텍스트입니다. ? 및 *와 같은 와일드카드를 사용할 수도 있습니다. 실제 ?와 * 문자를 찾으려면 ~?와 ~*를 사용하십시오.!은(는) find_text를 찾고자 하는 곳의 텍스트입니다.!은(는) within_text에서 왼쪽에서부터 검색을 시작하고자 하는 곳의 문자 개수입니다. 생략하면 1이 사용됩니다."}, "SEARCHB": {"a": "(find_text; within_text; [start_num])", "d": "함수는 두 번째 텍스트 문자열에서 지정된 텍스트 문자열을 검색하고, 두 번째 텍스트 문자열의 첫 문자를 기준으로 몇 번째 위치에서 첫 번째 텍스트 문자열이 시작하는지 나타내는 값을 반환합니다. 는 DBCS(더블바이트 문자 집합)를 사용하는 언어에 사용합니다. DBCS를 지원하는 언어로는 한국어, 일본어, 중국어.", "ad": "은(는) 찾으려는 텍스트입니다. ? 및 *와 같은 와일드카드를 사용할 수도 있습니다. 실제 ?와 * 문자를 찾으려면 ~?와 ~*를 사용하십시오.!은(는) find_text를 찾고자 하는 곳의 텍스트입니다.!은(는) within_text에서 왼쪽에서부터 검색을 시작하고자 하는 곳의 문자 개수입니다. 생략하면 1이 사용됩니다."}, "SUBSTITUTE": {"a": "(text; old_text; new_text; [instance_num])", "d": "텍스트 중의 old_text를 찾아서 new_text로 바꿉니다.", "ad": "은(는) 찾기 및 바꾸기의 대상이 되는 텍스트입니다.!은(는) 찾아서 new_text로 바꿀 텍스트입니다. 대/소문자가 정확하게 일치해야만 바꿀 수 있습니다.!은(는) old_text와 바꾸려는 새 텍스트입니다.!은(는) 몇 번째의 old_text를 바꿀 것인지 지정합니다."}, "T": {"a": "(value)", "d": "값이 텍스트이면 값을 돌려주고, 텍스트가 아니면 큰따옴표(빈 텍스트)를 돌려줍니다.", "ad": "은(는) 검사하려는 값입니다."}, "TEXT": {"a": "(value; format_text)", "d": " 값을 특정 번호 형식으로 변환합니다.", "ad": "은(는) 숫자 값으로 계산되거나 숫자 값이 있는 셀의 참조 수식입니다.!은(는) [셀 서식] 대화 상자에서 [범주] 탭의 텍스트 를 표시할 때 사용할 수 있습니다."}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "구분 기호를 사용하여 텍스트 문자열의 목록 또는 범위를 연결합니다.", "ad": "각 텍스트 항목 사이에 삽입할 문자 또는 문자열!TRUE(기본값)인 경우 빈 셀을 무시합니다!은(는) 1-252 사이의 텍스트 문자열 또는 연결할 범위입니다"}, "TRIM": {"a": "(text)", "d": "텍스트의 양 끝 공백을 없앱니다.", "ad": "은(는) 공백을 제거할 텍스트입니다."}, "UNICHAR": {"a": "(number)", "d": "지정된 숫자 값이 참조하는 유니코드 문자를 반환합니다.", "ad": "은(는) 문자를 나타내는 유니코드 숫자입니다."}, "UNICODE": {"a": "(text)", "d": "텍스트 첫 문자에 해당하는 숫자(코드 포인트)를 반환합니다.", "ad": "은(는) 유니코드 값을 구하려는 문자입니다."}, "UPPER": {"a": "(text)", "d": "텍스트 문자열을 모두 대문자로 바꿉니다.", "ad": "은(는) 대문자로 바꾸려는 텍스트입니다."}, "VALUE": {"a": "(text)", "d": "텍스트 문자열 인수를 숫자로 바꿉니다.", "ad": "은(는) 바꾸려는 텍스트가 있는 셀의 주소 또는 따옴표로 묶인 텍스트입니다."}, "AVEDEV": {"a": "(number1; [number2]; ...)", "d": "데이터 요소의 절대 편차의 평균을 구합니다. 인수는 숫자, 이름, 배열, 숫자가 들어 있는 참조가 될 수 있습니다.", "ad": "은(는) 절대 편차의 평균을 구하려는 인수로서 255개까지 지정할 수 있습니다."}, "AVERAGE": {"a": "(number1; [number2]; ...)", "d": "인수들의 평균을 구합니다. 인수는 숫자나 이름, 배열, 숫자가 들어 있는 참조 등이 될 수 있습니다.", "ad": "은(는) 평균을 구하고자 하는 값들로서 255개까지 지정할 수 있습니다."}, "AVERAGEA": {"a": "(value1; [value2]; ...)", "d": "인수들의 평균(산술 평균)을 구합니다. 인수에서 텍스트와 FALSE는 0으로 계산하며 TRUE는 1로 계산합니다. 인수는 숫자, 이름, 배열, 참조가 될 수 있습니다.", "ad": "은(는) 평균을 구할 인수로서 255개까지 지정할 수 있습니다."}, "AVERAGEIF": {"a": "(range; criteria; [average_range])", "d": "주어진 조건에 따라 지정되는 셀의 평균(산술 평균)을 구합니다.", "ad": "은(는) 계산할 셀의 범위입니다!은(는) 숫자, 식 또는 텍스트 형식의 조건으로 평균을 구할 셀을 정의합니다!은(는) 평균을 구하는 데 사용할 실제 셀입니다. 생략하면 범위 안에 있는 셀이 사용됩니다"}, "AVERAGEIFS": {"a": "(average_range; criteria_range; criteria; ...)", "d": "주어진 조건에 따라 지정되는 셀의 평균(산술 평균)을 구합니다.", "ad": "은(는) 평균을 구하는 데 사용할 실제 셀입니다.!은(는) 특정 조건에 따라 계산할 셀의 범위입니다.!은(는) 숫자, 식 또는 텍스트 형식의 조건으로 평균을 구할 셀을 정의합니다."}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "누적 베타 확률 밀도 함수 값을 구합니다.", "ad": "은(는) 함수를 계산하려는 값으로서 A와 B 사이의 값입니다.!은(는) 분포의 매개 변수로서 0보다 큰 값이어야 합니다.!은(는) 분포의 매개 변수로서 0보다 큰 값이어야 합니다.!은(는) 옵션이며 x 구간에서의 하한값입니다. 생략하면 A가 0이 됩니다.!은(는) 옵션이며 x 구간에서의 상한값입니다. 생략하면 B가 1이 됩니다."}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "역 누적 베타 확률 밀도 함수 (BETADIST) 값을 구합니다.", "ad": "은(는) 베타 분포를 따르는 확률값입니다.!은(는) 분포의 매개 변수로서 0보다 큰 값이어야 합니다.!은(는) 분포의 매개 변수로서 0보다 큰 값이어야 합니다.!은(는) x가 취할 수 있는 최소값입니다. 생략하면 A가 0이 됩니다.!은(는) x가 취할 수 있는 최대값입니다. 생략하면 B가 1이 됩니다."}, "BETA.DIST": {"a": "(x; alpha; beta; cumulative; [A]; [B])", "d": "베타 확률 분포 함수 값을 구합니다.", "ad": "은(는) 함수를 계산하려는 값으로서 A와 B 사이의 값입니다.!은(는) 분포의 매개 변수로서 0보다 큰 값이어야 합니다.!은(는) 분포의 매개 변수로서 0보다 큰 값이어야 합니다.!은(는) 함수의 형태를 결정하는 논리값입니다. 누적 분포 함수에는 TRUE를, 확률 밀도 함수에는 FALSE를 사용합니다.!은(는) 옵션이며 x 구간에서의 하한값입니다. 생략하면 A가 0이 됩니다.!은(는) 옵션이며 x 구간에서의 상한값입니다. 생략하면 B가 1이 됩니다."}, "BETA.INV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "역 누적 베타 확률 밀도 함수 값을 구합니다.", "ad": "은(는) 베타 분포를 따르는 확률값입니다.!은(는) 분포의 매개 변수로서 0보다 큰 값이어야 합니다.!은(는) 분포의 매개 변수로서 0보다 큰 값이어야 합니다.!은(는) x가 취할 수 있는 최소값입니다. 생략하면 A가 0이 됩니다.!은(는) x가 취할 수 있는 최대값입니다. 생략하면 B가 1이 됩니다."}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "개별항 이항 분포 확률을 구합니다.", "ad": "은(는) trials만큼의 시행 중 성공할 횟수입니다.!은(는) 독립적 시행 횟수입니다.!은(는) 각 시행에서의 성공 확률입니다.!은(는) 함수의 형태를 결정하는 논리값입니다. 누적 분포 함수에는 TRUE를, 확률 함수에는 FALSE를 사용합니다."}, "BINOM.DIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "개별항 이항 분포 확률을 구합니다.", "ad": "은(는) trials만큼의 시행 중 성공할 횟수입니다.!은(는) 독립적 시행 횟수입니다.!은(는) 각 시행에서의 성공 확률입니다.!은(는) 함수의 형태를 결정하는 논리 값입니다. 누적 분포 함수에는 TRUE를, 확률 함수에는 FALSE를 사용합니다."}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "이항 분포를 사용한 시행 결과의 확률을 반환합니다.", "ad": "은(는) 독립적 시행 횟수입니다.!은(는) 각 시행에서의 성공 확률입니다.!은(는) 성공한 시행 횟수입니다.!이 함수는 제공될 경우 성공한 시행 횟수가 number_s와 number_s2 사이에 있을 확률을 반환합니다."}, "BINOM.INV": {"a": "(trials; probability_s; alpha)", "d": "누적 이항 분포가 기준치 이상이 되는 값 중 최소값을 구합니다.", "ad": "은(는) 베르누이 시행 횟수입니다.!은(는) 각 시행의 성공 확률입니다. 값은 경계 값을 포함하며 0에서 1까지입니다.!은(는) 기준치입니다. 값은 경계 값을 포함하며 0에서 1까지입니다."}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "카이 제곱 분포의 우측 검정 확률을 구합니다.", "ad": "은(는) 분포를 계산하려는 값으로 0 또는 양수여야 합니다.!은(는) 1에서 10^10까지의 자유도입니다. 단, 10^10은 제외됩니다."}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "카이 제곱 분포의 역 우측 검정 확률을 구합니다.", "ad": "은(는) 카이 제곱 분포를 따르는 확률로, 값은 0에서 1까지입니다.!은(는) 1에서 10^10까지의 자유도입니다. 단, 10^10은 제외됩니다."}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "독립 검증 결과를 구합니다. 통계적이고 적절한 자유도에 대한 카이 제곱 분포값을 의미합니다.", "ad": "은(는) 기대값을 검증하기 위한 관측값이 있는 데이터 범위입니다.!은(는) 각 행과 열의 합을 곱한 값들의 전체 총계에 대한 비율이 있는 데이터 범위입니다."}, "CHISQ.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "카이 제곱 분포의 좌측 검정 확률을 구합니다.", "ad": "은(는) 분포를 계산하려는 값으로 0 또는 양수여야 합니다.!은(는) 1에서 10^10까지의 자유도입니다. 단, 10^10은(는) 제외됩니다.!은(는) 함수의 형태를 결정하는 논리값입니다. 누적 분포 함수에는 TRUE를, 확률 밀도 함수에는 FALSE를 사용합니다."}, "CHISQ.DIST.RT": {"a": "(x; deg_freedom)", "d": "카이 제곱 분포의 우측 검정 확률을 구합니다.", "ad": "은(는) 분포를 계산하려는 값으로 0 또는 양수여야 합니다.!은(는) 1에서 10^10까지의 자유도입니다. 단, 10^10은 제외됩니다."}, "CHISQ.INV": {"a": "(probability; deg_freedom)", "d": "카이 제곱 분포의 역 좌측 검정 확률을 구합니다.", "ad": "은(는) 카이 제곱 분포를 따르는 확률로, 값은 0에서 1까지입니다.!은(는) 1에서 10^10까지의 자유도입니다. 단, 10^10은 제외됩니다."}, "CHISQ.INV.RT": {"a": "(probability; deg_freedom)", "d": "카이 제곱 분포의 역 우측 검정 확률을 구합니다.", "ad": "은(는) 카이 제곱 분포를 따르는 확률로, 값은 0에서 1까지입니다.!은(는) 1에서 10^10까지의 자유도입니다. 단, 10^10은 제외됩니다."}, "CHISQ.TEST": {"a": "(actual_range; expected_range)", "d": "독립 검증 결과를 구합니다. 통계적이고 적절한 자유도에 대한 카이 제곱 분포값을 의미합니다.", "ad": "은(는) 기대값을 검증하기 위한 관측값이 있는 데이터 범위입니다.!은(는) 각 행과 열의 합을 곱한 값들의 전체 총계에 대한 비율이 있는 데이터 범위입니다."}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "정규 분포를 사용한 모집단 평균의 신뢰 구간을 나타냅니다.", "ad": "은(는) 신뢰도를 계산하는 데 쓰이는 유의 수준으로 0보다 크고 1보다 작아야 합니다.!은(는) 모집단의 표준 편차이며 알고 있다고 가정합니다. standard_dev는 0보다 큰 값이어야 합니다.!은(는) 표본의 크기입니다."}, "CONFIDENCE.NORM": {"a": "(alpha; standard_dev; size)", "d": "정규 분포를 사용하는 모집단 평균의 신뢰 구간을 나타냅니다.", "ad": "은(는) 신뢰도를 계산하는 데 쓰이는 유의 수준으로 0보다 크고 1보다 작아야 합니다.!은(는) 모집단의 표준 편차이며 알고 있다고 가정합니다. standard_dev는 0보다 큰 값이어야 합니다.!은(는) 표본의 크기입니다."}, "CONFIDENCE.T": {"a": "(alpha; standard_dev; size)", "d": "스튜던트 t-분포를 사용하는 모집단 평균의 신뢰 구간을 나타냅니다.", "ad": "은(는) 신뢰도를 계산하는 데 쓰이는 유의 수준으로 0보다 크고 1보다 작아야 합니다.!은(는) 모집단의 표준 편차이며 알고 있다고 가정합니다. standard_dev는 0보다 큰 값이어야 합니다.!은(는) 표본의 크기입니다."}, "CORREL": {"a": "(array1; array2)", "d": "두 데이터 집합 사이의 상관 계수를 구합니다.", "ad": "은(는) 첫째 값들의 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다.!은(는) 둘째 값들의 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다."}, "COUNT": {"a": "(value1; [value2]; ...)", "d": "범위에서 숫자가 포함된 셀의 개수를 구합니다.", "ad": "은(는) 최대 255개의 인수로서 다양한 데이터 유형을 포함하거나 참조할 수 있습니다. 이 가운데서 숫자만을 셉니다."}, "COUNTA": {"a": "(value1; [value2]; ...)", "d": "범위에서 비어 있지 않은 셀의 개수를 구합니다.", "ad": "은(는) 최대 255개의 인수로서 개수를 구할 값과 셀입니다. 값에는 모든 정보가 포함될 수 있습니다."}, "COUNTBLANK": {"a": "(range)", "d": "범위에서 비어 있는 셀의 개수를 구합니다.", "ad": "은(는) 빈 셀의 개수를 구하려는 셀 범위입니다."}, "COUNTIF": {"a": "(range; criteria)", "d": "지정한 범위 내에서 조건에 맞는 셀의 개수를 구합니다.", "ad": "은(는) 조건에 맞는 셀의 수를 구하려는 셀 범위입니다.!은(는) 숫자, 식, 텍스트 형태의 조건입니다."}, "COUNTIFS": {"a": "(criteria_range; criteria; ...)", "d": "범위 내에서 주어진 조건에 맞는 셀의 개수를 셉니다.", "ad": "은(는) 조건을 검사할 셀 범위입니다.!은(는) 숫자, 식, 텍스트 형식으로 된 조건으로 개수를 계산할 셀을 정의합니다."}, "COVAR": {"a": "(array1; array2)", "d": "두 데이터 집합 사이의 공 분산을 구합니다.", "ad": "은(는) 첫째 값들의 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다.!은(는) 둘째 값들의 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다."}, "COVARIANCE.P": {"a": "(array1; array2)", "d": "두 데이터 집합 사이의 모집단 공 분산을 구합니다.", "ad": "은(는) 첫째 값들의 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다.!은(는) 둘째 값들의 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다."}, "COVARIANCE.S": {"a": "(array1; array2)", "d": "두 데이터 집합 사이의 표본 집단 공 분산을 구합니다.", "ad": "은(는) 첫째 값들의 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다.!은(는) 둘째 값들의 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다."}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "누적 이항 분포가 기준치 이상이 되는 값 중 최소값을 구합니다.", "ad": "은(는) 베르누이 시행 횟수입니다.!은(는) 각 시행의 성공 확률입니다. 값은 경계 값을 포함하며 0에서 1까지입니다.!은(는) 기준치입니다. 값은 경계 값을 포함하며 0에서 1까지입니다."}, "DEVSQ": {"a": "(number1; [number2]; ...)", "d": "표본 평균으로부터 편차의 제곱의 합을 구합니다.", "ad": "은(는) 편차 제곱의 합을 구하려는 인수이거나 배열 또는 참조 영역으로서 255개까지 지정할 수 있습니다."}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "지수 분포값을 구합니다.", "ad": "은(는) 함수를 계산할 값으로 0 또는 양수이어야 합니다.!은(는) 매개 변수 값으로 양수여야 합니다.!은(는) 지수 함수의 형태를 나타내는 논리값입니다. 누적 분포 함수는 TRUE를, 확률 밀도 함수는 FALSE를 사용합니다."}, "EXPON.DIST": {"a": "(x; lambda; cumulative)", "d": "지수 분포값을 구합니다.", "ad": "은(는) 함수를 계산할 값으로 0 또는 양수이어야 합니다.!은(는) 매개 변수 값으로 양수여야 합니다.!은(는) 지수 함수의 형태를 나타내는 논리값입니다. 누적 분포 함수는 TRUE를, 확률 밀도 함수는 FALSE를 사용합니다."}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "두 데이터 집합에 대해 (우측 검정) F 확률 분포값(분포도)을 구합니다.", "ad": "은(는) 분포값을 계산하려는 값으로 0 또는 양수여야 합니다.!은(는) 분자의 자유도(1에서 10^10까지)입니다. 10^10은(는) 제외됩니다.!은(는) 분모의 자유도(1에서 10^10까지)입니다. 10^10은(는) 제외됩니다."}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "(우측 검정) F 확률 분포의 역함수 값을 구합니다. p=FDIST(x,...)이면 FINV(p,...)=x입니다.", "ad": "은(는) F 누적 분포를 따르는 확률입니다. 값은 경계값을 포함하며 0에서 1까지입니다.!은(는) 분자의 자유도(1에서 10^10까지)입니다. 10^10은(는) 제외됩니다.!은(는) 분모의 자유도(1에서 10^10까지)입니다. 10^10은(는) 제외됩니다."}, "FTEST": {"a": "(array1; array2)", "d": "array1과 array2의 분산이 크게 차이가 나지 않는 경우 양측 확률인 F-검정 결과를 구합니다.", "ad": "은(는) 첫째 데이터 배열 또는 범위이며 숫자 또는 이름, 배열 또는 숫자가 들어 있는 참조가 될 수 있습니다. 공백은 무시됩니다.!은(는) 둘째 데이터 배열 또는 범위이며 숫자 또는 이름, 배열 또는 숫자가 들어 있는 참조가 될 수 있습니다. 공백은 무시됩니다."}, "F.DIST": {"a": "(x; deg_freedom1; deg_freedom2; cumulative)", "d": "두 데이터 집합에 대해 좌측 F 확률 분포값(분포도)을 구합니다.", "ad": "은(는) 분포값을 계산하려는 값으로 0 또는 양수여야 합니다.!은(는) 분자의 자유도(1에서 10^10까지)입니다. 10^10은(는) 제외됩니다.!은(는) 분모의 자유도(1에서 10^10까지)입니다. 10^10은(는) 제외됩니다.!은(는) 함수의 형태를 결정하는 논리값입니다. TRUE이면 누적 분포 함수, FALSE이거나 생략하면 확률 질량 함수를 구합니다."}, "F.DIST.RT": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "두 데이터 집합에 대해 우측 F 확률 분포값(분포도)을 구합니다.", "ad": "은(는) 분포값을 계산하려는 값으로 0 또는 양수여야 합니다.!은(는) 분자의 자유도(1에서 10^10까지)입니다. 10^10은 제외됩니다.!은(는) 분모의 자유도(1에서 10^10까지)입니다. 10^10은 제외됩니다."}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "좌측 F 확률 분포의 역함수 값을 구합니다. p = F.DIST(x,...)이면 F.INV(p,...)=x입니다.", "ad": "은(는) F 누적 분포를 따르는 확률입니다. 값은 경계값을 포함하며 0에서 1까지입니다.!은(는) 분자의 자유도(1에서 10^10까지)입니다. 10^10은 제외됩니다.!은(는) 분모의 자유도(1에서 10^10까지)입니다. 10^10은 제외됩니다."}, "F.INV.RT": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "우측 F 확률 분포의 역함수 값을 구합니다. p=F.DIST.RT(x,...)이면 F.INV.RT(p,...)=x입니다.", "ad": "은(는) F 누적 분포를 따르는 확률입니다. 값은 경계값을 포함하며 0에서 1까지입니다.!은(는) 분자의 자유도(1에서 10^10까지)입니다. 10^10은 제외됩니다.!은(는) 분모의 자유도(1에서 10^10까지)입니다. 10^10은 제외됩니다."}, "F.TEST": {"a": "(array1; array2)", "d": "array1과 array2의 분산이 크게 차이가 나지 않는 경우 양측 확률인 F-검정 결과를 구합니다.", "ad": "은(는) 첫째 데이터 배열 또는 범위이며 숫자 또는 이름, 배열 또는 숫자가 들어 있는 참조가 될 수 있습니다. 공백은 무시됩니다.!은(는) 둘째 데이터 배열 또는 범위이며 숫자 또는 이름, 배열 또는 숫자가 들어 있는 참조가 될 수 있습니다. 공백은 무시됩니다."}, "FISHER": {"a": "(x)", "d": "Fisher 변환 값을 구합니다.", "ad": "은(는) 변환하려는 수입니다. 범위는 -1에서 1까지이며 -1과 1은 제외됩니다."}, "FISHERINV": {"a": "(y)", "d": "Fisher 변환의 역변환 값을 구합니다. y=FISHER(x)이면 FISHERINV(y)=x입니다.", "ad": "은(는) 역변환 값을 구하려는 값입니다."}, "FORECAST": {"a": "(x; known_y's; known_x's)", "d": "기존 값에 의거한 선형 추세에 따라 예측값을 구합니다.", "ad": "은(는) 값(y 값)을 예측하려는 데이터 요소(x 값)입니다. 반드시 수치 데이터여야 합니다.!은(는) 종속 데이터 배열 또는 범위입니다.!은(는) 독립 데이터 배열 또는 범위입니다. known_x's의 분산은 0이 되면 안됩니다."}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "지수평활법을 사용하여 특정 미래 대상 날짜에 대해 예측된 값을 반환합니다.", "ad": "은(는) Spreadsheet Editor에서 값(y 값)을 예측하려는 데이터 요소(x 값)입니다. 이는 시간 표시줄에서의 값 패턴과 이어져야 합니다.!은(는) 예측하는 숫자 데이터의 배열 또는 범위입니다.!은(는) 독립 데이터(숫자)의 배열 또는 범위입니다. 시간 표시줄의 날짜는 날짜 간에 일정한 간격이 있어야 하며 영(0)일 수 없습니다.!은(는) 선택적 숫자 값으로, 계절적 패턴의 길이를 나타냅니다. 기본 값인 1은 계절성이 자동으로 감지됨을 나타냅니다.!은(는) 누락된 값을 처리하기 위한 선택 값입니다. 기본 값인 1은 누락된 값을 보간법으로 대체하며 0은 누락된 값을 0으로 대체합니다.!은(는) 동일한 타임스탬프를 가진 여러 값을 집계하는 데 사용하는 숫자 값입니다(선택 사항). 비워두면 Spreadsheet Editor에서 평균 값을 계산합니다."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "지정된 대상 날짜의 예측값에 대한 신뢰 구간을 반환합니다.", "ad": "은(는) Spreadsheet Editor에서 값(y 값)을 예측하려는 데이터 요소(x 값)입니다. 이는 시간 표시줄에서의 값 패턴과 이어져야 합니다.!은(는) 예측하는 숫자 데이터의 배열 또는 범위입니다.!은(는) 독립 데이터(숫자)의 배열 또는 범위입니다. 시간 표시줄의 날짜는 날짜 간에 일정한 간격이 있어야 하며 영(0)일 수 없습니다.!은(는) 0과 1 사이의 숫자로, 계산된 신뢰 구간에 대한 신뢰 수준을 나타냅니다. 기본 값은 0.95입니다.!은(는) 선택적 숫자 값으로, 계절적 패턴의 길이를 나타냅니다. 기본 값인 1은 계절성이 자동으로 감지됨을 나타냅니다.!은(는) 누락된 값을 처리하기 위한 선택 값입니다. 기본 값인 1은 누락된 값을 보간법으로 대체하며 0은 누락된 값을 0으로 대체합니다.!은(는) 동일한 타임스탬프를 가진 여러 값을 집계하는 데 사용하는 숫자 값입니다(선택 사항). 비워두면 Spreadsheet Editor에서 평균 값을 계산합니다."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "애플리케이션에서 지정된 시계열에 대해 파악한 반복 패턴의 길이를 반환합니다.", "ad": "은(는) 예측하는 숫자 데이터의 배열 또는 범위입니다.!은(는) 독립 데이터(숫자)의 배열 또는 범위입니다. 시간 표시줄의 날짜는 날짜 간에 일정한 간격이 있어야 하며 영(0)일 수 없습니다.!은(는) 누락된 값을 처리하기 위한 선택 값입니다. 기본 값인 1은 누락된 값을 보간법으로 대체하며 0은 누락된 값을 0으로 대체합니다.!은(는) 동일한 타임스탬프를 가진 여러 값을 집계하는 데 사용하는 숫자 값입니다(선택 사항). 비워두면 Spreadsheet Editor에서 평균 값을 계산합니다."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "예측에 대해 요청된 통계를 반환합니다.", "ad": "은(는) 예측하는 숫자 데이터의 배열 또는 범위입니다.!은(는) 독립 데이터(숫자)의 배열 또는 범위입니다. 시간 표시줄의 날짜는 날짜 간에 일정한 간격이 있어야 하며 영(0)일 수 없습니다.!은(는) 1~8 사이의 숫자로, Spreadsheet Editor에서 계산된 예측에 대해 반환되는 통계의 유형을 나타냅니다.!은(는) 선택적 숫자 값으로, 계절적 패턴의 길이를 나타냅니다. 기본 값인 1은 계절성이 자동으로 감지됨을 나타냅니다.!은(는) 누락된 값을 처리하기 위한 선택 값입니다. 기본 값인 1은 누락된 값을 보간법으로 대체하며 0은 누락된 값을 0으로 대체합니다.!은(는) 동일한 타임스탬프를 가진 여러 값을 집계하는 데 사용하는 숫자 값입니다(선택 사항). 비워두면 Spreadsheet Editor에서 평균 값을 계산합니다."}, "FORECAST.LINEAR": {"a": "(x; known_y's; known_x's)", "d": "기존 값에 의거한 선형 추세에 따라 예측값을 구합니다.", "ad": "은(는) 값(y 값)을 예측하려는 데이터 요소(x 값)입니다. 반드시 수치 데이터여야 합니다.!은(는) 종속 데이터 배열 또는 범위입니다.!은(는) 독립 데이터 배열 또는 범위입니다. known_x's의 분산은 0이 되면 안됩니다."}, "FREQUENCY": {"a": "(data_array; bins_array)", "d": "도수 분포를 세로 배열의 형태로 구합니다.", "ad": "은(는) 빈도수를 계산하려는 값이 있는 셀 주소 또는 배열입니다.!은(는) data_array를 분류하는 데 필요한 구간값들이 있는 셀 주소 또는 배열입니다."}, "GAMMA": {"a": "(x)", "d": "감마 함수 값을 반환합니다.", "ad": "은(는) 감마를 구할 값입니다."}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "감마 분포값을 구합니다.", "ad": "은(는) 분포값을 계산하려는 위치에서의 값으로 0 또는 양수여야 합니다.!은(는) 분포의 매개 변수로 양수입니다.!은(는) 분포의 매개 변수로 양수입니다. 1이면 표준 감마 분포값이 산출됩니다.!은(는) 함수의 형태를 결정하는 논리값입니다. TRUE이면 누적 분포 함수, FALSE이거나 생략하면 확률 밀도 함수를 구합니다."}, "GAMMA.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "감마 분포값을 구합니다.", "ad": "은(는) 분포값을 계산하려는 위치에서의 값으로 0 또는 양수여야 합니다.!은(는) 분포의 매개 변수로 양수입니다.!은(는) 분포의 매개 변수로 양수입니다. 1이면 표준 감마 분포값이 산출됩니다.!은(는) 함수의 형태를 결정하는 논리값입니다. TRUE이면 누적 분포 함수, FALSE이거나 생략하면 확률 밀도 함수를 구합니다."}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "감마 누적 분포의 역함수를 구합니다. p = GAMMADIST(x,...)이면 GAMMAINV(p,...) = x입니다.", "ad": "은(는) 감마 분포를 따르는 확률입니다. 범위는 0에서 1까지입니다.!은(는) 분포의 매개 변수로서 양수입니다.!은(는) 분포의 매개 변수입니다. 1이면 표준 감마 분포의 역함수가 산출됩니다."}, "GAMMA.INV": {"a": "(probability; alpha; beta)", "d": "감마 누적 분포의 역함수를 구합니다. p = GAMMA.DIST(x,...)이면 GAMMA.INV(p,...) = x입니다.", "ad": "은(는) 감마 분포를 따르는 확률입니다. 범위는 0에서 1까지입니다.!은(는) 분포의 매개 변수로서 양수입니다.!은(는) 분포의 매개 변수입니다. 1이면 표준 감마 분포의 역함수가 산출됩니다."}, "GAMMALN": {"a": "(x)", "d": "감마 함수의 자연 로그값을 구합니다.", "ad": "은(는) GAMMALN을 구하려는 양수의 값입니다."}, "GAMMALN.PRECISE": {"a": "(x)", "d": "감마 함수의 자연 로그값을 구합니다.", "ad": "은(는) GAMMALN.PRECISE를 구하려는 양수의 값입니다."}, "GAUSS": {"a": "(x)", "d": "표준 정규 누적 분포값보다 0.5 작은 값을 반환합니다.", "ad": "은(는) 분포하려는 값입니다."}, "GEOMEAN": {"a": "(number1; [number2]; ...)", "d": "양수 데이터 범위나 배열의 기하 평균을 구합니다.", "ad": "은(는) 기하 평균을 구하려는 인수로서 255개까지 지정할 수 있습니다."}, "GROWTH": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "지정한 값들의 지수 추세를 구합니다.", "ad": "은(는) 양수들의 집합이며 y = b*m^x 식에서 이미 알고 있는 y 값의 집합입니다.!은(는) 옵션이며 y = b*m^x 식에서 Known_y와 크기가 일치하는 이미 알고 있는 x 값의 집합입니다.!은(는) y 값의 추세를 계산하고자 하는 새로운 x 값의 집합입니다.!은(는) TRUE로 설정하면 상수 b를 정상적으로 계산하며 FALSE로 설정하거나 생략하면 b를 1로 지정하는 논리값입니다."}, "HARMEAN": {"a": "(number1; [number2]; ...)", "d": "양수 데이터의 조화 평균을 구합니다.", "ad": "은(는) 조화 평균을 구하려는 인수로서 255개까지 지정할 수 있습니다."}, "HYPGEOM.DIST": {"a": "(sample_s; number_sample; population_s; number_pop; cumulative)", "d": "초기하 분포값을 구합니다.", "ad": "은(는) 표본에서의 성공 횟수입니다.!은(는) 표본의 크기입니다.!은(는) 모집단에서의 성공 횟수입니다.!은(는) 모집단의 크기입니다.!은(는) 함수의 형태를 결정하는 논리값입니다. 누적 분포 함수에는 TRUE를, 확률 밀도 함수에는 FALSE를 사용합니다."}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "초기하 분포값을 구합니다.", "ad": "은(는) 표본에서의 성공 횟수입니다.!은(는) 표본의 크기입니다.!은(는) 모집단에서의 성공 횟수입니다.!은(는) 모집단의 크기입니다."}, "INTERCEPT": {"a": "(known_y's; known_x's)", "d": "주어진 x와 y 값에 의거한 선형 회귀선의 y 절편을 구합니다.", "ad": "은(는) 관측의 종속 데이터 배열 또는 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다.!은(는) 관측의 독립 데이터 배열 또는 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다."}, "KURT": {"a": "(number1; [number2]; ...)", "d": "데이터 집합의 첨도를 구합니다.", "ad": "은(는) 첨도를 구하려는 인수로서 255개까지 지정할 수 있습니다."}, "LARGE": {"a": "(array; k)", "d": "데이터 집합에서 k번째로 큰 값을 구합니다(예: 데이터 집합에서 5번째로 큰 값).", "ad": "은(는) k번째로 큰 값을 결정하는 데 필요한 배열 또는 데이터 범위입니다.!은(는) 배열 또는 셀 범위에서 몇 번째로 큰 값을 구할 것인지 지정합니다."}, "LINEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "최소 자승법을 이용하여 직선을 근사시킴으로써 지정한 값들의 선형 추세 계수를 구합니다.", "ad": "은(는) y = mx + b 식에서 이미 알고 있는 y 값의 집합입니다.!은(는) 옵션이며 y = mx + b 식에서 이미 알고 있는 x 값의 집합입니다.!은(는) TRUE로 설정하거나 생략하면 상수 b를 정상적으로 계산하고 FALSE로 설정하면 0으로 지정하는 논리값입니다.!은(는) TRUE로 설정하면 추가적인 회귀 통계 항목을 함께 구하고 FALSE로 설정하거나 생략하면 m-계수와 상수 b를 구하는 논리값입니다."}, "LOGEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "지정한 값들의 지수 추세 계수를 구합니다.", "ad": "은(는) y = b*m^x 식에서 이미 알고 있는 y 값의 집합입니다.!은(는) 옵션이며 y = b*m^x 식에서 이미 알고 있는 x 값의 집합입니다.!은(는) TRUE로 설정하거나 생략하면 상수 b를 정상적으로 계산하고 FALSE로 설정하면 b를 1로 지정하는 논리값입니다.!은(는) TRUE로 설정하면 추가적인 회귀 통계 항목을 함께 구하고 FALSE로 설정하거나 생략하면 m-계수와 상수 b를 구하는 논리값입니다."}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "로그 정규 분포의 역함수 값을 구합니다. ln(x)는 평균과 표준 편차를 매개 변수로 갖는 정규 분포입니다.", "ad": "은(는) 로그 정규 분포를 따르는 확률입니다. 값은 0에서 1까지입니다.!은(는) ln(x)의 평균입니다.!은(는) ln(x)의 표준 편차로 양수입니다."}, "LOGNORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "x에서의 로그 정규 분포값을 구합니다. ln(x)는 평균과 표준 편차를 매개 변수로 갖는 정규 분포입니다.", "ad": "은(는) 함수값을 계산하려는 위치에서의 값으로 양수입니다.!은(는) ln(x)의 평균입니다.!은(는) ln(x)의 표준 편차로 양수입니다.!은(는) 함수의 형태를 결정하는 논리값입니다. 누적 분포 함수에는 TRUE를, 확률 밀도 함수에는 FALSE를 사용합니다."}, "LOGNORM.INV": {"a": "(probability; mean; standard_dev)", "d": "로그 정규 분포의 역함수 값을 구합니다. ln(x)는 평균과 표준 편차를 매개 변수로 갖는 정규 분포입니다.", "ad": "은(는) 로그 정규 분포를 따르는 확률입니다. 값은 0에서 1까지입니다.!은(는) ln(x)의 평균입니다.!은(는) ln(x)의 표준 편차로 양수입니다."}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "x에서의 로그 정규 누적 분포값을 구합니다. ln(x)는 평균과 표준 편차를 매개 변수로 갖는 정규 분포입니다.", "ad": "은(는) 함수값을 계산하려는 위치에서의 값으로 양수입니다.!은(는) ln(x)의 평균입니다.!은(는) ln(x)의 표준 편차로 양수입니다."}, "MAX": {"a": "(number1; [number2]; ...)", "d": "최대값을 구합니다. 논리값과 텍스트는 제외됩니다.", "ad": "은(는) 최대값을 구하려는 수로서 255개까지 지정할 수 있습니다."}, "MAXA": {"a": "(value1; [value2]; ...)", "d": "인수 목록에서 최대값을 구합니다. 논리값과 텍스트도 포함된 값입니다.", "ad": "은(는) 최대값을 구하려는 값들로서 255개까지 지정할 수 있습니다. 값은 숫자, 빈 셀, 논리값, 텍스트가 될 수 있습니다."}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "주어진 조건에 따라 지정되는 셀 간의 최대 값을 반환합니다.", "ad": "최대 값을 결정하는 셀!은(는) 특정 조건에 대해 평가할 셀의 범위입니다!은(는) 최대 값을 결정할 때 포함할 셀을 정의하는 숫자, 식 또는 텍스트의 유형에 있는 조건입니다"}, "MEDIAN": {"a": "(number1; [number2]; ...)", "d": "주어진 수들의 중간값을 구합니다.", "ad": "은(는) 중간값을 구하려는 수로서 255개까지 지정할 수 있습니다."}, "MIN": {"a": "(number1; [number2]; ...)", "d": "최소값을 구합니다. 논리값과 텍스트는 제외됩니다.", "ad": "은(는) 최소값을 구하려는 수로서 255개까지 지정할 수 있습니다."}, "MINA": {"a": "(value1; [value2]; ...)", "d": "인수 목록에서 최소값을 구합니다. 논리값과 텍스트도 포함된 값입니다.", "ad": "은(는) 최소값을 구하려는 값들로서 255개까지 지정할 수 있습니다. 값은 숫자, 빈 셀, 논리값, 텍스트가 될 수 있습니다."}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "주어진 조건에 따라 지정되는 셀 간의 최소 값을 반환합니다.", "ad": "최소 값을 결정하는 셀!은(는) 특정 조건에 대해 계산할 셀의 범위입니다!은(는) 최소 값을 결정할 때 포함할 셀을 정의하는 숫자, 식 또는 텍스트의 유형에 있는 조건입니다"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "데이터 집합에서 가장 자주 발생하는 값(최빈수)을 구합니다.", "ad": "은(는) 최빈수를 구하려는 인수로서 255개까지 지정할 수 있습니다."}, "MODE.MULT": {"a": "(number1; [number2]; ...)", "d": "데이터 집합에서 가장 자주 발생하는 값(최빈수)의 세로 배열을 구합니다. 가로 배열의 경우 TRANSPOSE(MODE.MULT(number1,number2,...))를 사용합니다.", "ad": "은(는) 최빈수를 구하려는 인수로서 255개까지 지정할 수 있습니다."}, "MODE.SNGL": {"a": "(number1; [number2]; ...)", "d": "데이터 집합에서 가장 자주 발생하는 값(최빈수)을 구합니다.", "ad": "은(는) 최빈수를 구하려는 인수로서 255개까지 지정할 수 있습니다."}, "NEGBINOM.DIST": {"a": "(number_f; number_s; probability_s; cumulative)", "d": "음 이항 분포값을 구합니다. 성공 확률이 Probability_s이고 Number_s번째 성공하기 전에 Number_f번 실패가 있는 경우의 확률을 의미합니다.", "ad": "은(는) 실패 횟수입니다.!은(는) 성공 횟수의 임계치입니다.!은(는) 성공 확률입니다. 값은 0에서 1 사이입니다.!은(는) 함수의 형태를 결정하는 논리값입니다. 누적 분포 함수에는 TRUE를, 확률 밀도 함수에는 FALSE를 사용합니다."}, "NEGBINOMDIST": {"a": "(number_f; number_s; probability_s)", "d": "음 이항 분포값을 구합니다. 성공 확률이 Probability_s이고 Number_s번째 성공하기 전에 Number_f번 실패가 있는 경우의 확률을 의미합니다.", "ad": "은(는) 실패 횟수입니다.!은(는) 성공 횟수의 임계치입니다.!은(는) 성공 확률입니다. 값은 0에서 1 사이입니다."}, "NORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "지정한 평균과 표준 편차에 의거 정규 분포값을 구합니다.", "ad": "은(는) 분포를 구하려는 값입니다.!은(는) 분포의 산술 평균입니다.!은(는) 분포의 표준 편차이며 양수입니다.!은(는) 함수의 형태를 결정하는 논리값입니다. TRUE이면 누적 분포 함수, FALSE이면 확률 밀도 함수를 구합니다."}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "지정한 평균과 표준 편차에 의거 정규 누적 분포값을 구합니다.", "ad": "은(는) 분포를 구하려는 값입니다.!은(는) 분포의 산술 평균입니다.!은(는) 분포의 표준 편차이며 양수입니다.!은(는) 함수의 형태를 결정하는 논리값입니다. TRUE이면 누적 분포 함수, FALSE이면 확률 밀도 함수를 구합니다."}, "NORM.INV": {"a": "(probability; mean; standard_dev)", "d": "지정한 평균과 표준 편차에 의거하여 정규 누적 분포의 역함수 값을 구합니다.", "ad": "은(는) 정규 분포를 따르는 확률입니다. 범위는 0에서 1까지입니다.!은(는) 분포의 산술 평균입니다.!은(는) 분포의 표준 편차로서 양수입니다."}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "지정한 평균과 표준 편차에 의거하여 정규 누적 분포의 역함수 값을 구합니다.", "ad": "은(는) 정규 분포를 따르는 확률입니다. 범위는 0에서 1까지입니다.!은(는) 분포의 산술 평균입니다.!은(는) 분포의 표준 편차로서 양수입니다."}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "표준 정규 누적 분포값을 구합니다. 평균이 0이고 표준 편차가 1인 정규 분포를 의미합니다.", "ad": "은(는) 분포를 구하려는 값입니다.!은(는) 함수의 형태를 결정하는 논리값입니다. 누적 분포 함수에는 TRUE를, 확률 밀도 함수에는 FALSE를 사용합니다."}, "NORMSDIST": {"a": "(z)", "d": "표준 정규 누적 분포값을 구합니다. 평균이 0이고 표준 편차가 1인 정규 분포를 의미합니다.", "ad": "은(는) 분포를 구하려는 값입니다."}, "NORM.S.INV": {"a": "(probability)", "d": "표준 정규 누적 분포의 역함수를 구합니다. 평균이 0이고 표준 편차가 1인 정규 분포를 의미합니다.", "ad": "은(는) 정규 분포를 따르는 확률입니다. 범위는 0에서 1까지입니다."}, "NORMSINV": {"a": "(probability)", "d": "표준 정규 누적 분포의 역함수를 구합니다. 평균이 0이고 표준 편차가 1인 정규 분포를 의미합니다.", "ad": "은(는) 정규 분포를 따르는 확률입니다. 범위는 0에서 1까지입니다."}, "PEARSON": {"a": "(array1; array2)", "d": "피어슨 곱 모멘트 상관 계수 r을 구합니다.", "ad": "은(는) 독립적인 값의 집합입니다.!은(는) 종속적인 값의 집합입니다."}, "PERCENTILE": {"a": "(array; k)", "d": "범위에서 k번째 백분위수를 구합니다.", "ad": "은(는) 상대 순위를 구할 데이터의 범위 또는 배열입니다.!은(는) 0에서 1까지 범위의 백분위수 값입니다."}, "PERCENTILE.EXC": {"a": "(array; k)", "d": "범위에서 k번째 백분위수를 구합니다.이때 k는 경계값을 제외한 0에서 1 사이의 수입니다.", "ad": "은(는) 상대 순위를 구할 데이터의 범위 또는 배열입니다.!은(는) 0에서 1까지 범위의 백분위수 값입니다."}, "PERCENTILE.INC": {"a": "(array; k)", "d": "범위에서 k번째 백분위수를 구합니다. 이때 k는 경계값을 포함한 0에서 1 사이의 수입니다.", "ad": "은(는) 상대 순위를 구할 데이터의 범위 또는 배열입니다.!은(는) 0에서 1까지 범위의 백분위수 값입니다."}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "데이터 집합에서 백분율 순위를 구합니다.", "ad": "은(는) 상대 순위를 구할 데이터의 범위 또는 배열입니다.!은(는) 순위를 알려는 값입니다.!은(는) 구한 백분율 값에 대한 유효 자릿수의 개수를 나타내는 값입니다. 생략되면 세 자리로 표시됩니다(예: 0.xxx%)."}, "PERCENTRANK.EXC": {"a": "(array; x; [significance])", "d": "데이터 집합에서 경계값을 제외한 0에서 1 사이의 백분율 순위를 구합니다.", "ad": "은(는) 상대 순위를 정의하는 숫자 값이 있는 데이터의 범위 또는 배열입니다.!은(는) 순위를 알려고 하는 값입니다.!은(는) 반환된 백분율 값에 대한 유효 자릿수의 개수를 나타내는 값입니다(선택 사항). 생략하면 세 자리로 표시됩니다(예: 0.xxx%)."}, "PERCENTRANK.INC": {"a": "(array; x; [significance])", "d": "데이터 집합에서 경계값을 포함한 0에서 1 사이의 백분율 순위를 구합니다.", "ad": "은(는) 상대 순위를 정의하는 숫자 값이 있는 데이터의 범위 또는 배열입니다.!은(는) 순위를 알려고 하는 값입니다.!은(는) 반환된 백분율 값에 대한 유효 자릿수의 개수를 나타내는 값입니다(선택 사항). 생략하면 세 자리로 표시됩니다(예: 0.xxx%)."}, "PERMUT": {"a": "(number; number_chosen)", "d": "개체 전체에서 선택하여 주어진 개체 수로 만들 수 있는 순열의 수를 구합니다.", "ad": "은(는) 개체의 전체 수입니다.!은(는) 각 순열에서의 개체 수입니다."}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "개체 전체에서 선택하여 주어진 개체 수(반복 포함)로 만들 수 있는 순열의 수를 구합니다.", "ad": "은(는) 개체의 전체 수입니다.!은(는) 각 순열에서의 개체 수입니다."}, "PHI": {"a": "(x)", "d": "표준 정규 분포값의 밀도 함수 값을 반환합니다.", "ad": "은(는) 표준 정규 분포값의 밀도를 구하려는 숫자입니다."}, "POISSON": {"a": "(x; mean; cumulative)", "d": "포아송 확률 분포값을 구합니다.", "ad": "은(는) 사건의 수입니다.!은(는) 기대값으로 양수입니다.!은(는) 확률 분포의 형태를 결정하는 논리값입니다. TRUE이면 누적 포아송 확률을, FALSE이면 포아송 확률 밀도 함수를 구합니다."}, "POISSON.DIST": {"a": "(x; mean; cumulative)", "d": "포아송 확률 분포값을 구합니다.", "ad": "은(는) 사건의 수입니다.!은(는) 기대값으로 양수입니다.!은(는) 확률 분포의 형태를 결정하는 논리값입니다. TRUE이면 누적 포아송 확률을, FALSE이면 포아송 확률 밀도 함수를 구합니다."}, "PROB": {"a": "(x_range; prob_range; lower_limit; [upper_limit])", "d": "영역 내의 값이 최소값을 포함한 두 한계값 사이에 있을 확률을 구합니다.", "ad": "은(는) 확률을 지정할 x 값의 범위입니다.!은(는) x_range의 각 값이 발생할 확률의 집합입니다. 범위는 0에서 1까지이며 0은 제외됩니다.!은(는) 확률을 구하고자 하는 구간의 하한값입니다.!은(는) 확률을 구하고자 하는 구간의 상한값입니다. 생략되면 x_range의 값이 하한값과 같을 때의 확률을 구해줍니다."}, "QUARTILE": {"a": "(array; quart)", "d": "데이터 집합에서 사분위수를 구합니다.", "ad": "은(는) 사분위수를 구하려는 수치 값의 배열 또는 셀 범위입니다.!은(는) 사분위수 값으로서 최소값은 0, 1분위는 1, 중간값은 2, 3분위는 3, 최대값은 4입니다."}, "QUARTILE.INC": {"a": "(array; quart)", "d": "데이터 집합에서 경계값을 포함한 0에서 1 사이의 사분위수를 구합니다.", "ad": "은(는) 사분위수를 구하려는 수치 값의 배열 또는 셀 범위입니다.!은(는) 사분위수 값으로서 최소값은 0, 1분위는 1, 중간값은 2, 3분위는 3, 최대값은 4입니다."}, "QUARTILE.EXC": {"a": "(array; quart)", "d": "데이터 집합에서 경계값을 제외한 0에서 1 사이의 사분위수를 구합니다.", "ad": "은(는) 사분위수를 구하려는 수치 값의 배열 또는 셀 범위입니다.!은(는) 사분위수 값으로서 최소값은 0, 1분위는 1, 중간값은 2, 3분위는 3, 최대값은 4입니다."}, "RANK": {"a": "(number; ref; [order])", "d": "수 목록 내에서 지정한 수의 크기 순위를 구합니다. 목록 내에서 다른 값에 대한 상대적인 크기를 말합니다.", "ad": "은(는) 순위를 구하려는 수입니다.!은(는) 수 목록의 배열 또는 셀 주소입니다. 수 이외의 값은 제외됩니다.!은(는) 순위를 정할 방법을 지정하는 수입니다. 0이나 생략하면 내림차순으로, 0이 아닌 값을 지정하면 오름차순으로 순위가 정해집니다."}, "RANK.AVG": {"a": "(number; ref; [order])", "d": "수 목록 내에서 지정한 수의 크기 순위를 구합니다. 목록 내에서 다른 값에 대한 상대적인 크기를 말합니다. 둘 이상의 값이 순위가 같으면 평균 순위가 반환됩니다.", "ad": "은(는) 순위를 구하려는 수입니다.!은(는) 수 목록의 배열 또는 셀 주소입니다. 수 이외의 값은 제외됩니다.!은(는) 순위를 정할 방법을 지정하는 수입니다. 0이나 생략하면 내림차순으로, 0이 아닌 값을 지정하면 오름차순으로 순위가 정해집니다."}, "RANK.EQ": {"a": "(number; ref; [order])", "d": "수 목록 내에서 지정한 수의 크기 순위를 구합니다. 목록 내에서 다른 값에 대한 상대적인 크기를 말합니다. 둘 이상의 값이 순위가 같으면 해당 값 집합에서 가장 높은 순위가 반환됩니다.", "ad": "은(는) 순위를 구하려는 수입니다.!은(는) 수 목록의 배열 또는 셀 주소입니다. 수 이외의 값은 제외됩니다.!은(는) 순위를 정할 방법을 지정하는 수입니다. 0이나 생략하면 내림차순으로, 0이 아닌 값을 지정하면 오름차순으로 순위가 정해집니다."}, "RSQ": {"a": "(known_y's; known_x's)", "d": "피어슨 곱 모멘트 상관 계수의 제곱을 구합니다.", "ad": "은(는) 데이터 배열 또는 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다.!은(는) 데이터 배열 또는 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다."}, "SKEW": {"a": "(number1; [number2]; ...)", "d": "분포의 왜곡도를 구합니다.", "ad": "은(는) 왜곡도를 구하려는 인수로서 255개까지 지정할 수 있습니다."}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "모집단을 기준으로 분포의 왜곡도를 구합니다.", "ad": "은(는) 모집단 왜곡도를 구하려는 인수로서 1개에서 254개까지 지정할 수 있으며 숫자나 이름, 배열 또는 참조가 될 수 있습니다."}, "SLOPE": {"a": "(known_y's; known_x's)", "d": "선형 회귀선의 기울기를 구합니다.", "ad": "은(는) 종속 데이터 배열 또는 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다.!은(는) 독립 데이터 배열 또는 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다."}, "SMALL": {"a": "(array; k)", "d": "데이터 집합에서 k번째로 작은 값을 구합니다(예: 데이터 집합에서 5번째로 작은 값).", "ad": "은(는) k번째로 작은 값을 결정하는 데 필요한 배열 또는 데이터 범위입니다.!은(는) 배열 또는 셀 범위에서 몇 번째로 작은 값을 구할 것인지 지정합니다."}, "STANDARDIZE": {"a": "(x; mean; standard_dev)", "d": "정규화된 값을 구합니다.", "ad": "은(는) 정규화하려는 값입니다.!은(는) 분포의 산술 평균입니다.!은(는) 분포의 표준 편차로 양수입니다."}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "표본 집단의 표준 편차를 구합니다. 표본 집단에서 텍스트나 논리값은 제외됩니다.", "ad": "은(는) 모집단 표본에 해당하는 인수로서 255개까지 지정할 수 있습니다."}, "STDEV.P": {"a": "(number1; [number2]; ...)", "d": "모집단의 표준 편차를 구합니다. 텍스트와 논리값은 제외됩니다.", "ad": "은(는) 모집단에 해당하는 인수로서 255개까지 지정할 수 있습니다."}, "STDEV.S": {"a": "(number1; [number2]; ...)", "d": "표본 집단의 표준 편차를 구합니다. 표본 집단에서 텍스트나 논리값은 제외됩니다.", "ad": "은(는) 모집단 표본에 해당하는 인수로서 255개까지 지정할 수 있습니다."}, "STDEVA": {"a": "(value1; [value2]; ...)", "d": "표본 집단의 표준 편차(논리값과 텍스트 포함)를 구합니다. 논리값 FALSE는 0 값을, TRUE는 1 값을 가집니다.", "ad": "은(는) 모집단 인구의 표준 편차를 구할 인수로서 255개까지 지정할 수 있습니다. 인수는 값, 이름, 참조가 될 수 있습니다."}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "모집단의 표준 편차를 구합니다. 텍스트와 논리값은 제외됩니다.", "ad": "은(는) 모집단에 해당하는 인수로서 255개까지 지정할 수 있습니다."}, "STDEVPA": {"a": "(value1; [value2]; ...)", "d": "전체 모집단의 표준 편차(논리값과 텍스트 포함)를 구합니다. FALSE는 0 값을, TRUE는 1 값을 가집니다.", "ad": "은(는) 표준 편차를 구할 인수로서 255개까지 지정할 수 있습니다. 인수는 값, 이름, 배열, 숫자가 들어 있는 참조가 될 수 있습니다."}, "STEYX": {"a": "(known_y's; known_x's)", "d": "회귀분석에 의해 예측한 y 값의 표준 오차를 각 x 값에 대하여 구합니다.", "ad": "은(는) 종속 데이터 배열 또는 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다.!은(는) 독립 데이터 배열 또는 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다."}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "스튜던트 t-분포값을 구합니다.", "ad": "은(는) 분포를 계산하려는 수입니다.!은(는) 분포의 특성을 정하는 자유도를 나타내는 정수입니다.!은(는) 분포가 양측인지 단측인지 나타내는 수입니다. 단측이면 1을, 양측이면 2를 지정합니다."}, "TINV": {"a": "(probability; deg_freedom)", "d": "스튜던트 t-분포의 양측 검증 역함수 값을 구합니다.", "ad": "은(는) 양측 스튜던트 t-분포를 따르는 0에서 1 사이의 확률입니다.!은(는) 분포를 결정짓는 자유도입니다."}, "T.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "좌측 스튜던트 t-분포값을 구합니다.", "ad": "은(는) 분포를 계산하는 숫자 값입니다.!은(는) 함수의 형태를 결정하는 논리값입니다. 누적 분포 함수에는 TRUE를, 확률 밀도 함수에는 FALSE!를 사용합니다."}, "T.DIST.2T": {"a": "(x; deg_freedom)", "d": "양측 스튜던트 t-분포값을 구합니다.", "ad": "은(는) 분포를 계산하려는 값입니다.!은(는) 분포의 특성을 정하는 자유도를 나타내는 정수입니다."}, "T.DIST.RT": {"a": "(x; deg_freedom)", "d": "우측 스튜던트 t-분포값을 구합니다.", "ad": "은(는) 분포를 계산하려는 값입니다.!은(는) 분포의 특성을 정하는 자유도를 나타내는 정수입니다."}, "T.INV": {"a": "(probability; deg_freedom)", "d": "좌측 스튜던트 t-분포의 역함수 값을 구합니다.", "ad": "은(는) 양측 스튜던트 t-분포를 따르는 0에서 1 사이의 확률입니다.!은(는) 분포의 특성을 정하는 자유도를 나타내는 양의 정수입니다."}, "T.INV.2T": {"a": "(probability; deg_freedom)", "d": "양측 스튜던트 t-분포의 역함수 값을 구합니다.", "ad": "은(는) 양측 스튜던트 t-분포를 따르는 0에서 1 사이의 확률입니다.!은(는) 분포의 특성을 정하는 자유도를 나타내는 양의 정수입니다."}, "T.TEST": {"a": "(array1; array2; tails; type)", "d": "스튜던트 t-검정에 근거한 확률을 구합니다.", "ad": "은(는) 첫 번째 데이터 집합입니다.!은(는) 두 번째 데이터 집합입니다.!은(는) 분포가 단측인지 양측인지를 지정하는 수입니다. 단측이면 1로, 양측이면 2로 지정합니다.!은(는) 수행할 t-검정의 종류입니다."}, "TREND": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "최소 자승법을 이용하여 지정한 값들의 선형 추세를 구합니다.", "ad": "은(는) y = mx + b 식에서 이미 알고 있는 y 값의 집합입니다.!은(는) 옵션이며 y = mx + b 식에서 Known_y와 크기가 일치하는 이미 알고 있는 x 값의 집합입니다.!은(는) y 값의 추세를 계산하고자 하는 새로운 x 값의 집합입니다.!은(는) TRUE로 설정하거나 생략하면 상수 b를 정상적으로 계산하며 FALSE로 설정하면 b를 0으로 지정하는 논리값입니다."}, "TRIMMEAN": {"a": "(array; percent)", "d": "데이터 집합의 양 끝값을 제외한 부분의 평균을 구합니다.", "ad": "은(는) 양 끝값을 없애고 평균을 구하려는 값의 배열 또는 범위입니다.!은(는) 데이터 집합의 양 끝에서 제외시킬 데이터의 비율입니다."}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "스튜던트 t-검정에 근거한 확률을 구합니다.", "ad": "은(는) 첫 번째 데이터 집합입니다.!은(는) 두 번째 데이터 집합입니다.!은(는) 분포가 단측인지 양측인지를 지정하는 수입니다. 단측이면 1로, 양측이면 2로 지정합니다.!은(는) 수행할 t-검정의 종류입니다."}, "VAR": {"a": "(number1; [number2]; ...)", "d": "표본 집단의 분산을 구합니다. 표본 집단에서 논리값과 텍스트는 제외됩니다.", "ad": "은(는) 모집단 표본에 해당하는 인수로서 255개까지 지정할 수 있습니다."}, "VAR.P": {"a": "(number1; [number2]; ...)", "d": "전체 모집단의 분산을 구합니다. 모집단에서 논리값과 텍스트는 제외됩니다.", "ad": "은(는) 모집단에 해당하는 인수로서 255개까지 지정할 수 있습니다."}, "VAR.S": {"a": "(number1; [number2]; ...)", "d": "표본 집단의 분산을 구합니다. 표본 집단에서 논리값과 텍스트는 제외됩니다.", "ad": "은(는) 모집단 표본에 해당하는 인수로서 255개까지 지정할 수 있습니다."}, "VARA": {"a": "(value1; [value2]; ...)", "d": "표본 집단의 평방 편차(논리값과 텍스트 포함)를 구합니다. 논리값 FALSE는 0 값을, TRUE는 1 값을 가집니다.", "ad": "은(는) 표본 집단 인구의 평방 편차를 구할 인수로서 255개까지 지정할 수 있습니다."}, "VARP": {"a": "(number1; [number2]; ...)", "d": "전체 모집단의 분산을 구합니다. 모집단에서 논리값과 텍스트는 제외됩니다.", "ad": "은(는) 모집단에 해당하는 인수로서 255개까지 지정할 수 있습니다."}, "VARPA": {"a": "(value1; [value2]; ...)", "d": "전체 모집단의 평방 편차(논리값과 텍스트 포함)를 구합니다. FALSE는 0 값을, TRUE는 1 값을 가집니다.", "ad": "은(는) 모집단의 평방 편차를 구할 인수로서 255개까지 지정할 수 있습니다."}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "와이블 분포값을 구합니다.", "ad": "은(는) 분포를 계산하려는 값으로 0 또는 양수이어야 합니다.!은(는) 분포의 매개 변수로 양수여야 합니다.!은(는) 분포의 매개 변수로 양수이어야 합니다.!은(는) 함수의 형태를 결정하는 논리값입니다. TRUE이면 누적 분포 함수, FALSE이거나 생략하면 확률 밀도 함수를 구합니다."}, "WEIBULL.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "와이블 분포값을 구합니다.", "ad": "은(는) 분포를 계산하려는 값으로 0 또는 양수이어야 합니다.!은(는) 분포의 매개 변수로 양수여야 합니다.!은(는) 분포의 매개 변수로 양수이어야 합니다.!은(는) 함수의 형태를 결정하는 논리값입니다. TRUE이면 누적 분포 함수, FALSE이거나 생략하면 확률 질량 함수를 구합니다."}, "Z.TEST": {"a": "(array; x; [sigma])", "d": "z-검정의 단측 P 값을 구합니다.", "ad": "은(는) x를 검정하기 위한 대상 데이터 배열 또는 범위입니다.!은(는) 검정할 값입니다.!은(는) 이미 알고 있는 모집단의 표준 편차입니다. 생략하면 표본 집단의 표준 편차가 사용됩니다."}, "ZTEST": {"a": "(array; x; [sigma])", "d": "z-검정의 단측 P 값을 구합니다.", "ad": "은(는) x를 검정하기 위한 대상 데이터 배열 또는 범위입니다.!은(는) 검정할 값입니다.!은(는) 이미 알고 있는 모집단의 표준 편차입니다. 생략하면 표본 집단의 표준 편차가 사용됩니다."}, "ACCRINT": {"a": "(issue; first_interest; settlement; rate; par; frequency; [basis]; [calc_method])", "d": "정기적으로 이자를 지급하는 유가 증권의 경과 이자를 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 발행일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 최초 이자 지급일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 유가 증권의 연간 확정 금리입니다.!은(는) 유가 증권의 액면가입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다.!은(는) 논리값으로, 발행일부터 계산하면 TRUE이거나 생략되고 마지막 확정 금리 지급일부터 계산하면 FALSE입니다."}, "ACCRINTM": {"a": "(issue; settlement; rate; par; [basis])", "d": "만기에 이자를 지급하는 유가 증권의 경과 이자를 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 발행일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 유가 증권의 연간 확정 금리입니다.!은(는) 유가 증권의 액면가입니다.!은(는) 날짜 계산 기준입니다."}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "매 회계 기간에 대한 감가 상각액을 반환합니다.", "ad": "은(는) 자산 취득가액입니다.!은(는) 자산 취득일입니다.!은(는) 첫째 회계 기간의 마지막 날짜입니다.!은(는) 자산의 수명이 다 되었을 때의 잔존 가치입니다.!은(는) 기간입니다.!은(는) 감가 상각률입니다.!은(는) 사용할 날짜 체계입니다. 1년 360일의 경우 0, 실제의 경우 1, 1년 365일의 경우 3입니다."}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "매 회계 기간에 대한 감가 상각액을 반환합니다.", "ad": "은(는) 자산 취득가액입니다.!은(는) 자산 취득일입니다.!은(는) 첫째 회계 기간의 마지막 날짜입니다.!은(는) 자산의 수명이 다 되었을 때의 잔존 가치입니다.!은(는) 기간입니다.!은(는) 감가 상각률입니다.!은(는) 사용할 날짜 체계입니다. 1년 360일의 경우 0, 실제의 경우 1, 1년 365일의 경우 3입니다."}, "COUPDAYBS": {"a": "(settlement; maturity; frequency; [basis])", "d": "이자 지급 기간의 시작일부터 결산일까지의 날짜 수를 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "COUPDAYS": {"a": "(settlement; maturity; frequency; [basis])", "d": "결산일이 들어 있는 이자 지급 기간의 날짜 수를 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "COUPDAYSNC": {"a": "(settlement; maturity; frequency; [basis])", "d": "결산일부터 다음 이자 지급일까지의 날짜 수를 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "COUPNCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "결산일 다음 첫 번째 이자 지급일을 나타내는 숫자를 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "COUPNUM": {"a": "(settlement; maturity; frequency; [basis])", "d": "결산일과 만기일 사이의 이자 지급 횟수를 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "COUPPCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "결산일 바로 전 이자 지급일을 나타내는 숫자를 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "주어진 기간 중에 납입하는 대출금 이자의 누계액을 반환합니다.", "ad": "은(는) 이자율입니다.!은(는) 총 불입 횟수입니다.!은(는) 현재 가치입니다.!은(는) 계산 기간의 최초 불입 회차입니다.!은(는) 계산 기간의 최종 불입 회차입니다.!은(는) 불입 시점입니다."}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "주어진 기간 중에 납입하는 대출금 원금의 누계액을 반환합니다.", "ad": "은(는) 이자율입니다.!은(는) 총 불입 횟수입니다.!은(는) 현재 가치입니다.!은(는) 계산 기간의 최초 불입 회차입니다.!은(는) 계산 기간의 최종 불입 회차입니다.!은(는) 불입 시점입니다."}, "DB": {"a": "(cost; salvage; life; period; [month])", "d": "정율법을 사용하여 지정한 기간 동안 자산의 감가 상각을 구합니다.", "ad": "은(는) 자산의 초기 취득가액입니다.!은(는) 감가 상각 종료 시 회수되는 값입니다.!은(는) 자산이 감가 상각되는 기간 수(자산의 수명 년수)입니다.!은(는) 감가 상각을 계산하려는 기간으로 수명 년수와 같은 단위여야 합니다.!은(는) 첫 해의 개월 수입니다. 생략된 경우는 12로 가정합니다."}, "DDB": {"a": "(cost; salvage; life; period; [factor])", "d": "지정한 기간 동안 이중 체감법이나 사용자가 정하는 방법으로 자산의 감가 상각액을 구합니다.", "ad": "은(는) 자산의 초기 취득가액입니다.!은(는) 감가 상각 종료 시 회수되는 값입니다.!은(는) 자산이 감가 상각되는 기간 수(자산의 수명 년수)입니다.!은(는) 감가 상각을 계산하려는 기간으로 life와 같은 단위여야 합니다.!은(는) 잔액이 감소되는 비율로, 생략하면 이중 체감법에 따라 2로 간주됩니다."}, "DISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "유가 증권의 할인율을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 액면가 $100당 유가 증권 가격입니다.!은(는) 액면가 $100당 유가 증권의 상환액입니다.!은(는) 날짜 계산 기준입니다."}, "DOLLARDE": {"a": "(fractional_dollar; fraction)", "d": "분수로 표시된 금액을 소수로 표시된 금액으로 변환합니다.", "ad": "은(는) 분수로 표시되는 수입니다.!은(는) 분수의 분모로 사용되는 정수입니다."}, "DOLLARFR": {"a": "(decimal_dollar; fraction)", "d": "소수로 표시된 금액을 분수로 표시된 금액으로 변환합니다.", "ad": "은(는) 소수입니다.!은(는) 분수의 분모로 사용되는 정수입니다."}, "DURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "정기적으로 이자를 지급하는 유가 증권의 연간 듀레이션을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 유가 증권의 연간 확정 금리입니다.!은(는) 유가 증권의 연간 수익률입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "EFFECT": {"a": "(nominal_rate; npery)", "d": "연간 실질 이자율을 반환합니다.", "ad": "은(는) 연간 명목 이자율입니다.!은(는) 연간 복리 계산 횟수입니다."}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "주기적이고 고정적인 지급액과 고정적인 이율에 의거한 투자의 미래 가치를 산출합니다.", "ad": "은(는) 기간별 이자율입니다. 예를 들어, 연 이율 6%에 분기 지급 시에는 6%/4를 사용합니다.!은(는) 투자의 총 지급 기간 수입니다.!은(는) 각 기간마다의 지급액입니다. 이것은 투자 기간 동안 변할 수 없습니다.!은(는) 일련의 미래 지급액에 상응하는 현재 가치 또는 총합계입니다. 생략하면 Pv는 0입니다.!은(는) 지급 시기를 나타내며 1은 투자 주기 초를, 0 또는 생략 시에는 투자 주기 말을 의미합니다."}, "FVSCHEDULE": {"a": "(principal; schedule)", "d": "초기 원금에 일련의 복리 이율을 적용했을 때의 예상 금액을 반환합니다.", "ad": "은(는) 현재의 가치입니다.!은(는) 적용할 이율로 구성된 배열입니다."}, "INTRATE": {"a": "(settlement; maturity; investment; redemption; [basis])", "d": "완전 투자한 유가 증권의 이자율을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 유가 증권의 투자액입니다.!은(는) 만기 시 상환액입니다.!은(는) 날짜 계산 기준입니다."}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "주기적이고 고정적인 지급액과 이자율에 기반한 일정 기간 동안의 투자 금액에 대한 이자 지급액을 구합니다.", "ad": "은(는) 기간별 이자율입니다. 예를 들어, 연 이율 6%로 분기 지급 시에는 6%/4를 사용합니다.!은(는) 이자를 계산할 기간으로 1에서 nper 사이여야 합니다.!은(는) 투자의 총 지급 기간 수입니다.!은(는) 일련의 미래 지급액에 상응하는 현재 가치의 개략적인 합계입니다.!은(는) 지급이 완료된 후 얻고자 하는 미래 가치 또는 현금 잔액입니다. 생략하면 Fv는 0이 됩니다.!은(는) 지급 기일로서, 0 또는 생략 시에는 기간 말이며 1이면 기간 초입니다."}, "IRR": {"a": "(values; [guess])", "d": "일련의 현금 흐름에 대한 내부 수익률을 구합니다.", "ad": "은(는) 내부 수익률을 계산할 수를 포함하는 셀의 참조 영역 또는 배열입니다.!은(는) IRR 결과의 근사값이라고 추정하는 수입니다. 생략하면 0.1(10%)이 됩니다."}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "일정 기간 동안의 투자에 대한 이자 지급액을 구합니다.", "ad": "은(는) 기간당 이자율입니다. 예를 들어, 연 이율 6%로 분기 지급 시에는 6%/4를 사용합니다.!은(는) 이자 지급액을 구하고자 하는 기간입니다.!은(는) 투자 기간입니다.!은(는) 일련의 미래 투자액이 상응하는 현재 가치의 개략적인 합계입니다."}, "MDURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "가정된 액면가 $100에 대한 유가 증권의 수정된 Macauley 듀레이션을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 유가 증권의 연간 확정 금리입니다.!은(는) 유가 증권의 연간 수익률입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "일련의 주기적 현금 흐름에 대해 투자 비용과 현금 재투자 수익을 고려한 내부 수익률을 구합니다.", "ad": "은(는) 정기적인 지출(음)과 수입(양)을 나타내는 수가 있는 셀의 참조 영역 또는 배열입니다.!은(는) 현금 흐름에 사용한 금액에 대해 지불할 이자율입니다.!은(는) 재투자함에 따라 현금 흐름에서 받을 이자율입니다."}, "NOMINAL": {"a": "(effect_rate; npery)", "d": "명목상의 연이율을 반환합니다.", "ad": "은(는) 실질적인 이율입니다.!은(는) 연간 복리 계산 횟수입니다."}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "주기적이고 고정적인 지급액과 고정적인 이율에 의거한 투자의 기간을 구합니다.", "ad": "은(는) 기간별 이자율입니다.예를 들어, 연 이율 6%에 분기 지급 시에는 6%/4를 사용합니다.!은(는) 각 기간마다의 지급액입니다. 이것은 투자 기간 동안 변할 수 없습니다.!은(는) 일련의 미래 지급액에 상응하는 현재 가치 또는 총합계입니다.!은(는) 지급이 완료된 후 얻고자 하는 미래 가치 또는 현금 잔액입니다. 생략하면 0으로 지정됩니다.!은(는) 지급 시기를 나타내며 1은 투자 주기 초를, 0 또는 생략 시에는 투자 주기 말을 의미합니다."}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "주기적인 현금 흐름과 할인율을 기준으로 투자의 순 현재 가치를 산출합니다.", "ad": "은(는) 일정 기간의 할인율입니다.!은(는) 지급액과 수입을 나타내는 1개에서 254개까지의 인수입니다."}, "ODDFPRICE": {"a": "(settlement; maturity; issue; first_coupon; rate; yld; redemption; frequency; [basis])", "d": "첫 이수 기간이 경상 이수 기간과 다른(짧거나 긴) 유가 증권의 액면가 $100당 가격을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 발행일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 최초 이자 지급일입니다.!은(는) 유가 증권의 이율입니다.!은(는) 유가 증권의 연간 수익률입니다.!은(는) 액면가 $100당 유가 증권의 상환액입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "ODDFYIELD": {"a": "(settlement; maturity; issue; first_coupon; rate; pr; redemption; frequency; [basis])", "d": "첫 이수 기간이 경상 이수 기간과 다른(짧거나 긴) 유가 증권의 연 수익률을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 발행일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 최초 이자 지급일입니다.!은(는) 유가 증권의 이율입니다.!은(는) 유가 증권의 가격입니다.!은(는) 액면가 $100당 유가 증권의 상환액입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "ODDLPRICE": {"a": "(settlement; maturity; last_interest; rate; yld; redemption; frequency; [basis])", "d": "마지막 이수 기간이 경상 이수 기간과 다른(짧거나 긴) 유가 증권의 액면가 $100당 가격을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 마지막 이자 지급일입니다.!은(는) 유가 증권의 이율입니다.!은(는) 유가 증권의 연간 수익률입니다.!은(는) 액면가 $100당 유가 증권의 상환액입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "ODDLYIELD": {"a": "(settlement; maturity; last_interest; rate; pr; redemption; frequency; [basis])", "d": "마지막 이수 기간이 경상 이수 기간과 다른(짧거나 긴) 유가 증권의 수익률을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 마지막 이자 지급일입니다.!은(는) 유가 증권의 이율입니다.!은(는) 유가 증권의 가격입니다.!은(는) 액면가 $100당 유가 증권의 상환액입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "PDURATION": {"a": "(rate; pv; fv)", "d": "투자를 통해 지정한 가치에 도달하는 데 필요한 기간을 반환합니다.", "ad": "은(는) 기간별 이자율입니다.!은(는) 투자의 현재 가치입니다.!은(는) 얻고자 하는 투자의 미래 가치입니다."}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "주기적이고 고정적인 지급액과 고정적인 이율에 의거한 대출 상환금을 계산합니다.", "ad": "은(는) 대출에 대한 기간별 이자율입니다. 예를 들어, 연 이율 6%에 분기 상환 시에는 6%/4를 사용합니다.!은(는) 대출 상환금의 총 지급 기간 수입니다.!은(는) 일련의 미래 지급액에 상응하는 현재 가치입니다.!은(는) 상환이 완료된 후 얻고자 하는 미래 가치 또는 현금 잔액입니다. 생략하면 0(영)으로 지정됩니다.!은(는) 상환 시기를 나타내며 1은 상환 주기 초를, 0 또는 생략 시에는 상환 주기 말을 의미합니다."}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "주기적이고 고정적인 지급액과 이자율에 기반한 일정 기간 동안의 투자에 대한 원금의 지급액을 구합니다.", "ad": "은(는) 기간별 이자율입니다. 예를 들어, 연 이율 6%로 분기 지급 시에는 6%/4를 사용합니다.!은(는) 기간을 지정하며, 1에서 nper까지의 범위입니다.!은(는) 투자의 총 지급 기간 수입니다.!은(는) 일련의 미래 지급액에 상응하는 현재 가치의 개략적인 합계입니다.!은(는) 지급이 완료된 후 얻고자 하는 미래 가치 또는 현금 잔액입니다.!은(는) 지급 기일로서, 0 또는 생략 시에는 기간 말이며 1이면 기간 초입니다."}, "PRICE": {"a": "(settlement; maturity; rate; yld; redemption; frequency; [basis])", "d": "정기적으로 이자를 지급하는 유가 증권의 액면가 $100당 가격을 구합니다.", "ad": "은(는) 날짜 일려 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일려 번호로 표시된 유가 증권의 만기일입니다.!은(는) 유가 증권의 연간 확정 금리입니다.!은(는) 유가 증권의 연간 수익률입니다.!은(는) 액면가 $100당 유가 증권의 상환액입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "PRICEDISC": {"a": "(settlement; maturity; discount; redemption; [basis])", "d": "할인된 유가 증권의 액면가 $100당 가격을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 유가 증권의 할인율입니다.!은(는) 액면가 $100당 유가 증권의 상환액입니다.!은(는) 날짜 계산 기준입니다."}, "PRICEMAT": {"a": "(settlement; maturity; issue; rate; yld; [basis])", "d": "만기 시 이자를 지급하는 유가 증권의 액면가 $100당 가격을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 발행일입니다.!은(는) 유가 증권 발행일의 이율입니다.!은(는) 유가 증권의 연간 수익률입니다.!은(는) 날짜 계산 기준입니다."}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "투자의 현재 가치를 구합니다. 일련의 미래 투자가 상응하는 현재 가치의 총합계입니다.", "ad": "은(는) 기간별 이자율입니다. 예를 들어, 연 이율 6%에 분기 지급 시에는 6%/4를 사용합니다.!은(는) 투자의 총 지급 기간입니다.!은(는) 각 기간에 대한 지급액으로서 투자 기간 중에 변경될 수 없습니다.!은(는) 투자가 완료된 후 얻고자 하는 미래 가치 또는 현금 잔액입니다.!은(는) 투자 주기 초에 지급 시에는 1로 설정하고 투자 주기 말에 지급 시에는 0으로 설정하거나 생략하는 논리값입니다."}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "대출 또는 투자의 기간별 이자율을 구합니다. 예를 들어, 연 이율 6%에 분기 지급 시에는 6%/4를 사용합니다.", "ad": "은(는) 대출 또는 투자의 총 지급 기간 수입니다.!은(는) 각 기간의 지급액으로서, 대출 또는 투자 기간 중에 변경될 수 없습니다.!은(는) 일련의 미래 지급액에 상응하는 현재 가치입니다.!은(는) 지급이 완료된 후 얻고자 하는 미래 가치 또는 현금 잔액입니다. 생략하면 Fv는 0이 됩니다.!은(는) 지급 시기를 나타내며 1은 지급 주기 초를, 0 또는 생략 시에는 지급 주기 말을 의미합니다.!은(는) 이율이 얼마나 될 것인가에 대한 추정입니다. 생략하면 Guess는 0.1(10퍼센트)이 됩니다."}, "RECEIVED": {"a": "(settlement; maturity; investment; discount; [basis])", "d": "완전 투자 유가 증권에 대해 만기 시 수령하는 금액을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 유가 증권의 투자액입니다.!은(는) 유가 증권의 할인율입니다.!은(는) 날짜 계산 기준입니다."}, "RRI": {"a": "(nper; pv; fv)", "d": "투자 증가액의 평균 이자율을 반환합니다.", "ad": "은(는) 투자 기간입니다.!은(는) 투자의 현재 가치입니다.!은(는) 투자의 미래 가치입니다."}, "SLN": {"a": "(cost; salvage; life)", "d": "한 기간 동안 정액법에 의한 자산의 감가 상각액을 구합니다.", "ad": "은(는) 자산의 초기 취득가액입니다.!은(는) 감가 상각 종료 시 회수되는 값입니다.!은(는) 자산이 감가 상각되는 기간 수(자산의 수명 년수)입니다."}, "SYD": {"a": "(cost; salvage; life; per)", "d": "지정한 기간 동안 연수 합계법에 의한 자산의 감가 상각액을 구합니다.", "ad": "은(는) 자산의 초기 취득가액입니다.!은(는) 감가 상각 종료 시 회수되는 값입니다.!은(는) 자산이 감가 상각되는 기간 수(자산의 수명 년수)입니다.!은(는) 지정한 기간이며 life와 같은 단위여야 합니다."}, "TBILLEQ": {"a": "(settlement; maturity; discount)", "d": "국채에 대해 채권에 해당하는 수익률을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 국채의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 국채의 만기일입니다.!은(는) 국채의 할인율입니다."}, "TBILLPRICE": {"a": "(settlement; maturity; discount)", "d": "국채에 대해 액면가 $100당 가격을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 국채의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 국채의 만기일입니다.!은(는) 국채의 할인율입니다."}, "TBILLYIELD": {"a": "(settlement; maturity; pr)", "d": "국채의 수익률을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 국채의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 국채의 만기일입니다.!은(는) 액면가 $100당 국채의 가격입니다."}, "VDB": {"a": "(cost; salvage; life; start_period; end_period; [factor]; [no_switch])", "d": "이중 체감법 또는 지정한 방법을 사용하여 일정 기간 동안 일부 기간을 포함한 자산의 감가 상각을 구합니다.", "ad": "은(는) 자산의 초기 취득가액입니다.!은(는) 감가 상각 종료 시 회수되는 값(자산의 잔존가액)입니다.!은(는) 자산이 감가 상각되는 기간 수(자산의 수명 년수)입니다.!은(는) 감가 상각 계산이 시작되는 기간입니다. 수명 년수와 동일한 단위를 사용합니다.!은(는) 감가 상각 계산이 끝나는 기간입니다. 수명 년수와 동일한 단위를 사용합니다.!은(는) 잔액이 감소되는 비율입니다. 생략하면 2(이중 체감 잔액율)를 적용합니다.!은(는) 감가 상각 금액이 이중 체감법 상각보다 클 때 정액법 감가 상각으로 전환할 것인지 지정하는 논리값입니다. FALSE로 정하거나 생략하면 전환하고, TRUE로 정하면 전환하지 않습니다."}, "XIRR": {"a": "(values; dates; [guess])", "d": "비정기적일 수도 있는 현금 흐름의 내부 회수율을 반환합니다.", "ad": "은(는) 지불 일정에 대응하는 일련의 현금 흐름입니다.!은(는) 현금 흐름상의 불입금에 해당하는 지불 일정입니다.!은(는) XIRR의 결과값에 가깝다고 추측되는 수입니다."}, "XNPV": {"a": "(rate; values; dates)", "d": "비정기적일 수도 있는 현금 흐름의 순 현재 가치를 반환합니다.", "ad": "은(는) 현금 흐름에 적용할 할인율입니다.!은(는) 지불 일정에 대응하는 일련의 현금 흐름입니다.!은(는) 현금 흐름상의 불입금에 대응하는 지불 일정입니다."}, "YIELD": {"a": "(settlement; maturity; rate; pr; redemption; frequency; [basis])", "d": "정기적으로 이자를 지급하는 유가 증권의 수익률을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 유가 증권의 연간 확정 금리입니다.!은(는) 액면가 $100당 유가 증권 가격입니다.!은(는) 액면가 $100당 유가 증권의 상환액입니다.!은(는) 연간 이자 지급 횟수입니다.!은(는) 날짜 계산 기준입니다."}, "YIELDDISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "할인된 유가 증권의 연 수익률을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 액면가 $100당 유가 증권 가격입니다.!은(는) 액면가 $100당 유가 증권의 상환액입니다.!은(는) 날짜 계산 기준입니다."}, "YIELDMAT": {"a": "(settlement; maturity; issue; rate; pr; [basis])", "d": "만기 시 이자를 지급하는 유가 증권의 연 수익률을 반환합니다.", "ad": "은(는) 날짜 일련 번호로 표시된 유가 증권의 결산일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 만기일입니다.!은(는) 날짜 일련 번호로 표시된 유가 증권의 발행일입니다.!은(는) 유가 증권 발행일의 이율입니다.!은(는) 유가 증권의 액면가 $100당 가격입니다.!은(는) 날짜 계산 기준입니다."}, "ABS": {"a": "(number)", "d": "절대 값을 구합니다.", "ad": "은(는) 절대 값을 구하려는 실수입니다."}, "ACOS": {"a": "(number)", "d": "0과 Pi 범위에서 라디안의 아크코사인 값을 구합니다.", "ad": "은(는) 구하려는 각도의 코사인 값으로 -1에서 1 사이 값이어야 합니다."}, "ACOSH": {"a": "(number)", "d": "역 하이퍼볼릭 코사인 값을 구합니다.", "ad": "은(는) 1보다 크거나 같은 실수입니다."}, "ACOT": {"a": "(number)", "d": "0부터 Pi까지의 아크탄젠트 값을 구합니다.", "ad": "은(는) 구하려는 각도의 코탄젠트 값입니다."}, "ACOTH": {"a": "(number)", "d": "역 하이퍼볼릭 코탄젠트 값을 구합니다.", "ad": "은(는) 구하려는 각도의 하이퍼볼릭 코탄젠트 값입니다."}, "AGGREGATE": {"a": "(function_num; options; ref1; ...)", "d": "목록 또는 데이터베이스에서 집계 값을 구합니다.", "ad": "은(는) 집계에 대한 요약 함수를 지정하는 숫자(1 - 19)입니다.!은(는) 집계에서 무시할 값을 지정하는 숫자(0 - 7)입니다.!은(는) 집계 값을 계산할 숫자 데이터의 범위 또는 배열입니다.!은(는) 배열에서의 위치입니다. k번째 가장 큰 값, k번째 가장 작은 값, k번째 백분위수 또는 k번째 사분위수를 나타냅니다.!은(는) 집계에 대한 요약 함수를 지정하는 숫자(1 - 19)입니다.!은(는) 집계에서 무시할 값을 지정하는 숫자(0 - 7)입니다.!은(는) 집계할 1 - 253 사이의 범위 또는 참조입니다."}, "ARABIC": {"a": "(text)", "d": "로마 숫자를 아라비아 숫자로 변환합니다.", "ad": "은(는) 변환할 로마 숫자입니다."}, "ASC": {"a": "(text)", "d": "DBCS(더블바이트 문자 집합) 언어의 경우 함수가 전자 문자(더블바이트)를 반자 문자(싱글바이트)로 변경합니다.", "ad": "텍스트 또는 변경할 텍스트가 들어 있는 셀에 대한 참조입니다."}, "ASIN": {"a": "(number)", "d": "-Pi/2부터 Pi/2까지의 아크사인 값을 구합니다.", "ad": "은(는) 구하려는 각도의 사인 값으로 -1에서 1 사이 값이어야 합니다."}, "ASINH": {"a": "(number)", "d": "역 하이퍼볼릭 사인 값을 구합니다.", "ad": "은(는) 1보다 크거나 같은 실수입니다."}, "ATAN": {"a": "(number)", "d": "-Pi/2부터 Pi/2까지의 아크탄젠트 값을 구합니다.", "ad": "은(는) 구하려는 각도의 탄젠트 값입니다."}, "ATAN2": {"a": "(x_num; y_num)", "d": "-Pi에서 Pi까지의 라디안에서 지정된 x, y 좌표의 아크탄젠트 값을 구합니다. 단 -Pi 값은 제외됩니다.", "ad": "은(는) 각을 구하려는 지점의 x 좌표입니다.!은(는) 각을 구하려는 지점의 y 좌표입니다."}, "ATANH": {"a": "(number)", "d": "역 하이퍼볼릭 탄젠트 값을 구합니다.", "ad": "은(는) -1과 1 사이의 실수입니다."}, "BASE": {"a": "(number; radix; [min_length])", "d": "숫자를 지정된 기수의 텍스트 표현으로 변환합니다.", "ad": "은(는) 변환할 숫자입니다.!은(는) 숫자를 변환할 기수입니다.!은(는) 반환된 문자열의 최소 길이입니다. 생략하면 앞에 0이 추가되지 않습니다."}, "CEILING": {"a": "(number; significance)", "d": "수를 significance의 배수가 되도록 절대 값을 올림합니다.", "ad": "은(는) 올림하려는 수입니다.!은(는) 배수의 기준이 되는 수입니다."}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "수를 가장 가까운 정수 또는 가장 가까운 significance의 배수가 되도록 올림합니다.", "ad": "은(는) 올림하려는 수입니다.!은(는) 올림하려는 수의 배수입니다.!이 함수에 0이 아닌 값을 지정하면 소수점 자리가 올림됩니다."}, "CEILING.PRECISE": {"a": "(number; [significance])", "d": "가장 가까운 정수 또는 significance의 배수로 올림된 수를 반환합니다. ", "ad": "은(는) 올림하려는 수입니다.!은(는) 배수의 기준이 되는 수입니다."}, "COMBIN": {"a": "(number; number_chosen)", "d": "주어진 개체 수로 만들 수 있는 조합의 수를 구합니다.", "ad": "은(는) 개체 수입니다.!은(는) 각 조합의 개체 수입니다."}, "COMBINA": {"a": "(number; number_chosen)", "d": "주어진 개체 수로 만들 수 있는 조합(반복 포함)의 수를 구합니다.", "ad": "은(는) 개체 수입니다.!은(는) 각 조합의 개체 수입니다."}, "COS": {"a": "(number)", "d": "각도의 코사인 값을 구합니다.", "ad": "은(는) 라디안으로 표시된 각도입니다."}, "COSH": {"a": "(number)", "d": "하이퍼볼릭 코사인 값을 구합니다.", "ad": "은(는) 실수입니다."}, "COT": {"a": "(number)", "d": "각도의 코탄젠트 값을 구합니다.", "ad": "은(는) 라디안으로 표시된 각도입니다."}, "COTH": {"a": "(number)", "d": "하이퍼볼릭 코탄젠트 값을 구합니다.", "ad": "은(는) 라디안으로 표시된 각도입니다."}, "CSC": {"a": "(number)", "d": "각도의 코시컨트 값을 구합니다.", "ad": "은(는) 라디안으로 표시된 각도입니다."}, "CSCH": {"a": "(number)", "d": "각도의 하이퍼볼릭 코시컨트 값을 구합니다.", "ad": "은(는) 라디안으로 표시된 각도입니다."}, "DECIMAL": {"a": "(number; radix)", "d": "지정된 기수의 텍스트 표현을 십진수로 변환합니다.", "ad": "은(는) 변환할 숫자입니다.!은(는) 변환하는 숫자의 기수입니다."}, "DEGREES": {"a": "(angle)", "d": "라디안 형태의 각도를 도 단위로 바꿉니다.", "ad": "은(는) 변환하려는 라디안 형태의 각도입니다."}, "ECMA.CEILING": {"a": "(number; significance)", "d": "수를 significance의 배수가 되도록 절대 값을 올림합니다.", "ad": "은(는) 올림하려는 수입니다.!은(는) 배수의 기준이 되는 수입니다."}, "EVEN": {"a": "(number)", "d": "가장 가까운 짝수인 정수로 양수는 올림하고 음수는 내림합니다.", "ad": "은(는) 올림 또는 내림할 값입니다."}, "EXP": {"a": "(number)", "d": "number를 지수로 한 e의 누승을 계산합니다.", "ad": "은(는) 밑 e에 적용할 지수입니다. 상수 e는 자연 로그값의 밑인 2.71828182845904와 같습니다."}, "FACT": {"a": "(number)", "d": "number의 계승값을 구합니다.", "ad": "은(는) 계승값을 구하려는 수로서 0 또는 양수여야 합니다."}, "FACTDOUBLE": {"a": "(number)", "d": "숫자의 이중 계승값을 반환합니다.", "ad": "은(는) 반환할 이중 계승값에 대한 값입니다."}, "FLOOR": {"a": "(number; significance)", "d": "수를 significance의 배수가 되도록 절대 값을 내림합니다.", "ad": "은(는) 내림하려는 수입니다.!은(는) 올림하려는 수의 배수입니다."}, "FLOOR.PRECISE": {"a": "(number; [significance])", "d": "가장 가까운 정수 또는 significance의 가장 가까운 배수로 내림된 숫자를 반환합니다.", "ad": "은(는) 올림하려는 수입니다.!은(는) 배수의 기준이 되는 수입니다."}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "수를 가장 가까운 정수 또는 significance의 가장 가까운 배수가 되도록 내림합니다.", "ad": "은(는) 내림하려는 수입니다.!은(는) 내림하려는 수의 배수입니다.!이 함수에 0이 아닌 값을 지정하면 소수점 자리가 내림됩니다."}, "GCD": {"a": "(number1; [number2]; ...)", "d": "두 개 이상의 정수의 최대 공약수를 구합니다.", "ad": "은(는) 계산할 값이며, 1개부터 255개까지 사용할 수 있습니다."}, "INT": {"a": "(number)", "d": "소수점 아래를 버리고 가장 가까운 정수로 내림합니다.", "ad": "은(는) 정수로 내림하려는 실수입니다."}, "ISO.CEILING": {"a": "(number; [significance])", "d": "가장 가까운 정수 또는 significance의 배수로 올림된 수를 반환합니다. number의 부호와 상관없이 number를 올림합니다. number 또는 significance가 0이면 0이 반환됩니다.", "ad": "은(는) 올림하려는 수입니다.!은(는) 배수의 기준이 되는 수입니다."}, "LCM": {"a": "(number1; [number2]; ...)", "d": "정수의 최소 공배수를 반환합니다.", "ad": "은(는) 최소 공배수를 구할 값이며, 1개부터 255개까지 사용할 수 있습니다."}, "LN": {"a": "(number)", "d": "자연 로그값을 구합니다.", "ad": "은(는) 자연 로그값을 구하려는 양의 실수입니다."}, "LOG": {"a": "(number; [base])", "d": "지정한 밑에 대한 로그를 구합니다.", "ad": "은(는) 로그값을 구하려는 양의 실수입니다.!은(는) 로그의 밑입니다. 생략하면 10이 됩니다."}, "LOG10": {"a": "(number)", "d": "밑이 10인 로그값을 구합니다.", "ad": "은(는) 밑이 10인 로그값을 구하려는 양의 실수입니다."}, "MDETERM": {"a": "(array)", "d": "배열의 행렬 식을 구합니다.", "ad": "은(는) 행과 열의 수가 같은 정방형 배열이며 셀 영역이거나 상수 배열입니다."}, "MINVERSE": {"a": "(array)", "d": "배열의 역행렬을 구합니다.", "ad": "은(는) 행과 열의 수가 같은 정방형 배열입니다."}, "MMULT": {"a": "(array1; array2)", "d": "두 배열의 행렬 곱을 구합니다. 행 수는 array1과 같고 열 수는 array2와 같아야 합니다.", "ad": "은(는) 곱하려는 숫자의 첫 번째 배열이며 열 수가 array2의 행 수와 같아야 합니다."}, "MOD": {"a": "(number; divisor)", "d": "나눗셈의 나머지를 구합니다.", "ad": "은(는) 나머지를 구하려는 수입니다.!은(는) 나누는 수입니다."}, "MROUND": {"a": "(number; multiple)", "d": "원하는 배수로 반올림된 수를 반환합니다.", "ad": "은(는) 반올림할 값입니다.!은(는) 숫자를 반올림할 배수의 기준이 되는 값입니다."}, "MULTINOMIAL": {"a": "(number1; [number2]; ...)", "d": "각 계승값의 곱에 대한 합계의 계승값 비율을 반환합니다.", "ad": "은(는) 계산할 값이며, 1개부터 255개까지 사용할 수 있습니다."}, "MUNIT": {"a": "(dimension)", "d": "지정된 차원의 단위 행렬을 반환합니다.", "ad": "은(는) 반환할 단위 행렬의 차원을 지정하는 정수입니다."}, "ODD": {"a": "(number)", "d": "주어진 수에 가장 가까운 홀수로, 양수인 경우 올림하고 음수인 경우 내림합니다.", "ad": "은(는) 올림 또는 내림하려는 수입니다."}, "PI": {"a": "()", "d": "원주율(파이:3.14159265358979) 값을 구합니다.", "ad": ""}, "POWER": {"a": "(number; power)", "d": "밑수를 지정한 만큼 거듭제곱한 결과를 구합니다.", "ad": "은(는) 밑수입니다.!은(는) 지수입니다."}, "PRODUCT": {"a": "(number1; [number2]; ...)", "d": "인수들의 곱을 구합니다.", "ad": "은(는) 곱하려는 수로서 255개까지 지정할 수 있습니다."}, "QUOTIENT": {"a": "(numerator; denominator)", "d": "나눗셈 몫의 정수 부분을 반환합니다.", "ad": "은(는) 피제수입니다.!은(는) 제수입니다."}, "RADIANS": {"a": "(angle)", "d": "도 단위로 표시된 각도를 라디안으로 변환합니다.", "ad": "은(는) 라디안으로 변환할 각도입니다."}, "RAND": {"a": "()", "d": "0보다 크거나 같고 1보다 작은, 균등하게 분포된 난수를 구합니다. 재계산 시에는 바뀝니다.", "ad": ""}, "RANDARRAY": {"a": "([행]; [열]; [최소]; [최대]; [정수])", "d": "난수의 배열을 반환합니다", "ad": "반환된 배열에 있는 행의 개수!반환된 배열에 있는 열의 개수!반환되기를 원하는 최소 수!반환되기를 원하는 최대 수!정수 또는 소수 값을 반환합니다. 정수는 TRUE, 소수는 FALSE"}, "RANDBETWEEN": {"a": "(bottom; top)", "d": "지정한 두 수 사이의 난수를 반환합니다.", "ad": "은(는) RANDBETWEEN 함수가 반환할 최소 정수값입니다.!은(는) RANDBETWEEN 함수가 반환할 최대 정수값입니다."}, "ROMAN": {"a": "(number; [form])", "d": "아라비아 숫자를 텍스트인 로마 숫자로 변환합니다.", "ad": "은(는) 변환할 아라비아 숫자입니다.!은(는) 원하는 로마 숫자의 형태를 나타내는 수입니다."}, "ROUND": {"a": "(number; num_digits)", "d": "수를 지정한 자릿수로 반올림합니다.", "ad": "은(는) 반올림하려는 수입니다.!은(는) 소수점 아래의 자릿수를 지정합니다."}, "ROUNDDOWN": {"a": "(number; num_digits)", "d": "0에 가까워지도록 수를 내림합니다.", "ad": "은(는) 내림하려는 실수입니다.!은(는) 내림하려는 자릿수입니다."}, "ROUNDUP": {"a": "(number; num_digits)", "d": "0에서 멀어지도록 수를 올림합니다.", "ad": "은(는) 올림하려는 실수입니다.!은(는) 올림하려는 자릿수입니다."}, "SEC": {"a": "(number)", "d": "각도의 시컨트 값을 구합니다.", "ad": "은(는) 라디안으로 표시된 각도입니다."}, "SECH": {"a": "(number)", "d": "각도의 하이퍼볼릭 시컨트 값을 구합니다.", "ad": "은(는) 라디안으로 표시된 각도입니다."}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "수식에 따라 멱급수의 합을 반환합니다.", "ad": "은(는) 멱급수에 대한 입력 값입니다.!은(는) x 멱급수의 초기항 차수입니다.!은(는) 급수에서 각 항목의 차수 n에 대한 증분입니다.!은(는) 연속된 각 x의 멱에 곱해지는 계수의 집합입니다."}, "SIGN": {"a": "(number)", "d": "수의 부호값을 반환합니다. 수가 양수이면 1을, 0이면 0을, 음수이면 -1을 돌려줍니다.", "ad": "은(는) 부호를 구할 실수입니다."}, "SIN": {"a": "(number)", "d": "각도의 사인 값을 구합니다.", "ad": "은(는) 라디안으로 표시된 각도입니다."}, "SINH": {"a": "(number)", "d": "하이퍼볼릭 사인 값을 구합니다.", "ad": "은(는) 실수입니다."}, "SQRT": {"a": "(number)", "d": "양의 제곱근을 구합니다.", "ad": "은(는) 제곱근을 구하려는 수입니다."}, "SQRTPI": {"a": "(number)", "d": "(number * Pi)의 제곱근을 반환합니다.", "ad": "은(는) p와 곱할 수입니다."}, "SUBTOTAL": {"a": "(function_num; ref1; ...)", "d": "목록이나 데이터베이스의 부분합을 구합니다.", "ad": "은(는) 목록 내에서 부분합을 계산하는 데 어떤 함수를 사용할 것인지 지정하는 1에서 11까지의 수입니다.!은(는) 부분합을 구하려는 최대 254개의 영역이나 셀의 주소입니다."}, "SUM": {"a": "(number1; [number2]; ...)", "d": "인수들의 합을 구합니다.", "ad": "은(는) 합계를 구하려는 값들로서 255개까지 지정할 수 있습니다. 논리값과 텍스트는 제외됩니다."}, "SUMIF": {"a": "(range; criteria; [sum_range])", "d": "주어진 조건에 의해 지정된 셀들의 합을 구합니다.", "ad": "은(는) 조건에 맞는지를 검사할 셀들입니다.!은(는) 더할 셀의 조건을 지정하는 수, 식 또는 텍스트입니다.!은(는) 합을 구할 실제 셀들입니다. 생략하면 범위 내의 셀들이 계산됩니다."}, "SUMIFS": {"a": "(sum_range; criteria_range; criteria; ...)", "d": "주어진 조건에 따라 지정되는 셀을 더합니다.", "ad": "은(는) 합을 구할 실제 셀입니다.!은(는) 조건을 적용시켜 계산할 셀 범위입니다.!은(는) 숫자, 식 또는 텍스트 형식으로 된 조건으로 더할 셀을 정의합니다."}, "SUMPRODUCT": {"a": "(array1; [array2]; [array3]; ...)", "d": "배열 또는 범위의 대응되는 값끼리 곱해서 그 합을 구합니다.", "ad": "은(는) 계산하려는 배열로서 2개에서 255개까지 지정할 수 있습니다. 모든 배열은 같은 차원이어야 합니다."}, "SUMSQ": {"a": "(number1; [number2]; ...)", "d": "인수의 제곱의 합을 구합니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다.", "ad": "은(는) 제곱의 합을 구하려는 인수로서 255개까지 지정할 수 있습니다."}, "SUMX2MY2": {"a": "(array_x; array_y)", "d": "두 배열에서 대응값의 제곱의 차이를 구한 다음 차이의 합을 구합니다.", "ad": "은(는) 첫 번째 배열 또는 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다.!은(는) 두 번째 배열 또는 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다."}, "SUMX2PY2": {"a": "(array_x; array_y)", "d": "두 배열에서 대응값의 제곱의 합의 합을 구합니다.", "ad": "은(는) 첫째 배열 또는 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다.!은(는) 둘째 배열 또는 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 배열이나 참조가 될 수 있습니다."}, "SUMXMY2": {"a": "(array_x; array_y)", "d": "두 배열이나 범위에서 대응값의 차이를 구한 다음 차이의 제곱의 합을 구합니다.", "ad": "은(는) 첫 번째 배열 또는 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 참조나 배열이 될 수 있습니다.!은(는) 두 번째 배열 또는 셀 범위입니다. 범위는 숫자, 이름, 숫자가 들어 있는 참조나 배열이 될 수 있습니다."}, "TAN": {"a": "(number)", "d": "각도의 탄젠트 값을 구합니다.", "ad": "은(는) 라디안으로 표시된 각도입니다."}, "TANH": {"a": "(number)", "d": "하이퍼볼릭 탄젠트 값을 구합니다.", "ad": "은(는) 실수입니다."}, "TRUNC": {"a": "(number; [num_digits])", "d": "지정한 자릿수만을 소수점 아래에 남기고 나머지 자리를 버립니다.", "ad": "은(는) 지정한 자릿수 아래를 잘라 낼 숫자입니다.!은(는) 잘라낼 소수점 이하의 자릿수입니다."}, "ADDRESS": {"a": "(row_num; column_num; [abs_num]; [a1]; [sheet_text])", "d": "지정된 행/열 번호를 가지고 셀 주소를 나타내는 텍스트를 만듭니다.", "ad": "은(는) 참조할 셀의 행 번호입니다. 즉, 행 1에 대한 Row_number는 1입니다.!은(는) 참조할 셀의 열 번호입니다. 즉, 열 D에 대한 Column_number는 4입니다.!은(는) 참조 영역의 유형을 지정합니다. 절대 참조는 1, 절대 행/상대 열 참조는 2, 상대 행/절대 열 참조는 3, 상대 참조는 4입니다.!은(는) A1이나 R1C1 참조 스타일을 지정하는 논리값입니다. 즉, A1 스타일은 1이거나 TRUE이고 R1C1 스타일은 0이거나 FALSE입니다.!은(는) 외부 참조로 사용될 워크시트 이름을 지정하는 텍스트입니다."}, "CHOOSE": {"a": "(index_num; value1; [value2]; ...)", "d": "인수 목록 중에서 하나를 고릅니다.", "ad": "은(는) 골라낼 인수의 위치를 지정합니다. Index_num은 1부터 254까지의 수이거나 1부터 254까지의 수에 대한 참조나 수식입니다.!은(는) 1부터 254까지의 수, 셀 참조, 정의된 이름, 수식, 함수, 텍스트 인수입니다."}, "COLUMN": {"a": "([reference])", "d": "참조 영역의 열 번호를 구합니다.", "ad": "은(는) 열 번호를 구하려는 셀 또는 셀 범위입니다. 생략하면 COLUMN 함수가 들어 있는 셀이 사용됩니다."}, "COLUMNS": {"a": "(array)", "d": "참조 영역이나 배열에 있는 열 수를 구합니다.", "ad": "은(는) 열 수를 구하려는 배열이나 배열식 또는 셀 범위의 주소입니다."}, "FORMULATEXT": {"a": "(reference)", "d": "수식을 문자열로 반환합니다.", "ad": "은(는) 수식에 대한 참조입니다."}, "HLOOKUP": {"a": "(lookup_value; table_array; row_index_num; [range_lookup])", "d": "배열의 첫 행에서 값을 검색하여, 지정한 행의 같은 열에서 데이터를 추출합니다.", "ad": "은(는) 표의 첫 행에서 찾으려는 값입니다. 값이나 셀 주소 또는 텍스트일 수 있습니다.!은(는) 데이터를 검색하고 추출하려는 표입니다. 표_범위는 범위에 대한 참조값이나 범위 이름이 될 수 있습니다.!은(는) 표_범위 내의 행 번호로, 값을 추출할 행을 지정합니다. 표의 첫 행 값은 행 1입니다.!은(는) 논리값으로서 비슷하게 일치하는 것을 찾으면 TRUE이고 정확하게 일치하는 것을 찾으면 FALSE입니다."}, "HYPERLINK": {"a": "(Link_location; [friendly_name])", "d": "하드 드라이브, 네트워크 서버, 인터넷 등에 저장된 문서로 이동할 바로 가기 키를 만듭니다.", "ad": "은(는) 하드 드라이브 위치, UNC 주소, URL 경로, 열리는 파일의 이름과 전체 경로입니다.!은(는) 셀에 표시하려는 숫자나 텍스트입니다. 생략하면 셀에 link_location 텍스트가 표시됩니다."}, "INDEX": {"a": "(array; row_num; [column_num]!reference; row_num; [column_num]; [area_num])", "d": "표나 범위 내에서 값이나 참조 영역을 구합니다.", "ad": "은(는) 배열로 입력된 셀의 범위입니다.!은(는) 값을 돌려줄 배열이나 참조의 행 번호를 지정합니다. 생략하면 column_num을 지정해야 합니다.!은(는) 값을 돌려줄 배열이나 참조의 열 번호를 지정합니다. 생략하면 row_num을 지정해야 합니다.!은(는) 하나 이상의 셀 범위를 포함하는 참조 영역입니다.!은(는) 값을 돌려줄 배열이나 참조의 행 번호를 지정합니다. 생략하면 column_num을 지정해야 합니다.!은(는) 값을 돌려줄 배열이나 참조의 열 번호를 지정합니다. 생략하면 row_num을 지정해야 합니다.!은(는) 값을 돌려줄 참조의 범위를 선택합니다. 선택하거나 입력한 첫째 영역이 1이고 둘째 영역이 2가 됩니다."}, "INDIRECT": {"a": "(ref_text; [a1])", "d": "텍스트 문자열로 지정한 셀 주소를 돌려줍니다.", "ad": "은(는) 셀의 주소로서, 주소 또는 텍스트 문자열 형태로 셀 주소로 정의된 이름인 A1 또는 R1C1 스타일의 셀 주소를 포함합니다.!은(는) ref_text 셀의 텍스트가 어떤 주소 형식인지 정의하는 논리값입니다. FALSE이면 R1C1 스타일이고 TRUE이거나 생략하면 A1 스타일입니다."}, "LOOKUP": {"a": "(lookup_value; lookup_vector; [result_vector]!lookup_value; array)", "d": "배열이나 한 행 또는 한 열 범위에서 값을 찾습니다. 이전 버전과의 호환성을 위해 제공됩니다.", "ad": "은(는) LOOKUP이 검색할_벡터에서 찾는 값입니다. 값은 숫자, 텍스트, 논리값, 값에 대한 이름이나 참조가 될 수 있습니다.!은(는) 텍스트, 숫자, 논리값의 한 행이나 한 열로만 이루어진 범위입니다.!은(는) 하나의 행이나 열로만 이루어진 범위입니다. 검색할_벡터와 크기가 같습니다.!은(는) LOOKUP이 배열에서 찾는 값입니다. 값은 숫자, 텍스트, 논리값, 값에 대한 이름이나 참조가 될 수 있습니다.!은(는) 검색할_값와 비교하려는 텍스트, 숫자, 논리값이 있는 셀 범위입니다."}, "MATCH": {"a": "(lookup_value; lookup_array; [match_type])", "d": "배열에서 지정된 순서상의 지정된 값에 일치하는 항목의 상대 위치 값을 찾습니다.", "ad": "은(는) 배열, 숫자, 텍스트, 논리값, 참조 등에서 찾으려고 하는 값입니다.!은(는) 참조 값, 값 배열, 배열 참조가 들어 있는 연속된 셀 범위입니다.!은(는) 되돌릴 값을 표시하는 숫자로 1, 0 또는 -1입니다."}, "OFFSET": {"a": "(reference; rows; cols; [height]; [width])", "d": "주어진 참조 영역으로부터 지정한 행과 열만큼 떨어진 위치의 참조 영역을 돌려줍니다.", "ad": "은(는) 기본 참조 영역입니다.!은(는) 기본 참조 영역의 첫 행과 출력할 영역의 첫 행 사이의 간격입니다.!은(는) 기본 참조 영역의 첫 열과 출력할 영역의 첫 열 사이의 간격입니다.!은(는) 출력하려는 참조 영역의 높이(행 수)입니다.!은(는) 출력하려는 참조 영역의 너비(열 수)입니다. 생략하면 기본 참조 영역과 동일하게 설정됩니다."}, "ROW": {"a": "([reference])", "d": "참조의 행 번호를 구합니다.", "ad": "은(는) 행 번호를 구하려는 셀 또는 셀 범위입니다. 생략하면 ROW 함수가 들어 있는 셀을 되돌립니다."}, "ROWS": {"a": "(array)", "d": "참조 영역이나 배열에 있는 행 수를 구합니다.", "ad": "은(는) 행 수를 구하려는 배열이나 배열식 또는 셀 범위의 주소입니다."}, "TRANSPOSE": {"a": "(array)", "d": "배열이나 범위의 행과 열을 바꿉니다.", "ad": "은(는) 바꾸려는 배열이나 워크시트의 셀 범위입니다."}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": " 범위나 배열에서 고유한 값을 반환 합니다.", "ad": " 고유 행 또는 열을 반환할 범위 또는 배열입니다.! 행을 서로 비교하고 고유 행을 반환합니다. 열을 서로 비교하고 고유 열을 반환합니다.! 배열 = TRUE에서 한 번만 발생 하는 행 또는 열을 반환합니다. = FALSE 또는 ommitted에서 모든 고유 행 또는 열을 반환합니다."}, "VLOOKUP": {"a": "(lookup_value; table_array; col_index_num; [range_lookup])", "d": "배열의 첫 열에서 값을 검색하여, 지정한 열의 같은 행에서 데이터를 돌려줍니다. 기본적으로 오름차순으로 표가 정렬됩니다.", "ad": "은(는) 표의 첫 열에서 찾으려는 값입니다. 값이나 셀 주소 또는 텍스트일 수 있습니다.!은(는) 데이터를 검색하고 추출하려는 표입니다. 표_범위는 범위 참조나 범위 이름이 될 수 있습니다.!은(는) 표_범위 내의 열 번호로, 값을 추출할 열을 지정합니다. 표의 첫 열 값은 열 1입니다.!은(는) 정확하게 일치하는 것을 찾으려면 FALSE를, 비슷하게 일치하는 것을 찾으려면 TRUE(또는 생략)를 지정합니다."}, "XLOOKUP": {"a": "(lookup_value; lookup_array; return_array; [if_not_found]; [match_mode]; [search_mode])", "d": "일치 항목에 대한 범위 또는 배열을 검색하고 두 번째 범위 또는 배열에서 해당 항목을 반환합니다. 기본적으로 정확하게 일치하는 항목이 사용됩니다.", "ad": "검색할 값입니다.!검색할 배열 또는 범위입니다.!반환할 배열 또는 범위입니다.!일치하는 항목이 없는 경우 반환됩니다.!검색할_범위에 대한 검색할_값 일치 방법을 지정하세요.!사용할 검색 모드를 지정하세요. 기본적으로 정방향 검색이 사용됩니다."}, "CELL": {"a": "(info_type; [reference])", "d": "는 셀의 서식이나 위치, 내용에 대한 정보를 반환합니다.", "ad": "반환할 셀 정보의 유형을 지정하는 텍스트 값입니다.!관련 정보가 필요한 셀입니다."}, "ERROR.TYPE": {"a": "(error_val)", "d": "오류 유형에 해당하는 번호를 구합니다.", "ad": "은(는) 해당 번호를 찾으려는 오류 값입니다. 오류 값이 들어 있는 셀 참조가 될 수도 있고 실제 오류 값이 될 수도 있습니다."}, "ISBLANK": {"a": "(value)", "d": "비어 있는 셀이면 TRUE를 돌려줍니다.", "ad": "은(는) 검사하려는 셀을 참조하는 셀이나 이름입니다."}, "ISERR": {"a": "(value)", "d": "값이 #N/A 이외의 오류인지 확인하여 TRUE 또는 FALSE를 반환합니다.", "ad": "은(는) 검사하려는 값입니다. 값은 셀, 수식이 될 수도 있고 셀, 수식, 값을 참조하는 이름이 될 수도 있습니다."}, "ISERROR": {"a": "(value)", "d": "값이 오류인지 확인하여 TRUE 또는 FALSE를 반환합니다.", "ad": "은(는) 검사하려는 값입니다. 값은 셀, 수식이 될 수도 있고 셀, 수식, 값을 참조하는 이름이 될 수도 있습니다."}, "ISEVEN": {"a": "(number)", "d": "숫자가 짝수이면 TRUE를 반환합니다.", "ad": "은(는) 검사할 값입니다."}, "ISFORMULA": {"a": "(reference)", "d": "수식을 포함하는 셀에 대한 참조인지 확인하고 TRUE 또는 FALSE를 반환합니다.", "ad": "은(는) 테스트할 셀에 대한 참조입니다. 참조는 셀을 참조하는 셀 참조, 수식 또는 이름일 수 있습니다."}, "ISLOGICAL": {"a": "(value)", "d": "값이 논리값이면 TRUE를 돌려줍니다.", "ad": "은(는) 검사하려는 값입니다. 값은 셀, 수식이 될 수도 있고 셀, 수식, 값을 참조하는 이름이 될 수도 있습니다."}, "ISNA": {"a": "(value)", "d": "값이 #N/A 오류 값인지 확인하여 TRUE 또는 FALSE를 반환합니다.", "ad": "은(는) 검사하려는 값입니다. 값은 셀, 수식이 될 수도 있고 셀, 수식, 값을 참조하는 이름이 될 수도 있습니다."}, "ISNONTEXT": {"a": "(value)", "d": "값이 텍스트가 아니면 TRUE를 돌려줍니다.", "ad": "은(는) 검사하려는 값입니다. 값은 셀, 수식이 될 수도 있고 셀, 수식, 값을 참조하는 이름이 될 수도 있습니다."}, "ISNUMBER": {"a": "(value)", "d": "값이 숫자이면 TRUE를 돌려줍니다.", "ad": "은(는) 검사하려는 값입니다. 값은 셀, 수식이 될 수도 있고 셀, 수식, 값을 참조하는 이름이 될 수도 있습니다."}, "ISODD": {"a": "(number)", "d": "숫자가 홀수이면 TRUE를 반환합니다.", "ad": "은(는) 검사할 값입니다."}, "ISREF": {"a": "(value)", "d": "값이 셀 주소이면 TRUE를 돌려줍니다.", "ad": "은(는) 검사하려는 값입니다. 값은 셀, 수식이 될 수도 있고 셀, 수식, 값을 참조하는 이름이 될 수도 있습니다."}, "ISTEXT": {"a": "(value)", "d": "값이 텍스트이면 TRUE를 돌려줍니다.", "ad": "은(는) 검사하려는 값입니다. 값은 셀, 수식이 될 수도 있고 셀, 수식, 값을 참조하는 이름이 될 수도 있습니다."}, "N": {"a": "(value)", "d": "숫자가 아닌 값은 숫자로, 날짜 값은 일련 번호로, TRUE 값은 1로, 그 외의 값은 0(영)으로 변환하여 돌려줍니다.", "ad": "은(는) 변환하려는 값입니다."}, "NA": {"a": "()", "d": "#N/A 오류 값을 반환합니다(값을 사용할 수 없음).", "ad": ""}, "SHEET": {"a": "([value])", "d": "참조된 시트의 시트 번호를 반환합니다.", "ad": "은(는) 시트 번호를 구하려는 시트 또는 참조의 이름입니다. 이 인수를 생략하면 해당 함수를 포함하는 시트 번호가 반환됩니다."}, "SHEETS": {"a": "([reference])", "d": "참조의 시트 수를 반환합니다.", "ad": "은(는) 포함된 시트 수를 알려는 참조입니다. 이 인수를 생략하면 해당 함수를 포함하는 통합 문서의 시트 수가 반환됩니다."}, "TYPE": {"a": "(value)", "d": "값의 데이터 형식을 나타내는 정수를 반환합니다. 숫자는 1, 텍스트는 2, 논리값은 4, 오류 값은 16, 배열은 64, 복합 데이터는 128입니다.", "ad": "은(는) 모든 값일 수 있습니다."}, "AND": {"a": "(logical1; [logical2]; ...)", "d": "인수가 모두 TRUE이면 TRUE를 반환합니다.", "ad": "은(는) TRUE 또는 FALSE 값을 가지는 조건으로 1개에서 255개까지 지정할 수 있으며 논리값, 배열, 참조가 될 수 있습니다."}, "FALSE": {"a": "()", "d": "논리값 FALSE를 돌려줍니다.", "ad": ""}, "IF": {"a": "(logical_test; [value_if_true]; [value_if_false])", "d": "논리 검사를 수행하여 TRUE나 FALSE에 해당하는 값을 반환합니다.", "ad": "은(는) TRUE나 FALSE로 계산될 값이나 식입니다.!은(는) logical_test가 TRUE일 때 돌려주는 값입니다. 생략하면 TRUE를 반환합니다. IF 함수를 일곱 번 중첩해서 쓸 수 있습니다.!은(는) logical_test가 FALSE일 때 돌려주는 값입니다. 생략하면 FALSE를 반환합니다."}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": "하나 이상의 조건이 충족되는지 확인하고 첫 번째 TRUE 조건에 해당하는 값을 반환합니다.", "ad": "은(는) TRUE 또는 FALSE로 계산될 수 있는 값 또는 식입니다.!은(는) Logical_test가 TRUE인 경우 반환되는 값입니다."}, "IFERROR": {"a": "(value; value_if_error)", "d": "식이나 식 자체의 값이 오류인 경우 value_if_error를 반환합니다.", "ad": "은(는) 값, 식 또는 참조입니다.!은(는) 값, 식 또는 참조입니다."}, "IFNA": {"a": "(value; value_if_na)", "d": "식 결과가 #N/A이면 지정한 값을 반환하고, 그렇지 않으면 식 결과를 반환합니다.", "ad": "은(는) 임의의 값이나 식 또는 참조입니다.!은(는) 임의의 값이나 식 또는 참조입니다."}, "NOT": {"a": "(logical)", "d": "TRUE 식에는 FALSE를, FALSE 식에는 TRUE를 돌려줍니다.", "ad": "은(는) TRUE나 FALSE로 계산될 수 있는 값 또는 식입니다."}, "OR": {"a": "(logical1; [logical2]; ...)", "d": "하나 이상의 인수가 TRUE이면 TRUE를 반환합니다. 인수가 모두 FALSE인 경우에만 FALSE를 반환합니다.", "ad": "은(는) TRUE 또는 FALSE 값을 가지는 조건으로 1개에서 255개까지 지정할 수 있습니다."}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "값의 목록에 대해 식을 계산하고 첫 번째로 일치하는 값에 상응하는 결과를 반환합니다. 일치 항목이 없는 경우 선택적인 기본값이 반환됩니다.", "ad": "은(는) 평가될 식입니다.!은(는) 식과 비교되는 값입니다.!은(는) 상응하는 값이 식과 일치하는 경우 반환되는 결과입니다."}, "TRUE": {"a": "()", "d": "논리값 TRUE를 돌려줍니다.", "ad": ""}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "모든 인수의 논리 '배타적 Or' 값을 구합니다.", "ad": "은(는) TRUE 또는 FALSE 값을 가지는 조건으로 1개에서 254개까지 지정할 수 있으며 논리값, 배열, 참조가 될 수 있습니다."}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " 문자를 구분하기 전의 텍스트를 반환합니다.", "ad": "구분 기호를 검색할 텍스트입니다.!구분 기호로 사용할 문자 또는 문자열입니다.!원하는 구분 기호 항목입니다. 기본값은 1입니다. 음수는 끝에서부터 검색됩니다.!텍스트에서 구분 기호 일치 항목을 검색합니다. 기본적으로 대/소문자를 구분하는 일치가 수행됩니다.!텍스트 끝에 구분 기호를 일치시킬지 여부를 지정합니다. 기본적으로 일치하지 않습니다.!일치하는 항목이 없으면 반환됩니다. 기본적으로 #N/A가 반환됩니다."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " 문자를 구분한 후의 텍스트를 반환합니다.", "ad": "구분 기호를 검색할 텍스트입니다.!구분 기호로 사용할 문자 또는 문자열입니다.!원하는 구분 기호 항목입니다. 기본값은 1입니다. 음수는 끝에서부터 검색됩니다.!텍스트에서 구분 기호 일치 항목을 검색합니다. 기본적으로 대/소문자를 구분하는 일치가 수행됩니다.!텍스트 끝에 구분 기호를 일치시킬지 여부를 지정합니다. 기본적으로 일치하지 않습니다.!일치하는 항목이 없으면 반환됩니다. 기본적으로 #N/A가 반환됩니다."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "구분 기호를 사용하여 텍스트를 행 또는 열로 분할합니다.", "ad": "분할할 텍스트입니다!열을 분할할 문자 또는 문자열입니다.!행을 분할할 문자 또는 문자열입니다.!빈 셀을 무시할지 여부입니다. 기본값은 FALSE입니다.!구분 기호 일치를 텍스트에서 검색합니다. 기본적으로 대/소문자 구분 일치가 수행됩니다.!패딩에 사용할 값입니다. 기본값은 #N/A가 사용됩니다."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "지정된 수의 값 뒤에 행 또는 열 벡터를 래핑합니다.", "ad": "래핑할 벡터 또는 참조입니다.!행당 최대 값 수입니다.!패딩할 값입니다. 기본값은 #N/A입니다."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "수직으로 배열을 하나의 배열로 쌓습니다.", "ad": " 쌓을 배열 또는 참조입니다."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "수평으로 배열을 하나의 배열로 쌓습니다.", "ad": " 쌓을 배열 또는 참조입니다."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "배열이나 참조에서 행을 반환합니다.", "ad": "반환할 행을 포함하는 배열 또는 참조입니다.!반환할 행의 개수입니다."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "배열 또는 참조에서 열을 반환합니다.", "ad": "반환할 열을 포함하는 배열 또는 참조입니다.!반환할 열의 개수입니다."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "배열을 하나의 열로 반환합니다.", "ad": "열로 반환할 배열 또는 참조입니다.!특정 유형의 값을 무시할지 여부입니다. 기본적으로 어떤 값도 무시되지 않습니다.!열별로 배열을 검사합니다. 기본적으로 배열은 행별로 검사됩니다."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "배열을 하나의 행으로 반환합니다.", "ad": "행으로 반환할 배열 또는 참조입니다.!특정 유형의 값을 무시할지 여부입니다. 기본적으로 어떤 값도 무시되지 않습니다.!열별로 배열을 검사합니다. 기본적으로 배열은 행별로 검사됩니다."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "지정된 수의 값 뒤에 행 또는 열 벡터를 래핑합니다.", "ad": "래핑할 벡터 또는 참조입니다.!열당 최대 값 수입니다.!패딩할 값입니다. 기본값은 #N/A입니다."}, "TAKE": {"a": "(array, rows, [columns])", "d": "배열 시작 또는 끝에서 행 또는 열을 반환합니다.", "ad": "행 또는 열을 가져올 배열입니다.!가져올 행 수입니다. 음수 값은 배열의 끝에서 가져옵니다.!가져갈 열 수입니다. 음수 값은 배열의 끝에서 가져옵니다."}, "DROP": {"a": "(array, rows, [columns])", "d": "배열 시작 또는 끝에서 행 또는 열을 삭제합니다.", "ad": "행 또는 열을 삭제할 배열입니다.!삭제할 행 수입니다. 음수 값은 배열의 끝에서 삭제됩니다.!삭제할 열 수입니다. 배열의 끝에서 음수 값이 삭제됩니다."}, "SEQUENCE": {"a": "(rows, [columns], [start], [step])", "d": "숫자 시퀀스를 반환합니다", "ad": "반환할 행 개수!반환할 열 개수!시퀀스의 첫 번째 숫자!시퀀스의 각 후속 값을 증분하는 값"}, "EXPAND": {"a": "(array, rows, [columns], [pad_with])", "d": "배열을 지정된 차원으로 확장합니다.", "ad": "확장할 배열.!확장된 배열의 행 수입니다. 누락된 경우 행이 확장되지 않습니다.!확장된 배열의 열 수입니다. 누락된 경우 열이 확장되지 않습니다.!채울 값입니다. 기본값은 #N/A입니다."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "배열에 있는 항목의 상대 위치를 반환합니다. 기본적으로 정확히 일치해야 합니다", "ad": "은(는) 검색할 값입니다!은(는) 검색할 배열 또는 범위입니다!검색할_범위의 값에 대한 검색할_값 일치 방법을 지정하세요!사용할 검색 모드를 지정하세요. 기본적으로 정방향 검색이 사용됩니다"}, "FILTER": {"a": "(배열, 포함, [if_empty])", "d": "범위 또는 배열 필터링", "ad": "필터링할 범위 또는 배열!TRUE가 유지할 행 또는 열을 나타내는 부울 배열!유지된 항목이 없는 경우 반환"}, "ARRAYTOTEXT": {"a": "(array, [format])", "d": "배열의 텍스트 표현을 반환 합니다.", "ad": " 텍스트로 나타낼 배열입니다.!은 (는) 텍스트의 서식입니다."}, "SORT": {"a": "(array, [sort_index], [sort_order], [by_col])", "d": "범위 또는 배열을 정렬", "ad": "정렬할 범위 또는 배열!정렬 기준이 될 행 또는 열을 나타내는 번호!원하는 정렬 순서를 나타내는 번호. 오름차순의 경우 1(기본값), 내림차순의 경우 -1!원하는 정렬 방향을 나타내는 논리값: 행 기준 정렬의 경우 FALSE(기본값), 열 기준 정렬의 경우 TRUE"}, "SORTBY": {"a": "(array, by_array, [sort_order], ...)", "d": "해당 범위 또는 배열의 값을 기준으로 범위 또는 배열을 정렬합니다", "ad": "정렬할 범위 또는 배열!정렬할 범위 또는 배열!원하는 정렬 순서를 나타내는 번호, 오름차순의 경우 1(기본값), 내림차순의 경우 -1"}, "GETPIVOTDATA": {"a": "(data_field; pivot_table; [field]; [item]; ...)", "d": "피벗 테이블 보고서 내에 저장된 데이터를 반환합니다.", "ad": "은(는) 데이터를 추출하려는 데이터 필드의 이름입니다.!은(는) 셀에 대한 참조이거나, 검색하려는 데이터가 포함된 피벗 테이블 보고서 셀의 범위입니다.!은(는) 참조할 필드입니다.!은(는) 참조할 필드 개체입니다."}, "IMPORTRANGE": {"a": "(스프레드시트_URL, 범위_문자열)", "d": "지정된 스프레드시트에서 셀 범위를 가져옵니다.", "ad": "가져올 데이터가 있는 스프레드시트의 URL입니다.!가져올 범위."}}