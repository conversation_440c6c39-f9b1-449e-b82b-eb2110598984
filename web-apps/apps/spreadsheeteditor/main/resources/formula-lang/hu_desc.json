{"DATE": {"a": "(év; hónap; nap)", "d": "Eredménye a dátumot dátum- és időértékben megadó szám", "ad": "egy 1900 vagy 1904 (a munkafüzet dátumrendszerétől függően) és 9999 közötti évszám!a hónap száma az éven belül (1-12)!a nap száma a hónapon belül (1-31)"}, "DATEDIF": {"a": "(kez<PERSON><PERSON>_dátum; z<PERSON><PERSON><PERSON>_dátum; egység)", "d": "<PERSON><PERSON><PERSON> d<PERSON> kö<PERSON>ok, hónapok vagy évek számát számítja ki", "ad": "<PERSON>gy adott időszak első vagy kezdő dátumát jelölő dátum!Az időszak utolsó, vagyis záró dátumát megadó dátum!A visszaadni kívánt információ típusa"}, "DATEVALUE": {"a": "(dátum_szöveg)", "d": "Szövegként megadott dátumot olyan sz<PERSON>mm<PERSON> alak<PERSON>t <PERSON>t, amely dátum- és időértékben adja meg a dátumot.", "ad": "a(z) Spreadsheet Editor valamely dátumformátumában szövegesen megadott dátum 1900. 01. 01. vagy 1904. 01. 01. (a munkafüzet dátumrendszerétől függően) és 9999. 12. 31. köz<PERSON>tt"}, "DAY": {"a": "(időérték)", "d": "A hónap napját adja meg 1 és 31 közötti számmal", "ad": "Spreadsheet Editor dátum- és időértékben megadott szám"}, "DAYS": {"a": "(kez<PERSON><PERSON>_dátum; z<PERSON><PERSON><PERSON>_dátum)", "d": "A két dátum kö<PERSON>ötti napok számát adja eredményül.", "ad": "A kezdő_dátum és a z<PERSON>r<PERSON>_dátum az a két dátum, amelyek között tudni szeretné a napok számát!A kezdő_dátum és a zár<PERSON>_dátum az a két dátum, amelyek között tudni szeretné a napok számát"}, "DAYS360": {"a": "(kez<PERSON><PERSON>_dátum; z<PERSON><PERSON><PERSON>_dátum; [mó<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> d<PERSON> közé eső napok számát adja meg a 360 napos naptár (tizenkét 30 napos hónap) alapján.", "ad": "a kezdő_dátum és a z<PERSON>r<PERSON>_dátum az a két dátum, amelynek napban mért távolságát meg szeretné tudni!a kezdő_dátum és a záró_dátum az a két dátum, amelynek napban mért távolságát meg szeretné tudni!a számítási módszert meghatározó logikai érték: ha HAMIS vagy elhagyja, USA-beli módszer; ha IGAZ, európai módszer."}, "EDATE": {"a": "(kezd<PERSON>_dátum; hónapok)", "d": "A kezdő_dátum-nál hónapok hónappal korábbi vagy későbbi dátum dátumértékét adja eredményül", "ad": "a kezdő_dátum dátumértéke!a hónapok száma a kezdő_dátum előtt vagy után"}, "EOMONTH": {"a": "(kezd<PERSON>_dátum; hónapok)", "d": "A megadott számú hónapnál korábbi vagy későbbi hónap utolsó napjának dátumértékét adja eredményül.", "ad": "a kezdő_dátum dátumértéke!a hónapok száma a kezdő_dátum előtt vagy után"}, "HOUR": {"a": "(időérték)", "d": "Az órát adja meg 0 (0:00, a<PERSON><PERSON>) és 23 (este 11:00) közötti sz<PERSON>mmal.", "ad": " Spreadsheet Editor dátum- és időértékben megadott s<PERSON>, illetve időformátum<PERSON> s<PERSON>öve<PERSON>, például 16:48:00 vagy 4:48:00 du."}, "ISOWEEKNUM": {"a": "(d<PERSON><PERSON>)", "d": "<PERSON><PERSON> adott d<PERSON>hoz tartozó hét ISO rendszer szerinti sorszámát adja eredményül az évben.", "ad": "a dátumok és időpontok számolásához az Spreadsheet Editor által haszonált dátum- és időérték"}, "MINUTE": {"a": "(időérték)", "d": "A percet adja meg 0 és 59 közötti számmal.", "ad": "Spreadsheet Editor dátum- és időértékben megadott s<PERSON>, illetve időformátum<PERSON> s<PERSON>öve<PERSON>, például 16:48:00 vagy 4:48:00 du."}, "MONTH": {"a": "(időérték)", "d": "A hónapot adja meg 1 (január) és 12 (december) közötti számmal.", "ad": "Spreadsheet Editor dátum- és időértékben megadott szám"}, "NETWORKDAYS": {"a": "(kezd<PERSON>_dátum; vég_dátum; [ünne<PERSON><PERSON>])", "d": "<PERSON><PERSON>t adja meg, hogy a két dátum kö<PERSON>ött hány teljes munkana<PERSON> van.", "ad": "a kezdő_dátum dátumértéke!a vég_dátum dátumértéke!a nem munkanap napok (állami, egyházi stb. ünnepek) dátumértékét tartalmazó tömb"}, "NETWORKDAYS.INTL": {"a": "(kezd<PERSON>_dátum; vég_dátum; [hétvége]; [ü<PERSON><PERSON><PERSON>])", "d": "A<PERSON>t adja meg, hogy a két dátum között és az egyéni hétvége-paraméterek mellett hány teljes munkana<PERSON> van", "ad": "a kezdő_dátum dátumértéke!a vég_dátum dátumértéke!egy sz<PERSON>m, illetve a hétvégéket jelző szöveg!a nem munkanapok (állami, egyházi stb. ünnepek) dátumértékét tartalmazó, nem kötelez<PERSON> halmaz"}, "NOW": {"a": "()", "d": "Az aktuális dátumot és időpontot adja meg dátum és idő formátumban.", "ad": ""}, "SECOND": {"a": "(időérték)", "d": "A másodpercet adja meg 0 és 59 közötti számmal.", "ad": "Spreadsheet Editor dátum- és időértékben megadott s<PERSON>, illetve időformátum<PERSON> s<PERSON>öve<PERSON>, például 16:48:23 vagy 4:48:47 du."}, "TIME": {"a": "(óra; perc; másodperc)", "d": "<PERSON><PERSON>, perc, másodperc alakban megadott időpont időértékét adja meg időformátum alakban", "ad": "az órát jelölő szám, 0 és 23 között lehet!a percet jelölő szám, 0 és 59 között lehet!a másodpercet jelölő szám, 0 és 59 között lehet"}, "TIMEVALUE": {"a": "(idő_szöveg)", "d": "A szövegként megadott időpontot időértékké, azaz 0 (0:00:00, v<PERSON><PERSON><PERSON>) és 0,999988426 (este 11:59:59) köz<PERSON>tti számmá alakítja át. A képlet beírását követően időformátumot kell hozzárendelni", "ad": "a(z) Spreadsheet Editor valamely időformátumában szövegként megadott időpont (a szövegben lévő dátumadatok nem lesznek figyelembe véve)"}, "TODAY": {"a": "()", "d": "Visszatérési értéke az aktuális dátum dátumkén<PERSON> formáz<PERSON>.", "ad": ""}, "WEEKDAY": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; [ered<PERSON><PERSON>_tí<PERSON>a])", "d": "<PERSON>gy d<PERSON> a hét egy napját azonosító számot ad eredményül, 1-t<PERSON>l 7-ig.", "ad": "dátumot jelölő szám!szám: vasárnap=1 -- szombat=7 számozáshoz 1;hétfő=1 -- vasárnap=7 számozáshoz 2; hétfő=0 -- vasárnap=6 számozáshoz 3"}, "WEEKNUM": {"a": "(sors<PERSON><PERSON><PERSON>; [ered<PERSON><PERSON>_tí<PERSON>a])", "d": "<PERSON><PERSON> adott dá<PERSON>z tartozó hét sorszámát adja eredményül az évben.", "ad": "a dátumok és időpontok számolásához az Spreadsheet Editor által haszonált dátum- és időérték!szám (1 vagy 2), amely a hét kezdőnapját hatá<PERSON>zza meg"}, "WORKDAY": {"a": "(kezd<PERSON>_dátum; napok; [ünne<PERSON>k])", "d": "A kezdő dátumnál napok munkanappal korábbi vagy későbbi dátum dátumértékét adja eredményül.", "ad": "a kezdő_dátum dátumértéke!napok száma ünnep- és munkaszüneti napok nélkül a kezdő_dátum előtt vagy után!a nem munkanap napok (állami, egyházi stb. ünnepek) dátumértékét tartalmazó tömb"}, "WORKDAY.INTL": {"a": "(kezd<PERSON>_dátum; napok; [hétvége]; [ü<PERSON><PERSON><PERSON>])", "d": "A megadott szá<PERSON>ú munkanap el<PERSON>tti vagy utáni dátumértéket adja meg, az egyéni hétvége-paraméterek mellett", "ad": "a kezdő_dátum dátumértéke!a nem hétvégére vagy ünnepre eső napok száma a kezdő_dátum előtt vagy után!a hétvégéket megadó szám vagy karakterlánc!a nem munkanapok (állami, egyházi stb. ünnepek) dátumértékét tartalmazó, nem kötelez<PERSON> halmaz"}, "YEAR": {"a": "(időérték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az adott dátum melyik évre esik (1900 és 9999 közötti egész szám).", "ad": "Spreadsheet Editor dátum- és időértékben megadott szám"}, "YEARFRAC": {"a": "(kezd<PERSON>_dátum; vég_dátum; [alap])", "d": "A kezdő_dátum és a vég_dátum közötti teljes napok számát törtévként fejezi ki.", "ad": "a kezdő_dátum dátumértéke!a vég_dátum dátumértéke!az évtöredék számításának alapja"}, "BESSELI": {"a": "(x; n)", "d": "Az In(x) módosított Bessel-függvény értékét adja eredményül.", "ad": "az az érték, amelyre a függvény értékét ki kell s<PERSON>ítani!a Bessel-függvény rendje"}, "BESSELJ": {"a": "(x; n)", "d": "A Jn(x) Bessel-függvény értékét adja er<PERSON>mé<PERSON>ül.", "ad": "az az érték, amelyre a függvény értékét ki kell s<PERSON>ítani!a Bessel-függvény rendje"}, "BESSELK": {"a": "(x; n)", "d": "A Kn(x) módosított Bessel-függvény értékét adja eredményül.", "ad": "az az érték, amelyre a függvény értékét ki kell s<PERSON>ítani!a Bessel-függvény rendje"}, "BESSELY": {"a": "(x; n)", "d": "Az Yn(x) módosított Bessel-függvény értékét adja eredményül.", "ad": "az az érték, amelyre a függvény értékét ki kell s<PERSON>ítani!a Bessel-függvény rendje"}, "BIN2DEC": {"a": "(szám)", "d": "<PERSON><PERSON><PERSON> s<PERSON> de<PERSON> alakít á<PERSON>.", "ad": "az átalakí<PERSON><PERSON><PERSON><PERSON>"}, "BIN2HEX": {"a": "(szám; [jegyek])", "d": "<PERSON><PERSON><PERSON> s<PERSON> hexadeci<PERSON> alakít <PERSON>.", "ad": "az átalakítandó bináris szám!a használandó karakterek száma"}, "BIN2OCT": {"a": "(szám; [jegyek])", "d": "<PERSON><PERSON><PERSON> s<PERSON> alakít <PERSON>.", "ad": "az átalakítandó bináris szám!a használandó karakterek száma"}, "BITAND": {"a": "(szám1; szám2)", "d": "<PERSON><PERSON><PERSON> „és” műveletet végez", "ad": "a kiértékelendő bináris szám decimális alakja!a kiértékelendő bináris szám decimális alakja"}, "BITLSHIFT": {"a": "(sz<PERSON>m; el<PERSON><PERSON><PERSON>_mérté<PERSON>)", "d": "<PERSON><PERSON> el<PERSON>ás_mértéke bittel balra tolt sz<PERSON>mot adja v<PERSON>za", "ad": "a kiértékelendő bináris szám decimális alakja!ah<PERSON><PERSON> bittel balra szeretné tolni a számot"}, "BITOR": {"a": "(szám1; szám2)", "d": "<PERSON><PERSON><PERSON> s<PERSON> „vagy” műveletet végez", "ad": "a kiértékelendő bináris szám decimális alakja!a kiértékelendő bináris szám decimális alakja"}, "BITRSHIFT": {"a": "(sz<PERSON>m; el<PERSON><PERSON><PERSON>_mérté<PERSON>)", "d": "<PERSON><PERSON> el<PERSON>ás_mértéke bittel jobbra tolt számot adja vissza", "ad": "a kiértékelendő bináris szám decimális alakja!ah<PERSON>y bittel jobbra szeretné tolni a számot"}, "BITXOR": {"a": "(szám1; szám2)", "d": "<PERSON><PERSON><PERSON> „kizárólagos vagy” műveletet végez", "ad": "a kiértékelendő bináris szám decimális alakja!a kiértékelendő bináris szám decimális alakja"}, "COMPLEX": {"a": "(val<PERSON>_szám; képzetes_szám; [képz_jel])", "d": "Valós és képzetes részekből komplex számot képez.", "ad": "a komplex szám valós része!a komplex szám képzetes része!a képzetes egység jele"}, "CONVERT": {"a": "(szám; miből; mibe)", "d": "Mértékegységeket vált át.", "ad": "a miből mértékegységben megadott, átv<PERSON><PERSON>ndó szám!az a mértékegység, amelyben a szám meg van adva!az eredmény mértékegysége"}, "DEC2BIN": {"a": "(szám; [jegyek])", "d": "Decim<PERSON><PERSON> s<PERSON><PERSON> alakí<PERSON>.", "ad": "az átalakítandó decimális egész szám!a használandó karakterek száma"}, "DEC2HEX": {"a": "(szám; [jegyek])", "d": "Decimális sz<PERSON>mot hexadecimális<PERSON>á alakít á<PERSON>.", "ad": "az átalakítandó decimális egész szám!a használandó karakterek száma"}, "DEC2OCT": {"a": "(szám; [jegyek])", "d": "Decimális sz<PERSON><PERSON> ok<PERSON>á alakít át.", "ad": "az átalakítandó decimális egész szám!a használandó karakterek száma"}, "DELTA": {"a": "(szám1; [szám2])", "d": "Azt vizsg<PERSON><PERSON><PERSON>, hogy két érték egyenlő-e.", "ad": "az első szám!a második szám"}, "ERF": {"a": "(als<PERSON>_határ; [f<PERSON><PERSON>_határ])", "d": "A hibaintegrál vagy hibafüggvény értékét adja eredményül.", "ad": "az integrál alsó határa!az integrál felső határa"}, "ERF.PRECISE": {"a": "(x)", "d": "A hibaintegrál értékét adja eredményül.", "ad": "A HIBAF.PONTOS függvény integrálásának alsó határa."}, "ERFC": {"a": "(x)", "d": "A hibaintegrál komplemensének értékét adja eredményül.", "ad": "az improprius integr<PERSON>l al<PERSON>"}, "ERFC.PRECISE": {"a": "(x)", "d": "A hibaintegrál komplemensének értékét adja eredményül.", "ad": "A HIBAFKOMPLEMENTER.PONTOS függvény integrálásának alsó határa."}, "GESTEP": {"a": "(szám; [kü<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON>t viz<PERSON>g<PERSON><PERSON><PERSON>, hogy egy szám nagyobb-e egy adott küszöbértéknél.", "ad": "a vizsgálandó érték!a küszöbérték"}, "HEX2BIN": {"a": "(szám; [jegyek])", "d": "Hexadecimális s<PERSON><PERSON><PERSON> alakít <PERSON>.", "ad": "az átalakítandó hexadecimális szám!a használandó karakterek száma"}, "HEX2DEC": {"a": "(szám)", "d": "Hexadecimális számot decimális<PERSON>á alakít át.", "ad": "az átalakítandó hexadecimális szám"}, "HEX2OCT": {"a": "(szám; [jegyek])", "d": "Hexadecimális sz<PERSON>mot oktális<PERSON>á alakít át.", "ad": "az átalakítandó hexadecimális szám!a használandó karakterek száma"}, "IMABS": {"a": "(k_szám)", "d": "Komplex szám abszolút érték<PERSON>t (modulusát) adja eredmé<PERSON>ül.", "ad": "az a komplex szám, amelynek abszolút értékét keressük"}, "IMAGINARY": {"a": "(k_szám)", "d": "Komplex szám képzetes részét adja eredményül.", "ad": "az a komplex szám, amelynek képzetes részét keressük"}, "IMARGUMENT": {"a": "(k_szám)", "d": "A komplex szám radiánban kifejezett argumentumát adja eredményül.", "ad": "az a komplex szám, amelynek argumentumát keressük"}, "IMCONJUGATE": {"a": "(k_szám)", "d": "Komplex szám komplex konjugáltját adja eredményül.", "ad": "az a komplex szám, amelynek a konjugáltját keressük"}, "IMCOS": {"a": "(k_szám)", "d": "Komplex szám koszinuszát adja eredményül.", "ad": "az a komplex szám, amelynek a koszinuszát keressük"}, "IMCOSH": {"a": "(k_szám)", "d": "Egy komplex szám hiperbolikus koszinuszát számítja ki", "ad": "az a komplex szám, am<PERSON><PERSON>k a hiperbolikus koszinusz<PERSON> keresi"}, "IMCOT": {"a": "(k_szám)", "d": "Egy komplex szám kotangensét számítja ki", "ad": "az a komplex szám, amelynek a kotangensét keresi"}, "IMCSC": {"a": "(k_szám)", "d": "Egy komplex szám koszekánsát számítja ki", "ad": "az a komplex szám, amelynek a koszekánsát keresi"}, "IMCSCH": {"a": "(k_szám)", "d": "Egy komplex szám hiperbolikus koszekánsát számítja ki", "ad": "az a komplex szám, am<PERSON><PERSON><PERSON> a hiperbolikus koszekáns<PERSON> keresi"}, "IMDIV": {"a": "(k_szám1; k_szám2)", "d": "Két komplex szám hányadosát adja eredményül.", "ad": "a komplex számláló vagy osztandó!a komplex nevező vagy osztó"}, "IMEXP": {"a": "(k_szám)", "d": "Az e szám komplex kitevőjű hatványát adja eredményül.", "ad": "az a komplex szám, amely<PERSON> e-t emelni kívánjuk"}, "IMLN": {"a": "(k_szám)", "d": "Komplex szám természetes logaritmusát adja eredményül.", "ad": "az a komplex szám, amelynek természetes logaritmusát kere<PERSON>ük"}, "IMLOG10": {"a": "(k_szám)", "d": "Komplex szám tízes alapú logaritmusát adja eredményül.", "ad": "az a komplex szám, amelynek tízes alapú logaritmusát kere<PERSON>ük"}, "IMLOG2": {"a": "(k_szám)", "d": "Komplex szám kettes alapú logaritmusát adja eredményül.", "ad": "az a komplex szám, amelynek kettes alapú logaritmusát kere<PERSON>ük"}, "IMPOWER": {"a": "(k_szám; szám)", "d": "Komplex szám hatványát adja eredményül.", "ad": "a hatványozandó komplex szám!a hatványkitevő"}, "IMPRODUCT": {"a": "(k_szám1; [k_szám2]; ...)", "d": "1–255 komplex szám szorzatának kiszámítása", "ad": "k_szám1, k_szám2,... : az összeszorzandó 1–255 komplex szám."}, "IMREAL": {"a": "(k_szám)", "d": "Komplex szám valós részét adja eredményül.", "ad": "az a komplex szám, amelynek valós részét keressük"}, "IMSEC": {"a": "(k_szám)", "d": "Egy komplex szám sze<PERSON>ánsát számítja ki", "ad": "az a komplex szám, amelynek a szekánsát keresi"}, "IMSECH": {"a": "(k_szám)", "d": "Egy komplex szám hiperbolikus szekánsát számítja ki", "ad": "az a komplex szám, am<PERSON><PERSON><PERSON> a hiperbolikus szekán<PERSON><PERSON> keresi"}, "IMSIN": {"a": "(k_szám)", "d": "Komplex szám szinuszát adja eredményül.", "ad": "az a komplex szám, amelynek a szinuszát keressük"}, "IMSINH": {"a": "(k_szám)", "d": "Egy komplex szám hiperbolikus szinuszát számítja ki", "ad": "az a komplex szám, am<PERSON><PERSON><PERSON> a hiperbolikus szinusz<PERSON> keresi"}, "IMSQRT": {"a": "(k_szám)", "d": "Komplex szám négyzetgyökét adja eredményül.", "ad": "az a komplex szám, amelynek négyzetgyökét keressük"}, "IMSUB": {"a": "(k_szám1; k_szám2)", "d": "Két komplex szám különbségét adja eredményül.", "ad": "az a komplex szám, amelyb<PERSON>l k_szám2-t ki kell vonni!az a komplex szám, amelyet a k_szám1-ből ki kell vonni"}, "IMSUM": {"a": "(k_szám1; [k_szám2]; ...)", "d": "Komplex számok összegének visszakeresése", "ad": "az összeadandó 1–255 komplex szám"}, "IMTAN": {"a": "(k_szám)", "d": "Egy komplex szám tan<PERSON>ét számítja ki", "ad": "az a komplex szám, am<PERSON><PERSON><PERSON> a tangensét keresi"}, "OCT2BIN": {"a": "(szám; [jegyek])", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> alak<PERSON>.", "ad": "az átalakítandó oktális szám!a használandó karakterek száma"}, "OCT2DEC": {"a": "(szám)", "d": "<PERSON><PERSON><PERSON><PERSON> sz<PERSON><PERSON> deci<PERSON>á alakít <PERSON>.", "ad": "az átalakítandó oktá<PERSON> s<PERSON>"}, "OCT2HEX": {"a": "(szám; [jegyek])", "d": "<PERSON><PERSON><PERSON><PERSON> sz<PERSON>mot hexadecimális<PERSON>á alakít <PERSON>.", "ad": "az átalakítandó oktális szám!a használandó karakterek száma"}, "DAVERAGE": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "Egy lista- v<PERSON><PERSON> ad<PERSON>zisoszlopban lévő azon értékek átlagát számítja ki, melyek megfelelnek a megadott feltételeknek", "ad": "a listát vagy adatbázist alkotó cellatartomány. Az adatbázis egymással kapcsolatos adatokból álló lista!vagy az oszlopfelirat idézőjelek között, vagy a listában az oszlop helyét megadó szám!a megadott feltételeket tartalmazó cellatartomány. A tartományban szerepel egy oszlopfelirat és alatta egy cellával a feltételt megadó címke"}, "DCOUNT": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON><PERSON> adat<PERSON><PERSON><PERSON><PERSON> adott feltételeknek eleget tevő rekordjaiban megszámolja, h<PERSON><PERSON> da<PERSON> s<PERSON><PERSON> van egy adott mezőben (oszlopban).", "ad": "a listát vagy adatbázist alkotó cellatartomány. Az adatbázis egymással kapcsolatos adatokból álló lista.!vagy az oszlopfelirat idézőjelek között, vagy a listában az oszlop helyét megadó szám!a megadott feltételeket tartalmazó cellatartomány. A tartományban szerepel egy oszlopfelirat és alatta egy cellával a feltételt megadó címke."}, "DCOUNTA": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON><PERSON> adat<PERSON><PERSON><PERSON><PERSON> adott feltételeknek eleget tevő rekordjaiban megszámolja, h<PERSON><PERSON> darab nem üres cella van egy adott mezőben (oszlopban).", "ad": "a listát vagy adatbázist alkotó cellatartomány. Az adatbázis egymással kapcsolatos adatokból álló lista.!vagy az oszlopfelirat idézőjelek között, vagy a listában az oszlop helyét megadó szám!a megadott feltételeket tartalmazó cellatartomány. A tartományban szerepel egy oszlopfelirat és alatta egy cellával a feltételt megadó címke."}, "DGET": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON><PERSON> ad<PERSON>ból egyetlen olyan mezőt vesz ki, amely megfelel a megadott feltételeknek.", "ad": "a listát vagy adatbázist alkotó cellatartomány. Az adatbázis egymással kapcsolatos adatokból álló lista.!vagy az oszlopfelirat idézőjelek között, vagy a listában az oszlop helyét megadó szám!a megadott feltételeket tartalmazó cellatartomány. A tartományban szerepel egy oszlopfelirat és alatta egy cellával a feltételt megadó címke."}, "DMAX": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON>z adatb<PERSON><PERSON><PERSON> adott feltételeknek eleget tevő rekordjaiból álló mező<PERSON> (oszlopban) lévő legnagyobb számot adja er<PERSON>ményül", "ad": "a listát vagy adatbázist alkotó cellatartomány. Az adatbázis egymással kapcsolatos adatokból álló lista!vagy az oszlopfelirat idézőjelek között, vagy a listában az oszlop helyét megadó szám!a megadott feltételeket tartalmazó cellatartomány. A tartományban szerepel egy oszlopfelirat és alatta egy cellával a feltételt megadó címke"}, "DMIN": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON>z adatb<PERSON><PERSON><PERSON> adott feltételeknek eleget tevő rekordjaiból álló mező<PERSON> (oszlopban) lévő legkisebb számot adja eredményül.", "ad": "a listát vagy adatbázist alkotó cellatartomány. Az adatbázis egymással kapcsolatos adatokból álló lista.!vagy az oszlopfelirat idézőjelek között, vagy a listában az oszlop helyét megadó szám!a megadott feltételeket tartalmazó cellatartomány. A tartományban szerepel egy oszlopfelirat és alatta egy cellával a feltételt megadó címke."}, "DPRODUCT": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON>z adatb<PERSON><PERSON><PERSON> adott feltételeknek eleget tevő rekordjaiból álló mezőben (oszlopban) összeszorozza az értékeket", "ad": "a listát vagy adatbázist alkotó cellatartomány. Az adatbázis egymással kapcsolatos adatokból álló lista.!vagy az oszlopfelirat idézőjelek között, vagy a listában az oszlop helyét megadó szám!a megadott feltételeket tartalmazó cellatartomány. A tartományban szerepel egy oszlopfelirat és alatta egy cellával a feltételt megadó címke."}, "DSTDEV": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "Az adatbázis kiválasztott elemei mint minta alapján becslést ad a szórásra.", "ad": "a listát vagy adatbázist alkotó cellatartomány. Az adatbázis egymással kapcsolatos adatokból álló lista.!vagy az oszlopfelirat idézőjelek között, vagy a listában az oszlop helyét megadó szám!a megadott feltételeket tartalmazó cellatartomány. A tartományban szerepel egy oszlopfelirat és alatta egy cellával a feltételt megadó címke."}, "DSTDEVP": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON>z adatbá<PERSON>s szűrt rekordjainak megadott mezőjében található értékek (nem mint minta, hanem a teljes sokaság) alapján kiszámítja a szórást.", "ad": "a listát vagy adatbázist alkotó cellatartomány. Az adatbázis egymással kapcsolatos adatokból álló lista.!vagy az oszlopfelirat idézőjelek között, vagy a listában az oszlop helyét megadó szám!a megadott feltételeket tartalmazó cellatartomány. A tartományban szerepel egy oszlopfelirat és alatta egy cellával a feltételt megadó címke."}, "DSUM": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON>z adatb<PERSON><PERSON><PERSON> adott feltételeknek eleget tevő rekordjaiból álló mező<PERSON> (oszlopban) összeadja az értékeket.", "ad": "a listát vagy adatbázist alkotó cellatartomány. Az adatbázis egymással kapcsolatos adatokból álló lista.!vagy az oszlopfelirat idézőjelek között, vagy a listában az oszlop helyét megadó szám!a megadott feltételeket tartalmazó cellatartomány. A tartományban szerepel egy oszlopfelirat és alatta egy cellával a feltételt megadó címke."}, "DVAR": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "Az adatbázis szűrt rekordjainak megadott mezőjében található értékek mint minta alapján becslést ad a varianciára; eredményül e becsült értéket adja.", "ad": "a listát vagy adatbázist alkotó cellatartomány. Az adatbázis egymással kapcsolatos adatokból álló lista.!vagy az oszlopfelirat idézőjelek között, vagy a listában az oszlop helyét megadó szám!a megadott feltételeket tartalmazó cellatartomány. A tartományban szerepel egy oszlopfelirat és alatta egy cellával a feltételt megadó címke."}, "DVARP": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON>z adatbá<PERSON>s szűrt rekordjainak megadott mezőjében található értékek (nem mint minta, hanem a teljes sokaság) alapján kiszámítja a varianciát.", "ad": "a listát vagy adatbázist alkotó cellatartomány. Az adatbázis egymással kapcsolatos adatokból álló lista.!vagy az oszlopfelirat idézőjelek között, vagy a listában az oszlop helyét megadó szám!a megadott feltételeket tartalmazó cellatartomány. A tartományban szerepel egy oszlopfelirat és alatta egy cellával a feltételt megadó címke."}, "CHAR": {"a": "(szám)", "d": "A kódszám által meghatározott karaktert adja eredményül a számítógépen beállított karakterkészletből", "ad": "a kívánt karakter kódja; 1 és 255 közé eső egész s<PERSON>m"}, "CLEAN": {"a": "(szöveg)", "d": "A szövegből eltünteti az összes nem kinyomtatható karaktert.", "ad": "a munkalapon tal<PERSON><PERSON><PERSON><PERSON> bármely olyan információ, amelyet meg kell tisztítani a nem kinyomtatható karakterektől"}, "CODE": {"a": "(szöveg)", "d": "Egy szövegdarab első karakterének numerikus kódját adja eredményül a számítógép által használt karakterkészlet szerint", "ad": "e szöveg első karakterének kódját kívánja megkapni"}, "CONCATENATE": {"a": "(szöveg1; [szöveg2]; ...)", "d": "Több szövegdarabot egyetlen szöveggé fűz <PERSON>ze", "ad": "az a legalább 1, leg<PERSON><PERSON><PERSON><PERSON> 255 sz<PERSON>veg<PERSON><PERSON>, amelyet egyetlen szöveggé kell összefűzni; lehetnek szövegdarabok, számok vagy egy cellára való hivatkozások"}, "CONCAT": {"a": "(szöveg1; ...)", "d": "Összefűz egy szöveges karakterláncokból álló listát vagy tartományt", "ad": "az egyetlen szöveges karakterlánccá összefűzni kívánt 1–254 szöveges karakterlánc vagy tartomány"}, "DOLLAR": {"a": "(sz<PERSON>m; [tized<PERSON>k])", "d": "Egy számot pénznem formátumú szöveggé alak<PERSON>.", "ad": "egy sz<PERSON><PERSON>, egy számértéket eredményez<PERSON> képlet, vagy egy számértéket tartalmazó cellára mutató hivatkozás!a tizedesjegyek száma. A szám szükség szerint kerekítve lesz; ha elhagyjuk, a tizedesjegyek száma 2 lesz."}, "EXACT": {"a": "(szöveg1; szöveg2)", "d": "Két szövegrészt has<PERSON><PERSON>, az eredmény IGAZ vagy HAMIS. Az AZONOS függvény megkülönbözteti a kis- és nagybetűket", "ad": "az első szövegrész!a második szövegrész"}, "FIND": {"a": "(keres_szöveg; szöveg; [kezdet])", "d": "Megkeres egy szövegrészt egy m<PERSON>, eredményül a talált szövegrész kezdőpozíciójának számát adja, a kis- és nagybetűket megkülönbözteti", "ad": "a megkeresendő szövegrész. K<PERSON><PERSON> (üres szöveg) segítségével a szöveg első karaktere található meg; helyettesítő karakterek nem használhatók!az a szöveg<PERSON>sz, amelyben keresni kell!azt a karaktert adja meg, amelytől a keresést el kell kezdeni. A szöveg első karaktere az 1-es számú karakter. Ha elhagyjuk, a kezdet = 1"}, "FINDB": {"a": "(keres_szöveg; szöveg; [kezdet])", "d": "Egy karaktersorozatban egy másikat kere<PERSON>, és eredményül az első karakterlánc első karakterének helyét adja a második karakterlánc elejétől számítva, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai", "ad": "a megkeresendő szövegrész. K<PERSON><PERSON> (üres szöveg) segítségével a szöveg első karaktere található meg; helyettesítő karakterek nem használhatók!az a szöveg<PERSON>sz, amelyben keresni kell!azt a karaktert adja meg, amelytől a keresést el kell kezdeni. A szöveg első karaktere az 1-es számú karakter. Ha elhagyjuk, a kezdet = 1"}, "FIXED": {"a": "(szám; [tizedesek]; [nincs_pont])", "d": "Egy szá<PERSON> adott szá<PERSON>ú tizedesjegyre kerekít és szöveg formában adja vissza ezreselválasztó jelekkel vagy azok nélkül.", "ad": "az a <PERSON>zá<PERSON>, amely<PERSON> kerekítés után szöveggé kell átalakítani!a tizedesjegyek száma. Ha elhagyjuk, a tizedesjegyek száma 2 lesz.!logikai érték: IGAZ esetén a FIX függvény eredményében nem szerepelnek ezreseket elválasztó jelek; ha HAMIS vagy elhagyjuk, szerepelnek"}, "LEFT": {"a": "(szöveg; [h<PERSON><PERSON>_karakter])", "d": "Egy szövegrész elejétől megadott számú karaktert ad eredményül", "ad": "az a sz<PERSON><PERSON><PERSON><PERSON><PERSON>, amelyb<PERSON><PERSON> ki kell venni a karaktereket!azt határozza meg, hogy a BAL függvény hány karaktert adjon eredményül; elhagyása esetén értéke 1"}, "LEFTB": {"a": "(szöveg; [h<PERSON><PERSON>_karakter])", "d": "A szöveg első karaktereit adja vissza a megadott bájtszám alapján, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai", "ad": "az a sz<PERSON><PERSON><PERSON><PERSON><PERSON>, amelyb<PERSON><PERSON> ki kell venni a karaktereket!azt határozza meg, hogy a LEFTB függvény hány karaktert adjon eredményül; elhagyása esetén értéke 1"}, "LEN": {"a": "(szöveg)", "d": "Egy szöveg karakterekben mért hosszát adja eredményül.", "ad": "az a szöveg, amelynek a hosszát meg kell határozni. A szóköz is karakternek számít."}, "LENB": {"a": "(szöveg)", "d": "A szöveg karaktereinek ábrázolására hasz<PERSON>lt bájtok számát adja v<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai", "ad": "az a szöveg, amelynek a hosszát meg kell határozni. A szóköz is karakternek számít."}, "LOWER": {"a": "(szöveg)", "d": "Egy szövegrészben lévő összes betűt kisbetűvé alakít át", "ad": "a kisbetűssé átalakítandó szöveg. A szöveg azon karakterei, melyek nem betűk, nem változnak meg"}, "MID": {"a": "(szöveg; honnant<PERSON><PERSON>; h<PERSON>y_karakter)", "d": "Eredményként megadott számú karaktert ad egy szövegből a megadott sorszámú karaktertől kezdődően", "ad": "az a szövegdarab, amely a kívánt karaktereket tartalmazza!a szöveg e karakterétől kezdve kell adott számút kiolvasni. A szöveg első karaktere az 1-es számú karakter!a kívánt eredmény karaktereinek száma"}, "MIDB": {"a": "(szöveg; honnant<PERSON><PERSON>; h<PERSON>y_karakter)", "d": "A karakterek által elfoglalt bájtok alapján megadott pozíciójú karaktertől kezdve adott számú karaktert ad vissza szövegből, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai", "ad": "az a szövegdarab, amely a kívánt karaktereket tartalmazza!a szöveg e karakterétől kezdve kell adott számút kiolvasni. A szöveg első karaktere az 1-es számú karakter!a kívánt eredmény karaktereinek száma"}, "NUMBERVALUE": {"a": "(szöveg; [tizedes_elv]; [ezres_elv])", "d": "Szöveget konvertál számmá területi beállítástól független módon.", "ad": "a konvertálni kívánt számot képviselő karakterlánc!a karakterláncban tizedes elválasztóként használt karakter!a karakterláncban ezreselválasztóként használt karakter"}, "PROPER": {"a": "(szöveg)", "d": "Egy szövegrész minden szavának kezdőbetűjét nagybetűre, az összes többi betűt pedig kisbetűre cseréli", "ad": "idézőjelek közé z<PERSON> s<PERSON>öveg, szöveget eredményül adó képlet vagy szöveget tartalmazó cellára való hivat<PERSON>"}, "REPLACE": {"a": "(régi_szöveg; honna<PERSON><PERSON><PERSON>; h<PERSON><PERSON>_karakter; új_szöveg)", "d": "Szövegdarab megadott részét eltérő szövegdarabbal cseréli ki.", "ad": "az a szöveg, amelyben néhány karaktert ki kell cserélni!az a régi_szövegbeli karakterhely, amelytől kezdve a karaktereket az új_szövegre ki kell cserélni!a régi_szövegben kicserélendő karakterek száma!az a szövegdarab, amely a régi_szövegbeli karaktereket helyettesíteni fogja"}, "REPLACEB": {"a": "(régi_szöveg; honna<PERSON><PERSON><PERSON>; h<PERSON><PERSON>_karakter; új_szöveg)", "d": "<PERSON><PERSON> adott bájtszám alapján a szöveg adott részét másik karaktersorozatra cseréli, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai", "ad": "az a szöveg, amelyben néhány karaktert ki kell cserélni!az a régi_szövegbeli karakterhely, amelytől kezdve a karaktereket az új_szövegre ki kell cserélni!a régi_szövegben kicserélendő karakterek száma!az a szövegdarab, amely a régi_szövegbeli karaktereket helyettesíteni fogja"}, "REPT": {"a": "(szöveg; hányszor)", "d": "Megadott alkalommal megismétel egy szövegdarabot. A SOKSZOR függvény segítségével egy szövegdarab számos példányával tölthet fel egy cellát.", "ad": "a megismétlendő szövegdarab!az ismétlések számát megadó pozitív szám"}, "RIGHT": {"a": "(szöveg; [h<PERSON><PERSON>_karakter])", "d": "Egy szövegrész végétől megadott számú karaktert ad eredményül", "ad": "az a sz<PERSON><PERSON><PERSON><PERSON><PERSON>, amely<PERSON><PERSON><PERSON> ki kell venni a karaktereket!azt hat<PERSON><PERSON>zza meg, hogy hány karaktert adjon eredményül; elhagyása esetén értéke 1"}, "RIGHTB": {"a": "(szöveg; [h<PERSON><PERSON>_karakter])", "d": "Szöveg utolsó karaktereit adja vissza a megadott bájtszám alapján, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai", "ad": "az a sz<PERSON><PERSON><PERSON><PERSON><PERSON>, amely<PERSON><PERSON><PERSON> ki kell venni a karaktereket!azt hat<PERSON><PERSON>zza meg, hogy hány karaktert adjon eredményül; elhagyása esetén értéke 1"}, "SEARCH": {"a": "(keres_szöveg; szöveg; [kezdet])", "d": "Azt a karaktersorszámot adja meg, ahol egy adott karakter vagy szövegdarab először fordul el<PERSON> balról jobbra haladva, a kis- és nagybetűket azonosnak tekintve.", "ad": "a megkeresendő szövegdarab. Használhatja a ? vagy * joker karaktereket; a ? és a * kereséséhez használja a ~? és a ~* kombinációkat!az a szöveg, amelyben a keres_szöveget meg kell keresni!a szövegnek az a balról számított karakterhelye, amelytől a keresést el kell kezdeni. Ha elhagyjuk, 1 lesz"}, "SEARCHB": {"a": "(keres_szöveg; szöveg; [kezdet])", "d": "Egy szöveges karakterláncot keres egy második karakterláncban, és visszaadja az első karakterlánc kezdő pozíciójának számát a második karakterlánc első karakterében, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai", "ad": "a megkeresendő szövegdarab. Használhatja a ? vagy * joker karaktereket; a ? és a * kereséséhez használja a ~? és a ~* kombinációkat!az a szöveg, amelyben a keres_szöveget meg kell keresni!a szövegnek az a balról számított karakterhelye, amelytől a keresést el kell kezdeni. Ha elhagyjuk, 1 lesz"}, "SUBSTITUTE": {"a": "(szöveg; régi_szöveg; új_szöveg; [melyiket])", "d": "Egy szövegdarabban a régi_szöveg előfordulásait az új_szövegre cseréli ki.", "ad": "az a szöveg vagy arra a szöveget tartalmazó cellára való hivatkozás, amelyben a karaktereket ki kell cserélni!a kicserélendő karaktersorozat. Ha a régi szövegben a kis- és nagybetűk nem felelnek meg az új szövegnek, a függvény nem végzi el a cserét.!az a szövegdarab, amelyre a régi_szöveget ki kell cserélni!a régi_szöveg azon előfordulásának a sorszámát adja meg, amelyet ki kell cserélni. Ha elhagyjuk, a régi_szöveg minden példánya cserélve lesz."}, "T": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték szöveg-e, é<PERSON> ha igen, az eredmény a szöveg; ha nem szöveg, az eredmény kettős idézőjel (üres szöveg)", "ad": "a vizsgálandó érték"}, "TEXT": {"a": "(érték; format_text)", "d": "Értéket konvertál egy adott számformátumú szöveggé", "ad": "az a szá<PERSON>, egy számértéket tartalma<PERSON><PERSON> k<PERSON>, vagy egy számértéket tartalmazó cellára mutató hivatkozás!a Cellák formázása párbeszédpanel szám mezőjében lévő Kategória mező egyik számformátuma a Kategóriák mezőben"}, "TEXTJOIN": {"a": "(elválasztó; üreset_mellőz; szöveg1; ...)", "d": "Elválasztó karakterrel összefűz egy karakterláncokból álló listát vagy tartományt", "ad": "A szövegelemek közé beszúrandó elválasztó karakter vagy sztring!ha az argumentum értéke IGAZ (alapértelmezés), a függvény figyelmen kívül hagyja az üres cellákat!az összefűzni kívánt 1–252 szöveges sztring vagy tartomány"}, "TRIM": {"a": "(szöveg)", "d": "Egy szövegből eltávolítja az összes szóközt a szavak közti egyszeres szóközök kivételével.", "ad": "az a szöveg, amelyből a szóközöket el kell távolítani"}, "UNICHAR": {"a": "(szám)", "d": "Az adott numerikus érték által hivatkozott Unicode-karaktert adja eredményül.", "ad": "a karaktert képviselő Unicode-szám"}, "UNICODE": {"a": "(szöveg)", "d": "A szöveg első karakterének megfelelő számot (kódpontot) adja eredményül.", "ad": "az a <PERSON>m, amelynek az Unicode-értékét keresi"}, "UPPER": {"a": "(szöveg)", "d": "Szövegrészt nagybetűssé alakít át", "ad": "a nagybetűssé átalakítandó szöveg; lehet hivat<PERSON> vagy szövegrész"}, "VALUE": {"a": "(szöveg)", "d": "S<PERSON><PERSON><PERSON> szöveget számmá alakít á<PERSON>.", "ad": "az átalakí<PERSON><PERSON><PERSON> s<PERSON>, idézőjelek kö<PERSON><PERSON>, ill. az átalakítandó szöveget tartalmazó cellára való hivat<PERSON>"}, "AVEDEV": {"a": "(szám1; [szám2]; ...)", "d": "Az adatpontoknak átlaguktól való átlagos abszolút eltérését számítja ki. Az argumentumok számok vagy számokat tartalmazó nevek, tömbök vagy hivatkozások lehetnek.", "ad": "ezek azok az adatpontok, amelyeknek átlagos abszolút eltérését ki kell számítani; számuk 1 és 255 között lehet"}, "AVERAGE": {"a": "(szám1; [szám2]; ...)", "d": "Az argumentumok átlagát (számtani Középértékét) számolja ki Az argumentumok nevek, tömbök vagy számokat tartalmazó hivatkozások lehetnek.", "ad": "<PERSON><PERSON>k azok az argumentumok (legfeljebb 255), <PERSON><PERSON><PERSON> ki kell <PERSON>"}, "AVERAGEA": {"a": "(érték1; [érték2]; ...)", "d": "Arg<PERSON><PERSON><PERSON><PERSON><PERSON> (számtani közepét) adja meg, a szöveget és a HAMIS értéket 0-nak veszi; az IGAZ értéket 1-nek. Az argumentumok számok, neve<PERSON>, tömbök vagy hivatkozások lehetnek.", "ad": "azon argument<PERSON>ok (számuk 1 és 255 között lehet), <PERSON><PERSON><PERSON> ki kell s<PERSON><PERSON>"}, "AVERAGEIF": {"a": "(tartomány; kritérium; [átlag_tartomány])", "d": "<PERSON><PERSON> <PERSON>ott feltétel vagy kritérium által meghatározott cellák <PERSON> (számtani közepét) számítja ki", "ad": "a kiértékelendő cellatartomány!az átlagolandó cellákat meghatározó feltétel vagy kritérium egy szám, kifejezés vagy szöveg formájában!maguk a cellák, amelyeknek az átlagát ki kell s<PERSON>ámítani. Ha ez az adat nincs megadva, a számítás a tartomány által meghatározott cellákat használja"}, "AVERAGEIFS": {"a": "(átlag_tartomány; kritériumtartomány; kritérium; ...)", "d": "<PERSON><PERSON> <PERSON><PERSON> felt<PERSON>- vagy kritériumkészlet által meghatározott cellák <PERSON> (számtani közepét) számítja ki", "ad": "maguk a cellák, amelyeknek az átlagát ki kell számítani.!az adott feltétellel kiértékelni kívánt cellák tartománya!a feltétel vagy kritérium egy az átlagolandó cellákat definiáló s<PERSON>, kifejezés vagy szöveg formájában"}, "BETADIST": {"a": "(x; alfa; béta; [A]; [B])", "d": "A bétaeloszlás sűrűségfüggvényének értékét számítja ki.", "ad": "az az A és B közé eső érték, amely<PERSON> a függvény értékét ki kell szám<PERSON>tani!az eloszlás paramétere, nullán<PERSON>l nagyobbnak kell lennie!az eloszl<PERSON> paramétere, nullán<PERSON>l nagyobbnak kell lennie!az x-ek intervallumának alsó határa; nem kötelező megadni. Ha elhagyjuk, A = 0.!az x-ek intervallumának felső határa; nem kötelező megadni. Ha elhagyjuk, B = 1."}, "BETAINV": {"a": "(valószínűség; alfa; béta; [A]; [B])", "d": "A bétaeloszlás sűrűségfüggvény (BÉTA.ELOSZLÁS) inverzét számítja ki.", "ad": "a bétaeloszláshoz tartozó valószínűségérték!az eloszlás paramétere, nullán<PERSON>l nagyobbnak kell lennie!az eloszlás paramétere, nullán<PERSON>l nagyobbnak kell lennie!az x-ek intervallumának alsó határa; nem kötelező megadni. Ha elhagyjuk, A = 0.!az x-ek intervallumának felső határa; nem kötelező megadni. Ha elhagyjuk, B = 1."}, "BETA.DIST": {"a": "(x; alfa; b<PERSON>ta; eloszlásfv; [A]; [B])", "d": "A béta valószínűségi eloszlás értékét számítja ki", "ad": "az az A és B közötti érték, am<PERSON><PERSON><PERSON><PERSON> a függvény értékét ki kell szám<PERSON>tani!az eloszlás paramétere, melynek nullánál nagyobbnak kell lennie! az eloszlás paramétere, melynek nullánál nagyobbnak kell lennie!a függvény fajtáját megadó logikai érték: ha IGAZ, akkor az eloszlásfüggvény értékét számítja ki; ha HAMIS, a sűrűségfüggvényét!az x-ek intervallumának alsó határa; nem kötelező megadni. Ha elhagyjuk, A = 0.!az x-ek intervallumának felső határa; nem kötelező megadni. Ha elhagyjuk, B = 1"}, "BETA.INV": {"a": "(valószínűség; alfa; béta; [A]; [B])", "d": "A bétaeloszlás sűrűségfüggvénye (BÉTA.ELOSZL) inverzét számítja ki.", "ad": "a bétaeloszláshoz tartozó valószínűségérték!az eloszlás paramétere, nullán<PERSON>l nagyobbnak kell lennie!az eloszlás paramétere, nullán<PERSON>l nagyobbnak kell lennie!az x-ek intervallumának alsó határa; nem kötelező megadni. Ha elhagyjuk, A = 0!az x-ek intervallumának felső határa; nem kötelező megadni. Ha elhagyjuk, B = 1"}, "BINOMDIST": {"a": "(sikeresek; kísérletek; siker_valószínűsége; eloszlásfv)", "d": "A diszkrét binomiális eloszlás valószínűségértékét számítja ki.", "ad": "a sikeres kísérletek száma!a független kísérletek száma!a siker valószínűsége az egyes kísérletek esetén!a függvény fajtáját megadó logikai érték: ha IGAZ, akkor az eloszlásfüggvény értékét számítja ki; ha HAMIS, a tömegfüggvényét."}, "BINOM.DIST": {"a": "(sikeresek; kísérletek; siker_valószínűsége; eloszlásfv)", "d": "A diszkrét binomiális eloszlás valószínűségértékét számítja ki.", "ad": "a sikeres kísérletek száma!a független kísérletek száma!a siker valószínűsége az egyes kísérletek esetén!a függvény fajtáját megadó logikai érték: ha IGAZ, akkor a BINOM.ELOSZLÁS függvény az eloszlásfüggvény értékét számítja ki; ha HAMIS, a sűrűségfüggvényét."}, "BINOM.DIST.RANGE": {"a": "(kísérletek; siker_valószínűsége; sikeresek; [sikeresek_2])", "d": "<PERSON><PERSON> adott kimenetel valószínűségét számítja ki binomiális eloszlás esetén.", "ad": "a független kísérletek száma!a siker valószínűsége az egyes kísérletek esetén!a sikeres kísérletek száma!ha meg van adva, a függvény annak a valószínűségét számítja ki, hogy a sikeres kísérletek száma a sikeresek és a sikeresek_2 érték közé esik"}, "BINOM.INV": {"a": "(kísérletek; siker_valószínűsége; alfa)", "d": "Azt a legkisebb számot adja er<PERSON>, amely<PERSON> a binomiális eloszlásfüggvény értéke nem kisebb egy adott határértéknél.", "ad": "a Bernoulli-kísérletek száma!a siker valószínűsége az egyes kísérletek esetén; 0 és 1 közti szám, a végpontokat is beleértve!a határérték; 0 és 1 közti szám, a végpontokat is beleértve"}, "CHIDIST": {"a": "(x; s<PERSON><PERSON><PERSON>ág<PERSON><PERSON>)", "d": "A khi-négyzet eloszlás jobbszélű valószínűségértékét számítja ki.", "ad": "az az <PERSON><PERSON><PERSON><PERSON> (nem negatív szám), <PERSON><PERSON><PERSON><PERSON><PERSON> az el<PERSON>zl<PERSON>t ki kell szá<PERSON>ítani!a szabadságfokok száma, 1 és 10^10 közötti szám a 10^10 kivételével"}, "CHIINV": {"a": "(valószínűség; szabadságfok)", "d": "A khi-négyzet eloszlás jobbszélű inverzét számítja ki.", "ad": "a khi-négyzet eloszláshoz tartozó valószínűségérték; 0 és 1 közti érték a végpontokat is beleértve!a szabadságfokok száma, 1 és 10^10 közötti szám a 10^10 kivételével"}, "CHITEST": {"a": "(tényleges_tartomány; várható_tartomány)", "d": "Függetlenségvizsgálatot hajt végre, eredményül a khi-négyzet eloszláshoz rendelt értéket adja a statisztika és a szabadságfokok megfelelő száma szerint.", "ad": "az az adattartomány, amely a várható értékekkel összehasonlítandó megfigyelt adatokat tartalmazza!az az adattartomány, amely a sorösszegek és oszlopösszegek szorzatának a teljes összeghez viszonyított arányát tartalmazza"}, "CHISQ.DIST": {"a": "(x; s<PERSON><PERSON>ságfok; eloszlásfv)", "d": "A khi-négyzet eloszlás balszélű valószínűségértékét számítja ki", "ad": "az az <PERSON><PERSON><PERSON><PERSON> (nem negatív szám), <PERSON><PERSON><PERSON><PERSON><PERSON> az eloszlást ki kell szám<PERSON>!a szabadságfokok száma, 1 és 10^10 közötti szám a 10^10 kivételével!a függvény által visszaadott logikai érték: ha IGAZ, az eloszlásfüggvény értékét számítja ki, ha HAMIS, a sűrűségfüggvényét"}, "CHISQ.DIST.RT": {"a": "(x; s<PERSON><PERSON><PERSON>ág<PERSON><PERSON>)", "d": "A khi-négyzet eloszlás jobbszélű valószínűségértékét számítja ki", "ad": "az az <PERSON><PERSON><PERSON><PERSON> (nem negatív szám), <PERSON><PERSON><PERSON><PERSON><PERSON> az el<PERSON>zl<PERSON>t ki kell szá<PERSON>ítani!a szabadságfokok száma, 1 és 10^10 közötti szám a 10^10 kivételével"}, "CHISQ.INV": {"a": "(valószínűség; szabadságfok)", "d": "A khi-négyzet eloszlás balszélű inverzét számítja ki.", "ad": "a khi-négyzet eloszláshoz tartozó valószínűségérték; 0 és 1 közti érték, a végpontokat is beleértve!a szabadságfokok száma, 1 és 10^10 közötti szám a 10^10 kivételével"}, "CHISQ.INV.RT": {"a": "(valószínűség; szabadságfok)", "d": "A khi-négyzet eloszlás jobbszélű inverzét számítja ki.", "ad": "a khi-négyzet eloszláshoz tartozó valószínűségérték; 0 és 1 közti érték, a végpontokat is beleértve!a szabadságfokok száma, 1 és 10^10 közötti szám a 10^10 kivételével"}, "CHISQ.TEST": {"a": "(tényleges_tartomány; várható_tartomány)", "d": "Függetlenségvizsgálatot hajt végre, eredményül a khi-négyzet eloszláshoz rendelt értéket adja a statisztika és a szabadságfokok megfelelő száma szerint.", "ad": "az az adattartomány, amely a várható értékekkel összehasonlítandó megfigyelt adatokat tartalmazza!az az adattartomány, amely a sorösszegek és oszlopösszegek szorzatának a teljes összeghez viszonyított arányát tartalmazza"}, "CONFIDENCE": {"a": "(alfa; szórás; méret)", "d": "Egy statisztikai sokaság várható értékének megbízhatósági intervallumát adja eredmé<PERSON>ül norm<PERSON>l el<PERSON><PERSON>", "ad": "a megbízhatósági szint kiszámításához használt pontossági szint, 0-nál nagyobb és 1-nél kisebb szám!a sokaságnak az adattartományon vett szórása; felt<PERSON><PERSON><PERSON><PERSON>ük, hogy ismert. A szórásnak nullánál nagyobbnak kell lennie!a minta mérete"}, "CONFIDENCE.NORM": {"a": "(alfa; szórás; méret)", "d": "Egy statisztikai sokaság várható értékének megbízhatósági intervallumát adja eredményül a normális el<PERSON><PERSON>", "ad": "a megbízhatósági szint kiszámításához használt pontossági szint, 0-nál nagyobb és 1-nél kisebb szám!a sokaságnak az adattartományon vett szórása; felt<PERSON><PERSON><PERSON><PERSON>ük, hogy ismert. A szórásnak nullánál nagyobbnak kell lennie!a minta mérete"}, "CONFIDENCE.T": {"a": "(alfa; szórás; méret)", "d": "Egy statisztikai sokaság várható értékének megbízhatósági intervallumát adja eredményül a Student-féle t-próba <PERSON>", "ad": "a megbízhatósági szint kiszámításához használt pontossági szint, 0-nál nagyobb és 1-nél kisebb szám!a sokaságnak az adattartományon vett szórása; felt<PERSON><PERSON><PERSON><PERSON>ük, hogy ismert. A szórásnak nullánál nagyobbnak kell lennie!a minta mérete"}, "CORREL": {"a": "(tömb1; tömb2)", "d": "<PERSON><PERSON><PERSON> ad<PERSON><PERSON><PERSON> e<PERSON>ütthatóját számítja ki.", "ad": "az egyik értékhalmaz cellatartománya. Az értékek számok, neve<PERSON>, tömbök vagy számokat tartalmazó hivatkozások lehetnek!a másik értékhalmaz cellatartománya. Az értékek számok, nevek, tömbök vagy számokat tartalmazó hivatkozások lehetnek"}, "COUNT": {"a": "(érték1; [érték2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy h<PERSON>y olyan cella van egy tart<PERSON>, amely s<PERSON><PERSON><PERSON> tarta<PERSON>.", "ad": "leg<PERSON><PERSON><PERSON>bb 255 argumentum, amely több<PERSON><PERSON><PERSON> tí<PERSON> adatot tartalmazhat vagy jelölhet meg, a program azonban csak a számokat veszi figyelembe"}, "COUNTA": {"a": "(érték1; [érték2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy hány nem üres cella található egy tartományban.", "ad": "argumentumok (számuk 1 és 255 között lehet), am<PERSON><PERSON> az összeszámolni kívánt értékeket és cellákat jelzik; az érték bármilyen típusú információ lehet."}, "COUNTBLANK": {"a": "(tartomány)", "d": "Kijelölt cellatartományban megszámlálja az üres cellákat", "ad": "az a tartomány, amelyben az üres cellákat meg kell számlálni"}, "COUNTIF": {"a": "(tartomány; kritérium)", "d": "Egy tartományban összeszámolja azokat a nem üres cellákat, amelyek eleget tesznek a megadott feltételeknek.", "ad": "az a cellatartomány, amelyben a nem üres cellákat meg kell számolni!az összeszámolandó cellákat meghatározó s<PERSON>, kifejezésként vagy szövegként megadott feltétel"}, "COUNTIFS": {"a": "(kritériumtartomány; kritérium; ...)", "d": "<PERSON><PERSON> adott <PERSON>- vagy kritériumkészlet által meghatározott cellatartomány celláinak számát állapítja meg", "ad": "az adott feltétellel kiértékelni kívánt cellák tartománya!a feltétel egy a megszámolandó cellákat definiáló s<PERSON>m, kifejezés vagy szöveg formájában"}, "COVAR": {"a": "(tömb1; tömb2)", "d": "A kova<PERSON>, a<PERSON>z két adathal<PERSON>z minden egyes adatpontpárja esetén vett eltérések szorzatának átlagát számítja ki.", "ad": "egész számokat tartalmazó első cellatartomány; elemei csak sz<PERSON>mok, tömbök vagy számokat tartalmazó hivatkozások lehetnek!egész számokat tartalmazó második cellatartomány; elemei csak sz<PERSON>mok, tömbök vagy számokat tartalmazó hivatkozások lehetnek"}, "COVARIANCE.P": {"a": "(tömb1; tömb2)", "d": "A sokaság kova<PERSON>, azaz a két adathalmaz minden egyes adatpontpárja esetén vett eltérések szorzatának átlagát számítja ki", "ad": "egész számokat tartalmaz<PERSON> el<PERSON>ő cellatartomány; el<PERSON><PERSON>, tömb<PERSON>k vagy számokat tartalmazó hivatkoz<PERSON>ok kell, hogy legyenek!egész számokat tartalmazó második cellatartomány; el<PERSON><PERSON>, tömbök vagy számokat tartalmazó hivatkozások kell, hogy legyenek"}, "COVARIANCE.S": {"a": "(tömb1; tömb2)", "d": "A minta kovarianciáját, azaz a két adathalmaz minden egyes adatpontpárja esetén vett eltérések szorzatának átlagát számítja ki", "ad": "egész számokat tartalmaz<PERSON> el<PERSON>ő cellatartomány; el<PERSON><PERSON>, tömb<PERSON>k vagy számokat tartalmazó hivatkoz<PERSON>ok kell, hogy legyenek!egész számokat tartalmazó második cellatartomány; el<PERSON><PERSON>, tömbök vagy számokat tartalmazó hivatkozások kell, hogy legyenek"}, "CRITBINOM": {"a": "(kísérletek; siker_valószínűsége; alfa)", "d": "Azt a legkisebb számot adja er<PERSON>, amely<PERSON> a binomiális eloszlásfüggvény értéke nem kisebb egy adott határértéknél.", "ad": "a Bernoulli-kísérletek száma!a siker valószínűsége az egyes kísérletek esetén; 0 és 1 közti szám a végpontokat is beleértve!a határérték; 0 és 1 közti szám a végpontokat is beleértve"}, "DEVSQ": {"a": "(szám1; [szám2]; ...)", "d": "Az egyes adatpontok középértéktől való eltérésnégyzeteinek összegét adja eredményül", "ad": " argument<PERSON><PERSON> (legalább 1 és legfeljebb 255 darab), ill. egy tömb vagy tö<PERSON><PERSON><PERSON>, amely<PERSON> a számítást el kell végezni."}, "EXPONDIST": {"a": "(x; lambda; eloszlásfv)", "d": "Az exponenciális eloszlás értékét számítja ki", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> az eloszl<PERSON>t ki kell <PERSON>, nem negatív szám!az eloszlás paramétere, pozitív szám!a függvény fajtáját megadó logikai érték: ha IGAZ, az eloszlásfüggvény értékét számítja ki, ha HAMIS, a sűrűségfüggvényét"}, "EXPON.DIST": {"a": "(x; lambda; eloszlásfv)", "d": "Az exponenciális eloszlás értékét számítja ki", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> az eloszl<PERSON>t ki kell <PERSON>, nem negatív szám!az eloszlás paramétere, pozitív szám!a függvény fajtáját megadó logikai érték: ha IGAZ, az eloszlásfüggvény értékét számítja ki, ha HAMIS, a sűrűségfüggvényét"}, "FDIST": {"a": "(x; szabadságfok1; szabadságfok2)", "d": "Az F-elos<PERSON>lás (jobbszélű) értékét (az eltérés fokát) számítja ki két adathalmazra.", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> a függv<PERSON>y értékét ki kell sz<PERSON>, nem negatív szám!a számláló szabadságfoka, 1 és 10^10 közötti szám a 10^10 kivételével!a nevező szabadságfoka, 1 és 10^10 közötti szám a 10^10 kivételével"}, "FINV": {"a": "(valószínűség; szabadságfok1; szabadságfok2)", "d": "Az F-elos<PERSON><PERSON><PERSON> (jobbszélű) értékét számítja ki: ha p = F.ELOSZLÁS(x,...), akkor INVERZ.F(p,...) = x", "ad": "az F-eloszláshoz tartozó valószínűségérték; 0 és 1 közti szám a végpontokat is beleértve!a számláló szabadságfoka, 1 és 10^10 közötti szám a 10^10 kivételével!a nevező szabadságfoka, 1 és 10^10 közötti szám a 10^10 kivételével"}, "FTEST": {"a": "(tömb1; tömb2)", "d": "Az F-próba értékét adja eredményül (annak a kétszélű valószínűségét, hogy a két tömb varianciája nem tér el szignifikánsan)", "ad": "az első adattömb vagy adattartomány; elemei számok vagy számokat tartalmazó nevek, tömbök vagy hivatkozások lehetnek (az üres cellák nem számítanak)!a második adattömb vagy adattartomány; elemei számok vagy számokat tartalmazó nevek, tömbök vagy hivatkozások lehetnek (az üres cellák nem számítanak)"}, "F.DIST": {"a": "(x; szabadságfok1; szabadságfok2; eloszlásfv)", "d": "A balszélű F-eloszlás é<PERSON> (az eltérés fokát) számítja ki két adathalmazra.", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> a függv<PERSON>y értékét ki kell sz<PERSON>, nem negatív szám!a számlá<PERSON>ó szabadságfoka, 1 és 10^10 közötti szám a 10^10 kivételével!a nevező szabadságfoka, 1 és 10^10 közötti szám a 10^10 kivételével!a függvény fajtáját megadó logikai érték: ha IGAZ, az eloszlásfüggvény értékét számítja ki, ha HAMIS, a sűrűségfüggvényét"}, "F.DIST.RT": {"a": "(x; szabadságfok1; szabadságfok2)", "d": "A jobbszélű F-eloszlás <PERSON> (az eltérés fokát) számítja ki két adathalmazra.", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> a függv<PERSON>y értékét ki kell sz<PERSON>, nem negatív szám!a számláló szabadságfoka, 1 és 10^10 közötti szám a 10^10 kivételével!a nevező szabadságfoka, 1 és 10^10 közötti szám a 10^10 kivételével"}, "F.INV": {"a": "(valószínűség; szabadságfok1; szabadságfok2)", "d": "A balszélű F-eloszlás inverzének értékét számítja ki: ha p = F.ELOSZL(x,...), akkor F.INVERZ(p,...) = x", "ad": "az F-eloszláshoz tartozó valószínűségérték; 0 és 1 közti szám, a végpontokat is beleértve!a számláló szabadságfoka, 1 és 10^10 közötti szám a 10^10 kivételével!a nevező szabadságfoka, 1 és 10^10 közötti szám a 10^10 kivételével"}, "F.INV.RT": {"a": "(valószínűség; szabadságfok1; szabadságfok2)", "d": "A jobbszélű F-eloszlás inverzének értékét számítja ki: ha p = F.ELOSZLÁS.JOBB(x,...), akkor F.INVERZ.JOBB(p,...) = x", "ad": "az F-eloszláshoz tartozó valószínűségérték; 0 és 1 közti szám, a végpontokat is beleértve!a számláló szabadságfoka, 1 és 10^10 közötti szám a 10^10 kivételével!a nevező szabadságfoka, 1 és 10^10 közötti szám a 10^10 kivételével"}, "F.TEST": {"a": "(tömb1; tömb2)", "d": "Az F-próba értékét adja eredményül (annak a kétszélű valószínűségét, hogy a két tömb varianciája nem tér el szignifikánsan)", "ad": "az első adattömb vagy adattartomány; elemei számok vagy számokat tartalmazó nevek, tömbök vagy hivatkozások lehetnek (az üres cellák nem számítanak)!a második adattömb vagy adattartomány; elemei számok vagy számokat tartalmazó nevek, tömbök vagy hivatkozások lehetnek (az üres cellák nem számítanak)"}, "FISHER": {"a": "(x)", "d": "Fisher-transzformációt hajt végre", "ad": "az a sz<PERSON><PERSON><PERSON><PERSON>, amely<PERSON> a transzformációt végre kell ha<PERSON>, -1 és 1 közötti szám a -1 és 1 kivételével"}, "FISHERINV": {"a": "(y)", "d": "A Fisher-transzformáció inverzét hajtja végre: ha y = FISHER(x), akkor INVERZ.FISHER(y) = x", "ad": "az az ért<PERSON>k, amelyre a transzformáció inverzét végre kell hajtani"}, "FORECAST": {"a": "(x; ismert_y-ok; ismert_x-ek)", "d": "<PERSON>z ismert értékek alapján lineáris regress<PERSON><PERSON> segítségével jövőbeli értéket számít ki vagy becsül meg.", "ad": "az az adat, amely<PERSON> előrejelzést kíván kapni; számértéknek kell lennie!a függő számértékeket tartalmazó tömb vagy adattartomány!a független számértékeket tartalmazó tömb vagy tartomány. Az ismert_x varianciája nem lehet nulla"}, "FORECAST.ETS": {"a": "(cél_d<PERSON><PERSON>; ért<PERSON>kek; id<PERSON>sor; [szezonalitás]; [adatkiegészítés]; [aggregáció])", "d": "Ez a függvény a megadott jövőbeli céldátumra vonatkozó előre jelzett értéket adja vissza exponenciális simítás alkalma<PERSON>.", "ad": "az az adat<PERSON>, amely<PERSON> az Spreadsheet Editor előre jelzi az értéket. Az idősor értékeinek formátumát kell követnie.!az előrejelzés alapjául szolgáló számadatok tömbje vagy tartománya.!számadatok független tömbje vagy tartománya. Az idősor dátumai között konzisztens, nem nulla értékű távolság kell legyen.!opcion<PERSON>lis számérték, amely a szezonális minta hosszát jelöli. Az alapértelmezett 1-es érték azt jelenti, hogy a szezonalitás észlelése automatikus.!opcionális érték a hiányzó értékek kezelésére. Az alapértelmezett 1-es érték a hiányzó értékeket interpolálással pótolja, a 0 érték nullákkal helyettesíti őket.!opcion<PERSON><PERSON> s<PERSON>, amely az azonos időbélyeggel rendelkező értékeket összesíti. Ha ü<PERSON>, az Spreadsheet Editor átlagolja az értékeket."}, "FORECAST.ETS.CONFINT": {"a": "(cél_d<PERSON><PERSON>; ért<PERSON>kek; id<PERSON>sor; [konfidenciaszint]; [szezonalitás]; [adatkiegészítés]; [aggreg<PERSON><PERSON>ó])", "d": "Ez a függvény a megadott céldátumra előre jelzett érték konfidencia-intervallumát adja v<PERSON>.", "ad": "az az adatpont, amely<PERSON> az Spreadsheet Editor előre jelzi az értéket. Az idősor értékeinek formátumát kell követnie.!az előrejelzés alapjául szolgáló számadatok tömbje vagy tartománya.!számadatok független tömbje vagy tartománya. Az idősor dátumai között konzisztens, nem nulla értékű távolság kell legyen.!egy 0 és 1 közé eső szám, amely a számított konfidencia-intervallum konfidenciaszintjét jelöli. Az alapértelmezett érték 0,95.!opcion<PERSON><PERSON> számérték, amely a szezonális minta hosszát jelöli. Az alapértelmezett 1-es érték azt jelenti, hogy a szezonalitás észlelése automatikus.!opcionális érték a hiányzó értékek kezelésére. Az alapértelmezett 1-es érték a hiányzó értékeket interpolálással pótolja, a 0 érték nullákkal helyettesíti őket.!opcionális számérték, amely az azonos időbélyeggel rendelkező értékeket összesíti. Ha üres, az Spreadsheet Editor átlagolja az értékeket."}, "FORECAST.ETS.SEASONALITY": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; id<PERSON><PERSON>; [adatkiegészítés]; [aggregáció])", "d": "Ez a függvény a megadott idősorban az alkalmazás által észlelt ismétlődő minta hosszát adja vissza.", "ad": "az előrejelzés alapjául szolgáló számadatok tömbje vagy tartománya.!számadatok független tömbje vagy tartománya. Az idősor dátumai között konzisztens, nem nulla értékű távolság kell legyen.!opcionális érték a hiányzó értékek kezelésére. Az alapértelmezett 1-es érték a hiányzó értékeket interpolálással pótolja, a 0 érték nullákkal helyettesíti őket.!opcionális számérték, amely az azonos időbélyeggel rendelkező értékeket összesíti. Ha ü<PERSON>, az Spreadsheet Editor átlagolja az értékeket."}, "FORECAST.ETS.STAT": {"a": "(<PERSON>rt<PERSON><PERSON>k; id<PERSON>sor; statisztika_típusa; [szezonalitás]; [adatkiegészítés]; [aggregáció])", "d": "Ez a függvény az előrejelzés megadott statisztikai adatait adja vissza.", "ad": "az előrejelzés alapjául szolgáló számadatok tömbje vagy tartománya.!a számadatok független tömbje vagy tartománya. Az idősor dátumai között konzisztens, nem nulla értékű távolság kell legyen.!egy 1 és 8 közé eső szám, amely j<PERSON>, hogy a Spreadsheet Editor mely statisztikai adatokat adja vissza a kiszámított előrejelzéshez.!opcion<PERSON>lis sz<PERSON>rt<PERSON>, amely a szezonális minta hosszát jelöli. Az alapértelmezett 1-es érték azt jelenti, hogy a szezonalitás észlelése automatikus.!opcionális érték a hiányzó értékek kezelésére. Az alapértelmezett 1-es érték a hiányzó értékeket interpolálással pótolja, a 0 érték nullákkal helyettesíti őket.!opcion<PERSON><PERSON>, amely az azonos időbélyeggel rendelkező értékeket összesíti. Ha üres, az Spreadsheet Editor átlagolja az értékeket."}, "FORECAST.LINEAR": {"a": "(x; ismert_y-ok; ismert_x-ek)", "d": "<PERSON>z ismert értékek alapján lineáris regress<PERSON><PERSON> segítségével jövőbeli értéket számít ki vagy becsül meg.", "ad": "az az adat, amely<PERSON> előrejelzést kíván kapni; számértéknek kell lennie!a függő számértékeket tartalmazó tömb vagy adattartomány!a független számértékeket tartalmazó tömb vagy tartomány. Az ismert_x varianciája nem lehet nulla"}, "FREQUENCY": {"a": "(adattömb; csoport_tömb)", "d": "A gyakorisági vagy empirikus eloszlás értékét (milyen gyakran fordulnak elő az értékek egy értéktartományban) a csoport_tömbnél eggyel több elemet tartalmazó függőleges tömbként adja eredményül.", "ad": "azon adatokat tartalmaz<PERSON> tömb ill. azon adatokra való hivat<PERSON>, amelyek gyakorisági eloszlását meg kell határozni (az üres és a szöveges cellák nem lesznek figyelembevéve)!azon intervallumokat tartalmazó tömb ill. azon intervallumokra való hivat<PERSON>, amelyekbe az adattömbbeli értékeket csoportosítani kell"}, "GAMMA": {"a": "(x)", "d": "A Gamma-függvény értékét számítja ki.", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, amelyhez ki szeretné s<PERSON>ámítani a Gamma-értéket"}, "GAMMADIST": {"a": "(x; alfa; b<PERSON>ta; eloszlásfv)", "d": "A gammaeloszlás értékét számítja ki", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> az eloszl<PERSON>t ki kell s<PERSON>, nem negatív szám!az eloszlás paramétere, pozitív szám!az eloszlás paramétere, pozitív szám. Ha béta = 1, a GAMMA.ELOSZLÁS a standard gammaeloszlás értékét adja eredményül!a függvény fajtáját megadó logikai érték; ha IGAZ, a GAMMA.ELOSZLÁS az eloszlásfüggvény értékét számítja ki; ha HAMIS vagy <PERSON>, a tömegfüggvényét"}, "GAMMA.DIST": {"a": "(x; alfa; b<PERSON>ta; eloszlásfv)", "d": "A gammaeloszlás értékét számítja ki", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> az eloszl<PERSON>t ki kell s<PERSON>, nem negatív szám!az eloszlás paramétere, pozitív szám!az eloszlás paramétere, pozitív szám. Ha béta = 1, a GAMMA.ELOSZL a standard gammaeloszlás értékét adja eredményül!a függvény fajtáját megadó logikai érték; ha IGAZ, a GAMMA.ELOSZL az eloszlásfüggvény értékét számítja ki; ha HAMIS vagy <PERSON>, a sűrűségfüggvényét"}, "GAMMAINV": {"a": "(valószínűség; alfa; béta)", "d": "A gammaeloszlás eloszlásfüggvénye inverzének értékét számítja ki: ha p = GAMMA.ELOSZLÁS(x,...), akkor INVERZ.GAMMA(p,...) = x.", "ad": "a gammaeloszláshoz tartozó valószínűség; 0 és 1 közti szám, a végpontokat is beleértve!az eloszlás paramétere, pozitív szám!az eloszlás paramétere, pozitív szám. Ha béta = 1, akkor az INVERZ.GAMMA a standard gammaeloszlással számol"}, "GAMMA.INV": {"a": "(valószínűség; alfa; béta)", "d": "A gammaeloszlás eloszlásfüggvénye inverzének értékét számítja ki: ha p = GAMMA.ELOSZL(x,...), akkor GAMMA.INVERZ(p,...) = x.", "ad": "a gammaeloszláshoz tartozó valószínűség; 0 és 1 közti szám, a végpontokat is beleértve!az eloszlás paramétere, pozitív szám!az eloszlás paramétere, pozitív szám. Ha béta = 1, akkor a GAMMA.INVERZ a standard gammaeloszlással számol"}, "GAMMALN": {"a": "(x)", "d": "A gamma-függvény természetes logaritmusát számítja ki", "ad": "az az érté<PERSON> (pozitív szám), amelyre a GAMMALN függvény értékét ki kell számítani"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "A gamma-függvény természetes logaritmusát számítja ki.", "ad": "<PERSON>z az é<PERSON>k (pozitív szám), amelyre a GAMMALN.PONTOS függvény értékét ki kell számítani."}, "GAUSS": {"a": "(x)", "d": "A standard normális eloszlás eloszlásfüggvényének értékénél 0,5-del kisebb értéket ad vissza.", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, amelyhez tudni szeretné az eloszlásfüggvény értékét"}, "GEOMEAN": {"a": "(szám1; [szám2]; ...)", "d": "Pozitív számértékekből álló tömb vagy tartomány mértani középértékét számítja ki.", "ad": "számok vagy számokat tartalmazó nevek, tömbök vagy hi<PERSON>ok (számuk 1 és 255 között lehet), amelyek mértani középértékét ki kell számítani"}, "GROWTH": {"a": "(ismert_y-ok; [ismert_x-ek]; [új_x-k]; [konstans])", "d": "Az eredmény az ismert adatpontoknak megfelelő, exponenciális trend szerint növekvő számok sorozata", "ad": "az y = b*m^x összefüggésből már ismert y-ért<PERSON><PERSON><PERSON>, pozitív számokból álló tömb vagy tartomány!az y = b*m^x összefüggésből már ismert x-ért<PERSON><PERSON><PERSON>, melyeket nem kötelező megadni, az ismert_y méretével azonos méretű tömb vagy tartomány!a megadott új x-értékeknek az a csoportja, amelyekre ki kell számítani a megfelelő y-értékeket!logikai érték: ha IGAZ, a b á<PERSON>ó kiszámítása a szokásos módon történik; ha HAMIS vagy elhagyjuk, a b állandó kötelezően 1 lesz"}, "HARMEAN": {"a": "(szám1; [szám2]; ...)", "d": "Pozitív számok halmazának harmonikus átlagát számítja ki: a számok reciprokai számtani közepének a reciprokát", "ad": "számok vagy számokat tartalmazó nevek, tömbök vagy hi<PERSON>ok (számuk 1 és 255 között lehet), amelyek harmonikus középértékét ki kell számítani"}, "HYPGEOM.DIST": {"a": "(minta_s; h<PERSON>y_minta; sokaság_s; sokaság_mérete; eloszlásfv)", "d": "A hipergeometriai eloszlás értékét számítja ki", "ad": "a mintabeli sikeres kísérletek száma!a minta mérete!a statisztikai sokaságbeli sikeres kísérletek száma!a statisztikai sokaság mérete!a függvény fajtáját megadó logikai érték: ha IGAZ, az eloszlásfüggvény értékét számítja ki, ha HAMIS, a sűrűségfüggvényét"}, "HYPGEOMDIST": {"a": "(minta_s; h<PERSON>y_minta; sokaság_s; sokaság_mérete)", "d": "A hipergeometriai eloszlás értékét számítja ki", "ad": "a mintabeli sikeres kísérletek száma!a minta mérete!a statisztikai sokaságbeli sikeres kísérletek száma!a statisztikai sokaság mérete"}, "INTERCEPT": {"a": "(ismert_y-ok; ismert_x-ek)", "d": "Az ismert x és y értékekre legjobban illeszked<PERSON> regressziós egyenes segítségével az egyenes y-tengel<PERSON>el való met<PERSON>t hatá<PERSON>zza meg.", "ad": "a függő változók vagy megfigyelések halmaza; elemei lehetnek számok vagy számokat tartalmazó nevek, tömbök vagy hivatkozások!a független változók vagy megfigyelések halmaza; elemei lehetnek számok vagy számokat tartalmazó nevek, tömbök vagy hivatkozások"}, "KURT": {"a": "(szám1; [szám2]; ...)", "d": "<PERSON><PERSON> ad<PERSON>osságát számítja ki", "ad": "azon számok vagy számokat tartalma<PERSON>ó nevek, t<PERSON>mb<PERSON><PERSON> vag<PERSON>, <PERSON><PERSON><PERSON>osságát ki kell s<PERSON>m<PERSON>tani; számuk 1 és 255 között lehet"}, "LARGE": {"a": "(tömb; k)", "d": "<PERSON><PERSON> adathalmaz k-adik legnagyobb elemét adja eredményül. Például az ötödik legnagyobb számot", "ad": "az a tömb vagy ad<PERSON>, amelynek k-adik legnagyobb értékét keresi!azt adja meg, hogy (a legnagyobbtól kezdve visszafelé) hányadik legnagyobb elemet kell megkeresni"}, "LINEST": {"a": "(ismert_y-ok; [ismert_x-ek]; [konstans]; [stat])", "d": "Visszatérési értéke a statisztikai adatok olyan lineá<PERSON>, amely ismert adatpontok egyeztetésével a legkisebb négyzetek módszerével az adatokra legjobban illeszkedő egyenes paramétereit tartalmazza", "ad": "az y = mx + b összefügg<PERSON>b<PERSON>l már ismert y-értékek!az y = mx + b összefüggésből már ismert x-értékek, nem kötelező megadni!logikai érték: ha IGAZ vagy <PERSON>, a b á<PERSON><PERSON> kiszámítása a szokásos módon történik; ha HAMIS, a b állandó kötelezően 0 lesz.!logikai érték: ha IGAZ, a regresszióra vonatkozó további statisztikai adatok is megjelennek; ha HAMIS, vagy el<PERSON>, csak az m együtthatók és a b konstans lesz az eredmény"}, "LOGEST": {"a": "(ismert_y-ok; [ismert_x-ek]; [konstans]; [stat])", "d": "Visszatérési értéke az ismert adatpontokhoz legjobban illeszkedő exponenciális görbét leíró statisztikai adatok", "ad": "az y = b*m^x összefüggésből már ismert y-értékek!az y = b*m^x összefüggésből már ismert x-értékek, melyeket nem kötelező megadni!logikai érték: ha IGAZ vagy <PERSON>, a b á<PERSON><PERSON> kiszámítása a szokásos módon történik; ha HAMIS, a b állandó kötelezően 1 lesz!egy logikai érték: ha IGAZ, a regresszióra vonatkozó további statisztikai adatok is megjelennek; ha HAMIS, vagy <PERSON>, csak az m együtthatók és a b konstans lesz az eredmény"}, "LOGINV": {"a": "(valószínűség; közép<PERSON>rték; szórás)", "d": "A lognormális eloszlás inverzét számítja ki x-re; ln(x) norm<PERSON>lis eloszlását a középérték és szórás paraméterei adják meg.", "ad": "a lognormális eloszláshoz tartozó valószínűség; 0 és 1 közti szám, a végpontokat is beleértve!az ln(x) középértéke!az ln(x) szórása, pozitív szám"}, "LOGNORM.DIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; s<PERSON><PERSON><PERSON><PERSON>; eloszlásfv)", "d": "A lognormális eloszlásfüggvény értékét számítja ki x-re; ln(x) normális eloszlását a középérték és szórás paraméterek adják meg", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> a függvény értékét ki kell s<PERSON>, pozitív szám!az ln(x) középértéke!az ln(x) szórása, pozitív szám!a függvény fajtáját megadó logikai érték: ha IGAZ, az eloszlásfüggvény értékét számítja ki, ha HAMIS, a sűrűségfüggvényét"}, "LOGNORM.INV": {"a": "(valószínűség; közép<PERSON>rték; szórás)", "d": "A lognormális eloszlás inverzét számítja ki x-re; ln(x) norm<PERSON>lis eloszlását a középérték és szórás paraméterek adják meg.", "ad": "a lognormális eloszláshoz tartozó valószínűség; 0 és 1 közti szám, a végpontokat is beleértve!az ln(x) középértéke!az ln(x) szórása, pozitív szám"}, "LOGNORMDIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; szórás)", "d": "A lognormális eloszlásfüggvény értékét számítja ki x-re; ln(x) normális eloszlását a középérték és szórás paraméterei adják meg", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> a függvény értékét ki kell s<PERSON>tani, pozitív szám!az ln(x) középértéke!az ln(x) szórása, pozitív szám"}, "MAX": {"a": "(szám1; [szám2]; ...)", "d": "Egy értékhalmazban szereplő legnagyobb számot adja meg. A logikai értékeket és szövegeket figyelmen kívül hagyja.", "ad": "azok a számok, <PERSON><PERSON> cellák, logikai értékek vagy szövegformában lévő számok (számuk 1 és 255 közé eshet), am<PERSON>ek legnagyobbikát keresi"}, "MAXA": {"a": "(érték1; [érték2]; ...)", "d": "Egy értékhalmazban szereplő legnagyobb értéket adja eredményül. A logikai értékeket és a szövegeket is figyelembe veszi.", "ad": "azok a sz<PERSON>mok, <PERSON><PERSON> cellák, logikai értékek vagy szövegformában lévő számok (számuk 1 és 255 közé eshet), melyek közül a legnagyobbat keresi"}, "MAXIFS": {"a": "(max_tartomány; kritérium_tartomány; kritériumok; ...)", "d": "A megadott feltétel- vagy kritériumkészlet által meghatározott cellák közül a legnagyobb értékűt adja eredményül", "ad": "azok a cellák, amely<PERSON>ben a legnagyobb értéket meg kell határozni!az a cellatartomány, amelyet a megadott feltételhez ki szeretne értékelni!az a szám, kifejezés vagy szöveg formátumú feltétel vagy kritérium, amely me<PERSON>, hogy a maximális érték megállapításakor mely cellákat kell figyelembe venni"}, "MEDIAN": {"a": "(szám1; [szám2]; ...)", "d": "<PERSON><PERSON> s<PERSON>mhal<PERSON>z <PERSON> (a halmaz közepén lévő számot) számítja ki.", "ad": "azok a számok vagy számokat tartalmazó nevek, tömbök vagy hivat<PERSON>zások (számuk 1 és 255 közé eshet), am<PERSON><PERSON> me<PERSON> keresi"}, "MIN": {"a": "(szám1; [szám2]; ...)", "d": "Egy értékhalmazban lévő legkisebb számot adja meg. A logikai értékeket és a szövegeket figyelmen kívül hagyja.", "ad": "azok a számok, <PERSON><PERSON> cellák, logikai értékek vagy szövegformában lévő számok (számuk 1 és 255 közé eshet), am<PERSON><PERSON> legkisebbikét keresi"}, "MINA": {"a": "(érték1; [érték2]; ...)", "d": "Egy értékhalmazban szereplő legkisebb értéket adja eredményül. A logikai értékeket és a szövegeket is figyelembe veszi.", "ad": "azok a sz<PERSON>mok, <PERSON><PERSON> cellák, logikai értékek vagy szövegformában lévő számok (számuk 1 és 255 közé eshet), melyek közül a legkisebbet keresi"}, "MINIFS": {"a": "(min_tartomány; kritérium_tartomány; kritériumok; ...)", "d": "A megadott feltétel- vagy kritériumkészlet által meghatározott cellák közül a legkisebb értékűt adja eredményül", "ad": "azok a cellák, amely<PERSON>ben a legkisebb értéket meg kell határozni!az a cellatartomány, amelyet a megadott feltételhez ki szeretne értékelni!az a szám, kifejezés vagy szöveg formátumú feltétel vagy kritérium, amely megh<PERSON>, hogy a minimális érték megállapításakor mely cellákat kell figyelembe venni"}, "MODE": {"a": "(szám1; [szám2]; ...)", "d": "<PERSON><PERSON> tömbből vagy adattartományból kiválasztja a leggyakrabban előforduló vagy ismétlődő számot.", "ad": "azon számok vagy számokat tartalmazó nevek, tömbök vagy hi<PERSON>, amelyekre a függvényt ki kell számítani; számuk 1 és 255 között lehet"}, "MODE.MULT": {"a": "(szám1; [szám2]; ...)", "d": "Egy tömbben vagy adattartományban leggyakrabban szereplő vagy ismétlődő értékek egy függőleges tömbjét adja vissza. Vízszintes tömbhöz használja a =TRANSZPONÁLÁS(MÓDUSZ.TÖBB(szám1,szám2,...))", "ad": "azon számok vagy számokat tartalmazó nevek, tömbök vagy hi<PERSON>, amelyekre a függvényt ki kell számítani; számuk 1 és 255 között lehet"}, "MODE.SNGL": {"a": "(szám1; [szám2]; ...)", "d": "<PERSON><PERSON> tömbből vagy adattartományból kiválasztja a leggyakrabban előforduló vagy ismétlődő számot", "ad": "azon számok vagy számokat tartalmazó nevek, tömbök vagy hi<PERSON>, amelyekre a függvényt ki kell számítani; számuk 1 és 255 között lehet"}, "NEGBINOM.DIST": {"a": "(kudarc_s<PERSON>m; si<PERSON><PERSON>k; valószínűség; eloszlásfv)", "d": "A negatív binomiális eloszlás értékét adja meg; annak a valószínűségét, hogy adott számú kudarc lesz a sikerek adott számú bekövetkezése előtt a siker adott valószínűsége esetén", "ad": "a kudarcok száma!a siker küszöbértéke!a siker valószínűsége; 0 és 1 közötti szám!logikai érték: ha IGAZ, az eloszlásfüggvény értékét számítja ki, ha HAMIS, a tömegfüggvényét"}, "NEGBINOMDIST": {"a": "(kudarc_sz<PERSON>m; si<PERSON><PERSON>k; valószínűség)", "d": "A negatív binomiális eloszlás értékét adja meg; annak a valószínűsége, hogy megadott számú kudarc lesz a sikerek megadott számú bekövetkezése előtt a siker adott valószínűsége esetén", "ad": "a kudarcok száma!a siker küszöbértéke!a siker valószínűsége; 0 és 1 közötti szám"}, "NORM.DIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; s<PERSON><PERSON><PERSON><PERSON>; eloszlásfv)", "d": "A normál eloszlás eloszlásfüggvényének értékét számítja ki a megadott középérték és szórás esetén", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> az eloszlást ki kell számítani!az eloszlás középért<PERSON>ke (várható értéke)!az eloszlás szórása, pozitív szám!logikai érték: ha IGAZ, az eloszlásfüggvény értékét számítja ki, ha HAMIS, a sűrűségfüggvényét"}, "NORMDIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; s<PERSON><PERSON><PERSON><PERSON>; eloszlásfv)", "d": "A normális eloszlás értékét számítja ki a megadott középérték és szórás esetén.", "ad": "az az <PERSON>rt<PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> az eloszlást ki kell számítani!az eloszlás középértéke (várható értéke)!az eloszlás szórása, pozitív szám!a függvény fajtáját megadó logikai érték: ha IGAZ, akkor az eloszlásfüggvény értékét számítja ki; ha HAMIS, a sűrűségfüggvényét."}, "NORM.INV": {"a": "(valószínűség; közép<PERSON>rték; szórás)", "d": "A normális eloszlás eloszlásfüggvénye inverzének értékét számítja ki a megadott középérték és szórás esetén.", "ad": "a normális eloszláshoz tartozó valószínűség; 0 és 1 közötti szám, a végpontokat is beleértve!az eloszlás középértéke (várható értéke)!az eloszlás szórása, pozitív szám"}, "NORMINV": {"a": "(valószínűség; közép<PERSON>rték; szórás)", "d": "A normális eloszlás eloszlásfüggvénye inverzének értékét számítja ki a megadott középérték és szórás esetén.", "ad": "a normális eloszláshoz tartozó valószínűség; 0 és 1 közötti szám a végpontokat is beleértve!az eloszlás középértéke (várható értéke)!az eloszlás szórása, pozitív szám"}, "NORM.S.DIST": {"a": "(z; eloszlásfv)", "d": "A standard normális eloszlás eloszlásfüggvényének értékét számítja ki (a standard normális eloszlás középértéke 0, szórása 1)", "ad": "az <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> az eloszlást ki kell számítani!a függvény által visszaadott logikai érték: ha IGAZ, az eloszlásfüggvény értékét számítja ki, ha HAMIS, a sűrűségfüggvényét"}, "NORMSDIST": {"a": "(z)", "d": "A standard normális eloszlás eloszlásfüggvényének értékét számítja ki. A standard normális eloszlás középértéke 0, szórása 1.", "ad": "az az <PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> az eloszlást ki kell s<PERSON>ámítani"}, "NORM.S.INV": {"a": "(valószínűség)", "d": "A standard normális eloszlás eloszlásfüggvénye inverzének értékét számítja ki. A standard normális eloszlás középértéke 0, szórása 1.", "ad": "a normális eloszláshoz tartozó valószínűség; 0 és 1 közötti szám, a végpontokat is beleértve"}, "NORMSINV": {"a": "(valószínűség)", "d": "A standard normális eloszlás eloszlásfüggvénye inverzének értékét számítja ki. A standard normális eloszlás középértéke 0, szórása 1.", "ad": "a normális eloszláshoz tartozó valószínűség; 0 és 1 közötti szám a végpontokat is beleértve"}, "PEARSON": {"a": "(tömb1; tömb2)", "d": "A Pearson-<PERSON><PERSON><PERSON> (r) számítja ki ", "ad": "a független értékek halmaza!a függő értékek halmaza"}, "PERCENTILE": {"a": "(tömb; k)", "d": "Egy tartományban található értékek k-adik percentilisét, a<PERSON><PERSON>kosztályát adja eredményül.", "ad": "az egymáshoz viszonyítandó adatokat tartalmazó tömb vagy tartomány!a százalékosztály száma 0 és 1 között, a végpontokat is beleértve"}, "PERCENTILE.EXC": {"a": "(tömb; k)", "d": "Egy tartományban található értékek k-adik percentilisét, a<PERSON><PERSON>ékosztályát adja eredményül, ahol k a 0 és 1 közötti tartományban található, a végpontok nélkül", "ad": "az egymáshoz viszonyítandó adatokat tartalmazó tömb vagy tartomány!a százalékosztály száma 0 és 1 között, a végpontokat is beleértve"}, "PERCENTILE.INC": {"a": "(tömb; k)", "d": "Egy tartományban található értékek k-adik percentilisét, a<PERSON><PERSON>ékosztályát adja eredményül, ahol k a 0 és 1 közötti tartományban található, a végpontokat is beleértve", "ad": "az egymáshoz viszonyítandó adatokat tartalmazó tömb vagy tartomány!a százalékosztály száma 0 és 1 között, a végpontokat is beleértve"}, "PERCENTRANK": {"a": "(tömb; x; [pontoss<PERSON>g])", "d": "Egy értéknek egy adathalmazon belül vett s<PERSON>ázal<PERSON> (elhelyezkedését) adja meg.", "ad": "az egymáshoz viszonyítandó számadatokat tartalmazó tömb vagy tartomány!az az érték, amelynek rangját meg kell határozni!az eredményül kapott százalékérték értékes jegyeinek számát határozza meg; nem kötelező megadni; he elhagyjuk, 3 értékes jegy lesz (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(tömb; x; [pontoss<PERSON>g])", "d": "Egy értéknek egy adathalmazon belül vett százalékos <PERSON> (elhelyezkedését) adja meg (0 és 1 között, a végpontok nélkül)", "ad": "az egymáshoz viszonyítandó számadatokat tartalmazó tömb vagy tartomány!az az érték, amelynek rangját meg kell határozni!az eredményül kapott százalékérték értékes jegyeinek számát határozza meg; nem kötelező megadni; ha elhagyjuk, 3 számjegyű lesz (0,xxx%)"}, "PERCENTRANK.INC": {"a": "(tömb; x; [pontoss<PERSON>g])", "d": "Egy értéknek egy adathalmazon belül vett százalékos <PERSON> (elhelyezkedését) adja meg (0 és 1 között, a végpontokat is beleértve)", "ad": "az egymáshoz viszonyítandó számadatokat tartalmazó tömb vagy tartomány!az az érték, amelynek rangját meg kell határozni!az eredményül kapott százalékérték értékes jegyeinek számát határozza meg; nem kötelező megadni; ha elhagyjuk, 3 számjegyű lesz (0,xxx%)"}, "PERMUT": {"a": "(szám; hány_kiválasztott)", "d": "Adott számú objektum k-ad osztá<PERSON>ú ismétlés nélküli variációinak számát számítja ki.", "ad": "az összes objektum száma!az egy-egy alkalommal kiválasztott objektumok száma"}, "PERMUTATIONA": {"a": "(szám; hány_kiválasztott)", "d": "Adott számú objektum k-ad osztá<PERSON><PERSON> ismétléses variációinak számát számítja ki", "ad": "az összes objektum száma!az egy-egy alkalommal kiválasztott objektumok száma"}, "PHI": {"a": "(x)", "d": "A standard normális elosz<PERSON>ás sűrűségfüggvényének értékét számítja ki.", "ad": "az a szám, am<PERSON><PERSON><PERSON><PERSON> a standard norm<PERSON>lis elosz<PERSON> s<PERSON>rűségfüggvényének értékére k<PERSON>i"}, "POISSON": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; eloszlásfv)", "d": "A Poisson-eloszlás értékét számítja ki.", "ad": "az események száma!a várható érték, pozitív szám!a függvény fajtáját megadó logikai érték: ha IGAZ, akkor a Poisson-eloszlásfüggvény értékét számítja ki; ha HAMIS, a Poisson-tömegfüggvényét."}, "POISSON.DIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; eloszlásfv)", "d": "A Poisson-eloszlás értékét számítja ki.", "ad": "az események száma!a várható érték, pozitív szám!a függvény fajtáját megadó logikai érték: ha IGAZ, akkor a POISSON függvény az eloszlásfüggvény értékét számítja ki; ha HAMIS, a sűrűségfüggvényét"}, "PROB": {"a": "(x_tartomány; val_tartomány; als<PERSON>_határ; [fels<PERSON>_határ])", "d": "Annak v<PERSON>ószínűségét számítja ki, hogy adott értékek két határérték közé <PERSON>, vagy az alsó határértékkel egy<PERSON>lőek", "ad": "azon számértékeket tartalmazó tartomány, amelyekhez ismertek a valószínűségértékek!az x_tartományban található számokhoz rendelt valószínűségek; 0 és 1 közötti értékek a 0 kivételével!az adatok alsó határa a valószínűség kiszámításához!az adatok felső határa (nem kötelező). Ha elhagyjuk, a függvény annak a valószínűségét adja meg, hogy az x-tartomány elemei az alsó-határral egyenlőek."}, "QUARTILE": {"a": "(tömb; kvart)", "d": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> (negyedszintjét) számítja ki.", "ad": "azon s<PERSON>ámértékek tömbje vagy <PERSON>, am<PERSON><PERSON> k<PERSON>til<PERSON>t meg kell határozni!azt jelzi, hogy melyik kvartilist kell kiszámítani: minimumérték = 0; <PERSON><PERSON><PERSON> kvartilis = 1; medián = 2; harmadik kvartilis = 3; maximumérték = 4"}, "QUARTILE.INC": {"a": "(tömb; kvart)", "d": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> (negyedszintjét) számítja ki az értékek percentilise, a<PERSON>z százalékosztálya alapján (0 és 1 között, a végpontokat is beleértve).", "ad": "azon s<PERSON>ámértékek tömbje vagy <PERSON>, am<PERSON><PERSON> k<PERSON>til<PERSON>t meg kell határozni!azt jelzi, hogy melyik kvartilist kell kiszámítani: minimumérték = 0; <PERSON><PERSON><PERSON> kvartilis = 1; medián = 2; harmadik kvartilis = 3; maximumérték = 4"}, "QUARTILE.EXC": {"a": "(tömb; kvart)", "d": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> (negyedszintjét) számítja ki az értékek percentilise, a<PERSON>z százalékosztálya alapján (0 és 1 között, a végpontok nélkül).", "ad": "azon s<PERSON>ámértékek tömbje vagy <PERSON>, am<PERSON><PERSON> k<PERSON>til<PERSON>t meg kell határozni!azt jelzi, hogy melyik kvartilist kell kiszámítani: minimumérték = 0; <PERSON><PERSON><PERSON> kvartilis = 1; medián = 2; harmadik kvartilis = 3; maximumérték = 4"}, "RANK": {"a": "(szám; hiv; [sorrend])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy egy szám nagysága alapján hányadik egy számsorozatban.", "ad": "az a <PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> meg kell <PERSON>, h<PERSON><PERSON>dik!egy számsorozatot tartalmazó tömb vagy egy számsorozatra való hivatkozás. A nem szám értékeket a függvény nem veszi számításba.!a számok sorba rendezését megadó számérték: ha 0 vagy elhagyjuk, csökkenő sorrend; bármely nem nulla érték esetén növekvő sorrend"}, "RANK.AVG": {"a": "(szám; hiv; [sorrend])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy egy szám nagysága alapján hányadik egy számsorozatban; ha több érték sorszáma a<PERSON>, a sorszámok átlagát adja v<PERSON>za", "ad": "az a <PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> meg kell <PERSON>, h<PERSON><PERSON>dik!egy számsorozatot tartalmazó tömb vagy egy számsorozatra való hivatkozás. A nem számértékeket a függvény nem veszi számításba!a számok sorba rendezését megadó számérték: ha 0 vagy elhagyjuk, csökkenő sorrend; bármely nem nulla érték esetén növekvő sorrend"}, "RANK.EQ": {"a": "(szám; hiv; [sorrend])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy egy szám nagysága alapján hányadik egy számsorozatban; ha több érték sorszáma azon<PERSON>, a halmazhoz tartozó legmagasabb sorszámot adja vissza", "ad": "az a <PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> meg kell <PERSON>, h<PERSON><PERSON>dik!egy számsorozatot tartalmazó tömb vagy egy számsorozatra való hivatkozás. A nem számértékeket a függvény nem veszi számításba!a számok sorba rendezését megadó számérték: ha 0 vagy elhagyjuk, csökkenő sorrend; bármely nem nulla érték esetén növekvő sorrend"}, "RSQ": {"a": "(ismert_y-ok; ismert_x-ek)", "d": "Kiszámítja a Pearson-féle szorzatmomentum korrelációs együtthatójának négyzetét a megadott adatpontok esetén.", "ad": "az adatokat tartalmazó tömb vagy tartomány; lehetnek számok, nevek, tömbök vagy számokat tartalmazó hivatkozások!az adatokat tartalmazó tömb vagy tartomány; lehetnek számok, nevek, tömbök vagy számokat tartalmazó hivatkozások"}, "SKEW": {"a": "(szám1; [szám2]; ...)", "d": "Egy eloszlás ferdeségét határozza meg; a ferdeség az eloszlás átlaga körül vett aszimmetria mértékét adja meg.", "ad": "azon számok vagy számokat tartalmazó nevek, tömbök vagy <PERSON> (számuk 1 és 255 között lehet), am<PERSON><PERSON><PERSON> a ferdeséget meg kell határozni."}, "SKEW.P": {"a": "(szám1; [szám2]; ...)", "d": "Egy eloszlás ferdeségét határozza meg egy sokaság al<PERSON>; a ferdeség az eloszlás átl<PERSON> körül vett aszimmetria mértékét adja meg.", "ad": "azon számok vagy számokat tartalmazó nevek, tömbök vagy <PERSON> (számuk 1 és 254 között lehet), amely<PERSON><PERSON> a sokaság ferdeségét meg kell határozni"}, "SLOPE": {"a": "(ismert_y-ok; ismert_x-ek)", "d": "A megadott adatpontokon át h<PERSON>ó lineáris regress<PERSON> egyenes meredekségét számítja ki.", "ad": "a függő értékeket tartalmazó tömb vagy cellatartomány; az elemek lehetnek számok vagy számokat tartalmazó nevek, tömbök vagy hivatkozások!a független értékek halmaza; az elemek lehetnek számok vagy számokat tartalmazó nevek, tömbök vagy hivatkozások"}, "SMALL": {"a": "(tömb; k)", "d": "<PERSON><PERSON> adathalmaz k-adik legkisebb elemét adja eredményül. Például az ötödik legkisebb számot", "ad": "az a tömb vagy ad<PERSON>, amelynek k-adik legkisebb értékét keresi!azt adja meg, hogy (a legkisebbtől kezdve visszafelé) hányadik legkisebb elemet kell megkeresni"}, "STANDARDIZE": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; szórás)", "d": "Középértékkel és szórással megadott eloszlásból normalizált értéket ad eredményül.", "ad": "a normalizálandó érték!az eloszlás számtani középértéke!az eloszlás szórása, pozitív szám"}, "STDEV": {"a": "(szám1; [szám2]; ...)", "d": "Minta alapján becslést ad a szórásra (a mintában lévő logikai értékeket és szöveget nem veszi figyelembe).", "ad": "a statisztikai mintát reprezent<PERSON><PERSON><PERSON>, számuk 1 és 255 között lehet; lehetnek számok vagy számokat tartalmazó hivat<PERSON>"}, "STDEV.P": {"a": "(szám1; [szám2]; ...)", "d": "Az argumentumokkal megadott statisztikai sokaság egészéből kiszámítja annak szórását (a logikai értékeket és a szövegeket figyelmen kívül hagyja)", "ad": "a statisztikai sokaságot reprezent<PERSON><PERSON><PERSON>, számuk 1 és 255 között lehet; lehetnek számok vagy számokat tartalmazó hivat<PERSON>"}, "STDEV.S": {"a": "(szám1; [szám2]; ...)", "d": "Minta alapján becslést ad a szórásra (a mintában lévő logikai értékeket és szöveget nem veszi figyelembe)", "ad": "a statisztikai mintát reprezent<PERSON><PERSON><PERSON>, számuk 1 és 255 között lehet; lehetnek számok vagy számokat tartalmazó hivat<PERSON>"}, "STDEVA": {"a": "(érték1; [érték2]; ...)", "d": "Minta alapján becslést ad a sokaság szórására, a logikai értékek és a szövegek figyelembevételével. A szöveg és a HAMIS logikai érték 0-nak, az IGAZ logikai érték 1-nek számít.", "ad": "a sokaságból vett minta értékei, számuk 1 és 255 között lehet; <PERSON><PERSON><PERSON><PERSON><PERSON>, nevek vagy értékhivatkozások lehetnek"}, "STDEVP": {"a": "(szám1; [szám2]; ...)", "d": "Az argumentumokkal megadott statisztikai sokaság egészéből kiszámítja annak szórását (a logikai értékeket és a szövegeket figyelmen kívül hagyja).", "ad": "a statisztikai sokaságot reprezent<PERSON><PERSON><PERSON>, számuk 1 és 255 között lehet; lehetnek számok vagy számokat tartalmazó hivat<PERSON>"}, "STDEVPA": {"a": "(érték1; [érték2]; ...)", "d": "A statisztikai sokaság egészéből kiszámítja a szórást, a logikai értékek és a szövegek figyelembevételével. A szöveg és a HAMIS logikai érték 0-nak, az IGAZ logikai érték 1-nek számít.", "ad": "a sokasághoz tartozó é<PERSON>, számuk 1 és 255 között lehet; <PERSON><PERSON><PERSON><PERSON><PERSON>, neve<PERSON>, tömbök vagy értéket tartalmazó hivatkozások lehetnek"}, "STEYX": {"a": "(ismert_y-ok; ismert_x-ek)", "d": "A regresszióban az egyes x-értékek alapján meghatározott y-értékek standard hibáját számítja ki.", "ad": "a függő értékeket tartalmazó tömb vagy tartomány; az elemek lehetnek számok vagy számokat tartalmazó nevek, tömbök vagy hivatkozások!a független értékeket tartalmazó tömb vagy tartomány; az elemek lehetnek számok vagy számokat tartalmazó nevek, tömbök vagy hivatkozások"}, "TDIST": {"a": "(x; sza<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; sz<PERSON><PERSON>)", "d": "A Student-féle t-el<PERSON><PERSON><PERSON><PERSON> értékét számítja ki.", "ad": "az a sz<PERSON><PERSON>, am<PERSON><PERSON><PERSON>l a függvény értékét ki kell számítani!az eloszlást jellemző szabadságfokok száma!az eloszlásszélek száma: egyszélű eloszlás esetén 1, kétszélű esetén 2"}, "TINV": {"a": "(valószínűség; szabadságfok)", "d": "A Student-féle t-eloszlás kétszélű inverzét számítja ki.", "ad": "a Student-féle t-eloszláshoz tartozó valószínűség; 0 és 1 közötti szám a végpontokat is beleértve!az eloszlás szabadságfokának számát jelző pozitív szám"}, "T.DIST": {"a": "(x; s<PERSON><PERSON>ságfok; eloszlásfv)", "d": "A Student-<PERSON><PERSON><PERSON> b<PERSON>z<PERSON>lű t-eloszlás értékét számítja ki", "ad": "az a <PERSON>z<PERSON><PERSON>, am<PERSON><PERSON><PERSON>l a függvény értékét ki kell számítani!az eloszlást jellemző szabadságfokok száma!logikai érték; ha IGAZ, akkor az eloszlásfüggvény értékét számítja ki; ha HAMIS, a sűrűségfüggvényét"}, "T.DIST.2T": {"a": "(x; s<PERSON><PERSON><PERSON>ág<PERSON><PERSON>)", "d": "A Student-féle t-eloszlás kétszélű értékét számítja ki.", "ad": "az a sz<PERSON><PERSON>, amelyn<PERSON>l a függvény értékét ki kell számítani!az eloszlást jellemző szabadságfokok száma"}, "T.DIST.RT": {"a": "(x; s<PERSON><PERSON><PERSON>ág<PERSON><PERSON>)", "d": "A Student-féle t-el<PERSON><PERSON><PERSON><PERSON> jobbszélű értékét számítja ki.", "ad": "az a sz<PERSON><PERSON>, amelyn<PERSON>l a függvény értékét ki kell számítani!az eloszlást jellemző szabadságfokok száma"}, "T.INV": {"a": "(valószínűség; szabadságfok)", "d": "A Student-féle t-eloszlás balszélű inverzét számítja ki", "ad": "a Student-féle t-eloszláshoz tartozó valószínűség; 0 és 1 közötti szám, a végpontokat is beleértve!az eloszlás szabadságfokának számát jelző pozitív szám"}, "T.INV.2T": {"a": "(valószínűség; szabadságfok)", "d": "A Student-féle t-eloszlás kétszélű inverzét számítja ki", "ad": "a Student-féle t-eloszláshoz tartozó valószínűség; 0 és 1 közötti szám, a végpontokat is beleértve!az eloszlás szabadságfokának számát jelző pozitív szám"}, "T.TEST": {"a": "(tömb1; tömb2; s<PERSON><PERSON><PERSON>; t<PERSON><PERSON>)", "d": "A Student-féle t-próbához tartozó valószínűséget számítja ki", "ad": "az első adathalmaz!a második adathalmaz!az eloszlásszélek száma: egyszélű eloszlás esetén 1, kétszélű esetén 2!a végrehajtandó t-próba fajtája: p<PERSON>rosított = 1, k<PERSON><PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON> varianciájú (homoscedasztikus) = 2, k<PERSON><PERSON><PERSON><PERSON><PERSON>, nem egyen<PERSON>ő varianciájú = 3"}, "TREND": {"a": "(ismert_y-ok; [ismert_x-ek]; [új_x-ek]; [konstans])", "d": "Visszatérési érték a legkisebb négyzetek módszere szerint az ismert adatpontokra fektetett egyenes segítségével lineá<PERSON> trend számértéke", "ad": "az y = mx + b összefügg<PERSON>ből már ismert y-értékekből álló tartomány vagy tömb!az y = mx + b összefüggésből már ismert x-értékekből álló tartomány vagy tömb, nem kötelező megadni. Mérete azonos az ismert y-értékekből álló tömb vagy tartomány méretével!az új x-értékekből álló azon tartomány vagy tömb, amelyre a TREND függvénynek a megfelelő y-értékeket ki kell számítani!logikai érték: ha IGAZ vagy elhagyjuk, a b állandó kiszámítása a szokásos módon történik; ha HAMIS, a b állandó kötelezően 0 lesz"}, "TRIMMEAN": {"a": "(tömb; százalék)", "d": "<PERSON><PERSON> ad<PERSON>kből álló halmaz középső részének átlagát számítja ki", "ad": "az a tartomány vagy tö<PERSON>, amelynek egy részét átlagolni kell!az adathalmaz elejéről és végéről elhagyandó adatok százalékos aránya"}, "TTEST": {"a": "(tömb1; tömb2; s<PERSON><PERSON><PERSON>; t<PERSON><PERSON>)", "d": "A Student-féle t-próbához tartozó valószínűséget számítja ki", "ad": "az első adathalmaz!a második adathalmaz!az eloszlásszélek száma: egyszélű eloszlás esetén 1, kétszélű esetén 2!a végrehajtandó t-próba fajtája: p<PERSON>rosított = 1, k<PERSON><PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON> varianciájú (homoscedasztikus) = 2, k<PERSON><PERSON><PERSON><PERSON><PERSON>, nem egyen<PERSON>ő varianciájú = 3"}, "VAR": {"a": "(szám1; [szám2]; ...)", "d": "Minta alapján becslést ad a varianciára (a mintában lévő logikai értékeket és szövegeket figyelmen kívül hagyja).", "ad": "a statisztikai mintát reprezentáló numerikus argumentumok, számuk 1 és 255 között lehet"}, "VAR.P": {"a": "(szám1; [szám2]; ...)", "d": "Egy statisztikai sokaság varianciáját számítja ki (a sokaságban lévő logikai értékeket és szövegeket figyelmen kívül hagyja)", "ad": "a statisztikai sokaságot reprezentáló numerikus argumentumok, számuk 1 és 255 között lehet"}, "VAR.S": {"a": "(szám1; [szám2]; ...)", "d": "Minta alapján becslést ad a varianciára (a mintában lévő logikai értékeket és szövegeket figyelmen kívül hagyja)", "ad": "a statisztikai mintát reprezentáló numerikus argumentumok, számuk 1 és 255 között lehet"}, "VARA": {"a": "(érték1; [érték2]; ...)", "d": "Minta alapján becslést ad a sokaság varianciájára, a logikai értékek és a szövegek figyelembevételével. A szöveg és a HAMIS logikai érték 0-nak, az IGAZ logikai érték 1-nek számít.", "ad": "a sokaságból vett minta értékei, számuk 1 és 255 között lehet"}, "VARP": {"a": "(szám1; [szám2]; ...)", "d": "Egy statisztikai sokaság varianciáját számítja ki (a sokaságban lévő logikai értékeket és szövegeket figyelmen kívül hagyja).", "ad": "a statisztikai sokaságot reprezentáló numerikus argumentumok, számuk 1 és 255 között lehet"}, "VARPA": {"a": "(érték1; [érték2]; ...)", "d": "A statisztikai sokaság egészéből kiszámítja a varianciát, a logikai értékek és a szövegek figyelembevételével. A szöveg és a HAMIS logikai érték 0-nak, az IGAZ logikai érték 1-nek számít.", "ad": "a sokasághoz tartozó értékek, számuk 1 és 255 között lehet"}, "WEIBULL": {"a": "(x; alfa; b<PERSON>ta; eloszlásfv)", "d": "<PERSON>bull-f<PERSON><PERSON> eloszlás <PERSON>rtékét számítja ki", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> a függvény értékét ki kell sz<PERSON>, nem negatív szám!az eloszlás paramétere, pozitív szám!az eloszlás paramétere, pozitív szám!a függvény fajtáját megadó logikai érték: ha IGAZ, akkor az eloszlásfüggvény értékét számítja ki; ha HAMIS, a tömegfüggvényét"}, "WEIBULL.DIST": {"a": "(x; alfa; b<PERSON>ta; eloszlásfv)", "d": "<PERSON>bull-f<PERSON><PERSON> eloszlás <PERSON>rtékét számítja ki", "ad": "az az <PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> a függvény értékét ki kell sz<PERSON>, nem negatív szám!az eloszlás paramétere, pozitív szám!az eloszlás paramétere, pozitív szám!a függvény fajtáját megadó logikai érték: ha IGAZ, akkor az eloszlásfüggvény értékét számítja ki; ha HAMIS, a sűrűségfüggvényét"}, "Z.TEST": {"a": "(tömb; x; [szigma])", "d": "Az egyszélű z-próbával kapott P-értéket (az aggregált elsőfajú hiba nagyságát) számítja ki.", "ad": "az x-szel összevetendő adatokat tartalmazó tömb vagy tartomány!a vizsgálandó érték!a sokaság (ismert) szórása. Ha elhagyja, a minta szórása lesz felhasználva"}, "ZTEST": {"a": "(tömb; x; [szigma])", "d": "Az egyszélű z-próbával kapott P-értéket (az aggregált elsőfajú hiba nagyságát) számítja ki.", "ad": "az x-szel összevetendő adatokat tartalmazó tömb vagy tartomány!a vizsgálandó érték!a sokaság (ismert) szórása. Ha elhagyja, a minta szórása lesz felhasználva"}, "ACCRINT": {"a": "(kibocs<PERSON>tás; <PERSON><PERSON><PERSON>_ka<PERSON>; elsz<PERSON>molás; ráta; névérték; gyakoriság; [alap]; [szám_mód])", "d": "Időszakosan kamatozó értékpapír felhalmozott kamatát adja eredményül.", "ad": "az értékpapír kibocsátásának dátuma dátumértékként kifejezve!az értékpapír első kamatfizetési időpontja dátumértékként kifejezve!az értékpapír elszámolási dátuma dátumértékként kifejezve!az értékpapír éves szelvénykamatlába!az értékpapír névértéke!a szelvénykifizetések évenkénti száma!az évtöredék számításának alapja!logikai érték: a kibocsátás óta felhalmozott kamat = TRUE vagy nincs megadva; a legutóbbi szelvénykifizetési dátumtól való számítás = FALSE"}, "ACCRINTM": {"a": "(kibocsátás; elszámolás; ráta; névérték; [alap])", "d": "Lejáratkor kamatozó értékpapír felhalmozott kamatát adja eredményül.", "ad": "az értékpapír kibocsátásának dátuma dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapír éves kamat- vagy osztalékszelvény-fizetési rátája!az értékpapír névértéke!az évtöredék számításának alapja"}, "AMORDEGRC": {"a": "(költség; be<PERSON><PERSON><PERSON><PERSON>_d<PERSON>; <PERSON><PERSON><PERSON>_id<PERSON><PERSON><PERSON>; marad<PERSON>y<PERSON>k; id<PERSON><PERSON><PERSON>; r<PERSON><PERSON>; [alap])", "d": "<PERSON>gy adott tárgyi eszköznek az egyes könyvelési időszakokra eső, a<PERSON><PERSON><PERSON><PERSON> felosz<PERSON>tt lineáris értékcsökkenését számítja ki.", "ad": "a tárgyi eszköz költsége!a tárgyi eszköz beszerzésének dátuma!az első időszak végének dátuma!a tárgyi eszköz maradványértéke az élettartam eltelte után!az az időszak, amelyre az értékcsökkenést ki kell számítani!az értékcsökkenés rátája!évalap: 0 = 360 napos év, 1 = tényleges napszámot tartalmazó év, 3 = 365 napos év"}, "AMORLINC": {"a": "(költség; be<PERSON><PERSON><PERSON><PERSON>_d<PERSON>; <PERSON><PERSON><PERSON>_id<PERSON><PERSON><PERSON>; marad<PERSON>y<PERSON>k; id<PERSON><PERSON><PERSON>; r<PERSON><PERSON>; [alap])", "d": "<PERSON>gy adott tárgyi eszköznek az egyes könyvelési időszakokra eső, a<PERSON><PERSON><PERSON><PERSON> felosz<PERSON>tt lineáris értékcsökkenését számítja ki.", "ad": "a tárgyi eszköz költsége!a tárgyi eszköz beszerzésének dátuma!az első időszak végének dátuma!a tárgyi eszköz maradványértéke az élettartam eltelte után!az az időszak, amelyre az értékcsökkenést ki kell számítani!az értékcsökkenés rátája!évalap: 0 = 360 napos év, 1 = tényleges napszámot tartalmazó év, 3 = 365 napos év"}, "COUPDAYBS": {"a": "(elsz<PERSON><PERSON>l<PERSON>; lej<PERSON><PERSON>; gyakoriság; [alap])", "d": "A szelvénykifizetési időszak kezdetétől az elszámolási dátumig tartó napok számát adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "COUPDAYS": {"a": "(elsz<PERSON><PERSON>l<PERSON>; lej<PERSON><PERSON>; gyakoriság; [alap])", "d": "<PERSON><PERSON> elszá<PERSON> időpont<PERSON>t is tartalmazó szelvénykifizetési időszak hosszát adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "COUPDAYSNC": {"a": "(elsz<PERSON><PERSON>l<PERSON>; lej<PERSON><PERSON>; gyakoriság; [alap])", "d": "Az elszámolás időpontjától a következő szelvénykifizetési dátumig tartó napok számát adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "COUPNCD": {"a": "(elsz<PERSON><PERSON>l<PERSON>; lej<PERSON><PERSON>; gyakoriság; [alap])", "d": "<PERSON>z elszámolást követő legelső szelvénykifizetési dátumot adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "COUPNUM": {"a": "(elsz<PERSON><PERSON>l<PERSON>; lej<PERSON><PERSON>; gyakoriság; [alap])", "d": "Az elszámolás és a lejárat időpontja között kifizetendő szelvények számát adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "COUPPCD": {"a": "(elsz<PERSON><PERSON>l<PERSON>; lej<PERSON><PERSON>; gyakoriság; [alap])", "d": "<PERSON><PERSON><PERSON> el<PERSON>tti utolsó szelvénykifizetési dátumot adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "CUMIPMT": {"a": "(r<PERSON><PERSON>; időszakok_száma; j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; kez<PERSON><PERSON>_időszak; <PERSON><PERSON><PERSON><PERSON>_időszak; típus)", "d": "Két fizetési időszak között kifizetett kamat halmozott értékét adja eredményül.", "ad": "kamatláb!a fizetési időszakok száma!a jelenérték!a számításban szereplő első időszak!a számításban szereplő utolsó időszak!a fizetés esedékessége"}, "CUMPRINC": {"a": "(r<PERSON><PERSON>; időszakok_száma; j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; kez<PERSON><PERSON>_időszak; <PERSON><PERSON><PERSON><PERSON>_időszak; típus)", "d": "Két fizetési időszak között kifizetett részletek halmozott (kamatot nem tartalmazó) értékét adja eredményül.", "ad": "kamatláb!a fizetési időszakok száma!a jelenérték!a számításban szereplő első időszak!a számításban szereplő utolsó időszak!a fizetés esedékessége"}, "DB": {"a": "(költség; maradván<PERSON>k; le<PERSON><PERSON><PERSON><PERSON>_id<PERSON>; id<PERSON><PERSON><PERSON>; [hónap])", "d": "Eredményül egy eszköz adott időszak alatti értékcsökkenését számítja ki a lineáris leírási modell alkalmazásával.", "ad": "az eszköz beszerzési ára!az eszköz maradványértéke a leírási idő eltelte után!a leírási időszakok teljes száma (azaz az eszköz hasznos élettartama)!az az időszak, amelyre az értékcsökkenés mértékét ki kell számítani; ugyanazt a mértékegységet kell használni, mint az élettartam megadásánál!a leírás első évében számításba veendő hónapok száma. Ha nincs megadva, akkor a KCS2 függvény ezt 12-nek tekinti."}, "DDB": {"a": "(költség; marad<PERSON>k; le<PERSON><PERSON><PERSON><PERSON>_id<PERSON>; id<PERSON><PERSON><PERSON>; [faktor])", "d": "Egy eszköz értékcsökkenését számítja ki egy adott időszakra vonat<PERSON> a progresszív vagy egyéb megadott leírási modell alkalmazásával.", "ad": "az eszköz beszerzési ára!az eszköz maradványértéke a leírási idő eltelte után!a leírási időszakok teljes száma (azaz az eszköz hasznos élettartama)!az az időszak, amelyre az értékcsökkenés mértékét ki kell számítani; ugyanazt a mértékegységet kell használni, mint az élettartam megadásánál!a leírás gyorsasága. Ha nincs megadva, akkor értékét a Spreadsheet Editor 2-nek veszi, azaz progresszív leírási modellel számol."}, "DISC": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; le<PERSON><PERSON><PERSON>; <PERSON>r; v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; [alap])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>r <PERSON>ítolási rátáját adja er<PERSON>ményül.", "ad": "az értékpapír kifizetésének időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!a 100 Ft névértékű értékpapír ára!a 100 Ft névértékű értékpapír visszaváltási árfolyama!az évtöredék számításának alapja"}, "DOLLARDE": {"a": "(tört_ért<PERSON>k; tört_nevez<PERSON>)", "d": "Közönséges törtként megadott számot tizedes törtté alakít át.", "ad": "a törtszám!a tört nevezőjében használt egész sz<PERSON>m"}, "DOLLARFR": {"a": "(tized<PERSON>_<PERSON>rt<PERSON>k; tört_nevez<PERSON>)", "d": "Tizedes törtként megadott számot közönséges törtté alakít át.", "ad": "tizedes szám!a tört nevezőjében használt egész szám"}, "DURATION": {"a": "(elsz<PERSON><PERSON><PERSON>ás; le<PERSON><PERSON><PERSON>; r<PERSON><PERSON>; hozam; gyakoris<PERSON>g; [alap])", "d": "Időszakonkénti kamatfizetésű értékpapír éves <PERSON>-kamatlábérzékenységét adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapír éves szelvénykamatlába!az értékpapír éves hozama!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "EFFECT": {"a": "(névleges_kamatláb; időszak_per_év)", "d": "<PERSON>z éves tényleges kamatláb értékét adja eredményül.", "ad": "a névleges kamatláb!a részidőszakok száma évenként"}, "FV": {"a": "(r<PERSON><PERSON>; időszakok_száma; r<PERSON><PERSON><PERSON>; [j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]; [típus])", "d": "Egy befektetés jövőbeli értékét számítja ki, időszakonkénti állandó összegű befizetéseket és állandó kamatlábat véve alapul.", "ad": "az időszakonkénti kamatláb; p<PERSON>ld<PERSON>ul 6%-os <PERSON><PERSON> kamatláb negyedévenkénti fizetéssel 6%/4!a fizetési időszakok száma a befektetési időszakban!a fizetési időszakokban esedékes befizetés, nagysága a befektetési időszak egészében változatlan!a jövőbeli befizetések jelenértéke, vagyis az a jelenbeli egyösszegű befizetés, amely egyenértékű a jövőbeli befizetések összegével; ha elhagyjuk, a jelenérték = 0!a részletfizetések esedékességét megadó érték: ha 1, a részletfizetés az időszak elején történik, ha 0 vagy elhagyjuk, az időszak végén"}, "FVSCHEDULE": {"a": "(tőke; ütemezés)", "d": "A kezdőtőke adott kamatlábak szerint megnövelt jövőbeli értékét adja eredményül.", "ad": "a jelenlegi érték!a kamatlábak tömbje"}, "INTRATE": {"a": "(elsz<PERSON>molás; lej<PERSON>rat; befektetés; visszaváltás; [alap])", "d": "<PERSON><PERSON> le<PERSON> telje<PERSON> lekötött értékpapír ka<PERSON>r<PERSON>áj<PERSON>t adja er<PERSON>.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapírba fektetett összeg!a lejáratkor esedékes összeg!az évtöredék számításának alapja"}, "IPMT": {"a": "(r<PERSON><PERSON>; id<PERSON><PERSON><PERSON>; id<PERSON>szakok_száma; j<PERSON><PERSON><PERSON><PERSON>; [jö<PERSON><PERSON><PERSON><PERSON>_érték]; [típus])", "d": "Egy befektetés részletfizetési összegeinek nagyságát számítja ki egy adott időszakra időszakonkénti, állandó összegű részletek és állandó kamatl<PERSON>.", "ad": "az időszakonk<PERSON><PERSON> ka<PERSON>, p<PERSON>ld<PERSON><PERSON> 6%-os éves kamat és negyedévenkénti fizetés esetén 6%/4!annak az időszaknak a sz<PERSON><PERSON>, amely<PERSON> a részletfizetést ki kell szám<PERSON>tani; 1 és az időszakok_száma közé kell esnie!a fizetési időszakok száma a befektetés időtartama alatt!a jövőbeli befizetések jelenértéke, vagyis az a jelenbeli egyösszegű befizetés, amely egyenértékű a jövőbeli befizetések összegével!a jövőbeli érték vagy az utolsó részlet befizetése után elérni kívánt összeg; ha elhagyjuk, a jövőbeli_érték = 0!a részletfizetések esedékessége: ha 1, a részletfizetés az időszak elején történik; ha 0 vagy elhagyjuk, akkor az időszak végén"}, "IRR": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; [be<PERSON><PERSON><PERSON>])", "d": "A megadott pénzáramlás-számsor (cash flow) be<PERSON><PERSON> megtérülési rátáját számítja ki.", "ad": "egy tömb vagy egy, a pénzáramlás azon értékeit tartalmazó cellákra való hivatkozás, amelyekre a bels<PERSON> megtérülési rátát ki kell s<PERSON>ámítani!egy olyan s<PERSON>, amely várhatóan közel esik az eredményhez; ha elhagyjuk, 0,1 (10 százalék)."}, "ISPMT": {"a": "(r<PERSON><PERSON>; id<PERSON>sz<PERSON>; időszakok_száma; jelenérték)", "d": "A befektetés időtartamának adott időszakára eső kamatösszeget számítja ki.", "ad": "időszakonkénti kamatlá<PERSON>; például 6% éves kamatlá<PERSON> negyedévenkénti fizetéssel 6%/4!az az id<PERSON>sz<PERSON>, amelyre a kamatot ki kell számítani!a fizetési időszakok száma a befektetés időtartama alatt!a jelenérték nagysága, vagyis az a jelenbeli egyösszegű befizetés, amely egyenértékű a jövőbeli befizetések összegével"}, "MDURATION": {"a": "(elsz<PERSON><PERSON><PERSON>ás; le<PERSON><PERSON><PERSON>; r<PERSON><PERSON>; hozam; gyakoris<PERSON>g; [alap])", "d": "Egy 100 Ft névértékű értékpapír <PERSON>-féle módosított árfolyam-kamatlábérzékenységét adja eredmé<PERSON>ül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapír éves szelvénykamatlába!az értékpapír éves hozama!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "MIRR": {"a": "(ért<PERSON><PERSON>k; pénzü<PERSON><PERSON>_kamat; újrabefektetési_ráta)", "d": "A befektetés belső megtérülési rátáját számítja ki ismétlődő pénzáramlások esetén a befektetés költségét és az újrabefektetett összegek után jár<PERSON> kamatot is figyelembe véve.", "ad": "egy számokat tartalmazó tömb vagy cell<PERSON>; az elemek negatív előjel esetén befizetést, pozitív előjel esetén bevételt jelentenek szabályos időközönként!a pénzáramlásban részt vevő összegekre kifizetett kamat!az újra befektetett összegek után kapott kamat"}, "NOMINAL": {"a": "(tényleges_kamatláb; időszak_per_év)", "d": "<PERSON>z éves névleges kamatláb értékét adja eredményül.", "ad": "a tényleges kamatláb!a részidőszakok száma évenként"}, "NPER": {"a": "(r<PERSON><PERSON>; r<PERSON><PERSON><PERSON>; j<PERSON><PERSON><PERSON><PERSON><PERSON>; [jö<PERSON><PERSON><PERSON><PERSON>_<PERSON>]; [típus])", "d": "A befektetési időszakok számát adja meg időszakonkénti, állandó összegű részletfizetések és állandó ka<PERSON>.", "ad": "az időszakonkénti kamatláb; p<PERSON>ld<PERSON>ul 6%-os <PERSON><PERSON> kamatláb negyedévenkénti fizetéssel 6%/4!a fizetési időszakokban esedékes befizetés; nagysága a befektetés időtartama alatt változatlan!a jövőbeli befizetések jelenértéke, vagyis az a jelenbeli egyösszegű befizetés, amely egyenértékű a jövőbeli befizetések összegével!a jövőbeli érték vagy az utolsó részlet befizetése után elérni kívánt összeg; ha elhagyjuk, a jövőbeli_érték = 0!logikai érték: ha 1, a részletfizetés az időszak elején történik; ha 0 vagy elhagyjuk, akkor az időszak végén"}, "NPV": {"a": "(r<PERSON>ta; érték1; [érték2]; ...)", "d": "Egy befektetés nettó jelenértékét számítja ki ismert diszkontráta és jövőbeli befizetések (negatív értékek), illetve bev<PERSON> (pozitív értékek) mellett.", "ad": "az egy időszakra érvényes diszkontráta!a jövőbeli bevételek és befizetések (számuk 1 és 254 között lehet), melyek egyenlő időközönként, az időszakok végén történnek"}, "ODDFPRICE": {"a": "(elszámolás; lej<PERSON>rat; kibocsátás; el<PERSON><PERSON>_szelvény; ráta; hozam; visszaváltás; gyakoriság; [alap])", "d": "Egy 100 Ft névértékű, a futamidő elején töredékidőszakos értékpapír árát adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapír kibocsátásának dátuma dátumértékként kifejezve!az értékpapír első szelvénykifizetésének dátuma dátumértékként kifejezve!az értékpapír kamatlába!az értékpapír éves hozama!a 100 Ft névértékű értékpapír visszaváltási árfolyama!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "ODDFYIELD": {"a": "(elsz<PERSON>molás; lej<PERSON>rat; kibocsátás; el<PERSON><PERSON>_szelvény; r<PERSON><PERSON>; ár; visszav<PERSON>ltás; gyakoriság; [alap])", "d": "<PERSON><PERSON>, a futamidő elején töredékidőszakos értékpapír ho<PERSON>át adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapír kibocsátásának dátuma dátumértékként kifejezve!az értékpapír első szelvénykifizetésének dátuma dátumértékként kifejezve!az értékpapír kamatlába!az értékpapír ára!a 100 Ft névértékű értékpapír visszaváltási árfolyama!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "ODDLPRICE": {"a": "(elsz<PERSON><PERSON>l<PERSON>; le<PERSON><PERSON><PERSON>; uto<PERSON><PERSON>_kamat; ráta; hozam; visszaváltás; gyakoriság; [alap])", "d": "Egy 100 Ft névértékű, a futamidő végén töredékidőszakos értékpapír árát adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapír utolsó szelvénykifizetésének dátuma dátumértékként kifejezve!az értékpapír kamatlába!az értékpapír éves hozama!a 100 Ft névértékű értékpapír visszaváltási árfolyama!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "ODDLYIELD": {"a": "(elsz<PERSON><PERSON><PERSON><PERSON>; le<PERSON><PERSON><PERSON>; uto<PERSON><PERSON>_kamat; ráta; ár; visszaváltás; gyakoriság; [alap])", "d": "<PERSON><PERSON>, a futamidő végén töredékidőszakos értékpapír hozamát adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapír utolsó szelvénykifiztésének dátuma dátumértékként kifejezve!az értékpapír kamatlába!az értékpapír ára!a 100 Ft névértékű értékpapír visszaváltási árfolyama!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "PDURATION": {"a": "(<PERSON><PERSON><PERSON>; j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Kiszámítja az ahhoz szükséges időszakok számát, hogy egy befektetés elérjen egy megadott értéket.", "ad": "az időszakonkénti kamatláb!a befektetés jelenértéke!a befektetéssel elérni kívánt jövőérték"}, "PMT": {"a": "(r<PERSON><PERSON>; időszakok_száma; j<PERSON><PERSON><PERSON><PERSON><PERSON>; [jö<PERSON><PERSON><PERSON><PERSON>_<PERSON>ék]; [típus])", "d": "A kölcsönre vonatkozó törlesztőösszeget számítja ki állandó nagyságú törlesztőrészletek és állandó kamatl<PERSON>.", "ad": "az időszakonkénti kamatláb a kölcsön időtartama alatt; például 6% <PERSON>ves kamatláb negyedévenkénti fizetéssel 6%/4!a kölcsön törlesztőrészleteinek száma!a jövőbeli befizetések jelenértéke, vagyis az a jelenbeli egyösszegű befizetés, amely egyenértékű a jövőbeli befizetések összegével!a jövőbeli érték vagy az utolsó részlet befizetése után elérni kívánt összeg; ha elhagyjuk, a jövőbeli_érték = 0!logikai érték: ha 1, a részletfizetés az időszak elején történik; ha 0 vagy elhagyjuk, akkor az időszak végén"}, "PPMT": {"a": "(r<PERSON><PERSON>; id<PERSON><PERSON><PERSON>; id<PERSON>szakok_száma; j<PERSON><PERSON><PERSON><PERSON>; [jö<PERSON><PERSON><PERSON><PERSON>_érték]; [típus])", "d": "Egy befektetés tőketörlesztésének nagyságát számítja ki egy adott időszakra időszakonkénti, állandó összegű részletfizetések és állandó kamatl<PERSON>.", "ad": "az időszakonkénti kamatláb; például 6%-os éves kamat és negyedévenkénti fizetés esetén 6%/4!megadja az időszakot, értékének 1 és az időszakok_száma közé kell esnie!a fizetési időszakok száma a befektetés időtartama alatt!a jövőbeli befizetések jelenértéke, vagyis az a jelenbeli egyösszegű befizetés, amely egyenértékű a jövőbeli befizetések összegével!a jövőbeli érték vagy az utolsó részlet befizetése után elérni kívánt összeg!logikai érték: ha 1, a részletfizetés az időszak elején történik; ha 0 vagy elhagyjuk, akkor az időszak végén"}, "PRICE": {"a": "(elsz<PERSON>molás; lej<PERSON><PERSON>; r<PERSON><PERSON>; hozam; visszavált<PERSON>; gyakoriság; [alap])", "d": "Egy 100 Ft névértékű, időszakosan kamatozó értékpapír árát adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapír éves szelvénykamatlába!az értékpapír éves hozama!a 100 Ft névértékű értékpapír visszaváltási árfolyama!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "PRICEDISC": {"a": "(elszámolás; lej<PERSON>rat; leszámítolás; visszaváltás; [alap])", "d": "Egy 100 Ft névértékű leszámítolt értékpapír árát adja eredményül.", "ad": "az értékpapír kifizetésének időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapír leszámítolási rátája!a 100 Ft névértékű értékpapír visszaváltási árfolyama!az évtöredék számításának alapja"}, "PRICEMAT": {"a": "(elszámolás; lej<PERSON>rat; kibocsátás; ráta; hozam; [alap])", "d": "Egy 100 Ft névértékű, a lejáratkor kamatozó értékpapír árát adja eredmé<PERSON>ül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapír kibocsátásának dátuma dátumértékként kifejezve!az értékpapír kamatrátája a kibocsátás időpontjában!az értékpapír éves hozama!az évtöredék számításának alapja"}, "PV": {"a": "(r<PERSON><PERSON>; időszakok_száma; r<PERSON><PERSON><PERSON>; [jö<PERSON><PERSON><PERSON><PERSON>_<PERSON>rték]; [típus])", "d": "Egy befektetés jelenértékét számítja ki: azt a jelenbeli egyösszegű befizetést, amely egyenértékű a jövőbeli befizetések összegével.", "ad": "az időszakonkénti kamatláb; p<PERSON>ld<PERSON>ul 6%-os <PERSON><PERSON> kamatláb negyedévenkénti fizetéssel 6%/4!a fizetési időszakok száma a befektetési időszakban!a fizetési időszakokban esedékes befizetés; nagysága a befektetés időtartama alatt változatlan!a jövőbeli érték vagy az utolsó részlet befizetése után elérni kívánt összeg!logikai érték: ha 1, a részletfizetés az időszak elején történik; ha 0 vagy elhagyjuk, az időszak végén"}, "RATE": {"a": "(időszakok_száma; r<PERSON><PERSON><PERSON>; j<PERSON><PERSON><PERSON>rt<PERSON>k; [jö<PERSON><PERSON><PERSON>i_érték]; [típus]; [be<PERSON><PERSON><PERSON>])", "d": "Egy kölcsön vagy befektetés időtartama alatt az egy időszakra eső kamatláb nagyságát számítja ki. Például 6%-os <PERSON><PERSON> kamatláb negyedévenkénti fizetéssel 6%/4.", "ad": "a fizetési időszakok száma a kölcsön vagy a befektetés időtartama alatt!a fizetési időszakokban esedékes befizetés; nagysága a kölcsön vagy a befektetés időtartama alatt változatlan!a jövőbeli befizetések jelenértéke, vagyis az a jelenbeli egyösszegű befizetés, amely egyenértékű a jövőbeli befizetések összegével!a jövőbeli érték vagy az utolsó részlet befizetése után elérni kívánt összeg. Ha elhagyjuk, a jövőbeli_érték = 0!logikai érték: ha 1, a részletfizetés az időszak elején történik; ha 0 vagy elhagyjuk, akkor az időszak végén!az Ön becslése a kamatláb nagyságára; ha elhagyjuk a becslés 0,1 (10 százalék) lesz"}, "RECEIVED": {"a": "(elszámolás; lej<PERSON>rat; befektetés; leszámítolás; [alap])", "d": "Egy lejá<PERSON>ig teljesen lekötött értékpapír lejáratakor kapott összeget adja eredményül.", "ad": "az értékpapír kifizetésének időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapírba fektetett összeg!az értékpapír leszámítolási rátája!az évtöredék számításának alapja"}, "RRI": {"a": "(időszakok_száma; jelen<PERSON>rt<PERSON>k; jövőérték)", "d": "Kiszámít egy befektetés növekedésével egyenértékű kamatlábat.", "ad": "a befektetési időszakok száma!a befektetés jelenértéke!a befektetés jövőbeli értéke"}, "SLN": {"a": "(költség; marad<PERSON>k; leír<PERSON>i_idő)", "d": "Egy tárgyi eszköz egy időszakra eső amortizációját adja meg, bruttó érték szerinti lineáris leírási kulcsot alkalmazva.", "ad": "az eszköz beszerzési ára!az eszköz maradványértéke a leírási idő eltelte után!a leírási időszakok teljes száma (azaz az eszköz hasznos élettartama)"}, "SYD": {"a": "(költség; maradván<PERSON>k; le<PERSON><PERSON><PERSON><PERSON>_id<PERSON>; id<PERSON>szak)", "d": "Egy tárgyi eszköz értékcsökkenését számítja ki adott időszakra az évek számjegyösszegével dolgozó módszer alapján.", "ad": "az eszköz beszerzési ára!az eszköz maradványértéke a leírási idő eltelte után!a leírási időszakok teljes száma (azaz az eszköz hasznos élettartama)!az az időszak, amelyre az értékcsökkenés mértékét ki kell számítani; ugyanazt a mértékegységet kell használni, mint az élettartam megadásánál"}, "TBILLEQ": {"a": "(kiegy<PERSON><PERSON><PERSON><PERSON><PERSON>; le<PERSON><PERSON><PERSON>; leszámítolás)", "d": "Egy kincstárjegy kötvény-egyenértékű hozamát adja eredményül.", "ad": "a kincstárjegy kifizetésének időpontja dátumértékként kifejezve!a kincstárjegy lejáratának időpontja dátumértékként kifejezve!a kincstárjegy leszámítolási rátája"}, "TBILLPRICE": {"a": "(kiegy<PERSON><PERSON><PERSON><PERSON><PERSON>; le<PERSON><PERSON><PERSON>; leszámítolás)", "d": "Egy 100 Ft névértékű kincstárjegy árát adja eredményül.", "ad": "a kincstárjegy kifizetésének időpontja dátumértékként kifejezve!a kincstárjegy lejáratának időpontja dátumértékként kifejezve!a kincstárjegy leszámítolási rátája"}, "TBILLYIELD": {"a": "(k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; le<PERSON><PERSON><PERSON>; <PERSON>r)", "d": "<PERSON><PERSON> kin<PERSON>tárjegy hozamát adja eredményül.", "ad": "a kincstárjegy kifizetésének időpontja dátumértékként kifejezve!a kincstárjegy lejáratának időpontja dátumértékként kifejezve!egy 100 Ft névértékű kincstárjegy ára"}, "VDB": {"a": "(költség; marad<PERSON>k; <PERSON><PERSON><PERSON><PERSON><PERSON>_idő; kez<PERSON><PERSON>_időszak; <PERSON><PERSON><PERSON><PERSON>_időszak; [faktor]; [nem_vált])", "d": "Egy tárgyi eszköz amortizációját számítja ki egy megadott vagy részidőszakra a dupla gyorsaságú csökkenő egyenleg módszerének, vagy más megadott módszernek az alkalmazásával.", "ad": "az eszköz beszerzési ára!az eszköz maradványértéke a leírási idő eltelte után!a leírási időszakok teljes száma (azaz az eszköz hasznos élettartama)!az első leírási időszak; ugyanazt a mértékegységet kell használni, mint az élettartam megadásánál!az utolsó leírási időszak; ugyanazt a mértékegységet kell használni, mint az élettartam megadásánál!a leírás gyorsasága; ha elhagyjuk, 2 (dupla gyorsaságú)!logikai érték. Ha HAMIS vagy elhagyjuk: át kell térni a lineáris leírási modellre, ha az nagyobb leírandó értéket adna, mint a csökkenő leírási módszer. Ha IGAZ, nem kell áttérni."}, "XIRR": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; d<PERSON><PERSON><PERSON>; [be<PERSON><PERSON><PERSON>])", "d": "Ütemezett pénzáramlás (cash flow) belső megtérülési rátáját adja eredményül.", "ad": "az egyes fizetési dátumokhoz tartozó be- vagy kifizetések!az egyes be- vagy kifizetésekhez tartozó dátumok!az XIRR függvény eredményének becsült értéke"}, "XNPV": {"a": "(r<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; d<PERSON><PERSON><PERSON>)", "d": "Ütemezett pénzáramlás (cash flow) nettó jelenértékét adja eredményül.", "ad": "a be- vagy kifizetéseknél alkalmazott diszkontráta!az egyes fizetési dátumokhoz tartozó be- vagy kifizetések!az egyes be- vagy kifizetésekhez tartozó dátumok"}, "YIELD": {"a": "(elsz<PERSON><PERSON>lás; le<PERSON><PERSON><PERSON>; r<PERSON><PERSON>; <PERSON>r; v<PERSON><PERSON>v<PERSON>; gyakoriság; [alap])", "d": "Időszakosan kamatozó értékpapír ho<PERSON>t adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapír éves szelvénykamatlába!a 100 Ft névértékű értékpapír ára!a 100 Ft névértékű értékpapír visszaváltási árfolyama!a szelvénykifizetések száma egy év alatt!az évtöredék számításának alapja"}, "YIELDDISC": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; le<PERSON><PERSON><PERSON>; <PERSON>r; v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; [alap])", "d": "<PERSON><PERSON> leszámítolt értékpapír (péld<PERSON>ul kincstárjegy) <PERSON><PERSON> ho<PERSON> adja <PERSON>ül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!a 100 Ft névértékű értékpapír ára!a 100 Ft névértékű értékpapír visszaváltási árfolyama!az évtöredék számításának alapja"}, "YIELDMAT": {"a": "(elsz<PERSON>molás; lej<PERSON>rat; kibocsátás; r<PERSON><PERSON>; ár; [alap])", "d": "Lejáratkor kamatozó értékpapír éves <PERSON> adja eredményül.", "ad": "az értékpapír elszámolásának időpontja dátumértékként kifejezve!az értékpapír lejáratának időpontja dátumértékként kifejezve!az értékpapír kibocsátásának dátuma dátumértékként kifejezve!az értékpapír kamatrátája a kibocsátás időpontjában!a 100 Ft névértékű értékpapír ára!az évtöredék számításának alapja"}, "ABS": {"a": "(szám)", "d": "Egy szám abszolút értékét adja eredményül (a számot előjel nélkül)", "ad": "az a valós <PERSON>, amelynek az abszolút értékére kíváncsiak vagyunk"}, "ACOS": {"a": "(szám)", "d": "Egy szám arkusz koszinuszát adja meg radiánban, a 0 - Pi tartományban. Az arkusz koszinusz az a szög, amelynek a koszinusza a megadott szám", "ad": "a keresett szög koszinusza; -1 és +1 közé kell esnie"}, "ACOSH": {"a": "(szám)", "d": "Egy szám area koszinusz hiperbolikusát számítja ki.", "ad": "tetszőleges 1-nél nem kisebb valós sz<PERSON>m"}, "ACOT": {"a": "(szám)", "d": "Egy szám arkusz kotangensét adja meg radiánban, a 0–Pi tartományban", "ad": "a keresett szög kotangense"}, "ACOTH": {"a": "(szám)", "d": "Egy s<PERSON>m inverz hiperbolikus kotangensét adja meg", "ad": "a keresett szög hiperbolikus kotangense"}, "AGGREGATE": {"a": "(függv_szám; beállítások; hiv1; ...)", "d": "Összegzést ad eredményül listában vagy adatbázi<PERSON>ban", "ad": "1 és 19 köz<PERSON><PERSON> sz<PERSON>, amely az összegzéshez használt összegző függvényt határozza meg.!egy 0 és 7 közötti szám, amely megadja az összegzéskor figyelmen kívül hagyandó értékeket!számadatokat tartalmazó tömb vagy tartomány, amelynek az összegét ki kell számítani!elhelyezkedés a tömbben; a k-adik legnagyobb, a k-adik legkisebb, a k-adik percentilis vagy a k-adik kvartilis.!1 és 19 közötti szám, amely az összegzéshez használt összegző függvényt határozza meg.!egy 0 és 7 közötti szám, amely megadja az összegzéskor figyelmen kívül hagyandó értékeket!1 és 253 közötti tartomány vagy hi<PERSON>, amely<PERSON> az összeget ki kell sz<PERSON>tani"}, "ARABIC": {"a": "(szöveg)", "d": "Római sz<PERSON>mot arab számmá alakí<PERSON>t", "ad": "az átalakítandó római szám"}, "ASC": {"a": "(szöveg)", "d": "Ez a függvény a k<PERSON>tb<PERSON><PERSON><PERSON> (DBCS) karakterkészletet használó nyelveknél egybájtos karakterekre cseréli a kétbájtos karaktereket", "ad": "Az a szöveg vagy azt a szöveget tartalmazó cellára mutató hi<PERSON>, amelyet módosítani kíván."}, "ASIN": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON> a<PERSON> s<PERSON> adja meg radi<PERSON>, a -Pi/2 - Pi/2 tartományban", "ad": "a kere<PERSON> szög s<PERSON>; -1 és +1 közé kell esnie"}, "ASINH": {"a": "(szám)", "d": "Egy szám area szinusz hiperbolikusát számítja ki.", "ad": "tetszőleges 1-nél nem kisebb valós sz<PERSON>m"}, "ATAN": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON> ark<PERSON> tan<PERSON>t adja meg radi<PERSON>, a -Pi/2 -Pi/2 tartományban", "ad": "a keresett szög tangense"}, "ATAN2": {"a": "(x_szám; y_szám)", "d": "A megadott x- és y-koordináták alapján számítja ki az arkusz tangens értéket radiánban -Pi és Pi között, -<PERSON> kivételével", "ad": "a pont x-koordinátája!a pont y-koordinátája"}, "ATANH": {"a": "(szám)", "d": "A szám tangens hiperbolikusát számítja ki.", "ad": "tetszőleges 1-nél nem kisebb valós sz<PERSON>m"}, "BASE": {"a": "(szám; alap; [min_hossz])", "d": "Átalakít egy számot a megadott alapú (számrendszerű) szöveges alakra.", "ad": "az átalakítandó szám!az átalakítás után kapott szám al<PERSON>száma!a visszaadott karakterlánc minimális ho<PERSON>. Ha nincs megadva, a bevezető nullák kimaradnak"}, "CEILING": {"a": "(szám; pontosság)", "d": "Egy számot a pontosságként megadott érték legközelebb eső többszörösére kerekít fel.", "ad": "a kerekítendő szám!az a szám, amelynek legközelebbi többszörösére kerekíteni kell"}, "CEILING.MATH": {"a": "(szám; [pontosság]; [mód])", "d": "Egy számot a legközelebbi egészre vagy a pontosságként megadott érték legközelebb eső többszörösére kerekít fel.", "ad": "a kerekítendő szám!az a szám, amelynek legközelebbi többszörösére kerekíteni kell!nem nulla érték megadása esetén a függvény nullától elkerekít"}, "CEILING.PRECISE": {"a": "(szám; [pontosság])", "d": "Egy számot a legközelebbi egészre vagy a pontosságként megadott érték legközelebb eső többszörösére kerekít", "ad": "a kerekítendő szám!az a szám, amelynek legközelebbi többszörösére kerekíteni kell"}, "COMBIN": {"a": "(szám; hány_kiválasztott)", "d": "Adott számú elem összes lehetséges kombinációinak számát számítja ki", "ad": "az összes elem száma!az egyes kombinációkban szereplő elemek száma"}, "COMBINA": {"a": "(szám; hány_kiválasztott)", "d": "Adott számú elem összes lehetséges ismétléses kombinációinak számát számítja ki", "ad": "az összes elem száma!az egyes kombinációkban szereplő elemek száma"}, "COS": {"a": "(szám)", "d": "<PERSON><PERSON> szög k<PERSON>zinuszát számítja ki.", "ad": "az a radiánban megadott s<PERSON>, amelynek koszinusz<PERSON> keresi"}, "COSH": {"a": "(szám)", "d": "Egy sz<PERSON>m koszinusz hiperbolikusát számítja ki.", "ad": "tetszőleges valós szám"}, "COT": {"a": "(szám)", "d": "<PERSON><PERSON> szög kotangensét számítja ki", "ad": "az a radiánban megadott s<PERSON>g, amelynek a kotangensét keresi"}, "COTH": {"a": "(szám)", "d": "Egy szám hiperbolikus kotangensét számítja ki", "ad": "az a radiánban megado<PERSON>, am<PERSON><PERSON><PERSON> a hiperbolikus kotangensét keresi"}, "CSC": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON>ög k<PERSON>zekánsát számítja ki", "ad": "az a radiánban megadott s<PERSON>g, amelynek a koszekánsát keresi"}, "CSCH": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON><PERSON>g hiperbolikus koszekánsát számítja ki", "ad": "az a radiánban megado<PERSON>, am<PERSON><PERSON><PERSON> a hiperbolikus koszekáns<PERSON>t keresi"}, "DECIMAL": {"a": "(szám; alap)", "d": "Egy szám megadott számrendszerbeli szöveges alakját decimális számmá alakítja.", "ad": "az átalakítandó szám!az átalakítandó szám alapszáma"}, "DEGREES": {"a": "(szög)", "d": "Radiánt fokká alakít <PERSON>t", "ad": "az a radiánban megado<PERSON>, am<PERSON><PERSON> foko<PERSON> kell <PERSON>z<PERSON>tani"}, "ECMA.CEILING": {"a": "(szám; pontosság)", "d": "Egy számot a pontosságként megadott érték legközelebb eső többszörösére kerekít fel", "ad": "a kerekítendő szám!az a szám, amelynek legközelebbi többszörösére kerekíteni kell"}, "EVEN": {"a": "(szám)", "d": "Egy pozitív számot felfel<PERSON>, egy negatív számot pedig lefelé kerekít a legközelebbi páros egész számra", "ad": "a kerekítendő szám"}, "EXP": {"a": "(szám)", "d": "e-nek adott kitevőjű hatványát számítja ki.", "ad": "az e alap kitevője. Az e konstans értéke 2.718281182845904, ez a természetes logaritmus alapja."}, "FACT": {"a": "(szám)", "d": "Egy szám faktoriálisát számítja ki. A szám faktoriálisa = 1*2*3*...*szám.", "ad": "az a nemnegatív szám, amelynek faktoriálisát ki kell számítani"}, "FACTDOUBLE": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON>m dupla faktoriális<PERSON>t adja eredményül.", "ad": "az az <PERSON>, amelynek dupla faktoriá<PERSON><PERSON><PERSON>"}, "FLOOR": {"a": "(szám; pontosság)", "d": "<PERSON>gy s<PERSON><PERSON><PERSON> le<PERSON> k<PERSON>, a pontosságként megadott érték legközelebb eső többszörösére.", "ad": "a kerekítendő szám!az a szám, amelynek legközelebbi többszörösére kerekíteni kell. A számnak és a pontosságnak azonos előjelűnek kell lennie."}, "FLOOR.PRECISE": {"a": "(szám; [pontosság])", "d": "Egy számot a legközelebbi egészre vagy a pontosságként megadott érték legközelebb eső többszörösére lefe<PERSON> kerek<PERSON>t", "ad": "a kerekítendő szám!az a szám, amelynek legközelebbi többszörösére kerekíteni kell"}, "FLOOR.MATH": {"a": "(szám; [pontosság]; [mód])", "d": "Egy számot a legközelebbi egészre vagy a pontosságként megadott érték legközelebb eső többszörösére kerekít le.", "ad": "a kerekítendő szám!az a szám, amelynek legközelebbi többszörösére kerekíteni kell!nem nulla érték megadása esetén a függvény nulla felé kerekít"}, "GCD": {"a": "(szám1; [szám2]; ...)", "d": "A legnagyobb közös osztót számítja ki", "ad": "1–255 érték"}, "INT": {"a": "(szám)", "d": "Egy szá<PERSON> le<PERSON> kerekít a legközelebbi egészre.", "ad": "a kerekítendő szám"}, "ISO.CEILING": {"a": "(szám; [pontosság])", "d": "Egy számot a legközelebbi egészre vagy a pontosságként megadott érték legközelebb eső többszörösére kerekít. A szám előjelétől függetlenül a számot felfelé kerekíti. Ugyanakkor ha a szám vagy a pontosságként megadott érték nulla, a függvény nullát ad vissza.", "ad": "a kerekítendő szám!az a szám, amelynek legközelebbi többszörösére kerekíteni kell"}, "LCM": {"a": "(szám1; [szám2]; ...)", "d": "A legkisebb közös többszöröst számítja ki", "ad": "az az 1–255 <PERSON><PERSON><PERSON><PERSON>, amelynek legkisebb közös többszörösét ki kell s<PERSON>ámítani"}, "LN": {"a": "(szám)", "d": "Egy sz<PERSON>m természetes logaritmusát számítja ki.", "ad": "az a pozitív valós s<PERSON>, amelynek természetes logaritmusát ki kell számítani"}, "LOG": {"a": "(szám; [alap])", "d": "<PERSON><PERSON> szám megadott alapú logaritmusát számítja ki.", "ad": "az a pozitív val<PERSON> s<PERSON>, amelynek logaritmusát ki kell s<PERSON>mítani!a logaritmus alapja; ha elhagyjuk, értéke 10 lesz."}, "LOG10": {"a": "(szám)", "d": "Egy szám 10-es alapú logaritmusát számítja ki.", "ad": "az a pozitív valós s<PERSON>, amelynek 10-es alapú logaritmusát ki kell számítani"}, "MDETERM": {"a": "(tömb)", "d": "<PERSON><PERSON> tömb mátrix-determinánsát számítja ki.", "ad": "egy csak számokat tartalmazó négyze<PERSON> (ugyanannyi sorból és oszlopból álló) tömb; cellatartomány vagy tömbkonstans lehet."}, "MINVERSE": {"a": "(tömb)", "d": "<PERSON>gy tömbben tárolt mátrix inverz mátrixát adja er<PERSON>ül.", "ad": "egy csak számokat tartalmazó négyze<PERSON> (ugyanannyi sorból és oszlopból álló) tömb; cellatartomány vagy tömbkonstans lehet."}, "MMULT": {"a": "(tömb1; tömb2)", "d": "Két tömb mátrix-szorzatát adja meg. Az eredménytömbnek ugyanannyi sora lesz, mint tömb1-nek és ugyanannyi oszlopa, mint tömb2-nek.", "ad": "a szorzásban szerepl<PERSON> els<PERSON> számtömb, amelynek ugyanannyi oszlopa kell, hogy legyen, mint ahány sora tömb2-nek van"}, "MOD": {"a": "(szám; osztó)", "d": "A számnak az osztóval való elosztása után kapott maradékát adja eredményül.", "ad": "az a sz<PERSON>m, melynek az osztás elvégzése utáni maradékát ki kell számítani!az a szám, am<PERSON>el a szám argumentumot el kell osztani"}, "MROUND": {"a": "(szám; pontosság)", "d": "A pontosság legközelebbi többszörösére kerekített értéket ad eredményül.", "ad": "a kerekítendő érték!az a szám, amelynek a többszörösére az értéket kerekíteni kell"}, "MULTINOMIAL": {"a": "(szám1; [szám2]; ...)", "d": "Egy számkészlet polinomját számítja ki", "ad": "az az 1–255 <PERSON><PERSON><PERSON><PERSON>, amely polin<PERSON><PERSON><PERSON> ki kell s<PERSON><PERSON>"}, "MUNIT": {"a": "(dimenzió)", "d": "A megadott dimenziójú egységmátrixot adja vissza.", "ad": "a visszaadandó egységmátrix dimenzióját megadó egész szám"}, "ODD": {"a": "(szám)", "d": "Egy pozitív számot felfel<PERSON>, egy negatív számot pedig lefelé kerekít a legközelebbi páratlan egész számra", "ad": "a kerekítendő szám"}, "PI": {"a": "()", "d": "A pi értékét adja vissza 15 jegy pontossággal (3,14159265358979).", "ad": ""}, "POWER": {"a": "(szám; kitevő)", "d": "<PERSON><PERSON> sz<PERSON>m adott kitevőjű hatványát számítja ki.", "ad": "az alap (szám), b<PERSON><PERSON><PERSON><PERSON> valós szám lehet!a kitev<PERSON> (amelyre az alapot emelni kell)"}, "PRODUCT": {"a": "(szám1; [szám2]; ...)", "d": "Az összes argumentumként megadott szám szorzatát számítja ki", "ad": "az összeszorzan<PERSON><PERSON>, logikai értékek vagy szöveges formában megadott számok; számuk 1 és 255 között lehet"}, "QUOTIENT": {"a": "(számláló; nevező)", "d": "<PERSON><PERSON> hán<PERSON>dos egész részét adja eredményül.", "ad": "az osztandó!az osztó"}, "RADIANS": {"a": "(szög)", "d": "Fokot radiánná alakít á<PERSON>.", "ad": "az átalakítandó szög értéke fokokban"}, "RAND": {"a": "()", "d": "0-n<PERSON>l nagyobb vagy azzal egyenlő és 1-nél kisebb egyenletesen elosztott véletlenszámot ad eredményül (az újraszámítástól függően).", "ad": ""}, "RANDARRAY": {"a": "([sorok]; [oszlopok]; [min]; [max]; [egész])", "d": "Véletlenszám tömböt ad eredményül", "ad": "az eredményként kapott tömb sorainak számát!az eredményként kapott tömb oszlopainak száma!a lehető legkevesebb milyen vissza!legfeljebb milyen vissza!egész vagy decimális értékét adja vissza. IGAZ, az e<PERSON><PERSON> s<PERSON>, deci<PERSON><PERSON><PERSON> s<PERSON> hamis"}, "RANDBETWEEN": {"a": "(alsó; felső)", "d": "<PERSON><PERSON><PERSON> adott szám közé eső véletlen számot <PERSON>.", "ad": "az al<PERSON><PERSON>, egész értékű határérték!a felső, egész értékű határérték"}, "ROMAN": {"a": "(szám; [alak])", "d": "Egy arab számot szövegként római számra alakí<PERSON> át", "ad": "az átalakítandó arab szám!a római szám kívánt típusát megadó szám."}, "ROUND": {"a": "(szám; hány_számjegy)", "d": "<PERSON><PERSON> sz<PERSON><PERSON> adott szá<PERSON>ú számjegyre kerekít.", "ad": "a kerekítendő szám!azon számjegyek száma, amennyi jegyre kerekíteni kell. Negatív érték esetén a tizedesponttól balra eső részhez kere<PERSON>t; zérus esetén a legközelebbi egészre"}, "ROUNDDOWN": {"a": "(szám; hány_számjegy)", "d": "<PERSON><PERSON> s<PERSON><PERSON><PERSON>, a nulla felé kere<PERSON>t.", "ad": "a kerekítendő valós szám!azon számjegyek száma, amennyi jegyre kerekíteni kell. Negatív érték esetén a tizedesponttól balra eső részhez kerekít; zérus vagy elhagyása esetén a legközelebbi egészre"}, "ROUNDUP": {"a": "(szám; hány_számjegy)", "d": "<PERSON><PERSON> s<PERSON><PERSON><PERSON>, a nullától távolabbra kerekít.", "ad": "a kerekítendő valós szám!azon számjegyek száma, amennyi jegyre kerekíteni kell. Negatív érték esetén a tizedesponttól balra eső részhez kerekít; zérus vagy elhagyása esetén a legközelebbi egészre"}, "SEC": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON><PERSON>g s<PERSON>án<PERSON>t számítja ki", "ad": "az a radiánban megadott s<PERSON>g, amelynek a szekánsát keresi"}, "SECH": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON><PERSON>g hiperbolikus szekánsát számítja ki", "ad": "az a radiánban megado<PERSON> s<PERSON>, am<PERSON><PERSON><PERSON> a hiperbolikus szekán<PERSON><PERSON><PERSON> keresi"}, "SERIESSUM": {"a": "(x; n; m; k<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Hatványsor összegét adja eredményül.", "ad": "az a hely, ahol a hatványsor összegét ki kell s<PERSON>mítani!a kezdő hatványkitevő, amelyre x-et emelni kell!lépésköz, amellyel n értéke tagonként növekszik!az x egyes hatványainak együtthatói"}, "SIGN": {"a": "(szám)", "d": "Egy szám előjelét határozza meg: pozitív szám esetén 1, z<PERSON>rus esetén 0, negatív szám esetén -1.", "ad": "tetszőleges valós szám"}, "SIN": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON>g s<PERSON>t számítja ki.", "ad": "az a radián<PERSON> megado<PERSON>, am<PERSON><PERSON><PERSON> s<PERSON>z<PERSON> ki kell s<PERSON>ámítani. Átszámítás: fok * PI()/180 = radián"}, "SINH": {"a": "(szám)", "d": "<PERSON><PERSON> sz<PERSON>m szin<PERSON>z hiperbolikusát számítja ki.", "ad": "tetszőleges valós szám"}, "SQRT": {"a": "(szám)", "d": "Egy szám négyzetgyökét számítja ki", "ad": "az a <PERSON>, amelynek négyzetgyökét ki kell számítani"}, "SQRTPI": {"a": "(szám)", "d": "A (szám * pi) érték négyzetgyökét adja v<PERSON>za.", "ad": "az a <PERSON>, am<PERSON><PERSON> pi-t meg kell szorozni"}, "SUBTOTAL": {"a": "(függv_szám; hiv1; ...)", "d": "Listában vagy adatbázisban részösszeget ad vissza", "ad": "egy szám 1-től 11-ig, amely a részösszeghez használt összegzési függvényt határozza meg.!azok a tartományok vagy hivat<PERSON>ok (1-től 254-ig), amely<PERSON><PERSON> a részösszeget ki szeretné számítani"}, "SUM": {"a": "(szám1; [szám2]; ...)", "d": "Az összes számot összeadja egy adott cellatartományban.", "ad": "az összeadandó argumentumok, számuk 1 és 255 között lehet. A cellákban lévő logikai értékeket és szövegeket nem veszi fi<PERSON>, az argumentumként beírtakat igen"}, "SUMIF": {"a": "(tartomány; kritérium; [összeg_tartomány])", "d": "A megadott feltételnek vagy kritériumnak eleget tevő cellákban található értékeket adja ö<PERSON>ze.", "ad": "a kiértékelendő cellatartomány!az összeadandó cellákat meghatározó <PERSON>, kifejezésként vagy szövegként megadott feltétel vagy kritérium!a ténylegesen összeadandó cellák. <PERSON> elhagyjuk, a tartomány összes cellája fel lesz használva"}, "SUMIFS": {"a": "(összegtartomány; kritériumtartomány; kritérium; ...)", "d": "A megadott feltétel- vagy kritériumkészletnek eleget tevő cellákban található értékeket adja ö<PERSON>ze", "ad": "az összegzendő cellák.!az adott feltétellel kiértékelni kívánt cellák tartománya!a feltétel vagy kritérium egy az összeadandó cellákat definiáló s<PERSON>, kifejezés vagy szöveg formájában"}, "SUMPRODUCT": {"a": "(tömb1; [tömb2]; [tömb3]; ...)", "d": "Eredményül a megadott tartományok vagy tömbök számelemei szorzatának az összegét adja", "ad": "azok a tömbök (számuk 2 és 255 között lehet), am<PERSON><PERSON> megfelelő elemeit össze kell szorozni, majd a szorzatokat össze kell adni. Minden tömbnek azonos méretűnek kell lennie"}, "SUMSQ": {"a": "(szám1; [szám2]; ...)", "d": "Argumentumai négyzetének összegét számítja ki. Az argumentumok számok, nevek, tömbök vagy számokat tartalmazó hivatkozások lehetnek.", "ad": "azon <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, nevek vagy tömb<PERSON>, amelyekre a négyzetösszeget ki kell számítani; számuk 1 és 255 között lehet."}, "SUMX2MY2": {"a": "(tömb_x; tömb_y)", "d": "Két tartomány vagy tömb megfelelő elemei négyzeteinek a különbségét összegezi", "ad": "a feldolgozandó számok első tartománya vagy tömbje; lehet szám vagy számokat tartalmazó név, tömb vagy hivatkozás!a feldolgozandó értékek második tartománya vagy tömbje; lehet szám vagy számokat tartalmazó név, tömb vagy hivatkozás"}, "SUMX2PY2": {"a": "(tömb_x; tömb_y)", "d": "Két tartomány vagy tömb megfelelő elemei összegének a négyzetösszegét összegezi", "ad": "a feldolgozandó értékek első tartománya vagy tömbje; lehet szám vagy számokat tartalmazó név, tömb vagy hivatkozás!a feldolgozandó értékek második tartománya vagy tömbje; lehet szám vagy számokat tartalmazó név, tömb vagy hivatkozás"}, "SUMXMY2": {"a": "(tömb_x; tömb_y)", "d": "<PERSON><PERSON>t tartomány vagy tömb megfelelő elemei különbségének négyzetösszegét számítja ki", "ad": "a feldolgozandó értékek első tartománya vagy tömbje; lehet szám vagy számokat tartalmazó név, tömb vagy hivatkozás!a feldolgozandó értékek második tartománya vagy tömbje; lehet szám vagy számokat tartalmazó név, tömb vagy hivatkozás"}, "TAN": {"a": "(szám)", "d": "<PERSON><PERSON> szög tangens<PERSON>t számítja ki.", "ad": "az a radián<PERSON> megado<PERSON>, am<PERSON><PERSON><PERSON> tangensét ki kell s<PERSON>ámítani. Átszámítás: fok * PI()/180 = radián"}, "TANH": {"a": "(szám)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> egy szám tangens hiperbolikusát adja v<PERSON>za", "ad": "tetszőleges valós szám"}, "TRUNC": {"a": "(szám; [hány_szám<PERSON><PERSON>])", "d": "Egy számot egé<PERSON><PERSON><PERSON> c<PERSON>, hogy a szám tizedes- vagy tö<PERSON> eltávolítja.", "ad": "a csonkítandó szám!a csonkítás pontosságát megadó számjegyek száma, elhagyása esetén 0 (<PERSON><PERSON><PERSON>)"}, "ADDRESS": {"a": "(sor_szám; oszlop_szám; [típus]; [a1]; [munkalapnév])", "d": "A megadott sor- és oszlopszám alapján cellahivatkozást hoz létre szöveges formában.", "ad": "a cellahivatkozásban használt sor száma: az 1-es sor esetén a sor_szám = 1!a cellahivatkozásban használt oszlop száma, pl. a D oszlop esetén az oszlop_szám = 4!a hivatkozás típusát határozza meg: abszolút = 1; abszolút sor/relatív oszlop = 2; relatív sor/abszolút oszlop = 3; relatív =4!a hivatkozás típusát meghatározó logikai érték: A1 típus = 1 vagy IGAZ; S1O1 típus = 0 vagy HAMIS!a munkalap külső hivatkozásként használható szövegként megadott neve"}, "CHOOSE": {"a": "(index; érték1; [érték2]; ...)", "d": "Értékek egy listájából választ ki egy elemet vagy végrehajtandó <PERSON>, indexszám <PERSON>j<PERSON>", "ad": "a kiválasztott argumentumot határozza meg. Az indexszámnak 1 és 254 közötti számnak vagy 1 és 254 közötti számot adó képletnek vagy hivatkozásnak kell lennie!legfeljebb 254 sz<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, def<PERSON><PERSON><PERSON> név, <PERSON><PERSON><PERSON><PERSON>, függvény vagy szöveges argumentum; a VÁLASZT ezek közül választ"}, "COLUMN": {"a": "([hi<PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON> oszlopszámát adja eredményül.", "ad": "az a cella vagy egymással határos cellákból álló tart<PERSON>, amelynek oszlopszámát meg kívánja kapni. Elhagyása esetén az OSZLOP függvényt tartalmazó cella lesz felhasználva"}, "COLUMNS": {"a": "(tömb)", "d": "Tömbben vagy hivatkozásban található oszlopok számát adja eredményül.", "ad": "az a tömb, tömbképlet vagy cellatartományra való hi<PERSON>, amelyben található oszlopok számát meg kívánja kapni"}, "FORMULATEXT": {"a": "(hivatkozás)", "d": "Egy képletet karakterláncként ad vissza.", "ad": "egy képletre mutató hi<PERSON>"}, "HLOOKUP": {"a": "(keresési_érték; táblázattömb; sorindex; [tartományi_keresés])", "d": "A táblázat vagy értéktömb felső sorában megkeresi a megadott értéket, és a megtalált értékhez tartozó oszlopból a megadott sorban elhelyezkedő értéket adja eredményül.", "ad": "a tábl<PERSON>zat első sorában keresendő érték, amely <PERSON>, hivat<PERSON><PERSON><PERSON> vagy szöveg lehet.!az a szövegekből, számokból vagy logikai értékekből áll<PERSON>, amelyben a keresés történik, és amely lehet tartományra vagy tartománynévre való hivatkozás is.!a táblázattömb azon sorának sz<PERSON>ma, amelyből a megtalált oszlop alapján a függvény az eredményt visszaadja; a táblázat első értéksora az 1-es számú sor.!logikai érték: HAMIS esetén pontos értéket keres; ha IGAZ, vagy elhagy<PERSON>, a felső sorban lévő legközelebbi értéket keresi meg (növekvő sorrend esetén)"}, "HYPERLINK": {"a": "(hivat<PERSON><PERSON><PERSON>_hely; [rö<PERSON>_név])", "d": "<PERSON><PERSON><PERSON>t vagy ugróhivatkozást létesít a merevlemezen, h<PERSON><PERSON><PERSON><PERSON><PERSON> kiszolgálón vagy az interneten tárolt dokumentum megnyitásához.", "ad": "s<PERSON><PERSON><PERSON><PERSON>, mely megadja a megnyitandó dokumentum elérési útvonalát és a fájlnevet; lehet mere<PERSON><PERSON><PERSON><PERSON><PERSON>, UNC cím vagy URL elérési út.!a cellában megjelenő szöveg vagy szám. Ha elhagyja, a cellában a hivatkozott_hely szövege jelenik meg."}, "INDEX": {"a": "(tömb; sor_szám; [osz<PERSON>_szám]!hivat<PERSON><PERSON>ás; sor_szám; [oszlop_szám]; [ter<PERSON><PERSON>_szám])", "d": "Értéket vagy hivatkozást ad vissza egy adott tartomány bizonyos sorának és oszlopának metszéspontjában lévő cellából", "ad": "cellatartomány vagy tömbkonstans.!ki<PERSON><PERSON><PERSON><PERSON> a tömb vagy hivatkozás azon sorát, amelyből az értéket vissza kell adni. Ha elhagyja, az oszlop_szám megadása szükséges!kije<PERSON><PERSON>li a tömb vagy hivatkozás azon o<PERSON>, amelyből az értéket vissza kell adni. Ha elhagyja, a sor_szám megadása szükséges!hivatkozás egy vagy több cellatartományra!kijel<PERSON>li a tömb vagy hivatkozás azon sorát , amelyből az értéket vissza kell adni. Ha elhagyja, az oszlop_szám megadása szükséges!kijelöli a tömb vagy hivatkozás azon oszlopát, amelyből az értéket vissza kell adni. Ha elhagyja, a sor_szám megadása szükséges!a hivatkozásnak azt a tartományát jelöli ki, amelyből az értéket vissza kell küldeni. Az első kijelölt vagy beírt terület az 1-es számú, a második terület a 2-es számú és így tovább"}, "INDIRECT": {"a": "(hiv_szöveg; [a1])", "d": "Szövegdarab által meghatározott hivatkozást ad eredményül.", "ad": "olyan cell<PERSON>ra való hivat<PERSON>, amely egy A1 vagy S1O1 t<PERSON>pus<PERSON> hi<PERSON>, hivat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> definiált nevet vagy pedig szövegként szereplő cellahivatkozást tartalmaz!a hiv_szöveg argumentum által meghatározott hivatkozás típusát megadó logikai érték: S1O1 típus esetén HAMIS, A1 típus esetén IGAZ vagy elhagyható"}, "LOOKUP": {"a": "(keres<PERSON>i_érték; keres<PERSON>i_vektor; [ered<PERSON><PERSON>_vektor]!keresési_érték; tömb)", "d": "Egy sorból vagy egy oszlopból álló tartományban vagy tömbben keres meg értékeket. A korábbi verziókkal való kompatibilitásra szolgál", "ad": "az az <PERSON>rt<PERSON><PERSON>, amelyet a KERES függvény a keresési_vektorban keres; lehet sz<PERSON>, sz<PERSON><PERSON>g, logikai érték vagy értékre hivatkozó név vagy hivatkozás!egyetlen sorból vagy egyetlen oszlopból álló tartomány, me<PERSON>, számokat vagy logikai értékeket tartalmaz, növekvő sorrendben elhelyezve!egyetlen sorból vagy egyetlen oszlopból álló tartomány, mérete azonos a keresési_vektor méretével!az az érték, amelyet a KUTAT függvény a tömbben keres; lehet sz<PERSON><PERSON>, szöveg, logikai érték vagy értékre hivatkozó név vagy hivatkozás!olyan szövegeket, számokat vagy logikai értékeket tartalmazó cellákbó<PERSON> áll<PERSON> tart<PERSON>, amelyeket a keresési_értékkel össze kell hasonl<PERSON>tani"}, "MATCH": {"a": "(kere<PERSON><PERSON><PERSON>_<PERSON>; tá<PERSON>; [egyez<PERSON>_típus])", "d": "<PERSON><PERSON> adott értéknek megfelelő tömbelem viszonylagos helyét adja meg adott sorrendben", "ad": "az az ért<PERSON><PERSON>, amelynek segítségével a tömbben a keresett érték megtalálható; sz<PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON>, logikai érték vagy ezek egyikére való hivatkozás!lehetséges keresési értékeket tartalmazó összefüggő cellatartomány, értékekből álló tömb vagy tömbhivatkozás !értéke -1, 0 vagy 1 lehet, a visszaadandó értéket jelzi."}, "OFFSET": {"a": "(hivatkozás; sorok; oszlopok; [magasság]; [sz<PERSON>lesség])", "d": "Megadott magasságú és szélességű hivatkozást ad meg egy hivatkozástól számított megadott sornyi és oszlopnyi távolságra.", "ad": "az a hivat<PERSON><PERSON><PERSON>, amelyhez képest az eredményül kapott hivatkozás helyzetét az argumentumok meghatározzák; cellára vagy egymás melletti cellák tartományára való hivatkozás!az eredmény bal felső cellája és a hivatkozás közötti függőleges távolság a sorok számában kifejezve!az eredmény bal felső cellája és a hivatkozás közötti vízszintes távolság az oszlopok számában kifejezve!az eredményül kapott hivatkozás magassága a sorok számában mérve; elhagyása esetén a hivatkozás magasságával azonos!az eredményül kapott hivatkozás szélessége az oszlopok számában mérve; elhagyása esetén a hivatkozás szélességével azonos"}, "ROW": {"a": "([hi<PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON> hi<PERSON> sorának számát adja meg.", "ad": "az a cella vagy cell<PERSON>, amely sor<PERSON><PERSON> s<PERSON>t meg kell <PERSON>pítani; ha elhagyjuk, a SOR függvényt tartalmazó cellát adja eredményül"}, "ROWS": {"a": "(tömb)", "d": "<PERSON><PERSON> vagy tömb sorainak számát adja meg.", "ad": "ebben a tömb<PERSON>, tömbképletben vagy cellatartományra való hivatkozásban található sorok számát kell meghatározni"}, "TRANSPOSE": {"a": "(tömb)", "d": "Függőleges cellatartományból vízszinteset állí<PERSON>, vagy fordítva", "ad": "a munkalap egy cellatartománya vagy egy értéktömb, amit transzponálni kíván"}, "UNIQUE": {"a": "(tömb; [by_col]; [exactly_once])", "d": " Egy tartomány vagy tömb egyedi értékeit adja v<PERSON>.", "ad": "az a tartomány vagy tömb, amelyből az egyedi sorokat vagy oszlopokat vissza szeretné adni!logikai érték: sorok összehasonlítása egymással, és az egyedi sorok = HAMIS vagy elhagyása; összehasonlíthatja egymással az oszlopokat, és visszaállíthatja az egyedi oszlopokat = IGAZ!logikai érték: a tömb = IGAZ függvényében pontosan egyszer előforduló sorok vagy oszlopok. az összes sort vagy oszlopot vissza kell adni a tömb = HAMIS vagy elhagyása"}, "VLOOKUP": {"a": "(keresési_érték; tábl<PERSON>zat; oszlopindex; [tartományi_keresés])", "d": "Egy táblá<PERSON>t bal sz<PERSON>lső oszlopában megkeres egy értéket, és a megtalált értékhez tartozó sorból a megadott oszlopban elhelyezkedő értéket adja eredményül; alapesetben a táblázatnak növekvő sorrendbe rendezettnek kell lennie.", "ad": "a tábla első oszlopában megker<PERSON><PERSON><PERSON> érték, amely <PERSON>, hivat<PERSON><PERSON><PERSON> vagy szövegdarab lehet!az a szöveget, számokat vagy logikai értékeket tartalmazó tá<PERSON>l<PERSON>, amelyben a keresés történik, és amely lehet tartományra való hivatkozás vagy tartománynév is!a táblázattömb azon oszlopának száma, amelyb<PERSON><PERSON> a megtalált sor alapján a függvény az eredményt visszaadja; a táblázat első értékoszlopa az 1-es számú oszlop!logikai érték: HAMIS esetén pontos értéket keres; ha IGAZ, vagy elhagyjuk, az első oszlopban lévő legközelebbi értéket keresi meg (növekvő sorrend esetén)"}, "XLOOKUP": {"a": "(keres<PERSON><PERSON>_<PERSON>rt<PERSON><PERSON>; keres<PERSON>i_tömb; viss<PERSON><PERSON>nd<PERSON>_tömb; [ha_nincs_tal<PERSON><PERSON>]; [egy<PERSON><PERSON><PERSON><PERSON>_mód]; [keres<PERSON><PERSON>_mód])", "d": "Egyezéseket keres valamely tartományban vagy tömbben, és egy második tartományból vagy tömbből adja vissza a megfelelő elemet. Alapértelmezés szerint pontos egyezést használ a program.", "ad": "a keresett érték!a keresés helyéül szolgáló tömb vagy tartomány!a visszaadandó tömb vagy tartomány!ez az eredmény akkor, ha nincs találat!a keresési_érték keresési_tömbben lévő értékekkel való egyeztetésének módja!a használandó keresési mód megadása. Alapértelmezés szerint szekvenciális (vagyis az első elemtől az utolsóig tartó) keresés történik."}, "CELL": {"a": "(infó<PERSON><PERSON><PERSON>; [hivat<PERSON><PERSON><PERSON>])", "d": "Egy cella formáz<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vagy tartalmáról ad információt", "ad": "szöveges érték, <PERSON><PERSON><PERSON>, hogy milyen tí<PERSON> adatot szeretne megtudni a celláról!az a cella, amelyr<PERSON>l információt szeretne kapni"}, "ERROR.TYPE": {"a": "(hi<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> egy hibaértékhez tartozó számot ad vissza.", "ad": "az a hiba<PERSON><PERSON><PERSON><PERSON>, amelynek azonosítószámát meg kívánja kapni. Tényleges hibaérték vagy hibaértéket tartalmazó cellára történő hivatkozás lehet"}, "ISBLANK": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a hivatkozás üres cellára mutat-e, és IGAZ vagy HAMIS értéket ad vissza", "ad": "a megvizsgálandó cellára hivatkozó cella vagy név"}, "ISERR": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az adott érték eltér-e a #HIÁNYZIK hibaértéktől, és IGAZ vagy HAMIS értéket ad vissza", "ad": "a megvizsgálni kívánt érték. Az érték hivatkozhat cellára, képletre vagy olyan névre, amely cell<PERSON>, képletre vagy értékre hivat<PERSON>k"}, "ISERROR": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy hiba-e az adott érték, és IGAZ vagy HAMIS értéket ad vissza", "ad": "a megvizsgálni kívánt érték. Az érték hivatkozhat cellára, képletre vagy olyan névre, amely cell<PERSON>, képletre vagy értékre hivat<PERSON>k"}, "ISEVEN": {"a": "(szám)", "d": "A függvény eredménye IGAZ, ha a szám páros.", "ad": "a vizsgált érték"}, "ISFORMULA": {"a": "(hivatkozás)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy egy hivatkozás képletet tartalmazó cellára mutat-e, és IGAZ vagy HAMIS értéket ad vissza.", "ad": "a vizsgálandó cellára mutató hi<PERSON>, amely lehet cell<PERSON>, képlet vagy cellára hivatkozó név"}, "ISLOGICAL": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték logikai érték-e (IGAZ vagy HAMIS), és IGAZ vagy HAMIS értéket ad vissza", "ad": "a megvizsgálni kívánt érték. Az érték hivatkozhat cellára, képletre vagy olyan névre, amely cell<PERSON>, képletre vagy értékre hivat<PERSON>k"}, "ISNA": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték a #HIÁNYZIK, és IGAZ vagy HAMIS értéket ad vissza", "ad": "a megvizsgálni kívánt érték. Az érték hivatkozhat cellára, képletre vagy olyan névre, amely cell<PERSON>, képletre vagy értékre hivat<PERSON>k"}, "ISNONTEXT": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték tényleg nem szöveg (az üres cellák nem számítanak szövegnek), és IGAZ vagy HAMIS értéket ad eredményül", "ad": "a megvizsgálandó érték: cella; k<PERSON><PERSON><PERSON>; vagy olyan né<PERSON>, amely <PERSON>, képletre vagy érté<PERSON><PERSON>k"}, "ISNUMBER": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték szám-e, és IGAZ vagy HAMIS értéket ad vissza", "ad": "a megvizsgálni kívánt érték. Az érték hivatkozhat cellára, képletre vagy olyan névre, amely cell<PERSON>, képletre vagy értékre hivat<PERSON>k"}, "ISODD": {"a": "(szám)", "d": "A függvény eredménye IGAZ, ha a s<PERSON>ám páratlan.", "ad": "a vizsgált érték"}, "ISREF": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték hivatkozás-e, és IGAZ vagy HAMIS értéket ad vissza", "ad": "a megvizsgálni kívánt érték. Az érték hivatkozhat cellára, képletre vagy olyan névre, amely cell<PERSON>, képletre vagy értékre hivat<PERSON>k"}, "ISTEXT": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték szöveg-e, és IGAZ vagy HAMIS értéket ad vissza", "ad": "a megvizsgálni kívánt érték. Az érték hivatkozhat cellára, képletre vagy olyan névre, amely cell<PERSON>, képletre vagy értékre hivat<PERSON>k"}, "N": {"a": "(érték)", "d": "A nem szám értéket számmá, a dátumot dátumértékk<PERSON>, az IGAZ értékből 1, b<PERSON>rmi egyébből 0 (zérus) lesz", "ad": "a számmá átalakítani kívánt érték"}, "NA": {"a": "()", "d": "Eredménye a #HIÁNYZIK (az érték nem áll rendelkezésre) hibaérték.", "ad": ""}, "SHEET": {"a": "([<PERSON><PERSON><PERSON><PERSON>])", "d": "A hivatkozott lap lapszámá<PERSON> adja v<PERSON>.", "ad": "a lap neve vagy egy <PERSON>, amely<PERSON><PERSON> a <PERSON>zám<PERSON> kere<PERSON>; ha nincs megadva, a függv<PERSON>y annak a lapnak a s<PERSON>á<PERSON> ad<PERSON> v<PERSON>, amely<PERSON>"}, "SHEETS": {"a": "([hi<PERSON><PERSON><PERSON><PERSON>])", "d": "A hivatkozásban szereplő lapok számát adja vissza.", "ad": "az a hi<PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> meg szeret<PERSON> tud<PERSON>, hogy h<PERSON>y lapot tartalmaz; ha nincs megad<PERSON>, a függv<PERSON>y annak a munkafüzetnek a lapszámát adja v<PERSON>, amelyben szerepel"}, "TYPE": {"a": "(érték)", "d": "<PERSON>z adott érték adattípusát jelölő egész számot adja eredményül: szám = 1; szöveg = 2; logikai érték = 4; hibaérték = 16; tömb = 64; összetett adatok = 128", "ad": "b<PERSON><PERSON><PERSON><PERSON>"}, "AND": {"a": "(logikai1; [logikai2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy minden argumentumára érvényes-e az IGAZ, és ha minden argumentuma IGAZ, eredménye IGAZ", "ad": "a megvizsg<PERSON><PERSON><PERSON>; log<PERSON><PERSON>, tömbök vagy <PERSON>, mindegyik értéke IGAZ vagy HAMIS, számuk pedig 1 és 255 közötti lehet"}, "FALSE": {"a": "()", "d": "A HAMIS logikai értéket adja eredményül.", "ad": ""}, "IF": {"a": "(logika<PERSON>_vizsg<PERSON><PERSON>; [érték_ha_igaz]; [érték_ha_hamis])", "d": "Ellenőrzi a feltétel megfelelését, és ha a megadott feltétel IGAZ, az egyik értéket adja v<PERSON>, ha HAMIS, akkor a  másikat", "ad": "olyan érték vagy k<PERSON>, amely kiértékeléskor IGAZ vagy HAMIS értéket vesz fel!ezt az értéket adja a függvény eredményül, ha a logikai_vizsgálat eredménye IGAZ. Ha elhagyjuk, az eredmény IGAZ lesz. Legfeljebb hét HA ágyazható egymásba!ezt az értéket adja a függvény eredményül, ha a logikai_vizsgálat eredménye HAMIS. Ha elhagyjuk, az eredmény HAMIS lesz"}, "IFS": {"a": "(logikai_teszt; <PERSON>rték_ha_igaz; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, hogy egy vagy több feltétel teljesül-e, és eredményül visszaadja az első IGAZ feltételnek megfelelő értéket", "ad": "b<PERSON><PERSON><PERSON>en érték vagy k<PERSON>, amely IGAZnak vagy HAMISnak kiértékelhető!az az érték, amely a logikai_teszt argumentum IGAZ eredménye esetén visszaadandó"}, "IFERROR": {"a": "(érték; érték_hiba_esetén)", "d": "Ha a kifejezés hiba, akkor az érték_hiba_esetén értéket, máskülönben magát a kifejezés értékét adja v<PERSON>za", "ad": "tetszőleges érték, kifejezés vagy hivatkozás!tetszőleges érték, kifejezés vagy hivatkozás"}, "IFNA": {"a": "(érték; érték_ha_hiányzik)", "d": "A megadott értéket adja vissza, ha a kifejezés #HIÁNYZIK eredményt ad, egyébként a kifejezés eredményét adja vissza.", "ad": "tetszőleges érték, kifejezés vagy hivatkozás!tetszőleges érték, kifejezés vagy hivatkozás"}, "NOT": {"a": "(logikai)", "d": "Az IGAZ vagy HAMIS értéket az ellenkezőjére váltja", "ad": "olyan érték vagy k<PERSON>, amelyik kiértékelésekor IGAZ vagy HAMIS eredményt ad"}, "OR": {"a": "(logikai1; [logikai2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy valamelyik értékére érvényes-e az IGAZ, és IGAZ vagy HAMIS eredményt ad vissza. Eredménye csak akkor HAMIS, ha minden argumentuma HAMIS", "ad": "a megvizsgá<PERSON><PERSON>ek; mindegyik értéke IGAZ vagy HAMIS lehet, számuk pedig 1 és 255 között lehet"}, "SWITCH": {"a": "(kifejez<PERSON>; érték1; eredmény1; [alapérték_vagy_érték2]; [eredmény2]; ...)", "d": "Ez a függvény egy kifejezést értékel ki egy értéklistán, és az első egyező értéknek megfelelő eredményt adja vissza. Ha nincs egyezés, egy tetszőlegesen beállított alapértéket ad vissza", "ad": "a kiértékelendő kifejezés!a kifejezés paraméterrel összehasonlítandó érték!a visszaadandó eredmény, ha a megfelelő érték egyezik a kifejezéssel"}, "TRUE": {"a": "()", "d": "Az IGAZ logikai értéket adja eredményül.", "ad": ""}, "XOR": {"a": "(logikai1; [logikai2]; ...)", "d": "Logikai „kizárólagos vagy” műveletet végez az összes argumentummal", "ad": "a megvizsg<PERSON><PERSON><PERSON>; log<PERSON><PERSON>, tömb<PERSON>k vagy <PERSON>, mindegyik értéke IGAZ vagy HAMIS, számuk pedig 1 és 254 közötti lehet"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Karakterek elválasztását megelőző szöveget küld vissza.", "ad": " Az elválasztóhoz keresendő szöveg.! Az elválasztó karakterként vagy sztringként való használata.! Az elválasztó kívánt előfordulása. Az alapértelmezett érték 1. Egy negatív szám a végéről keres.! Elválasztóegyezést keres a szövegben. Alapértelmezés szerint a rendszer az egyezést a kis- és nagybetűk megkülönböztetése alapján teszi.! Azt határozza meg, hogy az elválasztót a szöveg végére illessze-e. Alapértelmezés szerint ezek nem egyeznek.! Akkor küldi vissza, ha nem található egyezés. Alapértelmezés szerint a rendszer #N/A értéket ad vissza."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Karakterek elválasztását követő szöveget küld vissza.", "ad": " Az elválasztóhoz keresendő szöveg.! Az elválasztó karakterként vagy sztringként való használata.! Az elválasztó kívánt előfordulása. Az alapértelmezett érték 1. Egy negatív szám a végpontról keres.! Elválasztóegyezést keres a szövegben. Alapértelmezés szerint a rendszer az egyezést a kis- és nagybetűk megkülönböztetése alapján teszi.! Azt határozza meg, hogy az elválasztót a szöveg végére illessze-e. Alapértelmezés szerint ezek nem egyeznek.! Akkor küldi vissza, ha nem található egyezés. Alapértelmezés szerint a rendszer #N/A értéket ad vissza."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "A szöveget sorokra vagy oszlopokra osztja fel a határolókkal.", "ad": "A felosztandó szöveg! Az oszlopok felosztásához használandó karakter vagy sztring.!A sorok felosztásához használandó karakter vagy sztring.!A<PERSON>t jelzi, hogy figyelmen kívül kell-e hagyni az üres cellákat. Alapértelmezett érték: FALSE.!A szövegben elválasztóegyezés szempontjából keres. Alapértelmezés szerint a kis- és nagybetűk megkülönböztetési egyezése be van fejezve.!A kitöltéshez használt érték. Alapértelmezett érték: #N/A."}, "WRAPROWS": {"a": "(ve<PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON>_<PERSON>, [<PERSON><PERSON><PERSON><PERSON>_e<PERSON><PERSON>])", "d": "<PERSON>gy sor- vagy oszlopvektor tördelése megadott számú érték után.", "ad": "A törtelendő vektor vagy hivatkozás.!Az értékek maximális száma soronként.!A tördeléshez használt érték. Az alapértelmezett érték #N/A"}, "VSTACK": {"a": "(tömb1, [tömb2], ...)", "d": " A tömböket függőlegesen egy tömbbe halmozza.", "ad": "Halmozandó tömb vagy hi<PERSON>."}, "HSTACK": {"a": "(tömb1, [tömb2], ...)", "d": " A tömböket vízszintesen egy tömbbe halmozza.", "ad": "Halmozandó tömb vagy hi<PERSON>."}, "CHOOSEROWS": {"a": "(tö<PERSON>, sor_száma1, [sor_száma2], ...)", "d": "Sorokat ad vissza tömbből vagy hivatkozásból.", "ad": "A visszaadandó sorokat tartalmazó tömb vagy hivatkozás.!A visszaadandó sor száma"}, "CHOOSECOLS": {"a": "(tö<PERSON>, oszlop_száma1, [oszlop_száma2], ...)", "d": "Csak a megadott oszlopokat adja vissza tömbből vagy hivatkozásból", "ad": "a visszaadandó oszlopokat tartalmazó tömb vagy hivatko<PERSON>!a visszaadandó oszlop sz<PERSON>."}, "TOCOL": {"a": "(tömb, [minden<PERSON><PERSON><PERSON>], [vizs<PERSON><PERSON><PERSON>_o<PERSON>])", "d": " <PERSON><PERSON> adja vissza a tömböt. ", "ad": " Az oszlopként visszaadandó tömb vagy hivatkozás.!<PERSON><PERSON><PERSON> jelzi, hogy bizonyos típusú értékeket figyelmen kívül kell-e hagyni. Alapértelmezés szerint egyetlen értéket sincs figyelmen kívül hagyva.! A tömb vizsgálata oszlop szerint. Alapértelmezés szerint a tömb vizsgálata sor szerint történik."}, "TOROW": {"a": "(tömb, [minden<PERSON><PERSON><PERSON>], [vizs<PERSON><PERSON><PERSON>_o<PERSON>])", "d": "<PERSON><PERSON> sork<PERSON>t adja vissza a tömböt.", "ad": " A sorként visszaadandó tömb vagy hivatkozás.!<PERSON><PERSON><PERSON> jelzi, hogy bizonyos típusú értékeket figyelmen kívül kell-e hagyni. Alapértelmezés szerint egyetlen érték sincs figyelmen kívül hagyva.! A tömb vizsgálata oszloponként. Alapértelmezés szerint a tömb vizsgálata soronként történik."}, "WRAPCOLS": {"a": "(ve<PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON>_<PERSON>, [<PERSON><PERSON><PERSON><PERSON>_e<PERSON><PERSON>])", "d": "<PERSON>gy sor- vagy oszlopvektor tördelése megadott számú érték után.", "ad": "A törtelendő vektor vagy hivatkozás.!Az értékek maximális száma oszloponként.!A tördeléshez használt érték. Az alapértelmezett érték #N/A"}, "TAKE": {"a": "(tömb, sorok, [oszlopok])", "d": "Sorokat vagy oszlopokat ad vissza a tömb elejéről vagy végéről.", "ad": "Az a tömb, amelyből sorokat vagy oszlopokat kell elvenni.!Az elvenni kívánt sorok száma. Negatív értéket vesz el a tömb végéről.!Az elvenni kívánt oszlopok száma. Negatív értéket vesz el a tömb végéről."}, "DROP": {"a": "(tömb, sorok, [oszlopok])", "d": "Sorokat vagy oszlopokat távolít el a tömb elejéről vagy végéről.", "ad": "Az a tömb, amelyből sorokat vagy oszlopokat kell eltávolítani.!Az eltávolítandó sorok száma. Negatív érték esik le a tömb végéről.!Az eldobandó oszlopok száma. Negatív érték esik le a tömb végéről."}, "SEQUENCE": {"a": "(sorok, [sz<PERSON><PERSON>k], [indít<PERSON>], [lépés])", "d": "Számsort ad vissza.", "ad": "a visszaadandó sorok száma!a visszaadandó oszlopok száma!a számsor első száma!a számsor egymást követő értékei közötti növekmény"}, "EXPAND": {"a": "(tömb, sorok, [oszlopok], [kit<PERSON><PERSON><PERSON><PERSON>z])", "d": "<PERSON>gy tö<PERSON>t a megadott méretűre terjeszt ki.", "ad": "A kiterjesztendő tömb.!A kiterjesztett tömb sorainak száma. Ha hiányzik, a sorok nem lesznek kiterjesztve.!A kiterjesztett tömb oszlopainak száma. Ha hiányzik, az oszlopok nem lesznek kiterjesztve.!A kitöltéshez használt érték. Az alapértelmezett érték a #N/A."}, "XMATCH": {"a": "(kere<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>, kere<PERSON><PERSON><PERSON>_tömb, [egy<PERSON><PERSON><PERSON><PERSON>_mód], [kere<PERSON><PERSON><PERSON>_mód])", "d": "Egy elem relatív helyét adja vissza egy tömbön belül. Alapértelmezés szerint pontos egyezésre van szükség", "ad": "a keresendő érték!a keresés helyéül szolgáló tömb vagy tartomány!a keresési_érték és a keresési_tömbben lévő értékek egyeztetésének módját adja meg!a használandó keresési módot adja meg. Alapértelmezés szerint szekvenciális (vagyis az első elemtől az utolsóig tartó) keresés történik"}, "FILTER": {"a": "(t<PERSON><PERSON>, bele<PERSON>glalás, [ha_<PERSON>res])", "d": "Tartomány vagy tö<PERSON> szűrése", "ad": "a szűrendő tartomány vagy tömb!logikai értékek tömbje, amelyben az IGAZ érték a megőrzendő sort vagy oszlopot jelöli!csak akkor jelenik meg eredmény, ha egy elem sincs megőrizve"}, "ARRAYTOTEXT": {"a": "(tömb, [formátum])", "d": "<PERSON>z adott tömb szöveges alakját adja vissza.", "ad": "a szöveges alakban megjelenítendő tömb!a szöveg formátuma"}, "SORT": {"a": "(tömb, [rendezési_index], [rendezési_sorrend], [oszlop_szerint])", "d": "Tartományt vagy tömböt rendez", "ad": "a rendezendő tartomány vagy tömb!a rendezés alapjául szolgáló sort vagy oszlopot jelző szám!a kívánt rendezési sorrendet jelző szám; 1 = növekvő sorrend (ez az alapértelmezett), -1 = csökkenő sorrend!a kívánt rendezési irányt jelző logikai érték: HAMIS = rendezés sor szerint (ez az alapértelmezett), HAMIS = rendezés oszlop szerint"}, "SORTBY": {"a": "(tömb, tömb_szerint, [rendezési_sorrend], ...)", "d": "Tartományt vagy tömböt rendez a megfelelő tartományban vagy tömbben lévő értékek alapján.", "ad": "a rendezendő tartomány vagy tömb!a rendezés alapjául szolgáló tartomány vagy tömb!a kívánt rendezési sorrendet jelző szám; 1 = növekvő sorrend (ez az alapértelmezett), -1 = csökkenő sorrend"}, "GETPIVOTDATA": {"a": "(adat_mező; kimutatás; [mező]; [tétel]; ...)", "d": "Kimutatásban tárolt adatokat g<PERSON>űjt ki", "ad": "annak az adatmezőnek a neve, amelyből az adatokat ki szeretné g<PERSON>űjteni!hivatkozás a kimutatás azon cellájára vagy cellatartományára, amely a beolvasandó adatokat tartalmazza!hivatkozott mező!hivatkozott tétel"}, "IMPORTRANGE": {"a": "(táb<PERSON><PERSON><PERSON>t_url-címe, tartomány_karakterlánca)", "d": "Cellatartományt importál a megadott táblázatból."}}