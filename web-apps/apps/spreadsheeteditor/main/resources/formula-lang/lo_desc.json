{"DATE": {"a": "(year; month; day)", "d": "ສົ່ງ​ຄືນ​ຕົວ​ເລກ​ທີ່​ສະ​ແດງ​ເຖິງ​ວັນ​ທີ​ໃນລະຫັດ​ວັນ​ທີ-​ເວລາ", "ad": "​ແມ່ນ​ຕົວ​ເລກ​ແຕ່ 1900 ຫຼື 1904 (​ເຊິ່ງຂຶ້ນ​ກັບ​ລະບົບ​ວັນ​ທີ​ຂອງ​ສະໝຸດ​ວຽກ) ຫາ 9999!​ແມ່ນ​ຕົວ​ເລກ​​ແຕ່ 1 ຫາ 12 ທີ່​ສະ​ແດງ​ເຖິງ​ເດືອນ​ຂອງ​ປີ!​ແມ່ນ​ຕົວ​ເລກ​ແຕ່ 1 ຫາ 31 ທີ່​ສະ​ແດງ​ເຖິງ​ມື້​ຂອງ​ເດືອນ"}, "DATEDIF": {"a": "(start-date; end-date; unit)", "d": "ຄືນ ຜົນລົບ ຈາກສອງຄ່າ ວັນທີ (ວັນທີເລີ່ມ ແລະ ວັນທີສິ້ນສຸດ) ໂດຍອີງໃສ່ ຫົວໜ່ວຍ (unit) ທີໄດ້ລະບຸໄວ້"}, "DATEVALUE": {"a": "(date_text)", "d": "ປ່ຽນ​ວັນ​ທີ​ໃນ​ຮູບ​ແບບ​ຂໍ້ຄວາມ​ເປັນ​ຕົວ​ເລກ​ທີ່​ສະ​ແດງ​ເຖິງ​ວັນ​ທີ​ນັ້ນ​ໃນລະຫັດ​ວັນ​ທີ-​ເວລາ", "ad": "​ແມ່ນ​ຂໍ້ຄວາມ​ທີ່​ສະ​ແດງ​ເຖິງ​ວັນ​ທີ​ໃດ​ໜຶ່ງ​ໃນ​ຮູບ​ແບບ​ວັນ​ທີ Spreadsheet Editor, ລະ​ຫວ່າງ 1/1/1900 ຫຼື 1/1/1904 (​ເຊິ່ງຂຶ້ນ​ກັບ​ລະບົບ​ວັນ​ທີ​ຂອງ​ສະໝຸ​ດວຽກ) ​ແລະ 12/31/9999"}, "DAY": {"a": "(serial_number)", "d": "ສົ່ງ​ຄືນ​ຄ່າວັນ​ຂອງ​ເດືອນ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ແຕ່ 1 ຫາ 31.", "ad": "​ແມ່ນ​ຕົວ​ເລກ​ໃນ​ລະຫັດ​ວັນ​ທີ-​ເວລາ​ທີ່​ໃຊ້​ໂດຍ Spreadsheet Editor"}, "DAYS": {"a": "(end_date; start_date)", "d": "ສົ່ງ​ຄືນ​ຈຳນວນ​ມື້ລະ​ຫວ່າງ​ສອງ​ວັນ​ທີ.", "ad": "start_date ​ແລະ end_date ​ແມ່ນສອງ​ວັນ​ທີທີ່​ທ່ານ​ຕ້ອງການ​ຮູ້​ຈໍານວນ​ມື້ທັງ​ໝົດລະຫວ່າງ​ກາງ!start_date ​ແລະ end_date ​​ແມ່ນ​ສອງ​ວັນ​ທີ​ທີ່​ທ່ານ​ຕ້ອງການ​ຮູ້​ຈໍານວນ​ມື້​ທັງ​ໝົດ​ລະຫວ່າງ​ກາງ"}, "DAYS360": {"a": "(start_date; end_date; [method])", "d": "ສະ​ແດງ​ຜົນ​ຈຳ​ນວນວັນລະ​ຫວ່າງ​ສອງວັນ​ທີ​ຕາມປີ 360 ວັນ (ເດືອນ 30 ວັນ​ສິບ​ສອງ​ເດືອນ)", "ad": "​ວັນ​ທີ_ເລີ່ມ​ຕົ້ນ ແລະ ​ວັນ​ທີ_ສິ້ນ​ສຸດ ແມ່ນ​ສອງວັນ​ທີລະ​ຫວ່າງ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ຮູ້​ຈຳ​ນວນວັນ!​ວັນ​ທີ_ເລີ່ມ​ຕົ້ນ ແລະ ​ວັນ​ທີ_ສິ້ນ​ສຸດ ແມ່ນ​ສອງວັນ​ທີລະ​ຫວ່າງ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ຮູ້​ຈຳ​ນວນວັນ!ແມ່ນຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາລະ​ບຸ​ວິ​ທີ​ການ​ຄິດ​ໄລ່: ສະ​ຫະ​ລັດ (NASD) = False ຫຼື​ຖືກ​ລະ​ເວັ້ນ; ເອີ​ຣົບ = ​TRUE."}, "EDATE": {"a": "(start_date; months)", "d": "ສະ​ແດງ​ຜົນ​ຕົວ​ເລກ​ລຳ​ດັບ​ຂອງວັນ​ທີ​ທີ່​ເປັນ​ຕົວ​ເລກ​ຊີ້ບອກ​ໄວ້​ຂອງເດືອນກ່ອນ ຫຼື​ຫຼັງ​ຈາກວັນ​ທີ​ເລີ່ມ​ຕົ້ນ", "ad": "ແມ່ນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ​ທີ່​ແທນ​ວັນ​ທີ​ເລີ່ມ​ຕົ້ນ!ແມ່ນ​ຈຳ​ນວນ​ເດືອນ​ກ່ອນ ຫຼື​ຫຼັງ​ຈາກວັນ​ທີ_ເລີ່ມ​ຕົ້ນວັນ​ທີ_ເລີ່ມ​ຕົ້ນ"}, "EOMONTH": {"a": "(start_date; months)", "d": "​ສະ​ແດງ​ຜົນ​ຕົວ​ເລກ​ລຳ​ດັບ​ຂອງວັນ​ສຸດ​ທ້າຍ​ຂອງ​ເດືອນ​ກ່ອນ ຫຼື​ຫຼັງ​ຈາກຈຳ​ນວນ​ເດືອນລະ​ບຸ​ໄວ້", "ad": "ແມ່ນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ​ທີ່​ແທນ​ວັນ​ທີ​ເລີ່ມ​ຕົ້ນ!ແມ່ນ​ຈຳ​ນວນ​ເດືອນ​ກ່ອນ ຫຼື​ຫຼັງ​ຈາກວັນ​ທີ_ເລີ່ມ​ຕົ້ນ"}, "HOUR": {"a": "(serial_number)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຊົ່ວ​ໂມງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 0 (12:00 A.M.) ຫາ 23 (11:00 P.M.) ", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ໃນ​ລະຫັດ​ວັນ​ທີ-​ເວລາ​ທີ່​ໃຊ້​ໂດຍ Spreadsheet Editor ຫຼື​​ແມ່ນ​ຂໍ້ຄວາມ​ໃນ​ຮູບ​ແບບ​ເວລາ​ ເຊັ່ນ 16:48:00 ຫຼື 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(date)", "d": "ສົ່ງ​ຄືນ​ຈຳນວນ​ອາທິດ​ ISO ​ໃນ​ປີ​ສໍາລັບ​ວັນ​ທີ່​ທີ່​​ໃຫ້", "ad": "​ແມ່ນ​ລະຫັດ​ວັນ​ທີ-​ເວລາ​ທີ່​ໃຊ້​ໂດຍ Spreadsheet Editor ສໍາລັບການ​ຄໍານວນ​ວັນ​ທີ ​ແລະ​ເວລາ"}, "MINUTE": {"a": "(serial_number)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ນາທີ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກຕັ້ງ​ແຕ່ 0 ຫາ 59 ", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ໃນ​ລະຫັດ​ວັນ​ທີ-​ເວລາ​ທີ່​ໃຊ້​ໂດຍ Spreadsheet Editor ຫຼື​​ແມ່ນ​ຂໍ້ຄວາມ​ໃນ​ຮູບ​ແບບ​ເວລາ​ ເຊັ່ນ 16:48:00 ຫຼື 4:48:00 PM"}, "MONTH": {"a": "(serial_number)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ເດືອນ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 (ມັງກອນ) ຫາ 12 (ທັນວາ).", "ad": "ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ໃນ​ລະຫັດ​ວັນ​ທີ-​ເວລາ​ທີ່​ໃຊ້​ໂດຍ Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(start_date; end_date; [holidays])", "d": "ສະ​ແດງ​ຜົນ​ວັນ​ເຮັດ​ວຽກ​ທັງ​ໝົດ​ລະ​ຫວ່າງສອງວັນ​ທີ", "ad": "ແມ່ນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ​ທີ່​ແທນ​ວັນ​ທີ​ເລີ່ມ​ຕົ້ນ!ແມ່ນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ​ທີ່​ສະ​ແດງວັນ​ທີ​ສຸດ​ທ້າຍ!ແມ່ນ​ຊຸດ​ທາງ​ເລືອກ​ຂອງ​ໜຶ່ງ ຫຼື​ຫຼາຍ​ຕົວ​ເລກວັນ​ທີ ເພື່ອ​ເອົາ​ອອກ​ຈາກ​ປະ​ຕິ​ທິນ​ເຮັດ​ວຽກ, ເຊັ່ນ ວັນ​ພັກ​ຂອງ​ລັດ ແລະ​ຂອງ​ລັດ​ຖະ​ບານ​ກາງ ແລະ​ວັນ​ພັກ​ລອຍ"}, "NETWORKDAYS.INTL": {"a": "(start_date; end_date; [weekend]; [holidays])", "d": "ສົ່ງ​ກັບ​ຈໍານວນ​ວັນ​ເຮັດ​ວຽກ​ເຕັມ​ວັນ​ທີ່ຢູ່​ລະຫວ່າງ​ສອງ​ວັນ​ທີ ​ໂດຍ​ມີ​ພາຣາມິເຕີ​ວັນ​ທ້າຍ​ອາທິດ​ແບບ​ກໍານົດ​ເອງ", "ad": "​ແມ່ນ​ເລກ​ວັນ​ທີ​ຕໍ່​ເນື່ອງ​ກັນ​ທີ່​ສະ​ແດງ​ເຖິງ​ວັນ​ທີ​ເລີ່​ມຕົ້ນ!​ແມ່ນ​ເລກ​ວັນ​ທີ​ຕໍ່​ເນື່ອງ​ກັນ​ທີ່​ສະ​ແດງ​ເຖິງ​ວັນ​ທີ​ສິ້ນ​ສຸດ!​ແມ່ນ​ຕົວ​ເລກ ຫຼືສະຕຣິງທີ່​ລະບຸ​ວັນ​ທ້າຍ​ອາທິດ!​ແມ່ນ​ຊຸດ​ຂອງ​ໜຶ່ງ ຫຼືຫຼາຍຕົວ​ເລກ​ວັນ​ທີ​ຕໍ່​ເນື່ອງ​ກັນ​ທີ່​ຈະ​ບໍ່​ລວມ​ເອົາ​ໃນ​ປະຕິທິນ​ວັນ​ເຮັດວຽກ ​ເຊັ່ນ​ວ່າ​ວັນພັກລັດຖະການ ​ແລະ​ວັນພັກ​ທີ່​ບໍ່​ແນ່ນອນ ​ໂດຍ​ເປັນ​ຄ່າ​ທີ່​ຈະ​ລະບຸ ຫຼືບໍ່​ລະບຸ​ກໍ່​ໄດ້"}, "NOW": {"a": "()", "d": "ສົ່ງຄືນຄ່າວັນທີ ແລະ ເວລາປະຈຸບັນທີ່​ມີຮູບແບບ​ເປັນວັນທີ ແລະເວລາ.", "ad": ""}, "SECOND": {"a": "(serial_number)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ວິນາທີ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 0 ຫາ 59.", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ໃນ​ລະຫັດ​ວັນ​ທີ-​ເວລາ​ທີ່​ໃຊ້​ໂດຍ Spreadsheet Editor ຫຼືຂໍ້ຄວາມ​ໃນ​ຮູບ​ແບບ​ເວລາ ​ເຊັ່ນ​ວ່າ 16:48:23 ຫຼື 4:48:47 PM"}, "TIME": {"a": "(hour; minute; second)", "d": "ປ່ຽນແປງຄ່າຕົວເລກຊົ່ວໂມງ, ນາທີ, ແລະວິນາທີທີ່ລະບຸ, ໃຫ້ເປັນຕົວເລກລະຫັດຂອງ, ເຊິ່ງຈະຖືກຈັດຮູບແບບດ້ວຍຮູບແບບເວລາ", "ad": "ເປັນຕົວເລກຕັ້ງແຕ່ 0 ຫາ 23 ທີ່ໃຊ້ແທນຊົ່ວໂມງ!ເປັນຕົວເລກຕັ້ງແຕ່ 0 ຫາ 59 ທີ່ໃຊ້ແທນນາທີ!ເປັນຕົວເລກຕັ້ງແຕ່ 0 ຫາ 59 ທີ່ໃຊ້ແທນວິນາທີ"}, "TIMEVALUE": {"a": "(time_text)", "d": "ປ່ຽນ​ເວລາ​ຂໍ້ຄວາມ​ເປັນ​ເລກ​ດໍາ​ລັບ​ຕໍ່​ເນື່ອງ​ຂອງສໍາລັບ​ເວລາ​​ໃດ​ໜຶ່ງ, ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 0 (12:00:00 AM) ຫາ 0.999988426 (11:59:59 PM). ຈັດ​ຮູບ​ແບບ​ຕົວ​ເລກ​ທີ່​ມີ​ຮູບ​ແບບ​ເວລາ​ຫຼັງຈາກ​ການ​ປ້ອນ​ສູດ​ເຂົ້າ", "ad": "​ແມ່ນ​ສະຕຣິງຂໍ້ຄວາມ​ທີ່​ໃຫ້​ຄ່າ​ເວລາ​ໃນ​ຮູບ​ແບບ​ໜຶ່ງ​ໃນ​ບັນດາ​ຮູບ​ແບບ​ເວລາ Spreadsheet Editor (ຂໍ້​ມູນ​ວັນ​ທີ​ໃນ​ສະຕຣິງຈະ​ຖືກ​ລະ​ເວັ້ນ)"}, "TODAY": {"a": "()", "d": "ສົ່ງຄືນຄ່າວັນທີປະຈຸບັນທີ່ມີຮູບແບບເປັນວັນທີ.", "ad": ""}, "WEEKDAY": {"a": "(serial_number; [return_type])", "d": "ສົ່ງຄືນຄ່າຕົວເລກຕັ້ງແຕ່ 1 ຫາ 7 ເຊິ່ງຈະລະບຸວັນໃນອາທິດຂອງວັນທີ.", "ad": "ເປັນຄ່າຕົວເລກທີ່ໃຊ້ແທນວັນທີ!ເປັນຄ່າຕົວເລກ  ໂດຍຕົວເລກທີ່ແທນວັນອາທິດ=1 ຮອດວັນເສົາ=7, ໃຊ້ຕົວເລກ 1; ສຳລັບວັນຈັນ=1 ຮອດວັນທິດ=7, ໃຊ້ຕົວເລກ 2; ສຳລັບວັນຈັນ=0 ຮອດວັນທິດ=6, ໃຊ້ຕົວເລກ 3"}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "ສົ່ງ​ຄືນ​ຈຳນວນ​ອາທິດ​ໃນ​ປີ", "ad": "​ແມ່ນ​ລະຫັດ​ວັນ​ທີ-​ເວລາ​ທີ່​ໃຊ້​ໂດຍ Spreadsheet Editor ສໍາລັບການ​ຄໍານວນ​ວັນ​ທີ ​ແລະ​ເວລາ​!​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ (1 ຫຼື 2) ທີ່​ກຳນົດ​ປະ​ເພດ​ຂອງ​ຄ່າ​ທີ່​ສົ່ງ​ຄືນ"}, "WORKDAY": {"a": "(start_date; days; [holidays])", "d": "ສະ​ແດງ​ຜົນ​ຕົວ​ເລກ​ລຳ​ດັບ​ຂອງວັນ​ທີ ກ່ອນ ຫຼື​ຫຼັງ​ຈຳ​ນວນ​ວັນ​ສະ​ເພາະ", "ad": "ແມ່ນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ​ທີ່​ແທນ​ວັນ​ທີ​ເລີ່ມ​ຕົ້ນ!ແມ່ນ​ຈຳ​ນວນ​ວັນ​ຂອງວັນ​ທີ່​ບໍ່​ແມ່ນ​ທ້າຍ​ອາ​ທິດ ແລະ​ບໍ່​ແມ່ນ​ວັນ​ພັກ ກ່ອນ ຫຼື​ຫຼັງວັນ​ທີ_ເລີ່ມ​ຕົ້ນ!ແມ່ນແຖວ​ລຳ​ດັບທາງ​ເລືອກ​ຂອງ​ໜຶ່ງ ຫຼື​ຫຼາຍ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ ເພື່ອ​ເອົາ​ອອກ​ຈາກ​ປະ​ຕິ​ທິນ​ເຮັດ​ວຽກ, ເຊັ່ນ ວັນ​ພັກຂອງ​ລັດ ແລະ​ຂອງ​ລັດ​ຖະ​ບານ​ກາງ ແລະ​ວັນ​ພັກເລື່ອນ​ລອຍ"}, "WORKDAY.INTL": {"a": "(start_date; days; [weekend]; [holidays])", "d": "ສົ່ງ​ຄືນ​ເລກ​ດໍາ​ລັບ​ຂອງ​ວັນ​ທີ​ກ່ອນ ຫຼືຫຼັງຈໍານວນ​ວັນເຮັດ​ວຽກ​ທີ່​ລະບຸ ​ໂດຍ​ມີ​ພາຣາມິເຕີ​ວັນ​ທ້າຍ​ອາທິດ​ທີ່​ກຳນົດ​ເອງ", "ad": "​ແມ່ນ​ເລກ​ວັນ​ທີ​ທີ່​ຕໍ່​ເນື່ອງ​ກັນ​ທີ່​ສະ​ແດງ​ເຖິງ​ວັນ​ທີ​ເລີ່​ມຕົ້ນ!​ແມ່ນ​ຈໍານວນ​ຂອງ​ວັນ​ທີ່​ບໍ່​ແມ່ນ​ວັນ​ທ້າຍ​ອາທິດ ​ແລະ​ບໍ່​ແ​ມ່ນວັນພັກ​ທີ່​ມາ​ກ່ອນ ຫຼືຫຼັງ start_date!​ແມ່ນ​ຕົ​ວ​ເລກ ຫຼືສະຕຣິງທີ່​ລະບຸ​ວັນ​ທ້າຍ​ອາທິດ!​ແມ່ນ​ອາ​ເຣຍ໌ຂອງໜຶ່ງ ຫຼືຫຼາຍ​ເລກ​ວັນ​ທີທີ່​ຕໍ່​ເນື່ອງ​ກັນທີ່​ຈະ​ບໍ່​ລວມ​ໃນ​ປະຕິທິນ​ວັນ​ເຮັດ​ວຽກ ​ເຊັ່ນ​ວ່າ ວັນພັກ​ລັດຖະການ ​ແລະ​ວັນພັກ​ທີ່​ບໍ່​ແນ່ນອນ ​ໂດຍ​ຈະ​ລະບຸ ຫຼືບໍ່​ລະບຸ​ກໍ່​ໄດ້"}, "YEAR": {"a": "(serial_number)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປີ​ຂອງ​ວັນ​ທີ ​ເຊິ່ງ​ເປັນ​ຈໍານວນ​ຖ້ວນ​ໃນ​ຂອບ​ເຂດ 1900 - 9999.", "ad": "ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ໃນ​ລະຫັດ​ວັນ​ທີ-​ເວລາ​ທີ່​ໃຊ້​ໂດຍ Spreadsheet Editor"}, "YEARFRAC": {"a": "(start_date; end_date; [basis])", "d": "ສະ​ແດງ​ຜົນ​ເສດ​ສ່ວນ​ປີ​ທີ່​ແທນ​ໃຫ້​ຈຳ​ນວນ​ວັນ​ທັງ​ໝົດ​ລະ​ຫວ່າງ​ວັນ​ທີ_ເລີ່ມ​ຕົ້ນ ແລະ ​ວັນ​ທີ_ສິ້ນ​ສຸດ", "ad": "ແມ່ນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ​ທີ່​ແທນ​ວັນ​ທີ​ເລີ່ມ​ຕົ້ນ!ແມ່ນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ​ທີ່​ແທນ​ວັນ​ທີ​ສຸດ​ທ້າຍ!ແມ່ນ​ປະ​ເພດ​ຂອງ​ເກນ​ການ​ນັບ​ວັນ​ທີ່​ນຳ​ໃຊ້"}, "BESSELI": {"a": "(x; n)", "d": "ສະ​ແດງ​ຜົນຟັງ​ຄ໌​ຊັນ Bessel Yn(x)", "ad": "ແມ່ນຄ່າທີ່​ປະ​ເມີນ​ຜົນ​ຟັງ​ຄ໌​ຊັນ!ແມ່ນ​ລຳ​ດັບ​ຂອງ​ຟັງ​ຄ໌​ຊັນ"}, "BESSELJ": {"a": "(x; n)", "d": "ສະ​ແດງ​ຜົນຟັງ​ຄ໌​ຊັນ Bessel Jn(x)", "ad": "ແມ່ນຄ່າທີ່​ປະ​ເມີນ​ຜົນ​ຟັງ​ຄ໌​ຊັນ!ແມ່ນ​ລຳ​ດັບ​ຂອງ​ຟັງ​ຄ໌​ຊັນ Bessel"}, "BESSELK": {"a": "(x; n)", "d": "ສະ​ແດງ​ຜົນຟັງ​ຄ໌​ຊັນ Bessel ດັດ​ແປງ​ແລ້ວ ln(x)", "ad": "ແມ່ນຄ່າທີ່​ປະ​ເມີນ​ຜົນ​ຟັງ​ຄ໌​ຊັນ!ແມ່ນ​ລຳ​ດັບ​ຂອງ​ຟັງ​ຄ໌​ຊັນ Bessel"}, "BESSELY": {"a": "(x; n)", "d": "ສະ​ແດງ​ຜົນຟັງ​ຄ໌​ຊັນ Bessel Jn(x)", "ad": "ແມ່ນຄ່າທີ່​ປະ​ເມີນ​ຜົນ​ຟັງ​ຄ໌​ຊັນ!ແມ່ນ​ລຳ​ດັບ​ຂອງ​ຟັງ​ຄ໌​ຊັນ Bessel"}, "BIN2DEC": {"a": "(number)", "d": "ປ່ຽນເລກຖານສອງເປັນເລກຖານສິບ", "ad": "​ແມ່ນເລກຖານສອງທີ່ທ່ານຕ້ອງການປ່ຽນ"}, "BIN2HEX": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສອງເປັນເລກຖານສິບ ຫົກ", "ad": "​ແມ່ນເລກຖານສອງທີ່ທ່ານຕ້ອງການປ່ຽນ!​ແມ່ນຈຳນວນອັກຂະ​ລະທີ່ຕ້ອງການໃຊ້"}, "BIN2OCT": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສອງເປັນເລກຖານແປດ", "ad": " ຄືເລກຖານສອງທີ່ທ່ານຕ້ອງການປ່ຽນ!​ແມ່ນຈຳນວນອັກຂະ​ລະທີ່ຕ້ອງການໃຊ້"}, "BITAND": {"a": "(number1; number2)", "d": "ສົ່ງ​ຄືນ​ຜົນ​ຂອງ​ຄ່າ​ຄວາມຈິງ 'AND' ຂອງ​ສອງ​ຕົວ​ເລກ​ໃນ​ຮູບ​ຂອງ​ບິດ", "ad": "​ແມ່ນ​ຕົວ​ເລກ​ທີ່​ແທນ​ເລກ​ຖານ​ສອງ​ທີ່​ທ່ານ​ຕ້ອງການ​ປະ​ເມີນ!​ເປັນ​ຕົວ​ເລກ​ຖານ​ສິບ​ທີ່​ແທນ​ເລກ​ຖານ​ສອງ​ທີ່​ທ່ານ​ຕ້ອງການ​ປະ​ເມີນ"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "ສົ່ງ​ຄືນ​ຕົວ​ເລກ​ທີ່​ຍ້າຍ​ໄປ​ທາງ​ຊ້າຍ​ຈໍານວນ shift_amount ບິດ", "ad": "​ແມ່ນ​ຕົວ​ເລກ​ຖານ​ສິບ​ທີ່​ແທນ​ເລກ​ຖານ​ສອງ​ທີ່​ທ່ານ​ຕ້ອງການ​ຈະ​ປະ​ເມີນ!​ແມ່ນ​ຈໍານວນ​ຈໍານວນ​ບິດ​ທີ່​ທ່ານ​ຕ້ອງການ​ຍ້າຍ​ຕົວ​ເລກ​ໄປ​ທາງ​ຊ້າຍ"}, "BITOR": {"a": "(number1; number2)", "d": "ສົ່ງ​ຄືນ​ຜົນ​ຂອງ​ຄ່າ​ຄວາມ​ຈິງ 'Or' ຂອງ​ສອງ​ຕົວ​ເລກ​ໃນ​ຮູບ​ຂອງ​ບິດ", "ad": "​ແມ່ນ​ຕົວ​ເລກ​ທີ່​ແທນ​ເລກ​ຖານ​ສອງ​ທີ່​ທ່ານ​ຕ້ອງການ​ປະ​ເມີນ!​ເປັນ​ຕົວ​ເລກ​ຖານ​ສິບ​ທີ່​ແທນ​ເລກ​ຖານ​ສອງ​ທີ່​ທ່ານ​ຕ້ອງການ​ປະ​ເມີນ"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "ສົ່ງ​ຄືນ​ຕົວ​ເລກ​ທີ່​ຍ້າຍ​ໄປ​ທາງ​ຂວາຈໍານວນ shift_amount ບິດ", "ad": "​ແມ່ນ​ຕົວ​ເລກ​ຖານ​ສິບ​ທີ່​ແທນ​ເລກ​ຖານ​ສອງ​ທີ່​ທ່ານ​ຕ້ອງການ​ຈະ​ປະ​ເມີນ!​ແມ່ນ​ຈໍານວນ​ຈໍານວນ​ບິດ​ທີ່​ທ່ານ​ຕ້ອງການ​ຍ້າຍ​ຕົວ​ເລກ​ໄປ​ທາງ​ຂວາ"}, "BITXOR": {"a": "(number1; number2)", "d": "ສົ່ງ​ຄືນ​ຜົນ​ຂອງ​ຄ່າ​ຄວາມ​ຈິງ 'Exclusive Or' ຂອງ​ສອງ​ຕົວ​ເລກ​ໃນ​ຮູບ​ຂອງ​ບິດ", "ad": "​ແມ່ນ​ຕົວ​ເລກ​ທີ່​ແທນ​ເລກ​ຖານ​ສອງ​ທີ່​ທ່ານ​ຕ້ອງການ​ປະ​ເມີນ!​ເປັນ​ຕົວ​ເລກ​ຖານ​ສິບ​ທີ່​ແທນ​ເລກ​ຖານ​ສອງ​ທີ່​ທ່ານ​ຕ້ອງການ​ປະ​ເມີນ"}, "COMPLEX": {"a": "(real_num; i_num; [suffix])", "d": "ແປງສຳປະສິດຂອງສ່ວນຈິງ ແລະ ສ່ວນຈິນຕະນາການເປັນຈຳນວນຊັບຊ້ອນ", "ad": "​ແມ່ນສຳປະສິດຂອງສ່ວນຈິງຂອງຈຳນວນຊັບຊ້ອນ!​ແມ່ນສຳປະສິດຂອງສ່ວນຈິນຕະນາການຂອງຈຳນວນຊັບຊ້ອນ!​ແມ່ນອົງປະກອບຕໍ່ທ້າຍສ່ວນຈິນຕະນາການຂອງຈຳນວນຊັບຊ້ອນ"}, "CONVERT": {"a": "(number; from_unit; to_unit)", "d": "ປ່ຽນຈຳນວນຈາກລະບົບວັດ​ແທກໜຶ່ງເປັນອີກລະບົບ​ວັດ​ແທກ​ອື່ນໜຶ່ງ", "ad": "​ແມ່ນຄ່າ​ໃນ from_unit ທີ່ຕ້ອງການປ່ຽນ!​ແມ່ນໜ່ວຍວັດ​ແທກສຳລັບຈຳນວນທີ່ລະບຸ!​ແມ່ນໜ່ວຍວັດ​ແທກຂອງຜົນຮັບ"}, "DEC2BIN": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສິບເປັນເລກຖານສອງ", "ad": "​ແມ່ນຈຳນວນເຕັມຖານສິບທີ່ທ່ານຕ້ອງການປ່ຽນ!​ແມ່ນຈຳນວນອັກຂະ​ລະທີ່ຕ້ອງການໃຊ້"}, "DEC2HEX": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສິບເປັນເລກຖານສິບຫົກ", "ad": "​ແມ່ນຈຳນວນເລກຖານສິບທີ່ທ່ານຕ້ອງການປ່ຽນ!​ແມ່ນຈຳນວນອັກຂະ​ລະທີ່ຕ້ອງການໃຊ້"}, "DEC2OCT": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສິບເປັນເລກຖານແປດ", "ad": "​ແມ່ນຈຳນວນເລກຖານສິບທີ່ທ່ານຕ້ອງການປ່ຽນ!​ແມ່ນຈຳນວນອັກຂະ​ລະທີ່ຕ້ອງການໃຊ້"}, "DELTA": {"a": "(number1; [number2])", "d": "ທົດສອບວ່າຈຳນວນສອງຈຳນວນມີຄ່າເທົ່າກັນຫຼືບໍ່", "ad": "​ແມ່ນຈຳນວນທຳອິດ!​ແມ່ນຈຳນວນທີສອງ"}, "ERF": {"a": "(lower_limit; [upper_limit])", "d": "ສົ່ງຄືນຟັງຊັນຂໍ້ຜິດພາດ", "ad": "​ແມ່ນຂອບສູງສຸດຂອງການອິນທິເກຣດ ERF !​ແມ່ນຂອບຕໍ່າສຸດຂອງການອິນທິເກຣດ ERF"}, "ERF.PRECISE": {"a": "(X)", "d": "ສົ່ງ​ຄືນ​ຟັງ​ຊັນ​ຄ່າ​ຜິດພາດ", "ad": "​ແມ່ນ​ຂອບ​ຕໍ່າ​ສຸດ​ສໍາລັບ​ການ​ລວມ​ເຂົ້າກັນ​ເປັນ​ໜຶ່ງ ERFC.PRECISE"}, "ERFC": {"a": "(x)", "d": "ສະ​ແດງ​ຜົນ​ຟັງ​ຄ໌​ຊັນ​ຜິດ​ພາດ​ເຕີມ​ເຕັມ", "ad": "ແມ່ນ​ຂອບ​ຕ່ຳ​ສຳ​ລັບການ​ຫາ​ຈຳ​ນວນ​ເຕັມ ERF"}, "ERFC.PRECISE": {"a": "(X)", "d": "ສົ່ງ​ຄືນ​ຟັງ​ຊັນ​ຄ່າ​ຜິດພາດ​ເສີມ​ເຕີມ", "ad": "​ແມ່ນ​ຂອບ​ຕໍ່າ​ສຸດ​ສໍາລັບ​ການ​ລວມ​ເຂົ້າກັນ​ເປັນ​ໜຶ່ງ ERFC.PRECISE"}, "GESTEP": {"a": "(number; [step])", "d": "ທົດສອບວ່າຈຳນວນທີ່ລະບຸມີຄ່າຫຼາຍກ່ວາຄ່າທີ່ໃຊ້ເປັນຕົວທຽບຫຼືບໍ່", "ad": "​ແມ່ນຄ່າທີ່ທ່ານຕ້ອງການທົດສອບທຽບກັບ step! ຄືຄ່າທີ່ໃຊ້ເປັນຕົວທຽບ"}, "HEX2BIN": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສິບຫົກເປັນເລກຖານສອງ", "ad": "​ແມ່ນເລກຖານສິບຫົກທີ່ທ່ານຕ້ອງການປ່ຽນ​!​ແມ່ນຈຳນວນອັກຂະ​ລະທີ່ຕ້ອງການໃຊ້"}, "HEX2DEC": {"a": "(number)", "d": "ປ່ຽນເລກຖານສິບຫົກເປັນເລກຖານສິບ", "ad": "​ແມ່ນເລກຖານສິບຫົກທີ່ທ່ານຕ້ອງການປ່ຽນ"}, "HEX2OCT": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສິບຫົກເປັນເລກຖານແປດ", "ad": "​ແມ່ນເລກຖານສິບຫົກທີ່ທ່ານຕ້ອງການປ່ຽນ!​ແມ່ນຈຳນວນອັກຂະ​ລະທີ່ຕ້ອງການໃຊ້"}, "IMABS": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າສົມບູນ (ໂມດູນ) ຂອງຈຳນວນຊັບຊ້ອນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການຫາຄ່າສົມບູນ"}, "IMAGINARY": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າສຳປະສິດຂອງສ່ວນຈິນຕະນາການຂອງຈຳນວນຊັບຊ້ອນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການຫາຄ່າສຳປະສິດຂອງສ່ວນຈິນຕະນາການ"}, "IMARGUMENT": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າຂໍ້ພິສູດ q ເຊິ່ງເປັນຄ່າມູມໃນໜ່ວຍຣາດຽນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການຫາຄ່າຂໍ້ພິສູດ"}, "IMCONJUGATE": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າກະຈາຍ​ຮູບ​ຂອງຈຳນວນຊັບຊ້ອນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການຫາຄ່າກະຈາຍ​ຮູບ"}, "IMCOS": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າໂກຊິນຂອງຈຳນວນຊັບຊ້ອນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການຫາຄ່າໂກຊິນ"}, "IMCOSH": {"a": "(inumber)", "d": "ຕອບໄຮເປີບໍລິກໂກຊິນຂອງຈຳນວນຊັບຊ້ອນ", "ad": "ເປັນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການໃຫ້ຄໍານວນຄ່າໄຮເປີບໍລິກໂກຊິນ"}, "IMCOT": {"a": "(inumber)", "d": "ຕອບໂຄແທນເຈນຂອງຈຳນວນຊັບຊ້ອນ", "ad": "ເປັນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການໃຫ້ຄໍານວນຄ່າໂຄແທນເຈນ"}, "IMCSC": {"a": "(inumber)", "d": "ສົ່ງ​ຄືນ​ໂຄ​ຊີ​ແຄນ​ທ (cosecant) ຂອງ​ຈຳນວນ​ຊັບຊ້ອນ", "ad": "​ເປັນ​ຈຳນວນ​ຊັບ​ຊ້ອນ​ທີ່​ທ່ານ​ຕ້ອງການ​ໃຫ້​ຄໍານວນ​ຄ່າ​ໂຄ​ຊີ​ແຄນ​ທ"}, "IMCSCH": {"a": "(inumber)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກໂຄ​ຊີ​ແຄນ​ທ (hyperbolic cosecant) ຂອງ​ຈຳນວນ​ຊັບຊ້ອນ", "ad": "​ເປັນ​ຈຳນວນ​ຊັບ​ຊ້ອນ​ທີ່​ທ່ານ​ຕ້ອງການ​ໃຫ້​ຄໍານວນ​ຄ່າ​ໄຮ​ເປີ​ບໍລິກ​ໂຄ​ຊີ​ແຄນ​ທ"}, "IMDIV": {"a": "(inumber1; inumber2)", "d": "ສົ່ງຄືນຜົນຫານລະຫວ່າງຈຳນວນຊັບຊ້ອນສອງຈຳນວນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ເປັນຈຳນວນເສດ ຫຼືຕົວຕັ້ງຫານ!​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ເປັນສ່ວນ ຫຼືຕົວຫານ"}, "IMEXP": {"a": "(inumber)", "d": "ເລກກຳລັງຂອງຈຳນວນຊັບຊ້ອນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການຫາຄ່າເລກກຳລັງ"}, "IMLN": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າໂລກາລິດທຳມະຊາດຂອງຈຳນວນຊັບຊ້ອນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການຫາຄ່າໂລກາລິດທຳມະຊາດ"}, "IMLOG10": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າໂລກາລິດພື້ນສິບ ຂອງຈຳນນວນຊັບຊ້ອນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການຫາຄ່າໂລກາລິດພື້ນສິບ"}, "IMLOG2": {"a": "(inumber)", "d": "ສົ່ງຄ່າໂລກາລິດພື້ນສອງ ຂອງຈຳນວນ ຊັບຊ້ອນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການຫາຄ່າ ໂລກາລິດພື້ນສອງ"}, "IMPOWER": {"a": "(inumber; number)", "d": "ສົ່ງຄືນຈຳນວນຊັບຊ້ອນຂຶ້ນກຳລັງທີ່ມີເລກຂຶ້ນກຳລັງເປັນຈຳນວນເຕັມ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນ ທີ່ທ່ານຕ້ອງການຂຶ້ນກຳລັງ!​ແມ່ນເລກຂຶ້ນກຳລັງທີ່ທ່ານຕ້ອງການຂຶ້ນກຳລັງໃຫ້ຈຳນວນຊັບຊ້ອນ"}, "IMPRODUCT": {"a": "(inumber1; [inumber2]; ...)", "d": "ສົ່ງຄືນຜົນຄູນຂອງຈຳນວນຊັບຊ້ອນຕັ້ງແຕ່ 1 ຫາ 255 ຈຳນວນ", "ad": "inumber1, inumber2,...ເປັນຈຳນວນຊັບຊ້ອນຕັ້ງແຕ່ 1 ຫາ 255 ຈຳນວນທີ່ຈະນຳມາຄູນກັນ."}, "IMREAL": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າສຳປະສິດຂອງສ່ວນຈິງຂອງຈຳນວນຊັບຊ້ອນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການຫາຄ່າສຳປະສິດຂອງສ່ວນເປັນຈິງ"}, "IMSEC": {"a": "(inumber)", "d": "ສົ່ງ​ຄືນ​ຊີ​ແຄນ​ທ (secant) ຂອງ​ຈຳນວນ​ຊັບຊ້ອນ", "ad": "​ເປັນ​ຈຳນວນ​ຊັບ​ຊ້ອນ​ທີ່​ທ່ານ​ຕ້ອງການ​ໃຫ້​ຄໍານວນ​ຄ່າ​ຊີ​ແຄນທ"}, "IMSECH": {"a": "(inumber)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກຊີ​ແຄນ​ທ (hyperbolic secant) ຂອງ​ຈຳນວນ​ຊັບຊ້ອນ", "ad": "​ເປັນ​ຈຳນວນ​ຊັບ​ຊ້ອນ​ທີ່​ທ່ານ​ຕ້ອງການ​ໃຫ້​ຄໍານວນ​ຄ່າ​ໄຮ​ເປີ​ບໍລິກຊີ​ແຄນ​ທ"}, "IMSIN": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າຊິນຂອງຈຳນວນຊັບຊ້ອນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການຫາຄ່າຊິນ"}, "IMSINH": {"a": "(inumber)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກຊີນ (hyperbolic sine) ຂອງ​ຈຳນວນ​ຊັບຊ້ອນ", "ad": "​ເປັນ​ຈຳນວນ​ຊັບ​ຊ້ອນ​ທີ່​ທ່ານ​ຕ້ອງການ​ໃຫ້​ຄໍານວນ​ຄ່າ​ໄຮ​ເປີ​ບໍລິກຊີ​ນ"}, "IMSQRT": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າຮາກຂັ້ນສອງຂອງຈຳນວນຊັບຊ້ອນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ທ່ານຕ້ອງການຫາຄ່າຮາກຂັ້ນສອງ"}, "IMSUB": {"a": "(inumber1; inumber2)", "d": "ສົ່ງຄືນຄ່າແຕກຕ່າງລະຫວ່າງຈຳນວນຊັບຊ້ອນສອງຈຳນວນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ຈະນຳ inumber2 ມາລົບອອກ!​ແມ່ນຈຳນວນຊັບຊ້ອນທີ່ຈະລົບອອກຈາກ inumber1"}, "IMSUM": {"a": "(inumber1; [inumber2]; ...)", "d": "ສົ່ງຄືນຜົນລວມຂອງຈຳນວນຊັບຊ້ອນ", "ad": "​ແມ່ນຈຳນວນຊັບຊ້ອນຕັ້ງແຕ່ 1 ຫາ 255 ຈຳນວນທີ່ຈະນຳມາບວກກັນ"}, "IMTAN": {"a": "(inumber)", "d": "ສົ່ງ​ຄືນ​ແທນ​ເຈນ (tangent) ຂອງ​ຈຳນວນ​ຊັບຊ້ອນ", "ad": "​ເປັນ​ຈຳນວນ​ຊັບ​ຊ້ອນ​ທີ່​ທ່ານ​ຕ້ອງການ​ໃຫ້​ຄໍານວນ​ຄ່າ​​ແທນ​ເຈນ"}, "OCT2BIN": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານແປດເປັນເລກຖານສອງ", "ad": "​ແມ່ນເລກຖານແປດທີ່ທ່ານຕ້ອງການປ່ຽນ!​ແມ່ນຈຳນວນອັກຂະ​ລະທີ່ຕ້ອງການໃຊ້"}, "OCT2DEC": {"a": "(number)", "d": "ປ່ຽນ​ເລກຖານແປດເປັນເລກຖານສິບ", "ad": "​ແມ່ນເລກຖານແປດທີ່ທ່ານຕ້ອງການປ່ຽນ"}, "OCT2HEX": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານແປດເປັນເລກຖານສິບຫົກ", "ad": "​ແມ່ນເລກຖານແປດທີ່ທ່ານຕ້ອງການປ່ຽນ!​ແມ່ນຈຳນວນອັກສອນທີ່ຕ້ອງການໃຊ້"}, "DAVERAGE": {"a": "(database; field; criteria)", "d": "ຄ່າສະເລ່ຍໃນຖັນທີ່ຢູ່ໃນລາຍການ ຫຼືຖານຂໍ້ມູນທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸ", "ad": "ເປັນໄລຍະຂອງຫ້ອງທີ່ປະກອບເປັນລາຍການ ຫຼືຖານຂໍ້ມູນ.ຖານຂໍ້ມູນ​ແມ່ນລາຍການຂອງຂໍ້ມູນທີ່ກ່ຽວຂ້ອງກັນ!ເປັນປ້າຍຊື່ຂອງຖັນໃນ​ເຄື່ອງໝາຍ​ວົງ​ຢືມ ຫຼືເປັນໝາຍເລກທີ່ສະແດງຕຳແໜ່ງຂອງຖັນໃນລາຍການ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ບັນຈຸເງື່ອນໄຂທີ່ທ່ານລະບຸ. ຊ່ວງດັ່ງກ່າວນີ້ປະກອບໄປດ້ວຍປ້າຍຊື່ຖັນ ແລະ ຫ້ອງທີ່ຢູ່ທາງລຸ່ມປ້າຍຊື່ສຳລັບເງື່ອນໄຂໜຶ່ງ"}, "DCOUNT": {"a": "(database; field; criteria)", "d": "ນັບຈຳນວນຫ້ອງທີ່ບັນຈຸຕົວເລກໃນຂົງເຂດ (ຖັນ) ຂອງການບັນທຶກໃນຖານຂໍ້ມູນທີ່ສົມທຽບກັບເງື່ອນໄຂທີ່ທ່ານລະບຸ", "ad": "ເປັນຊ່ວງຂອງຫ້ອງທີ່ປະກອບເປັນລາຍການ ຫຼືຖານຂໍ້ມູນ. ຖານຂໍ້ມູນ​ແມ່ນລາຍການ ຂອງຂໍ້ມູນທີ່ກ່ຽວຂ້ອງກັນ!ເປັນປ້າຍຊື່ຂອງຖັນໃນ​ເຄື່ອງໝາຍ​ວົງ​ຢືມ ຫຼືເປັນໝາຍເລກທີ່ສະແດງຕຳແໜ່ງຂອງຖັນໃນລາຍການ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ບັນຈຸ ເງື່ອນໄຂທີ່ທ່ານລະບຸ. ຊ່ວງດັ່ງກ່າວນີ້ປະກອບໄປດ້ວຍປ້າຍຊື່ຖັນ ແລະ ຫ້ອງທີ່ຢູ່ທາງລຸ່ມປ້າຍຊື່ ສຳລັບເງື່ອນໄຂໜຶ່ງ"}, "DCOUNTA": {"a": "(database; field; criteria)", "d": "ນັບຫ້ອງທີ່ບໍ່ວ່າງໃນຂົງເຂດ (ຖັນ)ຂອງການບັນທຶກໃນຖານຂໍ້ມູນທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸ", "ad": "​ເປັນຊ່ວງຂອງຫ້ອງທີ່ປະກອບເປັນລາຍການ ຫຼືຖານຂໍ້ມູນ. ຖານຂໍ້ມູນແມ່ນລາຍການຂອງຂໍ້ມູນທີ່ກ່ຽວຂ້ອງກັນ!ເປັນໄດ້ທັງປ້າຍຊື່ຂອງຖັນໃນ​ເຄື່ອງໝາຍ​ວົງ​ຢືມ ຫຼືໝາຍເລກທີ່ແທນຕຳແໜ່ງຂອງຖັນໃນລາຍການ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ມີເງື່ອນໄຂທີ່ທ່ານລະບຸ. ຊ່ວງດັ່ງກ່າວປະກອບມີປ້າຍຊື່ຖັນ ແລະເງື່ອນໄຂຊື່ງຢູ່ກ້ອງປ້າຍຊື່"}, "DGET": {"a": "(database; field; criteria)", "d": "ແຍກການບັນທຶກອັນດຽວອອກທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸຈາກຖານຂໍ້ມູນ", "ad": "ເປັນຊ່ວງຂອງຫ້ອງທີ່ປະກອບເປັນລາຍການ ຫຼືຖານຂໍ້ມູນ. ຖານຂໍ້ມູນເປັນລາຍການຂອງຂໍ້ມູນທີ່ກ່ຽວຂ້ອງກັນ!ເປັນໄດ້ທັງປ້າຍຊື່ຂອງຖັນໃນ​ເຄື່ອງໝາຍ​ວົງ​ຢືມ ຫຼືຄ່າທີ່ສະແດງຕຳແໜ່ງຂອງຖັນໃນລາຍການ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ມີເງື່ອນໄຂທີ່ທ່ານລະບຸ. ຊ່ວງດັ່ງກ່າວປະກອບມີປ້າຍຊື່ຖັນ ແລະເງື່ອນໄຂເຊິ່ງຢູ່ກ້ອງປ້າຍຊື່"}, "DMAX": {"a": "(database; field; criteria)", "d": "ສົ່ງຄືນຄ່າຕົວເລກທີ່ມີຄ່າໃຫຍ່ທີ່ສຸດໃນຂົງເຂດ (ຖັນ) ຂອງການບັນທຶກໃນຖານຂໍ້ມູນທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸ", "ad": "ເປັນຊ່ວງຂອງຫ້ອງທີ່ປະກອບເປັນລາຍການ ຫຼືຖານຂໍ້ມູນ. ຖານຂໍ້ມູນ​ແມ່ນລາຍການຂໍ້ມູນທີ່ກ່ຽວຂ້ອງກັນ!ເປັນປ້າຍຂອງຖັນໃນ​ເຄື່ອງໝາຍ​ວົງ​ຢືມ ຫຼືເປັນໝາຍເລກ ທີ່ສະແດງຕຳແໜ່ງຂອງຖັນໃນລາຍການ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ບັນຈຸເງື່ອນໄຂທີ່ທ່ານລະບຸ. ຊ່ວງດັ່ງກ່າວນີ້ປະກອບມີປ້າຍຊື່ຖັນ ແລະຫ້ອງທີ່ຢູ່ທາງລຸ່ມປ້າຍຊື່ສຳລັບເງື່ອນໄຂໜຶ່ງ"}, "DMIN": {"a": "(database; field; criteria)", "d": "ສົ່ງຄືນຄ່າຕົວເລກທີ່ມີຄ່ານ້ອຍທີ່ສຸດໃນຂົງເຂດ (ຖັນ) ຂອງການບັນທຶກໃນຖານຂໍ້ມູນທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸ", "ad": "ເປັນຊ່ວງຂອງຫ້ອງທີ່ປະກອບ​ເປັນ​ລາຍ​ການ ຫຼືຖານຂໍ້ມູນ.ຖານຂໍ້ມູນ​ແມ່ນ​ລາຍການຂອງຂໍ້ມູນທີ່ກ່ຽວຂ້ອງກັນ!ເປັນປ້າຍຊື່ຂອງຖັນໃນ​ເຄື່ອງໝາຍ​ວົງ​ຢືມ ຫຼືເປັນໝາຍເລກທີ່ສະແດງຕຳແໜ່ງຂອງຖັນໃນລາຍການ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ບັນຈຸເງື່ອນໄຂທີ່ທ່ານລະບຸ. ຊ່ວງດັ່ງກ່າວນີ້ປະກອບໄປມີປ້າຍຊື່ຖັນ ແລະຫ້ອງທີ່ຢູ່ທາງລຸ່ມປ້າຍຊື່ສຳລັບເງື່ອນໄຂໜຶ່ງ"}, "DPRODUCT": {"a": "(database; field; criteria)", "d": "ຄູນຄ່າໃນຂົງເຂດ (ຖັນ) ຂອງລະບຽບໃນຖານຂໍ້ມູນທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸ", "ad": "ເປັນຊ່ວງຂອງຫ້ອງ​ທີ່​ປະກອບເປັນລາຍການ ຫຼືຖານຂໍ້ມູນ. ຖານຂໍ້ມູນແມ່ນລາຍການຂໍ້ມູນທີ່ກ່ຽວຂ້ອງກັນ!ເປັນໄດ້ທັງປ້າຍຊື່ຂອງຖັນໃນ​ເຄື່ອງໝາຍ​ວົງ​ຢືມ ຫຼືໝາຍເລກທີ່ແທນຕຳແໜ່ງຂອງຖັນໃນລາຍການ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ມີເງື່ອນໄຂທີ່ທ່ານລະບຸ. ຊ່ວງດັ່ງກ່າວປະກອບມີປ້າຍຊື່ຖັນ ແລະເງື່ອນໄຂເຊິ່ງຢູ່ກ້ອງປ້າຍຊື່"}, "DSTDEV": {"a": "(database; field; criteria)", "d": "ຄາດຄະເນການ​ຜິດ​ບ່ຽງມາດຕະຖານໂດຍໃຊ້ຄ່າຕົວຢ່າງຈາກການເຂົ້າເຖິງຖານຂໍ້ມູນທີ່ເລືອກ", "ad": "ເປັນຊ່ວງຂອງຫ້ອງທີ່ປະກອບເປັນລາຍການ ຫຼືຖານຂໍ້ມູນ. ຖານຂໍ້ມູນ​ແມ່ນລາຍການຂອງຂໍ້ມູນທີ່ກ່ຽວຂ້ອງກັນ!ເປັນປ້າຍຊື່ຂອງຖັນໃນ​ເຄື່ອງໝາຍ​ວົງ​ຢືມ ຫຼື ເປັນໝາຍເລກທີ່ສະແດງຕຳແໜ່ງຂອງຖັນໃນລາຍການ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ບັນຈຸເງື່ອນໄຂທີ່ທ່ານລະບຸ.ຊ່ວງ ດັ່ງກ່າວນີ້ປະກອບໄປດ້ວຍປ້າຍຊື່ຖັນ ແລະ ຫ້ອງທີ່ຢູ່ທາງລຸ່ມປ້າຍຊື່ສຳລັບເງື່ອນໄຂໜຶ່ງ"}, "DSTDEVP": {"a": "(database; field; criteria)", "d": "ຄຳນວນຫາການ​ຜິດ​ບ່ຽງມາດຕະຖານໂດຍໃຊ້ປະຊາກອນທັງໝົດ ເຊິ່ງເປັນລາຍການທີ່ຖືກເລືອກມາຈາກຖານຂໍ້ມູນ (ໂດຍໃຊ້ເງື່ອນໄຂ)", "ad": "ເປັນຊ່ວງຂອງຫ້ອງທີ່ປະ​ກອບ​ເປັນລາຍການ ຫຼືຖານຂໍ້ມູນ. ຖານຂໍ້ມູນແມ່ນລາຍການຂອງຂໍ້ມູນທີ່ກ່ຽວຂ້ອງກັນ!ເປັນໄດ້ທັງປ້າຍຊື່ຂອງຖັນໃນ​ເຄື່ອງໝາຍ​ວົງ​ຢືມ ຫຼືໝາຍເລກທີ່ແທນຕຳແໜ່ງຂອງຖັນໃນລາຍການ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ມີເງື່ອນໄຂທີ່ທ່ານລະບຸ. ຊ່ວງດັ່ງກ່າວປະກອບມີປ້າຍຊື່ຖັນ ແລະ ເງື່ອນໄຂຊື່ງຢູ່ກ້ອງປ້າຍຊື່"}, "DSUM": {"a": "(database; field; criteria)", "d": "ເພີ່ມຈຳນວນໃນຂົງເຂດ (ຖັນ) ຂອງການເຮັດບັນທຶກໃນຖານຂໍ້ມູນທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸໄວ້", "ad": "ເປັນຊ່ວງຂອງຫ້ອງທີ່ໃຊ້ເປັນລາຍການ ຫຼືຖານຂໍ້ມູນ. ຖານຂໍ້ມູນ​ແມ່ນລາຍການຂອງຂໍ້ມູນທີ່ກ່ຽວຂ້ອງກັນ!ເປັນປ້າຍຊື່ຂອງຖັນໃນ​ເຄື່ອງໝາຍ​ວົງ​ຢືມ ຫຼືເປັນໝາຍເລກທີ່ສະແດງຕຳແໜ່ງຂອງຖັນໃນລາຍການ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ບັນຈຸເງື່ອນໄຂທີ່ທ່ານລະບຸ. ຊ່ວງດັ່ງກ່າວນີ້ປະກອບໄປດ້ວຍປ້າຍຊື່ຖັນ ແລະ ຫ້ອງທີ່ຢູ່ທາງລຸ່ມປ້າຍຊື່ສຳລັບເງື່ອນໄຂໜຶ່ງ"}, "DVAR": {"a": "(database; field; criteria)", "d": "ຄາດຄະເນຖາານຕ່າງໆ ໃນຕົວຢ່າງຈາກການເລືອກການເຂົ້າເຖິງຖານຂໍ້ມູນ", "ad": "ເປັນຊ່ວງຂອງຫ້ອງທີ່ປະກອບເປັນລາຍການຫຼືຖານຂໍ້ມູນ. ຖານຂໍ້ມູນ​ແມ່ນລາຍການຂອງຂໍ້ມູນທີ່ກ່ຽວຂ້ອງກັນ!ເປັນປ້າຍຊື່ຂອງຖັນໃນ​ເຄື່ອງໝາຍ​ວົງ​ຢືມ ຫຼື ເປັນໝາຍເລກທີ່ສະແດງຕຳແໜ່ງຂອງຖັນໃນລາຍການ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ບັນຈຸເງື່ອນໄຂທີ່ທ່ານລະບຸ. ຊ່ວງດັ່ງກ່າວນີ້ປະກອບໄປດ້ວຍປ້າຍຊື່ຖັນແລະຫ້ອງທີ່ຢູ່ທາງລຸ່ມປ້າຍຊື່ສຳລັບເງື່ອນໄຂໜຶ່ງ"}, "DVARP": {"a": "(database; field; criteria)", "d": "ຄຳນວນຫາຄ່າຕ່າງໆ ໂດຍໃຊ້ປະຊາກອນທັງໝົດ ເຊິ່ງເປັນລາຍການທີ່ຖືກເລືອກມາຈາກຖານຂໍ້ມູນ (ໂດຍໃຊ້ເງື່ອນໄຂ)", "ad": "ເປັນຊ່ວງຂອງຫ້ອງປະກອບ​ເປັນລາຍການ ຫຼືຖານຂໍ້ມູນ. ຖານຂໍ້ມູນແມ່ນລາຍການຂອງຂໍ້ມູນທີ່ກ່ຽວຂ້ອງກັນ!ເປັນໄດ້ທັງປ້າຍຊື່ຂອງຖັນໃນ​ເຄື່ອງໝາຍ​ວົງ​ຢືມ ຫຼື ໝາຍເລກທີ່ແທນຕຳແໜ່ງຂອງຖັນໃນລາຍການ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ມີເງື່ອນໄຂທີ່ທ່ານລະບຸ. ຊ່ວງດັ່ງກ່າວປະກອບມີປ້າຍຊື່ຖັນ ແລະເງື່ອນໄຂເຊິ່ງຢູ່ກ້ອງປ້າຍຊື່"}, "CHAR": {"a": "(number)", "d": "ສົ່ງຄືນອັກຂະ​ລະເຊິ່ງຈະຖືກລະບຸ ໂດຍໝາຍເລກໂຄດຈາກຊຸດອັກຂະ​ລະສຳລັບເຄື່ອງຄອມພິວເຕີຂອງທ່ານ", "ad": "ເປັນຕົວເລກລະຫວ່າງ 1 ແລະ 255 ທີ່ໃຊ້ ລະບຸອັກຂະ​ລະທີ່ທ່ານຕ້ອງການt"}, "CLEAN": {"a": "(text)", "d": "ເອົາຕົວ​ອັກ​ຂະ​ລະທີ່​ບໍ່​ສາ​ມາດ​ພິມ​ໄດ້​ທັງ​ໝົດ​ອອກ​ໄປ​ຈາກ​ຂໍ້​ຄວາມ", "ad": "ແມ່ນ​ຂໍ້​ຄວາມ​ຕາ​ຕະ​ລາງວຽກ​ໃດ​ໜຶ່ງ​ຈາກ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ເອົາ​ຕົວ​ອັກ​ຂະ​ລະທີ່​ບໍ່​ສາ​ມາດ​ພິມ​ໄດ້​ອອກ"}, "CODE": {"a": "(text)", "d": "ສະ​ແດງ​ຜົນ​ລະ​ຫັດ​ຕົວ​ເລກ​ສຳ​ລັບ​ຕົວ​ອັກ​ຂະ​ລະ​ທຳ​ອິດ​ຢູ່​ໃນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ, ຢູ່​ໃນ​ຊຸດ​ຕົວ​ອັກ​ຂະ​​ລະ​ທີ່​​ຄອມ​ພິວ​ເຕີ​ຂອງ​ທ່ານ​ນຳ​ໃຊ້", "ad": "ແມ່ນ​ຂໍ້​ຄວາມ​ສຳ​ລັບ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ລະ​ຫັດ​ຂອງ​ຕົວ​ອັກ​ຂະ​ລະ​ກ່ອນ"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "ເອົາ​ຫຼາຍ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ເຂົ້າ​ເປັນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ດຽວ", "ad": "ແມ່ນ 1 ຫາ 255 ສະ​​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ທີ່​ຈະ​ຖືກ​ເອົາ​ລວມ​ເຂົ້າ​ກັນ​ເປັນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ດຽວ ແລະ​ສາ​ມາດ​ເປັນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ, ຕົວ​ເລກ, ຫຼື​ການ​ອ້າງ​ອີງ​ເຊວ​ດຽວ​ໄດ້"}, "CONCAT": {"a": "(text1; ...)", "d": "ເຊື່ອມຕໍ່ລາຍຊື່ ຫຼື ໄລຍະຂອງສະຕຣິງຂໍ້ຄວາມ", "ad": "ແມ່ນ 1 ຫາ 254 ສະຕຣິງຂໍ້ຄວາມ ຫຼື ໄລຍະເພື່ອເຂົ້າຮ່ວມຫາ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ດ່ຽວ"}, "DOLLAR": {"a": "(number; [decimals])", "d": "ປ່ຽນຕົວເລກຫາຂໍ້ຄວາມ, ໂດຍໃຊ້ຮູບແບບສະກຸນເງິນ", "ad": "ເປັນຕົວເລກ, ການອ້າງອີງຫາຫ້ອງທີ່ບັນຈຸຕົວເລກ, ຫຼືສູດທີ່ສາມາດປະເມີນຄ່າເປັນຕົວເລກໄດ້!ເປັນຈຳນວນຕົວເລກຫຼັກສິບ. ຕົວເລກຈະຖືກປັດຕາມຄວາມຈຳເປັນ; ຖ້າບໍ່ໃສ່ຄ່າຫຍັງ, ຕຳແໜ່ງຫຼັກສິບ = 2"}, "EXACT": {"a": "(text1; text2)", "d": "ກວ​ດ​ເບິ່ງວ່າ ສອງ​​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ຄື​ກັນ​ແທ້​ບໍ່, ແລະ​ສະ​ແດງ​ຜົນ TRUE ຫຼື False. TRUE​ແມ່ນ​ຢຳ ນັ້ນແມ່ນໂຕພິມໃຫຍ່ນ້ອຍມີຜົນຕ່າງກັນ", "ad": "ແມ່ນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ທຳ​ອິດ!ແມ່ນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ທີ​ສອງ"}, "FIND": {"a": "(find_text; within_text; [start_num])", "d": "ສົ່ງຄືນຄ່າຕຳແໜ່ງເລີ່ມຕົ້ນຂອງສາຍຂໍ້ຄວາມທີ່ຢູ່ພາຍໃນຕົວສາຍຂໍ້ຄວາມອີກອັນໜຶ່ງ. ເຊິ່ງຟັງຊັນ FIND ​ນັ້ນແມ່ນໂຕພິມໃຫຍ່ນ້ອຍມີຜົນຕ່າງກັນ", "ad": "ເປັນຂໍ້ຄວາມທີ່ທ່ານຕ້ອງການຄົ້ນຫາ. ໂດຍໃຫ້ໃຊ້​ເຄື່ອງໝາຍ​ວົງ​ຢືມ (ໂດຍບໍ່ໃສ່ຂໍ້ຄວາມ) ເມື່ອຕ້ອງການແທນອັກຂະ​ລະຕົວທຳອິດໃນ Within_text; ບໍ່ອະນຸຍາດໃຫ້ໃຊ້ສັນຍາລັກຕົວແທນກັບຂໍ້ຄວາມທີ່ຕ້ອງການຄົ້ນຫາ!ເປັນຂໍ້ຄວາມເຊິ່ງມີຂໍ້ຄວາມທີ່ທ່ານຕ້ອງການຄົ້ນຫາຢູ່ພາຍໃນ!ເປັນໝາຍເລກທີ່ບອກຕຳແໜ່ງຂອງອັກຂະ​ລະໃນ Within_text ທີ່ຕ້ອງການໃຫ້ເປັນຕຳແໜ່ງເລີ່ມການຄົ້ນຫາໂດຍໃຫ້ອັກຂະ​ລະທຳອິດໃນ Within_text ເປັນອັກຂະ​ລະໝາຍເລກ 1. ຖ້າບໍ່ໃສ່ຄ່າຫຍັງໄວ້, Start_num ຈະ= 1"}, "FINDB": {"a": "(find_text; within_text; [start_num])", "d": "ຊອກຫາ ຊຸດຕົວອັກສອນທີໄດ້ລະບຸໄວ້ ໃນຊຸດຕົວອັກສອນ ແລະ ສໍາລັບຕົວອັກສອນອ double-byte character set (DBCS) ເຊັ່ນພາສາຍີ່ປຸ່ນ, ຈີນ, ເກົາຫຼີແລະອື່ນໆ.", "ad": "ເປັນຂໍ້ຄວາມທີ່ທ່ານຕ້ອງການຄົ້ນຫາ. ໂດຍໃຫ້ໃຊ້​ເຄື່ອງໝາຍ​ວົງ​ຢືມ (ໂດຍບໍ່ໃສ່ຂໍ້ຄວາມ) ເມື່ອຕ້ອງການແທນອັກຂະ​ລະຕົວທຳອິດໃນ Within_text; ບໍ່ອະນຸຍາດໃຫ້ໃຊ້ສັນຍາລັກຕົວແທນກັບຂໍ້ຄວາມທີ່ຕ້ອງການຄົ້ນຫາ!ເປັນຂໍ້ຄວາມເຊິ່ງມີຂໍ້ຄວາມທີ່ທ່ານຕ້ອງການຄົ້ນຫາຢູ່ພາຍໃນ!ເປັນໝາຍເລກທີ່ບອກຕຳແໜ່ງຂອງອັກຂະ​ລະໃນ Within_text ທີ່ຕ້ອງການໃຫ້ເປັນຕຳແໜ່ງເລີ່ມການຄົ້ນຫາໂດຍໃຫ້ອັກຂະ​ລະທຳອິດໃນ Within_text ເປັນອັກຂະ​ລະໝາຍເລກ 1. ຖ້າບໍ່ໃສ່ຄ່າຫຍັງໄວ້, Start_num ຈະ= 1"}, "FIXED": {"a": "(number; [decimals]; [no_commas])", "d": "ປັດຈຳນວນເລກເສດຕາມຕຳແໜ່ງທົດສະນິຍົມທີ່ລະບຸ ແລະ ສົ່ງຄ່າຜົນຮັບກັບຄືນເປັນຂໍ້ຄວາມທີ່ມີ ຫຼືບໍ່ມີເຄື່ອງໝາຍຈຸດ", "ad": "ເປັນຈຳນວນທີ່ທ່ານຕ້ອງການປັດເສດ ແລະປ່ຽນຮູບແບບໄປເປັນຂໍ້ຄວາມ!ເປັນຈຳນວນຂອງຕົວເລກຫຼັກສິບ. ຖ້າບໍ່ໃສ່ຄ່າຫຍັງ, ຕົວເລກຫຼັກສິບ = 2!ເປັນຄ່າຄວາມ​ຈິງ: ຈະບໍ່ສະແດງເຄື່ອງໝາຍຈຸດໃນຂໍ້ຄວາມສົ່ງຄືນທີ່ມີຄ່າ = TRUE; ແຕ່ຈະສະແດງເຄື່ອງໝາຍຈຸດໃນຂໍ້ຄວາມສົ່ງຄືນທີ່ມີຄ່າ = FALSE ຫຼືລືມ"}, "LEFT": {"a": "(text; [num_chars])", "d": "ສະ​ແດງ​ຜົນ​ຈຳ​ນວນ​ລະ​ບຸ​ໄວ້​ຂອງ​ຕົວ​ອັກ​ຂະ​ລະ​ຈາກເລີ່ມ​ຕົ້ນ​ຂອງ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ", "ad": "ແມ່ນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ທີ່​ມີຕົວ​ອັກ​ຂະ​ລະທີ່​ທ່ານ​ຕ້ອງ​ການ​ແຍກ​ອອກ!ໃຫ້​ລະ​ບຸ​ວ່າ​ມີ​ຈັກ​ຕົວ​ອັກ​ຂະ​ລະທີ່​ທ່ານ​ຕ້ອງ​ການ​ແຍກ​ອອກ, 1 ຖ້າ​ໄດ້​ລະ​ເວັ້ນ"}, "LEFTB": {"a": "(text; [num_chars])", "d": "ສະກັດຊຸດຕົວອັກສອນ ຍ່ອຍຈາກ ຊຸດຕົວອັກາອນ ທີ່ລະບຸໄວ້ເລີ່ມຕົ້ນຈາກຕົວອັກສອນຊ້າຍສຸດ ແລະມີຈຸດປະສົງ ສຳ ລັບພາສາຕ່າງໆທີ່ໃຊ້ຊຸດຕົວອັກສອນ double-byte character set (DBCS) ເຊັ່ນພາສາຍີ່ປຸ່ນ, ຈີນ, ເກົາຫຼີແລະອື່ນໆ.", "ad": "ແມ່ນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ທີ່​ມີຕົວ​ອັກ​ຂະ​ລະທີ່​ທ່ານ​ຕ້ອງ​ການ​ແຍກ​ອອກ!ໃຫ້​ລະ​ບຸ​ວ່າ​ມີ​ຈັກ​ຕົວ​ອັກ​ຂະ​ລະທີ່​ທ່ານ​ຕ້ອງ​ການ​ແຍກ​ອອກ, 1 ຖ້າ​ໄດ້​ລະ​ເວັ້ນ"}, "LEN": {"a": "(text)", "d": "ສະ​ແດງ​ຜົນ​ຈຳ​ນວນຕົວ​ອັກ​ຂະ​ລະຢູ່​ໃນ​ສະ​​ຕ​ຣິງ​ຂໍ້​ຄວາມ", "ad": "ແມ່ນ​ຂໍ້​ຄວາມ​ຂອງ​ອັນ​ທີ່​ມີ​ຄວາມ​ຍາວ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ຊອກ​ຫາ. ນັບ​ບ່ອນ​ຍະ​ຫວ່າງ​ເປັນຕົວ​ອັກ​ຂະ​ລະ"}, "LENB": {"a": "(text)", "d": "ປະເມີນຊຸດຕົວອັກສອນ ທີໄດ້ລະບຸໄວ້ ແລະ ຄືນ ຕົວອັກສອນ ທີມີພາສາ double-byte character set (DBCS) like ເປັນຕົ້ນ ຈີນ, ຢີປຸ່ນ ເກົາຫຼີ ແລະ ອື່ນໆ.", "ad": "ແມ່ນ​ຂໍ້​ຄວາມ​ຂອງ​ອັນ​ທີ່​ມີ​ຄວາມ​ຍາວ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ຊອກ​ຫາ. ນັບ​ບ່ອນ​ຍະ​ຫວ່າງ​ເປັນຕົວ​ອັກ​ຂະ​ລະ"}, "LOWER": {"a": "(text)", "d": "ປ່ຽນ​ທຸກ​ຕົວ​ອັກ​ສອນ​ຢູ່​ໃນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ເປັນ​ຕົວ​ພິມ​ນ້ອຍ", "ad": "ແມ່ນ​ຂໍ້​ຄວາມ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ປ່ຽນ​ເປັນ​ຕົວພິມ​ນ້ອຍ. ​ຕົວ​ອັກ​ຂະ​ລະ​ຢູ່​ໃນ​ຂໍ້​ຄວາມ​ທີ່​ບໍ່​ແມ່ນ​ຕົວ​ອັກ​ສອນ​ຈະ​ບໍ່​ຖືກ​ປ່ຽນ"}, "MID": {"a": "(text; start_num; num_chars)", "d": "ສະ​ແດງ​ຜົນຕົວ​ອັກ​ຂະ​ລະຈາກ​ໃຈ​ກາງ​ຂອງ​ສະ​ຕ​ຣິງ, ໂດຍ​ທີ່​ມີ​ຕຳ​ແໜ່ງ​ເລີ່ມ​ຕົ້ນ ແລະ​ຄວາມ​ຍາວ", "ad": "ແມ່ນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ຈາກທີ່​​ທ່ານ​ຕ້ອງ​ການ​ແຍກ​ຕົວ​ອັກ​ຂະ​ລະອອກ!ແມ່ນ​ຕຳ​ແໜ່ງ​ຂອງ​ຕົວ​ອັກ​ຂະ​ລະ​ທຳ​ອິດ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ແຍກ​ອອກ​. ​ຕົວ​ອັກ​ຂະ​ລະ​ທຳ​ອິດ​ຢູ່​ໃນ​ຂໍ້​ຄວາມ​ແມ່ນ 1!ໃຫ້​ລະ​ບຸ​ວ່າ​ມີ​ຈັກ​ຕົວ​ອັກ​ຂະ​ລະໃຫ້​ສະ​ແດງ​ຜົນຈາກ​ຂໍ້​ຄວາມ"}, "MIDB": {"a": "(text; start_num; num_chars)", "d": "ດືງເອົາຕົວອັກສອນທີໄດ້ລະບຸໄວ້ ທີເລີ່ມຈາກ ຈຸດໃດໜື່ງ ແລະ ເພື່ອນໍາໃຊ້ ຊຸດພາສາ double-byte (DBCS) ເປັນຕົ້ນ ຈີນ, ຢີປຸ່ນ ເກົາຫຼີ ແລະ ອື່ນໆ.", "ad": "ແມ່ນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ຈາກທີ່​​ທ່ານ​ຕ້ອງ​ການ​ແຍກ​ຕົວ​ອັກ​ຂະ​ລະອອກ!ແມ່ນ​ຕຳ​ແໜ່ງ​ຂອງ​ຕົວ​ອັກ​ຂະ​ລະ​ທຳ​ອິດ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ແຍກ​ອອກ​. ​ຕົວ​ອັກ​ຂະ​ລະ​ທຳ​ອິດ​ຢູ່​ໃນ​ຂໍ້​ຄວາມ​ແມ່ນ 1!ໃຫ້​ລະ​ບຸ​ວ່າ​ມີ​ຈັກ​ຕົວ​ອັກ​ຂະ​ລະໃຫ້​ສະ​ແດງ​ຜົນຈາກ​ຂໍ້​ຄວາມ"}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "ປ່ຽນ​ຂໍ້ຄວາມ​ເປັນ​ຕົວ​ເລກ​ໃນ​ຮູບ​ແບບ​ທີ່​ເປັນ​ອິດສະຫຼະຈາກ​ລັກສະນະ​ທ້ອງ​ຖິ່ນ", "ad": "​ແມ່ນ​ສະຕຣິງທີ່​ສະ​ແດງ​ເຖິງ​ຕົວ​ເລກທີ່ທ່ານ​ຕ້ອງການ​ປ່ຽນ!​ແມ່ນ​ອັກຂະ​ລະ​ທີີ່​ໃຊ້​ເປັນ​ຕົວ​ແຍກ​ເລ​ກທດສະ​ນິຍົມ​ໃນ​ສະຕຣິງ!​ແມ່​ນອັກຂະ​ລະ​ທີ່​ໃຊ້​ເປັນ​ຕົວ​ແຍ​ກກຸ່ມ​ໃນ​ສະຕຣິງ"}, "PROPER": {"a": "(text)", "d": "ປ່ຽນ​ສະຕຣິງຂໍ້ຄວາມ​ເປັນ​ຕົວ​ພິມ​ທີ່​ເໝາະ​ສົມ; ຕົວ​ອັກສອນ​ທຳ​ອິດ​ໃນ​ແຕ່ລະ​ຄໍາ​ສັບ​ເປັນ​ຕົວ​ພິມ​ໃຫຍ່ ​ແລະ​ຕົວ​ອັກສອນ​ອື່ນ​ທັງ​ໝົດ​ເປັນ​ຕົວ​ພິມ​ນ້ອຍ", "ad": "​ແມ່ນ​ຂໍ້ຄວາມ​ທີ່​ມີ​ໃນ​ວົງ​ຢືມ, ສູດ​ທີ່​ສົ່ງ​ຄືນ​ຂໍ້ຄວາມ ຫຼືການ​ອ້າງ​ອີງ​ເຖິງ​ຫ້ອງ​ທີ່​ປະກອບ​ມີຂໍ້ຄວາມທີ່​ຖືກ​ເຮັດ​ໃຫ້​ເປັນ​ຕົວ​ພິມ​ໃຫຍ່​ບາງ​ສ່ວນ"}, "REPLACE": {"a": "(old_text; start_num; num_chars; new_text)", "d": "ແທນທີ່ບາງສ່ວນຂອງສາຍຂໍ້ຄວາມດ້ວຍສາຍຂໍ້ຄວາມອື່ນ", "ad": "ເປັນຂໍ້ຄວາມທີ່ທ່ານຕ້ອງການແທນທີ່ອັກຂະ​ລະບາງຕົວ!ເປັນຕຳແໜ່ງຂອງອັກຂະ​ລະໃນ Old_text ທີ່ທ່ານຕ້ອງການແທນທີ່ດ້ວຍ New_text!ເປັນຈຳນວນຂອງອັກຂະ​ລະໃນ Old_text ທີ່ທ່ານຕ້ອງການແທນທີ່!ເປັນຂໍ້ຄວາມທີ່ຈະແທນທີ່ອັກຂະ​ລະໃນ Old_text"}, "REPLACEB": {"a": "(old_text; start_num; num_chars; new_text)", "d": "ປ່ຽນແທນ ຊຸດຕົວອັກສອນ, ທີໄດ້ລະບຸ ຈໍານວນຕົວອັກສອນ ແລະ ຈຸດເລີ່ມຕົ້ນ, ປ່ຽນເປັນ ຊຸດຕົວອັກສອນໃໝ່ ເພື່ອນໍາໃຊ້ ຊຸດພາສາ double-byte (DBCS) ເປັນຕົ້ນ ຈີນ, ຢີປຸ່ນ ເກົາຫຼີ ແລະ ອື່ນໆ.", "ad": "ເປັນຂໍ້ຄວາມທີ່ທ່ານຕ້ອງການແທນທີ່ອັກຂະ​ລະບາງຕົວ!ເປັນຕຳແໜ່ງຂອງອັກຂະ​ລະໃນ Old_text ທີ່ທ່ານຕ້ອງການແທນທີ່ດ້ວຍ New_text!ເປັນຈຳນວນຂອງອັກຂະ​ລະໃນ Old_text ທີ່ທ່ານຕ້ອງການແທນທີ່!ເປັນຂໍ້ຄວາມທີ່ຈະແທນທີ່ອັກຂະ​ລະໃນ Old_text"}, "REPT": {"a": "(text; number_times)", "d": "​ຊ້ຳ​ຄືນ​ຂໍ້​ຄວາມ​ຈຳ​ນວນ​ຄັ້ງ​ທີ່​ໄດ້​ໃຫ້. ໃຊ້​ຊ້ຳ​ຄືນ​ເພື່ອ​ຕື່ມ​ເຊວ​ດ້ວຍ​ຈຳ​ນວນ​ຕົວ​ຢ່າງ​ຂອງ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ", "ad": "ແມ່ນ​ຂໍ້​ຄວາມ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ຊ້ຳ​ຄືນ!ແມ່ນ​ຈຳ​ນວນ​ບວກ​ລະ​ບຸ​ຈຳ​ນວນ​ຄັ້ງ​ເພື່ອ​ຊ້ຳ​ຄືນ​ຂໍ້​ຄວາມ"}, "RIGHT": {"a": "(text; [num_chars])", "d": "ສະ​ແດງ​ຜົນ​ຈຳ​ນວນ​ລະ​ບຸ​ໄວ້​ຂອງ​ຕົວ​ອັກ​ຂະ​ລະ​ຈາກ​ທ້າຍ​ຂອງ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ", "ad": "ແມ່ນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ທີ່​ມີຕົວ​ອັກ​ຂະ​ລະທີ່​ທ່ານ​ຕ້ອງ​ການ​ແຍກ​ອອກ!ໃຫ້​ລະ​ບຸ​ວ່າ​ມີ​ຈັກ​ຕົວ​ອັກ​ຂະ​ລະທີ່​ທ່ານ​ຕ້ອງ​ການ​ແຍກ​ອອກ, 1 ຖ້າ​ໄດ້​ລະ​ເວັ້ນ"}, "RIGHTB": {"a": "(text; [num_chars])", "d": "ດືງເອົາຕົວອັກສອນທີໄດ້ລະບຸໄວ້ ທີເລີ່ມຈາກ ຂວາສຸດ ແລະ ເພື່ອນໍາໃຊ້ ຊຸດພາສາ double-byte (DBCS) ເປັນຕົ້ນ ຈີນ, ຢີປຸ່ນ ເກົາຫຼີ ແລະ ອື່ນໆ.", "ad": "ແມ່ນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ທີ່​ມີຕົວ​ອັກ​ຂະ​ລະທີ່​ທ່ານ​ຕ້ອງ​ການ​ແຍກ​ອອກ!ໃຫ້​ລະ​ບຸ​ວ່າ​ມີ​ຈັກ​ຕົວ​ອັກ​ຂະ​ລະທີ່​ທ່ານ​ຕ້ອງ​ການ​ແຍກ​ອອກ, 1 ຖ້າ​ໄດ້​ລະ​ເວັ້ນ"}, "SEARCH": {"a": "(find_text; within_text; [start_num])", "d": "ສົ່ງຄືນໝາຍ​ເລກ​ອັກຂະ​ລະທີ່​ອັກ​ຂະ​ລະຖືກຄົ້ນພົບເປັນອັນດັບທຳອິດໃນສາຍອັກຂະ​ລະຫຼືຂໍ້ຄວາມສະ​ເພາະ, ການຄົ້ນຫາຈະຖືກເຮັດຈາກຊ້າຍໄປຂວາ (ໂຕພິມໃຫຍ່ນ້ອຍບໍ່ມີຜົນຕ່າງກັນ)", "ad": "ເປັນຂໍ້ຄວາມທີ່ທ່ານຕ້ອງການຄົ້ນຫາ. ທ່ານສາມາດໃຊ້ ? ແລະ * ເຄື່ອງໝາຍແທນອັກຂະ​ລະ; ໃຫ້ໃຊ້ ~? ແລະ ~* ເພື່ອຄົ້ນຫາອັກຂະ​ລະ ? ແລະ * !ເປັນຂໍ້ຄວາມທີ່ທ່ານຕ້ອງການຄົ້ນຫາ Find_text!ເປັນໝາຍ​ເລກອັກຂະ​ລະໃນ Within_text, ນັບຈາກເບື້ອງຊ້າຍ, ທີ່ທ່ານຕ້ອງການໃຫ້ເລີ່ມການຄົ້ນຫາຈາກ ຖ້າຖ້ບໍ່ໃສ່ຄ່າຫຍັງໄວ້, ໝາຍ​ເລກ​ອັກຂະ​ລະໝາຍເລກ 1 ຈະຖືກໃຊ້ໃນການເລີ່ມຕົ້ນ"}, "SEARCHB": {"a": "(find_text; within_text; [start_num])", "d": "ຄືນ ສະຖານທີ ຂອງຊຸດອັກສອນຍ່ອຍ ທີໄດ້ລະບຸໄວ້ ຈາກຊຸດຕົວອກາອນຫຼັກ. ແລະ ສໍາລັບຕົວອັກສອນທີເປັນ double-byte character set (DBCS) ເຊັ່ນພາສາຍີ່ປຸ່ນ, ຈີນ, ເກົາຫຼີແລະອື່ນໆ.", "ad": "ເປັນຂໍ້ຄວາມທີ່ທ່ານຕ້ອງການຄົ້ນຫາ. ທ່ານສາມາດໃຊ້ ? ແລະ * ເຄື່ອງໝາຍແທນອັກຂະ​ລະ; ໃຫ້ໃຊ້ ~? ແລະ ~* ເພື່ອຄົ້ນຫາອັກຂະ​ລະ ? ແລະ * !ເປັນຂໍ້ຄວາມທີ່ທ່ານຕ້ອງການຄົ້ນຫາ Find_text!ເປັນໝາຍ​ເລກອັກຂະ​ລະໃນ Within_text, ນັບຈາກເບື້ອງຊ້າຍ, ທີ່ທ່ານຕ້ອງການໃຫ້ເລີ່ມການຄົ້ນຫາຈາກ ຖ້າຖ້ບໍ່ໃສ່ຄ່າຫຍັງໄວ້, ໝາຍ​ເລກ​ອັກຂະ​ລະໝາຍເລກ 1 ຈະຖືກໃຊ້ໃນການເລີ່ມຕົ້ນ"}, "SUBSTITUTE": {"a": "(text; old_text; new_text; [instance_num])", "d": "ປ່ຽນ​ແທນ​ຂໍ້​ຄວາມ​ທີ່​ມີ​ຢູ່​ດ້ວຍ​ຂໍ້​ຄວາມ​ໃໝ່​ຢູ່​ໃນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ", "ad": "ແມ່ນ​ຂໍ້​ຄວາມ ຫຼື​ການ​ອ້າງ​ອີງ​ຫາ​ເຊວ​ທີ່​ມີ​ຂໍ້​ຄວາມ​ໃນ​ທີ່​ເຊິ່ງ​ທ່ານ​ຕ້ອງ​ການ​ປ່ຽນ​ແທນຕົວ​ອັກ​ຂະ​ລະ!ແມ່ນ​ຂໍ້​ຄວາມ​ທີ່​ມີ​ຢູ່​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ປ່ຽນ​ເລັກ. ຖ້າ​ກໍ​ລະ​ນີ​ຂອງຂໍ້​ຄວາມ_ເກົ່າ​ບໍ່​ຖືກ​ກັບ​ກໍ​ລະ​ນີ​ຂອງ​ຂໍ້​ຄວາມ, ຕົວ​ປ່ຽນ​ແທນ​ຈະ​ບໍ່​ປ່ຽນ​ແທນ​ຂໍ້​ຄວາມ!ແມ່ນ​ຂໍ້​ຄວາມ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ປ່ຽນ​ແທນ​ຂໍ້​ຄວາມ_ເກົ່າ​ດ້ວຍ!ໃຫ້​ລະ​ບຸ​ວ່າ ການ​ເກີດ​ຂຶ້ນ​ອັນ​ໃດ​ຂອງ​ຂໍ້​ຄວາມ​ເກົ່າ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ປ່ຽນ​ແທນ. ຖ້າ​ໄດ້​ລະ​ເວັ້ນ, ທຸກ​ຕົວ​ຢ່າງ​ຂອງ​ຂໍ້​ຄວາມ_ເກົ່າ​ຖືກ​ປ່ຽນ​ແທນ"}, "T": {"a": "(value)", "d": "ກວດ​ເບິ່ງວ່າ ຄ່າ​ແມ່ນ​ຂໍ້​ຄວາມ​ບໍ, ແລະ​ສະ​ແດງ​ຜົນ​ຂໍ້​ຄວາມ​ຖ້າ​ມັນ​ແມ່ນ, ຫຼື​ສະ​ແດງ​ຜົນ​ວົງ​ຢືມ​ຄູ່ (ຂໍ້​ຄວາມ​ຫວ່າງ​ເປົ່າ) ຖ້າ​ມັນ​ບໍ່​ແມ່ນ", "ad": "ແມ່ນ​ຄ່າ​​ທີ່​ຈະ​ທົດ​ສອບ"}, "TEXT": {"a": "(value; format_text)", "d": "ປ່ຽນຄ່າເປັນຂໍ້ຄວາມຢູ່ໃນຮູບແບບຕົວເລກສະເພາະ", "ad": "ແມ່ນຕົວເລກ, ສູດຄຳນວນທີ່ປະເມີນຜົນເປັນຄ່າຕົວເລກ, ຫຼື ການອ້າງອີງໄປຫາເຊວທີ່ມີຄ່າເປັນຕົວເລກ!ແມ່ນຮູບແບບຕົວເລກຢູ່ໃນຮູບແບບຂໍ້ຄວາມຈາກ ກ່ອງໝວດໝູ່ຢູ່ເທິງແຖບຕົວເລກ ຢູ່ໃນກ່ອງໂຕ້ຕອບຈັດຮູບແບບເຊວ"}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "ເຊື່ອມຕໍ່ລາຍຊື່ ຫຼື ໄລຍະຂອງສະຕຣິງຂໍ້ຄວາມໂດຍໃຊ້ເຄື່ອງໝາຍຂັ້ນ", "ad": "ຕົວອັກສອນ ຫຼື ສະຕຣິງເພື່ອແຊກລະຫວ່າງລາຍການຂໍ້ຄວາມແຕ່ລະອັນ!ຖ້າ TRUE(default), ໃຫ້ລະເວັ້ນເຊວຫວ່າງເປົ່າ!ແມ່ນສະຕຣິງຂໍ້ຄວາມ ຫຼື ຊ່ວງຂອງສະຕຣິງຂໍ້ຄວາມ 1 ຫາ 252 ລາຍການທີ່ຈະລວມເຂົ້າ"}, "TRIM": {"a": "(text)", "d": "ເອົາ​ທຸກ​ບ່ອນ​ຍະ​ຫວ່າງ​ອອກ​ໄປ​ຈາກ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ ຍົກ​ເວັ້ນ​ສຳ​ລັບ​ບ່ອນ​ຍະ​ຫວ່າງ​ລະ​ຫວ່າງ​ຄຳ​ເວົ້າ", "ad": "ແມ່ນ​ຂໍ້​ຄວາມ​ຈາກ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ໃຫ້​ເອົາ​ບ່ອນ​ຍະ​ຫວ່າງອອກ​ໄປ"}, "UNICHAR": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ອັກຂະ​ລະ Unicode ທີ່​ອ້າງ​ອີງ​​ໂດຍ​ຄ່າຕົວ​ເລກ​ທີ່​ໃຫ້​ໄວ້", "ad": "​ແມ່ນ​ຕົວ​ເລກ Unicode ທີ່​ສະ​ແດງ​ເຖິງ​ອັກຂະ​ລະ​ໃດ​ໜຶ່ງ"}, "UNICODE": {"a": "(text)", "d": "ສົ່ງ​ຄືນ​ຕົວ​ເລກ (ຈຸດ​ລະຫັດ) ທີ່​ກົງ​ກັບ​ອັກຂະ​ລະ​ທຳ​ອິດ​ຂອງ​ຂໍ້ຄວາມ", "ad": "​ແມ່ນ​ອັກຂະ​ລະ​ທີ່​ທ່ານ​ຕ້ອງການ​ຄ່າ Unicode ຂອງ​ມັນ"}, "UPPER": {"a": "(text)", "d": "ປ່ຽນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ເປັນ​ຕົວ​ພິມ​ໃຫຍ່​ທັງ​ໝົດ", "ad": "ແມ່ນ​ຂໍ້​ຄວາມ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ປ່ຽນ​ເປັນ​ຕົວ​ພິມ​ໃຫຍ່, ການ​ອ້າງ​ອີງ ຫຼື​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ"}, "VALUE": {"a": "(text)", "d": "ປ່ຽນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ທີ່​ແທນ​ຕົວ​ເລກ​ເປັນ​ຕົວ​ເລກ", "ad": "ແມ່ນ​ຂໍ້​ຄວາມ​ທີ່​ຢູ່​ໃນ​ວົງ​ຢືມ ຫຼື​ການ​ອ້າງ​ອີງ​ກັບ​ເຊວ​ທີ່​ມີ​ຂໍ້​ຄວາມ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ປ່ຽນ"}, "AVEDEV": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າສະເລ່ຍຂອງການຜິດ​ບ່ຽງສົມບູນຂອງຈຸດຂໍ້ມູນຈາກຄ່າສະເລ່ຍຂໍ້ມູນທັງໝົດ. ຂໍ້ພິສູດທີ່ລະບຸສາມາດເປັນໄດ້ທັງຕົວເລກ ຫຼື ຊື່, ອາ​ເຣຍ໌, ຫຼືການອ້າງອີງທີ່ມີຕົວເລກ", "ad": "​ແມ່ນຂໍ້ພິສູດ 1 ເຖິງ 255 ຄ່າສໍາລັບຄ່າສະເລ່ຍທີ່ຕ້ອງການຂອງການຜິດ​ບ່ຽງສົມບູນ"}, "AVERAGE": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າສະເລ່ຍ (ຄ່າສະເລ່ຍເລກຄະນິດ) ຂອງຂໍ້ພິສູດທັງໝົດ, ເຊິ່ງສາມາດເປັນຕົວເລກ ຫຼື ຊື່, ອາ​ເຣຍ໌, ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກຢູ່ນຳ", "ad": "​ແມ່ນຂໍ້ພິສູດທີ່ເປັນຕົວເລກ 1 ເຖິງ 255 ຕົວເຊິ່ງທ່ານຕ້ອງການຫາຄ່າສະເລ່ຍ"}, "AVERAGEA": {"a": "(value1; [value2]; ...)", "d": "ສະ​ແດງ​ຜົນສະ​ເລ່ຍ (ຄ່າ​ສະ​ເລ່ຍເລກ​ຄະ​ນິດ) ຂອງ​​ຂໍ້​ພິ​ສູດ, ຂໍ້​ຄວາມ​ການ​ປະ​ເມີນ ແລະ False ຢູ່​ໃນ​​ຂໍ້​ພິ​ສູດ​ເປັນ 0; ການ​ປະ​ເມີນ​ TRUE ເປັນ 1. ​ຂໍ້​ພິ​ສູດສາ​ມາດ​ເປັນ​ຕົວ​ເລກ, ຊື່, ແຖວ​ລຳ​ດັບ, ຫຼື​ການ​ອ້າງ​ອີງ", "ad": "ແມ່ນ 1 ຫາ 255 ​ຂໍ້​ພິ​ສູດ ທີ່​ທ່ານ​ຕ້ອງ​ການ​ຄ່າ​ສະ​ເລ່ຍ"}, "AVERAGEIF": {"a": "(range; criteria; [average_range])", "d": "ຫາຄ່າສະເລ່ຍ (ເລຂາຄະນິດ) ສຳລັບຫ້ອງທີ່ຖືກລະບຸ ໂດຍຊຸດຂອງເງື່ອນໄຂ ຫຼືເກນການຕັດສິນທີ່ກຳນົດໃຫ້", "ad": "ແມ່ນຊ່ວງຂອງເຊວທີ່ທ່ານຕ້ອງການໃຫ້ຖືກປະເມີນຄ່າ ສຳລັບເງື່ອນໄຂທີ່ລະບຸ!ແມ່ນເງື່ອນໄຂ ຫຼື ເກນການຕັດສິນໃນຮູບແບບຂອງຕົວເລກ, ສຳນວນ ຫຼື ຂໍ້ຄວາມ ທີ່ກຳນົດວ່າເຊວໃດຈະຖືກໃຊ້ໃນການຫາຄ່າສະເລ່ຍ!ແມ່ນເຊວຕົວຈິງທີ່ຈະໃຊ້ໃນການຫາຄ່າສະເລ່ຍ.ຖ້າບໍ່ກຳນົດຫ້ອງໃນຊ່ວງເວລາຈະຖືກໃຊ້"}, "AVERAGEIFS": {"a": "(average_range; criteria_range; criteria; ...)", "d": " ຫາຄ່າສະເລ່ຍ (ເລຂາຄະນິດ) ສຳລັບຫ້ອງທີ່ຖືກລະບຸ ໂດຍຊຸດຂອງເງື່ອນໄຂ ຫຼືເກນການຕັດສິນທີ່ກໍານົດໃຫ້", "ad": "​ແມ່ນຫ້ອງທີ່ຈະໃຊ້ໃນການຫາຄ່າສະເລ່ຍ!​ແມ່ນຊ່ວງຂອງຫ້ອງທີ່ທ່ານຕ້ອງການໃຫ້ຖືກປະເມີນຄ່າສຳລັບເງື່ອນໄຂທີ່ລະບຸ!​ແມ່ນເງື່ອນໄຂ ຫຼືເກນການຕັດສິນ ໃນຮູບແບບຂອງຕົວເລກ,ສຳນວນ,ຫຼືຂໍ້ຄວາມທີ່ກຳນົດວ່າຫ້ອງໃດຈະຖືກ​ໃຊ້ໃນການຫາຄ່າສະເລ່ຍ"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຟັງ​ຊັນ​ຄວາມ​ໜາ​ແໜ້ນຂອງ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ​ເບ​ຕ້າ​ສະ​ສົມ", "ad": "​ເປັນ​ຄ່າ​ລະຫວ່າງ A ກັບ B ທີ່​ຈະ​ໃຊ້​ໃນ​ການ​ປະ​ເມີນ​ຫາ​ຄ່າຟັງ​ຊັນ!​ແມ່ນ​ພາຣາມິເຕີ​ໃນ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ມີຄ່າ​ຫຼາຍກວ່າ 0!​ເປັນ​ພາຣາມິເຕີ​ໃນ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ມີຄ່າ​ຫຼາຍກວ່າ 0!​ແມ່ນ​ຂອບ​ຕໍ່າ​ສຸດ​ໃນ​ຊ່ວງ​ຂອງ x ທີ່​ໃສ່ ຫຼືບໍ່​ໃສ່​ກໍ່​ໄດ້. ຖ້າ​ບໍ່​ໃສ່​ຄ່າ​​ໃດ, A = 0!​ແມ່ນ​ຂອບ​ສູງ​ສຸດ​ໃນ​ຊ່ວງ​ຂອງ x ທີ່​ໃສ່ ຫຼືບໍ່​ໃສ່​ກໍ່​ໄດ້. ຖ້າ​ບໍ່​ໃສ່​ຄ່າ​ໃດ, B = 1"}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ຟັງ​ຊັນ​ຄວວາ​ມໜາ​ແໜ້ນ​ຂອງ​ຄວາມ​ເປັນ​​ໄປ​ໄດ້​ແບບ​ເບ​ຕ້າ​ສະ​ສົມ (BETA.DIST)", "ad": "​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ແບບ​ເບ​ຕ້າ!​ແມ່ນ​ຄ່າ​ພາຣາມິເຕີ​ຂອງ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ມີຄ່າ​ຫຼາຍກວ່າ 0!​ແ​ມ່ນພາຣາມິເຕີ​ຂອງ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ມີຄ່າ​ຫຼາຍກວ່າ 0!​ແມ່ນ​ຄ່າ​ທີ່​ຈະ​ລະບຸຫຼືບໍ່​ກໍ່​ໄດ້ ​ເຊິ່ງ​ເປັນ​ຂອບ​ລຸ່ມ​ຂອງ​ຊ່ວງ x. ຖ້າ​ບໍ່​ກໍານົດ, A = 0!​ແມ່ນ​ຄ່າ​ທີ່​ຈະ​ລະບຸ​ຫຼືບໍ່​ກໍ່​ໄດ້ ​ເຊິ່ງກໍານົດ​ຂອບ​ເທິງ​ຂອງ​ຊ່ວງ x. ຖ້າ​ບໍ່​ກຳນົດ, B = 1"}, "BETA.DIST": {"a": "(x; alpha; beta; cumulative; [A]; [B])", "d": "ສົ່ງ​ຄືນ​ຟັງ​ຊັນ​ການ​​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​​ໄດ້​ແບບ​ເບ​ຕ້າ", "ad": "​ເປັນ​ຄ່າ​ຄວາມ​ຈິງ​ລະຫວ່າງ A ຫາ B ທີ່​ໃຊ້​ປະ​ເມີນ​ຜົນ​ຟັງ​ຊັນ!​​ແ​ມ່ນ​ພາຣາມິເຕີຂອງ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ມີຄ່າ​ຫຼາຍກວ່າ 0!​​ແມ່ນ​ພາຣາມິເຕີ​ຂອງ​ການ​ແຈກ​ຢາຍ​ທີ່​ຕ້ອງ​ມີຄ່າ​ຫຼາຍກວ່າ 0!​​ແມ່ນ​ຄ່າຄວາມ​ຈິງ: ​ໃຊ້​ຄ່າ TRUE ສໍາລັບ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ຄວາ​ມຖີ່​ແບບ​ສ​ະສົມ; ຫຼື​ໃຊ້ FALSE ສໍາລັບ​ຟັງ​ຊັນ​ຄວາມ​ໜາ​ແໜ້ນ​ຂອງ​ຄວາມ​ເປັນ​ໄປ​ໄດ້!​ແມ່ນ​ຄ່າ​ທີ່​ຈະ​ລະບຸ ຫຼືບໍ່​ລະບຸ​ກໍ່​ໄດ້ ​ເຊິ່ງ​ເປັນ​ຂອບ​ລຸ່ມ​ຂອງ​ຊ່ວງ x. ຖ້າ​ບໍ່​ກໍານົດ​ຄ່າ​ໃດ, A = 0!​ແມ່ນ​ຄ່າ​ທີ່​ຈະ​ລະບຸ ຫຼືບໍ່​ລະບຸ​ກໍ່​ໄດ້ ​ເຊິ່ງ​ເປັນ​ຂອບ​ເທິງ​ຂອງຊ່ວງ x ຖ້າ​ບໍ່​ກຳນົດ​ຄ່າ​ໃດ, B = 1"}, "BETA.INV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ຟັງ​ຊັນ​ຄວວາ​ມໜາ​ແໜ້ນ​ຂອງ​ຄວາມ​ເປັນ​​ໄປ​ໄດ້​ແບບ​ເບ​ຕ້າ​ສະ​ສົມ (BETA.DIST)", "ad": "​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ແບບ​ເບ​ຕ້າ!​ແມ່ນ​ຄ່າ​ພາຣາມິເຕີ​ຂອງ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ມີຄ່າ​ຫຼາຍກວ່າ 0!​ແ​ມ່ນພາຣາມິເຕີ​ຂອງ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ມີຄ່າ​ຫຼາຍກວ່າ 0!​ແມ່ນ​ຄ່າ​ທີ່​ຈະ​ລະບຸຫຼືບໍ່​ກໍ່​ໄດ້ ​ເຊິ່ງ​ເປັນ​ຂອບ​ລຸ່ມ​ຂອງ​ຊ່ວງ x. ຖ້າ​ບໍ່​ກໍານົດ, A = 0!​ແມ່ນ​ຄ່າ​ທີ່​ຈະ​ລະບຸ​ຫຼືບໍ່​ກໍ່​ໄດ້ ​ເຊິ່ງກໍານົດ​ຂອບ​ເທິງ​ຂອງ​ຊ່ວງ x. ຖ້າ​ບໍ່​ກຳນົດ, B = 1"}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "ສົ່ງ​ຄືນຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ທະວີ​ນາມ​ສໍາລັບ​ແຕ່​ລະ​ຊຸດ​ຂອງ​ຜົນ​ການ​ທົດ​ລອງ", "ad": "​ແມ່ນ​ຈໍານວນ​ຄັ້ງ​ທີ່​ໄດ້​ຮັບ​ຜົນສຳ​ເລັດ​ໃນ​ການ​ທົດ​ລອງທັງ​ໝົດ!​ເປັນ​ຈໍານວນ​ຄັ້ງ​ທີ່​ທົດ​ລອງ​ທັງ​ໝົດ​ເຊິ່ງ​ເປັນ​ອິດສະຫຼະຕໍ່​ກັນ!​​ແມ່ນ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ຈະ​​ໄດ້​ຮັບ​ຜົນສຳ​ເລັດ​ໃນ​ການ​ທົດ​ລອງ​ແຕ່​ລະ​ຄັ້ງ!​ເປັນ​ຄ່າ​ຄວາມ​ຈິງ: ​ໃຊ້ TRUE ສໍາລັບ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ຄວາມຖີ່​ແບບ​ສະ​ສົມ; ​ໃຊ້ FALSE ສໍາລັບ​ຟັງ​ຊັນ​ມວນຄວາມ​ເປັນ​ໄປ​ໄດ້"}, "BINOM.DIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "ສົ່ງຄືນຄ່າຄວາມເປັນໄປໄດ້ ຂອງການແຈກຢາຍທາງຄະນິດສາດສໍາລັບແຕ່ລະຊຸດຂອງຜົນການທົດລອງ", "ad": "ເປັນຈຳນວນເທື່ອທີ່ໄດ້ຮັບຜົນສຳເລັດໃນການທົດລອງທັງໝົດ!ເປັນຈຳນວນເທື່ອທີ່ທົດລອງທັງໝົດຊື່ງເປັນອິດສະຫຼະຕໍ່ກັນ!ແມ່ນຄວາມເປັນໄປໄດ້ຂອງການທີ່ໄດ້ຮັບຜົນສຳເລັດໃນການທົດລອງແຕ່ລະຄັ້ງ!ແມ່ນຄ່າຄວາມ​ຈິງ: ສຳລັບການແຈກຢາຍຟັງຊັນແບບສະສົມ (cumulative distribution function) , ການໃຊ້ TRUE; ການໃຊ້ FALSE ສຳລັບຄວາມເປັນໄປໄດ້ຂອງຟັງຊັນມວນສານ"}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ຂອງ​ຜົນ​ການ​ທົດ​ລອງ​ໂດຍ​ໃຊ້​ການ​ແຈກ​ຢາຍ​ທະວີ​ນາມ", "ad": "​ແມ່ນ​ຈໍານວນ​ຂອງ​ກາ​ນທົດ​ລອງ​ທີ່​ເປັນ​ອິດສະຫຼະຕໍ່​ກັນ!​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ຂອງ​ຄວາ​ມສໍາ​ເລັດ​ໃນ​ການ​ທົດ​ລອງ​ແຕ່​ລະ​ຄັ້ງ!​ແມ່ນ​ຈໍານວນ​ຄວາມ​ສໍາ​ເລັດ​ໃນ​ການ​ທົດ​ລອງ!ຖ້າ​ໃຫ້​ໄວ້ ຟັງ​ຊັນ​ນີ້​ສົ່ງ​ກັບ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ຈໍານວນ​ຂອງ​ການ​ທົດ​ລອງ​ທີ່​ສໍາ​ເລັດ​ຈະ​ຢູ່ລະຫວ່າງ number_s ​ແລະ number_s2"}, "BINOM.INV": {"a": "(trials; probability_s; alpha)", "d": "ສະ​ແດງ​ຜົນ​ຄ່າ​ນ້ອຍ​ສຸດ​ສຳ​ລັບ​ອັນ​ທີ່​ການ​ກະ​ຈາຍ​ໄບ​ນໍ​ມຽວ​ສະ​ນົມ​ໃຫຍ່ກວ່າ ຫຼື​ເທົ່າ​ກັບ​ຄ່າ​ມາດ​ຖານ", "ad": "ແມ່ນ​ຈຳ​ນວນ​ຂອງ​ການ​ທົດ​ລອງ​ບີ​ນູ​ລ​ລິ!ແມ່ນ​ໂອ​ກາດ​ຂອງ​ຄວາມ​ສຳ​ເລັດ​ຢູ່​ໃນ​ແຕ່​ລະ​ການ​ທົດ​ລອງ, ຕົວ​ເລກ​ລະ​ຫວ່າງ 0 ຫາ 1 ລວມ​ໃນ​ນັ້ນ!ແມ່ນ​ຄ່າ​ມາດ​ຖານ, ຕົວ​ເລກ​ລະ​ຫວ່າງ 0 ຫາ 1 ລວມ​ໃນ​ນັ້ນ"}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາ​ມ​ເປັນ​ໄປ​ໄດ້​​ດ້ານ​ຂວາ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ໄຄ-ສະ​ແຄຣ", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ປະ​ເມີນ​ຜົນ​ການ​ແຈກ​ຢາຍ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ທີ່​ບໍ່​ແມ່ນ​ຈໍານວນ​ລົບ!​ແມ່ນ​ຈໍານວນ​ອົງສາ​ຂອງ​ຄວາມ​ເປັນ​ອິດສະຫຼະ ​ເຊິ່ງ​ແມ່ນ​ຕົວ​ເລກ​ລະຫວ່າງ 1 ຫາ 10^10 ​ທີ່ບໍ່​ລວມ​ເອົາ 10^10"}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "nສົ່ງ​ຄືນຄ່າປິ້ນຄືນຂອງຄວາມເປັນໄປໄດ້​ເບື້ອງຂວາຂອງການແຈກຢາຍແບບໄຄ-ສະແຄຣ", "ad": "ແມ່ນຄ່າຄວາມເປັນໄປໄດ້ທີ່ໄດ້ຈາກການແຈກຢາຍແບບໄຄ-ສະແຄຣ ໂດຍເປັນຄ່າຊ່ວງປິດລະຫວ່າງ 0 ຫາ 1!ແມ່ນຈໍານວນອົງສາຄວາມເປັນອິດສະຫຼະ ເຊິ່ງເປັນຕົວເລກຕັ້ງແຕ່ 1 ຫາ 10^10 ໂດຍບໍ່ລວມ 10^10"}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "nສົ່ງຄືນຄ່າການທົດສອບຄວາມເປັນອິດສະຫຼະ: ຄ່າຄວາມເປັນໄປໄດ້ຈາກການແຈກຢາຍແບບໄຊ-ສະແຄ (chi-squared) ຂອງສະຖີຕິໄຊ-ສະແຄ ແລະອົງສາຄວາມເປັນອິດສະຫຼະທີ່ເໝາະສົມ ", "ad": "ເປັນຊ່ວງຂອງຂໍ້ມູນທີ່ເປັນຄ່າສັງເກດທີ່ຈະຖືກໃຊ້ທົດສອບກັບຄ່າທີ່ຄາດໝາຍ!ເປັນຊ່ວງຂອງຂໍ້ມູນທີ່ມີອັດຕາສ່ວນຜົນຄູນຂອງຜົນລວມແຖວ ແລະ ຜົນລວມຖັນ ກັບຜົນລວມທັງໝົດ"}, "CHISQ.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາ​ມ​ເປັນ​ໄປ​ໄດ້​​ດ້ານຊ້າຍ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ໄຄ-ສະ​ແຄຣ", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ປະ​ເມີນ​ຜົນ​ການ​ແຈກ​ຢາຍ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ທີ່​ບໍ່​ແມ່ນ​ຈໍານວນ​ລົບ!​ແມ່ນ​ຈໍານວນ​ອົງສາ​ຂອງ​ຄວາມ​ເປັນ​ອິດສະຫຼະ ​ເຊິ່ງ​ແມ່ນ​ຕົວ​ເລກ​ລະຫວ່າງ 1 ຫາ 10^10 ​ທີ່ບໍ່​ລວມ​ເອົາ 10^10!​ແມ່ນ​ຄ່າ​ຄວາມຈິງ​ສໍາລັບ​ຟັງ​ຊັນ​ທີ່​ຈະ​ສົ່ງ​ຄືນ: ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ແບບ​ສະ​ສົມ = TRUE; ຟັງ​ຊັນ​ຄວາມ​ໜາ​ແໜ້ນ​​ຄວາ​ມ​ເປັນ​ໄປ​ໄດ້ = FALSE"}, "CHISQ.DIST.RT": {"a": "(x; deg_freedom)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາ​ມ​ເປັນ​ໄປ​ໄດ້​​ດ້ານ​ຂວາ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ໄຄ-ສະ​ແຄຣ", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ປະ​ເມີນ​ຜົນ​ການ​ແຈກ​ຢາຍ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ທີ່​ບໍ່​ແມ່ນ​ຈໍານວນ​ລົບ!​ແມ່ນ​ຈໍານວນ​ອົງສາ​ຂອງ​ຄວາມ​ເປັນ​ອິດສະຫຼະ ​ເຊິ່ງ​ແມ່ນ​ຕົວ​ເລກ​ລະຫວ່າງ 1 ຫາ 10^10 ​ທີ່ບໍ່​ລວມ​ເອົາ 10^10"}, "CHISQ.INV": {"a": "(probability; deg_freedom)", "d": "ສົ່ງ​ຄືນຄ່າປື້ນຄືນຂອງຄວາມເປັນໄປໄດ້​ເບື້ອງຊ້າຍຂອງການແຈກຢາຍແບບໄຄ-ສະແຄຣ", "ad": "ແມ່ນຄ່າຄວາມເປັນໄປໄດ້ທີ່ໄດ້ຈາກການແຈກຢາຍແບບໄຄ-ສະແຄຣ ໂດຍເປັນຄ່າຊ່ວງປິດລະຫວ່າງ 0 ຫາ 1!ແມ່ນຈໍານວນອົງສາຄວາມເປັນອິດສະຫຼະ ເຊິ່ງເປັນຕົວເລກຕັ້ງແຕ່ 1 ຫາ 10^10 ໂດຍບໍ່ລວມ 10^10"}, "CHISQ.INV.RT": {"a": "(probability; deg_freedom)", "d": "ສົ່ງ​ຄືນຄ່າປິ້ນຄືນຂອງຄວາມເປັນໄປໄດ້​ເບື້ອງຂວາຂອງການແຈກຢາຍແບບໄຄ-ສະແຄຣ", "ad": "ແມ່ນຄ່າຄວາມເປັນໄປໄດ້ທີ່ໄດ້ຈາກການແຈກຢາຍແບບໄຄ-ສະແຄຣ ໂດຍເປັນຄ່າຊ່ວງປິດລະຫວ່າງ 0 ຫາ 1!ແມ່ນຈໍານວນອົງສາຄວາມເປັນອິດສະຫຼະ ເຊິ່ງເປັນຕົວເລກຕັ້ງແຕ່ 1 ຫາ 10^10 ໂດຍບໍ່ລວມ 10^10"}, "CHISQ.TEST": {"a": "(actual_range; expected_range)", "d": "ສົ່ງຄືນຄ່າການທົດສອບຄວາມເປັນອິດສະຫຼະ: ຄ່າຄວາມເປັນໄປໄດ້ຈາກການແຈກຢາຍແບບໄຊ-ສະແຄ (chi-squared) ຂອງສະຖີຕິໄຊ-ສະແຄ ແລະອົງສາຄວາມເປັນອິດສະຫຼະທີ່ເໝາະສົມ ", "ad": "ເປັນຊ່ວງຂອງຂໍ້ມູນທີ່ເປັນຄ່າສັງເກດທີ່ຈະຖືກໃຊ້ທົດສອບກັບຄ່າທີ່ຄາດໝາຍ!ເປັນຊ່ວງຂອງຂໍ້ມູນທີ່ມີອັດຕາສ່ວນຜົນຄູນຂອງຜົນລວມແຖວ ແລະ ຜົນລວມຖັນ ກັບຜົນລວມທັງໝົດ"}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "ສົ່ງ​ຄືນຄ່າ​ຊ່ວງ​​ຄວາມ​ເຊື່ອ​ໝັ້ນຂອງ​ຄ່າ​ສະ​ເລ່ຍປະຊາກອນ ​ໂດຍ​ໃຊ້​ການ​ແຈກ​ຢາຍ​ປົກກະຕິ", "ad": "​ແມ່ນ​ລະດັບ​ຄວາມສຳຄັນ​ທີ່​ໃຊ້​ຄິດ​ໄລ່​ລະດັບ​ຄວາມ​ເຊື່ອ​ໝັ້ນ​ເຊິ່ງຕ້ອງ​ເປັນ​ຕົວ​ທີ່​ຫູາຍກວ່າ 0 ​ແຕ່​ນ້ອຍ​ກວ່າ 1!​ແມ່ນ​ສ່ວນ​ຜິດ​ບ່ຽງມາດຕະຖານ​ຂອງປະຊາກອນ​ສຳລັບ​ຊ່ວງ​ຂໍ້​ມູນ ​ແລະ​ຖືກ​ສົມມຸດ​ວ່າ​ຮູ້​ຄ່າ​ແລ້ວ. Standard_dev ຕ້ອງ​ມີຄ່າ​ຫຼາຍກວ່າ 0!​ແມ່ນ​ຂ​​ະໜາດ​ຂອງ​ຕົວຢ່າງ"}, "CONFIDENCE.NORM": {"a": "(alpha; standard_dev; size)", "d": "ສົ່ງ​ຄືນຄ່າ​ຊ່ວງ​​ຄວາມ​ເຊື່ອ​ໝັ້ນຂອງ​ຄ່າ​ສະ​ເລ່ຍປະຊາກອນ ​ໂດຍ​ໃຊ້​ການ​ແຈກ​ຢາຍ​ປົກກະຕິ", "ad": "​ແມ່ນ​ລະດັບ​ຄວາມສຳຄັນ​ທີ່​ໃຊ້​ຄິດ​ໄລ່​ລະດັບ​ຄວາມ​ເຊື່ອ​ໝັ້ນ​ເຊິ່ງຕ້ອງ​ເປັນ​ຕົວ​ທີ່​ຫູາຍກວ່າ 0 ​ແຕ່​ນ້ອຍ​ກວ່າ 1!​ແມ່ນ​ສ່ວນ​ຜິດ​ບ່ຽງມາດຕະຖານ​ຂອງປະຊາກອນ​ສຳລັບ​ຊ່ວງ​ຂໍ້​ມູນ ​ແລະ​ຖືກ​ສົມມຸດ​ວ່າ​ຮູ້​ຄ່າ​ແລ້ວ. Standard_dev ຕ້ອງ​ມີຄ່າ​ຫຼາຍກວ່າ 0!​ແມ່ນ​ຂ​​ະໜາດ​ຂອງ​ຕົວຢ່າງ"}, "CONFIDENCE.T": {"a": "(alpha; standard_dev; size)", "d": "ສົ່ງ​ຄືນຄ່າ​ຊ່ວງ​​ຄວາມ​ເຊື່ອ​ໝັ້ນຂອງ​ຄ່າ​ສະ​ເລ່ຍປະຊາກອນ ​ໂດຍ​ໃຊ້​ການ​ແຈກ​ຢາຍ T ຂອງ​ນັກຮຽນ", "ad": "​ແມ່ນ​ລະດັບ​ຄວາມສຳຄັນ​ທີ່​ໃຊ້​ຄິດ​ໄລ່​ລະດັບ​ຄວາມ​ເຊື່ອ​ໝັ້ນ​ເຊິ່ງຕ້ອງ​ເປັນ​ຕົວ​ທີ່​ຫູາຍກວ່າ 0 ​ແຕ່​ນ້ອຍ​ກວ່າ 1!​ແມ່ນ​ສ່ວນ​ຜິດ​ບ່ຽງມາດຕະຖານ​ຂອງປະຊາກອນ​ສຳລັບ​ຊ່ວງ​ຂໍ້​ມູນ ​ແລະ​ຖືກ​ສົມມຸດ​ວ່າ​ຮູ້​ຄ່າ​ແລ້ວ. Standard_dev ຕ້ອງ​ມີຄ່າ​ຫຼາຍກວ່າ 0!​ແມ່ນ​ຂ​​ະໜາດ​ຂອງ​ຕົວຢ່າງ"}, "CORREL": {"a": "(array1; array2)", "d": "ສົ່ງຄືນຄ່າສຳປະສິດສຳພັນລະຫວ່າງຊຸດຂໍ້ມູນ 2 ຊຸດຂໍ້ມູນ", "ad": "ເປັນຊ່ວງຫ້ອງທີ່ມີຄ່າ. ແລະ ຄ່າດັ່ງກ່າວຄວນຈະເປັນຕົວເລກ, ຊື່, ອາ​ເຣຍ໌, ຫຼື ການອ້າງອີງທີ່ມີຕົວ​ເລກ!ເປັນຊ່ວງຫ້ອງທີ 2 ທີ່ມີຄ່າໂດຍທີ່ຄ່າຄວນຈະເປັນຕົວເລກ, ຊື່, ອາ​ເຣຍ໌, ຫຼື ການອ້າງອີງທີ່ມີຕົວ​ເລກ"}, "COUNT": {"a": "(value1; [value2]; ...)", "d": "ນັບ​ຈຳ​ນວນ​ຂອງ​ເຊວ​ທີ່​ຢູ່​ໃນ​ຂອບ​ເຂດ​ທີ່​ມີ​ຕົວ​ເລກ", "ad": "ແມ່ນ 1 ຫາ 255 ​ຂໍ້​ພິ​ສູດ​ທີ່​ສາ​ມາດ​ມີ ຫຼື​ອ້າງ​ອີງ​ເຖິງ​ຂໍ້​ມູນ​ປະ​ເພດ​ຕ່າງໆ​ຫຼາຍ​ຊະ​ນິດ, ແຕ່​ຈະ​ນັບ​ແຕ່​ຕົວ​ເລກ​ເທົ່າ​ນັ້ນ"}, "COUNTA": {"a": "(value1; [value2]; ...)", "d": "ນັບ​ຈຳ​ນວນ​ຂອງ​ເຊວ​ຢູ່​ໃນ​ຂອບ​ເຂດ​ທີ່​ບໍ່​ຫວ່າງ​ເປົ່າ", "ad": "ແມ່ນ 1 ຫາ 255 ​ຂໍ້​ພິ​ສູດ​ສະ​ແດງ​ຄ່າ ແລະ​ເຊວ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ນັບ. ຄ່າ​ສາ​ມາດ​ເປັນ​ຂໍ້​ມູນ​ປະ​ເພດ​ໃດ​ໜຶ່ງ​ໄດ້"}, "COUNTBLANK": {"a": "(range)", "d": "ນັບຈຳນວນຫ້ອງທີ່ຫວ່າງໃນຊ່ວງຂອງຫ້ອງທີ່ລະບຸ", "ad": "​ແມ່ນຊ່ວງທີ່ທ່ານຕ້ອງການນັບຫ້ອງຫວ່າງ"}, "COUNTIF": {"a": "(range; criteria)", "d": "ນັບຈຳນວນຂອງຫ້ອງພາຍໃນຊ່ວງທີ່ກົງຕາມເງື່ອນໄຂທີ່ທ່ານລະບຸ", "ad": "ເປັນຊ່ວງຂອງຫ້ອງທີ່ທ່ານຕ້ອງການນັບຫ້ອງທີ່ບໍ່ຫວ່າງ ແລະ ຖືກຕາມເງື່ອນໄຂ!ເປັນເງື່ອນໄຂເຊິ່ງຢູ່ໃນຮູບແບບຕົວເລກ, ສຳນວນ, ຫຼື ຂໍ້ຄວາມເຊິ່ງຈະເປັນຕົວກຳນົດວ່າຫ້ອງໃດຈະຖືກນັບ"}, "COUNTIFS": {"a": "(criteria_range; criteria; ...)", "d": "ນັບຈຳນວນຂອງຫ້ອງທີ່ຖືກລະບຸໂດຍຊຸດຂອງເງື່ອນໄຂ ຫຼືເກນຕັດສິນທີ່ກຳນົດໃຫ້", "ad": "​ແມ່​ນຊ່ວງຂອງຫ້ອງທີ່ທ່ານຕ້ອງການໃຫ້ຖືກປະເມີນຄ່າສຳລັບເງື່ອນໄຂທີ່ລະບຸ!​ແມ່ນເງື່ອນໄຂ ຫຼືເກນການຕັດສິນໃນຮູບແບບຂອງຕົວເລກ, ສຳນວນ,ຫຼືຂໍ້ຄວາມທີ່ກຳນົດວ່າຫ້ອງໃດຈະຖືກນັບ"}, "COVAR": {"a": "(array1; array2)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາມ​ແຕກ​ຕ່າງຮ່ວມ​ ​ເຊິ່ງ​ເປັນ​ຄ່າ​ສະ​ເລ່ຍຂອງ​ຜົນ​ຄູນ​ຂອງ​ສ່ວນ​ຜິດ​ບ່ຽງສໍາລັບຄູ່​ຈຸດ​ຂໍ້​ມູນ​ແຕ່​ລະ​ຄູ່​ໃນ​ຊຸດ​ຂໍ້​ມູນ​ສອງ​ຊຸດ", "ad": "​ແມ່ນ​ຊ່ວງ​ຫ້ອງ​ທຳ​ອິດ​ຂອງ​ຈຳນວນ​ເຕັມ ​ແລະ​ຕ້ອງ​ເປັນ​ຕົວ​ເລກ, ອາ​ເຣຍ໌ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ມີຄ່າ​ເປັນ​ຕົວ​ເລກ!​ແມ່ນ​ຊ່ວງ​ຫ້ອງ​ທີສອງ​ຂອງ​ຈຳນວວນ​ເຕັມ ​ແລະ​ຕ້ອງ​ຕ້ອງ​ມີຄ່າ​ເປັນ​ຕົວ​ເລກ, ອາ​ເຣຍ໌ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ມີຄ່າ​ເປັນ​ຕົວ​ເລກ"}, "COVARIANCE.P": {"a": "(array1; array2)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາມ​ແຕກ​ຕ່າງຮ່ວມ​ຂອງ​ປະຊາກອນ ​ເຊິ່ງ​ເປັນ​ຄ່າ​ສະ​ເລ່ຍຂອງ​ຜົນ​ຄູນ​ຂອງ​ສ່ວນ​ຜິດ​ບ່ຽງສໍາລັບຄູ່​ຈຸດ​ຂໍ້​ມູນ​ແຕ່​ລະ​ຄູ່​ໃນ​ຊຸດ​ຂໍ້​ມູນ​ສອງ​ຊຸດ", "ad": "​ແມ່ນ​ຊ່ວງ​ຫ້ອງ​ທຳ​ອິດ​ຂອງ​ຈຳນວນ​ເຕັມ ​ແລະ​ຕ້ອງ​ເປັນ​ຕົວ​ເລກ, ອາ​ເຣຍ໌ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ມີຄ່າ​ເປັນ​ຕົວ​ເລກ!​ແມ່ນ​ຊ່ວງ​ຫ້ອງ​ທີສອງ​ຂອງ​ຈຳນວວນ​ເຕັມ ​ແລະ​ຕ້ອງ​ຕ້ອງ​ມີຄ່າ​ເປັນ​ຕົວ​ເລກ, ອາ​ເຣຍ໌ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ມີຄ່າ​ເປັນ​ຕົວ​ເລກ"}, "COVARIANCE.S": {"a": "(array1; array2)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາມ​ແຕກ​ຕ່າງຮ່ວມ​ຂອງ​ຕົວຢ່າງ ​ເຊິ່ງ​ເປັນ​ຄ່າ​ສະ​ເລ່ຍຂອງ​ຜົນ​ຄູນ​ຂອງ​ສ່ວນ​ຜິດ​ບ່ຽງສໍາລັບຄູ່​ຈຸດ​ຂໍ້​ມູນ​ແຕ່​ລະ​ຄູ່​ໃນ​ຊຸດ​ຂໍ້​ມູນ​ສອງ​ຊຸດ", "ad": "​ແມ່ນ​ຊ່ວງ​ຫ້ອງ​ທຳ​ອິດ​ຂອງ​ຈຳນວນ​ເຕັມ ​ແລະ​ຕ້ອງ​ເປັນ​ຕົວ​ເລກ, ອາ​ເຣຍ໌ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ມີຄ່າ​ເປັນ​ຕົວ​ເລກ!​ແມ່ນ​ຊ່ວງ​ຫ້ອງ​ທີສອງ​ຂອງ​ຈຳນວນ​ເຕັມ ​ແລະ​ຕ້ອງ​ຕ້ອງ​ມີຄ່າ​ເປັນ​ຕົວ​ເລກ, ອາ​ເຣຍ໌ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ມີຄ່າ​ເປັນ​ຕົວ​ເລກ"}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ນ້ອຍ​ສຸດ​ສໍາລັບ​ການ​ແຈກ​ຢາຍ​ທະ​ວີນາ​ມສະ​ສົມທີ່​ມີຄ່າ​ຫຼາຍກວ່າ ຫຼື​ເທົ່າ​ກັບ​ຄ່າ​ເກນ​ເງື່ອນ​ໄຂ", "ad": "​ແມ່ນ​ຈໍານວນ​ຄັ້ງ​ຂອງ​ການ​ທົດ​ລອງ (<PERSON><PERSON><PERSON>)!​ແມ່ນ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ຈະ​ໄດ້​ຮັບ​ຜົນ​ສໍາ​ເລັດ​ໃນ​ການ​ທົດ​ລອງ​ແຕ່​ລະ​ຄັ້ງ, ​ເປັນ​ຈໍານວນ​ທີ່ຢູ່​ລະຫວ່າງ 0 ຫາ 1 !​ເປັນ​ຄ່າ​ເກນ​ເງື່ອນ​ໄຂ​ທີ່ຢູ່​ລະຫວ່າງ 0 ຫາ 1"}, "DEVSQ": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າຜົນລວມຂອງສ່ວນຜິດ​ບ່ຽງຂອງຈຸດຂໍ້ມູນຈາກຄ່າສະເລ່ຍຕົວຢ່າງຂຶ້ນກຳລັງສອງ", "ad": "​ແມ່ຂໍ້​ພິສູດ 1 ຫາ 255 ຂໍ້, ຫຼື ອາເຣຍ໌ ຫຼືການ​ອ້າງ​ອີ​ອາ​ເຣຍ໌ທີ່ທ່ານຕ້ອງການໃຫ້ DEVSQ ຄຳນວນ"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ exponential", "ad": "​ແມ່ນ​ຄ່າ x ທີ່​ໃຊ້​ໃນ​ຟັງ​ຊັນ ​ແລະ​ຕ້ອງ​ບໍ່​ແມ່​ນຄ່າ​ລົບ!​ເປັນ​ຄ່າ​ພາຣາມິເຕີ ​ແລະ​ຕ້ອງ​ເປັນ​ຄ່​າບວກ!​ເປັນ​ຄ່າ​ຄວວາ​ມຈິງ​ເພື່ອ​ຊີ້​ບອກ​ຟັງ​ຊັນ​ທີ່​ຕ້ອງການ​ໃຫ້​ສົ່ງ​ຄືນ ​ໂດຍ​ໃຫ້​ໃຊ້ TRUE ສໍາລັບ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ແບບ​ສະ​ສົມ ຫຼື​ໃຊ້ FALSE ສໍາລັບ​ຟັງ​ຊັນ​ຄວາມ​ໜາ​ແໜ້ນ​ຂອງ​ຄວາມ​ເປັນ​ໄປ​ໄດ້"}, "EXPON.DIST": {"a": "(x; known_y's; known_x's)", "d": "ຄິດ​ໄລ່, ຫຼື​ເດົາ, ຄ່າອະ​ນາ​ຄົດ​ຕາມ​ແນວ​ໂນ້ມ​ຕາມ​ເສັ້ນ​ໂດຍ​ການ​ໃຊ້​ຄ່າ​ທີ່​ມີ​ຢູ່", "ad": "ແມ່ນ​ພາຣາມິເຕີທີ່​ທ່ານ​ຕ້ອງ​ການ​ເດົາ​ຄ່າ ແລະ​ຕ້ອງ​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ!ແມ່ນແຖວ​ລຳ​ດັບຂຶ້ນ​ກັບ ຫຼື​ຂອບ​ເຂດ​ຂອງ​ຂໍ້​ມູນ​ຕົວ​ເລກ!ແມ່ນແຖວ​ລຳ​ດັບຂຶ້ນ​ກັບ ຫຼື​ຂອບ​ເຂດ​ຂອງຂໍ້​ມູນ​ຕົວ​ເລກ. ການ​ຜັນ​ປ່ຽນຂອງ x_ທີ່​ຮູ້​ຈັກ​ຕ້ອງ​ບໍ່​ເປັນ​ສູນ"}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ F (ດ້ານ​ຂວາ) (ລະດັບ​ຄວາມ​ໜາ​ແໜ້ນ) ສໍາລັບ​ສອງ​ຊຸດ​ຂໍ້​ມູນ", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ໃຊ້​ເພື່ອ​ປະ​ເມີນ​ຜົນ​ຟັງ​ຊັນ ​ໂດຍບໍ່​ແມ່ນຄ່າ​ລົບ!​​​ແມ່ນ​ຕົວ​ເສດ​ຂອງ​ອົງສາ​ຄວາມ​ເປັນ​ອິດສະຫຼະ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 ຫາ 10^10 ທີ່​ບໍ່​ລວມ​ເອົາ 10^10!​ແມ່ນ​ຕົວ​ສ່ວນ​ຂອງ​ອົງສາ​ຄວາມ​ເປັນ​ອິດ​ສະຫຼະ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 ຫາ 10^10 ​ໂດຍ​ບໍ່​ລວມ​ເອົາ 10^10"}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ F (​ເບື້ອງ​ຂວາ): ຖ້າ p = F.DIST(x,...) ​ແລ້ວ F.INV(p,...) = x", "ad": "​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ຖີ່​​ສະ​ສົມ​ແບບ F ​ໂດຍ​ມີຄ່າ​ໃນ​ຊ່ວງ​ປິດ​ແຕ່ 0 ຫາ 1!​ແມ່ນ​ຕົວ​ເສດ​ຂອງ​ອົງສາ​ຄວາມ​ເປັນ​ອິດສະຫຼະ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 ຫາ 10^10 ທີ່​ບໍ່​ລວມ 10^10!​ແມ່ນ​ສ່ນ​ຂອງ​ອົງສາ​ຄວາມ​ເປັນ​ອິດສະຫຼະ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 ຫາ 10^10 ທີ່​ບໍ່​ລວມ 10^10"}, "FTEST": {"a": "(array1; array2)", "d": "ຕອບຜົນຂອງ F-test ເຊິ່ງເປັນຄ່າຄວາມເປັນໄປໄດ້ແບບສອງທາງ ທີ່ຄວາມແປຜັນໃນ Array1 ແລະ Array2 ບໍ່ແຕກຕ່າງກັນຫຼາຍ", "ad": "ແມນອາເຣ ຫຼືຊ່ວງຂໍ້ມູນທຳອິດ ແລະສາມາດເປັນໄດ້ທັງຕົວເລກ ຫຼື ຊື່, ອາເຣ ຫຼືການອ້າງອີງທີ່ປະກອບມີຕົວເລກ (ຄ່າວ່າຈະຖືກລະເວັ້ນ)!ແມ່ນອາເຣ ຫຼືຊ່ວງຂໍ້ມູນທີສອງ ລະສາມາດເປັນໄດ້ທັງຕົວເລກ ຫຼືຊື່, ອາເຣ ຫຼືການອ້າງອີງທີ່ປະກອບມີຕົວເລກ (ຄ່າວ່າງຈະຖືກລະເວັ້ນ)"}, "F.DIST": {"a": "(x; deg_freedom1; deg_freedom2; cumulative)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ F (ດ້ານ​ຂວາ) (ລະດັບ​ຄວາມ​ໜາ​ແໜ້ນ) ສໍາລັບ​ສອງ​ຊຸດ​ຂໍ້​ມູນ", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ໃຊ້​ເພື່ອ​ປະ​ເມີນ​ຜົນ​ຟັງ​ຊັນ ​ໂດຍບໍ່​ແມ່ນຄ່າ​ລົບ!​​​ແມ່ນ​ຕົວ​ເສດ​ຂອງ​ອົງສາ​ຄວາມ​ເປັນ​ອິດສະຫຼະ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 ຫາ 10^10 ທີ່​ບໍ່​ລວມ​ເອົາ 10^10!​ແມ່ນ​ຕົວ​ສ່ວນ​ຂອງ​ອົງສາ​ຄວາມ​ເປັນ​ອິດ​ສະຫຼະ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 ຫາ 10^10 ​ໂດຍ​ບໍ່​ລວມ​ເອົາ 10^10!​ແມ່ນ​ຄ່າ​ຄວາມ​ຈິງ​ສໍາລັບ​ຟັງ​ຊັນ​ທີ່​ຈະ​ສົ່ງ​ຄືນ: ຟັງ​ຊັນ​ແຈກ​ຢາຍ​ແບບ​ສະ​ສົມ = TRUE; ຟັງ​ຊັນ​ແຈກ​ຢາຍ​ຄວມໜາ​ແໜ້ນ​ຄວາມ​ເປັນ​ໄປ​ໄດ້ = FALSE"}, "F.DIST.RT": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ F (ດ້ານ​ຂວາ) (ລະດັບ​ຄວາມ​ໜາ​ແໜ້ນ) ສໍາລັບ​ສອງ​ຊຸດ​ຂໍ້​ມູນ", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ໃຊ້​ເພື່ອ​ປະ​ເມີນ​ຜົນ​ຟັງ​ຊັນ ​ໂດຍບໍ່​ແມ່ນຄ່າ​ລົບ!​​​ແມ່ນ​ຕົວ​ເສດ​ຂອງ​ອົງສາ​ຄວາມ​ເປັນ​ອິດສະຫຼະ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 ຫາ 10^10 ທີ່​ບໍ່​ລວມ​ເອົາ 10^10!​ແມ່ນ​ຕົວ​ສ່ວນ​ຂອງ​ອົງສາ​ຄວາມ​ເປັນ​ອິດ​ສະຫຼະ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 ຫາ 10^10 ​ໂດຍ​ບໍ່​ລວມ​ເອົາ 10^10"}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ F (​ເບື້ອງ​ຊ້າຍ): ຖ້າ p = F.DIST(x,...) ​ແລ້ວ F.INV(p,...) = x", "ad": "​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ຖີ່​​ສະ​ສົມ​ແບບ F ​ໂດຍ​ມີຄ່າ​ໃນ​ຊ່ວງ​ປິດ​ແຕ່ 0 ຫາ 1!​ແມ່ນ​ຕົວ​ເສດ​ຂອງ​ອົງສສາ​ຄວາມ​ເປັນ​ອິດສະຫຼະ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 ຫາ 10^10 ທີ່​ບໍ່​ລວມ 10^10!​ແມ່ນ​ສ່ນ​ຂອງ​ອົງສາ​ຄວາມ​ເປັນ​ອິດສະຫຼະ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 ຫາ 10^10 ທີ່​ບໍ່​ລວມ 10^10"}, "F.INV.RT": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ F (​ເບື້ອງ​ຂວາ): ຖ້າ p = F.DIST(x,...) ​ແລ້ວ F.INV(p,...) = x", "ad": "​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ຖີ່​​ສະ​ສົມ​ແບບ F ​ໂດຍ​ມີຄ່າ​ໃນ​ຊ່ວງ​ປິດ​ແຕ່ 0 ຫາ 1!​ແມ່ນ​ຕົວ​ເສດ​ຂອງ​ອົງສສາ​ຄວາມ​ເປັນ​ອິດສະຫຼະ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 ຫາ 10^10 ທີ່​ບໍ່​ລວມ 10^10!​ແມ່ນ​ສ່ນ​ຂອງ​ອົງສາ​ຄວາມ​ເປັນ​ອິດສະຫຼະ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 ຫາ 10^10 ທີ່​ບໍ່​ລວມ 10^10"}, "F.TEST": {"a": "(array1; array2)", "d": "ສົ່ງຄືນຄ່າຜົນຮັບຂອງ F-test,ເຊິ່ງເປັນຄ່າຄວາມເປັນໄປໄດ້ແບບສອງທາງ (two-tailed) ເຊິ່ງເປັນຜົນມາຈາກການທົດລອງທີ່ມີຄວາມຜັນແປ ຂອງອາ​ເຣຍ໌1 ແລະອາ​ເຣຍ໌2 ບໍ່ແຕກຕ່າງກັນຫຼາຍ", "ad": "ເປັນອາ​ເຣຍ໌ ຫຼືຊ່ວງຂໍ້ມູນທຳອິດ ແລະ ສາມາດເປັນໄດ້ທັງຕົວ​ເລກ ຊື່, ອາ​ເຣຍ໌,ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກ (ບ່ອນວ່າງຈະຖືກຍົກເວັ້ນ)!ເປັນອາ​ເຣຍ໌ ຫຼືຊ່ວງຂໍ້ມູນທີ 2 ແລະ ສາມາດເປັນໄດ້ທັງຕົວ​ເລກ ຊື່, ອາ​ເຣຍ໌, ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກ (ບ່ອນວ່າງຈະຖືກຍົກເວັ້ນ)"}, "FISHER": {"a": "(x)", "d": "ສະ​ແດງ​ຜົນການ​ແປງ​ຄ່າ Fisher", "ad": "ແມ່ນຄ່າທີ່​ທ່ານ​ຕ້ອງ​ການ​ແປງ, ຕົວ​ເລກ​ລະ​ຫວ່າ -1 ຫາ 1, ບໍ່​ລວມ -1 ແລະ 1"}, "FISHERINV": {"a": "(y)", "d": "ສະ​ແດງ​ຜົນປີ້ນ​ກັບ​ຂອງ​ການ​ແປງ Fisher: ຖ້າ y = FISHER(x), ຈາກນັ້ນ FISHERINV(y) = x", "ad": "is ຄ່າສຳ​ລັບ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ເຮັດ​ການ​ປີ້ນ​ກັບ​ຂອງ​ການ​ແປງ"}, "FORECAST": {"a": "(x; known_ys; known_xs)", "d": "ຄໍານວນ, ຫຼືຄາດ​ເດົາ, ຄ່າ​ໃນ​ອະນາຄົດ​ຕາມ​ແນວ​ໂນ້ມ​​ເສັ້ນຊື່ ​ໂດຍ​ການ​ໃຊ້​ຄ່າ​ທີ່ມີ​ຢູ່", "ad": "​ແມ່ນ​ຈຸດ​ຂໍ້​ມູນ​ທີ່​ທ່າ​ນຕ້ອງການ​ຄາດ​ເດົາ​ຄ່າ ​ແລະ​ຕ້ອງ​ເປັນ​ຄ່າ​ຕົວ​ເລກ!​ເປັນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງ​ຂໍ້​ມູນ​ຕົວ​ເລກທີ່ບໍ່​ເປັນ​ອິດສະຫຼະ!​ເປັນ​ອາ​ເຣຍ໌ອິດ​ສະ​ຫຼະ ຫຼືຊ່ວງ​ຂໍ້​ມູນ​ຕົວ​ເລກ. ຄ່າ​ຄວາມ​​ແຕກ​ຕ່າງ​ຂອງ Known_x's ຕ້ອງ​ບໍ່​ເທົ່າ​ກັບ​ສູນ"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄາດ​ການ​ໄວ້​ສຳ​ລັບ​ວັນ​ທີ​ເປົ້າ​ໝາຍ​ອະ​ນາ​ຄົດ​ສະ​ເພາະ​ດ້ວຍ​ການ​ໃຊ້​ວິ​ທີ​ການ​ເຮັດ​ໃຫ້​ລະ​ອຽດ​ເອັກ​ໂປ​ເນັນ​ຊຽວ.", "ad": "ເປັນຈຸດຂໍ້ມູນທີ່ Spreadsheet Editor ຄາດຄະເນ. ມັນຄວນຈະມີຮູບແບບຂອງຄ່າໃນທາມລາຍ.!ເປັນອາເຣ ຫຼື ຊ່ວງຂໍ້ມູນທີ່ເປັນຕົວເລກທີ່ທ່ານກຳລັງຄາດຄະເນ.!ເປັນອາເຣອິດສະຫຼະ ຫຼື ຊ່ວງຂໍ້ມູນທີ່ເປັນຕົວເລກ. ວັນທີໃນທາມລາຍຈະຕ້ອງມີຂັ້ນຕອນທີ່ສອດຄ່ອງກັນລະຫວ່າງພວກມັນ ແລະ ບໍ່ສາມາດເປັນສູນໄດ້.!ເປັນຄ່າຕົວເລກທີ່ເລືອກໄດ້ທີ່ລະບຸຄວາມຍາວຂອງຮູບແບບຕາມລະດູການ. ຄ່າເລີ່ມຕົ້ນຂອງ 1 ລະບຸວ່າ ກວດພົບຂໍ້ມູນຕາມລະດູການໂດຍອັດຕະໂນມັດ.!ເປັນຄ່າທີ່ເລືອກໄວ້ສຳລັບການຈັດການຄ່າທີ່ຂາດຫາຍໄປ. ຄ່າເລີ່ມຕົ້ນຂອງ 1 ຈະປ່ຽນແທນຄ່າທີ່ຂາດຫາຍໄປໂດຍການປະມານຄ່າໃນຊ່ວງ ແລະ 0 ຈະແທນທີ່ພວກມັນດ້ວຍເລກສູນ.!ເປັນຄ່າຕົວເລກທີ່ເລືອກໄດ້ສຳລັບການລວມຄ່າຫຼາຍຄ່າທີ່ມີສະແຕັມເວລາດຽວກັນ. ຖ້າຄ່າຫວ່າງ, Spreadsheet Editor ຈະສະເລ່ຍຄ່າ."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "ຜົນ​ໄດ້​ຮັບ​ຊ່ວງ​ຄວາມ​ເຊື່ອ​ໝັ້ນ​ສໍາລັບຄ່າ​ທີ່​ຄາດ​ໄວ້​ລ່ວງ​ໜ້າ​ໃນ​ວັນ​ທີ​ເປົ້າ​ໝາຍ​ທີ່​ລະບຸ​ໄວ້.", "ad": "ເປັນຈຸດຂໍ້ມູນທີ່ Spreadsheet Editor ຄາດຄະເນ. ມັນຄວນຈະມີຮູບແບບຂອງຄ່າໃນທາມລາຍ.!ເປັນອາເຣ ຫຼື ຊ່ວງຂໍ້ມູນທີ່ເປັນຕົວເລກທີ່ທ່ານກຳລັງຄາດຄະເນ.!ເປັນອາເຣອິດສະຫຼະ ຫຼື ຊ່ວງຂໍ້ມູນທີ່ເປັນຕົວເລກ. ວັນທີໃນທາມລາຍຈະຕ້ອງມີຂັ້ນຕອນທີ່ສອດຄ່ອງກັນລະຫວ່າງພວກມັນ ແລະ ບໍ່ສາມາດເປັນສູນໄດ້.!ເປັນຕົວເລກລະຫວ່າງ 0 ແລະ 1 ທີ່ສະແດງລະດັບຄວມເຊື່ອໝັ້ນສຳລັບໄລຍະເວລາຄວາມເຊື່ອໝັ້ນທີ່ຄຳນວນ. ຄ່າເລີ່ມຕົ້ນແມ່ນ .95.!ເປັນຄ່າຕົວເລກທີ່ເລືອກໄດ້ທີ່ລະບຸຄວາມຍາວຂອງຮູບແບບຕາມລະດູການ. ຄ່າເລີ່ມຕົ້ນຂອງ 1 ລະບຸວ່າ ກວດພົບຂໍ້ມູນຕາມລະດູການໂດຍອັດຕະໂນມັດ.!ເປັນຄ່າທີ່ເລືອກໄວ້ສຳລັບການຈັດການຄ່າທີ່ຂາດຫາຍໄປ. ຄ່າເລີ່ມຕົ້ນຂອງ 1 ຈະປ່ຽນແທນຄ່າທີ່ຂາດຫາຍໄປໂດຍການປະມານຄ່າໃນຊ່ວງ ແລະ 0 ຈະແທນທີ່ພວກມັນດ້ວຍເລກສູນ.!ເປັນຄ່າຕົວເລກທີ່ເລືອກໄດ້ສຳລັບການລວມຄ່າຫຼາຍຄ່າທີ່ມີສະແຕັມເວລາດຽວກັນ. ຖ້າຄ່າຫວ່າງ, Spreadsheet Editor ຈະສະເລ່ຍຄ່າ."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "ສົ່ງ​​ຄືນ​ຄວາມ​ຍາວ​ຂອງ​ຮູບ​ແບບ​ຊ້ຳກວດ​ຫາ​ຊຸດ​ເວ​ລາ​ສະ​ເພາະ.", "ad": "ເປັນອາເຣ ຫຼື ຊ່ວງຂໍ້ມູນທີ່ເປັນຕົວເລກທີ່ທ່ານກຳລັງຄາດຄະເນ.!ເປັນອາເຣອິດສະຫຼະ ຫຼື ຊ່ວງຂໍ້ມູນທີ່ເປັນຕົວເລກ. ວັນທີໃນທາມລາຍຕ້ອງມີຂັ້ນຕອນທີ່ສອດຄ່ອງກັນລະຫວ່າງພວກມັນ ແລະ ບໍ່ສາມາດເປັນເລກສູນໄດ້.!ເປັນຄ່າທີ່ເລືອກໄດ້ສຳລັບການຈັດການຄ່າທີ່ຂາດຫາຍໄປ. ຄ່າເລີ່ມຕົ້ນຂອງ 1 ຈະປ່ຽນແທນຄ່າທີ່ຂາດຫາຍໄປໂດຍການປະມານຄ່າໃນຊ່ວງ ແລະ 0 ຈະປ່ຽນແທນພວກມັນດ້ວຍເລກສູນ.!ເປັນຄ່າຕົວເລກທີ່ເລືອກໄດ້ສຳລັບການລວມຄ່າຫຼາຍຄ່າທີ່ມີສະແຕັມເວລາດຽວກັນ. ຖ້າຄ່າຫວ່າງ, Spreadsheet Editor ຈະສະເລ່ຍຄ່າ."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "ສົ່ງ​ຄືນ​ສະ​ຖິ​ຕິ​ສະ​ເໜີ​ສຳ​ລັບ​ການ​ຄາດ​ການ.", "ad": "ເປັນອາເຣ ຫຼື ຊ່ວງຂໍ້ມູນທີ່ເປັນຕົວເລກທີ່ທ່ານກຳລັງຄາດຄະເນ.!ເປັນອາເຣອິດສະຫຼະ ຫຼື ຊ່ວງຂໍ້ມູນທີ່ເປັນຕົວເລກ. ວັນທີໃນທາມລາຍຕ້ອງມີຂັ້ນຕອນທີ່ສອດຄ່ອງກັນລະຫວ່າງພວກມັນ ແລະ ບໍ່ສາມາດເປັນເລກສູນໄດ້!ເປັນຕົວເລກລະຫວ່າງ 1 ແລະ 8, ທີ່ລະບຸສະຖິຕິ Spreadsheet Editor ທີ່ຈະສົ່ງຄືນສຳລັບການພະຍາກອນທີ່ຄຳນວນ.!ເປັນຄ່າຕົວເລກທີ່ເລືອກໄດ້ທີ່ລະບຸຄວາມຍາວຂອງຮູບແບບຕາມລະດູການ. ຄ່າເລີ່ມຕົ້ນຂອງ 1 ລະບຸວ່າ ກວດພົບຂໍ້ມູນຕາມລະດູການໂດຍອັດຕະໂນມັດ.!ເປັນຄ່າທີ່ເລືອກສຳລັບການັຈດການຄ່າທີ່ຂາດຫາຍໄປ. ຄ່າເລີ່ມຕົ້ນຂອງ 1 ຈະປ່ຽນແທນຄ່າທີ່ຂາດຫາຍໄປໂດຍການປະມານຄ່າໃນຊ່ວງ ແລະ 0 ຈະປ່ຽນແທນພວກມັນດ້ວຍເລກສູນ.!ເປັນຄ່າຕົວເລກທີ່ເລືອກໄດ້ສຳລັບການລວມຄ່າຫຼາຍຄ່າທີ່ມີສະແຕັມເວລາດຽວກັນ. ຖ້າຄ່າຫວ່າງ, Spreadsheet Editor ຈະສະເລ່ຍຄ່າ."}, "FORECAST.LINEAR": {"a": "(x; known_ys; known_xs)", "d": "ຄໍານວນ, ຫຼືຄາດ​ເດົາ, ຄ່າ​ໃນ​ອະນາຄົດ​ຕາມ​ແນວ​ໂນ້ມ​​ເສັ້ນຊື່ ​ໂດຍ​ການ​ໃຊ້​ຄ່າ​ທີ່ມີ​ຢູ່", "ad": "​ແມ່ນ​ຈຸດ​ຂໍ້​ມູນ​ທີ່​ທ່າ​ນຕ້ອງການ​ຄາດ​ເດົາ​ຄ່າ ​ແລະ​ຕ້ອງ​ເປັນ​ຄ່າ​ຕົວ​ເລກ!​ເປັນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງ​ຂໍ້​ມູນ​ຕົວ​ເລກທີ່ບໍ່​ເປັນ​ອິດສະຫຼະ!​ເປັນ​ອາ​ເຣຍ໌ອິດ​ສະ​ຫຼະ ຫຼືຊ່ວງ​ຂໍ້​ມູນ​ຕົວ​ເລກ. ຄ່າ​ຄວາມ​​ແຕກ​ຕ່າງ​ຂອງ Known_x's ຕ້ອງ​ບໍ່​ເທົ່າ​ກັບ​ສູນ"}, "FREQUENCY": {"a": "(data_array; bins_array)", "d": "ຄຳນວນຫາຈຳນວນເທື່ອທີ່ເກີດຂຶ້ນຂອງຄ່າພາຍໃນຊ່ວງຂອງຄ່າທີ່ລະບຸ ຈາກນັ້ນສົ່ງຄືນອະເຣໃນແນວຕັ້ງ ເຊິ່ງມີຈຳນວນຂໍ້ມູນຫຼາຍກວ່າ ຈຳນວນຂໍ້ມູນຂອງ Bins_array ຢູ່ 1", "ad": "ເປັນອະເຣຂອງ ຫຼື ການອ້າງອີງຫາຊຸດຂອງຄ່າທີ່ທ່ານຕ້ອງການນັບຫາຄວາມຖີ່ (ທີ່ຫວ່າງ ແລະ ຂໍ້ຄວາມຈະຖືກລະເວັ້ນ)!ເປັນອະເຣຂອງ ຫຼືການອ້າງອີງຫາຊ່ວງໄລຍະຫ່າງທີ່ທ່ານຕ້ອງການໃຫ້ນັບຄວາມຖີ່ຂອງ data_array"}, "GAMMA": {"a": "(x)", "d": "ສົ່ງ​ຄືນ​ຄ່າຟັງ​ຊັນ​ແມ​ມາ", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ຄໍານວນ​​ແກມ​ມາ"}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ແກມ​ມາ", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ປະ​ເມີນ​ຫາ​ການ​ແຈກ​ຢາຍ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ທີ່​ບໍ່​ແມ່ນ​ຈຳນວນ​ລົບ!​ແມ່ນ​ພາຣາມິເຕີ​ຕໍ່​ກັບ​ການ​ແຈກ​ຢາຍ ​ເຊິ່ງ​ເປັນ​​ເລກຈໍານວນ​ບວກ!​ແມ່ນ​ພາຣາມິເຕີຕໍ່​ກັບ​ການ​ແຈກ​ຢາຍ ​ເຊິ່ງ​ເປັນ​​ເລກ​ຈຳນວນ​ບວກ. ຖ້າ​ເບ​ຕ້າ = 1, GAMMA.DIST ສົ່ງ​ຄືນ​ຄ່ການ​ແຈກ​ຢາຍ​ແກມມາ​ມາດຕະຖານ!​ແມ່ນ​ຄ່າ​ຄວາມ​ຈິງ: ສົ່ງ​ຄືນ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ສະ​ສົມ = TRUE; ສົ່ງ​ຄືນ​ຟັງ​ຊັນ​ມວນ​ຂອງ​ຄວາມ​ເປັນ​ໄປ​ໄດ້ = FALSE ຫຼືບໍ່​ໃສ່​ຄ່າ​ໃດ"}, "GAMMA.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ແກມ​ມາ", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ປະ​ເມີນ​ຫາ​ການ​ແຈກ​ຢາຍ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ທີ່​ບໍ່​ແມ່ນ​ຈຳນວນ​ລົບ!​ແມ່ນ​ພາຣາມິເຕີ​ຕໍ່​ກັບ​ການ​ແຈກ​ຢາຍ ​ເຊິ່ງ​ເປັນ​​ເລກຈໍານວນ​ບວກ!​ແມ່ນ​ພາຣາມິເຕີຕໍ່​ກັບ​ການ​ແຈກ​ຢາຍ ​ເຊິ່ງ​ເປັນ​​ເລກ​ຈຳນວນ​ບວກ. ຖ້າ​ເບ​ຕ້າ = 1, GAMMA.DIST ສົ່ງ​ຄືນ​ຄ່ການ​ແຈກ​ຢາຍ​ແກມມາ​ມາດຕະຖານ!​ແມ່ນ​ຄ່າ​ຄວາມ​ຈິງ: ສົ່ງ​ຄືນ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ສະ​ສົມ = TRUE; ສົ່ງ​ຄືນ​ຟັງ​ຊັນ​ມວນ​ຂອງ​ຄວາມ​ເປັນ​ໄປ​ໄດ້ = FALSE ຫຼືບໍ່​ໃສ່​ຄ່າ​ໃດ"}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ແກມ​ມາສະ​ສົມ: ຖ້າ​ຄ່າ p = GAMMA.DIST(x,...) แล้วค่า GAMMA.INV(p,...) ຈະ = x", "ad": "​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ແບບ​ແກມ​ມາ ​ແລະ​ເປັນ​ຈໍານວນ​ໃນ​ຊ່ວງ​ປິດ​ແຕ່ 0 ຫາ 1!​ແມ່ນ​ພາຣາມິເຕີ​ສໍາລັບ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ເປັນ​ຄ່າ​ບວກ!​ເປັນ​ພາຣາມິເຕີ​ສໍາລັບ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ເປັນ​ຄ່າ​ບວກ. ຖ້າ​ຄ່າ beta = 1 GAMMA.INV ຈະ​ສົ່ງ​ກັບ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ແກມ​ມາ​ມາດຕະຖານ"}, "GAMMA.INV": {"a": "(probability; alpha; beta)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ແກມ​ມາສະ​ສົມ: ຖ້າ​ຄ່າ p = GAMMA.DIST(x,...) แล้วค่า GAMMA.INV(p,...) ຈະ = x", "ad": "​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ແບບ​ແກມ​ມາ ​ແລະ​ເປັນ​ຈໍານວນ​ໃນ​ຊ່ວງ​ປິດ​ແຕ່ 0 ຫາ 1!​ແມ່ນ​ພາຣາມິເຕີ​ສໍາລັບ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ເປັນ​ຄ່າ​ບວກ!​ເປັນ​ພາຣາມິເຕີ​ສໍາລັບ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ເປັນ​ຄ່າ​ບວກ. ຖ້າ​ຄ່າ beta = 1 GAMMA.INV ຈະ​ສົ່ງ​ກັບ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ແກມ​ມາ​ມາດຕະຖານ"}, "GAMMALN": {"a": "(x)", "d": "ສະ​ແດງ​ຜົນໂລ​ກາ​ລິດ​ທຳ​ມະ​ຊາດ​ຂອງ​ຟັງ​ຄ໌​ຊັນ​ແກມ​ມາ", "ad": "ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ຄິດ​ໄລ່ GAMMALN, ຕົວ​ເລກ​ເປັນ​ບວກ"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ໂລກາ​ລິດ​ທໍາ​ມະ​ຊາດ​ຂອງ​ຟັງ​ຊັນ​ແກມມາ", "ad": "​ເປັນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ຄໍານວນ​ຫາ GAMMALN.PRECISE ​ແລະ​ຕ້ອງ​ເປັນ​ຈໍານວນ​ບວກ​ເທົ່າ​ນັ້ນ"}, "GAUSS": {"a": "(x)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ທີ່​ນ້ອຍ​ກວ່າ​ການ​ແຈກ​ຢາຍ​ສະ​ສົມ​ປົກກະຕິ​ມາດຕະຖານ 0.5", "ad": "​ແມ່ນ​ຄ່າ​ທີ່ທ່ານ​ຕ້ອງການ​ການແຈກ​ຢາຍ"}, "GEOMEAN": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າສະເລ່ຍເລຂາຄະນິດຂອງອາ​ເຣຍ໌ ຫຼືຊ່ວງຂອງຂໍ້ມູນທີ່ເປັນຕົວເລກບວກ", "ad": "​ແມ່ນຕົວເລກ ຫຼືຊື່, ອາເຣຍ໌ ຫຼືການອ້າງອີງທີ່ມີຕົວເລກ 1 ຫາ 255 ຕົວ​ເລກທີ່ທ່ານຕ້ອງການຫາຄ່າສະເລ່ຍເລຂາຄະນິດ"}, "GROWTH": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "ຜົນ​ໄດ້​ຮັບຕົວເລກທີ່ປະກອບຢູ່ໃນແນວໂນ້ມການເຕີບໂຕທາງເລກກຳລັງທີ່ກົງກັບຈຸດຂໍ້ມູນທີ່ຮູ້ຄ່າ", "ad": "ເປັນຊຸດຂອງຄ່າ y ທີ່ທ່ານຮູ້ຢູ່ແລ້ວໃນຄວາມສຳພັນ y = b*m^x, ອາ​ເຣ​ຍ໌ ຫຼືຊ່ວງຂອງຈຳນວນບວກ!ເປັນຊຸດຕົວເລືອກຂອງຄ່າ x ທີ່ທ່ານອາດຈະຮູ້ຢູ່ ແລ້ວໃນຄວາມສຳພັນ y = b*m^x, ເປັນອາ​ເຣຍ໌ ຫຼືຊ່ວງທີ່ມີຂະໜາດດຽວກັນກັບ Known_y's!ເປັນຄ່າ x ໃໝ່ທີ່ທ່ານຕ້ອງການ GROWTH ເພື່ອ​ໃຫ້​ຜົນ​ໄດ້​ຮັບຄ່າ y ທີ່​ສອດ​ຄ້ອງ!ເປັນຄ່າຄວາມ​ຈິງ: ຄ່າຄົງທີ່ b ຖືກ​ຄິດ​ໄລ່ຕາມປົກກະຕິຖ້າຄ່າ Const = TRUE; ຄ່າຄົງທີ່ b ຖືກຕັ້ງໃຫ້ເທົ່າ 1 ຖ້າຄ່າ Const = FALSE ຫຼືຖືກ​ລະ​ເວັ້ນ"}, "HARMEAN": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າສະເລ່ຍຮາໂມນິກ (harmonic - H.M.)ຂອງຊຸດຂໍ້ມູນຈຳນວນບວກ:ສ່ວນກັບຂອງຄ່າສະເລ່ຍເລກຄະນິດທີ່ມີສູດເປັນສ່ວນກັບ", "ad": "​ແມ່ນຕົວ​ເລກ ຫຼືຊື່, ອາເຣຍ໌ ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກ 1 ຫາ 255 ຕົວ​ເລກທີ່ທ່ານຕ້ອງການຫາຄ່າສະເລ່ຍຮາໂມນິກ "}, "HYPGEOM.DIST": {"a": "(sample_s; number_sample; population_s; number_pop; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ​ໄຮ​ເປີ​ຢີ​ໂອ​ເມ​ຕຣິກ", "ad": "​ແມ່ນ​ຈໍານວນ​ຄັ້ງ​ທີ່​ສໍາ​ເລັດ​ໃນ​ຊຸດ​ຕົວຢ່າງ!​ແມ່ນ​ຂະໜາດ​ຂອງ​ຕົວຢ່າງ!​ແມ່ນ​ຈໍານວນ​ຄັ້ງ​ທີ່​ສຳ​ເລັດ​ຈາກ​ປະຊາກອນ​ທັງ​ໝົດ!​ແມ່ນ​ຂະໜາດ​ຂອງ​ປະຊາກອນ!​ແມ່ນ​ຄ່າ​ຄວາມ​ຈິງ: ສໍາລັບ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ແບບ​ສະ​ສົມ, ​ໃຫ້​ໃຊ້ TRUE; ສໍາລັບ​ຟັງ​ຊັນ​ຄວາມ​ໜາ​ແໜ້ນ​ຂອງ​ຄວາ​ມ​ເປັນ​ໄປ​ໄດ້, ​ໃຫ້​ໃຊ້ FALSE"}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ​ໄຮ​ເປີ​ຢີ​ໂອ​ເມ​ຕຣິກ", "ad": "​ແມ່ນ​ຈໍານວນ​ຄັ້ງ​ທີ່​ສໍາ​ເລັດ​ໃນ​ຊຸດ​ຕົວຢ່າງ!​ແມ່ນ​ຂະໜາດ​ຂອງ​ຕົວຢ່າງ!​ແມ່ນ​ຈໍານວນ​ຄັ້ງ​ທີ່​ສຳ​ເລັດ​ຈາກ​ປະຊາກອນ​ທັງ​ໝົດ!​ແມ່ນ​ຂະໜາດ​ຂອງ​ປະຊາກອນ"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "ຄຳນວນຫາຈຸດທີ່ເສັ້ນຈະຕັດແກນ y ໂດຍການໃຊ້ເສັ້ນຖົດ​ຖອຍ​ທີ່​ພໍ​ດີ​ທີ່​ສຸດ​ທີ່​ໄດ້​ແຕ້ມ​ລົງຜ່ານຄ່າ x ແລະ y ທີ່ຮູ້ຢູ່ແລ້ວ", "ad": "ເປັນຊຸດຂອງຄ່າສັງເກດ ຫຼືຂໍ້ມູນທີ່ບໍ່ເປັນອິດສະຫຼະ ແລະສາມາດເປັນໄດ້ທັງຕົວ​ເລກ ຫຼື ຊື່, ອາ​ເຣຍ໌ ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກ!ເປັນຊຸດຂອງຄ່າສັງເກດ ຫຼືຂໍ້ມູນທີ່ເປັນອິດສະຫຼະ ແລະ ສາມາດເປັນໄດ້ທັງ​ຕົວ​ເລກ, ຊື່, ອາ​ເຣຍ໌ ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກ"}, "KURT": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າເຄີໂຕສີດສຂອງຊຸດຂໍ້ມູນ", "ad": "​ແມ່ນຕົວເລກ ຫຼື ຊື່, ອາ​ເຣຍ໌,ຫຼື ການອ້າງອີງ 1 ຫາ 255 ຄ່າທີ່ມີຈໍານວນທີ່ທ່ານຕ້ອງການຫາຄ່າ ເຄີໂຕສີດສ"}, "LARGE": {"a": "(array; k)", "d": "ສົ່ງຄືນຄ່າຫຼາຍທີ່ສຸດໃນລຳດັບທີ K ຂອງຊຸດຂໍ້ມູນ. ຕົວຢ່າງເຊັ່ນ, ຕົວເລກທີ່ຫຼາຍທີ່ສຸດ​ໃນອັນດັບທີ 5 ຂອງຊຸດຕົວເລກ", "ad": "ເປັນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງຂອງຂໍ້ມູນທີ່ທ່ານຕ້ອງການຫາຄ່າທີ່ຫຼາຍທີ່ສຸດໃນລຳດັບທີ K!ເປັນລຳດັບທີ (ຈາກຄ່າຫຼາຍສຸດ) ​ໃນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງຂອງຫ້ອງທີ່ທ່ານຕ້ອງການໃຫ້ສົ່ງຄືນ"}, "LINEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "ຜົນ​ໄດ້​ຮັບຄ່າສະຖິຕິເຊິ່ງຈະບອກລັກສະນະແນວໂນ້ມເສັ້ນຊື່ທີ່ກົງກັບຈຸດຂໍ້ມູນທີ່ຮູ້ຄ່າ,ໂດຍການປັບໃຫ້ເປັນເສັ້ນຊື່ພໍດີຈາກການນຳໃຊ້ວິທີກຳລັງສອງນ້ອຍສຸດ", "ad": "ເປັນຊຸດຂອງຄ່າ y ທີ່ທ່ານຮູ້ຢູ່ແລ້ວໃນຄວາມສຳພັນ y = mx + b!ເປັນຕົວເລືອກຂອງຊຸດຄ່າ x ທີ່ທ່ານອາດຈະຮູ້ຢູ່ ແລ້ວໃນຄວາມສຳພັນ y = mx + b!ເປັນຄ່າຄວາມ​ຈິງ: ຄ່າຄົງທີ່ b ຖືກຄຳນວນຕາມປົກກະຕິຖ້າຄ່າ Const = TRUE ຫຼືຖືກລະເວັ້ນ; b ຖືກຕັ້ງຄ່າໃຫ້ເທົ່າ 0 ຖ້າຄ່າ Const = FALSE!ເປັນຄ່າຄວາມ​ຈິງ: ຜົນ​ໄດ້​ຮັບຄ່າສະຖິຕິການ​ຖົດ​ຖອຍ​ເພີ່ມ​ເຕີມ = TRUE; ສົ່ງຄືນຄ່າສຳປະສິດ m ແລະຄ່າຄົງທີ່ b = FALSE ຫຼືຖືກ​ລະ​ເວັ້ນ"}, "LOGEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "ຜົນ​ໄດ້​ຮັບຄ່າສະຖິຕິເຊິ່ງຈະບອກລັກສະນະເສັ້ນ​ໂຄ້ງ​ເອັກ​ໂປ​ເນັນ​ຊຽ​ລທີ່ກົງກັບຈຸດຂໍ້ມູນທີ່ຮູ້ຄ່າ", "ad": "ເປັນຊຸດຂອງຄ່າ y ທີ່ທ່ານຮູ້ຢູ່ແລ້ວໃນຄວາມສຳພັນ y = b*m^x!ເປັນຕົວເລືອກຂອງຊຸດຄ່າ x ທີ່ທ່ານອາດຈະຮູ້ຢູ່ ແລ້ວໃນຄວາມສຳພັນ y = b*m^x!ເປັນຄ່າຄວາມ​ຈິງ: ຄ່າຄົງທີ່ b ຖືກຄຳນວນຕາມປົກກະຕິຖ້າຄ່າ Const = TRUE ຫຼືຖືກລະເວັ້ນ; b ຖືກຕັ້ງຄ່າໃຫ້ເທົ່າ 1 ຖ້າຄ່າ Const = FALSE!ເປັນຄ່າຄວາມ​ຈິງ: ຜົນ​ໄດ້​ຮັບຄ່າສະຖິຕິການ​ຖົດ​ຖອຍ​ເພີ່ມ​ເຕີມ = TRUE; ສົ່ງຄືນຄ່າສຳປະສິດ m ແລະຄ່າຄົງທີ່ b = FALSE ຫຼືຖືກ​ລະ​ເວັ້ນ"}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ສະ​ສົມ​ແບບ lognormal ຂອງ x ​ໂດຍ​ທີ່ ln(x) ​ເປັນ​ການ​ແຈ​ກຢາຍ​ແບບ​ປົກກະຕິ​ທີ່​ມີ​ພາຣາມິເຕີ Mean และ Standard_dev", "ad": "​​ແມ່ນ​ຄ່າ​ຄວາ​ມ​ເປັນ​ໄປ​ໄດ້ທີ່​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ແບບ lognormal ​ແລະ​ຕ້ອງ​ເປັນ​ຕົວ​ເລກ​ໃນ​ຊ່ວງ​ປິດ 0 ຫາ 1!​​ແມ່ນ​ຄ່າ​ສະ​ເລ່ຍຂອງ ln(x)!​ແມ່ນ​ສ່ວນ​ຜິດ​ບ່ຽງ​ມາດຕະຖານ​ຂອງ ln(x) ​ແລະ​ຕ້ອງ​ເປັນຈຳນວນ​ບວກ"}, "LOGNORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈ​ກຢາຍ​ແບບ lognormal ສະ​ສົມ​ຂອງ x ​ໂດຍ​ທີ່ ln(x) ​ແມ່ນ​ການ​ແຈກ​ຢາຍ​ແບບ​ປົກກະຕິ ທີ່​ໃຊ້​ຄ່າ​ພາຣາມິເຕີ Mean ​ແລະ Standard_dev", "ad": "​ແມ່ນ​ຄ່າ x ທີ່​ທ່ານ​ຕ້ອງການ​ໃຊ້​ໃນ​ການ​ປະ​ເມີນ​ຜົນ​ຟັງ​ຊັນ ​ແລະ​ຕ້ອງ​ເປັນ​ຈໍານວນ​ບວກ​ເທົ່າ​ນັ້ນ!​ແມ່ນ​ຄ່າ​ສ​ະ​ເລ່ຍຂອງ ln(x)!​ແມ່ນ​ສ່ວນ​ຜິດ​ບ່ຽງ​ມາດ​ຕະຖານຂອງ ln(x) ​ແລະ​ຕ້ອງ​ເປັນ​ຈໍານວນ​ບວກ​ເທົ່າ​ນັ້ນ!​ແມ່ນ​ຄ່າ​ຄວາມຈິງ: ສໍາລັບ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ແບ​ບສະ​ສົມ, ​ໃຫ້​ໃຊ້ TRUE; ສໍາລັບ​ຟັງ​ຊັນ​ຄວາມ​ໜາ​ແໜ້ນ​ຂອງ​ຄວາມ​ເປັນ​ໄປ​ໄດ້, ​ໃຫ້​ໃຊ້ FALSE"}, "LOGNORM.INV": {"a": "(probability; mean; standard_dev)", "d": "ສົ່ງຄືນຄ່າປີ້ນຄືນຂອງຟັງຊັນການແຈກຢາຍສະສົມແບບລ໋ອກນໍມັນຂອງ x, ໂດຍທີ່ ln(x) ເປັນການແຈກຢາຍແບບປົກກະຕິທີ່ໃຊ້ພາຣາມິເຕີເປັນຕົວກາງ ແລະ Standard_dev", "ad": "ເປັນຄ່າຄວາມເປັນໄປໄດ້ທີ່ໄດ້ຈາກການແຈກຢາຍແບບລ່ອກນໍມັນ, ຕ້ອງເປັນຈຳນວນລະຫວ່າງ! 0 ຫາ 1, ເປັນຄ່າສະເລ່ຍຂອງ ln(x)!ເປັນການ​ຜິດ​ບ່ຽງ​ມາດຕະຖານຂອງ ln(x),ຕ້ອງເປັນຈຳນວນບວກເທົ່ານັ້ນ"}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈ​ກຢາຍ​ແບບ lognormal ສະ​ສົມ​ຂອງ x ​ໂດຍ​ທີ່ ln(x) ​ແມ່ນ​ການ​ແຈກ​ຢາຍ​ແບບ​ປົກກະຕິ ທີ່​ໃຊ້​ຄ່າ​ພາຣາມິເຕີ Mean ​ແລະ Standard_dev", "ad": "​ແມ່ນ​ຄ່າ x ທີ່​ທ່ານ​ຕ້ອງການ​ໃຊ້​ໃນ​ການ​ປະ​ເມີນ​ຜົນ​ຟັງ​ຊັນ ​ແລະ​ຕ້ອງ​ເປັນ​ຈໍານວນ​ບວກ​ເທົ່າ​ນັ້ນ!​ແມ່ນ​ຄ່າ​ສ​ະ​ເລ່ຍຂອງ ln(x)!​ແມ່ນ​ສ່ວນ​ຜິດ​ບ່ຽງ​ມາດ​ຕະຖານຂອງ ln(x) ​ແລະ​ຕ້ອງ​ເປັນ​ຈໍານວນ​ບວກ​ເທົ່າ​ນັ້ນ"}, "MAX": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າທີ່ຫຼາຍທີ່ສຸດໃນຊຸດຂອງຄ່າທີ່ລະບຸ. ຍົກເວັ້ນຄ່າຄວາມ​ຈິງ ແລະ ຂໍ້ຄວາມ", "ad": "​ແມ່ນຕົວເລກ, ຫ້ອງຫວ່າງ, ຄ່າຄວາມ​ຈິງ, ຫຼືຕົວເລກຂໍ້ຄວາມ 1 ເຖິງ 255 ຄ່າທີ່ທ່ານຕ້ອງການຫາທີ່ໃຫ່ຍທີ່ສຸດ"}, "MAXA": {"a": "(value1; [value2]; ...)", "d": "ສະ​ແດງ​ຜົນຄ່າໃຫຍ່​ສຸດ​ຢູ່​ໃນ​ກຸ່ມ​ຂອງຄ່າ. ບໍ່​ປະ​ຕິ​ເສດຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ ແລະ​ຂໍ້​ຄວາມ", "ad": "ແມ່ນ 1 ຫາ 255 ຕົວ​ເລກ, ເຊວ​ຫວ່າງ​ເປົ່າ, ຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ, ຫຼື​ຕົວ​ເລກ​ຂໍ້​ຄວາມ​​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ສູງ​ສຸດ"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "ຕອບຄ່າສູງສຸດໃນຈຳນວນຕາລາງທີ່ລະບຸໂດຍຈຳນວນເງື່ອນໄຂ ຫຼືເກນກຳນົດທີ່ເຈາະຈົງໃຫ້", "ad": "ຕາລາງຕ່າງໆທີ່ໃຊ້ເພື່ອລະບຸຄ່າສູງສຸດ!ແມ່ນໄລຍະຂອງຕາລາງຕ່າງໆທີ່ທ່ານຕ້ອງການປະເມີນສຳລັບເງື່ອນໄຂສະເພາະ!ແມ່ນເປັນເງື່ອໄຂ ຫຼື ເກນກຳນົດໃນຮູບແບບຂອງຕົວເລກ, ສຳນວນ ຫຼື ຂໍ້ຄວາມທີ່ລະບຸວ່າຕາລາງໃດຈະຖືກຮວມເມື່ອມີການລະບຸຄ່າສູງສຸງ"}, "MEDIAN": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າກາງ, ຫຼືຈຳນວນທີ່ຢູ່ເຄິງກາງຂອງຊຸດຈຳນວນທີ່ລະບຸ", "ad": "ແມ່ນຕົວເລກ ຫຼືຊື່, ອາ​ເຣຍ໌ ຫຼືການອ້າງອີງທີ່ມີຕົວເລກ 1 ຫາ 255 ຕົວທີ່ທ່ານຕ້ອງການຫາຄ່າກາງ"}, "MIN": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຈຳນວນທີ່ນ້ອຍທີ່ສຸດໃນຊຸດຂອງຄ່າທີ່ລະບຸ. ຍົກເວັ້ນຄ່າຄວາມ​ຈິງ ແລະຂໍ້ຄວາມ", "ad": "​ແມ່ນຕົວເລກ, ຫ້ອງຫວ່າງ, ຄ່າຄວາມ​ຈິງ,ຫຼືຕົວເລກຂໍ້ຄວາມ 1 ເຖິງ 255 ຄ່າທີ່ທ່ານຕ້ອງການຫາຄ່າທີ່ນ້ອຍທີ່ສຸດ"}, "MINA": {"a": "(value1; [value2]; ...)", "d": "ສະ​ແດງ​ຜົນຄ່ານ້ອຍ​ສຸດ​ຢູ່​ໃນ​ກຸ່ມ​ຂອງຄ່າ. ບໍ່​ປະ​ຕິ​ເສດຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ ແລະ​ຂໍ້​ຄວາມ", "ad": "ແມ່ນ 1 ຫາ 255 ຕົວ​ເລກ, ເຊວ​ຫວ່າງ​ເປົ່າ, ຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ, ຫຼື​ຕົວ​ເລກ​ຂໍ້​ຄວາມ​​ທີ່​ທ່ານ​ຕ້ອງ​ການຕ່ຳ​ສຸດ"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "ຕອບຄ່າຕໍ່າສຸດໃນຈຳນວນຕາລາງທີ່ລະບຸໂດຍຈຳນວນເງື່ອນໄຂ ຫຼືເກນກຳນົດທີ່ເຈາະຈົງໃຫ້", "ad": "ເຊວຕ່າງໆທີ່ໃຊ້ເພື່ອລະບຸຄ່າຕໍ່າສຸດ!ແມ່ນໄລຍະຂອງຕາລາງຕ່າງໆທີ່ທ່ານຕ້ອງການປະເມີນສຳລັບເງື່ອນໄຂສະເພາະ!ແມ່ນເປັນເງື່ອໄຂ ຫຼື ເກນກຳນົດໃນຮູບແບບຂອງຕົວເລກ, ສຳນວນ ຫຼື ຂໍ້ຄວາມທີ່ລະບຸວ່າເຊວໃດຈະຖືກຮວມເມື່ອມີການລະບຸຄ່າຕໍ່າສຸດ"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ທີ່​ເກີດ​ຂຶ້ນ ຫຼືຊໍ້າ​ກັນ​ເລື້ອຍໆ​ທີ່​ສຸດ​ໃນ​ອາ​ເຣຍ໌ ຫຼືຊ່ອງ​ຂອງ​ຂໍ້​ມູນ", "ad": "​ແມ່ນ​ຕົວ​ເລກ 1 ຫາ 255 ຫຼື ຊື່, ອາ​ເຣຍ໌ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ປະກອບ​ມີ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ຫາ​ຄ່າ​ຖານ​ນິຍົມ"}, "MODE.MULT": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງ​ຄືນ​ອາ​ເຣຍ໌​ໃນ​ແນວ​ຕັ້ງ​ຂອງ​ຄ່າ​ໃນ​ອາ​ເຣຍ໌ ຫຼື​ໃນ​ຊ່ວງ​ຂອງ​ຂໍ້​ມູນ​ທີ່​ເກີດ​ຂຶ້ນ ຫຼືຊໍ້າ​ກັນ​ເລື້ອຍໆ​ທີ່​ສຸດ.  ສໍາລັບ​ອາ​​ເຣຍ໌​ແນວ​ນອນ ​ໃຫ້​ໃຊ້ =TRANSPOSE(MODE.MULT(number1,number2,...))", "ad": "​ແມ່ນ​ຕົວ​ເລກ, ຊື່, ອາ​​ເຣຍ໌ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ມີຄ່າ​ເປັນ​ຕົວ​ເລກ 1 ​ຫາ 255 ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ຫາ​ຖານ​ນິຍົມ"}, "MODE.SNGL": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າທີ່ເກີດຂຶ້ນເລື້ອຍໆ ຫຼື ເຮັດຊໍ້າຫຼາຍທີ່ສຸດໃນອາ​ເຣຍ໌ ຫຼືຊ່ວງຂອງຂໍ້ມູນ", "ad": "​ແມ່ນຕົວເລກ, ຊື່, ອາ​ເຣຍ໌, ຫຼືການອ້າງອີງ 1 ຫາ 255 ຄ່າທີ່ທ່ານຕ້ອງການຫາຄ່າຖານນິຍົມ"}, "NEGBINOM.DIST": {"a": "(number_f; number_s; probability_s; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ​ທະວີ​ນາມ​ລົບ (negative binomial distribution) ​ເຊິ່ງ​ເປັນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ຈະມີ​ຄວາມ​ລົ້ມ​ເຫຼວ Number_f ຄັ້ງ ​ເກີດ​ຂຶ້ນ​ກ່ອນ​ທີ່​ຈະ​ເກີດ​ຄວາມ​ສໍາ​ເລັດ​ຄັ້ງ​ທີ Number_s, ​ໂດຍ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ຈະ​ສໍາ​ເລັດ​ໃນ​ແຕ່​ລະກາ​ນທົດລອງ​ເທົ່າ​ກັບ Probability_s ", "ad": "​ແມ່ນ​ຈໍານວນ​ຄັ້ງ​ທີ່​ລົ້ມ​ເຫຼວ!​ແມ່ນ​ຈໍານວນ​ຄັ້ງ​ທີ່​ສຳ​ເລັດ​ທີ່​ໃຊ້​ເປັນ​ຕົວທຽບ​ກັບ​ຈໍາ​ນວນ​ຄັ້ງ​ທີ່​ລົ້ມ​ເຫຼວ!​ແມ່ນຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ຈະ​ສຳ​ເລັດ​ໃນ​ແຕ່​ລະກາ​ນທົດ​ລອງ; ຕ້ອງ​ເປັນ​ຄ່າ​ລະຫວ່າງ 0 ຫາ 1!​ແມ່ນ​ຄ່າ​ຄວາ​ມຈິງ: ​ໃຊ້​ຄ່າ TRUE ຖ້າ​ຕ້ອງການ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ຖີ່​ແບບ​ສະ​ສົມ ຫຼື​ໃຊ້​ຄ່າ FALSE ຖ້າ​ຕ້ອງການ​ຟັງ​ຊັນ​ມວນ​ຄວາມ​​ເປັນ​ໄປ​ໄດ້"}, "NEGBINOMDIST": {"a": "(number_f; number_s; probability_s)", "d": "ຕອບຄ່າການແຈກຢາຍແບບທະວິນາມລົບເຊິ່ງເປັນຄ່າຄວາມເປັນໄປໄດ້ທີ່ຈະມີຄວາມລົ້ມເຫຼວ Number_f ຄັ້ງ ເກີດຂຶ້ນກ່ອນທີ່ຈະເກີດຄວາມສໍາເລັດຄັ້ງທີ Number_s, ໂດຍຄ່າຄວາມເປັນໄປໄດ້ທີ່ຈະສໍາເລັດໃນແຕ່ລະການທົດລອງເທົ່າກັບ Probability_s ", "ad": "ແມ່ນຈໍານວນຄັ້ງທີ່ລົ້ມເຫຼວ!ແມ່ນຈໍານວນຄັ້ງທີ່ສຳເລັດທີ່ໃຊ້ເປັນຕົວທຽບກັບຈໍານວນຄັ້ງທີ່ລົ້ມເຫຼວ!ແມ່ນຄ່າຄວາມເປັນໄປໄດ້ທີ່ຈະສຳເລັດໃນແຕ່ລະການທົດລອງ; ຕ້ອງເປັນຄ່າລະຫວ່າງ 0 ຫາ 1"}, "NORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ປົກກະຕິ​ສໍາລັບ​ສ່ວນ​ຜິດ​ບ່ຽງ​ສະ​ເລ່ຍ ​ແລະ​ມາດຕະຖານ", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ການ​ແຈກ​ຢາຍ!​ແມ່ນ​ຄ່າ​ສະ​ເລ່ຍ​ເລກ​ຄະນິດ​ຂອງ​ການ​ແຈກ​ຢາຍ!​​ແມ່ນ​ສ່ວນຜິດ​ບ່ຽງ​ມາດຕະຖານ​ຂອງ​ການ​ແຈກ​ຢາຍ ​ເຊິ່ງ​ເປັນຕົວ​ເລກ​ຄ່າ​ບວກ!​ແມ່ນ​ຄ່າ​ຄວາມ​ຈິງ: ສໍາລັບ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ແບບ​ສະ​ສົມ ​ໃຫ້​ໃຊ້ TRUE; ສໍາລັບ​ຟັງ​ຊັນ​ຄວາມ​ໜາ​ແໜ້ນ​ຂອງ​ຄວາມ​ເປັນ​ໄປ​ໄດ້ ​ໃຫ້​ໃຊ້ FALSE"}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ສະ​ສົມປົກກະຕິ​ສໍາລັບ​ສ່ວນ​ຜິດ​ບ່ຽງ​ສະ​ເລ່ຍ ​ແລະ​ມາດຕະຖານ", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ການ​ແຈກ​ຢາຍ!​ແມ່ນ​ຄ່າ​ສະ​ເລ່ຍ​ເລກ​ຄະນິດ​ຂອງ​ການ​ແຈກ​ຢາຍ!​​ແມ່ນ​ສ່ວນຜິດ​ບ່ຽງ​ມາດຕະຖານ​ຂອງ​ການ​ແຈກ​ຢາຍ ​ເຊິ່ງ​ເປັນຕົວ​ເລກ​ຄ່າ​ບວກ!​ແມ່ນ​ຄ່າ​ຄວາມ​ຈິງ: ສໍາລັບ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ແບບ​ສະ​ສົມ ​ໃຫ້​ໃຊ້ TRUE; ສໍາລັບ​ຟັງ​ຊັນ​ຄວາມ​ໜາ​ແໜ້ນ​ຂອງ​ຄວາມ​ເປັນ​ໄປ​ໄດ້ ​ໃຫ້​ໃຊ້ FALSE"}, "NORM.INV": {"a": "(probability; mean; standard_dev)", "d": "ສົ່ງຄືນຄ່າການປີ້ນຄືນຂອງການແຈກຢາຍສະ​ສົມແບບປົກກະຕິ (normal cumulative distribution) ສຳລັບຄ່າສະເລ່ຍ ແລະ ສ່ວນຫາຄ່າມາດຕະຖານທີ່ລະບຸ", "ad": "ເປັນຄ່າຄວາມເປັນໄປໄດ້ ທີ່ໄດ້ຈາກການແຈກຢາຍແບບປົກກະຕິຕ້ອງເປັນຈໍານວນລະຫວ່າງ 0 ຫາ 1!ເປັນຄ່າສະເລ່ຍເລກຄະນິ ຂອງການແຈກຢາຍ!ເປັນການ​ຜິດ​ບ່ຽງ​ມາດຕະຖານຂອງການ ແຈກຢາຍ, ແລະ ຕ້ອງເປັນຈຳນວນບວກເທົ່ານັ້ນ"}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "ສົ່ງ​ກັບ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ປົກກະຕິ​ສໍາລັບ​ຄ່າ​ສະ​ເລ່ຍ ​ແລະ​ສ່ວນ​ຜິ​ດບ່ຽງ​ມາດຕະຖານ​ທີ່​ລະ​ບຸ", "ad": "​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ແບບ​ປົກກະຕິ ​ແລະ​ຕ້ອງ​ເປັນ​ຈໍານວນ​ໃນ​ຊ່ວງ​ປິດ 0 ຫາ 1!​ແມ່ນ​ຄ່າ​ສະ​ເລ່ຍ​ເລກ​ຄະນິດ​ຂອງ​ການ​ແຈກ​ຢາຍ!​ແມ່ນ​ສ່ວນ​ຜິດ​ບ່ຽງ​ມາດຕະຖານ​ຂອງ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ເປັນ​ຈໍານວນ​ບວກ"}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ແບບ​ສະ​ສົມ​ປົກກະຕິ (ມີຄ່າ​ສະ​ເລ່ຍ​ເທົ່າ​ສູນ​ ​ແລະຄ່າ​ຜິດ​ບ່ຽງ​ມາດຕະຖານ​ເທົ່າ​ໜຶ່ງ)", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ການ​ແຈກ​ຢາຍ!​ແມ່ນ​ຄ່າ​ຄວາມ​ຈິງ​ສໍາລັບ​ຟັງ​ຊັນທີ່​ຈະ​ສົ່ງ​ຄືນ: ສໍາລັບ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ແບບ​ສະ​ສົມ ​ໃຫ້​ໃຊ້ TRUE; ສໍາລັບ​ຟັງ​ຊັນ​ຄວາມ​ໜາ​ແໜ້ນ​ຂອງ​ຄວາມ​ເປັນ​ໄປ​ໄດ້ ​ໃຫ້​ໃຊ້ FALSE"}, "NORMSDIST": {"a": "(z)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ແບບ​ສະ​ສົມ​ປົກກະຕິ (ມີຄ່າ​ສະ​ເລ່ຍ​ເທົ່າ​ສູນ​ ​ແລະຄ່າ​ຜິດ​ບ່ຽງ​ມາດຕະຖານ​ເທົ່າ​ໜຶ່ງ)", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ການ​ແຈກ​ຢາຍ"}, "NORM.S.INV": {"a": "(probability)", "d": "ສົ່ງຄືນຄ່າປີ້ນຄືນຂອງການແຈກຢາຍສະ​ສົມແບບປົກກະຕິມາດຕະຖານ (ມີຄ່າສະເລ່ຍເທົ່າກັບສູນ ແລະຄ່າ​ຜິດ​ບ່ຽງມາດຕະຖານເທົ່າກັບໜຶ່ງ)", "ad": "ເປັນຄ່າຄວາມເປັນໄປໄດ້ທີ່ໄດ້ຈາກການແຈກຢາຍປົກກະຕິ, ແລະ ຕ້ອງເປັນຈຳນວນລະຫວ່າງ 0 ຫາ 1 "}, "NORMSINV": {"a": "(probability)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ປົກກະຕິ​ມາດຕະຖານ​ສະ​ສົມ (ມີຄ່າ​ສ​ະ​ເລ່ຍ​ເທົ່າ​ສູນ ​ແລະ​ສ່ວນ​ຜິດ​ບ່ຽງ​ມາດຕະຖານ​ເທົ່າ​ກັບ​ໜຶ່ງ)", "ad": "​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ປົກກະຕິ ​ແລະ​ຕ້ອງ​ເປັນ​ຈຳນວນ​ລະຫວ່າງ 0 ຫາ 1"}, "PEARSON": {"a": "(array1; array2)", "d": "ສົ່ງຄືນຄ່າສຳປະສິດສຳພັນຂອງຜະລິດຕະພັນເພຍສັນ (Pearson product moment correlation) ຫຼື ຄ່າ r", "ad": "ເປັນຊຸດຂອງຄ່າທີ່ເປັນອິດສະຫຼະ!ເປັນຊຸດຂອງຄ່າທີ່ບໍ່ເປັນອິດສະຫຼະ"}, "PERCENTILE": {"a": "(array; k)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ເປີ​ຊັນ​ອັນ​ດັບ​ທີ k ​ໃນ​ຊ່ວງ​ໃດ​ໜຶ່ງ", "ad": "​ແມ່ນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງ​ຂໍ້​ມູນ​ທີ່​ກຳນົດ​ຈຸດ​ທີ່​ກ່ຽວຂ້ອງ!​ແມ່ນ​ຄ່າ​ເປີ​ເຊັນ​ທີ່ຢູ່​ລະຫວ່າງ 0 ຫາ 1, ລວມທັງ​ໝົດ"}, "PERCENTILE.EXC": {"a": "(array; k)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ເປີ​ຊັນ​ອັນ​ດັບ​ທີ k ​ໃນ​ຊ່ວງ ບ່ອນ​ທີ່ k ​ແມ່ນ​ຢູ່​ຊ່ວງ 0..1, ສະ​ເພາະ​ເທົ່າ​ນັ້ນ", "ad": "​ແມ່ນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງ​ຂໍ້​ມູນ​ທີ່​ກຳນົດ​ຈຸດ​ທີ່​ກ່ຽວຂ້ອງ!​ແມ່ນ​ຄ່າ​ເປີ​ເຊັນ​ທີ່ຢູ່​ລະຫວ່າງ 0 ຫາ 1, ລວມທັງ​ໝົດ"}, "PERCENTILE.INC": {"a": "(array; k)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ເປີ​ຊັນ​ອັນ​ດັບ​ທີ k ​ໃນ​ຊ່ວງ ບ່ອນ​ທີ່ k ​ແມ່ນ​ຢູ່​ຊ່ວງ 0..1, ສະ​ເພາະ​ເທົ່າ​ນັ້ນ", "ad": "​ແມ່ນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງ​ຂໍ້​ມູນ​ທີ່​ກຳນົດ​ຈຸດ​ທີ່​ກ່ຽວຂ້ອງ!​ແມ່ນ​ຄ່າ​ເປີ​ເຊັນ​ທີ່ຢູ່​ລະຫວ່າງ 0 ຫາ 1, ລວມທັງ​ໝົດ"}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັນ​ດັບ​ໃນ​ຊຸດ​ຂໍ້​ມູນ​ເປັນ​ເປີ​ເຊັນ", "ad": "​ແມ່ນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງ​ຂໍ້​ມູນ​ທີ່​ເປັນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ກໍານົດຕໍາ​ແໜ່​ງສໍາພັນ​ໄດ້!​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ຄ່າ​ອັນ​ດັບ!​​ແມ່ນ​ຄ່າ​ຕົວ​ເລືອກ​ທີ່​ໃຊ້​ລະບຸ​ຈຳນວນ​ຕົວ​ເລກທີ່​ສຳຄັນ​ສໍາລັບ​ຄ່າ​ເປີ​​ເຊັນ​ທີ່​ຖືກ​ສົ່ງ​ຄືນ, ຖ້າ​ບໍ່​ໃສ່​ຄ່າ​ຫຍັງ ທົດ​ສະ​ນິຍົມ​ຈະ​ເປັນ​ສາມ​ຕໍາ​ແໜ່​ງ (0.xxx%)"}, "PERCENTRANK.EXC": {"a": "(array; x; [significance])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັນ​ດັບ​ໃນ​ຊຸດ​ຂໍ້​ມູນ​ເປັນ​ເປີ​ເຊັນ​ຂອງຊຸດ​ຂໍ້​ມູນ​ເປັນ​ເປີ​​ເຊັນ (0..1, ສ​ະ​ເພາະ)ຂອງ​ຊຸດ​ຂໍ້​ມູນ​ນັ້ນ", "ad": "​ແມ່ນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງ​ຂໍ້​ມູນ​ທີ່​ເປັນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ກໍານົດຕໍາ​ແໜ່​ງສໍາພັນ​ໄດ້!​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ຄ່າ​ອັນ​ດັບ!​​ແມ່ນ​ຄ່າ​ຕົວ​ເລືອກ​ທີ່​ໃຊ້​ລະບຸ​ຈຳນວນ​ຕົວ​ເລກທີ່​ສຳຄັນ​ສໍາລັບ​ຄ່າ​ເປີ​​ເຊັນ​ທີ່​ຖືກ​ສົ່ງ​ຄືນ, ຖ້າ​ບໍ່​ໃສ່​ຄ່າ​ຫຍັງ ທົດ​ສະ​ນິຍົມ​ຈະ​ເປັນ​ສາມ​ຕໍາ​ແໜ່​ງ (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(array; x; [significance])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັນ​ດັບ​ໃນ​ຊຸດ​ຂໍ້​ມູນ​ເປັນ​ເປີ​ເຊັນ​ຂອງຊຸດ​ຂໍ້​ມູນ​ເປັນ​ເປີ​​ເຊັນ (0..1, ສ​ະ​ເພາະ)ຂອງ​ຊຸດ​ຂໍ້​ມູນ​ນັ້ນ", "ad": "​ແມ່ນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງ​ຂໍ້​ມູນ​ທີ່​ເປັນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ກໍານົດຕໍາ​ແໜ່​ງສໍາພັນ​ໄດ້!​ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ຄ່າ​ອັນ​ດັບ!​​ແມ່ນ​ຄ່າ​ຕົວ​ເລືອກ​ທີ່​ໃຊ້​ລະບຸ​ຈຳນວນ​ຕົວ​ເລກທີ່​ສຳຄັນ​ສໍາລັບ​ຄ່າ​ເປີ​​ເຊັນ​ທີ່​ຖືກ​ສົ່ງ​ຄືນ, ຖ້າ​ບໍ່​ໃສ່​ຄ່າ​ຫຍັງ ທົດ​ສະ​ນິຍົມ​ຈະ​ເປັນ​ສາມ​ຕໍາ​ແໜ່​ງ (0.xxx%)"}, "PERMUT": {"a": "(number; number_chosen)", "d": "ສົ່ງຄືນຈຳນວນວິທີການປ່ຽນ​ລໍາດັບທີ່ເປັນໄປໄດ້ທັງໝົດ ສຳລັບຈຳນວນວັດຖຸທີ່ກຳນົດໃຫ້ເລືອກຈາກວັດຖຸທັງໝົດ", "ad": "ເປັນຈຳນວນວັດຖຸທັງໝົດ!ເປັນຈຳນວນວັດຖຸໃຫ້ເລືອກໃນແຕ່ລະການປ່ຽນ​ລໍາດັບ"}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "ສົ່ງ​ຄືນ​ຈໍານວນ​ການ​ລຽງສັບ​ປ່ຽນ​ທີ່​ເປັນ​ໄປ​ໄດ້​ທັງ​ໝົດ​ສໍາລັບ​ຈຳນວນ​ວັດຖຸ​ທີ່​ກຳນົດ​ໄວ້ (ທີ່​ມີ​ການ​ຊໍ້າ​ກັນ) ທີ່​ເລືອກ​ຈາກ​ວັດຖຸທັງ​ໝົດ", "ad": "​ແມ່ນ​ຈໍ​ານວນ​ວັດຖຸ​ທັງ​ໝົດ!​ແມ່ນ​ຈໍານວນ​ວັດຖຸ​ທີ່​ໃຫ້​ເລືອກ​ໃນ​ແຕ່​ລະກ​ນລຽງ​ສັບ​ປ່ຽນ"}, "PHI": {"a": "(x)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຟັງ​ຊັນ​ຄວາມ​ໜາ​ແໜ້ນ​ສໍາລັບ​ການ​ແຈກ​ຢາຍ​ປົກກະຕິ​ມາດຕະຖານ", "ad": "​ແມ່ນ​ຈໍານວນ​ທີ່​ທ່ານ​ຕ້ອງການ​ຫາ​ຄ່າ​ຄວາມ​ໜາ​ແໜ້ນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ປົກກະຕິ​ມາດຕະຖານ"}, "POISSON": {"a": "(x; mean; cumulative)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ແບບ Poisson", "ad": "​ແມ່ນ​ຈໍາ​ນວນ​ຂອງ​ເຫດການ!​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ຄາດ​ໄວ້ ​ເຊິ່ງ​ເປັນ​ຈໍານວນ​ບວກ!​ແມ່ນ​ຄ່າ​ຄວາມ​ຈິງ: ​ໃຊ້ TRUE ສໍາລັບ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ Poisson ສະ​ສົມ; ​ໃຊ້ FALSE ສໍາລັບ​ຟັງ​ຊັນ​ຄວາມ​ເປັນ​ໄປ​ໄດ້ Poisson ລວມ"}, "POISSON.DIST": {"a": "(x; lambda; cumulative)", "d": "ສະ​ແດງ​ຜົນການ​ກະ​ຈາຍເອັກ​ຊ໌​ໂປ​ເນັນ​ຊຽວ", "ad": "ແມ່ນຄ່າຂອງ​ຟັງ​ຄ໌​ຊັນ, ຕົວ​ເລກ​ທີ່​ບໍ່​ເປັນ​ລົບ!ແມ່ນ​ຄ່າພາ​ຣາ​ມີ​ເຕີ, ຕົວ​ເລກ​ເປັນ​ບວກ!ແມ່ນຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາສຳ​ລັບ​ຟັງ​ຄ໌​ຊັນ​ຕໍ່​ຜົນ​ໄດ້​ຮັບ: ຟັງ​ຄ໌​ຊັນການ​ກະ​ຈາຍ​ສະ​ສົມ = TRUE; ​ຟັງ​ຄ໌​ຊັນຄວາມ​ໜາ​ແໜ້ນ​ໂອ​ກາດ = False"}, "PROB": {"a": "(x_range; prob_range; lower_limit; [upper_limit])", "d": "ສະ​ແດງ​ຜົນໂອ​ກາດ​ທີ່​ຄ່າ​ຢູ່​ໃນ​ຂອບ​ເຂດ​ຢູ່​ລະ​ຫວ່າງ​ສອງ​ຂີດ​ຈຳ​ກັດ ຫຼື​ເທົ່າ​ກັບ​ຂີດ​ຕ່ຳ", "ad": "ແມ່ນ​ຂອບ​ເຂດ​ຂອງ​ຄ່າຕົວ​ເລກຂອງ x ພ້ອມ​ກັບ​ທີ່​ມີ​ໂອ​ກາດ​ທີ່​ກ່ຽວ​ຂ້ອງ!ແມ່ນ​ກຸ່ມຂອງ​ໂອ​ກາດ​ທີ່​ກ່ຽວ​ຂ້ອງ​ກັບຄ່າຢູ່​ໃນ ຂອບ​ເຂດ_X, ຄ່າລະ​ຫວ່າງ 0 ຫາ 1 ແລະ​ບໍ່​ລວມ 0!ແມ່ນ​ຂອບ​ຕ່ຳ​ຢູ່​ໃນ​ຄ່າ​ສຳ​ລັບ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ໂອ​ກາດ!ແມ່ນ​ຂອບ​ສູງ​ທາງ​ເລືອກ​ຢູ່​ເທິງ​ຄ່າ. ຖ້າ​ຖືກ​ລະ​ເວັ້ນ, ໂອ​ກາດສະ​ແດງ​ຜົນໂອາ​ກາດ​ທີ່ຄ່າ_X ເທົ່າ​ກັບ​ຂີດ_ຕ່ຳ"}, "QUARTILE": {"a": "(array; quart)", "d": "ຜົນໄດ້ຮັບຄ່າໜຶ່ງສ່ວນສີ່ຂອງຊຸດຂໍ້ມູນ", "ad": "ແມ່ນອາເຣຍ໌ ຫຼື ຊ່ວງຫ້ອງຂອງຄ່າຕົວເລກທີ່ທ່ານຕ້ອງການຫາຄ່າໜຶ່ງສ່ວນສີ່!ແມ່ນຕົວເລກ: ຄ່ານ້ອຍສຸດ = 0; ຄ່າໜຶ່ງສ່ວນສີທີ 1 = 1, ຄ່າກາງ = 2, ຄ່າໜຶ່ງສ່ວນສີ່ທີ 3 = 3, ຄ່າສູງສຸດ = 4"}, "QUARTILE.INC": {"a": "(array; quart)", "d": "ສົ່ງຄືນ​ຄ່າໜຶ່ງ​​ສ່ວນສີ່​ຂອງ​ຊຸດ​ຂໍ້​ມູນ ​ໂດຍ​ອີງ​ຕາມ​ຄ່າ​ເປີ​ເຊັນ​ຈາກ 0..1", "ad": "​ແມ່ນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງ​ຫ້ອງ​ຂອງ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ຫາ​ຄ່າ​ໜຶ່ງ​ສ່ວນ​ສີ່!​ແມ່ນ​ຕົວ​ເລກ: ຄ່າ​ນ້ອຍ​ສຸດ = 0, ຄ່າ​ໜຶ່ງ​ສ່ວນ​ສີ​ທີ 1 = 1, ຄ່າ​ກາງ = 2, ຄ່າ​ໜຶ່ງ​ສ່ວນ​ສີ່​ທີ 3 = 3, ຄ່າ​ຫຼາຍສຸດ = 4"}, "QUARTILE.EXC": {"a": "(array; quart)", "d": "ສົ່ງຄືນ​ຄ່າໜຶ່ງ​​ສ່ວນສີ່​ຂອງ​ຊຸດ​ຂໍ້​ມູນ ​ໂດຍ​ອີງ​ຕາມ​ຄ່າ​ເປີ​ເຊັນ​ຈາກ 0..1", "ad": "​ແມ່ນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງ​ຫ້ອງ​ຂອງ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ຫາ​ຄ່າ​ໜຶ່ງ​ສ່ວນ​ສີ່!​ແມ່ນ​ຕົວ​ເລກ: ຄ່າ​ນ້ອຍ​ສຸດ = 0, ຄ່າ​ໜຶ່ງ​ສ່ວນ​ສີ​ທີ 1 = 1, ຄ່າ​ກາງ = 2, ຄ່າ​ໜຶ່ງ​ສ່ວນ​ສີ່​ທີ 3 = 3, ຄ່າ​ຫຼາຍສຸດ = 4"}, "RANK": {"a": "(number; ref; [order])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັນ​ດັບ​ຂອງ​ຕົວ​ເລກ​ທີ່​​ຢູ່​ໃນ​ລາຍການ​ຂອງ​ຕົວ​ເລກ: ຂະໜາດ​ຂອງ​ມັນ​ກ່ຽວ​ພັນ​ກັບ​ຄ່າ​ອື່ນ​ໃນ​ລາຍການ", "ad": "​ເປັນ​ຕົວ​ເລກ​ທີ່​ທນ​ຕ້ອງການ​ຫາ​ຄ່າ​ອັນ​ດັບ!​ແມ່ນ​ອາ​ເຣຍ໌ຂອງ, ຫຼືການ​ອ້າງ​ອີງ​ໄປ​ຫາ​ລາຍການ​ຂອງ​ຕົວ​ເລກ. ຄ່າ​ທີ່​ບໍ່​ແມ່ນຕົວ​ເລກ​ຈະ​ຖືກ​ລະ​ເວັ້ນ!​ແ​ມ່ນຕົວ​ເລກ: ຖ້າ​ຕ້ອງການ​ຄ່າ​ອັນ​ດັບ​ໃນ​ລາຍ​ການ​ທີ່​ລຽງ​ແຕ່​ໃຫຍ່​ຫາ​ນ້ອຍ ​ໃຫ້​ຕັ້ງ​ຄ່າ​ນີ້ = 0 ຫຼືບໍ່​ໃສ່​ຫຍັງ; ຖ້າ​ຕ້ອງການ​ອັນ​ດັບ​ໃນ​ລາຍການ​ທີ່​ລຽງ​ແຕ່​ນ້ອຍ​ຫາ​ໃຫຍ່ ​ໃຫ້​ຕັ້ງຄ່າ​ນີ້ = ຄ່າ​ໃດ​ກໍ່​ໄດ້​ທີ່​ບໍ່​ແມ່ນສູນ"}, "RANK.AVG": {"a": "(number; ref; [order])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັນ​ດັບ​ຂອງ​ຕົວ​ເລກ​ທີ່​ລະບຸ​ຊື່​ຢູ່​ໃນ​ລາຍການ​ຂອງ​ຕົວ​ເລກ: ຂະໜາດ​ຂອງ​ມັນ​ກ່ຽວ​ພັນ​ກັບ​ຄ່າ​ອື່ນ​ໃນ​ລາຍການ; ຖ້າ​ຫຼາຍກວ່າ​ໜຶ່ງ​ຄ່າ​ມີ​ອັນ​ດັບ​ຄື​ກັນ, ອັນ​ດັບ​ສະ​ເລ່ຍຖືກ​ສົ່ງ​ຄືນ", "ad": "​ເປັນ​ຕົວ​ເລກ​ທີ່​ທນ​ຕ້ອງການ​ຫາ​ຄ່າ​ອັນ​ດັບ!​ແມ່ນ​ອາ​ເຣຍ໌ຂອງ, ຫຼືການ​ອ້າງ​ອີງ​ໄປ​ຫາ​ລາຍການ​ຂອງ​ຕົວ​ເລກ. ຄ່າ​ທີ່​ບໍ່​ແມ່ນຕົວ​ເລກ​ຈະ​ຖືກ​ລະ​ເວັ້ນ!​ແ​ມ່ນຕົວ​ເລກ: ຖ້າ​ຕ້ອງການ​ຄ່າ​ອັນ​ດັບ​ໃນ​ລາຍ​ການ​ທີ່​ລຽງ​ແຕ່​ໃຫຍ່​ຫາ​ນ້ອຍ ​ໃຫ້​ຕັ້ງ​ຄ່າ​ນີ້ = 0 ຫຼືບໍ່​ໃສ່​ຫຍັງ; ຖ້າ​ຕ້ອງການ​ອັນ​ດັບ​ໃນ​ລາຍການ​ທີ່​ລຽງ​ແຕ່​ນ້ອຍ​ຫາ​ໃຫຍ່ ​ໃຫ້​ຕັ້ງຄ່າ​ນີ້ = ຄ່າ​ໃດ​ກໍ່​ໄດ້​ທີ່​ບໍ່​ແມ່ນສູນ"}, "RANK.EQ": {"a": "(number; ref; [order])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັນ​ດັບ​ຂອງ​ຕົວ​ເລກ​ທີ່​ລະບຸ​ຊື່​ຢູ່​ໃນ​ລາຍການ​ຂອງ​ຕົວ​ເລກ: ຂະໜາດ​ຂອງ​ມັນ​ກ່ຽວ​ພັນ​ກັບ​ຄ່າ​ອື່ນ​ໃນ​ລາຍການ; ຖ້າ​ຫຼາຍກວ່າ​ໜຶ່ງ​ຄ່າ​ມີ​ອັນ​ດັບ​ຄື​ກັນ, ອັນ​ດັບ​ສູງ​ສຸດ​ຂອງ​ຊຸດ​ຄ່າ​ນັ້ນ​ຈະຖືກ​ສົ່ງ​ຄືນ", "ad": "​ເປັນ​ຕົວ​ເລກ​ທີ່​ທນ​ຕ້ອງການ​ຫາ​ຄ່າ​ອັນ​ດັບ!​ແມ່ນ​ອາ​ເຣຍ໌ຂອງ, ຫຼືການ​ອ້າງ​ອີງ​ໄປ​ຫາ​ລາຍການ​ຂອງ​ຕົວ​ເລກ. ຄ່າ​ທີ່​ບໍ່​ແມ່ນຕົວ​ເລກ​ຈະ​ຖືກ​ລະ​ເວັ້ນ!​ແ​ມ່ນຕົວ​ເລກ: ຖ້າ​ຕ້ອງການ​ຄ່າ​ອັນ​ດັບ​ໃນ​ລາຍ​ການ​ທີ່​ລຽງ​ແຕ່​ໃຫຍ່​ຫາ​ນ້ອຍ ​ໃຫ້​ຕັ້ງ​ຄ່າ​ນີ້ = 0 ຫຼືບໍ່​ໃສ່​ຫຍັງ; ຖ້າ​ຕ້ອງການ​ອັນ​ດັບ​ໃນ​ລາຍການ​ທີ່​ລຽງ​ແຕ່​ນ້ອຍ​ຫາ​ໃຫຍ່ ​ໃຫ້​ຕັ້ງຄ່າ​ນີ້ = ຄ່າ​ໃດ​ກໍ່​ໄດ້​ທີ່​ບໍ່​ແມ່ນສູນ"}, "RSQ": {"a": "(known_ys; known_xs)", "d": "ຜົນ​ໄດ້​ຮັບຄ່າກຳ​ລັງ​ສອງ​ຂອງ​ສຳ​ປາ​ສິດ​ຄວາມ​ສຳ​ພັນ​ຊ່ວງ​ເວ​ລາ​ຜະ​ລິດ​ຕະ​ພັນ​ເພຍ​ສັນຜ່ານຈຸດຂໍ້ມູນທີ່ໃຫ້ໄວ້", "ad": "ເປັນອາເຣ ຫຼືຊ່ວງຂອງຈຸດຂໍ້ມູນ ແລະສາມາດເປັນໄດ້ທັງຕົວ​ເລກ ຫຼືຊື່, ອາເຣຍ໌ ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກ!ເປັນອາ​ເຣ​ຍ໌ ຫຼືຂອບ​ເຂດຂອງຈຸດຂໍ້ມູນ ແລະສາມາດເປັນໄດ້ທັງຕົວ​ເລກ ຫຼື ຊື່, ອາເຣຍ໌ ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກ"}, "SKEW": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າຄວາມອ່ຽງຂອງການແຈກຢາຍ: ເປັນຄ່າທີ່ສະແດງລັກສະນະລະດັບຂອງຄວາມບໍ່ສົມສ່ວນຂອງການແຈກຢາຍອ້ອມຮອບ", "ad": "ຄ່າສະເລ່ຍຄື ຕົວເລກ ຊື່, ອາ​ເຣຍ໌, ຫຼື ການອ້າງອີງທີ່​ມີ​ຕົວ​ເລກ 1 ຫາ 255 ຕົວທີ່ທ່ານຕ້ອງການຫາຄ່າຄວາມອ່ຽງ"}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງ​ກັບ​ຄ່າ​ຄວາມ​ອ່ຽງຂອງ​ການ​ແຈກ​ຢາຍ​ໃນ​ກຸ່ມ​ປະຊາກອນ​ໜຶ່ງ: ​ເປັນ​ຄ່າ​ທີ່​ສະ​ແດງລັກສະນະ​ລະດັບ​ຄວາມ​ບໍ່ົມສ່ວນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ອ້ອມ​ຮອບ​ຄ່າ​ສະ​ເລ່ຍຂອງມັນ", "ad": "​ແມ່ນ​ຕົວ​ເລກ ຫຼືຊື່, ອາ​ເຣຍ໌ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ມີຄ່າ​ເປັນ​ຕົວ​ເລກ​ແຕ່ 1 ຫາ 254 ລາຍການ​ທີ່​ທ່ານ​ຕ້ອງການ​ຫາ​ຄ່າ​ຄວາມ​ອ່ຽງ"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "ຜົນ​ໄດ້​ຮັບຄ່າຄວາມຊັນຂອງການຖົດຖອຍເປັນ​​ເສັ້ນ​ຊື່ຜ່ານຈຸດຂໍ້ມູນທີ່ໃຫ້ໄວ້", "ad": "ເປັນອາເຣ ຫຼືຊ່ວງຂອງຫ້ອງຂອງຈຸດຂໍ້ມູນເອ​ກະ​ລາດ​ເປັນຕົວເລກ ແລະ ສາມາດເປັນໄດ້ທັງຕົວ​ເລກ ຫຼືຊື່, ອາເຣຍ໌ ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກ!ເປັນຊຸດຂອງຈຸດຂໍ້ມູນທີ່ໃຊ້ເປັນຄ່າອິດສະຫຼະ ແລະສາມາດເປັນໄດ້ທັງຕົວ​ເລກ ຫຼື ຊື່, ອາເຣຍ໌ ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກ"}, "SMALL": {"a": "(array; k)", "d": "ສົ່ງຄືນຄ່າທີ່ນ້ອຍທີ່ສຸດໃນລຳດັບທີ K ຂອງຊຸດຂໍ້ມູນ. ຕົວຢ່າງ, ຕົວເລກທີ່ນ້ອຍສຸດເປັນອັນດັບ 5 ຂອງຊຸດຕົວເລກ", "ad": "ເປັນອາ​ເຣຍ໌ ຫຼືຊ່ວງຂອງຂໍ້ມູນຕົວ​ເລກ​ທີ່ທ່ານຕ້ອງການຫາຄ່າທີ່ນ້ອຍສຸດໃນລຳດັບທີ K!ເປັນລຳດັບທີ (ຈາກຄ່າທີ່ນ້ອຍສຸດ) ໃນອາ​ເຣຍ໌ ຫຼືຊ່ວງຂອງຄ່າທີ່ທ່ານຕ້ອງການໃຫ້ສົ່ງຄືນ"}, "STANDARDIZE": {"a": "(x; mean; cumulative)", "d": "ສະ​ແດງ​ຜົນການ​ກະ​ຈາຍ Poisson", "ad": "ແມ່ນ​ຈຳ​ນວນ​ຂອງ​ເຫດ​ການ!ແມ່ນຄ່າຕົວ​ເລກ​ຄາດ​ໄວ້, ຕົວ​ເລກ​ເປັນ​ບວກ!ແມ່ນຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ: ສຳ​ລັບ​ໂອ​ກາດ Poisson ສະ​ສົມ, ໃຊ້ TRUE; ສຳ​ລັບ​ຟັງ​ຄ໌​ຊັນ​ຫຼາຍ​ໂອ​ກາດ, ໃຊ້ False"}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "ຄາດ​ຄະ​ເນ​ສ່ວນ​ຜິດ​ບ່ຽງ​ມາດຕະຖານ​ຈາກ​ຕົວຢ່າງ (ລ​ະ​ເວັ້ນຄ່າ​ຄວາມ​ຈິງ ​ແລະ​ຂໍ້ຄວາມ​ໃນ​ຕົວຢ່າງ)", "ad": "​ແມ່ນ​ຕົວ​ເລກ 1 ຫາ 255 ທີ່​ແທນ​ຕົວຢ່າງ​ຂອງ​ປະຊາກອນ ​ແລະ​ສາມາດ​ເປັນ​ໄດ້​ທັງ​ຕົວ​ເລກ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ມີ​ຕົວ​ເລກ"}, "STDEV.P": {"a": "(number1; [number2]; ...)", "d": "ຄຳນວນຫາການ​ຜິດ​ບ່ຽງມາດຕະຖານຈາກທັງກຸ່ມປະຊາກອນທັງໝົດທີ່ເປັນຂໍ້ພິສູດ (ລະເວັ້ນຄ່າຄວາມ​ຈິງ ແລະ ຂໍ້ຄວາມ)", "ad": "​ແມ່ນຕົວເລກ 1 ເຖິງ 255 ຕົວທີ່ໃຊ້ແທນປະຊາກອນເຊິ່ງສາມາດເປັນໄດ້ທັງຕົວເລກ ຫຼືການອ້າງອີງທີ່ມີຕົວເລກ"}, "STDEV.S": {"a": "(number1; [number2]; ...)", "d": "ປະເມີນການຄາດເຄືອນມາດຖານຈາກຕົວຢ່າງ (ລະເວັ້ນຄ່າຄວາມ​ຈິງ ແລະຂໍ້ຄວາມທີ່ຢູ່ໃນຕົວຢ່າງ)", "ad": "ແມ່ນຕົວເລກ 1 ເຖິງ 255 ຕົວທີ່ແທນຕົວຢ່າງຂອງປະຊາກອນ ແລະ ສາມາດເປັນໄດທັງຕົວເລກ ຫຼືການອ້າງອີງໄປຫາຕົວເລກ"}, "STDEVA": {"a": "(value1; [value2]; ...)", "d": "ປະ​ເມີນ​ຄວາມ​ບ່ຽງ​ເບນ​ມາດ​ຕະ​ຖານ​ອີງ​ຕາມຕົວ​ຢ່າງ, ລວມ​ທັງ ຄ່າ ແລະ​ຂໍ້​ຄວາມຕັກ​ກະ​ວິ​ທະ​ຍາ. ຂໍ້​ຄວາມ ແລະຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ False ມີ​ຄ່າ 0; the ຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ TRUE ມີ 1", "ad": "ເປັນ​ໜຶ່ງ​ໃນ 1 ຫາ 255 ຄ່າ ທີ່​ສອດ​ຄ້ອງ​ກັບຕົວ​ຢ່າງ​ປະ​ຊາ​ກອນ ແລະ​ບໍ່​ສາ​ມາດ​ເປັນ​ຄ່າ, ຫຼືຊື່ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ມີ​ຄ່າ"}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "ຄໍານວນ​ນຫາ​ສ່ວນ​ຜິດ​ບ່ຽງມາດຕະຖານ​ຈາ​ກຸ່ມ​ປະຊາກອນ​ທັງ​ໝົດ​ທີ່​ເປັນ​ຂໍ້​ພິສູດ (ລະ​ເວັ້ນຄ່າ​ຄວາມ​ຈິງ ​ແລະ​ຂໍ້ຄວາມ)", "ad": "​ແມ່ນ​ຕົວ​ເລກ 1 ຫາ 255 ທີ່​ໃຊ້​ແທນ​ປະຊາກອນ ​ແລະ​ສາມາດ​ເປັນ​ໄດ້ທັງ​ຕົວ​ເລກ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ມີ​ຕົວ​ເລກ"}, "STDEVPA": {"a": "(value1; [value2]; ...)", "d": "ຄິດ​ໄລ່​ຄວາມ​ບ່ຽງ​ເບນ​ມາດ​ຕະ​ຖານ​ອີງ​ຕາມ​ປະ​ຊາ​ກອນ​ທັງ​ໝົດ, ລວມ​ທັງ ຄ່າ ແລະ​ຂໍ້​ຄວາມຕັກ​ກະ​ວິ​ທະ​ຍາ. ຂໍ້​ຄວາມ ແລະຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ False ມີ​ຄ່າ 0; the ຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ TRUE ມີ 1", "ad": "ເປັນ​ໜຶ່ງ​ໃນ 1 ຫາ 255 ຄ່າ ທີ່​ສອດ​ຄ້ອງ​ກັບ​ປະ​ຊາ​ກອນ ແລະ​ບໍ່​ສາ​ມາດ​ເປັນ​ຄ່າ, ຊື່, ແຖວ​ລຳ​ດັບ, ການ​ອ້າງ​ອີງ​ທີ່​ມີ​ຄ່າ"}, "STEYX": {"a": "(known_ys; known_xs)", "d": "ຜົນ​ໄດ້​ຮັບຄວາມ​ຜິດ​ພາດ​ມາດ​ຕະ​ຖານ​ຂອງ​ຄ່າ y ທີ່​ຄາດ​ໄວ້​ສຳ​​ລັບ​ແຕ່​ລະ​ຕົວ x ຢູ່​ໃນ​ການ​ຖົດ​ຖອຍ", "ad": "ເປັນອາເຣ ຫຼືຊ່ວງຂອງຈຸດຂໍ້ມູນເອ​ກະ​ລາດ ແລະສາມາດເປັນໄດ້ທັງຕົວ​ເລກ ຫຼືຊື່, ອາເຣຍ໌ ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກ!ເປັນອາ​ເຣ​ຍ໌ ຫຼືຂອບ​ເຂດຂອງຈຸດຂໍ້ມູນ​ເອ​ກະ​ລາດ ແລະສາມາດເປັນໄດ້ທັງຕົວ​ເລກ ຫຼື ຊື່, ອາເຣຍ໌ ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກ"}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "ສົ່ງ​ຄືນຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ t ຂອງ​ນັກຮຽນ", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ໃຊ້​ເພື່ອ​ປະ​ເມີນ​ຜົນ​ການ​ແຈກ​ຢາຍ!​ແມ່ນ​ຈໍານວນ​ຖ້ວນ​ທີ່​ຊີ້​ບອກ​ຈໍານວນ​ອົງສາ​ຂອງ​ຄວາມ​ເປັນ​ອິດສະຫຼະທີ່​ກຳນົດ​ລັກສະນະ​ຂອງ​ການ​ແຈກ​ຢາຍ!ລະບຸ​ຈໍານວນ​ດ້ານ​ຂອງ​ການ​ແຈກ​ຢາຍ​ທີ່​ຈະ​ສົ່ງ​ຄືນ: ການ​ແຈກ​ຢາຍ​ດ້ານ​ດຽວ = 1; ການ​ແຈກ​ຢາຍ​ສອງ​ດ້ານ = 2"}, "TINV": {"a": "(probability; deg_freedom)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ທັງ​ສອງ​ດ້ານ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ t ຂອງ​ນັກຮຽນ", "ad": "​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ແບບ t ທັງ​ສອງ​ດ້ານ​ຂອງ​ນັກຮຽນ ​ແລະ​ເປັນ​ຕົວ​ເລັກ​ໃນ​ຊ່ວງ​ປິດ​ແຕ່ 0 ຫາ 1!​ແມ່ນ​ຈຳນວນ​ເຕັມ​ບວກ​ທີ່​ລະບຸ​ອົງສາ​ຄວາມ​ເປັນ​ອິດສະຫຼະ​ທີ່​ກຳນົດລັກສະນະ​ຂອງ​ການ​ແຈກ​ຢາຍ"}, "T.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "ສົ່ງ​ຄືນຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ t ຂອງ​ນັກຮຽນດ້ານ​ຊ້າຍ", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ໃຊ້​ເພື່ອ​ປະ​ເມີນ​ຜົນ​ການ​ແຈກ​ຢາຍ!​ແມ່ນ​ຈໍານວນ​ຖ້ວນ​ທີ່​ຊີ້​ບອກ​ຈໍານວນ​ອົງສາ​ຂອງ​ຄວາມ​ເປັນ​ອິດສະຫຼະທີ່​ກຳນົດ​ລັກສະນະ​ຂອງ​ການ​ແຈກ​ຢາຍ!​ແມ່ນ​ຄ່າ​ຄວາມ​ຈິງ: ​ສໍາລັບ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ແບບ​ສະ​ສົມ ​ໃຫ້​ໃຊ້ TRUE; ສໍາລັບ​ຟັງ​ຊັນ​ຄວາມ​ໝາ​ແໜ້ນ​ຄວາມ​ເປັນ​ໄປ​ໄດ້ ​ໃຫ້​ໃຊ້ FALSE"}, "T.DIST.2T": {"a": "(x; deg_freedom)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​​ແບບ t ຂອງ​ນັກຮຽນ​ທັງ​ສອງ​ດ້ານ", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ໃຊ້​ເພື່ອ​ປະ​ເມີນ​ຜົນ​ການ​ແຈກ​ຢາຍ!ແມ່ນ​ຈໍານວນ​ຖ້ວນ​ທີ່​ຊີ້​ບອກ​ຈໍານວນ​ອົງສາ​ຂອງ​ຄວາມ​ເປັນ​ອິດສະຫຼະທີ່​ກຳນົດ​ລັກສະນະ​ຂອງ​ການ​ແຈກ​ຢາຍ"}, "T.DIST.RT": {"a": "(x; deg_freedom)", "d": "ສົ່ງ​ຄືນຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ t ຂອງ​ນັກຮຽນດ້ານ​ຂວາ", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ໃຊ້​ເພື່ອ​ປະ​ເມີນ​ຜົນ​ການ​ແຈກ​ຢາຍ!​ແມ່ນ​ຈໍານວນ​ຖ້ວນ​ທີ່​ຊີ້​ບອກ​ຈໍານວນ​ອົງສາ​ຂອງ​ຄວາມ​ເປັນ​ອິດສະຫຼະທີ່​ກຳນົດ​ລັກສະນະ​ຂອງ​ການ​ແຈກ​ຢາຍ"}, "T.INV": {"a": "(probability; deg_freedom)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ທັງ​ສອງ​ດ້ານ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ t ຂອງ​ນັກຮຽນ", "ad": "​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ແບບ t ທັງ​ສອງ​ດ້ານ​ຂອງ​ນັກຮຽນ ​ແລະ​ເປັນ​ຕົວ​ເລັກ​ໃນ​ຊ່ວງ​ປິດ​ແຕ່ 0 ຫາ 1!​ແມ່ນ​ຈຳນວນ​ເຕັມ​ບວກ​ທີ່​ລະບຸ​ອົງສາ​ຄວາມ​ເປັນ​ອິດສະຫຼະ​ທີ່​ກຳນົດລັກສະນະ​ຂອງ​ການ​ແຈກ​ຢາຍ"}, "T.INV.2T": {"a": "(probability; deg_freedom)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ທັງ​ສອງ​ດ້ານ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ t ຂອງ​ນັກຮຽນ", "ad": "​ແມ່ນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ໄດ້​ຈາກ​ການ​ແຈກ​ຢາຍ​ແບບ t ທັງ​ສອງ​ດ້ານ​ຂອງ​ນັກຮຽນ ​ແລະ​ເປັນ​ຕົວ​ເລັກ​ໃນ​ຊ່ວງ​ປິດ​ແຕ່ 0 ຫາ 1!​ແມ່ນ​ຈຳນວນ​ເຕັມ​ບວກ​ທີ່​ລະບຸ​ອົງສາ​ຄວາມ​ເປັນ​ອິດສະຫຼະ​ທີ່​ກຳນົດລັກສະນະ​ຂອງ​ການ​ແຈກ​ຢາຍ"}, "T.TEST": {"a": "(array1; array2; tails; type)", "d": "ສົ່ງຄືນຄ່າຄວາມເປັນໄປໄດ້ທີ່ໄດ້ຈາກການເຮັດ t-Test ຂອງ​ນັກຮຽນ", "ad": "ເປັນຊຸດຂໍ້ມູນທຳອິດ! ເປັນຊຸດຂໍ້ມູນທີ 2! ລະບຸຈຳນວນທາງຂອງການແຈກຢາຍທີ່ຕ້ອງການສົ່ງ​ຄືນ: ການແຈກຢາຍແບບດ້ານດຽວ = 1 ການແຈກຢາຍແບບສອງດ້ານ = 2 !ເປັນຊະນິດຂອງການເຮັດ t-Test: ທົດສອບແບບຄູ່ =1 ສອງຊຸດຕົວຢ່າງທີ່ມີຄວາມຜັນແປເທົ່າກັນ (homoscedastic) =2 ເມື່ອໃຊ້, ສອງຊຸດຕົວຢ່າງທີ່ມີຄວາມຜັນແປບໍ່ເທົ່າກັນ =3"}, "TREND": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "ຜົນ​ໄດ້​ຮັບຄ່າຕົວເລກຢູ່ໃນແນວໂນ້ມເສັ້ນ ເຊິ່ງກົງກັບຈຸດຂໍ້ມູນທີ່ຮູ້ຄ່າ, ນຳໃຊ້ວິທີກຳລັງສອງນ້ອຍທີ່ສຸດ", "ad": "ເປັນຊ່ວງ ຫຼືອາ​ເຣຍ໌ຂອງຄ່າ y ທີ່ທ່ານຮູ້ຢູ່ແລ້ວໃນຄວາມສຳພັນ y = mx + b!ເປັນຕົວເລືອກຂອງໄລຍະ ຫຼືອາ​ເຣຍ໌ຂອງຄ່າ x ທີ່ທ່ານຮູ້ໃນຄວາມສຳພັນ y = mx + b, ເປັນອາ​ເຣຍ໌ທີ່ມີຂະໜາດດຽວກັນກັບ Known_y's!ເປັນຊ່ວງ ຫຼືອາ​ເຣຍ໌ຂອງຄ່າ x ໃໝ່ ທີ່ທ່ານຕ້ອງການ TREND ເພື່ອ​ໃຫ້​ຜົນ​ຄ່າ y ທີ່​ສອດ​ຄ້ອງ!ເປັນຄ່າຄວາມ​ຈິງ: ຄ່າຄົງທີ່ b ຖືກຄຳນວນຕາມປົກກະຕິຖ້າຄ່າ Const = TRUE ຫຼືຖືກ​ລະ​ເວັ້ນ; b ຖືກ​ຕັ້ງ​ໃຫ້ເທົ່າກັບ 0 ຖ້າຄ່າ Const = FALSE"}, "TRIMMEAN": {"a": "(array; percent)", "d": "ສົ່ງຄືນຄ່າສະເລ່ຍຂອງຊຸດຂໍ້ມູນທີ່ເຫຼືອຫຼັງຈາກເຮັດການຕັດບາງສ່ວນຂອງຊຸດຂໍ້ມູນອອກໄປ", "ad": "ເປັນຊ່ວງ ຫຼືອາ​ເຣຍ໌ຂອງຄ່າທີ່ຈະໃຊ້ໃນການຕັດອອກ ແລ້ວຫາຄ່າສະເລ່ຍຂອງສ່ວນທີ່ເຫຼືອ!ເປັນ​ເລກເສດສ່ວນຂອງຈຸດຂໍ້ມູນທີ່ຕ້ອງຕັດອອກຈາກສ່ວນເທິງ ແລະ ສ່ວນລຸ່ມຂອງຊຸດຂໍ້ມູນ"}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "ສົ່ງ​ຄືນ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ກ່ຽວ​ພັນ​ກັບ Student's t-Test", "ad": "​ແມ່ນ​ຊຸດ​ຂໍ້​ມູນ​ທຳ​ອິດ!​ແມ່ນ​ຊຸດ​ຂໍ້​ມູນ​ທີ​ສອງ!ລະບຸ​ຈຳນວນ​ຫາງ​ການ​ແຈກ​ຢາຍ​ເພື່ອ​ສົ່ງ​ຄືນ: ການ​ແຈກ​ຢາຍ​ແບບ​ດ້ານ​ດຽວ = 1; ການ​ແຈກ​ຢາຍ​ແບບ​ສອງ​ດ້ານ = 2!​ແມ່ນ​ປະ​ເພດ​ຂອງ​ການ​ທົດ​ສອບ t-test: ທົດ​ສອບ​ແບບ​ຄູ່ = 1, ສອງ​ຊຸດ​ຕົວ​ຢ່າງ​ທີ່​ມີ​ຄວາມ​ແຕກ​ຕ່າງ​​ເທົ່າ​ກັນ (homoscedastic) = 2, ສອງ​ຊຸດ​ຕົວຢ່າງ​ທີ່​ມີ​ຄວາມ​ແຕກ​ຕ່າງ​ບໍ່​ເທົ່າ​ກັນ = 3"}, "VAR": {"a": "(number1; [number2]; ...)", "d": "ຄາດ​ຄະ​ເນ​ຄ່າ​ຄວາມ​ແຕກ​ຕ່າງ​ຈາກ​ຕົວຢ່າງ (ລະ​ເວັ້ນຄ່າ​ຄວາມ​ຈິງ ​ແລະ​ຂໍ້ຄວາມ​ໃນ​ຕົວຢ່າງ)", "ad": "​ແມ່ນ​ຂໍ້​ພິສູດ​ທີ່​ມີ​ຕົວ​ເລກ 1 ຫາ 255 ທີ່​ກົງກັນ​ກັບ​ຕົວຢ່າງ​ປະ​ຊາກ​ອນ"}, "VAR.P": {"a": "(number1; [number2]; ...)", "d": "ຄຳນວນຫາຄ່າຕ່າງໆຈາກປະຊາກອນທັງໝົດ (ລະເວັ້ນຄ່າຄວາມ​ຈິງ ແລະຂໍ້ຄວາມຂອງປະຊາກອນ)", "ad": "​ແມ່ນຕົວເລກພິສູດ 1 ເຖິງ 255 ຕົວທີ່ໃຊ້ແທນປະຊາກອນ"}, "VAR.S": {"a": "(number1; [number2]; ...)", "d": "ຄາດຄະເນຖານຕ່າງໆຈາກຕົວຢ່າງ (ລະເວັ້ນຄ່າຄວາມ​ຈິງ ແລະຂໍ້ຄວາມໃນຕົວຢ່າງ)", "ad": "​ແມ່ນການພິສູດຕົວເລກ 1 ຫາ 255 ຕົວທີ່ແທນຕົວຢ່າງຂອງປະຊາກອນ"}, "VARA": {"a": "(value1; [value2]; ...)", "d": "ປະ​ເມີນ​​​ການຜັນ​ປ່ຽນ​ອີງ​ຕາມຕົວ​ຢ່າງ, ລວມ​ທັງ ຄ່າ ແລະ​ຂໍ້​ຄວາມຕັກ​ກະ​ວິ​ທະ​ຍາ. ຂໍ້​ຄວາມ ແລະຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ False ມີ​ຄ່າ 0; the ຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ TRUE ມີ 1", "ad": "ເປັນ​ໜຶ່ງ​ໃນ 1 ຫາ 255 ຄ່າ ທີ່​ສອດ​ຄ້ອງ​ກັບຕົວ​ຢ່າງ​ປະ​ຊາ​ກອນ"}, "VARP": {"a": "(number1; [number2]; ...)", "d": "ຄໍານວນ​ຄ່າ​ຄວາມ​ແຕກ​ຕ່າງ​ຈາກ​ປະຊາກອນ​ທັງ​ໝົດ (ລ​ະ​ເວັ້ນຄ່າ​ຄວາມ​ຈິງ ລ​ແລະ​ຂໍ້ຄວວາ​ມ​ໃນ​ປະຊາກອນ)", "ad": "​ແມ່ນ​ຂໍ້​ພິສູດ​ຕົວ​ເລກ 1 ຫາ 255 ທີ່​ໃຊ້​ແທນ​ປະຊາກອນ"}, "VARPA": {"a": "(value1; [value2]; ...)", "d": "ປະ​ເມີນ​​​ການຜັນ​ປ່ຽນ​ອີງ​ຕາມຕົວ​ຢ່າງ, ລວມ​ທັງ ຄ່າ ແລະ​ຂໍ້​ຄວາມຕັກ​ກະ​ວິ​ທະ​ຍາ. ຂໍ້​ຄວາມ ແລະຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ False ມີ​ຄ່າ 0; the ຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ TRUE ມີ 1", "ad": "ເປັນ​ໜຶ່ງ​ໃນ 1 ຫາ 255 ຄ່າ ທີ່​ສອດ​ຄ້ອງ​ກັບຕົວ​ຢ່າງ​ປະ​ຊາ​ກອນ"}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ແບບ <PERSON><PERSON>", "ad": "​ແມ່ນ​ຄ່າ​ທີ່​​ໃຊ້​ໃນ​ການປະ​ເມີນ​ຜົນຟັງ​ຊັນ ​ແລະ​ຕ້ອງບໍ່ເປັນ​ຈໍານວນ​ລົບ!​ແມນ​ພາຣາມີ​ເຕີ​ທີ່​ໃຊ້​ສໍາລັບ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ເປັນ​ຈໍານວນ​ບວກ!​ເປັນ​ພາຣາມີ​ເຕີ​ທີ່​ໃຊ້​ສໍາລັບ​ການ​ແຈກ​ຢາຍ ​ແລະ​ຕ້ອງ​ເປັນ​ຈຳນວນ​ບວກ!​ເປັນ​ຄ່າ​ຄວາມ​ຈິງ ​ໂດຍ​ໃຫ້​ໃຊ້​ຄ່າ TRUE ສໍາລັບ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ຖີ່​ແບບ​ສະ​ສົມ ຫຼື​ໃຫ້​ໃຊ້​ຄ່າ FALSE ສໍາລັບ​ຟັງ​ຊັນ​ມວນ​ຂອງຄວາມ​ເປັນ​ໄປ​ໄດ້"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "ສົ່ງກັບຄ່າການແຈກຢາຍ ແບບ Weibull", "ad": "ເປັນຄ່າທີ່ໃຊ້ໃນການປະເມີນຜົນຟັງຊັນ ແລະ ຕ້ອງບໍ່ເປັນຈຳນວນລົບ!ເປັນຕົວແປທີ່ໃຊ້ ສຳລັບ ການແຈກຢາຍ, ແລະ ຕ້ອງເປັນຈຳນວນບວກ!ເປັນພາຣາມິເຕີທີ່ໃຊ້ສຳລັບການແຈກຢາຍ, ແລະ ຕ້ອງເປັນຈຳນວນ ບວກ! ເປັນພາຣາມິເຕີໂດຍໃຫ້ໃຊ້ຄ່າ TRUE ສຳລັບຟັງ ຊັນການແຈກຢາຍຄວາມຖີ່ແບບສະສົມ, (cumulative distribution function) ຫຼື ໃຫ້ໃຊ້ຄ່າ FALSE,ສຳລັບຟັງຊັນ ຄວາມເປັນໄປໄດ້ລວມ, (probability mass function)"}, "Z.TEST": {"a": "(array; x; [sigma])", "d": "ສົ່ງຄືນຄ່າ P ສອງດ້ານຂອງການທົດສອບ z-test", "ad": "ເປັນອາ​ເຣຍ໌ ຫຼືຊ່ວງຂອງຂໍ້ມູນທີ່ຈະ ໃຊ້ທົດສອບຄ່າ X!ເປັນຄ່າທີ່ໃຊ້ທົດສອບ!ເປັນຊ່ວງທີ່ເປັນ ຕະຖານຂອງປະຊາກອນ (ທີ່ຮູ້ແລ້ວ). ຖ້າບໍ່ໃສ່ຄ່າຫຍັງໄວ້, ຄ່າສ່ວນທີ່ເປັນຕະຖານຂອງຕົວຢ່າງຈະຖືກໃຊ້ໃນການຄຳ ນວນແທນ"}, "ZTEST": {"a": "(array; x; [sigma])", "d": "ສົ່ງ​ຄືນ​ຄ່າ P ​ແບບ​ດ້ານ​ດຽວ​ຂອງ​ກາ​ນທົດ​ສອບ z-test", "ad": "​ແມ່ນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງ​ຂໍ້​ມູນ​ທີ່​ຈະ​ໃຊ້​ທົດ​ສອບ​ຄ່າ X!​ແມ່ນ​ຄ່າ​ທີ່​ໃຊ້​ທົດ​ສອບ!​ແມ່ນ​ສ່ວນ​ຜິດ​ບ່ຽງມາດຕະຖານ​ຂອງ​ປະຊາກອນ (ທີ່​ຮູ້​ຢູ່​ແລ້ວ). ຖ້າ​ບໍ່​ໃສ່​ຄ່າ​ຫຍັງ​ໄວ້, ຄ່າ​ສ່ວນ​ຜິດ​ບ່ຽງມາດຕະຖານ​ຂອງ​ຕົວຢ່າງ​ຈະ​ຖືກ​ໃຊ້​ໃນ​ການ​ຄິດ​ໄລ່​ແທນ"}, "ACCRINT": {"a": "(issue; first_interest; settlement; rate; par; frequency; [basis]; [calc_method])", "d": "ສົ່ງຄືນດອກເບ້ຍຄ້າງຮັບສຳລັບຫຼັກຊັບທີ່ຈ່າຍດອກເບ້ຍເປັນງວດ", "ad": "​ແມ່ນ​ວັນ​ທີອອກຈຳໜ່າຍຂອງຫຼັກຊັບເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນ​ວັນ​ທີຊຳລະດອກເບ້ຍເທື່ອທຳອິດຂອງຫຼັກຊັບເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນອັດຕາດອກເບ້ຍແຕ່ລະປີຂອງຫຼັກຊັບ!​ແມ່ນມູນຄ່າທີ່ໝາຍໄວ້ຂອງຫຼັກຊັບ!​ແມ່ນຈຳນວນຄັ້ງໃນການຈ່າຍດອກເບ້ຍໃນແຕ່ລະປີ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ!​ແມ່ນຄ່າດອກເບ້ຍຄ້າງຮັບຈາກວັນທີອອກ ຈຳໜ່າຍ = TRUE ຫຼື ບໍ່ໃສ່ຄ່າຫຍັງ;ຄຳນວນຈາກວັນຈ່າຍດອກເບ້ຍຄັ້ງໃກ້ສຸດ = FALSE"}, "ACCRINTM": {"a": "(issue; settlement; rate; par; [basis])", "d": "ສົ່ງຄືນດອກເບ້ຍຄ້າງຮັບສຳລັບຫຼັກຊັບທີ່ຈ່າຍດອກເບ້ຍໃນວັນຄົບກຳນົດຖອນ", "ad": "​ແມ່ນ​ວັນ​ທີອອກຈຳໜ່າຍຂອງຫຼັກຊັບ ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອດຖອນຂອງຫຼັກຊັບ ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນອັດຕາດອກເບ້ຍລາຍປີຂອງຫຼັກຊັບ!​ແມ່ນມູນຄ່າທີ່ໝາຍໄວ້ຂອງຫຼັກຊັບ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ"}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "ສົ່ງຄືນຄ່າ​ເສື່ອ​ມລາຄາແບບເສັ້ນຊື່ທີ່ແບ່ງຕາມສ່ວນ ຂອງຊັບ​ສິນສຳລັບຊ່ວງເວລາທາງບັນຊີແຕ່ລະຊ່ວງ", "ad": "​ແມ່ນລາຄາຂອງຊັບ​ສິນ!​ແມ່ນວັນທີທີ່ຊື້ຊັບ​ສິນ!​ແມ່ນວັນທີສິ້ນສຸດຊ່ວງເວລາທຳອິດ!​ແມ່ນມູນຄາເຫຼືອ ເມື່ອໝົດ ອາຍຸຊັບ​ສິນ!​ແມ່ນຊ່ວງເວລາ!​ແມ່ນອັດຕາຄ່າເສື່ອມລາຄາ! year_basis : 0 ກຳນົດໃຫ້ໜຶ່ງປີມີ 360 ວັນ, 1 ແທນປີຕາມຄວາມເປັນຈິງ, 3 ກຳນົດໃຫ້ໜຶ່ງປີມີ 365 ວັນ."}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "ສົ່ງຄືນຄ່າ​ເສື່ອ​ມລາຄາແບບເສັ້ນຊື່ທີ່ແບ່ງຕາມສ່ວນ (prorated) ຂອງຊັບ​ສິນສຳລັບຊ່ວງເວລາທາງບັນຊີແຕ່ລະຊ່ວງ", "ad": "​ແມ່ນລາຄາຂອງຊັບ​ສິນ!​ແມ່ນວັນທີທີ່ຊື້ຊັບ​ສິນ!​ແມ່ນວັນທີສິ້ນສຸດຊ່ວງເວລາທຳອິດ!​ແມ່ນມູນຄາເຫຼືອ ເມື່ອໝົດ ອາຍຸຊັບ​ສິນ!​ແມ່ນຊ່ວງເວລາ!​ແມ່ນອັດຕາຄ່າເສື່ອມລາຄາ! year_basis : 0 ກຳນົດໃຫ້ໜຶ່ງປີມີ 360 ວັນ, 1 ແທນປີຕາມຄວາມເປັນຈິງ, 3 ກຳນົດໃຫ້ໜຶ່ງປີມີ 365 ວັນ."}, "COUPDAYBS": {"a": "(settlement; maturity; frequency; [basis])", "d": "ສົ່ງຄືນຈຳນວນວັນຕັ້ງແຕ່ວັນທີເລີ່ມຕົ້ນງວດການຊຳລະຄ່າກາສານເຖິງວັນທີຊຳລະຄ່າຊື້ຂາຍ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອນ ຂອງຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນຈຳນວນຄັ້ງໃນການຈ່າຍດອກເບ້ຍໃນແຕ່ລະປີ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ"}, "COUPDAYS": {"a": "(settlement; maturity; frequency; [basis])", "d": "ສົ່ງຄືນຈຳນວນວັນໃນງວດຂອງການຈ່າຍດອກເບ້ຍ ເຊິ່ງລວມວັນທີຊຳລະຄ່າຊື້ຂາຍ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອນຂອງຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນຈຳນວນຄັ້ງໃນການຈ່າຍດອກເບ້ຍໃນແຕ່ລະປີ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ"}, "COUPDAYSNC": {"a": "(settlement; maturity; frequency; [basis])", "d": "ສົ່ງຄືນຈຳນວນວັນຕັ້ງແຕ່ວັນທີຊຳລະຄ່າຊື້ຂາຍເຖິງວັນທີຈ່າຍດອກເບ້ຍງວດຖັດໄປ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອນຂອງຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນຈຳນວນເທື່ອໃນການຈ່າຍດອກເບ້ຍໃນແຕ່ລະປີ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ"}, "COUPNCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "ສົ່ງຄືນວັນຈ່າຍດອກເບ້ຍງວດຖັດໄປຫຼັງຈາກວັນທີຊຳລະຄ່າຊື້ຂາຍ", "ad": "​ແມ່ນວັນທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີ ອະນຸກົມ!​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບ ແບບເລກວັນທີອະນຸກົມ!​ແມ່ນຈຳນວນເທື່ອໃນການຈ່າຍດອກເບ້ຍໃນແຕ່ລະປີ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ"}, "COUPNUM": {"a": "(settlement; maturity; frequency; [basis])", "d": "ສົ່ງຄືນຈຳນວນການຈ່າຍດອກເບ້ຍລະຫວ່າງວັນທີຊຳລະຄ່າຊື້ຂາຍ ແລະວັນຄົບກຳນົດຖອນ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າ ຊື້ຂາຍຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອນຂອງຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນຈຳນວນຄັ້ງໃນການຈ່າຍດອກເບ້ຍໃນແຕ່ລະປີ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ"}, "COUPPCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "ສົ່ງຄືນວັນຈ່າຍດອກເບ້ຍງວດທີ່ຜ່ານມາກ່ອນວັນທີຄ່າຊຳລະຄ່າຊື້ຂາຍ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອນຂອງຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນຈຳນວນເທື່ອໃນການຈ່າຍດອກເບ້ຍໃນແຕ່ລະປີ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ"}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "ສົ່ງຄືນດອກເບ້ຍສະສົມທີ່ຊຳລະລະຫວ່າງສອງງວດຄືອັດຕາດອກເບ້ຍ", "ad": "​ແມ່ນຈຳນວນງວດທັງໝົດໃນການຊຳລະ!​ແມ່ນມູນຄ່າປະຈຸບັນ! (Present Value)!​ແມ່ນງວດທຳອິດຂອງການຄຳນວນ!​ແມ່ນງວດສຸດທ້າຍຂອງການຄຳນວນ!​ແມ່ນຊະນິດຂອງຊ່ວງເວລາໃນການຊຳລະເງິນ"}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "ສົ່ງຄືນຄ່າເງິນຕົ້ນສະສົມທີ່ໄດ້ຊຳລະໃຫ້ກັບເງິນກູ້ໃນ ລະຫວ່າງສອງງວດ", "ad": "​ແມ່ນອັດຕາດອກເບ້ຍ!​ແມ່ນຈຳນວນງວດທັງ ໝົດໃນການຜ່ອນຊຳລະ!​ແມ່ນມູນຄ່າປະຈຸບັນ (Present Value)!​ແມ່ນງວດທຳອິດຂອງການຄຳນວນ!​ແມ່ນງວດສຸດທ້າຍ ຂອງການຄຳນວນ!​ແມ່ນຊະນິດຂອງຊ່ວງເວລາໃນການ ຊຳລະເງິນ"}, "DB": {"a": "(cost; salvage; life; period; [month])", "d": "ສົ່ງຄືນຄ່າ​ເສື່ອ​ມລາຄາຂອງຊັບສິນສຳລັບຊ່ວງເວລາທີ່ລະບຸ ໂດຍໃຊ້ວິທີການຍອດ​ເຫຼືອຫຼຸດລົງ​ຄົງ​ທີ່", "ad": "ເປັນລາຄາເລີ່ມຕົ້ນຂອງຊັບສິນ!ເປັນລາຄາຄ່າຊົດເຊີຍຢູ່ຊ່ວງສຸດທ້າຍຂອງອາຍຸຊັບສິນ!ເປັນຈຳນວນຊ່ວງເວລາທັງໝົດທີ່ຊັບສິນຖືກປະເມີນຄ່າ​ເສື່ອ​ມລາຄາ (ບາງເທື່ອເອີ້ນວ່າຊ່ວງການໃຊ້ປະໂຫຍດຂອງຊັບສິນ)!ເປັນຊ່ວງເວລາທີ່ທ່ານຕ້ອງການຫາຄ່າ​ເສື່ອ​ມລາຄາ. ຊ່ວງເວລາຕ້ອງການຖືກລະບຸໃຫ້ຢູ່ໃນໜ່ວຍດຽວກັບອາຍຸຂອງຊັບສິນ!ເປັນຈຳນວນຂອງເດືອນໃນປີທຳອິດ, ຖ້າເດືອນບໍ່ໄດ້ໃສ່ຄ່າຫຍັງ ຄ່າຈະເທົ່າກັບ 12"}, "DDB": {"a": "(cost; salvage; life; period; [factor])", "d": "ສົ່ງຄືນຄ່າເສື່​ອມລາຄາຂອງຊັບ​ສິນສຳລັບຊ່ວງເວລາທີ່ລະບຸ ໂດຍໃຊ້ວິທີການຍອດ​ເຫຼືອຫຼຸດລົງ​ສອງ​ເທົ່າ ຫຼືວິທີການອື່ນທີ່ທ່ານລະບຸ", "ad": "ເປັນລາຄ່າເລີ່ມຕົ້ນຂອງຊັບ​ສິນ!ເປັນລາຄາຊາກຊັບ​ສິນຢູ່ຊ່ວງສຸດທ້າຍຂອງອາຍຸຂອງຊັບ​ສິນ!ເປັນຈຳນວນຊ່ວງເວລາທັງໝົດທີ່ສິນຊັບຖືກປະເມີນຄ່າ​ເສື່ອ​ມລາຄາ (ບາງຄັ້ງເອີ້ນວ່າຊ່ວງການໃຊ້ປະໂຫຍດຂອງຊັບ​ສິນ)!ເປັນຊ່ວງເວລາທີ່ທ່ານຕ້ອງການຄຳນວນຄ່າ​ເສື່ອ​ມລາຄາ. ຊ່ວງເວລາຕ້ອງຖືກລະບຸຢູ່ ໃນໜ່ວຍດຽວກັບອາຍຸຂອງຊັບ​ສິນ!ເປັນອັດຕາ​ເສື່ອ​ມລາຄາ​ທີ່​ຍອດ​ເຫຼືອຫຼຸດລົງ. ຖ້າວ່າໃນກໍລະນີທີ່ບໍ່ໄດ້ໃສ່ຄ່າຫຍັງ, ຄ່າ 2 ຈະຖືກໃຊ້ (ວິທີການຍອດ​ເຫຼືອຫຼຸດລົງ​ສອງ​ເທົ່າ)"}, "DISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "ສະ​ແດງ​ຜົນ​ອັດ​ຕາ​ຫຼຸດ​ລາ​ຄາ​ສຳ​ລັບ​ຫຼັກ​ຊັບ", "ad": "ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​ລາ​ຄາ​ຂອງ​​ຫຼັກ​ຊັບ​ຕໍ່​ມູນ​ຄ່າ​ດ້ານ​ໜ້າ $100!ແມ່ນ​ຄ່າ​ການ​ກູ້​ຄືນ​ຂອງ​ຫຼັກ​ຊັບ​ຕໍ່​ມູນ​ຄ່າດ້ານ​ໜ້າ $100!ແມ່ນ​ປະ​ເພດ​ຂອງ​ເກນ​ການ​ນັບ​ວັນ​ທີ່​ນຳ​ໃຊ້"}, "DOLLARDE": {"a": "(fractional_dollar; fraction)", "d": "ປ່ຽນລາຄາໂດລາໃນ​ຮູບ​ແບບເລກເສດສ່ວນເປັນລາຄາໂດຍລາໃນຮູບແບບເລກທົດສະນິຍົມ", "ad": "​ແມ່ນຈຳນວນໃນຮູບແບບເລກເສດສ່ວນ!​ແມ່ນຈຳນວນເຕັມທີ່ຈະໃຊ້ເປັນຕົວຫານໃນເລກເສດສ່ວນ"}, "DOLLARFR": {"a": "(decimal_dollar; fraction)", "d": "ປ່ຽນລາຄາໂດລາ,ໃນຮູບແບບເລກທົດສະນິຍົມ,ເປັນລາຄາໂດລາ,ໃນຮູບແບບເລກເສດສ່ວນ", "ad": "​ເປັນເລກທົດສະນິຍົມ!​ເປັນຈຳນວນເຕັມທີ່ຈະໃຊ້ເປັນຕົວຫານໃນເລກເສດສ່ວນ"}, "DURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "ສົ່ງຄືນຊ່ວງເວລາຕໍ່ປີຂອງຫຼັກຊັບທີ່ຈ່າຍດອກເບ້ຍເປັນງວດ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດ​​​ຖອດຂອງຫຼັ ຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນອັດຕາດອກເບ້ຍລາຍປີຂອງຫຼັກຊັບ!​ແມ່ນອັດຕາຜົນຕອບແທນລາຍປີຂອງຫຼັກຊັບ!​ແມ່ນຈຳນວນຄັ້ງໃນການຈ່າຍດອກເບ້ຍໃນ ແຕ່ລະປີ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ"}, "EFFECT": {"a": "(nominal_rate; npery)", "d": "ສົ່ງຄືນອັດຕາດອກເບ້ຍທີ່ແທ້ຈິງຕໍ່ປີ", "ad": "​ແມ່ນອັດຕາດອກເບ້ຍທີ່ລະບຸ!​ແມ່ນຈຳນວນງວດທີ່ຄິດທົບຕໍ່ປີ"}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "ສົ່ງຄືນຄ່າ FV ມູນຄ່າໃນອະນາຄົດ ຂອງການລົງທຶນ ຄ່ານີ້ຖືກຄຳນວນ ໂດຍມີພື້ນຖານຢູ່ໃນການຈ່າຍເງິນເປັນງວໂດຍຍອດການຈ່າຍເງິນທີ່ຄົງທີ່ ແລະ ອັດຕາດອກເບ້ຍຕໍ່ຊ່ວງເວລາທີ່ຄົງທີ່", "ad": "ເປັນອັດຕາດອກເບ້ຍຕໍ່ຊ່ວງເວລາ, ຕົວຢ່າງເຊັ່ນ, ໃຫ້ໃຊ້ 6%/4 ສຳລັບການຈ່າຍເງິນທີ່ແບ່ງຈ່າຍທຸກໆ 3 ເດືອນລວມເປັນ 4 ງວດ ທີ່ອັດຕາດອກເບ້ຍ 6% ຕໍ່ປີ!ເປັນຈຳນວນຊ່ວງເວລາທັງໝົດໃນການຈ່າຍເງິນສຳລັບການລົງທຶນ!ເປັນມູນຄ່າປະຈຸບັນ,ຫຼືມູນຄ່າລວມທັງໝົດໃນເວລາປະຈຸບັນຂອງຈຳນວນເງິນໃນທຸກງວດທີ່ຈະຕ້ອງຈ່າຍໃນອະນາຄົດ! ຖ້າລະເວັ້ນ, Pv = 0!ເປັນຄ່າຄວາມ​ຈິງ ຖ້າການຈ່າຍເງິນຢູ່ທີ່ຈຸດເລີ່ມຕົ້ນຂອງຊ່ວງເວລາແຕ່ຄ່າ = 1; ການຈ່າຍເງິນທີ່ຈຸດສຸດທ້າຍຂອງຊ່ວງເວລາແຕ່ຄ່າ = 0 ຫຼື ລະເວັ້ນ"}, "FVSCHEDULE": {"a": "(principal; schedule)", "d": "ສົ່ງຄືນມູນຄ່າໃນອະນາຄົດ (Future Value) ຂອງເງິນຕົ້ນຫຼັງຈາກນຳຊຸດຂໍ້ມູນຂອງອັດຕາດອກເບ້ຍທົບຕົ້ນມາໃຊ້", "ad": "​ແມ່ນມູນຄ່າປະຈຸບັນ (Present Value)!​ແມ່ນອາ​ເຣຍ໌ຂອງອັດຕາດອກເບ້ຍທີ່ຈະໃຊ້"}, "INTRATE": {"a": "(settlement; maturity; investment; redemption; [basis])", "d": "ສົ່ງຄືນຄ່າອັດຕາດອກເບ້ຍຂອງຫຼັກຊັບທີ່ລົງທຶນທັງ​ໝົດ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອນຂອງຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນມູນຄ່າທີ່ລົງທຶນໃນຫຼັກຊັບ!​ແມ່ນມູນຄ່າທີ່ຈະໄດ້ຮັບໃນວັນຄົບກຳນົດຖອນ!​ແມ່ນຊະນິດຂອງຫຼັກເກນໃນການນັບຈຳນວນວັນ"}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "ສົ່ງຄືນຄ່າດອກເບ້ຍທີ່ຕ້ອງຈ່າຍສຳລັບຊ່ວງເວລາທີ່ລະບຸສຳລັບການລົງທືນ, ຄ່າທີ່ຄຳນວນໄດ້ມີພື້ນຖານຢູ່ເທິງການຈ່າຍເງິນເປັນງວດ, ຍອດການຈ່າຍເງິນທີ່ຄົງທີ່ ແລະ ອັດຕາດອກເບ້ຍທີ່ຄົງທີ່", "ad": "ເປັນອັດຕາດອກເບ້ຍຕໍ່ຊ່ວງເວລາ. ຕົວຢ່າງເຊັ່ນ, ໃຫ້ໃຊ້ 6%/4 ສຳລັບການຈ່າຍເງິນທີ່ແບ່ງຈ່າຍທຸກໆ 3 ເດືອນລວມເປັນ 4 ງວດທີ່ອັດຕາດອກເບ້ຍ 6% ຕໍ່ປີ!ເປັນຊ່ວງເວລາ ເຊິ່ງທ່ານຕ້ອງການຫາຄ່າດອກເບ້ຍ ແລະຈະຕ້ອງຢູ່ໃນຊ່ວງ 1 ຫາ Nper!ເປັນຈຳນວນຊ່ວງເວລາທັງໝົດໃນການຈ່າຍເງິນສຳລັບການລົງທຶນ!ເປັນຄ່າ PV (present value-ມູນຄ່າປະຈຸບັນ), ຫຼືມູນຄ່າລວມທັງໝົດໃນເວລາປະຈຸບັນຂອງຈຳນວນເງິນລວມທຸກງວດທີ່ຈະຕ້ອງຈ່າຍໃນອະນາຄົດ!ເປັນມູນຄ່າໃນອະນາຄົດ (FV - future value), ຫຼືຈຳນວນເງິນທີ່ທ່ານຕ້ອງການໃຫ້ຄົງເຫຼືອຫຼັງຈາກຈ່າຍເງິນງວດສຸດທ້າຍ. ຖ້າບໍ່​ໄດ້​ໃສ່​ຄ່າ​ຫຍັງຄ່າໄວ້, ຄ່າ Fv = 0!ເປັນຄ່າຄວາມ​ຈິງທີ່ບົ່ງບອກການກຳນົດເວລາການຈ່າຍເງິນ: ​ຢູ່​ຈຸດ​ສຸດທ້າຍ​ຂອງ​ຊ່ວ​ງ​ເວລາ = 0 ຫຼື ບໍ່​ໃສ່​ຄ່າ​ຫຍັງ​ໄວ້, ຢູ່​ຈຸດ​ເລີ່​ມຕົ້ນ​ຂອງ​ຊ່ວງ​ເວລາ = 1"}, "IRR": {"a": "(values; [guess])", "d": "ສະ​ແດງ​ຜົນອັດ​ຕາ​ພາຍ​ໃນ​ຂອງ​ຜົນ​ໄດ້​ຮັບ​ສຳ​ລັບ​ຊຸດ​ການ​ໄຫຼວຽນ​ເງິນ​ສົດ", "ad": "ແມ່ນແຖວ​ລຳ​ດັບ ຫຼື​ການ​ອ້າງ​ອີງ​ຕໍ່​ເຊວ​ທີ່​ມີ​ຕົວ​ເ​ລກ ເຊິ່ງ​ທ່ານ​ຕ້ອງ​ການ​ຄິດ​ໄລ່​ອັດ​ຕາ​ພາຍ​ໃນ​ຂອງ​ຜົນ​ຕ​ອບ​ແທນ!ແມ່ນ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ເດົາ​ໃກ້​ກັບ​ຜົນ​ໄດ້​ຮັບ​ຂອງ IRR; 0.1 (10 ເປີ​ເຊັນ) ຖ້າ​ຖືກ​ລະ​ເວັ້ນ"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "ສົ່ງຄືນຄ່າດອກເບ້ຍທີ່ຕ້ອງຊຳລະໃນ ລະຫວ່າງເວລາໜຶ່ງຂອງການລົງທຶນ", "ad": "ເປັນອັດຕາດອກເບ້ຍຕໍ່ເວລາຕົວຢ່າງ, ໃຫ້ໃຊ້ 6%/4 ສຳລັບການຊຳລະເງິນທີ່ແບ່ງຊຳລະທຸກໆ 3 ເດືອນລວມເປັນ 4 ງວດທີ່ອັດຕາດອກເບ້ຍ 6% ຕໍ່ປີ!ເປັນຄາບເວລາທີ່ທ່ານຕ້ອງການຫາຄ່າດອກເບ້ຍ!ເປັນຈຳນວນຄາບເວລາທັງໝົດໃນການຊຳລະເງິນຂອງການລົງທຶນ!ເປັນມູນຄ່າລວມທັງໝົດໃນເວລາປະຈຸບັນຂອງຈຳນວນເງິນໃນທຸກງວດທີ່ຈະຕ້ອງຊຳລະໃນອະນາຄົດ"}, "MDURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "ສົ່ງຄືນຊ່ວງເວລາທີ່​ດັດ​ແປງ​ຂອງແມັກຄໍລີ (Macauley Modified Duration ) ຂອງຫຼັກຊັບທີ່ສົມມຸດໃຫ້ມູນຄ່າທີ່ຕັ້ງໄວ້ເປັນ 100 ໂດຍລາ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອດຂອງຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນອັດຕາດອກເບ້ຍຕໍ່ປີຂອງຫຼັກຊັບ!​ແມ່ນອັດຕາຜົນຕອບແທນຕໍ່ປີຂອງຫຼັກຊັບ!​ແມ່ນຈຳນວນຄັ້ງໃນການຈ່າຍດອກເບ້ຍໃນແຕ່ລະປີ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ"}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "ສະ​ແດງ​ຜົນ​ອັດ​ຕາພາຍ​ໃນ​ຂອງ​ຜົນ​ຕອບ​ແທນ​ສຳ​ລັບ​ຊຸດ​ຂອງ​ການ​ໄຫຼວຽນ​ເງິນ​ສົດ, ພິ​ຈາ​ລະ​ນາ​ທັງມູນ​ຄ່າ​ການ​ລົງ​ທຶນ ແລະ​ດອກ​ເບ້ຍ​ຢູ່​ໃນ​ການ​ລົງ​ທຶນ​ເງິນ​ສົດ​ໃໝ່", "ad": "ແມ່ນແຖວ​ລຳ​ດັບ ​ຫຼື​ການ​ອ້າງ​ອີງ​ຕໍ່​ເຊວ​ທີ່​ມີ​ຕົວ​ເລກ​ທີ່​ແທນ​ໃຫ້​ຊຸດ​ຂອງ​ການ​ຊຳ​ລະ (​ເປັນ​ລົບ) ແລະ​ລາຍ​ໄດ້ (ເປັນ​ບວກ) ຢູ່​ໃນ​ໄລ​ຍະ​ປົກ​ກະ​ຕິ!ແມ່ນ​ອັດ​ຕາດອກ​ເບ້ຍ​ທີ່​ທ່ານ​ຈ່າຍ​ຕາມ​ເງິນ​ສົດ​ທີ່​ໄດ້​ໃຊ້​ຢູ່​ໃນ​ການ​ໄຫຼວຽນ​ເງິນ​ສົດ!ແມ່ນ​ອັດ​ຕາ​ດອກ​ເບ້ຍ​ທີ່​ທ່ານ​ໄດ້​ຮັບ​ຢູ່​ໃນ​ການ​ໄຫຼວຽນ​ເງິນ​ເງິນ​ຕາມ​ທີ່​ທ່ານ​ລົງ​ທຶນ​ໃໝ່​ໃສ່​ພວກ​ມັນ"}, "NOMINAL": {"a": "(effect_rate; npery)", "d": "ສົ່ງຄືນອັດຕາດອກເບ້ຍທີ່ລະບຸຕໍ່ປີ", "ad": "​ເປັນອັດຕາດອກເບ້ຍທີ່ແທ້ຈິງ!​ເປັນຈຳນວນງວດທີ່ຄິດທົບຕໍ່ປີ"}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "ສົ່ງຄືນຄ່າຈຳນວນຊ່ວງເວລາທັງໝົດໃນການຈ່າຍເງິນ ສຳລັບການລົງທຶນຈຳນວນດັ່ງ ກ່າວຖືກຄຳນວນ ໂດຍມີພື້ນຖານຢູ່ເທິງການຈ່າຍເງິນ ເປັນງວໂດຍອດການຈ່າຍເງິນທີ່ຄົງທີ່ ແລະ ອັດຕາດອກເບ້ຍທີ່ຄົງທີ່", "ad": "ເປັນອັດຕາດອກເບ້ຍຕໍ່ຊ່ວງເວລາ. ຕົວຢ່າງເຊັ່ນ, ໃຫ້ໃຊ້ 6%/4 ສຳລັບການຈ່າຍເງິນທີ່ແບ່ງຈ່າຍທຸກໆ 3 ເດືອນລວມເປັນ 4 ງວດທີ່ອັດຕາດອກເບ້ຍ 6% ຕໍ່ປີ!ເປັນຈຳນວນເງິນທີ່ຕ້ອງຈ່າຍໃນແຕ່ລະຊ່ວງເວລາ ແລະບໍ່ສາມາດປ່ຽນແປງໄດ້ໄປຕະຫຼອດຊ່ວງເວລາຂອງການ ລົງທຶນ!ເປັນຄ່າມູນຄ່າປະຈຸບັນ, ຫຼືມູນຄ່າທັງໝົດໃນເວລາປະຈຸບັນຂອງຈຳນວນເງິນໃນທຸກງວດທີ່ຈະຕ້ອງຈ່າຍໃນອະນາຄົດ!ເປັນມູນຄ່າໃນອະນາຄົດ,ຫຼືຈຳນວ ເງິນທີ່ທ່ານຕ້ອງການໃຫ້ຄົງເຫຼືອຫຼັງຈາກຈ່າຍເງິນງວດສຸດທ້າຍ. ຖ້າບໍ່​ໄດ້​ໃສ່​ຄ່າ​ຫຍັງ, ຄ່າ 0 ຈະຖືກໃຊ້!ເປັນຄ່າຄວາມ​ຈິງ: ການຈ່າຍເງິນທີ່ຈຸດເລີ່ມຕົ້ນຂອງຊ່ວງເວລາ = 1; ການຈ່າຍເງິນທີ່ຈຸດສຸດທ້າຍຂອງຊ່ວງເວລາ = 0 ຫຼືບໍ່​ໄດ້​ໃສ່​ຄ່າ​ຫຍັງ"}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "ສົ່ງຄືນມູນຄ່າປະຈຸບັນ ຂອງເງິນລົງທຶນເຊິ່ງຄຳນວນຈາກອັດຕາດອກເບ້ຍ (ຄ່າລົບ) ແລະ ລາຍໄດ້ (ຄ່າບວກ)ໃນອະນາຄົດ", "ad": "​ແມ່ນອັດຕາດອກເບ້ຍໃນຊ່ວງໜຶ່ງງວດ!​ແມ່ນການການຊຳລະເງິນ ແລະ ລາຍໄດ້ 1 ເຖິງ 254 ເທື່ອ, ເຊິ່ງຖືກແບ່ງໃນຊ່ວງເວລາທີ່ເທົ່າໆກັນ ແລະ ຈະເກີດຂຶ້ນເມື່ອສິ້ນສຸດແຕ່ລະງວດ"}, "ODDFPRICE": {"a": "(settlement; maturity; issue; first_coupon; rate; yld; redemption; frequency; [basis])", "d": "ສົ່ງຄືນລາຄາຕໍ່ມູນຄ່າຕາມຕາສານ 100 ໂດລາຂອງຫຼັກຊັບທີ່ມີຊ່ວງເວລາທຳອິດສັ້ນ ຫຼືຍາວກວ່າມາດຕະຖານ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອນຂອງຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນ​ວັນ​ທີອອກຈຳໜ່າຍຂອງຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນ​ວັນ​ທີຈ່າຍດອກເບ້ຍຄັ້ງທຳອິດຂອງຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນອັດຕາດອກເບ້ຍຂອງຫຼັກຊັບ! ຄືອັດຕາຜົນຕອບແທນລາຍປີຂອງຫຼັກຊັບ!​ແມ່ນມູນຄ່າຖອນຂອງຫຼັກຊັບຕໍ່ມູນຄ່າຕາມຕາສານ 100 ໂດລາ!​ແມ່ນຈຳນວນຄັ້ງໃນການຈ່າຍດອກເບ້ຍໃນແຕ່ລະປີ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ"}, "ODDFYIELD": {"a": "(settlement; maturity; issue; first_coupon; rate; pr; redemption; frequency; [basis])", "d": "ສົ່ງຄືນອັດຕາຜົນຕອບແທນຂອງຫຼັກຊັບທີ່ມີຊ່ວງເວລາທຳອິດສັ້ນ ຫຼືຍາວກ່ວາມາດຕະຖານ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບເຊິ່ງຢູ່ໃນຮູບແບບ ເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອນຂອງຫຼັກຊັບເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນ​ວັນ​ທີອອກຈຳໜ່າຍຂອງຫຼັກຊັບເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຈ່າຍດອກເບ້ຍຄັ້ງທຳອິດຂອງຫຼັກຊັບເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນອັດຕາດອກເບ້ຍຂອງຫຼັກຊັບ! ຄືລາຄາຂອງຫຼັກຊັບ!​ແມ່ນມູນຄ່າຖອນຂອງຫຼັກຊັບຕໍ່ມູນຄ່າຕາມຕາສານ 100 ໂດລາ!​ແມ່ນຈຳນວນເທື່ອໃນການຈ່າຍດອກເບ້ຍໃນແຕ່ລະປີ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນ ວັນ"}, "ODDLPRICE": {"a": "(settlement; maturity; last_interest; rate; yld; redemption; frequency; [basis])", "d": "ສະ​ແດງ​ຜົນ​ລ​າ​ຄາ​ຕໍ່​ມູນ​ຄ່າດ້ານ​ໜ້າ $100 ຂອງ​ຫຼັກ​ຊັບ​ທີ່​ມີ​ໄລ​ຍະ​ສຸດ​ທ້າຍ​ຄີກ", "ad": "ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​​ວັນ​ທີຄູ​ປ໋ອງ​ສຸດ​ທ້າຍຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​ອັດ​ຕາ​ດອກ​ເບ້ຍ​ຂອງ​​ຫຼັກ​ຊັບ!ແມ່ນ​ຜົນ​ໄດ້​ຮັບ​ປະ​ຈຳ​ປີ​ຂອງ​​ຫຼັກ​ຊັບ!ແມ່ນ​ຄ່າ​ການ​ກູ້​ຄືນ​ຂອງ​ຫຼັກ​ຊັບ​ຕໍ່​ມູນ​ຄ່າດ້ານ​ໜ້າ $100!ແມ່ນ​ຈຳ​ນວນ​ການ​ຊຳ​ລະ​ຄູ​ປ໋ອງ​ຕໍ່​ປີ!ແມ່ນ​ປະ​ເພດ​ຂອງ​ເກນ​ການ​ນັບ​ວັນ​ທີ່​ນຳ​ໃຊ້"}, "ODDLYIELD": {"a": "(settlement; maturity; last_interest; rate; pr; redemption; frequency; [basis])", "d": "ສະ​ແດງ​ຜົນ​ໄດ້​ຮັບ​ຂອງ​ການ​ຄ້ຳ​ປະ​ກັນ​ກັບ​ໄລ​ຍະ​ສຸດ​ທ້າຍ​ຄີກ", "ad": "ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​​ວັນ​ທີຄູ​ປ໋ອງ​ສຸດ​ທ້າຍຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​ອັດ​ຕາ​ດອກ​ເບ້ຍ​ຂອງ​​ຫຼັກ​ຊັບ!ແມ່ນ​ລາ​ຄາ​ຂອງ​​ຫຼັກ​ຊັບ!ແມ່ນ​ຄ່າ​ການ​ກູ້​ຄືນ​ຂອງ​ຫຼັກ​ຊັບ​ຕໍ່​ມູນ​ຄ່າດ້ານ​ໜ້າ $100!ແມ່ນ​ຈຳ​ນວນ​ການ​ຊຳ​ລະ​ຄູ​ປ໋ອງ​ຕໍ່​ປີ!ແມ່ນ​ປະ​ເພດ​ຂອງ​ເກນ​ການ​ນັບ​ວັນ​ທີ່​ນຳ​ໃຊ້"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "ສົ່ງ​ຄືນ​ຈໍານວນ​​ໄລຍະ​ເວລາ​ທີ່​ຕ້ອງ​ໃຊ້​​ໂດຍ​ການ​ລົງທຶນ​ເພື່ອ​ໃຫ້​ໄປ​ຮອດ​ຄ່າ​ທີ່​ລະບຸ​ໄວ້", "ad": "​​ເປັນ​ອັດຕາ​ດອກ​ເບ້ຍ​ຕໍ່​ໄລຍະ​ເວລາ!​ເປັນ​ມູນ​ຄ່າ​ປະຈຸ​ບັນ​ຂອງ​ການ​ລົງທຶນ!​ເປັນ​ມູນ​ຄ່າ​ອະນາຄົດ​ທີ່​ຕ້ອງການ​ຂອງ​ການ​ລົງທຶນ"}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "ຄຳນວນຫາຍອດການຈ່າຍເງິນສຳລັບເງິນກູ້ທີ່​ອີງ​ໃສ່ຍອດການຈ່າຍເງິນທີ່ຄົງທີ່", "ad": " ແລະອັດຕາດອກເບ້ຍທີ່ຄົງທີ່!ເປັນອັດຕາດອກເບ້ຍຕໍ່ຊ່ວງເວລາສຳລັບເງິນກູ້ ,ຕົວຢ່າງເຊັ່ນ, ໃຫ້ໃຊ້ 6%/4 ສຳລັບການຈ່າຍເງິນທີ່ແບ່ງຈ່າຍທຸກໆ 3 ເດືອນ ລວມເປັນ 4 ງວດ ທີ່ອັດຕາດອກເບ້ຍ 6% ຕໍ່ປີ!ເປັນຈຳ ນວນງວດຂອງການຈ່າຍເງິນທັງໝົດສຳລັບເງິນກູ້!ເປັນມູນຄ່າປະຈຸບັນ: ເຊິ່ງກໍ່ຄືມູນຄ່າລວມທັງໝົດໃນເວລາປະຈຸບັນຂອງຈຳນວນເງິນໃນທຸກງວດທີ່ຈະຕ້ອງຈ່າຍໃນອະນາຄົດຫຼືຈຳນວນເງິນທີ່ທ່ານຕ້ອງການໃຫ້ຄົງເຫຼືອຫຼັງຈາກຈ່າຍເງິນງວດສຸດທ້າຍ. ຖ້າບໍ່​ໄດ້​ໃສ່​ຄ່າ​ຫຍັງ, ຄ່າ 0 !ເປັນຄ່າຄວາມ​ຈິງ:ການຈ່າຍເງິນທີ່ຈຸດເລີ່ມຕົ້ນຂອງຊ່ວງເວລາແຕ່ຄ່າ = 1; ການຈ່າຍເງິນທີ່ຈຸດສຸດທ້າຍຂອງຊ່ວງເວລາແຕ່ຄ່າ = 0 ຫຼືບໍ່​ໄດ້​ໃສ່​ຄ່າ​ຫຍັງ"}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "ສົ່ງຄືນຄ່າເງິນຕົ້ນທີ່ຕ້ອງຈ່າຍສຳລັບການລົງທຶນການຄຳນວນໄດ້ມີພື້ນຖານຢູ່ເທິງ ການຈ່າຍເງິນເປັນງວດ, ຍອດການຈ່າຍເງິນທີ່ຄົງທີ່ ແລະ ອັດຕາດອກເບ້ຍທີ່ຄົງທີ່", "ad": "ເປັນອັດຕາດອກເບ້ຍຕໍ່ຊ່ວງເວລາ. ຕົວຢ່າງເຊັ່ນ, ໃຫ້ໃຊ້ 6%/4 ສຳລັບການຈ່າຍເງິນ ທີ່ແບ່ງຈ່າຍທຸກໆ 3 ເດືອນລວມເປັນ 4 ງວດທີ່ອັດຕາດອກເບ້ຍ 6% ຕໍ່ປີ!ເປັນຊ່ວງເວລາເຊິ່ງທ່ານຕ້ອງການຫາຄ່າເງິນຕົ້ນທີ່ຕ້ອງຈ່າຍ ແລະຈະຕ້ອງ ຢູ່ໃນຊ່ວງ 1 ຫາ nper!ເປັນຈຳນວນຊ່ວງເວລາທັງໝົດໃນການຈ່າຍເງິນສຳລັບການລົງທຶນ!ເປັນຄ່າ PV (present value-ມູນຄ່າປະຈຸບັນ), ຫຼືມູນຄ່າລວມທັງໝົດໃນເວລາປະຈຸບັນຂອງຈຳນວນເງິນລວມທຸກງວດທີ່ຈະຕ້ອງຈ່າຍໃນອະນາຄົດ!ເປັນມູນຄ່າໃນອະນາຄົດ FV - future value,ຫຼືຈຳນວນເງິນທີ່ທ່ານຕ້ອງການໃຫ້ຄົງເຫຼືອຫຼັງຈາກຈ່າຍເງິນງວດສຸດທ້າຍ. ຖ້າບໍ່​ໄດ້​ໃສ່​ຄ່າ​ຫຍັງຄ່າໄວ້, ຄ່ານີ້ຈະເປັນ 0!ເປັນຄ່າຄວາມ​ຈິງ: ຖ້າຈ່າຍເງິນທີ່ຈຸດ ເລີ່ມຕົ້ນຂອງຊ່ວງເວລາ = 1; ຖ້າຈ່າຍເງິນທີ່ຈຸດສຸດທ້າຍຂອງຊ່ວງເວລາ = 0 ຫຼືບໍ່​ໃສ່​ຄ່າຫຍັງ"}, "PRICE": {"a": "(settlement; maturity; rate; yld; redemption; frequency; [basis])", "d": "ສະ​ແດງ​ລາ​ຄາ​ຕໍ່​ມູນ​ຄ່າ​ດ້ານ​ໜ້າ $100 ຂອງ​ຫຼັກ​ຊັບ​ທີ່​ຈ່າຍດອກ​ເບ້ຍ​ເປັນ​ໄລ​ຍະ", "ad": "ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​ອັດ​ຕາ​ຄູ​ປ໋ອງ​ປະ​ຈຳ​ປີ​ຂອງ​ຫຼັກ​ຊັບ!ແມ່ນ​ຜົນ​ໄດ້​ຮັບ​ປະ​ຈຳ​ປີ​ຂອງ​​ຫຼັກ​ຊັບ!ແມ່ນ​ຄ່າ​ການ​ກູ້​ຄືນ​ຂອງ​ຫຼັກ​ຊັບ​ຕໍ່​ມູນ​ຄ່າດ້ານ​ໜ້າ $100!ແມ່ນ​ຈຳ​ນວນ​ການ​ຊຳ​ລະ​ຄູ​ປ໋ອງ​ຕໍ່​ປີ!ແມ່ນ​ປະ​ເພດ​ຂອງ​ເກນ​ການ​ນັບ​ວັນ​ທີ່​ນຳ​ໃຊ້"}, "PRICEDISC": {"a": "(settlement; maturity; discount; redemption; [basis])", "d": "ສົ່ງຄືນລາຄ່າຕໍ່ມູນຄ່າຕາມຕາສານ 100 ໂດລາຂອງຫຼັກຊັບທີ່ມີອັດຕາສ່ວນຫຼຸດ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອນຂອງຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນອັດຕາສ່ວນຂອງຫຼັກຊັບ (discount rate)!​ແມ່ນມູນຄ່າຖອດຖອນຂອງຫຼັກຊັບຕໍ່ມູນຄ່າຕາມຕາສານ 100 ໂດລາ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ"}, "PRICEMAT": {"a": "(settlement; maturity; issue; rate; yld; [basis])", "d": "​ສະ​ແດງ​ຜົນ​​ລາ​ຄາ​ຕໍ່​ມູນ​ຄ່າດ້ານ​ໜ້າ $100 ຂອ​ງ​​ຫຼັກ​ຊັບ​ທີ່​ຈ່າຍດອກ​ເບ້ຍ​ໃນ​ເວ​ລາ​ຄົບ​ກຳ​ນົດ", "ad": "ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​​ວັນ​ທີການອອກ​ໃຫ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​ອັດ​ຕາ​ດອກ​ເບ້ຍ​ໃນ​ວັນ​ທີ​ອອກ​ໃຫ້!ແມ່ນ​ຜົນ​ໄດ້​ຮັບ​ປະ​ຈຳ​ປີ​ຂອງ​​ຫຼັກ​ຊັບ!ແມ່ນ​ປະ​ເພດ​ຂອງ​ເກນ​ການ​ນັບ​ວັນ​ທີ່​ນຳ​ໃຊ້"}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "ສົ່ງຄືນຄ່າ PV (present value-ມູນຄ່າປະຈຸບັນ) ຂອງການລົງທຶນ: ເຊິ່ງກໍ່ແມ່ນມູນຄ່າລວມທັງໝົດໃນເວລາປະຈຸບັນຂອງຈຳນວນເງິນໃນທຸກງວດທີ່ຕ້ອງຈ່າຍໃນອະນາຄົດ", "ad": "ເປັນອັດຕາດອກເບ້ຍຕໍ່ຊ່ວງ. ຕົວຢ່າງເຊັ່ນ, ໃຫ້ໃຊ້ 6%/4 ສຳລັບການຈ່າຍເງິນທີ່ແບ່ງຈ່າຍທຸກໆ 3 ເດືອນລວມເປັນ 4 ງວດ ທີ່ອັດຕາດອກເບ້ຍ 6% ຕໍ່ປີ APR!ເປັນຈຳນວນຊ່ວງເວລາທັງໝົດໃນການຈ່າຍເງິນສຳລັບການລົງທຶນ!ເປັນຈຳນວນເງິນທີ່ຕ້ອງຈ່າຍໃນແຕ່ລະຊ່ວງເວລາ ແລະ ບໍ່ສາມາດປ່ຽນແປງໄດ້ໄປຕະຫຼອດຊ່ວງເວລາຂອງການລົງທຶນ!ເປັນມູນຄ່າໃນອະນາຄົດ, ຫຼືຈຳນວນເງິນທີ່ທ່ານຕ້ອງການໃຫ້ຄົງເຫຼືອຫຼັງຈາກຈ່າຍເງິນໃນງວດສຸດທ້າຍ!ເປັນຄ່າທີ່ມີເຫດຜົນ:ການຈ່າຍເງິນທີ່ຈຸດເລີ່ມຕົ້ນຂອງຊ່ວງເວລາແຕ່ຄ່າ = 1; ການຈ່າຍເງິນທີ່ຈຸດສຸດທ້າຍຂອງຊ່ວງເວລາແຕ່ຄ່າ = 0 ຫຼື ລະເວັ້ນ"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "ສົ່ງຄືນອັດຕາດອກເບ້ຍຕໍ່ຊ່ວງເວລາຂອງເງິນກູ້ ຫຼືການລົງທຶນ, ຕົວຢ່າງເຊັ່ນ, ໃຫ້ໃຊ້ 6%/4 ສຳລັບການຈ່າຍເງິນທີ່ແບ່ງຈ່າຍທຸກໆ 3 ເດືອນລວມເປັນ 4 ງວດທີ່ອັດຕາດອກເບ້ຍ 6% ຕໍ່ປີ", "ad": "ເປັນຈຳນວນຂອງຊ່ວງເວລາໃນການຈ່າຍເງິນທັງໝົດສຳລັບເງິນກູ້ ຫຼືການລົງທຶນ!ເປັນຈຳນວນເງິນທີ່ຕ້ອງຈ່າຍເງິນໃນແຕ່ລະຊ່ວງເວລາ ແລະ ບໍ່ສາມາດປ່ຽນແປງໄດ້ໄປຕະຫຼອດຊ່ວງເວລາຂອງເງິນກູ້ ຫຼືການລົງທຶນ!ເປັນຄ່າມູນຄ່າປະຈຸບັນ: ເຊິ່ງກໍ່ຄື ມູນຄ່າລວມທັງໝົດ! ໃນເວລາປະຈຸບັນຂອງຈຳນວນເງິນໃນທຸກງວດທີ່ຈະຕ້ອງຈ່າຍໃນອະນາຄົດ ຫຼືຈຳນວນເງິນທີ່ທ່ານຕ້ອງການໃຫ້ຄົງເຫຼືອຫຼັງຈາກຈ່າຍເງິນງວດສຸດທ້າຍ. ຖ້າບໍ່ໄດ້ໃສ່ຄ່າຫຍັງໄວ້, ໃຫ້ໃຊ້ມູນຄ່າໃນອະນາຄົດ Fv = 0!ເປັນຄ່າຄວາມ​ຈິງ: ການຈ່າຍເງິນທີ່ຈຸດເລີ່ມຕົ້ນຂອງຊ່ວງເວລາແຕ່ຄ່າ = 1; ການຈ່າຍເງິນທີ່ຈຸດສຸດທ້າຍຂອງຊ່ວງເວລາແຕ່ຄ່າ = 0 ຫຼືລະເວັ້ນ!ເປັນອັດຕາດອກເບ້ຍທີ່ທ່ານຄາດວ່າຈະ ເປັນໄປໄດ້; ຖ້າລະເວັ້ນ, Guess = 0.1 (10 ເປີເຊັນ)"}, "RECEIVED": {"a": "(settlement; maturity; investment; discount; [basis])", "d": "ສົ່ງຄືນມູນຄ່າທີ່ຈະໄດ້ຮັບໃນວັນຄົບກຳນົດຖອນຂອງຫຼັກຊັບທີ່ໃຊ້ຊັບ​ສິນທັງໝົດຂອງການລົງທຶນ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸ ກົມ!​ແມ່ນວັນຄົບກຳນົດຖອນຂອງຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນມູນຄ່າທີ່ລົງທຶນໃນຫຼັກຊັບ!​ແມ່ນອັດຕາສ່ວນຫຼຸດຂອງຫຼັກຊັບ!​ແມ່ນຊະນິດຂອງເກນໃນການນັບຈຳນວນວັນ"}, "RRI": {"a": "(nper; pv; fv)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັດຕາ​ດອກ​ເບ້ຍ​ທີ່​ທຽບ​ເທົ່າ​ກັບ​ການ​ເຕີບ​ໂຕ​ຂອງ​ການ​ລົງທຶນ​ອັນ​ໜຶ່ງ", "ad": "​ແມ່ນ​ຈໍານວນ​ຂອງ​​ໄລຍະ​ເວລາ​ສໍາລັບ​ການ​ລົງທຶນ!​ແມ່ນ​ມູນ​ຄ່າ​ປະຈຸ​ບັນ​ຂອງ​ການ​ລົງທຶນ!​ແມ່ນ​ມູນ​ຄ່າ​ອະນາຄົດ​ຂອງ​ການ​ລົງທຶນ"}, "SLN": {"a": "(cost; salvage; life)", "d": "ສົ່ງຄືນຄ່າເສື່ອມລາຄາແບບເສັ້ນຊື່ຂອງຊັບສິນສຳລັບໜຶ່ງຊ່ວງເວລາ", "ad": "ເປັນຄ່າເລີ່ມຕົ້ນຂອງຊັບສິນ!ເປັນລາຄາຊາກຊັບ​ສິນຢູ່ຊ່ວງສຸດທ້າຍຂອງອາຍຸຂອງຊັບສິນ!ເປັນຈຳນວນຊ່ວງເວລາທັງໝົດທີ່ຊັບສິນຖືກປະເມີນຄ່າເສື່ອມລາຄາ (ບາງເທື່ອເອີ້ນວ່າຊ່ວງການໃຊ້ປະໂຫຍດຂອງຊັບສິນ)"}, "SYD": {"a": "(cost; salvage; life; per)", "d": "ສົ່ງຄືນຄ່າເສື່ອມລາຄາແບບ sum-of-years' digits ຂອງຊັບ​ສິນ ສໍາລັບຊ່ວງເວລາທີ່ລະບຸ", "ad": "ເປັນລາຄາເລີ່ມຕົ້ນຂອງຊັບ​ສິນ!ເປັນລາຄ່າຊາກຊັບ​ສິນຢູ່ຊ່ວງສຸດທ້າຍຂອງອາຍຸຂອງຊັບ​ສິນ!ເປັນຈຳນວນຊ່ວ ເວລາທັງໝົດທີ່ຊັບ​ສິນຖືກປະເມີນຄ່າ​ເສື່ອ​ມລາຄາ (ບາງເທື່ອເອີ້ນວ່າຊ່ວງການໃຊ້ປະໂຫຍດຂອງຊັບ​ສິນ)!ເປັນຊ່ວງເວລາ ແລະ ຕ້ອງຖືກລະບຸຢູ່ໃນໜ່ວຍດຽວກັບອາຍຸຂອງຊັບ​ສິນ"}, "TBILLEQ": {"a": "(settlement; maturity; discount)", "d": "ສົ່ງຄືນຄ່າອັດຕາຜົນຕອບແທນທຽບເທົ່າພັນນະບັດໃບເງິນຄັງ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຂອງໃບເງິນຄັງ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸ ກົມ!​ແມ່ນວັນກຳນົດຖອນຂອງໃບເງິນຄັງ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນອັດຕາສ່ວນຫຼຸດຂອງໃບເງິນຄັງ"}, "TBILLPRICE": {"a": "(settlement; maturity; discount)", "d": "ສົ່ງຄືນລາຄາຕໍ່ມູນຄ່າຕາມຕາສານ 100 ໂດລາຂອງໃບ​ເງິນຄັງ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍ​ໃບເງິນຄັງ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອນ​ໃບເງິນຄັງ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນອັດຕາສ່ວນຫຼຸດຂອງ​ໃບເງິນຄັງ"}, "TBILLYIELD": {"a": "(settlement; maturity; pr)", "d": "​ສະ​ແດງ​ຜົນ​ໄດ້​ຮັບ​ສຳ​ລັບ​ໃບ​ບິນ​ຄັງ​ເງິນ", "ad": "ແມ່ນ​ວັນ​ທີ​ການ​ຊຳ​ລະ​ໜ້າ​ຂອງ​ໃບ​ບິນ​ຄັງ​ເງິນ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​ວັນ​ທີ​ການ​ຄົບ​ກຳ​ນົດ​ຂອງ​ໃບ​ບິນ​ຄັງ​ເງິນ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​ລາ​ຄາ​ຂອງ​ໃບ​ບິນ​ຄັງ​ເງິນ​ຕໍ່​ມູນ​ຄ່າ​ດ້ານ​ໜ້າ $100"}, "VDB": {"a": "(cost; salvage; life; start_period; end_period; [factor]; [no_switch])", "d": "ສົ່ງຄືນຄ່າ​ເສື່ອ​ມລາຄາຂອງຊັບສິນສຳລັບຊ່ວງເວລາໃດໆກໍ່ຕາມທີ່ທ່ານລະບຸ,ຫຼືບາງສ່ວນຂອງຊ່ວງເວລາໃດໆກໍ່ຕາມທີ່ທ່ານລະບຸ, ການຄຳນວນຈະໃຊ້ວິທີການຍອດ​ເຫຼືອຫຼຸດລົງ​ສອງ​ເທົ່າ ຫຼືວິທີອື່ນໆທີ່ທ່ານລະບຸ", "ad": "ເປັນລາຄາເລີ່ມຕົ້ນຂອງຊັບສິນ!ເປັນລາຄາຄ່າຊົດເຊີຍຢູ່​ຊ່ວງສຸດທ້າຍຂອງອາຍຸຂອງຊັບສິນ!ເປັນຈຳນວນຊ່ວງເວລາທັງໝົດທີ່ຊັບສິນຖືກປະເມີນຄ່າເສື່ອມລາຄາ (ບາງເທື່ອເອີ້ນວ່າຊ່ວງການໃຊ້ປະໂຫຍດຂອງຊັບສິນ)!ເປັນຊ່ວງເວລາເລີ່ມຕົ້ນທີ່ທ່ານຕ້ອງການຄຳນວນຫາຄ່າ​ເສື່ອ​ມລາຄາ,ແລະ ຕ້ອງຖືກລະບຸຢູ່ໃນໜ່ວຍດຽວກັບອາຍຸຂອງຊັບສິນ!ເປັນຊ່ວງເວລາສຸດທ້າຍທີ່ທ່ານຕ້ອງການຄຳນວນຫາຄ່າ​ເສື່ອ​ມລາຄາ ແລະ ຕ້ອງຖືກລະບຸຢູ່ໃນໜ່ວຍດຽວກັບອາຍຸຂອງຊັບສິນ!ເປັນອັດຕາການຫຼຸດລົງຂອງຍອດຄົງເຫຼືອ, ຄ່າຈະເທົ່າກັບ 2 (ວິທີການຍອດ​ເຫຼືອຫຼຸດລົງ​ສອງ​ເທົ່າ) ຖ້າລະເວັ້ນຄ່າໄວ້!ຖ້າຄ່າທີ່ໃສ່ = FALSE ຫຼືບໍ່ໃສ່ຄ່າຫຍັງ ການຄຳນວນຈະສະລັບກັບໄປໃຊ້ການຫາຄ່າ​ເສື່ອ​ມລາຄາແບບເສັ້ນຊື່ຖ້າຄ່າ​ເສື່ອ​ມລາຄາຈາກວິທີນີ້ຫຼາຍກວ່າທີ່ໄດ້ຈາກວິທີການຍອດ​ຄົງ​ເຫຼືອຫຼຸດລົງ​ສອງ​​ເທົ່າ; ຖ້າຄ່າທີ່ໃສ່ = TRUE ການຄຳນວນຈະບໍ່ສະລັບ"}, "XIRR": {"a": "(values; dates; [guess])", "d": "ສະ​ແດງ​ຜົນ​ອັດ​ຕາພາຍ​ໃນ​ຂອງ​ຜົນ​ຕອບ​ແທນ​ສຳ​ລັບກຳ​ນົດ​ເວ​ລາ​ການ​ໄຫຼວ​ວຽນ​ເງິນ​ສົດ", "ad": "ຊຸດ​ຂອງ​ການ​ໄຫຼວຽ​ນ​ເງິນ​ສົດ​ທີ່​ສອດ​ຄ້ອງ​ກັບ​ກຳ​ນົດ​ເວ​ລາ​ການ​ຊຳ​ລະ​ໃນ​ລາຍ​ລະ​ອຽດ!ແມ່ນ​ກຳ​ນົດ​ເວ​ລາ​ວັນ​ທີ​ການ​ຊຳ​ລະ​ທີ່​ສອດ​ຄ້ອງ​ກັບ​ການ​ຊຳ​ລະ​ການ​ໄຫຼວ​ຽນ​ເງິນ​ສົດ!ແມ່ນ​ຈຳ​ນວນ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ເດົາ​ໃກ້​ກັບ​ຜົນ​ໄດ້​ຮັບ​ຂອງ XIRR"}, "XNPV": {"a": "(rate; values; dates)", "d": "ສົ່ງຄືນມູນຄ່າປະຈຸບັນສຸດທິ (Net Present Value) ສຳລັບກະແສເງິນສົດໃນຊ່ວງເວລາໜຶ່ງ", "ad": " ຄືອັດຕາສ່ວນຫຼຸດທີ່ຈະນຳໄປໃຊ້ກັບກະແສເງິນສົດ!​ແມ່ນຊຸດ ຂໍ້ມູນຂອງກະແສເງິນສົດທີ່ມີວັນທີສຳພັນກັບການຊຳລະເງິນໃນຊ່ວງເວລາໜຶ່ງ!​ແມ່ນວັນທີການຊຳລະເງິນໃນຊ່ວງເວລາໜຶ່ງທີ່ສຳພັນກັບການຊຳລະເງິນສົດ"}, "YIELD": {"a": "(settlement; maturity; rate; pr; redemption; frequency; [basis])", "d": "ສະ​ແດງ​ຜົນ​ໄດ້​ຮັບ​ຢູ່​ໃນ​ຫຼັກ​ຊັບ​ທີ່​ຈ່າຍ​ດອກ​ເບ້ຍ​ເປັນ​ໄລ​ຍະ", "ad": "ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​ອັດ​ຕາ​ຄູ​ປ໋ອງ​ປະ​ຈຳ​ປີ​ຂອງ​ຫຼັກ​ຊັບ!ແມ່ນ​ລາ​ຄາ​ຂອງ​​ຫຼັກ​ຊັບ​ຕໍ່​ມູນ​ຄ່າ​ດ້ານ​ໜ້າ $100!ແມ່ນ​ຄ່າ​ການ​ກູ້​ຄືນ​ຂອງ​ຫຼັກ​ຊັບ​ຕໍ່​ມູນ​ຄ່າດ້ານ​ໜ້າ $100!ແມ່ນ​ຈຳ​ນວນ​ການ​ຊຳ​ລະ​ຄູ​ປ໋ອງ​ຕໍ່​ປີ!ແມ່ນ​ປະ​ເພດ​ຂອງ​ເກນ​ການ​ນັບ​ວັນ​ທີ່​ນຳ​ໃຊ້"}, "YIELDDISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "​ສະ​ແດງ​ຜົນ​ໄດ້​ຮັບ​ປະ​ຈຳ​ປີ​ສຳ​ລັບ​​ຫຼັກ​ຊັບ​ຫຼຸດ​ລາ​ຄາ. ຕົວ​ຢ່າງ, ​ໃບ​ບິນ​ຄັງ​ເງິນ", "ad": "ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​​ວັນ​ທີການ​ຊຳ​ລະ​ໜີ້ຂອງ​​ຫຼັກ​ຊັບ, ສະ​ແດງ​ອອກ​ເປັນ​ຕົວ​ເລກວັນ​ທີ​ລຳ​ດັບ!ແມ່ນ​ລາ​ຄາ​ຂອງ​​ຫຼັກ​ຊັບ​ຕໍ່​ມູນ​ຄ່າ​ດ້ານ​ໜ້າ $100!ແມ່ນ​ຄ່າ​ການ​ກູ້​ຄືນ​ຂອງ​ຫຼັກ​ຊັບ​ຕໍ່​ມູນ​ຄ່າດ້ານ​ໜ້າ $100!ແມ່ນ​ປະ​ເພດ​ຂອງ​ເກນ​ການ​ນັບ​ວັນ​ທີ່​ນຳ​ໃຊ້"}, "YIELDMAT": {"a": "(settlement; maturity; issue; rate; pr; [basis])", "d": "ສົ່ງຄືນຜົນຕອບແທນລາຍປີ ຂອງຫຼັກຊັບທີ່ຊຳລະດອກເບ້ຍໃນວັນຄົບກຳນົດຖອນ", "ad": "​ແມ່ນ​ວັນ​ທີຊຳລະຄ່າຊື້ຂາຍຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນຄົບກຳນົດຖອນຂອງຫຼັກຊັບ, ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນວັນທີອອກຈຳໜ່າຍຂອງຫຼັກຊັບ,ເຊິ່ງຢູ່ໃນຮູບແບບເລກວັນທີອະນຸກົມ!​ແມ່ນອັດຕາດອກເບ້ຍຂອງຫຼັກຊັບໃນວັນທີອອກຈຳໜ່າຍ!​ແມ່ນລາຄາຂອງຫຼັກຊັບຕໍ່ມູນຄ່າຕາມມາດຕາສານ 100 ໂດລາ! ຄືຊະນິດຂອງຫຼັກເກນໃນການນັບຈຳນວນວັນ"}, "ABS": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າທີ່ສົມບູນຂອງເລກ, ເລກທີ່ບໍ່ມີເຄື່ອງໝາຍ", "ad": "ແມ່ນຄ່າຈຳນວນຈິງສຳລັບທ່ານທີ່ຕ້ອງການຄ່າທີ່ສົມບູນ"}, "ACOS": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າອາກໂກຊິນຂອງຕົວເລກທີ່ລະບຸ, ຄ່າທີ່ສົ່ງຄືນຈະຢູ່ໃນແບບຣາດຽນທີ່ຢູ່ໃນຊ່ວງ 0 ຫາ Pi. ໝາຍເຫດ ຖ້າອາກໂກຊິນເປັນຄ່າມູມທີ່ໂກຊິນໃຊ້ຫາຄ່າ", "ad": "ເປັນໂກຊິນຂອງມູມທີ່ທ່ານຕ້ອງການ ແລະ ຕ້ອງມີຄ່າຢູ່ລະຫວ່າງ -1 ຫາ 1"}, "ACOSH": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໄຮເປີໂບລິກໂກຊິນ (hyperbolic cosine) ກັບກັນຂອງຈຳນວນທີ່ລະບຸ", "ad": "ເປັນຈຳນວນຈິງໃດໆທີ່ມີຄ່າເທົ່າກັບ ຫຼືຫຼາຍກວ່າ 1"}, "ACOT": {"a": "(number)", "d": "ສົ່ງ​ກັບ​ອາກ​ໂຄ​ແທນ​ເຈນ (arccotangent) ຂອງ​ຕົວ​ເລກ​ໃນ​ໜ່ວຍຣາດຽນ​ໃນ​ຊ່ວງ 0 ຫາ Pi", "ad": "​ແມ່ນ​ໂຄ​ແທນ​ເຈນ​ຂອງ​ມູມທີ່​ທ່ານ​ຕ້ອງການ"}, "ACOTH": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກ​ໂຄ​ແທນ​ເຈນ (hyperbolic cotangent) ​ແບບປິ້ນຄືນ​ຂອງ​ຕົວ​​ເລກ", "ad": "​ແມ່ນ​ໄຮ​ເປີ​ບໍລິກ​ໂຄ​ແທນ​ເຈນ​ຂອງ​ມູມທີ່​ທ່ານ​ຕ້ອງການ"}, "AGGREGATE": {"a": "(function_num; options; ref1; ...)", "d": "ສົ່ງ​ຄືນ​ການ​ລວມ​ໃນ​ລາຍການ ຫຼືຖານ​ຂໍ້​ມູນ", "ad": "​ແມ່ນ​ຕົວ​ເລກ 1 ຫາ 19 ທີ່​ລະບຸ​ຟັງ​ຊັນ​ສະຫຼູບສໍາລັບ​ການ​ລວມ!​ແມ່ນ​ຕົວ​ເລກ 0 ຫາ 7 ທີ່​ລະບຸ​ຄໍາ​ທີ່​ຈະລະ​ເວັ້ນ​ໃນ​ການ​ລວມ!​ແມ່ນ​ອາ​ເຣຍ໌ ຫຼືຊ່ວງ​ຂໍ້​ມູນ​ເລກ​ຕົວ​ທີ່​ໃຊ້​ຄໍານວນ​ການ​ລວມ!ຊີ້​ບອກຕໍາ​ແໜ່​ງ​ໃນ​ອາ​ເຣຍ໌; ມັນ​ເປັນ​ຄ່າ​ໃຫຍ່​ສຸດ​ອັນ​ດັບ​ທີ k, ຄ່າ​ນ້ອຍ​ສຸດ​ອັນ​ດັບ​ທີ k, ຄ່າ​ເປີ​ເຊັນ​ອັນ​ດັບ​ທີ k ຫຼືຄ່າ​ໜຶ່ງ​ສ່ວນສີ່​ອັນ​ດັບ​ທີ k!​ເປັນ​ຕົວ​ເລກ 1 ຫາ 19 ທີ່​ລະບຸ​ຟັງ​ຊັນ​ສະຫຼູບສຳລັບ​ການ​ລວມ!​ແມ່ນ​ຕົວ​ເລກ 0 ​ຫາ 7 ທີ່​ລະບຸ​ຄ່າ​ທີ່​ຈະລ​ະ​ເວັ້ນ​ໃນ​ການ​ລວມ!​ເປັນ​ຊ່ວງ ຫຼືການ​ອ້າງ​ອີງ 1 ຫາ 253 ລາຍການ​ທີ່​ທ່ານ​ຕ້ອງການ​ການລວມ"}, "ARABIC": {"a": "(text)", "d": "ປ່ຽນ​ຕົວ​ເລກ​ໂຣມັນ​ເປັນ​ອາຣາບິ​ກ", "ad": "​ແມ່ນ​ຕົວ​ເລກ​ໂຣມັນ​ທີ່​ທ່ານ​ຕ້ອງການ​ປ່ຽນ"}, "ASC": {"a": "(text)", "d": "ສໍາລັບພາສາ ຕົວອັກສອນເປັນຊຸດ Double-byte (DBCS), ໜ້າທີ ປ່ຽນ ຈະປ່ຽນ ຕົວອັກສອນຈາກ ເຕັມໜ້າ (double-byte) ເປັນ ຕົວອັກສອນ ເຄີ່ງໜ້າ (single-byte)"}, "ASIN": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າອາກຊິນຂອງຕົວເລກທີ່ລະບຸ,ຄ່າທີ່ສົ່ງຄືນຈະຢູ່ໃນຮູບແບບຣາດຽນ ທີ່ຢູ່ໃນຊ່ວງ -Pi/2 ຫາ Pi/2", "ad": "ເປັນຄ່າ ຊິນ ຂອງມູມທີ່ທ່ານຕ້ອງການ ແລະ ຕ້ອງມີຄ່າຢູ່ໃນຊ່ວງ -1 ຫາ 1"}, "ASINH": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໄຮເປີໂບລິກຊິນ (hyperbolic sine) ກັບກັນ ຂອງຈຳນວນທີ່ລະບຸ", "ad": "ເປັນຈຳນວນຈິງໃດໆທີ່ມີຄ່າເທົ່າກັບ ຫຼືຫຼາຍກວ່າ 1"}, "ATAN": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າ​ອາກ​ແທນ​ເຈັນຂອງ​ຕົວ​ເລກ ​ໃນ​​ຮູບແບບຣາດຽນ ທີ່ຢູ່ໃນຊ່ວງຫ່າງ -Pi/2 ຫາ Pi/2", "ad": "ແມ່ນຄ່າ​ແທນ​ເຈັນ​ຂອງ​ມູນ​ທີ່ທ່ານຕ້ອງການ"}, "ATAN2": {"a": "(x_num; y_num)", "d": "ສະ​ແດງ​ຜົນອາກ​ຕັງ​ຂອງຕົວ​ພິ​ກັດ x- ແລະ y-, ເປັນ​ຣາ​ດຽງ​​ລະ​ຫວ່າງ -Pi ຫາ Pi, ບໍ່​ລວມ -Pi", "ad": "ແມ່ນ​ຕົວ​ພິ​ກັດ x- ຂອງ​ຈຸດ!ແມ່ນ​ຕົວ​ພິ​ກັດ y-ຂອງ​​ຈຸດ"}, "ATANH": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໄຮເປີໂບລິກ​ແທນ​ເຈັນ (hyperbolic tangent) ກັບກັນຂອງຈຳນວນທີ່ລະບຸ", "ad": "ເປັນຈຳນວນຈິງໃດໆທີ່ຢູ່ລະຫວ່າງ -1 ແລະ 1ບໍ່ລວມ -1 ແລະ 1"}, "BASE": {"a": "(number; radix; [min_length])", "d": "ປ່ຽນ​ຕົວ​ເລກ​ໃຫ້​ເປັນ​ຂໍ້ຄວາມ​ທີ່​ໃຊ້​ແທນ​ຕົວ​ເລກ​ນັ້ນ​ໃນ​ຖານ Radix ທີ່​ກຳນົດ​ໃຫ້", "ad": "​ແມ່ນ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ປ່ຽໜ!​ແມ່ນ​ຖານ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ປ່ຽນ​ຕົວ​ເລກ!​ແມ່ນ​ຄວາມ​ຍາວ​ຕໍ່າ​ສຸດ​ຂອງ​ສະຕຣິງທີ່​ສົ່ງ​ຄືນ.  ຖ້າ​ບໍ່​ໄດ້​ໃສ່​ຄ່າ​ຫຍັງ ກໍ່​ຈະ​ບໍ່​ມີ​ການ​ເພີ່ມ​ເລກ​ສູນ​ນໍາ​ໜ້າ"}, "CEILING": {"a": "(number; significance)", "d": "ປັດ​ເສດ​ຕົວ​ເລກ​ຂຶ້ນ​ເປັນ​ຈໍານວນ​ເທົ່າ​ທີ່​ໃກ້​ທີ່​ສຸດຂອງ​ຄວາ​ມສຳຄັນ", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ປັດ​ເສດ!​ແມ່ນ​ຈໍານວນ​ເທົ່າ​ທີ່​ທ່ານຕ້ອງການ​ປັດ​ເສດ"}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "ປັດ​ເສດ​ຕົວ​ເລກ​ຂຶ້ນໃຫ້​ເປັນ​ຈໍານວນ​ເຕັມທີ່​ໃກ້​ທີ່​ສຸດ ຫຼື​ເປັນ​ຈໍານວນ​ເທົ່າ​ທີ່​ໃກ້​ທີ່​ສຸດ​ຂອງ​ຄວາມສຳຄັນ", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ປັດ​ເສດ!​ເປັນ​ຈໍານວນ​ເທົ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ປັດ​ເສດ!​ເມື່ອ​ລະບຸ ​ແລະ​ບໍ່​ແມ່ນ​ເລກ​ສູນ ຟັງ​ຊັນ​ນີ້​ກໍ່​ຈະ​ປັດ​ເສດ​ອອກ​ຈາກ​ສູນ"}, "CEILING.PRECISE": {"a": "(number; [significance])", "d": "ຄືນ ຕົວເລກທີປັດເສດຂື້ນເປັນຈໍານວນເຕັມທີໄກ້ຄຽງທີສຸດ ຫຼື ໄກ້ຄຽງກັບຕົວເລກທີໃຫ່ຍທີສຸດ, ແລະບໍ່ຂື້ນກັບ ເຄື່ອງໝາຍຂອງຕົວເລກ.", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ປັດ​ເສດ!​ແມ່ນ​ຈໍານວນ​ເທົ່າ​ທີ່​ທ່ານຕ້ອງການ​ປັດ​ເສດ"}, "COMBIN": {"a": "(number; number_chosen)", "d": "ສົ່ງຄືນຈຳນວນວິທີຈັດລວມທີ່ເປັນໄປໄດ້ສຳລັບ ຈໍານວນລາຍການທີລະບຸ", "ad": "ເປັນຈຳນວນລາຍການທັງໝົດ!ເປັນຈຳນວນລາຍການ ໃນແຕ່ລະວິທີຈັດລວມ"}, "COMBINA": {"a": "(number; number_chosen)", "d": "ສົ່ງ​ຄືນ​ຈໍານວນ​ການ​ປະສົມ​ເຂົ້າກັນ​ທີ່​ມີ​ການ​ຊໍ້າ​ກັນ​ສໍາລັບ​ຈໍານວນ​ລາຍການ​ທີ່​ລະບຸ", "ad": "​ແມ່ນ​ຈໍານວນ​ລາຍການ​ທັງ​ໝົດ!​ແມ່ນ​ຈໍານວນ​ລາຍການ​ໃນ​ແຕ່​ລະການ​ປະສົມ​ເຂົ້າກັນ"}, "COS": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໂກຊິນຂອງມູມ", "ad": "ແມ່ນມູມໃນອົງສາຣາດຽນທີ່ທ່ານຕ້ອງການຄ່າໂກຊິນ"}, "COSH": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໄຮເປີໂບລິກໂກຊິນ (hyperbolic cosine) ຂອງຈຳນວນທີ່ລະບຸ", "ad": "ເປັນຄ່າຈຳນວນຈິງໃດໆ"}, "COT": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ໂຄ​ແທນ​ເຈນ (cotangent) ຂອງ​ມູມ​ໃດ​ໜຶ່ງ", "ad": "​ແມ່ນ​ມູມ​ໃນ​ອົງສາຣາດຽນທີ່​ທ່ານ​ຕ້ອງການ​ຄ່າ​ໂຄ​ແທນ​ເຈນ"}, "COTH": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກ​ໂຄ​​ແທນ​ເຈນ (hyperbolic cotangent) ຂອງ​ມູມ​ໃດ​ໜຶ່ງ", "ad": "​ແມ່ນ​ມູມ​ໃນ​ອົງສາຣາດຽນທີ່​ທ່ານ​ຕ້ອງການ​ຄ່າ​ໄຮ​ເປີ​ບໍລິກ​ໂຄ​​ແທນ​ເຈນ"}, "CSC": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ໂຄ​ຊີ​ແຄນ​ທ (cosecant) ຂອງ​ມູມ​ໃດ​ໜຶ່ງ", "ad": "​ແມ່ນ​ມູມ​ໃນ​ອົງສາຣາດຽນທີ່​ທ່ານ​ຕ້ອງການ​ຄ່າ​ໂຄ​ຊີ​ແຄນ​ທ"}, "CSCH": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກ​ໂຄ​ຊີ​ແຄນ​ທ (hyperbolic cosecant) ຂອງ​ມູມ​ໃດ​ໜຶ່ງ", "ad": "​ແມ່ນ​ມູມ​ໃນ​ອົງສາຣາດຽນທີ່​ທ່ານ​ຕ້ອງການ​ຄ່າ​ໄຮ​ເປີ​ບໍລິກ​ໂຄ​ຊີ​ແຄນ​ທ"}, "DECIMAL": {"a": "(number; radix)", "d": "ປ່ຽນ​ຂໍ້ຄວາມ​ທີ່​ສະ​ແດງ​ແທນ​ຕົວ​ເລກ​​ໃນ​ຖານ​ທີ່​ກໍານົດ​ໃຫ້​ເປັນ​ເລກ​ຖານ​ສິບ", "ad": "​ແມ່ນ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ປ່ຽນ!​ແມ່ນ​ຖານ​ເລກ Radix ຂອງ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ກຳລັງ​ປ່ຽນ"}, "DEGREES": {"a": "(angle)", "d": "ປ່ຽນມູມຣາດຽນ (radians)​ໃຫ້​ເປັນມູມອົງສາ", "ad": "ເປັນມູມໃນຮູບຣາດຽນ (radians) ທີ່ທ່ານຕ້ອງການປ່ຽນໃຫ້ເປັນມູມອົງສາ"}, "ECMA.CEILING": {"a": "(number; significance)", "d": "ປັດຕົວເລກຂື້ນ ຫາຈຸດທີໄກ້ຄຽງ ທີໄດ້ກໍານົດໄວ້", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ປັດ​ເສດ!​ແມ່ນ​ຈໍານວນ​ເທົ່າ​ທີ່​ທ່ານຕ້ອງການ​ປັດ​ເສດ"}, "EVEN": {"a": "(number)", "d": "ປັດຄ່າຂຶ້ນສຳລັບຈຳນວນບວກ ແລະ ປັດຄ່າລົງສຳລັບຈຳນວນລົບ ໂດຍຈະປັດຄ່າໃຫ້ເທົ່າກັບຈຳນວນເຕັມທີ່ເປັນເລກຄູ່ທີ່ໃກ້ຄຽງທີ່ສຸດ", "ad": "ເປັນຄ່າທີ່ຕ້ອງການໃຫ້ປັດ"}, "EXP": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າ e ຂອງຈຳນວນທີ່ຍົກກຳລັງ", "ad": "ແມ່ນເລກກຳລັງທີ່ນຳໃຊ້ກັບຖານ e. ຄ່າຄົງທີ່ e ເທົ່າກັບ 2.71828182845904, ທີ່ເປັນຖານຂອງໂລກາລິກທຳມະຊາດ"}, "FACT": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າແຟັກເຕີຣຽລຂອງຕົວ​ເລກທີ່ລະບຸ, (ເທົ່າກັບ 1*2*3*...* ຕົວເລກ)", "ad": "ເປັນຄ່າຕົວເລກທີ່ບໍ່ເປັນລົບທີ່ທ່ານຕ້ອງການຫາຄ່າແຟັກເຕີຣຽລ"}, "FACTDOUBLE": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າສອງແຟັກເຕີ​​ຣຽລຂອງຕົວ​ເລກທີ່ລະບຸ", "ad": "​ແມ່ນຄ່າທີ່ທ່ານຕ້ອງການຫາຄ່າສອງແຟັກເຕີຣຽລ"}, "FLOOR": {"a": "(number; significance)", "d": "ປັດ​ເສດ​ຕົວ​ເລກ​ລົງ​ໃຫ້​ເປັນ​ຈໍານວນ​ທີ່​ໃຫ້​ທີ່​ສຸດ​ຂອງ​ຄວາມສຳຄັນ", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ທ່າ​ນຕ້ອງການ​ປັດ​ເສດ!​ແມ່ນ​ຈໍານວນ​ເທົ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ໃຫ້​ປັດ​ເສດ​ໂດຍ Number ​ແລະ Significance ຕ້ອງ​ເປັນ​ຈໍານວນ​ບວກທັງ​ສອງ ຫຼື​ເປັນ​ຈໍານວນ​ລົບ​ທັງ​ສອງ"}, "FLOOR.PRECISE": {"a": "(number; [significance])", "d": "ຄືນ ຕົວເລກທີປັດເສດລົງເປັນຈໍານວນເຕັມທີໄກ້ຄຽງທີສຸດ ຫຼື ໄກ້ຄຽງກັບຕົວເລກທີນ້ອຍທີສຸດ, ແລະບໍ່ຂື້ນກັບ ເຄື່ອງໝາຍຂອງຕົວເລກ.", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ປັດ​ເສດ!​ແມ່ນ​ຈໍານວນ​ເທົ່າ​ທີ່​ທ່ານຕ້ອງການ​ປັດ​ເສດ"}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "ປັດ​ເສດ​ຕົວ​ເລກ​ລົງ​​ໃຫ້​ເປັນ​ຈໍານວນ​ເຕັມທີ່​ໃກ້​ທີ່​ສຸດ ຫຼື​ເປັນ​ຈໍານວນ​ເທົ່າ​ທີ່​ໃກ້​ທີ່​ສຸດ​ຂອງ​ຄວາມສຳຄັນ", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ປັດ​ເສດ!​ເປັນ​ຈໍານວນ​ເທົ່າ​ທີ່​ທ່ານ​ຕ້ອງການ​ປັດ​ເສດ!​ເມື່ອ​ລະບຸ ​ແລະ​ບໍ່​ແມ່ນ​ເລກ​ສູນ ຟັງ​ຊັນ​ນີ້​ກໍ່​ຈະ​ປັດ​ເສດ​ເຂົ້າ​ຫາ​ສູນ"}, "GCD": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຕົວຫານຮ່ວມໃຫຍ່ສຸດ", "ad": "​ແມ່ນຄ່າ 1 ຫາ 255 ຄ່າ"}, "INT": {"a": "(number)", "d": "ປ່ຽນຈຳນວນເສດໃຫ້ເປັນຈຳນວນຖ້ວນທີ່ໃກ້ຄຽງທີ່ສຸດ", "ad": "ແມ່ນຈຳນວນຈິງທີ່ທ່ານຕ້ອງການປ່ຽນຈຳນວນເສດໃຫ້ເປັນຈຳນວນຖ້ວນ"}, "ISO.CEILING": {"a": "(number; [significance])", "d": "ຄືນ ຕົວເລກທີປັດເສດຂື້ນເປັນຈໍານວນເຕັມທີໄກ້ຄຽງທີສຸດ ຫຼື ໄກ້ຄຽງກັບຕົວເລກທີໃຫ່ຍທີສຸດ, ແລະບໍ່ຂື້ນກັບ ເຄື່ອງໝາຍຂອງຕົວເລກ. ແຕ່ວ່າ,ຖ້າຫາກ ຕົວຄູນ ແມ່ນເລກ ສູນ. ຈະຕ້ອງຄືນ ສູນ.", "ad": "​ແມ່ນ​ຄ່າ​ຕົວ​ເລກ​ທີ່​ທ່ານ​ຕ້ອງການ​ປັດ​ເສດ!​ແມ່ນ​ຈໍານວນ​ເທົ່າ​ທີ່​ທ່ານຕ້ອງການ​ປັດ​ເສດ"}, "LCM": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຕົວຄູນຮ່ວມນ້ອຍສຸດ", "ad": "​ແມ່ນຄ່າ 1 ຫາ 255 ຄ່າທີ່ທ່ານຕ້ອງການຫາຕົວຄູນຮ່ວມນ້ອຍສຸດ"}, "LN": {"a": "(number)", "d": "ສົ່ງຄືນຈຳນວນຂອງໂລກາລິກທຳມະຊາດ", "ad": "ແມ່ນຈຳນວນຈິງບວກສຳລັບຄ່າທີ່ທ່ານຕ້ອງການໂລກາລິກທຳມະຊາດ"}, "LOG": {"a": "(number; [base])", "d": "ສົ່ງຄືນຄ່າໂລກາລິກຂອງຕົວເລກ ໂດຍໃຊ້ຖານໂລກາລິກທີ່ທ່ານລະບຸ", "ad": "ເປັນຈຳນວນຈິງບວກທີ່ທ່ານຕ້ອງການຄ່າໂລກາລິກ!ເປັນຖານຂອງໂລກາລິກ; ຖືກຕັ້ງໃຫ້ເປັນ 10 ຖ້າບໍ່ໃສ່ຄ່າຫຍັງໄວ້"}, "LOG10": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໂລກາລິກຖານ-10 ຂອງ​ຈໍານວນ​ທີ່​ລະບຸ", "ad": "ແມ່ນຈຳນວນຈິງບວກທີ່ທ່ານຕ້ອງການຄ່າໂລກາລິກຖານ-10"}, "MDETERM": {"a": "(array)", "d": "ສົ່ງຄືນຄ່າຕົວ​ກຳນົດແມດທຣິກ (matrix determinant) ຂອງອາ​ເຣຍ໌", "ad": "ເປັນອາ​ເຣຍ໌ຕົວ​ເລກທີ່ມີຈຳນວນແຖວ ແລະ ຈຳນວນຖັນເທົ່າກັນ, ເປັນຊ່ວງຂອງຫ້ອງ ຫຼື ຄ່າຄົງທີ່ອາ​ເຣຍ໌"}, "MINVERSE": {"a": "(array)", "d": "ສົ່ງຄືນຄ່າແມດທຣິກປິ້ນກັນ (inverse matrix) ສຳລັ ແມດທຣິກທີ່ເກັບໃນຮູບ​ແບບອາ​ເຣຍ໌", "ad": "ເປັນອາ​ເຣຍ໌ຕົວ​ເລກທີ່ມີຈຳນວນແຖວ ແລະຈຳນວນຖັນເທົ່າກັນ, ເປັນຊ່ວງຂອງຫ້ອງ ຫຼືຄ່າຄົງທີ່ອາ​ເຣຍ໌"}, "MMULT": {"a": "(array1; array2)", "d": "ສົ່ງຄືນຜົນຄູນຂອງ ແມດທຣິກ 2 ຄ່າ ຮອບວຽນ, ອາ​ເຣຍ໌ທີ່ມີຈຳນວນແຖວເທົ່າກັບອາ​ເຣຍ໌ 1 ແລະ ຈຳນວນຖັນເທົ່າກັບອາ​ເຣຍ໌ 2", "ad": "ເປັນອາ​ເຣຍ໌ທຳອິດທີ່ຈະໃຊ້ຄູນ ແລະ ຕ້ອງມີຈຳນວນຖັນເທົ່າກັບຈຳນວນແຖວຂອງອາ​ເຣຍ໌2"}, "MOD": {"a": "(number; divisor)", "d": "ສົ່ງຄືນຄ່າເສດທີ່ເຫຼືອຫຼັງຈາກທີ່ຈຳນວນຖືກຫານໂດຍໂຕຫານແລ້ວ", "ad": "ເປັນຈຳນວນທີ່ທ່ານຕ້ອງການຫາເພື່ອຫາເສດທີ່ເຫຼືອ!ເປັນຕົວຫານ"}, "MROUND": {"a": "(number; multiple)", "d": "ສົ່ງຄືນຈຳນວນທີ່ທີ່ປັດເປັນຈຳນວນ ທະວີຄູນທີ່ທ່ານຕ້ອງການ ", "ad": "​ແມ່ນຄ່າທີ່ທ່ານຕ້ອງການໃຫ້ປັດ!​ແມ່ນຄ່າທະວີຄູນທີ່ທ່ານຕ້ອງການປັດໃຫ້ number"}, "MULTINOMIAL": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນພະຫຸພົດຂອງຊຸດຂໍ້ມູນຕົວເລກ", "ad": "​ແມ່ນຄ່າ 1 ຫາ 255 ຄ່າທີ່ທ່ານຕ້ອງການຫາພະຫຸພົດ"}, "MUNIT": {"a": "(dimension)", "d": "ສົ່ງ​ຄືນ​ແມັດທຣິກຫົວໜ່ວຍ​ສຳລັບ​ມິ​ຕິ​ທີ່​ລະບຸ​ໄວ້", "ad": "​ແມ່ນ​ຕົວ​​ເລກ​ຈຳນວນ​ເຕັມທີ່​ລະບຸ​ມິ​ຕິ​ຂອງ​ແມັດທຣິກຫົວໜ່ວຍ​ທີ່​ທ່ານ​ຕ້ອງການ​ສົ່ງ​ຄືນ"}, "ODD": {"a": "(number)", "d": "ປັດຄ່າຂຶ້ນສຳລັບຈຳນວນບວກ ແລະ ປັດຄ່າລົງສຳລັບຈຳນວນລົບ ໂດຍຈະປັດຄ່າໃຫ້ເທົ່າກັບຈຳນວນເຕັມທີ່ເປັນເລກຄີກທີ່ໃກ້ຄຽງທີ່ສຸດ", "ad": "ເປັນຄ່າທີ່ຕ້ອງການຈະປັດ"}, "PI": {"a": "()", "d": "ສົ່ງຄືນຄ່າ Pi, ເຊິ່ງເທົ່າກັບ 3.14159265358979, ຄວາມລະອຽດ 15 ຫຼັກ", "ad": ""}, "POWER": {"a": "(number; power)", "d": "ສົ່ງຄືນຄ່າຜົນລັບຂອງເລກຂຶ້ນກຳລັງ", "ad": "ເປັນເລກຖານ, ເປັນຈຳນວນຈິງໃດໆ!ເປັນເລກຊີ້ກຳລັງເຊິ່ງຢູ່ໃນເລກຖານ"}, "PRODUCT": {"a": "(number1; [number2]; ...)", "d": "ຫານຜົນຄູນຂອງຕົວເລກທີ່ເປັນຂໍ້ພິສູດທັງໝົດ", "ad": "​ແມ່ນຊຸດຂອງຕົວເລກຄ່າທີມີເຫດຜົນ ຫຼືຂໍ້ຄວາມທີ່ໃຊ້ແທນຕົວເລກ 1 ເຖິງ 255,ຊຸດທີ່ທ່ານຕ້ອງການຫາຜົນຄູນ"}, "QUOTIENT": {"a": "(numerator; denominator)", "d": "ສົ່ງຄືນຈຳນວນເຕັມຂອງຜົນຫານ", "ad": "​ແມ່ນຕົວຕັ້ງຫານ!​ແມ່ນຕົວຫານ"}, "RADIANS": {"a": "(angle)", "d": "ປ່ຽນມູມອົງສາເປັນມູມຣາດຽນ (radians)", "ad": "ເປັນມູມໃນຮູບອົງສາທີ່ທ່ານຕ້ອງການປ່ຽນໃຫ້ເປັນມູມຣາດຽນ"}, "RAND": {"a": "()", "d": "ສົ່ງຄືນຕົວເລກສຸ່ມທີ່ມີຄ່າຫຼາຍກວ່າ ຫຼືເທົ່າກັບ 0 ແຕ່ນ້ອຍກວ່າ 1, ທີ່ແຈກຢາຍຢ່າງເທົ່າໆກັນ (ຄ່າຈະຖືກປ່ຽນແປງເມື່ອເຮັດການຄຳນວນໃໝ່)", "ad": ""}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "ຕອບອະເຣຂອງຕົວເລກສຸ່ມ", "ad": "ຈຳນວນແຖວໃນອະເຣທີ່ຕອບກັບ!ຈຳນວນຖັນໃນອະເຣທີ່ຕອບກັບແລ້ວ!ຈຳນວນຂັ້ນຕ່ຳທີ່ທ່ານຕ້ອງການໃຫ້ຕອບກັບ!ຈຳນວນສູງສຸດທີ່ທ່ານຕ້ອງການໃຫ້ຕອບກັບ!ຕອບຈຳນວນຖ້ວນ ຫຼື ຄ່າທົດສະນິຍົມ. TRUE ສຳລັບຈຳນວນຖ້ວນ, FALSE ສຳລັບຈຳນວນທົດສະນິຍົມ"}, "RANDBETWEEN": {"a": "(bottom; top)", "d": "ສົ່ງຄືນຕົວເລກສຸ່ມທີ່ມີຄ່າລະຫວ່າງຈຳນວນທີ່ທ່ານລະບຸ", "ad": "​ເປັນຈຳນວນເຕັມທີ່ນ້ອຍສຸດທີ່ຟັງຊັນ RANDBETWEEN ຈະສົ່ງຄືນ!​ເປັນຈຳນວນ ເຕັມທີ່ຫຼາຍສຸດທີ່ຟັງຊັນ RANDBETWEEN ຈະສົ່ງຄືນ"}, "ROMAN": {"a": "(number; [form])", "d": "ປ່ຽນຕົວເລກອາລັບບິກໃຫ້ເປັນເລກໂລມັນ, ໃນຮູບແບບຂໍ້ຄວາມ", "ad": "ເປັນຕົວເລກອາລັບບິກທີ່ທ່ານຕ້ອງການປ່ຽນ!ເປັນຕົວເລກທີ່ລະບຸຊະນິດ ຂອງຕົວເລກໂລມັນທີ່ທ່ານຕ້ອງການ."}, "ROUND": {"a": "(number; num_digits)", "d": "ປ່ຽນຈຳນວນເສດໃຫ້ເປັນຈຳນວນທີ່ມີຕຳແໜ່ງທົດສະນິຍົມຕາມທີ່ລະບຸ", "ad": "ແມ່ນເລກທີ່ທ່ານຕ້ອງການປ່ຽນຈຳນວນເສດ!ເປັນຕຳແໜ່ງທົດສະນິຍົມທີ່ທ່ານຕ້ອງການປັດຈຳນວນເສດໃຫ້ເຫຼືອ. ຈຳນວນລົບປັດເສດໄປເບື້ອງຊ້າຍຂອງຈຸດສ່ວນຮ້ອຍ; ສູນເປັນຈຳນວນຖ້ວນທີ່ໃກ້ຄຽງທີ່ສຸດ"}, "ROUNDDOWN": {"a": "(number; num_digits)", "d": "ປັດເສດຈຳນວນລົງ", "ad": "ເປັນຄ່າຈຳນວນຈິງໃດໆທີ່ທ່ານຕ້ອງການປັດເສດລົງ!ເປັນຈຳນວນຂອງຕົວເລກທີ່ທ່ານຕ້ອງການປັດເສດໃຫ້ເຫຼືອ. ຖ້າຄ່າເປັນລົບການຖືກປັດເສດຈະປັດໄປທາງເບື້ອງຊ້າຍຂອງຈຸດສ່ວນເສດ; ຖ້າຄ່າເປັນສູນ ຫຼືລະເວັ້ນຄ່າໄວ້, ການປັດເສດຈະປັດໄປຫາຈຳນວນຖ້ວນທີ່ໃກ້ຄຽງທີ່ສຸດ"}, "ROUNDUP": {"a": "(number; num_digits)", "d": "ປັດເສດຈຳນວນຂຶ້ນ", "ad": "ເປັນຄ່າຈຳນວນຈິງໃດໆທີ່ທ່ານຕ້ອງການປັດເສດຂຶ້ນ!ເປັນຈຳນວນຂອງຕົວເລກທີ່ທ່ານຕ້ອງການປັດເສດໃຫ້ເຫຼືອ. ຖ້າຄ່າເປັນລົບການຖືກປັດເສດຈະປັດໄປທາງເບື້ອງຊ້າຍຂອງຈຸດສ່ວນເສດ; ຖ້າຄ່າເປັນສູນ ຫຼືປະຄ່າໄວ້, ການປັດເສດຈະປັດໄປ​ເປັນຈຳນວນຖ້ວນທີ່ໃກ້ຄຽງທີ່ສຸດ"}, "SEC": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ຊີ​ແຄນ​ທ (secant) ຂອງ​ມູມ", "ad": "​ແມ່ນ​ມູມ​ໃນ​ອົງສາ​ຣາດຽນທີ່​ທ່ານ​ຕ້ອງການ​ໃຫ້​ຄໍານວນ​ຄ່າ​ຊີ​ແຄນ​ທ"}, "SECH": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກ​ຊີ​​ແຄນ​ທ (hyperbolic secant) ຂອງ​ມູມ​ໃດ​ໜຶ່ງ", "ad": "​ແມ່ນ​ມູມ​ໃນ​ອົງສາຣາດຽນທີ່​ທ່ານ​ຕ້ອງການ​ຄ່າ​ໄຮ​ເປີ​ບໍລິກ​ຊີ​ແຄນ​ທ"}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "ສະ​ແດງ​ຜົນການ​ລວມ​ກັນ​ຂອງ​ຊຸດ​ກຳ​ລັງ​ຕ​າມ​ສູດ", "ad": "ແມ່ນ​ຄ່າ​ປ້ອນ​ເຂົ້າ​ໃສ່​ຊຸດ​ກຳ​ລັງ!ແມ່ນ​ກຳ​ລັງ​ເບື້ອງ​ຕົ້ນ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ຍົກກຳ​ລັງ x!ແມ່ນ​ຂັ້​ໜ​ຕອນ​ໂດຍທີ່​ເພີ່ມ n ສຳ​ລັບ​ແຕ່​ລະ​ລາຍ​ການ​ຢູ່​ໃນ​ຊຸດ!ແມ່ນ​ຊຸດ​ຂອງ​ສຳ​ປະ​ສິດ​ໂດຍ​ທີ່​ການ​ຍົກ​ກຳ​ລັງ​ຕໍ່​ມາ​ຂອງ x ແຕ່​ລະ​ຕົວ​ຖືກ​ຄູນ​ຂຶ້ນ"}, "SIGN": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າເຄື່ອງໝາຍຂອງຈຳນວນ: ເທົ່າກັບ1 ຖ້າຈຳນວນເປັນບວກ, ເທົ່າກັບ0 ຖ້າຈຳນວນເປັນສູນ, ຫຼື -1 ຖ້າຈຳນວນເປັນລົບ", "ad": "ແມ່ນຄ່າຈຳນວນຈິງໃດໆ"}, "SIN": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າ ຊິນ", "ad": "ແມ່ນມູມໃນອົງສາຣາດຽນສຳລັບທ່ານຕ້ອງການຊິນ. ອົງສາດີກຣີ * PI()/180 = ອົງສາຣາດຽນ"}, "SINH": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໄຮເປີໂບລິກຊິນ (hyperbolic sine) ຂອງຈຳນວນທີ່ລະບຸ", "ad": "ເປັນຄ່າຈຳນວນຈິງໃດໆ"}, "SQRT": {"a": "(number)", "d": "ສົ່ງຄືນຈຳນວນຂອງຮາກຂັ້ນສອງຕົວເລກທີ່ລະບຸ", "ad": "ແມ່ນຈຳນວນທີ່ທ່ານຕ້ອງການຫາຄ່າຮາກຂັ້ນສອງ"}, "SQRTPI": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າຮາກຂັ້ນສອງຂອງ (number * pi)", "ad": " ​ແມ່ນຈໍານວນທີ່ໄປຄູນກັບ p"}, "SUBTOTAL": {"a": "(function_num; ref1; ...)", "d": "ສົ່ງຄືນຄ່າຜົນລວມຍ່ອຍໃນລາຍການ ຫຼືຖານຂໍ້ມູນ", "ad": "​ແມ່ນຕົວເລກ 1 ຫາ 11 ທີ່ລະບຸຟັງຊັນສະຫຼຸບທີ່ຈະໃຊ້ສຳລັບການເຮັດຜົນລວມຍ່ອຍ! ​ແມ່ນຊ່ວງ 1 ຫາ 254 ຫຼືການອ້າງອີງທີ່ທ່ານຕ້ອງການເຮັດຜົນລວມຍ່ອຍ"}, "SUM": {"a": "(number1; [number2]; ...)", "d": "ບວກຈຳນວນທັງໝົດໃນຊ່ວງຂອງຫ້ອງ", "ad": "​ແມ່ນຕົວເລກ 1 ເຖິງ 255 ຕົວທີ່ນຳມາບວກກັນ. ຄ່າຄວາມ​ຈິງ ແລະຂໍ້ຄວາມໃນຫ້ອງຈະຖືກລະເວັ້ນ, ແຕ່ຈະຖືກລວມຢູ່ນຳ ຖ້າພິມເປັນຂໍ້ພິສູດ"}, "SUMIF": {"a": "(range; criteria; [sum_range])", "d": "ເຮັດການບວກຫ້ອງທີ່ຖືກກັບເງື່ອນໄຂ ຫຼືເກນທີ່ລະບຸ", "ad": "ເປັນຊ່ວງຂອງຫ້ອງທີ່ຕ້ອງການໃຊ້ໃນການປະເມີນເປັນເງື່ອນໄຂ ຫຼືເກນ ເຊິ່ງຢູ່ໃນຮູບແບບຕົວເລກ, ສຳນວນ, ຫຼື ຂໍ້ຄວາມເຊິ່ງຈະຖືກໃຊ້!ເປັນຕົວບອກວ່າຫ້ອງໃດຈະຖືກລວມເຂົ້າດ້ວຍກັນ!ເປັນຊ່ວງຂອງຫ້ອງຈິງທີ່ທ່ານຕ້ອງການຄ່າຜົນລວມ ຖ້າບໍ່ໃສ່ຄ່າຫຍັງໄວ້ຊ່ວງຂອງຫ້ອງທີ່ທ່ານໃຊ້ໃນການປະເມີນຈະຖືກ​ໃຊ້"}, "SUMIFS": {"a": "(sum_range; criteria_range; criteria; ...)", "d": "ເພີ່ມ​ເຊວ​ລະ​ບຸ​ໄວ້​ໂດຍ​ຊຸດ​ເງື່ອ​ໄຂ ຫຼື​ມາດ​ຖານ​ທີ່​ໃຫ້​ໄວ້", "ad": "ແມ່ນ​ເຊວ​ຕົວ​ຈິງ​ທີ່​ຈະ​ລວມ​ກັນ.!ແມ່ນ​ຂອບ​ເຂດ​ເຊວ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ໃຫ້​ປະ​ເມີນ​ຜົນ​ສຳ​ລັບ​ເງື່ອນ​ໄຂ​ສະ​ເພາະ!ແມ່ນ​ເງື່ອນ​ໄຂ ຫຼື​ມາດ​ຖານ​ຢູ່​ໃນ​ຮູບ​ແບບ​ຂອງ​ຕົວ​ເລກ, ສຳ​ນວນ, ຫຼື​ຂໍ້​ຄວາມ​ທີ່​ກຳ​ນົດ​ວ່າ​ຈະ​ເພີ່ມ​ເຊວ​ໃດ​ເຂົ້າ"}, "SUMPRODUCT": {"a": "(array1; [array2]; [array3]; ...)", "d": "ສົ່ງຄືນຜົນລວມຂອງຄ່າຈາກຜົນຄູນ ໂດຍແຕ່ລະຜົນຄູນເກີດຈາກການຄູນກັນລະຫວ່າງຕົວເລກທີ່ມີຕຳແໜ່ງສອດຄ່ອງກັນທີ່ຢູ່ຄົນລະຊ່ວງ ຫຼືຄົນລະອາ​ເຣຍ໌", "ad": "ແມ່ນອາ​ເຣຍ໌ຕັ້ງແຕ່ 2 ຫາ 255 ອາ​ເຣຍ໌ທີ່ທ່ານຕ້ອງການຈະນຳມາຄູນກັນ ແລ້ວຫາຜົນລວມ ໂດຍທີ່ອາ​ເຣຍ໌ທັງໝົດຕ້ອງມີຂະໜາດເທົ່າກັນ"}, "SUMSQ": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຜົນລວມຂອງຂໍ້ພິສູດຂຶ້ນກຳລັງສອງ. ຂໍ້​ພິສູດດັ່ງກ່າວເປັນໄດ້ທັງຕົວເລກ, ອາ​ເຣຍ໌, ຊື່, ຫຼືການອ້າງອີງຫາຫ້ອງທີ່ມີຕົວເລກ", "ad": "​ແມ່ນຕົວເລກ, ອາ​ເຣຍ໌, ຊື່, ຫຼືການອ້າງອີງຫາອາ​ເຣຍ໌ 1 ຫາ 255 ຄ່າທີ່ທ່ານຕ້ອງການຫາຄ່າຜົນລວມກຳລັງສອງ"}, "SUMX2MY2": {"a": "(array_x; array_y)", "d": "ຫາຜົນລວມຂອງຄ່າຜົນຕ່າງກັນ ເຊິ່ງແຕ່ລະຄ່າເປັນຜົນຕ່າງກັນລະຫວ່າງຄ່າຂຶ້ນກຳລັງສອງຂອງ​ສອງຊ່ວງ ຫຼືສອງອາ​ເຣຍ໌", "ad": " ເປັນຊ່ວງທຳອິດ ຫຼືອາ​ເຣຍ໌ທຳອິດຂອງຕົວເລກ ເຊິ່ງສາມາດເປັນໄດ້ທັງຕົວເລກ ຫຼື ຊື່,​ອາ​ເຣຍ໌, ຫຼືການອ້າງອີງທີ່ມີຄ່າຕົວເລກຢູ່ນຳ!ເປັນຊ່ວງທີສອງ ຫຼືອາ​ເຣຍ໌ທີສອງ ຂອງຕົວເລກເຊິ່ງສາມາດເປັນໄດ້ທັງຕົວເລກ ຫຼື ຊື່, ອາ​ເຣຍ໌ ຫຼື ການອ້າງອີງທີ່ມີຄ່າຕົວເລກຢູ່ນຳ"}, "SUMX2PY2": {"a": "(array_x; array_y)", "d": "ສົ່ງຄືນຜົນລວມທັງໝົດຂອງຄ່າຜົນບວກ ເຊິ່ງແຕ່ລະຄ່າເປັນຜົນບວກລະຫວ່າງຄ່າຂຶ້ນກຳລັງສອງຂອງຕົວເລກທີ່ມີຕຳແໜ່ງສອດຄ່ອງກັນໃນຊ່ວງສອງຊ່ວງ ຫຼືສ​ອງອາ​ເຣຍ໌", "ad": "ເປັນຊ່ວງທຳອິດ ຫຼືອາ​ເຣຍ໌ທຳອິດຂອງຕົວເລກ ເຊິ່ງສາມາດເປັນໄດ້ທັງຕົວເລກ ຫຼື ຊື່, ​ເປັນອາ​ເຣຍ໌, ຫຼືການອ້າງອີງທີ່ມີຄ່າຕົວເລກຢູ່ນຳ!ເປັນຊ່ວງທີສອງ ຫຼືອາ​ເຣຍ໌ທີສອງຂອງຕົວເລກ ເຊິ່ງສາມາດເປັນໄດ້ທັງຕົວເລກ, ຫຼື ຊື່, ​ເປັນອາ​ເຣຍ໌, ຫຼືການອ້າງອີງທີ່ມີຄ່າຕົວເລກຢູ່ນຳ"}, "SUMXMY2": {"a": "(array_x; array_y)", "d": "ຫາຜົນລວມຂອງຄ່າຂຶ້ນກຳລັງສອງຂອງຜົນຕ່າງລະຫວ່າງຕົວເລກທີ່ມີຕຳແໜ່ງສອດຄ່ອງກັນໃນສອງຊ່ວງ ຫຼືສອງອາ​ເຣຍ໌", "ad": " ເປັນຊ່ວງທຳອິດ ຫຼືອາ​ເຣຍ໌ທຳອິດ ຂອງຄ່າ ເຊິ່ງສາມາດເປັນໄດ້ທັງຕົວເລກ ຫຼື ຊື່,ອາ​ເຣຍ໌, ຫຼືການອ້າງອີງທີ່ມີຄ່າຕົວເລກຢູ່ນຳ! ເປັນຊ່ວງທີສອງ ຫຼື​ອາ​ເຣຍ໌ທີ​ສອງຂອງຄ່າເຊິ່ງສາມາດເປັນໄດ້ທັງຄ່າຕົວເລກ ຫຼື ຊື່,ອາ​ເຣຍ໌, ຫຼືການອ້າງອີງທີ່ມີຄ່າຕົວເລກຢູ່ນຳ"}, "TAN": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າ​ແທນ​ເຈັນ", "ad": "ແມ່ນມູມໃນອົງສາຣາດຽນ ທີ່ທ່ານຕ້ອງການຄ່າ​ແທນ​ເຈັນ. ອົງສາດີກຣີ * PI()/180 = ອົງສາຣາດຽນ"}, "TANH": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໄຮເປີໂບລິກ​ແທນ​ເຈັນ (hyperbolic tangent) ຂອງຈຳນວນທີ່ລະບຸ", "ad": "ເປັນຄ່າຈຳນວນຈິງໃດໆ"}, "TRUNC": {"a": "(number; [num_digits])", "d": "ປັດເສດຕົວເລກຖີ້ມໃຫ້ເປັນຈຳນວນຖ້ວນ ໂດຍການເອົາທົດສະນິຍົມ ຫຼືສ່ວນເສດອອກ", "ad": "ເປັນຕົວເລກທີ່ທ່ານຕ້ອງການປັດເສດຖີ້ມ!ເປັນຈຳນວນທີ່ລະບຸຄວາມແນ່ນອນຂອງການປັດເສດຄ່າຈະເປັນ 0 (ສູນ) ຖ້າບໍ່ໄດ້ໃສ່ຄ່າຫຍັງໄວ້"}, "ADDRESS": {"a": "(row_num; column_num; [abs_num]; [a1]; [sheet_text])", "d": "ສ້າງການອ້າງອີງຫ້ອງໃນຮູບແບບຂໍ້ຄວາມ, ໂດຍໃຊ້ການລະບຸໝາຍເລກແຖວ ແລະ ໝາຍເລກຖັນ", "ad": "ເປັນໝາຍ ເລກແຖວທີ່ໃຊ້ໃນການອ້າງອີງຫ້ອງ: Row_number = 1 ສຳລັບແຖວທີ່ 1!ເປັນໝາຍເລກຖັນທີ່ໃຊ້ໃນການອ້າງອີງຫ້ອງ. ຕົວຢ່າງເຊັ່ນ, Column_number = 4 ສຳລັບຖັນ D!ລະບຸຊະນິດໃນການອ້າງອີງ: ແບບສົມບູນ = 1; ແຖວທີ່ສົມບູນ/ຖັນທີ່ສຳພັນ = 2; ແຖວທີ່ສຳພັນ/ຖັນທີ່ສົມບູນ = 3; ຄວາມສຳພັນ = 4!ເປັນຄ່າຄວາມ​ຈິງທີ່ລະບຸຊະນິດໃນການອ້າງອີງ: ຮູບແບບ A1 ໃຫ້ໃຊ້ຄ່າ = 1 ຫຼື TRUE; ຮູບແບບ R1C1 ໃຫ້ໃຊ້ຄ່າ = 0 ຫຼື FALSE!ເປັນຂໍ້ຄວາມທີ່ໃຊ້ລະບຸຊື່ຂອງແຜນງານທີ່ຈະຖືກໃຊ້ເປັນການອ້າງອີງພາຍນອກ"}, "CHOOSE": {"a": "(index_num; value1; [value2]; ...)", "d": "ເລືອກຄ່າ ຫຼືຄຳສັ່ງທີ່ຈະດຳເນີນການຈາກລາຍການຂອງຄ່າ, ຕາມຕົວເລກດັດ​ສະ​ນີ", "ad": "ລະບຸວ່າຄ່າພິສູດໃດທີ່ຖືກເລືອກ. ໂດຍທີ່ Index_num ຕ້ອງມີຄ່າລະຫວ່າງ 1 ຫາ 254, ຫຼືເປັນສູດ ຫຼືເປັນການອ້າງອີງຫາຕົວເລກທີ່ມີຄ່າລະຫວ່າງ 1 ຫາ 254!ແມ່ນ 1 ຫາ 254ເປັນຕົວເລກການອ້າງອີງຫ້ອງ, ຊື່ທີ່ຖືກກຳນົດ, ສູດ, ຟັງຊັນ, ຫຼືການພິສູດຂໍ້ຄວາມທີ່ CHOOSE ເລືອກຈາກ"}, "COLUMN": {"a": "([reference])", "d": "ສົ່ງຄືນໝາຍເລກຖັນຂອງການອ້າງອີງ", "ad": "ເປັນຫ້ອງ ຫຼື ຊ່ວງຂອງຫ້ອງຕິດກັນທີ່ທ່ານຕ້ອງການໝາຍເລກຖັນ. ຖ້າບໍ່ໃສ່ຄ່າຫຍັງ, ຈະສົ່ງຄືນໝາຍເລກຖັນຂອງຫ້ອງທີ່ບັນຈຸຟັງຊັນ COLUMN "}, "COLUMNS": {"a": "(array)", "d": "ສົ່ງຄືນຈຳນວນຂອງຖັນ​ໃນ​​ອາ​ເຣຍ໌ ຫຼືການອ້າງອີງທີ່ລະບຸ", "ad": "ເປັນອາ​ເຣຍ໌ ຫຼືສູດອາ​ເຣຍ໌, ຫຼື​ເປັນການອ້າງອີງຫາຊ່ວງຂອງຫ້ອງທີ່ທ່ານຕ້ອງການຫາຈຳນວນຂອງຖັນ"}, "FORMULATEXT": {"a": "(reference)", "d": "ສົ່ງ​ຄືນ​ສູດ​ເປັນ​ສະຕຣິງ", "ad": "​ແມ່ນ​ການ​ອ້າງ​ອີງ​ເຖິງ​ສູດ​"}, "HLOOKUP": {"a": "(lookup_value; table_array; row_index_num; [range_lookup])", "d": "ຊອກຫາຄ່າໃນແຖວເທິງສຸດຂອງຕາຕະລາງ ຫຼືອາ​ເຣຍ໌ຂອງຄ່າ ແລະສົ່ງຄືນຄ່າໃນຖັນ ດຽວກັນຈາກແຖວທີ່ທ່ານລະບຸ", "ad": "ເປັນຄ່າທີ່ຕ້ອງການຄົ້ນຫາໃນແຖວທຳອິດຂອງຕາຕະລາງ ແລະ ສາມາດເປັນໄປໄດ້ທັງຄ່າ, ການອ້າງອີງ ຫຼື ສາຍຂໍ້ຄວາມ!ເປັນຕາຕະລາງຂອງຂໍ້ຄວາມ, ຕົວເລກ ຫຼື ຄ່າຄວາມ​ຈິງໃນການຊອກຫາຂໍ້ມູນ. Table_array ສາມາດເປັນການອ້າງອີງຫາຊ່ວງ ຫຼືຊ່ວງຂອງຊື່!ເປັນໝາຍເລກແຖວໃນ table_array ເຊິ່ງຄ່າທີ່ກົງກັນຈະຖືກສົ່ງຄືນ. ແຖວທຳອິດທີ່ເປັນຄ່າໃນຕາຕະລາງເປັນແຖວທີ່ 1!ເປັນຄ່າຄວາມ​ຈິງ: ເພື່ອຊອກຫາຄ່າທີ່ໃກ້ຄຽງທີ່ສຸດໃນແຖວທຳອິດ (ລຽງລຳດັບຈາກນ້ອຍໄປຫາໃຫຍ່) = TRUE ຫຼືປະຄ່າໄວ້; ເພື່ອຊອກຫາຄ່າທີ່ກົງກັນທຸກປະການ = FALSE"}, "HYPERLINK": {"a": "(link_location; [friendly_name])", "d": "ສ້າງທາງລັດເພື່ອເປີດເອກະສານທີ່ເກັບຢູ່ໃນຮາດ​​ດຣາຍຂອງທ່ານ,ໃນເຊີບເວີເຄື່ອຂ່າຍ, ຫຼືໃນອິນເຕີເນັດ", "ad": "ເປັນຂໍ້ຄວາມທີ່ລະບຸເສັ້ນທາງ ແລະ ຊື່​ໄຟ​ລຂອງເອກະສານທີ່ຕ້ອງການເປີດ,ລະບຸຕຳແໜ່ງຮາດດຣາຍ,ລະບຸທີ່ຢູ່ UNC, ຫຼື ເສັ້ນທາງ URL !ເປັນຂໍ້ຄວາມ ຫຼືຕົວເລກທີ່ຕ້ອງການໃຫ້ສະແດງໃນຫ້ອງ.ຖ້າບໍ່ໃສ່ຄຳຫຍັງໄວ້, ຫ້ອງຈະສະແດງຂໍ້ຄວາມ Link_location"}, "INDEX": {"a": "(array; row_num; [column_num]!reference; row_num; [column_num]; [area_num])", "d": "ສົ່ງຄືນຄ່າ ຫຼືການອ້າງອີງຂອງຫ້ອງຢູ່ຈຸດຕັດກັນລະຫວ່າງແຖວ ແລະຖັນ, ​ໃນ​ຊ່ວງທີ່ກໍານົດ​ໃຫ້", "ad": " ເປັນຊ່ວງຂອງຫ້ອງ ຫຼືຄ່າຄົງທີ່ອາເຣຍ໌.!ເລືອກແຖວໃນອາ​ເຣຍ໌ ຫຼືການອ້າງອີງທີ່ຕ້ອງການໃຫ້ສົ່ງຄືນຄ່າ​. ຖ້າບໍ່ລະບຸ, Column_num ​ແມ່ນ​ຈໍາ​ເປັນ​ຕ້ອງ​ລະບຸ!ເລືອກຖັນໃນອາ​ເຣຍ໌ ຫຼືການອ້າງອີງທີ່ຕ້ອງການໃຫ້ສົ່ງຄືນຄ່າ​. ຖ້າຂ້າມ​ໄປ, Row_num ຕ້ອງຖືກລະບຸ!ເປັນການອ້າງອີງໄປທີໜຶ່ງ ຫຼື ຫຼາຍຊ່ວງຂອງຫ້ອງ!ເລືອກແຖວໃນອາ​ເຣຍ໌ ຫຼືການອ້າງອີງທີ່ຕ້ອງການໃຫ້ສົ່ງຄືນຄ່າ. ຖ້າຂ້າມ​ໄປ, Column_num ​​ແມ່ນ​ຈໍາ​ເປັນ​ຕ້ອງ​ລະບຸ!ເລືອກຖັນໃນອາ​ເຣຍ໌ ຫຼືການອ້າງອີງທີ່ຕ້ອງການໃຫ້ສົ່ງຄືນຄ່າ​. ຖ້າຂ້າມ​ໄປ, Row_num ຕ້ອງຖືກລະບຸ! ເລືອກ​ຊ່ວງ​ທີ່​ຈະ​ສົ່ງ​ຄ່າ​ຄືນ​ຈາກ. ພື້ນທີ່ 1,ພື້ນທີ່ທີຖືກເລືອກ ຫຼືເຂົ້າເຖິງເປັນອັນ ດັບສອງຈະເປັນພື້ນທີ່ 2,ແລະ ແບບນີ້ຕໍ່ເນື່ອງໄປຕາມລຳດັບ"}, "INDIRECT": {"a": "(ref_text; [a1])", "d": "ສົ່ງຄືນຄ່າການອ້າງອີງທີ່ຖືກລະບຸຢູ່ໃນສາຍຂໍ້ຄວາມ", "ad": "ເປັນການອ້າງອີງຫາຫ້ອງທີ່ມີການອ້າງອີງໃນລັກສະນະ A1- ຫຼື R1C1 ເປັນການອ້າງອີງຫາຊື່ທີ່ຖືກກຳນົດສຳລັບການອ້າງອີງ, ຫຼືການອ້າງອີງຫາຫ້ອງທີ່ມີສາຍຂໍ້ຄວາມ!ເປັນຄ່າມີເຫດຜົນທີ່ລະບຸຊະນິດຂອງການອ້າງອີງໃນ Ref_text: ລັກສະນະແບບ R1C1 = FALSE; ຖ້າບໍ່​ໄດ້​ໃສ່​ຄ່າ​ຫຍັງ ຫຼືລັກສະນະແບບ A1 = TRUE"}, "LOOKUP": {"a": "(lookup_value; lookup_vector; [result_vector]!lookup_value; array)", "d": "ຊອກຫາອາດຖຶກຄ່າຈາກຊ່ວງໜຶ່ງ-ແຖວ ຫຼື ໜຶ່ງ-ຖັນ ຫຼືຈາກອາ​ເຣຍ໌. ສະໜອງ ສຳລັບຄວາມສາມາດເຂົ້າກັນໄດ້ລະຫວ່າງເກົ່າ ແລະ ໃໝ່", "ad": "ເປັນຄ່າທີ່ LOOKUP ຊອກຫາໃນ Lookup_vector ແລະ ສາມາດເປັນໄດ້ທັງໝາຍເລກ, ຂໍ້ຄວາມ, ຄ່າຄວາມ​ຈິງ ຫຼື ການອ້າງອີງ ຫຼື ຊື່ ຫຼື ການອ້າງອີງຫາຄ່າ!ເປັນຊ່ວງທີ່ມີພຽງໜຶ່ງແຖວ ຫຼື ໜຶ່ງຖັນຂອງຂໍ້ຄວາມ, ໝາຍເລກ ຫຼື ຄ່າຄວາມ​ຈິງ, ເຊິ່ງຖືກລຽງລຳດັບຈາກນ້ອຍໄປຫາໃຫຍ່!ເປັນຊ່ວງຂໍ້ຄວາມພາຍໃນທີ່ມີພຽງໜຶ່ງແຖວ ຫຼື ໜຶ່ງຖັນ ແລະ ມີຂະໜາດດຽວກັບ Lookup_vector!ເປັນຄ່າທີ່ LOOKUP ຊອກຫາໃນອະ​ເຣ ແລະ ສາມາດເປັນໄດ້ທັງໝາຍເລກ, ຂໍ້ຄວາມ, ຄ່າຄວາມ​ຈິງ ຫຼື ຊື່ ຫຼື ການອ້າງອີງຫາຄ່າ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ມີຂໍ້ຄວາມ, ໝາຍເລກ ຫຼື ຄ່າຄວາມ​ຈິງທີ່ທ່ານຕ້ອງການສົມທຽບກັບ Lookup_value"}, "MATCH": {"a": "(lookup_value; lookup_array; [match_type])", "d": "ສົ່ງຄືນຄ່າທີ່ກ່ຽວຂ້ອງກັບຕຳແໜ່ງຂອງລາຍການໃນອາ​ເຣຍ໌ທີ່ກົງກັບຄ່າທີ່ລະບຸເຊິ່ງຢູ່ໃນລຳດັບການຈັດ ລຽງທີ່ລະບຸ", "ad": "ເປັນຄ່າທີ່ທ່ານໃຊ້ໃນການຫາຄ່າທີ່ທ່ານຕ້ອງການຫາໃນອະ​ເຣ, ໝາຍເລກ, ຂໍ້ຄວາມ ຫຼື ຄ່າຄວາມ​ຈິງ ຫຼື ການອ້າງອີງໄປທີ່ຢ່າງໃດຢ່າງໜຶ່ງທີ່ກ່າວມາ!ເປັນຊ່ວງຂອງຫ້ອງທີ່ຕິດກັນທີ່ມີຄວາມເປັນໄປໄດ້ທີ່ຈະມີຄ່າທີ່ຕ້ອງການຄົ້ນຫາຢູ່ສາມາດເປັນໄດ້ທັງອະເຣຂອງຄ່າ ຫຼື ການອ້າງອີງຫາອະ​ເຣ!ເປັນໝາຍເລກ 1, 0 ຫຼື -1 ເຊິ່ງເປັນຕົວທີ່ບົ່ງບອກຄ່າທີ່ຈະສົ່ງຄືນ."}, "OFFSET": {"a": "(reference; rows; cols; [height]; [width])", "d": "ສົ່ງຄືນຄ່າທີ່ເປັນຜົນຈາກການອ້າງອີງຫາຊ່ວງທີ່ທີ່ຖືກລະບຸ ໂດຍຈຳນວນຂອງແຖວ ແລະຈຳນວນຂອງຖັນ ເຊິ່ງຖືກນັບຈາກການອ້າງອີງ", "ad": "ເປັນການອ້າງອີງທີ່ທ່ານຕ້ອງການໃຫ້ເປັນຖານສຳລັບການຫັກ​ລ້າງ, ເປັນການອ້າງອີງຫາຫ້ອງຫຼືຊ່ວງຂອງຫ້ອງທີ່ຢູ່ຕິດກັນ!ເປັນຈຳນວນຂອງແຖວ, ຂຶ້ນ ຫຼືລົງ,ທີ່ທ່ານຕ້ອງການໃຫ້ນັບຈາກຫ້ອງຂ້າງເທິງ-ເບື້ອງຊ້າຍຂອງຜົນທີ່ຢັ້ງຢືນເຖິງ!ເປັນຈຳນວນຂອງຖັນ, ທີ່ຢູ່ເບື້ອງຊ້າຍ ຫຼືເບື້ອງຂວາ, ທີ່ທ່ານຕ້ອງການໃຫ້ນັບຈາກຫ້ອງຂ້າງເທິງ-ເບື້ອງຊ້າຍຂອງ ຜົນທີ່ຢັ້ງຢືນເຖິງ!ເປັນຄວາມສູງ, ໃນຈຳນວນຂອງແຖວ, ທີ່ທ່ານຕ້ອງການໃຫ້ເປັນຜົນຮັບ, ຖ້າບໍ່​ໄດ້​ໃສ່​ຄ່າ​ຫຍັງ, ທີ່ມີຄວາມສູງຄືກັນກັບການອ້າງອີງ!ເປັນຄວາມກ້ວາງ, ໃນຈຳນວນຂອງຖັນ, ທີ່ທ່ານຕ້ອງການໃຫ້ເປັນຜົນຮັບ, ຄວາມກ້ວາງຄືກັນກັບການອ້າງອີງ ຖ້າ​ບໍ່​ໃສ່​ຄ່າ​ຫຍັງ"}, "ROW": {"a": "([reference])", "d": "ສົ່ງຄືນໝາຍເລກແຖວ ຂອງການອ້າງອີງ", "ad": "ເປັນຫ້ອງ ຫຼືຊ່ວງດຽວ ຂອງຫ້ອງທີ່ທ່ານຕ້ອງການຫາໝາຍເລກຂອງແຖວ; ຖ້າບໍ່ໃສ່ຄ່າຫຍັງ, ຈະສົ່ງຄືນໝາຍເລກແຖວຂອງຫ້ອງທີ່ບັນຈຸຟັງຊັນ ROW "}, "ROWS": {"a": "(array)", "d": "ສົ່ງຄືນຈຳນວນຂອງແຖວ​ໃນການອ້າງອີງ ຫຼືອາ​ເຣຍ໌", "ad": "ເປັນອາ​ເຣຍ໌, ສູດ​ອາ​ເຣຍ໌, ຫຼື​ເປັນການອ້າງອີງຫາ​ຊ່ວງຂອງຫ້ອງທີ່ທ່ານຕ້ອງການຫາຈຳນວນຂອງແຖວ"}, "TRANSPOSE": {"a": "(array)", "d": "ປ່ຽນຄ່າໃນຊ່ວງແນວຕັ້ງຂອງຫ້ອງໄປເປັນຄ່າໃນຊ່ວງແນວນອນ, ຫຼືປິ້ນກັນ", "ad": "ເປັນຊ່ວງຂອງຫ້ອງ​ເທິງຕາຕະລາງ​​ແຜ່ນ​ວຽກ ຫຼືອາ​ເຣຍ໌ຂອງ​ຄ່າທີ່ທ່ານຕ້ອງການສັບ​ປ່ຽນ"}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": "ຕອບຄ່າທີ່ບໍ່ຊ້ຳກັນຈາກໄລຍະ ຫຼື ອະເຣໃດໜຶ່ງ.", "ad": "ໄລຍະ ຫຼື ອະເຣໃດໜຶ່ງທີ່ຕອບຄ່າແຖວ ຫຼື ຖັນທີ່ບໍ່ຊ້ຳກັນ!ເປັນຄ່າໂລຈິກ: ປຽບທຽບແຖວກັບຂໍ້ມູນອື່ນແລ້ວຕອບກັບຄ່າແຖວແບບບໍ່ຊ້ຳກັນ = FALSE ຫຼື omitted; ປຽບທຽບຖັນກັບຂໍ້ມູນອື່ນແລ້ວຕອບກັບຄ່າຖັນແບບບໍ່ຊ້ຳກັນ = TRUE!ເປັນຄ່າໂລຈິກ: ຕອບຄ່າແຖວ ຫຼື ຖັນທີ່ເກີດຂຶ້ນເທື່ອດຽວຈາກອະເຣ = TRUE; ຕອບແຖວ ຫຼື ຖັນທີ່ແຕກຕ່າງກັນຢ່າງຊັດເຈນທັງໝົດຈາກອະເຣ = FALSE ຫຼືomitted"}, "VLOOKUP": {"a": "(lookup_value; table_array; col_index_num; [range_lookup])", "d": "ຊອກຫາຄ່າໃນຖັນຊ້າຍສຸດຂອງຕາຕະລາງ, ແລ້ວ ສົ່ງກັບຄືນຄ່າໃນແຖວດຽວກັນຈາກຖັນທີ່ທ່ານລະບຸ. ຕາມຄ່າ​ເລີ່​ມຕົ້ນ, ຕາຕະລາງຕ້ອງຖືກລຽງລຳດັບຈາກນອ້ຍໄປຫາໃຫຍ່", "ad": "ເປັນຄ່າທີ່ຕ້ອງການຄົ້ນຫາໃນຖັນທຳອິດຂອງຕາຕະລາງ ແລະ ສາມາດເປັນໄດ້ທັງຄ່າ, ການອ້າງອີງ ຫຼື ສາຍຂໍ້ຄວາມ!ເປັນຕາຕະລາງຂອງຂໍ້ຄວາມ, ຕົວເລກ ຫຼື ຄ່າຄວາມ​ຈິງ, ທີ່ມີການກູ້ຂໍ້ມູນຄືນ. Table_array ສາມາດເປັນການອ້າງຫາຊ່ວງ ຫຼື ຊ່ວງຂອງຊື່ກໍໄດ້!ເປັນໝາຍເລກຖັນໃນ table_array ເຊິ່ງຄ່າທີ່ກົງກັນຈະຖືກສົ່ງຄືນ. ແຖວທຳອິດທີ່ເປັນຄ່າໃນຕາຕະລາງເປັນຖັນທີ 1!ເປັນຄ່າຄວາມ​ຈິງ: ເພື່ອຊອກຫາຄ່າທີ່ໃກ້ຄຽງທີ່ສຸດໃນຖັນທຳອິດ (ລຽງລຳດັບຈາກນ້ອຍໄປຫາໃຫຍ່) = TRUE ຫຼືປະຄ່າໄວ້; ເພື່ອຊອກຫາຄ່າທີ່ກົງກັນທຸກປະການ = FALSE"}, "XLOOKUP": {"a": "(lookup_value; lookup_array; return_array; [if_not_found]; [match_mode]; [search_mode])", "d": "ຊອກຫາໄລຍະ ຫຼື ອະເຣໃດໜຶ່ງແລ້ວຕອບລາຍການທີ່ກົງກັນຈາກໄລຍະ ຫຼື ອະເຣທີສອງ. ຕາມຄ່າເລີ່ມຕົ້ນ, ຄ່າທີ່ແນ່ນອນຈະຖືກໃຊ້", "ad": "ເປັນຄ່າເພື່ອຊອກຫາ!ເປັນອະເຣ ຫຼື ຊ່ວງເພື່ອຊອກຫາ!ເປັນອະເຣ ຫຼື ຊ່ວງເພື່ອສົ່ງຄືນ!ສົ່ງຄືນຫາກບໍ່ພົບຄ່າທີ່ກົງກັນ!ລະບຸວິທີຈັບຄູ່ lookup_value ກັບຄ່າໃນ lookup_array!ລະບຸໂໝດຊອກຫາທີ່ຈະໃຊ້. ຕາມຄ່າເລີ່ມຕົ້ນ, ຈະມີການຊອກຫາຈາກທຳອິດຫາສຸດທ້າຍ"}, "CELL": {"a": "(info_type; [reference])", "d": "ຄືນຂໍ້ມູນກ່ຽວກັບການຈັດຮູບແບບ, ສະຖານທີ່, ຫຼື ເນື້ອໃນຂອງ ​ເຊ​ລ"}, "ERROR.TYPE": {"a": "(error_val)", "d": "ສົ່ງຄືນຄ່າຕົວເລກທີ່ກົງກັນກັບຄ່າ​ຜິດພາດ.", "ad": "ແມ່ນຄ່າຜິດພາດທີ່ທ່ານຕ້ອງການລະບຸເປັນຕົວເລກ, ແລະສາມາດເປັນຄ່າຂໍ້ຜິດພາດທີ່ແທ້ຈິງ ຫຼືເປັນການອ້າງອີງເຖິງຫ້ອງທີ່ບັນຈຸຄ່າຜິດພາດນັ້ນ"}, "ISBLANK": {"a": "(value)", "d": "ກວດ​ເບິ່ງວ່າ ການ​ອ້າງ​ອີງ​ແມ່ນ​ໄປ​ໃສ່​ເຊວ​ເປົ່າ​ບໍ, ແລະ​ສະ​ແດງ​ຜົນ TRUE ຫຼື False ", "ad": "ແມ່ນ​ເຊວ ຫຼື​ຊື່​ທີ່​ອ້າງ​ອີງເຖິງ​ເຊວ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ທົດ​ສອບ"}, "ISERR": {"a": "(value)", "d": "ກວດສອບເບິ່ງວ່າຄ່ານັ້ນເປັນຂໍ້ຜິດພາດນອກເໜືອໄປຈາກ #N/A ບໍ່ ແລະສົ່ງກັບຄ່າ TRUE ຫຼື FALSE", "ad": "ເປັນຄ່າທີ່ທ່ານຕ້ອງການທົດສອບ. ເຊິ່ງສາມາດເປັນຄ່າທີ່ອ້າງອີງໄປຫາຫ້ອງ, ສູດ, ຫຼືໄປຫາຊື່ ທີ່ອ້າງອີງເຖິ່ງ ຫ້ອງ, ສູດ, ຫຼືຄ່າ"}, "ISERROR": {"a": "(value)", "d": "ກວດສອບເບິ່ງວ່າຄ່ານັ້ນເປັນຂໍ້ຜິດພາດຫຼືບໍ່ ແລະ ຕອບກັບ TRUE ຫຼື FALSE", "ad": "ເປັນຄ່າທີ່ທ່ານຕ້ອງການທົດສອບ. ເຊິ່ງສາມາດເປັນຄ່າທີ່ອ້າງອີງໄປຫາຫ້ອງ, ສູດ, ຫຼືໄປຫາຊື່ ທີ່ອ້າງອີງເຖິ່ງ ຫ້ອງ, ສູດ, ຫຼືຄ່າ"}, "ISEVEN": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າ TRUE ຖ້າຈຳນວນທີ່ລະບຸເປັນເລກຄູ່", "ad": "​ແມ່ນຄ່າທ່ານທີ່ຕ້ອງການທົດສອບ"}, "ISFORMULA": {"a": "(reference)", "d": "ກວດ​ເບິ່ງ​ວ່າການ​ອ້າງ​ອີງ​​ແມ່ນ​ການ​ອ້າງ​ອີງ​ໄປ​ຫາ​ຫ້ອງ​ທີ່​ມີ​ສຸດ​ຢູ່​ຫຼືບໍ່ ​ແລະ​ສົ່ງ​ຄືນ​ຄ່າ TRUE ຫຼື FALSE", "ad": "​ເປັນ​ການ​ອ້າງ​ອີງ​ໄປ​ຫາ​ຫ້ອງ​ທີ່​ທ່ານ​ຕ້ອງການ​ທົດ​ສອບ.  ການ​ອ້າງ​ອີງ​ອາດ​ຈ​ແມ່ນ​ການ​ອ້າງ​ອີງ​​ຫ້ອງ, ສູດ ຫຼືຊື່​ທີ່​ອ້າງ​ອີງ​ເຖິງ​ຫ້ອງ​"}, "ISLOGICAL": {"a": "(value)", "d": "ກວດສອບຄ່າ ວ່າເປັນຄ່າທີ່ມີເຫດຜົນ (TRUE ຫຼື FALSE) ຫຼືບໍ່ ແລະ ສົ່ງກັບຄືນຄ່າ TRUE ຫຼື FALSE", "ad": "ເປັນຄ່າທີ່ທ່ານຕ້ອງການກວດສອບ. ເຊິ່ງສາ ມາດເປັນຄ່າທີ່ອ້າງອີງໄປທີ່ຫ້ອງ, ສູດ, ຫຼືໄປທີ່ຊື່ທີ່ອ້າງອີງ ເຖິງຫ້ອງ, ສູດ, ຫຼືຄ່າ"}, "ISNA": {"a": "(value)", "d": "ກວດສອບເບິ່ງວ່າເປັນຄ່າ #N/A, ແລະສົ່ງກັບຄ່າ TRUE ຫຼື FALSE", "ad": "ເປັນຄ່າທີ່ທ່ານຕ້ອງການທົດສອບ. ເຊິ່ງສາມາດເປັນຄ່າທີ່ອ້າງອີງໄປຫາຫ້ອງ, ສູດ, ຫຼືໄປຫາຊື່ ທີ່ອ້າງອີງເຖິ່ງ ຫ້ອງ, ສູດ, ຫຼືຄ່າ"}, "ISNONTEXT": {"a": "(value)", "d": "ກວດ​ເບິ່ງວ່າ ຄ່າ​ບໍ່​ຢູ່​ໃນ​ຂໍ້​ຄວາມ (ເຊວ​ເປົ່າບໍ່​ແມ່ນ​ຂໍ້​ຄວາມ) ບໍ່, ແລະ​ສະ​ແດງ​ຜົນ TRUE ຫຼື False", "ad": "ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ທົດ​ສອບ: ເຊວ; ສູດ; ຫຼື​ຊື່​ອ້າງ​ອີງ​ເຖິງ​ເຊວ, ສູດ, ຫຼື​ຄ່າ"}, "ISNUMBER": {"a": "(value)", "d": "ກວດ​ເບິ່ງວ່າ ​ຄ່າ​ແມ່ນຕົວ​ເລກບໍ, ແລະ​ສະ​ແດງ​ຜົນ TRUE ຫຼື False ", "ad": "ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ທົດ​ສອບ ​ຄ່າ​ສາ​ມາດ​ອ້າງ​ອີງ​ເຖິງ​ເຊວ, ສູດ, ຫຼື​ຊື່​ທີ່​ອ້າງ​ອີງ​ເຖິງ​ເຊວ, ສູດ, ຫຼື​ຄ່າ"}, "ISODD": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າ TRUE ຖ້າຈຳນວນທີ່ລະບຸເປັນເລກຄີກ ", "ad": "​ແມ່ນຄ່າທ່ານທີ່ຕ້ອງການທົດສອບ"}, "ISREF": {"a": "(value)", "d": "ກວດສອບຫາຄ່າວ່າເປັນການອ້າງອີງຫຼືບໍ່, ແລ້ວ ສົ່ງກັບຄືນຄ່າ TRUE ຫຼື FALSE", "ad": "ເປັນຄ່າທີ່ທ່ານຕ້ອງ ການທົດສອບ. ເຊິ່ງສາມາດເປັນຄ່າທີ່ອ້າງອີງໄປທີ່ຫ້ອງ, ສູດ, ຫຼືໄປຫາຊື່ທີ່ອ້າງອີງໄປທີ່ຫ້ອງ, ສູດ, ຫຼືຄ່າ"}, "ISTEXT": {"a": "(value)", "d": "ກວດ​ເບິ່ງວ່າ ​ຄ່າ​ແມ່ນຂໍ​ຄວາມບໍ, ແລະ​ສະ​ແດງ​ຜົນ TRUE ຫຼື False ", "ad": "ແມ່ນ​ເຄ່າ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ທົດ​ສອບ ​ຄ່າ​ສາ​ມາດ​ອ້າງ​ອີງ​ເຖິງ​ເຊວ, ສູດ, ຫຼື​ຊື່​ທີ່​ອ້າງ​ອີງ​ເຖິງ​ເຊວ, ສູດ, ຫຼື​ຄ່າ"}, "N": {"a": "(value)", "d": "ປ່ຽນ​ຄ່າ​ບໍ່​ແມ່ນ​ຕົວ​ເລກ​ເປັນ​ຕົວ​ເລກ, ວັນ​ທີ​ເປັນ​ຕົວ​ເລກ​ລຳ​ດັບ, TRUE ເປັນ 1, ອັນ​ອື່ນໆ​ເປັນ 0 (ສູນ)", "ad": "ແມ່ນ​ຄ່າ​ທີ່​ທ່ານ​ຕ້ອງ​ການ​ໃຫ້​ປ່ຽນ"}, "NA": {"a": "()", "d": "ສົ່ງຄືນຄ່າຄວາມຜິດພາດ #N/A (ຄ່າທີ່ໃຊ້ບໍ່ໄດ້)", "ad": ""}, "SHEET": {"a": "([value])", "d": "ສົ່ງ​ຄືນ​ເລກທີ​ຂອງ​ແຜ່ນ​ວຽກ​ທີ່​ອ້າງ​ອີງ", "ad": "​ແມ່ນ​ຊື່​ຂອງ​ແຜ່ນ​ວຽກ ຫຼືການ​ອ້າງ​ອີງ​ທີ່​ທ່ານ​ຕ້ອງການ​ຫາ​ເລກທີ​ຂອງ​ແຜ່ນ​ວຽກ.  ຖ້າ​ບໍ່​ໃສ່​ຄ່າ​ຫຍັງ ຈະ​ສົ່ງ​ຄືນ​ເລກທີ​ຂອງ​ແຜ່ນ​ວຽກ​ທີ່​ມີ​ຟັງ​ຊັນ​ນີ້"}, "SHEETS": {"a": "([reference])", "d": "ສົ່ງ​ຄືນ​ຈໍານວນ​ຂອງ​ແຜ່ນ​ວຽກ​ໃນ​ການ​ອ້າງ​ອີງ", "ad": "​ເປັນ​ການ​ອ້າງ​ອີງ​ທີ່​ທ່ານ​ຕ້ອງການ​ຮູ້​ຈໍາ​ນວນ​ແຜ່ນ​ວຽກ​ທີ່ຢູ່​ໃນ​ການ​ອ້າງ​ອີງ​ນັ້ນ.  ຖ້າ​ບໍ່​ໃສ່​ຄ່າ​ຫຍັງ ຈະ​ສົ່ງ​ຄືນ​ຈໍານວນ​ແຜ່ນ​ວຽກ​ໃນ​ສະໝຸດ​ວຽກ​ທີ່​ມີ​ຟັງ​ຊັນ​ນັ້ນ​ຢູ່"}, "TYPE": {"a": "(value)", "d": "ສະແດງຜົນຕົວເລກຖ້ວນແທນໃຫ້ກັບປະເພດຂໍ້ມູນຂອງຄ່າ: ຕົວເລກ = 1; ຂໍ້ຄວາມ = 2; ຄ່າໂລຈິກ = 4; ຄ່າຄວາມຜິດພາດ = 16; ອະເຣ  = 64; ຂໍ້ມູນປະສົມ = 128", "ad": "ສາມາດເປັນຄ່າໃດກໍໄດ້"}, "AND": {"a": "(logical1; [logical2]; ...)", "d": "ກວດກາເບິ່ງວ່າທຸກຂໍ້ພິສູດແມ່ນ TRUE, ແລະ ສົ່ງຄືນ TRUE ຖ້າທຸກຂໍ້ພິສູດແມ່ນ TRUE", "ad": "​ແມ່ນ 1 ຫາ 255 ໃນເງື່ອນ​ໄຂທີ່ທ່ານຕ້ອງການກວດສອບວ່າສາມາດເປັນໄດ້ທັງ TRUE ຫຼື FALSE ແລະສາມາດເປັນຄ່າຄວາມ​ຈິງ, ອາ​ເຣຍ໌, ຫຼືການອ້າງອີງ"}, "FALSE": {"a": "()", "d": "ສົ່ງກຄືນຄ່າຄວາມ​ຈິງ FALSE", "ad": ""}, "IF": {"a": "(logical_test; [value_if_true]; [value_if_false])", "d": "ກວດສອບວ່າເງື່ອນໄຂຖືກ​ຕອບ​ສະ​ໜອງ​ໄດ້ຫຼືບໍ່, ແລະສົ່ງຄືນຄ່າໜຶ່ງຖ້າເງື່ອນໄຂເປັນ TRUE ແລະສົ່ງຄືນອີກຄ່າໜຶ່ງ ຖ້າເງື່ອນໄຂເປັນ FALSE", "ad": "ເປັນຄ່າ ຫຼືຕົວປ່ຽນໃດໆທີ່ສາມາດຖືກປະເມີນເປັນ TRUE ຫຼື FALSE ໄດ້!ເປັນຄ່າທີ່ຈະຖືກສົ່ງຄືນ ຖ້າ Logical_test ເປັນ TRUE. ແຕ່ຖ້າບໍ່ໃສ່ຄ່າຫຍັງເລີຍ, ຄໍາ​ວ່າ TRUE ຈະຖືກສົ່ງຄືນມາແທນ. ທ່ານສາມາດໃຊ້ຟັງຊັນ IF ໄດ້ເຖິງ 7 ຟັງຊັນຊ້ອນກັນ!ເປັນຄ່າທີ່ຈະຖືກສົ່ງຄືນ ຖ້າ Logical_test ເປັນ FALSE. ແຕ່ຖ້າບໍ່ໃສ່ຄ່າຫຍັງໄວ້ ຄຳວ່າ FALSE ຈະຖືກສົ່ງຄືນມາແທນ"}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": "ກວດສອບວ່າໜຶ່ງ ຫຼືຫຼາຍກວ່າໜຶ່ງເງື່ອນໄຂແມ່ນກົງກັນ ແລະຕອບຄ່າທີ່ສອດຄ່ອງກັບເງື່ອນໄຂ TRUE ທຳອິດ", "ad": "ແມ່ນເປັນຄ່າ ຫຼືນິພົດໃດກໍໄດ້ທີ່ສາມາດຖືກປະເມີນເປັນ TRUE ຫຼື FALSE!ແມ່ນເປັນຄ່າທີ່ຕອບກັບມາຫາກ Logical_test ແມ່ນ TRUE"}, "IFERROR": {"a": "(value; value_if_error)", "d": "ສະ​ແດງ​ຜົນ ຄ່າ_ຖ້າ_ຜິດ​ພາດ ຖ້າ​ສຳ​ນວນ​ແມ່ນ​ຄວາມ​ຜິດ​ພາດ ແລະ​ຄ່າ​ຂອງ​ສຳ​ນວນ​ມັນ​ເອງ", "ad": " ນອກ​ຈາກວ່າ​ເປັນຄ່າ ຫຼື​ສຳ​ນວນ ຫຼື​ການ​ອ້າງ​ອີງ​ໃດ​ໜຶ່ງ!ແມ່ນ​ຄ່າ ຫຼື​ສຳ​ນວນ ຫຼື​ສົ່ງ​ຕໍ່ໃດ​ໜຶ່ງ"}, "IFNA": {"a": "(value; value_if_na)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ທີ່​ທ່ານ​ລະບຸ ຖ້າສໍານວນ​ໃຫ້​ຜົນ​ເປັນ#N/A, ບໍ່​ດັ່ງ​ນັ້ນ​ສົ່ງ​ຄືນ​ຜົນ​ຮັບ​ຂອງ​ສຳນວນ", "ad": "​​ເປັນ​ຄ່າ​ ຫຼືສຳນວນ ຫຼືການ​ອ້າງ​ອີງ​ໃດໆ​ກໍ່ຕາມ!​ເປັນ​ຄ່າ ຫຼືສຳນວນ ຫຼືການ​ອ້າງ​ອີງ​ໃດໆ​ກໍ່ຕາມ"}, "NOT": {"a": "(logical)", "d": "ປ່ຽນຄ່າຈາກ FALSE ໄປເປັນ TRUE, ຫຼື TRUE ໄປເປັນ FALSE", "ad": "ເປັນຄ່າ ຫຼືສະແດງສົມຜົນທີ່ສາມາດປະເມີນໄດ້ວ່າເປັນ TRUE ຫຼື FALSE"}, "OR": {"a": "(logical1; [logical2]; ...)", "d": "ກວດກາເບິ່ງວ່າມີທຸກຂໍ້ພິສູດແມ່ນ TRUE, ແລະສົ່ງຄືນ TRUE ຫຼື FALSE. ສົ່ງຄືນ ຄືນ FALSE ຖ້າຂໍ້ພິສູດທັງໝົດແມ່ນ FALSE", "ad": "ແມ່ນ 1 ຫາ 255 ເງື່ອນໄຂທີ່ທ່ານຕ້ອງການກວດສອບວ່າສາມາດເປັນໄດ້ທັງ TRUE ຫຼື FALSE"}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "ປະ​ເມີນ​ຜົນ​ການ​ສະ​ແດງ​ອອກ​ຕໍ່​ລາຍ​ການ​ຂອງ​ຄ່າ ແລະ ​ໃຫ້​ຜົນ​ສອດ​ຄ້ອງ​ກັບ​ຄ່າ​ຖືກ​ກັນ​ທຳ​ອິດ. ຖ້າ​ມີ​ອັນ​ບໍ່​ກົງ​ກັນ, ຄ່າ​ມາດ​ຕະ​ຖານ​ທາງ​ເລືອກ​ຖືກ​ກັບ​ຄືນ​ມາ", "ad": "ແມ່ນການ​ສະ​ແດງ​ອອກ​ທີ່​ຈະ​ໄດ້​ຮັບ​ການ​ປະ​ເມີນ​ຜົນ!ແມ່ນ​ຄ່າ​ທີ່​ຈະ​ຖືກ​ປຽບ​ທຽບ​ກັບ​ສຳ​ນວນ!ແມ່ນ​ຜົນ​ໄດ້​ຮັບ​ທີ່​ຈະ​ໄດ້​ຄືນ​ມາ ຖ້າ​ຄ່າ​ສອດ​ຄ້ອງ​ຖືກບ​ຜົນ​ການ​ສະ​ແດງ​ອອກ"}, "TRUE": {"a": "()", "d": "ສົ່ງຄືນຄ່າຄວາມ​ຈິງ TRUE", "ad": ""}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "ສົ່ງ​ຄືນ​ຜົນ​ຄ່າ​ຄວາມ​ຈິງ 'Exclusive OR' ຂອງ​ຂໍ້​ພິສູດ​ທັງ​ໝົດ", "ad": "​​ແມ່ນ​ເງື່ອນ​ໄຂ 1 ຫາ 254 ​ເງື່ອນ​ໄຂ​ທີ່​ທ່ານ​ຕ້ອງການ​ທົດ​ສອບ ​ເຊິ່ງສາມາດ​ເປັນ​ໄດ້​ທັງ TRUE ຫຼື FALSE​ແລະ​ສາມາດ​ເປັນ​ໄປ​ໄດ້​ທັງ​ຄ່າ​ຄວາມຈິງ, ອາ​ເຣຍ໌ ຫຼືກາ​ນາອ້າງ​ອີງ"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "ສົ່ງຄືນຂໍ້ຄວາມທີ່ຢູ່ກ່ອນການຈຳກັດຕົວອັກສອນ.", "ad": "ຂໍ້ຄວາມທີ່ທ່ານຕ້ອງການຊອກຫາຕົວຂັ້ນ.!ຕົວອັກສອນ ຫຼືສະຕຣິງທີ່ຈະໃຊ້ເປັນຕົວຂັ້ນ.!ສິ່ງ​ທີ່​ເກີດ​ຂຶ້ນທີ່ຕ້ອງການຂອງຕົວຂັ້ນ. ຄ່າເລີ່ມຕົ້ນແມ່ນ 1. ຊອກຫາຈຳນວນລົບຈາກສ່ວນທ້າຍ.!ຊອກຫາຂໍ້ຄວາມເພື່ອຈັບຄູ່ຕົວຂັ້ນ. ໂດຍຄ່າເລີ່ມຕົ້ນ, ການຈັບຄູ່ຕົວພິມນ້ອຍໃຫຍ່ມີຜົນຕ່າງກັນຈະ​ສໍາ​ເລັດ.!ບໍ່ວ່າຈະຈັບຄູ່ຕົວຂັ້ນກັບສ່ວນທ້າຍຂອງຂໍ້ຄວາມຫຼືບໍ່. ໂດຍຄ່າເລີ່ມຕົ້ນ, ຄ່າເຫຼົ່ານີ້ຈະບໍ່ກົງກັນ.!ຈະສົ່ງຄືນຖ້າບໍ່ພົບລາຍການທີ່ກົງກັນ. ໂດຍຄ່າເລີ່ມຕົ້ນ, #N/A ຈະຖືກສົ່ງຄືນ."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "ສົ່ງຄືນຂໍ້ຄວາມທີ່ຢູ່ຫຼັງຈາກການຈຳກັດຕົວອັກສອນ.", "ad": "ຂໍ້ຄວາມທີ່ທ່ານຕ້ອງການຊອກຫາຕົວຂັ້ນ.!ຕົວອັກສອນ ຫຼື ສະຕຣິງທີ່ຈະໃຊ້ເປັນຕົວຂັ້ນ.!ການປະກົດຕົວທີ່ຕ້ອງການຂອງຕົວຂັ້ນ. ຄ່າເລີ່ມຕົ້ນແມ່ນ 1. ຕົວເລກລົບຊອກຫາຈາກທ້າຍສຸດ.!ຊອກຫາຂໍ້ຄວາມເພື່ອຈັບຄູ່ຕົວຂັ້ນ. ໂດຍຄ່າເລີ່ມຕົ້ນ, ການຈັບຄູ່ຕົວພິມນ້ອຍໃຫຍ່ແມ່ນເຮັດແລ້ວ.!ບໍ່ວ່າຈະຈັບຄູ່ຕົວຂັ້ນກັບທ້າຍຂໍ້ຄວາມຫຼືບໍ່. ໂດຍຄ່າເລີ່ມຕົ້ນ, ພວກມັນບໍ່ກົງກັນ.!ຈະສົ່ງຄືນຖ້າບໍ່ພົບການຈັບຄູ່. ໂດຍຄ່າເລີ່ມຕົ້ນ, #N/A ຈະຖືກສົ່ງຄືນ."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "ແຍກຂໍ້ຄວາມອອກເປັນແຖວ ຫຼື ຖັນໂດຍໃຊ້ຕົວຂັ້ນ.", "ad": "ຂໍ້ຄວາມທີ່ຈະແຍກ!ຕົວອັກຂະລະ ຫຼື ສະຕຣິງທີ່ຈະແຍກຖັນ.!ຕົວອັກຂະລະ ຫຼື ສະຕຣິງທີ່ຈະແຍກແຖວ.!ຈະລະເວັ້ນເຊວທີ່ເປົ່າຫວ່າງຫຼືບໍ່. ຄ່າເລີ່ມຕົ້ນເປັນ FALSE.!ຊອກຫາຂໍ້ຄວາມເພື່ອຈັບຄູ່ຕົວຂັ້ນ. ໂດຍຄ່າເລີ່ມຕົ້ນ, ການຈັບຄູ່ຕົວພິມນ້ອຍໃຫຍ່ມີຜົນຕ່າງກັນຈະ​ສໍາ​ເລັດ.!ຄ່າທີ່ຈະໃຊ້ສຳລັບຊ່ອງວ່າງພາຍໃນ. ຕາມຄ່າເລີ່ມຕົ້ນ, ໃຊ້ #N/A ແລ້ວ."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "ຕັດແຖວ ຫຼື ເວັກເຕີຖັນ ຫຼັງຈໍານວນທີ່ກໍານົດໄວ້ຂອງຄ່າທີ່ລະບຸ.", "ad": "vector ຫຼືການອ້າງອີງເພື່ອຕັດ.!ຈຳນວນສູງສຸດຂອງຄ່າຕໍ່ແຖວ.!ຄ່າທີ່ຈະແພດ. ຄ່າເລີ່ມຕົ້ນແມ່ນ #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "ອະເຣແບບຊ້ອນກັນໃນແນວຕັ້ງເປັນອະເຣດຽວ.", "ad": "ອະເຣຫຼືການອ້າງອີງທີ່ຈະວາງຊ້ອນກັນ."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "ອະເຣແບບຊ້ອນກັນໃນແນວນອນເປັນອະເຣດຽວ.", "ad": "ອະເຣຫຼືການອ້າງອີງທີ່ຈະວາງຊ້ອນກັນ."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "ສົ່ງຄືນແຖວຈາກອະເຣ ຫຼືການອ້າງອີງ.", "ad": "ອະເຣ ຫຼື ການອ້າງອີງໃດໜຶ່ງທີ່ມີແຖວທີ່ຈະຕອບ.!ຈຳນວນແຖວທີ່ຈະຕອບ."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "ສົ່ງຄືນຖັນຈາກອະເຣ ຫຼືການອ້າງອີງ.", "ad": "ອະເຣ ຫຼື ການອ້າງອີງທີ່ມີຖັນເພື່ອໃຫ້ຕອບ.!ຈຳນວນຖັນທີ່ຈະຕອບກັບ."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "ສົ່ງຄືນອະເຣເປັນຖັນດຽວ.", "ad": "ອະເຣ ຫຼືການອ້າງອີງເພື່ອສົ່ງຄືນເປັນຖັນ.!ບໍ່ສົນໃຈບາງປະເພດຄ່າຫຼືບໍ່. ໂດຍຄ່າເລີ່ມຕົ້ນ, ຈະບໍ່ມີຄ່າໃດຖືກລະເລີຍ.!ສະແກນອະເຣໂດຍຖັນ. ໂດຍຄ່າເລີ່ມຕົ້ນ, ອະເຣຈະຖືກສະແກນເປັນແຖວ."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "ສົ່ງຄືນອະເຣເປັນແຖວດຽວ.", "ad": "ອະເຣ ຫຼື ການອ້າງອີງເພື່ອສົ່ງຄືນເປັນແຖວ.!ຈະລະເວັ້ນຄ່າບາງປະເພດຫຼືບໍ່. ຕາມຄ່າເລີ່ມຕົ້ນ, ຈະບໍ່ມີຄ່າໃດຖືກລະເວັ້ນ.!ສະແກນອະເຣຕາມຖັນ. ຕາມຄ່າເລີ່ມຕົ້ນ, ອະເຣຈະຖືກສະແກນເປັນແຖວ."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "ຕັດແຖວ ຫຼື ເວັກເຕີຖັນ ຫຼັງຈໍານວນທີ່ກໍານົດໄວ້ຂອງຄ່າທີ່ລະບຸ.", "ad": "vector ຫຼືການອ້າງອີງເພື່ອຕັດ.!ຈຳນວນສູງສຸດຂອງຄ່າຕໍ່ຖັນ.!ຄ່າທີ່ຈະແພດ. ຄ່າເລີ່ມຕົ້ນແມ່ນ #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": "ສົ່ງຄືນແຖວ ຫຼືຖັນຈາກອະເຣເລີ່ມຕົ້ນ ຫຼືສິ້ນສຸດ.", "ad": "ອະເຣທີ່ຈະໃຊ້ແຖວ ຫຼືຖັນ.!ຈຳນວນແຖວທີ່ຈະໃຊ້. ຄ່າລົບໃຊ້ຈາກທ້າຍຂອງອະເຣ.!ຈໍານວນຖັນທີ່ຈະໃຊ້. ຄ່າລົບມາ​ຈາກສ່ວນທ້າຍຂອງອະເຣ."}, "DROP": {"a": "(array, rows, [columns])", "d": "ວາງແຖວ ຫຼືຖັນຈາກອະເຣເລີ່ມຕົ້ນ ຫຼືສິ້ນສຸດ.", "ad": "ອະເຣທີ່ຈະວາງແຖວ ຫຼືຖັນ.!ຈຳນວນແຖວທີ່ຈະວາງ. ຄ່າລົບວາງຈາກທ້າຍຂອງອະເຣ.!ຈໍານວນຖັນທີ່ຈະວາງ. ຄ່າລົບມາ​ຈາກສ່ວນທ້າຍຂອງອະເຣ."}, "SEQUENCE": {"a": "(rows, [columns], [start], [step])", "d": "ຕອບຊຸດຕົວເລກ", "ad": "ຈຳນວນແຖວທີ່ຈະຕອບ!ຈຳນວນຖັນທີ່ຈະຕອບ!ຈຳນວນທຳອິດຂອງຊຸດ!ຈຳນວນທີ່ຈະເພີ່ມຕໍ່ຄ່າທີ່ຕາມມາໃນແຕ່ລະອັນໃນຊຸດ"}, "EXPAND": {"a": "(array, rows, [columns], [pad_with])", "d": "ຂະຫຍາຍແຖວທີ່ເປັນມິຕິພາບທີ່ກຳນົດໄວ້.", "ad": "ແຖວທີ່ຈະຂະຫຍາຍ.!ຈຳນວນແຖວໃນແຖວທີ່ຂະຫຍາຍ. ຖ້າຂາດຫາຍໄປ, ແຖວຈະບໍ່ຖືກຂະຫຍາຍ.!ຈຳນວນຖັນໃນແຖວທີ່ຂະຫຍາຍ. ຖ້າຂາດຫາຍໄປ, ຖັນຈະບໍ່ຖືກຂະຫຍາຍ.!ຄ່າທີ່ຈະແພດ. ຄ່າເລີ່ມຕົ້ນເປັນ #N/A."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "ສົ່ງຄືນຕຳແໜ່ງສຳພັນຂອງລາຍການໃນອະເຣ. ຕາມຄ່າເລີ່ມຕົ້ນ, ຕ້ອງລະບຸຄ່າທີ່ກົງກັນທຸກປະການ", "ad": "ເປັນຄ່າເພື່ອຊອກຫາ!ເປັນອະເຣ ຫຼື ຊ່ວງທີ່ຈະຊອກຫາ!ລະບຸວິທີຈັບຄູ່ lookup_value ກັບຄ່າໃນ lookup_array!ລະບຸໂໝດການຊອກຫາທີ່ຈະໃຊ້. ຕາມຄ່າເລີ່ມຕົ້ນ, ຈະມີການຊອກຫາຈາກທຳອິດຫາສຸດທ້າຍ"}, "FILTER": {"a": "(array, include, [if_empty])", "d": "ກັ່ນຕອງໄລຍະ ຫຼື ອະເຣໃດໜຶ່ງ", "ad": "ໄລຍະ ຫຼື ອະເຣເພື່ອກັ່ນຕອງ!ອະເຣຂອງ booleans ທີ່ TRUE ແທນທີ່ແຖວ ຫຼື ຖັນເພື່ອຮັກສາໄວ້!ຕອບກັບຫາກບໍ່ມີລາຍການຖືກຮັກສາໄວ້"}, "ARRAYTOTEXT": {"a": "(array, [format])", "d": "ຕອບກັບຕົວແທນຂໍ້ຄວາມຂອງອະເຣໃດໜຶ່ງ", "ad": "ອະເຣເພື່ອນຳສະເໜີເປັນຂໍ້ຄວາມ!ຮູບແບບຂອງຂໍ້ຄວາມ"}, "SORT": {"a": "(array, [sort_index], [sort_order], [by_col])", "d": "ຈັດຮຽງໄລຍະ ຫຼື ອະເຣ", "ad": "ໄລຍະ ຫຼື ອະເຣທີ່ຈະຈັດຮຽງ!ຈຳນວນລະບຸແຖວ ຫຼື ຖັນທີ່ຈະຈັດຮຽງຕາມ!ຈຳນວນທີ່ລະບຸລຳດັບການຈັດຮຽງທີ່ຕ້ອງການ; 1 ສຳລັບລຳດັບການຈັດຮຽງແບບນ້ອຍຫາໃຫຍ່ (ຄ່າເລີ່ມຕົ້ນ), -1 ສຳລັບລຳດັບການຈັດຮຽງແບບໃຫຍ່ຫານ້ອຍ!ຄ່າໂລຈິກທີ່ລະບຸທິດທາງການຈັດຮຽງທີ່ຕ້ອງການ: FALSE ເພື່ອຈັດຮຽງຕາມແຖວ (ຄ່າເລີ່ມຕົ້ນ), TRUE ເພື່ອຈັດຮຽງຕາມຖັນ"}, "SORTBY": {"a": "(array, by_array, [sort_order], ...)", "d": "ຈັດຮຽງໄລຍະ ຫຼື ອະເຣໂດຍອ້າງອີງຈາກຄ່າໃນໄລຍະ ຫຼື ອະເຣທີ່ກ່ຽວຂ້ອງ", "ad": "ໄລຍະ ຫຼື ອະເຣທີ່ຈັດຈັດຮຽງໃສ່!ຈຳນວນທີ່ລະບຸລຳດັບການຈັດຮຽງທີ່ຕ້ອງການ!ຈຳນວນທີ່ລະບຸລຳດັບການຈັດຮຽງທີ່ຕ້ອງການ; 1 ສຳລັບລຳດັບຈາກນ້ອຍຫາໃຫຍ່ (ຄ່າເລີ່ມຕົ້ນ), -1 ສຳລັບລຳດັບໃຫຍ່ຫານ້ອຍ"}, "GETPIVOTDATA": {"a": "(data_field; pivot_table; [field]; [item]; ...)", "d": "ແຍກຂໍ້ມູນທີ່ເກັບໃນຕາຕະລາງPivotTable", "ad": "ເປັນຊື່ຂອງຂົງເຂດທີ່ຕ້ອງການແຍກຂໍ້ມູນອອກມາ!ເປັນການອ້າງອີງໄປຍັງຫ້ອງ ຫຼືຊ່ວງຂອງຫ້ອງໃນຕາຕະລາງ PivotTable ທີ່ມີຂໍ້ມູນທີ່ທ່ານຕ້ອງການຮັບ!ເຂດຂໍ້ມູນທີ່ຈະອ້າງອີງເຖິງ!ລາຍການໃນຂົງເຂດທີ່ຈະອ້າງອີງເຖິງ"}, "IMPORTRANGE": {"a": "(spreadsheet_url, ໄລະຍະຂອງ String)", "d": "ນຳເຂົ້າໄລຍະຂອງຕາລາງຈາກສະເປຣດຊີດທີ່ລະບຸ."}}