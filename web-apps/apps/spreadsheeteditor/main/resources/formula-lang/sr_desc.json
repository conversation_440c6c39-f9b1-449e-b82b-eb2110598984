{"DATE": {"a": "(godina; mesec; dan)", "d": "Vraća broj koji predstavlja datum u kodu datum-vreme", "ad": "je broj od 1900 ili 1904 (u zavisnosti od datumskog sistema radne sveske) do 9999!je broj od 1 do 12 koji predstavlja mesec u godini!je broj od 1 do 31 koji predstavlja dan u mesecu"}, "DATEDIF": {"a": "(početni-datum; krajnji-datum; jedinica)", "d": "Vraća razliku između dva datuma (početni datum i krajnji datum), na osnovu specificiranog intervala (jedinice)", "ad": "je datum koji predstavlja prvi, ili početni datum datog perioda!je datum koji predstavlja poslednji, ili z<PERSON>, datum perioda!je vrsta informacija koje želite da se vrati"}, "DATEVALUE": {"a": "(datum_tekst)", "d": "Pretvara datum u obliku teksta u broj koji predstavlja datum u kodu datum-vreme", "ad": "je tekst koji predstavlja datum u Spreadsheet Editor-u formatu datuma, između 1/1/1900 ili 1/1/1904 (u zavisnosti od datumskog sistema radne sveske) i 12/31/9999"}, "DAY": {"a": "(serij<PERSON>_broj)", "d": "Vraća dan u mesecu, broj od 1 do 31.", "ad": "je broj u datumsko-vremenskom kodu koji koristi Spreadsheet Editor"}, "DAYS": {"a": "(krajnji_datum; početni_datum)", "d": "Vraća broj dana između dva datuma.", "ad": "početni_datum i krajnji_datum su dva datuma između kojih želite da znate broj dana!početni_datum i krajnji_datume su dva datuma između kojih želite da znate broj dana"}, "DAYS360": {"a": "(poč<PERSON><PERSON>_datum; krajnji_datum; [metod])", "d": "<PERSON>rać<PERSON> broj dana između dva datuma na osnovu 360-dnevnog kalendara (dvanaest 30-dnev<PERSON>h meseci)", "ad": "početni_datum i krajnji_datum su dva datuma između kojih želite da znate broj dana!početni_datum a krajnji_datum su dva datuma između kojih želite da znate broj dana!je logička vrednost koja određuje metod obračuna: U.S. (NASD) = NETAČNO ili izostavljeno; Evropski = TAČNO."}, "EDATE": {"a": "(poč<PERSON>ni_datum; meseci)", "d": "Vraća serijski broj datuma koji je navedeni broj meseci pre ili posle početnog datuma", "ad": "je serijski broj datuma koji predstavlja datum početka!je broj meseci pre ili posle početnog_datuma"}, "EOMONTH": {"a": "(poč<PERSON>ni_datum; meseci)", "d": "Vraća serijski broj poslednjeg dana meseca pre ili posle navedenog broja meseci", "ad": "je serijski broj datuma koji predstavlja datum početka!je broj meseci pre ili posle početnog_datuma"}, "HOUR": {"a": "(serij<PERSON>_broj)", "d": "<PERSON><PERSON><PERSON><PERSON> sat kao broj od 0 (12:00 AM) do 23 (11:00 PM).", "ad": "je broj u kodu datuma i vremena koji koristi Spreadsheet Editor, ili tekst u vremenskom formatu, kao <PERSON>to je 16:48:00 ili 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(datum)", "d": "Vraća ISO broj nedelje u godini za dati datum", "ad": "je datumsko-vremenski kod koji koristi Spreadsheet Editor za izračunavanje datuma i vremena"}, "MINUTE": {"a": "(serij<PERSON>_broj)", "d": "<PERSON><PERSON><PERSON><PERSON> minut, broj od 0 do 59.", "ad": "je broj u datumsko-vremenskom kodu koji koristi Spreadsheet Editor ili tekst u vremenskom formatu, kao što je 16:48:00 ili 4:48:00 PM"}, "MONTH": {"a": "(serij<PERSON>_broj)", "d": "<PERSON><PERSON><PERSON><PERSON> mesec, broj od 1 (januar) do 12 (decembar).", "ad": "je broj u datumsko-vremenskom kodu koji koristi Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(poč<PERSON><PERSON>_datum; krajnji_datum; [praznici])", "d": "Vraća broj celih radnih dana između dva datuma", "ad": "je serijski broj datuma koji predstavlja datum početka!je serijski broj datuma koji predstavlja krajnji datum!je opcioni skup jednog ili više serijskih brojeva datuma da se isključe iz radnog kalendara, kao što su državni i savezni praznici i plutajući praznici"}, "NETWORKDAYS.INTL": {"a": "(poč<PERSON><PERSON>_datum; krajnji_datum; [vikend]; [praznici])", "d": "Vraća broj celih radnih dana između dva datuma sa prilagođenim parametrima vikenda", "ad": "je serijski broj datuma koji predstavlja datum početka!je serijski broj datuma koji predstavlja krajnji datum!je broj ili niz koji određuje kada se vikendi javljaju!je opcioni skup jednog ili više serijskih brojeva datuma da se isključe iz radnog kalendara, kao što su državni i savezni praznici i plutajući praznici"}, "NOW": {"a": "()", "d": "Vraća trenutni datum i vreme formatirano kao datum i vreme.", "ad": ""}, "SECOND": {"a": "(serij<PERSON>_broj)", "d": "<PERSON><PERSON><PERSON><PERSON>, bro<PERSON> od 0 do 59.", "ad": "je broj u datumsko-vremenskom kodu koji koristi Spreadsheet Editor ili tekst u vremenskom formatu, kao <PERSON>to je 16:48:23 ili 4:48:47 PM"}, "TIME": {"a": "(sat; minuta; sekunda)", "d": "<PERSON><PERSON><PERSON><PERSON> sate, minute i sekunde date kao brojevi u serijski broj, formatiran sa formatom vremena", "ad": "je broj od 0 do 23 koji predstavlja sat!je broj od 0 do 59 koji predstavlja minut!je broj od 0 do 59 koji predstavlja sekundu"}, "TIMEVALUE": {"a": "(tekst_vremena)", "d": "Pretvara tekstualno vreme u serijski broj za vreme, broj od 0 (12:00:00 AM) do 0.999988426 (11:59:59 PM). Formatirajte broj sa formatom vremena nakon unosa formule", "ad": "je tekstualni niz koji daje vreme u bilo kom od Spreadsheet Editor vremenskih formata (informacije o datumu u nizu se ignorišu)"}, "TODAY": {"a": "()", "d": "Vraća trenutni datum formatiran kao datum.", "ad": ""}, "WEEKDAY": {"a": "(serij<PERSON>_broj; [tip_povratka])", "d": "Vraća broj od 1 do 7 koji identifikuje dan u nedelji za dati datum.", "ad": "je broj koji predstavlja datum!je broj: za nedelju=1 do subote=7, koristite 1; za ponedeljak=1 do nedelje=7, koristite 2; za ponedeljak=0 do nedelje=6, koristite 3"}, "WEEKNUM": {"a": "(serij<PERSON>_broj; [tip_povratka])", "d": "Vraća broj nedelje u godini", "ad": "je datum-vreme kod koji koristi Spreadsheet Editor za izračunavanje datuma i vremena!je broj (1 ili 2) koji određuje vrstu povratne vrednosti"}, "WORKDAY": {"a": "(početni_datum; dani; [praznici])", "d": "Vraća serijski broj datuma pre ili posle navedenog broja radnih dana", "ad": "je serijski broj datuma koji predstavlja datum početka!je broj dana koji nisu vikend i nisu praznik pre ili posle start_date!je opcioni niz jednog ili više serijskih brojeva datuma da se isključe iz radnog kalendara, kao što su državni i savezni praznici i plutajući praznici"}, "WORKDAY.INTL": {"a": "(početni_datum; dani; [vikend]; [praznici])", "d": "Vraća serijski broj datuma pre ili posle navedenog broja radnih dana sa prilagođenim parametrima vikenda", "ad": "je serijski broj datuma koji predstavlja datum početka!je broj dana koji nisu vikend i nisu praznik pre ili posle start_date!je broj ili niz koji određuje kada se vikendi javljaju!je opcioni niz jednog ili više serijskih brojeva datuma koji se isključuju iz radnog kalendara, kao što su državni i savezni praznici i plutajući praznici"}, "YEAR": {"a": "(serij<PERSON>_broj)", "d": "<PERSON><PERSON><PERSON><PERSON> god<PERSON>, ceo broj u opsegu od 1900-9999.", "ad": "je broj u datumsko-vremenskom kodu koji koristi Spreadsheet Editor"}, "YEARFRAC": {"a": "(po<PERSON><PERSON><PERSON>_datum; kraj<PERSON>_datum; [osnova])", "d": "Vraća godišnji udeo koji predstavlja broj celih dana između početni_datum i krajnji_datum", "ad": "je serijski broj datuma koji predstavlja datum početka!je serijski datum broj koji predstavlja krajnji datum!je vrsta osnove za brojanje dana koju treba koristiti"}, "BESSELI": {"a": "(x; n)", "d": "<PERSON>rać<PERSON> modifiko<PERSON>sselovu funkciju In(x)", "ad": "je vrednost na kojoj se procenjuje funkcija!je redosled Besselove funkcije"}, "BESSELJ": {"a": "(x; n)", "d": "Vraća Besselovu funkciju Jn(x)", "ad": "je vrednost na kojoj se procenjuje funkcija!je redosled Besselove funkcije"}, "BESSELK": {"a": "(x; n)", "d": "Vraća modifiko<PERSON>u Besselovu funkciju Kn(x)", "ad": "je vrednost na kojoj se procenjuje funkcija!je redosled funkcije"}, "BESSELY": {"a": "(x; n)", "d": "<PERSON>raća Besselovu funkciju Yn(x)", "ad": "je vrednost na kojoj se procenjuje funkcija!je redosled funkcije"}, "BIN2DEC": {"a": "(broj)", "d": "Konvertuje binarni broj u decimalni", "ad": "je binarni broj koji ž<PERSON> da konvertujete"}, "BIN2HEX": {"a": "(broj; [mesta])", "d": "Konvertuje binarni broj u heksadecimalni", "ad": "je binarni broj koji želite da konvertujete!je broj znakova za korišćenje"}, "BIN2OCT": {"a": "(broj; [mesta])", "d": "Konvertuje binarni broj u oktalni", "ad": "je binarni broj koji želite da konvertujete!je broj znakova za korišćenje"}, "BITAND": {"a": "(broj1; broj2)", "d": "<PERSON><PERSON><PERSON><PERSON> 'I' dva broja", "ad": "je decimalni prikaz binarnog broja koji želite da procenite!je decimalni prikaz binarnog broja koji želite da procenite"}, "BITLSHIFT": {"a": "(broj; pomak)", "d": "Vraća broj pomeran ulevo za pomak bitova", "ad": "je decimalni prikaz binarnog broja koji želite da procenite!je broj bitova za koji želite da pomerite broj ulevo"}, "BITOR": {"a": "(broj1; broj2)", "d": "<PERSON><PERSON><PERSON><PERSON> 'ILI' dva broja", "ad": "je decimalni prikaz binarnog broja koji želite da procenite!je decimalni prikaz binarnog broja koji želite da procenite"}, "BITRSHIFT": {"a": "(broj; pomak)", "d": "Vraća broj pomeran udesno za pomak bitova", "ad": "je decimalni prikaz binarnog broja koji želite da procenite!je broj bitova za koji želite da pomerite broj udesno"}, "BITXOR": {"a": "(broj1; broj2)", "d": "Vraća bitovski 'Ekskluzivno ILI' dva broja", "ad": "je decimalni prikaz binarnog broja koji želite da procenite!je decimalni prikaz binarnog broja koji želite da procenite"}, "COMPLEX": {"a": "(realan_broj; i_broj; [sufiks])", "d": "Konvertuje realne i imaginarne koeficijente u kompleksan broj", "ad": "je realni koeficijent kompleksnog broja!je imaginarni koeficijent kompleksnog broja!je sufiks za imaginarnu komponentu kompleksnog broja"}, "CONVERT": {"a": "(broj; iz_jedinica; u_jedinice)", "d": "Konvertuje broj iz jednog mernog sistema u drugi", "ad": "je vrednost u from_units da konvertujete!je jedinica za broj!je jedinica za rezultat"}, "DEC2BIN": {"a": "(broj; [mesta])", "d": "Konvertuje decimalni broj u binarni", "ad": "je decimalni ceo broj koji želite da konvertujete!je broj znakova koji se koriste"}, "DEC2HEX": {"a": "(broj; [mesta])", "d": "Konvertuje decimalni broj u heksadecimalni", "ad": "je decimalni ceo broj koji želite da konvertujete!je broj znakova koji se koriste"}, "DEC2OCT": {"a": "(broj; [mesta])", "d": "Konvertuje decimalni broj u oktalni", "ad": "je decimalni ceo broj koji želite da konvertujete!je broj znakova koji se koriste"}, "DELTA": {"a": "(broj1; [broj2])", "d": "Testira da li su dva broja jednaka", "ad": "je prvi broj!je drugi broj"}, "ERF": {"a": "(donja_granica; [gornja_granica])", "d": "Vraća grešku funkcije", "ad": "je donja granica za integraciju ERF!je gornja granica za integraciju ERF"}, "ERF.PRECISE": {"a": "(X)", "d": "Vraća grešku funkcije", "ad": "je donja granica za integraciju ERF.TAČNA"}, "ERFC": {"a": "(x)", "d": "Vraća komplementarnu grešku funkcije", "ad": "je donja granica za integraciju ERF"}, "ERFC.PRECISE": {"a": "(X)", "d": "Vraća komplementarnu grešku funkcije", "ad": "je donja granica za integraciju ERF.KOMPL.TAČNA"}, "GESTEP": {"a": "(broj; [korak])", "d": "Testira da li je broj veći od praga vrednosti", "ad": "je vrednost za testiranje u odnosu na korak!je vrednost praga"}, "HEX2BIN": {"a": "(broj; [mesta])", "d": "Konvertuje heksadecimalni broj u binarni", "ad": "je heksadecimalni broj koji želite da konvertujete!je broj znakova koji se koriste"}, "HEX2DEC": {"a": "(broj)", "d": "Konvertuje heksadecimalni broj u decimalni", "ad": "je heksadecimalni broj koji želite da konvertujete"}, "HEX2OCT": {"a": "(broj; [mesta])", "d": "Konvertuje heksadecimalni broj u oktalni", "ad": "je heksadecimalni broj koji želite da konvertujete!je broj znakova koji se koriste"}, "IMABS": {"a": "(kompleksni_broj)", "d": "Vraća apsolutnu vrednost (modul) kompleksnog broja", "ad": "je kompleksan broj za koji želite apsolutnu vrednost"}, "IMAGINARY": {"a": "(kompleksni_broj)", "d": "Vraća imaginarni koeficijent kompleksnog broja", "ad": "je kompleksni broj za koji želite imaginarni koeficijent"}, "IMARGUMENT": {"a": "(kompleksni_broj)", "d": "Vraća argument q, ugao izražen u radijanima", "ad": "je kompleksni broj za koji želite argument"}, "IMCONJUGATE": {"a": "(kompleksni_broj)", "d": "Vraća kompleksno konjugovanje kompleksnog broja", "ad": "je kompleksni broj za koji želite konjugat"}, "IMCOS": {"a": "(kompleksni_broj)", "d": "<PERSON><PERSON><PERSON><PERSON> kos<PERSON> kompleksnog broja", "ad": "je kompleksni broj za koji želite kosinus"}, "IMCOSH": {"a": "(kompleksni_broj)", "d": "Vraća hiperbolični kosinus kompleksnog broja", "ad": "je kompleksni broj za koji želite hiperbolički kosinus"}, "IMCOT": {"a": "(kompleksni_broj)", "d": "Vraća kotangens kompleksnog broja", "ad": "je kompleksni broj za koji želite kotangens"}, "IMCSC": {"a": "(kompleksni_broj)", "d": "Vraća kosekans kompleksnog broja", "ad": "je kompleksni broj za koji želite kosekans"}, "IMCSCH": {"a": "(kompleksni_broj)", "d": "Vraća hiperbolični kosekans kompleksnog broja", "ad": "je kompleksni broj za koji želite hiperbolički kosekans"}, "IMDIV": {"a": "(kompleksni_broj1; kompleksni_broj2)", "d": "Vraća količnik dva kompleksna broja", "ad": "je složeni brojnik ili dividenda!je složeni imenilac ili delilac"}, "IMEXP": {"a": "(kompleksni_broj)", "d": "Vraća eksponencijalni kompleksnog broja", "ad": "je kompleksni broj za koji želite eksponencijalni"}, "IMLN": {"a": "(kompleksni_broj)", "d": "Vraća prirodni logaritam kompleksnog broja", "ad": "je kompleksni broj za koji želite prirodni logaritam"}, "IMLOG10": {"a": "(kompleksni_broj)", "d": "Vraća logaritam po osnovi 10 kompleksnog broja", "ad": "je kompleksni broj za koji želite zajednički logaritam"}, "IMLOG2": {"a": "(kompleksni_broj)", "d": "Vraća logaritam po osnovi 2 kompleksnog broja", "ad": "je kompleksni broj za koji želite logaritam baze-2"}, "IMPOWER": {"a": "(kompleksni_broj; broj)", "d": "Vraća kompleksan broj na celobrojnu potenciju", "ad": "je kompleksni broj koji želite da podignete na stepen!je stepen na koju želite da podignete kompleksni broj"}, "IMPRODUCT": {"a": "(kompleksni_broj1; [kompleksni_broj2]; ...)", "d": "Vraća proizvod od 1 do 255 kompleksnih brojeva", "ad": "Inumber1, Inumber2,... su od 1 do 255 kompleksnih brojeva za množenje."}, "IMREAL": {"a": "(kompleksni_broj)", "d": "Vraća realni koeficijent kompleksnog broja", "ad": "je kompleksni broj za koji želite realni koeficijent"}, "IMSEC": {"a": "(kompleksni_broj)", "d": "Vraća sekans kompleksnog broja", "ad": "je kompleksni broj za koji želite sekant"}, "IMSECH": {"a": "(kompleksni_broj)", "d": "Vraća hiperbolični sekans kompleksnog broja", "ad": "je kompleksni broj za koji želite hiperbolički sekans"}, "IMSIN": {"a": "(kompleksni_broj)", "d": "Vraća sinus kompleksnog broja", "ad": "je kompleksni broj za koji želite sinus"}, "IMSINH": {"a": "(kompleksni_broj)", "d": "Vraća hiperbolični sinus kompleksnog broja", "ad": "je kompleksni broj za koji želite hiperbolički sinus"}, "IMSQRT": {"a": "(kompleksni_broj)", "d": "Vraća kvadratni koren kompleksnog broja", "ad": "je kompleksni broj za koji želite kvadratni koren"}, "IMSUB": {"a": "(kompleksni_broj1; kompleksni_broj2)", "d": "Vraća razliku dva kompleksna broja", "ad": "je kompleksni broj od kojeg treba oduzeti inumber2!je kompleksni broj koji treba oduzeti od inumber1"}, "IMSUM": {"a": "(kompleksni_broj1; [kompleksni_broj2]; ...)", "d": "Vraća zbir kompleksnih brojeva", "ad": "su od 1 do 255 složenih brojeva za dodavanje"}, "IMTAN": {"a": "(kompleksni_broj)", "d": "Vraća tangens kompleksnog broja", "ad": "je kompleksni broj za koji želite tangentu"}, "OCT2BIN": {"a": "(broj; [mesta])", "d": "Pretvara oktalni broj u binarni", "ad": "je oktalni broj koji želite da konvertujete!je broj znakova za korišćenje"}, "OCT2DEC": {"a": "(broj)", "d": "Pretvara oktalni broj u decimalni", "ad": "je oktalni broj koji želite da konvertujete"}, "OCT2HEX": {"a": "(broj; [mesta])", "d": "Pretvara oktalni broj u heksadecimalni", "ad": "je oktalni broj koji želite da konvertujete!je broj znakova za korišćenje"}, "DAVERAGE": {"a": "(baza_podataka; polje; kriterijum)", "d": "Izračunava prosečne vrednosti u koloni u listi ili bazi podataka koje zadovoljavaju navedene uslove", "ad": "je opseg ćelija koje čine listu ili bazu podataka. Baza podataka je lista srodnih podataka!je ili oznaka kolone u dvostrukim navodnicima ili broj koji predstavlja poziciju kolone na listi!je opseg ćelija koji sadrži uslove koje odredite. Opseg uključuje oznaku kolone i jednu ćeliju ispod oznake za uslov"}, "DCOUNT": {"a": "(baza_podataka; polje; kriterijum)", "d": "Broji ćelije koje sadrže brojeve u polju (koloni) zapisa u bazi podataka koje zadovoljavaju navedene uslove", "ad": "je opseg ćelija koje čine listu ili bazu podataka. Baza podataka je lista srodnih podataka! je ili oznaka kolone u dvostrukim navodnicima ili broj koji predstavlja poziciju kolone na listi!je opseg ćelija koji sadrži uslove koje odredite. Opseg uključuje oznaku kolone i jednu ćeliju ispod oznake za uslov"}, "DCOUNTA": {"a": "(baza_podataka; polje; kriterijum)", "d": "Broji ne-prazne ćelije u polju (koloni) zapisa u bazi podataka koje zadovoljavaju navedene uslove", "ad": "je opseg ćelija koje čine listu ili bazu podataka. Baza podataka je lista srodnih podataka!je ili oznaka kolone u dvostrukim navodnicima ili broj koji predstavlja poziciju kolone na listi!je opseg ćelija koji sadrži uslove koje odredite. Opseg uključuje oznaku kolone i jednu ćeliju ispod oznake za uslov"}, "DGET": {"a": "(baza_podataka; polje; kriterijum)", "d": "Izvlači iz baze podataka jedan zapis koji zadovoljava navedene uslove", "ad": "je opseg ćelija koje čine listu ili bazu podataka. Baza podataka je lista srodnih podataka!je ili oznaka kolone u dvostrukim navodnicima ili broj koji predstavlja poziciju kolone na listi!je opseg ćelija koji sadrži uslove koje odredite. Opseg uključuje oznaku kolone i jednu ćeliju ispod oznake za uslov"}, "DMAX": {"a": "(baza_podataka; polje; kriterijum)", "d": "Vraća najveći broj u polju (koloni) zapisa u bazi podataka koji zadovoljava navedene uslove", "ad": "je opseg ćelija koje čine listu ili bazu podataka. Baza podataka je lista srodnih podataka!je ili oznaka kolone u dvostrukim navodnicima ili broj koji predstavlja poziciju kolone na listi!je opseg ćelija koji sadrži uslove koje odredite. Opseg uključuje oznaku kolone i jednu ćeliju ispod oznake za uslov"}, "DMIN": {"a": "(baza_podataka; polje; kriterijum)", "d": "Vraća najmanji broj u polju (koloni) zapisa u bazi podataka koji zadovoljava navedene uslove", "ad": "je opseg ćelija koje čine listu ili bazu podataka. Baza podataka je lista srodnih podataka!je ili oznaka kolone u dvostrukim navodnicima ili broj koji predstavlja poziciju kolone na listi!je opseg ćelija koji sadrži uslove koje odredite. Opseg uključuje oznaku kolone i jednu ćeliju ispod oznake za uslov"}, "DPRODUCT": {"a": "(baza_podataka; polje; kriterijum)", "d": "Množi vrednosti u polju (koloni) zapisa u bazi podataka koje zadovoljavaju navedene uslove", "ad": "je opseg ćelija koje čine listu ili bazu podataka. Baza podataka je lista srodnih podataka!je ili oznaka kolone u dvostrukim navodnicima ili broj koji predstavlja poziciju kolone na listi!je opseg ćelija koji sadrži uslove koje odredite. Opseg uključuje oznaku kolone i jednu ćeliju ispod oznake za uslov"}, "DSTDEV": {"a": "(baza_podataka; polje; kriterijum)", "d": "Procena standardne devijacije na osnovu uzorka izabranih unosa u bazi podataka", "ad": "je opseg ćelija koje čine listu ili bazu podataka. Baza podataka je lista srodnih podataka!je ili oznaka kolone u dvostrukim navodnicima ili broj koji predstavlja poziciju kolone na listi!je opseg ćelija koji sadrži uslove koje odredite. Opseg uključuje oznaku kolone i jednu ćeliju ispod oznake za uslov"}, "DSTDEVP": {"a": "(baza_podataka; polje; kriterijum)", "d": "Izračunava standardnu devijaciju na osnovu cele populacije izabranih unosa u bazi podataka", "ad": "je opseg ćelija koje čine listu ili bazu podataka. Baza podataka je lista srodnih podataka!je ili oznaka kolone u dvostrukim navodnicima ili broj koji predstavlja poziciju kolone na listi!je opseg ćelija koji sadrži uslove koje odredite. Opseg uključuje oznaku kolone i jednu ćeliju ispod oznake za uslov"}, "DSUM": {"a": "(baza_podataka; polje; kriterijum)", "d": "Sabira brojeve u polju (koloni) zapisa u bazi podataka koje zadovoljavaju navedene uslove", "ad": "je opseg ćelija koje čine listu ili bazu podataka. Baza podataka je lista srodnih podataka!je ili oznaka kolone u dvostrukim navodnicima ili broj koji predstavlja poziciju kolone na listi!je opseg ćelija koji sadrži uslove koje odredite. Opseg uključuje oznaku kolone i jednu ćeliju ispod oznake za uslov"}, "DVAR": {"a": "(baza_podataka; polje; kriterijum)", "d": "Procena varijanse na osnovu uzorka izabranih unosa u bazi podataka", "ad": "je opseg ćelija koje čine listu ili bazu podataka. Baza podataka je lista srodnih podataka!je ili oznaka kolone u dvostrukim navodnicima ili broj koji predstavlja poziciju kolone na listi!je opseg ćelija koji sadrži uslove koje odredite. Opseg uključuje oznaku kolone i jednu ćeliju ispod oznake za uslov"}, "DVARP": {"a": "(baza_podataka; polje; kriterijum)", "d": "Izračunava varijansu na osnovu cele populacije izabranih unosa u bazi podataka", "ad": "je opseg ćelija koje čine listu ili bazu podataka. Baza podataka je lista srodnih podataka!je ili oznaka kolone u dvostrukim navodnicima ili broj koji predstavlja poziciju kolone na listi!je opseg ćelija koji sadrži uslove koje odredite. Opseg uključuje oznaku kolone i jednu ćeliju ispod oznake za uslov"}, "CHAR": {"a": "(broj)", "d": "Vraća karakter specificiran brojem koda iz skupa karaktera vašeg računara", "ad": "je broj između 1 i 255 koji određuje koji znak želite"}, "CLEAN": {"a": "(tekst)", "d": "Uklanja sve neštampane karaktere iz teksta", "ad": "je bilo koja informacija o radnom listu iz koje želite ukloniti znakove koji se ne mogu štampati"}, "CODE": {"a": "(tekst)", "d": "Vraća numerički kod za prvi karakter u tekstualnom nizu, u skupu karaktera koji koristi vaš računar", "ad": "je tekst za koji želite kod prvog znaka"}, "CONCATENATE": {"a": "(tekst1; [tekst2]; ...)", "d": "Spaja više tekstualnih nizova u jedan tekstualni niz", "ad": "su od 1 do 125 tekstualnih nizova koji se spajaju u jedan tekstualni niz i mogu biti tekstualni nizovi, brojevi ili reference sa jednom ćelijom"}, "CONCAT": {"a": "(tekst1; ...)", "d": "Konkatenira listu ili opseg tekstualnih nizova", "ad": "su od 1 do 254 tekstualnih nizova ili opsega koji se pridružuju jednom tekstualnom nizu"}, "DOLLAR": {"a": "(broj; [decimale])", "d": "Pretvara broj u tekst koristeći format valute", "ad": "je broj, referenca na ćeliju koja sadrži broj, ili formula koja se procenjuje na broj!je broj cifara desno od decimalne tačke. Broj se zaokružuje po potrebi; ako je izostavljen, Decimale = 2"}, "EXACT": {"a": "(tekst1; tekst2)", "d": "Proverava da li su dva tekstualna niza identična i vraća TRUE (TAČNO) ili FALSE (NETAČNO). EXACT razlikuje velika i mala slova", "ad": "je prvi tekstualni niz!je drugi tekstualni niz"}, "FIND": {"a": "(traži_tekst; unutar_teksta; [po<PERSON><PERSON><PERSON>_broj])", "d": "Vraća početnu poziciju jednog tekstualnog niza unutar drugog tekstualnog niza. FIND razlikuje velika i mala slova", "ad": "je tekst koji želite da pronađete. Koristite dvostruke navodnike (prazan tekst) za podudaranje sa prvim znakom u Unutar_teksta; džoker znakovi nisu dozvoljeni!je tekst koji sadrži tekst koji želite da pronađete!specifikuje znak na kome treba da započne pretragu. Prvi znak Unutar_teksta je znak broj 1. <PERSON><PERSON> je izostavljen, Početni_broj = 1"}, "FINDB": {"a": "(niz-1; niz-2; [početna_pozicija])", "d": "Pronalazi navedeni podniz (niz-1) unutar niza (niz-2) i namenjen je za jezike sa skupom dvos<PERSON><PERSON><PERSON> b<PERSON> (DBCS) kao što su japanski, kineski, korejski itd.", "ad": "je tekst koji želite da pronađete. Korist<PERSON> dvostr<PERSON> navodnike (prazan tekst) da se podudaraju sa prvim znakom u string-2; džoker znakovi nisu dozvoljeni!je tekst koji sadrži tekst koji želite da pronađete!specifikuje karakter na kome treba da započne pretragu. Prvi znak u string-2 je znak broj 1. <PERSON><PERSON> je izostavlje<PERSON>, start-pos = 1"}, "FIXED": {"a": "(broj; [decimale]; [bez_zareza])", "d": "Zaokružuje broj na navedeni broj decimala i vraća rezultat kao tekst sa ili bez zareza", "ad": "je broj koji želite da zaokružite i pretvorite u tekst!je broj cifara desno od decimalne tačke. Ako je izostavljen, Decimale = 2!je logička vrednost: ne prikazuju zareze u vraćenom tekstu = TAČNO; prikaži zareze u vraćenom tekstu = FALSE ili izostavljen"}, "LEFT": {"a": "(tekst; [broj_karak<PERSON>])", "d": "Vraća navedeni broj karaktera s početka tekstualnog niza", "ad": "je tekstualni niz koji sadrži znakove koje želite da izdvojite!određuje koliko znakova želite da LEVO izdvojite; 1 ako je izostavljen"}, "LEFTB": {"a": "(niz; [broj_karak<PERSON>])", "d": "Izvlači podniz iz navedenog niza počevši od levog karaktera i namenjen je za jezike koji koriste skup d<PERSON><PERSON><PERSON><PERSON> b<PERSON> (DBCS) kao što su japanski, kineski, korejski itd.", "ad": "je tekstualni niz koji sadrži znakove koje želite da izdvojite!specificira koliko znakova želite da LEVOB izdvoji; 1 ako je izostavljen"}, "LEN": {"a": "(tekst)", "d": "Vraća broj karaktera u tekstualnom nizu", "ad": "je tekst čiju dužinu želite da pronađete. Razmaci se računaju kao znakovi"}, "LENB": {"a": "(niz)", "d": "Analizira navedeni niz i vraća broj karaktera koje sadrži, a namenjen je za jezike koji koriste skup d<PERSON><PERSON><PERSON><PERSON> b<PERSON> (DBCS) kao što su japanski, kineski, korejski itd.", "ad": "je tekst čiju dužinu želite da pronađete. Razmaci se računaju kao znakovi"}, "LOWER": {"a": "(tekst)", "d": "Pretvara sva slova u tekstualnom nizu u mala slova", "ad": "je tekst koji želite da konvertujete u mala slova. Znakovi u tekstu koji nisu slova se ne menjaju"}, "MID": {"a": "(tekst; poč<PERSON>ni_broj; broj_karaktera)", "d": "Vraća karaktere iz sredine tekstualnog niza, prema početnoj poziciji i dužini", "ad": "je tekstualni niz iz kojeg želite da izvučete znakove!je položaj prvog znaka koji želite da izvučete. Prvi znak u tekstu je 1!određuje koliko znakova da se vrati iz teksta"}, "MIDB": {"a": "(niz; početna_pozicija; broj_karaktera)", "d": "Izvlači karaktere iz navedenog niza počevši od bilo koje pozicije i namenjen je za jezike koji koriste skup d<PERSON><PERSON><PERSON><PERSON> b<PERSON> (DBCS) kao što su japanski, kineski, korejski itd.", "ad": "je tekstualni niz iz kojeg želite da izvučete znakove!je položaj prvog znaka koji želite da izvučete. Prvi znak u Nizu je 1!određuje koliko znakova da se vrati iz Niza"}, "NUMBERVALUE": {"a": "(tekst; [decimalni_razdvojnik]; [razdvojnik_grupa])", "d": "Pretvara tekst u broj na nezavisni način od regionalnih postavki", "ad": "je niz koji predstavlja broj koji želite da konvertujete!je znak koji se koristi kao decimalni niz!je znak koji se koristi kao grupni separator u nizu"}, "PROPER": {"a": "(tekst)", "d": "Pretvara tekstualni niz u pravilan oblik; prvo slovo u svakoj reči u veliko, a sva ostala slova u mala", "ad": "je tekst stavljen u navodnike, formula koja vraća tekst, ili referenca na ćeliju koja sadrži tekst za delimično pisanje velikih slova"}, "REPLACE": {"a": "(stari_tekst; po<PERSON><PERSON><PERSON>_broj; broj_karak<PERSON>; novi_tekst)", "d": "Zamenjuje deo tekstualnog niza drugim tekstualnim nizom", "ad": "je tekst u kojem želite da zamenite neke znakove!je položaj karaktera u Stari_tekst koji želite da zamenite sa Novi_tekst!je broj znakova u Stari_tekst koji želite da zamenite!je tekst koji će zameniti znakove u Stari_tekst"}, "REPLACEB": {"a": "(niz-1; po<PERSON><PERSON>na_pozicija; broj_karaktera; niz-2)", "d": "Zamenjuje skup karaktera, na osnovu broja karaktera i početne pozicije koju navedete, novim skupom karaktera i namenjen je za jezike koji koriste skup d<PERSON><PERSON><PERSON><PERSON> b<PERSON> (DBCS) kao što su japanski, kineski, korejski itd.", "ad": "je tekst u kome želite da zamenite neke znakove!je položaj karaktera u Niz-1 koji želite da zamenite sa Niz-2!je broj znakova u Niz-1 koji želite da zamenite!je tekst koji će zameniti znakove u Niz-1"}, "REPT": {"a": "(tekst; broj_ponavljanja)", "d": "Ponavlja tekst određeni broj puta. Koristite PONOVI za popunjavanje ćelije određenim brojem instanci tekstualnog niza", "ad": "je tekst koji želite da ponovite!je pozitivan broj koji određuje koliko puta se ponavlja tekst"}, "RIGHT": {"a": "(tekst; [broj_karak<PERSON>])", "d": "Vraća navedeni broj karaktera s kraja tekstualnog niza", "ad": "je tekstualni niz koji sadrži znakove koje želite da izdvojite!specifikuje koliko znakova želite da izdvojite, 1 ako je izostavljen"}, "RIGHTB": {"a": "(niz; [broj_karak<PERSON>])", "d": "Izvlači podniz iz niza počevši od desnog karaktera, na osnovu navedenog broja karaktera i namenjen je za jezike koji koriste skup d<PERSON><PERSON><PERSON><PERSON> b<PERSON> (DBCS) kao što su japanski, kineski, korejski itd.", "ad": "je tekstualni niz koji sadrži znakove koje želite da izdvojite!specifikuje koliko znakova želite da izdvojite, 1 ako je izostavljen"}, "SEARCH": {"a": "(traži_tekst; unutar_teksta; [po<PERSON><PERSON><PERSON>_broj])", "d": "Vraća broj karaktera na kojem se prvi put nalazi određeni karakter ili tekstualni niz, čitajući s leva na desno (nije osetljivo na velika i mala slova)", "ad": "je tekst koji želite da pronađete. Možete koristiti ? i * džoker znakove; Koristite ~? i ~* da pronađete ? i * znakove!je tekst u kojem želite da tražite Traži_tekst!je broj karaktera u Unutar_teksta, računajući sa leve strane, na kojoj želite da počnete pretragu. Ako se izostavi, koristi se 1"}, "SEARCHB": {"a": "(niz-1; niz-2; [početna_pozicija])", "d": "Vraća lokaciju navedenog podniza u nizu i namenjen je za jezike koji koriste skup d<PERSON><PERSON><PERSON><PERSON> b<PERSON> (DBCS) kao što su japanski, kineski, korejski itd.", "ad": "je tekst koji želite da pronađete. Možete koristiti ? i * džoker znakove; Koristite ~? i ~* da pronađete ? i * znakove!je tekst u kojem želite da tražite Niz-1! je broj karaktera u Niz-2, ra<PERSON>unajući sa leve strane, na kojoj želite da počnete pretragu. <PERSON><PERSON> se izostavi, koristi se 1"}, "SUBSTITUTE": {"a": "(tekst; stari_tekst; novi_tekst; [broj_instance])", "d": "Zamenjuje postojeći tekst novim tekstom u tekstualnom nizu", "ad": "je tekst ili referenca na ćeliju koja sadrži tekst u kojem želite da zamenite karaktere!je postojeći tekst koji želite da zamenite. Ako se slučaj Stari_tekst ne poklapa sa slučajem teksta, ZAMENA neće zameniti tekst!je tekst kojim želite da zamenite Stari_tekst!određuje koju pojavu Stari_tekst želite da zamenite. Ako se izostavi, svaka instanca Stari_tekst se zamenjuje"}, "T": {"a": "(vrednost)", "d": "Proverava da li je vrednost tekst i vraća tekst ako jeste, ili vraća dvostruke navodnike (prazan tekst) ako nije", "ad": "je vrednost za testiranje"}, "TEXT": {"a": "(vrednost; format_teksta)", "d": "Pretvara vrednost u tekst u određenom brojevnom formatu", "ad": "je broj, formula koja se procenjuje na numeričku vrednost ili referenca na ćeliju koja sadrži numeričku vrednost!je format broja u tekstualnom obliku iz Kategorije polja na kartici Broj u dijalogu Formatiranje ćelija"}, "TEXTJOIN": {"a": "(razd<PERSON>jnik; ignoriši_prazno; tekst1; ...)", "d": "Konkatenira listu ili opseg tekstualnih nizova koristeći razdvojnik", "ad": "znak ili niz za umetanje izmedju svake tekstualne stavke!ako TAČNO (podrazumevano), ignoriše prazne ćelije!su od 1 do 252 tekstualnih nizova ili opsega koji se pridružuju"}, "TRIM": {"a": "(tekst)", "d": "Uklanja sve razmake iz tekstualnog niza osim jednostrukih razmaka između reči", "ad": "je tekst iz kojeg želite da se uklone razmaci"}, "UNICHAR": {"a": "(broj)", "d": "Vraća Unicode karakter referenciran datom numeričkom vrednošću", "ad": "je Unicode broj koji predstavlja znak"}, "UNICODE": {"a": "(tekst)", "d": "<PERSON><PERSON><PERSON><PERSON> broj (kodnu tačku) koja odgovara prvom karakteru teksta", "ad": "je znak za koji želite Unicode vrednost"}, "UPPER": {"a": "(tekst)", "d": "Pretvara tekstualni niz u sva velika slova", "ad": "je tekst koji želite pretvoriti u velika slova, referencu ili tekstualni niz"}, "VALUE": {"a": "(tekst)", "d": "Pretvara tekstualni niz koji predstavlja broj u broj", "ad": "je tekst stavljen pod navodnike ili referenca na ćeliju koja sadrži tekst koji želite da konvertujete"}, "AVEDEV": {"a": "(broj1; [broj2]; ...)", "d": "Vraća prosečnu vrednost apsolutnih devijacija tačaka podataka od njihove sredine. Argumenti mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve", "ad": "su od 1 do 255 argumenata za koje želite prosek apsolutnih odstupanja"}, "AVERAGE": {"a": "(broj1; [broj2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> (aritmetičku sredinu) s<PERSON><PERSON><PERSON> argumenat<PERSON>, koji mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve", "ad": "su od 1 do 255 numeričkih argumenata za koje želite prosek"}, "AVERAGEA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "<PERSON>rać<PERSON> prose<PERSON> (aritmetičku sredinu) s<PERSON><PERSON><PERSON> argumenata, evaluirajući tekst i FALSE (NETAČNO) u argumentima kao 0; TRUE (TAČNO) se evaluira kao 1. Argumenti mogu biti brojevi, imena, nizovi ili reference", "ad": "su od 1 do 255 argumenata za koje želite prosek"}, "AVERAGEIF": {"a": "(opseg; kriterijum; [prosečni_opseg])", "d": "Pronalazi prosek (aritmetičku sredinu) za ćelije specificirane određenim uslovom ili kriterijumom", "ad": "je opseg ćelija koje želite proceniti!je uslov ili kriterijum u obliku broja, izraza ili teksta koji definiše koje ćelije će se koristiti za pronalaženje proseka!su stvarne ćelije koje će se koristiti za pronalaženje proseka. Ako se izostavi, koriste se ćelije u opsegu"}, "AVERAGEIFS": {"a": "(prosečni_opseg; opseg_kriterijuma; kriterijum; ...)", "d": "Pronalazi prosek (aritmetičku sredinu) za ćelije specificirane određenim skupom uslova ili kriterijuma", "ad": "su stvarne ćelije koje će se koristiti za pronalaženje proseka.!je opseg ćelija koje želite proceniti za određeni uslov!je uslov ili kriterijum u obliku broja, izraza ili teksta koji definiše koje ćelije će se koristiti za pronalaženje proseka"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Vraća kumulativnu beta funkciju verovatnoće", "ad": "je vrednost između A i B na kojoj se procenjuje funkcija!je parametar za distribuciju i mora biti veći od 0!je parametar za distribuciju i mora biti veći od 0!je opciona donja granica za interval od x. Ako je izostavljen, A = 0!je opciona gornja granica intervala od x. <PERSON><PERSON> je izostavljen, B = 1"}, "BETAINV": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; alfa; beta; [A]; [B])", "d": "Vraća inverznu vrednost kumulativne beta funkcije verovatnoće (BETADIST)", "ad": "je verovatnoća povezana sa beta distribucijom!je parametar za distribuciju i mora biti veći od 0!je parametar za distribuciju i mora biti veći od 0!je opciona donja granica intervala x. Ako je izostavljen, A = 0!je opciona gornja granica intervala od x. Ako je izostavljen, B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; kumulativno; [A]; [B])", "d": "Vraća beta funkciju raspodele verovatnoće", "ad": "je vrednost između A i B na kojoj se procenjuje funkcija!je parametar za distribuciju i mora biti veći od 0!je parametar za distribuciju i mora biti veći od 0!je logička vrednost: za kumulativnu funkciju distribucije, koristiti TAČNO; za funkciju gustine verovat<PERSON>će, koristite NETAČNO!je opciona donja granica intervala x. Ako je izostavljen, A = 0! je opciona gornja granica intervala od x. Ako je izostavljen, B = 1"}, "BETA.INV": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; alfa; beta; [A]; [B])", "d": "Vraća inverznu vrednost kumulativne beta gustine verovatnoće (BETA.DIST)", "ad": "je verovatnoća povezana sa beta distribucijom!je parametar za distribuciju i mora biti veći od 0!je parametar za distribuciju i mora biti veći od 0!je opciona donja granica intervala x. Ako je izostavljen, A = 0!je opciona gornja granica intervala od x. Ako je izostavljen, B = 1"}, "BINOMDIST": {"a": "(broj_s; pokušaji; verovatnoća_s; kumulativno)", "d": "Vraća pojedinačnu binomnu raspodelu verovatnoće", "ad": "je broj uspeha u suđenjima!je broj nezavisnih suđenja!da li je verovatnoća uspeha na svakom suđenju!je logička vrednost: za funkciju kumulativne distribucije, koristite TAČNO; za funkciju mase ve<PERSON>, koristite NETAČNO"}, "BINOM.DIST": {"a": "(broj_s; pokušaji; verovatnoća_s; kumulativno)", "d": "Vraća pojedinačnu binomnu raspodelu verovatnoće", "ad": "je broj uspeha u suđenjima!je broj nezavisnih suđenja!da li je verovatnoća uspeha na svakom suđenju!je logička vrednost: za funkciju kumulativne distribucije, koristite TAČNO; za funkciju mase ve<PERSON>, koristite NETAČNO"}, "BINOM.DIST.RANGE": {"a": "(poku<PERSON><PERSON>; verovatnoća_s; broj_s; [broj_s2])", "d": "Vraća verovatnoću rezultata pokušaja koristeći binomnu raspodelu", "ad": "je broj nezavisnih suđenja!je verovatnoća uspeha na svakom suđenju!je broj uspeha u suđenjima!Ako je pod uslovom da ova funkcija vraća verovatnoću da će broj uspešnih suđenja biti između broj_s i broj_s2"}, "BINOM.INV": {"a": "(pokušaji; verovatnoća_s; alfa)", "d": "Vraća najmanju vrednost za koju je kumulativna binomna raspodela veća ili jednaka kriterijumu", "ad": "je broj Bernoullijevih suđenja!je verovatnoća uspeha na svakom suđenju, broj između 0 i 1 uključujući!je vrednost kriterijuma, broj između 0 i 1"}, "CHIDIST": {"a": "(x; stepeni_slobode)", "d": "<PERSON><PERSON><PERSON><PERSON> verovatnoću hi-kvadrat raspodele sa desnim repom", "ad": "je vrednost na kojoj želite da procenite distribuciju, nenegativan broj!je broj stepeni slobode, broj između 1 i 10^10, iskl<PERSON>č<PERSON><PERSON>ći 10^10"}, "CHIINV": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; step<PERSON>_slobode)", "d": "Vraća inverznu vrednost hi-kvadrat raspodele sa desnim repom", "ad": "je verovatnoća povezana sa hi-kvadratnom distribucijom, vrednost između 0 i 1 uključujući!je broj stepeni slobode, broj između 1 i 10^10, isključujući 10^10"}, "CHITEST": {"a": "(stvarni_opseg; očekivani_opseg)", "d": "Vraća test nezavisnosti: vrednost iz hi-kvadrat raspodele za statistiku i odgovarajuće stepene slobode", "ad": "je opseg podataka koji sadrži zapažanja za testiranje u odnosu na očekivane vrednosti!je opseg podataka koji sadrži odnos proizvoda redova i kolona ukupnog iznosa"}, "CHISQ.DIST": {"a": "(x; stepeni_slobode; kumula<PERSON>v<PERSON>)", "d": "<PERSON>rać<PERSON> verovatnoću hi-kvadrat raspodele sa levim repom", "ad": "je vrednost na kojoj želite da procenite distribuciju, nenegativan broj!je broj stepeni slobode, broj između 1 i 10^10, isključujući 10^10!je logička vrednost za funkciju da vrati: funkcija kumulativne distribucije = TAČNO; funkcija gustine verovat<PERSON>e = NETAČNO"}, "CHISQ.DIST.RT": {"a": "(x; stepeni_slobode)", "d": "<PERSON><PERSON><PERSON><PERSON> verovatnoću hi-kvadrat raspodele sa desnim repom", "ad": "je vrednost na kojoj želite da procenite distribuciju, nenegativan broj!je broj stepeni slobode, broj između 1 i 10^10, iskl<PERSON>č<PERSON><PERSON>ći 10^10"}, "CHISQ.INV": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; step<PERSON>_slobode)", "d": "Vraća inverznu vrednost hi-kvadrat raspodele sa levim repom", "ad": "je verovatnoća povezana sa hi-kvadratnom distribucijom, vrednost između 0 i 1 uključujući!je broj stepeni slobode, broj između 1 i 10^10, isključujući 10^10"}, "CHISQ.INV.RT": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; step<PERSON>_slobode)", "d": "Vraća inverznu vrednost hi-kvadrat raspodele sa desnim repom", "ad": "je verovatnoća povezana sa hi-kvadratnom distribucijom, vrednost između 0 i 1 uključujući!je broj stepeni slobode, broj između 1 i 10^10, isključujući 10^10"}, "CHISQ.TEST": {"a": "(stvarni_opseg; očekivani_opseg)", "d": "Vraća test nezavisnosti: vrednost iz hi-kvadrat raspodele za statistiku i odgovarajuće stepene slobode", "ad": "je opseg podataka koji sadrži zapažanja za testiranje u odnosu na očekivane vrednosti!je opseg podataka koji sadrži odnos proizvoda redova i kolona ukupnog iznosa"}, "CONFIDENCE": {"a": "(alfa; standardna_devijacija; veličina)", "d": "Vraća interval poverenja za srednju vrednost populacije, koristeći normalnu raspodelu", "ad": "je nivo značajnosti koji se koristi za izračunavanje nivoa pouzdanosti, broj veći od 0 i manji od 1!je standardna devijacija populacije za opseg podataka i pretpostavlja se da je poznat. Standard_dev mora biti veća od 0!je veličina uzorka"}, "CONFIDENCE.NORM": {"a": "(alfa; standardna_devijacija; veličina)", "d": "Vraća interval poverenja za srednju vrednost populacije, koristeći normalnu raspodelu", "ad": "je nivo značajnosti koji se koristi za izračunavanje nivoa pouzdanosti, broj veći od 0 i manji od 1!je standardna devijacija populacije za opseg podataka i pretpostavlja se da je poznat. Standard_dev mora biti veća od 0!je veličina uzorka"}, "CONFIDENCE.T": {"a": "(alfa; standardna_devijacija; veličina)", "d": "Vraća interval poverenja za srednju vrednost populacije, koristeći Studentovu T raspodelu", "ad": "je nivo značajnosti koji se koristi za izračunavanje nivoa pouzdanosti, broj veći od 0 i manji od 1!je standardna devijacija populacije za opseg podataka i pretpostavlja se da je poznat. Standard_dev mora biti veća od 0!je veličina uzorka"}, "CORREL": {"a": "(niz1; niz2)", "d": "Vraća koeficijent korelacije između dva skupa podataka", "ad": "je ćelijski opseg vrednosti. Vrednosti treba da budu brojevi, imena, nizovi ili reference koje sadrže brojeve!je drugi opseg ćelija vrednosti. Vrednosti treba da budu brojevi, imena, nizovi ili reference koje sadrže brojeve"}, "COUNT": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Broji broj ćelija u opsegu koje sadrže brojeve", "ad": "su od 1 do 255 argumenata koji mogu da sadrže ili se odnose na različite vrste podataka, ali se računaju samo brojevi"}, "COUNTA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Broji broj ćelija u opsegu koje nisu prazne", "ad": "su od 1 do 255 argumenata koji predstavljaju vrednosti i ćelije koje želite da brojite. Vrednosti mogu biti bilo koja vrsta informacija"}, "COUNTBLANK": {"a": "(opseg)", "d": "Broji broj praznih ćelija u određenom opsegu ćelija", "ad": "je opseg iz kojeg želite da brojite prazne ćelije"}, "COUNTIF": {"a": "(opseg; kriterijum)", "d": "B<PERSON>ji broj ćelija unutar opsega koje ispunjavaju dati uslov", "ad": "je opseg ćelija iz kojih želite da brojite neprazne ćelije!je uslov u obliku broja, izraza ili teksta koji definiše koje ćelije će se brojati"}, "COUNTIFS": {"a": "(opseg_kriterijuma; kriterijum; ...)", "d": "B<PERSON>ji broj <PERSON><PERSON> određ<PERSON>h skupom uslova ili kriterijuma", "ad": "je opseg ćelija koje želite proceniti za određeno stanje!je uslov u obliku broja, izraza ili teksta koji definiše koje ćelije će se brojati"}, "COVAR": {"a": "(niz1; niz2)", "d": "<PERSON><PERSON><PERSON><PERSON>, prosečnu vrednost proizvoda odstupanja za svaki par podataka u dva skupa podataka", "ad": "je prvi opseg ćelija celih brojeva i moraju biti brojevi, nizovi ili reference koje sadrže brojeve!je drugi opseg ćelija celih brojeva i mora biti brojevi, nizovi ili reference koje sadrže brojeve"}, "COVARIANCE.P": {"a": "(niz1; niz2)", "d": "Vraća populacionu k<PERSON>, prosečnu vrednost proizvoda odstupanja za svaki par podataka u dva skupa podataka", "ad": "je prvi opseg ćelija celih brojeva i moraju biti brojevi, nizovi ili reference koje sadrže brojeve!je drugi opseg ćelija celih brojeva i mora biti brojevi, nizovi ili reference koje sadrže brojeve"}, "COVARIANCE.S": {"a": "(niz1; niz2)", "d": "Vraća uzorkova<PERSON>, prosečnu vrednost proizvoda odstupanja za svaki par podataka u dva skupa podataka", "ad": "je prvi opseg ćelija celih brojeva i moraju biti brojevi, nizovi ili reference koje sadrže brojeve!je drugi opseg ćelija celih brojeva i mora biti brojevi, nizovi ili reference koje sadrže brojeve"}, "CRITBINOM": {"a": "(pokušaji; verovatnoća_s; alfa)", "d": "Vraća najmanju vrednost za koju je kumulativna binomna raspodela veća ili jednaka kriterijumu", "ad": "je broj Bernoullijevih suđenja!je verovatnoća uspeha na svakom suđenju, broj između 0 i 1 uključujući!je vrednost kriterijuma, broj između 0 i 1"}, "DEVSQ": {"a": "(broj1; [broj2]; ...)", "d": "Vraća zbir kvadrata odstupanja tačaka podataka od njihove uzorkovane srednje vrednosti", "ad": "su od 1 do 255 argumenata, ili niz ili referenca niza, na kojoj želite da ODSTUPANJEKV izračuna"}, "EXPONDIST": {"a": "(x; lambda; kumulativno)", "d": "Vraća eksponencijalnu raspodelu", "ad": "je vrednost funkcije, nenegativan broj!je vrednost parametra, pozitivan broj!je logička vrednost za funkciju da se vrati: kumulativna funkcija distribucije = TAČNO; funkcija gustine verov<PERSON> = NETAČNO"}, "EXPON.DIST": {"a": "(x; lambda; kumulativno)", "d": "Vraća eksponencijalnu raspodelu", "ad": "je vrednost funkcije, nenegativan broj!je vrednost parametra, pozitivan broj!je logička vrednost za funkciju da se vrati: kumulativna funkcija distribucije = TAČNO; funkcija gustine verov<PERSON> = NETAČNO"}, "FDIST": {"a": "(x; stepeni_slobode1; stepeni_slobode2)", "d": "Vraća (desno-repnu) F verovatnoću raspodele (stepen različitosti) za dva skupa podataka", "ad": "je vrednost na kojoj se procenjuje funkcija, nenegativan broj!je broji<PERSON><PERSON> stepeni slobode, broj između 1 i 10^10, iskl<PERSON><PERSON><PERSON><PERSON>ći 10^10!je imeni<PERSON><PERSON> stepeni slobode, broj između 1 i 10^10, iskl<PERSON><PERSON><PERSON><PERSON>ći 10^10"}, "FINV": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; stepeni_slobode1; stepeni_slobode2)", "d": "Vraća inverznu vrednost (desno-repne) F verovatnoće raspodele: ako je p = FDIST(x,...), tada je FINV(p,...) = x", "ad": "je verovatnoća povezana sa F kumulativnom distribucijom, broj između 0 i 1 uključujući!je broji<PERSON>c stepeni slobode, broj između 1 i 10^10, isključujući 10^10!je imeni<PERSON><PERSON> stepeni slobode, broj između 1 i 10^10, iskl<PERSON>č<PERSON><PERSON>ći 10^10"}, "FTEST": {"a": "(niz1; niz2)", "d": "Vraća rezultat F-testa, dvostruku verovatnoću da varijanse u Niz1 i Niz2 nisu značajno različite", "ad": "je prvi niz ili opseg podataka i mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve (praznine se zanemaruju)!je drugi niz ili opseg podataka i mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve (praznine se zanemaruju)"}, "F.DIST": {"a": "(x; stepeni_slobode1; stepeni_slobode2; kumula<PERSON>v<PERSON>)", "d": "Vraća (levo-repnu) F verovatnoću raspodele (stepen različitosti) za dva skupa podataka", "ad": "je vrednost na kojoj se procenjuje funkcija, nenegativan broj!je broji<PERSON><PERSON> stepeni slobode, broj između 1 i 10^10, isk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i 10^10!je imeni<PERSON><PERSON> stepeni slobode, broj između 1 i 10^10, iskl<PERSON>ču<PERSON>ći 10^10!je logička vrednost za funkciju da vrati: funkcija kumulativne distribucije = TAČNO; funkcija gustine verovatnoće = NETAČNO"}, "F.DIST.RT": {"a": "(x; stepeni_slobode1; stepeni_slobode2)", "d": "Vraća (desno-repnu) F verovatnoću raspodele (stepen različitosti) za dva skupa podataka", "ad": "je vrednost na kojoj se procenjuje funkcija, nenegativan broj!je broji<PERSON><PERSON> stepeni slobode, broj između 1 i 10^10, iskl<PERSON><PERSON><PERSON><PERSON>ći 10^10!je imeni<PERSON><PERSON> stepeni slobode, broj između 1 i 10^10, iskl<PERSON><PERSON><PERSON><PERSON>ći 10^10"}, "F.INV": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; stepeni_slobode1; stepeni_slobode2)", "d": "Vraća inverznu vrednost (levo-repne) F verovatnoće raspodele: ako je p = F.DIST(x,...), tada je F.INV(p,...) = x", "ad": "je verovatnoća povezana sa F kumulativnom distribucijom, broj između 0 i 1 uključujući!je broji<PERSON><PERSON> stepeni slobode, broj između 1 i 10^10, isključujući 10^10!je imenitelj stepeni slobode, broj između 1 i 10^10, iskl<PERSON>č<PERSON><PERSON>ći 10^10"}, "F.INV.RT": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; stepeni_slobode1; stepeni_slobode2)", "d": "Vraća inverznu vrednost (desno-repne) F verovatnoće raspodele: ako je p = F.DIST.RT(x,...), tada je F.INV.RT(p,...) = x", "ad": "je verovatnoća povezana sa F kumulativnom distribucijom, broj između 0 i 1 uključujući!je broji<PERSON>c stepeni slobode, broj između 1 i 10^10, isključujući 10^10!je imeni<PERSON><PERSON> stepeni slobode, broj između 1 i 10^10, iskl<PERSON>č<PERSON><PERSON>ći 10^10"}, "F.TEST": {"a": "(niz1; niz2)", "d": "Vraća rezultat F-testa, dvostruku verovatnoću da varijanse u Niz1 i Niz2 nisu značajno različite", "ad": "je prvi niz ili opseg podataka i mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve (praznine se zanemaruju)!je drugi niz ili opseg podataka i mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve (praznine se zanemaruju)"}, "FISHER": {"a": "(x)", "d": "Vraća Fišerovu transformaciju", "ad": "je vrednost za koju želite transformaciju, broj između -1 i 1, isključujući -1 i 1"}, "FISHERINV": {"a": "(y)", "d": "Vraća inverznu vrednost Fišerove transformacije: ako je y = FIŠER(x), tada je FIŠERINV(y) = x", "ad": "je vrednost za koju želite da izvršite obrnuto od transformacije"}, "FORECAST": {"a": "(x; poznate_y; poznate_x)", "d": "Izračunava ili predviđa buduću vrednost duž linearne tendencije koristeći postojeće vrednosti", "ad": "je tačka podataka za koju želite da predvidite vrednost i mora biti numerička vrednost!je zavisni niz ili opseg numeričkih podataka!je nezavisni niz ili opseg numeričkih podataka. Varijansa Known_x-a ne sme biti nula"}, "FORECAST.ETS": {"a": "(cil<PERSON>i_datum; vrednosti; vremenska_linija; [sezonalnost]; [dovr<PERSON><PERSON><PERSON>_podataka]; [agregacija])", "d": "Vraća predviđenu vrednost za određeni budući ciljni datum koristeći metodu eksponencijalnog izglađivanja.", "ad": "je tačka podataka za koju Spreadsheet Editor predviđa vrednost. Trebalo bi da nastavi sa obrascem vrednosti u vremenskoj liniji.!je niz ili opseg numeričkih podataka koje predviđate.!je nezavisni niz ili opseg numeričkih podataka. Datumi u vremenskoj liniji moraju imati dosledan korak između njih i ne mogu biti nula.!je opciona numerička vrednost koja označava dužinu sezonskog obrasca. Podrazumevana vrednost 1 označava sezonalnost koja se automatski detektuje.!je opciona vrednost za rukovanje vrednostima koje nedostaju. Podrazumevana vrednost 1 zamenjuje vrednosti koje nedostaju interpolacijom, a 0 ih zamenjuje nulama.!je opciona numerička vrednost za agregiranje više vrednosti sa istom vremenskom oznakom. <PERSON><PERSON> je prazno, Spreadsheet Editor uprosečuje vrednosti"}, "FORECAST.ETS.CONFINT": {"a": "(cil<PERSON>i_datum; vrednosti; vremenska_linija; [nivo_pouzdanosti]; [sezonalnost]; [dovr<PERSON><PERSON><PERSON>_podataka]; [agregacija])", "d": "Vraća interval pouzdanosti za predviđenu vrednost na određenom ciljnog datumu.", "ad": "je tačka podataka za koju Spreadsheet Editor predviđa vrednost. Trebalo bi da nastavi obrazac vrednosti u vremenskoj liniji.!je niz ili opseg numeričkih podataka koje predviđate.!je nezavisni niz ili opseg numeričkih podataka. Datumi u vremenskoj liniji moraju imati dosledan korak između njih i ne mogu biti nula.!je broj između 0 i 1 koji pokazuje nivo pouzdanosti za izračunati interval pouzdanosti. Osnovna vrednost je .95.!je opciona numerička vrednost koja označava dužinu sezonskog obrasca. Podrazumevana vrednost 1 označava sezonalnost koja se automatski detektuje.!je opciona vrednost za rukovanje vrednostima koje nedostaju. Podrazumevana vrednost 1 zamenjuje vrednosti koje nedostaju interpolacijom, a 0 ih zamenjuje nulama.!je opciona numerička vrednost za agregiranje više vrednosti sa istom vremenskom oznakom. Ako je prazno, Spreadsheet Editor u uprosečuje vrednosti"}, "FORECAST.ETS.SEASONALITY": {"a": "(vrednosti; vremenska_linija; [dovr<PERSON><PERSON><PERSON>_podataka]; [agregacija])", "d": "Vraća dužinu ponavljajućeg obrasca koji aplikacija detektuje za određeni niz vremenskih podataka.", "ad": "je niz ili opseg numeričkih podataka koje predviđate.!je nezavisni niz ili opseg numeričkih podataka. Datumi u vremenskoj liniji moraju imati dosledan korak između njih i ne mogu biti nula.!je opciona vrednost za rukovanje vrednostima koje nedostaju. Podrazumevana vrednost 1 zamenjuje vrednosti koje nedostaju interpolacijom, a 0 ih zamenjuje nulama.!je opciona numerička vrednost za agregiranje više vrednosti sa istom vremenskom oznakom. <PERSON><PERSON> je prazno, Spreadsheet Editor uprosečuje vrednosti"}, "FORECAST.ETS.STAT": {"a": "(vrednosti; vremenska_linija; tip_statistike; [sezonalnost]; [dovr<PERSON><PERSON><PERSON>_podataka]; [agregacija])", "d": "Vraća zahtevanu statistiku za prognozu.", "ad": "je niz ili opseg numeričkih podataka koje predviđate.!je nezavisni niz ili opseg numeričkih podataka. Datumi u vremenskoj liniji moraju imati dosledan korak između njih i ne mogu biti nula.!je broj između 1 i 8, što ukazuje na to koja statistika Spreadsheet Editor će se vratiti za izračunatu prognozu.!je opciona numerička vrednost koja označava dužinu sezonskog obrasca. Podrazumevana vrednost 1 označava sezonalnost koja se automatski detektuje.!je opciona vrednost za rukovanje nedostajućim vrednostima. Podrazumevana vrednost 1 zamenjuje vrednosti koje nedostaju interpolacijom, a 0 ih zamenjuje nulama.!je opciona numerička vrednost za agregiranje više vrednosti sa istim vremenskim pečatom. <PERSON><PERSON> je prazno, Spreadsheet Editor u uprosečuje vrednosti"}, "FORECAST.LINEAR": {"a": "(x; poznate_y; poznate_x)", "d": "Izračunava ili predviđa buduću vrednost duž linearne tendencije koristeći postojeće vrednosti", "ad": "je tačka podataka za koju želite da predvidite vrednost i mora biti numerička vrednost!je zavisni niz ili opseg numeričkih podataka!je nezavisni niz ili opseg numeričkih podataka. Varijansa Known_x ne sme biti nula"}, "FREQUENCY": {"a": "(niz_podataka; niz_intervala)", "d": "Izračunava koliko često se vrednosti pojavljuju unutar opsega vrednosti i zatim vraća vertikalni niz brojeva koji ima jedan element više od niza intervala", "ad": "je niz ili referenca na skup vrednosti za koje želite da brojite frekvencije (praznine i tekst se ignorišu)!je niz ili referenca na intervale u koje želite da grupišete vrednosti u niz_podataka"}, "GAMMA": {"a": "(x)", "d": "Vraća vrednost Gamma funkcije", "ad": "je vrednost za koju želite da izračunate Gamma"}, "GAMMADIST": {"a": "(x; alfa; beta; kumulativno)", "d": "Vraća gamma raspodelu", "ad": "je vrednost na kojoj želite da procenite distribuciju, nenegativan broj!je parametar za distribuciju, pozitivan broj!je parametar za distribuciju, pozitivan broj. Ako je beta = 1, GAMMADIST vraća standardnu gama distribuciju!je logička vrednost: vratiti kumulativnu funkciju distribucije = TAČNO; vratiti funkciju mase verovatnoće = NETAČNO ili izostavljeno"}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulativno)", "d": "Vraća gamma raspodelu", "ad": "je vrednost na kojoj želite da procenite distribuciju, nenegativan broj!je parametar za distribuciju, pozitivan broj!je parametar za distribuciju, pozitivan broj. Ako je beta = 1, GAMMA.DIST vraća standardnu gama distribuciju!je logička vrednost: vratite kumulativnu funkciju distribucije = TAČNO; vrati funkciju mase verovatnoće = NETAČNO ili izostavljeno"}, "GAMMAINV": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; alfa; beta)", "d": "Vraća inverznu vrednost kumulativne gamma raspodele: ako je p = GAMMADIST(x,...), tada je GAMMAINV(p,...) = x", "ad": "je verovatnoća povezana sa gama distribucijom, broj između 0 i 1, uk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>!je parametar za distribuciju, pozitivan broj!je parametar za distribuciju, pozitivan broj. Ako je beta = 1, GAMMAINV vraća inverznu od standardne gama distribucije"}, "GAMMA.INV": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; alfa; beta)", "d": "Vraća inverznu vrednost kumulativne gamma raspodele: ako je p = GAMMA.DIST(x,...), tada je GAMMA.INV(p,...) = x", "ad": "je verovatnoća povezana sa gama distribucijom, broj između 0 i 1, uk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>!je parametar za distribuciju, pozitivan broj!je parametar za distribuciju, pozitivan broj. Ako je beta = 1, GAMA.INV vraća inverznu od standardne gama distribucije"}, "GAMMALN": {"a": "(x)", "d": "Vraća prirodni logaritam gamma funkcije", "ad": "je vrednost za koju želite da izračunate GAMALN, pozitivan broj"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Vraća prirodni logaritam gamma funkcije", "ad": "je vrednost za koju želite da izračunate GAMALN.TAČNO, pozitivan broj"}, "GAUSS": {"a": "(x)", "d": "Vraća 0,5 manje od standardne normalne kumulativne raspodele", "ad": "je vrednost za koju želite distribuciju"}, "GEOMEAN": {"a": "(broj1; [broj2]; ...)", "d": "Vraća geometrijsku sredinu niza ili opsega pozitivnih numeričkih podataka", "ad": "su od 1 do 255 brojeva ili imena, nizova ili referenci koje sadrže brojeve za koje želite srednju vrednost"}, "GROWTH": {"a": "(poznate_y; [poznate_x]; [nove_x]; [konstanta])", "d": "Vraća brojeve u eksponencijalnom trendu rasta koji odgovaraju poznatim tačkama podataka", "ad": "je skup y-vrednosti koje već znate u odnosu y = b*m^x, niz ili opseg pozitivnih brojeva!je opcioni skup x-vrednosti koje možda već znate u odnosu y = b*m^x, niz ili opseg iste veličine kao Poznate_y!su nove x-vrednosti za koje želite da RAST vrati odgovarajuće y-vrednosti!je logička vrednost: konstanta b se izračunava normalno ako je Konst = TAČNO; b je podešen jednak 1 ako je Konst = NETAČNO ili izostavljeno"}, "HARMEAN": {"a": "(broj1; [broj2]; ...)", "d": "Vraća harmonijsku sredinu skupa pozitivnih brojeva: recipročnu vrednost aritmetičke sredine recipročnih vrednosti", "ad": "su od 1 do 255 brojeva ili imena, nizova ili referenci koje sadrže brojeve za koje želite harmoničnu sredinu"}, "HYPGEOM.DIST": {"a": "(uzorak_s; broj_uzoraka; populacija_s; broj_populacije; kumulativno)", "d": "Vraća hipergeometrijsku raspodelu", "ad": "je broj uspeha u uzorku!je veličina uzorka!je broj uspeha u populaciji!je veličina populacije!je logička vrednost: za kumulativnu funkciju distribucije, koristite TAČNO; za funkciju gustine <PERSON>, koristite NETAČNO"}, "HYPGEOMDIST": {"a": "(uzorak_s; broj_uzoraka; populacija_s; broj_populacije)", "d": "Vraća hipergeometrijsku raspodelu", "ad": "je broj uspeha u uzorku!je veličina uzorka!je broj uspeha u populaciji!je veličina populacije"}, "INTERCEPT": {"a": "(poznate_y; poznate_x)", "d": "Izračunava tačku na kojoj će se linija preseći sa y-osom koristeći liniju najbolje prilagođene regresije nacrtane kroz poznate x-vrednosti i y-vrednosti", "ad": "je zavisni skup zapažanja ili podataka i mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve!je nezavisan skup zapažanja ili podataka i mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve"}, "KURT": {"a": "(broj1; [broj2]; ...)", "d": "Vraća kurtosis skupa podataka", "ad": "su od 1 do 255 brojeva ili imena, nizova ili referenci koje sadrže brojeve za koje želite kurtozis"}, "LARGE": {"a": "(niz; k)", "d": "Vraća k-tu najveću vrednost u skupu podataka. Na primer, peti najveći broj", "ad": "je niz ili opseg podataka za koje želite da odredite k-tu najveću vrednost!je pozicija (od najvećeg) u nizu ili opsegu ćelija vrednosti za povratak"}, "LINEST": {"a": "(poznate_y; [poznate_x]; [konstanta]; [statistike])", "d": "Vraća statistike koje opisuju linearni trend koji odgovara poznatim tačkama podataka, pomoću crtanja prave linije koristeći metodu najmanjih kvadrata", "ad": "je skup y-vrednosti koje već znate u odnosu y = b*m^x, niz ili opseg pozitivnih brojeva!je opcioni skup x-vrednosti koje možda već znate u odnosu y = b*m^x, niz ili opseg iste veličine kao Poznata_y!su nove x-vrednosti za koje želite da RAST vrati odgovarajuće y-vrednosti!je logička vrednost: konstanta b se izračunava normalno ako je Konst = TAČNO; b je podešen jednak 1 ako je Konst = NETAČNO ili izostavljeno"}, "LOGEST": {"a": "(poznate_y; [poznate_x]; [konstanta]; [statistike])", "d": "Vraća statistike koje opisuju eksponencijalnu krivu koja odgovara poznatim tačkama podataka", "ad": "je skup y-vrednosti koje već znate u odnosu y = b*m^x!je opcioni skup x-vrednosti koje možda već znate u odnosu y = b*m^x!je logička vrednost: konstanta b se izračunava normalno ako je Konst = TAČNO ili izostavljena; b je podešen jednak 1 ako je Konst = NETAČNO!je logička vrednost: vratiti dodatnu regresijsku statistiku = TAČNO; povratak m-koeficijenti i konstanta b = NETAČNO ili izostavljeno"}, "LOGINV": {"a": "(verovatnoća; srednja; standardna_devijacija)", "d": "Vraća inverznu vrednost lognormalne kumulativne funkcije raspodele x, gde je ln(x) normalno distribuirana sa parametrima Srednja i Standardna_devijacija", "ad": "je verovatnoća povezana sa lognormalnom distribucijom, broj između 0 i 1, uk<PERSON><PERSON><PERSON><PERSON><PERSON>ći!je srednja vrednost ln(x)!je standardna devijacija ln(x), pozitivan broj"}, "LOGNORM.DIST": {"a": "(x; srednja; standardna_devijacija; kumulativno)", "d": "Vraća log-normalnu raspodelu za x, gde je ln(x) normalno distribuirano sa parametrima Srednja i Standardna_devijacija", "ad": "je vrednost na kojoj se procenjuje funkcija, pozitivan broj!je srednja vrednost ln(x)!je standardna devijacija ln(x), pozitivan broj!je logička vrednost: za kumulativnu funkciju distribucije, koristite TAČNO; za funkciju gustine ve<PERSON>, koristite NETAČNO"}, "LOGNORM.INV": {"a": "(verovatnoća; srednja; standardna_devijacija)", "d": "Vraća inverznu vrednost kumulativne log-normalne funkcije raspodele za x, gde je ln(x) normalno distribuirano sa parametrima Srednja i Standardna_devijacija", "ad": "je verovatnoća povezana sa lognormalnom distribucijom, broj između 0 i 1, uk<PERSON><PERSON><PERSON><PERSON><PERSON>ći!je srednja vrednost ln(x)!je standardna devijacija ln(x), pozitivan broj"}, "LOGNORMDIST": {"a": "(x; srednja; standardna_devijacija)", "d": "Vraća kumulativnu log-normalnu raspodelu za x, gde je ln(x) normalno distribuirano sa parametrima Srednja i Standardna_devijacija", "ad": "je vrednost na kojoj se procenjuje funkcija, pozitivan broj!je srednja vrednost ln(x)!je standardna devijacija ln(x), pozitivan broj"}, "MAX": {"a": "(broj1; [broj2]; ...)", "d": "Vraća najveću vrednost u skupu vrednosti. Zanemaruje logičke vrednosti i tekst", "ad": "su od 1 do 255 brojevi, p<PERSON><PERSON>, logičke vrednosti ili tekstualni brojevi za koje želite maksimum"}, "MAXA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Vraća najveću vrednost u skupu vrednosti. Ne zanemaruje logičke vrednosti i tekst", "ad": "su od 1 do 255 brojevi, p<PERSON><PERSON>, logičke vrednosti ili tekstualni brojevi za koje želite maksimum"}, "MAXIFS": {"a": "(max_opseg; opseg_kriterijuma; kriterijum; ...)", "d": "Vraća maksimalnu vrednost među ćelijama određenim zadatim skupom uslova ili kriterijuma", "ad": "ćelije u kojima se određuje maksimalna vrednost!je opseg ćelija koje želite da procenite za određeno stanje!je uslov ili kriterijum u obliku broja, izraza ili teksta koji definiše koje ćelije će biti uključene prilikom određivanja maksimalne vrednosti"}, "MEDIAN": {"a": "(broj1; [broj2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> medijan<PERSON>, ili broj u sredini skupa datih brojeva", "ad": "su od 1 do 255 brojeva ili imena, nizova ili referenci koje sadrže brojeve za koje želite medijanu"}, "MIN": {"a": "(broj1; [broj2]; ...)", "d": "Vraća najmanji broj u skupu vrednosti. Zanemaruje logičke vrednosti i tekst", "ad": "su od 1 do 255 brojevi, p<PERSON><PERSON>, logičke vrednosti ili tekstualni brojevi za koje želite minimum"}, "MINA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Vraća najmanju vrednost u skupu vrednosti. Ne zanemaruje logičke vrednosti i tekst", "ad": "su od 1 do 255 brojevi, p<PERSON><PERSON>, logičke vrednosti ili tekstualni brojevi za koje želite minimum"}, "MINIFS": {"a": "(min_opseg; opseg_kriterijuma; kriterijum; ...)", "d": "Vraća minimalnu vrednost među ćelijama određenim zadatim skupom uslova ili kriterijuma", "ad": "ćelije u kojima se određuje minimalna vrednost!je opseg ćelija koje želite da procenite za određeno stanje!je uslov ili kriterijumi u obliku broja, izraza ili teksta koji definiše koje ćelije će biti uključene prilikom određivanja minimalne vrednosti"}, "MODE": {"a": "(broj1; [broj2]; ...)", "d": "Vraća najčešće ponavljanu vrednost u nizu ili opsegu podataka", "ad": "su od 1 do 255 brojevi, ili imena, nizovi ili reference koje sadrže brojeve za koje želite režim"}, "MODE.MULT": {"a": "(broj1; [broj2]; ...)", "d": "Vraća vertikalni niz najčešće ponavljanih vrednosti u nizu ili opsegu podataka. Za horizontalni niz, koris<PERSON> =TRANSPONUJ(MODE.MULT(broj1,broj2,...))", "ad": "su od 1 do 255 brojevi, ili imena, nizovi ili reference koje sadrže brojeve za koje želite režim"}, "MODE.SNGL": {"a": "(broj1; [broj2]; ...)", "d": "Vraća najčešće ponavljanu vrednost u nizu ili opsegu podataka", "ad": "su od 1 do 255 brojevi, ili imena, nizovi ili reference koje sadrže brojeve za koje želite režim"}, "NEGBINOM.DIST": {"a": "(broj_neus<PERSON><PERSON>; broj_uspeha; verovat<PERSON>ća_uspeha; kumulativno)", "d": "Vra<PERSON>a negativnu binomnu raspodelu, verovat<PERSON><PERSON>u da će biti broj_neuspeha pre broja_uspeha, sa verovatnoćom_uspeha za uspeh", "ad": "je broj neuspeha!je prag broja uspeha!je verovatnoća uspeha; broj između 0 i 1!je logička vrednost: za kumulativnu funkciju distribucije, koristite TAČNO; za funkciju mase <PERSON>, koristite NETAČNO"}, "NEGBINOMDIST": {"a": "(broj_neus<PERSON><PERSON>; broj_uspeha; verovat<PERSON><PERSON>a_uspeha)", "d": "Vra<PERSON>a negativnu binomnu raspodelu, verovat<PERSON><PERSON>u da će biti broj_neuspeha pre broja_uspeha, sa verovatnoćom_uspeha za uspeh", "ad": "je broj neuspeha!je prag broja uspeha!je verovatnoća uspeha; broj između 0 i 1"}, "NORM.DIST": {"a": "(x; srednja; standardna_devijacija; kumulativno)", "d": "Vraća normalnu raspodelu za određenu srednju vrednost i standardnu devijaciju", "ad": "je vrednost za koju želite distribuciju!je aritmetička sredina raspodele!je standardna devijacija distribucije, pozitivan broj!je logička vrednost: za funkciju kumulativne raspodele koristite TAČNO; za funkciju gustine ve<PERSON>, koristite NETAČNO"}, "NORMDIST": {"a": "(x; srednja; standardna_devijacija; kumulativno)", "d": "Vraća normalnu kumulativnu raspodelu za određenu srednju vrednost i standardnu devijaciju", "ad": "je vrednost za koju želite distribuciju!je aritmetička sredina raspodele!je standardna devijacija distribucije, pozitivan broj!je logička vrednost: za funkciju kumulativne raspodele koristite TAČNO; za funkciju gustine ve<PERSON>, koristite NETAČNO"}, "NORM.INV": {"a": "(verovatnoća; srednja; standardna_devijacija)", "d": "Vraća inverznu vrednost normalne kumulativne raspodele za određenu srednju vrednost i standardnu devijaciju", "ad": "je verovatnoća koja odgovara normalnoj distribuciji, broj između 0 i 1 uključujući!je aritmetička sredina distribucije!je standardna devijacija distribucije, pozitivan broj"}, "NORMINV": {"a": "(verovatnoća; srednja; standardna_devijacija)", "d": "Vraća inverznu vrednost normalne kumulativne raspodele za određenu srednju vrednost i standardnu devijaciju", "ad": "je verovatnoća koja odgovara normalnoj distribuciji, broj između 0 i 1 uključujući!je aritmetička sredina distribucije!je standardna devijacija distribucije, pozitivan broj"}, "NORM.S.DIST": {"a": "(z; kumulativno)", "d": "Vraća standardnu normalnu raspodelu (ima srednju vrednost nula i standardnu devijaciju jedan)", "ad": "je vrednost za koju želite distribuciju!je logička vrednost za funkciju da vrati: kumulativna funkcija distribucije = TAČNO; funkcija gustine verov<PERSON> = NETAČNO"}, "NORMSDIST": {"a": "(z)", "d": "Vraća standardnu normalnu kumulativnu raspodelu (ima srednju vrednost nula i standardnu devijaciju jedan)", "ad": "je vrednost za koju želite distribuciju"}, "NORM.S.INV": {"a": "(verovat<PERSON>ća)", "d": "Vraća inverznu vrednost standardne normalne kumulativne raspodele (ima srednju vrednost nula i standardnu devijaciju jedan)", "ad": "je verovatnoća koja odgovara normalnoj distribuciji, broj između 0 i 1 uključujući"}, "NORMSINV": {"a": "(verovat<PERSON>ća)", "d": "Vraća inverznu vrednost standardne normalne kumulativne raspodele (ima srednju vrednost nula i standardnu devijaciju jedan)", "ad": "je verovatnoća koja odgovara normalnoj distribuciji, broj između 0 i 1 uključujući"}, "PEARSON": {"a": "(niz1; niz2)", "d": "<PERSON><PERSON><PERSON><PERSON> koeficijent korelacije proizvoda momenata, r", "ad": "je skup nezavisnih vrednosti!je skup zavisnih vrednosti"}, "PERCENTILE": {"a": "(niz; k)", "d": "Vraća k-ti percentil vrednosti u opsegu", "ad": "je niz ili opseg podataka koji definiše relativni položaj!je percentilna vrednost koja je između 0 do 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "PERCENTILE.EXC": {"a": "(niz; k)", "d": "Vraća k-ti percentil vrednosti u opsegu, gde je k u opsegu od 0 do 1, iskl<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> granice", "ad": "je niz ili opseg podataka koji definiše relativni položaj!je percentilna vrednost koja je između 0 do 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "PERCENTILE.INC": {"a": "(niz; k)", "d": "Vraća k-ti percentil vrednosti u opsegu, gde je k u opsegu od 0 do 1, ukl<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> granice", "ad": "je niz ili opseg podataka koji definiše relativni položaj!je percentilna vrednost koja je između 0 do 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "PERCENTRANK": {"a": "(niz; x; [zna<PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON>ć<PERSON> rang vrednosti u skupu podataka kao procenat skupa podataka", "ad": "je niz ili opseg podataka sa numeričkim vrednostima koji definiše relativni položaj!je vrednost za koju želite da znate rang!je opciona vrednost koja identifikuje broj značajnih cifara za vraćeni procenat, tri cifre ako su izostavljene (0.xxx%)"}, "PERCENTRANK.EXC": {"a": "(niz; x; [zna<PERSON><PERSON><PERSON><PERSON>])", "d": "Vraća rang vrednosti u skupu podataka kao procenat skupa podataka (0 do 1, iskl<PERSON>č<PERSON><PERSON><PERSON><PERSON> grani<PERSON>)", "ad": "je niz ili opseg podataka sa numeričkim vrednostima koji definiše relativni položaj!je vrednost za koju želite da znate rang!je opciona vrednost koja identifikuje broj značajnih cifara za vraćeni procenat, tri cifre ako su izostavljene (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(niz; x; [zna<PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON>rać<PERSON> rang vrednosti u skupu podataka kao procenat skupa podataka (0 do 1, ukl<PERSON>č<PERSON><PERSON><PERSON><PERSON> grani<PERSON>)", "ad": "je niz ili opseg podataka sa numeričkim vrednostima koji definiše relativni položaj!je vrednost za koju želite da znate rang!je opciona vrednost koja identifikuje broj značajnih cifara za vraćeni procenat, tri cifre ako su izostavljene (0.xxx%)"}, "PERMUT": {"a": "(broj; broj_i<PERSON><PERSON><PERSON>h)", "d": "Vraća broj permutacija za dati broj objekata koji se mogu izabrati iz ukupnog broja objekata", "ad": "je ukupan broj objekata!je broj objekata u svakoj permutaciji"}, "PERMUTATIONA": {"a": "(broj; broj_i<PERSON><PERSON><PERSON>h)", "d": "Vraća broj permutacija za dati broj objekata (sa ponavljanjem) koji se mogu izabrati iz ukupnog broja objekata", "ad": "je ukupan broj objekata!je broj objekata u svakoj permutaciji"}, "PHI": {"a": "(x)", "d": "Vraća vrednost funkcije gustine za standardnu normalnu raspodelu", "ad": "je broj za koji želite gustinu standardne normalne distribucije"}, "POISSON": {"a": "(x; srednja; kumulativno)", "d": "<PERSON><PERSON><PERSON><PERSON> raspodelu", "ad": "je broj događaja!je očekivana numerička vrednost, pozitivan broj!je logička vrednost: za kumulativnu Poasonovu verov<PERSON>, koristite TAČNO; za Poasonovu funkciju mase verov<PERSON>, koristite NETAČNO"}, "POISSON.DIST": {"a": "(x; srednja; kumulativno)", "d": "<PERSON><PERSON><PERSON><PERSON> raspodelu", "ad": "je broj događaja!je očekivana numerička vrednost, pozitivan broj!je logička vrednost: za kumulativnu Poasonovu verov<PERSON>, koristite TAČNO; za Poasonovu funkciju mase verov<PERSON>, koristite NETAČNO"}, "PROB": {"a": "(opseg_x; opseg_verovatnoća; donja_granica; [gornja_granica])", "d": "Vraća verovatnoću da vrednosti u opsegu budu između dve granice ili jednake donjoj granici", "ad": "je opseg numeričkih vrednosti od x sa kojima postoje povezane verovatnoće!je skup verovatnoća povezanih sa vrednostima u X_opsegu, vrednosti između 0 i 1 i isključujući 0!je donja granica vrednosti za koju želite verovatnoću!je opciona gornja granica na vrednosti. Ako je izostavljen, VEROVATNOĆA vraća verovatnoću da su X_opseg vrednosti jednake donja_granica"}, "QUARTILE": {"a": "(niz; kvar<PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> skupa podataka", "ad": "je niz ili opseg ćelija numeričkih vrednosti za koje želite kvartilnu vrednost!je broj: minimalna vrednost = 0; 1. kvar<PERSON> = 1; srednja vrednost = 2; 3. kvar<PERSON> = 3; maks<PERSON>lna vrednost = 4"}, "QUARTILE.INC": {"a": "(niz; kvar<PERSON>)", "d": "<PERSON>raća k<PERSON><PERSON> skupa podataka, bazirano na percentilnim vrednostima od 0 do 1, ukl<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> granice", "ad": "je niz ili opseg ćelija numeričkih vrednosti za koje želite kvartilnu vrednost!je broj: minimalna vrednost = 0; 1. kvar<PERSON> = 1; srednja vrednost = 2; 3. kvar<PERSON> = 3; maks<PERSON>lna vrednost = 4"}, "QUARTILE.EXC": {"a": "(niz; kvar<PERSON>)", "d": "Vraća kvartil skupa podataka, bazirano na percentilnim vrednostima od 0 do 1, iskl<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> granice", "ad": "je niz ili opseg ćelija numeričkih vrednosti za koje želite kvartilnu vrednost!je broj: minimalna vrednost = 0; 1. kvar<PERSON> = 1; srednja vrednost = 2; 3. kvar<PERSON> = 3; maks<PERSON>lna vrednost = 4"}, "RANK": {"a": "(broj; ref; [redosled])", "d": "Vraća rang broja u listi brojeva: njegova veličina u odnosu na druge vrednosti u listi", "ad": "je broj za koji želite da pronađete rang!je niz ili referenca na listu brojeva. Nenumeričke vrednosti se zanemaruju!je broj: rang na listi sortiran opadajuće = 0 ili izostavljen; rang na listi sortiran rastuće = bilo koja vrednost različita od nule"}, "RANK.AVG": {"a": "(broj; ref; [redosled])", "d": "Vraća rang broja u listi brojeva: njegova veličina u odnosu na druge vrednosti u listi; ako više vrednosti ima isti rang, vraća se prosečan rang", "ad": "je broj za koji želite da pronađete rang!je niz ili referenca na listu brojeva. Nenumeričke vrednosti se zanemaruju!je broj: rang na listi sortiran opadajuće = 0 ili izostavljen; rang na listi sortiran rastuće = bilo koja vrednost različita od nule"}, "RANK.EQ": {"a": "(broj; ref; [redosled])", "d": "Vraća rang broja u listi brojeva: njegova veličina u odnosu na druge vrednosti u listi; ako više vrednosti ima isti rang, vraća se najviši rang tog skupa vrednosti", "ad": "je broj za koji želite da pronađete rang!je niz ili referenca na listu brojeva. Nenumeričke vrednosti se zanemaruju!je broj: rang na listi sortiran opadajuće = 0 ili izostavljen; rang na listi sortiran rastuće = bilo koja vrednost različita od nule"}, "RSQ": {"a": "(poznati_y; poznati_x)", "d": "Vraća kvadrat Pirsonovog koeficijenta korelacije proizvoda momenata kroz date tačke podataka", "ad": "je niz ili opseg tačaka podataka i mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve!je niz ili opseg tačaka podataka i mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve"}, "SKEW": {"a": "(broj1; [broj2]; ...)", "d": "Vraća asimetriju raspodele: karakterizaciju stepena asimetrije raspodele oko njene srednje vrednosti", "ad": "su od 1 do 255 brojeva ili imena, nizova ili referenci koje sadrže brojeve za koje želite iskrivljenost"}, "SKEW.P": {"a": "(broj1; [broj2]; ...)", "d": "Vraća asimetriju raspodele baziranu na populaciji: karakterizaciju stepena asimetrije raspodele oko njene srednje vrednosti", "ad": "su od 1 do 254 brojeva ili imena, nizova ili referenci koje sadrže brojeve za koje želite iskrivljenost populacije"}, "SLOPE": {"a": "(poznati_y; poznati_x)", "d": "Vraća nagib linije linearne regresije kroz date tačke podataka", "ad": "je niz ili opseg ćelija numeričkih zavisnih tačaka podataka i mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve!je skup nezavisnih tačaka podataka i mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve"}, "SMALL": {"a": "(niz; k)", "d": "Vraća k-tu najmanju vrednost u skupu podataka. Na primer, peti najmanji broj", "ad": "je niz ili opseg numeričkih podataka za koje želite da odredite k-tu najmanju vrednost!je pozicija (od najmanje) u nizu ili opsegu vrednosti koja se vraća"}, "STANDARDIZE": {"a": "(x; srednja; standardna_devijacija)", "d": "Vraća normalizovanu vrednost iz raspodele karakterisane srednjom vrednošću i standardnom devijacijom", "ad": "je vrednost koju želite da normalizujete!je aritmetička sredina distribucije!je standardna devijacija distribucije, pozitivan broj"}, "STDEV": {"a": "(broj1; [broj2]; ...)", "d": "Procena standardne devijacije bazirana na uzorku (zanemaruje logičke vrednosti i tekst u uzorku)", "ad": "su od 1 do 255 brojeva koji odgovaraju uzorku populacije i mogu biti brojevi ili reference koje sadrže brojeve"}, "STDEV.P": {"a": "(broj1; [broj2]; ...)", "d": "Izračunava standardnu devijaciju baziranu na celokupnoj populaciji datoj kao argumenti (zanemaruje logičke vrednosti i tekst)", "ad": "su od 1 do 255 brojeva koji odgovaraju populaciji i mogu biti brojevi ili reference koje sadrže brojeve"}, "STDEV.S": {"a": "(broj1; [broj2]; ...)", "d": "Procena standardne devijacije bazirana na uzorku (zanemaruje logičke vrednosti i tekst u uzorku)", "ad": "su od 1 do 255 brojeva koji odgovaraju uzorku populacije i mogu biti brojevi ili reference koje sadrže brojeve"}, "STDEVA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Procena standardne devijacije bazirana na uzorku, uključujući logičke vrednosti i tekst. Tekst i logička vrednost FALSE (NETAČNO) imaju vrednost 0; logička vrednost TRUE (TAČNO) ima vrednost 1", "ad": "su od 1 do 255 vrednosti koje odgovaraju uzorku populacije i mogu biti vrednosti ili imena ili reference na vrednosti"}, "STDEVP": {"a": "(broj1; [broj2]; ...)", "d": "Izračunava standardnu devijaciju baziranu na celokupnoj populaciji datoj kao argumenti (zanemaruje logičke vrednosti i tekst)", "ad": "su od 1 do 255 brojeva koji odgovaraju populaciji i mogu biti brojevi ili reference koje sadrže brojeve"}, "STDEVPA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Izračunava standardnu devijaciju baziranu na celokupnoj populaciji, uključujući logičke vrednosti i tekst. Tekst i logička vrednost FALSE (NETAČNO) imaju vrednost 0; logička vrednost TRUE (TAČNO) ima vrednost 1", "ad": "su od 1 do 255 vrednosti koje odgovaraju populaciji i mogu biti vrednosti, imena, nizovi ili reference koje sadrže vrednosti"}, "STEYX": {"a": "(poznati_y; poznati_x)", "d": "Vraća standardnu grešku predviđene y-vrednosti za svaki x u regresiji", "ad": "je niz ili opseg zavisnih tačaka podataka i mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve!je niz ili opseg nezavisnih tačaka podataka i mogu biti brojevi ili imena, nizovi ili reference koje sadrže brojeve"}, "TDIST": {"a": "(x; stepeni_slobode; repovi)", "d": "Vraća Studentovu t-raspodelu", "ad": "je numerička vrednost na kojoj se procenjuje distribucija!je ceo broj koji ukazuje na broj stepeni slobode koji karakterišu distribuciju!specifikuje broj repova distribucije koje treba vratiti: jednostrana distribucija = 1; dvostrana distribucija = 2"}, "TINV": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; step<PERSON>_slobode)", "d": "Vraća dvostrano inverznu vrednost Studentove t-raspodele", "ad": "je verovatnoća povezana sa dvostranom Studentovom t-distribucijom, broj između 0 i 1 uključujući!je pozitivan ceo broj koji ukazuje na broj stepeni slobode za karakterizaciju distribucije"}, "T.DIST": {"a": "(x; stepeni_slobode; kumula<PERSON>v<PERSON>)", "d": "<PERSON>raća levo-repnu <PERSON>ovu t-raspodelu", "ad": "je numerička vrednost na kojoj se procenjuje distribucija!je ceo broj koji ukazuje na broj stepeni slobode koji karakterišu distribuciju!je logička vrednost: za kumulativnu funkciju distribucije, koristite TAČNO; za funkciju gustine ve<PERSON>, koristite NETAČNO"}, "T.DIST.2T": {"a": "(x; stepeni_slobode)", "d": "Vraća dvostrano Studentovu t-raspodelu", "ad": "je numerička vrednost na kojoj se procenjuje distribucija!je ceo broj koji ukazuje na broj stepeni slobode koji karakterišu distribuciju"}, "T.DIST.RT": {"a": "(x; stepeni_slobode)", "d": "<PERSON><PERSON><PERSON><PERSON> desno-repnu <PERSON>u t-raspodelu", "ad": "je numerička vrednost na kojoj se procenjuje distribucija!je ceo broj koji ukazuje na broj stepeni slobode koji karakterišu distribuciju"}, "T.INV": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; step<PERSON>_slobode)", "d": "Vraća levo-repnu inverznu vrednost Studentove t-raspodele", "ad": "je verovatnoća povezana sa dvokrakom Studentovom t-distribucijom, broj između 0 i 1 uključujući!je pozitivan ceo broj koji ukazuje na broj stepeni slobode za karakterizaciju distribucije"}, "T.INV.2T": {"a": "(verov<PERSON><PERSON><PERSON><PERSON>; step<PERSON>_slobode)", "d": "Vraća dvostrano inverznu vrednost Studentove t-raspodele", "ad": "je verovatnoća povezana sa dvokrakom Studentovom t-distribucijom, broj između 0 i 1 uključujući!je pozitivan ceo broj koji ukazuje na broj stepeni slobode za karakterizaciju distribucije"}, "T.TEST": {"a": "(niz1; niz2; repovi; tip)", "d": "Vraća verovatnoću povezanu sa <PERSON>ovim t-testom", "ad": "je prvi skup podataka!je drugi skup podataka!određuje broj repova distribucije za povratak: jednostrana distribucija = 1; Dvostrana distribucija = 2!Je vrsta T-testa: uparen = 1, dva uzorka jednaka varijansa (homoskedastično) = 2, dva uzorka nejednaka varijansa = 3"}, "TREND": {"a": "(poznati_y; [poznati_x]; [novi_x]; [konst])", "d": "Vraća brojeve u linearnom trendu koji odgovara poznatim podacima, koristeći metodu najmanjih kvadrata", "ad": "je opseg ili niz y-vrednosti koje već znate u odnosu y = mx + b!je opcioni opseg ili niz x-vrednosti koje znate u odnosu y = mx + b, niz iste veličine kao Poznati_y!je opseg ili niz novih x-vrednosti za koje želite da TREND vrati odgovarajuće y-vrednosti!je logička vrednost: konstanta b se izračunava normalno ako je Konst = TAČNO ili izostavljena; b je postavljen jednak 0 ako je Konst = NETAČNO"}, "TRIMMEAN": {"a": "(niz; procenat)", "d": "Vraća srednju vrednost unutrašnjeg dela skupa podataka", "ad": "je opseg ili niz vrednosti za skraćivanje i prosek!je frakcioni broj tačaka podataka koje treba isključiti sa vrha i dna skupa podataka"}, "TTEST": {"a": "(niz1; niz2; repovi; tip)", "d": "Vraća verovatnoću povezanu sa <PERSON>ovim t-testom", "ad": "je prvi skup podataka!je drugi skup podataka!određuje broj repova distribucije za povratak: jednostrana distribucija = 1; dvostrana distribucija = 2!Je vrsta T-testa: uparen = 1, dva uzorka jednaka varijansa (homoskedastično) = 2, dva uzorka nejednaka varijansa = 3"}, "VAR": {"a": "(broj1; [broj2]; ...)", "d": "Procena varijanse bazirana na uzorku (zanemaruje logičke vrednosti i tekst u uzorku)", "ad": "su od 1 do 255 numeričkih argumenata koji odgovaraju uzorku populacije"}, "VAR.P": {"a": "(broj1; [broj2]; ...)", "d": "Izračunava varijansu baziranu na celokupnoj populaciji (zanemaruje logičke vrednosti i tekst u populaciji)", "ad": "su od 1 do 255 numeričkih argumenata koji odgovaraju populaciji"}, "VAR.S": {"a": "(broj1; [broj2]; ...)", "d": "Procena varijanse bazirana na uzorku (zanemaruje logičke vrednosti i tekst u uzorku)", "ad": "su od 1 do 255 numeričkih argumenata koji odgovaraju uzorku populacije"}, "VARA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Procena varijanse bazirana na uzorku, uključujući logičke vrednosti i tekst. Tekst i logička vrednost FALSE (NETAČNO) imaju vrednost 0; logička vrednost TRUE (TAČNO) ima vrednost 1", "ad": "su od 1 do 255 vrednosnih argumenata koji odgovaraju uzorku populacije"}, "VARP": {"a": "(broj1; [broj2]; ...)", "d": "Izračunava varijansu baziranu na celokupnoj populaciji (zanemaruje logičke vrednosti i tekst u populaciji)", "ad": "su od 1 do 255 numeričkih argumenata koji odgovaraju populaciji"}, "VARPA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Izračunava varijansu baziranu na celokupnoj populaciji, uključujući logičke vrednosti i tekst. Tekst i logička vrednost FALSE (NETAČNO) imaju vrednost 0; logička vrednost TRUE (TAČNO) ima vrednost 1", "ad": "su od 1 do 255 vrednosnih argumenata koji odgovaraju populaciji"}, "WEIBULL": {"a": "(x; alfa; beta; kumulativno)", "d": "<PERSON><PERSON><PERSON><PERSON>-ovu raspodelu", "ad": "je vrednost na kojoj se procenjuje funkcija, nenegativan broj!je parametar za distribuciju, pozitivan broj!je parametar za distribuciju, pozitivan broj!je logička vrednost: za kumulativnu funkciju distribucije, koristite TAČNO; za funkciju mase verov<PERSON>, koristite NETAČNO"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; kumulativno)", "d": "<PERSON><PERSON><PERSON><PERSON>-ovu raspodelu", "ad": "je vrednost na kojoj se procenjuje funkcija, nenegativan broj!je parametar za distribuciju, pozitivan broj!je parametar za distribuciju, pozitivan broj!je logička vrednost: za kumulativnu funkciju distribucije, koristite TAČNO; za funkciju mase verov<PERSON>, koristite NETAČNO"}, "Z.TEST": {"a": "(niz; x; [sigma])", "d": "Vraća jednostranu P-vrednost z-testa", "ad": "je niz ili opseg podataka prema kojima se testira X!je vrednost za testiranje!je populacija (poznata) standardna devijacija. <PERSON><PERSON> se i<PERSON>vi, koristi se standardna devijacija uzorka"}, "ZTEST": {"a": "(niz; x; [sigma])", "d": "Vraća jednostranu P-vrednost z-testa", "ad": "je niz ili opseg podataka prema kojima se testira X!je vrednost za testiranje!je populacija (poznata) standardna devijacija. <PERSON><PERSON> se i<PERSON>vi, koristi se standardna devijacija uzorka"}, "ACCRINT": {"a": "(izdavanje; prvi_interes; namirenje; stopa; nominalna_vrednost; učestalost; [osnova]; [metod_izračunavanja])", "d": "Vraća obračunate kamate za hartiju od vrednosti koja plaća periodične kamate.", "ad": "je datum izdavanja hartije od vrednosti, izražen kao serijski broj datuma!je prvi datum interesa hartije od vrednosti, izražen kao broj serijskog datuma!je godišnja stopa kupona hartije od vrednosti!je nominalna vrednost hartije od vrednosti!je broj isplata kupona godišnje!je vrsta dana brojanja osnova za korišćenje!je logična vrednost: obračunata kamata od datuma izdavanja = TAČNO ili izostavljena; da izračunate od poslednjeg datuma isplate kupona = NETAČNO"}, "ACCRINTM": {"a": "(izdavanje; namirenje; stopa; nominalna_vrednost; [osnova])", "d": "Vraća obračunate kamate za hartiju od vrednosti koja plaća kamatu na dospelost.", "ad": "je datum izdavanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je godišnja stopa kupona hartije od vrednosti!je nominalna vrednost hartije od vrednosti!je vrsta dana računanja osnova za korišćenje"}, "AMORDEGRC": {"a": "(trošak; datum_kupovine; prvi_period; ostatak_vrednosti; period; stopa; [osnova])", "d": "Vraća proračunatu linearnu amortizaciju sredstva za svaki računovodstveni period.", "ad": "je cena sredstva!je datum kupovine sredstva!je datum završetka prvog perioda!je vrednost spasavanja na kraju života sredstva.!je period!je stopa amortizacije!godina_osnova : 0 za godinu od 360 dana, 1 za stvar<PERSON>, 3 za godinu od 365 dana."}, "AMORLINC": {"a": "(trošak; datum_kupovine; prvi_period; ostatak_vrednosti; period; stopa; [osnova])", "d": "Vraća proračunatu linearnu amortizaciju sredstva za svaki računovodstveni period.", "ad": "je cena sredstva!je datum kupovine sredstva!je datum završetka prvog perioda!je vrednost spasavanja na kraju života sredstva.!je period!je stopa amortizacije!godina_osnova : 0 za godinu od 360 dana, 1 za stvar<PERSON>, 3 za godinu od 365 dana."}, "COUPDAYBS": {"a": "(na<PERSON><PERSON><PERSON>; dospelost; učestalost; [osnova])", "d": "Vraća broj dana od početka kuponskog perioda do datuma namirenja.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je broj isplata kupona godišnje!je vrsta osnove za brojanje dana koju treba iskoristiti"}, "COUPDAYS": {"a": "(na<PERSON><PERSON><PERSON>; dospelost; učestalost; [osnova])", "d": "Vraća broj dana u kuponskom periodu koji sadrži datum namirenja.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je broj isplata kupona godišnje!je vrsta osnove za brojanje dana koju treba iskoristiti"}, "COUPDAYSNC": {"a": "(na<PERSON><PERSON><PERSON>; dospelost; učestalost; [osnova])", "d": "Vraća broj dana od datuma namirenja do sledećeg datuma kupona.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je broj isplata kupona godišnje!je vrsta osnove za brojanje dana koju treba iskoristiti"}, "COUPNCD": {"a": "(na<PERSON><PERSON><PERSON>; dospelost; učestalost; [osnova])", "d": "<PERSON><PERSON><PERSON><PERSON> sledeći datum kupona nakon datuma namirenja.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je broj isplata kupona godišnje!je vrsta osnove za brojanje dana koju treba iskoristiti"}, "COUPNUM": {"a": "(na<PERSON><PERSON><PERSON>; dospelost; učestalost; [osnova])", "d": "Vraća broj kupona koji se plaćaju između datuma namirenja i datuma dospelosti.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je broj isplata kupona godišnje!je vrsta osnove za brojanje dana koju treba iskoristiti"}, "COUPPCD": {"a": "(na<PERSON><PERSON><PERSON>; dospelost; učestalost; [osnova])", "d": "<PERSON><PERSON>ća prethodni datum kupona pre datuma namirenja.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je broj isplata kupona godišnje!je vrsta osnove za brojanje dana koju treba iskoristiti"}, "CUMIPMT": {"a": "(stopa; br_perioda; sada<PERSON><PERSON>_vrednost; poč<PERSON>ni_period; krajnji_period; tip)", "d": "Vraća kumulativnu isplaćenu kamatu između dva perioda.", "ad": "je kamatna stopa!je ukupan broj perioda plaćanja!je sadašnja vrednost!je prvi period u obračunu!je poslednji period u obračunu!je vreme plaćanja"}, "CUMPRINC": {"a": "(stopa; br_perioda; sada<PERSON><PERSON>_vrednost; poč<PERSON>ni_period; krajnji_period; tip)", "d": "Vraća kumulativno isplaćeni glavnicu zajma između dva perioda.", "ad": "je kamatna stopa!je ukupan broj perioda plaćanja!je sadašnja vrednost!je prvi period u obračunu!je poslednji period u obračunu!je vreme plaćanja"}, "DB": {"a": "(trošak; ostatak_vrednosti; vek; period; [mesec])", "d": "Vraća amortizaciju sredstva za određeni period koristeći metodu fiksnog opadajućeg salda.", "ad": "je početni trošak sredstva!je preostala vrednost na kraju životnog veka sredstva!je broj perioda tokom kojih se sredstvo amortizuje (ponekad se naziva i korisni vek sredstva)!je period za koji želite da izračunate amortizaciju. Period mora da koristi iste jedinice kao Život!je broj meseci u prvoj godini. Ako je mesec izostavljen, pretpostavlja se da je 12"}, "DDB": {"a": "(trošak; ostatak_vrednosti; vek; period; [faktor])", "d": "Vraća amortizaciju sredstva za određeni period koristeći metodu dvostruko opadajućeg salda ili neki drugi metod koji navedete.", "ad": "je početni trošak sredstva!je preostala vrednost na kraju životnog veka sredstva!je broj perioda tokom kojih se sredstvo amortizuje (ponekad se naziva i korisni vek sredstva)!je period za koji želite da izračunate amortizaciju. Period mora da koristi iste jedinice kao Život!je stopa kojom se saldo smanjuje. Ako je faktor i<PERSON>, pretpostavlja se da je 2 (metoda dvostruko opadajuć<PERSON> salda)"}, "DISC": {"a": "(na<PERSON><PERSON><PERSON>; dospel<PERSON>; cena; otkup; [osnova])", "d": "Vraća diskontnu stopu za hartiju od vrednosti.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je cena hartije od vrednosti po $100 nominalna vrednost!je otkupna vrednost hartije od vrednosti po $100 nominalna vrednost!je tip osove za brojanje dana koje treba koristiti"}, "DOLLARDE": {"a": "(cen_frakciona; frakcija)", "d": "Pretvara cenu dolara, izraženu kao frakciju, u cenu dolara, izraženu kao decimalni broj.", "ad": "je broj izražen kao razlomak! je ceo broj koji se koristi u imeniocu razlomka"}, "DOLLARFR": {"a": "(cen_decimalna; frakcija)", "d": "Pretvara cenu dolara, izraženu kao decimalni broj, u cenu dolara, izraženu kao frakciju.", "ad": "je decimalni broj! je ceo broj koji se koristi u imeniocu razlomka"}, "DURATION": {"a": "(namiren<PERSON>; dospelost; kupon; prinos; učestalost; [osnova])", "d": "Vraća godišnje trajanje hartije od vrednosti sa periodičnim kamatnim isplatama.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je godišnja stopa kupona hartije od vrednosti!je godišnji prinos hartije od vrednosti!je tip osnove za brojanje dana koje treba koristiti"}, "EFFECT": {"a": "(nominalna_stopa; br_perioda_god)", "d": "Vraća efektivnu godišnju kamatnu stopu.", "ad": "je nominalna kamatna stopa!je broj složenih perioda godišnje"}, "FV": {"a": "(stopa; br_perioda; uplata; [sadaš<PERSON>_vrednost]; [tip])", "d": "Vraća buduću vrednost investicije na osnovu periodičnih, konstantnih uplata i konstantne kamatne stope.", "ad": "je kamatna stopa po periodu. Na primer, koristite 6%/4 za kvartalne isplate na 6% APR!je ukupan broj perioda plaćanja u investiciji!je plaćanje izvršeno u svakom periodu; Ne može se promeniti tokom života investicije!je sada<PERSON><PERSON> vrednost, ili paušalni iznos koji sada vredi niz budućih isplata. Ako je izostavljen, Pv = 0!je vrednost koja predstavlja vreme plaćanja: plaćanje na početku perioda = 1; plaćanje na kraju perioda = 0 ili izostavljeno"}, "FVSCHEDULE": {"a": "(glavnica; raspored)", "d": "Vraća buduću vrednost početne glavnice nakon primene niza složenih kamatnih stopa.", "ad": "je sadašnja vrednost! je niz kamatnih stopa koje se primenjuju"}, "INTRATE": {"a": "(namirenje; dospelost; investicija; otkup; [osnova])", "d": "<PERSON>raća kamatnu stopu za potpuno investiranu hartiju od vrednosti.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je iznos uložen u hartiju od vrednosti!je iznos koji se prima po dospeću!je tip osnove za brojanje dana koje treba koristiti"}, "IPMT": {"a": "(stopa; period; br_perioda; sada<PERSON><PERSON>_vrednost; [buduća_vrednost]; [tip])", "d": "Vraća kamatnu uplatu za dati period investicije, na osnovu periodičnih, konstantnih uplata i konstantne kamatne stope.", "ad": "je kamatna stopa po periodu. Na primer, koristite 6%/4 za kvartalne isplate na 6% APR!je period za koji želite da pronađete kamatu i mora biti u opsegu od 1 do Nper!je ukupan broj perioda plaćanja u investiciji!je sada<PERSON><PERSON> vrednost, ili paušalni iznos koji serija budućih plaćanja vredi sada!je buduća vrednost, ili gotovinski saldo koji želite da postignete nakon poslednje uplate. Ako je izostavljen, Fv = 0!je logička vrednost koja predstavlja vreme plaćanja: na kraju perioda = 0 ili izostavljena, na početku perioda = 1"}, "IRR": {"a": "(v<PERSON><PERSON><PERSON>; [pretpostavka])", "d": "Vraća unutrašnju stopu povrata za niz novč<PERSON>h tokova.", "ad": "je niz ili referenca na ćelije koje sadrže brojeve za koje želite da izračunate unutrašnju stopu povratka!je broj za koji pretpostavljate da je blizu rezultata IRR-a; 0,1 (10 procenata) ako se izostavi"}, "ISPMT": {"a": "(stopa; period; br_perioda; sadašnja_vrednost)", "d": "Vraća isplaćenu kamatu tokom određenog perioda investicije.", "ad": "kamatna stopa po periodu. Na primer, koristite 6%/4 za kvartalne isplate na 6% APR!period za koji želite da pronađete kamatu!broj perioda plaćanja u investiciji!paušalni iznos koji je serija budućih isplata je upravo sada"}, "MDURATION": {"a": "(namiren<PERSON>; dospelost; kupon; prinos; učestalost; [osnova])", "d": "Vraća Makoli modifikovano trajanje za hartiju od vrednosti sa pretpostavljenom nominalnom vrednošću od $100.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je godišnja stopa kupona hartije od vrednosti!je godišnji prinos hartije od vrednosti!je tip osnove za brojanje dana koje treba iskoristiti"}, "MIRR": {"a": "(v<PERSON><PERSON><PERSON>; stopa_finansiranja; stopa_reinvestiranja)", "d": "Vraća unutrašnju stopu povrata za niz periodičnih novčanih tokova, uzimajući u obzir troškove investicije i kamatu na reinvestiranje novca.", "ad": "je niz ili referenca na ćelije koje sadrže brojeve koji predstavljaju niz plaćanja (negativnih) i prihoda (pozitivnih) u redovnim periodima!je kamatna stopa koju plaćate na novac koji se koristi u novčanim tokovima!je kamatna stopa koju dobijate na novčane tokove dok ih reinvestirate"}, "NOMINAL": {"a": "(efektivna_stopa; br_perioda_god)", "d": "Vraća nominalnu godišnju kamatnu stopu.", "ad": "je efektivna kamatna stopa!je broj složenih perioda godišnje"}, "NPER": {"a": "(stopa; uplata; sadaš<PERSON>_vrednost; [buduća_vrednost]; [tip])", "d": "Vraća broj perioda za investiciju na osnovu periodičnih, konstantnih uplata i konstantne kamatne stope.", "ad": "je kamatna stopa po periodu. Na primer, koristite 6%/4 za kvartalne isplate na 6% APR!je plaćanje izvršeno u svakom periodu;to ne može da se promeni tokom života investicije!je sada<PERSON><PERSON> vrednost, ili paušalni iznos koji serija budućih plaćanja vredi sada!je buduća vrednost, ili gotovinski saldo koji želite da postignete nakon što je izvršena poslednja uplata. Ako se izostavi, koristi se nula!je logička vrednost: plaćanje na početku perioda = 1; plaćanje na kraju perioda = 0 ili izostavljeno"}, "NPV": {"a": "(stopa; vrednost1; [vrednost2]; ...)", "d": "Vraća neto sadašnju vrednost investicije na osnovu diskontne stope i niza budućih uplata (negativne vrednosti) i prihoda (pozitivne vrednosti).", "ad": "je stopa popusta u dužini od jednog perioda!su od 1 do 254 isplate i prihodi, podjednako raspoređeni u vremenu i javljaju se na kraju svakog perioda"}, "ODDFPRICE": {"a": "(namirenje; dospelost; emisija; prvi_kupon; stopa; prinos; otkup; učestalost; [osnova])", "d": "Vraća cenu po $100 nominalne vrednosti hartije od vrednosti sa nepravilnim prvim periodom.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao broj dospeća hartije od vrednosti!je prvi datum kupona hartije od vrednosti, izražen kao serijski broj datuma!je kamatna stopa hartije od vrednosti!je godišnji prinos hartije od vrednosti!je vrednost otkupa hartije od vrednosti po $100 nominalna vrednost!je broj isplata kupona godišnje!je tip osnove za brojanje dana koje treba iskoristiti"}, "ODDFYIELD": {"a": "(namirenje; dospelost; emisija; prvi_kupon; stopa; cena; otkup; učestalost; [osnova])", "d": "Vraća prinos hartije od vrednosti sa nepravilnim prvim periodom.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski datum datuma!je prvi datum kupona hartije od vrednosti, izražen kao serijski broj datuma!je kamatna stopa hartije od vrednosti!je cena hartije od vrednosti!je vrednost otkupa hartije od vrednosti po $100 nominalna vrednost!je broj isplata kupona godišnje!je tip osnove za brojanje dana koje treba iskoristiti"}, "ODDLPRICE": {"a": "(na<PERSON>en<PERSON>; dospelost; pos<PERSON><PERSON>_kamata; stopa; prinos; otkup; učestalost; [osnova])", "d": "Vraća cenu po $100 nominalne vrednosti hartije od vrednosti sa nepravilnim poslednjim periodom.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski datum datuma!je kamatna stopa hartije od vrednosti!je godišnji prinos hartije od vrednosti!je vrednost otkupa hartije od vrednosti po $100 nominalna vrednost!je broj isplata kupona godišnje!je tip osnove za brojanje dana koje treba iskoristiti"}, "ODDLYIELD": {"a": "(na<PERSON><PERSON><PERSON>; dospel<PERSON>; pos<PERSON><PERSON>_kamata; stopa; cena; otkup; učestalost; [osnova])", "d": "Vraća prinos hartije od vrednosti sa nepravilnim poslednjim periodom.", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski datum datuma!je kamatna stopa hartije od vrednosti!je cena hartije od vrednosti!je vrednost otkupa hartije od vrednosti po $100 nominalna vrednost!je broj isplata kupona godišnje!je tip osnove za brojanje dana koje treba iskoristiti"}, "PDURATION": {"a": "(stopa; sada<PERSON><PERSON>_vrednost; buduća_vrednost)", "d": "Vraća broj perioda potrebnih da investicija dostigne određenu vrednost.", "ad": "je kamatna stopa po periodu.!je sadašnja vrednost investicije!je željena buduća vrednost investicije"}, "PMT": {"a": "(stopa; br_perioda; sada<PERSON><PERSON>_vrednost; [buduća_vrednost]; [tip])", "d": "Izračunava uplatu za zajam na osnovu konstantnih uplata i konstantne kamatne stope.", "ad": "je kamatna stopa po periodu za kredit. Na primer, koris<PERSON> 6%/4 za kvartalne isplate na 6% APR!je ukupan broj uplata za kredit!je sadašnja vrednost: ukupan iznos koji serija budućih plaćanja vredi sada!je buduća vrednost, ili gotovinski saldo koji želite da postignete nakon što je izvršena poslednja uplata, 0 (nula) ako je izostavljena!je logična vrednost: plaćanje na početku perioda = 1; plaćanje na kraju perioda = 0 ili izostavljeno"}, "PPMT": {"a": "(stopa; period; br_perioda; sada<PERSON><PERSON>_vrednost; [buduća_vrednost]; [tip])", "d": "Vraća uplatu na glavnicu za određenu investiciju na osnovu periodičnih, konstantnih uplata i konstantne kamatne stope.", "ad": "je kamatna stopa po periodu. Na primer, koristite 6%/4 za kvartalne isplate na 6% APR!određuje period i mora biti u opsegu od 1 do nper!je ukupan broj perioda plaćanja u investiciji!je sadašnja vrednost: ukupan iznos koji serija budućih plaćanja vredi sada!je buduća vrednost, ili gotovinski saldo koji želite da postignete nakon što je izvršena poslednja uplata!je logična vrednost: plaćanje na početku perioda = 1; plaćanje na kraju perioda = 0 ili izostavljeno"}, "PRICE": {"a": "(poravnanje; dos<PERSON><PERSON>e; stopa; prinos; otkup; učestalost; [osnova])", "d": "Vraća cenu po nominalnoj vrednosti od 100 dolara za hartiju od vrednosti koja plaća periodične kamate", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je godišnja stopa kupona hartije od vrednosti!je vrednost otkupa hartije od vrednosti po $100 nominalna vrednost!je broj isplata kupona godišnje!je tip osnove za brojanje dana koje treba iskorisiti"}, "PRICEDISC": {"a": "(p<PERSON><PERSON><PERSON><PERSON>; dos<PERSON><PERSON><PERSON>; diskont; otkup; [osnova])", "d": "Vraća cenu po nominalnoj vrednosti od 100 dolara za diskontovanu hartiju od vrednosti", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je diskontna stopa hartije od vrednosti!je otkupna vrednost hartije od vrednosti po $100 nominalne vrednosti!je tip osnove za brojanje dana koje treba iskoristiti"}, "PRICEMAT": {"a": "(poravnanje; dospeće; emisija; stopa; prinos; [osnova])", "d": "Vraća cenu po nominalnoj vrednosti od 100 dolara za hartiju od vrednosti koja plaća kamatu na dospeće", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je datum izdavanja hartije od vrednosti, izražen kao serijski broj datuma!je kamatna stopa hartije od vrednosti na dan izdavanja!je godišnji prinos hartije od vrednosti!je tip osnove za brojanje dana koje treba iskoristiti"}, "PV": {"a": "(stopa; br_perioda; rata; [buduća_vrednost]; [tip])", "d": "Vraća sadašnju vrednost investicije: ukupan iznos koji je niz budućih plaćanja vredan sada", "ad": "je kamatna stopa po periodu. Na primer, koristite 6%/4 za kvartalne isplate na 6% APR!je ukupan broj perioda plaćanja u investiciji!je uplata izvršena u svakom periodu i ne može se menjati tokom životnog veka investicije!je buduća vrednost, ili gotovinski saldo koji želite da postignete nakon što je izvršena poslednja uplata!je logična vrednost: plaćanje na početku perioda = 1; plaćanje na kraju perioda = 0 ili izostavljeno"}, "RATE": {"a": "(br_perioda; rata; sada<PERSON><PERSON>_vrednost; [buduća_vrednost]; [tip]; [pretpostavka])", "d": "Vraća kamatnu stopu po periodu zajma ili investicije. Na primer, koris<PERSON> 6%/4 za kvartalna plaćanja po godišnjoj stopi od 6%", "ad": "je ukupan broj perioda plaćanja za kredit ili investiciju!je uplata izvršena u svakom periodu i ne može se menjati tokom trajanja kredita ili investicije!je sadašnja vrednost: ukupan iznos koji serija budućih plaćanja vredi sada!je buduća vrednost, ili gotovinski saldo koji želite da postignete nakon poslednje uplate. Ako je izostavljen, koristi Fv = 0!je logička vrednost: plaćanje na početku perioda = 1; plaćanje na kraju perioda = 0 ili izostavljeno!je vaša pretpostavka za šta će stopa biti; ako je izostavljen, Pretpostavka = 0,1 (10 procenata)"}, "RECEIVED": {"a": "(porav<PERSON><PERSON>; dospeće; investicija; diskont; [osnova])", "d": "Vraća iznos primljen na dospeće za potpuno investiranu hartiju od vrednosti", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je iznos uložen u hartiji od vrednosti!je diskontna stopa hartije od vrednosti! je tip osove za brojanje dana koje treba iskoristiti"}, "RRI": {"a": "(br_perioda; sada<PERSON><PERSON>_vrednost; buduća_vrednost)", "d": "Vraća ekvivalentnu kamatnu stopu za rast investicije", "ad": "je broj perioda za investiciju!je sadašnja vrednost investicije!je buduća vrednost investicije"}, "SLN": {"a": "(trošak; otkup; vek)", "d": "Vraća linearnu amortizaciju sredstva za jedan period", "ad": "je početni trošak sredstva!je preostala vrednost na kraju životnog veka sredstva!je broj perioda tokom kojih se sredstvo amortizuje (ponekad se naziva korisni vek sredstva)"}, "SYD": {"a": "(trošak; otkup; vek; period)", "d": "Vraća amortizaciju sredstva po metodologiji cifara godina za određeni period", "ad": "je početni trošak sredstva!je preostala vrednost na kraju životnog veka sredstva!je broj perioda tokom kojih se sredstvo amortizuje (ponekad se naziva korisni vek sredstva)!je period i mora da koristi iste jedinice kao život"}, "TBILLEQ": {"a": "(p<PERSON><PERSON><PERSON><PERSON>; dos<PERSON><PERSON>e; diskont)", "d": "Vraća prinos ekvivalentan obveznici za trezorski zapis", "ad": "je datum poravnanja trezorskog zapisa, izražen kao serijski broj datuma!je datum dospeća trezorskog zapisa, izražen kao serijski broj datuma!je diskontna stopa trezorskog zapisa"}, "TBILLPRICE": {"a": "(p<PERSON><PERSON><PERSON><PERSON>; dos<PERSON><PERSON>e; diskont)", "d": "Vraća cenu po nominalnoj vrednosti od 100 dolara za trezorski zapis", "ad": "je datum poravnanja trezorskog zapisa, izražen kao serijski broj datuma!je datum dospeća trezorskog zapisa, izražen kao serijski broj datuma!je diskontna stopa trezorskog zapisa"}, "TBILLYIELD": {"a": "(p<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>e; cena)", "d": "Vraća prinos za trezorski zapis", "ad": "je datum poravnanja trezorskog zapisa, izražen kao serijski broj datuma!je datum dospeća trezorskog zapisa, izražen kao serijski broj datuma!je diskontna stopa trezorskog zapisa"}, "VDB": {"a": "(trošak; otkup; vek; početni_period; krajnji_period; [faktor]; [bez_preklopa])", "d": "Vraća amortizaciju sredstva za bilo koji period koji navedete, uključujući delimične periode, koristeći metodu dvostruke opadajuće vrednosti ili neku drugu metodu koju navedete", "ad": "je početni trošak sredstva!je preostala vrednost na kraju životnog veka sredstva!je broj perioda tokom kojih se sredstvo amortizira (ponekad se naziva i korisni vek sredstva)!je početni period za koji želite da izračunate amortizaciju, u istim jedinicama kao i život!je završni period za koji želite da izračunate amortizaciju, u istim jedinicama kao Život!je stopa po kojoj se stanje smanjuje, 2 (dvostruko opadajući bilans) ako je izostavljena!prebacite se na pravolinijsku amortizaciju kada je amortizacija veća od opadajućeg bilansa = NETAČNO ili izostavljena; nemoj prebaciti = TAČNO"}, "XIRR": {"a": "(v<PERSON><PERSON><PERSON>; datumi; [pretpostavka])", "d": "Vraća internu stopu povrata za raspored nov<PERSON><PERSON><PERSON> tokova", "ad": "je niz novč<PERSON>h tokova koji odgovaraju rasporedu plaćanja u datumima!je raspored datuma plaćanja koji odgovara isplatama novčanog toka!je broj koji pretpostavljate da je blizu rezultata XIRR"}, "XNPV": {"a": "(stopa; vred<PERSON>ti; datumi)", "d": "Vraća neto sadašnju vrednost za raspored novčanih tokova", "ad": "je diskontna stopa koja se primenjuje na novčane tokove!je niz novčanih tokova koji odgovaraju rasporedu plaćanja u datumima!je raspored datuma plaćanja koji odgovara isplatama novčanog toka"}, "YIELD": {"a": "(poravnanje; dos<PERSON><PERSON>e; stopa; cena; otkup; uč<PERSON>lost; [osnova])", "d": "Vraća prinos na hartiju od vrednosti koja plaća periodične kamate", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je godišnja stopa kupona hartije od vrednosti!je cena hartije od vrednosti po $100 nominalne vrednosti!je broj isplata kupona godišnje!je vrsta osnove za brojanje dana koju treba koristiti"}, "YIELDDISC": {"a": "(p<PERSON><PERSON><PERSON><PERSON>; dos<PERSON><PERSON><PERSON>; cena; otkup; [osnova])", "d": "Vraća godišnji prinos za diskontovanu hartiju od vrednosti. Na primer, trezorski zapis", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je cena hartije od vrednosti po $100 nominalne vrednosti!je otkupna vrednost hartije od vrednosti po $100 nominalne vrednosti!je vrsta osnove za brojanje dana koju treba koristiti"}, "YIELDMAT": {"a": "(poravnanje; dos<PERSON><PERSON>e; emisija; stopa; cena; [osnova])", "d": "Vraća godišnji prinos hartije od vrednosti koja plaća kamatu na dospeće", "ad": "je datum poravnanja hartije od vrednosti, izražen kao serijski broj datuma!je datum dospeća hartije od vrednosti, izražen kao serijski broj datuma!je datum izdavanja hartije od vrednosti, izražen kao serijski broj datuma!je kamatna stopa hartije od vrednosti na dan izdavanja!je cena hartije od vrednosti po $100 nominalne vrednosti!je vrsta osnove za brojanje dana koju treba koristiti"}, "ABS": {"a": "(broj)", "d": "Vraća apsolutnu vrednost broja, broj bez njegovog znaka", "ad": "je realan broj za koji želite apsolutnu vrednost"}, "ACOS": {"a": "(broj)", "d": "Vraća arkus kosinus broja, u radijanima u rasponu od 0 do Pi. Arkus kosinus je ugao čiji je kosinus Broj", "ad": "je kos<PERSON>eljenog ugla i mora biti od -1 do 1"}, "ACOSH": {"a": "(broj)", "d": "Vraća inverzni hiperbolički kosinus broja", "ad": "je bilo koji realni broj jednak ili veći od 1"}, "ACOT": {"a": "(broj)", "d": "Vraća arkus kotangens broja, u radijanima u rasponu od 0 do Pi", "ad": "je kotangens ugla koji <PERSON>"}, "ACOTH": {"a": "(broj)", "d": "Vraća inverzni hiperbolički kotangens broja", "ad": "je hiperbolički kotangens ugla koji želite"}, "AGGREGATE": {"a": "(broj_funkcije; opcije; referenca1; ...)", "d": "Vraća agregat u listi ili bazi podataka", "ad": "je broj od 1 do 19 koji određuje funkciju sažetka za agregat.!je broj od 0 do 7 koji određuje vrednosti koje treba zanemariti za agregat!je niz ili opseg numeričkih podataka na kojima se izračunava agregat!označava poziciju u nizu; To je k-ti najveći, k-ti najmanji, k-ti percentil, ili k-ti kvartil.!je broj od 1 do 19 koji određuje funkciju sažetka za agregat.!je broj od 0 do 7 koji specificira vrednosti koje treba zanemariti za agregat!su od 1 do 253 opsega ili reference za koje želite agregat"}, "ARABIC": {"a": "(tekst)", "d": "Pretvara rimski broj u arapski", "ad": "je rimski broj koji želite da konvertujete"}, "ASC": {"a": "(tekst)", "d": "Za jezike sa d<PERSON><PERSON><PERSON><PERSON> (DBCS), funkcija menja pune (dvobajtne) karaktere u polovične (jednobajtne) karaktere", "ad": "je tekst koji želite da promenite. Ako tekst ne sadrži slova pune širine, tekst se ne menja."}, "ASIN": {"a": "(broj)", "d": "Vraća arkus sinus broja u radijanima, u rasponu od -Pi/2 do Pi/2", "ad": "je sinus željenog ugla i mora biti od -1 do 1"}, "ASINH": {"a": "(broj)", "d": "Vraća inverzni hiperbolički sinus broja", "ad": "je bilo koji realni broj jednak ili veći od 1"}, "ATAN": {"a": "(broj)", "d": "Vraća arkus tangens broja u radijanima, u rasponu od -Pi/2 do Pi/2", "ad": "je tangenta ugla koji <PERSON>"}, "ATAN2": {"a": "(x_broj; y_broj)", "d": "Vraća arkus tangens za navedene x i y koordinate, u radijanima između -Pi i Pi, isključujući -Pi", "ad": "je x-koordinata tačke!je y-koordinata tačke"}, "ATANH": {"a": "(broj)", "d": "Vraća inverzni hiperbolički tangens broja", "ad": "je bilo koji realni broj između -1 i 1 isključujući -1 i 1"}, "BASE": {"a": "(broj; baza; [minimalna_du<PERSON><PERSON>])", "d": "Pretvara broj u tekstualnu reprezentaciju sa datom bazom (osnovom)", "ad": "je broj koji želite da konvertujete!je osnova Radix u koji želite da konvertujete broj!je minimalna dužina vraćenog niza. Ako se ne dodaju izostavljene vodeće nule"}, "CEILING": {"a": "(broj; značajnost)", "d": "Zaokružuje broj naviše, do najbližeg višekratnika značajnosti", "ad": "je vrednost koju želite da zaokružite!je višestruki na koji želite da zaokružite"}, "CEILING.MATH": {"a": "(broj; [zna<PERSON><PERSON><PERSON><PERSON>]; [način])", "d": "Zaokružuje broj naviše, do najbližeg celog broja ili do najbližeg višekratnika značajnosti", "ad": "je vrednost koju želite da zaokružite!je višestruki na koji želite da zaokružite!kada je dat i nije nula, ova funkcija će zaokružiti dalje od nule"}, "CEILING.PRECISE": {"a": "(x; [z<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Vraća broj zaokružen naviše do najbližeg celog broja ili do najbližeg višekratnika značajnosti", "ad": "je vrednost koju želite da zaokružite!je višestruki na koji želite da zaokružite"}, "COMBIN": {"a": "(broj; izabrano)", "d": "Vraća broj kombinacija za dati broj stavki", "ad": "je ukupan broj stavki!je broj stavki u svakoj kombinaciji"}, "COMBINA": {"a": "(broj; izabrano)", "d": "Vraća broj kombinacija sa ponavljanjima za dati broj stavki", "ad": "je ukupan broj stavki!je broj stavki u svakoj kombinaciji"}, "COS": {"a": "(broj)", "d": "<PERSON><PERSON><PERSON><PERSON> kos<PERSON> ugla", "ad": "je ugao u radijanima za koji želite kosinus"}, "COSH": {"a": "(broj)", "d": "Vraća hiperbolički kosinus broja", "ad": "je bilo koji realni broj"}, "COT": {"a": "(broj)", "d": "Vraća kotangens ugla", "ad": "je ugao u radijanima za koji želite kotangens"}, "COTH": {"a": "(broj)", "d": "Vraća hiperbolički kotangens broja", "ad": "je ugao u radijanima za koji želite hiperbolički kotangens"}, "CSC": {"a": "(broj)", "d": "<PERSON>raća kosekan<PERSON> ugla", "ad": "je ugao u radijanima za koji želite kosekans"}, "CSCH": {"a": "(broj)", "d": "Vraća hiperbolički kosekans ugla", "ad": "je ugao u radijanima za koji želite hiperbolički kosekans"}, "DECIMAL": {"a": "(broj; baza)", "d": "Pretvara tekstualnu reprezentaciju broja u datoj bazi u decimalni broj", "ad": "je broj koji želite da konvertujete!je osnova radix broja koji se pretvara"}, "DEGREES": {"a": "(ugao)", "d": "<PERSON>tvara radi<PERSON>e u <PERSON>", "ad": "je ugao u radijancima koji želite da konvertujete"}, "ECMA.CEILING": {"a": "(x; značajnost)", "d": "Zaokružuje broj naviše do najbližeg višekratnika značajnosti", "ad": "je vrednost koju želite da zaokružite!je višestruki na koji želite da zaokružite"}, "EVEN": {"a": "(broj)", "d": "Zaokružuje pozitivan broj naviše i negativan broj naniže do najbližeg parnog celog broja", "ad": "je vrednost koju treba zaokružiti"}, "EXP": {"a": "(broj)", "d": "Vraća e podignuto na stepen zadanog broja", "ad": "je eksponent koji se primenjuje na bazu e. Konstanta e jednaka je 2.71828182845904, osnova prirodnog logaritma"}, "FACT": {"a": "(broj)", "d": "<PERSON>rać<PERSON> fak<PERSON>, jednak 1*2*3*...* Broj", "ad": "je nenegativan broj za koji želite faktorijel"}, "FACTDOUBLE": {"a": "(broj)", "d": "Vraća dvostruki faktorijel broja", "ad": "je vrednost za koju se vraća dvostruki faktorijal"}, "FLOOR": {"a": "(broj; značajnost)", "d": "Zaokružuje broj naniže do najbližeg višekratnika značajnosti", "ad": "je numerička vrednost koju želite da zaokružite!je višestruki na koji želite da zaokružite. Broj i Značajnost moraju biti ili oba pozitivna ili oba negativna"}, "FLOOR.PRECISE": {"a": "(x; [z<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Vraća broj zaokružen naniže do najbližeg celog broja ili do najbližeg višekratnika značajnosti", "ad": "je vrednost koju želite da zaokružite!je višestruki na koji želite da zaokružite"}, "FLOOR.MATH": {"a": "(broj; [zna<PERSON><PERSON><PERSON><PERSON>]; [način])", "d": "Zaokružuje broj naniže, do najbližeg celog broja ili do najbližeg višekratnika značajnosti", "ad": "je vrednost koju želite da zaokružite!je višestruki na koji želite da zaokružite!Kada je data i nije nula, ova funkcija će se zaokružiti prema nuli"}, "GCD": {"a": "(broj1; [broj2]; ...)", "d": "Vraća najveći zajednički delilac", "ad": "su od 1 do 255 vrednosti"}, "INT": {"a": "(broj)", "d": "Zaokružuje broj naniže do najbližeg celog broja", "ad": "je stvarni broj koji želite da zaokružite na ceo broj"}, "ISO.CEILING": {"a": "(broj; [zna<PERSON><PERSON><PERSON><PERSON>])", "d": "Vraća broj zaokružen naviše do najbližeg celog broja ili do najbližeg višekratnika značajnosti bez obzira na znak broja. <PERSON><PERSON><PERSON><PERSON>, ako je broj ili značajnost nula, vraća se nula.", "ad": "je vrednost koju želite da zaokružite!je višestruki na koji želite da zaokružite"}, "LCM": {"a": "(broj1; [broj2]; ...)", "d": "Vraća najmanji zajednički sadržalac", "ad": "su od 1 do 255 vrednosti za koje želite najmanji zajednički sadržalac"}, "LN": {"a": "(broj)", "d": "Vraća prirodni logaritam broja", "ad": "je pozitivan realni broj za koji želite prirodni logaritam"}, "LOG": {"a": "(broj; [baza])", "d": "Vraća logaritam broja u zadatoj bazi", "ad": "je pozitivan realni broj za koji želite logaritam!je osnova logaritma; 10 ako je izostavljen"}, "LOG10": {"a": "(broj)", "d": "Vraća logaritam broja u bazi 10", "ad": "je pozitivan realni broj za koji želite logaritam baze-10"}, "MDETERM": {"a": "(niz)", "d": "<PERSON>raća determinantu matrice niza", "ad": "je numerički niz sa jednakim brojem redova i kolona, bilo opseg ćelija ili konstanta niza"}, "MINVERSE": {"a": "(niz)", "d": "Vraća inverznu matricu za matricu pohranjenu u nizu", "ad": "je numerički niz sa jednakim brojem redova i kolona, bilo opseg ćelija ili konstanta niza"}, "MMULT": {"a": "(niz1; niz2)", "d": "Vraća matrični proizvod dva niza, niz sa istim brojem redova kao niz1 i kolona kao niz2", "ad": "je prvi niz brojeva za množenje i mora imati isti broj kolona kao što Niz2 ima redova"}, "MOD": {"a": "(broj; delilac)", "d": "Vraća ostatak nakon što se broj podeli deliocem", "ad": "je broj za koji želite da pronađete ostatak nakon što se izvrši podela!je broj kojim želite da podelite Broj"}, "MROUND": {"a": "(broj; višekratnik)", "d": "Vraća broj zaokružen na željeni višekratnik", "ad": "je vrednost koju treba zaokružiti!je višestruki na koji želite da zaokružite broj"}, "MULTINOMIAL": {"a": "(broj1; [broj2]; ...)", "d": "Vraća multinomijal skupa brojeva", "ad": "su od 1 do 255 vrednosti za koje želite multinomijal"}, "MUNIT": {"a": "(dimenzija)", "d": "Vraća jediničnu matricu za zadatu dimenziju", "ad": "je ceo broj koji određuje dimenziju matrice jedinica koju želi<PERSON> da vratite"}, "ODD": {"a": "(broj)", "d": "Zaokružuje pozitivan broj naviše i negativan broj naniže do najbližeg neparnog celog broja", "ad": "je vrednost koju treba zaokružiti"}, "PI": {"a": "()", "d": "Vraća vrednost Pi, 3.14159265358979, tačno do 15 cifara", "ad": ""}, "POWER": {"a": "(broj; eksponent)", "d": "Vraća rezultat broja podignutog na zadati stepen", "ad": "je osnovni broj, bilo koji realni broj!je eksponent, na koji je podignut osnovni broj"}, "PRODUCT": {"a": "(broj1; [broj2]; ...)", "d": "Množi sve zadate brojeve", "ad": "su od 1 do 255 brojeva, logičke vrednosti ili tekstualne reprezentacije brojeva koje želite da pomnožite"}, "QUOTIENT": {"a": "(deljenik; delilac)", "d": "Vraća celobrojni deo deljenja", "ad": "je deljenik!je delilac"}, "RADIANS": {"a": "(ugao)", "d": "<PERSON><PERSON><PERSON><PERSON> step<PERSON> u radijane", "ad": "je ugao u stepenima koji želi<PERSON> da konvertujete"}, "RAND": {"a": "()", "d": "Vraća slučajan broj veći ili jednak 0 i manji od 1, ravnomerno distribuiran (menja se pri ponovnom izračunavanju)", "ad": ""}, "RANDARRAY": {"a": "([redovi]; [kolone]; [min]; [max]; [ceo_broj])", "d": "Vraća niz slučajnih brojeva", "ad": "broj redova u vraćenom nizu!broj kolona u vraćenom nizu!minimalni broj koji želite vratiti!maksimalni broj koji želite vratiti!vratite ceo broj ili decimalnu vrednost. TAČNO za ceo broj, NETAČNO za decimalni broj"}, "RANDBETWEEN": {"a": "(dole; gore)", "d": "Vraća nasumičan broj između specificiranih brojeva", "ad": "je najmanji ceo broj koji će RANDIZMEĐU vratiti!je najveći ceo broj koji će RANDIZMEĐU vratiti"}, "ROMAN": {"a": "(broj; [forma])", "d": "Pretvara arapski broj u rimski, kao tekst", "ad": "je arapski broj koji želite da konvertujete!je broj koji određuje vrstu rimskog broja koji želite."}, "ROUND": {"a": "(broj; broj_cifara)", "d": "Zaokružuje broj na određeni broj cifara", "ad": "je broj koji želite da zaokružite!je broj cifara na koje želite da zaokružite. Negativni broj zaokružuje levo od decimalne tačke; nula do najbližeg celog broja"}, "ROUNDDOWN": {"a": "(broj; broj_cifara)", "d": "Zaokruž<PERSON><PERSON> broj <PERSON>, prema nuli", "ad": "je bilo koji realan broj koji želite zaokružiti nadole!je broj cifara na koje želite da zaokružite. Negativni broj zaokružuje levo od decimalne tačke; nula ili izostavljeno, na najbliži ceo broj"}, "ROUNDUP": {"a": "(broj; broj_cifara)", "d": "Zaokružuje broj navi<PERSON>, od nule", "ad": "je bilo koji realan broj koji želite zaokružiti naviše!je broj cifara na koje želite da zaokružite. Negativni broj zaokružuje levo od decimalne tačke; nula ili izostavljeno, na najbliži ceo broj"}, "SEC": {"a": "(broj)", "d": "<PERSON><PERSON><PERSON><PERSON> se<PERSON> ugla", "ad": "je ugao u radijanima za koji želite sekant"}, "SECH": {"a": "(broj)", "d": "Vraća hiperbolički sekans ugla", "ad": "je ugao u radijanima za koji želite hiperbolički sekant"}, "SERIESSUM": {"a": "(x; n; m; koe<PERSON><PERSON><PERSON><PERSON>)", "d": "Vraća zbir redova snaga na osnovu formule", "ad": "je ulazna vrednost u stepenasti niz!je početni stepen na koju želite da podignete x!je korak kojim se povećava n za svaki član u nizu!je skup koeficijenata po kojima se množi svaki uzastopna stepen x"}, "SIGN": {"a": "(broj)", "d": "Vraća znak broja: 1 ako je broj pozitivan, nula ako je broj nula, ili -1 ako je broj negativan", "ad": "je bilo koji realni broj"}, "SIN": {"a": "(broj)", "d": "Vraća sinus ugla", "ad": "je ugao u radijanima za koji želite sinus. Stepeni * PI()/180 = radijani"}, "SINH": {"a": "(broj)", "d": "Vraća hiperbolički sinus broja", "ad": "je bilo koji realni broj"}, "SQRT": {"a": "(broj)", "d": "Vraća kvadratni koren broja", "ad": "je broj za koji želite kvadratni koren"}, "SQRTPI": {"a": "(broj)", "d": "Vraća kvadratni koren (broj * Pi)", "ad": "je broj kojim se p množi"}, "SUBTOTAL": {"a": "(broj_funkcije; ref1; ...)", "d": "Vraća međuzbir u listi ili bazi podataka", "ad": "je broj od 1 do 11 koji određuje funkciju rezimea za međuzbir.!su od 1 do 254 opsega ili referenci za koje želite međuzbir"}, "SUM": {"a": "(broj1; [broj2]; ...)", "d": "Sabira sve brojeve u opsegu ćelija", "ad": "su od 1 do 255 brojeva za sabiranje. Logičke vrednosti i tekst se ignorišu u ćelijama, uključene ako su upisane kao argumenti"}, "SUMIF": {"a": "(opseg; uslov; [opseg_sume])", "d": "Sabira ćelije koje zadovoljavaju određeni uslov ili kriterijum", "ad": "je opseg ćelija koje želite proceniti!je uslov ili kriterijum u obliku broja, izraza ili teksta koji definiše koje ćelije će biti dodate!su stvarne ćelije za sumiranje. Ako se izostavi, koriste se ćelije u opsegu"}, "SUMIFS": {"a": "(opseg_sume; opseg_kriterijuma; uslov; ...)", "d": "<PERSON>bira ćelije koje zadovoljavaju zadati skup uslova ili kriterijuma", "ad": "su realne ćelije koje treba sumirati.!je opseg ćelija koje želite proceniti za određeni uslov!je uslov ili kriterijum u obliku broja, izraza ili teksta koji definiše koje ćelije će biti dodate"}, "SUMPRODUCT": {"a": "(niz1; [niz2]; [niz3]; ...)", "d": "Vraća zbir proizvoda odgovarajućih opsega ili nizova", "ad": "su od 2 do 255 nizova za koje želite da pomnožite, a zatim dodate komponente. Svi nizovi moraju imati iste dimenzije"}, "SUMSQ": {"a": "(broj1; [broj2]; ...)", "d": "Vraća zbir kvadrata argumenata. Argumenti mogu biti brojevi, <PERSON><PERSON><PERSON>, imena ili reference na ćelije koje sadrže brojeve", "ad": "su od 1 do 255 brojeva, ni<PERSON><PERSON>, imena ili referenci na nizove za koje želite zbir kvadrata"}, "SUMX2MY2": {"a": "(niz_x; niz_y)", "d": "Sabira razlike između kvadrata dva odgovarajuća niza ili opsega", "ad": "je prvi opseg ili niz brojeva i može biti broj ili ime, niz ili referenca koja sadrži brojeve!je drugi opseg ili niz brojeva i može biti broj ili ime, niz ili referenca koja sadrži brojeve"}, "SUMX2PY2": {"a": "(niz_x; niz_y)", "d": "Vraća ukupnu sumu kvadrata brojeva u dva odgovarajuća niza ili opsega", "ad": "je prvi opseg ili niz brojeva i može biti broj ili ime, niz ili referenca koja sadrži brojeve!je drugi opseg ili niz brojeva i može biti broj ili ime, niz ili referenca koja sadrži brojeve"}, "SUMXMY2": {"a": "(niz_x; niz_y)", "d": "Sabira kvadrate razlika u dva odgovarajuća niza ili opsega", "ad": "je prvi opseg ili niz vrednosti i može biti broj ili ime, niz ili referenca koja sadrži brojeve!je drugi opseg ili niz vrednosti i može biti broj ili ime, niz ili referenca koja sadrži brojeve"}, "TAN": {"a": "(broj)", "d": "Vraća tangens ugla", "ad": "je ugao u radijanima za koji želite tangentu. Stepeni * PI()/180 = radijani"}, "TANH": {"a": "(broj)", "d": "Vraća hiperbolički tangens broja", "ad": "je bilo koji realni broj"}, "TRUNC": {"a": "(broj; [broj_cifara])", "d": "Skraćuje broj na ceo broj uklanjanjem decimalnog, ili f<PERSON>, dela broja", "ad": "je broj koji želite da skratite!je broj koji određuje preciznost skraćivanja, 0 (nula) ako je izostavljen"}, "ADDRESS": {"a": "(broj_reda; broj_kolone; [apsolutni_broj]; [a1]; [ime_lista])", "d": "<PERSON><PERSON><PERSON> referencu na ćeliju kao tekst, dato prema specificiranim brojevima reda i kolone", "ad": "je broj reda koji se koristi u referenci ćelije: broj_reda = 1 za red 1!je broj kolone koji se koristi u referenci ćelije. Na primer, broj_kolone = 4 za kolonu D!određuje tip reference: apsolutni = 1; apsolutni red / relativna kolona = 2; relativni red / apsolutna kolona = 3; relativna = 4!je logička vrednost koja određuje stil reference: A1 stil = 1 ili TAČNO; R1C1 stil = 0 ili NETAČNO! je tekst koji određuje ime radnog lista koji će se koristiti kao spoljna referenca"}, "CHOOSE": {"a": "(indeksni_broj; vrednost1; [vrednost2]; ...)", "d": "Bira vrednost ili radnju koju treba izvršiti iz liste vrednosti, na osnovu indeksnog broja", "ad": "specificira koji argument vrednosti je izabran. Index_num mora biti između 1 i 254, ili formula ili referenca na broj između 1 i 254!su 1 do 254 brojeva, referen<PERSON>, defini<PERSON><PERSON> imena, formule, funkcije ili tekstualni argumenti iz kojih IZABERI bira"}, "COLUMN": {"a": "([referenca])", "d": "Vraća broj kolone reference", "ad": "je ćelija ili opseg susednih ćelija za koje želite broj kolone. Ako je izostavl<PERSON>, koristi se ćelija koja sadrži funkciju KOLONA"}, "COLUMNS": {"a": "(niz)", "d": "Vraća broj kolona u nizu ili referenci", "ad": "je niz ili formula niza, ili referenca na niz ćelija za koje želite broj kolona"}, "FORMULATEXT": {"a": "(referenca)", "d": "Vraća formulu kao string", "ad": "je referenca na formulu"}, "HLOOKUP": {"a": "(vrednost_pretrage; niz_tabele; broj_reda; [opseg_pretrage])", "d": "Traži vrednost u gornjem redu tabele ili niza vrednosti i vraća vrednost u istoj koloni iz reda koji vi navedete", "ad": "je vrednost koja se nalazi u prvom redu tabele i može biti vrednost, referenca ili tekstualni niz!je tabela teksta, brojeva ili logičkih vrednosti u kojima se podaci pretražuju. Niz_tabele može biti referenca na opseg ili ime opsega!je broj reda u nizu_tabele iz kojeg treba da se vrati odgovarajuća vrednost. Prvi red vrednosti u tabeli je red 1!je logička vrednost: da pronađete najbliže podudaranje u gornjem redu (sortirano u uzlaznom redosledu) = TAČNO ili izostavljeno; pronađi tačno podudaranje = NETAČNO"}, "HYPERLINK": {"a": "(lokacija_veze; [prija<PERSON><PERSON><PERSON>_ime])", "d": "<PERSON><PERSON>ira prečicu ili skok koji otvara dokument sačuvan na vašem hard disku, mrežnom serveru ili na Internetu", "ad": "je tekst koji daje putanju i ime datoteke dokumentu koji se otvara, lokacija čvrstog diska, UNC adresa ili URL putanja!je tekst ili broj koji se prikazuje u ćeliji. A<PERSON> je <PERSON>l<PERSON>, ćelija prikazuje Link_lokaciju teksta"}, "INDEX": {"a": "(niz; broj_reda; [broj_kolone]; referen<PERSON>; broj_reda; [broj_kolone]; [broj_podru<PERSON><PERSON>])", "d": "Vraća vrednost ili referencu ćelije na preseku određenog reda i kolone u datom opsegu", "ad": "je niz ćelija ili konstanta niza.!bira red u nizu ili referencu iz koje će vratiti vrednost. Ako je izostavljen, broj_kolone je obavezan!bira kolonu u nizu ili referencu iz koje se vraća vrednost. Ako je izostavljen, broj_reda je obavezno!je referenca na jedan ili više opsega ćelija!bira red u nizu ili referenci iz koje se vraća vrednost. Ako je izostavljen, broj_kolonr je obavezan!bira kolonu u nizu ili referencu iz koje se vraća vrednost. Ako je izostavljen, broj_reda je obavezan!bira opseg u Referenci iz koje se vraća vrednost. Prva oblast izabrana ili unesena je oblast 1, druga oblast je oblast 2, i tako dalje"}, "INDIRECT": {"a": "(tekst_referenca; [a1])", "d": "Vraća referencu <PERSON>nu tekstualnim stringom", "ad": "je referenca na ćeliju koja sadrži referencu u stilu A1 - ili R1C1, ime definisano kao referenca, ili referenca na ćeliju kao tekstualni niz!je logička vrednost koja određuje vrstu reference u Ref_tekst: R1C1-stil = NETAČNO; A1-stil = TAČNO ili izostavljen"}, "LOOKUP": {"a": "(vrednost_pretrage; vektor_pretrage; [vektor_rezultata]; vrednost_pretrage; niz)", "d": "Traži vrednost ili iz jednog reda ili opsega jedne kolone ili iz niza. Obezbeđeno za kompatibilnost unazad", "ad": "je vrednost koju PRONAĐI traži u Vektor_preetrage i može biti broj, tekst, logička vrednost ili ime ili referenca na vrednost!je opseg koji sadrži samo jedan red ili jednu kolonu teksta, brojeva ili logičkih vrednosti, postavljenih u rastućem redosledu!je opseg koji sadrži samo jedan red ili kolonu, ista veličina kao Vektor_preterage!je vrednost koju PRONAĐI traži u Nizu i može biti broj, tekst, logička vrednost ili ime ili referenca na vrednost!je niz ćelija koje sadrže tekst, broj ili logičke vrednosti koje želite da uporedite sa vrednost_pretrage"}, "MATCH": {"a": "(vrednost_pretrage; niz_pretrage; [tip_podudaranja])", "d": "Vraća relativnu poziciju stavke u nizu koja odgovara specificiranoj vrednosti u određenom redosledu", "ad": "je vrednost koju koristite da biste pronašli vrednost koju želite u nizu, broj, tekst ili logička vrednost, ili referenca na jednu od ovih!je susedni opseg ćelija koje sadrže moguće vrednosti pretrage, niz vrednosti ili referencu na niz! je broj 1, 0 ili -1 koji označava koju vrednost treba vratiti."}, "OFFSET": {"a": "(referen<PERSON>; redovi; kolone; [visina]; [širina])", "d": "Vraća referencu na opseg koji je određen broj redova i kolona udaljen od date reference", "ad": "je referenca na koju želite da se zasniva pomeranje, referenca na ćeliju ili opseg susednih ćelija!je broj redova, gore ili dole, na koji želite da se gornja leva ćelija rezultata odnosi!je broj kolona, levo ili desno, na koje želite da se gornja leva ćelija rezultata odnosi!je visina, u broju redova, da želite da rezultat bude, iste visine kao Referenca ako je izostavljena!je širina, u broju kolona, da želite da rezultat bude, iste širine kao Referenca ako je izostavljen"}, "ROW": {"a": "([referenca])", "d": "Vraća broj reda reference", "ad": "je ćelija ili jedan opseg ćelija za koje želite broj reda; ako je izostavl<PERSON>, vraća ćeliju koja sadrži funkciju RED"}, "ROWS": {"a": "(niz)", "d": "Vraća broj redova u referenci ili nizu", "ad": "je niz, formula niza ili referenca na niz ćelija za koje želite broj redova"}, "TRANSPOSE": {"a": "(niz)", "d": "Pretvara vertikalni opseg ćelija u horizontalni opseg, ili obrnuto", "ad": "je opseg ćelija na radnom listu ili niz vrednosti koje želi<PERSON> da prenesete"}, "UNIQUE": {"a": "(niz; [po_koloni]; [tačno_jednom])", "d": "Vraća jedinstvene vrednosti iz opsega ili niza.", "ad": "opseg ili niz iz kojeg se vraćaju jedinstveni redovi ili kolone!je logička vrednost: uporedite redove jedne protiv drugih i vratite jedinstvene redove = TAČNO ili izostavljene; uporedite kolone jedne protiv drugih i vratite jedinstvene kolone = TAČNO!je logička vrednost: vratite redove ili kolone koje se javljaju tačno jednom iz niza = TAČNO; vratite sve različite redove ili kolone iz niza = NETAČNO ili izostavljeno"}, "VLOOKUP": {"a": "(vrednost_za_pretragu; tabela_niz; broj_kolone; [opseg_pretrage])", "d": "Traži vrednost u krajnjoj levoj koloni tabele i zatim vraća vrednost u istom redu iz kolone koju navedete. Po podrazumevanju, tabela mora biti sortirana u rastućem redosledu", "ad": "je vrednost koja se nalazi u prvoj koloni tabele, a može biti vrednost, referenca ili tekstualni niz!je tabela teksta, brojeva ili logičkih vrednosti, u kojoj se podaci preuzimaju. Tabela_niza može biti referenca na opseg ili ime opsega!je broj kolone u tabeli_niza iz koje treba da se vrati odgovarajuća vrednost. Prva kolona vrednosti u tabeli je kolona 1!je logička vrednost: da pronađete najbliže podudaranje u prvoj koloni (sortirano u uzlaznom redosledu) = TAČNO ili izostavljeno; pronađi tačno podudaranje = NETAČNO"}, "XLOOKUP": {"a": "(vrednost_za_pretragu; niz_za_pretragu; povratni_niz; [ako_nije_prona<PERSON><PERSON>]; [način_podudaranja]; [način_pretrage])", "d": "Pretražuje opseg ili niz radi pronalaženja podudaranja i vraća odgovarajuću stavku iz drugog opsega ili niza. Po podrazumevanju se koristi tačno podudaranje", "ad": "je vrednost za pretragu!je niz ili opseg za pretragu!je niz ili opseg koji se vraća!vraćeno ako se ne nađe poklapanje!navesti kako da se vrednost_za_pretragu upoređuje sa vrednostima u niz_za_pretragu!navesti režim pretrage koji će se koristiti. Podrazumevano, prva do poslednja pretraga će se koristiti"}, "CELL": {"a": "(tip_informacije; [referenca])", "d": "Vraća informacije o formatiranju, lokaciji ili sadržaju ćelije", "ad": "je tekstualna vrednost koja određuje koju vrstu informacija o ćeliji želite da vratite!ćelija o kojoj želite informacije"}, "ERROR.TYPE": {"a": "(vrednost_greške)", "d": "Vraća broj koji odgovara vrednosti greške.", "ad": "je vrednost greške za koju želite identifikacioni broj, i može biti stvarna vrednost greške ili referenca na ćeliju koja sadrži vrednost greške"}, "ISBLANK": {"a": "(vrednost)", "d": "Proverava da li je referenca na praznu ćeliju i vraća TRUE (TAČNO) ili FALSE (NETAČNO)", "ad": "je ćelija ili ime koje se odnosi na ćeliju koju želite testirati"}, "ISERR": {"a": "(vrednost)", "d": "Proverava da li je vrednost greška osim #N/A, i vraća TRUE (TAČNO) ili FALSE (NETAČNO)", "ad": "je vrednost koju želite da testirate. Vrednost se može odnositi na ćeliju, formulu ili ime koje se odnosi na ćeliju, formulu ili vrednost"}, "ISERROR": {"a": "(vrednost)", "d": "Proverava da li je vrednost greška i vraća TRUE (TAČNO) ili FALSE (NETAČNO)", "ad": "je vrednost koju želite da testirate. Vrednost se može odnositi na ćeliju, formulu ili ime koje se odnosi na ćeliju, formulu ili vrednost"}, "ISEVEN": {"a": "(broj)", "d": "Vraća TRUE (TAČNO) ako je broj paran", "ad": "je vrednost za testiranje"}, "ISFORMULA": {"a": "(referenca)", "d": "Proverava da li je referenca na ćeliju koja sadrži formulu i vraća TRUE (TAČNO) ili FALSE (NETAČNO)", "ad": "je referenca na ćeliju koju želite da testirate. Referenca može biti referenca ćelije, formula ili ime koje se odnosi na ćeliju"}, "ISLOGICAL": {"a": "(vrednost)", "d": "Proverava da li je vrednost logička vrednost (TRUE (TAČNA) ili FALSE (NETAČNA)) i vraća TRUE (TAČNO) ili FALSE (NETAČNO)", "ad": "je vrednost koju želite da testirate. Vrednost se može odnositi na ćeliju, formulu ili ime koje se odnosi na ćeliju, formulu ili vrednost"}, "ISNA": {"a": "(vrednost)", "d": "Proverava da li je vrednost #N/A i vraća TRUE (TAČNO) ili FALSE (NETAČNO)", "ad": "je vrednost koju želite da testirate. Vrednost se može odnositi na ćeliju, formulu ili ime koje se odnosi na ćeliju, formulu ili vrednost"}, "ISNONTEXT": {"a": "(vrednost)", "d": "Proverava da li vrednost nije tekst (prazne ćelije nisu tekst) i vraća TRUE (TAČNO) ili FALSE (NETAČNO)", "ad": "je vrednost koju želite testirati: ćelija; formula; ili ime koje se odnosi na ćeliju, formulu ili vrednost"}, "ISNUMBER": {"a": "(vrednost)", "d": "Proverava da li je vrednost broj i vraća TRUE (TAČNO) ili FALSE (NETAČNO)", "ad": "je vrednost koju želite da testirate. Vrednost se može odnositi na ćeliju, formulu ili ime koje se odnosi na ćeliju, formulu ili vrednost"}, "ISODD": {"a": "(broj)", "d": "Vraća TRUE (TAČNO) ako je broj neparan", "ad": "je vrednost za testiranje"}, "ISREF": {"a": "(vrednost)", "d": "Proverava da li je vrednost referenca i vraća TRUE (TAČNO) ili FALSE (NETAČNO)", "ad": "je vrednost koju želite da testirate. Vrednost se može odnositi na ćeliju, formulu ili ime koje se odnosi na ćeliju, formulu ili vrednost"}, "ISTEXT": {"a": "(vrednost)", "d": "Proverava da li je vrednost tekst i vraća TRUE (TAČNO) ili FALSE (NETAČNO)", "ad": "je vrednost koju želite da testirate. Vrednost se može odnositi na ćeliju, formulu ili ime koje se odnosi na ćeliju, formulu ili vrednost"}, "N": {"a": "(vrednost)", "d": "Pretvara vrednost koja nije broj u broj, datume u serijske brojeve, TRUE (TAČNO) u 1, bilo šta drugo u 0 (nula)", "ad": "je vrednost koju želite konvertovati"}, "NA": {"a": "()", "d": "Vraća vrednost greške #N/A (vrednost nije dostupna)", "ad": ""}, "SHEET": {"a": "([vrednost])", "d": "Vraća broj lista referenciranog lista", "ad": "je ime lista ili reference za koju želite broj lista. Ako je izostavljen, broj lista koji sadrži funkciju se vraća"}, "SHEETS": {"a": "([referenca])", "d": "Vraća broj listova u referenci", "ad": "je referenca za koju želite da znate broj listova koje sadrži. Ako je izostavljen, broj listova u radnoj svesci koja sadrži funkciju se vraća"}, "TYPE": {"a": "(vrednost)", "d": "Vraća ceo broj koji predstavlja tip podataka vrednosti: broj = 1; tekst = 2; logička vrednost = 4; vrednost greške = 16; niz = 64; slo<PERSON>eni podaci = 128", "ad": "može biti bilo koja vrednost"}, "AND": {"a": "(logički1; [logički2]; ...)", "d": "Proverava da li su svi argumenti TRUE (TAČNI) i vraća TRUE (TAČNO) ako su svi argumenti TRUE (TAČNI)", "ad": "su od 1 do 255 uslova koje želite da testirate koji mogu biti TAČNO ili NETAČNO i mogu biti logičke vrednosti, nizovi ili reference"}, "FALSE": {"a": "()", "d": "Vraća logičku vrednost FALSE (NETAČNO)", "ad": ""}, "IF": {"a": "(logički_test; [vrednost_ako_true]; [vrednost_ako_false])", "d": "Proverava da li je uslov ispunjen i vraća jednu vrednost ako je TRUE (TAČNO), a drugu vrednost ako je FALSE (NETAČNO)", "ad": "je bilo koja vrednost ili izraz koji se može proceniti na TAČNO ili NETAČNO!je vrednost koja se vraća ako je Logički_test TAČNO. Ako se izosta<PERSON>, vraća se TAČNO. Možete ugnezditi do sedam IF funkcija!je vrednost koja se vraća ako je Logički_test NETAČNO. A<PERSON> je <PERSON>l<PERSON>, NETAČNO se vraća"}, "IFS": {"a": "(logički_test; vrednost_ako_true; ...)", "d": "Proverava da li je jedan ili više uslova ispunjeno i vraća vrednost koja odgovara prvom TRUE (TAČNOM) uslovu", "ad": "je bilo koja vrednost ili izraz koji se može proceniti na TAČNO ili NETAČNO!je vrednost vraćena ako je Logički_test TAČNO"}, "IFERROR": {"a": "(vrednost; vrednost_ako_greška)", "d": "Vraća vrednost_ako_greška ako je izraz greška, a vrednost izraza u suprotnom", "ad": "je bilo koja vrednost ili izraz ili referenca!je bilo koja vrednost ili izraz ili referenca"}, "IFNA": {"a": "(vrednost; vrednost_ako_na)", "d": "Vraća vrednost koju navedete ako izraz rezultira sa #N/A, u suprotnom vraća rezultat izraza", "ad": "je bilo koja vrednost ili izraz ili referenca!je bilo koja vrednost ili izraz ili referenca"}, "NOT": {"a": "(logički)", "d": "Menja FALSE (NETAČNO) u TRUE (TAČNO), ili TRUE (TAČNO) u FALSE (NETAČNO)", "ad": "je vrednost ili izraz koji se može proceniti na TAČNO ili NETAČNO"}, "OR": {"a": "(logički1; [logički2]; ...)", "d": "Proverava da li je bilo koji od argumenata TRUE (TAČAN) i vraća TRUE (TAČNO) ili FALSE (NETAČNO). Vraća FALSE (NETAČNO) samo ako su svi argumenti FALSE (NETAČNI)", "ad": "su od 1 do 255 uslova koje želite da testirate koji mogu biti TAČNO ili NETAČNO"}, "SWITCH": {"a": "(izraz; vrednost1; rezultat1; [podrazumevana_vrednost_ili_vrednost2]; [rezultat2]; ...)", "d": "Procenjuje izraz prema listi vrednosti i vraća rezultat koji odgovara prvoj vrednosti koja se poklapa. Ako nema poklapanja, vraća se opcionalna podrazumevana vrednost", "ad": "je izraz koji treba proceniti!je vrednost koja se upoređuje sa izrazom!je rezultat koji se vraća ako odgovarajuća vrednost odgovara izrazu"}, "TRUE": {"a": "()", "d": "Vraća logičku vrednost TRUE (TAČNO)", "ad": ""}, "XOR": {"a": "(logički1; [logički2]; ...)", "d": "Vraća logičku vrednost 'Ekskluzivno Ili' svih argumenata", "ad": "su od 1 do 254 uslova koje želite da testirate koji mogu biti TAČNO ili NETAČNO i mogu biti logičke vrednosti, nizovi ili reference"}, "TEXTBEFORE": {"a": "(tekst, delimiter, [broj_instanci], [na<PERSON><PERSON>_poklap<PERSON><PERSON>], [poklapan<PERSON>_kraja], [a<PERSON>_ni<PERSON>_prona<PERSON><PERSON>])", "d": "Vraća tekst koji se nalazi pre delimitirajućih karaktera", "ad": "Tekst koji želite da pretražujete za razdvajačem.!Karakter ili string koji će se koristiti kao razdvajač.!Željena pojava razdvajača. Podrazumevano je 1. Negativan broj traži od kraja.!Pretražuje tekst za poklapanje razdvajača. Po defaultu, podudaranje osetljivo na velika i mala slova je urađeno.!se poklapa sa razdvajačem na kraju teksta. Po defaultu, oni se ne poklapaju.!Vraća se ako se ne pronađe podudaranje. Podrazumevano, #N/A se vraća."}, "TEXTAFTER": {"a": "(tekst, delimiter, [broj_instanci], [na<PERSON><PERSON>_poklap<PERSON><PERSON>], [poklapan<PERSON>_kraja], [a<PERSON>_ni<PERSON>_prona<PERSON><PERSON>])", "d": "Vraća tekst koji se nalazi posle delimitirajućih karaktera", "ad": "Tekst koji želite da pretražujete za razdvajačem.!Karakter ili string koji će se koristiti kao razdjelnik.!Željena pojava razdvajača. Podrazumevano je 1. Negativan broj traži od kraja.!Pretražuje tekst za poklapanje razdvajača. Po defaultu, podudaranje osetljivo na velika i mala slova je urađeno.!se poklapa sa razdvajačem na kraju teksta. Po defaultu, oni se ne poklapaju.!Vraća se ako se ne pronađe podudaranje. Podrazumevano, #N/A se vraća."}, "TEXTSPLIT": {"a": "(tekst, kol_delimiter, [red_delimiter], [i<PERSON><PERSON><PERSON><PERSON>_prazne], [na<PERSON><PERSON>_poklap<PERSON>], [dopuni_sa])", "d": "Razdvaja tekst u redove ili kolone koristeći delimitere", "ad": "Tekst za podelu!Karakter ili niz za podelu kolona.!Karakter ili niz za podelu redova po.!da ignorišete prazne ćelije. Podrazumevano je NETAČNO.!Pretražuje tekst za poklapanje razdvajača. Po defaultu, podudaranje osetljivo na velika i mala slova je urađeno.!Vrednost koja se koristi za podlogu. Podrazumevano se koristi #N/A."}, "WRAPROWS": {"a": "(vektor, broj_zavi<PERSON>, [dopuni_sa])", "d": "Zavija red ili kolonu vektora nakon određenog broja vrednosti", "ad": "Vektor ili referenca za zavijanje.!Maksimalan broj vrednosti po redu.!Vrednost kojom će se popuniti. Podrazumevano je #N/A."}, "VSTACK": {"a": "(niz1, [niz2], ...)", "d": "Vertikalno slaže nizove u jedan niz", "ad": "<PERSON>z ili referenca koja se slaže."}, "HSTACK": {"a": "(niz1, [niz2], ...)", "d": "Horizontalno slaže nizove u jedan niz", "ad": "<PERSON>z ili referenca koja se slaže."}, "CHOOSEROWS": {"a": "(niz, red_broj1, [red_broj2], ...)", "d": "Vraća redove iz niza ili reference", "ad": "Niz ili referenca koja sadrži redove koji se vraćaju.!Broj reda koji se vraća."}, "CHOOSECOLS": {"a": "(niz, kol_broj1, [kol_broj2], ...)", "d": "Vraća kolone iz niza ili reference", "ad": "Niz ili referenca koja sadrži kolone koje treba vratiti.!Broj kolone koja se vraća."}, "TOCOL": {"a": "(niz, [i<PERSON><PERSON><PERSON>i], [skan<PERSON><PERSON>_po_kolonama])", "d": "Vraća niz kao jednu kolonu", "ad": "Niz ili referenca da se vrati kao kolona.!Da li da ignorišemo određene vrste vrednosti. <PERSON> defaultu, nijedna vrednost se ne ignoriše.!Skenirajte niz po koloni. <PERSON> defaultu, niz se skenira po redovima."}, "TOROW": {"a": "(niz, [i<PERSON><PERSON><PERSON>i], [skan<PERSON><PERSON>_po_kolonama])", "d": "Vraća niz kao jedan red", "ad": "Niz ili referenca da se vrati kao red.!Da li da ignorišemo određene vrste vrednosti. <PERSON> defaultu, nijedna vrednost se ne ignoriše.!Skenirajte niz po koloni. <PERSON> defaultu, niz se skenira po redovima."}, "WRAPCOLS": {"a": "(vektor, broj_zavi<PERSON>, [dopuni_sa])", "d": "Zavija red ili kolonu vektora nakon određenog broja vrednosti", "ad": "Vektor ili referenca za zavijanje.!Maksimalan broj vrednosti po koloni.!Vrednost sa kojom će se popuniti. Podrazumevano je #N/A."}, "TAKE": {"a": "(niz, redovi, [kolone])", "d": "Vraća redove ili kolone sa početka ili kraja niza", "ad": "Niz iz kojeg se uzimaju redovi ili kolone.!B<PERSON>j redova koje treba uzeti. Negativna vrednost uzima od kraja niza.!Broj kolona koje treba uzeti. Negativna vrednost uzima od kraja niza."}, "DROP": {"a": "(niz, redovi, [kolone])", "d": "Izbacuje redove ili kolone sa početka ili kraja niza", "ad": "Niz iz kojeg se ispuštaju redovi ili kolone.!Broj redova za ispuštanje. Negativna vrednost pada sa kraja niza.!Broj kolona koje treba ispustiti. Negativna vrednost pada sa kraja niza."}, "SEQUENCE": {"a": "(redovi, [kolone], [poč<PERSON>k], [korak])", "d": "<PERSON>ra<PERSON><PERSON> se<PERSON><PERSON> broje<PERSON>", "ad": "broj redova za povratak!broj kolona za povratak!prvi broj u nizu!iznos za povećanje svake naredne vrednosti u nizu"}, "EXPAND": {"a": "(niz, redovi, [kolone], [dopuni_sa])", "d": "Proširuje niz na određene dimenzije", "ad": "Niz za proširenje.!Broj redova u proširenom nizu. A<PERSON> nedos<PERSON>, redovi neće biti prošireni.!Broj kolona u proširenom nizu. A<PERSON> nedosta<PERSON>, kolone neće biti proširene.!Vrednost sa kojom će se popuniti. Podrazumevano je #N/A."}, "XMATCH": {"a": "(vrednost_pretrage, niz_pretrage, [način_poklap<PERSON>ja], [način_pretrage])", "d": "Vraća relativnu poziciju stavke u nizu. Podrazumevano se koristi tačno poklapanje", "ad": "je vrednost za pretragu!je niz ili opseg za pretragu!navesti kako da se poklapa vrednost_pretrage sa vrednostima u nizu_pretrage!navesti režim pretrage da se koristi. Podrazumevano, prva do poslednja pretraga će se koristiti"}, "FILTER": {"a": "(niz, ukl<PERSON><PERSON><PERSON>, [ako_je_prazno])", "d": "Filtrira opseg ili niz", "ad": "opseg ili niz za filtriranje!niz logičkih vrednosti gde TAČNO predstavlja red ili kolonu za zadržavanje!vraća se ako se ne zadrže stavke"}, "ARRAYTOTEXT": {"a": "(niz, [format])", "d": "Vraća tekstualnu reprezentaciju niza", "ad": "niz koji će se predstaviti kao tekst!format teksta"}, "SORT": {"a": "(niz, [indeks_sortiranja], [redosled_sortiranja], [po_kolonama])", "d": "Sortira opseg ili niz", "ad": "opseg ili niz za sortiranje!broj koji označava red ili kolonu za sortiranje!broj koji označava željeni redosled sortiranja; 1 za uzlazni redosled (podrazumevano), -1 za silazni redosled!logička vrednost koja ukazuje na željeni pravac sortiranja: NETAČNO za sortiranje po redu (podrazumevano), TAČNO za sortiranje po koloni"}, "SORTBY": {"a": "(niz, po_nizu, [redosled_sortiranja], ...)", "d": "Sortira opseg ili niz na osnovu vrednosti u odgovarajućem opsegu ili nizu", "ad": "opseg ili niz za sortiranje!opseg ili niz za sortiranje!broj koji označava željeni redosled sortiranja; 1 za uzlazni redosled (podrazumevano), -1 za opadajući redosled"}, "GETPIVOTDATA": {"a": "(polje_podataka; pivot_tabela; [polje]; [stavka]; ...)", "d": "Ekstrahuje podatke iz PivotTable tabele", "ad": "je ime polja podataka iz kojeg treba izvući podatke!je referenca na ćeliju ili opseg ćelija u PivotTable koji sadrži podatke koje želite da preuzmete!polje na koje se odnosi!polje na koje se odnosi"}, "IMPORTRANGE": {"a": "(url_tabele, opseg_stringa)", "d": "Uvozi opseg ćelija iz određene tabele", "ad": "je URL tabele odakle će podaci biti uvezeni!je opseg za uvoz"}}