{"DATE": {"a": "(leto; mesec; dan)", "d": "<PERSON><PERSON> število, ki predstavlja datum v kodi za datum in uro.", "ad": "je število od 1900 ali 1904 (kar je odvisno od sistema datumov delovnega zvezka) do 9999!je število od 1 do 12, ki predstavlja mesec v letu.!je število od 1 do 31, ki predstavlja dan v mesecu."}, "DATEDIF": {"a": "(za<PERSON><PERSON>ni_datum; končni_datum; enota)", "d": "Izračuna število dni, mesecev ali let med dvema datumoma", "ad": "<PERSON>tum, ki predstavlja prvi ali začetni datum danega obdobja!Datum, ki predstavlja zadnji, končni datum nekega obdobja!Vrsta informacij, ki jo želite pridobiti"}, "DATEVALUE": {"a": "(besedilo_datuma)", "d": "Pretvori datum v obliki besedila v število, ki predstavlja datum v kodi za datum in uro.", "ad": "je <PERSON><PERSON><PERSON>, ki predstavlja datum v obliki datuma med 1.1.1900 ali 1.1.1904 (kar je odvisno od sistema datumov v delovnem zvezku) in 31.12.9999 v programu Spreadsheet Editor"}, "DAY": {"a": "(se<PERSON><PERSON>ska_številka)", "d": "Vrne dan v mesecu, število med 1 in 31.", "ad": "je število v datumsko-časovni kodi, ki jo uporablja Spreadsheet Editor"}, "DAYS": {"a": "(končni_datum; začetni_datum)", "d": "<PERSON><PERSON>o dni med dvema datumoma.", "ad": "za<PERSON>etni_datum in končni_datum sta datuma, katerih število dni želite dobiti!začetni_datum in končni_datum sta datuma, katerih število dni želite dobiti"}, "DAYS360": {"a": "(za<PERSON><PERSON>ni_datum; končni_datum; [metoda])", "d": "<PERSON><PERSON> število dni med dvema datumoma v 360-dnevnem letu (dvanajst 30-dnevnih mesecev).", "ad": "Za<PERSON>etni_datum in Končni_datum sta dva datuma, med katerima želite poznati število dni.!Začetni_datum in Končni_datum sta dva datuma, med katerima želite poznati število dni.!je logična vrednost, ki določa metodo za izračun: ZDA (NASD) = FALSE ali izpuščeno; Evropska = TRUE."}, "EDATE": {"a": "(za<PERSON><PERSON>ni_datum; meseci)", "d": "<PERSON>rne zaporedno število datum<PERSON>, ki je do<PERSON><PERSON><PERSON>tevilo mesecev pred ali po začetnem datumu", "ad": "je zaporedna številka datuma, ki predstavlja začetni datum! je število mesecev pred ali po začetnem datumu"}, "EOMONTH": {"a": "(za<PERSON><PERSON>ni_datum; meseci)", "d": "Vrne zaporedno število zadnjega dneva meseca pred ali po navedenem številu mesecev", "ad": "je zaporedna številka datuma, ki predstavlja začetni datum! je število mesecev pred ali po začetnem datumu"}, "HOUR": {"a": "(se<PERSON><PERSON>ska_številka)", "d": "<PERSON><PERSON>, ki je število med 0 (<PERSON><PERSON><PERSON><PERSON>) in 23 (23:00).", "ad": "je število v datumsko-časovni kodi, ki jo uporablja Spreadsheet Editor, ali be<PERSON><PERSON> v časovni obliki, na primer 16:48:00 ali 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(datum)", "d": "Vrne število ISO številke tedna v letu za dani datum", "ad": "je datumsko-<PERSON><PERSON><PERSON>na koda, ki jo uporablja Spreadsheet Editor za izračun datuma in ure"}, "MINUTE": {"a": "(se<PERSON><PERSON>ska_številka)", "d": "<PERSON><PERSON> minute, ki so c<PERSON> med 0 in 59.", "ad": "je število v datumsko-časovni kodi, ki jo uporablja Spreadsheet Editor, ali be<PERSON><PERSON> v časovni obliki, na primer 16:48:00 ali 4:48:00 PM"}, "MONTH": {"a": "(se<PERSON><PERSON>ska_številka)", "d": "<PERSON><PERSON> mese<PERSON>, ki je število od 1 (januar) do 12 (december).", "ad": "je število v datumsko-časovni kodi, ki jo uporablja Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(za<PERSON><PERSON>ni_datum; končni_datum; [prazniki])", "d": "<PERSON><PERSON> celot<PERSON>h delovnih dni med dvema datumoma", "ad": "je zaporedna številka datuma, ki predstavlja začetni datum!je zaporedna številka datuma, ki predstavlja končni datum!je izbirni niz ene ali več zaporednih številk datuma, ki bodo izključene iz delovnega koledarja, kot so dr<PERSON><PERSON>ni in zvezni prazniki ter porodniški dopusti"}, "NETWORKDAYS.INTL": {"a": "(za<PERSON><PERSON>ni_datum; končni_datum; [vikend]; [prazniki])", "d": "<PERSON><PERSON> celo<PERSON>h delovnih dni med dvema datumoma s parametri vikendov po meri.", "ad": "je zaporedna številka datuma, ki predstavlja začetni datum.!je zaporedna številka datuma, ki predstavlja končni datum.!je število ali nabor, ki do<PERSON>, kdaj nastopijo vikendi.!je izbirni nabor ene ali več zaporednih številk datuma, ki bodo izključene iz delovnega koledarja, kot so dr<PERSON>avni in zvezni prazniki ter porodniški dopusti."}, "NOW": {"a": "()", "d": "<PERSON>rne trenutni datum in uro, oblikovano kot datum in ura.", "ad": ""}, "SECOND": {"a": "(se<PERSON><PERSON>ska_številka)", "d": "<PERSON><PERSON> se<PERSON>, ki so celo <PERSON> med 0 in 59.", "ad": "je število v datumsko-časovni kodi, ki jo uporablja Spreadsheet Editor, ali be<PERSON><PERSON> v časovni obliki, na primer 16:48:23 ali 4:48:47 PM"}, "TIME": {"a": "(ura; minuta; sekunda)", "d": "Pretvori ure, minute in sekunde, navedene kot števila, v zaporedno število v časovni obliki.", "ad": "je število med 0 in 23, ki predstavlja uro.!je število med 0 in 59, ki predstavlja minuto.!je število med 0 in 59, ki predstavlja sekundo."}, "TIMEVALUE": {"a": "(besedilo_ure)", "d": "Pretvori uro, zapisano v besedilni obliki, v zaporedno število za uro; števila od 0 (12:00:00) do 0,999988426 (23:59:59). Po vnosu formule oblikujte število z obliko zapisa ure.", "ad": "je besedilni niz, ki navaja uro v eni od oblik zapisa ure programa Spreadsheet Editor (informacije o datumu v nizu so prezrte)."}, "TODAY": {"a": "()", "d": "<PERSON><PERSON> trenutni datum, oblikovan kot datum.", "ad": ""}, "WEEKDAY": {"a": "(se<PERSON><PERSON><PERSON>_številka; [vrsta_rezultata])", "d": "Vrne število od 1 do 7, ki označuje dan v tednu datuma.", "ad": "je <PERSON><PERSON><PERSON><PERSON>, ki predstavlja datum.!je število: za nedeljo = 1 do sobote = 7, uporabite 1; za ponedeljek = 1 do nedelje = 7, uporabite 2; za ponedeljek = 0 do nedelje = 6, uporabite 3"}, "WEEKNUM": {"a": "(se<PERSON><PERSON><PERSON>_številka; [vrsta_vrednosti])", "d": "Vrne število tednov v letu", "ad": "je datumsko-<PERSON><PERSON>ovna koda, ki jo uporablja program Spreadsheet Editor za izračune datuma in časa!je število (1 ali 2), ki določa vrsto vrnjene vrednosti"}, "WORKDAY": {"a": "(za<PERSON><PERSON><PERSON>_datum; dnevi; [prazniki])", "d": "Vrne zaporedno številko datuma pred ali po navedenem številu delovnih dni", "ad": "je zaporedna številka datuma, ki predstavlja začetni datum!je število delovnih dni pred ali po začetnem datumu!je izbirna matrika ene ali več zaporednih številk datuma, ki bodo izključene iz delovnega koledarja, kot so dr<PERSON><PERSON>ni in zvezni prazniki ter porodniški dopusti"}, "WORKDAY.INTL": {"a": "(za<PERSON><PERSON><PERSON>_datum; dnevi; [vikend]; [prazniki])", "d": "Vrne zaporedno številko datuma pred ali po navedenem številu delovnih dni s parametri vikendov po meri", "ad": "je zaporedna številka datuma, ki predstavlja začetni datum!je število delovnih dni pred ali po začetnem datumu!je število ali nabor, ki dolo<PERSON>a, kdaj nastopijo vikendi!je izbirna matrika za več zaporednih številk datuma, ki bodo izključeni iz delovnega koledarja, na primer državni in zvezni prazniki ter porodniški dopusti"}, "YEAR": {"a": "(se<PERSON><PERSON>ska_številka)", "d": "<PERSON><PERSON> leto da<PERSON>, ki je celo števil<PERSON> v intervalu od 1900 - 9999.", "ad": "je število v datumsko-časovni kodi, ki jo uporablja Spreadsheet Editor"}, "YEARFRAC": {"a": "(za<PERSON><PERSON><PERSON>_datum; končni_datum; [osnova])", "d": "<PERSON><PERSON> ulomek leta, ki predstavlja število celih dni med začetnim in končnim datumom", "ad": "je zaporedna številka datuma, ki predstavlja začetni datum!je zaporedna številka datuma, ki predstavlja končni datum!je vrsta na osnovi štetja dni za uporabo"}, "BESSELI": {"a": "(x; n)", "d": "Vrne spremenjeno Besselovo funkcijo In(x)", "ad": "je vred<PERSON>t, pri kateri ovrednotite funkcijo!je red Besselove funkcije"}, "BESSELJ": {"a": "(x; n)", "d": "Vrne Besselovo funkcijo Jn(x)", "ad": "je vred<PERSON>t, pri kateri ovrednotite funkcijo!je red Besselove funkcije"}, "BESSELK": {"a": "(x; n)", "d": "Vrne spremenjeno Besselovo funkcijo Kn(x)", "ad": "je vred<PERSON>t, pri kateri ovrednotite funkcijo!je red funkcije"}, "BESSELY": {"a": "(x; n)", "d": "<PERSON>rne Besselovo funkcijo Yn(x)", "ad": "je vred<PERSON>t, pri kateri ovrednotite funkcijo!je red funkcije"}, "BIN2DEC": {"a": "(števil<PERSON>)", "d": "Pretvori dvojiško število v desetiško", "ad": "je d<PERSON><PERSON><PERSON><PERSON>, ki ga želite pretvoriti"}, "BIN2HEX": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori dvojiško število v šestnajstiško", "ad": "je dvo<PERSON><PERSON><PERSON>, ki ga želite pretvoriti!je število znakov za uporabo"}, "BIN2OCT": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori dvojiško število v osmiško", "ad": "je dvo<PERSON><PERSON><PERSON>, ki ga želite pretvoriti!je število znakov za uporabo"}, "BITAND": {"a": "(število1; število2)", "d": "Vrne bitno vrednost »And« dveh števil", "ad": "je decimalni predstavnik binarnega števila, ki ga želite oceniti!je decimalni predstavnik binarnega števila, ki ga želite ovrednotiti"}, "BITLSHIFT": {"a": "(število; shift_amount)", "d": "<PERSON><PERSON>, ki jih biti shift_amount premaknejo na levo", "ad": "je decimalni predstavnik binarnega števila, ki ga želite ovrednotiti!je š<PERSON><PERSON><PERSON> bitov, ki jih želite premakniti"}, "BITOR": {"a": "(število1; število2)", "d": "Vrne bitno vrednost »Or« dveh števil", "ad": "je decimalni predstavnik binarnega števila, ki ga želite oceniti!je decimalni predstavnik binarnega števila, ki ga želite ovrednotiti"}, "BITRSHIFT": {"a": "(število; shift_amount)", "d": "<PERSON><PERSON>, ki jih biti shift_amount premaknejo na desno", "ad": "je decimalni predstavnik binarnega števila, ki ga želite ovrednotiti!je š<PERSON><PERSON><PERSON> bitov, ki jih želite premakniti"}, "BITXOR": {"a": "(number1; number2)", "d": "Vrne bitno vrednost »Exclusive Or« dveh števil", "ad": "je decimalni predstavnik binarnega števila, ki ga želite oceniti!je decimalni predstavnik binarnega števila, ki ga želite ovrednotiti"}, "COMPLEX": {"a": "(realno_št; i_št; [pripona])", "d": "Pretvori realne in imaginarne koeficiente v kompleksna števila", "ad": "je realni koeficient kompleksnega števila! je imaginarni koeficient kompleksnega števila!je pripona za imaginarno komponento kompleksnega števila"}, "CONVERT": {"a": "(š<PERSON><PERSON><PERSON>; od_enote; do_enote)", "d": "Pretvori število iz enega merskega sistema v drugega", "ad": "je vrednost v iz_enot za pretvorbo!je enot za število!je enot za rezultat"}, "DEC2BIN": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori desetiško število v dvojiško", "ad": " je deseti<PERSON><PERSON>, ki ga želite pretvoriti!je število znakov za uporabo"}, "DEC2HEX": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori desetiško število v šestnajstiško", "ad": "je desetiško celo <PERSON>, ki ga želite pretvoriti!je število znakov za uporabo"}, "DEC2OCT": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori desetiško število v osmiško", "ad": "je desetiško celo <PERSON>, ki ga želite pretvoriti!je število znakov za uporabo"}, "DELTA": {"a": "(število1; [število2])", "d": "<PERSON><PERSON><PERSON><PERSON>, ali sta dve <PERSON><PERSON> enaki", "ad": "je prvo število!je drugo število"}, "ERF": {"a": "(spodnja_meja; [zgor<PERSON>_meja])", "d": "<PERSON><PERSON> funk<PERSON> napake", "ad": "je spodnja meja za integriranje ERF!je zgornja meja za integriranje ERF"}, "ERF.PRECISE": {"a": "(X)", "d": "<PERSON><PERSON> funk<PERSON> napake", "ad": "je spodnja meja za integriranje ERF.PRECISE"}, "ERFC": {"a": "(x)", "d": "<PERSON>rne komplementarno funkcijo napake", "ad": "je spodnja meja za integriranje ERF"}, "ERFC.PRECISE": {"a": "(X)", "d": "<PERSON>rne komplementarno funkcijo napake", "ad": "je spodnja meja za integriranje ERFC.PRECISE"}, "GESTEP": {"a": "(število; [korak])", "d": "<PERSON><PERSON><PERSON><PERSON>, ali je število večje od mejne vrednosti", "ad": "je vrednost, ki se preverja po korakih!je mejna vrednost"}, "HEX2BIN": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori šestnajstiško število v dvojiško", "ad": "je šest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ki ga želite pretvoriti!je število znakov za uporabo"}, "HEX2DEC": {"a": "(števil<PERSON>)", "d": "Pretvori šestnajstiško število v desetiško", "ad": "je š<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ki ga želite pretvoriti"}, "HEX2OCT": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori šestnajstiško število v osmiško", "ad": " je šest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ki ga želite pretvoriti!je število znakov za uporabo"}, "IMABS": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne absolutno vrednost (modul) kompleksnega števila", "ad": "je komple<PERSON><PERSON>, katerega absolutno vrednost želite"}, "IMAGINARY": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne imaginarni koeficient kompleksnega števila", "ad": "je komple<PERSON><PERSON>, katerega imaginarni koe<PERSON>"}, "IMARGUMENT": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne argument q, kot, izražen v radianih", "ad": "je komple<PERSON><PERSON>, ka<PERSON><PERSON>a <PERSON> želite"}, "IMCONJUGATE": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne kompleksno izpeljanko kompleksnega števila", "ad": "je kompleks<PERSON>, katerega izpeljanko želite"}, "IMCOS": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON> kosinus kompleksnega števila", "ad": "je kom<PERSON><PERSON><PERSON>, ka<PERSON><PERSON><PERSON> k<PERSON><PERSON>"}, "IMCOSH": {"a": "(števil<PERSON>)", "d": "Vrne hiperbolični kosinus kompleksnega števila", "ad": "je komple<PERSON><PERSON>, ki mu želite izračunati hiperbolični kosinus"}, "IMCOT": {"a": "(inumber)", "d": "Vrne kotangens kompleksnega števila", "ad": "je komple<PERSON><PERSON>, ki mu želite izračunati kotangens"}, "IMCSC": {"a": "(inumber)", "d": "Vrne kosekans kompleksnega števila", "ad": "je komple<PERSON><PERSON>, ki mu želite izračunati kosekans"}, "IMCSCH": {"a": "(inumber)", "d": "Vrne hiperbolični kosekans kompleksnega števila", "ad": "je komple<PERSON><PERSON>, ki mu želite izračunati hiperbolični kosekans"}, "IMDIV": {"a": "(ištevilo1; ištevilo2)", "d": "Vrne količnik dveh kompleksnih števil", "ad": "je kompleksni števec ali deljenec!je kompleksni imenovalec ali delitelj"}, "IMEXP": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne eksponent kompleksnega števila", "ad": "je komple<PERSON><PERSON>, katerega eksponent ž<PERSON><PERSON>"}, "IMLN": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne naravni logaritem kompleksnega števila", "ad": "je komple<PERSON><PERSON>, katerega naravni logaritem želite"}, "IMLOG10": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne logaritem z osnovo 10 kompleksnega števila", "ad": "je komple<PERSON><PERSON>, katerega navadni logaritem želite"}, "IMLOG2": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne logaritem z osnovo 2 kompleksnega števila", "ad": "je kompleks<PERSON> število, katerega logaritem z osnovo 2 želite"}, "IMPOWER": {"a": "(i<PERSON><PERSON><PERSON><PERSON>; število)", "d": "Vrne kompleksno število potencirano na celo število", "ad": "je kompleksno število, ki ga želite potencirati!je potenca, na katero želite potencirati kompleksno število"}, "IMPRODUCT": {"a": "(iš<PERSON>vilo1; [ištevilo2]; ...)", "d": "Vrne zmnožek 1 do 255 kompleksnih števil", "ad": "<PERSON><PERSON>tevilo1, <PERSON><PERSON><PERSON><PERSON><PERSON>2,... je 1 do 255 kompleksnih števil za množenje."}, "IMREAL": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne realni koeficient kompleksnega števila", "ad": "je komple<PERSON><PERSON>, katerega realni koeficient želite"}, "IMSEC": {"a": "(inumber)", "d": "Vrne sekans kompleksnega števila", "ad": "je komple<PERSON><PERSON>, ki mu želite izračunati sekans"}, "IMSECH": {"a": "(inumber)", "d": "Vrne hiperbolični sekans kompleksnega števila", "ad": "je komple<PERSON><PERSON>, ki mu želite izračunati hiperbolični sekans"}, "IMSIN": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne sinus kompleksnega števila", "ad": "je komple<PERSON><PERSON>, kater<PERSON>a sinus želite"}, "IMSINH": {"a": "(števil<PERSON>)", "d": "Vrne hiperbolični sinus kompleksnega števila", "ad": "je komple<PERSON><PERSON>, ki mu želite izračunati hiperbolični sinus"}, "IMSQRT": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne kvadratni koren kompleksnega števila", "ad": "je komple<PERSON><PERSON>, katerega kvadratni koren želite"}, "IMSUB": {"a": "(ištevilo1; ištevilo2)", "d": "Vrne razliko dveh kompleksnih števil", "ad": "je kompleksno število, od katerega odštejete »ištevilo2«!je kompleksno število, katerega odštejete od »ištevilo1«"}, "IMSUM": {"a": "(iš<PERSON>vilo1; [ištevilo2]; ...)", "d": "Vrne vsoto kompleksnih števil", "ad": "je 1 do 255 kompleksnih števil za seštevanje"}, "IMTAN": {"a": "(inumber)", "d": "Vrne tangens kompleksnega števila", "ad": "je komple<PERSON><PERSON>, ki mu želite izračunati tangens"}, "OCT2BIN": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori osmiško število v dvojiško", "ad": "je osmi<PERSON><PERSON>, ki ga želite pretvoriti!je število znakov za uporabo"}, "OCT2DEC": {"a": "(števil<PERSON>)", "d": "Pretvori osmiško število v desetiško", "ad": "je o<PERSON><PERSON><PERSON><PERSON>, ki ga želite pretvoriti"}, "OCT2HEX": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori osmiško število v šestnajstiško", "ad": "je osmi<PERSON><PERSON>, ki ga želite pretvoriti!je število znakov za uporabo"}, "DAVERAGE": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Izračuna povprečje vrednosti v stolpcu, na seznamu ali v zbirki podatkov, ki ustrezajo navedenim pogojem.", "ad": "je obseg celic, ki sestavlja seznam ali zbirko podatkov. Zbirka podatkov je seznam sorodnih podatkov.!je bodisi oznaka stolpca v dvojnih narekovajih bodisi številka, ki predstavlja položaj stolpca na seznamu.!je obseg celic s pogoji, ki jih določite sami. Obseg vsebuje oznako stolpca in celico pod njo, ki vsebuje pogoj."}, "DCOUNT": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Prešteje celice s števili v polju (stolpcu) zapisov zbirke podatkov, ki ustrezajo pogojem, ki ste jih določili.", "ad": "je obseg celic, ki sestavlja seznam ali zbirko podatkov. Zbirka podatkov je seznam sorodnih podatkov.!je bodisi oznaka stolpca v dvojnih narekovajih bodisi številka, ki predstavlja položaj stolpca na seznamu.!je obseg celic s pogoji, ki jih določite sami. Obseg vsebuje oznako stolpca in celico pod njo, ki vsebuje pogoj."}, "DCOUNTA": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Prešteje neprazne celice v polju (stolpcu) zapisov zbirke podatkov, ki ustrezajo pogojem, ki ste jih določili.", "ad": "je obseg celic, ki sestavlja seznam ali zbirko podatkov. Zbirka podatkov je seznam sorodnih podatkov.!je bodisi oznaka stolpca v dvojnih narekovajih bodisi številka, ki predstavlja položaj stolpca na seznamu.!je obseg celic s pogoji, ki jih določite sami. Obseg vsebuje oznako stolpca in celico pod njo, ki vsebuje pogoj."}, "DGET": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "<PERSON>z zbirke podatkov izvleče en zapis, ki ustreza pogojem, ki ste jih določili.", "ad": "je obmo<PERSON>je celic, ki sestavlja seznam ali zbirko podatkov. Zbirka podatkov je seznam sorodnih podatkov.!je bodisi oznaka stolpca v dvojnih narekovajih bodisi številka, ki predstavlja položaj stolpca na seznamu.!je obseg celic s pogoji, ki jih določite sami. Obseg vsebuje oznako stolpca in celico pod njo, ki vsebuje pogoj."}, "DMAX": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Vrne največje število v polju (stolpcu) zapisov zbirke podatkov, ki ustreza pogojem, ki ste jih določili.", "ad": "je obmo<PERSON>je celic, ki sestavlja seznam ali zbirko podatkov. Zbirka podatkov je seznam sorodnih podatkov.!je bodisi oznaka stolpca v dvojnih narekovajih bodisi številka, ki predstavlja položaj stolpca na seznamu.!je obseg celic s pogoji, ki jih določite sami. Obseg vsebuje oznako stolpca in celico pod njo, ki vsebuje pogoj."}, "DMIN": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Vrne najmanjše število v polju (stolpcu) zapisov zbirke podatkov, ki ustreza pogojem, ki ste jih določili.", "ad": "je obmo<PERSON>je celic, ki sestavlja seznam ali zbirko podatkov. Zbirka podatkov je seznam sorodnih podatkov.!je bodisi oznaka stolpca v dvojnih narekovajih bodisi številka, ki predstavlja položaj stolpca na seznamu.!je obseg celic s pogoji, ki jih določite sami. Obseg vsebuje oznako stolpca in celico pod njo, ki vsebuje pogoj."}, "DPRODUCT": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Zmnoži vrednosti v polju (stolpcu) zapisov zbirke podatkov, ki ustrezajo pogojem, ki ste jih določili.", "ad": "je obseg celic, ki sestavlja seznam ali zbirko podatkov. Zbirka podatkov je seznam sorodnih podatkov.!je bodisi oznaka stolpca v dvojnih narekovajih bodisi številka, ki predstavlja položaj stolpca na seznamu.!je obseg celic s pogoji, ki jih določite sami. Obseg vsebuje oznako stolpca in celico pod njo, ki vsebuje pogoj."}, "DSTDEV": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Na osnovi izbranih vnosov zbirke podatkov oceni standardni odklon. Ocena temelji na vzorcu.", "ad": "je obseg celic, ki sestavlja seznam ali zbirko podatkov. Zbirka podatkov je seznam sorodnih podatkov.!je bodisi oznaka stolpca v dvojnih narekovajih bodisi številka, ki predstavlja položaj stolpca na seznamu.!je obseg celic s pogoji, ki jih določite sami. Obseg vsebuje oznako stolpca in celico pod njo, ki vsebuje pogoj."}, "DSTDEVP": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Izračuna standardni odklon, ki temelji na celotni populaciji izbranih vnosov zbirke podatkov.", "ad": "je obseg celic, ki sestavlja seznam ali zbirko podatkov. Zbirka podatkov je seznam sorodnih podatkov.!je bodisi oznaka stolpca v dvojnih narekovajih bodisi številka, ki predstavlja položaj stolpca na seznamu.!je obseg celic s pogoji, ki jih določite sami. Obseg vsebuje oznako stolpca in celico pod njo, ki vsebuje pogoj."}, "DSUM": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Sešteje števila v polju (stolpcu) zapisov zbirke podatkov, ki ustrezajo pogojem, ki ste jih določili.", "ad": "je obseg celic, ki sestavlja seznam ali zbirko podatkov. Zbirka podatkov je seznam sorodnih podatkov.!je bodisi oznaka stolpca v dvojnih narekovajih bodisi številka, ki predstavlja položaj stolpca na seznamu.!je obseg celic s pogoji, ki jih določite sami. Obseg vsebuje oznako stolpca in celico pod njo, ki vsebuje pogoj."}, "DVAR": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Na osnovi izbranih vnosov zbirke podatkov oceni varianco. Ocena temelji na vzorcu.", "ad": "je obseg celic, ki sestavlja seznam ali zbirko podatkov. Zbirka podatkov je seznam sorodnih podatkov.!je bodisi oznaka stolpca v dvojnih narekovajih bodisi številka, ki predstavlja položaj stolpca na seznamu.!je obseg celic s pogoji, ki jih določite sami. Obseg vsebuje oznako stolpca in celico pod njo, ki vsebuje pogoj."}, "DVARP": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Izračuna varianco celotne populacije izbranih vnosov zbirke podatkov.", "ad": "je obseg celic, ki sestavlja seznam ali zbirko podatkov. Zbirka podatkov je seznam sorodnih podatkov.!je bodisi oznaka stolpca v dvojnih narekovajih bodisi številka, ki predstavlja položaj stolpca na seznamu.!je obseg celic s pogoji, ki jih določite sami. Obseg vsebuje oznako stolpca in celico pod njo, ki vsebuje pogoj."}, "CHAR": {"a": "(števil<PERSON>)", "d": "<PERSON><PERSON> z<PERSON>, ki ga določa kodno število, iz nabora znakov vašega računalnika.", "ad": "je število med 1 in 255, s ka<PERSON><PERSON> dolo<PERSON> želeni znak."}, "CLEAN": {"a": "(be<PERSON><PERSON>)", "d": "<PERSON>z besedila odstrani vse znake, ki se ne tiskajo.", "ad": "je kateri koli delovni list, s katerega želimo odstraniti znake, ki se ne tiskajo."}, "CODE": {"a": "(be<PERSON><PERSON>)", "d": "Vrne številsko kodo prvega znaka v besedilnem nizu, v naboru znakov, ki ga uporablja računalnik.", "ad": "je be<PERSON>ilo, ki mu določate kodo prvega znaka."}, "CONCATENATE": {"a": "(besedilo1; [besedilo2]; ...)", "d": "Združi več besedilnih nizov v enega.", "ad": "od 1 do 255 besed<PERSON><PERSON>h nizov, ki naj se združijo v en besedilni niz, in so lahko besedilni nizi, <PERSON><PERSON><PERSON> ali sklici na eno samo celico"}, "CONCAT": {"a": "(text1; ...)", "d": "Zdr<PERSON>ži seznam ali obseg besedilnih nizov", "ad": "1 do 254 besedilnih nizov ali obsegov, ki jih je treba združiti v en besedilni niz"}, "DOLLAR": {"a": "(š<PERSON><PERSON><PERSON>; [decimalna_mesta])", "d": "Pretvori število v besedilo z uporabo valutne oblike.", "ad": "je š<PERSON><PERSON><PERSON>, sklic na celico s številom ali formula za ovrednotenje števila.!je število decimalnih mest desno od decimalne vejice. Število je po potrebi zaokroženo; če iz<PERSON><PERSON>, sta uporabljeni dve decimalni mesti."}, "EXACT": {"a": "(besedilo1; besedilo2)", "d": "<PERSON><PERSON><PERSON>, ali sta dva besedilna niza popolnoma enaka, in vrne TRUE ali FALSE. EXACT lo<PERSON>i velike in male črke.", "ad": "je prvi besedilni niz!je drugi besedilni niz"}, "FIND": {"a": "(iskano_besedilo; v_besedilu; [za<PERSON><PERSON><PERSON>_znak])", "d": "Vrne začetni položaj besedilnega niza v drugem besedilnem nizu. FIND loči velike in male črke.", "ad": "je be<PERSON>ilo, ki ga želite poiskati. Uporabite dvojne narekovaje (prazno besedilo), če želite primerjati s prvim znakom v argumentu »V_besedilu«; uporaba nadomestnih znakov ni dovoljena.!je besedilo, ki vsebuje iskano besedilo.!dolo<PERSON>i znak, pri katerem se iskanje začne. Prvi znak v argumentu »V_besedilu« je znak številka 1. <PERSON><PERSON> iz<PERSON>ščeno, velja »Začetni_znak = 1«."}, "FINDB": {"a": "(iskano_besedilo; v_besedilu; [za<PERSON><PERSON><PERSON>_znak])", "d": "Je mogoče en besedilni niz poiskati z drugim in vrniti število začetnih mest prvega besedilnega niza iz prvega znaka drugega besedilnega niza, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japon<PERSON><PERSON><PERSON>, kit<PERSON>š<PERSON>ina in korejščina", "ad": "je be<PERSON>ilo, ki ga želite poiskati. Uporabite dvojne narekovaje (prazno besedilo), če želite primerjati s prvim znakom v argumentu »V_besedilu«; uporaba nadomestnih znakov ni dovoljena.!je besedilo, ki vsebuje iskano besedilo.!dolo<PERSON>i znak, pri katerem se iskanje začne. Prvi znak v argumentu »V_besedilu« je znak številka 1. <PERSON><PERSON> iz<PERSON>ščeno, velja »Začetni_znak = 1«."}, "FIXED": {"a": "(š<PERSON><PERSON><PERSON>; [decimalna_mesta]; [brez_vejic])", "d": "Zaokroži številko na določeno število decimalnih mest in vrne rezultat kot besedilo z vejicami ali brez njih.", "ad": "je <PERSON><PERSON><PERSON><PERSON>, ki ga želite zaokrožiti in pretvoriti v besedilo.!je število števk desno od decimalne vejice. Če nič ne vnesete, sta decimalki dve.!je logična vrednost: ne prikaže ločil tisočic v vrnjenem besedilu = TRUE; prikaže ločila v vrnjenem besedilu = FALSE ali ni določena."}, "LEFT": {"a": "(besed<PERSON>; [št_znakov])", "d": "Vrne določeno število znakov od začetka besedilnega niza.", "ad": "je besedilni niz, v ka<PERSON>em so znaki, ki jih želite izvleči.!do<PERSON><PERSON><PERSON>, koliko znakov naj izvleče LEFT; enega, če je izpuščeno."}, "LEFTB": {"a": "(besed<PERSON>; [št_znakov])", "d": "Vrne prvi znak ali znake v nizu besedila na osnovi navedenega števila bajtov, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japonščina, kitajščina in korejščina", "ad": "je besedilni niz, v ka<PERSON>em so znaki, ki jih želite izvleči.!do<PERSON><PERSON>a, koliko znakov naj izvleče LEFTB; enega, če je izpuščeno."}, "LEN": {"a": "(be<PERSON><PERSON>)", "d": "Vrne število znakov v besedilnem nizu.", "ad": "je be<PERSON><PERSON>, ki mu želite poiskati dolž<PERSON>. Presledki se štejejo kot znaki."}, "LENB": {"a": "(be<PERSON><PERSON>)", "d": "Vrne število b<PERSON>, ki predstavljajo znake v besedilnem nizu, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japonščina, kitajščina in korejščina", "ad": "je be<PERSON><PERSON>, ki mu želite poiskati dolž<PERSON>. Presledki se štejejo kot znaki."}, "LOWER": {"a": "(be<PERSON><PERSON>)", "d": "Pretvori vse črke v besedilnem nizu v male črke.", "ad": "je be<PERSON><PERSON>, ki ga želite pretvoriti v male črke. Znaki v besedilu, ki niso črke, ne bodo spremenjeni."}, "MID": {"a": "(besed<PERSON>; prvi_znak; št_znakov)", "d": "Vrne znake iz sredine besedilnega niza, če sta podana začetni položaj in dolžina.", "ad": "je besedilni niz, v ka<PERSON>em so znaki, ki jih želite izvleči.!je položaj prvega znaka, ki ga želite izvleči iz besedila. Prvi znak v besedilu je 1.!določa število znakov, ki jih vrne iz besedila."}, "MIDB": {"a": "(besed<PERSON>; prvi_znak; št_znakov)", "d": "Vrne določeno število znakov iz besedilnega niza z začetkom pri navedenem položaju in na osnovi navedenega števila bajtov, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japonščina, kitajščina in korejščina", "ad": "je besedilni niz, v ka<PERSON>em so znaki, ki jih želite izvleči.!je položaj prvega znaka, ki ga želite izvleči iz besedila. Prvi znak v besedilu je 1.!določa število znakov, ki jih vrne iz besedila."}, "NUMBERVALUE": {"a": "(besedilo; [decimalno_ločilo]; [sku<PERSON>_ločilo])", "d": "Pretvori besedilo v številko, neodvisno od lokalnega načina", "ad": "je niz, ki predstav<PERSON><PERSON>tevilo, ki ga želite pretvoriti!je znak, ki je uporabljen kot ločilo v nizu!je znak, ki je uporabljen kot skupinsko ločilo v nizu"}, "PROPER": {"a": "(be<PERSON><PERSON>)", "d": "Pretvori besedilni niz v velike in male črke; vsako prvo črko v besedi v veliko začetnico, vse preostale črke pa pretvori v male.", "ad": "je besedilo v na<PERSON><PERSON>jih, formula, ki vrne besedilo ali sklic na celico z besedilom, kjer želite delno postaviti velike začetnice. "}, "REPLACE": {"a": "(staro_besedilo; mesto_znaka; št_znakov; novo_besedilo)", "d": "Zamenja del besedilnega niza z drugim besedilnim nizom.", "ad": "je be<PERSON>ilo, v katerem želite zamenjati nekatere znake.!je mesto znaka v starem besedilu, ki ga želite zamenjati z novim besedilom.!je število znakov v »staro_besedilo«, ki ga želite zamenjati z »novo_besedilo«.!je besedilo, ki bo zamenjalo znake v starem besedilu."}, "REPLACEB": {"a": "(staro_besedilo; mesto_znaka; št_znakov; novo_besedilo)", "d": "Nadomesti del besedilnega niza z drugim besedilnim nizom glede na navedeno število bajtov, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japonščina, kitajščina in korejščina", "ad": "je be<PERSON>ilo, v katerem želite zamenjati nekatere znake.!je mesto znaka v starem besedilu, ki ga želite zamenjati z novim besedilom.!je število znakov v »staro_besedilo«, ki ga želite zamenjati z »novo_besedilo«.!je besedilo, ki bo zamenjalo znake v starem besedilu."}, "REPT": {"a": "(besedilo; št_ponovitev)", "d": "<PERSON><PERSON><PERSON> be<PERSON>ilo <PERSON>, kolikor krat je navedeno. Uporabite REPT, če želite zapolniti celico z več ponovitvami besedilnega niza.", "ad": "je be<PERSON>ilo, ki ga želite ponavljati.!je pozitivno število, ki do<PERSON>, kolikokrat se besedilo ponovi."}, "RIGHT": {"a": "(besed<PERSON>; [št_znakov])", "d": "Vrne določeno število znakov od konca besedilnega niza.", "ad": "je besedilni niz, v ka<PERSON>em so znaki, ki jih želite izvleči.!do<PERSON><PERSON><PERSON>, koliko znakov želite izvleči. Enega, če je izpuščeno."}, "RIGHTB": {"a": "(besed<PERSON>; [št_znakov])", "d": "Vrne zadnji znak ali znake v besedilnem nizu, in sicer na podlagi navedenega števila bajtov, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japonščina, kitajščina in korejščina", "ad": "je besedilni niz, v ka<PERSON>em so znaki, ki jih želite izvleči.!do<PERSON><PERSON><PERSON>, koliko znakov želite izvleči. Enega, če je izpuščeno."}, "SEARCH": {"a": "(iskano_besedilo; v_besedilu; [št_za<PERSON>etka])", "d": "<PERSON><PERSON>vil<PERSON> znaka, kjer je prvič – gledano z leve proti desni - najden poseben znak ali besedilni niz (ne loči velikih in malih črk).", "ad": "je be<PERSON>ilo, ki ga želite poiskati. Uporabite lahko nadomestna znaka »?« in »*«. Če želite iskati znaka »?« in »*«, uporabite »~?« in »~*«.!je besedilo, v katerem želite najti »Iskano_besedilo«.!je številka znaka v argumentu »V_besedilu«, šteto od leve, pri katerem želite začeti iskanje. Če je izpuščeno, se uporabi 1."}, "SEARCHB": {"a": "(iskano_besedilo; v_besedilu; [št_za<PERSON>etka])", "d": "Je mogoče en besedilni niz poiskati z drugim in vrniti število začetnih mest prvega besedilnega niza iz prvega znaka drugega besedilnega niza, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japon<PERSON><PERSON><PERSON>, kit<PERSON>š<PERSON>ina in korejščina", "ad": "je be<PERSON>ilo, ki ga želite poiskati. Uporabite lahko nadomestna znaka »?« in »*«. Če želite iskati znaka »?« in »*«, uporabite »~?« in »~*«.!je besedilo, v katerem želite najti »Iskano_besedilo«.!je številka znaka v argumentu »V_besedilu«, šteto od leve, pri katerem želite začeti iskanje. Če je izpuščeno, se uporabi 1."}, "SUBSTITUTE": {"a": "(besedilo; staro_besedilo; novo_besedilo; [št_primerka])", "d": "Zamenja staro besedilo z novim v besedilnem nizu.", "ad": "je besedilo ali sklic na celico z besedilom, v katerem želite zamenjevati znake.!je obstoječe besedilo, ki ga želite zamenjati. Če se velike in male črke v »Staro_besedilo« ne ujemajo z velikimi in malimi črkami v besedilu, funkcija SUBSTITUTE ne bo zamenjala besedila.!je besedilo, s katerim želite zamenjati »Staro_besedilo«.!dolo<PERSON>a, katero pojavitev »Staro_besedilo« želite zamenjati. Če je izpuščeno, bodo zamenjane vse pojavitve »Staro_besedilo«."}, "T": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vred<PERSON>t besedilo; če je, vrne besedilo, če ni, vrne dvojne narekovaje (prazno besedilo).", "ad": "je vred<PERSON><PERSON>, ki jo ž<PERSON>te preveriti."}, "TEXT": {"a": "(vrednost; oblika_besedila)", "d": "Vrednost pretvori v besedilo v točno določeni obliki zapisa števila", "ad": "je številska vrednost, formula za ovrednotenje na številsko vrednost ali sklic na celico s številsko vrednostjo!je številska oblika v besedilni obliki iz pogovornega okna »Oblika celic«, zavihka »Številke« in polja »Kategorija«"}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "Združi seznam ali obseg besedilnih nizov z ločilom", "ad": "Znak ali niz za vstavljanje med vsak besedilni element!Če je vrednost TRUE(privzeto), prezre prazne celice!1 do 252 besedilnih nizov ali obsegov za združitev"}, "TRIM": {"a": "(be<PERSON><PERSON>)", "d": "<PERSON>z besedilnega niza odstrani vse presledke, razen enojnih presledkov med besedami.", "ad": "je be<PERSON><PERSON>, iz katerega želite odstraniti presledke."}, "UNICHAR": {"a": "(števil<PERSON>)", "d": "Vrne znak Unicode, na katerega se sklicuje številska vrednost", "ad": "je število Unicode, ki predstavlja znak"}, "UNICODE": {"a": "(be<PERSON><PERSON>)", "d": "<PERSON><PERSON> (kodno točko), ki ustreza prvemu znaku besedila", "ad": "je znak, za katerega želite pridobiti vrednost Unicode"}, "UPPER": {"a": "(be<PERSON><PERSON>)", "d": "Pretvori besedilni niz v vse velike črke.", "ad": "je <PERSON><PERSON><PERSON>, ki ga želite pretvoriti v velike črke, sklic ali besedilni niz."}, "VALUE": {"a": "(be<PERSON><PERSON>)", "d": "Pretvori besedilni niz, ki predstavlja število, v število.", "ad": "je besedilo v narekovajih ali sklic na celice z besedilom, ki ga želite pretvoriti."}, "AVEDEV": {"a": "(število1; [število2]; ...)", "d": "Vrne povprečje absolutnih odstopanj podatkovnih točk od srednje vrednosti. Argumenti so lah<PERSON> ali imena, mat<PERSON><PERSON> ali <PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>.", "ad": "od 1 do 255 <PERSON><PERSON>, za katere ž<PERSON>š določiti povprečje absolutnih odstopanj"}, "AVERAGE": {"a": "(število1; [število2]; ...)", "d": "<PERSON><PERSON> aritmetično povprečno vrednost njegovih argumentov, ki so <PERSON><PERSON><PERSON>, imena, matrike al<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>.", "ad": "od 1 do 255 <PERSON><PERSON><PERSON><PERSON><PERSON> argumentov, za katere iščete povprečno vrednost."}, "AVERAGEA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Vrne aritmetično povprečno vrednost svojih argumentov. Besedilo in logična vrednost FALSE v argumentih se ovrednoti kot 0, logična vrednost TRUE pa kot 1. <PERSON>rg<PERSON><PERSON> so <PERSON><PERSON><PERSON>, im<PERSON>, matrike ali <PERSON>.", "ad": "od 1 do 255 <PERSON><PERSON>, za katere želite povprečje"}, "AVERAGEIF": {"a": "(obseg; pogoji; [obseg_za_povprečje])", "d": "<PERSON>j<PERSON> povprečje (aritmetično povprečno vrednost) za celice, navedene z danim pogojem ali kriterijem", "ad": "je obseg celic, katere želite ovrednotiti!je pogoj ali kriterij v obliki števila, izraza ali besedila, ki do<PERSON>, katere celice bodo uporabljene za iskanje povprečja!so dejanske celice, ki bodo uporabljene za iskanje povprečja. Če bodo izpuščene, bodo uporabljene celice v obsegu"}, "AVERAGEIFS": {"a": "(obseg_za_povprečje; obseg_pogojev; pogoji; ...)", "d": "Najde povprečje (aritmetično povprečno vrednost) za celice, navedene v danem nizu pogojev ali kriterijev", "ad": "so dejanske celice, ki bodo uporabljene za iskanje povprečja.!je obseg celic, katere želite ovrednotiti za določen pogoj!je pogoj ali kriterij v obliki števila, i<PERSON><PERSON>a ali be<PERSON>, ki dolo<PERSON>, katere celice bodo uporabljene za iskanje povprečja"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Vrne kumulativno porazdelitev beta gostote verjetnosti", "ad": "je vrednost med A in B, pri kateri se ovrednoti funkcija.!je parameter porazdelitve in mora biti večji od 0.!je parameter porazdelitve in mora biti večji od 0.!je izbirna spodnja meja intervala x. Če je izpuščeno, velja A = 0.!je izbirna zgornja meja intervala x. Če je izpuščeno, velja B = 1."}, "BETAINV": {"a": "(verjetnost; alfa; beta; [A]; [B])", "d": "Vrne inverzno kumulativno porazdelitev beta gostote verjetnosti (BETADIST).", "ad": "je ve<PERSON><PERSON><PERSON><PERSON>, povezana s porazdelitvijo beta.!je parameter porazdelitve in mora biti večji od 0.!je parameter porazdelitve in mora biti večji od 0.!je izbirna spodnja meja intervala x. Če je izpuščeno, velja A = 0.!je izbirna zgornja meja intervala x. Če je izpuščeno, velja B = 1."}, "BETA.DIST": {"a": "(x; alfa; beta; kumulativno; [A]; [B])", "d": "Vrne beta porazdelitev verjetnosti.", "ad": "je vrednost med A in B, pri kateri se funkcija ovrednoti.!je parameter porazdelitve in mora biti večji od 0.!je parameter porazdelitve in mora biti večji od 0.!je logična vrednost: za kumulativno porazdelitveno funkcijo uporabite TRUE; za porazdelitev gostote verjetnosti uporabite FALSE.!je izbirna spodnja meja intervala x. Če je izpuščen, velja A = 0.!je izbirna zgornja meja intervala x. Če je izpuščen, velja B = 1."}, "BETA.INV": {"a": "(verjetnost; alfa; beta; [A]; [B])", "d": "Vrne inverzno kumulativno beta porazdelitev gostote verjetnosti (BETA.DIST).", "ad": "je ver<PERSON><PERSON><PERSON>, povezana z beta porazdelitvijo.!je parameter porazdelitve in mora biti večji od 0.!je parameter porazdelitve in mora biti večji od 0.!je izbirna spodnja meja intervala x. Če je izpuščen, velja A = 0.!je izbirna zgornja meja intervala x. Če je izpuščen, velja B = 1."}, "BINOMDIST": {"a": "(število_s; poskusi; verjetnost_s; kumulativno)", "d": "Vrne posamezno binomsko porazdelitveno verjetnost.", "ad": "je število uspešnih preskusov.!je število neodvisnih preskusov.!je verjetnost uspeha vsakega preskusa.!je logična vrednost: za kumulativno porazdelitveno funkcijo uporabite TRUE; za verjetnostno masno funkcijo pa FALSE."}, "BINOM.DIST": {"a": "(število_s; poskusi; verjetnost_s; kumulativno)", "d": "Vrne posamezno binomsko porazdelitveno verjetnost.", "ad": "je število uspešnih preizkusov.!je število neodvisnih preizkusov.!je verjetnost uspeha vsakega preizkusa.!je logična vrednost: za kumulativno porazdelitveno funkcijo uporabite TRUE; za verjetnostno masno funkcijo pa FALSE."}, "BINOM.DIST.RANGE": {"a": "(poskusi; verjetnost_s; število_s; [število_s2])", "d": "Vrne verjetnost preskusnega rezultata z binomsko porazdelitvijo", "ad": "je število samostojnih poskusov!je verjetnost uspeha posameznega preskusa!je število uspehov pri poskusih!če je na voljo, ta funkcija vrne verjetnost, da bo število uspeš<PERSON>h poskusov med številko_s in številko_s2"}, "BINOM.INV": {"a": "(poskusi; verjetnost_s; alfa)", "d": "<PERSON><PERSON> najmanj<PERSON><PERSON>, za katero je kumulativna binomska porazdelitev večja ali enaka vrednosti kriterija.", "ad": "je število Bernoullijevih poskusov.!je verjetnost uspešnosti posameznega poskusa in je število med 0 in vključno 1.!je vrednost kriterija in je število med 0 in vključno 1."}, "CHIDIST": {"a": "(x; stop_prostosti)", "d": "Vrne verjetnost dvorepe porazdelitve Hi-kvadrat.", "ad": "je <PERSON><PERSON><PERSON><PERSON>, pri kateri želite ovrednotiti porazdelitev, in je nenegativno število!je število stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10."}, "CHIINV": {"a": "(verjetnost; stop_prostosti)", "d": "Vrne inverzno verjetnost dvorepe porazdelitve Hi-kvadrat.", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON>, pove<PERSON><PERSON> s porazdelitvijo Hi-k<PERSON>t, in je vrednost med 0 in vključno 1.!je število stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10."}, "CHITEST": {"a": "(dejan<PERSON>_obseg; pri<PERSON><PERSON><PERSON>ni_obseg)", "d": "Vrne preizkus neodvisnosti: vrednost iz porazdelitve hi-kvadrat za statistične in ustrezne stopnje prostosti.", "ad": "je obseg podatkov, ki vsebuje opazovanja za preizkus pričakovanih vrednosti.!je obseg podatkov, ki vsebuje razmerje vsot vrst in vsot stolpcev proti skupni vsoti."}, "CHISQ.DIST": {"a": "(x; stop_prostosti; kumula<PERSON>vno)", "d": "Vrne levorepo verjetnost porazdelitve hi-kvadrat.", "ad": "je v<PERSON><PERSON><PERSON>, pri kateri želite ovrednotiti porazdelitev, in je nenegativno število.!je število stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10.!je logi<PERSON><PERSON> vred<PERSON>, ki <PERSON>, kaj naj funkcija vrne: kumulativno porazdelitveno funkcijo = TRUE; porazdelitev gostote verjetnosti = FALSE."}, "CHISQ.DIST.RT": {"a": "(x; stop_prostosti)", "d": "Vrne levorepo verjetnost porazdelitve hi-kvadrat.", "ad": "je v<PERSON><PERSON><PERSON>, pri kateri želite ovrednotiti porazdelitev, in je nenegativno število.!je število stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10."}, "CHISQ.INV": {"a": "(verjetnost; stop_prostosti)", "d": "Vrne inverzno verjetnost levorepe porazdelitve hi-kvadrat.", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON>, pove<PERSON>a s porazdelitvijo hi-kvadrat, in je vrednost med 0 in vključno 1.!je število stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10."}, "CHISQ.INV.RT": {"a": "(verjetnost; stop_prostosti)", "d": "Vrne inverzno verjetnost desnorepe porazdelitve hi-kvadrat.", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON>, pove<PERSON>a s porazdelitvijo hi-kvadrat, in je vrednost med 0 in vključno 1.!je število stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10."}, "CHISQ.TEST": {"a": "(dejan<PERSON>_obseg; pri<PERSON><PERSON><PERSON>ni_obseg)", "d": "<PERSON><PERSON> preskus neodvisnosti: vrednost iz porazdelitve hi-kvadrat za statistične in ustrezne stopnje prostosti.", "ad": "je obseg podatkov z opazovanji za preskus pričakovanih vrednosti.!je obseg podatkov z razmerji vsot vrst in vsot stolpcev proti skupni vsoti."}, "CONFIDENCE": {"a": "(alfa; standardni_odklon; velikost)", "d": "Vrne interval zaupanja za populacijsko srednjo vrednost", "ad": "je raven pome<PERSON><PERSON><PERSON>, ki se uporabi za izračun ravni zaupanja, in je število, ki je večje od 0 in manjše od 1!je domnevno znan standardni odklon populacije za obseg podatkov. Standardni_odklon mora biti večje kot 0!je velikost vzorca"}, "CONFIDENCE.NORM": {"a": "(alfa; standardni_odklon; velikost)", "d": "Z normalno porazdelitvijo vrne interval zaupanja za populacijsko srednjo vrednost.", "ad": "je raven pomembnosti za izračun ravni zaupanja, in je število, ki je večje od 0 in manjše od 1.!je domnevno znan standardni odklon populacije za obseg podatkov. »Standardni_odklon« mora biti večje kot 0.!je velikost vzorca."}, "CONFIDENCE.T": {"a": "(alfa; standardni_odklon; velikost)", "d": "S Studentovo t-porazdelitvijo vrne interval zaupanja za populacijsko srednjo vrednost.", "ad": "je raven pomembnosti za izračun ravni zaupanja, in je število, ki je večje od 0 in manjše od 1.!je domnevno znan standardni odklon populacije za obseg podatkov. »Standardni_odklon« mora biti večje kot 0.!je velikost vzorca."}, "CORREL": {"a": "(matrika1; matrika2)", "d": "<PERSON><PERSON> k<PERSON>lacijski koeficient med dvema naboroma podatkov.", "ad": "so vrednosti obsega celic. Vrednosti morajo biti š<PERSON>, imena, matrike ali sklici, ki vseb<PERSON><PERSON><PERSON>.!je drug obseg celic z vrednostmi. Vrednosti morajo biti š<PERSON>, imena, matrike ali sklici, ki vs<PERSON><PERSON><PERSON><PERSON>."}, "COUNT": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Prešteje celice v obsegu, ki vsebujejo števila", "ad": "od 1 do 255 <PERSON><PERSON>, ki vseb<PERSON>je<PERSON> ali se sklicujejo na različne vrste podatkov, pri tem pa se preštevajo le števila"}, "COUNTA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Prešteje neprazne celice v obsegu", "ad": "od 1 do 255 argumentov, ki preds<PERSON>v<PERSON><PERSON><PERSON> vred<PERSON>ti in celice, ki jih želite prešteti. Vrednosti so lahko informacija katere koli vrste"}, "COUNTBLANK": {"a": "(obseg)", "d": "Izračuna število praznih celic v navedenem obsegu.", "ad": "je obseg, v katerem želite prešteti prazne celice."}, "COUNTIF": {"a": "(obseg; pogoji)", "d": "Prešteje celice v obsegu, ki se ujemajo z danim pogojem.", "ad": "je obseg celic, v katerem želite prešteti neprazne celice.!je pogoj v ob<PERSON><PERSON> š<PERSON>vila, i<PERSON><PERSON><PERSON> ali be<PERSON>, ki do<PERSON>, katere celice naj se preštejejo."}, "COUNTIFS": {"a": "(obseg_pogojev; pogoji; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>, ki jih navaja dani niz pogojev ali kriterijev", "ad": "je obseg celic, katere želite ovrednotiti za določen pogoj!je pogoj v obliki š<PERSON>vila, i<PERSON><PERSON><PERSON> ali be<PERSON>, ki do<PERSON>, katere celice bodo preš<PERSON>te"}, "COVAR": {"a": "(matrika1; matrika2)", "d": "<PERSON><PERSON> k<PERSON>, ki je povprečje produktov odklonov za vsak par podatkovnih točk v dveh podatkovnih množicah.", "ad": "je prvi obseg celic celih <PERSON>, ki morajo biti <PERSON>, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>.!je drugi obseg celic celih <PERSON>, ki morajo biti <PERSON>, matrike ali s<PERSON>lic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>."}, "COVARIANCE.P": {"a": "(matrika1; matrika2)", "d": "Vrne populacijsko kovarianco, ki je povprečje produktov odklonov za vsak par podatkovnih točk v dveh podatkovnih množicah.", "ad": "je prvi obseg celic celih <PERSON>, ki morajo biti <PERSON>, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>.!je drugi obseg celic celih <PERSON>, ki morajo biti <PERSON>, matrike ali s<PERSON>lic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>."}, "COVARIANCE.S": {"a": "(matrika1; matrika2)", "d": "Vrne vzorčno kovarianco, ki je povprečje rezultatov odklonov za vsak par podatkovnih točk v dveh podatkovnih množicah.", "ad": "je prvi obseg celic celih <PERSON>, ki morajo biti <PERSON>, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>.!je drugi obseg celic celih <PERSON>, ki morajo biti <PERSON>, matrike ali s<PERSON>lic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>."}, "CRITBINOM": {"a": "(poskusi; verjetnost_s; alfa)", "d": "<PERSON><PERSON> najmanj<PERSON><PERSON>, za katero je kumulativna binomska porazdelitev večja ali enaka vrednosti kriterija.", "ad": "je število Bernoullijevih poskusov.!je verjetnost uspešnosti posameznega poskusa in je število med 0 in vključno 1.!je vrednost kriterija in je število med 0 in vključno 1."}, "DEVSQ": {"a": "(število1; [število2]; ...)", "d": "Vrne vsoto kvadratov odklonov podatkovnih točk iz njihovih vzorčnih srednjih vrednosti.", "ad": "od 1 do 255 argumentov ali matriko ali matriko s<PERSON>, za katere želite določiti vsoto kvadratnih odklonov uporabo funkcije DEVSQ"}, "EXPONDIST": {"a": "(x; lambda; kumulativno)", "d": "Vrne eksponentno porazdelitev.", "ad": "je vrednost funkcije in je nenegativno število!je vrednost parametra in je pozitivno število!je logična vrednost, ki oz<PERSON>, kaj naj funkcija vrne: kumulativno porazdelitveno funkcijo = TRUE; porazdelitev gostote verjetnosti = FALSE"}, "EXPON.DIST": {"a": "(x; lambda; kumulativno)", "d": "Vrne eksponentno porazdelitev.", "ad": "je vrednost funkcije in je nenegativno število.!je vrednost parametra in je pozitivno število.!je logična vrednost, ki nazna<PERSON>, kaj naj funkcija vrne: kumulativno porazdelitveno funkcijo = TRUE; porazdelitev gostote verjetnosti = FALSE."}, "FDIST": {"a": "(x; stop_prostosti1; stop_prostosti2)", "d": "Vrne F-porazdelitev (dvorepo) verjetnosti (stopnja razpršenosti) za dve podatkovni množici.", "ad": "je v<PERSON><PERSON><PERSON>, pri kateri se ovrednoti funkcija, in je nenegativno število.!je števec stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10.!je imenovalec stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10."}, "FINV": {"a": "(verjetnost; stop_prostosti1; stop_prostosti2)", "d": "Vrne inverzno verjetnostno F-porazdelitev (dvorepo): če p = FDIST(x,...), potem FINV(p,...) = x.", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON>, pove<PERSON>a s F-kumulativno porazdelitvijo, in je število med 0 in vključno 1.!je števec stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10.!je imenovalec stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10."}, "FTEST": {"a": "(matrika1; matrika2)", "d": "Vrne rezultat F-preizkusa, ki je dvor<PERSON>a verjetnost, da se varianci argumentov »matrika1« in »matrika2« bistveno ne razlikujeta.", "ad": "je prva matrika ali obseg podatkov, ki so lah<PERSON> ali imena, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON> (presledki se prezrejo).!je druga matrika ali obseg podatkov, ki so lahko <PERSON> ali imena, matrike ali s<PERSON>lic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON> (presledki se prezrejo)."}, "F.DIST": {"a": "(x; stop_prostosti1; stop_prostosti2; kumula<PERSON>vno)", "d": "Vrne F-porazdelitev (levorepo) verjetnosti (stopnja razpršenosti) za dve podatkovni množici.", "ad": "je vred<PERSON><PERSON>, pri kateri se ovrednoti funkcija, in je nenegativno število.!je števec stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10.!je imenovalec stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10.!je logi<PERSON><PERSON> vred<PERSON>, ki do<PERSON><PERSON><PERSON>, kaj naj funkcija vrne: kumulativno porazdelitveno funkcijo = TRUE; porazdelitev gostote verjetnosti = FALSE."}, "F.DIST.RT": {"a": "(x; stop_prostosti1; stop_prostosti2)", "d": "Vrne F-porazdelitev (desnorepo) verjetnosti (stopnja razpršenosti) za dve podatkovni množici.", "ad": "je v<PERSON><PERSON><PERSON>, pri kateri se ovrednoti funkcija, in je nenegativno število!je števec stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10.!je imenovalec stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10."}, "F.INV": {"a": "(verjetnost; stop_prostosti1; stop_prostosti2)", "d": "Vrne inverzno F verjetnostno (levorepo) porazdelitev: če p = F.DIST(x, ...), potem F.INV(p, ...) = x.", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON>, pove<PERSON>a s F-kumulativno porazdelitvijo, in je število med 0 in vključno 1.!je števec stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10.!je imenovalec stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10."}, "F.INV.RT": {"a": "(verjetnost; stop_prostosti1; stop_prostosti2)", "d": "Vrne inverzno F verjetnostno (desnorepo) porazdelitev: če p = F.DIST.RT(x, ...), potem F.INV.RT(p, ...) = x.", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON>, pove<PERSON>a s F-kumulativno porazdelitvijo, in je število med 0 in vključno 1.!je števec stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10.!je imenovalec stopenj prostosti, ki je število med 1 in 10^10 in ni 10^10."}, "F.TEST": {"a": "(matrika1; matrika2)", "d": "Vrne rezultat preskusa F, ki je dvorepa verjetnost, da se varianci argumentov »matrika1« in »matrika2« bistveno ne razlikujeta.", "ad": "je prva matrika ali obseg podatkov, ki so lah<PERSON> ali imena, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON> (presledki se prezrejo).!je druga matrika ali obseg podatkov, ki so lahko <PERSON> ali imena, matrike ali s<PERSON>lic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON> (presledki se prezrejo)."}, "FISHER": {"a": "(x)", "d": "<PERSON><PERSON> transformacijo.", "ad": "je številska vrednost, ki jo želite pretvor<PERSON>, in je število med -1 in 1, ni pa -1 ali 1."}, "FISHERINV": {"a": "(y)", "d": "Vrne inverzno Fischerjevo transformacijo: če y = FISHER(x), potem FISHERINV(y) = x.", "ad": "je vred<PERSON>t, ki jo želite pretvoriti."}, "FORECAST": {"a": "(x; known_ys; known_xs)", "d": "Izračuna ali predvidi bodočo vrednost vzdolž linearnega trenda z uporabo obstoječih vrednosti.", "ad": "je podatkovna to<PERSON>, ki ji želite predvideti vrednost, in mora biti številska vrednost.!je odvisna matrika ali obseg številskih podatkov.!je neodvisna matrika ali obseg številskih podatkov. Varianca parametra ne sme biti nič."}, "FORECAST.ETS": {"a": "(cil<PERSON>i_datum; vred<PERSON><PERSON>; časovna_premica; [se<PERSON><PERSON><PERSON>]; [do<PERSON><PERSON><PERSON><PERSON>_podatkov]; [združevanje])", "d": "Vrne predvideno vrednost za določen prihodnji datum tako, da uporabi metodo eksponentnega glajenja.", "ad": "je podatkovna to<PERSON>, za katero Spreadsheet Editor predvidi vrednost. Nadaljevati bi moral vzorec vrednosti na časovni premici.!je matrika obsega številskih podatkov, ki jih predvidevate.!je neodvisna metrika ali obseg številskih podatkov. Med datumi na časovni premici mora biti dosleden korak in ne more biti nič.!je opcijska številska vrednost, ki označuje dolžino vzorca sezonskosti. Privzeta vrednost 1 označuje, da je sezonskost zaznana samodejno.!je opcijska vrednost za obravnavo manjkajočih vrednosti. Privzeta vrednost 1 zamenja manjkajoče vrednosti z interpolacijo, 0 pa jih zamenja z ničlami.!je opcijska številska vrednost za združevanje več vrednosti z istim časovnim žigom. Če je vrednost prazna, Spreadsheet Editor izračuna povprečje vrednosti."}, "FORECAST.ETS.CONFINT": {"a": "(cil<PERSON><PERSON>_datum; vred<PERSON>ti; č<PERSON>ovna_premica; [raven_z<PERSON><PERSON><PERSON>]; [se<PERSON><PERSON><PERSON>]; [dokon<PERSON><PERSON><PERSON>_podatkov]; [zdr<PERSON>ževanje])", "d": "Vrne interval zanesljivosti za vrednost napovedi za določen ciljni datum.", "ad": "je podatkovna točka, za katero Spreadsheet Editor predvidi vrednost. Nadaljevati bi moral vzorec vrednosti na časovni premici.!je matrika obsega številskih podatkov, ki jih predvidevate.!je neodvisna metrika ali obseg številskih podatkov. Med datumi na časovni premici mora biti dosleden korak in ne more biti nič.!je število med 0 in 1, ki prikazuje raven zanesljivosti izračunanega intervala zanesljivosti. Privzeta vrednost je .95.!je opcijska številska vrednost, ki označuje dolžino vzorca sezonskosti. Privzeta vrednost 1 označuje, da je sezonskost zaznana samodejno.!je opcijska vrednost za obravnavo manjkajočih vrednosti. Privzeta vrednost 1 zamenja manjkajoče vrednosti z interpolacijo, 0 pa jih zamenja z ničlami.!je opcijska številska vrednost za združevanje več vrednosti z istim časovnim žigom. Če je vrednost prazna, Spreadsheet Editor izračuna povprečje vrednosti."}, "FORECAST.ETS.SEASONALITY": {"a": "(v<PERSON><PERSON><PERSON>; časovna_premica; [do<PERSON><PERSON><PERSON><PERSON>_podatkov]; [zdr<PERSON><PERSON>evan<PERSON>])", "d": "Vrne dolžino ponavljajočega se vzorca, ki ga aplikacijo zazna za določen časovni niz.", "ad": "je matrica ali obseg števil<PERSON> podatkov, ki jih predvidevate.!je neodvisna matrika ali obseg številskih podatkov. Med datumi na časovni premici mora biti dosleden korak in ne more biti nič.!je opcijska vrednost za obravnavo manjkajočih vrednosti. Privzeta vrednost 1 zamenja manjkajoče vrednosti z interpolacijo, 0 pa jih zamenja z ničlami.!je opcijska številska vrednost za združevanje več vrednosti z isto časovno štampiljko. Če je vrednost prazna, Spreadsheet Editor izračuna povprečje vrednosti."}, "FORECAST.ETS.STAT": {"a": "(v<PERSON><PERSON>ti; časovna_premica; vrsta_statistike; [se<PERSON><PERSON><PERSON>]; [do<PERSON><PERSON><PERSON><PERSON>_podatkov]; [zdr<PERSON><PERSON>evanje])", "d": "Vrne zahtevane statistične podatke za napoved.", "ad": "je matrica ali obseg števil<PERSON><PERSON> podatkov, ki jih napovedujete.!je neodvisna matrika ali obseg številskih podatkov. Med datumi na časovni premici mora biti dosleden korak in ne more biti nič.!je število med 1 in 8. <PERSON><PERSON><PERSON><PERSON><PERSON>, katero statistiko bo Spreadsheet Editor vrnil za izračunano napoved.!je opcijska številska vrednost, ki označuje dolžino vzorca sezonskosti. Privzeta vrednost 1 označuje, da je sezonskost zaznana samodejno.!je opcijska vrednost za obravnavo manjkajočih vrednosti. Privzeta vrednost 1 zamenja manjkajoče vrednosti z interpolacijo, 0 pa jih zamenja z ničlami.!je opcijska številska vrednost za združevanje več vrednosti z isto časovno štampiljko. Če je vrednost prazna, Spreadsheet Editor izračuna povprečje vrednosti."}, "FORECAST.LINEAR": {"a": "(x; znani_y-i; znani_x-i)", "d": "Izračuna ali predvidi bodočo vrednost vzdolž linearnega trenda z uporabo obstoječih vrednosti.", "ad": "je podatkovna to<PERSON>, ki ji želite predvideti vrednost, in mora biti številska vrednost.!je odvisna matrika ali obseg številskih podatkov.!je neodvisna matrika ali obseg številskih podatkov. Varianca parametra ne sme biti nič."}, "FREQUENCY": {"a": "(matrika_podatkov; matrika_raz<PERSON>v)", "d": "Preračuna, kako pogosto se vrednosti pojavljajo v obsegu vrednosti, in nato vrne navpično matriko števil, ki ima en element več kot »matrika_razredov«.", "ad": "je matrika vrednosti ali sklic na množico vrednosti, v kateri želite šteti frekvence (presledki in besedilo so prezrti).!je matrika ali sklic na intervale, v katerih želite razvrščati v skupine vrednosti iz »matrika_podatkov«."}, "GAMMA": {"a": "(x)", "d": "Vrne vrednost funkcije Gama", "ad": "je v<PERSON><PERSON><PERSON>, ki ji želite izračunati funkcijo Gama"}, "GAMMADIST": {"a": "(x; alfa; beta; kumulativno)", "d": "<PERSON>rne gama porazdelitev.", "ad": "je vrednost, pri kateri želite ovrednotiti porazdelitev, in je nenegativno število.!je parameter porazdelitve in je pozitivno število.!je parameter porazdelitve in je pozitivno število. Če je beta = 1, GAMMADIST vrne standardno gama porazdelitev.!je logična vrednost: vrne kumulativno porazdelitveno funkcijo = TRUE; vrne verjetnostno masno funkcijo = FALSE ali izpuščeno."}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulativno)", "d": "<PERSON>rne gama porazdelitev.", "ad": "je vrednost, pri kateri želite ovrednotiti porazdelitev, in je nenegativno število.!je parameter porazdelitve in je pozitivno število.!je parameter porazdelitve in je pozitivno število. Če je beta = 1, GAMMA.DIST vrne standardno gama porazdelitev.!je logična vrednost: vrne kumulativno porazdelitveno funkcijo = TRUE; vrne verjetnostno masno funkcijo = FALSE ali izpuščeno."}, "GAMMAINV": {"a": "(verjetnost; alfa; beta)", "d": "Vrne inverzno gama kumulativno porazdelitev: če je p = GAMMADIST(x,...), potem je GAMMAINV(p,...) = x.", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON>, povezana z gama porazdelitvijo, in je število med 0 in vključno 1.!je parameter porazdelitve in je pozitivno število.!je parameter porazdelitve in je pozitivno število. Kadar je beta = 1, GAMMAINV vrne inverzno standardno gama porazdelitev."}, "GAMMA.INV": {"a": "(verjetnost; alfa; beta)", "d": "Vrne inverzno gama kumulativno porazdelitev: če je p = GAMMA.DIST(x, ...), potem je GAMMA.INV(p, ...) = x.", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON>, povezana z gama porazdelitvijo, in je število med 0 in vključno 1.!je parameter porazdelitve in je pozitivno število.!je parameter porazdelitve in je pozitivno število. Kadar je beta = 1, GAMMA.INV vrne inverzno standardno gama porazdelitev."}, "GAMMALN": {"a": "(x)", "d": "Vrne naravni logaritem gama funkcije.", "ad": "je v<PERSON><PERSON><PERSON>, za katero želite izračunati pozitivno število GAMMALN."}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Vrne naravni logaritem gama funkcije.", "ad": "je v<PERSON><PERSON><PERSON>, za katero <PERSON>elite izračunati pozitivno število GAMMALN.PRECISE"}, "GAUSS": {"a": "(x)", "d": "Vrne 0,5 manj, kot je standardna normalna kumulativna porazdelitev", "ad": "je v<PERSON>, za katero iš<PERSON> porazdelitev"}, "GEOMEAN": {"a": "(število1; [število2]; ...)", "d": "Vrne geometrično srednjo vrednost matrike ali obsega pozitivnih številskih podatkov.", "ad": "od 1 do 255 <PERSON><PERSON><PERSON> ali imen, mat<PERSON> <PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, za katera želite izračunati srednjo vrednost"}, "GROWTH": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Vrne števila v trendu eksponentne rasti, ujemajočem z znanimi podatkovnimi točkami.", "ad": "je nabor y-v<PERSON><PERSON><PERSON>, ki jih že poznate v odnosu y = b*m^x, in je matrika ali obseg pozitivnih števil.!je izbirni nabor x-vred<PERSON>ti, ki jih morda že poznate v odnosu y = b*m^x, in je matrika ali obseg enake velikosti kot znani y-i.!so nove x-vrednosti, za katere <PERSON>eli<PERSON>, da GROWTH vrne ustrezne y-vrednosti.!je logična vrednost: konstanta b se izračuna normalno, če je konstanta enaka TRUE; b je 1, če je konstanta enaka FALSE ali vrednost ni določena."}, "HARMEAN": {"a": "(število1; [število2]; ...)", "d": "Vrne harmonično srednjo vrednost za nabor pozitivnih števil. Harmonična srednja vrednost je obratna vrednost aritmetične srednje vrednosti za obratne (recipročne) vrednosti.", "ad": "od 1 do 255 <PERSON><PERSON><PERSON> ali imen, mat<PERSON> <PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, za katera želite izračunati harmonično srednjo vrednost"}, "HYPGEOM.DIST": {"a": "(vzorec_s; številka_vzorca; populacija_s; številka_populacije; kumulativno)", "d": "Vrne hipergeometrično porazdelitev.", "ad": "je število uspehov v vzorcu.!je velikost vzorca.!je število uspehov v populaciji.!je velikost populacije.!je logična vrednost: za kumulativno porazdelitveno funkcijo uporabite TRUE; za verjetnostno masno funkcijo pa FALSE."}, "HYPGEOMDIST": {"a": "(vzorec_s; velikost_vzorca; populacija_s; velikost_populacije)", "d": "Vrne hipergeometrično porazdelitev.", "ad": "je število uspehov v vzorcu.!je velikost vzorca.!je število uspehov v populaciji.!je velikost populacije."}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "Izračuna presečišče regresijske premice, ki gre skozi podatkovne točke znanih_x-ov in znanih_y-ov, z osjo y.", "ad": "je odvisen nabor opazovanj ali podatkov, ki so lahko <PERSON> ali imena, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>.!je neodvisen nabor opazovanj ali podatkov, ki so lahko <PERSON> ali imena, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>."}, "KURT": {"a": "(število1; [število2]; ...)", "d": "Vrne sploščenost podatkovne množice.", "ad": "od 1 do 255 <PERSON><PERSON><PERSON> ali imen, mat<PERSON> al<PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, za katera želite izračunati sp<PERSON><PERSON><PERSON><PERSON>"}, "LARGE": {"a": "(matrika; k)", "d": "Vrne k-to največjo vrednost nabora pod<PERSON>kov, na primer peto najve<PERSON>tevilo.", "ad": "je matrika ali obseg <PERSON>, za katero želite določiti k-to največjo vrednost.!je mesto vrednosti za vračanje (od največjega) v matriki ali v obsegu celic."}, "LINEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "<PERSON><PERSON> statistiko, ki opisuje linearni trend, ujemajoč z znanimi podatkovnimi točkami, s prilagajanjem premici po metodi najmanjših k<PERSON>dratov.", "ad": "je nabor y-v<PERSON><PERSON><PERSON>, ki so vam že znane za razmerje y = mx + b.!je izbiren nabor x-v<PERSON><PERSON><PERSON>, ki jih že poznate za razmerje y = mx + b!je logična vrednost: konstanta b se izračuna normalno, če je Const = TRUE ali ni določena; b ima vrednost 0, če je Const = FALSE.!je logična vrednost: vrne dodatno regresijsko statistiko = TRUE; vrne m-koeficiente in konstanto b = FALSE ali ni določena."}, "LOGEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "<PERSON><PERSON> statist<PERSON>, s katero je opisana eksponentna krivulja, ki ustreza znanim podatkovnim točkam.", "ad": "je nabor y-v<PERSON><PERSON><PERSON>, ki jih že poznate v odnosu y = b*m^x.!je izbiren nabor x-v<PERSON><PERSON><PERSON>, ki jih že poznate v odnosu y = b*m^x.!je logična vrednost: konstanta b se izračuna normalno, če je Const = TRUE ali ni določeno; b je enako 1, če je Const = FALSE.!je logična vrednost: vrne dodatno regresijsko statistiko = TRUE; vrne m-koeficiente in konstanto b = FALSE ali ni določena."}, "LOGINV": {"a": "(verjetnost; srednja_vrednost; standardni_odklon)", "d": "Vrne inverzno logaritmično normalno kumulativno porazdelitev funkcije x-a, kjer je ln(x) normalno porazdeljen, s parametroma »Srednja_vrednost« in »Standardni_odklon«.", "ad": "je ve<PERSON><PERSON><PERSON><PERSON>, povezana z logaritmično normalno porazdelitvijo, in je število med 0 in vključno 1.!je srednja vrednost za ln(x).!je standardni odklon za ln(x) in je pozitivno število."}, "LOGNORM.DIST": {"a": "(x; srednja_vrednost; standardni_odklon; kumulativno)", "d": "Vrne logaritmično normalno porazdelitev za x, kjer je ln(x) normalno porazdeljen, s parametroma »srednja_vrednost« in »standardni_odklon«.", "ad": "je vred<PERSON><PERSON>, pri kateri želite ovrednotiti funkcijo, in je pozitivno število.!je srednja vrednost za ln(x).!je standardni odklon za ln(x) in je pozitivno število.!je logična vrednost: za kumulativno porazdelitveno funkcijo uporabite TRUE; za verjetnostno masno funkcijo pa FALSE."}, "LOGNORM.INV": {"a": "(verjetnost; srednja_vrednost; standardni_odklon)", "d": "Vrne inverzno logaritmično normalno kumulativno porazdelitev funkcije x-a, kjer je ln(x) normalno porazdeljen, s parametroma Mean in Standard_dev.", "ad": "je ve<PERSON><PERSON><PERSON><PERSON>, povezana z logaritmično normalno porazdelitvijo, in je število med 0 in vključno 1.!je srednja vrednost za ln(x).!je standardni odklon za ln(x) in je pozitivno število."}, "LOGNORMDIST": {"a": "(x; srednja_vrednost; standardni_odklon)", "d": "Vrne kumulativno logaritmično normalno porazdelitev za x, kjer je ln(x) normalno porazdeljen, s parametroma »Srednja_vrednost« in »Standardni_odklon«.", "ad": "je vred<PERSON><PERSON>, pri kateri želite ovrednotiti funkcijo, in je pozitivno število.!je srednja vrednost za ln(x).!je standardni odklon za ln(x) in je pozitivno število."}, "MAX": {"a": "(število1; [število2]; ...)", "d": "Vrne največjo vrednost v množici vrednosti. Prezre logične vrednosti in besedilo.", "ad": "od 1 do 255 <PERSON><PERSON>, p<PERSON><PERSON>, logi<PERSON><PERSON> vrednosti ali besed<PERSON>, za katera želite poiskati največjo vrednost"}, "MAXA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Vrne največjo vrednost v množici vrednosti. Ne prezre logičnih vrednosti ali besedila.", "ad": "od 1 do 255 <PERSON><PERSON>, p<PERSON><PERSON>, logi<PERSON><PERSON> vrednosti ali besed<PERSON>, med katerimi želite poiskati največjo vrednost"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "Vrne največjo vrednost med celicami, določeni z danim naborom pogojev ali meril", "ad": "celice, v katerih je treba določiti največjo vrednost!je obseg celic, ki jih želite ovrednotiti za določen pogoj!je pogoj ali merilo v obliki števila, i<PERSON><PERSON>a ali besedila, ki do<PERSON>, katere celice bodo vključene, ko bo določena največja vrednost"}, "MEDIAN": {"a": "(število1; [število2]; ...)", "d": "Vrne mediano ali število v sredini množice danih števil.", "ad": "od 1 do 255 <PERSON><PERSON><PERSON> ali imen, mat<PERSON> <PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, za katera iš<PERSON><PERSON> mediano"}, "MIN": {"a": "(število1; [število2]; ...)", "d": "Vrne najmanjšo vrednost v množici vrednosti. Prezre logične vrednosti in besedilo.", "ad": "od 1 do 255 <PERSON><PERSON>, p<PERSON><PERSON>, logi<PERSON><PERSON> vrednosti ali besed<PERSON>, za katera želite poiskati najmanjšo vrednost"}, "MINA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Vrne najmanjšo vrednost v množici vrednosti. Ne prezre logičnih vrednosti in besedila.", "ad": "od 1 do 255 <PERSON><PERSON>, p<PERSON><PERSON>, logi<PERSON><PERSON> vrednosti ali besed<PERSON>, med katerimi želite poiskati najmanjšo vrednost"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "Vrne najmanjšo vrednost med celicami, določeni z danim naborom pogojev ali meril", "ad": "celice, v katerih je treba določiti najmanjšo vrednost!je obseg celic, ki jih želite ovrednotiti za določen pogoj!je pogoj ali merilo v ob<PERSON><PERSON> števila, i<PERSON><PERSON>a ali besedila, ki do<PERSON>, katere celice bodo vključene, ko bo določena najmanjša vrednost"}, "MODE": {"a": "(število1; [število2]; ...)", "d": "Vrne najpogostejšo vrednost v matriki ali v obsegu podatkov.", "ad": "od 1 do 255 <PERSON><PERSON><PERSON> ali imen, mat<PERSON> <PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, za ka<PERSON> ž<PERSON>te na<PERSON>in"}, "MODE.MULT": {"a": "(število1; [število2]; ...)", "d": "Vrne navpično matriko najpogostejših ali ponavljajočih se vrednosti v matriki ali obsegu podatkov. Za vodoravno matriko uporabite =TRANSPOSE(MODE.MULT(število1,število2, ...))", "ad": "od 1 do 255 <PERSON><PERSON><PERSON> ali imen, mat<PERSON> <PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, za katere želi<PERSON>."}, "MODE.SNGL": {"a": "(število1; [število2]; ...)", "d": "Vrne najpogostejšo vrednost v matriki ali v obsegu podatkov.", "ad": "od 1 do 255 <PERSON><PERSON><PERSON> ali imen, mat<PERSON> <PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, za ka<PERSON> ž<PERSON>te na<PERSON>in"}, "NEGBINOM.DIST": {"a": "(število_f; število_s; verjetnost_s; kumulativno)", "d": "<PERSON><PERSON> negativno binoms<PERSON> p<PERSON>v, ki je ver<PERSON><PERSON><PERSON>, da boste do<PERSON><PERSON><PERSON>_f ne<PERSON><PERSON><PERSON> pred uspehom, ki je po vrstnem redu število_s, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da je konstantna verjetnost uspeha enaka verjetnost_s.", "ad": "je šte<PERSON><PERSON> ne<PERSON>pehov.!je prag uspehov.!je verjetnost uspeha in je število med 0 in 1.!je logična vrednost: za kumulativno porazdelitveno funkcijo uporabite TRUE; za verjetnostno masno funkcijo pa FALSE."}, "NEGBINOMDIST": {"a": "(število_f; število_s; verjetnost_s)", "d": "<PERSON>rne negativno binomsko porazdelitev, ki je verjet<PERSON><PERSON>, da boste dož<PERSON> »število_f« neus<PERSON>hov pred uspehom, ki je po vrstnem redu »število_s«, up<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da je konstantna verjetnost uspeha enaka »verjetnost_s«.", "ad": "je š<PERSON><PERSON><PERSON> ne<PERSON>.!je prag uspehov.!je verjet<PERSON>t uspeha in je število med 0 in 1."}, "NORM.DIST": {"a": "(x; srednja_vrednost; standardni_odklon; kumulativno)", "d": "Vrne normalno porazdelitev za navedeno srednjo vrednost in standardni odklon.", "ad": "je vrednost, za katero iščete porazdelitev.!je aritmetična srednja vrednost porazdelitve.!je standardni odklon porazdelitve in je pozitivno število.!je logična vrednost: za kumulativno porazdelitveno funkcijo uporabite TRUE; za porazdelitev gostote verjetnosti pa uporabite FALSE."}, "NORMDIST": {"a": "(x; srednja_vrednost; standardni_odklon; kumulativno)", "d": "Vrne normalno kumulativno porazdelitev za navedeno srednjo vrednost in standardni odklon.", "ad": "je vrednost, za katero iščete porazdelitev.!je aritmetična srednja vrednost porazdelitve.!je standardni odklon porazdelitve in je pozitivno število.!je logična vrednost: za kumulativno porazdelitveno funkcijo uporabite TRUE; za verjetnostno masno funkcijo pa uporabite FALSE."}, "NORM.INV": {"a": "(verjetnost; srednja_vrednost; standardni_odklon)", "d": "Vrne inverzno normalno kumulativno porazdelitev za navedeno srednjo vrednost in standardni odklon.", "ad": "je ve<PERSON><PERSON><PERSON><PERSON>, ki ustreza normalni porazdelitvi, in je število med 0 in vključno 1.!je aritmetična srednja vrednost porazdelitve.!je standardni odklon porazdelitve in je pozitivno število."}, "NORMINV": {"a": "(verjetnost; srednja_vrednost; standardni_odklon)", "d": "Vrne inverzno normalno kumulativno porazdelitev za navedeno srednjo vrednost in standardni odklon.", "ad": "je ve<PERSON><PERSON><PERSON><PERSON>, ki ustreza normalni porazdelitvi, in je število med 0 in vključno 1.!je aritmetična srednja vrednost porazdelitve.!je standardni odklon porazdelitve in je pozitivno število."}, "NORM.S.DIST": {"a": "(z; kumulativno)", "d": "Vrne standardno normalno porazdelitev (ima srednjo vrednost nič in standardni odklon ena).", "ad": "je vred<PERSON><PERSON>, za katero iščete porazdelitev.!je logična vrednost, ki do<PERSON>, kaj naj funkcija vrne: kumulativno porazdelitveno funkcijo = TRUE; porazdelitev gostote verjetnosti = FALSE."}, "NORMSDIST": {"a": "(z)", "d": "Vrne standardno normalno kumulativno porazdelitev (ima srednjo vrednost nič in standardni odklon ena).", "ad": "je v<PERSON>, za katero iš<PERSON> porazdelitev."}, "NORM.S.INV": {"a": "(verjetnost)", "d": "Vrne inverzno standardno normalno kumulativno porazdelitev (ima srednjo vrednost nič in standardni odklon ena).", "ad": "je ve<PERSON><PERSON><PERSON><PERSON>, ki ustreza normalni porazdelitvi, in je število med 0 in vključno 1."}, "NORMSINV": {"a": "(verjetnost)", "d": "Vrne inverzno standardno normalno kumulativno porazdelitev (ima srednjo vrednost nič in standardni odklon ena).", "ad": "je ve<PERSON><PERSON><PERSON><PERSON>, ki ustreza normalni porazdelitvi, in je število med 0 in vključno 1."}, "PEARSON": {"a": "(matrika1; matrika2)", "d": "<PERSON><PERSON> korelacijski koeficient r.", "ad": "je nabor neodvisnih vrednosti.!je nabor odvisnih vrednosti."}, "PERCENTILE": {"a": "(matrika; k)", "d": "Vrne k-ti percentil vrednosti v obsegu.", "ad": "je matrika ali obs<PERSON> pod<PERSON>, ki določa relativni položaj.!je vrednost percentila, ki je med 0 in vključno 1."}, "PERCENTILE.EXC": {"a": "(matrika; k)", "d": "Vrne k-ti percentil vrednosti v obsegu, kjer je k v obsegu med 0 in izključno 1.", "ad": "je matrika ali obs<PERSON> pod<PERSON>, ki določa relativni položaj.!je vrednost percentila, ki je med 0 in vključno 1."}, "PERCENTILE.INC": {"a": "(matrika; k)", "d": "Vrne k-ti percentil vrednosti v obsegu, kjer je k v obsegu med 0 in vključno 1.", "ad": "je matrika ali obs<PERSON> pod<PERSON>, ki določa relativni položaj.!je vrednost percentila, ki je med 0 in vključno 1."}, "PERCENTRANK": {"a": "(matrika; x; [pomembnost])", "d": "Vrne rang vrednosti v množici podatkov kot odstotek podatkovne množice.", "ad": "je matrika ali obseg podatkov s številskimi vrednostmi, ki določa relativni položaj.!je vred<PERSON><PERSON>, za katero želite poznati rang.!je izbirna vrednost, ki označuje število pomembnih števk za vrnjeno odstotno vrednost. Če ta argument izpustite, so uporabljene tri števke (0.xxx%)."}, "PERCENTRANK.EXC": {"a": "(matrika; x; [spomembnost])", "d": "Vrne rang vrednosti v množici podatkov kot odstotek podatkovne množice (od 0 do izključno 1).", "ad": "je matrika ali obseg podatkov s številskimi vrednostmi, ki določa relativni položaj.!je vrednos<PERSON>, za katero želite poznati rang.!je izbirna vrednost, ki določa število števk za vrnjeno odstotno vrednost. Če ta <PERSON> spustite, so uporabljene tri števke (0,xxx %)"}, "PERCENTRANK.INC": {"a": "(matrika; x; [pomembnost])", "d": "Vrne rang vrednosti v množici podatkov kot odstotek podatkovne množice (od 0 do vključno 1).", "ad": "je matrika ali obseg podatkov s številskimi vrednostmi, ki določa relativni položaj.!je vrednos<PERSON>, za katero želite poznati rang.!je izbirna vrednost, ki določa število števk za vrnjeno odstotno vrednost. Če ta <PERSON> spustite, so uporabljene tri števke (0,xxx %)"}, "PERMUT": {"a": "(število; število_iz<PERSON>nih)", "d": "Vrne število permutacij za dano število predmetov ki so lahko izbrani izmed vseh predmetov.", "ad": "je število vseh predmetov.!je število predmetov v vsaki permutaciji."}, "PERMUTATIONA": {"a": "(število; število_izbrano)", "d": "<PERSON><PERSON> število permutacij za dano <PERSON><PERSON> pred<PERSON> (s ponovitvami), ki jih je mogoče izbrati med skupnim številom predmetov", "ad": "je skupno število predmetov!je število predmetov v vsaki permutaciji"}, "PHI": {"a": "(x)", "d": "Vrne vrednost porazdelitve gostote za standardno normalno porazdelitev", "ad": "je <PERSON><PERSON><PERSON><PERSON>, ki mu želite izračunati porazdelitev gostote za standardno normalno porazdelitev"}, "POISSON": {"a": "(x; srednja_vrednost; kunulativno)", "d": "Vrne Poissonovo porazdelitev.", "ad": "je šte<PERSON>o dogodkov.!je pričakovana številska vrednost in je pozitivno število.!je logična vrednost: za kumulativno Poissonovo verjetnost uporabite TRUE, za Poissonovo verjetnostno masno funkcijo pa FALSE."}, "POISSON.DIST": {"a": "(x; srednja_vrednost; kumulativno)", "d": "Vrne Poissonovo porazdelitev.", "ad": "je šte<PERSON>o dogodkov.!je pričakovana številska vrednost in je pozitivno število.!je logična vrednost: za kumulativno Poissonovo verjetnost uporabite TRUE, za Poissonovo verjetnostno masno funkcijo pa FALSE."}, "PROB": {"a": "(x_obseg; verjet_obseg; spodnja_meja; [zgor<PERSON>_meja])", "d": "<PERSON><PERSON>, da so vrednosti obsega med obema mejama ali enake spodnji meji.", "ad": "je obseg številskih vrednosti za x, ki so pridružene verjetnosti.!je nabor verjetnosti, povezanih z vrednostmi v »X_obseg«, in so vrednosti med 0 in 1 in niso 0.!je spodnja meja za vrednost, za katero določate verjetnost.!je izbirna zgornja meja vrednosti. Če jo i<PERSON>, PROB vrne verjetnost, da so vrednosti v »X_obseg« enake »spodnja_meja«."}, "QUARTILE": {"a": "(matrika; k<PERSON><PERSON>)", "d": "<PERSON><PERSON> na<PERSON>a <PERSON>.", "ad": "je matrika ali obseg celic s številskimi vrednostmi, za katere želite določiti kvartilne vrednosti.!je število: minimalna vrednost = 0; prvi kvartil = 1; mediana = 2; tret<PERSON> kvartil = 3; maks<PERSON>lna vrednost = 4."}, "QUARTILE.INC": {"a": "(matrika; k<PERSON><PERSON>)", "d": "Vrne kvartil nabora podatkov na podlagi vrednosti percentila med 0 in vključno 1.", "ad": "je matrika ali obseg celic s številskimi vrednostmi, za katere želite določiti kvartilne vrednosti.!je število: minimalna vrednost = 0; prvi kvartil = 1; mediana = 2; tret<PERSON> kvartil = 3; maks<PERSON>lna vrednost = 4."}, "QUARTILE.EXC": {"a": "(matrika; k<PERSON><PERSON>)", "d": "Vrne kvartil nabora podatkov na osnovi vrednosti percentila med 0 in izključno 1.", "ad": "je matrika ali obseg celic s številskimi vrednostmi, za katere želite določiti kvartilne vrednosti.!je število: minimalna vrednost = 0; prvi kvartil = 1; mediana = 2; tret<PERSON> kvartil = 3; maks<PERSON>lna vrednost = 4."}, "RANK": {"a": "(števil<PERSON>; sklic; [vrstni_red])", "d": "<PERSON><PERSON> rang števila na seznamu števil, ki je relativna velikost števila glede na druge vrednosti na seznamu.", "ad": "je <PERSON><PERSON><PERSON><PERSON>, ki mu želite poiskati rang.!je matrika števil ali sklic na seznamu števil. Neštevilske vrednosti so prezrte.!je š<PERSON><PERSON><PERSON>, ki do<PERSON><PERSON><PERSON>, kako bo urejen rang na seznamu: če je 0 ali <PERSON><PERSON><PERSON>, bo rang urejen padajoče; če je katera koli neničelna vrednost, bo rang urejen naraščajoče"}, "RANK.AVG": {"a": "(števil<PERSON>; sklic; [vrstni_red])", "d": "Vrne rang števila na seznamu števil, ki je relativna velikost števila glede na druge vrednosti na seznamu. Če ima več vrednosti enak rang, vrne povprečni rang.", "ad": "je š<PERSON><PERSON><PERSON>, ki mu želite poiskati rang.!je matrika števil ali sklic na seznam števil. Neštevilske vrednosti se ne upoštevajo.!je število: če je 0 ali <PERSON><PERSON><PERSON><PERSON>, bo rang urejen padajoče; če je katera koli neničelna vrednost, bo rang urejen naraščajoče."}, "RANK.EQ": {"a": "(števil<PERSON>; sklic; [vrstni_red])", "d": "Vrne rang števila na seznamu števil, ki je relativna velikost števila glede na druge vrednosti na seznamu. Če ima več vrednosti enak rang, vrne najvišji rang tiste množice vrednosti.", "ad": "je š<PERSON><PERSON><PERSON>, ki mu želite poiskati rang.!je matrika števil ali sklic na seznam števil. Neštevilske vrednosti se ne upoštevajo.!je število: če je 0 ali <PERSON><PERSON><PERSON><PERSON>, bo rang urejen padajoče; če je katera koli neničelna vrednost, bo rang urejen naraščajoče."}, "RSQ": {"a": "(known_ys; known_xs)", "d": "Vrne kvadrat Pearsonovega korelacijskega koeficienta.", "ad": "je matrika ali obseg podatkovnih toč<PERSON>, ki so lahko <PERSON> ali imena, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>.!je matrika ali obseg podatkovnih točk, ki so lahko <PERSON> ali imena, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>."}, "SKEW": {"a": "(število1; [število2]; ...)", "d": "Vrne asimetrijo porazdelitve, ki je označitev stopnje asimetrije porazdelitve okoli njene srednje vrednosti.", "ad": "od 1 do 255 <PERSON><PERSON><PERSON> ali imen, mat<PERSON> <PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, za katera želite izračunati asimetrijo"}, "SKEW.P": {"a": "(število1; [število2]; ...)", "d": "Vrne asimetrijo porazdelitve glede na populacijo: označitev stopnje asimetrije porazdelitve okoli njene srednje vrednosti.", "ad": "od 1 do 255 <PERSON><PERSON><PERSON> ali imen, mat<PERSON> <PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, za katera želite izračunati asimetrijo"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "Vrne naklon regresijske premice skozi dane podatkovne toč<PERSON>.", "ad": "je matrika ali obseg celic <PERSON> odvisnih podatkovnih točk, ki so lahko <PERSON> ali imena, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>.!je množica neodvisnih podatkovnih točk, ki so lahko <PERSON> ali imena, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>."}, "SMALL": {"a": "(matrika; k)", "d": "Vrne k-to najmanjšo vrednost nabora pod<PERSON>kov, na primer peto najmanjše število.", "ad": "je matrika ali obseg <PERSON><PERSON>, za katere želite določiti k-to najmanjšo vrednost.!je mesto vrednosti za vračanje (od najmanjšega) v matriki ali v obsegu celic."}, "STANDARDIZE": {"a": "(x; srednja_vrednost; standardni_odklon)", "d": "Vrne normalizirano vrednost iz porazdelitve, ki je označena s srednjo vrednostjo in standardnim odklonom.", "ad": "je vrednost, ki jo želite normalizirati.!je aritmetična srednja vrednost porazdelitve.!je standardni odklon porazdelitve in je pozitivno število."}, "STDEV": {"a": "(število1; [število2]; ...)", "d": "Oceni standardni odklon glede na vzorec (v vzorcu prezre logične vrednosti in besedilo).", "ad": "od 1 do 255 števil, ki ustrezajo vzorcu populacije in so lahko števila ali sklici s številkami"}, "STDEV.P": {"a": "(število1; [število2]; ...)", "d": "Izračuna standardni odklon na osnovi celotne populacije, podane v obliki argumentov (prezre logične vrednosti in besedilo).", "ad": "od 1 do 255 <PERSON><PERSON>, ki ustre<PERSON>jo populaciji in so la<PERSON><PERSON> ali sklici, ki vs<PERSON><PERSON><PERSON><PERSON>"}, "STDEV.S": {"a": "(število1; [število2]; ...)", "d": "Oceni standardni odklon vzorca na osnovi vzorca (v vzorcu prezre logične vrednosti in besedilo).", "ad": "od 1 do 255 argument<PERSON>, ki ustre<PERSON><PERSON> v<PERSON><PERSON> populacije in so <PERSON><PERSON><PERSON> ali sklici, ki vs<PERSON><PERSON><PERSON><PERSON>"}, "STDEVA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Ugotovi standardni odklon, ki temelji na vzorcu, ki vsebuje logične vrednosti in besedilo. Besedilo in logična vrednost FALSE imata vrednost 0, logična vrednost TRUE pa ima vrednost 1.", "ad": "od 1 do 255 v<PERSON><PERSON><PERSON>, ki ustrezajo vzorcu iz populacije in so lahko vrednosti, imena ali sklici na vrednosti"}, "STDEVP": {"a": "(število1; [število2]; ...)", "d": "Izračuna standardni odklon na podlagi celotne populacije, navedene v obliki argumentov (prezre logične vrednosti in besedilo).", "ad": "od 1 do 255 šte<PERSON>, ki us<PERSON>zajo populaciji in so lahko <PERSON> ali sklici s <PERSON>tevili"}, "STDEVPA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Izračuna standardni odklon, ki temelji na celotni populaciji, vključno z logičnimi vrednostmi in besedilom. Besedilo in logična vrednost FALSE imata vrednost 0, logična vrednost TRUE pa ima vrednost 1.", "ad": "od 1 do 255 v<PERSON><PERSON><PERSON>, ki us<PERSON><PERSON><PERSON> populaciji in so <PERSON><PERSON><PERSON> vred<PERSON>, im<PERSON>, matrike ali <PERSON>, ki vseb<PERSON><PERSON><PERSON> vrednosti"}, "STEYX": {"a": "(known_ys; known_xs)", "d": "Vrne standardno napako predvidenih y-vrednosti za vsak x v regresiji.", "ad": "je matrika ali obseg odvisnih podatkovnih to<PERSON>k, ki so lah<PERSON> ali imena, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>.!je matrika ali obseg neodvisnih podatkovnih točk, ki so lahko <PERSON> ali imena, matrike ali sklic<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>."}, "TDIST": {"a": "(x; stop_prostosti; repi)", "d": "Vrne študentovo t-porazdelitev.", "ad": "je številska vrednost, pri kateri se porazdelitev ovrednoti.!je celo <PERSON>, ki označuje število stopenj prostosti, ki označujejo porazdelitev.!do<PERSON><PERSON><PERSON>, koliko repov porazdelitve bo vrnila funkcija: enorepa porazdelitev = 1; dvorepa porazdelitev = 2."}, "TINV": {"a": "(verjetnost; stop_Prostosti)", "d": "Vrne dvorepo inverzno Studentovo t-porazdelitev.", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON>, povezana z dvorepo Studentovo t-porazdelitvijo in je število med 0 in vključno 1.!je naravno število, ki označuje stopnje prostosti in tako tudi porazdelitev."}, "T.DIST": {"a": "(x; stop_prostosti; kumula<PERSON>vno)", "d": "Vrne levorepo Studentovo t-porazdelitev.", "ad": "je številska vrednost, pri kateri se porazdelitev ovrednoti.!je celo <PERSON>, ki označuje število stopenj prostosti, ki označujejo porazdelitev.!je logična vrednost: za kumulativno porazdelitveno funkcijo uporabite TRUE; za porazdelitev gostote verjetnosti pa FALSE."}, "T.DIST.2T": {"a": "(x; stop_prostosti)", "d": "Vrne dvorepo Studentovo t-porazdelitev.", "ad": "je števil<PERSON>na vrednosti, pri kateri se porazdelitev ovrednoti.!je c<PERSON>, ki označuje število stopenj prostosti, ki označujejo porazdelitev."}, "T.DIST.RT": {"a": "(x; stop_prostosti)", "d": "Vrne desnorepo Studentovo t-porazdelitev.", "ad": "je številska vrednost, pri kateri se porazdelitev ovrednoti.!je celo <PERSON>, ki označuje število stopenj prostosti, ki označujejo porazdelitev"}, "T.INV": {"a": "(verjetnost; stop_prostosti)", "d": "Vrne levorepo inverzno Studentovo t-porazdelitev.", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON>, povezana z dvorepo Studentovo t-porazdelitvijo in je število med 0 in vključno 1.!je pozitivno celo <PERSON>vilo, ki označuje število stopenj prostosti in tako tudi porazdelitev."}, "T.INV.2T": {"a": "(verjetnost; stop_prostosti)", "d": "Vrne desnorepo inverzno Studentovo t-porazdelitev.", "ad": "je <PERSON><PERSON><PERSON><PERSON><PERSON>, povezana z dvorepo Studentovo t-porazdelitvijo in je število med 0 in vključno 1.!je pozitivno celo <PERSON>vilo, ki označuje število stopenj prostosti in tako tudi porazdelitev."}, "T.TEST": {"a": "(matrika1; matrika2; repi; vrsta)", "d": "<PERSON><PERSON>, povezano s Studentovim t-preizkusom.", "ad": "je prvi nabor podatkov.!je drugi nabor podatkov.!<PERSON><PERSON><PERSON><PERSON>, koliko repov porazdelitve bo vrnila funkcija: enorepa porazdelitev = 1; dvorepa porazdelitev = 2.!je vrsta t-preizkusa: v paru = 1; dvo<PERSON><PERSON><PERSON><PERSON>, z enako varianco (homoskedastični) = 2; dvo<PERSON><PERSON><PERSON><PERSON>, z neenako varianco = 3."}, "TREND": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Vrne števila v linearnem trendu, ujemajočem z znanimi podatkovnimi točkami po metodi najmanjših kvadratov.", "ad": "je obseg ali matrika y-vred<PERSON>ti, ki jih že poznate za razmerje y = mx + b.!je izbiren obseg ali matrika x-vrednosti, ki jih že poznate za razmerje y = mx + b in je enake velikosti kot znani y-i (Known_y).!je obseg ali matrika novih x-vrednosti, za katere naj TREND vrne pripadajoče y-vrednosti.!je logična vrednost: konstanta b se izračuna normalno, če je Const = TRUE ali ni določena; b ima vrednost 0, če je Const = FALSE."}, "TRIMMEAN": {"a": "(matrika; odstotek)", "d": "Vrne srednjo vrednost iz množice podatkovnih vrednosti.", "ad": "je obseg ali matrika vrednosti, ki se obreže in izračuna povprečje.!je racionalno število (ulomek) – dele<PERSON> podatkovnih točk, ki jih želite izločiti iz računanja."}, "TTEST": {"a": "(matrika1; matrika2; repi; vrsta)", "d": "<PERSON><PERSON>, povezano s Studentovim t-preskusom.", "ad": "je prvi nabor podatkov.!je drugi nabor podatkov.!<PERSON><PERSON><PERSON><PERSON>, koliko repov porazdelitve bo vrnila funkcija: enorepa porazdelitev = 1; dvorepa porazdelitev = 2.!je vrsta t-preizkusa: v paru = 1; dvo<PERSON><PERSON><PERSON><PERSON>, z enako varianco (homoskedastični) = 2; dvo<PERSON><PERSON><PERSON><PERSON>, z neenako varianco = 3."}, "VAR": {"a": "(število1; [število2]; ...)", "d": "Oceni odmik glede na vzorec (v vzorcu prezre logične vrednosti in besedilo).", "ad": "od 1 do 255 <PERSON><PERSON><PERSON><PERSON><PERSON>, ki ustrezajo v<PERSON><PERSON> populacije"}, "VAR.P": {"a": "(število1; [število2]; ...)", "d": "Izračuna varianco na osnovi celotne populacije (v populaciji prezre logične vrednosti in besedilo).", "ad": "od 1 do 255 <PERSON><PERSON><PERSON><PERSON><PERSON>, ki ustrezajo populaciji"}, "VAR.S": {"a": "(število1; [število2]; ...)", "d": "Oceni varianco na osnovi vzorca (v vzorcu prezre logične vrednosti in besedilo).", "ad": "od 1 do 255 <PERSON><PERSON><PERSON><PERSON><PERSON>, ki ustrezajo v<PERSON><PERSON> populacije"}, "VARA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>, ki temelji na vzorcu, ki vseb<PERSON>je tudi logične vrednosti in besedilo. Besedilo in logična vrednost FALSE imata vrednost 0, logična vrednost TRUE pa ima vrednost 1.", "ad": "od 1 do 255 argument<PERSON>, ki ustrezajo vzorcu iz populacije"}, "VARP": {"a": "(število1; [število2]; ...)", "d": "Izračuna odmik na podlagi celotne populacije (prezre logične vrednosti in besedilo v populaciji).", "ad": "od 1 do 255 <PERSON><PERSON><PERSON><PERSON><PERSON>, ki ustrezajo populaciji"}, "VARPA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Izračuna varianco, ki temelji na celotni populaciji, ki vsebuje tudi logične vrednosti in besedilo. Besedilo in logična vrednost FALSE imata vrednost 0, logična vrednost TRUE pa ima vrednost 1.", "ad": "od 1 do 255 <PERSON><PERSON>, ki us<PERSON><PERSON><PERSON> populaciji"}, "WEIBULL": {"a": "(x; alfa; beta; kumulativno)", "d": "<PERSON>rne <PERSON>ovo porazdelitev.", "ad": "je vrednost, pri kateri se ovrednoti funkcija, in je nenegativno število.!je parameter porazdelitve in je pozitivno število.!je parameter porazdelitve in je pozitivno število.!je logična vrednost: za kumulativno porazdelitveno funkcijo uporabite TRUE; za verjetnostno masno funkcijo pa FALSE."}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kumulativno)", "d": "<PERSON>rne <PERSON>ovo porazdelitev.", "ad": "je vrednost, pri kateri se ovrednoti funkcija, in je nenegativno število.!je parameter porazdelitve in je pozitivno število.!je parameter porazdelitve in je pozitivno število.!je logična vrednost: za kumulativno porazdelitveno funkcijo uporabite TRUE; za verjetnostno masno funkcijo pa FALSE."}, "Z.TEST": {"a": "(matrika; x; [sigma])", "d": "Vrne enorepo P-vrednost z-preizkusa.", "ad": "je matrika ali o<PERSON><PERSON> pod<PERSON>, proti katerim se preizkuša x.!je vrednos<PERSON>, ki se preizkuša.!je (znani) standardni odklon populacije. Če je <PERSON><PERSON><PERSON>, bo uporabljen standardni odklon vzorca"}, "ZTEST": {"a": "(matrika; x; [sigma])", "d": "Vrne enorepo P-vrednost z-preizkusa.", "ad": "je matrika ali o<PERSON><PERSON> pod<PERSON>, proti katerim se preizkuša x.!je vred<PERSON>t, ki se preizkuša.!je (znani) standardni odklon populacije. Če se i<PERSON><PERSON><PERSON>, bo uporabljen standardni odklon vzorca"}, "ACCRINT": {"a": "(i<PERSON><PERSON>; p<PERSON>_o<PERSON>ti; poravnava; mera; vrednost; pogostost; [osnova]; [nač<PERSON>_izrač<PERSON>])", "d": "Vrne povečane obresti za vrednostni papir, ki periodično izplačuje obresti.", "ad": "je datum izdaje vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum prvih obresti vrednostnega papirja, izražen kot zaporedna številka datuma!je datum poravnave vrednostnega papirja, izražen kot zaporedna številka datuma!je letna obrestna mera vrednostnega papirja!je imenska vrednost vrednostnega papirja!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo!je logična vrednost: na povečane obresti z datuma izdaje = TRUE ali izpuščeno; za izračun z datuma zadnjega plačila vrednostnega papirja = FALSE"}, "ACCRINTM": {"a": "(izdaja; poravnava; mera; vrednost; [osnova])", "d": "Vrne povečane obresti za vrednostni papir, ki izplača obresti ob zapadlosti", "ad": "je datum izdaje vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je letna obrestna mera vrednostnega papirja!je imenska vrednost vrednostnega papirja!je vrsta na osnovi štetja dni za uporabo"}, "AMORDEGRC": {"a": "(s<PERSON><PERSON><PERSON>; datum_nakupa; prvo_obdobje; amortizacija; obdobje; stopnja; [osnova])", "d": "Vrne sorazmerno linearno razvrednotenje sredstva za posamezno računovodsko obdobje.", "ad": "is cena sredstva!je datum nakupa sredstva!je datum konca prvega obdobja!je rešena vrednost ob koncu življenjske dobe sredstva.!je obdobje!je stopnja razvrednotenja!na_osnovi_leta: 0 za leto 360 dni, 1 za dejansko, 3 za leto 265 dni."}, "AMORLINC": {"a": "(s<PERSON><PERSON><PERSON>; datum_nakupa; prvo_obdobje; amortizacija; obdobje; stopnja; [osnova])", "d": "Vrne sorazmerno linearno razvrednotenje sredstva za posamezno računovodsko obdobje.", "ad": "is cena sredstva!je datum nakupa sredstva!je datum konca prvega obdobja!je rešena vrednost ob koncu življenjske dobe sredstva.!je obdobje!je stopnja razvrednotenja!na_osnovi_leta: 0 za leto 360 dni, 1 za dejansko, 3 za leto 265 dni."}, "COUPDAYBS": {"a": "(poravnava; zapadlost; pogostost; [osnova])", "d": "Vrne število dni od začetka obdobja vrednostnega papirja do datuma poravnave", "ad": "je datum poravnave vrednostnega papirja, izražen kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "COUPDAYS": {"a": "(poravnava; zapadlost; pogostost; [osnova])", "d": "Vrne število dni v obdobju vrednostnega papirja, ki vsebuje datum poravnave", "ad": "je datum poravnave vrednostnega papirja, izražen kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "COUPDAYSNC": {"a": "(poravnava; zapadlost; pogostost; [osnova])", "d": "Vrne število dni od datuma poravnave do naslednjega datuma vrednostnega papirja", "ad": "je datum poravnave vrednostnega papirja, izražen kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "COUPNCD": {"a": "(poravnava; zapadlost; pogostost; [osnova])", "d": "Vrne naslednji datum poravnave po datumu poravnave", "ad": "je datum poravnave vrednostnega papirja, izražen kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "COUPNUM": {"a": "(poravnava; zapadlost; pogostost; [osnova])", "d": "<PERSON><PERSON> število vrednostnih papirjev, ki bodo plačani med datumom poravnave in datumom zapadlosti", "ad": "je datum poravnave vrednostnega papirja, izražen kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "COUPPCD": {"a": "(poravnava; zapadlost; pogostost; [osnova])", "d": "Vrne prejšnji datum vrednostnega papirja pred datumom poravnave", "ad": "je datum poravnave vrednostnega papirja, izražen kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "CUMIPMT": {"a": "(mera; št_obdobij; sv; začetno_obdobje; končno_obdobje; vrsta)", "d": "<PERSON><PERSON> kumulativ<PERSON>, plačane med dvema obdobjema", "ad": "je obrestna mera!je skupno število plačilnih obdobij!je sedanja vrednost!je prvo obdobje v izračunu!je zadnje obdobje v izračunu!je čas plačila"}, "CUMPRINC": {"a": "(mera; št_obdobij; sv; začetno_obdobje; končno_obdobje; vrsta)", "d": "<PERSON><PERSON> kumulativno glavnico, plačano s posojilom med dvema obdobjema", "ad": "je obrestna mera!je skupno število plačilnih obdobij!je sedanja vrednost!je prvo obdobje v izračunu!je zadnje obdobje v izračunu!je čas plačila"}, "DB": {"a": "(s<PERSON><PERSON><PERSON>; vrednost_po_amor; št_obdobij; obdobje; [mesec])", "d": "Vrne amortizacijo sredstva za določeno obdobje po metodi fiksnopojemajočega salda.", "ad": "je začetna cena sredstva.!je rešena vrednost na koncu življenjske dobe sredstva.!je število obdobij, prek katerih se amortizira sredstvo (imenovano tudi življenjska doba sredstva).!je ob<PERSON><PERSON><PERSON>, za katerega želite izračunati amortizacijo. Obdobje mora biti v istih enotah kot življenjska doba.!je število mesecev v prvem letu. Kadar jih izpustimo, se privzame 12 mesecev."}, "DDB": {"a": "(s<PERSON><PERSON><PERSON>; vrednost_po_amor; št_obdobij; obdbo<PERSON>; [faktor])", "d": "Vrne amortizacijo sredstva za določeno obdobje z metodo dvojnopojemajočega salda ali s kakšno drugo metodo, ki jo dolo<PERSON>.", "ad": "je začetna cena sredstva.!je rešena vrednost na koncu življenjske dobe sredstva.!je šte<PERSON>o obdobij, prek katerih se amortizira sredstvo (imenovano tudi življenjska doba sredstva).!je ob<PERSON><PERSON><PERSON>, za katerega želite izračunati amortizacijo. Obdobje mora biti v istih enotah kot življenjska doba.!je hitrost upadanja salda. Izpustitev faktorja pomeni, da je ta 2 (metoda dvojnopojemajočega salda)."}, "DISC": {"a": "(poravnava; zapadlost; cena; odkup; [osnova])", "d": "<PERSON>rne stopnjo rabata vrednostnega papirja", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je cena vrednostnega papirja na 100 € imenske vrednosti!je amortizacijska vrednost vrednostnega papirja na €100 imenske vrednosti!je vrsta na osnovi štetja dni za uporabo"}, "DOLLARDE": {"a": "(ulomek_valuta; imenovalec)", "d": "Pretvori ceno v dolarjih, izraženo kot ulomek, v ceno v dolarjih, izraženo kot decimalno število", "ad": "je <PERSON><PERSON><PERSON><PERSON>, izraženo kot ulomek!je celo število, uporabljeno v imenovalcu ulomka"}, "DOLLARFR": {"a": "(decimalka_valuta; imenovalec)", "d": "Pretvori ceno v dolarjih, izraženo kot decimalno število, v ceno v dolarjih, izraženo kot ulomek", "ad": "je decimalno število!je celo število, uporabljeno v imenovalcu ulomka"}, "DURATION": {"a": "(p<PERSON>vna<PERSON>; zapadlost; o<PERSON><PERSON>; donos; pogo<PERSON><PERSON>; [osnova])", "d": "Vrne letno trajanje vrednostnega papirja s periodičnimi plačili o<PERSON>", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je letna obrestna mera vrednostnega papirja!je letni donos vrednostnega papirja!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "EFFECT": {"a": "(nominalna_obr_mera; št_obdobij_leto)", "d": "Vrne efektivno letno obrestno mero", "ad": "je nominalna obrestna mera!je število združenih obdobij na leto"}, "FV": {"a": "(mera; obdobja; pla<PERSON>ilo; [sv]; [vrsta])", "d": "<PERSON><PERSON> bodočo vrednost naložbe, ki temelji na periodičnih, en<PERSON>h pla<PERSON> in nespremenljivi obrestni meri.", "ad": "je obrestna mera na obdobje. Za četrtletna plačila pri 6 % APR na primer uporabite 6 %/4.!je skupno število plačilnih obdobij na naložbo.!je pla<PERSON><PERSON>, nakazano v vsakem obdobju; ne sme se spreminjati prek življenja naložbe.!je sedanja vrednost ali enkratni znesek, ki predstavlja sedanjo vrednost vrste bodočih plačil. Če je izpuščena, je sedanja vrednost 0.!je š<PERSON><PERSON><PERSON>, ki označuje, kdaj so plačila dospela: na začetku obdobja = 1; na koncu obdobja = 0 ali ni določeno."}, "FVSCHEDULE": {"a": "(glavnica; razpored)", "d": "Vrne bodočo vrednost začetne glavnice po uporabi niza sestavljenih obrestnih mer", "ad": "je sedanja vrednost!je matrika o<PERSON>nih mer, ki bodo uporabljene"}, "INTRATE": {"a": "(poravnava; zapadlost; naložba; odkup; [osnova])", "d": "Vrne obrestno mero za v celoti vloženi vrednostni papir", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je koli<PERSON>ina, v<PERSON>žena v vrednostni papir!je količina, ki bo prejeta ob zapadlosti!je vrsta na osnovi štetja dni za uporabo"}, "IPMT": {"a": "(mera; obdobje; št_plačil; sv; [pv]; [vrsta])", "d": "Vrne plačilo obresti za naložbo v navedenem obdobju, ki temelji na periodičnih, enakih plačilih in nespremenljivi obrestni meri.", "ad": "je obrestna mera na obdobje. Za četrtletna plačila pri 6 % APR na primer uporabite 6 %/4.!je ob<PERSON><PERSON><PERSON>, v katerem iščete obresti, in mora biti v obsegu od 1 do »št_plačil«.!je skupno število plačilnih obdobij na naložbo.!je sedanja vrednost ali enkratni znesek, ki predstavlja sedanjo vrednost vrste bodočih plačil.!je bodoča vrednost ali blagajniško stanje, ki ga želite doseči po izvedbi zadnjega plačila. Če ni določeno, velja »Pv = 0«.!je logična vrednost, ki predstavlja čas plačila: na koncu obdobja = 0 ali ni določeno, na začetku obdobja = 1."}, "IRR": {"a": "(v<PERSON><PERSON><PERSON>; [domneva])", "d": "<PERSON><PERSON> notranjo stopnjo donosa za vrsto denarnih tokov.", "ad": "je matrika ali sklic na celice s <PERSON>, ki jim želite določiti notranjo stopnjo donosa.!je <PERSON><PERSON><PERSON><PERSON>, za ka<PERSON><PERSON><PERSON> u<PERSON>, da je blizu rezultatu IRR; 0.1 (10 ods<PERSON><PERSON><PERSON>), če je i<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "ISPMT": {"a": "(mera; obdobje; št_obdobij; sv)", "d": "Vrne obresti, plačane v določenem obdobju naložbe.", "ad": "obrestna mera na obdobje. Za četrtletna plačila pri 6 % APR na primer uporabite 6 %/4.!obdo<PERSON><PERSON>, za katerega želite poiskati obresti.!število plačilnih obdobij za naložbo.!enkratno izplačilo v sedanji vrednosti vrste bodočih izplačil."}, "MDURATION": {"a": "(p<PERSON>vna<PERSON>; zapadlost; o<PERSON><PERSON>; donos; pogo<PERSON><PERSON>; [osnova])", "d": "Vrne trajanje vrednostnega papirja, spremenjeno po <PERSON>u, z domnevno enako vrednostjo 100 €", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je letna obrestna mera vrednostnega papirja!je letni donos vrednostnega papirja!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "MIRR": {"a": "(v<PERSON><PERSON><PERSON>; obrestna_mera; mera_reinvesticije)", "d": "Vrne notranjo stopnjo donosa za vrsto periodičnih denarnih tokov, upoštevajoč oba: ceno naložbe in obresti na vnovično naložbo denarja.", "ad": "je matrika ali sklic na celice s <PERSON>vili, ki predstavljajo vrsto plačil (negativno) in prihodek (pozitivno) v pravilnih časovnih obdobjih.!je obrestna mera, ki jo plačate za denar v denarnih tokovih.!je obrestna mera, ki jo prejmete na denarna tokova med vnovično naložbo."}, "NOMINAL": {"a": "(efektivna_obr_mera; št_obdobij_leto)", "d": "Vrne nominalno letno obrestno mero", "ad": "je efektivna obrestna mera!je število združenih obdobij na leto"}, "NPER": {"a": "(mera; plačilo; sv; [pv]; [vrsta])", "d": "<PERSON>rne število obdobij za naložbo, ki temelji na periodičnih, enakih plačilih in nespremenljivi obrestni meri.", "ad": "je obrestna mera na obdobje. Za četrtletna plačila pri 6 % APR na primer uporabite 6 %/4.!je plačilo nakazano v vsakem obdobju; ne sme se spreminjati prek življenja naložbe.!je sedanja vrednost ali enkratni znesek, ki predstavlja sedanjo vrednost vrste bodočih plačil.!je bodoča vrednost ali blagajniško stanje, ki ga želite doseči po izvedbi zadnjega plačila. Če izpustite, je uporabljena vrednost nič.!je logična vrednost: plačilo v začetku obdobja = 1; plačilo na koncu obdobja = 0 ali izpuščeno."}, "NPV": {"a": "(stopnja; vrednost1; [vrednost2]; ...)", "d": "Vrne sedanjo neto vrednost naložbe, ki temelji na diskontni stopnji in na vrsti bodočih plačil (negativne vrednosti) in prihodku (pozitivne vrednosti).", "ad": "je diskontna stopnja v enem obdobju.!od 1 do 254 enakomerno porazdeljenih plačil in prihodkov, ki se pojavljajo na koncu posameznega obdobja"}, "ODDFPRICE": {"a": "(poravnava; zapadlost; izdaja; p<PERSON>_o<PERSON><PERSON>; mera; dnoso; odkup; pogostost; [osnova])", "d": "Vrne ceno vrednostnega papirja na 100 € imenske vrednosti, z lihim prvim obdobjem", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je datum izdaje vrednostnega papirja, izražen kot zaporedna številka datuma!je datum prvega vrednostnega papirja, izražen kot zaporedna številka datuma!je obrestna mera vrednostnega papirja!je letni donos vrednostnega papirja!je amortizacijska vrednost vrednostnega papirja na 100 € imenske vrednosti!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "ODDFYIELD": {"a": "(poravnava; zapadlost; izdaja; p<PERSON>_o<PERSON><PERSON>; mera; cena; odkup; pogostost; [osnova])", "d": "Vrne donos vrednostnega papirja z lihim prvim obdobjem", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je datum izdaje vrednostnega papirja, izražen kot zaporedna številka datuma!je datum prvega vrednostnega papirja!je obrestna mera vrednostnega papirja!je cena vrednostnega papirja!je amortizacijska vrednost vrednostnega papirja na 100 € imenske vrednosti!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "ODDLPRICE": {"a": "(poravnava; zapadlost; zadn<PERSON>_obresti; mera; donos; odkup; pogostost; [osnova])", "d": "Vrne ceno vrednostnega papirja na 100 € imenske vrednosti, z lihim zadnjim obdobjem", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je zadnji datum vrednostnega papirja, izražen kot zaporedna številka datuma!je obrestna mera vrednostnega papirja!je letni donos vrednostnega papirja!je amortizacijska vrednost vrednostnega papirja na 100 € imenske vrednosti!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "ODDLYIELD": {"a": "(poravnava; zapadlost; zadn<PERSON>_obresti; mera; cena; odkup; pogostost; [osnova])", "d": "Vrne donos vrednostnega papirja z lihim zadnjim obdobjem", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je zadnji datum vrednostnega papirja, izražen kot zaporedna številka datuma!je obrestna mera vrednostnega papirja!je cena vrednostnega papirja!je amortizacijska vrednost vrednostnega papirja na 100 € imenske vrednosti!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "<PERSON><PERSON>, ki jih investicija zah<PERSON>va, če ž<PERSON> doseči določeno vrednost", "ad": "je obrestna mera na obdobje.!je sedanja vrednost investicije!je želena vrednost investicije v prihodnosti"}, "PMT": {"a": "(mera; obdobja; sv; [pv]; [vrsta])", "d": "Izračuna plačilo za posojilo, ki temelji na enakih plačilih in nespremenljivi obrestni meri.", "ad": "je obrestna mera za posojilo na obdobje.!je skupno število plačilnih obdobij na posojilo. Za četrtletna plačila pri 6 % APR na primer uporabite 6 %/4.!je sedanja vrednost: s<PERSON><PERSON>, enak sedanji vrednosti vrste bodočih plačil.!je bodoča vrednost ali blagajniško stanje, ki ga želite doseči po izvedbi zadnjega plačila, nič, če je izpuščeno.!je logična vrednost: plačilo v začetku obdobja = 1; plačilo na koncu obdobja = 0 ali izpuščeno."}, "PPMT": {"a": "(mera; obdobja; št_plačil; sv; [pv]; [vrsta])", "d": "Vrne plačilo na glavnico za naložbo, ki temelji na enakih plačilih in nespremenljivi obrestni meri.", "ad": "je obrestna mera na obdobje. Za četrtletna plačila pri 6 % APR na primer uporabite 6 %/4.!določa obdobje in mora biti v obsegu od 1 do »št_plačil«.!je skupno število plačilnih obdobij na naložbo.!je sedanja vrednost: sku<PERSON> znesek, enak sedanji vrednosti vrste bodočih plačil.!je bodoča vrednost ali blagajniško stanje, ki ga želite doseči po izvedbi zadnjega plačila.!je logična vrednost: plačilo v začetku obdobja = 1; plačilo na koncu obdobja = 0 ali izpuščeno."}, "PRICE": {"a": "(poravnava; zapadlost; stopnja; obresti; odkup; pogostost; [osnova])", "d": "Vrne ceno vrednostnega papirja na 100 € imenske vrednosti, ki izplača periodične obresti", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je letna obrestna mera vrednostnega papirja!je letni donos vrednostnega papirja!je amortizacijska vrednost vrednostnega papirja na 100 € imenske vrednosti!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "PRICEDISC": {"a": "(poravnava; zapadlost; rabata; odkup; [osnova])", "d": "Vrne ceno vrednostnega papirja z rabatom na 100 € imenske vrednosti", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je stopnja rabata vrednostnega papirja!je amortizacijska vrednost vrednostnega papirja na 100 € imenske vrednosti!je vrsta na osnovi štetja dni za uporabo"}, "PRICEMAT": {"a": "(poravnava; zapadlost; izdaja; stopnja; obresti; [osnova])", "d": "Vrne ceno vrednostnega papirja na 100 € imenske vrednosti, ki izplača obresti ob zapadlosti", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je datum izdaje vrednostnega papirja, izražen kot zaporedna številka datuma!je stopnja obresti vrednostnega papirja na datum izdaje!je letni donos vrednostnega papirja!je vrsta na osnovi štetja dni za uporabo"}, "PV": {"a": "(mera; obdobja; pla<PERSON>ilo; [sv]; [vrsta])", "d": "Vrne sedanjo vrednost naložbe: celotna vsota vrednosti vrste bodočih plačil v tem trenutku.", "ad": "je obrestna mera na obdobje. Za četrtletna plačila pri 6 % APR na primer uporabite 6 %/4.!je skupno število plačilnih obdobij na naložbo.!je plačilo, izvedeno v vsakem obdobju, in se ne more spremeniti prek življenja naložbe.!je bodoča vrednost ali blagajniško stanje, ki ga želite doseči po izvedbi zadnjega plačila.!je logična vrednost: plačilo v začetku obdobja = 1; plačilo na koncu obdobja = 0 ali izpuščeno."}, "RATE": {"a": "(obdo<PERSON><PERSON>; plačilo; sv; [pv]; [vrsta]; [domneva])", "d": "Vrne obrestno mero na obdobje posojila ali naložbe. Za četrtletna plačila pri 6 % APR na primer uporabite 6 %/4.", "ad": "je skupno število plačilnih obdobij na posojilo ali naložbo.!je pla<PERSON>ilo, izvedeno v vsakem obdobju, in se ne more spremeniti prek življenja posojila ali naložbe.!je sedanja vrednost: skupen znesek enak sedanji vrednosti vrste bodočih plačil.!je bodoča vrednost ali blagajniško stanje, ki ga želite doseči po izvedbi zadnjega plačila. Če je izpuščeno, se uporabi Pv = 0.!je logična vrednost: plačilo v začetku obdobja = 1; plačilo na koncu obdobja = 0 ali izpuščeno.!je va<PERSON> domneva, kakšna bo mera. Če je izpuščeno, je va<PERSON> do<PERSON>ne<PERSON> (Domneva) enaka 0,1 (10 odstotkov)."}, "RECEIVED": {"a": "(poravnava; zapadlost; naložba; rabat; [osnova])", "d": "<PERSON><PERSON>, ki je prejeta ob zapadlosti za v celoti vloženi vrednostni papir", "ad": "je datum poravnave vrednostnega papirja, izražen kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je koli<PERSON>ina, vložena v vrednostni papir!je stopnja rabata vrednostnega papirja!je vrsta na osnovi štetja dni za uporabo"}, "RRI": {"a": "(nper; pv; fv)", "d": "Vrne ekvivalentno obrestno mero za rast investicije", "ad": "je število obdobij investicije!je trenutna vrednost investicije!je prihodnja vrednost investicije"}, "SLN": {"a": "(stro<PERSON><PERSON>; vrednost_po_amor; št_obdobij)", "d": "Vrne linearno amortizacijo sredstva za eno obdobje.", "ad": "je začetna cena sredstva.!je rešena vrednost na koncu življenjske dobe sredstva.!je število obdobij, prek katerih se amortizira sredstvo (imenovano tudi življenjska doba sredstva)."}, "SYD": {"a": "(stro<PERSON><PERSON>; vrednost_po_amor; št_obdobij; obdobje)", "d": "Vrne amortizacijo po metodi vsote letnih števk za sredstvo prek določenega obdobja.", "ad": "je začetna cena sredstva.!je rešena vrednost na koncu življenjske dobe sredstva.!je šte<PERSON>o obdobij, prek katerih se amortizira sredstvo (imenovano tudi življenjska doba sredstva).!je obdobje in mora biti v enakih enotah kot življenjska doba."}, "TBILLEQ": {"a": "(poravnava; zapadlost; rabat)", "d": "<PERSON><PERSON>, ki je enak o<PERSON>vez<PERSON>, za zakladno menico", "ad": "je datum poravnave zakladne menice, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti zakladne menice, izražen kot zaporedna številka datuma!je stopnja rabata zakladne menice"}, "TBILLPRICE": {"a": "(poravnava; zapadlost; rabat)", "d": "Vrne ceno zakladne menice na 100 € imenske vrednosti", "ad": "je datum poravnave zakladne menice, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti zakladne menice, izražen kot zaporedna številka datuma!je stopnja rabata zakladne menice"}, "TBILLYIELD": {"a": "(poravnava; zapadlost; cena)", "d": "Vrne donos za zakladno menico", "ad": "je datum poravnave zakladne menice, izražen kot zaporedna številka datuma!je datum zapadlosti zakladne menice, izražen kot zaporedna številka datuma!je cena zakladne menice na 100 € imenske vrednosti"}, "VDB": {"a": "(s<PERSON><PERSON><PERSON>; vred<PERSON>t_po_amor; št_obdobij; začetno_obdobje; končno_obdobje; [faktor]; [brez_preklopa])", "d": "Vrne amortizacijo sredstva za poljubno obdobje (tudi za delna obdobja), ki ga dolo<PERSON>ite, z metodo dvojnopojemajočega salda ali s kakšno drugo metodo, ki jo določite.", "ad": "je začetna cena sredstva.!je rešena vrednost na koncu življenjske dobe sredstva.!je število obdobij, prek katerih se amortizira sredstvo (imenovano tudi življenjska doba sredstva).!je začetno obdobje, za katerega želite izračunati amortizacijo, in mora biti v istih časovnih enotah kot »št_obdobij«.!je zadnje obdobje, za katerega želite izračunati amortizacijo, in mora biti v istih časovnih enotah kot »št_obdobij«.!je mera, s katero saldo upada. Če jo izpus<PERSON>, je privzeta vrednost 2 (način dvojnopojemajočega salda).!program preklopi na linearno amortizacijo, ko je amortizacija večja od izračunanega upadajočega salda = FALSE ali izpuščeno; program ne preklopi = TRUE."}, "XIRR": {"a": "(v<PERSON><PERSON><PERSON>; datumi; [domneva])", "d": "Vrne notranjo stopnjo povračila za razpored pretokov denarja", "ad": "je niz pretokov denarja, ki ustrezajo razporedu plačil v datumih!je razpored datumov plačil, ki ustrezajo plačilom pretokov denarja!je <PERSON><PERSON><PERSON><PERSON>, za katerega menite, da je blizu rezultatu XIRR"}, "XNPV": {"a": "(stopnja; vrednosti; datumi)", "d": "Vrne sedanjo neto vrednost za razpored pretokov denarja", "ad": "je stop<PERSON> rabata, ki jo boste uporabili na pretokih denarja!je niz pretokov denarja, ki ustrezajo razporedu plačil v datumih!je razpored datumov plačil, ki ustrezajo plačilom pretokov denarja"}, "YIELD": {"a": "(poravnava; zapadlost; stopnja; cena; odkup; pogostost; [osnova])", "d": "Vrne donos na vrednostnem papirju, ki izplačuje periodične obresti", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je letna obrestna mera vrednostnega papirja!je cena vrednostnega papirja na 100 € imenske vrednosti!je amortizacijska vrednost vrednostnega papirja na 100 € imenske vrednosti!je število plačil vrednostnega papirja na leto!je vrsta na osnovi štetja dni za uporabo"}, "YIELDDISC": {"a": "(poravnava; zapadlost; cena; odkup; [osnova])", "d": "<PERSON><PERSON> letni donos za vrednostni papir z rabatom. Na primer, zakladna menica", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je cena vrednostnega papirja na 100 € imenske vrednosti!je amortizacijska vrednost vrednostnega papirja na 100 € imenske vrednosti!je vrsta na osnovi štetja dni za uporabo"}, "YIELDMAT": {"a": "(poravnava; zapadlost; izdaja; mera; cena; [osnova])", "d": "Vrne letni donos vrednostnega papirja, ki izplača obresti ob zapadlosti", "ad": "je datum poravnave vrednostnega papirja, izra<PERSON>en kot zaporedna številka datuma!je datum zapadlosti vrednostnega papirja, izražen kot zaporedna številka datuma!je datum izdaje vrednostnega papirja, izražen kot zaporedna številka datuma!je obrestna mera vrednostnega papirja na dan izdaje!je cena vrednostnega papirja na 100 € imenske vrednosti!je vrsta na osnovi štetja dni za uporabo"}, "ABS": {"a": "(števil<PERSON>)", "d": "Vrne absolutno vrednost <PERSON>, število brez predz<PERSON>.", "ad": "je realno število, ki mu želite poiskati absolutno vrednost."}, "ACOS": {"a": "(števil<PERSON>)", "d": "Vrne arkus kosinus <PERSON>, v radianih, iz obsega od 0 do Pi. Arkus kosinus je kot, ka<PERSON><PERSON>a kosinus je dano <PERSON>.", "ad": "je kos<PERSON> kota, ki mora biti med -1 in 1."}, "ACOSH": {"a": "(števil<PERSON>)", "d": "Vrne inverzni hiperbolični kosinus <PERSON>.", "ad": "je katero koli realno število, enako ali večje kot 1."}, "ACOT": {"a": "(number)", "d": "Vrne arccot števila, v radianih v obsegu od 0 do Pi.", "ad": " je kotangens želenega kota"}, "ACOTH": {"a": "(number)", "d": " Vrne inverzni hiperbolični kotangens števila", "ad": "je hiperbolični kotangens želenega kota"}, "AGGREGATE": {"a": "(št_funkcije; možnosti; sklic1; ...)", "d": "Vrne agregat s seznama ali iz zbirke podatkov.", "ad": "je število med 1 in 19, ki določa funkcijo povzemanja za agregat.!je število med 0 in 7,ki dolo<PERSON>a vrednosti, ki jih agregat prezre!je matrika za obseg numeri<PERSON><PERSON><PERSON>kov, na osnovi katerih se izračuna agregat.!označuje položaj v matriki; je k-ti največji, k-ti najmanjši, k-ti percentil ali k-ti kvartil.!je število med 1 in 19, ki določa funkcijo povzemanja za agregat.!je število med 0 in 7, ki določa vrednoti, ki jih agregat prezre!od 1 do 253 obsegov sklicev, za katere želite agregat."}, "ARABIC": {"a": "(be<PERSON><PERSON>)", "d": "Pretvori rimsko številko v arabsko", "ad": "je rimska številka, ki jo želite pretvoriti"}, "ASC": {"a": "(be<PERSON><PERSON>)", "d": "Za jezike z dvobajtnim naborom znakov (DBCS) spremeni funkcija znake polne širine (dvobajtne) v znake polovične (enobajtne) širine", "ad": "Besedilo ali sklic na celico, ki vseb<PERSON>je besedilo, v katerem hočete zamenjati znake"}, "ASIN": {"a": "(števil<PERSON>)", "d": "Vrne arkus sinus števila v radianih, iz obsega od -Pi/2 do Pi/2.", "ad": "je sinus iskanega kota, ki mora biti med -1 in 1."}, "ASINH": {"a": "(števil<PERSON>)", "d": "Vrne inverzni hiperbolični sinus števila.", "ad": "je katero koli realno število, ki je enako ali večje kot 1."}, "ATAN": {"a": "(števil<PERSON>)", "d": "Vrne arkus tangens števila v radianih, iz obsega od -Pi/2 do Pi/2.", "ad": "je tangens želenega kota."}, "ATAN2": {"a": "(št_x; št_y)", "d": "Vrne arkus tangens podanih x- in y-koordinat v radianih, iz obsega od -Pi do Pi, brez -Pi.", "ad": "je x-koordinata točke.!je y-koordinata točke."}, "ATANH": {"a": "(števil<PERSON>)", "d": "Vrne inverzni hiperbolični tangens števila.", "ad": "je katero koli realno število med -1 in 1 brez -1 in 1."}, "BASE": {"a": "(<PERSON><PERSON><PERSON><PERSON>; koren; [min_do<PERSON><PERSON><PERSON>])", "d": "Pretvori število v besedilo z danim korenom (osnova)", "ad": "je <PERSON><PERSON><PERSON><PERSON>, ki ga želite pretvoriti!je osnovni koren, v katerega želite število pretvoriti!je najmanjša dolžina vrnjenega niza. Če niso dodane izpuščene vodilne ničle"}, "CEILING": {"a": "(število; pomembnost)", "d": "Zaokroži število navzgor, na najbližji mnogokratnik značilnega števila.", "ad": "je v<PERSON><PERSON><PERSON>, ki jo želite zaokrožiti.!je mnogokratnik, s katerim želite zaokroževati."}, "CEILING.MATH": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [osnova]; [način])", "d": "Zaokroži število na najbližje celo število ali večkratnik osnove navzgor", "ad": "je v<PERSON><PERSON><PERSON>, ki jo želite zaokrožiti.!je mnogokratnik, s katerim želite zaokroževati."}, "CEILING.PRECISE": {"a": "(š<PERSON><PERSON><PERSON>; [pomembnost])", "d": "<PERSON><PERSON> število, ki je zaokroženo na najbližje celo število ali na najbližji mnogokratnik značilnega števila", "ad": "je v<PERSON><PERSON><PERSON>, ki jo želite zaokrožiti.!je mnogokratnik, s katerim želite zaokroževati."}, "COMBIN": {"a": "(šte<PERSON><PERSON>; izbra<PERSON>_število)", "d": "<PERSON>rne število kombinacij za dano število pred<PERSON>ov.", "ad": "je število predmetov.!je število predmetov v vsaki kombinaciji."}, "COMBINA": {"a": "(število; število_izbrano)", "d": "<PERSON><PERSON> število kombinacij za dano <PERSON>te<PERSON> elementov (s ponovitvami), ki jih je mogoče izbrati med skupnim številom elementov", "ad": "je skupno število elementov!je število elementov v vsaki kombinaciji"}, "COS": {"a": "(števil<PERSON>)", "d": "<PERSON><PERSON> kosinus kota.", "ad": "je kot v radianih, za katerega želite poiskati kosinus"}, "COSH": {"a": "(števil<PERSON>)", "d": "<PERSON>rne hiperbolični k<PERSON>.", "ad": "je katero koli realno število."}, "COT": {"a": "(number)", "d": "Vrne kotangens kota", "ad": "je kot v radianih, za katerega želite dobiti kotangens"}, "COTH": {"a": "(number)", "d": "Vrne hiperbolični kotangens števila", "ad": "je kot v radianih, za katerega želite dobiti kotangens"}, "CSC": {"a": "(number)", "d": "<PERSON>rne kosekans kota", "ad": "je kotv radianih, ki mu želite izračunati kosekans"}, "CSCH": {"a": "(number)", "d": "Vrne hiperbolični kosekans kota", "ad": "je kot v radianih, ki mu želite izračunati hiperbolični kosekans"}, "DECIMAL": {"a": "(število; koren)", "d": "Pretvori besedilo, ki predstavlja številko, v danem korenu v decimalno število", "ad": "je <PERSON><PERSON><PERSON><PERSON>, ki ga želite pretvoriti!je osnovni koren <PERSON>, ki ga želite pretvoriti"}, "DEGREES": {"a": "(kot)", "d": "Pretvori radiane v stopinje.", "ad": "je kot v radianih, ki ga želite pretvoriti."}, "ECMA.CEILING": {"a": "(število; pomembnost)", "d": "Zaokroži število navzgor, na najbližji mnogokratnik značilnega števila", "ad": "je v<PERSON><PERSON><PERSON>, ki jo želite zaokrožiti.!je mnogokratnik, s katerim želite zaokroževati."}, "EVEN": {"a": "(števil<PERSON>)", "d": "Zaokroži pozitivno število navzgor in negativno število navzdol na najbližje sodo celo število.", "ad": "je vred<PERSON>t, ki se zaokroži."}, "EXP": {"a": "(števil<PERSON>)", "d": " Vrne e na potenco navedenega števila.", "ad": "je eksponent z osnovo e. Konstanta e je enaka 2,71828182845904 in je osnova naravnega logaritma."}, "FACT": {"a": "(števil<PERSON>)", "d": "<PERSON><PERSON> fak<PERSON>, ki je enaka 1*2*3*...*<PERSON>te<PERSON><PERSON>.", "ad": "je ne<PERSON><PERSON><PERSON> š<PERSON>vilo, ki mu želite določiti fakulteto."}, "FACTDOUBLE": {"a": "(števil<PERSON>)", "d": "Vrne dvojno fakulteto števila", "ad": "je v<PERSON><PERSON><PERSON>, za katero <PERSON> dvojno fakulteto"}, "FLOOR": {"a": "(število; pomembnost)", "d": "Zaokroži število navzdol do najbližjega mnogokratnika značilnega števila.", "ad": "je številska vrednost, ki jo želite zaokrožiti.!je mnogokratnik, na katerega želite zaokroževati. Število in značilno število morata biti ali obe pozitivni ali obe negativni."}, "FLOOR.PRECISE": {"a": "(š<PERSON><PERSON><PERSON>; [pomembnost])", "d": "<PERSON><PERSON>vilo, ki je zaokroženo navzdol na najbližje celo število ali na najbližji večkratnik osnove", "ad": "je v<PERSON><PERSON><PERSON>, ki jo želite zaokrožiti.!je mnogokratnik, s katerim želite zaokroževati."}, "FLOOR.MATH": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [osnova]; [način])", "d": "Zaokroži število na najbližje celo število ali večkratnik osnove navzdol", "ad": "je v<PERSON><PERSON><PERSON>, ki jo želite zaokrožiti!je večkratnik, na katerega želite zaokrožiti!če je dana in je neničelna, bo ta funkcija zaokrožila na število, ki se približa številu nič"}, "GCD": {"a": "(število1; [število2]; ...)", "d": "Vrne največji skupni delitelj", "ad": "je 1 do 255 vrednosti"}, "INT": {"a": "(števil<PERSON>)", "d": "Število zaokroži navzdol do najbližjega celega števila.", "ad": "je realno število, ki ga želite zaokrožiti navzdol v celo število."}, "ISO.CEILING": {"a": "(š<PERSON><PERSON><PERSON>; [pomembnost])", "d": "<PERSON><PERSON> š<PERSON>vilo, ki je zaokroženo na najbližje celo število ali na najbližji mnogokratnik značilnega števila. Ne glede na znak števila, je število zaokroženo navzgor. Če je vrednost števila ali značilnega števila nič, je vrnjena vrednost nič.", "ad": "je v<PERSON><PERSON><PERSON>, ki jo želite zaokrožiti.!je mnogokratnik, s katerim želite zaokroževati."}, "LCM": {"a": "(število1; [število2]; ...)", "d": "Vrne najmanjši skupni mnogokratnik", "ad": "je 1 do 255 v<PERSON><PERSON><PERSON>, za katere želite najmanjši skupni mnogokratnik"}, "LN": {"a": "(števil<PERSON>)", "d": "Vrne naravni logaritem števila.", "ad": "je pozitivno realno število, ki mu želite določiti naravni logaritem."}, "LOG": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [osnova])", "d": "Vrne logaritem števila z osnovo, ki jo določ<PERSON>.", "ad": "je pozitivno realno število, ki mu želite določiti logaritem.!je osnova logaritma; 10, če je iz<PERSON><PERSON>."}, "LOG10": {"a": "(števil<PERSON>)", "d": "Vrne desetiški logaritem števila.", "ad": "je pozitivno realno število, ki mu želite določiti desetiški logaritem."}, "MDETERM": {"a": "(matrika)", "d": "<PERSON>rne determinanto matrike.", "ad": "je številska matrika z enakim številom vrstic in stolpcev, obseg celic ali matrična konstanta."}, "MINVERSE": {"a": "(matrika)", "d": "Vrne inverzno matriko matrike, shranjene v polju.", "ad": "je številska matrika z enakim številom vrstic in stolpcev, obseg celic ali matrična konstanta."}, "MMULT": {"a": "(matrika1; matrika2)", "d": "Vrne produkt dveh matrik, ki je matrika z enakim številom vrstic kot »matrika1« in z enakim številom stolpcev kot »matrika2«", "ad": "je prva matrika števil za množenje in mora imeti enako število stolpcev kot ima »matrika2« vrstic."}, "MOD": {"a": "(<PERSON><PERSON><PERSON><PERSON>; delitelj)", "d": "<PERSON><PERSON> o<PERSON>.", "ad": "je <PERSON><PERSON><PERSON><PERSON>, ki mu želite poiskati ostanek po deljenju.!je <PERSON><PERSON><PERSON><PERSON>, s katerim želite deliti število."}, "MROUND": {"a": "(število; večkratnik)", "d": "Vrne število, zaokroženo na želeni večkratnik", "ad": "je v<PERSON><PERSON><PERSON>, ki se zaokroži!je večkratnik, na katerega želite zaokrožiti število"}, "MULTINOMIAL": {"a": "(število1; [število2]; ...)", "d": "Vrne mnogočlenski niz števil", "ad": "je 1 do 255 v<PERSON><PERSON><PERSON>, za katere želite mnogočlenski niz števil"}, "MUNIT": {"a": "(dimenzija)", "d": "Vrne matriko enote za določeno dimenzijo", "ad": "je c<PERSON>, ki določa dimenzijo matrike enote, ki jo želite dobiti"}, "ODD": {"a": "(števil<PERSON>)", "d": "Zaokroži pozitivno število navzgor in negativno število navzdol do najbližjega lihega celega števila.", "ad": "je vred<PERSON>t, ki se zaokroži."}, "PI": {"a": "()", "d": "Vrne vrednost Pi na 15 decimalnih mest točno (3,14159265358979).", "ad": ""}, "POWER": {"a": "(število; potenca)", "d": "<PERSON><PERSON> poten<PERSON>.", "ad": "je osnova potence in je lahko katero koli realno število.!je eksponent potence."}, "PRODUCT": {"a": "(število1; [število2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, ki so bila podana kot argumenti.", "ad": "od 1 do 255 <PERSON>, logičnih vrednosti ali besedilnih predstavitev števil, ki jih želite zmnožiti"}, "QUOTIENT": {"a": "(deljenec; delitelj)", "d": "Vrne del celega števila deljenja", "ad": "je deljenec!je delitelj"}, "RADIANS": {"a": "(kot)", "d": "Pretvori stopinje v radiane.", "ad": "je kot v stopinjah, ki ga želite pretvoriti."}, "RAND": {"a": "()", "d": "Vrne naključno število, ki je večje ali enako 0 in manjše od 1, enakomerno porazdeljeno (spremembe vnovičnega izračuna).", "ad": ""}, "RANDARRAY": {"a": "([vrstice]; [stolpce]; [min]; [max]; [c<PERSON>_<PERSON><PERSON>])", "d": "Vrne niz naklju<PERSON>h <PERSON>vil", "ad": " število vrstic v vrnjeni matriki! število stolpcev v vrnjeni matriki! najmanjše število, ki bi želeli vrnil! največje število, ki bi želeli vrnil! vrne celo število ali decimalne vrednosti. RES za celo število, drži za decimalno število"}, "RANDBETWEEN": {"a": "(najmnajše; največje)", "d": "<PERSON><PERSON> naklju<PERSON> število med navedenimi števili", "ad": "je najman<PERSON><PERSON><PERSON> celo <PERSON>, ki ga vrne funkcija RANDBETWEEN!je največje celo število, ki ga vrne funkcija RANDBETWEEN"}, "ROMAN": {"a": "(š<PERSON><PERSON><PERSON>; [oblika])", "d": "Pretvori arabsko številko v rimsko v obliki besedila.", "ad": "je arabska številka, ki jo želite pretvoriti.!je številka, ki določa vrsto rimske številke."}, "ROUND": {"a": "(število; št_števk)", "d": "Zaokroži število prek določenega števila števk.", "ad": "je <PERSON><PERSON><PERSON><PERSON>, ki ga želite zaokrožiti.!dolo<PERSON><PERSON>, na koliko decimalnih mest želite zaokrožiti število. Negativno število pomeni zaokrožanje na levo od decimalne vejice, nič pa pomeni zaokrožanje na najbližje celo število."}, "ROUNDDOWN": {"a": "(število; št_števk)", "d": "Zaokroži število navzdol proti nič.", "ad": "je katero koli realno število, ki ga želite zaokrožiti navzdol.!določa, na koliko decimalnih mest želite zaokrožiti število. Negativno število pomeni zaokrožanje na levo od decimalne vejice, nič ali izpuščeno pa pomeni zaokrožanje na najbližje celo število."}, "ROUNDUP": {"a": "(število; št_števk)", "d": "Zaokroži število navzgor, stran od nič.", "ad": "je katero koli realno število, ki ga želite zaokrožiti.!določa, na koliko decimalnih mest želite zaokrožiti število. Negativno število pomeni zaokrožanje na levo od decimalne vejice, nič ali izpuščeno pa pomeni zaokrožanje na najbližje celo število."}, "SEC": {"a": "(number)", "d": "<PERSON><PERSON> sekans kota", "ad": "je kot v radianih, ki mu želite izračunati sekans"}, "SECH": {"a": "(number)", "d": "Vrne hiperbolični sekans kota", "ad": "je kot v radianih, ki mu želite izračunati hiperbolični sekans"}, "SERIESSUM": {"a": "(x; n; m; koe<PERSON>i)", "d": "Vrne vsoto potenciranih nizov glede na formulo", "ad": "je vnosna vrednost za potencirane nize!je začetna potenca, na katero želite potencirati x!je korak, po katerem povečate n za vsak člen v nizu!je niz koe<PERSON>, s katerimi je vsaka naslednja potenca x-a pomnožena"}, "SIGN": {"a": "(števil<PERSON>)", "d": "Vrne predznak <PERSON>: 1, če je število pozitivno, ni<PERSON>, če je število nič, ali -1, če je število negativno.", "ad": "je katero koli realno število."}, "SIN": {"a": "(pš<PERSON><PERSON><PERSON>)", "d": "Vrne sinus kota.", "ad": "je kot v radianih, za katerega želite poiskati sinus. Stopinje*PI()/180 = radiani."}, "SINH": {"a": "(števil<PERSON>)", "d": "<PERSON>rne hiperbolični sinus števila.", "ad": "je katero koli realno število."}, "SQRT": {"a": "(števil<PERSON>)", "d": "Vrne pozitivni kvadratni koren števila.", "ad": "je <PERSON><PERSON><PERSON><PERSON>, ki mu želite določiti kvadratni koren."}, "SQRTPI": {"a": "(števil<PERSON>)", "d": "Vrne k<PERSON>dratni koren za (število * pi)", "ad": "je <PERSON><PERSON><PERSON><PERSON>, s katerim je p pomno<PERSON>en"}, "SUBTOTAL": {"a": "(št_funkcije; sklic1; ...)", "d": "<PERSON>rne delno vsoto s seznama ali iz zbirke podatkov.", "ad": "je število med 1 do 11, ki na<PERSON><PERSON>, katera funkcija se uporabi za izračun delnih vsot na seznamu.!od 1 do 254 obsegov al<PERSON>, ka<PERSON><PERSON> določiti delne vsote"}, "SUM": {"a": "(število1; [število2]; ...)", "d": "Sešteje vsa števila v obsegu celic.", "ad": "od 1 do 255 <PERSON><PERSON><PERSON>, ki jih želite sešteti. Logične vrednosti in besedilo v celicah se prezrejo, tudi če jih vnesete kot argumente"}, "SUMIF": {"a": "(obseg; pogoji; [obseg_se<PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>, ki jih določa podan pogoj ali kriterij.", "ad": "je obseg celic, ki ga želite ovrednotiti.!je pogoj ali kriterij v obliki števila, i<PERSON><PERSON><PERSON> ali besedila, ki do<PERSON>, katere celice naj se seštevajo.!so dejan<PERSON> celice, ki se bodo seštele. Če je <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bodo uporabljene celice v obsegu."}, "SUMIFS": {"a": "(obseg_se<PERSON><PERSON><PERSON><PERSON>; obseg_pogojev; pogoji; ...)", "d": "<PERSON><PERSON> celi<PERSON>, ki jih navaja dani niz pogojev ali kriterijev", "ad": "so dejanske celice za seštevanje.!je obseg celic, ki jih želite ovrednotiti za določen pogoj!je pogoj ali kriterij v obliki števila, i<PERSON><PERSON><PERSON> ali be<PERSON>, ki do<PERSON>, katere celice bodo dodane"}, "SUMPRODUCT": {"a": "(matrika1; [matrika2]; [matrika3]; ...)", "d": "Vrne vsoto produktov ustreznih obsegov ali matrik.", "ad": "od 2 do 255 matrik, katerih elemente želite množiti in nato sešteti. Vse matrike morajo imeti enake raz<PERSON>žnosti"}, "SUMSQ": {"a": "(število1; [število2]; ...)", "d": "<PERSON><PERSON> vs<PERSON> k<PERSON> argumentov. <PERSON>rg<PERSON><PERSON> so <PERSON><PERSON><PERSON>, mat<PERSON><PERSON>, imena <PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>.", "ad": "od 1 do 255 <PERSON><PERSON><PERSON>, mat<PERSON>, imen <PERSON><PERSON>, za katere želite izračunati vsoto kvadratov"}, "SUMX2MY2": {"a": "(matrika_x; matrika_y)", "d": "Izračuna vsoto razlik kvadratov pripadajočih števil v dveh obsegih ali matrikah.", "ad": "je prvi obseg ali matrika števil in je lahko število ali ime, matrika ali sklic, ki vsebu<PERSON>.!je drugi obseg ali matrika števil in je lahko število ali ime, matrika ali sklic, ki vseb<PERSON><PERSON>."}, "SUMX2PY2": {"a": "(matrika_x; matrika_y)", "d": "Izračuna skupno vsoto vseh vsot kvadratov števil v dveh pripadajočih obsegih ali matrikah.", "ad": "je prvi obseg ali matrika števil in je lahko število ali ime, matrika ali sklic, ki vsebu<PERSON>.!je drugi obseg ali matrika števil in je lahko število ali ime, matrika ali sklic, ki vseb<PERSON><PERSON>."}, "SUMXMY2": {"a": "(matrika_x; matrika_y)", "d": "Izračuna vsoto kvadratov razlik v dveh pripadajočih obsegih ali matrikah.", "ad": "je prvi obseg ali matrika vrednosti in je lahko <PERSON>tevilo ali ime, matrika ali sklic, ki vsebu<PERSON>.!je drugi obseg ali matrika vrednosti in je lahko število ali ime, matrika ali sklic, ki vseb<PERSON><PERSON>."}, "TAN": {"a": "(števil<PERSON>)", "d": "Vrne tangens kota.", "ad": "je kot v radianih, za katerega želite poiskati tangens. Stopinje*PI()/180 = radiani."}, "TANH": {"a": "(števil<PERSON>)", "d": "Vrne hiperbolični tangens števila.", "ad": "je katero koli realno število."}, "TRUNC": {"a": "(število; [št_števk])", "d": "Prireže število na celo število tako, da odstrani decimalni del števila ali ulomek.", "ad": "je <PERSON><PERSON><PERSON><PERSON>, ki ga želite prirezati.!je število, ki določa natančnost prirezovanja; 0, če iz<PERSON>š<PERSON><PERSON>."}, "ADDRESS": {"a": "(št_vrstice; št_stolpca; [abs_št]; [a1]; [besedilo_lista])", "d": "Ustvari sklic na celico kot besedilo. Podati morate številko vrstice in številko stolpca.", "ad": "je številka vrstice pri sklicevanju na celico: Št_vrstice = 1 za vrstico 1.!je številka stolpca pri sklicevanju na celico: na primer št_stolpca = 4 za stolpec D.!določa vrsto sklica: absolutni = 1; absolutna vrstica/relativni stolpec = 2; relativna vrstica/absolutni stolpec = 3; relativni = 4.!je logična vrednost za določanje sloga sklicevanja: A1 slog = 1 ali TRUE; R1C1 slog = 0 ali FALSE.!je besedilo za določanje imena delovnega lista, ki se uporabi kot zunanji sklic."}, "CHOOSE": {"a": "(št_indeksa; vrednost1; [vrednost2]; ...)", "d": "<PERSON>zbere vrednost ali <PERSON>, ki naj se izvede, s seznama vrednosti, ki temelji na indeksni številki.", "ad": "nava<PERSON>, kateri argument z vrednostjo je izbran. »Št_indeksa« mora biti število med 1 in 254, formula ali sklic na število med 1 in 254.!so števila med 1 in 254, sklici na celice, do<PERSON><PERSON><PERSON> imena, formule, funkcije ali besedilni argumenti, med katerimi izbere CHOOSE"}, "COLUMN": {"a": "([sklic])", "d": "Vrne številko stolpca danega sklica.", "ad": "je celica ali obseg celic, ki jim do<PERSON> številko stolpca. <PERSON>e tega ni, je uporabl<PERSON>na celica, ki vseb<PERSON>je funkcijo COLUMN."}, "COLUMNS": {"a": "(matrika)", "d": "Vrne število stolpcev v matriki ali v sklicu.", "ad": "je matrika ali matrična formula oziroma sklic na obseg celic, za katerega določate število stol<PERSON>cev."}, "FORMULATEXT": {"a": "(sklic)", "d": "<PERSON><PERSON> formulo kot niz", "ad": "je sklic na formulo"}, "HLOOKUP": {"a": "(iskana_vrednost; matrika_tabele; št_indeksa_vrstice; [obseg_iskanja])", "d": "Poišče vrednost v zgornji vrstici tabele ali matrike vrednosti in vrne vrednost iz istega stolpca in vrstice, ki jo vi določite.", "ad": "je vred<PERSON><PERSON>, ki naj se poišče v prvi vrstici tabele, in je lahko vrednost, sklic ali besedilni niz.!je tabela, v kateri se iščejo podatki, in vseb<PERSON>je besedilo, števila ali logične vrednosti. Table_array je lahko sklic na obseg ali na ime obsega.!je številka vrstice v table_array, iz katere naj se vrne ujemajoča vrednost. Prva vrstica vrednosti v tabeli je vrstica 1.!je logična vrednost: če želite, da bo ujemanje približno (urejeno v naraščajočem vrstnem redu), jo nastavite na TRUE ali pustite prazno; če želite natančno ujemanje, pa naj bo FALSE."}, "HYPERLINK": {"a": "(mesto_povezave; [prija<PERSON><PERSON>_ime])", "d": "Ustvari bližnjico ali skok, ki odpre dokument, shranjen na vašem disku, v omrežnem strežniku ali v internetu.", "ad": "je be<PERSON>ilo, ki oz<PERSON><PERSON><PERSON><PERSON> pot in datotečno ime dokumenta, ki ga želite odpreti, mesto diska, naslov UNC ali pot spletnega naslova.!je besedilo ali številka, ki je prikazana v celici. Če jo iz<PERSON>tite, bo v celici prikazano besedilo Link_location."}, "INDEX": {"a": "(matrika; št_vrstice; [št_stolpca]!sklic; št_vrstice; [št_stolpca]; [št_podro<PERSON>ja])", "d": "Vrne vrednost ali sklic na celico v preseku določene vrstice in stolpca v navedenem obsegu.", "ad": "je obseg celic ali matrična konstanta, ki se je vnesla kot matrika.!izbere vrstico v matriki ali v sklicu, iz katerega bo vrnjena vrednost. Če to izpustite, morate navesti argument »Št_stolpca«.!izbere stolpec v matriki ali v sklicu, iz katerega bo vrnjena vrednost. Če to izpustite, morate navesti argument »Št_vrstice«.!je sklic na enega ali več obsegov celic.!izbere vrstico v matriki ali v sklicu, iz katerega bo vrnjena vrednost. Če to izpustite, morate navesti argument »Št_stolpca«.!izbere stolpec v matriki ali v sklicu, iz katerega bo vrnjena vrednost. Če to izpustite, morate navesti argument »Št_vrstice«.!izbere obseg v sk<PERSON><PERSON>, iz katerega bo vrnjena vrednost. Najprej je izbrano ali vneseno področje 1, nato področje 2 itd."}, "INDIRECT": {"a": "(besedilo_sklica; [a1])", "d": "<PERSON><PERSON> s<PERSON>, ki ga določa besedilni niz.", "ad": "je sklic na celico, ki vsebuje A1- ali R1C1- slog sklicevan<PERSON>, ime, ki je dolo<PERSON><PERSON> kot sklic, ali sklic na celico, kot na besedilni niz.!je logična vrednost, ki določa vrsto sklicevanja v argumentu »Besedilo_sklica«: slog R1C1 = FALSE; slog A1 = TRUE ali ni določeno."}, "LOOKUP": {"a": "(iskana_vrednost; vektor_iskanja; [vektor_rezultata]!iskana_vrednost; matrika)", "d": "Poišče vrednost bodisi iz obsega, ki vsebuje le eno vrstico ali le en stolpec, bodisi iz matrike.Na voljo zaradi združljivosti s prejšnjimi različicami.", "ad": "je vrednost, ki jo LOOKUP išče v parametru »Vektor_iskanja« in je lahko številka, besedilo, logi<PERSON>na vrednost, ime ali sklic na vrednost.!je obseg, ki vsebuje le eno vrstico ali le en stolpec besedila, števil ali logičnih vrednosti, razporejenih v naraščajočem vrstnem redu.!je obseg, ki vsebuje le eno vrstico ali le en stolpec enake velikosti kot »Vektor_iskanja«.!je vrednost, ki jo LOOKUP išče v matriki, in je lahko število, besedilo, logična vrednost, ime ali sklic na vrednost.!je obseg celic, ki vsebujejo besedilo, števila ali logične vrednosti, ki jih želite primerjati z »Vektor_iskanja«."}, "MATCH": {"a": "(iskana_vrednost; matrika_iskanja; [vrsta_ujemanja])", "d": "Vrne relativni položaj elementa v matriki, ki se ujema z navedeno vrednostjo v navedenem vrstnem redu.", "ad": "je vred<PERSON><PERSON>, ki jo uporabite za iskanje vrednosti v matriki, <PERSON><PERSON><PERSON><PERSON>, logična vrednost ali sklic na eno od tega.!je neprekinjen obseg celic, v katerem so možne iskane vrednosti, matrika vrednosti ali sklic na matriko.!je število 1, 0, ali -1, ki <PERSON><PERSON>, katera vrednost naj se vrne."}, "OFFSET": {"a": "(sklic; vrstice; stolpci; [viš<PERSON>]; [širina])", "d": "<PERSON>rne sklic na obseg, ki je dano <PERSON> vrst<PERSON> in stolpcev iz danega sklica.", "ad": "je sklic, na katerem temelji odmik, in je sklic na celico ali na obseg priležnih celic.!je š<PERSON><PERSON><PERSON> vrstic, navzgor ali navzdol, na katere naj se sklicuje zgornja leva celica rezultata.!je šte<PERSON><PERSON> sto<PERSON>v, levo ali desno, na katere naj se sklicuje zgornja leva celica rezultata.!je višina v števil<PERSON> vrstic, ki jo želite za rezultat. Če to opustite, bo višina enaka, kot je določeno v argumentu Sklic«.!je širina v številu stolpcev, ki jo želite za rezultat. Če to opustite, bo širina enaka, kot je določeno v argumentu Sklic«."}, "ROW": {"a": "([sklic])", "d": "Vrne številko vrstice za sklic.", "ad": "je celica ali obseg celic, za katere želite število vrstice; če je i<PERSON><PERSON><PERSON><PERSON><PERSON>, vrne celico, ki vsebuje funkcijo ROW."}, "ROWS": {"a": "(matrika)", "d": "Vrne število vrstic v sklicu ali v matriki.", "ad": "je matrika, matri<PERSON>na formula ali sklic na obseg celic, za katere želimo poznati število vrstic."}, "TRANSPOSE": {"a": "(matrika)", "d": "Pretvori navpični obseg celic v vodoravni obseg in obratno.", "ad": "je obseg celic na delovnem listu ali matrika vrednosti, ki jo želite transponirati."}, "UNIQUE": {"a": "(polje; [po_kol]; [natančno_enkrat])", "d": "Vrne enolične vrednosti obsega ali polja.", "ad": "obseg ali polje, iz katerega bodo vrnjene enolične vrstice ali stolpci!je logična vrednost: primerjava vrstic med seboj in vrnitev enoličnih vrstic = FALSE ali opuščeno; primerjava stolpcev med seboj in vrnitev enoličnih stolpcev = TRUE!je logična vrednost: vrne vrstice ali stolpce, ki se v polju pojavijo natančno enkrat = TRUE; vrne vse različne vrstice ali stolpce iz polja = FALSE ali opuščeno"}, "VLOOKUP": {"a": "(iskana_vrednost; matrika_tabele; št_indeksa_stolpca; [obseg_iskana])", "d": "Poišče vrednost v skrajnem levem stolpcu tabele in vrne vrednost v isti vrstici iz stolpca, ki ga navedete. Privzeto mora biti tabela urejena v naraščajočem vrstnem redu.", "ad": "je vred<PERSON><PERSON>, ki naj se poišče v prvem stolpcu matrike in je lahko vrednost, sklic ali besedilni niz.!je tabela z besedilom, šte<PERSON> ali logičnimi vrednostmi, iz katere so dobljeni podatki. Table_array je lahko sklic na obseg ali ime obsega.!je številka stolpca v table_array, s katerega naj se vrne ujemajoča vrednost. Prvi stolpec vrednosti v tabeli je stolpec 1.!je logična vrednost: če želite najti najboljše ujemanje v prvem stolpcu (urejeno v naraščajočem vrstnem redu) = TRUE ali izpuščeno; če želite najti natančno ujemanje = FALSE."}, "XLOOKUP": {"a": "(iskana_vrednost; iskani_niz; vrni_niz; [če_ni_najdeno]; [način_ujeman<PERSON>]; [način_iskanja])", "d": "Poišče ujemanje v obsegu ali polju in vrne ustrezen element iz drugega obsega ali polja. Privzeto je uporabljeno natančno ujemanje", "ad": "je vrednost za iskanje!je polje ali obseg za iskanje!je vrnjeno polje ali obseg!vrn<PERSON><PERSON>, če ni najdeno!navedite, kako želite iskati ujemanje vrednosti iskana_vrednost z vrednostmi v polju iskani_niz!navedite način iskanja, ki ga želite uporabiti. Privzeto bo uporabljeno iskanje od prvega do zadnjega"}, "CELL": {"a": "(vrsta_informacij; [sklic])", "d": "Vrne informacije o oblikovanju, mestu ali vs<PERSON><PERSON> celice", "ad": "besedilna vrednost, ki <PERSON>, kakšno vrsto informacij o celici želite dobiti!celica, o kateri želite dobiti informacije"}, "ERROR.TYPE": {"a": "(vrednost_napake)", "d": "<PERSON><PERSON>, ki ustreza vrednosti napake.", "ad": "je vrednost napake, za katero želite poiskati ID, in je lahko dejanska vrednost napake ali sklic na celico, v kateri je vrednost napake"}, "ISBLANK": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali gre za sklic na prazno celico, in vrne TRUE ali FALSE.", "ad": "je celica ali ime, ki se sklicuje na celico, ki jo želite preskusiti."}, "ISERR": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost napaka, ki ni #N/V, in vrne TRUE ali FALSE", "ad": "je vred<PERSON><PERSON>, ki jo želite preskusiti. Vrednost se lahko sklicuje na celico, formulo ali ime, s katerim se sklicujete na celico, formulo ali vrednost"}, "ISERROR": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost napaka, in vrne TRUE ali FALSE", "ad": "je vred<PERSON><PERSON>, ki jo želite preskusiti. Vrednost se lahko sklicuje na celico, formulo ali ime, s katerim se sklicujete na celico, formulo ali vrednost"}, "ISEVEN": {"a": "(števil<PERSON>)", "d": "<PERSON><PERSON> TRUE, če je število sodo", "ad": "je vrednost za preskus"}, "ISFORMULA": {"a": "(sklic)", "d": "<PERSON><PERSON><PERSON>, ali je sklic povezan s celico, ki vs<PERSON><PERSON><PERSON> formulo, in vrne TRUE ali FALSE", "ad": "je sklic na celico, ki ga <PERSON><PERSON>te preveriti. Sklic je lahko sklic na celico, formulo ali ime, ki se sklicuje na celico"}, "ISLOGICAL": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost logična vrednost (TRUE ali FALSE), in vrne TRUE ali FALSE.", "ad": "je vred<PERSON>t, ki jo želite preskusiti. Vrednost se lahko sklicuje na celico, formulo ali ime, s katerim se sklicujete na celico, formulo ali vrednost."}, "ISNA": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost  #N/V, in vrne TRUE ali FALSE.", "ad": "je vred<PERSON>t, ki jo želite preskusiti. Vrednost se lahko sklicuje na celico, formulo ali ime, s katerim se sklicujete na celico, formulo ali vrednost."}, "ISNONTEXT": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali vrednost ni besedilo (prazne celice niso besedilo), in vrne TRUE ali FALSE.", "ad": "je vred<PERSON><PERSON>, ki jo želite preskusiti: celica, formula ali ime, s katerim se sklicujete na celico, formulo ali vrednost."}, "ISNUMBER": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost število, in vrne TRUE ali FALSE.", "ad": "je vred<PERSON>t, ki jo želite preskusiti. Vrednost se lahko sklicuje na celico, formulo ali ime, s katerim se sklicujete na celico, formulo ali vrednost."}, "ISODD": {"a": "(števil<PERSON>)", "d": "<PERSON>rne TRUE, če je število liho", "ad": "je vrednost za preskus"}, "ISREF": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost sklic, in vrne TRUE ali FALSE.", "ad": "je vred<PERSON>t, ki jo želite preskusiti. Vrednost se lahko sklicuje na celico, formulo ali ime, s katerim se sklicujete na celico, formulo ali vrednost."}, "ISTEXT": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost besedilo, in vrne TRUE ali FALSE.", "ad": "je vred<PERSON>t, ki jo želite preskusiti. Vrednost se lahko sklicuje na celico, formulo ali ime, s katerim se sklicujete na celico, formulo ali vrednost."}, "N": {"a": "(vrednost)", "d": "Pretvori neštevilsko vrednost v število, datume v zaporedna števila, TRUE v 1, kar koli drugega pa v 0 (nič).", "ad": "je vred<PERSON>t, ki jo želite pretvoriti."}, "NA": {"a": "()", "d": "Vrne vrednost napake #N/V (vrednost ni na voljo).", "ad": ""}, "SHEET": {"a": "([vrednost])", "d": "Vrne število listov sklicevanega lista", "ad": "je ime lista ali sklic lista. Če je <PERSON><PERSON>, je vrnjena števil<PERSON> lista, ki vs<PERSON><PERSON><PERSON>jo"}, "SHEETS": {"a": "([sklic])", "d": "Vrne število listov v sklicu", "ad": "je <PERSON><PERSON><PERSON>, za ka<PERSON>ega ž<PERSON> vedeti, koli<PERSON> listov vsebu<PERSON>. Če je <PERSON><PERSON><PERSON><PERSON>, je vrnjeno število listov v delovnem zvezku, ki vsebujejo funkcijo"}, "TYPE": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ki predstavlja podatkovni tip vrednosti: številka = 1; besedilo = 2; logična vrednost = 4; vrednost napake = 16; polje = 64; sestavljeni podatki = 128", "ad": "je lahko katera koli vrednost"}, "AND": {"a": "(logično1; [logično2]; ...)", "d": "<PERSON><PERSON><PERSON>, ali imajo vsi argumenti vrednost TRUE, in vrne vrednost TRUE, če imajo vsi argumenti vrednost TRUE.", "ad": "od 1 do 255 p<PERSON><PERSON><PERSON>, ki jih želite preskusiti, in so lahko ali TRUE ali FALSE. Lahko so logi<PERSON>ne vrednosti, matrike ali sk<PERSON>i"}, "FALSE": {"a": "()", "d": "Vrne logično vrednost FALSE", "ad": ""}, "IF": {"a": "(logični_test; [vrednost_če_je_true]; [vrednost_če_je_false])", "d": "<PERSON><PERSON><PERSON>, ali je pogoj <PERSON>, in vrne eno vrednost, če je TRUE, in drugo vrednost, če je FALSE.", "ad": "je katera koli vrednost ali izraz, ki se lahko vrednoti s TRUE ali FALSE.!je vrednost, ki se vrne, če je »Logični_test« enak TRUE. Če je izpuščena, se vrne TRUE. Gnezdite lahko do sedem funkcij IF.!je vrednost, ki se vrne, če je »Logični_test« enak FALSE. Če je izpuščena, se vrne FALSE."}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": "<PERSON><PERSON><PERSON>, ali je izpolnjen eden ali več pogojev in vrne vrednost glede na prvi pogoj TRUE", "ad": "je kateri koli i<PERSON>raz, ki ga je mogoče ovrednotiti z vrednostjo TRUE ali FALSE!je vrednost, če je vrednost testa Logical_test TRUE"}, "IFERROR": {"a": "(vrednost; vrednost_če_napaka)", "d": "Vrne value_if_error, če je izraz <PERSON>aka, in vrednost samega izraza", "ad": "je katera koli vrednost ali izraz ali sklic!je katera koli vrednost ali izraz ali sklic"}, "IFNA": {"a": "(vrednost; vrednost_če_nv)", "d": "<PERSON>rne vrednost, ki jo <PERSON>, če se izraz razreši v #N/V, v nasprotnem primeru vrne vrednost izraza", "ad": "je katera koli vrednost ali izraz ali sklic!je katera koli vrednost ali izraz ali sklic"}, "NOT": {"a": "(logično)", "d": "Spremeni FALSE v TRUE ali TRUE v FALSE.", "ad": "je vrednost ali izraz, ki se ovrednoti kot TRUE ali FALSE."}, "OR": {"a": "(logično1; [logično2]; ...)", "d": "<PERSON><PERSON><PERSON>, ali ima kateri argument vrednost TRUE; vrne vrednost TRUE ali FALSE. Vrne vrednost FALSE, če imajo vsi argumenti vrednost FALSE", "ad": "od 1 do 255 pogo<PERSON>v, ki jih želite preskusiti, in so lahko ali TRUE ali FALSE"}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "Ovrednoti izraz glede na seznam vrednosti in vrne rezultat, ki ustreza prvi ujemajoči se vrednosti. Če ni nobenega zadetka, je vrnjena opcijska privzeta vrednost", "ad": "je izraz, ki ga je treba ovrednotiti!je vrednost, ki jo je treba primerjati z izrazom!je rezultat, ki bo v<PERSON><PERSON><PERSON>, če se bo ustrezna vrednost ujemala z izrazom"}, "TRUE": {"a": "()", "d": "Vrne logično vrednost TRUE.", "ad": ""}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "<PERSON>rne logi<PERSON> »Exclusive Or« vseh argumentov", "ad": "so pogoji 1 do 254, ki jih želite preskusiti in so lahko TRUE ali FALSE ter so lahko logi<PERSON> vrednosti, matrike ali sk<PERSON>i"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON>, ki je pred loč<PERSON> znakov.", "ad": "<PERSON><PERSON><PERSON>, ki ga želite poiskati za ločilo. <PERSON><PERSON><PERSON> ali niz, ki ga želite uporabiti kot ločilo.!Želena pojavitev ločila. Privzeta vrednost je 1. Negativno število išče od konca.!V besedilu poišče ujemanje ločila.!Privzeto je izvedeno ujemanje z razlikovanjem med velikimi in malimi črkami.!<PERSON><PERSON><PERSON><PERSON><PERSON>, ali se ločilo ujema s koncem besedila. Privzeto se ne ujemata.!Vrn<PERSON>no, če ni bilo mogoče najti nobenega zadetka. Privzeto je vrnjen #N/A."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON>, ki je za ločilo<PERSON> znakov.", "ad": "<PERSON><PERSON><PERSON>, ki ga želite poiskati za ločilo.!<PERSON><PERSON><PERSON> ali niz, ki ga želite uporabiti kot ločilo.!Želena pojavitev ločila. Privzeta vrednost je 1. Negativno število išče od konca. V besedilu poišče ujemanje ločila.!Privzeto je izvedeno ujemanje z razlikovanjem med velikimi in malimi črkami.!<PERSON><PERSON><PERSON><PERSON><PERSON>, ali se ločilo ujema s koncem besedila. Privzeto se ne ujemata.!Vrn<PERSON>no, če ni bilo mogoče najti nobenega zadetka. Privzeto je vrnjen #N/A."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Razdeli besedilo v vrstice ali stolpce z ločili.", "ad": "<PERSON><PERSON><PERSON>, ki bo razdeljeno.!Znak ali niz za razdelitev stolpcev po.!Znak ali niz za razdelitev vrstic po.!<PERSON><PERSON><PERSON><PERSON><PERSON>, ali je treba prezreti prazne celice. Privzeto je FALSE.!V besedilu poišče ujemanje ločila. Privzeto je izvedeno ujemanje z razlikovanjem med velikimi in malimi črkami.!Vrednost, ki bo veljavna za odmik. Privzeto je #N/A."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": " Prelomi vektor vrstice ali stolpca za določenim številom vrednosti.", "ad": " Vek<PERSON> al<PERSON>, ki bo oviti.! Največje število vrednosti na vrstico.! Vrednost, s katero želite zapolniti. Privzeta vrednost je #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Navpično zloži matrike v eno polje.", "ad": "<PERSON><PERSON> s<PERSON> ali sk<PERSON>, ki bo na<PERSON>."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Vodoravno zloži matrike v eno polje.", "ad": "<PERSON><PERSON> s<PERSON> ali sk<PERSON>, ki bo na<PERSON>."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Vrne vrstice iz matrike ali sklica.", "ad": "<PERSON><PERSON>, ki vs<PERSON><PERSON><PERSON> vrstice, ki bodo vrnjene.!Številka vrstice, ki bo vrnjena."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "<PERSON>rne stolpce iz matrike ali sklica.", "ad": "<PERSON><PERSON>, ki vs<PERSON><PERSON><PERSON> s<PERSON>, ki bodo vrnjeni.!Številka stolpca, ki bo vrnjen."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Vrne matriko kot en stolpec.", "ad": "<PERSON><PERSON> al<PERSON>, ki bo vrnjen kot stolpec.!<PERSON><PERSON><PERSON><PERSON><PERSON>, ali je treba prezreti določene vrste vrednosti. Privzeto ni prezrta nobena vrednost.!Preglejte polje po stolpcu. Polje je privzeto pregledano z vrstico."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Vrne matriko kot eno vrstico.", "ad": "<PERSON><PERSON> al<PERSON>, ki bo vrnjen kot vrstica.!<PERSON><PERSON><PERSON><PERSON><PERSON>, ali je treba prezreti določene vrste vrednosti. Privzeto ni prezrta nobena vrednost.!Preglejte polje po stolpcu. Polje je privzeto pregledano z vrstico."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": " Prelomi vektor vrstice ali stolpca za določenim številom vrednosti.", "ad": " Vek<PERSON> al<PERSON>, ki bo oviti.! Največje število vrednosti na stolpec.! Vrednost, s katero želite zapolniti. Privzeta vrednost je #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Vrne vrstice ali stolpce z začetka ali konca matrike.", "ad": "<PERSON><PERSON>, iz katere boste vzeli vrstice ali stolpce.!<PERSON><PERSON><PERSON><PERSON> v<PERSON>, ki bodo vzete. Negativna vrednost se vzame s konca matrike.!<PERSON><PERSON><PERSON><PERSON> stolp<PERSON>v, ki jih je treba vzeti. Negativna vrednost se vzame s konca matrike."}, "DROP": {"a": "(array, rows, [columns])", "d": "Spusti vrstice ali stolpce z začetka ali konca matrike.", "ad": "<PERSON><PERSON>, iz katere bodo spuščene vrstice ali stolpci.!<PERSON><PERSON><PERSON><PERSON> vrst<PERSON>, ki bodo spuščene. Negativna vrednost se vzame s konca matrike.!<PERSON><PERSON><PERSON><PERSON> stol<PERSON>v, ki bodo spuščeni. Negativna vrednost se vzame s konca matrike."}, "SEQUENCE": {"a": "(rows, [columns], [start], [step])", "d": "Vrne zapored<PERSON>", "ad": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>, ki bodo vrnjene!š<PERSON><PERSON><PERSON> sto<PERSON>v, ki bodo vrnjeni!prvo število v zaporedju!vrednost, s katero bodo povečane vse nadaljnje vrednosti v zaporedju"}, "EXPAND": {"a": "(matrika, v<PERSON>ice, [stolpce], [pad_with])", "d": "Razširi polje na določene dimenzije.", "ad": "Pol<PERSON>, ki bo razširjeno.!Število vrstic v razširjenem polju. <PERSON>e manjka, vrstice ne bodo razširjene.!Število stolpcev v razširjenem polju. <PERSON>e manjka, stolpci ne bodo razširjeni.!<PERSON><PERSON><PERSON>t, s katero je treba zapolniti. Privzeta vrednost je #N/A."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "Vrne relativen položaj elementa v polju. Privzeto je zahtevano natančno ujemanje", "ad": "je vrednost za iskanje!je polje ali obseg za iskanje!navedite, kako želite iskati vrednost iskana_vrednost v vrednostih v polju iskano_polje!določite način iskanja, ki ga želite uporabiti. Privzeto bo uporabljeno iskanje od prvega do zadnjega"}, "FILTER": {"a": "(array, include, [if_empty])", "d": "Filt<PERSON>raj obseg ali polje", "ad": "obseg ali polje za filtriranje!polje logičnih vrednosti, kjer vrednost TRUE predstavlja vrstico ali polje, ki bo obdrž<PERSON>!vrnjeno, če ni obdržan noben element"}, "ARRAYTOTEXT": {"a": "(matrika, [oblika])", "d": "<PERSON><PERSON> predsta<PERSON>tev besedila matrike", "ad": " matrika, ki predstavlja besedilo! oblika besedila"}, "SORT": {"a": "(array, [sort_index], [sort_order], [by_col])", "d": "Razvrstite obseg ali polje", "ad": "obseg ali polje za razvrstitev!številka, ki označuje vrstico ali stolpec, po katerem bo izvedena razvrstitev!številka, ki označuje želeni vrsti red razvrstitve; 1 za naraščajoč vrstni red (privzeto), -1 za padajoč vrstni red!logična vrednost, ki označuje želeno smer razvrščanja: FALSE za razvrščanje po vrstici (privzeto), TRUE za razvrščanje po stolpcu"}, "SORTBY": {"a": "(array, by_array, [sort_order], ...)", "d": "Razvrsti obseh ali polje glede na vrednosti v pripadajočem obsegu ali polju", "ad": "obseg ali polje za razvrstitev!obseg ali polje, po katerem želite razvrstiti!število, ki označuje želeni vrsti red razvrstitve; 1 za naraščajoč vrstni red (privzeto), -1 za padajoč vrstni red"}, "GETPIVOTDATA": {"a": "(podatkovno_polje; vrtilna_tabela; [polje]; [element]; ...)", "d": "Ekstrahira podatke, ki so shranjeni v vrtilni tabeli.", "ad": "je ime podatkovnega polja, iz katerega se podatki ekstrahirajo!je sklic na celico ali na obseg celic v vrtilni tabeli, ki vsebuje podatke, katere želite pridobiti.!polje, na katerega se sklicujete!element polja, na katerega se sklicujete"}, "IMPORTRANGE": {"a": "(url_preglednice, niz_obsega)", "d": "Uvozi obseg celic iz podane preglednice."}}