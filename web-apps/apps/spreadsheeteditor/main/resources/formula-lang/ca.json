{"DATE": "DATA", "DATEDIF": "DATEDIF", "DATEVALUE": "VALDATA", "DAY": "DIA", "DAYS": "DIES", "DAYS360": "DIES360", "EDATE": "FECHA.MES", "EOMONTH": "FIN.MES", "HOUR": "HORA", "ISOWEEKNUM": "NUM.DE.SEMANA.ISO", "MINUTE": "MINUT", "MONTH": "MES", "NETWORKDAYS": "DIAS.LAB", "NETWORKDAYS.INTL": "DIES.LAB.INTL", "NOW": "ACTUAL", "SECOND": "SEGON", "TIME": "TEMPS", "TIMEVALUE": "VALHORA", "TODAY": "AVUI", "WEEKDAY": "DIASETM", "WEEKNUM": "NUM.DE.SEMANA", "WORKDAY": "DIA.LAB", "WORKDAY.INTL": "DIA.LAB.INTL", "YEAR": "ANY", "YEARFRAC": "FRAC.AÑO", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BIN.A.DEC", "BIN2HEX": "BIN.A.HEX", "BIN2OCT": "BIN.A.OCT", "BITAND": "BIT.I", "BITLSHIFT": "DESPLESQBIT", "BITOR": "BIT.O", "BITRSHIFT": "DESPLDERBIT", "BITXOR": "BIT.OEXC", "COMPLEX": "COMPLEX", "CONVERT": "CONVERT", "DEC2BIN": "DEC.A.BIN", "DEC2HEX": "DEC.A.HEX", "DEC2OCT": "DEC.A.OCT", "DELTA": "DELTA", "ERF": "FUN.ERROR", "ERF.PRECISE": "FUNC.ERROR.EXACTE", "ERFC": "FUN.ERROR.COMPL", "ERFC.PRECISE": "FUNC.ERROR.COMPL.EXACTE", "GESTEP": "MAYOR.O.IGUAL", "HEX2BIN": "HEX.A.BIN", "HEX2DEC": "HEX.A.DEC", "HEX2OCT": "HEX.A.OCT", "IMABS": "IM.ABS", "IMAGINARY": "IMAGINARIO", "IMARGUMENT": "IM.ANGULO", "IMCONJUGATE": "IM.CONJUGADA", "IMCOS": "IM.COS", "IMCOSH": "IM.COSH", "IMCOT": "IM.COT", "IMCSC": "IM.CSC", "IMCSCH": "IM.CSCH", "IMDIV": "IM.DIV", "IMEXP": "IM.EXP", "IMLN": "IM.LN", "IMLOG10": "IM.LOG10", "IMLOG2": "IM.LOG2", "IMPOWER": "IM.POT", "IMPRODUCT": "IM.PRODUCT", "IMREAL": "IM.REAL", "IMSEC": "IM.SEC", "IMSECH": "IM.SECH", "IMSIN": "IM.SIN", "IMSINH": "IM.SINH", "IMSQRT": "IM.ARREL2", "IMSUB": "IM.SUSTR", "IMSUM": "IM.SUM", "IMTAN": "IM.TAN", "OCT2BIN": "OCT.A.BIN", "OCT2DEC": "OCT.A.DEC", "OCT2HEX": "OCT.A.HEX", "DAVERAGE": "BDMITJANA", "DCOUNT": "BDCOMPT", "DCOUNTA": "BDCOMPTAA", "DGET": "BEXTREU", "DMAX": "BDMAX", "DMIN": "BDMIN", "DPRODUCT": "BDPRODUCTE", "DSTDEV": "BDDESVEST", "DSTDEVP": "BDDESVESTP", "DSUM": "BDSUMA", "DVAR": "BDVAR", "DVARP": "BDVARP", "CHAR": "CAR", "CLEAN": "NETEJA", "CODE": "CODI", "CONCATENATE": "CONCATENA", "CONCAT": "CONCAT", "DOLLAR": "MONEDA", "EXACT": "IGUAL", "FIND": "BUSCA", "FINDB": "FINDB", "FIXED": "DECIMAL", "LEFT": "ESQUERRA", "LEFTB": "LEFTB", "LEN": "LLARG", "LENB": "LENB", "LOWER": "MINUSC", "MID": "MIG", "MIDB": "MIDB", "NUMBERVALUE": "VALOR.NUMERO", "PROPER": "NOMPROPI", "REPLACE": "REEMPLAÇA", "REPLACEB": "REPLACEB", "REPT": "REPETEIX", "RIGHT": "DRETA", "RIGHTB": "RIGHTB", "SEARCH": "CERCAR", "SEARCHB": "SEARCHB", "SUBSTITUTE": "SUBSTITUEIX", "T": "T", "T.TEST": "PROVAT", "TEXT": "TEXT", "TEXTJOIN": "UNEIXCADENES", "TREND": "TENDENCIA", "TRIM": "RETALLA", "TRIMMEAN": "MITJANA.ACOTADA", "TTEST": "PROV.T", "UNICHAR": "UNICAR", "UNICODE": "UNICODE", "UPPER": "MAJUSC", "VALUE": "VALOR", "AVEDEV": "DESVMITJ", "AVERAGE": "MITJANA", "AVERAGEA": "MITJANAA", "AVERAGEIF": "MITJANA.SI", "AVERAGEIFS": "MITJANA.SI.CONJUNT", "BETADIST": "DISTRIBUCIOBETA", "BETAINV": "INVBETA", "BETA.DIST": "DISTRIBUCIO.BETA", "BETA.INV": "INV.BETA", "BINOMDIST": "DISTR.BINOM", "BINOM.DIST": "DISTR.BINOM.N", "BINOM.DIST.RANGE": "INTERVAL.DISTR.BINOM", "BINOM.INV": "INV.BINOM", "CHIDIST": "DISTR.CHI", "CHIINV": "PROVA.CHI.INV", "CHITEST": "PROVA.CHI", "CHISQ.DIST": "DISTR.CHIQUADR", "CHISQ.DIST.RT": "DISTR.CHIQUADR.CD", "CHISQ.INV": "INV.CHIQUADR", "CHISQ.INV.RT": "INV.CHIQUADR.CD", "CHISQ.TEST": "PROVA.CHIQUADR", "CONFIDENCE": "CONFIANÇA", "CONFIDENCE.NORM": "INTERVAL.CONFIANÇA.NORM", "CONFIDENCE.T": "INTERVAL.CONFIANÇA.T", "CORREL": "COEF.CORREL", "COUNT": "COMPT", "COUNTA": "COMPTAA", "COUNTBLANK": "COMPTA.BLANC", "COUNTIF": "COMPTA.SI", "COUNTIFS": "COMPTE.SI.CONJUNT", "COVAR": "COVAR", "COVARIANCE.P": "COVARIANÇA.P", "COVARIANCE.S": "COVARIANÇA.S", "CRITBINOM": "BINOM.CRIT", "DEVSQ": "DESVIA2", "EXPON.DIST": "DISTR.EXP.N", "EXPONDIST": "DISTR.EXP", "FDIST": "DISTR.F", "FINV": "DISTR.F.INV", "FTEST": "PROVA.F", "F.DIST": "DISTR.F.N", "F.DIST.RT": "DISTR.F.CD", "F.INV": "INV.F", "F.INV.RT": "INV.F.CD", "F.TEST": "PROVA.F.N", "FISHER": "FISHER", "FISHERINV": "PROVA.FISHER.INV", "FORECAST": "EXTRAPOLACIO", "FORECAST.ETS": "EXTRAPOLACIO.ETS", "FORECAST.ETS.CONFINT": "EXTRAPOLACIO.ETS.CONFINT", "FORECAST.ETS.SEASONALITY": "EXTRAPOLACIO.ETS.ESTACIONALITAT", "FORECAST.ETS.STAT": "FORECAST.ETS.STAT", "FORECAST.LINEAR": "EXTRAPOLACIO.LINEAL", "FREQUENCY": "FREQUENCIA", "GAMMA": "GAMMA", "GAMMADIST": "DISTRIBUCIOGAMMA", "GAMMA.DIST": "DISTRIBUCIO.GAMMA", "GAMMAINV": "INVGAMMA", "GAMMA.INV": "INV.GAMMA", "GAMMALN": "GAMMA.LN", "GAMMALN.PRECISE": "GAMMA.LN.EXACTE", "GAUSS": "GAUSS", "GEOMEAN": "MITJANA.GEO", "GROWTH": "CREIXEMENT", "HARMEAN": "MITJANA.HARM", "HYPGEOM.DIST": "DISTR.HIPERGEOM.N", "HYPGEOMDIST": "DISTR.HIPERGEOM", "INTERCEPT": "INTERSECCIO", "KURT": "CURTOSI", "LARGE": "MAJOR", "LINEST": "ESTIMACIO.LINEAL", "LOGEST": "ESTIMACIO.LOGARITMICA", "LOGINV": "DISTR.LOG.INV", "LOGNORM.DIST": "DISTR.LOGNORM", "LOGNORM.INV": "INV.LOGNORM", "LOGNORMDIST": "DISTR.LOG.NORM", "MAX": "MAX", "MAXA": "MAXA", "MAXIFS": "MAX.SI.CONJUNT", "MEDIAN": "MEDIANA", "MIN": "MIN", "MINA": "MINA", "MINIFS": "MIN.SI.CONJUNT", "MODE": "MODA", "MODE.MULT": "MODA.DIVERSOS", "MODE.SNGL": "MODA.UN", "NEGBINOM.DIST": "DISTR.NEGBINOM", "NEGBINOMDIST": "DISTRNEGBINOM", "NORM.DIST": "DISTR.NORM.N", "NORM.INV": "INV.NORM", "NORM.S.DIST": "DISTR.NORM.ESTAND.N", "NORM.S.INV": "INV.NORM.ESTAND", "NORMDIST": "DISTR.NORM", "NORMINV": "DISTR.NORM.INV", "NORMSDIST": "DISTR.NORM.ESTAND", "NORMSINV": "DISTR.NORM.ESTAND.INV", "PEARSON": "PEARSON", "PERCENTILE": "PERCENTIL", "PERCENTILE.EXC": "PERCENTIL.EXC", "PERCENTILE.INC": "PERCENTIL.INC", "PERCENTRANK": "RANG.PERCENTIL", "PERCENTRANK.EXC": "RANG.PERCENTIL.EXC", "PERCENTRANK.INC": "RANG.PERCENTIL.INC", "PERMUT": "PERMUTACIO", "PERMUTATIONA": "PERMUTACIOA", "PHI": "PHI", "POISSON": "POISSON", "POISSON.DIST": "DISTR.POISSON", "PROB": "PROBABILITAT", "QUARTILE": "QUARTI", "QUARTILE.INC": "QUARTI.INC", "QUARTILE.EXC": "QUARTI.EXC", "RANK.AVG": "JERARQUIA.MITJANA", "RANK.EQ": "JERARQUIA.EQV", "RANK": "JERARQUIA", "RSQ": "COEF.QUADRAT", "SKEW": "BIAIX", "SKEW.P": "BIAIX.P", "SLOPE": "PENDENT", "SMALL": "MENOR", "STANDARDIZE": "NORMALITZA", "STDEV": "DESVEST", "STDEV.P": "DESVEST.P", "STDEV.S": "DESVEST.M", "STDEVA": "DESVESTA", "STDEVP": "DESVESTP", "STDEVPA": "DESVESTPA", "STEYX": "ERROR.TIPIC.XY", "TDIST": "DISTR.T", "TINV": "DISTR.T.INV", "T.DIST": "DISTR.T.N", "T.DIST.2T": "DISTR.T.2C", "T.DIST.RT": "DISTR.T.CD", "T.INV": "INV.T", "T.INV.2T": "INV.T.2C", "VAR": "VAR", "VAR.P": "VAR.P", "VAR.S": "VAR.M", "VARA": "VARA", "VARP": "VARP", "VARPA": "VARPA", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "DISTR.WEIBULL", "Z.TEST": "PROVAZ", "ZTEST": "PROV.Z", "ACCRINT": "INT.ACUM", "ACCRINTM": "INT.ACUM.V", "AMORDEGRC": "AMORTITZ.PROGRE", "AMORLINC": "AMORTITZ.LIN", "COUPDAYBS": "CUPON.DIAS.L1", "COUPDAYS": "CUPON.DIAS", "COUPDAYSNC": "CUPON.DIAS.L2", "COUPNCD": "CUPON.FECHA.L2", "COUPNUM": "CUPON.NUM", "COUPPCD": "CUPON.FECHA.L1", "CUMIPMT": "PAGO.INT.ENTRE", "CUMPRINC": "PAGO.PRINC.ENTRE", "DB": "DB", "DDB": "DDD", "DISC": "TASA.DESC", "DOLLARDE": "MONEDA.DEC", "DOLLARFR": "MONEDA.FRAC", "DURATION": "DURACION", "EFFECT": "INT.EFECTIVO", "FV": "VF", "FVSCHEDULE": "VF.PLAN", "INTRATE": "TASA.INT", "IPMT": "PAGI", "IRR": "TIR", "ISPMT": "INT.PAG.DIR", "MDURATION": "DURACION.MODIF", "MIRR": "TIRM", "NOMINAL": "TASA.NOMINAL", "NPER": "NPER", "NPV": "VAN", "ODDFPRICE": "PRECIO.PER.IRREGULAR.1", "ODDFYIELD": "RENDTO.PER.IRREGULAR.1", "ODDLPRICE": "PRECIO.PER.IRREGULAR.2", "ODDLYIELD": "RENDTO.PER.IRREGULAR.2", "PDURATION": "DURACION.P", "PMT": "PAGAMENT", "PPMT": "PAGPRIN", "PRICE": "PRECIO", "PRICEDISC": "PRECIO.DESCUENTO", "PRICEMAT": "PRECIO.VENCIMIENTO", "PV": "VP", "RATE": "TAXA", "RECEIVED": "CANTIDAD.RECIBIDA", "RRI": "RIR", "SLN": "SLN", "SYD": "DMA", "TBILLEQ": "LETRA.DE.TES.EQV.A.BONO", "TBILLPRICE": "LETRA.DE.TES.PRECIO", "TBILLYIELD": "LETRA.DE.TES.RENDTO", "VDB": "DVS", "XIRR": "TIR.NO.PER", "XNPV": "VNA.NO.PER", "YIELD": "RENDTO", "YIELDDISC": "RENDTO.DESC", "YIELDMAT": "RENDTO.VENCTO", "ABS": "ABS", "ACOS": "ACOS", "ACOSH": "ACOSH", "ACOT": "ACOT", "ACOTH": "ACOTH", "AGGREGATE": "VALOR.AFEGIT", "ARABIC": "ARAB", "ASC": "ASC", "ASIN": "ASIN", "ASINH": "ASINH", "ATAN": "ATAN", "ATAN2": "ATAN2", "ATANH": "ATANH", "BASE": "BASE", "CEILING": "MULTIPLE.SUPERIOR", "CEILING.MATH": "MULTIPLE.SUPERIOR.MAT", "CEILING.PRECISE": "CEILING.PRESIZE", "COMBIN": "COMBIN", "COMBINA": "COMBINA", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "CONV.DECIMAL", "DEGREES": "GRAUS", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "PARELL", "EXP": "EXP", "FACT": "FACT", "FACTDOUBLE": "FACT.DOBLE", "FLOOR": "MULTIPLE.INFERIOR", "FLOOR.PRECISE": "FLOOR.PRECISE", "FLOOR.MATH": "MULTIPLE.INFERIOR.MAT", "GCD": "M.C.D", "INT": "ENTER", "ISO.CEILING": "ISO.CEILING", "LCM": "M.C.M", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MDETERM", "MINVERSE": "MINVERSA", "MMULT": "MMULT", "MOD": "RESIDU", "MROUND": "REDOND.MULT", "MULTINOMIAL": "MULTINOMIAL", "MUNIT": "MUNIT", "ODD": "SENAR", "PI": "PI", "POWER": "POTENCIA", "PRODUCT": "PRODUCTE", "QUOTIENT": "COCIENTE", "RADIANS": "RADIANS", "RAND": "ALEAT", "RANDARRAY": "MATRIUALEAT", "RANDBETWEEN": "ALEATORIO.ENTRE", "ROMAN": "NUMERO.ROMA", "ROUND": "ARRODONEIX", "ROUNDDOWN": "ARRODONEIX.MENYS", "ROUNDUP": "ARRODONEIX.MES", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SUMA.SERIES", "SIGN": "SIGNE", "SIN": "SIN", "SINH": "SINH", "SQRT": "ARREL", "SQRTPI": "ARREL2PI", "SUBTOTAL": "SUBTOTAL", "SUM": "SUMA", "SUMIF": "SUMA.SI", "SUMIFS": "SUMA.SI.CONJUNT", "SUMPRODUCT": "SUMAPRODUCTE", "SUMSQ": "SUMA.QUADRATS", "SUMX2MY2": "SUMAX2MENYSY2", "SUMX2PY2": "SUMAX2MESY2", "SUMXMY2": "SUMAXMENYSY2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "TRUNC", "ADDRESS": "ADREÇA", "CHOOSE": "TRIA", "COLUMN": "COLUMNA", "COLUMNS": "COLUMNES", "FORMULATEXT": "TEXTFORMULA", "HLOOKUP": "CONSULH", "HYPERLINK": "ENLLAÇ", "INDEX": "INDEX", "INDIRECT": "INDIRECTE", "LOOKUP": "CONSULTA", "MATCH": "COINCIDEIX", "OFFSET": "DESPLAÇAMENT", "ROW": "FILA", "ROWS": "FILES", "TRANSPOSE": "TRANSPOSA", "UNIQUE": "ÚNICS", "VLOOKUP": "CONSULV", "XLOOKUP": "XLOOKUP", "CELL": "CELL", "ERROR.TYPE": "TIPUS.ERROR", "ISBLANK": "ESBLANC", "ISERR": "ESERR", "ISERROR": "ESERROR", "ISEVEN": "ES.PAR", "ISFORMULA": "ESFORMULA", "ISLOGICAL": "ESLOGIC", "ISNA": "ESND", "ISNONTEXT": "ESNOTEXT", "ISNUMBER": "ESNUM", "ISODD": "ES.IMPAR", "ISREF": "ESREF", "ISTEXT": "ESTEXT", "N": "N", "NA": "ND", "SHEET": "FULL", "SHEETS": "FULLS", "TYPE": "TIPUS", "AND": "I", "FALSE": "FALS", "IF": "SI", "IFS": "SI.CONJUNT", "IFERROR": "SIERROR", "IFNA": "SIND", "NOT": "NO", "OR": "O", "SWITCH": "CANVIA", "TRUE": "CERT", "XOR": "OEXC", "TEXTBEFORE": "TEXTABANS", "TEXTAFTER": "TEXTDESPRES", "TEXTSPLIT": "DIVIDEIXTEXT", "WRAPROWS": "AJUSTAFILES", "VSTACK": "APILAV", "HSTACK": "APILAH", "CHOOSEROWS": "TRIAFILES", "CHOOSECOLS": "TRIACOL", "TOCOL": "ACOL", "TOROW": "AFILA", "WRAPCOLS": "AJUSTACOL", "TAKE": "PREN", "DROP": "EXCLOU", "SEQUENCE": "SEQÜÈNCIA", "EXPAND": "EXPANDEIX", "XMATCH": "XMATCH", "FILTER": "FILTRA", "ARRAYTOTEXT": "MATRIUATEXT", "SORT": "ORDENA", "SORTBY": "ORDENAPER", "GETPIVOTDATA": "OBTEN.DADES.DINAMIQUES", "IMPORTRANGE": "IMPORTRANGE", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "TRUE", "f": "FALSE"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!", "calc": "#CALC!"}, "CELL_FUNCTION_INFO_TYPE": {"address": "ad<PERSON><PERSON>", "col": "columna", "color": "color", "contents": "contingut", "filename": "nom del fitxer", "format": "format", "parentheses": "<PERSON><PERSON><PERSON><PERSON>", "prefix": "prefix", "protect": "protecció", "row": "fila", "type": "tipus", "width": "amplada"}}}