{"DATE": {"a": "(yıl; ay; gün)", "d": "Tarih-saat kodundaki tarihi gösteren sayıyı verir.", "ad": "Windows için Spreadsheet Editor'de 1900 veya 1904 ile (çalışma kitabının tarih sistemine göre) 9999 arasındaki bir sayı!yılın ayını gösteren 1 ile 12 arasındaki sayı!ayın gününü gösteren 1 ile 31 arasındaki sayı"}, "DATEDIF": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; bitiş_tarihi; birim)", "d": "<PERSON>ki tarih a<PERSON> g<PERSON>n, ay veya yıl sayı<PERSON>ını hesaplar", "ad": "Belirli bir dönemin ilk veya başlangıç tarihini temsil eden tarih!Dönemin son veya bitiş tarihini gösterir!Döndürülmesini istediğiniz bilgi türü"}, "DATEVALUE": {"a": "(tarih_metni)", "d": "Metin formunda bulunan bir tarihi tarih-saat kodunu gösteren bir sayıya dönüştürür", "ad": "Bir tarihi Spreadsheet Editor tarih biçiminde 1/1/1900 ile 1/1/1904 veya (çalışma kitabının tarih sistemine göre) 12/31/9999 arasında gösteren metin"}, "DAY": {"a": "(seri_no)", "d": "1 ile 31 <PERSON><PERSON><PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dö<PERSON>ü<PERSON>ü<PERSON>.", "ad": "Spreadsheet Editor tarafından kullanılan tarih-saat kodundaki sayı"}, "DAYS": {"a": "(bitiş_tarihi; ba<PERSON><PERSON><PERSON><PERSON>_tarihi)", "d": "İki tarih arasındaki gün sayısını döndürür.", "ad": "ba<PERSON><PERSON><PERSON>ç_tarihi ve bitiş_tarihi aralarındaki gün sayısını bilmek istediğiniz iki tarih!başlangıç_tarihi ve bitiş_tarihi aralarındaki gün sayısını bilmek istediğiniz iki tarih"}, "DAYS360": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; bitiş_tarihi; [yö<PERSON><PERSON>])", "d": "İki tarih arasındaki gün sayısını 360 günlük yılı kullanarak hesaplar (oniki 30 günlük ay)", "ad": "ba<PERSON><PERSON><PERSON>ç_tarihi ve son_tarih, iki tarih arasındaki gün sayısını bulmak için kullanılır!baş<PERSON><PERSON>ç_tarihi ve son_tarih, iki tarih arasındaki gün sayısını bulmak için kullanılır!hesaplama yöntemini belirten mantıksal değer: ABD (NASD) = YANLIŞ ya da atlanmış; Avrupa = DOĞRU."}, "EDATE": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; ay_sayısı)", "d": "Başlangıç tarihinden önceki veya sonraki ay sayısını belirten tarihin seri numarasını döndürür", "ad": "baş<PERSON><PERSON>ç tarihini gösteren tarih seri numarası!başlangıç tarihinden önceki veya sonraki ay sayısı"}, "EOMONTH": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; ay_sayısı)", "d": "<PERSON><PERSON><PERSON><PERSON> sayıda ay önce veya sonraki ayın son gü<PERSON><PERSON><PERSON><PERSON> belirten seri numarası döndür<PERSON>r", "ad": "baş<PERSON><PERSON>ç tarihini gösteren tarih seri numarası!başlangıç tarihinden önceki veya sonraki ay sayısı"}, "HOUR": {"a": "(seri_no)", "d": "<PERSON><PERSON> ve<PERSON>, bir seri numa<PERSON>ına karşılık gelen 0 (12:00)'dan 23 (11:00)'e kadar bir tamsayı.", "ad": "Spreadsheet Editor tara<PERSON><PERSON><PERSON><PERSON> kullanılan tarih-saat kodundaki sayı ya da zaman biçiminde metin, <PERSON><PERSON><PERSON><PERSON> 16:48:00 ya da 4:48:00"}, "ISOWEEKNUM": {"a": "(tarih)", "d": "Verilen tarih için yıl içinde ISO hafta numarasını döndürür", "ad": "tarih ve saat hesaplama için Spreadsheet Editor tarafından kullanılan tarih-saat kodu"}, "MINUTE": {"a": "(seri_no)", "d": "<PERSON>ir seri numarasına karşıl<PERSON><PERSON> gelen, 0-59 a<PERSON><PERSON>nda bir tamsayı olan dakikayı verir.", "ad": "Spreadsheet Editor tara<PERSON><PERSON><PERSON><PERSON> kullanılan tarih-saat kodundaki sayı ya da zaman biçimindeki metin, <PERSON><PERSON><PERSON><PERSON> 16:48:00 ya da 4:48:00"}, "MONTH": {"a": "(seri_no)", "d": "1 (Ocak) ile 12 (Aralık) arasındaki bir sayı ile ifade edilen ayı döndürür.", "ad": "Spreadsheet Editor tarafından kullanılan tarih-saat kodundaki sayı"}, "NETWORKDAYS": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; biti<PERSON>_tarihi; [tatiller])", "d": "İki tarih arasındaki tüm işgünlerinin sayısını döndürür", "ad": "başlangıç tarihini gösteren tarih seri numarası!bitiş tarihini gösteren tarih seri numarası!isteğe göre çalışma takviminden çıkarılacak, bir veya daha fazla tarih seri numarası serisi; <PERSON>rneğin resmi tatiller"}, "NETWORKDAYS.INTL": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; biti<PERSON>_tarihi; [hafta_sonu]; [tatiller])", "d": "İki tarih arasındaki tam işgünlerinin sayısını özel hafta sonu parametreleriyle verir", "ad": "baş<PERSON><PERSON>ç tarihini gösteren tarih seri numarası!bitiş tarihini gösteren tarih seri numarası!hafta sonunun ne zaman olduğunu belirten sayı veya dize!isteğe göre çalışma takviminden çıkarılacak, bir veya daha fazla tarih seri numarası serisi; örneğin resmi tatiller"}, "NOW": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> tarihi ve saati, tarih ve saat biçiminde verir.", "ad": ""}, "SECOND": {"a": "(seri_no)", "d": "Saniyeyi seri numarasına karşılık gelen 0 ile 59 arasında bir tamsayı cinsinden verir.", "ad": "Spreadsheet Editor tara<PERSON><PERSON><PERSON><PERSON> kullanılan tarih-saat kodundaki sayı ya da saat biçimindeki metin, <PERSON><PERSON><PERSON><PERSON> 16:48:23 ya da 4:48:47"}, "TIME": {"a": "(saat; dakika; saniye)", "d": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, saniye olarak girilen sayıları zaman biçimindeki seri numarasına dönüştürür", "ad": "0-23 arasında saati gösteren sayı!0-59 arasında dakikayı gösteren sayı!0-59 arasında saniyeyi gösteren sayı"}, "TIMEVALUE": {"a": "(saat_metni)", "d": "Bir metin dizesiyle (saat_metni) gösterilen bir saati 0 (00:00:00) ile 0,999988426 (23:59:59) arasındaki saat seri numarasına çevirir. Formülü girdikten sonra sayıyı saat biçiminde biçimlendirin", "ad": "saati Spreadsheet Editor saat biçimlerinden herhangi bi<PERSON>yle gösteren metin dizesi (dizedeki tarih bilgisi yoksayılır)"}, "TODAY": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarihi, tarih biçiminde verir.", "ad": ""}, "WEEKDAY": {"a": "(seri_no; [dö<PERSON><PERSON><PERSON>_tür])", "d": "Verilen tarih gösteren sayıyı kullanarak haftanın gününü tanımlayan 1 ile 7 arasındaki sayı.", "ad": "bir tarih gö<PERSON>en sayı!bir sayı: Pazar = 1'den Cumartesi = 7'ye ise, 1'i kullanın; Pazartesi = 1'den Pazar = 7'ye ise, 2'yi kullanın; Pazartesi = 0'dan <PERSON> = 6'ya ise, 3'ü kullanın"}, "WEEKNUM": {"a": "(seri_num; [dö<PERSON><PERSON><PERSON>_türü])", "d": "<PERSON><PERSON>l içinde haftanın numarasını döndürür", "ad": "Spreadsheet Editor tarafından tarih ve zaman hesaplamalarında kullanılan tarih-zaman kodu!döndürülen değerin türünü belirleyen sayı(1 veya 2)"}, "WORKDAY": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; gün_sayısı; [tatiller])", "d": "Belirtilen sayıda işgünü önce veya sonraki bir tarihin seri numarasını döndürür", "ad": "ba<PERSON><PERSON><PERSON>ç tarihini gösteren tarih seri numarası!başlangıç tarihinden önceki veya sonraki tatil veya haftasonu olmayan günler !isteğe göre çalışma takviminden çıkarılacak, bir veya daha fazla tarih seri numarası serisi; <PERSON>rne<PERSON>in resmi tatiller"}, "WORKDAY.INTL": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; gün_sayısı; [hafta_sonu]; [tatiller])", "d": "Belirtilen sayıda işgünü önce veya sonraki bir tarihin seri numarasını özel hafta sonu parametreleriyle verir", "ad": "ba<PERSON><PERSON><PERSON>ç tarihini gösteren tarih seri numarası!başlangıç tarihinden önceki veya sonraki tatil veya hafta sonu olmayan günler!hafta sonlarının ne zaman olduğunu belirten sayı veya dize!isteğe göre çalışma takviminden çıkarılacak, bir veya daha fazla tarih seri numarası dizisi; örneğin resmi tatiller"}, "YEAR": {"a": "(seri_no)", "d": "1900 - 9999 aralığındaki bir tamsayı ile ifade edilen tarihin yılını döndürür.", "ad": "Spreadsheet Editor tarafından kullanılan tarih-saat kodundaki sayı"}, "YEARFRAC": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; bitiş_tarihi; [temel])", "d": "Başlangıç ve bitiş tarihleri arasındaki tam gün sayısını gösteren yıl oranını döndürür", "ad": "ba<PERSON><PERSON><PERSON>ç tarihini gösteren tarih seri numarası!bitiş tarihini gösteren tarih seri numarası!kullanılacak gün sayısı türü"}, "BESSELI": {"a": "(x; n)", "d": "In(x) değiştirilmiş Bessel işlevini döndürür", "ad": "işlevin hesaplanacağı değer!Bessel işlevinin derecesi"}, "BESSELJ": {"a": "(x; n)", "d": "Jn(x) Bessel işlevini döndürür", "ad": "işlevin hesaplanacağı değer!Bessel işlevinin derecesi"}, "BESSELK": {"a": "(x; n)", "d": "Kn(x) değiştirilmiş Bessel işlevini döndürür", "ad": "işlevin hesaplanacağı değer!işlevin derecesi"}, "BESSELY": {"a": "(x; n)", "d": "Yn(x) Bessel işlevini döndürür", "ad": "işlevin hesaplanacağı değer!işlevin derecesi"}, "BIN2DEC": {"a": "(sayı)", "d": "İkilik düzendeki bir sayıyı onluk düzene dönüştürür", "ad": "dönüştürmek istediğiniz ikilik düzendeki sayı"}, "BIN2HEX": {"a": "(sayı; [basamak])", "d": "İkilik düzendeki bir sayıyı onaltılık düzene dönüştürür", "ad": "dönüştürmek istediğiniz ikilik düzendeki sayı!kullanılacak karakter sayısı"}, "BIN2OCT": {"a": "(sayı; [basamak])", "d": "İkilik düzendeki bir sayıyı sekizlik düzene dönüştürür", "ad": "dönüştürmek istediğiniz ikilik düzendeki sayı!kullanılacak karakter sayısı"}, "BITAND": {"a": "(sayı1; sayı2)", "d": "İki sayının bit tabanlı bir 'Ve' değerini verir", "ad": "hesaplamak istediğiniz ikili sayının ondalık gösterimi!hesaplamak istediğiniz ikili sayının ondalık gösterimi"}, "BITLSHIFT": {"a": "(sayı; kaydırma_miktarı)", "d": "kaydırma_miktarı kadar bit sola kaydırılan bir sayıyı verir", "ad": "hesaplamak istediğiniz ikilik düzendeki bir sayının ondalık gösterimidir!Sayıyı sola kaydırmak istediğiniz bitlerin sayısı"}, "BITOR": {"a": "(sayı1; sayı2)", "d": "İki sayının bit tabanlı bir 'Veya' değerini verir", "ad": "hesaplamak istediğiniz ikili sayının ondalık gösterimi!hesaplamak istediğiniz ikili sayının ondalık gösterimi"}, "BITRSHIFT": {"a": "(sayı; kaydırma_miktarı)", "d": "kaydırma_miktarı kadar bit sağa kaydırılan bir sayıyı verir", "ad": "hesaplamak istediğiniz ikilik düzendeki bir sayının ondalık gösterimidir!Sayıyı sağa kaydırmak istediğiniz bitlerin sayısı"}, "BITXOR": {"a": "(sayı1; sayı2)", "d": "İki sayının bit tabanlı bir 'Özel Veya' değ<PERSON>ni verir", "ad": "hesaplamak istediğiniz ikili sayının ondalık gösterimi!hesaplamak istediğiniz ikili sayının ondalık gösterimi"}, "COMPLEX": {"a": "(ger<PERSON><PERSON>_sayı; karm_sayı; [sonek])", "d": "Gerçel ve sanal katsayıları bir karmaşık sayıya dönüştürür", "ad": "karmaşık sayının gerçel katsayısı!karmaşık sayının sanal katsayısı!karmaşık sayının sanal kısmı için sonek"}, "CONVERT": {"a": "(sayı; ilk_birim; son_birim)", "d": "Sayıyı bir ölçü biriminden bir diğerine dönüştürür", "ad": "dönüşecek birimdeki değer!sayı için birim!sonuç için birim"}, "DEC2BIN": {"a": "(sayı; [basamak])", "d": "Onluk düzendeki bir sayıyı ikilik düzene dönüştürür", "ad": "dönüştürmek istediğiniz onlu tamsayı!kullanılacak karakter sayısı"}, "DEC2HEX": {"a": "(sayı; [basamak])", "d": "Onluk düzendeki bir sayıyı onaltılık düzene dönüştürür", "ad": "dönüştürmek istediğiniz onlu tamsayı!kullanılacak karakter sayısı"}, "DEC2OCT": {"a": "(sayı; [basamak])", "d": "Onluk düzendeki bir sayıyı sekizlik düzene dönüştürür", "ad": "dönüştürmek istediğiniz onlu tamsayı!kullanılacak karakter sayısı"}, "DELTA": {"a": "(sayı1; [sayı2])", "d": "İki sayının e<PERSON> sınar", "ad": "ilk sayı!ikinci sayı"}, "ERF": {"a": "(alt_limit; [üst_limit])", "d": "Hata işlevini döndürür", "ad": "ERF için alt sınır!ERF için üst sınır"}, "ERF.PRECISE": {"a": "(X)", "d": "Hata işlevini döndürür", "ad": "HATAİŞLEV.DUYARLI işlevini tümleştirmek için alt sınırdır"}, "ERFC": {"a": "(x)", "d": "Tümleyici hata işlevini döndürür", "ad": "ERF için alt sınır"}, "ERFC.PRECISE": {"a": "(X)", "d": "Tamamlayıcı hata işlevini döndürür", "ad": "TÜMHATAİŞLEV.DUYARLI işlevini tümleştirmek için alt sınırdır"}, "GESTEP": {"a": "(sayı; [sın<PERSON><PERSON>_de<PERSON><PERSON>])", "d": "<PERSON><PERSON> sayının sınır bir değerden büyük olup olmadığını sınar.", "ad": "sınanacak sayı!sınır <PERSON>"}, "HEX2BIN": {"a": "(sayı; [basamak])", "d": "Onaltılık düzendeki bir sayıyı ikilik düzene dönüştürür", "ad": "dönüştürmek istediğiniz onaltılık düzendeki sayı!kullanılacak karakter sayısı"}, "HEX2DEC": {"a": "(sayı)", "d": "Onaltılık düzendeki bir sayıyı onluk düzene çevirir", "ad": "dönüştürmek istediğiniz onaltılık düzendeki sayı"}, "HEX2OCT": {"a": "(sayı; [basamak])", "d": "Onaltılık düzendeki bir sayıyı sekizlik düzene dönüştürür", "ad": "dönüştürmek istediğiniz onaltılık düzendeki sayı!kullanılacak karakter sayısı"}, "IMABS": {"a": "(karmsayı)", "d": "Bir karmaşık sayının mutlak <PERSON> (modulus) döndürür", "ad": "mutlak değeri istenen karmaşık sayı"}, "IMAGINARY": {"a": "(karmsayı)", "d": "Bir karmaşık sayının sanal katsayısını döndürür", "ad": "sanal katsayısı istenen karmaşık sayı"}, "IMARGUMENT": {"a": "(karmsayı)", "d": "Bir karmaşık sayının radyan cinsinden bağ<PERSON><PERSON><PERSON><PERSON> (q) döndürür", "ad": "argümanı istenen karmaşık sayı"}, "IMCONJUGATE": {"a": "(karmsayı)", "d": "Bir karmaşık sayının eşleneğini döndürür", "ad": "eşleneği istenen karmaşık sayı"}, "IMCOS": {"a": "(karmsayı)", "d": "Bir karmaşık sayının kosinüs değerini döndürür", "ad": "kosinüsü istenen karmaşık sayı"}, "IMCOSH": {"a": "(karmsayı)", "d": "Bir karmaşık sayının hiperbolik kosinüs değerini verir", "ad": "hiperbolik kosinüsü istenen karmaşık sayı"}, "IMCOT": {"a": "(karmsayı)", "d": "Bir karmaşık sayının kotanjant değerini verir", "ad": "kotanjantı istenen karmaşık sayı"}, "IMCSC": {"a": "(karmsayı)", "d": "Bir karmaşık sayının kosekant değerini verir", "ad": "kosekantı istenen karmaşık sayı"}, "IMCSCH": {"a": "(karmsayı)", "d": "Bir karmaşık sayının hiperbolik kosekant değerini verir", "ad": "hiperbolik kosekantı istenen karmaşık sayı"}, "IMDIV": {"a": "(karmsayı1; karmsayı2)", "d": "İki karmaşık sayının bölümünü döndürür", "ad": "karmaşık pay veya bölünen!karmaşık payda veya bölen"}, "IMEXP": {"a": "(karmsayı)", "d": "Bir karmaşık sayının üssel değerini döndürür", "ad": "üssel değeri bulunacak karmaşık sayı"}, "IMLN": {"a": "(karmsayı)", "d": "Bir karmaşık sayının doğal logaritmasını döndürür", "ad": "doğal logaritması alınacak karmaşık sayı"}, "IMLOG10": {"a": "(karmsayı)", "d": "Bir karmaşık sayının 10 tabanında logaritmasını döndürür", "ad": "ortak logaritması alınacak karmaşık sayı"}, "IMLOG2": {"a": "(karmsayı)", "d": "Bir karmaşık sayının 2 tabanında logaritmasını döndürür", "ad": "2 tabanında logaritması alınacak karmaşık sayı"}, "IMPOWER": {"a": "(karmsayı; sayı)", "d": "Bir karmaşık sayının tamsayı bir kuvvetini döndürür", "ad": "kuvveti alınacak karmaşık sayı!karmaşık sayının yükseltileceği kuvvet"}, "IMPRODUCT": {"a": "(isayı1; [isayı2]; ...)", "d": "En az 1 en çok 255 karmaşık sayının çarpımını döndürür", "ad": "<PERSON><PERSON><PERSON>1, <PERSON><PERSON><PERSON>2,... çarpımı yapılacak en az 1 en çok 255 sayıdır."}, "IMREAL": {"a": "(karmsayı)", "d": "Bir karmaşık sayının gerçel katsayısını döndürür", "ad": "gerçel katsayısı istenen karmaşık sayı"}, "IMSEC": {"a": "(karmsayı)", "d": "Bir karmaşık sayının sekant değerini verir", "ad": "sekantı istenen karmaşık sayı"}, "IMSECH": {"a": "(karmsayı)", "d": "Bir karmaşık sayının hiperbolik sekant değerini verir", "ad": "hiperbolik sekantı istenen karmaşık sayı"}, "IMSIN": {"a": "(karmsayı)", "d": "Bir karmaşık sayının sinüs değerini döndürür", "ad": "sinüsü istenen karmaşık sayı"}, "IMSINH": {"a": "(karmsayı)", "d": "Bir karmaşık sayının hiperbolik sinüs değerini verir", "ad": "hiperbolik sinüsü istenen karmaşık sayı"}, "IMSQRT": {"a": "(karmsayı)", "d": "Bir karmaşık sayının karekökünü döndürür", "ad": "karekökü istenen karmaşık sayı"}, "IMSUB": {"a": "(karmsayı1; karmsayı2)", "d": "İki karmaşık sayının farkını döndürür", "ad": "karmsayı2 değerinin çıkarılacağı karmaşık sayı!karmsayı1 değerinden çıkarılacak karmaşık sayı"}, "IMSUM": {"a": "(isayı1; [isayı2]; ...)", "d": "Karmaşık sayıların toplamını döndürür", "ad": "eklenecek en az 1 en çok 255 karmaşık sayı"}, "IMTAN": {"a": "(karmsayı)", "d": "Bir karmaşık sayının tanjant değerini verir", "ad": "tanjantı istenen karmaşık sayı"}, "OCT2BIN": {"a": "(sayı; [basamak])", "d": "Sekizlik düzendeki bir sayıyı ikilik düzene dönüştürür", "ad": "dönüştürmek istediğiniz sekizlik düzendeki sayı!kullanılacak karakter sayısı"}, "OCT2DEC": {"a": "(sayı)", "d": "Sekizlik düzendeki bir sayıyı onluk düzene dönüştürür", "ad": "dönüştürmek istediğiniz sekizlik düzendeki sayı"}, "OCT2HEX": {"a": "(sayı; [basamak])", "d": "Sekizlik düzendeki bir sayıyı onaltılık düzene dönüştürür", "ad": "dönüştürmek istediğiniz sekizlik düzendeki sayı!kullanılacak karakter sayısı"}, "DAVERAGE": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Bir liste ya da veritabanındaki bir sütunda yer alan ve belirttiğiniz koşullara uyan değerlerin ortalamasını verir", "ad": "veritabanını ya da listeyi oluşturan hücreler aralığı. Veritabanı ilgili verilerin listesidir!çift tırnak işareti içindeki sütun etiketi, ya da sütunun listedeki konumunu gösteren sayı!belirttiğiniz koşulları içeren hücreler aralığı. Aralık, sütun etiketi ile etiketin altında bulunan ve koşulu taşıyan bir hücreyi içerir"}, "DCOUNT": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Veritabanındaki kayıt alanında (sütun) bulunan sayıları içeren ve belirttiğiniz koşullara uyan hücreleri sayar", "ad": "veritabanını ya da listeyi oluşturan hücre aralığı. Veritabanı ilgili verilerin listesidir!çift tırnak işareti içindeki sütun etiketi veya sütunun listedeki konumunu gösteren sayı!belirttiğiniz koşulları içeren hücre aralığı. <PERSON><PERSON><PERSON>k, sütun etiketi ile etiketin altında bulunan ve koşulu taşıyan bir hücreyi içerir"}, "DCOUNTA": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Veritabanındaki kayıt alanında (sütun) bulunan ve belirttiğiniz koşullara uyan boş olmayan hücreleri sayar", "ad": "veritabanını ya da listeyi oluşturan hücreler aralığı. Veritabanı ilgili verilerin listesidir!çift tırnak işareti içindeki sütun etiketi, ya da sütunun listedeki konumunu gösteren sayı!belirttiğiniz koşulları içeren hücreler aralığı. Aralık, sütun etiketi ile etiketin altında bulunan ve koşulu taşıyan bir hücreyi içerir"}, "DGET": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Belirttiğiniz koşullara uyan tek bir kaydı veritabanından çıkarır", "ad": "veritabanını ya da listeyi oluşturan hücreler aralığı. Veritabanı ilgili verilerin listesidir!çift tırnak işareti içindeki sütun etiketi, ya da sütunun listedeki konumunu gösteren sayı!belirttiğiniz koşulları içeren hücreler aralığı. Aralık, sütun etiketi ile etiketin altında bulunan ve koşulu taşıyan bir hücreyi içerir"}, "DMAX": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Veritabanındaki kayıt alanında (sütun) bulunan ve belirttiğiniz koşullara uyan en büyük sayıyı verir.", "ad": "veritabanını ya da listeyi oluşturan hücreler aralığı. Veritabanı ilgili verilerin listesidir!çift tırnak işareti içindeki sütun etiketi, ya da sütunun listedeki konumunu gösteren sayı!belirttiğiniz koşulları içeren hücreler aralığı. Aralık, sütun etiketi ile etiketin altında bulunan ve koşulu taşıyan bir hücreyi içerir"}, "DMIN": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Veritabanındaki kayıt alanında (sütun) bulunan ve belirttiğiniz koşullara uyan en küçük sayıyı verir", "ad": "veritabanını ya da listeyi oluşturan hücreler aralığı. Veritabanı ilgili verilerin listesidir!çift tırnak işareti içindeki sütun etiketi, ya da sütunun listedeki konumunu gösteren sayı!belirttiğiniz koşulları içeren hücreler aralığı. Aralık, sütun etiketi ile etiketin altında bulunan ve koşulu taşıyan bir hücreyi içerir"}, "DPRODUCT": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Veritabanındaki kayıt alanında (sütun) bulunan ve belirttiğiniz koşullara uyan verileri çarpar", "ad": "veritabanını ya da listeyi oluşturan hücreler aralığı. Veritabanı ilgili verilerin listesidir!çift tırnak işareti içindeki sütun etiketi, ya da sütunun listedeki konumunu gösteren sayı!belirttiğiniz koşulları içeren hücreler aralığı. Aralık, sütun etiketi ile etiketin altında bulunan ve koşulu taşıyan bir hücreyi içerir"}, "DSTDEV": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Seçili veritabanı girdilerinden alınan bir örneğin standart sapmasını tahmin eder", "ad": "veritabanını ya da listeyi oluşturan hücreler aralığı. Veritabanı ilgili verilerin listesidir!çift tırnak işareti içindeki sütun etiketi, ya da sütunun listedeki konumunu gösteren sayı!belirttiğiniz koşulları içeren hücreler aralığı. Aralık, sütun etiketi ile etiketin altında bulunan ve koşulu taşıyan bir hücreyi içerir"}, "DSTDEVP": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Seçili veritabanı girdilerinden oluşan tüm popülasyonun standart sapmasını hesaplar", "ad": "veritabanını ya da listeyi oluşturan hücreler aralığı. Veritabanı ilgili verilerin listesidir!çift tırnak işareti içindeki sütun etiketi, ya da sütunun listedeki konumunu gösteren sayı!belirttiğiniz koşulları içeren hücreler aralığı. Aralık, sütun etiketi ile etiketin altında bulunan ve koşulu taşıyan bir hücreyi içerir"}, "DSUM": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Veritabanındaki kayıt alanında (sütun) bulunan ve belirttiğiniz koşullara uyan sayıları toplar", "ad": "veritabanını ya da listeyi oluşturan hücreler aralığı. Veritabanı ilgili verilerin listesidir!çift tırnak işareti içindeki sütun etiketi, ya da sütunun listedeki konumunu gösteren sayı!belirttiğiniz koşulları içeren hücreler aralığı. Aralık, sütun etiketi ile etiketin altında bulunan ve koşulu taşıyan bir hücreyi içerir"}, "DVAR": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Seçili veritabanı girdilerinden alınan örneğin varyansını tahmin eder", "ad": "veritabanını ya da listeyi oluşturan hücreler aralığı. Veritabanı ilgili verilerin listesidir!çift tırnak işareti içindeki sütun etiketi, ya da sütunun listedeki konumunu gösteren sayı!belirttiğiniz koşulları içeren hücreler aralığı. Aralık, sütun etiketi ile etiketin altında bulunan ve koşulu taşıyan bir hücreyi içerir"}, "DVARP": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Seçili veritabanı popülasyonunun varyansını hesaplar", "ad": "veritabanını ya da listeyi oluşturan hücreler aralığı. Veritabanı ilgili verilerin listesidir!çift tırnak işareti içindeki sütun etiketi, ya da sütunun listedeki konumunu gösteren sayı!belirttiğiniz koşulları içeren hücreler aralığı. Aralık, sütun etiketi ile etiketin altında bulunan ve koşulu taşıyan bir hücreyi içerir"}, "CHAR": {"a": "(sayı)", "d": "Bilgisayarınızın karakter kümesindeki kod numarasıyla belirtilen karakteri verir", "ad": "1-255 a<PERSON><PERSON><PERSON> kullanacağınız karakteri belirten sayı"}, "CLEAN": {"a": "(metin)", "d": "Metinden yazdırılamayan karakterleri kaldırır", "ad": "yazılamayan karakterleri kaldırmak istediğiniz çalışma sayfası bilgisi"}, "CODE": {"a": "(metin)", "d": "Bilgisayarınızın kullandığı karakter kümesinden, metin dizesindeki ilk karakter için sayısal bir kod verir", "ad": "ilk karakterinin kodunu istediğiniz metin"}, "CONCATENATE": {"a": "(metin1; [metin2]; ...)", "d": "<PERSON><PERSON> fazla metin dizesini bir metin dizesi halinde birle<PERSON>rir", "ad": "bir tek metin dizesinde birleştirilecek olan 1-255 arasındaki metin dizeleridir ve metin dizesi, sayı ya da tek hücre başvurusu olabilir"}, "CONCAT": {"a": "(metint1; ...)", "d": "<PERSON><PERSON> di<PERSON> oluşan listeyi veya aralığı birleştirir", "ad": "tek bir dizede birleştirilecek 1 ile 254 arasındaki metin dizesi veya aralık"}, "DOLLAR": {"a": "(sayı; [onluklar])", "d": "Bir sayıyı para biçimi kullanarak metne dönüştürür", "ad": "bir sayı, bir sayı içeren hücreye başvuru ya da hesaplama sonucu bir sayı olan formül!virgülden sonra sağda ondalık hanesinde kullanılan rakam sayısı. <PERSON><PERSON> gerektiği gibi yuvarlanır; atlanırsa, Rakamlar = 2"}, "EXACT": {"a": "(metin1; metin2)", "d": "İki metin dizesini ka<PERSON>şılaştırır ve tamamen aynıysalar DOĞRU, başka durumlarda YANLIŞ verir (büyük küçük harf duyarlı)", "ad": "ilk metin karakter dizesi!ikinci metin karakter dizesi"}, "FIND": {"a": "(bul_metin; metin_içinde; [ba<PERSON><PERSON><PERSON><PERSON>_sayısı])", "d": "Bir metin dizesini diğer bir metin dizesi içinde bulur ve bulunan dizenin başlama konumu numarasını verir (büyük küçük harfe duyarlı)", "ad": "bulmak istediğiniz metin. Metin_içindeki ilk karakteri eşleştirmek için çift tırnak (boş metin) kullanın; eşleştirme karakterleri kullanılamaz!bulmak istediğiniz metni içeren metin!aramanın başlayacağı karakteri belirtir. Metin_içindeki ilk karakterin numarası 1'dir. Atlanırsa Başl_num = 1"}, "FINDB": {"a": "(bul_metin; metin_içinde; [ba<PERSON><PERSON><PERSON><PERSON>_sayısı])", "d": "Bir metin dizesi içinde bir metin dizesi bulur ve ikinci metin dizesinin ilk karakterinden ilk metin dizesinin başlangıç konumuna ait sayıyı verir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir", "ad": "bulmak istediğiniz metin. Metin_içindeki ilk karakteri eşleştirmek için çift tırnak (boş metin) kullanın; eşleştirme karakterleri kullanılamaz!bulmak istediğiniz metni içeren metin!aramanın başlayacağı karakteri belirtir. Metin_içindeki ilk karakterin numarası 1'dir. Atlanırsa Başl_num = 1"}, "FIXED": {"a": "(sayı; [on<PERSON><PERSON>]; [virg<PERSON><PERSON>_yok])", "d": "<PERSON>ir sayıyı belirtilen sayıda ondalıklara yuvarlar ve sonucu virgüllü ya da virgülsüz metin olarak verir", "ad": "yuvar<PERSON><PERSON>p metin türüne döndürmek istediğiniz sayı!ondalık virgülün sağında kullanılan rakam sayısı. Atlanırsa, Ondalıklar = 2!bir mantıksal değer: gelen metinde virgülleri görüntüleme = DOĞRU; gelen metinde virgülleri görüntüle = YANLIŞ ya da atlanmış"}, "LEFT": {"a": "(metin; [say<PERSON>_ka<PERSON><PERSON><PERSON>])", "d": "Bir metin dizesinin ilk (en solundaki) belirtilen sayıdaki karakter ya da karakterlerini verir", "ad": "çıkartmak istediğiniz karakterleri içeren metin!SOLDAN fonksiyonunun çıkarmasını istediğiniz karakter sayısını belirtir; atlanırsa 1"}, "LEFTB": {"a": "(metin; [say<PERSON>_ka<PERSON><PERSON><PERSON>])", "d": "Belirteceğiniz bayt sayısına göre bir metin dizesindeki ilk karakteri veya karakterleri verir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir", "ad": "çıkartmak istediğiniz karakterleri içeren metin!SOLDAN fonksiyonunun çıkarmasını istediğiniz karakter sayısını belirtir; atlanırsa 1"}, "LEN": {"a": "(metin)", "d": "Bir karakter dizesi içerisindeki karakter sayısını verir", "ad": "karakter sayısını bulmak istediğiniz metin. Boşluklar karakter olarak sayılır"}, "LENB": {"a": "(metin)", "d": "Bir metin dizesinde karakterleri temsil etmek üzere kullanılan bayt sayısını verir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir", "ad": "karakter sayısını bulmak istediğiniz metin. Boşluklar karakter olarak sayılır"}, "LOWER": {"a": "(metin)", "d": "<PERSON>in dizesindeki tüm büyük harfleri küçük harfe çevirir", "ad": "küçük harfe dönüştürmek istediğiniz metin. Metindeki harf olmayan karakterler değiştirilmez"}, "MID": {"a": "(metin; ba<PERSON><PERSON><PERSON><PERSON>_sayısı; sayı_karak<PERSON><PERSON>)", "d": "Belirttiğiniz konumdan başlamak üzere metinden belirli sayıda karakter verir", "ad": "çıkartmak istediğiniz karakterlerin bulunduğu metin dizesi!çıkartmak istediğiniz ilk karakterin konumu. Metindeki ilk karakter 1'dir!Metinden kaç tane karakterin geleceğini belirtir"}, "MIDB": {"a": "(metin; ba<PERSON><PERSON><PERSON><PERSON>_sayısı; sayı_karak<PERSON><PERSON>)", "d": "Bir metin dizesinden, be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yerden başlayarak, be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bayt sayısına göre belirli sayıda karakteri verir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir", "ad": "çıkartmak istediğiniz karakterlerin bulunduğu metin dizesi!çıkartmak istediğiniz ilk karakterin konumu. Metindeki ilk karakter 1'dir!Metinden kaç tane karakterin geleceğini belirtir"}, "NUMBERVALUE": {"a": "(metin; [ondalık_ayırıcı]; [grup_ayırıcı])", "d": "<PERSON>ni yerel ba<PERSON><PERSON>ms<PERSON>z durumdaki sayıya dönüştürür", "ad": "dönüştürmek istediğiniz sayıyı temsil eden dize! dizedeki ondalık ayırıcı olarak kullanılan karakter!dizedeki grup ayırıcı olarak kullanılan karakter"}, "PROPER": {"a": "(metin)", "d": "<PERSON><PERSON> di<PERSON>i her sözcüğün ilk harfini büyük harfe, di<PERSON><PERSON> tüm harfleri de küçük harfe dönüştürür", "ad": "tırnak işareti içinde bir metin, metin veren bir <PERSON>, ya da kısmen büyük harf yapacağınız metin içeren bir hücreye başvuru"}, "REPLACE": {"a": "(eski_metin; ba<PERSON><PERSON><PERSON><PERSON>_sayısı; sayı_karak<PERSON><PERSON>; yeni_metin)", "d": "Metin dizesinin bir kısmını başka bir metin dizesiyle <PERSON>", "ad": "bazı karakterlerini değiştirmek istediğiniz metin!Yeni_metin ile değiştirmek istediğiniz Eski_metin'deki karakterin konumu!Eski_metin'de değiştirmek istediğiniz karakter sayısı!Eski_metin'deki karakterleri dönüştürecek olan metin"}, "REPLACEB": {"a": "(eski_metin; ba<PERSON><PERSON><PERSON><PERSON>_sayısı; sayı_karak<PERSON><PERSON>; yeni_metin)", "d": "Belirteceğiniz bayt say<PERSON> gö<PERSON>, bir metin dizesinin bir kısmını farklı bir metin dizesi ile değiştirir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir", "ad": "bazı karakterlerini değiştirmek istediğiniz metin!Yeni_metin ile değiştirmek istediğiniz Eski_metin'deki karakterin konumu!Eski_metin'de değiştirmek istediğiniz karakter sayısı!Eski_metin'deki karakterleri dönüştürecek olan metin"}, "REPT": {"a": "(metin; sayı_kere)", "d": "Bir metni verilen sayıda yineler. Hücreyi metin dizesindeki birçok örnekle doldurmak için YİNELE'yi kullanın", "ad": "yinelemek istediğiniz metin!metnin kaç kez yineleneceğini belirten sayı"}, "RIGHT": {"a": "(metin; [say<PERSON>_ka<PERSON><PERSON><PERSON>])", "d": "Bir metin dizesinin son (en sa<PERSON><PERSON><PERSON>) belirtilen sayıdaki karakter ya da karakterlerini verir", "ad": "çıkarmak istediğiniz karakterleri içeren metin dizesi!kaç tane karakter çıkarmak istediğinizi belirtir, atlanırsa 1"}, "RIGHTB": {"a": "(metin; [say<PERSON>_ka<PERSON><PERSON><PERSON>])", "d": "Belirteceğiniz bayt say<PERSON> g<PERSON>, bir metin dizesindeki son karakteri veya karakterleri verir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir", "ad": "çıkarmak istediğiniz karakterleri içeren metin dizesi!kaç tane karakter çıkarmak istediğinizi belirtir, atlanırsa 1"}, "SEARCH": {"a": "(bul_metin; metin; [ba<PERSON><PERSON><PERSON><PERSON>_sayısı])", "d": "Özel bir karakter ya da metin dizesinin ilk geçtiği yerin karakter numarasını verir, soldan sağa okuma sırasında (büyük küçük harfe duyarlı değil)", "ad": "bulmak istediğiniz metin. ? ve * eşleştirme karakterlerini kullanabilirsiniz; ? ve * karakterlerini bulmak için ~? ve ~* kullanın!Bul_metin'de kullanılan metin!aramanın başlayacağı Metin_içinde bulunan karakter sayısı (soldan sayıldığında). Atlanırsa, 1 kullanılır"}, "SEARCHB": {"a": "(bul_metin; metin; [ba<PERSON><PERSON><PERSON><PERSON>_sayısı])", "d": "Bir metin dizesi içinde bir metin dizesi bulur ve ikinci metin dizesinin ilk karakterinden ilk metin dizesinin başlangıç konumuna ait sayıyı verir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir", "ad": "bulmak istediğiniz metin. ? ve * eşleştirme karakterlerini kullanabilirsiniz; ? ve * karakterlerini bulmak için ~? ve ~* kullanın!Bul_metin'de kullanılan metin!aramanın başlayacağı Metin_içinde bulunan karakter sayısı (soldan sayıldığında). Atlanırsa, 1 kullanılır"}, "SUBSTITUTE": {"a": "(metin; eski_metin; yeni_metin; [y<PERSON><PERSON><PERSON>_sayıs<PERSON>])", "d": "<PERSON><PERSON> dizesindeki eski bir metni ye<PERSON>", "ad": "karakterlerini değiştirmek istediğiniz metni içeren hücre başvurusu veya metin!değiştirmek istediğiniz metin. Eski_metin'in büyük ve küçük harfleriyle eşleşmezse YERİNEKOY metni değiştirmez!Eski_metin ile değiştirmek istediğiniz metin!hangi Eski_metin örneğini değiştirmek istediğinizi belirtir. Atlanırsa, Eski_metin'in her örneği değiştirilir"}, "T": {"a": "(de<PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON>'in başvurduğu metni verir", "ad": "sınanmak istenen değer: <PERSON><PERSON><PERSON> bir metin ya da metin başvurusu ise T Değer verir; <PERSON><PERSON><PERSON> metin değilse T çift tırnak (boş metin) verir"}, "TEXT": {"a": "(de<PERSON><PERSON>; biç<PERSON>_metni)", "d": "Bir değeri belirli bir sayı biçimindeki metne dönüştürür", "ad": "bir sayı, sayısal değer veren bir formül veya sayısal değer içeren bir hücre başvurusu!Hücreleri Biçimlendir iletişim kutusunda bulunan Sayı sekmesindeki Kategori kutusundan alınan metin biçimindeki sayı biçimi"}, "TEXTJOIN": {"a": "(sı<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; bo<PERSON><PERSON><PERSON>_yoksay; metin1; ...)", "d": "Sınırlayıcı kullanarak metin dizelerinden oluşan bir listeyi veya aralığı birleştirir", "ad": "Her metin öğesinin arasına eklenecek karakter veya dize!DOĞRU(var<PERSON><PERSON><PERSON>) ise, boş hücreleri yoksayar!birleştirilecek 1 ile 252 arasındaki metin dizesi veya aralık"}, "TRIM": {"a": "(metin)", "d": "Bir metin dizesinden sözcükler arasındaki tek boşluklar dışındaki tüm boşlukları kaldırır", "ad": "boşluklarını kaldırmak istediğiniz metin"}, "UNICHAR": {"a": "(sayı)", "d": "Verilen sayısal değer tarafından başvurulan Unicode karakterini verir", "ad": "bir karakteri temsil eden Unicode sayısı"}, "UNICODE": {"a": "(metin)", "d": "Metnin ilk karakterine karşılık gelen sayıyı (kod noktası) döndürür", "ad": "Unicode değerini bulmak istediğiniz karakter"}, "UPPER": {"a": "(metin)", "d": "<PERSON>ir metni büyük harfe dönüştürür", "ad": "büyük harfe dönüştürmek istediğiniz metin, bir başvuru ya da bir metin dizesi"}, "VALUE": {"a": "(metin)", "d": "Bir sayıyı gösteren bir metin dizesini bir sayıya dönüştürür", "ad": "dönüştürmek istediğiniz metni içeren hücre başvurusu veya tırnak işaretiyle kapalı metin"}, "AVEDEV": {"a": "(sayı1; [sayı2]; ...)", "d": "Veri noktalarının mutlak sapmalarının aritmetik ortalamasını bu noktaların ortalaması aracılığıyla verir. Bağımsız değişkenler sayı, ad, dizi veya sayı içeren başvurular olabilir", "ad": "Mutlak sapmalarının ortalamasını elde etmek istediğiniz en az 1 en fazla 255 bağımsız değişkendir"}, "AVERAGE": {"a": "(sayı1; [sayı2]; ...)", "d": "Bağıms<PERSON>z <PERSON> (aritmetik) ortalamasını verir, bunlar sayı ya da sayılar içeren ad, dizi veya başvurular olabilir", "ad": "ortalamasını bulmak istediğiniz en az 1 en fazla 255 bağımsız sayısal değişkendir"}, "AVERAGEA": {"a": "(değer1; [değer2]; ...)", "d": "Bağımsız değişkenlerinin aritmetik ortalamasını verir, metni ve bağımsız değişkenlerdeki YANLIŞ değerini 0; DOĞRU değerini 1 olarak hesaplar. Bağımsız değişkenler sayı, ad, dizi ya da başvuru olabilir", "ad": "Aritmetik ortalamasını bulmak istediğiniz en az 1 en fazla 255 bağımsız değişkendir"}, "AVERAGEIF": {"a": "(aralık; ölçüt; [aralık_ortalaması])", "d": "Verili bir koşul veya ölçüt tarafından belirtilen hücrelerin ortalamasını (aritmetik ortalama) bulur", "ad": "hesaplanmasını istediğiniz hücre aralığı!ortalamayı bulmak için kullanılacak hücreleri tanımlayan, sayı, ifade veya metin biçimindeki koşul veya ölçüt!ortalamayı bulmak için kullanılacak asıl hücreler. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aralıktaki hücreler kullanılır"}, "AVERAGEIFS": {"a": "(aralık_ortalaması; ölçüt_aralığı; ölçüt; ...)", "d": "Verili bir koşul veya ölçüt kümesi tarafından belirtilen hücrelerin ortalamasını (aritmetik ortalama) bulur", "ad": "ortalamayı bulmak için kullanılacak asıl hücreler.!belirli bir koşula göre hesaplanmasını istediğiniz hücre aralığı!ortalamayı bulmak için kullanılacak hücreleri tanımlayan, sayı, ifade veya metin biçimindeki koşul veya ölçüt"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Kümülatif beta olasılık yoğunluk işlevini verir", "ad": "işlevin değerini hesaplarken kullanılan A ile B arasındaki değer!dağılım parametresi, 0'dan büyük olmalıdır!dağılım parametresi, 0'dan büyük olmalıdır!x aralığı için isteğe bağlı alt sınır. Atlanırsa, A = 0!x aralığı için isteğe bağlı üst sınır. Atlanırsa, B = 1"}, "BETAINV": {"a": "(olasılık; alfa; beta; [A]; [B])", "d": "Kümülatif beta olasılık yoğunluk işlevinin (BETADAĞ) tersini verir", "ad": "beta dağılımıyla ilgili olasılık!dağılım parametresi, 0'dan büyük olmalıdır!dağılım parametresi, 0'dan büyük olmalıdır!x aralığı için isteğe bağlı alt sınır. Atlanırsa, A = 0!x aralığı için isteğe bağlı üst sınır. Atlanırsa, B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; kümülatif; [A]; [B])", "d": "Beta olasılık dağılımı işlevini verir", "ad": "işlevin değerini hesaplarken kullanılan A ile B arasındaki değer!dağılım parametresi, 0'dan büyük olmalıdır!dağılım parametresi, 0'dan büyük olmalıdır!mantıksal değer: kümülatif dağılım işlevi için DOĞRU'yu; olasılık yoğunluk işlevi için YANLIŞ'ı kullanın!x aralığı için isteğe bağlı alt sınır. Atlanırsa, A = 0!x aralığı için isteğe bağlı üst sınır. Atlanırsa, B = 1"}, "BETA.INV": {"a": "(olasılık; alfa; beta; [A]; [B])", "d": "Kümülatif beta olasılık yoğunluk işlevinin (BETA.DAĞ) tersini verir", "ad": "beta dağılımıyla ilgili olasılık!dağılım parametresi, 0'dan büyük olmalıdır!dağılım parametresi, 0'dan büyük olmalıdır!x aralığı için isteğe bağlı alt sınır. Atlanırsa, A = 0!x aralığı için isteğe bağlı üst sınır. Atlanırsa, B = 1"}, "BINOMDIST": {"a": "(başar<PERSON>_sayısı; den<PERSON><PERSON><PERSON>; başar<PERSON>_olasılığı; kümülatif)", "d": "Tek terimli binom dağılımı olasılığını verir", "ad": "denemelerdeki başarı sayısı!bağımsız denemeler sayısı!her denemedeki başarı olasılığı!mantıksal değer: kümülatif dağılım fonksiyonu için DOĞRU'yu; olasılık kütle fonksiyonu için YANLIŞ'ı kullanın"}, "BINOM.DIST": {"a": "(başar<PERSON>_sayısı; den<PERSON><PERSON><PERSON>; başar<PERSON>_olasılığı; kümülatif)", "d": "Tek terimli binom dağılımı olasılığını verir", "ad": "denemelerdeki başarı sayısı!bağımsız denemeler sayısı!her denemedeki başarı olasılığı!mantıksal değer: kümülatif dağılım fonksiyonu için DOĞRU'yu; olasılık kütle fonksiyonu için YANLIŞ'ı kullanın"}, "BINOM.DIST.RANGE": {"a": "(den<PERSON><PERSON><PERSON>; başarı_olasılığı; başarı_sayısı; [başarı_sayısı2])", "d": "Binom dağılımını kullanarak bir deneme sonucunun başarı olasılığını döndürür", "ad": "bağı<PERSON><PERSON>z denemelerin sayısı!her bir denemedeki başarı olasılığı!denemelerdeki başarı sayısı!varsa, bu fonksiyon başarılı denemelerin başarı_sayısı ve başarı_sayısı2 arasında yer alma olasılığını döndürür"}, "BINOM.INV": {"a": "(den<PERSON><PERSON><PERSON>; başarı_olasılığı; alfa)", "d": "Kümülatif binom dağılımının ölçüt değerinden küçük veya ona eşit olduğu en küçük değeri verir", "ad": "Bernoulli denemeler sayısı!her denemedeki başarı olasılığı, 0 ile 1 arasında (bunlar dahil) bir sayı!ölçüt değeri, 0 ile 1 arasında (bunlar dahil) bir sayı"}, "CHIDIST": {"a": "(x; serb_derecesi)", "d": "<PERSON><PERSON><PERSON>ılımının sağ kuyruklu olasılığını verir", "ad": "dağ<PERSON>lımı hesaplamak istediğ<PERSON>, negatif o<PERSON>yan bir sayı!serbestlik derecesi sayısı; 1 ile 10^10 arasında (10^10 hariç) bir sayı"}, "CHIINV": {"a": "(olasıl<PERSON>k; serb_derecesi)", "d": "<PERSON><PERSON><PERSON>ılımının sağ kuyruklu olasılığının tersini verir", "ad": "kikare dağılımı ile ilgili olasılık; 0 ile 1 arasında (bunlar dahil) bir değer!serbestlik derecesi sayısı; 1 ile 10^10 arasında (10^10 hariç) bir sayı"}, "CHITEST": {"a": "(etkin_erim; beklenen_erim)", "d": "Bağımsızlık sınaması sonucunu verir: istatistik için kikare dağılımından alınan değer ve uygun serbestlik derecesi", "ad": "beklenen değerler karşısında sınanacak gözlemleri içeren veri aralığı!satır toplamları ile sütun toplamları çarpımının büyük toplama oranını içeren veri aralığı"}, "CHISQ.DIST": {"a": "(x; serb_derecesi; kümülatif)", "d": "<PERSON><PERSON><PERSON>ımının sol kuyruklu olasılığını verir", "ad": "dağılımı hesaplamak istediğ<PERSON>z <PERSON>, negatif olmayan bir sayı!serbestlik derecesi sayısı; 1 ile 10^10 arasında (10^10 hariç) bir sayı!işlevin vereceği mantıksal değer: kümülatif dağılım fonksiyonu = DOĞRU; olasılık yoğunluğu fonksiyonu = YANLIŞ"}, "CHISQ.DIST.RT": {"a": "(x; serb_derecesi)", "d": "<PERSON><PERSON><PERSON>ılımının sağ kuyruklu olasılığını verir", "ad": "dağ<PERSON>lımı hesaplamak istediğ<PERSON>, negatif o<PERSON>yan bir sayı!serbestlik derecesi sayısı; 1 ile 10^10 arasında (10^10 hariç) bir sayı"}, "CHISQ.INV": {"a": "(olasıl<PERSON>k; serb_derecesi)", "d": "<PERSON><PERSON><PERSON>ımının sol kuyruklu olasılığının tersini verir", "ad": "kikare dağılımı ile ilişkili olasılık; 0 ile 1 arasında (bunlar dahil) bir değer!serbestlik derecesi sayısı; 1 ile 10^10 arasında (10^10 hariç) bir sayı"}, "CHISQ.INV.RT": {"a": "(olasıl<PERSON>k; serb_derecesi)", "d": "<PERSON><PERSON><PERSON>ılımının sağ kuyruklu olasılığının tersini verir", "ad": "kikare dağılımı ile ilgili olasılık; 0 ile 1 arasında (bunlar dahil) bir değer!serbestlik derecesi sayısı; 1 ile 10^10 arasında (10^10 hariç) bir sayı"}, "CHISQ.TEST": {"a": "(etkin_erim; beklenen_erim)", "d": "Bağımsızlık sınaması sonucunu verir: istatistik için kikare dağılımından alınan değer ve uygun serbestlik derecesi", "ad": "beklenen değerler karşısında sınanacak gözlemleri içeren veri aralığı!satır toplamları ile sütun toplamları çarpımının büyük toplama oranını içeren veri aralığı"}, "CONFIDENCE": {"a": "(alfa; standart_sapma; boyut)", "d": "Popülasyon ortalaması için normal bir dağılım kullanarak güvenilirlik aralığını verir", "ad": "güvenirlik düzeyini hesaplamak için kullanılan anlamlılık düzeyi; 0'dan büyük 1'den küçük bir sayı!veri aralığının bilindiği varsayılan popülasyon standart sapması. Standart_sap 0'dan büyük olmalıdır!örnek boyutu"}, "CONFIDENCE.NORM": {"a": "(alfa; standart_sapma; boyut)", "d": "Popülasyon ortalaması için normal bir dağılım kullanarak güvenilirlik aralığını verir", "ad": "güvenirlik düzeyini hesaplamak için kullanılan anlamlılık düzeyi; 0'dan büyük 1'den küçük bir sayı!veri aralığının bilindiği varsayılan popülasyon standart sapması. Standart_sap 0'dan büyük olmalıdır!örnek boyutu"}, "CONFIDENCE.T": {"a": "(alfa; standart_sapma; boyut)", "d": "Popülasyon ortalaması için bir T-dağılımı kullanarak güvenilirlik aralığını verir", "ad": "güvenirlik düzeyini hesaplamak için kullanılan anlamlılık düzeyi; 0'dan büyük 1'den küçük bir sayı!veri aralığının bilindiği varsayılan popülasyon standart sapması. Standart_sap 0'dan büyük olmalıdır!örnek boyutu"}, "CORREL": {"a": "(dizi1; dizi2)", "d": "İki veri kümesi arasındaki korelasyon katsayısını verir", "ad": "değer içeren hücre aralığı. <PERSON><PERSON><PERSON><PERSON> sayı, ad, dizi ya da sayı içeren başvurular olmalıdır!ikinci bir değer içeren hücre aralığı. <PERSON><PERSON><PERSON><PERSON> sayı, ad, dizi ya da sayı içeren başvurular olmalıdır"}, "COUNT": {"a": "(değer1; [değer2]; ...)", "d": "Aralıktaki sayı içeren hücrelerin kaç tane olduğunu sayar", "ad": "en az 1 en fazla 255 ba<PERSON><PERSON><PERSON><PERSON><PERSON>dir, her biri farklı türdeki çeşitli verileri taşıyabilir ya da bunlara baş<PERSON>rab<PERSON>r, fakat yaln<PERSON><PERSON><PERSON> sayılar dikkate alınır"}, "COUNTA": {"a": "(değer1; [değer2]; ...)", "d": "Aralıktaki bo<PERSON> o<PERSON>yan hücre<PERSON>in kaç tane olduğunu sayar", "ad": "Saymak istediğiniz değerleri ve hücreleri temsil eden en az 1 en fazla 255 bağımsız değişkendir. Değerler herhangi bir bilgi türünde olabilir"}, "COUNTBLANK": {"a": "(aralık)", "d": "Belirtilen hücre aralığındaki boş hücreleri sayar", "ad": "boş hücrelerin sayısını öğrenmek istediğiniz aralık"}, "COUNTIF": {"a": "(aralık; ölçüt)", "d": "Verilen koşula uyan aralık içindeki hücreleri sayar", "ad": "boş olmayan hücrelerin sayısını öğrenmek istediğiniz aralık!hangi hücrelerin sayılacağını tanımlayan sayı, ifade veya metin biçimindeki koşul"}, "COUNTIFS": {"a": "(ölçüt_aralığı; ölçüt; ...)", "d": "Verili bir koşul veya ölçüt kümesi tarafından belirtilen hücreleri sayar", "ad": "belirli bir koşula göre hesaplanmasını istediğiniz hücre aralığı!sayılacak hücreleri tanımlayan, sayı, ifade veya metin biçimindeki koşul"}, "COVAR": {"a": "(dizi1; dizi2)", "d": "Kovaryansı verir; iki veri kümes<PERSON>eki her veri noktası çifti için sapmaların çarpımlarının ortalaması", "ad": "ilk tamsayılar hücre aralığıdır ve sayı, ad dizi, ya da sayı içeren başvuru olmalıdır!ikinci tamsayılar hücre aralığıdır ve sayı, ad dizi, ya da sayı içeren başvuru olmalıdır"}, "COVARIANCE.P": {"a": "(dizi1; dizi2)", "d": "Popülasyon kovaryansını verir; iki veri kümesindeki her veri noktası çifti için sapmaların çarpımlarının ortalaması", "ad": "ilk tamsayılar hücre aralığıdır ve sayı, ad dizi, ya da sayı içeren başvuru olmalıdır!ikinci tamsayılar hücre aralığıdır ve sayı, ad dizi, ya da sayı içeren başvuru olmalıdır"}, "COVARIANCE.S": {"a": "(dizi1; dizi2)", "d": "Örnek kovaryansı verir; iki veri kümesindeki her veri noktası çifti için sapmaların çarpımlarının ortalaması", "ad": "ilk tamsayılar hücre aralığıdır ve sayı, ad dizi, ya da sayı içeren başvuru olmalıdır!ikinci tamsayılar hücre aralığıdır ve sayı, ad dizi, ya da sayı içeren başvuru olmalıdır"}, "CRITBINOM": {"a": "(den<PERSON><PERSON><PERSON>; başarı_olasılığı; alfa)", "d": "Kümülatif binom dağılımının ölçüt değerinden küçük veya ona eşit olduğu en küçük değeri verir", "ad": "Bernoulli denemeler sayısı!her denemedeki başarı olasılığı, 0 ile 1 arasında (bunlar dahil) bir sayı!ölçüt değeri, 0 ile 1 arasında (bunlar dahil) bir sayı"}, "DEVSQ": {"a": "(sayı1; [sayı2]; ...)", "d": "Veri noktalarının kendi örneklerinin ortalamasından sapmaların kareleri toplamını verir", "ad": "SAPKARE'sini hesaplamak istediğiniz En az 1 en fazla 255 bağıms<PERSON>z <PERSON>ğ<PERSON>, ya da bir dizi veya dizi başvurusudur"}, "EXPONDIST": {"a": "(x; lambda; kümülatif)", "d": "Üstel dağılımı verir", "ad": "i<PERSON><PERSON><PERSON> de<PERSON>, negatif olmayan bir sayı!parametre değeri, pozitif bir sayı!işlevin vereceği mantıksal değer: kümülatif dağılım fonksiyonu = DOĞRU; olasılık yoğunluğu fonksiyonu = YANLIŞ"}, "EXPON.DIST": {"a": "(x; lambda; kümülatif)", "d": "Üstel dağılımı verir", "ad": "i<PERSON><PERSON><PERSON> de<PERSON>, negatif olmayan bir sayı!parametre değeri, pozitif bir sayı!işlevin vereceği mantıksal değer: kümülatif dağılım işlevi = DOĞRU; olasılık yoğunluğu işlevi = YANLIŞ"}, "FDIST": {"a": "(x; serb_derecesi1; serb_derecesi2)", "d": "İki veri kümesi için (sağ kuyruklu) F olasılık dağılımını (basıklık derecesi) verir", "ad": "<PERSON><PERSON><PERSON><PERSON>, negatif o<PERSON>yan bir sayı!serbestlik derecesi payında olan sayı, 1 ile 10^10 arasında (10^10 hariç) bir sayı!serbestlik derecesi paydası, 1 ile 10^10 arasında (10^10 hariç) bir sayı"}, "FINV": {"a": "(olas<PERSON>l<PERSON>k; serb_derecesi1; serb_derecesi2)", "d": "(<PERSON>ğ kuyruk<PERSON>) F olasılık dağılımının tersini verir: p = FDAĞ(x,...) ise, FTERS(p,...) = x", "ad": "F kümülatif dağılımı ile ilgili olasılık; 0 ile 1 arasında (bunlar dahil) bir sayı!serbestlik derecesi payında olan sayı, 1 ile 10^10 arasında (10^10 hariç) bir sayı!serbestlik derecesi paydası, 1 ile 10^10 arasında (10^10 hariç) bir sayı"}, "FTEST": {"a": "(dizi1; dizi2)", "d": "Bir F-test sonucu verir; Dizi1 ve Dizi2'nin vary<PERSON>ının çok farklı olmadığı iki kuyruklu olasılıktır", "ad": "ilk veri aralığı ya da dizisidir ve sayı, ad, dizi, ya da sayı içeren başvuru olabilir (boşluklar yoksayılır)!ikinci veri aralığı ya da dizisidir ve sayı, ad, dizi, ya da sayı içeren başvuru olabilir (boşluklar yoksayılır)"}, "F.DIST": {"a": "(x; serb_derecesi1; serb_derecesi2; kümülatif)", "d": "İki veri kümesi için (sol kuyruklu) F olasılık dağılımını (basıklık derecesi) verir", "ad": "işlevin he<PERSON>, negatif olmayan bir sayı!serbestlik derecesi payında olan sayı, 1 ile 10^10 arasında (10^10 hariç) bir sayı!serbestlik derecesi paydası, 1 ile 10^10 arasında (10^10 hariç) bir sayı!işlevin vereceği mantıksal değer: kümülatif dağılım fonksiyonu = DOĞRU; olasılık yoğunluğu fonksiyonu = YANLIŞ"}, "F.DIST.RT": {"a": "(x; serb_derecesi1; serb_derecesi2)", "d": "İki veri kümesi için (sağ kuyruklu) F olasılık dağılımını (basıklık derecesi) verir", "ad": "<PERSON><PERSON><PERSON><PERSON>, negatif o<PERSON>yan bir sayı!serbestlik derecesi payında olan sayı, 1 ile 10^10 arasında (10^10 hariç) bir sayı!serbestlik derecesi paydası, 1 ile 10^10 arasında (10^10 hariç) bir sayı"}, "F.INV": {"a": "(olas<PERSON>l<PERSON>k; serb_derecesi1; serb_derecesi2)", "d": "(Sol kuyruklu) F olasılık dağılımının tersini verir: p = F.DAĞ(x,...) ise, F.TERS(p,...) = x", "ad": "F kümülatif dağılımı ile ilgili olasılık; 0 ile 1 arasında (bunlar dahil) bir sayı!serbestlik derecesi payında olan sayı, 1 ile 10^10 arasında (10^10 hariç) bir sayı!serbestlik derecesi paydası, 1 ile 10^10 arasında (10^10 hariç) bir sayı"}, "F.INV.RT": {"a": "(olas<PERSON>l<PERSON>k; serb_derecesi1; serb_derecesi2)", "d": "(<PERSON>ğ kuyruk<PERSON>) F olasılık dağılımının tersini verir: p = F.DAĞ.SAĞK(x,...) ise, F.TERS.SAĞK(p,...) = x", "ad": "F kümülatif dağılımı ile ilgili olasılık; 0 ile 1 arasında (bunlar dahil) bir sayı!serbestlik derecesi payında olan sayı, 1 ile 10^10 arasında (10^10 hariç) bir sayı!serbestlik derecesi paydası, 1 ile 10^10 arasında (10^10 hariç) bir sayı"}, "F.TEST": {"a": "(dizi1; dizi2)", "d": "Bir F-test sonucu verir; Dizi1 ve Dizi2'nin vary<PERSON>ının çok farklı olmadığı iki kuyruklu olasılıktır", "ad": "ilk veri aralığı ya da dizisidir ve sayı, ad, dizi, ya da sayı içeren başvuru olabilir (boşluklar yoksayılır)!ikinci veri aralığı ya da dizisidir ve sayı, ad, dizi, ya da sayı içeren başvuru olabilir (boşluklar yoksayılır)"}, "FISHER": {"a": "(x)", "d": "<PERSON> dö<PERSON>üşü<PERSON><PERSON><PERSON><PERSON> verir", "ad": "dönüşümünü istediğiniz değer, -1 ile 1 arasında (-1 ve 1 hariç) bir sayı"}, "FISHERINV": {"a": "(y)", "d": "Fisher dönüşü<PERSON>ünün tersini verir: y = FISHER(x) ise, FISHERTERS(y) = x", "ad": "dönüşümün tersini almak istediğiniz de<PERSON>"}, "FORECAST": {"a": "(x; bilinen_y'ler; bilinen_x'ler)", "d": "Varolan değerleri kullanarak bir gelecek değeri doğrusal bir eğilim boyunca hesaplar ya da tahmin eder", "ad": "bir değer tahmin edilecek veri noktasıdır ve sayısal bir değer olmalıdır!bağımlı sayısal veri dizisi ya da aralığıdır!bağımsız sayısal veri dizisi ya da aralığıdır. Bilinen_x'lerin varyansı sıfır olmamalıdır"}, "FORECAST.ETS": {"a": "(hede<PERSON>_tari<PERSON>; <PERSON><PERSON><PERSON><PERSON>; z<PERSON>_<PERSON>; [mev<PERSON><PERSON><PERSON><PERSON>]; [veri_tamamlama]; [toplama])", "d": "Üstel düzeltme yöntemini kullanarak belirtilen hedef gelecek tarihi için tahmin edilen bir değer verir.", "ad": "Spreadsheet Editor uygulamasının bir değer tahmin ettiği veri noktasıdır. Zaman çizelgesindeki değer desenlerini sürdürmesi gerekir.!tahmin ettiğiniz sayısal veri aralığı veya dizisidir.!bağımsız sayısal veri aralığı veya dizisidir. Zaman çizelgesindeki tarihler arasında tutarlı bir adım olmalıdır ve sıfır olamaz.!mevsimsel desenin uzunluğunu gösteren isteğe bağlı sayısal bir değerdir. Varsayılan değer olan 1, mevsimselliğin otomatik olarak algılandığını gösterir.!eksik değerleri işlemek için kullanılan isteğe bağlı bir değerdir. Varsayılan değer olan 1 eksik değerleri ilişkilendirme yoluyla değiştirir ve 0 da bunları sıfırlarla değiştirir.!aynı zaman damgasına sahip birden fazla değeri toplamak için kullanılan isteğe bağlı bir sayısal değerdir. Boş bırakılırsa Spreadsheet Editor değerlerin ortalamasını verir."}, "FORECAST.ETS.CONFINT": {"a": "(hede<PERSON>_tari<PERSON>; <PERSON><PERSON><PERSON><PERSON>; z<PERSON>_<PERSON>; [g<PERSON><PERSON>_d<PERSON><PERSON>]; [mev<PERSON><PERSON><PERSON><PERSON>]; [veri_tamamlama]; [toplama])", "d": "Belirtilen hedef tari<PERSON>eki tahmin de<PERSON> için bir güvenilirlik aralığını verir.", "ad": "Spreadsheet Editor uygulamasının bir değer tahmin ettiği veri noktasıdır. Zaman çizelgesindeki değer desenlerini sürdürmesi gerekir.!tahmin ettiğiniz sayısal veri aralığı veya dizisidir.!bağımsız sayısal veri aralığı veya dizisidir. Zaman çizelgesindeki tarihler arasında tutarlı bir adım olmalıdır ve sıfır olamaz.!hesaplanan güven aralığının güven düzeyini gösteren 0 ve 1 arasında bir sayıdır.Varsayılan değer .95'tir.!mevsimsel desenin uzunluğunu gösteren isteğe bağlı sayısal bir değerdir. Varsayılan değer olan 1, mevsimselliğin otomatik olarak algılandığını gösterir.!eksik değerleri işlemek için kullanılan isteğe bağlı bir değerdir. Varsayılan değer olan 1 eksik değerleri ilişkilendirme yoluyla değiştirir ve 0 da bunları sıfırlarla değiştirir.!aynı zaman damgasına sahip birden fazla değeri toplamak için kullanılan isteğe bağlı bir sayısal değerdir. Boş bırakılırsa Spreadsheet Editor değerlerin ortalamasını verir."}, "FORECAST.ETS.SEASONALITY": {"a": "(<PERSON><PERSON><PERSON><PERSON>; z<PERSON>_<PERSON>; [veri_tamamlama]; [toplama])", "d": "Belirtilen zaman dizisi için uygulama tarafından algılanan tekrarlanan desenin uzunluğunu verir.", "ad": "tahmin ettiğ<PERSON>z sayısal veri aralığı veya dizisidir.!bağımsız sayısal veri aralığı veya dizisidir. Zaman çizelgesindeki tarihler arasında tutarlı bir adım olmalıdır ve sıfır olamaz.!eksik değerleri işlemek için isteğe bağlı bir değer.Varsayılan değer olan 1 eksik değerleri ilişkilendirme yoluyla değiştirir ve 0 da bunları sıfırlarla değiştirir.!aynı zaman damgasına sahip birden fazla değeri toplamak için kullanılan isteğe bağlı bir sayısal değerdir. Boş bırakılırsa Spreadsheet Editor değerlerin ortalamasını verir."}, "FORECAST.ETS.STAT": {"a": "(<PERSON><PERSON><PERSON><PERSON>; z<PERSON>_<PERSON>; istatistik_türü; [mevsi<PERSON><PERSON>k]; [veri_tamamlama]; [toplama])", "d": "<PERSON><PERSON><PERSON> i<PERSON><PERSON> istenen istatistiği döndürür.", "ad": "tahmin ettiğ<PERSON>z sayısal veri aralığı veya dizisidir.!bağımsız sayısal veri aralığı veya dizisidir. Zaman çizelgesindeki tarihler arasında tutarlı bir adım olmalıdır ve sıfır olamaz.!Spreadsheet Editor uygulamasının hesaplanan tahmin için vereceği istatistiği gösteren 1 ve 8 arasında bir sayıdır. !mevsimsel desenin uzunluğunu gösteren isteğe bağlı sayısal bir değerdir. Varsayılan değer olan 1, mevsimselliğin otomatik olarak algılandığını gösterir.!eksik değerleri işlemek için kullanılan isteğe bağlı bir değerdir. Varsayılan değer olan 1 eksik değerleri ilişkilendirme yoluyla değiştirir ve 0 da bunları sıfırlarla değiştirir.!aynı zaman damgasına sahip birden fazla değeri toplamak için kullanılan isteğe bağlı bir sayısal değerdir. Boş bırakılırsa Spreadsheet Editor değerlerin ortalamasını verir."}, "FORECAST.LINEAR": {"a": "(x; bilinen_y'ler; bilinen_x'ler)", "d": "Varolan değerleri kullanarak bir gelecek değeri doğrusal bir eğilim boyunca hesaplar ya da tahmin eder", "ad": "bir değer tahmin edilecek veri noktasıdır ve sayısal bir değer olmalıdır!bağımlı sayısal veri dizisi ya da aralığıdır!bağımsız sayısal veri dizisi ya da aralığı. Bilinen_x'lerin varyansı sıfır olmamalıdır"}, "FREQUENCY": {"a": "(veri_dizisi; b<PERSON><PERSON><PERSON>_dizisi)", "d": "Bir değerler aralığındaki değerlerin hangi sıklıkta yer aldığını hesaplar ve bölme_dizisi'nden bir fazla elemana sahip olan bir dikey sayı dizisi verir", "ad": "sıklıkları hesaplamak istediğiniz bir değerler kümesi dizisi veya başvurusu (boş<PERSON> ve metin yoksayılır)!veri_dizisi'ndeki değerleri gruplayacağınız aralıklar dizisi veya başvurusu"}, "GAMMA": {"a": "(x)", "d": "Gama işlevi değerini verir", "ad": " Gama değerini he<PERSON>lamak istediğ<PERSON>z <PERSON>"}, "GAMMADIST": {"a": "(x; alfa; beta; kümülatif)", "d": "Gama dağılımını verir", "ad": "dağılımı hesaplamak istediğ<PERSON>z <PERSON>, negatif olmayan bir sayı!dağılım parametresi, pozitif bir sayı!dağılım parametresi, pozitif bir sayı. beta = 1 ise, GAMADAĞ standart gama dağılımını verir!mantıksal değer: kümülatif dağılım fonksiyonu = DOĞRU; olasılık kütle fonksiyonu = YANLIŞ ya da atlanmış"}, "GAMMA.DIST": {"a": "(x; alfa; beta; kümülatif)", "d": "Gama dağılımını verir", "ad": "dağılımı hesaplamak istediğ<PERSON>z <PERSON>, negatif olmayan bir sayı!dağılım parametresi, pozitif bir sayı!dağılım parametresi, pozitif bir sayı. beta = 1 ise, GAMA.DAĞ standart gama dağılımını verir!mantıksal değer: kümülatif dağılım işlevi = DOĞRU; olasılık kütle işlevi = YANLIŞ ya da atlanmış"}, "GAMMAINV": {"a": "(olasılık; alfa; beta)", "d": "Gama kümülatif <PERSON>mının tersini verir: p = GAMADAĞ(x,...) ise, GAMATERS(p,...) = x", "ad": "gama dağılımıyla ilgili olasılık, 0 ile 1 arasında (bunlar dahil) bir sayı!dağılım parametresi, pozitif bir sayı!dağılım parametresi, pozitif bir sayı. beta = 1 ise, GAMATERS standart gama dağılımının tersini verir"}, "GAMMA.INV": {"a": "(olasılık; alfa; beta)", "d": "Gama kümülatif <PERSON>ımının tersini verir: p = GAMA.DAĞ(x,...) ise, GAMA.TERS(p,...) = x", "ad": "gama dağılımıyla ilgili olasılık, 0 ile 1 arasında (bunlar dahil) bir sayı!dağılım parametresi, pozitif bir sayı!dağılım parametresi, pozitif bir sayı. beta = 1 ise, GAMA.TERS standart gama dağılımının tersini verir"}, "GAMMALN": {"a": "(x)", "d": "Gama fonksiyonunun doğal logaritmasını verir", "ad": "GAMALN'ini hesa<PERSON>ak isted<PERSON><PERSON><PERSON>, pozitif bir sayı"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Gama işlevinin doğal logaritmasını döndürür", "ad": "GAMALN.DUYARLI işlevini hesaplamak istediğiniz <PERSON>, pozitif bir sayı"}, "GAUSS": {"a": "(x)", "d": "Standart normal kümülatif dağılımdan 0,5 daha azını verir", "ad": "dağılımını hesaplamak istediğiniz de<PERSON>"}, "GEOMEAN": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir dizi ya da pozitif sayısal veri aralığının geometrik ortalamasını verir", "ad": "ortalamasını bulmak istediğiniz en az 1 en fazla 255 sayı, ad, dizi, ya da sayı içeren başvurudur"}, "GROWTH": {"a": "(bilinen_y'ler; [bilinen_x'ler]; [yeni_x'ler]; [sabit])", "d": "Bilinen veri noktalarıyla eşleşen üstel büyüme trendi içindeki sayıları döndürür", "ad": "y=b*m^x denkleminde kullanılan y değ<PERSON><PERSON>i kümesi, bir dizi ya da pozitif sayılar aralığı!y=b*m^x denkleminde kullanılan isteğe bağlı x de<PERSON><PERSON><PERSON>i, bir dizi ya da Bilinen_y'lerle aynı boyuttaki aralık!ilişkili y değerlerini BÜYÜME ile bulacağınız yeni x değerleri!mantıksal değer: Sabit = DOĞRU ise sabit b değeri olağan şekilde hesaplanır; Sabit = YANLIŞ ya da atlanmış ise b 1'e eşitlenir"}, "HARMEAN": {"a": "(sayı1; [sayı2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> olu<PERSON>an bir veri kümesinin harmonik ortalamasını verir: devrik de<PERSON>lerin aritmetik ortalamasının devrik <PERSON>", "ad": "harmonik ortalamasını bulmak istediğiniz en az 1 en fazla 255 sayı, ad, dizi, ya da sayı içeren başvurudur"}, "HYPGEOM.DIST": {"a": "(başar<PERSON>_örnek; örnek_sayısı; başarı_popülasyon; pop_sayısı; kümülatif)", "d": "Hipergeometrik dağılımı verir", "ad": "örnek içindeki başarı sayısı!örnek boyutu!popülasyondaki başarı sayısı!popülasyon boyutu!mantıksal değer: kümülatif dağılım fonksiyonu için DOĞRU'yu; olasılık yoğunluk fonksiyonu için YANLIŞ'ı kullanın"}, "HYPGEOMDIST": {"a": "(başar<PERSON>_örnek; sayı_örnek; başar<PERSON>_popülasyon; sayı_pop)", "d": "Hipergeometrik dağılımı verir", "ad": "örnek içindeki başarı sayısı!örnek boyutu!popülasyondaki başarı sayısı!popülasyon boyutu"}, "INTERCEPT": {"a": "(bilinen_y'ler; bilinen_x'ler)", "d": "Bilinen x ve y-değerleri üzerindeki en uygun regresyon çizgisini kullanarak bir çizginin y-eksenini kestiği noktaları hesaplar", "ad": "bağımlı veri ya da gözlemler kümesidir ve sayı, ad, dizi ya da sayı içeren başvuru olabilir!bağımsız veri ya da gözlemler kümesidir ve sayı, ad, dizi ya da sayı içeren başvuru olabilir"}, "KURT": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir veri kümesinin basıklığını verir", "ad": "basıklığını bulmak istediğiniz en az 1 en fazla 255 sayı, ad, dizi ya da sayı içeren başvurudur"}, "LARGE": {"a": "(dizi; k)", "d": "Bir veri kümesi içindeki en büyük k. değeri verir. <PERSON><PERSON><PERSON><PERSON>, beşinci en büyük sayı", "ad": "k. en büyük değeri belirlemek için kullanılan veri dizisi veya aralığı!gelecek olan değerin bulunduğu hücre aralığı veya dizideki konumu (en büyük değerden)"}, "LINEST": {"a": "(bilinen_y'ler; [bilinen_x'ler]; [sabit]; [konum])", "d": "En küçük kareler yöntemiyle hesaplanmış olan verilerinize en iyi uyan doğruyu tanımlayan diziyi verir", "ad": "y=mx+b denklemindeki y-değerleri kümesi!y=mx+b denkleminde kullanılan isteğe bağlı x değerleri!mantıksal değer: Sabit = DOĞRU ya da atlanmış ise sabit b değeri olağan şekilde hesaplanır; Sabit = YANLIŞ ise b 0'a eşitlenir!mantıksal değer: ek regresyon istatistiği = DOĞRU; m-katsayıları ve sabit b değeri = YANLIŞ ya da atlanmış"}, "LOGEST": {"a": "(bilinen_y'ler; [bilinen_x'ler]; [sabit]; [konum])", "d": "Verilerinize uyması için regresyon çözümlemesi yöntemiyle hesaplanmış olan üstel eğriyi tanımlayan değerler dizisini verir", "ad": "y=b*m^x denklemindeki y-değerleri kümesi!y=b*m^x denkleminde kullanılan isteğe bağlı x değerleri!mantıksal değer: Sabit = DOĞRU ya da atlanmış ise b sabit değeri olağan şekilde hesaplanır; Sabit = YANLIŞ ise b 1'e eşitlenir!mantıksal değer: ek gerileme istatistiği = DOĞRU; m-katsayıları ve sabit b değeri = YANLIŞ ya da atlanmış"}, "LOGINV": {"a": "(olasılık; ortalama; standart_sapma)", "d": "ln(x)'in Ortalama ve Standart_sapma parametreleriyle normal dağıldığı durumlarda x'in kümülatif lognormal dağılım işlevinin tersini verir", "ad": "lognormal dağılımı ile ilgili olasılık, 0 ile 1 arasında (bunlar dahil) bir sayı!ln(x)'in ortalaması!ln(x)'in standart sapması, pozitif bir sayı"}, "LOGNORM.DIST": {"a": "(x; ortalama; standart_sapma; kümülatif)", "d": "ln(x)'in normal olarak Ortalama ve Standart_sapma parametreleriyle dağıldığı durumlarda x'in lognormal dağılımını verir", "ad": "işlevin hesaplana<PERSON> de<PERSON>, pozitif bir sayı!ln(x)'in ortalaması!ln(x)'in standart sapması, pozitif bir sayı!mantıksal değer: kümülatif da<PERSON>ılım fonksiyonu için DOĞRU'yu; olasılık yoğunluk fonksiyonu için YANLIŞ'ı kullanın"}, "LOGNORM.INV": {"a": "(olasılık; ortalama; standart_sapma)", "d": "ln(x)'in Ortalama ve Standart_sapma parametreleriyle normal dağıldığı durumlarda x'in kümülatif lognormal dağılım işlevinin tersini verir", "ad": "lognormal dağılımı ile ilgili olasılık, 0 ile 1 arasında (bunlar dahil) bir sayı!ln(x)'in ortalaması!ln(x)'in standart sapması, pozitif bir sayı"}, "LOGNORMDIST": {"a": "(x; ortalama; standart_sapma)", "d": "ln(x)'in normal olarak Ortalama ve Standart_sapma parametreleriyle dağıldığı durumlarda x'in kümülatif lognormal dağılımını verir", "ad": "<PERSON><PERSON><PERSON><PERSON>, pozitif bir sayı!ln(x)'in ortalaması!ln(x)'in standart sapması, pozitif bir sayı"}, "MAX": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir değerler kümesindeki en büyük değeri verir. Mantıksal değerleri ve metni yoksayar", "ad": "en büyüğünü bulmak istediğiniz en az 1 en fazla 255 sayı, bo<PERSON> hücre, mantı<PERSON><PERSON> değer ya da metin biçiminde sayıdır"}, "MAXA": {"a": "(değer1; [değer2]; ...)", "d": "Bir değerler kümesindeki en büyük değeri verir. Mantıksal değerleri ve metni yoksaymaz", "ad": "en büyüğünü bulmak istediğiniz en az 1 en fazla 255 sayı, bo<PERSON> hücre, mantı<PERSON><PERSON> değer ya da metin biçimindeki sayıdır"}, "MAXIFS": {"a": "(en_büyük_aralık; ölçüt_aralığı; ölçüt; ...)", "d": "Verilen koşul veya ölçüt kümesiyle belirtilen hücrelerdeki en büyük değeri döndürür", "ad": "en büyük değerin belirleneceği hücreler!belirli bir koşul için hesaplamak istediğiniz hücre aralığıdır!en büyük değer belirlenirken hangi hücrelerin dahil edileceğini tanımlayan sayı, ifade veya metin biçimindeki koşul veya ölçüttür"}, "MEDIAN": {"a": "(sayı1; [sayı2]; ...)", "d": "Verilen sayılar kümesinin ortancasını ya da bu kümenin ortasındaki sayıyı verir", "ad": "ortancasını aradığınız en az 1 en fazla 255 sayı, ad, dizi ya da sayı içeren başvurudur"}, "MIN": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir değerler kümesindeki en küçük değeri verir. Mantıksal değerleri ve metni yoksayar", "ad": "en küçüğünü bulmak istediğiniz en az 1 en fazla 255 sayı, bo<PERSON> hücre, mantı<PERSON><PERSON> değer ya da metin biçiminde sayıdır"}, "MINA": {"a": "(değer1; [değer2]; ...)", "d": "Bir değerler kümesindeki en küçük değeri verir. Mantıksal değerleri ve metni yoksaymaz", "ad": "en küçüğünü bulmak istediğiniz en az 1 en fazla 255 sayı, bo<PERSON> hücre, mantıks<PERSON> değer ya da metin biçimindeki sayıdır"}, "MINIFS": {"a": "(en_küçük_aralık; ölçüt_aralığı; ölçüt; ...)", "d": "Verilen koşul veya ölçüt kümesiyle belirtilen hücrelerdeki en küçük değeri döndürür", "ad": "en küçük değerin belirleneceği hücreler!belirli bir koşul için hesaplamak istediğiniz hücre aralığıdır!en küçük değer belirlenirken hangi hücrelerin dahil edileceğini tanımlayan sayı, ifade veya metin biçimindeki koşul veya ölçüttür"}, "MODE": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir veri dizisi ya da aralığında en sık karşılaşılan ya da en çok yinelenen değeri verir", "ad": "modunu bulmak istediğiniz en az 1 en fazla 255 sayı, ad, dizi ya da sayı içeren başvurudur"}, "MODE.MULT": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir veri dizisi veya aralığında en sık karşılaşılan veya en çok yinelenen değerleri içeren dikey bir dizi verir. Yatay bir dizi için, =DEVRİK_DÖNÜŞÜM(ENÇOK_OLAN.ÇOK(sayı1,sayı2,...)) kullanın", "ad": "modunu bulmak istediğiniz en az 1 en fazla 255 sayı, ad, dizi ya da sayı içeren başvurudur"}, "MODE.SNGL": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir veri dizisi ya da aralığında en sık karşılaşılan ya da en çok yinelenen değeri verir", "ad": "modunu bulmak istediğiniz en az 1 en fazla 255 sayı, ad, dizi ya da sayı içeren başvurudur"}, "NEGBINOM.DIST": {"a": "(hata_sayısı; başarı_sayısı; başarı_olasılığı; kümülatif)", "d": "Bir başarının negatif binom dağılımını, yani Başarı_sayısı kadar başarıdan önce Başarısızlık_s kadar başarısızlık olması olasılığını Başarı_olasılığı kadar olasılıkla verir", "ad": "başarısızlık sayısı!başarı eşik sayısı!başarı olasılığı; 0 ile 1 arasında bir sayı!mantıksal değer: kümülatif dağılım fonksiyonu için DOĞRU'yu; olasılık kütle fonksiyonu için YANLIŞ'ı kullanın"}, "NEGBINOMDIST": {"a": "(başarısızlık_s; başarı_sı; başarı_o)", "d": "Bir başarının negatif binom dağılımını, yani Başarı_sı kadar başarıdan önce Başarısızlık_s kadar başarısızlık olması olasılığını ve Başarı_o olarak başarı olasılığını verir", "ad": "başarısızlık sayısı!başarı eşik sayısı!başarı olasılığı; 0 ile 1 arasında bir sayı"}, "NORM.DIST": {"a": "(x; ortalama; standart_sapma; kümülatif)", "d": "Belirtilen ortalama ve standart sapma için normal dağılımı verir", "ad": "dağılımını bulmak istediğiniz değer!dağılımın aritmetik ortalaması!dağılımın standart sapması, pozitif bir sayı!mantıksal değer: kümülatif dağılım işlevi için DOĞRU'yu; olasılık yoğunluk işlevi için YANLIŞ'ı kullanın"}, "NORMDIST": {"a": "(x; ortalama; standart_sapma; kümülatif)", "d": "Belirtilen ortalama ve standart sapma için normal kümülatif <PERSON>ğılımı verir", "ad": "dağılımını bulmak istediğiniz değer!dağılımın aritmetik ortalaması!dağılımın standart sapması, pozitif bir sayı!mantıksal değer: kümülatif dağılım fonksiyonu için DOĞRU'yu; olasılık yoğunluk fonksiyonu için YANLIŞ'ı kullanın"}, "NORM.INV": {"a": "(olasılık; ortalama; standart_sapma)", "d": "Belirtilen ortalama ve standart sapma için normal kümülatif <PERSON>ımın tersini verir", "ad": "normal dağılıma karşılık gelen olasılık, 0 ile 1 arasında (bunlar dahil) bir sayı!dağılımın aritmetik ortalaması!dağılımın standart sapması, pozitif bir sayı"}, "NORMINV": {"a": "(olasılık; ortalama; standart_sapma)", "d": "Belirtilen ortalama ve standart sapma için normal kümülatif <PERSON>ımın tersini verir", "ad": "normal dağılıma karşılık gelen olasılık, 0 ile 1 arasında (bunlar dahil) bir sayı!dağılımın aritmetik ortalaması!dağılımın standart sapması, pozitif bir sayı"}, "NORM.S.DIST": {"a": "(z; kümülatif)", "d": "Standart normal dağılımı (ortalaması sıfır, standart sapması bir) verir", "ad": "dağılımını bulmak istediğiniz değer!fonksiyonun vereceği mantıksal değer: kümülatif dağılım fonksiyonu = DOĞRU; olasılık yoğunluğu fonksiyonu = YANLIŞ"}, "NORMSDIST": {"a": "(z)", "d": "Standart normal kümülatif <PERSON>ımı (ortalaması sıfır, standart sapması bir) verir", "ad": "dağılımını bulmak istediğiniz <PERSON>"}, "NORM.S.INV": {"a": "(olasılık)", "d": "Standart normal kümülat<PERSON> (ortalaması sıfır, standart sapması bir) tersini verir", "ad": "normal dağılıma karşılık gelen olasılık, 0 ile 1 arasında (bunlar dahil) bir sayı"}, "NORMSINV": {"a": "(olasılık)", "d": "Standart normal kümülat<PERSON> (ortalaması sıfır, standart sapması bir) tersini verir", "ad": "normal dağılıma karşılık gelen olasılık, 0 ile 1 arasında (bunlar dahil) bir sayı"}, "PEARSON": {"a": "(dizi1; dizi2)", "d": "Pearson çarpım moment korelasyon katsayısı olan r'yi verir", "ad": "bağımsız değerler kümesi!bağımlı değerler kümesi"}, "PERCENTILE": {"a": "(dizi; k)", "d": "Bir aralık içerisindeki değerlerin k. yüzdebir toplamını verir", "ad": "göreceli durumu tanımlayan veri dizisi ya da aralığı!0 ile 1 arasındaki (bunlar dahil) yüzdebirlik değeri"}, "PERCENTILE.EXC": {"a": "(dizi; k)", "d": "Aralıktaki değerlerin k. yüzdebirliğini verir; k, 0..1 aralığındadır (bunlar hariç)", "ad": "göreceli durumu tanımlayan veri dizisi ya da aralığı!0 ile 1 arasındaki (bunlar dahil) yüzdebirlik değeri"}, "PERCENTILE.INC": {"a": "(dizi; k)", "d": "Aralıktaki değerlerin k. yüzdebirliğini verir; k, 0..1 aralığındadır (bunlar dahil)", "ad": "göreceli durumu tanımlayan veri dizisi ya da aralığı!0 ile 1 arasındaki (bunlar dahil) yüzdebirlik değeri"}, "PERCENTRANK": {"a": "(dizi; x; [anlam])", "d": "Bir veri kümesindeki bir değerin sı<PERSON>ını, veri kümesinin yüzdesi olarak verir", "ad": "göreceli durumu tanımlayan sayısal değerli veri dizisi veya aralığı!derecesini öğrenmek istediğiniz değer!dönen yüzde oranı için anlamlı rakam sayısını belirten isteğe bağlı değer, atlanırsa üç rakam kullanılır (0.xxx%)"}, "PERCENTRANK.EXC": {"a": "(dizi; x; [anlam])", "d": "Bir veri kümesindeki değerin derecesini veri kümesinin yüzdesi (0..1, bun<PERSON> hari<PERSON>) olarak verir", "ad": "göreceli durumu tanımlayan sayısal değerli veri dizisi veya aralığı!derecesini öğrenmek istediğiniz değer!dönen yüzde oranı için anlamlı rakam sayısını belirten isteğe bağlı değer, atlanırsa üç rakam kullanılır (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(dizi; x; [anlam])", "d": "Bir veri kümesindeki değerin derecesini veri kümesinin yüzdesi (0..1, bunlar da<PERSON>) olarak verir", "ad": "göreceli durumu tanımlayan sayısal değerli veri dizisi veya aralığı!derecesini öğrenmek istediğiniz değer!dönen yüzde oranı için anlamlı rakam sayısını belirten isteğe bağlı değer, atlanırsa üç rakam kullanılır (0.xxx%)"}, "PERMUT": {"a": "(sayı; sayı_seçilen)", "d": "Tüm nesnelerden seçilebilecek olan verilen sayıda nesne için permütasyon sayısını verir", "ad": "toplam nesne sayısı!her permütasyondaki nesne sayısı"}, "PERMUTATIONA": {"a": "(sayı; sayı_seçilen)", "d": "Tüm nesnelerden seçilebilecek olan verilen sayıda (yinelemelerle) nesne için permütasyon sayısını verir", "ad": "nesnelerin toplam sayısı!her permütasyondaki nesne sayısı"}, "PHI": {"a": "(x)", "d": "Standart normal dağılımın yoğunluk fonksiyonunun değerini döndürür", "ad": "standart normal dağı<PERSON>ım yoğunluğunu bulmak istediğiniz sayı"}, "POISSON": {"a": "(x; ortalama; kümülatif)", "d": "Poisson dağılımını verir", "ad": "olay sayısı!beklenen sayısal de<PERSON>, pozitif bir sayı!mantıksal değer: kümülatif Poisson olasılığı için DOĞRU'yu; Poisson olasılık kütle fonksiyonu için YANLIŞ'ı kullanın"}, "POISSON.DIST": {"a": "(x; ortalama; kümülatif)", "d": "Poisson dağılımını verir", "ad": "olay sayısı!beklenen sayısal de<PERSON>, pozitif bir sayı!mantıksal değer: kümülatif Poisson olasılığı için DOĞRU'yu; Poisson olasılık kütle işlevi için YANLIŞ'ı kullanın"}, "PROB": {"a": "(x_aralığ<PERSON>; olasılık_aralığı; alt_sınır; [üst_sınır])", "d": "Bir aralıktaki değerlerin iki sınır arasında ya da alt sınıra eşit olması olasılığını verir", "ad": "olasılık değerleriyle ilgili sayısal x değerleri aralığı!X_aralığındaki değerlerle ilgili olasılıklar kümesi, 0 ile 1 arasındaki (0 hariç) değerler!olasılık işleminin yapılacağı değerin alt sınırı!değerin isteğe bağlı en üst sınırı, OLASILIK X_aralığı değerlerinin Alt_sınır'a eşit olma olasılığını verir"}, "QUARTILE": {"a": "(dizi; d<PERSON>rt<PERSON><PERSON>)", "d": "Bir veri kümesinin dörttebirliğini verir", "ad": "dörttebir değerini istediğiniz sayısal değerler dizisi veya hücreler aralığı!sayı: en küçük değer = 0; birinci dörttebir = 1; medyan de<PERSON> = 2; üçüncü dörttebir = 3; en büyük değer = 4"}, "QUARTILE.INC": {"a": "(dizi; d<PERSON>rt<PERSON><PERSON>)", "d": "0..1 (b<PERSON><PERSON> dahil) aralığındaki yüzdebir değerlerini temel alarak veri kümesinin dörttebirliğini verir", "ad": "dörttebir değerini istediğiniz sayısal değerler dizisi veya hücreler aralığı!sayı: en küçük değer = 0; birinci dörtebir = 1; medyan de<PERSON> = 2; üçüncü dörtebir = 3; en büyük değer = 4"}, "QUARTILE.EXC": {"a": "(dizi; d<PERSON>rt<PERSON><PERSON>)", "d": "0..1 (b<PERSON><PERSON> hari<PERSON>) aralığındaki yüzdebir değerlerini temel alarak veri kümesinin dörttebirliğini verir", "ad": "dörttebir değerini istediğiniz sayısal değerler dizisi veya hücreler aralığı!sayı: en küçük değer = 0; birinci dörtebir = 1; medyan de<PERSON> = 2; üçüncü dörtebir = 3; en büyük değer = 4"}, "RANK": {"a": "(sayı; başv; [sıra])", "d": "Bir sayı listesindeki bir sayının derecesini verir: <PERSON><PERSON><PERSON> di<PERSON> değerlere göreli olarak büyüklüğü", "ad": "derecesini bulmak istediğiniz sayı!bir sayı listesi başvurusu veya dizisi. <PERSON><PERSON><PERSON> olmayan değ<PERSON>ler yoksayılır!sayı: azalan sıralı listedeki derece = 0 ya da atlanmış; artan sıralı listedeki derece = sıfır olmayan herhangi bir değer"}, "RANK.AVG": {"a": "(sayı; başv; [sıra])", "d": "Bir sayı listesindeki bir sayının derecesini verir: <PERSON><PERSON><PERSON> di<PERSON>er değerlere göreli olarak büyüklüğü; birden fazla değer aynı dereceye sahipse, ortalama derece döndürülür", "ad": "derecesini bulmak istediğiniz sayı!bir sayı listesi başvurusu veya dizisi. <PERSON><PERSON><PERSON> olmayan değ<PERSON>ler yoksayılır!sayı: azalan sıralı listedeki derece = 0 ya da atlanmış; artan sıralı listedeki derece = sıfır olmayan herhangi bir değer"}, "RANK.EQ": {"a": "(sayı; başv; [sıra])", "d": "Bir sayı listesindeki bir sayının derecesini verir: <PERSON><PERSON><PERSON> diğer değerlere göreli olarak büyüklüğü; birden fazla değer aynı dereceye sahipse, de<PERSON>er kümesindeki en yüksek derece döndürülür", "ad": "derecesini bulmak istediğiniz sayı!bir sayı listesi başvurusu veya dizisi. <PERSON><PERSON><PERSON> olmayan değ<PERSON>ler yoksayılır!sayı: azalan sıralı listedeki derece = 0 ya da atlanmış; artan sıralı listedeki derece = sıfır olmayan herhangi bir değer"}, "RSQ": {"a": "(bilinen_y'ler; bilinen_x'ler)", "d": "Verilen veri noktaları boyunca Pearson çarpım moment korelasyon katsayısının karesini verir", "ad": "dizi ya da veri noktaları aralığıdır ve sayı, ad, dizi ya da sayı içeren başvurular olabilir!dizi ya da veri noktaları aralığıdır ve sayı, ad, dizi ya da sayı içeren başvurular olabilir"}, "SKEW": {"a": "(sayı1; [sayı2]; ...)", "d": "Dağılımın eğriliğini verir: bir dağılımın ortalaması etrafındaki asimetri derecesini belirtir", "ad": "eğriliği hesaplamak istediğiniz en az 1 en fazla 255 sayı, ad, dizi ya da sayı içeren başvurudur"}, "SKEW.P": {"a": "(sayı1; [sayı2]; ...)", "d": "Popülasyona bağlı olarak dağılımın eğriliğini verir: bir dağılımın ortalaması etrafındaki asimetri derecesini belirtir", "ad": "popülasyon eğriliğini hesaplamak istediğiniz en az 1 en fazla 254 sayı, ad, dizi ya da sayı içeren başvurudur"}, "SLOPE": {"a": "(bilinen_y'ler; bilinen_x'ler)", "d": "Verilen veri noktaları boyunca doğrusal regresyon çizgisinin eğimini verir", "ad": "sayısal bağımlı veri noktaları dizisi veya hücre aralığıdır ve sayı, ad, dizi ya da sayı içeren başvuru olabilir!bağımsız veri noktaları kümesidir ve sayı, ad dizi ya da sayı içeren başvuru olabilir"}, "SMALL": {"a": "(dizi; k)", "d": "Bir veri kümesinde k. en küçük değeri verir. <PERSON><PERSON><PERSON><PERSON>, beşinci en küçük sayı", "ad": "k. en küçük değeri bulmak istediğiniz sayısal veri dizisi veya aralığı!gelecek olan değerin bulunduğu hücre aralığı veya dizideki konumu (en küçük değerden)"}, "STANDARDIZE": {"a": "(x; ortalama; standart_sapma)", "d": "Bir ortalama ve standart sapma tarafından temsil edilen bir dağılımdan normalleştirilen değeri verir", "ad": "normalize etmek istediğiniz değer!dağılımın aritmetik ortalaması!dağılımın standart sapması, pozitif bir sayı"}, "STDEV": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir örneğe day<PERSON>rak standart sapmayı tahmin eder (örnekteki mantıksal değerleri ve metni yoksayar)", "ad": "Popülasyondan alınmış bir örneğe karşılık gelen en az 1 en fazla 255 sayıdır; her biri sayı ya da sayı içeren başvuru olabilir"}, "STDEV.P": {"a": "(sayı1; [sayı2]; ...)", "d": "Bağımsız değişkenler olarak verilen tüm popülasyonu temel alarak standart sapmayı hesaplar (mantıksal değerleri ve metni yoksayar)", "ad": "Popülasyona karşılık gelen en az 1 en fazla 255 sayıdır ve her biri sayı ya da sayı içeren başvuru olabilir"}, "STDEV.S": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir örneğe day<PERSON>rak standart sapmayı tahmin eder (örnekteki mantıksal değerleri ve metni yoksayar)", "ad": "popülasyondan alınmış bir örneğe karşılık gelen en az 1 en fazla 255 sayıdır; her biri sayı ya da sayı içeren başvuru olabilir"}, "STDEVA": {"a": "(değer1; [değer2]; ...)", "d": "Mantıksal değerler ve metin içeren bir örneğin standart sapmasını tahmin eder. Metin ve YANLIŞ mantıksal değer 0; DOĞRU mantıksal değer ise 1 değerini alır", "ad": "bir popülasyonun bir örneğine karşılık gelen en az 1 en fazla 255 değerdir ve değer, ad ya da değer başvurusu olabilir"}, "STDEVP": {"a": "(sayı1; [sayı2]; ...)", "d": "Bağımsız değişkenler olarak verilen tüm popülasyonu temel alarak standart sapmayı hesaplar (mantıksal değerleri ve metni yoksayar)", "ad": "popülasyona karşılık gelen en az 1 en fazla 255 sayıdır ve her biri sayı ya da sayı içeren başvuru olabilir"}, "STDEVPA": {"a": "(değer1; [değer2]; ...)", "d": "Mantıksal değerler ve metin içeren tüm bir popülasyon için standart sapmayı hesaplar. Metin ve YANLIŞ mantıksal değer 0; DOĞRU mantıksal değer ise 1 değerini alır", "ad": "bir popülasyona karşılık gelen en az 1 en fazla 255 değerdir ve her biri değer, ad, dizi ya da değer içeren başvuru olabilir"}, "STEYX": {"a": "(bilinen_y'ler; bilinen_x'ler)", "d": "<PERSON><PERSON> regresyon<PERSON>i her x değeri için tahmin edilen y değerinin standart hatasını verir", "ad": "bağımlı veri noktaları aralığı veya dizisidir ve sayı, ad, dizi ya da sayı içeren başvuru olabilir!bağımsız veri noktaları aralığı veya dizisidir ve sayı, ad, dizi ya da sayı içeren başvuru olabilir"}, "TDIST": {"a": "(x; serb<PERSON><PERSON>_der; yazı_say)", "d": "T-dağılımını verir", "ad": "dağılımı hesaplamak istediğiniz sayısal değer!dağılımı karakterize eden serbestlik derecesinin sayısını gösteren tamsayı!verilecek dağılım kuyruklarının sayısını belirtir: tek kuyruklu dağılım = 1; çift kuyruklu dağılım = 2"}, "TINV": {"a": "(olasıl<PERSON>k; serb_derecesi)", "d": "T-dağılımının iki kuyruklu tersini verir", "ad": "iki kuyruklu t-dağılımının olasılığı, 0 ile 1 arasındaki (bunlar dahil) bir sayı!dağılımın türünü belirleyen serbestlik derecesinin sayısını gösteren pozitif tamsayı"}, "T.DIST": {"a": "(x; serb_derecesi; kümülatif)", "d": "Sol kuyruklu t-dağılımını verir", "ad": "dağılımı hesaplamak istediğiniz sayısal değer!dağılımı karakterize eden serbestlik derecesinin sayısını gösteren tam sayı!mantıksal değer: kümülatif dağılım fonksiyonu için DOĞRU'yu; olasılık yoğunluk fonksiyonu için YANLIŞ'ı kullanın"}, "T.DIST.2T": {"a": "(x; serb_derecesi)", "d": "İki kuyruklu t-dağılımını verir", "ad": "dağılımı hesaplamak istediğiniz sayısal değer!dağılımı karakterize eden serbestlik derecesinin sayısını gösteren tam sayı"}, "T.DIST.RT": {"a": "(x; serb_derecesi)", "d": "Sağ kuyruklu t-dağılımını verir", "ad": "dağılımı hesaplamak istediğiniz sayısal değer!dağılımı karakterize eden serbestlik derecesinin sayısını gösteren tam sayı"}, "T.INV": {"a": "(olasıl<PERSON>k; serb_derecesi)", "d": "T-dağılımının sol kuyruklu tersini verir", "ad": "iki kuyruklu t-dağılımının olasılığı, 0 ile 1 arasındaki (bunlar dahil) bir sayı!dağılımın türünü belirleyen serbestlik derecesinin sayısını gösteren pozitif tamsayı"}, "T.INV.2T": {"a": "(olasıl<PERSON>k; serb_derecesi)", "d": "T-dağılımının iki kuyruklu tersini verir", "ad": "iki kuyruklu t-dağılımının olasılığı, 0 ile 1 arasındaki (bunlar dahil) bir sayı!dağılımın türünü belirleyen serbestlik derecesinin sayısını gösteren pozitif tamsayı"}, "T.TEST": {"a": "(dizi1; dizi2; yazı_say; tür)", "d": "Bir t-Test i<PERSON><PERSON> verir", "ad": "ilk veri kümesi!ikinci veri kümesi!verilecek dağılım kuyruklarının sayısını belirtir: tek kuyruklu dağılım = 1; çift kuyruklu dağılım = 2!uygulanacak t-test türü: eşli = 1, iki-örnek eşit varyans (homoskedastik) = 2, iki örnek eşit olmayan varyans = 3"}, "TREND": {"a": "(bilinen_y'ler; [bilinen_x'ler]; [yeni_x'ler]; [sabit])", "d": "Bilinen değerlere en küçük kareler yöntemini uygulayarak değerleri bir doğruya uydurur ve bir doğrusal eğilim boyunca verir", "ad": "y = mx+b denklemindeki bilinen y-değerleri aralığı ya da dizisi!y = mx+b denkleminde kullanılan isteğe bağlı bilinen x-değerleri aralığı ya da dizisi, Bilinen_y'lerle aynı boyuttaki dizi!ilişkili y-değerlerini EĞİLİM ile bulacağınız yeni x-değerleri aralığı ya da dizisi!mantıksal değer: Sabit = DOĞRU ya da atlanmış ise sabit b değeri olağan şekilde hesaplanır; Sabit = YANLIŞ ise b 0'a eşitlenir"}, "TRIMMEAN": {"a": "(dizi; yüzde)", "d": "Bir veri kümesinin iç kısmının ortalamasını verir", "ad": "kırpılıp ortalaması alınacak değerler dizisi veya aralığı!veri kümesinin alt ve üst ucunda bulunan ve hesaplama dışı tutulacak olan veri noktalarının kesirli sayısı"}, "TTEST": {"a": "(dizi1; dizi2; yazı_say; tür)", "d": "Bir t-Test i<PERSON><PERSON> verir", "ad": "ilk veri kümesi!ikinci veri kümesi!verilecek dağılım kuyruklarının sayısını belirtir: tek kuyruklu dağılım = 1; çift kuyruklu dağılım = 2!uygulanacak t-test türü: eşli = 1, iki-örnek eşit varyans (homoskedastik) = 2, iki örnek eşit olmayan varyans = 3"}, "VAR": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir örneğe dayanarak <PERSON>ı tahmin eder (örnekteki mantıksal değerleri ve metni yoksayar)", "ad": "popülasyondan alınmış bir örneğe karşılık gelen en az 1 en fazla 255 sayısal bağımsız değişkendir"}, "VAR.P": {"a": "(sayı1; [sayı2]; ...)", "d": "Tüm popülasyonun varyansını hesaplar (popülasyondaki mantıksal değerleri ve metni yoksayar)", "ad": "Popülasyona karşılık gelen en az 1 en fazla 255 sayısal bağımsız değişkendir"}, "VAR.S": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir örneğe dayanarak <PERSON>ı tahmin eder (örnekteki mantıksal değerleri ve metni yoksayar)", "ad": "Popülasyondan alınmış bir örneğe karşılık gelen en az 1 en fazla 255 sayısal bağımsız değişkendir"}, "VARA": {"a": "(değer1; [değer2]; ...)", "d": "Mantıksal değerler ve metin içeren bir örneğin varyansını tahmin eder. Metin ve YANLIŞ mantıksal değer 0; DOĞRU mantıksal değer ise 1 değerini alır", "ad": "bir popülasyonun bir örneğine karşılık gelen en az 1 en fazla 255 değer bağımsız değişkenidir"}, "VARP": {"a": "(sayı1; [sayı2]; ...)", "d": "Tüm popülasyonun varyansını hesaplar (popülasyondaki mantıksal değerleri ve metni yoksayar)", "ad": "popülasyona karşılık gelen en az 1 en fazla 255 sayısal bağımsız değişkendir"}, "VARPA": {"a": "(değer1; [değer2]; ...)", "d": "Mantıksal değerler ve metin içeren bir popülasyon için varyansı hesaplar. Metin ve YANLIŞ mantıksal değer 0; DOĞRU mantıksal değer ise 1 değerini alır", "ad": "bir popülasyona karşılık gelen en az 1 en fazla 255 değer bağımsız değişkenidir"}, "WEIBULL": {"a": "(x; alfa; beta; kümülatif)", "d": "<PERSON><PERSON> dağılımını verir", "ad": "işlevin he<PERSON><PERSON><PERSON>, negatif olmayan bir sayı!dağılım parametresi, pozitif bir sayı!dağılım parametresi, pozitif bir sayı!mantıksal değer: kümülatif dağılım fonksiyonu için DOĞRU'yu; olasılık kütle fonksiyonu için YANLIŞ'ı kullanın"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kümülatif)", "d": "<PERSON><PERSON> dağılımını verir", "ad": "işlevin he<PERSON><PERSON><PERSON>, negatif olmayan bir sayı!dağılım parametresi, pozitif bir sayı!dağılım parametresi, pozitif bir sayı!mantıksal değer: kümülatif dağılım fonksiyonu için DOĞRU'yu; olasılık kütle fonksiyonu için YANLIŞ'ı kullanın"}, "Z.TEST": {"a": "(dizi; x; [sigma])", "d": "Bir z-test'in tek kuyruklu P-değerini verir", "ad": "X'in sınanacağı veri dizisi veya aralığıdır!sınanacak değerdir!popülasyonun (bilinen) standart sapmasıdır. <PERSON><PERSON><PERSON><PERSON>, örnek standart sapma kullanılır"}, "ZTEST": {"a": "(dizi; x; [sigma])", "d": "Bir z-test'in tek kuyruklu P-değerini verir", "ad": "X'in sınanacağı veri dizisi veya aralığıdır!sınanacak değerdir!popülasyonun (bilinen) standart sapmasıdır. <PERSON><PERSON><PERSON><PERSON>, örnek standart sapma kullanılır"}, "ACCRINT": {"a": "(çıkış; ilk_faiz; mutabakat; oran; nominal; sıklık; [temel]; [hesapl_y<PERSON><PERSON><PERSON>])", "d": "Düzenli faiz ödenen bir menkul kıymetin birikmiş faizini döndürür.", "ad": "menkul kıymetin çıkış tarihi, tarih seri numarası cinsinden!menkul kıymetin ilk faiz tarihi, tarih seri numarası cinsinden!menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin yıllık kupon oranı!menkul kıymetin nominal değeri!yıl başına düşen kupon ödemeleri sayısı!kullanılacak gün sayısı türü!mantıksal bir değer: çıkış tarihinden başlayarak biriken faiz için = DOĞRU veya yoksayılır; en son kupon ödeme tarihinden hesaplamak için = YANLIŞ"}, "ACCRINTM": {"a": "(çıkış; mutabakat; oran; nominal; [temel])", "d": "Vadesinde faiz ödeyen bir menkul kıymet için elde edilen faizi döndürür", "ad": "menkul kıymetin çı<PERSON> tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin yıllık kupon oranı!menkul kıymetin nominal değeri!kullanılacak gün sayısı türü"}, "AMORDEGRC": {"a": "(maliyet; alı<PERSON>_tarihi; ilk_dönem; hurda; dönem; oran; [temel])", "d": "<PERSON>ir malın her hesap dönemi için doğrusal amortisman eşdağılımını döndürür", "ad": "malın değeri!malın satın alındığı tarih!ilk dönemin son günü!malın ömrü sonundaki hurda değeri!dönem!amortisman oranı!temel alınacak yıl: 360 gün için 0, ger<PERSON>ek değeri için 1, 365 gün için 3."}, "AMORLINC": {"a": "(maliyet; alı<PERSON>_tarihi; ilk_dönem; hurda; dönem; oran; [temel])", "d": "<PERSON>ir malın her hesap dönemi için doğrusal amortisman eşdağılımını döndürür", "ad": "malın değeri!malın satın alındığı tarih!ilk dönemin son günü!malın ömrü sonundaki hurda değeri!dönem!amortisman oranı!temel alınacak yıl: 360 gün için 0, ger<PERSON>ek değeri için 1, 365 gün için 3."}, "COUPDAYBS": {"a": "(mutabakat; vade; sıklık; [temel])", "d": "Kupon döneminin başlangıcından mutabakat tarihine kadar olan gün sayısını döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "COUPDAYS": {"a": "(mutabakat; vade; sıklık; [temel])", "d": "Mutabakat tarihini içeren kupon dönemindeki gün sayısını döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "COUPDAYSNC": {"a": "(mutabakat; vade; sıklık; [temel])", "d": "Mutabakat tarihinden bir sonraki kupon tarihine kadar olan gün sayısını döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "COUPNCD": {"a": "(mutabakat; vade; sıklık; [temel])", "d": "Mutabakat tarihinden sonraki kupon tarihini dö<PERSON>ü<PERSON><PERSON><PERSON>", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "COUPNUM": {"a": "(mutabakat; vade; sıklık; [temel])", "d": "Mutabakat ve vade tarihleri arasındaki ödenebilir kupon sayısını döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "COUPPCD": {"a": "(mutabakat; vade; sıklık; [temel])", "d": "Mutabakat tarihinden önceki kupon tarihini döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "CUMIPMT": {"a": "(oran; döne<PERSON>_sayısı; bd; ba<PERSON><PERSON><PERSON><PERSON>_döne<PERSON>; bitiş_dönemi; tür)", "d": "İki dönem arasında ödenen bileşik faizi döndürür", "ad": "faiz oranı!toplam ödeme dönemi sayısı!bugünkü değer!hesaplamadaki ilk dönem!hesaplamadaki son dönem!ödeme zamanı"}, "CUMPRINC": {"a": "(oran; döne<PERSON>_sayısı; bd; ba<PERSON><PERSON><PERSON><PERSON>_döne<PERSON>; bitiş_dönemi; tür)", "d": "İki dönem arasında borç için ödenen bileşik anaparayı döndürür", "ad": "faiz oranı!ödeme dönemi toplam sayısı!bugünkü değer!hesaplamadaki ilk dönem!hesaplamadaki son dönem!ödeme zamanı"}, "DB": {"a": "(mali<PERSON>t; hurda; ömür; dönem; [ay])", "d": "Sabit azalan bakiye yöntemi kullanarak bir varlığın belirtilen dönem içindeki yıpranmasını verir", "ad": "varlığın ilk maliyeti!varlığın kullanım ömrü bittikten sonraki hurda değeri!varlığın yıpranma dönemi miktarı (bazen varlığın kullanım ömrü olarak da kullanılır)!yıpranmayı hesaplamak istediğiniz dönem. Ömür ile aynı birimde olmalıdır!ilk yılda bulunan ay sayısı. Ay sayısı boş bırakılırsa 12 olduğu varsayılır"}, "DDB": {"a": "(mali<PERSON>t; hurda; ömür; dönem; [faktör])", "d": "Çift azalan bakiye yöntemi veya belirttiğiniz diğer bir yöntemle, bir varlığın belirtilen dönem içindeki yıpranmasını verir", "ad": "varlığın ilk maliyeti!varlığın kullanım ömrü bittikten sonraki hurda değeri!varlığın yıpranma dönemi miktarı (bazen varlığın kullanım ömrü olarak da kullanılır)!yıpranmayı hesaplamak istediğiniz dönem. Ömür ile aynı birimde olmalıdır!bakiyenin azalma oranı. Faktör yoksayılırsa, 2 olarak varsayılır (çift azalan bakiye yöntemi)"}, "DISC": {"a": "(mutabakat; vade; fiyat; itfa; [temel])", "d": "Menkul kıymet için indirim oranını döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin 100 TL nominal değer başına fiyatı!menkul kıymetin 100 TL nominal değer başına itfa değeri!kullanılacak gün sayısı türü"}, "DOLLARDE": {"a": "(kesirli_para; payda)", "d": "Ke<PERSON>li olarak gösterilen ücreti ondalık düzene çevirir", "ad": "kesir cinsinden gösterilen sayı!kesrin paydasında kullanılacak tamsayı"}, "DOLLARFR": {"a": "(ondalık_para; payda)", "d": "Ondalık düzende gösterilen ücreti kesir şekline çevirir", "ad": "onluk düzende sayı!kesrin paydasında kullanılacak tamsayı"}, "DURATION": {"a": "(mutabakat; vade; kupon; getiri; sıklık; [temel])", "d": "Dönemsel faiz ödemeli bir menkul kıymet için yıllık süreyi döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin yıllık kupon oranı!menkul kıymetin yıllık getirisi!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "EFFECT": {"a": "(nominal_oran; döne<PERSON>_sayısı)", "d": "Etkin bileşik faiz oranını döndürür", "ad": "nominal faiz oranı!yıl başına bileşim sayısı"}, "FV": {"a": "(oran; döne<PERSON>_sayısı; dö<PERSON><PERSON>el_ödeme; [bd]; [tür])", "d": "Bir yatır<PERSON><PERSON><PERSON>n gel<PERSON>, dönemsel sabit ödemeler ve sabit faiz oranı kullanarak hesaplar.", "ad": "dönem başına faiz oranı. <PERSON><PERSON><PERSON><PERSON>, %6 yıllık faiz oranına karşılık üç aylık ödeme için %6/4 kullanın!yatırımın toplam ödeme dönemi sayısı!her dönem yapılan ödeme miktarı; yatırım süresi boyunca değişemez!bugünkü değer veya gelecekte yapılacak bir dizi ödemenin bugünkü toplam değeri. Atlanırsa, Bd = 0!ödemelerin zamanını gösteren değer: dönem başında = 1; dönem sonunda = 0 ya da atlanmış"}, "FVSCHEDULE": {"a": "(anapara; program)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bir seri bileşik faiz uygulandıkt<PERSON>, gelecekteki değerini verir", "ad": "şimdiki değer!uygulanacak faiz oranları serisi"}, "INTRATE": {"a": "(mutabakat; vade; yatırım; itfa; [temel])", "d": "Tam olarak yatırım yapılan bir menkul kıymetin faiz oranını döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymete yatırılan miktar!vadesindeki getiri miktarı!kullanılacak gün sayısı türü"}, "IPMT": {"a": "(oran; dönem; döne<PERSON>_sayısı; bd; [gd]; [tür])", "d": "Dönemsel sabit ödemeli ve sabit faiz oranlı bir yatırımın verilen dönem için faiz ödemesini verir.", "ad": "dönem başına düşen faiz oranı. <PERSON><PERSON><PERSON><PERSON>, %6 yıllık faiz oranına karşılık üç aylık ödeme için %6/4 kullanın!faiz tutarını bulmak istediğiniz ve 1 ile dönem_sayısı arasında olması gereken dönem!yatırımın toplam ödeme dönemi sayısı!bugünkü değer veya gelecekte yapılacak bir dizi ödemenin bugünkü değerini gösteren toplam miktar!gelecek değer ya da son ödeme yapıldıktan sonra elde edilmek istenen nakit bakiyesi. Atlanırsa gd = 0!ödeme zamanını gösteren mantıksal değer: dönem başında ödeme = 1; dönem sonunda ödeme = 0 ya da atlanmış"}, "IRR": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [tahmin])", "d": "Bir dizi nakit akışı için iç verim oranını verir", "ad": "iç verim oranını hesaplamak istediğiniz sayıları içeren dizi veya hücreler başvurusu!tahm<PERSON><PERSON>z, İÇ_VERİM_ORANI sonucuna yakın bir sayıdır; Atlanırsa 0,1 (yüzde 10) kabul edilir"}, "ISPMT": {"a": "(oran; dönem; döne<PERSON>_sayısı; bd)", "d": "Yatırımın belirli bir döneminde ödenen faizi verir", "ad": "dönem başına düşen faiz oranı. <PERSON><PERSON><PERSON><PERSON>, %6 yıllık faiz oranına karşılık üç aylık ödeme için %6/4 kullanın!faizini bulmak istediğiniz dönem!yatırımın ödeme dönemi sayısı!gelecekte yapılacak bir dizi ödemenin bugünkü toplam değeri"}, "MDURATION": {"a": "(mutabakat; vade; kupon; getiri; sıklık; [temel])", "d": "100 TL nominal değerli bir menkul kıymet için <PERSON>ley farklılaştırılmış süresini döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin yıllık kupon oranı!menkul kıymetin yıllık getirisi!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "MIRR": {"a": "(<PERSON><PERSON><PERSON><PERSON>; finansman_faiz_oranı; tekrar_yatırım_oranı)", "d": "Yatırım maliyetini ve nakit paranın tekrar yatırımından elde edilen faizin getirisini dikkate alarak, dönemsel nakit akışları serisi için iç verim oranını verir", "ad": "d<PERSON><PERSON><PERSON> dönemlerde gerçekleşen bir seri ödeme (negatif) ve geliri (pozitif) temsil eden sayıları içeren hücre başvurusu ya da dizi!nakit akışında kullanılan paraya ödediğiniz faiz oranı!tekrar yatırım yaptığınızda nakit akışı için elde edeceğiniz faiz oranı"}, "NOMINAL": {"a": "(et<PERSON>_oran; d<PERSON><PERSON><PERSON>_sayısı)", "d": "Yıllık nominal faiz oranını döndürür", "ad": "etkin faiz oranı!yıl başına bileşim sayısı"}, "NPER": {"a": "(oran; dö<PERSON><PERSON><PERSON>_ödeme; bd; [gd]; [tür])", "d": "Dönemsel sabit ödemeli ve sabit faizli bir yatırımın dönem sayısını verir", "ad": "dönem başına düşen faiz oranı. <PERSON><PERSON><PERSON><PERSON>, %6 yıllık faiz oranına karşılık üç aylık ödeme için %6/4 kullanın!her dönem yapılan ödeme miktarı; yatırım süresi boyunca değişemez!bugünkü değer veya gelecekte yapılacak bir dizi ödemenin bugünkü toplam değeri!gelecek değer ya da son ödeme yapıldıktan sonra elde etmek istediğiniz nakit bakiyesi. Atlanırsa sıfır kullanılır!mantıksal değer: dönem başındaki ödemeler = 1; dönem sonundaki ödemeler = 0 ya da atlanmış"}, "NPV": {"a": "(oran; değer1; [değer2]; ...)", "d": "<PERSON><PERSON><PERSON>, gelecekte yapılacak bir dizi ödemeyi (negatif değ<PERSON>ler) ve geliri (pozitif değerler) temel alarak yatırımın bugünkü net değerini verir", "ad": "bir dönem başına düşen indirim oranıdır!zamana eşit olarak yayılmış ve her dönemin sonunda gerçekleşen en az 1 en fazla 254 gelir ve ödeme"}, "ODDFPRICE": {"a": "(mutabakat; vade; çıkış; ilk_kupon; oran; getiri; itfa; sıklık; [temel])", "d": "Tek ilk dönemli bir menkul kıymet için 100 TL nominal değer başına fiyatı döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin çıkış tarihi, tarih seri numarası cinsinden!menkul kıymetin ilk kupon tarihi, tarih seri numarası cinsinden!menkul kıymetin faiz oranı!menkul kıymetin yıllık getirisi!menkul kıymetin 100 TL nominal değer başına itfa değeri!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "ODDFYIELD": {"a": "(mutabakat; vade; çıkış; ilk_kupon; oran; fiyat; itfa; sıklık; [temel])", "d": "Tek ilk dönemli bir menkul kıymet için getiriyi döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin çıkış tarihi, tarih seri numarası cinsinden!menkul kıymetin ilk kupon tarihi, tarih seri numarası cinsinden!menkul kıymetin faiz oranı!menkul kıymetin fiyatı!menkul kıymetin 100 TL nominal değer başına itfa değeri!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "ODDLPRICE": {"a": "(mutaba<PERSON>; vade; son_faiz; oran; getiri; itfa; sıklık; [temel])", "d": "<PERSON><PERSON> son dö<PERSON><PERSON><PERSON> bir menkul kıymet için 100 TL nominal değer başına fiyatı döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin son kupon tarihi, tarih seri numarası cinsinden!menkul kıymetin faiz oranı!menkul kıymetin yıllık getirisi!menkul kıymetin 100 TL nominal değer başına itfa değeri!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "ODDLYIELD": {"a": "(mutaba<PERSON>; vade; son_faiz; oran; fiyat; itfa; sıklık; [temel])", "d": "<PERSON><PERSON> son dö<PERSON><PERSON><PERSON> bir menkul kı<PERSON>tin getirisini dö<PERSON><PERSON><PERSON><PERSON><PERSON>", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin son kupon tarihi, tarih seri numarası cinsinden!menkul kıymetin faiz oranı!menkul kıymetin fiyatı!menkul kıymetin 100 TL nominal değer başına itfa değeri!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "PDURATION": {"a": "(oran; bd; gd)", "d": "Ya<PERSON><PERSON><PERSON><PERSON>m tarafından belirtilen bir değere ulaşmak için gereken dönem sayısını döndürür", "ad": "dönem başına düşen faiz oranı.!yatırımın bugünkü değeri!yatırımın gelecekteki istenen değeri"}, "PMT": {"a": "(oran; döne<PERSON>_sayısı; bd; [gd]; [tür])", "d": "Sabit ödemeli ve sabit faizli bir borç için yapılacak ödemeyi hesaplar", "ad": "borç için dönem başına düşen faiz oranı. <PERSON><PERSON><PERSON><PERSON>, %6 yıllık faiz oranına karşılık üç aylık ödeme için %6/4 kullanın!borç için toplam ödeme dönemi sayısı!bugünkü değer: gelecekte yapılacak bir dizi ödemenin bugünkü değerini gösteren toplam miktar!gelecek değer ya da son ödeme yapıldıktan sonra elde edilmek istenen nakit bakiyesi, atlanırsa 0 (sıfır)!mantıksal değer: dönem başında ödeme = 1; dönem sonunda ödeme = 0 ya da atlanmış"}, "PPMT": {"a": "(oran; dönem; döne<PERSON>_sayısı; bd; [gd]; [tür])", "d": "Dönemsel sabit ödemeli ve sabit faizli bir yatırım için yapılacak anapara ödemesi tutarını verir", "ad": "dönem başına düşen faiz oranı. <PERSON><PERSON><PERSON><PERSON>, %6 yıllık faiz oranına karşılık üç aylık ödeme için %6/4 kullanın!1 ile dönem_sayısı arasında olması gereken dönemi belirtir!yatırımın toplam ödeme dönemi sayısı!bugünkü değer: gelecekte yapılacak bir dizi ödemenin bugünkü değerini gösteren toplam miktar!gelecek değer ya da son ödeme yapıldıktan sonra elde edilmek istenen nakit bakiyesi!mantıksal değer: dönem başında ödeme = 1; dönem sonunda ödeme = 0 ya da atlanmış"}, "PRICE": {"a": "(mutabakat; vade; oran; getiri; itfa; sıklık; [temel])", "d": "Dönemsel faiz ödeyen bir menkul kıymet için 100 TL nominal değer başına fiyatı döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin yıllık kupon oranı!menkul kıymetin yıllık getirisi!menkul kıymetin 100 TL nominal değer başına itfa değeri!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "PRICEDISC": {"a": "(mutabakat; vade; indirim; itfa; [temel])", "d": "İndirimli bir menkul kıymet için 100 TL nominal değer başına fiyatı döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin indirim oranı!menkul kıymetin 100 TL nominal değer başına itfa değeri!kullanılacak gün sayısı türü"}, "PRICEMAT": {"a": "(mutabakat; vade; çıkış; oran; getiri; [temel])", "d": "Vadesinde faiz ödeyen bir menkul kıymet için 100 TL nominal değer başına fiyatı döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin çıkış tarihi, tarih seri numarası cinsinden!menkul kıymetin çıkış tarihindeki faiz oranı!menkul kıymetin yıllık getirisi!kullanılacak gün sayısı türü"}, "PV": {"a": "(oran; döne<PERSON>_sayısı; dö<PERSON>msel_ödeme; [gd]; [tür])", "d": "Bir yatırımın bugünkü değerini verir: gelecekte yapılacak bir dizi ödemenin bugünkü toplam değeri", "ad": "dönem başına faiz oranı. <PERSON><PERSON><PERSON><PERSON>, %6 yıllık faiz oranına karşılık üç aylık ödeme için %6/4 kullanın!yatırımın toplam ödeme dönemi sayısı!her dönem yapılan ödeme, yatırım süresi boyunca değişmez!gelecek değer ya da son ödeme yapıldıktan sonra elde edilmek istenen nakit bakiyesi!mantıksal değer: dönem başında ödeme = 1; dönem sonunda ödeme = 0 ya da atlanmış"}, "RATE": {"a": "(dö<PERSON><PERSON>_sayısı; dönemsel_ödeme; bd; [gd]; [tür]; [tahmin])", "d": "Bir borç ya da yatırım için dönem başına faiz oranını verir. Örneğin, %6 yıllık faiz oranına karşılık üç aylık ödeme için %6/4 kullanın", "ad": "yatırım ya da borç için toplam ödeme dönemi sayısı!her dönem yapılan ödeme, yatırım ya da borç süresi boyunca değişmez!bugünkü değer: gelecekte yapılacak bir dizi ödemenin bugünkü değerini gösteren toplam miktar!gelecek değer ya da son ödeme yapıldıktan sonra elde edilmek istenen nakit bakiyesi. Atlanırsa, Gd = 0!mantıksal değer: dönem başında ödeme = 1; dönem sonunda ödeme = 0 ya da atlanmış!oran için sizin tahmininiz; atlanırsa, <PERSON>hmin = 0,1 (yüzde 10)"}, "RECEIVED": {"a": "(mutabakat; vade; yatırım; indirim; [temel])", "d": "Tam olarak yatırım yapılan bir menkul kıymetin vadesindeki getiri miktarını döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymete yatırılan miktar!menkul kıymetin indirim oranı!kullanılacak gün sayısı türü"}, "RRI": {"a": "(döne<PERSON>_sayısı; bd; gd)", "d": "Yatırımın büyümesi için eşdeğer bir faiz oranı verir", "ad": "yatırım dönemlerinin sayısı!yatırımın bugünkü değeri!yatırımın gelecekteki değeri"}, "SLN": {"a": "(maliyet; hurda; ömür)", "d": "Bir malın bir dönem için doğrusal yıpranmasını verir", "ad": "malın ilk maliyeti!malın kullanım ömrü bittikten sonraki hurda değeri!malın yıpranma dönemi sayısı (bazen malın kullanım ömrü olarak da kullanılır)"}, "SYD": {"a": "(mali<PERSON>t; hurda; öm<PERSON>r; dö<PERSON><PERSON>)", "d": "Bir malın belirtilen bir dönem için yıpranmasını verir", "ad": "malın ilk maliyeti!malın kullanım ömrü bittikten sonraki hurda değeri!malın yıpranma dönemi miktarı (bazen malın kullanım ömrü olarak da kullanılır)!Ömür birimiyle aynı birimde olması gereken dönem"}, "TBILLEQ": {"a": "(d<PERSON><PERSON><PERSON><PERSON>; vade; indirim)", "d": "Hazine tahvili için bono eşdeğerini döndürür", "ad": "Hazine tahvilinin düzen<PERSON>e tarihi, tarih seri numarası cinsinden!Hazine tahvilinin vade tarihi, tarih seri numarası cinsinden!Hazine tahvilinin indirim oranı"}, "TBILLPRICE": {"a": "(d<PERSON><PERSON><PERSON><PERSON>; vade; indirim)", "d": "Hazine tahvili için 100 TL başına yüz değerini döndürür", "ad": "Hazine tahvilinin düzen<PERSON>e tarihi, tarih seri numarası cinsinden!Hazine tahvilinin vade tarihi, tarih seri numarası cinsinden!Hazine tahvilinin indirim oranı"}, "TBILLYIELD": {"a": "(d<PERSON><PERSON><PERSON><PERSON>; vade; <PERSON><PERSON>t)", "d": "Hazine tahvili i<PERSON><PERSON> dö<PERSON>ürür", "ad": "Hazine tahvilinin düzen<PERSON>e tarihi, tarih seri numarası cinsinden!Hazine tahvilinin vade tarihi, tarih seri numarası cinsinden!Hazine tahvilinin 100 TL başına yüz değeri fiyatı"}, "VDB": {"a": "(ma<PERSON><PERSON><PERSON>; hurda; <PERSON>m<PERSON><PERSON>; ba<PERSON><PERSON><PERSON><PERSON>_döne<PERSON>; son_döne<PERSON>; [fakt<PERSON><PERSON>]; [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Çift azalan bakiye yöntemini ya da belirttiğiniz başka bir yöntemi kullanarak, kı<PERSON>i dönemleri de içeren belirli bir dönem için bir malın amortismanını verir", "ad": "malın ilk maliyeti!malın kullanım ömrü bittikten sonraki hurda değeri!malın yıpranma dönemi miktarı (bazen malın kullanım ömrü olarak da kullanılır)!yıpranmayı hesaplamak istediğiniz başlangıç dönemi, Ömür ile aynı birimde!yıpranmayı hesaplamak istediğiniz bitiş dönemi, <PERSON><PERSON><PERSON>r ile aynı birimde!bakiyenin azalma oranı, atlanırsa 2 (çift-azal<PERSON> bakiye)!yıpranma azalan bakiyeden büyük olduğunda doğrusal yıpranmaya geçiş yap = YANLIŞ ya da atlanmış; geçiş yapma = DOĞRU"}, "XIRR": {"a": "(<PERSON><PERSON><PERSON><PERSON>; ta<PERSON><PERSON>; [tahmin])", "d": "Nakit akışı planı için iç verim oranını döndürür", "ad": "tarihlerdeki ödeme planına karşılık gelen nakit akışı serisi!nakit akışı ödemelerine karşılık gelen ödeme tarihleri planı!AİÇVERİMORANI işlevinin sonucuna yakın olarak tahmin edilen sayı"}, "XNPV": {"a": "(oran; <PERSON><PERSON><PERSON><PERSON>; ta<PERSON><PERSON>)", "d": "Nakit akışı planı için bugünkü net değeri döndürür", "ad": "nakit akışına uygulanacak indirim oranı!tarihlerdeki ödeme planına karşılık gelen nakit akışı serisi!nakit akışı ödemelerine karşılık gelen ödeme tarihleri planı"}, "YIELD": {"a": "(mutabakat; vade; oran; fiyat; itfa; sıklık; [temel])", "d": "Dönemsel faiz ödeyen bir menkul kıymetin getirisini döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin yıllık kupon oranı!menkul kıymetin 100 TL nominal değer başına fiyatı!menkul kıymetin 100 TL nominal değer başına itfa değeri!yıllık kupon ödemesi sayısı!kullanılacak gün sayısı türü"}, "YIELDDISC": {"a": "(mutabakat; vade; fiyat; itfa; [temel])", "d": "İndirimli bir menkul kıymet için yıllık getiriyi döndürür, <PERSON><PERSON><PERSON><PERSON> hazine bonosu", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin 100 TL nominal değer başına fiyatı!menkul kıymetin 100 TL nominal değer başına itfa değeri!kullanılacak gün sayısı türü"}, "YIELDMAT": {"a": "(mutabakat; vade; çıkış; oran; fiyat; [temel])", "d": "Vadesinde faiz ödeyen bir menkul kıymet için yıllık getiriyi döndürür", "ad": "menkul kıymetin mutabakat tarihi, tarih seri numarası cinsinden!menkul kıymetin vade tarihi, tarih seri numarası cinsinden!menkul kıymetin çıkış tarihi, tarih seri numarası cinsinden!menkul kıymetin çıkış tarihindeki faiz oranı!menkul kıymetin 100 TL nominal değer başına fiyatı!kullanılacak gün sayısı türü"}, "ABS": {"a": "(sayı)", "d": "<PERSON><PERSON> sayının mutlak <PERSON> verir, <PERSON><PERSON><PERSON><PERSON> o<PERSON> sayı", "ad": "mutlak değerini istediğiniz gerçek sayı"}, "ACOS": {"a": "(sayı)", "d": "Bir sayının a<PERSON>ü<PERSON> verir, radyan cinsinde ve 0 - Pi aralığındadır. <PERSON><PERSON><PERSON><PERSON><PERSON>, kosinüsü Sayı olan açıdır", "ad": "istediğiniz açının kosinüs değeri, -1 ile 1 arasında olmalıdır"}, "ACOSH": {"a": "(sayı)", "d": "B<PERSON> sayının ters hiperbolik kosinüsünü verir", "ad": "1 'e eşit veya 1'den büyük herhangi bir gerçek sayı"}, "ACOT": {"a": "(sayı)", "d": "Bir sayının arkkotanjantını 0 ile Pi aralığındaki radyanlar cinsinden verir.", "ad": "istediğiniz açının kotanjantı"}, "ACOTH": {"a": "(sayı)", "d": "B<PERSON> sayının ters hiperbolik kotanjant değerini verir", "ad": "istenen açının hiperbolik kotanjantı"}, "AGGREGATE": {"a": "(işlev_num; seçenekler; başv1; ...)", "d": "Bir liste veya veritabanından bir toplam verir", "ad": "toplamı hesaplamak için kullanılan özet işlevini belirten 1-19 arasındaki sayı.!toplama işlevinde yok sayılacak değerleri belirten 0 ile 7 arasındaki sayı!toplamı hesaplanacak sayısal veri dizisi veya aralığı!dizideki konumu gösterir; k. en büyük, k. en küçük, k. yüzdebir veya k. dörttebirdir.!toplamı hesaplamak için kullanılan özet işlevini belirten 1-19 arasındaki sayı.!toplama işlevinde yok sayılacak değerleri belirten 0 ile 7 arasındaki sayı!toplamını almak istediğiniz en az 1 en fazla 253 başvuru veya aralık"}, "ARABIC": {"a": "(metin)", "d": "Bir Roma rakamını Arap rakamına dönüştürür", "ad": "dönüştürmek istediğiniz Roma rakamı"}, "ASC": {"a": "(metin)", "d": "Çift bayt karakter kü<PERSON> (DBCS) dillerde, i<PERSON><PERSON> tam geni<PERSON> (çift bayt) karakterleri yarı geni<PERSON> (tek bayt) karakterlere dönüştürür", "ad": "Değiştirmek istediğiniz metin veya değiştirmek istediğiniz metnin bulunduğu hücreye başvurudur."}, "ASIN": {"a": "(sayı)", "d": "Bir sayının radyan cinsinden -Pi/2 ile Pi/2 aralığındaki arksinüsünü verir", "ad": "istediğiniz açının sinüs değeri, -1 ile 1 arasında olmalıdır"}, "ASINH": {"a": "(sayı)", "d": "B<PERSON> sayının ters hiperbolik sinüsünü verir", "ad": "1'e eşit veya 1'den büyük herhangi bir gerçek sayı"}, "ATAN": {"a": "(sayı)", "d": "Bir sayının radyan cinsinden -Pi/2 ile Pi/2 aralığındaki arktanjantını verir", "ad": "istediğiniz açının tanjantı"}, "ATAN2": {"a": "(x_sayısı; y_sayısı)", "d": "Belirtilen x- ve y- koordinatlarının radyan cinsinden -Pi (-<PERSON> hariç) ile Pi arasındaki arktanjantını verir", "ad": "noktanın x-koordinatı!noktanın y-koordinatı"}, "ATANH": {"a": "(sayı)", "d": "Bir sayının ters hiperbolik tanjantını verir", "ad": "-1 ve 1 arasında (-1 ve 1 hariç) herhangi bir gerçek sayı"}, "BASE": {"a": "(sayı; sayıtabanı; [min_uzunluk])", "d": "Bir sayıyı verilen sayı tabanı (temel) ile bir metin gösterimine dönüştürür", "ad": "dönüştürmek istediğiniz sayı!sayıyı dönüştürmek istediğiniz temel Sayı Tabanı!döndürülen dizenin minimum uzunluğu. Atlanırsa baştaki sıfırlar eklenmez"}, "CEILING": {"a": "(sayı; anlam)", "d": "<PERSON><PERSON> sayıyı, yukarı doğru en yakın anlamlı sayı katına yuvarlar", "ad": "yuvarlamak istediğiniz değer!yuvarlamak istediğiniz kat"}, "CEILING.MATH": {"a": "(sayı; [anlam]; [mod])", "d": "<PERSON><PERSON> sayıyı, yukarı doğru en yakın tamsayı veya anlamlı sayı katına yuvarlar", "ad": "yuvarlamak istediğiniz değer!sayıyı yuvarlamak istediğiniz kat!belirtildiğinde ve sıfır olmadığında bu işlev sıfırdan ıraksayarak yuvarlar"}, "CEILING.PRECISE": {"a": "(sayı; [anlam])", "d": "En yakın tam sayıya ya da en yakın katına yuvarlanmış sayıyı verir", "ad": "yuvarlamak istediğiniz değer!yuvarlamak istediğiniz kat"}, "COMBIN": {"a": "(sayı; sayı_seçilen)", "d": "Verilen öğelerin sayısı için kombinasyon sayısını verir", "ad": "toplam öğe sayısı!her kombinasyonda kullanılan öğe sayısı"}, "COMBINA": {"a": "(sayı; sayı_seçilen)", "d": "Verilen sayıda öğe için yinelemelerle birleşimlerin sayısını verir", "ad": "<PERSON><PERSON><PERSON><PERSON> toplam sayısı!her birleşimdeki öğe sayısı"}, "COS": {"a": "(sayı)", "d": "Bir açının kosinüsünü verir", "ad": "kosinüs değerini almak istediğiniz radyan cinsinden açı"}, "COSH": {"a": "(sayı)", "d": "Bir sayının hiperbolik kosinüsünü verir", "ad": "<PERSON><PERSON><PERSON> bir ger<PERSON><PERSON> sayı"}, "COT": {"a": "(sayı)", "d": "Bir açının kotanjant değerini verir", "ad": "kotanjantı istenen radyan cinsinden açı"}, "COTH": {"a": "(sayı)", "d": "Bir sayının hiperbolik kotanjant değerini verir", "ad": "hiperbolik kotanjantı istenen radyan cinsinden açı"}, "CSC": {"a": "(sayı)", "d": "Bir açının kosekant değerini verir", "ad": "kosekantı istenen radyan cinsinden açı"}, "CSCH": {"a": "(sayı)", "d": "Bir açının hiperbolik kosekant değerini verir", "ad": "hiperbolik kosekantı istenen radyan cinsinden açı"}, "DECIMAL": {"a": "(sayı; sayıtabanı)", "d": "Verilen temeldeki bir sayının metin gösterimini ondalık bir sayıya dönüştürür", "ad": "dönüştürmek istediğiniz sayı!dönüştürdüğünüz sayının temel Sayı Tabanı"}, "DEGREES": {"a": "(açı)", "d": "Radyanı dereceye <PERSON>", "ad": "dönüştürmek istediğiniz radyan cinsinden açı"}, "ECMA.CEILING": {"a": "(sayı; anlam)", "d": "<PERSON><PERSON> sayıyı, yukarı doğru en yakın anlamlı sayı katına yuvarlar", "ad": "yuvarlamak istediğiniz değer!yuvarlamak istediğiniz kat"}, "EVEN": {"a": "(sayı)", "d": "<PERSON><PERSON> sayıyı, mutlak değerce kendinden büyük en yakın çift tamsayıya yuvarlar", "ad": "yuvar<PERSON><PERSON><PERSON>"}, "EXP": {"a": "(sayı)", "d": "Verilen bir sayının üssünün e sayısının üssü olarak kullanılması ile oluşan sonucu verir", "ad": "e tabanına uygulanan üs. Doğal logaritmanın temeli olan e sabiti 2,71828182845904 değerine eşittir"}, "FACT": {"a": "(sayı)", "d": "Bir sayının 1*2*3*...*Sayı şeklinde çarpınımını verir", "ad": "çarpınımını almak istediğiniz negatif olma<PERSON> sayı"}, "FACTDOUBLE": {"a": "(sayı)", "d": "Verilen bir sayıdan bire kadar ikişer ikişer azalarak oluşan sayıların çarpımını döndürür.", "ad": "işlevin uygulanacağı sayı"}, "FLOOR": {"a": "(sayı; anlam)", "d": "<PERSON><PERSON> sayıyı, anlamlı en yakın katına, aşağ<PERSON> doğru yuvarlar", "ad": "yuvarlamak istediğiniz sayısal değer!yuvarlamak istediğiniz kat. <PERSON><PERSON> <PERSON>n her ikisi de negatif ya da her ikisi de pozitif olmalıdır"}, "FLOOR.PRECISE": {"a": "(sayı; [anlam])", "d": "En yakın sayıya veya en yakın anlam katına aşağı yuvarlanmış bir sayı verir", "ad": "yuvarlamak istediğiniz değer!yuvarlamak istediğiniz kat"}, "FLOOR.MATH": {"a": "(sayı; [anlam]; [mod])", "d": "<PERSON><PERSON> sayıyı, aşağı doğru en yakın tamsayı veya anlamlı sayı katına yuvarlar", "ad": "yuvarlamak istediğiniz değer!yuvarlamak istediğiniz kat!verilen ve sıfır olmayan bu işlev sıfıra doğru yuvarlanır"}, "GCD": {"a": "(sayı1; [sayı2]; ...)", "d": "En büyük ortak böleni döndürür", "ad": "En az 1 en çok 255 değer"}, "INT": {"a": "(sayı)", "d": "<PERSON><PERSON> sayıyı, sı<PERSON><PERSON>rdan ıraksayarak en yakın tam sayıya yuvarlar", "ad": "bir tam sayıya aşağıya yuvarlamak istediğiniz gerçek sayı"}, "ISO.CEILING": {"a": "(sayı; [anlam])", "d": "En yakın tam sayıya ya da en yakın katına yuvarlanmış sayıyı verir. Sayının işareti dikkate alınmadan sayı yuvarlanır. <PERSON><PERSON><PERSON>, sayı veya anlam sıfırsa, sıfır verilir.", "ad": "yuvarlamak istediğiniz değer!yuvarlamak istediğiniz kat"}, "LCM": {"a": "(sayı1; [sayı2]; ...)", "d": "En küçük ortak çarpanı döndürür", "ad": "en küçük ortak çarpanını istediğiniz en az 1 en çok 255 değer"}, "LN": {"a": "(sayı)", "d": "Bir sayının doğal logaritmasını verir", "ad": "doğal logaritmasını almak istediğiniz pozitif gerçek sayı"}, "LOG": {"a": "(sayı; [taban])", "d": "Bir sayının belirttiğiniz tabandaki logaritmasını alır", "ad": "logaritmasını almak istediğiniz pozitif gerçek sayı!logaritma tabanı; atlanırsa 10"}, "LOG10": {"a": "(sayı)", "d": "Bir sayının 10 tabanında logaritmasını verir", "ad": "10 tabanında logaritmasını almak istediğiniz pozitif gerçek sayı"}, "MDETERM": {"a": "(dizi)", "d": "Bir dizinin determinantını verir", "ad": "eşit sayıda satır ve sütuna sahip olan sayısal dizi, ya da bir hücre aralığı veya bir dizi sabiti"}, "MINVERSE": {"a": "(dizi)", "d": "Bir dizide saklanan bir dizeyin tersini verir", "ad": "eşit sayıda satır ve sütuna sahip olan sayısal dizi, ya da bir hücre aralığı veya bir dizi sabiti"}, "MMULT": {"a": "(dizi1; dizi2)", "d": "İki dizinin dizey çarpımını verir, sonu<PERSON>, dizi1 ile aynı sayıda satıra ve dizi2 ile aynı sayıda sütuna sahip olan bir dizidir", "ad": "çarpmak istediğiniz sayılar dizisidir ve sütun sayısı Dizi2'nin satır sayısıyla aynı olmalıdır"}, "MOD": {"a": "(sayı; bölen)", "d": "<PERSON>ir sayının bir bölen tarafından bölünmesinden sonra kalanı verir", "ad": "b<PERSON><PERSON>e işlemi sonucunda kalanını bulmak istediğiniz sayıdır!Sayıyı bölen sayı"}, "MROUND": {"a": "(sayı; katsayı)", "d": "İstenen katsayıya yuvarlanmış bir sayı döndürür", "ad": "yuvarlanacak değer!sayıyı yuvarlamak istediğiniz katsayı"}, "MULTINOMIAL": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir sayı kümesinin çok terimli değerini döndürür", "ad": "çok terimli değerini istediğiniz en az 1 en çok 255 değer"}, "MUNIT": {"a": "(boyut)", "d": "<PERSON><PERSON><PERSON><PERSON> boyut için birim matris döndür<PERSON>r", "ad": "döndürmek istediğiniz birim matrisin boyutunu belirten tamsayı"}, "ODD": {"a": "(sayı)", "d": "<PERSON><PERSON> sayıyı, mutlak değerce kendinden büyük en yakın tek tamsayıya yuvarlar", "ad": "yuvar<PERSON><PERSON><PERSON>"}, "PI": {"a": "()", "d": "<PERSON>, 15 r<PERSON><PERSON> kadar y<PERSON><PERSON><PERSON> hali 3,14159265358979'dur", "ad": ""}, "POWER": {"a": "(sayı; üs)", "d": "Üssü alınmış sayının sonucunu verir", "ad": "taban sayısı, her<PERSON>i bir gerçek sayı!belirli bir tabanda olan sayının üssü"}, "PRODUCT": {"a": "(sayı1; [sayı2]; ...)", "d": "Bağımsız değişken olarak verilen tüm sayıları çarpar", "ad": "çarpımını bulmak istediğiniz en az 1 en fazla 255 sayı, mantıksal değer ya da sayıları temsil eden metindir"}, "QUOTIENT": {"a": "(pay; payda)", "d": "Bir bölmenin tamsayı kısmını döndürür", "ad": "bölünen!bölen"}, "RADIANS": {"a": "(açı)", "d": "<PERSON><PERSON><PERSON><PERSON> r<PERSON> d<PERSON>", "ad": "dönüştürmek istediğiniz derece cinsinden açı"}, "RAND": {"a": "()", "d": "0 ya da 0'dan büyük ve 1'den küçük bir sayıyı eşit dağılımla rastgele verir (yeniden hesaplama sonucunda değişir)", "ad": ""}, "RANDARRAY": {"a": "([satırlar]; [sü<PERSON><PERSON>]; [min]; [maks]; [tamsayı])", "d": "<PERSON><PERSON> rastgele sayı dizisini döndürür", "ad": "döndürülen dizideki satır sayısı!döndürülen dizideki sütun sayısı!döndürmek istediğiniz en küçük sayı!döndürmek istediğiniz en büyük sayı!bir tamsayı veya ondalık değer döndürür. Tamsayı için DOĞRU, ondalık sayı için YANLIŞ"}, "RANDBETWEEN": {"a": "(alt; üst)", "d": "Belirttiğiniz sayılar arasında rastgele bir sayı döndürür", "ad": "RASTGELEARADA işlevinin döndüreceği en küçük tamsayı!RASTGELEARADA işlevinin döndüreceği en büyük tamsayı"}, "ROMAN": {"a": "(sayı; [form])", "d": "<PERSON>p raka<PERSON>ını metin biçimiyle romen rakamlarına dönüştürür", "ad": "dönüştürmek istediğiniz sayı!hangi tür romen sayısı istediğinizi belirten sayı."}, "ROUND": {"a": "(sayı; sayı_raka<PERSON>)", "d": "Sayıyı belirli sayıdaki rakama yuvar<PERSON>", "ad": "yuvarlamak istediğiniz sayı!sayıyı yuvarlamak istediğiniz rakam sayısı. Negatif, ondalık noktasının soluna; sıfır ise en yakın tamsayıya yuvarlanır"}, "ROUNDDOWN": {"a": "(sayı; sayı_raka<PERSON>)", "d": "Bir sayıyı sıfıra yakınsayarak yuvarlar", "ad": "aşağı yuvarlamak istediğiniz herhangi bir gerçek sayı!sayıyı yuvarlamak istediğiniz rakam sayısı. Negatif sayılar ondalık noktasının soluna; sıfır ya da atlanmış olanlar ise en yakın tamsayıya yuvarlanır"}, "ROUNDUP": {"a": "(sayı; sayı_raka<PERSON>)", "d": "Bir sayıyı sıfırdan ıraksayarak yukarı yuvarlar", "ad": "yukarı yuvarlamak istediğiniz herhangi bir gerçek sayı!sayıyı yuvarlamak istediğiniz rakam sayısı. Negatif sayılar ondalık noktasının soluna; sıfır ya da atlanmış olanlar ise en yakın tamsayıya yuvarlanır"}, "SEC": {"a": "(sayı)", "d": "Bir açının sekant değerini verir", "ad": "sekantı istenen radyan cinsinden açı"}, "SECH": {"a": "(sayı)", "d": "Bir açının hiperbolik sekant değerini verir", "ad": "hiperbolik sekantı istenen radyan cinsinden açı"}, "SERIESSUM": {"a": "(x; n; m; katsay<PERSON><PERSON>)", "d": "Form<PERSON>le dayalı olan kuvvet serisinin toplamını döndürür", "ad": "kuvvet serisi için giriş değ<PERSON>!x'in yükseltileceği ilk kuvvet!serideki her öğe için n'nin artırma oranı!x'in ardıl kuvvetlerinin çarpıldığı katsayılar kümesi"}, "SIGN": {"a": "(sayı)", "d": "Bir sayının işaretini verir: sayı pozitif ise 1, sı<PERSON>ır ise sıfır, negatif ise -1", "ad": "<PERSON><PERSON><PERSON> bir ger<PERSON><PERSON> sayı"}, "SIN": {"a": "(sayı)", "d": "<PERSON>ir açının <PERSON> verir", "ad": "sinüsünü almak istediğiniz radyan cinsinden açı. * PI()/180 derece = radyan"}, "SINH": {"a": "(sayı)", "d": "Bir sayının hiperbolik sinüsünü verir", "ad": "<PERSON><PERSON><PERSON> bir ger<PERSON><PERSON> sayı"}, "SQRT": {"a": "(sayı)", "d": "<PERSON><PERSON> sayının karekökünü verir", "ad": "karekökünü almak istediğiniz sayı"}, "SQRTPI": {"a": "(sayı)", "d": "Sayının Pi sayısıyla çarpımının karekökünü döndürür", "ad": "pi sayısıyla çarpılacak sayı"}, "SUBTOTAL": {"a": "(iş<PERSON>_sayısı; başv1; ...)", "d": "Bir liste veya veritabanından bir alt toplam verir", "ad": "alt toplamı hesaplamak için kullanılan özet işlevini belirten 1-11 arasındaki sayı.!alt toplamını almak istediğiniz en az 1 en fazla 254 başvuru veya aralıktır"}, "SUM": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir hücre aralığındaki tüm sayıları toplar", "ad": "toplanacak en az 1 en fazla 255 sayıdır. Mantıksal <PERSON> ve metin, hü<PERSON><PERSON>de yoks<PERSON>ıl<PERSON>, ancak bağımsız değişken olarak girilmişlerse eklenirler"}, "SUMIF": {"a": "(aralık; ölçüt; [toplam_aralığı])", "d": "Verilen bir koşul ya da ölçüt tarafından belirtilen hücreleri toplar", "ad": "hesaplamak istediğiniz hücreler aralığı!hangi hücrelerin toplanacağını tanımlayan sayı, ifade ya da metin formundaki ölçüt veya koşul!toplanacak gerçek hücreler. Atlanırsa, aralıktaki hücreler kullanılır"}, "SUMIFS": {"a": "(aralık_toplamı; ölçüt_aralığı; ölçüt; ...)", "d": "Verili bir koşul veya ölçüt kümesi tarafından belirtilen hücreleri toplar", "ad": "toplanacak asıl hücreler.!belirli bir koşula göre hesaplanmasını istediğiniz hücre aralığı!toplamı alınacak hücreleri tanımlayan, sayı, ifade veya metin biçimindeki koşul veya ölçüt"}, "SUMPRODUCT": {"a": "(dizi1; [dizi2]; [dizi3]; ...)", "d": "Verilen aralık ya da dizilerde birbirine karşılık gelen sayısal bileşenleri çarpar ve bu çarpımların toplamını verir", "ad": "Bileşenlerini önce çarpıp sonra toplamak istediğiniz en az 2 en fazla 255 dizidir. Tüm diziler aynı boyutlara sahip olmalıdır"}, "SUMSQ": {"a": "(sayı1; [sayı2]; ...)", "d": "Bağımsız değişkenlerin karelerinin toplamını verir. Bağımsız değişkenler sayı, ad, dizi, ya da sayı içeren hücre başvuruları olabilir", "ad": "ka<PERSON>er toplamını bulmak istediğiniz en az 1 en fazla 255 sayı, ad, dizi ya da dizi başvurusudur"}, "SUMX2MY2": {"a": "(dizi_x; dizi_y)", "d": "Birbirine karşılık gelen iki aralık ya da dizideki sayıların kareleri arasındaki farkı hesaplar ve sonra da bu farkların toplamını verir", "ad": "ilk sayı dizisi ya da aralığıdır ve sayı, ad, dizi, ya da sayı içeren başvurular olabilir!ikinci sayı aralığı veya dizisidir ve sayı, ad, dizi, ya da sayı içeren başvuru olabilir"}, "SUMX2PY2": {"a": "(dizi_x; dizi_y)", "d": "Birbirine karşılık gelen iki aralık ya da dizideki sayıların karelerinin toplamlarını hesaplar ve sonra da bu toplamların toplamını verir", "ad": "ilk sayı dizisi ya da aralığıdır ve sayı, ad, dizi, ya da sayı içeren başvurular olabilir!ikinci sayı aralığı veya dizisidir ve sayı, ad, dizi, ya da sayı içeren başvuru olabilir"}, "SUMXMY2": {"a": "(dizi_x; dizi_y)", "d": "Birbirine karşılık gelen iki aralık ya da dizideki değerlerin farklarını hesaplar ve sonra da bu farkların kareleri toplamını verir", "ad": "ilk değerler aralığı ya da dizisidir ve sayı, ad, dizi, ya da sayı içeren başvuru olabilir!ikinci değerler aralığı ya da dizisidir ve sayı, ad, dizi, ya da sayı içeren başvuru olabilir"}, "TAN": {"a": "(sayı)", "d": "<PERSON><PERSON> sayının tanjantını verir", "ad": "tanjantını almak istediğiniz radyan cinsinden olan açı. * PI()/180 derece = radyan"}, "TANH": {"a": "(sayı)", "d": "Bir sayının hiperbolik tanjantını verir", "ad": "<PERSON><PERSON><PERSON> bir ger<PERSON><PERSON> sayı"}, "TRUNC": {"a": "(sayı; [sayı_raka<PERSON><PERSON>])", "d": "Bir sayıyı ondalık ya da kesir kısmını kaldırarak bir tamsayıya yuvarlar", "ad": "yuvarlamak istediğiniz sayı!yuvarlamanın du<PERSON>lılığını belirten sayı, atlanırsa 0 (sıfır) kullanılır"}, "ADDRESS": {"a": "(satır_num; sütun_num; [mutlak_num]; [a1]; [say<PERSON>_metni])", "d": "<PERSON><PERSON> hü<PERSON> ba<PERSON><PERSON><PERSON><PERSON>, beli<PERSON><PERSON>n satır ve sütun numaraları verilmiş halde metin olarak oluşturur", "ad": "hücre başvurusunda kullanılacak satır numarası: satır 1 için, Satır_num = 1 !hücre başvurusunda kullanılacak sütun numarası. <PERSON><PERSON><PERSON><PERSON>, D sütunu için Sütun_num = 4!başvuru türünü belirtir: mutlak = 1; mutlak satır/göreceli sütun = 2; göreceli satır/mutlak sütun = 3; göreceli = 4!başvuru stilini belirten mantıksal değer: A1 stili = 1 ya da DOĞRU; R1C1 stili = 0 ya da YANLIŞ!dış başvuru olarak kullanılacak çalışma sayfasının adını belirten metin"}, "CHOOSE": {"a": "(dizin_sayısı; değer1; [değer2]; ...)", "d": "Bir dizin numarasını temel alan bir değerler listesinden gerçekleştirmek üzere bir değer ya da eylem seçer", "ad": "hangi değer bağımsız değişkeninin seçildiğini belirtir. Dizin_num 1 ile 254 arasında, bir formül ya da 1 ile 254 arasındaki bir sayıya başvuru olmalıdır!ELEMAN ile seçilen en az 1 en çok 254 sayı, h<PERSON><PERSON> baş<PERSON><PERSON>u, tan<PERSON><PERSON><PERSON><PERSON><PERSON>ş ad, form<PERSON><PERSON>, işlev ya da metin biçiminde bağımsız değer olabilir"}, "COLUMN": {"a": "([ba<PERSON><PERSON><PERSON>])", "d": "Başvurunun sütun say<PERSON>ını verir", "ad": "sütun sayısını öğrenmek istediğiniz hücre veya bitişik hücreler aralığı. Atlanırsa, SÜTUN işlevini içeren hücre kullanılır"}, "COLUMNS": {"a": "(dizi)", "d": "Bir dizideki ya da başvurudaki sütun sayısını verir", "ad": "sütun sayısını öğrenmek için kullanılan dizi, dizi formülü ya da bir hücre aralığı başvurusu"}, "FORMULATEXT": {"a": "(baş<PERSON>ru)", "d": "<PERSON><PERSON><PERSON><PERSON> bir dize olarak verir", "ad": "bir <PERSON><PERSON><PERSON> b<PERSON>"}, "HLOOKUP": {"a": "(a<PERSON><PERSON>_<PERSON><PERSON><PERSON>; tablo_dizisi; satır_indis_sayısı; [aralık_bak])", "d": "Tablonun üst satırındaki değeri ya da değerler dizisini arar ve aynı sütunda belirtilen satırdan değeri verir", "ad": "tablonun ilk satırında bulunması gereken değerdir ve bir değer, başvuru veya metin olabilir!verinin arandığı metin tablosu, sayılar ya da mantıksal değerlerdir. Tablo_dizisi bir aralığa ya da bir aralık adına yapılan bir başvuru olabilir!eşleşen değerin geleceği tablo_dizisi'nde bulunan satır sayısı. Tablodaki ilk veri satırı satır 1'dir!mantıksal değer: üst satırdaki (artan sırada sıralanmış) en yakın eşleşmeyi bulmak için = DOĞRU ya da atlanmış; tam eşleşmeyi bulmak için = YANLIŞ"}, "HYPERLINK": {"a": "(bağ_konumu; [yakın_ad])", "d": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, sun<PERSON><PERSON> ağı ya da İnternet'te depolanmış olan bir belgeyi açmak için kısayol ya da atlama oluşturur", "ad": "açılması istenen belgeye giden yolu ve dosya adını veren metindir, bir sabit sür<PERSON><PERSON><PERSON> konumu, UNC adresi, ya da URL yolu!hücrede görüntülenen metin ya da sayıdır. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hücre bağ_konumu metnini gö<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "INDEX": {"a": "(dizi; satır_sayısı; [sütun_sayısı]!başv; satır_sayısı; [sütun_sayısı]; [alan_sayısı])", "d": "Bir tablo ya da aralıktan bir değer ya da değere yapılan başvuruyu verir", "ad": "bir hücre<PERSON> aralığı ya da dizi sabiti.!dizi ya da Başvuru'da, almak istediğiniz değeri veren satırı seçer. Atlanırsa, Süt_num gerekir!dizi ya da Başvuru'da, almak istediğiniz değeri veren sütunu seçer. Atlanırsa, Sat_num gerekir!bir veya daha fazla hücre aralığı başvurusu!dizi ya da Başvuru'da, almak istediğiniz değeri veren satırı seçer. Atlanırsa, Süt_num gerekir!dizi ya da Başvuru'da, almak istediğiniz değeri veren sütunu seçer. Atlanırsa, Sat_num gerekir!başvuru'da almak istediğiniz değeri veren aralığı seçer. Seçilen ya da girilen ilk alan alan 1, ikincisi alan 2, ve bu ş<PERSON>ilde devam eder"}, "INDIRECT": {"a": "(başv_metni; [a1])", "d": "Bir metin dizesiyle beli<PERSON>ş başvuruyu verir", "ad": "A1 ya da R1C1-stili başvurusu içeren hücre başvurusu, baş<PERSON>ru olarak tanımlanmış bir ad ya da metin dizesi halindeki bir hücre başvurusu!Başv_metni'nde geçen başvuru türünü belirten mantıksal değer: R1C1-stili = YANLIŞ; A1-stili = DOĞRU ya da atlanmış"}, "LOOKUP": {"a": "(aranan_de<PERSON>er; aranan_vektör; [sonuç_vektör]!aranan_değer; dizi)", "d": "Tek-satırlı ya da tek-sütunlu bir aralıktan ya da bir diziden bir değer verir. Geriye dönük uyumluluk için sağlanmıştır", "ad": "ARA'nın ara_vektörü içerisinde arayacağı değerdir ve bir sayı, metin, mant<PERSON><PERSON><PERSON> de<PERSON>, ya da bir değer adı veya bir değer başvurusu olabilir!sadece bir satır veya sütundan oluşan ve artan sıraya yerleştirilmiş olan metin, sayı ya da mantıksal değerler içeren aralık!sadece bir satır veya sütun içeren aralıktır, Ara_vektörü ile aynı boyuttadır!ARA'nın bir Dizi içerisinde arayacağı değerdir ve bir sayı, metin, mant<PERSON><PERSON><PERSON> de<PERSON><PERSON>, ya da bir değer adı veya bir değer başvurusu olabilir!Aranan_değer'le karşılaştırmak istediğiniz metin,sayı veya mantıksal değer içeren hücreler aralığı"}, "MATCH": {"a": "(aranan_de<PERSON><PERSON>; aranan_dizi; [e<PERSON><PERSON><PERSON><PERSON><PERSON>_tür])", "d": "Belirli bir sırada belirtilen değerle eşleşen bir öğenin bir dizi içerisindeki göreceli konumunu verir", "ad": "dizi içerisinde aradığınız değeri bulmak için kullandığınız de<PERSON>, say<PERSON>, metin, mantıks<PERSON> değer ya da bunlardan birine yapılan başvuru!aranan değerleri içeren ardışık hücreler aralığı, değerler dizisi ya da dizi başvurusu!gelen değeri işaret eden 1, 0 ya da -1 sayısı."}, "OFFSET": {"a": "(başv; satırlar; sü<PERSON><PERSON>; [yüks<PERSON><PERSON>]; [g<PERSON><PERSON><PERSON>])", "d": "Bir hücre ya da hücreler aralığında belirtilen satır ve sütun sayısına karşılık gelen bir aralığa yapılan başvuruyu verir", "ad": "göreceli konuma temel oluşturmak istediğiniz başvuru, bir hücreye ya da bitişik hücreler aralığına yapılan başvuru!sonucun sol üst hücresinin gösterdiği satır sayısı, aşa<PERSON><PERSON> veya yukarı!sonucun sol-üst hücresinin gösterdiği sütun sayısı, sola veya sağa!sonucun sahip olmasını istediğiniz yüksekliği (satır sayısı olarak), atlanırsa Başvurunun yüksekliğiyle aynı kabul edilir!sonucun sahip olmasını istediğiniz genişliği (sütun sayısı olarak), atlanırsa Başvurunun genişliğiyle aynı kabul edilir"}, "ROW": {"a": "([ba<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> satır numarasını verir", "ad": "satır numarasını öğrenmek istediğiniz hücre veya tek bir hücreler aralığı; atlanırsa, SATIR işlevini içeren hücreyi verir"}, "ROWS": {"a": "(dizi)", "d": "Bir başvuru ya da dizideki satır sayısını verir", "ad": "<PERSON><PERSON>r sayısını öğrenmek istediğiniz hücreler için bir başvuru, dizi veya dizi formülü"}, "TRANSPOSE": {"a": "(dizi)", "d": "Düşey bir hücreler aralığını yatay bir aralık olarak verir, ya da tam tersi", "ad": "devriğini almak istediğiniz çalışma sayfasında bulunan bir hücreler aralığı ya da değerler dizisi"}, "UNIQUE": {"a": "(dizi; [by_col]; [exactly_once])", "d": " Bir Aralık veya dizideki benzersiz değerleri döndürür.", "ad": " benzersiz satır veya sütun döndürmek için kullanılacak aralık veya dizi! mantıksal değer: satırları birbiriyle karşılaştırın ve benzersiz satırları döndürün = YANLIŞ ya da atlanmış; sütunları birbiriyle karşılaştırın ve benzersiz sütunları döndürün = TRUE! mantıksal değer: dizi = TRUE olduğunda tam olarak bir kez oluşan satırları veya sütunları döndürün; dizideki tüm belirgin satır veya sütunları döndürün = YANLIŞ ya da atlanmış"}, "VLOOKUP": {"a": "(a<PERSON><PERSON>_de<PERSON><PERSON>; tablo_dizisi; sütun_indis_sayısı; [aralık_bak])", "d": "Bir tablonun en sol sütunundaki bir değeri arar ve daha sonra aynı satırda belirttiğiniz sütundan bir değer verir. Varsayılan olarak tablo artan sırada sıralanmalıdır", "ad": "tablonun ilk sütununda bulunacak değerdir ve bir değ<PERSON>, bir başvuru ya da bir metin dizesi olabilir!verinin alınacağı bir metin, sayılar ya da mantıksal değerler tablosu. Tablo_dizisi bir aralığa ya da aralık adına yapılan başvuru olabilir!uyuşan değerin verileceği tablo_dizisi'ndeki sütun sayısı. Tablodaki ilk değer sütunu sütun1'dir!mantıksal değer: ilk sütundaki (artan sırada sıralanmış) en yakın eşleştirmeyi bulmak için = DOĞRU ya da atlanmış; tam bir eşleştirme bulmak için = YANLIŞ"}, "XLOOKUP": {"a": "(arama_de<PERSON><PERSON>; arama_dizi<PERSON>; d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dizi; [bulunam<PERSON>yorsa]; [e<PERSON><PERSON><PERSON><PERSON>rme_modu]; [arama_modu])", "d": "Eşleştirme bulmak için bir aralıkta veya dizide arama yapar ve ilgili öğeyi ikinci bir aralıkta ya da dizide döndürür. Varsayılan olarak tam eşleşme kullanılır", "ad": "aranacak değer!arama yapılacak dizi veya aralık!döndürülecek dizi veya aralık!eşleşme bulunamıyorsa döndürülür!arama_değ<PERSON>'ni, arama_dizisi'ndeki değerlerle eşleştirme biçimini belirtin!kullanılacak arama modunu belirtin. Varsayılan olarak baştan sona doğru arama kullanılır"}, "CELL": {"a": "(bilgi_türü; [ba<PERSON><PERSON><PERSON>])", "d": "Hücrenin biçimlendirmesi, konumu ve içeriği hakkındaki bilgileri verir", "ad": "hangi türde hücre bilgisi vermek istediğinizi belirten metin değeri!hakkında bilgi almak istediğiniz hücre"}, "ERROR.TYPE": {"a": "(hat<PERSON>_<PERSON><PERSON><PERSON>)", "d": "Bir hata değerine karşılık gelen bir sayı verir.", "ad": "tanımlayıcı numarasını bulmak istediğiniz hata değeridir ve gerçek bir hata değeri ya da bir hata değeri içeren bir hücreye yapılan başvuru olabilir"}, "ISBLANK": {"a": "(de<PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON> boş bir hü<PERSON>ye ba<PERSON><PERSON><PERSON>a bulunuyorsa DOĞRU verir", "ad": "sınamak istedi<PERSON><PERSON><PERSON>, ya da hücreye başvuran ad"}, "ISERR": {"a": "(de<PERSON><PERSON>)", "d": "Değerin #YOK dışında bir hata olup olmadığını denetler ve DOĞRU ya da YANLIŞ döndürür", "ad": "sınamak istediğiniz de<PERSON>. <PERSON><PERSON>, bir hü<PERSON>, bir formü<PERSON> ya da br hücre, form<PERSON><PERSON> veya de<PERSON> başvuran bir ada başvuruda bulunabilir"}, "ISERROR": {"a": "(de<PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> bir hata olup olmadığını denetler ve DOĞRU ya da YANLIŞ döndürür", "ad": "sınamak istediğiniz de<PERSON>. <PERSON><PERSON>, bir hü<PERSON>, bir formüle ya da bir hücre, form<PERSON><PERSON> veya değ<PERSON> başvuran bir ada başvuruda bulunabilir"}, "ISEVEN": {"a": "(sayı)", "d": "Sayı bir çift sayı ise DOĞRU döndürür", "ad": "sınanacak sayı"}, "ISFORMULA": {"a": "(baş<PERSON>ru)", "d": "Başvurunun formül içeren bir hücreye yapılıp yapılmadığını denetler ve DOĞRU ya da YANLIŞ değerini döndürür", "ad": "sınamak istediğiniz hücreye yapılan başvuru.  Başvuru bir hücre baş<PERSON><PERSON><PERSON>, bir formül ya da hücreye başvuran bir ad olabilir"}, "ISLOGICAL": {"a": "(de<PERSON><PERSON>)", "d": "Bir değerin mantıksal bir <PERSON> (DOĞRU veya YANLIŞ) olup olmadığını denetler ve DOĞRU veya YANLIŞ değerini döndürür", "ad": "sınamak istediğiniz <PERSON>. <PERSON><PERSON>, bir <PERSON><PERSON><PERSON>, bir formüle ya bunlara veya bir değere başvuran bir ada başvuruda bulunabilir"}, "ISNA": {"a": "(de<PERSON><PERSON>)", "d": "Değerin #YOK olup olmadığını denetler ve DOĞRU ya da YANLIŞ verir", "ad": "sınamak istediğiniz <PERSON>. <PERSON><PERSON>, bir <PERSON><PERSON><PERSON>, bir formüle ya bunlara veya bir değere başvuran bir ada başvuruda bulunabilir"}, "ISNONTEXT": {"a": "(de<PERSON><PERSON>)", "d": "Bir de<PERSON>erin metin olup o<PERSON>ığını denetler (boş hücreler metin değildir) ve metin değilse DOĞRU, metinse YANLIŞ döndürür", "ad": "sınamak istediğiniz değer: bir hücre; bir formü<PERSON>; ya da bir hücreye, formüle veya değere başvuran bir ad"}, "ISNUMBER": {"a": "(de<PERSON><PERSON>)", "d": "Bir değerin sayı olup olmadığını denetler ve sayıysa DOĞRU, değilse YANLIŞ döndürür", "ad": "sınamak istediğiniz <PERSON>. <PERSON><PERSON>, bir <PERSON><PERSON><PERSON>, bir formüle ya da bunlara veya bir değere başvuran bir ada başvuruda bulunabilir"}, "ISODD": {"a": "(sayı)", "d": "<PERSON>ı bir tek sayı ise DOĞRU döndürür", "ad": "sınanacak sayı"}, "ISREF": {"a": "(de<PERSON><PERSON>)", "d": "Bir değerin başvuru olup olmadığını denetler ve başvuruysa DOĞRU, değilse YANLIŞ döndürür", "ad": "sınamak istediğiniz <PERSON>. <PERSON><PERSON>, bir <PERSON><PERSON><PERSON>, bir formüle ya da bunlara veya bir değere başvuran bir ada başvuruda bulunabilir"}, "ISTEXT": {"a": "(de<PERSON><PERSON>)", "d": "Bir değerin metin olup olmadığını denetler ve metinse DOĞRU, metin değilse YANLIŞ döndürür", "ad": "sınamak istediğiniz <PERSON>. <PERSON><PERSON>, bir <PERSON><PERSON><PERSON>, bir formüle ya da bunlara veya bir değere başvuran bir ada başvuruda bulunabilir"}, "N": {"a": "(de<PERSON><PERSON>)", "d": "Bir sayıya dönüştürülmüş değeri verir. <PERSON><PERSON><PERSON>, tari<PERSON> seri numa<PERSON>, DOĞRU 1'e, bun<PERSON><PERSON>n dışındaki şeyler de 0 (sıfır)'a dönüştürülür", "ad": "dönüştürmek istediğiniz <PERSON>"}, "NA": {"a": "()", "d": "#YOK hata değeri<PERSON> verir (kullanılabilir değer yok)", "ad": ""}, "SHEET": {"a": "([de<PERSON><PERSON>])", "d": "Başvurulan sayfanın sayfa numarasını döndürür", "ad": "sayfa numarasını bulmak istediğiniz bir sayfanın veya başvurunun adı.  Atlanırsa, fonksiyonu içeren sayfa numarası döndürülür"}, "SHEETS": {"a": "([ba<PERSON><PERSON><PERSON>])", "d": "Bir başvurudaki sayfa sayısını döndürür", "ad": "içerdiği sayfa sayısını bilmek istediğiniz başvuru.  Atlanırsa, fonksiyonu içeren çalışma kitabındaki sayfa sayısı döndürülür"}, "TYPE": {"a": "(de<PERSON><PERSON>)", "d": "Değerin veri türünü gösteren sayıyı verir: sayı = 1; metin = 2; man<PERSON><PERSON><PERSON><PERSON> = 4; hata de<PERSON><PERSON> = 16; dizi = 64; birleşik veri = 128", "ad": "<PERSON><PERSON><PERSON> bir <PERSON><PERSON>"}, "AND": {"a": "(mantıksal1; [mantıksal2]; ...)", "d": "Tüm bağ<PERSON><PERSON><PERSON><PERSON>ken<PERSON>in DOĞRU olup olma<PERSON>ığı<PERSON> denetler, tümü DOĞRU ise DOĞRU döndürür", "ad": "DOĞRU veya YANLIŞ olduğunu sınamak istediğiniz en az 1 en fazla 255 koşuldur ve her biri mantı<PERSON><PERSON>, dizi ya da başvuru olabilir"}, "FALSE": {"a": "()", "d": "YANLIŞ mantıksal değerini verir", "ad": ""}, "IF": {"a": "(mantı<PERSON><PERSON>_sınama; [eğ<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_de<PERSON><PERSON>]; [eğer_yan<PERSON><PERSON><PERSON><PERSON>_değer])", "d": "Belirttiğiniz koşul DOĞRU olarak hesaplanıyorsa bir değer, YANLIŞ olarak hesaplanıyorsa başka bir değer verir", "ad": "DOĞRU veya YANLIŞ olarak hesaplanabilecek bir değer veya ifade!mantıksal_sınama DOĞRU olduğunda gelen değer. Atlanırsa, DOĞRU gelir. En çok yedi eğer fonksiyonunu iç içe geçirebilirsiniz!mantıksal_sınama YANLIŞ olduğunda gelen değer. Atlanırsa, YANLIŞ gelir"}, "IFS": {"a": "(mantıksal_test; do<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>; ...)", "d": "Bir veya birden fazla koşulun karşılanıp karşılanmadığını denetler ve ilk DOĞRU koşula karşılık gelen bir değer döndürür", "ad": "DOĞRU veya YANLIŞ olarak hesaplanabilecek bir değer veya ifadedir!mantıksal_test DOĞRU ise döndürülen değerdir"}, "IFERROR": {"a": "(de<PERSON><PERSON>; e<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_de<PERSON><PERSON>)", "d": "İfade hatalı olursa eğer_hatalıysa_de<PERSON>er, hatalı olmazsa ifadenin kendi değerini döndürür", "ad": "herhangi bir değer veya ifade veya başvuru!herhangi bir değer veya ifade veya başvuru"}, "IFNA": {"a": "(de<PERSON><PERSON>; de<PERSON><PERSON>_eğer_yok)", "d": "İfade #YOK olarak çözümlenirse belirttiğiniz değeri dö<PERSON>ü<PERSON><PERSON>, aksi durumda ifadenin sonucunu döndürür", "ad": "herhangi bir de<PERSON><PERSON>, ifade veya başvuru!herhangi bir değer, ifade veya başvuru"}, "NOT": {"a": "(mant<PERSON><PERSON><PERSON>)", "d": "Bağımsız değişkenin mantığını tersine çevirir: DOĞRU bir bağımsız değişken için YANLIŞ, YANLIŞ bir bağımsız değişken için DOĞRU verir", "ad": "DOĞRU veya YANLIŞ olarak hesaplanabilecek değer veya ifade"}, "OR": {"a": "(mantıksal1; [mantıksal2]; ...)", "d": "Bağımsız değişkenlerin DOĞRU olup olmadığını denetler ve DOĞRU veya YANLIŞ döndürür. Yalnızca bağımsız değişkenlerin tümü YANLIŞ ise YANLIŞ döndürür", "ad": "sınamak istediğiniz en az 1 en fazla 255 koşuldur, her biri DOĞRU veya YANLIŞ olabilir"}, "SWITCH": {"a": "(ifade; değer1; sonuç1; [var<PERSON><PERSON><PERSON>_veya_değer2]; [sonuç2]; ...)", "d": "İfadeyi bir değer listesine göre hesaplayarak ilk eşleşen değere karşılık gelen sonucu döndürür. Eşleşme yoksa isteğe bağlı varsayılan bir değer döndürülür", "ad": "hesaplanacak ifade!ifadeyle karşılaştırılacak değer!karşılık gelen değer ifadeyle eşleştiğinde dönüdürülecek sonuç"}, "TRUE": {"a": "()", "d": "Mantıksal DOĞRU'yu verir", "ad": ""}, "XOR": {"a": "(mantıksal1; [mantıksal2]; ...)", "d": "<PERSON>ü<PERSON> ba<PERSON><PERSON><PERSON><PERSON>z <PERSON>ğişkenlere mantıksal 'Dışlayıcı Veya' işlecini uygular ve sonucu döndürür", "ad": "DOĞRU veya YANLIŞ olduğunu sınamak istediğiniz en az 1 en fazla 254 koşuldur ve her biri mantı<PERSON><PERSON>, dizi ya da başvuru olabilir"}, "TEXTBEFORE": {"a": "(metin, sın<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [<PERSON><PERSON><PERSON>_say<PERSON><PERSON><PERSON>], [eşleştirme_modu], [eş<PERSON>ştirme_sonu], [bulu<PERSON><PERSON><PERSON><PERSON>])", "d": "Karakterleri sınırlandırmadan önceki metni döndürür.", "ad": "Sınırlayıcıyı aramak istediğiniz metin.!Sınırlayıcı olarak kullanılacak karakter veya dize.!Sınırlayıcının istenen oluşumu. Varsayılan değer 1'dir. Negatif bir sayı sondan itibaren arar.!Metinde sınırlayıcı eşleşmesi arar. Varsayılan olarak, büyük/küçük harfe duyarlı bir eşleştirme yapılır.! Sınırlayıcının metnin sonuna göre eşleştirilip eşleştirilmeyeceği. Varsayılan olarak eşleşmezler.!Eşleşme bulunamazsa döndürülür. Varsayılan olarak, #YOK döndürülür."}, "TEXTAFTER": {"a": "(metin, sın<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [<PERSON><PERSON><PERSON>_say<PERSON><PERSON><PERSON>], [eşleştirme_modu], [eş<PERSON>ştirme_sonu], [bulu<PERSON><PERSON><PERSON><PERSON>])", "d": "Karakterleri sınırlandırmadan sonraki metni döndürür.", "ad": "Sınırlayıcıyı aramak istediğiniz metin.!Sınırlayıcı olarak kullanılacak karakter veya dize.!Sınırlayıcının istenen oluşumu. Varsayılan değer 1'dir. Negatif bir sayı sondan itibaren arar.!Metinde sınırlayıcı eşleşmesi arar. Varsayılan olarak, büyük/küçük harfe duyarlı bir eşleşme yapılır.!Sınırlayıcının metnin sonuna göre eşleşip eşleşmeyeceği. Varsayılan olarak eşleşmezler.!Eşleşme bulunamazsa döndürülür. Varsayılan olarak #YOK döndürülür."}, "TEXTSPLIT": {"a": "(metin, sütun_sınırlayıcı, [satır_sınırlayıc<PERSON>], [b<PERSON><PERSON><PERSON><PERSON>_yoksay], [e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_modu], [<PERSON><PERSON><PERSON><PERSON>_doldur])", "d": "Sınırlayıcıları kullanarak metni satırlara veya sütunlara böler.", "ad": "Bölünecek metin!Sütunları bölmek için karakter veya dize.!Satırları bölmek için karakter veya dize.!Boş hücrelerin göz ardı edilip edilmeyeceği. Varsayılan olarak YANLIŞ.!Metinde bir sınırlayıcı eşleşmesi arar. Varsayılan olarak büyük/küçük harfe duyarlı bir eşleşme yapılır.!Doldurma için kullanılacak değer. Varsayılan olarak #YOK kullanılır."}, "WRAPROWS": {"a": "(ve<PERSON><PERSON><PERSON>, sarma_sayısı, [ş<PERSON><PERSON><PERSON>_doldur])", "d": "Belirt<PERSON>n sayıda de<PERSON>en sonra bir satır veya sütun vektörünü sarar.", "ad": "Sarılacak vektör veya başvuru.!Satır başına maksimum değer sayısı.!Doldurulacak değer. Varsayılan değer #YOK'tur."}, "VSTACK": {"a": "(dizi1, [dizi2], ...)", "d": "Dizileri tek bir dizide dikey olarak yığınlar.", "ad": "Yığınlanacak bir dizi veya başvuru."}, "HSTACK": {"a": "(dizi1, [dizi2], ...)", "d": "Dizileri tek bir dizide yatay olarak yığınlar.", "ad": "Yığınlanacak bir dizi veya başvuru."}, "CHOOSEROWS": {"a": "(dizi, row_num1, [row_num2], ...)", "d": "Bir diziden veya başvurudan satırları döndürür.", "ad": "Döndürülecek satırları içeren dizi veya başvuru.!Döndürülecek satırın numarası."}, "CHOOSECOLS": {"a": "(dizi, col_num1, [col_num2], ...)", "d": "Bir diziden veya ba<PERSON><PERSON>rudan sütunları döndürür.", "ad": "Döndürülecek sütunları içeren dizi veya başvuru.!Döndürülecek sütunun numarası."}, "TOCOL": {"a": "(dizi, [yoksay], [scan_by_column])", "d": "<PERSON><PERSON><PERSON> bir sütun o<PERSON>ak dö<PERSON>ürür.", "ad": "Sütun olarak döndürülecek dizi veya başvuru.!Belirli değer türlerinin yok sayılma veya yok sayılmama durumu. Varsayılan olarak, hiçbir değer göz ardı edilmez.!Diziyi sütuna göre tarayın. Varsayılan olarak, dizi satıra göre taranır."}, "TOROW": {"a": "(dizi, [yoksay], [scan_by_column])", "d": "<PERSON><PERSON><PERSON> bir satır o<PERSON>ak döndürür.", "ad": "Satır olarak döndürülecek dizi veya başvuru.!Belirli değer türlerinin yok sayılma veya yok sayılmama durumu. Varsayılan olarak, hiçbir değer göz ardı edilmez.!Diziyi sütuna göre tarayın. Varsayılan olarak, dizi satıra göre taranır."}, "WRAPCOLS": {"a": "(ve<PERSON><PERSON><PERSON>, sarma_sayısı, [ş<PERSON><PERSON><PERSON>_doldur])", "d": "Belirt<PERSON>n sayıda de<PERSON>en sonra bir satır veya sütun vektörünü sarar.", "ad": "Sarılacak vektör veya başvuru.!Sütun başına maksimum değer sayısı.!Doldurulacak değer. Varsayılan değer #YOK'tur."}, "TAKE": {"a": "(dizi, satırlar, [s<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> ba<PERSON>langıcından veya sonundan satırları veya sütunları döndürür.", "ad": "Satır veya sütunların alınacağı dizi.!Alınacak satır sayısı. Dizinin sonundan negatif bir değer alır.!Alınacak sütun sayısı. Dizinin sonundan negatif bir değer alır."}, "DROP": {"a": "(dizi, satırlar, [s<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> ba<PERSON>langıcından veya sonundan satırları veya sütunları bırakır.", "ad": "Satırların veya sütunların bırakılacağı dizi.!Bırakılacak satır sayısı. Dizinin sonundan negatif bir değer düşer.!Bırakılacak sütun sayısı. Dizinin sonundan negatif bir değer düşer."}, "SEQUENCE": {"a": "(<PERSON><PERSON><PERSON><PERSON>, [s<PERSON><PERSON><PERSON>], [ba<PERSON><PERSON><PERSON><PERSON>], [artı<PERSON>])", "d": "<PERSON><PERSON> sayı dizisi döndürür", "ad": "döndürülecek satır sayısı!döndürülecek sütun sayısı!dizideki ilk sayı!dizide bir sonraki değerde yapılacak artış miktarı"}, "EXPAND": {"a": "(dizi, satırlar, [sü<PERSON><PERSON>], [pad_with])", "d": "Bir diziyi belirtilen boyutlara genişletir.", "ad": "Genişletilecek dizi.!Genişletilmiş dizideki satır sayısı.Eksikse, satırlar genişletilmez.!Genişletilmiş dizideki sütun sayısı.Eksikse, sütunlar genişletilmez.!Doldurulacak değer. Varsayılan değer #N/A'dır."}, "XMATCH": {"a": "(arama_<PERSON><PERSON><PERSON>, arama_di<PERSON><PERSON>, [e<PERSON><PERSON><PERSON><PERSON>rme_modu], [arama_modu])", "d": "Bir dizideki öğenin göreli konumunu döndürür. Varsayılan olarak tam eşleşme gereklidir", "ad": "aranacak değer!arama yapılacak dizi veya aralık!arama_de<PERSON><PERSON><PERSON>, arama_dizisindeki değerlerle eşleştirme biçimini belirtin!kullanılacak arama modunu belirtin. Varsayılan olarak baştan sona doğru arama kullanılır"}, "FILTER": {"a": "(dizi, ekle, [bo<PERSON><PERSON>])", "d": "Bir aralığı veya diziyi filtreler", "ad": "filtrelenecek aralık veya dizi!korunacak satır veya sütunun DOĞRU değeriyle gösterildiği boole değerleri dizisi!korunan öğe yoksa döndürülür"}, "ARRAYTOTEXT": {"a": "(dizi, [biçim])", "d": "<PERSON><PERSON><PERSON> metin olarak temsilini döndürür", "ad": "metin olarak temsil edilecek dizi!metnin biçimi"}, "SORT": {"a": "(dizi, [sı<PERSON><PERSON>_dizini], [sıral<PERSON>_düzeni], [s<PERSON><PERSON><PERSON>_g<PERSON><PERSON>])", "d": "Aralığı veya diziyi sıralar", "ad": "sıralanacak aralık veya dizi!sıralama ölçütü olan satırı veya sütunu belirten sayı!istenen sıralama düzenini belirten sayı; artan düzen (varsayılan) için 1, azalan düzen için -1!istenen sıralama yönünü belirten mantıksal değer: satıra göre (varsayılan) sıralamak için YANLIŞ, sütuna göre sıralamak için DOĞRU"}, "SORTBY": {"a": "(dizi, dizi<PERSON>_göre, [s<PERSON><PERSON><PERSON>_d<PERSON><PERSON><PERSON>], ...)", "d": "Bir aralığı veya diziyi, karşılık gelen aralık veya dizideki değerlere göre sıralar", "ad": "sıralanacak aralık veya dizi!sıralama yaparken temel alınacak aralık veya dizi!istenen sıralama düzenini belirten sayı; artan düzen (varsayılan) için 1, a<PERSON><PERSON> düzen için -1"}, "GETPIVOTDATA": {"a": "(veri_alanı; pivot_table; [alan]; [öğe]; ...)", "d": "PivotTable'da depolanmış verileri verir", "ad": "verinin alınacağı veri alanının adıdır!almak istediğiniz verileri içeren PivotTable'daki bir hücreye veya hücreler aralığına yapılan başvurudur!başvuruda bulunulan alan!başvuruda bulunulan alan <PERSON>"}, "IMPORTRANGE": {"a": "(e-tablo_url'si; aralık_dizesi)", "d": "Belirtilen bir e-tablodan bir hücre aralığını içe aktarır.", "ad": "Verilerin içe aktarılacağı e-tablonun URL'si!aktarılacak aralığı belirten bir dize"}}