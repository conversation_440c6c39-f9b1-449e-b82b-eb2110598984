{"DATE": {"a": "(year; month; day)", "d": "Retorna la xifra que representa la data en codi de data i hora", "ad": "és una xifra de 1.900 o de 1.904 (en funció del sistema de dates del llibre de treball) a 9.999!és una xifra de l'1 al 12 que representa el mes de l'any!és una xifra de l'1 al 31 que representa el dia del mes"}, "DATEDIF": {"a": "(data_inicial; data_final; unitat)", "d": "Retorna la diferència entre dos valors de data (data inicial i data final), basada en l'interval (unitat) especificat"}, "DATEVALUE": {"a": "(date_text)", "d": "Converteix una data en forma de text en una xifra que representa la data en el codi de data i hora", "ad": "és text que representa una data en un format de data de l'Spreadsheet Editor, entre 1/1/1900 o 1/1/1904 (en funció del sistema de dates del llibre de treball) i 31/12/9999"}, "DAY": {"a": "(serial_number)", "d": "Retorna el dia del mes, un valor de l'1 al 31.", "ad": "és una xifra en el codi de data i hora que utilitza l'Spreadsheet Editor"}, "DAYS": {"a": "(data_fi; data_inici)", "d": "Retorna el nombre de dies entre les dues dates.", "ad": "data_inici i data_final són les dues dates entre les què voleu saber el nombre de dies!data_inici i data_final són les dues dates entre les què voleu saber el nombre de dies"}, "DAYS360": {"a": "(data_inicial; data_final; [mètode])", "d": "Retorna el nombre de dies entre dues dates basant-se en un any de 360 dies (dotze mesos de 30 dies)", "ad": "data_inici i data_final són les dues dates entre les quals voleu conèixer el nombre de dies!data_inici i data_final són les dues dates entre les quals voleu conèixer el nombre de dies!és un valor lògic que especifica el mètode de càlcul: EUA (NASD) = FALS o s'omet; europeu = CERT."}, "EDATE": {"a": "(data_inicial; mesos)", "d": "Retorna el número de sèrie de la data, que és el nombre indicat de mesos abans o després de la data inicial", "ad": "és un nombre de data en sèrie que representa la data inicial!és el nombre de mesos abans o després de la data_inici"}, "EOMONTH": {"a": "(data_inicial; mesos)", "d": "Retorna el número de sèrie del darrer dia del mes abans o després d'un nombre especificat de mesos", "ad": "és un nombre de data en sèrie que representa la data inicial!és el nombre de mesos abans o després de la data_inici"}, "HOUR": {"a": "(serial_number)", "d": "Retorna l'hora com una xifra del 0 (12:00 a. m.) al 23 (11:00 p. m.).", "ad": "és una xifra en el codi de data i hora que utilitza l'Spreadsheet Editor o un text en format d'hora, com ara 16:48:00 o 4:48:00 p. m."}, "ISOWEEKNUM": {"a": "(date)", "d": "Torna el número de setmana ISO de l'any d'una data determinada", "ad": "és el codi data-hora utilitzat per l'Spreadsheet Editor per al càlcul de data i hora"}, "MINUTE": {"a": "(serial_number)", "d": "Retorna els minuts, una xifra del 0 al 59.", "ad": "és una xifra en el codi de data i hora que utilitza l'Spreadsheet Editor o un text en format d'hora, com ara 16:48:00 o 4:48:00 PM"}, "MONTH": {"a": "(serial_number)", "d": "<PERSON><PERSON>na el me<PERSON>, una xifra de l'1 (gener) al 12 (desembre).", "ad": "és una xifra en el codi de data i hora que utilitza l'Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(data_inicial; data_final; [festius])", "d": "Retorna el nombre de dies laborables sencers entre dues dates", "ad": "és un nombre de data en sèrie que representa la data inicial!és un nombre de data en sèrie que representa la data final!és un conjunt opcional d'un o diversos nombres de data en sèrie que s'exclouran del calendari laboral, com ara dies festius locals, autonòmics i estatals i dies lliures"}, "NETWORKDAYS.INTL": {"a": "(data_inicial; data_final; [cap_de_setmana]; [dies_festius])", "d": "Retorna el nombre de dies feiners entre dues dates amb paràmetres de cap de setmana personalitzats", "ad": "és un número de data en sèrie que representa la data inicial!és un número de data en sèrie que representa la data final!és un número o una cadena que especifica quan tenen lloc els caps de setmana!és un conjunt opcional d'un o diversos números de data en sèrie que s'exclourà del calendari feiner, com ara els dies festius regionals o nacionals i dies festius de lliure elecció"}, "NOW": {"a": "()", "d": "Retorna la data i l'hora actuals amb format de data i hora.", "ad": ""}, "SECOND": {"a": "(serial_number)", "d": "Retorna els segons, una xifra del 0 al 59.", "ad": "és una xifra en el codi de data i hora que utilitza l'Spreadsheet Editor o un text en format d'hora, com ara 16:48:23 o 4:48:47 PM"}, "TIME": {"a": "(hora; minut; segon)", "d": "Converteix les hores, els minuts i els segons especificats com a números en un número de sèrie, amb format d'hora", "ad": "és un número de 0 a 23 que representa l'hora!és un número de 0 a 59 que representa els minuts!és un número de 0 a 59 que representa els segons"}, "TIMEVALUE": {"a": "(time_text)", "d": "Converteix una hora de text en un número de sèrie per a una hora, una xifra de 0 (12:00:00) a 0,999988426 (11:59:59). Aplica un format d'hora a la xifra després d'introduir la fórmula", "ad": "és una cadena de text que indica l'hora en qualsevol dels formats d'hora de l'Spreadsheet Editor (s'ignora la informació de data de la cadena)"}, "TODAY": {"a": "()", "d": "Retorna la data actual amb format de data.", "ad": ""}, "WEEKDAY": {"a": "(núm_de_sèrie; [tipus])", "d": "Retorna un número de l'1 al 7 que identifica el dia de la setmana d'una data.", "ad": "és un número que representa una data!és un número: per a diumenge=1 fins dissabte=7, utilitzeu 1; per a dilluns=1 fins diumenge=7, utilitzeu 2; per a dilluns=0 fins diumenge=6, utilitzeu 3"}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "Retorna el número de setmana de l'any", "ad": "és el codi de data i hora utilitzat per l'Spreadsheet Editor per al càlcul de la data i l'hora!és un nombre (1 o 2) que determina el tipus de valor retornat"}, "WORKDAY": {"a": "(data_inicial; dies; [festius])", "d": "Retorna el número de sèrie de la data abans o després d'un nombre de dies laborables especificat", "ad": "és un nombre de data en sèrie que representa la data inicial!és el nombre de dies laborables abans o després de la data_inici!és una matriu opcional d'un o diversos nombres de data en sèrie que s'exclouran del calendari laboral, com ara dies festius locals, autonòmics i estatals i dies lliures"}, "WORKDAY.INTL": {"a": "(data_inicial; dies; [cap_de_setmana]; [dies_festius])", "d": "Retorna el número de sèrie de la data abans o després d'un nombre especificat de dies feiners amb paràmetres personalitzats de cap de setmana", "ad": "és un número de data en sèrie que representa la data inicial!és el número de dies que no corresponen a caps de setmana ni dies festius abans o després de data_inici!és un número o una cadena que especifica quan tenen lloc els caps de setmana!és una matriu opcional d'un o més números de data en sèrie que s'exclouen del calendari feiner, com ara els dies festius regionals o nacionals i els dies festius de lliure elecció"}, "YEAR": {"a": "(serial_number)", "d": "Retorna l'any d'una data, un enter de l'interval de 1.900 a 9.999.", "ad": "és una xifra en el codi de data i hora que utilitza l'Spreadsheet Editor"}, "YEARFRAC": {"a": "(data_inicial; data_final; [base])", "d": "Retorna la fracció de l'any que representa el nombre de dies sencers entre la data_inicial i la data_final", "ad": "és el nombre de data en sèrie que representa la data inicial!és el nombre de data en sèrie que representa la data final!és el tipus de base de recompte de dies que cal utilitzar"}, "BESSELI": {"a": "(x; n)", "d": "Retorna la funció Bessel In(x) modificada", "ad": "és el valor amb què es calcula la funció!és l'ordre de la funció Bessel"}, "BESSELJ": {"a": "(x; n)", "d": "Retorna la funció Bessel Jn(x)", "ad": "és el valor amb què es calcula la funció!és l'ordre de la funció Bessel"}, "BESSELK": {"a": "(x; n)", "d": "Retorna la funció Bessel Kn(x) modificada", "ad": "és el valor amb què es calcula la funció!és l'ordre de la funció"}, "BESSELY": {"a": "(x; n)", "d": "Retorna la funció Bessel Yn(x)", "ad": "és el valor amb què es calcula la funció!és l'ordre de la funció"}, "BIN2DEC": {"a": "(nombre)", "d": "Converteix un nombre binari en decimal", "ad": "és el nombre binari que voleu convertir"}, "BIN2HEX": {"a": "(nombre; [posicions])", "d": "Converteix un nombre binari en hexadecimal", "ad": "és el nombre binari que voleu convertir!és el nombre de caràcters que cal utilitzar"}, "BIN2OCT": {"a": "(nombre; [posicions])", "d": "Converteix un nombre binari en octal", "ad": "és el nombre binari que voleu convertir!és el nombre de caràcters que cal utilitzar"}, "BITAND": {"a": "(número1; número2)", "d": "<PERSON>na un bit a bit 'i' de dos números", "ad": "és la representació decimal del número binari que voleu calcular!és la representació decimal del número binari que voleu calcular"}, "BITLSHIFT": {"a": "(nombre; canvi_quantitat)", "d": "Retorna un número desplaçat a l'esquerra per shift_amount bits", "ad": "és la representació decimal del número binari que voleu calcular!és el número de bits en què voleu desplaçar el número cap a l'esquerra"}, "BITOR": {"a": "(número1; número2)", "d": "<PERSON>na un bit a bit '<PERSON>' de dos números", "ad": "és la representació decimal del número binari que voleu calcular!és la representació decimal del número binari que voleu calcular"}, "BITRSHIFT": {"a": "(nombre; canvi_quantitat)", "d": "Retorna un número desplaçat a la dreta per shift_amount bits", "ad": "és la representació decimal del número binari que voleu calcular!és el número de bits en què voleu desplaçar el número cap a la dreta"}, "BITXOR": {"a": "(número1; número2)", "d": "Torna un bit a bit \"Exclusiu o\" de dos números", "ad": "és la representació decimal del número binari que voleu calcular!és la representació decimal del número binari que voleu calcular"}, "COMPLEX": {"a": "(nombre_real; i_nombre; [sufix])", "d": "Converteix coeficients reals i imaginaris en un nombre complex", "ad": "és el coeficient real del nombre complex!és el coeficient imaginari del nombre complex!és el sufix per al component imaginari del nombre complex"}, "CONVERT": {"a": "(nombre; des_d'unitat; a_unitat)", "d": "Converteix un nombre d'un sistema de mesura a un altre", "ad": "és el valor de des_d'unitat que cal convertir!són les unitats per al nombre!són les unitats per al resultat"}, "DEC2BIN": {"a": "(nombre; [posicions])", "d": "Converteix un nombre decimal en binari", "ad": "és l'enter decimal que voleu convertir!és el nombre de caràcters que cal utilitzar"}, "DEC2HEX": {"a": "(nombre; [posicions])", "d": "Converteix un nombre decimal en hexadecimal", "ad": "és l'enter decimal que voleu convertir!és el nombre de caràcters que cal utilitzar"}, "DEC2OCT": {"a": "(nombre; [posicions])", "d": "Converteix un nombre decimal en octal", "ad": "és l'enter decimal que voleu convertir!és el nombre de caràcters que cal utilitzar"}, "DELTA": {"a": "(nombre1; [nombre2])", "d": "Prova si dos nombres són iguals", "ad": "és el primer nombre!és el segon nombre"}, "ERF": {"a": "(límit_inferior; [límit_superior])", "d": "Retorna la funció d'error", "ad": "és el límit inferior per integrar FUN.ERROR!és el límit superior per integrar FUN.ERROR"}, "ERF.PRECISE": {"a": "(X)", "d": "Retorna la funció d'error", "ad": "és el límit inferior per integrar FUNC.ERROR.EXACTE"}, "ERFC": {"a": "(x)", "d": "Retorna la funció d'error complementària", "ad": "és el límit inferior per integrar FUN.ERROR"}, "ERFC.PRECISE": {"a": "(X)", "d": "Retorna la funció d'error complementària", "ad": "és el límit inferior per integrar FUNC.ERROR.COMPL.EXACTE"}, "GESTEP": {"a": "(nombre; [pas])", "d": "Prova si un nombre és major que un valor de llindar", "ad": "és el pas amb què es farà la prova!és el valor de llindar"}, "HEX2BIN": {"a": "(nombre; [posicions])", "d": "Converteix un nombre hexadecimal en binari", "ad": "és el nombre hexadecimal que voleu convertir!és el nombre de caràcters que cal utilitzar"}, "HEX2DEC": {"a": "(nombre)", "d": "Converteix un nombre hexadecimal en decimal", "ad": "és el nombre hexadecimal que voleu convertir"}, "HEX2OCT": {"a": "(nombre; [posicions])", "d": "Converteix un nombre hexadecimal en octal", "ad": "és el nombre hexadecimal que voleu convertir!és el nombre de caràcters que cal utilitzar"}, "IMABS": {"a": "(inombre)", "d": "Retorna el valor absolut (mòdul) d'un nombre complex", "ad": "és el nombre complex el valor del qual voleu calcular"}, "IMAGINARY": {"a": "(inombre)", "d": "Retorna el coeficient imaginari d'un nombre complex", "ad": "és el nombre complex el coeficient imaginari del qual voleu calcular"}, "IMARGUMENT": {"a": "(inombre)", "d": "Retorna l'argument q, un angle expressat en radians", "ad": "és el nombre complex l'argument del qual voleu calcular"}, "IMCONJUGATE": {"a": "(inombre)", "d": "Retorna el conjugat complex d'un nombre complex", "ad": "és el nombre complex el conjugat del qual voleu calcular"}, "IMCOS": {"a": "(inombre)", "d": "Retorna el cosinus d'un nombre complex", "ad": "és el nombre complex el cosinus del qual voleu calcular"}, "IMCOSH": {"a": "(número)", "d": "Retorna el cosinus hiperbòlic d'un número complex", "ad": "és el número complex del qual voleu el cosinus hiperbòlic"}, "IMCOT": {"a": "(número)", "d": "Torna la cotangent d'un número complex", "ad": "és un número complex per al què voleu la cotangent"}, "IMCSC": {"a": "(número)", "d": "Torna la cosecant d'un número complex", "ad": "és un número complex per al què voleu la cosecant"}, "IMCSCH": {"a": "(número)", "d": "Torna la cosecant hiperbòlica d'un número complex", "ad": "és un número complex per al què voleu la cosecant hiperbòlica"}, "IMDIV": {"a": "(inombre1; inombre2)", "d": "Retorna el quocient de dos nombres complexos", "ad": "és el numerador o dividend complex!és el denominador o divisor complex"}, "IMEXP": {"a": "(inombre)", "d": "Retorna el valor exponencial d'un nombre complex", "ad": "és el nombre complex el valor exponencial del qual voleu calcular"}, "IMLN": {"a": "(inombre)", "d": "Retorna el logaritme natural d'un nombre complex", "ad": "és el nombre complex el logaritme natural del qual voleu calcular"}, "IMLOG10": {"a": "(inombre)", "d": "Retorna el logaritme de base 10 d'un nombre complex", "ad": "és el nombre complex el logaritme comú del qual voleu calcular"}, "IMLOG2": {"a": "(inombre)", "d": "Retorna el logaritme de base 2 d'un nombre complex", "ad": "és el nombre complex el logaritme de base 2 del qual voleu calcular"}, "IMPOWER": {"a": "(inombre; nombre)", "d": "Retorna un nombre complex elevat a una potència d'enter", "ad": "és un nombre complex que voleu elevar a una potència!és la potència a la qual voleu elevar el nombre complex"}, "IMPRODUCT": {"a": "(inombre1; [inombre2]; ...)", "d": "Retorna el producte d'1 a 255 nombres complexos", "ad": "Inombre1, Inombre2,... són d'1 a 255 nombres complexos que voleu multiplicar."}, "IMREAL": {"a": "(inombre)", "d": "Retorna el coeficient real d'un nombre complex", "ad": "és el nombre complex el coeficient real del qual voleu calcular"}, "IMSEC": {"a": "(número)", "d": "Torna la secant d'un número complex", "ad": "és un número complex per al què voleu la secant"}, "IMSECH": {"a": "(número)", "d": "Torna la secant hiperbòlica d'un número complex", "ad": "és un número complex per al què voleu la secant hiperbòlica"}, "IMSIN": {"a": "(inombre)", "d": "Retorna el sinus d'un nombre complex", "ad": "és el nombre complex el sinus del qual voleu calcular"}, "IMSINH": {"a": "(número)", "d": "Retorna el sinus hiperbòlic d'un número complex", "ad": "és un número complex del qual voleu el sinus hiperbòlic"}, "IMSQRT": {"a": "(inombre)", "d": "Retorna l'arrel quadrada d'un nombre complex", "ad": "és el nombre complex l'arrel quadrada del qual voleu calcular"}, "IMSUB": {"a": "(inombre1; inombre2)", "d": "Retorna la diferència de dos nombres complexos", "ad": "és el nombre complex del qual es restarà inombre2!és el nombre complex que es restarà de inombre1"}, "IMSUM": {"a": "(inombre1; [inombre2]; ...)", "d": "Retorna la suma de nombres complexos", "ad": "són d'1 a 255 nombres complexos que voleu sumar"}, "IMTAN": {"a": "(número)", "d": "Torna la tangent d'un número complex", "ad": "és un número complex per al què voleu la tangent"}, "OCT2BIN": {"a": "(nombre; [posicions])", "d": "Converteix un nombre octal en binari", "ad": "és el nombre octal que voleu convertir!és el nombre de caràcters que cal utilitzar"}, "OCT2DEC": {"a": "(nombre)", "d": "Converteix un nombre octal en decimal", "ad": "és el nombre octal que voleu convertir"}, "OCT2HEX": {"a": "(nombre; [posicions])", "d": "Converteix un nombre octal en hexadecimal", "ad": "és el nombre octal que voleu convertir!és el nombre de caràcters que cal utilitzar"}, "DAVERAGE": {"a": "(base_de_dades; camp; criteris)", "d": "Obté la mitjana dels valors d'una columna en una llista o base de dades que coincideixen amb les condicions especificades", "ad": "és l'interval de cel·les que constitueix la llista o base de dades. Una base de dades és una llista de dades relacionades!és l'etiqueta entre cometes dobles de la columna o un número que representa la posició de la columna a la llista!és l'interval de cel·les que conté les condicions especificades. L'interval inclou una etiqueta de columna i una cel·la a sota de l'etiqueta per a una condició"}, "DCOUNT": {"a": "(base_de_dades; camp; criteris)", "d": "Compta les cel·les que contenen números al camp (columna) de registres a la base de dades que coincideixen amb les condicions especificades", "ad": "és l'interval de cel·les que constitueix la llista o base de dades. Una base de dades és una llista de dades relacionades!és l'etiqueta entre cometes dobles de la columna o un número que representa la posició de la columna a la llista!és l'interval de cel·les que conté les condicions especificades. L'interval inclou una etiqueta de columna i una cel·la a sota de l'etiqueta per a una condició"}, "DCOUNTA": {"a": "(base_de_dades; camp; criteris)", "d": "Compta les cel·les que no estan en blanc del camp (columna) de registres de la base de dades que coincideixen amb les condicions especificades", "ad": "és l'interval de cel·les que constitueix la llista o base de dades. Una base de dades és una llista de dades relacionades!és l'etiqueta de la columna entre cometes dobles o un número que representa la posició de la columna a la llista!és l'interval de cel·les que conté les condicions especificades. L'interval inclou una etiqueta de columna i una cel·la a sota de l'etiqueta per a una condició"}, "DGET": {"a": "(base_de_dades; camp; criteris)", "d": "Extreu un únic registre d'una base de dades que coincideix amb les condicions especificades", "ad": "és l'interval de cel·les que constitueix la llista o base de dades. Una base de dades és una llista de dades relacionades!és l'etiqueta de la columna entre cometes dobles o un número que representa la posició de la columna a la llista!és l'interval de cel·les que conté les condicions especificades. L'interval inclou una etiqueta de columna i una cel·la a sota de l'etiqueta per a una condició"}, "DMAX": {"a": "(base_de_dades; camp; criteris)", "d": "Retorna el número més gran del camp (columna) de registres de la base de dades que coincideixen amb les condicions especificades", "ad": "és l'interval de cel·les que constitueix la llista o base de dades. Una base de dades és una llista de dades relacionades!és l'etiqueta entre cometes dobles de la columna o un número que representa la posició de la columna a la llista!és l'interval de cel·les que conté les condicions especificades. L'interval inclou una etiqueta de columna i una cel·la a sota de l'etiqueta per a una condició"}, "DMIN": {"a": "(base_de_dades; camp; criteris)", "d": "Retorna el número més petit del camp (columna) de registres de la base de dades que coincideixen amb les condicions especificades", "ad": "és l'interval de cel·les que constitueix la llista o base de dades. Una base de dades és una llista de dades relacionades!és l'etiqueta entre cometes dobles de la columna o un número que representa la posició de la columna a la llista!és l'interval de cel·les que conté les condicions especificades. L'interval inclou una etiqueta de columna i una cel·la a sota de l'etiqueta per a una condició"}, "DPRODUCT": {"a": "(base_de_dades; camp; criteris)", "d": "Multiplica els valors del camp (columna) de registres de la base de dades que coincideixen amb les condicions especificades", "ad": "és l'interval de cel·les que constitueix la llista o base de dades. Una base de dades és una llista de dades relacionades!és l'etiqueta de la columna entre cometes dobles o un número que representa la posició a la llista!és l'interval de cel·les que conté les condicions especificades. L'interval inclou una etiqueta de columna i una cel·la a sota de l'etiqueta per a una condició"}, "DSTDEV": {"a": "(base_de_dades; camp; criteris)", "d": "Calcula la desviació estàndard a partir d'una mostra de les entrades seleccionades d'una base de dades", "ad": "és l'interval de cel·les que constitueix la llista o base de dades. Una base de dades és una llista de dades relacionades!és l'etiqueta entre cometes dobles de la columna o un número que representa la posició de la columna a la llista!és l'interval de cel·les que conté les condicions especificades. L'interval inclou una etiqueta de columna i una cel·la a sota de l'etiqueta per a una condició"}, "DSTDEVP": {"a": "(base_de_dades; camp; criteris)", "d": "Calcula la desviació estàndard a partir de tota la població d'entrades seleccionades a la base de dades", "ad": "és l'interval de cel·les que constitueix la llista o base de dades. Una base de dades és una llista de dades relacionades!és l'etiqueta de la columna entre cometes dobles o un número que representa la posició de la columna a la llista!és l'interval de cel·les que conté les condicions especificades. L'interval inclou una etiqueta de columna i una cel·la a sota de l'etiqueta per a una condició"}, "DSUM": {"a": "(base_de_dades; camp; criteris)", "d": "Suma els números al camp (columna) de registres de la base de dades que coincideixen amb les condicions especificades", "ad": "és l'interval de cel·les que constitueix la llista o base de dades. Una base de dades és una llista de dades relacionades!és l'etiqueta entre cometes dobles de la columna o un número que representa la posició de la columna a la llista!és l'interval de cel·les que conté les condicions especificades. L'interval inclou una etiqueta de columna i una cel·la a sota de l'etiqueta per a una condició"}, "DVAR": {"a": "(base_de_dades; camp; criteris)", "d": "Calcula la variança a partir d'una mostra de les entrades seleccionades a una base de dades", "ad": "és l'interval de cel·les que constitueix la llista o base de dades. Una base de dades és una llista de dades relacionades!és l'etiqueta entre cometes dobles de la columna o un número que representa la posició de la columna a la llista!és l'interval de cel·les que conté les condicions especificades. L'interval inclou una etiqueta de columna i una cel·la a sota de l'etiqueta per a una condició"}, "DVARP": {"a": "(base_de_dades; camp; criteris)", "d": "Calcula la variança a partir de tota la població d'entrades seleccionades a la base de dades", "ad": "és l'interval de cel·les que constitueix la llista o base de dades. Una base de dades és una llista de dades relacionades!és l'etiqueta de la columna entre cometes dobles o un número que representa la posició de la columna a la llista!és l'interval de cel·les que conté les condicions especificades. L'interval inclou una etiqueta de columna i una cel·la a sota de l'etiqueta per a una condició"}, "CHAR": {"a": "(número)", "d": "Retorna el caràcter especificat pel número de codi a partir del conjunt de caràcters definit a l'ordinador", "ad": "és un número entre 1 i 255 que especifica el caràcter desitjat"}, "CLEAN": {"a": "(text)", "d": "Suprimeix tots els caràcters no imprimibles del text", "ad": "és qualsevol informació del text de la qual voleu suprimir els caràcters no imprimibles"}, "CODE": {"a": "(text)", "d": "Retorna un codi numèric per al primer caràcter d'una cadena de text, en el conjunt de caràcters utilitzat per l'ordinador", "ad": "és el text del qual voleu obtenir el codi del primer caràcter"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Uneix diverses cadenes de text en una de sola", "ad": "són d'1 a 255 cadenes de text que es volen unir en una de sola i poden ser cadenes de text, números o referències simples de cel·les"}, "CONCAT": {"a": "(text1; ...)", "d": "Concatena una llista o un interval de cadenes de text", "ad": "són d'1 a 254 intervals o cadenes de text que cal unir en una cadena de text única"}, "DOLLAR": {"a": "(número; [decimals])", "d": "Converteix un número en text, utilitzant format de moneda", "ad": "és un número, una referència a una cel·la que conté un número, o una fórmula que calcula un número!és el nombre de decimals a la dreta del separador decimal. El número s'arrodoneix quan cal. Si s'omet, s'estableix: Decimals = 2"}, "EXACT": {"a": "(text1; text2)", "d": "Comprova si dues cadenes de text són exactament iguals i retorna CERT o FALS. IGUAL fa distinció entre majúscules i minúscules", "ad": "és la primera cadena de text!és la segona cadena de text"}, "FIND": {"a": "(text_cercat; dins_text; [núm_inicial])", "d": "Retorna la posició inicial d'una cadena de text a dins d'una altra cadena de text. BUSCA fa distinció entre majúscules i minúscules", "ad": "és el text que voleu trobar. <PERSON><PERSON><PERSON><PERSON> cometes dobles (sense text) perquè coincideixi amb el primer caràcter de Dins_text; els caràcters comodí no són permesos!és el text que conté el text que voleu trobar!especifica el caràcter on voleu començar la cerca. El primer caràcter de Dins_text és el caràcter 1. Si s'omet, Núm_inicial = 1"}, "FINDB": {"a": "(text_cercat; dins_text; [núm_inicial])", "d": "Cerca la subcadena especificada (cadena-1) dins d'una cadena (cadena-2) i està destinada a llenguatges amb el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc.", "ad": "és el text que voleu trobar. <PERSON><PERSON><PERSON><PERSON> cometes dobles (sense text) perquè coincideixi amb el primer caràcter de Dins_text; els caràcters comodí no són permesos!és el text que conté el text que voleu trobar!especifica el caràcter on voleu començar la cerca. El primer caràcter de Dins_text és el caràcter 1. Si s'omet, Núm_inicial = 1"}, "FIXED": {"a": "(número; [decimals]; [sense_comes])", "d": "Arrodoneix un número al nombre de decimals especificat i retorna el resultat com a text amb o sense comes", "ad": "és el número que voleu arrodonir i convertir en text!és el nombre de decimals a la dreta del separador decimal. Si s'omet, Decimals = 2!és un valor lògic: per mostrar comes en el text retornat = CERT; per mostrar comes en el text retornat = FALS o omès"}, "LEFT": {"a": "(text; [nombre_caràcters])", "d": "Retorna el número especificat de caràcters des del començament d'una cadena de text", "ad": "és la cadena de text que conté els caràcters que es volen extreure!especifica quants caràcters voleu que extregui ESQUERRA. Si s'omet, s'assumeix 1"}, "LEFTB": {"a": "(text; [nombre_caràcters])", "d": "Extreu la subcadena de la cadena especificada començant pel caràcter de l'esquerra i està destinada a llenguatges que utilitzen el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc.", "ad": "és la cadena de text que conté els caràcters que es volen extreure!especifica quants caràcters voleu que extregui LEFTB. Si s'omet, s'assumeix 1"}, "LEN": {"a": "(text)", "d": "Retorna el número de caràcters d'una cadena de text", "ad": "és el text la longitud del qual es vol trobar. Els espais compten com a caràcters"}, "LENB": {"a": "(text)", "d": "Analitza la cadena especificada i retorna el nombre de caràcters que conté i està pensat per a llenguatges que utilitzen el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc.", "ad": "és el text la longitud del qual es vol trobar. Els espais compten com a caràcters"}, "LOWER": {"a": "(text)", "d": "Converteix totes les lletres d'una cadena de text en minúscules", "ad": "és el text que es vol convertir en minúscules. Els caràcters de Text que no són lletres no es canvien"}, "MID": {"a": "(text; posició_inicial; nombre_caràcters)", "d": "Retorna els caràcters del centre d'una cadena de text, donada una posició inicial i la longitud", "ad": "és la cadena de text de la qual es volen extreure els caràcters!és la posició del primer caràcter que es vol extreure. El primer caràcter de Text és 1!especifica quants caràcters s'han de retornar de Text"}, "MIDB": {"a": "(text; posició_inicial; nombre_caràcters)", "d": "Extreu els caràcters de la cadena especificada començant des de qualsevol posició i està pensat per a llenguatges que utilitzen el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc.", "ad": "és la cadena de text de la qual es volen extreure els caràcters!és la posició del primer caràcter que es vol extreure. El primer caràcter de Text és 1!especifica quants caràcters s'han de retornar de Text"}, "NUMBERVALUE": {"a": "(text; [separador_decimal]; [separador_grup])", "d": "Converteix text en número de manera local i independent", "ad": "és la cadena que representa el número que voleu convertir!és el caràcter utilitzat com a separador decimal en la cadena!és el caràcter utilitzat com a separador de grup en la cadena"}, "PROPER": {"a": "(text)", "d": "Converteix una cadena de text en majúscules; la primera lletra de cada paraula en majúscules i la resta en minúscules", "ad": "és text entre cometes, una fórmula que retorna text o una referència a una cel·la que conté text que es vol posar en majúscules parcialment"}, "REPLACE": {"a": "(text_original; núm_inicial; nombre_caràcters; text_nou)", "d": "Substitueix una part d'una cadena de text per una cadena de text diferent", "ad": "és text en què voleu substituir alguns caràcters!és la posició del caràcter a text_original que voleu substituir per text_nou!és el nombre de caràcters de text_original que voleu substituir!és el text que substituirà els caràcters del text_original"}, "REPLACEB": {"a": "(text_original; núm_inicial; nombre_caràcters; text_nou)", "d": "Situa un conjunt de caràcters, basat en el nombre de caràcters i la posició inicial que especifiqueu, amb un nou conjunt de caràcters i està pensat per a llenguatges que utilitzen el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc.", "ad": "és text en què voleu substituir alguns caràcters!és la posició del caràcter a text_original que voleu substituir per text_nou!és el nombre de caràcters de text_original que voleu substituir!és el text que substituirà els caràcters del text_original"}, "REPT": {"a": "(text; nú<PERSON><PERSON>_de_vegades)", "d": "Repeteix el text un número determinat de vegades. <PERSON><PERSON><PERSON>eu REPETEIX per omplir una cel·la amb el número d'ocurrències d'una cadena de text", "ad": "és el text que es vol repetir!és un número positiu que especifica el número de vegades que s'ha de repetir el text"}, "RIGHT": {"a": "(text; [nombre_caràcters])", "d": "Retorna el número especificat de caràcters des de la fi d'una cadena de text", "ad": "és la cadena de text que conté els caràcters que es volen extreure!especifica quants caràcters voleu extreure. Si s'omet, s'assumeix 1"}, "RIGHTB": {"a": "(text; [nombre_caràcters])", "d": "Extreu una subcadena d'una cadena que comença des del caràcter de més a la dreta, basant-se en el nombre especificat de caràcters i està destinat a llenguatges que utilitzen el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc.", "ad": "és la cadena de text que conté els caràcters que es volen extreure!especifica quants caràcters voleu extreure. Si s'omet, s'assumeix 1"}, "SEARCH": {"a": "(text_cercat; dins_text; [núm_inicial])", "d": "Retorna el número del caràcter on es troba un caràcter concret o una cadena de text, llegint d'esquerra a dreta (no fa distinció entre majúscules i minúscules)", "ad": "és el text que es vol trobar. Podeu emprar els caràcters de comodí ? i *; utilitzeu ~? i ~* per trobar els caràcters ? i *!és el text on voleu cercar el Text_cercat!és el número del caràcter de Dins_text, comptant des de l'esquerra, on voleu començar a cercar. Si s'omet, s'utilitza 1"}, "SEARCHB": {"a": "(text_cercat; dins_text; [núm_inicial])", "d": "Retorna la ubicació de la subcadena especificada en una cadena i està destinada a llenguatges que utilitzen el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc.", "ad": "és el text que es vol trobar. Podeu emprar els caràcters de comodí ? i *; utilitzeu ~? i ~* per trobar els caràcters ? i *!és el text on voleu cercar el Text_cercat!és el número del caràcter de Dins_text, comptant des de l'esquerra, on voleu començar a cercar. Si s'omet, s'utilitza 1"}, "SUBSTITUTE": {"a": "(text; text_original; text_nou; [núm_ocurrència])", "d": "Substitueix el text existent amb text nou en una cadena de text", "ad": "és el text o la referència a una cel·la que conté text en què voleu substituir caràcters!és el text existent que voleu substituir. Si les majúscules o minúscules del Text_original no coincideixen amb les del text, SUBSTITUEIX no substituirà el text!és el text amb què voleu substituir el Text_original!especifica quina ocurrència del Text_original voleu substituir. Si s'omet, se substitueixen totes les ocurrències de Text_original"}, "T": {"a": "(valor)", "d": "Comprova si un valor és text i retorna el text si ho és o bé cometes dobles (sense text) si no ho és", "ad": "és el valor que voleu provar"}, "TEXT": {"a": "(valor; format)", "d": "Converteix un valor en text, amb un format de número específic", "ad": "és un número, una fórmula que calcula un valor numèric o una referència a una cel·la que conté un valor numèric!és un número en format de text del quadre Categoria, pestanya Número del quadre de diàleg Format de les cel·les"}, "TEXTJOIN": {"a": "(delimitador; ignora_buit; text1; ...)", "d": "Concatena una llista o un interval de cadenes de text mitjançant un delimitador", "ad": "Caràcter o cadena que cal inserir entre cada element del text!si és TRUE (valor per defecte), ignora les cel·les buides!hi ha d'1 a 252 intervals o cadenes de text que cal unir"}, "TRIM": {"a": "(text)", "d": "Sup<PERSON>ei<PERSON> tots els espais d'una cadena de text excepte els espais individuals entre les paraules", "ad": "és el text del qual voleu suprimir els espais"}, "UNICHAR": {"a": "(número)", "d": "Retorna el caràcter Unicode referenciat pel valor numèric determinat", "ad": "és el nombre Unicode que representa un caràcter"}, "UNICODE": {"a": "(text)", "d": "Retorna el nombre (punt de codi) corresponent al primer caràcter del text", "ad": "és el caràcter del qual voleu el valor Unicode"}, "UPPER": {"a": "(text)", "d": "Converteix totes les lletres d'una cadena de text en majúscules", "ad": "és el text que es vol convertir en minúscules, una referència o una cadena de text"}, "VALUE": {"a": "(text)", "d": "Converteix una cadena de text que representa un número en un número", "ad": "és el text entre cometes o una referència a una cel·la que conté el text que es vol convertir"}, "AVEDEV": {"a": "(número1; [número2]; ...)", "d": "Retorna la mitjana de les desviacions absolutes de la mitjana dels punts de dades. Els arguments poden ser números o noms, matrius o referències que contenen números", "ad": "són d'1 a 255 arguments dels quals voleu conèixer la mitjana de les desviacions absolutes"}, "AVERAGE": {"a": "(número1; [número2]; ...)", "d": "Retorna la mitjana (aritmètica) dels seus arguments, que poden ser números o noms, matrius o referències que continguin números", "ad": "són d'1 a 255 arguments numèrics dels quals es vol obtenir la mitjana"}, "AVERAGEA": {"a": "(valor1; [valor2]; ...)", "d": "Retorna la mitjana (mitjana aritmè<PERSON>) dels seus arguments; el text i el valor FALS es calculen com a 0; CERT es calcula com a 1. Els arguments poden ser números, noms, matrius o referències", "ad": "són d'1 a 255 arguments dels quals voleu obtenir la mitjana"}, "AVERAGEIF": {"a": "(interval; criteris; [interval_mitjana])", "d": "Troba la mitjana (aritmètica) per a les cel·les especificades per una condició o un criteri determinats", "ad": "és l'interval de cel·les que voleu avaluar!és la condició o criteri en format de número, expressió o text que defineix les cel·les que es faran servir per trobar la mitjana!són les cel·les reals que es faran servir per trobar la mitjana. Si s'omet, es fan servir les cel·les de l'interval"}, "AVERAGEIFS": {"a": "(interval_mitjana; interval_criteris; criteris; ...)", "d": "Troba la mitjana (aritmètica) per a les cel·les especificades per un conjunt de condicions o criteris determinat", "ad": "són les cel·les reals que es faran servir per trobar la mitjana.!és l'interval de cel·les que voleu calcular per a la condició concreta!és la condició o criteri en format de número, expressió o text que defineix les cel·les que es faran servir per trobar la mitjana"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "Retorna la funció de densitat de probabilitat beta acumulativa", "ad": "és el valor entre A i B amb què es calcularà la funció!és un paràmetre de la distribució i ha de ser major que 0!és un paràmetre de la distribució i ha de ser major que 0!és un límit inferior opcional de l'interval de x. Si s'omet, s'assumeix que A = 0!és un límit superior opcional de l'interval de x. Si s'omet, s'assumeix que B = 1"}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "Retorna l'invers de la funció de densitat de probabilitat beta acumulativa (DIST.BETA)", "ad": "és una probabilitat associada amb la distribució beta!és un paràmetre de la distribució i ha de ser major que 0!és un paràmetre de la distribució i ha de ser major que 0!és un límit inferior opcional de l'interval de x. Si s'omet, s'assumeix que A = 0!és un límit superior opcional de l'interval de x. Si s'omet, s'assumeix que B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; acumulat; [A]; [B])", "d": "Retorna la funció de distribució de densitat de probabilitat beta", "ad": "és el valor entre A i B amb què es calcularà la funció!és un paràmetre de la distribució i ha de ser major que 0!és un paràmetre de la distribució i ha de ser major que 0!és un valor lògic: per a la funció de distribució acumulativa, utilitzeu CERT; per a la funció de densitat de probabilitat, utilitzeu FALS!és un límit inferior opcional de l'interval d'x. Si s'omet, s'assumeix que A = 0!és un límit superior opcional de l'interval d'x. Si s'omet, s'assumeix que B = 1"}, "BETA.INV": {"a": "(probabilitat; alfa; beta; [A]; [B])", "d": "Retorna l'invers de la funció de densitat de probabilitat beta acumulativa (DISTRIBUCIO.BETA)", "ad": "és una probabilitat associada amb la distribució beta!és un paràmetre de la distribució i ha de ser major que 0!és un paràmetre de la distribució i ha de ser major que 0!és un límit inferior opcional de l'interval d'x. Si s'omet, s'assumeix que A = 0!és un límit superior opcional de l'interval d'x. Si s'omet, s'assumeix que B = 1"}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "Retorna la probabilitat d'una variable aleatòria discreta seguint una distribució binomial", "ad": "és el nombre d'èxits dels intents!és el nombre d'intents independents!és la probabilitat d'èxit de cada intent!és un valor lògic: per utilitzar la funció de distribució acumulativa = CERT; per utilitzar la funció de probabilitat bruta = FALS"}, "BINOM.DIST": {"a": "(nombre_èxits; proves; probabilitat_èxit; acumulat)", "d": "Retorna la probabilitat d'una variable aleatòria discreta seguint una distribució binomial", "ad": "és el nombre d'èxits dels intents!és el nombre d'intents independents!és la probabilitat d'èxit de cada intent!és un valor lògic: per utilitzar la funció de distribució acumulativa = CERT; per utilitzar la funció de probabilitat bruta = FALS"}, "BINOM.DIST.RANGE": {"a": "(intents; prob_èxit; nombre_èxit; [nombre_èxit2])", "d": "Torna la probabilitat d'un resultat de prova utilitzant una distribució binominal", "ad": "és el nombre de proves independents!és la probabilitat d'èxit en cada prova!és el nombre d'èxits en les proves!Si s'ha proporcionat, aquesta funció torna la probabilitat que hauria de tenir el nombre de proves d'èxit entre el nombre_èxit i el nombre_èxit2"}, "BINOM.INV": {"a": "(proves; probabilitat_èxit; alfa)", "d": "Retorna el valor més petit la distribució binomial acumulativa del qual és major o igual que un valor de criteri", "ad": "és el nombre d'intents de <PERSON>lli!és la probabilitat d'èxit de cada intent, un número entre 0 i 1 inclosos!és el valor de criteri, un número entre 0 i 1 inclosos"}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "Retorna la probabilitat d'una variable aleatòria contínua seguint una distribució chi quadrat d'una única cua", "ad": "és el valor del qual voleu calcular la distribució, un nombre no negatiu!és el nombre de graus de llibertat, una xifra entre 1 i 10^10, exclòs 10^10"}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "Retorna l'invers d'una probabilitat determinada, d'una única cua, amb una distribució chi quadrat", "ad": "és una probabilitat associada amb la distribució chi quadrat, un valor entre 0 i 1 inclosos!és el nombre de graus de llibertat, un valor entre 1 i 10^10, exclòs 10^10"}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "Retorna la prova d'independència: el valor de la distribució chi quadrat de l'estadística i els graus de llibertat corresponents", "ad": "és l'interval de dades que conté observacions que es volen contrastar amb els valors esperats!és l'interval de dades que conté el resultat del producte dels totals de files i columnes amb el total general"}, "CHISQ.DIST": {"a": "(x; graus_llibertat; acumulat)", "d": "Retorna la probabilitat de cua esquerra de la distribució khi quadrat", "ad": "és el valor del qual voleu calcular la distribució, un número no negatiu!és el número de graus de llibertat, un número entre 1 i 10^10, exclòs 10^10!és un valor lògic de la funció que s'ha de tornar: la funció de distribució acumulativa = CERT; la funció de densitat de probabilitat = FALS"}, "CHISQ.DIST.RT": {"a": "(x; graus_llibertat)", "d": "Retorna la probabilitat de cua dreta de la distribució khi quadrat", "ad": "és el valor del qual voleu calcular la distribució, un número no negatiu!és el número de graus de llibertat, un número entre 1 i 10^10, exclòs 10^10"}, "CHISQ.INV": {"a": "(probabilitat; graus_llibertat)", "d": "Retorna l'invers de la probabilitat de cua esquerra amb una distribució khi quadrat", "ad": "és una probabilitat associada amb la distribució khi quadrat, un valor entre 0 i 1 inclosos!és el número de graus de llibertat, un número entre 1 i 10^10, exclòs 10^10"}, "CHISQ.INV.RT": {"a": "(probabilitat; graus_llibertat)", "d": "Retorna l'invers de la probabilitat de cua dreta amb una distribució khi quadrat", "ad": "és una probabilitat associada amb la distribució khi quadrat, un valor entre 0 i 1 inclosos!és el número de graus de llibertat, un número entre 1 i 10^10, exclòs 10^10"}, "CHISQ.TEST": {"a": "(interval_real; interval_esperat)", "d": "Retorna la prova d'independència: el valor de la distribució chi quadrat de l'estadística i els graus de llibertat corresponents", "ad": "és l'interval de dades que conté observacions que es volen contrastar amb els valors esperats!és l'interval de dades que conté el resultat del producte dels totals de files i columnes amb el total general"}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "Retorna l'interval de confiança de la mitjana d'una població, utilitzant una distribució normal", "ad": "és el nivell de significació que s'utilitza per calcular el nivell de confiança, un nombre major que 0 i menor que 1!és la desviació estàndard de la població de l'interval de dades i s'assumeix que és coneguda. Desv_estàndard ha de ser major que 0!és la mida de la mostra"}, "CONFIDENCE.NORM": {"a": "(alfa; desv_estàndard; mida)", "d": "Retorna l'interval de confiança de la mitjana d'una població utilitzant una distribució normal", "ad": "és el nivell de significació que s'utilitza per calcular el nivell de confiança, un número major que 0 i menor que 1!és la desviació estàndard de la població de l'interval de dades i s'assumeix que és coneguda. Desv_estàndard ha de ser major que 0!és la mida de la mostra"}, "CONFIDENCE.T": {"a": "(alfa; desv_estàndard; mida)", "d": "Retorna l'interval de confiança de la mitjana d'una població utilitzant una distribució en T de Student", "ad": "és el nivell de significació que s'utilitza per calcular el nivell de confiança, un número major que 0 i menor que 1!és la desviació estàndard de la població de l'interval de dades i s'assumeix que és coneguda. Desv_estàndard ha de ser major que 0!és la mida de la mostra"}, "CORREL": {"a": "(matriu1; matriu2)", "d": "Retorna el coeficient de correlació de dos conjunts de dades", "ad": "és un interval de cel·les de valors. Els valors han de ser números, noms, matrius o referències que continguin números!és un segon interval de cel·les de valors. Els valors han de ser números, noms, matrius o referències que continguin números"}, "COUNT": {"a": "(valor1; [valor2]; ...)", "d": "Compta el nombre de cel·les d'un interval que contenen nombres", "ad": "són d'1 a 255 arguments que contenen o fan referència a diversos tipus de dades diferents, però només es compten els nombres"}, "COUNTA": {"a": "(valor1; [valor2]; ...)", "d": "Compta el nombre de cel·les d'un interval que no són buides", "ad": "són d'1 a 255 arguments que representen els valors i les cel·les que voleu comptar. Els valors poden ser qualsevol tipus d'informació"}, "COUNTBLANK": {"a": "(interval)", "d": "Compta el nombre de cel·les buides que hi ha en un interval de cel·les especificat", "ad": "és l'interval del qual es vol comptar les cel·les buides"}, "COUNTIF": {"a": "(interval; criteris)", "d": "Compta el nombre de cel·les d'un interval que compleixen la condició especificada", "ad": "és l'interval de cel·les del qual es vol comptar les cel·les que no són en blanc!és la condició en forma de número, expressió o text que defineix quines cel·les es comptaran"}, "COUNTIFS": {"a": "(interval_criteris; criteris; ...)", "d": "Recompta el número de cel·les especificades per un conjunt de condicions o criteris determinat", "ad": "és l'interval de cel·les que voleu calcular per a la condició concreta!és la condició en format de número, expressió o text que defineix les cel·les que es recomptaran"}, "COVAR": {"a": "(array1; array2)", "d": "Retorna la covariància, que és la mitjana dels productes de les desviacions, dels parells de punts de dades de dos conjunts de dades", "ad": "és el primer interval de cel·les de nombres enters que han de ser: nombres, matrius o referències que continguin xifres!és el segon interval de cel·les de nombres enters que han de ser: nombres, matrius o referències que continguin xifres"}, "COVARIANCE.P": {"a": "(matriu1; matriu2)", "d": "Retorna la covariança de la població, que és la mitjana dels productes de les desviacions, dels parells de punts de dades a dos conjunts de dades", "ad": "és el primer interval de cel·les de números enters que han de ser: nú<PERSON><PERSON>, matrius o referències que continguin números!és el segon interval de cel·les de números enters que han de ser: númer<PERSON>, matrius o referències que continguin números"}, "COVARIANCE.S": {"a": "(matriu1; matriu2)", "d": "Retorna la covariança de la mostra, que és la mitjana dels productes de les desviacions, dels parells de punts de dades de dos conjunts de dades", "ad": "és el primer interval de cel·les de números enters que han de ser: nú<PERSON><PERSON>, matrius o referències que continguin números!és el segon interval de cel·les de números enters que han de ser: númer<PERSON>, matrius o referències que continguin números"}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "Retorna el valor més petit la distribució binomial acumulativa del qual és major o igual que un valor de criteri", "ad": "és el nombre d'intents de <PERSON>lli!és la probabilitat d'èxit de cada intent, una xifra entre 0 i 1 inclosos!és el valor de criteri, una xifra entre 0 i 1 inclosos"}, "DEVSQ": {"a": "(número1; [número2]; ...)", "d": "Retorna la suma dels quadrats de les desviacions de punts de dades en relació amb la mitjana de la mostra", "ad": "són d'1 a 255 arguments, o una matriu o referència de matriu, dels quals voleu calcular la DESVIA2"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "Retorna la distribució exponencial", "ad": "és el valor de la funció, un nombre no negatiu!és el valor de paràmetre, un nombre positiu!és un valor lògic que retorna la funció: funció de distribució acumulativa = CERT; funció de densitat de probabilitat = FALS"}, "EXPON.DIST": {"a": "(x; lambda; acumulat)", "d": "Retorna la distribució exponencial", "ad": "és el valor de la funció, un número no negatiu!és el valor de paràmetre, un número positiu!és un valor lògic que retorna la funció: funció de distribució acumulativa = CERT; funció de densitat de probabilitat = FALS"}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Retorna la distribució de probabilitat F (grau de diversitat) de cua dreta de dos conjunts de dades", "ad": "és el valor amb què es vol calcular la funció, un nombre no negatiu!és el nombre de graus de llibertat del numerador, una xifra entre 1 i 10^10, exclòs 10^10!és el nombre de graus de llibertat del denominador, una xifra entre 1 i 10^10, exclòs 10^10"}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Retorna l'invers de la distribució de probabilitat F: (de cua dreta) si p = DISTR.F(x...), aleshores DISTR.F.INV(p,...) = x", "ad": "és una probabilitat associada amb la funció de distribució acumulativa F, un valor entre 0 i 1 inclosos!és el nombre de graus de llibertat del numerador, un valor entre 1 i 10^10, exclòs 10^10!és el nombre de graus de llibertat del denominador, un valor entre 1 i 10^10, exclòs 10^10"}, "FTEST": {"a": "(array1; array2)", "d": "Retorna el resultat d'una prova F, la probabilitat de dues cues que les variàncies de matriu1 i matriu2 no siguin significativament diferents", "ad": "és el primer interval o matriu de dades i poden ser nombres o noms, matrius o referències que continguin nombres (s'ignoren els espais en blanc)!és el segon interval o matriu de dades i poden ser nombres o noms, matrius o referències que continguin nombres (s'ignoren els espais en blanc)"}, "F.DIST": {"a": "(x; graus_llibertat1; graus_llibertat2; acumulat)", "d": "Retorna la distribució (de cua esquerra) de probabilitat F (grau de diversitat) de dos conjunts de dades", "ad": "és el valor amb què es vol calcular la funció, un número no negatiu!és el nombre de graus de llibertat del numerador, un número entre 1 i 10^10, exclòs 10^10!és el nombre de graus de llibertat del denominador, un número entre 1 i 10^10, exclòs 10^10!és un valor lògic que tornarà la funció: la funció de distribució acumulativa = CERT; la funció de densitat de probabilitat = FALS"}, "F.DIST.RT": {"a": "(x; graus_llibertat1; graus_llibertat2)", "d": "Retorna la distribució (de cua dreta) de probabilitat F (grau de diversitat) de dos conjunts de dades", "ad": "és el valor amb què es vol calcular la funció, un número no negatiu!és el nombre de graus de llibertat del numerador, un número entre 1 i 10^10, exclòs 10^10!és el nombre de graus de llibertat del denominador, un número entre 1 i 10^10, exclòs 10^10"}, "F.INV": {"a": "(probabilitat; graus_llibertat1; graus_llibertat2)", "d": "Retorna l'invers de la distribució de probabilitat F: si p = DISTR.F.N(x...), aleshores INV.F(p,...) = x", "ad": "és una probabilitat associada amb la funció de distribució acumulativa F, un número entre 0 i 1 inclosos!és el nombre de graus de llibertat del numerador, un número entre 1 i 10^10, exclòs 10^10!és el nombre de graus de llibertat del denominador, un número entre 1 i 10^10, exclòs 10^10"}, "F.INV.RT": {"a": "(probabilitat; graus_llibertat1; graus_llibertat2)", "d": "Retorna l'invers (de cua dreta) de la distribució de probabilitat F: si p = DISTR.F.CD(x...), aleshores INV.F.CD(p,...) = x", "ad": "és una probabilitat associada amb la funció de distribució acumulativa F, un número entre 0 i 1 inclosos!és el nombre de graus de llibertat del numerador, un número entre 1 i 10^10, exclòs 10^10!és el nombre de graus de llibertat del denominador, un número entre 1 i 10^10, exclòs 10^10"}, "F.TEST": {"a": "(matriu1; matriu2)", "d": "Retorna el resultat d'una prova F, la probabilitat de dues cues que les variàncies de matriu1 i matriu2 no siguin significativament diferents", "ad": "és el primer interval o matriu de dades i poden ser nombres o noms, matrius o referències que continguin nombres (s'ignoren els espais en blanc)!és el segon interval o matriu de dades i poden ser nombres o noms, matrius o referències que continguin nombres (s'ignoren els espais en blanc)"}, "FISHER": {"a": "(x)", "d": "Retorna la transformació Fisher", "ad": "és el valor del qual es vol obtenir la transformació, un número entre -1 i 1, exclosos -1 i 1"}, "FISHERINV": {"a": "(y)", "d": "Retorna la funció inversa de la transformació Fisher: si y = FISHER(x), aleshores PROVA.FISHER.INV(y) = x", "ad": "és el valor al qual es vol realitzar la transformació inversa"}, "FORECAST": {"a": "(x; conegut_ys; conegut_xs)", "d": "Calcula o prediu un valor futur en una tendència lineal utilitzant valors existents", "ad": "és el punt de dades per al qual es vol predir un valor i ha de ser un valor numèric!És la matriu o l'interval de dades numèriques dependent!és la matriu o l'interval de dades numèriques independent. La variància de conegut_x no pot ser zero"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [agreggation])", "d": "Retorna el valor previst per a una determinada data de destinació futura mitjançant el mètode de suavització exponencial .", "ad": "És el punt de dades per al qual l'Spreadsheet Editor prediu un valor. Ha de continuar el patró de valors en la cronologia.!És la matriu o l'interval de dades numèriques que voleu predir.!És la matriu o l'interval de dades numèriques independent. Les dates de la cronologia han de tenir un pas coherent entre si i no poden ser zero.!és un valor numèric opcional que indica la durada del patró estacional. El valor predeterminat 1 indica que l’estacionalitat es detecta automàticament.!és un valor opcional per controlar valors que no hi són. El valor predeterminat 1 substitueix els valors que no hi són mitjançant la interpolació, i 0 els substitueix per zeros.!.és un valor numèric opcional per agregar diversos valors amb la mateixa marca horària. Si es deixa en blanc, l'Spreadsheet Editor fa una mitjana dels valors."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Retorna un interval de confiança per al valor de pronòstic en la data de destinació especificada.", "ad": "És el punt de dades per al qual l'Spreadsheet Editor prediu un valor. Ha de continuar el patró de valors en la cronologia.!És la matriu o l'interval de dades numèriques que voleu predir.!És la matriu o l'interval de dades numèriques independent. Les dates de la cronologia han de tenir un pas coherent entre si i no poden ser zero.!És un nombre entre 0 i 1, que indica el nivell de confiança per a l'interval de confiança calculat. El valor predeterminat és .95.!és un valor numèric opcional que indica la durada del patró estacional. El valor predeterminat 1 indica que l’estacionalitat es detecta automàticament.!és un valor opcional per controlar valors que no hi són. El valor predeterminat 1 substitueix els valors que no hi són mitjançant la interpolació, i 0 els substitueix per zeros.!.és un valor numèric opcional per agregar diversos valors amb la mateixa marca horària. Si es deixa en blanc, l'Spreadsheet Editor fa una mitjana dels valors."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Retorna la durada del patró repetitiu que una aplicació detecta per a la sèrie de temps especificada.", "ad": "És la matriu o l'interval de dades numèriques que voleu predir.!És la matriu o l'interval de dades numèriques independent. Les dates de la cronologia han de tenir un pas coherent entre si i no poden ser zero.!És un valor opcional per controlar els valors que no hi són. El valor predeterminat 1 substitueix els valors que no hi són mitjançant la interpolació i 0 els substitueix per zeros.!És un valor numèric opcional per agregar diversos valors amb la mateixa marca horària. Si es deixa en blanc, l'Spreadsheet Editor fa una mitjana dels valors."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "Retorna l’estadística sol·licitada per a la predicció.", "ad": "És la matriu o l'interval de les dades numèriques que es prediuen.!És la matriu o l'interval independent de les dades numèriques. Les dates de la cronologia han de ser correlatives i no poden ser zero.!És un número entre l'1 i el 8 que indica quina estadística retornarà l'Spreadsheet Editor per a la predicció calculada.!És un valor numèric opcional que indica la durada del patró estacional. El valor per defecte 1 indica que l'estacionalitat es detecta automàticament.!És un valor opcional per controlar valors que no hi són. El valor predeterminat 1 substitueix els valors que no hi són mitjançant la interpolació i 0 els substitueix per zeros.!És un valor numèric opcional per agregar diversos valors amb la mateixa marca horària. Si es deixa en blanc, l'Spreadsheet Editor fa una mitjana dels valors."}, "FORECAST.LINEAR": {"a": "(xs; conegut_ys; conegut_xs)", "d": "Calcula o prediu un valor futur en una tendència lineal utilitzant valors existents", "ad": "és el punt de dades per al qual es vol predir un valor i ha de ser un valor numèric!és la matriu o l'interval de dades numèriques dependent!és l'interval o la matriu de dades numèriques independent. La variància de conegut_x no pot ser zero"}, "FREQUENCY": {"a": "(dades; grups)", "d": "Calcula la freqüència amb què es produeix un valor en un interval de valors i retorna una matriu vertical de números amb més d'un element que Grups", "ad": "és una matriu o una referència a un conjunt de valors dels quals es volen conèixer les freqüències (s'ignoren els espais en blanc i el text)!és una matriu o una referència a intervals en què es vol agrupar els valors de Dades"}, "GAMMA": {"a": "(x)", "d": "Retorna el valor de la funció Gamma", "ad": "és el valor per al què voleu calcular la Gamma"}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "Retorna la distribució gamma", "ad": "és el valor amb el qual es vol calcular la distribució, un nombre no negatiu!és un paràmetre de la distribució, un nombre positiu!és un paràmetre de la distribució, un nombre positiu. Si beta = 1, DIST.GAMMA retorna la distribució gamma estàndard!és un valor lògic: perquè retorni la funció de distribució acumulativa = CERT; perquè retorni la funció de probabilitat bruta = FALS o s'omet"}, "GAMMA.DIST": {"a": "(x; alfa; beta; acumulat)", "d": "Retorna la distribució gamma", "ad": "és el valor amb el qual es vol calcular la distribució, un número no negatiu!és un paràmetre de la distribució, un número positiu!és un paràmetre de la distribució, un número positiu. Si beta = 1, DISTRIBUCIO.GAMMA retorna la distribució gamma estàndard!és un valor lògic: perquè retorni la funció de distribució acumulativa = CERT; perquè retorni la funció de probabilitat bruta = FALS o s'omet"}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "Retorna el valor invers de la distribució gamma acumulativa: si p = DIST.GAMMA(x,...), aleshores DIST.GAMMA.INV(p,...) = x", "ad": "és la probabilitat associada amb la distribució gamma, un nombre entre 0 i 1, inclosos!és un paràmetre de la distribució, un nombre positiu!és un paràmetre de la distribució, un nombre positiu. Si beta = 1, DIST.GAMMA.INV retorna el valor invers de la distribució gamma estàndard"}, "GAMMA.INV": {"a": "(probabilitat; alfa; beta)", "d": "Retorna el valor invers de la distribució gamma acumulativa: si p = DISTRIBUCIO.GAMMA(x,...), aleshores INV.GAMMA(p,...) = x", "ad": "és la probabilitat associada amb la distribució gamma, un número entre 0 i 1, inclosos!és un paràmetre de la distribució, un número positiu!és un paràmetre de la distribució, un número positiu. Si beta = 1, INV.GAMMA retorna el valor invers de la distribució gamma estàndard"}, "GAMMALN": {"a": "(x)", "d": "Retorna el logaritme natural de la funció gamma", "ad": "és el valor la funció GAMMA.LN del qual voleu calcular, un número positiu"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Retorna el logaritme natural de la funció gamma", "ad": "és el valor la funció GAMMA.LN.EXACTE del qual voleu calcular, un número positiu"}, "GAUSS": {"a": "(x)", "d": "Torna un 0,5 menys que la distribució acumulativa estàndard normal", "ad": "és el valor del qual voleu obtenir la distribució"}, "GEOMEAN": {"a": "(número1; [número2]; ...)", "d": "Retorna la mitjana geomètrica d'una matriu o un interval de dades numèriques positives", "ad": "són d'1 a 255 números o noms, matrius o referències que contenen números dels quals voleu obtenir la mitjana"}, "GROWTH": {"a": "(conegut_ys; [conegut_xs]; [nova_xs]; [constant])", "d": "Retorna números en una tendència de creixement exponencial que coincideix amb punts de dades coneguts", "ad": "és el conjunt de valors d'y coneguts en la relació y = b*m^x, una matriu o un interval de números positius!és un conjunt de valors d'x opcionals (que poden ser coneguts) de la relació y = b*m^x, una matriu o un interval de la mateixa mida que conegut_y!són valors nous d'x per als quals es vol que la funció CREIXEMENT retorni els valors d'y corresponents!és un valor lògic: la constant b es calcula normalment si Const = CERT; es defineix b = 1 si Const = FALS o s'omet"}, "HARMEAN": {"a": "(número1; [número2]; ...)", "d": "Retorna la mitjana harmònica d'un conjunt de dades de números positius: el recíproc de la mitjana aritmètica dels recíprocs", "ad": "són d'1 a 255 números, noms, matrius o referències que contenen números dels quals voleu calcular la mitjana harmònica"}, "HYPGEOM.DIST": {"a": "(mostra_èxits; número_mostra; població_èxit; número_població; acumulat)", "d": "Retorna la distribució hipergeomètrica", "ad": "és el nombre d'èxits de la mostra!és la mida de la mostra!és el nombre d'èxits de la població!és la mida de la població!és un valor lògic: per a la funció de distribució acumulativa, utilitzeu CERT; per a la funció de densitat de probabilitat, utilitzeu FALS"}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "Retorna la distribució hipergeomètrica", "ad": "és el nombre d'èxits de la mostra!és la mida de la mostra!és el nombre d'èxits de la població!és la mida de la població"}, "INTERCEPT": {"a": "(conegut_ys; conegut_xs)", "d": "Calcula el punt en què la línia intersecarà amb l'eix Y utilitzant una línia de regressió optimitzada traçada a través dels valors coneguts d'X i Y", "ad": "és el conjunt d'observacions o dades dependent i poden ser: números, noms, matrius o referències que continguin números!és el conjunt d'observacions o dades independent format per: números, noms, matrius o referències que continguin números"}, "KURT": {"a": "(número1; [número2]; ...)", "d": "Retorna la curtosi d'un conjunt de dades", "ad": "són d'1 a 255 números o noms, matrius o referències que contenen números dels quals voleu obtenir la curtosi"}, "LARGE": {"a": "(matriu; k)", "d": "Retorna el valor k-èsim més gran d'un conjunt de dades. Per exemple, el cinquè número més gran", "ad": "és la matriu o l'interval de dades del qual voleu determinar el valor k-èsim més gran!és la posició (des del valor més gran) a la matriu o l'interval de cel·les del valor que s'ha de retornar"}, "LINEST": {"a": "(conegut_ys; [conegut_xs]; [constant]; [estadística])", "d": "Retorna estadístiques que descriuen una tendència lineal que coincideix amb punts de dades coneguts, per mitjà d'una línia recta fent servir el mètode dels mínims quadres", "ad": "és el conjunt de valors d'y coneguts a la relació y = mx + b!és un conjunt de valors d'x opcionals (que poden ser coneguts) de la relació y = mx + b!és un valor lògic: la constant b es calcula normalment si Const = CERT o s'omet; b serà igual a 0 si Const = FALS!és un valor lògic: retorna estadístiques de regressió addicionals = CERT; retorna coeficients m i la constant b = FALS o s'omet"}, "LOGEST": {"a": "(conegut_ys; [conegut_xs]; [constant]; [estadística])", "d": "Retorna estadístiques que descriuen una corba exponencial, que coincideix amb punts de dades coneguts", "ad": "és el conjunt de valors d'y coneguts en la relació y = b*m^x!és un conjunt opcional de valors d'x que poden ser coneguts en la relació y = b*m^x!és un valor lògic: la constant b es calcula normalment si Const = CERT o s'omet; b serà igual a 1 si Const = FALS!és un valor lògic: retorna estadístiques de regressió addicionals = CERT; retorna coeficients m i la constant b = FALS o s'omet"}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "Retorna l'invers de la distribució logarítmica-normal acumulativa de x, on ln(x) es distribueix de forma normal amb els paràmetres Mitjana i Desv_estàndard", "ad": "és una probabilitat associada amb la distribució logarítmica-normal, un valor entre 0 i 1, inclosos!és la mitjana de ln(x)!és la desviació estàndard de ln(x), un nombre positiu"}, "LOGNORM.DIST": {"a": "(x; mitjana; desv_estàndard; acumulat)", "d": "Retorna la distribució logarítmica normal d'x, on ln(x) es distribueix de manera normal amb els paràmetres Mitjana i Desv_estàndard", "ad": "és el valor al qual es vol calcular la funció, un número positiu!és la mitjana de ln(x)!és la desviació estàndard de ln(x), un número positiu!és un valor lògic: per a la funció de distribució acumulativa, utilitzeu CERT; per a la funció de densitat de probabilitat, utilitzeu FALS"}, "LOGNORM.INV": {"a": "(probabilitat; mitjana; desv_estàndard)", "d": "Retorna l'invers de la distribució logarítmica-normal acumulativa d'x, on ln(x) es distribueix de forma normal amb els paràmetres Mitjana i Desv_estàndard", "ad": "és una probabilitat associada amb la distribució logarítmica-normal, un número entre 0 i 1, inclosos!és la mitjana de ln(x)!és la desviació estàndard de ln(x), un número positiu"}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "Retorna la distribució logarítmica normal de x, on ln(x) es distribueix de forma normal amb els paràmetres Mitjana i Desv_estàndard", "ad": "és el valor amb el qual es vol calcular la funció, un nombre positiu!és la mitjana de ln(x)!és la desviació estàndard de ln(x), un nombre positiu"}, "MAX": {"a": "(número1; [número2]; ...)", "d": "Retorna el valor més gran d'un conjunt de valors. Ignora els valors lògics i el text", "ad": "són d'1 a 255 números, cel·les buides, valors lògics o números de text dels quals es vol obtenir el màxim"}, "MAXA": {"a": "(valor1; [valor2]; ...)", "d": "Retorna el valor més gran d'un conjunt de valors. No ignora els valors lògics ni el text", "ad": "són d'1 a 255 números, cel·les buides, valors lògics o números de text dels quals voleu calcular el màxim"}, "MAXIFS": {"a": "(interval_max; interval_de_criteris; criteris; ...)", "d": "Retorna el valor màxim de totes les cel·les especificat per un conjunt determinat de condicions o de criteris", "ad": "les cel·les en què es determina el valor màxim!és l'interval de cel·les que voleu avaluar per a la condició concreta!és la condició o el criteri en la forma d'un número, d'una expressió o del text que defineix quines cel·les s'inclouen en determinar el valor màxim"}, "MEDIAN": {"a": "(número1; [número2]; ...)", "d": "Retorna la mitjana o el número central d'un conjunt de números", "ad": "són d'1 a 255 números o noms, matrius o referències que contenen números i dels quals voleu obtenir la mitjana"}, "MIN": {"a": "(número1; [número2]; ...)", "d": "Retorna el número més petit d'un conjunt de valors. Ignora els valors lògics i el text", "ad": "són d'1 a 255 números, cel·les buides, valors lògics o números de text dels quals es vol obtenir el mínim"}, "MINA": {"a": "(valor1; [valor2]; ...)", "d": "Retorna el valor més petit d'un conjunt de valors. No ignora els valors lògics ni el text", "ad": "són d'1 a 255 números, cel·les buides, valors lògics o números de text dels quals voleu obtenir el mínim"}, "MINIFS": {"a": "(interval_min; interval_de_criteris; criteris; ...)", "d": "Retorna el valor mínim de totes les cel·les especificat per un conjunt determinat de condicions o de criteris", "ad": "les cel·les en què es determina el valor mínim!és l'interval de cel·les que voleu avaluar per a la condició concreta!és la condició o el criteri en la forma d'un número, d'una expressió o del text que defineix quines cel·les s'inclouen en determinar el valor mínim"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "Retorna el valor més freqüent o més repetitiu d'una matriu o un interval de dades", "ad": "són d'1 a 255 xifres o noms, matrius o referències que contenen xifres i dels quals se'n vol conèixer la moda"}, "MODE.MULT": {"a": "(número1; [número2]; ...)", "d": "Retorna una matriu vertical dels valors més freqüents o més repetitius d'una matriu o un interval de dades. Per a una matriu horitzontal, utilitz<PERSON> =TRANSPOSA(MODA.DIVERSOS(número1,número2,...))", "ad": "són d'1 a 255 números o noms, matrius o referències que contenen números i dels quals se'n vol conèixer la moda"}, "MODE.SNGL": {"a": "(número1; [número2]; ...)", "d": "Retorna el valor més freqüent o més repetitiu d'una matriu o un interval de dades", "ad": "són d'1 a 255 números o noms, matrius o referències que contenen números i dels quals se'n vol conèixer la moda"}, "NEGBINOM.DIST": {"a": "(nombre_fracassos; nombre_èxits; probabilitat_èxit; acumulat)", "d": "Retorna la distribució binomial negativa, la probabilitat que es produeixin els fracassos de nombre_fracassos abans de l'èxit nombre_èxits, amb la probabilitat d'èxit probabilitat_èxit", "ad": "és el nombre de fracassos!és el nombre màxim d'èxits!és la probabilitat d'èxit; un número entre 0 i 1!és un valor lògic: per a la funció de distribució acumulativa, utilitzeu CERT; per a la funció massiva de probabilitat, utilitzeu FALS"}, "NEGBINOMDIST": {"a": "(nombre_fracassos; nombre_èxits; probabilitat_èxit)", "d": "Retorna la distribució binomial negativa, la probabilitat que es produeixin els fracassos de nombre_fracassos abans de l'èxit nombre_èxits, amb probabilitat d’èxit probabilitat_èxit", "ad": "és el nombre de fracassos!és el nombre màxim d’èxits! és la probabilitat d'èxit; un número entre 0 i 1"}, "NORM.DIST": {"a": "(x; mitjana; desv_estàndard; acumulat)", "d": "Retorna la distribució normal de la mitjana i la desviació estàndard especificades", "ad": "és el valor del qual es vol obtenir la distribució!és la mitjana aritmètica de la distribució!és la desviació estàndard de la distribució, un número positiu!és un valor lògic: per a la funció de distribució acumulativa, utilitzeu CERT; per a la funció de densitat de probabilitat, utilitzeu FALS"}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Retorna la distribució normal acumulativa de la mitjana i la desviació estàndard especificades", "ad": "és el valor del qual es vol obtenir la distribució!és la mitjana aritmètica de la distribució!és la desviació estàndard de la distribució, un nombre positiu!és un valor lògic: per a la funció de distribució acumulativa, utilitzeu CERT; per a la funció de densitat de probabilitat, utilitzeu FALS"}, "NORM.INV": {"a": "(probabilitat; mitjana; desv_estàndard)", "d": "Retorna l'invers de la distribució acumulativa normal de la mitjana i la desviació estàndard especificades", "ad": "és una probabilitat que correspon a la distribució normal, un número entre 0 i 1, inclosos!és la mitjana aritmètica de la distribució!és la desviació estàndard de la distribució, un valor positiu"}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "Retorna l'invers de la distribució acumulativa normal de la mitjana i la desviació estàndard especificades", "ad": "és una probabilitat que correspon a la distribució normal, un valor entre 0 i 1, inclosos!és la mitjana aritmètica de la distribució!és la desviació estàndard de la distribució, un valor positiu"}, "NORM.S.DIST": {"a": "(z; acumulat)", "d": "Retorna la distribució normal estàndard (té una mitjana de zero i una desviació estàndard d'u)", "ad": "és el valor per al qual voleu la distribució!és un valor lògic que la funció ha de tornar: la funció de distribució acumulativa = CERT; la funció de densitat de probabilitat = FALS"}, "NORMSDIST": {"a": "(z)", "d": "Retorna la distribució normal estàndard acumulativa (té una mitjana de zero i una distribució estàndard d'u)", "ad": "és el valor del qual es vol conèixer la distribució"}, "NORM.S.INV": {"a": "(probabilitat)", "d": "Retorna l'invers de la distribució normal estàndard acumulativa (té una mitjana de zero i una distribució estàndard d'u)", "ad": "és una probabilitat que correspon a la distribució normal, un número entre 0 i 1, inclosos"}, "NORMSINV": {"a": "(probability)", "d": "Retorna l'invers de la distribució normal estàndard acumulativa (té una mitjana de zero i una distribució estàndard d'u)", "ad": "és una probabilitat que correspon a la distribució normal, un nombre entre 0 i 1, inclosos"}, "PEARSON": {"a": "(matriu1; matriu2)", "d": "Retorna el coeficient del moment de correlació del producte Pearson, r", "ad": "és un conjunt de valors independents!és un conjunt de valors dependents"}, "PERCENTILE": {"a": "(array; k)", "d": "Retorna el percentil k-èsim de valors d'un interval", "ad": "és la matriu o l'interval de dades que defineix la posició relativa!és el valor del percentil entre 0 i 1, inclosos"}, "PERCENTILE.EXC": {"a": "(matriu; k)", "d": "Retorna el percentil k-èsim de valors d'un interval en què k es troba dins l'interval 0..1, exclosos", "ad": "és la matriu o l'interval de dades que defineix la posició relativa!és el valor del percentil entre 0 i 1, inclosos"}, "PERCENTILE.INC": {"a": "(matriu; k)", "d": "Retorna el percentil k-èsim de valors d'un interval en què k es troba dins l'interval 0..1, inclosos", "ad": "és la matriu o l'interval de dades que defineix la posició relativa!és el valor del percentil entre 0 i 1, inclosos"}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "Retorna la classificació d'un valor en un conjunt de dades com un percentatge del conjunt de dades", "ad": "és la matriu o l'interval de dades amb valors numèrics que defineix la posició relativa!és el valor del qual voleu conèixer la classificació!és un valor opcional que identifica el nombre de decimals significatius del percentatge retornat, tres decimals si s'omet (0.xxx%)"}, "PERCENTRANK.EXC": {"a": "(matriu; x; [xifra_significativa])", "d": "Retorna la classificació d'un valor en un conjunt de dades com un percentatge del conjunt de dades com un percentatge (0..1, exclosos) del conjunt de dades", "ad": "és la matriu o l'interval de dades amb valors numèrics que defineix la posició relativa!és el valor del qual voleu conèixer la classificació!és un valor opcional que identifica el número de decimals significatius del percentatge retornat, tres decimals si s'omet (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(matriu; x; [xifra_significativa])", "d": "Retorna la classificació d'un valor en un conjunt de dades com un percentatge del conjunt de dades com un percentatge (0..1, inclosos) del conjunt de dades", "ad": "és la matriu o l'interval de dades amb valors numèrics que defineix la posició relativa!és el valor del qual voleu conèixer la classificació!és un valor opcional que identifica el número de decimals significatius del percentatge retornat, tres dígits si s'omet (0.xxx%)"}, "PERMUT": {"a": "(número; número_triat)", "d": "Retorna el nombre de permutacions d'un nombre determinat d'objectes que es poden seleccionar del total d'objectes", "ad": "és el nombre total d'objectes!és el nombre d'objectes de cada permutació"}, "PERMUTATIONA": {"a": "(número; número_triat)", "d": "Retorna el nombre de permutacions d'un determinat nombre d'objectes (amb repeticions) que es poden seleccionar dels objectes totals", "ad": "és el nombre total d'objectes!és el nombre d'objectes a cada permutació"}, "PHI": {"a": "(x)", "d": "Retorna el valor de la funció de densitat d'una distribució estàndard normal", "ad": "és el número del qual voleu obtenir la densitat de la distribució estàndard normal"}, "POISSON": {"a": "(x; mean; cumulative)", "d": "Retorna la distribució de Poisson", "ad": "és el nombre d'incidències!és el valor numèric esperat, un nombre positiu!és un valor lògic: per a la probabilitat acumulativa de Poisson, utilitzeu CERT; per a la funció de probabilitat bruta de Poisson, utilitzeu FALS"}, "POISSON.DIST": {"a": "(x; mitjana; acumulat)", "d": "Retorna la distribució de Poisson", "ad": "és el nombre d'incidències!és el valor numèric esperat, un número positiu!és un valor lògic: per a la probabilitat acumulativa de Poisson, utilitzeu CERT; per a la funció de probabilitat bruta de Poisson, utilitzeu FALS"}, "PROB": {"a": "(interval_x; interval_probabilitat; límit_inf; [límit_sup])", "d": "Retorna la probabilitat que els valors d'un interval es trobin entre dos límits o siguin iguals que un límit inferior", "ad": "és l'interval de valors numèrics d'X amb el qual hi ha probabilitats associades!és un conjunt de probabilitats associat a valors de l'interval_x, valors entre 0 i 1, exclòs el 0!és el límit inferior del valor del qual es vol conèixer una probabilitat!és el límit superior opcional del valor. Si s'omet, PROB retorna la probabilitat que els valors d'interval_x siguin iguals que el límit_inf"}, "QUARTILE": {"a": "(array; quart)", "d": "Retorna el quartí d'un conjunt de dades", "ad": "és la matriu o l'interval de cel·les de valors numèrics del qual es vol obtenir el valor quartí!és una xifra: valor mínim = 0; 1r quartí = 1; valor mitjà = 2; 3r quartí = 3; valor màxim = 4"}, "QUARTILE.INC": {"a": "(mitjana; quartí)", "d": "Retorna el quartí d'un conjunt de dades basat en el percentil dels valors entre 0..1, inclosos", "ad": "és la matriu o l'interval de cel·les de valors numèrics del qual es vol obtenir el valor quartí!és un número: valor mínim = 0; 1r quartí = 1; valor mitjà = 2; 3r quartí = 3; valor màxim = 4"}, "QUARTILE.EXC": {"a": "(mitjana; quartí)", "d": "Retorna el quartí d'un conjunt de dades basat en el percentil dels valors entre 0..1, exclosos", "ad": "és la matriu o l'interval de cel·les de valors numèrics del qual es vol obtenir el valor quartí!és un número: valor mínim = 0; 1r quartí = 1; valor mitjà = 2; 3r quartí = 3; valor màxim = 4"}, "RANK": {"a": "(number; ref; [order])", "d": "Retorna la jerarquia d'una xifra en una llista: la seva mida és relativa als altres valors de la llista", "ad": "és la xifra de la qual voleu conèixer la jerarquia!és una matriu o una referència a una llista de xifres. Els valors no numèrics s'ignoren!és una xifra: si la llista està ordenada de forma descendent = 0 o s'omet; si la llista està ordenada de forma ascendent = qualsevol valor que no sigui zero"}, "RANK.AVG": {"a": "(número; ref; [ordre])", "d": "Retorna la classificació d'un número en una llista: la seva mida és relativa als altres valors de la llista; si més d'un valor té la mateixa classificació, es retorna la classificació mitjana", "ad": "és el número del qual voleu conèixer la classificació!és una matriu o una referència a una llista de números. Els valors no numèrics s'ignoren!és un número: si la classificació de la llista està ordenada de forma descendent = 0 o s'omet; si la classificació de la llista està ordenada de forma ascendent = qualsevol valor que no sigui zero"}, "RANK.EQ": {"a": "(número; ref; [ordre])", "d": "Retorna la classificació d'un número en una llista: la seva mida és relativa als altres valors de la llista; si més d'un valor té la mateixa classificació, es retorna la classificació superior d'aquest conjunt de valors", "ad": "és el número del qual voleu conèixer la classificació!és una matriu o una referència a una llista de números. Els valors no numèrics s'ignoren!és un número: si la classificació de la llista està ordenada de forma descendent = 0 o s'omet; si la classificació de la llista està ordenada de forma ascendent = qualsevol valor que no sigui zero"}, "RSQ": {"a": "(conegut_ys; conegut_xs)", "d": "Retorna el quadrat del coeficient del moment de correlació del producte Pearson dels punts de dades donats", "ad": "és una matriu o un interval de punts de dades formada per: números, noms, matrius o referències que continguin números!és una matriu o un interval de punts de dades i poden ser números o noms, matrius o referències que continguin números"}, "SKEW": {"a": "(número1; [número2]; ...)", "d": "Retorna el biaix d'una distribució: una caracterització del grau d'asimetria d'una distribució entorn de la seva mitjana", "ad": "són d'1 a 255 números o noms, matrius o referències que contenen números dels quals voleu obtenir el biaix"}, "SKEW.P": {"a": "(número1; [número2]; ...)", "d": "Retorna el biaix d'una distribució basada en una població: una caracterització del grau d'asimetria d'una distribució entorn de la seva mitjana", "ad": "són d'1 a 254 números o noms, matrius o referències que contenen números dels quals voleu obtenir el biaix de població"}, "SLOPE": {"a": "(conegut_ys; conegut_xs)", "d": "Retorna la pendent d'una línia de regressió lineal dels punts de dades donats", "ad": "és una matriu o un interval de cel·les de punts de dades numèrics dependents i poden ser números, noms, matrius o referències que continguin números!és el conjunt de punts de dades independents i poden ser números, noms, matrius o referències que continguin números"}, "SMALL": {"a": "(matriu; k)", "d": "Retorna el valor k-èsim més petit d'un conjunt de dades. Per exemple, el cinquè número més petit", "ad": "és la matriu o l'interval de dades numèriques del qual voleu determinar el valor k-èsim més petit!és la posició (des del valor més petit) a la matriu o l'interval del valor que s'ha de retornar"}, "STANDARDIZE": {"a": "(x; mitjana; desv_estàndard)", "d": "Retorna un valor normalitzat d'una distribució caracteritzada per una mitjana i una desviació estàndard", "ad": "és el valor que es vol normalitzar!és la mitjana aritmètica de la distribució!és la desviació estàndard de la distribució, un número positiu"}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "Calcula la desviació estàndard d'una mostra (ignora els valors lògics i el text de la mostra)", "ad": "són d'1 a 255 xifres que corresponen a una mostra d'una població i poden ser xifres o referències que contenen xifres"}, "STDEV.P": {"a": "(número1; [número2]; ...)", "d": "Calcula la desviació estàndard de la població total especificada com a arguments (ignora els valors lògics i el text)", "ad": "són d'1 a 255 números que corresponen a una població i poden ser números o referències que continguin números"}, "STDEV.S": {"a": "(número1; [número2]; ...)", "d": "Calcula la desviació estàndard d'una mostra (ignora els valors lògics i el text de la mostra)", "ad": "són d'1 a 255 números que corresponen a una mostra d'una població i poden ser números o referències que contenen números"}, "STDEVA": {"a": "(valor1; [valor2]; ...)", "d": "Fa una estimació de la desviació estàndard a partir d'una mostra, inclosos els valors lògics i el text. El text i el valor lògic FALS tenen el valor 0; el valor lògic CERT té el valor 1", "ad": "són d'1 a 255 valors que corresponen a una mostra d'una població i poden ser valors o noms o referències a valors"}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "Calcula la desviació estàndard de la població total especificada com a arguments (ignora els valors lògics i el text)", "ad": "són d'1 a 255 xifres que corresponen a una població i poden ser xifres o referències que continguin xifres"}, "STDEVPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula la desviació estàndard a partir de tota una població, inclosos els valors lògics i el text. El text i el valor lògic FALS tenen el valor 0; el valor lògic CERT té el valor 1", "ad": "són d'1 a 255 valors que corresponen a una població i poden ser valors, noms, matrius o referències que contenen valors"}, "STEYX": {"a": "(conegut_ys; conegut_xs)", "d": "Retorna l'error típic del valor d'Y previst per a cada X de la regressió", "ad": "és una matriu o un interval de punts de dades dependents i poden ser números o noms, matrius o referències que continguin números!és una matriu o un interval de punts de dades independents, format per: números o noms, matrius o referències que continguin números"}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "Retorna la distribució t de Student", "ad": "és el valor numèric amb què es calcula la distribució!és un enter que indica el nombre de graus de llibertat que caracteritza la distribució!especifica el nombre de cues de distribució que s'han de retornar: distribució d'una cua = 1; distribució de dues cues = 2"}, "TINV": {"a": "(probability; deg_freedom)", "d": "Retorna l'invers de la distribució t de Student", "ad": "és la probabilitat associada amb la distribució t de dues cues de Student, un valor entre 0 i 1, inclosos!és un enter positiu que indica el nombre de graus de llibertat que caracteritza la distribució"}, "T.DIST": {"a": "(x; graus_llibertat; acumulat)", "d": "Retorna la distribució T de Student de cua esquerra", "ad": "és el valor numèric amb què es calcula la distribució!és un enter que indica el nombre de graus de llibertat que caracteritza la distribució!és un valor lògic: per a la funció de distribució acumulativa, utilitzeu CERT; per a la funció de densitat de probabilitat, utilitzeu FALS"}, "T.DIST.2T": {"a": "(x; graus_llibertat)", "d": "Retorna la distribució T de Student de dues cues", "ad": "és el valor numèric amb què es calcula la distribució!és un enter que indica el nombre de graus de llibertat que caracteritza la distribució"}, "T.DIST.RT": {"a": "(x; graus_llibertat)", "d": "Retorna la distribució T de Student de cua dreta", "ad": "és el valor numèric amb què es calcula la distribució!és un enter que indica el nombre de graus de llibertat que caracteritza la distribució"}, "T.INV": {"a": "(probabilitat; graus_llibertat)", "d": "Retorna l'invers de cua esquerra de la distribució T de Student", "ad": "és la probabilitat associada amb la distribució T de dues cues de Student, un número entre 0 i 1, inclosos!és un enter positiu que indica el número de graus de llibertat que caracteritza la distribució"}, "T.INV.2T": {"a": "(probabilitat; graus_llibertat)", "d": "Retorna l'invers de dues cues de la distribució T de Student", "ad": "és la probabilitat associada amb la distribució T de dues cues de Student, un número entre 0 i 1, inclosos!és un enter positiu que indica el número de graus de llibertat que caracteritza la distribució"}, "T.TEST": {"a": "(matriu1; matriu2; cues; tipus)", "d": "Retorna la probabilitat associada amb una prova t de Student", "ad": "és el primer conjunt de dades!és el segon conjunt de dades!especifica el número de cues de distribució per retornar: una cua de distribució = 1, dues cues de distribució = 2!és el tipus de prova t: aparellat = 1, dues mostres de variança igual = 2, dues mostres de variança diferent = 3"}, "TREND": {"a": "(conegut_ys; [conegut_xs]; [nova_xs]; [constant])", "d": "Retorna números en una tendència lineal que coincideix amb punts de dades coneguts, utilitzant el mètode dels mínims quadrats", "ad": "és un interval o una matriu de valors d'y coneguts en la relació y = mx + b!és un interval o una matriu de valors d'x opcionals (que poden ser coneguts) de la relació y = mx + b, una matriu de la mateixa mida que conegut_y!és un interval o una matriu de valors nous d'x per als quals es vol que TENDENCIA retorni els valors d'y corresponents!és un valor lògic: la constant b es calcula normalment si Const = CERT o s'omet; b serà igual a 0 si Const = FALS"}, "TRIMMEAN": {"a": "(matriu; percentatge)", "d": "Retorna la mitjana de la part interior d'un conjunt de valors de dades", "ad": "és l'interval o la matriu de valors que es vol acotar i calcular-ne la mitjana!és el número fraccionari de punts de dades que s'exclouen de l'extrem superior i inferior del conjunt de dades"}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "Retorna la probabilitat associada amb una prova t de Student", "ad": "és el primer conjunt de dades!és el segon conjunt de dades!especifica el nombre de cues de distribució per retornar: una cua de distribució = 1, dues cues de distribució = 2!és el tipus de prova t: aparellat = 1, dues mostres de variància igual = 2, dues mostres de variància diferent = 3"}, "VAR": {"a": "(number1; [number2]; ...)", "d": "Calcula la variància d'una mostra (ignora els valors lògics i el text)", "ad": "són d'1 a 255 arguments numèrics que corresponen a una mostra d'una població"}, "VAR.P": {"a": "(número1; [número2]; ...)", "d": "Calcula la variància de la població total (ignora els valors lògics i el text de la població)", "ad": "són d'1 a 255 arguments numèrics que corresponen a una població"}, "VAR.S": {"a": "(número1; [número2]; ...)", "d": "Calcula la variància d'una mostra (ignora els valors lògics i el text)", "ad": "són d'1 a 255 arguments numèrics que corresponen a una mostra d'una població"}, "VARA": {"a": "(valor1; [valor2]; ...)", "d": "Fa una estimació de la variància a partir d'una mostra, inclosos els valors lògics i el text. El text i el valor lògic FALS tenen el valor 0; el valor lògic CERT té el valor 1", "ad": "són d'1 a 255 arguments de valor que corresponen a una mostra d'una població"}, "VARP": {"a": "(number1; [number2]; ...)", "d": "Calcula la variància de la població total (ignora els valors lògics i el text de la població)", "ad": "són d'1 a 255 arguments numèrics que corresponen a una població"}, "VARPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula la variància a partir de tota la població, inclosos els valors lògics i el text. El text i el valor lògic FALS tenen el valor 0; el valor lògic CERT té el valor 1", "ad": "són d'1 a 255 arguments de valor que corresponen a una població"}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "Retorna la distribució <PERSON>", "ad": "és el valor amb què es vol calcular la funció, un nombre no negatiu!és un paràmetre de la distribució, un nombre positiu!és un paràmetre de la distribució, un nombre positiu!és un valor lògic: perquè retorni la funció de distribució acumulativa, utilitzeu CERT; perquè retorni la funció de probabilitat bruta, utilitzeu FALS"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; acumulat)", "d": "Retorna la distribució <PERSON>", "ad": "és el valor amb què es vol calcular la funció, un número no negatiu!és un paràmetre de la distribució, un número positiu!és un paràmetre de la distribució, un número positiu!és un valor lògic: perquè retorni la funció de distribució acumulativa, utilitzeu CERT; perquè retorni la funció de probabilitat bruta, utilitzeu FALS"}, "Z.TEST": {"a": "(matriu; x; [sigma])", "d": "Retorna el valor P d'una cua d'una prova, z", "ad": "és la matriu o l'interval de dades amb els quals es contrastarà X!és el valor que es vol comprovar!és la desviació estàndard (coneguda) de la població. Si s'omet, s'utilitzarà la desviació estàndard d'exemple"}, "ZTEST": {"a": "(array; x; [sigma])", "d": "Retorna el valor P d'una cua d'una prova, z", "ad": "és la matriu o l'interval de dades amb els quals es contrastarà X!és el valor que es vol comprovar!és la desviació estàndard (coneguda) de la població. Si s'omet, s'utilitzarà la desviació estàndard d'exemple"}, "ACCRINT": {"a": "(emissió; primer_interès; liquidació; taxa; valor_nominal; freqüència; [base]; [mètode_calc])", "d": "Retorna el cupó corregut d'un títol valor que paga interessos periòdics.", "ad": "és la data d'emissió del títol valor, expressada com a nombre de data en sèrie!és la primera data d'interès del títol valor, expressada com a nombre de data en sèrie!és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la taxa de cupó anual del títol valor!és el valor nominal del títol valor!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar!és un valor lògic: per al cupó corregut des de la data d'emissió = CERT o omès; per calcular a partir de la darrera data de pagament de cupó = FALS"}, "ACCRINTM": {"a": "(emissió; liquidació; taxa; valor_nominal; [base])", "d": "Retorna el cupó corregut per a un títol valor que paga interessos al venciment", "ad": "és la data d'emissió del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la taxa de cupó anual del títol valor!és el valor nominal del títol valor!és el tipus de base de recompte de dies que cal utilitzar"}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Retorna l'amortització lineal prorratejada d'un actiu per a cada període comptable.", "ad": "és el cost de l'actiu!és la data de compra de l'actiu!és la data del final del primer període!és el valor residual al final de la vida de l'actiu.!és el període!és la taxa d'amortització!base_any: 0 per a un any de 360 dies, 1 per a real, 3 per a un any de 365 dies."}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Retorna l'amortització lineal prorratejada d'un actiu per a cada període comptable.", "ad": "és el cost de l'actiu!és la data de compra de l'actiu!és la data del final del primer període!és el valor residual al final de la vida de l'actiu.!és el període!és la taxa d'amortització!base_any: 0 per a un any de 360 dies, 1 per a real, 3 per a un any de 365 dies."}, "COUPDAYBS": {"a": "(liquidació; venciment; freqüència; [base])", "d": "Retorna el nombre de dies des de l'inici del període de cupó fins a la data de liquidació", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "COUPDAYS": {"a": "(liquidació; venciment; freqüència; [base])", "d": "Retorna el nombre de dies del període de cupó que conté la data de liquidació", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "COUPDAYSNC": {"a": "(liquidació; venciment; freqüència; [base])", "d": "Retorna el nombre de dies des de la data de liquidació fins a la propera data de cupó", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "COUPNCD": {"a": "(liquidació; venciment; freqüència; [base])", "d": "Retorna la següent data de cupó després de la data de liquidació", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "COUPNUM": {"a": "(liquidació; venciment; freqüència; [base])", "d": "Retorna el nombre de cupons a pagar entre la data de liquidació i la data de venciment", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "COUPPCD": {"a": "(liquidació; venciment; freqüència; [base])", "d": "Retorna la data de cupó anterior abans de la data de liquidació", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Retorna l'interès acumulat pagat entre dos períodes", "ad": "és la taxa d'interès!és el nombre total de períodes de pagament!és el valor actual!és el primer període del càlcul!és el darrer període del càlcul!és el període de pagament"}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Retorna el capital acumulat pagat en un préstec entre dos períodes", "ad": "és la taxa d'interès!és el nombre total de períodes de pagament!és el valor actual!és el primer període del càlcul!és el darrer període del càlcul!és el període de pagament"}, "DB": {"a": "(cost; valor_residual; vida; període; [mes])", "d": "Retorna la depreciació d'un bé durant un període específic per mitjà del mètode de depreciació fixa de saldo", "ad": "és el cost inicial del bé!és el valor residual al final de la vida d'un bé!és el número de períodes al llarg dels quals es produeix la depreciació del bé (que algunes vegades es denomina la vida útil del bé)!és el període del qual es vol calcular la depreciació. Per al Període s'han d'utilitzar les mateixes unitats que per a Vida!és el número de mesos del primer any. Si s'omet Mes, s'assumeix que és 12"}, "DDB": {"a": "(cost; valor_residual; vida; període; [factor])", "d": "Retorna la depreciació d'un bé durant un període específic per mitjà del mètode de depreciació per doble disminució de saldo o un altre mètode que s'especifiqui", "ad": "és el cost inicial del bé!és el valor residual al final de la vida del bé!és el número de períodes al llarg dels quals es produeix la depreciació del bé (que algunes vegades es denomina la vida útil del bé)!és el període del qual es vol calcular la depreciació. Per al Període s'han d'utilitzar les mateixes unitats que per a Vida!és la taxa a la qual disminueix el saldo. Si s'omet Factor, s'assumirà el valor 2 (mètode de doble disminució del saldo)"}, "DISC": {"a": "(liquidació; venciment; pr; amortització; [base])", "d": "Retorna la taxa de descompte d'un títol valor", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és el preu del títol valor per cada 100 € de valor nominal!és el valor de bescanvi del títol valor per cada 100 € de valor nominal!és el tipus de base de recompte de dies que cal utilitzar"}, "DOLLARDE": {"a": "(dòlar_fraccional; fracció)", "d": "Converteix un preu en dòlars expressat com a fracció en un preu en dòlars expressat com a nombre decimal", "ad": "és un nombre expressat com a fracció!és l'enter que s'utilitzarà al denominador de la fracció"}, "DOLLARFR": {"a": "(dòlar_decimal; fracció)", "d": "Converteix un preu en dòlars expressat com a nombre decimal en un preu en dòlars expressat com a fracció", "ad": "és un nombre decimal!és l'enter que s'utilitzarà al denominador de la fracció"}, "DURATION": {"a": "(liquidació; venciment; cupó; rdt; freqüència; [base])", "d": "Retorna la durada anual d'un títol valor amb pagaments d'interès periòdics", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la taxa de cupó anual del títol valor!és el rendiment anual del títol valor!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "EFFECT": {"a": "(taxa_nominal; nombre_per_any)", "d": "Retorna la taxa d'interès efectiva anual", "ad": "és la taxa d'interès nominal!és el nombre de períodes compostos per any"}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "Retorna el valor futur d'una inversió en funció de pagaments periòdics i constants i un tipus d'interès constant", "ad": "és el tipus d'interès per període. Per exemple, podeu utilitzar 6%/4 per als pagaments trimestrals al 6 % TPA!és el nombre total de períodes de pagament d'una inversió!és el pagament fet cada període; no pot canviar durant la vigència de la inversió!és el valor actual o la suma total del valor actual al qual ascendeix una sèrie de pagaments futurs. Si s'omet, Vp = 0!és un valor que representa quan vencen els pagaments: pagament al començament del període = 1; pagament al final del període = 0 o s'omet"}, "FVSCHEDULE": {"a": "(capital; planificació)", "d": "Retorna el valor futur d'un capital inicial després d'aplicar-hi una sèrie de taxes d'interès compost", "ad": "és el valor actual!és una matriu de taxes d'interès que cal aplicar"}, "INTRATE": {"a": "(liquidació; venciment; inversió; amortització; [base])", "d": "Retorna la taxa d'interès per a un títol valor completament invertit", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la quantitat invertida en el títol valor!és la quantitat que es rebrà al venciment!és el tipus de base de recompte de dies que cal utilitzar"}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Retorna l'interès pagat per una inversió en un període determinat, en funció de pagaments periòdics i constants i un tipus d'interès constant", "ad": "és el tipus d'interès per període. Per exemple, podeu utilitzar 6%/4 per als pagaments trimestrals al 6 % TPA!és el període del qual voleu saber l'interès i s'ha de trobar en l'interval d'1 a Nper!és el nombre total de períodes de pagament en una inversió!és el valor actual o la suma total d'una sèrie de pagaments futurs!és el valor futur o saldo en efectiu que es vol obtenir després d'efectuar el darrer pagament. Si s'omet, Vf = 0!és un valor lògic que representa quan vencen els pagaments: si és al final del període = 0 o s'omet; si és al començament del període = 1"}, "IRR": {"a": "(valors; [estimació])", "d": "Retorna la taxa interna de retorn per a una sèrie de fluxos en efectiu", "ad": "és una matriu o referència a cel·les que contenen els números per als quals es vol calcular la taxa interna de retorn!és un número que l'usuari considera que s'aproximarà al resultat de TIR; s'assumeix 0,1 (10 per cent) si s'omet"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "Retorna l'interès pagat durant un període específic d'una inversió", "ad": "tipus d'interès per període. Per exemple, podeu utilitzar 6%/4 per als pagaments trimestrals al 6 % TPA!és el període del qual voleu saber l'interès!número de períodes de pagament d'una inversió!és la suma total del valor actual d'una sèrie de pagaments futurs"}, "MDURATION": {"a": "(liquidació; venciment; cupó; rdt; freqüència; [base])", "d": "Retorna la durada modificada de Macauley per a un títol valor amb un valor nominal assumit de 100 €", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la taxa de cupó anual del títol valor!és el rendiment anual del títol valor!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "Retorna la taxa interna de retorn per a una sèrie de fluxos d'efectiu periòdics tenint en compte el cost de la inversió i l'interès en tornar a invertir l'efectiu", "ad": "és una matriu o una referència a cel·les que contenen números que representen una sèrie de pagaments (negatius) i ingressos (positius) efectuats en períodes regulars!és el tipus d'interès que es paga dels diners utilitzats en fluxos d'efectiu!és el tipus d'interès que es rep dels fluxos d'efectiu a mesura que es tornen a invertir"}, "NOMINAL": {"a": "(taxa_efectiva; nombre_per_any)", "d": "Retorna la taxa d'interès nominal anual", "ad": "és la taxa d'interès efectiva!és el nombre de períodes compostos per any"}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "Retorna el número de pagaments d'una inversió en funció de pagaments periòdics i constants i d’un tipus d'interès constant", "ad": "és el tipus d'interès per període. Per exemple, podeu utilitzar 6%/4 per als pagaments trimestrals al 6 % TPA!és el pagament efectuat cada període; no pot canviar durant la vigència de la inversió!és el valor actual o suma total d'una sèrie de pagaments futurs!és el valor futur o saldo en efectiu que es vol aconseguir després d'efectuar el darrer pagament. Si s'omet, s'utilitza zero!és un valor lògic: per al pagament al començament del període = 1; per al pagament al final del període = 0 o s'omet"}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "Retorna el valor net actual d'una inversió en funció d'una taxa de descompte i d’una sèrie de pagaments futurs (valors negatius) i d'ingressos (valors positius)", "ad": "és la taxa de descompte durant un període!són d'1 a 254 pagaments i ingressos, repartits en el temps de la mateixa manera i que es produeixen al final de cada període"}, "ODDFPRICE": {"a": "(liquidació; venciment; emissió; primer_cupó; taxa; rdt; amortització; freqüència; [base])", "d": "Retorna el preu per cada 100 € de valor nominal d'un títol valor amb un període inicial senar", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la data d'emissió del títol valor, expressada com a nombre de data en sèrie!és la primera data de cupó del títol valor, expressada com a nombre de data en sèrie!és la taxa d'interès del títol valor!és el rendiment anual del títol valor!és el valor de bescanvi del títol valor per cada 100 € de valor nominal!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "ODDFYIELD": {"a": "(liquidació; venciment; emissió; primer_cupó; taxa; pr; amortització; freqüència; [base])", "d": "Retorna el rendiment d'un títol valor amb un primer període senar", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la data d'emissió del títol valor, expressada com a nombre de data en sèrie!és la primera data de cupó del títol valor, expressada com a nombre de data en sèrie!és la taxa d'interès del títol valor!és el preu del títol valor!és el valor de bescanvi del títol valor per cada 100 € de valor nominal!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "ODDLPRICE": {"a": "(liquidació; venciment; darrer_interès; taxa; rdt; amortització; freqüència; [base])", "d": "Retorna el preu per cada 100 € de valor nominal d'un títol valor amb un període final senar", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és el títol valor, expressada com a nombre de data en sèrie!és la taxa d'interès del títol valor!és el rendiment anual del títol valor!és el valor de bescanvi del títol valor per cada 100 € de valor nominal!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "ODDLYIELD": {"a": "(liquidació; venciment; darrer_interès; taxa; pr; amortització; freqüència; [base])", "d": "Retorna el rendiment d'un títol valor amb un període final senar", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la darrera data de cupó del títol valor, expressada com a nombre de data en sèrie!és la taxa d'interès del títol valor!és el preu del títol valor!és el valor de bescanvi del títol valor per cada 100 € de valor nominal!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "PDURATION": {"a": "(taxa; va; vf)", "d": "Retorna el nombre de períodes necessaris per a que una inversió arribi a un valor especificat", "ad": "és la taxa d'interès per període.!és el valor present de la inversió!és el valor futur desitjat de la inversió"}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "Calcula el pagament d'un préstec en funció de pagaments constants i d’un tipus d'interès constant", "ad": "és el tipus d'interès per període del préstec. Per exemple, podeu utilitzar 6%/4 per als pagaments trimestrals al 6% TPA!és el nombre total de pagaments del préstec!és el valor actual: l’import total al qual ascendeix una sèrie de pagaments futurs!és el valor futur o saldo en efectiu que es vol aconseguir després d'efectuar el darrer pagament; s'assumeix 0 (zero) si s'omet!és un valor lògic: per al pagament al començament del període = 1; per al pagament al final del període = 0 o s'omet"}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Retorna el pagament del capital d'una inversió determinada en funció de pagaments constants i periòdics i un tipus d'interès constant", "ad": "és el tipus d'interès per període. Per exemple, util<PERSON><PERSON> 6%/4 per als pagaments trimestrals al 6 % TPA!especifica el període i s'ha de trobar en l'interval d'1 a nper!és el nombre total de períodes de pagament d'una inversió!és el valor actual: l’import total al qual ascendeix una sèrie de pagaments futurs!és el valor futur o el saldo efectiu que es vol aconseguir després d'efectuar el darrer pagament!és un valor lògic: per al pagament al començament del període = 1; per al pagament al final del període = 0 o s'omet"}, "PRICE": {"a": "(liquidació; venciment; taxa; rdt; amortització; freqüència; [base])", "d": "Retorna el preu per cada 100 € de valor nominal d'un títol valor que paga interessos periòdics", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la taxa de cupó anual del títol valor!és el rendiment anual del títol valor!és el valor de bescanvi del títol valor per cada 100 € de valor nominal!és nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "PRICEDISC": {"a": "(liquidació; venciment; descompte; amortització; [base])", "d": "Retorna el preu per cada 100 € de valor nominal d'un títol valor amb descompte", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la taxa de descompte del títol valor!és el valor de bescanvi del títol valor por cada 100 € de valor nominal!és el tipus de base de recompte de dies que cal utilitzar"}, "PRICEMAT": {"a": "(liquidació; venciment; emissió; taxa; rdt; [base])", "d": "Retorna el preu per cada 100 € de valor nominal d'un títol valor que paga interessos al venciment", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la data d'emissió del títol valor, expressada com a nombre de data en sèrie!és la taxa d'interès del títol valor en la data d'emissió!és el rendiment anual del títol valor!és el tipus de base de recompte de dies que cal utilitzar"}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "Retorna el valor actual d'una inversió: l’import total al qual ascendeix una sèrie de pagaments futurs", "ad": "és el tipus d'interès per període. Per exemple, podeu utilitzar 6%/4 per als pagaments trimestrals al 6% TPA!és el nombre total de períodes de pagament d’una inversió!és el pagament efectuat cada període i no pot canviar durant la vigència de la inversió!és el valor futur o saldo en efectiu que es vol aconseguir després d'efectuar el darrer pagament!és un valor lògic: per al pagament al començament del període = 1; per al pagament al final del període = 0 o s'omet"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "Retorna el tipus d'interès per període d'un préstec o d’una inversió. Per exemple, podeu utilitzar 6%/4 per als pagaments trimestrals al 6 % TPA", "ad": "és el nombre total de períodes de pagament d'un préstec o d’una inversió!és el pagament efectuat cada període i no pot canviar durant la vigència del préstec o de la inversió!és el valor actual: l’import total al qual ascendeix una sèrie de pagaments futurs!és el valor futur o saldo en efectiu que es vol aconseguir després d'efectuar el darrer pagament. Si s'omet, s'utilitza Vf = 0!és un valor lògic: per al pagament al començament del període = 1; per al pagament al final del període = 0 o s'omet!és la vostra estimació del tipus d'interès; si s'omet, Estimació = 0,1 (10 %)"}, "RECEIVED": {"a": "(liquidació; venciment; inversió; descompte; [base])", "d": "Retorna la quantitat rebuda al venciment d'un títol valor completament invertit", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la quantitat invertida en el títol valor!és la taxa de descompte del títol valor!és el tipus de base de recompte de dies que cal utilitzar"}, "RRI": {"a": "(nper; va; vf)", "d": "Retorna un tipus d'interès equivalent al creixement d'una inversió", "ad": "és el nombre de períodes de la inversió!és el valor actual de la inversió!és el valor futur de la inversió"}, "SLN": {"a": "(cost; valor_residual; vida)", "d": "Retorna la depreciació per mètode directe d'un bé en un període determinat", "ad": "és el cost inicial del bé!és el valor residual al final de la vida del bé!és el número de períodes al llarg dels quals es produeix la depreciació del bé (que algunes vegades es denomina la vida útil del bé)"}, "SYD": {"a": "(cost; valor_residual; vida; període)", "d": "Retorna la depreciació per mètode d'anualitats d'un bé durant un període específic", "ad": "és el cost inicial del bé!és el valor residual al final de la vida del bé!és el número de períodes al llarg dels quals es produeix la depreciació del bé (que algunes vegades es denomina la vida útil del bé)!és el període i s'han d'utilitzar les mateixes unitats que a Vida"}, "TBILLEQ": {"a": "(liquidació; venciment; descompte)", "d": "Retorna el rendiment equivalent a bo d'una lletra del tresor", "ad": "és la data de liquidació de la lletra del tresor, expressada com a nombre de data en sèrie!és la data de venciment de la lletra del tresor, expressada com a nombre de data en sèrie!és la taxa de descompte de la lletra del tresor"}, "TBILLPRICE": {"a": "(liquidació; venciment; descompte)", "d": "Retorna el preu per cada 100 € de valor nominal d'una lletra del tresor", "ad": "és la data de liquidació de la lletra del tresor, expressada com a nombre de data en sèrie!és la data de venciment de la lletra del tresor, expressada com a nombre de data en sèrie!és la taxa de descompte de la lletra del tresor"}, "TBILLYIELD": {"a": "(liquidació; venciment; pr)", "d": "Retorna el rendiment d'una lletra del tresor", "ad": "és la data de liquidació de la lletra del tresor, expressada com a nombre de data en sèrie!és la data de venciment de la lletra del tresor, expressada com a nombre de data en sèrie!és el preu de la lletra del tresor per cada 100 € de valor nominal"}, "VDB": {"a": "(cost; valor_residual; vida; període_inicial; període_final; [factor]; [sense_canvis])", "d": "Retorna la depreciació d'un bé per a qualsevol període especificat, inclosos els períodes parcials, utilitzant el mètode de depreciació per doble disminució del saldo o un altre mètode que especifiqueu", "ad": "és el cost inicial del bé!és el valor residual al final de la vida d'un bé!és el número de períodes durant els quals es produeix la depreciació del bé (algunes vegades denominat vida útil del bé)!és el període inicial del qual es vol calcular la depreciació, en les mateixes unitats que Vida!és el període final del qual es vol calcular la depreciació, en les mateixes unitats que Vida!és la taxa en què disminueix el saldo; si s'omet, s'assumeix 2 (depreciació per doble disminució)!canvia al mètode directe de depreciació quan la depreciació és superior al saldo en disminució = FALS o s'omet; si no es vol que canviï = CERT"}, "XIRR": {"a": "(values; dates; [guess])", "d": "Retorna la taxa interna de retorn per a una planificació de fluxos d'efectiu", "ad": "és una sèrie de fluxos d'efectiu corresponents a una planificació de pagaments per dates!és una planificació de dates de pagament corresponents als pagaments de flux d'efectiu!és un nombre que estimeu que és aproximat al resultat de TIR.NO.PER"}, "XNPV": {"a": "(rate; values; dates)", "d": "Retorna el valor actual net per a una planificació de fluxos d'efectiu", "ad": "és la taxa de descompte que cal aplicar als fluxos d'efectiu!és una sèrie de fluxos d'efectiu corresponents a una planificació de pagaments per dates!és una planificació de dates de pagament corresponents als pagaments de flux d'efectiu"}, "YIELD": {"a": "(liquidació; venciment; taxa; pr; amortització; freqüència; [base])", "d": "Retorna el rendiment d'un títol valor que paga interessos periòdics", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la taxa de cupó anual del títol valor!és el preu del títol valor per cada 100 € de valor nominal!és el valor de bescanvi del títol valor per cada 100 € de valor nominal!és el nombre de pagaments de cupó per any!és el tipus de base de recompte de dies que cal utilitzar"}, "YIELDDISC": {"a": "(liquidació; venciment; pr; amortització; [base])", "d": "Retorna el rendiment anual per a un títol valor amb descompte. Per exemple, una lletra del tresor", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és el preu del títol valor per cada 100 € de valor nominal!és el valor de bescanvi del títol valor per cada 100 € de valor nominal!és el tipus de base de recompte de dies que cal utilitzar"}, "YIELDMAT": {"a": "(liquidació; venciment; emissió; taxa; pr; [base])", "d": "Retorna el rendiment anual d'un títol valor que genera interessos al venciment", "ad": "és la data de liquidació del títol valor, expressada com a nombre de data en sèrie!és la data de venciment del títol valor, expressada com a nombre de data en sèrie!és la data d'emissió del títol valor, expressada com a nombre de data en sèrie!és la taxa d'interès del títol valor en la data d'emissió!és el preu del títol valor per cada 100 € de valor nominal!és el tipus de base de recompte de dies que cal utilitzar"}, "ABS": {"a": "(número)", "d": "Retorna el valor absolut d'un número, és a dir, un número sense signe", "ad": "és el número real del qual es vol obtenir el valor absolut"}, "ACOS": {"a": "(número)", "d": "Retorna l'arc cosinus d'un número en radians, en l'interval de 0 a Pi. L'arc cosinus és l'angle el cosinus del qual és Número", "ad": "és el cosinus de l'angle que voleu i s'ha de trobar entre -1 i 1"}, "ACOSH": {"a": "(número)", "d": "Retorna el cosinus hiperbòlic invers d'un número", "ad": "és un número real igual o major que 1"}, "ACOT": {"a": "(número)", "d": "Torna l'arctangent d'un número, en radiants en l'interval 0 a Pi.", "ad": " és la cotangent de l'angle que voleu"}, "ACOTH": {"a": "(número)", "d": "Torna la cotangent hiperbòlica inversa d'un número", "ad": "és la cotangent hiperbòlica de l'angle que voleu"}, "AGGREGATE": {"a": "(funció_número; opcions; ref1; ...)", "d": "Retorna un valor afegit en una llista o base de dades", "ad": "és un número de l'1 al 19 que especifica la funció de resum del valor afegit.! és un número del 0 al 7 que especifica els valors que s'ignoraran per al valor afegit!és la matriu o l'interval de dades numèriques sobre els quals es calcula el valor afegit!indica la posició a la matriu; és el valor k-èsim més gran, el valor k-èsim més petit, el percentil k-èsim o el quartil k-èsim.!és un número de l'1 al 19 que especifica la funció de resum del valor afegit.! és un número del 0 al 7 que especifica els valors que s'ignoraran per al valor afegit!són intervals o referències de l'1 al 253 per a les quals voleu el valor afegit"}, "ARABIC": {"a": "(text)", "d": "Converteix un numeral llatí en àrab", "ad": "és el numeral llatí que voleu convertir"}, "ASC": {"a": "(text)", "d": "Per a llenguatges de doble byte (DBCS), la funció canvia caràcters d'amplada completa (doble byte) a caràcters de mitja amplada (un byte)"}, "ASIN": {"a": "(número)", "d": "Retorna l'arc sinus d'un número en radians, en l'interval de -Pi/2 a Pi/2", "ad": "és el sinus de l'angle que voleu i s'ha de trobar entre -1 i 1"}, "ASINH": {"a": "(número)", "d": "Retorna el sinus hiperbòlic invers d'un número", "ad": "és un número real igual o major que 1"}, "ATAN": {"a": "(número)", "d": "Retorna l'arc tangent d'un número en radians, dins de l'interval de -Pi/2 a Pi/2", "ad": "és la tangent de l'angle que voleu obtenir"}, "ATAN2": {"a": "(coord_x; coord_y)", "d": "Retorna l'arc tangent de les coordenades x i y especificades, en radians entre -Pi i Pi, excloent -Pi", "ad": "és la coordenada x del punt!és la coordenada y del punt"}, "ATANH": {"a": "(número)", "d": "Retorna la tangent hiperbòlica inversa d'un número", "ad": "és un número real que ha de ser major que -1 i menor que 1"}, "BASE": {"a": "(número; base; [longitud_mín])", "d": "Converteix un número en una representació de text amb la base proporcionada", "ad": "és el número que voleu convertir!és la base en què voleu convertir el número!és la longitud mínima de la cadena tornada. Si s'omet, els zeros a l'esquerra no s'afegiran"}, "CEILING": {"a": "(número; xifra_significativa)", "d": "Arrodoneix un número cap amunt en el múltiple significatiu més proper", "ad": "és el valor que es vol arrodonir!és el múltiple al qual es vol arrodonir"}, "CEILING.MATH": {"a": "(nombre; [xifra_significativa]; [moda])", "d": "Arrodoneix un número cap amunt, cap a l'enter més proper o el múltiple significatiu més proper", "ad": "és el valor que es vol arrodonir!és el múltiple al qual es vol arrodonir!Quan s'especifiqui i el número no sigui zero, aquesta funció arrodonirà cap a un número que no sigui el zero"}, "CEILING.PRECISE": {"a": "(nombre; [significat])", "d": "Retorna un nombre que s'arrodoneix fins a l'enter més proper o al múltiple més proper de significat", "ad": "és el valor que es vol arrodonir!és el múltiple al qual es vol arrodonir"}, "COMBIN": {"a": "(nombre; nombre_triat)", "d": "Retorna el nombre de combinacions d'un nombre determinat d'elements", "ad": "és el nombre total d'elements!és el nombre d'elements de cada combinació"}, "COMBINA": {"a": "(número; número_triat)", "d": "Retorna el nombre de combinacions amb repeticions per a un determinat nombre d'elements", "ad": "és el nombre total d'elements!és el número d'elements en cada combinació"}, "COS": {"a": "(número)", "d": "Retorna el cosinus d'un angle", "ad": "és l'angle en radians del qual es vol obtenir el cosinus"}, "COSH": {"a": "(número)", "d": "Retorna el cosinus hiperbòlic d'un número", "ad": "és un número real"}, "COT": {"a": "(número)", "d": "Torna la cotangent d'un angle", "ad": "és un angle en els radians per al què voleu la cotangent"}, "COTH": {"a": "(número)", "d": "Torna la cotangent hiperbòlica d'un número", "ad": "és l'angle en radiants per al què voleu la cotangent hiperbòlica"}, "CSC": {"a": "(número)", "d": "Torna la cosecant d'un angle", "ad": "és l'angle en radiants per al què voleu la cosecant"}, "CSCH": {"a": "(número)", "d": "Torna la cosecant hiperbòlica d'un angle", "ad": "és l'angle en radiants per al qual voleu la cosecant hiperbòlica"}, "DECIMAL": {"a": "(número; base)", "d": "Converteix una representació de text d'un número d'una base determinada en un número decimal", "ad": "és el número que voleu convertir!és la base del número que esteu convertint"}, "DEGREES": {"a": "(angle)", "d": "Converteix els radians en graus", "ad": "és un angle en radians que es vol convertir"}, "ECMA.CEILING": {"a": "(número; significat)", "d": "Arrodoneix el nombre fins al múltiple més proper d'importància", "ad": "és el valor que es vol arrodonir!és el múltiple al qual es vol arrodonir"}, "EVEN": {"a": "(número)", "d": "Arrodoneix un número positiu cap amunt i un número negatiu cap avall fins a l'enter parell més proper", "ad": "és el valor que s'arrodoneix"}, "EXP": {"a": "(número)", "d": "Retorna e elevat a la potència d'un número determinat", "ad": "és l'exponent aplicat a la base e. La constant e és igual a 2,71828182845904, la base de l'algoritme natural"}, "FACT": {"a": "(número)", "d": "Retorna el factorial d'un número, igual a 1*2*3*...* Número", "ad": "és el número no negatiu del qual es vol obtenir el factorial"}, "FACTDOUBLE": {"a": "(nombre)", "d": "Retorna el factorial doble d'un nombre", "ad": "és el nombre el factorial doble del qual voleu calcular"}, "FLOOR": {"a": "(número; xifra_significativa)", "d": "Arrodoneix un número cap avall en el múltiple significatiu més proper", "ad": "és el valor numèric que es vol arrodonir!és el múltiple al qual es vol arrodonir. Número i Xifra_significativa han de ser ambdós positius o negatius"}, "FLOOR.PRECISE": {"a": "(nombre; [significat] )", "d": "Retorna un nombre que s'arrodoneix cap avall a l'enter més proper o al múltiple més proper de significat", "ad": "és el valor que es vol arrodonir!és el múltiple al qual es vol arrodonir"}, "FLOOR.MATH": {"a": "(nombre; [xifra_significativa]; [moda])", "d": "Arrodoneix un número cap a baix, cap a l'enter més proper o el múltiple significatiu més proper", "ad": "és el valor que es vol arrodonir!és el múltiple al que es vol arrodonir!quan s'especifiqui i el número no sigui zero, aquesta funció arrodonirà cap a el zero"}, "GCD": {"a": "(nombre1; [nombre2]; ...)", "d": "Retorna el màxim comú divisor", "ad": "són valors de l'1 al 255"}, "INT": {"a": "(número)", "d": "Arrodoneix un número fins a l'enter inferior més proper", "ad": "és el número real que es vol arrodonir a un enter"}, "ISO.CEILING": {"a": "(nombre; [significat])", "d": "Retorna un nombre que s'arrodoneix fins a l'enter més proper o al múltiple més proper d'importància independentment del signe del nombre. No obstant això, si el nombre o el significat és zero, es retorna zero.", "ad": "és el valor que es vol arrodonir!és el múltiple al qual es vol arrodonir"}, "LCM": {"a": "(nombre1; [nombre2]; ...)", "d": "Retorna el mínim comú múltiple", "ad": "són valors de l'1 al 255 per als quals es vol obtenir el mínim comú múltiple"}, "LN": {"a": "(número)", "d": "Retorna l'algoritme natural d'un número", "ad": "és el número real positiu del qual es vol obtenir l'algoritme natural"}, "LOG": {"a": "(número; [base])", "d": "Retorna el logaritme d'un número en la base que especifiqueu", "ad": "és el número real positiu del qual voleu conèixer el logaritme!és la base del logaritme; 10 si s'omet"}, "LOG10": {"a": "(número)", "d": "Retorna l'algoritme en base 10 d'un número", "ad": "és el número real positiu del qual es vol obtenir l'algoritme en base 10"}, "MDETERM": {"a": "(matriu)", "d": "Retorna el determinant matricial d'una matriu", "ad": "és una matriu numèrica amb un número igual de files i columnes, ja sigui un interval de cel·les o una constant matricial"}, "MINVERSE": {"a": "(matriu)", "d": "Retorna la matriu inversa de la matriu a dins d'una matriu", "ad": "és una matriu numèrica amb un nombre igual de files i columnes, ja sigui un interval de cel·les o una constant matricial"}, "MMULT": {"a": "(matriu1; matriu2)", "d": "Retorna el producte matricial de dues matrius, una matriu amb el mateix nombre de files que matriu1 i columnes que matriu2", "ad": "és la primera matriu de nombres per multiplicar i el seu nombre de columnes ha de ser igual al nombre de files de matriu2"}, "MOD": {"a": "(número; núm_divisor)", "d": "Retorna el residu després de dividir un número per un divisor", "ad": "és el número del qual es vol trobar el residu després de realitzar la divisió!és el número pel qual es vol dividir Número"}, "MROUND": {"a": "(nombre; múltiple)", "d": "Retorna un nombre arrodonit al múltiple desitjat", "ad": "és el valor que cal arrodonir!és el múltiple al qual voleu arrodonir el nombre"}, "MULTINOMIAL": {"a": "(nombre1; [nombre2]; ...)", "d": "Retorna la distribució multinomial d'un conjunt de nombres", "ad": "són valors de l'1 al 255 per als quals es vol obtenir la distribució multinomial"}, "MUNIT": {"a": "(dimensió)", "d": "Torna la matriu unitària de la dimensió especificada", "ad": "és un enter que especifica la dimensió de la matriu unitària que voleu tornar"}, "ODD": {"a": "(número)", "d": "Arrodoneix un número positiu cap amunt i un número negatiu cap avall, cap a l'enter imparell més proper", "ad": "és el valor que es vol arrodonir"}, "PI": {"a": "()", "d": "Retorna el valor de Pi, 3,14159265358979, amb una precisió de 15 decimals", "ad": ""}, "POWER": {"a": "(número; potencia)", "d": "Retorna el resultat d'un número elevat a una potència", "ad": "és el número base, qualsevol número real!és l'exponent al qual s'eleva el número base"}, "PRODUCT": {"a": "(número1; [número2]; ...)", "d": "Multiplica tots els números especificats com a arguments", "ad": "són d'1 a 255 números, valors lògics o text que representa números que voleu multiplicar"}, "QUOTIENT": {"a": "(numerador; denominador)", "d": "Retorna la part entera d'una divisió", "ad": "és el dividend!és el divisor"}, "RADIANS": {"a": "(angle)", "d": "Converteix els graus en radians", "ad": "és un angle en graus que es vol convertir"}, "RAND": {"a": "()", "d": "Retorna un número aleatori major o igual que 0 i menor que 1, distribu<PERSON>t (canvia en tornar a calcular)", "ad": ""}, "RANDARRAY": {"a": "([files]; [columnes]; [mínim]; [màxim]; [enter])", "d": "Retorna una matriu de nombres aleatoris", "ad": "el nombre de files de la matriu retornada!el nombre de columnes de la matriu retornada!el nombre mínim que voleu que es retorni!el nombre màxim que voleu que es retorni!retorna un enter o un valor decimal. TRUE per a un enter i FALSE per a un nombre decimal"}, "RANDBETWEEN": {"a": "(inferior; superior)", "d": "Retorna un nombre aleatori entre els nombres que especifiqueu", "ad": "és l'enter més petit que retornarà ALEATORIO.ENTRE!és l'enter més gran que retornarà ALEATORIO.ENTRE"}, "ROMAN": {"a": "(número; [forma])", "d": "Converteix un número aràbic en romà, en format de text", "ad": "és el número aràbic que voleu convertir!és el número que especifica el tipus de número romà que voleu obtenir."}, "ROUND": {"a": "(número; núm_decimals)", "d": "Arrodoneix un número al número de decimals especificat", "ad": "és el número que es vol arrodonir!especifica el nombre de decimals al qual es vol arrodonir. Els números negatius s'arrodoneixen a l'esquerra de la coma decimal; el zero s'arrodoneix a l'enter més proper"}, "ROUNDDOWN": {"a": "(número; núm_decimals)", "d": "Arrodoneix un número cap a baix, cap a zero", "ad": "és qualsevol número real que voleu arrodonir cap a baix!és el número de decimals a què voleu arrodonir. Per als números negatius s'arrodoneix cap a l'esquerra de la coma decimal; si s'omet o el valor és zero, s'arrodoneix cap a l'enter més proper"}, "ROUNDUP": {"a": "(número; núm_decimals)", "d": "Arrodoneix un número cap a dalt, en direcció contrària a zero", "ad": "és qualsevol número real que voleu arrodonir cap a dalt!és el número de decimals a què voleu arrodonir. Per als números negatius s'arrodoneix cap a l'esquerra de la coma decimal; si s'omet o el valor és zero, s'arrodoneix cap a l'enter més proper"}, "SEC": {"a": "(número)", "d": "Torna la secant d'un angle", "ad": "és l'angle en radiants per al què voleu la secant"}, "SECH": {"a": "(número)", "d": "Torna la secant hiperbòlica d'un angle", "ad": "és l'angle en radiants per al què voleu la secant hiperbòlica"}, "SERIESSUM": {"a": "(x; n; m; coeficients)", "d": "Retorna la suma d'una sèrie de potències basant-se en la fórmula", "ad": "és el valor d'entrada per a la sèrie de potències!és la potència inicial a la qual voleu elevar x!és el pas en què s'incrementa n per a cada terme de la sèrie!és un conjunt de coeficients pel qual cada potència successiva d'x es multiplica"}, "SIGN": {"a": "(número)", "d": "Retorna el signe d'un número: 1 si és número és positiu, zero si el número és zero i -1 si el número és negatiu", "ad": "és qualsevol número real"}, "SIN": {"a": "(número)", "d": "Retorna el sinus d'un angle determinat", "ad": "és l'angle en radians del qual es vol obtenir el sinus. Graus * PI()/180 = radians"}, "SINH": {"a": "(número)", "d": "Retorna el sinus hiperbòlic d'un número", "ad": "és un número real"}, "SQRT": {"a": "(número)", "d": "Retorna l'arrel quadrada d'un número", "ad": "és el número del qual es vol obtenir l'arrel quadrada"}, "SQRTPI": {"a": "(nombre)", "d": "Retorna l'arrel quadrada de (nombre * Pi)", "ad": "és el nombre pel qual es multiplica p"}, "SUBTOTAL": {"a": "(funció_número; ref1; ...)", "d": "Retorna un subtotal en una llista o base de dades", "ad": "és el número de l'1 a l'11 que especifica la funció de resum del subtotal.!són d'1 a 254 intervals o referències dels quals voleu obtenir el subtotal"}, "SUM": {"a": "(número1; [número2]; ...)", "d": "Suma tots els números d'un interval de cel·les", "ad": "són d'1 a 255 números que s'han de sumar. Els valors lògics i el text de les cel·les s'ignoren, fins i tot si estan escrits com a arguments"}, "SUMIF": {"a": "(interval; criteris; [interval_suma])", "d": "Suma les cel·les especificades amb una condició o uns criteris especificats", "ad": "és l'interval de cel·les que es vol calcular!és la condició o els criteris en forma de número, expressió o text que defineix les cel·les que se sumaran!són les cel·les reals que s'han de sumar. Si s’omet, s'utilitzaran les cel·les de l’interval"}, "SUMIFS": {"a": "(interval_suma; interval_criteris; criteris; ...)", "d": "Suma les cel·les especificades per un conjunt de condicions o criteris determinat", "ad": "són les cel·les reals que es volen sumar.!és l'interval de cel·les que voleu calcular per a la condició concreta!és la condició o criteri en format de número, expressió o text que defineix les cel·les que se sumaran"}, "SUMPRODUCT": {"a": "(matriu1; [matriu2]; [matriu3]; ...)", "d": "Retorna la suma dels productes dels intervals o les matrius corresponents", "ad": "són de 2 a 255 matrius els components de les quals voleu multiplicar i després sumar. Totes les matrius han de tenir les mateixes dimensions"}, "SUMSQ": {"a": "(número1; [número2]; ...)", "d": "Retorna la suma dels quadrats dels arguments. Els arguments poden ser números, matrius, noms o referències a cel·les que contenen números", "ad": "són d'1 a 255 números, matrius, noms o referències a matrius dels quals voleu obtenir la suma dels quadrats"}, "SUMX2MY2": {"a": "(matriu_x; matriu_y)", "d": "Suma les diferències entre els quadrats de dos intervals o matrius corresponents", "ad": "és el primer interval o matriu de números i pot ser un número o un nom, una matriu o una referència que conté números!és el segon interval o matriu de números i pot ser un número o un nom, una matriu o una referència que conté números"}, "SUMX2PY2": {"a": "(matriu_x; matriu_y)", "d": "Retorna la suma total de les sumes dels quadrats de números de dos intervals o matrius corresponents", "ad": "és el primer interval o matriu de números i pot ser un número o un nom, una matriu o una referència que conté números!és el segon interval o matriu de números i pot ser un número o un nom, una matriu o una referència que conté números"}, "SUMXMY2": {"a": "(matriu_x; matriu_y)", "d": "<PERSON><PERSON> els quadrats de les diferències en dos intervals o matrius corresponents", "ad": "és el primer interval o matriu de valors i pot ser un número o un nom, una matriu o una referència que conté números!és el segon interval o matriu de valors i pot ser un número o un nom, una matriu o una referència que conté números"}, "TAN": {"a": "(número)", "d": "Retorna la tangent d'un angle", "ad": "és l'angle en radians del qual es vol obtenir la tangent. Graus * PI()/180 = radians"}, "TANH": {"a": "(número)", "d": "Retorna la tangent hiperbòlica d'un número", "ad": "és un número real"}, "TRUNC": {"a": "(número; [núm_decimals])", "d": "Converteix un número decimal en un d'enter suprimint-ne la part decimal o de fracció", "ad": "és el número que voleu truncar!és un número que especifica la precisió del truncament, 0 (zero) si s'omet"}, "ADDRESS": {"a": "(fila; columna; [abs]; [a1]; [full])", "d": "Crea una referència de cel·la en forma de text una vegada s'han especificat els números de fila i columna", "ad": "és el número de fila que s'utilitza a la referència de cel·la: Núm_fila = 1 per a la fila 1!és el número de columna que s'utilitza a la referència de cel·la. Per exemple, Núm_columna = 4 per a la columna D!especifica el tipus de referència: absoluta = 1;fila absoluta i columna relativa = 2; fila relativa i columna absoluta = 3; relativa =4!és el valor lògic que especifica l'estil de referència: per a l'estil A1 = 1 o CERT; per a l'estil F1C1 = 0 o FALS!és el nom del full de càlcul que s'utilitzarà com a referència externa"}, "CHOOSE": {"a": "(núm_índex; valor1; [valor2]; ...)", "d": "Escull un valor o una acció d'una llista de valors a partir d'un número d'índex", "ad": "especifica l'argument de valor que se selecciona. El núm_índex s'ha de trobar entre 1 i 254 o ser una fórmula o una referència a un número entre 1 i 254!són d'1 a 254 números, referències de cel·la, noms definits, fórmules, funcions o arguments de text d'entre els quals TRIA en tria un"}, "COLUMN": {"a": "([ref])", "d": "Retorna el número de columna d'una referència", "ad": "és la cel·la o l'interval de cel·les contigües de què es vol obtenir el número de columna. Si s'omet, s'utilitza la cel·la que conté la funció COLUMNA"}, "COLUMNS": {"a": "(matriu)", "d": "Retorna el número de columnes d'una matriu o una referència", "ad": "és una matriu, una fórmula de matriu o una referència a un interval de cel·les del qual es vol conèixer el número de columnes"}, "FORMULATEXT": {"a": "(referència)", "d": "Torna una fórmula com una cadena", "ad": "és una referència a una fórmula"}, "HLOOKUP": {"a": "(valor_cercat; matriu_taula; indicador_files; [interval_cercat])", "d": "Cerca un valor a la fila superior d'una taula o una matriu de valors i retorna el valor a la mateixa columna des d'una fila especificada", "ad": "és el valor que se cerca a la primera fila de la taula i pot ser un valor, una referència o una cadena de text!és una taula de text, números o valors lògics on se cerquen les dades. Matriu_taula pot ser una referència a un interval o un nom d'interval!és el número de fila de matriu_taula des del qual s'hauria de retornar el valor que coincideixi. La primera fila de valors de la taula és la fila 1!és un valor lògic: per trobar la coincidència més propera a la fila superior (ordenada de forma ascendent) = CERT o s'omet; per trobar una coincidència exacta = FALS"}, "HYPERLINK": {"a": "(ubic<PERSON><PERSON><PERSON>_enllaç; [nom_descriptiu])", "d": "Crea una drecera o salt que obre un document emmagatzemat al disc dur, a un servidor de xarxa o a Internet", "ad": "és el text que dóna el camí i el nom de fitxer del document que s'ha d'obrir, una ubicació del disc dur, una adreça UNC o un camí URL!és text o un número que es visualitza a la cel·la. Si s'omet, la cel·la mostra el text d'ubicació_enllaç"}, "INDEX": {"a": "(matriu; núm_fila; [núm_columna]!ref; núm_fila; [núm_columna]; [núm_à<PERSON>])", "d": "Retorna un valor o una referència de la cel·la a la intersecció d'una fila i una columna concretes, en un interval determinat", "ad": "és un interval de cel·les o una constant de matriu.!selecciona la fila de Matriu o Referència de la qual es vol obtenir un valor. Si s'omet, Núm_columna és necessari!selecciona la columna de Matriu o Referència de la qual es vol obtenir un valor. Si s'omet, Núm_fila és necessari!és una referència a un o més intervals de cel·les!selecciona la fila de Matriu o Referència de la qual es vol obtenir un valor. Si s'omet, Núm_columna és necessari!selecciona la columna de Matriu o Referència de la qual es vol obtenir un valor. Si s'omet, Núm_fila és necessari!selecciona un interval de Referència del qual es vol obtenir un valor. La primera àrea seleccionada o introduïda és l'àrea 1, la segona àrea és l'àrea 2 i així successivament"}, "INDIRECT": {"a": "(ref; [a1])", "d": "Retorna la referència especificada per una cadena de text", "ad": "és una referència a una cel·la que conté una referència del tipus A1- o F1C1, un nom definit com una referència o una referència a una cel·la com una cadena de text!és un valor lògic que especifica el tipus de referència a Ref: tipus F1C1 = FALS; tipus A1 = CERT o s'omet"}, "LOOKUP": {"a": "(valor_cercat; vector_cercat; [vector_resultat]!valor_cercat; matriu)", "d": "Cerca un valor a partir d'un interval d'una fila o una columna o a partir d'una matriu. Proporcionat per a compatibilitat universal", "ad": "és un valor que CONSULTA cerca a vector_cercat i pot ser un número, text, un valor lògic o un nom o referència a un valor!és un interval que conté només una fila o una columna de text, números o valors lògics, ordenats en ordre ascendent!és un interval que conté només una fila o una columna, de la mateixa mida que vector_cercat!és un valor que CONSULTA cerca a la Matriu i pot ser un número, text, un valor lògic o un nom o referència a un valor!és un interval de cel·les que contenen text, números o valors lògics que es volen comparar amb valor_cercat"}, "MATCH": {"a": "(valor_cercat; matriu_cercada; [tipus_coincidència])", "d": "Retorna la posició relativa d'un element en una matriu, que coincideix amb un valor determinat en un ordre especificat", "ad": "és el valor que s'utilitza per trobar el valor desitjat d'una matriu i pot ser un número, text, un valor lògic o una referència a un d'aquests valors!és un interval contigu de cel·les que contenen possibles valors de cerca, una matriu de valors o una referència a una matriu!és un número 1, 0 o -1 que indica el valor que es retornarà."}, "OFFSET": {"a": "(ref; files; columnes; [alçada]; [amplada])", "d": "Retorna una referència a un interval que és un número determinat de files i columnes d'una referència especificada", "ad": "és la referència en què es vol basar la desviació, una referència a una cel·la o un interval de cel·les adjacents!és el número de files, cap amunt o cap avall, a les quals es vol que faci referència el resultat de la cel·la superior esquerra!és el número de columnes, cap a la dreta o l'esquerra, a les quals es vol que faci referència el resultat de la cel·la superior esquerra!és l'alçada, en número de files, que es vol que tingui el resultat i que, si s'omet, tindrà la mateixa alçada que Ref!és l'amplada, en número de columnes, que es vol que tingui el resultat i que, si s'omet, tindrà la mateixa amplada que Ref"}, "ROW": {"a": "([ref])", "d": "Retorna el número de fila d'una referència", "ad": "és la cel·la o un interval individual de cel·les del qual es vol obtenir el número de fila; si s'omet, retorna la cel·la que conté la funció FILA"}, "ROWS": {"a": "(matriu)", "d": "Retorna el número de files d'una referència o una matriu", "ad": "és una matriu, una fórmula de matriu o una referència a un interval de cel·les del qual es vol conèixer el número de files"}, "TRANSPOSE": {"a": "(matriu)", "d": "Converteix un interval vertical de cel·les en un interval horitzontal o viceversa", "ad": "és un interval de cel·les d'un full de càlcul o una matriu de valors que voleu transposar"}, "UNIQUE": {"a": "(matriu; [per_columna]; [apareix_una_vegada])", "d": "Retorna els valors únics d'un interval o una matriu.", "ad": "l'interval o la matriu de què s'han de retornar les files o les columnes úniques!és un valor lògic: compara les files i retorna les files úniques = _FALS o omès; compara les columnes i retorna les columnes úniques = CERT!és un valor lògic: retorna les files o les columnes que succeeixen exactament una vegada de la matriu = CERT; retorna totes les files o les columnes diferents de la matriu = FALS o omès"}, "VLOOKUP": {"a": "(valor_cercat; matriu_taula; indicador_columnes; [interval_cercat])", "d": "Cerca un valor a la columna de més a l'esquerra d'una taula i retorna un valor a la mateixa fila des d'una columna especificada. Per defecte, la taula s'ordena de manera ascendent", "ad": "és el valor que s'ha de cercar a la primera columna de la taula, i pot ser un valor, una referència o una cadena de text!és una taula de text, números o valors lògics d'on es recuperen les dades. Matriu_taula pot ser una referència a un interval o un nom d'interval!és el número de la columna de matriu_taula des d'on s'han de recuperar els valors coincidents. La primera columna de valors de la taula és la columna 1!és un valor lògic: per trobar la coincidència més propera a la primera columna (ordenada de forma ascendent) = CERT o s'omet; per trobar una coincidència exacta = FALS"}, "XLOOKUP": {"a": "(valor_consulta; matriu_consulta; matriu_retorn; [si_no_es_troba]; [mode_coincidència]; [mode_cerca])", "d": "Cerca una coincidència en un interval o una matriu i retorna l'element corresponent d'un segon interval o d'una segona matriu. Per defecte, s'utilitzarà una coincidència exacta", "ad": "és el valor que s'ha de cercar!és la matriu o l'interval en què s'ha de cercar!és la matriu o l'interval que s'ha de retornar!es retorna si no es troba cap coincidència!especifica com s'ha de relacionar valor_consulta amb els valors de matriu_consulta!especifica el mode de cerca que s'ha d'utilitzar. Per defecte, s'utilitzarà el mode de cerca del primer element a l'últim"}, "CELL": {"a": "(info_tipus; [referència])", "d": "Retorna la informació sobre el format, la ubicació o el contingut d'una cel·la"}, "ERROR.TYPE": {"a": "(val_error)", "d": "Retorna un número que coincideix amb un valor d'error.", "ad": "és el valor d'error del qual voleu conèixer el número d'identificació i pot ser un valor d'error real o una referència a una cel·la que conté un valor d'error"}, "ISBLANK": {"a": "(valor)", "d": "Comprova si una referència és una cel·la buida i retorna CERT o FALS", "ad": "és la cel·la o un nom que fa referència a la cel·la que voleu provar"}, "ISERR": {"a": "(value)", "d": "Comprova si un valor és un error diferent de #N/D i retorna TRUE o FALSE", "ad": "és el valor que voleu provar. El valor pot fer referència a una cel·la, una fórmula o un nom que fa referència a una cel·la, una fórmula o un valor"}, "ISERROR": {"a": "(value)", "d": "Comprova si un valor és un error i retorna TRUE o FALSE", "ad": "és el valor que voleu provar. El valor pot fer referència a una cel·la, una fórmula o un nom que fa referència a una cel·la, una fórmula o un valor"}, "ISEVEN": {"a": "(nombre)", "d": "Retorna CERT si el nombre és parell", "ad": "és el valor que es provarà"}, "ISFORMULA": {"a": "(referència)", "d": "Comprova si una referència correspon a una cel·la amb una fórmula i torna CERT o FALS", "ad": "és una referència a una cel·la que voleu provar. La referència pot ser una referència de cel·la, una fórmula, o un nom que faci referència a una cel·la"}, "ISLOGICAL": {"a": "(valor)", "d": "Comprova si un valor és un valor lògic (CERT o FALS) i retorna CERT o FALS", "ad": "és el valor que voleu provar. Valor pot fer referència a una cel·la, una fórmula o ser un nom que fa referència a una cel·la, una fórmula o un valor"}, "ISNA": {"a": "(valor)", "d": "Comprova si un valor és #N/D i retorna CERT o FALS", "ad": "és el valor que es vol provar. El valor pot fer referència a una cel·la, una fórmula o un nom que fa referència a una cel·la, fórmula o valor"}, "ISNONTEXT": {"a": "(valor)", "d": "Comprova si un valor no és text (les cel·les en blanc no són text) i retorna CERT o FALS", "ad": "és el valor que voleu provar: una cel·la, una fórmula o un nom que fa referència a una cel·la, una fórmula o un valor"}, "ISNUMBER": {"a": "(valor)", "d": "Comprova si un valor és un número i retorna CERT o FALS", "ad": "és el valor que voleu provar. Valor pot fer referència a una cel·la, una fórmula o ser un nom que fa referència a una cel·la, una fórmula o un valor"}, "ISODD": {"a": "(nombre)", "d": "Retorna CERT si el nombre és senar", "ad": "és el valor que es provarà"}, "ISREF": {"a": "(valor)", "d": "Comprova si un valor és una referència i retorna CERT o FALS", "ad": "és el valor que voleu provar. <PERSON>or pot fer referència a una cel·la, una fórmula o un nom que fa referència a una cel·la, una fórmula o un valor"}, "ISTEXT": {"a": "(valor)", "d": "Comprova si un valor és text i retorna CERT o FALS", "ad": "és el valor que voleu provar. Valor pot fer referència a una cel·la, una fórmula o ser un nom que fa referència a una cel·la, una fórmula o un valor"}, "N": {"a": "(valor)", "d": "Converteix els valors no numèrics en números, les dates en números de sèrie, CERT en 1, qualsevol altra cosa en 0 (zero)", "ad": "és el valor que voleu convertir"}, "NA": {"a": "()", "d": "Retorna el valor d'error #N/D (valor no disponible)", "ad": ""}, "SHEET": {"a": "([valor])", "d": "Retorna el número de full del full de referència", "ad": "és el nom d'un full o d'una referència de la qual voleu el número de full. Si s'omet, es tornarà el número del full que conté la funció"}, "SHEETS": {"a": "([referència])", "d": "Retorna el nombre de fulls d'una referència", "ad": "és una referència de la qual voleu saber el número de fulls que conté. Si s'omet, es retornarà el nombre de fulls del llibre de treball que conté la funció"}, "TYPE": {"a": "(valor)", "d": "Retorna un valor enter que representa el tipus de dades d'un valor: nombre = 1; text = 2; valor lògic = 4; valor d'error = 16; matriu = 64; dades compostes = 128", "ad": "pot ser qualsevol valor"}, "AND": {"a": "(valor_lògic1; [valor_lògic2]; ...)", "d": "Comprova si tots els arguments són CERT i retorna CERT si tots els arguments són CERT", "ad": "són d'1 a 255 condicions que es volen comprovar, que poden ser CERT o FALS i poden ser valors lògics, matrius o referències"}, "FALSE": {"a": "()", "d": "Retorna el valor lògic FALS", "ad": ""}, "IF": {"a": "(prova_lògica; [valor_si_cert]; [valor_si_fals])", "d": "Comprova si es compleix una condició i retorna un valor si és CERT i un altre valor si és FALS", "ad": "és qualsevol valor o expressió que pot donar com a resultat CERT o FALS!És el valor que es retorna si Prova_lògica és CERT. Si s'omet, es retorna CERT. Podeu incrustar set funcions IF!és el valor que es retorna si Prova_lògica és FALS. Si s'omet, es retorna FALS"}, "IFS": {"a": "(prova_lògica1; valor_si_cert; ...)", "d": "Comprova si es compleix una condició com a mínim i retorna un valor que correspon a la primera condició CERT", "ad": "és qualsevol valor o expressió que pugui donar com a resultat CERT o FALS!és el valor retornat si la prova lògica és CERT"}, "IFERROR": {"a": "(valor; valor_si_error)", "d": "Retorna valor_si_error si l'expressió és un error i el valor de l'expressió si no ho és", "ad": "és qualsevol valor o expressió o referència!és qualsevol valor o expressió o referència"}, "IFNA": {"a": "(valor; valor_si_nd)", "d": "Retorna el valor que heu especificat si el resultat de l'expressió és #ND, o bé torna el resultat de l'expressió", "ad": "és qualsevol valor o expressió o referència!és qualsevol valor o expressió o referència"}, "NOT": {"a": "(valor_lògic)", "d": "Canvia FALS per CERT, o CERT per FALS", "ad": "és un valor o una expressió que pot donar com a resultat CERT o FALS"}, "OR": {"a": "(valor_lògic1; [valor_lògic2]; ...)", "d": "Comprova si algun dels arguments és CERT i retorna CERT o FALS. Retorna FALS si tots els arguments són FALS", "ad": "són d'1 a 255 condicions que es volen comprovar, que poden ser CERT o FALS"}, "SWITCH": {"a": "(expressió; valor1; resultat1; [per_defecte_o_valor2]; [resultat2]; ...)", "d": "Calcula una expressió amb una llista de valors i retorna el resultat corresponent al primer valor que coincideixi. Si no hi ha coincidències, retorna un valor per defecte opcional", "ad": "és una expressió que cal calcular!és un valor que cal comparar amb l'expressió!és el resultat que cal retornar si el valor corresponent coincideix amb l'expressió"}, "TRUE": {"a": "()", "d": "Retorna el valor lògic CERT", "ad": ""}, "XOR": {"a": "(lògica1; [lògica2]; ...)", "d": "Torna una lògica \"Exclusiu o\" de tots els arguments", "ad": "són d'1 a 254 condicions que voleu provar que poden ser CERT o FALS i poden ser valors, referències o matrius lògiques"}, "TEXTBEFORE": {"a": "(text, delimitador, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retorna text que és abans de delimitar caràcters.", "ad": "El text que voleu cercar al delimitador.!El caràcter o cadena que s'utilitzarà com a delimitador.!L'ocurrència desitjada del delimitador. El valor per defecte és 1. Un nombre de cerques negatiu des del final.!Cerca una coincidència delimitadora al text. Per defecte, s'ha completat una coincidència que distingeix entre majúscules i minúscules.!Si s'ha de coincidir amb el delimitador al final del text. Per defecte, no coincideixen.!Es retorna si no es troba cap coincidència. Per defecte, es retorna el valor #N/A."}, "TEXTAFTER": {"a": "(text, delimitador, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retorna text que és després de delimitar caràcters.", "ad": "Text que voleu cercar al delimitador.!Caràcter o cadena que s'utilitzarà com a delimitador.!Ocurrència desitjada del delimitador. El valor per defecte és 1. Un nombre de cerques negatiu des del final.!Cerca una coincidència delimitadora al text. Per defecte, s'ha completat una coincidència que distingeix entre majúscules i minúscules.!Si s'ha de coincidir amb el delimitador al final del text. Per defecte, no coincideixen.!Es retorna si no es troba cap coincidència. Per defecte, #Nretorna el valor /A."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": " Divideix el text en files o columnes utilitzant delimitadors.", "ad": "El text a dividir!El caràcter o cadena pel que s'han de dividir les columnes.!El caràcter o cadena pel que s'han de dividir les files.! Si s'han d'ignorar les cel·les buides. El valor per defecte és FALSE.!Cerca una coincidència delimitadora al text. Per defecte, s’ha completat una coincidència que distingeix entre majúscules i minúscules.!El valor que s’utilitzarà per a l’espaiat. Per defecte s'utilitza #N/A."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Ajusta un vector de fila o columna després d’un nombre de valors especificat.", "ad": "El vector o la referència per ajustar.!El nombre màxim de valors per fila.!El valor amb què farcir. El valor predeterminat és #N/A."}, "VSTACK": {"a": "(array1, [matriu2], ...)", "d": "<PERSON><PERSON><PERSON> les matrius verticalment en una sola matriu.", "ad": "<PERSON><PERSON><PERSON> o referència que s'ha d'apilar."}, "HSTACK": {"a": "(array1, [matriu2], ...)", "d": "<PERSON><PERSON><PERSON> les matrius horitzontalment en una sola matriu.", "ad": "<PERSON><PERSON><PERSON> o referència que s'ha d'apilar."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Retorna files d'una matriu o una referència.", "ad": "La matriu o referència que conté les files que es retornaran.!El número de la fila que es retornarà."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Retorna columnes d'una matriu o una referència.", "ad": "La matriu o referència que conté les columnes que es retornaran.!El nombre de la columna que es retornarà."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Retorna la matriu com una columna.", "ad": "La matriu o la referència a retornar com a columna.!Si s'han d'ignorar determinats tipus de valors.Per defecte, no s'ignora cap valor.!Escaneja la matriu per columna. Per defecte, la matriu s'escaneja per fila."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Retorna la matriu com una fila.", "ad": "La matriu o referència que es retornarà com a fila!Si s'han d'ignorar determinats tipus de valors. Per defecte, no s'ignora cap valor.!Escaneja la matriu per columna. Per defecte, la matriu s'explora per fila."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Ajusta un vector de fila o columna després d’un nombre de valors especificat.", "ad": "El vector o la referència per ajustar.!El nombre màxim de valors per columna.!El valor amb què farcir. El valor predeterminat és #N/A."}, "TAKE": {"a": "(array, files, [columnes])", "d": "Retorna files o columnes des de l'inici o el final de la matriu.", "ad": "La matriu des de la qual s'han de prendre files o columnes.!El nombre de files a prendre. Un valor negatiu pren des del final de la matriu.!El nombre de columnes a prendre. Un valor negatiu pren des del final de la matriu."}, "DROP": {"a": "(array, rows, [columns])", "d": "Deixa anar files o columnes de l'inici o el final de la matriu.", "ad": "La matriu a partir de la qual s'han d'arrossegar files o columnes.!El nombre de files que s'han de deixar anar. Un valor negatiu cau del final de la matriu.!El nombre de columnes que s'han de deixar anar. Un valor negatiu cau del final de la matriu."}, "SEQUENCE": {"a": "(rows, [columns], [start], [step])", "d": "Retorna una seqüència de nombres", "ad": "el nombre de files que s'ha de retornar!el nombre de columnes que s'ha de retornar!el primer nombre de la seqüència!la quantitat que s'ha d'augmentar cada valor subsegüent de la seqüència"}, "EXPAND": {"a": "(matriu, files, [columnes], [pad_with])", "d": "Expandeix una matriu a les dimensions especificades", "ad": "La matriu que s'expandirà!El nombre de files de la matriu expandida. Si falta, les files no s'expandiran.!El nombre de columnes de la matriu expandida. Si falta, les columnes no s'expandiran.!El valor que s'utilitzara per a l'espaiat. Per defecte és #N/A."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "Retorna la posició relativa d'un element que forma part d'una matriu. Per defecte, cal una coincidència exacta", "ad": "és el valor que s'ha de cercar!és la matriu o l'interval que s'ha de cercar!especifica com s'ha de relacionar el valor_consulta amb els valors de matriu_consulta!especifica el mode de cerca que s'ha de fer servir. Per defecte, es farà servir el mode de cerca del primer element a l'últim"}, "FILTER": {"a": "(array, include, [if_empty])", "d": "Filtra un interval o una matriu", "ad": "l'interval o la matriu que s'ha de filtrar!una matriu de valors booleans, a la qual \"TRUE\" representa una fila o una columna que cal conservar!es retorna si no es conserva cap element"}, "ARRAYTOTEXT": {"a": "(matriu, [format])", "d": "Retorna una representació de text d'una matriu", "ad": " la matriu representa com a text! el format del text"}, "SORT": {"a": "(matriu, [índex_ordenació], [criteri_ordenació], [per_columna])", "d": "Ordena un interval o una matriu", "ad": "l'interval o la matriu que s'ordenarà!un nombre que indica la fila o la columna per la qual s'ordenarà!un nombre que indica el criteri d'ordenació desitjat: 1 per a l'ordre ascendent (opció per defecte), -1 per a l'ordre descendent!un valor lògic que indica la direcció d'ordenació desitjada: FALSE per ordenar per files (opció per defecte) i TRUE per ordenar per columnes"}, "SORTBY": {"a": "(matriu, per_matriu, [criteri_ordenaci<PERSON>], ...)", "d": "Ordena un interval o una matriu segons els valors d'un interval o d'una matriu corresponent", "ad": "l'interval o la matriu que s'ha d'ordenar!l'interval o la matriu que defineix el criteri d'ordenació!un nombre que indica el criteri d'ordenació: 1 per a l'ordre ascendent (opció per defecte) i -1 per a l'ordre descendent"}, "GETPIVOTDATA": {"a": "(camp_dades; taula_dinàmica; [camp]; [element]; ...)", "d": "Extreu les dades emmagatzemades en una taula dinàmica.", "ad": "és el nom del camp de dades del qual s'han d'extreure les dades!és una referència a una cel·la o un interval de cel·les de la taula dinàmica que conté les dades que voleu recuperar!camp al qual es fa referència!element de camp al qual es fa referència"}, "IMPORTRANGE": {"a": "(spreadsheet_url, cadena_interval)", "d": "Importa un interval de cel·les d'un full de càlcul especificat."}}