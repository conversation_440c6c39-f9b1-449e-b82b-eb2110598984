{"DATE": {"a": "(gads; mēnesis; diena)", "d": "<PERSON><PERSON><PERSON><PERSON>, kas ap<PERSON><PERSON><PERSON><PERSON> datumu datuma/laika kodā", "ad": "ir skaitlis diapazonā no 1900 vai 1904 (atkarībā no darbgrāmatas datumu sistēmas) līdz 9999!ir skaitlis no 1 līdz 12, kas apzī<PERSON>ē gada mēnesi!ir skaitlis no 1 līdz 31, kas apz<PERSON><PERSON><PERSON> mē<PERSON><PERSON> dienu"}, "DATEDIF": {"a": "(sākuma_datums; beigu_datums; mēr<PERSON><PERSON><PERSON><PERSON>)", "d": "Aprēķina dienu, mēne<PERSON>u vai gadu skaitu starp diviem datumiem", "ad": "Datums, kas apzīmē dotā perioda pirmo jeb sākuma datumu!Datums, kas apzīmē perioda pēdējo jeb beigu datumu!Informācijas veids, kas ir j<PERSON><PERSON><PERSON><PERSON>ž"}, "DATEVALUE": {"a": "(datums_teksts)", "d": "Pārveido datumu teksta formātā par skaitli, kas apzīmē datumu datuma/laika kodā", "ad": "ir <PERSON><PERSON><PERSON>, kas apz<PERSON><PERSON>ē datumu programmas Spreadsheet Editor datumu formātā - no 01.01.1900. (atkarībā no darbgrāmatas datumu sistēmas) līdz 31.12.9999."}, "DAY": {"a": "(seri<PERSON><PERSON><PERSON>_skaitlis)", "d": "<PERSON><PERSON><PERSON><PERSON> m<PERSON><PERSON><PERSON> dienu - skaitli no 1 līdz 31.", "ad": "ir skai<PERSON><PERSON> da<PERSON>/laika kod<PERSON>, ko lieto programma Spreadsheet Editor"}, "DAYS": {"a": "(beigu_datums; sākuma_datums)", "d": "Atgriež dienu skaitu starp diviem datumiem.", "ad": "sākuma_datums un beigu_datums ir divi datumi, starp kuriem jāuzzina dienu skaits!sākuma_datums un beigu_datums ir divi datumi, starp kuriem jāuzzina dienu skaits"}, "DAYS360": {"a": "(sākuma_datums; beigu_datums; [metode])", "d": "<PERSON><PERSON><PERSON><PERSON> dienu skaitu starp diviem da<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ka gadā ir 360 dienas (divpadsmit 30 dienu mēneši)", "ad": "sākuma_datums un beigu_datums ir divi datumi, starp kuriem esošais dienu skaits ir jāuzzina!sākuma_datums un beigu_datums ir divi datumi, starp kuriem esošais dienu skaits ir jāuzzina!ir loģiskā vērtība, kas norāda aprēķina metodi: ASV (NASD) = FALSE vai izlaists; Eiropas = TRUE."}, "EDATE": {"a": "(sākuma_datums; mēne<PERSON>i)", "d": "Atgriež datuma sērijas numuru, kas ir nor<PERSON><PERSON><PERSON><PERSON>s mēnešu skaits pirms vai pēc sākuma datuma", "ad": "ir datuma sērijas numurs, kas ataino sākuma datumu!ir mēne<PERSON>u skaits pirms vai pēc sākuma_datuma"}, "EOMONTH": {"a": "(sākuma_datums; mēne<PERSON>i)", "d": "<PERSON><PERSON><PERSON><PERSON> tās mēne<PERSON> pēdējās dienas sērijas numuru, kas ir pirms vai pēc noteikta mēnešu skaita", "ad": "ir datuma sērijas numurs, kas ataino sākuma datumu!ir mēne<PERSON>u skaits pirms vai pēc sākuma_datuma"}, "HOUR": {"a": "(seri<PERSON><PERSON><PERSON>_skaitlis)", "d": "Atgriež stundu kā skaitli no 0 (12:00 A.M.) līdz 23 (11:00 P.M.).", "ad": "ir s<PERSON><PERSON><PERSON>/laika kodā, ko lieto programma Spreadsheet Editor, vai teksts laika formātā, piemēram, 16:48:00"}, "ISOWEEKNUM": {"a": "(datums)", "d": "Atgriež gada ISO nedēļas numuru noteiktam datumam", "ad": "ir datuma/laika kods, ko Spreadsheet Editor i<PERSON><PERSON> datuma un laika aprēķināšanai"}, "MINUTE": {"a": "(seri<PERSON><PERSON><PERSON>_skaitlis)", "d": "Atgriež minūti kā skaitli no 0 līdz 59.", "ad": "ir s<PERSON><PERSON><PERSON>/laika kodā, ko lieto programma Spreadsheet Editor, vai teksts laika formātā, piemēram, 16:48:00"}, "MONTH": {"a": "(seri<PERSON><PERSON><PERSON>_skaitlis)", "d": "Atgriež mēnesi - skaitli no 1 (jan<PERSON><PERSON><PERSON>) līdz 12 (de<PERSON><PERSON><PERSON>).", "ad": "ir skai<PERSON><PERSON> da<PERSON>/laika kod<PERSON>, ko lieto programma Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(sākuma_datums; beigu_datums; [brīvdienas])", "d": "Atgriež pilnu darbdienu skaitu starp diviem datumiem", "ad": "ir datuma sērijas numurs, kas ataino sākuma datumu!ir datuma sērijas numurs, kas ataino beigu datumu!ir neobligāta viena vai vairāku datuma sēriju numuru kopa, lai ne<PERSON>ļautu darba kalend<PERSON>r<PERSON>, pie<PERSON><PERSON><PERSON>, valsts/reģiona un federālās svētku dienas un svētku dienas ar mainīgiem datumiem"}, "NETWORKDAYS.INTL": {"a": "(sākuma_datums; beigu_datums; [nedē<PERSON><PERSON>_nogale]; [brīv<PERSON><PERSON>])", "d": "Atgriež pilnu darbdienu skaitu starp diviem datumiem ar pielāgotu nedēļas nogales parametru", "ad": "ir datuma sērijas numurs, kas ataino sākuma datumu!ir datuma sērijas numurs, kas ataino beigu datumu!ir skaitl<PERSON> vai virk<PERSON>, kas nor<PERSON>, kad nedēļas nogales iestājas!ir neobligāta viena vai vairāku datuma sēriju numuru kopa, lai darba kalendārā ne<PERSON>, piem<PERSON><PERSON>, valsts/reģiona svētku dienas un svētku dienas ar mainīgiem datumiem"}, "NOW": {"a": "()", "d": "Atgriež šīsdienas datumu un laiku, kas formatēts kā datums un laiks.", "ad": ""}, "SECOND": {"a": "(seri<PERSON><PERSON><PERSON>_skaitlis)", "d": "Atgriež sekundi kā skaitli no 0 līdz 59.", "ad": "ir s<PERSON><PERSON><PERSON>/laika kodā, ko lieto programma Spreadsheet Editor, vai teksts laika formātā, piemēram, 16:48:23"}, "TIME": {"a": "(stunda; minūte; sekunde)", "d": "<PERSON><PERSON><PERSON><PERSON>, min<PERSON><PERSON> un sekundes, kas norā<PERSON><PERSON><PERSON> kā skaitļi, par se<PERSON><PERSON><PERSON> skaitli, kas formatēts laika formātā", "ad": "ir skaitl<PERSON> no 0 līdz 23, kas ap<PERSON><PERSON><PERSON><PERSON> stundu!ir skaitlis no 0 līdz 59, kas apz<PERSON><PERSON><PERSON> minūti!ir skaitlis no 0 līdz 59, kas apz<PERSON><PERSON><PERSON> sekundi"}, "TIMEVALUE": {"a": "(laiks)", "d": "<PERSON><PERSON><PERSON><PERSON> teksta laiku seriāl<PERSON> skaitl<PERSON>, kas ap<PERSON><PERSON><PERSON><PERSON> laiku, - skai<PERSON><PERSON> no 0 (00:00:00) līdz 0.999988426 (23:59:59). Formatējiet skaitli laika formātā pēc formulas ievadīšanas", "ad": "ir teksta v<PERSON>, kas piedāvā laiku jebkurā no Spreadsheet Editor laika form<PERSON> (datuma informācija tekstā tiek ignorēta)"}, "TODAY": {"a": "()", "d": "Atgriež pa<PERSON><PERSON><PERSON><PERSON><PERSON> datumu, kas ir formatēts kā datums.", "ad": ""}, "WEEKDAY": {"a": "(se<PERSON><PERSON><PERSON><PERSON>_skaitlis; [atgrie<PERSON><PERSON>_tips])", "d": "Atgriež skaitli no 1 līdz 7, kas a<PERSON><PERSON><PERSON><PERSON><PERSON> ned<PERSON><PERSON><PERSON> die<PERSON>.", "ad": "ir skaitl<PERSON>, kas ap<PERSON><PERSON><PERSON><PERSON> datumu!ir skaitlis: ja svētdiena=1 un sestdiena=7, izmantojiet 1; ja pirmdiena=1 un svētdiena=7, izmantojiet 2; ja pirmdiena=0 un svētdiena=6, izmantojiet 3"}, "WEEKNUM": {"a": "(s<PERSON><PERSON><PERSON>_numurs; [atg<PERSON><PERSON><PERSON>_tips])", "d": "Atgriež gada nedēļas numuru", "ad": "ir datuma laika kods, ko lieto Spreadsheet Editor datuma un laika aprēķinam!ir <PERSON><PERSON><PERSON><PERSON> (1 vai 2), kas no<PERSON><PERSON>grie<PERSON> vērtības tipu"}, "WORKDAY": {"a": "(sākuma_datums; dienas; [brīvdienas])", "d": "Atgriež tā datuma sērijas numuru, kas ir pirms vai pēc noteiktā darbdienas numura", "ad": "ir datuma sērijas numurs, kas ataino sākuma datumu!ir to dienu skaits, kas nav brīvdienas un svētku dienas, pirms vai pēc sākuma_datuma!ir viena vai vairāku datumu sērijas numuru neoblig<PERSON><PERSON> k<PERSON>, lai ne<PERSON> darba kalendār<PERSON>, pie<PERSON><PERSON><PERSON>, valsts/reģiona un federālās svētku dienas un svētku dienas ar mainīgiem datumiem"}, "WORKDAY.INTL": {"a": "(sāku<PERSON>_datums; dienas; [nedē<PERSON><PERSON>_nogale]; [brīvdienas])", "d": "Atgriež tā datuma sērijas numuru, kas ir pirms vai pēc noteiktā darbdienas numura ar pielāgotiem nedēļas nogales parametriem", "ad": "ir datuma sērijas numurs, kas ataino sākuma datumu!ir to dienu skaits, kas nav ne nedēļas nogal<PERSON>, ne svētku dienas, pirms vai pēc sākuma_datuma!ir skaitl<PERSON> vai virk<PERSON>, kas nor<PERSON><PERSON>, kad ir nedēļas nogales!ir vienas vai vairāku datumu sērijas numuru neobligāts masīvs, lai darba kalendārā neiekļautu, piem<PERSON><PERSON>, valsts/reģiona svētku dienas un svētku dienas ar mainīgiem datumiem"}, "YEAR": {"a": "(seri<PERSON><PERSON><PERSON>_skaitlis)", "d": "Atgriež gadu - veselu skaitli robežās no 1900 līdz 9999.", "ad": "ir skai<PERSON><PERSON> da<PERSON>/laika kod<PERSON>, ko lieto programma Spreadsheet Editor"}, "YEARFRAC": {"a": "(sākuma_datums; beigu_datums; [pamats])", "d": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>, kas ataino veselu dienu skaitu laika posmā starp sākuma_datumu un beigu_datumu", "ad": "ir datuma sērijas numurs, kas ataino sākuma datumu!ir datuma sērijas numurs, kas ataino beigu datumu!ir lietoja<PERSON>is dienu s<PERSON> pamats"}, "BESSELI": {"a": "(x; n)", "d": "Atgriež modificēto Beseļa funkciju In(x)", "ad": "ir vērt<PERSON><PERSON>, kurā jāvērtē funkcija!ir <PERSON><PERSON>ļa funkcijas secība"}, "BESSELJ": {"a": "(x; n)", "d": "Atgriež Beseļa funkciju Jn(x)", "ad": "ir vērt<PERSON><PERSON>, kurā jāvērtē funkcija!ir <PERSON><PERSON>ļa funkcijas secība"}, "BESSELK": {"a": "(x; n)", "d": "Atgriež modificēto Beseļa funkciju Kn(x)", "ad": "ir vērtība, kurā jāvērtē funkcija!ir funkcijas secība"}, "BESSELY": {"a": "(x; n)", "d": "Atgriež Beseļa funkciju Yn(x)", "ad": "ir vērtība, kurā jāvērtē funkcija!ir funkcijas secība"}, "BIN2DEC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tl<PERSON> deci<PERSON>", "ad": "ir <PERSON><PERSON><PERSON>, kuru vē<PERSON><PERSON> pārvē<PERSON>"}, "BIN2HEX": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "ad": "ir bin<PERSON><PERSON>, kuru vēlaties pārvērst!ir lieto<PERSON><PERSON> r<PERSON> skaits"}, "BIN2OCT": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tl<PERSON> okt<PERSON>", "ad": "ir bin<PERSON><PERSON>, kuru vēlaties pārvērst!ir lieto<PERSON><PERSON> r<PERSON> skaits"}, "BITAND": {"a": "(skaitlis1; skaitlis2)", "d": "Bitu veidā atgriež divu skaitļu vērtību \"Un\"", "ad": "ir tā binārā skaitļa decimāls atveidojums, kas jānovērtē!ir tā binārā skaitļa decimāls atveidojums, kas jānovērtē"}, "BITLSHIFT": {"a": "(skaitl<PERSON>; pārvie<PERSON>š_apjoms)", "d": "<PERSON><PERSON><PERSON><PERSON>, kas pār<PERSON><PERSON><PERSON> pa kreisi pa pārvie<PERSON>šanas_apjoma bitiem", "ad": "ir tā binārā skaitļa decim<PERSON> atveidojums, kas jānovērtē!ir bitu skaits, ar kādu skaitlis jāpārvie<PERSON> pa kreisi"}, "BITOR": {"a": "(skaitlis1; skaitlis2)", "d": "Bitu veidā atgriež divu skaitļu vērtību \"Vai\"", "ad": "ir tā binārā skaitļa decimāls atveidojums, kas jānovērtē!ir tā binārā skaitļa decimāls atveidojums, kas jānovērtē"}, "BITRSHIFT": {"a": "(skaitl<PERSON>; pārvie<PERSON>š_apjoms)", "d": "<PERSON><PERSON><PERSON><PERSON>, kas pār<PERSON><PERSON><PERSON> pa labi pa pār<PERSON><PERSON><PERSON><PERSON>_daud<PERSON>ma bitiem", "ad": "ir tā binārā skaitļa decim<PERSON> atveidojums, kas jānovērtē!ir bitu skaits, ar kādu skaitlis jāpārvie<PERSON> pa labi"}, "BITXOR": {"a": "(skaitlis1; skaitlis2)", "d": "Bitu veidā atgriež divu skaitļu vērtību \"Izņemot/Vai\"", "ad": "ir tā binārā skaitļa decimāls atveidojums, kas jānovērtē!ir tā binārā skaitļa decimāls atveidojums, kas jānovērtē"}, "COMPLEX": {"a": "(re<PERSON><PERSON>_skaitlis; i_skaitlis; [sufikss])", "d": "Kon<PERSON><PERSON> reālus un iedomātus koeficientus saliktā skaitlī", "ad": "ir salikta skaitļa reāls koeficients!ir salikta skaitļa iedomāts koeficients!ir salikta skaitļa iedomātā komponenta sufikss"}, "CONVERT": {"a": "(skai<PERSON><PERSON>; no_vien<PERSON><PERSON>; līdz_vienībai)", "d": "Konvert<PERSON> skaitli no vienas mērvienību sistēmas citā", "ad": "ir v<PERSON><PERSON><PERSON><PERSON>, kas jākonvertē no_vienības!ir skaitļa vienības!ir rezultāta vienības"}, "DEC2BIN": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ad": "ir vesels de<PERSON>, kuru vēlaties pārvērst!ir lieto<PERSON><PERSON> r<PERSON> skaits"}, "DEC2HEX": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>", "ad": "ir vesels de<PERSON>, kuru vēlaties pārvērst!ir lieto<PERSON><PERSON> r<PERSON> skaits"}, "DEC2OCT": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oktāl<PERSON>", "ad": "ir vesels de<PERSON>, kuru vēlaties pārvērst!ir lieto<PERSON><PERSON> r<PERSON> skaits"}, "DELTA": {"a": "(skaitlis1; [skaitlis2])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai divi skaitļi ir vienādi", "ad": "ir pirmais skaitlis!ir otrais skaitlis"}, "ERF": {"a": "(apakšējā_robeža; [augšējā_robeža])", "d": "Atgriež kļūdas <PERSON>", "ad": "ir apakšējā robeža ERF integrēšanai!ir augšējā robeža ERF integrēšanai"}, "ERF.PRECISE": {"a": "(X)", "d": "Atgriež kļūdas <PERSON>", "ad": "ir ERF.PRECISE integrēšanas apakšējā <PERSON>a"}, "ERFC": {"a": "(x)", "d": "Atgriež papildu kļūdas funkciju", "ad": "ir apa<PERSON><PERSON><PERSON><PERSON><PERSON>ža ERF integrēšanai"}, "ERFC.PRECISE": {"a": "(x)", "d": "Atgriež papildu kļūdas funkciju", "ad": "ir ERFC.PRECISE integrēšanas apakšējā <PERSON>a"}, "GESTEP": {"a": "(skaitlis; [solis])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai skaitlis ir lielāks par sliekšņa vērtību", "ad": "ir vē<PERSON><PERSON><PERSON>, kura jāp<PERSON>rbauda pret soli!ir sliekšņa vērtība"}, "HEX2BIN": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "ad": "ir he<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuru vēlaties pārvērst!ir lieto<PERSON><PERSON> r<PERSON> skaits"}, "HEX2DEC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skaitli decimā<PERSON>", "ad": "ir he<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuru vēlat<PERSON> pārvērst"}, "HEX2OCT": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tli oktāl<PERSON>", "ad": "ir he<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuru vēlaties pārvērst!ir lieto<PERSON><PERSON> r<PERSON> skaits"}, "IMABS": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa absolūto v<PERSON> (moduli)", "ad": "ir sa<PERSON><PERSON>, kuram i<PERSON><PERSON>ma absolūtā vērtība"}, "IMAGINARY": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa iedomā<PERSON> k<PERSON>", "ad": "ir sa<PERSON><PERSON>, kam iegū<PERSON><PERSON> iedomātais koe<PERSON>s"}, "IMARGUMENT": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež argumentu q, leņķi, kas izteikts radiānos", "ad": "ir sa<PERSON><PERSON>, kam <PERSON><PERSON> arguments"}, "IMCONJUGATE": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa saliktu konjugātu", "ad": "ir sa<PERSON><PERSON> s<PERSON>, kam ieg<PERSON>sta<PERSON> konjugāts"}, "IMCOS": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa kos<PERSON>", "ad": "ir sa<PERSON><PERSON>, kuram j<PERSON><PERSON> kos<PERSON>s"}, "IMCOSH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa hiperbolisko kosinusu", "ad": "ir sa<PERSON><PERSON>, par kuru jāiegūst hiperboliskais kosinuss"}, "IMCOT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež saliktā skaitļa kotangensu", "ad": "ir sa<PERSON><PERSON><PERSON>, kam aprēķināms kotange<PERSON>s"}, "IMCSC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež saliktā skaitļa kosekansu", "ad": "ir sa<PERSON><PERSON><PERSON>, kam aprēķināms kosekanss"}, "IMCSCH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež saliktā skaitļa hiperbolisko kosekansu", "ad": "ir sa<PERSON><PERSON><PERSON>, kam aprēķināms hiperboliskais kosekanss"}, "IMDIV": {"a": "(iskaitlis1; iskaitlis2)", "d": "Atgriež divu saliktu skaitļu dalījumu", "ad": "ir salikts skaitītājs vai dalāmais!ir salikts saucējs vai dalītājs"}, "IMEXP": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa eksponentu", "ad": "ir sa<PERSON><PERSON>, kura<PERSON> j<PERSON> eks<PERSON>"}, "IMLN": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa naturālo logaritmu", "ad": "ir sa<PERSON><PERSON>, kam iegūstams naturālais logaritms"}, "IMLOG10": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa bāzes 10 logaritmu", "ad": "ir sa<PERSON><PERSON> s<PERSON>, kam iegūsta<PERSON> vienkār<PERSON> logaritms"}, "IMLOG2": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa bāzes 2 logaritmu", "ad": "ir sa<PERSON><PERSON>, kam <PERSON><PERSON><PERSON> bāzes 2 logaritms"}, "IMPOWER": {"a": "(iskaitl<PERSON>; skaitlis)", "d": "<PERSON>g<PERSON><PERSON>, kas k<PERSON><PERSON> veselā pakāpē", "ad": "ir salikts skaitl<PERSON>, kuru vēlaties kāpināt pakāpē!ir pakāpe, kurā vēlaties kāpināt saliktu skaitli"}, "IMPRODUCT": {"a": "(iskaitlis1; [iskaitlis2]; ...)", "d": "Atgriež produktam saliktus skaitļus no 1 līdz 255", "ad": "Is<PERSON>tl<PERSON>1, <PERSON><PERSON><PERSON><PERSON>2,... re<PERSON><PERSON><PERSON><PERSON><PERSON> ir saliktie skaitļi no 1 līdz 255."}, "IMREAL": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skai<PERSON>a <PERSON>", "ad": "ir sa<PERSON><PERSON>, kam i<PERSON><PERSON><PERSON><PERSON> re<PERSON><PERSON> k<PERSON>s"}, "IMSEC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež saliktā skaitļa se<PERSON>", "ad": "ir sa<PERSON><PERSON><PERSON>, kam aprēķināma sekante"}, "IMSECH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež saliktā skaitļa hiperbolisko sekanti", "ad": "ir sa<PERSON><PERSON><PERSON>, kam aprēķināma hiperbolisk<PERSON> sekante"}, "IMSIN": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa sinusu", "ad": "ir sa<PERSON><PERSON>, kam i<PERSON><PERSON> sinuss"}, "IMSINH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa hiperbolisko sinusu", "ad": "ir sa<PERSON><PERSON>, par kuru jāiegūst hiperboliskais sinuss"}, "IMSQRT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa kvadrātsakni", "ad": "ir sa<PERSON><PERSON>, kuram i<PERSON><PERSON> k<PERSON>"}, "IMSUB": {"a": "(iskaitlis1; iskaitlis2)", "d": "Atgriež divu saliktu skaitļu starpību", "ad": "ir salikts skaitlis, no kura jāatņem iskaitlis2!ir salikts skaitlis, kurš jāatņem no iskaitļa1"}, "IMSUM": {"a": "(iskaitlis1; [iskaitlis2]; ...)", "d": "Atgriež salikta skaitļa summu", "ad": "saskaitīšanai ir saliktie skaitļi no 1 līdz 255"}, "IMTAN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež saliktā skaitļa tangensu", "ad": "ir sa<PERSON><PERSON><PERSON>, kam aprēķināms tan<PERSON>s"}, "OCT2BIN": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tl<PERSON>", "ad": "ir okt<PERSON><PERSON>, kuru vēlaties pārvērst!ir lieto<PERSON>mo r<PERSON>tz<PERSON> skaits"}, "OCT2DEC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skaitli decimāl<PERSON>", "ad": "ir <PERSON><PERSON><PERSON><PERSON>, kuru vēlat<PERSON> pārvērst"}, "OCT2HEX": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tl<PERSON> he<PERSON>āl<PERSON>", "ad": "ir okt<PERSON><PERSON>, kuru vēlaties pārvērst!ir lieto<PERSON>mo r<PERSON>tz<PERSON> skaits"}, "DAVERAGE": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Aprēķina vidējo vērtību no vērtībām norādītajiem nosacījumiem atbilstošas datu bāzes ierakstu laukā (kolonnā)", "ad": "ir to <PERSON><PERSON><PERSON> diapazons, kas veido sarakstu jeb datu bāzi. Datu bāze ir savstarpēji saistītu datu saraksts!ir kolonnas etiķete pēdiņās vai skaitl<PERSON>, kas apz<PERSON><PERSON>ē kolonnas pozīciju sarakstā!ir š<PERSON>nu diapazons, kur<PERSON> atrodas norādītie nosacījumi. Diapazonā ietilpst kolonnas etiķete un viena šūna zem etiķetes, kas paredzēta nosacījumam"}, "DCOUNT": {"a": "(datu_bāze; lauks; kritēriji)", "d": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> i<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nosa<PERSON>ījumiem atbil<PERSON> datu bāzes ierakstu la<PERSON> (kolonnā)", "ad": "ir to <PERSON><PERSON><PERSON> diapazons, kas veido sarakstu jeb datu bāzi. Datu bāze ir savstarpēji saistītu datu saraksts!ir kolonnas etiķete pēdiņās vai skaitl<PERSON>, kas apz<PERSON><PERSON>ē kolonnas pozīciju sarakstā!ir š<PERSON>nu diapazons, kur<PERSON> atrodas norādītie nosacījumi. Diapazonā ietilpst kolonnas etiķete un viena šūna zem etiķetes, kas paredzēta nosacījumam"}, "DCOUNTA": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Saskaita netukšās <PERSON> iera<PERSON>tu lauk<PERSON> (kolonn<PERSON>) datu bāzē, kas atbilst norādītajiem kritērijiem", "ad": "ir <PERSON><PERSON>nu diapazons, kas veido sarakstu jeb datu bāzi. Datu bāze ir savstarpēji saistītu datu saraksts!ir kolonnas etiķete dubultpēdiņā<PERSON> vai skaitl<PERSON>, kas apz<PERSON><PERSON>ē kolonnas pozīciju sarakstā!ir <PERSON><PERSON>nu diapazons, kur<PERSON> atrodas norādītie nosacījumi. Diapazonā ietilpst kolonnas etiķete un viena šūna zem etiķetes nosacījuma ievadīšanai"}, "DGET": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Izgūst no datu bāzes vienu iera<PERSON>tu, kas atbilst norādī<PERSON>jiem nosa<PERSON>ījumiem", "ad": "ir š<PERSON>nu diapazons, kas veido sarakstu jeb datu bāzi. Datu bāze ir savstarpēji saistītu datu saraksts!ir kolonnas etiķete pēdiņās vai skaitl<PERSON>, kas apzī<PERSON>ē kolonnas pozīciju sarakstā!ir š<PERSON>nu diapazons, kur<PERSON> atrodas norādītie nosacījumi. Diapazonā ietilpst kolonnas etiķete un viena šūna zem etiķetes, kas paredzēta nosacījumam"}, "DMAX": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Atgriež vislielāko skaitli norādītajiem nosacījumiem atbilstošas datu bāzes ierakstu la<PERSON> (kolonnā)", "ad": "ir to <PERSON><PERSON><PERSON> diapazons, kas veido sarakstu jeb datu bāzi. Datu bāze ir savstarpēji saistītu datu saraksts!ir kolonnas etiķete pēdiņās vai skaitl<PERSON>, kas apz<PERSON><PERSON>ē kolonnas pozīciju sarakstā!ir š<PERSON>nu diapazons, kur<PERSON> atrodas norādītie nosacījumi. Diapazonā ietilpst kolonnas etiķete un viena šūna zem etiķetes, kas paredzēta nosacījumam"}, "DMIN": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Atgriež vismazāko skaitli norādītajiem nosacījumiem atbilstošas datu bāzes ierakstu lauk<PERSON> (kolonnā)", "ad": "ir to <PERSON><PERSON><PERSON> diapazons, kas veido sarakstu jeb datu bāzi. Datu bāze ir savstarpēji saistītu datu saraksts!ir kolonnas etiķete pēdiņās vai skaitl<PERSON>, kas apz<PERSON><PERSON>ē kolonnas pozīciju sarakstā!ir š<PERSON>nu diapazons, kur<PERSON> atrodas norādītie nosacījumi. Diapazonā ietilpst kolonnas etiķete un viena šūna zem etiķetes, kas paredzēta nosacījumam"}, "DPRODUCT": {"a": "(datu_bāze; lauks; kritēriji)", "d": "<PERSON><PERSON><PERSON>, kura<PERSON> atrodas datu bāzes iera<PERSON> (kolonnā), kas atbilst nor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kritērijiem", "ad": "ir <PERSON><PERSON>nu diapazons, kas veido sarakstu jeb datu bāzi. Datu bāze ir savstarpēji saistītu datu saraksts!ir kolonnas etiķete dubultpēdiņā<PERSON> vai skaitl<PERSON>, kas apz<PERSON><PERSON>ē kolonnas pozīciju sarakstā!ir <PERSON><PERSON>nu diapazons, kur<PERSON> atrodas norādītie nosacījumi. Diapazonā ietilpst kolonnas etiķete un viena šūna zem etiķetes nosacījuma ievadīšanai"}, "DSTDEV": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Aprēķina <PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> atlas<PERSON>to datu bāzes ievadņu i<PERSON>i", "ad": "ir to <PERSON><PERSON><PERSON> diapazons, kas veido sarakstu jeb datu bāzi. Datu bāze ir savstarpēji saistītu datu saraksts!ir kolonnas etiķete pēdiņās vai skaitl<PERSON>, kas apz<PERSON><PERSON>ē kolonnas pozīciju sarakstā!ir š<PERSON>nu diapazons, kur<PERSON> atrodas norādītie nosacījumi. Diapazonā ietilpst kolonnas etiķete un viena šūna zem etiķetes, kas paredzēta nosacījumam"}, "DSTDEVP": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Aprēķina stand<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> atlas<PERSON>tu datu bāzes ievadņu visu populāciju", "ad": "ir <PERSON><PERSON>nu diapazons, kas veido sarakstu jeb datu bāzi. Datu bāze ir savstarpēji saistītu datu saraksts!ir kolonnas etiķete dubultpēdiņā<PERSON> vai skaitl<PERSON>, kas apz<PERSON><PERSON>ē kolonnas pozīciju sarakstā!ir <PERSON><PERSON>nu diapazons, kur<PERSON> atrodas norādītie nosacījumi. Diapazonā ietilpst kolonnas etiķete un viena šūna zem etiķetes nosacījuma ievadīšanai"}, "DSUM": {"a": "(datu_bāze; lauks; kritēriji)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> nor<PERSON>jiem nosacījumiem atbilstoša<PERSON> datu bāzes ierakstu laukā (kolonnā)", "ad": "ir to <PERSON><PERSON><PERSON> diapazons, kas veido sarakstu jeb datu bāzi. Datu bāze ir savstarpēji saistītu datu saraksts!ir kolonnas etiķete pēdiņās vai skaitl<PERSON>, kas apz<PERSON><PERSON>ē kolonnas pozīciju sarakstā!ir š<PERSON>nu diapazons, kur<PERSON> atrodas norādītie nosacījumi. Diapazonā ietilpst kolonnas etiķete un viena šūna zem etiķetes, kas paredzēta nosacījumam"}, "DVAR": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Aprēķina dispersiju, i<PERSON><PERSON><PERSON><PERSON> atlas<PERSON>to datu bāzes ievadņu i<PERSON>i", "ad": "ir to <PERSON><PERSON><PERSON> diapazons, kas veido sarakstu jeb datu bāzi. Datu bāze ir savstarpēji saistītu datu saraksts!ir kolonnas etiķete pēdiņās vai skaitl<PERSON>, kas apz<PERSON><PERSON>ē kolonnas pozīciju sarakstā!ir š<PERSON>nu diapazons, kur<PERSON> atrodas norādītie nosacījumi. Diapazonā ietilpst kolonnas etiķete un viena šūna zem etiķetes, kas paredzēta nosacījumam"}, "DVARP": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Aprēķina dispersiju, i<PERSON><PERSON><PERSON><PERSON> atlas<PERSON>tu datu bāzes ievadņu visu populāciju", "ad": "ir <PERSON><PERSON>nu diapazons, kas veido sarakstu jeb datu bāzi. Datu bāze ir savstarpēji saistītu datu saraksts!ir kolonnas etiķete dubultpēdiņā<PERSON> vai skaitl<PERSON>, kas apz<PERSON><PERSON>ē kolonnas pozīciju sarakstā!ir <PERSON><PERSON>nu diapazons, kur<PERSON> atrodas norādītie nosacījumi. Diapazonā ietilpst kolonnas etiķete un viena šūna zem etiķetes nosacījuma ievadīšanai"}, "CHAR": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuru nor<PERSON>da koda numurs datora raks<PERSON><PERSON><PERSON><PERSON> kopā", "ad": "ir skai<PERSON><PERSON> no 1 lī<PERSON><PERSON> 255, kas <PERSON><PERSON><PERSON> vaja<PERSON><PERSON><PERSON><PERSON> r<PERSON>"}, "CLEAN": {"a": "(teksts)", "d": "No teksta izņem visas nedrukājamā<PERSON> r<PERSON>", "ad": "ir j<PERSON><PERSON> da<PERSON> informā<PERSON>, no kuras jāiz<PERSON>em nedruk<PERSON> r<PERSON>"}, "CODE": {"a": "(teksts)", "d": "Atgriež teksta virknes pirmās rakstzīmes skaitlisko kodu datorā izmantotajā rakstzīmju kopā", "ad": "ir te<PERSON><PERSON>, kuram j<PERSON>a pirm<PERSON>s r<PERSON><PERSON><PERSON> kods"}, "CONCATENATE": {"a": "(teksts1; [teksts2]; ...)", "d": "Savieno vairākas teksta virknes vienā", "ad": "ir 1 līdz 255 teksta virk<PERSON>, kas sa<PERSON><PERSON><PERSON> vienā teksta virknē; tās var būt teksta virk<PERSON>, skaitļi vai vienš<PERSON>nas atsauces"}, "CONCAT": {"a": "(teksts1; ...)", "d": "Savieno teksta virkņu sarakstu vai diapazonu", "ad": "vai teksta virknes vai diapazoni no 1 līdz 254 ir jā<PERSON>vieno vienā teksta virknē"}, "DOLLAR": {"a": "(skai<PERSON><PERSON>; [decim<PERSON>lskaitļi])", "d": "Konvertē skaitli par tekstu, i<PERSON><PERSON><PERSON><PERSON> valūtas formātu", "ad": "ir skaitl<PERSON>, at<PERSON><PERSON> u<PERSON>nu, kur<PERSON> ir skaitlis vai formula, ar kuru izrēķina skaitli!ir ciparu skaits pa labi no decimālzīmes. Skaitlis tiek noapaļots pēc vajadzības; ja izlai<PERSON>, decim<PERSON><PERSON> = 2"}, "EXACT": {"a": "(teksts1; teksts2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai divas teksta virknes ir pilnīgi vienādas un atgriež TRUE vai FALSE. Funkcija EXACT ir reģistrjutīga", "ad": "ir pirmā teksta virkne!ir otrā teksta virkne"}, "FIND": {"a": "(atrast_tekstu; tekstā; [sākuma_num])", "d": "Atgriež vienas teksta virknes sākuma pozīciju citā teksta virknē. Funkcija FIND ir reģistrjutīga", "ad": "ir atrodamais teksts. <PERSON><PERSON><PERSON><PERSON><PERSON> (tukšu tekstu), lai pieskaņotu pirmo rakstzīmi tekstā Tekstā; aizstājējz<PERSON><PERSON> nav atļautas!ir teksts, kurā atrodas atrodamais teksts!nor<PERSON><PERSON> r<PERSON>, no kuras sākt mek<PERSON>. Pirmā rakstzīme tekstā Atrast_tekstu ir 1. rakstzīme. Ja izlaista, sākuma_num = 1"}, "FINDB": {"a": "(atrast_tekstu; tekstā; [sākuma_num])", "d": "Atgriež vienu teksta virkni otrā teksta virknē un atgriež pirmās teksta virknes sākuma pozīcijas numuru no otrās teksta virknes pirmās r<PERSON>, ir par<PERSON><PERSON><PERSON><PERSON> valo<PERSON>, k<PERSON><PERSON> i<PERSON> dub<PERSON> r<PERSON><PERSON>op<PERSON> (DBCS) - j<PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu", "ad": "ir atrodamais teksts. <PERSON><PERSON><PERSON><PERSON><PERSON> (tukšu tekstu), lai pieskaņotu pirmo rakstzīmi tekstā Tekstā; aizstājējz<PERSON><PERSON> nav atļautas!ir teksts, kurā atrodas atrodamais teksts!nor<PERSON><PERSON> r<PERSON>, no kuras sākt mek<PERSON>. Pirmā rakstzīme tekstā Atrast_tekstu ir 1. rakstzīme. Ja izlaista, sākuma_num = 1"}, "FIXED": {"a": "(skai<PERSON><PERSON>; [deci<PERSON><PERSON><PERSON><PERSON><PERSON>]; [bez_komatiem])", "d": "Noapaļo skaitli līdz norādītajam decimāldaļu skaitam un atgriež rezultātu kā tekstu ar komatiem vai bez tiem", "ad": "ir no<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>, kur<PERSON> jākonvertē par tekstu!ir ciparu skaits pa labi no decimālzīmes. Ja izlai<PERSON>, Decimāldaļas = 2!ir loģiskā vērtība: atgrieztajā tekstā netiek rādīti komati = TRUE; atgrieztajā tekstā tiek rādīti komati = FALSE vai izlaists"}, "LEFT": {"a": "(teksts; [skait<PERSON><PERSON>_rakstz])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> skaitu no teksta virknes sākuma", "ad": " ir te<PERSON><PERSON>, k<PERSON><PERSON> i<PERSON><PERSON>t izgūstam<PERSON>s rakstz<PERSON>!nor<PERSON><PERSON>, cik rakstzīmes funkcijai LEFT ir jā<PERSON><PERSON>st; 1, ja izlaists"}, "LEFTB": {"a": "(teksts; [skait<PERSON><PERSON>_rakstz])", "d": "Atgriež pirmo rakstzīmi vai rakstzī<PERSON> teksta virkn<PERSON>, pamatojoties uz norād<PERSON>to bait<PERSON> s<PERSON>, ir pared<PERSON><PERSON><PERSON> valod<PERSON>, k<PERSON><PERSON> i<PERSON><PERSON> dub<PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON> kop<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu", "ad": " ir te<PERSON><PERSON>, k<PERSON><PERSON> i<PERSON><PERSON>t izgūstam<PERSON>s rakstz<PERSON>!nor<PERSON><PERSON>, cik rakstzīmes funkcijai LEFTB ir jā<PERSON>gūst; 1, ja izlaists"}, "LEN": {"a": "(teksts)", "d": "Atgriež rakstzīmju skaitu teksta virknē", "ad": "ir te<PERSON><PERSON>, kura garums ir j<PERSON><PERSON><PERSON><PERSON><PERSON>. Atstarpes tiek skaitītas kā rakstzīmes"}, "LENB": {"a": "(teksts)", "d": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, kas tiek i<PERSON><PERSON><PERSON> rakstzī<PERSON><PERSON> attēloša<PERSON> teksta virknē, ir pared<PERSON><PERSON>ta valod<PERSON>, k<PERSON><PERSON> i<PERSON><PERSON> dub<PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON> kop<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu", "ad": "ir te<PERSON><PERSON>, kura garums ir j<PERSON><PERSON><PERSON><PERSON><PERSON>. Atstarpes tiek skaitītas kā rakstzīmes"}, "LOWER": {"a": "(teksts)", "d": "Visus burtus teksta virknē konvertē par mazajiem burtiem", "ad": "ir uz mazajiem burtiem konvertējamais teksts. <PERSON><PERSON><PERSON>, kas nav burti, netiek mainītas"}, "MID": {"a": "(teksts; sākuma_num; raks<PERSON><PERSON><PERSON><PERSON>_skaits)", "d": "Atgriež rakstzīmes no teksta virknes vidus, nor<PERSON><PERSON><PERSON> sākuma pozīciju un garumu", "ad": "ir teksta v<PERSON>, no kuras jāizgūst rakstzīmes!ir pirmās izgūstamās rakstzīmes pozīcija. <PERSON><PERSON>ā rakstzīme Tekstā ir 1!<PERSON><PERSON><PERSON>, cik rakstzīmes no Teksta ir jāatgriež"}, "MIDB": {"a": "(teksts; sākuma_num; raks<PERSON><PERSON><PERSON><PERSON>_skaits)", "d": "Atgriež noteiktas rakstzī<PERSON> no teksta virknes, s<PERSON><PERSON> no norād<PERSON>tās vietas un pamatojoties uz norād<PERSON>to baitu s<PERSON>, ir pared<PERSON><PERSON>ta valod<PERSON>, k<PERSON><PERSON> i<PERSON><PERSON> dub<PERSON> r<PERSON><PERSON><PERSON><PERSON>op<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu", "ad": "ir teksta v<PERSON>, no kuras jāizgūst rakstzīmes!ir pirmās izgūstamās rakstzīmes pozīcija. <PERSON><PERSON>ā rakstzīme Tekstā ir 1!<PERSON><PERSON><PERSON>, cik rakstzīmes no Teksta ir jāatgriež"}, "NUMBERVALUE": {"a": "(teksts; [decim<PERSON>lzīme]; [grupu_atdalītājs])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tekstu par skaitli no lokalizācijas neatkarīgā veidā", "ad": "ir v<PERSON><PERSON>, kas at<PERSON><PERSON><PERSON><PERSON><PERSON>, kas jā<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>!ir r<PERSON><PERSON><PERSON><PERSON>, kas virknē tiek izmantota kā decimālzīme!ir rakstz<PERSON><PERSON>, kas virknē tiek izmantota kā grupu atdalītājs"}, "PROPER": {"a": "(teksts)", "d": "Pārveido teksta virkni pareizajā burtu reģistrā; katra vārda pirmo burtu par lielo, bet pārējos - par mazajiem", "ad": "ir teksts, kas ielikts p<PERSON>, formula, kas atg<PERSON><PERSON> tekstu, vai atsauce uz <PERSON>, kur<PERSON> ir teksts, kas da<PERSON><PERSON>ji rakstāms ar liela<PERSON>em burtiem"}, "REPLACE": {"a": "(vecais_teksts; s<PERSON>ku<PERSON>_num; raks<PERSON><PERSON><PERSON><PERSON>_skaits; jaunais_teksts)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> teksta virknes ar citu teksta virkni", "ad": "ir teksts, kur<PERSON> jāaizstāj dažas rakstzī<PERSON>!ir rakstz<PERSON><PERSON> pozīcija Vecajā_tekstā, kas jāaizstāj ar Jauno_tekstu!ir aizst<PERSON><PERSON><PERSON> rakstzī<PERSON>ju skaits Vecajā_tekstā!ir teksts, kas aizstās rakstzī<PERSON> Vecajā_tekstā"}, "REPLACEB": {"a": "(vecais_teksts; s<PERSON>ku<PERSON>_num; raks<PERSON><PERSON><PERSON><PERSON>_skaits; jaunais_teksts)", "d": "Aiz<PERSON><PERSON><PERSON> teksta virknes daļu ar citu teksta virkni, pamatojoties uz norād<PERSON><PERSON> bait<PERSON> s<PERSON>, ir pared<PERSON><PERSON><PERSON> valod<PERSON>, k<PERSON><PERSON> i<PERSON><PERSON> dub<PERSON> r<PERSON><PERSON><PERSON><PERSON>op<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu", "ad": "ir teksts, kur<PERSON> jāaizstāj dažas rakstzī<PERSON>!ir rakstz<PERSON><PERSON> pozīcija Vecajā_tekstā, kas jāaizstāj ar Jauno_tekstu!ir aizst<PERSON><PERSON><PERSON> rakstzī<PERSON>ju skaits Vecajā_tekstā!ir teksts, kas aizstās rakstzī<PERSON> Vecajā_tekstā"}, "REPT": {"a": "(teksts; skaitlis_reizes)", "d": "Atkārto tekstu norād<PERSON>to re<PERSON> skaitu. Izmantojiet funkciju REPT, lai aizpild<PERSON>tu šūnu ar noteiktu skaitu teksta virkņu", "ad": "ir atk<PERSON><PERSON><PERSON><PERSON><PERSON> teksts!ir pozitī<PERSON>, ka<PERSON>, cik reižu teksts ir jāatk<PERSON>rto"}, "RIGHT": {"a": "(teksts; [skait<PERSON><PERSON>_rakstz])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> skaitu no teksta virknes beigām", "ad": "ir te<PERSON><PERSON>, k<PERSON><PERSON> i<PERSON><PERSON><PERSON> izgūstam<PERSON><PERSON> rakstz<PERSON>!<PERSON><PERSON><PERSON>, cik rakstz<PERSON><PERSON> ir j<PERSON><PERSON><PERSON><PERSON><PERSON>, 1, ja i<PERSON><PERSON><PERSON>"}, "RIGHTB": {"a": "(teksts; [skait<PERSON><PERSON>_rakstz])", "d": "Atgriež pēdējo rakstzīmi vai rakstzīmes teksta virknē, pamatojoties uz norād<PERSON>to bait<PERSON> s<PERSON>, ir pared<PERSON><PERSON>ta valod<PERSON>, k<PERSON><PERSON> i<PERSON><PERSON> dub<PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON>op<PERSON> (DBCS) - j<PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu", "ad": "ir te<PERSON><PERSON>, k<PERSON><PERSON> i<PERSON><PERSON><PERSON> izgūstam<PERSON><PERSON> rakstz<PERSON>!<PERSON><PERSON><PERSON>, cik rakstz<PERSON><PERSON> ir j<PERSON><PERSON><PERSON><PERSON><PERSON>, 1, ja i<PERSON><PERSON><PERSON>"}, "SEARCH": {"a": "(atrast_tekstu; tekstā; [sākuma_num])", "d": "<PERSON>grie<PERSON> raks<PERSON><PERSON><PERSON> numuru, pie kuras noteikta rakstzīme vai teksta virkne atrasta pirmo reizi, lasot virzienā no kreisās uz labo pusi (nav reģistrjutīgi)", "ad": "ir meklējamais teksts. Var izmantot aizstājējzīmes ? un *; izmantojiet ~? un ~*, lai atrastu rakstzīmes ? un *!ir teksts, kurā jāmeklē Atrast_tekstu!ir tās rakstzīmes numurs Tekstā, skaitot virzienā no kreisās puses, no kuras sākt meklēšanu. Ja izlaists, tiek lietots 1"}, "SEARCHB": {"a": "(atrast_tekstu; tekstā; [sākuma_num])", "d": "Atrod vienu teksta virkni otrā teksta virknē un atgriež pirmās teksta virknes sākuma atrašanās vietas numuru, skaitot no otrās teksta virknes pirmā<PERSON> r<PERSON>, ir pared<PERSON><PERSON><PERSON> valod<PERSON>, k<PERSON><PERSON> i<PERSON> dub<PERSON> r<PERSON> kop<PERSON> (DBCS) - j<PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu", "ad": "ir meklējamais teksts. Var izmantot aizstājējzīmes ? un *; izmantojiet ~? un ~*, lai atrastu rakstzīmes ? un *!ir teksts, kurā jāmeklē Atrast_tekstu!ir tās rakstzīmes numurs Tekstā, skaitot virzienā no kreisās puses, no kuras sākt meklēšanu. Ja izlaists, tiek lietots 1"}, "SUBSTITUTE": {"a": "(teksts; vecais_teksts; jaunais_teksts; [gad<PERSON><PERSON><PERSON>_numurs])", "d": "Teksta virknē a<PERSON>stāj esošu tekstu ar jaunu tekstu", "ad": "ir teksts vai atsauce uz <PERSON>nu, kur<PERSON> ir teksts, kur j<PERSON><PERSON><PERSON> rakstzīmju aizst<PERSON>!ir esošais aizstājamais teksts. Ja Vecā_teksta burtu reģistrs nesaskan ar teksta burtu reģistru, funkcija SUBSTITUTE neaizstās tekstu!ir teksts, ar kuru jāaizstāj Vecais_teksts!nor<PERSON><PERSON>, kuru <PERSON>ecā_teksta gadījumu ir jāaizstāj. Ja izlaists, tiek aizstāts katrs Vecā_teksta gadījums"}, "T": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir teksts un atgriež tekstu, ja tā ir vai dubultpēdiņ<PERSON> (tuk<PERSON><PERSON> tekstu), ja tā nav", "ad": "ir pā<PERSON><PERSON><PERSON><PERSON><PERSON> vērtība"}, "TEXT": {"a": "(vērtība; formāts_teksts)", "d": "Konvertē vērtību par tekstu noteiktā skaitļu formātā", "ad": "ir skaitlis, formula, ar kuru iegūst skaitlisku vērtību, vai atsauce uz šūnu, kurā ietilpst skaitliska vērtība!ir skaitļa formāts teksta veidā, kas norādīts dialoglodziņa <PERSON> (ne Vispārīgi) cilnes Skaitlis lodziņā Kategorija"}, "TEXTJOIN": {"a": "(nor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; ignorēt_tukšu; teksts1; ...)", "d": "Savieno teksta virkņu sarakstu vai di<PERSON>u, i<PERSON><PERSON><PERSON><PERSON>", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON> vai virkne, ko ievietot aiz katra teksta vienuma!ja vērtība ir TRUE (noklusējums), tuk<PERSON><PERSON><PERSON> tiek ignorētas!vai teksta virknes vai diapazoni no 1 līdz 252 ir jā<PERSON>vieno "}, "TRIM": {"a": "(tekstst)", "d": "<PERSON><PERSON><PERSON><PERSON> visas atstarpes no teksta virknes, iz<PERSON><PERSON><PERSON> vienplatuma atstarpes starp vārdiem", "ad": "ir te<PERSON><PERSON>, no kura ir j<PERSON><PERSON><PERSON> atstarpes"}, "UNICHAR": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež unikoda r<PERSON>, uz ko atsaucas noteiktā skaitliskā vērtība", "ad": "ir un<PERSON><PERSON>, kas <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "UNICODE": {"a": "(teksts)", "d": "<PERSON><PERSON><PERSON><PERSON> (koda punktu), kas atbilst teksta pirmajai r<PERSON>", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON>, kuras unikoda vērtība jā<PERSON>g<PERSON>st"}, "UPPER": {"a": "(teksts)", "d": "Konvertē teksta virkni uz visiem lielajiem burtiem", "ad": "ir uz lie<PERSON>em konvertēja<PERSON> te<PERSON>, atsauce vai teksta virkne"}, "VALUE": {"a": "(teksts)", "d": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>, ka<PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON> skaitl<PERSON>, par skaitli", "ad": "ir teksts pēdiņ<PERSON>s vai atsauce uz <PERSON>, kur<PERSON> ir konvertējama<PERSON> teksts"}, "AVEDEV": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež vidējo vērtību datu punktu absolūtajām novirzēm no vidējā. Argumenti var būt skaitļi vai nosaukumi, mas<PERSON><PERSON> vai atsauces, kur<PERSON><PERSON> i<PERSON><PERSON> skaitļi", "ad": "ir 1 līdz 255 argumentu, kam aprēķināmas absolūto novir<PERSON>u vidējā<PERSON> vērt<PERSON>bas"}, "AVERAGE": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> savu <PERSON> v<PERSON> (aritmētisko), kas var būt skait<PERSON>i vai nosaukumi, mas<PERSON><PERSON> vai atsauce<PERSON>, k<PERSON><PERSON><PERSON> ir skait<PERSON>i", "ad": "ir 1 līdz 255 skaitlisku argumentu, no kuriem iegūstams vidējais"}, "AVERAGEA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "Atgriež argumentu vid<PERSON> (vidē<PERSON> aritmētis<PERSON>), novērtējuma tekstu un FALSE argumentos kā 0; TRUE tiek novērtēts kā 1. <PERSON>rg<PERSON>nti var būt s<PERSON>, <PERSON><PERSON><PERSON><PERSON>, mas<PERSON><PERSON> vai atsauces", "ad": "ir 1 līdz 255 argumentu, kam j<PERSON><PERSON><PERSON><PERSON><PERSON> vidējais"}, "AVERAGEIF": {"a": "(diapazons; kritēriji; [vidējais_diapazons])", "d": "<PERSON><PERSON> vid<PERSON> (vid<PERSON><PERSON>) <PERSON><PERSON><PERSON><PERSON><PERSON>, ko norāda dotais nosacījums vai kritērijs", "ad": "ir novērtē<PERSON><PERSON> šūnu diapazons!ir nosacījums vai kritēriji skaitļa, izteiksmes vai teksta formā, kas definē to, kuras š<PERSON>nas tiks lietotas, lai atrastu vidējo!ir faktisk<PERSON><PERSON>, kuras lieto vidējā at<PERSON>. <PERSON>a izlai<PERSON>, tiek lietotas diapazona šūnas"}, "AVERAGEIFS": {"a": "(vidējais_diapazons; kritēriju_diapazons; kritēriji; ...)", "d": "<PERSON><PERSON> vid<PERSON> (vid<PERSON><PERSON>) <PERSON><PERSON><PERSON><PERSON><PERSON>, ko norāda dotā nosacījumu kopa vai kritēriji", "ad": "ir faktisk<PERSON><PERSON>, ko lieto vidējā atraša<PERSON>!ir š<PERSON>nu diapazons, kas jāvērtē pēc noteiktā nosacījuma!ir nosacījums vai kritērijs skaitļa veidā, iz<PERSON><PERSON><PERSON><PERSON> vai teksts, kas nosaka, kura<PERSON> tiks lietotas vidējā atrašanai"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Atgriež kumulatīvo beta varbūtības blīvuma funkciju", "ad": "ir vērtība no A līdz B, kas jāaprēķina ar funkciju!ir sadalījuma parametrs, un tam jābūt lielākam par 0!ir sadalījuma parametrs, un tam jābūt lielākam par 0!ir neobligāta x intervāla apakšējā robeža. Ja izlaista, A = 0!ir neobligāta x intervāla augšējā robeža. Ja izlaista, B = 1"}, "BETAINV": {"a": "(varb<PERSON><PERSON><PERSON>ba; alfa; beta; [A]; [B])", "d": "Atgriež apgrieztu kumulatīvo beta varbūtības blīvuma funkciju (BETADIST)", "ad": "ir var<PERSON><PERSON><PERSON><PERSON><PERSON>, kas saistīta ar beta sadalījumu!ir sadalījuma parametrs, un tam jābūt lielākam par 0!ir sadalījuma parametrs, un tam jābūt lielākam par 0!ir neobligāta x intervāla apakšējā robeža. Ja izlaista, A = 0!ir neobligāta x intervāla augšējā robeža. Ja izlaista, B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; kumulatīvā; [A]; [B])", "d": "Atgriež beta varbūtības sadalījuma funkciju", "ad": "ir vērtība no A līdz B, kas jāaprēķina ar funkciju!ir sadalījuma parametrs, un tam jābūt lielākam par 0!ir sadalījuma parametrs, un tam jābūt lielākam par 0!ir loģiskā vērtība: kumulatīvajai sadalījuma funkcijai izmantojiet TRUE; varbūtības blīvuma funkcijai izmantojiet FALSE!pēc izvēles izdod x intervāla apakšējo robežu. Ja izlaists, A = 0!pēc izvēles izdod x intervāla augšējo robežu. Ja izlaists, B = 1"}, "BETA.INV": {"a": "(varb<PERSON><PERSON><PERSON>ba; alfa; beta; [A]; [B])", "d": "Atgriež apgrieztu kumulatīvo beta varbūtības blīvuma funkciju (BETA.DIST)", "ad": "ir var<PERSON><PERSON><PERSON><PERSON><PERSON>, kas saistīta ar beta sadalījumu!ir sadalījuma parametrs, un tam jābūt lielākam par 0!ir sadalījuma parametrs, un tam jābūt lielākam par 0!pēc izvēles izdod x intervāla apakšējo robežu. Ja izlaists, A = 0!pēc izvēles izdod x intervāla augšējo robežu. Ja izlaists, B = 1"}, "BINOMDIST": {"a": "(labvēlizn_skaits; mēģinājumi; labvēlizn_varbūtība; kumulatīvā)", "d": "Atgriež atsevišķas izteiksmes binomiālā sadalījuma varbūtību", "ad": "ir labvēlīgu iznākumu skaits mēģinājumos!ir patstāvīgu mēģinājumu skaits!ir labvēlīga iznākuma varbūtība katrā mēģinājumā!ir loģiskā vērtība: kumulatīvai sadalījuma funkcijai izmantojiet TRUE; varbūtību masas funkcijai izmantojiet FALSE"}, "BINOM.DIST": {"a": "(labvēlizn_skaits; mēģinājumi; labvēlizn_varbūtība; kumulatīvā)", "d": "Atgriež binomiālā sadalījuma atsevišķas vērtības varbūtību", "ad": "ir labvēlīgu iznākumu skaits mēģinājumos!ir patstāvīgu mēģinājumu skaits!ir labvēlīga iznākuma varbūtība katrā mēģinājumā!ir loģiskā vērtība: kumulatīvajai sadalījuma funkcijai izmantojiet TRUE; varbūtību masas funkcijai izmantojiet FALSE"}, "BINOM.DIST.RANGE": {"a": "(mēģinājumi; labvēlizn_varbūtība; izdošan<PERSON>s_skaits; [izdošan<PERSON>s_skaits2])", "d": "Atgriež mēģinājuma rezultātu i<PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> binomu sadali", "ad": "ir neatkarīgu mēģinājumu skaits!ir katra mēģinājuma labvēlīga iznākuma varbūtība!ir mēģinājumu labvēlīgu iznākumu skaits!ja paredz<PERSON>ts, tad šī funkcija atgriež varbūtību, ka mēģinājumu skaits ar labvēlīgu iznākumu būs no izdošanās_skaits līdz izdošanās_skaits2"}, "BINOM.INV": {"a": "(mēģinājumi; labvēlizn_varbūtība; alfa)", "d": "Atgriež vismaz<PERSON><PERSON> v<PERSON>, k<PERSON> kumulatīvais binomiālais sadalījums ir lielāks vai vienāds ar kritērija vērtību", "ad": "ir Bernulli eksperimentu skaits!ir labvēlīga iznākuma varbūtība katrā mēģinājumā - skaitlis no 0 līdz 1 ieskaitot!ir kritērija vērtība - skaitlis no 0 līdz 1 ieskaitot"}, "CHIDIST": {"a": "(x; brīv<PERSON><PERSON>_pakāpe)", "d": "Atgriež labā zara hī kvadrātā sadalījuma varbūtību", "ad": "ir v<PERSON><PERSON><PERSON><PERSON>, kur<PERSON> jāaprēķina sadal<PERSON><PERSON><PERSON>, ne<PERSON><PERSON><PERSON><PERSON><PERSON> skaitl<PERSON>!ir brīvības pakāpju skaits - skaitlis no 1 līdz 10^10, neieskaitot 10^10"}, "CHIINV": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; brīvības_pakāpe)", "d": "Atgriež apgrieztu hī kvadrāta sadalījuma labā zara varbūtību", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas saist<PERSON>ta ar hī kvadrātā sadalījumu - vērtību no 0 līdz 1 ieskaitot!ir brīv<PERSON><PERSON> pak<PERSON><PERSON><PERSON> skai<PERSON>, s<PERSON><PERSON><PERSON> no 1 līdz 10^10, neieskaitot 10^10"}, "CHITEST": {"a": "(faktiskais_diapazons; sagaidāmais_diapazons)", "d": "Atg<PERSON>ž neatkarības pārbaudi: statiskās un piemērotās brīvības pakāpes hī kvadrātā sadalījuma vērtību", "ad": "ir datu diapazons, kurā ietilpst novē<PERSON><PERSON><PERSON>, lai veiktu pārbaudi salīdzinājumā ar sagaidāmajām vērtībām!ir datu diapazons, kurā ietilpst rindu summu un kolonnu summu reizinājuma attiecība pret visu gala summu"}, "CHISQ.DIST": {"a": "(x; brīvī<PERSON>_pakāpe; kumulatīvā)", "d": "Atgriež kreisā zara hī kvadrātā sadalījuma varbūtību", "ad": "ir vērt<PERSON><PERSON>, kurā jāaprēķina sadalījums - nenegatīvs skaitlis!ir brīvības pakāpju skaits - skaitlis no 1 līdz 10^10, neieskaitot 10^10!ir loģiskā vērt<PERSON><PERSON>, kas funkcijai jāatgriež: kumulatīvajai sadalījuma funkcijai = TRUE; varbūtības blīvuma funkcijai = FALSE"}, "CHISQ.DIST.RT": {"a": "(x; brīv<PERSON><PERSON>_pakāpe)", "d": "Atgriež labā zara hī kvadrātā sadalījuma varbūtību", "ad": "ir v<PERSON><PERSON><PERSON><PERSON>, kur<PERSON> jāaprēķina sadalījums - nenegatī<PERSON>s skaitlis!ir brīvības pakāpju skaits - skaitlis no 1 līdz 10^10, neieskaitot 10^10"}, "CHISQ.INV": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; brīvības_pakāpe)", "d": "Atgriež apgrieztu kreisā zara hī kvadrātā sadalījuma varbūtību", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas saistīta ar hī kvadrātā sadalījumu - vērtību no 0 līdz 1 ieskaitot!ir brīv<PERSON>bas pakāpju skaits - skaitlis no 1 līdz 10^10, neieskaitot 10^10"}, "CHISQ.INV.RT": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; brīvības_pakāpe)", "d": "Atgriež apgrieztu labā zara hī kvadrātā sadalījuma varbūtību", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas saistīta ar hī kvadrātā sadalījumu - vērtību no 0 līdz 1 ieskaitot!ir brīv<PERSON>bas pakāpju skaits - skaitlis no 1 līdz 10^10, neieskaitot 10^10"}, "CHISQ.TEST": {"a": "(faktiskais_diapazons; sagaidāmais_diapazons)", "d": "Atg<PERSON>ž neatkarības pārbaudi: statiskās un piemērotās brīvības pakāpes hī kvadrātā sadalījuma vērtību", "ad": "ir datu diapazons, kurā ietilpst novē<PERSON><PERSON><PERSON>, lai veiktu pārbaudi salīdzinājumā ar sagaidāmajām vērtībām!ir datu diapazons, kurā ietilpst rindu summu un kolonnu summu reizinājuma attiecība pret visu gala summu"}, "CONFIDENCE": {"a": "(alfa; standartnovirze; lielums)", "d": "Atgriež populācijas vidējā ticamības intervālu, <PERSON><PERSON><PERSON><PERSON><PERSON>", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lī<PERSON>, ko <PERSON><PERSON><PERSON> ticam<PERSON>bas līmeņa aprēķināšanai, <PERSON><PERSON><PERSON><PERSON>, kas lielāks par 0 un mazāks par 1!ir datu diapazona standartnovirzes populācija, un tiek pieņ<PERSON>, ka tā ir zināma. Standartnovirzei ir jābūt lielākai par 0!ir izlases lie<PERSON>"}, "CONFIDENCE.NORM": {"a": "(alfa; standartnovirze; lielums)", "d": "Atgriež populācijas vidējā ticamības intervālu, <PERSON><PERSON><PERSON><PERSON><PERSON>", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lī<PERSON>, ko <PERSON><PERSON><PERSON> ticam<PERSON>bas līmeņa aprēķināšanai, <PERSON><PERSON><PERSON><PERSON>, kas lielāks par 0 un mazāks par 1!ir datu diapazona standartnovirzes populācija, un tiek pieņ<PERSON>, ka tā ir zināma. Standartnovirzei ir jābūt lielākai par 0!ir izlases lie<PERSON>"}, "CONFIDENCE.T": {"a": "(alfa; standartnovirze; lielums)", "d": "Atgriež populācijas vidējā ticamības intervālu, <PERSON><PERSON><PERSON><PERSON><PERSON> t <PERSON>alījumu", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lī<PERSON>, ko <PERSON><PERSON><PERSON> ticam<PERSON>bas līmeņa aprēķināšanai, <PERSON><PERSON><PERSON><PERSON>, kas lielāks par 0 un mazāks par 1!ir datu diapazona standartnovirzes populācija, un tiek pieņ<PERSON>, ka tā ir zināma. Standartnovirzei ir jābūt lielākai par 0!ir izlases lie<PERSON>"}, "CORREL": {"a": "(masīvs1; masīvs2)", "d": "Atgriež divu datu kopu korelācijas koeficientu", "ad": "ir vērtību šūnu diapazons. Vērt<PERSON><PERSON>ām ir jābūt skaitļiem, nosaukumiem, masīviem vai atsaucēm, kurās ir skaitļi!ir otrs vērtību šūnu diapazons. Vērt<PERSON>bām ir jābūt skaitļiem, nosa<PERSON><PERSON>m, masīviem vai atsaucēm, kur<PERSON><PERSON> ir skaitļi"}, "COUNT": {"a": "(vērtība1; [vērtība2]; ...)", "d": "<PERSON><PERSON><PERSON>, cik <PERSON><PERSON><PERSON><PERSON><PERSON> ir s<PERSON>i", "ad": "ir 1 līdz 255 argumentu, k<PERSON><PERSON> var ietilpt visdažādāko tipu dati vai atsauces uz šādiem datiem, taču tiek skaitīti tikai skaitļi"}, "COUNTA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "Di<PERSON><PERSON><PERSON> sa<PERSON>, cik <PERSON><PERSON> nav tukšas", "ad": "ir 1 līdz 255 argumentu, kas apzī<PERSON>ē saskaitāmās vērtības un šūnas. Vērt<PERSON><PERSON> var būt jebkura tipa informācija"}, "COUNTBLANK": {"a": "(diapazons)", "d": "<PERSON><PERSON><PERSON>, cik tukšu šūnu ir norādītajā šūnu diapazonā", "ad": "ir diapazons, kurā jā<PERSON><PERSON><PERSON> tukšā<PERSON>"}, "COUNTIF": {"a": "(diapazons; kritēriji)", "d": "<PERSON><PERSON><PERSON>, cik <PERSON>ūnu diapazonā atbilst noteiktajam nosacījumam", "ad": "ir <PERSON><PERSON><PERSON> di<PERSON>, kura netukš<PERSON>s šūnas ir jāsaskaita!ir nosacījums skait<PERSON>a, izteiksmes vai teksta formā, kas definē, kā<PERSON> ir jāsaskaita"}, "COUNTIFS": {"a": "(kritēriju_diapazons; kritēriji; ...)", "d": "<PERSON><PERSON><PERSON>, ko norāda dotā nosacījumu kopa vai kritēriji", "ad": "ir <PERSON><PERSON><PERSON> di<PERSON>, kas jāvērtē pēc noteiktā nosacījuma!ir nosacījums skaitļa veidā, izteik<PERSON><PERSON> vai teksts, kas nosa<PERSON>, k<PERSON><PERSON> tiks skait<PERSON>tas"}, "COVAR": {"a": "(masīvs1; masīvs2)", "d": "Atgriež kovariāciju - katra divu kopu datu punktu pāra noviržu reizinājuma vidējo", "ad": "ir pirmais veselo skaitļu šūnu diapazons; tiem ir jābūt skait<PERSON>m, masīviem vai atsaucēm, kurās ir skaitļi!ir otrais veselo skaitļu šūnu diapazons; tiem ir jābūt skait<PERSON>, masīviem vai atsaucēm, kur<PERSON><PERSON> ir skaitlis"}, "COVARIANCE.P": {"a": "(masīvs1; masīvs2)", "d": "Atgriež populācijas kovariāciju - katra divu kopu datu punktu pāra noviržu reizināju<PERSON> vidējo", "ad": "ir pirmais veselo skaitļu šūnu diapazons; tiem ir jābūt skait<PERSON>m, masīviem vai atsaucēm, kurās ir skaitļi!ir otrais veselo skaitļu šūnu diapazons; tiem ir jābūt skait<PERSON>, masīviem vai atsaucēm, kur<PERSON><PERSON> ir skaitlis"}, "COVARIANCE.S": {"a": "(masīvs1; masīvs2)", "d": "Atgriež izlases kovariāciju - katra divu kopu datu punktu pāra noviržu reizinājuma vidējo", "ad": "ir pirmais veselo skaitļu šūnu diapazons; tiem ir jābūt skait<PERSON>m, masīviem vai atsaucēm, kurās ir skaitļi!ir otrais veselo skaitļu šūnu diapazons; tiem ir jābūt skait<PERSON>, masīviem vai atsaucēm, kur<PERSON><PERSON> ir skaitlis"}, "CRITBINOM": {"a": "(izmēģinājumi; varbūtība_s; alfa)", "d": "Atgriež v<PERSON><PERSON><PERSON><PERSON> v<PERSON>, k<PERSON><PERSON> kumulat<PERSON> binomu sadalījums ir lielāks vai vienāds ar kritērija vērtību", "ad": "ir Bernulli eksperimentu skaits!ir labvēlīga iznākuma varbūtība katrā izmēģinājumā - skaitlis no 0 līdz 1 ieskaitot!ir kritērija vērtība - skaitlis no 0 līdz 1 ieskaitot"}, "DEVSQ": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež datu punktu to novir<PERSON>u kvadrātu summu, kuras aprēķinātas no to izla<PERSON>u vidējā", "ad": "ir 1 līdz 255 argumentu vai masīvu vai masīvu atsauču, kurām funkcija DEVSQ varētu veikt aprēķinu"}, "EXPONDIST": {"a": "(x; lambda; kumulatīvā)", "d": "Atgriež eksponenciālo <PERSON>", "ad": "ir funkcijas vērtība, ne<PERSON><PERSON><PERSON><PERSON>s skaitlis!ir parametra vērtība, pozitīvs skaitlis!ir loģiskā vērtība, kas funkcijai ir jāatgriež: kumulatīvā sadalījuma funkcija = TRUE; varbūtības blīvuma funkcija = FALSE"}, "EXPON.DIST": {"a": "(x; lambda; kumulatīva)", "d": "Atgriež eksponenciālo <PERSON>", "ad": "ir funkcijas vērtība, ne<PERSON><PERSON><PERSON><PERSON>s skaitlis!ir parametra vērtība, pozitīvs skaitlis!ir loģiskā vērtība, kas funkcijai ir jāatgriež: kumulatīvā sadalījuma funkcija = TRUE; varbūtības blīvuma funkcija = FALSE"}, "FDIST": {"a": "(x; brīvības_pakāpe1; brīvības_pakāpe2)", "d": "Atgriež divu datu kopu (labā zara) F varbūtības sadalījumu (atšķirības pakāpi)", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> jāaprēķina funk<PERSON>, ne<PERSON><PERSON><PERSON><PERSON><PERSON> skaitlis!ir skaitīt<PERSON>ja brīvības pakā<PERSON>, skaitl<PERSON> no 1 līdz 10^10, neieskaitot 10^10!ir saucēja brīvības pakāpes, skaitl<PERSON> no 1 līdz 10^10, neieskai<PERSON>t 10^10"}, "FINV": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; brīvības_pak1; brīvības_pak2)", "d": "Atgriež apgrieztu (labā zara) F varbūtības sadalījumu: ja p = FDIST(x,...), FINV(p,...) = x", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas sa<PERSON><PERSON><PERSON> ar <PERSON> kumulatīvo sadalījumu; tas ir skaitlis no 0 līdz 1 ieskaitot!ir brīvības pakāpju skaitītājs - skaitlis no 1 līdz 10^10, neieskaitot 10^10!ir brīvības pakāpju saucējs - skaitlis no 1 līdz 10^10, neieskai<PERSON>t 10^10"}, "FTEST": {"a": "(masīvs1; masīvs2)", "d": "Atgriež F testa rezultātus - divu zaru varbūtību, ka dispersijas Masīvā1 un Masīvā2 nav nozīmīgi atšķirīgas", "ad": "ir pirmais datu masīvs vai diapazons; tie var būt skaitļi vai nosaukumi, masīvi vai atsauces, kur<PERSON><PERSON> ir skaitļi (atstarpes tiek ignorētas)!ir otrais datu masīvs vai diapazons; tie var būt skaitļi vai nosaukumi, mas<PERSON><PERSON> vai atsauces, kur<PERSON><PERSON> ir skait<PERSON> (atstarpes tiek ignorētas)"}, "F.DIST": {"a": "(x; brīvības_pakāpe1; brīvības_pakāpe2; kumulatīvā)", "d": "Atgriež divu datu kopu (kreisā zara) F varbūtības sadalījumu (atšķirības pakāpi)", "ad": "ir v<PERSON><PERSON><PERSON><PERSON>, kur<PERSON> jāaprēķina funkcija, nenegatī<PERSON><PERSON> skaitlis!ir skaitītāja brīvības pakāpes - skaitlis no 1 līdz 10^10, neieskaitot 10^10!ir saucēja brīvības pakāpes - skaitlis no 1 līdz 10^10, neieskai<PERSON>t 10^10!ir loģiskā v<PERSON><PERSON><PERSON><PERSON>, kas funkcijai jāatgriež: kumulatīvajai sadalījuma funkcijai = TRUE; varb<PERSON>t<PERSON><PERSON> blīvuma funkcijai = FALSE"}, "F.DIST.RT": {"a": "(x; brīvības_pakāpe1; brīvības_pakāpe2)", "d": "Atgriež divu datu kopu (labā zara) F varbūtības sadalījumu (atšķirības pakāpi)", "ad": "ir v<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> jāaprēķina funkcija, ne<PERSON><PERSON><PERSON><PERSON><PERSON> skaitlis!ir skaitītāja brīvības pakāpes - skaitlis no 1 līdz 10^10, neieskaitot 10^10!ir saucēja brīvības pakāpes - skaitlis no 1 līdz 10^10, neieskaitot 10^10"}, "F.INV": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; brīvības_pak1; brīvības_pak2)", "d": "Atgriež apgrieztu (kreisā zara) F varbūtības sadalījumu: ja p = F.DIST(x,...), F.INV(p,...) = x", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas sa<PERSON><PERSON><PERSON> ar <PERSON> kumulatīvo sadalījumu; tas ir skaitlis no 0 līdz 1 ieskaitot!ir brīvības pakāpju skaitītājs - skaitlis no 1 līdz 10^10, neieskaitot 10^10!ir brīvības pakāpju saucējs - skaitlis no 1 līdz 10^10, neieskai<PERSON>t 10^10"}, "F.INV.RT": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; brīvības_pak1; brīvības_pak2)", "d": "Atgriež apgrieztu (labā zara) F varbūtības sadalījumu: ja p = F.DIST.RT(x,...), F.INV.RT(p,...) = x", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas sa<PERSON><PERSON><PERSON> ar <PERSON> kumulatīvo sadalījumu; tas ir skaitlis no 0 līdz 1 ieskaitot!ir brīvības pakāpju skaitītājs - skaitlis no 1 līdz 10^10, neieskaitot 10^10!ir brīvības pakāpju saucējs - skaitlis no 1 līdz 10^10, neieskai<PERSON>t 10^10"}, "F.TEST": {"a": "(masīvs1; masīvs2)", "d": "Atgriež F testa rezultātus - divu zaru varbūtību, ka dispersijas Masīvā1 un Masīvā2 nav nozīmīgi atšķirīgas", "ad": "ir pirmais datu masīvs vai diapazons; tie var būt skaitļi vai nosaukumi, masīvi vai atsauces, kur<PERSON><PERSON> ir skaitļi (atstarpes tiek ignorētas)!ir otrais datu masīvs vai diapazons; tie var būt skaitļi vai nosaukumi, mas<PERSON><PERSON> vai atsauces, kur<PERSON><PERSON> ir skait<PERSON> (atstarpes tiek ignorētas)"}, "FISHER": {"a": "(x)", "d": "Atgriež Fišera transformāciju", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> j<PERSON> - <PERSON><PERSON><PERSON><PERSON> no -1 un 1, <PERSON>ieskaitot -1 un 1"}, "FISHERINV": {"a": "(y)", "d": "Atgriež apgrieztu Fišera transformāciju: ja y = FISHER(x), FISHERINV(y) = x", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON>, kurai j<PERSON>iegūst apgrieztā transformā<PERSON>ja"}, "FORECAST": {"a": "(x; zināmie_y; zināmie_x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> esoš<PERSON> vērt<PERSON>, aprēķina vai prognozē nākotnes vērtību saskaņā ar lineāru tendences līkni", "ad": "ir datu punkts, kuram j<PERSON><PERSON> vērtība; tai jābūt skaitliskai vērtībai!ir atkarīgais skaitlisku datu masīvs vai diapazons!ir neatkarīgais skaitlisku datu masīvs vai diapazons. Zināmo_x dispersija nedrīkst būt nulle"}, "FORECAST.ETS": {"a": "(mērķa_datums; vērtības; laika_grafiks; [sezonalitāte]; [datu_p<PERSON><PERSON>na]; [apkopoju<PERSON>])", "d": "Atgriež prognozēto vērtību konkrētam nākotnes mērķa datumam, i<PERSON><PERSON><PERSON><PERSON> eksponenci<PERSON><PERSON> l<PERSON> metodi.", "ad": "ir datu punkts, kuram Spreadsheet Editor prognozē vērtību. Tam ir jāturpina vērtību raksts laika grafikā.!ir skaitlisko vērtību masīvs vai diapazons, ko prognozējat.!ir neatkarīgais skaitlisko datu masīvs vai diapazons. Datumiem laika grafikā ir jābūt konsekventam solim starp tiem, un tas nevar būt nulle.!ir neobligāta skaitliska vērtība, kas norāda sezonālā modeļa garumu. Noklusējuma vērtība 1 norāda, ka sezonalitāte tiek konstatēta automātiski.!ir neobligāta vērtība trūkstošo vērtību apstrādei. Noklusējuma vērtība 1 aizstāj trūksto<PERSON><PERSON><PERSON> vērt<PERSON>, izmantojot interpolāciju, bet 0 aizstāj tās ar nullēm.!ir neobligāta skaitliska vērtība vairāku vērtību apkopošanai ar vienu un to pašu laikspiedolu. Ja tā ir tukša, programma Spreadsheet Editor nosaka vidējās vērtības."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Atgriež ticamības intervālu prognozētajai vērtībai norādītajā mērķa datumā.", "ad": "ir datu punkts, kuram Spreadsheet Editor prognozē vērtību. Tam ir jāturpina vērtību raksts laika grafikā.!ir skaitlisko vērtību masīvs vai diapazons, ko prognozējat.!ir neatkarīgais skaitlisko datu masīvs vai diapazons. Datumiem laika grafikā ir jābūt konsekventam solim starp tiem, un tas nevar būt nulle.!ir skaitlis starp 0 un 1, kas norāda ticamības līmeni aprēķinātajam ticamības intervālam. Noklusējuma vērtība ir .95.!ir neobligāta skaitliska vērtība, kas norāda sezonālā modeļa garumu. Noklusējuma vērtība 1 norāda, ka sezonalitāte tiek konstatēta automātiski.!ir neobligāta vērtība trūkstošo vērtību apstrādei. Noklusējuma vērtība 1 aizstāj trū<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>, i<PERSON><PERSON><PERSON><PERSON> interpol<PERSON>ju, bet 0 aizstāj tās ar nullēm.!ir neobligāta skaitliska vērtība vairāku vērtību apkopošanai ar vienu un to pašu laikspiedolu. Ja tā ir tukša, programma Spreadsheet Editor nosaka vidējās vērtības."}, "FORECAST.ETS.SEASONALITY": {"a": "(v<PERSON><PERSON><PERSON><PERSON>; laika_grafiks; [datu_p<PERSON><PERSON><PERSON>]; [ap<PERSON><PERSON>ju<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> tā atkārtotās modeļa garu<PERSON>, ko programma noteica norādītajai laika sērijai.", "ad": "ir skaitlisku datu masīvs vai diapazons, ko prognozējat.!ir neatkarīgs skaitlisku datu masīvs vai diapazons. Datumiem laika grafikā jābūt konsekventam solim starp tiem, un tas nevar būt nulle.!ir neobligāta vērtība trūkstošo vērtību apstrādei. 0 aizstāj trūkstošās vērtības ar nullēm. Noklusējuma vērtība (1) aizstāj trūkstoš<PERSON><PERSON> vērt<PERSON>, izmantojot interpolāciju, bet 0 tās aizstāj ar nullēm.!ir neobligāta skaitliska vērtība vairāku vērtību apkopošanai ar vienu un to pašu laikspiedolu. Ja tā ir tukša, programma Spreadsheet Editor nosaka vidējās vērtības."}, "FORECAST.ETS.STAT": {"a": "(v<PERSON><PERSON><PERSON><PERSON>; laika_grafiks; statistikas_tips; [sezon<PERSON><PERSON><PERSON>te]; [datu_p<PERSON><PERSON><PERSON><PERSON>]; [apkopoju<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> piepra<PERSON> prognozes statistiku.", "ad": "ir skaitlisku datu masīvs vai diapazons, ko prognozējat.!ir neatkarīgs skaitlisku datu masīvs vai diapazons. Starp datumiem laika grafikā jābūt konsekventam solim, un tas nevar būt nulle.!ir skaitlis no 1 līdz 8, kas norāda uz to, kuru statistiku Spreadsheet Editor atgriezīs aprēķinātajai prognozei. !ir neobligāta skaitliska vērtība, kas norāda sezonālā modeļa garumu. Noklusējuma vērtība (1) norāda uz to, ka sezonalitāte tiek konstatēta automātiski.!ir neobligāta vērtība trūkstošo vērtību apstrādei. 0 aizstāj trūkstošās vērtības ar nullēm. Noklusējuma vērtība (1) aizstāj trūksto<PERSON><PERSON><PERSON> v<PERSON>, izman<PERSON><PERSON><PERSON> interpolāciju, bet 0 tās aizstāj ar nullēm.!ir neobligāta skaitliska vērtība vairāku vērtību apkopošanai ar vienu un to pašu laikspiedolu. Ja tā ir tukša, programma Spreadsheet Editor nosaka vidējās vērtības."}, "FORECAST.LINEAR": {"a": "(x; zināmie_y; zināmie_x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> esoš<PERSON> vērt<PERSON>, aprēķina vai prognozē nākotnes vērtību saskaņā ar lineāru tendences līkni", "ad": "ir datu punkts, kuram j<PERSON><PERSON> vērtība; tai jābūt skaitliskai vērtībai!ir atkarīgais skaitlisku datu masīvs vai diapazons!ir neatkarīgais skaitlisku datu masīvs vai diapazons. Zināmo_x dispersija nedrīkst būt nulle"}, "FREQUENCY": {"a": "(datu_masīvs; intervālu_masīvs)", "d": "Aprēķina, cik bieži vērtības sastopamas vērtību diapazonā un pēc tam atgriež vertikālu skaitļu masīvu, kurā ir par vienu elementu vairāk nekā Intervālu_masīvā", "ad": "ir vērtību kopas masīvs vai atsauce uz vērtību kopu, kurā jāaprēķina sastopamības biežums (atstarpes un teksts tiek ignorēts)!ir intervālu masīvs vai atsauce uz intervāliem, kādā jāsagrupē vērtības datu_masīvā"}, "GAMMA": {"a": "(x)", "d": "Atgriež gamma funkcijas vērtību", "ad": "ir v<PERSON><PERSON><PERSON><PERSON>, kurai jāaprēķina gamma"}, "GAMMADIST": {"a": "(x; alfa; beta; kumulatīvā)", "d": "Atgriež gamma sadalījumu", "ad": "ir vērtība, no kuras jānovērtē sadalījums; tas ir nenegatīvs skaitlis!ir sadalījuma parametrs; tas ir pozitīvs skaitlis!ir sadalījuma parametrs; tas ir pozitīvs skaitlis. Ja beta = 1, funkcija GAMMADIST atgriež standarta gamma sadalījumu!ir loģiskā vērtība: atgriezt kumulatīvo sadalījuma funkciju = TRUE; atgriezt varbūtību masas funkciju = FALSE vai izlaists"}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulatīvā)", "d": "Atgriež gamma sadalījumu", "ad": "ir vērtība, no kuras jānovērtē sadalījums; tas ir nenegatīvs skaitlis!ir sadalījuma parametrs; tas ir pozitīvs skaitlis!ir sadalījuma parametrs; tas ir pozitīvs skaitlis. Ja beta = 1, funkcija GAMMA.DIST atgriež standarta gamma sadalījumu!ir loģiskā vērtība: atgriezt kumulatīvo sadalījuma funkciju = TRUE; atgriezt varbūtību masas funkciju = FALSE vai izlaists"}, "GAMMAINV": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; alfa; beta)", "d": "Atgriež apgrieztu gamma kumulatīvo sadalījumu: ja p = GAMMADIST(x,...), GAMMAINV(p,...) = x", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas sa<PERSON><PERSON>ta ar gamma sadalījumu; tas ir skaitlis no 0 līdz 1 ieskaitot!ir sadalījuma parametrs; tas ir pozitīvs skaitlis!ir sadalījuma parametrs; tas ir pozitīvs skaitlis. Ja beta = 1, GAMMAINV atgriež apgrieztu standarta gamma sadalījumu"}, "GAMMA.INV": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; alfa; beta)", "d": "Atgriež apgrieztu gamma kumulatīvo sadalījumu: ja p = GAMMA.DIST(x,...), GAMMA.INV(p,...) = x", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas sa<PERSON><PERSON>ta ar gamma sadalījumu; tas ir skaitlis no 0 līdz 1 ieskaitot!ir sadalījuma parametrs; tas ir pozitīvs skaitlis!ir sadalījuma parametrs; tas ir pozitīvs skaitlis. Ja beta = 1, GAMMA.INV atgriež apgrieztu standarta gamma sadalījumu"}, "GAMMALN": {"a": "(x)", "d": "Atgriež gamma funkcijas naturālo logaritmu", "ad": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, kam jāaprēķina funkcija GAMMALN, poz<PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Atgriež gamma funkcijas naturālo logaritmu", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON>, kam jāaprēķina funkcija GAMMALN.PRECISE, pozitī<PERSON><PERSON> s<PERSON>is"}, "GAUSS": {"a": "(x)", "d": "<PERSON>g<PERSON><PERSON> par 0,5 mazāk nekā standarta parastais kumulatīvais sadalīju<PERSON>", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON>, kurai jāveido sadalīju<PERSON>"}, "GEOMEAN": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež pozitīvu skaitlisku datu masīva vai diapazona vidējo ģeometrisko", "ad": "ir 1 līdz 255 skait<PERSON><PERSON> vai no<PERSON>, mas<PERSON><PERSON> vai atsau<PERSON>, k<PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON> skait<PERSON><PERSON>, kam var aprēķināt vidējo"}, "GROWTH": {"a": "(zināmie_y; [zināmie_x]; [jaunie_x]; [konst])", "d": "Atgriež skaitļus eksponenciālā progresijā saskaņā ar zināmo datu punktiem", "ad": "ir jau zināmo y vērtību kopa relācijā y=b*m^x - pozitīvu skaitļu diapazons vai masīvs!ir tādu x vērtību papildu kopa, k<PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON>, ir zin<PERSON><PERSON> relācijā y=b*m^x - masīvs vai diapazons, kas ir tikpat liels kā Zināmie_y!ir jaunas x vērtības, kurām funkcijai GROWTH jāatgriež atbilstošās y vērtības!ir loģiskā vērtība: konstante b tiek aprēķināta kā parasti, ja Konst = TRUE; b tiek iestatīts vienāds ar 1, ja Konst = FALSE vai izlaists"}, "HARMEAN": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež pozitīvu skaitļu kopas vidējo harmonisko: apgriezto skaitļu apgriezto vidējo aritmētisko", "ad": "ir 1 līdz 255 skait<PERSON><PERSON> vai no<PERSON>, mas<PERSON><PERSON> vai atsau<PERSON>, k<PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON> skai<PERSON>, kam aprēķināms vidējais harmoniskais"}, "HYPGEOM.DIST": {"a": "(izlase_labvēlizn; skaits_izlasē; populācija_labvēlizn; skaits_pop; kumulatīvā)", "d": "Atgriež hiperģeometrisko sadalījumu", "ad": "ir labvēlīgu iznākumu skaits izlasē!ir izlases lielums!ir labvēlīgu iznākumu skaits populācijā!ir populācijas lielums!ir loģiskā vērtība: kumulatīvajai sadalījuma funkcijai izmantojiet TRUE; varbūtības blīvuma funkcijai izmantojiet FALSE"}, "HYPGEOMDIST": {"a": "(izlase_labvēlizn; skaitlis_izlase; populācija_labvēlizn; skaitlis_pop)", "d": "Atgriež hiperģeometrisku sadalījumu", "ad": "ir labvēlīgu iznākumu skaits izlasēs!ir izlases lielums!ir labvēlīgu iznākumu skaits populācijā!ir populācijas lielums"}, "INTERCEPT": {"a": "(zināmie_y; zināmie_x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> regres<PERSON>, kas novilkta caur zināmajām x vērtībām un y vērtībām un visvairāk tām atbilst, aprēķina punktu, kurā taisne krustosies ar y asi", "ad": "ir atkarīgā novērojumu vai datu kopa; tie var būt skaitļi vai nosaukumi, masīvi vai atsauces, kur<PERSON>s ir skaitļi!ir neatkarīgā novērojumu vai datu kopa; tie var būt skaitļi vai nosaukumi, masīvi vai atsauces, kur<PERSON><PERSON> ir skaitļi"}, "KURT": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež datu kopas ekscesa koeficientu", "ad": "ir 1 līdz 255 skait<PERSON><PERSON> vai no<PERSON>, mas<PERSON><PERSON> vai atsau<PERSON>, kur<PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON> skai<PERSON><PERSON>, kam aprēķināms ekscesa koeficients"}, "LARGE": {"a": "(masīvs; k)", "d": "Atgriež datu kopas k-to lielāko vērtību. <PERSON><PERSON><PERSON><PERSON>, piekto liel<PERSON>ko skaitli", "ad": "ir datu masīvs vai diapazons, kam jānosaka k-tā lielākā vērtība!ir atgriežamās vērtības pozīcija (sākot ar lielāko) masīvā vai šūnu diapazonā"}, "LINEST": {"a": "(zināmie_y; [zināmie_x]; [konst]; [statist])", "d": "<PERSON><PERSON><PERSON><PERSON> statist<PERSON>, kas raksturo lineāru tendences līkni pa zināmo datu punktiem, novelkot taisni ar ma<PERSON>ā<PERSON> k<PERSON>dr<PERSON> metodi", "ad": "ir y vērtību kopa, kas jau zināma relācijā y=mx+b!ir x vērtību papildu kopa, kura<PERSON>, ies<PERSON><PERSON><PERSON><PERSON>, jau ir zināmas relācijā y=mx+b!ir loģiskā vērtība: konstante b tiek aprēķināta kā parasti, ja <PERSON>nst = TRUE vai izlaists; b tiek iestatīts vienāds ar nulli, ja Konst = FALSE!ir loģiskā vērtība: atgriež papildu regresijas statistiku = TRUE; atgriež m koeficientus un konstanti b = FALSE vai izlaists"}, "LOGEST": {"a": "(zināmie_y; [zināmie_x]; [konst]; [statist])", "d": "<PERSON><PERSON><PERSON><PERSON> statist<PERSON>, kas raksturo ar zināmajiem datu punktiem saskanošo eksponentlīkni", "ad": "ir jau zināmo y vērtību kopa relācijā y=b*m^x!ir to x vērtību papild<PERSON>pa, k<PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON>, jau ir zināmas relācijā y=b*m^x!ir loģiskā vērtība: konstante b tiek aprēķināta kā parasti, ja <PERSON><PERSON> = TRUE vai izlaists; b tiek iestatīts vienāds ar 1, ja Konst = FALSE!ir loģiskā vērtība: atgriež papildu regresijas statistiku = TRUE; atgriež m koeficientus un koeficientu b = FALSE vai izlaiž"}, "LOGINV": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; vid<PERSON><PERSON><PERSON>; standart<PERSON>irze)", "d": "Atgriež apgrieztu logaritmiski normālu kumulatīvu x sadalījuma funkciju, kur ln(x) parasti ir sadalīts ar parametriem Vidējais un Standartnovirze", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas saist<PERSON>ta ar logaritmiski normālu sadalījumu; tas ir skaitlis no 0 līdz 1 ieskaitot!ir ln(x) vidējais!ir ln(x) standartnovirze; tas ir pozitīvs skaitlis"}, "LOGNORM.DIST": {"a": "(x; vidē<PERSON><PERSON>; standart<PERSON>ir<PERSON>; kumulatīvā)", "d": "Atgriež x logaritmiski normā<PERSON>, kur ln(x) ir normāli sadalīts ar parametriem Vidējais un Standartnovirze", "ad": "ir vērtība, kurā jāaprēķina funkcija - pozitīvs skaitlis!ir ln(x) vidējais!ir ln(x) standartnovirze - pozitīvs skaitlis!ir loģiskā vērtība: kumulatīvajai sadalījuma funkcijai izmantojiet TRUE; varbūtības blīvuma funkcijai izmantojiet FALSE"}, "LOGNORM.INV": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; vid<PERSON><PERSON><PERSON>; standart<PERSON>irze)", "d": "Atgriež apgrieztu logaritmiski normālu kumulatīvu x sadalījuma funkciju, kur ln(x) ir normāli sadalīts ar parametriem Vidējais un Standartnovirze", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas saist<PERSON>ta ar logaritmiski normālu sadalījumu; tas ir skaitlis no 0 līdz 1 ieskaitot!ir ln(x) vidējais!ir ln(x) standartnovirze; tas ir pozitīvs skaitlis"}, "LOGNORMDIST": {"a": "(x; vid<PERSON><PERSON><PERSON>; standart<PERSON>irze)", "d": "Atgriež kumulatīvo logaritmiski normālo x sadalījumu, kur ln(x) parasti ir sadalīts ar parametriem Vidējais un Standartnovirze", "ad": "ir v<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> j<PERSON>rt<PERSON> funk<PERSON>; tas ir pozitīvs skaitlis!ir ln(x) vidējais!ir ln(x) standartnovirze; tas ir pozitīvs skaitlis"}, "MAX": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež visliel<PERSON>ko vērtību no vērtību kopas. Ignorē loģiskās vērtības un tekstu", "ad": "ir 1 līdz 255 skaitļu, t<PERSON><PERSON><PERSON>, loģisko vērtību vai teksta skaitļu, no kuriem iegūstams lielākais"}, "MAXA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "Atgriež lielāko vērtību kopas vērtību. Neignorē loģiskās vērtības un tekstu", "ad": "ir 1 līdz 255 skait<PERSON>u, t<PERSON><PERSON><PERSON>, loģisko vērtību vai teksta skaitļu, kam j<PERSON><PERSON><PERSON><PERSON><PERSON> maksim<PERSON><PERSON> vērtība"}, "MAXIFS": {"a": "(maks<PERSON><PERSON><PERSON><PERSON>_diapazons; kritēriju_diapazons; kritēriji; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> maks<PERSON><PERSON> v<PERSON>, ko norāda attiecīgā nosacījumu vai kritēriju kopa", "ad": "<PERSON><PERSON><PERSON>, kur<PERSON><PERSON> noteikt maksimālo vērtību!ir to š<PERSON><PERSON> diapazons, kuras vēlaties novērtēt konkrētajam nosacījumam!ir nosacījums vai kritērijs s<PERSON>, izteiksm<PERSON> vai teksta formā, kas definē to, kuras š<PERSON> tiks i<PERSON>, nosakot maksimālo vērtību"}, "MEDIAN": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež mediānu jeb skaitli norā<PERSON><PERSON><PERSON> skait<PERSON>u kopas vidū", "ad": "ir 1 līdz 255 skait<PERSON><PERSON> vai no<PERSON>, mas<PERSON><PERSON> vai atsau<PERSON>, k<PERSON><PERSON><PERSON> ir skai<PERSON><PERSON><PERSON>, kam j<PERSON><PERSON><PERSON>a medi<PERSON>a"}, "MIN": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež vismazāko skaitli vērtību kopā. Ignorē loģiskās vērtības un tekstu", "ad": "ir 1 līdz 255 skaitļu, t<PERSON><PERSON><PERSON>, loģisko vērtību vai teksta skaitļu, no kuriem iegūstams mazākais"}, "MINA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "Atgriež mazāko vērtību kopas vērtību. Neignorē loģiskās vērtības un tekstu", "ad": "ir 1 līdz 255 skait<PERSON>u, t<PERSON><PERSON><PERSON>, loģisko vērtību vai teksta skaitļu, kam jānoskaidro minimālā vērtība"}, "MINIFS": {"a": "(minimālais_diapazons; kritēriju_diapazons; kritēriji; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> minim<PERSON><PERSON> v<PERSON>, ko norāda attiecīgā nosacījumu vai kritēriju kopa", "ad": "<PERSON><PERSON><PERSON>, kur<PERSON><PERSON> noteikt minimālo vērtību!ir to <PERSON><PERSON><PERSON> diapazons, kuras vēlaties novērtēt konkrētajam nosacījumam!ir nosacījums vai kritērijs s<PERSON>t<PERSON>, izteiksm<PERSON> vai teksta formā, kas definē to, kuras š<PERSON> tiks i<PERSON>, nosakot minimālo vērtību"}, "MODE": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež datu masīvā vai diapazonā visbiežāk sastopamo vai atkārtoto vērtību", "ad": "ir no 1 līdz 255 skaitļiem vai nosaukumiem, masī<PERSON>m vai atsaucēm, kurām aprēķināma moda"}, "MODE.MULT": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež vertikālu visbiežāk sastopamo vai atkārtojošos vērtību masīvu kā datu masīvu vai diapazonu. Horizontālam masīvam izmantojiet =TRANSPOSE(MODE.MULT(skaitlis1,skaitlis2,...))", "ad": "ir no 1 līdz 255 skaitļiem vai nosaukumiem, masī<PERSON><PERSON> vai atsaucēm, kur<PERSON><PERSON> ir skait<PERSON><PERSON>, kam aprēķināma moda"}, "MODE.SNGL": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež datu masīvā vai diapazonā visbiežāk sastopamo vai atkārtoto vērtību", "ad": "ir no 1 līdz 255 skaitļiem vai nosaukumiem, masī<PERSON><PERSON> vai atsaucēm, kur<PERSON><PERSON> ir skait<PERSON><PERSON>, kam aprēķināma moda"}, "NEGBINOM.DIST": {"a": "(nelabvēlizn_skaits; labvēlizn_skaits; labvēlizn_varbūtība; kumulatīvā)", "d": "Atgriež negatīvu binomiālo <PERSON>, var<PERSON><PERSON><PERSON><PERSON><PERSON>, ka būs (nelabvēlizn_ skaits) nelabvēlīgi iznākumi pirms (labvēlizn_skaits). labvēlīgā iznākuma ar (labvēlizn_varbūtība) varbūtību", "ad": "ir nelabvēlīgu iznākumu skaits!ir labvēlīgu iznākumu skaita slieksnis!ir labvēlīgu iznākumu varbūtība - skaitlis no 0 līdz 1!ir loģiskā vērtība: kumulatīvajai sadalījuma funkcijai izmantojiet TRUE; varbūtību masas funkcijai izmantojiet FALSE"}, "NEGBINOMDIST": {"a": "(nelabvēlizn_skaits; labvēlizn_skaits; labvēlizn_varbūtība)", "d": "Atgriež negatīvu binomiālo <PERSON>, var<PERSON><PERSON><PERSON><PERSON><PERSON>, ka būs (nelabvēlizn_skaits) nelabvēlīgi iznākumi pirms (labvēlizn_skaits). labvēlīgā iznākumā ar (labvēlizn_varbūtība) varbūtību", "ad": "ir nelabvēlīgu iznākumu skaits!ir labvēlīgu iznākumu skaita slieksnis!ir labvēlīgu iznākumu varbūtība - skaitlis no 0 līdz 1"}, "NORM.DIST": {"a": "(x; vidē<PERSON><PERSON>; standart<PERSON>ir<PERSON>; kumulatīvā)", "d": "<PERSON><PERSON><PERSON>ž norā<PERSON><PERSON><PERSON><PERSON> vidējā lie<PERSON>a un standartnovirzes norm<PERSON><PERSON> sad<PERSON>", "ad": "ir vērt<PERSON><PERSON>, kam jāiegūst sadalījums!ir sadalījuma vidējais aritmētiskais!ir sadalījuma standartnovirze; tas ir pozitīvs skaitlis!ir loģiskā vērtība: kumulatīvajai sadalījuma funkcijai izmantojiet TRUE; varbūtības blīvuma funkcijai izmantojiet FALSE"}, "NORMDIST": {"a": "(x; vidē<PERSON><PERSON>; standart<PERSON>ir<PERSON>; kumulatīvā)", "d": "Atg<PERSON>ž norādī<PERSON><PERSON> vidējā lieluma un standartnovirzes normālo kumulatīvo sadalījumu", "ad": "ir vērt<PERSON><PERSON>, kam jāiegūst sadalījums!ir sadalījuma vidējais aritmētiskais!ir sadalījuma standartnovirze; tas ir pozitīvs skaitlis!ir loģiskā vērtība: kumulatīvajai sadalījuma funkcijai izmantojiet TRUE; varbūtības blīvuma funkcijai izmantojiet FALSE"}, "NORM.INV": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; vid<PERSON><PERSON><PERSON>; standart<PERSON>irze)", "d": "Atgriež apgrieztu norād<PERSON><PERSON><PERSON><PERSON> vidējās vērtības un standartnovirzes normālo kumulatīvo sadalījumu", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas atbilst normālajam sadalījumam; tas ir skaitlis no 0 līdz 1 ieskaitot!ir sadalījuma vidējais aritmētiskais!ir sadalī<PERSON><PERSON> standartnovirze; tas ir pozitīvs skaitlis"}, "NORMINV": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; vid<PERSON><PERSON><PERSON>; standart<PERSON>irze)", "d": "Atgriež nor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vidējās vērtības un standartnovirzes apgrieztu normālo kumulatīvo sadalījumu", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas atbilst normālajam sadalījumam; tas ir skaitlis no 0 līdz 1 ieskaitot!ir sadalījuma vidējais aritmētiskais!ir sadalī<PERSON><PERSON> standartnovirze; tas ir pozitīvs skaitlis"}, "NORM.S.DIST": {"a": "(z; kumulatīvā)", "d": "<PERSON>g<PERSON><PERSON> standarta norm<PERSON> (tā vidē<PERSON><PERSON> ir nulle, bet standartnovirze - viens)", "ad": "ir vērt<PERSON><PERSON>, kam jāaprēķina sadalījums!ir loģiskā vērtība, kas funkcijai jāatgriež: kumulatīvajai sadalījuma funkcijai = TRUE; varbūtības blīvuma funkcijai = FALSE"}, "NORMSDIST": {"a": "(z)", "d": "Atgriež standarta normālo kum<PERSON> sadalīju<PERSON> (tā vidē<PERSON><PERSON> ir nulle, bet standartnovirze - viens)", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON>, kurai jāiegūst sadalījums"}, "NORM.S.INV": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež apgrieztu standarta normālo kumulatīvo sadalījumu (tā vidē<PERSON>is ir nulle, bet standartnovirze - viens)", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas atbilst normālajam sadalījumam; tas ir skaitlis no 0 līdz 1 ieskaitot"}, "NORMSINV": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež apgrieztu standarta normālo kumulatīvo sadalījumu (tā vidē<PERSON>is ir nulle, bet standartnovirze - viens)", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas atbilst normālajam sadalījumam; tas ir skaitlis no 0 līdz 1 ieskaitot"}, "PEARSON": {"a": "(masīvs1; masīvs2)", "d": "Atgriež Pīrsona korelācijas koeficientu r", "ad": "ir neatkarīgu vērtību kopa!ir atkarīgu vērtību kopa"}, "PERCENTILE": {"a": "(masīvs; k)", "d": "Atgriež diapazona vērtību k-to procentili", "ad": "ir datu masīvs vai diapazons, kas definē relatīvo novietojumu!ir procentiles vērtība no 0 līdz 1 ieskaitot"}, "PERCENTILE.EXC": {"a": "(masīvs; k)", "d": "Atgriež diapazona vērtību k-to procentili, kur k ir diapazons no 0 līdz 1 neieskaitot", "ad": "ir datu masīvs vai diapazons, kas definē relatīvo novietojumu!ir procentiles vērtība no 0 līdz 1 ieskaitot"}, "PERCENTILE.INC": {"a": "(masīvs; k)", "d": "Atgriež diapazona vērtību k-to procentili, kur k ir diapazons no 0 līdz 1 ieskaitot", "ad": "ir datu masīvs vai diapazons, kas definē relatīvo novietojumu!ir procentiles vērtība no 0 līdz 1 ieskaitot"}, "PERCENTRANK": {"a": "(masīvs; x; [nozī<PERSON><PERSON>ba])", "d": "Atgriež datu kopā ietilps<PERSON>šas vērtības rangu procentuāli no datu kopas", "ad": "ir datu masīvs vai diapazons, kur<PERSON> skaitliskās vērtības definē relatīvo novietojumu!ir vē<PERSON><PERSON><PERSON>, kam noskaidrojams rangs!ir neoblig<PERSON><PERSON> vērtība, kas identificē nozīmīgu ciparu skaitu atgrieztajā procentuālajā vērtībā; ja i<PERSON><PERSON><PERSON>, tie ir trīs cipari (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(masīvs; x; [nozī<PERSON><PERSON>ba])", "d": "Atgriež datu kopā ietilpstošas vērtības rangu kā procentu (no 0 līdz 1 neieskaitot) no datu kopas", "ad": "ir datu masīvs vai diapazons, kur<PERSON> skaitliskās vērtības definē relatīvo novietojumu!ir vē<PERSON><PERSON><PERSON>, kam noskaidrojams rangs!ir neoblig<PERSON><PERSON> vērtība, kas identificē nozīmīgu ciparu skaitu atgrieztajā procentuālajā vērtībā; ja i<PERSON><PERSON><PERSON>, tie ir trīs cipari (0,xxx%)"}, "PERCENTRANK.INC": {"a": "(masīvs; x; [nozī<PERSON><PERSON>ba])", "d": "Atgriež datu kopā ietilpstošas vērtības rangu kā procentu (no 0 līdz 1 ieskaitot) no datu kopas", "ad": "ir datu masīvs vai diapazons, kur<PERSON> skaitliskās vērtības definē relatīvo novietojumu!ir vē<PERSON><PERSON><PERSON>, kam noskaidrojams rangs!ir neoblig<PERSON><PERSON> vērtība, kas identificē nozīmīgu ciparu skaitu atgrieztajā procentuālajā vērtībā; ja i<PERSON><PERSON><PERSON>, tie ir trīs cipari (0,xxx%)"}, "PERMUT": {"a": "(skaitlis; izvēlētais_skaitlis)", "d": "Atgriež no visiem objektiem izvēlēta noteikta objektu skaita permutāciju skaitu", "ad": "ir objektu kopskaits!ir objektu skaits katrā permutācijā"}, "PERMUTATIONA": {"a": "(skaitlis; izvēlētais_skaitlis)", "d": "Atgriež no visiem objektiem izvēlēta noteikta objektu skaita permutāciju skaitu", "ad": "ir objektu kopskaits!ir objektu skaits katrā permutācijā"}, "PHI": {"a": "(x)", "d": "Atg<PERSON>ž standarta normālā sadalījuma blīvuma funkcijas vērtību", "ad": "ir <PERSON>, kuram jāaprēķina standarta normālā sadalījuma blīvums"}, "POISSON": {"a": "(x; vidējais; kumulatīvā)", "d": "Atgriež Puasona sadalījumu", "ad": "ir notikumu skaits!ir sagaidāmā skaitliskā vērtība; tas ir pozitīvs skaitlis!ir loģiskā vērtība: kumulatīvajai Puasona varbūtībai izmantojiet TRUE; Puasona varbūtību masas sadalījuma funkcijai izmantojiet FALSE"}, "POISSON.DIST": {"a": "(x; vidējais; kumulatīvā)", "d": "Atgriež Puasona sadalījumu", "ad": "ir notikumu skaits!ir sagaidāmā skaitliskā vērtība; tas ir pozitīvs skaitlis!ir loģiskā vērtība: kumulatīvajai Puasona varbūtībai izmantojiet TRUE; Puasona varbūtību masas funkcijai izmantojiet FALSE"}, "PROB": {"a": "(x_diapazons; varb_diapazons; zemākā_robeža; [augstākā_robeža])", "d": "<PERSON><PERSON><PERSON><PERSON>, ka vērtības diapazonā atrodas starp abām robežām vai ir vienādas ar zemāko robežu", "ad": "ir skaitlisko vērtību x diapazons, ar kuru ir saistītas varbūtības!ir to varb<PERSON>t<PERSON>bu kopa, kuras saistītas ar vērtībām X_diapazonā, vērtības no 0 līdz 1, neieskaitot 0!ir vērtības zemākā robeža, kam jānoskaidro varbūtība!ir neobligāta vērtības augšējā robe<PERSON>, Funkcija PROB atgriež varbūtību, ka X_diapazona vērtības ir vienādas ar Z<PERSON>ā<PERSON>_robežu"}, "QUARTILE": {"a": "(masīvs; kvar<PERSON>)", "d": "Atgriež datu kopas k<PERSON>i", "ad": "ir skaitlisku vērtību masīvs vai šūnu diapazons, kam aprēķināma kvartiles vērtība!ir skaitlis: minimālā vērtība = 0; 1. kvartile = 1; medi<PERSON><PERSON> vērtība = 2; 3. kvartile = 3; maks<PERSON><PERSON><PERSON><PERSON> vērtība = 4"}, "QUARTILE.INC": {"a": "(masīvs; kvar<PERSON>)", "d": "Atgriež datu k<PERSON>, izmantojot procentiles vērtības no 0 līdz 1 ieskaitot", "ad": "ir skaitlisku vērtību masīvs vai šūnu diapazons, kam aprēķināma kvartiles vērtība!ir skaitlis: minimālā vērtība = 0; 1. kvartile = 1; medi<PERSON><PERSON> vērtība = 2; 3. kvartile = 3; maks<PERSON><PERSON><PERSON><PERSON> vērtība = 4"}, "QUARTILE.EXC": {"a": "(masīvs; kvar<PERSON>)", "d": "Atgriež datu k<PERSON>, izman<PERSON>jot procentiles vērtības no 0 līdz 1 neieskaitot", "ad": "ir skaitlisku vērtību masīvs vai šūnu diapazons, kam aprēķināma kvartiles vērtība!ir skaitlis: minimālā vērtība = 0; 1. kvartile = 1; medi<PERSON><PERSON> vērtība = 2; 3. kvartile = 3; maks<PERSON><PERSON><PERSON><PERSON> vērtība = 4"}, "RANK": {"a": "(skaitlis; ats; [kārtība])", "d": "Atgriež skaitļa rangu skaitļu sarakstā: tā lielumu attiecībā pret pārējām vērtībām sarakstā", "ad": "ir s<PERSON><PERSON><PERSON>, kura rangs ir jāatrod!ir skaitļu saraksta masīvs vai atsauce uz to. <PERSON><PERSON><PERSON><PERSON><PERSON>, kas nav skaitl<PERSON>, tiek ignorētas!ir skaitlis: rangs dilstošā secībā kārtotā sarakstā = 0 vai izlaists; rangs augošā secībā kārtotā sarakstā = jeb<PERSON> vērtība, kas nav nulle"}, "RANK.AVG": {"a": "(skaitlis; ats; [kārtība])", "d": "Atgriež skaitļa rangu skaitļu sarakstā: tā lielumu attiecībā pret pārējām vērtībām sarakstā; ja vairākām vērtībām ir vienāds rangs, tiek atgriezts vidējais rangs", "ad": "ir s<PERSON><PERSON><PERSON>, kura rangs ir jāatrod!ir skaitļu saraksta masīvs vai atsauce uz to. <PERSON><PERSON><PERSON><PERSON><PERSON>, kas nav skaitl<PERSON>, tiek ignorētas!ir skaitlis: rangs dilstošā secībā kārtotā sarakstā = 0 vai izlaists; rangs augošā secībā kārtotā sarakstā = jeb<PERSON> vērtība, kas nav nulle"}, "RANK.EQ": {"a": "(skaitlis; ats; [kārtība])", "d": "Atgriež skaitļa rangu skaitļu sarakstā: tā lielumu attiecībā pret pārējām vērtībām sarakstā; ja vairākām vērtībām ir rangs, tiek atgriezts augstākais šīs vērtību kopas rangs", "ad": "ir s<PERSON><PERSON><PERSON>, kura rangs ir jāatrod!ir skaitļu saraksta masīvs vai atsauce uz to. <PERSON><PERSON><PERSON><PERSON><PERSON>, kas nav skaitl<PERSON>, tiek ignorētas!ir skaitlis: rangs dilstošā secībā kārtotā sarakstā = 0 vai izlaists; rangs augošā secībā kārtotā sarakstā = jeb<PERSON> vērtība, kas nav nulle"}, "RSQ": {"a": "(zināmie_y; zināmie_x)", "d": "Atgriež Pīrsona korelācijas koeficienta kvadrātu norādītajos datu punktos", "ad": "ir datu punktu masīvs vai diapazons; tie var būt skaitļi vai nosaukumi, masīvi vai atsauces, kurās ietilpst skaitļi!ir datu punktu masīvs vai diapazons; tie var būt skaitļi vai nosaukumi, masīvi vai atsauces, kur<PERSON>s ietilpst skaitļi"}, "SKEW": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež sadalījuma asimetriju: sadalījuma asimetrijas pakāpi attiecībā pret vidējo", "ad": "ir 1 līdz 255 skait<PERSON>u vai no<PERSON>, mas<PERSON><PERSON> vai atsau<PERSON>, kur<PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON> skait<PERSON>i, kam aprēķināma asimetrija"}, "SKEW.P": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež sadalī<PERSON><PERSON> asime<PERSON>, pamatojoties uz kopskaitu: sadalījuma asimetrijas pakāpi attiecībā pret vidējo", "ad": "ir 1 līdz 254 skait<PERSON>i vai no<PERSON>, ma<PERSON><PERSON><PERSON> vai atsa<PERSON>, k<PERSON><PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON> skai<PERSON>, kam aprēķināma asimetrija"}, "SLOPE": {"a": "(zināmie_y; zināmie_x)", "d": "Atgriež lineārā<PERSON> regresijas taisnes slīpumu caur norādītajiem datu punktiem", "ad": "ir skaitlisku atkarīgu datu punktu masīvs vai šūnu diapazons; tie var būt skaitļi vai nosaukumi, masīvi vai atsauces, kur<PERSON>s ietilpst skaitļi!ir neatkarīgu datu punktu kopa, tie var būt skaitļi vai nosaukumi, masīvi vai atsauces, kur<PERSON><PERSON> ietilpst skaitļi"}, "SMALL": {"a": "(masīvs; k)", "d": "Atgriež datu kopas k-to mazāko vērtī<PERSON>. <PERSON><PERSON><PERSON><PERSON>, piekto maz<PERSON>ko skaitli", "ad": "ir datu masīvs vai diapazons, kam jānosaka k-tā mazākā vērtība!ir atgriežamās vērtības pozī<PERSON>ja (sākot ar ma<PERSON>) masīvā vai šūnu diapazonā"}, "STANDARDIZE": {"a": "(x; vid<PERSON><PERSON><PERSON>; standart<PERSON>irze)", "d": "Atg<PERSON>ž tāda sadalījuma normalizētu vērtību, kuru raksturo vidējais un standartnovirze", "ad": "ir normalizējamā vērtība!ir sadalījuma vidējais aritmētiskais!ir sadalījuma standartnovirze; tas ir pozitīvs skaitlis"}, "STDEV": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina stand<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> (izlas<PERSON> ignorē loģiskās vērtības un tekstu)", "ad": "ir no 1 līdz 255 skait<PERSON><PERSON><PERSON>, kas atbilst populācijas izlasei un kas var būt skaitļi vai atsauces, kur<PERSON><PERSON> ir skaitļi"}, "STDEV.P": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina stand<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> visu populāciju kā argumentus (ignorē loģiskās vērtības un tekstu)", "ad": "ir no 1 līdz 255 skait<PERSON><PERSON><PERSON>, kas atbilst populācijai un kas var būt skaitļi vai atsauces, kur<PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON> skaitļi"}, "STDEV.S": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina stand<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> (izlas<PERSON> ignorē loģiskās vērtības un tekstu)", "ad": "ir no 1 līdz 255 skait<PERSON><PERSON><PERSON>, kas atbilst populācijas izlasei un kas var būt skaitļi vai atsauces, kur<PERSON><PERSON> ir skaitļi"}, "STDEVA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vēr<PERSON>, ies<PERSON><PERSON>t loģiskās vērtības un tekstu. Tekstam un loģiskajai vērtībai FALSE ir vērtība 0; loģiskajai vērtībai TRUE ir vērtība 1", "ad": "ir 1 līdz 255 vērt<PERSON><PERSON>, kuras atbilst populācijas izlasei un kuras var būt vērtības vai nosaukumi vai atsauces uz vērtībām"}, "STDEVP": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina stand<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> visu populāciju kā argumentus (ignorē loģiskās vērtības un tekstu)", "ad": "ir no 1 līdz 255 skait<PERSON><PERSON><PERSON>, kas atbilst populācijai un kas var būt skaitļi vai atsauces, kur<PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON> skaitļi"}, "STDEVPA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "Aprēķina <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vērā visu populāciju, arī loģiskās vērtības un tekstu. Tekstam un loģiskajai vērtībai FALSE ir vērtība 0; loģiskajai vērtībai TRUE ir vērtība 1", "ad": "ir 1 līdz 255 vē<PERSON><PERSON><PERSON>, kuras atbilst populācijai un kuras var būt vērtības, <PERSON><PERSON><PERSON><PERSON>, mas<PERSON><PERSON> vai atsauces, kur<PERSON><PERSON> ietil<PERSON>t vērtības"}, "STEYX": {"a": "(zināmie_y; zināmie_x)", "d": "Atgriež regresijas katra zināmā x prognozētās y vērtības standarta kļūdu", "ad": "ir atkarīgu datu punktu masīvs vai diapazons; tie var būt skaitļi vai nosaukumi, mas<PERSON>vi vai atsauces, kur<PERSON>s ietilpst skaitļi!ir neatkarīgu datu punktu masīvs vai diapazons; tie var būt skaitļi vai nosaukumi, masīvi vai atsauces, kur<PERSON><PERSON> ietil<PERSON>t skaitļi"}, "TDIST": {"a": "(x; brīv<PERSON><PERSON>_pak; zari)", "d": "Atgriež Stjūdenta t sadalījumu", "ad": "ir skaitl<PERSON><PERSON> vērt<PERSON>, kurā jānovērtē sadalījums!ir vesels s<PERSON>, kas norāda sadalījumu raksturojošo brīvības pakāpju skaitu!norāda atgriežamo sadalījuma zaru skaitu: viena zara sadalījums = 1; divu zaru sadalījums = 2"}, "TINV": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; brīvības_pakāpe)", "d": "Atgriež divu zaru apgriezto Stjūdenta t sadalījumu", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas saist<PERSON>ta ar divu zaru Stjūdenta t sadalījumu - skaitlis no 0 līdz 1 ieskaitot!ir vesels pozitī<PERSON> s<PERSON>, kas nor<PERSON>da sadalījumu raksturojošo brīvības pakāpju skaitu"}, "T.DIST": {"a": "(x; brīvī<PERSON>_pakāpe; kumulatīvā)", "d": "Atgriež kreisā zara Stjūdenta t sadalījumu", "ad": "ir skaitliska vērtība, kurā novērtēt sadalījumu!ir vesels s<PERSON>, kas norāda sadalījumu raksturojošo brīvības pakāpju skaitu!ir loģiskā vērtība: kumulatīvajai sadalījuma funkcijai izmantojiet TRUE; varbūtības blīvuma funkcijai izmantojiet FALSE"}, "T.DIST.2T": {"a": "(x; brīv<PERSON><PERSON>_pakāpe)", "d": "Atgriež divu zaru Stjūdenta t sadalījumu", "ad": "ir skaitl<PERSON>a vērt<PERSON>, kurā novērtēt sadalījumu!ir vesel<PERSON> s<PERSON>, kas norāda sadalījumu raksturo<PERSON><PERSON><PERSON> brīvības pakāpju skaitu"}, "T.DIST.RT": {"a": "(x; brīv<PERSON><PERSON>_pakāpe)", "d": "Atgriež labā zara Stjūdenta t sadalījumu", "ad": "ir skaitl<PERSON>a vērt<PERSON>, kurā novērtēt sadalījumu!ir vesel<PERSON> s<PERSON>, kas norāda sadalījumu raksturo<PERSON><PERSON><PERSON> brīvības pakāpju skaitu"}, "T.INV": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; brīvības_pakāpe)", "d": "Atgriež kreisā zara apgriezto Stjūdenta t sadalījumu", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas saist<PERSON>ta ar divu zaru Stjūdenta t sadalījumu - skaitlis no 0 līdz 1 ieskaitot!ir vesels pozitī<PERSON> s<PERSON>, kas nor<PERSON>da sadalījumu raksturojošo brīvības pakāpju skaitu"}, "T.INV.2T": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; brīvības_pakāpe)", "d": "Atgriež divu zaru apgriezto Stjūdenta t sadalījumu", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas saist<PERSON>ta ar divu zaru Stjūdenta t sadalījumu - skaitlis no 0 līdz 1 ieskaitot!ir vesels pozitī<PERSON> s<PERSON>, kas nor<PERSON>da sadalījumu raksturojošo brīvības pakāpju skaitu"}, "T.TEST": {"a": "(masīvs1; masīvs2; zari; tips)", "d": "Atgriež Stjūdenta t testam atbilstošu varbūtību", "ad": "ir pirmā datu kopa!ir otrā datu kopa!norāda atgriežamo sadalījuma zaru skaitu: viena zara sadalījums = 1; divu zaru sadalījums = 2!ir t testa tips: pāra = 1, divu izlašu vienāda dispersija (homoskedastiska) = 2, divu izlašu nevienāda dispersija = 3"}, "TREND": {"a": "(zināmie_y; [zināmie_x]; [jaunie_x]; [konst])", "d": "<PERSON><PERSON><PERSON>jot ma<PERSON>, at<PERSON><PERSON><PERSON> skaitļ<PERSON> l<PERSON>, kas atbilst zināmiem datu punktiem", "ad": "ir tādu y vērtību diapazons vai masīvs, kuras jau ir zināmas relācijā y=mx+b!ir papildu diapazons vai masīvs ar x vērtībām, kuras jau ir zināmas relācijā y=mx+b, masīvs, kurš ir tikpat liels kā Zināmie_y!ir diapazons vai masīvs ar jaunām x vērtībām, kurām funkcijai TREND jāatgriež atbilstošās y vērtības!is loģiskā vērtība: konstante b tiek aprēķināta kā parasti, ja Konst = TRUE vai izlaists; b tiek iestatīts vienāds ar 0, ja Konst = FALSE"}, "TRIMMEAN": {"a": "(masīvs; procents)", "d": "Atgriež datu vērtības kopas iekšēj<PERSON>s da<PERSON> vidējo", "ad": "ir tādu vērtību diapazons vai masīvs, kuras ir apstrād<PERSON><PERSON><PERSON> vidējā iegūša<PERSON>!ir no datu kopas augšdaļas un lejasdaļas izslēdzamu datu punktu daļskaitlis"}, "TTEST": {"a": "(masīvs1; masīvs2; zari; tips)", "d": "Atgriež Stjūdenta t testam atbilstošu varbūtību", "ad": "ir pirmā datu kopa!ir otrā datu kopa!norāda atgriežamo sadalījuma zaru skaitu: viena zara sadalījums = 1; divu zaru sadalījums = 2!ir t testa tips: pāru = 1, divu izlašu vienāda dispersija (homoskedastiska)= 2, divu izlašu nevienāda dispersija = 3"}, "VAR": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina dispersiju, i<PERSON><PERSON><PERSON><PERSON> i<PERSON> (izlas<PERSON> ignorē loģiskās vērtības un tekstu)", "ad": "ir no 1 līdz 255 skaitliskiem argumentiem, kas atbilst populācijas izlasei"}, "VAR.P": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina dispersiju, i<PERSON><PERSON><PERSON><PERSON> visu populāciju (ignorē loģiskās vērtības un tekstu populācijā)", "ad": "ir no 1 līdz 255 skaitliskiem argumentiem, kas atbilst populācijai"}, "VAR.S": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina dispersiju, i<PERSON><PERSON><PERSON><PERSON> i<PERSON> (izlas<PERSON> ignorē loģiskās vērtības un tekstu)", "ad": "ir no 1 līdz 255 skaitliskiem argumentiem, kas atbilst populācijas izlasei"}, "VARA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vēr<PERSON>, i<PERSON><PERSON><PERSON>t loģiskās vērtības un tekstu. Tekstam un loģiskajai vērtībai FALSE ir vērtība 0; loģiskajai vērtībai TRUE ir vērtība 1", "ad": "ir 1 līdz 255 vē<PERSON><PERSON><PERSON>, kuras atbilst populācijas izlasei"}, "VARP": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina dispersiju, i<PERSON><PERSON><PERSON><PERSON> visu populāciju (ignorē loģiskās vērtības un tekstu populācijā)", "ad": "ir no 1 līdz 255 skaitliskiem argumentiem, kas atbilst populācijai"}, "VARPA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "Aprēķina dispersiju, <PERSON><PERSON>ot vērā visu populāciju, arī loģiskās vērtības un tekstu. Tekstam un loģiskajai vērtībai FALSE ir vērtība 0; loģiskajai vērtībai TRUE ir vērtība 1", "ad": "ir 1 līdz 255 argumentu, kuri atbilst populācijai"}, "WEIBULL": {"a": "(x; alfa; beta; kumulatīvā)", "d": "Atgriež Veibula sadalījumu", "ad": "ir v<PERSON><PERSON><PERSON><PERSON>, kur<PERSON> jānovērtē funkcija, tas ir nenegatīvs skaitlis!ir sadalījuma parametrs; tas ir pozitīvs skaitlis!ir sadalījuma parametrs; tas ir pozitīvs skaitlis!ir loģiskā vērtība: kumulatīvajai sadalījuma funkcijai izmantojiet TRUE; varbūtību masas funkcijai izmantojiet FALSE"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kumulatīvā)", "d": "Atgriež Veibula sadalījumu", "ad": "ir v<PERSON><PERSON><PERSON><PERSON>, kur<PERSON> jānovērtē funkcija, tas ir nenegatīvs skaitlis!ir sadalījuma parametrs; tas ir pozitīvs skaitlis!ir sadalījuma parametrs; tas ir pozitīvs skaitlis!ir loģiskā vērtība: kumulatīvajai sadalījuma funkcijai izmantojiet TRUE; varbūtību masas funkcijai izmantojiet FALSE"}, "Z.TEST": {"a": "(masīvs; x; [sigma])", "d": "Atgriež viena zara z testa P vērtību", "ad": "ir datu masīvs vai diapazons, attiecībā pret kuru jāpārbauda X!ir pārbaudāmā vērtība!ir populācijas (zin<PERSON><PERSON><PERSON><PERSON>) standartnovirze. <PERSON><PERSON> <PERSON><PERSON>, tiek izmantota izlases standartnovirze"}, "ZTEST": {"a": "(masīvs; x; [sigma])", "d": "Atgriež viena zara z testa P vērtību", "ad": "ir datu masīvs vai diapazons, attiecībā pret kuru jāpārbauda X!ir pārbaudāmā vērtība!ir populācijas (zin<PERSON><PERSON><PERSON><PERSON>) standartnovirze. <PERSON><PERSON> <PERSON><PERSON>, tiek izmantota izlases standartnovirze"}, "ACCRINT": {"a": "(emisi<PERSON>; pirmie_procenti; apmaksa; likme; nom; biež<PERSON>; [bāze]; [apr_metode])", "d": "<PERSON><PERSON><PERSON><PERSON> tāda vērtspapīra uzkr<PERSON> procentus, par kuru tiek periodiski maksāti procenti.", "ad": "ir vērtspapīra emisijas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra pirmo procentu datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra ikgadējā kupona likme!ir vērtspapīra nominālvērtība!ir kuponu maksājumu skaits gadā!ir lietojamais dienu skaitīšanas pamata tips!ir loģiskā vērtība: no emisijas datuma uzkrātiem procentiem = TRUE vai izlaists; aprēķinam no pēdēja kupona apmaksas datuma = FALSE"}, "ACCRINTM": {"a": "(emisija; apm<PERSON>a; likme; nom; [pamats])", "d": "<PERSON>g<PERSON><PERSON> tāda vērtspapīra uz<PERSON>r<PERSON><PERSON> procentus, kuru <PERSON>, tiek maksāti procenti", "ad": "ir vērtspapīra emisijas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra ikgadējā kupona likme!ir vērtspapīra nominālvērtība!ir lietojamais dienu skaitīša<PERSON> bāzes tips"}, "AMORDEGRC": {"a": "(cena; datums_iegādāts; pirmais_periods; likvidācija; periods; likme; [pamats])", "d": "Atgriež katra grāmatošanas perioda proporcionāli sadalītu lineāru aktīvu amortizāciju.", "ad": "ir aktīvu cena!ir aktīvu iegādes datums!ir pirmā perioda beigu datums!ir aktīva likvidācijas vērtība tā kalpošanas beigās.!ir periods!ir amortizācijas likme!gada_pamats: 0 gadam ar 360 dienām, 1 faktiskais, 3 gadam ar 365 dienām."}, "AMORLINC": {"a": "(cena; datums_iegādāts; pirmais_periods; likvidācija; periods; likme; [pamats])", "d": "Atgriež katra grāmatošanas perioda proporcionāli sadalītu lineāru aktīvu amortizāciju.", "ad": "ir aktīvu cena!ir aktīvu iegādes datums!ir pirmā perioda beigu datums!ir aktīva likvidācijas vērtība tā kalpošanas beigās.!ir periods!ir amortizācijas likme!gada_pamats: 0 gadam ar 360 dienām, 1 faktiskais, 3 gadam ar 365 dienām."}, "COUPDAYBS": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; biežums; [bāze])", "d": "<PERSON><PERSON><PERSON><PERSON> dienu skaitu no kupona perioda sākuma līdz apmaksas datumam", "ad": "ir vērtspap<PERSON>ra a<PERSON> datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir kupona maksājumu skaits gadā!ir lietojamā dienu skait<PERSON> bāze"}, "COUPDAYS": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; biežums; [bāze])", "d": "<PERSON><PERSON><PERSON><PERSON> dienu skaitu k<PERSON>a period<PERSON>, ietverot apm<PERSON>as datumu", "ad": "ir vērtspap<PERSON>ra a<PERSON> datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir kupona maksājumu skaits gadā!ir lietojamā dienu skait<PERSON> bāze"}, "COUPDAYSNC": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; biežums; [bāze])", "d": "<PERSON><PERSON><PERSON><PERSON> dienu skaitu no apmaksas datuma līdz nākamajam kupona datumam", "ad": "ir vērtspap<PERSON>ra a<PERSON> datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir kupona maksājumu skaits gadā!ir lietojamā dienu skait<PERSON> bāze"}, "COUPNCD": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; biežums; [bāze])", "d": "Atgriež nākamo kupona datumu pēc apm<PERSON>as dienas", "ad": "ir vērtspap<PERSON>ra a<PERSON> datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir kupona maksājumu skaits gadā!ir lietojamā dienu skait<PERSON> bāze"}, "COUPNUM": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; biežums; [bāze])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas maksāja<PERSON> no apmaksas datuma līdz d<PERSON><PERSON><PERSON> datumam", "ad": "ir vērtspap<PERSON>ra a<PERSON> datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir kupona maksājumu skaits gadā!ir lietojamā dienu skait<PERSON> bāze"}, "COUPPCD": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; biežums; [bāze])", "d": "Atgriež iepriekšējo kupona datumu pirms apmaksas datuma", "ad": "ir vērtspap<PERSON>ra a<PERSON> datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir kupona maksājumu skaits gadā!ir lietojamā dienu skait<PERSON> bāze"}, "CUMIPMT": {"a": "(likme; skg; pv; sākuma_periods; beigu_periods; tips)", "d": "<PERSON>griež kum<PERSON> procentus, kas maksāti par aizdevumu starp diviem periodiem", "ad": "ir procentu likme!ir kopējais maksājuma periodu skaits!ir pašreizējā vērtība!ir pirmais aprēķina periods!ir pēdējais aprēķina periods!ir noteiktais maksājuma laiks"}, "CUMPRINC": {"a": "(likme; skg; pv; sākuma_periods; beigu_periods; tips)", "d": "<PERSON>g<PERSON>ž kumulat<PERSON><PERSON>, kas maksāta par aizdevumu starp diviem periodiem", "ad": "ir procentu likme!ir kopējais maksājuma periodu skaits!ir pašreizējā vērtība!ir pirmais aprēķina periods!ir pēdējais aprēķina periods!ir noteiktais maksājuma laiks"}, "DB": {"a": "(vērt<PERSON>ba; likvid<PERSON><PERSON><PERSON>_vērt; kal<PERSON><PERSON><PERSON>_laiks; periods; [mēnesis])", "d": "Atgriež aktīvu amortizāciju noteiktā periodā, izman<PERSON>jot aritmētiski degresīvo nolietojuma aprēķināšanas metodi", "ad": "ir sākotnējās aktīvu izmaksas!ir likvidācijas vērtība aktīvu kalpošanas laika beigās!ir periodu skaits, kuru laikā aktīvi tiek amortizēti (to dēvē arī par aktīvu lietderīgo kalpošanas laiku)!ir periods, kurā amortizācija ir jāaprēķina. Periods jānorāda tādās pašās vienībās kā kalpošanas laiks!ir pirmā gada mēnešu skaits. Ja mēnesis ir izlaists, tiek pieņ<PERSON>, ka skaits ir 12"}, "DDB": {"a": "(vērt<PERSON><PERSON>; likvid<PERSON><PERSON><PERSON>_vērt; kal<PERSON><PERSON><PERSON>_laiks; periods; [koeficients])", "d": "Atgriež aktīvu amortizāciju norādītā periodā, izmantojot ģeometriski degresīvo nolietojuma aprēķināšanas metodi vai kādu citu norādīto metodi", "ad": "ir aktīvu sākotnējā vērtība!ir likvidācijas vērtība aktīvu kalpošanas laika beigās!ir periodu skaits, kuru laikā aktīvi tiek amortizēti (to dēvē arī par aktīvu lietderīgo kalpošanas laiku)!ir periods, kas jānorāda tādās pašās vienībās kā kalpošanas laiks!ir nolietojuma aprēķināšanas koeficients. Ja Koeficients ir izlaists, pieņ<PERSON>, ka tas ir 2 (ģeometriski degresīvā nolietojuma aprēķināšanas metode)"}, "DISC": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; c; iz<PERSON><PERSON><PERSON><PERSON>; [bāze])", "d": "Atgriež vērtspapīra diskonta likmi", "ad": "ir vērtspap<PERSON>ra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra cena par 100 EUR nominālvērtību!ir vērtspapīra izpirkšanas vērtība par 100 EUR nominālvērtību!ir lietojamais dienu skaitīšanas bāzes tips"}, "DOLLARDE": {"a": "(daļa_do<PERSON><PERSON><PERSON>; daļa)", "d": "<PERSON><PERSON><PERSON><PERSON> cenu <PERSON>, kas izte<PERSON>ta kā da<PERSON>a, do<PERSON><PERSON><PERSON> cen<PERSON>, kas izteikta kā deci<PERSON>", "ad": "ir s<PERSON><PERSON><PERSON>, kas izteikts kā daļskaitlis!ir vesels skaitl<PERSON>, ko lieto kā daļskaitļa saucēju"}, "DOLLARFR": {"a": "(decim<PERSON><PERSON><PERSON><PERSON><PERSON>_dolārs; daļa)", "d": "<PERSON><PERSON><PERSON><PERSON> cenu <PERSON>, kas izte<PERSON>ta kā de<PERSON>, do<PERSON><PERSON><PERSON> cen<PERSON>, kas izteikta kā <PERSON>", "ad": "ir decim<PERSON>lskaitl<PERSON>!ir vesels s<PERSON>, ko lieto kā daļskaitļa saucēju"}, "DURATION": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; kupons; peļņa; biež<PERSON>; [bāze])", "d": "Atgriež vērtspapīra ikgadējo ilgumu ar periodiskiem procentu maksājumiem", "ad": "ir vērtspapīra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra ikgadējā kupona likme!ir vērtspapīra ikgadējā peļņa!ir kuponu maksājumu skaits gadā!ir lietojamais dienu skaitīšanas bāzes tips"}, "EFFECT": {"a": "(nomināls_likme; skg)", "d": "Atgriež faktisko ikgadējo procentu likmi", "ad": "ir nomin<PERSON><PERSON>ā procentu likme!ir salikto periodu skaits gadā"}, "FV": {"a": "(likme; per_sk; maks; [nv]; [tips])", "d": "<PERSON><PERSON><PERSON><PERSON> tādas investīcijas nākotnes v<PERSON>rt<PERSON>, k<PERSON><PERSON> pama<PERSON> ir periodiski, konstanti maksājumi un konstanta procentu likme", "ad": "ir procentu likme periodā. <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> 6%/4, ja tiek veikti ikceturkšņa maksājumi ar ikgadējo procentu likmi (APR) 6%!ir investīcijas maksājuma periodu kopskaits!ir maks<PERSON><PERSON><PERSON>, kas tiek veikts katru periodu; to nevar mainīt visa investīcijas perioda laikā!ir pašreizējā vērtība jeb pilnā summa, ko šobrīd ir vērta nākotnē veicamu maksājumu virkne. Ja izlaists, Nv = 0!ir vē<PERSON><PERSON><PERSON>, kas apzīmē maksājuma laiku: maksājums perioda sākumā = 1; maksājums perioda beigās = 0 vai izlaists"}, "FVSCHEDULE": {"a": "(pamatsumma; grafiks)", "d": "Atgriež sākotnēj<PERSON>s pamatsummas nākotnes vērtību pēc salikto procentu likmju sērijas lie<PERSON>", "ad": "ir pašreizējā vērtība! ir lietojamo procenta likmju masīvs"}, "INTRATE": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; invest<PERSON><PERSON><PERSON>; iz<PERSON><PERSON>; [bāze])", "d": "Atgriež pilnībā investēta vērtspapīra procentu likmi", "ad": "ir vērtspap<PERSON>ra apm<PERSON> datums, kas izteikts kā datumu sērijas numurs!ir vērtspapīra dzēša<PERSON> datums, kas izteikts kā datumu sērijas numurs!ir vērtspapīrā investētā summa!ir dzēšanas datumā saņemamā summa!ir lietojamais dienu skaitīša<PERSON> bāzes tips"}, "IPMT": {"a": "(likme; per; per_sk; pv; [nv]; [tips])", "d": "Atgriež noteikta perioda procentu maksāju<PERSON> par invest<PERSON>, ja tiek veikti periodiski, konstanti maksājumi un ir konstanta procentu likme", "ad": "ir procentu likme vienā periodā. <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON>jiet 6%/4, ja tiek veikti ikceturkšņa maksājumi ar ikgadējo procentu likmi (APR) 6%!ir periods, kur procenti jānoskaidro; tam jāb<PERSON>t robežās no 1 līdz Per_sk!ir maksājumu periodu kopskaits par investīciju!ir pašreizējā vērtība jeb pilnā summa, ko šobrīd ir vērta nākotnē veicamu maksājumu virkne!ir nākotnes vērtība jeb naudas summa, kuru plāno iegūt pēc pēdējā maksājuma veikšanas. Ja izlaists, F = 0!ir loģiskā vērtība, kas apzīmē maksājuma laikus: perioda beigās = 0 vai izlaists, perioda sākumā = 1"}, "IRR": {"a": "(vē<PERSON><PERSON><PERSON>; [minējums])", "d": "Atgriež iekšējo ienākumu normu (internal rate of return, IRR) sērijai naudas plūsmas", "ad": "ir masīvs vai atsauce uz <PERSON>, kur<PERSON><PERSON>t<PERSON>, kam jāaprēķina iekšējā ienākumu norma!ir s<PERSON><PERSON><PERSON>, kas tiek minēts un ir tuvu IRR; 0,1 (10 procenti) ja izlaists"}, "ISPMT": {"a": "(likme; per; persk; pv)", "d": "<PERSON><PERSON><PERSON><PERSON> procentus, kas jā<PERSON><PERSON>ā noteiktā investī<PERSON> periodā", "ad": "procentu likme vienā periodā. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 6%/4, ja tiek veikti ikceturkšņa maksājumi ar ikgadējo procentu likmi (APR) 6%!periods, kuram jāaprēķina procenti!maksājuma periodu skaits investīcijas laikā!Ko<PERSON><PERSON><PERSON><PERSON> naudas summa, cik šobrīd ir vērta virkne turpmāk veicamo maksājumu"}, "MDURATION": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; kupons; peļņa; biež<PERSON>; [pamats])", "d": "Atgriež Makaolija modificēto vērtspapīra ilgumu ar 100 EUR pieņemtu nominālvērtību", "ad": "ir vērtspapīra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra ikgadējā kupona likme!ir vērtspapīra ikgadējā peļņa!ir kuponu maksājumu skaits gadā!ir lietojamais dienu skaitīšanas pamata tips"}, "MIRR": {"a": "(v<PERSON><PERSON><PERSON><PERSON>; finansiālā_likme; p<PERSON><PERSON><PERSON>t_likme)", "d": "Atgriež iekšējo ienākumu normu (internal rate of return) sērijai periodisku naudas plūsm<PERSON>, <PERSON><PERSON>ot vērā gan investīcijas i<PERSON>, gan procentus par naudas pārinvestē<PERSON>", "ad": "ir masīvs vai atsauce uz <PERSON>, kur<PERSON><PERSON>, kas apzī<PERSON><PERSON> maksāju<PERSON> (negatīvi) un ienākumu (pozitīvi) sēriju regulāros intervālos!ir procentu likme, kas tiek maksāta par naudas plūsmā izmantoto naudu!ir procentu likme, ko saņem par naudas pārinvestēšanu"}, "NOMINAL": {"a": "(faktiskā_likme; skg)", "d": "Atgriež ikgadējo nominālo procentu likmi", "ad": "ir faktiskā procentu likme!ir salikto periodu skaits gadā"}, "NPER": {"a": "(likme; maks; pv; [nv]; [tips])", "d": "Atgriež investīci<PERSON> periodu skaitu, ja tiek veikti periodiski, konstanti maksājumi un ir konstanta procentu likme", "ad": "ir procentu likme par vienu periodu. <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> 6%/4, ja tiek veikti ikceturkšņa maksājumi ar ikgadējo procentu likmi (APR) 6%!ir maks<PERSON><PERSON><PERSON>, kas tiek veikts katru periodu; tas netiek mainīts visa investīcijas perioda laikā!ir pašreizējā vērtība jeb pilnā summa, ko šobrīd ir vērta nākotnē veicamu maksājumu virkne!ir nākotnes vērtība jeb naudas summa, kuru plāno iegūt pēc pēdējā maksājuma veikšanas. Ja izlaists, tiek izmantota nulle!ir loģiskā vērtība: maksājums perioda sākumā = 1; maksājums perioda beigās = 0 vai izlaists"}, "NPV": {"a": "(likme; vērtība1; [vērtība2]; ...)", "d": "Atgriež investīcijas pašreizējo neto vērtību, i<PERSON><PERSON><PERSON><PERSON> diskonta likmi un turpmāku maksājumu virkni (negatīvas vērtības) un ienākumus (pozitīvas vērtības)", "ad": "ir diskonta likme viena perioda garumā!ir 1 līdz 254 maksājumu un ienākumu, kas ir vienmēr<PERSON>gi sadalīti laikā un tiek veikti katra perioda beigās"}, "ODDFPRICE": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; emisija; pir<PERSON><PERSON>_kupons; likme; peļ<PERSON>a; iz<PERSON><PERSON>ša<PERSON>; bie<PERSON><PERSON>; [bāze])", "d": "Atgriež tāda vērtspapīra cenu par 100 EUR nominālvērt<PERSON>bu, kura pirmais periods ir nepāra", "ad": "ir vērtspapīra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra emisijas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra pirmā kupona datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra procentu likme!ir vērtspapīra ikgadējā peļņa!ir vērtspapīra izpirkšanas vērtība par 100 EUR nominālvērtību!ir kupona maksājumu skaits gadā!ir lietojamais dienu skaitīšanas bāzes tips"}, "ODDFYIELD": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; emisija; pir<PERSON><PERSON>_kupons; likme; pr; izpi<PERSON>ša<PERSON>; bie<PERSON><PERSON>; [bāze])", "d": "Atgriež tāda vērtspapī<PERSON> p<PERSON>, kura pirmais periods ir nepāra", "ad": "ir vērtspapīra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra emisijas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra pirmā kupona datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra procentu likme!ir vērtspapīra cena!ir vērtspapīra izpirkšanas vērtība par 100 EUR nominālvērtību!ir kupona maksājumu skaits gadā!ir lietojamais dienu skaitīšanas bāzes tips"}, "ODDLPRICE": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; pēd<PERSON><PERSON>e_procenti; likme; pe<PERSON><PERSON>a; iz<PERSON><PERSON><PERSON>; bie<PERSON><PERSON>; [bāze])", "d": "Atgriež tāda vērtspapīra cenu uz 100 EUR nominālvērtību, kura pēdē<PERSON>is periods ir nepāra", "ad": "ir vērtspapīra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra pēdējā kupona datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra procentu likme!ir vērtspapīra ikgadējā peļņa!ir vērtspapīra izpirkšanas vērtība par 100 EUR nominālvērtību!ir kupona maksājumu skaits gadā!ir lietojamais dienu skaitīšanas bāzes tips"}, "ODDLYIELD": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; pēd<PERSON><PERSON>e_procenti; likme; pr; iz<PERSON><PERSON><PERSON><PERSON>; bie<PERSON><PERSON>; [bāze])", "d": "Atgriež tāda vērtspapīra cenu, kura pēd<PERSON><PERSON> periods ir nepāra", "ad": "ir vērtspapīra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra pēdējā kupona datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra procentu likme!ir vērtspapīra cena!ir vērtspapīra izpirkšanas vērtība par 100 EUR nominālvērtību!ir kupona maksājumu skaits gadā!ir lietojamais dienu skait<PERSON> bāzes tips"}, "PDURATION": {"a": "(likme; pv; nv)", "d": "<PERSON>g<PERSON><PERSON>u s<PERSON>tu, kuru laikā ieguldījums sasniedz noteiktu vērtību", "ad": "ir procentu likme periodā.!ir ieguldījuma pašreizējā vērtība!ir ieguldījuma vēlamā nākotnes vērtība"}, "PMT": {"a": "(likme; persk; pv; [nv]; [tips])", "d": "Aprēķina a<PERSON><PERSON><PERSON><PERSON>, ja tiek veikti konstanti maksājumi ar konstantu procentu likmi", "ad": "ir aizdevuma procentu likme vienā periodā. <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> 6%/4, ja tiek veikti ikceturkšņa maksājumi ar ikgadējo procentu likmi (APR) 6%!ir maks<PERSON><PERSON><PERSON> kops<PERSON>, kas jāveic par aizdevumu!ir pašreizējā vērtība: kops<PERSON><PERSON>, kuru šobrīd ir vērta nākotnē veicamu maksājumu virkne!ir nākotnes vērtība jeb naudas summa, kuru plānots iegūt pēc pēdējā maksājuma veikša<PERSON>, 0 (nulle) ja izlaists!ir loģiskā vērtība: maksājums perioda sākumā = 1; maksājums perioda beigās = 0 vai izlaists"}, "PPMT": {"a": "(likme; per; per_sk; pv; [nv]; [tips])", "d": "Atgriež noteiktas investīcijas pamatsummas ma<PERSON><PERSON><PERSON>, ja tiek veikti periodisk<PERSON>, konstanti maksā<PERSON><PERSON> un ir konstanta procentu likme", "ad": "ir procentu likme vienā periodā. <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> 6%/4, ja tiek veikti ikceturkšņa maksājumi ar ikgadējo procentu likmi (APR) 6%!norāda periodu un tam jābūt robežās no 1 līdz Per_sk!ir maksājuma periodu kopskaits par investīciju!ir pašreizējā vērtība ir pašreizējā vērtība: kopējā summa, ko šobrīd ir vērta nākotnē veicamu maksājumu virkne!ir nākotnes vērtība jeb naudas summa, kuru plāno iegūt pēc pēdējā maksājuma veikšanas!ir loģiskā vērtība: maksājums perioda sākumā = 1; maksājums perioda beigās = 0 vai izlaists"}, "PRICE": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; likme; peļ<PERSON>a; iz<PERSON><PERSON><PERSON>; bie<PERSON><PERSON>; [bāze])", "d": "Atg<PERSON>ž vērtspapīra cenu par 100 EUR nominālvē<PERSON><PERSON><PERSON>, ma<PERSON><PERSON><PERSON><PERSON> periodiskus procentus", "ad": "ir vērtspapīra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra ikgadējā kupona likme!ir vērtspapīra ikgadējā peļņa!ir vērtspapīra izpirkšanas vērtība par 100 EUR nominālvērtību!ir kupona maksājumu skaits gadā!ir lietojamais dienu skaitīšanas bāzes tips"}, "PRICEDISC": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; diskonts; izpi<PERSON><PERSON>na; [bāze])", "d": "Atgriež diskontēta vērtspapīra cenu par 100 EUR nominālvērtību", "ad": "ir vērtspapīra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra diskonta likme!ir vērtspapīra izpirkšanas vērtība par 100 EUR nominālvērtību!ir lietojamais dienu skaitīšanas bāzes tips"}, "PRICEMAT": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; emisija; likme; peļņa; [bāze])", "d": "Atgriež cenu vērtspapīram par 100 EUR nominālvērtību, par kuru procentus maksā d<PERSON>ot", "ad": "ir vērtspapīra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra emisijas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra procentu likme emisijas datumā!ir vērtspapīra ikgadējā peļņa!ir lietojamais dienu skaitīšanas bāzes tips"}, "PV": {"a": "(likme; per_sk; maks; [nv]; [tips])", "d": "Atgriež investīcijas pašreizējo vērtību: turpmāk veicamu maksājumu virknes vērtības kopsumma šobrīd", "ad": "ir procentu likme vienā periodā. <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> 6%/4, ja tiek veikti ikceturkšņa maksājumi ar ikgadējo procentu likmi (APR) 6%!ir investīcijas maksājuma periodu skaits!ir maks<PERSON><PERSON><PERSON>, kas tiek veikts katru periodu un kas nemainās visa investīcijas perioda laikā!ir nākotnes vērtība jeb naudas summa, kuru plāno iegūt pēc pēdējā maksājuma veikšanas!ir loģiskā vērtība: maksājums perioda sākumā = 1; maksājums perioda beigās = 0 vai izlaists"}, "RATE": {"a": "(per_sk; maks; pv; [nv]; [tips]; [minējums])", "d": "Atgriež viena perioda procentu likmi aizdevuma vai investīcijas perioda laikā. <PERSON><PERSON><PERSON><PERSON>, <PERSON>zman<PERSON>jiet 6%/4, ja tiek veikti ikceturkšņa maksājumi ar ikgadējo procentu likmi (APR) 6%", "ad": "ir maksājuma periodu kopskaits par aizdevumu vai investīciju!ir maksājums, kas tiek veikts katru periodu un kas nemainās visa aizdevuma vai investīcijas perioda laikā!ir pašreizējā vērtība: piln<PERSON> summa, ko šobrīd ir vērta nākotnē veicamu maksājumu virkne!ir nākotnes vērtība jeb naudas summa, kuru plānots iegūt pēc pēdējā maksājuma veikšanas. Ja izlaista, izmanto Nv = 0!ir loģiskā vērtība: maksājums perioda sākumā = 1; maksājums perioda beigās = 0 vai izlaista!ir minējums par to, kāda būs likme; ja izlaists, Minējums = 0,1 (10 procenti)"}, "RECEIVED": {"a": "(a<PERSON><PERSON>a; dz<PERSON><PERSON><PERSON>_datums; investīcijas; diskonts; [bāze])", "d": "Atgriež pilnībā investēta vērtspapīra dzēša<PERSON> datumā saņemamo summu", "ad": "ir vērtspapīra apm<PERSON> datums, kas izteikts kā datumu sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datumu sērijas numurs!ir vērtspapīrā investētā summa!ir vērtspapīra diskonta likme!ir lietojamais dienu skait<PERSON> bāzes tips"}, "RRI": {"a": "(per_sk; pv; nv)", "d": "Atgriež ieguldījuma pieauguma procentu likmes ekvivalentu", "ad": "ir ieguldījuma periodu skaits!ir ieguldījuma pašreizējā vērtība!ir ieguldījuma nākotnes vērtība"}, "SLN": {"a": "(vērt<PERSON><PERSON>; lik<PERSON><PERSON><PERSON><PERSON>_vērt; ka<PERSON><PERSON><PERSON><PERSON>_laiks)", "d": "Atgriež aktīvu lineāro amortizāciju vienā periodā", "ad": "ir sākotnējās aktīvu izmaksas!ir likvidācijas vērtība aktīvu kalpošanas laika beigās!ir periodu skaits, kuru laikā aktīvi tiek amortizēti (to dēvē arī par aktīvu lietderīgo kalpošanas laiku)"}, "SYD": {"a": "(vērt<PERSON><PERSON>; lik<PERSON><PERSON><PERSON><PERSON>_vērt; kal<PERSON><PERSON><PERSON>_laiks; per)", "d": "Atgriež aktīvu amortizāciju noteiktā periodā, aprēķinot ar gada ciparu summas metodi", "ad": "ir ir sākotnējās aktīvu izmaksas!ir likvidācijas vērtība aktīvu kalpošanas laika beigās!ir periodu skaits, kuru laikā aktīvi tiek amortizēti (to dēvē arī par aktīvu lietderīgo kalpošanas laiku)!ir periods, kas jānorāda tādās pašās vienībās kā kalpošanas laiks"}, "TBILLEQ": {"a": "(a<PERSON><PERSON>a; dz<PERSON><PERSON><PERSON>_datums; diskonts)", "d": "Atgriež valsts kases vekseļ<PERSON> p<PERSON>, kas ekvi<PERSON>a obligācijām", "ad": "ir valsts kases vekseļa apmaksas datums, kas izteikts kā datuma sērijas numurs!ir valsts kases vekseļa dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir valsts kases vekseļa diskonta likme"}, "TBILLPRICE": {"a": "(a<PERSON><PERSON>a; dz<PERSON><PERSON><PERSON>_datums; diskonts)", "d": "Atgriež valsts kases vekseļa cenu par 100 ASV dolāru nominālvērtību", "ad": "ir valsts kases vekseļa apmaksas datums, kas izteikts kā datuma sērijas numurs!ir valsts kases vekseļa dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir valsts kases vekseļa diskonta likme"}, "TBILLYIELD": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; pr)", "d": "Atgriež valsts kases vekseļa peļņu", "ad": "ir valsts kases vekseļa apmaksas datums, kas izteikts kā datuma sērijas numurs!ir valsts kases vekseļa dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir valsts kases vekseļa cena par 100 ASV dolāru nominālvērtību"}, "VDB": {"a": "(vērt<PERSON>ba; likvid<PERSON><PERSON><PERSON>_vērt; kal<PERSON><PERSON><PERSON>_laiks; sāk_periods; beigu_periods; [koeficients]; [nav_pārslēgš])", "d": "Atgriež aktīvu amortizāciju jebkurā norādītā periodā, ar<PERSON> periodos, izmantojot ģeometriski degresīvo nolietojuma aprēķināšanas metodi vai kādu citu norādītu metodi", "ad": "ir aktīvu sākotnējā vērtība!ir likvidācijas vērtība aktīvu kalpošanas laika beigās!ir periodu skaits, kuru laikā aktīvi tiek amortizēti (to dēvē arī par aktīvu lietderīgo kalpošanas laiku)!ir sākuma periods, kam jāaprēķina amortizācija, norādot tādās pašās vienībās kā kalpošanas laiks!ir beigu periods, kam jāaprēķina amortizācija, norādot tādās pašās vienībās kā kalpošanas laiks!ir nolietojuma aprēķināšanas koeficients, ja izlaists, tas tiek uzskatīts par 2 (ģeometriski degresīvā nolietojuma aprēķināšanas metode)!pārslēd<PERSON> uz lineārās amortizācijas metodi, ja amortizācija ir lielāka nekā sarūkošā vērtība = FALSE vai izlaists; nepārslēdzas = TRUE"}, "XIRR": {"a": "(vē<PERSON><PERSON><PERSON>; datumi; [minējums])", "d": "Atgrie<PERSON> nauda<PERSON> plū<PERSON> grafika iekšējo i<PERSON>sīguma normu", "ad": "ir naudas plūsmu sērija, kas atbilst maksājumu grafikam datumos!ir maksājumu datumu grafiks, kas atbilst naudas plūsmas maksājumiem!ir <PERSON><PERSON><PERSON><PERSON>, kur<PERSON> varētu būt tuvs funkcijas XIRR rezultātam"}, "XNPV": {"a": "(likme; vērt<PERSON>ba; datumi)", "d": "Atgriež naudas plūsmas grafika pašreizējo neto vērtību", "ad": "ir disk<PERSON>a lik<PERSON>, ko lieto naudas plūsmai!ir naudas plūsma<PERSON> s<PERSON>, kas attiecas uz maksājumu grafiku datumos!ir maksājumu datumu grafiks, kas attiecas uz naudas plūsmas maksāju<PERSON>m"}, "YIELD": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; likme; c; iz<PERSON><PERSON>; bie<PERSON><PERSON>; [bāze])", "d": "<PERSON><PERSON><PERSON><PERSON>, ko devis vērtspapīrs ar periodisku procentu izmaksu", "ad": "ir vērtspap<PERSON>ra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra ikgadējā kupona likme!ir vērtspapīra cena par 100 EUR nominālvērtību!ir vērtspapīra izpirkšanas vērtība par 100 EUR nominālvērtību!ir kupona maksājumu skaits gadā!ir lietojamais dienu skaitīša<PERSON> bāzes tips"}, "YIELDDISC": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; c; iz<PERSON><PERSON><PERSON><PERSON>; [bāze])", "d": "Atgriež diskontēta vērtspapīra ikgadējo peļņu. <PERSON><PERSON><PERSON><PERSON>, valsts kases vekselis", "ad": "ir vērtspap<PERSON>ra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra cena par 100 EUR nominālvērtību!ir vērtspapīra izpirkšanas vērtība par 100 EUR nominālvērtību!ir lietojamais dienu skaitīšanas bāzes tips"}, "YIELDMAT": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; emisija; likme; c; [bāze])", "d": "<PERSON>g<PERSON>ž ikgad<PERSON><PERSON> vē<PERSON>pap<PERSON> p<PERSON>, par kuru procentus maksā <PERSON>", "ad": "ir vērtspapīra apmaksas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra dzēšanas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra emisijas datums, kas izteikts kā datuma sērijas numurs!ir vērtspapīra procentu likme emisijas datumā!ir vērtspapīra cena par 100 EUR nominālvērtību!ir lietojamais dienu skaitīšanas bāzes tips"}, "ABS": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa absolūto vērtību - skaitli bez zīmes", "ad": "ir <PERSON><PERSON><PERSON><PERSON>, kam i<PERSON><PERSON><PERSON><PERSON> absolūtā vērtība"}, "ACOS": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa arkkosinusu radiānos diapazonā no 0 līdz Pi. Arkkosinuss ir leņķis, kura kosinus<PERSON> ir S<PERSON>tl<PERSON>", "ad": "ir vaja<PERSON><PERSON><PERSON><PERSON><PERSON> leņķa kosinuss; tam jāb<PERSON>t no -1 līdz 1"}, "ACOSH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa apgriezto hiperbolisko kosinusu", "ad": "ir j<PERSON><PERSON><PERSON><PERSON>, kas vien<PERSON><PERSON> vai liel<PERSON>ks par 1"}, "ACOT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa arkkotangensu radiānos diapazonā no 0 līdz Pi.", "ad": "ir vajadz<PERSON>g<PERSON> leņķa kotangenss"}, "ACOTH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa inverso hiperbolisko kotangensu", "ad": "ir vajadzīgā leņķa hiperboliskais kotangenss"}, "AGGREGATE": {"a": "(funkcijas_num; opcijas; ats1; ...)", "d": "Atgriež apkopojumu kā sarakstu vai datu bāzi", "ad": "ir skaitlis no 1 līdz 19, kas nor<PERSON>da apkopojuma kopsavilkuma funkciju.!ir skaitlis no 0 līdz 7, kas norāda ignorēja<PERSON><PERSON><PERSON> vērt<PERSON>, kuras attiecībā uz apkopojumu ir jāignorē!ir masīvs vai skaitlisko datu diapazons, kurā aprēķināt apkopojumu!norāda pozīciju masīvā; ir k-tais lielākais, k-tais maz<PERSON>, k-tā procentile vai k-tā kvartile.!ir skaitlis no 1 līdz 19, kas norāda apkopojuma kopsavilkuma funkciju.!ir skaitlis no 0 līdz 7, kas norāda vērtības, kuras jāignor<PERSON> attiecībā uz apkopojumu!ir no 1 līdz 253 diapazoniem vai atsaucēm, kurām jāiegūst apkopojums"}, "ARABIC": {"a": "(teksts)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> romi<PERSON>u ciparu par arābu ciparu", "ad": "ir romi<PERSON><PERSON><PERSON> cipars, kas j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ASC": {"a": "(teksts)", "d": "Dubultbaitu r<PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) valodām funkcija nomaina pilna platuma (dubultbaitu) rakstzī<PERSON> ar <PERSON> (vienbaita) rakstzīmēm", "ad": "ir te<PERSON><PERSON>, kas j<PERSON><PERSON><PERSON>"}, "ASIN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa arksīnusu radiānos diapazonā no -Pi/2 līdz Pi/2", "ad": "ir vaja<PERSON><PERSON><PERSON><PERSON><PERSON> leņķa sinuss; tam jābūt no -1 līdz 1"}, "ASINH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa apgriezto hiperbolisko sinusu", "ad": "ir j<PERSON><PERSON><PERSON><PERSON>, kas vien<PERSON><PERSON> vai liel<PERSON>ks par 1"}, "ATAN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> skai<PERSON><PERSON><PERSON> r<PERSON>, diapazonā no -Pi/2 līdz Pi/2", "ad": "ir vajad<PERSON><PERSON>gais leņķa tangenss"}, "ATAN2": {"a": "(x_num; y_num)", "d": "Atgriež <PERSON>d<PERSON> x un y koordinātu arktangensu radiānos no -Pi un Pi, izslēdzot -Pi", "ad": "ir punkta x koordināta!ir punkta y koordināta"}, "ATANH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa apgriezto hiperbolisko tangensu", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skaitlis starp -1 un 1, izslēdzot -1 un 1"}, "BASE": {"a": "(skaitl<PERSON>; bāze; [min_garums])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skaitli par teksta atveidojumu ar doto bāzi", "ad": "ir skai<PERSON><PERSON>, kas jāpārvēr<PERSON>!ir b<PERSON><PERSON>, par kuru skaitlis jāpārvērš!ir atgrieztās virknes minimālais garums, ja netiek pievienotas izlaistās nulles pirms"}, "CEILING": {"a": "(skaitl<PERSON>; būtisku<PERSON>)", "d": "Noapaļo skaitli uz augšu līdz tuvāka<PERSON>m bū<PERSON><PERSON><PERSON><PERSON> skai<PERSON>, kas dal<PERSON><PERSON> bez at<PERSON>", "ad": "ir no<PERSON><PERSON>ojam<PERSON> vērtība!ir <PERSON><PERSON><PERSON><PERSON>, kas dal<PERSON><PERSON> be<PERSON>, līdz kuram j<PERSON>o"}, "CEILING.MATH": {"a": "(skaitlis; [būtisku<PERSON>]; [rež<PERSON><PERSON>])", "d": "Noapaļo skaitli uz augšu līdz tuvākajam veselajam skaitlim vai līdz tuvākajam nozīmīgajam skaitlim, kas dal<PERSON><PERSON> bez at<PERSON>uma", "ad": "ir noapaļojamā vērtība!ir <PERSON><PERSON><PERSON><PERSON>, kas da<PERSON><PERSON><PERSON> be<PERSON> at<PERSON>, līdz kuram jānoa<PERSON>!ja dota un ja nav nulle, šī funkcija noapaļos uz augšu no nulles"}, "CEILING.PRECISE": {"a": "(skaitl<PERSON>; [būtisku<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas noapa<PERSON>ots līdz tuvākajam veselajam skaitlim vai tuvākajam būtis<PERSON><PERSON><PERSON> skaitlim, kas da<PERSON><PERSON><PERSON> bez at<PERSON>", "ad": "ir no<PERSON><PERSON>ojam<PERSON> vērtība!ir <PERSON><PERSON><PERSON><PERSON>, kas dal<PERSON><PERSON> be<PERSON>, līdz kuram j<PERSON>o"}, "COMBIN": {"a": "(skaitlis; izvēlētais_skaitlis)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vienumu skaita kombin<PERSON><PERSON><PERSON> skaitu", "ad": "ir vienumu kopējais skaits!ir vienumu skaits katrā kombin<PERSON>jā"}, "COMBINA": {"a": "(skaitlis; izvēlētais_skaitlis)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vienumu skaita kombin<PERSON><PERSON><PERSON> skaitu", "ad": "ir vienumu kopējais skaits!ir vienumu skaits katrā kombin<PERSON>jā"}, "COS": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež leņķa kosinusu", "ad": "ir leņķis r<PERSON><PERSON>, kam j<PERSON><PERSON>st kos<PERSON>s"}, "COSH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa hiperbolisko kosinusu", "ad": "ir j<PERSON><PERSON><PERSON><PERSON>"}, "COT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "atgriež leņķa kotangensu", "ad": "ir leņķis r<PERSON><PERSON><PERSON>, kam aprēķināms kotangenss"}, "COTH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "atgriež skaitļa hiperbolisko kotangensu", "ad": "ir leņķis r<PERSON><PERSON><PERSON>, kam aprēķināms hiperboliskais kotangenss"}, "CSC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "atgriež leņķa kosekansu", "ad": "ir leņķis r<PERSON><PERSON><PERSON>, kam aprēķināms kosekanss"}, "CSCH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "atgriež leņķa hiperbolisko kosekansu", "ad": "ir leņķis r<PERSON><PERSON><PERSON>, kam aprēķināms hiperboliskais kosekanss"}, "DECIMAL": {"a": "(skai<PERSON><PERSON>; bāze)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tļa teksta atveidojumu dotā bāzē par decimālskaitli", "ad": "ir <PERSON><PERSON><PERSON><PERSON>, kas jāpārvēr<PERSON>!ir tā skaitļa bāze, kuru pārv<PERSON><PERSON><PERSON>t"}, "DEGREES": {"a": "(leņķis)", "d": "<PERSON><PERSON><PERSON><PERSON> radiānus par grādiem", "ad": "ir kon<PERSON><PERSON><PERSON> leņķis radi<PERSON>os"}, "ECMA.CEILING": {"a": "(skaitl<PERSON>; būtisku<PERSON>)", "d": "Noapaļo skaitli uz augšu līdz tuvāka<PERSON>m bū<PERSON><PERSON><PERSON><PERSON> skai<PERSON>, kas dal<PERSON><PERSON> bez at<PERSON>", "ad": "ir no<PERSON><PERSON>ojam<PERSON> vērtība!ir <PERSON><PERSON><PERSON><PERSON>, kas dal<PERSON><PERSON> be<PERSON>, līdz kuram j<PERSON>o"}, "EVEN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Noapaļo pozitīvu skaitli uz augšu, bet negatīvu - uz leju līdz tuvākajam veselajam pārskaitlim", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vērt<PERSON>ba"}, "EXP": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> e, kas kāpin<PERSON>ts norādītajā pakāpē", "ad": "ir bāzes e kāpinātājs. Nemainīgais ir vienāds ar 2,71828182845904 - natur<PERSON><PERSON><PERSON> logaritma bāzi"}, "FACT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON>, kas vien<PERSON> ar 1*2*3*...* <PERSON><PERSON><PERSON><PERSON>", "ad": "ir ne<PERSON><PERSON><PERSON><PERSON><PERSON>, kam j<PERSON><PERSON><PERSON><PERSON> faktor<PERSON>"}, "FACTDOUBLE": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa dubulto faktori<PERSON>", "ad": "ir <PERSON><PERSON><PERSON><PERSON><PERSON>, kurai atgriežams dubultais faktoriāls"}, "FLOOR": {"a": "(skaitl<PERSON>; būtisku<PERSON>)", "d": "Noapaļo skaitli uz leju līdz tuvākajam būtis<PERSON><PERSON><PERSON> skaitlim, kas dal<PERSON><PERSON> bez at<PERSON>", "ad": "ir skaitl<PERSON><PERSON> v<PERSON>, kura jānoa<PERSON>!ir s<PERSON><PERSON><PERSON>, kas da<PERSON><PERSON><PERSON> be<PERSON>, līdz kuram jānoapa<PERSON>o. <PERSON><PERSON> <PERSON>, gan bū<PERSON><PERSON><PERSON>m ir jābūt vai nu pozitīvam, vai negatīvam"}, "FLOOR.PRECISE": {"a": "(skaitl<PERSON>; [būtisku<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas noapa<PERSON> uz leju līdz tuvākajam veselajam skaitlim vai tuvākajam būtiska<PERSON>m skaitlim, kas da<PERSON><PERSON><PERSON> bez at<PERSON>", "ad": "ir no<PERSON><PERSON>ojam<PERSON> vērtība!ir <PERSON><PERSON><PERSON><PERSON>, kas dal<PERSON><PERSON> be<PERSON>, līdz kuram j<PERSON>o"}, "FLOOR.MATH": {"a": "(skaitlis; [būtisku<PERSON>]; [rež<PERSON><PERSON>])", "d": "Noapaļo skaitli uz leju līdz tuvākajam veselajam skaitlim vai līdz tuvākajam nozīmīgajam skaitlim, kas dal<PERSON><PERSON> bez at<PERSON>uma", "ad": "ir noapaļojamā vērtība!ir <PERSON><PERSON><PERSON><PERSON>, kas da<PERSON><PERSON><PERSON> be<PERSON> at<PERSON>, līdz kuram jānoa<PERSON>!ja dota un ja nav nulle, š<PERSON> funkcija noapaļos uz leju līdz nullei"}, "GCD": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "<PERSON>g<PERSON><PERSON>l<PERSON><PERSON> k<PERSON>", "ad": "ir v<PERSON><PERSON><PERSON><PERSON> no 1 līdz 255"}, "INT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Noapaļo skaitli līdz tuvākajam veselajam skaitlim", "ad": "ir <PERSON><PERSON><PERSON><PERSON>, kas <PERSON><PERSON> līdz veselam skaitlim"}, "ISO.CEILING": {"a": "(skaitl<PERSON>; [būtisku<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas noapaļots līdz tuvākajam veselajam skaitlim vai tuvākajam būtiska<PERSON>m skaitlim, kas dal<PERSON><PERSON> bez atlikuma. Neatkarīgi no skaitļa <PERSON>, tas tiek noapaļots uz augšu. Ta<PERSON><PERSON> ja skaitlis vai būtiskais skaitlis ir nulle, tiek atgriezta nulle.", "ad": "ir no<PERSON><PERSON>ojam<PERSON> vērtība!ir <PERSON><PERSON><PERSON><PERSON>, kas dal<PERSON><PERSON> be<PERSON>, līdz kuram j<PERSON>o"}, "LCM": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atg<PERSON>ž ma<PERSON><PERSON><PERSON>, ar kuru dal<PERSON><PERSON> bez at<PERSON>uma", "ad": "ir v<PERSON><PERSON><PERSON><PERSON> no 1 līdz 255, kur<PERSON><PERSON> iegūstams mazākais kop<PERSON><PERSON>, ar kuru dalā<PERSON> bez atlikuma"}, "LN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa naturālo logaritmu", "ad": "ir pozitīvs <PERSON>, kam iegūstams naturālais logaritms"}, "LOG": {"a": "(skaitlis; [bāze])", "d": "Atgriež skaitļa logaritmu norādītā<PERSON> bāzei", "ad": "ir pozitī<PERSON>s <PERSON>, kam j<PERSON><PERSON>zina logaritms!ir logaritma bāze; 10, ja i<PERSON><PERSON><PERSON>"}, "LOG10": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa bāzes 10 logaritmu", "ad": "ir pozitī<PERSON><PERSON>, kam ieg<PERSON><PERSON> bāzes 10 logaritms"}, "MDETERM": {"a": "(masīvs)", "d": "Atgriež masīva matricas determinantu", "ad": "ir skaitlisks masīvs ar vienādu rindu un kolonnu skaitu, konstantu <PERSON>ūnu diapazonu vai masīvu"}, "MINVERSE": {"a": "(masīvs)", "d": "Atgriež masīvā glabātas matricas apgriezto matricu", "ad": "ir skaitlisks masīvs ar vienādu rindu un kolonnu skaitu, konstantu <PERSON>ūnu diapazonu vai masīvu"}, "MMULT": {"a": "(masīvs1; masīvs2)", "d": "Atgriež divu masīvu reizinājumu - masīvu ar to pašu rindu skaitu kā masīvs1 un ar to pašu kolonnu skaitu kā masīvs2", "ad": "ir pirmais reizin<PERSON><PERSON>is skaitļu masīvs un tam jābūt tikpat daudz kolonnām kā masīvs2 rindām"}, "MOD": {"a": "(skai<PERSON><PERSON>; dal<PERSON>t<PERSON>js)", "d": "Atgriež atlikumu pēc skaitļa dalī<PERSON> ar dal<PERSON>", "ad": "ir s<PERSON><PERSON><PERSON>, kura at<PERSON> pēc dalīša<PERSON> ir jāuzzina!ir skai<PERSON><PERSON>, ar kuru jā<PERSON><PERSON>"}, "MROUND": {"a": "(skaitlis; dalāmais_skaitlis)", "d": "<PERSON><PERSON><PERSON><PERSON>, kas noapaļ<PERSON> līdz vēlamajam dalāmajam skaitlim", "ad": "ir no<PERSON><PERSON><PERSON><PERSON><PERSON> vērtība!ir <PERSON><PERSON><PERSON><PERSON><PERSON>, lī<PERSON><PERSON> kuram j<PERSON><PERSON> skaitlis"}, "MULTINOMIAL": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež datu kopas multinomiālu", "ad": "ir v<PERSON><PERSON><PERSON><PERSON> no 1 līdz 255, kur<PERSON>m iegūstams multinomiāls"}, "MUNIT": {"a": "(dimensija)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dimensijas vien<PERSON> matricu", "ad": "ir ve<PERSON><PERSON>, kas nor<PERSON>da tās vien<PERSON>bas matricas dimensiju, k<PERSON> j<PERSON><PERSON>"}, "ODD": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Noapaļo p<PERSON>itīvu skaitli uz augšu, bet negatīvu - uz leju līdz tuvākajam veselajam nepāra skaitlim", "ad": "noapaļojamā vērtība"}, "PI": {"a": "()", "d": "Atgriež vērtību Pi - 3,14159265358979 ar precizitāti līdz 15 cipariem", "ad": ""}, "POWER": {"a": "(skai<PERSON><PERSON>; pak<PERSON>pe)", "d": "Atgriež skaitļa k<PERSON>āšanas rezultātu", "ad": "ir bāzes skaitl<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> skaitl<PERSON>!ir bāzes skaitļa kāpinātājs"}, "PRODUCT": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "<PERSON><PERSON><PERSON> visus s<PERSON>, kas sniegti k<PERSON>i", "ad": "ir 1 līdz 255 skaitļu. loģisko vērtību vai tekstuālu skaitļu atspoguļojumu, ko var sareizināt"}, "QUOTIENT": {"a": "(skait<PERSON>t<PERSON>js; saucējs)", "d": "<PERSON>g<PERSON>ž da<PERSON><PERSON><PERSON><PERSON> veselo da<PERSON>", "ad": "ir dalāmais!ir dalītājs"}, "RADIANS": {"a": "(leņķis)", "d": "Konvertē grādus par radiāniem", "ad": "ir kon<PERSON><PERSON><PERSON><PERSON><PERSON> leņķis grādos"}, "RAND": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON>, kas liel<PERSON>ks vai vienāds ar nulli un mazāks par 1 un ir vienm<PERSON>r<PERSON><PERSON> sadal<PERSON> (veicot pārrēķinu, mainās)", "ad": ""}, "RANDARRAY": {"a": "([rindas]; [ailes]; [min]; [max]; [vesels_skaitlis])", "d": "Atgriež gadījumskaitļu ma<PERSON>īvu", "ad": "rindu skaits atgrieztajā masīvā!sleju skaits atgrieztajā masīvā!minim<PERSON><PERSON><PERSON> skaitl<PERSON>, kuru vēlaties atgriezt! maks<PERSON><PERSON><PERSON><PERSON> skaitl<PERSON>, kuru vēlaties atgriezt! atgriezt veselu skaitļu vai decimāldaļskaitļu vērtības. TRUE veselam skaitlim, FALSE decimāldaļskaitlim"}, "RANDBETWEEN": {"a": "(mazākais; lielākais)", "d": "Atgriež nejauši izvēlētu skaitli starp norādītajiem skaitļiem", "ad": "ir mazākais veselais skaitlis RANDBETWEEN, kas tiek atgriezts!ir lielākais veselais skaitlis RANDBETWEEN, kas tiek atgriezts"}, "ROMAN": {"a": "(skaitlis; [forma])", "d": "Konvertē arābu skaitļus uz romiešu skaitļiem kā tekstu", "ad": "ir konvert<PERSON><PERSON><PERSON>is arābu skaitlis!ir skaitl<PERSON>, kas nor<PERSON>da vajadzīgā romiešu skaitļa tipu."}, "ROUND": {"a": "(skaitlis; ciparu_skaits)", "d": "Noapaļo skaitli līdz norādītajam ciparu skaitam", "ad": "ir no<PERSON><PERSON><PERSON><PERSON><PERSON> skaitlis!ir ciparu skaits, lī<PERSON><PERSON> kuram j<PERSON>. <PERSON><PERSON>, no<PERSON><PERSON><PERSON><PERSON>na notiek pa kreisi no decimālzīmes; nulle - līdz tuvākajam veselajam skaitlim"}, "ROUNDDOWN": {"a": "(skaitlis; ciparu_skaits)", "d": "Noapaļo skaitli uz leju virzienā uz nulli", "ad": "ir j<PERSON><PERSON><PERSON><PERSON>, kas no<PERSON> uz leju!ir ciparu skaits, līdz kuram jānoa<PERSON>. Ja negatīvs - skaitlis tiek noapaļots pa kreisi no decimālzīmes; ja nulle vai izlaists - līdz tuvākajam veselajam skaitlim"}, "ROUNDUP": {"a": "(skaitlis; ciparu_skaits)", "d": "Noapaļo skaitli uz augšu virzienā no nulles", "ad": "ir j<PERSON><PERSON><PERSON><PERSON>, kas no<PERSON> uz augšu!ir ciparu skaits, līdz kuram jānoapa<PERSON>o. Ja negatīvs - skaitlis tiek noapaļots pa kreisi no decimālzīmes; ja nulle vai izlaists - līdz tuvākajam veselajam skaitlim"}, "SEC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "atgriež leņķa sekanti", "ad": "ir leņķis radi<PERSON><PERSON>, kam aprēķināma sekante"}, "SECH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "atgriež leņķa hiper<PERSON>isko se<PERSON>ti", "ad": "ir leņķis r<PERSON><PERSON><PERSON>, kam aprēķināma hiperboliskā sekante"}, "SERIESSUM": {"a": "(x; n; m; koe<PERSON>i)", "d": "Atgriež pakāpju sērijas summu, kas balst<PERSON>ta formulā", "ad": "ir ievades vērtība pakāpju sērijās!ir sākotn<PERSON><PERSON><PERSON> pak<PERSON>, kurā kāpināms x!ir solis, par kādu palielināt n katrā sērijas izteiksmē!ir koeficientu kopa, kurā tiek reizināta katra secīgā x pakāpe"}, "SIGN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON>griež skait<PERSON>a <PERSON>: 1, ja skaitlis ir pozitīv<PERSON>, nulli, ja skaitlis ir nulle, vai -1, ja skaitlis ir negatīvs", "ad": "ir j<PERSON><PERSON><PERSON><PERSON>"}, "SIN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež leņķa sinusu", "ad": "ir leņķis radi<PERSON><PERSON>, kam j<PERSON>st sinuss. Grādi * PI()/180 = radiāni"}, "SINH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa hiperbolisko sinusu", "ad": "ir j<PERSON><PERSON><PERSON><PERSON>"}, "SQRT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa kvadrātsakni", "ad": "ir <PERSON>, kuram i<PERSON>"}, "SQRTPI": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež k<PERSON> (skaitlis * Pi)", "ad": "ir <PERSON><PERSON>, ar kuru p <PERSON>k reizin<PERSON>ts"}, "SUBTOTAL": {"a": "(funkcijas_num; ats1; ...)", "d": "Atgriež starpsummu sarakstā vai datu bāzē", "ad": "ir s<PERSON><PERSON><PERSON> no 1 līdz 11, kas nor<PERSON><PERSON> starpsummas kopsavilkuma funkciju.!ir 1 līdz 254 diapazoni vai atsauces, kam jāaprēķina starpsummas"}, "SUM": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Saskaita visus šūnu di<PERSON>zon<PERSON> esošos skait<PERSON>us", "ad": "ir 1 līdz 255 saskaitāmu skaitļu. Loģiskās vērtības un teksts, kas ir <PERSON><PERSON>, tiek ignorēts arī tad, ja ievadīts kā argumenti"}, "SUMIF": {"a": "(diapazons; kritēriji; [summas_diapazons])", "d": "Saskaita noteikta nosacījuma vai kritēriju nor<PERSON><PERSON><PERSON><PERSON>", "ad": "ir novērtējamo šūnu diapazons!ir nosacījums vai kritēriji skaitļa, izteiksmes vai teksta formā, kas definē, kuras šūnas tiks saskaitītas!ir faktiskās summējamā<PERSON>. <PERSON><PERSON> <PERSON><PERSON><PERSON>, tiek summētas diapazona šūnas"}, "SUMIFS": {"a": "(summas_diapazons; kritēriju_diapazons; kritēriji; ...)", "d": "<PERSON><PERSON><PERSON>, ko norāda dotā nosacījumu kopa vai kritēriji", "ad": "ir faktiski saskaitāmās <PERSON>!ir <PERSON><PERSON>nu diapazons, kuru jāvērtē pēc noteiktā nosacījuma!ir nosacījums skaitļa veidā, iz<PERSON><PERSON><PERSON><PERSON> vai teks<PERSON>, kas nosa<PERSON>, kuras <PERSON> tiks pievie<PERSON>as"}, "SUMPRODUCT": {"a": "(masīvs1; [masīvs2]; [masīvs3]; ...)", "d": "Atgriež atbilstošo diapazonu vai masīvu reizinājumu summu", "ad": "ir 2 līdz 255 masīvu, kuri jā<PERSON> un pēc tam to komponenti jāsaskaita. Visiem masīviem jābūt vienādu izmēru"}, "SUMSQ": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež argumentu kvadrātu summu. Argumenti var būt skait<PERSON>, <PERSON><PERSON><PERSON><PERSON>, no<PERSON><PERSON><PERSON> vai atsauces uz <PERSON>, k<PERSON><PERSON><PERSON> ir skait<PERSON>i", "ad": "ir 1 līdz 255 s<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> vai atsauču uz masīviem, kuri<PERSON> aprēķināma kvadr<PERSON>tu summa"}, "SUMX2MY2": {"a": "(masīvs_x; masīvs_y)", "d": "Sasummē divu atbilstošu diapazonu vai masīvu kvadrātu atšķirības", "ad": "ir pirmais vērtību diapazons vai masīvs; tās var būt skaitļi vai nosaukumi, mas<PERSON><PERSON> vai atsauces, kurās ir skaitļi!ir otrais vērtību diapazons vai masīvs; tās var būt skaitļi vai nosaukumi, masīvi vai atsauces, kurās ir skaitļi"}, "SUMX2PY2": {"a": "(masīvs_x; masīvs_y)", "d": "Atgriež divu atbilstošu diapazonu vai masīvu skaitļu kvadrātu summu kopsummu", "ad": "ir pirmais skaitļu diapazons vai masīvs; tas var būt skaitļi vai nosaukumi, masīvi vai atsauces, kurās ir skaitļi!ir otrais skaitļu diapazons vai masīvs; tas var būt skaitļi vai nosaukumi, masīvi vai atsauces, kurās ir skaitļi"}, "SUMXMY2": {"a": "(masīvs_x; masīvs_y)", "d": "Sasummē divu atbilstošu diapazonu vai masīvu atšķirību kvadrātus", "ad": "ir pirmais vērtību diapazons vai masīvs; tās var būt skaitļi vai nosaukumi, mas<PERSON><PERSON> vai atsauces, kurās ir skaitļi!ir otrais vērtību diapazons vai masīvs; tās var būt skaitļi vai nosaukumi, masīvi vai atsauces, kurās ir skaitļi"}, "TAN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež leņķa tangensu", "ad": "ir leņķis radiān<PERSON>, kam jā<PERSON>gūst tangenss. Grādi * PI()/180 = radiāni"}, "TANH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa hiperbolisko tangensu", "ad": "ir j<PERSON><PERSON><PERSON><PERSON>"}, "TRUNC": {"a": "(skaitlis; [ciparu_skaits])", "d": "<PERSON><PERSON><PERSON> skaitli lī<PERSON>z ve<PERSON> s<PERSON>, <PERSON><PERSON><PERSON><PERSON> skait<PERSON>a de<PERSON>", "ad": "ir aprau<PERSON><PERSON><PERSON> skaitl<PERSON>!ir s<PERSON><PERSON><PERSON>, kas <PERSON><PERSON><PERSON> a<PERSON>, 0 (nulle), ja i<PERSON><PERSON><PERSON>"}, "ADDRESS": {"a": "(rindas_num; kolonnas_num; [abs_num]; [a1]; [lapas])", "d": "Izve<PERSON> at<PERSON> kā te<PERSON>tu, ja nor<PERSON><PERSON><PERSON><PERSON> rindas un kolonnas numuri", "ad": "ir rindas numurs, kas izmantojams šūnas atsaucē: Rindas_numurs = 1 ir rindai1!ir kolonnas numurs, kas izmantojams šūnas atsaucē. Piem<PERSON><PERSON>, Kolonnas_numurs = 4 ir kolonnaiD!norāda atsauces tipu: absolūtais = 1; absolūtais rindas/relatīvais kolonnas = 2; relatīvais rindas/absolūtais kolonnas = 3; relatīvais = 4!ir loģiskā vērtība, kas norāda atsauces stilu: A1 stils = 1 vai TRUE; R1K1 stils = 0 vai FALSE!ir teksts, kas norāda tās darblapas nosaukumu, kura izmanto<PERSON>ma kā ārējā atsauce"}, "CHOOSE": {"a": "(indeksa_num; vērtība1; [vērtība2]; ...)", "d": "<PERSON>zv<PERSON><PERSON> vērtību vai veicamo darbību no vērtību saraksta atkarībā no indeksa skaitļa", "ad": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> vērt<PERSON> arguments tiek atlasīts. Indeksa_num ir jābūt no 1 līdz 254 vai formulai vai atsaucei uz skaitli no 1 līdz 254!ir 1 līdz 254 skai<PERSON><PERSON><PERSON>, atsauces u<PERSON><PERSON><PERSON>, defin<PERSON><PERSON>, funkcijas vai teksta argumenti, no kuriem funkcija CHOOSE veic atlasi"}, "COLUMN": {"a": "([atsauce])", "d": "Atgriež atsauces kolonnas numuru", "ad": "ir š<PERSON>na vai blak<PERSON> di<PERSON>zon<PERSON>, kam uzzin<PERSON>ms kolonnas numurs. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, tiek i<PERSON><PERSON><PERSON>, kur<PERSON> ir COLUMN funkcija"}, "COLUMNS": {"a": "(masīvs)", "d": "Atgriež kolonnu skaitu atsaucē vai masīvā", "ad": "ir masī<PERSON><PERSON>, masīva formula vai atsauce uz <PERSON><PERSON>, kam j<PERSON><PERSON><PERSON>a kolonnu skaits"}, "FORMULATEXT": {"a": "(atsauce)", "d": "Atgriež formulu kā virkni", "ad": "ir formulas atsauce"}, "HLOOKUP": {"a": "(uzmeklējamā_vērtība; tabulas_masīvs; rinda_indeksa_num; [diapazona_uzmeklēšana])", "d": "Meklē vērtību tabulas vai vērtību masīva augšējā rindā un atgriež to tajā pašā rindas kolonnā, kuru norāda", "ad": "ir tabulas pirmajā rindā uzmeklējamā vērtība; tā var būt vērtība, atsauce vai teksta virkne!ir teksta, skaitļu vai loģisko vērtību tabula, kurā dati tiek meklēti. Tabulas_masīvs var būt atsauce uz diapazonu vai diapazona nosaukumu!ir rindas numurs tabulas_masīvā, no kura jāatgriež atbilstīgā vērtība. Tabulas pirmā vērtību rinda ir 1. rinda!ir loģiskā vērtība: lai augšējā rindā (kārtojot augošā secībā) atrastu visvairāk atbilstošo = TRUE vai izlaists; lai atrastu pilnīgi atbilstošu = FALSE"}, "HYPERLINK": {"a": "(saite_uz_atraš_vietu; [p<PERSON><PERSON><PERSON><PERSON>_vārds])", "d": "Izveido saīsni vai <PERSON>, kas atver datora cietajā diskā, tīkla serverī vai internetā glabātu dokumentu", "ad": "ir teks<PERSON>, kas nor<PERSON>da atveramā dokumenta ceļu un faila nosaukumu - vietu cietajā diskā, UNC adresi vai URL ceļu!ir teksts vai s<PERSON>tl<PERSON>, kas tiek rādīts šūnā. <PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tiek rādīts teksts Saite_uz_atraš_vietu"}, "INDEX": {"a": "(masīvs; rindas_numurs; [kolonnas_numurs]!atsauce; rindas_numurs; [kolonnas_numurs]; [apga<PERSON>a_numurs])", "d": "Atgriež vērt<PERSON><PERSON> vai <PERSON>, kas atrodas noteiktas rindas un kolonnas krustpunktā norādītajā diapazonā", "ad": "ir šūnu diapazons vai masīva konstante.!atlasa rindu Masīvā vai Atsauci, no kuras jāatgriež vērtība. Ja izlaists, ir nepieciešams Kolonnas_numurs!atlasa kolonnu Masīvā vai Atsauci, no kuras jāatgriež vērtība. Ja izlai<PERSON>, ir nepieciešams Rindas_numurs!ir atsauce uz vienu vai vairākiem šūnu diapazoniem!atlasa rindu Masīvā vai Atsauci, no kuras jāatgriež vērtība. Ja izlaists, ir nepieciešams Kolonnas_numurs!atlasa kolonnu Masīvā vai Atsauci, no kuras jāatgriež vērtība. Ja izlaists, ir nepieciešams Rindas_numurs!atlasa diapazonu Atsaucē, no kuras jāatgriež vērtība. Pirmais atlasītais vai ievadītais apgabals ir 1. apgabals, otrais apgabals ir 2. apga<PERSON>s un tā tālāk"}, "INDIRECT": {"a": "(ats_teksts; [a1])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas <PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>ta v<PERSON>", "ad": "ir atsauce uz <PERSON>ūnu, k<PERSON><PERSON> ietilpst A1 vai R1K1 stila atsauce, no<PERSON><PERSON><PERSON>, kas definēts kā atsauce vai atsauce uz šūnu kā teksta virkne!ir loģiskā vērtība, kas norāda atsauces tipu Ats_tekstā: R1K1 stils = FALSE; A1 stils = TRUE vai izlaists"}, "LOOKUP": {"a": "(uzmeklējamā_vērtība; uzmekl<PERSON><PERSON><PERSON>_vektors; [rezultāta_vektors]!uzmeklējamā_vērtība; masīvs)", "d": "Uzmeklē vērtību vienas rindas vai vienas kolonnas diapazonā vai masīvā. Paredzēts atpakaļsaderībai", "ad": "ir vērt<PERSON><PERSON>, kuru funkcija LOOKUP meklē Uzmeklēšanas_vektorā; tas var būt skaitlis, teksts, loģiskā vērtība vai nosaukums vai atsauce uz vērtību!ir diapazons, kurā ietilpst tikai viena rinda vai viena kolonna ar tekstu, skaitļiem vai loģiskajām vērtībām, kas izkārtotas augošā secībā!ir diapazons, kurā ir tikai viena rinda vai kolonna; tas ir tikpat liels kā Uzmeklēšanas_vektors!ir vērtī<PERSON>, kuru funkcija LOOKUP meklē masīvā; tas var būt skaitlis, teksts, loģiskā vērtība vai nosaukums vai atsauce uz vērtību!ir šūnu diapazons, kurā ietilpst teksts, skaitlis vai loģiskās vērtības, kas jā<PERSON>īdzina ar Uzmeklējamo_vērtību"}, "MATCH": {"a": "(uzmeklējamā_vērtība; uzmeklē<PERSON><PERSON>_masīvs; [atbilstības_tips])", "d": "Atg<PERSON>ž tāda masīva vienuma relatīvo pozīciju, kur<PERSON> atbilst norādītai vērtībai norādītā kārtībā", "ad": "ir v<PERSON><PERSON><PERSON><PERSON>, ko <PERSON><PERSON>, lai atrastu vajadz<PERSON>go vērtību masīvā, skai<PERSON><PERSON>, tekstu vai loģisko vērtību, vai atsauci uz kādu no tiem!ir blak<PERSON><PERSON><PERSON>u šūnu diapazons, kur<PERSON> ietil<PERSON>t iespējamās uzmeklējamās vērt<PERSON>, vērtību masīvs vai atsauce uz masīvu!ir skaitlis 1, 0 vai -1, kas norāda atgriežamo vērtību."}, "OFFSET": {"a": "(atsauce; rindas; kolonnas; [augstums]; [platums])", "d": "Atgriež atsauci uz diapazonu, kas ir noteikts rindu un kolonnu skaits no noteiktas atsauces", "ad": "ir atsauce, no kuras jāveido nobīde, atsauce uz šūnu vai blakusesošu šūnu diapazonu!ir rindu skaits uz augšu vai uz leju, uz kuru jāatsaucas rezultāta kreisajai augšējai šūnai!ir kolonnu skaits pa kreisi vai pa labi, uz kuru jāatsaucas rezultāta kreisajai augšējai šūnai!ir augstums rindās, kādam jābūt rezultātam; tāds pats augstums kā Atsaucei, ja izlaists!ir platums kolonnās, kādam jāb<PERSON>t rezultātam; tāds pats platums kā Atsaucei, ja izlaists"}, "ROW": {"a": "([atsauce])", "d": "<PERSON>g<PERSON><PERSON> atsauces rindas numuru", "ad": "ir <PERSON><PERSON>na vai viens šūnu diapazons, kam uzzin<PERSON>ms rindas numurs; ja i<PERSON><PERSON><PERSON>, tiek at<PERSON><PERSON><PERSON><PERSON>, kurā ir RINDAS funkcija"}, "ROWS": {"a": "(masīvs)", "d": "Atgriež rindu skaitu atsaucē vai masīvā", "ad": "ir masīvs, mas<PERSON>va formula vai <PERSON>nu diapazons, kam j<PERSON><PERSON><PERSON>a rindu skaits"}, "TRANSPOSE": {"a": "(masīvs)", "d": "Konvertē vertikālu šūnu diapazonu par horizontālu un pretēji", "ad": "ir <PERSON><PERSON><PERSON> diapazons darb<PERSON>ā vērtī<PERSON> masīvs, kas j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "UNIQUE": {"a": "(masīvs; [pēc_kolonnas]; [tieši_vienre<PERSON>])", "d": "Atgriež unikālās vērtības no diapazona vai masīva.", "ad": "diapazons vai masīvs, no kura jāatgriež unikālas rindas vai kolonnas!ir loģiska vērtība: salīdziniet rindas savā starpā un atgrieziet unikālās rindas = FALSE vai izlaists; salīdziniet kolonnas savā starpā un atgrieziet unikālās kolonnas = TRUE!ir loģiska vērtība: atgriež rindas vai kolonnas, kas parādās tieši vienreiz no masīva = TRUE; atgriezt visas atšķirīgās rindas vai kolonnas no masīva = FALSE vai izlaists"}, "VLOOKUP": {"a": "(uzmeklējamā_vērtība; tabulas_masīvs; kolonna_indeksa_num; [diapazona_uzmeklēšana])", "d": "Meklē vērtību tabulas pēdējā kolonnā pa kreisi un pēc tam atgriež vērtību tajā pašā kolonnas rindā, kuru norāda. Pēc noklusējuma tabulai jābūt sakārtotai augošā secībā", "ad": "ir tabulas pirmajā kolonnā atrodamā vērtība; tā var būt vērtība, atsauce vai teksta virkne!ir teksta, skaitļu vai loģisko vērtību tabula, no kuras tiek izgūti dati. Tabulas_masīvs var būt atsauce uz diapazonu vai diapazona nosaukums!ir kolonnas numurs tabulas_masīvā, no kura jāatgriež atbilstīgās vērtības. Pirmā vērtību kolonna tabulā ir 1. kolonna!ir loģiskā vērtība: lai atrastu visatbilstošāko pirmajā kolonnā (kas sakārtota augošā secībā) = TRUE vai izlaista; lai atrastu pilnīgi atbilstošu = FALSE"}, "XLOOKUP": {"a": "(uzmeklējamā_vērtība; uzmekl<PERSON><PERSON><PERSON>_masīvs; atgrie<PERSON><PERSON>_masīvs; [ja_nav_atrasts]; [atbilst<PERSON><PERSON>_režīms]; [mekl<PERSON><PERSON><PERSON>_režīms])", "d": "Meklē diapazonā vai masīvā atbilstību un atgriež atbilstošo vienumu no otra diapazona vai masīva. Pēc noklusējuma tiek izmantota precīza atbilstība", "ad": "ir meklējamā vērtība!ir masīvs vai diapazons, kurā meklēt!ir masīvs vai diapazons vērtības atgriešanai!tiek atgriezts, ja nav atrasta atbilstība!norādiet uzmeklējamās vērtības atbilstību ar vērtībām uzmeklēšanas masīvā!norādiet izmantojamo meklēšanas režīmu. Pēc noklusējuma tiks izmantota meklēšana no pirmā līdz pēdējam vienumam"}, "CELL": {"a": "(info_tips; [atsauce])", "d": "Atgriež informāciju par šū<PERSON>, atrašanās vietu vai saturu", "ad": "<PERSON><PERSON><PERSON> v<PERSON>, <PERSON><PERSON>, kā<PERSON>nas informācijas tipu vēlaties atgriezt!<PERSON><PERSON><PERSON>, par kuru vēlaties informāciju"}, "ERROR.TYPE": {"a": "(k<PERSON><PERSON><PERSON>_vērt)", "d": "<PERSON><PERSON><PERSON><PERSON>, kas atbilst kļūdas vērtībai.", "ad": "ir k<PERSON><PERSON><PERSON> vērtī<PERSON>, kam jāuzzina identifikācijas numurs; tā var būt reālā kļūdas vērtība vai atsauce uz <PERSON>nu, kurā ietilpst kļūdas vērtība"}, "ISBLANK": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai atsauce nav uz tukšu šūnu un atgriež TRUE vai FALSE", "ad": "ir <PERSON>, vai <PERSON>, kas at<PERSON> uz pā<PERSON><PERSON><PERSON><PERSON>"}, "ISERR": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir <PERSON>, kas nav #N/A, un atgriež TRUE vai FALSE", "ad": "ir pārbaud<PERSON><PERSON><PERSON> vērtība. Vērt<PERSON><PERSON> var atsaukties uz šūnu, formulu vai uz nosaukumu, kur<PERSON> atsaucas uz šūnu, formulu vai vērtību"}, "ISERROR": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir k<PERSON>, un atgriež TRUE vai FALSE", "ad": "ir pārbaud<PERSON><PERSON><PERSON> vērtība. Vērt<PERSON><PERSON> var atsaukties uz šūnu, formulu vai uz nosaukumu, kur<PERSON> atsaucas uz šūnu, formulu vai vērtību"}, "ISEVEN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež TRUE, ja ir <PERSON><PERSON><PERSON>", "ad": "ir pā<PERSON><PERSON><PERSON><PERSON><PERSON> vērtība"}, "ISFORMULA": {"a": "(atsauce)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai atsauce ir uz <PERSON>, kur<PERSON> ir formula, un atgriež TRUE vai FALSE", "ad": "ir atsauce uz šūnu, k<PERSON> j<PERSON>.  Atsauce var būt šū<PERSON> atsauce, formula vai nosaukums, kas atsaucas uz šūnu"}, "ISLOGICAL": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir loģiskā vērtība (TRUE vai FALSE) un atgriež TRUE vai FALSE", "ad": "ir pārbaud<PERSON><PERSON><PERSON> vērtība. Vērt<PERSON><PERSON> var atsaukties uz šūnu, formulu vai nosaukumu, kas atsaucas uz šūnu, formulu vai vērtību"}, "ISNA": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir #N/A un atgriež TRUE vai FALSE", "ad": "ir pārbaud<PERSON><PERSON><PERSON> vērtība. Vērt<PERSON><PERSON> var atsaukties uz šūnu, formulu vai uz nosaukumu, kur<PERSON> atsaucas uz šūnu, formulu vai vērtību"}, "ISNONTEXT": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība nav teksts (tuk<PERSON><PERSON> nav teksts) un atgriež TRUE vai FALSE", "ad": "ir pārbaudāmā vērtība: <PERSON><PERSON>na, formula vai nosaukums, kas atsaucas uz <PERSON>nu, formulu vai vērtību"}, "ISNUMBER": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir skaitlis un atgriež TRUE vai FALSE", "ad": "ir pārbaud<PERSON><PERSON><PERSON> vērtība. Vērt<PERSON><PERSON> var atsaukties uz šūnu, formulu vai nosaukumu, kas atsaucas uz šūnu, formulu vai vērtību"}, "ISODD": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež TRUE, ja ir <PERSON><PERSON><PERSON>", "ad": "ir pā<PERSON><PERSON><PERSON><PERSON><PERSON> vērtība"}, "ISREF": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir atsauce un atgriež TRUE vai FALSE", "ad": "ir pārbaud<PERSON><PERSON><PERSON> vērtība. Vērtī<PERSON> var atsaukties uz šūnu, formulu vai nosaukumu, kas atsaucas uz šūnu"}, "ISTEXT": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir teksts un atgriež TRUE vai FALSE", "ad": "ir pārbaud<PERSON><PERSON><PERSON> vērtība. Vērt<PERSON><PERSON> var atsaukties uz šūnu, formulu vai nosaukumu, kas atsaucas uz šūnu, formulu vai vērtību"}, "N": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>, kas nav skai<PERSON>, par skaitli, datumus - par seriā<PERSON><PERSON> skaitļiem, TRUE uz 1, visu citu - uz 0 (nulli)", "ad": "ir kon<PERSON><PERSON><PERSON><PERSON><PERSON> vērtība"}, "NA": {"a": "()", "d": "Atgriež kļūdas vērtību #N/A (vērtība nav pieejama)", "ad": ""}, "SHEET": {"a": "([vērtība])", "d": "Atgriež atsauces lapas numuru", "ad": "ir lapas nosaukums vai atsauce, kurai j<PERSON><PERSON>da lapas numurs.  <PERSON><PERSON> <PERSON><PERSON>, tiek atgriezts tās lapas numurs, kur<PERSON> ir <PERSON><PERSON>"}, "SHEETS": {"a": "([atsauce])", "d": "<PERSON><PERSON><PERSON><PERSON>u skaitu at<PERSON>", "ad": "ir at<PERSON><PERSON>, par kuru jānorāda tajā esošo lapu skaits. <PERSON>a <PERSON><PERSON>, tiek atgriezts tās darbgr<PERSON><PERSON><PERSON> lapu skaits, kur<PERSON> ir <PERSON><PERSON>ja"}, "TYPE": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> veselu skaitli, kas ap<PERSON><PERSON><PERSON><PERSON> vērtības datu tipu: skaitlis = 1; teksts = 2; loģiskā vērtība = 4; k<PERSON><PERSON><PERSON> vērtība = 16; masīvs = 64; salik<PERSON> dati = 128", "ad": "var būt jeb<PERSON> vērtība"}, "AND": {"a": "(loģiskā1; [loģiskā2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai visi argumenti ir TRUE, un atgriež TRUE, ja visi argumenti ir TRUE", "ad": "ir 1 līdz 255 no<PERSON><PERSON><PERSON><PERSON><PERSON>, kas p<PERSON><PERSON><PERSON><PERSON><PERSON> un kas var būt TRUE vai FALSE; tie var būt loģiskās vērt<PERSON><PERSON>, masīvi vai atsauces"}, "FALSE": {"a": "()", "d": "Atgriež loģisko vērtību FALSE", "ad": ""}, "IF": {"a": "(loģiskais_tests; [vērtība_ja_true]; [vērtība_ja_false])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai ir ievē<PERSON>, un atgriež vienu vērt<PERSON>, ja TRUE, bet citu vērtību, ja - FALSE", "ad": "ir jeb<PERSON> vērtība vai izteiksme, kuru var novērtēt ar TRUE vai FALSE!ir v<PERSON><PERSON><PERSON><PERSON>, kuru atgrie<PERSON>, ja loģiskais_tests ir TRUE. Ja tas tiek izlaists, tiek atgriezts TRUE. Ir iespējams ligzdot līdz septiņām IF funkcijām!ir vērt<PERSON><PERSON>, kas tiek atgriezta, ja loģiskais_tests ir FALSE. Ja tiek izlaists, tiek atgriezts FALSE"}, "IFS": {"a": "(loģiskais_tests; vērtība_ja_patiess; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai ir izpild<PERSON>ts viens vai vairāki no<PERSON>, un atgriež vērt<PERSON>, kas atbilst pirmajam PATIESAJAM nosacījumam", "ad": "<PERSON><PERSON><PERSON> vērtība vai izteiksme, kuru var novērtēt ar PATIESS vai APLAMS!Atgrieztā vērtība, ja loģiskais_tests ir PATIESS"}, "IFERROR": {"a": "(vērtība; vērtība_ja_kļūda)", "d": "<PERSON>griež vērtī<PERSON>_ja_k<PERSON><PERSON><PERSON>, ja izteik<PERSON>e ir k<PERSON>, bet pretējā gadīju<PERSON> — pašas izteiksmes vērtību", "ad": "ir jebkāda vērtība vai izteiksme, vai atsauce!ir jebkāda vērtība vai izteiksme, vai atsauce"}, "IFNA": {"a": "(vērtība; vērtība_ja_nav_pieej)", "d": "Atg<PERSON><PERSON> nor<PERSON><PERSON><PERSON><PERSON> v<PERSON>, ja izteiksmes atrisinājums ir #N/A; citos gadījumos atgriež izteiksmes vērtību", "ad": "ir jebkāda vērtībai vai izteiksme, vai atsauce!ir jebkāda vērtībai vai izteiksme, vai atsauce"}, "NOT": {"a": "(loģiskā)", "d": "Maina FALSE uz TRUE vai TRUE uz FALSE", "ad": "ir vērtība vai izteiksme, ko var novērtēt kā TRUE vai FALSE"}, "OR": {"a": "(loģiskā1; [loģiskā2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai kāds no argumentiem ir TRUE, un atgriež TRUE vai FALSE. Atgriež FALSE tikai tad, ja visi argumenti ir FALSE", "ad": "ir 1 līdz 255 no<PERSON><PERSON><PERSON><PERSON><PERSON>, kas p<PERSON><PERSON><PERSON><PERSON><PERSON> un kas var būt TRUE vai FALSE"}, "SWITCH": {"a": "(iz<PERSON><PERSON>sm<PERSON>; vērtība1; rezultāts1; [noklusēju<PERSON>_vai_vērtība2]; [rezultāts2]; ...)", "d": "Novērtē izteiksmi pret vērtību sarakstu un atgriež rezultātu, kas atbilst pirmajai atbilstošajai vērtībai. Ja atbilstības nav, tiek atgriezta neobligāta noklusējuma vērtība", "ad": "ir i<PERSON><PERSON><PERSON><PERSON><PERSON>, kas jānovērtē!ir vērt<PERSON><PERSON>, kas jā<PERSON><PERSON><PERSON><PERSON>a ar izteiksmi!ir rezu<PERSON><PERSON><PERSON>, kas j<PERSON><PERSON><PERSON><PERSON><PERSON>, ja atbilstošā vērtība atbilst izteiksmei"}, "TRUE": {"a": "()", "d": "Atgriež loģisko vērtību TRUE", "ad": ""}, "XOR": {"a": "(loģiskā_vērtība1; [loģiskā_vērtība2]; ...)", "d": "No visiem argumentiem atgriež loģisko vērtību \"Izņemot/Vai\"", "ad": "ir 1 līdz 254 no<PERSON><PERSON><PERSON><PERSON><PERSON>, kuri jāizmēģina, kuri var būt TRUE vai FALSE un kuri var būt loģiskās vērt<PERSON><PERSON>, masīvi vai atsauces"}, "TEXTBEFORE": {"a": "(teksts, norobežotājs, [instances_num], [atbilstības_režīms], [atbilstības_beigas], [ja_nav_atrasts])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas ir pirms norobežošanas rakstzīmēm.", "ad": "<PERSON><PERSON><PERSON>, kuru vēlaties meklēt norobežotājā.!Rakstz<PERSON><PERSON> vai virkne, ko izmantot kā norobežotāju.!Vēlamais norobežotāja gadījums. Noklusējums ir 1. Negatīva skaitļa meklēšana no beigām.!Meklē norobežotāja atbilstības tekstu. Pēc noklusējuma ir pabeigta reģistrjutīga atbilstība.!Vai saskaņot norobežotāju ar teksta beigām. Pēc noklusējuma tie neatbilst.!Tiek atgriezts, ja atbilstības nav atrastas. Pēc noklusējuma tiek atgriezts #N/A."}, "TEXTAFTER": {"a": "(teksts, norobežotājs, [instances_num], [atbilstības_režīms], [atbilstības_beigas], [ja_nav_atrasts])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas ir pēc norobež<PERSON> r<PERSON>tzī<PERSON>.", "ad": "<PERSON><PERSON><PERSON>, kuru vēlaties meklēt norobežotājā.!Rakstz<PERSON><PERSON> vai virkne, ko izmantot kā norobežotāju.!Vēlamais norobežotāja gadījums. Noklusējums ir 1. Negatīva skaitļa meklēšana no beigām.!Meklē norobežotāja atbilstības tekstu. Pēc noklusējuma ir pabeigta reģistrjutīga atbilstība.!Vai saskaņot norobežotāju ar teksta beigām. Pēc noklusējuma tie neatbilst.!Tiek atgriezts, ja atbilstības nav atrastas. Pēc noklusējuma tiek atgriezts #N/A."}, "TEXTSPLIT": {"a": "(teksts, kolonnu_norobežotājs, [rindu_norobežotājs], [ignorēt_tukšu], [atbilst<PERSON><PERSON>_rež<PERSON><PERSON>], [pilda_ar])", "d": "Sad<PERSON> tekstu rind<PERSON> vai kolo<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>.", "ad": "Sadalāmais teksts!<PERSON><PERSON><PERSON><PERSON><PERSON> vai virkne, ar kuru sadalīt kolonnas.!<PERSON><PERSON><PERSON><PERSON><PERSON> vai virkne, ar kuru sadalīt rindas.!Vai ignorēt tukšās šūnas. Noklusējumi ir FALSE.!Meklē norobežotāja atbilstības tekstu. Pēc noklusējuma ir pabeigta reģistrjutīga atbilstība.!Vērtība pildīšanai. Pēc noklusējuma tiek lietots #N/A."}, "WRAPROWS": {"a": "(vektors, wrap_count, [pad_with])", "d": "Aplauzt rindas vai kolonnas vektoru pēc norādītā vērtību skaita.", "ad": " Vektors vai atsauce, ko aplauzt! <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vērtību skaits rindā.! <PERSON><PERSON><PERSON><PERSON><PERSON>, ar kuru pievienot tastatūru. Noklusējums ir #NA."}, "VSTACK": {"a": "(masīvs1, [masīvs2], ...)", "d": "Vert<PERSON><PERSON><PERSON> sagrupē masīvus vienā masīvā.", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON> vai at<PERSON>, kas jāsagrup<PERSON>."}, "HSTACK": {"a": "(masīvs1, [masīvs2], ...)", "d": "Horizontāli sagrupē masīvus vienā masīvā.", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON> vai at<PERSON>, kas jāsagrup<PERSON>."}, "CHOOSEROWS": {"a": "(masīvs, row_num1, [row_num2], ...)", "d": "Atgriež rindas no masīva vai atsauces.", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON> vai atsauce, kur<PERSON> ir atgriežamās rindas.!Atgriežamās rindas numurs."}, "CHOOSECOLS": {"a": "(masīvs, col_num1, [col_num2], ...)", "d": "Atgriež kolonnas no masīva vai atsauces.", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON> vai atsauce, kas satur atgrie<PERSON><PERSON><PERSON><PERSON> kolo<PERSON>.!Atgriežamo kolonnu skaits."}, "TOCOL": {"a": "(masīvs, [ignorēt], [scan_by_column])", "d": "Atgriež masīvu kā vienu kolonnu.", "ad": "Masīvs vai atsauce, kas jāatgriež kā kolonna.!Vai ignorēt noteiktu vērtību tipus. Pēc noklusējuma vērtības netiek ignorētas.!Pārbaudiet masīvu pēc kolonnas. Pēc noklusējuma masīvs tiek pārbaudīts pēc rindas."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Atgriež masīvu kā vienu rindu.", "ad": "Masīvs vai atsauce, kas jāatgriež kā rinda.!Vai ignorēt noteiktu vērtību tipus. Pēc noklusējuma vērtības netiek ignorētas.!Pārbaudiet masīvu pēc kolonnas. Pēc noklusējuma masīvs tiek pārbaudīts pēc rindas."}, "WRAPCOLS": {"a": "(vektors, wrap_count, [pad_with])", "d": " Aplauzt rindas vai kolonnas vektoru pēc norādītā vērtību skaita.", "ad": " Vektors vai atsauce uz aplaušanu.! <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vērtību skaits kolonnā.! <PERSON><PERSON><PERSON><PERSON><PERSON>, ar kuru pievienot tastatūru. Noklusējums ir #N/A."}, "TAKE": {"a": "(masīvs, rindas, [kolonnas])", "d": "Atgriež rindas vai kolonnas no masīva sākuma vai beigām.", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON>, no kura paņemt rindas vai kolonnas.!Paņemamo rindu skaits. Negatīva vērtība ir no masīva beigām.!Paņemamo kolonnu skaits. Negatīva vērtība ir no masīva beigām."}, "DROP": {"a": "(masīvs, rindas, [kolonnas])", "d": "Nomet rindas vai kolonnas no masīva sākuma vai beigām.", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON>, no kura nomest rindas vai kolonnas.!Nometamo rindu skaits. Negatīva vērtība krītas no masīva beigām.!Nometamo kolonnu skaits. Negatīva vērtība krītas no masīva beigām."}, "SEQUENCE": {"a": "(rindas, [kolonnas], [sākums], [solis])", "d": "Atgriež skaitļu secību", "ad": "atgriežamo rindu skaits!atgriežamo kolonnu skaits!pirmais numurs secībā!palielinājuma lielums katrai nākamajai secības vērtībai"}, "EXPAND": {"a": "(masīvs, rindas, [kolonnas], [pad_with])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON> lī<PERSON>ā<PERSON>em izmērie<PERSON>.", "ad": "Pap<PERSON><PERSON><PERSON><PERSON><PERSON>is masīvs.!Rindu skaits paplašinātajā masīvā. Ja nav norādīts, rindas netiks pievienotas.!<PERSON>lon<PERSON> skaits paplašinātajā masīvā. Ja nav norādīts, kolonnas netiks pievienotas.!<PERSON><PERSON><PERSON><PERSON><PERSON>, ar kuru papildināt masīvu. Noklusējums ir #N/A."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "Atgriež vienuma relatīvo pozīciju masīvā. Pēc noklusējuma ir nepieciešama precīza atbilstība", "ad": "ir meklējamā vērtība!ir masīvs vai diapazons, kurā veikt meklēšanu!norādiet uzmeklēšanas_vērtība atbilstību ar uzmeklēšanas_masīvs vērtībām!norādiet izmantojamo meklēšanas režīmu. Pēc noklusējuma tiks izmantota meklēšana no pirmā līdz pēdējam vienumam"}, "FILTER": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, [ja_tuk<PERSON><PERSON>])", "d": "Filtrēt diapazonu vai masīvu", "ad": "diapazons vai masīvs filtrēšanai!Būla vērtību masīvs, kur PATIESS norāda saglabājamu rindu vai kolonnu!tiek atgriezts, ja neviens vienums netiek saglabāts"}, "ARRAYTOTEXT": {"a": "(masīvs, [formāts])", "d": "Atgriež masīva teksta attēlojumu", "ad": " mas<PERSON><PERSON><PERSON>, kas jāattēlo kā teksts! teksta formāts"}, "SORT": {"a": "(masīvs, [kārt_indekss], [kārt_secība], [pēc_kol])", "d": "K<PERSON>rto diapazonu vai masīvu", "ad": "kārtojamais diapazons vai masīvs!s<PERSON><PERSON><PERSON>, kas norāda rindu vai kolonnas, pēc kuras kārtot!s<PERSON><PERSON><PERSON>, kas norāda vajadzīgo kārtošanas secību; 1 nozīmē augošā secībā (pēc noklusējuma), -1 nozīmē dilstošā secībā!loģiskā vērtība, kas norāda vajadzīgo kārtošanas virzienu: FALSE, lai kārtotu pēc rindas (noklusējums), TRUE, lai kārtotu pēc kolonnas"}, "SORTBY": {"a": "(masī<PERSON>s, pēc_masīva, [kārt<PERSON><PERSON><PERSON>_secība], ...)", "d": "K<PERSON>rto diapazonu vai masīvu pēc vērtībām atbilstošā diapazonā vai masīvā", "ad": "kārtojamais diapazons vai masīvs!diapazons vai masīvs, pēc kura kārtot!s<PERSON><PERSON><PERSON>, kas nor<PERSON>da vajadzīgo kārtošanas secību; 1 augo<PERSON><PERSON> secībai (pēc noklusējuma), -1 dilstošai secībai"}, "GETPIVOTDATA": {"a": "(datu_lauks; rakurstabula; [lauks]; [objekts]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus, kas tiek glab<PERSON>ti r<PERSON>.", "ad": "ir tā datu lauka nosaukums, no kura jāizgūst dati!ir atsauce uz šūnu vai šūnu diapazonu rakurstabul<PERSON>, kurā ietilpst izgūstamie dati!lauks, uz kuru ir atsauce!lauka objekts, uz kuru ir atsauce"}, "IMPORTRANGE": {"a": "(izkl<PERSON>jlapas_url, diapazona_virkne)", "d": "Import<PERSON>ūnu diapazonu no noteiktas izklājlapas."}}