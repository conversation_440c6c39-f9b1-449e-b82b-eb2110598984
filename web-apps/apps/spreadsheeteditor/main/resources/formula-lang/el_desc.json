{"DATE": {"a": "(έτος; μήνας; ημέρα)", "d": "Αποδίδει τον αριθμό που αναπ<PERSON><PERSON>ιστά την ημερομηνία στον κώδικα ημερομηνίας-<PERSON>ρας", "ad": "είναι ένας αριθμός από το 1900 ή το 1904 (εξαρτά<PERSON><PERSON><PERSON> από το σύστημα ημερομηνιών του βιβλίου εργασίας) έως το 9999!είν<PERSON><PERSON> ένας αριθμός από το 1 έως το 12 που αναπαριστά το μήνα του έτους!είναι ένας αριθμός από το 1 έως το 31 που αναπαριστά την ημέρα του μήνα"}, "DATEDIF": {"a": "(ημερομηνία_έναρξης; ημερομηνία_λήξης; μονάδα)", "d": "Υπολογίζει τον αριθμό των ημερών, των μηνών ή των ετών μεταξύ δύο ημερομηνιών", "ad": "Μια ημερομηνία που αντιπροσωπεύει την πρώτη ή την ημερομηνία έναρξης μιας δεδομένης περιόδου!Μια ημερομηνία που αντιπροσωπεύει την τελευταία ημερομηνία ή ημερομηνία λήξης, της περιόδου!Ο τύπος των πληροφοριών που θέλετε να επιστραφεί"}, "DATEVALUE": {"a": "(κείμενο_ημερομηνίας)", "d": "Μετατρέπει μια ημερομηνία που έχει μορφή κειμένου σε αριθμό που αναπαριστά την ημερομηνία στον κώδικα ημερομηνίας-ώρας", "ad": "είναι κείμεν<PERSON>, το οποίο αναπ<PERSON><PERSON>ιστά μια ημερομηνία σε μορφή ημερομηνίας του Spreadsheet Editor, μεταξύ 1/1/1900 ή 1/1/1904 (εξαρτάται από το σύστημα ημερομηνιών του φύλλου εργασίας) και 31/12/9999"}, "DAY": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός)", "d": "Αποδίδει την ημέρα του μήνα, έν<PERSON>ς αριθμός από το 1 έως το 31.", "ad": "είναι ένας αριθμός στον κώδικα ημερομηνίας-<PERSON>ρας που χρησιμοποιείται από το Spreadsheet Editor"}, "DAYS": {"a": "(τελική_ημερομηνία; αρχική_ημερομηνία)", "d": "Αποδίδει τον αριθμό ημερών μεταξύ των δύο ημερομηνιών.", "ad": "η αρχική_ημερομηνία και η τελική_ημερομηνία είναι οι δύο ημερομηνίες μεταξύ των οποίων θέλετε να ξέρετε πόσες ημέρες υπάρχουν!η αρχική_ημερομηνία και η τελική_ημερομηνία είναι οι δύο ημερομηνίες μεταξύ των οποίων θέλετε να ξέρετε τον αριθμό των ημερών"}, "DAYS360": {"a": "(ημερομηνία_έναρξης; ημερομηνία_λήξης; [μέθοδος])", "d": "Υπολογίζει το πλήθος των ημερών ανάμεσα σε δύο ημερομηνίες, β<PERSON><PERSON><PERSON><PERSON> έτους 360 ημερών (δώδεκα μήνες των 30 ημερών)", "ad": "οι τιμές ημερομηνία_έναρξης και ημερομηνία_λήξης είναι οι δύο ημερομηνίες μεταξύ των οποίων θέλετε να υπολογίσετε το πλήθος των ημερών!οι τιμές ημερομηνία_έναρξης και ημερομηνία_λήξης είναι οι δύο ημερομηνίες μεταξύ των οποίων θέλετε να υπολογίσετε το πλήθος των ημερών!είναι μια λογική τιμή που καθορίζει τη μέθοδο υπολογισμού που θα χρησιμοποιηθεί: U.S. (NASD) = FALSE ή παραλείπεται, European =TRUE."}, "EDATE": {"a": "(ημερομηνία_έναρξης; μήνες)", "d": "Αποδίδει τον αύξοντα αριθμό μιας ημερομηνίας που υποδεικνύεται από το πλήθος των μηνών πριν ή μετά την αρχική ημερομηνία", "ad": "είναι ο αύξων αριθμός που αναπαριστά την αρχική ημερομηνία!είναι ο αριθμός των μηνών πριν ή μετά την ημερομηνία_έναρξης"}, "EOMONTH": {"a": "(ημερομηνία_έναρξης; μήνες)", "d": "Αποδίδει τον αύξοντα αριθμό της τελευταίας ημέρας του μήνα πριν ή μετά από ένα συγκεκριμένο αριθμό μηνών", "ad": "είναι ο αύξων αριθμός που αναπαριστά την αρχική ημερομηνία!είναι ο αριθμός των μηνών πριν ή μετά την ημερομηνία_έναρξης"}, "HOUR": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός)", "d": "Αποδίδει την ώρα με τη μορφή αριθμού από το 0 (12:00 ΠΜ) έως το 23 (11:00 ΜΜ).", "ad": "είναι ένας αριθμός στον κώδικα ημερομηνίας-<PERSON><PERSON><PERSON><PERSON> που χρησιμοποιείται από το Spreadsheet Editor, ή κείμενο σε μορφή ώρας, για παράδειγμα 16:48:00 ή 4:48:00 ΜΜ"}, "ISOWEEKNUM": {"a": "(ημερομηνία)", "d": "Αποδίδει τον αριθμό εβδομάδας κατά ISO του έτους για μια δεδομένη ημερομηνία", "ad": "είναι ο κωδι<PERSON><PERSON>ς ημερομηνίας-<PERSON><PERSON><PERSON><PERSON> που χρησιμοποιείται από το Spreadsheet Editor για υπολογισμούς που περιλαμβάνουν ημερομηνίες και ώρες"}, "MINUTE": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός)", "d": "Αποδίδει το λεπτό, έν<PERSON><PERSON> αριθμός από το 0 έως το 59.", "ad": "είναι ένας αριθμός στον κώδικα ημερομηνίας-<PERSON>ρ<PERSON>ς που χρησιμοποιείται από το Spreadsheet Editor ή ένα κείμενο σε μορφή ώρας, όπως 16:48:00 ή 4:48:00 ΜΜ"}, "MONTH": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός)", "d": "Αποδίδει το μήνα, <PERSON><PERSON><PERSON><PERSON> αριθμός από το 1 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) έως το 12 (<PERSON><PERSON><PERSON><PERSON><PERSON>βρι<PERSON>).", "ad": "είναι ένας αριθμός στον κώδικα ημερομηνίας-<PERSON>ρας που χρησιμοποιείται από το Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(ημερομηνία_έναρξης; ημερομηνία_λήξης; [αργίες])", "d": "Αποδίδει τον αριθμό των ολόκληρων ημερών εργασίας μεταξύ δύο ημερομηνιών", "ad": "είναι ο αύξων αριθμός που αναπαριστά την αρχική ημερομηνία!είναι ο αύξων αριθμός που αναπαριστά την τελική ημερομηνία!είναι ένα προαιρετικό σύνολο ενός ή περισσότερων αυξόντων αριθμών ημερομηνίας που θα εξαιρεθούν από το ημερολόγιο εργασίας, όπως εθνικές και κινητές εορτές"}, "NETWORKDAYS.INTL": {"a": "(ημερομηνία_έναρξης; ημερομηνία_λήξης; [σαββατοκύριακο]; [αργίες])", "d": "Αποδίδει τον αριθμό των ολόκληρων ημερών εργασίας μεταξύ δύο ημερομηνιών με προσαρμοσμένες παραμέτρους σαββατοκύριακου", "ad": "είναι ο αύξων αριθμός που αναπαριστά την αρχική ημερομηνία!είναι ο αύξων αριθμός που αναπαριστά την τελική ημερομηνία!είναι ένας αριθμός σαββατοκύριακου ή μια συμβολοσειρά που καθορίζει πότε πέφτει σαββατοκύριακο!είναι ένα προαιρετικό σύνολο ενός ή περισσότερων αυξόντων αριθμών ημερομηνίας που θα εξαιρεθούν από το ημερολόγιο εργασίας, όπως εθνικές και κινητές εορτές"}, "NOW": {"a": "()", "d": "Αποδίδει την τρέχουσα ημερομηνία και ώρα με τη μορφή ημερομηνίας και ώρας.", "ad": ""}, "SECOND": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός)", "d": "Αποδίδει το δευτερ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, έν<PERSON><PERSON> αριθμός από το 0 έως το 59.", "ad": "είναι ένας αριθμός στον κώδικα ημερομηνίας-<PERSON><PERSON><PERSON><PERSON> που χρησιμοποιείται από το Spreadsheet Editor ή κείμενο σε μορφή ώρας, όπως το 16:48:23 ή 4:48:47 ΜΜ"}, "TIME": {"a": "(ώρα; λεπτό; δευτερόλεπτο)", "d": "Μετατρέπει τις ώρες, τα λεπτά και τα δευτερόλεπτα που δίνονται με τη μορφή αριθμών σε έναν αύξοντα αριθμό, με μορφή ώρας", "ad": "είναι ένας αριθμός από το 0 έως το 23 που αναπαριστά ώρα!είναι ένας αριθμός από το 0 έως το 59 που αναπαριστά λεπτό!είναι ένας αριθμός από το 0 έως το 59 που αναπαριστά δευτερόλεπτο"}, "TIMEVALUE": {"a": "(κείμενο_ώρας)", "d": "Μετατρέπει μια ώρα με τη μορφή κειμένου σε αύξοντα αριθμό ώρας, α<PERSON><PERSON> το 0 (12:00:00 ΠΜ) έως το 0,999988426 (11:59:59 ΜΜ). Μορφοποιεί τον αριθμό με μορφή ώρας μετά την πληκτρολόγηση του τύπου", "ad": "είναι μια ακολουθία χαρακτήρων κειμένου που αποδίδει την ώρα σε οποιαδήποτε από τις μορφές ώρας του Spreadsheet Editor (οι πληροφορίες ημερομηνίας στη συμβολοσειρά παραβλέπονται)"}, "TODAY": {"a": "()", "d": "Αποδίδει την τρέχουσα ημερομηνία με μορφή ημερομηνίας.", "ad": ""}, "WEEKDAY": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός; [τύπος_επιστροφής])", "d": "Αποδίδει έναν αριθμό από το 1 έως το 7 ο οποίος προσδιορίζει την ημέρα της εβδομάδας που αντιστοιχεί σε μια ημερομηνία.", "ad": "είναι ένας αριθμός που αναπαριστά μια ημερομηνία!είναι ένας αριθμός που παίρνει την τιμή 1 για το διάστημα Κυριακή=1 έως Σάββατο=7, την τιμή 2 για Δευτέρα=1 έως Κυριακή=7 ή την τιμή 3 για Δευτέρα=0 έως Κυριακή=6"}, "WEEKNUM": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός; [τύπος_επιστροφής])", "d": "Αποδίδει τον αριθμό της εβδομάδας μέσα στο έτος", "ad": "είναι ο κωδικός ημερομηνίας-<PERSON><PERSON><PERSON><PERSON> που χρησιμοποιείται από το Spreadsheet Editor για τους υπολογισμούς ημερομηνίας και ώρας!είναι ένας αριθμός (1 ή 2) που καθορίζει τον τύπο της επιστρεφόμενης τιμής"}, "WORKDAY": {"a": "(ημερομηνία_έναρξης; ημέρες; [αργίες])", "d": "Αποδίδει τον αύξοντα αριθμό της ημερομηνίας πριν ή έπειτα από έναν συγκεκριμένο αριθμό ημερών εργασίας", "ad": "είναι ο αύξων αριθμός που αναπαριστά την αρχική ημερομηνία!είναι ο αριθμός των ημερών που δεν είναι αργίες πριν ή μετά την ημερομηνία_έναρξης!είναι ένας προαιρετικό σύνολο ενός ή περισσότερων αυξόντων αριθμών ημερομηνίας που θα εξαιρεθούν από το ημερολόγιο εργασίας, ό<PERSON>ω<PERSON> εθνικές και κινητές εορτές"}, "WORKDAY.INTL": {"a": "(ημερομηνία_έναρξης; ημέρες; [σαββατοκύριακο]; [αργίες])", "d": "Αποδίδει τον αύξοντα αριθμό της ημερομηνίας πριν ή έπειτα από έναν συγκεκριμένο αριθμό ημερών εργασίας με προσαρμοσμένες παραμέτρους σαββατοκύριακου", "ad": "είναι ο αύξων αριθμός που αναπαριστά την αρχική ημερομηνία!είναι ο αριθμός των ημερών που δεν είναι σαββατοκύριακα ούτε αργίες πριν ή μετά την ημερομηνία_έναρξης!είναι ένας αριθμός σαββατοκύριακου ή μια συμβολοσειρά που καθορίζει πότε πέφτει σαββατοκύριακο!είναι ένας προαιρετικός πίνακας ενός ή περισσότερων αυξόντων αριθμών ημερομηνίας που θα εξαιρεθούν από το ημερολόγιο εργασίας, όπως εθνικές και κινητές εορτές"}, "YEAR": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός)", "d": "Αποδίδει το έτος μιας ημερομηνίας, ένας ακέραιος μεταξύ 1900 και 9999.", "ad": "είναι ένας αριθμός στον κώδικα ημερομηνίας-<PERSON>ρας που χρησιμοποιείται από το Spreadsheet Editor"}, "YEARFRAC": {"a": "(ημερομηνία_έναρξης; ημερομηνία_λήξης; [βάση])", "d": "Αποδίδει το κλάσμα του έτους που αναπαριστά τον αριθμό των ολόκληρων ημερών μεταξύ των παραμέτρων \"ημερομηνία_έναρξης\" και \"ημερομηνία_λήξης\"", "ad": "είναι ο αύξων αριθμός που αναπαριστά την αρχική ημερομηνία!είναι ο αύξων αριθμός που αναπαριστά την τελική ημερομηνία!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "BESSELI": {"a": "(x; n)", "d": "Αποδίδει την τροποποιημένη συνάρτηση Bessel In(x)", "ad": "είναι η τιμή για την οποία θα υπολογιστεί η συνάρτηση!είναι η τάξη της συνάρτησης Bessel"}, "BESSELJ": {"a": "(x; n)", "d": "Αποδίδει τη συνάρτηση Bessel Jn(x)", "ad": "είναι η τιμή για την οποία θα υπολογιστεί η συνάρτηση!είναι η τάξη της συνάρτησης Bessel"}, "BESSELK": {"a": "(x; n)", "d": "Αποδίδει την τροποποιημένη συνάρτηση Bessel Kn(x)", "ad": "είναι η τιμή για την οποία θα υπολογιστεί η συνάρτηση!είναι η τάξη της συνάρτησης"}, "BESSELY": {"a": "(x; n)", "d": "Αποδίδει τη συνάρτηση Bessel Yn(x)", "ad": "είναι η τιμή για την οποία θα υπολογιστεί η συνάρτηση!είναι η τάξη της συνάρτησης"}, "BIN2DEC": {"a": "(αριθμός)", "d": "Μετατρέ<PERSON><PERSON>ι έναν δυαδικό αριθμό σε δεκαδικό", "ad": "είναι ο δυαδικός αριθμός που θέλετε να μετατρέψετε"}, "BIN2HEX": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δυαδικό αριθμό σε δεκαεξαδικό", "ad": "είναι ο δυαδικός αριθμός που θέλετε να μετατρέψετε!είναι ο αριθμός των χαρακτήρων που θα χρησιμοποιηθούν"}, "BIN2OCT": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δυαδικό αριθμό σε οκταδικό", "ad": "είναι ο δυαδικός αριθμός που θέλετε να μετατρέψετε!είναι ο αριθμός των χαρακτήρων που θα χρησιμοποιηθούν"}, "BITAND": {"a": "(αριθμός1; αριθμός2)", "d": "Αποδίδει το λογικό 'και' με βάση τα bit δύο αριθμών", "ad": "είναι η δεκαδική αναπαράσταση του δυαδικού αριθμού που θέλετε να συμπεριλάβετε στον υπολογισμό!είναι η δεκαδική αναπαράσταση του δυαδικού αριθμού που θέλετε να συμπεριλάβετε στον υπολογισμό"}, "BITLSHIFT": {"a": "(αριθμός; πλήθος_μετατόπισης)", "d": "Αποδίδει έναν αριθμό ο οποίος έχει μετατοπιστεί προς τα αριστερά κατά τόσα bit όσα το  πλήθος_μετατόπισης", "ad": "είναι η δεκαδική αναπαράσταση του δυαδικού αριθμού που θέλετε να συμπεριλάβετε στον υπολογισμό!είναι το πλήθος των bit κατά το οποίο θα μετατοπιστεί ο αριθμός προς τα αριστερά"}, "BITOR": {"a": "(αριθμός1; αριθμός2)", "d": "Αποδίδει το λογικό 'ή' με βάση τα bit δύο αριθμών", "ad": "είναι η δεκαδική αναπαράσταση του δυαδικού αριθμού που θέλετε να συμπεριλάβετε στον υπολογισμό!είναι η δεκαδική αναπαράσταση του δυαδικού αριθμού που θέλετε να συμπεριλάβετε στον υπολογισμό"}, "BITRSHIFT": {"a": "(αριθμός; πλήθος_μετατόπισης)", "d": "Αποδίδει έναν αριθμό ο οποίος έχει μετατοπιστεί προς τα δεξιά κατά τόσα bit όσα το πλήθος_μετατόπισης", "ad": "είναι η δεκαδική αναπαράσταση του δυαδικού αριθμού που θέλετε να συμπεριλάβετε στον υπολογισμό!είναι το πλήθος των bit κατά το οποίο θα μετατοπιστούν τα bit του αριθμού προς τα δεξιά"}, "BITXOR": {"a": "(αριθμός1; αριθμός2)", "d": "Αποδίδει το λογικό 'αποκλειστικό ή' με βάση τα bit δύο αριθμών", "ad": "είναι η δεκαδική αναπαράσταση του δυαδικού αριθμού που θέλετε να συμπεριλάβετε στον υπολογισμό!είναι η δεκαδική αναπαράσταση του δυαδικού αριθμού που θέλετε να συμπεριλάβετε στον υπολογισμό"}, "COMPLEX": {"a": "(πραγματικ<PERSON>ς_αριθμός; μιγαδικός_αριθμός; [επίθημα])", "d": "Μετατρέπει τον πραγματικό και τον φανταστικό συντελεστή σε μιγαδικό αριθμό", "ad": "είναι ο πραγματικός συντελεστής του μιγαδικού αριθμού!είναι ο φανταστικός  συντελεστής του μιγαδικού αριθμού!είναι το πρόθεμα για το φανταστικό μέρος του μιγαδικού αριθμού"}, "CONVERT": {"a": "(αριθμός; από_μονάδα; σε_μονάδα)", "d": "Μετατρέ<PERSON><PERSON><PERSON> έναν αριθμό από ένα σύστημα μέτρησης σε άλλο", "ad": "είναι η τιμή στο όρισμα από_μονάδα που θα μετατραπεί!είναι οι μονάδες για τον αριθμό!είναι οι μονάδες για το αποτέλεσμα"}, "DEC2BIN": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δεκαδικό αριθμό σε δυαδικό", "ad": "είναι ο δεκαδικός ακέραιος που θέλετε να μετατρέψετε!είναι το πλήθος των χαρακτήρων που θα χρησιμοποιηθούν"}, "DEC2HEX": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δεκαδικό αριθμό σε δεκαεξαδικό", "ad": "είναι ο δεκαδικός ακέραιος που θέλετε να μετατρέψετε!είναι το πλήθος των χαρακτήρων που θα χρησιμοποιηθούν"}, "DEC2OCT": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δεκαδικό αριθμό σε οκταδικό", "ad": "είναι ο δεκαδικός ακέραιος που θέλετε να μετατρέψετε!είναι το πλήθος των χαρακτήρων που θα χρησιμοποιηθούν"}, "DELTA": {"a": "(αριθμός1; [αριθμός2])", "d": "Ελέγχει αν δύο αριθμοί είναι ίσοι", "ad": "είναι ο πρώτος αριθμός!είναι ο δεύτερος αριθμός"}, "ERF": {"a": "(κατώτερο_όριο; [ανώτερο_όριο])", "d": "Αποδίδει τη συνάρτηση σφάλματος", "ad": "είναι το κάτω όριο για την ολοκληρωτική ERF!είναι το ανώτερο όριο για την ολοκληρωτική ERF"}, "ERF.PRECISE": {"a": "(X)", "d": "Αποδίδει τη συνάρτηση σφάλματος", "ad": "είναι το χαμηλότερο όριο για την ολοκληρωτική ERF.PRECISE"}, "ERFC": {"a": "(x)", "d": "Αποδίδει τη συμπληρωματική συνάρτηση σφάλματος", "ad": "είναι το κάτω όριο για την ολοκληρωτική ERF"}, "ERFC.PRECISE": {"a": "(X)", "d": "Αποδίδει τη συμπληρωματική συνάρτηση σφάλματος", "ad": "είναι το χαμηλότερο όριο για την ολοκληρωτική ERFC.PRECISE"}, "GESTEP": {"a": "(αριθμός; [βήμα])", "d": "Ελέγχει αν ένας αριθμός είναι μεγαλύτερος από μια τιμή κατωφλίου", "ad": "είναι η τιμή που θα ελεγχθεί με βάση το βήμα!είναι η τιμή κατωφλίου"}, "HEX2BIN": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δεκαεξαδικό αριθμό σε δυαδικό", "ad": "είναι ο δεκαεξα<PERSON>ικός αριθμός που θέλετε να μετατρέψετε!είναι ο αριθμός των χαρακτήρων που θα χρησιμοποιηθούν"}, "HEX2DEC": {"a": "(αριθμός)", "d": "Μετατρέ<PERSON><PERSON>ι έναν δεκαεξαδικό αριθμό σε δεκαδικό", "ad": "είναι ο δεκα<PERSON><PERSON><PERSON><PERSON>ικός αριθμός που θέλετε να μετατρέψετε"}, "HEX2OCT": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δεκαεξαδικό αριθμό σε οκταδικό", "ad": "είναι ο δεκαεξα<PERSON>ικός αριθμός που θέλετε να μετατρέψετε!είναι ο αριθμός των χαρακτήρων που θα χρησιμοποιηθούν"}, "IMABS": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την απόλυτη τιμή (μέτρο) ενός μιγαδικού αριθμού", "ad": "είν<PERSON><PERSON> ένας μιγα<PERSON>ικ<PERSON>ς αριθμός του οποίου θέλετε την απόλυτη τιμή"}, "IMAGINARY": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τον φανταστικό συντελεστή ενός μιγαδικού αριθμού", "ad": "είνα<PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε τον φανταστικό συντελεστή"}, "IMARGUMENT": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει το όρισμα q, μια γωνία εκφρασμένη σε ακτίνια", "ad": "είνα<PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε το όρισμα"}, "IMCONJUGATE": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τον συζυγή μιγαδικ<PERSON> ενός μιγαδικού αριθμού", "ad": "είν<PERSON><PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε τον συζυγή"}, "IMCOS": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει το συνημίτονο ενός μιγαδικού αριθμού", "ad": "είνα<PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε το συνημίτονο"}, "IMCOSH": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει το υπερβολικό συνημίτονο ενός μιγαδικού αριθμού", "ad": "είναι ένας μιγαδικός αριθμός του οποίου θέλετε το υπερβολικό συνημίτονο"}, "IMCOT": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τη συνεφαπτομένη ενός μιγαδικού αριθμού", "ad": "είν<PERSON><PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε τη συνεφαπτομένη"}, "IMCSC": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τη συντέμνουσα ενός μιγαδικού αριθμού", "ad": "είν<PERSON><PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε τη συντέμνουσα"}, "IMCSCH": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την υπερβολική συντέμνουσα ενός μιγαδικού αριθμού", "ad": "είνα<PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε την υπερβολική συντέμνουσα"}, "IMDIV": {"a": "(μιγαδικ<PERSON>ς_αριθμός1; μιγαδικός_αριθμός2)", "d": "Αποδίδει το πηλίκο δύο μιγαδικών αριθμών", "ad": "είναι ο μιγαδικός αριθμητής ή διαιρετέος!είναι ο μιγαδικός παρονομαστής ή διαιρέτης"}, "IMEXP": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την εκθετική δύναμη ενός μιγαδικού αριθμού", "ad": "είνα<PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε την εκθετική δύναμη"}, "IMLN": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τον φυσικό λογάριθμο ενός μιγαδικού αριθμού", "ad": "είνα<PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε τον φυσικό λογάριθμο"}, "IMLOG10": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τον δεκαδικό λογάριθμο ενός μιγαδικού αριθμού", "ad": "είνα<PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε τον κοινό λογάριθμο"}, "IMLOG2": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει το λογάριθμο με βάση 2 ενός μιγαδικού αριθμού", "ad": "είν<PERSON><PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε το λογάριθμο με βάση το 2"}, "IMPOWER": {"a": "(μιγαδικ<PERSON>ς_αριθμός; αριθμός)", "d": "Αποδίδει έναν μιγαδικό αριθμό υψωμένο σε ακέραια δύναμη", "ad": "είναι ένας μιγαδικός αριθμός τον οποίο θέλετε να υψώσετε σε δύναμη!είναι η δύναμη στην οποία θέλετε να υψώσετε τον μιγαδικό αριθμό"}, "IMPRODUCT": {"a": "(μιγαδικός_αριθμός1; [μιγαδικός_αριθμός2]; ...)", "d": "Επιστρέφει το γινόμενο 1 έως 255 μιγαδικών αριθμών", "ad": "Inumber1, Inumber2,... είναι 1 έως 255 μιγαδικοί αριθμοί προς πολλαπλασιασμό."}, "IMREAL": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τον πραγματικό συντελεστή ενός μιγαδικού αριθμού", "ad": "είνα<PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε τον πραγματικό συντελεστή"}, "IMSEC": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την τέμνουσα ενός μιγαδικού αριθμού", "ad": "είν<PERSON><PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε την τέμνουσα"}, "IMSECH": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την υπερβολική τέμνουσα ενός μιγαδικού αριθμού", "ad": "είν<PERSON><PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε την υπερβολική τέμνουσα"}, "IMSIN": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει το ημίτονο ενός μιγαδικού αριθμού", "ad": "είναι ένας μιγαδικός αριθμός του οποίου θέλετε το ημίτονο"}, "IMSINH": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει το υπερβολικ<PERSON> ημίτονο ενός μιγαδικού αριθμού", "ad": "είναι ένας μιγαδικός αριθμός του οποίου θέλετε το υπερβολικό ημίτονο"}, "IMSQRT": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την τετραγωνική ρίζα ενός μιγαδικού αριθμού", "ad": "είν<PERSON><PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε την τετραγωνική ρίζα"}, "IMSUB": {"a": "(μιγαδικ<PERSON>ς_αριθμός1; μιγαδικός_αριθμός2)", "d": "Αποδίδει τη διαφορά δύο μιγαδικών αριθμών", "ad": "είναι ο μιγαδικός αριθμός από τον οποίο θα αφαιρεθεί ο μιγαδικός_αριθμός2!είναι ο μιγαδικός αριθμός που θα αφαιρεθεί από τον αριθμό μιγαδικός_αριθμός1"}, "IMSUM": {"a": "(μιγαδικός_αριθμός1; [μιγαδικός_αριθμός2]; ...)", "d": "Επιστρέφει το άθροισμα μιγαδικών αριθμών", "ad": "είναι 1 έως 255 μιγαδικοί αριθμοί που θα προστεθούν"}, "IMTAN": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την εφαπτομένη ενός μιγαδικού αριθμού", "ad": "είν<PERSON><PERSON> ένας μιγαδικός αριθμός του οποίου θέλετε την εφαπτομένη"}, "OCT2BIN": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON><PERSON> έναν οκταδικό αριθμό σε δυαδικό", "ad": "είναι ο οκταδικός αριθμός που θέλετε να μετατρέψετε!είναι ο αριθμός των χαρακτήρων που θα χρησιμοποιηθούν"}, "OCT2DEC": {"a": "(αριθμός)", "d": "Μετατρέ<PERSON><PERSON><PERSON> έναν οκταδικό αριθμό σε δεκαδικό", "ad": "είναι ο οκτα<PERSON>ικ<PERSON>ς αριθμός που θέλετε να μετατρέψετε"}, "OCT2HEX": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON><PERSON> έναν οκταδικό αριθμό σε δεκαεξαδικό", "ad": "είναι ο οκταδικός αριθμός που θέλετε να μετατρέψετε!είναι ο αριθμός των χαρακτήρων που θα χρησιμοποιηθούν"}, "DAVERAGE": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Αποδίδει τον μέσο όρο των τιμών μιας στήλης σε μια λίστα ή βάση δεδομένων που ικανοποιεί τις καθορισμένες συνθήκες", "ad": "είναι η περιοχή κελιών που σχηματίζουν τη λίστα ή τη βάση δεδομένων. Η βάση δεδομένων είναι μια λίστα από δεδομένα που σχετίζονται μεταξύ τους!είναι είτε η ετικέτα της στήλης σε εισαγωγικά είτε ένας αριθμός που αναπαριστά τη θέση της στήλης στη λίστα!είναι η περιοχή κελιών που περιέχει τις συνθήκες που καθορίζετε. Η περιοχή περιλαμβάνει μια ετικέτα στήλης και ένα κελί κάτω από την ετικέτα για την εισαγωγή συνθήκης"}, "DCOUNT": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Μετράει τα κελιά που περιέχουν αριθμούς στο πεδίο (στήλη) εγγραφών μιας βάσης δεδομένων, τα οποία ικανοποιούν τις καθορισμένες συνθήκες", "ad": "είναι η περιοχή κελιών που σχηματίζουν τη λίστα ή τη βάση δεδομένων. Η βάση δεδομένων είναι μια λίστα από δεδομένα που σχετίζονται μεταξύ τους.!είναι είτε η ετικέτα της στήλης σε εισαγωγικά είτε ένας αριθμός που αναπαριστά τη θέση της στήλης στη λίστα!είναι η περιοχή κελιών που περιέχει τις συνθήκες που καθορίζετε. Η περιοχή περιλαμβάνει μια ετικέτα στήλης και ένα κελί κάτω από την ετικέτα για την εισαγωγή συνθήκης"}, "DCOUNTA": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Μετράει μη κενά κελιά στο πεδίο (στήλη) εγγραφών μιας βάσης δεδομένων που ικανοποιούν τις καθορισμένες συνθήκες", "ad": "είναι η περιοχή κελιών που σχηματίζουν τη λίστα ή τη βάση δεδομένων. Η βάση δεδομένων είναι μια λίστα από δεδομένα που σχετίζονται μεταξύ τους.!είναι είτε η ετικέτα της στήλης σε εισαγωγικά είτε ένας αριθμός που αναπαριστά τη θέση της στήλης στη λίστα!είναι η περιοχή κελιών που περιέχει τις συνθήκες που καθορίζετε. Η περιοχή περιλαμβάνει μια ετικέτα στήλης και ένα κελί κάτω από την ετικέτα για την εισαγωγή συνθήκης"}, "DGET": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Εξαγάγει από βάση δεδομένων μια μόνο εγγραφή που ικανοποιεί τις καθορισμένες συνθήκες", "ad": "είναι η περιοχή κελιών που σχηματίζουν τη λίστα ή τη βάση δεδομένων. Η βάση δεδομένων είναι μια λίστα από δεδομένα που σχετίζονται μεταξύ τους.!είναι είτε η ετικέτα της στήλης σε εισαγωγικά είτε ένας αριθμός που αναπαριστά τη θέση της στήλης στη λίστα!είναι η περιοχή κελιών που περιέχει τις συνθήκες που καθορίζετε. Η περιοχή περιλαμβάνει μια ετικέτα στήλης και ένα κελί κάτω από την ετικέτα για την εισαγωγή μιας συνθήκης"}, "DMAX": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Αποδίδει τον μεγαλύτερο αριθμό που ικανοποιεί τις καθορισμένες συνθήκες στο πεδίο (στήλη) εγγραφών μιας βάσης δεδομένων", "ad": "είναι η περιοχή κελιών που σχηματίζουν τη λίστα ή τη βάση δεδομένων. Η βάση δεδομένων είναι μια λίστα από δεδομένα που σχετίζονται μεταξύ τους!είναι είτε η ετικέτα της στήλης σε εισαγωγικά είτε ένας αριθμός που αναπαριστά τη θέση της στήλης στη λίστα!είναι η περιοχή κελιών που περιέχει τις συνθήκες που καθορίζετε. Η περιοχή περιλαμβάνει μια ετικέτα στήλης και ένα κελί κάτω από την ετικέτα για την εισαγωγή μιας συνθήκης"}, "DMIN": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Αποδίδει τον μικρότερο αριθμό που ικανοποιεί τις καθορισμένες συνθήκες στο πεδίο (στήλη) εγγραφών μιας βάσης δεδομένων", "ad": "είναι η περιοχή κελιών που σχηματίζουν τη λίστα ή τη βάση δεδομένων. Η βάση δεδομένων είναι μια λίστα από δεδομένα που σχετίζονται μεταξύ τους.!είναι είτε η ετικέτα της στήλης σε εισαγωγικά είτε ένας αριθμός που αναπαριστά τη θέση της στήλης στη λίστα!είναι η περιοχή κελιών που περιέχει τις συνθήκες που καθορίζετε. Η περιοχή περιλαμβάνει μια ετικέτα στήλης και ένα κελί κάτω από την ετικέτα για την εισαγωγή συνθήκης"}, "DPRODUCT": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Πολλαπλασιάζει τις τιμές που ικανοποιούν τις καθορισμένες συνθήκες στο πεδίο (στήλη) εγγραφών της βάσης δεδομένων", "ad": "είναι η περιοχή κελιών που σχηματίζουν τη λίστα ή τη βάση δεδομένων. Η βάση δεδομένων είναι μια λίστα από δεδομένα που σχετίζονται μεταξύ τους.!είναι είτε η ετικέτα της στήλης σε εισαγωγικά είτε ένας αριθμός που αναπαριστά τη θέση της στήλης στη λίστα!είναι η περιοχή κελιών που περιέχει τις συνθήκες που καθορίζετε. Η περιοχή περιλαμβάνει μια ετικέτα στήλης και ένα κελί κάτω από την ετικέτα για την εισαγωγή συνθήκης"}, "DSTDEV": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Υπολογίζει την τυπική απόκλιση βάσει δείγματος από επιλεγμένες καταχωρήσεις της βάσης δεδομένων", "ad": "είναι η περιοχή κελιών που σχηματίζουν τη λίστα ή τη βάση δεδομένων. Η βάση δεδομένων είναι μια λίστα από δεδομένα που σχετίζονται μεταξύ τους!είναι είτε η ετικέτα της στήλης σε εισαγωγικά είτε ένας αριθμός που αναπαριστά τη θέση της στήλης στη λίστα!είναι η περιοχή κελιών που περιέχει τις συνθήκες που καθορίζετε. Η περιοχή περιλαμβάνει μια ετικέτα στήλης και ένα κελί κάτω από την ετικέτα για την εισαγωγή συνθήκης"}, "DSTDEVP": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Υπολογίζει την τυπική απόκλιση βάσει ολόκληρου του πληθυσμού από τις επιλεγμένες καταχωρήσεις της βάσης δεδομένων", "ad": "είναι η περιοχή κελιών που σχηματίζουν τη λίστα ή τη βάση δεδομένων. Η βάση δεδομένων είναι μια λίστα από δεδομένα που σχετίζονται μεταξύ τους!είναι είτε η ετικέτα της στήλης σε εισαγωγικά είτε ένας αριθμός που αναπαριστά τη θέση της στήλης στη λίστα!είναι η περιοχή κελιών που περιέχει τις συνθήκες που καθορίζετε. Η περιοχή περιλαμβάνει μια ετικέτα στήλης και ένα κελί κάτω από την ετικέτα για την εισαγωγή συνθήκης"}, "DSUM": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Προσθέτει τους αριθμούς στο πεδίο (στήλη) εγγραφών της βάσης δεδομένων που ικανοποιούν τις καθορισμένες συνθήκες", "ad": "είναι η περιοχή κελιών που σχηματίζουν τη λίστα ή τη βάση δεδομένων. Η βάση δεδομένων είναι μια λίστα από δεδομένα που σχετίζονται μεταξύ τους.!είναι είτε η ετικέτα της στήλης σε εισαγωγικά είτε ένας αριθμός που αναπαριστά τη θέση της στήλης στη λίστα!είναι η περιοχή κελιών που περιέχει τις συνθήκες που καθορίζετε. Η περιοχή περιλαμβάνει μια ετικέτα στήλης και ένα κελί κάτω από την ετικέτα για την εισαγωγή συνθήκης"}, "DVAR": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Υπολογίζει τη διακύμανση βάσει δείγματος από επιλεγμένες καταχωρήσεις βάσης δεδομένων", "ad": "είναι η περιοχή κελιών που σχηματίζουν τη λίστα ή τη βάση δεδομένων. Η βάση δεδομένων είναι μια λίστα από δεδομένα που σχετίζονται μεταξύ τους!είναι είτε η ετικέτα της στήλης σε εισαγωγικά είτε ένας αριθμός που αναπαριστά τη θέση της στήλης στη λίστα!είναι η περιοχή κελιών που περιέχει τις συνθήκες που καθορίζετε. Η περιοχή περιλαμβάνει μια ετικέτα στήλης και ένα κελί κάτω από την ετικέτα για την εισαγωγή συνθήκης"}, "DVARP": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Υπολογίζει τη διακύμανση βάσει ολόκληρου του πληθυσμού επιλεγμένων καταχωρήσεων βάσης δεδομένων", "ad": "είναι η περιοχή κελιών που σχηματίζουν τη λίστα ή τη βάση δεδομένων. Η βάση δεδομένων είναι μια λίστα από δεδομένα που σχετίζονται μεταξύ τους.!είναι είτε η ετικέτα της στήλης σε εισαγωγικά είτε ένας αριθμός που αναπαριστά τη θέση της στήλης στη λίστα!είναι η περιοχή κελιών που περιέχει τις συνθήκες που καθορίζετε. Η περιοχή περιλαμβάνει μια ετικέτα στήλης και ένα κελί κάτω από την ετικέτα για την εισαγωγή συνθήκης"}, "CHAR": {"a": "(αριθμός)", "d": "Αποδίδει το χαρακτήρα που καθορίζεται από τον κωδικό αριθμό του συνόλου χαρακτήρων του υπολογιστή σας", "ad": "είναι ένας αριθμός μεταξύ 1 και 255 που καθορίζει το χαρακτήρα που θέλετε"}, "CLEAN": {"a": "(κείμενο)", "d": "Καταρ<PERSON><PERSON><PERSON> όλους τους μη εκτυπώσιμους χαρακτήρες από το κείμενο", "ad": "είναι οποιαδήποτε πληροφορία φύλλου εργασίας, από την οποία θέλετε να καταργήσετε τους μη εκτυπώσιμους χαρακτήρες"}, "CODE": {"a": "(κείμενο)", "d": "Αποδίδει έναν αριθμητικ<PERSON> κωδικό για τον πρώτο χαρακτήρα μιας ακολουθίας, στο σύνολο χαρακτήρων του υπολογιστή σας", "ad": "είναι το κείμενο του οποίου θέλετε τον κωδικό για τον πρώτο χαρακτήρα"}, "CONCATENATE": {"a": "(κείμενο1; [κείμενο2]; ...)", "d": "Ενοποιεί συμβολοσειρές κειμένου σε μια συμβολοσειρά", "ad": "είναι 1 έως 255 συμβολοσειρές κειμένου που θα ενοποιηθούν σε μια συμβολοσειρά και μπορεί να είναι συμβολοσειρές κειμένου, αριθμο<PERSON> ή αναφορές σε ένα μόνο κελί"}, "CONCAT": {"a": "(κείμενο1; ...)", "d": "Συνενώνει μια λίστα ή μια περιοχή με συμβολοσειρές κειμένου", "ad": "είναι 1 έως 254 συμβολοσειρές κειμένου ή περιοχές που θα ενοποιηθούν σε μία συμβολοσειρά κειμένου"}, "DOLLAR": {"a": "(αριθμός; [δεκαδικοί])", "d": "Μετατρέπει αριθμό σε κείμενο με χρήση νομισματικής μορφής", "ad": "είναι ένας αριθμός, μια α<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> σε κελί που περιέχει αριθμό ή ένας τύπος που αποδίδει αριθμό!είναι ο αριθμός ψηφίων δεξιά της υποδιαστολής. Ο αριθμός στρογγυλοποιείται αν είναι απαραίτητο. Εάν παραλειφθεί, Δεκαδικοί =2"}, "EXACT": {"a": "(κείμενο1; κείμενο2)", "d": "Ελέγχει αν δύο ακολουθίες χαρακτήρων κειμένου είναι πανομοιότυπες και επιστρέφει TRUE ή FALSE. Η παράμετρος EXACT κάνει διάκριση πεζών-κεφαλαίων", "ad": "είναι η πρώτη ακολουθία χαρακτήρων κειμένου!είναι η δεύτερη ακολουθία χαρακτήρων κειμένου"}, "FIND": {"a": "(εύρεση_κειμένου; εντός_κειμένου; [αριθμός_έναρξης])", "d": "Επιστρέφει τη θέση έναρξης μιας ακολουθίας χαρακτήρων κειμένου μέσα σε μια άλλη ακολουθία χαρακτήρων κειμένου. Η παράμετρος FIND κάνει διάκριση πεζών-κεφαλαίων", "ad": "είναι το κείμενο που θέλετε να εντοπίσετε. Χρησιμοποιήστε εισαγωγικά (κενό κείμενο), για να βρείτε τον πρώτο χαρακτήρα στο Εντός_κειμένου. Δεν επιτρέπεται η χρήση χαρακτήρων μπαλαντέρ!είναι  το κείμενο το οποίο περιέχει το κείμενο που θέλετε να εντοπίσετε!καθορίζει το χαρακτήρα από τον οποίο θα αρχίσει η αναζήτηση. Ο πρώτος χαρακτήρας στο Εντός_κειμένου είναι ο αριθμός χαρακτήρα 1. <PERSON><PERSON><PERSON> παραλειφθεί, Έναρξη_αριθμός =1"}, "FINDB": {"a": "(εύρεση_κειμένου; εντός_κειμένου; [αριθμός_έναρξης])", "d": "Εντοπίζουν μία συμβολοσειρά κειμένου εντός μιας δεύτερης συμβολοσειράς κειμένου και επιστρέφουν τον αριθμό της θέσης έναρξης της πρώτης συμβολοσειράς κειμένου από τον πρώτο χαρακτήρα της δεύτερης συμβολοσειράς κειμένου, προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - Ιαπωνικά, Κινεζικά και Κορεατικά", "ad": "είναι το κείμενο που θέλετε να εντοπίσετε. Χρησιμοποιήστε εισαγωγικά (κενό κείμενο), για να βρείτε τον πρώτο χαρακτήρα στο Εντός_κειμένου. Δεν επιτρέπεται η χρήση χαρακτήρων μπαλαντέρ!είναι  το κείμενο το οποίο περιέχει το κείμενο που θέλετε να εντοπίσετε!καθορίζει το χαρακτήρα από τον οποίο θα αρχίσει η αναζήτηση. Ο πρώτος χαρακτήρας στο Εντός_κειμένου είναι ο αριθμός χαρακτήρα 1. <PERSON><PERSON><PERSON> παραλειφθεί, Έναρξη_αριθμός =1"}, "FIXED": {"a": "(αριθμός; [δεκαδικά_ψηφία]; [χωρίς_τελείες])", "d": "Στρογγυλοπ<PERSON><PERSON><PERSON><PERSON> έναν αριθμό στο καθορισμένο πλήθος δεκαδικών και επιστρέφει το αποτέλεσμα ως κείμενο με ή χωρίς τελείες", "ad": "είναι ο αριθμός που επιθυμείτε να στρογγυλοποιήσετε και να μετατρέψετε σε κείμενο!είναι ο αριθμός των ψηφίων δεξιά της υποδιαστολής. Εάν παραλειφθεί, δεκαδικά_ψηφία = 2!είναι μια λογική τιμή: όταν εμφανίζονται τελείες στο κείμενο που επιστρέφεται, η τιμή είναι TRUE και όταν δεν εμφανίζονται τελείες, η τιμή είναι FALSE ή παραλείπεται"}, "LEFT": {"a": "(κείμενο; [αριθμός_χαρακτήρων])", "d": "Αποδίδει το καθορισμένο πλήθος χαρακτήρων από την αρχή μιας ακολουθίας χαρακτήρων κειμένου", "ad": "είναι η ακολουθία χαρακτήρων κειμένου η οποία περιέχει τους χαρακτήρες που θέλετε να εξαγάγετε!καθορίζει το πλήθος των χαρακτήρων που θέλετε να εξαγάγει η LEFT. Παίρνει την τιμή 1 αν παραλειφθεί"}, "LEFTB": {"a": "(κείμενο; [αριθμός_χαρακτήρων])", "d": "Eπιστρέφει τον πρώτο χαρακτήρα ή τους χαρακτήρες μιας συμβολοσειράς κειμένου, με βάση τον αριθμό των byte που καθορίζετε, προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>κ<PERSON>, Κινεζικά και Κορεατικά", "ad": "είναι η ακολουθία χαρακτήρων κειμένου η οποία περιέχει τους χαρακτήρες που θέλετε να εξαγάγετε!καθορίζει το πλήθος των χαρακτήρων που θέλετε να εξαγάγει η LEFTB. Παίρνει την τιμή 1 αν παραλειφθεί"}, "LEN": {"a": "(κείμενο)", "d": "Αποδίδει το πλήθος των χαρακτήρων σε μια ακολουθία χαρακτήρων κειμένου", "ad": "είναι το κείμενο του οποίου θέλετε να βρείτε το μήκος. Τα διαστήματα υπολογίζονται ως χαρακτήρες"}, "LENB": {"a": "(κείμενο)", "d": "Eπιστρέφει το πλήθος των byte που χρησιμοποιούνται για την απεικόνιση των χαρακτήρων σε μια συμβολοσειρά κειμένου., προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - Ιαπωνικά, Κινεζικά και Κορεατικά", "ad": "είναι το κείμενο του οποίου θέλετε να βρείτε το μήκος. Τα διαστήματα υπολογίζονται ως χαρακτήρες"}, "LOWER": {"a": "(κείμενο)", "d": "Μετατρέπει όλα τα γράμματα μιας ακολουθίας χαρακτήρων κειμένου σε πεζά", "ad": "είναι το κείμενο που θέλετε να μετατρέψετε σε πεζά γράμματα. Οι χαρακτήρες του κειμένου που δεν αντιστοιχούν σε γράμματα δεν αλλάζουν"}, "MID": {"a": "(κείμεν<PERSON>; αριθμός_έναρξης; αριθμός_χαρακτήρων)", "d": "Αποδίδει τους χαρακτήρες από το μέσο μιας ακολουθίας χαρακτήρων κειμένου, εφ<PERSON><PERSON><PERSON><PERSON> καθοριστεί η αρχική θέση και το μήκος", "ad": "είναι η ακολουθία χαρακτήρων κειμένου από την οποία θέλετε να εξαγάγετε τους χαρακτήρες!είναι η θέση του πρώτου χαρακτήρα που θέλετε να εξαγάγετε. Ο πρώτος χαρακτήρας στο κείμενο ορίζεται ως 1!καθορίζει το πλήθος των χαρακτήρων που θα αποδοθούν από το κείμενο"}, "MIDB": {"a": "(κείμεν<PERSON>; αριθμός_έναρξης; αριθμός_χαρακτήρων)", "d": "Eπιστρέφει έναν συγκεκριμένο αριθμό χαρακτήρων από μια συμβολοσειρά κειμένου, αρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ας από μια καθορισμένη θέση, βά<PERSON>ει του αριθμού των byte που καθορίζετε., προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - Ιαπωνικά, Κινεζικά και Κορεατικά", "ad": "είναι η ακολουθία χαρακτήρων κειμένου από την οποία θέλετε να εξαγάγετε τους χαρακτήρες!είναι η θέση του πρώτου χαρακτήρα που θέλετε να εξαγάγετε. Ο πρώτος χαρακτήρας στο κείμενο ορίζεται ως 1!καθορίζει το πλήθος των χαρακτήρων που θα αποδοθούν από το κείμενο"}, "NUMBERVALUE": {"a": "(κείμενο; [διαχωριστικ<PERSON>_δεκαδικών]; [διαχωριστικό_ομάδων])", "d": "Μετατρέπει κείμενο σε αριθμό με τρόπο ανεξάρτητο από τις τοπικές ρυθμίσεις του συστήματος", "ad": "είναι η συμβολοσειρά που αντιπροσωπεύει τον αριθμό που θέλετε να μετατρέψετε!είναι ο χαρακτήρας που χρησιμοποιείται για το διαχωρισμό των δεκαδικών ψηφίων στη συμβολοσειρά!είναι ο χαρακτήρας που χρησιμοποιείται για διαχωρισμό ομάδων ψηφίων στη συμβολοσειρά"}, "PROPER": {"a": "(κείμενο)", "d": "Μετατρέπει το πρώτο γράμμα όλων των λέξεων μιας ακολουθίας χαρακτήρων κειμένου σε κεφαλαίο και όλα τα υπόλοιπα γράμματα σε πεζά", "ad": "είναι κείμενο μέσα σε εισαγω<PERSON><PERSON>κ<PERSON>, τύπος που επιστρέφει κείμενο ή αναφορά σε κελί που περιέχει κείμενο, το οποίο θέλετε να μετατρέψετε εν μέρει σε κεφαλαία"}, "REPLACE": {"a": "(παλιό_κείμενο; αριθμός_έναρξης; αριθμός_χαρακτήρων; νέο_κείμενο)", "d": "Αντικαθιστά χαρακτήρες μέσα σε κείμενο", "ad": "είναι το κείμενο στο οποίο θέλετε να αντικαταστήσετε ορισμένους χαρακτήρες!είναι η θέση του χαρακτήρα στο παλιό_κείμενο που θέλετε να αντικατασταθεί με το νέο_κείμενο!είναι το πλήθος των χαρακτήρων στο παλιό_κείμενο, τους οποίους θέλετε να αντικαταστήσετε!είναι το κείμενο που θα αντικαταστήσει τους χαρακτήρες στο παλιό_κείμενο"}, "REPLACEB": {"a": "(παλιό_κείμενο; αριθμός_έναρξης; αριθμός_χαρακτήρων; νέο_κείμενο)", "d": "Aντι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> μέρος μιας συμβολοσειράς κειμένου με άλλη συμβολοσειρά, βάσει του αριθμού των byte που καθορίζετε, προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - Ιαπωνικά, Κινεζικά και Κορεατικά", "ad": "είναι το κείμενο στο οποίο θέλετε να αντικαταστήσετε ορισμένους χαρακτήρες!είναι η θέση του χαρακτήρα στο παλιό_κείμενο που θέλετε να αντικατασταθεί με το νέο_κείμενο!είναι το πλήθος των χαρακτήρων στο παλιό_κείμενο, τους οποίους θέλετε να αντικαταστήσετε!είναι το κείμενο που θα αντικαταστήσει τους χαρακτήρες στο παλιό_κείμενο"}, "REPT": {"a": "(κείμενο; αριθμός_επαναλήψεων)", "d": "Επαναλαμβάνει κείμενο όσες φορές έχει οριστεί. Χρησιμοποιήστε την REPT, για να συμπληρώσετε ένα κελί, ε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>βάνοντας μια ακολουθία χαρακτήρων κειμένου.", "ad": "είναι το κείμενο που θέλετε να επαναλάβετε!είναι ένας θετικός αριθμός που καθορίζει τον αριθμό επαναλήψεων του κειμένου"}, "RIGHT": {"a": "(κείμενο; [αριθμός_χαρακτήρων])", "d": "Αποδίδει το καθορισμένο πλήθος χαρακτήρων από το τέλος μιας ακολουθίας χαρακτήρων κειμένου", "ad": "είναι η ακολουθία χαρακτήρων κειμένου η οποία περιέχει τους χαρακτήρες που θέλετε να εξαγάγετε!καθορίζει το πλήθος των χαρακτήρων που θέλετε να εξαγάγετε και παίρνει την τιμή 1 αν παραλειφθεί"}, "RIGHTB": {"a": "(κείμενο; [αριθμός_χαρακτήρων])", "d": "Αποδίδει τους τελευταίους χαρακτήρες μιας συμβολοσειράς κειμένου, με βάση τον αριθμό των byte που καθορίζετε, προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - Ιαπωνικά, Κινεζικά και Κορεατικά", "ad": "είναι η ακολουθία χαρακτήρων κειμένου η οποία περιέχει τους χαρακτήρες που θέλετε να εξαγάγετε!καθορίζει το πλήθος των χαρακτήρων που θέλετε να εξαγάγετε και παίρνει την τιμή 1 αν παραλειφθεί"}, "SEARCH": {"a": "(εύρεση_κειμένου; εντός_κειμένου; [αριθμός_έναρξης])", "d": "Αποδίδει τον αριθμό του χαρακτήρα όπου εντοπίζεται για πρώτη φορά ένας χαρακτήρας ή συμβολοσειρά κειμένου, απ<PERSON> αριστερά προς δεξιά (χωρίς διάκριση πεζών-κεφαλαίων)", "ad": "είναι το κείμενο που θέλετε να εντοπίσετε. Μπορείτε να χρησιμοποιήσετε τους χαρακτήρες μπαλαντέρ (?) και (*). Πληκτρολογήστε ~? και ~* αν θέλετε να εντοπίσετε τους χαρακτήρες ? και *.!είναι το κείμενο στο οποίο θέλετε να αναζητηθεί η παράμετρος Εύρεση_κειμένου!είναι ο αριθμός θέσης του χαρακτήρα στο Εντός_κειμένου, μετρώντας από τα αριστερά, από τον οποίο θέλετε να αρχίσει η αναζήτηση. Εάν παραλειφθεί, ορ<PERSON><PERSON><PERSON>ται ως 1"}, "SEARCHB": {"a": "(εύρεση_κειμένου; εντός_κειμένου; [αριθμός_έναρξης])", "d": "Εντοπίζουν μία συμβολοσειρά κειμένου εντός μιας δεύτερης συμβολοσειράς κειμένου και επιστρέφουν τον αριθμό της θέσης έναρξης της πρώτης συμβολοσειράς κειμένου από τον πρώτο χαρακτήρα της δεύτερης συμβολοσειράς κειμένου, προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - Ιαπωνικά, Κινεζικά και Κορεατικά", "ad": "είναι το κείμενο που θέλετε να εντοπίσετε. Μπορείτε να χρησιμοποιήσετε τους χαρακτήρες μπαλαντέρ (?) και (*). Πληκτρολογήστε ~? και ~* αν θέλετε να εντοπίσετε τους χαρακτήρες ? και *.!είναι το κείμενο στο οποίο θέλετε να αναζητηθεί η παράμετρος Εύρεση_κειμένου!είναι ο αριθμός θέσης του χαρακτήρα στο Εντός_κειμένου, μετρώντας από τα αριστερά, από τον οποίο θέλετε να αρχίσει η αναζήτηση. Εάν παραλειφθεί, ορ<PERSON><PERSON><PERSON>ται ως 1"}, "SUBSTITUTE": {"a": "(κείμενο; παλιό_κείμενο; νέο_κείμενο; [αριθμός_παρουσίας])", "d": "Αντικαθιστά παλιό κείμενο με νέο κείμενο σε ακολουθία χαρακτήρων κειμένου", "ad": "είναι το κείμενο ή η αναφορά σε κελί με κείμενο, του οποίου θέλετε να αντικαταστήσετε χαρακτήρες!είναι το κείμενο που θέλετε να αντικαταστήσετε. Εάν τα πεζά και τα κεφαλαία του παλιό_κείμενο δεν ταιριάζουν με του κειμένου, η SUBSTITUTE δεν θα αντικαταστήσει το κείμενο.!είναι το κείμενο με το οποίο θέλετε να αντικαταστήσετε το παλιό_κείμενο!καθορίζει ποια εμφάνιση στο παλιό_κείμενο θέλετε να αντικαταστήσετε. <PERSON><PERSON><PERSON> παραλειφθεί, γίνεται αντικατάσταση σε όλες τις περιπτώσεις εμφάνισης του παλιό_κείμενο"}, "T": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι κείμενο και, αν ναι, αποδίδει το κείμενο, αλλ<PERSON><PERSON><PERSON> αποδίδει εισαγωγικά (κενό κείμενο)", "ad": "είναι η τιμή που θέλετε να ελέγξετε"}, "TEXT": {"a": "(τιμή; μορφοποίηση_κειμένου)", "d": "Μετατρέπει μια τιμή σε κείμενο με μια συγκεκριμένη μορφή αριθμού", "ad": "είναι ένας αριθμός, έν<PERSON><PERSON> τύπος που αξιολογείται σε μια αριθμητική τιμή ή μια αναφορά σε κελί που περιέχει μια αριθμητική τιμή!είναι ένας αριθμός με μορφή κειμένου από το πλαίσιο “Κατηγορία” στην καρτέλα “Αριθμός” του παραθύρου διαλόγου “Μορφοποίηση κελιών”"}, "TEXTJOIN": {"a": "(οριοθέτης; παράβλεψη_κενό; κείμενο1; ...)", "d": "Συνενώνει μια λίστα ή μια περιοχή με συμβολοσειρές κειμένου με τη χρήση οριοθέτη", "ad": " Χαρακτή<PERSON><PERSON>ς ή συμβολοσειρά για εισαγωγή μεταξύ κάθε στοιχείου κειμένου!εάν ΕΊΝΑΙ TRUE(προεπιλογή), παραβλέπει κενά κελιά!είναι 1 έως 252 συμβολοσειρές κειμένου ή περιοχές για σύνδεση"}, "TRIM": {"a": "(κείμενο)", "d": "Καταργεί τα διαστήματα από το κείμενο, διατηρώντας όμως τα μονά διαστήματα μεταξύ των λέξεων", "ad": "είναι το κείμενο του οποίου θέλετε να καταργηθούν τα διαστήματα"}, "UNICHAR": {"a": "(αριθμός)", "d": "Αποδίδει τον χαρακτήρα Unicode που αντιστοιχεί στην δεδομένη αριθμητική τιμή", "ad": "είναι ο αριθμός Unicode που αντιστοιχεί σε έναν χαρακτήρα"}, "UNICODE": {"a": "(κείμενο)", "d": "Αποδίδει τον αριθμό (σημε<PERSON><PERSON> κώδικα) που αντιστοιχεί στον πρώτο χαρακτήρα του κειμένου", "ad": "είναι ο χαρακτήρας του οποίου θέλετε την τιμή Unicode"}, "UPPER": {"a": "(κείμενο)", "d": "Μετατρέπει κείμενο σε κεφαλα<PERSON>α γράμματα", "ad": "είναι το κείμενο που θέλετε να μετατραπεί σε κεφαλαία, μια αναφορά ή μια ακολουθία χαρακτήρων κειμένου"}, "VALUE": {"a": "(κείμενο)", "d": "Μετατρέπει σε αριθμό μια ακολουθία χαρακτήρων κειμένου που αναπαριστά αριθμό", "ad": "είναι το κείμενο σε εισαγω<PERSON><PERSON><PERSON><PERSON> ή αναφορά σε κελί που περιέχει το κείμενο που θέλετε να μετατρέψετε"}, "AVEDEV": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τον μέσο όρο των απόλυτων αποκλίσεων των σημείων δεδομένων από τον μέσο τους. Τα ορίσματα μπορεί να είναι αριθμοί ή ονόματα, καθώς και πίνακες ή αναφορές που περιέχουν αριθμούς.", "ad": "είναι 1 έως 255 ορίσματα για τα οποία θέλετε τον μέσο όρο των απόλυτων αποκλίσεων"}, "AVERAGE": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τον αριθμητικό μέσο όρο των ορισμάτων του, τα οποία μπορεί να είναι αριθμοί ή ονόματα, πίνακες ή αναφορές που περιέχουν αριθμούς", "ad": "είναι 1 έως 255 αριθμητικά ορίσματα για τα οποία αναζητάτε τον μέσο όρο"}, "AVERAGEA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Αποδίδει τον μέσο όρο (αριθμητικό μέσο) των ορισμάτων του, θεωρώντας το κείμενο και τις τιμές FALSE ως 0 και τις τιμές TRUE ως 1. Τα ορίσματα μπορεί να είναι αριθμοί, ονόματα πίνακες ή αναφορές", "ad": "είναι 1 έως 255 ορίσματα των οποίων θέλετε τον μέσο όρο"}, "AVERAGEIF": {"a": "(περιοχή; κριτήρια; [περιοχή_μέσου_όρου])", "d": "Βρίσκει τον μέσο όρο (αριθμητικ<PERSON> μέσο όρο) για τα κελιά που καθορίζονται από μια δεδομένη συνθήκη ή κριτήριο", "ad": "είναι η περιοχή κελιών που θέλετε να αξιολογηθούν!είναι η συνθήκη ή τα κριτήρια με τη μορφή αριθμού, παρ<PERSON><PERSON><PERSON><PERSON><PERSON>ης ή κειμένου που ορίζει ποια κελιά θα χρησιμοποιηθούν για την εύρεση του μέσου όρου!είναι τα πραγματικά κελιά που θα χρησιμοποιηθούν για την εύρεση του μέσου όρου. <PERSON><PERSON><PERSON> παραλειφθεί, χρησιμοποιούνται τα κελιά στην περιοχή"}, "AVERAGEIFS": {"a": "(περιοχή_μέσου_όρου; περιοχή_κριτηρίων; κριτήρια; ...)", "d": "Βρίσκει τον μέσο όρο (αριθμητικ<PERSON> μέσο όρο) για τα κελιά που καθορίζονται από ένα δεδομένο σύνολο συνθηκών ή κριτηρίων", "ad": "είναι τα κελιά που θα χρησιμοποιηθούν για τον υπολογισμό του μέσου όρου.!είναι η περιοχή κελιών που θέλετε να αναλύσετε για τη συγκεκριμένη συνθήκη!είναι η συνθήκη ή το κριτήριο με τη μορφή αριθμού, παράστασης ή κειμένου που καθορίζει ποια κελιά θα χρησιμοποιηθούν για την εύρεση του μέσου όρου"}, "BETADIST": {"a": "(x; άλφα; βήτα; [A]; [B])", "d": "Αποδίδει τη συνάρτηση πυκνότητας αθροιστικής πιθανότητας βήτα", "ad": "είναι η τιμή μεταξύ του Α και του Β στην οποία θα εκτιμηθεί η συνάρτηση!είναι μια παράμετρος κατανομής και πρέπει να είναι μεγαλύτερη από 0!είναι μια παράμετρος κατανομής και πρέπει να είναι μεγαλύτερη από 0!είναι ένα προαιρετικό κατώτατο όριο στο διάστημα του x. Εάν παραλειφθεί, το Α = 0.!είναι ένα προαιρετικό ανώτατο όριο στο διάστημα του x. Εάν παραλειφθεί, το Β = 1"}, "BETAINV": {"a": "(πιθανότητα; άλφα; βήτα; [A]; [B])", "d": "Αποδίδει το αντίστροφο της συνάρτησης αθροιστικής πυκνότητας πιθανότητας βήτα (BETADIST)", "ad": "είναι πιθανότητα σχετική με την κατανομή βήτα!είναι μια παράμετρος κατανομής και πρέπει να είναι μεγαλύτερη από 0!είναι μια παράμετρος κατανομής και πρέπει να είναι μεγαλύτερη από 0!είναι ένα προαιρετικό κατώτατο όριο στο διάστημα του x. <PERSON><PERSON>ν παραλειφθεί, το Α = 0.!είναι ένα προαιρετικό ανώτατο όριο στο διάστημα του x. <PERSON><PERSON><PERSON> παραλειφθεί, το Β = 1"}, "BETA.DIST": {"a": "(x; άλφα; βήτα; αθροιστική; [A]; [B])", "d": "Αποδίδει τη συνάρτηση κατανομής πιθανότητας βήτα", "ad": "είναι η τιμή μεταξύ του A και του B στην οποία θα εκτιμηθεί η συνάρτηση!είναι μια παράμετρος κατανομής και πρέπει να είναι μεγαλύτερη από 0!είναι μια παράμετρος κατανομής και πρέπει να είναι μεγαλύτερη από 0!είναι μια λογική τιμή: παίρνει την τιμή TRUE για τη συνάρτηση αθροιστικής κατανομής και την τιμή FALSE για τη συνάρτηση πυκνότητας πιθανότητας!είναι ένα προαιρετικό κατώτατο όριο στο διάστημα του x. <PERSON><PERSON>ν παραλειφθεί, A = 0!είναι ένα προαιρετικό ανώτατο όριο στο διάστημα του x. Εάν παραλειφθεί, B = 1"}, "BETA.INV": {"a": "(πιθανότητα; άλφα; βήτα; [A]; [B])", "d": "Αποδίδει το αντίστροφο της συνάρτησης αθροιστικής πυκνότητας πιθανότητας βήτα (BETA.DIST)", "ad": "είναι πιθανότητα σχετική με την κατανομή βήτα!είναι μια παράμετρος κατανομής και πρέπει να είναι μεγαλύτερη από 0!είναι μια παράμετρος κατανομής και πρέπει να είναι μεγαλύτερη από 0!είναι ένα προαιρετικό κατώτατο όριο στο διάστημα του x. <PERSON><PERSON>ν παραλειφθεί, το Α = 0.!είναι ένα προαιρετικό ανώτατο όριο στο διάστημα του x. <PERSON><PERSON><PERSON> παραλειφθεί, το Β = 1"}, "BINOMDIST": {"a": "(αριθμός; δοκιμές; πιθανότητα; αθροιστική)", "d": "Αποδίδει την πιθανότητα διωνυμικής κατανομής μεμονωμένου όρου", "ad": "είναι το πλήθος των επιτυχιών στις δοκιμές!είναι το πλήθος των ανεξάρτητων δοκιμών!είναι η πιθανότητα επιτυχίας κάθε δοκιμής!είναι μια λογική τιμή που παίρνει τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή FALSE για τη συνάρτηση πιθανότητας"}, "BINOM.DIST": {"a": "(αριθμός_επιτυχιών; δοκιμές; πιθανότητα_επιτυχίας; αθροιστική)", "d": "Αποδίδει την πιθανότητα διωνυμικής κατανομής μεμονωμένου όρου", "ad": "είναι το πλήθος των επιτυχιών στις δοκιμές!είναι το πλήθος των ανεξάρτητων δοκιμών!είναι η πιθανότητα επιτυχίας κάθε δοκιμής!είναι μια λογική τιμή που παίρνει τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή FALSE για τη συνάρτηση πιθανότητας"}, "BINOM.DIST.RANGE": {"a": "(δοκιμές; πιθανότητα_ε; πλήθος_ε; [πλήθος_ε2])", "d": "Αποδίδει την πιθανότητα ενός αποτελέσματος δοκιμής που προκύπτει από τη χρήση διωνυμικής κατανομής", "ad": "είναι το πλήθος των ανεξάρτητων δοκιμών!είναι η πιθανότητα επιτυχίας σε κάθε δοκιμή!είναι το πλήθος των επιτυχιών στις δοκιμές!εάν παρέχεται, η συνάρτηση αυτή αποδίδει την πιθανότητα το πλήθος των επιτυχών δοκιμών να βρίσκεται μεταξύ των αριθμών πλήθος_ε και πλήθος_ε2"}, "BINOM.INV": {"a": "(δοκιμές; πιθανότητα_επιτυχίας; άλφα)", "d": "Αποδίδει τη μικρότερη τιμή της οποίας η αθροιστική διωνυμική κατανομή είναι μεγαλύτερη ή ίση της τιμής ενός κριτηρίου", "ad": "είναι ο αριθμός των δοκιμώ<PERSON>!είναι η πιθανότητα επιτυχίας κάθε δοκιμής, ένας αριθμός στο κλειστό διάστημα 0 έως 1!είναι η τιμή κριτηρίου, ένας αριθμός στο κλειστό διάστημα 0 έως 1"}, "CHIDIST": {"a": "(x; βαθμοί_ελευθερίας)", "d": "Αποδίδει την πιθανότητα της δεξιάς πλευράς της κατανομής Χ-τετράγωνο", "ad": "είναι η τιμή στην οποία θέλετε να εκτιμήσετε την κατανομή, ένας μη αρνητικός αριθμός!είναι ο αριθμός βαθμών ελευθερίας, έν<PERSON><PERSON> αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10"}, "CHIINV": {"a": "(πιθανότητα; βαθμοί_ελευθερίας)", "d": "Αποδίδει το αντίστροφο της πιθανότητας της δεξιάς πλευράς της κατανομής Χ-τετράγωνο", "ad": "είναι πιθανότητα σχετική με την κατανομή Χ-τετρ<PERSON>γων<PERSON>, μια τιμή στο κλειστό διάστημα 0 έως 1!είναι ο αριθμός βαθμών ελευθερίας, <PERSON><PERSON><PERSON><PERSON> αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10"}, "CHITEST": {"a": "(πραγματικ<PERSON>_εύρος; αναμενόμενο_εύρος)", "d": "Επιστρέφει τον έλεγχο της ανεξαρτησίας: την τιμή από την κατανομή x-τετράγωνο για τη στατιστική και τους ανάλογους βαθμούς ελευθερίας", "ad": "είναι η περιοχή δεδομένων με τις παρατηρήσεις που θα ελεγχθούν έναντι αναμενόμενων τιμών!είναι η περιοχή δεδομένων που περιέχει το λόγο του γινομένου των αθροισμάτων των γραμμών και των στηλών προς το γενικό άθροισμα"}, "CHISQ.DIST": {"a": "(x; βαθμοί_ελευθερίας; αθροιστική)", "d": "Αποδίδει την πιθανότητα αριστερής πλευράς της κατανομής Χ-τετράγωνο", "ad": "είναι η τιμή, στην οποία θέλετε να εκτιμήσετε την κατανομή, ένας μη αρνητικός αριθμός!είναι ο αριθμός βαθμών ελευθερίας, <PERSON><PERSON><PERSON><PERSON> αριθμός μεταξύ 1 και 10^10, εκτός του 10^10!είναι μια λογική τιμή που παίρνει την τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή την τιμή FALSE για τη συνάρτηση πυκνότητας πιθανότητας"}, "CHISQ.DIST.RT": {"a": "(x; βαθμοί_ελευθερίας)", "d": "Αποδίδει την πιθανότητα δεξιάς πλευράς της κατανομής Χ-τετράγωνο", "ad": "είναι η τιμή στην οποία θέλετε να εκτιμήσετε την κατανομή, ένας μη αρνητικός αριθμός!είναι ο αριθμός βαθμών ελευθερίας, έ<PERSON><PERSON><PERSON> αριθμός μεταξύ 1 και 10^10, εκτός του 10^10"}, "CHISQ.INV": {"a": "(πιθανότητα; βαθμοί_ελευθερίας)", "d": "Αποδίδει το αντίστροφο της πιθανότητας αριστερής πλευράς της κατανομής Χ-τετράγωνο", "ad": "είναι πιθανότητα σχετική με την κατανομή Χ-τετρ<PERSON>γων<PERSON>, μια τιμή στο κλειστό διάστημα 0 έως 1!είναι ο αριθμός βαθμών ελευθερίας, <PERSON><PERSON><PERSON><PERSON> αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10"}, "CHISQ.INV.RT": {"a": "(πιθανότητα; βαθμοί_ελευθερίας)", "d": "Αποδίδει το αντίστροφο της πιθανότητας δεξιάς πλευράς της κατανομής Χ-τετράγωνο", "ad": "είναι πιθανότητα σχετική με την κατανομή Χ-τετρ<PERSON>γων<PERSON>, μια τιμή στο κλειστό διάστημα 0 έως 1!είναι ο αριθμός βαθμών ελευθερίας, <PERSON><PERSON><PERSON><PERSON> αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10"}, "CHISQ.TEST": {"a": "(πραγματικ<PERSON>_εύρος; αναμενόμενο_εύρος)", "d": "Αποδίδει τον έλεγχο της ανεξαρτησίας: την τιμή από την κατανομή x-τετράγωνο για τη στατιστική και τους ανάλογους βαθμούς ελευθερίας", "ad": "είναι η περιοχή δεδομένων με τις παρατηρήσεις που θα ελεγχθούν έναντι αναμενόμενων τιμών!είναι η περιοχή δεδομένων που περιέχει το λόγο του γινομένου των αθροισμάτων των γραμμών και των στηλών προς το γενικό άθροισμα"}, "CONFIDENCE": {"a": "(άλφα; τυπική_απόκλιση; μέγεθος)", "d": "Αποδίδει το διάστημα εμπιστοσύνης για τον αριθμητικό μέσο ενός πληθυσμού χρησιμοποιώντας μια κανονική κατανομή", "ad": "είναι το επίπεδο σημαντικότητας που χρησιμοποιείται για τον υπολογισμό του επιπέδου εμπιστοσύνης, <PERSON><PERSON><PERSON><PERSON> αριθμός μεγαλύτερος του 0 και μικρότερος του 1!είναι η τυπική απόκλιση του πληθυσμού για την περιοχή δεδομένων και θεωρείται γνωστή. Η παράμετρος τυπική_απόκλιση πρέπει να είναι μεγαλύτερη από το 0!είναι το μέγεθος του δείγματος"}, "CONFIDENCE.NORM": {"a": "(άλφα; τυπική_απόκλιση; μέγεθος)", "d": "Αποδίδει το διάστημα εμπιστοσύνης για τον αριθμητικό μέσο ενός πληθυσμού, χρησιμοποιώντας κανονική κατανομή", "ad": "είναι το επίπεδο σημαντικότητας που χρησιμοποιείται για τον υπολογισμό του επιπέδου εμπιστοσύνης, <PERSON><PERSON><PERSON><PERSON> αριθμός μεγαλύτερος του 0 και μικρότερος του 1!είναι η τυπική απόκλιση του πληθυσμού για την περιοχή δεδομένων και θεωρείται γνωστή. Η παράμετρος Standard_dev πρέπει να είναι μεγαλύτερη από το 0!είναι το μέγεθος του δείγματος"}, "CONFIDENCE.T": {"a": "(άλφα; τυπική_απόκλιση; μέγεθος)", "d": "Αποδίδει το διάστημα εμπιστοσύνης για τον αριθμητικό μέσο ενός πληθυσμού, χρησιμοποιώντας κατανομή T Student", "ad": "είναι το επίπεδο σημαντικότητας που χρησιμοποιείται για τον υπολογισμό του επιπέδου εμπιστοσύνης, <PERSON><PERSON><PERSON><PERSON> αριθμός μεγαλύτερος του 0 και μικρότερος του 1!είναι η τυπική απόκλιση του πληθυσμού για την περιοχή δεδομένων και θεωρείται γνωστή. Η παράμετρος \"τυπική_απόκλιση\" πρέπει να είναι μεγαλύτερη από το 0!είναι το μέγεθος του δείγματος"}, "CORREL": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει το συντελεστή συσχέτισης δύο συνόλων δεδομένων", "ad": "είναι μια περιοχή κελιών με τιμές. Οι τιμές θα πρέπει να είναι αριθμοί, ονόματα, πίνακες ή αναφορές που περιέχουν αριθμούς.!είναι μια δεύτερη περιοχή κελιών με τιμές. Οι τιμές θα πρέπει να είναι αριθμοί, ονόματα, πίνακες ή αναφορές που περιέχουν αριθμούς"}, "COUNT": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Μετράει το πλήθος των κελιών σε μια περιοχή που περιέχει αριθμούς", "ad": "είναι 1 έως 255 ορίσματα που μπορούν να περιέχουν ή να αναφέρονται σε ποικιλία τύπων δεδομένων, από τα οποία ωστόσο καταμετρούνται μόνο οι αριθμοί"}, "COUNTA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Μετράει το πλήθος των κελιών μιας περιοχής που δεν είναι κενά", "ad": "είναι 1 έως 255 ορίσματα που αναπαριστούν τις τιμές και τα κελιά που θέλετε να καταμετρήσετε. Οι τιμές μπορεί να αντιστοιχούν σε οποιονδήποτε τύπο πληροφορίας"}, "COUNTBLANK": {"a": "(περιοχή)", "d": "Μετρά το πλήθος των κενών κελιών σε μια καθορισμένη περιοχή", "ad": "είναι η περιοχή της οποίας τα κενά κελιά επιθυμείτε να καταμετρήσετε"}, "COUNTIF": {"a": "(περιοχή; κριτήρια)", "d": "Μετράει το πλήθος των κελιών, σε μια περιοχή που ικανοποιεί την καθορισμένη συνθήκη", "ad": "είναι η περιοχή κελιών στην οποία επιθυμείτε να καταμετρήσετε τα μη κενά κελιά!είναι η συνθήκη με μορφή αριθμού, παράστασης ή κειμένου που ορίζει ποια κελιά θα καταμετρηθούν"}, "COUNTIFS": {"a": "(περιοχή_κριτηρίων; κριτήρια; ...)", "d": "Μετράει τα κελιά που καθορίζονται από ένα δεδομένο σύνολο συνθηκών ή κριτηρίων", "ad": "είναι η περιοχή κελιών που θέλετε να αναλύσετε για τη συγκεκριμένη συνθήκη!είναι η συνθήκη με τη μορφή αριθμού, παράστασης ή κειμένου που ορίζει ποια κελιά θα απαριθμηθούν"}, "COVAR": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει τη συνδιακύμανση, το μέσο όρο των γινομένων των αποκλίσεων για κάθε ζεύγος σημείων δεδομένων σε δύο σύνολα δεδομένων", "ad": "είναι η πρώτη περιοχή κελιών με ακέραιους αριθμούς και πρέπει να είναι αριθμοί, πίνακες ή αναφορές που περιέχουν αριθμούς!είναι η δεύτερη περιοχή κελιών με ακέραιους αριθμούς και πρέπει να είναι αριθμοί, πίνακες ή αναφορές που περιέχουν αριθμούς"}, "COVARIANCE.P": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει τη συνδιακύμανση, το μέσο όρο των γινομένων των αποκλίσεων για κάθε ζεύγος σημείων δεδομένων σε δύο σύνολα δεδομένων", "ad": "είναι η πρώτη περιοχή κελιών με ακέραιους αριθμούς και πρέπει να είναι αριθμοί, πίνακες ή αναφορές που περιέχουν αριθμούς!είναι η δεύτερη περιοχή κελιών με ακέραιους αριθμούς και πρέπει να είναι αριθμοί, πίνακες ή αναφορές που περιέχουν αριθμούς"}, "COVARIANCE.S": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει τη συνδιακύμανση δείγματος, το μέσο όρο των γινομένων των αποκλίσεων για κάθε ζεύγος σημείων δεδομένων σε δύο σύνολα δεδομένων", "ad": "είναι η πρώτη περιοχή κελιών με ακέραιους αριθμούς και πρέπει να είναι αριθμοί, πίνακες ή αναφορές που περιέχουν αριθμούς!είναι η δεύτερη περιοχή κελιών με ακέραιους αριθμούς και πρέπει να είναι αριθμοί, πίνακες ή αναφορές που περιέχουν αριθμούς"}, "CRITBINOM": {"a": "(δοκιμές; πιθανότητα; άλφα)", "d": "Αποδίδει τη μικρότερη τιμή της οποίας η αθροιστική διωνυμική κατανομή είναι μεγαλύτερη ή ίση της τιμής ενός κριτηρίου", "ad": "είναι ο αριθμός των δοκιμώ<PERSON>!είναι η πιθανότητα επιτυχίας κάθε δοκιμής, ένας αριθμός στο κλειστό διάστημα 0 έως 1!είναι η τιμή κριτηρίου, ένας αριθμός στο κλειστό διάστημα 0 έως 1"}, "DEVSQ": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει το άθροισμα των τετραγώνων των αποκλίσεων των σημείων δεδομένων από τον αριθμητικό μέσο του δείγματός τους", "ad": "είναι 1 έως 255 ορίσματα ή ένας πίνακας ή μια αναφορά σε πίνακα, των οποίων θέλετε να υπολογίσετε το άθροισμα των τετραγωνικών αποκλίσεων"}, "EXPONDIST": {"a": "(x; λάμδα; αθροιστική)", "d": "Αποδίδει την εκθετική κατανομή", "ad": "είναι η τιμή της συνάρτησης, ένας μη αρνητικός αριθμός!είναι η τιμή παραμέτρου, ένας θετικός αριθμός!είναι μια λογική τιμή που παίρνει την τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή την τιμή FALSE για την συνάρτηση πυκνότητας πιθανότητας"}, "EXPON.DIST": {"a": "(x; λάμδα; αθροιστική)", "d": "Αποδίδει την εκθετική κατανομή", "ad": "είναι η τιμή της συνάρτησης, ένας μη αρνητικός αριθμός!είναι η τιμή παραμέτρου, ένας θετικός αριθμός!είναι μια λογική τιμή που παίρνει την τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή την τιμή FALSE για την συνάρτηση πυκνότητας πιθανότητας"}, "FDIST": {"a": "(x; βαθμοί_ελευθερίας1; βαθμοί_ελευθερίας2)", "d": "Αποδίδει την κατανομή πιθανοτήτων F (δεξιάς πλευράς) (βαθμός διαφοροποίησης) για δύο ομάδες δεδομένων", "ad": "είναι η τιμή για την οποία θα υπολογίσετε τη συνάρτηση, ένας μη αρνητικός αριθμός!είναι οι βαθμοί ελευθερίας του αριθμητή, έν<PERSON><PERSON> αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10!είναι οι βαθμοί ελευθερίας του παρονομαστή, ένας αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10"}, "FINV": {"a": "(πιθανότητα; βαθμοί_ελευθερίας1; βαθμοί_ελευθερίας2)", "d": "Αποδίδει το αντίστροφο της κατανομής πιθανοτήτων F (δεξιάς πλευράς). Εάν p = FDIST(x,...), τότε FINV(p,...) = x.", "ad": "είναι πιθανότητα σχετική με την αθροιστική κατανομή F, ένας αριθμός στο κλειστό διάστημα 0 έως 1!είναι οι βαθμοί ελευθερίας του αριθμητή, ένας αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10!είναι οι βαθμοί ελευθερίας του παρονομαστή, ένας αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10"}, "FTEST": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει αποτέλεσμα ελέγχου F, τη δίπλευρη πιθανότητα ότι οι διακυμάνσεις στους πίνακες 1 και 2 δεν παρουσιάζουν σημαντικές διαφορές", "ad": "είναι ο πρώτος πίνακας ή περιοχή δεδομένων και μπορεί να είναι αριθμοί ή ονόματα, καθώς και πίνακες ή αναφορές που περιέχουν αριθμούς (τα κενά παραβλέπονται)!είναι ο δεύτερος πίνακας ή περιοχή δεδομένων και μπορεί να είναι αριθμοί ή ονόματα, καθώς και πίνακες ή αναφορές που περιέχουν αριθμούς (τα κενά παραβλέπονται)"}, "F.DIST": {"a": "(x; βαθμοί_ελευθερίας1; βαθμοί_ελευθερίας2; αθροιστική)", "d": "Αποδίδει την κατανομή πιθανοτήτων F (αριστερής πλευράς) (βαθμός διαφοροποίησης) για δύο σύνολα δεδομένων", "ad": "είναι η τιμή για την οποία θα υπολογίσετε τη συνάρτηση, ένας μη αρνητικός αριθμός!είναι οι βαθμοί ελευθερίας του αριθμητή, έν<PERSON><PERSON> αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10!είναι οι βαθμοί ελευθερίας του παρονομαστή, ένας αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10!είναι μια λογική τιμή που παίρνει την τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή την τιμή FALSE για την συνάρτηση πυκνότητας πιθανότητας"}, "F.DIST.RT": {"a": "(x; βαθμοί_ελευθερίας1; βαθμοί_ελευθερίας2)", "d": "Αποδίδει την κατανομή πιθανοτήτων F (δεξιάς πλευράς) (βαθμός διαφοροποίησης) για δύο σύνολα δεδομένων", "ad": "είναι η τιμή για την οποία θα υπολογίσετε τη συνάρτηση, ένας μη αρνητικός αριθμός!είναι οι βαθμοί ελευθερίας του αριθμητή, έν<PERSON><PERSON> αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10!είναι οι βαθμοί ελευθερίας του παρονομαστή, ένας αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10"}, "F.INV": {"a": "(πιθανότητα; βαθμοί_ελευθερίας1; βαθμοί_ελευθερίας2)", "d": "Αποδίδει το αντίστροφο της κατανομής πιθανοτήτων F (αριστερής πλευράς). Εάν p = F.DIST(x,...), τότε F.INV(p,...) = x", "ad": "είναι μια πιθανότητα σχετική με την αθροιστική κατανομή F, ένας αριθμός στο κλειστό διάστημα 0 έως 1!είναι οι βαθμοί ελευθερίας του αριθμητή, έν<PERSON><PERSON> αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10!είναι οι βαθμοί ελευθερίας του παρονομαστή, ένας αριθμός στο διάστημα 1 έως 10^10, εκτ<PERSON>ς του 10^10"}, "F.INV.RT": {"a": "(πιθανότητα; βαθμοί_ελευθερίας1; βαθμοί_ελευθερίας2)", "d": "Αποδίδει το αντίστροφο της κατανομής πιθανοτήτων F (δεξιάς πλευράς). Εάν p = F.DIST.RT(x,...), τότε F.INV.RT(p,...) = x.", "ad": "είναι πιθανότητα σχετική με την αθροιστική κατανομή F, ένας αριθμός στο κλειστό διάστημα 0 έως 1!είναι οι βαθμοί ελευθερίας του αριθμητή, ένας αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10!είναι οι βαθμοί ελευθερίας του παρονομαστή, ένας αριθμός στο διάστημα 1 έως 10^10, εκτός του 10^10"}, "F.TEST": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει το αποτέλεσμα ενός ελέγχου F, τη δίπλευρη πιθανότητα ότι οι διακυμάνσεις στον πίνακα 1 και στον πίνακα 2 δεν παρουσιάζουν σημαντικές διαφορές", "ad": "είναι ο πρώτος πίνακας ή περιοχή δεδομένων και μπορεί να είναι αριθμοί ή ονόματα, καθώς και πίνακες ή αναφορές που περιέχουν αριθμούς (τα κενά παραβλέπονται)!είναι ο δεύτερος πίνακας ή περιοχή δεδομένων και μπορεί να είναι αριθμοί ή ονόματα, καθώς και πίνακες ή αναφορές που περιέχουν αριθμούς (τα κενά παραβλέπονται)"}, "FISHER": {"a": "(x)", "d": "Αποδίδει το μετασχηματισμ<PERSON> Fisher", "ad": "είναι η τιμή την οποία θέλετε να μετασχηματίσετε, ένας αριθμός στο ανοιχτό διάστημα -1 έως 1"}, "FISHERINV": {"a": "(y)", "d": "Αποδίδει το αντίστροφο του μετασχηματισμού Fisher: αν y = FISHER(x) τότε FISHERINV(y) = x", "ad": "είναι η τιμή στην οποία θέλετε να εφαρμόσετε τον αντίστροφο μετασχηματισμό"}, "FORECAST": {"a": "(x; γνωστά_y; γνωστά_x)", "d": "Υπολογίζει ή προβλέπει μια μελλοντική τιμή σε μια γραμμική τάση, χρησιμοποιώντας υπάρχουσες τιμές", "ad": "είναι το σημείο δεδομένων για το οποίο θέλετε να προβλέψετε μια τιμή και πρέπει να είναι μια αριθμητική τιμή!είναι ο εξαρτημένος πίνακας ή η περιοχή των αριθμητικών δεδομένων!είναι ο ανεξάρτητος πίνακας ή η περιοχή των αριθμητικών δεδομένων. Η διακύμανση των γνωστών_x δεν πρέπει να είναι μηδενική"}, "FORECAST.ETS": {"a": "(ημερομηνία_στόχου; τιμές; λωρίδα_χρόνου; [εποχικότητα]; [ολοκλήρωση_δεδομένων]; [συνάθροιση])", "d": "Επιστρέφει την προβλεπόμενη τιμή για μια συγκεκριμένη μελλοντική ημερομηνία στόχου, με τη χρήση μιας μεθόδου εκθετικής εξομάλυνσης.", "ad": "είναι το σημείο δεδομένων για το οποίο το Spreadsheet Editor προβλέπει μια τιμή. Θα πρέπει να φέρει το μοτίβο τιμών στη λωρίδα χρόνου.!είναι ο πίνακας ή το εύρος αριθμητικών δεδομένων που προβλέπετε.!είναι ο ανεξάρτητος πίνακας ή το εύρος των αριθμητικών δεδομένων. Οι ημερομηνίες στη λωρίδα χρόνου πρέπει να έχουν ένα ομοιόμορφο βήμα μεταξύ τους και δεν μπορεί να είναι μηδέν.!είναι μια προαιρετική αριθμητική τιμή που δηλώνει τη διάρκεια του εποχιακού μοτίβου. Η προεπιλεγμένη τιμή 1 δηλώνει ότι η εποχικότητα εντοπίζεται αυτόματα.!είναι μια προαιρετική τιμή για το χειρισμό των τιμών που λείπουν. Η προεπιλεγμένη τιμή του 1 αντικαθιστά τις τιμές που λείπουν με παρεμβολή και το 0 τις αντικαθιστά με μηδενικά.! είναι μια προαιρετική αριθμητική τιμή για τη συγκέντρωση πολλαπλών τιμών με την ίδια χρονική σήμανση. Εάν είναι κενό, το Spreadsheet Editor υπολογίζει κατά μέσο όρο τις τιμές."}, "FORECAST.ETS.CONFINT": {"a": "(ημερομηνία_στόχου; τιμές; λωρίδα_χρόνου; [επίπεδο_εμπιστοσύνης]; [εποχικότητα]; [ολοκλήρωση_δεδομένων]; [συνάθροιση])", "d": "Επιστρέφει ένα διάστημα εμπιστοσύνης για την τιμή πρόβλεψης κατά την καθορισμένη ημερομηνία στόχου.", "ad": "είναι το σημείο δεδομένων για το οποίο το Spreadsheet Editor προβλέπει μια τιμή. Θα πρέπει να φέρει το μοτίβο τιμών στη λωρίδα χρόνου.!είναι ο πίνακας ή το εύρος αριθμητικών δεδομένων που προβλέπετε.!είναι ο ανεξάρτητος πίνακας ή το εύρος των αριθμητικών δεδομένων. Οι ημερομηνίες στη λωρίδα χρόνου πρέπει να έχουν ένα ομοιόμορφο βήμα μεταξύ τους και δεν μπορεί να είναι μηδέν.!είναι ένας αριθμός μεταξύ 0 και 1 που δείχνει το επίπεδο εμπιστοσύνης για το υπολογισμένο διάστημα εμπιστοσύνης. Η προεπιλεγμένη τιμή είναι 0,95.!είναι μια προαιρετική αριθμητική τιμή που δηλώνει τη διάρκεια του εποχιακού μοτίβου. Η προεπιλεγμένη τιμή 1 δηλώνει ότι η εποχικότητα εντοπίζεται αυτόματα.!είναι μια προαιρετική τιμή για το χειρισμό των τιμών που λείπουν. Η προεπιλεγμένη τιμή του 1 αντικαθιστά τις τιμές που λείπουν με παρεμβολή και το 0 τις αντικαθιστά με μηδενικά.! είναι μια προαιρετική αριθμητική τιμή για τη συγκέντρωση πολλαπλών τιμών με την ίδια χρονική σήμανση. Εάν είναι κενό, το Spreadsheet Editor υπολογίζει κατά μέσο όρο τις τιμές."}, "FORECAST.ETS.SEASONALITY": {"a": "(τιμές; λωρίδα_χρόνου; [ολοκλήρωση_δεδομένων]; [συνάθροιση])", "d": "Επιστρέφει τη διάρκεια του επαναληπτικού μοντέλου που εντοπίζεται από μια εφαρμογή για την καθορισμένη χρονική σειρά.", "ad": "είναι ο πίνακας ή το εύρος των αριθμητικών δεδομένων που προβλέπετε.!είναι ο ανεξάρτητος πίνακας ή το εύρος των αριθμητικών δεδομένων. Οι ημερομηνίες στη λωρίδα χρόνου πρέπει να έχουν ομοιόμορφο βήμα μεταξύ τους και δεν μπορεί να είναι μηδέν.!είναι μια προαιρετική τιμή για το χειρισμό των τιμών που λείπουν. Η προεπιλεγμένη τιμή 1 αντικαθιστά τις τιμές που λείπουν με παρεμβολή και το 0 τις αντικαθιστά με μηδενικά.!είναι μια προαιρετική αριθμητική τιμή για τη συνάθροιση πολλαπλών τιμών με την ίδια χρονική σήμανση. Εάν είναι κενό, το Spreadsheet Editor υπολογίζει το μέσο όρο των τιμών."}, "FORECAST.ETS.STAT": {"a": "(τιμές; λωρίδα_χρόνου; στατιστικ<PERSON>ς_τύπος; [εποχικότητα]; [ολοκλήρωση_δεδομένων]; [συνάθροιση])", "d": "Επιστρέφει τα ζητούμενα στατιστικά στοιχεία για την πρόβλεψη.", "ad": "είναι ο πίνακας ή η περιοχή των αριθμητικών δεδομένων που προβλέπετε.!είναι ο ανεξάρτητος πίνακας ή η περιοχή αριθμητικών δεδομένων. Οι ημερομηνίες στη λωρίδα χρόνου πρέπει να έχουν ένα συνεπές βήμα μεταξύ τους και δεν μπορούν να είναι μηδέν.!είναι ένας αριθμός μεταξύ 1 και 8, που υποδεικνύει ποια στατιστική Spreadsheet Editor θα επιστρέψει για την υπολογισμένη πρόβλεψη.!είναι μια προαιρετική αριθμητική τιμή που υποδεικνύει το μήκος του εποχιακού σχεδίου. Η προεπιλεγμένη τιμή 1 υποδεικνύει ότι η εποχικότητα εντοπίζεται αυτόματα.!είναι μια προαιρετική τιμή για τον χειρισμό τιμών που λείπουν. Η προεπιλεγμένη τιμή του 1 αντικαθιστά τις τιμές που λείπουν με παρεμβολή και το 0 τις αντικαθιστά με μηδενικά.! είναι μια προαιρετική αριθμητική τιμή για τη συγκέντρωση πολλαπλών τιμών με την ίδια χρονική σήμανση. Εάν είναι κενό, το Spreadsheet Editor υπολογίζει κατά μέσο όρο τις τιμές."}, "FORECAST.LINEAR": {"a": "(x; γνωστά_y; γνωστά_x)", "d": "Υπολογίζει ή προβλέπει μια μελλοντική τιμή σε μια γραμμική τάση, χρησιμοποιώντας υπάρχουσες τιμές", "ad": "είναι το σημείο δεδομένων για το οποίο θέλετε να προβλέψετε μια τιμή και πρέπει να είναι μια αριθμητική τιμή!είναι ο εξαρτημένος πίνακας ή η περιοχή των αριθμητικών δεδομένων!είναι ο ανεξάρτητος πίνακας ή η περιοχή των αριθμητικών δεδομένων. Η διακύμανση των γνωστών_x δεν πρέπει να είναι μηδενική"}, "FREQUENCY": {"a": "(πίνακ<PERSON><PERSON>_δεδομένων; πίνακας_κατηγοριών)", "d": "Υπολογίζει τη συχνότητα τιμών σε μια περιοχή και αποδίδει έναν κατακόρυφο πίνακα αριθμών, ο οπ<PERSON><PERSON>ος έχει ένα στοιχείο παραπάνω από την παράμετρο πίνακας_κατηγοριών", "ad": "είναι ένας πίνακας ή μια αναφορ<PERSON> ομάδας τιμών, των οποίων θέλετε να υπολογίσετε τη συχνότητα (τα κενά και το κείμενο παραβλέπονται)!είναι ένας πίνακας ή μια αναφορά σε εσωτερικά κελιά, στα οποία θέλετε να ομαδοποιήσετε τις τιμές του πίνακας_δεδομένων"}, "GAMMA": {"a": "(x)", "d": "Αποδίδει την τιμή της συνάρτησης γάμμα", "ad": "είναι η τιμή για την οποία θέλετε να υπολογίσετε τη συνάρτηση γάμμα"}, "GAMMADIST": {"a": "(x; άλφα; βήτα; αθροιστική)", "d": "Αποδίδει την κατανομή γάμμα", "ad": "είναι η τιμή για την οποία θέλετε να υπολογίσετε την κατανομή, ένας μη αρνητικός αριθμός!είναι μια παράμετρος της κατανομής, έν<PERSON><PERSON> θετικός αριθμός!είναι μια παράμετρος της κατανομής, έν<PERSON><PERSON> θετικός αριθμός. Εάν βήτα=1, η GAMMADIST αποδίδει την τυπική κατανομή γάμμα!είναι μια λογική τιμή που παίρνει την τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή FALSE για τη συνάρτηση πιθανότητας ή αν παραλειφθεί"}, "GAMMA.DIST": {"a": "(x; άλφα; βήτα; αθροιστική)", "d": "Αποδίδει την κατανομή γάμμα", "ad": "είναι η τιμή για την οποία θέλετε να υπολογίσετε την κατανομή, ένας μη αρνητικός αριθμός!είναι μια παράμετρος της κατανομής, έν<PERSON><PERSON> θετικός αριθμός!είναι μια παράμετρος της κατανομής, ένας θετικός αριθμός. Εάν βήτα=1, η GAMMA.DIST αποδίδει την τυπική κατανομή γάμμα!είναι μια λογική τιμή που παίρνει την τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή FALSE για τη συνάρτηση πιθανότητας ή αν παραλειφθεί"}, "GAMMAINV": {"a": "(πιθανότητα; άλφα; βήτα)", "d": "Αποδίδει το αντίστροφο της αθροιστικής κατανομής γάμμα. Εάν p = GAMMADIST(x,...), τότε GAMMAINV(p,...) = x.", "ad": "είναι πιθανότητα σχετιζόμενη με την κατανομή γάμμα, ένας αριθμός στο κλειστό διάστημα 0 έως 1!είναι παράμετρος της κατανομής, έ<PERSON><PERSON><PERSON> θετικός αριθμός!είναι παράμετρος κατανομής, έν<PERSON><PERSON> θετικός αριθμός. Εάν βήτα = 1, η GAMMAINV αποδίδει το αντίστροφο της σταθερής κατανομής γάμμα"}, "GAMMA.INV": {"a": "(πιθανότητα; άλφα; βήτα)", "d": "Αποδίδει το αντίστροφο της αθροιστικής κατανομής γάμμα. Εάν p = GAMMADIST(x,...), τότε GAMMA.INV(p,...) = x.", "ad": "είναι πιθανότητα σχετιζόμενη με την κατανομή γάμμα, ένας αριθμός στο κλειστό διάστημα 0 έως 1!είναι παράμετρος της κατανομής, έν<PERSON><PERSON> θετικός αριθμός!είναι παράμετρος κατανομής, έν<PERSON>ς θετικός αριθμός. Εάν βήτα = 1, η GAMMA.INV αποδίδει το αντίστροφο της σταθερής κατανομής γάμμα"}, "GAMMALN": {"a": "(x)", "d": "Αποδίδει το φυσικό λογάριθμο της συνάρτησης γάμμα", "ad": "είναι η τιμή της οποίας θέλετε να υπολογίσετε την παράμετρο GAMMALN, ένας θετικός αριθμός"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Αποδίδει το φυσικό λογάριθμο της συνάρτησης γάμμα", "ad": "είναι η τιμή της οποίας θέλετε να υπολογίσετε την παράμετρο GAMMALN.PRECISE, ένας θετικός αριθμός"}, "GAUSS": {"a": "(x)", "d": "Αποδίδει κατά 0,5 λιγότερο από την τυπική κανονική αθροιστική κατανομή", "ad": "είναι η τιμή για την οποία θέλετε την κατανομή"}, "GEOMEAN": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τον γεωμετρικό μέσο ενός πίνακα ή μιας περιοχής θετικών αριθμητικών δεδομένων", "ad": "είναι 1 έως 255 αριθμοί ή ονόματα, π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ή αναφορές που περιέχουν αριθμούς των οποίων θέλετε να υπολογίσετε τον αριθμητικό μέσο"}, "GROWTH": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Επιστρέφει αριθμούς σε μια εκθετική τάση ανάπτυξης η οποία διέρχεται από γνωστά σημεία δεδομένων", "ad": "είναι η ομάδα των τιμών y που ήδη γνωρίζετε στη σχέση y=b*m^x, ένας πίνακας ή μια περιοχή θετικών αριθμών!είναι μια προαιρετική ομάδα τιμών x που ίσως ήδη γνωρίζετε στη σχέση y = b*m^x, ένας πίνακας ή μια περιοχή ίδιου μεγέθους με αυτό του Known_y!είναι νέες τιμές x για τις οποίες θέλετε η συνάρτηση GROWTH να επιστρέψει τις αντίστοιχες τιμές y!είναι μια λογική τιμή: η σταθερά b υπολογίζεται κανονικά εάν Const = TRUE και η σταθερά b ορίζεται ως 1 εάν Const = FALSE ή παραλείπεται"}, "HARMEAN": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τον αρμονικό μέσο μιας ομάδας θετικών αριθμών: το αντίστροφο του αριθμητικού μέσου των αντίστροφων.", "ad": "είναι 1 έως 255 αριθμοί ή ονόματα, π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ή αναφορές που περιέχουν αριθμούς, των οποίων θέλετε να υπολογίσετε τον αρμονικό μέσο"}, "HYPGEOM.DIST": {"a": "(δείγμα_επιτυχιών; αριθμός_δείγματος; πληθυσμός_επιτυχίες; αριθμός_πληθυσμός; αθροιστική)", "d": "Αποδίδει την υπεργεωμετρική κατανομή", "ad": "είναι ο αριθμός των επιτυχιών στο δείγμα!είναι το μέγεθος του δείγματος!είναι ο αριθμός επιτυχιών στον πληθυσμό!είναι το μέγεθος του πληθυσμού!είναι μια λογική τιμή που παίρνει τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή FALSE για τη συνάρτηση πυκνότητας πιθανότητας"}, "HYPGEOMDIST": {"a": "(δείγμα_επιτυχιών; αριθμός_δείγματος; πληθυσμός_επιτυχίες; αριθμός_πληθυσμός)", "d": "Αποδίδει την υπεργεωμετρική κατανομή", "ad": "είναι ο αριθμός των επιτυχιών στο δείγμα!είναι το μέγεθος δείγματος!είναι ο αριθμός επιτυχιών στον πληθυσμό!είναι το μέγεθος του πληθυσμού"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "Υπολογίζει το σημείο τομής μιας γραμμής με τον άξονα y, χρησιμοποιώντας τη γραμμή παλινδρόμησης βέλτιστης προσαρμογής που διέρχεται από τις γνωστές τιμές x και y", "ad": "είναι η εξαρτημένη ομάδα παρατηρήσεων ή δεδομένων και μπορεί να είναι αριθμοί ή ονόματα, καθώς και πίνακες ή αναφορές που περιέχουν αριθμούς!είναι η ανεξάρτητη ομάδα παρατηρήσεων ή δεδομένων και μπορεί να είναι αριθμοί ή ονόματα, καθώς και πίνακες ή αναφορές που περιέχουν αριθμούς"}, "KURT": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει την κύρτωση ενός συνόλου δεδομένων", "ad": "είναι 1 έως 255 αριθμοί ή ονόματα, π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ή αναφορές που περιέχουν αριθμούς, των οποίων θέλετε να υπολογίσετε την κύρτωση"}, "LARGE": {"a": "(πίνακας; k)", "d": "Αποδίδει την k μεγαλύτερη τιμή σε ένα σύνολο δεδομένων. Για παράδειγμα, τον πέμπτο μεγαλύτερο αριθμό", "ad": "είναι ο πίνακας ή η περιοχή δεδομένων των οποίων θέλετε να καθοριστεί η k μεγαλύτερη τιμή!είναι η θέση (ξε<PERSON><PERSON>νώντας από τη μεγαλύτερη) στον πίνακα ή στην περιοχή κελιών της τιμής που θα αποδοθεί"}, "LINEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Επιστρέφει τα στατιστικά στοιχεία που περιγράφουν μια γραμμική τάση η οποία διέρχεται από γνωστά σημεία δεδομένων, προσαρμόζοντας μια ευθεία γραμμή με τη χρήση της μεθόδου των ελαχίστων τετραγώνων", "ad": "είναι η ομάδα των τιμών y που ήδη γνωρίζετε στη σχέση y = mx + b!είναι μια προαιρετική ομάδα τιμών x που ίσως ήδη γνωρίζετε στη σχέση y=mx + b!είναι μια λογική τιμή: η σταθερά b υπολογίζεται κανονικά εάν Const = TRUE ή παραλείπεται και η σταθερά b ορίζεται ως ίση με 0 εάν Const = FALSE!είναι μια λογική τιμή: επιστροφή επιπλέον στατιστικών στοιχείων παλινδρόμησης = TRUE, επιστροφή συντελεστών m και σταθερά b =FALSE ή παραλείπεται"}, "LOGEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Επιστρέφει τα στατιστικ<PERSON> στοιχεία που περιγράφουν μια εκθετική καμπύλη η οποία διέρχεται από γνωστά σημεία δεδομένων", "ad": "είναι η ομάδα τιμών y που ήδη γνωρίζετε στη σχέση y = b*m^x!είναι μια προαιρετική ομάδα τιμών x που ίσως ήδη γνωρίζετε στη σχέση y = b*m^x!είναι μια λογική τιμή: η σταθερά b υπολογίζεται κανονικά εάν Const = TRUE ή παραλείπεται. H σταθερά b ορίζεται ως ίση με 1 εάν Const = FALSE!είναι μια λογική τιμή: επιστροφή επιπλέον στατιστικών στοιχείων παλινδρόμησης = TRUE και επιστροφή συντελεστών m και η σταθερά b =FALSE ή παραλείπεται"}, "LOGINV": {"a": "(πιθανότητα; μέση_τιμή; τυπική_απόκλιση)", "d": "Αποδίδει την αντίστροφη κανονική λογαριθμική συνάρτηση της αθροιστικής κατανομής του x, όπου η ln(x) κατανέμεται κανονικά με παραμέτρους Mean και Standard_dev", "ad": "είναι μια πιθανότητα σχετική με την κανονική λογαριθμική κατανομή, ένας αριθμός στο κλειστό διάστημα 0 έως 1!είναι ο αριθμητικός μέσος της ln(x)!είναι η τυπική απόκλιση της ln(x), ένας θετικός αριθμός"}, "LOGNORM.DIST": {"a": "(x; μέση_τιμή; τυπική_απόκλιση; αθροιστική)", "d": "Αποδίδει την κανονική λογαριθμική συνάρτηση της αθροιστικής κατανομής του x, όπου η ln(x) κατανέμεται κανονικά με παραμέτρους μέσος_όρος και τυπική_απόκλιση", "ad": "είναι η τιμή για την οποία θα υπολογίσετε τη συνάρτηση, ένας θετικός αριθμός!είναι ο αριθμητικός μέσος της ln(x)!είναι η τυπική απόκλιση της ln(x), ένας θετικός αριθμός!είναι μια λογική τιμή που παίρνει τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή FALSE για τη συνάρτηση πυκνότητας πιθανότητας"}, "LOGNORM.INV": {"a": "(πιθανότητα; μέση_τιμή; τυπική_απόκλιση)", "d": "Αποδίδει το αντίστροφο της κανονικής λογαριθμικής συνάρτησης της αθροιστικής κατανομής του x, όπου η ln(x) κατανέμεται κανονικά με παραμέτρους μέσος_όρος και τυπική_απόκλιση", "ad": "είναι μια πιθανότητα σχετική με την κανονική λογαριθμική κατανομή, ένας αριθμός στο κλειστό διάστημα 0 έως 1!είναι ο αριθμητικός μέσος της ln(x)!είναι η τυπική απόκλιση της ln(x), ένας θετικός αριθμός"}, "LOGNORMDIST": {"a": "(x; μέση_τιμή; τυπική_απόκλιση)", "d": "Αποδίδει την κανονική λογαριθμική συνάρτηση της αθροιστικής κατανομής του x, όπου η ln(x) κατανέμεται κανονικά με παραμέτρους Mean και Standard_dev", "ad": "είναι η τιμή για την οποία θα υπολογίσετε τη συνάρτηση, ένας θετικός αριθμός!είναι ο αριθμητικός μέσος της ln(x)!είναι η τυπική απόκλιση της ln(x), έν<PERSON><PERSON> θετικός αριθμός"}, "MAX": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τη μεγαλύτερη τιμή ενός συνόλου ορισμάτων. Παραβλέπει λογικές τιμές και κείμενο.", "ad": "είναι 1 έως 255 αριθμοί, κ<PERSON><PERSON><PERSON> κελιά, λογικ<PERSON>ς τιμές ή αριθμοί κειμένου των οποίων θέλετε το μέγιστο"}, "MAXA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Αποδίδει τη μέγιστη τιμή ενός συνόλου τιμών. Δεν παραβλέπει λογικές τιμές και κείμενο", "ad": "είναι 1 έως 255 αριθμοί, κ<PERSON><PERSON><PERSON> κελιά, λογικ<PERSON>ς τιμές ή αριθμοί κειμένου των οποίων θέλετε το μέγιστο"}, "MAXIFS": {"a": "(μεγ_περιοχή; κριτήρια_περιοχή; κριτήρια; ...)", "d": "Επιστρέφει τη μέγιστη τιμή στα κελιά που καθορίζονται από ένα δεδομένο σύνολο συνθηκών ή κριτηρίων", "ad": "τα κελιά στα οποία θα προσδιοριστεί η μέγιστη τιμή!είναι η περιοχή κελιών που θέλετε να υπολογίσετε για τη συγκεκριμένη συνθήκη!είναι η συνθήκη ή τα κριτήρια με τη μορφή αριθμού, παράστασης ή κειμένου που ορίζει ποια κελιά θα συμπεριληφθούν κατά τον καθορισμό της μέγιστης τιμής"}, "MEDIAN": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει το διάμεσο ή τον αριθμό που βρίσκεται στη μέση του συνόλου των καθορισμένων αριθμών", "ad": "είναι 1 έως 255 αριθμοί ή ονόματα, π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ή αναφορές, που περιέχουν αριθμούς για τους οποίους θέλετε το διάμεσο"}, "MIN": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τη μικρότερη τιμή ενός συνόλου ορισμάτων. Παραβλέπει λογικές τιμές και κείμενο.", "ad": "είναι 1 έως 255 αριθμοί, κ<PERSON><PERSON><PERSON> κελιά, λογικ<PERSON>ς τιμές ή αριθμοί κειμένου των οποίων θέλετε το ελάχιστο"}, "MINA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Αποδίδει την ελάχιστη τιμή μιας ομάδας τιμών. Δεν παραβλέπει λογικές τιμές και κείμενο", "ad": "είναι 1 έως 255 αριθμοί, κ<PERSON><PERSON><PERSON> κελιά, λογικ<PERSON>ς τιμές ή αριθμοί κειμένου των οποίων θέλετε το ελάχιστο"}, "MINIFS": {"a": "(ελαχ_περιοχή; κριτήρια_περιοχή; κριτήρια; ...)", "d": "Επιστρέφει την ελάχιστη τιμή στα κελιά που καθορίζονται από ένα δεδομένο σύνολο συνθηκών ή κριτηρίων", "ad": "τα κελιά στα οποία θα προσδιοριστεί η ελάχιστη τιμή!είναι η περιοχή κελιών που θέλετε να υπολογίσετε για τη συγκεκριμένη συνθήκη!είναι η συνθήκη ή τα κριτήρια με τη μορφή αριθμού, παράστασης ή κειμένου που ορίζει ποια κελιά θα συμπεριληφθούν κατά τον καθορισμό της ελάχιστης τιμής"}, "MODE": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τη συνηθέστερη ή συχνότερα επαναλαμβανόμενη τιμή σε έναν πίνακα ή μια περιοχή δεδομένων", "ad": "είναι 1 έως 255 αριθμοί ή ονόματα, π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ή αναφορές που περιέχουν αριθμούς των οποίων θέλετε να υπολογίσετε την επικρατούσα τιμή"}, "MODE.MULT": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει έναν κατακόρυφο πίνακα των συνηθέστερων ή συχνότερα επαναλαμβανόμενων τιμών σε έναν πίνακα ή μια περιοχή δεδομένων.  Για οριζόντιο πίνακα, χρησιμοποιήστε =TRANSPOSE(MODE.MULT(αριθμός1,αριθμός2,...))", "ad": "είναι αριθμοί από 1 έως 255 ή ονόματα, π<PERSON><PERSON><PERSON><PERSON><PERSON>ς ή αναφορές που περιέχουν αριθμούς, των οποίων θέλετε να υπολογίσετε την επικρατούσα τιμή"}, "MODE.SNGL": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τη συνηθέστερη ή συχνότερα επαναλαμβανόμενη τιμή σε έναν πίνακα ή μια περιοχή δεδομένων", "ad": "είναι 1 έως 255 αριθμοί ή ονόματα, π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ή αναφορές που περιέχουν αριθμούς των οποίων θέλετε να υπολογίσετε την επικρατούσα τιμή"}, "NEGBINOM.DIST": {"a": "(αριθμός_αποτυχιών; αριθμός_επιτυχιών; πιθανότητα_επιτυχίας; αθροιστική)", "d": "Αποδίδει την αρνητική διωνυμική κατανομή, την πιθανότητα να υπάρχουν αριθμός_αποτυχιών αποτυχίες πριν από τις αριθμός_επιτυχιών επιτυχίες, με πιθανότητα_επιτυχίας πιθανότητα επιτυχίας", "ad": "είναι ο αριθμός αποτυχιών!είναι ο αριθμός κατωφλίου επιτυχιών!είναι η πιθανότητα επιτυχίας, ένας αριθμός από το 0 έως το 1!είναι μια λογική τιμή που παίρνει τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή FALSE για τη συνάρτηση πιθανότητας"}, "NEGBINOMDIST": {"a": "(αριθμός_αποτυχιών; αριθμός_επιτυχιών; πιθανότητα_επιτυχίας)", "d": "Αποδίδει αρνητική διωνυμική κατανομή, την πιθανότητα να υπάρχει αριθμός_αποτυχιών αποτυχιών πριν από το τακτικό αριθμητικό επιτυχιών, με την πιθανότητα_επιτυχίας πιθανότητα επιτυχίας", "ad": "να είναι ο αριθμός αποτυχιών!να είναι ο αριθμός κατωφλιού επιτυχιών!να είναι η πιθανότητα επιτυχίας, έν<PERSON><PERSON> αριθμός από 0 έως 1"}, "NORM.DIST": {"a": "(x; μέση_τιμή; τυπική_απόκλιση; αθροιστική)", "d": "Αποδίδει την κανονική κατανομή για τον αριθμητικό μέσο και τη μέση απόκλιση τετραγώνου που καθορίστηκαν", "ad": "είναι η τιμή της οποίας θέλετε την κατανομή!είναι ο αριθμητικός μέσος της κατανομής!είναι η τυπική απόκλιση της κατανομής, έν<PERSON><PERSON> θετικός αριθμός!είναι μια λογική τιμή που παίρνει τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή FALSE για τη συνάρτηση πιθανότητας"}, "NORMDIST": {"a": "(x; μέση_τιμή; τυπική_απόκλιση; αθροιστική)", "d": "Αποδίδει την κανονική αθροιστική κατανομή για τον αριθμητικό μέσο και τη μέση απόκλιση τετραγώνου που καθορίστηκαν", "ad": "είναι η τιμή της οποίας θέλετε την κατανομή!είναι ο αριθμητικός μέσος της κατανομής!είναι η τυπική απόκλιση της κατανομής, έν<PERSON><PERSON> θετικός αριθμός!είναι μια λογική τιμή που παίρνει τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή FALSE για τη συνάρτηση πυκνότητας πιθανότητας"}, "NORM.INV": {"a": "(πιθανότητα; μέση_τιμή; τυπική_απόκλιση)", "d": "Αποδίδει το αντίστροφο της κανονικής αθροιστικής κατανομής για τον αριθμητικό μέσο και την τυπική απόκλιση που καθορίσατε", "ad": "είναι πιθανότητα αντίστοιχη με την κανονική κατανομή, ένας αριθμός στο κλειστό διάστημα από 0 έως 1!είναι ο αριθμητικός μέσος της κατανομής!είναι η τυπική απόκλιση της κατανομής, ένας θετικός αριθμός"}, "NORMINV": {"a": "(πιθανότητα; μέση_τιμή; τυπική_απόκλιση)", "d": "Αποδίδει το αντίστροφο της κανονικής αθροιστικής κατανομής για τον αριθμητικό μέσο και την τυπική απόκλιση που καθορίσατε", "ad": "είναι πιθανότητα αντίστοιχη με την κανονική κατανομή, ένας αριθμός στο κλειστό διάστημα από 0 έως 1!είναι ο αριθμητικός μέσος της κατανομής!είναι η τυπική απόκλιση της κατανομής, ένας θετικός αριθμός"}, "NORM.S.DIST": {"a": "(z; αθροιστική)", "d": "Αποδίδει την τυπική κανονική κατανομή (ο αριθμητικός μέσος ισούται με μηδέν και η τυπική απόκλιση με ένα)", "ad": "είναι η τιμή της οποίας θέλετε την κατανομή!είναι μια λογική τιμή που παίρνει την τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή την τιμή FALSE για την συνάρτηση πυκνότητας πιθανότητας"}, "NORMSDIST": {"a": "(z)", "d": "Αποδίδει την τυπική κανονική αθροιστική κατανομή (ο αριθμητικός μέσος ισούται με μηδέν και η τυπική απόκλιση με ένα)", "ad": "είναι η τιμή της οποίας θέλετε την κατανομή"}, "NORM.S.INV": {"a": "(πιθανότητα)", "d": "Αποδίδει το αντίστροφο της τυπικής κανονικής αθροιστικής κατανομής (ο αριθμητικός μέσος ισούται με μηδέν και η μέση απόκλιση με ένα)", "ad": "είναι πιθανότητα αντίστοιχη με την κανονική κατανομή, ένας αριθμός στο κλειστό διάστημα από 0 έως 1"}, "NORMSINV": {"a": "(πιθανότητα)", "d": "Αποδίδει το αντίστροφο της τυπικής κανονικής αθροιστικής κατανομής (ο αριθμητικός μέσος ισούται με μηδέν και η μέση απόκλιση με ένα)", "ad": "είναι πιθανότητα αντίστοιχη με την κανονική κατανομή, ένας αριθμός στο κλειστό διάστημα από 0 έως 1"}, "PEARSON": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει το συντελεστή συσχέτιση<PERSON> Pearson του γινομένου των ροπών, r", "ad": "είναι ομάδα ανεξάρτητων τιμών!είναι ομάδα εξαρτημένων τιμών"}, "PERCENTILE": {"a": "(πίνακας; k)", "d": "Αποδίδει το k εκατοστημόριο τιμών μιας περιοχής", "ad": "είναι ο πίνακας ή η περιοχή δεδομένων που ορίζει σχετικό standing!είναι η τιμή εκατοστημορίου στο κλειστό διάστημα 0 έως 1"}, "PERCENTILE.EXC": {"a": "(πίνακας; k)", "d": "Αποδίδει το k εκατοστημόριο τιμών μιας περιοχής, όπου k είναι στο ανοιχτό διάστημα 0..1", "ad": "είναι ο πίνακας ή η περιοχή δεδομένων που ορίζει τη σχετική θέση κατάταξης!είναι η τιμή εκατοστημορίου στο κλειστό διάστημα 0 έως 1"}, "PERCENTILE.INC": {"a": "(πίνακας; k)", "d": "Αποδίδει το k εκατοστημόριο τιμών μιας περιοχής, όπου k είναι η κλειστή περιοχή 0..1", "ad": "είναι ο πίνακας ή η περιοχή δεδομένων που ορίζει τη σχετική θέση κατάταξης!είναι η τιμή εκατοστημορίου στο κλειστό διάστημα 0 έως 1"}, "PERCENTRANK": {"a": "(πίνακας; x; [σημαντικότητα])", "d": "Αποδίδει τη σειρά κατάταξης μιας τιμής ως ποσοστό επί του συνόλου δεδομένων", "ad": "είναι ο πίνακας ή η περιοχή δεδομένων με αριθμητικές τιμές που ορίζει τη σχετική θέση κατάταξης!είναι η τιμή της οποίας θέλετε να γνωρίζετε τη σειρά κατάταξης!είναι μια προαιρετική τιμή που καθορίζει τον αριθμό των σημαντικών ψηφίων για το επιστρεφόμενο ποσοστό και ορίζεται ως τρία ψηφία αν παραλειφθεί (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(πίνακας; x; [σημαντικότητα])", "d": "Αποδίδει τη σειρά κατάταξης μιας τιμής σε ένα σύνολο δεδομένων ως ποσοστό του συνόλου δεδομένων ως ποσοστό (ανοιχτό διάστημα 0..1) του συνόλου δεδομένων", "ad": "είναι η συστοιχία ή η περιοχή δεδομένων με αριθμητικές τιμές που ορίζει τη σχετική κατάταξη!είναι η τιμή για την οποία θέλετε να γνωρίζετε την κατάταξη!είναι μια προαιρετική τιμή που προσδιορίζει τον αριθμό των σημαντικών ψηφίων για το επιστρεφόμενο ποσοστό, τρία ψηφία αν παραλειφθούν (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(πίνακας; x; [σημαντικότητα])", "d": "Αποδίδει τη σειρά κατάταξης μιας τιμής σε ένα σύνολο δεδομένων ως ποσοστό του συνόλου δεδομένων ως ποσοστό (στο κλειστό διάστημα 0..1) του συνόλου δεδομένων", "ad": "είναι η συστοιχία ή η περιοχή δεδομένων με αριθμητικές τιμές που ορίζει τη σχετική κατάταξη!είναι η τιμή για την οποία θέλετε να γνωρίζετε την κατάταξη!είναι μια προαιρετική τιμή που προσδιορίζει τον αριθμό των σημαντικών ψηφίων για το επιστρεφόμενο ποσοστό, τρία ψηφία αν παραλειφθούν (0.xxx%)"}, "PERMUT": {"a": "(αριθμός; επιλεγμένος_αριθμός)", "d": "Αποδίδει τον αριθμό των διατάξεων για δεδομένο αριθμό αντικειμένων, τα οποία μπορούν να επιλεχθούν από το σύνολο των αντικειμένων", "ad": "είναι το συνολικό πλήθος των αντικειμένων!είναι το πλήθος των αντικειμένων σε κάθε συνδυασμό"}, "PERMUTATIONA": {"a": "(αριθμός; επιλεγμένος_αριθμός)", "d": "Αποδίδει τον αριθμό των μεταθέσεων για δεδομένο αριθμό αντικειμένων (με επαναλήψεις), τα οποία μπορούν να επιλεχθούν από το σύνολο των αντικειμένων", "ad": "είναι το συνολικό πλήθος των αντικειμένων!είναι το πλήθος των αντικειμένων σε κάθε μετάθεση"}, "PHI": {"a": "(x)", "d": "Αποδίδει την τιμή της συνάρτησης πυκνότητας για μια τυπική κανονική κατανομή", "ad": "είναι ο αριθμός για τον οπ<PERSON><PERSON>ο θέλετε την πυκνότητα της τυπικής κανονικής κατανομής"}, "POISSON": {"a": "(x; μέση_τιμή; αθροιστική)", "d": "Αποδίδει την κατανομή Poisson", "ad": "είναι ο αριθμός συμβάντων!είναι η αναμενόμενη αριθμητική τιμή, ένας θετικός αριθμός!είναι λογική τιμή που παίρνει την τιμή TRUE για την αθροιστική κατανομή πιθανοτήτων Poisson ή την τιμή FALSE για την κατανομή πιθανοτήτων Poisson"}, "POISSON.DIST": {"a": "(x; μέση_τιμή; αθροιστική)", "d": "Αποδίδει την κατανομή Poisson", "ad": "είναι ο αριθμός συμβάντων!είναι η αναμενόμενη αριθμητική τιμή, ένας θετικός αριθμός!είναι λογική τιμή που παίρνει την τιμή TRUE για την αθροιστική κατανομή πιθανοτήτων Poisson ή την τιμή FALSE για την κατανομή πιθανοτήτων Poisson"}, "PROB": {"a": "(x_εύρος; εύρος_πιθανοτήτων; κατώτερο_όριο; [ανώτερο_όριο])", "d": "Αποδίδει την πιθανότητα ότι οι τιμές μιας περιοχής βρίσκονται μεταξύ δύο ορίων ή ότι είναι ίσες με ένα κατώτερο όριο", "ad": "είναι η περιοχή αριθμητικών τιμών του x για τις οποίες υπάρχουν σχετικές πιθανότητες!είναι μια ομάδα πιθανοτήτων σχετικών με τιμές στην παράμετρο εύρος_x, με τιμές στο διάστημα 0 έως 1 εκτός του 0!είναι το κατώτερο όριο για την τιμή, της οποίας θέλετε την πιθανότητα!είναι το προαιρετικό ανώτερο όριο για την τιμή. Εάν παραλειφθεί, η συνάρτηση PROB αποδίδει την πιθανότητα οι τιμές της παραμέτρου εύρος_X να είναι ίσες με το κατώτερο_όριο"}, "QUARTILE": {"a": "(πίνακας; τεταρτημόριο)", "d": "Αποδίδει το τεταρτημόρι<PERSON> ενός συνόλου δεδομένων", "ad": "είναι ο πίνακας ή η περιοχή κελιών αριθμητικών τιμών των οποίων θέλετε τιμή τεταρτημορίου!είναι ένας αριθμός που παίρνει την τιμή 0 για την ελάχιστη τιμή, την τιμή 1 για το πρώτο τεταρτημόριο, την τιμή 2 για το διάμεσο, την τιμή 3 για το τρίτο τεταρτημόριο ή την τιμή 4 για τη μέγιστη τιμή"}, "QUARTILE.INC": {"a": "(πίνακας; τεταρτημόριο)", "d": "Αποδίδει το τεταρτημόρι<PERSON> ενός συνόλου δεδομένων, βάσει τιμών εκατοστημορίου στο κλειστό διάστημα 0..1", "ad": "είναι ο πίνακας ή η περιοχή κελιών αριθμητικών τιμών των οποίων θέλετε τιμή τεταρτημορίου!είναι ένας αριθμός: ελάχιστη τιμή = 0, 1ο τεταρτημόριο = 1, διάμεση τιμή = 2, 3ο τεταρτημόριο = 3, μέγιστη τιμή = 4"}, "QUARTILE.EXC": {"a": "(πίνακας; τεταρτημόριο)", "d": "Αποδίδει το τεταρτημόρι<PERSON> ενός συνόλου δεδομένων, βάσει τιμών εκατοστημορίου στο ανοιχτό διάστημα 0..1", "ad": "είναι ο πίνακας ή η περιοχή κελιών αριθμητικών τιμών των οποίων θέλετε τιμή τεταρτημορίου!είναι ένας αριθμός: ελάχιστη τιμή = 0, 1ο τεταρτημόριο = 1, διάμεση τιμή = 2, 3ο τεταρτημόριο = 3, μέγιστη τιμή = 4"}, "RANK": {"a": "(αριθμός; αναφορά; [σειρά])", "d": "Αποδίδει τη σειρά ενός αριθμού μέσα σε μια λίστα αριθμών. Η τιμή είναι σχετική με άλλες τιμές στη λίστα.", "ad": "είναι ο αριθμός του οποίου θέλετε να βρείτε τη σειρά!είναι ένας πίνακας ή αναφορά λίστας αριθμών. Οι μη αριθμητικές τιμές παραβλέπονται!είναι ένας αριθμός: όταν η λίστα είναι ταξινομημένη κατά φθίνουσα σειρά, η τιμή είναι 0 ή παραλείπεται και όταν η λίστα είναι ταξινομημένη κατά αύξουσα σειρά, η τιμή είναι οποιαδήποτε μη μηδενική τιμή"}, "RANK.AVG": {"a": "(αριθμός; αναφορά; [σειρά])", "d": "Αποδίδει τη σειρά κατάταξης ενός αριθμού σε μια λίστα αριθμών: το μέγεθός του σε σχέση με τις άλλες τιμές της λίστας. Εάν περισσότερες από μία τιμές έχουν την ίδια σειρά κατάταξης, αποδ<PERSON>δεται η μέση σειρά κατάταξης", "ad": "είναι ο αριθμός του οποίου θέλετε να βρείτε τη σειρά κατάταξης!είναι ένας πίνακας μιας λίστας αριθμών ή μια αναφορά σε μια λίστα αριθμών. Οι μη αριθμητικές τιμές παραβλέπονται!είναι ένας αριθμός: σειρά κατάταξης στη λίστα ταξινομημένη με φθίνουσα σειρά = 0 ή παραλείπετε, σειρά κατάταξης στη λίστα ταξινομημένη με αύξουσα σειρά = οποιαδήποτε μη μηδενική τιμή"}, "RANK.EQ": {"a": "(αριθμός; αναφορά; [σειρά])", "d": "Αποδίδει τη σειρά κατάταξης ενός αριθμού σε μια λίστα αριθμών: το μέγεθός του σε σχέση με τις άλλες τιμές της λίστας. Εάν περισσότερες από μία τιμές έχουν την ίδια σειρά κατάταξης, απο<PERSON><PERSON>δ<PERSON>ται η ανώτατη θέση κατάταξης αυτού του συνόλου τιμών", "ad": "είναι ο αριθμός του οποίου θέλετε να βρείτε τη σειρά κατάταξης!είναι ένας πίνακας μιας λίστας αριθμών ή μια αναφορά σε μια λίστα αριθμών. Οι μη αριθμητικές τιμές παραβλέπονται!είναι ένας αριθμός: σειρά κατάταξης στη λίστα ταξινομημένη με φθίνουσα σειρά = 0 ή παραλείπεται, σειρά κατάταξης στη λίστα ταξινομημένη με αύξουσα σειρά = οποιαδήποτε μη μηδενική τιμή"}, "RSQ": {"a": "(known_ys; known_xs)", "d": "Επιστρέφει το τετράγωνο του συντελεστή συσχέτισης Pearson του γινομένου ροπών μέσω των καθορισμένων σημείων δεδομένων", "ad": "είναι ένας πίνακας ή μια περιοχή σημείων δεδομένων και μπορεί να είναι αριθμοί ή ονόματα, καθώς και πίνακες ή αναφορές που περιέχουν αριθμούς!είναι ένας πίνακας ή μια περιοχή σημείων δεδομένων και μπορεί να είναι αριθμοί ή ονόματα, καθώς και πίνακες ή αναφορές που περιέχουν αριθμούς"}, "SKEW": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει την ασυμμετρία μιας κατανομής: ένα<PERSON> χαρακτηρισμός του βαθμού ασυμμετρίας μιας κατανομής γύρω από τον αριθμητικό μέσο της.", "ad": "είναι 1 έως 255 αριθμο<PERSON>, ο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ή αναφορές που περιέχουν αριθμούς, των οποίων θέλετε να υπολογίσετε την ασυμμετρία"}, "SKEW.P": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τη λοξότητα μιας κατανομής με βάση έναν πληθυσμό: χαρακτηρισμός του βαθμού ασυμμετρίας μιας κατανομής γύρω από το μέσο όρο της", "ad": "είναι 1 έως 254 αριθμοί ή ονόματα, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ή αναφορές που περιέχουν αριθμούς, των οποίων θέλετε να υπολογίσετε τη λοξότητα πληθυσμού"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "Επιστρέφει την κλίση της γραμμής γραμμικής παλινδρόμησης που διέρχεται από τα καθορισμένα σημεία δεδομένων", "ad": "είναι ένας πίνακας ή μια περιοχή κελιών αριθμητικά εξαρτημένων σημείων δεδομένων και μπορεί να είναι αριθμοί ή ονόματα, καθ<PERSON><PERSON> και πίνακες ή αναφορές που περιέχουν αριθμούς!είναι η ομάδα ανεξάρτητων σημείων δεδομένων και μπορεί να είναι αριθμοί ή ονόματα, καθώς και πίνακες ή αναφορές που περιέχουν αριθμούς"}, "SMALL": {"a": "(πίνακας; k)", "d": "Αποδίδει την k μικρότερη τιμή σε ένα σύνολο δεδομένων. Για παράδειγμα, τον πέμπτο μικρότερο αριθμό", "ad": "είναι ο πίνακας ή η περιοχή δεδομένων των οποίων θέλετε να καθοριστεί η k μικρότερη τιμή!είναι η θέση (ξε<PERSON><PERSON>ν<PERSON>ντας από τη μικρότερη) στον πίνακα ή στην περιοχή κελιών της τιμής που θα αποδοθεί"}, "STANDARDIZE": {"a": "(x; μέση_τιμή; τυπική_απόκλιση)", "d": "Αποδίδει μια κανονικοποιημένη τιμή από μια κατανομή, η οποία χαρακτηρίζεται από έναν αριθμητικό μέσο και μια μέση απόκλιση τετραγώνου", "ad": "είναι η τιμή που θέλετε να κανονικοποιήσετε!είναι ο αριθμητικός μέσος της κατανομής!είναι η μέση απόκλιση τετραγώνου της κατανομής, ένας θετικός αριθμός"}, "STDEV": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Υπολογίζει τη μέση απόκλιση τετραγώνου βάσει ενός δείγματος (παραβλέπει τις λογικές τιμές και το κείμενο μέσα στο δείγμα)", "ad": "είναι 1 έως 255 αριθμοί που αντιστοιχούν στο δείγμα ενός πληθυσμού και μπορεί να είναι αριθμοί ή αναφορές που περιέχουν αριθμούς"}, "STDEV.P": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Υπολογίζει την τυπική απόκλιση βάσει ολόκληρου του πληθυσμού των ορισμάτων (παραβλέπει λογικές τιμές και κείμενο)", "ad": "είναι 1 έως 255 αριθμοί που αντιστοιχούν σε έναν πληθυσμό και μπορούν να είναι αριθμοί ή αναφορές που περιέχουν αριθμούς"}, "STDEV.S": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Εκτιμά την τυπική απόκλιση με βάση ένα δείγμα (παραβλέπει λογικές τιμές και κείμενο στο δείγμα)", "ad": " είναι 1 έως 255 αριθμοί που αντιστοιχούν στο δείγμα ενός πληθυσμού και μπορεί να είναι αριθμοί ή αναφορές που περιέχουν αριθμούς"}, "STDEVA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Εκτιμά την τυπική απόκλιση βάσει δείγματος, μαζί με λογικές τιμές και κείμενο. Κείμενο και λογική τιμή FALSE = 0, λογική τιμή TRUE = 1", "ad": "είναι 1 έως 255 τιμές που αντιστοιχούν σε δείγμα πληθυσμού και μπορεί να είναι τιμές, ονό<PERSON>α<PERSON><PERSON> ή αναφορές σε τιμές"}, "STDEVP": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Υπολογίζει την τυπική απόκλιση βάσει ολόκληρου του πληθυσμού των ορισμάτων (παραβλέπει λογικές τιμές και κείμενο)", "ad": "είναι 1 έως 255 αριθμοί που αντιστοιχούν σε έναν πληθυσμό και μπορούν να είναι αριθμοί ή αναφορές που περιέχουν αριθμούς"}, "STDEVPA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Υπολογίζει την τυπική απόκλιση βάσει όλου του πληθυσμού, μαζί με λογικές τιμές και κείμενο. Κείμενο και λογική τιμή FALSE = 0, λογική τιμή TRUE = 1.", "ad": "είναι 1 έως 255 τιμές που αντιστοιχούν σε πληθυσμό και μπορεί να είναι τιμές, ον<PERSON><PERSON>ατα, πίνακες ή αναφορές με τιμές"}, "STEYX": {"a": "(known_ys; known_xs)", "d": "Επιστρέφει το τυπικό σφάλμα της προβλεπόμενης τιμής y για κάθε x σε μια παλινδρόμηση", "ad": "είναι ένας πίνακας ή μια περιοχή εξαρτημένων σημείων δεδομένων και μπορεί να είναι αριθμοί ή ονόματα, καθ<PERSON><PERSON> και πίνακες ή αναφορές που περιέχουν αριθμούς!είναι ένας πίνακας ή μια περιοχή ανεξάρτητων σημείων δεδομένων και μπορεί να είναι αριθμοί ή ονόματα, καθώς και πίνακες ή αναφορές που περιέχουν αριθμούς"}, "TDIST": {"a": "(x; βαθμοί_ελευθερίας; ουρές)", "d": "Αποδίδει την κατανομή t Student", "ad": "είναι η αριθμητική τιμή στην οποία θα υπολογιστεί η κατανομή!είναι ένας ακέραιος που δείχνει τον αριθμό των βαθμών ελευθερίας που χαρακτηρίζουν την κατανομή!καθορίζει τον αριθμό των ουρών κατανομής που θα επιστραφούν. Παίρνει την τιμή 1 για κατανομή μιας ουράς ή την τιμή 2 για κατανομή δύο ουρών"}, "TINV": {"a": "(πιθανότητα; βαθμοί_ελευθερίας)", "d": "Αποδίδει το αντίστροφο της κατανομής δύο ουρών t Student", "ad": "είναι η πιθανότητα, η σχετική με την κατανομή δύο ουρών t της Student, ένας αριθμός στο κλειστό διάστημα από 0 έως 1!είναι ένας θετικός ακέραιος που καθορίζει τους βαθμούς ελευθερίας που χαρακτηρίζουν την κατανομή"}, "T.DIST": {"a": "(x; βαθμοί_ελευθερίας; αθροιστική)", "d": "Αποδίδει την κατανομή t Student αριστερής πλευράς", "ad": "είναι η αριθμητική τιμή στην οποία θα υπολογιστεί η κατανομή!είναι ένας ακέραιος που δείχνει τον αριθμό των βαθμών ελευθερίας που χαρακτηρίζουν την κατανομή!είναι μια λογική τιμή: παίρνει την τιμή TRUE για την αθροιστική κατανομή και FALSE για τη συνάρτηση πυκνότητας πιθανότητας"}, "T.DIST.2T": {"a": "(x; βαθμοί_ελευθερίας)", "d": "Αποδίδει τη δίπλευρη κατανομή t Student", "ad": "είναι η αριθμητική τιμή στην οποία θα υπολογιστεί η κατανομή!είναι ένας ακέραιος που δείχνει τον αριθμό των βαθμών ελευθερίας που χαρακτηρίζουν την κατανομή"}, "T.DIST.RT": {"a": "(x; βαθμοί_ελευθερίας)", "d": "Αποδίδει την κατανομή t Student δεξιάς πλευράς", "ad": "είναι η αριθμητική τιμή στην οποία θα υπολογιστεί η κατανομή!είναι ένας ακέραιος που δείχνει τον αριθμό των βαθμών ελευθερίας που χαρακτηρίζουν την κατανομή"}, "T.INV": {"a": "(πιθανότητα; βαθμοί_ελευθερίας)", "d": "Αποδίδει το αντίστροφο αριστερής πλευράς της κατανομής t Student", "ad": "είναι η πιθανότητα, η σχετική με τη δίπλευρη κατανομή t Student, ένας αριθμός στο κλειστό διάστημα 0 έως 1!είν<PERSON><PERSON> ένας θετικός ακέραιος που καθορίζει τον αριθμό βαθμών ελευθερίας που χαρακτηρίζουν την κατανομή"}, "T.INV.2T": {"a": "(πιθανότητα; βαθμοί_ελευθερίας)", "d": "Αποδίδει το δίπλευρο αντίστροφο της κατανομής t Student", "ad": "είναι η πιθανότητα, η σχετική με τη δίπλευρη κατανομή t Student, ένας αριθμός στο κλειστό διάστημα 0 έως 1!είν<PERSON><PERSON> ένας θετικός ακέραιος που καθορίζει τον αριθμό βαθμών ελευθερίας που χαρακτηρίζουν την κατανομή"}, "T.TEST": {"a": "(πίνακας1; πίνακας2; ουρές; τύπος)", "d": "Αποδίδει την πιθανότητα που σχετίζεται με έναν έλεγχο t Student", "ad": "είναι το πρώτο σύνολο δεδομένων!είναι το δεύτερο σύνολο δεδομένων!καθορίζει τον αριθμό των πλευρών κατανομής που θα επιστραφούν. Παίρνει την τιμή 1 για μονόπλευρη κατανομή ή την τιμή 2 για δίπλευρη κατανομή.!είναι το είδος του ελέγχου t: ανά ζεύγη= 1, ίση διακύμανση (με τις ίδιες στατιστικές διαφορές) δύο δειγμάτων= 2, άνιση διακύμανση δύο δειγμάτων= 3"}, "TREND": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Επιστρέφει αριθμούς σε μια γραμμική τάση που διέρχεται από γνωστά σημεία δεδομένων, χρησιμοποιώντας τη μέθοδο των ελαχίστων τετραγώνων", "ad": "είναι μια περιοχή ή ένας πίνακας τιμών y που ήδη γνωρίζετε στη σχέση y = mx + b!είναι μια προαιρετική περιοχή ή ένας πίνακας τιμών x που ήδη γνωρίζετε στη σχέση y = mx + b, ένας πίνακας ίδιου μεγέθους με αυτόν του Known_y!είναι μια περιοχή ή ένας πίνακας νέων τιμών x, για τις οποίες θέλετε η συνάρτηση TREND να επιστρέψει τις αντίστοιχες τιμές y!είναι μια λογική τιμή: η σταθερά b υπολογίζεται κανονικά εάν Const = TRUE ή παραλείπεται και η σταθερά b ορίζεται ως ίση με 0 εάν Const = FALSE"}, "TRIMMEAN": {"a": "(πίνακας; ποσοστό)", "d": "Αποδίδει τον αριθμητικό μέσο του εσωτερικού μιας ομάδας τιμών δεδομένων", "ad": "είναι ο πίνακας ή η περιοχή των τιμών, όπου θα καταργηθούν τα κενά και θα υπολογιστεί ο μέσος όρος!είναι ο κλασματικός αριθμός των σημείων δεδομένων που θα εξαιρεθούν από την αρχή και το τέλος του συνόλου δεδομένων"}, "TTEST": {"a": "(πίνακας1; πίνακας2; ουρές; τύπος)", "d": "Αποδίδει την πιθανότητα που σχετίζεται με έναν έλεγχο t Student", "ad": "είναι το πρώτο σύνολο δεδομένων!είναι το δεύτερο σύνολο δεδομένων!καθορίζει τον αριθμό των ουρών κατανομής που θα επιστραφούν. Παίρνει την τιμή 1 για μονόπλευρη κατανομή ή την τιμή 2 για δίπλευρη κατανομή!είναι το είδος του ελέγχου t: ανά ζεύγη= 1, ίση διακύμανση (με τις ίδιες στατιστικές διαφορές) δύο δειγμάτων= 2, άνιση διακύμανση δύο δειγμάτων= 3"}, "VAR": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Υπολογίζει τη διακύμανση βάσει ενός δείγματος (παραβλέπει λογικές τιμές και κείμενο στο δείγμα)", "ad": "είναι 1 έως 255 αριθμητικά ορίσματα που αντιστοιχούν στο δείγμα ενός πληθυσμού"}, "VAR.P": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Υπολογίζει τη διακύμανση βάσει ολόκληρου του πληθυσμού (παραβλέπει λογικές τιμές και κείμενο στον πληθυσμό)", "ad": "είναι 1 έως 255 αριθμητικά ορίσματα που αντιστοιχούν σε έναν πληθυσμό"}, "VAR.S": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Εκτιμά τη διακύμανση βάσει ενός δείγματος (παραβλέπει λογικές τιμές και κείμενο στο δείγμα)", "ad": "είναι 1 έως 255 αριθμητικά ορίσματα που αντιστοιχούν στο δείγμα ενός πληθυσμού"}, "VARA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Εκτιμά τη διακύμανση βάσει ενός δείγματος, συμπεριλαμβάνοντας τις λογικές τιμές και το κείμενο. Το κείμενο και η λογική τιμή FALSE αντιστοιχούν στο 0, ενώ η λογική τιμή TRUE αντιστοιχεί στην τιμή 1", "ad": "είναι 1 έως 255 ορίσματα τιμών που αντιστοιχούν σε δείγμα του πληθυσμού"}, "VARP": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Υπολογίζει τη διακύμανση βάσει ολόκληρου του πληθυσμού (παραβλέπει λογικές τιμές και κείμενο στον πληθυσμό)", "ad": "είναι 1 έως 255 αριθμητικά ορίσματα που αντιστοιχούν σε έναν πληθυσμό"}, "VARPA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Υπολογίζει τη διακύμανση βάσει όλου του πληθυσμού, μαζί με λογικές τιμές και κείμενο. Κείμενο και λογική τιμή FALSE = 0, λογική τιμή TRUE = 1", "ad": "είναι 1 έως 255 ορίσματα τιμών που αντιστοιχούν σε έναν πληθυσμό"}, "WEIBULL": {"a": "(x; άλφα; βήτα; αθροιστική)", "d": "Αποδίδει την κατα<PERSON><PERSON><PERSON><PERSON>", "ad": "είναι η τιμή για την οποία θα υπολογίσετε τη συνάρτηση, ένας μη αρνητικός αριθμός!είναι μια παράμετρος της κατανομής, ένας θετικός αριθμός!είναι μια παράμετρος της κατανομής, ένας θετικός αριθμός!είναι μια λογική τιμή που παίρνει την τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή FALSE για τη συνάρτηση πιθανότητας"}, "WEIBULL.DIST": {"a": "(x; άλφα; βήτα; αθροιστική)", "d": "Αποδίδει την κατα<PERSON><PERSON><PERSON><PERSON>", "ad": "είναι η τιμή για την οποία θα υπολογίσετε τη συνάρτηση, ένας μη αρνητικός αριθμός!είναι μια παράμετρος της κατανομής, ένας θετικός αριθμός!είναι μια παράμετρος της κατανομής, ένας θετικός αριθμός!είναι μια λογική τιμή που παίρνει την τιμή TRUE για την αθροιστική συνάρτηση κατανομής ή FALSE για τη συνάρτηση πιθανότητας"}, "Z.TEST": {"a": "(πίνακας; x; [σίγμα])", "d": "Αποδίδει τη μονόπλευρη τιμή P ενός ελέγχου z", "ad": "είναι ο πίνακας ή η περιοχή δεδομένων έναντι των οποίων θα γίνει ο έλεγχος του Χ!είναι η τιμή που θα ελεγχθεί!είναι η μέση απόκλιση τετραγώνου του πληθυσμού (γνωστού). Εάν παραλειφθεί, χρησιμοποιείται η μέση απόκλιση του δείγματος"}, "ZTEST": {"a": "(πίνακας; x; [σίγμα])", "d": "Αποδίδει τη μονόπλευρη τιμή P ενός ελέγχου z", "ad": "είναι ο πίνακας ή η περιοχή δεδομένων έναντι των οποίων θα γίνει ο έλεγχος του Χ!είναι η τιμή που θα ελεγχθεί!είναι η μέση απόκλιση τετραγώνου του πληθυσμού (γνωστού). Εάν παραλειφθεί, χρησιμοποιείται η μέση απόκλιση του δείγματος"}, "ACCRINT": {"a": "(έκδοση; πρώτο_επιτόκιο; διακανονισμός; επιτ<PERSON>κι<PERSON>; ονομαστική_αξία; συχνότητα; [βάση]; [μέθοδος_υπολογισμού])", "d": "Αποδίδει τον πληρωτέο τόκο ενός χρεογράφου που δίνει περιοδικό τόκο.", "ad": "είναι η ημερομηνία έκδοσης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία συμφωνίας του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το ετήσιο επιτόκιο των τοκομεριδίων του χρεογράφου!είναι η ονομαστική αξία του χρεογράφου!είναι ο αριθμός των πληρωμών των τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί!είναι μια λογική τιμή: για υπολογισμό τόκου από την ημερομηνία έκδοσης = TRUE ή παραλείπεται, για τον υπολογισμό από την ημερομηνία πληρωμής τοκομεριδίου = FALSE"}, "ACCRINTM": {"a": "(έκδοση; διακανονισμός; επιτ<PERSON>κι<PERSON>; ονομαστική_αξία; [βάση])", "d": "Αποδίδει τον πληρωτέο τόκο ενός χρεογράφου που δίνει τόκο κατά τη λήξη", "ad": "είναι η ημερομηνία έκδοσης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το ετήσιο επιτόκιο των τοκομεριδίων του χρεογράφου!είναι η ονομαστική αξία του χρεογράφου!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "AMORDEGRC": {"a": "(κόστος; ημερομηνία_αγοράς; πρώτη_περίοδος; υπολειμματική; περίοδος; συντελεστής; [βάση])", "d": "Αποδίδει την αναλογική γραμμική απόσβεση περιουσιακού στοιχείου για κάθε λογιστική περίοδο", "ad": "είναι το κόστος του περιουσιακού στοιχείου!είναι η ημερομηνία αγοράς του περιουσιακού στοιχείου!είναι η ημερομηνία του τέλους της πρώτης περιόδου!είναι η υπολειμματική αξία στο τέλος της ζωής του περιουσιακού στοιχείου!είναι η περίοδος!είναι ο συντελεστής απόσβεσης!βάση_έτους: 0 για έτος 360 ημερών, 1 για το πραγματικό, 3 για έτος 365 ημερών."}, "AMORLINC": {"a": "(κόστος; ημερομηνία_αγοράς; πρώτη_περίοδος; υπολειμματική; περίοδος; συντελεστής; [βάση])", "d": "Αποδίδει την αναλογική γραμμική απόσβεση περιουσιακού στοιχείου για κάθε λογιστική περίοδο", "ad": "είναι το κόστος του περιουσιακού στοιχείου!είναι η ημερομηνία αγοράς του περιουσιακού στοιχείου!είναι η ημερομηνία του τέλους της πρώτης περιόδου!είναι η υπολειμματική αξία στο τέλος της ζωής του περιουσιακού στοιχείου!είναι η περίοδος!είναι ο συντελεστής απόσβεσης!βάση_έτους: 0 για έτος 360 ημερών, 1 για το πραγματικό, 3 για έτος 365 ημερών."}, "COUPDAYBS": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; συχνότητα; [βάση])", "d": "Αποδίδει τον αριθμό των ημερών από την αρχή της περιόδου τοκομεριδίων μέχρι την ημερομηνία διακανονισμού", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι ο αριθμός των πληρωμών τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "COUPDAYS": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; συχνότητα; [βάση])", "d": "Αποδίδει τον αριθμό των ημερών της περιόδου τοκομεριδίων που περιλαμβάνει την ημερομηνία διακανονισμού", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι ο αριθμός των πληρωμών τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "COUPDAYSNC": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; συχνότητα; [βάση])", "d": "Αποδίδει τον αριθμό των ημερών από την ημερομηνία διακανονισμού έως την ημερομηνία του επόμενου τοκομεριδίου", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι ο αριθμός των πληρωμών τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "COUPNCD": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; συχνότητα; [βάση])", "d": "Αποδίδει την επόμενη ημερομηνία τοκομεριδίου μετά την ημερομηνία διακανονισμού", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι ο αριθμός των πληρωμών τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "COUPNUM": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; συχνότητα; [βάση])", "d": "Αποδίδει τον αριθμό των πληρωτέων τοκομεριδίων μεταξύ της ημερομηνίας διακανονισμού και της ημερομηνίας λήξης", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι ο αριθμός των πληρωμών τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "COUPPCD": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; συχνότητα; [βάση])", "d": "Αποδίδει την ημερομηνία του προηγούμενου τοκομεριδίου πριν από την ημερομηνία διακανονισμού", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι ο αριθμός των πληρωμών τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "CUMIPMT": {"a": "(επιτ<PERSON><PERSON><PERSON><PERSON>; αριθμός_περιόδων; παρούσα_αξία; περίοδος_έναρξης; περίοδος_λήξης; τύπος)", "d": "Αποδίδει τον σωρευτικό τόκο πληρωμής μεταξύ δύο περιόδων", "ad": "είναι το επιτόκιο!είναι ο συνολικός αριθμός των περιόδων πληρωμής!είναι η παρούσα αξία!είναι η πρώτη περίοδος στον υπολογισμό!είναι η τελευταία  περίοδος στον υπολογισμό!είναι η ρύθμιση του χρόνου πληρωμής"}, "CUMPRINC": {"a": "(επιτ<PERSON><PERSON><PERSON><PERSON>; αριθμός_περιόδων; παρούσα_αξία; περίοδος_έναρξης; περίοδος_λήξης; τύπος)", "d": "Αποδίδει το σωρευτικ<PERSON> κεφάλαιο πληρωμής μεταξύ δύο περιόδων", "ad": "είναι το επιτόκιο!είναι ο συνολικός αριθμός των περιόδων πληρωμής!είναι η παρούσα αξία!είναι η πρώτη περίοδος στον υπολογισμό!είναι η τελευταία  περίοδος στον υπολογισμό!είναι η ρύθμιση του χρόνου πληρωμής"}, "DB": {"a": "(κόστος; υπολειμματική; ζωή; περίοδος; [μήνας])", "d": "Αποδίδει την απόσβεση περιουσιακού στοιχείου για καθορισμένη περίοδο με τη μέθοδο του σταθερά φθίνοντος υπολοίπου", "ad": "είναι το αρχικ<PERSON> κόστος περιουσιακού στοιχείου!είναι η υπολειπόμενη αξία στο τέλος της ζωής του περιουσιακού στοιχείου!είναι ο αριθμός των περιόδων κατά τις οποίες γίνεται η απόσβεση του περιουσιακού στοιχείου (μερικές φορές λέγεται ωφέλιμη ζωή του περιουσιακού στοιχείου)!είναι η περίοδος για την οποία θέλετε να υπολογίσετε την απόσβεση. Η περίοδος πρέπει να χρησιμοποιεί τις ίδιες μονάδες με την ωφέλιμη ζωή.!είναι ο αριθμός των μηνών του πρώτου έτους. Εάν ο μήνας παραλείπεται, θεωρείται 12"}, "DDB": {"a": "(κόστος; υπολειμματική; ζωή; περίοδος; [παράγοντας])", "d": "Αποδίδει την απόσβεση περιουσιακού στοιχείου για μια συγκεκριμένη περίοδο, με τη μέθοδο του διπλά φθίνοντος υπολοίπου ή με άλλη μέθοδο που καθορίζετε", "ad": "είναι το αρχικ<PERSON> κόστος περιουσιακού στοιχείου!είναι η υπολειμματική αξία στο τέλος της ζωής του περιουσιακού στοιχείου!είναι ο αριθμός των περιόδων κατά τις οποίες γίνεται η απόσβεση του περιουσιακού στοιχείου (μερικές φορές λέγεται ωφέλιμη ζωή του περιουσιακού στοιχείου)!είναι η περίοδος για την οποία θέλετε να υπολογίσετε την απόσβεση. Η περίοδος πρέπει να χρησιμοποιεί τις ίδιες μονάδες με την ωφέλιμη ζωή.!είναι ο ρυθμός με τον οποίο φθίνει το υπόλοιπο. Εάν ο συντελεστής παραλείπεται, θεωρείται ότι είναι 2 (η μέθοδος του διπλά φθίνοντος υπολοίπου)"}, "DISC": {"a": "(διακαν<PERSON><PERSON><PERSON>σμ<PERSON><PERSON>; λήξη; τιμή; εξαργύρωση; [βάση])", "d": "Αποδίδει το προεξοφλητικ<PERSON> επιτόκιο ενός χρεογράφου", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η τιμή του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι η τιμή εξαργύρωσης του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "DOLLARDE": {"a": "(κλασματικ<PERSON>ς_δολάριο; κλάσμα)", "d": "Μετατρέπει μια τιμή δολαρίων εκφρασμένη με κλάσμα, σε τιμή δολαρίων εκφρασμένη σε δεκαδικό αριθμό", "ad": "είναι ένας αριθμός εκφρασμένος με κλάσμα!είναι ο ακέραιος που θα χρησιμοποιηθεί στον παρονομαστή του κλάσματος"}, "DOLLARFR": {"a": "(δεκαδι<PERSON><PERSON><PERSON>_δολάριο; κλάσμα)", "d": "Μετατρέπει μια τιμή δολαρίων εκφρασμένη σε δεκαδικό αριθμό, σε τιμή δολαρίων εκφρασμένη σε κλάσμα", "ad": "είναι ένας δεκαδικός αριθμός!είναι ο ακέραιος που θα χρησιμοποιηθεί στον παρονομαστή του κλάσματος"}, "DURATION": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; τοκομερίδιο; απόδοση; συχνότητα; [βάση])", "d": "Αποδίδει την ετήσια διάρκεια ενός χρεογράφου με πληρωμές περιοδικού τόκου", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το ετήσιο επιτόκιο των τοκομεριδίων του χρεογράφου!είναι η ετήσια απόδοση του χρεογράφου!είναι ο αριθμός των πληρωμών των τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "EFFECT": {"a": "(ονομαστι<PERSON><PERSON>_επιτόκιο; αριθμός_περιόδων_ανά_έτος)", "d": "Αποδίδει το πραγματικό ετήσιο επιτόκιο", "ad": "είναι το ονομαστικό επιτόκιο!είναι ο αριθμός των σύνθετων περιόδων ανά έτος"}, "FV": {"a": "(επιτ<PERSON><PERSON><PERSON><PERSON>; αριθμός_περιόδων; πληρωμή; [παρούσα_αξία]; [τύπος])", "d": "Αποδίδει τη μελλοντική αξία μιας επένδυσης βάσει περιοδικών, σταθερών πληρωμών και ενός σταθερού επιτοκίου", "ad": "είναι το επιτόκιο ανά περίοδο. Για παράδειγμα, χρησιμοποιήστε 6%/4 για τριμηνιαίες πληρωμές με Ετήσιο Ποσοστό Επιβαρύνσεων 6%!είναι το συνολικό πλήθος περιόδων πληρωμών σε μια επένδυση!είναι η πληρωμή που γίνεται κάθε περίοδο. Δεν είναι δυνατή η αλλαγή της κατά τη διάρκεια της επένδυσης!είναι η παρούσα αξία ή το πληρωτέο εφάπαξ ποσό στο οποίο ανέρχεται αυτήν τη στιγμή μια σειρά μελλοντικών πληρωμών. Ε<PERSON>ν παραλειφθεί, Παρούσα_αξία=0!είναι η τιμή που αναπαριστά το χρόνο πληρωμής: όταν η πληρωμή γίνεται στην αρχή της περιόδου, η τιμή είναι 1 και όταν η πληρωμή γίνεται στο τέλος της περιόδου, είναι 0 ή παραλείπεται"}, "FVSCHEDULE": {"a": "(κεφά<PERSON><PERSON><PERSON><PERSON>; χρονοδιάγραμμα)", "d": "Αποδίδει τη μελλοντική αξία ενός αρχικού κεφαλαίου μετά την εφαρμογή μιας σειράς επιτοκίων ανατοκισμού", "ad": "είναι η παρούσα αξία!είναι ένας πίνακας επιτοκίων που θα εφαρμοστούν"}, "INTRATE": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; επένδυση; εξαργύρωση; [βάση])", "d": "Αποδίδει το επιτό<PERSON>ι<PERSON> ενός πλήρως επενδεδυμένου χρεογράφου", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το ποσό που επενδύθηκε στο χρεόγραφο!είναι το ποσό που θα ληφθεί κατά τη λήξη!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "IPMT": {"a": "(επιτ<PERSON><PERSON><PERSON><PERSON>; περίοδος; αριθμός_περιόδων; παρούσα_αξία; [μελλοντική_αξία]; [τύπος])", "d": "Αποδίδει τον τόκο μιας επένδυσης για μια δεδομένη περίοδο βάσει περιοδικών, σταθερών πληρωμών και ενός σταθερού επιτοκίου", "ad": "είναι το επιτόκιο ανά περίοδο. Για παράδειγμα, χρησιμοποιήστε 6%/4 για τριμηνιαίες πληρωμές με Ετήσιο Ποσοστό Επιβαρύνσεων 6%!είναι η περίοδος για την οποία θέλετε να βρείτε τον τόκο και πρέπει να είναι μεταξύ 1 και Νper!είναι το συνολικό πλήθος περιόδων πληρωμών σε μια επένδυση!είναι η παρούσα αξία ή το πληρωτέο εφάπαξ ποσό στο οποίο ανέρχεται αυτήν τη στιγμή μια σειρά μελλοντικών πληρωμών!είναι η μελλοντική αξία ή το υπόλοιπο ταμείου που θέλετε να επιτύχετε αφού γίνει η τελευταία πληρωμή. Εάν παραλειφθεί, Μελλοντική_αξία=0.!είναι μια λογική τιμή: όταν η πληρωμή γίνεται στο τέλος της περιόδου, η τιμή είναι 0 ή παραλείπεται και όταν η πληρωμή γίνεται στην αρχή της περιόδου, η τιμή είναι 1"}, "IRR": {"a": "(τιμές; [εκτίμηση])", "d": "Αποδίδει τον εσωτερικό ρυθμό απόδοσης μιας σειράς ταμειακών ροών", "ad": "είναι ένας πίνακας ή αναφορά σε κελιά που περιέχουν αριθμούς για τους οποίους θέλετε να υπολογίσετε τον εσωτερικό ρυθμό απόδοσης!είναι ο αριθμός που εκτιμάτε ότι προσεγγίζει το αποτέλεσμα του ΕΡΑ. Παίρνει την τιμή 0,1 (10%) αν παραλειφθεί"}, "ISPMT": {"a": "(επιτ<PERSON><PERSON><PERSON><PERSON>; περίοδος; αριθμός_περιόδων; παρούσα_αξία)", "d": "Επιστρέφει τον τόκο που καταβάλλεται σε μια συγκεκριμένη περίοδο μιας επένδυσης", "ad": " Για παράδειγμα, χρησιμοποιήστε 6%/4 για τριμηνιαίες πληρωμές με Ετήσιο Ποσοστό Επιβαρύνσεων 6%! περίοδος για την οποία θέλετε να υπολογίσετε τον τόκο! το πλήθος των περιόδων πληρωμών μιας επένδυσης! το πληρωτέο εφάπαξ ποσό στο οποίο ανέρχεται αυτήν τη στιγμή μια σειρά μελλοντικών πληρωμών"}, "MDURATION": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; τοκομερίδιο; απόδοση; συχνότητα; [βάση])", "d": "Αποδίδει την τροποποιημένη διάρκε<PERSON><PERSON>ley για ένα χρεόγραφο με υποθετική ονομαστική αξία 100 €", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το ετήσιο επιτόκιο των τοκομεριδίων του χρεογράφου!είναι η ετήσια απόδοση του χρεογράφου!είναι ο αριθμός των πληρωμών των τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "MIRR": {"a": "(τιμές; απόδοση; επιτόκιο_επανεπένδυσης)", "d": "Αποδίδει τον εσωτερικό ρυθμό απόδοσης για μια σειρά περιοδικών ταμειακών ροών, λα<PERSON><PERSON><PERSON>νοντας υπόψη το κόστος της επένδυσης και του επιτοκίου κατά την εκ νέου επένδυση των ροών", "ad": "είναι ένας πίνακας ή μια αναφορά σε κελιά που περιέχουν αριθμούς, οι οποίοι αναπαριστούν μια σειρά από πληρωμές (αρνητικ<PERSON><PERSON>) και εισοδήματα (θετικοί) σε τακτικές χρονικές περιόδους!είναι το επιτόκιο που πληρώνετε για τις ταμειακές ροές!είναι το επιτόκιο που λαμβάνετε από τις ταμειακές ροές, όταν τις επενδύετε εκ νέου"}, "NOMINAL": {"a": "(πραγματι<PERSON><PERSON>_επιτόκιο; αριθμός_περιόδων_ανά_έτος)", "d": "Αποδίδει το ετήσιο ονομαστικ<PERSON> επιτόκιο", "ad": "είναι το πραγματικό επιτόκιο!είναι ο αριθμός των σύνθετων περιόδων ανά έτος"}, "NPER": {"a": "(επιτ<PERSON><PERSON>ι<PERSON>; πληρωμή; παρούσα_αξία; [μελλοντική_αξία]; [τύπος])", "d": "Αποδίδει το πλήθος των περιόδων μιας επένδυσης, β<PERSON><PERSON><PERSON><PERSON> περιοδικών, σταθ<PERSON>ρών πληρωμών και ενός σταθερού επιτοκίου", "ad": "είναι το επιτόκιο ανά περίοδο. Για παράδειγμα, χρησιμοποιήστε 6%/4 για τριμηνιαίες πληρωμές με Ετήσιο Ποσοστό Επιβαρύνσεων 6%!είναι η πληρωμή που γίνεται κάθε περίοδο. Δεν είναι δυνατή η αλλαγή της κατά τη διάρκεια της επένδυσης!είναι η παρούσα αξία ή το πληρωτέο εφάπαξ ποσό στο οποίο ανέρχεται αυτήν τη στιγμή μια σειρά μελλοντικών πληρωμών!είναι η μελλοντική αξία ή το υπόλοιπο ταμείου που θέλετε να επιτύχετε αφού γίνει η τελευταία πληρωμή. <PERSON><PERSON>ν παραλειφθεί, χρησιμοποιείται το μηδέν!είναι μια λογική τιμή: όταν η πληρωμή γίνεται στην αρχή της περιόδου, η τιμή είναι 1 και όταν η πληρωμή γίνεται στο τέλος της περιόδου, η τιμή είναι 0 ή παραλείπεται"}, "NPV": {"a": "(επιτόκι<PERSON>; αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει την καθαρή παρούσα αξία μιας επένδυσης, β<PERSON><PERSON><PERSON><PERSON> ενός προεξοφλητικού επιτοκίου και μιας σειράς από μελλοντικές πληρωμές (αρνητικές τιμές) και εισοδήματα (θετικές τιμές)", "ad": "είναι το προεξοφλητικ<PERSON> επιτόκιο στο διάστημα μιας περιόδου!είναι 1 έως 254 ορίσματα που αναπαριστούν πληρωμές και εισόδημα, που έχουν κατανεμηθεί ισοδύναμα στο χρόνο και καταβάλλονται στο τέλος της περιόδου"}, "ODDFPRICE": {"a": "(διακαν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; έκδοση; πρώτο_τοκομερίδιο; επιτόκιο; απόδοση; εξαργύρωση; συχνότητα; [βάση])", "d": "Αποδίδει την τιμή ανά 100 € ονομαστικής αξίας για ένα χρεόγραφο με μη τακτική πρώτη περίοδο", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία έκδοσης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία του πρώτου τοκομεριδίου του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το επιτόκιο του χρεογράφου!είναι η ετήσια απόδοση του χρεογράφου!είναι η τιμή εξαργύρωσης του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι ο αριθμός των πληρωμών των τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "ODDFYIELD": {"a": "(διακαν<PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; έκδοση; πρώτο_τοκομερίδιο; επιτόκιο; τιμή; εξαργύρωση; συχνότητα; [βάση])", "d": "Αποδίδει την απόδοση ενός χρεογράφου με μη τακτική πρώτη περίοδο", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία έκδοσης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία του πρώτου τοκομεριδίου του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το επιτόκιο του χρεογράφου!είναι η τιμή του χρεογράφου!είναι η τιμή εξαργύρωσης του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι ο αριθμός των πληρωμών των τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "ODDLPRICE": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; τελευτα<PERSON><PERSON>; επιτόκι<PERSON>; απόδοση; αποζημίωση; συχνότητα; [βάση])", "d": "Αποδίδει την τιμή ανά 100 € ονομαστικής αξίας ενός χρεογράφου με μη τακτική τελευταία περίοδο", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία του τελευταίου τοκομεριδίου του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το επιτόκιο του χρεογράφου!είναι η ετήσια απόδοση του χρεογράφου!είναι η τιμή εξαργύρωσης του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι ο αριθμός των πληρωμών των τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "ODDLYIELD": {"a": "(διακαν<PERSON><PERSON><PERSON><PERSON>μ<PERSON>ς; λήξη; τελευτα<PERSON><PERSON>; επιτόκιο; τιμή; αποζημίωση; συχνότητα; [βάση])", "d": "Αποδίδει την απόδοση ενός χρεογράφου με μη τακτική τελευταία περίοδο", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία του τελευταίου τοκομεριδίου του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το επιτόκιο του χρεογράφου!είναι η τιμή του χρεογράφου!είναι η τιμή εξαργύρωσης του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι ο αριθμός των πληρωμών των τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "PDURATION": {"a": "(επιτ<PERSON><PERSON>ι<PERSON>; παρούσα_αξία; μελλοντική_αξία)", "d": "Αποδίδει τον αριθμό περιόδων που απαιτούνται προκειμένου μια επένδυση να φτάσει σε μια καθορισμένη τιμή", "ad": "είναι το επιτόκιο ανά περίοδο.!είναι η παρούσα αξία της επένδυσης!είναι η επιθυμητή μελλοντική αξία της επένδυσης"}, "PMT": {"a": "(επιτ<PERSON><PERSON>ι<PERSON>; πληρωμή; παρούσα_αξία; [μελλοντική_αξία]; [τύπος])", "d": "Αποδίδει την πληρωμή για ένα δάνειο, βά<PERSON><PERSON>ι σταθερών πληρωμών και ενός σταθερού επιτοκίου", "ad": "είναι το επιτόκιο του δανείου ανά περίοδο. Για παράδειγμα, χρησιμοποιήστε 6%/4 για τριμηνιαίες πληρωμές με Ετήσιο Ποσοστό Επιβαρύνσεων 6%!είναι το συνολικό πλήθος πληρωμών του δανείου!είναι η παρούσα αξία: το συνολικό ποσό στο οποίο ανέρχεται αυτή τη στιγμή μια σειρά μελλοντικών πληρωμών!είναι  η μελλοντική αξία ή το υπόλοιπο ταμείου που θέλετε να επιτύχετε, αφού γίνει η τελευταία πληρωμή. Παίρνει την τιμή μηδέν (0) αν παραλειφθεί!είναι μια λογική τιμή: όταν η πληρωμή γίνεται στην αρχή της περιόδου, η τιμή είναι 1 και όταν η πληρωμή γίνεται στο τέλος της περιόδου, η τιμή είναι 0 ή παραλείπεται"}, "PPMT": {"a": "(επιτ<PERSON><PERSON><PERSON><PERSON>; περίοδος; αριθμός_περιόδων; παρούσα_αξία; [μελλοντική_αξία]; [τύπος])", "d": "Αποδίδει την πληρωμή επί του αρχικού κεφαλαίου μιας επένδυσης, βά<PERSON><PERSON><PERSON> περιοδικών, σταθ<PERSON><PERSON><PERSON><PERSON> πληρωμών και ενός σταθερού επιτοκίου", "ad": "είναι το επιτόκιο ανά περίοδο. Για παράδειγμα, χρησιμοποιήστε 6%/4 για τριμηνιαίες πληρωμές με Ετήσιο Ποσοστό Επιβαρύνσεων 6%!καθορίζει την περίοδο και πρέπει να κυμαίνεται μεταξύ 1 και nper!είναι το συνολικό πλήθος περιόδων πληρωμών σε μια επένδυση!είναι η παρούσα αξία: το συνολικό ποσό στο οποίο ανέρχεται αυτή τη στιγμή μια σειρά μελλοντικών πληρωμών!είναι η μελλοντική αξία ή το υπόλοιπο ταμείου που θέλετε να επιτύχετε, αφού γίνει η τελευταία πληρωμή!είναι μια λογική τιμή: όταν η πληρωμή γίνεται στην αρχή της περιόδου, η τιμή είναι 1 και όταν η πληρωμή γίνεται στο τέλος της περιόδου, η τιμή είναι 0 ή παραλείπεται"}, "PRICE": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; επιτ<PERSON><PERSON>ι<PERSON>; απόδοση; εξαργύρωση; συχνότητα; [βάση])", "d": "Αποδίδει την τιμή ανά 100 € ονομαστικής αξίας ενός χρεογράφου που δίνει περιοδικό τόκο", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το ετήσιο επιτόκιο των τοκομεριδίων του χρεογράφου!είναι η ετήσια απόδοση του χρεογράφου!είναι η τιμή εξαργύρωσης του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι ο αριθμός των πληρωμών των τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "PRICEDISC": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; προεξόφληση; εξαργύρωση; [βάση])", "d": "Αποδίδει την τιμή ανά 100 €   ονομαστικής αξίας ενός προεξοφληθέντος χρεογράφου", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το προεξοφλητικό επιτόκιο του χρεογράφου!είναι η τιμή εξαργύρωσης του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "PRICEMAT": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; έκδοση; επιτόκιο; απόδοση; [βάση])", "d": "Αποδίδει τιμή ανά 100 € ονομαστικής αξίας χρεογράφου που δίνει περιοδικό τόκο", "ad": "είναι η ημερομηνία διακανονισμού χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το ετήσιο επιτόκιο τοκομεριδίων χρεογράφου!είναι η ετήσια απόδοση του χρεογράφου!είναι η τιμή εξαργύρωσης του χρεογράφου ανά 100 € ονομαστικής αξίας είναι ο αριθμός πληρωμών των τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "PV": {"a": "(επιτ<PERSON><PERSON>ι<PERSON>; αριθμός_περιόδων; πληρωμή; [μελλοντική_αξία]; [τύπος])", "d": "Αποδίδει την παρούσα αξία μιας επένδυσης, δηλαδή το συνολικό ποσό στο οποίο ανέρχεται αυτήν τη στιγμή μια σειρά μελλοντικών πληρωμών", "ad": "είναι το επιτόκιο ανά περίοδο. Για παράδειγμα, χρησιμοποιήστε 6%/4 για τριμηνιαίες πληρωμές με Ετήσιο Ποσοστό Επιβαρύνσεων 6%!είναι το συνολικό πλήθος περιόδων πληρωμών σε μια επένδυση!είναι η πληρωμή που γίνεται κάθε περίοδο και δεν μπορεί να αλλάξει κατά τη διάρκεια της επένδυσης!είναι η μελλοντική αξία ή το υπόλοιπο ταμείου που θέλετε να επιτύχετε, αφού γίνει η τελευταία πληρωμή!είναι μια λογική τιμή: όταν η πληρωμή γίνεται στην αρχή της περιόδου, η τιμή είναι 1 και όταν η πληρωμή γίνεται στο τέλος της περιόδου, η τιμή είναι 0 ή παραλείπεται"}, "RATE": {"a": "(αριθμός_περιόδων; πληρωμή; παρούσα_αξία; [μελλοντική_αξία]; [τύπος]; [εκτίμηση])", "d": "Αποδίδει το επιτόκιο ενός δανείου ή μιας επένδυσης ανά περίοδο. Για παράδειγμα, χρησιμοποιήστε 6%/4 για τριμηνιαίες πληρωμές με Ετήσιο Ποσοστό Επιβαρύνσεων 6%", "ad": "είναι το συνολι<PERSON><PERSON> πλήθος περιόδων πληρωμών του δανείου ή της επένδυσης!είναι η πληρωμή που γίνεται κάθε περίοδο και δεν μπορεί να αλλάξει κατά τη διάρκεια του δανείου ή της επένδυσης!είναι η παρούσα αξία: το συνολικό ποσό στο οποίο ανέρχεται αυτήν τη στιγμή μια σειρά μελλοντικών πληρωμών!είναι η μελλοντική αξία ή το υπόλοιπο ταμείου που θέλετε να επιτύχετε, αφού γίνει η τελευταία πληρωμή. <PERSON><PERSON><PERSON> παραλειφθεί, χρησιμοποιείται η τιμή Fv=0!είναι μια λογική τιμή: όταν η πληρωμή γίνεται στην αρχή της περιόδου, η τιμή είναι 1 και όταν η πληρωμή γίνεται στο τέλος της περιόδου, η τιμή είναι 0 ή παραλείπεται!είναι η εκτίμησή σας για την τιμή του επιτοκίου. Εάν παραλειφθεί, Guess = 0,1 (10%)"}, "RECEIVED": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; επένδυση; προεξόφληση; [βάση])", "d": "Αποδίδει το ποσό που παραλήφθηκε κατά τη λήξη ενός πλήρως επενδεδυμένου χρεογράφου", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το ποσό που επενδύθηκε στο χρεόγραφο!είναι το προεξοφλητικό επιτόκιο του χρεογράφου!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "RRI": {"a": "(αριθμός_περιόδων; παρούσα_αξία; μελλοντική_αξία)", "d": "Αποδίδει το ισοδύναμο επιτόκιο για την αύξηση μιας επένδυσης", "ad": "είναι ο αριθμός περιόδων της επένδυσης!είναι η παρούσα αξία της επένδυσης!είναι η μελλοντική αξία της επένδυσης"}, "SLN": {"a": "(κόστος; υπολειμματική; ζωή)", "d": "Αποδίδει τη σταθερή απόσβεση ενός περιουσιακού στοιχείου για μία περίοδο", "ad": "είναι το αρχικ<PERSON> κόστος του περιουσιακού στοιχείου!είναι η υπολειμματική αξία στο τέλος της ζωής του περιουσιακού στοιχείου!είναι ο αριθμός των περιόδων κατά τις οποίες γίνεται η απόσβεση του περιουσιακού στοιχείου (μερικές φορές λέγεται ωφέλιμη ζωή του περιουσιακού στοιχείου)"}, "SYD": {"a": "(κόστος; υπολειμματική; ζωή; περίοδος)", "d": "Αποδίδει την απόσβεση ενός περιουσιακού στοιχείου για μια καθορισμένη περίοδο με τη μέθοδο του αθροίσματος των ετών της ζωής του", "ad": "είναι το αρχι<PERSON><PERSON> κόστος του περιουσιακού στοιχείου!είναι η υπολειμματική αξία στο τέλος της ζωής του περιουσιακού στοιχείου!είναι ο αριθμός των περιόδων κατά τις οποίες γίνεται η απόσβεση του περιουσιακού στοιχείου (μερικές φορές λέγεται ωφέλιμη ζωή του περιουσιακού στοιχείου)!είναι η περίοδος και πρέπει να περιέχει τις ίδιες μονάδες με την ωφέλιμη ζωή"}, "TBILLEQ": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; προεξόφληση)", "d": "Αποδίδει την απόδοση που είναι ισοδύναμη με τις ομολογίες ενός εντόκου γραμματίου", "ad": "είναι η ημερομηνία διακανονισμού του εντόκου γραμματίου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του εντόκου γραμματίου, εκφρασμένη σε αύξοντα αριθμό ημερομηνίας!είναι το προεξοφλητικό επιτόκιο του εντόκου γραμματίου"}, "TBILLPRICE": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; προεξόφληση)", "d": "Αποδίδει την τιμή ανά 100 € ονομαστικής αξίας για ένα έντοκο γραμμάτιο", "ad": "είναι η ημερομηνία διακανονισμού του εντόκου γραμματίου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του έντοκου γραμματίου, εκφρασμένη σε αύξοντα αριθμό ημερομηνίας!είναι το προεξοφλητικό επιτόκιο του εντόκου γραμματίου"}, "TBILLYIELD": {"a": "(διακανονισμός; λήξη; τιμή)", "d": "Αποδίδει την απόδοση ενός εντόκου γραμματίου", "ad": "είναι η ημερομηνία διακανονισμού του εντόκου γραμματίου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του έντοκου γραμματίου, εκφρασμένη σε αύξοντα αριθμό ημερομηνίας!είναι η τιμή του εντόκου γραμματίου ανά 100 € ονομαστικής αξίας"}, "VDB": {"a": "(κόστος; υπολειμματική; ζωή; περίοδος_έναρξης; περίοδος_λήξης; [παράγοντας]; [χωρίς_μετάβαση])", "d": "Αποδίδει την απόσβεση περιουσιακού στοιχείου για καθορισμένη περίοδο ή τμήμα περιόδου, με τη μέθοδο φθίνοντος υπολοίπου ή με άλλη μέθοδο που καθορίζετε", "ad": "είναι το αρχικ<PERSON> κόστος του περιουσιακού στοιχείου!είναι η υπολειμματική αξία στο τέλος της ζωής του περιουσιακού στοιχείου!είναι ο αριθμός των περιόδων κατά τις οποίες γίνεται η απόσβεση του περιουσιακού στοιχείου (μερικές φορές λέγεται ωφέλιμη ζωή του περιουσιακού στοιχείου)!είναι η αρχή της περιόδου για την οποία θέλετε να υπολογιστεί η απόσβεση, με τις ίδιες μονάδες της ωφέλιμης ζωής!είναι το τέλος της περιόδου για την οποία θέλετε να υπολογιστεί η απόσβεση, με τις ίδιες μονάδες της ωφέλιμης ζωής!είναι ο ρυθμός με τον οποίο φθίνει το υπόλοιπο και παίρνει την τιμή 2 (μέθοδος διπλά φθίνοντος υπολοίπου) αν παραλειφθεί!μετάβαση σε σταθερή απόσβεση μόλις η απόσβεση ξεπεράσει το φθίνον υπόλοιπο=FALSE ή παραλείπεται. Χωρίς μετάβαση=TRUE"}, "XIRR": {"a": "(τιμές; ημερομηνίες; [εκτίμηση])", "d": "Αποδίδει το εσωτερικό ρυθμό απόδοσης για ένα χρονοδιάγραμμα ταμειακών ροών", "ad": "είναι μια σειρά ταμειακών ροών που αντιστοιχεί σε ένα χρονοδιάγραμμα πληρωμών στις ημερομηνίες!είναι ένα χρονοδιάγραμμα ημερομηνιών πληρωμής που αντιστοιχεί στις πληρωμές ταμειακής ροής!είναι ο αριθμός που πιστεύετε ότι είναι κοντά στο αποτέλεσμα της XIRR"}, "XNPV": {"a": "(επιτόκι<PERSON>; τιμές; ημερομηνίες)", "d": "Αποδίδει την καθαρή παρούσα αξία ενός χρονοδιαγράμματος", "ad": "είναι το προεξοφλητικό επιτόκιο που θα εφαρμοστεί στις ταμειακές ροές!είναι μια σειρά ταμειακών ροών που αντιστοιχεί σε ένα χρονοδιάγραμμα πληρωμών στις ημερομηνίες!είναι ένα χρονοδιάγραμμα ημερομηνιών πληρωμής που αντιστοιχεί στις πληρωμές ταμειακής ροής"}, "YIELD": {"a": "(διακαν<PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; επιτόκιο; τιμή; εξαργύρωση; συχνότητα; [βάση])", "d": "Αποδίδει την απόδοση ενός χρεογράφου που δίνει περιοδικό τόκο", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το ετήσιο επιτόκιο των τοκομεριδίων του χρεογράφου!είναι η τιμή του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι η τιμή εξαργύρωσης του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι ο αριθμός των πληρωμών των τοκομεριδίων ανά έτος!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "YIELDDISC": {"a": "(διακαν<PERSON><PERSON><PERSON>σμ<PERSON><PERSON>; λήξη; τιμή; εξαργύρωση; [βάση])", "d": "Αποδίδει την ετήσια απόδοση ενός προεξοφλημένου χρεογράφου. Για παράδειγμα, έ<PERSON><PERSON> <PERSON>ντοκο γραμμάτιο", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η τιμή του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι η τιμή εξαργύρωσης του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "YIELDMAT": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; έκδοση; επιτόκιο; παρούσα_αξία; [βάση])", "d": "Αποδίδει την ετήσια απόδοση ενός χρεογράφου που δίνει τόκο κατά τη λήξη", "ad": "είναι η ημερομηνία διακανονισμού του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία λήξης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι η ημερομηνία έκδοσης του χρεογράφου, εκφρασμένη με αύξοντα αριθμό ημερομηνίας!είναι το επιτόκιο του χρεογράφου κατά την ημερομηνία έκδοσης!είναι η τιμή του χρεογράφου ανά 100 € ονομαστικής αξίας!είναι ο τύπος της βάσης καταμέτρησης ημερών που θα χρησιμοποιηθεί"}, "ABS": {"a": "(αριθμός)", "d": "Αποδίδει την απόλυτη τιμή ενός αριθμού, ενός αριθμού χωρίς πρόσημο", "ad": "είναι ο πραγματικ<PERSON>ς αριθμός του οποίου θέλετε την απόλυτη τιμή"}, "ACOS": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο συνημίτονου ενός αριθμού σε ακτίνια από το 0 έως το π. Το τόξο συνημίτονου είναι η γωνία της οποίας το συνημίτονο είναι αριθμός", "ad": "είναι το συνημίτονο της γωνίας που θέλετε και πρέπει να είναι μεταξύ -1 και 1"}, "ACOSH": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο υπερβολικού συνημίτονου ενός αριθμού", "ad": "είναι οποιοσδήποτε πραγματικός αριθμός ίσος ή μεγαλύτερος του 1"}, "ACOT": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο συνεφαπτομένης ενός αριθμού, σε ακτίνια στην περιοχή 0 έως π.", "ad": "είναι η συνεφαπτομένη της γωνίας που θέλετε"}, "ACOTH": {"a": "(αριθμός)", "d": "Αποδίδει την αντίστροφη υπερβολική συνεφαπτομένη ενός αριθμού", "ad": "είναι η υπερβολική συνεφαπτομένη της γωνίας που θέλετε"}, "AGGREGATE": {"a": "(αριθμός_συνάρτησης; επιλογές; αναφορά1; ...)", "d": "Αποδίδει ένα συγκεντρωτικ<PERSON> αποτέλεσμα σε μια λίστα ή βάση δεδομένων", "ad": "είναι ο αριθμός από 1 έως 19 που καθορίζει τη συνάρτηση σύνοψης για το συγκεντρωτικό αποτέλεσμα.!είναι ο αριθμός 0 έως 7 που καθορίζει τις τιμές που θα αγνοηθούν για το συγκεντρωτικό αποτέλεσμα!είναι ο πίνακας ή η περιοχή αριθμητικών δεδομένων, με τα οποία υπολογίζεται το συγκεντρωτικό αποτέλεσμα!υποδεικνύει τη θέση στον πίνακα; είναι η k μεγαλύτερη, η k μικρότερη, το k εκατοστημόριο τιμών ή το k τεταρτημόριο τιμών.!είναι ο αριθμός από 1 έως 19 που καθορίζει τη συνάρτηση σύνοψης για το συγκεντρωτικό αποτέλεσμα.!είναι ο αριθμός 0 έως 7 που καθορίζει τις τιμές που θα αγνοηθούν για το συγκεντρωτικό αποτέλεσμα!είναι 1 έως 253 περιοχές ή αναφορές, για τις οποίες θέλετε το συγκεντρωτικό αποτέλεσμα"}, "ARABIC": {"a": "(κείμενο)", "d": "Μετατρέπει ένα ρωμαϊκό νούμερο σε αραβικά ψηφία", "ad": "είναι το ρωμαϊκό νούμερο που θέλετε να μετατρέψετε"}, "ASC": {"a": "(κείμενο)", "d": "Σε γλώσσες με σύνολα χαρακτήρων διπλού byte (Double-Byte Character Set - DBCS), μετατρέπει τους χαρακτήρες πλήρους πλάτους (δύο byte) σε χαρακτήρες μισού πλάτους (ενός byte)", "ad": "Το κείμενο ή η αναφορά σε ένα κελί που περιέχει το κείμενο που θέλετε να αλλάξετε"}, "ASIN": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο ημίτονου ενός αριθμού σε ακτίνια, με πεδίο τιμών από το -π/2 έως το π/2", "ad": "είναι το ημίτονο της γωνίας που θέλετε και πρέπει να είναι από -1 έως 1"}, "ASINH": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο υπερβολικού ημίτονου ενός αριθμού", "ad": "είναι οποιοσδήποτε πραγματικός αριθμός ίσος ή μεγαλύτερος του 1"}, "ATAN": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο εφαπτομένης ενός αριθμού σε ακτίνια, με πεδίο τιμών από το -π/2 έως το π/2", "ad": "είναι η εφαπτομένη της γωνίας που θέλετε"}, "ATAN2": {"a": "(αριθμός_x; αριθμός_y)", "d": "Αποδίδει το τόξο εφαπτομένης των καθορισμένων συντεταγμένων x και y σε ακτίνια, με πεδίο τιμών από -π έως π, εκτός του -π", "ad": "είναι η συντεταγμένη x του σημείου!είναι η συντεταγμένη y του σημείου"}, "ATANH": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο υπερβολικής εφαπτομένης ενός αριθμού", "ad": "είναι οποιοσδήποτε πραγματικός αριθμός μεταξύ 1 και -1, εκτός των τιμών -1 και 1"}, "BASE": {"a": "(αριθμός; βάση; [ελάχιστο_μήκος])", "d": "Μετατρέ<PERSON><PERSON><PERSON> έναν αριθμό σε αναπαράσταση κειμένου με την δεδομένη βάση", "ad": "είναι ο αριθμός που θέλετε να μετατρέψετε!είναι η βάση στην οποία θέλετε να μετατρέψετε τον αριθμό!είναι το ελάχιστο μήκος της αποδιδόμενης συμβολοσειράς.  Ε<PERSON>ν παραληφθεί, δεν προστίθενται μηδενικά μπροστά από τον αριθμό του αποτελέσματος"}, "CEILING": {"a": "(αριθμός; σημαντικότητα)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν αριθμό προς τα πάνω, στον πλησιέστερο ακέραιο ή στο πλησιέστερο σημαντικό πολλαπλάσιο", "ad": "είναι η τιμή που θέλετε να στρογγυλοποιήσετε!είναι το πολλαπλάσιο στο οποίο θέλετε να στρογγυλοποιήσετε"}, "CEILING.MATH": {"a": "(αριθμός; [σημαντικότητα]; [τρόπος])", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν αριθμό προς τα επάνω, στον πλησιέστερο ακέραιο ή στο πλησιέστερο πολλαπλάσιο των σημαντικών ψηφίων", "ad": "είναι η τιμή που θέλετε να στρογγυλοποιήσετε!είναι το πολλαπλάσιο στο οποίο θέλετε να γίνει η στρογγυλοποίηση!όταν δίνεται και είναι διαφορετικός από μηδέν, η συνάντηση αυτή στρογγυλοποιεί αντίθετα από το μηδέν"}, "CEILING.PRECISE": {"a": "(αριθμός; [σημαντικότητα])", "d": "Επιστρέφει έναν αριθμό που είναι στρογγυλοποιημένος προς τα επάνω στον πλησιέστερο ακέραιο ή στο πλησιέστερο σημαντικό πολλαπλάσιο", "ad": "είναι η τιμή που θέλετε να στρογγυλοποιήσετε!είναι το πολλαπλάσιο στο οποίο θέλετε να στρογγυλοποιήσετε"}, "COMBIN": {"a": "(αριθμός; επιλεγμένος_αριθμός)", "d": "Αποδίδει τον αριθμό συνδυασμών για δεδομένο πλήθος στοιχείων", "ad": "είναι το συνολικό πλήθος στοιχείων!είναι το πλήθος των στοιχείων σε κάθε συνδυασμό"}, "COMBINA": {"a": "(αριθμός; επιλεγμένος_αριθμός)", "d": "Αποδίδει τον αριθμό συνδυασμών με επαναλήψεις για δεδομένο πλήθος στοιχείων", "ad": "είναι το συνολικό πλήθος στοιχείων!είναι το πλήθος των στοιχείων σε κάθε συνδυασμό"}, "COS": {"a": "(αριθμός)", "d": "Αποδίδει το συνημίτονο μιας γωνίας", "ad": "είναι η γωνία σε ακτίνια για την οποία αναζητάτε το συνημίτονο"}, "COSH": {"a": "(αριθμός)", "d": "Αποδίδει το υπερβολικό συνημίτονο ενός αριθμού", "ad": "είναι οποιοσδήποτε πραγματικός αριθμός"}, "COT": {"a": "(αριθμός)", "d": "Αποδίδει την συνεφαπτομένη μιας γωνίας", "ad": "είναι η γωνία σε ακτίνια της οποίας θέλετε τη συνεφαπτομένη"}, "COTH": {"a": "(αριθμός)", "d": "Αποδίδει την υπερβολική συνεφαπτομένη ενός αριθμού", "ad": "είναι η γωνία σε ακτίνια της οποίας θέλετε την υπερβολική συνεφαπτομένη"}, "CSC": {"a": "(αριθμός)", "d": "Αποδίδει τη συντέμνουσα μιας γωνίας", "ad": "είναι η γωνία σε ακτίνια της οποίας θέλετε τη συντέμνουσα"}, "CSCH": {"a": "(αριθμός)", "d": "Αποδίδει την υπερβολική συντέμνουσα μιας γωνίας", "ad": "είναι η γωνία σε ακτίνια της οποίας θέλετε την υπερβολική συντέμνουσα"}, "DECIMAL": {"a": "(αριθμός; βάση)", "d": "Μετατρέπει μια αναπαράσταση κειμένου ενός αριθμού δεδομένης βάσης σε δεκαδικό αριθμό", "ad": "είναι ο αριθμός που θέλετε να μετατρέψετε!είναι η βάση του αριθμού που μετατρέπετε"}, "DEGREES": {"a": "(γωνία)", "d": "Μετατρέπει τα ακτίνια σε μοίρες", "ad": "είναι η γωνία σε ακτίνια που θέλετε να μετατρέψετε"}, "ECMA.CEILING": {"a": "(αριθμός; σημαντικότητα)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν αριθμό προς τα πάνω, στον πλησιέστερο ακέραιο ή στο πλησιέστερο σημαντικό πολλαπλάσιο", "ad": "είναι η τιμή που θέλετε να στρογγυλοποιήσετε!είναι το πολλαπλάσιο στο οποίο θέλετε να στρογγυλοποιήσετε"}, "EVEN": {"a": "(αριθμός)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν θετικό αριθμό προς τα επάνω και έναν αρνητικό αριθμό προς τα κάτω στον πλησιέστερο άρτιο ακέραιο", "ad": "είναι η τιμή που θα στρογγυλοποιηθεί"}, "EXP": {"a": "(αριθμός)", "d": "Αποδίδει το e υψωμένο στη δύναμη του καθορισμένου αριθμού", "ad": "είναι ο εκθέτης που εφαρμόζεται στη βάση e. Η σταθερά e είναι ίση με το 2,71828182845904, που αποτελεί τη βάση του φυσικού λογαρίθμου"}, "FACT": {"a": "(αριθμός)", "d": "Αποδίδει το παραγοντικ<PERSON> ενός αριθμού που ισοδυναμεί με 1*2*3*...*αριθμός", "ad": "είναι ο μη αρνητικ<PERSON>ς αριθμός του οποίου θέλετε το παραγοντικό"}, "FACTDOUBLE": {"a": "(αριθμός)", "d": "Αποδίδει το διπλό παραγοντικ<PERSON> ενός αριθμού", "ad": "είναι η τιμή για την οποία θα επιστραφεί το διπλό παραγοντικό"}, "FLOOR": {"a": "(αριθμός; σημαντικότητα)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν αριθμό προς τα κάτω, προς το πλησιέστερο σημαντικό πολλαπλάσιο", "ad": "είναι η αριθμητική τιμή που θέλετε να στρογγυλοποιήσετε!είναι το πολλαπλάσιο στο οποίο θέλετε να γίνει στρογγυλοποίηση. Οι παράμετροι \"αριθμός\" και \"σημαντικότητα\" πρέπει να είναι και οι δύο θετικές ή αρνητικές"}, "FLOOR.PRECISE": {"a": "(αριθμός; [σημαντικότητα] )", "d": "Επιστρέφει έναν αριθμό που είναι στρογγυλοποιημένος προς τα κάτω στον πλησιέστερο ακέραιο ή στο πλησιέστερο σημαντικό πολλαπλάσιο", "ad": "είναι η τιμή που θέλετε να στρογγυλοποιήσετε!είναι το πολλαπλάσιο στο οποίο θέλετε να στρογγυλοποιήσετε"}, "FLOOR.MATH": {"a": "(αριθμός; [σημαντικότητα]; [τρόπος])", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν αριθμό προς τα κάτω, στον πλησιέστερο ακέραιο ή στο πλησιέστερο πολλαπλάσιο των σημαντικών ψηφίων", "ad": "είναι η τιμή που θέλετε να στρογγυλοποιήσετε!είναι το πολλαπλάσιο στο οποίο θέλετε να γίνει η στρογγυλοποίηση!όταν δίνεται και είναι διαφορετικός από μηδέν, η συνάντηση αυτή στρογγυλοποιεί προς το μηδέν"}, "GCD": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Επιστρέφει τον μέγιστο κοινό διαιρέτη", "ad": "είναι 1 έως 255 τιμές"}, "INT": {"a": "(αριθμός)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> προς τα κάτω έναν αριθμό στον πλησιέστερο ακέραιο", "ad": "είναι ο πραγματικ<PERSON>ς αριθμός που θέλετε να στρογγυλοποιήσετε προς τα κάτω σε ακέραιο"}, "ISO.CEILING": {"a": "(αριθμός; [σημαντικότητα])", "d": "Επιστρέφει έναν αριθμό που είναι στρογγυλοποιημένος προς τα επάνω στον πλησιέστερο ακέραιο ή στο πλησιέστερο σημαντικό πολλαπλάσιο. Ανεξάρτητα από το πρόσημο του αριθμού, ο αριθμός στρογγυλοποιείται προς τα επάνω. Εάν το όρισμα αριθμός ή το όρισμα σημαντικότητα είναι μηδέν, η συνάρτηση επιστρέφει την τιμή μηδέν.", "ad": "είναι η τιμή που θέλετε να στρογγυλοποιήσετε!είναι το πολλαπλάσιο στο οποίο θέλετε να στρογγυλοποιήσετε"}, "LCM": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Επιστρέφει τον ελάχιστο κοινό πολλαπλάσιο", "ad": "είναι 1 έως 255 τιμές για τις οποίες θέλετε τον ελάχιστο κοινό πολλαπλάσιο"}, "LN": {"a": "(αριθμός)", "d": "Αποδίδει το φυσικό λογάριθμο ενός αριθμού", "ad": "είναι ο θετικός πραγματικός αριθμός του οποίου θέλετε τον φυσικό λογάριθμο"}, "LOG": {"a": "(αριθμός; [βάση])", "d": "Αποδίδει το λογάριθμο ενός αριθμού με την καθορισμένη βάση", "ad": "είναι ο θετικός πραγματικός αριθμός του οποίου θέλετε το λογάριθμο!είναι η βάση του λογαρίθμου. Παίρνει την τιμή 10 αν παραλειφθεί"}, "LOG10": {"a": "(αριθμός)", "d": "Αποδίδει τον δεκαδικό λογάριθμο ενός αριθμού", "ad": "είναι ο θετικός πραγματικός αριθμός του οποίου θέλετε τον δεκαδικό λογάριθμο"}, "MDETERM": {"a": "(πίνακας)", "d": "Αποδίδει την ορίζουσα ενός πίνακα", "ad": "είναι ένας αριθμητικός πίνακας με ίσο αριθμό γραμμών και στηλών, μια περιοχή κελιών ή μια σταθερά πίνακα"}, "MINVERSE": {"a": "(πίνακας)", "d": "Αποδίδει τον ανάστροφο ενός επιλεγμένου πίνακα", "ad": "είναι ένας αριθμητικός πίνακας με ίσο αριθμό γραμμών και στηλών, μια περιοχή κελιών ή μια σταθερά πίνακα"}, "MMULT": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει το γινόμενο δύο πινάκων, έν<PERSON>ν πίνακα που διαθέτει το ίδιο πλήθος γραμμών με τον πίνακα 1 και τον ίδιο αριθμό στηλών με τον πίνακα 2", "ad": "είναι ο πρώτος πίνακας αριθμών που θέλετε να πολλαπλασιάσετε και πρέπει να διαθέτει ένα πλήθος στηλών ίσο με το πλήθος γραμμών του πίνακα 2"}, "MOD": {"a": "(αριθμός; διαιρέτης)", "d": "Αποδίδει το υπόλοιπο της διαίρεσης", "ad": "είναι ο αριθμός του οποίου θέλετε να βρείτε το υπόλοιπο, αφού ολοκληρωθεί η διαίρεση!είναι ο αριθμός με τον οποίο θέλετε να διαιρέσετε τον αριθμό"}, "MROUND": {"a": "(αριθμός; πολλαπλάσιο)", "d": "Αποδίδει έναν αριθμό στρογγυλοποιημένο στο επιθυμητό πολλαπλάσιο", "ad": "είναι η τιμή που θα στρογγυλοποιηθεί!είναι το πολλαπλάσιο στο οποίο θέλετε να στρογγυλοποιήσετε τον αριθμό"}, "MULTINOMIAL": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Επιστρέφει το πολυώνυμο ενός συνόλου αριθμών", "ad": "είναι 1 έως 255 τιμές για τις οποίες θέλετε το πολυώνυμο"}, "MUNIT": {"a": "(διάσταση)", "d": "Αποδίδει το μοναδι<PERSON><PERSON><PERSON> πίνακα για την καθορισμένη διάσταση", "ad": "είν<PERSON><PERSON> έν<PERSON>ς ακέρα<PERSON>ος αριθμός που καθορίζει τη διάσταση του μοναδιαίου πίνακα που θέλετε να πάρετε"}, "ODD": {"a": "(αριθμός)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν θετικό αριθμό προς τα επάνω και έναν αρνητικό αριθμό προς τα κάτω, στον πλησιέστερο περιττό ακέραιο", "ad": "είναι η τιμή που θα στρογγυλοποιηθεί"}, "PI": {"a": "()", "d": "Αποδίδει την τιμή του π, 3,14159265358979, με ακρίβεια 15 ψηφίων", "ad": ""}, "POWER": {"a": "(αριθμός; δύναμη)", "d": "Αποδίδει το αποτέλεσμα ενός αριθμού υψωμένου σε δύναμη", "ad": "είναι ο αριθμός βάσης, ο<PERSON><PERSON><PERSON><PERSON><PERSON>δήποτε πραγματικός αριθμός!είναι ο εκθέτης, στον οποίο θα υψωθεί ο αριθμός της βάσης"}, "PRODUCT": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει το γινόμενο όλων των αριθμών που έχουν εισαχθεί ως ορίσματα", "ad": "είναι 1 έως 255 αριθμοί, λο<PERSON><PERSON><PERSON><PERSON>ς τιμές ή αριθμοί με μορφή κειμένου, τους οποίους θέλετε να πολλαπλασιάσετε"}, "QUOTIENT": {"a": "(αριθμητής; παρονομαστής)", "d": "Αποδίδει το ακέραιο τμήμα μιας διαίρεσης", "ad": "είναι ο διαιρετέος!είναι ο διαιρέτης"}, "RADIANS": {"a": "(γωνία)", "d": "Μετατρέπει τις μοίρες σε ακτίνια", "ad": "είναι μια γωνία σε μοίρες, την οποία θέλετε να μετατρέψετε"}, "RAND": {"a": "()", "d": "Αποδίδει έναν τυχαίο αριθμό μεγαλύτερο ή ίσο του 0 και μικρότερο του 1, ομοιόμορφα κατανεμημένο (αλλά<PERSON><PERSON><PERSON> κατά την επανάληψη του υπολογισμού)", "ad": ""}, "RANDARRAY": {"a": "([γραμμές]; [στήλες]; [ελάχ.]; [μέγ.]; [ακέραιος])", "d": "Επιστρέφει έναν πίνακα τυχαίων αριθμών", "ad": "ο αριθμός των γραμμών στον πίνακα που επιστράφηκε!ο αριθμός των στηλών του πίνακα που επιστράφηκε!ο ελάχιστος αριθμός που θα θέλατε να επιστρέψει!ο μέγιστος αριθμός που θα θέλατε να επιστρέψει!επιστροφή μιας ακέραιης ή δεκαδικής τιμής. ΑΛΗΘΕΣ για έναν ακέραιο, ΨΕΥΔΕΣ για έναν δεκαδικό αριθμό"}, "RANDBETWEEN": {"a": "(μικρότερος; μεγαλύτερος)", "d": "Αποδίδει έναν τυχαίο αριθμό μεταξύ των αριθμών που καθορίζετε", "ad": "είναι ο μικρότερος ακέραιος που θα επιστρέψει η RANDBETWEEN!είναι ο μεγαλύτερος ακέραιος που θα επιστρέψει η RANDBETWEEN"}, "ROMAN": {"a": "(αριθμός; [φόρμα])", "d": "Μετατρέπει ένα αραβικό αριθμητικό ψηφίο σε λατινικό, ως κείμενο", "ad": "είναι το αραβικό αριθμητικό ψηφίο που θέλετε να μετατρέψετε!είναι ο αριθμός ο οποίος καθορίζει τον τύπο του λατινικού αριθμητικού ψηφίου που θέλετε."}, "ROUND": {"a": "(αριθμός; αριθμός_ψηφίων)", "d": "Στρογγυλοπ<PERSON>ι<PERSON><PERSON> έναν αριθμό σε καθορισμένο αριθμό ψηφίων", "ad": "είναι ο αριθμός που θέλετε να στρογγυλοποιήσετε!είναι ο αριθμός των ψηφίων στα οποία θέλετε να στρογγυλοποιήσετε τον αριθμό. Αρνητικός αριθμός στρογγυλοποιεί προς τα αριστερά της υποδιαστολής, το μηδέν στον κοντινότερο ακέραιο"}, "ROUNDDOWN": {"a": "(αριθμός; ψηφία_αριθμού)", "d": "Στρογγυλοποιεί προς τα κάτω έναν αριθμό, προς το μηδέν", "ad": "είναι οποιοσδήποτε πραγματικός αριθμός που θέλετε να στρογγυλοποιήσετε προς τα κάτω!είναι ο αριθμός των ψηφίων στα οποία θέλετε να εφαρμοστεί η στρογγυλοποίηση. Η αρνητική τιμή στρογγυλοποιεί προς τα αριστερά της υποδιαστολής, η μηδενική τιμή ή η παράλειψη προς τον κοντινότερο ακέραιο"}, "ROUNDUP": {"a": "(αριθμός; ψηφία_αριθμού)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν αριθμό προς τα επάνω", "ad": "είναι οποιοσδήποτε πραγματικός αριθμός που θέλετε να στρογγυλοποιήσετε προς τα επάνω!είναι ο αριθμός των ψηφίων στα οποία θέλετε να εφαρμοστεί η στρογγυλοποίηση. Η αρνητική τιμή στρογγυλοποιεί προς τα αριστερά της υποδιαστολής, η μηδενική τιμή ή η παράλειψη προς τον κοντινότερο ακέραιο"}, "SEC": {"a": "(αριθμός)", "d": "Αποδίδει την τέμνουσα μιας γωνίας", "ad": "είναι η γωνία σε ακτίνια της οποίας θέλετε την τέμνουσα"}, "SECH": {"a": "(αριθμός)", "d": "Αποδίδει την υπερβολική τέμνουσα μιας γωνίας", "ad": "είναι η γωνία σε ακτίνια της οποίας θέλετε την υπερβολική τέμνουσα"}, "SERIESSUM": {"a": "(x; n; m; συντελεστές)", "d": "Αποδίδει το άθροισμα μιας δυναμοσειράς βάσει του τύπου", "ad": "είναι η τιμή εισόδου στη δυναμοσειρά!είναι η αρχική δύναμη στην οποία θέλετε να υψώσετε το x!είναι το βήμα με το οποίο θα αυξάνεται το n για κάθε όρο της σειράς!είναι μια ομάδα συντελεστών με τους οποίους πολλαπλασιάζεται κάθε διαδοχική δύναμη του x"}, "SIGN": {"a": "(αριθμός)", "d": "Αποδίδει το πρόσημο ενός αριθμού. Παίρνει την τιμή 1 αν ο αριθμός είναι θετικός, την τιμή 0 αν ο αριθμός είναι μηδέν ή την τιμή -1 αν ο αριθμός είναι αρνητικός", "ad": "είναι οποιοσδήποτε πραγματικός αριθμός"}, "SIN": {"a": "(αριθμός)", "d": "Αποδίδει το ημίτονο μιας γωνίας", "ad": "είναι η γωνία σε ακτίνια της οποίας θέλετε το ημίτονο. Μοίρες*π/180 = ακτίνια"}, "SINH": {"a": "(αριθμός)", "d": "Αποδίδει το υπερβολικ<PERSON> ημίτονο ενός αριθμού", "ad": "είναι οποιοσδήποτε πραγματικός αριθμός"}, "SQRT": {"a": "(αριθμός)", "d": "Αποδίδει την τετραγωνική ρίζα ενός αριθμού", "ad": "είναι ο αριθμός του οποίου θέλετε την τετραγωνική ρίζα"}, "SQRTPI": {"a": "(αριθμός)", "d": "Αποδίδει την τετραγωνική ρίζα του (αριθμός * π)", "ad": "είναι ο αριθμός με τον οποίο πολλαπλασιάζεται το π"}, "SUBTOTAL": {"a": "(αριθμός_αναφοράς; αναφορά1; ...)", "d": "Αποδίδει το μερικό άθροισμα μέσα σε μια λίστα ή μια βάση δεδομένων", "ad": "είναι αριθμός από το 1 έως το 11 που καθορίζει ποια συνάρτηση μερικού αθροίσματος θα χρησιμοποιηθεί για τον υπολογισμό του μερικού αθροίσματος.!είναι 1 έως 254 περιοχές ή αναφορές, για τις οποίες θέλετε να υπολογιστεί το μερικό άθροισμα"}, "SUM": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Προσθέτει όλους τους αριθμούς σε μια περιοχή κελιών", "ad": "είναι 1 έως 255 αριθμοί τους οποίους θέλετε να αθροίσετε. Οι λογικές τιμές και το κείμενο παραβλέπονται, ακόμα και αν εισαχθούν ως ορίσματα"}, "SUMIF": {"a": "(περιοχή; κριτήρια; [περιοχή_αθροίσματος])", "d": "Προσθέτει τα κελιά που καθορίζονται από μια συνθήκη ή από δεδομένα κριτήρια", "ad": "είναι η περιοχή κελιών που θέλετε να εκτιμηθούν!είναι η συνθήκη ή τα κριτήρια με μορφή αριθμού, παρ<PERSON><PERSON>τ<PERSON><PERSON>ης ή κειμένου που ορίζουν ποια κελιά θα προστεθούν!είναι τα κελιά που θα αθροιστούν. Εάν παραλειφθούν, χρησιμοποιούνται τα κελιά της περιοχής"}, "SUMIFS": {"a": "(περιοχή_αθροίσματος; περιοχή_κριτηρίων; κριτήρια; ...)", "d": "Προσθέτει τα κελιά που καθορίζονται από ένα δεδομένο σύνολο συνθηκών ή κριτηρίων", "ad": "είναι τα κελιά που θα προστεθούν.!είναι η περιοχή κελιών που θέλετε να αναλυθούν για τη συγκεκριμένη συνθήκη!είναι η συνθήκη ή το κριτήριο με τη μορφή αριθμού, παράστασης ή κειμένου που ορίζει ποια κελιά θα προστεθούν"}, "SUMPRODUCT": {"a": "(πίνακας1; [πίνακας2]; [πίνακας3]; ...)", "d": "Αποδίδει το άθροισμα των γινομένων των αντίστοιχων αριθμητικών στοιχείων πίνακα ή περιοχής", "ad": "είναι 2 έως 255 πίνακες των οποίων τα στοιχεία θέλετε να πολλαπλασιάσετε και κατόπιν να προσθέσετε. Όλοι οι πίνακες πρέπει να έχουν τις ίδιες διαστάσεις"}, "SUMSQ": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει το άθροισμα των τετραγώνων των ορισμάτων. Τα ορίσματα μπορεί να είναι αριθμοί, π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ον<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> αναφορές σε κελιά που περιέχουν αριθμούς.", "ad": "είναι 1 έως 255 αριθμο<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ή αναφορές σε πίνακες των οποίων θέλετε να υπολογίσετε το άθροισμα των τετραγώνων"}, "SUMX2MY2": {"a": "(πίνακας_x; πίνακας_y)", "d": "Αποδίδει το άθροισμα των διαφορών των τετραγώνων για τις αντίστοιχες τιμές δύο περιοχών ή πινάκων", "ad": "είναι ο πρώτος πίνακας ή περιοχή αριθμών και μπορεί να είναι αριθμός ή όνομα, καθώς και πίνακας ή αναφορά που περιέχει αριθμούς!είναι ο δεύτερος πίνακας ή περιοχή αριθμών και μπορεί να είναι αριθμός ή όνομα, καθώς και πίνακας ή αναφορά που περιέχει αριθμούς"}, "SUMX2PY2": {"a": "(πίνακας_x; πίνακας_y)", "d": "Αποδίδει το σύνολο των αθροισμάτων των τετραγώνων για τις αντίστοιχες τιμές δύο περιοχών ή πινάκων", "ad": "είναι ο πρώτος πίνακας ή περιοχή αριθμών και μπορεί να είναι αριθμός ή όνομα, καθώς και πίνακας ή αναφορά που περιέχει αριθμούς!είναι ο δεύτερος πίνακας ή περιοχή αριθμών και μπορεί να είναι αριθμός ή όνομα, καθώς και πίνακας ή αναφορά που περιέχει αριθμούς"}, "SUMXMY2": {"a": "(πίνακας_x; πίνακας_y)", "d": "Αποδίδει το άθροισμα των τετραγώνων των διαφορών για τις αντίστοιχες τιμές δύο περιοχών ή πινάκων", "ad": "είναι ο πρώτος πίνακας ή περιοχή τιμών και μπορεί να είναι ένας αριθμός ή όνομα, καθώς και ένας πίνακας ή μια αναφορά που περιέχει αριθμούς!είναι ο δεύτερος πίνακας ή περιοχή τιμών και μπορεί να είναι αριθμός ή όνομα, καθώς και ένας πίνακας ή μια αναφορά που περιέχει αριθμούς"}, "TAN": {"a": "(αριθμός)", "d": "Αποδίδει την εφαπτομένη μιας γωνίας", "ad": "είναι η γωνία σε ακτίνια της οποίας θέλετε την εφαπτομένη. Μοίρες*π/180 = ακτίνια"}, "TANH": {"a": "(αριθμός)", "d": "Αποδίδει την υπερβολική εφαπτομένη ενός αριθμού", "ad": "είναι οποιοσδήποτε πραγματικός αριθμός"}, "TRUNC": {"a": "(αριθμός; [ψηφία_αριθμού])", "d": "Αποκοπή του δεκαδικού ή κλασματικού μέρους ενός αριθμού", "ad": "είναι ο αριθμός του οποίου θέλετε να αποκοπεί το δεκαδικό μέρος!είναι ένας αριθμός που καθορίζει την ακρίβεια της αποκοπής. Παίρνει την τιμή 0 (μηδέν) αν παραλειφθεί"}, "ADDRESS": {"a": "(αριθμός_γραμμής; αριθμός_στήλης; [απόλυτος_αριθμός]; [a1]; [κείμενο_φύλλου])", "d": "Δημιουργεί μια αναφορά κελιού με μορφή κειμένου, για καθορισμένους αριθμούς γραμμών και στηλών", "ad": "είναι ο αριθμός γραμμής που θα χρησιμοποιηθεί στην αναφορά κελιού: αριθμός_γραμμής =1 για τη γραμμή 1!είναι ο αριθμός στήλης που θα χρησιμοποιηθεί στην αναφορά κελιών. Για παράδειγμα, αριθμός_στήλης =4 για τη στήλη D.!καθορίζει τον τύπο αναφοράς: απόλυτη =1, απόλυτη γραμμή/σχετική στήλη = 2, σχετική γραμμή/απόλυτη στήλη = 3, σχετική = 4 !είναι μια λογική τιμή που καθορίζει το στυλ αναφοράς: στυλ A1 = 1 ή TRUE, στυλ R1C1 = 0 ή FALSE!είναι κείμενο που καθορίζει το όνομα του φύλλου εργασίας που θα χρησιμοποιηθεί ως εξωτερική αναφορά"}, "CHOOSE": {"a": "(αριθμός_δείκτη; τιμή1; [τιμή2]; ...)", "d": "Επιλέγει μια τιμή ή μια ενέργεια προς εκτέλεση από λίστα τιμών, βάσ<PERSON>ι ενός αριθμού ευρετηρίου", "ad": "καθορίζει ποιο όρισμα τιμής έχει επιλεγεί. Η παράμετρος αριθμός_δείκτη θα πρέπει είτε να παίρνει τιμές από 1 έως 254 είτε να είναι ένας τύπος ή μια αναφορά σε έναν αριθμό από 1 έως 254!είναι 1 έως 254 αριθμοί, αναφ<PERSON><PERSON><PERSON><PERSON> κελιών, καθορισμένα ονόματα, τύποι ή ορίσματα κειμένου, από τα οποία επιλέγει η συνάρτηση CHOOSE"}, "COLUMN": {"a": "([αναφορά])", "d": "Αποδίδει τον αριθμό στήλης μιας αναφοράς", "ad": "είναι το κελί ή η περιοχή συνεχόμενων κελιών για τα οποία θέλετε τον αριθμό στήλης. Ε<PERSON><PERSON> παραλειφθεί, χρησιμοποιείται το κελί που περιέχει τη συνάρτηση COLUMN"}, "COLUMNS": {"a": "(πίνακας)", "d": "Αποδίδει τον αριθμό στηλών που περιέχονται σε έναν πίνακα ή μια αναφορά", "ad": "είναι πίνακα<PERSON>, τ<PERSON><PERSON><PERSON> πίνακα ή αναφορά σε περιοχή κελιών, για τα οποία αναζητάτε το πλήθος των στηλών"}, "FORMULATEXT": {"a": "(αναφορά)", "d": "Αποδίδει έναν τύπο ως συμβολοσειρά", "ad": "είναι μια αναφορά σε έναν τύπο"}, "HLOOKUP": {"a": "(τιμή_αναζήτησης; πίν<PERSON><PERSON><PERSON><PERSON>; αριθμός_δείκτη_γραμμής; [περιοχή_αναζήτησης])", "d": "Αναζητά μια τιμή στην πρώτη γραμμή ενός πίνακα ή μιας περιοχής τιμών και επιστρέφει την τιμή στην ίδια στήλη από μια καθορισμένη γραμμή", "ad": "είναι η τιμή προς αναζήτηση στην πρώτη γραμμή του πίνακα και μπορεί να είναι τιμή, αναφ<PERSON><PERSON><PERSON> ή ακολουθία χαρακτήρων κειμένου!είναι ένας πίνακας κειμένου, αριθμών ή λογικών τιμών στον οποίο γίνεται αναζήτηση δεδομένων. Η παράμετρος πίνακας μπορεί να είναι μια αναφορά σε περιοχή ή όνομα περιοχής!είναι ο αριθμός γραμμής στην περιοχή πίνακας από την οποία θα επιστραφεί η τιμή που ικανοποιεί τα κριτήρια. Η πρώτη γραμμή τιμών στον πίνακα είναι η γραμμή 1!είναι μια λογική τιμή: όταν θέλετε να βρείτε το κατά προσέγγιση ταίριασμα στην πρώτη γραμμή (ταξινομημένη σε αύξουσα σειρά), η τιμή είναι TRUE ή παραλείπεται και όταν θέλετε να βρείτε ακριβές ταίριασμα, η τιμή είναι FALSE"}, "HYPERLINK": {"a": "(τοποθεσία_σύνδεσης; [φιλικό_όνομα])", "d": "Δημιουργεί μια συντόμευση ή μεταπήδηση, η οποία ανοίγει ένα έγγραφο που είναι αποθηκευμένο στον σκληρό σας δίσκο, σε ένα διακομιστή δικτύου ή στο Internet", "ad": "είναι το κείμενο που αντιστ<PERSON><PERSON><PERSON><PERSON><PERSON> στη διαδρομή και στο όνομα αρχείου του εγγράφου που θα ανοιχθεί, έν<PERSON><PERSON> σκληρός δίσκος, μια διεύθυνση UNC ή μια διαδρομή URL!είναι κείμενο ή αριθμός που εμφανίζεται στο κελί. Εάν παραλειφθεί, στο κελί εμφανίζεται το κείμενο του ορίσματος Θέση_σύνδεσης"}, "INDEX": {"a": "(πίνακας; αριθμός_γραμμής; [αριθμός_στήλης]!αναφορά; αριθμός_γραμμής; [αριθμός_στήλης]; [αριθμός_περιοχής])", "d": "Αποδίδει μια τιμή ή αναφορά του κελιού στην τομή μιας συγκεκριμένης γραμμής και στήλης, σε μια δεδομένη περιοχή", "ad": "είναι μια περιοχή κελιών ή μια σταθερά πίνακα.!επιλέγει τη γραμμή πίνακα ή αναφοράς από όπου θα επιστραφεί μια τιμή. Ε<PERSON>ν παραλειφθεί, απαιτείται εισαγωγή τιμής στην παράμετρο αριθμός_στήλης!επιλέγει τη στήλη πίνακα ή αναφοράς από όπου θα επιστραφεί μια τιμή. Ε<PERSON>ν παραλειφθεί, απαιτείται η εισαγωγή τιμής στην παράμετρο αριθμός_γραμμής !είναι αναφορά σε μία ή περισσότερες περιοχές κελιών!επιλέγει τη γραμμή πίνακα ή αναφοράς από όπου θα επιστραφεί μια τιμή. Εάν παραλειφθεί, απαιτείται η εισαγωγή τιμής στην παράμετρο αριθμός_στήλης!επιλέγει τη στήλη πίνακα ή αναφοράς από όπου θα επιστραφεί μια τιμή. Εάν παραλειφθεί, απαιτείται η εισαγωγή τιμής στην παράμετρο αριθμός_γραμμής!επιλέγει μια περιοχή αναφοράς από την οποία θα επιστραφεί μια τιμή. Η πρώτη περιοχή που εισάγεται ή επιλέγεται είναι η περιοχή 1, η δεύ"}, "INDIRECT": {"a": "(κείμενο_αναφοράς; [a1])", "d": "Αποδίδει την αναφορά που καθορίζεται από μια ακολουθία χαρακτήρων κειμένου", "ad": "είναι μια ανα<PERSON><PERSON><PERSON>ά σε κελί που περιέχει αναφορά στυλ Α1 ή R1C1, ένα όνομα ορισμένο ως αναφορά ή μια αναφορά σε κελί ως ακολουθία χαρακτήρων κειμένου!είναι μια λογική τιμή που καθορίζει τον τύπο της αναφοράς στο Αναφορά_κείμενο: στυλ R1C1 = FALSE, στυλ Α1 = TRUE ή παραλείπεται"}, "LOOKUP": {"a": "(τιμή_αναζήτησης; άνυσμα_αναζήτησης; [άνυσμα_αποτελέσματος]!τιμή_αναζήτησης; πίνακα<PERSON>)", "d": "Αναζητά μια τιμή από πίνακα ή από περιοχή που αποτελείται από μία μόνο γραμμή ή στήλη. Παρέχεται για λόγους αντίστροφης συμβατότητας", "ad": "είναι η τιμή την οποία η συνάρτηση LOOKUP θα αναζητήσει στην παράμετρο άνυσμα_αναζήτησης και μπορεί να είναι αριθμός, κ<PERSON><PERSON><PERSON><PERSON>ν<PERSON>, λογική τιμή ή όνομα ή αναφορά σε μια τιμή!είναι μια περιοχή που περιέχει μόνο μία γραμμή ή στήλη κειμένου, αριθμών ή λογικών τιμών, τοποθετημένων κατά αύξουσα σειρά!είναι μια περιοχή που περιέχει μόνο μία γραμμή ή στήλη, με μέγεθος ίσο με της παραμέτρου άνυσμα_αναζήτησης!είναι η τιμή που η συνάρτηση LOOKUP αναζητά σε έναν πίνακα και μπορεί να είναι αριθμός, κείμενο, λογική τιμή ή όνομα ή αναφορά σε μια τιμή!είναι μια περιοχή κελιών που περιέχουν κείμενο, αριθμούς ή λογικές τιμές και τα οποία θέλετε να συγκρίνετε με την παράμετρο τιμή_αναζήτησης"}, "MATCH": {"a": "(τιμή_αναζήτησης; πίνακ<PERSON><PERSON>_αναζήτησης; [τύπος_ταιριάσματος])", "d": "Αποδίδει τη σχετική θέση ενός στοιχείου σε έναν πίνακα που ταιριάζει με μια καθορισμένη τιμή σε μια καθορισμένη σειρά", "ad": "είναι η τιμή που χρησιμοποιείτε, για να βρείτε την τιμή που θέλετε στον πίνακα, ένα<PERSON> αριθμός, κ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, μια λογική τιμή ή μια αναφορά σε κάποιο από τα παραπάνω!είναι μια συνεχόμενη περιοχή κελιών που περιέχει πιθανές τιμές αναζήτησης, ένας πίνακας τιμών ή μια αναφορά σε πίνακα!είναι ο αριθμός 1, 0 ή -1, ο οποίος δείχνει ποια τιμή θα επιστραφεί."}, "OFFSET": {"a": "(αναφορά; γραμμές; στήλες; [ύψος]; [πλάτος])", "d": "Αποδίδει μια αναφορά σε μια περιοχή που αποτελε<PERSON> έναν καθορισμένο αριθμό γραμμών και στηλών από μια δεδομένη αναφορά", "ad": "είναι η αναφορά στην οποία θέλετε να βασιστεί η μετατόπιση, μια αναφορά σε κελί ή μια περιοχή συνεχόμενων κελιών!είναι το πλήθος των γραμμών, προς τα επάνω ή κάτω, στις οποίες θέλετε να αναφέρεται το άνω αριστερά κελί του αποτελέσματος!είναι το πλήθος των στηλών, προς τα αριστερά ή δεξιά, στις οποίες θέλετε να αναφέρεται το άνω αριστερά κελί του αποτελέσματος!είναι το ύψος, σε αριθμό γραμμών, που θέλετε να έχει το αποτέλεσμα. <PERSON><PERSON><PERSON> παραλειφθεί, ε<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> με το ύψος της αναφοράς!είναι το πλάτος, σε αριθμό στηλών, που θέλετε να έχει το αποτέλεσμα. Εάν παραλειφθεί, είναι ίσο με το πλάτος της αναφοράς"}, "ROW": {"a": "([αναφορά])", "d": "Επιστρέφει τον αριθμό γραμμής μιας αναφοράς", "ad": "είναι το κελί ή η περιοχή κελιών των οποίων θέλετε τον αριθμό γραμμής. Ε<PERSON>ν παραλειφθεί, επιστρέφει το κελί που περιέχει τη συνάρτηση ROW"}, "ROWS": {"a": "(πίνακας)", "d": "Αποδίδει το πλήθος γραμμών σε αναφορά ή πίνακα", "ad": "είναι πίνακα<PERSON>, τ<PERSON><PERSON><PERSON> πίνακα ή αναφορά σε περιοχή κελιών, για τα οποία θέλετε το πλήθος των γραμμών"}, "TRANSPOSE": {"a": "(πίνακας)", "d": "Αποδίδει τον ανάστροφο ενός πίνακα-στήλης ή αντίστροφα", "ad": "είναι μια περιοχή κελιών σε φύλλο εργασίας ή ένας πίνακας τιμών τον οποίο θέλετε να αντιμεταθέσετε"}, "UNIQUE": {"a": "(πίνακας; [κατ<PERSON>_στήλη]; [ακριβώς_μία_φορά])", "d": "Επιστρέφει τις μοναδικές τιμές από ένα εύρος ή έναν πίνακα.", "ad": " Η περιοχή ή ο πίνακας από τον οποίο θα επιστραφούν μοναδικές σειρές ή στήλες! Είναι μια λογική τιμή: συγκρίνει σειρές μεταξύ τους και επιστρέφει τις μοναδικές σειρές = FALSE ή παραλείπεται. Συγκρίνει στήλες μεταξύ τους και επιστρέφει τις μοναδικές στήλες = TRUE! Είναι μια λογική τιμή: επιστρέφει σειρές ή στήλες που εμφανίζονται ακριβώς μία φορά από τον πίνακα = TRUE. Επιστρέφει όλες τις ξεχωριστές σειρές ή στήλες από τον πίνακα = FALSE ή παραλείπεται"}, "VLOOKUP": {"a": "(τιμή_αναζήτησης; πίν<PERSON><PERSON><PERSON><PERSON>; αριθμός_δείκτη_στήλης; [περιοχή_αναζήτησης])", "d": "Αναζητά μια τιμή στην πρώτη στήλη ενός πίνακα και επιστρέφει μια τιμή στην ίδια γραμμή από μια καθορισμένη στήλη. Εξ ορισμού, ο πίνακας θα πρέπει να είναι ταξινομημένος κατά αύξουσα σειρά", "ad": "είναι η τιμή που θα βρεθεί στην πρώτη στήλη του πίνακα και μπορεί να είναι μια τιμή, μια αναφορά ή μια ακολουθία χαρακτήρων κειμένου!είναι ένας πίνακας κειμένου, αριθμών ή λογικών τιμών, από τον οποίο γίνεται ανάκτηση δεδομένων. Η παράμετρος πίνακας μπορεί να είναι μια αναφορά σε περιοχή ή όνομα περιοχής!είναι ο αριθμός στήλης στην περιοχή πίνακας από την οποία θα επιστραφεί η τιμή ταιριάσματος. Η πρώτη στήλη τιμών σε έναν πίνακα είναι η στήλη 1!είναι μια λογική τιμή που παίρνει την τιμή TRUE ή παραλείπεται, για να βρεθεί το κατά προσέγγιση ταίριασμα στην πρώτη στήλη (ταξινομημένη κατά αύξουσα σειρά) ή την τιμή FALSE, για να βρεθεί το ακριβές ταίριασμα"}, "XLOOKUP": {"a": "(τιμή_αναζήτησης; πίν<PERSON><PERSON><PERSON><PERSON>_αναζήτησης; πίνακ<PERSON>ς_επιστροφής; [εάν_δεν_βρεθεί]; [λειτουργία_αντιστοίχισης]; [λειτουργία_αναζήτησης])", "d": "Κάνει αναζήτηση σε ένα εύρος ή έναν πίνακα για μια αντιστοίχιση και επιστρέφει το αντίστοιχο στοιχείο από ένα δεύτερο εύρος ή πίνακα. Από προεπιλογή, χρησιμοποιείται ακριβής αντιστοίχιση", "ad": "είναι η τιμή για αναζήτηση!είναι ο πίνακας ή το εύρος για αναζήτηση!είναι ο πίνακας ή το εύρος για επιστροφή!επιστρέφεται εάν δεν βρεθεί αντιστοιχία!καθορίστε τον τρόπο αντιστοίχισης της τιμής_αναζήτησης με τις τιμές στον πίνακα_αναζήτησης!καθορίστε τη λειτουργία αναζήτησης που θα χρησιμοποιηθεί. Από προεπιλογή, θα χρησιμοποιηθεί αναζήτηση από το πρώτο προς το τελευτα<PERSON><PERSON> στοιχείο"}, "CELL": {"a": "(τύπος_πληροφοριών; [αναφορά])", "d": "Επιστρέφει πληροφορίες σχετικά με τη μορφοποίηση, τη θέση ή τα περιεχόμενα ενός κελιού", "ad": "Πρόκειται για μια τιμή κειμένου που καθορίζει τον τύπο της πληροφορίας που επιθυμείτε να επιστραφεί!Πρόκειται για το κελί για το οποίο θέλετε πληροφορίες"}, "ERROR.TYPE": {"a": "(τιμή_σφάλματος)", "d": "Αποδίδει έναν αριθμό που αντιστοιχεί σε μια τιμή σφάλματος.", "ad": "είναι η τιμή σφάλματος της οποίας θέλετε να βρείτε τον αριθμό αναγνώρισης και μπορεί να είναι μια πραγματική τιμή σφάλματος ή μια αναφορά σε κελί που περιέχει τιμή σφάλματος"}, "ISBLANK": {"a": "(τιμή)", "d": "Ελέγχει αν μια αναφορά γίνεται σε κενό κελί και επιστρέφει TRUE ή FALSE", "ad": "είναι κε<PERSON><PERSON> ή όνομα που αναφέρεται στο κελί που θέλετε να ελέγξετε"}, "ISERR": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι σφάλμα διαφορετικό από #Δ/Υ και αποδίδει TRUE ή FALSE", "ad": "είναι η τιμή που θέλετε να ελέγξετε. Η τιμή μπορεί να αναφέρεται σε ένα κελί, έναν τύπο ή σε ένα όνομα που αναφέρεται σε κελί, τύπο ή τιμή"}, "ISERROR": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι σφάλμα και αποδίδει TRUE ή FALSE", "ad": "είναι η τιμή που θέλετε να ελέγξετε. Η τιμή μπορεί να αναφέρεται σε ένα κελί, έναν τύπο ή σε ένα όνομα που αναφέρεται σε κελί, τύπο ή τιμή"}, "ISEVEN": {"a": "(αριθμός)", "d": "Αποδίδει TRUE εάν ο αριθμός είναι ζυγός", "ad": "είναι η τιμή που θα ελεγχθεί"}, "ISFORMULA": {"a": "(αναφορά)", "d": "Ελέγχει εάν μια αναφορ<PERSON> δείχνει ένα κελί που περιέχει τύπο και αποδίδει TRUE ή FALSE", "ad": "είναι μια αναφορά προς το κελί που θέλετε να ελέγξετε.  Η αναφορά μπορεί να είναι αναφορά σε κελί, τύπος ή όνομα που αναφέρεται σε ένα κελί"}, "ISLOGICAL": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι μία από τις λογικές τιμές (TRUΕ ή FALSE) και επιστρέφει TRUE ή FALSE", "ad": "είναι η τιμή που θέλετε να ελέγξετε. Το Value μπορεί να αναφέρεται σε ένα κελί, έναν τύπο ή σε ένα όνομα που αναφέρεται σε κελί, τύπο ή τιμή"}, "ISNA": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι #Δ/Υ και αποδίδει TRUE ή FALSE", "ad": "είναι η τιμή που θέλετε να ελέγξετε. Το Value μπορεί να αναφέρεται σε ένα κελί, έναν τύπο ή σε ένα όνομα που αναφέρεται σε κελί, τύπο ή τιμή"}, "ISNONTEXT": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι κείμενο (τα κενά κελιά δεν είναι κείμενο) και επιστρέφει TRUE ή FALSE", "ad": "είναι η τιμή που θέλετε να ελέγξετε: έν<PERSON> κελί, ένας τύπος ή ένα όνομα που αναφέρεται σε κελί, τύπο ή τιμή"}, "ISNUMBER": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι αριθμός και επιστρέφει TRUE ή FALSE", "ad": "είναι η τιμή που θέλετε να ελέγξετε. Το Value μπορεί να αναφέρεται σε ένα κελί, έναν τύπο ή σε ένα όνομα που αναφέρεται σε κελί, τύπο ή τιμή"}, "ISODD": {"a": "(αριθμός)", "d": "Αποδίδει TRUE εάν ο αριθμός είναι μονός", "ad": "είναι η τιμή που θα ελεγχθεί"}, "ISREF": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι αναφορά και αποδίδει TRUE ή FALSE", "ad": "είναι η τιμή που θέλετε να ελέγξετε. Το Value μπορεί να αναφέρεται σε ένα κελί, έναν τύπο ή σε ένα όνομα που αναφέρεται σε κελί, τύπο ή τιμή"}, "ISTEXT": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι κείμενο και επιστρέφει TRUE ή FALSE", "ad": "είναι η τιμή που θέλετε να ελέγξετε. Το Value μπορεί να αναφέρεται σε ένα κελί, έναν τύπο ή σε ένα όνομα που αναφέρεται σε κελί, τύπο ή τιμή"}, "N": {"a": "(τιμή)", "d": "Μετατρέπει μη αριθμητικές τιμές σε αριθμό, ημερομηνίες σε αύξοντες αριθμούς, την τιμή TRUE σε 1 και οτιδήποτε άλλο σε 0 (μηδέν)", "ad": "είναι η τιμή που θέλετε να μετατραπεί"}, "NA": {"a": "()", "d": "Αποδίδει την τιμή σφάλματος #Δ/Υ (η τιμή δεν υπάρχει)", "ad": ""}, "SHEET": {"a": "([τιμή])", "d": "Αποδίδει τον αριθμό φύλλου του φύλλου στο οποίο γίνεται αναφορά", "ad": "είναι το όνομα ενός φύλλου ή μια αναφορά της οποίας θέλετε τον αριθμό φύλλου.  Εάν παραληφθεί, αποδίδεται ο αριθμός του φύλλου στο οποίο περιέχεται η συνάρτηση"}, "SHEETS": {"a": "([αναφορά])", "d": "Αποδίδει τον αριθμό φύλλων σε μια αναφορά", "ad": "είναι μια ανα<PERSON>ορά για την οποία θέλετε να γνωρίζετε το πλήθος των φύλλων που περιέχει.  Ε<PERSON><PERSON> παραληφθεί, απ<PERSON><PERSON><PERSON>δεται ο αριθμός των φύλλων που υπάρχουν στο βιβλίο εργασίας που περιέχει τη συνάρτηση"}, "TYPE": {"a": "(τιμή)", "d": "Επιστρέφει έναν ακέραιο που δηλώνει τον τύπο δεδομένων μιας τιμής: 1 αν είναι αριθμός, 2 αν είναι κείμενο, 4 αν είναι λογική τιμή, 16 αν είναι τιμή σφάλματος, 64 αν είναι πίνακας, 128 αν είναι σύνθετα δεδομένα", "ad": "μπορεί να είναι οποιαδήποτε τιμή"}, "AND": {"a": "(λογική1; [λογική2]; ...)", "d": "Ελέγχει αν όλα τα ορίσματα είναι TRUE και αποδίδει TRUE αν όλα τα ορίσματα είναι TRUE", "ad": "είναι 1 έως 255 συνθήκες που θέλετε να ελέγξετε, οι οποίες μπορεί να είναι TRUE ή FALSE και είναι δυνατό να αντιστοιχούν σε λογικές τιμές, πίν<PERSON><PERSON><PERSON>ς ή αναφορές"}, "FALSE": {"a": "()", "d": "Αποδίδει τη λογική τιμή FALSE", "ad": ""}, "IF": {"a": "(λογική_έλεγχος; [τιμή_αν_true]; [τιμή_αν_false])", "d": "Ελέγχει αν ικανοποιείται μια συνθήκη και αποδίδει μία τιμή αν η συνθήκη είναι TRUE και μία άλλη τιμή αν είναι FALSE", "ad": "είναι οποιαδήποτε τιμή ή παράσταση που μπορεί να πάρει την τιμή TRUE ή FALSE!είναι η τιμή που αποδίδεται, αν το Logical_test είναι TRUE. Ε<PERSON>ν παραλειφθεί, αποδίδεται η τιμή TRUE. Μπορείτε να ενσωματώσετε έως και 7 συναρτήσεις IF!είναι η τιμή που αποδίδεται, αν το Logical_test είναι FALSE. Ε<PERSON>ν παραλειφθεί, αποδίδεται η τιμή FALSE"}, "IFS": {"a": "(λογική_έλεγχος; τιμή_αν_true; ...)", "d": "Ελέγχει αν πληρούνται μία ή περισσότερες συνθήκες και επιστρέφει μια τιμή που αντιστοιχεί στην πρώτη συνθήκη με τιμή TRUE", "ad": "είναι οποιαδήποτε τιμή ή παράσταση που μπορεί να πάρει την τιμή TRUE ή FALSE!είναι η τιμή που επιστρέφεται αν η συνθήκη λογική_έλεγχος έχει τιμή TRUE"}, "IFERROR": {"a": "(τιμή; τιμή_εάν_σφάλμα)", "d": "Επιστρέφει την τιμή τιμή_εάν_σφάλμα, εάν η παράσταση είναι σφάλμα και η τιμή της παράστασης είναι διαφορετική", "ad": "είναι οποιαδήποτε τιμή ή παράσταση ή αναφορά!είναι οποιαδήποτε τιμή ή παράσταση ή αναφορά"}, "IFNA": {"a": "(τιμή; τιμή_εάν_δυ)", "d": "Αποδίδει την τιμή που καθορίζετε εάν η παράσταση αποδώσει #Δ/Υ, διαφορετι<PERSON><PERSON> αποδίδει το αποτέλεσμα της παράστασης", "ad": "είναι οποιαδήποτε τιμή ή παράσταση ή αναφορά!είναι οποιαδήποτε τιμή ή παράσταση ή αναφορά"}, "NOT": {"a": "(λογική)", "d": "Αλλάζει την τιμή TRUE σε FALSE ή την τιμή FALSE σε TRUE", "ad": "είναι τιμή ή παράσταση που είναι δυνατό να πάρει την τιμή TRUE ή FALSE"}, "OR": {"a": "(λογική1; [λογική2]; ...)", "d": "Ελέγχει αν κάποιο από τα ορίσματα είναι TRUE και αποδίδει TRUE ή FALSE. Αποδίδει FALSE μόνο αν όλα τα ορίσματα είναι FALSE", "ad": "είναι 1 έως 255 συνθήκες που θέλετε να ελέγξετε, οι οποίες μπορεί να είναι TRUE ή FALSE"}, "SWITCH": {"a": "(παράσταση; τιμή1; αποτέλεσμα1; [προεπιλογή_ή_τιμή2]; [αποτέλεσμα2]; ...)", "d": "Αξιολογεί μια παράσταση έναντι μιας λίστας τιμών και επιστρέφει το αποτέλεσμα που αντιστοιχεί στον πρώτο κανόνα αντιστοίχισης. Ε<PERSON>ν δεν υπάρχει συμφωνία, επιστρέφεται μια προαιρετική προεπιλεγμένη τιμή", "ad": "είναι μια παράσταση προς αξιολόγηση!είναι μια τιμή με την οποία θα συγκριθεί η παράσταση!είναι το αποτέλεσμα που επιστρέφεται εάν η αντίστοιχη τιμή συμφωνεί με την παράσταση"}, "TRUE": {"a": "()", "d": "Αποδίδει τη λογική τιμή TRUE", "ad": ""}, "XOR": {"a": "(λογική1; [λογική2]; ...)", "d": "Αποδίδει το λογικό 'αποκλειστικ<PERSON> ή' όλων των ορισμάτων", "ad": "είναι 1 έως 254 συνθήκες που θέλετε να ελέγξετε, οι οποίες μπορεί να είναι TRUE ή FALSE και μπορεί να είναι λογικές τιμές, πίνακες ή αναφορές"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Επιστρέφει κείμενο που είναι πριν από την οριοθέτηση χαρακτήρων.", "ad": "Το κείμενο που θέλετε να αναζητήσετε για τον οριοθέτη.!Ο χαρακτήρας ή η συμβολοσειρά που θα χρησιμοποιηθεί ως οριοθέτης.!Η επιθυμητή εμφάνιση οριοθέτη. Η προεπιλογή είναι 1. Ένας αρνητικός αριθμός αναζητά από το τέλος.!Πραγματοποιεί αναζήτηση στο κείμενο για αντιστοίχιση οριοθέτη. Από προεπιλογή, γίνεται αντιστοίχιση με διάκριση πεζών-κεφαλαίων.!Είτε θα αντιστοιχιστεί ο οριοθέτης με το τέλος του κειμένου. Από προεπιλογή, δεν ταιριάζουν.!Επιστρέφεται εάν δεν βρεθεί αντιστοιχία. Από προεπιλογή, το #N/A επιστρέφεται."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Επιστρέφει κείμενο που είναι μετά την οριοθέτηση χαρακτήρων.", "ad": "Το κείμενο που θέλετε να αναζητήσετε για τον οριοθέτη.!Ο χαρακτήρας ή τη συμβολοσειρά που θα χρησιμοποιήσετε ως οριοθέτη.!Η επιθυμητή εμφάνιση οριοθέτη. Η προεπιλογή είναι 1. Ένας αρνητικός αριθμός αναζητά από το τέλος.!Αναζητά το κείμενο για αντιστοίχιση οριοθέτη. Από προεπιλογή, γίνεται αντιστοίχιση με διάκριση πεζών-κεφαλαίων.!Είτε θα αντιστοιχιστεί ο οριοθέτης με το τέλος του κειμένου. Από προεπιλογή, δεν έχουν αντιστοιχιστεί.!Επιστρέφεται εάν δεν βρεθεί αντιστοιχία. Από προεπιλογή, το #N/A επιστρέφεται."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Διαχωρίζει το κείμενο σε σειρές ή στήλες χρησιμοποιώντας οριοθέτες.", "ad": "Το κείμενο προς διαίρεση!Χ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ρας ή συμβολοσειρά για διαχωρισμό στηλών κατά.!Χαρακτήρας ή συμβολοσειρά για διαχωρισμό σειρών κατά.!Εάν θα αγνοηθούν τα άδεια κελιά. Προεπιλογή σε FALSE.!Αναζητά το κείμενο για αντιστοίχιση οριοθέτη. Απ<PERSON> προεπιλογή, γίνεται αντιστοίχιση με διάκριση πεζών-κεφαλαίων.!Η τιμή που χρησιμοποιείται για την αναπλήρωση. Από προεπιλογή, χρησιμοποιείται #N/A."}, "WRAPROWS": {"a": "(διάνυσμα, wrap_count, [pad_with])", "d": " Αναδιπλώνει ένα διάνυσμα γραμμής ή στήλης μετά από έναν καθορισμένο αριθμό τιμών.", "ad": " Το διάνυσμα ή η αναφορά για αναδίπλωση.! Ο μέγιστος αριθμός τιμών ανά γραμμή.! Η τιμή με την οποία συμπληρώνεται. Η προεπιλογή είναι #N/A."}, "VSTACK": {"a": "(πίνακας1, [πίνακας2], ...)", "d": " Στοιβάζ<PERSON>ι κατακόρυφα πίνακες σε έναν πίνακα.", "ad": " Ένας πίνακας ή μια αναφορά σε στοίβα."}, "HSTACK": {"a": "(πίνακας1, [πίνακας2], ...)", "d": " Στοιχίζει οριζόντια πίνακες σε έναν πίνακα.", "ad": " Ένας πίνακας ή μια αναφορά σε στοίβα."}, "CHOOSEROWS": {"a": "(πίν<PERSON>κα<PERSON>, row_num1, [row_num2], ...)", "d": " Επιστρέφει γραμμές από έναν πίνακα ή αναφορά.", "ad": " Ο πίνακας ή η αναφορά που περιέχει τις γραμμές που θα επιστραφούν.! Ο αριθμός της γραμμής που θα επιστραφεί."}, "CHOOSECOLS": {"a": "(πίν<PERSON><PERSON><PERSON><PERSON>, col_num1, [col_num2], ...)", "d": " Επιστρέφει στήλες από έναν πίνακα ή αναφορά.", "ad": " Ο πίνακας ή η αναφορά που περιέχει τις στήλες που θα επιστραφούν.! Ο αριθμός της στήλης που θα επιστραφεί."}, "TOCOL": {"a": "(πίνακας, [παράβλεψη], [scan_by_column])", "d": " Επιστρέφει τον πίνακα ως μία στήλη.", "ad": " Ο πίνακας ή η αναφορά για επιστροφή ως στήλη.! Εάν θα παραβλέψετε ορισμένους τύπους τιμών. Από προεπιλογή, δεν παραβλέπονται τιμές.! Σαρώστε τον πίνακα κατά στήλη. Από προεπιλογή, ο πίνακας σαρώνεται κατά γραμμή."}, "TOROW": {"a": "(πίνακας, [παράβλεψη], [scan_by_column])", "d": " Επιστρέφει τον πίνακα ως μία γραμμή.", "ad": " Ο πίνακας ή η αναφορά για επιστροφή ως γραμμή.! Εάν θα παραβλέψετε ορισμένους τύπους τιμών. Από προεπιλογή, δεν παραβλέπονται τιμές.! Σαρώστε τον πίνακα κατά στήλη. Από προεπιλογή, ο πίνακας σαρώνεται κατά γραμμή."}, "WRAPCOLS": {"a": "(διάνυσμα, wrap_count, [pad_with])", "d": " Αναδιπλώνει ένα διάνυσμα γραμμής ή στήλης μετά από έναν καθορισμένο αριθμό τιμών.", "ad": " Το διάνυσμα ή η αναφορά για αναδίπλωση.! Ο μέγιστος αριθμός τιμών ανά στήλη.! Η τιμή με την οποία συμπληρώνεται. Η προεπιλογή είναι #N/A."}, "TAKE": {"a": "(πίνακας, γραμμές, [στήλες])", "d": " Επιστρέφει γραμμές ή στήλες από την αρχή ή το τέλος του πίνακα.", "ad": " Ο πίνακας από τον οποίο θα λάβετε γραμμές ή στήλες.! Ο αριθμός των γραμμών που θα χρειαστούν. Μια αρνητική τιμή λαμβάνει από το τέλος του πίνακα.! Ο αριθμός των στηλών που θα χρειαστούν. Μια αρνητική τιμή λαμβάνει από το τέλος του πίνακα."}, "DROP": {"a": "(πίνακας, γραμμές, [στήλες])", "d": " Αποθέτει γραμμές ή στήλες από την αρχή ή το τέλος του πίνακα.", "ad": " Ο πίνακας από τον οποίο θα αποθέστε γραμμές ή στήλες.! Ο αριθμός των γραμμών προς απόθεση. Μια αρνητική τιμή πέφτει από το τέλος του πίνακα.! Ο αριθμός των στηλών προς απόθεση. Μια αρνητική τιμή πέφτει από το τέλος του πίνακα."}, "SEQUENCE": {"a": "(γραμμές, [στήλες], [έναρξη], [βήμα])", "d": "Επιστρέφει μια ακολουθία αριθμών", "ad": "ο αριθμός των γραμμών για επιστροφή!ο αριθμός των στηλών για επιστροφή!ο πρώτος αριθμός στην ακολουθία!το ποσό αύξησης κάθε διαδοχική τιμή στην ακολουθία"}, "EXPAND": {"a": "(πίνακας, γραμμές, [στήλες], [pad_with])", "d": "Επεκτείνει έναν πίνακα στις καθορισμένες διαστάσεις.", "ad": " Ο πίνακας προς ανάπτυξη.! Ο αριθμός των γραμμών στον αναπτυγμένο πίνακα. Εάν λείπουν, οι γραμμές δεν θα αναπτυχθούν.! Ο αριθμός των στηλών στον αναπτυγμένο πίνακα. Ε<PERSON>ν λείπουν, οι στήλες δεν θα αναπτυχθούν.! Η τιμή με την οποία θα γίνει συμπλήρωση. Η προεπιλογή είναι #N/A."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "Επιστρέφει τη σχετική θέση ενός στοιχείου σε έναν πίνακα. Από προεπιλογή, απαιτείται ακριβής αντιστοίχιση", "ad": "είναι η τιμή προς αναζήτηση!είν<PERSON>ι ο πίνακας ή η περιοχή για αναζήτηση!καθορίστε τον τρόπο αντιστοίχισης της τιμής_αναζήτησης με τις τιμές στον πίνακα_αναζήτησης!καθορίστε τη λειτουργία αναζήτησης που θα χρησιμοποιηθεί. Από προεπιλογή, θα χρησιμοποιηθεί μια πρώτη έως τελευταία αναζήτηση"}, "FILTER": {"a": "(πίνακα<PERSON>, συμπερίληψη, [αν_κενός])", "d": "Φιλτράρισμα μιας περιοχής ή ενός πίνακα", "ad": "η περιοχή ή ο πίνακας προς φιλτράρισμα!ένας πίνακας με δυαδικές τιμές όπου η τιμή TRUE αντιπροσωπεύει μια γραμμή ή μια στήλη προς διατήρηση!επιστρέφεται αν δεν έχουν διατηρηθεί στοιχεία"}, "ARRAYTOTEXT": {"a": "(πίνακας, [μορφή])", "d": "Επιστρέφει μια αναπαράσταση κειμένου ενός πίνακα", "ad": "ο πίνακας που θα αναπαρασταθεί ως κείμενο!η μορφή του κειμένου"}, "SORT": {"a": "(πίνακας, [δείκτης_ταξινόμησης], [σειρά_ταξινόμησης], [κατά_στήλη])", "d": "Ταξινομεί μια περιοχή ή έναν πίνακα", "ad": "η περιοχή ή ο πίνακας προς ταξινόμηση!ένας αριθμός που δηλώνει τη γραμμή ή τη στήλη με βάση την οποία θα γίνει ταξινόμηση!ένας αριθμός που δηλώνει την επιθυμητή σειρά ταξινόμησης, 1 για αύξουσα σειρά (προεπιλογή), -1 για φθίνουσα σειρά!μια λογική τιμή που υποδεικνύει την επιθυμητή κατεύθυνση ταξινόμησης: FALSE, για ταξινόμηση κατά γραμμή (προεπιλογή), TRUE για ταξινόμηση κατά στήλη"}, "SORTBY": {"a": "(π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, κατ<PERSON>_πίνακα, [σειρά_ταξινόμησης], ...)", "d": "Ταξινομ<PERSON><PERSON> ένα εύρος τιμών ή έναν πίνακα με βάση τις τιμές σε ένα αντίστοιχο εύρος τιμών ή πίνακα", "ad": "το εύρος τιμών ή ο πίνακας για ταξινόμηση!το εύρος τιμών ή ο πίνακας που θα χρησιμοποιηθεί για ταξινόμηση!ένας αριθμός που υποδεικνύει την επιθυμητή σειρά ταξινόμησης; 1 για αύξουσα σειρά (προεπιλογή), -1 για φθίνουσα σειρά"}, "GETPIVOTDATA": {"a": "(πεδίο_δεδομένων; συγκεντρωτικός_πίνακας; [πεδίο]; [στοιχείο]; ...)", "d": "Εξάγει δεδομένα που είναι αποθηκευμένα σε έναν Συγκεντρωτικό Πίνακα", "ad": "είναι το όνομα του πεδίου δεδομένων από το οποίο θέλετε να εξαγάγετε δεδομένα!είναι μια αναφορά σε ένα κελί ή μια περιοχή κελιών στον Συγκεντρωτικό Πίνακα που περιέχει τα δεδομένα τα οποία θέλετε να ανακτήσετε!πεδίο στο οποίο γίνεται αναφορά!στοιχείο πεδίου στο οποίο γίνεται αναφορά"}, "IMPORTRANGE": {"a": "(spreadsheet_url, συμβολοσειρά_εύρους)", "d": "Εισάγει εύρος κελιών από κάποιο καθορισμένο υπολογιστικό φύλλο."}}