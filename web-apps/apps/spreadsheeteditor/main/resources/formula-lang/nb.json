{"DATE": "DATO", "DATEDIF": "DATEDIF", "DATEVALUE": "DATOVERDI", "DAY": "DAG", "DAYS": "DAGER", "DAYS360": "DAGER360", "EDATE": "DAG.ETTER", "EOMONTH": "MÅNEDSSLUTT", "HOUR": "TIME", "ISOWEEKNUM": "ISOUKENR", "MINUTE": "MINUTT", "MONTH": "MÅNED", "NETWORKDAYS": "NETT.ARBEIDSDAGER", "NETWORKDAYS.INTL": "NETT.ARBEIDSDAGER.INTL", "NOW": "NÅ", "SECOND": "SEKUND", "TIME": "TID", "TIMEVALUE": "TIDSVERDI", "TODAY": "IDAG", "WEEKDAY": "UKEDAG", "WEEKNUM": "UKENR", "WORKDAY": "ARBEIDSDAG", "WORKDAY.INTL": "ARBEIDSDAG.INTL", "YEAR": "ÅR", "YEARFRAC": "ÅRDEL", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BINTILDES", "BIN2HEX": "BINTILHEKS", "BIN2OCT": "BINTILOKT", "BITAND": "BITOG", "BITLSHIFT": "BITVFORSKYV", "BITOR": "BITELLER", "BITRSHIFT": "BITHFORSKYV", "BITXOR": "BITEKSKLUSIVELLER", "COMPLEX": "KOMPLEKS", "CONVERT": "KONVERTER", "DEC2BIN": "DESTILBIN", "DEC2HEX": "DESTILHEKS", "DEC2OCT": "DESTILOKT", "DELTA": "DELTA", "ERF": "FEILF", "ERF.PRECISE": "FEILF.PRESIS", "ERFC": "FEILFK", "ERFC.PRECISE": "FEILFK.PRESIS", "GESTEP": "GRENSEVERDI", "HEX2BIN": "HEKSTILBIN", "HEX2DEC": "HEKSTILDES", "HEX2OCT": "HEKSTILOKT", "IMABS": "IMABS", "IMAGINARY": "IMAGINÆR", "IMARGUMENT": "IMARGUMENT", "IMCONJUGATE": "IMKONJUGERT", "IMCOS": "IMCOS", "IMCOSH": "IMCOSH", "IMCOT": "IMCOT", "IMCSC": "IMCSC", "IMCSCH": "IMCSCH", "IMDIV": "IMDIV", "IMEXP": "IMEKSP", "IMLN": "IMLN", "IMLOG10": "IMLOG10", "IMLOG2": "IMLOG2", "IMPOWER": "IMOPPHØY", "IMPRODUCT": "IMPRODUKT", "IMREAL": "IMREELL", "IMSEC": "IMSEC", "IMSECH": "IMSECH", "IMSIN": "IMSIN", "IMSINH": "IMSINH", "IMSQRT": "IMROT", "IMSUB": "IMSUB", "IMSUM": "IMSUMMER", "IMTAN": "IMTAN", "OCT2BIN": "OKTTILBIN", "OCT2DEC": "OKTTILDES", "OCT2HEX": "OKTTILHEKS", "DAVERAGE": "DGJENNOMSNITT", "DCOUNT": "DANTALL", "DCOUNTA": "DANTALLA", "DGET": "DHENT", "DMAX": "DMAKS", "DMIN": "DMIN", "DPRODUCT": "DPRODUKT", "DSTDEV": "DSTDAV", "DSTDEVP": "DSTDAVP", "DSUM": "DSUMMER", "DVAR": "DVARIANS", "DVARP": "DVARIANSP", "CHAR": "TEGNKODE", "CLEAN": "RENSK", "CODE": "KODE", "CONCATENATE": "KJEDE.SAMMEN", "CONCAT": "KJED.SAMMEN", "DOLLAR": "VALUTA", "EXACT": "EKSAKT", "FIND": "FINN", "FINDB": "FINDB", "FIXED": "FASTSATT", "LEFT": "VENSTRE", "LEFTB": "LEFTB", "LEN": "LENGDE", "LENB": "LENB", "LOWER": "SMÅ", "MID": "DELTEKST", "MIDB": "MIDB", "NUMBERVALUE": "TALLVERDI", "PROPER": "STOR.FORBOKSTAV", "REPLACE": "ERSTATT", "REPLACEB": "REPLACEB", "REPT": "GJENTA", "RIGHT": "HØYRE", "RIGHTB": "RIGHTB", "SEARCH": "SØK", "SEARCHB": "SEARCHB", "SUBSTITUTE": "BYTT.UT", "T": "T", "T.TEST": "T.TEST", "TEXT": "TEKST", "TEXTJOIN": "TEKST.KOMBINER", "TREND": "TREND", "TRIM": "TRIMME", "TRIMMEAN": "TRIMMET.GJENNOMSNITT", "TTEST": "TTEST", "UNICHAR": "UNICODETEGN", "UNICODE": "UNICODE", "UPPER": "STORE", "VALUE": "VERDI", "AVEDEV": "GJENNOMSNITTSAVVIK", "AVERAGE": "GJENNOMSNITT", "AVERAGEA": "GJENNOMSNITTA", "AVERAGEIF": "GJENNOMSNITTHVIS", "AVERAGEIFS": "GJENNOMSNITT.HVIS.SETT", "BETADIST": "BETA.FORDELING", "BETAINV": "INVERS.BETA.FORDELING", "BETA.DIST": "BETA.FORDELING.N", "BETA.INV": "BETA.INV", "BINOMDIST": "BINOM.FORDELING", "BINOM.DIST": "BINOM.FORDELING.N", "BINOM.DIST.RANGE": "BINOM.FORDELING.OMRÅDE", "BINOM.INV": "BINOM.INV", "CHIDIST": "KJI.FORDELING", "CHIINV": "INVERS.KJI.FORDELING", "CHITEST": "KJI.TEST", "CHISQ.DIST": "KJIKVADRAT.FORDELING", "CHISQ.DIST.RT": "KJIKVADRAT.FORDELING.H", "CHISQ.INV": "KJIKVADRAT.INV", "CHISQ.INV.RT": "KJIKVADRAT.INV.H", "CHISQ.TEST": "KJIKVADRAT.TEST", "CONFIDENCE": "KONFIDENS", "CONFIDENCE.NORM": "KONFIDENS.NORM", "CONFIDENCE.T": "KONFIDENS.T", "CORREL": "KORRELASJON", "COUNT": "ANTALL", "COUNTA": "ANTALLA", "COUNTBLANK": "TELLBLANKE", "COUNTIF": "ANTALL.HVIS", "COUNTIFS": "ANTALL.HVIS.SETT", "COVAR": "KOVARIANS", "COVARIANCE.P": "KOVARIANS.P", "COVARIANCE.S": "KOVARIANS.S", "CRITBINOM": "GRENSE.BINOM", "DEVSQ": "AVVIK.KVADRERT", "EXPON.DIST": "EKSP.FORDELING.N", "EXPONDIST": "EKSP.FORDELING", "FDIST": "FFORDELING", "FINV": "FFORDELING.INVERS", "FTEST": "FTEST", "F.DIST": "F.FORDELING", "F.DIST.RT": "F.FORDELING.H", "F.INV": "F.INV", "F.INV.RT": "F.INV.H", "F.TEST": "F.TEST", "FISHER": "FISHER", "FISHERINV": "FISHERINV", "FORECAST": "PROGNOSE", "FORECAST.ETS": "PROGNOSE.ETS", "FORECAST.ETS.CONFINT": "PROGNOSE.ETS.CONFINT", "FORECAST.ETS.SEASONALITY": "PROGNOSE.ETS.SESONGAVHENGIGHET", "FORECAST.ETS.STAT": "PROGNOSE.ETS.STAT", "FORECAST.LINEAR": "PROGNOSE.LINEÆR", "FREQUENCY": "FREKVENS", "GAMMA": "GAMMA", "GAMMADIST": "GAMMAFORDELING", "GAMMA.DIST": "GAMMA.FORDELING", "GAMMAINV": "GAMMAINV", "GAMMA.INV": "GAMMA.INV", "GAMMALN": "GAMMALN", "GAMMALN.PRECISE": "GAMMALN.PRESIS", "GAUSS": "GAUSS", "GEOMEAN": "GJENNOMSNITT.GEOMETRISK", "GROWTH": "VEKST", "HARMEAN": "GJENNOMSNITT.HARMONISK", "HYPGEOM.DIST": "HYPGEOM.FORDELING.N", "HYPGEOMDIST": "HYPGEOM.FORDELING", "INTERCEPT": "SKJÆRINGSPUNKT", "KURT": "KURT", "LARGE": "N.<PERSON>", "LINEST": "RETTLINJE", "LOGEST": "KURVE", "LOGINV": "LOGINV", "LOGNORM.DIST": "LOGNORM.FORDELING", "LOGNORM.INV": "LOGNORM.INV", "LOGNORMDIST": "LOGNORMFORD", "MAX": "STØRST", "MAXA": "MAKSA", "MAXIFS": "MAKS.HVIS.SETT", "MEDIAN": "MEDIAN", "MIN": "MIN", "MINA": "MINA", "MINIFS": "MIN.HVIS.SETT", "MODE": "MODUS", "MODE.MULT": "MODUS.MULT", "MODE.SNGL": "MODUS.SNGL", "NEGBINOM.DIST": "NEGBINOM.FORDELING.N", "NEGBINOMDIST": "NEGBINOM.FORDELING", "NORM.DIST": "NORM.FORDELING", "NORM.INV": "NORM.INV", "NORM.S.DIST": "NORM.S.FORDELING", "NORM.S.INV": "NORM.S.INV", "NORMDIST": "NORMALFORDELING", "NORMINV": "NORMINV", "NORMSDIST": "NORMSFORDELING", "NORMSINV": "NORMSINV", "PEARSON": "PEARSON", "PERCENTILE": "PERSENTIL", "PERCENTILE.EXC": "PERSENTIL.EKS", "PERCENTILE.INC": "PERSENTIL.INK", "PERCENTRANK": "PROSENTDEL", "PERCENTRANK.EXC": "PROSENTDEL.EKS", "PERCENTRANK.INC": "PROSENTDEL.INK", "PERMUT": "PERMUTER", "PERMUTATIONA": "PERMUTASJONA", "PHI": "PHI", "POISSON": "POISSON", "POISSON.DIST": "POISSON.FORDELING", "PROB": "SANNSYNLIG", "QUARTILE": "KVARTIL", "QUARTILE.INC": "KVARTIL.INK", "QUARTILE.EXC": "KVARTIL.EKS", "RANK.AVG": "RANG.GJSN", "RANK.EQ": "RANG.EKV", "RANK": "RANG", "RSQ": "RKVADRAT", "SKEW": "SKJEVFORDELING", "SKEW.P": "SKJEVFORDELING.P", "SLOPE": "STIGNINGSTALL", "SMALL": "N.<PERSON>", "STANDARDIZE": "NORMALISER", "STDEV": "STDAV", "STDEV.P": "STDAV.P", "STDEV.S": "STDAV.S", "STDEVA": "STDAVVIKA", "STDEVP": "STDAVP", "STDEVPA": "STDAVVIKPA", "STEYX": "STANDARDFEIL", "TDIST": "TFORDELING", "TINV": "TINV", "T.DIST": "T.FORDELING", "T.DIST.2T": "T.FORDELING.2T", "T.DIST.RT": "T.FORDELING.H", "T.INV": "T.INV", "T.INV.2T": "T.INV.2T", "VAR": "VARIANS", "VAR.P": "VARIANS.P", "VAR.S": "VARIANS.S", "VARA": "VARIANSA", "VARP": "VARIANSP", "VARPA": "VARIANSPA", "WEIBULL": "WEIBULL.FORDELING", "WEIBULL.DIST": "WEIBULL.DIST.N", "Z.TEST": "Z.TEST", "ZTEST": "ZTEST", "ACCRINT": "PÅLØPT.PERIODISK.RENTE", "ACCRINTM": "PÅLØPT.FORFALLSRENTE", "AMORDEGRC": "AMORDEGRC", "AMORLINC": "AMORLINC", "COUPDAYBS": "OBLIG.DAGER.FF", "COUPDAYS": "OBLIG.DAGER", "COUPDAYSNC": "OBLIG.DAGER.NF", "COUPNCD": "OBLIG.DAGER.EF", "COUPNUM": "OBLIG.ANTALL", "COUPPCD": "OBLIG.DAG.FORRIGE", "CUMIPMT": "SAMLET.RENTE", "CUMPRINC": "SAMLET.HOVEDSTOL", "DB": "DAVSKR", "DDB": "DEGRAVS", "DISC": "DISKONTERT", "DOLLARDE": "DOLLARDE", "DOLLARFR": "DOLLARBR", "DURATION": "VARIGHET", "EFFECT": "EFFEKTIV.RENTE", "FV": "SLUTTVERDI", "FVSCHEDULE": "SVPLAN", "INTRATE": "RENTESATS", "IPMT": "RAVDRAG", "IRR": "IR", "ISPMT": "ER.AVDRAG", "MDURATION": "MVARIGHET", "MIRR": "MODIR", "NOMINAL": "NOMINELL", "NPER": "PERIODER", "NPV": "NNV", "ODDFPRICE": "AVVIKFP.PRIS", "ODDFYIELD": "AVVIKFP.AVKASTNING", "ODDLPRICE": "AVVIKSP.PRIS", "ODDLYIELD": "AVVIKSP.AVKASTNING", "PDURATION": "PVARIGHET", "PMT": "AVDRAG", "PPMT": "AMORT", "PRICE": "PRIS", "PRICEDISC": "PRIS.DISKONTERT", "PRICEMAT": "PRIS.FORFALL", "PV": "NÅVERDI", "RATE": "RENTE", "RECEIVED": "MOTTATT.AVKAST", "RRI": "REALISERT.AVKASTNING", "SLN": "LINAVS", "SYD": "ÅRSAVS", "TBILLEQ": "TBILLEKV", "TBILLPRICE": "TBILLPRIS", "TBILLYIELD": "TBILLAVKASTNING", "VDB": "VERDIAVS", "XIRR": "XIR", "XNPV": "XNNV", "YIELD": "AVKAST", "YIELDDISC": "AVKAST.DISKONTERT", "YIELDMAT": "AVKAST.FORFALL", "ABS": "ABS", "ACOS": "ARCCOS", "ACOSH": "ARCCOSH", "ACOT": "ACOT", "ACOTH": "ACOTH", "AGGREGATE": "MENGDE", "ARABIC": "ARABISK", "ASC": "ASC", "ASIN": "ARCSIN", "ASINH": "ARCSINH", "ATAN": "ARCTAN", "ATAN2": "ARCTAN2", "ATANH": "ARCTANH", "BASE": "GRUNNTALL", "CEILING": "AVRUND.GJELDENDE.MULTIPLUM", "CEILING.MATH": "AVRUND.GJELDENDE.MULTIPLUM.OPP.MATEMATISK", "CEILING.PRECISE": "CEILING.PRESIZE", "COMBIN": "KOMBINASJON", "COMBINA": "KOMBINASJONA", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "DESIMAL", "DEGREES": "GRADER", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "AVRUND.TIL.PARTALL", "EXP": "EKSP", "FACT": "FAKULTET", "FACTDOUBLE": "DOBBELFAKT", "FLOOR": "AVRUND.GJELDENDE.MULTIPLUM.NED", "FLOOR.PRECISE": "FLOOR.PRECISE", "FLOOR.MATH": "AVRUND.GJELDENDE.MULTIPLUM.NED.MATEMATISK", "GCD": "SFF", "INT": "HELTALL", "ISO.CEILING": "ISO.CEILING", "LCM": "MFM", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MDETERM", "MINVERSE": "MINVERS", "MMULT": "MMULT", "MOD": "REST", "MROUND": "MRUND", "MULTINOMIAL": "MULTINOMINELL", "MUNIT": "MENHET", "ODD": "AVRUND.TIL.ODDETALL", "PI": "PI", "POWER": "OPPHØYD.I", "PRODUCT": "PRODUKT", "QUOTIENT": "KVOTIENT", "RADIANS": "RADIANER", "RAND": "TILFELDIG", "RANDARRAY": "TILFELDIGMATRISE", "RANDBETWEEN": "TILFELDIGMELLOM", "ROMAN": "ROMERTALL", "ROUND": "AVRUND", "ROUNDDOWN": "AVRUND.NED", "ROUNDUP": "AVRUND.OPP", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SUMMER.REKKE", "SIGN": "FORTEGN", "SIN": "SIN", "SINH": "SINH", "SQRT": "ROT", "SQRTPI": "ROTPI", "SUBTOTAL": "DELSUM", "SUM": "SUMMER", "SUMIF": "SUMMERHVIS", "SUMIFS": "SUMMER.HVIS.SETT", "SUMPRODUCT": "SUMMERPRODUKT", "SUMSQ": "SUMMERKVADRAT", "SUMX2MY2": "SUMMERX2MY2", "SUMX2PY2": "SUMMERX2PY2", "SUMXMY2": "SUMMERXMY2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "AVKORT", "ADDRESS": "ADRESSE", "CHOOSE": "VELG", "COLUMN": "KOLONNE", "COLUMNS": "KOLONNER", "FORMULATEXT": "FORMELTEKST", "HLOOKUP": "FINN.KOLONNE", "HYPERLINK": "HYPERKOBLING", "INDEX": "INDEKS", "INDIRECT": "INDIREKTE", "LOOKUP": "SLÅ.OPP", "MATCH": "SAMMENLIGNE", "OFFSET": "FORSKYVNING", "ROW": "RAD", "ROWS": "RADER", "TRANSPOSE": "TRANSPONER", "UNIQUE": "UNIK", "VLOOKUP": "FINN.RAD", "XLOOKUP": "XOPPSLAG", "CELL": "CELL", "ERROR.TYPE": "FEIL.TYPE", "ISBLANK": "ERTOM", "ISERR": "ERF", "ISERROR": "ERFEIL", "ISEVEN": "ERPARTALL", "ISFORMULA": "ERFORMEL", "ISLOGICAL": "ERLOGISK", "ISNA": "ERIT", "ISNONTEXT": "ERIKKETEKST", "ISNUMBER": "ERTALL", "ISODD": "ERODDE", "ISREF": "ERREF", "ISTEXT": "ERTEKST", "N": "N", "NA": "IT", "SHEET": "ARK", "SHEETS": "ANTALL.ARK", "TYPE": "VERDITYPE", "AND": "OG", "FALSE": "USANN", "IF": "HVIS", "IFS": "HVIS.SETT", "IFERROR": "HVISFEIL", "IFNA": "HVIS.IT", "NOT": "IKKE", "OR": "ELLER", "SWITCH": "BRYTER", "TRUE": "SANN", "XOR": "EKSKLUSIVELLER", "TEXTBEFORE": "TEKSTFØR", "TEXTAFTER": "TEKSTETTER", "TEXTSPLIT": "TEKSTDELING", "WRAPROWS": "BRYTRADER", "VSTACK": "VSTAKK", "HSTACK": "HSTAKK", "CHOOSEROWS": "VELGRADER", "CHOOSECOLS": "VELGKOL", "TOCOL": "TILKOL", "TOROW": "TILRAD", "WRAPCOLS": "BRYTKOL", "TAKE": "TA", "DROP": "UTELAT", "SEQUENCE": "SEKVENS", "EXPAND": "UTVID", "XMATCH": "XSAMSVAR", "FILTER": "FILTRER", "ARRAYTOTEXT": "MATRISETILTEKST", "SORT": "SORTER", "SORTBY": "SORTER.ETTER", "GETPIVOTDATA": "HENTPIVOTDATA", "IMPORTRANGE": "IMPORTRANGE", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "TRUE", "f": "FALSE"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!", "calc": "#BEREGN!"}, "CELL_FUNCTION_INFO_TYPE": {"address": "adresse", "col": "kol", "color": "farge", "contents": "innhold", "filename": "filnavn", "format": "format", "parentheses": "parenteser", "prefix": "prefiks", "protect": "be<PERSON><PERSON>", "row": "rad", "type": "type", "width": "bredde"}}}