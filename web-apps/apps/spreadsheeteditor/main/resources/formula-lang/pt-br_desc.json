{"DATE": {"a": "(ano; mês; dia)", "d": "Retorna o número que representa a data no código data-hora", "ad": "é um número de 1900 ou 1904 (dependendo do sistema de datas da pasta de trabalho) a 9999!é um número de 1 a 12 que representa o mês do ano!é um número de 1 a 31 que representa o dia do mês"}, "DATEDIF": {"a": "(data_inicial; data_final; unidade)", "d": "Calcula o número de dias, meses ou anos entre duas datas", "ad": "Uma data que representa a primeira ou a data de início de um determinado período!Uma data que representa a última data, ou final, do período!O tipo de informação que você deseja retornar"}, "DATEVALUE": {"a": "(texto_data)", "d": "Converte uma data com formato de texto em um número que representa a data no código data-hora", "ad": "é o texto que representa uma data no formato de data do Spreadsheet Editor, entre 1/1/1900 ou 1/1/1904 (dependendo do sistema de datas da pasta de trabalho) e 31/12/9999"}, "DAY": {"a": "(núm_série)", "d": "Retorna o dia do mês, um número de 1 a 31.", "ad": "é um número no código data-hora usado pelo Spreadsheet Editor"}, "DAYS": {"a": "(data_final; data_inicial)", "d": "Retorna o número de dias entre duas datas.", "ad": "data_inicial e data_final são as duas datas entre as quais você deseja saber o número de dias!data_inicial e data_final são as duas datas entre as quais você deseja saber o número de dias"}, "DAYS360": {"a": "(data_inicial; data_final; [método])", "d": "Retorna o número de dias entre duas datas com base em um ano de 360 dias (doze meses de 30 dias)", "ad": "'Data_inicial' e 'Data_final' são as duas datas entre as quais você deseja saber o número de dias!'Data_inicial' e 'Data_final' são as duas datas entre as quais você deseja saber o número de dias!é um valor lógico que especifica o método de cálculo: U.S. (NASD) = FALSO ou não especificado; Europeu = VERDADEIRO."}, "EDATE": {"a": "(data_inicial; meses)", "d": "Retorna o número de série da data que é o número indicado de meses antes ou depois da data inicial", "ad": "é o número serial de data que representa a data inicial!é o número de meses antes ou depois de data_inicial"}, "EOMONTH": {"a": "(data_inicial; meses)", "d": "Retorna o número serial do último dia do mês antes ou depois de um dado número de meses", "ad": "é o número serial de data que representa a data inicial!é o número de meses antes ou depois da data inicial"}, "HOUR": {"a": "(núm_série)", "d": "Retorna a hora como um número de 0 (12:00 AM) a 23 (11:00 PM).", "ad": "é um número no código data-hora usado pelo Spreadsheet Editor ou um texto no formato de hora como, por exemplo, 16:48:00 ou 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(data)", "d": "Retorna o número da semana ISO do ano de uma determinada data", "ad": "é o código data-hora usado pelo Spreadsheet Editor para cálculo de data e hora"}, "MINUTE": {"a": "(núm_série)", "d": "Retorna o minuto, um número de 0 a 59.", "ad": "é um número no código data-hora usado pelo Spreadsheet Editor ou um texto no formato de hora como, por exemplo, 16:48:00 ou 4:48:00 PM"}, "MONTH": {"a": "(núm_série)", "d": "<PERSON><PERSON><PERSON> o mês, um número entre 1 (janeiro) e 12 (de<PERSON><PERSON><PERSON>).", "ad": "é um número no código data-hora usado pelo Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(data_inicial; data_final; [feriados])", "d": "Retorna o número de dias úteis entre duas datas", "ad": "é o número serial de data que representa a data inicial!é o número serial de data que representa a data final!é uma matriz opcional de um ou mais números seriais de datas para serem excluídos do calendário de trabalho, como feriados estaduais e federais e feriados móveis"}, "NETWORKDAYS.INTL": {"a": "(data_inicial; data_final; [fimdesemana]; [feriados])", "d": "Retorna o número de dias úteis entre duas datas com parâmetros de fim de semana personalizados", "ad": "é um número de série de datas que representa a data inicial!é um número de série de datas que representa a data final!é um número ou cadeia de caracteres que especifica quando ocorrem os fins de semana!é um conjunto opcional de um ou mais números de série de datas a serem excluídos do calendário de trabalho, como feriados estaduais e federais e feriados móveis"}, "NOW": {"a": "()", "d": "Retorna a data e a hora atuais formatadas como data e hora.", "ad": ""}, "SECOND": {"a": "(núm_série)", "d": "Retorna o segundo, um número de 0 a 59.", "ad": "é um número no código data-hora usado pelo Spreadsheet Editor ou um texto no formato de hora como, por exemplo, 16:48:23 ou 4:48:47 PM"}, "TIME": {"a": "(hora; minuto; segundo)", "d": "Converte horas, minutos e segundos fornecidos como números em um número de série, formatado com formato de hora", "ad": "é um número de 0 a 23 representando a hora!é um número de 0 a 59 representando o minuto!é um número de 0 a 59 representando o segundo"}, "TIMEVALUE": {"a": "(texto_hora)", "d": "Converte uma hora de texto em um número de série que represente hora, um número de 0 (12:00:00 AM) a 0.999988426 (11:59:59 PM). Formate o número com um formato de hora após inserir a fórmula", "ad": "é uma cadeia de texto que retorna uma hora em qualquer um dos formatos de hora do Spreadsheet Editor (a informação de data na cadeia é ignorada)"}, "TODAY": {"a": "()", "d": "Retorna a data de hoje formatada como uma data.", "ad": ""}, "WEEKDAY": {"a": "(núm_série; [retornar_tipo])", "d": "Retorna um número entre 1 e 7 identificando o dia da semana,", "ad": "é um número que representa uma data!é um número: para Domingo=1 até Sábado=7, utilize 1; para Segunda=1 até Domingo=7, utilize 2; para Segunda=0 até Domingo=6, utilize 3"}, "WEEKNUM": {"a": "(núm_série; [tipo_retorno])", "d": "Retorna o número da semana no ano", "ad": "é o código data-hora utilizado pelo Spreadsheet Editor para cálculo de data e hora!é um número (1 ou 2) que determina o tipo do valor retornado"}, "WORKDAY": {"a": "(data_inicial; dias; [feriados])", "d": "Retorna o número de série da data antes ou depois de um número especificado de dias úteis", "ad": "é o número serial de data que representa a data inicial!é o número de dias úteis antes ou depois da data inicial!é uma matriz opcional de um ou mais números seriais de datas para serem excluídos do calendário de trabalho, como feriados estaduais e federais e feriados móveis"}, "WORKDAY.INTL": {"a": "(data_inicial; dias; [fimdesemana]; [feriados])", "d": "Retorna o número de série de datas anteriores ou posteriores a um número especificado de dias úteis com parâmetros de fim de semana personalizados", "ad": "é o número de série de datas que representa a data inicial!é o número de dias úteis antes ou depois da data_inicial!é um número ou cadeia de caracteres que especifica quando ocorrem os fins de semana!é uma matriz opcional de um ou mais números de série de datas a serem excluídos do calendário de trabalho, como feriados estaduais e federais e feriados móveis"}, "YEAR": {"a": "(núm_série)", "d": "Retorna o ano de uma data, um número inteiro no intervalo de 1900 a 9999.", "ad": "é um número no código data-hora usado pelo Spreadsheet Editor"}, "YEARFRAC": {"a": "(data_inicial; data_final; [base])", "d": "Retorna a fração do ano que representa o número de dias inteiros entre data_inicial e data_final", "ad": "é o número serial de data que representa a data inicial!é o número serial de data que representa a data final!é o tipo de base de contagem diária a ser utilizada"}, "BESSELI": {"a": "(x; n)", "d": "Retorna a função Bessel modificada ln(x)", "ad": "é o valor com o qual a função será avaliada.!é a ordem da função Bessel"}, "BESSELJ": {"a": "(x; n)", "d": "Retorna a função Bessel Jn(x).", "ad": "é o valor com o qual a função será avaliada!é a ordem da função Bessel"}, "BESSELK": {"a": "(x; n)", "d": "Retorna a função Bessel modificada Kn(x)", "ad": "é o valor com o qual a função será avaliada.!é a ordem da função"}, "BESSELY": {"a": "(x; n)", "d": "Retorna a função Bessel Yn(x)", "ad": "é o valor com o qual a função será avaliada.!é a ordem da função"}, "BIN2DEC": {"a": "(núm)", "d": "Converte um número binário em decimal", "ad": "é o número binário que se deseja converter"}, "BIN2HEX": {"a": "(núm; [casas])", "d": "Converte um número binário em hexadecimal", "ad": "é o número binário que se deseja converter!é o número de caracteres a ser utilizado"}, "BIN2OCT": {"a": "(núm; [casas])", "d": "Converte um número binário em octal", "ad": "é o número binário que se deseja converter!é o número de caracteres a ser utilizado"}, "BITAND": {"a": "(número1; número2)", "d": "Retorna um bit 'E' de dois números", "ad": "é a representação decimal do número binário que você deseja avaliar!é a representação decimal do número binário que você deseja avaliar"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "Retorna um número deslocado à esquerda por shift_amount bits", "ad": "é a representação decimal do número binário que você deseja avaliar!é o número de bits pelos quais você deseja deslocar o Número para a esquerda"}, "BITOR": {"a": "(número1; número2)", "d": "Retorna um bit 'Ou' de dois números", "ad": "é a representação decimal do número binário que você deseja avaliar!é a representação decimal do número binário que você deseja avaliar"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "Retorna um número deslocados à direita por shift_amount bits", "ad": "é a representação decimal do número decimal que você deseja avaliar!é o número de bits pelos quais você deseja deslocar o Número à direita"}, "BITXOR": {"a": "(número1; número2)", "d": "Retorna um bit 'Exclusivo Ou' de dois números", "ad": "é a representação decimal do número binário que você deseja avaliar!é a representação decimal do número binário que você deseja avaliar"}, "COMPLEX": {"a": "(núm_real; i_núm; [sufixo])", "d": "Converte coeficientes reais e imaginários em um número complexo", "ad": "é o coeficiente real do número complexo!é o coeficiente imaginário do número complexo!é o sufixo para o componente imaginário do número complexo"}, "CONVERT": {"a": "(núm; de_unidade; para_unidade)", "d": "Converte um número de um sistema de medidas para outro", "ad": "é o valor em de_unidades a ser convertido!é a unidade do núm!é a unidade do resultado"}, "DEC2BIN": {"a": "(núm; [casas])", "d": "Converte um número decimal em binário", "ad": "é o inteiro decimal que se deseja converter!é o número de caracteres a ser utilizado"}, "DEC2HEX": {"a": "(núm; [casas])", "d": "Converte um número decimal em hexadecimal", "ad": "é o inteiro decimal que se deseja converter!é o número de caracteres a ser utilizado"}, "DEC2OCT": {"a": "(núm; [casas])", "d": "Converte um número decimal em octal", "ad": "é o inteiro decimal que se deseja converter!é o número de caracteres a ser utilizado"}, "DELTA": {"a": "(núm1; [núm2])", "d": "Testa se dois números são iguais", "ad": "é o primeiro número!é o segundo número"}, "ERF": {"a": "(limite_inferior; [limite_superior])", "d": "Retorna a função de erro", "ad": "é o limite inferior na integração de FUNERRO!é o limite superior na integração de FUNERRO"}, "ERF.PRECISE": {"a": "(X)", "d": "Retorna a função de erro", "ad": "é o limite inferior para a integração de FUNERRO.PRECISO"}, "ERFC": {"a": "(x)", "d": "Retorna a função de erro complementar", "ad": "é o limite inferior na integração de FUNERRO"}, "ERFC.PRECISE": {"a": "(x)", "d": "Retorna a função de erro complementar", "ad": "é o limite inferior para a integração de FUNERROCOMPL.PRECISO"}, "GESTEP": {"a": "(núm; [passo])", "d": "Testa se um número é maior que um valor limite", "ad": "é o valor a ser testado em comparação a passo!é o valor limite"}, "HEX2BIN": {"a": "(núm; [casas])", "d": "Converte um número hexadecimal em binário", "ad": "é o número hexadecimal que se deseja converter!é o número de caracteres a ser utilizado"}, "HEX2DEC": {"a": "(núm)", "d": "Converte um número hexadecimal em decimal", "ad": "é o número hexadecimal que se deseja converter"}, "HEX2OCT": {"a": "(núm; [casas])", "d": "Converte um número hexadecimal em octal", "ad": "é o número hexadecimal que se deseja converter!é o número de caracteres a ser utilizado"}, "IMABS": {"a": "(inúm)", "d": "Retorna o valor absoluto (módulo) de um número complexo", "ad": "é um número complexo cujo valor absoluto se deseja obter"}, "IMAGINARY": {"a": "(inúm)", "d": "Retorna o coeficiente imaginário de um número complexo", "ad": "é um número complexo cujo coeficiente imaginário se deseja obter"}, "IMARGUMENT": {"a": "(inúm)", "d": "Retorna o argumento q, um ângulo expresso em radianos", "ad": "é um número complexo cujo argumento se deseja obter"}, "IMCONJUGATE": {"a": "(inúm)", "d": "Retorna o conjugado complexo de um número complexo", "ad": "é um número complexo cujo conjugado se deseja obter"}, "IMCOS": {"a": "(inúm)", "d": "Retorna o cosseno de um número complexo", "ad": "é um número complexo cujo cosseno se deseja obter"}, "IMCOSH": {"a": "(inúmero)", "d": "Retorna o cosseno hiperbólico de um número complexo", "ad": "é um número complexo cujo cosseno hiperbólico você deseja"}, "IMCOT": {"a": "(inúmero)", "d": "Retorna a cotangente de um número complexo", "ad": "é um número complexo para o qual você deseja a cotangente"}, "IMCSC": {"a": "(inúmero)", "d": "Retorna a cossecante de um número complexo", "ad": "é um número complexo para o qual você deseja a cossecante"}, "IMCSCH": {"a": "(inúmero)", "d": "Retorna a hiperbólica da cossecante de um número complexo", "ad": "é um número complexo para o qual você deseja a hiperbólica da cossecante"}, "IMDIV": {"a": "(inúm1; inúm2)", "d": "Retorna o quociente de dois números complexos", "ad": "é o numerador ou dividendo complexo!é o denominador ou divisor complexo"}, "IMEXP": {"a": "(inúm)", "d": "Retorna o exponencial de um número complexo", "ad": "é um número complexo cujo exponencial se deseja obter"}, "IMLN": {"a": "(inúm)", "d": "Retorna o logaritmo natural de um número complexo", "ad": "é um número complexo cujo logaritmo natural se deseja obter"}, "IMLOG10": {"a": "(inúm)", "d": "Retorna o logaritmo de base-10 de um número complexo", "ad": "é um número complexo cujo logaritmo de base-10 se deseja obter"}, "IMLOG2": {"a": "(inúm)", "d": "Retorna o logaritmo de base-2 de um número complexo", "ad": "é um número complexo cujo logaritmo de base-2 se deseja obter"}, "IMPOWER": {"a": "(inúm; núm)", "d": "Retorna um número complexo elevado a uma potência inteira", "ad": "é um número complexo que se deseja elevar a uma potência!é a potência para a qual se deseja elevar o número complexo"}, "IMPRODUCT": {"a": "(inúm1; [inúm2]; ...)", "d": "Retorna o produto de 1 a 255 números complexos", "ad": "Inúm1, Inúm2,... são de 1 a 255 números complexos para multiplicar."}, "IMREAL": {"a": "(inúm)", "d": "Retorna o coeficiente real de um número complexo", "ad": "é um número complexo cujo coeficiente real se deseja obter"}, "IMSEC": {"a": "(inúmero)", "d": "Retorna a secante de um número complexo", "ad": "é um número complexo para o qual você deseja a secante"}, "IMSECH": {"a": "(inúmero)", "d": "Retorna a hiperbólica da secante de um número complexo", "ad": "é um número complexo para o qual você deseja a hiperbólica da secante"}, "IMSIN": {"a": "(inúm)", "d": "Retorna o seno de um número complexo", "ad": "é um número complexo cujo seno se deseja obter"}, "IMSINH": {"a": "(inúmero)", "d": "Retorna o seno hiperbólico de um número complexo", "ad": "é um número complexo do qual você deseja obter o seno hiperbólico"}, "IMSQRT": {"a": "(inúm)", "d": "Retorna a raiz quadrada de um número complexo", "ad": "é um número complexo cuja raiz quadrada se deseja obter"}, "IMSUB": {"a": "(inúm1; inúm2)", "d": "Retorna a diferença de dois números complexos", "ad": "é o número complexo do qual inúm2 será subtraído!é o número complexo a ser subtraído de inúm1"}, "IMSUM": {"a": "(inúm1; [inúm2]; ...)", "d": "Retorna a soma dos números complexos", "ad": "são de 1 a 255 números complexos para adicionar"}, "IMTAN": {"a": "(inúmero)", "d": "Retorna a tangente de um número complexo", "ad": "é um número complexo para o qual você deseja a tangente"}, "OCT2BIN": {"a": "(núm; [casas])", "d": "Converte um número octal em binário", "ad": "é o número octal que se deseja converter!é o número de caracteres a ser utilizado"}, "OCT2DEC": {"a": "(núm)", "d": "Converte um número octal em decimal", "ad": "é o número octal que se deseja converter"}, "OCT2HEX": {"a": "(núm; [casas])", "d": "Converte um número octal em hexadecimal", "ad": "é o número octal que se deseja converter!é o número de caracteres a ser utilizado"}, "DAVERAGE": {"a": "(banco_dados; campo; critérios)", "d": "Calcula a média dos valores em uma coluna de uma lista ou um banco de dados que correspondam às condições especificadas", "ad": "é o intervalo de células que constitui a lista ou o banco de dados. Um banco de dados é uma lista de dados relacionados!é o rótulo da coluna entre aspas ou o número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para a condição"}, "DCOUNT": {"a": "(banco_dados; campo; critérios)", "d": "<PERSON>ta as c<PERSON><PERSON>las contendo números no campo (coluna) de registros no banco de dados que corresponde às condições especificadas", "ad": "é o intervalo de células que constitui a lista ou banco de dados. Um banco de dados é uma lista de dados relacionados!é o rótulo de coluna entre aspas duplas ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DCOUNTA": {"a": "(banco_dados; campo; critérios)", "d": "Conta as células não vazias no campo (coluna) de registros do banco de dados que atendam às condições especificadas", "ad": "é o intervalo de células que constitui a lista ou banco de dados. Um banco de dados é uma lista de dados relacionados!é o rótulo da coluna entre aspas ou o número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para a condição"}, "DGET": {"a": "(banco_dados; campo; critérios)", "d": "Extrai de um banco de dados um único registro que corresponde às condições especificadas", "ad": "é o intervalo de células que constitui a lista ou banco de dados. Um banco de dados é uma lista de dados relacionados!é o rótulo da coluna entre aspas ou o número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DMAX": {"a": "(banco_dados; campo; critérios)", "d": "Retorna o maior número no campo (coluna) de registros do banco de dados que atendam às condições especificadas", "ad": "é o intervalo de células que constitui a lista ou o banco de dados. Um banco de dados é uma lista de dados relacionados!é o rótulo da coluna entre aspas ou o número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para a condição"}, "DMIN": {"a": "(banco_dados; campo; critérios)", "d": "Retorna o menor número no campo (coluna) de registros do banco de dados que atendam às condições especificadas", "ad": "é o intervalo de células que constitui a lista ou banco de dados. Um banco de dados é uma lista de dados relacionados!é o rótulo da coluna entre aspas ou o número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para a condição"}, "DPRODUCT": {"a": "(banco_dados; campo; critérios)", "d": "Multiplica os valores no campo (coluna) de registros do banco de dados que atendam às condições especificadas", "ad": "é o intervalo de células que constitui a lista ou banco de dados. Um banco de dados é uma lista de dados relacionados!é o rótulo da coluna entre aspas ou o número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para a condição"}, "DSTDEV": {"a": "(banco_dados; campo; critérios)", "d": "Estima o desvio padrão com base em uma amostra de entradas selecionadas do banco de dados", "ad": "é o intervalo de células que constitui a lista ou banco de dados. Um banco de dados é uma lista de dados relacionados!é o rótulo da coluna entre aspas ou o número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para a condição"}, "DSTDEVP": {"a": "(banco_dados; campo; critérios)", "d": "Calcula o desvio padrão com base na população total de entradas selecionadas do banco de dados", "ad": "é o intervalo de células que constitui a lista ou banco de dados. Um banco de dados é uma lista de dados relacionados!é o rótulo da coluna entre aspas ou o número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para a condição"}, "DSUM": {"a": "(banco_dados; campo; critérios)", "d": "Soma os números no campo (coluna) de registros no banco de dados que atendam às condições especificadas", "ad": "é o intervalo de células que constitui a lista ou banco de dados. Um banco de dados é uma lista de dados relacionados!é o rótulo da coluna entre aspas ou o número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para a condição"}, "DVAR": {"a": "(banco_dados; campo; critérios)", "d": "Estima a variação com base em uma amostra das entradas selecionadas do banco de dados", "ad": "é o intervalo de células que constitui a lista ou banco de dados. Um banco de dados é uma lista de dados relacionados!é o rótulo da coluna entre aspas ou o número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para a condição"}, "DVARP": {"a": "(banco_dados; campo; critérios)", "d": "Calcula a variação com base na população total de entradas selecionadas de banco de dados", "ad": "é o intervalo de células que constitui a lista ou banco de dados. Um banco de dados é uma lista de dados relacionados!é o rótulo da coluna entre aspas ou o número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para a condição"}, "CHAR": {"a": "(núm)", "d": "Retorna o caractere especificado pelo código numérico do conjunto de caracteres de seu computador", "ad": "é um número entre 1 e 255 que especifica o caractere desejado"}, "CLEAN": {"a": "(texto)", "d": "Remove do texto todos os caracteres não imprimíveis", "ad": "é qualquer informação sobre a planilha de onde você deseja remover os caracteres que não podem ser impressos"}, "CODE": {"a": "(texto)", "d": "Retorna um código numérico para o primeiro caractere de uma cadeia de texto, no conjunto de caracteres usado por seu computador", "ad": "é o texto cujo código do primeiro caractere você deseja obter"}, "CONCATENATE": {"a": "(texto1; [texto2]; ...)", "d": "Agrupa várias cadeias de texto em uma única sequência de texto", "ad": "de 1 a 255 cadeias de texto a serem agrupadas em uma única cadeia, podendo ser cadeias de texto, números ou referências a células únicas"}, "CONCAT": {"a": "(texto1; ...)", "d": "Concatena uma lista ou intervalo de cadeias de texto", "ad": "são de 1 a 254 cadeias de texto ou intervalos a serem agrupadas em uma única cadeia de texto"}, "DOLLAR": {"a": "(núm; [decimais])", "d": "Converte um número em texto, utilizando o formato de moeda", "ad": "é um número, uma referência a uma célula contendo um número, ou uma fórmula que gera um número!é o número de dígitos à direita da vírgula decimal. O número é arredondado conforme necessário; caso não seja especificado, Decimais = 2"}, "EXACT": {"a": "(texto1; texto2)", "d": "Verifica se duas cadeias são exatamente iguais e retorna VERDADEIRO ou FALSO. EXATO diferencia maiúsculas de minúsculas", "ad": "é a primeira cadeia de texto!é a segunda cadeia de texto"}, "FIND": {"a": "(texto_procurado; no_texto; [núm_inicial])", "d": "Retorna a posição inicial de uma cadeia de texto encontrada em outra cadeia de texto. PROCURAR diferencia maiúsculas de minúsculas", "ad": "é o texto que se deseja encontrar. Utilize aspas duplas (texto vazio) para corresponder ao primeiro caractere em no_texto; não é permitido o uso de caracteres curinga!é o texto contendo o texto que se deseja encontrar!especifica o caractere a partir do qual a pesquisa será iniciada. O primeiro caractere em 'No_texto' é o caractere número 1. Quando não especificado, Núm_inicial = 1"}, "FINDB": {"a": "(texto_procurado; no_texto; [núm_inicial])", "d": "Localiza uma cadeia de texto em uma segunda cadeia de texto e retorna o número da posição inicial da primeira cadeia de texto do primeiro caractere da segunda cadeia de texto, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano.", "ad": "é o texto que se deseja encontrar. Utilize aspas duplas (texto vazio) para corresponder ao primeiro caractere em no_texto; não é permitido o uso de caracteres curinga!é o texto contendo o texto que se deseja encontrar!especifica o caractere a partir do qual a pesquisa será iniciada. O primeiro caractere em 'No_texto' é o caractere número 1. Quando não especificado, Núm_inicial = 1"}, "FIXED": {"a": "(núm; [decimais]; [sem_sep_milhar])", "d": "Arredonda um número para o número de casas decimais especificado e retorna o resultado como texto com ou sem separadores de milhares", "ad": "é o número que se deseja arrendondar e converter para texto!é o número de dígitos à direita da vírgula decimal. Quando não especificado, Decimais = 2!é um valor lógico: não exibir separadores de milhares no texto retornado = VERDADEIRO; exibir separadores de milhares no texto retornado = FALSO ou não especificado"}, "LEFT": {"a": "(texto; [núm_caract])", "d": "Retorna o número especificado de caracteres do início de uma cadeia de texto", "ad": "é a cadeia de texto que contém os caracteres que se deseja extrair!especifica quantos caracteres ESQUERDA deve extrair; retorna 1 quando não especificado"}, "LEFTB": {"a": "(texto; [núm_caract])", "d": "Retorna o primeiro caractere ou os primeiros caracteres em uma cadeia de texto com base no número de bytes especificado por você, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano.", "ad": "é a cadeia de texto que contém os caracteres que se deseja extrair!especifica quantos caracteres LEFTB deve extrair; retorna 1 quando não especificado"}, "LEN": {"a": "(texto)", "d": "Retorna o número de caracteres em uma cadeia de texto", "ad": "é o texto cujo tamanho se deseja determinar. Os espaços são considerados caracteres"}, "LENB": {"a": "(texto)", "d": "Retorna o número de bytes usados para representar os caracteres de uma cadeia de texto, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano.", "ad": "é o texto cujo tamanho se deseja determinar. Os espaços são considerados caracteres"}, "LOWER": {"a": "(texto)", "d": "Converte todas as letras em uma cadeia de texto em minúsculas", "ad": "é o texto que se deseja converter em minúsculas. Os caracteres do texto que não são letras não serão alterados"}, "MID": {"a": "(texto; núm_inicial; núm_caract)", "d": "Retorna os caracteres do meio de uma cadeia de texto, tendo a posição e o comprimento especificados", "ad": "é a cadeia de texto que contém os caracteres que se deseja extrair!é a posição do primeiro caractere que se deseja extrair. O primeiro caractere do texto é 1!especifica o número de caracteres a serem retornados do texto"}, "MIDB": {"a": "(texto; núm_inicial; núm_caract)", "d": "Retorna um número específico de caracteres de uma cadeia de texto, começando na posição especificada, com base no número de bytes especificado, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano.", "ad": "é a cadeia de texto que contém os caracteres que se deseja extrair!é a posição do primeiro caractere que se deseja extrair. O primeiro caractere do texto é 1!especifica o número de caracteres a serem retornados do texto"}, "NUMBERVALUE": {"a": "(texto; [separador_decimal]; [separador_grupo])", "d": "Converte texto em número de maneira independente de localidade", "ad": "é a cadeia de caracteres que representa o número que você deseja converter!é o caractere usado como separador decimal na cadeia de caracteres!é o caractere usado como separador de grupo na cadeia de caracteres"}, "PROPER": {"a": "(texto)", "d": "Converte uma cadeia de texto no formato apropriado; a primeira letra de cada palavra em maiúsculas e as demais letras em minúsculas", "ad": "é o texto entre aspas, uma fórmula que retorna o texto ou uma referência a uma célula que contenha o texto o qual você deseja colocar parcialmente em maiúsculas"}, "REPLACE": {"a": "(texto_antigo; núm_inicial; núm_caract; novo_texto)", "d": "Substitui parte de uma cadeia de texto por uma cadeia diferente", "ad": "é o texto no qual se deseja substituir alguns caracteres!é a posição do caractere em 'Texto_antigo' que se deseja substituir por 'Novo_texto'!é o número de caracteres em 'Texto_antigo' que se deseja substituir!é o texto que substituirá caracteres em 'Texto_antigo'"}, "REPLACEB": {"a": "(texto_antigo; núm_inicial; núm_caract; novo_texto)", "d": "Substitui parte de uma cadeia de texto, com base no número de bytes especificado, por uma cadeia de texto diferente, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano.", "ad": "é o texto no qual se deseja substituir alguns caracteres!é a posição do caractere em 'Texto_antigo' que se deseja substituir por 'Novo_texto'!é o número de caracteres em 'Texto_antigo' que se deseja substituir!é o texto que substituirá caracteres em 'Texto_antigo'"}, "REPT": {"a": "(texto; número_vezes)", "d": "Repete o texto um determinado número de vezes. Utilize REPT para preencher uma célula com um número de repetições de uma cadeia", "ad": "é o texto que se deseja repetir!é um número positivo que especifica o número de vezes que texto deve ser repetido"}, "RIGHT": {"a": "(texto; [núm_caract])", "d": "Retorna o número de caracteres especificado do final de uma cadeia de texto", "ad": "é a cadeia de texto que contém os caracteres que se deseja extrair!especifica o número de caracteres que se deseja extrair, 1 quando não especificado"}, "RIGHTB": {"a": "(texto; [núm_caract])", "d": "Retorna o último caractere ou os últimos caracteres em uma cadeia de texto, com base no número de bytes especificado, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano.", "ad": "é a cadeia de texto que contém os caracteres que se deseja extrair!especifica o número de caracteres que se deseja extrair, 1 quando não especificado"}, "SEARCH": {"a": "(texto_procurado; no_texto; [núm_inicial])", "d": "Retorna o número do caractere no qual um caractere ou uma cadeia de texto foram localizados, sendo a leitura feita da esquerda para a direita (não distingue maiúsculas de minúsculas)", "ad": "é o texto que se deseja localizar. Você pode usar os caracteres curinga '?' e '*'. Use '~?' e '~*' para localizar os caracteres '?' e '*'!é o texto no qual se deseja localizar 'Texto_procurado'!é o número do caractere em 'No_texto', a partir da esquerda, no qual se deseja iniciar a pesquisa. Quando não especificado, é usado 1"}, "SEARCHB": {"a": "(texto_procurado; no_texto; [núm_inicial])", "d": "Localiza uma cadeia de texto em uma segunda cadeia de texto e retorna o número da posição inicial da primeira cadeia de texto do primeiro caractere da segunda cadeia de texto, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano.", "ad": "é o texto que se deseja localizar. Você pode usar os caracteres curinga '?' e '*'. Use '~?' e '~*' para localizar os caracteres '?' e '*'!é o texto no qual se deseja localizar 'Texto_procurado'!é o número do caractere em 'No_texto', a partir da esquerda, no qual se deseja iniciar a pesquisa. Quando não especificado, é usado 1"}, "SUBSTITUTE": {"a": "(texto; texto_antigo; novo_texto; [núm_da_ocorrência])", "d": "Substitui um texto antigo por outro novo em uma cadeia de texto", "ad": "é o texto ou a referência a uma célula com texto cujos caracteres você deseja substituir!é o texto que se deseja substituir. SUBSTITUIR não irá substituir textos iguais que não coincidam maiúsculas e minúsculas!é o texto pelo qual você deseja substituir 'Texto_antigo'!especifica qual a ocorrência de 'Texto_antigo' que deve ser substituída por 'Novo_texto'. Quando não especificada, toda instância de 'Texto_antigo' é substituída"}, "T": {"a": "(valor)", "d": "Verifica se um valor é texto e retorna o texto referido em caso afirmativo ou retorna aspas duplas (texto vazio) em caso negativo", "ad": "é o valor que se deseja testar"}, "TEXT": {"a": "(valor; formato_texto)", "d": "Converte um valor em texto com um formato de número específico", "ad": "é um valor numérico, uma fórmula avaliada em um valor numérico, ou uma referência a uma célula que contém um valor numérico!é um formato de número na forma de texto contido na caixa 'Categoria' da guia 'Número' na caixa de diálogo 'Formatar células'"}, "TEXTJOIN": {"a": "(delimitador; ignorar_vazio; texto1; ...)", "d": "Concatena uma lista ou intervalo de cadeias de texto usando um delimitador", "ad": "Caractere ou cadeia de caracteres para inserir entre cada item de texto!se TRUE(padrão), ignora células vazias!são de 1 a 252 cadeias de caracteres de texto ou intervalos a serem unidos"}, "TRIM": {"a": "(texto)", "d": "Remove os espaços de uma cadeia de texto, com exceção dos espaços simples entre palavras", "ad": "é o texto de onde você deseja que os espaços sejam removidos"}, "UNICHAR": {"a": "(número)", "d": "Retorna o caractere Unicode referenciado por um determinado valor numérico", "ad": "é o número de Unicode que representa um caractere"}, "UNICODE": {"a": "(texto)", "d": "Retorna o número (ponto de código) correspondente ao primeiro caractere do texto", "ad": "é o caractere do qual você deseja o valor Unicode"}, "UPPER": {"a": "(texto)", "d": "Converte a cadeia de texto em maiúsculas", "ad": "é o texto que se deseja converter em maiúsculas, uma referência ou uma cadeia de texto"}, "VALUE": {"a": "(texto)", "d": "Converte uma cadeia de texto que representa um número em um número", "ad": "é o texto entre aspas ou a referência a uma célula contendo o texto que se deseja converter"}, "AVEDEV": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a média dos desvios absolutos dos pontos de dados a partir de sua média. Os argumentos podem ser números ou nomes, matrizes ou referências que contenham números", "ad": "de 1 a 255 argumentos cuja média dos desvios absolutos se deseja calcular"}, "AVERAGE": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a média (aritmética) dos argumentos que podem ser números ou nomes, matrizes ou referências que contêm números", "ad": "de 1 255 argumentos numéricos cuja média se deseja obter"}, "AVERAGEA": {"a": "(valor1; [valor2]; ...)", "d": "Retorna a média aritmética dos argumentos, avaliando texto e FALSO em argumentos como 0, VERDADEIRO é avaliado como 1. Os argumentos podem ser números, nomes, matrizes ou referências", "ad": "de 1 a 255 argumentos cuja média você deseja calcular"}, "AVERAGEIF": {"a": "(intervalo; critérios; [intervalo_média])", "d": "Descobre a média aritmética das células especificadas por uma dada condição ou determinados critérios", "ad": "é o intervalo de células que você deseja avaliar!é a condição ou critério na forma de um número, expressão ou texto que define quais células serão usadas para encontrar a média! são as células reais a serem usadas para encontrar a média. Se omitido, as células no intervalo são usadas"}, "AVERAGEIFS": {"a": "(intervalo_média; intervalo_critérios; critérios; ...)", "d": "Descobre a média aritmética das células especificadas por um dado conjunto de condições ou critérios", "ad": "são as células que serão realmente usadas para descobrir a média.!é o intervalo de células que se deseja avaliar para a condição dada!é a condição ou os critérios expressos como um número, uma expressão ou um texto que define quais células serão usadas para calcular a média"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Retorna a função de densidade da probabilidade beta cumulativa", "ad": "é o valor entre A e B no qual se avalia a função!é um parâmetro da distribuição, devendo ser maior do que 0!é um parâmetro da distribuição, devendo ser maior que 0!é um limite inferior opcional para o intervalo de x. Quando não especificado, A = 0!é um limite superior opcional para o intervalo de x. Quando não especificado, B = 1"}, "BETAINV": {"a": "(probabilidade; alfa; beta; [A]; [B])", "d": "Retorna o inverso da função de densidade da probabilidade beta cumulativa (DISTBETA)", "ad": "é a probabilidade associada à distribuição beta!é um parâmetro da distribuição, devendo ser maior que 0!é um parâmetro da distribuição, devendo ser maior que 0!é um limite inferior opcional para o intervalo de x. Quando não especificado, A = 0!é um limite superior opcional para o intervalo de x. Quando não especificado, B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; cumulativo; [A]; [B])", "d": "Retorna a função de distribuição da probabilidade beta", "ad": "é o valor entre A e B no qual se avalia a função!é um parâmetro da distribuição, devendo ser maior que 0!é um parâmetro da distribuição, devendo ser maior que 0!é um valor lógico: para a função de distribuição cumulativa, use VERDADEIRO; para a função de densidade da probabilidade, use FALSO!é um limite inferior opcional para o intervalo de x. Quando não especificado, A = 0!é um limite superior opcional para o intervalo de x. Quando não especificado, B = 1"}, "BETA.INV": {"a": "(probabilidade; alfa; beta; [A]; [B])", "d": "Retorna o inverso da função de densidade da probabilidade beta cumulativa (DIST.BETA)", "ad": "é uma probabilidade associada à distribuição beta!é o parâmetro da distribuição que deve ser maior que 0!é o parâmetro da distribuição que deve ser maior que 0!é o limite inferior opcional para o intervalo de x. Quando não especificado, A = 0!é o limite superior opcional para o intervalo de x. Quando não especificado, B = 1"}, "BINOMDIST": {"a": "(núm_s; tentativas; probabilidade_s; cumulativo)", "d": "Retorna a probabilidade da distribuição binomial do termo individual", "ad": "é o número de tentativas bem-sucedidas!é o número de tentativas independentes!é a probabilidade de sucesso em cada tentativa!é um valor lógico: para a função de distribuição cumulativa, use VERDADEIRO. Para a função de probabilidade de massa, use FALSO"}, "BINOM.DIST": {"a": "(núm_s; tentativas; probabilidade_s; cumulativo)", "d": "Retorna a probabilidade da distribuição binomial do termo individual", "ad": "é o número de tentativas bem sucedidas!é o número de tentativas independentes!é a probabilidade de sucesso em cada tentativa!é um valor lógico: para a função de distribuição cumulativa, use VERDADEIRO. Para a função de probabilidade de massa, use FALSO"}, "BINOM.DIST.RANGE": {"a": "(tentativas; probabilidade_s; número_s; [número_s2])", "d": "Retorna a probabilidade de um resultado de teste usando uma distribuição binomial", "ad": "é o número de tentativas independentes!é a probabilidade de sucesso em cada tentativa!é o número de tentativas bem-sucedidas!se fornecido, esta função retorna a probabilidade de que o número de tentativas bem-sucedidas deverá estar entre núm_s e number_s2"}, "BINOM.INV": {"a": "(tentativas; probabilidade_s; alfa)", "d": "Retorna o menor valor para o qual a distribuição binomial cumulativa é maior ou igual ao valor padrão", "ad": "é o número de tentativas de Bernoulli!é a probabilidade de sucesso em cada tentativa, um número entre 0 e 1 inclusive!é o valor padrão, um número entre 0 e 1 inclusive"}, "CHIDIST": {"a": "(x; graus_liberdade)", "d": "Retorna a probabilidade de cauda direita da distribuição qui-quadrada", "ad": "é o valor no qual se deseja avaliar a distribuição, um número não negativo!é o número de graus de liberdade, um número entre 1 e 10^10, excluindo 10^10"}, "CHIINV": {"a": "(probabilidade; graus_liberdade)", "d": "Retorna o inverso da probabilidade de cauda direita da distribuição qui-quadrada", "ad": "é a probabilidade associada à distribuição qui-quadrada, um valor entre 0 e 1 inclusive!é o número de graus de liberdade, um número entre 1 e 10^10, excluindo 10^10"}, "CHITEST": {"a": "(intervalo_real; intervalo_esperado)", "d": "Retorna o teste para independência: o valor da distribuição qui-quadrada para a estatística e o grau apropriado de liberdade", "ad": "é o intervalo de dados que contém observações para serem comparadas com os valores esperados!é o intervalo de dados que contém a proporção do produto entre os totais de linhas e os totais de colunas e o total geral"}, "CHISQ.DIST": {"a": "(x; graus_liberdade; cumulativo)", "d": "Retorna a probabilidade de cauda esquerda da distribuição qui-quadrada", "ad": "é o valor no qual se deseja avaliar a distribuição, um número não negativo!é o número de graus de liberdade, um número entre 1 e 10^10, excluindo 10^10!é um valor lógico a ser retornado pela função: a função de distribuição cumulativa = VERDADEIRO, a função de densidade da probabilidade = FALSO"}, "CHISQ.DIST.RT": {"a": "(x; graus_liberdade)", "d": "Retorna a probabilidade de cauda direita da distribuição qui-quadrada", "ad": "é o valor no qual se deseja avaliar a distribuição, um número não negativo!é o número de graus de liberdade, um número entre 1 e 10^10, excluindo 10^10"}, "CHISQ.INV": {"a": "(probabilidade; graus_liberdade)", "d": "Retorna o inverso da probabilidade de cauda esquerda da distribuição qui-quadrada", "ad": "é a probabilidade associada à distribuição qui-quadrada, um valor entre 0 e 1 inclusive!é o número de graus de liberdade, um número entre 1 e 10^10, excluindo 10^10"}, "CHISQ.INV.RT": {"a": "(probabilidade; graus_liberdade)", "d": "Retorna o inverso da probabilidade de cauda direita da distribuição qui-quadrada", "ad": "é a probabilidade associada à distribuição qui-quadrada, um valor entre 0 e 1 inclusive!é o número de graus de liberdade, um número entre 1 e 10^10, excluindo 10^10"}, "CHISQ.TEST": {"a": "(intervalo_real; intervalo_esperado)", "d": "Retorna o teste para independência: o valor da distribuição qui-quadrada para a estatística e o grau apropriado de liberdade", "ad": "é o intervalo de dados que contém observações para serem comparadas com os valores esperados!é o intervalo de dados que contém a proporção entre o produto dos totais de linhas e dos totais de colunas e o total geral"}, "CONFIDENCE": {"a": "(alfa; desv_padrão; tamanho)", "d": "Retorna o intervalo de confiança para uma média da população", "ad": "é o nível de significância utilizado para calcular o nível de confiança, um número maior que 0 e menor que 1!é o desvio padrão da população para o intervalo de dados, e presume-se conhecido. Desv_padrão deve ser maior que 0!é o tamanho da amostra"}, "CONFIDENCE.NORM": {"a": "(alfa; desv_padrão; tamanho)", "d": "Retorna o intervalo de confiança para uma média da população, usando uma distribuição normal", "ad": "é o nível de significância utilizado para calcular o nível de confiança, um número maior que 0 e menor que 1!é o desvio padrão da população para o intervalo de dados, e presume-se conhecido. Desv_padrão deve ser maior que 0!é o tamanho da amostra"}, "CONFIDENCE.T": {"a": "(alfa; desv_padrão; tamanho)", "d": "Retorna o intervalo de confiança para uma média da população, usando uma distribuição T de Student", "ad": "é o nível de significância utilizado para calcular o nível de confiança, um número maior que 0 e menor que 1!é o desvio padrão da população para o intervalo de dados, e presume-se conhecido. Desv_padrão deve ser maior que 0!é o tamanho da amostra"}, "CORREL": {"a": "(matriz1; matriz2)", "d": "Retorna o coeficiente de correlação entre dois conjuntos de dados", "ad": "é um intervalo de células de valores. Os valores devem ser números, nomes, matrizes ou referências que contenham números!é um segundo intervalo de células de valores. Os valores devem ser números, nomes, matrizes ou referências que contenham números"}, "COUNT": {"a": "(valor1; [valor2]; ...)", "d": "Calcula o número de células em um intervalo que contém números", "ad": "de 1 a 255 argumentos que podem conter ou referir-se a diversos tipos de dados, mas somente os números são contados"}, "COUNTA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula o número de células em um intervalo que não estão vazias", "ad": "de 1 a 255 argumentos que representam os valores e as células que deseja contar. Valores podem ser quaisquer tipos de informação"}, "COUNTBLANK": {"a": "(intervalo)", "d": "Conta o número de células vazias em um intervalo de células especificado", "ad": "é o intervalo a partir do qual se deseja contar as c<PERSON><PERSON><PERSON> vazias"}, "COUNTIF": {"a": "(intervalo; critérios)", "d": "Calcula o número de células não vazias em um intervalo que corresponde a uma determinada condição", "ad": "é o intervalo de células no qual se deseja contar células que não estão em branco!é a condição, na forma de um número, expressão ou texto, que define quais células serão contadas"}, "COUNTIFS": {"a": "(intervalo_critérios; critérios; ...)", "d": "Conta o número de células especificadas por um dado conjunto de condições ou critérios", "ad": "é o intervalo de células que se deseja avaliar para a condição determinada!é a condição expressa como um número, uma expressão ou um texto que define quais células serão contadas"}, "COVAR": {"a": "(matriz1; matriz2)", "d": "Retorna a covariação, a média dos produtos dos desvios de cada par de pontos de dados em dois conjuntos de dados", "ad": "é o primeiro intervalo de células de inteiros, devendo ser números, matrizes ou referências que contenham números!é o segundo intervalo de células de inteiros, devendo ser números, matrizes ou referências que contenham números"}, "COVARIANCE.P": {"a": "(matriz1; matriz2)", "d": "Retorna a covariação da população, a média dos produtos dos desvios para cada par de pontos de dados em dois conjuntos de dados", "ad": "é o primeiro intervalo de células de inteiros, devendo ser números, matrizes ou referências que contenham números!é o segundo intervalo de células de inteiros, devendo ser números, matrizes ou referências que contenham números"}, "COVARIANCE.S": {"a": "(matriz1; matriz2)", "d": "Retorna a covariação da amostra, a média dos produtos dos desvios de cada par de pontos de dados em dois conjuntos de dados", "ad": "é o primeiro intervalo de células de inteiros, devendo ser números, matrizes ou referências que contenham números!é o segundo intervalo de células de inteiros, devendo ser números, matrizes ou referências que contenham números"}, "CRITBINOM": {"a": "(tentativas; probabilidade_s; alfa)", "d": "Retorna o menor valor para o qual a distribuição binomial cumulativa é maior ou igual ao valor padrão", "ad": "é o número de tentativas de Bernoulli!é a probabilidade de sucesso em cada tentativa, um número entre 0 e 1 inclusive!é o valor padrão, um número entre 0 e 1 inclusive"}, "DEVSQ": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a soma dos quadrados dos desvios dos pontos de dados da média de suas amostras", "ad": "de 1 a 255 argumentos, ou uma matriz ou referência de matriz, da qual se deseja calcular DEVSQ"}, "EXPONDIST": {"a": "(x; lambda; cumulativo)", "d": "Retorna a distribuição exponencial", "ad": "é o valor da função, um número não negativo!é o valor do parâmetro, um número positivo!é um valor lógico a ser retornado pela função: a função de distribuição cumulativa = VERDADEIRO, a função de densidade da probabilidade = FALSO"}, "EXPON.DIST": {"a": "(x; lambda; cumulativo)", "d": "Retorna a distribuição exponencial", "ad": "é o valor da função, um número não negativo!é o valor do parâmetro, um número positivo!é um valor lógico a ser retornado pela função: a função de distribuição cumulativa = VERDADEIRO, a função de densidade da probabilidade = FALSO"}, "FDIST": {"a": "(x; graus_liberdade1; graus_liberdade2)", "d": "Retorna a distribuição (grau de diversidade) de probabilidade F (cauda direita) para dois conjuntos de dados", "ad": "é o valor no qual se avalia a função, um número não negativo!é o grau de liberdade do numerador, um número entre 1 e 10^10, excluindo 10^10!é o grau de liberdade do denominador, um número entre 1 e 10^10, excluindo 10^10"}, "FINV": {"a": "(probabilidade; graus_liberdade1; graus_liberdade2)", "d": "Retorna o inverso da distribuição de probabilidades F (cauda direita): se p = DISTF(x,...), então INVF(p,...) = x", "ad": "é a probabilidade associada à distribuição cumulativa F, um número entre 0 e 1 inclusive!é o grau de liberdade do numerador, um número entre 1 e 10^10, excluindo 10^10!é o grau de liberdade do denominador, um número entre 1 e 10^10, excluindo 10^10"}, "FTEST": {"a": "(matriz1; matriz2)", "d": "Retorna o resultado de um teste F, a probabilidade bicaudal de que as variações em Matriz1 e Matriz2 não sejam significativamente diferentes", "ad": "é a primeira matriz ou intervalo de dados, podendo ser números ou nomes, matrizes ou referências que contenham números (os espaços em branco são ignorados)!é a segunda matriz ou intervalo de dados, podendo ser números ou nomes, matrizes ou referências que contenham números (os espaços em branco são ignorados)"}, "F.DIST": {"a": "(x; graus_liberdade1; graus_liberdade2; cumulativo)", "d": "Retorna a distribuição (grau de diversidade) de probabilidade F (cauda esquerda) para dois conjuntos de dados", "ad": "é o valor no qual se avalia a função, um número não negativo!é o grau de liberdade do numerador, um número entre 1 e 10^10, excluindo 10^10!é o grau de liberdade do denominador, um número entre 1 e 10^10, excluindo 10^10!é um valor lógico a ser retornado pela função: a função de distribuição cumulativa = VERDADEIRO, a função de densidade da probabilidade = FALSO"}, "F.DIST.RT": {"a": "(x; graus_liberdade1; graus_liberdade2)", "d": "Retorna a distribuição (grau de diversidade) de probabilidade F (cauda direita) para dois conjuntos de dados", "ad": "é o valor no qual se avalia a função, um número não negativo!é o grau de liberdade do numerador, um número entre 1 e 10^10, excluindo 10^10!é o grau de liberdade do denominador, um número entre 1 e 10^10, excluindo 10^10"}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Retorna o inverso da distribuição de probabilidade F (cauda esquerda): se p = DISTF(x,...), então INV.F(p,...) = x", "ad": "é a probabilidade associada à distribuição cumulativa F, um número entre 0 e 1 inclusive!é o grau de liberdade do numerador, um número entre 1 e 10^10, excluindo 10^10!é o grau de liberdade do denominador, um número entre 1 e 10^10, excluindo 10^10"}, "F.INV.RT": {"a": "(probabilidade; graus_liberdade1; graus_liberdade2)", "d": "Retorna o inverso da distribuição de probabilidade F (cauda direita): se p = DIST.F.CD(x,...), então INV.F.CD(p,...) = x", "ad": "é a probabilidade associada à distribuição cumulativa F, um número entre 0 e 1 inclusive!é o grau de liberdade do numerador, um número entre 1 e 10^10, excluindo 10^10!é o grau de liberdade do denominador, um número entre 1 e 10^10, excluindo 10^10"}, "F.TEST": {"a": "(matriz1; matriz2)", "d": "Retorna o resultado de um teste F, a probabilidade bicaudal de que as variações em Matriz1 e Matriz2 não sejam significativamente diferentes", "ad": "é a primeira matriz ou intervalo de dados, podendo ser números ou nomes, matrizes ou referências que contenham números (os espaços em branco são ignorados)!é a segunda matriz ou intervalo de dados, podendo ser números ou nomes, matrizes ou referências que contenham números (os espaços em branco são ignorados)"}, "FISHER": {"a": "(x)", "d": "Retorna a transformação Fisher", "ad": "é um valor no qual se deseja a transformação, um número entre -1 e 1, excluindo -1 e 1"}, "FISHERINV": {"a": "(y)", "d": "Retorna o inverso da transformação Fisher: se y = FISHER(x), então FISHERINV(y) = x", "ad": "é o valor para o qual se deseja executar o inverso da transformação"}, "FORECAST": {"a": "(x; known_ys; known_xs)", "d": "Calcula ou prevê um valor futuro ao longo de uma tendência linear usando valores existentes", "ad": "é o ponto de dados para o qual você deseja prever um valor e deve ser um valor numérico!é o intervalo de dados numéricos ou matriz dependente!é o intervalo de dados numéricos ou matriz independente. A variação de Known_x's não pode ser igual a zero"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "Retorna o valor previsto para uma data de destino futura específica usando o método de suavização exponencial.", "ad": "é o ponto de dados para o qual o Spreadsheet Editor prevê um valor. Ele deve executar o padrão de valores na linha do tempo.!é a matriz ou o intervalo de dados numéricos que você está prevendo.!é a matriz ou o intervalo de dados numéricos independente. As datas na linha do tempo devem ter uma etapa consistente entre elas e não podem ser zero.!é um valor numérico opcional que indica o comprimento do padrão sazonal. O valor padrão 1 indica que a sazonalidade é detectada automaticamente.!é um valor opcional para lidar com valores ausentes. O valor padrão 1 substitui os valores ausentes por interpolação e 0 os substitui por zeros.!é um valor numérico opcional para agregar vários valores com o mesmo carimbo de data/hora. Caso estejam em branco, Spreadsheet Editor calculará a média dos valores."}, "FORECAST.ETS.CONFINT": {"a": "(data_alvo; valores; linha_cronológica; [nível_de_confiança]; [sazonalidade]; [conclusão_dos_dados]; [agregação])", "d": "Retorna um intervalo de confiança para o valor de previsão na data de destino especificada.", "ad": "é o ponto de dados para o qual o Spreadsheet Editor prevê um valor. Ele deve executar o padrão de valores na linha do tempo.!é a matriz ou o intervalo de dados numéricos que você está prevendo.!é a matriz ou o intervalo de dados numéricos independente. As datas na linha do tempo devem ter uma etapa consistente entre elas e não podem ser zero.!é um número entre 0 e 1 que indica o nível de confiança do intervalo de confiança calculado. O valor padrão é .95!é um valor numérico opcional que indica o comprimento do padrão sazonal. O valor padrão 1 indica que a sazonalidade é detectada automaticamente.!é um valor opcional para lidar com valores ausentes. O valor padrão 1 substitui os valores ausentes por interpolação e 0 os substitui por zeros.!é um valor numérico opcional para agregar vários valores com o mesmo carimbo de data/hora. Caso estejam em branco, Spreadsheet Editor calculará a média dos valores."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Retorna o comprimento do padrão repetitivo que um aplicativo detecta para a série de tempo especificada.", "ad": "é a matriz ou o intervalo de dados numéricos que você está prevendo.!é o intervalo de dados numéricos ou matriz independente. As datas na linha do tempo devem ter uma etapa consistente entre elas e ser diferentes de zero.!é um valor opcional para manipular valores ausentes. O valor padrão 1 substitui os valores ausentes por interpolação e 0 os substitui por zeros.!é um valor numérico opcional para agregar vários valores com o mesmo carimbo de data/hora. Caso estejam em branco, Spreadsheet Editor calculará a média dos valores."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "Retorna a estatística solicitada para a previsão.", "ad": "é a matriz ou intervalo de dados numéricos que você está prevendo.!é a matriz ou intervalo independente de dados numéricos. As datas na linha do tempo devem ter uma etapa consistente entre elas e não podem ser zero.!é um número entre 1 e 8, indicando qual estatística Spreadsheet Editor retornará para a previsão calculada.!é um valor numérico opcional que indica a duração do padrão sazonal. O valor padrão de 1 indica que a sazonalidade é detectada automaticamente.!é um valor opcional para lidar com valores ausentes. O valor padrão de 1 substitui os valores ausentes por interpolação e 0 os substitui por zeros.!é um valor numérico opcional para agregar vários valores com o mesmo carimbo de data/hora. Se estiver em branco, Spreadsheet Editor calcula a média dos valores."}, "FORECAST.LINEAR": {"a": "(x; known_ys; known_xs)", "d": "Calcula ou prevê um valor futuro ao longo de uma tendência linear usando valores existentes", "ad": "é o ponto de dados para o qual você deseja prever um valor e deve ser um valor numérico!é o intervalo de dados numéricos ou a matriz dependente!é o intervalo de dados numéricos ou a matriz independente. A variação de Known_x's não pode ser igual a zero"}, "FREQUENCY": {"a": "(matriz_dados; matriz_bin)", "d": "Calcula a frequência de ocorrência de valores em um intervalo de valores e retorna uma matriz vertical de números contendo um elemento a mais do que 'Matriz_bin'", "ad": "é uma matriz de ou uma referência a um conjunto de valores cujas frequências você deseja contar (espaços em branco e textos são ignorados)!é uma matriz de ou referência a intervalos nos quais você deseja agrupar os valores contidos em 'Matriz_dados'"}, "GAMMA": {"a": "(x)", "d": "Retorna o valor da função Gama", "ad": "é o valor para o qual você deseja calcular a Gama"}, "GAMMADIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Retorna a distribuição gama", "ad": "é o valor no qual se deseja avaliar a distribuição, um número não negativo!é o parâmetro da distribuição, um número positivo!é um parâmetro da distribuição, um número positivo. Se beta = 1, DISTGAMA retorna a distribuição gama padrão!é um valor lógico: retornar a função de distribuição cumulativa = VERDADEIRO, retornar a função de probabilidade de massa = FALSO ou não especificado"}, "GAMMA.DIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Retorna a distribuição gama", "ad": "é o valor no qual se deseja avaliar a distribuição, um número não negativo!é o parâmetro da distribuição, um número positivo!é o parâmetro da distribuição, um número positivo. Se beta = 1, DIST.GAMA retorna a distribuição gama padrão!é um valor lógico: retornar a função de distribuição cumulativa = VERDADEIRO, retornar a função de probabilidade de massa = FALSO ou não especificado"}, "GAMMAINV": {"a": "(probabilidade; alfa; beta)", "d": "Retorna o inverso da distribuição cumulativa gama: se p = DISTGAMA(x,...), então INVGAMA(p,...) = x", "ad": "é a probabilidade associada à distribuição gama, um número entre 0 e 1 inclusive!é o parâmetro da distribuição, um número positivo!é um parâmetro da distribuição, um número positivo. Se beta = 1, INVGAMA retorna o inverso da distribuição gama padrão"}, "GAMMA.INV": {"a": "(probabilidade; alfa; beta)", "d": "Retorna o inverso da distribuição cumulativa gama: se p = DIST.GAMA(x,...), então INV.GAMA(p,...) = x", "ad": "é a probabilidade associada à distribuição gama, um número entre 0 e 1 inclusive!é o parâmetro da distribuição, um número positivo!é o parâmetro para a distribuição, um número positivo. Se beta = 1, INV.GAMA retorna o inverso da distribuição gama padrão"}, "GAMMALN": {"a": "(x)", "d": "Retorna o logaritmo natural da função gama", "ad": "é o valor para o qual você deseja calcular LNGAMA, um número positivo"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Retorna o logaritmo natural da função gama", "ad": "é o valor pelo qual você deseja calcular LNGAMA.PRECISO, um número positivo"}, "GAUSS": {"a": "(x)", "d": "Retorna 0,5 menos que a distribuição cumulativa normal padrão", "ad": "é o valor cuja distribuição você deseja obter"}, "GEOMEAN": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a média geométrica de uma matriz ou um intervalo de dados numéricos positivos", "ad": "de 1 a 255 números ou nomes, matrizes ou referências que contenham números cuja média você deseja calcular"}, "GROWTH": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Retorna números em uma tendência de crescimento exponencial que corresponda a pontos de dados conhecidos", "ad": "é o conjunto de valores de y já conhecidos na relação y = b*m^x, uma matriz ou intervalo de números positivos!é um conjunto opcional de valores de x que já deve ser conhecido na relação y = b*m^x, uma matriz ou intervalo de mesmo tamanho que Known_y's!são novos valores de x para os quais se deseja que CRESCIMENTO retorne valores y correspondentes!é um valor lógico: a constante b é calculada normalmente se Const = VERDADEIRA; b é definido como 1 se Const = FALSA ou não especificada"}, "HARMEAN": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a média harmônica de um conjunto de dados de números positivos: o recíproco da média aritmética de recíprocos", "ad": "de 1 a 255 números ou nomes, matrizes ou referências que contenham números cuja média harmônica você deseja calcular"}, "HYPGEOM.DIST": {"a": "(exemplo_s; exemplo_núm; população_s; núm_população; cumulativo)", "d": "Retorna a distribuição hipergeométrica", "ad": "é o número de sucessos em uma amostra!é o tamanho da amostra!é o número de sucessos na população!é o tamanho da população!é um valor lógico: para a função de distribuição cumulativa, use VERDADEIRO; para a função de densidade da probabilidade, use FALSO"}, "HYPGEOMDIST": {"a": "(exemplo_s; exemplo_núm; população_s; núm_população)", "d": "Retorna a distribuição hipergeométrica", "ad": "é o número de sucessos em uma amostra!é o tamanho da amostra!é o número de sucessos na população!é o tamanho da população"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "Calcula o ponto em que uma linha interceptará o eixo y usando uma linha de regressão de melhor ajuste plotada através dos valores de x e valores de y conhecidos", "ad": "é o conjunto dependente de observações ou dados, podendo ser números ou nomes, matrizes ou referências que contenham números!é o conjunto independente de observações ou dados, podendo ser números ou nomes, matrizes ou referências que contenham números"}, "KURT": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a curtose de um conjunto de dados", "ad": "de 1 a 255 números ou nomes, matrizes ou referências que contenham números cuja curtose você deseja obter"}, "LARGE": {"a": "(matriz; k)", "d": "Retorna o maior valor k-ésimo de um conjunto de dados. Por exemplo, o quinto maior número", "ad": "é a matriz ou intervalo de dados cujo maior valor k-ésimo você deseja determinar!é a posição (começando do maior) na matriz ou intervalo de células do valor a ser retornado"}, "LINEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Retorna a estatística que descreve a tendência linear que corresponda aos pontos de dados, ajustando uma linha reta através do método de quadrados mínimos", "ad": "é o conjunto de valores de y já conhecidos na relação y = mx + b!é um conjunto opcional de valores x que já deve ser conhecido na relação y = mx + b!é um valor lógico: a constante b é calculada normalmente se Const = VERDADEIRA ou não especificada; \"b\" é definido como 0 se Const = FALSA!é um valor lógico: retorna estatística de regressão adicional = VERDADEIRA; retorna coeficientes-m e a constante b = FALSA ou não especificada"}, "LOGEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Retorna a estatística que descreve uma curva exponencial que corresponda a pontos de dados conhecidos", "ad": "é o conjunto de valores de y já conhecidos na relação y = b*m^x!é um conjunto opcional de valores x que já deve ser conhecido na relação y = b*m^x!é um valor lógico: a constante b é calculada normalmente se Const = VERDADEIRA ou não especificada; b é definido como 1 se Const = FALSA!é um valor lógico: retorna estatística de regressão adicional = VERDADEIRA; retorna coeficientes-m e a constante b = FALSA ou não especificada"}, "LOGINV": {"a": "(probabilidade; média; desv_padrão)", "d": "Retorna o inverso da função de distribuição log-normal cumulativa de x, onde ln(x) é normalmente distribuído com parâmetros Média e Dev_padrão", "ad": "é a probabilidade associada à distribuição log-normal, um número entre 0 e 1 inclusive!é a média do ln(x)!é o desvio padrão do ln(x), um número positivo"}, "LOGNORM.DIST": {"a": "(x; média; desv_padrão; cumulativo)", "d": "Retorna a distribuição log-normal de x, onde ln(x) é normalmente distribuído com parâmetros Média e Desv_padrão", "ad": "é o valor no qual se avalia a função, um número positivo!é a média do ln(x)!é o desvio padrão do ln(x), um número positivo!é um valor lógico: para a função de distribuição cumulativa, use VERDADEIRO; para a função de densidade da probabilidade, use FALSO"}, "LOGNORM.INV": {"a": "(probabilidade; média; desv_padrão)", "d": "Retorna o inverso da função de distribuição cumulativa log-normal de x, onde ln(x) é normalmente distribuído com parâmetros Média e Desv_padrão", "ad": "é a probabilidade associada à distribuição log-normal, um número entre 0 e 1 inclusive!é a média do ln(x)!é o desvio padrão do ln(x), um número positivo"}, "LOGNORMDIST": {"a": "(x; média; desv_padrão)", "d": "Retorna a distribuição log-normal cumulativa de x, onde ln(x) é normalmente distribuído com parâmetros Média e Desv_padrão", "ad": "é o valor no qual se avalia a função, um número positivo!é a média do ln(x)!é o desvio padrão do ln(x), um número positivo"}, "MAX": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o valor máximo de um conjunto de argumentos. Valores lógicos e texto são ignorados", "ad": "de 1 a 255 números, c<PERSON><PERSON><PERSON> vazias, valores lógicos ou números em forma de texto cujo valor máximo você deseja obter"}, "MAXA": {"a": "(valor1; [valor2]; ...)", "d": "Retorna o valor máximo contido em um conjunto de valores. Não ignora valores lógicos e texto", "ad": "de 1 a 255 números, c<PERSON><PERSON><PERSON> vazias, valores lógicos ou números em formato de texto cujo valor máximo você deseja obter"}, "MAXIFS": {"a": "(intervalo_máximo; intervalo_critérios; critérios; ...)", "d": "Retorna o valor máximo entre células especificadas por um dado conjunto de condições ou critérios", "ad": "as células nas quais determinar o valor máximo!é o intervalo de células que você deseja avaliar para o determinado condição!é a condição ou critério na forma de um número, expressão ou texto que define quais células serão incluídas ao determinar o valor máximo"}, "MEDIAN": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a mediana, ou o número central de um determinado conjunto de números", "ad": "de 1 a 255 números ou nomes, matrizes ou referências que contêm números cuja mediana você deseja obter"}, "MIN": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o valor mínimo contido em um conjunto de valores. Texto e valores lógicos são ignorados", "ad": "de 1 a 255 números, c<PERSON><PERSON><PERSON> vazias, valores lógicos ou números em forma de texto cujo valor mínimo você deseja obter"}, "MINA": {"a": "(valor1; [valor2]; ...)", "d": "Retorna o valor mínimo contido em um conjunto de valores. Não ignora valores lógicos e texto", "ad": "de 1 a 255 números, c<PERSON><PERSON><PERSON> vazias, valores lógicos ou números em formato de texto cujo valor mínimo você deseja obter"}, "MINIFS": {"a": "(intervalo_mínimo; critério_intervalo; critérios; ...)", "d": "Retorna o valor mínimo entre células especificadas por um dado conjunto de condições ou critérios", "ad": "as células nas quais determinar o valor mínimo!é o intervalo de células que você deseja avaliar para o determinado condição!é a condição ou critério na forma de um número, expressão ou texto que define quais células serão incluídas ao determinar o valor mínimo"}, "MODE": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o valor mais repetitivo ou que ocorre com maior frequência, em uma matriz ou um intervalo de dados", "ad": "de 1 a 255 números ou nomes, matrizes ou referências que contenham números cujo modo você deseje obter"}, "MODE.MULT": {"a": "(núm1; [núm2]; ...)", "d": "Retorna uma matriz vertical dos valores mais repetidos, ou que ocorrem com maior frequência, em uma matriz ou um intervalo de dados. Para a matriz horizontal, use =TRANSPOR(MODO.MULT(número1,número2,...))", "ad": "de 1 a 255 números ou nomes, matrizes ou referências que contenham números cujo modo você deseja obter"}, "MODE.SNGL": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o valor mais repetido, ou que ocorre com maior frequência, em uma matriz ou um intervalo de dados", "ad": "de 1 a 255 números ou nomes, matrizes ou referências que contenham números cujo modo você deseja obter"}, "NEGBINOM.DIST": {"a": "(núm_f; núm_s; probabilidade_s; cumulativo)", "d": "Retorna a distribuição binomial negativa, a probabilidade de que ocorrerá Núm_f de falhas antes de Núm_s de sucessos, com probabilidade Probabilidade_s de um sucesso", "ad": "é o número de falhas!é o número a partir do qual se considera haver sucesso!é a probabilidade de sucesso, um número entre 0 e 1!é um valor lógico: para a função de distribuição cumulativa, use VERDADEIRO; para a função de probabilidade de massa, use FALSO"}, "NEGBINOMDIST": {"a": "(núm_f; núm_s; probabilidade_s)", "d": "Retorna a distribuição binomial negativa, a probabilidade de que ocorrerá Núm_f de falhas antes de Núm_s de sucessos, com probabilidade Probabilidade_s de um sucesso", "ad": "é o número de falhas!é o número a partir do qual se considera haver sucesso!é a probabilidade de sucesso, um número entre 0 e 1"}, "NORM.DIST": {"a": "(x; média; desv_padrão; cumulativo)", "d": "Retorna a distribuição normal da média e do desvio padrão especificados", "ad": "é o valor cuja distribuição você deseja obter!é a média aritmética da distribuição!é o desvio padrão da distribuição, um número positivo!é um valor lógico: para a função de distribuição cumulativa, use VERDADEIRO; para a função de densidade da probabilidade, use FALSO"}, "NORMDIST": {"a": "(x; média; desv_padrão; cumulativo)", "d": "Retorna a distribuição cumulativa normal para um desvio padrão e média especificada", "ad": "é o valor cuja distribuição você deseja obter!é a média aritmética da distribuição!é o desvio padrão da distribuição, um número positivo!é um valor lógico: para a função de distribuição cumulativa, use VERDADEIRO. Para a função de densidade da probabilidade, use FALSO"}, "NORM.INV": {"a": "(probabilidade; média; desv_padrão)", "d": "Retorna o inverso da distribuição cumulativa normal para a média e o desvio padrão especificados", "ad": "é uma probabilidade correspondente à distribuição normal, um número entre 0 e 1 inclusive!é a média aritmética da distribuição!é o desvio padrão da distribuição, um número positivo"}, "NORMINV": {"a": "(probabilidade; média; desv_padrão)", "d": "Retorna o inverso da distribuição cumulativa normal para a média e o desvio padrão especificados", "ad": "é uma probabilidade correspondente à distribuição normal, um número entre 0 e 1 inclusive!é a média aritmética da distribuição!é o desvio padrão da distribuição, um número positivo"}, "NORM.S.DIST": {"a": "(z; cumulativo)", "d": "Retorna a distribuição normal padrão (possui uma média zero e um desvio padrão 1)", "ad": "é o valor cuja distribuição você deseja obter!é um valor lógico a ser retornado pela função: a função de distribuição cumulativa = VERDADEIRO, a função de densidade da probabilidade = FALSO"}, "NORMSDIST": {"a": "(z)", "d": "Retorna a distribuição cumulativa normal padrão (possui uma média zero e um desvio padrão 1)", "ad": "é o valor cuja distribuição você deseja obter"}, "NORM.S.INV": {"a": "(probabilidade)", "d": "Retorna o inverso da distribuição cumulativa normal padrão (possui uma média zero e um desvio padrão 1)", "ad": "é uma probabilidade correspondente à distribuição normal, um número entre 0 e 1 inclusive"}, "NORMSINV": {"a": "(probabilidade)", "d": "Retorna o inverso da distribuição cumulativa normal padrão (possui uma média zero e um desvio padrão 1)", "ad": "é uma probabilidade correspondente à distribuição normal, um número entre 0 e 1 inclusive"}, "PEARSON": {"a": "(matriz1; matriz2)", "d": "Retorna o coeficiente de correlação do momento do produto Pearson, r", "ad": "é um conjunto de valores independentes!é um conjunto de valores dependentes"}, "PERCENTILE": {"a": "(matriz; k)", "d": "Retorna o k-ésimo percentil de valores em um intervalo", "ad": "é a matriz ou o intervalo de dados que define a posição relativa!é o valor do percentil entre 0 e 1, inclusive"}, "PERCENTILE.EXC": {"a": "(matriz; k)", "d": "Retorna o k-ésimo percentil de valores em um intervalo, em que k está no intervalo 0..1, exclusive", "ad": "é a matriz ou intervalo de dados que define a posição relativa!é o valor do percentil no intervalo 0 a 1, inclusive"}, "PERCENTILE.INC": {"a": "(matriz; k)", "d": "Retorna o k-ésimo percentil de valores em um intervalo, em que k está no intervalo 0..1, inclusive", "ad": "é a matriz ou intervalo de dados que define a posição relativa!é o valor do percentil no intervalo 0 a 1, inclusive"}, "PERCENTRANK": {"a": "(matriz; x; [significância])", "d": "Retorna a posição de um valor em um conjunto de dados na forma de uma porcentagem do conjunto de dados", "ad": "é a matriz ou o intervalo de dados com valores numéricos que define uma posição relativa!é o valor cuja posição você deseja saber!é um valor opcional que identifica o número de dígitos significativos para a porcentagem retornada, três dígitos quando não especificado (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(matriz; x; [significância])", "d": "Retorna a posição de um valor em um conjunto de dados como uma porcentagem do conjunto de dados (0..1, exclusive)", "ad": "é a matriz ou intervalo de dados com valores numéricos que define uma posição relativa!é o valor cuja posição você quer saber!é um valor opcional que identifica o número de dígitos significativos para a porcentagem retornada, três dígitos, quando não especificado (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(matriz; x; [significância])", "d": "Retorna a posição de um valor em um conjunto de dados como uma porcentagem do conjunto de dados (0..1, inclusive)", "ad": "é a matriz ou intervalo de dados com valores numéricos que define uma posição relativa!é o valor cuja posição você quer saber!é um valor opcional que identifica o número de dígitos significativos para a porcentagem retornada, três dígitos, quando não especificado (0,xxx%)"}, "PERMUT": {"a": "(núm; núm_escolhido)", "d": "Retorna o número de permutações para um dado número de objetos que podem ser selecionados do total de objetos", "ad": "é o número que descreve o número de objetos!é o número de objetos em cada permutação"}, "PERMUTATIONA": {"a": "(número; núm_escolhido)", "d": "Retorna o número de permutações para um determinado número de objetos (com repetições) que podem ser selecionados do total de objetos", "ad": "é o número total de objetos!é o número de objetos em cada permutação"}, "PHI": {"a": "(x)", "d": "Retorna o valor da função de densidade para uma distribuição normal padrão", "ad": "é o número do qual você deseja a densidade da distribuição normal padrão"}, "POISSON": {"a": "(x; média; cumulativo)", "d": "Retorna a distribuição Poisson", "ad": "é o número de eventos!é o valor numérico esperado, um número positivo!é um valor lógico: para a probabilidade Poisson, use VERDADEIRO; para a função de probabilidade de massa Poisson, use FALSO"}, "POISSON.DIST": {"a": "(x; média; cumulativo)", "d": "Retorna a distribuição Poisson", "ad": "é o número de eventos.!é o valor numérico esperado, um número positivo!é um valor lógico: para a probabilidade Poisson, use VERDADEIRO; para a função de probabilidade de massa Poisson, use FALSO"}, "PROB": {"a": "(intervalo_x; intervalo_prob; limite_inferior; [limite_superior])", "d": "Retorna a probabilidade de que valores em um intervalo estão entre dois limites ou são iguais ao limite inferior", "ad": "é o intervalo de valores numéricos de x com os quais são associadas probabilidades!é um conjunto de probabilidades associadas a valores no 'Intervalo_x', valores entre 0 e 1 excluindo 0!é o limite inferior do valor cuja probabilidade você deseja obter!é o limite superior opcional do valor cuja probabilidade você deseja obter. Quando não especificado, PROB retorna a probabilidade de os valores de 'Intervalo_x' serem iguais ao 'Limite_inferior'"}, "QUARTILE": {"a": "(matriz; quarto)", "d": "Retorna o quartil do conjunto de dados", "ad": "é a matriz ou o intervalo de células de valores numéricos cujo valor quartil você deseja obter!é um número: valor mínimo = 0, primeiro quartil = 1, valor mediano = 2, terceiro quartil = 3, valor máximo = 4"}, "QUARTILE.INC": {"a": "(matriz; quarto)", "d": "Retorna o quartil do conjunto de dados, com base nos valores de percentil de 0..1, inclusive", "ad": "é a matriz ou intervalo de células de valores numéricos cujo valor do quartil você deseja obter!é um número: valor mínimo = 0, primeiro quartil = 1, valor mediano = 2, terceiro quartil = 3, valor máximo = 4"}, "QUARTILE.EXC": {"a": "(matriz; quarto)", "d": "Retorna o quartil do conjunto de dados, com base nos valores de percentil de 0..1, exclusive", "ad": "é a matriz ou intervalo de células de valores numéricos cujo valor do quartil você deseja obter!é um número: valor mínimo = 0, primeiro quartil = 1, valor mediano = 2, terceiro quartil = 3, valor máximo = 4"}, "RANK": {"a": "(núm; ref; [ordem])", "d": "Retorna a posição de um número em uma lista de números: o seu tamanho em relação a outros valores da lista", "ad": "é o número cuja posição se deseja localizar!é uma matriz de, ou referência a, uma lista de números. Valores não numéricos são ignorados!é um número: posicionar na lista em ordem decrescente = 0 ou não especificado; posicionar na lista em ordem crescente = qualquer valor diferente de zero"}, "RANK.AVG": {"a": "(núm; ref; [ordem])", "d": "Retorna a posição de um número em uma lista de números: o seu tamanho em relação a outros valores da lista; se mais de um valor tiver a mesma posição, será retornada uma posição média", "ad": "é o número cuja posição você deseja localizar!é uma matriz de, ou referência a, uma lista de números. Valores não numéricos são ignorados!é um número: posicionar na lista em ordem decrescente = 0 ou não especificado; posicionar na lista em ordem crescente = qualquer valor diferente de zero"}, "RANK.EQ": {"a": "(núm; ref; [ordem])", "d": "Retorna a posição de um número em uma lista de números: o seu tamanho em relação a outros valores da lista; se mais de um valor tiver a mesma posição, será retornada a posição mais elevada do conjunto de valores", "ad": "é o número cuja posição você deseja localizar!é uma matriz de, ou referência a, uma lista de números. Valores não numéricos são ignorados!é um número: posicionar na lista em ordem decrescente = 0 ou não especificado; posicionar na lista em ordem crescente = qualquer valor diferente de zero"}, "RSQ": {"a": "(known_ys; known_xs)", "d": "Retorna o quadrado do coeficiente de correlação do momento do produto de Pearson para os pontos de dados determinados", "ad": "é uma matriz ou intervalo de pontos de dados, podendo ser números ou nomes, matrizes ou referências que contenham números!é uma matriz ou intervalo de pontos de dados, podendo ser números ou nomes, matrizes ou referências que contenham números"}, "SKEW": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a distorção de uma distribuição: uma caracterização do grau de assimetria da distribuição em relação à média", "ad": "de 1 a 255 números ou nomes, matrizes ou referências que contenham números cuja distorção você deseja obter"}, "SKEW.P": {"a": "(número1; [número2]; ...)", "d": "Retorna a distorção de uma distribuição com base na população: uma caracterização do grau de assimetria da distribuição em torno da média", "ad": " são de 1 a 254 números ou nomes, matrizes ou referências que contenham números para o qual você deseja a distorção da população"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "Retorna a inclinação da reta de regressão linear para os pontos de dados determinados", "ad": "é uma matriz ou intervalo de pontos de dados dependentes, podendo ser números ou nomes, matrizes ou referências que contenham números!é uma matriz ou intervalo de pontos de dados independentes, podendo ser números ou nomes, matrizes ou referências que contenham números"}, "SMALL": {"a": "(matriz; k)", "d": "Retorna o menor valor k-ésimo do conjunto de dados. Por exemplo, o quinto menor número", "ad": "é uma matriz ou intervalo de dados numéricos cujo menor valor k-ésimo você deseja determinar!é a posição (começando do menor) na matriz ou intervalo do valor a ser retornado"}, "STANDARDIZE": {"a": "(x; média; desv_padrão)", "d": "Retorna um valor normalizado de uma distribuição caracterizada por uma média e um desvio padrão", "ad": "é o valor que se deseja normalizar!é a média aritmética da distribuição!é o desvio padrão da distribuição, um número positivo"}, "STDEV": {"a": "(núm1; [núm2]; ...)", "d": "Estima o desvio padrão com base em uma amostra (ignora os valores lógicos e texto da amostra)", "ad": "de 1 a 255 números que correspondem a uma amostra de uma população, podendo ser números ou referências que contenham números"}, "STDEV.P": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio padrão com base na população total determinada como argumento (ignora valores lógicos e texto)", "ad": "de 1 a 255 números que correspondem a uma população, podendo ser números ou referências que contenham números"}, "STDEV.S": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio padrão a partir de uma amostra (ignora os valores lógicos e o texto da amostra)", "ad": "de 1 a 255 números que correspondem a uma amostra de uma população, podendo ser números ou referências que contenham números"}, "STDEVA": {"a": "(valor1; [valor2]; ...)", "d": "Estima o desvio padrão com base em uma amostra, incluindo valores lógicos e texto. Texto e o valor lógico FALSO têm o valor 0, o valor lógico VERDADEIRO tem valor 1", "ad": "de 1 a 255 valores que correspondem a uma amostra de uma população, podendo ser valores, nomes ou referências que contenham valores"}, "STDEVP": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio padrão com base na população total determinada como argumento (ignora valores lógicos e texto)", "ad": "de 1 a 255 números que correspondem a uma população, podendo ser números ou referências que contenham números"}, "STDEVPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula o desvio padrão com base na população total, incluindo valores lógicos e texto. Texto e o valor lógico FALSO têm o valor 0, o valor lógico VERDADEIRO tem valor 1", "ad": "de 1 a 255 valores que correspondem à população, podendo ser valores, nomes, matrizes ou referências que contenham valores"}, "STEYX": {"a": "(known_ys; known_xs)", "d": "Retorna o erro padrão do valor de y previsto para cada x da regressão", "ad": "é uma matriz ou intervalo de pontos de dados dependentes, podendo ser números ou nomes, matrizes ou referências que contenham números!é uma matriz ou intervalo de pontos de dados independentes, podendo ser números ou nomes, matrizes ou referências que contenham números"}, "TDIST": {"a": "(x; graus_liberdade; caudas)", "d": "Retorna a distribuição t de Student", "ad": "é o valor numérico em que se avalia a distribuição!é um número inteiro indicando o número de graus de liberdade que caracteriza a distribuição!especifica o número de caudas da distribuição a ser retornado: distribuição unicaudal = 1; distribuição bicaudal = 2"}, "TINV": {"a": "(probabilidade; graus_liberdade)", "d": "Retorna o inverso bicaudal da distribuição t de Student", "ad": "é a probabilidade associada à distribuição t de Student bicaudal, um número entre 0 e 1 inclusive!é um inteiro positivo que indica o número de graus de liberdade que caracteriza a distribuição"}, "T.DIST": {"a": "(x; graus_liberdade; cumulativo)", "d": "Retorna a cauda esquerda da distribuição t de Student", "ad": "é o valor numérico em que se avalia a distribuição!é um número inteiro indicando o número de graus de liberdade que caracteriza a distribuição!é um valor lógico: para a função de distribuição cumulativa, use VERDADEIRO; para a função de densidade da probabilidade, use FALSO"}, "T.DIST.2T": {"a": "(x; graus_liberdade)", "d": "Retorna a distribuição t de Student bicaudal", "ad": "é o valor numérico em que se avalia a distribuição!é um inteiro indicando o número de graus de liberdade que caracteriza a distribuição"}, "T.DIST.RT": {"a": "(x; graus_liberdade)", "d": "Retorna a distribuição t de Student de cauda direita", "ad": "é o valor numérico em que se avalia a distribuição!é um inteiro indicando o número de graus de liberdade que caracteriza a distribuição"}, "T.INV": {"a": "(probabilidade; graus_liberdade)", "d": "Retorna o inverso de cauda esquerda da distribuição t de Student", "ad": "é a probabilidade associada à distribuição t de Student bicaudal, um número entre 0 e 1 inclusive!é um inteiro positivo que indica o número de graus de liberdade que caracteriza a distribuição"}, "T.INV.2T": {"a": "(probabilidade; graus_liberdade)", "d": "Retorna o inverso bicaudal da distribuição t de Student", "ad": "é a probabilidade associada à distribuição t de Student bicaudal, um número entre 0 e 1 inclusive!é um inteiro positivo que indica o número de graus de liberdade que caracteriza a distribuição"}, "T.TEST": {"a": "(matriz1; matriz2; caudas; tipo)", "d": "Retorna a probabilidade associada ao teste t de Student", "ad": "é o primeiro conjunto de dados!é o segundo conjunto de dados!especifica o número de caudas da distribuição a ser retornado: distribuição unicaudal = 1; distribuição bicaudal = 2!é o tipo de teste t: par = 1, variação igual de duas amostras (homoscedástica) = 2, variação desigual de duas amostras = 3"}, "TREND": {"a": "(val_conhecidos_y; [val_conhecidos_x]; [novos_valores_x]; [constante])", "d": "Retorna números em uma tendência linear que corresponda a pontos de dados conhecidos através do método de quadrados mínimos", "ad": "é um intervalo ou matriz de valores de y conhecidos na relação y = mx + b!é um intervalo ou matriz de valores de x conhecidos na relação y = mx + b, uma matriz com o mesmo tamanho que Known_y's!é um intervalo ou matriz de valores de x para os quais você deseja que TENDÊNCIA forneça valores de y correspondentes!é um valor lógico: a constante b é calculada normalmente se Const = VERDADEIRA ou não especificada; b é definido como 0 se Const = FALSA"}, "TRIMMEAN": {"a": "(matriz; porcentagem)", "d": "Retorna a média da parte interior de um conjunto de valores de dados", "ad": "é o intervalo ou matriz de valores a se calcular a média desprezando os desvios!é o número fracionário de ponto de dados a ser excluído do início e fim do conjunto de dados"}, "TTEST": {"a": "(matriz1; matriz2; caudas; tipo)", "d": "Retorna a probabilidade associada ao teste t de Student", "ad": "é o primeiro conjunto de dados!é o segundo conjunto de dados!especifica o número de caudas da distribuição a ser retornado: distribuição unicaudal = 1; distribuição bicaudal = 2!é o tipo de teste t: par = 1, variação igual de duas amostras (homoscedástica) = 2, variação desigual de duas amostras = 3"}, "VAR": {"a": "(núm1; [núm2]; ...)", "d": "Estima a variação com base em uma amostra (ignora valores lógicos e texto da amostra)", "ad": "de 1 a 255 argumentos numéricos que correspondem a uma amostra de uma população"}, "VAR.P": {"a": "(núm1; [núm2]; ...)", "d": "Calcula a variação com base na população total (ignora valores lógicos e texto da população)", "ad": "de 1 a 255 argumentos numéricos que correspondem a uma população"}, "VAR.S": {"a": "(núm1; [núm2]; ...)", "d": "Estima a variação com base em uma amostra (ignora valores lógicos e texto na amostra)", "ad": "de 1 a 255 argumentos numéricos que correspondem a uma amostra de uma população"}, "VARA": {"a": "(valor1; [valor2]; ...)", "d": "Estima a variação com base em uma amostra, incluindo valores lógicos e texto. Texto e o valor lógico FALSO têm o valor 0, o valor lógico VERDADEIRO tem valor 1", "ad": "de 1 a 255 argumentos que correspondem a uma amostra de uma população"}, "VARP": {"a": "(núm1; [núm2]; ...)", "d": "Calcula a variação com base na população total (ignora valores lógicos e texto da população)", "ad": "de 1 a 255 argumentos numéricos que correspondem a uma população"}, "VARPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula a variação com base na população total, incluindo valores lógicos e texto. Texto e o valor lógico FALSO têm o valor 0, o valor lógico VERDADEIRO tem valor 1", "ad": "de 1 a 255 valores que correspondem a uma população"}, "WEIBULL": {"a": "(x; alfa; beta; cumulativo)", "d": "Retorna a distribuição Weibull", "ad": "é o valor no qual se avalia a função, um número não negativo!é o parâmetro da distribuição, um número positivo!é o parâmetro da distribuição, um número positivo!é um valor lógico: para a função de distribuição cumulativa, use VERDADEIRO; para a função de probabilidade de massa, use FALSO"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Retorna a distribuição Weibull", "ad": "é o valor no qual se avalia a função, um número não negativo!é o parâmetro da distribuição, um número positivo!é o parâmetro da distribuição, um número positivo!é um valor lógico: para a função de distribuição cumulativa, use VERDADEIRO; para a função de probabilidade de massa, use FALSO"}, "Z.TEST": {"a": "(matriz; x; [sigma])", "d": "Retorna o valor-P unicaudal do teste-z", "ad": " é a matriz ou intervalo de dados em que x será testado! é o valor para teste!é o desvio padrão da população (conhecida). Quando não especificado, o desvio padrão da amostra é utilizado"}, "ZTEST": {"a": "(matriz; x; [sigma])", "d": "Retorna o valor-P unicaudal do teste z", "ad": " é a matriz ou o intervalo de dados em que x será testado! é o valor para teste!é o desvio padrão da população (conhecida). Quando não especificado, o desvio padrão da amostra é utilizado"}, "ACCRINT": {"a": "(emissão; primeiro_juro; liquidação; taxa; valor_nominal; frequência; [base]; [méto<PERSON>_cálculo])", "d": "Retorna os juros acumulados de um título que paga juros periódicos.", "ad": "é a data de emissão do título, expressa como um número serial de data!é a data do primeiro juro do título, expressa como um número serial de data!é a data de liquidação do título, expressa como um número serial de data!é a taxa de cupom anual do título!é o valor par do título!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser usada!é um valor lógico: para calcular os juros acumulados desde a data de emissão = VERDADEIRO ou omitido; para calcular a partir da última data de pagamento de cupom = FALSO"}, "ACCRINTM": {"a": "(emissão; liquidação; taxa; valor_nominal; [base])", "d": "Retorna os juros acumulados de um título que paga juros no vencimento", "ad": "é a data de emissão do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a taxa de cupom anual do título!é o valor de paridade do título!é o tipo de base de contagem diária a ser utilizada"}, "AMORDEGRC": {"a": "(custo; data_aquisição; prim_período; recuperação; período; taxa; [base])", "d": "Retorna a depreciação linear pro rata de um ativo para cada período contábil.", "ad": "é o custo do ativo!é a data na qual o ativo foi comprado!é a data do final do primeiro período!é o valor de recuperação no final da vida do ativo.!é o período!é a taxa de depreciação!base_ano: 0 para anos de 360 dias, 1 para real, 3 para anos de 365 dias."}, "AMORLINC": {"a": "(custo; data_aquisição; prim_período; recuperação; período; taxa; [base])", "d": "Retorna a depreciação linear pro rata de um ativo para cada período contábil", "ad": "é o custo do ativo!é a data na qual o ativo foi comprado!é a data do final do primeiro período!é o valor de recuperação no final da vida do ativo.!é o período!é a taxa de depreciação!base_ano: 0 para anos de 360 dias, 1 para real, 3 para anos de 365 dias."}, "COUPDAYBS": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Retorna o número de dias entre o início do período do cupom e a data de liquidação.", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "COUPDAYS": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Retorna o número de dias no período do cupom que contém a data de liquidação.", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "COUPDAYSNC": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Retorna o número de dias entre a data de liquidação e a próxima data do cupom.", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "COUPNCD": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Retorna a próxima data do cupom depois da data de liquidação.", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "COUPNUM": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Retorna o número de cupons a serem pagos entre a data de liquidação e a data de vencimento", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "COUPPCD": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Retorna a última data do cupom antes da data de liquidação.", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "CUMIPMT": {"a": "(taxa; nper; vp; início_período; final_período; tipo_pgto)", "d": "Retorna os juros cumulativos pagos entre dois períodos.", "ad": "é a taxa de juros!é o número total de períodos de pagamento!é o valor presente!é o primeiro período do cálculo!é o último período do cálculo!indica quando o pagamento é efetuado"}, "CUMPRINC": {"a": "(taxa; nper; vp; início_período; final_período; tipo_pgto)", "d": "Retorna o capital cumulativo pago em um empréstimo entre dois períodos.", "ad": "é a taxa de juros!é o número total de períodos de pagamento!é o valor presente!é o primeiro período do cálculo!é o último período do cálculo!indica quando o pagamento é efetuado"}, "DB": {"a": "(custo; recuperação; vida_útil; período; [mês])", "d": "Retorna a depreciação de um ativo para um determinado período utilizando o método de balanço de declínio fixo", "ad": "é o custo inicial do ativo!é o valor residual no final da vida útil do ativo!é o número de períodos durante os quais o ativo está sendo depreciado (algumas vezes denominado a vida útil do ativo)!é o período cuja depreciação você deseja calcular. A unidade do período deve ser a mesma de 'Vida_útil'!é o número de meses do primeiro ano. Se mês for omitido, presume-se que seja 12"}, "DDB": {"a": "(custo; recuperação; vida_útil; período; [fator])", "d": "Retorna a depreciação de um ativo para um determinado período utilizando o método do balanço de declínio duplo ou qualquer outro método especificado", "ad": "é o custo inicial do ativo!é o valor residual no final da vida útil do ativo!é o número de períodos durante os quais o ativo está sendo depreciado (algumas vezes denominado a vida útil do ativo)!é o período cuja depreciação você deseja calcular. A unidade do período deve ser a mesma de 'Vida_útil'!é o índice de declínio do saldo. Se o fator for omitido, o fator adotado será 2 (método do saldo decrescente duplo)"}, "DISC": {"a": "(liquidação; vencimento; pr; resgate; [base])", "d": "Retorna a taxa de desconto de um título", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é o preço do título por R$100 do valor nominal!é o valor de resgate do título por R$100 do valor nominal!é o tipo de base de contagem diária a ser utilizada"}, "DOLLARDE": {"a": "(moeda_fracionária; fração)", "d": "Converte um preço em moeda, expresso como uma fração, em um preço em moeda, expresso como um número decimal", "ad": "é um número expresso como uma fração!é o inteiro a ser utilizado no denominador da fração"}, "DOLLARFR": {"a": "(moeda_decimal; fração)", "d": "Converte um preço em moeda, expresso como um número decimal, em um preço em moeda, expresso como uma fração", "ad": "é um número decimal!é o inteiro a ser utilizado no denominador da fração"}, "DURATION": {"a": "(liquidação; vencimento; cupom; lcr; frequência; [base])", "d": "Retorna a duração anual de um título com pagamentos de juros periódicos.", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a taxa de cupom anual do título!é o lucro anual do título!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "EFFECT": {"a": "(taxa_nominal; núm_por_ano)", "d": "Retorna a taxa de juros efetiva anual", "ad": "é a taxa de juros nominal!é o número de períodos compostos por ano"}, "FV": {"a": "(taxa; nper; pgto; [vp]; [tipo])", "d": "Retorna o valor futuro de um investimento com base em pagamentos constantes e periódicos e uma taxa de juros constante", "ad": "é a taxa de juros por período. Por exemplo, use 6%/4 para pagamentos trimestrais a uma taxa de 6% TPA!é o número total de períodos de pagamento em um investimento!é o pagamento efetuado a cada período; não pode ser alterado no decorrer do investimento!é o valor presente, ou a quantia total atual correspondente a uma série de pagamentos futuros. Quando não especificado, Vp = 0!é o valor que representa o vencimento do pagamento; pagamento no início do período = 1; pagamento ao final do período = 0 ou não especificado"}, "FVSCHEDULE": {"a": "(capital; plano)", "d": "Retorna o valor futuro de um capital inicial depois de ter sido aplicada uma série de taxas de juros compostos.", "ad": "é o valor presente!é uma matriz de taxas de juros a ser aplicada"}, "INTRATE": {"a": "(liquidação; vencimento; investimento; resgate; [base])", "d": "Retorna a taxa de juros de um título totalmente investido", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a quantia investida no título!é a quantia a ser recebida no vencimento!é o tipo de base de contagem diária a ser utilizada"}, "IPMT": {"a": "(taxa; período; nper; vp; [vf]; [tipo])", "d": "Retorna o pagamento dos juros de um investimento durante um determinado período, com base nos pagamentos constantes, periódicos e na taxa de juros constante", "ad": "é a taxa de juros por período. Por exemplo, use 6%/4 para pagamentos trimestrais a uma taxa de 6% TPA!é o período cujos juros se deseja saber e deve estar no intervalo entre 1 e nper!é o número total de períodos de pagamento em um investimento!é o valor presente ou a quantia total atual correspondente a uma série de pagamentos futuros!é o valor futuro ou um saldo em dinheiro que se deseja obter após o último pagamento ter sido efetuado. Quando não especificado, Vf = 0!é um valor lógico que representa o vencimento do pagamento: ao final do período = 0 ou não especificado; no início do período = 1"}, "IRR": {"a": "(valores; [estimativa])", "d": "Retorna a taxa interna de retorno de uma série de fluxos de caixa", "ad": "é uma matriz ou uma referência a células que contêm números cuja taxa interna de retorno se deseja calcular!é um número que se estima ser próximo do resultado de TIR; 0,1 (10%) quando não especificado"}, "ISPMT": {"a": "(taxa; período; nper; vp)", "d": "Retorna os juros pagos durante um período específico do investimento", "ad": "taxa de juros por período. Por exemplo, use 6%/4 para pagamentos trimestrais a uma taxa de 6% TPA!período cujos juros você deseja obter!número de períodos de pagamento em um investimento!quantia total atual correspondente a uma série de pagamentos futuros"}, "MDURATION": {"a": "(liquidação; vencimento; cupom; lcr; frequência; [base])", "d": "Retorna a duração modificada Macauley de um título com um valor par presumido de R$ 100.", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a taxa de cupom anual do título!é o lucro anual do título!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "MIRR": {"a": "(valores; taxa_financ; taxa_reinvest)", "d": "Retorna a taxa interna de retorno para uma série de fluxos de caixa periódicos, considerando o custo de investimento e os juros de reinvestimento de caixa", "ad": "é uma matriz ou referência a células que contêm números que representam uma série de pagamentos (negativa) e renda (positiva) em períodos regulares!é a taxa de juros paga sobre o dinheiro utilizado em fluxos de caixa!é a taxa de juros recebida sobre os fluxos de caixa à medida que estes forem sendo reinvestidos"}, "NOMINAL": {"a": "(taxa_efetiva; núm_por_ano)", "d": "Retorna a taxa de juros nominal anual", "ad": "é a taxa de juros efetiva!é o número de períodos compostos por ano"}, "NPER": {"a": "(taxa; pgto; vp; [vf]; [tipo])", "d": "Retorna o número de períodos de um investimento com base em pagamentos constantes periódicos e uma taxa de juros constante", "ad": "é a taxa de juros por período. Por exemplo, use 6%/4 para pagamentos trimestrais a uma taxa de 6% TPA!é o pagamento efetuado a cada período; ele não pode ser alterado no decorrer do investimento!é o valor presente ou a quantia total atual correspondente a uma série de pagamentos futuros!é o valor futuro ou um saldo em dinheiro que se deseja obter após o último pagamento ter sido efetuado. Quando não especificado, é usado valor zero!é um valor lógico: pagamento no início do período = 1; pagamento ao final do período = 0 ou não especificado"}, "NPV": {"a": "(taxa; valor1; [valor2]; ...)", "d": "Retorna o valor líquido atual de um investimento, com base em uma taxa de desconto e uma série de pagamentos futuros (valores negativos) e renda (valores positivos)", "ad": "é a taxa de desconto durante um período!de 1 a 254 pagamentos e rendas, distribuídos em espaços iguais, e que ocorrem ao final de cada período"}, "ODDFPRICE": {"a": "(liquidação; vencimento; emissão; prim_cupom; taxa; lcr; resgate; frequência; [base])", "d": "Retorna o preço por R$100 do valor nominal de um título com um período inicial incompleto.", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a data de emissão do título, expressa como um número serial de data!é a primeira data do cupom do título, expressa como um número serial de data!é a taxa de juros do título!é o lucro anual do título!é valor de resgate por R$100 do valor nominal!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "ODDFYIELD": {"a": "(liquidação; vencimento; emissão; prim_cupom; taxa; pr; resgate; frequência; [base])", "d": "Retorna o rendimento de um título com um período inicial incompleto.", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a data de emissão do título, expressa como um número serial de data!é a primeira data do cupom do título, expressa como um número serial de data!é a taxa de juros do título!é o preço do título!é valor de resgate por R$100 do valor nominal!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "ODDLPRICE": {"a": "(liquidação; vencimento; último_juros; taxa; lcr; resgate; frequência; [base])", "d": "Retorna o preço por R$100 do valor nominal de um título com um período final incompleto", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a última data do cupom do título, expressa como um número serial de data!é a taxa de juros do título!é o lucro anual do título!é valor de resgate por R$100 do valor nominal!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "ODDLYIELD": {"a": "(liquidação; vencimento; último_juros; taxa; pr; resgate; frequência; [base])", "d": "Retorna o rendimento de um título com um período final incompleto.", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a última data do cupom do título, expressa como um número serial de data!é a taxa de juros do título!é o preço do título!é valor de resgate por R$100 do valor nominal!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "PDURATION": {"a": "(taxa; pv; fv)", "d": "Retorna o número de períodos exigido por um investimento para atingir um valor especificado", "ad": "é a taxa de juros por período.!é o valor presente do investimento!é o valor futuro desejado do investimento"}, "PMT": {"a": "(taxa; nper; vp; [vf]; [tipo])", "d": "Calcula o pagamento de um empréstimo com base em pagamentos e em uma taxa de juros constantes", "ad": "é a taxa de juros por período de um empréstimo. Por exemplo, use 6%/4 para pagamentos trimestrais a uma taxa de 6% TPA!é o número total de pagamentos em um empréstimo!é o valor presente: a quantia total atual de uma série de pagamentos futuros!é o valor futuro, ou um saldo em dinheiro que se deseja obter após o último pagamento ter sido efetuado; 0 (zero) quando não especificado!é um valor lógico: pagamento no início do período = 1; pagamento ao final do período = 0 ou não especificado"}, "PPMT": {"a": "(taxa; per; nper; vp; [vf]; [tipo])", "d": "Retorna o pagamento sobre o montante de um investimento com base em pagamentos e em uma taxa de juros constantes, periódicos", "ad": "é a taxa de juros por período. Por exemplo, use 6%/4 para pagamentos trimestrais a uma taxa de 6% TPA!especifica o período e deve estar no intervalo entre 1 e nper!é o número total de períodos de pagamento em um investimento!é o valor presente: a quantia total atual de uma série de pagamentos futuros!é o valor futuro, ou um saldo em dinheiro que se deseja obter após o último pagamento ter sido efetuado!é um valor lógico: pagamento no início do período = 1; pagamento ao final do período = 0 ou não especificado"}, "PRICE": {"a": "(liquidação; vencimento; taxa; lcr; resgate; frequência; [base])", "d": "Retorna o preço por R$100 do valor nominal de um título que paga juros periódicos.", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a taxa de cupom anual do título!é o lucro anual do título!é o valor de resgate do título por R$100 do valor nominal!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "PRICEDISC": {"a": "(liquidação; vencimento; desconto; resgate; [base])", "d": "Retorna o preço por R$100 do valor nominal de um título com deságio", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a taxa de desconto do título!é o valor de resgate do título por R$100 do valor nominal!é o tipo de base de contagem diária a ser utilizada"}, "PRICEMAT": {"a": "(liquidação; vencimento; emissão; taxa; lcr; [base])", "d": "Retorna o preço por R$100 do valor nominal de um título que paga juros no vencimento", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a data de emissão do título, expressa como um número serial de data!é a taxa de juros do título na data de emissão!é o lucro anual do título!é o tipo de base de contagem diária a ser utilizada"}, "PV": {"a": "(taxa; per; pgto; [vf]; [tipo])", "d": "Retorna o valor presente de um investimento: a quantia total atual de uma série de pagamentos futuros", "ad": "é a taxa de juros por período. Por exemplo, use 6%/4 para pagamentos trimestrais a uma taxa de 6% TPA!é o número total de períodos de pagamento em um investimento!é o pagamento efetuado a cada período, não podendo ser alterado no decorrer do investimento!é o valor futuro ou um saldo em dinheiro que se deseja obter após o último pagamento ter sido efetuado!é um valor lógico: pagamento no início do período = 1; pagamento ao final do período = 0 ou não especificado"}, "RATE": {"a": "(nper; pgto; vp; [vf]; [tipo]; [estimativa])", "d": "Retorna a taxa de juros por período em um empréstimo ou investimento. Por exemplo, use 6%/4 para pagamentos trimestrais a uma taxa de 6% TPA", "ad": "é o número total de períodos de pagamento em um empréstimo ou um investimento!é o pagamento efetuado a cada período e não pode ser alterado no decorrer do empréstimo ou investimento!é o valor presente: a quantia total atual de uma série de pagamentos futuros!é o valor futuro, ou um saldo em dinheiro que se deseja atingir após o último pagamento ter sido efetuado. Quando não especificado, utiliza Vf = 0!é um valor lógico: pagamento no início do período = 1; pagamento ao final do período = 0 ou não especificado!é a estimativa do valor da taxa; quando não especificado, Estimar = 0,1 (10 por cento)"}, "RECEIVED": {"a": "(liquidação; vencimento; investimento; desconto; [base])", "d": "Retorna a quantia recebida no vencimento para um título totalmente investido.", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a quantia investida no título!é a taxa de desconto do título!é o tipo de base de contagem diária a ser utilizada"}, "RRI": {"a": "(nper; pv; fv)", "d": "Retorna uma taxa de juros equivalente para o crescimento de um investimento", "ad": "é o número de períodos do investimento!é o valor presente do investimento!é o valor futuro do investimento"}, "SLN": {"a": "(custo; recuperação; vida_útil)", "d": "Retorna a depreciação em linha reta de um ativo durante um período", "ad": "é o custo inicial do ativo!é o valor residual no final da vida útil do ativo!é o número de períodos durante os quais o ativo está sendo depreciado (algumas vezes denominado a vida útil do ativo)"}, "SYD": {"a": "(custo; recuperação; vida_útil; per)", "d": "Retorna a depreciação dos dígitos da soma dos anos de um ativo para um período especificado", "ad": "é o custo inicial do ativo!é o valor residual no final da vida útil do ativo!é o número de períodos durante os quais o ativo está sendo depreciado (algumas vezes denominado a vida útil do ativo)!é o período e deve utilizar as mesmas unidades de 'Vida_útil'"}, "TBILLEQ": {"a": "(liquidação; vencimento; desconto)", "d": "Retorna o rendimento de uma letra do tesouro equivalente ao rendimento de um título", "ad": "é a data de liquidação da letra do tesouro, expressa como um número serial de data!é a data de vencimento da letra do tesouro, expressa como um número serial de data!é a taxa de desconto da letra do tesouro"}, "TBILLPRICE": {"a": "(liquidação; vencimento; desconto)", "d": "Retorna o preço por R$100 do valor nominal de uma letra do tesouro", "ad": "é a data de liquidação da letra do tesouro, expressa como um número serial de data!é a data de vencimento da letra do tesouro, expressa como um número serial de data!é a taxa de desconto da letra do tesouro"}, "TBILLYIELD": {"a": "(liquidação; vencimento; pr)", "d": "Retorna o rendimento de uma letra do tesouro", "ad": "é a data de liquidação da letra do tesouro, expressa como um número serial de data!é a data de vencimento da letra do tesouro, expressa como um número serial de data!é o preço por R$100 do valor nominal da letra do tesouro"}, "VDB": {"a": "(custo; recuperação; vida_útil; início_período; final_período; [fator]; [sem_mudança])", "d": "Retorna a depreciação de um ativo para um período específico, incluindo o período parcial, utilizando o método de balanço decrescente duplo ou qualquer outro método especificado", "ad": "é o custo inicial do ativo!é o valor residual no final da vida útil do ativo!é o número de períodos durante os quais o ativo está sendo depreciado (algumas vezes denominado a vida útil do ativo)!é o período inicial cuja depreciação você deseja calcular. A unidade do período deve ser a mesma unidade de 'Vida_útil'.!é o período final cuja depreciação você deseja calcular. A unidade do período deve ser a mesma unidade de 'Vida_útil'!é o índice de declínio do saldo. Quando não especificado, é utilizado 2 (balanço decrescente duplo)!alternar para depreciação em linha reta quando a depreciação é maior que o balanço decrescente = FALSO ou não especificado. Não alternar = VERDADEIRO"}, "XIRR": {"a": "(valores; datas; [estimativa])", "d": "Retorna a taxa de retorno interna de um cronograma de fluxos de caixa", "ad": "é uma série de fluxos de caixa que corresponde a um cronograma de pagamentos em datas!é o cronograma de datas de pagamento que corresponde aos pagamentos de fluxo de caixa!é um número que se estima ser próximo ao resultado de XTIR"}, "XNPV": {"a": "(taxa; valores; datas)", "d": "Retorna o valor presente líquido de um cronograma de fluxos de caixa", "ad": "é a taxa de desconto a ser aplicada nos fluxos de caixa!é uma série de fluxos de caixa que corresponde a um cronograma de pagamentos em datas!é o cronograma de datas de pagamento que corresponde aos pagamentos de fluxo de caixa"}, "YIELD": {"a": "(liquidação; vencimento; taxa; pr; resgate; frequência; [base])", "d": "Retorna o rendimento de um título que paga juros periódicos", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a taxa de cupom anual do título!é o preço do título por R$100 do valor nominal!é o valor de resgate do título por R$100 do valor nominal!é o número de pagamentos de cupom por ano!é o tipo de base de contagem diária a ser utilizada"}, "YIELDDISC": {"a": "(liquidação; vencimento; pr; resgate; [base])", "d": "Retorna o rendimento anual de um título com deságio. Por exemplo, uma letra do tesouro", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é o preço do título por R$100 do valor nominal!é o valor de resgate do título por R$100 do valor nominal!é o tipo de base de contagem diária a ser utilizada"}, "YIELDMAT": {"a": "(liquidação; vencimento; emissão; taxa; pr; [base])", "d": "Retorna o rendimento anual de um título que paga juros no vencimento", "ad": "é a data de liquidação do título, expressa como um número serial de data!é a data de vencimento do título, expressa como um número serial de data!é a data de emissão do título, expressa como um número serial de data!é a taxa de juros do título na data de emissão!é o preço do título por R$100 do valor nominal!é o tipo de base de contagem diária a ser utilizada"}, "ABS": {"a": "(núm)", "d": "Retorna o valor absoluto de um número, um número sem sinal", "ad": "é o número real cujo valor absoluto se deseja obter"}, "ACOS": {"a": "(núm)", "d": "Retorna o arco cosseno de um número, em radianos no intervalo de 0 a Pi. O arco cosseno é o ângulo cujo cosseno é número", "ad": "é o cosseno do ângulo desejado e deve estar entre -1 e 1"}, "ACOSH": {"a": "(núm)", "d": "Retorna o cosseno hiperbólico inverso de um número", "ad": "é qualquer número real igual ou maior do que 1"}, "ACOT": {"a": "(número)", "d": "Retorna o arco cotangente de um número, em radianos no intervalo de 0 a Pi.", "ad": "é o cotangente do ângulo desejado"}, "ACOTH": {"a": "(número)", "d": "Retorna a hiperbólica inversa da cotangente de um número", "ad": "é a hiperbólica da cotangente do ângulo desejado"}, "AGGREGATE": {"a": "(núm_função; opções; ref1; ...)", "d": "Retorna uma agregação em uma lista ou um banco de dados", "ad": "é o número de 1 a 19 que especifica a função de resumo da agregação.!é o número de 0 a 7 que especifica os valores a serem ignorados para a agregação!é a matriz ou o intervalo de dados numéricos de base para calcular a agregação!indica a posição na matriz; é o maior k-ésimo, o menor k-ésimo, o k-ésimo percentil ou p k-ésimo quartil.!é o número de 1 a 19 que especifica a função de resumo da agregação.!é o número de 0 a 7 que especifica os valores a serem ignorados para a agregação!são intervalos de 1 a 253 ou referências das quais você deseja obter a agregação"}, "ARABIC": {"a": "(texto)", "d": "Converte um numeral romano em arábico", "ad": "é o algarismo romano que você deseja converter"}, "ASC": {"a": "(texto)", "d": "Para idiomas do conjunto de caracteres de dois bytes (DBCS), a função altera os caracteres de largura total (byte duplo) para caracteres de meia largura (byte único)", "ad": "é o texto a ser alterado"}, "ASIN": {"a": "(núm)", "d": "Retorna o arco seno de um número em radianos, no intervalo entre -Pi/2 e Pi/2", "ad": "é o seno do ângulo que se deseja e que deve estar entre -1 e 1"}, "ASINH": {"a": "(núm)", "d": "Retorna o seno hiperbólico inverso de um número", "ad": "é qualquer número real igual ou maior do que 1"}, "ATAN": {"a": "(núm)", "d": "Retorna o arco tangente de um número em radianos, no intervalo entre -Pi e p1/2", "ad": "é a tangente do ângulo que se deseja"}, "ATAN2": {"a": "(núm_x; núm_y)", "d": "Retorna o arco tangente das coordenadas x e y especificadas, em radianos entre -Pi e Pi, excluindo -Pi", "ad": "é a coordenada x do ponto!é a coordenada y do ponto"}, "ATANH": {"a": "(núm)", "d": "Retorna a tangente hiperbólica inversa de um número", "ad": "é um número real entre -1 e 1 (excluindo -1 e 1)"}, "BASE": {"a": "(número; radix; [min_length])", "d": "Converte um número em uma representação de texto com determinado radix (base)", "ad": "é o número que você deseja converter!é o radix de base em que você deseja converter o número!é o comprimento mínimo da cadeia de caracteres retornada. Se omitido, os zeros à esquerda não serão adicionados"}, "CEILING": {"a": "(núm; significância)", "d": "Arredonda um número para cima, para o próximo múltiplo significativo", "ad": "é o valor que se deseja arredondar!é o múltiplo para o qual se deseja arredondar"}, "CEILING.MATH": {"a": "(número; [significância]; [modo])", "d": "Arredonda um número para cima, para o inteiro mais próximo ou para o próximo múltiplo significativo", "ad": "é o valor que você deseja arredondar!é o múltiplo para o qual você deseja arredondar!quando determinado e diferente de zero, essa função arredondará afastando de zero"}, "CEILING.PRECISE": {"a": "(número; [significância])", "d": "Retorna um número que é arredondado para o inteiro mais próximo ou para o múltiplo mais próximo de significância", "ad": "é o valor que se deseja arredondar!é o múltiplo para o qual se deseja arredondar"}, "COMBIN": {"a": "(núm; núm_escolhido)", "d": "Retorna o número de combinações para um determinado número de itens", "ad": "é o número total de itens!é o número de itens em cada combinação"}, "COMBINA": {"a": "(número; núm_escolhido)", "d": "Retorna o número de combinações com repetições para um determinado número de itens", "ad": "é o número total de itens!é o número de itens em cada combinação"}, "COS": {"a": "(núm)", "d": "Retorna o cosseno de um ângulo", "ad": "é o ângulo em radianos para o qual você deseja obter o cosseno"}, "COSH": {"a": "(núm)", "d": "Retorna o cosseno hiperbólico de um número", "ad": "é qualquer número real"}, "COT": {"a": "(número)", "d": "Retorna a cotangente de um ângulo", "ad": "é o ângulo em radianos para o qual você deseja a cotangente"}, "COTH": {"a": "(número)", "d": "Retorna a hiperbólica da cotangente de um número", "ad": "é o ângulo em radianos para o qual você deseja a hiperbólica da cotangente"}, "CSC": {"a": "(número)", "d": "Retorna a cossecante de um ângulo", "ad": "é o ângulo em radianos para o qual você deseja a cossecante"}, "CSCH": {"a": "(número)", "d": "Retorna a hiperbólica da cossecante de um ângulo", "ad": "é o ângulo em radianos para o qual você deseja a hiperbólica da cossecante"}, "DECIMAL": {"a": "(número; radix)", "d": "Converte uma representação de texto de um número em uma determinada base em um número decimal", "ad": "é o número que você deseja converter!é o radix de base do número que você está convertendo"}, "DEGREES": {"a": "(â<PERSON><PERSON>)", "d": "Converte radianos em graus", "ad": "é o ângulo em radianos que se deseja converter"}, "ECMA.CEILING": {"a": "(número; significância)", "d": "Arredonda um número para cima para o próximo múltiplo significativo", "ad": "é o valor que se deseja arredondar!é o múltiplo para o qual se deseja arredondar"}, "EVEN": {"a": "(núm)", "d": "Arredonda um número positivo para cima e um número negativo para baixo até o valor inteiro mais próximo", "ad": "é o valor a ser arredondado"}, "EXP": {"a": "(núm)", "d": "Retorna 'e' elevado à potência de um determinado número", "ad": "é o expoente aplicado à base 'e'. A constante 'e' é igual a 2,71828182845904, a base do logaritmo natural"}, "FACT": {"a": "(núm)", "d": "Retorna o fatorial de um número, igual a 1*2*3*...*núm", "ad": "é o número não negativo do qual se deseja o fatorial"}, "FACTDOUBLE": {"a": "(núm)", "d": "Retorna o fatorial duplo de um número", "ad": "é o valor cujo fatorial duplo se deseja retornar"}, "FLOOR": {"a": "(núm; significância)", "d": "Arredonda um número para baixo até o múltiplo ou a significância mais próxima", "ad": "é o valor numérico que se deseja arredondar!é o múltiplo para o qual se deseja arredondar. 'Núm' e 'Significância' devem ser ambos positivos ou negativos"}, "FLOOR.PRECISE": {"a": "(número; [significância])", "d": "Retorna um número que é arredondado para baixo para o inteiro mais próximo ou para o múltiplo mais próximo de significância", "ad": "é o valor que se deseja arredondar!é o múltiplo para o qual se deseja arredondar"}, "FLOOR.MATH": {"a": "(número; [significância]; [modo])", "d": "Arredonda um número para baixo, para o inteiro mais próximo ou para o próximo múltiplo significativo", "ad": "é o valor que você deseja arredondar!é o múltiplo para o qual você deseja arredondar!quando determinado e diferente de zero essa função arredondará em direção a zero"}, "GCD": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o máximo divisor comum", "ad": "são de 1 a 255 valores"}, "INT": {"a": "(núm)", "d": "Arredonda um número para baixo até o número inteiro mais próximo", "ad": "é o número real que se deseja arredondar para baixo até um inteiro"}, "ISO.CEILING": {"a": "(número; [significância])", "d": "Retorna um número que é arredondado para o inteiro mais próximo ou para o múltiplo mais próximo de significância. Independentemente do sinal de núm, um valor será arredondado. No entanto, se núm ou significância for zero, zero será retornado.", "ad": "é o valor que se deseja arredondar!é o múltiplo para o qual se deseja arredondar"}, "LCM": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o mínimo múl<PERSON>lo comum", "ad": "são de 1 a 255 valores cujos mínimos múltiplos comum se deseja obter"}, "LN": {"a": "(núm)", "d": "Retorna o logaritmo natural de um número", "ad": "é o número real positivo cujo logaritmo natural você deseja obter"}, "LOG": {"a": "(núm; [base])", "d": "Retorna o logaritmo de um número em uma base especificada", "ad": "é o número real positivo cujo logaritmo você deseja!é a base do logaritmo; 10 quando não especificado"}, "LOG10": {"a": "(núm)", "d": "Retorna o logaritmo de base 10 de um número", "ad": "é o número real positivo cujo logaritmo de base 10 você deseja obter"}, "MDETERM": {"a": "(matriz)", "d": "Retorna o determinante de uma matriz", "ad": "é uma matriz numérica com um mesmo número de linhas e de colunas, um intervalo de células ou uma constante de matriz"}, "MINVERSE": {"a": "(matriz)", "d": "Retorna a matriz inversa de uma matriz", "ad": "é uma matriz numérica com um mesmo número de linhas e de colunas, um intervalo de células ou constante de matriz"}, "MMULT": {"a": "(matriz1; matriz2)", "d": "Retorna a matriz produto de duas matrizes, uma matriz com o mesmo número de linhas que a matriz1 e de colunas que a matriz2", "ad": "é a primeira matriz de números a serem multiplicados e deve possuir o mesmo número de colunas que a Matriz2 possui de linhas"}, "MOD": {"a": "(núm; divisor)", "d": "Retorna o resto da divisão após um número ter sido dividido por um divisor", "ad": "é o número cujo resto da divisão você deseja localizar!é o número pelo qual você deseja dividir 'Núm'"}, "MROUND": {"a": "(núm; múltiplo)", "d": "Retorna um número arredondado para o múltiplo desejado", "ad": "é o valor a ser arredondado!é o múltiplo para o qual se deseja arredondar núm"}, "MULTINOMIAL": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o multinômio de um conjunto de números", "ad": "são de 1 a 255 valores cujos multinômios se deseja obter"}, "MUNIT": {"a": "(dimensão)", "d": "Retorna a matriz de unidade para a dimensão especificada", "ad": "é um inteiro que especifica a dimensão da matriz da unidade que você deseja retornar"}, "ODD": {"a": "(núm)", "d": "Arredonda um número positivo para cima e um número negativo para baixo até o número ímpar inteiro mais próximo", "ad": "é o valor a ser arredondado"}, "PI": {"a": "()", "d": "<PERSON><PERSON><PERSON> o valor de Pi, 3,14159265358979, em 15 dígitos", "ad": ""}, "POWER": {"a": "(núm; potência)", "d": "Retorna o resultado de um número elevado a uma potência", "ad": "é o número de base, qualquer número real!é o expoente para o qual a base é elevada"}, "PRODUCT": {"a": "(núm1; [núm2]; ...)", "d": "Multiplica todos os números dados como argumentos", "ad": "de 1 a 255 números, valores lógicos ou representações de número em forma de texto que você deseja multiplicar"}, "QUOTIENT": {"a": "(numerador; denominador)", "d": "Retorna a parte inteira de uma divisão", "ad": "é o dividendo!é o divisor"}, "RADIANS": {"a": "(â<PERSON><PERSON>)", "d": "Converte graus em radianos", "ad": "é um ângulo em graus que se deseja converter"}, "RAND": {"a": "()", "d": "Retorna um número aleatório maior ou igual a 0 e menor que 1 (modificado quando recalculado) distribuído uniformemente", "ad": ""}, "RANDARRAY": {"a": "([linhas]; [colunas]; [min]; [max]; [inteiro])", "d": "Retorna uma matriz de números aleatórios", "ad": "o número de linhas na matriz retornada!o número de colunas na matriz retornada!o número mínimo você quer retornar!o número máximo que você quer retornar!retorna um número inteiro ou um valor decimal. VERDADEIRO para um número inteiro, FALSO para um número decimal"}, "RANDBETWEEN": {"a": "(inferior; superior)", "d": "Retorna um número aleatório entre os números especificados", "ad": "é o menor inteiro que ALEATÓRIOENTRE retornará!é o maior inteiro que ALEATÓRIOENTRE retornará"}, "ROMAN": {"a": "(núm; [forma])", "d": "Converte um algarismo arábico em romano, como texto", "ad": "é o algarismo arábico que se deseja converter!é o número que especifica o tipo de algarismo romano desejado."}, "ROUND": {"a": "(núm; núm_dígitos)", "d": "Arredonda um número até uma quantidade especificada de dígitos", "ad": "é o número que se deseja arredondar!é o número de dígitos para o qual se deseja arredondar. Números negativos são arredondados para a esquerda da vírgula decimal e zero para o inteiro mais próximo"}, "ROUNDDOWN": {"a": "(núm; núm_dígitos)", "d": "Arredonda um número para baixo até zero", "ad": "é qualquer número real que se deseja arredondado para baixo!é o número de dígitos para o qual se deseja arredondar. Números negativos são arredondados para a direita da vírgula decimal. Zero ou não especificado, para o inteiro mais próximo"}, "ROUNDUP": {"a": "(núm; núm_dígitos)", "d": "Arredonda um número para cima afastando-o de zero", "ad": "é qualquer número real que se deseja arredondado para cima!é o número de dígitos para o qual se deseja arredondar. Números negativos são arredondados para a esquerda da vírgula decimal. Zero ou não especificado, para o inteiro mais próximo"}, "SEC": {"a": "(número)", "d": "Retorna a secante de um ângulo", "ad": "é o ângulo em radianos para o qual você deseja a secante"}, "SECH": {"a": "(número)", "d": "Retorna a hiperbólica da secante de um ângulo", "ad": "é o ângulo em radianos para o qual você deseja a hiperbólica da secante"}, "SERIESSUM": {"a": "(x; n; m; coeficientes)", "d": "Retorna a soma de uma série de potência baseada na fórmula", "ad": "é o valor de entrada para a série de potência!é a potência inicial à qual se deseja elevar x!é a etapa pela qual se acrescenta n em cada termo na série!é um conjunto de coeficientes pelo qual cada potência sucessiva de x será multiplicada"}, "SIGN": {"a": "(núm)", "d": "Retorna o sinal de um número: 1 se o número for positivo, 0 se o número for zero ou -1 se o número for negativo", "ad": "é qualquer número real"}, "SIN": {"a": "(núm)", "d": "Retorna o seno de um ângulo", "ad": "é o ângulo em radianos cujo seno você deseja obter. Graus * PI()/180 = radianos"}, "SINH": {"a": "(núm)", "d": "Retorna o seno hiperbólico de um número", "ad": "é qualquer número real"}, "SQRT": {"a": "(núm)", "d": "Retorna a raiz quadrada de um número", "ad": "é o número cuja raiz quadrada você deseja calcular"}, "SQRTPI": {"a": "(núm)", "d": "Retorna a raiz quadrada de (núm * Pi)", "ad": "é o número pelo qual pi é multiplicado"}, "SUBTOTAL": {"a": "(núm_função; ;ref1]; ...)", "d": "Retorna um subtotal em uma lista ou um banco de dados", "ad": "é o número de 1 a 11 que determina a função resumo do subtotal!de 1 a 254 intervalos ou referências cujos subtotais se deseja"}, "SUM": {"a": "(núm1; [núm2]; ...)", "d": "Soma todos os números em um intervalo de células", "ad": "de 1 a 255 números a serem somados. Valores lógicos e texto são ignorados, mesmo quando digitados como argumentos"}, "SUMIF": {"a": "(intervalo; critérios; [intervalo_soma])", "d": "Adiciona as células especificadas por um determinado critério ou condição", "ad": "é o intervalo de células que se quer calculado!é o critério ou condição na forma de um número, expressão ou texto, que definem quais células serão adicionadas!são células a serem somadas. Quando não especificadas, são usadas as células do intervalo"}, "SUMIFS": {"a": "(intervalo_soma; intervalo_critérios; critérios; ...)", "d": "Adiciona as células especificadas por um dado conjunto de condições ou critérios", "ad": "são as células a serem somadas.!é o intervalo de células que se deseja avaliar com a condição dada!é a condição ou os critérios expressos como um número, uma expressão ou um texto que define quais células serão adicionadas"}, "SUMPRODUCT": {"a": "(matriz1; [matriz2]; [matriz3]; ...)", "d": "Retorna a soma dos produtos de intervalos ou matrizes correspondentes", "ad": "de 2 a 255 matrizes para as quais você deseja multiplicar e somar componentes. <PERSON><PERSON> as matrizes devem ter as mesmas dimensões"}, "SUMSQ": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a soma dos quadrados dos argumentos. Os argumentos podem ser números, matrizes, nomes ou referências a células que contenham números", "ad": "de 1 a 255 números, matrizes, nomes ou referências a matrizes cuja soma dos quadrados você deseja calcular"}, "SUMX2MY2": {"a": "(matriz_x; matriz_y)", "d": "<PERSON><PERSON> as diferenças entre dois quadrados de dois intervalos ou matrizes correspondentes", "ad": "é a primeira matriz ou intervalo de números, podendo ser um número ou nome, matriz ou referência que contenha números!é a segunda matriz ou intervalo de números, podendo ser um número ou nome, matriz ou referência que contenha números"}, "SUMX2PY2": {"a": "(matriz_x; matriz_y)", "d": "Retorna a soma total das somas dos quadrados dos números em dois intervalos ou matrizes correspondentes", "ad": "é a primeira matriz ou intervalo de números, podendo ser um número ou nome, matriz ou referência que contenha números!é a segunda matriz ou intervalo de números, podendo ser um número ou nome, matriz ou referência que contenha números"}, "SUMXMY2": {"a": "(matriz_x; matriz_y)", "d": "Soma os quadrados das diferenças em dois intervalos ou matrizes correspondentes", "ad": "é a primeira matriz ou intervalo de valores, podendo ser um número ou nome, matriz ou referência que contenha números!é a segunda matriz ou intervalo de valores, podendo ser um número ou nome, matriz ou referência que contenha números"}, "TAN": {"a": "(núm)", "d": "Retorna a tangente de um ângulo", "ad": "é o ângulo em radianos cuja tangente você deseja obter. Graus * PI()/180 = radianos"}, "TANH": {"a": "(núm)", "d": "Retorna a tangente hiperbólica de um número", "ad": "é qualquer número real"}, "TRUNC": {"a": "(núm; [núm_dígitos])", "d": "Trunca um número até um número inteiro removendo a parte decimal ou fracionária do número", "ad": "é o número que se deseja truncar!é um número que especifica a precisão da truncagem, 0 (zero) quando não especificado"}, "ADDRESS": {"a": "(núm_lin; núm_col; [núm_abs]; [a1]; [texto_planilha])", "d": "Cria uma referência de célula como texto, de acordo com números de linha e coluna específicos", "ad": "é o número da linha a ser utilizado na referência de célula: Núm_lin = 1 para linha 1!é o número da coluna a ser utilizado na referência da célula. Por exemplo, Núm_col = 4 para coluna D!especifica o tipo de referência: absoluta = 1; linha absoluta/coluna relativa = 2; linha relativa/coluna absoluta = 3; relativa = 4!é um valor lógico que especifica o estilo de referência: estilo A1 = 1 ou VERDADEIRO; estilo L1C1 = 0 ou FALSO!é o texto que especifica o nome da planilha a ser utilizada como referência externa"}, "CHOOSE": {"a": "(núm_índice; valor1; [valor2]; ...)", "d": "Escolhe um valor a partir de uma lista de valores, com base em um número de índice", "ad": "especifica qual o argumento de valor está selecionado, 'Núm_índice' deve estar entre 1 e 254 ou, uma fórmula ou uma referência a um número entre 1 e 254!de 1 a 254 números, referências de célula, nomes definidos, fórm<PERSON><PERSON>, funções ou argumentos de texto a partir dos quais ESCOLHER seleciona um valor"}, "COLUMN": {"a": "([ref])", "d": "Retorna o número da coluna de uma referência", "ad": "é a célula ou células contíguas cujo número da coluna você deseja obter. Se omitido, a célula contendo a função COL será usada"}, "COLUMNS": {"a": "(matriz)", "d": "Retorna o número de colunas contido em uma matriz ou referência", "ad": "é uma matriz, fórmula matricial ou uma referência a um intervalo de células cujo número de colunas você deseja obter"}, "FORMULATEXT": {"a": "(reference)", "d": "Retorna uma fórmula como uma cadeia de caracteres", "ad": "é uma referência a uma fórmula"}, "HLOOKUP": {"a": "(valor_procurado; matriz_tabela; núm_índice_lin; [procurar_intervalo])", "d": "Pesquisa um valor na linha superior de uma tabela ou matriz de valores e retorna o valor na mesma coluna a partir de uma linha especificada", "ad": "é o valor a ser localizado na primeira linha da tabela. Podendo ser um valor, uma referência ou uma cadeia de texto!é uma tabela de texto, números ou valores lógicos na qual são pesquisados dados. 'Matriz_tabela' pode ser uma referência a um intervalo ou um nome de intervalo!é o número da linha em 'Matriz_tabela' de onde o valor correspondente deve ser retornado. A primeira linha de valores da tabela é linha 1!é um valor lógico: para determinar a correspondência mais semelhante na linha superior (classificada em ordem crescente) = VERDADEIRO ou não especificada; determinar uma correspondência exata = FALSO"}, "HYPERLINK": {"a": "(local_vínculo; [nome_amigável])", "d": "Cria um atalho ou salto que abre um documento armazenado no disco rígido, em um servidor de rede ou na Internet", "ad": "é o texto que informa o caminho e nome de arquivo do documento a ser aberto, um local no disco rígido, um endereço UNC ou um caminho de URL!é o texto ou um número que é exibido em uma célula. Quando não especificado, a célula exibe o texto 'Local_vínculo'"}, "INDEX": {"a": "(matriz; núm_linha; [núm_coluna]!ref; núm_linha; [núm_coluna]; [núm_<PERSON><PERSON>])", "d": "Retorna um valor ou a referência da célula na interseção de uma linha e coluna específica, em um dado intervalo", "ad": "é um intervalo de células ou uma constante de matriz!seleciona a linha na matriz ou referência de onde um valor será retornado. Quando não especificado, núm_coluna é necessário!seleciona a coluna na matriz ou referência de onde um valor será retornado. Quando não especificado, núm_linha é necessário!é uma referência a um ou mais intervalos de célula!seleciona a linha na matriz ou referência de onde um valor será retornado. Quando não especificado, núm_coluna é necessário!seleciona a coluna na matriz ou referência de onde será retornado um valor. Quando não especificado, núm_linha é necessário!seleciona um intervalo na referência de onde um valor será retornado. A primeira área selecionada ou incluída é área 1, a segunda é área 2 e assim por diante"}, "INDIRECT": {"a": "(texto_ref; [a1])", "d": "Retorna uma referência indicada por um valor de texto", "ad": "é uma referência a uma célula que contém uma referência de estilo A1 ou L1C1, um nome definido como uma referência ou uma referência a uma célula como uma cadeia de texto!é um valor lógico que especifica o tipo de referência contido em texto_ref; estilo L1C1 = FALSO; estilo A1 = VERDADEIRO ou não especificado"}, "LOOKUP": {"a": "(valor_procurado; vetor_proc; [vetor_result]!valor_procurado; matriz)", "d": "Procura um valor a partir de um intervalo de linha ou coluna ou de uma matriz. Fornecido para manter a compatibilidade com versões anteriores", "ad": "é o valor que PROC pesquisa no Vetor_proc, podendo ser um número, texto, um valor lógico, um nome ou uma referência a um valor!é o intervalo que contém somente uma linha ou uma coluna de texto, números ou valores lógicos, colocados em order crescente!é um intervalor que contém apneas uma linha ou coluna com o mesmo tamanho de Vetor_proc!é um valor que PROC pesquisa em uma Matriz, podendo ser um número, texto, um valor lógico, um nome ou uma referência a um valor!é um intervalo de células que contém texto, números ou valores lógicos que se deseja comparar com o valor_procurado"}, "MATCH": {"a": "(valor_procurado; matriz_procurada; [tipo_correspondência])", "d": "Retorna a posição relativa de um item em uma matriz que corresponda a um valor específico em uma ordem específica", "ad": "é o valor utilizado para encontrar o valor desejado na matriz, podendo ser um número, texto, um valor lógico ou um nome que faça referência a um destes valores!é um intervalo contíguo de células que contém valores possíveis de procura, uma matriz de valores ou uma referência a uma matriz!é um número 1, 0 ou -1 indicando qual valor retornar."}, "OFFSET": {"a": "(ref; lins; cols; [altura]; [largura])", "d": "Retorna uma referência a um intervalo que possui um número específico de linhas e colunas com base em uma referência especificada", "ad": "é a referência em que se deseja basear o deslocamento, uma referência a uma célula ou intervalo de células adjacentes!é o número de linhas, acima e abaixo, ao qual você deseja que a célula superior esquerda do resultado faça referência!é o número de colunas, à esquerda ou à direita, ao qual você deseja que a célula superior esquerda do resultado faça referência!é a altura, em número de linhas, na qual você deseja que o resultado se apresente, quando não especificada terá a mesma altura que 'Ref'!é a largura, em número de colunas, na qual você deseja que o resultado se apresente, quando não especificada terá a mesma largura que 'Ref'"}, "ROW": {"a": "([ref])", "d": "Retorna o número da linha de uma referência", "ad": "é a célula ou intervalo de célula cujo número da linha você deseja obter. Quando não especificado, retorna a célula que contém a função LIN"}, "ROWS": {"a": "(matriz)", "d": "Retorna o número de linhas em uma referência ou matriz", "ad": "é uma matriz, uma fórmula matricial ou uma referência a um intervalo de células cujo número de linhas você deseja obter"}, "TRANSPOSE": {"a": "(matriz)", "d": "Converte um intervalo de células vertical em um intervalo horizontal e vice-versa", "ad": "é um intervalo de células em uma planilha ou matriz de valores que se deseja transpor"}, "UNIQUE": {"a": "(matriz; [by_col]; [exactly_once])", "d": " Retorna os valores exclusivos de um intervalo ou matriz.", "ad": " o intervalo ou matriz do qual retornar linhas ou colunas exclusivas!é um valor lógico: comparar linhas entre si e retornar as linhas exclusivas = FALSE ou omitido; comparar colunas entre si e retornar as colunas exclusivas = TRUE!é um valor lógico: retornar linhas ou colunas que ocorrem exatamente uma vez da matriz = TRUE; retornar todas as linhas ou colunas distintas da matriz = FALSE ou omitido"}, "VLOOKUP": {"a": "(valor_procurado; matriz_tabela; núm_índice_coluna; [procurar_intervalo])", "d": "Procura um valor na primeira coluna à esquerda de uma tabela e retorna um valor na mesma linha de uma coluna especificada. Como padrão, a tabela deve estar classificada em ordem crescente", "ad": "é o valor a ser localizado na primeira coluna de uma tabela, podendo ser um valor, uma referência ou uma cadeia de texto!é uma tabela de texto, números ou valores lógicos cujos dados são recuperados. 'Matriz_tabela' pode ser uma referência a um intervalo ou a um nome de intervalo!é o número da coluna em 'Matriz_tabela' a partir do qual o valor correspondente deve ser retornado. A primeira coluna de valores na tabela é a coluna 1!é um valor lógico: para encontrar a correspondência mais próxima na primeira coluna (classificada em ordem crescente) = VERDADEIRO ou não especificado. Para encontrar a correspondência exata = FALSO"}, "XLOOKUP": {"a": "(pesquisa_valor; pesquisa_matriz; matriz_retorno; [se_não_encontrada]; [modo_correspondência]; [modo_pesquisa])", "d": "Procura uma correspondência em um intervalo ou matriz e retorna o item correspondente de uma segunda matriz ou intervalo. Por padr<PERSON>, é usada uma correspondência exata", "ad": "é o valor a ser pesquisado!é a matriz ou o intervalo para pesquisar!é a matriz ou o intervalo para retornar!retornado se nenhuma correspondência for encontrada!especificar como corresponder pesquisa_valor com os valores contidos em pesquisa_matriz!especificar o modo de pesquisa a ser usado. Por padr<PERSON>, será usada uma pesquisa do primeiro para o último"}, "CELL": {"a": "(tipo_info; [referência])", "d": "Retorna informações sobre a formatação, o local ou o conteúdo de uma célula", "ad": "um valor de texto que especifica que tipo de informações de célula você deseja retornar!a célula sobre a qual você deseja informações"}, "ERROR.TYPE": {"a": "(val_erro)", "d": "Retorna um número que corresponde a um valor de erro.", "ad": "é o valor de erro cujo número de identificação você deseja obter, podendo ser um valor de erro real ou uma referência a uma célula contendo um valor de erro"}, "ISBLANK": {"a": "(valor)", "d": "Verifica se uma referência está sendo feita a uma célula vazia e retorna VERDADEIRO ou FALSO", "ad": "é a célula ou um nome que faz referência à célula a ser testada"}, "ISERR": {"a": "(valor)", "d": "Verifica se um valor é um erro diferente de #N/D e retorna VERDADEIRO ou FALSO", "ad": "é o valor que você deseja testar. O valor pode se referir a uma célula, a uma fórmula ou a um nome que faz referência a uma célula, fórmula ou valor"}, "ISERROR": {"a": "(valor)", "d": "Verifica se um valor é um erro e retorna VERDADEIRO ou FALSO", "ad": "é o valor que você deseja testar. Valor pode se referir a uma célula, a uma fórmula ou a um nome que faz referência a uma célula, fórmula ou valor"}, "ISEVEN": {"a": "(núm)", "d": "Retorna VERDADEIRO se o número for par", "ad": "é o valor a ser testado"}, "ISFORMULA": {"a": "(referência)", "d": "Verifica se uma referência é uma célula que contém uma fórmula e retorna VERDADEIRO ou FALSO", "ad": "é uma referência à célula que você deseja testar. A referência pode ser uma referência de célula, fórmula ou nome que se refere a uma célula"}, "ISLOGICAL": {"a": "(valor)", "d": "Verifica se um valor é lógico (VERDADEIRO ou FALSO) e retorna VERDADEIRO ou FALSO", "ad": "é o valor que se deseja testar. 'Val<PERSON>' pode se referir a uma célula, a uma fórmula ou a um nome que faz referência a uma célula, fórmula ou valor"}, "ISNA": {"a": "(valor)", "d": "Verifica se um valor é #N/D e retorna VERDADEIRO ou FALSO", "ad": "é o valor que se deseja testar. 'Val<PERSON>' pode se referir a uma célula, a uma fórmula ou a um nome que faz referência a uma célula, fórmula ou valor"}, "ISNONTEXT": {"a": "(valor)", "d": "Verifica se um valor não é texto (células vazias não são texto) e retorna VERDADEIRO ou FALSO", "ad": "é o valor que se deseja testar: uma célula, uma fórmula ou um nome que faz referência a uma célula, fórmula ou valor"}, "ISNUMBER": {"a": "(valor)", "d": "Verifica se um valor é um número e retorna VERDADEIRO ou FALSO", "ad": "é o valor que se deseja testar. 'Val<PERSON>' pode se referir a uma célula, a uma fórmula ou a um nome que faz referência a uma célula, fórmula ou valor"}, "ISODD": {"a": "(núm)", "d": "Retorna VERDADEIRO se o número for ímpar", "ad": "é o valor a ser testado"}, "ISREF": {"a": "(valor)", "d": "Verifica se um valor é uma referência e retorna VERDADEIRO ou FALSO", "ad": "é o valor que se deseja testar. 'Val<PERSON>' pode se referir a uma célula, a uma fórmula ou a um nome que faz referência a uma célula, fórmula ou valor"}, "ISTEXT": {"a": "(valor)", "d": "Verifica se um valor é texto e retorna VERDADEIRO ou FALSO", "ad": "é o valor que se deseja testar. 'Val<PERSON>' pode se referir a uma célula, a uma fórmula ou a um nome que faz referência a uma célula, fórmula ou valor"}, "N": {"a": "(valor)", "d": "Converte um valor não numérico em um número, datas em números de série, VERDADEIRO em 1 e qualquer outro valor em 0 (zero)", "ad": "é o valor que se deseja converter"}, "NA": {"a": "()", "d": "Retorna o valor de erro #N/D (valor não disponível)", "ad": ""}, "SHEET": {"a": "([valor])", "d": "Retorna o número da folha da folha de referência", "ad": " é o nome de uma planilha ou uma referência que você deseja que o número da folha de. Se omitido, o número da planilha que contém a função será retornado"}, "SHEETS": {"a": "([referência])", "d": "Retorna o número de planilhas em uma referência", "ad": " é uma referência para o qual você deseja saber o número de planilhas que ela contém. Se omitido, o número das planilhas na pasta de trabalho que contém a função será retornado"}, "TYPE": {"a": "(valor)", "d": "Retorna um número inteiro que indica o tipo de dados de um valor: número = 1, texto = 2, valor lógico = 4, valor de erro = 16, matriz = 64;dados compostos = 128", "ad": "pode ser qualquer valor"}, "AND": {"a": "(lógico1; [lógico2]; ...)", "d": "Verifica se os argumentos são VERDADEIRO e retorna VERDADEIRO se todos os argumentos forem VERDADEIRO", "ad": "de 1 a 255 condições a serem testadas que podem ter valor VERDADEIRO ou FALSO e podem ter valores lógicos, matrizes ou referências"}, "FALSE": {"a": "()", "d": "Retorna o valor lógico FALSO", "ad": ""}, "IF": {"a": "(teste_lógico; [valor_se_verdadeiro]; [valor_se_falso])", "d": "Verifica se uma condição foi satisfeita e retorna um valor se for VERDADEIRO e retorna um outro valor se for FALSO", "ad": "é qualquer valor ou expressão que pode ser avaliada como VERDADEIRO ou FALSO!é o valor retornado se 'Teste_lógico' for VERDADEIRO. Quando não especificado, é retornado VERDADEIRO. Você pode aninhar até sete funções SE!é o valor retornado se 'Teste_lógico' for FALSO. Quando não especificado, é retornado FALSO"}, "IFS": {"a": "(teste_lógico; valor_se_verdadeiro; ...)", "d": "Verifica se uma ou mais condições são atendidas e retorna um valor correspondente à primeira condição VERDADEIRA", "ad": "é qualquer valor ou expressão que pode ser avaliada como VERDADEIRO ou FALSO!é o valor retornado se Teste_lógico for VERDADEIRO"}, "IFERROR": {"a": "(valor; valor_se_erro)", "d": "Retorna valor_se_erro se a expressão for um erro ; caso contr<PERSON>rio, retorna o valor da expressão", "ad": "é qualquer valor, expressão ou referência!é qualquer valor, expressão ou referência"}, "IFNA": {"a": "(valor; valor_se_na)", "d": "Retorna o valor que você especificar se a expressão resolver para #N/A, caso contr<PERSON>rio, retorna o resultado da expressão", "ad": "é qualquer valor ou expressão ou referência!é qualquer valor ou expressão ou referência"}, "NOT": {"a": "(lógico)", "d": "Inverte FALSO para VERDADEIRO, ou VERDADEIRO para FALSO", "ad": "é o valor ou expressão que podem ser avaliados como VERDADEIRO ou FALSO"}, "OR": {"a": "(lógico1; [lógico2]; ...)", "d": "Verifica se algum argumento é VERDADEIRO e retorna VERDADEIRO ou FALSO. Retorna FALSO somente se todos os argumentos forem FALSO", "ad": "de 1 a 255 condições que você deseja testar, podendo ser VERDADEIRO ou FALSO"}, "SWITCH": {"a": "(expressão; valor1; resultado1; [padrão_ou_valor2]; [resultado2]; ...)", "d": "Avalia uma expressão em uma lista de valores e retorna o resultado correspondente para o primeiro valor coincidente. Se não houver nenhuma correspondência, será retornado um valor padrão opcional", "ad": "é uma expressão a ser avaliada!é um valor a ser comparado com expressão!é um resultado a ser retornado se o valor correspondente corresponde a expressão"}, "TRUE": {"a": "()", "d": "Retorna o valor lógico VERDADEIRO", "ad": ""}, "XOR": {"a": "(lógico1; [lógico2]; ...)", "d": "Retorna uma lógica 'Exclusivo Ou' de todos os argumentos", "ad": "são de 1 a 254 condições que você deseja testar e que podem ser VERDADEIRAS ou FALSAS e podem ser valores lógicos, matrizes ou referências"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retorna o texto que está antes dos caracteres delimitadores.", "ad": "O texto que você deseja pesquisar pelo delimitador.!O caractere ou cadeia de caracteres usados como delimitador.!A ocorrência desejada do delimitador. O padrão é 1. Um número negativo pesquisa a partir do final.!Pesquisa no texto uma correspondência de delimitador. Por padrão, é feita uma correspondência que diferencia maiúsculas de minúsculas.!Se compara o delimitador com o final do texto. Por padr<PERSON>, eles não correspondem.!Retornado se nenhuma correspondência for encontrada. <PERSON>r padr<PERSON>, #N/A é retornado."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retorna o texto que está depois dos caracteres delimitadores.", "ad": "O texto que você deseja pesquisar pelo delimitador.!O caractere ou cadeia de caracteres usados como delimitador.!A ocorrência desejada do delimitador. O padrão é 1. Um número negativo pesquisa a partir do final.!Pesquisa no texto uma correspondência de delimitador. Por padrão, é feita uma correspondência que diferencia maiúsculas de minúsculas.!Se compara o delimitador com o final do texto. Por padr<PERSON>, eles não correspondem.!Retornado se nenhuma correspondência for encontrada. <PERSON>r padr<PERSON>, #N/A é retornado."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Divide o texto em linhas ou colunas usando delimitadores.", "ad": "O texto a ser dividido!Caractere ou cadeia de caracteres para dividir as colunas.!Caractere ou cadeia de caracteres para dividir as linhas.!Se ignora as células vazias. O padrão é FALSE.!Pesquisa no texto uma correspondência de delimitador. Por padrão, é feita uma correspondência que diferencia maiúsculas de minúsculas.!O valor a ser usado para preenchimento. Por padr<PERSON>, #N/A é usado."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": " Encapsula um vetor de linha ou coluna após um número especificado de valores.", "ad": "O vetor ou a referência a ser encapsulada.!O número máximo de valores por linha.!O valor com o qual preencher. O padrão é #N/A."}, "VSTACK": {"a": "(matriz1, [matriz2], ...)", "d": "Empilha verticalmente matrizes em uma matriz.", "ad": "Uma matriz ou referência a ser empilhada."}, "HSTACK": {"a": "(matriz1, [matriz2], ...)", "d": "Empilha horizontalmente matrizes em uma matriz.", "ad": "Uma matriz ou referência a ser empilhada."}, "CHOOSEROWS": {"a": "(matriz, row_num1, [row_num2], ...)", "d": "Retorna linhas de uma matriz ou referência.", "ad": "A matriz ou referência que contém as linhas a serem retornadas!O número da linha a ser retornada."}, "CHOOSECOLS": {"a": "(matriz, col_num1, [col_num2], ...)", "d": "<PERSON><PERSON><PERSON> as colu<PERSON> de uma matriz ou referência", "ad": "A matriz ou referência que contém as colunas a serem retornadas!O número da coluna a ser retornada."}, "TOCOL": {"a": "(matriz, [ignorar], [scan_by_column])", "d": "Retorna a matriz como uma coluna.", "ad": "A matriz ou referência a ser retornada como uma coluna.!Se deseja ignorar determinados tipos de valores. Por padr<PERSON>, nenhum valor é ignorado.!Examinar a matriz por coluna. Por padrão, a matriz é verificada por linha."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Retorna a matriz como uma linha.", "ad": "A matriz ou referência a ser retornada como uma linha.!Se deve ignorar certos tipos de valores. Por padr<PERSON>, nenhum valor é ignorado.!Verifique a matriz por coluna. Por padrão, a matriz é verificada por linha."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": " Encapsula um vetor de linha ou coluna após um número especificado de valores.", "ad": " O vetor ou a referência a ser encapsulada.!O número máximo de valores por coluna.!O valor com o qual preencher. O padrão é #N/A."}, "TAKE": {"a": "(matriz, lin<PERSON>, [colunas])", "d": "<PERSON><PERSON><PERSON> linhas ou colunas de início ou té<PERSON><PERSON> da matriz.", "ad": "A matriz da qual as linhas ou colunas serão removidas.!O número de linhas a serem tomadas. Um valor negativo removido do final da matriz.!O número de colunas a serem tomadas. Um valor negativo removido do final da matriz."}, "DROP": {"a": "(matriz, lin<PERSON>, [colunas])", "d": "Remove linhas ou colunas de início ou té<PERSON><PERSON> da matriz.", "ad": "A matriz da qual as linhas ou colunas serão removidas.!O número de linhas a serem removidas. Um valor negativo cai do final da matriz.!O número de colunas a serem removidas. Um valor negativo cai do final da matriz."}, "SEQUENCE": {"a": "(lin<PERSON>, [colunas], [in<PERSON><PERSON>], [final])", "d": "Retorna uma sequência de números", "ad": "o número de linhas a serem retornadas!o número de colunas a serem retornadas!o primeiro número da sequência!o valor com que incrementar cada valor subsequente na sequência"}, "EXPAND": {"a": "(matriz, lin<PERSON>, [colunas], [preenchimento_com])", "d": "Expande uma matriz para as dimensões especificadas.", "ad": "A matriz a ser expandida.!O número de linhas na matriz expandida. Se estiver ausente, as linhas não serão expandidas.!O número de colunas na matriz expandida. Se estiver ausente, as colunas não serão expandidas.!O valor com o qual preencher. O padrão é #N/A."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "Retorna a posição relativa de um item em uma matriz. <PERSON><PERSON> padr<PERSON>, uma correspondência exata é necessária", "ad": "é o valor a ser pesquisado!é a matriz ou intervalo a ser pesquisado!especifique como combinar o valor_procurado com os valores em matriz_procurada!especifique o modo de pesquisa a ser usado. Por padr<PERSON>, uma pesquisa do primeiro ao último será usada"}, "FILTER": {"a": "(matriz, incluir, [se_vazia])", "d": "Filtrar um intervalo ou uma matriz", "ad": "o intervalo ou a matriz para filtrar!uma matriz de valores de boolianos onde TRUE representa uma linha ou uma coluna a ser retida!retornada se nenhum item for retido"}, "ARRAYTOTEXT": {"a": "(matriz, [formato])", "d": "Retorna uma representação de texto de uma matriz", "ad": " a matriz a ser representada como texto! o formato do texto"}, "SORT": {"a": "(matriz, [índice_de_classificação], [ordem_de_classificação], [por_col])", "d": "Classifica um intervalo ou uma matriz", "ad": "o intervalo ou a matriz para classificar!um número que indica a linha ou a coluna pela qual classificar!um número que indica a ordem de classificação desejada; 1 para ordem ascendente (padrão), -1 para ordem descendente!um valor lógico indicando a direção de classificação desejada: FALSE para classificar por linha (padrão), True para classificar por coluna"}, "SORTBY": {"a": "(matriz, índice_de_classificação, [ordem_de_classificação], ...)", "d": "Classifica um intervalo ou matriz com base nos valores de uma matriz ou intervalo correspondente", "ad": "a matriz ou o intervalo a ser classificado!o intervalo ou a matriz que serve como base para a classificação!um número indicando a ordem de classificação desejada,1 para ordem ascendente (padrão), -1 para ordem decrescente"}, "GETPIVOTDATA": {"a": "(campo_dados; tab_din; [campo]; [item]; ...)", "d": "Extrai os dados armazenados em uma tabela dinâmica", "ad": "é o nome do campo de dados do qual se deseja extrair dados!é uma referência a uma célula ou a um intervalo de células da tabela dinâmica que contém os dados que você deseja recuperar!campo ao qual se deseja fazer referência!item de campo ao qual se deseja fazer referência"}, "IMPORTRANGE": {"a": "(url_da_planilha; string_do_intervalo)", "d": "Importa um intervalo de células da planilha especificada.", "ad": "o URL da planilha da qual os dados serão importados!o intervalo a ser importado"}}