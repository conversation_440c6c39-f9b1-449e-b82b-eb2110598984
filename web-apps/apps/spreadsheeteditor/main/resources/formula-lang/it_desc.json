{"DATE": {"a": "(anno; mese; giorno)", "d": "Restituisce il numero che rappresenta la data in codice data-ora.", "ad": "è un numero compreso tra 1900 o 1904 (a seconda del sistema data della cartella di lavoro) e 9999!è un numero compreso tra 1 e 12 che rappresenta il mese dell'anno!è un numero compreso tra 1 e 31 che rappresenta il giorno del mese"}, "DATEDIF": {"a": "(data_inizio; data_fine; unit)", "d": "Restituisce la differenza tra le due date in ingresso (data_inizio e data_fine), basato sull'unità (unit) specificata", "ad": "Data che rappresenta la prima o la data iniziale di un determinato periodo!Data che rappresenta l'ultima data, o data finale, del periodo!Tipo di informazioni da restituire"}, "DATEVALUE": {"a": "(data)", "d": "Converte una data in formato testo in un numero che rappresenta la data nel codice data-ora.", "ad": "è il testo che rappresenta una data in formato data di Spreadsheet Editor, compreso tra 01/01/1900 o 01/01/1904 (a seconda del sistema data della cartella di lavoro) e 31/12/9999"}, "DAY": {"a": "(num_seriale)", "d": "Restituisce il giorno del mese, un numero compreso tra 1 e 31.", "ad": "è un numero nel codice data-ora utilizzato da Spreadsheet Editor"}, "DAYS": {"a": "(data_fine; data_inizio)", "d": "Restituisce il numero di giorni che intercorre tra due date.", "ad": "data_iniziale e data_finale sono le due date per le quali si vuole conoscere il numero di giorni che intercorre tra di esse!data_iniziale e data_finale sono le due date per le quali si vuole conoscere il numero di giorni che intercorre tra di esse"}, "DAYS360": {"a": "(data_iniziale; data_finale; [metodo])", "d": "Restituisce il numero di giorni compresi tra due date sulla base di un anno di 360 giorni (dodici mesi di 30 giorni).", "ad": "data_iniziale e data_finale sono le due date che delimitano il periodo di cui si desidera conoscere il numero di giorni!data_iniziale e data_finale sono le due date che delimitano il periodo di cui si desidera conoscere il numero di giorni!è un valore logico che specifica il metodo di calcolo: U.S. (NASD) = FALSO o omesso; Europeo = VERO."}, "EDATE": {"a": "(data_iniziale; mesi)", "d": "Restituisce il numero seriale della data il cui mese è precedente o successivo a quello della data iniziale, a seconda del numero indicato dall'argomento mesi.", "ad": "è la data iniziale espressa come numero seriale!è il numero di mesi precedenti o successivi alla data iniziale"}, "EOMONTH": {"a": "(data_iniziale; mesi)", "d": "Restituisce il numero seriale dell'ultimo giorno del mese precedente o successivo di un numero specificato di mesi.", "ad": "è un numero seriale che rappresenta la data iniziale!è il numero di mesi precedenti o successivi alla data iniziale"}, "HOUR": {"a": "(num_seriale)", "d": "Restituisce l'ora come numero compreso tra 0 e 23.", "ad": "è un numero nel codice data-ora utilizzato da Spreadsheet Editor o testo in formato ora, quale 16.48.00"}, "ISOWEEKNUM": {"a": "(data)", "d": "Restituisce il numero settimana ISO dell'anno per una data specificata", "ad": "è il codice data-ora usato da Spreadsheet Editor per il calcolo della data e dell'ora"}, "MINUTE": {"a": "(num_seriale)", "d": "Restituisce il minuto, un numero compreso tra 0 e 59.", "ad": "è un numero nel codice data-ora utilizzato da Spreadsheet Editor o testo in formato ora, quale 16.48.00"}, "MONTH": {"a": "(num_seriale)", "d": "Restituisce il mese, un numero compreso tra 1 (gennaio) e 12 (dicembre).", "ad": "è un numero nel codice data-ora utilizzato da Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(data_iniziale; data_finale; [vacanze])", "d": "Restituisce il numero dei giorni lavorativi compresi tra due date", "ad": "è la data iniziale espressa come numero seriale.!è la data finale espressa come numero seriale!è un insieme facoltativo di una o più date, espresse come numero seriale, che rappresentano i giorni da escludere dal calendario lavorativo, come le feste nazionali, locali e i permessi retribuiti"}, "NETWORKDAYS.INTL": {"a": "(data_iniziale; data_finale; [festivi]; [vacanze])", "d": "Restituisce il numero dei giorni lavorativi compresi tra due date con parametri di giorni festivi personalizzati", "ad": "è la data iniziale espressa come numero seriale.!è la data finale espressa come numero seriale!è un numero o una stringa che specifica i giorni festivi!è un set facoltativo di una o più date, espresse come numero seriale, che rappresentano i giorni da escludere dal calendario lavorativo, come le feste nazionali, locali e i permessi retribuiti"}, "NOW": {"a": "()", "d": "Restituisce la data e l'ora correnti nel formato data-ora.", "ad": ""}, "SECOND": {"a": "(num_seriale)", "d": "Restituisce il secondo, un numero compreso tra 0 e 59.", "ad": "è un numero nel codice data-ora utilizzato da Spreadsheet Editor o testo in formato ora, quale 16.48.23"}, "TIME": {"a": "(ora; minuto; secondo)", "d": "Converte ore, minuti e secondi forniti come numeri in un un numero seriale, formattato in modo appropriato.", "ad": "è un numero compreso tra 0 e 23 che rappresenta l'ora!è un numero compreso tra 0 e 59 che rappresenta i minuti!è un numero compreso tra 0 e 59 che rappresenta i secondi"}, "TIMEval": {"a": "(ora)", "d": "Restituisce il numero seriale del tempo"}, "TODAY": {"a": "()", "d": "Restituisce la data corrente nel formato data.", "ad": ""}, "WEEKDAY": {"a": "(num_seriale; [tipo_restituito])", "d": "Restituisce un numero compreso tra 1 e 7 che identifica il giorno della settimana di una data.", "ad": "è un numero che rappresenta una data!è un numero: per domenica=1 fino a sabato=7 utilizzare 1; per lunedì=1 fino a domenica=7 utilizzare 2; per lunedì=0 fino a domenica=6 utilizzare 3"}, "WEEKNUM": {"a": "(num_seriale; [tipo_restituito])", "d": "Restituisce il numero della settimana dell'anno.", "ad": "è il codice data-ora utilizzato da Spreadsheet Editor per il calcolo della data e dell'ora!è un numero (1 o 2) che determina il tipo del valore restituito"}, "WORKDAY": {"a": "(data_iniziale; giorni; [vacanze])", "d": "Restituisce la data, espressa come numero seriale, del giorno precedente o successivo a un numero specificato di giorni lavorativi.", "ad": "è la data iniziale espressa come numero seriale!è il numero dei giorni lavorativi precedenti o successivi a 'data_iniziale'!è una matrice facoltativa con una o più date, espresse come numero seriale, che rappresentano i giorni da escludere dal calendario lavorativo, come le feste nazionali, locali e i permessi retribuiti"}, "WORKDAY.INTL": {"a": "(data_iniziale; giorni; [festivi]; [vacanze])", "d": "Restituisce la data, espressa come numero seriale, del giorno precedente o successivo a un numero specificato di giorni lavorativi con parametri di giorni festivi personalizzati.", "ad": "è la data iniziale espressa come numero seriale!è il numero dei giorni lavorativi precedenti o successivi a 'data_iniziale'!è un numero o una stringa che specifica i giorni festivi!è una matrice facoltativa con una o più date, espresse come numero seriale, che rappresentano i giorni da escludere dal calendario lavorativo, come le feste nazionali, locali e i permessi retribuiti"}, "YEAR": {"a": "(num_seriale)", "d": "Restituisce l'anno di una data, un intero nell'intervallo compreso tra 1900 e 9999.", "ad": "è un numero nel codice data-ora utilizzato da Spreadsheet Editor"}, "YEARFRAC": {"a": "(data_iniziale; data_finale; [base])", "d": "Restituisce la frazione dell'anno corrispondente al numero dei giorni complessivi compresi tra 'data_iniziale' e 'data_finale'.", "ad": "è la data iniziale espressa in numero seriale!è la data finale espressa in numero seriale!è il tipo di base da utilizzare per il conteggio dei giorni"}, "BESSELI": {"a": "(x; n)", "d": "Restituisce la funzione di Bessel modificata In(x).", "ad": "è il valore in cui calcolare la funzione!è l'ordine della funzione di Bessel"}, "BESSELJ": {"a": "(x; n)", "d": "Restituisce la funzione di Bessel Jn(x).", "ad": "è il valore in cui calcolare la funzione!è l'ordine della funzione di Bessel"}, "BESSELK": {"a": "(x; n)", "d": "Restituisce la funzione di Bessel modificata Kn(x).", "ad": "è il valore in cui calcolare la funzione!è l'ordine della funzione"}, "BESSELY": {"a": "(x; n)", "d": "Restituisce la funzione di Bessel Yn(x).", "ad": "è il valore in cui calcolare la funzione!è l'ordine della funzione"}, "BIN2DEC": {"a": "(num)", "d": "Converte un numero binario in decimale.", "ad": "è il numero binario da convertire"}, "BIN2HEX": {"a": "(num; [cifre])", "d": "Converte un numero binario in esadecimale.", "ad": "è il numero binario da convertire!è il numero di caratteri da utilizzare"}, "BIN2OCT": {"a": "(num; [cifre])", "d": "Converte un numero binario in ottale.", "ad": "è il numero binario da convertire!è il numero di caratteri da utilizzare"}, "BITAND": {"a": "(num1; num2)", "d": "Restituisce un 'AND' bit per bit di due numeri", "ad": "è la rappresentazione decimale del numero binario da calcolare!è la rappresentazione decimale del numero binario da calcolare"}, "BITLSHIFT": {"a": "(num; bit_spostamento)", "d": "Restituisce un numero spostato a sinistra dei bit indicati in bit_spostamento", "ad": "è la rappresentazione decimale del numero binario da calcolare!è il numero di bit di cui si vuole spostare num a sinistra"}, "BITOR": {"a": "(num1; num2)", "d": "Restituisce un 'OR' bit per bit di due numeri", "ad": "è la rappresentazione decimale del numero binario da calcolare!è la rappresentazione decimale del numero binario da calcolare"}, "BITRSHIFT": {"a": "(num; bit_spostamento)", "d": "Restituisce un numero spostato a destra dei bit indicati in bit_spostamento", "ad": "è la rappresentazione decimale del numero binario da calcolare!è il numero di bit di cui si vuole spostare num a destra"}, "BITXOR": {"a": "(num1; num2)", "d": "Restituisce un 'OR esclusivo' bit per bit di due numeri", "ad": "è la rappresentazione decimale del numero binario da calcolare!è la rappresentazione decimale del numero binario da calcolare"}, "COMPLEX": {"a": "(parte_reale; coeff_imm; [suffisso])", "d": "Converte la parte reale e il coefficiente dell'immaginario in un numero complesso.", "ad": "è la parte reale del numero complesso!è il coefficiente dell'immaginario del numero complesso!è il suffisso per la parte immaginaria del numero complesso"}, "DEC2BIN": {"a": "(num; [cifre])", "d": "Converte un numero decimale in binario.", "ad": "è l'intero decimale da convertire!è il numero di caratteri da utilizzare"}, "DEC2HEX": {"a": "(num; [cifre])", "d": "Converte un numero decimale in esadecimale.", "ad": "è l'intero decimale da convertire!è il numero di caratteri da utilizzare"}, "DEC2OCT": {"a": "(num; [cifre])", "d": "Converte un numero decimale in ottale.", "ad": "è l'intero decimale da convertire!è il numero di caratteri da utilizzare"}, "DELTA": {"a": "(num1; [num2])", "d": "Verifica se due numeri sono uguali", "ad": "è il primo numero.!è il secondo numero"}, "ERF": {"a": "(limite_inf; [limite_sup])", "d": "Restituisce la funzione di errore.", "ad": "è il limite inferiore di integrazione per FUNZ.ERRORE!è il limite superiore di integrazione per FUNZ.ERRORE"}, "ERF.PRECISE": {"a": "(X)", "d": "Restituisce la funzione di errore", "ad": "è il limite inferiore di integrazione per FUNZ.ERRORE.PRECISA"}, "ERFC": {"a": "(x)", "d": "Restituisce la funzione di errore complementare.", "ad": "è il limite inferiore di integrazione per FUNZ.ERRORE"}, "ERFC.PRECISE": {"a": "(X)", "d": "Restituisce la funzione di errore complementare", "ad": "è il limite inferiore di integrazione per FUNZ.ERRORE.COMP.PRECISA"}, "GESTEP": {"a": "(num; [val_soglia])", "d": "Verifica se un numero è maggiore di un valore soglia", "ad": "è il valore da confrontare con val_soglia!è il valore soglia"}, "HEX2BIN": {"a": "(num; [cifre])", "d": "Converte un numero esadecimale in binario.", "ad": "è il numero esadecimale da convertire!è il numero di caratteri da utilizzare"}, "HEX2DEC": {"a": "(num)", "d": "Converte un numero esadecimale in decimale.", "ad": "è il numero esadecimale da convertire"}, "HEX2OCT": {"a": "(num; [cifre])", "d": "Converte un numero esadecimale in ottale.", "ad": "è il numero esadecimale da convertire!è il numero di caratteri da utilizzare"}, "IMABS": {"a": "(num_comp)", "d": "Restituisce il valore assoluto (modulo) di un numero complesso.", "ad": "è il numero complesso di cui calcolare il valore assoluto"}, "IMAGINARY": {"a": "(num_comp)", "d": "Restituisce il coefficiente dell'immaginario di un numero complesso.", "ad": "è il numero complesso di cui ottenere il coefficiente dell'immaginario"}, "IMARGUMENT": {"a": "(num_comp)", "d": "Restituisce l'argomento teta, un angolo espresso in radianti.", "ad": "è il numero complesso di cui calcolare l'argomento"}, "IMCONJUGATE": {"a": "(num_comp)", "d": "Restituisce il complesso coniugato di un numero complesso.", "ad": "è il numero complesso di cui ottenere il coniugato"}, "IMCOS": {"a": "(num_comp)", "d": "Restituisce il coseno di un numero complesso.", "ad": "è il numero complesso di cui calcolare il coseno"}, "IMCOSH": {"a": "(num_comp)", "d": "Restituisce il coseno iperbolico di un numero complesso", "ad": "è il numero complesso di cui calcolare il coseno iperbolico"}, "IMCOST": {"a": "(num_comp)", "d": "Restituisce la cotangente di un numero complesso"}, "IMCSC": {"a": "(num_comp)", "d": "Restituisce la cosecante di un numero complesso", "ad": "è il numero complesso di cui calcolare la cosecante"}, "IMCSCH": {"a": "(num_comp)", "d": "Restituisce la cosecante iperbolica di un numero complesso", "ad": "è il numero complesso di cui calcolare la cosecante iperbolica"}, "IMDIV": {"a": "(num_comp1; num_comp2)", "d": "Restituisce il quoziente di due numeri complessi.", "ad": "è il numeratore o dividendo complesso!è il denominatore o divisore complesso"}, "IMEXP": {"a": "(num_comp)", "d": "Restituisce l'esponenziale di un numero complesso.", "ad": "è il numero complesso di cui calcolare l'esponenziale"}, "IMLN": {"a": "(num_comp)", "d": "Restituisce il logaritmo naturale di un numero complesso.", "ad": "è il numero complesso di cui calcolare il logaritmo naturale"}, "IMLOG10": {"a": "(num_comp)", "d": "Restituisce il logaritmo in base 10 di un numero complesso.", "ad": "è il numero complesso di cui calcolare il logaritmo"}, "IMLOG2": {"a": "(num_comp)", "d": "Restituisce il logaritmo in base 2 di un numero complesso.", "ad": "è il numero complesso di cui calcolare il logaritmo in base 2"}, "IMPOWER": {"a": "(num_comp; num)", "d": "Restituisce un numero complesso elevato a un esponente intero.", "ad": "è il numero complesso da elevare a potenza!è l'esponente a cui elevare il numero complesso"}, "IMPRODUCT": {"a": "(num_comp1; [num_comp2]; ...)", "d": "Restituisce il prodotto di numeri complessi.", "ad": "num_comp1, num_comp2,... sono da 1 a 255 numeri complessi da moltiplicare"}, "IMREAL": {"a": "(num_comp)", "d": "Restituisce la parte reale di un numero complesso.", "ad": "è il numero complesso di cui ottenere la parte reale"}, "IMSEC": {"a": "(num_comp)", "d": "Restituisce la secante di un numero complesso", "ad": "è il numero complesso di cui calcolare la secante"}, "IMSECH": {"a": "(num_comp)", "d": "Restituisce la secante iperbolica di un numero complesso", "ad": "è il numero complesso di cui calcolare la secante iperbolica"}, "IMSIN": {"a": "(num_comp)", "d": "Restituisce il seno di un numero complesso.", "ad": "è il numero complesso di cui calcolare il seno"}, "IMSINH": {"a": "(num_comp)", "d": "Restituisce il seno iperbolico di un numero complesso", "ad": "è il numero complesso di cui calcolare il seno iperbolico"}, "IMSQRT": {"a": "(num_comp)", "d": "Restituisce la radice quadrata di un numero complesso.", "ad": "è il numero complesso di cui calcolare la radice quadrata"}, "IMSUB": {"a": "(num_comp1; num_comp2)", "d": "Restituisce la differenza di due numeri complessi.", "ad": "è il numero complesso da cui si desidera sottrarre 'num_comp2'!è il numero complesso da sottrarre a 'num_comp1'"}, "IMSUM": {"a": "(num_comp1; [num_comp2]; ...)", "d": "Restituisce la somma di numeri complessi.", "ad": "sono da 1 a 255 numeri complessi da sommare"}, "IMTAN": {"a": "(num_comp)", "d": "Restituisce la tangente di un numero complesso", "ad": "è il numero complesso di cui calcolare la tangente"}, "OCT2BIN": {"a": "(num; [cifre])", "d": "Converte un numero ottale in binario.", "ad": "è il numero ottale da convertire!è il numero di caratteri da utilizzare"}, "OCT2DEC": {"a": "(num)", "d": "Converte un numero ottale in decimale.", "ad": "è il numero ottale da convertire"}, "OCT2HEX": {"a": "(num; [cifre])", "d": "Converte un numero ottale in esadecimale.", "ad": "è il numero ottale da convertire!è il numero di caratteri da utilizzare"}, "DAVERAGE": {"a": "(database; campo; criteri)", "d": "Restituisce la media dei valori di una colonna di un elenco o di un database che soddisfano le condizioni specificate.", "ad": "è l'intervallo di celle che costituisce l'elenco o il database. Un database è un elenco di dati correlati.!è l'etichetta di colonna tra virgolette o un numero che rappresenta la posizione della colonna nell'elenco.!è l'intervallo di celle che contiene le condizioni specificate. L'intervallo include un'etichetta di colonna e una cella sottostante in cui immettere una condizione"}, "DCOUNT": {"a": "(database; campo; criteri)", "d": "Conta le celle nel campo (colonna) dei record del database che soddisfano le condizioni specificate.", "ad": "è l'intervallo di celle che costituisce l'elenco o il database. Un database è un elenco di dati correlati.!è l'etichetta di colonna tra virgolette o un numero che rappresenta la posizione della colonna nell'elenco!è l'intervallo di celle che contiene le condizioni specificate. L'intervallo include un'etichetta di colonna e una cella sotto l'etichetta."}, "DCOUNTA": {"a": "(database; campo; criteri)", "d": "Conta le celle non vuote nel campo (colonna) dei record del database che soddisfano le condizioni specificate.", "ad": "è l'intervallo di celle che costituisce l'elenco o il database. Un database è un elenco di dati correlati.!è l'etichetta di colonna tra virgolette o un numero che rappresenta la posizione della colonna nell'elenco!è l'intervallo di celle che contiene le condizioni specificate. L'intervallo include un'etichetta di colonna e una cella sotto l'etichetta."}, "DGET": {"a": "(database; campo; criteri)", "d": "Estrae da un database un singolo record che soddisfa le condizioni specificate.", "ad": "è l'intervallo di celle che costituisce l'elenco o il database. Un database è un elenco di dati correlati.!è l'etichetta della colonna tra virgolette o un numero che rappresenta la posizione della colonna nell'elenco!è l'intervallo di celle che contiene le condizioni specificate. L'intervallo include un'etichetta di colonna e una cella sottostante in cui immettere una condizione."}, "DMAX": {"a": "(database; campo; criteri)", "d": "Restituisce il valore massimo nel campo (colonna) di record del database che soddisfa le condizioni specificate.", "ad": "è l'intervallo di celle che costituisce l'elenco o il database. Un database è un elenco di dati correlati.!è l'etichetta della colonna tra virgolette o un numero che rappresenta la posizione della colonna nell'elenco!è l'intervallo di celle che contiene le condizioni specificate. L'intervallo include un'etichetta di colonna e una cella sottostante in cui immettere una condizione."}, "DMIN": {"a": "(database; campo; criteri)", "d": "Restituisce il valore minimo nel campo (colonna) di record del database che soddisfa le condizioni specificate.", "ad": "è l'intervallo di celle che costituisce l'elenco o il database. Un database è un elenco di dati correlati.!è l'etichetta della colonna tra virgolette o un numero che rappresenta la posizione della colonna nell'elenco!è l'intervallo di celle che contiene le condizioni specificate. L'intervallo include un'etichetta di colonna e una cella sottostante in cui immettere una condizione."}, "DPRODUCT": {"a": "(database; campo; criteri)", "d": "Moltiplica i valori nel campo (colonna) di record del database che soddisfano le condizioni specificate.", "ad": "è l'intervallo di celle che costituisce l'elenco o il database. Un database è un elenco di dati correlati.!è l'etichetta della colonna tra virgolette o un numero che rappresenta la posizione della colonna nell'elenco!è l'intervallo di celle che contiene le condizioni specificate. L'intervallo include un'etichetta di colonna e una cella sottostante in cui immettere una condizione."}, "DSTDEV": {"a": "(database; campo; criteri)", "d": "Stima la deviazione standard sulla base di un campione di voci del database selezionate.", "ad": "è l'intervallo di celle che costituisce l'elenco o il database. Un database è un elenco di dati correlati.!è l'etichetta della colonna tra virgolette o un numero che rappresenta la posizione della colonna nell'elenco!è l'intervallo di celle che contiene le condizioni specificate. L'intervallo include un'etichetta di colonna e una cella sottostante in cui immettere una condizione."}, "DSTDEVP": {"a": "(database; campo; criteri)", "d": "Calcola la deviazione standard sulla base dell'intera popolazione di voci del database selezionate.", "ad": "è l'intervallo di celle che costituisce l'elenco o il database. Un database è un elenco di dati correlati.!è l'etichetta della colonna tra virgolette o un numero che rappresenta la posizione della colonna nell'elenco!è l'intervallo di celle che contiene le condizioni specificate. L'intervallo include un'etichetta di colonna e una cella sottostante in cui immettere una condizione."}, "DSUM": {"a": "(database; campo; criteri)", "d": "Aggiunge i numeri nel campo (colonna) di record del database che soddisfano le condizioni specificate.", "ad": "è l'intervallo di celle che costituisce l'elenco o il database. Un database è un elenco di dati correlati.!è l'etichetta della colonna tra virgolette o un numero che rappresenta la posizione della colonna nell'elenco!è l'intervallo di celle che contiene le condizioni specificate. L'intervallo include un'etichetta di colonna e una cella sottostante in cui immettere una condizione."}, "DVAR": {"a": "(database; campo; criteri)", "d": "Stima la varianza sulla base di un campione di voci del database selezionate.", "ad": "è l'intervallo di celle che costituisce l'elenco o il database. Un database è un elenco di dati correlati.!è l'etichetta della colonna tra virgolette o un numero che rappresenta la posizione della colonna nell'elenco!è l'intervallo di celle che contiene le condizioni specificate. L'intervallo include un'etichetta di colonna e una cella sottostante in cui immettere una condizione."}, "DVARP": {"a": "(database; campo; criteri)", "d": "Calcola la varianza sulla base dell'intera popolazione di voci del database selezionate.", "ad": "è l'intervallo di celle che costituisce l'elenco o il database. Un database è un elenco di dati correlati.!è l'etichetta della colonna tra virgolette o un numero che rappresenta la posizione della colonna nell'elenco!è l'intervallo di celle che contiene le condizioni specificate. L'intervallo include un'etichetta di colonna e una cella sottostante in cui immettere una condizione."}, "CHAR": {"a": "(num)", "d": "Restituisce il carattere specificato dal numero di codice del set di caratteri del computer.", "ad": "è un numero compreso tra 1e 255 che specifica il carattere desiderato"}, "CLEAN": {"a": "(testo)", "d": "Rimuove dal testo tutti i caratteri che non possono essere stampati.", "ad": "è una qualsiasi informazione del foglio di lavoro dalla quale rimuovere i caratteri che non possono essere stampati"}, "CODE": {"a": "(testo)", "d": "Restituisce il codice numerico del primo carattere di una stringa di testo, in base al set di caratteri installato nel sistema.", "ad": "è il testo da cui ottenere il codice del primo carattere"}, "CONCATENATE": {"a": "(testo1; [testo2]; ...)", "d": "Unisce diverse stringhe di testo in una singola stringa.", "ad": "sono da 1 a 255 stringhe di testo da unire in una singola stringa di testo e possono essere stringhe di testo, numeri o riferimenti a celle singole"}, "CONCAT": {"a": "(testo1; ...)", "d": "Concatena un elenco o un intervallo di stringhe di testo", "ad": "sono le stringhe di testo, da 1 a 254, o gli intervalli da unire a una singola stringa di testo"}, "DOLLAR": {"a": "(num; [decimali])", "d": "Converte un numero in testo utilizzando un formato valuta.", "ad": "è un numero, un riferimento a una cella contenente un numero o una formula che calcola un numero!è il numero di cifre a destra della virgola decimale. Il numero verrà arrotondato come necessario; se viene omesso, Decimali = 2"}, "EXACT": {"a": "(testo1; testo2)", "d": "Controlla due stringhe di testo e restituisce il valore VERO se sono identiche e FALSO in caso contrario. Distingue tra maiuscole e minuscole.", "ad": "è la prima stringa di testo!è la seconda stringa di testo"}, "FIND": {"a": "(testo; stringa; [inizio])", "d": "Trova una stringa di testo all'interno di un'altra stringa e restituisce il numero corrispondente alla posizione iniziale della stringa trovata. La funzione distingue tra maiuscole e minuscole", "ad": " Util<PERSON><PERSON><PERSON> le virgolette (testo vuoto) per trovare una corrispondenza con il primo carattere nella casella Stringa. Non sono ammessi caratteri jolly.!è il testo contenente il testo da trovare!specifica il carattere in corrispondenza del quale iniziare la ricerca. Il primo carattere nella casella Stringa è il carattere numero 1. Se viene omesso, Inizio = 1"}, "FINDB": {"a": "(testo-1; stringa; [inizio])", "d": "Trova una stringa di testo all'interno di un'altra stringa e restituisce il numero corrispondente alla posizione iniziale della stringa trovata. Distingue tra maiuscole e minuscole, set (DBSC) per linguaggi come Japanese, Chinese, Korean etc.", "ad": "Util<PERSON><PERSON><PERSON> le virgolette (testo vuoto) per trovare una corrispondenza con il primo carattere nella casella Stringa. Non sono ammessi caratteri jolly.!è il testo contenente il testo da trovare!specifica il carattere in corrispondenza del quale iniziare la ricerca. Il primo carattere nella casella Stringa è il carattere numero 1. Se viene omesso, Inizio = 1"}, "FIXED": {"a": "(num; [decimali]; [nessun_separatore])", "d": "Arrotonda un numero al numero di cifre decimali specificato e restituisce il risultato come testo.", "ad": "è il numero da arrotondare e convertire in testo!è il numero di cifre a destra della virgola decimale. Se viene omesso, Decimali = 2!è un valore logico: non visualizzare i separatori delle migliaia nel testo restituito con il valore VERO; visualizzarli nel testo restituito con il valore FALSO"}, "LEFT": {"a": "(testo; [num_caratt])", "d": "Restituisce il carattere o i caratteri più a sinistra di una stringa di testo.", "ad": "è la stringa di testo che contiene i caratteri da estrarre!specifica il numero di caratteri da estrarre da SINISTRA; 1 se omesso"}, "LEFTB": {"a": "(testo; [num_caratt])", "d": "Restituisce il carattere o i caratteri più a sinistra di una stringa di testo set (DBCS) per linguaggi come Japanese, Chinese, Korean etc.", "ad": "è la stringa di testo che contiene i caratteri da estrarre!specifica il numero di caratteri da estrarre da LEFTB; 1 se omesso"}, "LEN": {"a": "(testo)", "d": "Restituisce il numero di caratteri in una stringa di testo.", "ad": "è il testo di cui conoscere la lunghezza. Gli spazi sono contati come caratteri."}, "LENB": {"a": "(testo)", "d": "Restituisce il numero di caratteri in una stringa di testo set (DBCS) per linguaggi come Japanese, Chinese, Korean etc.", "ad": "è il testo di cui conoscere la lunghezza. Gli spazi sono contati come caratteri."}, "LOWER": {"a": "(testo)", "d": "Converte le lettere maiuscole in una stringa di testo in lettere minuscole.", "ad": "è il testo da convertire in minuscolo. I caratteri che non sono lettere verranno lasciati invariati."}, "MID": {"a": "(testo; inizio; num_caratt)", "d": "Restituisce un numero specifico di caratteri da una stringa di testo iniziando dalla posizione specificata.", "ad": "è la stringa di testo da cui estrarre i caratteri!è la posizione del primo carattere da estrarre. Il primo carattere in Testo è 1!specifica il numero di caratteri che devono essere restituiti da Testo"}, "MIDB": {"a": "(testo; inizio; num_caratt)", "d": "Restituisce un numero specifico di caratteri da una stringa di testo iniziando dalla posizione specificata set (DBCS) per linguaggi come Japanese, Chinese, Korean etc.", "ad": "è la stringa di testo da cui estrarre i caratteri!è la posizione del primo carattere da estrarre. Il primo carattere in Testo è 1!specifica il numero di caratteri che devono essere restituiti da Testo"}, "NUMBERVALUE": {"a": "(testo; [separatore_decimale]; [separatore_gruppo])", "d": "Converte il testo in numero in modo indipendente dalle impostazioni locali", "ad": "è la stringa che rappresenta il numero da convertire!è il carattere usato come separatore decimale nella stringa!è il carattere usato come separatore di gruppo nella stringa"}, "PROPER": {"a": "(testo)", "d": "Converte in maiuscolo la prima lettera di ciascuna parola in una stringa di testo e converte le altre lettere in minuscolo.", "ad": "è del testo racchiuso tra virgolette, una formula che restituisce del testo o un riferimento ad una cella contenente del testo da convertire parzialmente in maiuscolo"}, "REPLACE": {"a": "(testo_prec; inizio; num_caratt; nuovo_testo)", "d": "Sostituisce parte di una stringa di testo con un'altra stringa di testo.", "ad": "è il testo nel quale si desidera sostituire alcuni caratteri!è la posizione del carattere in Testo_prec da sostituire con Nuovo_testo!è il numero di caratteri in Testo_prec da sostituire con Nuovo_testo!è il testo che sostituirà i caratteri in Testo_prec"}, "REPLACEB": {"a": "(testo_prec; inizio; num_caratt; nuovo_testo)", "d": "Sostituisce parte di una stringa di testo con un'altra stringa di testo set (DBCS) per linguaggi come Japanese, Chinese, Korean etc.", "ad": "è il testo nel quale si desidera sostituire alcuni caratteri!è la posizione del carattere in Testo_prec da sostituire con Nuovo_testo!è il numero di caratteri in Testo_prec da sostituire con Nuovo_testo!è il testo che sostituirà i caratteri in Testo_prec"}, "REPT": {"a": "(testo; volte)", "d": "Ripete un testo per il numero di volte specificato. Utilizzare RIPETI per riempire una cella con il numero di occorrenze di una stringa di testo.", "ad": "è il testo da ripetere!è un numero positivo che specifica di quante volte ripetere il testo"}, "RIGHT": {"a": "(testo; [num_caratt])", "d": "Restituisce il carattere o i caratteri più a destra di una stringa di testo.", "ad": "è la stringa di testo che contiene i caratteri da estrarre!specifica il numero dei caratteri da estrarre, 1 se omesso"}, "RIGHTB": {"a": "(testo; [num_caratt])", "d": "Restituisce l'ultimo o gli ultimi caratteri di una stringa di testo, in base al numero di byte specificati set (DBCS) per linguaggi come Japanese, Chinese, Korean etc.", "ad": "è la stringa di testo che contiene i caratteri da estrarre!specifica il numero dei caratteri da estrarre, 1 se omesso"}, "SEARCH": {"a": "(testo; stringa; [inizio])", "d": "Restituisce il numero corrispondente al carattere o alla stringa di testo trovata in una seconda stringa di testo (non distingue tra maiuscole e minuscole).", "ad": "è il testo da trovare. È possibile utilizzare i caratteri jolly ? e *. Utilizzare ~? e ~* per trovare i caratteri ? e *.!è il testo all'interno del quale effettuare la ricerca di Testo!è il numero del carattere in Stringa, a partire da sinistra,  dal quale si desidera iniziare la ricerca. Se omesso, verrà utilizzato 1"}, "SEARCHB": {"a": "(testo; stringa; [inizio])", "d": "Restituisce il numero corrispondente al carattere o alla stringa di testo trovata in una seconda stringa di testo (non distingue tra maiuscole e minuscole) set (DBCS) per linguaggi come Japanese, Chinese, Korean etc.", "ad": "è il testo da trovare. È possibile utilizzare i caratteri jolly ? e *. Utilizzare ~? e ~* per trovare i caratteri ? e *.!è il testo all'interno del quale effettuare la ricerca di Testo!è il numero del carattere in Stringa, a partire da sinistra,  dal quale si desidera iniziare la ricerca. Se omesso, verrà utilizzato 1"}, "SUBSTITUTE": {"a": "(testo; testo_prec; nuovo_testo; [occorrenza])", "d": "Sostituisce il nuovo testo a quello esistente in una stringa di testo.", "ad": "è il testo o un riferimento ad una cella contenente del testo in cui si desidera sostituire dei caratteri!è il testo esistente da sostituire. Se i caratteri maiuscoli/minuscoli di Testo_prec non corrispondono a quelli del testo, SOSTITUISCI non funzionerà.!è il testo da sostituire a Testo_prec!specifica l'occorrenza di Testo_prec da sostituire. Se viene omesso, tutte le occorrenze di Testo_prec verranno sostituite."}, "T": {"a": "(val)", "d": "Controlla se il valore è un testo e, in caso positivo, lo restituisce, altrimenti vengono restituite delle virgolette, ossia testo vuoto", "ad": "è il valore da verificare"}, "T.TEST": {"a": "(matrice1; matrice2; coda; tipo)", "d": "Restituisce la probabilità associata ad un test t di Student.", "ad": "è il primo set di dati!è il secondo set di dati!specifica il numero delle code della distribuzione da restituire: distribuzione a una coda = 1; distribuzione a due code = 2!è il tipo di test t da eseguire: accoppiato = 1, a due campioni a varianza identica a due campioni (omoscedastico) = 2, varianza dissimile a due campioni = 3"}, "TEXTJOIN": {"a": "(delimitatore; ignora_vuote; testo1; ...)", "d": "Concatena un elenco o un intervallo di stringhe di testo tramite un delimitatore", "ad": "<PERSON>ttere o stringa da inserire tra ogni elemento di testo!se VERO(impostazione predefinita), ignora le celle vuote!sono le stringhe di testo da 1 a 252 o gli intervalli da unire"}, "TEXT": {"a": "(valore; format_text)", "d": "Converte un valore in testo in un formato di numero specifico", "ad": "è un numero, una formula che restituisce un valore numerico o un riferimento a una cella contenente un valore numerico!è un formato numerico in forma di testo dalla casella categoria nella scheda numero della finestra di dialogo Formato celle"}, "TRIM": {"a": "(testo)", "d": "Rimuove gli spazi da una stringa di testo eccetto gli spazi singoli tra le parole.", "ad": "è il testo da cui si desidera rimuovere gli spazi"}, "TREND": {"a": "(y_note; [x_note]; [nuove_x]; [cost])", "d": "Restituisce i numeri in una una tendenza lineare corrispondente a punti dati noti usando il metodo dei minimi quadrati.", "ad": "è l'insieme dei valori y già noti dalla relazione y = mx + b!è una matrice o un intervallo facoltativo di valori x che possono essere già noti dalla relazione y = mx + b, una matrice delle stesse dimensioni di y_note!è un intervallo o una matrice di nuovi valori x per i quali TENDENZA restituirà i corrispondenti valori y!è un valore logico: la costante b viene calcolata normalmente se Cost = VERO oppure omesso; b è impostato uguale a 0 se Cost = FALSO"}, "TRIMMEAN": {"a": "(matrice; percento)", "d": "Restituisce la media della parte interna di un set di valori di dati.", "ad": "è la matrice o intervallo di valori da troncare e di cui si calcola la media!è il numero di dati frazionario da escludere dall'inizio e dalla fine del set di dati"}, "TTEST": {"a": "(matrice1; matrice2; coda; tipo)", "d": "Restituisce la probabilità associata ad un test t di Student.", "ad": "è il primo set di dati!è il secondo set di dati!specifica il numero delle code della distribuzione da restituire: distribuzione a una coda = 1; distribuzione a due code = 2!è il tipo di test t da eseguire: accoppiato = 1, a due campioni a varianza identica a due campioni (omoscedastico) = 2, varianza dissimile a due campioni = 3"}, "UNICHAR": {"a": "(num)", "d": "Restituisce il carattere Unicode corrispondente al valore numerico specificato", "ad": "è il numero Unicode che rappresenta un carattere"}, "UNICODE": {"a": "(testo)", "d": "Restituisce il numero (punto di codice) corrispondente al primo carattere del testo", "ad": "è il carattere per il quale si calcola il valore Unicode"}, "UPPER": {"a": "(testo)", "d": "Converte una stringa di testo in maiuscolo.", "ad": "è il testo da convertire in maius<PERSON>lo, un riferimento o una stringa di testo"}, "VALUE": {"a": "(testo)", "d": "Converte una stringa di testo che rappresenta un numero in una stringa di testo.", "ad": "è il testo racchiuso tra virgolette o un riferimento a una cella contenente il testo da convertire"}, "AVEDEV": {"a": "(num1; [num2]; ...)", "d": "Restituisce la media delle deviazioni assolute delle coordinate rispetto alla media di queste ultime. Gli argomenti possono essere numeri o nomi, matrici o riferimenti contenenti numeri.", "ad": "sono da 1 a 255 argomenti di cui si calcola la media delle deviazioni assolute"}, "AVERAGE": {"a": "(num1; [num2]; ...)", "d": "Restituisce la media aritmetica degli argomenti (numeri, nomi o riferimenti contenenti numeri).", "ad": "sono da 1 a 255 argomenti numerici di cui calcolare la media"}, "AVERAGEA": {"a": "(val1; [val2]; ...)", "d": "Restituisce la media aritmetica degli argomenti. Gli argomenti costituiti da testo o dal valore FALSO vengono valutati come 0, quelli costituiti dal valore VERO come 1. Gli argomenti possono essere numeri, nomi, matrici o riferimenti.", "ad": "sono da 1 a 255 argomenti di cui calcolare la media"}, "AVERAGEIF": {"a": "(intervallo; criterio; [int_media])", "d": "Determina la media aritmetica per le celle specificate da una determinata condizione o criterio.", "ad": "è l'intervallo di celle da valutare!è la condizione o i criteri sotto forma di numero, espressione o testo che definisce quali celle verranno usate per trovare la media!sono le celle effettive da usare per trovare la media. Se viene omesso, verranno usate le celle nell'intervallo"}, "AVERAGEIFS": {"a": "(int_media; int_criteri; criterio; ...)", "d": "Determina la media aritmetica per le celle specificate da un determinato insieme di condizioni o criteri.", "ad": "sono le celle effettive da utilizzare per determinare la media!è l'intervallo di celle da valutare per la condizione specificata!è il criterio o la condizione, in forma di numero, espressione o testo, che definisce le celle da utilizzare per determinare la media"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Calcola la funzione densità di probabilità cumulativa beta.", "ad": "è il valore in cui si calcola la funzione nell'intervallo A  x  B!è un parametro per la distribuzione e deve essere maggiore di 0!è un parametro per la distribuzione e deve essere maggiore di 0!è un valore facoltativo per l'estremo inferiore dell'intervallo di x. Se omesso, A = 0.!è un valore facoltativo per l'estremo superiore dell'intervallo di x. Se omesso, B = 1."}, "BETAINV": {"a": "(probabilità; alfa; beta; [A]; [B])", "d": "Restituisce l'inversa della funzione densità di probabilità cumulativa beta (DISTRIB.BETA).", "ad": "è la probabilità associata alla distribuzione beta!è un parametro per la distribuzione e deve essere maggiore di 0!è un parametro per la distribuzione e deve essere maggiore di 0!è un valore facoltativo per l'estremo inferiore dell'intervallo di x. Se omesso, A = 0!è un valore facoltativo per l'estremo superiore dell'intervallo di x. Se omesso, B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; cumulativa; [A]; [B])", "d": "Calcola la funzione di distribuzione probabilità beta.", "ad": "è il valore in cui si calcola la funzione nell'intervallo compreso tra A e B!è un parametro per la distribuzione e deve essere maggiore di 0!è un parametro per la distribuzione e deve essere maggiore di 0!è un valore logico: per la funzione di distribuzione cumulativa, utilizzare VERO, per la funzione densità di probabilità, utilizzare FALSO!è un valore facoltativo per l'estremo inferiore dell'intervallo di x. Se omesso, A = 0.!è un valore facoltativo per l'estremo superiore dell'intervallo di x. Se omesso, B = 1."}, "BETA.INV": {"a": "(probabilità; alfa; beta; [A]; [B])", "d": "Restituisce l'inversa della funzione densità di probabilità cumulativa beta (DISTRIB.BETA.N).", "ad": "è la probabilità associata alla distribuzione beta!è un parametro per la distribuzione e deve essere maggiore di 0!è un parametro per la distribuzione e deve essere maggiore di 0!è un valore facoltativo per l'estremo inferiore dell'intervallo di x. Se omesso, A = 0!è un valore facoltativo per l'estremo superiore dell'intervallo di x. Se omesso, B = 1"}, "BINOMDIST": {"a": "(num_successi; prove; probabilità_s; cumulativo)", "d": "Restituisce la distribuzione binomiale per il termine individuale.", "ad": "è il numero dei successi nelle prove!è il numero di prove indipendenti!è la probabilità di successo per ciascuna prova!è un valore logico: utilizzare VERO per la funzione distribuzione cumulativa; utilizzare FALSO per la funzione probabilità di massa"}, "BINOM.DIST": {"a": "(num_successi; prove; probabilità_s; cumulativo)", "d": "Restituisce la distribuzione binomiale per il termine individuale.", "ad": "è il numero dei successi nelle prove!è il numero di prove indipendenti!è la probabilità di successo per ciascuna prova!è un valore logico: utilizzare VERO per la funzione distribuzione cumulativa; utilizzare FALSO per la funzione probabilità di massa"}, "BINOM.DIST.RANGE": {"a": "(prove; probabilità_s; num_s; [num_s2])", "d": "Restituisce la probabilità di un risultato di prova usando una distribuzione binomiale", "ad": "è il numero di prove indipendenti!è la probabilità di successo di ogni prova!è il numero di successi nelle prove!se specificato, questa funzione restituisce la probabilità che il numero di prove di successo sia compreso tra num_s e num_s2"}, "BINOM.INV": {"a": "(prove; probabilità_s; alfa)", "d": "Restituisce il più piccolo valore per il quale la distribuzione cumulativa binomiale risulta maggiore o uguale ad un valore di criterio.", "ad": "è il numero delle prove di <PERSON>lli!è la probabilità di un successo ad ogni prova, un numero tra 0 e 1 inclusi!è il valore di criterio, un numero tra 0 e 1 inclusi"}, "CHIDIST": {"a": "(x; grad_libertà)", "d": "Restituisce la probabilità a una coda destra per la distribuzione del chi quadrato.", "ad": "è il valore in cui si calcola la distribuzione, un numero non negativo!è il numero di gradi di libertà, un numero compreso tra 1 e 10^10, escluso 10^10"}, "CHIINV": {"a": "(probabilità; grado_libertà)", "d": "Restituisce l'inversa della probabilità a una coda destra per la distribuzione del chi quadrato.", "ad": "è la probabilità associata alla distribuzione del chi quadrato, un valore tra 0 e 1 inclusi!è il numero di gradi di libertà, un numero compreso tra 1 e 10^10, escluso 10^10"}, "CHITEST": {"a": "(int_effettivo; int_previsto)", "d": "Restituisce il test per l'indipendenza: il valore della distribuzione del chi quadrato per la statistica e i gradi di libertà appropriati.", "ad": "è l'intervallo di dati contenente le osservazioni da confrontare con i valori attesi!è l'intervallo di dati contenente il rapporto tra il prodotto dei totali di riga e la colonna per il totale complessivo"}, "CHISQ.DIST": {"a": "(x; grad_libertà; cumulativa)", "d": "Restituisce la probabilità a una coda sinistra per la distribuzione del chi quadrato.", "ad": "è il valore in cui si calcola la distribuzione, un numero non negativo!è il numero di gradi di libertà, un numero compreso tra 1 e 10^10, escluso 10^10!è un valore logico per la funzione da restituire: la funzione di distribuzione cumulativa = VERO; la funzione densità di probabilità = FALSO"}, "CHISQ.DIST.RT": {"a": "(x; grad_libertà)", "d": "Restituisce la probabilità ad una coda destra per la distribuzione del chi quadrato.", "ad": "è il valore in cui si calcola la distribuzione, un numero non negativo!è il numero di gradi di libertà, un numero compreso tra 1 e 10^10, escluso 10^10"}, "CHISQ.INV": {"a": "(probabilità; grado_libertà)", "d": "Restituisce l'inversa della probabilità a una coda sinistra della distribuzione del chi quadrato.", "ad": "è la probabilità associata alla distribuzione del chi quadrato, un valore tra 0 e 1 inclusi!è il numero di gradi di libertà, un numero compreso tra 1 e 10^10, escluso 10^10"}, "CHISQ.INV.RT": {"a": "(probabilità; grado_libertà)", "d": "Restituisce l'inversa della probabilità a una coda destra della distribuzione del chi quadrato.", "ad": "è la probabilità associata alla distribuzione del chi quadrato, un valore tra 0 e 1 inclusi!è il numero di gradi di libertà, un numero compreso tra 1 e 10^10, escluso 10^10"}, "CHISQ.TEST": {"a": "(int_effettivo; int_previsto)", "d": "Restituisce il test per l'indipendenza: il valore dalla distribuzione del chi quadrato per la statistica e i gradi di libertà appropriati.", "ad": "è l'intervallo di dati contenente le osservazioni da confrontare con i valori attesi!è l'intervallo di dati contenente la proporzione  del prodotto dei totali di riga e di colonna per il totale complessivo"}, "CONFIDENCE": {"a": "(alfa; dev_standard; dimensioni)", "d": "Restituisce l'intervallo di confidenza per una popolazione, utilizzando una distribuzione normale", "ad": "è il livello di significatività utilizzato per calcolare il livello di confidenza, un numero maggiore di 0 e minore di 1!è la deviazione standard della popolazione per l'intervallo di dati e si presuppone sia nota. Dev_standard deve essere maggiore di 0.!è la dimensione del campione"}, "CONFIDENCE.NORM": {"a": "(alfa; dev_standard; dimensioni)", "d": "Restituisce l'intervallo di confidenza per una popolazione, utilizzando una distribuzione normale.", "ad": "è il livello di significatività utilizzato per calcolare il livello di confidenza, un numero maggiore di 0 e minore di 1!è la deviazione standard della popolazione per l'intervallo di dati e si presuppone sia nota. Dev_standard deve essere maggiore di 0.!è la dimensione del campione"}, "CONFIDENCE.T": {"a": "(alfa; dev_standard; dimensioni)", "d": "Restituisce l'intervallo di confidenza per una popolazione, utilizzando una distribuzione T di Student.", "ad": "è il livello di significatività utilizzato per calcolare il livello di confidenza, un numero maggiore di 0 e minore di 1!è la deviazione standard della popolazione per l'intervallo di dati e si presuppone sia nota. Dev_standard deve essere maggiore di 0.!è la dimensione del campione"}, "CORREL": {"a": "(matrice1; matrice2)", "d": "Restituisce il coefficiente di correlazione tra due set di dati.", "ad": "è un intervallo di celle di valori. I valori possono essere numeri, nomi, matrici o riferimenti contenenti numeri!è il secondo intervallo di celle di valori. I valori possono essere numeri, nomi, matrici o riferimenti contenenti numeri"}, "COUNT": {"a": "(val1; [val2]; ...)", "d": "Conta il numero di celle in un intervallo contenente numeri e i numeri presenti nell'elenco degli argomenti", "ad": "sono da 1 a 255 argomenti che possono contenere o riferirsi a più tipi di dati, di cui vengono contati soltanto i numeri"}, "COUNTA": {"a": "(val1; [val2]; ...)", "d": "Conta il numero delle celle non vuote e i valori presenti nell'elenco degli argomenti.", "ad": "sono da 1 a 255 argomenti che rappresentano i valori e le celle da contare. Il valore può essere costituito da qualsiasi tipo di informazione."}, "COUNTBLANK": {"a": "(intervallo)", "d": "Conta il numero di celle vuote in uno specificato intervallo.", "ad": "è l'intervallo di cui contare le celle vuote"}, "COUNTIF": {"a": "(intervallo; criterio)", "d": "Conta il numero di celle in un intervallo che corrispondono al criterio dato.", "ad": "è l'intervallo di celle di cui contare le celle non vuote!è la condizione in forma di numero, espressione o testo che definisce le celle da contare"}, "COUNTIFS": {"a": "(intervallo_criteri; criteri; ...)", "d": "Conta il numero di celle specificate da un determinato insieme di condizioni o criteri.", "ad": "è l'intervallo di celle da valutare per una particolare condizione!è la condizione, in forma di numero, espressione o testo, che definisce le celle da contare"}, "COVAR": {"a": "(matrice1; matrice2)", "d": "Calcola la covarianza, la media dei prodotti delle deviazioni di ciascuna coppia di coordinate in due set di dati.", "ad": "è il primo intervallo di celle di interi e deve essere costituito da numeri, matrici o riferimenti contenenti numeri!è il secondo intervallo di celle di interi e deve essere costituito da numeri, matrici o riferimenti contenenti numeri"}, "COVARIANCE.P": {"a": "(matrice1; matrice2)", "d": "Calcola la covarianza della popolazione, la media dei prodotti delle deviazioni di ciascuna coppia di coordinate in due set di dati", "ad": "è il primo intervallo di celle di interi e deve essere costituito da numeri, matrici o riferimenti contenenti numeri!è il secondo intervallo di celle di interi e deve essere costituito da numeri, matrici o riferimenti contenenti numeri"}, "COVARIANCE.S": {"a": "(matrice1; matrice2)", "d": "Calcola la covarianza del campione, la media dei prodotti delle deviazioni di ciascuna coppia di coordinate in due set di dati", "ad": "è il primo intervallo di celle di interi e deve essere costituito da numeri, matrici o riferimenti contenenti numeri!è il secondo intervallo di celle di interi e deve essere costituito da numeri, matrici o riferimenti contenenti numeri"}, "CRITBINOM": {"a": "(prove; probabilità_s; alfa)", "d": "Restituisce il più piccolo valore per il quale la distribuzione cumulativa binomiale risulta maggiore o uguale ad un valore di criterio.", "ad": "è il numero delle prove di <PERSON>lli!è la probabilità di un successo ad ogni prova, un numero tra 0 e 1 inclusi!è il valore di criterio, un numero tra 0 e 1 inclusi"}, "DEVSQ": {"a": "(num1; [num2]; ...)", "d": "Restituisce la somma dei quadrati delle deviazioni delle coordinate dalla media di queste ultime sul campione.", "ad": "sono da 1 a 255 argomenti, una matrice o un riferimento a una matrice in base ai quali calcolare DEV.Q"}, "EXPON.DIST": {"a": "(x; lambda; cumulativo)", "d": "Restituisce la distribuzione esponenziale.", "ad": "è il valore della funzione, un numero non negativo!è il valore del parametro, un numero positivo!è un valore logico che la funzione deve restituire: la funzione distribuzione cumulativa = VERO; la funzione densità di probabilità = FALSO"}, "EXPONDIST": {"a": "(x; lambda; cumulativo)", "d": "Restituisce la distribuzione esponenziale.", "ad": "è il valore della funzione, un numero non negativo!è il valore del parametro, un numero positivo!è un valore logico che la funzione deve restituire: la funzione distribuzione cumulativa = VERO; la funzione densità di probabilità = FALSO"}, "FDIST": {"a": "(x; grad_libertà1; grad_libertà2)", "d": "Restituisce la distribuzione di probabilità F (coda destra) (grado di diversità) per due set di dati.", "ad": "è il valore in cui si calcola la funzione, un numero non negativo!sono i gradi di libertà al numeratore, un numero compreso tra 1 e 10^10, escluso 10^10!sono i gradi di libertà al denominatore, un numero compreso tra 1 e 10^10, escluso 10^10"}, "FINV": {"a": "(probabilità; grado_libertà1; grado_libertà2)", "d": "Restituisce l'inversa della distribuzione di probabilità F (coda destra): se p = FDIST(x,...), allora FINV(p,...) = x", "ad": "è una probabilità associata alla distribuzione cumulativa F, un numero tra 0 e 1 inclusi.!sono i gradi di libertà al numeratore, un numero compreso tra 1 e 10^10, escluso 10^10!sono i gradi di libertà al denominatore, un numero compreso tra 1 e 10^10, escluso 10^10"}, "F.DIST": {"a": "(x; grad_libertà1; grad_libertà2; cumulativa)", "d": "Restituisce la distribuzione di probabilità F (coda sinistra ) (grado di diversità) per due set di dati.", "ad": "è il valore in cui si calcola la funzione, un numero non negativo!sono i gradi di libertà al numeratore, un numero compreso tra 1 e 10^10, escluso 10^10!sono i gradi di libertà al denominatore, un numero compreso tra 1 e 10^10, escluso 10^10!è un valore logico per la funzione da restituire: la funzione di distribuzione cumulativa = VERO; la funzione densità di probabilità = FALSO"}, "F.DIST.RT": {"a": "(x; grad_libertà1; grad_libertà2)", "d": "Restituisce la distribuzione di probabilità F (coda destra) (grado di diversità) per due set di dati.", "ad": "è il valore in cui si calcola la funzione, un numero non negativo!sono i gradi di libertà al numeratore, un numero compreso tra 1 e 10^10, escluso 10^10!sono i gradi di libertà al denominatore, un numero compreso tra 1 e 10^10, escluso 10^10"}, "F.INV": {"a": "(probabilità; grado_libertà1; grado_libertà2)", "d": "Restituisce l'inversa della distribuzione di probabilità F (coda sinistra): se p = DISTRIB.F(x,...), allora INVF(p,...) = x", "ad": "è una probabilità associata alla distribuzione cumulativa F, un numero tra 0 e 1 inclusi.!sono i gradi di libertà al numeratore, un numero compreso tra 1 e 10^10, escluso 10^10!sono i gradi di libertà al denominatore, un numero compreso tra 1 e 10^10, escluso 10^10"}, "F.INV.RT": {"a": "(probabilità; grado_libertà1; grado_libertà2)", "d": "Restituisce l'inversa della distribuzione di probabilità F (coda destra): se p = DISTRIB.F.DS(x,...), allora INV.F.DS(p,...) = x", "ad": "è una probabilità associata alla distribuzione cumulativa F, un numero tra 0 e 1 inclusi.!sono i gradi di libertà al numeratore, un numero compreso tra 1 e 10^10, escluso 10^10!sono i gradi di libertà al denominatore, un numero compreso tra 1 e 10^10, escluso 10^10"}, "FISHER": {"a": "(x)", "d": "Restituisce la trasformazione di Fisher.", "ad": "è il valore per il quale si desidera eseguire la trasformazione, un numero tra -1 e 1 non compresi"}, "FISHERINV": {"a": "(y)", "d": "Restituisce l'inversa della trasformazione di Fisher: se y = FISHER(x), allora INV.FISHER(y) = x.", "ad": "è il valore per il quale si desidera eseguire l'inversa della trasformazione"}, "FORECAST": {"a": "(x; y_note; x_note)", "d": "<PERSON><PERSON> o prevede un valore futuro lungo una tendenza lineare usando i valori esistenti", "ad": "è il punto dati di cui prevedere il valore e deve essere un valore numerico!è la matrice o l'intervallo di dati numerici dipendente!è la matrice o l'intervallo di dati numerici indipendente. La varianza di x_note deve essere diversa da zero"}, "FORECAST.LINEAR": {"a": "(x; y_note; x_note)", "d": "<PERSON><PERSON> o prevede un valore futuro lungo una tendenza lineare usando i valori esistenti.", "ad": "è il punto dati per cui prevedere un valore e deve essere un valore numerico.!è la matrice o l'intervallo di dati numerici dipendente!è la matrice o l'intervallo di dati numerici indipendente. La varianza di x_note deve essere diversa da zero"}, "FREQUENCY": {"a": "(matrice_dati; matrice_bin)", "d": "Calcola la frequenza con cui si presentano valori compresi in un intervallo e restituisce una matrice verticale di numeri con un elemento in più rispetto a Matrice_bin.", "ad": "è una matrice o un riferimento ad un insieme di valori di cui si calcola la frequenza. Gli spazi e il testo vengono ignorati.!è una matrice o un riferimento agli intervalli in cui raggruppare i valori contenuti in matrice_dati"}, "GAMMA": {"a": "(x)", "d": "Restituisce il valore della funzione GAMMA", "ad": "è il valore per il quale si calcola GAMMA"}, "GAMMADIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Restituisce la distribuzione gamma.", "ad": "è il valore in cui si calcola la distribuzione, un numero non negativo!è un parametro per la distribuzione, un numero positivo!è un parametro per la distribuzione, un numero positivo. Se beta = 1, DISTRIB.GAMMA restituisce la distribuzione gamma standard.!è un valore logico: restituisce la funzione distribuzione cumulativa = VERO; restituisce la funzione probabilità di massa = FALSO oppure omesso"}, "GAMMA.DIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Restituisce la distribuzione gamma.", "ad": "è il valore in cui si calcola la distribuzione, un numero non negativo!è un parametro per la distribuzione, un numero positivo!è un parametro per la distribuzione, un numero positivo. Se beta = 1, DISTRIB.GAMMA.N restituisce la distribuzione gamma standard.!è un valore logico: restituisce la funzione distribuzione cumulativa = VERO; restituisce la funzione probabilità di massa = FALSO oppure omesso"}, "GAMMAINV": {"a": "(probabilità; alfa; beta)", "d": "Restituisce l'inversa della distribuzione cumulativa gamma: se p = DISTRIB.GAMMA(x,...), allora INV.GAMMA(p,...) = x", "ad": "è la probabilità associata alla distribuzione gamma, un numero tra 0 e 1 inclusi!è un parametro per la distribuzione, un numero positivo!è un parametro per la distribuzione, un numero positivo. Se beta = 1, INV.GAMMA restituisce la distribuzione gamma standard."}, "GAMMA.INV": {"a": "(probabilità; alfa; beta)", "d": "Restituisce l'inversa della distribuzione cumulativa gamma: se p = DISTRIB.GAMMA.N(x,...), allora INV.GAMMA.N(p,...) = x", "ad": "è la probabilità associata alla distribuzione gamma, un numero tra 0 e 1 inclusi!è un parametro per la distribuzione, un numero positivo!è un parametro per la distribuzione, un numero positivo. Se beta = 1, INV.GAMMA.N restituisce l'inversa della distribuzione gamma standard."}, "GAMMALN": {"a": "(x)", "d": "Restituisce il logaritmo naturale della funzione gamma.", "ad": "è il valore per il quale si calcola INV.GAMMA, un numero positivo"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Restituisce il logaritmo naturale della funzione gamma.", "ad": "è il valore per il quale si calcola LN.GAMMA.PRECISA, un numero positivo"}, "GAUSS": {"a": "(x)", "d": "Restituisce il valore risultante dalla detrazione di 0,5 dalla distribuzione normale standard cumulativa", "ad": "è il valore per il quale si vuole calcolare la distribuzione"}, "GEOMEAN": {"a": "(num1; [num2]; ...)", "d": "Restituisce la media geometrica di una matrice o di un intervallo di dati numerici positivi.", "ad": "sono da 1 a 255 numeri, nomi, matrici o riferimenti contenenti numeri di cui calcolare la media"}, "GROWTH": {"a": "(y_note; [x_note]; [nuove_x]; [cost])", "d": "Calcola la crescita esponenziale prevista utilizzando coordinate esistenti.", "ad": "è l'insieme dei valori y già noti dalla relazione y = b*m^x, da una matrice o da un intervallo di valori positivi!è un insieme facoltativo di valori x che possono essere già noti dalla relazione y = b*m^x, da una matrice o da un intervallo della stessa dimensione di y_note!sono i nuovi valori x per i quali CRESCITA restituirà i corrispondenti valori y!è un valore logico: la costante b viene calcolata normalmente se Const = VERO; b è impostato a 1 se Const = FALSO oppure omesso"}, "HARMEAN": {"a": "(num1; [num2]; ...)", "d": "Calcola la media armonica (il reciproco della media aritmetica dei reciproci) di un set di dati costituiti da numeri positivi.", "ad": "sono da 1 a 255 numeri, nomi, matrici o riferimenti contenenti numeri di cui calcolare la media armonica"}, "HYPGEOM.DIST": {"a": "(s_campione; num_campione; s_pop; num_pop; cumulativa)", "d": "Restituisce la distribuzione ipergeometrica", "ad": "è il numero di successi nel campione!è la dimensione del campione!è il numero di successi nella popolazione!è la dimensione della popolazione!è un valore logico: per la funzione di distribuzione cumulativa, utilizzare VERO, per la funzione densità di probabilità, utilizzare FALSO"}, "HYPGEOMDIST": {"a": "(s_esempio; num_esempio; s_pop; num_pop)", "d": "Restituisce la distribuzione ipergeometrica", "ad": "è il numero di successi nel campione!è la dimensione del campione!è il numero di successi nella popolazione!è la dimensione della popolazione"}, "INTERCEPT": {"a": "(y_note; x_note)", "d": "Calcola il punto di intersezione della retta con l'asse y tracciando una regressione lineare fra le coordinate note.", "ad": "è l'insieme dipendente di osservazioni o dati e può essere costituito da numeri, nomi, matrici o riferimenti contenenti numeri!è l'insieme indipendente di osservazioni o dati e può essere costituito da numeri, nomi, matrici o riferimenti contenenti numeri"}, "KURT": {"a": "(num1; [num2]; ...)", "d": "Restituisce la curtosi di un set di dati.", "ad": "sono da 1 a 255 numeri, nomi, matrici o riferimenti contenenti numeri di cui calcolare la curtosi"}, "LARGE": {"a": "(matrice; k)", "d": "Restituisce il k-esimo valore più grande in un set di dati. Ad esempio, il quinto numero più grande.", "ad": "è la matrice o intervallo di dati di cui determinare il k-esimo valore più grande!è la posizione, partendo dal più grande, nella matrice o intervallo di celle del valore da restituire"}, "LINEST": {"a": "(y_note; [x_note]; [cost]; [stat])", "d": "Restituisce statistiche che descrivono una tendenza lineare corrispondente a punti dati noti usando il metodo dei minimi quadrati.", "ad": "è l'insieme dei valori y già noti dalla relazione y = mx + b!è un insieme facoltativo di valori x che possono essere già noti dalla relazione y = mx + b!è un valore logico: la costante b viene calcolata normalmente se Cost = VERO o omesso; b viene impostata uguale a 0 se Cost = FALSO!è un valore logico: restituisce statistiche aggiuntive per la regressione = VERO; restituisce coefficienti m e la costante b = FALSO oppure omesso"}, "LOGEST": {"a": "(y_note; [x_note]; [cost]; [stat])", "d": "Restituisce statistiche che descrivono una curva esponenziale che corrisponde a punti dati noti.", "ad": "è l'insieme dei valori y già noti dalla relazione y = b*m^x!è un insieme facoltativo di valori x che possono essere già noti dalla relazione y = b*m^x!è un valore logico: la costante b viene calcolata normalmente se Cost = VERO oppure omesso; b viene impostato uguale a 1 se Cost = FALSO!è un valore logico: restituisce statistiche aggiuntive per la regressione = VERO; restituisce coefficienti m e la costante b = FALSO oppure omesso"}, "LOGINV": {"a": "(probabilità; media; dev_standard)", "d": "Restituisce l'inversa della distribuzione lognormale di x, in cui ln(x) è distribuito normalmente con i parametri Media e Dev_standard.", "ad": "è la probabilità associata alla distribuzione lognormale, un numero tra 0 e 1 inclusi!è la media di ln(x)!è la deviazione standard di ln(x), un numero positivo"}, "LOGNORM.DIST": {"a": "(x; media; dev_standard; cumulativa)", "d": "Restituisce la distribuzione lognormale di x, in cui ln(x) è distribuito normalmente con i parametri Media e Dev_standard.", "ad": "è il valore in cui si calcola la funzione, un numero positivo!è la media di ln(x)!è la deviazione standard di ln(x), un numero positivo!è un valore logico: per la funzione di distribuzione cumulativa, utilizzare VERO, per la funzione densità di probabilità, utilizzare FALSO"}, "LOGNORM.INV": {"a": "(probabilità; media; dev_standard)", "d": "Restituisce l'inversa della distribuzione lognormale di x, in cui ln(x) è distribuito normalmente con i parametri Media e Dev_standard.", "ad": "è la probabilità associata alla distribuzione lognormale, un numero tra 0 e 1 inclusi!è la media di ln(x)!è la deviazione standard di ln(x), un numero positivo"}, "LOGNORMDIST": {"a": "(x; media; dev_standard)", "d": "Restituisce la distribuzione lognormale di x, in cui ln(x) è distribuito normalmente con i parametri Media e Dev_standard.", "ad": "è il valore in cui si calcola la funzione, un numero positivo!è la media di ln(x)!è la deviazione standard di ln(x), un numero positivo"}, "MAX": {"a": "(num1; [num2]; ...)", "d": "Restituisce il valore massimo di un insieme di valori. Ignora i valori logici e il testo.", "ad": "sono da 1 a 255 numeri, celle vuote, valori logici o numeri in forma di testo di cui trovare il valore massimo"}, "MAXA": {"a": "(val1; [val2]; ...)", "d": "Restituisce il valore massimo di un insieme di valori. Non ignora i valori logici e il testo.", "ad": "sono da 1 a 255 numeri, celle vuote, valori logici o numeri in forma di testo per cui determinare il valore massimo"}, "MAXIFS": {"a": "(intervallo_max; intervallo_criteri; criteri; ...)", "d": "Restituisce il valore massimo tra le celle specificato da un determinato insieme di condizioni o criteri", "ad": "le celle in cui determinare il valore massimo!è l'intervallo di celle da valutare in una determinata condizione!è la condizione o il criterio sotto forma di numero, espressione o testo che definisce quali celle verranno incluse nella determinazione del valore massimo"}, "MEDIAN": {"a": "(num1; [num2]; ...)", "d": "Restituisce la mediana, ovvero il valore centrale, di un insieme ordinato di numeri specificato.", "ad": "sono da 1 a 255 numeri, nomi, matrici o riferimenti contenenti numeri di cui si calcola la mediana"}, "MIN": {"a": "(num1; [num2]; ...)", "d": "Restituisce il valore minimo di un insieme di valori. Ignora i valori logici e il testo.", "ad": "sono da 1 a 255 numeri, celle vuote, valori logici o numeri in forma di testo di cui trovare il valore minimo"}, "MINA": {"a": "(val1; [val2]; ...)", "d": "Restituisce il valore minimo di un insieme di valori. Non ignora i valori logici e il testo.", "ad": "sono da 1 a 255 numeri, celle vuote, valori logici o numeri in forma di testo per cui determinare il valore minimo"}, "MINIFS": {"a": "(intervallo_min; intervallo_criteri; criteri; ...)", "d": "Restituisce il valore minimo tra le celle specificato da un determinato insieme di condizioni o criteri", "ad": "le celle in cui determinare il valore minimo!è l'intervallo di celle da valutare per la condizione specificata!è la condizione o il criterio sotto forma di numero, espressione o testo che definisce quali celle includere per determinare il valore minimo"}, "MODE": {"a": "(num1; [num2]; ...)", "d": "Restituisce il valore più ricorrente in una matrice o intervallo di dati.", "ad": "sono da 1 a 255 numeri, nomi, matrici o riferimenti contenenti numeri di cui calcolare la moda"}, "MODE.MULT": {"a": "(num1; [num2]; ...)", "d": "Restituisce il valore più ricorrente in una matrice o intervallo di dati. Per una matrice orizzontale, utilizzare MATR.TRASPOSTA(MODA.MULT(num1,num2,...))", "ad": "sono da 1 a 255 numeri, nomi, matrici o riferimenti contenenti numeri di cui calcolare la moda"}, "MODE.SNGL": {"a": "(num1; [num2]; ...)", "d": "Restituisce il valore più ricorrente in una matrice o intervallo di dati.", "ad": "sono da 1 a 255 numeri, nomi, matrici o riferimenti contenenti numeri di cui calcolare la moda"}, "NEGBINOM.DIST": {"a": "(num_insuccessi; num_successi; probabilità_s; cumulativa)", "d": "Restituisce la distribuzione binomiale negativa, la probabilità che un numero di insuccessi pari a Num_insuccessi si verifichi prima del successo Num_successi, data la probabilità di successo Probabilità_s.", "ad": "è il numero degli insuccessi!è il numero di soglia per i successi!è la probabilità di avere un successo, un numero compreso tra 0 e 1!è un valore logico: per la funzione di distribuzione cumulativa, utilizzare VERO, per la funzione probabilità di massa, utilizzare FALSO"}, "NEGBINOMDIST": {"a": "(num_insuccessi; num_successi; probabilità_s)", "d": "Restituisce la distribuzione binomiale negativa, la probabilità che un numero di insuccessi pari a Num_insuccessi si verifichi prima del successo Num_successi, data la probabilità di successo Probabilità_s.", "ad": "è il numero degli insuccessi!è il numero di soglia per i successi!è la probabilità di avere un successo, un numero compreso tra 0 e 1"}, "NORM.DIST": {"a": "(x; media; dev_standard; cumulativo)", "d": "Restituisce la distribuzione normale per la media e la deviazione standard specificate.", "ad": "è il valore per il quale si desidera la distribuzione!è la media aritmetica della distribuzione!è la deviazione standard della distribuzione, un numero positivo!è un valore logico: utilizzare VERO per la funzione distribuzione cumulativa; utilizzare FALSO per la funzione probabilità di densità"}, "NORMDIST": {"a": "(x; media; dev_standard; cumulativo)", "d": "Restituisce la distribuzione normale cumulativa per la media e la deviazione standard specificate.", "ad": "è il valore per il quale si desidera la distribuzione!è la media aritmetica della distribuzione!è la deviazione standard della distribuzione, un numero positivo!è un valore logico: utilizzare VERO per la funzione distribuzione cumulativa; utilizzare FALSO per la funzione probabilità di densità"}, "NORM.INV": {"a": "(probabilità; media; dev_standard)", "d": "Restituisce l'inversa della distribuzione normale cumulativa per la media e la deviazione standard specificate.", "ad": "è la probabilità corrispondente alla distribuzione normale, un numero tra 0 e 1 inclusi!è la media aritmetica della distribuzione!è la deviazione standard della distribuzione, un numero positivo"}, "NORMINV": {"a": "(probabilità; media; dev_standard)", "d": "Restituisce l'inversa della distribuzione normale cumulativa per la media e la deviazione standard specificate.", "ad": "è la probabilità corrispondente alla distribuzione normale, un numero tra 0 e 1 inclusi!è la media aritmetica della distribuzione!è la deviazione standard della distribuzione, un numero positivo"}, "NORM.S.DIST": {"a": "(z; cumulativa)", "d": "Restituisce la distribuzione normale standard cumulativa (ha media pari a zero e deviazione standard pari a 1).", "ad": "è il valore per il quale si desidera la distribuzione!è un valore logico per la funzione da restituire: la funzione di distribuzione cumulativa = VERO; la funzione densità di probabilità = FALSO"}, "NORMSDIST": {"a": "(z)", "d": "Restituisce la distribuzione normale standard cumulativa (ha media pari a zero e deviazione standard pari a 1).", "ad": "è il valore per il quale si desidera la distribuzione"}, "NORMS.INV": {"a": "(probabilità)", "d": "Restituisce l'inversa della distribuzione normale standard cumulativa( ha media = 0 e dev_standard = 1)"}, "NORMSINV": {"a": "(probabilità)", "d": "Restituisce l'inversa della distribuzione normale standard cumulativa (ha media pari a zero e deviazione standard pari a 1).", "ad": "è la probabilità corrispondente alla distribuzione normale, un numero tra 0 e 1 inclusi"}, "PEARSON": {"a": "(matrice1; matrice2)", "d": "Restituisce il prodotto del coefficiente di momento di correlazione di Pearson, r.", "ad": "è un insieme di valori indipendenti!è un insieme di valori dipendenti"}, "PERCENTILE": {"a": "(matrice; k)", "d": "Restituisce il k-esimo dato percentile di valori in un intervallo.", "ad": "è la matrice o l'intervallo di dati che definisce la condizione relativa!è il valore percentile, compreso nell'intervallo tra 0 e 1 inclusi"}, "PERCENTILE.EXC": {"a": "(matrice; k)", "d": "Restituisce il k-esimo dato percentile di valori in un intervallo, dove k è compreso nell'intervallo 0..1, estremi esclusi.", "ad": "è la matrice o l'intervallo di dati che definisce la condizione relativa!è il valore percentile, compreso nell'intervallo tra 0 e 1 inclusi"}, "PERCENTILE.INC": {"a": "(matrice; k)", "d": "Restituisce il k-esimo dato percentile di valori in un intervallo, dove k è compreso nell'intervallo 0..1, estremi inclusi.", "ad": "è la matrice o l'intervallo di dati che definisce la condizione relativa!è il valore percentile, compreso nell'intervallo tra 0 e 1 inclusi"}, "PERCENTRANK": {"a": "(matrice; x; [cifre_signific])", "d": "Restituisce il rango di un valore in un set di dati come percentuale del set di dati.", "ad": "è la matrice o l'intervallo di dati con valori numerici che definiscono la condizione relativa!è il valore di cui conoscere il rango!è un valore facoltativo che identifica il numero di cifre significative per la percentuale restituita, tre cifre se omesso (0,xxx %)"}, "PERCENTRANK.EXC": {"a": "(matrice; x; [cifre_signific])", "d": "Restituisce il rango di un valore in un set di dati come percentuale del set di dati come percentuale (0..1, estremi esclusi) del set di dati.", "ad": " È la matrice o l'intervallo di dati con valori numerici che definiscono la posizione relativa! È il valore per cui si vuole conoscere la classificazione! È un valore facoltativo che identifica il numero di cifre significative per la percentuale restituita; tre cifre se omesso (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(matrice; x; [cifre_signific])", "d": "Restituisce il rango di un valore in un set di dati come percentuale del set di dati come percentuale (0..1, estremi inclusi) del set di dati.", "ad": " È la matrice o l'intervallo di dati con valori numerici che definiscono la posizione relativa! È il valore per cui si vuole conoscere la classificazione! È un valore facoltativo che identifica il numero di cifre significative per la percentuale restituita; tre cifre se omesso (0.xxx%)"}, "PERMUT": {"a": "(num; classe)", "d": "Restituisce il numero delle permutazioni per un dato numero di oggetti che possono essere selezionati dagli oggetti totali.", "ad": "è il numero totale di oggetti!è il numero di oggetti per ogni permutazione"}, "PERMUTATIONA": {"a": "(num; classe)", "d": "Restituisce il numero delle permutazioni per un dato numero di oggetti (con ripetizioni) che possono essere selezionati dagli oggetti totali", "ad": "è il numero totale di oggetti!è il numero di oggetti per ogni permutazione"}, "PHI": {"a": "(x)", "d": "Restituisce il valore della funzione densità per una distribuzione normale standard", "ad": "è il valore per il quale si calcola la densità della distribuzione normale standard"}, "POISSON": {"a": "(x; media; cumulativo)", "d": "Calcola la distribuzione di probabilità di Poisson", "ad": "è il numero degli eventi!è il valore numerico previsto, un numero positivo!è un valore logico: utilizzare VERO per la probabilità cumulativa di Poisson; utilizzare FALSO per la funzione probabilità di massa"}, "POISSON.DIST": {"a": "(x; media; cumulativo)", "d": "Calcola la distribuzione di probabilità di Poisson", "ad": "è il numero degli eventi!è il valore numerico previsto, un numero positivo!è un valore logico: utilizzare VERO per la probabilità cumulativa di Poisson; utilizzare FALSO per la funzione probabilità di massa"}, "PROB": {"a": "(int_x; prob_int; limite_inf; [limite_sup])", "d": "Calcola la probabilità che dei valori in un intervallo siano compresi tra due limiti o pari al limite inferiore.", "ad": "è l'intervallo dei valori numerici per x a cui sono associate delle probabilità!è l'insieme delle probabilità associate ai valori di Int_x, compresi tra 0 e 1 (0 escluso)!è il limite inferiore del valore per il quale si calcola la probabilità!è il limite superiore facoltativo del valore. Se viene omesso, PROBABILITÀ restituirà la probabilità che i valori Int_x siano uguali a Limite_inf."}, "QUARTILE": {"a": "(matrice; quarto)", "d": "Restituisce il quartile di un set di dati.", "ad": "è la matrice o intervallo di celle a valori numerici per cui si calcola il valore quartile!è un numero: valore minimo = 0; primo quartile = 1; mediana = 2; terzo quartile = 3; valore massimo = 4"}, "QUARTILE.INC": {"a": "(matrice; quarto)", "d": "Restituisce il quartile di un set di dati, in base a valori di percentile compresi nell'intervallo 0..1, estremi inclusi.", "ad": "è la matrice o intervallo di celle a valori numerici per cui si calcola il valore quartile!è un numero: valore minimo = 0; primo quartile = 1; mediana = 2; terzo quartile = 3; valore massimo = 4"}, "QUARTILE.EXC": {"a": "(matrice; quarto)", "d": "Restituisce il quartile di un set di dati, in base a valori di percentile compresi nell'intervallo 0..1, estremi esclusi.", "ad": "è la matrice o intervallo di celle a valori numerici per cui si calcola il valore quartile!è un numero: valore minimo = 0; primo quartile = 1; mediana = 2; terzo quartile = 3; valore massimo = 4"}, "RANK": {"a": "(num; rif; [ordine])", "d": "Restituisce il rango di un numero in un elenco di numeri: la sua grandezza relativa agli altri valori nell'elenco.", "ad": "è il numero di cui ricercare il rango!è una matrice di numeri o un riferimento ad un elenco di numeri. I valori in rif che non sono di tipo numerico vengono ignorati.!è un numero: rango discendente nell'elenco ordinato = 0 oppure omesso; rango ascendente nell'elenco ordinato = qualsiasi valore diverso da zero"}, "RANK.AVG": {"a": "(num; rif; [ordine])", "d": "Restituisce il rango di un numero in un elenco di numeri: la sua grandezza relativa agli altri valori nell'elenco; se più valori hanno lo stesso rango, viene restituito il rango medio.", "ad": "è il numero di cui ricercare il rango!è una matrice di numeri o un riferimento ad un elenco di numeri. I valori che non sono di tipo numerico vengono ignorati.!è un numero: rango nell'elenco in ordine decrescente = 0 oppure omesso; rango nell'elenco in ordine crescente = qualsiasi valore diverso da zero"}, "RANK.EQ": {"a": "(num; rif; [ordine])", "d": "Restituisce il rango di un numero in un elenco di numeri: la sua grandezza relativa agli altri valori nell'elenco; se più valori hanno lo stesso rango, viene restituito il rango massimo del set di valori.", "ad": "è il numero di cui ricercare il rango!è una matrice di numeri o un riferimento ad un elenco di numeri. I valori che non sono di tipo numerico vengono ignorati.!è un numero: rango nell'elenco in ordine decrescente = 0 oppure omesso; rango nell'elenco in ordine crescente = qualsiasi valore diverso da zero"}, "RSQ": {"a": "(y_note; x_note)", "d": "Restituisce la radice quadrata del coefficiente di momento di correlazione di Pearson in corrispondenza delle coordinate date.", "ad": "è una matrice o un intervallo di valori e può essere costituito da numeri, nomi, matrici o riferimenti contenenti numeri!è una matrice o un intervallo di valori e può essere costituito da numeri, nomi, matrici o riferimenti contenenti numeri"}, "SKEW": {"a": "(num1; [num2]; ...)", "d": "Restituisce il grado di asimmetria di una distribuzione, ovvero una caratterizzazione del grado di asimmetria di una distribuzione attorno alla media.", "ad": "sono da 1 a 255 numeri, nomi, matrici o riferimenti contenenti numeri di cui calcolare l'asimmetria"}, "SKEW.P": {"a": "(num1; [num2]; ...)", "d": "Restituisce il grado di asimmetria di una distribuzione in base a una popolazione, ovvero una caratterizzazione del grado di asimmetria di una distribuzione attorno alla media", "ad": "sono da 1 a 255 numeri, nomi, matrici o riferimenti contenenti numeri di cui calcolare l'asimmetria della popolazione"}, "SLOPE": {"a": "(y_note; x_note)", "d": "Restituisce la pendenza della retta di regressione lineare fra le coordinate note.", "ad": "è una matrice o un intervallo di celle di valori numerici dipendenti e può essere costituito da numeri, nomi, matrici o riferimenti contenenti numeri!è l'insieme dei valori indipendenti e può essere costituito da numeri, nomi, matrici o riferimenti contenenti numeri"}, "SMALL": {"a": "(matrice; k)", "d": "Restituisce il k-esimo valore più piccolo di un set di dati. Ad esempio il quinto numero più piccolo.", "ad": "è una matrice o un intervallo di dati numerici di cui determinare il k-esimo valore più piccolo!è la posizione del valore da restituire, partendo dal più piccolo, nella matrice o nell'intervallo"}, "STANDARDIZE": {"a": "(x; media; dev_standard)", "d": "Restituisce un valore normalizzato da una distribuzione caratterizzata da una media e da una deviazione standard.", "ad": "è il valore da normalizzare!è la media aritmetica della distribuzione!è la deviazione standard della distribuzione, un numero positivo"}, "STDEV": {"a": "(num1; [num2]; ...)", "d": "Restituisce una stima della deviazione standard sulla base di un campione. Ignora i valori logici e il testo nel campione.", "ad": "sono da 1 a 255 argomenti corrispondenti a un campione della popolazione e possono essere numeri o riferimenti contenenti numeri"}, "STDEV.P": {"a": "(num1; [num2]; ...)", "d": "Calcola la deviazione standard sulla base dell'intera popolazione, passata come argomenti (ignora i valori logici e il testo).", "ad": "sono da 1 a 255 argomenti corrispondenti a una popolazione e possono essere numeri o riferimenti contenenti numeri"}, "STDEV.S": {"a": "(num1; [num2]; ...)", "d": "Restituisce una stima della deviazione standard sulla base di un campione. Ignora i valori logici e il testo nel campione.", "ad": "sono da 1 a 255 argomenti corrispondenti a un campione della popolazione e possono essere numeri o riferimenti contenenti numeri"}, "STDEVA": {"a": "(val1; [val2]; ...)", "d": "Restituisce una stima della deviazione standard sulla base di un campione, inclusi valori logici e testo. Il testo e il valore FALSO vengono valutati come 0, il valore VERO come 1.", "ad": "sono da 1 a 255 valori corrispondenti a un campione di popolazione e possono essere valori, nomi o riferimenti a valori"}, "STDEVP": {"a": "(num1; [num2]; ...)", "d": "Calcola la deviazione standard sulla base dell'intera popolazione, passata come argomenti (ignora i valori logici e il testo).", "ad": "sono da 1 a 255 argomenti corrispondenti a una popolazione e possono essere numeri o riferimenti contenenti numeri"}, "STDEVPA": {"a": "(val1; [val2]; ...)", "d": "Calcola la deviazione standard sulla base dell'intera popolazione, inclusi valori logici e testo. Il testo e il valore FALSO vengono valutati come 0, il valore VERO come 1.", "ad": "sono da 1 a 255 valori corrispondenti a una popolazione e possono essere valori, nomi, matrici o riferimenti contenenti valori"}, "STEYX": {"a": "(y_note; x_note)", "d": "Restituisce l'errore standard del valore previsto per y per ciascun valore di x nella regressione.", "ad": "è una matrice o un intervallo di dati dipendenti e può essere costituito da numeri, nomi, matrici o riferimenti contenenti numeri!è una matrice o un intervallo di dati indipendenti e può essere costituito da numeri, nomi, matrici o riferimenti contenenti numeri"}, "TDIST": {"a": "(x; grad_libertà; code)", "d": "Restituisce la distribuzione t di Student.", "ad": "è il valore numerico in cui si calcola la distribuzione!è un intero che indica il numero di gradi di libertà che caratterizza la distribuzione!specifica il numero di code di distribuzione da restituire: distribuzione a una coda = 1; distribuzione a due code = 2"}, "TINV": {"a": "(probabilità; grado_libertà)", "d": "Restituisce l'inversa della distribuzione t di Student.", "ad": "è la probabilità associata alla distribuzione t di Student a due code, un numero tra 0 e 1 inclusi!è l'intero positivo che indica il numero di gradi di libertà della distribuzione"}, "T.DIST": {"a": "(x; grad_libertà; code)", "d": "Restituisce la distribuzione t di Student a una coda sinistra.", "ad": "è il valore numerico in cui si calcola la distribuzione!è un intero che indica il numero di gradi di libertà che caratterizza la distribuzione!è un valore logico: per la funzione di distribuzione cumulativa, utilizzare VERO, per la funzione densità di probabilità utilizzare FALSO"}, "T.DIST.2T": {"a": "(x; grad_libertà)", "d": "Restituisce la distribuzione t di Student a due code.", "ad": "è il valore numerico in cui si calcola la distribuzione!è un intero che indica il numero di gradi di libertà che caratterizza la distribuzione"}, "T.DIST.RT": {"a": "(x; grad_libertà)", "d": "Restituisce la distribuzione t di Student a una coda destra.", "ad": "è il valore numerico in cui si calcola la distribuzione!è un intero che indica il numero di gradi di libertà che caratterizza la distribuzione"}, "T.INV": {"a": "(probabilità; grado_libertà)", "d": "Restituisce l'inversa della distribuzione t di Student a una coda sinistra.", "ad": "è la probabilità associata alla distribuzione t di Student a due code, un numero tra 0 e 1 inclusi!è l'intero positivo che indica il numero di gradi di libertà della distribuzione"}, "T.INV.2T": {"a": "(probabilità; grado_libertà)", "d": "Restituisce l'inversa della distribuzione t di Student a due code.", "ad": "è la probabilità associata alla distribuzione t di Student a due code, un numero tra 0 e 1 inclusi!è l'intero positivo che indica il numero di gradi di libertà della distribuzione"}, "VAR": {"a": "(num1; [num2]; ...)", "d": "Stima la varianza sulla base di un campione. Ignora i valori logici e il testo nel campione.", "ad": "sono da 1 a 255 argomenti numerici corrispondenti a un campione della popolazione"}, "VAR.P": {"a": "(num1; [num2]; ...)", "d": "Calcola la varianza sulla base dell'intera popolazione. Ignora i valori logici e il testo nella popolazione.", "ad": "sono da 1 a 255 argomenti numerici corrispondenti a una popolazione"}, "VAR.S": {"a": "(num1; [num2]; ...)", "d": "Stima la varianza sulla base di un campione. Ignora i valori logici e il testo nel campione.", "ad": "sono da 1 a 255 argomenti numerici corrispondenti a un campione della popolazione"}, "VARA": {"a": "(val1; [val2]; ...)", "d": "Restituisce una stima della varianza sulla base di un campione, inclusi valori logici e testo. Il testo e il valore FALSO vengono valutati come 0, il valore VERO come 1.", "ad": "sono da 1 a 255 argomenti di valori corrispondenti a un campione di popolazione"}, "VARP": {"a": "(num1; [num2]; ...)", "d": "Calcola la varianza sulla base dell'intera popolazione. Ignora i valori logici e il testo nella popolazione.", "ad": "sono da 1 a 255 argomenti numerici corrispondenti a una popolazione"}, "VARPA": {"a": "(val1; [val2]; ...)", "d": "Calcola la varianza sulla base dell'intera popolazione, inclusi valori logici e testo. Il testo e il valore FALSO vengono valutati come 0, il valore VERO come 1.", "ad": "sono da 1 a 255 argomenti di tipo valore corrispondenti a una popolazione"}, "WEIBULL": {"a": "(x; alfa; beta; cumulativo)", "d": "Restituisce la distribuzione di Weibull.", "ad": "è il valore in cui si calcola la funzione, un numero non negativo!è un parametro per la distribuzione, un numero positivo!è un parametro per la distribuzione, un numero positivo!è un valore logico: utilizzare VERO per la funzione distribuzione cumulativa; utilizzare FALSO per la funzione probabilità di massa"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Restituisce la distribuzione di Weibull.", "ad": "è il valore in cui si calcola la funzione, un numero non negativo!è un parametro per la distribuzione, un numero positivo!è un parametro per la distribuzione, un numero positivo!è un valore logico: utilizzare VERO per la funzione distribuzione cumulativa; utilizzare FALSO per la funzione probabilità di massa"}, "Z.TEST": {"a": "(matrice; x; [sigma])", "d": "Restituisce il livello di significatività a una coda per un test z.", "ad": "è la matrice o intervallo di dati con cui verificare x!è il valore da esaminare!è la deviazione standard della popolazione (nota). Se viene omessa, verrà utilizzata la deviazione standard del campione."}, "ZTEST": {"a": "(matrice; x; [sigma])", "d": "Restituisce il livello di significatività a una coda per un test z.", "ad": "è la matrice o intervallo di dati con cui verificare x!è il valore da esaminare!è la deviazione standard della popolazione (nota). Se viene omessa, verrà utilizzata la deviazione standard del campione."}, "ACCRINT": {"a": "(emiss; primo_int; liquid; tasso_int; val_nom; num_rate; [base]; [metodo_calc])", "d": "Restituisce l'interesse maturato di un titolo per cui è pagato un interesse periodico.", "ad": "è la data di emissione del titolo espressa come numero seriale!è la data del primo interesse del titolo, espressa come numero seriale!è la data di liquidazione del titolo, espressa come numero seriale!è il tasso di interesse nominale annuo del titolo!è il valore nominale del titolo!è il numero di pagamenti per anno!è il tipo di base da usare per il conteggio dei giorni!è un valore logico: VERO o assente per calcolare dalla data di emissione all'interesse maturato, FALSO per calcolare dalla data di pagamento dell'ultima cedola"}, "ACCRINTM": {"a": "(emiss; liquid; tasso_int; val_nom; [base])", "d": "Restituisce l'interesse maturato per un titolo i cui interessi vengono pagati alla scadenza.", "ad": "è la data di emissione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il tasso di interesse nominale annuo del titolo!è il valore nominale del titolo!è il tipo di base da usare per il conteggio dei giorni"}, "AMORDEGRC": {"a": "(costo; data_acquisto; primo_periodo; valore_residuo; periodo; tasso_int; [base])", "d": "Restituisce l'ammortamento lineare ripartito proporzionalmente di un bene per ogni periodo contabile.", "ad": "è il costo del bene!è la data di acquisto del bene!è la data di fine del primo periodo!è il valore residuo al termine della vita utile del bene!è il periodo!è il tasso di ammortamento!base_annua: 0 per un anno di 360 giorni, 1 per l'anno effettivo, 3 per un anno di 365 giorni"}, "AMORLINC": {"a": "(costo; data_acquisto; primo_periodo; valore_residuo; periodo; tasso_int; [base])", "d": "Restituisce l'ammortamento lineare ripartito proporzionalmente di un bene per ogni periodo contabile.", "ad": "è il costo del bene!è la data di acquisto del bene!è la data di fine del primo periodo!è il valore residuo al termine della vita utile del bene!è il periodo!è il tasso di ammortamento!base_annua: 0 per un anno di 360 giorni, 1 per l'anno effettivo, 3 per un anno di 365 giorni"}, "COUPDAYBS": {"a": "(liquid; scad; num_rate; [base])", "d": "Calcola il numero dei giorni che intercorrono dalla data di inizio del periodo della cedola alla data di liquidazione.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "COUPDAYS": {"a": "(liquid; scad; num_rate; [base])", "d": "Calcola il numero dei giorni nel periodo della cedola che contiene la data di liquidazione", "ad": "è la data di liquidazione del titolo espressa in numero seriale.!è la data di scadenza del titolo espressa in numero seriale!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "COUPDAYSNC": {"a": "(liquid; scad; num_rate; [base])", "d": "Calcola il numero dei giorni che intercorrono dalla data di liquidazione alla data della nuova cedola.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "COUPNCD": {"a": "(liquid; scad; num_rate; [base])", "d": "Restituisce la data della cedola successiva alla data di liquidazione.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "COUPNUM": {"a": "(liquid; scad; num_rate; [base])", "d": "Calcola il numero di cedole valide tra la data di liquidazione e la data di scadenza.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "COUPPCD": {"a": "(liquid; scad; num_rate; [base])", "d": "Restituisce la data della cedola precedente alla data di liquidazione.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "CUMIPMT": {"a": "(tasso_int; periodi; val_attuale; iniz_per; fine_per; tipo)", "d": "Calcola l'interesse cumulativo pagato tra due periodi.", "ad": "è il tasso di interesse!è il numero totale dei periodi di pagamento!è il valore attuale!è il primo periodo nel calcolo!è l'ultimo periodo nel calcolo!è la scadenza del pagamento"}, "CUMPRINC": {"a": "(tasso_int; per; val_attuale; iniz_per; fine_per; tipo)", "d": "Calcola il capitale cumulativo pagato per estinguere un debito tra due periodi.", "ad": "è il tasso di interesse!è il numero totale dei periodi di pagamento!è il valore attuale!è il primo periodo nel calcolo!è l'ultimo periodo nel calcolo!è la scadenza del pagamento"}, "DB": {"a": "(costo; val_residuo; vita_utile; periodo; [mese])", "d": "Restituisce l'ammortamento di un bene per un periodo specificato utilizzando il metodo a quote fisse proporzionali ai valori residui.", "ad": "è il costo iniziale del bene!è il valore di recupero al termine della vita utile del bene!è il numero di periodi in cui il bene viene ammortizzato, denominato anche vita utile di un bene!è il periodo per il quale si calcola l'ammortamento. Periodo deve utilizzare la stessa unità di misura di Vita_utile.!è il numero di mesi nel primo anno. Se non è specificato, verrà considerato uguale a 12."}, "DDB": {"a": "(costo; val_residuo; vita_utile; periodo; [fattore])", "d": "Restituisce l'ammortamento di un bene per un periodo specificato utilizzando il metodo a doppie quote proporzionali ai valori residui o un altro metodo specificato.", "ad": "è il costo iniziale del bene!è il valore di recupero al termine della vita utile del bene!è il numero di periodi in cui il bene viene ammortizzato, denominato anche vita utile di un bene!è il periodo per il quale si calcola l'ammortamento. Periodo deve utilizzare la stessa unità di misura di Vita_utile.!è il tasso di deprezzamento del valore residuo. Se Fattore non è specificato, verrà considerato uguale a 2 (metodo di ammortamento a doppie quote proporzionali ai valori residui)."}, "DISC": {"a": "(liquid; scad; prezzo; prezzo_rimb; [base])", "d": "Calcola il tasso di sconto di un titolo.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il prezzo del titolo con valore nominale di 100 euro!è il valore di rimborso del titolo con valore nominale di 100 euro!è il tipo di base da utilizzare per il conteggio dei giorni"}, "DOLLARDE": {"a": "(valuta_frazione; frazione)", "d": "Converte un prezzo espresso come frazione in un prezzo espresso come numero decimale.", "ad": "è un numero espresso come frazione!è l'intero che costituisce il denominatore della frazione"}, "DOLLARFR": {"a": "(valuta_decimale; frazione)", "d": "Converte un prezzo espresso come numero decimale in un prezzo espresso come frazione.", "ad": "è un numero decimale!è l'intero che costituisce il denominatore della frazione"}, "DURATION": {"a": "(liquid; scad; cedola; rend; num_rate; [base])", "d": "Restituisce la durata annuale per un titolo per cui è pagato un interesse periodico.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il tasso di interesse nominale annuo del titolo!è il rendimento annuale del titolo!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "EFFECT": {"a": "(tasso_nominale; periodi)", "d": "Restituisce il tasso di interesse effettivo annuo.", "ad": "è il tasso di interesse nominale!è il numero dei periodi di capitalizzazione per anno"}, "FV": {"a": "(tasso_int; periodi; pagam; [val_attuale]; [tipo])", "d": "Restituisce il valore futuro di un investimento dati pagamenti periodici costanti e un tasso di interesse costante.", "ad": "è il tasso di interesse per il periodo. Ad esempio, usare 6%/4 per pagamenti trimestrali al 6%.!è il numero totale dei periodi di pagamento nell'investimento!è il pagamento effettuato in ciascun periodo e non può variare nel corso dell'investimento!è il valore attuale o la somma forfettaria pari al valore attuale di una serie di pagamenti futuri. Se viene omesso, val_attuale = 0!è un valore corrispondente al momento del pagamento: un pagamento all'inizio del periodo = 1; un pagamento alla fine del periodo = 0 oppure omesso"}, "FVSCHEDULE": {"a": "(capitale; piano_invest)", "d": "Restituisce il valore futuro di un capitale iniziale dopo l'applicazione di una serie di tassi di interesse composto.", "ad": "è il valore attuale!è una matrice di tassi di interesse da applicare"}, "INTRATE": {"a": "(liquid; scad; invest; prezzo_rimb; [base])", "d": "Restituisce il tasso di interesse per un titolo interamente investito.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è l'importo investito nel titolo!è l'importo da ricevere alla scadenza!è il tipo di base da utilizzare per il conteggio dei giorni"}, "IPMT": {"a": "(tasso_int; periodo; periodi; val_attuale; [val_futuro]; [tipo])", "d": "Restituisce l'ammontare degli interessi relativi ad un investimento di una certa durata con pagamenti periodici costanti e un tasso di interesse costante.", "ad": "è il tasso di interesse per il periodo. Ad esempio, utilizzare 6%/4 per pagamenti trimestrali al tasso del 6%.!è il periodo, compreso tra 1 e n periodi, per il quale si calcolano gli interessi.!è il numero totale dei periodi di pagamento in un investimento!è il valore attuale o la somma forfettaria pari al valore attuale di una serie di pagamenti futuri!è il valore futuro o il saldo in contanti da conseguire dopo l'ultimo pagamento. Se omesso, val_futuro = 0!è un valore corrispondente al momento del pagamento: all'inizio del periodo = 1; alla fine del periodo = 0 oppure omesso"}, "IRR": {"a": "(val; [ipotesi])", "d": "Restituisce il tasso di rendimento interno per una serie di flussi di cassa.", "ad": "è una matrice o un riferimento a celle che contengono numeri di cui si calcola il tasso di rendimento!è un numero che si suppone vicino al risultato di TIR.COST; 0,1 (10 %) se omesso"}, "ISPMT": {"a": "(tasso_int; periodo; periodi; val_attuale)", "d": "Restituisce il tasso di interesse del prestito a tasso fisso.", "ad": "tasso di interesse per il periodo. Ad esempio, usare 6%/4 per pagamenti trimestrali al 6%.!periodo di cui si vuole calcolare l'interesse!numero dei periodi di pagamento in un'annualità!somma forfettaria pari al valore attuale di una serie di pagamenti futuri"}, "MDURATION": {"a": "(liquid; scad; cedola; rend; num_rate; [base])", "d": "Calcola la durata Macauley modificata per un titolo con valore nominale presunto di 100 euro.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il tasso di interesse nominale annuo del titolo!è il rendimento annuo del titolo!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "MIRR": {"a": "(val; costo; ritorno)", "d": "Restituisce il tasso di rendimento interno per una serie di flussi di cassa periodici, considerando sia il costo di investimento sia gli interessi da reinvestimento della liquidità.", "ad": "è una matrice o un riferimento a celle che contengono numeri che rappresentano una serie di uscite (negativi) ed entrate (positivi) a intervalli regolari!è il tasso di interesse corrisposto sul contante utilizzato per i flussi di cassa!è il tasso di interesse percepito sui flussi di cassa nel momento in cui il contante viene reinvestito"}, "NOMINAL": {"a": "(tasso_effettivo; periodi)", "d": "Restituisce il tasso di interesse nominale annuo.", "ad": "è il tasso di interesse effettivo!è il numero dei periodi di capitalizzazione per anno"}, "NPER": {"a": "(tasso_int; pagam; val_attuale; [val_futuro]; [tipo])", "d": "Restituisce il numero di periodi relativi ad un investimento con pagamenti periodici costanti e un tasso di interesse costante.", "ad": "è il tasso di interesse per il periodo. Ad esempio, usare 6%/4 per pagamenti trimestrali al 6%.!è il pagamento effettuato in ciascun periodo e non può variare nel corso dell'investimento.!è il valore attuale o la somma forfettaria pari al valore attuale di una serie di pagamenti futuri!è il valore futuro o il saldo in contanti da conseguire dopo l'ultimo pagamento. Se omesso, verrà considerato uguale a zero.!è un valore logico: pagamento all'inizio del periodo = 1; pagamento alla fine del periodo = 0 oppure omesso"}, "NPV": {"a": "(tasso_int; val1; [val2]; ...)", "d": "Restituisce il valore attuale netto di un investimento basato su una serie di uscite (valori negativi) e di entrate (valori positivi) future.", "ad": "è il tasso di sconto per la durata di un periodo!sono da 1 a 254 entrate e uscite periodiche al termine di ogni periodo"}, "ODDFPRICE": {"a": "(liquid; scad; emiss; prima_ced; tasso_int; rend; prezzo_rimb; num_rate; [base])", "d": "Restituisce il prezzo di un titolo con valore nominale di 100 euro avente il primo periodo di durata irregolare.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è la data di emissione del titolo espressa come numero seriale!è la data della prima cedola espressa come numero seriale!è il tasso di interesse del titolo!è il rendimento annuo del titolo!è il valore di rimborso del titolo con valore nominale di 100 euro!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "ODDFYIELD": {"a": "(liquid; scad; emiss; prima_ced; tasso_int; prezzo; prezzo_rimb; num_rate; [base])", "d": "Restituisce il rendimento di un titolo avente il primo periodo di durata irregolare.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è la data di emissione del titolo espressa come numero seriale!è la data della prima cedola espressa come numero seriale!è il tasso di interesse del titolo!è il prezzo del titolo!è il valore di rimborso del titolo con valore nominale di 100 euro!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "ODDLPRICE": {"a": "(liquid; scad; ultimo_int; tasso_int; rend; prezzo_rimb; num_rate; [base])", "d": "Restituisce il prezzo di un titolo con valore nominale di 100 euro avente l'ultimo periodo di durata irregolare.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è la data dell'ultima cedola espressa come numero seriale!è il tasso di interesse del titolo!è il rendimento annuo del titolo!è il valore di rimborso del titolo con valore nominale di 100 euro!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "ODDLYIELD": {"a": "(liquid; scad; ultimo_int; tasso_int; prezzo; prezzo_rimb; num_rate; [base])", "d": "Restituisce il rendimento di un titolo avente l'ultimo periodo di durata irregolare.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è la data dell'ultima cedola espressa come numero seriale!è il tasso di interesse del titolo!è il prezzo del titolo!è il valore di rimborso del titolo con valore nominale di 100 euro!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "PMT": {"a": "(tasso_int; periodi; val_attuale; [val_futuro]; [tipo])", "d": "Calcola il pagamento per un prestito in base a pagamenti costanti e a un tasso di interesse costante.", "ad": "è il tasso di interesse per il periodo relativo al prestito. Ad esempio, usare 6%/4 per pagamenti trimestrali al 6%.!è il numero totale dei pagamenti per il prestito!è il valore attuale o la somma forfettaria pari al valore attuale di una serie di pagamenti futuri!è il valore futuro o il saldo in contanti da conseguire dopo l'ultimo pagamento. Se omesso, verrà considerato uguale a 0 (zero).!è un valore logico: pagamento all'inizio del periodo = 1; pagamento alla fine del periodo = 0 oppure omesso"}, "PPMT": {"a": "(tasso_int; periodo; periodi; val_attuale; [val_futuro]; [tipo])", "d": "Restituisce il pagamento sul capitale di un investimento per un dato periodo con pagamenti periodici costanti e un tasso di interesse costante", "ad": "è il tasso di interesse per il periodo. Ad esempio, utilizzare 6%/4 per pagamenti trimestrali al tasso del 6%!specifica il periodo e deve essere compreso tra 1 e n periodi!è il numero totale di periodi di pagamento in un investimento!è il valore attuale: la somma totale pari al valore attuale di una serie di pagamenti futuri!è il valore futuro o il saldo in contanti da conseguire dopo l'ultimo pagamento!è un valore logico: pagamento all'inizio del periodo = 1; pagamento alla fine del periodo = 0 oppure omesso"}, "PRICE": {"a": "(liquid; scad; tasso_int; rend; prezzo_rimb; num_rate; [base])", "d": "Restituisce il prezzo di un titolo con valore nominale di 100 euro e interessi periodici.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il tasso di interesse nominale annuo del titolo!è il rendimento annuo del titolo!è il valore di rimborso del titolo con valore nominale di 100 euro!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "PRICEDISC": {"a": "(liquid; scad; sconto; prezzo_rimb; [base])", "d": "Restituisce il prezzo di un titolo scontato con valore nominale di 100 euro.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il tasso di sconto del titolo!è il valore di rimborso del titolo con valore nominale di 100 euro!è il tipo di base da utilizzare per il conteggio dei giorni"}, "PRICEMAT": {"a": "(liquid; scad; emiss; tasso_int; rend; [base])", "d": "Restituisce il prezzo di un titolo con valore nominale di 100 euro e i cui interessi vengono pagati alla scadenza.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è la data di emissione del titolo espressa come numero seriale!è il tasso di interesse del titolo alla data di emissione!è il rendimento annuo del titolo!è il tipo di base da utilizzare per il conteggio dei giorni"}, "PV": {"a": "(tasso_int; periodi; pagam; [val_futuro]; [tipo])", "d": "Restituisce il valore attuale di un investimento: l'ammontare totale del valore attuale di una serie di pagamenti futuri.", "ad": "è il tasso di interesse per il periodo. Ad esempio, usare 6%/4 per pagamenti trimestrali al 6%.!è il numero totale dei periodi in un investimento!è il pagamento effettuato in ciascun periodo e non può variare nel corso dell'annualità!è il valore futuro o il saldo in contanti da conseguire dopo l'ultimo pagamento!è un valore logico: pagamento all'inizio del periodo = 1; pagamento alla fine del periodo = 0 oppure omesso"}, "RATE": {"a": "(periodi; pagam; val_attuale; [val_futuro]; [tipo]; [ipotesi])", "d": "Restituisce il tasso di interesse per il periodo relativo a un prestito o a un investimento. Ad esempio, usare 6%/4 per pagamenti trimestrali al 6%", "ad": "è il numero totale dei periodi di pagamento per il prestito o per l'investimento!è il pagamento effettuato in ciascun periodo e non può variare nel corso del prestito o dell'investimento.!è il valore attuale o la somma forfettaria pari al valore attuale di una serie di pagamenti futuri!è il valore futuro o il saldo in contanti da conseguire dopo l'ultimo pagamento. Se omesso, val_futuro = 0!è un valore logico: pagamento all'inizio del periodo = 1; pagamento alla fine del periodo = 0 oppure omesso!è la previsione del tasso di interesse futuro; se omesso, Ipotesi = 0,1 (10 %)"}, "RECEIVED": {"a": "(liquid; scad; invest; sconto; [base])", "d": "Calcola l'importo ricevuto alla scadenza di un titolo.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è l'importo investito nel titolo!è il tasso di sconto del titolo!è il tipo di base da utilizzare per il conteggio dei giorni"}, "RRI": {"a": "(periodi; val_attuale; val_futuro)", "d": "Restituisce un tasso di interesse equivalente per la crescita di un investimento", "ad": "è il numero di periodi per l'investimento!è il valore attuale dell'investimento!è il valore futuro dell'investimento"}, "SLN": {"a": "(costo; val_residuo; vita_utile)", "d": "Restituisce l'ammortamento costante di un bene per un periodo.", "ad": "è il costo iniziale del bene!è il valore di recupero al termine della vita utile del bene!è il numero di periodi in cui il bene viene ammortizzato, denominato anche vita utile di un bene"}, "SYD": {"a": "(costo; val_residuo; vita_utile; periodo)", "d": "Restituisce l'ammortamento americano di un bene per un determinato periodo.", "ad": "è il costo iniziale del bene!è il valore di recupero al termine della vita utile del bene!è il numero di periodi in cui il bene viene ammortizzato, denominato anche vita utile di un bene!definisce il periodo per il quale devono essere utilizzate le stesse unità di misura di Vita_utile"}, "TBILLEQ": {"a": "(liquid; scad; sconto)", "d": "Calcola il rendimento equivalente a un'obbligazione per un buono del tesoro.", "ad": "è la data di liquidazione del buono del tesoro espressa come numero seriale!è la data di scadenza del buono del tesoro espressa come numero seriale!è il tasso di sconto del buono del tesoro"}, "TBILLPRICE": {"a": "(liquid; scad; sconto)", "d": "Calcola il prezzo di un buono del tesoro con valore nominale di 100 euro.", "ad": "è la data di liquidazione del buono del tesoro espressa come numero seriale!è la data di scadenza del buono del tesoro espressa come numero seriale!è il tasso di sconto del buono del tesoro"}, "TBILLYIELD": {"a": "(liquid; scad; prezzo)", "d": "Calcola il rendimento di un buono del tesoro.", "ad": "è la data di liquidazione del buono del tesoro espressa come numero seriale!è la data di scadenza del buono del tesoro espressa come numero seriale!è il prezzo del buono del tesoro con valore nominale di 100 euro"}, "VDB": {"a": "(costo; val_residuo; vita_utile; inizio; fine; [fattore]; [nessuna_opzione])", "d": "Restituisce l'ammortamento di un bene per un periodo specificato, anche parziale, utilizzando il metodo a quote proporzionali ai valori residui o un altro metodo specificato.", "ad": "è il costo iniziale del bene!è il valore di recupero al termine della vita utile del bene!è il numero di periodi in cui il bene viene ammortizzato, denominato anche vita utile di un bene!è il periodo iniziale per il quale si calcola l'ammortamento, espresso nella stessa unità di Vita!è il periodo finale per il quale si calcola l'ammortamento, espresso nella stessa unità di Vita!è il tasso di diminuzione della quota, 2 (a doppie quote proporzionali ai valori residui) se omesso!passa all'ammortamento a quote costanti quando l'ammortamento è maggiore della quota decrescente = FALSO oppure omesso; non passare = VERO"}, "XIRR": {"a": "(valori; date_pagam; [ipotesi])", "d": "Restituisce il tasso di rendimento interno per un impiego di flussi di cassa.", "ad": "è una serie di flussi di cassa che corrispondono alle scadenze di pagamento!sono le scadenze di pagamento che corrispondono ai pagamenti dei flussi di cassa!è un numero che si suppone vicino al risultato della formula TIR.X"}, "XNPV": {"a": "(tasso_int; valori; date_pagam)", "d": "Restituisce il valore attuale netto per un impiego di flussi di cassa.", "ad": "è il tasso di sconto sui flussi di cassa!è una serie di flussi di cassa che corrispondono alle scadenze di pagamento!sono le scadenze di pagamento che corrispondono ai pagamenti dei flussi di cassa"}, "YIELD": {"a": "(liquid; scad; tasso_int; prezzo; prezzo_rimb; num_rate; [base])", "d": "Calcola il rendimento di un titolo per cui è pagato un interesse periodico.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il tasso di interesse nominale annuo del titolo!è il prezzo del titolo con valore nominale di 100 euro!è il valore di rimborso del titolo con valore nominale di 100 euro!è il numero di pagamenti per anno!è il tipo di base da utilizzare per il conteggio dei giorni"}, "YIELDDISC": {"a": "(liquid; scad; prezzo; prezzo_rimb; [base])", "d": "Calcola il rendimento annuale per un titolo scontato, ad esempio un buono del tesoro.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è il prezzo del titolo con valore nominale di 100 euro!è il valore di rimborso del titolo con valore nominale di 100 euro!è il tipo di base da utilizzare per il conteggio dei giorni"}, "YIELDMAT": {"a": "(liquid; scad; emiss; tasso_int; prezzo; [base])", "d": "Calcola il rendimento annuo per un titolo i cui interessi vengono pagati alla scadenza.", "ad": "è la data di liquidazione del titolo espressa come numero seriale!è la data di scadenza del titolo espressa come numero seriale!è la data di emissione del titolo espressa come numero seriale!è il tasso di interesse del titolo alla data di emissione!è il prezzo del titolo con valore nominale di 100 euro!è il tipo di base da utilizzare per il conteggio dei giorni"}, "ABS": {"a": "(num)", "d": "Restituisce il valore assoluto di un numero, il numero privo di segno.", "ad": "è il numero reale di cui si calcola il valore assoluto"}, "ACOS": {"a": "(num)", "d": "Restituisce l'arcocoseno di un numero, espresso in radianti da 0 a pi greco. L'arcocoseno è l'angolo il cui coseno è pari al numero.", "ad": "è il coseno dell'angolo desiderato, un valore compreso tra -1 e 1"}, "ACOSH": {"a": "(num)", "d": "Restituisce l'inversa del coseno iperbolico di un numero.", "ad": "è un numero reale maggiore o uguale ad 1"}, "ACOT": {"a": "(num)", "d": "Restituisce l'arcocotangente di un numero, espressa in radianti nell'intervallo tra 0 e pi greco.", "ad": "è la cotangente dell'angolo"}, "ACOTH": {"a": "(num)", "d": "Restituisce la cotangente iperbolica inversa di un numero", "ad": "è la cotangente iperbolica dell'angolo"}, "AGGREGATE": {"a": "(num_funzione; opzioni; rif1; ...)", "d": "Restituisce un'aggregazione in un elenco o un database", "ad": "è il numero compreso tra 1 e 19 che specifica la funzione di riepilogo per l'aggregazione.!è il numero compreso tra 0 e 7 che specifica il valore da ignorare per l'aggregazione!è la matrice o l'intervallo di dati numerici su cui viene calcolata l'aggregazione!indica la posizione nella matrice; è il k-esimo più grande, il k-esimo più piccolo, il k-esimo percentile o il k-esimo quartile.!è il numero compreso tra 1 e 19 che specifica la funzione di riepilogo per l'aggregazione.!è il numero compreso tra 0 e 7 che specifica i valori da ignorare per l'aggregazione!sono riferimenti o intervalli da 1 a 253 per cui si desidera calcolare l'aggregazione"}, "ARABIC": {"a": "(num)", "d": "Converte un numero romano in arabo", "ad": "è il numero romano che si vuole convertire"}, "ASC": {"a": "(testo)", "d": "Nelle lingue che utilizzano set di caratteri a byte doppio (DBCS, Double-Byte Character Set), la funzione converte i caratteri latini a byte doppio (DB, Double-Byte) in caratteri a byte singolo (SB, Single-Byte)", "ad": "è il testo che si desidera modificare"}, "ASIN": {"a": "(num)", "d": "Restituisce l'arcoseno di un numero, espresso in radianti nell'intervallo tra -pi greco/2 e pi greco/2.", "ad": "è il seno dell'angolo desiderato, un valore compreso tra -1 e 1"}, "ASINH": {"a": "(num)", "d": "Restituisce l'inversa del seno iperbolico di un numero.", "ad": "è un numero reale maggiore o uguale a 1"}, "ATAN": {"a": "(num)", "d": "Restituisce l'arcotangente di un numero, espressa in radianti nell'intervallo tra -pi greco/2 e pi greco/2.", "ad": "è la tangente dell'angolo desiderato"}, "ATAN2": {"a": "(x; y)", "d": "Restituisce l'arcotangente in radianti dalle coordinate x e y specificate, nell'intervallo tra -pi greco e pi greco, escluso -pi greco.", "ad": "è l'ascissa del punto!è l'ordinata del punto"}, "ATANH": {"a": "(num)", "d": "Restituisce l'inversa della tangente iperbolica di un numero.", "ad": "è un numero reale compreso tra -1 ed 1 esclusi"}, "BASE": {"a": "(num; radice; [lungh_min])", "d": "Converte un numero in una rappresentazione testuale con la radice (base) specificata", "ad": "è il numero che si vuole convertire!è la radice di base per la conversione del numero!è la lunghezza minima della stringa restituita. Se omesso, non vengono aggiunti zeri iniziali"}, "CEILING": {"a": "(num; peso)", "d": "Arrotonda un numero per eccesso al multiplo più vicino a peso.", "ad": "è il valore da arrotondare!è il multiplo per il quale si desidera arrotondare"}, "CEILING.MATH": {"a": "(num; [peso]; [modalità])", "d": "Arrotonda un numero per eccesso all'intero più vicino o al multiplo più vicino a peso", "ad": "è il valore da arrotondare!è il multiplo in base al quale eseguire l'arrotondamento!se specificato e diverso da zero, la funzione eseguirà un arrotondamento allontanandosi dallo zero"}, "CEILING.PRECISE": {"a": "( x; [peso] )", "d": "Arrotonda un numero per eccesso all'intero più vicino o al multiplo più vicino a peso.", "ad": "è il valore da arrotondare!è il multiplo per il quale si desidera arrotondare"}, "COMBIN": {"a": "(num; classe)", "d": "Calcola il numero delle combinazioni per un numero assegnato di oggetti.", "ad": "è il numero totale di oggetti!è il numero degli oggetti in ciascuna combinazione"}, "COMBINA": {"a": "(num; classe)", "d": "Restituisce il numero delle combinazioni con ripetizioni per un numero specificato di elementi", "ad": "è il numero totale di elementi!è il numero di elementi in ogni combinazione"}, "COS": {"a": "(num)", "d": "Restituisce il coseno di un numero.", "ad": "è l'angolo espresso in radianti di cui si calcola il coseno"}, "COSH": {"a": "(num)", "d": "Restituisce il coseno iperbolico di un numero.", "ad": "è un numero reale qualsiasi"}, "COT": {"a": "(num)", "d": "Restituisce la cotangente di un angolo", "ad": "è l'angolo espresso in radianti di cui si calcola la cotangente"}, "COTH": {"a": "(num)", "d": "Restituisce la cotangente iperbolica di un numero", "ad": "è l'angolo espresso in radianti di cui si calcola la cotangente iperbolica"}, "CSC": {"a": "(num)", "d": "Restituisce la cosecante di un angolo", "ad": "è l'angolo espresso in radianti di cui si calcola la cosecante"}, "CSCH": {"a": "(num)", "d": "Restituisce la cosecante iperbolica di un angolo", "ad": "è l'angolo espresso in radianti di cui si calcola la cosecante iperbolica"}, "DECIMALE": {"a": "( num; radice )", "d": "Converte la rappresentazione di un numero in formato testo di una determinata base in un numero decimale"}, "DEGREES": {"a": "(angolo)", "d": "Converte i radianti in gradi.", "ad": "è l'angolo da convertire, espresso in radianti"}, "ECMA.CEILING": {"a": "( x; peso )", "d": "Arrotonda un numero per eccesso al multiplo più vicino a peso", "ad": "è il valore da arrotondare!è il multiplo per il quale si desidera arrotondare"}, "EVEN": {"a": "(num)", "d": "Arrotonda il valore assoluto di un numero per eccesso all'intero pari più vicino. I numeri negativi sono arrotondati per difetto.", "ad": "è il valore da arrotondare"}, "EXP": {"a": "(num)", "d": "Restituisce il numero e elevato alla potenza di un dato numero.", "ad": "è l'esponente applicato alla base e. La costante e, base dei logaritmi naturali, è uguale a 2,71828182845904."}, "FACT": {"a": "(num)", "d": "Restituisce il fattoriale di un numero, uguale a 1*2*3*...* numero.", "ad": "è il numero non negativo di cui si calcola il fattoriale"}, "FACTDOUBLE": {"a": "(num)", "d": "Restituisce il fattoriale doppio di un numero.", "ad": "è il valore di cui calcolare il fattoriale doppio"}, "FLOOR": {"a": "(num; peso)", "d": "Arrotonda un numero per difetto al multiplo più vicino a peso.", "ad": "è il valore numerico da arrotondare!è il multiplo per il quale si desidera arrotondare. Numero e Peso devono essere entrambi positivi o negativi."}, "FLOOR.PRECISE": {"a": "( x; peso )", "d": "Arrotonda un numero per difetto all'intero più vicino o al multiplo più vicino a peso.", "ad": "è il valore da arrotondare!è il multiplo per il quale si desidera arrotondare"}, "FLOOR.MATH": {"a": "(num; [peso]; [modalità])", "d": "Arrotonda un numero per difetto all'intero più vicino o al multiplo più vicino a peso", "ad": "è il valore da arrotondare!è il multiplo in base al quale eseguire l'arrotondamento!se specificato e diverso da zero, la funzione eseguirà un arrotondamento avvicinandosi allo zero"}, "GCD": {"a": "(num1; [num2]; ...)", "d": "Restituisce il massimo comun divisore.", "ad": "sono da 1 a 255 valori"}, "INT": {"a": "(num)", "d": "Arrotonda un numero per difetto all'intero più vicino.", "ad": "è il numero reale da arrotondare per difetto a un intero"}, "ISO.CEILING": {"a": "(num; [peso])", "d": "Restituisce un numero arrotondato per eccesso all'intero più vicino o al multiplo più vicino a peso. Indipendentemente dal segno di num, il numero viene arrotondato per eccesso. Se tuttavia num o peso è zero, verrà restituito il valore zero.", "ad": "è il valore da arrotondare!è il multiplo per il quale si desidera arrotondare"}, "LCM": {"a": "(num1; [num2]; ...)", "d": "Restituisce il minimo comune multiplo.", "ad": "sono da 1 a 255 valori per cui calcolare il minimo comune multiplo"}, "LN": {"a": "(num)", "d": "Restituisce il logaritmo naturale di un numero.", "ad": "è il numero reale positivo di cui si calcola il logaritmo naturale"}, "LOG": {"a": "(num; [base])", "d": "Restituisce il logaritmo di un numero nella base specificata.", "ad": "è il numero reale positivo di cui si calcola il logaritmo!è la base del logaritmo; 10 se omesso"}, "LOG10": {"a": "(num)", "d": "Restituisce il logaritmo in base 10 di un numero.", "ad": "è il numero reale positivo di cui si calcola il logaritmo in base 10"}, "MDETERM": {"a": "(matrice)", "d": "Restituisce il determinante di una matrice.", "ad": "è una matrice numerica quadrata, un intervallo di celle o una costante di matrice"}, "MINVERSE": {"a": "(matrice)", "d": "Restituisce l'inversa di una matrice.", "ad": "è una matrice numerica quadrata, un intervallo di celle o una costante di matrice"}, "MMULT": {"a": "(matrice1; matrice2)", "d": "Restituisce il prodotto di due matrici, una matrice avente un numero di righe pari a Matrice1 e un numero di colonne pari a Matrice2.", "ad": "è la prima matrice di numeri da moltiplicare e deve avere un numero di colonne pari alle righe di Matrice2"}, "MOD": {"a": "(dividendo; divisore)", "d": "Restituisce il resto della divisione di due numeri.", "ad": "è il numero di cui si calcola il resto dopo l'esecuzione della divisione!è il numero per il quale si desidera dividere il dividendo"}, "MROUND": {"a": "(num; multiplo)", "d": "Restituisce un numero arrotondato al multiplo desiderato.", "ad": "è il valore da arrotondare!è il multiplo a cui arrotondare il numero"}, "MULTINOMIAL": {"a": "(num1; [num2]; ...)", "d": "Restituisce il multinomiale di un insieme di numeri.", "ad": "sono da 1 a 255 valori per cui calcolare il multinomiale"}, "MUNIT": {"a": "(dimensione)", "d": "Restituisce la matrice unitaria per la dimensione specificata", "ad": "è un intero che specifica la dimensione della matrice unitaria da restituire"}, "ODD": {"a": "(num)", "d": "Arrotonda un numero positivo per eccesso al numero intero più vicino e uno negativo per difetto al numero dispari più vicino.", "ad": "è il valore da arrotondare"}, "PI": {"a": "()", "d": "Restituisce il valore di pi greco 3,14159265358979, approssimato a 15 cifre.", "ad": ""}, "POWER": {"a": "(num; potenza)", "d": "Restituisce il risultato di un numero elevato a potenza.", "ad": "è la base, un qualsiasi numero reale!è l'esponente a cui elevare la base"}, "PRODUCT": {"a": "(num1; [num2]; ...)", "d": "Moltiplica tutti i numeri passati come argomenti e restituisce il prodotto.", "ad": "sono da 1 a 255 numeri, valori logici o rappresentazioni in formato testo dei numeri da moltiplicare"}, "QUOTIENT": {"a": "(numeratore; denominatore)", "d": "Restituisce il quoziente di una divisione.", "ad": "è il dividendo!è il divisore"}, "RADIANS": {"a": "(angolo)", "d": "Converte gradi in radianti.", "ad": "è l'angolo da convertire, espresso in gradi"}, "RAND": {"a": "()", "d": "Restituisce un numero casuale uniformemente distribuito, ossia cambia se viene ricalcolato, e maggiore o uguale a 0 e minore di 1", "ad": ""}, "RANDARRAY": {"a": "([righe]; [colonne]; [min]; [max]; [intero])", "d": "Restituisce una matrice di numeri casuali", "ad": " il numero di righe nella matrice restituita! il numero di colonne nella matrice restituita! ha restituito il numero minimo desiderato! ha restituito il numero massimo di! restituire un numero intero o decimale. TRUE per un numero intero, FALSE per un numero decimale"}, "RANDBETWEEN": {"a": "(minore; maggiore)", "d": "Restituisce un numero casuale compreso tra i numeri specificati.", "ad": "è l'intero più piccolo restituito da CASUALE.TRA!è l'intero più grande restituito da CASUALE.TRA"}, "ROMAN": {"a": "(num; [forma])", "d": "Converte un numero arabo in un numero romano in forma di testo.", "ad": "è il numero arabo da convertire!è il numero che specifica il tipo di numero romano che si desidera."}, "ROUND": {"a": "(num; num_cifre)", "d": "Arrotonda un numero ad un numero specificato di cifre.", "ad": "è il numero da arrotondare!è il numero di cifre a cui si desidera arrotondare. Negativo arrotonda a sinistra della virgola decimale; zero all'intero più prossimo."}, "ROUNDDOWN": {"a": "(num; num_cifre)", "d": "Arrotonda il valore assoluto di un numero per difetto.", "ad": "è un qualsiasi numero reale da arrotondare per difetto!è il numero di cifre a cui si desidera arrotondare. Negativo arrotonda a sinistra della virgola decimale, zero oppure omesso all'intero più prossimo."}, "ROUNDUP": {"a": "(num; num_cifre)", "d": "Arrotonda il valore assoluto di un numero per eccesso.", "ad": "è un qualsiasi numero reale da arrotondare!è il numero di cifre a cui si desidera arrotondare. Negativo arrotonda a sinistra della virgola decimale, zero oppure omesso all'intero più prossimo."}, "SEC": {"a": "(num)", "d": "Restituisce la secante di un angolo", "ad": "è l'angolo espresso in radianti di cui si calcola la secante"}, "SECH": {"a": "(num)", "d": "Restituisce la secante iperbolica di un angolo", "ad": "è l'angolo espresso in radianti di cui si calcola la secante iperbolica"}, "SERIESSUM": {"a": "(x; n; m; coefficienti)", "d": "Restituisce la somma di una serie di potenze basata sulla formula.", "ad": "è il valore di input della serie di potenze!è la potenza iniziale a cui elevare x!è l'incremento di 'n' per ogni termine della serie!è un insieme di coefficienti per cui moltiplicare ogni potenza successiva di x"}, "SIGN": {"a": "(num)", "d": "Restituisce il segno di un numero: 1 se il numero è positivo, zero se il numero è zero o -1 se il numero è negativo.", "ad": "è un qualsiasi numero reale"}, "SIN": {"a": "(radianti)", "d": "Restituisce il seno di un angolo.", "ad": "è l'angolo espresso in radianti di cui si calcola il seno. Gradi * PI()/180 = radianti."}, "SINH": {"a": "(num)", "d": "Restituisce il seno iperbolico di un numero.", "ad": "è un qualsiasi numero reale"}, "SQRT": {"a": "(num)", "d": "Restituisce la radice quadrata di un numero.", "ad": "è il numero del quale si desidera la radice quadrata"}, "SQRTPI": {"a": "(num)", "d": "Restituisce la radice quadrata di (num *pi greco).", "ad": "è il numero per cui moltiplicare pi greco"}, "SUBTOTAL": {"a": "(num_funzione; rif1; ...)", "d": "Restituisce un subtotale in un elenco o un database.", "ad": "è un numero compreso tra 1 e 11 che specifica la funzione di riepilogo per il subtotale!sono da 1 a 254 intervalli o riferimenti di cui calcolare il subtotale"}, "SUM": {"a": "(num1; [num2]; ...)", "d": "Somma i numeri presenti in un intervallo di celle", "ad": "sono da 1 a 255 argomenti di cui ottenere la somma. I valori logici e il testo vengono ignorati, anche se digitati come argomenti."}, "SUMIF": {"a": "(intervallo; criterio; [int_somma])", "d": "Somma le celle specificate secondo una condizione o criterio assegnato.", "ad": "è l'intervallo di celle da analizzare!è il criterio o la condizione, in forma di numero, espressione o testo, che stabilisce quali celle verranno sommate!sono le effettive celle da sommare. Se viene omesso, verranno utilizzate le celle nell'intervallo."}, "SUMIFS": {"a": "(int_somma; intervallo_criteri; criteri; ...)", "d": "Somma le celle specificate da un determinato insieme di condizioni o criteri.", "ad": "sono le celle effettive da sommare!è l'intervallo di celle da valutare per la condizione specificata!è la condizione o il criterio, in forma di numero, espressione o testo, che definisce le celle da sommare"}, "SUMPRODUCT": {"a": "(matrice1; [matrice2]; [matrice3]; ...)", "d": "Moltiplica elementi numerici corrispondenti in matrici o intervalli di dati e restituisce la somma dei prodotti.", "ad": "sono da 2 a 255 matrici di cui moltiplicare e quindi sommare gli elementi. Le matrici devono avere le stesse dimensioni."}, "SUMSQ": {"a": "(num1; [num2]; ...)", "d": "Restituisce la somma dei quadrati degli argomenti. Gli argomenti possono essere numeri, nomi, matrici o riferimenti a celle contenenti numeri.", "ad": "sono da 1 a 255 numeri, nomi, matrici o riferimenti a matrici di cui calcolare la somma dei quadrati"}, "SUMX2MY2": {"a": "(matrice_x; matrice_y)", "d": "Calcola la differenza tra i quadrati di numeri corrispondenti di due intervalli o matrici e restituisce la somma delle differenze.", "ad": "è il primo intervallo o matrice di numeri e può essere un numero, un nome, una matrice o un riferimento contenente numeri!è il secondo intervallo o matrice di numeri e può essere un numero, un nome, una matrice o un riferimento contenente numeri"}, "SUMX2PY2": {"a": "(matrice_x; matrice_y)", "d": "Calcola la somma dei quadrati di numeri corrispondenti di due intervalli o matrici e restituisce la somma delle somme.", "ad": "è il primo intervallo o matrice di numeri e può essere un numero, un nome, una matrice o un riferimento contenente numeri!è il secondo intervallo o matrice di numeri e può essere un numero, un nome, una matrice o un riferimento contenente numeri"}, "SUMXMY2": {"a": "(matrice_x; matrice_y)", "d": "Calcola la differenza tra valori corrispondenti di due intervalli o matrici e restituisce la somma dei quadrati delle differenze.", "ad": "è il primo intervallo o matrice di valori e può essere un numero, un nome, una matrice o un riferimento contenente numeri!è il secondo intervallo o matrice di valori e può essere un numero, un nome, una matrice o un riferimento contenente numeri"}, "TAN": {"a": "(radianti)", "d": "Restituisce la tangente di un numero.", "ad": "è l'angolo espresso in radianti di cui si calcola la tangente. Gradi * PI()/180 = radianti."}, "TANH": {"a": "(num)", "d": "Restituisce la tangente iperbolica di un numero.", "ad": "è un qualsiasi numero reale"}, "TRUNC": {"a": "(num; [num_cifre])", "d": "Elimina la parte decimale di un numero.", "ad": "è il numero da troncare!è un numero che specifica la precisione del troncamento, 0 (zero) se omesso"}, "ADDRESS": {"a": "(riga; col; [ass]; [a1]; [foglio])", "d": "Dati il numero di riga e di colonna, crea un riferimento di cella in formato testo.", "ad": "è il numero di riga da utilizzare nel riferimento di cella; ad esempio, Riga = 1 per la riga 1!è il numero di colonna da utilizzare nel riferimento di cella; ad esempio, Col = 4 per la colonna D!specifica il tipo di riferimento: assoluto = 1; riga assoluta/colonna relativa = 2;  riga relativa/colonna assoluta = 3; relativo = 4!è un valore logico che specifica lo stile di riferimento: stile A1 = 1 o VERO; stile R1C1 = 0 o FALSO!è il testo indicante il nome del foglio di lavoro da utilizzare come riferimento esterno"}, "CHOOSE": {"a": "(indice; val1; [val2]; ...)", "d": "Seleziona un valore o un'azione da eseguire da un elenco di valori in base a un indice", "ad": "specifica quale argomento valore viene selezionato. L'indice deve essere un numero compreso tra 1 e 254, una formula o un riferimento a un numero compreso tra 1 e 254.!sono da 1 a 254 numeri, riferimenti di cella, nomi definiti, formule, funzioni o argomenti di testo tra i quali viene effettuata una selezione tramite SCEGLI"}, "COLUMN": {"a": "([rif])", "d": "Restituisce il numero di colonna di un riferimento.", "ad": "è la cella o l'intervallo di celle contigue da cui ottenere il numero di colonna. Se viene omesso, verrà usata la cella contenente la funzione COLONNA."}, "COLUMNS": {"a": "(matrice)", "d": "Restituisce il numero di colonne in una matrice o riferimento.", "ad": "è una matrice o una formula in forma di matrice oppure un riferimento ad un intervallo di celle da cui ottenere il numero di colonne"}, "HLOOKUP": {"a": "(valore; matrice_tabella; indice; [intervallo])", "d": "Cerca un valore nella prima riga di una tabella o di una matrice e restituisce il valore nella stessa colonna da una riga specificata.", "ad": "è il valore da ricercare nella prima riga della tabella e può essere un valore, un riferimento o una stringa di testo!è la tabella di testo, numeri o valori logici nella quale vengono cercati i dati. Matrice_tabella può essere un riferimento a un intervallo o un nome di intervallo.!è il numero di riga in matrice_tabella da cui viene restituito il valore corrispondente. Riga 1 è la prima riga di valori nella tabella.!è un valore logico: per trovare la corrispondenza più simile nella prima riga, ordinata in ordine ascendente, = VERO oppure omesso; per trovare una corrispondenza esatta = FALSO"}, "HYPERLINK": {"a": "(posizione_collegamento; [nome_collegamento])", "d": "Crea un collegamento ipertestuale che apre un documento memorizzato sul disco rigido, su un server di rete o su Internet.", "ad": "è il percorso completo e il nome del documento che verrà aperto, la posizione di un disco rigido, un indirizzo UNC o un percorso URL!è il testo o il numero visualizzato nella cella. Se viene omesso, nella cella verrà visualizzato il testo Posizione_collegamento"}, "INDEX": {"a": "(matrice; riga; [col]!rif; riga; [col]; [area])", "d": "Restituisce un valore o un riferimento della cella all'intersezione di una particolare riga e colonna in un dato intervallo.", "ad": "è un intervallo di celle o una costante di matrice.!seleziona la riga nella Matrice o nel Riferimento dal quale restituire un valore. Se omesso, sarà necessario specificare Col.!seleziona la colonna nella Matrice o nel Riferimento dal quale restituire un valore. Se omesso, sarà necessario specificare Riga.!è un riferimento a uno o più intervalli di celle!seleziona la riga nella Matrice o nel Riferimento dal quale restituire un valore. Se omesso, sarà necessario specificare Col.!seleziona la colonna nella Matrice o nel Riferimento da cui restituire un valore. Se omesso, sarà necessario specificare Riga.!seleziona un intervallo in Riferimento da cui restituire un valore. Area 1 è la prima area selezionata o immessa, area 2 la seconda e così via"}, "INDIRECT": {"a": "(rif; [a1])", "d": "Restituisce un riferimento indicato da una stringa di testo.", "ad": "è un riferimento a una cella che contiene un riferimento di tipo A1 o di tipo R1C1, un nome definito come riferimento o un riferimento a una cella come stringa di testo!è un valore logico che specifica il tipo di riferimento contenuto in rif: tipo R1C1 = FALSO; tipo A1 = VERO o omesso"}, "LOOKUP": {"a": "(valore; vettore; [risultato]!valore; matrice)", "d": "Ricerca un valore in un intervallo di una riga o di una colonna o da una matrice. Fornito per compatibilità con versioni precedenti", "ad": "è un valore che CERCA in Vettore e può essere un numero, un testo, un valore logico, un nome o un riferimento a un valore!è un intervallo che contiene solo una riga o una colonna di testo, numeri o valori logici posti in ordine ascendente!è un intervallo che contiene solo una riga o una colonna delle stesse dimensioni di Vettore!è un valore che CERCA in una Matrice e può essere un numero, un testo, un valore logico, un nome o un riferimento a un valore!è un intervallo di celle contenente del testo, dei numeri o dei valori logici da confrontare con Valore"}, "MATCH": {"a": "(valore; matrice; [corrisp])", "d": "Restituisce la posizione relativa di un elemento di matrice che corrisponde a un valore specificato in un ordine specificato.", "ad": "è il valore utilizzato per cercare il valore desiderato nella matrice, un numero, testo o un valore logico oppure un riferimento ad essi!è un intervallo contiguo di celle che contengono i possibili valori da cercare, una matrice di valori o un riferimento a una matrice!è un numero (1, 0 o -1) che indica il valore da restituire."}, "OFFSET": {"a": "(rif; righe; colonne; [altezza]; [largh])", "d": "Restituisce un riferimento a un intervallo costituito da un numero specificato di righe e colonne da un riferimento dato.", "ad": "è il riferimento da cui si desidera iniziare lo scostamento, un riferimento a una cella o a un intervallo di celle adiacenti!è il numero di righe, in alto o in basso, da utilizzare come riferimento per la cella superiore sinistra del risultato!è il numero di colonne, in alto o in basso, da utilizzare come riferimento per la cella superiore sinistra del risultato!è l'altezza del risultato espressa in numero di righe. Se viene omessa, sarà pari all'altezza in Riferimento.!è la larghezza del risultato espressa in numero di colonne. Se viene omessa, sarà pari alla larghezza in Riferimento"}, "ROW": {"a": "([rif])", "d": "Restituisce il numero di riga corrispondente a rif", "ad": "è la cella o l'intervallo di celle di cui ottenere il numero di riga. Se viene omesso, verrà restituita la cella contenente la funzione RIGA."}, "ROWS": {"a": "(matrice)", "d": "Restituisce il numero di righe in un riferimento o in una matrice.", "ad": "è una matrice o una formula in forma di matrice oppure un riferimento ad un intervallo di celle di cui ottenere il numero di righe"}, "TRANSPOSE": {"a": "(matrice)", "d": "Converte un intervallo verticale in un un intervallo orizzontale o viceversa.", "ad": "è un intervallo di celle in un foglio di lavoro o una matrice di valori da trasporre"}, "UNIQUE": {"a": "(matrice; [by_col]; [exactly_once])", "d": " Restituisce i valori univoci di un intervallo o di una matrice.", "ad": " l'intervallo o la matrice da cui restituire righe o colonne univoche! è un valore logico: Confronta le righe e restituisce le righe univoche = falso o omesso. Confronta le colonne e restituisce le colonne univoche = vero! è un valore logico: restituisce righe o colonne che si verificano esattamente una volta dalla matrice = vero; Restituisce tutte le righe o le colonne distinte dalla matrice = FALSE o omesso"}, "VLOOKUP": {"a": "(valore; matrice_tabella; indice; [intervallo])", "d": "Cerca un valore nella prima colonna sinistra di una tabella e restituisce un valore nella stessa riga da una colonna specificata. La tabella viene ordinata in ordine crescente per impostazione predefinita.", "ad": "è il valore da ricercare nella prima colonna della tabella e può essere un valore, un riferimento o una stringa di testo!è una tabella di testo, numeri o valori logici nella quale vengono cercati i dati. Matrice_tabella può essere un riferimento a un intervallo o un nome di intervallo.!è il numero di colonna in matrice_tabella da cui viene restituito il valore corrispondente. La prima colonna di valori nella tabella è la colonna 1.!è un valore logico: per trovare la corrispondenza più simile nella prima colonna (in ordine crescente) = VERO oppure omesso; trova una corrispondenza esatta = FALSO"}, "XLOOKUP": {"a": "(valore; matrice_ricerca; matrice_restituita; [se_non_trovato]; [modalità_confronto]; [modalità_ricerca])", "d": "Cerca una corrispondenza in un intervallo o una matrice e restituisce l'elemento corrispondente di una seconda matrice o intervallo. Per impostazione predefinita, viene utilizzata una corrispondenza esatta", "ad": "è il valore da cercare!è la matrice o intervallo da cercare!è la matrice o l'intervallo da restituire!restituito se non viene trovata alcuna corrispondenza!specifica come confrontare il valore di ricerca e i valori nella matrice di ricerca!specifica la modalità di ricerca da usare. Per impostazione predefinita, si effettuerà una ricerca dal primo all'ultimo valore"}, "CELL": {"a": "(info_type; [reference])", "d": "Restituisce informazioni sulla formattazione, la posizione o il contenuto di una cella", "ad": "valore di testo che indica il tipo di dati della cella che devono essere restituiti!cella di cui si desidera ottenere informazioni"}, "ERROR.TYPE": {"a": "(errore)", "d": "Restituisce un numero corrispondente a uno dei valori di errore.", "ad": "è il valore di errore di cui trovare il numero di identificazione. Può essere un valore di errore o un riferimento a una cella contenente un valore di errore"}, "ISBLANK": {"a": "(val)", "d": "Restituisce VERO se il valore è una cella vuota.", "ad": "è la cella o il nome che fa riferimento alla cella da verificare"}, "ISERR": {"a": "(val)", "d": "Controlla se un valore è un errore diverso da #N/A e restituisce VERO o FALSO.", "ad": "è il valore da verificare. Il valore può riferirsi a una cella, a una formula o a un nome che fa riferimento a una cella, a una formula o a un valore"}, "ISERROR": {"a": "(val)", "d": "Controlla se un valore è un errore e restituisce VERO o FALSO.", "ad": "è il valore da verificare. Il valore può riferirsi a una cella, a una formula o a un nome che fa riferimento a una cella, a una formula o a un valore"}, "ISEVEN": {"a": "(num)", "d": "Restituisce VERO se il numero è pari.", "ad": "è il valore da esaminare"}, "ISFORMULA": {"a": "(riferimento)", "d": "Controlla se il riferimento specificato è a una cella contenente una formula e restituisce VERO o FALSO", "ad": "è un riferimento alla cella da verificare. Riferimento può essere un riferimento di cella, una formula o un nome che fa riferimento a una cella"}, "ISLOGICAL": {"a": "(val)", "d": "Restituisce VERO se Valore è un valore logico, VERO o FALSO.", "ad": "è il valore da verificare. <PERSON><PERSON> può riferirsi a una cella, a una formula o a un nome che fa riferimento a una cella, a una formula o a un valore"}, "ISNA": {"a": "(val)", "d": "Controlla se un valore è #N/D e restituisce VERO o FALSO.", "ad": "è il valore da verificare. Il valore può riferirsi a una cella, a una formula o a un nome che fa riferimento a una cella, a una formula o a un valore"}, "ISNONTEXT": {"a": "(val)", "d": "Restituisce VERO se il valore non è del testo (le celle vuote non sono testo).", "ad": "è il valore da verificare: una cella, una formula o un nome che fa riferimento a una cella, a una formula o a un valore"}, "ISNUMBER": {"a": "(val)", "d": "Restituisce VERO se il valore è un numero.", "ad": "è il valore da verificare. <PERSON><PERSON> può riferirsi a una cella, a una formula o a un nome che fa riferimento a una cella, a una formula o a un valore"}, "ISODD": {"a": "(num)", "d": "Restituisce VERO se il numero è dispari.", "ad": "è il valore da esaminare"}, "ISREF": {"a": "(val)", "d": "Controlla se il valore è un riferimento e restituisce VERO o FALSO.", "ad": "è il valore da verificare. <PERSON><PERSON> può riferirsi a una cella, a una formula o a un nome che fa riferimento a una cella, a una formula o a un valore"}, "ISTEXT": {"a": "(val)", "d": "Restituisce VERO se il valore è un testo.", "ad": "è il valore da verificare. <PERSON><PERSON> può riferirsi a una cella, a una formula o a un nome che si riferisce a una cella, a una formula o a un valore."}, "N": {"a": "(val)", "d": "Converte stringhe di valori in numeri, date in numeri seriali,  VERO in 1, ogni altro valore in 0 (zero).", "ad": "è il valore da convertire"}, "NA": {"a": "()", "d": "Restituisce il valore di errore #N/D (valore non disponibile).", "ad": ""}, "SHEET": {"a": "([valore])", "d": "Restituisce il numero del foglio del riferimento", "ad": "è il nome di un foglio o un riferimento per il quale si vuole ottenere il numero di foglio. Se omesso, viene restituito il numero del foglio contenente la funzione"}, "SHEETS": {"a": "([riferimento])", "d": "Restituisce il numero di fogli in un riferimento", "ad": "è un riferimento per il quale si vuole conoscere il numero di fogli contenuti. Se omesso, viene restituito il numero di fogli nella cartella di lavoro contenente la funzione"}, "TYPE": {"a": "(val)", "d": "Restituisce un numero intero che indica il tipo di dati di un valore: numero = 1; testo = 2; valore logico = 4; valore di errore = 16; matrice = 64; dati composti = 128", "ad": "può essere un qualsiasi valore"}, "AND": {"a": "(logico1; [logico2]; ...)", "d": "Restituisce VERO se tutti gli argomenti hanno valore VERO.", "ad": "sono da 1 a 255 condizioni (valori logici, matrici o riferimenti) da verificare, che possono avere valore VERO o FALSO"}, "FALSE": {"a": "()", "d": "Restituisce il valore logico FALSO.", "ad": ""}, "IF": {"a": "(test; [se_vero]; [se_falso])", "d": "Restituisce un valore se una condizione specificata dà come risultato VERO e un altro valore se dà come risultato FALSO.", "ad": "è un valore o un'espressione qualsiasi che può dare come risultato VERO o FALSO!è il valore che viene restituito se test è VERO. Se viene omesso, verrà restituito VERO. È possibile annidare fino a sette funzioni SE.!è il valore che viene restituito se test è FALSO. Se viene omesso, verrà restituito FALSO."}, "IFNA": {"a": "(valore; valore_se_nd)", "d": "Restituisce il valore specificato se l'espressione restituisce #N/D, in caso contrario restituisce il risultato dell'espressione", "ad": "è un valore, un'espressione o un riferimento qualsiasi!è un valore, un'espressione o un riferimento qualsiasi"}, "IFERROR": {"a": "(valore; valore_se_errore)", "d": "Restituisce valore_se_errore se l'espressione genera un errore, in caso contrario restituisce il valore dell'espressione stessa.", "ad": "è un valore, un'espressione o un riferimento qualsiasi!è un valore, un'espressione o un riferimento qualsiasi"}, "NOT": {"a": "(logico)", "d": "Inverte il valore logico dell'argomento: restituisce FALSO per un argomento VERO e VERO per un argomento FALSO.", "ad": "è un valore o un'espressione che può dare come risultato VERO o FALSO"}, "SWITCH": {"a": "(espressione; valore1; risultato1; [predefinito_o_valore2]; [risultato2]; ...)", "d": "Valuta un'espressione rispetto a un elenco di valori e restituisce il risultato associato al primo valore corrispondente. Se non ci sono corrispondenze, viene restituito un valore predefinito facoltativo", "ad": "è un'espressione da valutare!è un valore da confrontare con l'espressione!è un risultato da restituire se il valore associato corrisponde all'espressione"}, "OR": {"a": "(logico1; [logico2]; ...)", "d": "Restituisce VERO se un argomento qualsiasi è VERO, FALSO se tutti gli argomenti sono FALSO.", "ad": "sono da 1 a 255 condizioni da verificare, che possono avere valore VERO o FALSO"}, "TRUE": {"a": "()", "d": "Restituisce il valore logico VERO.", "ad": ""}, "XOR": {"a": "(logico1; [logico2]; ...)", "d": "Restituisce un 'OR esclusivo' logico di tutti gli argomenti", "ad": "sono le condizioni da 1 a 254 da verificare che possono essere VERO o FALSO e possono essere valori logici, matrici o riferimenti"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Restituisce il testo che si trova prima dei caratteri di delimitazione.", "ad": "Il testo che si desidera cercare per il delimitatore.!Il carattere o la stringa da usare come delimitatore.!La ricorrenza desiderata del delimitatore. L'impostazione predefinita è 1. Un numero negativo esegue la ricerca a partire dalla fine.!Cerca nel testo una corrispondenza con il delimitatore. Per impostazione predefinita, viene eseguita una corrispondenza sensibile alle maiuscole e alle minuscole.!Se confrontare il delimitatore con la fine del testo. Per impostazione predefinita, non vengono abbinati.!Viene restituito se non viene trovata alcuna corrispondenza. Per impostazione predefinita, viene restituito #N/A."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Restituisce il testo che si trova dopo i caratteri di delimitazione.", "ad": "Il testo che si desidera cercare per il delimitatore.!Il carattere o la stringa da usare come delimitatore.!La ricorrenza desiderata del delimitatore. L'impostazione predefinita è 1. Un numero negativo esegue la ricerca a partire dalla fine.!Cerca nel testo una corrispondenza con il delimitatore. Per impostazione predefinita, viene eseguita una corrispondenza sensibile alle maiuscole e alle minuscole.!Se confrontare il delimitatore con la fine del testo. Per impostazione predefinita, non vengono abbinati.!Viene restituito se non viene trovata alcuna corrispondenza. Per impostazione predefinita, viene restituito #N/A."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Divide il testo in righe o colonne tramite i delimitatori.", "ad": "Il testo da dividere!<PERSON>ttere o stringa per cui dividere le colonne.!<PERSON>ttere o stringa per cui dividere le righe.!Se ignorare le celle vuote. L'impostazione predefinita è FALSO.!Cerca nel testo una corrispondenza con il delimitatore. Per impostazione predefinita, viene eseguita una corrispondenza Maiuscole/minuscole.!Il valore da utilizzare per la spaziatura interna. Per impostazione predefinita, viene usato #N/D."}, "WRAPROWS": {"a": "(vettore, wrap_count, [pad_with])", "d": "Esegue il wrapping di un vettore di riga o colonna dopo un numero specificato di valori.", "ad": "Vettore o riferimento da eseguire il wrapping.!Numero massimo di valori per riga.!Valore con cui aggiungere il riempimento. Il valore predefinito è #N/D."}, "VSTACK": {"a": "(matrice1, [matrice2], ...)", "d": "Impila verticalmente le matrici in un'unica matrice.", "ad": "<PERSON>rice o riferimento da impilare."}, "HSTACK": {"a": "(matrice1, [matrice2], ...)", "d": "Impila orizzontalmente le matrici in un'unica matrice.", "ad": "<PERSON>rice o riferimento da impilare."}, "CHOOSEROWS": {"a": "(matrice, row_num1, [row_num2], ...)", "d": "Restituisce righe da una matrice o un riferimento.", "ad": "La matrice o il riferimento contenente le righe da restituire.!Il numero della riga da restituire."}, "CHOOSECOLS": {"a": "(matrice, col_num1, [col_num2], ...)", "d": "Restituisce colonne specificate da una matrice o un riferimento.", "ad": "La matrice o il riferimento contenente le colonne da restituire !Il numero della colonna da restituire."}, "TOCOL": {"a": "(matrice, [ignora], [scan_by_column])", "d": "Restituisce la matrice come una colonna. ", "ad": "Matrice o riferimento da restituire come colonna.!Indica se ignorare determinati tipi di valori. Per impostazione predefinita, nessun valore viene ignorato.!Analizzare la matrice in base alla colonna. Per impostazione predefinita, la matrice viene analizzata per riga."}, "TOROW": {"a": "(matrice, [ignora], [scan_by_column])", "d": "Restituisce la matrice come una riga.", "ad": "La matrice o riferimento da restituire come riga.!Indica se ignorare determinati tipi di valori. Per impostazione predefinita, nessun valore viene ignorato.!Analizzare la matrice in base alla colonna. Per impostazione predefinita, la matrice viene analizzata per riga."}, "WRAPCOLS": {"a": "(vettore, wrap_count, [pad_with])", "d": "Esegue il wrapping di un vettore di riga o colonna dopo un numero specificato di valori.", "ad": "Vettore o riferimento da eseguire il wrapping.!Numero massimo di valori per colonna.!Valore con cui aggiungere il riempimento. Il valore predefinito è #N/D."}, "TAKE": {"a": "(matrice, righe, [colonne])", "d": "Restituisce righe o colonne dall'inizio o dalla fine della matrice.", "ad": "Matrice da cui accettare righe o colonne.!Numero di righe da accettare. Un valore negativo viene accettato dalla fine della matrice.!Numero di colonne da accettare. Un valore negativo viene accettato dalla fine della matrice."}, "DROP": {"a": "(matrice, righe, [colonne])", "d": "Elimina righe o colonne dall'inizio o dalla fine della matrice.", "ad": "Matrice da cui eliminare righe o colonne.! Numero di righe da eliminare. Un valore negativo viene eliminato dalla fine della matrice.! Numero di colonne da eliminare. Un valore negativo viene eliminato dalla fine della matrice."}, "SEQUENCE": {"a": "(righe, [colonne], [inizio], [passaggio])", "d": "Restituisce una sequenza di numeri", "ad": "il numero di righe da restituire!il numero di colonne da restituire!il primo numero della sequenza!l'incremento di ogni valore successivo della sequenza"}, "EXPAND": {"a": "(matrice, righe, [colonne], [pad_with])", "d": "Espande una matrice alle dimensioni specificate.", "ad": "Matrice da espandere.!Numero di righe nella matrice espansa. Se mancante, le righe non verranno espanse.!Numero di colonne nella matrice espansa. Se mancante, le colonne non verranno espanse.!<PERSON>ore da riempire. Il valore predefinito è #N/D."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "Restituisce la posizione relativa di un elemento in una matrice. Per impostazione predefinita, è necessaria una corrispondenza esatta", "ad": "è il valore da cercare!è la matrice o intervallo da cercare!specifica come confrontare il valore da cercare e i valori nella matrice!specifica la modalità di ricerca da usare. Per impostazione predefinita, si effettuerà una ricerca dal primo all'ultimo valore"}, "FILTER": {"a": "(matrice, includi, [se_vuoto])", "d": "Filtra un intervallo o una matrice", "ad": "l'intervallo o la matrice da filtrare!matrice di booleani, dove VERO rappresenta una riga o una colonna da mantenere!valore restituito se non vengono mantenuti elementi"}, "ARRAYTOTEXT": {"a": "(matrice, [formato])", "d": "Restituisce una rappresentazione testuale di una matrice", "ad": " la matrice da rappresentare come testo! il formato del testo"}, "SORT": {"a": "(matrice, [indice_ordinamento], [ordinamento], [per_col])", "d": "Ordina un intervallo o una matrice", "ad": "l'intervallo o la matrice da ordinaret!un numero che indica la riga o la colonna in base a cui ordinare!un numero che indica il tipo di ordinamento selezionato; 1 per ordine crescente (impostazione predefinita), -1 per ordine decrescente!un valore logico che indica la direzione di ordinamento desiderata: FALSO per ordinare per riga (impostazione predefinita), VERO per ordinare per colonna"}, "SORTBY": {"a": "(matrice, con_matrice, [ordinamento], ...)", "d": "Ordina un intervallo o una matrice in base ai valori di una matrice o intervallo corrispondente", "ad": "intervallo o matrice da ordinare!intervallo o matrice in base a cui ordinare!un numero che indica l'ordinamento desiderato; 1 per ordinamento crescente (impostazione predefinita), -1 per ordinamento decrescente"}, "GETPIVOTDATA": {"a": "(campo_dati; tabella_pivot; [campo]; [elemento]; ...)", "d": "E<PERSON><PERSON> i dati memorizzati in una tabella pivot.", "ad": "è il nome del campo dati dal quale estrarre i dati!è un riferimento a una cella o intervallo di celle nella tabella pivot che contiene i dati da recuperare!è il campo a cui fare riferimento!è l'elemento del campo a cui fare riferimento"}, "IMPORTRANGE": {"a": "(url_foglio_di_lavoro; stringa_intervallo)", "d": "Importa un intervallo di celle da un foglio di lavoro specificato.", "ad": "l'URL del foglio di lavoro da cui importare i dati!l'intervallo da importare"}}