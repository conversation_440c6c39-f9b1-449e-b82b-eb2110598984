{"Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "Common.Controllers.Desktop.hintBtnHome": "Show main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Anonym", "Common.Controllers.ExternalDiagramEditor.textClose": "Lukk", "Common.Controllers.ExternalDiagramEditor.warningText": "The object is disabled because it is being edited by another user.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.textAnonymous": "Anonym", "Common.Controllers.ExternalMergeEditor.textClose": "Lukk", "Common.Controllers.ExternalMergeEditor.warningText": "The object is disabled because it is being edited by another user.", "Common.Controllers.ExternalMergeEditor.warningTitle": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textAnonymous": "Anonym", "Common.Controllers.ExternalOleEditor.textClose": "Lukk", "Common.Controllers.ExternalOleEditor.warningText": "The object is disabled because it is being edited by another user.", "Common.Controllers.ExternalOleEditor.warningTitle": "<PERSON><PERSON><PERSON>", "Common.Controllers.History.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "In order to compare documents all the tracked changes in them will be considered to have been accepted. Do you want to continue?", "Common.Controllers.ReviewChanges.textAtLeast": "minst", "Common.Controllers.ReviewChanges.textAuto": "auto", "Common.Controllers.ReviewChanges.textBaseline": "Basislinje", "Common.Controllers.ReviewChanges.textBold": "Fet", "Common.Controllers.ReviewChanges.textBreakBefore": "Page break before", "Common.Controllers.ReviewChanges.textCaps": "Store bokstaver", "Common.Controllers.ReviewChanges.textCenter": "Still opp senter", "Common.Controllers.ReviewChanges.textChar": "Character level", "Common.Controllers.ReviewChanges.textChart": "Diagram", "Common.Controllers.ReviewChanges.textColor": "Skriftfarge", "Common.Controllers.ReviewChanges.textContextual": "Don't add interval between paragraphs of the same style", "Common.Controllers.ReviewChanges.textDeleted": "<b>Slettet:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "Double strikethrough", "Common.Controllers.ReviewChanges.textEquation": "Equation", "Common.Controllers.ReviewChanges.textExact": "nøyaktig", "Common.Controllers.ReviewChanges.textFirstLine": "<PERSON><PERSON><PERSON><PERSON> linje", "Common.Controllers.ReviewChanges.textFontSize": "Font size", "Common.Controllers.ReviewChanges.textFormatted": "Formatted", "Common.Controllers.ReviewChanges.textHighlight": "Highlight color", "Common.Controllers.ReviewChanges.textImage": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textIndentLeft": "Indent left", "Common.Controllers.ReviewChanges.textIndentRight": "Indent right", "Common.Controllers.ReviewChanges.textInserted": "<b>Satt inn:</b>", "Common.Controllers.ReviewChanges.textItalic": "Italic", "Common.Controllers.ReviewChanges.textJustify": "Still opp jevnt", "Common.Controllers.ReviewChanges.textKeepLines": "Keep lines together", "Common.Controllers.ReviewChanges.textKeepNext": "Keep with next", "Common.Controllers.ReviewChanges.textLeft": "Still opp venstre", "Common.Controllers.ReviewChanges.textLineSpacing": "Line spacing: ", "Common.Controllers.ReviewChanges.textMultiple": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textNoBreakBefore": "No page break before", "Common.Controllers.ReviewChanges.textNoContextual": "Legg til mellomrom mellom", "Common.Controllers.ReviewChanges.textNoKeepLines": "Don't keep lines together", "Common.Controllers.ReviewChanges.textNoKeepNext": "Don't keep with next", "Common.Controllers.ReviewChanges.textNot": "Not ", "Common.Controllers.ReviewChanges.textNoWidow": "No widow control", "Common.Controllers.ReviewChanges.textNum": "<PERSON><PERSON> num<PERSON>", "Common.Controllers.ReviewChanges.textOff": "{0} sporer ikke endringer lenger.", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} deaktive<PERSON> \"spor endringer\" for alle.", "Common.Controllers.ReviewChanges.textOn": "{0} sporer n<PERSON> endringer.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} aktiverte \"spor endringer\" for alle.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b><PERSON><PERSON><PERSON><PERSON><PERSON> slettet</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "Paragraph formatted", "Common.Controllers.ReviewChanges.textParaInserted": "<b>Avsnitt satt inn</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>Flyttet ned:</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>Flyttet opp:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>Flyttet</b>", "Common.Controllers.ReviewChanges.textPosition": "Posisjon", "Common.Controllers.ReviewChanges.textRight": "Still opp høyre", "Common.Controllers.ReviewChanges.textShape": "<PERSON><PERSON>r", "Common.Controllers.ReviewChanges.textShd": "Bakgrunnsfarge", "Common.Controllers.ReviewChanges.textShow": "Show changes at", "Common.Controllers.ReviewChanges.textSmallCaps": "Small caps", "Common.Controllers.ReviewChanges.textSpacing": "Avstand", "Common.Controllers.ReviewChanges.textSpacingAfter": "Av<PERSON> etter", "Common.Controllers.ReviewChanges.textSpacingBefore": "<PERSON><PERSON><PERSON> før", "Common.Controllers.ReviewChanges.textStrikeout": "Gjennomstreking", "Common.Controllers.ReviewChanges.textSubScript": "Senket skrift", "Common.Controllers.ReviewChanges.textSuperScript": "<PERSON><PERSON> skrift", "Common.Controllers.ReviewChanges.textTableChanged": "<b><PERSON>bell<PERSON><PERSON><PERSON><PERSON> endret</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b><PERSON><PERSON><PERSON><PERSON> lagt til</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b><PERSON><PERSON><PERSON><PERSON> slettet</b>", "Common.Controllers.ReviewChanges.textTabs": "<PERSON><PERSON>er", "Common.Controllers.ReviewChanges.textTitleComparison": "Innstillinger for sammenlikning", "Common.Controllers.ReviewChanges.textUnderline": "Understreking", "Common.Controllers.ReviewChanges.textUrl": "Paste a document URL", "Common.Controllers.ReviewChanges.textWidow": "Widow control", "Common.Controllers.ReviewChanges.textWord": "Word level", "Common.define.chartData.textArea": "Areal", "Common.define.chartData.textAreaStacked": "Stacked area", "Common.define.chartData.textAreaStackedPer": "100% stablet område", "Common.define.chartData.textBar": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Clustered column", "Common.define.chartData.textBarNormal3d": "3D gruppert kolonne", "Common.define.chartData.textBarNormal3dPerspective": "3D kolonne", "Common.define.chartData.textBarStacked": "Stacked column", "Common.define.chartData.textBarStacked3d": "3D stablet kolonne", "Common.define.chartData.textBarStackedPer": "100% stablet kolonne", "Common.define.chartData.textBarStackedPer3d": "3D 100% stablet kolonne", "Common.define.chartData.textCharts": "Diagrammer", "Common.define.chartData.textColumn": "Kolonne", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Stacked area - clustered column", "Common.define.chartData.textComboBarLine": "Clustered column - line", "Common.define.chartData.textComboBarLineSecondary": "Clustered column - line on secondary axis", "Common.define.chartData.textComboCustom": "Custom combination", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "Clustered bar", "Common.define.chartData.textHBarNormal3d": "3D gruppert stolpe", "Common.define.chartData.textHBarStacked": "Stacked bar", "Common.define.chartData.textHBarStacked3d": "3D stablet stolpe", "Common.define.chartData.textHBarStackedPer": "100% stablet stolpe", "Common.define.chartData.textHBarStackedPer3d": "3D 100% Stablet stolpe", "Common.define.chartData.textLine": "Line", "Common.define.chartData.textLine3d": "3D linje", "Common.define.chartData.textLineMarker": "Line with markers", "Common.define.chartData.textLineStacked": "Stacked line", "Common.define.chartData.textLineStackedMarker": "Stacked line with markers", "Common.define.chartData.textLineStackedPer": "100% stablet linje", "Common.define.chartData.textLineStackedPerMarker": "100% stablet linje med markering", "Common.define.chartData.textPie": "Sirkeldiagram", "Common.define.chartData.textPie3d": "3D sirkeldiagram", "Common.define.chartData.textPoint": "XY (<PERSON><PERSON><PERSON>)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Scatter with straight lines", "Common.define.chartData.textScatterLineMarker": "Scatter with straight lines and markers", "Common.define.chartData.textScatterSmooth": "Scatter with smooth lines", "Common.define.chartData.textScatterSmoothMarker": "Scatter with smooth lines and markers", "Common.define.chartData.textStock": "Stock", "Common.define.chartData.textSurface": "Surface", "Common.define.smartArt.textAccentedPicture": "Aksenttegnsbilde", "Common.define.smartArt.textAccentProcess": "Aksenttegnprosess", "Common.define.smartArt.textAlternatingFlow": "Alternerende flyt", "Common.define.smartArt.textAlternatingHexagons": "Alternerende sekskanter", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternerende bildeblokker", "Common.define.smartArt.textAlternatingPictureCircles": "Alternerende bildesirkler", "Common.define.smartArt.textArchitectureLayout": "Arkitekturlayout", "Common.define.smartArt.textArrowRibbon": "Pilbånd", "Common.define.smartArt.textAscendingPictureAccentProcess": "Stigende aksenttegnprosess", "Common.define.smartArt.textBalance": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "<PERSON><PERSON> underfallende prosess", "Common.define.smartArt.textBasicBlockList": "<PERSON><PERSON> b<PERSON>", "Common.define.smartArt.textBasicChevronProcess": "<PERSON><PERSON>", "Common.define.smartArt.textBasicCycle": "<PERSON><PERSON>", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON> matrise", "Common.define.smartArt.textBasicPie": "<PERSON>kelt sir<PERSON>gram", "Common.define.smartArt.textBasicProcess": "<PERSON><PERSON> prosess", "Common.define.smartArt.textBasicPyramid": "Enkel pyramide", "Common.define.smartArt.textBasicRadial": "Enkel radial", "Common.define.smartArt.textBasicTarget": "<PERSON><PERSON><PERSON> mål", "Common.define.smartArt.textBasicTimeline": "<PERSON><PERSON> t<PERSON>", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON><PERSON> Venn-diagram", "Common.define.smartArt.textBendingPictureAccentList": "<PERSON><PERSON><PERSON> a<PERSON>enttegnsliste", "Common.define.smartArt.textBendingPictureBlocks": "B<PERSON>yd bildeblokk", "Common.define.smartArt.textBendingPictureCaption": "<PERSON><PERSON><PERSON>krive<PERSON>", "Common.define.smartArt.textBendingPictureCaptionList": "Bøyd bildebeskrivelsesliste", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bøyd semi-transparent bildetekst", "Common.define.smartArt.textBlockCycle": "Blokksyklus", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron accent process", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Circle accent timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging arrows", "Common.define.smartArt.textDivergingRadial": "Diverging radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon Radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy list", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined List", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and title organization chart", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing ideas", "Common.define.smartArt.textOrganizationChart": "Organization СЃhart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased process", "Common.define.smartArt.textPicture": "<PERSON><PERSON>", "Common.define.smartArt.textPictureAccentBlocks": "Picture accent blocks", "Common.define.smartArt.textPictureAccentList": "Picture accent list", "Common.define.smartArt.textPictureAccentProcess": "Picture accent process", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture strips", "Common.define.smartArt.textPieProcess": "Sirkeldiagramprosess", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "Spiral Picture", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Tabbed <PERSON>", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "More", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "You can't edit this file because it's being edited in another app.", "Common.Translation.warnFileLockedBtnEdit": "Lag en kopi", "Common.Translation.warnFileLockedBtnView": "Open for viewing", "Common.UI.ButtonColored.textAutoColor": "Automatisk", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "Legg til ny egendefinert farge", "Common.UI.Calendar.textApril": "april", "Common.UI.Calendar.textAugust": "august", "Common.UI.Calendar.textDecember": "desember", "Common.UI.Calendar.textFebruary": "February", "Common.UI.Calendar.textJanuary": "January", "Common.UI.Calendar.textJuly": "July", "Common.UI.Calendar.textJune": "June", "Common.UI.Calendar.textMarch": "March", "Common.UI.Calendar.textMay": "May", "Common.UI.Calendar.textMonths": "Months", "Common.UI.Calendar.textNovember": "November", "Common.UI.Calendar.textOctober": "October", "Common.UI.Calendar.textSeptember": "September", "Common.UI.Calendar.textShortApril": "apr", "Common.UI.Calendar.textShortAugust": "Aug", "Common.UI.Calendar.textShortDecember": "Dec", "Common.UI.Calendar.textShortFebruary": "Feb", "Common.UI.Calendar.textShortFriday": "Fr", "Common.UI.Calendar.textShortJanuary": "Jan", "Common.UI.Calendar.textShortJuly": "Jul", "Common.UI.Calendar.textShortJune": "Jun", "Common.UI.Calendar.textShortMarch": "Mar", "Common.UI.Calendar.textShortMay": "May", "Common.UI.Calendar.textShortMonday": "Mo", "Common.UI.Calendar.textShortNovember": "Nov", "Common.UI.Calendar.textShortOctober": "Oct", "Common.UI.Calendar.textShortSaturday": "Sa", "Common.UI.Calendar.textShortSeptember": "Sep", "Common.UI.Calendar.textShortSunday": "Su", "Common.UI.Calendar.textShortThursday": "Th", "Common.UI.Calendar.textShortTuesday": "Tu", "Common.UI.Calendar.textShortWednesday": "We", "Common.UI.Calendar.textYears": "Years", "Common.UI.ComboBorderSize.txtNoBorders": "No borders", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "No borders", "Common.UI.ComboDataView.emptyComboText": "No styles", "Common.UI.ExtendedColorDialog.addButtonText": "Legg til", "Common.UI.ExtendedColorDialog.textCurrent": "Nåværende", "Common.UI.ExtendedColorDialog.textHexErr": "The entered value is incorrect.<br>Please enter a value between 000000 and FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "New", "Common.UI.ExtendedColorDialog.textRGBErr": "The entered value is incorrect.<br>Please enter a numeric value between 0 and 255.", "Common.UI.HSBColorPicker.textNoColor": "No color", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnCalendar.textDate": "Select date", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Hide password", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Show password", "Common.UI.SearchBar.textFind": "<PERSON>", "Common.UI.SearchBar.tipCloseSearch": "Close find", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Previous result", "Common.UI.SearchDialog.textHighlight": "Highlight results", "Common.UI.SearchDialog.textMatchCase": "Følsom for store og små bokstaver", "Common.UI.SearchDialog.textReplaceDef": "Enter the replacement text", "Common.UI.SearchDialog.textSearchStart": "Enter your text here", "Common.UI.SearchDialog.textTitle": "Finn og erstatt", "Common.UI.SearchDialog.textTitle2": "<PERSON>", "Common.UI.SearchDialog.textWholeWords": "Whole words only", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "Erstatt alle", "Common.UI.SynchronizeTip.textDontShow": "Don't show this message again", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "The document has been changed by another user.<br>Please click to save your changes and reload the updates.", "Common.UI.ThemeColorPalette.textRecentColors": "Recent colors", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textTransparent": "Transparent", "Common.UI.Themes.txtThemeClassicLight": "Classic Light", "Common.UI.Themes.txtThemeContrastDark": "Contrast Dark", "Common.UI.Themes.txtThemeDark": "Dark", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "Light", "Common.UI.Themes.txtThemeSystem": "Same as system", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "Lukk", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Bekreftelse", "Common.UI.Window.textDontShow": "Don't show this message again", "Common.UI.Window.textError": "<PERSON><PERSON>", "Common.UI.Window.textInformation": "Informasjon", "Common.UI.Window.textWarning": "<PERSON><PERSON><PERSON>", "Common.UI.Window.yesButtonText": "<PERSON>a", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Aksenttegn", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Bakgrunn", "Common.Utils.ThemeColor.txtBlack": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtBlue": "Blå", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Dark green", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON>", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "adresse:", "Common.Views.About.txtLicensee": "LICENSEE", "Common.Views.About.txtLicensor": "LICENSOR", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Drevet av", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Versjon", "Common.Views.AutoCorrectDialog.textAdd": "Legg til", "Common.Views.AutoCorrectDialog.textApplyText": "Utfør følgende endringer mens du skriver", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Text AutoCorrect", "Common.Views.AutoCorrectDialog.textAutoFormat": "Fortløpende auto-formatering", "Common.Views.AutoCorrectDialog.textBulleted": "Oppdag og omgjør til punktlister automatisk", "Common.Views.AutoCorrectDialog.textBy": "Av", "Common.Views.AutoCorrectDialog.textDelete": "<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Legg til punktum med dobbeltmellomrom", "Common.Views.AutoCorrectDialog.textFLCells": "Capitalize first letter of table cells", "Common.Views.AutoCorrectDialog.textFLDont": "Don`t capitalize after", "Common.Views.AutoCorrectDialog.textFLSentence": "Capitalize first letter of sentences", "Common.Views.AutoCorrectDialog.textForLangFL": "Exceptions for the language:", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet and network paths with hyperlinks", "Common.Views.AutoCorrectDialog.textHyphens": "Hyphens (--) with dash (вЂ”)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Math AutoCorrect", "Common.Views.AutoCorrectDialog.textNumbered": "Oppdag og omgjør til nummererte lister automatisk", "Common.Views.AutoCorrectDialog.textQuotes": "\"Rette sitattegn\" med “smarte sitattegn”", "Common.Views.AutoCorrectDialog.textRecognized": "Recognized functions", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "The following expressions are recognized math expressions. They will not be automatically italicized.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "Replace as you type", "Common.Views.AutoCorrectDialog.textReplaceType": "Replace text as you type", "Common.Views.AutoCorrectDialog.textReset": "Reset", "Common.Views.AutoCorrectDialog.textResetAll": "Reset to default", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Autokorrektur", "Common.Views.AutoCorrectDialog.textWarnAddFL": "Exceptions must contain only the letters, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Recognized functions must contain only the letters A through Z, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnResetFL": "Alle unntak du har lagt til vil reverseres og de som er fjernet vil bli gjenopprettet. Vil du fortsette?", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Alle uttrykk du har lagt til vil fjernes og de du har tatt vekk vil bli gjenopprettet. Vil du fortsette?", "Common.Views.AutoCorrectDialog.warnReplace": "The autocorrect entry for %1 already exists. Do you want to replace it?", "Common.Views.AutoCorrectDialog.warnReset": "Alle autokorrekturendringer du har utført vil reverseres til sine tidligere verdier. Vil du fortsette?", "Common.Views.AutoCorrectDialog.warnRestore": "The autocorrect entry for %1 will be reset to its original value. Do you want to continue?", "Common.Views.Chat.textChat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "Send", "Common.Views.Comments.mniAuthorAsc": "Forfatter A til Å", "Common.Views.Comments.mniAuthorDesc": "Forfatter Å til A", "Common.Views.Comments.mniDateAsc": "Oldest", "Common.Views.Comments.mniDateDesc": "Newest", "Common.Views.Comments.mniFilterGroups": "Filter by group", "Common.Views.Comments.mniPositionAsc": "From top", "Common.Views.Comments.mniPositionDesc": "From bottom", "Common.Views.Comments.textAdd": "Legg til", "Common.Views.Comments.textAddComment": "Tilføy", "Common.Views.Comments.textAddCommentToDoc": "Tilføy kommentar til", "Common.Views.Comments.textAddReply": "<PERSON><PERSON> til svar", "Common.Views.Comments.textAll": "Alle", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "Lukk", "Common.Views.Comments.textClosePanel": "Close comments", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Enter your comment here", "Common.Views.Comments.textHintAddComment": "Legg til kommentar", "Common.Views.Comments.textOpenAgain": "Open again", "Common.Views.Comments.textReply": "<PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "Resolved", "Common.Views.Comments.textSort": "Sort comments", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "You have no permission to reopen the comment", "Common.Views.Comments.txtEmpty": "There are no comments in the document.", "Common.Views.CopyWarningDialog.textDontShow": "Don't show this message again", "Common.Views.CopyWarningDialog.textMsg": "Copy, cut and paste actions using the editor toolbar buttons and context menu actions will be performed within this editor tab only.<br><br>To copy or paste to or from applications outside the editor tab use the following keyboard combinations:", "Common.Views.CopyWarningDialog.textTitle": "Hand<PERSON> for <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ut og Lim inn", "Common.Views.CopyWarningDialog.textToCopy": "for Co<PERSON>", "Common.Views.CopyWarningDialog.textToCut": "for Cut", "Common.Views.CopyWarningDialog.textToPaste": "for Paste", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Laster...", "Common.Views.DocumentAccessDialog.textTitle": "<PERSON><PERSON>-innstillinger", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "<PERSON>a", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "Select", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "Select", "Common.Views.Draw.txtSize": "Size", "Common.Views.ExternalDiagramEditor.textTitle": "Diagramredigering", "Common.Views.ExternalEditor.textClose": "Lukk", "Common.Views.ExternalEditor.textSave": "Save & Exit", "Common.Views.ExternalMergeEditor.textTitle": "Mail merge recipients", "Common.Views.ExternalOleEditor.textTitle": "Spreadsheet Editor", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Users who are editing the file:", "Common.Views.Header.textAddFavorite": "<PERSON> as favorite", "Common.Views.Header.textAdvSettings": "Avanserte innstillinger", "Common.Views.Header.textBack": "Open file location", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "Hide toolbar", "Common.Views.Header.textDocEditDesc": "Make any changes", "Common.Views.Header.textDocViewDesc": "View the file, but make no changes", "Common.Views.Header.textDocViewFormDesc": "See how the form will look like when filling out", "Common.Views.Header.textDownload": "Download", "Common.Views.Header.textEdit": "Editing", "Common.Views.Header.textHideLines": "Hide Rulers", "Common.Views.Header.textHideStatusBar": "Hide status bar", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Remove from Favorites", "Common.Views.Header.textReview": "Reviewing", "Common.Views.Header.textReviewDesc": "Suggest changes", "Common.Views.Header.textShare": "Share", "Common.Views.Header.textStartFill": "Share & collect", "Common.Views.Header.textView": "Viewing", "Common.Views.Header.textViewForm": "Viewing form", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Manage document access rights", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDocEdit": "Editing", "Common.Views.Header.tipDocView": "Viewing", "Common.Views.Header.tipDocViewForm": "Viewing form", "Common.Views.Header.tipDownload": "Download file", "Common.Views.Header.tipFillStatus": "Filling status", "Common.Views.Header.tipGoEdit": "Rediger gjeldende fil", "Common.Views.Header.tipPrint": "Print file", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "Redo", "Common.Views.Header.tipReview": "Reviewing", "Common.Views.Header.tipSave": "Lagre", "Common.Views.Header.tipSearch": "Find", "Common.Views.Header.tipUndo": "<PERSON><PERSON>", "Common.Views.Header.tipUsers": "View users", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipViewUsers": "Vis brukere og administrer tilgangsrettigheter", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON>", "Common.Views.Header.txtRename": "<PERSON><PERSON>", "Common.Views.History.textCloseHistory": "Lukk loggen", "Common.Views.History.textHideAll": "Hide detailed changes", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "Show detailed changes", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Paste an image URL:", "Common.Views.ImageFromUrlDialog.txtEmpty": "This field is required", "Common.Views.ImageFromUrlDialog.txtNotUrl": "This field should be a URL in the \"http://www.example.com\" format", "Common.Views.InsertTableDialog.textInvalidRowsCols": "You need to specify valid rows and columns count.", "Common.Views.InsertTableDialog.txtColumns": "Number of columns", "Common.Views.InsertTableDialog.txtMaxText": "The maximum value for this field is {0}.", "Common.Views.InsertTableDialog.txtMinText": "The minimum value for this field is {0}.", "Common.Views.InsertTableDialog.txtRows": "Number of rows", "Common.Views.InsertTableDialog.txtTitle": "Tabellstørrelse", "Common.Views.InsertTableDialog.txtTitleSplit": "Split cell", "Common.Views.LanguageDialog.labelSelect": "Select document language", "Common.Views.MacrosDialog.textCopy": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "Lukk filen", "Common.Views.OpenDialog.txtEncoding": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtIncorrectPwd": "Password is incorrect.", "Common.Views.OpenDialog.txtOpenFile": "Skriv inn passordet for å opne fila", "Common.Views.OpenDialog.txtPassword": "Passord", "Common.Views.OpenDialog.txtPreview": "Forhåndsvisning", "Common.Views.OpenDialog.txtProtected": "Once you enter the password and open the file, the current password to the file will be reset.", "Common.Views.OpenDialog.txtTitle": "Velg %1 alternativer", "Common.Views.OpenDialog.txtTitleProtected": "Protected file", "Common.Views.PasswordDialog.txtDescription": "Set a password to protect this document", "Common.Views.PasswordDialog.txtIncorrectPwd": "Bekreftet passord er ikke identisk", "Common.Views.PasswordDialog.txtPassword": "Passord", "Common.Views.PasswordDialog.txtRepeat": "Repeat password", "Common.Views.PasswordDialog.txtTitle": "Set password", "Common.Views.PasswordDialog.txtWarning": "Åtvaring! Dersom du mister eller gløymer passordet, kan det ikkje gjenopprettast. Oppbevare han på ein trygg stad.", "Common.Views.PluginDlg.textLoading": "Loading", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "Encrypt with password", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "<PERSON><PERSON> eller slett passord", "Common.Views.Protection.hintSignature": "Legg til digital signatur eller signaturlinje", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON> passord", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON> passord", "Common.Views.Protection.txtEncrypt": "Encrypt", "Common.Views.Protection.txtInvisibleSignature": "Legg til digital signatur", "Common.Views.Protection.txtSignature": "Signature", "Common.Views.Protection.txtSignatureLine": "Legg til signaturlinje", "Common.Views.RecentFiles.txtOpenRecent": "Open recent", "Common.Views.RenameDialog.textName": "Filnavn", "Common.Views.RenameDialog.txtInvalidName": "The file name cannot contain any of the following characters: ", "Common.Views.ReviewChanges.hintNext": "To next change", "Common.Views.ReviewChanges.hintPrev": "To previous change", "Common.Views.ReviewChanges.mniFromFile": "Document from file", "Common.Views.ReviewChanges.mniFromStorage": "Document from storage", "Common.Views.ReviewChanges.mniFromUrl": "Document from URL", "Common.Views.ReviewChanges.mniMMFromFile": "From file", "Common.Views.ReviewChanges.mniMMFromStorage": "From storage", "Common.Views.ReviewChanges.mniMMFromUrl": "From URL", "Common.Views.ReviewChanges.mniSettings": "Innstillinger for sammenlikning", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Real-time co-editing. All changes are saved automatically.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Use the 'Save' button to sync the changes you and others make.", "Common.Views.ReviewChanges.textEnable": "Enable", "Common.Views.ReviewChanges.textWarnTrackChanges": "Track Changes will be switched ON for all users with full access. The next time anyone opens the doc, Track Changes will remain enabled.", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "Enable track changes for everyone?", "Common.Views.ReviewChanges.tipAcceptCurrent": "<PERSON><PERSON> end<PERSON>", "Common.Views.ReviewChanges.tipCoAuthMode": "Set co-editing mode", "Common.Views.ReviewChanges.tipCombine": "Combine current document with another one", "Common.Views.ReviewChanges.tipCommentRem": "Delete comments", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Delete current comments", "Common.Views.ReviewChanges.tipCommentResolve": "Resolve comments", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.tipCompare": "Sammenlikn dette dokumentet med et annet", "Common.Views.ReviewChanges.tipHistory": "Show version history", "Common.Views.ReviewChanges.tipMailRecepients": "Mail merge", "Common.Views.ReviewChanges.tipRejectCurrent": "Reject current change and move to next", "Common.Views.ReviewChanges.tipReview": "Track changes", "Common.Views.ReviewChanges.tipReviewView": "Select the mode you want the changes to be displayed", "Common.Views.ReviewChanges.tipSetDocLang": "Set document language", "Common.Views.ReviewChanges.tipSetSpelling": "Spell checking", "Common.Views.ReviewChanges.tipSharing": "Manage document access rights", "Common.Views.ReviewChanges.txtAccept": "God<PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "<PERSON><PERSON> alle endringer", "Common.Views.ReviewChanges.txtAcceptChanges": "<PERSON><PERSON> endringer", "Common.Views.ReviewChanges.txtAcceptCurrent": "<PERSON><PERSON> end<PERSON>", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "Lukk", "Common.Views.ReviewChanges.txtCoAuthMode": "Co-editing Mode", "Common.Views.ReviewChanges.txtCombine": "Combine", "Common.Views.ReviewChanges.txtCommentRemAll": "Delete all comments", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Delete current comments", "Common.Views.ReviewChanges.txtCommentRemMy": "Delete my comments", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Delete my current comments", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolve all comments", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolve my comments", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolve my current comments", "Common.Views.ReviewChanges.txtCompare": "Sammenlikn", "Common.Views.ReviewChanges.txtDocLang": "Language", "Common.Views.ReviewChanges.txtEditing": "Redigering", "Common.Views.ReviewChanges.txtFinal": "Alle endringer er godtatt {0}", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Version history", "Common.Views.ReviewChanges.txtMailMerge": "Mail Merge", "Common.Views.ReviewChanges.txtMarkup": "<PERSON>e endringer {0}", "Common.Views.ReviewChanges.txtMarkupCap": "Markup and balloons", "Common.Views.ReviewChanges.txtMarkupSimple": "<PERSON>e endringer{0}<br><PERSON><PERSON> oppsprettvinduer", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "Only markup", "Common.Views.ReviewChanges.txtNext": "Neste", "Common.Views.ReviewChanges.txtOff": "OFF for me", "Common.Views.ReviewChanges.txtOffGlobal": "OFF for me and everyone", "Common.Views.ReviewChanges.txtOn": "ON for me", "Common.Views.ReviewChanges.txtOnGlobal": "ON for me and everyone", "Common.Views.ReviewChanges.txtOriginal": "<PERSON>e endringer er avvist {0}", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtPreview": "Forhåndsvisning", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Reject All Changes", "Common.Views.ReviewChanges.txtRejectChanges": "Reject changes", "Common.Views.ReviewChanges.txtRejectCurrent": "Reject current change", "Common.Views.ReviewChanges.txtSharing": "Sharing", "Common.Views.ReviewChanges.txtSpelling": "Spell checking", "Common.Views.ReviewChanges.txtTurnon": "Track Changes", "Common.Views.ReviewChanges.txtView": "Visningsmodus", "Common.Views.ReviewChangesDialog.textTitle": "Review changes", "Common.Views.ReviewChangesDialog.txtAccept": "God<PERSON>", "Common.Views.ReviewChangesDialog.txtAcceptAll": "<PERSON><PERSON> alle endringer", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "<PERSON><PERSON> end<PERSON>", "Common.Views.ReviewChangesDialog.txtNext": "To next change", "Common.Views.ReviewChangesDialog.txtPrev": "To previous change", "Common.Views.ReviewChangesDialog.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtRejectAll": "Reject all changes", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "Reject current change", "Common.Views.ReviewPopover.textAdd": "Legg til", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON> til svar", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "Lukk", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textFollowMove": "Follow move", "Common.Views.ReviewPopover.textMention": "+mention vil gi tilgang til dokumentet og varsle via epost", "Common.Views.ReviewPopover.textMentionNotify": "+mention vil varsle personen via epost", "Common.Views.ReviewPopover.textOpenAgain": "Open again", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "You have no permission to reopen the comment", "Common.Views.ReviewPopover.txtAccept": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtDeleteTip": "<PERSON><PERSON>", "Common.Views.ReviewPopover.txtEditTip": "Edit", "Common.Views.ReviewPopover.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Loading", "Common.Views.SaveAsDlg.textTitle": "Folder for save", "Common.Views.SearchPanel.textCaseSensitive": "Case sensitive", "Common.Views.SearchPanel.textCloseSearch": "Close find", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "<PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "Finn og erstatt", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} obje<PERSON><PERSON> ble erstattet.", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} objekter ble erstattet. {2} objekter gjenstår fordi de er låst av andre brukere.", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "Erstatt alle", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearchAgain": "{0}<PERSON><PERSON><PERSON><PERSON><PERSON> nytt søk{1} for et mer nøyaktig resultat.", "Common.Views.SearchPanel.textSearchHasStopped": "Search has stopped", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textTooManyResults": "There are too many results to show here", "Common.Views.SearchPanel.textWholeWords": "Whole words only", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "Previous result", "Common.Views.SelectFileDlg.textLoading": "Loading", "Common.Views.SelectFileDlg.textTitle": "Select data source", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Fet", "Common.Views.SignDialog.textCertificate": "Ser<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "<PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "Input signer name", "Common.Views.SignDialog.textItalic": "Italic", "Common.Views.SignDialog.textNameError": "Signer name must not be empty.", "Common.Views.SignDialog.textPurpose": "Purpose for signing this document", "Common.Views.SignDialog.textSelect": "Velg", "Common.Views.SignDialog.textSelectImage": "Select image", "Common.Views.SignDialog.textSignature": "Signature looks as", "Common.Views.SignDialog.textTitle": "Sign document", "Common.Views.SignDialog.textUseImage": "or click 'Select Image' to use a picture as signature", "Common.Views.SignDialog.textValid": "Valid from %1 to %2", "Common.Views.SignDialog.tipFontName": "Font name", "Common.Views.SignDialog.tipFontSize": "Font size", "Common.Views.SignSettingsDialog.textAllowComment": "Tillat at den som signerer legger til en kommentar i signaturdialogen", "Common.Views.SignSettingsDialog.textDefInstruction": "Se over og verifiser at innholdet er korrekt før du signerer dokumentet.", "Common.Views.SignSettingsDialog.textInfoEmail": "Suggested signer's e-mail", "Common.Views.SignSettingsDialog.textInfoName": "Suggested signer", "Common.Views.SignSettingsDialog.textInfoTitle": "Suggested signer's title", "Common.Views.SignSettingsDialog.textInstructions": "Instructions for signer", "Common.Views.SignSettingsDialog.textShowDate": "Show sign date in signature line", "Common.Views.SignSettingsDialog.textTitle": "Signature setup", "Common.Views.SignSettingsDialog.txtEmpty": "This field is required", "Common.Views.SymbolTableDialog.textCharacter": "Bokstav", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX value", "Common.Views.SymbolTableDialog.textCopyright": "Copyright sign", "Common.Views.SymbolTableDialog.textDCQuote": "Closing double quote", "Common.Views.SymbolTableDialog.textDOQuote": "Opening double quote", "Common.Views.SymbolTableDialog.textEllipsis": "Horizontal ellipsis", "Common.Views.SymbolTableDialog.textEmDash": "Em dash", "Common.Views.SymbolTableDialog.textEmSpace": "Em space", "Common.Views.SymbolTableDialog.textEnDash": "En dash", "Common.Views.SymbolTableDialog.textEnSpace": "En space", "Common.Views.SymbolTableDialog.textFont": "Font", "Common.Views.SymbolTableDialog.textNBHyphen": "Non-breaking hyphen", "Common.Views.SymbolTableDialog.textNBSpace": "No-break space", "Common.Views.SymbolTableDialog.textPilcrow": "Pilcrow sign", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 mellomrom", "Common.Views.SymbolTableDialog.textRange": "Range", "Common.Views.SymbolTableDialog.textRecent": "Recently used symbols", "Common.Views.SymbolTableDialog.textRegistered": "Registered sign", "Common.Views.SymbolTableDialog.textSCQuote": "Closing single quote", "Common.Views.SymbolTableDialog.textSection": "Section sign", "Common.Views.SymbolTableDialog.textShortcut": "Shortcut key", "Common.Views.SymbolTableDialog.textSHyphen": "Soft hyphen", "Common.Views.SymbolTableDialog.textSOQuote": "Opening single quote", "Common.Views.SymbolTableDialog.textSpecial": "Special characters", "Common.Views.SymbolTableDialog.textSymbols": "Symbols", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Trademark symbol ", "Common.Views.UserNameDialog.textDontShow": "Don't ask me again", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label must not be empty.", "DE.Controllers.DocProtection.txtIsProtectedComment": "Document is protected. You may only insert comments to this document.", "DE.Controllers.DocProtection.txtIsProtectedForms": "Document is protected. You may only fill in forms in this document.", "DE.Controllers.DocProtection.txtIsProtectedTrack": "Document is protected. You may edit this document, but all changes will be tracked.", "DE.Controllers.DocProtection.txtIsProtectedView": "Document is protected. You may only view this document.", "DE.Controllers.DocProtection.txtWasProtectedComment": "Document has been protected by another user.\nYou may only insert comments to this document.", "DE.Controllers.DocProtection.txtWasProtectedForms": "Document has been protected by another user.\nYou may only fill in forms in this document.", "DE.Controllers.DocProtection.txtWasProtectedTrack": "Document has been protected by another user.\nYou may edit this document, but all changes will be tracked.", "DE.Controllers.DocProtection.txtWasProtectedView": "Document has been protected by another user.\nYou may only view this document.", "DE.Controllers.DocProtection.txtWasUnprotected": "Document has been unprotected.", "DE.Controllers.LeftMenu.leavePageText": "Alle ulagrede endringer i dokumentet vil ikke bli lagret.<br><PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" og så \"Lagre\" for å lagre endringene. Klikk \"OK\" for å forkaste alle ulagrede endringer.", "DE.Controllers.LeftMenu.newDocumentTitle": "Unnamed document", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Controllers.LeftMenu.requestEditRightsText": "Requesting editing rights...", "DE.Controllers.LeftMenu.textLoadHistory": "Loading version history...", "DE.Controllers.LeftMenu.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "DE.Controllers.LeftMenu.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "DE.Controllers.LeftMenu.textReplaceSuccess": "The search has been done. Occurrences replaced: {0}", "DE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "DE.Controllers.LeftMenu.txtCompatible": "The document will be saved to the new format. It will allow to use all the editor features, but might affect the document layout.<br>Use the 'Compatibility' option of the advanced settings if you want to make the files compatible with older MS Word versions.", "DE.Controllers.LeftMenu.txtUntitled": "Untitled", "DE.Controllers.LeftMenu.warnDownloadAs": "If you continue saving in this format all features except the text will be lost.<br>Are you sure you want to continue?", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "Your {0} will be converted to an editable format. This may take a while. The resulting document will be optimized to allow you to edit the text, so it might not look exactly like the original {0}, especially if the original file contained lots of graphics.", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "If you continue saving in this format some of the formatting might be lost.<br>Are you sure you want to continue?", "DE.Controllers.LeftMenu.warnReplaceString": "{0} er ikke et gyldig spesialtegn for erstatningsfeltet.", "DE.Controllers.Main.applyChangesTextText": "Loading the changes...", "DE.Controllers.Main.applyChangesTitleText": "Loading the Changes", "DE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "DE.Controllers.Main.convertationTimeoutText": "Conversion timeout exceeded.", "DE.Controllers.Main.criticalErrorExtText": "Press \"OK\" to return to document list.", "DE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON>", "DE.Controllers.Main.downloadErrorText": "Nedlasting feilet.", "DE.Controllers.Main.downloadMergeText": "Laster ned...", "DE.Controllers.Main.downloadMergeTitle": "Downloading", "DE.Controllers.Main.downloadTextText": "Laster ned dokument...", "DE.Controllers.Main.downloadTitleText": "Laster ned dokument", "DE.Controllers.Main.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "DE.Controllers.Main.errorBadImageUrl": "Image URL is incorrect", "DE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the document.", "DE.Controllers.Main.errorCoAuthoringDisconnect": "Server connection lost. The document cannot be edited right now.", "DE.Controllers.Main.errorComboSeries": "To create a combination chart, select at least two series of data.", "DE.Controllers.Main.errorCompare": "The Compare Documents feature is not available while co-editing. ", "DE.Controllers.Main.errorConnectToServer": "The document could not be saved. Please check connection settings or contact your administrator.<br>When you click the 'OK' button, you will be prompted to download the document.", "DE.Controllers.Main.errorDatabaseConnection": "<PERSON>kstern feil.<br><PERSON><PERSON> i forbindel<PERSON> til databasen. Vennligst kontakt brukerstøtte hvis feilen vedvarer. ", "DE.Controllers.Main.errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "DE.Controllers.Main.errorDataRange": "Incorrect data range.", "DE.Controllers.Main.errorDefaultMessage": "Feilkode: %1", "DE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "DE.Controllers.Main.errorEditingDownloadas": "Det oppstod en feil ved arbeid med dokumentet. <br>Bruk 'Last ned som' opsjonen til å lagre en kopi av dokumentet til din lokale datamaskin.", "DE.Controllers.Main.errorEditingSaveas": "Det oppstod en feil ved arbeid med dokumentet.<br>Bruk opsjonen 'Lagre som...' til å lagre en kopi av dokumentet til din lokale datamaskin.", "DE.Controllers.Main.errorEditProtectedRange": "You are not allowed to edit this selection because it is protected.", "DE.Controllers.Main.errorEmailClient": "No email client could be found.", "DE.Controllers.Main.errorEmptyTOC": "Start creating a table of contents by applying a heading style from the Styles gallery to the selected text.", "DE.Controllers.Main.errorFilePassProtect": "Filen er passordbeskyttet og kan ikke åpnes.", "DE.Controllers.Main.errorFileSizeExceed": "The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.", "DE.Controllers.Main.errorForceSave": "Det skjedde en feil ved lagring av filen. Bruk opsjonen 'Lagre som...' til å lagre filen til din lokale datamaskin eller prøv igjen senere.", "DE.Controllers.Main.errorInconsistentExt": "En feil oppstod under åpningen av filen.<br>Filens innhold stemmer ikke overens med filens etternavn.", "DE.Controllers.Main.errorInconsistentExtDocx": "En feil oppstod under åpningen av filen.<br>Innholdet tilsier at dette er et tekstdokument (f.eks.: docx), men filens etternavn er \"%1\".", "DE.Controllers.Main.errorInconsistentExtPdf": "En feil oppstod under åpningen av filen.<br>Innholdet tilsier at dette er en pdf/djvu/xps/oxps, men filens etternavn er \"%1\".", "DE.Controllers.Main.errorInconsistentExtPptx": "En feil oppstod under åpningen av filen.<br>Inn<PERSON>et tilsier at dette er en presentasjon (f.eks.: pptx), men filens etternavn er \"%1\".", "DE.Controllers.Main.errorInconsistentExtXlsx": "En feil oppstod under åpningen av filen.<br>Innholdet tilsier at dette er et regneark (f.eks.: xlsx), men filens etternavn er \"%1\".", "DE.Controllers.Main.errorKeyEncrypt": "Unknown key descriptor", "DE.Controllers.Main.errorKeyExpire": "Key descriptor expired", "DE.Controllers.Main.errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "DE.Controllers.Main.errorMailMergeLoadFile": "Loading the document failed. Please select a different file.", "DE.Controllers.Main.errorMailMergeSaveFile": "Me<PERSON> failed.", "DE.Controllers.Main.errorNoTOC": "There's no table of contents to update. You can insert one from the References tab.", "DE.Controllers.Main.errorPasswordIsNotCorrect": "The password you supplied is not correct.<br>Verify that the CAPS LOCK key is off and be sure to use the correct capitalization.", "DE.Controllers.Main.errorProcessSaveResult": "Saving failed.", "DE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "DE.Controllers.Main.errorServerVersion": "The editor version has been updated. The page will be reloaded to apply the changes.", "DE.Controllers.Main.errorSessionAbsolute": "The document editing session has expired. Please reload the page.", "DE.Controllers.Main.errorSessionIdle": "The document has not been edited for quite a long time. Please reload the page.", "DE.Controllers.Main.errorSessionToken": "The connection to the server has been interrupted. Please reload the page.", "DE.Controllers.Main.errorSetPassword": "Password could not be set.", "DE.Controllers.Main.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "DE.Controllers.Main.errorSubmit": "Submit failed.", "DE.Controllers.Main.errorTextFormWrongFormat": "The value entered does not match the format of the field.", "DE.Controllers.Main.errorToken": "The document security token is not correctly formed.<br>Please contact your Document Server administrator.", "DE.Controllers.Main.errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator.", "DE.Controllers.Main.errorUpdateVersion": "The file version has been changed. The page will be reloaded.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "DE.Controllers.Main.errorUserDrop": "The file cannot be accessed right now.", "DE.Controllers.Main.errorUsersExceed": "The number of users allowed by the pricing plan was exceeded", "DE.Controllers.Main.errorViewerDisconnect": "Mistet nettverksforbindelse. Du kan fremdeles se dokumentet,<br>men vil ikke kunne laste ned eller skrive det ut før nettverksforbindelsen er gjenopprettet og siden oppdatert.", "DE.Controllers.Main.leavePageText": "You have unsaved changes in this document. Click \"Stay on This Page\", then \"Save\" to save them. Click \"Leave This Page\" to discard all the unsaved changes.", "DE.Controllers.Main.leavePageTextOnClose": "Alle endringer som ikke er lagret vil gå tapt.<br> <PERSON><PERSON><PERSON> <PERSON>å \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" og \"Lagre\" for å lagre endringene. Trykk på \"OK\" for å forkaste alle endringer.", "DE.Controllers.Main.loadFontsTextText": "Laster data...", "DE.Controllers.Main.loadFontsTitleText": "Laster data", "DE.Controllers.Main.loadFontTextText": "Laster data...", "DE.Controllers.Main.loadFontTitleText": "Laster data", "DE.Controllers.Main.loadImagesTextText": "Laster bilder...", "DE.Controllers.Main.loadImagesTitleText": "Laster bi<PERSON>", "DE.Controllers.Main.loadImageTextText": "Laster bilde...", "DE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON> bilde", "DE.Controllers.Main.loadingDocumentTextText": "Laster dokument...", "DE.Controllers.Main.loadingDocumentTitleText": "Laster dokument", "DE.Controllers.Main.mailMergeLoadFileText": "Laster datakilde...", "DE.Controllers.Main.mailMergeLoadFileTitle": "Laster <PERSON>", "DE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.openErrorText": "En feil oppstod når under å<PERSON>ning av filen.", "DE.Controllers.Main.openTextText": "<PERSON><PERSON><PERSON> dokument...", "DE.Controllers.Main.openTitleText": "Å<PERSON>ner dokument", "DE.Controllers.Main.printTextText": "Skriver ut dokument...", "DE.Controllers.Main.printTitleText": "Skriver ut dokument", "DE.Controllers.Main.reloadButtonText": "Oppdater side", "DE.Controllers.Main.requestEditFailedMessageText": "Someone is editing this document right now. Please try again later.", "DE.Controllers.Main.requestEditFailedTitleText": "Tilgang nektet", "DE.Controllers.Main.saveErrorText": "En feil oppstod mens filen ble lagret.", "DE.Controllers.Main.saveErrorTextDesktop": "This file cannot be saved or created.<br>Possible reasons are: <br>1. The file is read-only. <br>2. The file is being edited by other users. <br>3. The disk is full or corrupted.", "DE.Controllers.Main.saveTextText": "Lagrer dokument...", "DE.Controllers.Main.saveTitleText": "Lagrer dokument", "DE.Controllers.Main.savingText": "Saving", "DE.Controllers.Main.scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please reload the page.", "DE.Controllers.Main.sendMergeText": "Sending merge...", "DE.Controllers.Main.sendMergeTitle": "Sending merge", "DE.Controllers.Main.splitDividerErrorText": "The number of rows must be a divisor of %1.", "DE.Controllers.Main.splitMaxColsErrorText": "The number of columns must be less than %1.", "DE.Controllers.Main.splitMaxRowsErrorText": "The number of rows must be less than %1.", "DE.Controllers.Main.textAnonymous": "Anonym", "DE.Controllers.Main.textAnyone": "H<PERSON>msomhel<PERSON>", "DE.Controllers.Main.textApplyAll": "Bruk på alle likninger", "DE.Controllers.Main.textBuyNow": "Besøk webside", "DE.Controllers.Main.textChangesSaved": "Alle endringer er lagret", "DE.Controllers.Main.textClose": "Lukk", "DE.Controllers.Main.textCloseTip": "Klikk for å lukke tipset", "DE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "DE.Controllers.Main.textContactUs": "Kontakt salgsavdelingen", "DE.Controllers.Main.textContinue": "Continue", "DE.Controllers.Main.textConvertEquation": "This equation was created with an old version of the equation editor which is no longer supported. To edit it, convert the equation to the Office Math ML format.<br>Convert now?", "DE.Controllers.Main.textCustomLoader": "Please note that according to the terms of the license you are not entitled to change the loader.<br>Please contact our Sales Department to get a quote.", "DE.Controllers.Main.textDisconnect": "Connection is lost", "DE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "DE.Controllers.Main.textLearnMore": "Learn More", "DE.Controllers.Main.textLoadingDocument": "Laster dokument", "DE.Controllers.Main.textLongName": "Enter a name that is less than 128 characters.", "DE.Controllers.Main.textNoLicenseTitle": "License limit reached", "DE.Controllers.Main.textPaidFeature": "<PERSON><PERSON> funk<PERSON>", "DE.Controllers.Main.textReconnect": "Connection is restored", "DE.Controllers.Main.textRemember": "Husk valget mitt", "DE.Controllers.Main.textRememberMacros": "Remember my choice for all macros", "DE.Controllers.Main.textRenameError": "User name must not be empty.", "DE.Controllers.Main.textRenameLabel": "Enter a name to be used for collaboration", "DE.Controllers.Main.textRequestMacros": "En marko forsøker å nå en URL. Vil du tillate tilkobling til %1?", "DE.Controllers.Main.textShape": "<PERSON><PERSON>r", "DE.Controllers.Main.textSignature": "Signature", "DE.Controllers.Main.textStrict": "Strict mode", "DE.Controllers.Main.textText": "Tekst", "DE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "DE.Controllers.Main.textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.<br>Click the 'Strict mode' button to switch to the Strict co-editing mode to edit the file without other users interference and send your changes only after you save them. You can switch between the co-editing modes using the editor Advanced settings.", "DE.Controllers.Main.textTryUndoRedoWarn": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "DE.Controllers.Main.textUndo": "<PERSON><PERSON>", "DE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "DE.Controllers.Main.textUpdating": "Updating", "DE.Controllers.Main.titleLicenseExp": "License expired", "DE.Controllers.Main.titleLicenseNotActive": "License not active", "DE.Controllers.Main.titleServerVersion": "Editor updated", "DE.Controllers.Main.titleUpdateVersion": "<PERSON><PERSON> ve<PERSON>", "DE.Controllers.Main.txtAbove": "over", "DE.Controllers.Main.txtArt": "<PERSON><PERSON><PERSON> din her", "DE.Controllers.Main.txtBasicShapes": "Basisformer", "DE.Controllers.Main.txtBelow": "under", "DE.Controllers.Main.txtBookmarkError": "Feil! Bokmerke er ikke definert", "DE.Controllers.Main.txtButtons": "<PERSON>nap<PERSON>", "DE.Controllers.Main.txtCallouts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtCharts": "Diagram", "DE.Controllers.Main.txtChoose": "Velg et punkt", "DE.Controllers.Main.txtClickToLoad": "Click to load image", "DE.Controllers.Main.txtCurrentDocument": "Gjeldende dokument", "DE.Controllers.Main.txtDiagramTitle": "Diagramtittel", "DE.Controllers.Main.txtEditingMode": "Set editing mode...", "DE.Controllers.Main.txtEndOfFormula": "Unexpected end of formula", "DE.Controllers.Main.txtEnterDate": "Enter a date", "DE.Controllers.Main.txtErrorLoadHistory": "History loading failed", "DE.Controllers.Main.txtEvenPage": "Partallside", "DE.Controllers.Main.txtFiguredArrows": "Pilfigurer", "DE.Controllers.Main.txtFirstPage": "First page", "DE.Controllers.Main.txtFooter": "Footer", "DE.Controllers.Main.txtFormulaNotInTable": "The Formula Not In Table", "DE.Controllers.Main.txtHeader": "Topptekst", "DE.Controllers.Main.txtHyperlink": "<PERSON><PERSON>", "DE.Controllers.Main.txtIndTooLarge": "Index too large", "DE.Controllers.Main.txtLines": "Lines", "DE.Controllers.Main.txtMainDocOnly": "Error! Main document only.", "DE.Controllers.Main.txtMath": "Math", "DE.Controllers.Main.txtMissArg": "Missing argument", "DE.Controllers.Main.txtMissOperator": "Missing operator", "DE.Controllers.Main.txtNeedSynchronize": "<PERSON> har opp<PERSON><PERSON>er", "DE.Controllers.Main.txtNone": "None", "DE.Controllers.Main.txtNoTableOfContents": "There are no headings in the document. Apply a heading style to the text so that it appears in the table of contents.", "DE.Controllers.Main.txtNoTableOfFigures": "No table of figures entries found.", "DE.Controllers.Main.txtNoText": "Error! No text of specified style in document.", "DE.Controllers.Main.txtNotInTable": "Is not in table", "DE.Controllers.Main.txtNotValidBookmark": "Error! Not a valid bookmark self-reference.", "DE.Controllers.Main.txtOddPage": "Oddetallsside", "DE.Controllers.Main.txtOnPage": "on page", "DE.Controllers.Main.txtRectangles": "Rectangles", "DE.Controllers.Main.txtSameAsPrev": "Same as previous", "DE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "DE.Controllers.Main.txtScheme_Aspect": "Aspekt", "DE.Controllers.Main.txtScheme_Blue": "Blue", "DE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "DE.Controllers.Main.txtScheme_Blue_II": "Blue II", "DE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "DE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "DE.Controllers.Main.txtScheme_Green": "Green", "DE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "DE.Controllers.Main.txtScheme_Marquee": "Marquee", "DE.Controllers.Main.txtScheme_Median": "Median", "DE.Controllers.Main.txtScheme_Office": "Office", "DE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "DE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "DE.Controllers.Main.txtScheme_Orange": "Orange", "DE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "DE.Controllers.Main.txtScheme_Paper": "Paper", "DE.Controllers.Main.txtScheme_Red": "Red", "DE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "DE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "DE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "DE.Controllers.Main.txtScheme_Violet": "Violet", "DE.Controllers.Main.txtScheme_Violet_II": "Violet II", "DE.Controllers.Main.txtScheme_Yellow": "Yellow", "DE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "DE.Controllers.Main.txtSection": "-Seks<PERSON>", "DE.Controllers.Main.txtSeries": "Series", "DE.Controllers.Main.txtShape_accentBorderCallout1": "Line callout 1 (Border and accent bar)", "DE.Controllers.Main.txtShape_accentBorderCallout2": "Line callout 2 (Border and accent bar)", "DE.Controllers.Main.txtShape_accentBorderCallout3": "Line callout 3 (Border and accent bar)", "DE.Controllers.Main.txtShape_accentCallout1": "Line callout 1 (Accent bar)", "DE.Controllers.Main.txtShape_accentCallout2": "Line callout 2 (Accent bar)", "DE.Controllers.Main.txtShape_accentCallout3": "Line callout 3 (Accent bar)", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "<PERSON>il<PERSON><PERSON>- el<PERSON> for<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_actionButtonBeginning": "Startknapp", "DE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON> knapp", "DE.Controllers.Main.txtShape_actionButtonDocument": "Document button", "DE.Controllers.Main.txtShape_actionButtonEnd": "End button", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "Forward or next button", "DE.Controllers.Main.txtShape_actionButtonHelp": "Help button", "DE.Controllers.Main.txtShape_actionButtonHome": "Home button", "DE.Controllers.Main.txtShape_actionButtonInformation": "Information button", "DE.Controllers.Main.txtShape_actionButtonMovie": "Movie button", "DE.Controllers.Main.txtShape_actionButtonReturn": "Return button", "DE.Controllers.Main.txtShape_actionButtonSound": "Sound button", "DE.Controllers.Main.txtShape_arc": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON> pil", "DE.Controllers.Main.txtShape_bentConnector5": "Elbow connector", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "Elbow arrow connector", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Elbow double-arrow connector", "DE.Controllers.Main.txtShape_bentUpArrow": "Oppadbuet pil", "DE.Controllers.Main.txtShape_bevel": "Skrå", "DE.Controllers.Main.txtShape_blockArc": "Blokkbue", "DE.Controllers.Main.txtShape_borderCallout1": "Line callout 1", "DE.Controllers.Main.txtShape_borderCallout2": "Line callout 2", "DE.Controllers.Main.txtShape_borderCallout3": "Line callout 3", "DE.Controllers.Main.txtShape_bracePair": "Double brace", "DE.Controllers.Main.txtShape_callout1": "Line callout 1 (No border)", "DE.Controllers.Main.txtShape_callout2": "Line callout 2 (No border)", "DE.Controllers.Main.txtShape_callout3": "Line Callout 3 (No border)", "DE.Controllers.Main.txtShape_can": "<PERSON>n", "DE.Controllers.Main.txtShape_chevron": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_chord": "Chord", "DE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> pil", "DE.Controllers.Main.txtShape_cloud": "Sky", "DE.Controllers.Main.txtShape_cloudCallout": "Cloud callout", "DE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_cube": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3": "Curved connector", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Curved arrow connector", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Curved double-arrow connector", "DE.Controllers.Main.txtShape_curvedDownArrow": "Curved down arrow", "DE.Controllers.Main.txtShape_curvedLeftArrow": "Curved left arrow", "DE.Controllers.Main.txtShape_curvedRightArrow": "Curved right arrow", "DE.Controllers.Main.txtShape_curvedUpArrow": "Curved up arrow", "DE.Controllers.Main.txtShape_decagon": "Decagon", "DE.Controllers.Main.txtShape_diagStripe": "Diagonal linje", "DE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_dodecagon": "Dodecagon", "DE.Controllers.Main.txtShape_donut": "Donut", "DE.Controllers.Main.txtShape_doubleWave": "Double wave", "DE.Controllers.Main.txtShape_downArrow": "Down arrow", "DE.Controllers.Main.txtShape_downArrowCallout": "Down arrow callout", "DE.Controllers.Main.txtShape_ellipse": "Ellipse", "DE.Controllers.Main.txtShape_ellipseRibbon": "Curved down ribbon", "DE.Controllers.Main.txtShape_ellipseRibbon2": "Curved up ribbon", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "Flowchart: Alternate process", "DE.Controllers.Main.txtShape_flowChartCollate": "Flowchart: Collate", "DE.Controllers.Main.txtShape_flowChartConnector": "Flowchart: Connector", "DE.Controllers.Main.txtShape_flowChartDecision": "Flowchart: Decision", "DE.Controllers.Main.txtShape_flowChartDelay": "Flowchart: Delay", "DE.Controllers.Main.txtShape_flowChartDisplay": "Flowchart: Display", "DE.Controllers.Main.txtShape_flowChartDocument": "Flowchart: Document", "DE.Controllers.Main.txtShape_flowChartExtract": "Flowchart: Extract", "DE.Controllers.Main.txtShape_flowChartInputOutput": "Flowchart: Data", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "Flowchart: Internal storage", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flowchart: Magnetic disk", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flowchart: Direct access storage", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "Flowchart: Sequential access storage", "DE.Controllers.Main.txtShape_flowChartManualInput": "Flowchart: Manual input", "DE.Controllers.Main.txtShape_flowChartManualOperation": "Flowchart: Manual operation", "DE.Controllers.Main.txtShape_flowChartMerge": "Flowchart: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartMultidocument": "Flowchart: Multidocument ", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flowchart: Off-page connector", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flowchart: Stored data", "DE.Controllers.Main.txtShape_flowChartOr": "Flowchart: Or", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flowchart: Predefined process", "DE.Controllers.Main.txtShape_flowChartPreparation": "Flowchart: Preparation", "DE.Controllers.Main.txtShape_flowChartProcess": "Flowchart: Process", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "Flowchart: Card", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "Flowchart: Punched tape", "DE.Controllers.Main.txtShape_flowChartSort": "Flowchart: Sort", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "Flowchart: Summing junction", "DE.Controllers.Main.txtShape_flowChartTerminator": "Flowchart: Terminator", "DE.Controllers.Main.txtShape_foldedCorner": "Folded corner", "DE.Controllers.Main.txtShape_frame": "<PERSON>ame", "DE.Controllers.Main.txtShape_halfFrame": "Half frame", "DE.Controllers.Main.txtShape_heart": "Heart", "DE.Controllers.Main.txtShape_heptagon": "Heptagon", "DE.Controllers.Main.txtShape_hexagon": "Hexagon", "DE.Controllers.Main.txtShape_homePlate": "Pentagon", "DE.Controllers.Main.txtShape_horizontalScroll": "Horizontal scroll", "DE.Controllers.Main.txtShape_irregularSeal1": "Explosion 1", "DE.Controllers.Main.txtShape_irregularSeal2": "Explosion 2", "DE.Controllers.Main.txtShape_leftArrow": "Left arrow", "DE.Controllers.Main.txtShape_leftArrowCallout": "Left arrow callout", "DE.Controllers.Main.txtShape_leftBrace": "Left brace", "DE.Controllers.Main.txtShape_leftBracket": "Left bracket", "DE.Controllers.Main.txtShape_leftRightArrow": "Left right arrow", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "Left right arrow callout", "DE.Controllers.Main.txtShape_leftRightUpArrow": "Left right up arrow", "DE.Controllers.Main.txtShape_leftUpArrow": "Left up arrow", "DE.Controllers.Main.txtShape_lightningBolt": "Lightning bolt", "DE.Controllers.Main.txtShape_line": "Line", "DE.Controllers.Main.txtShape_lineWithArrow": "<PERSON>l", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "Double arrow", "DE.Controllers.Main.txtShape_mathDivide": "Division", "DE.Controllers.Main.txtShape_mathEqual": "Equal", "DE.Controllers.Main.txtShape_mathMinus": "Minus", "DE.Controllers.Main.txtShape_mathMultiply": "Multiply", "DE.Controllers.Main.txtShape_mathNotEqual": "Not equal", "DE.Controllers.Main.txtShape_mathPlus": "Plus", "DE.Controllers.Main.txtShape_moon": "Moon", "DE.Controllers.Main.txtShape_noSmoking": "\"Forbudt\"-symbol", "DE.Controllers.Main.txtShape_notchedRightArrow": "Notched right arrow", "DE.Controllers.Main.txtShape_octagon": "Octagon", "DE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "DE.Controllers.Main.txtShape_pentagon": "Pentagon", "DE.Controllers.Main.txtShape_pie": "Sirkeldiagram", "DE.Controllers.Main.txtShape_plaque": "Sign", "DE.Controllers.Main.txtShape_plus": "Plus", "DE.Controllers.Main.txtShape_polyline1": "Scribble", "DE.Controllers.Main.txtShape_polyline2": "Freeform", "DE.Controllers.Main.txtShape_quadArrow": "Quad arrow", "DE.Controllers.Main.txtShape_quadArrowCallout": "Quad arrow callout", "DE.Controllers.Main.txtShape_rect": "Rectangle", "DE.Controllers.Main.txtShape_ribbon": "Down ribbon", "DE.Controllers.Main.txtShape_ribbon2": "Up ribbon", "DE.Controllers.Main.txtShape_rightArrow": "Right Arrow", "DE.Controllers.Main.txtShape_rightArrowCallout": "Right arrow callout", "DE.Controllers.Main.txtShape_rightBrace": "Right brace", "DE.Controllers.Main.txtShape_rightBracket": "Right bracket", "DE.Controllers.Main.txtShape_round1Rect": "Round single corner rectangle", "DE.Controllers.Main.txtShape_round2DiagRect": "Round diagonal corner rectangle", "DE.Controllers.Main.txtShape_round2SameRect": "Round same side corner rectangle", "DE.Controllers.Main.txtShape_roundRect": "Round corner rectangle", "DE.Controllers.Main.txtShape_rtTriangle": "Right triangle", "DE.Controllers.Main.txtShape_smileyFace": "Smiley face", "DE.Controllers.Main.txtShape_snip1Rect": "Snip single corner rectangle", "DE.Controllers.Main.txtShape_snip2DiagRect": "Snip diagonal corner rectangle", "DE.Controllers.Main.txtShape_snip2SameRect": "Snip same side corner rectangle", "DE.Controllers.Main.txtShape_snipRoundRect": "Snip and round single corner rectangle", "DE.Controllers.Main.txtShape_spline": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_star10": "10-<PERSON><PERSON> stjerne", "DE.Controllers.Main.txtShape_star12": "12-<PERSON><PERSON> stjerne", "DE.Controllers.Main.txtShape_star16": "16-<PERSON><PERSON> stjerne", "DE.Controllers.Main.txtShape_star24": "24-<PERSON><PERSON> stjerne", "DE.Controllers.Main.txtShape_star32": "32-<PERSON><PERSON> stjerne", "DE.Controllers.Main.txtShape_star4": "4-<PERSON><PERSON> stjerne", "DE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON> stjerne", "DE.Controllers.Main.txtShape_star6": "6-<PERSON><PERSON> stjerne", "DE.Controllers.Main.txtShape_star7": "7-<PERSON><PERSON> stjerne", "DE.Controllers.Main.txtShape_star8": "8-<PERSON><PERSON> stjerne", "DE.Controllers.Main.txtShape_stripedRightArrow": "Striped right arrow", "DE.Controllers.Main.txtShape_sun": "Sun", "DE.Controllers.Main.txtShape_teardrop": "Teardrop", "DE.Controllers.Main.txtShape_textRect": "Text box", "DE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "DE.Controllers.Main.txtShape_triangle": "Triangle", "DE.Controllers.Main.txtShape_upArrow": "Up arrow", "DE.Controllers.Main.txtShape_upArrowCallout": "Up arrow callout", "DE.Controllers.Main.txtShape_upDownArrow": "Up down arrow", "DE.Controllers.Main.txtShape_uturnArrow": "U-Turn arrow", "DE.Controllers.Main.txtShape_verticalScroll": "Vertical scroll", "DE.Controllers.Main.txtShape_wave": "Wave", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval СЃallout", "DE.Controllers.Main.txtShape_wedgeRectCallout": "Rectangular callout", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Rounded rectangular callout", "DE.Controllers.Main.txtStarsRibbons": "Stars & ribbons", "DE.Controllers.Main.txtStyle_Book_Title": "Book title", "DE.Controllers.Main.txtStyle_Caption": "Bildetekst", "DE.Controllers.Main.txtStyle_Default_Paragraph_Font": "Default paragraph font", "DE.Controllers.Main.txtStyle_Emphasis": "Emphasis", "DE.Controllers.Main.txtStyle_endnote_reference": "Endnote reference", "DE.Controllers.Main.txtStyle_endnote_text": "Endnote text", "DE.Controllers.Main.txtStyle_footnote_reference": "Footnote reference", "DE.Controllers.Main.txtStyle_footnote_text": "Fotnotetekst", "DE.Controllers.Main.txtStyle_Heading_1": "Heading 1", "DE.Controllers.Main.txtStyle_Heading_2": "Heading 2", "DE.Controllers.Main.txtStyle_Heading_3": "Heading 3", "DE.Controllers.Main.txtStyle_Heading_4": "Heading 4", "DE.Controllers.Main.txtStyle_Heading_5": "Heading 5", "DE.Controllers.Main.txtStyle_Heading_6": "Heading 6", "DE.Controllers.Main.txtStyle_Heading_7": "Heading 7", "DE.Controllers.Main.txtStyle_Heading_8": "Heading 8", "DE.Controllers.Main.txtStyle_Heading_9": "Heading 9", "DE.Controllers.Main.txtStyle_Intense_Emphasis": "Intense emphasis", "DE.Controllers.Main.txtStyle_Intense_Quote": "Intense quote", "DE.Controllers.Main.txtStyle_Intense_Reference": "Intense reference", "DE.Controllers.Main.txtStyle_List_Paragraph": "List paragraph", "DE.Controllers.Main.txtStyle_No_List": "No list", "DE.Controllers.Main.txtStyle_No_Spacing": "Ingen avstand", "DE.Controllers.Main.txtStyle_Normal": "Normal", "DE.Controllers.Main.txtStyle_Quote": "Quote", "DE.Controllers.Main.txtStyle_Strong": "Strong", "DE.Controllers.Main.txtStyle_Subtitle": "Undertittel", "DE.Controllers.Main.txtStyle_Subtle_Emphasis": "Subtle emphasis", "DE.Controllers.Main.txtStyle_Subtle_Reference": "Subtle reference", "DE.Controllers.Main.txtStyle_Title": "Title", "DE.Controllers.Main.txtSyntaxError": "Syntax error", "DE.Controllers.Main.txtTableInd": "Table index cannot be zero", "DE.Controllers.Main.txtTableOfContents": "Innholdsfortegnelse", "DE.Controllers.Main.txtTableOfFigures": "Table of figures", "DE.Controllers.Main.txtTOCHeading": "TOC Heading", "DE.Controllers.Main.txtTooLarge": "Number too large to format", "DE.Controllers.Main.txtTypeEquation": "Type an equation here.", "DE.Controllers.Main.txtUndefBookmark": "Undefined bookmark", "DE.Controllers.Main.txtXAxis": "X-akse", "DE.Controllers.Main.txtYAxis": "Y-akse", "DE.Controllers.Main.txtZeroDivide": "Zero divide", "DE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON><PERSON> feil.", "DE.Controllers.Main.unsupportedBrowserErrorText": "Nettleseren din er ikke støttet.", "DE.Controllers.Main.uploadDocExtMessage": "Unknown document format.", "DE.Controllers.Main.uploadDocFileCountMessage": "No documents uploaded.", "DE.Controllers.Main.uploadDocSizeMessage": "Maximum document size limit exceeded.", "DE.Controllers.Main.uploadImageExtMessage": "Ukjent bildeformat.", "DE.Controllers.Main.uploadImageFileCountMessage": "No images uploaded.", "DE.Controllers.Main.uploadImageSizeMessage": "The image is too big. The maximum size is 25 MB.", "DE.Controllers.Main.uploadImageTextText": "Laster opp bilde...", "DE.Controllers.Main.uploadImageTitleText": "<PERSON>er opp bilde", "DE.Controllers.Main.waitText": "Vennligst vent...", "DE.Controllers.Main.warnBrowserIE9": "The application has low capabilities on IE9. Use IE10 or higher", "DE.Controllers.Main.warnBrowserZoom": "Your browser current zoom setting is not fully supported. Please reset to the default zoom by pressing Ctrl+0.", "DE.Controllers.Main.warnLicenseAnonymous": "Tilgang nektet for anonyme brukere.<br>Dette dokumentet åpnes i visningsmodus.", "DE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "DE.Controllers.Main.warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact your administrator to learn more.", "DE.Controllers.Main.warnLicenseExp": "<PERSON><PERSON><PERSON> din har utløpt.<br>Vennligst oppdater lisensen og oppdater siden.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "License expired.<br>You have no access to document editing functionality.<br>Please contact your administrator.", "DE.Controllers.Main.warnLicenseLimitedRenewed": "License needs to be renewed.<br>You have a limited access to document editing functionality.<br>Please contact your administrator to get full access", "DE.Controllers.Main.warnLicenseUsersExceeded": "Du har nådd brukergrensen på %1 redigeringsprogram. Kontakt administratoren din for mer informasjon.", "DE.Controllers.Main.warnNoLicense": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact %1 sales team for personal upgrade terms.", "DE.Controllers.Main.warnNoLicenseUsers": "Du har nådd brukergrensen på antall %1 redigeringsprogram. Kontakt %1 salgsteamet for personlig oppgraderingsvilkår.", "DE.Controllers.Main.warnProcessRightsChange": "You have been denied the right to edit the file.", "DE.Controllers.Main.warnStartFilling": "Form filling is in progress.<br>File editing is not currently available.", "DE.Controllers.Navigation.txtBeginning": "Starten av dokumentet", "DE.Controllers.Navigation.txtGotoBeginning": "Go to the beginning of the document", "DE.Controllers.Print.textMarginsLast": "Last Custom", "DE.Controllers.Print.txtCustom": "Egendefinert", "DE.Controllers.Print.txtPrintRangeInvalid": "Invalid print range", "DE.Controllers.Print.txtPrintRangeSingleRange": "Enter either a single page number or a single page range (for example, 5-12). Or you can Print to PDF.", "DE.Controllers.Search.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Search.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "DE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "DE.Controllers.Search.textReplaceSuccess": "Search has been done. {0} occurrences have been replaced", "DE.Controllers.Search.warnReplaceString": "{0} er ikke et gyldig spesialtegn for \"erstatt med\"-boksen.", "DE.Controllers.Statusbar.textDisconnect": "<b><PERSON><PERSON><PERSON><PERSON> er brutt</b><br><PERSON><PERSON><PERSON><PERSON> å koble til. Sjekk tilkoblingsinnstillingene.", "DE.Controllers.Statusbar.textHasChanges": "New changes have been tracked", "DE.Controllers.Statusbar.textSetTrackChanges": "You are in Track Changes mode", "DE.Controllers.Statusbar.textTrackChanges": "The document is opened with the Track Changes mode enabled", "DE.Controllers.Statusbar.tipReview": "Track changes", "DE.Controllers.Statusbar.zoomText": "Zoom {0}%", "DE.Controllers.Toolbar.confirmAddFontName": "The font you are going to save is not available on the current device.<br>The text style will be displayed using one of the system fonts, the saved font will be used when it is available.<br>Do you want to continue?", "DE.Controllers.Toolbar.dataUrl": "Paste a data URL", "DE.Controllers.Toolbar.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "DE.Controllers.Toolbar.fileUrl": "Paste a file URL", "DE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "DE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "DE.Controllers.Toolbar.helpRtlDir": "Toggle between right-to-left (RTL) and left-to-right (LTR) text direction in  your documents.", "DE.Controllers.Toolbar.helpRtlDirHeader": "Switch text direction", "DE.Controllers.Toolbar.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textAccent": "Aksenter", "DE.Controllers.Toolbar.textBracket": "Klammeparantes", "DE.Controllers.Toolbar.textConvertFormDownload": "Download file as a fillable PDF form to be able to fill it out.", "DE.Controllers.Toolbar.textConvertFormSave": "Save file as a fillable PDF form to be able to fill it out.", "DE.Controllers.Toolbar.textDownloadPdf": "Download PDF", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "You need to specify URL.", "DE.Controllers.Toolbar.textFieldExample": "Example of writing code: TIM<PERSON> \\@ \"dddd, MMMM d, yyyy\"", "DE.Controllers.Toolbar.textFieldLabel": "Field codes", "DE.Controllers.Toolbar.textFieldTitle": "Field", "DE.Controllers.Toolbar.textFontSizeErr": "The entered value is incorrect.<br>Please enter a numeric value between 1 and 300", "DE.Controllers.Toolbar.textFraction": "Fractions", "DE.Controllers.Toolbar.textFunction": "Functions", "DE.Controllers.Toolbar.textGroup": "Gruppe", "DE.Controllers.Toolbar.textInsert": "Insert", "DE.Controllers.Toolbar.textIntegral": "Integrals", "DE.Controllers.Toolbar.textLargeOperator": "Large operators", "DE.Controllers.Toolbar.textLimitAndLog": "Limits and logarithms", "DE.Controllers.Toolbar.textMatrix": "Matrices", "DE.Controllers.Toolbar.textOperator": "Operators", "DE.Controllers.Toolbar.textRadical": "Radicals", "DE.Controllers.Toolbar.textRecentlyUsed": "Recently used", "DE.Controllers.Toolbar.textSavePdf": "Save as PDF", "DE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textSymbols": "Symboler", "DE.Controllers.Toolbar.textTabForms": "Forms", "DE.Controllers.Toolbar.textWarning": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_ArrowD": "Right-left arrow above", "DE.Controllers.Toolbar.txtAccent_ArrowL": "Leftwards arrow above", "DE.Controllers.Toolbar.txtAccent_ArrowR": "Rightwards arrow above", "DE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "DE.Controllers.Toolbar.txtAccent_BarTop": "Overbar", "DE.Controllers.Toolbar.txtAccent_BorderBox": "Innrammet formel (med eksempel)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Innrammet formel(Eksempel)", "DE.Controllers.Toolbar.txtAccent_Check": "Avkryssingsboks", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Overbrace", "DE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "ABC med overstrekning", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y with overbar", "DE.Controllers.Toolbar.txtAccent_DDDot": "Triple dot", "DE.Controllers.Toolbar.txtAccent_DDot": "Double dot", "DE.Controllers.Toolbar.txtAccent_Dot": "Dot", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "Double overbar", "DE.Controllers.Toolbar.txtAccent_Grave": "Grave", "DE.Controllers.Toolbar.txtAccent_GroupBot": "Grouping character below", "DE.Controllers.Toolbar.txtAccent_GroupTop": "Grouping character above", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "Leftwards harpoon above", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "Rightwards harpoon above", "DE.Controllers.Toolbar.txtAccent_Hat": "Hat", "DE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle": "Klammeparantes", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Klammeparenteser med skillelinjer ", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Klammeparenteser med skillelinjer ", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Right angle bracket", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Left angle bracket", "DE.Controllers.Toolbar.txtBracket_Curve": "Klammeparantes", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Klammeparenteser med skillelinjer ", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Right curly bracket", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Left curly bracket", "DE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON> (<PERSON> <PERSON><PERSON><PERSON><PERSON>)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON>)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "Stack object", "DE.Controllers.Toolbar.txtBracket_Custom_4": "Stack object in parentheses", "DE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Custom_6": "Binomialkoeffisienten", "DE.Controllers.Toolbar.txtBracket_Custom_7": "Binomialkoeffisienten", "DE.Controllers.Toolbar.txtBracket_Line": "Klammeparantes", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Right vertical bar", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Left vertical bar", "DE.Controllers.Toolbar.txtBracket_LineDouble": "Klammeparantes", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Right double vertical bar", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Left double vertical bar", "DE.Controllers.Toolbar.txtBracket_LowLim": "Klammeparantes", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Right floor", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Left floor", "DE.Controllers.Toolbar.txtBracket_Round": "Klammeparantes", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Klammeparenteser med skillelinjer ", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Right parenthesis", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Left parenthesis", "DE.Controllers.Toolbar.txtBracket_Square": "Klammeparantes", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Klammeparantes", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Klammeparantes", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Right square bracket", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Left square bracket", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Klammeparantes", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "Klammeparantes", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Right double square bracket", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Left double square bracket", "DE.Controllers.Toolbar.txtBracket_UppLim": "Klammeparantes", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Right ceiling", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Left ceiling", "DE.Controllers.Toolbar.txtDownload": "Download", "DE.Controllers.Toolbar.txtFractionDiagonal": "Skewed fraction", "DE.Controllers.Toolbar.txtFractionDifferential_1": "Differensial", "DE.Controllers.Toolbar.txtFractionDifferential_2": "Differensial", "DE.Controllers.Toolbar.txtFractionDifferential_3": "Differensial", "DE.Controllers.Toolbar.txtFractionDifferential_4": "Differensial", "DE.Controllers.Toolbar.txtFractionHorizontal": "Linear fraction", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi over 2", "DE.Controllers.Toolbar.txtFractionSmall": "Small fraction", "DE.Controllers.Toolbar.txtFractionVertical": "Stacked fraction", "DE.Controllers.Toolbar.txtFunction_1_Cos": "Inverse cosine function", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperbolic inverse cosine function", "DE.Controllers.Toolbar.txtFunction_1_Cot": "Inverse cotangent function", "DE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperbolic inverse cotangent function", "DE.Controllers.Toolbar.txtFunction_1_Csc": "Inverse cosecant function", "DE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperbolic inverse cosecant function", "DE.Controllers.Toolbar.txtFunction_1_Sec": "Inverse secant function", "DE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperbolic inverse secant function", "DE.Controllers.Toolbar.txtFunction_1_Sin": "Inverse sine function", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperbolic inverse sine function", "DE.Controllers.Toolbar.txtFunction_1_Tan": "Inverse tangent function", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperbolic inverse tangent function", "DE.Controllers.Toolbar.txtFunction_Cos": "cosinus funk<PERSON>", "DE.Controllers.Toolbar.txtFunction_Cosh": "Hyperbolic cosine function", "DE.Controllers.Toolbar.txtFunction_Cot": "Cotangens funksjon", "DE.Controllers.Toolbar.txtFunction_Coth": "Hyperbolic cotangent function", "DE.Controllers.Toolbar.txtFunction_Csc": "Cosecant function", "DE.Controllers.Toolbar.txtFunction_Csch": "Hyperbolic cosecant function", "DE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "Tangent formula", "DE.Controllers.Toolbar.txtFunction_Sec": "Secant function", "DE.Controllers.Toolbar.txtFunction_Sech": "Hyperbolic secant function", "DE.Controllers.Toolbar.txtFunction_Sin": "Sine function", "DE.Controllers.Toolbar.txtFunction_Sinh": "Hyperbolic sine function", "DE.Controllers.Toolbar.txtFunction_Tan": "Tangent function", "DE.Controllers.Toolbar.txtFunction_Tanh": "Hyperbolic tangent function", "DE.Controllers.Toolbar.txtIntegral": "Integral", "DE.Controllers.Toolbar.txtIntegral_dtheta": "Differential theta", "DE.Controllers.Toolbar.txtIntegral_dx": "Differential x", "DE.Controllers.Toolbar.txtIntegral_dy": "Differential y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral with stacked limits", "DE.Controllers.Toolbar.txtIntegralDouble": "Dobbeltintegral", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Dobbeltintegral", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Dobbeltintegral", "DE.Controllers.Toolbar.txtIntegralOriented": "Contour integral", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Contour integral with stacked limits", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "Surface integral", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Surface integral with stacked limits", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Surface integral with limits", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Contour integral with limits", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume integral", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volume integral with stacked limits", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volume integral with limits", "DE.Controllers.Toolbar.txtIntegralSubSup": "Integral with limits", "DE.Controllers.Toolbar.txtIntegralTriple": "Triple integral", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple integral with stacked limits", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple integral with limits", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Logical And", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Logical And with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Logical And with limits", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Logical And with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Logical And with subscript/superscript limits", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "Samprodukt", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Samprodukt", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Samprodukt", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Samprodukt", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Samprodukt", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summation over k of n choose k", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summation from i equal zero to n", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summation example using two indices", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Product example", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Union example", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Logical Or", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Logical Or with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Logical Or with limits", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Logical Or with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Logical Or with subscript/superscript limits", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersection", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersection with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersection with limits", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersection with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersection with subscript/superscript limits", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "Product", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Product with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Product with limits", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Product with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Product with subscript/superscript limits", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "Summation", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summation with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summation with limits", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summation with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summation with subscript/superscript limits", "DE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union with limits", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union with subscript/superscript limits", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "Limit example", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "Maximum example", "DE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "DE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON><PERSON>g logaritme", "DE.Controllers.Toolbar.txtLimitLog_Log": "Logarithm", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarithm", "DE.Controllers.Toolbar.txtLimitLog_Max": "Maximum", "DE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "DE.Controllers.Toolbar.txtMarginsH": "Top and bottom margins are too high for a given page height", "DE.Controllers.Toolbar.txtMarginsW": "Left and right margins are too wide for a given page width", "DE.Controllers.Toolbar.txtMatrix_1_2": "1x2 tom matrise", "DE.Controllers.Toolbar.txtMatrix_1_3": "1x3 tom matrise", "DE.Controllers.Toolbar.txtMatrix_2_1": "2x1 tom matrise", "DE.Controllers.Toolbar.txtMatrix_2_2": "2x2 tom matrise", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Empty 2 by 2 matrix in double vertical bars", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Empty 2 by 2 determinant", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Empty 2 by 2 matrix in parentheses", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Empty 2 by 2 matrix in brackets", "DE.Controllers.Toolbar.txtMatrix_2_3": "2x3 tom matrise", "DE.Controllers.Toolbar.txtMatrix_3_1": "3x1 tom matrise", "DE.Controllers.Toolbar.txtMatrix_3_2": "3x2 tom matrise", "DE.Controllers.Toolbar.txtMatrix_3_3": "3x3 tom matrise", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Grunnlinjepunkt", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "Midline dots", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonale prikker", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Vertical dots", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "Sparse matrix in parentheses", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "Sparse matrix in brackets", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 identitets matrise", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 identitetsmatrise", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 identitets-matrise", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 identitets-matrise", "DE.Controllers.Toolbar.txtNeedDownload": "PDF viewer can only save new changes in separate file copies. It doesn't support co-editing and other users won't see your changes unless you share a new file version.", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Right-left arrow below", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Right-left arrow above", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Leftwards arrow below", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Leftwards arrow above", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Rightwards arrow below", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Rightwards arrow above", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "Colon equal", "DE.Controllers.Toolbar.txtOperator_Custom_1": "Yields", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Delta yields", "DE.Controllers.Toolbar.txtOperator_Definition": "Equal to by definition", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta er lik", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Right-left double arrow below", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Right-left double arrow above", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Leftwards arrow below", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Leftwards arrow above", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Rightwards arrow below", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Rightwards arrow above", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "Equal equal", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "Minus equal", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus equal", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Measured by", "DE.Controllers.Toolbar.txtRadicalCustom_1": "Right hand side of quadratic formula", "DE.Controllers.Toolbar.txtRadicalCustom_2": "Square root of a squared plus b squared", "DE.Controllers.Toolbar.txtRadicalRoot_2": "Square root with degree", "DE.Controllers.Toolbar.txtRadicalRoot_3": "Kubikkrot", "DE.Controllers.Toolbar.txtRadicalRoot_n": "Radical with degree", "DE.Controllers.Toolbar.txtRadicalSqrt": "Square root", "DE.Controllers.Toolbar.txtSaveCopy": "Save copy", "DE.Controllers.Toolbar.txtScriptCustom_1": "x subscript y squared", "DE.Controllers.Toolbar.txtScriptCustom_2": "e to the minus i omega t", "DE.Controllers.Toolbar.txtScriptCustom_3": "x squared", "DE.Controllers.Toolbar.txtScriptCustom_4": "Y left superscript n left subscript one", "DE.Controllers.Toolbar.txtScriptSub": "Senket skrift", "DE.Controllers.Toolbar.txtScriptSubSup": "Subscript-superscript", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "Left subscript-superscript", "DE.Controllers.Toolbar.txtScriptSup": "<PERSON><PERSON> skrift", "DE.Controllers.Toolbar.txtSymbol_about": "Omtrent", "DE.Controllers.Toolbar.txtSymbol_additional": "Komplement", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "DE.Controllers.Toolbar.txtSymbol_approx": "Nesten lik", "DE.Controllers.Toolbar.txtSymbol_ast": "Stjerne operatoren", "DE.Controllers.Toolbar.txtSymbol_beta": "Beta", "DE.Controllers.Toolbar.txtSymbol_beth": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_bullet": "Punktoperatør", "DE.Controllers.Toolbar.txtSymbol_cap": "Intersection", "DE.Controllers.Toolbar.txtSymbol_cbrt": "Kubikkrot", "DE.Controllers.Toolbar.txtSymbol_cdots": "Midline horizontal ellipsis", "DE.Controllers.Toolbar.txtSymbol_celsius": "grader celsius", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "Tilnærmet lik", "DE.Controllers.Toolbar.txtSymbol_cup": "Union", "DE.Controllers.Toolbar.txtSymbol_ddots": "Down right diagonal ellipsis", "DE.Controllers.Toolbar.txtSymbol_degree": "grader", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "Division sign", "DE.Controllers.Toolbar.txtSymbol_downarrow": "Down arrow", "DE.Controllers.Toolbar.txtSymbol_emptyset": "Empty set", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "DE.Controllers.Toolbar.txtSymbol_equals": "Equal", "DE.Controllers.Toolbar.txtSymbol_equiv": "Identical to", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "There exist", "DE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grader fahrenheit", "DE.Controllers.Toolbar.txtSymbol_forall": "For all", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "DE.Controllers.Toolbar.txtSymbol_geq": "Greater than or equal to", "DE.Controllers.Toolbar.txtSymbol_gg": "Much greater than", "DE.Controllers.Toolbar.txtSymbol_greater": "Greater than", "DE.Controllers.Toolbar.txtSymbol_in": "Element of", "DE.Controllers.Toolbar.txtSymbol_inc": "Increment", "DE.Controllers.Toolbar.txtSymbol_infinity": "Infinity", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "Left arrow", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Left-right arrow", "DE.Controllers.Toolbar.txtSymbol_leq": "Less than or equal to", "DE.Controllers.Toolbar.txtSymbol_less": "Less than", "DE.Controllers.Toolbar.txtSymbol_ll": "Much less than", "DE.Controllers.Toolbar.txtSymbol_minus": "Minus", "DE.Controllers.Toolbar.txtSymbol_mp": "Minus plus", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "Not equal to", "DE.Controllers.Toolbar.txtSymbol_ni": "Contains as member", "DE.Controllers.Toolbar.txtSymbol_not": "Not sign", "DE.Controllers.Toolbar.txtSymbol_notexists": "There does not exist", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omicron", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "Partial differential", "DE.Controllers.Toolbar.txtSymbol_percent": "Prosentdel", "DE.Controllers.Toolbar.txtSymbol_phi": "Phi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "Plus", "DE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "DE.Controllers.Toolbar.txtSymbol_propto": "Proportional to", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "Fourth root", "DE.Controllers.Toolbar.txtSymbol_qed": "End of proof", "DE.Controllers.Toolbar.txtSymbol_rddots": "Up right diagonal ellipsis", "DE.Controllers.Toolbar.txtSymbol_rho": "Rho", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "Right arrow", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "Radical sign", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "Therefore", "DE.Controllers.Toolbar.txtSymbol_theta": "Theta", "DE.Controllers.Toolbar.txtSymbol_times": "Multiplikasjons-tegn", "DE.Controllers.Toolbar.txtSymbol_uparrow": "Up Arrow", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon variant", "DE.Controllers.Toolbar.txtSymbol_varphi": "Phi variant", "DE.Controllers.Toolbar.txtSymbol_varpi": "Pi variant", "DE.Controllers.Toolbar.txtSymbol_varrho": "Rho variant", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma variant", "DE.Controllers.Toolbar.txtSymbol_vartheta": "Theta variant", "DE.Controllers.Toolbar.txtSymbol_vdots": "Vertical ellipsis", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Toolbar.txtUntitled": "Untitled", "DE.Controllers.Viewport.textFitPage": "Fit to <PERSON>", "DE.Controllers.Viewport.textFitWidth": "Fit to Width", "DE.Controllers.Viewport.txtDarkMode": "Dark mode", "DE.Views.BookmarksDialog.textAdd": "Legg til", "DE.Views.BookmarksDialog.textBookmarkName": "Bokmerk navnet", "DE.Views.BookmarksDialog.textClose": "Lukk", "DE.Views.BookmarksDialog.textCopy": "<PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textDelete": "<PERSON><PERSON>", "DE.Views.BookmarksDialog.textGetLink": "Get link", "DE.Views.BookmarksDialog.textGoto": "Go to", "DE.Views.BookmarksDialog.textHidden": "Hidden bookmarks", "DE.Views.BookmarksDialog.textLocation": "Plassering", "DE.Views.BookmarksDialog.textName": "Navn", "DE.Views.BookmarksDialog.textSort": "Sort by", "DE.Views.BookmarksDialog.textTitle": "Bokmerker", "DE.Views.BookmarksDialog.txtInvalidName": "Bokmerkets navn kan bare inneholde bokstaver, tall og streker, og skal begynne med bokstaven", "DE.Views.CaptionDialog.textAdd": "Legg til", "DE.Views.CaptionDialog.textAfter": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textBefore": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textCaption": "Bildetekst", "DE.Views.CaptionDialog.textChapter": "Kapittel starter med stil", "DE.Views.CaptionDialog.textChapterInc": "Include chapter number", "DE.Views.CaptionDialog.textColon": "kolon", "DE.Views.CaptionDialog.textDash": "tankestrek", "DE.Views.CaptionDialog.textDelete": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textEquation": "Equation", "DE.Views.CaptionDialog.textExamples": "Examples: Table 2-A, Image 1.IV", "DE.Views.CaptionDialog.textExclude": "Exclude label from caption", "DE.Views.CaptionDialog.textFigure": "Figure", "DE.Views.CaptionDialog.textHyphen": "hyphen", "DE.Views.CaptionDialog.textInsert": "Insert", "DE.Views.CaptionDialog.textLabel": "Label", "DE.Views.CaptionDialog.textLabelError": "Label must not be empty.", "DE.Views.CaptionDialog.textLongDash": "long dash", "DE.Views.CaptionDialog.textNumbering": "Numbering", "DE.Views.CaptionDialog.textPeriod": "period", "DE.Views.CaptionDialog.textSeparator": "Use separator", "DE.Views.CaptionDialog.textTable": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textTitle": "Insert caption", "DE.Views.CellsAddDialog.textCol": "<PERSON><PERSON><PERSON>", "DE.Views.CellsAddDialog.textDown": "Under <PERSON><PERSON><PERSON>", "DE.Views.CellsAddDialog.textLeft": "To the left", "DE.Views.CellsAddDialog.textRight": "To the right", "DE.Views.CellsAddDialog.textRow": "<PERSON><PERSON>", "DE.Views.CellsAddDialog.textTitle": "Insert several", "DE.Views.CellsAddDialog.textUp": "Over <PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "DE.Views.ChartSettings.text3dHeight": "Height (% of base)", "DE.Views.ChartSettings.text3dRotation": "3D rotasjon", "DE.Views.ChartSettings.textAdvanced": "Show advanced settings", "DE.Views.ChartSettings.textAutoscale": "Autoskalér", "DE.Views.ChartSettings.textChartType": "<PERSON><PERSON> diagramtype", "DE.Views.ChartSettings.textDefault": "Default rotation", "DE.Views.ChartSettings.textDown": "Down", "DE.Views.ChartSettings.textEditData": "Rediger data", "DE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textLeft": "Left", "DE.Views.ChartSettings.textNarrow": "Narrow field of view", "DE.Views.ChartSettings.textOriginalSize": "Standard størrelse", "DE.Views.ChartSettings.textPerspective": "Perspective", "DE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textRightAngle": "Right angle axes", "DE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textStyle": "Stil", "DE.Views.ChartSettings.textUndock": "Undock from panel", "DE.Views.ChartSettings.textUp": "Up", "DE.Views.ChartSettings.textWiden": "Widen field of view", "DE.Views.ChartSettings.textWidth": "Bredde", "DE.Views.ChartSettings.textWrap": "Wrapping Style", "DE.Views.ChartSettings.textX": "X rotation", "DE.Views.ChartSettings.textY": "Y rotation", "DE.Views.ChartSettings.txtBehind": "Bak", "DE.Views.ChartSettings.txtInFront": "<PERSON>an", "DE.Views.ChartSettings.txtInline": "In line with text", "DE.Views.ChartSettings.txtSquare": "Square", "DE.Views.ChartSettings.txtThrough": "Gjennom", "DE.Views.ChartSettings.txtTight": "<PERSON><PERSON>", "DE.Views.ChartSettings.txtTitle": "Diagram", "DE.Views.ChartSettings.txtTopAndBottom": "<PERSON><PERSON> og bunn", "DE.Views.ControlSettingsDialog.strGeneral": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textAdd": "Legg til", "DE.Views.ControlSettingsDialog.textAppearance": "Utseende", "DE.Views.ControlSettingsDialog.textApplyAll": "Bruk på alle", "DE.Views.ControlSettingsDialog.textBox": "Avgrensningsboks", "DE.Views.ControlSettingsDialog.textChange": "Edit", "DE.Views.ControlSettingsDialog.textCheckbox": "Avkrysningsboks", "DE.Views.ControlSettingsDialog.textChecked": "Checked symbol", "DE.Views.ControlSettingsDialog.textColor": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textCombobox": "Combo box", "DE.Views.ControlSettingsDialog.textDate": "Datoformat", "DE.Views.ControlSettingsDialog.textDelete": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textDisplayName": "Display name", "DE.Views.ControlSettingsDialog.textDown": "Down", "DE.Views.ControlSettingsDialog.textDropDown": "Drop-down list", "DE.Views.ControlSettingsDialog.textFormat": "Display the date like this", "DE.Views.ControlSettingsDialog.textLang": "Language", "DE.Views.ControlSettingsDialog.textLock": "Locking", "DE.Views.ControlSettingsDialog.textName": "Title", "DE.Views.ControlSettingsDialog.textNone": "None", "DE.Views.ControlSettingsDialog.textPlaceholder": "Placeholder", "DE.Views.ControlSettingsDialog.textShowAs": "Show as", "DE.Views.ControlSettingsDialog.textSystemColor": "System", "DE.Views.ControlSettingsDialog.textTag": "Tag", "DE.Views.ControlSettingsDialog.textTitle": "Innstillinger for innholdskontroll", "DE.Views.ControlSettingsDialog.textUnchecked": "Unchecked symbol", "DE.Views.ControlSettingsDialog.textUp": "Up", "DE.Views.ControlSettingsDialog.textValue": "Verdi", "DE.Views.ControlSettingsDialog.tipChange": "<PERSON>re symbol", "DE.Views.ControlSettingsDialog.txtLockDelete": "Innholdskontroll kan ikke slettes", "DE.Views.ControlSettingsDialog.txtLockEdit": "Contents cannot be edited", "DE.Views.ControlSettingsDialog.txtRemContent": "Remove content control when contents are edited", "DE.Views.CrossReferenceDialog.textAboveBelow": "Over/under", "DE.Views.CrossReferenceDialog.textBookmark": "Bokmerke", "DE.Views.CrossReferenceDialog.textBookmarkText": "Bokmerketekst", "DE.Views.CrossReferenceDialog.textCaption": "Entire caption", "DE.Views.CrossReferenceDialog.textEmpty": "The request reference is empty.", "DE.Views.CrossReferenceDialog.textEndnote": "Endnote", "DE.Views.CrossReferenceDialog.textEndNoteNum": "Endnote number", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "Endnote number (formatted)", "DE.Views.CrossReferenceDialog.textEquation": "Equation", "DE.Views.CrossReferenceDialog.textFigure": "Figure", "DE.Views.CrossReferenceDialog.textFootnote": "Fotnote", "DE.Views.CrossReferenceDialog.textHeading": "Heading", "DE.Views.CrossReferenceDialog.textHeadingNum": "Heading number", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "Heading number (full context)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "Heading number (no context)", "DE.Views.CrossReferenceDialog.textHeadingText": "Heading text", "DE.Views.CrossReferenceDialog.textIncludeAbove": "Include above/below", "DE.Views.CrossReferenceDialog.textInsert": "Insert", "DE.Views.CrossReferenceDialog.textInsertAs": "Insert as hyperlink", "DE.Views.CrossReferenceDialog.textLabelNum": "Only label and number", "DE.Views.CrossReferenceDialog.textNoteNum": "Footnote number", "DE.Views.CrossReferenceDialog.textNoteNumForm": "Footnote number (formatted)", "DE.Views.CrossReferenceDialog.textOnlyCaption": "Only caption text", "DE.Views.CrossReferenceDialog.textPageNum": "Sidetall", "DE.Views.CrossReferenceDialog.textParagraph": "Numbered item", "DE.Views.CrossReferenceDialog.textParaNum": "Paragraph number", "DE.Views.CrossReferenceDialog.textParaNumFull": "Paragraph number (full context)", "DE.Views.CrossReferenceDialog.textParaNumNo": "Paragraph number (no context)", "DE.Views.CrossReferenceDialog.textSeparate": "Separate numbers with", "DE.Views.CrossReferenceDialog.textTable": "<PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textText": "Paragraph text", "DE.Views.CrossReferenceDialog.textWhich": "For which caption", "DE.Views.CrossReferenceDialog.textWhichBookmark": "For which bookmark", "DE.Views.CrossReferenceDialog.textWhichEndnote": "For which endnote", "DE.Views.CrossReferenceDialog.textWhichHeading": "For which heading", "DE.Views.CrossReferenceDialog.textWhichNote": "For which footnote", "DE.Views.CrossReferenceDialog.textWhichPara": "For which numbered item", "DE.Views.CrossReferenceDialog.txtReference": "Insert reference to", "DE.Views.CrossReferenceDialog.txtTitle": "Cross-reference", "DE.Views.CrossReferenceDialog.txtType": "Reference type", "DE.Views.CustomColumnsDialog.textColumns": "Number of columns", "DE.Views.CustomColumnsDialog.textEqualWidth": "Equal column width", "DE.Views.CustomColumnsDialog.textSeparator": "Column divider", "DE.Views.CustomColumnsDialog.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.CustomColumnsDialog.textTitleSpacing": "Spacing", "DE.Views.CustomColumnsDialog.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.DateTimeDialog.confirmDefault": "Set default format for {0}: \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "Set as default", "DE.Views.DateTimeDialog.textFormat": "Formats", "DE.Views.DateTimeDialog.textLang": "Language", "DE.Views.DateTimeDialog.textUpdate": "Update automatically", "DE.Views.DateTimeDialog.txtTitle": "Dato og tidspunkt", "DE.Views.DocProtection.hintProtectDoc": "Protect document", "DE.Views.DocProtection.txtDocProtectedComment": "Document is protected.<br>You may only insert comments to this document.", "DE.Views.DocProtection.txtDocProtectedForms": "Document is protected.<br>You may only fill in forms in this document.", "DE.Views.DocProtection.txtDocProtectedTrack": "Document is protected.<br>You may edit this document, but all changes will be tracked.", "DE.Views.DocProtection.txtDocProtectedView": "Document is protected.<br>You may only view this document.", "DE.Views.DocProtection.txtDocUnlockDescription": "Enter a password to unprotect document", "DE.Views.DocProtection.txtProtectDoc": "Protect Document", "DE.Views.DocProtection.txtUnlockTitle": "Unprotect document", "DE.Views.DocumentHolder.aboveText": "Over", "DE.Views.DocumentHolder.addCommentText": "Tilføy kommentar", "DE.Views.DocumentHolder.advancedDropCapText": "Drop cap settings", "DE.Views.DocumentHolder.advancedEquationText": "Equation settings", "DE.Views.DocumentHolder.advancedFrameText": "Frame advanced settings", "DE.Views.DocumentHolder.advancedParagraphText": "Paragraph advanced settings", "DE.Views.DocumentHolder.advancedTableText": "<PERSON>nse<PERSON> tabell-innstillinger", "DE.Views.DocumentHolder.advancedText": "Avanserte innstillinger", "DE.Views.DocumentHolder.alignmentText": "Opps<PERSON>ling", "DE.Views.DocumentHolder.allLinearText": "Alle - lineær", "DE.Views.DocumentHolder.allProfText": "Alle - profesjonell", "DE.Views.DocumentHolder.belowText": "Under", "DE.Views.DocumentHolder.breakBeforeText": "Page break before", "DE.Views.DocumentHolder.bulletsText": "Punkter og nummerering", "DE.Views.DocumentHolder.cellAlignText": "<PERSON><PERSON><PERSON><PERSON> justering", "DE.Views.DocumentHolder.cellText": "Celle", "DE.Views.DocumentHolder.centerText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.chartText": "Diagram avanserte innstillinger", "DE.Views.DocumentHolder.columnText": "Kolonne", "DE.Views.DocumentHolder.currLinearText": "Current - Linear", "DE.Views.DocumentHolder.currProfText": "Current - Professional", "DE.Views.DocumentHolder.deleteColumnText": "Slett kolonne", "DE.Views.DocumentHolder.deleteRowText": "Slett rad", "DE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON> tabell", "DE.Views.DocumentHolder.deleteText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.direct270Text": "Rotate text up", "DE.Views.DocumentHolder.direct90Text": "Rotate text down", "DE.Views.DocumentHolder.directHText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.directionText": "Text direction", "DE.Views.DocumentHolder.editChartText": "Rediger data", "DE.Views.DocumentHolder.editFooterText": "<PERSON><PERSON> bunnte<PERSON>t", "DE.Views.DocumentHolder.editHeaderText": "<PERSON>iger topptekst", "DE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON> lenke", "DE.Views.DocumentHolder.eqToDisplayText": "Change to Display", "DE.Views.DocumentHolder.eqToInlineText": "Change to Inline", "DE.Views.DocumentHolder.guestText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "DE.Views.DocumentHolder.hyperlinkText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.ignoreAllSpellText": "Ignore all", "DE.Views.DocumentHolder.ignoreSpellText": "Ignore", "DE.Views.DocumentHolder.imageText": "Image advanced settings", "DE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> left", "DE.Views.DocumentHolder.insertColumnRightText": "Column right", "DE.Views.DocumentHolder.insertColumnText": "Insert column", "DE.Views.DocumentHolder.insertRowAboveText": "Row above", "DE.Views.DocumentHolder.insertRowBelowText": "Row below", "DE.Views.DocumentHolder.insertRowText": "Insert row", "DE.Views.DocumentHolder.insertText": "Insert", "DE.Views.DocumentHolder.keepLinesText": "Keep lines together", "DE.Views.DocumentHolder.langText": "Select language", "DE.Views.DocumentHolder.latexText": "LaTeX", "DE.Views.DocumentHolder.leftText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.loadSpellText": "Loading variants...", "DE.Views.DocumentHolder.mergeCellsText": "Merge cells", "DE.Views.DocumentHolder.mniImageFromFile": "Image from File", "DE.Views.DocumentHolder.mniImageFromStorage": "Image from Storage", "DE.Views.DocumentHolder.mniImageFromUrl": "Image from URL", "DE.Views.DocumentHolder.moreText": "More variants...", "DE.Views.DocumentHolder.noSpellVariantsText": "No variants", "DE.Views.DocumentHolder.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.originalSizeText": "Standard størrelse", "DE.Views.DocumentHolder.paragraphText": "Avsnitt", "DE.Views.DocumentHolder.removeHyperlinkText": "Remove hyperlink", "DE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.rowText": "Row", "DE.Views.DocumentHolder.saveStyleText": "<PERSON><PERSON><PERSON><PERSON> ny stil", "DE.Views.DocumentHolder.selectCellText": "Select cell", "DE.Views.DocumentHolder.selectColumnText": "Select column", "DE.Views.DocumentHolder.selectRowText": "Select row", "DE.Views.DocumentHolder.selectTableText": "Select table", "DE.Views.DocumentHolder.selectText": "Velg", "DE.Views.DocumentHolder.shapeText": "Shape advanced settings", "DE.Views.DocumentHolder.showEqToolbar": "Show equation toolbar", "DE.Views.DocumentHolder.spellcheckText": "Spellcheck", "DE.Views.DocumentHolder.splitCellsText": "Split cell...", "DE.Views.DocumentHolder.splitCellTitleText": "Split Cell", "DE.Views.DocumentHolder.strDelete": "Remove signature", "DE.Views.DocumentHolder.strDetails": "Signature Details", "DE.Views.DocumentHolder.strSetup": "Signature Setup", "DE.Views.DocumentHolder.strSign": "Sign", "DE.Views.DocumentHolder.styleText": "Formatting as Style", "DE.Views.DocumentHolder.tableText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textAccept": "<PERSON><PERSON><PERSON><PERSON> endring", "DE.Views.DocumentHolder.textAlign": "Still opp", "DE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textArrangeBack": "Send to background", "DE.Views.DocumentHolder.textArrangeBackward": "Send backward", "DE.Views.DocumentHolder.textArrangeForward": "Flytt fremover", "DE.Views.DocumentHolder.textArrangeFront": "<PERSON><PERSON><PERSON> fremst", "DE.Views.DocumentHolder.textCells": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textClearField": "Clear field", "DE.Views.DocumentHolder.textCol": "Delete entire column", "DE.Views.DocumentHolder.textContentControls": "Innholdskontroll", "DE.Views.DocumentHolder.textContinueNumbering": "Fortsett nummerering", "DE.Views.DocumentHolder.textCopy": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCrop": "Beskjære", "DE.Views.DocumentHolder.textCropFill": "Fill", "DE.Views.DocumentHolder.textCropFit": "Fit", "DE.Views.DocumentHolder.textCut": "<PERSON><PERSON><PERSON> ut", "DE.Views.DocumentHolder.textDistributeCols": "Distribute columns", "DE.Views.DocumentHolder.textDistributeRows": "<PERSON>el rader", "DE.Views.DocumentHolder.textEditControls": "Innstillinger for innholdskontroll", "DE.Views.DocumentHolder.textEditField": "Edit field", "DE.Views.DocumentHolder.textEditObject": "Edit object", "DE.Views.DocumentHolder.textEditPoints": "Edit points", "DE.Views.DocumentHolder.textEditWrapBoundary": "Rediger ombrytningsgrense", "DE.Views.DocumentHolder.textFieldCodes": "Toggle field codes", "DE.Views.DocumentHolder.textFlipH": "Flip horizontally", "DE.Views.DocumentHolder.textFlipV": "Flip vertically", "DE.Views.DocumentHolder.textFollow": "Follow move", "DE.Views.DocumentHolder.textFromFile": "From file", "DE.Views.DocumentHolder.textFromStorage": "From storage", "DE.Views.DocumentHolder.textFromUrl": "From URL", "DE.Views.DocumentHolder.textIndents": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textJoinList": "Join to previous list", "DE.Views.DocumentHolder.textLeft": "Shift cells left", "DE.Views.DocumentHolder.textNest": "Nest table", "DE.Views.DocumentHolder.textNextPage": "Next page", "DE.Views.DocumentHolder.textNumberingValue": "Numbering value", "DE.Views.DocumentHolder.textPaste": "Lim inn", "DE.Views.DocumentHolder.textPrevPage": "Previous Page", "DE.Views.DocumentHolder.textRedo": "Redo", "DE.Views.DocumentHolder.textRefreshField": "Update field", "DE.Views.DocumentHolder.textReject": "Reject change", "DE.Views.DocumentHolder.textRemCheckBox": "Remove Checkbox", "DE.Views.DocumentHolder.textRemComboBox": "Remove Combo Box", "DE.Views.DocumentHolder.textRemDropdown": "Remove Dropdown", "DE.Views.DocumentHolder.textRemField": "Remove text field", "DE.Views.DocumentHolder.textRemove": "Remove", "DE.Views.DocumentHolder.textRemoveControl": "Remove content control", "DE.Views.DocumentHolder.textRemPicture": "Slett bilde", "DE.Views.DocumentHolder.textRemRadioBox": "Remove Radio button", "DE.Views.DocumentHolder.textReplace": "Replace image", "DE.Views.DocumentHolder.textResetCrop": "Reset crop", "DE.Views.DocumentHolder.textRotate": "Rotate", "DE.Views.DocumentHolder.textRotate270": "Rotate 90В° Counterclockwise", "DE.Views.DocumentHolder.textRotate90": "Rotate 90В° Clockwise", "DE.Views.DocumentHolder.textRow": "Delete entire row", "DE.Views.DocumentHolder.textSaveAsPicture": "Save as picture", "DE.Views.DocumentHolder.textSeparateList": "Separate list", "DE.Views.DocumentHolder.textSettings": "Innstillinger", "DE.Views.DocumentHolder.textSeveral": "Several rows/columns", "DE.Views.DocumentHolder.textShapeAlignBottom": "Still opp bunn", "DE.Views.DocumentHolder.textShapeAlignCenter": "Still opp senter", "DE.Views.DocumentHolder.textShapeAlignLeft": "Still opp venstre", "DE.Views.DocumentHolder.textShapeAlignMiddle": "Still opp midt", "DE.Views.DocumentHolder.textShapeAlignRight": "Still opp høyre", "DE.Views.DocumentHolder.textShapeAlignTop": "Still opp topp", "DE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "DE.Views.DocumentHolder.textStartNewList": "Start new list", "DE.Views.DocumentHolder.textStartNumberingFrom": "Set numbering value", "DE.Views.DocumentHolder.textTitleCellsRemove": "Delete cells", "DE.Views.DocumentHolder.textTOC": "Innholdsfortegnelse", "DE.Views.DocumentHolder.textTOCSettings": "Innstillinger for innholdsfortegnelsen", "DE.Views.DocumentHolder.textUndo": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textUpdateAll": "Update entire table", "DE.Views.DocumentHolder.textUpdatePages": "Update page numbers only", "DE.Views.DocumentHolder.textUpdateTOC": "Update table of contents", "DE.Views.DocumentHolder.textWrap": "Wrapping style", "DE.Views.DocumentHolder.tipIsLocked": "This element is currently being edited by another user.", "DE.Views.DocumentHolder.toDictionaryText": "Legg til ordbok", "DE.Views.DocumentHolder.txtAddBottom": "Legg til bunn<PERSON>me", "DE.Views.DocumentHolder.txtAddFractionBar": "Legg til brøkstrek", "DE.Views.DocumentHolder.txtAddHor": "Legg til horisontal linje", "DE.Views.DocumentHolder.txtAddLB": "<PERSON><PERSON> til bunnlinje", "DE.Views.DocumentHolder.txtAddLeft": "Legg til venstre ramme", "DE.Views.DocumentHolder.txtAddLT": "Legg til venstre topplinje", "DE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON> til høyre ramme", "DE.Views.DocumentHolder.txtAddTop": "<PERSON><PERSON> til toppramme", "DE.Views.DocumentHolder.txtAddVer": "Legg til vertikal linje", "DE.Views.DocumentHolder.txtAlignToChar": "Juster til tegn", "DE.Views.DocumentHolder.txtBehind": "Bak", "DE.Views.DocumentHolder.txtBorderProps": "Rammeegenskaper", "DE.Views.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtColumnAlign": "Column alignment", "DE.Views.DocumentHolder.txtDecreaseArg": "Reduser argumentets størrelse", "DE.Views.DocumentHolder.txtDeleteArg": "<PERSON><PERSON> argument", "DE.Views.DocumentHolder.txtDeleteBreak": "Slett manuelt linjeskift", "DE.Views.DocumentHolder.txtDeleteChars": "Slett de omsluttende tegnene", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Slett de omsluttende tegnene og seperatorer", "DE.Views.DocumentHolder.txtDeleteEq": "<PERSON><PERSON> ligning", "DE.Views.DocumentHolder.txtDeleteGroupChar": "Slett bokstav", "DE.Views.DocumentHolder.txtDeleteRadical": "Delete radical", "DE.Views.DocumentHolder.txtDistribHor": "Distribute horizontally", "DE.Views.DocumentHolder.txtDistribVert": "Distribute vertically", "DE.Views.DocumentHolder.txtEmpty": "(<PERSON><PERSON>)", "DE.Views.DocumentHolder.txtFractionLinear": "<PERSON><PERSON> til lineær brøk", "DE.Views.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> til skjev brøk", "DE.Views.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> til stablet brøk", "DE.Views.DocumentHolder.txtGroup": "Gruppe", "DE.Views.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON> over tekst", "DE.Views.DocumentHolder.txtGroupCharUnder": "Karakter under tekst", "DE.Views.DocumentHolder.txtHideBottom": "Hide bottom border", "DE.Views.DocumentHolder.txtHideBottomLimit": "Hide bottom limit", "DE.Views.DocumentHolder.txtHideCloseBracket": "Hide closing bracket", "DE.Views.DocumentHolder.txtHideDegree": "Hide degree", "DE.Views.DocumentHolder.txtHideHor": "Hide horizontal line", "DE.Views.DocumentHolder.txtHideLB": "<PERSON><PERSON> left bottom line", "DE.Views.DocumentHolder.txtHideLeft": "<PERSON><PERSON> left border", "DE.Views.DocumentHolder.txtHideLT": "<PERSON><PERSON> left top line", "DE.Views.DocumentHolder.txtHideOpenBracket": "Hide opening bracket", "DE.Views.DocumentHolder.txtHidePlaceholder": "Hide placeholder", "DE.Views.DocumentHolder.txtHideRight": "Hide right border", "DE.Views.DocumentHolder.txtHideTop": "Hide top border", "DE.Views.DocumentHolder.txtHideTopLimit": "Hide top limit", "DE.Views.DocumentHolder.txtHideVer": "Hide vertical line", "DE.Views.DocumentHolder.txtIncreaseArg": "Increase argument size", "DE.Views.DocumentHolder.txtInFront": "<PERSON>an", "DE.Views.DocumentHolder.txtInline": "In line with text", "DE.Views.DocumentHolder.txtInsertArgAfter": "Insert argument after", "DE.Views.DocumentHolder.txtInsertArgBefore": "Insert argument before", "DE.Views.DocumentHolder.txtInsertBreak": "Insert manual break", "DE.Views.DocumentHolder.txtInsertCaption": "Insert caption", "DE.Views.DocumentHolder.txtInsertEqAfter": "Insert equation after", "DE.Views.DocumentHolder.txtInsertEqBefore": "Insert equation before", "DE.Views.DocumentHolder.txtInsImage": "Insert image from file", "DE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "DE.Views.DocumentHolder.txtKeepTextOnly": "Keep text only", "DE.Views.DocumentHolder.txtLimitChange": "<PERSON><PERSON>", "DE.Views.DocumentHolder.txtLimitOver": "Limit over text", "DE.Views.DocumentHolder.txtLimitUnder": "Limit under text", "DE.Views.DocumentHolder.txtMatchBrackets": "Match brackets to argument height", "DE.Views.DocumentHolder.txtMatrixAlign": "Matrix alignment", "DE.Views.DocumentHolder.txtOverbar": "<PERSON><PERSON> over teksten", "DE.Views.DocumentHolder.txtOverwriteCells": "Overwrite cells", "DE.Views.DocumentHolder.txtPasteSourceFormat": "Keep source formatting", "DE.Views.DocumentHolder.txtPressLink": "Press {0} and click link", "DE.Views.DocumentHolder.txtPrintSelection": "Print selection", "DE.Views.DocumentHolder.txtRemFractionBar": "Remove fraction bar", "DE.Views.DocumentHolder.txtRemLimit": "Remove limit", "DE.Views.DocumentHolder.txtRemoveAccentChar": "Remove accent character", "DE.Views.DocumentHolder.txtRemoveBar": "Remove bar", "DE.Views.DocumentHolder.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "DE.Views.DocumentHolder.txtRemScripts": "Remove scripts", "DE.Views.DocumentHolder.txtRemSubscript": "Remove subscript", "DE.Views.DocumentHolder.txtRemSuperscript": "Remove superscript", "DE.Views.DocumentHolder.txtScriptsAfter": "Scripts after text", "DE.Views.DocumentHolder.txtScriptsBefore": "Scripts before text", "DE.Views.DocumentHolder.txtShowBottomLimit": "Show bottom limit", "DE.Views.DocumentHolder.txtShowCloseBracket": "Show closing bracket", "DE.Views.DocumentHolder.txtShowDegree": "Show degree", "DE.Views.DocumentHolder.txtShowOpenBracket": "Show opening bracket", "DE.Views.DocumentHolder.txtShowPlaceholder": "Show placeholder", "DE.Views.DocumentHolder.txtShowTopLimit": "Show top limit", "DE.Views.DocumentHolder.txtSquare": "Square", "DE.Views.DocumentHolder.txtStretchBrackets": "Stretch brackets", "DE.Views.DocumentHolder.txtThrough": "Gjennom", "DE.Views.DocumentHolder.txtTight": "<PERSON><PERSON>", "DE.Views.DocumentHolder.txtTop": "Topp", "DE.Views.DocumentHolder.txtTopAndBottom": "<PERSON><PERSON> og bunn", "DE.Views.DocumentHolder.txtUnderbar": "Linje under teksten", "DE.Views.DocumentHolder.txtUngroup": "Ungroup", "DE.Views.DocumentHolder.txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?", "DE.Views.DocumentHolder.unicodeText": "Unicode", "DE.Views.DocumentHolder.updateStyleText": "Update %1 style", "DE.Views.DocumentHolder.vertAlignText": "Vertical alignment", "DE.Views.DropcapSettingsAdvanced.strBorders": "Linjer & Fyll", "DE.Views.DropcapSettingsAdvanced.strDropcap": "Drop cap", "DE.Views.DropcapSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textAlign": "Opps<PERSON>ling", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "Minst", "DE.Views.DropcapSettingsAdvanced.textAuto": "Auto", "DE.Views.DropcapSettingsAdvanced.textBackColor": "Bakgrunnsfarge", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "Klikk på diagrammet eller bruk knappene til å velge rammer", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "Linjestørrel<PERSON>", "DE.Views.DropcapSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textCenter": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textColumn": "Kolonne", "DE.Views.DropcapSettingsAdvanced.textDistance": "Distance from text", "DE.Views.DropcapSettingsAdvanced.textExact": "Nøyaktig", "DE.Views.DropcapSettingsAdvanced.textFlow": "Flow frame", "DE.Views.DropcapSettingsAdvanced.textFont": "Font", "DE.Views.DropcapSettingsAdvanced.textFrame": "<PERSON>ame", "DE.Views.DropcapSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textInline": "Inline frame", "DE.Views.DropcapSettingsAdvanced.textInMargin": "In margin", "DE.Views.DropcapSettingsAdvanced.textInText": "In text", "DE.Views.DropcapSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textMargin": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textMove": "Move with text", "DE.Views.DropcapSettingsAdvanced.textNone": "None", "DE.Views.DropcapSettingsAdvanced.textPage": "Side", "DE.Views.DropcapSettingsAdvanced.textParagraph": "Avsnitt", "DE.Views.DropcapSettingsAdvanced.textParameters": "Parameters", "DE.Views.DropcapSettingsAdvanced.textPosition": "Posisjon", "DE.Views.DropcapSettingsAdvanced.textRelative": "Relative to", "DE.Views.DropcapSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "Height in rows", "DE.Views.DropcapSettingsAdvanced.textTitle": "Drop cap - advanced settings", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "Frame - advanced settings", "DE.Views.DropcapSettingsAdvanced.textTop": "Topp", "DE.Views.DropcapSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textWidth": "Bredde", "DE.Views.DropcapSettingsAdvanced.tipFontName": "Font", "DE.Views.EditListItemDialog.textDisplayName": "Display name", "DE.Views.EditListItemDialog.textNameError": "Display name must not be empty.", "DE.Views.EditListItemDialog.textValue": "Verdi", "DE.Views.EditListItemDialog.textValueError": "Det finnes allerede en gjenstand med samme verdi.", "DE.Views.FileMenu.ariaFileMenu": "File menu", "DE.Views.FileMenu.btnBackCaption": "Open File Location", "DE.Views.FileMenu.btnCloseEditor": "Close File", "DE.Views.FileMenu.btnCloseMenuCaption": "Lukk menyen", "DE.Views.FileMenu.btnCreateNewCaption": "Opprett ny", "DE.Views.FileMenu.btnDownloadCaption": "Last ned som", "DE.Views.FileMenu.btnExitCaption": "Lukk", "DE.Views.FileMenu.btnFileOpenCaption": "Open", "DE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnHistoryCaption": "Version History", "DE.Views.FileMenu.btnInfoCaption": "Dokumentinformasjon", "DE.Views.FileMenu.btnPrintCaption": "Skriv ut", "DE.Views.FileMenu.btnProtectCaption": "Protect", "DE.Views.FileMenu.btnRecentFilesCaption": "Open Recent", "DE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON>", "DE.Views.FileMenu.btnReturnCaption": "Tilbake til dokument", "DE.Views.FileMenu.btnRightsCaption": "Tilgangsrettigheter", "DE.Views.FileMenu.btnSaveAsCaption": "Save As", "DE.Views.FileMenu.btnSaveCaption": "Lagre", "DE.Views.FileMenu.btnSaveCopyAsCaption": "Save Copy As", "DE.Views.FileMenu.btnSettingsCaption": "Avanserte innstillinger", "DE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "DE.Views.FileMenu.btnToEditCaption": "Rediger dokument", "DE.Views.FileMenu.textDownload": "Last ned", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "Tomt dokument", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Opprett ny", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Bruk", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON> til forfatter", "DE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON> til tekst", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Applikasjon", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "Kommentar", "DE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Opprettet", "DE.Views.FileMenuPanels.DocumentInfo.txtDocumentInfo": "Dokumentinformasjon", "DE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "Fast web view", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "Laster...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Last modified by", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Last Modified", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "<PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "Avsnitt", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "PDF Producer", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "Tagged PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "PDF Version", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Plassering", "DE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "DE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "Persons who have rights", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "Characters with spaces", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "Statistics", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subject", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "Symboler", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Title", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Lastet opp", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "Words", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "<PERSON>a", "DE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Tilgangsrettigheter", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "Persons who have rights", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "With password", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "Protect Document", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "Med signatur", "DE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the document.<br>The document is protected from editing.", "DE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the document by adding an<br>invisible digital signature", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Rediger dokument", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Editing will remove signatures from the document.<br>Continue?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "This document has been protected with password", "DE.Views.FileMenuPanels.ProtectDoc.txtProtectDocument": "Encrypt this document with a password", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "This document needs to be signed.", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Valid signatures have been added to the document. The document is protected from editing.", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Some of the digital signatures in the document are invalid or could not be verified. The document is protected from editing.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "Vis signaturer", "DE.Views.FileMenuPanels.Settings.okButtonText": "Bruk", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "Co-editing mode", "DE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strFontRender": "Font hinting", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignore words in UPPERCASE", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignore words with numbers", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "Macros settings", "DE.Views.FileMenuPanels.Settings.strPasteButton": "Show the Paste Options button when the content is pasted", "DE.Views.FileMenuPanels.Settings.strRTLSupport": "RTL interface", "DE.Views.FileMenuPanels.Settings.strShowChanges": "Real-time collaboration changes", "DE.Views.FileMenuPanels.Settings.strShowComments": "Show comments in text", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Show changes from other users", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "Show resolved comments", "DE.Views.FileMenuPanels.Settings.strStrict": "Strict", "DE.Views.FileMenuPanels.Settings.strTabStyle": "Tab style", "DE.Views.FileMenuPanels.Settings.strTheme": "Interface theme", "DE.Views.FileMenuPanels.Settings.strUnit": "Unit of measurement", "DE.Views.FileMenuPanels.Settings.strZoom": "Standard zoom-verdi", "DE.Views.FileMenuPanels.Settings.text10Minutes": "Hvert 10. minutt", "DE.Views.FileMenuPanels.Settings.text30Minutes": "H<PERSON> 30. minutt", "DE.Views.FileMenuPanels.Settings.text5Minutes": "Hvert 5. minutt", "DE.Views.FileMenuPanels.Settings.text60Minutes": "Hver time", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "Automatisk gjenoppretting", "DE.Views.FileMenuPanels.Settings.textAutoSave": "Autolagre", "DE.Views.FileMenuPanels.Settings.textDisabled": "Deaktivert", "DE.Views.FileMenuPanels.Settings.textFill": "Fill", "DE.Views.FileMenuPanels.Settings.textForceSave": "Saving intermediate versions", "DE.Views.FileMenuPanels.Settings.textLine": "Line", "DE.Views.FileMenuPanels.Settings.textMinute": "<PERSON><PERSON> minutt", "DE.Views.FileMenuPanels.Settings.textOldVersions": "Make the files compatible with older MS Word versions when saved as DOCX, DOTX", "DE.Views.FileMenuPanels.Settings.textSmartSelection": "Use smart paragraph selection", "DE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "Avanserte innstillinger", "DE.Views.FileMenuPanels.Settings.txtAll": "Vis alt", "DE.Views.FileMenuPanels.Settings.txtAppearance": "Utseende", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Alternativer for autokorrektur...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "Default cache mode", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "Show by click in balloons", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "Show by hover in tooltips", "DE.Views.FileMenuPanels.Settings.txtCm": "Centimeter", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "Collaboration", "DE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "Customize quick access", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "Turn on document dark mode", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "Editing and saving", "DE.Views.FileMenuPanels.Settings.txtFastTip": "Real-time co-editing. All changes are saved automatically", "DE.Views.FileMenuPanels.Settings.txtFitPage": "Fit to page", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "Fit to width", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Hieroglyphs", "DE.Views.FileMenuPanels.Settings.txtInch": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtLast": "View last", "DE.Views.FileMenuPanels.Settings.txtLastUsed": "Last used", "DE.Views.FileMenuPanels.Settings.txtMac": "som OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "Native", "DE.Views.FileMenuPanels.Settings.txtNone": "View none", "DE.Views.FileMenuPanels.Settings.txtProofing": "Proofing", "DE.Views.FileMenuPanels.Settings.txtPt": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "Show the Quick Print button in the editor header", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "Enable all", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Aktiver alle makroer uten varsling", "DE.Views.FileMenuPanels.Settings.txtScreenReader": "Turn on screen reader support", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "Show track changes", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "Spell checking", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "Deaktiver alt", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Deaktiver alle makroer uten varsling", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "Use the \"Save\" button to sync the changes you and others make", "DE.Views.FileMenuPanels.Settings.txtTabBack": "Use toolbar color as tabs background", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "<PERSON><PERSON> var<PERSON>ling", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Deaktiver alle makroer med et varsel", "DE.Views.FileMenuPanels.Settings.txtWin": "som Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "Workspace", "DE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Last ned som", "DE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save copy as", "DE.Views.FormSettings.textAlways": "Alltid", "DE.Views.FormSettings.textAnyone": "H<PERSON>msomhel<PERSON>", "DE.Views.FormSettings.textAspect": "Lock aspect ratio", "DE.Views.FormSettings.textAtLeast": "Minst", "DE.Views.FormSettings.textAuto": "Auto", "DE.Views.FormSettings.textAutofit": "Auto-tilpass", "DE.Views.FormSettings.textBackgroundColor": "Bakgrunnsfarge", "DE.Views.FormSettings.textCheckbox": "Checkbox", "DE.Views.FormSettings.textCheckDefault": "Checkbox is checked by default", "DE.Views.FormSettings.textColor": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textComb": "Comb of characters", "DE.Views.FormSettings.textCombobox": "Combo box", "DE.Views.FormSettings.textComplex": "Complex field", "DE.Views.FormSettings.textConnected": "Fields connected", "DE.Views.FormSettings.textCreditCard": "Credit card number (e.g 4111-1111-1111-1111)", "DE.Views.FormSettings.textDateField": "Date & time field", "DE.Views.FormSettings.textDateFormat": "Display the date like this", "DE.Views.FormSettings.textDefValue": "Default value", "DE.Views.FormSettings.textDelete": "<PERSON><PERSON>", "DE.Views.FormSettings.textDigits": "Digits", "DE.Views.FormSettings.textDisconnect": "Disconnect", "DE.Views.FormSettings.textDropDown": "Dropdown", "DE.Views.FormSettings.textExact": "Exactly", "DE.Views.FormSettings.textField": "Text field", "DE.Views.FormSettings.textFillRoles": "Who needs to fill this out?", "DE.Views.FormSettings.textFixed": "Fixed size field", "DE.Views.FormSettings.textFormat": "Format", "DE.Views.FormSettings.textFormatSymbols": "<PERSON>ate symboler", "DE.Views.FormSettings.textFromFile": "From file", "DE.Views.FormSettings.textFromStorage": "From storage", "DE.Views.FormSettings.textFromUrl": "From URL", "DE.Views.FormSettings.textGroupKey": "Group key", "DE.Views.FormSettings.textImage": "<PERSON><PERSON>", "DE.Views.FormSettings.textKey": "Key", "DE.Views.FormSettings.textLang": "Language", "DE.Views.FormSettings.textLetters": "Letters", "DE.Views.FormSettings.textLock": "Lock", "DE.Views.FormSettings.textMask": "<PERSON><PERSON><PERSON><PERSON> maske", "DE.Views.FormSettings.textMaxChars": "Characters limit", "DE.Views.FormSettings.textMulti": "Multiline field", "DE.Views.FormSettings.textNever": "Never", "DE.Views.FormSettings.textNoBorder": "Ingen kantlinje", "DE.Views.FormSettings.textNone": "None", "DE.Views.FormSettings.textPhone1": "Phone Number (e.g. (*************)", "DE.Views.FormSettings.textPhone2": "Phone Number (e.g. +447911123456)", "DE.Views.FormSettings.textPlaceholder": "Placeholder", "DE.Views.FormSettings.textRadiobox": "Radio button", "DE.Views.FormSettings.textRadioChoice": "Radio button choice", "DE.Views.FormSettings.textRadioDefault": "But<PERSON> is checked by default", "DE.Views.FormSettings.textReg": "Regular expression", "DE.Views.FormSettings.textRequired": "Obligatorisk", "DE.Views.FormSettings.textScale": "When to scale", "DE.Views.FormSettings.textSelectImage": "Select Image", "DE.Views.FormSettings.textSignature": "Signature", "DE.Views.FormSettings.textTag": "Tag", "DE.Views.FormSettings.textTip": "Tip", "DE.Views.FormSettings.textTipAdd": "Legg til ny verdi", "DE.Views.FormSettings.textTipDelete": "Delete value", "DE.Views.FormSettings.textTipDown": "Move down", "DE.Views.FormSettings.textTipUp": "Move up", "DE.Views.FormSettings.textTooBig": "Image is too big", "DE.Views.FormSettings.textTooSmall": "Image is too small", "DE.Views.FormSettings.textUKPassport": "UK Passport number (e.g. *********)", "DE.Views.FormSettings.textUnlock": "Unlock", "DE.Views.FormSettings.textUSSSN": "US SSN (e.g. ***********)", "DE.Views.FormSettings.textValue": "Value options", "DE.Views.FormSettings.textWidth": "Cell width", "DE.Views.FormSettings.textZipCodeUS": "US ZIP Code (e.g. 92663 or 92663-1234)", "DE.Views.FormsTab.capBtnCheckBox": "Checkbox", "DE.Views.FormsTab.capBtnComboBox": "Combo Box", "DE.Views.FormsTab.capBtnComplex": "Complex Field", "DE.Views.FormsTab.capBtnDownloadForm": "Download As PDF", "DE.Views.FormsTab.capBtnDropDown": "Dropdown", "DE.Views.FormsTab.capBtnEmail": "E-postadresse", "DE.Views.FormsTab.capBtnImage": "<PERSON><PERSON>", "DE.Views.FormsTab.capBtnManager": "Manage Roles", "DE.Views.FormsTab.capBtnNext": "Next Field", "DE.Views.FormsTab.capBtnPhone": "Phone Number", "DE.Views.FormsTab.capBtnPrev": "Previous Field", "DE.Views.FormsTab.capBtnRadioBox": "Radio Button", "DE.Views.FormsTab.capBtnSaveForm": "Save As PDF", "DE.Views.FormsTab.capBtnSaveFormDesktop": "Save As...", "DE.Views.FormsTab.capBtnSignature": "Signature Field", "DE.Views.FormsTab.capBtnSubmit": "Complete & Submit", "DE.Views.FormsTab.capBtnText": "Text Field", "DE.Views.FormsTab.capBtnView": "View Form", "DE.Views.FormsTab.capCreditCard": "Credit Card", "DE.Views.FormsTab.capDateTime": "Date & Time", "DE.Views.FormsTab.capZipCode": "ZIP Code", "DE.Views.FormsTab.helpTextFillStatus": "This form is ready for role-based filling. Click on the status button to check the filling stage.", "DE.Views.FormsTab.textAnyone": "H<PERSON>msomhel<PERSON>", "DE.Views.FormsTab.textClear": "Clear Fields", "DE.Views.FormsTab.textClearFields": "Clear All Fields", "DE.Views.FormsTab.textCreateForm": "Legg til felt og lag et utfyllbart PDF-dokument", "DE.Views.FormsTab.textFilled": "Filled", "DE.Views.FormsTab.textGotIt": "Got it", "DE.Views.FormsTab.textHighlight": "Highlight Settings", "DE.Views.FormsTab.textNoHighlight": "No highlighting", "DE.Views.FormsTab.textRequired": "To submit the form, you must fill in all required fields", "DE.Views.FormsTab.textSubmited": "Form submitted successfully", "DE.Views.FormsTab.textSubmitOk": "Your PDF form has been saved in the Complete section. You can fill out this form again and send another result.", "DE.Views.FormsTab.tipCheckBox": "Insert checkbox", "DE.Views.FormsTab.tipComboBox": "Insert combo box", "DE.Views.FormsTab.tipComplexField": "Insert complex field", "DE.Views.FormsTab.tipCreateField": "To create a field select the desired field type on the toolbar and click on it. The field will appear in the document.", "DE.Views.FormsTab.tipCreditCard": "Insert credit card number", "DE.Views.FormsTab.tipDateTime": "Insert date and time", "DE.Views.FormsTab.tipDownloadForm": "Download a file as a fillable PDF", "DE.Views.FormsTab.tipDropDown": "Insert dropdown list", "DE.Views.FormsTab.tipEmailField": "Insert email address", "DE.Views.FormsTab.tipFieldSettings": "You can configure selected fields on the right sidebar. Click this icon to open the field settings.", "DE.Views.FormsTab.tipFieldsLink": "Learn more about field parameters", "DE.Views.FormsTab.tipFirstPage": "Go to the first page", "DE.Views.FormsTab.tipFixedText": "Insert fixed text field", "DE.Views.FormsTab.tipFormGroupKey": "Group radio buttons to make the filling process faster. Choices with the same names will be synchronized. Users can only tick one radio button from the group.", "DE.Views.FormsTab.tipFormKey": "You can assign a key to a field or a group of fields. When a user fills in the data, it will be copied to all the fields with the same key.", "DE.Views.FormsTab.tipHelpRoles": "Use the Manage Roles feature to group fields by purpose and assign the responsible team members.", "DE.Views.FormsTab.tipImageField": "Sett inn bilde", "DE.Views.FormsTab.tipInlineText": "Insert inline text field", "DE.Views.FormsTab.tipLastPage": "Go to the last page", "DE.Views.FormsTab.tipManager": "Manage roles", "DE.Views.FormsTab.tipNextForm": "Go to the next field", "DE.Views.FormsTab.tipNextPage": "Go to the next page", "DE.Views.FormsTab.tipPhoneField": "Insert phone number", "DE.Views.FormsTab.tipPrevForm": "Go to the previous field", "DE.Views.FormsTab.tipPrevPage": "Go to the previous page", "DE.Views.FormsTab.tipRadioBox": "Insert radio button", "DE.Views.FormsTab.tipRolesLink": "Learn more about roles", "DE.Views.FormsTab.tipSaveFile": "Click \"Save As PDF\" to save the form in the format ready for filling.", "DE.Views.FormsTab.tipSaveForm": "Save a file as a fillable PDF", "DE.Views.FormsTab.tipSignField": "Insert signature field", "DE.Views.FormsTab.tipSubmit": "Submit form", "DE.Views.FormsTab.tipTextField": "Insert text field", "DE.Views.FormsTab.tipViewForm": "View form", "DE.Views.FormsTab.tipZipCode": "Insert ZIP code", "DE.Views.FormsTab.txtFixedDesc": "Insert fixed text field", "DE.Views.FormsTab.txtFixedText": "Fixed", "DE.Views.FormsTab.txtInlineDesc": "Insert inline text field", "DE.Views.FormsTab.txtInlineText": "Inline", "DE.Views.FormsTab.txtUntitled": "Untitled", "DE.Views.HeaderFooterSettings.textBottomCenter": "<PERSON><PERSON><PERSON> senter", "DE.Views.HeaderFooterSettings.textBottomLeft": "<PERSON><PERSON> bunn", "DE.Views.HeaderFooterSettings.textBottomPage": "Nederst på siden", "DE.Views.HeaderFooterSettings.textBottomRight": "<PERSON><PERSON><PERSON>ø<PERSON>", "DE.Views.HeaderFooterSettings.textDiffFirst": "Ulik førsteside", "DE.Views.HeaderFooterSettings.textDiffOdd": "<PERSON><PERSON> odde- og parsider", "DE.Views.HeaderFooterSettings.textFrom": "Start at", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "Footer from bottom", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "Header from Top", "DE.Views.HeaderFooterSettings.textInsertCurrent": "Insert to current position", "DE.Views.HeaderFooterSettings.textNumFormat": "Number format", "DE.Views.HeaderFooterSettings.textOptions": "Options", "DE.Views.HeaderFooterSettings.textPageNum": "Insert page number", "DE.Views.HeaderFooterSettings.textPageNumbering": "Page numbering", "DE.Views.HeaderFooterSettings.textPosition": "Posisjon", "DE.Views.HeaderFooterSettings.textPrev": "Continue from previous section", "DE.Views.HeaderFooterSettings.textSameAs": "Link to Previous", "DE.Views.HeaderFooterSettings.textTopCenter": "Top center", "DE.Views.HeaderFooterSettings.textTopLeft": "Top left", "DE.Views.HeaderFooterSettings.textTopPage": "Top of page", "DE.Views.HeaderFooterSettings.textTopRight": "Top right", "DE.Views.HeaderFooterSettings.txtMoreTypes": "More types", "DE.Views.HyperlinkSettingsDialog.textDefault": "Selected text fragment", "DE.Views.HyperlinkSettingsDialog.textDisplay": "Vis", "DE.Views.HyperlinkSettingsDialog.textExternal": "Ekstern lenke", "DE.Views.HyperlinkSettingsDialog.textInternal": "Place in document", "DE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "DE.Views.HyperlinkSettingsDialog.textTitle": "Hyperlink settings", "DE.Views.HyperlinkSettingsDialog.textTooltip": "ScreenTip text", "DE.Views.HyperlinkSettingsDialog.textUrl": "Link to", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "Starten av dokumentet", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "Bokmerker", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "This field is required", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "Headings", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "This field should be a URL in the \"http://www.example.com\" format", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "This field is limited to 2083 characters", "DE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "DE.Views.HyphenationDialog.textAuto": "<PERSON>gg til bindestreker automatisk", "DE.Views.HyphenationDialog.textCaps": "Hyphenate words in CAPS", "DE.Views.HyphenationDialog.textLimit": "Limit consecutive hyphens to", "DE.Views.HyphenationDialog.textNoLimit": "No limit", "DE.Views.HyphenationDialog.textTitle": "Hyphenation", "DE.Views.HyphenationDialog.textZone": "Hyphenation zone", "DE.Views.ImageSettings.strTransparency": "Opacity", "DE.Views.ImageSettings.textAdvanced": "Show advanced settings", "DE.Views.ImageSettings.textCrop": "Beskjære", "DE.Views.ImageSettings.textCropFill": "Fill", "DE.Views.ImageSettings.textCropFit": "Fit", "DE.Views.ImageSettings.textCropToShape": "Crop to shape", "DE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "DE.Views.ImageSettings.textEditObject": "Rediger objekt", "DE.Views.ImageSettings.textFitMargins": "Fit to margin", "DE.Views.ImageSettings.textFlip": "Flip", "DE.Views.ImageSettings.textFromFile": "From file", "DE.Views.ImageSettings.textFromStorage": "From storage", "DE.Views.ImageSettings.textFromUrl": "From URL", "DE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textHint270": "Rotate 90В° Counterclockwise", "DE.Views.ImageSettings.textHint90": "Rotate 90В° Clockwise", "DE.Views.ImageSettings.textHintFlipH": "Flip horizontally", "DE.Views.ImageSettings.textHintFlipV": "Flip vertically", "DE.Views.ImageSettings.textInsert": "Replace Image", "DE.Views.ImageSettings.textOriginalSize": "Standard størrelse", "DE.Views.ImageSettings.textRecentlyUsed": "Recently used", "DE.Views.ImageSettings.textResetCrop": "Reset crop", "DE.Views.ImageSettings.textRotate90": "Rotate 90В°", "DE.Views.ImageSettings.textRotation": "Rotation", "DE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textWidth": "Bredde", "DE.Views.ImageSettings.textWrap": "Wrapping Style", "DE.Views.ImageSettings.txtBehind": "Bak", "DE.Views.ImageSettings.txtInFront": "<PERSON>an", "DE.Views.ImageSettings.txtInline": "In line with text", "DE.Views.ImageSettings.txtSquare": "Square", "DE.Views.ImageSettings.txtThrough": "Gjennom", "DE.Views.ImageSettings.txtTight": "<PERSON><PERSON>", "DE.Views.ImageSettings.txtTopAndBottom": "<PERSON><PERSON> og bunn", "DE.Views.ImageSettingsAdvanced.strMargins": "Text padding", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAlignment": "Opps<PERSON>ling", "DE.Views.ImageSettingsAdvanced.textAlt": "Alternativ tekst", "DE.Views.ImageSettingsAdvanced.textAltDescription": "Beskrivelse", "DE.Views.ImageSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart, or table.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "Title", "DE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textArrows": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "Lock aspect ratio", "DE.Views.ImageSettingsAdvanced.textAutofit": "Autotilpass", "DE.Views.ImageSettingsAdvanced.textBeginSize": "Startstørrelse", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "Startstil", "DE.Views.ImageSettingsAdvanced.textBelow": "under", "DE.Views.ImageSettingsAdvanced.textBevel": "Skråkant", "DE.Views.ImageSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "<PERSON><PERSON> bunn", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "Text wrapping", "DE.Views.ImageSettingsAdvanced.textCapType": "Cap Type", "DE.Views.ImageSettingsAdvanced.textCenter": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textCharacter": "Tegn", "DE.Views.ImageSettingsAdvanced.textColumn": "Kolonne", "DE.Views.ImageSettingsAdvanced.textDistance": "Distance from text", "DE.Views.ImageSettingsAdvanced.textEndSize": "End size", "DE.Views.ImageSettingsAdvanced.textEndStyle": "End style", "DE.Views.ImageSettingsAdvanced.textFlat": "Flat", "DE.Views.ImageSettingsAdvanced.textFlipped": "Flipped", "DE.Views.ImageSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontally", "DE.Views.ImageSettingsAdvanced.textJoinType": "Join type", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "Konstant størrelsesforhold", "DE.Views.ImageSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "Left margin", "DE.Views.ImageSettingsAdvanced.textLine": "Line", "DE.Views.ImageSettingsAdvanced.textLineStyle": "Line style", "DE.Views.ImageSettingsAdvanced.textMargin": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMiter": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMove": "Move object with text", "DE.Views.ImageSettingsAdvanced.textOptions": "Options", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "Standard størrelse", "DE.Views.ImageSettingsAdvanced.textOverlap": "Tillat overlapping", "DE.Views.ImageSettingsAdvanced.textPage": "Side", "DE.Views.ImageSettingsAdvanced.textParagraph": "Avsnitt", "DE.Views.ImageSettingsAdvanced.textPosition": "Posisjon", "DE.Views.ImageSettingsAdvanced.textPositionPc": "Relative position", "DE.Views.ImageSettingsAdvanced.textRelative": "relative to", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "Relative", "DE.Views.ImageSettingsAdvanced.textResizeFit": "Resize shape to fit text", "DE.Views.ImageSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textRightMargin": "Right margin", "DE.Views.ImageSettingsAdvanced.textRightOf": "to the right of", "DE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "DE.Views.ImageSettingsAdvanced.textRound": "Round", "DE.Views.ImageSettingsAdvanced.textShape": "Shape settings", "DE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textSquare": "Square", "DE.Views.ImageSettingsAdvanced.textTextBox": "Text box", "DE.Views.ImageSettingsAdvanced.textTitle": "Image - advanced settings", "DE.Views.ImageSettingsAdvanced.textTitleChart": "Diagram - Avanserte innstillinger", "DE.Views.ImageSettingsAdvanced.textTitleShape": "Shape - advanced settings", "DE.Views.ImageSettingsAdvanced.textTop": "Topp", "DE.Views.ImageSettingsAdvanced.textTopMargin": "Top margin", "DE.Views.ImageSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "Weights & Arrows", "DE.Views.ImageSettingsAdvanced.textWidth": "Bredde", "DE.Views.ImageSettingsAdvanced.textWrap": "Wrapping style", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "Bak", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "<PERSON>an", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "In line with text", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "Square", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "Gjennom", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "<PERSON><PERSON> og bunn", "DE.Views.LeftMenu.ariaLeftMenu": "Left menu", "DE.Views.LeftMenu.tipAbout": "Om", "DE.Views.LeftMenu.tipChat": "Cha<PERSON>", "DE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.tipNavigation": "Navigasjon", "DE.Views.LeftMenu.tipOutline": "Headings", "DE.Views.LeftMenu.tipPageThumbnails": "Page thumbnails", "DE.Views.LeftMenu.tipPlugins": "Plugins", "DE.Views.LeftMenu.tipSearch": "Find", "DE.Views.LeftMenu.tipSupport": "Tilbakemelding og støtte", "DE.Views.LeftMenu.tipTitles": "Titles", "DE.Views.LeftMenu.txtDeveloper": "UTVIKLERMODUS", "DE.Views.LeftMenu.txtEditor": "Document Editor", "DE.Views.LeftMenu.txtLimit": "Limit access", "DE.Views.LeftMenu.txtTrial": "TRIAL MODE", "DE.Views.LeftMenu.txtTrialDev": "Trial Developer Mode", "DE.Views.LineNumbersDialog.textAddLineNumbering": "Legg til linjenummerering", "DE.Views.LineNumbersDialog.textApplyTo": "Bruk endringer på", "DE.Views.LineNumbersDialog.textContinuous": "Continuous", "DE.Views.LineNumbersDialog.textCountBy": "Count by", "DE.Views.LineNumbersDialog.textDocument": "Whole document", "DE.Views.LineNumbersDialog.textForward": "This point forward", "DE.Views.LineNumbersDialog.textFromText": "From text", "DE.Views.LineNumbersDialog.textNumbering": "Numbering", "DE.Views.LineNumbersDialog.textRestartEachPage": "Restart each page", "DE.Views.LineNumbersDialog.textRestartEachSection": "Restart each section", "DE.Views.LineNumbersDialog.textSection": "Current section", "DE.Views.LineNumbersDialog.textStartAt": "Start at", "DE.Views.LineNumbersDialog.textTitle": "Line numbers", "DE.Views.LineNumbersDialog.txtAutoText": "Auto", "DE.Views.Links.capBtnAddText": "<PERSON><PERSON> til tekst", "DE.Views.Links.capBtnBookmarks": "<PERSON>g bok<PERSON>ke", "DE.Views.Links.capBtnCaption": "Bildetekst", "DE.Views.Links.capBtnContentsUpdate": "Update Table", "DE.Views.Links.capBtnCrossRef": "Cross-reference", "DE.Views.Links.capBtnInsContents": "Innholdsfortegnelse", "DE.Views.Links.capBtnInsFootnote": "Fotnote", "DE.Views.Links.capBtnInsLink": "<PERSON><PERSON>", "DE.Views.Links.capBtnTOF": "Table of Figures", "DE.Views.Links.confirmDeleteFootnotes": "Do you want to delete all footnotes?", "DE.Views.Links.confirmReplaceTOF": "Do you want to replace the selected table of figures?", "DE.Views.Links.mniConvertNote": "Convert all notes", "DE.Views.Links.mniDelFootnote": "<PERSON><PERSON> alle fotnoter", "DE.Views.Links.mniInsEndnote": "Insert endnote", "DE.Views.Links.mniInsFootnote": "Insert footnote", "DE.Views.Links.mniNoteSettings": "Notes settings", "DE.Views.Links.textContentsRemove": "Remove table of contents", "DE.Views.Links.textContentsSettings": "Innstillinger", "DE.Views.Links.textConvertToEndnotes": "Convert all footnotes to endnotes", "DE.Views.Links.textConvertToFootnotes": "Convert all endnotes to footnotes", "DE.Views.Links.textGotoEndnote": "Go to endnotes", "DE.Views.Links.textGotoFootnote": "Go to footnotes", "DE.Views.Links.textSwapNotes": "Swap footnotes and endnotes", "DE.Views.Links.textUpdateAll": "Update entire table", "DE.Views.Links.textUpdatePages": "Update page numbers only", "DE.Views.Links.tipAddText": "Include heading in the table of contents", "DE.Views.Links.tipBookmarks": "<PERSON><PERSON><PERSON><PERSON> et bokmerke", "DE.Views.Links.tipCaption": "Insert caption", "DE.Views.Links.tipContents": "Insert table of contents", "DE.Views.Links.tipContentsUpdate": "Update table of contents", "DE.Views.Links.tipCrossRef": "Insert cross-reference", "DE.Views.Links.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON><PERSON> lenke", "DE.Views.Links.tipNotes": "Insert or edit footnotes", "DE.Views.Links.tipTableFigures": "Insert table of figures", "DE.Views.Links.tipTableFiguresUpdate": "Update table of figures", "DE.Views.Links.titleUpdateTOF": "Update table of figures", "DE.Views.Links.txtDontShowTof": "Do not show in table of contents", "DE.Views.Links.txtLevel": "Level", "DE.Views.ListIndentsDialog.textSpace": "Space", "DE.Views.ListIndentsDialog.textTab": "Tab character", "DE.Views.ListIndentsDialog.textTitle": "List Indents", "DE.Views.ListIndentsDialog.txtFollowBullet": "Follow bullet with", "DE.Views.ListIndentsDialog.txtFollowNumber": "Follow number with", "DE.Views.ListIndentsDialog.txtIndent": "Text indent", "DE.Views.ListIndentsDialog.txtNone": "None", "DE.Views.ListIndentsDialog.txtPosBullet": "Bullet position", "DE.Views.ListIndentsDialog.txtPosNumber": "Number position", "DE.Views.ListSettingsDialog.textAuto": "Automatisk", "DE.Views.ListSettingsDialog.textBold": "Fet", "DE.Views.ListSettingsDialog.textCenter": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.textHide": "Hide settings", "DE.Views.ListSettingsDialog.textItalic": "Italic", "DE.Views.ListSettingsDialog.textLeft": "Left", "DE.Views.ListSettingsDialog.textLevel": "Level", "DE.Views.ListSettingsDialog.textMore": "Show more settings", "DE.Views.ListSettingsDialog.textPreview": "Forhåndsvisning", "DE.Views.ListSettingsDialog.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.textSelectLevel": "Select level", "DE.Views.ListSettingsDialog.textSpace": "Space", "DE.Views.ListSettingsDialog.textTab": "Tab character", "DE.Views.ListSettingsDialog.txtAlign": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtAlignAt": "ved", "DE.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtFollow": "Follow number with", "DE.Views.ListSettingsDialog.txtFontName": "Font", "DE.Views.ListSettingsDialog.txtInclcudeLevel": "Include level number", "DE.Views.ListSettingsDialog.txtIndent": "Text indent", "DE.Views.ListSettingsDialog.txtLikeText": "Like a text", "DE.Views.ListSettingsDialog.txtMoreTypes": "More types", "DE.Views.ListSettingsDialog.txtNewBullet": "New bullet", "DE.Views.ListSettingsDialog.txtNone": "None", "DE.Views.ListSettingsDialog.txtNumFormatString": "Number format", "DE.Views.ListSettingsDialog.txtRestart": "Restart list", "DE.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtStart": "Start at", "DE.Views.ListSettingsDialog.txtSymbol": "Symbol", "DE.Views.ListSettingsDialog.txtTabStop": "Legg til innrykk ved", "DE.Views.ListSettingsDialog.txtTitle": "List settings", "DE.Views.ListSettingsDialog.txtType": "Type", "DE.Views.ListTypesAdvanced.labelSelect": "Select list type", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "Send", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textAttachDocx": "Legg ved som DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "Legg ved som PDF", "DE.Views.MailMergeEmailDlg.textFileName": "Filnavn", "DE.Views.MailMergeEmailDlg.textFormat": "Mail format", "DE.Views.MailMergeEmailDlg.textFrom": "<PERSON>a", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "Message", "DE.Views.MailMergeEmailDlg.textSubject": "Subject line", "DE.Views.MailMergeEmailDlg.textTitle": "Send to email", "DE.Views.MailMergeEmailDlg.textTo": "To", "DE.Views.MailMergeEmailDlg.textWarning": "Warning!", "DE.Views.MailMergeEmailDlg.textWarningMsg": "Please note that mailing cannot be stopped once your click the 'Send' button.", "DE.Views.MailMergeSettings.downloadMergeTitle": "Merging", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "Me<PERSON> failed.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.textAddRecipients": "<PERSON><PERSON> til mottakere til listen først", "DE.Views.MailMergeSettings.textAll": "<PERSON>e oppføringer", "DE.Views.MailMergeSettings.textCurrent": "Gjeldende registrering", "DE.Views.MailMergeSettings.textDataSource": "Datakilde", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "Last ned", "DE.Views.MailMergeSettings.textEditData": "<PERSON><PERSON> mott<PERSON>e", "DE.Views.MailMergeSettings.textEmail": "Email", "DE.Views.MailMergeSettings.textFrom": "<PERSON>a", "DE.Views.MailMergeSettings.textGoToMail": "Go to Mail", "DE.Views.MailMergeSettings.textHighlight": "Highlight merge fields", "DE.Views.MailMergeSettings.textInsertField": "Insert merge field", "DE.Views.MailMergeSettings.textMaxRecepients": "Max 100 recipients.", "DE.Views.MailMergeSettings.textMerge": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textMergeFields": "Merge fields", "DE.Views.MailMergeSettings.textMergeTo": "Merge to", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "Lagre", "DE.Views.MailMergeSettings.textPreview": "Preview results", "DE.Views.MailMergeSettings.textReadMore": "Les mer", "DE.Views.MailMergeSettings.textSendMsg": "Alle meldinger er klargjort og vil bli sendt innen kort tid.<br>Dette vil avhenge av hastigheten på din epost-tjener.<br>Du kan fortsette å jobbe med dokumentet eller lukke det. Når oppgaven er fullført vil du få en melding om dette på din registrerte epost-adresse.", "DE.Views.MailMergeSettings.textTo": "To", "DE.Views.MailMergeSettings.txtFirst": "To first record", "DE.Views.MailMergeSettings.txtFromToError": "\"Fra\"-verdien må være mindre enn \"Til\"-verdien", "DE.Views.MailMergeSettings.txtLast": "To last record", "DE.Views.MailMergeSettings.txtNext": "To next record", "DE.Views.MailMergeSettings.txtPrev": "To previous record", "DE.Views.MailMergeSettings.txtUntitled": "Untitled", "DE.Views.MailMergeSettings.warnProcessMailMerge": "Starting merge failed", "DE.Views.Navigation.strNavigate": "Headings", "DE.Views.Navigation.txtClosePanel": "Close headings", "DE.Views.Navigation.txtCollapse": "Lukk alt", "DE.Views.Navigation.txtDemote": "Degrader", "DE.Views.Navigation.txtEmpty": "There are no headings in the document.<br>Apply a heading style to the text so that it appears in the table of contents.", "DE.Views.Navigation.txtEmptyItem": "Empty heading", "DE.Views.Navigation.txtEmptyViewer": "There are no headings in the document.", "DE.Views.Navigation.txtExpand": "Expand all", "DE.Views.Navigation.txtExpandToLevel": "Expand to level", "DE.Views.Navigation.txtFontSize": "Font size", "DE.Views.Navigation.txtHeadingAfter": "New heading after", "DE.Views.Navigation.txtHeadingBefore": "New heading before", "DE.Views.Navigation.txtLarge": "Large", "DE.Views.Navigation.txtMedium": "Medium", "DE.Views.Navigation.txtNewHeading": "New subheading", "DE.Views.Navigation.txtPromote": "Promote", "DE.Views.Navigation.txtSelect": "Select content", "DE.Views.Navigation.txtSettings": "Headings settings", "DE.Views.Navigation.txtSmall": "Small", "DE.Views.Navigation.txtWrapHeadings": "Wrap long headings", "DE.Views.NoteSettingsDialog.textApply": "Bruk", "DE.Views.NoteSettingsDialog.textApplyTo": "Bruk endringer på", "DE.Views.NoteSettingsDialog.textContinue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textCustom": "Egendefinert markering", "DE.Views.NoteSettingsDialog.textDocEnd": "End of document", "DE.Views.NoteSettingsDialog.textDocument": "Whole document", "DE.Views.NoteSettingsDialog.textEachPage": "Restart each page", "DE.Views.NoteSettingsDialog.textEachSection": "Restart each section", "DE.Views.NoteSettingsDialog.textEndnote": "Endnote", "DE.Views.NoteSettingsDialog.textFootnote": "Fotnote", "DE.Views.NoteSettingsDialog.textFormat": "Format", "DE.Views.NoteSettingsDialog.textInsert": "Insert", "DE.Views.NoteSettingsDialog.textLocation": "Plassering", "DE.Views.NoteSettingsDialog.textNumbering": "Numbering", "DE.Views.NoteSettingsDialog.textNumFormat": "Number format", "DE.Views.NoteSettingsDialog.textPageBottom": "Nederst på siden", "DE.Views.NoteSettingsDialog.textSectEnd": "End of section", "DE.Views.NoteSettingsDialog.textSection": "Gjeldende seksjon", "DE.Views.NoteSettingsDialog.textStart": "Start at", "DE.Views.NoteSettingsDialog.textTextBottom": "Under teksten", "DE.Views.NoteSettingsDialog.textTitle": "Notes settings", "DE.Views.NotesRemoveDialog.textEnd": "Delete all endnotes", "DE.Views.NotesRemoveDialog.textFoot": "Delete all footnotes", "DE.Views.NotesRemoveDialog.textTitle": "Delete notes", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textBottom": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textGutter": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textGutterPosition": "Gutter position", "DE.Views.PageMarginsDialog.textInside": "Inside", "DE.Views.PageMarginsDialog.textLandscape": "Landskap", "DE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textMirrorMargins": "Mirror margins", "DE.Views.PageMarginsDialog.textMultiplePages": "Multiple pages", "DE.Views.PageMarginsDialog.textNormal": "Normal", "DE.Views.PageMarginsDialog.textOrientation": "Orientering", "DE.Views.PageMarginsDialog.textOutside": "Outside", "DE.Views.PageMarginsDialog.textPortrait": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textPreview": "Forhåndsvisning", "DE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTop": "Topp", "DE.Views.PageMarginsDialog.txtMarginsH": "Top and bottom margins are too high for a given page height", "DE.Views.PageMarginsDialog.txtMarginsW": "Left and right margins are too wide for a given page width", "DE.Views.PageSizeDialog.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.textPreset": "Preset", "DE.Views.PageSizeDialog.textTitle": "Page size", "DE.Views.PageSizeDialog.textWidth": "Bredde", "DE.Views.PageSizeDialog.txtCustom": "Egendefinert", "DE.Views.PageThumbnails.textClosePanel": "Close page thumbnails", "DE.Views.PageThumbnails.textHighlightVisiblePart": "Highlight visible part of page", "DE.Views.PageThumbnails.textPageThumbnails": "Page thumbnails", "DE.Views.PageThumbnails.textThumbnailsSettings": "Thumbnails settings", "DE.Views.PageThumbnails.textThumbnailsSize": "Thumbnails size", "DE.Views.ParagraphSettings.strIndent": "Indents", "DE.Views.ParagraphSettings.strIndentsLeftText": "Left", "DE.Views.ParagraphSettings.strIndentsRightText": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.strIndentsSpecial": "Special", "DE.Views.ParagraphSettings.strLineHeight": "Line spacing", "DE.Views.ParagraphSettings.strParagraphSpacing": "Paragraph spacing", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "Don't add interval between paragraphs of the same style", "DE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.textAdvanced": "Show advanced settings", "DE.Views.ParagraphSettings.textAt": "Ved", "DE.Views.ParagraphSettings.textAtLeast": "Minst", "DE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.textBackColor": "Bakgrunnsfarge", "DE.Views.ParagraphSettings.textExact": "Nøyaktig", "DE.Views.ParagraphSettings.textFirstLine": "<PERSON><PERSON><PERSON><PERSON> linje", "DE.Views.ParagraphSettings.textHanging": "Hanging", "DE.Views.ParagraphSettings.textNoneSpecial": "(ingen)", "DE.Views.ParagraphSettings.txtAutoText": "Auto", "DE.Views.ParagraphSettingsAdvanced.noTabs": "The specified tabs will appear in this field", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "Store bokstaver", "DE.Views.ParagraphSettingsAdvanced.strBorders": "Linjer & Fyll", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "Page break before", "DE.Views.ParagraphSettingsAdvanced.strDirection": "Direction", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Double strikethrough", "DE.Views.ParagraphSettingsAdvanced.strIndent": "Indents", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Line spacing", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "Outline level", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Special", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "Keep lines together", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "Keep with next", "DE.Views.ParagraphSettingsAdvanced.strMargins": "Paddings", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "Orphan control", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Font", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indents & spacing", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "Line & page breaks", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "Placement", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Small caps", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "Don't add interval between paragraphs of the same style", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "Avstand", "DE.Views.ParagraphSettingsAdvanced.strStrike": "Gjennomstreking", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "Senket skrift", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "<PERSON><PERSON> skrift", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "Suppress line numbers", "DE.Views.ParagraphSettingsAdvanced.strTabs": "Tabs", "DE.Views.ParagraphSettingsAdvanced.textAlign": "Opps<PERSON>ling", "DE.Views.ParagraphSettingsAdvanced.textAll": "Alle", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "Minst", "DE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "Bakgrunnsfarge", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "Grunnleggende tekst", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "Trykk på diagram eller bruk knappene for å velge kanter og bruke valgt stil på dem", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "Linjestørrel<PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textCentered": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Tegnavstand", "DE.Views.ParagraphSettingsAdvanced.textContext": "Contextual", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "Contextual and discretionary", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "Contextual, historical and discretionary", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "Contextual and historical", "DE.Views.ParagraphSettingsAdvanced.textDefault": "Standard fane", "DE.Views.ParagraphSettingsAdvanced.textDirLtr": "Left-to-right", "DE.Views.ParagraphSettingsAdvanced.textDirRtl": "Right-to-left", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "Discretionary", "DE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textExact": "Eøyaktig", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON><PERSON> linje", "DE.Views.ParagraphSettingsAdvanced.textHanging": "Hanging", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "Historical", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "Historical and discretionary", "DE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textLeader": "Leader", "DE.Views.ParagraphSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textLevel": "Level", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "Ligatures", "DE.Views.ParagraphSettingsAdvanced.textNone": "None", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ingen)", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "OpenType features", "DE.Views.ParagraphSettingsAdvanced.textPosition": "Posisjon", "DE.Views.ParagraphSettingsAdvanced.textRemove": "Remove", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Remove all", "DE.Views.ParagraphSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textSet": "Specify", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "Avstand", "DE.Views.ParagraphSettingsAdvanced.textStandard": "Standard only", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "Standard and contextual", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "Standard, contextual and discretionary", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "Standard, contextual and historical", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "Standard and discretionary", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "Standard, historical and discretionary", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "Standard and historical", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "Tabulator posisjon", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraph - advanced settings", "DE.Views.ParagraphSettingsAdvanced.textTop": "Topp", "DE.Views.ParagraphSettingsAdvanced.tipAll": "Set outer border and all inner lines", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "Set bottom border only", "DE.Views.ParagraphSettingsAdvanced.tipInner": "Set horizontal inner lines only", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "Set left border only", "DE.Views.ParagraphSettingsAdvanced.tipNone": "Set no borders", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "Set outer border only", "DE.Views.ParagraphSettingsAdvanced.tipRight": "Set right border only", "DE.Views.ParagraphSettingsAdvanced.tipTop": "Set top border only", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "No borders", "DE.Views.PrintWithPreview.textMarginsLast": "Last Custom", "DE.Views.PrintWithPreview.textMarginsModerate": "Moderate", "DE.Views.PrintWithPreview.textMarginsNarrow": "<PERSON>rrow", "DE.Views.PrintWithPreview.textMarginsNormal": "Normal", "DE.Views.PrintWithPreview.textMarginsWide": "Wide", "DE.Views.PrintWithPreview.txtAllPages": "Alle sider", "DE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "DE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "DE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "DE.Views.PrintWithPreview.txtBottom": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtCopies": "Copies", "DE.Views.PrintWithPreview.txtCurrentPage": "Current page", "DE.Views.PrintWithPreview.txtCustom": "Egendefinert", "DE.Views.PrintWithPreview.txtCustomPages": "Custom print", "DE.Views.PrintWithPreview.txtLandscape": "Landskap", "DE.Views.PrintWithPreview.txtLeft": "Left", "DE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtOf": "of {0}", "DE.Views.PrintWithPreview.txtOneSide": "Print one sided", "DE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "DE.Views.PrintWithPreview.txtPage": "Side", "DE.Views.PrintWithPreview.txtPageNumInvalid": "Page number invalid", "DE.Views.PrintWithPreview.txtPageOrientation": "Page orientation", "DE.Views.PrintWithPreview.txtPages": "<PERSON><PERSON>", "DE.Views.PrintWithPreview.txtPageSize": "Page size", "DE.Views.PrintWithPreview.txtPortrait": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtPrint": "Skriv ut", "DE.Views.PrintWithPreview.txtPrintPdf": "Print to PDF", "DE.Views.PrintWithPreview.txtPrintRange": "Print range", "DE.Views.PrintWithPreview.txtPrintSides": "Print sides", "DE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtSelection": "Selection", "DE.Views.PrintWithPreview.txtTop": "Topp", "DE.Views.ProtectDialog.textComments": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ProtectDialog.textForms": "Filling forms", "DE.Views.ProtectDialog.textReview": "Tracked changes", "DE.Views.ProtectDialog.textView": "No changes (Read only)", "DE.Views.ProtectDialog.txtAllow": "Tillat kun denne redigeringstypen i dokumentet", "DE.Views.ProtectDialog.txtIncorrectPwd": "Confirmation password is not identical", "DE.Views.ProtectDialog.txtLimit": "Password is limited to 15 characters", "DE.Views.ProtectDialog.txtOptional": "optional", "DE.Views.ProtectDialog.txtPassword": "Passord", "DE.Views.ProtectDialog.txtProtect": "Protect", "DE.Views.ProtectDialog.txtRepeat": "Repeat password", "DE.Views.ProtectDialog.txtTitle": "Protect", "DE.Views.ProtectDialog.txtWarning": "Warning: If you lose or forget the password, it cannot be recovered. Please keep it in a safe place.", "DE.Views.RightMenu.ariaRightMenu": "Right menu", "DE.Views.RightMenu.txtChartSettings": "Diagram innstillinger", "DE.Views.RightMenu.txtFormSettings": "Form settings", "DE.Views.RightMenu.txtHeaderFooterSettings": "Header and footer settings", "DE.Views.RightMenu.txtImageSettings": "Image settings", "DE.Views.RightMenu.txtMailMergeSettings": "Mail merge settings", "DE.Views.RightMenu.txtParagraphSettings": "Paragraph settings", "DE.Views.RightMenu.txtShapeSettings": "Shape settings", "DE.Views.RightMenu.txtSignatureSettings": "Signature settings", "DE.Views.RightMenu.txtTableSettings": "Table settings", "DE.Views.RightMenu.txtTextArtSettings": "Text Art settings", "DE.Views.RoleDeleteDlg.textLabel": "To delete this role, you  need to move the fields associated with it to another role.", "DE.Views.RoleDeleteDlg.textSelect": "Select for field merger role", "DE.Views.RoleDeleteDlg.textTitle": "Delete role", "DE.Views.RoleEditDlg.errNameExists": "Role with such a name already exists.", "DE.Views.RoleEditDlg.textEmptyError": "Role name must not be empty.", "DE.Views.RoleEditDlg.textName": "Role name", "DE.Views.RoleEditDlg.textNameEx": "Example: Applicant, Client, Sales Rep", "DE.Views.RoleEditDlg.textNoHighlight": "No highlighting", "DE.Views.RoleEditDlg.txtTitleEdit": "Edit Role", "DE.Views.RoleEditDlg.txtTitleNew": "Create new role", "DE.Views.RolesManagerDlg.textAnyone": "H<PERSON>msomhel<PERSON>", "DE.Views.RolesManagerDlg.textDelete": "<PERSON><PERSON>", "DE.Views.RolesManagerDlg.textDeleteLast": "<PERSON>r du sikker på at du vil fjerne rollen {0}?<br><PERSON><PERSON><PERSON> den er fjernet vil standardrollen opprettes.", "DE.Views.RolesManagerDlg.textDescription": "Legg til roller og avgjør i hvilke rekkefølge mottagerene mottar og signerer dokumentet", "DE.Views.RolesManagerDlg.textDown": "Move role down", "DE.Views.RolesManagerDlg.textEdit": "Edit", "DE.Views.RolesManagerDlg.textEmpty": "No roles have been created yet.<br>Create at least one role and it will appear in this field.", "DE.Views.RolesManagerDlg.textNew": "New", "DE.Views.RolesManagerDlg.textUp": "Move role up", "DE.Views.RolesManagerDlg.txtTitle": "Manage roles", "DE.Views.RolesManagerDlg.warnCantDelete": "You cannot delete this role because it has associated fields.", "DE.Views.RolesManagerDlg.warnDelete": "<PERSON>r du sikker på at du vil fjerne rollen {0}?", "DE.Views.SaveFormDlg.saveButtonText": "Lagre", "DE.Views.SaveFormDlg.textAnyone": "H<PERSON>msomhel<PERSON>", "DE.Views.SaveFormDlg.textDescription": "When saving to the PDF, only roles with fields are added to the filling list", "DE.Views.SaveFormDlg.textEmpty": "There are no roles associated with fields.", "DE.Views.SaveFormDlg.textFill": "Filling list", "DE.Views.SaveFormDlg.txtTitle": "Save as form", "DE.Views.ShapeSettings.strBackground": "Bakgrunnsfarge", "DE.Views.ShapeSettings.strChange": "<PERSON><PERSON> figur", "DE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "DE.Views.ShapeSettings.strFill": "Fill", "DE.Views.ShapeSettings.strForeground": "Foreground color", "DE.Views.ShapeSettings.strPattern": "Pattern", "DE.Views.ShapeSettings.strShadow": "Show shadow", "DE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strStroke": "Line", "DE.Views.ShapeSettings.strTransparency": "Opasitet", "DE.Views.ShapeSettings.strType": "Type", "DE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "DE.Views.ShapeSettings.textAdvanced": "Show advanced settings", "DE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "DE.Views.ShapeSettings.textColor": "<PERSON><PERSON><PERSON>ar<PERSON>", "DE.Views.ShapeSettings.textDirection": "Retning", "DE.Views.ShapeSettings.textEditPoints": "Edit points", "DE.Views.ShapeSettings.textEditShape": "Edit shape", "DE.Views.ShapeSettings.textEmptyPattern": "No pattern", "DE.Views.ShapeSettings.textEyedropper": "Eyedropper", "DE.Views.ShapeSettings.textFlip": "Flip", "DE.Views.ShapeSettings.textFromFile": "From file", "DE.Views.ShapeSettings.textFromStorage": "From storage", "DE.Views.ShapeSettings.textFromUrl": "From URL", "DE.Views.ShapeSettings.textGradient": "Gradient points", "DE.Views.ShapeSettings.textGradientFill": "Gradient fill", "DE.Views.ShapeSettings.textHint270": "Rotate 90В° Counterclockwise", "DE.Views.ShapeSettings.textHint90": "Rotate 90В° Clockwise", "DE.Views.ShapeSettings.textHintFlipH": "Flip horizontally", "DE.Views.ShapeSettings.textHintFlipV": "Flip vertically", "DE.Views.ShapeSettings.textImageTexture": "Picture or texture", "DE.Views.ShapeSettings.textLinear": "Linear", "DE.Views.ShapeSettings.textMoreColors": "More colors", "DE.Views.ShapeSettings.textNoFill": "No fill", "DE.Views.ShapeSettings.textNoShadow": "No Shadow", "DE.Views.ShapeSettings.textPatternFill": "Pattern", "DE.Views.ShapeSettings.textPosition": "Posisjon", "DE.Views.ShapeSettings.textRadial": "Radial", "DE.Views.ShapeSettings.textRecentlyUsed": "Recently used", "DE.Views.ShapeSettings.textRotate90": "Rotate 90В°", "DE.Views.ShapeSettings.textRotation": "Rotation", "DE.Views.ShapeSettings.textSelectImage": "Select picture", "DE.Views.ShapeSettings.textSelectTexture": "Velg", "DE.Views.ShapeSettings.textShadow": "Shadow", "DE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textStyle": "Stil", "DE.Views.ShapeSettings.textTexture": "From texture", "DE.Views.ShapeSettings.textTile": "Tile", "DE.Views.ShapeSettings.textWrap": "Wrapping Style", "DE.Views.ShapeSettings.tipAddGradientPoint": "Legg til fargeovergangspunkt", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "Remove gradient point", "DE.Views.ShapeSettings.txtBehind": "Bak", "DE.Views.ShapeSettings.txtBrownPaper": "Gråpa<PERSON>r", "DE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON><PERSON> stoff", "DE.Views.ShapeSettings.txtGrain": "Grain", "DE.Views.ShapeSettings.txtGranite": "Granite", "DE.Views.ShapeSettings.txtGreyPaper": "Gray paper", "DE.Views.ShapeSettings.txtInFront": "<PERSON>an", "DE.Views.ShapeSettings.txtInline": "In line with text", "DE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "DE.Views.ShapeSettings.txtLeather": "Leather", "DE.Views.ShapeSettings.txtNoBorders": "No line", "DE.Views.ShapeSettings.txtPapyrus": "Papyrus", "DE.Views.ShapeSettings.txtSquare": "Square", "DE.Views.ShapeSettings.txtThrough": "Gjennom", "DE.Views.ShapeSettings.txtTight": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtTopAndBottom": "<PERSON><PERSON> og bunn", "DE.Views.ShapeSettings.txtWood": "<PERSON>", "DE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Views.SignatureSettings.strDelete": "Remove Signature", "DE.Views.SignatureSettings.strDetails": "Signature details", "DE.Views.SignatureSettings.strInvalid": "Invalid signatures", "DE.Views.SignatureSettings.strRequested": "Requested signatures", "DE.Views.SignatureSettings.strSetup": "Signature setup", "DE.Views.SignatureSettings.strSign": "Sign", "DE.Views.SignatureSettings.strSignature": "Signature", "DE.Views.SignatureSettings.strSigner": "Signer", "DE.Views.SignatureSettings.strValid": "Valid signatures", "DE.Views.SignatureSettings.txtContinueEditing": "<PERSON><PERSON> allikevel", "DE.Views.SignatureSettings.txtEditWarning": "Editing will remove signatures from the document.<br>Continue?", "DE.Views.SignatureSettings.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "DE.Views.SignatureSettings.txtRequestedSignatures": "This document needs to be signed.", "DE.Views.SignatureSettings.txtSigned": "Valid signatures have been added to the document. The document is protected from editing.", "DE.Views.SignatureSettings.txtSignedInvalid": "Some of the digital signatures in the document are invalid or could not be verified. The document is protected from editing.", "DE.Views.Statusbar.goToPageText": "Go to Page", "DE.Views.Statusbar.pageIndexText": "Page {0} of {1}", "DE.Views.Statusbar.tipFitPage": "Fit to page", "DE.Views.Statusbar.tipFitWidth": "Fit to width", "DE.Views.Statusbar.tipHandTool": "Hand tool", "DE.Views.Statusbar.tipSelectTool": "Select tool", "DE.Views.Statusbar.tipSetLang": "Set text language", "DE.Views.Statusbar.tipZoomFactor": "Zoom", "DE.Views.Statusbar.tipZoomIn": "Zoom in", "DE.Views.Statusbar.tipZoomOut": "Zoom out", "DE.Views.Statusbar.txtPageNumInvalid": "Page number invalid", "DE.Views.Statusbar.txtPages": "<PERSON><PERSON>", "DE.Views.Statusbar.txtParagraphs": "Avsnitt", "DE.Views.Statusbar.txtSpaces": "Symbols with spaces", "DE.Views.Statusbar.txtSymbols": "Symbols", "DE.Views.Statusbar.txtWordCount": "Word count", "DE.Views.Statusbar.txtWords": "Words", "DE.Views.StyleTitleDialog.textHeader": "<PERSON><PERSON><PERSON><PERSON> ny stil", "DE.Views.StyleTitleDialog.textNextStyle": "Next paragraph style", "DE.Views.StyleTitleDialog.textTitle": "Title", "DE.Views.StyleTitleDialog.txtEmpty": "This field is required", "DE.Views.StyleTitleDialog.txtNotEmpty": "Feltet kan ikke være tomt", "DE.Views.StyleTitleDialog.txtSameAs": "Same as created new style", "DE.Views.TableFormulaDialog.textBookmark": "Paste bookmark", "DE.Views.TableFormulaDialog.textFormat": "Number format", "DE.Views.TableFormulaDialog.textFormula": "Formula", "DE.Views.TableFormulaDialog.textInsertFunction": "Paste function", "DE.Views.TableFormulaDialog.textTitle": "Formula settings", "DE.Views.TableOfContentsSettings.strAlign": "Right align page numbers", "DE.Views.TableOfContentsSettings.strFullCaption": "Include label and number", "DE.Views.TableOfContentsSettings.strLinks": "Format table of contents as links", "DE.Views.TableOfContentsSettings.strLinksOF": "Format table of figures as links", "DE.Views.TableOfContentsSettings.strShowPages": "Show page numbers", "DE.Views.TableOfContentsSettings.textBuildTable": "Bygg innholdsfortegnelsen fra", "DE.Views.TableOfContentsSettings.textBuildTableOF": "Build table of figures from", "DE.Views.TableOfContentsSettings.textEquation": "Equation", "DE.Views.TableOfContentsSettings.textFigure": "Figure", "DE.Views.TableOfContentsSettings.textLeader": "Leader", "DE.Views.TableOfContentsSettings.textLevel": "Level", "DE.Views.TableOfContentsSettings.textLevels": "Levels", "DE.Views.TableOfContentsSettings.textNone": "None", "DE.Views.TableOfContentsSettings.textRadioCaption": "Bildetekst", "DE.Views.TableOfContentsSettings.textRadioLevels": "Outline levels", "DE.Views.TableOfContentsSettings.textRadioStyle": "Stil", "DE.Views.TableOfContentsSettings.textRadioStyles": "Selected styles", "DE.Views.TableOfContentsSettings.textStyle": "Stil", "DE.Views.TableOfContentsSettings.textStyles": "Styles", "DE.Views.TableOfContentsSettings.textTable": "<PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textTitle": "Innholdsfortegnelse", "DE.Views.TableOfContentsSettings.textTitleTOF": "Table of figures", "DE.Views.TableOfContentsSettings.txtCentered": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.txtClassic": "Klassisk", "DE.Views.TableOfContentsSettings.txtCurrent": "Nåværende", "DE.Views.TableOfContentsSettings.txtDistinctive": "Distinctive", "DE.Views.TableOfContentsSettings.txtFormal": "Formal", "DE.Views.TableOfContentsSettings.txtModern": "Modern", "DE.Views.TableOfContentsSettings.txtOnline": "Online", "DE.Views.TableOfContentsSettings.txtSimple": "Simple", "DE.Views.TableOfContentsSettings.txtStandard": "Standard", "DE.Views.TableSettings.deleteColumnText": "Slett kolonne", "DE.Views.TableSettings.deleteRowText": "Slett rad", "DE.Views.TableSettings.deleteTableText": "<PERSON><PERSON> tabell", "DE.Views.TableSettings.insertColumnLeftText": "Insert column left", "DE.Views.TableSettings.insertColumnRightText": "Insert column right", "DE.Views.TableSettings.insertRowAboveText": "Insert row above", "DE.Views.TableSettings.insertRowBelowText": "Insert row below", "DE.Views.TableSettings.mergeCellsText": "Merge cells", "DE.Views.TableSettings.selectCellText": "Select cell", "DE.Views.TableSettings.selectColumnText": "Select column", "DE.Views.TableSettings.selectRowText": "Select row", "DE.Views.TableSettings.selectTableText": "Select table", "DE.Views.TableSettings.splitCellsText": "Split Cell...", "DE.Views.TableSettings.splitCellTitleText": "Split Cell", "DE.Views.TableSettings.strRepeatRow": "Repeat as header row at the top of each page", "DE.Views.TableSettings.textAddFormula": "<PERSON>gg til formel", "DE.Views.TableSettings.textAdvanced": "Show advanced settings", "DE.Views.TableSettings.textBackColor": "Bakgrunnsfarge", "DE.Views.TableSettings.textBanded": "Bundet", "DE.Views.TableSettings.textBorderColor": "<PERSON><PERSON>", "DE.Views.TableSettings.textBorders": "Linjestil", "DE.Views.TableSettings.textCellSize": "Cellestørrelse", "DE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textConvert": "Convert table to text", "DE.Views.TableSettings.textDistributeCols": "Distribute columns", "DE.Views.TableSettings.textDistributeRows": "<PERSON>el rader", "DE.Views.TableSettings.textEdit": "Rows & columns", "DE.Views.TableSettings.textEmptyTemplate": "No templates", "DE.Views.TableSettings.textFirst": "First", "DE.Views.TableSettings.textHeader": "Topptekst", "DE.Views.TableSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textLast": "Last", "DE.Views.TableSettings.textRows": "<PERSON><PERSON>", "DE.Views.TableSettings.textSelectBorders": "Select borders you want to change applying style chosen above", "DE.Views.TableSettings.textTemplate": "Select from template", "DE.Views.TableSettings.textTotal": "Total", "DE.Views.TableSettings.textWidth": "Bredde", "DE.Views.TableSettings.tipAll": "Set outer border and all inner lines", "DE.Views.TableSettings.tipBottom": "Set outer bottom border only", "DE.Views.TableSettings.tipInner": "Set inner lines only", "DE.Views.TableSettings.tipInnerHor": "Set horizontal inner lines only", "DE.Views.TableSettings.tipInnerVert": "Set vertical inner lines only", "DE.Views.TableSettings.tipLeft": "Set outer left border only", "DE.Views.TableSettings.tipNone": "Set no borders", "DE.Views.TableSettings.tipOuter": "Set outer border only", "DE.Views.TableSettings.tipRight": "Set outer right border only", "DE.Views.TableSettings.tipTop": "Set outer top border only", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "Innrammede og forede tabeller", "DE.Views.TableSettings.txtGroupTable_Custom": "Egendefinert", "DE.Views.TableSettings.txtGroupTable_Grid": "Grid tables", "DE.Views.TableSettings.txtGroupTable_List": "List tables", "DE.Views.TableSettings.txtGroupTable_Plain": "Plain tables", "DE.Views.TableSettings.txtNoBorders": "No borders", "DE.Views.TableSettings.txtTable_Accent": "Lesetegn", "DE.Views.TableSettings.txtTable_Bordered": "Innrammet", "DE.Views.TableSettings.txtTable_BorderedAndLined": "Innrammet og foret", "DE.Views.TableSettings.txtTable_Colorful": "Fargerik", "DE.Views.TableSettings.txtTable_Dark": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_GridTable": "Grid table", "DE.Views.TableSettings.txtTable_Light": "Light", "DE.Views.TableSettings.txtTable_Lined": "Lined", "DE.Views.TableSettings.txtTable_ListTable": "List table", "DE.Views.TableSettings.txtTable_PlainTable": "Plain table", "DE.Views.TableSettings.txtTable_TableGrid": "Table grid", "DE.Views.TableSettingsAdvanced.textAlign": "Opps<PERSON>ling", "DE.Views.TableSettingsAdvanced.textAlignment": "Opps<PERSON>ling", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "Tillat avstand mellom cellene", "DE.Views.TableSettingsAdvanced.textAlt": "Alternativ tekst", "DE.Views.TableSettingsAdvanced.textAltDescription": "Beskrivelse", "DE.Views.TableSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart, or table.", "DE.Views.TableSettingsAdvanced.textAltTitle": "Title", "DE.Views.TableSettingsAdvanced.textAnchorText": "Tekst", "DE.Views.TableSettingsAdvanced.textAutofit": "Tilpass størrelse til å automatisk passe innholdet", "DE.Views.TableSettingsAdvanced.textBackColor": "Bakgrunnsfarge", "DE.Views.TableSettingsAdvanced.textBelow": "under", "DE.Views.TableSettingsAdvanced.textBorderColor": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textBorderDesc": "Trykk på diagram eller bruk knappene for å velge kanter og bruke valgt stil på dem", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "Linjer & Bakgrunn", "DE.Views.TableSettingsAdvanced.textBorderWidth": "Linjestørrel<PERSON>", "DE.Views.TableSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCellOptions": "Celle opsjoner", "DE.Views.TableSettingsAdvanced.textCellProps": "Celleegenskaper", "DE.Views.TableSettingsAdvanced.textCellSize": "Cellestørrelse", "DE.Views.TableSettingsAdvanced.textCenter": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCheckMargins": "Use default margins", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "Standard cellemarginer", "DE.Views.TableSettingsAdvanced.textDistance": "Distance from text", "DE.Views.TableSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textIndLeft": "Indent from left", "DE.Views.TableSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textMargin": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textMargins": "Cellemarginer", "DE.Views.TableSettingsAdvanced.textMeasure": "Measure in", "DE.Views.TableSettingsAdvanced.textMove": "Move object with text", "DE.Views.TableSettingsAdvanced.textOnlyCells": "For selected cells only", "DE.Views.TableSettingsAdvanced.textOptions": "Options", "DE.Views.TableSettingsAdvanced.textOverlap": "Tillat overlapping", "DE.Views.TableSettingsAdvanced.textPage": "Side", "DE.Views.TableSettingsAdvanced.textPosition": "Posisjon", "DE.Views.TableSettingsAdvanced.textPrefWidth": "Preferred width", "DE.Views.TableSettingsAdvanced.textPreview": "Forhåndsvisning", "DE.Views.TableSettingsAdvanced.textRelative": "relative to", "DE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textRightOf": "to the right of", "DE.Views.TableSettingsAdvanced.textRightTooltip": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTable": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTableBackColor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTablePosition": "Table position", "DE.Views.TableSettingsAdvanced.textTableSize": "Tabellstørrelse", "DE.Views.TableSettingsAdvanced.textTitle": "Table - advanced settings", "DE.Views.TableSettingsAdvanced.textTop": "Topp", "DE.Views.TableSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWidth": "Bredde", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "Bredde og mellomrom", "DE.Views.TableSettingsAdvanced.textWrap": "Text wrapping", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "Inline table", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "Flow table", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "Wrapping style", "DE.Views.TableSettingsAdvanced.textWrapText": "Pakk inn tekst", "DE.Views.TableSettingsAdvanced.tipAll": "Set outer border and all inner lines", "DE.Views.TableSettingsAdvanced.tipCellAll": "Set borders for inner cells only", "DE.Views.TableSettingsAdvanced.tipCellInner": "Set vertical and horizontal lines for inner cells only", "DE.Views.TableSettingsAdvanced.tipCellOuter": "Set outer borders for inner cells only", "DE.Views.TableSettingsAdvanced.tipInner": "Set inner lines only", "DE.Views.TableSettingsAdvanced.tipNone": "Set no borders", "DE.Views.TableSettingsAdvanced.tipOuter": "Set outer border only", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "Set outer border and borders for all inner cells", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "Set outer border and vertical and horizontal lines for inner cells", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "Set table outer border and outer borders for inner cells", "DE.Views.TableSettingsAdvanced.txtCm": "Centimeter", "DE.Views.TableSettingsAdvanced.txtInch": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.txtNoBorders": "No borders", "DE.Views.TableSettingsAdvanced.txtPercent": "Prosent", "DE.Views.TableSettingsAdvanced.txtPt": "<PERSON><PERSON>", "DE.Views.TableToTextDialog.textEmpty": "You must type a character for the custom separator.", "DE.Views.TableToTextDialog.textNested": "Convert nested tables", "DE.Views.TableToTextDialog.textOther": "Other", "DE.Views.TableToTextDialog.textPara": "Paragraph marks", "DE.Views.TableToTextDialog.textSemicolon": "Semicolons", "DE.Views.TableToTextDialog.textSeparator": "Separate text with", "DE.Views.TableToTextDialog.textTab": "Tabs", "DE.Views.TableToTextDialog.textTitle": "Convert table to text", "DE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "DE.Views.TextArtSettings.strFill": "Fill", "DE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strStroke": "Line", "DE.Views.TextArtSettings.strTransparency": "Opasitet", "DE.Views.TextArtSettings.strType": "Type", "DE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "DE.Views.TextArtSettings.textColor": "<PERSON><PERSON><PERSON>ar<PERSON>", "DE.Views.TextArtSettings.textDirection": "Retning", "DE.Views.TextArtSettings.textGradient": "Gradient points", "DE.Views.TextArtSettings.textGradientFill": "Gradient fill", "DE.Views.TextArtSettings.textLinear": "Linear", "DE.Views.TextArtSettings.textNoFill": "No fill", "DE.Views.TextArtSettings.textPosition": "Posisjon", "DE.Views.TextArtSettings.textRadial": "Radial", "DE.Views.TextArtSettings.textSelectTexture": "Velg", "DE.Views.TextArtSettings.textStyle": "Stil", "DE.Views.TextArtSettings.textTemplate": "Template", "DE.Views.TextArtSettings.textTransform": "Transform", "DE.Views.TextArtSettings.tipAddGradientPoint": "Legg til fargeovergangspunkt", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "Remove gradient point", "DE.Views.TextArtSettings.txtNoBorders": "No line", "DE.Views.TextToTableDialog.textAutofit": "Oppførsel for auto-tilpassning", "DE.Views.TextToTableDialog.textColumns": "<PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textContents": "Auto-tilpass til innhold", "DE.Views.TextToTableDialog.textEmpty": "You must type a character for the custom separator.", "DE.Views.TextToTableDialog.textFixed": "Fixed column width", "DE.Views.TextToTableDialog.textOther": "Other", "DE.Views.TextToTableDialog.textPara": "Avsnitt", "DE.Views.TextToTableDialog.textRows": "<PERSON><PERSON>", "DE.Views.TextToTableDialog.textSemicolon": "Semicolons", "DE.Views.TextToTableDialog.textSeparator": "Separate text at", "DE.Views.TextToTableDialog.textTab": "Tabs", "DE.Views.TextToTableDialog.textTableSize": "Tabellstørrelse", "DE.Views.TextToTableDialog.textTitle": "Convert text to table", "DE.Views.TextToTableDialog.textWindow": "Auto-tilpass til vindu", "DE.Views.TextToTableDialog.txtAutoText": "Auto", "DE.Views.Toolbar.capBtnAddComment": "Legg til kommentar", "DE.Views.Toolbar.capBtnBlankPage": "<PERSON> side", "DE.Views.Toolbar.capBtnColumns": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnComment": "Kommentar", "DE.Views.Toolbar.capBtnDateTime": "Dato og tidspunkt", "DE.Views.Toolbar.capBtnHand": "Hand", "DE.Views.Toolbar.capBtnHyphenation": "Hyphenation", "DE.Views.Toolbar.capBtnInsChart": "Diagram", "DE.Views.Toolbar.capBtnInsControls": "Innholdskontroller", "DE.Views.Toolbar.capBtnInsDropcap": "Drop Cap", "DE.Views.Toolbar.capBtnInsEquation": "Equation", "DE.Views.Toolbar.capBtnInsField": "Field", "DE.Views.Toolbar.capBtnInsHeader": "Header & Footer", "DE.Views.Toolbar.capBtnInsImage": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsPagebreak": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsShape": "<PERSON><PERSON>r", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "Symbol", "DE.Views.Toolbar.capBtnInsTable": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsTextart": "Text Art", "DE.Views.Toolbar.capBtnInsTextbox": "Text Box", "DE.Views.Toolbar.capBtnInsTextFromFile": "Text from File", "DE.Views.Toolbar.capBtnLineNumbers": "Line Numbers", "DE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnPageColor": "Page Color", "DE.Views.Toolbar.capBtnPageOrient": "Orientering", "DE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnSelect": "Select", "DE.Views.Toolbar.capBtnWatermark": "Watermark", "DE.Views.Toolbar.capColorScheme": "Colors", "DE.Views.Toolbar.capImgAlign": "Still opp", "DE.Views.Toolbar.capImgBackward": "Send Backward", "DE.Views.Toolbar.capImgForward": "Flytt fremover", "DE.Views.Toolbar.capImgGroup": "Gruppe", "DE.Views.Toolbar.capImgWrapping": "Wrapping", "DE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "DE.Views.Toolbar.mniCapitalizeWords": "Capitalize Each Word", "DE.Views.Toolbar.mniCustomTable": "Insert custom table", "DE.Views.Toolbar.mniDrawTable": "Draw table", "DE.Views.Toolbar.mniEditControls": "Control settings", "DE.Views.Toolbar.mniEditDropCap": "Drop Cap Settings", "DE.Views.Toolbar.mniEditFooter": "<PERSON><PERSON> bunnte<PERSON>t", "DE.Views.Toolbar.mniEditHeader": "<PERSON>iger topptekst", "DE.Views.Toolbar.mniEraseTable": "Erase table", "DE.Views.Toolbar.mniFromFile": "From File", "DE.Views.Toolbar.mniFromStorage": "From Storage", "DE.Views.Toolbar.mniFromUrl": "From URL", "DE.Views.Toolbar.mniHiddenBorders": "Hidden table borders", "DE.Views.Toolbar.mniHiddenChars": "Nonprinting characters", "DE.Views.Toolbar.mniHighlightControls": "Highlight settings", "DE.Views.Toolbar.mniImageFromFile": "Image from file", "DE.Views.Toolbar.mniImageFromStorage": "Image from storage", "DE.Views.Toolbar.mniImageFromUrl": "Image from URL", "DE.Views.Toolbar.mniInsertSSE": "Insert Spreadsheet", "DE.Views.Toolbar.mniLowerCase": "lowercase", "DE.Views.Toolbar.mniRemoveFooter": "Remove footer", "DE.Views.Toolbar.mniRemoveHeader": "Remove header", "DE.Views.Toolbar.mniSentenceCase": "Sentence case.", "DE.Views.Toolbar.mniTextFromLocalFile": "Text from the local file", "DE.Views.Toolbar.mniTextFromStorage": "Text from the storage file", "DE.Views.Toolbar.mniTextFromURL": "Text from the URL file", "DE.Views.Toolbar.mniTextToTable": "Convert Text to Table", "DE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "DE.Views.Toolbar.mniUpperCase": "UPPERCASE", "DE.Views.Toolbar.strMenuNoFill": "No Fill", "DE.Views.Toolbar.textAddSpaceAfter": "Add space after paragraph", "DE.Views.Toolbar.textAddSpaceBefore": "Add space before paragraph", "DE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "DE.Views.Toolbar.textAuto": "Automatisk", "DE.Views.Toolbar.textAutoColor": "Automatisk", "DE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "DE.Views.Toolbar.textBlackHeart": "Black heart suit", "DE.Views.Toolbar.textBold": "Fet", "DE.Views.Toolbar.textBottom": "Bunn: ", "DE.Views.Toolbar.textBullet": "<PERSON><PERSON>", "DE.Views.Toolbar.textChangeLevel": "Change list level", "DE.Views.Toolbar.textCheckboxControl": "Avkrysningsboks", "DE.Views.Toolbar.textColumnsCustom": "Egendefinerte kolonner", "DE.Views.Toolbar.textColumnsLeft": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textColumnsOne": "One", "DE.Views.Toolbar.textColumnsRight": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textColumnsThree": "Three", "DE.Views.Toolbar.textColumnsTwo": "Two", "DE.Views.Toolbar.textComboboxControl": "Combo Box", "DE.Views.Toolbar.textContinuous": "Continuous", "DE.Views.Toolbar.textContPage": "Sammenhengende side", "DE.Views.Toolbar.textCopyright": "Copyright Sign", "DE.Views.Toolbar.textCustomHyphen": "Hyphenation options", "DE.Views.Toolbar.textCustomLineNumbers": "Line numbering options", "DE.Views.Toolbar.textDateControl": "Da<PERSON>", "DE.Views.Toolbar.textDegree": "Degree Sign", "DE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "DE.Views.Toolbar.textDirLtr": "Left-to-right", "DE.Views.Toolbar.textDirRtl": "Right-to-left", "DE.Views.Toolbar.textDivision": "Division Sign", "DE.Views.Toolbar.textDollar": "Dollar Sign", "DE.Views.Toolbar.textDropdownControl": "Drop-down list", "DE.Views.Toolbar.textEditMode": "Edit PDF", "DE.Views.Toolbar.textEditWatermark": "Egendefinert vannmerke", "DE.Views.Toolbar.textEuro": "Euro Sign", "DE.Views.Toolbar.textEvenPage": "Partallside", "DE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "DE.Views.Toolbar.textIndAfter": "Indent after", "DE.Views.Toolbar.textIndBefore": "Indent before", "DE.Views.Toolbar.textIndLeft": "Left indent", "DE.Views.Toolbar.textIndRight": "Right indent", "DE.Views.Toolbar.textInfinity": "Infinity", "DE.Views.Toolbar.textInMargin": "In margin", "DE.Views.Toolbar.textInsColumnBreak": "Insert column break", "DE.Views.Toolbar.textInsertPageCount": "Insert number of pages", "DE.Views.Toolbar.textInsertPageNumber": "Insert page number", "DE.Views.Toolbar.textInsPageBreak": "Insert page break", "DE.Views.Toolbar.textInsSectionBreak": "Insert section break", "DE.Views.Toolbar.textInText": "In text", "DE.Views.Toolbar.textItalic": "Italic", "DE.Views.Toolbar.textLandscape": "Landskap", "DE.Views.Toolbar.textLeft": "Left: ", "DE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "DE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "DE.Views.Toolbar.textLineSpaceOptions": "Line spacing options", "DE.Views.Toolbar.textListSettings": "List settings", "DE.Views.Toolbar.textMarginsLast": "Last Custom", "DE.Views.Toolbar.textMarginsModerate": "Moderate", "DE.Views.Toolbar.textMarginsNarrow": "Smal", "DE.Views.Toolbar.textMarginsNormal": "Normal", "DE.Views.Toolbar.textMarginsWide": "Wide", "DE.Views.Toolbar.textMoreSymbols": "More symbols", "DE.Views.Toolbar.textNewColor": "Legg til ny egendefinert farge", "DE.Views.Toolbar.textNextPage": "Next Page", "DE.Views.Toolbar.textNoHighlight": "No highlighting", "DE.Views.Toolbar.textNone": "None", "DE.Views.Toolbar.textNotEqualTo": "Not Equal To", "DE.Views.Toolbar.textOddPage": "Oddetallsside", "DE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "DE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "DE.Views.Toolbar.textPageMarginsCustom": "Egendefinerte marginer", "DE.Views.Toolbar.textPageSizeCustom": "Egendefinert sidestørrelse", "DE.Views.Toolbar.textPictureControl": "<PERSON><PERSON>", "DE.Views.Toolbar.textPlainControl": "Plain text", "DE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "DE.Views.Toolbar.textPortrait": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textRegistered": "Registered Sign", "DE.Views.Toolbar.textRemoveControl": "Remove content control", "DE.Views.Toolbar.textRemSpaceAfter": "Remove space after paragraph", "DE.Views.Toolbar.textRemSpaceBefore": "Remove space before paragraph", "DE.Views.Toolbar.textRemWatermark": "Remove watermark", "DE.Views.Toolbar.textRestartEachPage": "Restart each page", "DE.Views.Toolbar.textRestartEachSection": "Restart each section", "DE.Views.Toolbar.textRichControl": "Rich text", "DE.Views.Toolbar.textRight": "Right: ", "DE.Views.Toolbar.textSection": "Section Sign", "DE.Views.Toolbar.textShapesCombine": "Combine", "DE.Views.Toolbar.textShapesFragment": "Fragment", "DE.Views.Toolbar.textShapesIntersect": "Intersect", "DE.Views.Toolbar.textShapesSubstract": "Subtract", "DE.Views.Toolbar.textShapesUnion": "Union", "DE.Views.Toolbar.textSmile": "White Smiling Face", "DE.Views.Toolbar.textSpaceAfter": "Space after", "DE.Views.Toolbar.textSpaceBefore": "Space before", "DE.Views.Toolbar.textSquareRoot": "Square Root", "DE.Views.Toolbar.textStrikeout": "Gjennomstreking", "DE.Views.Toolbar.textStyleMenuDelete": "<PERSON><PERSON> stil", "DE.Views.Toolbar.textStyleMenuDeleteAll": "Slett alle brukerdefinerte formateringer", "DE.Views.Toolbar.textStyleMenuNew": "New style from selection", "DE.Views.Toolbar.textStyleMenuRestore": "Restore to default", "DE.Views.Toolbar.textStyleMenuRestoreAll": "Restore all to default styles", "DE.Views.Toolbar.textStyleMenuUpdate": "Update from selection", "DE.Views.Toolbar.textSubscript": "Senket skrift", "DE.Views.Toolbar.textSuperscript": "<PERSON><PERSON> skrift", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "Suppress for current paragraph", "DE.Views.Toolbar.textTabCollaboration": "Samarbeid", "DE.Views.Toolbar.textTabDraw": "Draw", "DE.Views.Toolbar.textTabFile": "Fil", "DE.Views.Toolbar.textTabHome": "Home", "DE.Views.Toolbar.textTabInsert": "Insert", "DE.Views.Toolbar.textTabLayout": "Layout", "DE.Views.Toolbar.textTabLinks": "References", "DE.Views.Toolbar.textTabProtect": "Protection", "DE.Views.Toolbar.textTabReview": "Review", "DE.Views.Toolbar.textTabView": "View", "DE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "DE.Views.Toolbar.textTitleError": "<PERSON><PERSON>", "DE.Views.Toolbar.textToCurrent": "To current position", "DE.Views.Toolbar.textTop": "Top: ", "DE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "DE.Views.Toolbar.textUnderline": "Understreking", "DE.Views.Toolbar.textYen": "Yen Sign", "DE.Views.Toolbar.tipAlignCenter": "Still opp senter", "DE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON>", "DE.Views.Toolbar.tipAlignLeft": "Still opp venstre", "DE.Views.Toolbar.tipAlignRight": "Still opp høyre", "DE.Views.Toolbar.tipBack": "Tilbake", "DE.Views.Toolbar.tipBlankPage": "Insert blank page", "DE.Views.Toolbar.tipChangeCase": "Change case", "DE.Views.Toolbar.tipChangeChart": "<PERSON><PERSON> diagramtype", "DE.Views.Toolbar.tipClearStyle": "Fjern formatering", "DE.Views.Toolbar.tipColorSchemas": "<PERSON><PERSON>", "DE.Views.Toolbar.tipColumns": "Insert columns", "DE.Views.Toolbar.tipControls": "Insert content controls", "DE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> stil", "DE.Views.Toolbar.tipCut": "Cut", "DE.Views.Toolbar.tipDateTime": "Insert current date and time", "DE.Views.Toolbar.tipDecFont": "<PERSON><PERSON> sk<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipDecPrLeft": "Reduser innrykk", "DE.Views.Toolbar.tipDownload": "Download file", "DE.Views.Toolbar.tipDropCap": "Insert drop cap", "DE.Views.Toolbar.tipEditHeader": "Rediger topptekst eller bunntekst", "DE.Views.Toolbar.tipEditMode": "Edit current file.<br>The page will be reloaded.", "DE.Views.Toolbar.tipFontColor": "Skriftfarge", "DE.Views.Toolbar.tipFontName": "Font", "DE.Views.Toolbar.tipFontSize": "Font size", "DE.Views.Toolbar.tipHandTool": "Hand tool", "DE.Views.Toolbar.tipHighlightColor": "Highlight color", "DE.Views.Toolbar.tipHyphenation": "Change hyphenation", "DE.Views.Toolbar.tipImgAlign": "Still opp objekter", "DE.Views.Toolbar.tipImgGroup": "Group objects", "DE.Views.Toolbar.tipImgWrapping": "Pakk inn tekst", "DE.Views.Toolbar.tipIncFont": "Increment font size", "DE.Views.Toolbar.tipIncPrLeft": "Increase indent", "DE.Views.Toolbar.tipInsertChart": "Insert chart", "DE.Views.Toolbar.tipInsertEquation": "Insert equation", "DE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "DE.Views.Toolbar.tipInsertImage": "Sett inn bilde", "DE.Views.Toolbar.tipInsertNum": "Insert page number", "DE.Views.Toolbar.tipInsertShape": "Insert shape", "DE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "DE.Views.Toolbar.tipInsertSymbol": "Insert symbol", "DE.Views.Toolbar.tipInsertTable": "Insert table", "DE.Views.Toolbar.tipInsertText": "Insert text box", "DE.Views.Toolbar.tipInsertTextArt": "Insert Text Art", "DE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "DE.Views.Toolbar.tipInsField": "Insert field", "DE.Views.Toolbar.tipLineNumbers": "Show line numbers", "DE.Views.Toolbar.tipLineSpace": "Paragraph line spacing", "DE.Views.Toolbar.tipMailRecepients": "Mail merge", "DE.Views.Toolbar.tipMarkers": "Kulepunkt", "DE.Views.Toolbar.tipMarkersArrow": "Pilpunkter", "DE.Views.Toolbar.tipMarkersCheckmark": "Checkmark bullets", "DE.Views.Toolbar.tipMarkersDash": "Dash bullets", "DE.Views.Toolbar.tipMarkersFRhombus": "Filled rhombus bullets", "DE.Views.Toolbar.tipMarkersFRound": "Filled round bullets", "DE.Views.Toolbar.tipMarkersFSquare": "Filled square bullets", "DE.Views.Toolbar.tipMarkersHRound": "Hollow round bullets", "DE.Views.Toolbar.tipMarkersStar": "Star bullets", "DE.Views.Toolbar.tipMultiLevelArticl": "Multi-level numbered articles", "DE.Views.Toolbar.tipMultiLevelChapter": "Multi-level numbered chapters", "DE.Views.Toolbar.tipMultiLevelHeadings": "Multi-level numbered headings", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "Multi-level various numbered headings", "DE.Views.Toolbar.tipMultiLevelNumbered": "Multi-level numbered bullets", "DE.Views.Toolbar.tipMultilevels": "Multilevel list", "DE.Views.Toolbar.tipMultiLevelSymbols": "Multi-level symbols bullets", "DE.Views.Toolbar.tipMultiLevelVarious": "Multi-level various numbered bullets", "DE.Views.Toolbar.tipNumbers": "Numbering", "DE.Views.Toolbar.tipPageBreak": "Insert page or section break", "DE.Views.Toolbar.tipPageColor": "Change page color", "DE.Views.Toolbar.tipPageMargins": "Page margins", "DE.Views.Toolbar.tipPageOrient": "Page orientation", "DE.Views.Toolbar.tipPageSize": "Page size", "DE.Views.Toolbar.tipParagraphStyle": "Paragraph style", "DE.Views.Toolbar.tipPaste": "Lim inn", "DE.Views.Toolbar.tipPrColor": "Shading", "DE.Views.Toolbar.tipPrint": "Skriv ut", "DE.Views.Toolbar.tipPrintQuick": "Quick print", "DE.Views.Toolbar.tipRedo": "Redo", "DE.Views.Toolbar.tipReplace": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipSave": "Lagre", "DE.Views.Toolbar.tipSaveCoauth": "Save your changes for the other users to see them.", "DE.Views.Toolbar.tipSelectAll": "Velg alle", "DE.Views.Toolbar.tipSelectTool": "Select tool", "DE.Views.Toolbar.tipSendBackward": "Send backward", "DE.Views.Toolbar.tipSendForward": "Flytt fremover", "DE.Views.Toolbar.tipShapesMerge": "Merge shapes", "DE.Views.Toolbar.tipShowHiddenChars": "Nonprinting characters", "DE.Views.Toolbar.tipSynchronize": "The document has been changed by another user. Please click to save your changes and reload the updates.", "DE.Views.Toolbar.tipTextDir": "Text direction", "DE.Views.Toolbar.tipTextFromFile": "Text from file", "DE.Views.Toolbar.tipUndo": "<PERSON><PERSON>", "DE.Views.Toolbar.tipWatermark": "Edit watermark", "DE.Views.Toolbar.txtAutoText": "Auto", "DE.Views.Toolbar.txtDistribHor": "Distribute horizontally", "DE.Views.Toolbar.txtDistribVert": "Distribute vertically", "DE.Views.Toolbar.txtGroupBulletDoc": "Document bullets", "DE.Views.Toolbar.txtGroupBulletLib": "Bullet library", "DE.Views.Toolbar.txtGroupMultiDoc": "Lists in current document", "DE.Views.Toolbar.txtGroupMultiLib": "List library", "DE.Views.Toolbar.txtGroupNumDoc": "Document numbering formats", "DE.Views.Toolbar.txtGroupNumLib": "Numbering library", "DE.Views.Toolbar.txtGroupRecent": "Recently used", "DE.Views.Toolbar.txtMarginAlign": "Juster i forhold til marg", "DE.Views.Toolbar.txtObjectsAlign": "<PERSON><PERSON> valgte objekter", "DE.Views.Toolbar.txtPageAlign": "Juster i forhold til side", "DE.Views.ViewTab.textAlwaysShowToolbar": "Alltid vis verktøyslinjen", "DE.Views.ViewTab.textDarkDocument": "Dark Document", "DE.Views.ViewTab.textFill": "Fill", "DE.Views.ViewTab.textFitToPage": "<PERSON><PERSON> To <PERSON>", "DE.Views.ViewTab.textFitToWidth": "Fit To <PERSON>th", "DE.Views.ViewTab.textInterfaceTheme": "Interface Theme", "DE.Views.ViewTab.textLeftMenu": "Left Panel", "DE.Views.ViewTab.textLine": "Line", "DE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "DE.Views.ViewTab.textNavigation": "Navigasjon", "DE.Views.ViewTab.textOutline": "Headings", "DE.Views.ViewTab.textRightMenu": "Right Panel", "DE.Views.ViewTab.textRulers": "Rulers", "DE.Views.ViewTab.textStatusBar": "Status Bar", "DE.Views.ViewTab.textTabStyle": "Tab style", "DE.Views.ViewTab.textZoom": "Zoom", "DE.Views.ViewTab.tipDarkDocument": "Dark document", "DE.Views.ViewTab.tipFitToPage": "Fit to page", "DE.Views.ViewTab.tipFitToWidth": "Fit to width", "DE.Views.ViewTab.tipHeadings": "Headings", "DE.Views.ViewTab.tipInterfaceTheme": "Interface theme", "DE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textAuto": "Auto", "DE.Views.WatermarkSettingsDialog.textBold": "Fet", "DE.Views.WatermarkSettingsDialog.textColor": "Text color", "DE.Views.WatermarkSettingsDialog.textDiagonal": "Diagonal", "DE.Views.WatermarkSettingsDialog.textFont": "Font", "DE.Views.WatermarkSettingsDialog.textFromFile": "From file", "DE.Views.WatermarkSettingsDialog.textFromStorage": "From storage", "DE.Views.WatermarkSettingsDialog.textFromUrl": "From URL", "DE.Views.WatermarkSettingsDialog.textHor": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textImageW": "Image watermark", "DE.Views.WatermarkSettingsDialog.textItalic": "Italic", "DE.Views.WatermarkSettingsDialog.textLanguage": "Language", "DE.Views.WatermarkSettingsDialog.textLayout": "Layout", "DE.Views.WatermarkSettingsDialog.textNone": "None", "DE.Views.WatermarkSettingsDialog.textScale": "Scale", "DE.Views.WatermarkSettingsDialog.textSelect": "Select image", "DE.Views.WatermarkSettingsDialog.textStrikeout": "Gjennomstreking", "DE.Views.WatermarkSettingsDialog.textText": "Tekst", "DE.Views.WatermarkSettingsDialog.textTextW": "Text watermark", "DE.Views.WatermarkSettingsDialog.textTitle": "Watermark settings", "DE.Views.WatermarkSettingsDialog.textTransparency": "Semitransparent", "DE.Views.WatermarkSettingsDialog.textUnderline": "Understreking", "DE.Views.WatermarkSettingsDialog.tipFontName": "Font name", "DE.Views.WatermarkSettingsDialog.tipFontSize": "Font size", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}