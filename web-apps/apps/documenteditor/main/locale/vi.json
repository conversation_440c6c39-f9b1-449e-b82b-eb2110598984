{"Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "Common.Controllers.Desktop.hintBtnHome": "Show main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Nặc danh", "Common.Controllers.ExternalDiagramEditor.textClose": "Đ<PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "<PERSON><PERSON>i tượng bị vô hiệu vì nó đang được chỉnh sửa bởi một người dùng khác.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "Common.Controllers.ExternalMergeEditor.textAnonymous": "Nặc danh", "Common.Controllers.ExternalMergeEditor.textClose": "Đ<PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.warningText": "<PERSON><PERSON>i tượng bị vô hiệu vì nó đang được chỉnh sửa bởi một người dùng khác.", "Common.Controllers.ExternalMergeEditor.warningTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "Common.Controllers.ExternalOleEditor.textAnonymous": "Anonymous", "Common.Controllers.ExternalOleEditor.textClose": "Đ<PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "The object is disabled because it is being edited by another user.", "Common.Controllers.ExternalOleEditor.warningTitle": "Warning", "Common.Controllers.History.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "In order to compare documents all the tracked changes in them will be considered to have been accepted. Do you want to continue?", "Common.Controllers.ReviewChanges.textAtLeast": "<PERSON><PERSON> n<PERSON>t", "Common.Controllers.ReviewChanges.textAuto": "tự động", "Common.Controllers.ReviewChanges.textBaseline": "<PERSON><PERSON><PERSON><PERSON> cơ sở", "Common.Controllers.ReviewChanges.textBold": "Đậm", "Common.Controllers.ReviewChanges.textBreakBefore": "<PERSON><PERSON>t trang đằng trước", "Common.Controllers.ReviewChanges.textCaps": "Tất cả Drop cap", "Common.Controllers.ReviewChanges.textCenter": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textChar": "Character level", "Common.Controllers.ReviewChanges.textChart": "<PERSON><PERSON><PERSON><PERSON> đồ", "Common.Controllers.ReviewChanges.textColor": "<PERSON><PERSON><PERSON> chữ", "Common.Controllers.ReviewChanges.textContextual": "<PERSON><PERSON><PERSON><PERSON> thêm kho<PERSON>ng cách gi<PERSON>a các đoạn văn cùng kiểu", "Common.Controllers.ReviewChanges.textDeleted": "<b><PERSON><PERSON> xóa:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "Gạch bỏ kép", "Common.Controllers.ReviewChanges.textEquation": "<PERSON><PERSON><PERSON><PERSON> trình", "Common.Controllers.ReviewChanges.textExact": "ch<PERSON><PERSON> x<PERSON>c", "Common.Controllers.ReviewChanges.textFirstLine": "<PERSON><PERSON><PERSON> đầu tiên", "Common.Controllers.ReviewChanges.textFontSize": "Cỡ chữ", "Common.Controllers.ReviewChanges.textFormatted": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> d<PERSON>ng", "Common.Controllers.ReviewChanges.textHighlight": "<PERSON><PERSON><PERSON> tô sáng", "Common.Controllers.ReviewChanges.textImage": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textIndentLeft": "<PERSON><PERSON><PERSON><PERSON> lề trái", "Common.Controllers.ReviewChanges.textIndentRight": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> phải", "Common.Controllers.ReviewChanges.textInserted": "<b><PERSON><PERSON> chèn:</b>", "Common.Controllers.ReviewChanges.textItalic": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textJustify": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textKeepLines": "<PERSON><PERSON><PERSON> các dòng cùng nhau", "Common.Controllers.ReviewChanges.textKeepNext": "<PERSON><PERSON><PERSON> cho tiếp theo", "Common.Controllers.ReviewChanges.textLeft": "<PERSON><PERSON><PERSON> t<PERSON>", "Common.Controllers.ReviewChanges.textLineSpacing": "<PERSON><PERSON><PERSON><PERSON> cách dòng:", "Common.Controllers.ReviewChanges.textMultiple": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textNoBreakBefore": "<PERSON><PERSON><PERSON><PERSON> ngắt trang đằng trước", "Common.Controllers.ReviewChanges.textNoContextual": "<PERSON>h<PERSON><PERSON> khoảng trống vào giữa các đoạn có cùng kiểu", "Common.Controllers.ReviewChanges.textNoKeepLines": "Không giữ các dòng với nhau", "Common.Controllers.ReviewChanges.textNoKeepNext": "<PERSON><PERSON>ng giữ với tiếp theo", "Common.Controllers.ReviewChanges.textNot": "K<PERSON>ô<PERSON>", "Common.Controllers.ReviewChanges.textNoWidow": "<PERSON><PERSON><PERSON>ng kiểm soát dòng lẻ cuối trang trước", "Common.Controllers.ReviewChanges.textNum": "<PERSON>hay đổi đ<PERSON>h số", "Common.Controllers.ReviewChanges.textOff": "{0} is no longer using Track Changes.", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} disabled Track Changes for everyone.", "Common.Controllers.ReviewChanges.textOn": "{0} is now using Track Changes.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} enabled Track Changes for everyone.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b><PERSON><PERSON> x<PERSON><PERSON> đo<PERSON>n</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "<PERSON><PERSON><PERSON><PERSON> văn bản đ<PERSON><PERSON><PERSON> định dạng", "Common.Controllers.ReviewChanges.textParaInserted": "<b><PERSON><PERSON> chèn đo<PERSON></b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>Moved down:</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>Moved up:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>Moved:</b>", "Common.Controllers.ReviewChanges.textPosition": "<PERSON><PERSON> trí", "Common.Controllers.ReviewChanges.textRight": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textShape": "<PERSON><PERSON><PERSON> d<PERSON>", "Common.Controllers.ReviewChanges.textShd": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textShow": "Show changes at", "Common.Controllers.ReviewChanges.textSmallCaps": "Drop cap nhỏ", "Common.Controllers.ReviewChanges.textSpacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch", "Common.Controllers.ReviewChanges.textSpacingAfter": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch sau", "Common.Controllers.ReviewChanges.textSpacingBefore": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch trư<PERSON>", "Common.Controllers.ReviewChanges.textStrikeout": "Gạch bỏ", "Common.Controllers.ReviewChanges.textSubScript": "Chỉ số dưới", "Common.Controllers.ReviewChanges.textSuperScript": "Chỉ số trên", "Common.Controllers.ReviewChanges.textTableChanged": "<b>Table settings changed</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b>Table rows added</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b>Table rows deleted</b>", "Common.Controllers.ReviewChanges.textTabs": "Thay đổi tab", "Common.Controllers.ReviewChanges.textTitleComparison": "Comparison settings", "Common.Controllers.ReviewChanges.textUnderline": "G<PERSON><PERSON> chân", "Common.Controllers.ReviewChanges.textUrl": "Paste a document URL", "Common.Controllers.ReviewChanges.textWidow": "<PERSON><PERSON><PERSON> soát dòng lẻ cuối trang trước", "Common.Controllers.ReviewChanges.textWord": "Word level", "Common.define.chartData.textArea": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "Stacked area", "Common.define.chartData.textAreaStackedPer": "100% Stacked area", "Common.define.chartData.textBar": "Gạch", "Common.define.chartData.textBarNormal": "Clustered column", "Common.define.chartData.textBarNormal3d": "3-D Clustered column", "Common.define.chartData.textBarNormal3dPerspective": "Cột 3-D", "Common.define.chartData.textBarStacked": "Stacked column", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Stacked column", "Common.define.chartData.textBarStackedPer": "100% Stacked column", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Stacked column", "Common.define.chartData.textCharts": "Charts", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Stacked area - clustered column", "Common.define.chartData.textComboBarLine": "Clustered column - line", "Common.define.chartData.textComboBarLineSecondary": "Clustered column - line on secondary axis", "Common.define.chartData.textComboCustom": "Custom combination", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "Clustered bar", "Common.define.chartData.textHBarNormal3d": "3-D Clustered bar", "Common.define.chartData.textHBarStacked": "Stacked bar", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Stacked bar", "Common.define.chartData.textHBarStackedPer": "100% Stacked bar", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Stacked bar", "Common.define.chartData.textLine": "Đường kẻ", "Common.define.chartData.textLine3d": "Đường 3-D", "Common.define.chartData.textLineMarker": "Line with markers", "Common.define.chartData.textLineStacked": "Stacked line", "Common.define.chartData.textLineStackedMarker": "Stacked line with markers", "Common.define.chartData.textLineStackedPer": "100% Stacked line", "Common.define.chartData.textLineStackedPerMarker": "100% Stacked line with markers", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON> b<PERSON>", "Common.define.chartData.textPie3d": "3-D pie", "Common.define.chartData.textPoint": "XY (Phân tán)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Scatter with straight lines", "Common.define.chartData.textScatterLineMarker": "Scatter with straight lines and markers", "Common.define.chartData.textScatterSmooth": "Scatter with smooth lines", "Common.define.chartData.textScatterSmoothMarker": "Scatter with smooth lines and markers", "Common.define.chartData.textStock": "<PERSON><PERSON> phi<PERSON>u", "Common.define.chartData.textSurface": "Bề mặt", "Common.define.smartArt.textAccentedPicture": "Accented picture", "Common.define.smartArt.textAccentProcess": "Accent process", "Common.define.smartArt.textAlternatingFlow": "Alternating flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating Hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating picture blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating picture circles", "Common.define.smartArt.textArchitectureLayout": "Architecture layout", "Common.define.smartArt.textArrowRibbon": "Arrow ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending picture accent process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic bending process", "Common.define.smartArt.textBasicBlockList": "Basic block list", "Common.define.smartArt.textBasicChevronProcess": "Basic chevron process", "Common.define.smartArt.textBasicCycle": "Basic cycle", "Common.define.smartArt.textBasicMatrix": "Basic matrix", "Common.define.smartArt.textBasicPie": "Basic pie", "Common.define.smartArt.textBasicProcess": "Basic process", "Common.define.smartArt.textBasicPyramid": "Basic pyramid", "Common.define.smartArt.textBasicRadial": "Basic radial", "Common.define.smartArt.textBasicTarget": "Basic target", "Common.define.smartArt.textBasicTimeline": "Basic timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending picture accent list", "Common.define.smartArt.textBendingPictureBlocks": "Bending picture blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending picture caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending picture caption list", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending picture semi-transparent text", "Common.define.smartArt.textBlockCycle": "Block cycle", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron accent process", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Circle accent timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging arrows", "Common.define.smartArt.textDivergingRadial": "Diverging radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon Radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy list", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined List", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and title organization chart", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing ideas", "Common.define.smartArt.textOrganizationChart": "Organization СЃhart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture accent blocks", "Common.define.smartArt.textPictureAccentList": "Picture accent list", "Common.define.smartArt.textPictureAccentProcess": "Picture accent process", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture strips", "Common.define.smartArt.textPieProcess": "Pie process", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "Spiral Picture", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Tabbed <PERSON>", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "More", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "Tệ<PERSON> này chỉ có thể đọc. Đ<PERSON> giữ các thay đổi, lưu tệp dưới một tên khác hoặc ở vị trí khác", "Common.Translation.warnFileLocked": "Bạn không thể chỉnh sửa tệp này vì nó đang được chỉnh sửa trong một ứng dụng khác.", "Common.Translation.warnFileLockedBtnEdit": "Create a copy", "Common.Translation.warnFileLockedBtnView": "Open for viewing", "Common.UI.ButtonColored.textAutoColor": "Automatic", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "Common.UI.Calendar.textApril": "April", "Common.UI.Calendar.textAugust": "August", "Common.UI.Calendar.textDecember": "December", "Common.UI.Calendar.textFebruary": "February", "Common.UI.Calendar.textJanuary": "January", "Common.UI.Calendar.textJuly": "July", "Common.UI.Calendar.textJune": "June", "Common.UI.Calendar.textMarch": "March", "Common.UI.Calendar.textMay": "May", "Common.UI.Calendar.textMonths": "Months", "Common.UI.Calendar.textNovember": "November", "Common.UI.Calendar.textOctober": "October", "Common.UI.Calendar.textSeptember": "September", "Common.UI.Calendar.textShortApril": "Apr", "Common.UI.Calendar.textShortAugust": "Aug", "Common.UI.Calendar.textShortDecember": "Dec", "Common.UI.Calendar.textShortFebruary": "Feb", "Common.UI.Calendar.textShortFriday": "Fr", "Common.UI.Calendar.textShortJanuary": "Jan", "Common.UI.Calendar.textShortJuly": "Jul", "Common.UI.Calendar.textShortJune": "Jun", "Common.UI.Calendar.textShortMarch": "Mar", "Common.UI.Calendar.textShortMay": "May", "Common.UI.Calendar.textShortMonday": "Mo", "Common.UI.Calendar.textShortNovember": "Nov", "Common.UI.Calendar.textShortOctober": "Oct", "Common.UI.Calendar.textShortSaturday": "Sa", "Common.UI.Calendar.textShortSeptember": "Sep", "Common.UI.Calendar.textShortSunday": "Su", "Common.UI.Calendar.textShortThursday": "Th", "Common.UI.Calendar.textShortTuesday": "Tu", "Common.UI.Calendar.textShortWednesday": "We", "Common.UI.Calendar.textYears": "Years", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON><PERSON> có kiểu", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON><PERSON><PERSON> t<PERSON>i", "Common.UI.ExtendedColorDialog.textHexErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị thuộc từ 000000 đến FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị số thuộc từ 0 đến 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON><PERSON><PERSON> màu", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnCalendar.textDate": "<PERSON><PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Hide password", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON><PERSON> mật k<PERSON>", "Common.UI.SearchBar.textFind": "Find", "Common.UI.SearchBar.tipCloseSearch": "Close find", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Previous result", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON> sáng kết quả", "Common.UI.SearchDialog.textMatchCase": "<PERSON>ân biệt chữ hoa chữ thường", "Common.UI.SearchDialog.textReplaceDef": "<PERSON><PERSON><PERSON><PERSON> văn bản thay thế", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON><PERSON> từ khóa của bạn ở đây", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON><PERSON> và Thay thế", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Chỉ toàn bộ từ", "Common.UI.SearchDialog.txtBtnHideReplace": "Ẩn Thay thế", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON>hay thế", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON> thế tất cả", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON>ông hiển thị lại thông báo này", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Tài liệu đã được thay đổi bởi người dùng khác.<br><PERSON><PERSON> lòng nhấp để lưu thay đổi của bạn và tải lại các cập nhật.", "Common.UI.ThemeColorPalette.textRecentColors": "Recent colors", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Màu theme", "Common.UI.ThemeColorPalette.textTransparent": "Transparent", "Common.UI.Themes.txtThemeClassicLight": "Classic Light", "Common.UI.Themes.txtThemeContrastDark": "Contrast Dark", "Common.UI.Themes.txtThemeDark": "Dark", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "Light", "Common.UI.Themes.txtThemeSystem": "Same as system", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "Đ<PERSON><PERSON>", "Common.UI.Window.noButtonText": "K<PERSON>ô<PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "<PERSON><PERSON>ông hiển thị lại thông báo này", "Common.UI.Window.textError": "Lỗi", "Common.UI.Window.textInformation": "Thông tin", "Common.UI.Window.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "Common.UI.Window.yesButtonText": "<PERSON><PERSON>", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Background", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Blue", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Dark green", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON>", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "địa chỉ:", "Common.Views.About.txtLicensee": "NGƯỜI ĐƯỢC CẤP GIẤY PHÉP", "Common.Views.About.txtLicensor": "NGƯỜI CẤP GIẤY PHÉP", "Common.Views.About.txtMail": "email:", "Common.Views.About.txtPoweredBy": "Được hỗ trợ bởi", "Common.Views.About.txtTel": "ĐT.: ", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "thêm", "Common.Views.AutoCorrectDialog.textApplyText": "Apply as you type", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Text AutoCorrect", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat as you type", "Common.Views.AutoCorrectDialog.textBulleted": "Automatic bulleted lists", "Common.Views.AutoCorrectDialog.textBy": "By", "Common.Views.AutoCorrectDialog.textDelete": "Delete", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Add period with double-space", "Common.Views.AutoCorrectDialog.textFLCells": "Capitalize first letter of table cells", "Common.Views.AutoCorrectDialog.textFLDont": "Don`t capitalize after", "Common.Views.AutoCorrectDialog.textFLSentence": "Capitalize first letter of sentences", "Common.Views.AutoCorrectDialog.textForLangFL": "Exceptions for the language:", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet and network paths with hyperlinks", "Common.Views.AutoCorrectDialog.textHyphens": "Hyphens (--) with dash (вЂ”)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Math AutoCorrect", "Common.Views.AutoCorrectDialog.textNumbered": "Automatic numbered lists", "Common.Views.AutoCorrectDialog.textQuotes": "\"Straight quotes\" with \"smart quotes\"", "Common.Views.AutoCorrectDialog.textRecognized": "Recognized functions", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "The following expressions are recognized math expressions. They will not be automatically italicized.", "Common.Views.AutoCorrectDialog.textReplace": "Replace", "Common.Views.AutoCorrectDialog.textReplaceText": "Replace as you type", "Common.Views.AutoCorrectDialog.textReplaceType": "Replace text as you type", "Common.Views.AutoCorrectDialog.textReset": "Reset", "Common.Views.AutoCorrectDialog.textResetAll": "Reset to default", "Common.Views.AutoCorrectDialog.textRestore": "Rest<PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "AutoCorrect", "Common.Views.AutoCorrectDialog.textWarnAddFL": "Exceptions must contain only the letters, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Recognized functions must contain only the letters A through Z, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnResetFL": "Any exceptions you added will be removed and the removed ones will be restored. Do you want to continue?", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Any expression you added will be removed and the removed ones will be restored. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnReplace": "The autocorrect entry for %1 already exists. Do you want to replace it?", "Common.Views.AutoCorrectDialog.warnReset": "Any autocorrect you added will be removed and the changed ones will be restored to their original values. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnRestore": "The autocorrect entry for %1 will be reset to its original value. Do you want to continue?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Author A to Z", "Common.Views.Comments.mniAuthorDesc": "Author Z to A", "Common.Views.Comments.mniDateAsc": "Oldest", "Common.Views.Comments.mniDateDesc": "Newest", "Common.Views.Comments.mniFilterGroups": "Filter by group", "Common.Views.Comments.mniPositionAsc": "From top", "Common.Views.Comments.mniPositionDesc": "From bottom", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n xét", "Common.Views.Comments.textAddCommentToDoc": "<PERSON><PERSON><PERSON><PERSON> nhận xét vào tài liệu", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAll": "All", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "Đ<PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON>n x<PERSON>t", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON> lu<PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON><PERSON> bình luận của bạn ở đây", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "Common.Views.Comments.textOpenAgain": "Mở lại", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON> lờ<PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON> gi<PERSON>i quy<PERSON>", "Common.Views.Comments.textSort": "<PERSON><PERSON><PERSON> x<PERSON>p nhận xét", "Common.Views.Comments.textSortFilter": "<PERSON>ắp xếp và lọc nhận xét", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "Bạn không có quyền mở lại nhận xét", "Common.Views.Comments.txtEmpty": "<PERSON><PERSON><PERSON><PERSON> có nhận xét nào trong tài liệu", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON>ông hiển thị lại thông báo này", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON>, cắt và dán bằng cách sử dụng các nút trên thanh công cụ của trình soạn thảo và các tác vụ trình đơn ngữ cảnh sẽ chỉ được thực hiện trong tab trình soạn thảo này.<br><br> Để sao chép hoặc dán vào hoặc từ các ứng dụng bên ngoài tab trình soạn thảo sử dụng các kết hợp bàn phím sau đây:", "Common.Views.CopyWarningDialog.textTitle": "Sao ché<PERSON>, Cắt và Dán", "Common.Views.CopyWarningDialog.textToCopy": "để sao chép", "Common.Views.CopyWarningDialog.textToCut": "<PERSON><PERSON>", "Common.Views.CopyWarningDialog.textToPaste": "<PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "<PERSON><PERSON> tả<PERSON>...", "Common.Views.DocumentAccessDialog.textTitle": "Cài đặt chia sẻ", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.hintSelect": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtEraser": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtSize": "Size", "Common.Views.ExternalDiagramEditor.textTitle": "Tr<PERSON><PERSON> chỉnh sửa biểu đồ", "Common.Views.ExternalEditor.textClose": "Close", "Common.Views.ExternalEditor.textSave": "Lưu & Thoát", "Common.Views.ExternalMergeEditor.textTitle": "<PERSON><PERSON><PERSON><PERSON> nhận thư trộn", "Common.Views.ExternalOleEditor.textTitle": "Spreadsheet Editor", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "<PERSON>ài liệu hiện đang được chỉnh sửa bởi nhiều người dùng.", "Common.Views.Header.textAddFavorite": "<PERSON> as favorite", "Common.Views.Header.textAdvSettings": "Advanced settings", "Common.Views.Header.textBack": "Mở vị trí tệp", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "Hide toolbar", "Common.Views.Header.textDocEditDesc": "Make any changes", "Common.Views.Header.textDocViewDesc": "View the file, but make no changes", "Common.Views.Header.textDocViewFormDesc": "See how the form will look like when filling out", "Common.Views.Header.textDownload": "Download", "Common.Views.Header.textEdit": "Editing", "Common.Views.Header.textHideLines": "Hide Rulers", "Common.Views.Header.textHideStatusBar": "Hide status bar", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Remove from Favorites", "Common.Views.Header.textReview": "Reviewing", "Common.Views.Header.textReviewDesc": "Suggest changes", "Common.Views.Header.textShare": "Share", "Common.Views.Header.textStartFill": "Share & collect", "Common.Views.Header.textView": "Viewing", "Common.Views.Header.textViewForm": "Viewing form", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "<PERSON><PERSON><PERSON><PERSON> lý quyền truy cập tài liệu", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDocEdit": "Editing", "Common.Views.Header.tipDocView": "Viewing", "Common.Views.Header.tipDocViewForm": "Viewing form", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipFillStatus": "Filling status", "Common.Views.Header.tipGoEdit": "Chỉnh sửa tệp hiện tại", "Common.Views.Header.tipPrint": "In tệp", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "Redo", "Common.Views.Header.tipReview": "Reviewing", "Common.Views.Header.tipSave": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSearch": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndo": "Undo", "Common.Views.Header.tipUsers": "View users", "Common.Views.Header.tipViewSettings": "View settings", "Common.Views.Header.tipViewUsers": "<PERSON>em người dùng và quản lý quyền truy cập tài liệu", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON> đ<PERSON>i quyền truy cập", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON> tên", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON> l<PERSON>ch sử", "Common.Views.History.textHideAll": "Ẩn thay đổi chi tiết", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "Common.Views.History.textShowAll": "<PERSON><PERSON><PERSON> thị thay đổi chi tiết", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Dán URL hình ảnh:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Trường này phải là một URL có định dạng \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "<PERSON>ạn cần x<PERSON>c đ<PERSON>nh số hàng và cột hợp lệ.", "Common.Views.InsertTableDialog.txtColumns": "Số cột", "Common.Views.InsertTableDialog.txtMaxText": "<PERSON><PERSON><PERSON> trị lớn nhất cho trường này là {0}.", "Common.Views.InsertTableDialog.txtMinText": "<PERSON><PERSON><PERSON> trị nhỏ nhất cho trường này là {0}.", "Common.Views.InsertTableDialog.txtRows": "<PERSON><PERSON> hàng", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON><PERSON> b<PERSON>", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON><PERSON>", "Common.Views.LanguageDialog.labelSelect": "<PERSON><PERSON><PERSON> ngôn ngữ tài liệu", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtEncoding": "Mã hóa", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON> kh<PERSON>u không đúng.", "Common.Views.OpenDialog.txtOpenFile": "Nhập mật khẩu để mở tệp", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "Preview", "Common.Views.OpenDialog.txtProtected": "Once you enter the password and open the file, the current password to the file will be reset.", "Common.Views.OpenDialog.txtTitle": "Chọn %1 lựa chọn", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> vệ", "Common.Views.PasswordDialog.txtDescription": "Set a password to protect this document", "Common.Views.PasswordDialog.txtIncorrectPwd": "Confirmation password is not identical", "Common.Views.PasswordDialog.txtPassword": "Password", "Common.Views.PasswordDialog.txtRepeat": "Repeat password", "Common.Views.PasswordDialog.txtTitle": "Set password", "Common.Views.PasswordDialog.txtWarning": "Chú ý: Nếu bạn mất hoặc quên mật khẩu, bạn không thể khôi phục mật khẩu.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON> t<PERSON>", "Common.Views.PluginPanel.textClosePanel": "<PERSON><PERSON><PERSON> ph<PERSON>n bổ trợ", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Plugin", "Common.Views.Plugins.strPlugins": "Phần mở rộng", "Common.Views.Plugins.textBackgroundPlugins": "<PERSON><PERSON><PERSON> bổ trợ trong nền", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON> đ<PERSON>u", "Common.Views.Plugins.textStop": "Dừng", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "<PERSON><PERSON> (các) phần bổ trợ chạy trong nền", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "Encrypt with password", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "Change or delete password", "Common.Views.Protection.hintSignature": "<PERSON>h<PERSON><PERSON> chữ ký số hoặc dòng chứ ký", "Common.Views.Protection.txtAddPwd": "Add password", "Common.Views.Protection.txtChangePwd": "Change password", "Common.Views.Protection.txtDeletePwd": "Delete password", "Common.Views.Protection.txtEncrypt": "Encrypt", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON><PERSON> chữ ký số", "Common.Views.Protection.txtSignature": "<PERSON><PERSON> ký", "Common.Views.Protection.txtSignatureLine": "Add signature line", "Common.Views.RecentFiles.txtOpenRecent": "Open recent", "Common.Views.RenameDialog.textName": "<PERSON><PERSON><PERSON>", "Common.Views.RenameDialog.txtInvalidName": "<PERSON><PERSON><PERSON> tệ<PERSON> không đ<PERSON><PERSON><PERSON> chứa bất kỳ ký tự nào sau đây:", "Common.Views.ReviewChanges.hintNext": "<PERSON><PERSON><PERSON> thay đổi tiếp theo", "Common.Views.ReviewChanges.hintPrev": "<PERSON><PERSON><PERSON> thay đổi trư<PERSON><PERSON> đó", "Common.Views.ReviewChanges.mniFromFile": "<PERSON><PERSON><PERSON> li<PERSON>u từ tệp", "Common.Views.ReviewChanges.mniFromStorage": "Document from storage", "Common.Views.ReviewChanges.mniFromUrl": "Document from URL", "Common.Views.ReviewChanges.mniMMFromFile": "From file", "Common.Views.ReviewChanges.mniMMFromStorage": "From storage", "Common.Views.ReviewChanges.mniMMFromUrl": "From URL", "Common.Views.ReviewChanges.mniSettings": "Comparison settings", "Common.Views.ReviewChanges.strFast": "Fast", "Common.Views.ReviewChanges.strFastDesc": "Đồng chỉnh sửa thời gian thực. Tất cả các thay đổi được lưu một cách tự động", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Use the 'Save' button to sync the changes you and others make.", "Common.Views.ReviewChanges.textEnable": "Enable", "Common.Views.ReviewChanges.textWarnTrackChanges": "Track Changes will be switched ON for all users with full access. The next time anyone opens the doc, Track Changes will remain enabled.", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "Enable track changes for everyone?", "Common.Views.ReviewChanges.tipAcceptCurrent": "<PERSON><PERSON><PERSON> nhận thay đổi hiện tại", "Common.Views.ReviewChanges.tipCoAuthMode": "Đặt chế độ đồng chỉnh sửa", "Common.Views.ReviewChanges.tipCombine": "Combine current document with another one", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON><PERSON><PERSON> nh<PERSON>n x<PERSON>t", "Common.Views.ReviewChanges.tipCommentRemCurrent": "<PERSON><PERSON><PERSON> nhận xét hiện tại", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON><PERSON> quyết nhận xét", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "<PERSON><PERSON><PERSON><PERSON> quyết nhận xét hiện tại", "Common.Views.ReviewChanges.tipCompare": "Compare current document with another one", "Common.Views.ReviewChanges.tipHistory": "Show version history", "Common.Views.ReviewChanges.tipMailRecepients": "Mail merge", "Common.Views.ReviewChanges.tipRejectCurrent": "Từ chối thay đổi hiện tại", "Common.Views.ReviewChanges.tipReview": "<PERSON> đ<PERSON>i", "Common.Views.ReviewChanges.tipReviewView": "<PERSON><PERSON><PERSON> chế độ bạn muốn các thay đổi đư<PERSON><PERSON> hiển thị", "Common.Views.ReviewChanges.tipSetDocLang": "Đặt ngôn ngữ tài liệu", "Common.Views.ReviewChanges.tipSetSpelling": "<PERSON><PERSON><PERSON> tra ch<PERSON>h tả", "Common.Views.ReviewChanges.tipSharing": "Manage document access rights", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "<PERSON><PERSON><PERSON> nhận mọi thay đổi", "Common.Views.ReviewChanges.txtAcceptChanges": "<PERSON><PERSON><PERSON> nhận thay đổi", "Common.Views.ReviewChanges.txtAcceptCurrent": "<PERSON><PERSON><PERSON> nhận thay đổi hiện tại", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "Đ<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Chế độ đồng chỉnh sửa", "Common.Views.ReviewChanges.txtCombine": "Combine", "Common.Views.ReviewChanges.txtCommentRemAll": "<PERSON><PERSON><PERSON> tất cả nhận xét", "Common.Views.ReviewChanges.txtCommentRemCurrent": "<PERSON><PERSON><PERSON> nhận xét hiện tại", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON><PERSON><PERSON> nhận xét của tôi", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "<PERSON><PERSON><PERSON> nhận xét hiện tại của tôi", "Common.Views.ReviewChanges.txtCommentRemove": "Delete", "Common.Views.ReviewChanges.txtCommentResolve": "Resolve", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON><PERSON> quyết tất cả nhận xét", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "<PERSON><PERSON><PERSON><PERSON> quyết nhận xét hiện tại", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON><PERSON><PERSON> quyết nhận xét của tôi", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "<PERSON><PERSON><PERSON><PERSON> quyết nhận xét hiện tại của tôi", "Common.Views.ReviewChanges.txtCompare": "Compare", "Common.Views.ReviewChanges.txtDocLang": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtEditing": "Chỉnh sửa", "Common.Views.ReviewChanges.txtFinal": "<PERSON>ấ<PERSON> cả thay đổi đ<PERSON><PERSON><PERSON> chấp nh<PERSON> {0}", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Version history", "Common.Views.ReviewChanges.txtMailMerge": "Mail Merge", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON><PERSON> cả thay đổi {0}", "Common.Views.ReviewChanges.txtMarkupCap": "Markup and balloons", "Common.Views.ReviewChanges.txtMarkupSimple": "All changes {0}<br>No balloons", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "Only markup", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON> tiếp", "Common.Views.ReviewChanges.txtOff": "OFF for me", "Common.Views.ReviewChanges.txtOffGlobal": "OFF for me and everyone", "Common.Views.ReviewChanges.txtOn": "ON for me", "Common.Views.ReviewChanges.txtOnGlobal": "ON for me and everyone", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON><PERSON> cả thay đổi bị từ chối {0}", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "<PERSON><PERSON><PERSON> thay đổi trư<PERSON><PERSON> đó", "Common.Views.ReviewChanges.txtPreview": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON> chối", "Common.Views.ReviewChanges.txtRejectAll": "Từ chối tất cả thay đổi", "Common.Views.ReviewChanges.txtRejectChanges": "Từ chối thay đổi", "Common.Views.ReviewChanges.txtRejectCurrent": "Từ chối thay đổi hiện tại", "Common.Views.ReviewChanges.txtSharing": "Sharing", "Common.Views.ReviewChanges.txtSpelling": "<PERSON><PERSON><PERSON> tra ch<PERSON>h tả", "Common.Views.ReviewChanges.txtTurnon": "<PERSON> đ<PERSON>i", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON> độ <PERSON> thị", "Common.Views.ReviewChangesDialog.textTitle": "<PERSON><PERSON> lại <PERSON>hay đ<PERSON>i", "Common.Views.ReviewChangesDialog.txtAccept": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtAcceptAll": "<PERSON><PERSON><PERSON> nhận mọi thay đổi", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "<PERSON><PERSON><PERSON> nhận thay đổi hiện tại", "Common.Views.ReviewChangesDialog.txtNext": "<PERSON><PERSON><PERSON> thay đổi tiếp theo", "Common.Views.ReviewChangesDialog.txtPrev": "<PERSON><PERSON><PERSON> thay đổi trư<PERSON><PERSON> đó", "Common.Views.ReviewChangesDialog.txtReject": "<PERSON><PERSON> chối", "Common.Views.ReviewChangesDialog.txtRejectAll": "Từ chối tất cả thay đổi", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "Từ chối thay đổi hiện tại", "Common.Views.ReviewPopover.textAdd": "thêm", "Common.Views.ReviewPopover.textAddReply": "Add reply", "Common.Views.ReviewPopover.textCancel": "Cancel", "Common.Views.ReviewPopover.textClose": "Đ<PERSON><PERSON>", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "<PERSON><PERSON><PERSON><PERSON> nhận xét của bạn ở đây", "Common.Views.ReviewPopover.textFollowMove": "Follow move", "Common.Views.ReviewPopover.textMention": "+mention sẽ cấp quyền truy cập tài liệu và gửi email", "Common.Views.ReviewPopover.textMentionNotify": "+mention sẽ thông báo cho người dùng qua email", "Common.Views.ReviewPopover.textOpenAgain": "Open again", "Common.Views.ReviewPopover.textReply": "Reply", "Common.Views.ReviewPopover.textResolve": "Resolve", "Common.Views.ReviewPopover.textViewResolved": "Bạn không có quyền mở lại nhận xét", "Common.Views.ReviewPopover.txtAccept": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtDeleteTip": "Delete", "Common.Views.ReviewPopover.txtEditTip": "Edit", "Common.Views.ReviewPopover.txtReject": "Reject", "Common.Views.SaveAsDlg.textLoading": "Loading", "Common.Views.SaveAsDlg.textTitle": "Folder for save", "Common.Views.SearchPanel.textCaseSensitive": "Case sensitive", "Common.Views.SearchPanel.textCloseSearch": "Close find", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "Find", "Common.Views.SearchPanel.textFindAndReplace": "Find and replace", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} <PERSON><PERSON><PERSON><PERSON> mục thay thế hoàn .", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} k<PERSON><PERSON><PERSON> mục đã thay thế. {2} kho<PERSON><PERSON> mục còn lại đang bị khóa bởi (những) người dùng khác", "Common.Views.SearchPanel.textReplace": "Replace", "Common.Views.SearchPanel.textReplaceAll": "Replace All", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "<PERSON><PERSON><PERSON> kiếm đã dừng lại", "Common.Views.SearchPanel.textSearchResults": "<PERSON><PERSON><PERSON> qu<PERSON> tìm kiếm: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textTooManyResults": "There are too many results to show here", "Common.Views.SearchPanel.textWholeWords": "Whole words only", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "Previous result", "Common.Views.SelectFileDlg.textLoading": "Loading", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON> nguồn dữ liệu", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Bold", "Common.Views.SignDialog.textCertificate": "Certificate", "Common.Views.SignDialog.textChange": "Change", "Common.Views.SignDialog.textInputName": "Input signer name", "Common.Views.SignDialog.textItalic": "Italic", "Common.Views.SignDialog.textNameError": "Signer name must not be empty.", "Common.Views.SignDialog.textPurpose": "Purpose for signing this document", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "Select image", "Common.Views.SignDialog.textSignature": "Signature looks as", "Common.Views.SignDialog.textTitle": "<PERSON><PERSON> tài li<PERSON>u", "Common.Views.SignDialog.textUseImage": "hoặc bấm 'Chọn ảnh' để dùng ảnh như là chữ ký", "Common.Views.SignDialog.textValid": "Valid from %1 to %2", "Common.Views.SignDialog.tipFontName": "Font name", "Common.Views.SignDialog.tipFontSize": "Font size", "Common.Views.SignSettingsDialog.textAllowComment": "<PERSON> phép người ký đ<PERSON><PERSON><PERSON> thêm nhận xét vào hộp thoại chữ ký", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "Suggested signer's e-mail", "Common.Views.SignSettingsDialog.textInfoName": "Suggested signer", "Common.Views.SignSettingsDialog.textInfoTitle": "Suggested signer's title", "Common.Views.SignSettingsDialog.textInstructions": "Instructions for signer", "Common.Views.SignSettingsDialog.textShowDate": "Show sign date in signature line", "Common.Views.SignSettingsDialog.textTitle": "Signature setup", "Common.Views.SignSettingsDialog.txtEmpty": "This field is required", "Common.Views.SymbolTableDialog.textCharacter": "Character", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX value", "Common.Views.SymbolTableDialog.textCopyright": "Copyright sign", "Common.Views.SymbolTableDialog.textDCQuote": "Closing double quote", "Common.Views.SymbolTableDialog.textDOQuote": "Opening double quote", "Common.Views.SymbolTableDialog.textEllipsis": "Horizontal ellipsis", "Common.Views.SymbolTableDialog.textEmDash": "Em dash", "Common.Views.SymbolTableDialog.textEmSpace": "Em space", "Common.Views.SymbolTableDialog.textEnDash": "En dash", "Common.Views.SymbolTableDialog.textEnSpace": "En space", "Common.Views.SymbolTableDialog.textFont": "Font", "Common.Views.SymbolTableDialog.textNBHyphen": "Non-breaking hyphen", "Common.Views.SymbolTableDialog.textNBSpace": "No-break space", "Common.Views.SymbolTableDialog.textPilcrow": "Pilcrow sign", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em space", "Common.Views.SymbolTableDialog.textRange": "Range", "Common.Views.SymbolTableDialog.textRecent": "Recently used symbols", "Common.Views.SymbolTableDialog.textRegistered": "Registered sign", "Common.Views.SymbolTableDialog.textSCQuote": "Closing single quote", "Common.Views.SymbolTableDialog.textSection": "Section sign", "Common.Views.SymbolTableDialog.textShortcut": "Shortcut key", "Common.Views.SymbolTableDialog.textSHyphen": "Soft hyphen", "Common.Views.SymbolTableDialog.textSOQuote": "Opening single quote", "Common.Views.SymbolTableDialog.textSpecial": "Special characters", "Common.Views.SymbolTableDialog.textSymbols": "Symbols", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Trademark symbol ", "Common.Views.UserNameDialog.textDontShow": "Don't ask me again", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label must not be empty.", "DE.Controllers.DocProtection.txtIsProtectedComment": "Tài liệu đư<PERSON>c bảo vệ. Bạn có thể chỉ nhận xét vào tài liệu này.", "DE.Controllers.DocProtection.txtIsProtectedForms": "Document is protected. You may only fill in forms in this document.", "DE.Controllers.DocProtection.txtIsProtectedTrack": "Document is protected. You may edit this document, but all changes will be tracked.", "DE.Controllers.DocProtection.txtIsProtectedView": "Document is protected. You may only view this document.", "DE.Controllers.DocProtection.txtWasProtectedComment": "Tài liệu đã được bảo vệ bởi người dùng khác.\nBạn có thể chỉ thêm được nhận xét vào tài liệu này", "DE.Controllers.DocProtection.txtWasProtectedForms": "Document has been protected by another user.\nYou may only fill in forms in this document.", "DE.Controllers.DocProtection.txtWasProtectedTrack": "Document has been protected by another user.\nYou may edit this document, but all changes will be tracked.", "DE.Controllers.DocProtection.txtWasProtectedView": "Document has been protected by another user.\nYou may only view this document.", "DE.Controllers.DocProtection.txtWasUnprotected": "Document has been unprotected.", "DE.Controllers.LeftMenu.leavePageText": "Tất cả các thay đổi chưa lưu trong tài liệu này sẽ bị mất.<br><PERSON><PERSON><PERSON><PERSON> và<PERSON> \"<PERSON><PERSON><PERSON>\" r<PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" để lưu chúng. <PERSON><PERSON><PERSON><PERSON> \"OK\" để loại bỏ tất cả các thay đổi chưa lưu.", "DE.Controllers.LeftMenu.newDocumentTitle": "<PERSON><PERSON><PERSON> li<PERSON>u không tên", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "DE.Controllers.LeftMenu.requestEditRightsText": "<PERSON><PERSON><PERSON> c<PERSON>u quyền chỉnh sửa...", "DE.Controllers.LeftMenu.textLoadHistory": "<PERSON><PERSON> tải lịch sử các phiên bản...", "DE.Controllers.LeftMenu.textNoTextFound": "<PERSON><PERSON><PERSON><PERSON> thể tìm thấy dữ liệu bạn đang tìm kiếm. Vui lòng điều chỉnh các tùy chọn tìm kiếm của bạn.", "DE.Controllers.LeftMenu.textReplaceSkipped": "<PERSON><PERSON> thực hiện thay thế. {0} lần xuất hiện đã bị bỏ qua.", "DE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON><PERSON> thực hiện tìm kiếm. Số lần thay thế: {0}", "DE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "DE.Controllers.LeftMenu.txtCompatible": "Tài liệu sẽ đượ<PERSON> lưu sang định dạng mới. Nó sẽ cho phép sử dụng tất cả các tính năng của trình chỉnh sửa nhưng có thể ảnh hưởng đến bố cục tài liệu.<br>Hãy sử dụng tùy chọn 'Tương thích' của cài đặt nâng cao nếu bạn muốn làm cho các tệp tương thích với các phiên bản MS Word cũ hơn.", "DE.Controllers.LeftMenu.txtUntitled": "Untitled", "DE.Controllers.LeftMenu.warnDownloadAs": "Nếu bạn tiếp tục lưu ở định dạng này tất cả các tính năng trừ văn bản sẽ bị mất.<br>Bạn có chắc là muốn tiếp tục?", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "{0} của bạn sẽ được chuyển đổi sang định dạng có thể chỉnh sửa được. Việc này có thể mất một lúc. Tài liệu thu được sẽ được tối ưu hóa để cho phép bạn chỉnh sửa văn bản, do đó, nó có thể trông không giống hệt {0} gốc, đặc biệt nếu tệp gốc chứa nhiều đồ họa.", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "If you continue saving in this format some of the formatting might be lost.<br>Are you sure you want to continue?", "DE.Controllers.LeftMenu.warnReplaceString": "{0} is not a valid special character for the replacement field.", "DE.Controllers.Main.applyChangesTextText": "<PERSON><PERSON> tải các thay đổi...", "DE.Controllers.Main.applyChangesTitleText": "<PERSON><PERSON> tải Thay đổi", "DE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "DE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON> quá thời gian chờ chuyển đổi.", "DE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON><PERSON><PERSON> \"OK\" để trở lại danh sách tài liệu.", "DE.Controllers.Main.criticalErrorTitle": "Lỗi", "DE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON> về không thành công.", "DE.Controllers.Main.downloadMergeText": "<PERSON><PERSON> tả<PERSON>...", "DE.Controllers.Main.downloadMergeTitle": "<PERSON><PERSON> t<PERSON> về", "DE.Controllers.Main.downloadTextText": "<PERSON><PERSON> tải tài liệu...", "DE.Controllers.Main.downloadTitleText": "<PERSON><PERSON> tải tài liệu...", "DE.Controllers.Main.errorAccessDeny": "Bạn đang cố gắng thực hiện hành động mà bạn không có quyền.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Server <PERSON><PERSON><PERSON> li<PERSON> củ<PERSON> bạn.", "DE.Controllers.Main.errorBadImageUrl": "URL hình ảnh không chính xác", "DE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the document.", "DE.Controllers.Main.errorCoAuthoringDisconnect": "<PERSON>ất kết nối server. K<PERSON>ông thể chỉnh sửa tài liệu ngay lúc này.", "DE.Controllers.Main.errorComboSeries": "To create a combination chart, select at least two series of data.", "DE.Controllers.Main.errorCompare": "<PERSON><PERSON><PERSON> năng So sánh tài liệu không sẵn dùng khi đồng chỉnh sửa", "DE.Controllers.Main.errorConnectToServer": "<PERSON><PERSON><PERSON><PERSON> thể lưu tài liệu. Vui lòng kiểm tra cài đặt kết nối hoặc liên hệ với quản trị viên của bạn.<br><PERSON><PERSON> bạn nhấp vào nút 'OK', bạn sẽ được nhắc tải xuống tài liệu.", "DE.Controllers.Main.errorDatabaseConnection": "Lỗi bên ngoài.<br>Lỗi kết nối cơ sở dữ liệu. <PERSON>ui lòng liên hệ bộ phận hỗ trợ trong trường hợp lỗi vẫn còn.", "DE.Controllers.Main.errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "DE.Controllers.Main.errorDataRange": "Phạm vi dữ liệu không ch<PERSON>h xác.", "DE.Controllers.Main.errorDefaultMessage": "Mã lỗi: %1", "DE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "DE.Controllers.Main.errorEditingDownloadas": "<PERSON><PERSON> xảy ra lỗi trong quá trình làm việc với tài liệu.<br><PERSON><PERSON>y sử dụng tùy chọn 'Tải xuống dưới dạng' để lưu bản sao lưu tệp vào ổ đĩa.", "DE.Controllers.Main.errorEditingSaveas": "An error occurred during the work with the document.<br>Use the 'Save as...' option to save the file backup copy to a drive.", "DE.Controllers.Main.errorEditProtectedRange": "You are not allowed to edit this selection because it is protected.", "DE.Controllers.Main.errorEmailClient": "No email client could be found.", "DE.Controllers.Main.errorEmptyTOC": "Start creating a table of contents by applying a heading style from the Styles gallery to the selected text.", "DE.Controllers.Main.errorFilePassProtect": "<PERSON><PERSON><PERSON> liệu được bảo vệ bằng mật khẩu và không thể mở được.", "DE.Controllers.Main.errorFileSizeExceed": "<PERSON><PERSON><PERSON> thướ<PERSON> tệp vượt quá giới hạn được đặt cho máy chủ của bạn.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Máy chủ Tài liệu của bạn để biết chi tiết.", "DE.Controllers.Main.errorForceSave": "An error occurred while saving the file. Please use the 'Download as' option to save the file to a drive or try again later.", "DE.Controllers.Main.errorInconsistentExt": "<PERSON><PERSON> x<PERSON>y ra lỗi khi mở tệp.<br><PERSON><PERSON><PERSON> dung tệp không khớp với phần mở rộng tệp.", "DE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "DE.Controllers.Main.errorInconsistentExtPdf": "<PERSON><PERSON><PERSON> ra lỗi khi mở tệp.<br><PERSON><PERSON>i dung tệp tương ứng với một trong các định dạng sau: pdf/djvu/xps/oxps, nhưng tệp có phần mở rộng không nhất quán: %1.", "DE.Controllers.Main.errorInconsistentExtPptx": "<PERSON><PERSON> x<PERSON>y ra lỗi khi mở tệp.<br><PERSON><PERSON><PERSON> dung tệp tương ứng với bản trình bà<PERSON> (ví dụ: pptx), nh<PERSON><PERSON> tệp có phần mở rộng không nhất quán: %1.", "DE.Controllers.Main.errorInconsistentExtXlsx": "<PERSON><PERSON> xảy ra lỗi khi mở tệp.<br><PERSON><PERSON><PERSON> dung tệp tương ứng với bảng t<PERSON> (ví dụ: xlsx), nh<PERSON><PERSON> tệp có phần mở rộng không nhất quán: %1.", "DE.Controllers.Main.errorKeyEncrypt": "Key descriptor k<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "DE.Controllers.Main.errorKeyExpire": "Key của descriptor đ<PERSON> hết hạn", "DE.Controllers.Main.errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "DE.Controllers.Main.errorMailMergeLoadFile": "<PERSON><PERSON><PERSON> không thành công", "DE.Controllers.Main.errorMailMergeSaveFile": "<PERSON>r<PERSON><PERSON> không thành công.", "DE.Controllers.Main.errorNoTOC": "There's no table of contents to update. You can insert one from the References tab.", "DE.Controllers.Main.errorPasswordIsNotCorrect": "The password you supplied is not correct.<br>Verify that the CAPS LOCK key is off and be sure to use the correct capitalization.", "DE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON><PERSON> không thành công.", "DE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "DE.Controllers.Main.errorServerVersion": "<PERSON><PERSON><PERSON> bản trình chỉnh sửa này đã được cập nhật. Trang sẽ được tải lại để áp dụng các thay đổi.", "DE.Controllers.Main.errorSessionAbsolute": "Phiên chỉnh sửa tài liệu đã hết hạn. <PERSON><PERSON> lòng tải lại trang.", "DE.Controllers.Main.errorSessionIdle": "Tài liệu đã không được chỉnh sửa trong một thời gian khá dài. <PERSON>ui lòng tải lại trang.", "DE.Controllers.Main.errorSessionToken": "<PERSON><PERSON><PERSON> nối với server bị gi<PERSON> đ<PERSON>. <PERSON><PERSON> lòng tải lại trang.", "DE.Controllers.Main.errorSetPassword": "Password could not be set.", "DE.Controllers.Main.errorStockChart": "Thứ tự hàng không chính xác. <PERSON><PERSON> xây dựng một biểu đồ chứng khoán đặt dữ liệu trên giấy theo thứ tự sau:<br>gi<PERSON> mở phiên, gi<PERSON> cao nh<PERSON>t, g<PERSON><PERSON> thấ<PERSON> nhất, gi<PERSON> đóng phiên.", "DE.Controllers.Main.errorSubmit": "Submit failed.", "DE.Controllers.Main.errorTextFormWrongFormat": "The value entered does not match the format of the field.", "DE.Controllers.Main.errorToken": "To<PERSON> bảo mật tài liệu không đư<PERSON><PERSON> tạo đúng.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Server <PERSON><PERSON><PERSON> liệu của bạn.", "DE.Controllers.Main.errorTokenExpire": "To<PERSON> bảo mật tài liệu đã hết hạn.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Server <PERSON><PERSON><PERSON> liệu của bạn.", "DE.Controllers.Main.errorUpdateVersion": "<PERSON><PERSON><PERSON> bản tệp này đã được thay đổi. Trang này sẽ được tải lại.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "Kết nối đã được khôi phục và phiên bản tệp đã được thay đổi.<br>Tr<PERSON><PERSON><PERSON> khi có thể tiếp tục làm việ<PERSON>, bạn cần tải xuống tệp hoặc sao chép nội dung của tệp để đảm bảo không có gì bị mất, sau đó tải lại trang này.", "DE.Controllers.Main.errorUserDrop": "<PERSON><PERSON><PERSON><PERSON> thể truy cập tệp ngay lúc n<PERSON>y.", "DE.Controllers.Main.errorUsersExceed": "<PERSON><PERSON> vư<PERSON>t quá số người dùng đ<PERSON><PERSON><PERSON> phép của gói dịch vụ này", "DE.Controllers.Main.errorViewerDisconnect": "<PERSON><PERSON>t kết nối. Bạn vẫn có thể xem tài liệu,<br>nhưng không thể tải về hoặc in cho đến khi kết nối được khôi phục.", "DE.Controllers.Main.leavePageText": "<PERSON>ạn có những thay đổi chưa lưu trong tài liệu này. <PERSON>h<PERSON><PERSON> vào \"Ở lại Trang này\", sau đ<PERSON> \"<PERSON><PERSON><PERSON>\" để lưu chúng. <PERSON><PERSON><PERSON><PERSON> vào \"Rời trang này\" để bỏ tất cả các thay đổi chưa lưu.", "DE.Controllers.Main.leavePageTextOnClose": "All unsaved changes in this document will be lost.<br> Click \"Cancel\" then \"Save\" to save them. Click \"OK\" to discard all the unsaved changes.", "DE.Controllers.Main.loadFontsTextText": "<PERSON><PERSON> tải dữ liệu...", "DE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON> t<PERSON> liệu", "DE.Controllers.Main.loadFontTextText": "<PERSON><PERSON> tải dữ liệu...", "DE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON> t<PERSON> liệu", "DE.Controllers.Main.loadImagesTextText": "<PERSON><PERSON> tải h<PERSON>nh <PERSON>...", "DE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON> tả<PERSON> h<PERSON>nh <PERSON>", "DE.Controllers.Main.loadImageTextText": "<PERSON><PERSON> tải h<PERSON>nh <PERSON>...", "DE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON> tả<PERSON> h<PERSON>nh <PERSON>", "DE.Controllers.Main.loadingDocumentTextText": "<PERSON><PERSON> tải tài liệu...", "DE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON> tải tài liệu", "DE.Controllers.Main.mailMergeLoadFileText": "<PERSON><PERSON> tải nguồn dữ liệu...", "DE.Controllers.Main.mailMergeLoadFileTitle": "<PERSON><PERSON> tải nguồn dữ liệu", "DE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "DE.Controllers.Main.openErrorText": "<PERSON><PERSON><PERSON> ra lỗi khi mở tệp", "DE.Controllers.Main.openTextText": "<PERSON><PERSON> mở tài liệu...", "DE.Controllers.Main.openTitleText": "<PERSON><PERSON> mở tài liệu...", "DE.Controllers.Main.printTextText": "<PERSON><PERSON> in Tài liệu...", "DE.Controllers.Main.printTitleText": "<PERSON><PERSON> in Tài liệu", "DE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON> l<PERSON>", "DE.Controllers.Main.requestEditFailedMessageText": "Hiện có ai đó đang chỉnh sửa tài liệu này. <PERSON><PERSON> lòng thử lại sau.", "DE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> bị từ chối", "DE.Controllers.Main.saveErrorText": "<PERSON><PERSON><PERSON> ra lỗi khi lưu tệp", "DE.Controllers.Main.saveErrorTextDesktop": "This file cannot be saved or created.<br>Possible reasons are: <br>1. The file is read-only. <br>2. The file is being edited by other users. <br>3. The disk is full or corrupted.", "DE.Controllers.Main.saveTextText": "<PERSON><PERSON> lưu tài liệu...", "DE.Controllers.Main.saveTitleText": "<PERSON><PERSON> lưu tài liệu...", "DE.Controllers.Main.savingText": "Saving", "DE.Controllers.Main.scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please reload the page.", "DE.Controllers.Main.sendMergeText": "<PERSON><PERSON>...", "DE.Controllers.Main.sendMergeTitle": "<PERSON><PERSON>", "DE.Controllers.Main.splitDividerErrorText": "Số hàng phải là ước của %1.", "DE.Controllers.Main.splitMaxColsErrorText": "Số cột phải nhỏ hơn %1.", "DE.Controllers.Main.splitMaxRowsErrorText": "S<PERSON> hàng phải nhỏ hơn %1.", "DE.Controllers.Main.textAnonymous": "Nặc danh", "DE.Controllers.Main.textAnyone": "Anyone", "DE.Controllers.Main.textApplyAll": "Apply to all equations", "DE.Controllers.Main.textBuyNow": "<PERSON><PERSON><PERSON> c<PERSON>p trang web", "DE.Controllers.Main.textChangesSaved": "<PERSON><PERSON> lưu mọi thay đổi", "DE.Controllers.Main.textClose": "Close", "DE.Controllers.Main.textCloseTip": "<PERSON><PERSON><PERSON><PERSON> để đóng gợi ý", "DE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "DE.Controllers.Main.textContactUs": "<PERSON><PERSON><PERSON> hệ bộ phận bán hàng", "DE.Controllers.Main.textContinue": "Continue", "DE.Controllers.Main.textConvertEquation": "This equation was created with an old version of the equation editor which is no longer supported. To edit it, convert the equation to the Office Math ML format.<br>Convert now?", "DE.Controllers.Main.textCustomLoader": "Please note that according to the terms of the license you are not entitled to change the loader.<br>Please contact our Sales Department to get a quote.", "DE.Controllers.Main.textDisconnect": "Connection is lost", "DE.Controllers.Main.textGuest": "Guest", "DE.Controllers.Main.textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "DE.Controllers.Main.textLearnMore": "Learn More", "DE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON> tải tài liệu", "DE.Controllers.Main.textLongName": "Enter a name that is less than 128 characters.", "DE.Controllers.Main.textNoLicenseTitle": "<PERSON><PERSON><PERSON> bản mã nguồn mở ONLYOFFICE", "DE.Controllers.Main.textPaidFeature": "Paid feature", "DE.Controllers.Main.textReconnect": "Connection is restored", "DE.Controllers.Main.textRemember": "<PERSON><PERSON> nhớ lựa chọn của tôi cho tất cả tệp", "DE.Controllers.Main.textRememberMacros": "Remember my choice for all macros", "DE.Controllers.Main.textRenameError": "User name must not be empty.", "DE.Controllers.Main.textRenameLabel": "<PERSON><PERSON><PERSON><PERSON> tên để cộng tác", "DE.Controllers.Main.textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "DE.Controllers.Main.textShape": "<PERSON><PERSON><PERSON> d<PERSON>", "DE.Controllers.Main.textSignature": "Signature", "DE.Controllers.Main.textStrict": "<PERSON><PERSON> độ nghiêm ngặt", "DE.Controllers.Main.textText": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "DE.Controllers.Main.textTryUndoRedo": "<PERSON><PERSON><PERSON> chức năng Hoàn tác/<PERSON>à<PERSON> lại bị vô hiệu hóa cho chế độ đồng chỉnh sửa Nhanh.<br>Nhấp vào nút 'Chế độ nghiêm ngặt' để chuyển sang chế độ đồng chỉnh sửa Nghiêm ngặt để chỉnh sửa các tệp mà không có sự can thiệp của người dùng khác và gửi các thay đổi của bạn chỉ sau khi bạn đã lưu. Bạn có thể chuyển đổi giữa các chế độ đồng chỉnh sửa bằng cách sử dụng Cài đặt Nâng cao trình biên tập.", "DE.Controllers.Main.textTryUndoRedoWarn": "<PERSON><PERSON><PERSON> năng <PERSON> tác/<PERSON><PERSON><PERSON> lại bị vô hiệu hóa cho chế độ đồng chỉnh sử<PERSON>.", "DE.Controllers.Main.textUndo": "Undo", "DE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "DE.Controllers.Main.textUpdating": "Updating", "DE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> hết hạn", "DE.Controllers.Main.titleLicenseNotActive": "License not active", "DE.Controllers.Main.titleServerVersion": "<PERSON><PERSON> cập nhật trình chỉnh sửa", "DE.Controllers.Main.titleUpdateVersion": "<PERSON><PERSON> thay đổi phiên bản", "DE.Controllers.Main.txtAbove": "above", "DE.Controllers.Main.txtArt": "<PERSON><PERSON><PERSON> bản của bạn ở đây", "DE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON> d<PERSON>ng c<PERSON> bản", "DE.Controllers.Main.txtBelow": "below", "DE.Controllers.Main.txtBookmarkError": "Error! Bookmark not defined.", "DE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtCallouts": "Callout", "DE.Controllers.Main.txtCharts": "<PERSON><PERSON><PERSON><PERSON> đồ", "DE.Controllers.Main.txtChoose": "Choose an item", "DE.Controllers.Main.txtClickToLoad": "Click to load image", "DE.Controllers.Main.txtCurrentDocument": "Current document", "DE.Controllers.Main.txtDiagramTitle": "Tiêu đề biểu đồ", "DE.Controllers.Main.txtEditingMode": "Đặt chế độ chỉnh sửa...", "DE.Controllers.Main.txtEndOfFormula": "Unexpected end of formula", "DE.Controllers.Main.txtEnterDate": "Enter a date", "DE.Controllers.Main.txtErrorLoadHistory": "<PERSON><PERSON><PERSON> lịch sử không thành công", "DE.Controllers.Main.txtEvenPage": "Even page", "DE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON> tên có hình vẽ", "DE.Controllers.Main.txtFirstPage": "First page", "DE.Controllers.Main.txtFooter": "Footer", "DE.Controllers.Main.txtFormulaNotInTable": "The Formula Not In Table", "DE.Controllers.Main.txtHeader": "Header", "DE.Controllers.Main.txtHyperlink": "Hyperlink", "DE.Controllers.Main.txtIndTooLarge": "Index too large", "DE.Controllers.Main.txtLines": "Đường kẻ", "DE.Controllers.Main.txtMainDocOnly": "Error! Main document only.", "DE.Controllers.Main.txtMath": "<PERSON><PERSON>", "DE.Controllers.Main.txtMissArg": "Missing argument", "DE.Controllers.Main.txtMissOperator": "Missing operator", "DE.Controllers.Main.txtNeedSynchronize": "<PERSON><PERSON><PERSON> c<PERSON> cập nh<PERSON>t", "DE.Controllers.Main.txtNone": "None", "DE.Controllers.Main.txtNoTableOfContents": "There are no headings in the document. Apply a heading style to the text so that it appears in the table of contents.", "DE.Controllers.Main.txtNoTableOfFigures": "<PERSON><PERSON><PERSON><PERSON> đầu vào nào trong mục lục hình được tìm thấy", "DE.Controllers.Main.txtNoText": "Error! No text of specified style in document.", "DE.Controllers.Main.txtNotInTable": "Is not in table", "DE.Controllers.Main.txtNotValidBookmark": "Error! Not a valid bookmark self-reference.", "DE.Controllers.Main.txtOddPage": "Odd page", "DE.Controllers.Main.txtOnPage": "on page", "DE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON> chữ nhật", "DE.Controllers.Main.txtSameAsPrev": "Same as previous", "DE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "DE.Controllers.Main.txtScheme_Aspect": "Aspect", "DE.Controllers.Main.txtScheme_Blue": "Blue", "DE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "DE.Controllers.Main.txtScheme_Blue_II": "Blue II", "DE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "DE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "DE.Controllers.Main.txtScheme_Green": "Green", "DE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "DE.Controllers.Main.txtScheme_Marquee": "Marquee", "DE.Controllers.Main.txtScheme_Median": "Median", "DE.Controllers.Main.txtScheme_Office": "Office", "DE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "DE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "DE.Controllers.Main.txtScheme_Orange": "Orange", "DE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "DE.Controllers.Main.txtScheme_Paper": "Paper", "DE.Controllers.Main.txtScheme_Red": "Red", "DE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "DE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "DE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "DE.Controllers.Main.txtScheme_Violet": "Violet", "DE.Controllers.Main.txtScheme_Violet_II": "Violet II", "DE.Controllers.Main.txtScheme_Yellow": "Yellow", "DE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "DE.Controllers.Main.txtSection": "-Section", "DE.Controllers.Main.txtSeries": "Chuỗi", "DE.Controllers.Main.txtShape_accentBorderCallout1": "Line callout 1 (Border and accent bar)", "DE.Controllers.Main.txtShape_accentBorderCallout2": "Line callout 2 (Border and accent bar)", "DE.Controllers.Main.txtShape_accentBorderCallout3": "Line callout 3 (Border and accent bar)", "DE.Controllers.Main.txtShape_accentCallout1": "Line callout 1 (Accent bar)", "DE.Controllers.Main.txtShape_accentCallout2": "Line callout 2 (Accent bar)", "DE.Controllers.Main.txtShape_accentCallout3": "Line callout 3 (Accent bar)", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "Back or previous button", "DE.Controllers.Main.txtShape_actionButtonBeginning": "Beginning button", "DE.Controllers.Main.txtShape_actionButtonBlank": "Blank button", "DE.Controllers.Main.txtShape_actionButtonDocument": "Document button", "DE.Controllers.Main.txtShape_actionButtonEnd": "End button", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "Forward or next button", "DE.Controllers.Main.txtShape_actionButtonHelp": "Help button", "DE.Controllers.Main.txtShape_actionButtonHome": "Home button", "DE.Controllers.Main.txtShape_actionButtonInformation": "Information button", "DE.Controllers.Main.txtShape_actionButtonMovie": "Movie button", "DE.Controllers.Main.txtShape_actionButtonReturn": "Return button", "DE.Controllers.Main.txtShape_actionButtonSound": "Sound button", "DE.Controllers.Main.txtShape_arc": "Arc", "DE.Controllers.Main.txtShape_bentArrow": "Bent arrow", "DE.Controllers.Main.txtShape_bentConnector5": "Elbow connector", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "Elbow arrow connector", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Elbow double-arrow connector", "DE.Controllers.Main.txtShape_bentUpArrow": "Bent up arrow", "DE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_blockArc": "Block arc", "DE.Controllers.Main.txtShape_borderCallout1": "Line callout 1", "DE.Controllers.Main.txtShape_borderCallout2": "Line callout 2", "DE.Controllers.Main.txtShape_borderCallout3": "Line callout 3", "DE.Controllers.Main.txtShape_bracePair": "Double brace", "DE.Controllers.Main.txtShape_callout1": "Line callout 1 (No border)", "DE.Controllers.Main.txtShape_callout2": "Line callout 2 (No border)", "DE.Controllers.Main.txtShape_callout3": "Line Callout 3 (No border)", "DE.Controllers.Main.txtShape_can": "Can", "DE.Controllers.Main.txtShape_chevron": "Chevron", "DE.Controllers.Main.txtShape_chord": "Chord", "DE.Controllers.Main.txtShape_circularArrow": "Circular arrow", "DE.Controllers.Main.txtShape_cloud": "Cloud", "DE.Controllers.Main.txtShape_cloudCallout": "Cloud callout", "DE.Controllers.Main.txtShape_corner": "Corner", "DE.Controllers.Main.txtShape_cube": "C<PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3": "Curved connector", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Curved arrow connector", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Curved double-arrow connector", "DE.Controllers.Main.txtShape_curvedDownArrow": "Curved down arrow", "DE.Controllers.Main.txtShape_curvedLeftArrow": "Curved left arrow", "DE.Controllers.Main.txtShape_curvedRightArrow": "Curved right arrow", "DE.Controllers.Main.txtShape_curvedUpArrow": "Curved up arrow", "DE.Controllers.Main.txtShape_decagon": "Decagon", "DE.Controllers.Main.txtShape_diagStripe": "Diagonal stripe", "DE.Controllers.Main.txtShape_diamond": "Diamond", "DE.Controllers.Main.txtShape_dodecagon": "Dodecagon", "DE.Controllers.Main.txtShape_donut": "Donut", "DE.Controllers.Main.txtShape_doubleWave": "Double wave", "DE.Controllers.Main.txtShape_downArrow": "Down arrow", "DE.Controllers.Main.txtShape_downArrowCallout": "Down arrow callout", "DE.Controllers.Main.txtShape_ellipse": "Ellipse", "DE.Controllers.Main.txtShape_ellipseRibbon": "Curved down ribbon", "DE.Controllers.Main.txtShape_ellipseRibbon2": "Curved up ribbon", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "Flowchart: Alternate process", "DE.Controllers.Main.txtShape_flowChartCollate": "Flowchart: Collate", "DE.Controllers.Main.txtShape_flowChartConnector": "Flowchart: Connector", "DE.Controllers.Main.txtShape_flowChartDecision": "Flowchart: Decision", "DE.Controllers.Main.txtShape_flowChartDelay": "Flowchart: Delay", "DE.Controllers.Main.txtShape_flowChartDisplay": "Flowchart: Display", "DE.Controllers.Main.txtShape_flowChartDocument": "Flowchart: Document", "DE.Controllers.Main.txtShape_flowChartExtract": "Flowchart: Extract", "DE.Controllers.Main.txtShape_flowChartInputOutput": "Flowchart: Data", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "Flowchart: Internal storage", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flowchart: Magnetic disk", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flowchart: Direct access storage", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "Flowchart: Sequential access storage", "DE.Controllers.Main.txtShape_flowChartManualInput": "Flowchart: Manual input", "DE.Controllers.Main.txtShape_flowChartManualOperation": "Flowchart: Manual operation", "DE.Controllers.Main.txtShape_flowChartMerge": "Flowchart: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartMultidocument": "Flowchart: Multidocument ", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flowchart: Off-page connector", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flowchart: Stored data", "DE.Controllers.Main.txtShape_flowChartOr": "Flowchart: Or", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flowchart: Predefined process", "DE.Controllers.Main.txtShape_flowChartPreparation": "Flowchart: Preparation", "DE.Controllers.Main.txtShape_flowChartProcess": "Flowchart: Process", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "Flowchart: Card", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "Flowchart: Punched tape", "DE.Controllers.Main.txtShape_flowChartSort": "Flowchart: Sort", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "Flowchart: Summing junction", "DE.Controllers.Main.txtShape_flowChartTerminator": "Flowchart: Terminator", "DE.Controllers.Main.txtShape_foldedCorner": "Folded corner", "DE.Controllers.Main.txtShape_frame": "<PERSON>ame", "DE.Controllers.Main.txtShape_halfFrame": "Half frame", "DE.Controllers.Main.txtShape_heart": "Heart", "DE.Controllers.Main.txtShape_heptagon": "Heptagon", "DE.Controllers.Main.txtShape_hexagon": "Hexagon", "DE.Controllers.Main.txtShape_homePlate": "Pentagon", "DE.Controllers.Main.txtShape_horizontalScroll": "Horizontal scroll", "DE.Controllers.Main.txtShape_irregularSeal1": "Explosion 1", "DE.Controllers.Main.txtShape_irregularSeal2": "Explosion 2", "DE.Controllers.Main.txtShape_leftArrow": "Left arrow", "DE.Controllers.Main.txtShape_leftArrowCallout": "Left arrow callout", "DE.Controllers.Main.txtShape_leftBrace": "Left brace", "DE.Controllers.Main.txtShape_leftBracket": "Left bracket", "DE.Controllers.Main.txtShape_leftRightArrow": "Left right arrow", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "Left right arrow callout", "DE.Controllers.Main.txtShape_leftRightUpArrow": "Left right up arrow", "DE.Controllers.Main.txtShape_leftUpArrow": "Left up arrow", "DE.Controllers.Main.txtShape_lightningBolt": "Lightning bolt", "DE.Controllers.Main.txtShape_line": "Line", "DE.Controllers.Main.txtShape_lineWithArrow": "Arrow", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "Double arrow", "DE.Controllers.Main.txtShape_mathDivide": "Division", "DE.Controllers.Main.txtShape_mathEqual": "Equal", "DE.Controllers.Main.txtShape_mathMinus": "Minus", "DE.Controllers.Main.txtShape_mathMultiply": "Multiply", "DE.Controllers.Main.txtShape_mathNotEqual": "Not equal", "DE.Controllers.Main.txtShape_mathPlus": "Plus", "DE.Controllers.Main.txtShape_moon": "Moon", "DE.Controllers.Main.txtShape_noSmoking": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> \"<PERSON>hông\"", "DE.Controllers.Main.txtShape_notchedRightArrow": "Notched right arrow", "DE.Controllers.Main.txtShape_octagon": "Octagon", "DE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "DE.Controllers.Main.txtShape_pentagon": "Pentagon", "DE.Controllers.Main.txtShape_pie": "Pie", "DE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_plus": "Plus", "DE.Controllers.Main.txtShape_polyline1": "Scribble", "DE.Controllers.Main.txtShape_polyline2": "Freeform", "DE.Controllers.Main.txtShape_quadArrow": "Quad arrow", "DE.Controllers.Main.txtShape_quadArrowCallout": "Quad arrow callout", "DE.Controllers.Main.txtShape_rect": "Rectangle", "DE.Controllers.Main.txtShape_ribbon": "Down ribbon", "DE.Controllers.Main.txtShape_ribbon2": "Up ribbon", "DE.Controllers.Main.txtShape_rightArrow": "Right Arrow", "DE.Controllers.Main.txtShape_rightArrowCallout": "Right arrow callout", "DE.Controllers.Main.txtShape_rightBrace": "Right brace", "DE.Controllers.Main.txtShape_rightBracket": "Right bracket", "DE.Controllers.Main.txtShape_round1Rect": "Round single corner rectangle", "DE.Controllers.Main.txtShape_round2DiagRect": "Round diagonal corner rectangle", "DE.Controllers.Main.txtShape_round2SameRect": "Round same side corner rectangle", "DE.Controllers.Main.txtShape_roundRect": "Round corner rectangle", "DE.Controllers.Main.txtShape_rtTriangle": "Right triangle", "DE.Controllers.Main.txtShape_smileyFace": "Smiley face", "DE.Controllers.Main.txtShape_snip1Rect": "Snip single corner rectangle", "DE.Controllers.Main.txtShape_snip2DiagRect": "Snip diagonal corner rectangle", "DE.Controllers.Main.txtShape_snip2SameRect": "Snip same side corner rectangle", "DE.Controllers.Main.txtShape_snipRoundRect": "Snip and round single corner rectangle", "DE.Controllers.Main.txtShape_spline": "Curve", "DE.Controllers.Main.txtShape_star10": "Sao 10 cánh", "DE.Controllers.Main.txtShape_star12": "Sao 12 cánh", "DE.Controllers.Main.txtShape_star16": "16-Point Star", "DE.Controllers.Main.txtShape_star24": "24-Point Star", "DE.Controllers.Main.txtShape_star32": "32-Point Star", "DE.Controllers.Main.txtShape_star4": "4-Point Star", "DE.Controllers.Main.txtShape_star5": "5-Point Star", "DE.Controllers.Main.txtShape_star6": "6-<PERSON> Star", "DE.Controllers.Main.txtShape_star7": "7-Point Star", "DE.Controllers.Main.txtShape_star8": "8-Point Star", "DE.Controllers.Main.txtShape_stripedRightArrow": "Striped right arrow", "DE.Controllers.Main.txtShape_sun": "Sun", "DE.Controllers.Main.txtShape_teardrop": "Teardrop", "DE.Controllers.Main.txtShape_textRect": "Text box", "DE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "DE.Controllers.Main.txtShape_triangle": "Triangle", "DE.Controllers.Main.txtShape_upArrow": "Up arrow", "DE.Controllers.Main.txtShape_upArrowCallout": "Up arrow callout", "DE.Controllers.Main.txtShape_upDownArrow": "Up down arrow", "DE.Controllers.Main.txtShape_uturnArrow": "U-Turn arrow", "DE.Controllers.Main.txtShape_verticalScroll": "Vertical scroll", "DE.Controllers.Main.txtShape_wave": "Wave", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval СЃallout", "DE.Controllers.Main.txtShape_wedgeRectCallout": "Rectangular callout", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Rounded rectangular callout", "DE.Controllers.Main.txtStarsRibbons": "Sao & Ruy-băng", "DE.Controllers.Main.txtStyle_Book_Title": "Book title", "DE.Controllers.Main.txtStyle_Caption": "Caption", "DE.Controllers.Main.txtStyle_Default_Paragraph_Font": "Default paragraph font", "DE.Controllers.Main.txtStyle_Emphasis": "Emphasis", "DE.Controllers.Main.txtStyle_endnote_reference": "Endnote reference", "DE.Controllers.Main.txtStyle_endnote_text": "Endnote text", "DE.Controllers.Main.txtStyle_footnote_reference": "Footnote reference", "DE.Controllers.Main.txtStyle_footnote_text": "Footnote text", "DE.Controllers.Main.txtStyle_Heading_1": "Tiêu đề 1", "DE.Controllers.Main.txtStyle_Heading_2": "Tiêu đề 2", "DE.Controllers.Main.txtStyle_Heading_3": "Tiêu đề 3", "DE.Controllers.Main.txtStyle_Heading_4": "T<PERSON>êu <PERSON> 4", "DE.Controllers.Main.txtStyle_Heading_5": "<PERSON><PERSON><PERSON><PERSON> 5", "DE.Controllers.Main.txtStyle_Heading_6": "<PERSON><PERSON><PERSON><PERSON> 6", "DE.Controllers.Main.txtStyle_Heading_7": "<PERSON><PERSON><PERSON><PERSON> 7", "DE.Controllers.Main.txtStyle_Heading_8": "<PERSON><PERSON><PERSON><PERSON> 8", "DE.Controllers.Main.txtStyle_Heading_9": "<PERSON><PERSON><PERSON><PERSON> 9", "DE.Controllers.Main.txtStyle_Intense_Emphasis": "Intense emphasis", "DE.Controllers.Main.txtStyle_Intense_Quote": "Trích dẫn mạnh mẽ", "DE.Controllers.Main.txtStyle_Intense_Reference": "Intense reference", "DE.Controllers.Main.txtStyle_List_Paragraph": "<PERSON><PERSON><PERSON><PERSON> danh s<PERSON>ch", "DE.Controllers.Main.txtStyle_No_List": "No list", "DE.Controllers.Main.txtStyle_No_Spacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch", "DE.Controllers.Main.txtStyle_Normal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtStyle_Quote": "<PERSON><PERSON><PERSON><PERSON> dẫn", "DE.Controllers.Main.txtStyle_Strong": "Strong", "DE.Controllers.Main.txtStyle_Subtitle": "<PERSON><PERSON> đề", "DE.Controllers.Main.txtStyle_Subtle_Emphasis": "Subtle emphasis", "DE.Controllers.Main.txtStyle_Subtle_Reference": "Subtle reference", "DE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "DE.Controllers.Main.txtSyntaxError": "Syntax error", "DE.Controllers.Main.txtTableInd": "Table index cannot be zero", "DE.Controllers.Main.txtTableOfContents": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtTableOfFigures": "<PERSON><PERSON><PERSON> h<PERSON>", "DE.Controllers.Main.txtTOCHeading": "TOC Heading", "DE.Controllers.Main.txtTooLarge": "Number too large to format", "DE.Controllers.Main.txtTypeEquation": "Type an equation here.", "DE.Controllers.Main.txtUndefBookmark": "Undefined bookmark", "DE.Controllers.Main.txtXAxis": "Trục X", "DE.Controllers.Main.txtYAxis": "<PERSON><PERSON><PERSON><PERSON> Y", "DE.Controllers.Main.txtZeroDivide": "Zero divide", "DE.Controllers.Main.unknownErrorText": "Lỗi không xác định.", "DE.Controllers.Main.unsupportedBrowserErrorText": "Tr<PERSON><PERSON> du<PERSON>t của bạn không được hỗ trợ.", "DE.Controllers.Main.uploadDocExtMessage": "Unknown document format.", "DE.Controllers.Main.uploadDocFileCountMessage": "No documents uploaded.", "DE.Controllers.Main.uploadDocSizeMessage": "Maximum document size limit exceeded.", "DE.Controllers.Main.uploadImageExtMessage": "Định dạng hình <PERSON>nh không xác đ<PERSON>nh.", "DE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh đư<PERSON> tải lên.", "DE.Controllers.Main.uploadImageSizeMessage": "<PERSON><PERSON> vượt quá giới hạn kích thước tối đa của hình <PERSON>nh.", "DE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON> tải lên h<PERSON>nh <PERSON>...", "DE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON> tải lên h<PERSON>nh <PERSON>", "DE.Controllers.Main.waitText": "Please, wait...", "DE.Controllers.Main.warnBrowserIE9": "Ứng dụng vận hành kém trên IE9. Sử dụng IE10 hoặc cao hơn", "DE.Controllers.Main.warnBrowserZoom": "Hiện cài đặt thu phóng trình duyệt của bạn không được hỗ trợ đầy đủ. <PERSON>ui lòng thiết lập lại chế độ thu phóng mặc định bằng cách nhấn Ctrl+0.", "DE.Controllers.Main.warnLicenseAnonymous": "<PERSON><PERSON><PERSON> cậ<PERSON> bị từ chối cho người dùng ẩn danh.<br><PERSON><PERSON><PERSON> liệu này sẽ được mở ở dạng chỉ xem.", "DE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "DE.Controllers.Main.warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact your administrator to learn more.", "DE.Controllers.Main.warnLicenseExp": "G<PERSON><PERSON><PERSON> phép của bạn đã hết hạn.<br><PERSON><PERSON> lòng cập nhật gi<PERSON>y phép và làm mới trang.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "License expired.<br>You have no access to document editing functionality.<br>Please contact your administrator.", "DE.Controllers.Main.warnLicenseLimitedRenewed": "License needs to be renewed.<br>You have a limited access to document editing functionality.<br>Please contact your administrator to get full access", "DE.Controllers.Main.warnLicenseUsersExceeded": "You've reached the user limit for %1 editors. Contact your administrator to learn more.", "DE.Controllers.Main.warnNoLicense": "Bạn đang sử dụng phiên bản nguồn mở của %1. <PERSON><PERSON>n bản có giới hạn các kết nối đồng thời với server tà<PERSON> liệu (20 kết nối cùng một lúc).<br><PERSON><PERSON><PERSON> bạn cần thêm, h<PERSON><PERSON> cân nhắc mua giấy phép thương mại.", "DE.Controllers.Main.warnNoLicenseUsers": "You've reached the user limit for %1 editors. Contact %1 sales team for personal upgrade terms.", "DE.Controllers.Main.warnProcessRightsChange": "Bạn đã bị từ chối quyền chỉnh sửa tệp này.", "DE.Controllers.Main.warnStartFilling": "Form filling is in progress.<br>File editing is not currently available.", "DE.Controllers.Navigation.txtBeginning": "Beginning of document", "DE.Controllers.Navigation.txtGotoBeginning": "Go to the beginning of the document", "DE.Controllers.Print.textMarginsLast": "Last Custom", "DE.Controllers.Print.txtCustom": "Custom", "DE.Controllers.Print.txtPrintRangeInvalid": "Invalid print range", "DE.Controllers.Print.txtPrintRangeSingleRange": "Enter either a single page number or a single page range (for example, 5-12). Or you can Print to PDF.", "DE.Controllers.Search.notcriticalErrorTitle": "Warning", "DE.Controllers.Search.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "DE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "DE.Controllers.Search.textReplaceSuccess": "Tìm kiếm đã được thực hiện. {0} kho<PERSON>n mục đã được thay thế", "DE.Controllers.Search.warnReplaceString": "{0} is not a valid special character for the Replace With box.", "DE.Controllers.Statusbar.textDisconnect": "<b>Connection is lost</b><br>Trying to connect. Please check connection settings.", "DE.Controllers.Statusbar.textHasChanges": "<PERSON><PERSON><PERSON> thay đổi mới đã đư<PERSON><PERSON> đánh dấu", "DE.Controllers.Statusbar.textSetTrackChanges": "You are in Track Changes mode", "DE.Controllers.Statusbar.textTrackChanges": "<PERSON>ài liệu được mở với chế độ Theo dõi Thay đổi được kích hoạt", "DE.Controllers.Statusbar.tipReview": "<PERSON> đ<PERSON>i", "DE.Controllers.Statusbar.zoomText": "<PERSON>hu phóng {0}%", "DE.Controllers.Toolbar.confirmAddFontName": "Phông chữ bạn sẽ lưu không có sẵn trên thiết bị hiện tại.<br>Ki<PERSON><PERSON> văn bản sẽ được hiển thị bằng một trong các phông chữ hệ thống, phông chữ đã lưu sẽ được sử dụng khi có sẵn.<br>Bạn có muốn tiếp tục?", "DE.Controllers.Toolbar.dataUrl": "Paste a data URL", "DE.Controllers.Toolbar.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "DE.Controllers.Toolbar.fileUrl": "Paste a file URL", "DE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "DE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "DE.Controllers.Toolbar.helpRtlDir": "Toggle between right-to-left (RTL) and left-to-right (LTR) text direction in  your documents.", "DE.Controllers.Toolbar.helpRtlDirHeader": "Switch text direction", "DE.Controllers.Toolbar.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "DE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON> phụ", "DE.Controllers.Toolbar.textBracket": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.textConvertFormDownload": "<PERSON><PERSON><PERSON> tệp dưới dạng biểu mẫu PDF có thể điền vào", "DE.Controllers.Toolbar.textConvertFormSave": "<PERSON><PERSON><PERSON> tệp dưới dạng biểu mẫu PDF có thể điền vào", "DE.Controllers.Toolbar.textDownloadPdf": "Download PDF", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "You need to specify URL.", "DE.Controllers.Toolbar.textFieldExample": "Example of writing code: TIM<PERSON> \\@ \"dddd, MMMM d, yyyy\"", "DE.Controllers.Toolbar.textFieldLabel": "Field codes", "DE.Controllers.Toolbar.textFieldTitle": "Field", "DE.Controllers.Toolbar.textFontSizeErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị số thuộc từ 1 đến 300", "DE.Controllers.Toolbar.textFraction": "<PERSON><PERSON> số", "DE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textGroup": "Group", "DE.Controllers.Toolbar.textInsert": "Insert", "DE.Controllers.Toolbar.textIntegral": "<PERSON><PERSON><PERSON> phân", "DE.Controllers.Toolbar.textLargeOperator": "<PERSON><PERSON> tử lớn", "DE.Controllers.Toolbar.textLimitAndLog": "Giới hạn và Lô-ga", "DE.Controllers.Toolbar.textMatrix": "<PERSON> trận", "DE.Controllers.Toolbar.textOperator": "<PERSON><PERSON> tử", "DE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textRecentlyUsed": "Recently used", "DE.Controllers.Toolbar.textSavePdf": "<PERSON><PERSON><PERSON> dạng PDF", "DE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textTabForms": "Forms", "DE.Controllers.Toolbar.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "DE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON><PERSON> tên hai chiều ở trên", "DE.Controllers.Toolbar.txtAccent_ArrowL": "<PERSON><PERSON><PERSON> tên trên hướng về trái", "DE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON><PERSON> tên phải ở trên", "DE.Controllers.Toolbar.txtAccent_Bar": "Gạch", "DE.Controllers.Toolbar.txtAccent_BarBot": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON><PERSON> trên", "DE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON> thứ<PERSON> đ<PERSON> (Có Placeholder)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "<PERSON><PERSON><PERSON> thức đ<PERSON>g khung (Ví dụ)", "DE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Ngoặc ôm ở dưới", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Ngoặc ôm ở trên", "DE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "ABC với Gạch trên", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y Vớ<PERSON> trên", "DE.Controllers.Toolbar.txtAccent_DDDot": "Ba chấm", "DE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON><PERSON> đ<PERSON>i", "DE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON> trên k<PERSON>", "DE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_GroupBot": "<PERSON><PERSON> nhóm ký tự ở dưới", "DE.Controllers.Toolbar.txtAccent_GroupTop": "<PERSON><PERSON> nhóm ký tự ở trên", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON> c<PERSON>u trên hư<PERSON> về trái", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON> móc phải ở trên", "DE.Controllers.Toolbar.txtAccent_Hat": "Mũ", "DE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON><PERSON> ng<PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Dấu ngoặc với Dấu phân cách", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Dấu ngoặc với Dấu phân cách", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Dấu ngoặc với Dấu phân cách", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (hai đi<PERSON><PERSON> ki<PERSON>)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Ba điều kiện)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON> t<PERSON> xếp chồng", "DE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON><PERSON><PERSON> t<PERSON> xếp chồng", "DE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON> dụ cho tr<PERSON><PERSON><PERSON> hợp", "DE.Controllers.Toolbar.txtBracket_Custom_6": "<PERSON><PERSON> số nhị thức", "DE.Controllers.Toolbar.txtBracket_Custom_7": "<PERSON><PERSON> số nhị thức", "DE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Dấu ngoặc với Dấu phân cách", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON>u ngoặc", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Ngoặc đơn", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Ngoặc đơn", "DE.Controllers.Toolbar.txtDownload": "Download", "DE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON> số vi<PERSON><PERSON> l<PERSON>ch", "DE.Controllers.Toolbar.txtFractionDifferential_1": "Vi phân", "DE.Controllers.Toolbar.txtFractionDifferential_2": "Vi phân", "DE.Controllers.Toolbar.txtFractionDifferential_3": "Vi phân", "DE.Controllers.Toolbar.txtFractionDifferential_4": "Vi phân", "DE.Controllers.Toolbar.txtFractionHorizontal": "<PERSON><PERSON> số viết ngang", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi hơn 2", "DE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON> số nhỏ", "DE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON> số xếp chồng", "DE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON><PERSON><PERSON> cos ngh<PERSON><PERSON> đ<PERSON>o", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "<PERSON><PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_1_Cot": "<PERSON><PERSON><PERSON> cotg ngh<PERSON><PERSON> đ<PERSON>o", "DE.Controllers.Toolbar.txtFunction_1_Coth": "<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_1_Csc": "<PERSON><PERSON><PERSON> cosec ngh<PERSON><PERSON> đ<PERSON>o", "DE.Controllers.Toolbar.txtFunction_1_Csch": "<PERSON><PERSON><PERSON> cosec <PERSON>-péc-b<PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON> sec ngh<PERSON><PERSON> đ<PERSON>o", "DE.Controllers.Toolbar.txtFunction_1_Sech": "<PERSON><PERSON><PERSON> sec <PERSON>y-p<PERSON><PERSON>-b<PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_1_Sin": "<PERSON><PERSON><PERSON> sin ngh<PERSON>ch đ<PERSON>o", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_1_Tan": "<PERSON><PERSON><PERSON> tan ngh<PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON>m cosin", "DE.Controllers.Toolbar.txtFunction_Cosh": "<PERSON><PERSON><PERSON> cos <PERSON>-bôn", "DE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON><PERSON> co<PERSON>g", "DE.Controllers.Toolbar.txtFunction_Coth": "<PERSON><PERSON><PERSON>b<PERSON>n", "DE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON> cos", "DE.Controllers.Toolbar.txtFunction_Csch": "<PERSON><PERSON><PERSON> cosec <PERSON>bôn", "DE.Controllers.Toolbar.txtFunction_Custom_1": "Sin theta", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON> thức tan", "DE.Controllers.Toolbar.txtFunction_Sec": "Hàm sec", "DE.Controllers.Toolbar.txtFunction_Sech": "<PERSON><PERSON><PERSON>n", "DE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON> sin", "DE.Controllers.Toolbar.txtFunction_Sinh": "<PERSON><PERSON><PERSON>n", "DE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON><PERSON> tan", "DE.Controllers.Toolbar.txtFunction_Tanh": "<PERSON><PERSON><PERSON>n", "DE.Controllers.Toolbar.txtIntegral": "<PERSON><PERSON><PERSON> phân", "DE.Controllers.Toolbar.txtIntegral_dtheta": "Vi phân của theta", "DE.Controllers.Toolbar.txtIntegral_dx": "Vi phân của x", "DE.Controllers.Toolbar.txtIntegral_dy": "Vi phân của y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "<PERSON><PERSON><PERSON> phân", "DE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON> phân kép", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON> phân kép", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON> phân kép", "DE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON> phân theo chu tuyến", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "<PERSON><PERSON><PERSON> phân theo chu tuyến", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON> phân bề mặt", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON> phân bề mặt", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON> phân bề mặt", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON> phân theo chu tuyến", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "<PERSON><PERSON><PERSON> phân kh<PERSON>i", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "<PERSON><PERSON><PERSON> phân kh<PERSON>i", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "<PERSON><PERSON><PERSON> phân kh<PERSON>i", "DE.Controllers.Toolbar.txtIntegralSubSup": "<PERSON><PERSON><PERSON> phân", "DE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON><PERSON> phân ba lớp", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON><PERSON> phân ba lớp", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON><PERSON> phân ba lớp", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON><PERSON> c<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON><PERSON> c<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON><PERSON> c<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON> p<PERSON>m", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON> <PERSON>hau", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON> <PERSON>hau", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON> <PERSON>hau", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON> <PERSON>hau", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON> <PERSON>hau", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON> p<PERSON>m", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON> p<PERSON>m", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON> p<PERSON>m", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON><PERSON> p<PERSON>m", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON> p<PERSON>m", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON><PERSON> c<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON><PERSON> c<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON><PERSON> c<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON><PERSON> c<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "<PERSON><PERSON><PERSON> c<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Union": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON>í dụ giới hạn", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON> dụ Lớn nhất", "DE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON><PERSON><PERSON> hạn", "DE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON>-ga-r<PERSON>t tự nhiên", "DE.Controllers.Toolbar.txtLimitLog_Log": "Lô-ga-r<PERSON>t", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "Lô-ga-r<PERSON>t", "DE.Controllers.Toolbar.txtLimitLog_Max": "Lớn n<PERSON>t", "DE.Controllers.Toolbar.txtLimitLog_Min": "Nhỏ nhất", "DE.Controllers.Toolbar.txtMarginsH": "<PERSON><PERSON> trên và dưới cùng quá cao nên không thể làm chiều cao của trang", "DE.Controllers.Toolbar.txtMarginsW": "Lề trái và phải quá rộng nên không thể làm chiều rộng của trang", "DE.Controllers.Toolbar.txtMatrix_1_2": "<PERSON> trận rỗng 1x2", "DE.Controllers.Toolbar.txtMatrix_1_3": "<PERSON> trận rỗng 1x3", "DE.Controllers.Toolbar.txtMatrix_2_1": "<PERSON> trận rỗng 2x1", "DE.Controllers.Toolbar.txtMatrix_2_2": "<PERSON> trận rỗng 2x2", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Ma trận rỗng với dấu ngoặc", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Ma trận rỗng với dấu ngoặc", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Ma trận rỗng với dấu ngoặc", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Ma trận rỗng với dấu ngoặc", "DE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON> trận rỗng 2x3", "DE.Controllers.Toolbar.txtMatrix_3_1": "<PERSON> trận rỗng 3x1", "DE.Controllers.Toolbar.txtMatrix_3_2": "<PERSON> trận rỗng 3x2", "DE.Controllers.Toolbar.txtMatrix_3_3": "<PERSON> trận rỗng 3x3", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON> chấm câu", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON>m gi<PERSON>a dòng", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON> chấm chéo", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON> trận thưa", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON> trận thưa", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "<PERSON> trận Đơn vị 2x2", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "<PERSON> trận Đơn vị 3x3", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "<PERSON> trận Đơn vị 3x3", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "<PERSON> trận Đơn vị 3x3", "DE.Controllers.Toolbar.txtNeedDownload": "PDF viewer can only save new changes in separate file copies. It doesn't support co-editing and other users won't see your changes unless you share a new file version.", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON><PERSON><PERSON> tên hai chiều ở dưới", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON><PERSON> tên hai chiều ở trên", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "<PERSON><PERSON><PERSON> tên dư<PERSON>i hướng về trái", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "<PERSON><PERSON><PERSON> tên trên hướng về trái", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON><PERSON> tên phải ở dưới", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON><PERSON> tên phải ở trên", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "<PERSON> chấm Bằng", "DE.Controllers.Toolbar.txtOperator_Custom_1": "Yields", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Lợi suất Delta", "DE.Controllers.Toolbar.txtOperator_Definition": "Bằng với theo <PERSON> ngh<PERSON>a", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta bằng với", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON><PERSON><PERSON> tên hai chiều ở dưới", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON><PERSON> tên hai chiều ở trên", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "<PERSON><PERSON><PERSON> tên dư<PERSON>i hướng về trái", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "<PERSON><PERSON><PERSON> tên trên hướng về trái", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON><PERSON> tên phải ở dưới", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON><PERSON> tên phải ở trên", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "Bằng bằng", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "Trừ Bằng", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "Cộng Bằng", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON><PERSON> đo bằng", "DE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON> bậc hai có bậc", "DE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON> b<PERSON>c ba", "DE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON> b<PERSON>", "DE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hai", "DE.Controllers.Toolbar.txtSaveCopy": "Save copy", "DE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptSub": "Chỉ số dưới", "DE.Controllers.Toolbar.txtScriptSubSup": "Chỉ số dưới-Chỉ số trên", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "Chỉ số dưới-Chỉ số trên bên trái", "DE.Controllers.Toolbar.txtScriptSup": "Chỉ số trên", "DE.Controllers.Toolbar.txtSymbol_about": "Xấp xỉ", "DE.Controllers.Toolbar.txtSymbol_additional": "<PERSON><PERSON> sung", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "DE.Controllers.Toolbar.txtSymbol_approx": "Gần bằng với", "DE.Controllers.Toolbar.txtSymbol_ast": "<PERSON><PERSON> t<PERSON> *", "DE.Controllers.Toolbar.txtSymbol_beta": "Beta", "DE.Controllers.Toolbar.txtSymbol_beth": "Bet", "DE.Controllers.Toolbar.txtSymbol_bullet": "<PERSON><PERSON> tử Dấu đầu dòng", "DE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON> <PERSON>hau", "DE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON> b<PERSON>c ba", "DE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON>m lửng nằm ngang giữa dòng", "DE.Controllers.Toolbar.txtSymbol_celsius": "Độ C", "DE.Controllers.Toolbar.txtSymbol_chi": "X", "DE.Controllers.Toolbar.txtSymbol_cong": "Xấp xỉ bằng với", "DE.Controllers.Toolbar.txtSymbol_cup": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON><PERSON> lửng chéo xuống bên ph<PERSON>i", "DE.Controllers.Toolbar.txtSymbol_degree": "Độ", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON> chia", "DE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON> tên xu<PERSON>", "DE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON> hợp rỗng", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "DE.Controllers.Toolbar.txtSymbol_equals": "Bằng", "DE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "<PERSON><PERSON> tồn tại", "DE.Controllers.Toolbar.txtSymbol_factorial": "<PERSON><PERSON><PERSON> th<PERSON>a", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "Độ F", "DE.Controllers.Toolbar.txtSymbol_forall": "<PERSON> tất cả", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "DE.Controllers.Toolbar.txtSymbol_geq": "Lớn hơn hoặc bằng", "DE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON> h<PERSON>", "DE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_in": "<PERSON>ần tử của", "DE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON> h<PERSON>", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON><PERSON> tên trái", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON><PERSON> tên <PERSON>-<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_leq": "Nhỏ hơn hoặc Bằng", "DE.Controllers.Toolbar.txtSymbol_less": "Nhỏ hơn", "DE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_minus": "Trừ", "DE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "Không bằng", "DE.Controllers.Toolbar.txtSymbol_ni": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON><PERSON><PERSON> ký hiệu", "DE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON><PERSON><PERSON><PERSON> tồn tại", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "chữ 'o' ngắn", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "Sai phân riêng", "DE.Controllers.Toolbar.txtSymbol_percent": "<PERSON><PERSON><PERSON> tr<PERSON>m", "DE.Controllers.Toolbar.txtSymbol_phi": "Phi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>ng", "DE.Controllers.Toolbar.txtSymbol_pm": "Cộng Trừ", "DE.Controllers.Toolbar.txtSymbol_propto": "Tỷ lệ", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON> thứ tư", "DE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> chứ<PERSON>h", "DE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON><PERSON> lửng chéo lên ph<PERSON>i", "DE.Controllers.Toolbar.txtSymbol_rho": "Rho", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> tên bên ph<PERSON>i", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON> v<PERSON>y", "DE.Controllers.Toolbar.txtSymbol_theta": "Theta", "DE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON> tên chỉ lên", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON><PERSON> thể Epsilon", "DE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "DE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "DE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON><PERSON> thể của <PERSON>ho", "DE.Controllers.Toolbar.txtSymbol_varsigma": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "DE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON><PERSON> thể theta", "DE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON> l<PERSON> d<PERSON>c", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Toolbar.txtUntitled": "Untitled", "DE.Controllers.Viewport.textFitPage": "Fit to <PERSON>", "DE.Controllers.Viewport.textFitWidth": "Fit to Width", "DE.Controllers.Viewport.txtDarkMode": "Dark mode", "DE.Views.BookmarksDialog.textAdd": "Add", "DE.Views.BookmarksDialog.textBookmarkName": "Bookmark name", "DE.Views.BookmarksDialog.textClose": "Close", "DE.Views.BookmarksDialog.textCopy": "Copy", "DE.Views.BookmarksDialog.textDelete": "Delete", "DE.Views.BookmarksDialog.textGetLink": "Get link", "DE.Views.BookmarksDialog.textGoto": "Go to", "DE.Views.BookmarksDialog.textHidden": "Hidden bookmarks", "DE.Views.BookmarksDialog.textLocation": "Location", "DE.Views.BookmarksDialog.textName": "Name", "DE.Views.BookmarksDialog.textSort": "Sort by", "DE.Views.BookmarksDialog.textTitle": "Bookmarks", "DE.Views.BookmarksDialog.txtInvalidName": "Bookmark name can only contain letters, digits and underscores, and should begin with the letter", "DE.Views.CaptionDialog.textAdd": "Add label", "DE.Views.CaptionDialog.textAfter": "After", "DE.Views.CaptionDialog.textBefore": "Before", "DE.Views.CaptionDialog.textCaption": "Caption", "DE.Views.CaptionDialog.textChapter": "Chapter starts with style", "DE.Views.CaptionDialog.textChapterInc": "Include chapter number", "DE.Views.CaptionDialog.textColon": "colon", "DE.Views.CaptionDialog.textDash": "dash", "DE.Views.CaptionDialog.textDelete": "Delete label", "DE.Views.CaptionDialog.textEquation": "Equation", "DE.Views.CaptionDialog.textExamples": "Examples: Table 2-A, Image 1.IV", "DE.Views.CaptionDialog.textExclude": "Exclude label from caption", "DE.Views.CaptionDialog.textFigure": "Figure", "DE.Views.CaptionDialog.textHyphen": "hyphen", "DE.Views.CaptionDialog.textInsert": "Insert", "DE.Views.CaptionDialog.textLabel": "Label", "DE.Views.CaptionDialog.textLabelError": "Label must not be empty.", "DE.Views.CaptionDialog.textLongDash": "long dash", "DE.Views.CaptionDialog.textNumbering": "Numbering", "DE.Views.CaptionDialog.textPeriod": "period", "DE.Views.CaptionDialog.textSeparator": "Use separator", "DE.Views.CaptionDialog.textTable": "Table", "DE.Views.CaptionDialog.textTitle": "Insert caption", "DE.Views.CellsAddDialog.textCol": "Columns", "DE.Views.CellsAddDialog.textDown": "Below the cursor", "DE.Views.CellsAddDialog.textLeft": "To the left", "DE.Views.CellsAddDialog.textRight": "To the right", "DE.Views.CellsAddDialog.textRow": "Rows", "DE.Views.CellsAddDialog.textTitle": "Insert several", "DE.Views.CellsAddDialog.textUp": "Above the cursor", "DE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "DE.Views.ChartSettings.text3dHeight": "Height (% of base)", "DE.Views.ChartSettings.text3dRotation": "3D Rotation", "DE.Views.ChartSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "DE.Views.ChartSettings.textAutoscale": "Autoscale", "DE.Views.ChartSettings.textChartType": "Thay đổi Loại biểu đồ", "DE.Views.ChartSettings.textDefault": "Default rotation", "DE.Views.ChartSettings.textDown": "Down", "DE.Views.ChartSettings.textEditData": "Chỉnh sửa <PERSON> liệu", "DE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON> cao", "DE.Views.ChartSettings.textLeft": "Left", "DE.Views.ChartSettings.textNarrow": "Narrow field of view", "DE.Views.ChartSettings.textOriginalSize": "<PERSON><PERSON><PERSON> thước mặc định", "DE.Views.ChartSettings.textPerspective": "Perspective", "DE.Views.ChartSettings.textRight": "Right", "DE.Views.ChartSettings.textRightAngle": "Right angle axes", "DE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textUndock": "<PERSON>h<PERSON><PERSON> khỏi bảng điều khiển", "DE.Views.ChartSettings.textUp": "Up", "DE.Views.ChartSettings.textWiden": "Widen field of view", "DE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "DE.Views.ChartSettings.textWrap": "<PERSON><PERSON><PERSON> ng<PERSON> dòng", "DE.Views.ChartSettings.textX": "X rotation", "DE.Views.ChartSettings.textY": "Y rotation", "DE.Views.ChartSettings.txtBehind": "Sau", "DE.Views.ChartSettings.txtInFront": "Ở trước", "DE.Views.ChartSettings.txtInline": "<PERSON><PERSON><PERSON> dòng", "DE.Views.ChartSettings.txtSquare": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.txtThrough": "<PERSON><PERSON><PERSON><PERSON> qua", "DE.Views.ChartSettings.txtTight": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.txtTitle": "<PERSON><PERSON><PERSON><PERSON> đồ", "DE.Views.ChartSettings.txtTopAndBottom": "<PERSON><PERSON><PERSON><PERSON> cùng và dưới cùng", "DE.Views.ControlSettingsDialog.strGeneral": "General", "DE.Views.ControlSettingsDialog.textAdd": "Add", "DE.Views.ControlSettingsDialog.textAppearance": "Appearance", "DE.Views.ControlSettingsDialog.textApplyAll": "Apply to all", "DE.Views.ControlSettingsDialog.textBox": "Bounding box", "DE.Views.ControlSettingsDialog.textChange": "Edit", "DE.Views.ControlSettingsDialog.textCheckbox": "Check box", "DE.Views.ControlSettingsDialog.textChecked": "Checked symbol", "DE.Views.ControlSettingsDialog.textColor": "Color", "DE.Views.ControlSettingsDialog.textCombobox": "Combo box", "DE.Views.ControlSettingsDialog.textDate": "<PERSON><PERSON><PERSON> dạng ng<PERSON>y", "DE.Views.ControlSettingsDialog.textDelete": "Delete", "DE.Views.ControlSettingsDialog.textDisplayName": "Display name", "DE.Views.ControlSettingsDialog.textDown": "Down", "DE.Views.ControlSettingsDialog.textDropDown": "Drop-down list", "DE.Views.ControlSettingsDialog.textFormat": "Display the date like this", "DE.Views.ControlSettingsDialog.textLang": "Language", "DE.Views.ControlSettingsDialog.textLock": "Locking", "DE.Views.ControlSettingsDialog.textName": "Title", "DE.Views.ControlSettingsDialog.textNone": "None", "DE.Views.ControlSettingsDialog.textPlaceholder": "Placeholder", "DE.Views.ControlSettingsDialog.textShowAs": "Show as", "DE.Views.ControlSettingsDialog.textSystemColor": "System", "DE.Views.ControlSettingsDialog.textTag": "Tag", "DE.Views.ControlSettingsDialog.textTitle": "<PERSON>ài đặt điều khiển nội dung", "DE.Views.ControlSettingsDialog.textUnchecked": "Unchecked symbol", "DE.Views.ControlSettingsDialog.textUp": "Up", "DE.Views.ControlSettingsDialog.textValue": "Value", "DE.Views.ControlSettingsDialog.tipChange": "Change symbol", "DE.Views.ControlSettingsDialog.txtLockDelete": "<PERSON><PERSON><PERSON><PERSON> thể xóa điều khiển nội dung", "DE.Views.ControlSettingsDialog.txtLockEdit": "Contents cannot be edited", "DE.Views.ControlSettingsDialog.txtRemContent": "Bỏ điều khiển nội dung khi nội dung đang được chỉnh sửa", "DE.Views.CrossReferenceDialog.textAboveBelow": "Above/below", "DE.Views.CrossReferenceDialog.textBookmark": "Bookmark", "DE.Views.CrossReferenceDialog.textBookmarkText": "Bookmark text", "DE.Views.CrossReferenceDialog.textCaption": "Entire caption", "DE.Views.CrossReferenceDialog.textEmpty": "The request reference is empty.", "DE.Views.CrossReferenceDialog.textEndnote": "Endnote", "DE.Views.CrossReferenceDialog.textEndNoteNum": "Endnote number", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "Endnote number (formatted)", "DE.Views.CrossReferenceDialog.textEquation": "Equation", "DE.Views.CrossReferenceDialog.textFigure": "Figure", "DE.Views.CrossReferenceDialog.textFootnote": "Footnote", "DE.Views.CrossReferenceDialog.textHeading": "Heading", "DE.Views.CrossReferenceDialog.textHeadingNum": "Heading number", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "Heading number (full context)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "Heading number (no context)", "DE.Views.CrossReferenceDialog.textHeadingText": "Heading text", "DE.Views.CrossReferenceDialog.textIncludeAbove": "Include above/below", "DE.Views.CrossReferenceDialog.textInsert": "Insert", "DE.Views.CrossReferenceDialog.textInsertAs": "Insert as hyperlink", "DE.Views.CrossReferenceDialog.textLabelNum": "Only label and number", "DE.Views.CrossReferenceDialog.textNoteNum": "Footnote number", "DE.Views.CrossReferenceDialog.textNoteNumForm": "Footnote number (formatted)", "DE.Views.CrossReferenceDialog.textOnlyCaption": "Only caption text", "DE.Views.CrossReferenceDialog.textPageNum": "Page number", "DE.Views.CrossReferenceDialog.textParagraph": "Numbered item", "DE.Views.CrossReferenceDialog.textParaNum": "Paragraph number", "DE.Views.CrossReferenceDialog.textParaNumFull": "Paragraph number (full context)", "DE.Views.CrossReferenceDialog.textParaNumNo": "Paragraph number (no context)", "DE.Views.CrossReferenceDialog.textSeparate": "Separate numbers with", "DE.Views.CrossReferenceDialog.textTable": "Table", "DE.Views.CrossReferenceDialog.textText": "Paragraph text", "DE.Views.CrossReferenceDialog.textWhich": "For which caption", "DE.Views.CrossReferenceDialog.textWhichBookmark": "For which bookmark", "DE.Views.CrossReferenceDialog.textWhichEndnote": "For which endnote", "DE.Views.CrossReferenceDialog.textWhichHeading": "For which heading", "DE.Views.CrossReferenceDialog.textWhichNote": "For which footnote", "DE.Views.CrossReferenceDialog.textWhichPara": "For which numbered item", "DE.Views.CrossReferenceDialog.txtReference": "Insert reference to", "DE.Views.CrossReferenceDialog.txtTitle": "Cross-reference", "DE.Views.CrossReferenceDialog.txtType": "Reference type", "DE.Views.CustomColumnsDialog.textColumns": "Số cột", "DE.Views.CustomColumnsDialog.textEqualWidth": "Equal column width", "DE.Views.CustomColumnsDialog.textSeparator": "<PERSON><PERSON>", "DE.Views.CustomColumnsDialog.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.CustomColumnsDialog.textTitleSpacing": "Spacing", "DE.Views.CustomColumnsDialog.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.DateTimeDialog.confirmDefault": "Set default format for {0}: \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "Set as default", "DE.Views.DateTimeDialog.textFormat": "Formats", "DE.Views.DateTimeDialog.textLang": "Language", "DE.Views.DateTimeDialog.textUpdate": "Update automatically", "DE.Views.DateTimeDialog.txtTitle": "Ngày & Giờ", "DE.Views.DocProtection.hintProtectDoc": "<PERSON>ả<PERSON> vệ tài liệu", "DE.Views.DocProtection.txtDocProtectedComment": "Tài liệu đư<PERSON> bảo vệ.<br><PERSON><PERSON><PERSON> có thể chỉ nhận xét vào tài liệu này.", "DE.Views.DocProtection.txtDocProtectedForms": "Document is protected.<br>You may only fill in forms in this document.", "DE.Views.DocProtection.txtDocProtectedTrack": "Document is protected.<br>You may edit this document, but all changes will be tracked.", "DE.Views.DocProtection.txtDocProtectedView": "Document is protected.<br>You may only view this document.", "DE.Views.DocProtection.txtDocUnlockDescription": "Enter a password to unprotect document", "DE.Views.DocProtection.txtProtectDoc": "<PERSON>ả<PERSON> vệ tài liệu", "DE.Views.DocProtection.txtUnlockTitle": "Unprotect document", "DE.Views.DocumentHolder.aboveText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.addCommentText": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "DE.Views.DocumentHolder.advancedDropCapText": "Drop cap settings", "DE.Views.DocumentHolder.advancedEquationText": "Equation settings", "DE.Views.DocumentHolder.advancedFrameText": "Cài đặt Nâng cao <PERSON>hung", "DE.Views.DocumentHolder.advancedParagraphText": "<PERSON>ài đặt Nâng cao <PERSON> văn bản", "DE.Views.DocumentHolder.advancedTableText": "Cài đặt Nâng cao Bảng", "DE.Views.DocumentHolder.advancedText": "Cài đặt nâng cao", "DE.Views.DocumentHolder.alignmentText": "<PERSON><PERSON>n chỉnh", "DE.Views.DocumentHolder.allLinearText": "All - Linear", "DE.Views.DocumentHolder.allProfText": "All - Professional", "DE.Views.DocumentHolder.belowText": "Dư<PERSON><PERSON>", "DE.Views.DocumentHolder.breakBeforeText": "<PERSON><PERSON>t trang đằng trước", "DE.Views.DocumentHolder.bulletsText": "Bullets and numbering", "DE.Views.DocumentHolder.cellAlignText": "<PERSON><PERSON><PERSON> chỉnh dọc ô", "DE.Views.DocumentHolder.cellText": "Ô", "DE.Views.DocumentHolder.centerText": "Trung tâm", "DE.Views.DocumentHolder.chartText": "Cài đặt Nâng cao Biểu đồ", "DE.Views.DocumentHolder.columnText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.currLinearText": "Current - Linear", "DE.Views.DocumentHolder.currProfText": "Current - Professional", "DE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON> c<PERSON>", "DE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> h<PERSON>", "DE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.deleteText": "Xóa", "DE.Views.DocumentHolder.direct270Text": "<PERSON><PERSON><PERSON> v<PERSON>n bản lên", "DE.Views.DocumentHolder.direct90Text": "<PERSON><PERSON><PERSON> v<PERSON>n bản x<PERSON>", "DE.Views.DocumentHolder.directHText": "Nằm ngang", "DE.Views.DocumentHolder.directionText": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> bản", "DE.Views.DocumentHolder.editChartText": "Chỉnh sửa <PERSON> liệu", "DE.Views.DocumentHolder.editFooterText": "Chỉnh s<PERSON><PERSON>", "DE.Views.DocumentHolder.editHeaderText": "Chỉnh s<PERSON><PERSON>er", "DE.Views.DocumentHolder.editHyperlinkText": "Chỉnh sửa <PERSON><PERSON><PERSON> liên kết", "DE.Views.DocumentHolder.eqToDisplayText": "Change to Display", "DE.Views.DocumentHolder.eqToInlineText": "Change to Inline", "DE.Views.DocumentHolder.guestText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "DE.Views.DocumentHolder.hyperlinkText": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>", "DE.Views.DocumentHolder.ignoreAllSpellText": "Bỏ qua tất cả", "DE.Views.DocumentHolder.ignoreSpellText": "Bỏ qua", "DE.Views.DocumentHolder.imageText": "Cài đặt Hình <PERSON>nh <PERSON> cao", "DE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> trái", "DE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.insertColumnText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> trên", "DE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.insertRowText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.insertText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.keepLinesText": "<PERSON><PERSON><PERSON> các dòng cùng nhau", "DE.Views.DocumentHolder.langText": "<PERSON><PERSON><PERSON> ngôn ngữ", "DE.Views.DocumentHolder.latexText": "LaTeX", "DE.Views.DocumentHolder.leftText": "Trái", "DE.Views.DocumentHolder.loadSpellText": "<PERSON><PERSON> tải các biến thể...", "DE.Views.DocumentHolder.mergeCellsText": "<PERSON><PERSON><PERSON> nhi<PERSON>u ô và không canh giữa", "DE.Views.DocumentHolder.mniImageFromFile": "Image from File", "DE.Views.DocumentHolder.mniImageFromStorage": "Image from Storage", "DE.Views.DocumentHolder.mniImageFromUrl": "Image from URL", "DE.Views.DocumentHolder.moreText": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>c biến thể...", "DE.Views.DocumentHolder.noSpellVariantsText": "<PERSON><PERSON><PERSON><PERSON> có biến thể", "DE.Views.DocumentHolder.notcriticalErrorTitle": "Warning", "DE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON> thước mặc định", "DE.Views.DocumentHolder.paragraphText": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>n bản", "DE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON> si<PERSON>u liên k<PERSON>t", "DE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.rowText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.saveStyleText": "<PERSON><PERSON><PERSON> ki<PERSON>u mới", "DE.Views.DocumentHolder.selectCellText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON> b<PERSON>", "DE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.shapeText": "Cài đặt Nâng cao Hình dạng", "DE.Views.DocumentHolder.showEqToolbar": "Show equation toolbar", "DE.Views.DocumentHolder.spellcheckText": "<PERSON><PERSON><PERSON> tra ch<PERSON>h tả", "DE.Views.DocumentHolder.splitCellsText": "<PERSON><PERSON><PERSON> ô...", "DE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.strDelete": "Remove signature", "DE.Views.DocumentHolder.strDetails": "<PERSON> tiết chữ ký", "DE.Views.DocumentHolder.strSetup": "Signature Setup", "DE.Views.DocumentHolder.strSign": "<PERSON><PERSON>", "DE.Views.DocumentHolder.styleText": "<PERSON><PERSON><PERSON> dạng theo <PERSON>", "DE.Views.DocumentHolder.tableText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textAccept": "<PERSON><PERSON><PERSON> nhận thay đổi", "DE.Views.DocumentHolder.textAlign": "<PERSON><PERSON>n chỉnh", "DE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>p", "DE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON><PERSON> t<PERSON>", "DE.Views.DocumentHolder.textArrangeBackward": "Gửi về phía sau", "DE.Views.DocumentHolder.textArrangeForward": "<PERSON> chuyển tiến lên", "DE.Views.DocumentHolder.textArrangeFront": "<PERSON><PERSON><PERSON> lên <PERSON> cảnh", "DE.Views.DocumentHolder.textCells": "Cells", "DE.Views.DocumentHolder.textClearField": "Clear field", "DE.Views.DocumentHolder.textCol": "Delete entire column", "DE.Views.DocumentHolder.textContentControls": "<PERSON><PERSON><PERSON><PERSON> khiển nội dung", "DE.Views.DocumentHolder.textContinueNumbering": "Continue numbering", "DE.Views.DocumentHolder.textCopy": "Sao chép", "DE.Views.DocumentHolder.textCrop": "Crop", "DE.Views.DocumentHolder.textCropFill": "Fill", "DE.Views.DocumentHolder.textCropFit": "Fit", "DE.Views.DocumentHolder.textCut": "Cắt", "DE.Views.DocumentHolder.textDistributeCols": "Distribute columns", "DE.Views.DocumentHolder.textDistributeRows": "Distribute rows", "DE.Views.DocumentHolder.textEditControls": "<PERSON>ài đặt điều khiển nội dung", "DE.Views.DocumentHolder.textEditField": "Edit field", "DE.Views.DocumentHolder.textEditObject": "Edit object", "DE.Views.DocumentHolder.textEditPoints": "Edit points", "DE.Views.DocumentHolder.textEditWrapBoundary": "Chỉnh sửa đường biên bao quanh", "DE.Views.DocumentHolder.textFieldCodes": "Toggle field codes", "DE.Views.DocumentHolder.textFlipH": "Flip horizontally", "DE.Views.DocumentHolder.textFlipV": "Flip vertically", "DE.Views.DocumentHolder.textFollow": "Follow move", "DE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON> tệp", "DE.Views.DocumentHolder.textFromStorage": "From storage", "DE.Views.DocumentHolder.textFromUrl": "From URL", "DE.Views.DocumentHolder.textIndents": "<PERSON><PERSON><PERSON>u chỉnh thụt lề danh sách", "DE.Views.DocumentHolder.textJoinList": "Join to previous list", "DE.Views.DocumentHolder.textLeft": "Shift cells left", "DE.Views.DocumentHolder.textNest": "Nest table", "DE.Views.DocumentHolder.textNextPage": "<PERSON><PERSON> ti<PERSON><PERSON> theo", "DE.Views.DocumentHolder.textNumberingValue": "Numbering value", "DE.Views.DocumentHolder.textPaste": "Dán", "DE.Views.DocumentHolder.textPrevPage": "<PERSON><PERSON> tr<PERSON>", "DE.Views.DocumentHolder.textRedo": "Redo", "DE.Views.DocumentHolder.textRefreshField": "Update field", "DE.Views.DocumentHolder.textReject": "Reject change", "DE.Views.DocumentHolder.textRemCheckBox": "Remove Checkbox", "DE.Views.DocumentHolder.textRemComboBox": "Remove Combo Box", "DE.Views.DocumentHolder.textRemDropdown": "Remove Dropdown", "DE.Views.DocumentHolder.textRemField": "Remove text field", "DE.Views.DocumentHolder.textRemove": "Remove", "DE.Views.DocumentHolder.textRemoveControl": "Bỏ điều khiển nội dung", "DE.Views.DocumentHolder.textRemPicture": "Remove image", "DE.Views.DocumentHolder.textRemRadioBox": "Remove Radio button", "DE.Views.DocumentHolder.textReplace": "Replace image", "DE.Views.DocumentHolder.textResetCrop": "Reset crop", "DE.Views.DocumentHolder.textRotate": "Rotate", "DE.Views.DocumentHolder.textRotate270": "Quay 90° ng<PERSON><PERSON><PERSON> chiều kim đồng hồ", "DE.Views.DocumentHolder.textRotate90": "Quay 90° chi<PERSON>u kim đồng hồ", "DE.Views.DocumentHolder.textRow": "Delete entire row", "DE.Views.DocumentHolder.textSaveAsPicture": "<PERSON><PERSON><PERSON> dạng <PERSON>", "DE.Views.DocumentHolder.textSeparateList": "Separate list", "DE.Views.DocumentHolder.textSettings": "Settings", "DE.Views.DocumentHolder.textSeveral": "Several rows/columns", "DE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON><PERSON> c<PERSON>ng", "DE.Views.DocumentHolder.textShapeAlignCenter": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON><PERSON> t<PERSON>", "DE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON><PERSON> trên c<PERSON>ng", "DE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "DE.Views.DocumentHolder.textStartNewList": "Start new list", "DE.Views.DocumentHolder.textStartNumberingFrom": "Set numbering value", "DE.Views.DocumentHolder.textTitleCellsRemove": "Delete cells", "DE.Views.DocumentHolder.textTOC": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textTOCSettings": "Table of contents settings", "DE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textUpdateAll": "Update entire table", "DE.Views.DocumentHolder.textUpdatePages": "Update page numbers only", "DE.Views.DocumentHolder.textUpdateTOC": "Update table of contents", "DE.Views.DocumentHolder.textWrap": "<PERSON><PERSON><PERSON> ng<PERSON> dòng", "DE.Views.DocumentHolder.tipIsLocked": "<PERSON> tiết này hiện đang được chỉnh sửa bởi một người dùng khác.", "DE.Views.DocumentHolder.toDictionaryText": "Add to dictionary", "DE.Views.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON> đườ<PERSON> viền dưới cùng", "DE.Views.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON><PERSON><PERSON> dấu phân số", "DE.Views.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON><PERSON> đường kẻ ngang", "DE.Views.DocumentHolder.txtAddLB": "Thê<PERSON> đường kẻ dưới cùng bên trái", "DE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON>ê<PERSON> đườ<PERSON> viền trái", "DE.Views.DocumentHolder.txtAddLT": "Thê<PERSON> đường kẻ trên cùng bên trái", "DE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON>ê<PERSON> đườ<PERSON> viền bên phải", "DE.Views.DocumentHolder.txtAddTop": "<PERSON>hê<PERSON> đường viền trên cùng", "DE.Views.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON><PERSON> đường kẻ dọc", "DE.Views.DocumentHolder.txtAlignToChar": "<PERSON><PERSON>n chỉnh theo ký tự", "DE.Views.DocumentHolder.txtBehind": "Sau", "DE.Views.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h đư<PERSON> vền", "DE.Views.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "DE.Views.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON> chỉnh cột", "DE.Views.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON><PERSON> đ<PERSON>i số", "DE.Views.DocumentHolder.txtDeleteArg": "<PERSON><PERSON><PERSON> đ<PERSON>i số", "DE.Views.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON> ng<PERSON>t thủ công", "DE.Views.DocumentHolder.txtDeleteChars": "<PERSON><PERSON><PERSON> các ký tự kèm theo", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON><PERSON> các ký tự và dấu phân cách kèm theo", "DE.Views.DocumentHolder.txtDeleteEq": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> trình", "DE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON> biểu đồ", "DE.Views.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON> c<PERSON>c", "DE.Views.DocumentHolder.txtDistribHor": "Distribute horizontally", "DE.Views.DocumentHolder.txtDistribVert": "Distribute vertically", "DE.Views.DocumentHolder.txtEmpty": "(Empty)", "DE.Views.DocumentHolder.txtFractionLinear": "<PERSON><PERSON> sang phân số viết ngang", "DE.Views.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> sang phân số viết lệch", "DE.Views.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> đ<PERSON> sang phân số viết đứng", "DE.Views.DocumentHolder.txtGroup": "Nhóm", "DE.Views.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON><PERSON> đồ trên văn bản", "DE.Views.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> dư<PERSON><PERSON> văn bản", "DE.Views.DocumentHolder.txtHideBottom": "Ẩn đường viền dưới cùng", "DE.Views.DocumentHolder.txtHideBottomLimit": "Ẩn giới hạn dưới", "DE.Views.DocumentHolder.txtHideCloseBracket": "Ẩn dấu ngoặc đóng", "DE.Views.DocumentHolder.txtHideDegree": "Ẩn cấp độ", "DE.Views.DocumentHolder.txtHideHor": "Ẩn đường kẻ ngang", "DE.Views.DocumentHolder.txtHideLB": "Ẩn đường kẻ dưới cùng bên trái", "DE.Views.DocumentHolder.txtHideLeft": "Ẩn đường viền trái", "DE.Views.DocumentHolder.txtHideLT": "Ẩn đường kẻ trên cùng bên trái", "DE.Views.DocumentHolder.txtHideOpenBracket": "Ẩn dấu ngoặc mở", "DE.Views.DocumentHolder.txtHidePlaceholder": "Ẩn placeholder", "DE.Views.DocumentHolder.txtHideRight": "Ẩn đường viền bên phải", "DE.Views.DocumentHolder.txtHideTop": "Ẩn đường viền trên cùng", "DE.Views.DocumentHolder.txtHideTopLimit": "Ẩn giới hạn trên cùng", "DE.Views.DocumentHolder.txtHideVer": "Ẩn đường kẻ dọc", "DE.Views.DocumentHolder.txtIncreaseArg": "<PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON><PERSON> đối số", "DE.Views.DocumentHolder.txtInFront": "Ở trước", "DE.Views.DocumentHolder.txtInline": "<PERSON><PERSON><PERSON> dòng", "DE.Views.DocumentHolder.txtInsertArgAfter": "<PERSON><PERSON><PERSON> đ<PERSON>i số sau", "DE.Views.DocumentHolder.txtInsertArgBefore": "<PERSON><PERSON><PERSON> đ<PERSON>i số trước", "DE.Views.DocumentHolder.txtInsertBreak": "<PERSON><PERSON><PERSON> ng<PERSON>t thủ công", "DE.Views.DocumentHolder.txtInsertCaption": "Insert caption", "DE.Views.DocumentHolder.txtInsertEqAfter": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON>ng trình sau", "DE.Views.DocumentHolder.txtInsertEqBefore": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> trình trước", "DE.Views.DocumentHolder.txtInsImage": "<PERSON><PERSON><PERSON> từ tệp", "DE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "DE.Views.DocumentHolder.txtKeepTextOnly": "Chỉ gi<PERSON> văn bản", "DE.Views.DocumentHolder.txtLimitChange": "<PERSON>hay đổi giới hạn địa điểm", "DE.Views.DocumentHolder.txtLimitOver": "<PERSON><PERSON><PERSON><PERSON> hạn trên văn bản", "DE.Views.DocumentHolder.txtLimitUnder": "<PERSON><PERSON><PERSON><PERSON> hạn dưới văn bản", "DE.Views.DocumentHolder.txtMatchBrackets": "Chỉnh dấu ngoặc phù hợp với độ cao đối số", "DE.Views.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON> chỉnh ma trận", "DE.Views.DocumentHolder.txtOverbar": "<PERSON><PERSON><PERSON> trên v<PERSON><PERSON> bản", "DE.Views.DocumentHolder.txtOverwriteCells": "Overwrite cells", "DE.Views.DocumentHolder.txtPasteSourceFormat": "Keep source formatting", "DE.Views.DocumentHolder.txtPressLink": "Ấn {0} và nhấp vào liên kết", "DE.Views.DocumentHolder.txtPrintSelection": "In phần đ<PERSON><PERSON><PERSON> chọn", "DE.Views.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON> d<PERSON>u phân số", "DE.Views.DocumentHolder.txtRemLimit": "<PERSON>óa gi<PERSON> hạn", "DE.Views.DocumentHolder.txtRemoveAccentChar": "Xóa ký tự dấu phụ", "DE.Views.DocumentHolder.txtRemoveBar": "<PERSON>óa v<PERSON>", "DE.Views.DocumentHolder.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "DE.Views.DocumentHolder.txtRemScripts": "Xóa script", "DE.Views.DocumentHolder.txtRemSubscript": "Xóa chỉ số dưới", "DE.Views.DocumentHolder.txtRemSuperscript": "Xóa chỉ số trên", "DE.Views.DocumentHolder.txtScriptsAfter": "Các script sau văn bản", "DE.Views.DocumentHolder.txtScriptsBefore": "Các script trước văn bản", "DE.Views.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON> thị giới hạn dưới", "DE.Views.DocumentHolder.txtShowCloseBracket": "Hiển thị dấu ngoặc đóng", "DE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON> thị cấp độ", "DE.Views.DocumentHolder.txtShowOpenBracket": "Hiển thị dấu ngoặc mở", "DE.Views.DocumentHolder.txtShowPlaceholder": "Hiển thị placeholder", "DE.Views.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON> thị giới hạn trên", "DE.Views.DocumentHolder.txtSquare": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtStretchBrackets": "<PERSON>éo dài ngoặc", "DE.Views.DocumentHolder.txtThrough": "<PERSON><PERSON><PERSON><PERSON> qua", "DE.Views.DocumentHolder.txtTight": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtTop": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtTopAndBottom": "<PERSON><PERSON><PERSON><PERSON> cùng và dưới cùng", "DE.Views.DocumentHolder.txtUnderbar": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> v<PERSON><PERSON> bản", "DE.Views.DocumentHolder.txtUngroup": "Bỏ nhóm", "DE.Views.DocumentHolder.txtWarnUrl": "Vi<PERSON><PERSON> nhấp vào liên kết này có thể gây hại cho thiết bị và dữ liệu của bạn.<br>Bạn có chắc chắn muốn tiếp tục không?", "DE.Views.DocumentHolder.unicodeText": "Unicode", "DE.Views.DocumentHolder.updateStyleText": "<PERSON>ậ<PERSON> nh<PERSON><PERSON> k<PERSON>u %1", "DE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON> chỉnh dọc", "DE.Views.DropcapSettingsAdvanced.strBorders": "Viền & <PERSON><PERSON> màu", "DE.Views.DropcapSettingsAdvanced.strDropcap": "Drop Cap", "DE.Views.DropcapSettingsAdvanced.strMargins": "Lề", "DE.Views.DropcapSettingsAdvanced.textAlign": "<PERSON><PERSON>n chỉnh", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "<PERSON><PERSON> n<PERSON>t", "DE.Views.DropcapSettingsAdvanced.textAuto": "<PERSON><PERSON> động", "DE.Views.DropcapSettingsAdvanced.textBackColor": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "<PERSON>hấ<PERSON> vào biểu đồ hoặc sử dụng các nút để chọn đường viền", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h đư<PERSON> vền", "DE.Views.DropcapSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "DE.Views.DropcapSettingsAdvanced.textCenter": "Trung tâm", "DE.Views.DropcapSettingsAdvanced.textColumn": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textDistance": "<PERSON><PERSON><PERSON><PERSON> cách từ văn bản", "DE.Views.DropcapSettingsAdvanced.textExact": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textFlow": "Phủ chữ quanh khung", "DE.Views.DropcapSettingsAdvanced.textFont": "Phông chữ", "DE.Views.DropcapSettingsAdvanced.textFrame": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON> cao", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "Nằm ngang", "DE.Views.DropcapSettingsAdvanced.textInline": "<PERSON><PERSON><PERSON> cùng dòng", "DE.Views.DropcapSettingsAdvanced.textInMargin": "<PERSON>rong lề", "DE.Views.DropcapSettingsAdvanced.textInText": "<PERSON>rong lề", "DE.Views.DropcapSettingsAdvanced.textLeft": "Trái", "DE.Views.DropcapSettingsAdvanced.textMargin": "Lề", "DE.Views.DropcapSettingsAdvanced.textMove": "<PERSON> chuyển cùng văn bản", "DE.Views.DropcapSettingsAdvanced.textNone": "K<PERSON>ô<PERSON>", "DE.Views.DropcapSettingsAdvanced.textPage": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textParagraph": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>n bản", "DE.Views.DropcapSettingsAdvanced.textParameters": "<PERSON>h<PERSON><PERSON> số", "DE.Views.DropcapSettingsAdvanced.textPosition": "<PERSON><PERSON> trí", "DE.Views.DropcapSettingsAdvanced.textRelative": "<PERSON>ân x<PERSON>ng với", "DE.Views.DropcapSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "<PERSON><PERSON><PERSON> cao trong hàng", "DE.Views.DropcapSettingsAdvanced.textTitle": "Drop Cap - Cài đặt Nâng cao", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "Khung - Cài đặt Nâng cao", "DE.Views.DropcapSettingsAdvanced.textTop": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "DE.Views.DropcapSettingsAdvanced.tipFontName": "Phông chữ", "DE.Views.EditListItemDialog.textDisplayName": "Display name", "DE.Views.EditListItemDialog.textNameError": "Display name must not be empty.", "DE.Views.EditListItemDialog.textValue": "Value", "DE.Views.EditListItemDialog.textValueError": "An item with the same value already exists.", "DE.Views.FileMenu.ariaFileMenu": "File menu", "DE.Views.FileMenu.btnBackCaption": "Mở vị trí tệp", "DE.Views.FileMenu.btnCloseEditor": "Close File", "DE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON> mới", "DE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> về dưới dạng", "DE.Views.FileMenu.btnExitCaption": "Close", "DE.Views.FileMenu.btnFileOpenCaption": "Open", "DE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON> g<PERSON>", "DE.Views.FileMenu.btnHistoryCaption": "<PERSON><PERSON><PERSON> sử phiên bản", "DE.Views.FileMenu.btnInfoCaption": "<PERSON>h<PERSON><PERSON> tin Tài liệu", "DE.Views.FileMenu.btnPrintCaption": "In", "DE.Views.FileMenu.btnProtectCaption": "Bả<PERSON> vệ", "DE.Views.FileMenu.btnRecentFilesCaption": "Mở gần đây", "DE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON> tên", "DE.Views.FileMenu.btnReturnCaption": "Quay lại Tài liệu", "DE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON><PERSON> truy cập", "DE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON><PERSON> dạng", "DE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnSaveCopyAsCaption": "Save Copy As", "DE.Views.FileMenu.btnSettingsCaption": "Cài đặt nâng cao", "DE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "DE.Views.FileMenu.btnToEditCaption": "Chỉnh sửa <PERSON><PERSON>i liệu", "DE.Views.FileMenu.textDownload": "<PERSON><PERSON><PERSON> về", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "Blank document", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Create New", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Apply", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Add Author", "DE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Add Text", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Application", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Tác g<PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON> đ<PERSON>i quyền truy cập", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>t", "DE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Created", "DE.Views.FileMenuPanels.DocumentInfo.txtDocumentInfo": "Document Info", "DE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "Fast web view", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "<PERSON><PERSON> tả<PERSON>...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Last modified by", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Last Modified", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Owner", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "<PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>n bản", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "PDF Producer", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "Tagged PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "PDF Version", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "DE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON><PERSON> cá nhân có quyền", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "<PERSON><PERSON><PERSON><PERSON> tượ<PERSON> có dấu cách", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "<PERSON><PERSON><PERSON><PERSON> kê", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subject", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Tiêu đề tài liệu", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Uploaded", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "Từ", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "DE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "<PERSON><PERSON><PERSON><PERSON> truy cập", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON> đ<PERSON>i quyền truy cập", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON><PERSON> cá nhân có quyền", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Warning", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "With password", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON>ả<PERSON> vệ tài liệu", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "With signature", "DE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Chữ ký hợp lệ đã được thêm vào tài liệu.<br>Tài liệu được bảo vệ khỏi việc bị chỉnh sửa", "DE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the document by adding an<br>invisible digital signature", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Edit document", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Editing will remove signatures from the document.<br>Continue?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "This document has been protected with password", "DE.Views.FileMenuPanels.ProtectDoc.txtProtectDocument": "Encrypt this document with a password", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "This document needs to be signed.", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Chữ ký hợp lệ đã được thêm vào tài liệu. Tài liệu được bảo vệ khỏi việc bị chỉnh sửa", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Some of the digital signatures in the document are invalid or could not be verified. The document is protected from editing.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "View signatures", "DE.Views.FileMenuPanels.Settings.okButtonText": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "Chế độ đồng chỉnh sửa", "DE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strFontRender": "Phông chữ gợi ý", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignore words in UPPERCASE", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignore words with numbers", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "Macros settings", "DE.Views.FileMenuPanels.Settings.strPasteButton": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> chọn dán khi nội dung đ<PERSON><PERSON><PERSON> dán", "DE.Views.FileMenuPanels.Settings.strRTLSupport": "RTL interface", "DE.Views.FileMenuPanels.Settings.strShowChanges": "Thay đ<PERSON>i <PERSON> tác Thời gian thực", "DE.Views.FileMenuPanels.Settings.strShowComments": "<PERSON><PERSON><PERSON> nhận xét như chữ", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Show changes from other users", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "<PERSON><PERSON><PERSON> nhận xét đ<PERSON><PERSON><PERSON> g<PERSON> quyết", "DE.Views.FileMenuPanels.Settings.strStrict": "Nghiêm ngặt", "DE.Views.FileMenuPanels.Settings.strTabStyle": "Tab style", "DE.Views.FileMenuPanels.Settings.strTheme": "Interface theme", "DE.Views.FileMenuPanels.Settings.strUnit": "Đơn vị đo lư<PERSON>", "DE.Views.FileMenuPanels.Settings.strZoom": "<PERSON><PERSON><PERSON> trị <PERSON> to Mặc định", "DE.Views.FileMenuPanels.Settings.text10Minutes": "Mỗi 10 phút", "DE.Views.FileMenuPanels.Settings.text30Minutes": "Mỗi 30 phút", "DE.Views.FileMenuPanels.Settings.text5Minutes": "Mỗi 5 phút", "DE.Views.FileMenuPanels.Settings.text60Minutes": "Mỗi giờ", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "Hướng dẫn Căn chỉnh", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "Tự động khôi phục", "DE.Views.FileMenuPanels.Settings.textAutoSave": "<PERSON><PERSON> động lưu", "DE.Views.FileMenuPanels.Settings.textDisabled": "Tắt", "DE.Views.FileMenuPanels.Settings.textFill": "Fill", "DE.Views.FileMenuPanels.Settings.textForceSave": "Lưu vào Server", "DE.Views.FileMenuPanels.Settings.textLine": "Line", "DE.Views.FileMenuPanels.Settings.textMinute": "Mỗi phút", "DE.Views.FileMenuPanels.Settings.textOldVersions": "<PERSON><PERSON><PERSON> cho các tệp tương thích với các phiên bản MS Word cũ hơn khi được lưu dưới dạng DOCX, DOTX", "DE.Views.FileMenuPanels.Settings.textSmartSelection": "Use smart paragraph selection", "DE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "Advanced settings", "DE.Views.FileMenuPanels.Settings.txtAll": "<PERSON><PERSON> t<PERSON>t cả", "DE.Views.FileMenuPanels.Settings.txtAppearance": "Appearance", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "AutoCorrect options...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "Default cache mode", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "Show by click in balloons", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "Show by hover in tooltips", "DE.Views.FileMenuPanels.Settings.txtCm": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "<PERSON><PERSON><PERSON> t<PERSON>c", "DE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "Customize quick access", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "Turn on document dark mode", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "Editing and saving", "DE.Views.FileMenuPanels.Settings.txtFastTip": "Đồng chỉnh sửa thời gian thực. Tất cả các thay đổi được lưu một cách tự động", "DE.Views.FileMenuPanels.Settings.txtFitPage": "Vừa với trang", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "Vừa với <PERSON> rộng", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Hieroglyphs", "DE.Views.FileMenuPanels.Settings.txtInch": "Inch", "DE.Views.FileMenuPanels.Settings.txtLast": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtLastUsed": "Last used", "DE.Views.FileMenuPanels.Settings.txtMac": "như OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtNone": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtProofing": "Proofing", "DE.Views.FileMenuPanels.Settings.txtPt": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "Show the Quick Print button in the editor header", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "Enable all", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Enable all macros without a notification", "DE.Views.FileMenuPanels.Settings.txtScreenReader": "Turn on screen reader support", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "Show track changes", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "<PERSON><PERSON><PERSON> tra ch<PERSON>h tả", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "Disable All", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Disable all macros without a notification", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "Use the \"Save\" button to sync the changes you and others make", "DE.Views.FileMenuPanels.Settings.txtTabBack": "Use toolbar color as tabs background", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "<PERSON><PERSON><PERSON> thông báo", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Disable all macros with a notification", "DE.Views.FileMenuPanels.Settings.txtWin": "như Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "Workspace", "DE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "DE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save copy as", "DE.Views.FormSettings.textAlways": "Always", "DE.Views.FormSettings.textAnyone": "Anyone", "DE.Views.FormSettings.textAspect": "Lock aspect ratio", "DE.Views.FormSettings.textAtLeast": "At least", "DE.Views.FormSettings.textAuto": "Auto", "DE.Views.FormSettings.textAutofit": "AutoFit", "DE.Views.FormSettings.textBackgroundColor": "Background color", "DE.Views.FormSettings.textCheckbox": "Checkbox", "DE.Views.FormSettings.textCheckDefault": "Checkbox is checked by default", "DE.Views.FormSettings.textColor": "Border color", "DE.Views.FormSettings.textComb": "Comb of characters", "DE.Views.FormSettings.textCombobox": "Combo box", "DE.Views.FormSettings.textComplex": "Complex field", "DE.Views.FormSettings.textConnected": "Fields connected", "DE.Views.FormSettings.textCreditCard": "Credit card number (e.g 4111-1111-1111-1111)", "DE.Views.FormSettings.textDateField": "Date & time field", "DE.Views.FormSettings.textDateFormat": "Display the date like this", "DE.Views.FormSettings.textDefValue": "Default value", "DE.Views.FormSettings.textDelete": "Delete", "DE.Views.FormSettings.textDigits": "Digits", "DE.Views.FormSettings.textDisconnect": "Disconnect", "DE.Views.FormSettings.textDropDown": "Dropdown", "DE.Views.FormSettings.textExact": "Exactly", "DE.Views.FormSettings.textField": "Text field", "DE.Views.FormSettings.textFillRoles": "Who needs to fill this out?", "DE.Views.FormSettings.textFixed": "Fixed size field", "DE.Views.FormSettings.textFormat": "Format", "DE.Views.FormSettings.textFormatSymbols": "Allowed symbols", "DE.Views.FormSettings.textFromFile": "<PERSON><PERSON> tệp", "DE.Views.FormSettings.textFromStorage": "From storage", "DE.Views.FormSettings.textFromUrl": "From URL", "DE.Views.FormSettings.textGroupKey": "Group key", "DE.Views.FormSettings.textImage": "Image", "DE.Views.FormSettings.textKey": "Key", "DE.Views.FormSettings.textLang": "Language", "DE.Views.FormSettings.textLetters": "Letters", "DE.Views.FormSettings.textLock": "Lock", "DE.Views.FormSettings.textMask": "Arbitrary Mask", "DE.Views.FormSettings.textMaxChars": "Characters limit", "DE.Views.FormSettings.textMulti": "Multiline field", "DE.Views.FormSettings.textNever": "Never", "DE.Views.FormSettings.textNoBorder": "No border", "DE.Views.FormSettings.textNone": "None", "DE.Views.FormSettings.textPhone1": "Phone Number (e.g. (*************)", "DE.Views.FormSettings.textPhone2": "Phone Number (e.g. +447911123456)", "DE.Views.FormSettings.textPlaceholder": "Placeholder", "DE.Views.FormSettings.textRadiobox": "Radio button", "DE.Views.FormSettings.textRadioChoice": "Radio button choice", "DE.Views.FormSettings.textRadioDefault": "But<PERSON> is checked by default", "DE.Views.FormSettings.textReg": "Regular expression", "DE.Views.FormSettings.textRequired": "Required", "DE.Views.FormSettings.textScale": "When to scale", "DE.Views.FormSettings.textSelectImage": "Select Image", "DE.Views.FormSettings.textSignature": "Signature", "DE.Views.FormSettings.textTag": "Tag", "DE.Views.FormSettings.textTip": "Tip", "DE.Views.FormSettings.textTipAdd": "Add new value", "DE.Views.FormSettings.textTipDelete": "Delete value", "DE.Views.FormSettings.textTipDown": "Move down", "DE.Views.FormSettings.textTipUp": "Move up", "DE.Views.FormSettings.textTooBig": "Image is too big", "DE.Views.FormSettings.textTooSmall": "Image is too small", "DE.Views.FormSettings.textUKPassport": "UK Passport number (e.g. *********)", "DE.Views.FormSettings.textUnlock": "Unlock", "DE.Views.FormSettings.textUSSSN": "US SSN (e.g. ***********)", "DE.Views.FormSettings.textValue": "Value options", "DE.Views.FormSettings.textWidth": "Cell width", "DE.Views.FormSettings.textZipCodeUS": "US ZIP Code (e.g. 92663 or 92663-1234)", "DE.Views.FormsTab.capBtnCheckBox": "Checkbox", "DE.Views.FormsTab.capBtnComboBox": "Combo Box", "DE.Views.FormsTab.capBtnComplex": "Complex Field", "DE.Views.FormsTab.capBtnDownloadForm": "Download As PDF", "DE.Views.FormsTab.capBtnDropDown": "Dropdown", "DE.Views.FormsTab.capBtnEmail": "Email Address", "DE.Views.FormsTab.capBtnImage": "Image", "DE.Views.FormsTab.capBtnManager": "Manage Roles", "DE.Views.FormsTab.capBtnNext": "Next Field", "DE.Views.FormsTab.capBtnPhone": "Phone Number", "DE.Views.FormsTab.capBtnPrev": "Previous Field", "DE.Views.FormsTab.capBtnRadioBox": "Radio Button", "DE.Views.FormsTab.capBtnSaveForm": "<PERSON><PERSON><PERSON> dạng PDF", "DE.Views.FormsTab.capBtnSaveFormDesktop": "Save As...", "DE.Views.FormsTab.capBtnSignature": "Signature Field", "DE.Views.FormsTab.capBtnSubmit": "Complete & Submit", "DE.Views.FormsTab.capBtnText": "Text Field", "DE.Views.FormsTab.capBtnView": "View Form", "DE.Views.FormsTab.capCreditCard": "Credit Card", "DE.Views.FormsTab.capDateTime": "Ngày & Giờ", "DE.Views.FormsTab.capZipCode": "ZIP Code", "DE.Views.FormsTab.helpTextFillStatus": "This form is ready for role-based filling. Click on the status button to check the filling stage.", "DE.Views.FormsTab.textAnyone": "Anyone", "DE.Views.FormsTab.textClear": "Clear Fields", "DE.Views.FormsTab.textClearFields": "Clear All Fields", "DE.Views.FormsTab.textCreateForm": "Add fields and create a fillable PDF", "DE.Views.FormsTab.textFilled": "Filled", "DE.Views.FormsTab.textGotIt": "Got it", "DE.Views.FormsTab.textHighlight": "Highlight Settings", "DE.Views.FormsTab.textNoHighlight": "No highlighting", "DE.Views.FormsTab.textRequired": "To submit the form, you must fill in all required fields", "DE.Views.FormsTab.textSubmited": "Form submitted successfully", "DE.Views.FormsTab.textSubmitOk": "Your PDF form has been saved in the Complete section. You can fill out this form again and send another result.", "DE.Views.FormsTab.tipCheckBox": "Insert checkbox", "DE.Views.FormsTab.tipComboBox": "Insert combo box", "DE.Views.FormsTab.tipComplexField": "Insert complex field", "DE.Views.FormsTab.tipCreateField": "To create a field select the desired field type on the toolbar and click on it. The field will appear in the document.", "DE.Views.FormsTab.tipCreditCard": "Insert credit card number", "DE.Views.FormsTab.tipDateTime": "Insert date and time", "DE.Views.FormsTab.tipDownloadForm": "<PERSON><PERSON><PERSON> tệp dư<PERSON><PERSON> dạng PDF có thể điền vào", "DE.Views.FormsTab.tipDropDown": "Insert dropdown list", "DE.Views.FormsTab.tipEmailField": "Insert email address", "DE.Views.FormsTab.tipFieldSettings": "You can configure selected fields on the right sidebar. Click this icon to open the field settings.", "DE.Views.FormsTab.tipFieldsLink": "Learn more about field parameters", "DE.Views.FormsTab.tipFirstPage": "Go to the first page", "DE.Views.FormsTab.tipFixedText": "Insert fixed text field", "DE.Views.FormsTab.tipFormGroupKey": "Group radio buttons to make the filling process faster. Choices with the same names will be synchronized. Users can only tick one radio button from the group.", "DE.Views.FormsTab.tipFormKey": "You can assign a key to a field or a group of fields. When a user fills in the data, it will be copied to all the fields with the same key.", "DE.Views.FormsTab.tipHelpRoles": "Use the Manage Roles feature to group fields by purpose and assign the responsible team members.", "DE.Views.FormsTab.tipImageField": "Insert image", "DE.Views.FormsTab.tipInlineText": "Insert inline text field", "DE.Views.FormsTab.tipLastPage": "Go to the last page", "DE.Views.FormsTab.tipManager": "Manage roles", "DE.Views.FormsTab.tipNextForm": "Go to the next field", "DE.Views.FormsTab.tipNextPage": "Go to the next page", "DE.Views.FormsTab.tipPhoneField": "Insert phone number", "DE.Views.FormsTab.tipPrevForm": "Go to the previous field", "DE.Views.FormsTab.tipPrevPage": "Go to the previous page", "DE.Views.FormsTab.tipRadioBox": "Insert radio button", "DE.Views.FormsTab.tipRolesLink": "Learn more about roles", "DE.Views.FormsTab.tipSaveFile": "Bấm \"<PERSON><PERSON><PERSON>\" để lưu biểu mẫu ở định dạng sẵn sàng để điền", "DE.Views.FormsTab.tipSaveForm": "<PERSON><PERSON><PERSON> tệp dư<PERSON><PERSON> dạng PDF có thể điền vào", "DE.Views.FormsTab.tipSignField": "Insert signature field", "DE.Views.FormsTab.tipSubmit": "Submit form", "DE.Views.FormsTab.tipTextField": "Insert text field", "DE.Views.FormsTab.tipViewForm": "View form", "DE.Views.FormsTab.tipZipCode": "Insert ZIP code", "DE.Views.FormsTab.txtFixedDesc": "Insert fixed text field", "DE.Views.FormsTab.txtFixedText": "Fixed", "DE.Views.FormsTab.txtInlineDesc": "Insert inline text field", "DE.Views.FormsTab.txtInlineText": "Inline", "DE.Views.FormsTab.txtUntitled": "Untitled", "DE.Views.HeaderFooterSettings.textBottomCenter": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> bên <PERSON>", "DE.Views.HeaderFooterSettings.textBottomLeft": "<PERSON><PERSON><PERSON><PERSON> cùng bên trái", "DE.Views.HeaderFooterSettings.textBottomPage": "<PERSON><PERSON> trang", "DE.Views.HeaderFooterSettings.textBottomRight": "<PERSON><PERSON><PERSON><PERSON> cùng bên ph<PERSON>i", "DE.Views.HeaderFooterSettings.textDiffFirst": "<PERSON><PERSON><PERSON><PERSON> trang đầu tiên", "DE.Views.HeaderFooterSettings.textDiffOdd": "<PERSON><PERSON><PERSON><PERSON> trang lẻ và chẵn", "DE.Views.HeaderFooterSettings.textFrom": "Start at", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "Footer từ dưới", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "Header từ trên c<PERSON>ng", "DE.Views.HeaderFooterSettings.textInsertCurrent": "Insert to current position", "DE.Views.HeaderFooterSettings.textNumFormat": "Number format", "DE.Views.HeaderFooterSettings.textOptions": "<PERSON><PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textPageNum": "<PERSON><PERSON><PERSON> s<PERSON> trang", "DE.Views.HeaderFooterSettings.textPageNumbering": "Page numbering", "DE.Views.HeaderFooterSettings.textPosition": "<PERSON><PERSON> trí", "DE.Views.HeaderFooterSettings.textPrev": "Continue from previous section", "DE.Views.HeaderFooterSettings.textSameAs": "<PERSON><PERSON><PERSON> kết với trang trước", "DE.Views.HeaderFooterSettings.textTopCenter": "<PERSON><PERSON> trên ch<PERSON> g<PERSON>", "DE.Views.HeaderFooterSettings.textTopLeft": "<PERSON><PERSON> trên bên trái", "DE.Views.HeaderFooterSettings.textTopPage": "Top of page", "DE.Views.HeaderFooterSettings.textTopRight": "<PERSON><PERSON> trên bên <PERSON>i", "DE.Views.HeaderFooterSettings.txtMoreTypes": "More types", "DE.Views.HyperlinkSettingsDialog.textDefault": "<PERSON><PERSON><PERSON><PERSON> văn bản đã chọn", "DE.Views.HyperlinkSettingsDialog.textDisplay": "<PERSON><PERSON><PERSON> thị", "DE.Views.HyperlinkSettingsDialog.textExternal": "External link", "DE.Views.HyperlinkSettingsDialog.textInternal": "Place in document", "DE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "DE.Views.HyperlinkSettingsDialog.textTitle": "<PERSON><PERSON><PERSON> đặt <PERSON><PERSON>u liên kết", "DE.Views.HyperlinkSettingsDialog.textTooltip": "<PERSON><PERSON><PERSON> b<PERSON>n <PERSON>ip", "DE.Views.HyperlinkSettingsDialog.textUrl": "<PERSON><PERSON><PERSON> kết t<PERSON>i", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "Beginning of document", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "Bookmarks", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "Headings", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "Trường này phải là một URL có định dạng \"http://www.example.com\"", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "This field is limited to 2083 characters", "DE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "DE.Views.HyphenationDialog.textAuto": "Automatically hyphenate document", "DE.Views.HyphenationDialog.textCaps": "Hyphenate words in CAPS", "DE.Views.HyphenationDialog.textLimit": "Limit consecutive hyphens to", "DE.Views.HyphenationDialog.textNoLimit": "No limit", "DE.Views.HyphenationDialog.textTitle": "Hyphenation", "DE.Views.HyphenationDialog.textZone": "Hyphenation zone", "DE.Views.ImageSettings.strTransparency": "Opacity", "DE.Views.ImageSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "DE.Views.ImageSettings.textCrop": "Crop", "DE.Views.ImageSettings.textCropFill": "Fill", "DE.Views.ImageSettings.textCropFit": "Fit", "DE.Views.ImageSettings.textCropToShape": "Crop to shape", "DE.Views.ImageSettings.textEdit": "Chỉnh sửa", "DE.Views.ImageSettings.textEditObject": "Chỉnh sửa <PERSON><PERSON> tư<PERSON>", "DE.Views.ImageSettings.textFitMargins": "Vừa với Lề", "DE.Views.ImageSettings.textFlip": "Flip", "DE.Views.ImageSettings.textFromFile": "<PERSON><PERSON> tệp", "DE.Views.ImageSettings.textFromStorage": "From storage", "DE.Views.ImageSettings.textFromUrl": "Từ URL", "DE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON> cao", "DE.Views.ImageSettings.textHint270": "Quay 90° ng<PERSON><PERSON><PERSON> chiều kim đồng hồ", "DE.Views.ImageSettings.textHint90": "Quay 90° chi<PERSON>u kim đồng hồ", "DE.Views.ImageSettings.textHintFlipH": "Flip horizontally", "DE.Views.ImageSettings.textHintFlipV": "Flip vertically", "DE.Views.ImageSettings.textInsert": "<PERSON><PERSON> thế <PERSON>nh", "DE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON> thước mặc định", "DE.Views.ImageSettings.textRecentlyUsed": "Recently used", "DE.Views.ImageSettings.textResetCrop": "Reset crop", "DE.Views.ImageSettings.textRotate90": "Quay 90°", "DE.Views.ImageSettings.textRotation": "Quay", "DE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "DE.Views.ImageSettings.textWrap": "<PERSON><PERSON><PERSON> ng<PERSON> dòng", "DE.Views.ImageSettings.txtBehind": "Sau", "DE.Views.ImageSettings.txtInFront": "Ở trước", "DE.Views.ImageSettings.txtInline": "<PERSON><PERSON><PERSON> dòng", "DE.Views.ImageSettings.txtSquare": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.txtThrough": "<PERSON><PERSON><PERSON><PERSON> qua", "DE.Views.ImageSettings.txtTight": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.txtTopAndBottom": "<PERSON><PERSON><PERSON><PERSON> cùng và dưới cùng", "DE.Views.ImageSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON><PERSON> padding cho vă<PERSON> bản", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "DE.Views.ImageSettingsAdvanced.textAlignment": "<PERSON><PERSON>n chỉnh", "DE.Views.ImageSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "DE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "DE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON> tên", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "Khóa tỷ lệ bên ngoài", "DE.Views.ImageSettingsAdvanced.textAutofit": "AutoFit", "DE.Views.ImageSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> thước khởi đầu", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "<PERSON><PERSON>u khởi đầu", "DE.Views.ImageSettingsAdvanced.textBelow": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "<PERSON><PERSON><PERSON> dòng", "DE.Views.ImageSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textCenter": "Trung tâm", "DE.Views.ImageSettingsAdvanced.textCharacter": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textColumn": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textDistance": "<PERSON><PERSON><PERSON><PERSON> cách từ văn bản", "DE.Views.ImageSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textEndStyle": "<PERSON><PERSON><PERSON> kết th<PERSON>c", "DE.Views.ImageSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON> m<PERSON>u", "DE.Views.ImageSettingsAdvanced.textFlipped": "Flipped", "DE.Views.ImageSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON> cao", "DE.Views.ImageSettingsAdvanced.textHorizontal": "Nằm ngang", "DE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontally", "DE.Views.ImageSettingsAdvanced.textJoinType": "<PERSON><PERSON><PERSON> k<PERSON>", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "Tỷ lệ không đổi", "DE.Views.ImageSettingsAdvanced.textLeft": "Trái", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "<PERSON><PERSON> trái", "DE.Views.ImageSettingsAdvanced.textLine": "Đường kẻ", "DE.Views.ImageSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON> đường kẻ", "DE.Views.ImageSettingsAdvanced.textMargin": "Lề", "DE.Views.ImageSettingsAdvanced.textMiter": "Góc 45 độ", "DE.Views.ImageSettingsAdvanced.textMove": "<PERSON> chuyển đối tượng cùng văn bản", "DE.Views.ImageSettingsAdvanced.textOptions": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "<PERSON><PERSON><PERSON> thước mặc định", "DE.Views.ImageSettingsAdvanced.textOverlap": "<PERSON> phép chồng chéo", "DE.Views.ImageSettingsAdvanced.textPage": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textParagraph": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>n bản", "DE.Views.ImageSettingsAdvanced.textPosition": "<PERSON><PERSON> trí", "DE.Views.ImageSettingsAdvanced.textPositionPc": "<PERSON><PERSON> trí cân xứng", "DE.Views.ImageSettingsAdvanced.textRelative": "c<PERSON> xứng với", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textResizeFit": "Resize shape to fit text", "DE.Views.ImageSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textRightMargin": "<PERSON><PERSON> bên <PERSON>", "DE.Views.ImageSettingsAdvanced.textRightOf": "bên p<PERSON><PERSON><PERSON> c<PERSON>a", "DE.Views.ImageSettingsAdvanced.textRotation": "Quay", "DE.Views.ImageSettingsAdvanced.textRound": "Tròn", "DE.Views.ImageSettingsAdvanced.textShape": "<PERSON>ài đặt hình dạng", "DE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textTextBox": "Text box", "DE.Views.ImageSettingsAdvanced.textTitle": "Hình <PERSON>nh - Cài đặt Nâng cao", "DE.Views.ImageSettingsAdvanced.textTitleChart": "<PERSON><PERSON><PERSON><PERSON> đồ - <PERSON>ài đặt Nâng cao", "DE.Views.ImageSettingsAdvanced.textTitleShape": "<PERSON><PERSON>nh dạng - <PERSON>ài đặt Nâng cao", "DE.Views.ImageSettingsAdvanced.textTop": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textTopMargin": "<PERSON><PERSON> trên", "DE.Views.ImageSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textVertically": "Vertically", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "<PERSON><PERSON> & <PERSON><PERSON><PERSON> tên", "DE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "DE.Views.ImageSettingsAdvanced.textWrap": "<PERSON><PERSON><PERSON> ng<PERSON> dòng", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "Sau", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "Ở trước", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "<PERSON><PERSON><PERSON> dòng", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "<PERSON><PERSON><PERSON><PERSON> qua", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "<PERSON><PERSON><PERSON><PERSON> cùng và <PERSON>ướ<PERSON> cùng", "DE.Views.LeftMenu.ariaLeftMenu": "Left menu", "DE.Views.LeftMenu.tipAbout": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "DE.Views.LeftMenu.tipChat": "Cha<PERSON>", "DE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON> lu<PERSON>", "DE.Views.LeftMenu.tipNavigation": "Navigation", "DE.Views.LeftMenu.tipOutline": "Headings", "DE.Views.LeftMenu.tipPageThumbnails": "Page thumbnails", "DE.Views.LeftMenu.tipPlugins": "Plugin", "DE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.tipSupport": "<PERSON><PERSON><PERSON> & Hỗ trợ", "DE.Views.LeftMenu.tipTitles": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "DE.Views.LeftMenu.txtDeveloper": "CHẾ ĐỘ NHÀ PHÁT TRIỂN", "DE.Views.LeftMenu.txtEditor": "Document Editor", "DE.Views.LeftMenu.txtLimit": "Limit access", "DE.Views.LeftMenu.txtTrial": "TRIAL MODE", "DE.Views.LeftMenu.txtTrialDev": "Trial Developer Mode", "DE.Views.LineNumbersDialog.textAddLineNumbering": "Add line numbering", "DE.Views.LineNumbersDialog.textApplyTo": "Apply changes to", "DE.Views.LineNumbersDialog.textContinuous": "Continuous", "DE.Views.LineNumbersDialog.textCountBy": "Count by", "DE.Views.LineNumbersDialog.textDocument": "Whole document", "DE.Views.LineNumbersDialog.textForward": "This point forward", "DE.Views.LineNumbersDialog.textFromText": "From text", "DE.Views.LineNumbersDialog.textNumbering": "Numbering", "DE.Views.LineNumbersDialog.textRestartEachPage": "Restart each page", "DE.Views.LineNumbersDialog.textRestartEachSection": "Restart each section", "DE.Views.LineNumbersDialog.textSection": "Current section", "DE.Views.LineNumbersDialog.textStartAt": "Start at", "DE.Views.LineNumbersDialog.textTitle": "Line numbers", "DE.Views.LineNumbersDialog.txtAutoText": "Auto", "DE.Views.Links.capBtnAddText": "Add Text", "DE.Views.Links.capBtnBookmarks": "Bookmark", "DE.Views.Links.capBtnCaption": "Caption", "DE.Views.Links.capBtnContentsUpdate": "Update Table", "DE.Views.Links.capBtnCrossRef": "Cross-reference", "DE.Views.Links.capBtnInsContents": "<PERSON><PERSON><PERSON>", "DE.Views.Links.capBtnInsFootnote": "Footnote", "DE.Views.Links.capBtnInsLink": "Hyperlink", "DE.Views.Links.capBtnTOF": "<PERSON><PERSON><PERSON> h<PERSON>", "DE.Views.Links.confirmDeleteFootnotes": "Do you want to delete all footnotes?", "DE.Views.Links.confirmReplaceTOF": "Bạn có muốn thay thế mục lục hình đư<PERSON>c chọn không?", "DE.Views.Links.mniConvertNote": "Convert all notes", "DE.Views.Links.mniDelFootnote": "Delete all notes", "DE.Views.Links.mniInsEndnote": "Insert endnote", "DE.Views.Links.mniInsFootnote": "Insert footnote", "DE.Views.Links.mniNoteSettings": "Notes settings", "DE.Views.Links.textContentsRemove": "Remove table of contents", "DE.Views.Links.textContentsSettings": "Settings", "DE.Views.Links.textConvertToEndnotes": "Convert all footnotes to endnotes", "DE.Views.Links.textConvertToFootnotes": "Convert all endnotes to footnotes", "DE.Views.Links.textGotoEndnote": "Go to endnotes", "DE.Views.Links.textGotoFootnote": "Go to footnotes", "DE.Views.Links.textSwapNotes": "Swap footnotes and endnotes", "DE.Views.Links.textUpdateAll": "Update entire table", "DE.Views.Links.textUpdatePages": "Update page numbers only", "DE.Views.Links.tipAddText": "Include heading in the table of contents", "DE.Views.Links.tipBookmarks": "Create a bookmark", "DE.Views.Links.tipCaption": "Insert caption", "DE.Views.Links.tipContents": "Insert table of contents", "DE.Views.Links.tipContentsUpdate": "Update table of contents", "DE.Views.Links.tipCrossRef": "Insert cross-reference", "DE.Views.Links.tipInsertHyperlink": "Add hyperlink", "DE.Views.Links.tipNotes": "Insert or edit footnotes", "DE.Views.Links.tipTableFigures": "<PERSON><PERSON><PERSON> m<PERSON> lục hình", "DE.Views.Links.tipTableFiguresUpdate": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> mục lụ<PERSON> h<PERSON>nh", "DE.Views.Links.titleUpdateTOF": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> mục lụ<PERSON> h<PERSON>nh", "DE.Views.Links.txtDontShowTof": "Do not show in table of contents", "DE.Views.Links.txtLevel": "Level", "DE.Views.ListIndentsDialog.textSpace": "Space", "DE.Views.ListIndentsDialog.textTab": "Tab character", "DE.Views.ListIndentsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> danh s<PERSON>ch", "DE.Views.ListIndentsDialog.txtFollowBullet": "Follow bullet with", "DE.Views.ListIndentsDialog.txtFollowNumber": "Follow number with", "DE.Views.ListIndentsDialog.txtIndent": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> văn bản", "DE.Views.ListIndentsDialog.txtNone": "None", "DE.Views.ListIndentsDialog.txtPosBullet": "Bullet position", "DE.Views.ListIndentsDialog.txtPosNumber": "Number position", "DE.Views.ListSettingsDialog.textAuto": "Automatic", "DE.Views.ListSettingsDialog.textBold": "Bold", "DE.Views.ListSettingsDialog.textCenter": "Center", "DE.Views.ListSettingsDialog.textHide": "Hide settings", "DE.Views.ListSettingsDialog.textItalic": "Italic", "DE.Views.ListSettingsDialog.textLeft": "Left", "DE.Views.ListSettingsDialog.textLevel": "Level", "DE.Views.ListSettingsDialog.textMore": "<PERSON><PERSON> thêm cài đặt", "DE.Views.ListSettingsDialog.textPreview": "Preview", "DE.Views.ListSettingsDialog.textRight": "Right", "DE.Views.ListSettingsDialog.textSelectLevel": "Select level", "DE.Views.ListSettingsDialog.textSpace": "Space", "DE.Views.ListSettingsDialog.textTab": "Tab character", "DE.Views.ListSettingsDialog.txtAlign": "Alignment", "DE.Views.ListSettingsDialog.txtAlignAt": "at", "DE.Views.ListSettingsDialog.txtBullet": "Bullet", "DE.Views.ListSettingsDialog.txtColor": "Color", "DE.Views.ListSettingsDialog.txtFollow": "Follow number with", "DE.Views.ListSettingsDialog.txtFontName": "Font", "DE.Views.ListSettingsDialog.txtInclcudeLevel": "Include level number", "DE.Views.ListSettingsDialog.txtIndent": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> văn bản", "DE.Views.ListSettingsDialog.txtLikeText": "Like a text", "DE.Views.ListSettingsDialog.txtMoreTypes": "More types", "DE.Views.ListSettingsDialog.txtNewBullet": "New bullet", "DE.Views.ListSettingsDialog.txtNone": "None", "DE.Views.ListSettingsDialog.txtNumFormatString": "Number format", "DE.Views.ListSettingsDialog.txtRestart": "Restart list", "DE.Views.ListSettingsDialog.txtSize": "Size", "DE.Views.ListSettingsDialog.txtStart": "Start at", "DE.Views.ListSettingsDialog.txtSymbol": "Symbol", "DE.Views.ListSettingsDialog.txtTabStop": "Add tab stop at", "DE.Views.ListSettingsDialog.txtTitle": "List settings", "DE.Views.ListSettingsDialog.txtType": "Type", "DE.Views.ListTypesAdvanced.labelSelect": "Select list type", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "Theme", "DE.Views.MailMergeEmailDlg.textAttachDocx": "<PERSON><PERSON><PERSON> kèm dưới dạng DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "<PERSON><PERSON><PERSON> k<PERSON> dư<PERSON> dạng PDF", "DE.Views.MailMergeEmailDlg.textFileName": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textFormat": "Định dạng mail", "DE.Views.MailMergeEmailDlg.textFrom": "Từ", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "<PERSON>", "DE.Views.MailMergeEmailDlg.textSubject": "<PERSON><PERSON><PERSON> tiêu đề", "DE.Views.MailMergeEmailDlg.textTitle": "<PERSON><PERSON><PERSON> t<PERSON>i <PERSON>", "DE.Views.MailMergeEmailDlg.textTo": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textWarning": "<PERSON><PERSON>nh báo!", "DE.Views.MailMergeEmailDlg.textWarningMsg": "<PERSON><PERSON> lòng lưu ý rằng bạn không thể dừng gửi thư sau khi nhấp vào nút 'Gửi'.", "DE.Views.MailMergeSettings.downloadMergeTitle": "<PERSON><PERSON> trộn", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "<PERSON>r<PERSON><PERSON> không thành công.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "DE.Views.MailMergeSettings.textAddRecipients": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiên hãy thêm người nhận vào danh s<PERSON>ch", "DE.Views.MailMergeSettings.textAll": "<PERSON><PERSON><PERSON> cả bản ghi", "DE.Views.MailMergeSettings.textCurrent": "<PERSON><PERSON><PERSON> ghi hiện tại", "DE.Views.MailMergeSettings.textDataSource": "<PERSON><PERSON><PERSON><PERSON> dữ liệu", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "<PERSON><PERSON><PERSON> về", "DE.Views.MailMergeSettings.textEditData": "Chỉnh sửa danh sách ng<PERSON><PERSON>i nhận", "DE.Views.MailMergeSettings.textEmail": "Email", "DE.Views.MailMergeSettings.textFrom": "Từ", "DE.Views.MailMergeSettings.textGoToMail": "Tới Mail", "DE.Views.MailMergeSettings.textHighlight": "<PERSON><PERSON> sáng các trường trộn", "DE.Views.MailMergeSettings.textInsertField": "<PERSON><PERSON><PERSON> trường trộn", "DE.Views.MailMergeSettings.textMaxRecepients": "Tối đa 100 người nhận.", "DE.Views.MailMergeSettings.textMerge": "Trộn", "DE.Views.MailMergeSettings.textMergeFields": "Tr<PERSON><PERSON> các trường", "DE.Views.MailMergeSettings.textMergeTo": "Trộn với", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.textPreview": "<PERSON><PERSON> kế<PERSON> quả", "DE.Views.MailMergeSettings.textReadMore": "<PERSON><PERSON><PERSON>ê<PERSON>", "DE.Views.MailMergeSettings.textSendMsg": "Tất cả tin nhắn mail đã sẵn sàng và sẽ được gửi đi trong chốc lát.<br>Tốc độ gửi thư tùy thuộc vào dịch vụ thư của bạn.<br>Bạn có thể tiếp tục làm việc với tài liệu hoặc đóng nó. Sau khi thao tác hoàn tất, thông báo sẽ được gửi đến địa chỉ email đăng ký của bạn.", "DE.Views.MailMergeSettings.textTo": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.txtFirst": "<PERSON><PERSON><PERSON> bản ghi đầu tiên", "DE.Views.MailMergeSettings.txtFromToError": "<PERSON><PERSON><PERSON> trị \"Từ\" phải nhỏ hơn giá trị \"Đến\"", "DE.Views.MailMergeSettings.txtLast": "<PERSON><PERSON><PERSON> bản ghi cuối cùng", "DE.Views.MailMergeSettings.txtNext": "<PERSON><PERSON><PERSON> bản ghi tiếp theo", "DE.Views.MailMergeSettings.txtPrev": "<PERSON><PERSON><PERSON> bản ghi trư<PERSON><PERSON> đó", "DE.Views.MailMergeSettings.txtUntitled": "<PERSON><PERSON><PERSON><PERSON> có tiêu đề", "DE.Views.MailMergeSettings.warnProcessMailMerge": "<PERSON><PERSON><PERSON><PERSON> thể bắt đầu trộn", "DE.Views.Navigation.strNavigate": "Headings", "DE.Views.Navigation.txtClosePanel": "<PERSON><PERSON><PERSON> đầu đề", "DE.Views.Navigation.txtCollapse": "Collapse all", "DE.Views.Navigation.txtDemote": "Demote", "DE.Views.Navigation.txtEmpty": "There are no headings in the document.<br>Apply a heading style to the text so that it appears in the table of contents.", "DE.Views.Navigation.txtEmptyItem": "Empty heading", "DE.Views.Navigation.txtEmptyViewer": "There are no headings in the document.", "DE.Views.Navigation.txtExpand": "Expand all", "DE.Views.Navigation.txtExpandToLevel": "Expand to level", "DE.Views.Navigation.txtFontSize": "Font size", "DE.Views.Navigation.txtHeadingAfter": "New heading after", "DE.Views.Navigation.txtHeadingBefore": "New heading before", "DE.Views.Navigation.txtLarge": "Large", "DE.Views.Navigation.txtMedium": "Medium", "DE.Views.Navigation.txtNewHeading": "New subheading", "DE.Views.Navigation.txtPromote": "Promote", "DE.Views.Navigation.txtSelect": "<PERSON><PERSON><PERSON> n<PERSON>i dung", "DE.Views.Navigation.txtSettings": "Headings settings", "DE.Views.Navigation.txtSmall": "Small", "DE.Views.Navigation.txtWrapHeadings": "Wrap long headings", "DE.Views.NoteSettingsDialog.textApply": "<PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textApplyTo": "<PERSON><PERSON> dụng thay đổi cho", "DE.Views.NoteSettingsDialog.textContinue": "<PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textCustom": "<PERSON><PERSON><PERSON> chỉnh dấu", "DE.Views.NoteSettingsDialog.textDocEnd": "End of document", "DE.Views.NoteSettingsDialog.textDocument": "<PERSON><PERSON>n bộ tài liệu", "DE.Views.NoteSettingsDialog.textEachPage": "<PERSON><PERSON><PERSON> đầu lại ở mỗi trang", "DE.Views.NoteSettingsDialog.textEachSection": "<PERSON><PERSON><PERSON> đầu lại ở mỗi phần", "DE.Views.NoteSettingsDialog.textEndnote": "Endnote", "DE.Views.NoteSettingsDialog.textFootnote": "<PERSON><PERSON> thích cuối trang", "DE.Views.NoteSettingsDialog.textFormat": "<PERSON><PERSON><PERSON> d<PERSON>ng", "DE.Views.NoteSettingsDialog.textInsert": "<PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textLocation": "<PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textNumFormat": "<PERSON><PERSON><PERSON> dạng số", "DE.Views.NoteSettingsDialog.textPageBottom": "<PERSON><PERSON><PERSON> trang", "DE.Views.NoteSettingsDialog.textSectEnd": "End of section", "DE.Views.NoteSettingsDialog.textSection": "<PERSON><PERSON><PERSON> hi<PERSON>n tại", "DE.Views.NoteSettingsDialog.textStart": "<PERSON><PERSON><PERSON> đ<PERSON>u tại", "DE.Views.NoteSettingsDialog.textTextBottom": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> bản", "DE.Views.NoteSettingsDialog.textTitle": "Cài đặt Ghi chú", "DE.Views.NotesRemoveDialog.textEnd": "Delete all endnotes", "DE.Views.NotesRemoveDialog.textFoot": "Delete all footnotes", "DE.Views.NotesRemoveDialog.textTitle": "Delete notes", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "DE.Views.PageMarginsDialog.textBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "DE.Views.PageMarginsDialog.textGutter": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textGutterPosition": "Gutter position", "DE.Views.PageMarginsDialog.textInside": "Inside", "DE.Views.PageMarginsDialog.textLandscape": "Landscape", "DE.Views.PageMarginsDialog.textLeft": "Trái", "DE.Views.PageMarginsDialog.textMirrorMargins": "Mirror margins", "DE.Views.PageMarginsDialog.textMultiplePages": "Multiple pages", "DE.Views.PageMarginsDialog.textNormal": "Normal", "DE.Views.PageMarginsDialog.textOrientation": "Orientation", "DE.Views.PageMarginsDialog.textOutside": "Outside", "DE.Views.PageMarginsDialog.textPortrait": "Portrait", "DE.Views.PageMarginsDialog.textPreview": "Preview", "DE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTitle": "Lề", "DE.Views.PageMarginsDialog.textTop": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.txtMarginsH": "<PERSON><PERSON> trên và dưới cùng quá cao nên không thể làm chiều cao của trang", "DE.Views.PageMarginsDialog.txtMarginsW": "Lề trái và phải quá rộng nên không thể làm chiều rộng của trang", "DE.Views.PageSizeDialog.textHeight": "<PERSON><PERSON><PERSON> cao", "DE.Views.PageSizeDialog.textPreset": "Preset", "DE.Views.PageSizeDialog.textTitle": "<PERSON><PERSON><PERSON> trang", "DE.Views.PageSizeDialog.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "DE.Views.PageSizeDialog.txtCustom": "Custom", "DE.Views.PageThumbnails.textClosePanel": "Close page thumbnails", "DE.Views.PageThumbnails.textHighlightVisiblePart": "Highlight visible part of page", "DE.Views.PageThumbnails.textPageThumbnails": "Page thumbnails", "DE.Views.PageThumbnails.textThumbnailsSettings": "Thumbnails settings", "DE.Views.PageThumbnails.textThumbnailsSize": "Thumbnails size", "DE.Views.ParagraphSettings.strIndent": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "DE.Views.ParagraphSettings.strIndentsLeftText": "Left", "DE.Views.ParagraphSettings.strIndentsRightText": "Right", "DE.Views.ParagraphSettings.strIndentsSpecial": "Special", "DE.Views.ParagraphSettings.strLineHeight": "<PERSON><PERSON><PERSON><PERSON> cách dòng", "DE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch đo<PERSON>n", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "<PERSON><PERSON><PERSON><PERSON> thêm kho<PERSON>ng cách gi<PERSON>a các đoạn văn cùng kiểu", "DE.Views.ParagraphSettings.strSpacingAfter": "Sau", "DE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "DE.Views.ParagraphSettings.textAt": "Tại", "DE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.textBackColor": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.textExact": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.textFirstLine": "First line", "DE.Views.ParagraphSettings.textHanging": "Hanging", "DE.Views.ParagraphSettings.textNoneSpecial": "(none)", "DE.Views.ParagraphSettings.txtAutoText": "<PERSON><PERSON> động", "DE.Views.ParagraphSettingsAdvanced.noTabs": "<PERSON><PERSON><PERSON> tab được chỉ định sẽ xuất hiện trong trường này", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "Tất cả Drop cap", "DE.Views.ParagraphSettingsAdvanced.strBorders": "Viền & <PERSON><PERSON> màu", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "<PERSON><PERSON>t trang đằng trước", "DE.Views.ParagraphSettingsAdvanced.strDirection": "Direction", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON>ch đôi giữa chữ", "DE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Trái", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Line spacing", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "Outline level", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "After", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Before", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Special", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "<PERSON><PERSON><PERSON> các dòng cùng nhau", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "<PERSON><PERSON><PERSON> cho tiếp theo", "DE.Views.ParagraphSettingsAdvanced.strMargins": "Padding", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "<PERSON><PERSON><PERSON> soát dòng lẻ đầu trang sau", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Phông chữ", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Thụt lề & Căn chỉnh", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "Line & page breaks", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "<PERSON><PERSON>n chỉnh", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Drop cap nhỏ", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "Don't add interval between paragraphs of the same style", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "Spacing", "DE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON><PERSON> gi<PERSON>a chữ", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "Chỉ số dưới", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "Chỉ số trên", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "Suppress line numbers", "DE.Views.ParagraphSettingsAdvanced.strTabs": "Tab", "DE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON>n chỉnh", "DE.Views.ParagraphSettingsAdvanced.textAll": "All", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "At least", "DE.Views.ParagraphSettingsAdvanced.textAuto": "Multiple", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "Basic text", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "Nhấp vào biểu đồ hoặc sử dụng các nút để chọn đường viền và áp dụng kiểu đã chọn cho chúng", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h đư<PERSON> vền", "DE.Views.ParagraphSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "DE.Views.ParagraphSettingsAdvanced.textCentered": "Centered", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "<PERSON><PERSON><PERSON> c<PERSON>ch trắng", "DE.Views.ParagraphSettingsAdvanced.textContext": "Contextual", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "Contextual and discretionary", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "Contextual, historical and discretionary", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "Contextual and historical", "DE.Views.ParagraphSettingsAdvanced.textDefault": "Tab mặc định", "DE.Views.ParagraphSettingsAdvanced.textDirLtr": "Left-to-right", "DE.Views.ParagraphSettingsAdvanced.textDirRtl": "Right-to-left", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "Discretionary", "DE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textExact": "Exactly", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "First line", "DE.Views.ParagraphSettingsAdvanced.textHanging": "Hanging", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "Historical", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "Historical and discretionary", "DE.Views.ParagraphSettingsAdvanced.textJustified": "Justified", "DE.Views.ParagraphSettingsAdvanced.textLeader": "Leader", "DE.Views.ParagraphSettingsAdvanced.textLeft": "Trái", "DE.Views.ParagraphSettingsAdvanced.textLevel": "Level", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "Ligatures", "DE.Views.ParagraphSettingsAdvanced.textNone": "None", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(none)", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "OpenType features", "DE.Views.ParagraphSettingsAdvanced.textPosition": "<PERSON><PERSON> trí", "DE.Views.ParagraphSettingsAdvanced.textRemove": "Xóa", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON><PERSON> tất cả", "DE.Views.ParagraphSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON> r<PERSON>", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch", "DE.Views.ParagraphSettingsAdvanced.textStandard": "Standard only", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "Standard and contextual", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "Standard, contextual and discretionary", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "Standard, contextual and historical", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "Standard and discretionary", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "Standard, historical and discretionary", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "Standard and historical", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "Trung tâm", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "Trái", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON> trí <PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTitle": "<PERSON><PERSON><PERSON><PERSON> văn bản - <PERSON><PERSON>i đặt Nâng cao", "DE.Views.ParagraphSettingsAdvanced.textTop": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.tipAll": "Đặt viền ngoài và tất cả đường kẻ bên trong", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "Chỉ đặt viền dưới cùng", "DE.Views.ParagraphSettingsAdvanced.tipInner": "Chỉ đặt các đường ngang bên trong", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "Chỉ đặt viền bên trái", "DE.Views.ParagraphSettingsAdvanced.tipNone": "K<PERSON>ông đặt viền", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "Chỉ đặt viền ngoài", "DE.Views.ParagraphSettingsAdvanced.tipRight": "Chỉ đặt viền bên phải", "DE.Views.ParagraphSettingsAdvanced.tipTop": "Chỉ viền trên cùng", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "DE.Views.PrintWithPreview.textMarginsLast": "Last Custom", "DE.Views.PrintWithPreview.textMarginsModerate": "Moderate", "DE.Views.PrintWithPreview.textMarginsNarrow": "<PERSON>rrow", "DE.Views.PrintWithPreview.textMarginsNormal": "Normal", "DE.Views.PrintWithPreview.textMarginsWide": "Wide", "DE.Views.PrintWithPreview.txtAllPages": "All pages", "DE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "DE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "DE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "DE.Views.PrintWithPreview.txtBottom": "Bottom", "DE.Views.PrintWithPreview.txtCopies": "Copies", "DE.Views.PrintWithPreview.txtCurrentPage": "Current page", "DE.Views.PrintWithPreview.txtCustom": "Custom", "DE.Views.PrintWithPreview.txtCustomPages": "Custom print", "DE.Views.PrintWithPreview.txtLandscape": "Landscape", "DE.Views.PrintWithPreview.txtLeft": "Left", "DE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON>", "DE.Views.PrintWithPreview.txtOf": "of {0}", "DE.Views.PrintWithPreview.txtOneSide": "Print one sided", "DE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "DE.Views.PrintWithPreview.txtPage": "Page", "DE.Views.PrintWithPreview.txtPageNumInvalid": "Page number invalid", "DE.Views.PrintWithPreview.txtPageOrientation": "Page orientation", "DE.Views.PrintWithPreview.txtPages": "Pages", "DE.Views.PrintWithPreview.txtPageSize": "Page size", "DE.Views.PrintWithPreview.txtPortrait": "Portrait", "DE.Views.PrintWithPreview.txtPrint": "Print", "DE.Views.PrintWithPreview.txtPrintPdf": "Print to PDF", "DE.Views.PrintWithPreview.txtPrintRange": "Print range", "DE.Views.PrintWithPreview.txtPrintSides": "Print sides", "DE.Views.PrintWithPreview.txtRight": "Right", "DE.Views.PrintWithPreview.txtSelection": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtTop": "Top", "DE.Views.ProtectDialog.textComments": "n<PERSON><PERSON><PERSON> xét", "DE.Views.ProtectDialog.textForms": "Filling forms", "DE.Views.ProtectDialog.textReview": "Tracked changes", "DE.Views.ProtectDialog.textView": "No changes (Read only)", "DE.Views.ProtectDialog.txtAllow": "Allow only this type of editing in the document", "DE.Views.ProtectDialog.txtIncorrectPwd": "Confirmation password is not identical", "DE.Views.ProtectDialog.txtLimit": "Password is limited to 15 characters", "DE.Views.ProtectDialog.txtOptional": "optional", "DE.Views.ProtectDialog.txtPassword": "Password", "DE.Views.ProtectDialog.txtProtect": "Bả<PERSON> vệ", "DE.Views.ProtectDialog.txtRepeat": "Repeat password", "DE.Views.ProtectDialog.txtTitle": "Bả<PERSON> vệ", "DE.Views.ProtectDialog.txtWarning": "Warning: If you lose or forget the password, it cannot be recovered. Please keep it in a safe place.", "DE.Views.RightMenu.ariaRightMenu": "Right menu", "DE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON>", "DE.Views.RightMenu.txtFormSettings": "Form settings", "DE.Views.RightMenu.txtHeaderFooterSettings": "Cài đặt Header và Footer", "DE.Views.RightMenu.txtImageSettings": "<PERSON>ài đặt hình ảnh", "DE.Views.RightMenu.txtMailMergeSettings": "<PERSON>ài đặt trộn thư", "DE.Views.RightMenu.txtParagraphSettings": "<PERSON>ài đặt đoạn văn", "DE.Views.RightMenu.txtShapeSettings": "<PERSON>ài đặt hình dạng", "DE.Views.RightMenu.txtSignatureSettings": "Signature settings", "DE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON>i đặt bảng", "DE.Views.RightMenu.txtTextArtSettings": "Cài đặt chữ Nghệ thuật", "DE.Views.RoleDeleteDlg.textLabel": "To delete this role, you  need to move the fields associated with it to another role.", "DE.Views.RoleDeleteDlg.textSelect": "Select for field merger role", "DE.Views.RoleDeleteDlg.textTitle": "Delete role", "DE.Views.RoleEditDlg.errNameExists": "<PERSON>ai trò với tên đó đã xuất hiện.", "DE.Views.RoleEditDlg.textEmptyError": "Tên vai trò không đư<PERSON><PERSON> để trống", "DE.Views.RoleEditDlg.textName": "Role name", "DE.Views.RoleEditDlg.textNameEx": "Example: Applicant, Client, Sales Rep", "DE.Views.RoleEditDlg.textNoHighlight": "No highlighting", "DE.Views.RoleEditDlg.txtTitleEdit": "Edit Role", "DE.Views.RoleEditDlg.txtTitleNew": "Create new role", "DE.Views.RolesManagerDlg.textAnyone": "Anyone", "DE.Views.RolesManagerDlg.textDelete": "Delete", "DE.Views.RolesManagerDlg.textDeleteLast": "Are you sure you want to delete the role {0}?<br>Once deleted, the default role will be created.", "DE.Views.RolesManagerDlg.textDescription": "Add roles and set the order in which the fillers receive and sign the document", "DE.Views.RolesManagerDlg.textDown": "Move role down", "DE.Views.RolesManagerDlg.textEdit": "Edit", "DE.Views.RolesManagerDlg.textEmpty": "No roles have been created yet.<br>Create at least one role and it will appear in this field.", "DE.Views.RolesManagerDlg.textNew": "New", "DE.Views.RolesManagerDlg.textUp": "Move role up", "DE.Views.RolesManagerDlg.txtTitle": "Manage roles", "DE.Views.RolesManagerDlg.warnCantDelete": "You cannot delete this role because it has associated fields.", "DE.Views.RolesManagerDlg.warnDelete": "Are you sure you want to delete the role {0}?", "DE.Views.SaveFormDlg.saveButtonText": "<PERSON><PERSON><PERSON>", "DE.Views.SaveFormDlg.textAnyone": "Anyone", "DE.Views.SaveFormDlg.textDescription": "When saving to the PDF, only roles with fields are added to the filling list", "DE.Views.SaveFormDlg.textEmpty": "There are no roles associated with fields.", "DE.Views.SaveFormDlg.textFill": "Filling list", "DE.Views.SaveFormDlg.txtTitle": "<PERSON><PERSON><PERSON> như biểu mẫu", "DE.Views.ShapeSettings.strBackground": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strChange": "Thay đ<PERSON>i Autoshape", "DE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strFill": "<PERSON><PERSON> màu", "DE.Views.ShapeSettings.strForeground": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strPattern": "<PERSON><PERSON> văn", "DE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON> bóng đổ", "DE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON> mờ", "DE.Views.ShapeSettings.strType": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "DE.Views.ShapeSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "DE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textBorderSizeErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị từ thuộc từ 0 pt đến 1584 pt.", "DE.Views.ShapeSettings.textColor": "<PERSON><PERSON> màu", "DE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textEditPoints": "Edit points", "DE.Views.ShapeSettings.textEditShape": "Edit shape", "DE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>ng hoa văn", "DE.Views.ShapeSettings.textEyedropper": "Eyedropper", "DE.Views.ShapeSettings.textFlip": "Flip", "DE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON> tệp", "DE.Views.ShapeSettings.textFromStorage": "From storage", "DE.Views.ShapeSettings.textFromUrl": "Từ URL", "DE.Views.ShapeSettings.textGradient": "Gradient", "DE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON> màu <PERSON>", "DE.Views.ShapeSettings.textHint270": "Quay 90° ng<PERSON><PERSON><PERSON> chiều kim đồng hồ", "DE.Views.ShapeSettings.textHint90": "Quay 90° chi<PERSON>u kim đồng hồ", "DE.Views.ShapeSettings.textHintFlipH": "Flip horizontally", "DE.Views.ShapeSettings.textHintFlipV": "Flip vertically", "DE.Views.ShapeSettings.textImageTexture": "H<PERSON>nh <PERSON>nh hoặc Texture", "DE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textMoreColors": "More colors", "DE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON>ông đổ màu", "DE.Views.ShapeSettings.textNoShadow": "No Shadow", "DE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON> văn", "DE.Views.ShapeSettings.textPosition": "Position", "DE.Views.ShapeSettings.textRadial": "Tỏa tròn", "DE.Views.ShapeSettings.textRecentlyUsed": "Recently used", "DE.Views.ShapeSettings.textRotate90": "Quay 90°", "DE.Views.ShapeSettings.textRotation": "Rotation", "DE.Views.ShapeSettings.textSelectImage": "Select picture", "DE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textShadow": "Shadow", "DE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON> d<PERSON>i", "DE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textTexture": "Từ Texture", "DE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textWrap": "<PERSON><PERSON><PERSON> ng<PERSON> dòng", "DE.Views.ShapeSettings.tipAddGradientPoint": "Add gradient point", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "Remove gradient point", "DE.Views.ShapeSettings.txtBehind": "Sau", "DE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>u", "DE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "DE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON> t<PERSON>i màu", "DE.Views.ShapeSettings.txtGrain": "Thớ gỗ", "DE.Views.ShapeSettings.txtGranite": "Đá granite", "DE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>m", "DE.Views.ShapeSettings.txtInFront": "Ở trước", "DE.Views.ShapeSettings.txtInline": "<PERSON><PERSON><PERSON> dòng", "DE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON> xen", "DE.Views.ShapeSettings.txtLeather": "Da", "DE.Views.ShapeSettings.txtNoBorders": "Không đường kẻ", "DE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "DE.Views.ShapeSettings.txtSquare": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtThrough": "<PERSON><PERSON><PERSON><PERSON> qua", "DE.Views.ShapeSettings.txtTight": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtTopAndBottom": "<PERSON><PERSON><PERSON><PERSON> cùng và dưới cùng", "DE.Views.ShapeSettings.txtWood": "Gỗ", "DE.Views.SignatureSettings.notcriticalErrorTitle": "Warning", "DE.Views.SignatureSettings.strDelete": "Remove Signature", "DE.Views.SignatureSettings.strDetails": "<PERSON> tiết chữ ký", "DE.Views.SignatureSettings.strInvalid": "Invalid signatures", "DE.Views.SignatureSettings.strRequested": "Requested signatures", "DE.Views.SignatureSettings.strSetup": "Signature setup", "DE.Views.SignatureSettings.strSign": "<PERSON><PERSON>", "DE.Views.SignatureSettings.strSignature": "<PERSON><PERSON> ký", "DE.Views.SignatureSettings.strSigner": "Signer", "DE.Views.SignatureSettings.strValid": "Valid signatures", "DE.Views.SignatureSettings.txtContinueEditing": "Edit anyway", "DE.Views.SignatureSettings.txtEditWarning": "Editing will remove signatures from the document.<br>Continue?", "DE.Views.SignatureSettings.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "DE.Views.SignatureSettings.txtRequestedSignatures": "This document needs to be signed.", "DE.Views.SignatureSettings.txtSigned": "Chữ ký hợp lệ đã được thêm vào tài liệu. Tài liệu được bảo vệ khỏi việc bị chỉnh sửa", "DE.Views.SignatureSettings.txtSignedInvalid": "Some of the digital signatures in the document are invalid or could not be verified. The document is protected from editing.", "DE.Views.Statusbar.goToPageText": "<PERSON><PERSON><PERSON>", "DE.Views.Statusbar.pageIndexText": "Trang {0} trên {1}", "DE.Views.Statusbar.tipFitPage": "Vừa với trang", "DE.Views.Statusbar.tipFitWidth": "Vừa với <PERSON> rộng", "DE.Views.Statusbar.tipHandTool": "Hand tool", "DE.Views.Statusbar.tipSelectTool": "Select tool", "DE.Views.Statusbar.tipSetLang": "Đặt ngôn ngữ văn bản", "DE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON> ph<PERSON>g", "DE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON><PERSON> to", "DE.Views.Statusbar.tipZoomOut": "<PERSON>hu nhỏ", "DE.Views.Statusbar.txtPageNumInvalid": "Số trang không hợp lệ", "DE.Views.Statusbar.txtPages": "Pages", "DE.Views.Statusbar.txtParagraphs": "Paragraphs", "DE.Views.Statusbar.txtSpaces": "Symbols with spaces", "DE.Views.Statusbar.txtSymbols": "Symbols", "DE.Views.Statusbar.txtWordCount": "Word count", "DE.Views.Statusbar.txtWords": "Words", "DE.Views.StyleTitleDialog.textHeader": "<PERSON><PERSON><PERSON> ki<PERSON>u mới", "DE.Views.StyleTitleDialog.textNextStyle": "<PERSON><PERSON><PERSON> đo<PERSON>n tiếp theo", "DE.Views.StyleTitleDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "DE.Views.StyleTitleDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "DE.Views.StyleTitleDialog.txtNotEmpty": "Trư<PERSON><PERSON> không đư<PERSON><PERSON> để trống", "DE.Views.StyleTitleDialog.txtSameAs": "Same as created new style", "DE.Views.TableFormulaDialog.textBookmark": "Paste bookmark", "DE.Views.TableFormulaDialog.textFormat": "Number format", "DE.Views.TableFormulaDialog.textFormula": "Formula", "DE.Views.TableFormulaDialog.textInsertFunction": "Paste function", "DE.Views.TableFormulaDialog.textTitle": "Formula settings", "DE.Views.TableOfContentsSettings.strAlign": "Right align page numbers", "DE.Views.TableOfContentsSettings.strFullCaption": "Include label and number", "DE.Views.TableOfContentsSettings.strLinks": "Format table of contents as links", "DE.Views.TableOfContentsSettings.strLinksOF": "<PERSON><PERSON><PERSON> dạng mục lục hình dưới dạng liên kết", "DE.Views.TableOfContentsSettings.strShowPages": "<PERSON>ện số trang", "DE.Views.TableOfContentsSettings.textBuildTable": "Build table of contents from", "DE.Views.TableOfContentsSettings.textBuildTableOF": "<PERSON><PERSON><PERSON> mục lục hình từ", "DE.Views.TableOfContentsSettings.textEquation": "Equation", "DE.Views.TableOfContentsSettings.textFigure": "Figure", "DE.Views.TableOfContentsSettings.textLeader": "Leader", "DE.Views.TableOfContentsSettings.textLevel": "Level", "DE.Views.TableOfContentsSettings.textLevels": "Levels", "DE.Views.TableOfContentsSettings.textNone": "None", "DE.Views.TableOfContentsSettings.textRadioCaption": "Caption", "DE.Views.TableOfContentsSettings.textRadioLevels": "Outline levels", "DE.Views.TableOfContentsSettings.textRadioStyle": "Style", "DE.Views.TableOfContentsSettings.textRadioStyles": "Selected styles", "DE.Views.TableOfContentsSettings.textStyle": "Style", "DE.Views.TableOfContentsSettings.textStyles": "Styles", "DE.Views.TableOfContentsSettings.textTable": "Table", "DE.Views.TableOfContentsSettings.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textTitleTOF": "<PERSON><PERSON><PERSON> h<PERSON>", "DE.Views.TableOfContentsSettings.txtCentered": "Centered", "DE.Views.TableOfContentsSettings.txtClassic": "Classic", "DE.Views.TableOfContentsSettings.txtCurrent": "Current", "DE.Views.TableOfContentsSettings.txtDistinctive": "Distinctive", "DE.Views.TableOfContentsSettings.txtFormal": "Formal", "DE.Views.TableOfContentsSettings.txtModern": "Modern", "DE.Views.TableOfContentsSettings.txtOnline": "Online", "DE.Views.TableOfContentsSettings.txtSimple": "Simple", "DE.Views.TableOfContentsSettings.txtStandard": "Standard", "DE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON> c<PERSON>", "DE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> h<PERSON>", "DE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.insertColumnLeftText": "<PERSON><PERSON><PERSON> c<PERSON>t bên trái", "DE.Views.TableSettings.insertColumnRightText": "<PERSON><PERSON><PERSON> c<PERSON>t bên ph<PERSON>i", "DE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON> hàng bên trên", "DE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON><PERSON> hàng bên <PERSON>", "DE.Views.TableSettings.mergeCellsText": "<PERSON><PERSON><PERSON> nhi<PERSON>u ô và không canh giữa", "DE.Views.TableSettings.selectCellText": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON> b<PERSON>", "DE.Views.TableSettings.splitCellsText": "<PERSON><PERSON><PERSON> ô...", "DE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.strRepeatRow": "Lặp lại như hàng header ở đầu mỗi trang", "DE.Views.TableSettings.textAddFormula": "Add formula", "DE.Views.TableSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "DE.Views.TableSettings.textBackColor": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON> d<PERSON>i màu", "DE.Views.TableSettings.textBorderColor": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textBorders": "<PERSON><PERSON><PERSON> viền", "DE.Views.TableSettings.textCellSize": "Rows & columns size", "DE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textConvert": "Convert table to text", "DE.Views.TableSettings.textDistributeCols": "Distribute columns", "DE.Views.TableSettings.textDistributeRows": "Distribute rows", "DE.Views.TableSettings.textEdit": "Hàng & Cột", "DE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON>ng có mẫu", "DE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON> tiên", "DE.Views.TableSettings.textHeader": "Header", "DE.Views.TableSettings.textHeight": "Height", "DE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "DE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textSelectBorders": "Chọn đường viền bạn muốn thay đổi áp dụng kiểu đã chọn ở trên", "DE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON> từ Template", "DE.Views.TableSettings.textTotal": "<PERSON><PERSON><PERSON> cộng", "DE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.tipAll": "Đặt viền ngoài và tất cả đường kẻ bên trong", "DE.Views.TableSettings.tipBottom": "Chỉ đặt viền ngoài dưới cùng", "DE.Views.TableSettings.tipInner": "Chỉ đặt các đường kẻ bên trong", "DE.Views.TableSettings.tipInnerHor": "Chỉ đặt các đường ngang bên trong", "DE.Views.TableSettings.tipInnerVert": "Chỉ đặt đường kẻ dọc bên trong", "DE.Views.TableSettings.tipLeft": "Chỉ đặt viền ngoài bên trái", "DE.Views.TableSettings.tipNone": "K<PERSON>ông đặt viền", "DE.Views.TableSettings.tipOuter": "Chỉ đặt viền ngoài", "DE.Views.TableSettings.tipRight": "Chỉ đặt viền ngoài bên phải", "DE.Views.TableSettings.tipTop": "Chỉ đặt viền ngoài trên cùng", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "Bordered & Lined tables", "DE.Views.TableSettings.txtGroupTable_Custom": "Custom", "DE.Views.TableSettings.txtGroupTable_Grid": "Grid tables", "DE.Views.TableSettings.txtGroupTable_List": "List tables", "DE.Views.TableSettings.txtGroupTable_Plain": "Plain tables", "DE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "DE.Views.TableSettings.txtTable_Accent": "Accent", "DE.Views.TableSettings.txtTable_Bordered": "Bordered", "DE.Views.TableSettings.txtTable_BorderedAndLined": "Bordered & Lined", "DE.Views.TableSettings.txtTable_Colorful": "Colorful", "DE.Views.TableSettings.txtTable_Dark": "Dark", "DE.Views.TableSettings.txtTable_GridTable": "Grid table", "DE.Views.TableSettings.txtTable_Light": "Light", "DE.Views.TableSettings.txtTable_Lined": "Lined", "DE.Views.TableSettings.txtTable_ListTable": "List table", "DE.Views.TableSettings.txtTable_PlainTable": "Plain table", "DE.Views.TableSettings.txtTable_TableGrid": "Table grid", "DE.Views.TableSettingsAdvanced.textAlign": "<PERSON><PERSON>n chỉnh", "DE.Views.TableSettingsAdvanced.textAlignment": "<PERSON><PERSON>n chỉnh", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch gi<PERSON><PERSON> c<PERSON>c <PERSON>", "DE.Views.TableSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "DE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "DE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "DE.Views.TableSettingsAdvanced.textAnchorText": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAutofit": "Tự động điều chỉnh kích cỡ để phù hợp với nội dung", "DE.Views.TableSettingsAdvanced.textBackColor": "<PERSON><PERSON><PERSON> c<PERSON>", "DE.Views.TableSettingsAdvanced.textBelow": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textBorderColor": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textBorderDesc": "Nhấp vào biểu đồ hoặc sử dụng các nút để chọn đường viền và áp dụng kiểu đã chọn cho chúng", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "Đường viền & Nền", "DE.Views.TableSettingsAdvanced.textBorderWidth": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h đư<PERSON> vền", "DE.Views.TableSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "DE.Views.TableSettingsAdvanced.textCellOptions": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCellProps": "Ô", "DE.Views.TableSettingsAdvanced.textCellSize": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCenter": "Trung tâm", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "Trung tâm", "DE.Views.TableSettingsAdvanced.textCheckMargins": "Sử dụng lề mặc định", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "Mặc định lề của ô", "DE.Views.TableSettingsAdvanced.textDistance": "<PERSON><PERSON><PERSON><PERSON> cách từ văn bản", "DE.Views.TableSettingsAdvanced.textHorizontal": "Nằm ngang", "DE.Views.TableSettingsAdvanced.textIndLeft": "<PERSON><PERSON><PERSON><PERSON> từ bên trái", "DE.Views.TableSettingsAdvanced.textLeft": "Trái", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "Trái", "DE.Views.TableSettingsAdvanced.textMargin": "Lề", "DE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON> của ô", "DE.Views.TableSettingsAdvanced.textMeasure": "<PERSON><PERSON> trong", "DE.Views.TableSettingsAdvanced.textMove": "<PERSON> chuyển đối tượng cùng văn bản", "DE.Views.TableSettingsAdvanced.textOnlyCells": "Chỉ cho các ô đã chọn", "DE.Views.TableSettingsAdvanced.textOptions": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textOverlap": "<PERSON> phép chồng chéo", "DE.Views.TableSettingsAdvanced.textPage": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textPosition": "<PERSON><PERSON> trí", "DE.Views.TableSettingsAdvanced.textPrefWidth": "<PERSON><PERSON><PERSON> rộng ưu tiên", "DE.Views.TableSettingsAdvanced.textPreview": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textRelative": "c<PERSON> xứng với", "DE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textRightOf": "bên p<PERSON><PERSON><PERSON> c<PERSON>a", "DE.Views.TableSettingsAdvanced.textRightTooltip": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTableBackColor": "<PERSON><PERSON><PERSON> b<PERSON>", "DE.Views.TableSettingsAdvanced.textTablePosition": "<PERSON><PERSON> trí bảng", "DE.Views.TableSettingsAdvanced.textTableSize": "<PERSON><PERSON><PERSON> b<PERSON>", "DE.Views.TableSettingsAdvanced.textTitle": "Bảng - Cài đặt Nâng cao", "DE.Views.TableSettingsAdvanced.textTop": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "Chiều rộng & Khoảng cách", "DE.Views.TableSettingsAdvanced.textWrap": "<PERSON><PERSON><PERSON> dòng", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "<PERSON><PERSON><PERSON> cùng dòng", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "<PERSON>ủ chữ quanh bảng", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "<PERSON><PERSON><PERSON> ng<PERSON> dòng", "DE.Views.TableSettingsAdvanced.textWrapText": "<PERSON><PERSON><PERSON> dòng", "DE.Views.TableSettingsAdvanced.tipAll": "Đặt viền ngoài và tất cả đường kẻ bên trong", "DE.Views.TableSettingsAdvanced.tipCellAll": "Chỉ đặt viền bên trong ô", "DE.Views.TableSettingsAdvanced.tipCellInner": "Chỉ đặt đường kẻ thẳng và ngang cho các ô bên trong", "DE.Views.TableSettingsAdvanced.tipCellOuter": "Chỉ đặt viền ngoài cho tất các ô bên trong", "DE.Views.TableSettingsAdvanced.tipInner": "Chỉ đặt các đường kẻ bên trong", "DE.Views.TableSettingsAdvanced.tipNone": "K<PERSON>ông đặt viền", "DE.Views.TableSettingsAdvanced.tipOuter": "Chỉ đặt viền ngoài", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "Đặt viền ngoài và viền cho toàn bộ ô bên trong", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "Đặt viền ngoài và đường kẻ dọc và ngang cho các ô bên trong", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "Đặt viền ngoài cho bảng và viền ngoài cho các ô bên trong", "DE.Views.TableSettingsAdvanced.txtCm": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.txtInch": "Inch", "DE.Views.TableSettingsAdvanced.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "DE.Views.TableSettingsAdvanced.txtPercent": "<PERSON><PERSON><PERSON> tr<PERSON>m", "DE.Views.TableSettingsAdvanced.txtPt": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableToTextDialog.textEmpty": "You must type a character for the custom separator.", "DE.Views.TableToTextDialog.textNested": "Convert nested tables", "DE.Views.TableToTextDialog.textOther": "Other", "DE.Views.TableToTextDialog.textPara": "Paragraph marks", "DE.Views.TableToTextDialog.textSemicolon": "Semicolons", "DE.Views.TableToTextDialog.textSeparator": "Separate text with", "DE.Views.TableToTextDialog.textTab": "Tabs", "DE.Views.TableToTextDialog.textTitle": "Convert table to text", "DE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strFill": "<PERSON><PERSON> màu", "DE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON> mờ", "DE.Views.TextArtSettings.strType": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textBorderSizeErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị từ thuộc từ 0 pt đến 1584 pt.", "DE.Views.TextArtSettings.textColor": "<PERSON><PERSON> màu", "DE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textGradient": "Gradient", "DE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON> màu <PERSON>", "DE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON>ông đổ màu", "DE.Views.TextArtSettings.textPosition": "Position", "DE.Views.TextArtSettings.textRadial": "Tỏa tròn", "DE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textTemplate": "Template", "DE.Views.TextArtSettings.textTransform": "<PERSON><PERSON><PERSON><PERSON> đổi", "DE.Views.TextArtSettings.tipAddGradientPoint": "Add gradient point", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "Remove gradient point", "DE.Views.TextArtSettings.txtNoBorders": "Không đường kẻ", "DE.Views.TextToTableDialog.textAutofit": "Autofit behavior", "DE.Views.TextToTableDialog.textColumns": "Columns", "DE.Views.TextToTableDialog.textContents": "Autofit to contents", "DE.Views.TextToTableDialog.textEmpty": "You must type a character for the custom separator.", "DE.Views.TextToTableDialog.textFixed": "Fixed column width", "DE.Views.TextToTableDialog.textOther": "Other", "DE.Views.TextToTableDialog.textPara": "Paragraphs", "DE.Views.TextToTableDialog.textRows": "Rows", "DE.Views.TextToTableDialog.textSemicolon": "Semicolons", "DE.Views.TextToTableDialog.textSeparator": "Separate text at", "DE.Views.TextToTableDialog.textTab": "Tabs", "DE.Views.TextToTableDialog.textTableSize": "Table size", "DE.Views.TextToTableDialog.textTitle": "Convert text to table", "DE.Views.TextToTableDialog.textWindow": "Autofit to window", "DE.Views.TextToTableDialog.txtAutoText": "Auto", "DE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n xét", "DE.Views.Toolbar.capBtnBlankPage": "<PERSON>rang <PERSON>rống", "DE.Views.Toolbar.capBtnColumns": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON> lu<PERSON>", "DE.Views.Toolbar.capBtnDateTime": "Ngày & Giờ", "DE.Views.Toolbar.capBtnHand": "Hand", "DE.Views.Toolbar.capBtnHyphenation": "Hyphenation", "DE.Views.Toolbar.capBtnInsChart": "<PERSON><PERSON><PERSON><PERSON> đồ", "DE.Views.Toolbar.capBtnInsControls": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n <PERSON> dung", "DE.Views.Toolbar.capBtnInsDropcap": "Drop Cap", "DE.Views.Toolbar.capBtnInsEquation": "<PERSON><PERSON><PERSON><PERSON> trình", "DE.Views.Toolbar.capBtnInsField": "Field", "DE.Views.Toolbar.capBtnInsHeader": "Header/Footer", "DE.Views.Toolbar.capBtnInsImage": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsPagebreak": "<PERSON><PERSON><PERSON> trang", "DE.Views.Toolbar.capBtnInsShape": "<PERSON><PERSON><PERSON> d<PERSON>", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "Symbol", "DE.Views.Toolbar.capBtnInsTable": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsTextart": "<PERSON><PERSON> nghệ thuật", "DE.Views.Toolbar.capBtnInsTextbox": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsTextFromFile": "Text from File", "DE.Views.Toolbar.capBtnLineNumbers": "Line Numbers", "DE.Views.Toolbar.capBtnMargins": "Lề", "DE.Views.Toolbar.capBtnPageColor": "Page Color", "DE.Views.Toolbar.capBtnPageOrient": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnSelect": "Select", "DE.Views.Toolbar.capBtnWatermark": "Watermark", "DE.Views.Toolbar.capColorScheme": "Colors", "DE.Views.Toolbar.capImgAlign": "<PERSON><PERSON>n chỉnh", "DE.Views.Toolbar.capImgBackward": "Gửi về phía sau", "DE.Views.Toolbar.capImgForward": "<PERSON><PERSON><PERSON> lên tr<PERSON>", "DE.Views.Toolbar.capImgGroup": "Nhóm", "DE.Views.Toolbar.capImgWrapping": "Phủ chữ", "DE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "DE.Views.Toolbar.mniCapitalizeWords": "Capitalize Each Word", "DE.Views.Toolbar.mniCustomTable": "<PERSON><PERSON><PERSON> bảng tùy chỉnh", "DE.Views.Toolbar.mniDrawTable": "Vẽ bảng", "DE.Views.Toolbar.mniEditControls": "<PERSON>ài đặt kiểm soát", "DE.Views.Toolbar.mniEditDropCap": "Cài đặt Drop Cap", "DE.Views.Toolbar.mniEditFooter": "Chỉnh s<PERSON><PERSON>", "DE.Views.Toolbar.mniEditHeader": "Chỉnh s<PERSON><PERSON>er", "DE.Views.Toolbar.mniEraseTable": "Erase table", "DE.Views.Toolbar.mniFromFile": "<PERSON><PERSON> tệp", "DE.Views.Toolbar.mniFromStorage": "From Storage", "DE.Views.Toolbar.mniFromUrl": "From URL", "DE.Views.Toolbar.mniHiddenBorders": "<PERSON><PERSON><PERSON><PERSON> viền bảng đ<PERSON><PERSON><PERSON>n", "DE.Views.Toolbar.mniHiddenChars": "<PERSON><PERSON> tự không in", "DE.Views.Toolbar.mniHighlightControls": "Highlight settings", "DE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON> từ tệp", "DE.Views.Toolbar.mniImageFromStorage": "Image from storage", "DE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON><PERSON> từ URL", "DE.Views.Toolbar.mniInsertSSE": "Insert Spreadsheet", "DE.Views.Toolbar.mniLowerCase": "lowercase", "DE.Views.Toolbar.mniRemoveFooter": "Remove footer", "DE.Views.Toolbar.mniRemoveHeader": "Remove header", "DE.Views.Toolbar.mniSentenceCase": "Sentence case.", "DE.Views.Toolbar.mniTextFromLocalFile": "Text from the local file", "DE.Views.Toolbar.mniTextFromStorage": "Text from the storage file", "DE.Views.Toolbar.mniTextFromURL": "Text from the URL file", "DE.Views.Toolbar.mniTextToTable": "Convert Text to Table", "DE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "DE.Views.Toolbar.mniUpperCase": "UPPERCASE", "DE.Views.Toolbar.strMenuNoFill": "<PERSON><PERSON>ông đổ màu", "DE.Views.Toolbar.textAddSpaceAfter": "Add space after paragraph", "DE.Views.Toolbar.textAddSpaceBefore": "Add space before paragraph", "DE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "DE.Views.Toolbar.textAuto": "Automatic", "DE.Views.Toolbar.textAutoColor": "<PERSON><PERSON> động", "DE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "DE.Views.Toolbar.textBlackHeart": "Black heart suit", "DE.Views.Toolbar.textBold": "Đậm", "DE.Views.Toolbar.textBottom": "<PERSON><PERSON><PERSON><PERSON> cùng: ", "DE.Views.Toolbar.textBullet": "Bullet", "DE.Views.Toolbar.textChangeLevel": "Change list level", "DE.Views.Toolbar.textCheckboxControl": "Check box", "DE.Views.Toolbar.textColumnsCustom": "<PERSON><PERSON><PERSON> chỉnh cột", "DE.Views.Toolbar.textColumnsLeft": "Trái", "DE.Views.Toolbar.textColumnsOne": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textColumnsRight": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textColumnsThree": "Ba", "DE.Views.Toolbar.textColumnsTwo": "<PERSON>", "DE.Views.Toolbar.textComboboxControl": "Combo Box", "DE.Views.Toolbar.textContinuous": "Continuous", "DE.Views.Toolbar.textContPage": "<PERSON><PERSON> liên t<PERSON>c", "DE.Views.Toolbar.textCopyright": "Copyright Sign", "DE.Views.Toolbar.textCustomHyphen": "Hyphenation options", "DE.Views.Toolbar.textCustomLineNumbers": "Line numbering options", "DE.Views.Toolbar.textDateControl": "Date picker", "DE.Views.Toolbar.textDegree": "Degree Sign", "DE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "DE.Views.Toolbar.textDirLtr": "Left-to-right", "DE.Views.Toolbar.textDirRtl": "Right-to-left", "DE.Views.Toolbar.textDivision": "Division Sign", "DE.Views.Toolbar.textDollar": "Dollar Sign", "DE.Views.Toolbar.textDropdownControl": "Drop-down list", "DE.Views.Toolbar.textEditMode": "Edit PDF", "DE.Views.Toolbar.textEditWatermark": "Custom watermark", "DE.Views.Toolbar.textEuro": "Euro Sign", "DE.Views.Toolbar.textEvenPage": "Trang chẵn", "DE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "DE.Views.Toolbar.textIndAfter": "Indent after", "DE.Views.Toolbar.textIndBefore": "Indent before", "DE.Views.Toolbar.textIndLeft": "Left indent", "DE.Views.Toolbar.textIndRight": "Right indent", "DE.Views.Toolbar.textInfinity": "Infinity", "DE.Views.Toolbar.textInMargin": "<PERSON>rong lề", "DE.Views.Toolbar.textInsColumnBreak": "<PERSON><PERSON><PERSON> c<PERSON>t", "DE.Views.Toolbar.textInsertPageCount": "<PERSON><PERSON><PERSON> s<PERSON> trang", "DE.Views.Toolbar.textInsertPageNumber": "<PERSON><PERSON><PERSON> s<PERSON> trang", "DE.Views.Toolbar.textInsPageBreak": "<PERSON><PERSON><PERSON> trang", "DE.Views.Toolbar.textInsSectionBreak": "<PERSON><PERSON><PERSON>n", "DE.Views.Toolbar.textInText": "<PERSON>rong lề", "DE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textLandscape": "Nằm ngang", "DE.Views.Toolbar.textLeft": "Trái: ", "DE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "DE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "DE.Views.Toolbar.textLineSpaceOptions": "Line spacing options", "DE.Views.Toolbar.textListSettings": "List settings", "DE.Views.Toolbar.textMarginsLast": "Tuỳ chỉnh cuối cùng", "DE.Views.Toolbar.textMarginsModerate": "Vừa phải", "DE.Views.Toolbar.textMarginsNarrow": "<PERSON>hu hẹp", "DE.Views.Toolbar.textMarginsNormal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textMarginsWide": "Rộng", "DE.Views.Toolbar.textMoreSymbols": "More symbols", "DE.Views.Toolbar.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "DE.Views.Toolbar.textNextPage": "<PERSON><PERSON> ti<PERSON><PERSON> theo", "DE.Views.Toolbar.textNoHighlight": "No highlighting", "DE.Views.Toolbar.textNone": "K<PERSON>ô<PERSON>", "DE.Views.Toolbar.textNotEqualTo": "Not Equal To", "DE.Views.Toolbar.textOddPage": "Trang lẻ", "DE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "DE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "DE.Views.Toolbar.textPageMarginsCustom": "<PERSON><PERSON><PERSON> chỉnh lề", "DE.Views.Toolbar.textPageSizeCustom": "T<PERSON><PERSON> chỉnh kích thước trang", "DE.Views.Toolbar.textPictureControl": "Picture", "DE.Views.Toolbar.textPlainControl": "Plain text", "DE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "DE.Views.Toolbar.textPortrait": "Thẳng đứng", "DE.Views.Toolbar.textRegistered": "Registered Sign", "DE.Views.Toolbar.textRemoveControl": "Bỏ điều khiển nội dung", "DE.Views.Toolbar.textRemSpaceAfter": "Remove space after paragraph", "DE.Views.Toolbar.textRemSpaceBefore": "Remove space before paragraph", "DE.Views.Toolbar.textRemWatermark": "Remove watermark", "DE.Views.Toolbar.textRestartEachPage": "Restart each page", "DE.Views.Toolbar.textRestartEachSection": "Restart each section", "DE.Views.Toolbar.textRichControl": "Rich text", "DE.Views.Toolbar.textRight": "<PERSON><PERSON><PERSON>: ", "DE.Views.Toolbar.textSection": "Section Sign", "DE.Views.Toolbar.textShapesCombine": "Combine", "DE.Views.Toolbar.textShapesFragment": "Fragment", "DE.Views.Toolbar.textShapesIntersect": "Intersect", "DE.Views.Toolbar.textShapesSubstract": "Subtract", "DE.Views.Toolbar.textShapesUnion": "Union", "DE.Views.Toolbar.textSmile": "White Smiling Face", "DE.Views.Toolbar.textSpaceAfter": "Space after", "DE.Views.Toolbar.textSpaceBefore": "Space before", "DE.Views.Toolbar.textSquareRoot": "Square Root", "DE.Views.Toolbar.textStrikeout": "Gạch bỏ", "DE.Views.Toolbar.textStyleMenuDelete": "<PERSON><PERSON><PERSON> k<PERSON>", "DE.Views.Toolbar.textStyleMenuDeleteAll": "<PERSON><PERSON><PERSON> tất cả kiểu tùy chỉnh", "DE.Views.Toolbar.textStyleMenuNew": "<PERSON><PERSON><PERSON> mới từ danh sách chọn", "DE.Views.Toolbar.textStyleMenuRestore": "<PERSON><PERSON><PERSON><PERSON> phụ<PERSON> về mặc định", "DE.Views.Toolbar.textStyleMenuRestoreAll": "<PERSON><PERSON><PERSON><PERSON> phục tất cả các kiểu mặc định", "DE.Views.Toolbar.textStyleMenuUpdate": "<PERSON><PERSON><PERSON> nh<PERSON>t từ lựa chọn", "DE.Views.Toolbar.textSubscript": "Chỉ số dưới", "DE.Views.Toolbar.textSuperscript": "Chỉ số trên", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "Suppress for current paragraph", "DE.Views.Toolbar.textTabCollaboration": "<PERSON><PERSON><PERSON> t<PERSON>c", "DE.Views.Toolbar.textTabDraw": "Vẽ", "DE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabHome": "Trang chủ", "DE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabLayout": "Bố cục", "DE.Views.Toolbar.textTabLinks": "<PERSON><PERSON>", "DE.Views.Toolbar.textTabProtect": "Bả<PERSON> vệ", "DE.Views.Toolbar.textTabReview": "<PERSON><PERSON>", "DE.Views.Toolbar.textTabView": "View", "DE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "DE.Views.Toolbar.textTitleError": "Lỗi", "DE.Views.Toolbar.textToCurrent": "<PERSON><PERSON>n vị trí hiện tại", "DE.Views.Toolbar.textTop": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>: ", "DE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "DE.Views.Toolbar.textUnderline": "G<PERSON><PERSON> chân", "DE.Views.Toolbar.textYen": "Yen Sign", "DE.Views.Toolbar.tipAlignCenter": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON> đ<PERSON>u", "DE.Views.Toolbar.tipAlignLeft": "<PERSON><PERSON><PERSON> t<PERSON>", "DE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipBack": "Quay lại", "DE.Views.Toolbar.tipBlankPage": "Insert blank page", "DE.Views.Toolbar.tipChangeCase": "Change case", "DE.Views.Toolbar.tipChangeChart": "Thay đổi Loại biểu đồ", "DE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipColorSchemas": "<PERSON>hay đ<PERSON>i <PERSON> màu", "DE.Views.Toolbar.tipColumns": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipControls": "<PERSON><PERSON><PERSON> điều khiển nội dung", "DE.Views.Toolbar.tipCopy": "Sao chép", "DE.Views.Toolbar.tipCopyStyle": "<PERSON>o ch<PERSON>p kiểu", "DE.Views.Toolbar.tipCut": "Cut", "DE.Views.Toolbar.tipDateTime": "Insert current date and time", "DE.Views.Toolbar.tipDecFont": "Giảm cỡ chữ", "DE.Views.Toolbar.tipDecPrLeft": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "DE.Views.Toolbar.tipDownload": "Download file", "DE.Views.Toolbar.tipDropCap": "<PERSON><PERSON>n Drop Cap", "DE.Views.Toolbar.tipEditHeader": "Chỉnh s<PERSON><PERSON> Header hoặc Footer", "DE.Views.Toolbar.tipEditMode": "Edit current file.<br>The page will be reloaded.", "DE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON> chữ", "DE.Views.Toolbar.tipFontName": "Phông chữ", "DE.Views.Toolbar.tipFontSize": "Cỡ chữ", "DE.Views.Toolbar.tipHandTool": "Hand tool", "DE.Views.Toolbar.tipHighlightColor": "<PERSON><PERSON><PERSON> tô sáng", "DE.Views.Toolbar.tipHyphenation": "Change hyphenation", "DE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON> chỉnh đối tượng", "DE.Views.Toolbar.tipImgGroup": "<PERSON><PERSON><PERSON><PERSON> đố<PERSON> t<PERSON>", "DE.Views.Toolbar.tipImgWrapping": "<PERSON><PERSON><PERSON> dòng", "DE.Views.Toolbar.tipIncFont": "<PERSON><PERSON><PERSON> kích thước phông chữ", "DE.Views.Toolbar.tipIncPrLeft": "<PERSON><PERSON><PERSON> th<PERSON>t l<PERSON>", "DE.Views.Toolbar.tipInsertChart": "<PERSON><PERSON><PERSON> bi<PERSON>u đồ", "DE.Views.Toolbar.tipInsertEquation": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> trình", "DE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "DE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipInsertNum": "<PERSON><PERSON><PERSON> s<PERSON> trang", "DE.Views.Toolbar.tipInsertShape": "Chèn Autoshape", "DE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "DE.Views.Toolbar.tipInsertSymbol": "Insert symbol", "DE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> b<PERSON>", "DE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "DE.Views.Toolbar.tipInsertTextArt": "<PERSON><PERSON><PERSON> chữ nghệ thuật", "DE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "DE.Views.Toolbar.tipInsField": "Insert field", "DE.Views.Toolbar.tipLineNumbers": "Show line numbers", "DE.Views.Toolbar.tipLineSpace": "<PERSON><PERSON><PERSON><PERSON> cách Dòng trong <PERSON>n văn bản", "DE.Views.Toolbar.tipMailRecepients": "<PERSON><PERSON><PERSON><PERSON> thư", "DE.Views.Toolbar.tipMarkers": "<PERSON><PERSON><PERSON> đầu dòng", "DE.Views.Toolbar.tipMarkersArrow": "Arrow bullets", "DE.Views.Toolbar.tipMarkersCheckmark": "Checkmark bullets", "DE.Views.Toolbar.tipMarkersDash": "Dash bullets", "DE.Views.Toolbar.tipMarkersFRhombus": "Filled rhombus bullets", "DE.Views.Toolbar.tipMarkersFRound": "Filled round bullets", "DE.Views.Toolbar.tipMarkersFSquare": "Filled square bullets", "DE.Views.Toolbar.tipMarkersHRound": "Hollow round bullets", "DE.Views.Toolbar.tipMarkersStar": "Star bullets", "DE.Views.Toolbar.tipMultiLevelArticl": "Multi-level numbered articles", "DE.Views.Toolbar.tipMultiLevelChapter": "Multi-level numbered chapters", "DE.Views.Toolbar.tipMultiLevelHeadings": "Multi-level numbered headings", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "Multi-level various numbered headings", "DE.Views.Toolbar.tipMultiLevelNumbered": "Multi-level numbered bullets", "DE.Views.Toolbar.tipMultilevels": "<PERSON><PERSON> c<PERSON>", "DE.Views.Toolbar.tipMultiLevelSymbols": "Multi-level symbols bullets", "DE.Views.Toolbar.tipMultiLevelVarious": "Multi-level various numbered bullets", "DE.Views.Toolbar.tipNumbers": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipPageBreak": "<PERSON><PERSON><PERSON> trang hoặc ngắt phần", "DE.Views.Toolbar.tipPageColor": "Change page color", "DE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON> trang", "DE.Views.Toolbar.tipPageOrient": "<PERSON><PERSON><PERSON><PERSON> trang", "DE.Views.Toolbar.tipPageSize": "<PERSON><PERSON><PERSON> trang", "DE.Views.Toolbar.tipParagraphStyle": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipPaste": "Dán", "DE.Views.Toolbar.tipPrColor": "<PERSON><PERSON><PERSON> n<PERSON>n đoạn văn bản", "DE.Views.Toolbar.tipPrint": "In", "DE.Views.Toolbar.tipPrintQuick": "Quick print", "DE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipReplace": "Replace", "DE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON><PERSON> thay đổi của bạn để những người dùng khác thấy chúng.", "DE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON> tất cả", "DE.Views.Toolbar.tipSelectTool": "Select tool", "DE.Views.Toolbar.tipSendBackward": "Gửi về phía sau", "DE.Views.Toolbar.tipSendForward": "<PERSON><PERSON><PERSON> lên tr<PERSON>", "DE.Views.Toolbar.tipShapesMerge": "Merge shapes", "DE.Views.Toolbar.tipShowHiddenChars": "<PERSON><PERSON> tự không in", "DE.Views.Toolbar.tipSynchronize": "Tài liệu đã được thay đổi bởi một người dùng khác. <PERSON><PERSON> lòng nhấp để lưu thay đổi của bạn và tải lại các cập nhật.", "DE.Views.Toolbar.tipTextDir": "Text direction", "DE.Views.Toolbar.tipTextFromFile": "Text from file", "DE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipWatermark": "Edit watermark", "DE.Views.Toolbar.txtAutoText": "Auto", "DE.Views.Toolbar.txtDistribHor": "Distribute horizontally", "DE.Views.Toolbar.txtDistribVert": "Distribute vertically", "DE.Views.Toolbar.txtGroupBulletDoc": "Document bullets", "DE.Views.Toolbar.txtGroupBulletLib": "Bullet library", "DE.Views.Toolbar.txtGroupMultiDoc": "Lists in current document", "DE.Views.Toolbar.txtGroupMultiLib": "List library", "DE.Views.Toolbar.txtGroupNumDoc": "Document numbering formats", "DE.Views.Toolbar.txtGroupNumLib": "Numbering library", "DE.Views.Toolbar.txtGroupRecent": "Recently used", "DE.Views.Toolbar.txtMarginAlign": "Align to margin", "DE.Views.Toolbar.txtObjectsAlign": "<PERSON><PERSON><PERSON> c<PERSON>c vật thể đư<PERSON><PERSON> chọn", "DE.Views.Toolbar.txtPageAlign": "Align to page", "DE.Views.ViewTab.textAlwaysShowToolbar": "Always Show Toolbar", "DE.Views.ViewTab.textDarkDocument": "Dark Document", "DE.Views.ViewTab.textFill": "Fill", "DE.Views.ViewTab.textFitToPage": "<PERSON><PERSON> To <PERSON>", "DE.Views.ViewTab.textFitToWidth": "Fit To <PERSON>th", "DE.Views.ViewTab.textInterfaceTheme": "Interface Theme", "DE.Views.ViewTab.textLeftMenu": "Left Panel", "DE.Views.ViewTab.textLine": "Line", "DE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "DE.Views.ViewTab.textNavigation": "Navigation", "DE.Views.ViewTab.textOutline": "Headings", "DE.Views.ViewTab.textRightMenu": "Right Panel", "DE.Views.ViewTab.textRulers": "Rulers", "DE.Views.ViewTab.textStatusBar": "Status Bar", "DE.Views.ViewTab.textTabStyle": "Tab style", "DE.Views.ViewTab.textZoom": "Zoom", "DE.Views.ViewTab.tipDarkDocument": "Dark document", "DE.Views.ViewTab.tipFitToPage": "Fit to page", "DE.Views.ViewTab.tipFitToWidth": "Fit to width", "DE.Views.ViewTab.tipHeadings": "Headings", "DE.Views.ViewTab.tipInterfaceTheme": "Interface theme", "DE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textAuto": "Auto", "DE.Views.WatermarkSettingsDialog.textBold": "Bold", "DE.Views.WatermarkSettingsDialog.textColor": "Text color", "DE.Views.WatermarkSettingsDialog.textDiagonal": "Diagonal", "DE.Views.WatermarkSettingsDialog.textFont": "Font", "DE.Views.WatermarkSettingsDialog.textFromFile": "<PERSON><PERSON> tệp", "DE.Views.WatermarkSettingsDialog.textFromStorage": "From storage", "DE.Views.WatermarkSettingsDialog.textFromUrl": "From URL", "DE.Views.WatermarkSettingsDialog.textHor": "Horizontal", "DE.Views.WatermarkSettingsDialog.textImageW": "Image watermark", "DE.Views.WatermarkSettingsDialog.textItalic": "Italic", "DE.Views.WatermarkSettingsDialog.textLanguage": "Language", "DE.Views.WatermarkSettingsDialog.textLayout": "Layout", "DE.Views.WatermarkSettingsDialog.textNone": "None", "DE.Views.WatermarkSettingsDialog.textScale": "Scale", "DE.Views.WatermarkSettingsDialog.textSelect": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textStrikeout": "Strikethrough", "DE.Views.WatermarkSettingsDialog.textText": "Text", "DE.Views.WatermarkSettingsDialog.textTextW": "Text watermark", "DE.Views.WatermarkSettingsDialog.textTitle": "Watermark settings", "DE.Views.WatermarkSettingsDialog.textTransparency": "Semitransparent", "DE.Views.WatermarkSettingsDialog.textUnderline": "Underline", "DE.Views.WatermarkSettingsDialog.tipFontName": "Font name", "DE.Views.WatermarkSettingsDialog.tipFontSize": "Font size", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}