{"Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Desktop.hintBtnHome": "顯示主視窗", "Common.Controllers.Desktop.itemCreateFromTemplate": "從模板創建", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "匿名", "Common.Controllers.ExternalDiagramEditor.textClose": "關閉", "Common.Controllers.ExternalDiagramEditor.warningText": "該物件因正在被其他使用者編輯而被禁用。", "Common.Controllers.ExternalDiagramEditor.warningTitle": "警告", "Common.Controllers.ExternalMergeEditor.textAnonymous": "匿名", "Common.Controllers.ExternalMergeEditor.textClose": "關閉", "Common.Controllers.ExternalMergeEditor.warningText": "該物件因正在被其他使用者編輯而被禁用。", "Common.Controllers.ExternalMergeEditor.warningTitle": "警告", "Common.Controllers.ExternalOleEditor.textAnonymous": "匿名", "Common.Controllers.ExternalOleEditor.textClose": "關閉", "Common.Controllers.ExternalOleEditor.warningText": "該物件因正在被其他使用者編輯而被禁用。", "Common.Controllers.ExternalOleEditor.warningTitle": "警告", "Common.Controllers.History.notcriticalErrorTitle": "警告", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "為了比較文件，其中的所有修訂將被視為已接受。您要繼續嗎？", "Common.Controllers.ReviewChanges.textAtLeast": "至少", "Common.Controllers.ReviewChanges.textAuto": "自動", "Common.Controllers.ReviewChanges.textBaseline": "基準線", "Common.Controllers.ReviewChanges.textBold": "粗體", "Common.Controllers.ReviewChanges.textBreakBefore": "分頁之前", "Common.Controllers.ReviewChanges.textCaps": "全部大寫", "Common.Controllers.ReviewChanges.textCenter": "居中對齊", "Common.Controllers.ReviewChanges.textChar": "字元層級", "Common.Controllers.ReviewChanges.textChart": "圖表", "Common.Controllers.ReviewChanges.textColor": "字型顏色", "Common.Controllers.ReviewChanges.textContextual": "不在相同樣式的段落間增加間距", "Common.Controllers.ReviewChanges.textDeleted": "<b>已刪除</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "雙刪除線", "Common.Controllers.ReviewChanges.textEquation": "方程式", "Common.Controllers.ReviewChanges.textExact": "確切地", "Common.Controllers.ReviewChanges.textFirstLine": "首行縮排", "Common.Controllers.ReviewChanges.textFontSize": "字型大小", "Common.Controllers.ReviewChanges.textFormatted": "已格式化", "Common.Controllers.ReviewChanges.textHighlight": "文字醒目提示色彩", "Common.Controllers.ReviewChanges.textImage": "圖片", "Common.Controllers.ReviewChanges.textIndentLeft": "向左縮排", "Common.Controllers.ReviewChanges.textIndentRight": "向右縮排", "Common.Controllers.ReviewChanges.textInserted": "<b>已插入：</b>", "Common.Controllers.ReviewChanges.textItalic": "斜體", "Common.Controllers.ReviewChanges.textJustify": "對齊", "Common.Controllers.ReviewChanges.textKeepLines": "保持線條一致", "Common.Controllers.ReviewChanges.textKeepNext": "與下一段連在一起", "Common.Controllers.ReviewChanges.textLeft": "對齊左側", "Common.Controllers.ReviewChanges.textLineSpacing": "行間距", "Common.Controllers.ReviewChanges.textMultiple": "多個", "Common.Controllers.ReviewChanges.textNoBreakBefore": "不在分頁之前換行", "Common.Controllers.ReviewChanges.textNoContextual": "在相同風格的段落之間加入間隔", "Common.Controllers.ReviewChanges.textNoKeepLines": "不保持連續行", "Common.Controllers.ReviewChanges.textNoKeepNext": "不與下一段保持在一起", "Common.Controllers.ReviewChanges.textNot": "非", "Common.Controllers.ReviewChanges.textNoWidow": "無行控制", "Common.Controllers.ReviewChanges.textNum": "變更編號", "Common.Controllers.ReviewChanges.textOff": "{0} 已沒有再使用追蹤。", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} 關閉了所有用戶的追蹤修訂。", "Common.Controllers.ReviewChanges.textOn": "{0} 已開始使用追蹤。", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} 開啟了所有帳戶的追蹤修訂。", "Common.Controllers.ReviewChanges.textParaDeleted": "<b>段落已刪除</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "已格式化的段落", "Common.Controllers.ReviewChanges.textParaInserted": "<b>段落已插入</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>已下移:</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>已上移:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>已移動:<b>", "Common.Controllers.ReviewChanges.textPosition": "位置", "Common.Controllers.ReviewChanges.textRight": "對齊右側", "Common.Controllers.ReviewChanges.textShape": "形狀", "Common.Controllers.ReviewChanges.textShd": "背景顏色", "Common.Controllers.ReviewChanges.textShow": "顯示變更於", "Common.Controllers.ReviewChanges.textSmallCaps": "小型大寫", "Common.Controllers.ReviewChanges.textSpacing": "間距", "Common.Controllers.ReviewChanges.textSpacingAfter": "段落後間距", "Common.Controllers.ReviewChanges.textSpacingBefore": "段落前間距", "Common.Controllers.ReviewChanges.textStrikeout": "刪除線", "Common.Controllers.ReviewChanges.textSubScript": "下標", "Common.Controllers.ReviewChanges.textSuperScript": "上標", "Common.Controllers.ReviewChanges.textTableChanged": "<b>表格設定已刪除</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b>已添加表格行</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b>已刪除表格行</b>", "Common.Controllers.ReviewChanges.textTabs": "變更定位點", "Common.Controllers.ReviewChanges.textTitleComparison": "比較設定", "Common.Controllers.ReviewChanges.textUnderline": "底線", "Common.Controllers.ReviewChanges.textUrl": "貼上文件URL", "Common.Controllers.ReviewChanges.textWidow": "孤行控制", "Common.Controllers.ReviewChanges.textWord": "詞級", "Common.define.chartData.textArea": "區域圖", "Common.define.chartData.textAreaStacked": "堆叠面積", "Common.define.chartData.textAreaStackedPer": "100% 堆疊面積圖", "Common.define.chartData.textBar": "長條圖", "Common.define.chartData.textBarNormal": "叢集柱狀圖", "Common.define.chartData.textBarNormal3d": "3-D 組合直式長條圖", "Common.define.chartData.textBarNormal3dPerspective": "3-D 直式長條圖", "Common.define.chartData.textBarStacked": "堆疊柱狀圖", "Common.define.chartData.textBarStacked3d": "3-D 堆疊直式長條圖", "Common.define.chartData.textBarStackedPer": "100% 堆疊直式長條圖", "Common.define.chartData.textBarStackedPer3d": "3-D 100% 堆疊直式長條圖", "Common.define.chartData.textCharts": "流程圖", "Common.define.chartData.textColumn": "欄", "Common.define.chartData.textCombo": "組合圖", "Common.define.chartData.textComboAreaBar": "堆疊面積-叢集柱狀圖", "Common.define.chartData.textComboBarLine": "叢集柱狀圖 - 折線圖", "Common.define.chartData.textComboBarLineSecondary": "叢集柱狀圖 - 折線圖（次要軸）", "Common.define.chartData.textComboCustom": "自訂組合", "Common.define.chartData.textDoughnut": "環狀圖", "Common.define.chartData.textHBarNormal": "叢集長條圖", "Common.define.chartData.textHBarNormal3d": "3-D 組合直式長條圖", "Common.define.chartData.textHBarStacked": "堆疊長條圖", "Common.define.chartData.textHBarStacked3d": "3-D 堆疊橫式長條圖", "Common.define.chartData.textHBarStackedPer": "100% 堆疊橫式長條圖", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% 堆疊橫式長條圖", "Common.define.chartData.textLine": "折線圖", "Common.define.chartData.textLine3d": "3-D 直線圖", "Common.define.chartData.textLineMarker": "帶標記的線條", "Common.define.chartData.textLineStacked": "堆疊折線圖", "Common.define.chartData.textLineStackedMarker": "帶標記的堆疊折線圖", "Common.define.chartData.textLineStackedPer": "100% 堆疊直線圖", "Common.define.chartData.textLineStackedPerMarker": "100% 堆疊直線圖加標記", "Common.define.chartData.textPie": "圓餅圖", "Common.define.chartData.textPie3d": "3-D 圓餅圖", "Common.define.chartData.textPoint": "XY散佈圖", "Common.define.chartData.textRadar": "雷達圖", "Common.define.chartData.textRadarFilled": "填充雷達圖", "Common.define.chartData.textRadarMarker": "含標記的雷達圖", "Common.define.chartData.textScatter": "散佈圖", "Common.define.chartData.textScatterLine": "直線散佈圖", "Common.define.chartData.textScatterLineMarker": "直線和標記的散佈圖", "Common.define.chartData.textScatterSmooth": "平滑線條散佈圖", "Common.define.chartData.textScatterSmoothMarker": "平滑線條和標記的散佈圖", "Common.define.chartData.textStock": "庫存", "Common.define.chartData.textSurface": "表面", "Common.define.smartArt.textAccentedPicture": "強調圖片", "Common.define.smartArt.textAccentProcess": "強調處理", "Common.define.smartArt.textAlternatingFlow": "交替流程", "Common.define.smartArt.textAlternatingHexagons": "交替六邊形", "Common.define.smartArt.textAlternatingPictureBlocks": "交替圖片區塊", "Common.define.smartArt.textAlternatingPictureCircles": "交替圖片圓形", "Common.define.smartArt.textArchitectureLayout": "架構佈局", "Common.define.smartArt.textArrowRibbon": "箭頭功能表", "Common.define.smartArt.textAscendingPictureAccentProcess": "遞增圖片裝飾處理", "Common.define.smartArt.textBalance": "平衡", "Common.define.smartArt.textBasicBendingProcess": "基本彎曲程序", "Common.define.smartArt.textBasicBlockList": "基本區塊清單", "Common.define.smartArt.textBasicChevronProcess": "基本箭頭程序", "Common.define.smartArt.textBasicCycle": "基本循環", "Common.define.smartArt.textBasicMatrix": "基本矩陣", "Common.define.smartArt.textBasicPie": "基本圓餅圖", "Common.define.smartArt.textBasicProcess": "基本程序", "Common.define.smartArt.textBasicPyramid": "基本金字塔", "Common.define.smartArt.textBasicRadial": "基本放射狀", "Common.define.smartArt.textBasicTarget": "基本目標", "Common.define.smartArt.textBasicTimeline": "基本時間軸", "Common.define.smartArt.textBasicVenn": "基本文氏圖", "Common.define.smartArt.textBendingPictureAccentList": "彎曲圖片重點清單", "Common.define.smartArt.textBendingPictureBlocks": "彎曲圖片區塊", "Common.define.smartArt.textBendingPictureCaption": "彎曲圖片標題", "Common.define.smartArt.textBendingPictureCaptionList": "彎曲圖片標題清單", "Common.define.smartArt.textBendingPictureSemiTranparentText": "彎曲圖片半透明文字", "Common.define.smartArt.textBlockCycle": "區塊循環", "Common.define.smartArt.textBubblePictureList": "泡泡圖片清單", "Common.define.smartArt.textCaptionedPictures": "帶標題的圖片", "Common.define.smartArt.textChevronAccentProcess": "箭頭強調程序", "Common.define.smartArt.textChevronList": "箭頭清單", "Common.define.smartArt.textCircleAccentTimeline": "圓形強調時間軸", "Common.define.smartArt.textCircleArrowProcess": "圓形箭頭程序", "Common.define.smartArt.textCirclePictureHierarchy": "圓形圖片階層", "Common.define.smartArt.textCircleProcess": "圓形程序", "Common.define.smartArt.textCircleRelationship": "圓形關聯", "Common.define.smartArt.textCircularBendingProcess": "圓形彎曲程序", "Common.define.smartArt.textCircularPictureCallout": "圓形圖片註解", "Common.define.smartArt.textClosedChevronProcess": "閉合式強調程序", "Common.define.smartArt.textContinuousArrowProcess": "連續箭頭流程", "Common.define.smartArt.textContinuousBlockProcess": "連續塊流程", "Common.define.smartArt.textContinuousCycle": "連續循環", "Common.define.smartArt.textContinuousPictureList": "連續圖片列表", "Common.define.smartArt.textConvergingArrows": "匯聚箭頭", "Common.define.smartArt.textConvergingRadial": "匯聚徑向", "Common.define.smartArt.textConvergingText": "匯聚文件", "Common.define.smartArt.textCounterbalanceArrows": "對稱平衡箭頭", "Common.define.smartArt.textCycle": "循環", "Common.define.smartArt.textCycleMatrix": "循環矩陣", "Common.define.smartArt.textDescendingBlockList": "遞減區塊清單", "Common.define.smartArt.textDescendingProcess": "遞減處理", "Common.define.smartArt.textDetailedProcess": "詳細處理", "Common.define.smartArt.textDivergingArrows": "分歧箭頭", "Common.define.smartArt.textDivergingRadial": "分歧徑向", "Common.define.smartArt.textEquation": "方程式", "Common.define.smartArt.textFramedTextPicture": "框線文字圖片", "Common.define.smartArt.textFunnel": "漏斗", "Common.define.smartArt.textGear": "齒輪", "Common.define.smartArt.textGridMatrix": "格狀矩陣", "Common.define.smartArt.textGroupedList": "分組清單", "Common.define.smartArt.textHalfCircleOrganizationChart": "半圓形組織圖", "Common.define.smartArt.textHexagonCluster": "六邊形集群", "Common.define.smartArt.textHexagonRadial": "六邊形放射狀", "Common.define.smartArt.textHierarchy": "層級結構", "Common.define.smartArt.textHierarchyList": "層級結構清單", "Common.define.smartArt.textHorizontalBulletList": "水平項目清單", "Common.define.smartArt.textHorizontalHierarchy": "水平層級結構", "Common.define.smartArt.textHorizontalLabeledHierarchy": "水平標籤層級結構", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "水平多層次層級結構", "Common.define.smartArt.textHorizontalOrganizationChart": "水平組織圖", "Common.define.smartArt.textHorizontalPictureList": "水平圖片清單", "Common.define.smartArt.textIncreasingArrowProcess": "遞增箭頭流程", "Common.define.smartArt.textIncreasingCircleProcess": "遞增圓圈流程", "Common.define.smartArt.textInterconnectedBlockProcess": "互連塊狀流程", "Common.define.smartArt.textInterconnectedRings": "互連環圖", "Common.define.smartArt.textInvertedPyramid": "倒置金字塔", "Common.define.smartArt.textLabeledHierarchy": "標記層次結構", "Common.define.smartArt.textLinearVenn": "線性文氏圖", "Common.define.smartArt.textLinedList": "有格線的清單", "Common.define.smartArt.textList": "清單", "Common.define.smartArt.textMatrix": "矩陣", "Common.define.smartArt.textMultidirectionalCycle": "多方向循環", "Common.define.smartArt.textNameAndTitleOrganizationChart": "名稱與職稱組織圖", "Common.define.smartArt.textNestedTarget": "巢狀目標", "Common.define.smartArt.textNondirectionalCycle": "非定向循環", "Common.define.smartArt.textOpposingArrows": "相對箭頭", "Common.define.smartArt.textOpposingIdeas": "相對觀點", "Common.define.smartArt.textOrganizationChart": "組織圖", "Common.define.smartArt.textOther": "其它", "Common.define.smartArt.textPhasedProcess": "分階段處理", "Common.define.smartArt.textPicture": "圖片", "Common.define.smartArt.textPictureAccentBlocks": "圖片強調區塊", "Common.define.smartArt.textPictureAccentList": "圖片強調清單", "Common.define.smartArt.textPictureAccentProcess": "圖片強調流程", "Common.define.smartArt.textPictureCaptionList": "圖片標題清單", "Common.define.smartArt.textPictureFrame": "圖片框架", "Common.define.smartArt.textPictureGrid": "圖片網格", "Common.define.smartArt.textPictureLineup": "圖片對齊", "Common.define.smartArt.textPictureOrganizationChart": "圖片組織圖", "Common.define.smartArt.textPictureStrips": "圖片條帶", "Common.define.smartArt.textPieProcess": "圓餅圖流程", "Common.define.smartArt.textPlusAndMinus": "加號和減號", "Common.define.smartArt.textProcess": "流程", "Common.define.smartArt.textProcessArrows": "流程箭頭", "Common.define.smartArt.textProcessList": "流程清單", "Common.define.smartArt.textPyramid": "金字塔", "Common.define.smartArt.textPyramidList": "金字塔清單", "Common.define.smartArt.textRadialCluster": "放射狀群集", "Common.define.smartArt.textRadialCycle": "放射狀循環", "Common.define.smartArt.textRadialList": "放射狀清單", "Common.define.smartArt.textRadialPictureList": "放射狀圖片清單", "Common.define.smartArt.textRadialVenn": "放射狀文氏圖", "Common.define.smartArt.textRandomToResultProcess": "隨機到結果的流程", "Common.define.smartArt.textRelationship": "關聯性", "Common.define.smartArt.textRepeatingBendingProcess": "重複彎曲流程", "Common.define.smartArt.textReverseList": "反向清單", "Common.define.smartArt.textSegmentedCycle": "分段循環", "Common.define.smartArt.textSegmentedProcess": "分段流程", "Common.define.smartArt.textSegmentedPyramid": "分段金字塔", "Common.define.smartArt.textSnapshotPictureList": "快照圖片清單", "Common.define.smartArt.textSpiralPicture": "螺旋圖片", "Common.define.smartArt.textSquareAccentList": "方形強調清單", "Common.define.smartArt.textStackedList": "堆疊清單", "Common.define.smartArt.textStackedVenn": "堆疊文氏圖", "Common.define.smartArt.textStaggeredProcess": "交錯式流程", "Common.define.smartArt.textStepDownProcess": "向下階梯式流程", "Common.define.smartArt.textStepUpProcess": "向上階梯式流程", "Common.define.smartArt.textSubStepProcess": "子步驟流程", "Common.define.smartArt.textTabbedArc": "帶有制表符的弧形", "Common.define.smartArt.textTableHierarchy": "表格階層", "Common.define.smartArt.textTableList": "表格清單", "Common.define.smartArt.textTabList": "定位清單", "Common.define.smartArt.textTargetList": "目標清單", "Common.define.smartArt.textTextCycle": "文字循環", "Common.define.smartArt.textThemePictureAccent": "主題圖片強調", "Common.define.smartArt.textThemePictureAlternatingAccent": "主題圖片交替強調", "Common.define.smartArt.textThemePictureGrid": "主題圖片網格", "Common.define.smartArt.textTitledMatrix": "標題矩陣", "Common.define.smartArt.textTitledPictureAccentList": "標題圖片強調清單", "Common.define.smartArt.textTitledPictureBlocks": "標題圖片區塊", "Common.define.smartArt.textTitlePictureLineup": "標題圖片對齊", "Common.define.smartArt.textTrapezoidList": "梯形列表", "Common.define.smartArt.textUpwardArrow": "向上箭頭", "Common.define.smartArt.textVaryingWidthList": "不同寬度清單", "Common.define.smartArt.textVerticalAccentList": "垂直強調清單", "Common.define.smartArt.textVerticalArrowList": "垂直箭頭清單", "Common.define.smartArt.textVerticalBendingProcess": "垂直彎曲流程", "Common.define.smartArt.textVerticalBlockList": "垂直區塊清單", "Common.define.smartArt.textVerticalBoxList": "垂直方塊清單", "Common.define.smartArt.textVerticalBracketList": "垂直括弧清單", "Common.define.smartArt.textVerticalBulletList": "垂直項目符號清單", "Common.define.smartArt.textVerticalChevronList": "垂直雙箭頭清單", "Common.define.smartArt.textVerticalCircleList": "垂直圓圈清單", "Common.define.smartArt.textVerticalCurvedList": "垂直曲線清單", "Common.define.smartArt.textVerticalEquation": "垂直方程式", "Common.define.smartArt.textVerticalPictureAccentList": "垂直圖片重音清單", "Common.define.smartArt.textVerticalPictureList": "垂直圖片清單", "Common.define.smartArt.textVerticalProcess": "垂直流程", "Common.Translation.textMoreButton": "更多", "Common.Translation.tipFileLocked": "文件正在被鎖定，您可以進行更改並稍後保存為本地副本。", "Common.Translation.tipFileReadOnly": "該文件是唯讀的。若要保留您的變更，請使用新名稱或不同位置儲存該文件。", "Common.Translation.warnFileLocked": "您無法編輯此文件，因為它正在另一個應用程式中被編輯。", "Common.Translation.warnFileLockedBtnEdit": "創建副本", "Common.Translation.warnFileLockedBtnView": "僅供查看", "Common.UI.ButtonColored.textAutoColor": "自動", "Common.UI.ButtonColored.textEyedropper": "吸管", "Common.UI.ButtonColored.textNewColor": "更多顏色", "Common.UI.Calendar.textApril": "四月", "Common.UI.Calendar.textAugust": "八月", "Common.UI.Calendar.textDecember": "十二月", "Common.UI.Calendar.textFebruary": "二月", "Common.UI.Calendar.textJanuary": "一月", "Common.UI.Calendar.textJuly": "七月", "Common.UI.Calendar.textJune": "六月", "Common.UI.Calendar.textMarch": "三月", "Common.UI.Calendar.textMay": "五月", "Common.UI.Calendar.textMonths": "月份", "Common.UI.Calendar.textNovember": "十一月", "Common.UI.Calendar.textOctober": "十月", "Common.UI.Calendar.textSeptember": "九月", "Common.UI.Calendar.textShortApril": "四月", "Common.UI.Calendar.textShortAugust": "八月", "Common.UI.Calendar.textShortDecember": "十二月", "Common.UI.Calendar.textShortFebruary": "二月", "Common.UI.Calendar.textShortFriday": "Fr", "Common.UI.Calendar.textShortJanuary": "一月", "Common.UI.Calendar.textShortJuly": "七月", "Common.UI.Calendar.textShortJune": "六月", "Common.UI.Calendar.textShortMarch": "三月", "Common.UI.Calendar.textShortMay": "May", "Common.UI.Calendar.textShortMonday": "Mo", "Common.UI.Calendar.textShortNovember": "十一月", "Common.UI.Calendar.textShortOctober": "十月", "Common.UI.Calendar.textShortSaturday": "Sa", "Common.UI.Calendar.textShortSeptember": "9月", "Common.UI.Calendar.textShortSunday": "次方，上標", "Common.UI.Calendar.textShortThursday": "th", "Common.UI.Calendar.textShortTuesday": "Tu", "Common.UI.Calendar.textShortWednesday": "我們", "Common.UI.Calendar.textYears": "年份", "Common.UI.ComboBorderSize.txtNoBorders": "無邊框", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "無邊框", "Common.UI.ComboDataView.emptyComboText": "無樣式", "Common.UI.ExtendedColorDialog.addButtonText": "新增", "Common.UI.ExtendedColorDialog.textCurrent": "目前", "Common.UI.ExtendedColorDialog.textHexErr": "輸入的值不正確。<br>請輸入介於000000和FFFFFF之間的值。", "Common.UI.ExtendedColorDialog.textNew": "新增", "Common.UI.ExtendedColorDialog.textRGBErr": "輸入的值不正確。<br>請輸入介於0和255之間的數值。", "Common.UI.HSBColorPicker.textNoColor": "沒有顏色", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnCalendar.textDate": "選擇日期", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "隱藏密碼", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "顯示密碼", "Common.UI.SearchBar.textFind": "尋找", "Common.UI.SearchBar.tipCloseSearch": "關閉搜尋", "Common.UI.SearchBar.tipNextResult": "下一個結果", "Common.UI.SearchBar.tipOpenAdvancedSettings": "開啟進階設定", "Common.UI.SearchBar.tipPreviousResult": "前一個結果", "Common.UI.SearchDialog.textHighlight": "突顯結果", "Common.UI.SearchDialog.textMatchCase": "區分大小寫", "Common.UI.SearchDialog.textReplaceDef": "請輸入替換文字", "Common.UI.SearchDialog.textSearchStart": "在此輸入您的文字", "Common.UI.SearchDialog.textTitle": "尋找並取代", "Common.UI.SearchDialog.textTitle2": "尋找", "Common.UI.SearchDialog.textWholeWords": "僅完整單詞", "Common.UI.SearchDialog.txtBtnHideReplace": "隱藏取代", "Common.UI.SearchDialog.txtBtnReplace": "取代", "Common.UI.SearchDialog.txtBtnReplaceAll": "全部取代", "Common.UI.SynchronizeTip.textDontShow": "不再顯示此訊息", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "文件已被其他使用者更改。<br>請按一下以保存您的更改並重新載入更新。", "Common.UI.ThemeColorPalette.textRecentColors": "近期顏色", "Common.UI.ThemeColorPalette.textStandartColors": "標準顏色", "Common.UI.ThemeColorPalette.textThemeColors": "主題顏色", "Common.UI.ThemeColorPalette.textTransparent": "透明", "Common.UI.Themes.txtThemeClassicLight": "經典亮色", "Common.UI.Themes.txtThemeContrastDark": "對比度深", "Common.UI.Themes.txtThemeDark": "深色", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "淺色", "Common.UI.Themes.txtThemeSystem": "與系統相同", "Common.UI.Window.cancelButtonText": "取消", "Common.UI.Window.closeButtonText": "關閉", "Common.UI.Window.noButtonText": "否", "Common.UI.Window.okButtonText": "確定", "Common.UI.Window.textConfirmation": "確認", "Common.UI.Window.textDontShow": "不再顯示此訊息", "Common.UI.Window.textError": "錯誤", "Common.UI.Window.textInformation": "資訊", "Common.UI.Window.textWarning": "警告", "Common.UI.Window.yesButtonText": "是", "Common.Utils.Metric.txtCm": "公分", "Common.Utils.Metric.txtPt": "點（排版單位）", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "移動", "Common.Utils.ThemeColor.txtaccent": "口音", "Common.Utils.ThemeColor.txtAqua": "水藍色", "Common.Utils.ThemeColor.txtbackground": "背景", "Common.Utils.ThemeColor.txtBlack": "黑色", "Common.Utils.ThemeColor.txtBlue": "藍色", "Common.Utils.ThemeColor.txtBrightGreen": "明亮綠色", "Common.Utils.ThemeColor.txtBrown": "棕色", "Common.Utils.ThemeColor.txtDarkBlue": "深藍色", "Common.Utils.ThemeColor.txtDarker": "更暗", "Common.Utils.ThemeColor.txtDarkGray": "深灰色", "Common.Utils.ThemeColor.txtDarkGreen": "深綠色", "Common.Utils.ThemeColor.txtDarkPurple": "深紫色", "Common.Utils.ThemeColor.txtDarkRed": "深紅色", "Common.Utils.ThemeColor.txtDarkTeal": "深青色", "Common.Utils.ThemeColor.txtDarkYellow": "深黃色", "Common.Utils.ThemeColor.txtGold": "金色", "Common.Utils.ThemeColor.txtGray": "灰色", "Common.Utils.ThemeColor.txtGreen": "綠色", "Common.Utils.ThemeColor.txtIndigo": "靛藍色", "Common.Utils.ThemeColor.txtLavender": "薰衣草色", "Common.Utils.ThemeColor.txtLightBlue": "淺藍色", "Common.Utils.ThemeColor.txtLighter": "較淺的", "Common.Utils.ThemeColor.txtLightGray": "淺灰色", "Common.Utils.ThemeColor.txtLightGreen": "淺綠色", "Common.Utils.ThemeColor.txtLightOrange": "淺橘色", "Common.Utils.ThemeColor.txtLightYellow": "淺黃色", "Common.Utils.ThemeColor.txtOrange": "橘色", "Common.Utils.ThemeColor.txtPink": "粉紅色", "Common.Utils.ThemeColor.txtPurple": "紫色", "Common.Utils.ThemeColor.txtRed": "紅色", "Common.Utils.ThemeColor.txtRose": "玫瑰色", "Common.Utils.ThemeColor.txtSkyBlue": "天藍色", "Common.Utils.ThemeColor.txtTeal": "藍綠色", "Common.Utils.ThemeColor.txttext": "文字", "Common.Utils.ThemeColor.txtTurquosie": "藍綠色", "Common.Utils.ThemeColor.txtViolet": "紫羅蘭色", "Common.Utils.ThemeColor.txtWhite": "白色", "Common.Utils.ThemeColor.txtYellow": "黃色", "Common.Views.About.txtAddress": "地址:", "Common.Views.About.txtLicensee": "被授權方", "Common.Views.About.txtLicensor": "授權方", "Common.Views.About.txtMail": "電子郵件：", "Common.Views.About.txtPoweredBy": "由...提供", "Common.Views.About.txtTel": "電話: ", "Common.Views.About.txtVersion": "版本", "Common.Views.AutoCorrectDialog.textAdd": "新增", "Common.Views.AutoCorrectDialog.textApplyText": "輸入時套用", "Common.Views.AutoCorrectDialog.textAutoCorrect": "文字自動更正", "Common.Views.AutoCorrectDialog.textAutoFormat": "輸入時自動套用格式", "Common.Views.AutoCorrectDialog.textBulleted": "自動項目符號", "Common.Views.AutoCorrectDialog.textBy": "依照", "Common.Views.AutoCorrectDialog.textDelete": "刪除", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "按兩下空白鍵自動增加一個句點(.)符號", "Common.Views.AutoCorrectDialog.textFLCells": "儲存格第一個字母大寫", "Common.Views.AutoCorrectDialog.textFLDont": "之後不要大寫", "Common.Views.AutoCorrectDialog.textFLSentence": "每個句子的第一個字母大寫", "Common.Views.AutoCorrectDialog.textForLangFL": "語言的例外情況：", "Common.Views.AutoCorrectDialog.textHyperlink": "以超連結取代網際網路和網路路徑", "Common.Views.AutoCorrectDialog.textHyphens": "連字號（--）取代成破折號（—）", "Common.Views.AutoCorrectDialog.textMathCorrect": "數學自動校正", "Common.Views.AutoCorrectDialog.textNumbered": "自動編號列表", "Common.Views.AutoCorrectDialog.textQuotes": "\"直引號\"與\"智能引號\"", "Common.Views.AutoCorrectDialog.textRecognized": "公認的功能", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "下面的運算式是可辨識的數學運算式，將不會自動以斜體表示", "Common.Views.AutoCorrectDialog.textReplace": "取代", "Common.Views.AutoCorrectDialog.textReplaceText": "輸入時替換文字", "Common.Views.AutoCorrectDialog.textReplaceType": "輸入時替換文字", "Common.Views.AutoCorrectDialog.textReset": "重置", "Common.Views.AutoCorrectDialog.textResetAll": "重設為預設值", "Common.Views.AutoCorrectDialog.textRestore": "恢復", "Common.Views.AutoCorrectDialog.textTitle": "自動更正", "Common.Views.AutoCorrectDialog.textWarnAddFL": "例外情況只能包含字母，大寫或小寫。", "Common.Views.AutoCorrectDialog.textWarnAddRec": "識別的函數只能包含字母A到Z，大寫或小寫。", "Common.Views.AutoCorrectDialog.textWarnResetFL": "您新增的所有例外狀況都將被移除，已移除的例外狀況將會被還原。您要繼續嗎？", "Common.Views.AutoCorrectDialog.textWarnResetRec": "您新增的所有表達式都將被刪除，被刪除的表達式將被恢復。你想繼續嗎？", "Common.Views.AutoCorrectDialog.warnReplace": "%1的自動更正項已經存在。是否要更換？", "Common.Views.AutoCorrectDialog.warnReset": "您增加的所有自動更正功能將被刪除，更改後的自動更正將恢復為其原始值。你想繼續嗎？", "Common.Views.AutoCorrectDialog.warnRestore": "%1的自動更正項目將被重置為其原始值。是否要繼續？", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "發送", "Common.Views.Comments.mniAuthorAsc": "作者 A 到 Z", "Common.Views.Comments.mniAuthorDesc": "作者 Z 到 A", "Common.Views.Comments.mniDateAsc": "最舊的", "Common.Views.Comments.mniDateDesc": "最新的", "Common.Views.Comments.mniFilterGroups": "依群組篩選", "Common.Views.Comments.mniPositionAsc": "從頂部", "Common.Views.Comments.mniPositionDesc": "來自底部", "Common.Views.Comments.textAdd": "新增", "Common.Views.Comments.textAddComment": "發表註解", "Common.Views.Comments.textAddCommentToDoc": "在文檔中新增註解", "Common.Views.Comments.textAddReply": "加入回應", "Common.Views.Comments.textAll": "全部", "Common.Views.Comments.textAnonym": "訪客", "Common.Views.Comments.textCancel": "取消", "Common.Views.Comments.textClose": "關閉", "Common.Views.Comments.textClosePanel": "關閉評論", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "評論", "Common.Views.Comments.textEdit": "確定", "Common.Views.Comments.textEnterCommentHint": "在此輸入您的評論", "Common.Views.Comments.textHintAddComment": "新增註解", "Common.Views.Comments.textOpenAgain": "重新開啟", "Common.Views.Comments.textReply": "回覆", "Common.Views.Comments.textResolve": "解決", "Common.Views.Comments.textResolved": "已解決", "Common.Views.Comments.textSort": "排序註解", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "您沒有重新開啟評論的權限", "Common.Views.Comments.txtEmpty": "文件中沒有任何註解。", "Common.Views.CopyWarningDialog.textDontShow": "不再顯示此訊息", "Common.Views.CopyWarningDialog.textMsg": "僅能在此編輯器中使用編輯器工具列按鈕和上下文選單進行複製、剪下和貼上操作。<br><br>要在編輯器外的應用程序之間進行複製或貼上，請使用以下鍵盤組合：", "Common.Views.CopyWarningDialog.textTitle": "複製、剪下和貼上動作", "Common.Views.CopyWarningDialog.textToCopy": "適用於複製", "Common.Views.CopyWarningDialog.textToCut": "適用於剪下", "Common.Views.CopyWarningDialog.textToPaste": "適用於貼上", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "載入中...", "Common.Views.DocumentAccessDialog.textTitle": "分享設定", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "橡皮擦", "Common.Views.Draw.hintSelect": "選擇", "Common.Views.Draw.txtEraser": "橡皮擦", "Common.Views.Draw.txtHighlighter": "螢光筆", "Common.Views.Draw.txtMM": "毫米", "Common.Views.Draw.txtPen": "筆", "Common.Views.Draw.txtSelect": "選擇", "Common.Views.Draw.txtSize": "大小", "Common.Views.ExternalDiagramEditor.textTitle": "圖表編輯器", "Common.Views.ExternalEditor.textClose": "關閉", "Common.Views.ExternalEditor.textSave": "儲存並關閉", "Common.Views.ExternalMergeEditor.textTitle": "郵件合併收件人", "Common.Views.ExternalOleEditor.textTitle": "試算表編輯器", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "正在編輯文件的使用者：", "Common.Views.Header.textAddFavorite": "標記為常用項目", "Common.Views.Header.textAdvSettings": "進階設定", "Common.Views.Header.textBack": "打開檔案位置", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "隱藏工具列", "Common.Views.Header.textDocEditDesc": "Make any changes", "Common.Views.Header.textDocViewDesc": "View the file, but make no changes", "Common.Views.Header.textDocViewFormDesc": "See how the form will look like when filling out", "Common.Views.Header.textDownload": "Download", "Common.Views.Header.textEdit": "Editing", "Common.Views.Header.textHideLines": "隱藏標尺", "Common.Views.Header.textHideStatusBar": "隱藏狀態列", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "唯讀", "Common.Views.Header.textRemoveFavorite": "\n從最愛收藏夾中刪除", "Common.Views.Header.textReview": "Reviewing", "Common.Views.Header.textReviewDesc": "Suggest changes", "Common.Views.Header.textShare": "分享", "Common.Views.Header.textStartFill": "Share & collect", "Common.Views.Header.textView": "Viewing", "Common.Views.Header.textViewForm": "Viewing form", "Common.Views.Header.textZoom": "縮放", "Common.Views.Header.tipAccessRights": "管理文檔存取權限", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDocEdit": "Editing", "Common.Views.Header.tipDocView": "Viewing", "Common.Views.Header.tipDocViewForm": "Viewing form", "Common.Views.Header.tipDownload": "下載文件", "Common.Views.Header.tipFillStatus": "Filling status", "Common.Views.Header.tipGoEdit": "編輯當前檔案", "Common.Views.Header.tipPrint": "列印檔案", "Common.Views.Header.tipPrintQuick": "快速列印", "Common.Views.Header.tipRedo": "重做", "Common.Views.Header.tipReview": "Reviewing", "Common.Views.Header.tipSave": "儲存", "Common.Views.Header.tipSearch": "搜尋", "Common.Views.Header.tipUndo": "復原", "Common.Views.Header.tipUsers": "檢視使用者", "Common.Views.Header.tipViewSettings": "檢視設定", "Common.Views.Header.tipViewUsers": "檢視使用者並管理文件存取權限", "Common.Views.Header.txtAccessRights": "變更存取權限", "Common.Views.Header.txtRename": "重新命名", "Common.Views.History.textCloseHistory": "關閉歷史紀錄", "Common.Views.History.textHideAll": "隱藏詳細更改", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "還原", "Common.Views.History.textShowAll": "顯示詳細變更", "Common.Views.History.textVer": "版本", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "貼上圖片URL：", "Common.Views.ImageFromUrlDialog.txtEmpty": "此欄位為必填欄位", "Common.Views.ImageFromUrlDialog.txtNotUrl": "此欄位應為符合「http://www.example.com」格式的網址。", "Common.Views.InsertTableDialog.textInvalidRowsCols": "您需要指定有效的列數和行數。", "Common.Views.InsertTableDialog.txtColumns": "欄數", "Common.Views.InsertTableDialog.txtMaxText": "該欄位的最大值為 {0}。", "Common.Views.InsertTableDialog.txtMinText": "該欄位的最小值為 {0}。", "Common.Views.InsertTableDialog.txtRows": "列數", "Common.Views.InsertTableDialog.txtTitle": "表格大小", "Common.Views.InsertTableDialog.txtTitleSplit": "分割儲存格", "Common.Views.LanguageDialog.labelSelect": "選擇文件語言", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "關閉檔案", "Common.Views.OpenDialog.txtEncoding": "編碼", "Common.Views.OpenDialog.txtIncorrectPwd": "密碼不正確。", "Common.Views.OpenDialog.txtOpenFile": "請輸入用於開啟文件的密碼", "Common.Views.OpenDialog.txtPassword": "密碼", "Common.Views.OpenDialog.txtPreview": "預覽", "Common.Views.OpenDialog.txtProtected": "當您輸入密碼並打開文件後，該文件的當前密碼將被重置。", "Common.Views.OpenDialog.txtTitle": "選擇％1個選項", "Common.Views.OpenDialog.txtTitleProtected": "受保護的檔案", "Common.Views.PasswordDialog.txtDescription": "設定密碼以保護此文件。", "Common.Views.PasswordDialog.txtIncorrectPwd": "確認密碼不相同", "Common.Views.PasswordDialog.txtPassword": "密碼", "Common.Views.PasswordDialog.txtRepeat": "重複輸入密碼", "Common.Views.PasswordDialog.txtTitle": "設置密碼", "Common.Views.PasswordDialog.txtWarning": "警告：如果您遺失或忘記密碼，將無法恢復。請將密碼保存在安全的地方。", "Common.Views.PluginDlg.textLoading": "載入中", "Common.Views.PluginPanel.textClosePanel": "關閉插件", "Common.Views.PluginPanel.textLoading": "載入中", "Common.Views.Plugins.groupCaption": "外掛程式", "Common.Views.Plugins.strPlugins": "外掛程式", "Common.Views.Plugins.textBackgroundPlugins": "背景組件", "Common.Views.Plugins.textSettings": "設定", "Common.Views.Plugins.textStart": "開始", "Common.Views.Plugins.textStop": "停止", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "更多", "Common.Views.Protection.hintAddPwd": "使用密碼進行加密", "Common.Views.Protection.hintDelPwd": "刪除密碼", "Common.Views.Protection.hintPwd": "變更或刪除密碼", "Common.Views.Protection.hintSignature": "新增數位簽章或簽名欄", "Common.Views.Protection.txtAddPwd": "新增密碼", "Common.Views.Protection.txtChangePwd": "變更密碼", "Common.Views.Protection.txtDeletePwd": "刪除密碼", "Common.Views.Protection.txtEncrypt": "加密", "Common.Views.Protection.txtInvisibleSignature": "新增數位簽章", "Common.Views.Protection.txtSignature": "簽名", "Common.Views.Protection.txtSignatureLine": "新增簽名欄", "Common.Views.RecentFiles.txtOpenRecent": "最近打開的", "Common.Views.RenameDialog.textName": "檔案名稱", "Common.Views.RenameDialog.txtInvalidName": "文件名稱不能包含以下任何字元：", "Common.Views.ReviewChanges.hintNext": "到下一個變更處", "Common.Views.ReviewChanges.hintPrev": "到前一個變更處", "Common.Views.ReviewChanges.mniFromFile": "從檔案建立文件", "Common.Views.ReviewChanges.mniFromStorage": "從儲存空間建立文件", "Common.Views.ReviewChanges.mniFromUrl": "從 URL 建立文件", "Common.Views.ReviewChanges.mniMMFromFile": "From file", "Common.Views.ReviewChanges.mniMMFromStorage": "From storage", "Common.Views.ReviewChanges.mniMMFromUrl": "From URL", "Common.Views.ReviewChanges.mniSettings": "比較設定", "Common.Views.ReviewChanges.strFast": "快速", "Common.Views.ReviewChanges.strFastDesc": "即時共同編輯。所有更改都會自動儲存。", "Common.Views.ReviewChanges.strStrict": "嚴格", "Common.Views.ReviewChanges.strStrictDesc": "使用「儲存」按鈕同步您和其他人所做的更改", "Common.Views.ReviewChanges.textEnable": "啟用", "Common.Views.ReviewChanges.textWarnTrackChanges": "追蹤變更將開啟，所有擁有完全訪問權限的使用者都將被開啟。下一次有人打開文件時，追蹤變更功能將保持啟用。", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "為所有人啟用追蹤變更？", "Common.Views.ReviewChanges.tipAcceptCurrent": "同意當前更改", "Common.Views.ReviewChanges.tipCoAuthMode": "設定共同編輯模式", "Common.Views.ReviewChanges.tipCombine": "將當前文件與另一個文件結合", "Common.Views.ReviewChanges.tipCommentRem": "刪除註解", "Common.Views.ReviewChanges.tipCommentRemCurrent": "刪除當前註解", "Common.Views.ReviewChanges.tipCommentResolve": "標記註解為已解決", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "將所有的註解標記為已解決", "Common.Views.ReviewChanges.tipCompare": "將當前文件與另一個文件進行比較", "Common.Views.ReviewChanges.tipHistory": "顯示版本歷史", "Common.Views.ReviewChanges.tipMailRecepients": "Mail merge", "Common.Views.ReviewChanges.tipRejectCurrent": "拒絕當前修訂", "Common.Views.ReviewChanges.tipReview": "追蹤變更", "Common.Views.ReviewChanges.tipReviewView": "選擇要顯示變更的模式", "Common.Views.ReviewChanges.tipSetDocLang": "設定文件語言", "Common.Views.ReviewChanges.tipSetSpelling": "拼字檢查", "Common.Views.ReviewChanges.tipSharing": "管理文檔存取權限", "Common.Views.ReviewChanges.txtAccept": "同意", "Common.Views.ReviewChanges.txtAcceptAll": "接受所有變更", "Common.Views.ReviewChanges.txtAcceptChanges": "同意更改", "Common.Views.ReviewChanges.txtAcceptCurrent": "接受目前的變更", "Common.Views.ReviewChanges.txtChat": "聊天", "Common.Views.ReviewChanges.txtClose": "關閉", "Common.Views.ReviewChanges.txtCoAuthMode": "共同編輯模式", "Common.Views.ReviewChanges.txtCombine": "結合", "Common.Views.ReviewChanges.txtCommentRemAll": "刪除所有註解", "Common.Views.ReviewChanges.txtCommentRemCurrent": "刪除當前註解", "Common.Views.ReviewChanges.txtCommentRemMy": "刪除我的註解", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "刪除我當前的註解", "Common.Views.ReviewChanges.txtCommentRemove": "刪除", "Common.Views.ReviewChanges.txtCommentResolve": "解決", "Common.Views.ReviewChanges.txtCommentResolveAll": "標記註解為已解決", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "將所有的註解標記為已解決", "Common.Views.ReviewChanges.txtCommentResolveMy": "將自己所有的註解標記為已解決", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "將自己的註解標記為已解決", "Common.Views.ReviewChanges.txtCompare": "比較", "Common.Views.ReviewChanges.txtDocLang": "語言", "Common.Views.ReviewChanges.txtEditing": "正在編輯", "Common.Views.ReviewChanges.txtFinal": "更改已全部接受 {0}", "Common.Views.ReviewChanges.txtFinalCap": "最終的", "Common.Views.ReviewChanges.txtHistory": "版本歷史", "Common.Views.ReviewChanges.txtMailMerge": "Mail Merge", "Common.Views.ReviewChanges.txtMarkup": "全部的更改{0}", "Common.Views.ReviewChanges.txtMarkupCap": "標記和註釋", "Common.Views.ReviewChanges.txtMarkupSimple": "所有變化{0}<br>無文字通知", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "僅標記", "Common.Views.ReviewChanges.txtNext": "下一個", "Common.Views.ReviewChanges.txtOff": "我關閉了", "Common.Views.ReviewChanges.txtOffGlobal": "我和所有人都關閉了", "Common.Views.ReviewChanges.txtOn": "開啟我自己的", "Common.Views.ReviewChanges.txtOnGlobal": "開啟我和所有人的", "Common.Views.ReviewChanges.txtOriginal": "全部更改被拒絕{0}", "Common.Views.ReviewChanges.txtOriginalCap": "原始的", "Common.Views.ReviewChanges.txtPrev": "前一個", "Common.Views.ReviewChanges.txtPreview": "預覽", "Common.Views.ReviewChanges.txtReject": "拒絕", "Common.Views.ReviewChanges.txtRejectAll": "拒絕所有變更", "Common.Views.ReviewChanges.txtRejectChanges": "拒絕變更", "Common.Views.ReviewChanges.txtRejectCurrent": "拒絕當前變更", "Common.Views.ReviewChanges.txtSharing": "分享", "Common.Views.ReviewChanges.txtSpelling": "拼字檢查", "Common.Views.ReviewChanges.txtTurnon": "追蹤變更", "Common.Views.ReviewChanges.txtView": "顯示模式", "Common.Views.ReviewChangesDialog.textTitle": "查看變更", "Common.Views.ReviewChangesDialog.txtAccept": "同意", "Common.Views.ReviewChangesDialog.txtAcceptAll": "接受所有變更", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "接受目前的變更", "Common.Views.ReviewChangesDialog.txtNext": "到下一個變更處", "Common.Views.ReviewChangesDialog.txtPrev": "到前一個變更處", "Common.Views.ReviewChangesDialog.txtReject": "拒絕", "Common.Views.ReviewChangesDialog.txtRejectAll": "拒絕所有變更", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "拒絕當前變更", "Common.Views.ReviewPopover.textAdd": "新增", "Common.Views.ReviewPopover.textAddReply": "加入回應", "Common.Views.ReviewPopover.textCancel": "取消", "Common.Views.ReviewPopover.textClose": "關閉", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "確定", "Common.Views.ReviewPopover.textEnterComment": "在此輸入您的評論", "Common.Views.ReviewPopover.textFollowMove": "跟隨移動", "Common.Views.ReviewPopover.textMention": "+提及將提供對文檔的存取權限並發送電子郵件", "Common.Views.ReviewPopover.textMentionNotify": "+提及將通過電子郵件通知帳戶", "Common.Views.ReviewPopover.textOpenAgain": "重新開啟", "Common.Views.ReviewPopover.textReply": "回覆", "Common.Views.ReviewPopover.textResolve": "解決", "Common.Views.ReviewPopover.textViewResolved": "您沒有重新開啟評論的權限", "Common.Views.ReviewPopover.txtAccept": "同意", "Common.Views.ReviewPopover.txtDeleteTip": "刪除", "Common.Views.ReviewPopover.txtEditTip": "編輯", "Common.Views.ReviewPopover.txtReject": "拒絕", "Common.Views.SaveAsDlg.textLoading": "載入中", "Common.Views.SaveAsDlg.textTitle": "儲存用的資料夾", "Common.Views.SearchPanel.textCaseSensitive": "區分大小寫", "Common.Views.SearchPanel.textCloseSearch": "關閉搜尋", "Common.Views.SearchPanel.textContentChanged": "文件已更改。", "Common.Views.SearchPanel.textFind": "尋找", "Common.Views.SearchPanel.textFindAndReplace": "尋找並取代", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} 項成功取代。", "Common.Views.SearchPanel.textMatchUsingRegExp": "使用正規表達式比對", "Common.Views.SearchPanel.textNoMatches": "沒有符合的結果", "Common.Views.SearchPanel.textNoSearchResults": "無搜索结果", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} 項已取代。剩餘 {2} 項被其他使用者鎖定。", "Common.Views.SearchPanel.textReplace": "取代", "Common.Views.SearchPanel.textReplaceAll": "全部取代", "Common.Views.SearchPanel.textReplaceWith": "取代為", "Common.Views.SearchPanel.textSearchAgain": "{0}進行新的搜尋{1}以獲得準確的結果。", "Common.Views.SearchPanel.textSearchHasStopped": "已停止搜尋", "Common.Views.SearchPanel.textSearchResults": "搜索结果：{0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textTooManyResults": "因數量過多而無法顯示部分結果", "Common.Views.SearchPanel.textWholeWords": "僅完整單詞", "Common.Views.SearchPanel.tipNextResult": "下一個結果", "Common.Views.SearchPanel.tipPreviousResult": "前一個結果", "Common.Views.SelectFileDlg.textLoading": "載入中", "Common.Views.SelectFileDlg.textTitle": "選擇資料來源", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "粗體", "Common.Views.SignDialog.textCertificate": "證書", "Common.Views.SignDialog.textChange": "變更", "Common.Views.SignDialog.textInputName": "輸入簽署者名稱", "Common.Views.SignDialog.textItalic": "斜體", "Common.Views.SignDialog.textNameError": "簽署者名稱不得為空白。", "Common.Views.SignDialog.textPurpose": "簽署本文件的目的", "Common.Views.SignDialog.textSelect": "選擇", "Common.Views.SignDialog.textSelectImage": "選擇圖片", "Common.Views.SignDialog.textSignature": "簽名外觀如下", "Common.Views.SignDialog.textTitle": "簽署文件", "Common.Views.SignDialog.textUseImage": "或點選「選取圖片」以使用圖片作為簽名，橙色", "Common.Views.SignDialog.textValid": "有效期從 %1 到 %2", "Common.Views.SignDialog.tipFontName": "字型名稱", "Common.Views.SignDialog.tipFontSize": "字型大小", "Common.Views.SignSettingsDialog.textAllowComment": "允許簽名者在簽名對話框中添加註釋", "Common.Views.SignSettingsDialog.textDefInstruction": "在簽署此文件前，請確認您簽署的內容正確無誤。", "Common.Views.SignSettingsDialog.textInfoEmail": "建議簽署人的電子郵件", "Common.Views.SignSettingsDialog.textInfoName": "建議簽署人", "Common.Views.SignSettingsDialog.textInfoTitle": "建議簽署人稱謂", "Common.Views.SignSettingsDialog.textInstructions": "簽署者說明", "Common.Views.SignSettingsDialog.textShowDate": "在簽名行中顯示簽署日期", "Common.Views.SignSettingsDialog.textTitle": "簽名設定", "Common.Views.SignSettingsDialog.txtEmpty": "此欄位為必填欄位", "Common.Views.SymbolTableDialog.textCharacter": "字元", "Common.Views.SymbolTableDialog.textCode": "Unicode十六進制值", "Common.Views.SymbolTableDialog.textCopyright": "版權符號", "Common.Views.SymbolTableDialog.textDCQuote": "結束的雙引號", "Common.Views.SymbolTableDialog.textDOQuote": "開頭雙引號", "Common.Views.SymbolTableDialog.textEllipsis": "水平橢圓", "Common.Views.SymbolTableDialog.textEmDash": "破折號", "Common.Views.SymbolTableDialog.textEmSpace": "字寬空白", "Common.Views.SymbolTableDialog.textEnDash": "短橫線", "Common.Views.SymbolTableDialog.textEnSpace": "字元寬空白", "Common.Views.SymbolTableDialog.textFont": "字型", "Common.Views.SymbolTableDialog.textNBHyphen": "不間斷連字號", "Common.Views.SymbolTableDialog.textNBSpace": "不間斷空格", "Common.Views.SymbolTableDialog.textPilcrow": "段落符號", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 字寬空白", "Common.Views.SymbolTableDialog.textRange": "範圍", "Common.Views.SymbolTableDialog.textRecent": "最近使用的符號", "Common.Views.SymbolTableDialog.textRegistered": "註冊標誌", "Common.Views.SymbolTableDialog.textSCQuote": "結束的單引號", "Common.Views.SymbolTableDialog.textSection": "分區標誌", "Common.Views.SymbolTableDialog.textShortcut": "快捷鍵", "Common.Views.SymbolTableDialog.textSHyphen": "軟連字號", "Common.Views.SymbolTableDialog.textSOQuote": "單引號", "Common.Views.SymbolTableDialog.textSpecial": "特殊字符", "Common.Views.SymbolTableDialog.textSymbols": "符號", "Common.Views.SymbolTableDialog.textTitle": "符號", "Common.Views.SymbolTableDialog.textTradeMark": "商標符號", "Common.Views.UserNameDialog.textDontShow": "不要再次詢問我", "Common.Views.UserNameDialog.textLabel": "標籤：", "Common.Views.UserNameDialog.textLabelError": "標籤不能為空。", "DE.Controllers.DocProtection.txtIsProtectedComment": "文件已受保護。您只能在此文件中插入註解。", "DE.Controllers.DocProtection.txtIsProtectedForms": "文件已受保護。您只能填寫此文件中的表單。", "DE.Controllers.DocProtection.txtIsProtectedTrack": "文件已受保護。您可以編輯此文件，但所有更改都將被追蹤。", "DE.Controllers.DocProtection.txtIsProtectedView": "文件已受保護。您只能查看此文件。", "DE.Controllers.DocProtection.txtWasProtectedComment": "文件已由其他使用者保護。您只能在此文件中插入註解。", "DE.Controllers.DocProtection.txtWasProtectedForms": "文件已由其他使用者保護。您只能在此文件中填寫表單。", "DE.Controllers.DocProtection.txtWasProtectedTrack": "文件已由其他使用者保護。您可以編輯此文件，但所有更改都將被追蹤。", "DE.Controllers.DocProtection.txtWasProtectedView": "文件已由其他使用者保護。您只能檢視此文件。", "DE.Controllers.DocProtection.txtWasUnprotected": "文件已解除保護。", "DE.Controllers.LeftMenu.leavePageText": "該文檔中所有未儲存的更改都將丟失。<br>單擊“取消”，然後單擊“存檔”以保存它們。單擊“確定”，放棄所有未儲存的更改。", "DE.Controllers.LeftMenu.newDocumentTitle": "未命名文件", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "警告", "DE.Controllers.LeftMenu.requestEditRightsText": "正在請求編輯權限...", "DE.Controllers.LeftMenu.textLoadHistory": "載入版本記錄中...", "DE.Controllers.LeftMenu.textNoTextFound": "找不到您正在搜尋的資料。請調整您的搜尋選項。", "DE.Controllers.LeftMenu.textReplaceSkipped": "替換已完成。有 {0} 個項目被跳過。", "DE.Controllers.LeftMenu.textReplaceSuccess": "已完成搜尋。已替換的次數：{0}。", "DE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "DE.Controllers.LeftMenu.txtCompatible": "文件將儲存為新格式。這將允許使用所有編輯器功能，但可能會影響文件的版面配置。<br>如果您想使文件與較舊的 MS Word 版本兼容，請使用進階設定的相容性選項。", "DE.Controllers.LeftMenu.txtUntitled": "未命名", "DE.Controllers.LeftMenu.warnDownloadAs": "如果您繼續以此格式儲存，除了文字外的所有功能都將遺失。您確定要繼續嗎？", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "您的{0}將被轉換為可編輯的格式。這可能需要一段時間。生成的文件將被優化，以便您可以編輯文字，因此它可能不會完全與原始的{0}相同，特別是如果原始文件包含大量圖形。", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "如果您繼續以此格式儲存，部分格式可能會遺失。您確定要繼續嗎？", "DE.Controllers.LeftMenu.warnReplaceString": "{0}不可用於替換段文字落的有效特殊符號。", "DE.Controllers.Main.applyChangesTextText": "載入變更中...", "DE.Controllers.Main.applyChangesTitleText": "載入變更中", "DE.Controllers.Main.confirmMaxChangesSize": "操作的大小超出了您的伺服器設定的限制。<br>按一下「復原」來取消上一個動作，或按一下「繼續」在本地保留動作（您需要下載該文件或複製其內容，以確保不會丟失任何內容）。", "DE.Controllers.Main.convertationTimeoutText": "轉換逾時", "DE.Controllers.Main.criticalErrorExtText": "按下「確定」返回文件清單。", "DE.Controllers.Main.criticalErrorTitle": "錯誤", "DE.Controllers.Main.downloadErrorText": "下載失敗", "DE.Controllers.Main.downloadMergeText": "正在下載中...", "DE.Controllers.Main.downloadMergeTitle": "載入中", "DE.Controllers.Main.downloadTextText": "正在下載文件...", "DE.Controllers.Main.downloadTitleText": "正在下載文件", "DE.Controllers.Main.errorAccessDeny": "您正在嘗試執行一個您無權限進行的操作。<br>請聯絡您的文件伺服器管理員。", "DE.Controllers.Main.errorBadImageUrl": "圖片網址不正確", "DE.Controllers.Main.errorCannotPasteImg": "我們無法從剪貼簿貼上此圖片，但您可以將其儲存到您的裝置，然後再從那裡插入；或者您可以複製不帶文字的圖片並將其貼入文件中。", "DE.Controllers.Main.errorCoAuthoringDisconnect": "伺服器連線中斷，目前無法編輯文件。", "DE.Controllers.Main.errorComboSeries": "要建立組合圖表，請選擇至少兩個資料系列。", "DE.Controllers.Main.errorCompare": "在共同編輯時，無法使用比較文件功能。", "DE.Controllers.Main.errorConnectToServer": "文件無法儲存。請檢查連線設定或聯繫您的管理員。<br>按下「確定」按鈕後，您將被提示下載文件。", "DE.Controllers.Main.errorDatabaseConnection": "外部錯誤。<br>資料庫連線錯誤。如錯誤持續發生，請聯繫支援人員。", "DE.Controllers.Main.errorDataEncrypted": "已接收到加密的更改，無法解密。", "DE.Controllers.Main.errorDataRange": "資料範圍不正確。", "DE.Controllers.Main.errorDefaultMessage": "錯誤編號：%1", "DE.Controllers.Main.errorDirectUrl": "請驗證文件的連結。該連結必須是直接連結到可下載的文件。", "DE.Controllers.Main.errorEditingDownloadas": "在處理文檔期間發生錯誤。<br>使用“下載為”選項將文件備份副本保存到計算機硬碟驅動器中。", "DE.Controllers.Main.errorEditingSaveas": "使用文檔期間發生錯誤。<br>使用“另存為...”選項將文件備份副本保存到硬碟中。", "DE.Controllers.Main.errorEditProtectedRange": "You are not allowed to edit this selection because it is protected.", "DE.Controllers.Main.errorEmailClient": "找不到電子郵件客戶端。", "DE.Controllers.Main.errorEmptyTOC": "將樣式庫中的標題樣式應用到所選文件上", "DE.Controllers.Main.errorFilePassProtect": "該文件已被密碼保護，無法打開。", "DE.Controllers.Main.errorFileSizeExceed": "該文件大小超出您伺服器設定的限制。<br>請聯繫您的文件伺服器管理員以瞭解詳情。", "DE.Controllers.Main.errorForceSave": "保存文件時發生錯誤。請使用“下載為”選項將文件保存到電腦硬碟中，或稍後再試。", "DE.Controllers.Main.errorInconsistentExt": "開啟檔案時發生錯誤。<br>檔案內容與副檔名不一致。", "DE.Controllers.Main.errorInconsistentExtDocx": "開啟檔案時發生錯誤。<br>檔案內容對應到文字檔（例如 docx），但檔案副檔名不一致：%1。", "DE.Controllers.Main.errorInconsistentExtPdf": "開啟檔案時發生錯誤。<br>檔案內容對應到下列格式之一：pdf/djvu/xps/oxps，但檔案副檔名不一致：%1。", "DE.Controllers.Main.errorInconsistentExtPptx": "開啟檔案時發生錯誤。<br>檔案內容對應到簡報檔（例如 pptx），但檔案副檔名不一致：%1。", "DE.Controllers.Main.errorInconsistentExtXlsx": "開啟檔案時發生錯誤。<br>檔案內容對應到試算表檔案（例如 xlsx），但檔案副檔名不一致：%1。", "DE.Controllers.Main.errorKeyEncrypt": "未知的按鍵快捷功能", "DE.Controllers.Main.errorKeyExpire": "密鑰描述符已過期", "DE.Controllers.Main.errorLoadingFont": "字型未載入。<br>請聯繫您的文件伺服器管理員。", "DE.Controllers.Main.errorMailMergeLoadFile": "載入文件失敗。請選擇其他檔案。", "DE.Controllers.Main.errorMailMergeSaveFile": "合併失敗.", "DE.Controllers.Main.errorNoTOC": "沒有目錄可更新。您可以從「參考」標籤中插入一個。", "DE.Controllers.Main.errorPasswordIsNotCorrect": "您提供的密碼不正確。<br>請確保大寫鎖定鍵已關閉，並確保使用正確的大小寫。", "DE.Controllers.Main.errorProcessSaveResult": "儲存失敗", "DE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "DE.Controllers.Main.errorServerVersion": "編輯器版本已更新。將重新載入頁面以更新改動。", "DE.Controllers.Main.errorSessionAbsolute": "文件編輯會話已過期。請重新載入頁面。", "DE.Controllers.Main.errorSessionIdle": "該文件已經有一段時間未編輯。請重新載入頁面。", "DE.Controllers.Main.errorSessionToken": "與服務器的連接已中斷。請重新加載頁面。", "DE.Controllers.Main.errorSetPassword": "無法設定密碼。", "DE.Controllers.Main.errorStockChart": "資料行順序不正確。要建立股票圖表，請將數據按以下順序放置在工作表中：開盤價、最高價、最低價、收盤價。", "DE.Controllers.Main.errorSubmit": "提交失敗", "DE.Controllers.Main.errorTextFormWrongFormat": "輸入的值與欄位的格式不符合。", "DE.Controllers.Main.errorToken": "文件安全權杖格式不正確。<br>請聯繫您的文件伺服器管理員。", "DE.Controllers.Main.errorTokenExpire": "文件安全權杖已過期。<br>請聯繫您的文件伺服器管理員。", "DE.Controllers.Main.errorUpdateVersion": "文件版本已更改。將重新載入頁面。", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "連線已恢復，並且文件版本已更改。<br>在繼續工作之前，您需要下載文件或複製其內容，以確保不會丟失任何內容，然後重新加載此頁面。", "DE.Controllers.Main.errorUserDrop": "目前無法存取該檔案。", "DE.Controllers.Main.errorUsersExceed": "已超出價格方案允許的使用者數量。", "DE.Controllers.Main.errorViewerDisconnect": "連線已中斷。您仍然可以查看文件，<br>但在連線恢復並重新載入頁面之前，無法下載或列印文件。", "DE.Controllers.Main.leavePageText": "您在此文件中有未儲存的變更。按一下「留在此頁面」，然後按「儲存」以儲存這些變更。按一下「離開此頁面」以放棄所有未儲存的變更。", "DE.Controllers.Main.leavePageTextOnClose": "該文檔中所有未儲存的更改都將遺失。<br>單擊\"取消\"，然後單擊\"存檔\"以保存它們。單擊\"確定\"，放棄所有未儲存的更改。", "DE.Controllers.Main.loadFontsTextText": "載入資料中...", "DE.Controllers.Main.loadFontsTitleText": "載入資料", "DE.Controllers.Main.loadFontTextText": "載入資料中...", "DE.Controllers.Main.loadFontTitleText": "載入資料", "DE.Controllers.Main.loadImagesTextText": "載入圖片中...", "DE.Controllers.Main.loadImagesTitleText": "載入圖片中", "DE.Controllers.Main.loadImageTextText": "載入圖片中...", "DE.Controllers.Main.loadImageTitleText": "載入圖片中", "DE.Controllers.Main.loadingDocumentTextText": "載入文件中...", "DE.Controllers.Main.loadingDocumentTitleText": "載入文件中", "DE.Controllers.Main.mailMergeLoadFileText": "載入資料來源...", "DE.Controllers.Main.mailMergeLoadFileTitle": "載入資料來源", "DE.Controllers.Main.notcriticalErrorTitle": "警告", "DE.Controllers.Main.openErrorText": "開啟檔案時發生錯誤。", "DE.Controllers.Main.openTextText": "正在打開文件", "DE.Controllers.Main.openTitleText": "正在打開文件", "DE.Controllers.Main.printTextText": "正在列印文件...", "DE.Controllers.Main.printTitleText": "正在列印文件", "DE.Controllers.Main.reloadButtonText": "重新載入頁面", "DE.Controllers.Main.requestEditFailedMessageText": "有人正在編輯此文件。請稍後再試。", "DE.Controllers.Main.requestEditFailedTitleText": "拒絕存取", "DE.Controllers.Main.saveErrorText": "儲存檔案時發生錯誤", "DE.Controllers.Main.saveErrorTextDesktop": "無法保存或建立此文件。可能的原因包括：<br>1. 文件是唯讀的。<br>2. 文件正在被其他使用者編輯。<br>3. 磁碟已滿或損壞。", "DE.Controllers.Main.saveTextText": "正在儲存文件...", "DE.Controllers.Main.saveTitleText": "正在儲存文件", "DE.Controllers.Main.savingText": "Saving", "DE.Controllers.Main.scriptLoadError": "連線速度太慢，無法載入某些元件。請重新載入頁面。", "DE.Controllers.Main.sendMergeText": "發送合併中...", "DE.Controllers.Main.sendMergeTitle": "正在發送合併", "DE.Controllers.Main.splitDividerErrorText": "列數必須是 %1 的除數。", "DE.Controllers.Main.splitMaxColsErrorText": "欄位數必須少於 %1。", "DE.Controllers.Main.splitMaxRowsErrorText": "列數必須少於 %1。", "DE.Controllers.Main.textAnonymous": "匿名", "DE.Controllers.Main.textAnyone": "任何人", "DE.Controllers.Main.textApplyAll": "套用於所有方程式", "DE.Controllers.Main.textBuyNow": "瀏覽網站", "DE.Controllers.Main.textChangesSaved": "所有更改已儲存", "DE.Controllers.Main.textClose": "關閉", "DE.Controllers.Main.textCloseTip": "點擊以關閉提示", "DE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "DE.Controllers.Main.textContactUs": "聯繫銷售部門", "DE.Controllers.Main.textContinue": "繼續", "DE.Controllers.Main.textConvertEquation": "此方程式是使用不再支援的舊版方程式編輯器創建的。要編輯它，請將方程式轉換為 Office Math ML 格式。<br>立即轉換嗎？", "DE.Controllers.Main.textCustomLoader": "請注意，根據授權證書的權限，您沒有權限更改載入程式。請聯繫我們的銷售部門以獲取報價。", "DE.Controllers.Main.textDisconnect": "連線已中斷", "DE.Controllers.Main.textGuest": "訪客", "DE.Controllers.Main.textHasMacros": "該檔案包含自動巨集。<br>您是否要執行巨集？", "DE.Controllers.Main.textLearnMore": "了解更多", "DE.Controllers.Main.textLoadingDocument": "載入文件中", "DE.Controllers.Main.textLongName": "請輸入少於128個字元的名稱。", "DE.Controllers.Main.textNoLicenseTitle": "已達到授權人數限制", "DE.Controllers.Main.textPaidFeature": "付費功能", "DE.Controllers.Main.textReconnect": "連線已恢復", "DE.Controllers.Main.textRemember": "記住我對所有檔案的選擇", "DE.Controllers.Main.textRememberMacros": "記住我對所有巨集的選擇", "DE.Controllers.Main.textRenameError": "使用者名稱不能為空", "DE.Controllers.Main.textRenameLabel": "請輸入用於協作的名稱", "DE.Controllers.Main.textRequestMacros": "有一個巨集指令要求連結至URL。是否允許該要求至%1?", "DE.Controllers.Main.textShape": "形狀", "DE.Controllers.Main.textSignature": "Signature", "DE.Controllers.Main.textStrict": "嚴格模式", "DE.Controllers.Main.textText": "文字", "DE.Controllers.Main.textTryQuickPrint": "您選擇了快速列印：整個文件將會列印到最後一次選擇的或預設的印表機。<br>您要繼續嗎？", "DE.Controllers.Main.textTryUndoRedo": "快速共同編輯模式下無法使用復原/重做功能。<br>按一下「嚴格模式」按鈕切換到嚴格共同編輯模式，在不受其他使用者干擾的情況下編輯文件，並在保存更改後才發送。您可以使用編輯器的進階設定切換共同編輯模式。", "DE.Controllers.Main.textTryUndoRedoWarn": "在快速共同編輯模式下，復原/重做功能被禁用。", "DE.Controllers.Main.textUndo": "復原", "DE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "DE.Controllers.Main.textUpdating": "Updating", "DE.Controllers.Main.titleLicenseExp": "授權證書已過期", "DE.Controllers.Main.titleLicenseNotActive": "授權證書未啟用", "DE.Controllers.Main.titleServerVersion": "編輯器已更新", "DE.Controllers.Main.titleUpdateVersion": "版本已更改", "DE.Controllers.Main.txtAbove": "以上", "DE.Controllers.Main.txtArt": "在此輸入文字", "DE.Controllers.Main.txtBasicShapes": "基本圖案", "DE.Controllers.Main.txtBelow": "下方", "DE.Controllers.Main.txtBookmarkError": "錯誤！未定義的書籤。", "DE.Controllers.Main.txtButtons": "按鈕", "DE.Controllers.Main.txtCallouts": "圖說文字", "DE.Controllers.Main.txtCharts": "流程圖", "DE.Controllers.Main.txtChoose": "選擇項目", "DE.Controllers.Main.txtClickToLoad": "點擊以載入圖像", "DE.Controllers.Main.txtCurrentDocument": "目前文件", "DE.Controllers.Main.txtDiagramTitle": "圖表標題", "DE.Controllers.Main.txtEditingMode": "設定編輯模式...", "DE.Controllers.Main.txtEndOfFormula": "公式意外結束", "DE.Controllers.Main.txtEnterDate": "請輸入日期", "DE.Controllers.Main.txtErrorLoadHistory": "歷史載入失敗", "DE.Controllers.Main.txtEvenPage": "偶數頁", "DE.Controllers.Main.txtFiguredArrows": "箭號圖案", "DE.Controllers.Main.txtFirstPage": "首頁", "DE.Controllers.Main.txtFooter": "頁尾", "DE.Controllers.Main.txtFormulaNotInTable": "公式不在表格中", "DE.Controllers.Main.txtHeader": "頁首", "DE.Controllers.Main.txtHyperlink": "超連結", "DE.Controllers.Main.txtIndTooLarge": "索引太大", "DE.Controllers.Main.txtLines": "線數", "DE.Controllers.Main.txtMainDocOnly": "錯誤！僅限主文件。", "DE.Controllers.Main.txtMath": "方程式圖案", "DE.Controllers.Main.txtMissArg": "缺少參數", "DE.Controllers.Main.txtMissOperator": "缺少運算子", "DE.Controllers.Main.txtNeedSynchronize": "您有更新", "DE.Controllers.Main.txtNone": "無", "DE.Controllers.Main.txtNoTableOfContents": "文件中沒有任何標題。請對文字應用標題樣式，以便在目錄中顯示。", "DE.Controllers.Main.txtNoTableOfFigures": "找不到圖表目錄項目。", "DE.Controllers.Main.txtNoText": "錯誤！文件中沒有指定樣式的文字。", "DE.Controllers.Main.txtNotInTable": "不在表格中", "DE.Controllers.Main.txtNotValidBookmark": "錯誤！不是有效的書籤自我參照。", "DE.Controllers.Main.txtOddPage": "奇數頁", "DE.Controllers.Main.txtOnPage": "在頁面上", "DE.Controllers.Main.txtRectangles": "長方形", "DE.Controllers.Main.txtSameAsPrev": "與前一個相同", "DE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "DE.Controllers.Main.txtScheme_Aspect": "Aspect", "DE.Controllers.Main.txtScheme_Blue": "Blue", "DE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "DE.Controllers.Main.txtScheme_Blue_II": "Blue II", "DE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "DE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "DE.Controllers.Main.txtScheme_Green": "Green", "DE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "DE.Controllers.Main.txtScheme_Marquee": "Marquee", "DE.Controllers.Main.txtScheme_Median": "Median", "DE.Controllers.Main.txtScheme_Office": "Office", "DE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "DE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "DE.Controllers.Main.txtScheme_Orange": "Orange", "DE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "DE.Controllers.Main.txtScheme_Paper": "Paper", "DE.Controllers.Main.txtScheme_Red": "Red", "DE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "DE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "DE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "DE.Controllers.Main.txtScheme_Violet": "Violet", "DE.Controllers.Main.txtScheme_Violet_II": "Violet II", "DE.Controllers.Main.txtScheme_Yellow": "Yellow", "DE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "DE.Controllers.Main.txtSection": "-段落", "DE.Controllers.Main.txtSeries": "系列", "DE.Controllers.Main.txtShape_accentBorderCallout1": "線條標注 1（有邊框和重音線）", "DE.Controllers.Main.txtShape_accentBorderCallout2": "線條標注 2（有邊框和重音線）", "DE.Controllers.Main.txtShape_accentBorderCallout3": "線條標注 3（有邊框和重音線）", "DE.Controllers.Main.txtShape_accentCallout1": "線條標注 1（帶重音線）", "DE.Controllers.Main.txtShape_accentCallout2": "線條標注 2（帶重音線）", "DE.Controllers.Main.txtShape_accentCallout3": "線條標注 3（帶重音線）", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "返回或上一步按鈕", "DE.Controllers.Main.txtShape_actionButtonBeginning": "開始按鈕", "DE.Controllers.Main.txtShape_actionButtonBlank": "空白按鈕", "DE.Controllers.Main.txtShape_actionButtonDocument": "文件按鈕", "DE.Controllers.Main.txtShape_actionButtonEnd": "結束按鈕", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "前往下一步按鈕", "DE.Controllers.Main.txtShape_actionButtonHelp": "說明按鈕", "DE.Controllers.Main.txtShape_actionButtonHome": "首頁按鈕", "DE.Controllers.Main.txtShape_actionButtonInformation": "資訊按鈕", "DE.Controllers.Main.txtShape_actionButtonMovie": "電影按鈕", "DE.Controllers.Main.txtShape_actionButtonReturn": "返回按鈕", "DE.Controllers.Main.txtShape_actionButtonSound": "音效按鈕", "DE.Controllers.Main.txtShape_arc": "弧", "DE.Controllers.Main.txtShape_bentArrow": "彎曲箭頭", "DE.Controllers.Main.txtShape_bentConnector5": "彎曲連接器", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "彎曲箭頭連接器", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "彎曲雙箭頭連接器", "DE.Controllers.Main.txtShape_bentUpArrow": "向上彎曲箭頭", "DE.Controllers.Main.txtShape_bevel": "立體斜角", "DE.Controllers.Main.txtShape_blockArc": "區塊弧形", "DE.Controllers.Main.txtShape_borderCallout1": "線條標注 1", "DE.Controllers.Main.txtShape_borderCallout2": "線條標注 2", "DE.Controllers.Main.txtShape_borderCallout3": "線條標注 3", "DE.Controllers.Main.txtShape_bracePair": "雙大括號", "DE.Controllers.Main.txtShape_callout1": "線路標註1（無邊框）", "DE.Controllers.Main.txtShape_callout2": "線條標注 2（無邊框）", "DE.Controllers.Main.txtShape_callout3": "線條標注 3（無邊框）", "DE.Controllers.Main.txtShape_can": "罐狀", "DE.Controllers.Main.txtShape_chevron": "箭頭", "DE.Controllers.Main.txtShape_chord": "和弦", "DE.Controllers.Main.txtShape_circularArrow": "圓形箭頭", "DE.Controllers.Main.txtShape_cloud": "雲", "DE.Controllers.Main.txtShape_cloudCallout": "雲形註解", "DE.Controllers.Main.txtShape_corner": "角", "DE.Controllers.Main.txtShape_cube": "立方體", "DE.Controllers.Main.txtShape_curvedConnector3": "彎曲連接器", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "彎曲箭頭連接器", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "彎曲雙箭頭連接器", "DE.Controllers.Main.txtShape_curvedDownArrow": "彎曲向下箭頭", "DE.Controllers.Main.txtShape_curvedLeftArrow": "彎曲向左箭頭", "DE.Controllers.Main.txtShape_curvedRightArrow": "彎曲向右箭頭", "DE.Controllers.Main.txtShape_curvedUpArrow": "彎曲向上箭頭", "DE.Controllers.Main.txtShape_decagon": "十邊形", "DE.Controllers.Main.txtShape_diagStripe": "對角線條紋", "DE.Controllers.Main.txtShape_diamond": "菱形", "DE.Controllers.Main.txtShape_dodecagon": "十二邊形", "DE.Controllers.Main.txtShape_donut": "圓環圖", "DE.Controllers.Main.txtShape_doubleWave": "雙波浪線", "DE.Controllers.Main.txtShape_downArrow": "下箭頭", "DE.Controllers.Main.txtShape_downArrowCallout": "下箭頭標註", "DE.Controllers.Main.txtShape_ellipse": "橢圓", "DE.Controllers.Main.txtShape_ellipseRibbon": "彎曲向下緞帶", "DE.Controllers.Main.txtShape_ellipseRibbon2": "彎曲向上緞帶", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "流程圖：替代過程", "DE.Controllers.Main.txtShape_flowChartCollate": "流程圖：整理", "DE.Controllers.Main.txtShape_flowChartConnector": "流程圖：連接器", "DE.Controllers.Main.txtShape_flowChartDecision": "流程圖：決策", "DE.Controllers.Main.txtShape_flowChartDelay": "流程圖：延遲", "DE.Controllers.Main.txtShape_flowChartDisplay": "流程圖：顯示", "DE.Controllers.Main.txtShape_flowChartDocument": "流程圖：文件", "DE.Controllers.Main.txtShape_flowChartExtract": "流程圖：擷取", "DE.Controllers.Main.txtShape_flowChartInputOutput": "流程圖：資料", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "流程圖：內部儲存", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "流程圖：磁碟", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "流程圖：直接存取儲存", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "流程圖：順序存取儲存", "DE.Controllers.Main.txtShape_flowChartManualInput": "流程圖：手動輸入", "DE.Controllers.Main.txtShape_flowChartManualOperation": "流程圖：手動操作", "DE.Controllers.Main.txtShape_flowChartMerge": "流程圖：合併", "DE.Controllers.Main.txtShape_flowChartMultidocument": "流程圖：多文件", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "流程圖：頁面連接器", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "流程圖：儲存的資料", "DE.Controllers.Main.txtShape_flowChartOr": "流程圖：或", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "流程圖：預定義流程", "DE.Controllers.Main.txtShape_flowChartPreparation": "流程圖：準備", "DE.Controllers.Main.txtShape_flowChartProcess": "流程圖：流程", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "流程圖：卡片", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "流程圖：打孔紙帶", "DE.Controllers.Main.txtShape_flowChartSort": "流程圖：排序", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "流程圖：加總連結點", "DE.Controllers.Main.txtShape_flowChartTerminator": "流程圖：終止符號", "DE.Controllers.Main.txtShape_foldedCorner": "摺疊角落", "DE.Controllers.Main.txtShape_frame": "框線", "DE.Controllers.Main.txtShape_halfFrame": "半框架", "DE.Controllers.Main.txtShape_heart": "心形", "DE.Controllers.Main.txtShape_heptagon": "七邊形", "DE.Controllers.Main.txtShape_hexagon": "六邊形", "DE.Controllers.Main.txtShape_homePlate": "五邊形", "DE.Controllers.Main.txtShape_horizontalScroll": "水平捲動", "DE.Controllers.Main.txtShape_irregularSeal1": "爆炸效果1", "DE.Controllers.Main.txtShape_irregularSeal2": "爆炸效果2", "DE.Controllers.Main.txtShape_leftArrow": "向左箭頭", "DE.Controllers.Main.txtShape_leftArrowCallout": "向左箭頭標註", "DE.Controllers.Main.txtShape_leftBrace": "左大括號", "DE.Controllers.Main.txtShape_leftBracket": "左方括號", "DE.Controllers.Main.txtShape_leftRightArrow": "左右箭頭", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "左右箭頭標註", "DE.Controllers.Main.txtShape_leftRightUpArrow": "左右上箭頭", "DE.Controllers.Main.txtShape_leftUpArrow": "左上箭頭", "DE.Controllers.Main.txtShape_lightningBolt": "閃電符號", "DE.Controllers.Main.txtShape_line": "折線圖", "DE.Controllers.Main.txtShape_lineWithArrow": "箭頭", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "雙向箭頭", "DE.Controllers.Main.txtShape_mathDivide": "除法", "DE.Controllers.Main.txtShape_mathEqual": "等於", "DE.Controllers.Main.txtShape_mathMinus": "減號", "DE.Controllers.Main.txtShape_mathMultiply": "乘", "DE.Controllers.Main.txtShape_mathNotEqual": "不等於", "DE.Controllers.Main.txtShape_mathPlus": "加號", "DE.Controllers.Main.txtShape_moon": "月亮", "DE.Controllers.Main.txtShape_noSmoking": "\"否\"符號", "DE.Controllers.Main.txtShape_notchedRightArrow": "凹右箭頭", "DE.Controllers.Main.txtShape_octagon": "八邊形", "DE.Controllers.Main.txtShape_parallelogram": "平行四邊形", "DE.Controllers.Main.txtShape_pentagon": "五邊形", "DE.Controllers.Main.txtShape_pie": "圓餅圖", "DE.Controllers.Main.txtShape_plaque": "簽名", "DE.Controllers.Main.txtShape_plus": "加號", "DE.Controllers.Main.txtShape_polyline1": "塗鴉", "DE.Controllers.Main.txtShape_polyline2": "自由形狀", "DE.Controllers.Main.txtShape_quadArrow": "四向箭頭", "DE.Controllers.Main.txtShape_quadArrowCallout": "四向箭頭圖說", "DE.Controllers.Main.txtShape_rect": "長方形", "DE.Controllers.Main.txtShape_ribbon": "向下緞帶", "DE.Controllers.Main.txtShape_ribbon2": "上方功能區", "DE.Controllers.Main.txtShape_rightArrow": "右箭頭", "DE.Controllers.Main.txtShape_rightArrowCallout": "右箭頭註解", "DE.Controllers.Main.txtShape_rightBrace": "右大括號", "DE.Controllers.Main.txtShape_rightBracket": "右方括弧", "DE.Controllers.Main.txtShape_round1Rect": "圓角單邊矩形", "DE.Controllers.Main.txtShape_round2DiagRect": "圓斜角矩形", "DE.Controllers.Main.txtShape_round2SameRect": "圓角同邊矩形", "DE.Controllers.Main.txtShape_roundRect": "圓角矩形", "DE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "DE.Controllers.Main.txtShape_smileyFace": "笑臉符號", "DE.Controllers.Main.txtShape_snip1Rect": "剪下圓角單邊矩形", "DE.Controllers.Main.txtShape_snip2DiagRect": "剪下圓斜角矩形", "DE.Controllers.Main.txtShape_snip2SameRect": "剪下圓角同邊矩形", "DE.Controllers.Main.txtShape_snipRoundRect": "剪下圓角單邊矩形", "DE.Controllers.Main.txtShape_spline": "曲線", "DE.Controllers.Main.txtShape_star10": "十點星", "DE.Controllers.Main.txtShape_star12": "十二點星", "DE.Controllers.Main.txtShape_star16": "十六點星", "DE.Controllers.Main.txtShape_star24": "24點星", "DE.Controllers.Main.txtShape_star32": "32點星", "DE.Controllers.Main.txtShape_star4": "4點星", "DE.Controllers.Main.txtShape_star5": "5點星", "DE.Controllers.Main.txtShape_star6": "6點星", "DE.Controllers.Main.txtShape_star7": "7點星", "DE.Controllers.Main.txtShape_star8": "8點星", "DE.Controllers.Main.txtShape_stripedRightArrow": "條紋右箭頭", "DE.Controllers.Main.txtShape_sun": "太陽", "DE.Controllers.Main.txtShape_teardrop": "淚滴形狀", "DE.Controllers.Main.txtShape_textRect": "文字方塊", "DE.Controllers.Main.txtShape_trapezoid": "梯形", "DE.Controllers.Main.txtShape_triangle": "三角形", "DE.Controllers.Main.txtShape_upArrow": "向上箭頭", "DE.Controllers.Main.txtShape_upArrowCallout": "向上箭頭註解", "DE.Controllers.Main.txtShape_upDownArrow": "上下箭頭", "DE.Controllers.Main.txtShape_uturnArrow": "U 形箭頭", "DE.Controllers.Main.txtShape_verticalScroll": "垂直捲動", "DE.Controllers.Main.txtShape_wave": "波浪", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "橢圓形標注", "DE.Controllers.Main.txtShape_wedgeRectCallout": "矩形標註", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "圓角矩形標註", "DE.Controllers.Main.txtStarsRibbons": "星星和緞帶", "DE.Controllers.Main.txtStyle_Book_Title": "Book title", "DE.Controllers.Main.txtStyle_Caption": "標題", "DE.Controllers.Main.txtStyle_Default_Paragraph_Font": "Default paragraph font", "DE.Controllers.Main.txtStyle_Emphasis": "Emphasis", "DE.Controllers.Main.txtStyle_endnote_reference": "Endnote reference", "DE.Controllers.Main.txtStyle_endnote_text": "章節附註文字", "DE.Controllers.Main.txtStyle_footnote_reference": "Footnote reference", "DE.Controllers.Main.txtStyle_footnote_text": "註腳文字", "DE.Controllers.Main.txtStyle_Heading_1": "標題 1", "DE.Controllers.Main.txtStyle_Heading_2": "標題 2", "DE.Controllers.Main.txtStyle_Heading_3": "標題 3", "DE.Controllers.Main.txtStyle_Heading_4": "標題 4", "DE.Controllers.Main.txtStyle_Heading_5": "標題 5", "DE.Controllers.Main.txtStyle_Heading_6": "標題 6", "DE.Controllers.Main.txtStyle_Heading_7": "標題 7", "DE.Controllers.Main.txtStyle_Heading_8": "標題 8", "DE.Controllers.Main.txtStyle_Heading_9": "標題 9", "DE.Controllers.Main.txtStyle_Intense_Emphasis": "Intense emphasis", "DE.Controllers.Main.txtStyle_Intense_Quote": "強調引文", "DE.Controllers.Main.txtStyle_Intense_Reference": "Intense reference", "DE.Controllers.Main.txtStyle_List_Paragraph": "段落列表", "DE.Controllers.Main.txtStyle_No_List": "No list", "DE.Controllers.Main.txtStyle_No_Spacing": "無間距", "DE.Controllers.Main.txtStyle_Normal": "一般", "DE.Controllers.Main.txtStyle_Quote": "引用", "DE.Controllers.Main.txtStyle_Strong": "Strong", "DE.Controllers.Main.txtStyle_Subtitle": "副標題", "DE.Controllers.Main.txtStyle_Subtle_Emphasis": "Subtle emphasis", "DE.Controllers.Main.txtStyle_Subtle_Reference": "Subtle reference", "DE.Controllers.Main.txtStyle_Title": "標題", "DE.Controllers.Main.txtSyntaxError": "語法錯誤", "DE.Controllers.Main.txtTableInd": "表格索引不能為零", "DE.Controllers.Main.txtTableOfContents": "目錄", "DE.Controllers.Main.txtTableOfFigures": "圖表目錄", "DE.Controllers.Main.txtTOCHeading": "目錄標題", "DE.Controllers.Main.txtTooLarge": "數字太大而無法格式化", "DE.Controllers.Main.txtTypeEquation": "在此處輸入方程式。", "DE.Controllers.Main.txtUndefBookmark": "未定義的書籤", "DE.Controllers.Main.txtXAxis": "X軸", "DE.Controllers.Main.txtYAxis": "Y軸", "DE.Controllers.Main.txtZeroDivide": "零分度", "DE.Controllers.Main.unknownErrorText": "未知錯誤。", "DE.Controllers.Main.unsupportedBrowserErrorText": "不支援您的瀏覽器", "DE.Controllers.Main.uploadDocExtMessage": "未知文件格式。", "DE.Controllers.Main.uploadDocFileCountMessage": "沒有上傳文件。", "DE.Controllers.Main.uploadDocSizeMessage": "超出最大文檔大小限制。", "DE.Controllers.Main.uploadImageExtMessage": "未知圖片格式。", "DE.Controllers.Main.uploadImageFileCountMessage": "沒有上傳圖片。", "DE.Controllers.Main.uploadImageSizeMessage": "圖片超出最大大小限制。最大大小為25MB。", "DE.Controllers.Main.uploadImageTextText": "正在上傳圖片...", "DE.Controllers.Main.uploadImageTitleText": "正在上傳圖片", "DE.Controllers.Main.waitText": "請稍候...", "DE.Controllers.Main.warnBrowserIE9": "該應用程式在IE9上的功能有限。請使用IE10或更高版本", "DE.Controllers.Main.warnBrowserZoom": "您的瀏覽器目前的縮放設定不完全支援。請按Ctrl+0重設為預設縮放。", "DE.Controllers.Main.warnLicenseAnonymous": "匿名使用者無法存取。<br>此文件將僅供檢視。", "DE.Controllers.Main.warnLicenseBefore": "授權證書未啟用。請聯繫您的管理員。", "DE.Controllers.Main.warnLicenseExceeded": "您的系統已經達到同時編輯連線的 %1 編輯器。只能以檢視模式開啟此文件。<br>欲得知進一步訊息, 請聯絡您的帳號管理者。", "DE.Controllers.Main.warnLicenseExp": "您的授權已過期。<br>請更新您的授權並重新整理頁面。", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "授權過期<br>您已沒有編輯文件功能的授權<br> 請與您的管理者聯繫。", "DE.Controllers.Main.warnLicenseLimitedRenewed": "授權證書已過期。因此無法編輯文件。請聯繫您的管理員。", "DE.Controllers.Main.warnLicenseUsersExceeded": "您已達到％1個編輯器限制。請聯絡你的帳號管理員以了解更多資訊。", "DE.Controllers.Main.warnNoLicense": "您已達到同時連線 %1 編輯者的限制。此文件將僅以檢視模式開啟。", "DE.Controllers.Main.warnNoLicenseUsers": "您已達到編輯器的使用者限制。", "DE.Controllers.Main.warnProcessRightsChange": "您被拒絕了編輯文件的權限。", "DE.Controllers.Main.warnStartFilling": "Form filling is in progress.<br>File editing is not currently available.", "DE.Controllers.Navigation.txtBeginning": "文件開頭", "DE.Controllers.Navigation.txtGotoBeginning": "前往文件開頭", "DE.Controllers.Print.textMarginsLast": "最後自訂", "DE.Controllers.Print.txtCustom": "自訂", "DE.Controllers.Print.txtPrintRangeInvalid": "無效的列印範圍", "DE.Controllers.Print.txtPrintRangeSingleRange": "請輸入單一頁面編號或單一頁面範圍（例如，5-12）。或者您可以將其列印成PDF。", "DE.Controllers.Search.notcriticalErrorTitle": "警告", "DE.Controllers.Search.textNoTextFound": "找不到您正在搜尋的資料。請調整您的搜尋選項。", "DE.Controllers.Search.textReplaceSkipped": "替換已完成。有 {0} 個項目被跳過。", "DE.Controllers.Search.textReplaceSuccess": "搜尋完成。 {0}個符合結果已被取代", "DE.Controllers.Search.warnReplaceString": "{0}不是有效的字元", "DE.Controllers.Statusbar.textDisconnect": "<b>連線失敗</b><br>正在嘗試連線。請檢查網路連線設定。", "DE.Controllers.Statusbar.textHasChanges": "已追蹤新變更", "DE.Controllers.Statusbar.textSetTrackChanges": "您正在「追蹤修訂」模式下", "DE.Controllers.Statusbar.textTrackChanges": "以「追蹤修訂」模式開啟文件", "DE.Controllers.Statusbar.tipReview": "追蹤變更", "DE.Controllers.Statusbar.zoomText": "縮放至{0}%", "DE.Controllers.Toolbar.confirmAddFontName": "您即將保存的字體在當前設備上不可用。<br>該文字樣式將使用其中一種系統字體顯示，而在字體可用時將使用保存的字體。<br>您是否要繼續？", "DE.Controllers.Toolbar.dataUrl": "貼上資料URL", "DE.Controllers.Toolbar.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "DE.Controllers.Toolbar.fileUrl": "Paste a file URL", "DE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "DE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "DE.Controllers.Toolbar.helpRtlDir": "Toggle between right-to-left (RTL) and left-to-right (LTR) text direction in  your documents.", "DE.Controllers.Toolbar.helpRtlDirHeader": "Switch text direction", "DE.Controllers.Toolbar.notcriticalErrorTitle": "警告", "DE.Controllers.Toolbar.textAccent": "口音", "DE.Controllers.Toolbar.textBracket": "括號", "DE.Controllers.Toolbar.textConvertFormDownload": "Download file as a fillable PDF form to be able to fill it out.", "DE.Controllers.Toolbar.textConvertFormSave": "Save file as a fillable PDF form to be able to fill it out.", "DE.Controllers.Toolbar.textDownloadPdf": "Download PDF", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "您需要指定的網址。", "DE.Controllers.Toolbar.textFieldExample": "Example of writing code: TIM<PERSON> \\@ \"dddd, MMMM d, yyyy\"", "DE.Controllers.Toolbar.textFieldLabel": "Field codes", "DE.Controllers.Toolbar.textFieldTitle": "Field", "DE.Controllers.Toolbar.textFontSizeErr": "輸入的值不正確。<br>請輸入介於1和300之間的數值。", "DE.Controllers.Toolbar.textFraction": "分數", "DE.Controllers.Toolbar.textFunction": "函數", "DE.Controllers.Toolbar.textGroup": "群組", "DE.Controllers.Toolbar.textInsert": "插入", "DE.Controllers.Toolbar.textIntegral": "積分符號", "DE.Controllers.Toolbar.textLargeOperator": "大型運算符", "DE.Controllers.Toolbar.textLimitAndLog": "極限和對數", "DE.Controllers.Toolbar.textMatrix": "矩陣", "DE.Controllers.Toolbar.textOperator": "運算符", "DE.Controllers.Toolbar.textRadical": "根式", "DE.Controllers.Toolbar.textRecentlyUsed": "最近使用", "DE.Controllers.Toolbar.textSavePdf": "Save as PDF", "DE.Controllers.Toolbar.textScript": "腳本", "DE.Controllers.Toolbar.textSymbols": "符號", "DE.Controllers.Toolbar.textTabForms": "表單", "DE.Controllers.Toolbar.textWarning": "警告", "DE.Controllers.Toolbar.txtAccent_Accent": "尖銳", "DE.Controllers.Toolbar.txtAccent_ArrowD": "上方左右箭頭", "DE.Controllers.Toolbar.txtAccent_ArrowL": "上方左箭頭", "DE.Controllers.Toolbar.txtAccent_ArrowR": "上方向右箭頭", "DE.Controllers.Toolbar.txtAccent_Bar": "長條圖", "DE.Controllers.Toolbar.txtAccent_BarBot": "底線", "DE.Controllers.Toolbar.txtAccent_BarTop": "橫槓", "DE.Controllers.Toolbar.txtAccent_BorderBox": "有方框的公式（包含佔位符）", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "帶框公式（範例）", "DE.Controllers.Toolbar.txtAccent_Check": "勾選", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "底括號", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "上大括號", "DE.Controllers.Toolbar.txtAccent_Custom_1": "向量A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "帶橫線的ABC", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y帶有上橫線", "DE.Controllers.Toolbar.txtAccent_DDDot": "三點", "DE.Controllers.Toolbar.txtAccent_DDot": "雙點", "DE.Controllers.Toolbar.txtAccent_Dot": "點", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "雙上橫槓", "DE.Controllers.Toolbar.txtAccent_Grave": "重音符號", "DE.Controllers.Toolbar.txtAccent_GroupBot": "下方分組字元", "DE.Controllers.Toolbar.txtAccent_GroupTop": "上方分組字元", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "上方左矢", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "上方右箭頭", "DE.Controllers.Toolbar.txtAccent_Hat": "帽子", "DE.Controllers.Toolbar.txtAccent_Smile": "短音符", "DE.Controllers.Toolbar.txtAccent_Tilde": "波浪號", "DE.Controllers.Toolbar.txtBracket_Angle": "角括號", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "帶分隔符的角括號", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "帶兩個分隔符的角括號", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_Curve": "大括號", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "帶分隔符的大括號", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_Custom_1": "情況（兩個條件）", "DE.Controllers.Toolbar.txtBracket_Custom_2": "情況（三個條件）", "DE.Controllers.Toolbar.txtBracket_Custom_3": "堆疊物件", "DE.Controllers.Toolbar.txtBracket_Custom_4": "堆疊物件", "DE.Controllers.Toolbar.txtBracket_Custom_5": "情況範例", "DE.Controllers.Toolbar.txtBracket_Custom_6": "二項式係數", "DE.Controllers.Toolbar.txtBracket_Custom_7": "帶有角括號的二項式係數", "DE.Controllers.Toolbar.txtBracket_Line": "括號", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_LineDouble": "雙豎線", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_LowLim": "底部整數", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "單括號", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_Round": "括號", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "帶分隔符的括號", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_Square": "括號", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "括號", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "括號", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "括號", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "雙方括號", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_UppLim": "天花板", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "單括號", "DE.Controllers.Toolbar.txtDownload": "Download", "DE.Controllers.Toolbar.txtFractionDiagonal": "斜向分數", "DE.Controllers.Toolbar.txtFractionDifferential_1": "dx 除以 dy", "DE.Controllers.Toolbar.txtFractionDifferential_2": "Δy 除以 Δx 的大寫Δ", "DE.Controllers.Toolbar.txtFractionDifferential_3": "微分", "DE.Controllers.Toolbar.txtFractionDifferential_4": "Δx 除以 Δy", "DE.Controllers.Toolbar.txtFractionHorizontal": "線性分數", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi除以2", "DE.Controllers.Toolbar.txtFractionSmall": "小分數", "DE.Controllers.Toolbar.txtFractionVertical": "堆積分數", "DE.Controllers.Toolbar.txtFunction_1_Cos": "反餘弦函數", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "雙曲反餘弦函數", "DE.Controllers.Toolbar.txtFunction_1_Cot": "反餘切函數", "DE.Controllers.Toolbar.txtFunction_1_Coth": "雙曲反餘切函數", "DE.Controllers.Toolbar.txtFunction_1_Csc": "反餘割函數", "DE.Controllers.Toolbar.txtFunction_1_Csch": "雙曲反餘割函數", "DE.Controllers.Toolbar.txtFunction_1_Sec": "反正割函數", "DE.Controllers.Toolbar.txtFunction_1_Sech": "雙曲反正割函數", "DE.Controllers.Toolbar.txtFunction_1_Sin": "反正弦函數", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "雙曲反正弦函數", "DE.Controllers.Toolbar.txtFunction_1_Tan": "反正切函數", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "雙曲反正切函數", "DE.Controllers.Toolbar.txtFunction_Cos": "餘弦函數", "DE.Controllers.Toolbar.txtFunction_Cosh": "雙曲餘弦函數", "DE.Controllers.Toolbar.txtFunction_Cot": "余切函數", "DE.Controllers.Toolbar.txtFunction_Coth": "雙曲餘切函數", "DE.Controllers.Toolbar.txtFunction_Csc": "余割函數", "DE.Controllers.Toolbar.txtFunction_Csch": "雙曲餘割函數", "DE.Controllers.Toolbar.txtFunction_Custom_1": "正弦θ", "DE.Controllers.Toolbar.txtFunction_Custom_2": "cos2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "正切公式", "DE.Controllers.Toolbar.txtFunction_Sec": "正割函數", "DE.Controllers.Toolbar.txtFunction_Sech": "雙曲正割函數", "DE.Controllers.Toolbar.txtFunction_Sin": "正弦函數", "DE.Controllers.Toolbar.txtFunction_Sinh": "雙曲正弦函數", "DE.Controllers.Toolbar.txtFunction_Tan": "正切函數", "DE.Controllers.Toolbar.txtFunction_Tanh": "雙曲正切函數", "DE.Controllers.Toolbar.txtIntegral": "積分", "DE.Controllers.Toolbar.txtIntegral_dtheta": "微分θ", "DE.Controllers.Toolbar.txtIntegral_dx": "微分 x", "DE.Controllers.Toolbar.txtIntegral_dy": "微分 y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "積分", "DE.Controllers.Toolbar.txtIntegralDouble": "雙重積分", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "帶堆疊限制的雙重積分", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "帶限制的雙重積分", "DE.Controllers.Toolbar.txtIntegralOriented": "等高線積分", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "帶堆疊限制的等高積分", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "曲面積分", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "表面積分", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "表面積分", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "帶限制的等高積分", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "體積積分", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "體積積分", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "體積積分", "DE.Controllers.Toolbar.txtIntegralSubSup": "積分", "DE.Controllers.Toolbar.txtIntegralTriple": "三重積分", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "三重積分", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "三重積分", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "楔", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "楔", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "楔", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "楔", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "楔", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "共積", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "帶下限的共積", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "帶限制的共積", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "帶有下標下限的共積", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "帶有上下標限制的共積", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "產品", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "聯合", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "邏輯運算子Or", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "邏輯運算子Or", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "邏輯運算子Or", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "邏輯運算子Or", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "交集", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "交叉點", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "交叉點", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "交叉點", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "交叉點", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "乘積", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "產品", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "產品", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "產品", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "產品", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "總和", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Union": "聯合", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "聯合", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "聯合", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "聯合", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "聯合", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "限制例子", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大範例", "DE.Controllers.Toolbar.txtLimitLog_Lim": "限制", "DE.Controllers.Toolbar.txtLimitLog_Ln": "自然對數", "DE.Controllers.Toolbar.txtLimitLog_Log": "對數", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "對數", "DE.Controllers.Toolbar.txtLimitLog_Max": "最大值", "DE.Controllers.Toolbar.txtLimitLog_Min": "最小值", "DE.Controllers.Toolbar.txtMarginsH": "頂部和底部的邊界對於給定的頁面高度太高了", "DE.Controllers.Toolbar.txtMarginsW": "給定頁面寬度下，左右邊界過寬", "DE.Controllers.Toolbar.txtMatrix_1_2": "1x2 空矩陣", "DE.Controllers.Toolbar.txtMatrix_1_3": "1x3 空矩陣", "DE.Controllers.Toolbar.txtMatrix_2_1": "2x1 空矩陣", "DE.Controllers.Toolbar.txtMatrix_2_2": "2x2 空矩陣", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "以雙豎線表示的空的 2x2 矩陣", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "空的 2x2 行列式", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "以括弧表示的空的 2x2 矩陣", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "以括號表示的空的 2x2 矩陣", "DE.Controllers.Toolbar.txtMatrix_2_3": "2x3 空矩陣", "DE.Controllers.Toolbar.txtMatrix_3_1": "3x1 空矩陣", "DE.Controllers.Toolbar.txtMatrix_3_2": "3x2 空矩陣", "DE.Controllers.Toolbar.txtMatrix_3_3": "3x3 空矩陣", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "基線點", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "中線點", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "對角點", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "垂直點", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "稀疏矩陣", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "稀疏矩陣", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 除了對角線以外都是零的單位矩陣", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2 除了對角線以外都是空白的單位矩陣", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 除了對角線以外都是零的單位矩陣", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 除了對角線以外都是空白的單位矩陣", "DE.Controllers.Toolbar.txtNeedDownload": "PDF viewer can only save new changes in separate file copies. It doesn't support co-editing and other users won't see your changes unless you share a new file version.", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "下方左右箭頭", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "上方左右箭頭", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "下方左箭頭", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "上方左箭頭", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "下方向右箭頭", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "上方向右箭頭", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "冒號等號", "DE.Controllers.Toolbar.txtOperator_Custom_1": "產生", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Delta 收益", "DE.Controllers.Toolbar.txtOperator_Definition": "按照定義相等", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta 等於", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "下方的左右箭頭", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "上方的左右箭頭", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "下方左箭頭", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "上方左箭頭", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "下方向右箭頭", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "上方向右箭頭", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "等於 等於", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "負等於", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "加等於", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "由...測量", "DE.Controllers.Toolbar.txtRadicalCustom_1": "根號", "DE.Controllers.Toolbar.txtRadicalCustom_2": "根號", "DE.Controllers.Toolbar.txtRadicalRoot_2": "帶次數的平方根", "DE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "DE.Controllers.Toolbar.txtRadicalRoot_n": "帶有次數的根號符號", "DE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "DE.Controllers.Toolbar.txtSaveCopy": "Save copy", "DE.Controllers.Toolbar.txtScriptCustom_1": "腳本", "DE.Controllers.Toolbar.txtScriptCustom_2": "e 的負 i omega t 次方", "DE.Controllers.Toolbar.txtScriptCustom_3": "腳本", "DE.Controllers.Toolbar.txtScriptCustom_4": "腳本", "DE.Controllers.Toolbar.txtScriptSub": "下標", "DE.Controllers.Toolbar.txtScriptSubSup": "上下標", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "左下標-上標", "DE.Controllers.Toolbar.txtScriptSup": "上標", "DE.Controllers.Toolbar.txtSymbol_about": "大約", "DE.Controllers.Toolbar.txtSymbol_additional": "補集", "DE.Controllers.Toolbar.txtSymbol_aleph": "Aleph", "DE.Controllers.Toolbar.txtSymbol_alpha": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_approx": "Approx", "DE.Controllers.Toolbar.txtSymbol_ast": "星號運算子", "DE.Controllers.Toolbar.txtSymbol_beta": "測試版", "DE.Controllers.Toolbar.txtSymbol_beth": "賭注", "DE.Controllers.Toolbar.txtSymbol_bullet": "點運算子", "DE.Controllers.Toolbar.txtSymbol_cap": "交集", "DE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "DE.Controllers.Toolbar.txtSymbol_cdots": "中線水平省略號", "DE.Controllers.Toolbar.txtSymbol_celsius": "攝氏度", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "大約等於", "DE.Controllers.Toolbar.txtSymbol_cup": "聯合", "DE.Controllers.Toolbar.txtSymbol_ddots": "向右下對角省略號", "DE.Controllers.Toolbar.txtSymbol_degree": "度", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta 等於", "DE.Controllers.Toolbar.txtSymbol_div": "除號", "DE.Controllers.Toolbar.txtSymbol_downarrow": "向下箭頭", "DE.Controllers.Toolbar.txtSymbol_emptyset": "空集合", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "DE.Controllers.Toolbar.txtSymbol_equals": "等於", "DE.Controllers.Toolbar.txtSymbol_equiv": "相同於", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "存在", "DE.Controllers.Toolbar.txtSymbol_factorial": "階乘", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "華氏度", "DE.Controllers.Toolbar.txtSymbol_forall": "適用於全部", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "DE.Controllers.Toolbar.txtSymbol_geq": "Geq", "DE.Controllers.Toolbar.txtSymbol_gg": "遠大於", "DE.Controllers.Toolbar.txtSymbol_greater": "大於等於", "DE.Controllers.Toolbar.txtSymbol_in": "元素", "DE.Controllers.Toolbar.txtSymbol_inc": "增量", "DE.Controllers.Toolbar.txtSymbol_infinity": "無窮大", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "向左箭頭", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右箭頭", "DE.Controllers.Toolbar.txtSymbol_leq": "小於或等於", "DE.Controllers.Toolbar.txtSymbol_less": "小於", "DE.Controllers.Toolbar.txtSymbol_ll": "遠小於", "DE.Controllers.Toolbar.txtSymbol_minus": "減號", "DE.Controllers.Toolbar.txtSymbol_mp": "減加號", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "不等於", "DE.Controllers.Toolbar.txtSymbol_ni": "包含為成員", "DE.Controllers.Toolbar.txtSymbol_not": "沒有簽名", "DE.Controllers.Toolbar.txtSymbol_notexists": "不存在", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Ο (歐米克戎)", "DE.Controllers.Toolbar.txtSymbol_omega": "Ω (歐米茄)", "DE.Controllers.Toolbar.txtSymbol_partial": "偏微分", "DE.Controllers.Toolbar.txtSymbol_percent": "百分比", "DE.Controllers.Toolbar.txtSymbol_phi": "Phi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "加號", "DE.Controllers.Toolbar.txtSymbol_pm": "加減號", "DE.Controllers.Toolbar.txtSymbol_propto": "比例縮放為", "DE.Controllers.Toolbar.txtSymbol_psi": "Ψ（希臘字母普西）", "DE.Controllers.Toolbar.txtSymbol_qdrt": "四次根", "DE.Controllers.Toolbar.txtSymbol_qed": "證明結束", "DE.Controllers.Toolbar.txtSymbol_rddots": "右上對角省略符號", "DE.Controllers.Toolbar.txtSymbol_rho": "希臘字母\"ρ\"", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "右箭頭", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "根號符號", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "因此", "DE.Controllers.Toolbar.txtSymbol_theta": "Theta", "DE.Controllers.Toolbar.txtSymbol_times": "乘法符號", "DE.Controllers.Toolbar.txtSymbol_uparrow": "向上箭頭", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon變體", "DE.Controllers.Toolbar.txtSymbol_varphi": "Phi 變體", "DE.Controllers.Toolbar.txtSymbol_varpi": "Pi變體", "DE.Controllers.Toolbar.txtSymbol_varrho": "Rho變體", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma 變體", "DE.Controllers.Toolbar.txtSymbol_vartheta": "Θ 變體", "DE.Controllers.Toolbar.txtSymbol_vdots": "垂直省略號", "DE.Controllers.Toolbar.txtSymbol_xsi": "希臘字母\"Ξ\"", "DE.Controllers.Toolbar.txtSymbol_zeta": "ζ", "DE.Controllers.Toolbar.txtUntitled": "Untitled", "DE.Controllers.Viewport.textFitPage": "調整至頁面大小", "DE.Controllers.Viewport.textFitWidth": "調整至寬度大小", "DE.Controllers.Viewport.txtDarkMode": "深色模式", "DE.Views.BookmarksDialog.textAdd": "新增", "DE.Views.BookmarksDialog.textBookmarkName": "書籤名稱", "DE.Views.BookmarksDialog.textClose": "關閉", "DE.Views.BookmarksDialog.textCopy": "複製", "DE.Views.BookmarksDialog.textDelete": "刪除", "DE.Views.BookmarksDialog.textGetLink": "取得連結", "DE.Views.BookmarksDialog.textGoto": "前往", "DE.Views.BookmarksDialog.textHidden": "隱藏書籤", "DE.Views.BookmarksDialog.textLocation": "位置", "DE.Views.BookmarksDialog.textName": "名稱", "DE.Views.BookmarksDialog.textSort": "排序方式", "DE.Views.BookmarksDialog.textTitle": "書籤", "DE.Views.BookmarksDialog.txtInvalidName": "書籤名稱只能包含字母、數字和底線，且必須以字母開頭", "DE.Views.CaptionDialog.textAdd": "新增標籤", "DE.Views.CaptionDialog.textAfter": "之後", "DE.Views.CaptionDialog.textBefore": "之前", "DE.Views.CaptionDialog.textCaption": "標題", "DE.Views.CaptionDialog.textChapter": "章節起始樣式", "DE.Views.CaptionDialog.textChapterInc": "包括章節編號", "DE.Views.CaptionDialog.textColon": "冒號", "DE.Views.CaptionDialog.textDash": "虛線", "DE.Views.CaptionDialog.textDelete": "刪除標籤", "DE.Views.CaptionDialog.textEquation": "方程式", "DE.Views.CaptionDialog.textExamples": "範例：表格2-A，圖片1.IV", "DE.Views.CaptionDialog.textExclude": "排除標籤以外的說明文字", "DE.Views.CaptionDialog.textFigure": "圖表", "DE.Views.CaptionDialog.textHyphen": "連字號", "DE.Views.CaptionDialog.textInsert": "插入", "DE.Views.CaptionDialog.textLabel": "標籤", "DE.Views.CaptionDialog.textLabelError": "Label must not be empty.", "DE.Views.CaptionDialog.textLongDash": "長破折號", "DE.Views.CaptionDialog.textNumbering": "編號", "DE.Views.CaptionDialog.textPeriod": "區間", "DE.Views.CaptionDialog.textSeparator": "使用分隔符", "DE.Views.CaptionDialog.textTable": "表格", "DE.Views.CaptionDialog.textTitle": "插入標題", "DE.Views.CellsAddDialog.textCol": "欄", "DE.Views.CellsAddDialog.textDown": "游標下方", "DE.Views.CellsAddDialog.textLeft": "向左", "DE.Views.CellsAddDialog.textRight": "向右", "DE.Views.CellsAddDialog.textRow": "列", "DE.Views.CellsAddDialog.textTitle": "插入多個", "DE.Views.CellsAddDialog.textUp": "游標上方", "DE.Views.ChartSettings.text3dDepth": "深度（相對於基底的百分比）", "DE.Views.ChartSettings.text3dHeight": "高度（基礎的百分比）", "DE.Views.ChartSettings.text3dRotation": "3D 旋轉", "DE.Views.ChartSettings.textAdvanced": "顯示進階設定", "DE.Views.ChartSettings.textAutoscale": "自動調整比例", "DE.Views.ChartSettings.textChartType": "變更圖表類型", "DE.Views.ChartSettings.textDefault": "預設旋轉", "DE.Views.ChartSettings.textDown": "下", "DE.Views.ChartSettings.textEditData": "編輯資料", "DE.Views.ChartSettings.textHeight": "\n高度", "DE.Views.ChartSettings.textLeft": "左", "DE.Views.ChartSettings.textNarrow": "視野狹窄", "DE.Views.ChartSettings.textOriginalSize": "實際大小", "DE.Views.ChartSettings.textPerspective": "透視", "DE.Views.ChartSettings.textRight": "右", "DE.Views.ChartSettings.textRightAngle": "直角座標軸", "DE.Views.ChartSettings.textSize": "大小", "DE.Views.ChartSettings.textStyle": "樣式", "DE.Views.ChartSettings.textUndock": "取消固定於面板", "DE.Views.ChartSettings.textUp": "向上", "DE.Views.ChartSettings.textWiden": "擴大視野", "DE.Views.ChartSettings.textWidth": "寬度", "DE.Views.ChartSettings.textWrap": "換行樣式", "DE.Views.ChartSettings.textX": "X旋轉", "DE.Views.ChartSettings.textY": "Y旋轉", "DE.Views.ChartSettings.txtBehind": "文字在後", "DE.Views.ChartSettings.txtInFront": "置於文字前方", "DE.Views.ChartSettings.txtInline": "與文字對齊", "DE.Views.ChartSettings.txtSquare": "方形", "DE.Views.ChartSettings.txtThrough": "透過", "DE.Views.ChartSettings.txtTight": "緊密", "DE.Views.ChartSettings.txtTitle": "圖表", "DE.Views.ChartSettings.txtTopAndBottom": "頂部和底部", "DE.Views.ControlSettingsDialog.strGeneral": "一般", "DE.Views.ControlSettingsDialog.textAdd": "新增", "DE.Views.ControlSettingsDialog.textAppearance": "外觀", "DE.Views.ControlSettingsDialog.textApplyAll": "套用至所有", "DE.Views.ControlSettingsDialog.textBox": "邊界框", "DE.Views.ControlSettingsDialog.textChange": "編輯", "DE.Views.ControlSettingsDialog.textCheckbox": "核取方塊", "DE.Views.ControlSettingsDialog.textChecked": "勾選符號", "DE.Views.ControlSettingsDialog.textColor": "顏色", "DE.Views.ControlSettingsDialog.textCombobox": "下拉式方框", "DE.Views.ControlSettingsDialog.textDate": "日期格式", "DE.Views.ControlSettingsDialog.textDelete": "刪除", "DE.Views.ControlSettingsDialog.textDisplayName": "顯示名稱", "DE.Views.ControlSettingsDialog.textDown": "下", "DE.Views.ControlSettingsDialog.textDropDown": "下拉式清單", "DE.Views.ControlSettingsDialog.textFormat": "以這種方式顯示日期", "DE.Views.ControlSettingsDialog.textLang": "語言", "DE.Views.ControlSettingsDialog.textLock": "鎖定中", "DE.Views.ControlSettingsDialog.textName": "標題", "DE.Views.ControlSettingsDialog.textNone": "無", "DE.Views.ControlSettingsDialog.textPlaceholder": "佔位符", "DE.Views.ControlSettingsDialog.textShowAs": "顯示為", "DE.Views.ControlSettingsDialog.textSystemColor": "系統", "DE.Views.ControlSettingsDialog.textTag": "標籤", "DE.Views.ControlSettingsDialog.textTitle": "內容控制設定", "DE.Views.ControlSettingsDialog.textUnchecked": "未勾選符號", "DE.Views.ControlSettingsDialog.textUp": "向上", "DE.Views.ControlSettingsDialog.textValue": "值", "DE.Views.ControlSettingsDialog.tipChange": "變更符號", "DE.Views.ControlSettingsDialog.txtLockDelete": "無法刪除內容控制", "DE.Views.ControlSettingsDialog.txtLockEdit": "無法編輯內容", "DE.Views.ControlSettingsDialog.txtRemContent": "Remove content control when contents are edited", "DE.Views.CrossReferenceDialog.textAboveBelow": "上/下", "DE.Views.CrossReferenceDialog.textBookmark": "書籤", "DE.Views.CrossReferenceDialog.textBookmarkText": "書籤文字", "DE.Views.CrossReferenceDialog.textCaption": "完整的標題", "DE.Views.CrossReferenceDialog.textEmpty": "請求參考為空", "DE.Views.CrossReferenceDialog.textEndnote": "註腳", "DE.Views.CrossReferenceDialog.textEndNoteNum": "註腳編號", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "註腳編號（格式化）", "DE.Views.CrossReferenceDialog.textEquation": "方程式", "DE.Views.CrossReferenceDialog.textFigure": "圖表", "DE.Views.CrossReferenceDialog.textFootnote": "註腳", "DE.Views.CrossReferenceDialog.textHeading": "標題", "DE.Views.CrossReferenceDialog.textHeadingNum": "標題編號", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "完整內容的標題編號", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "無內容的標題編號", "DE.Views.CrossReferenceDialog.textHeadingText": "標題文字", "DE.Views.CrossReferenceDialog.textIncludeAbove": "包括上方/下方", "DE.Views.CrossReferenceDialog.textInsert": "插入", "DE.Views.CrossReferenceDialog.textInsertAs": "插入超連結", "DE.Views.CrossReferenceDialog.textLabelNum": "僅標籤和編號", "DE.Views.CrossReferenceDialog.textNoteNum": "註腳編號", "DE.Views.CrossReferenceDialog.textNoteNumForm": "註腳編號（格式化）", "DE.Views.CrossReferenceDialog.textOnlyCaption": "僅標題文字", "DE.Views.CrossReferenceDialog.textPageNum": "頁碼", "DE.Views.CrossReferenceDialog.textParagraph": "編號項目", "DE.Views.CrossReferenceDialog.textParaNum": "段落編號", "DE.Views.CrossReferenceDialog.textParaNumFull": "段落編號（完整內容）", "DE.Views.CrossReferenceDialog.textParaNumNo": "段落編號（無內容）", "DE.Views.CrossReferenceDialog.textSeparate": "用...分隔數字", "DE.Views.CrossReferenceDialog.textTable": "表格", "DE.Views.CrossReferenceDialog.textText": "段落文字", "DE.Views.CrossReferenceDialog.textWhich": "對於哪個標題", "DE.Views.CrossReferenceDialog.textWhichBookmark": "對於哪個書籤", "DE.Views.CrossReferenceDialog.textWhichEndnote": "對應哪個章節附註", "DE.Views.CrossReferenceDialog.textWhichHeading": "對於哪個標題", "DE.Views.CrossReferenceDialog.textWhichNote": "對應哪個註腳", "DE.Views.CrossReferenceDialog.textWhichPara": "對於哪個編號項目", "DE.Views.CrossReferenceDialog.txtReference": "插入參照至", "DE.Views.CrossReferenceDialog.txtTitle": "交叉引用", "DE.Views.CrossReferenceDialog.txtType": "參考類型", "DE.Views.CustomColumnsDialog.textColumns": "欄數", "DE.Views.CustomColumnsDialog.textEqualWidth": "等寬欄寬", "DE.Views.CustomColumnsDialog.textSeparator": "欄分隔線", "DE.Views.CustomColumnsDialog.textTitle": "欄", "DE.Views.CustomColumnsDialog.textTitleSpacing": "間距", "DE.Views.CustomColumnsDialog.textWidth": "寬度", "DE.Views.DateTimeDialog.confirmDefault": "設定 {0} 的預設格式為 \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "設為預設值", "DE.Views.DateTimeDialog.textFormat": "格式", "DE.Views.DateTimeDialog.textLang": "語言", "DE.Views.DateTimeDialog.textUpdate": "自動更新", "DE.Views.DateTimeDialog.txtTitle": "日期和時間", "DE.Views.DocProtection.hintProtectDoc": "受保護的文件", "DE.Views.DocProtection.txtDocProtectedComment": "文件已受保護。<br>您只能在此文件中插入註解。", "DE.Views.DocProtection.txtDocProtectedForms": "文件已受保護。<br>您只能填寫此文件中的表單。", "DE.Views.DocProtection.txtDocProtectedTrack": "文件已受保護。<br>您可以編輯此文件，但所有更改都將被追蹤。", "DE.Views.DocProtection.txtDocProtectedView": "文件已受保護。<br>您只能查看此文件。", "DE.Views.DocProtection.txtDocUnlockDescription": "請輸入用於解除保護文件的密碼", "DE.Views.DocProtection.txtProtectDoc": "受保護的文件", "DE.Views.DocProtection.txtUnlockTitle": "解除保護文件", "DE.Views.DocumentHolder.aboveText": "以上", "DE.Views.DocumentHolder.addCommentText": "新增註解", "DE.Views.DocumentHolder.advancedDropCapText": "首字大寫設定", "DE.Views.DocumentHolder.advancedEquationText": "方程式設定", "DE.Views.DocumentHolder.advancedFrameText": "框線進階設定", "DE.Views.DocumentHolder.advancedParagraphText": "段落進階設定", "DE.Views.DocumentHolder.advancedTableText": "表格進階設定", "DE.Views.DocumentHolder.advancedText": "進階設定", "DE.Views.DocumentHolder.alignmentText": "對齊", "DE.Views.DocumentHolder.allLinearText": "全部 - 線性", "DE.Views.DocumentHolder.allProfText": "全部 - 專業", "DE.Views.DocumentHolder.belowText": "下方", "DE.Views.DocumentHolder.breakBeforeText": "分頁之前", "DE.Views.DocumentHolder.bulletsText": "項目符號與編號", "DE.Views.DocumentHolder.cellAlignText": "儲存格垂直對齊", "DE.Views.DocumentHolder.cellText": "儲存格", "DE.Views.DocumentHolder.centerText": "置中", "DE.Views.DocumentHolder.chartText": "圖表進階設定", "DE.Views.DocumentHolder.columnText": "欄", "DE.Views.DocumentHolder.currLinearText": "目前 - 線性", "DE.Views.DocumentHolder.currProfText": "目前 - 專業", "DE.Views.DocumentHolder.deleteColumnText": "刪除欄", "DE.Views.DocumentHolder.deleteRowText": "刪除列", "DE.Views.DocumentHolder.deleteTableText": "刪除表格", "DE.Views.DocumentHolder.deleteText": "刪除", "DE.Views.DocumentHolder.direct270Text": "向上旋轉文字", "DE.Views.DocumentHolder.direct90Text": "向下旋轉文字", "DE.Views.DocumentHolder.directHText": "水平", "DE.Views.DocumentHolder.directionText": "文字方向", "DE.Views.DocumentHolder.editChartText": "編輯資料", "DE.Views.DocumentHolder.editFooterText": "編輯頁尾", "DE.Views.DocumentHolder.editHeaderText": "編輯頁首", "DE.Views.DocumentHolder.editHyperlinkText": "編輯超連結", "DE.Views.DocumentHolder.eqToDisplayText": "預覽更改", "DE.Views.DocumentHolder.eqToInlineText": "變更為內嵌", "DE.Views.DocumentHolder.guestText": "訪客", "DE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "DE.Views.DocumentHolder.hyperlinkText": "超連結", "DE.Views.DocumentHolder.ignoreAllSpellText": "全部忽略", "DE.Views.DocumentHolder.ignoreSpellText": "忽略", "DE.Views.DocumentHolder.imageText": "圖片進階設定", "DE.Views.DocumentHolder.insertColumnLeftText": "左欄", "DE.Views.DocumentHolder.insertColumnRightText": "右欄", "DE.Views.DocumentHolder.insertColumnText": "插入欄", "DE.Views.DocumentHolder.insertRowAboveText": "上方列", "DE.Views.DocumentHolder.insertRowBelowText": "下方列", "DE.Views.DocumentHolder.insertRowText": "插入列", "DE.Views.DocumentHolder.insertText": "插入", "DE.Views.DocumentHolder.keepLinesText": "保持線條一致", "DE.Views.DocumentHolder.langText": "選擇語言", "DE.Views.DocumentHolder.latexText": "LaTeX", "DE.Views.DocumentHolder.leftText": "左", "DE.Views.DocumentHolder.loadSpellText": "載入變體中...", "DE.Views.DocumentHolder.mergeCellsText": "合併儲存格", "DE.Views.DocumentHolder.mniImageFromFile": "Image from File", "DE.Views.DocumentHolder.mniImageFromStorage": "Image from Storage", "DE.Views.DocumentHolder.mniImageFromUrl": "Image from URL", "DE.Views.DocumentHolder.moreText": "更多變體...", "DE.Views.DocumentHolder.noSpellVariantsText": "沒有變體", "DE.Views.DocumentHolder.notcriticalErrorTitle": "警告", "DE.Views.DocumentHolder.originalSizeText": "實際大小", "DE.Views.DocumentHolder.paragraphText": "段落", "DE.Views.DocumentHolder.removeHyperlinkText": "移除超連結", "DE.Views.DocumentHolder.rightText": "右", "DE.Views.DocumentHolder.rowText": "列", "DE.Views.DocumentHolder.saveStyleText": "創建新樣式", "DE.Views.DocumentHolder.selectCellText": "選擇儲存格", "DE.Views.DocumentHolder.selectColumnText": "選擇欄", "DE.Views.DocumentHolder.selectRowText": "選擇列", "DE.Views.DocumentHolder.selectTableText": "選擇表格", "DE.Views.DocumentHolder.selectText": "選擇", "DE.Views.DocumentHolder.shapeText": "形狀進階設定", "DE.Views.DocumentHolder.showEqToolbar": "Show equation toolbar", "DE.Views.DocumentHolder.spellcheckText": "拼字檢查", "DE.Views.DocumentHolder.splitCellsText": "分割儲存格...", "DE.Views.DocumentHolder.splitCellTitleText": "分割儲存格", "DE.Views.DocumentHolder.strDelete": "移除簽名", "DE.Views.DocumentHolder.strDetails": "簽名詳細資訊", "DE.Views.DocumentHolder.strSetup": "簽名設定", "DE.Views.DocumentHolder.strSign": "簽名", "DE.Views.DocumentHolder.styleText": "格式設定為樣式", "DE.Views.DocumentHolder.tableText": "表格", "DE.Views.DocumentHolder.textAccept": "同意更新", "DE.Views.DocumentHolder.textAlign": "對齊", "DE.Views.DocumentHolder.textArrange": "排列", "DE.Views.DocumentHolder.textArrangeBack": "傳送到背景", "DE.Views.DocumentHolder.textArrangeBackward": "向後發送", "DE.Views.DocumentHolder.textArrangeForward": "向前移動", "DE.Views.DocumentHolder.textArrangeFront": "置於前景", "DE.Views.DocumentHolder.textCells": "儲存格", "DE.Views.DocumentHolder.textClearField": "Clear field", "DE.Views.DocumentHolder.textCol": "刪除整個欄", "DE.Views.DocumentHolder.textContentControls": "內容控制", "DE.Views.DocumentHolder.textContinueNumbering": "繼續編號", "DE.Views.DocumentHolder.textCopy": "複製", "DE.Views.DocumentHolder.textCrop": "裁剪", "DE.Views.DocumentHolder.textCropFill": "填入", "DE.Views.DocumentHolder.textCropFit": "適應大小", "DE.Views.DocumentHolder.textCut": "剪下", "DE.Views.DocumentHolder.textDistributeCols": "分佈欄", "DE.Views.DocumentHolder.textDistributeRows": "分佈列", "DE.Views.DocumentHolder.textEditControls": "內容控制設定", "DE.Views.DocumentHolder.textEditField": "Edit field", "DE.Views.DocumentHolder.textEditObject": "Edit object", "DE.Views.DocumentHolder.textEditPoints": "編輯點", "DE.Views.DocumentHolder.textEditWrapBoundary": "編輯換行邊界", "DE.Views.DocumentHolder.textFieldCodes": "Toggle field codes", "DE.Views.DocumentHolder.textFlipH": "水平翻轉", "DE.Views.DocumentHolder.textFlipV": "垂直翻轉", "DE.Views.DocumentHolder.textFollow": "跟隨移動", "DE.Views.DocumentHolder.textFromFile": "從檔案", "DE.Views.DocumentHolder.textFromStorage": "從儲存空間", "DE.Views.DocumentHolder.textFromUrl": "從網址", "DE.Views.DocumentHolder.textIndents": "調整清單縮排", "DE.Views.DocumentHolder.textJoinList": "加入上一個列表", "DE.Views.DocumentHolder.textLeft": "向左移動儲存格", "DE.Views.DocumentHolder.textNest": "巢狀表格", "DE.Views.DocumentHolder.textNextPage": "下一頁", "DE.Views.DocumentHolder.textNumberingValue": "編號值", "DE.Views.DocumentHolder.textPaste": "貼上", "DE.Views.DocumentHolder.textPrevPage": "前一頁", "DE.Views.DocumentHolder.textRedo": "Redo", "DE.Views.DocumentHolder.textRefreshField": "更新欄位", "DE.Views.DocumentHolder.textReject": "拒絕變更", "DE.Views.DocumentHolder.textRemCheckBox": "移除核取方塊", "DE.Views.DocumentHolder.textRemComboBox": "移除下拉式清單", "DE.Views.DocumentHolder.textRemDropdown": "移除下拉式選單", "DE.Views.DocumentHolder.textRemField": "移除文字欄位", "DE.Views.DocumentHolder.textRemove": "移除", "DE.Views.DocumentHolder.textRemoveControl": "移除內容控制項", "DE.Views.DocumentHolder.textRemPicture": "移除圖片", "DE.Views.DocumentHolder.textRemRadioBox": "移除單選按鈕", "DE.Views.DocumentHolder.textReplace": "取代圖片", "DE.Views.DocumentHolder.textResetCrop": "Reset crop", "DE.Views.DocumentHolder.textRotate": "旋轉", "DE.Views.DocumentHolder.textRotate270": "逆時針旋轉90°", "DE.Views.DocumentHolder.textRotate90": "順時針旋轉90°", "DE.Views.DocumentHolder.textRow": "刪除整列", "DE.Views.DocumentHolder.textSaveAsPicture": "另存為圖片", "DE.Views.DocumentHolder.textSeparateList": "分隔清單", "DE.Views.DocumentHolder.textSettings": "設定", "DE.Views.DocumentHolder.textSeveral": "多列/多欄", "DE.Views.DocumentHolder.textShapeAlignBottom": "底部對齊", "DE.Views.DocumentHolder.textShapeAlignCenter": "居中對齊", "DE.Views.DocumentHolder.textShapeAlignLeft": "對齊左側", "DE.Views.DocumentHolder.textShapeAlignMiddle": "中央對齊", "DE.Views.DocumentHolder.textShapeAlignRight": "對齊右側", "DE.Views.DocumentHolder.textShapeAlignTop": "上方對齊", "DE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "DE.Views.DocumentHolder.textStartNewList": "開始新清單", "DE.Views.DocumentHolder.textStartNumberingFrom": "設定編號值", "DE.Views.DocumentHolder.textTitleCellsRemove": "刪除儲存格", "DE.Views.DocumentHolder.textTOC": "目錄", "DE.Views.DocumentHolder.textTOCSettings": "目錄設定", "DE.Views.DocumentHolder.textUndo": "復原", "DE.Views.DocumentHolder.textUpdateAll": "更新整個表格", "DE.Views.DocumentHolder.textUpdatePages": "僅更新頁碼", "DE.Views.DocumentHolder.textUpdateTOC": "更新目錄", "DE.Views.DocumentHolder.textWrap": "文繞圖", "DE.Views.DocumentHolder.tipIsLocked": "此元素正在被其他使用者編輯。", "DE.Views.DocumentHolder.toDictionaryText": "新增到字典", "DE.Views.DocumentHolder.txtAddBottom": "新增底部邊框", "DE.Views.DocumentHolder.txtAddFractionBar": "新增分數欄", "DE.Views.DocumentHolder.txtAddHor": "新增水平線", "DE.Views.DocumentHolder.txtAddLB": "新增左邊框", "DE.Views.DocumentHolder.txtAddLeft": "新增左邊框", "DE.Views.DocumentHolder.txtAddLT": "新增左頂行", "DE.Views.DocumentHolder.txtAddRight": "加入右邊框", "DE.Views.DocumentHolder.txtAddTop": "加入上邊框", "DE.Views.DocumentHolder.txtAddVer": "加入垂直線", "DE.Views.DocumentHolder.txtAlignToChar": "與角色對齊", "DE.Views.DocumentHolder.txtBehind": "文字在後", "DE.Views.DocumentHolder.txtBorderProps": "邊框屬性", "DE.Views.DocumentHolder.txtBottom": "底部", "DE.Views.DocumentHolder.txtColumnAlign": "欄對齊", "DE.Views.DocumentHolder.txtDecreaseArg": "減少參數大小", "DE.Views.DocumentHolder.txtDeleteArg": "刪除參數", "DE.Views.DocumentHolder.txtDeleteBreak": "刪除手動分頁", "DE.Views.DocumentHolder.txtDeleteChars": "刪除封閉字元", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "刪除括起來的字元和分隔符號", "DE.Views.DocumentHolder.txtDeleteEq": "刪除方程式", "DE.Views.DocumentHolder.txtDeleteGroupChar": "刪除字元", "DE.Views.DocumentHolder.txtDeleteRadical": "刪除根號", "DE.Views.DocumentHolder.txtDistribHor": "水平分散對齊", "DE.Views.DocumentHolder.txtDistribVert": "垂直分散對齊", "DE.Views.DocumentHolder.txtEmpty": "（空）", "DE.Views.DocumentHolder.txtFractionLinear": "變更為線性分數", "DE.Views.DocumentHolder.txtFractionSkewed": "變更為斜角分數", "DE.Views.DocumentHolder.txtFractionStacked": "變更為堆疊分數", "DE.Views.DocumentHolder.txtGroup": "群組", "DE.Views.DocumentHolder.txtGroupCharOver": "字元上方", "DE.Views.DocumentHolder.txtGroupCharUnder": "字元下方", "DE.Views.DocumentHolder.txtHideBottom": "隱藏底部邊框", "DE.Views.DocumentHolder.txtHideBottomLimit": "隱藏底部限制", "DE.Views.DocumentHolder.txtHideCloseBracket": "隱藏閉括號", "DE.Views.DocumentHolder.txtHideDegree": "隱藏度數", "DE.Views.DocumentHolder.txtHideHor": "隱藏水平線", "DE.Views.DocumentHolder.txtHideLB": "隱藏左下線", "DE.Views.DocumentHolder.txtHideLeft": "隱藏左邊框", "DE.Views.DocumentHolder.txtHideLT": "隱藏左上線", "DE.Views.DocumentHolder.txtHideOpenBracket": "隱藏開括號", "DE.Views.DocumentHolder.txtHidePlaceholder": "隱藏佔位符", "DE.Views.DocumentHolder.txtHideRight": "隱藏右邊框", "DE.Views.DocumentHolder.txtHideTop": "隱藏頂部邊框", "DE.Views.DocumentHolder.txtHideTopLimit": "隱藏頂部限制", "DE.Views.DocumentHolder.txtHideVer": "隱藏垂直線", "DE.Views.DocumentHolder.txtIncreaseArg": "增加參數大小", "DE.Views.DocumentHolder.txtInFront": "置於文字前方", "DE.Views.DocumentHolder.txtInline": "與文字對齊", "DE.Views.DocumentHolder.txtInsertArgAfter": "在後面插入參數", "DE.Views.DocumentHolder.txtInsertArgBefore": "在前面插入參數", "DE.Views.DocumentHolder.txtInsertBreak": "插入手動分頁", "DE.Views.DocumentHolder.txtInsertCaption": "插入標題", "DE.Views.DocumentHolder.txtInsertEqAfter": "在之後插入方程式", "DE.Views.DocumentHolder.txtInsertEqBefore": "在之前插入方程式", "DE.Views.DocumentHolder.txtInsImage": "Insert image from file", "DE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "DE.Views.DocumentHolder.txtKeepTextOnly": "僅保留文字", "DE.Views.DocumentHolder.txtLimitChange": "變更限制位置", "DE.Views.DocumentHolder.txtLimitOver": "文字限制", "DE.Views.DocumentHolder.txtLimitUnder": "文字下的限制", "DE.Views.DocumentHolder.txtMatchBrackets": "括號與其內容的高度對齊", "DE.Views.DocumentHolder.txtMatrixAlign": "矩陣對齊", "DE.Views.DocumentHolder.txtOverbar": "文字上方橫線", "DE.Views.DocumentHolder.txtOverwriteCells": "覆寫儲存格", "DE.Views.DocumentHolder.txtPasteSourceFormat": "保留原始格式", "DE.Views.DocumentHolder.txtPressLink": "按{0}並單擊連結", "DE.Views.DocumentHolder.txtPrintSelection": "列印選擇", "DE.Views.DocumentHolder.txtRemFractionBar": "移除分數欄", "DE.Views.DocumentHolder.txtRemLimit": "移除限制", "DE.Views.DocumentHolder.txtRemoveAccentChar": "刪除強調字符", "DE.Views.DocumentHolder.txtRemoveBar": "移除欄", "DE.Views.DocumentHolder.txtRemoveWarning": "您是否要刪除此簽名？<br>此操作無法撤消。", "DE.Views.DocumentHolder.txtRemScripts": "移除腳本", "DE.Views.DocumentHolder.txtRemSubscript": "移除下標", "DE.Views.DocumentHolder.txtRemSuperscript": "移除上標", "DE.Views.DocumentHolder.txtScriptsAfter": "文字後的腳本", "DE.Views.DocumentHolder.txtScriptsBefore": "文字前的腳本", "DE.Views.DocumentHolder.txtShowBottomLimit": "顯示底限", "DE.Views.DocumentHolder.txtShowCloseBracket": "顯示結束括號", "DE.Views.DocumentHolder.txtShowDegree": "顯示度量單位", "DE.Views.DocumentHolder.txtShowOpenBracket": "顯示開始括號", "DE.Views.DocumentHolder.txtShowPlaceholder": "顯示佔位符", "DE.Views.DocumentHolder.txtShowTopLimit": "顯示頂部限制", "DE.Views.DocumentHolder.txtSquare": "方形", "DE.Views.DocumentHolder.txtStretchBrackets": "延伸括號", "DE.Views.DocumentHolder.txtThrough": "透過", "DE.Views.DocumentHolder.txtTight": "緊密", "DE.Views.DocumentHolder.txtTop": "頂部", "DE.Views.DocumentHolder.txtTopAndBottom": "頂部和底部", "DE.Views.DocumentHolder.txtUnderbar": "文字下方橫線", "DE.Views.DocumentHolder.txtUngroup": "取消分組", "DE.Views.DocumentHolder.txtWarnUrl": "點擊此連結可能對您的設備和資料造成損害。您確定要繼續嗎？", "DE.Views.DocumentHolder.unicodeText": "Unicode", "DE.Views.DocumentHolder.updateStyleText": "更新%1樣式", "DE.Views.DocumentHolder.vertAlignText": "垂直對齊", "DE.Views.DropcapSettingsAdvanced.strBorders": "框線和新增", "DE.Views.DropcapSettingsAdvanced.strDropcap": "首字大寫", "DE.Views.DropcapSettingsAdvanced.strMargins": "邊框", "DE.Views.DropcapSettingsAdvanced.textAlign": "對齊", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "至少", "DE.Views.DropcapSettingsAdvanced.textAuto": "自動", "DE.Views.DropcapSettingsAdvanced.textBackColor": "背景顏色", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "邊框色彩", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "點擊圖表或使用按鈕選擇邊框", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "邊框大小", "DE.Views.DropcapSettingsAdvanced.textBottom": "底部", "DE.Views.DropcapSettingsAdvanced.textCenter": "置中", "DE.Views.DropcapSettingsAdvanced.textColumn": "欄", "DE.Views.DropcapSettingsAdvanced.textDistance": "與文字的距離", "DE.Views.DropcapSettingsAdvanced.textExact": "確切地", "DE.Views.DropcapSettingsAdvanced.textFlow": "流程框架", "DE.Views.DropcapSettingsAdvanced.textFont": "字型", "DE.Views.DropcapSettingsAdvanced.textFrame": "框線", "DE.Views.DropcapSettingsAdvanced.textHeight": "高度", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "水平", "DE.Views.DropcapSettingsAdvanced.textInline": "內嵌框架", "DE.Views.DropcapSettingsAdvanced.textInMargin": "在邊界內", "DE.Views.DropcapSettingsAdvanced.textInText": "在文字中", "DE.Views.DropcapSettingsAdvanced.textLeft": "左", "DE.Views.DropcapSettingsAdvanced.textMargin": "邊框", "DE.Views.DropcapSettingsAdvanced.textMove": "隨文字移動", "DE.Views.DropcapSettingsAdvanced.textNone": "無", "DE.Views.DropcapSettingsAdvanced.textPage": "頁面", "DE.Views.DropcapSettingsAdvanced.textParagraph": "段落", "DE.Views.DropcapSettingsAdvanced.textParameters": "參數", "DE.Views.DropcapSettingsAdvanced.textPosition": "位置", "DE.Views.DropcapSettingsAdvanced.textRelative": "關係到", "DE.Views.DropcapSettingsAdvanced.textRight": "右", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "行高", "DE.Views.DropcapSettingsAdvanced.textTitle": "插入首字大寫-進階設定", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "框線-進階設定", "DE.Views.DropcapSettingsAdvanced.textTop": "頂部", "DE.Views.DropcapSettingsAdvanced.textVertical": "垂直", "DE.Views.DropcapSettingsAdvanced.textWidth": "寬度", "DE.Views.DropcapSettingsAdvanced.tipFontName": "字型", "DE.Views.EditListItemDialog.textDisplayName": "顯示名稱", "DE.Views.EditListItemDialog.textNameError": "顯示名稱不能為空白。", "DE.Views.EditListItemDialog.textValue": "數值", "DE.Views.EditListItemDialog.textValueError": "具有相同數值的項目已存在。", "DE.Views.FileMenu.ariaFileMenu": "File menu", "DE.Views.FileMenu.btnBackCaption": "打開檔案位置", "DE.Views.FileMenu.btnCloseEditor": "Close File", "DE.Views.FileMenu.btnCloseMenuCaption": "關閉選單", "DE.Views.FileMenu.btnCreateNewCaption": "新增", "DE.Views.FileMenu.btnDownloadCaption": "另存為", "DE.Views.FileMenu.btnExitCaption": "關閉", "DE.Views.FileMenu.btnFileOpenCaption": "開啟", "DE.Views.FileMenu.btnHelpCaption": "說明", "DE.Views.FileMenu.btnHistoryCaption": "版本歷史", "DE.Views.FileMenu.btnInfoCaption": "文件資訊", "DE.Views.FileMenu.btnPrintCaption": "列印", "DE.Views.FileMenu.btnProtectCaption": "保護", "DE.Views.FileMenu.btnRecentFilesCaption": "最近打開的", "DE.Views.FileMenu.btnRenameCaption": "重新命名", "DE.Views.FileMenu.btnReturnCaption": "返回文件", "DE.Views.FileMenu.btnRightsCaption": "存取權限", "DE.Views.FileMenu.btnSaveAsCaption": "另存為", "DE.Views.FileMenu.btnSaveCaption": "儲存", "DE.Views.FileMenu.btnSaveCopyAsCaption": "另存副本", "DE.Views.FileMenu.btnSettingsCaption": "進階設定", "DE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "DE.Views.FileMenu.btnToEditCaption": "編輯文件", "DE.Views.FileMenu.textDownload": "下載", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "空白文檔", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "新增", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "套用", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "新增作者", "DE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "新增文字", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "應用程式", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作者", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "變更存取權限", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "評論", "DE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "已創建", "DE.Views.FileMenuPanels.DocumentInfo.txtDocumentInfo": "文件資訊", "DE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "快速網頁檢視", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "載入中...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "上次修改者", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "上次修改時間", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "否", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "擁有者", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "頁數", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "頁面大小", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "段落數", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "PDF產生器", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "已標記的PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "PDF版本", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "位置", "DE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "DE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "擁有權限的人", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "字元數(含空白)", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "統計", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "主旨", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "字元數(不含空白)", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "標籤", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "標題", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "\n已上傳", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "字數", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "是", "DE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "存取權限", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "變更存取權限", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "擁有權限的人", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "帶密碼", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "受保護的文件", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "帶簽名", "DE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "有效簽名已添加到文件中。文件已受到編輯保護。", "DE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "通過添加不可見的數位簽名確保文件的完整性", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "編輯文件", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "編輯將從文件中刪除簽名<br>是否繼續？", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "此文件已受到密碼保護。", "DE.Views.FileMenuPanels.ProtectDoc.txtProtectDocument": "使用密碼對該文件進行加密", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "該文件需要簽名。", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有效簽名已添加到文件中。文件已受到編輯保護。", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "文件中的一些數位簽章無效或無法驗證。該文件受到保護，無法編輯。", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "檢視簽署", "DE.Views.FileMenuPanels.Settings.okButtonText": "套用", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "共同編輯模式", "DE.Views.FileMenuPanels.Settings.strFast": "快速", "DE.Views.FileMenuPanels.Settings.strFontRender": "字型微調", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "忽略大寫字詞", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "忽略帶數字的單詞", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "巨集設定", "DE.Views.FileMenuPanels.Settings.strPasteButton": "貼上內容時顯示\"貼上選項\"按鈕", "DE.Views.FileMenuPanels.Settings.strRTLSupport": "RTL interface", "DE.Views.FileMenuPanels.Settings.strShowChanges": "即時共同編輯設定更新", "DE.Views.FileMenuPanels.Settings.strShowComments": "在文字中顯示註解", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "顯示其他使用者的變更", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "顯示已解決的註釋", "DE.Views.FileMenuPanels.Settings.strStrict": "嚴格", "DE.Views.FileMenuPanels.Settings.strTabStyle": "Tab style", "DE.Views.FileMenuPanels.Settings.strTheme": "介面主題", "DE.Views.FileMenuPanels.Settings.strUnit": "測量單位", "DE.Views.FileMenuPanels.Settings.strZoom": "預設縮放值", "DE.Views.FileMenuPanels.Settings.text10Minutes": "每10分鐘", "DE.Views.FileMenuPanels.Settings.text30Minutes": "每30分鐘", "DE.Views.FileMenuPanels.Settings.text5Minutes": "每5分鐘", "DE.Views.FileMenuPanels.Settings.text60Minutes": "每小時", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "對齊指南", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "自動恢復", "DE.Views.FileMenuPanels.Settings.textAutoSave": "自動儲存", "DE.Views.FileMenuPanels.Settings.textDisabled": "已停用", "DE.Views.FileMenuPanels.Settings.textFill": "Fill", "DE.Views.FileMenuPanels.Settings.textForceSave": "儲存所有歷史版本到伺服器", "DE.Views.FileMenuPanels.Settings.textLine": "Line", "DE.Views.FileMenuPanels.Settings.textMinute": "每分鐘", "DE.Views.FileMenuPanels.Settings.textOldVersions": "將文件保存為DOCX格式時，使其與舊版MS Word兼容", "DE.Views.FileMenuPanels.Settings.textSmartSelection": "Use smart paragraph selection", "DE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "進階設定", "DE.Views.FileMenuPanels.Settings.txtAll": "檢視全部", "DE.Views.FileMenuPanels.Settings.txtAppearance": "Appearance", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "自動校正選項...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "預設暫存模式", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "點擊後，以球形文字顯示修訂", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "游標放在修訂內容上時，以提示框顯示", "DE.Views.FileMenuPanels.Settings.txtCm": "公分", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "共同編輯", "DE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "Customize quick access", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "開啟文件暗黑模式", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "編輯並儲存", "DE.Views.FileMenuPanels.Settings.txtFastTip": "即時共同編輯。所有更改都會自動儲存", "DE.Views.FileMenuPanels.Settings.txtFitPage": "調整至頁面大小", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "調整至寬度大小", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "象形文字", "DE.Views.FileMenuPanels.Settings.txtInch": "英寸", "DE.Views.FileMenuPanels.Settings.txtLast": "檢視最後", "DE.Views.FileMenuPanels.Settings.txtLastUsed": "Last used", "DE.Views.FileMenuPanels.Settings.txtMac": "參照 OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "參照本機", "DE.Views.FileMenuPanels.Settings.txtNone": "不檢視", "DE.Views.FileMenuPanels.Settings.txtProofing": "校對", "DE.Views.FileMenuPanels.Settings.txtPt": "點", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "在編輯器標頭中顯示快速列印按鈕", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "文件將被列印在上次選擇的或預設的印表機上。", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "全部啟用", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "啟用巨集時不通知", "DE.Views.FileMenuPanels.Settings.txtScreenReader": "Turn on screen reader support", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "顯示追蹤更改", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "拼字檢查", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "停用全部", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "停用巨集時不通知", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "使用「儲存」按鈕同步您和其他人所做的更改", "DE.Views.FileMenuPanels.Settings.txtTabBack": "Use toolbar color as tabs background", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "使用 Alt 鍵使用鍵盤瀏覽使用者介面", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "使用 Option 鍵使用鍵盤瀏覽使用者介面", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "顯示通知", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "停用巨集時通知", "DE.Views.FileMenuPanels.Settings.txtWin": "參照 Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "工作區", "DE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "另存為", "DE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "另存新檔為", "DE.Views.FormSettings.textAlways": "永遠", "DE.Views.FormSettings.textAnyone": "任何人", "DE.Views.FormSettings.textAspect": "鎖定長寬比", "DE.Views.FormSettings.textAtLeast": "至少", "DE.Views.FormSettings.textAuto": "自動", "DE.Views.FormSettings.textAutofit": "自動調整大小", "DE.Views.FormSettings.textBackgroundColor": "背景顏色", "DE.Views.FormSettings.textCheckbox": "核取方塊", "DE.Views.FormSettings.textCheckDefault": "核取方塊預設為已勾選", "DE.Views.FormSettings.textColor": "邊框色彩", "DE.Views.FormSettings.textComb": "字符組合", "DE.Views.FormSettings.textCombobox": "下拉式方框", "DE.Views.FormSettings.textComplex": "複雜字段", "DE.Views.FormSettings.textConnected": "已連結的欄位", "DE.Views.FormSettings.textCreditCard": "信用卡號碼（例如 4111-1111-1111-1111）", "DE.Views.FormSettings.textDateField": "日期和時間欄位", "DE.Views.FormSettings.textDateFormat": "以這種方式顯示日期", "DE.Views.FormSettings.textDefValue": "預設值", "DE.Views.FormSettings.textDelete": "刪除", "DE.Views.FormSettings.textDigits": "數字", "DE.Views.FormSettings.textDisconnect": "斷線", "DE.Views.FormSettings.textDropDown": "下拉式選單", "DE.Views.FormSettings.textExact": "確切地", "DE.Views.FormSettings.textField": "文字段落", "DE.Views.FormSettings.textFillRoles": "誰需要填寫這個？", "DE.Views.FormSettings.textFixed": "固定大小欄位", "DE.Views.FormSettings.textFormat": "格式", "DE.Views.FormSettings.textFormatSymbols": "允許的符號", "DE.Views.FormSettings.textFromFile": "從檔案", "DE.Views.FormSettings.textFromStorage": "從儲存空間", "DE.Views.FormSettings.textFromUrl": "從網址", "DE.Views.FormSettings.textGroupKey": "群組密鑰", "DE.Views.FormSettings.textImage": "圖像", "DE.Views.FormSettings.textKey": "密鑰", "DE.Views.FormSettings.textLang": "語言", "DE.Views.FormSettings.textLetters": "字母", "DE.Views.FormSettings.textLock": "鎖定", "DE.Views.FormSettings.textMask": "任意遮罩", "DE.Views.FormSettings.textMaxChars": "字元數限制", "DE.Views.FormSettings.textMulti": "多行文字欄位", "DE.Views.FormSettings.textNever": "永不", "DE.Views.FormSettings.textNoBorder": "無邊框", "DE.Views.FormSettings.textNone": "無", "DE.Views.FormSettings.textPhone1": "電話號碼（例：（123）456-7890）", "DE.Views.FormSettings.textPhone2": "電話號碼（例：+447911123456）", "DE.Views.FormSettings.textPlaceholder": "佔位符", "DE.Views.FormSettings.textRadiobox": "單選按鈕", "DE.Views.FormSettings.textRadioChoice": "Radio button choice", "DE.Views.FormSettings.textRadioDefault": "按鈕預設為選中狀態", "DE.Views.FormSettings.textReg": "正規表示式", "DE.Views.FormSettings.textRequired": "必填", "DE.Views.FormSettings.textScale": "何時進行縮放", "DE.Views.FormSettings.textSelectImage": "選擇圖片", "DE.Views.FormSettings.textSignature": "Signature", "DE.Views.FormSettings.textTag": "標籤", "DE.Views.FormSettings.textTip": "提示", "DE.Views.FormSettings.textTipAdd": "增加新數值", "DE.Views.FormSettings.textTipDelete": "刪除數值", "DE.Views.FormSettings.textTipDown": "向下移動", "DE.Views.FormSettings.textTipUp": "向上移動", "DE.Views.FormSettings.textTooBig": "圖片過大", "DE.Views.FormSettings.textTooSmall": "圖片過小", "DE.Views.FormSettings.textUKPassport": "英國護照號碼（例如*********）", "DE.Views.FormSettings.textUnlock": "解鎖", "DE.Views.FormSettings.textUSSSN": "美國社會安全碼（例如***********）", "DE.Views.FormSettings.textValue": "數值項", "DE.Views.FormSettings.textWidth": "儲存格寬度", "DE.Views.FormSettings.textZipCodeUS": "美國郵遞區號（例如92663或92663-1234）", "DE.Views.FormsTab.capBtnCheckBox": "核取方塊", "DE.Views.FormsTab.capBtnComboBox": "下拉式方框", "DE.Views.FormsTab.capBtnComplex": "複雜字段", "DE.Views.FormsTab.capBtnDownloadForm": "另存為pdf", "DE.Views.FormsTab.capBtnDropDown": "下拉式選單", "DE.Views.FormsTab.capBtnEmail": "電子郵件", "DE.Views.FormsTab.capBtnImage": "圖片", "DE.Views.FormsTab.capBtnManager": "管理角色", "DE.Views.FormsTab.capBtnNext": "下一欄位", "DE.Views.FormsTab.capBtnPhone": "電話號碼", "DE.Views.FormsTab.capBtnPrev": "前一個欄位", "DE.Views.FormsTab.capBtnRadioBox": "單選按鈕", "DE.Views.FormsTab.capBtnSaveForm": "另存為 PDF", "DE.Views.FormsTab.capBtnSaveFormDesktop": "Save As...", "DE.Views.FormsTab.capBtnSignature": "Signature Field", "DE.Views.FormsTab.capBtnSubmit": "提交", "DE.Views.FormsTab.capBtnText": "文字段落", "DE.Views.FormsTab.capBtnView": "檢視表單", "DE.Views.FormsTab.capCreditCard": "信用卡", "DE.Views.FormsTab.capDateTime": "日期和時間", "DE.Views.FormsTab.capZipCode": "郵遞區號", "DE.Views.FormsTab.helpTextFillStatus": "This form is ready for role-based filling. Click on the status button to check the filling stage.", "DE.Views.FormsTab.textAnyone": "任何人", "DE.Views.FormsTab.textClear": "清除欄位", "DE.Views.FormsTab.textClearFields": "清除所有欄位", "DE.Views.FormsTab.textCreateForm": "新增文字段落並建立一個可填寫的 PDF 文件", "DE.Views.FormsTab.textFilled": "Filled", "DE.Views.FormsTab.textGotIt": "了解", "DE.Views.FormsTab.textHighlight": "突顯設定", "DE.Views.FormsTab.textNoHighlight": "無醒目標示", "DE.Views.FormsTab.textRequired": "填寫所有必填欄位以傳送表單。", "DE.Views.FormsTab.textSubmited": "表單提交成功", "DE.Views.FormsTab.textSubmitOk": "Your PDF form has been saved in the Complete section. You can fill out this form again and send another result.", "DE.Views.FormsTab.tipCheckBox": "插入核取方塊", "DE.Views.FormsTab.tipComboBox": "插入組合框", "DE.Views.FormsTab.tipComplexField": "插入複雜欄位", "DE.Views.FormsTab.tipCreateField": "To create a field select the desired field type on the toolbar and click on it. The field will appear in the document.", "DE.Views.FormsTab.tipCreditCard": "插入信用卡號碼", "DE.Views.FormsTab.tipDateTime": "插入日期和時間", "DE.Views.FormsTab.tipDownloadForm": "下載成可編輯PDF文件", "DE.Views.FormsTab.tipDropDown": "插入下拉清單", "DE.Views.FormsTab.tipEmailField": "插入電子郵件地址", "DE.Views.FormsTab.tipFieldSettings": "You can configure selected fields on the right sidebar. Click this icon to open the field settings.", "DE.Views.FormsTab.tipFieldsLink": "Learn more about field parameters", "DE.Views.FormsTab.tipFirstPage": "Go to the first page", "DE.Views.FormsTab.tipFixedText": "插入固定文字欄位", "DE.Views.FormsTab.tipFormGroupKey": "Group radio buttons to make the filling process faster. Choices with the same names will be synchronized. Users can only tick one radio button from the group.", "DE.Views.FormsTab.tipFormKey": "You can assign a key to a field or a group of fields. When a user fills in the data, it will be copied to all the fields with the same key.", "DE.Views.FormsTab.tipHelpRoles": "Use the Manage Roles feature to group fields by purpose and assign the responsible team members.", "DE.Views.FormsTab.tipImageField": "插入圖片", "DE.Views.FormsTab.tipInlineText": "插入內嵌文字欄位", "DE.Views.FormsTab.tipLastPage": "Go to the last page", "DE.Views.FormsTab.tipManager": "管理角色", "DE.Views.FormsTab.tipNextForm": "前往下一個欄位", "DE.Views.FormsTab.tipNextPage": "Go to the next page", "DE.Views.FormsTab.tipPhoneField": "插入電話號碼", "DE.Views.FormsTab.tipPrevForm": "前往上一個欄位", "DE.Views.FormsTab.tipPrevPage": "Go to the previous page", "DE.Views.FormsTab.tipRadioBox": "插入單選按鈕", "DE.Views.FormsTab.tipRolesLink": "Learn more about roles", "DE.Views.FormsTab.tipSaveFile": "點擊\"儲存成pdf\"即可轉成可填入的表單", "DE.Views.FormsTab.tipSaveForm": "儲存一份可以填寫的 PDF 檔案", "DE.Views.FormsTab.tipSignField": "Insert signature field", "DE.Views.FormsTab.tipSubmit": "提交表單", "DE.Views.FormsTab.tipTextField": "插入文字欄位", "DE.Views.FormsTab.tipViewForm": "檢視表單", "DE.Views.FormsTab.tipZipCode": "插入郵遞區號", "DE.Views.FormsTab.txtFixedDesc": "插入固定文字欄位", "DE.Views.FormsTab.txtFixedText": "固定", "DE.Views.FormsTab.txtInlineDesc": "插入內嵌文字欄位", "DE.Views.FormsTab.txtInlineText": "內嵌", "DE.Views.FormsTab.txtUntitled": "未命名", "DE.Views.HeaderFooterSettings.textBottomCenter": "底部中間", "DE.Views.HeaderFooterSettings.textBottomLeft": "左下方", "DE.Views.HeaderFooterSettings.textBottomPage": "頁面底部", "DE.Views.HeaderFooterSettings.textBottomRight": "底部右側", "DE.Views.HeaderFooterSettings.textDiffFirst": "首頁不同", "DE.Views.HeaderFooterSettings.textDiffOdd": "不同的奇數和偶數頁", "DE.Views.HeaderFooterSettings.textFrom": "從...開始", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "頁尾距底部", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "頁首距離上方", "DE.Views.HeaderFooterSettings.textInsertCurrent": "插入至目前位置", "DE.Views.HeaderFooterSettings.textNumFormat": "Number format", "DE.Views.HeaderFooterSettings.textOptions": "選項", "DE.Views.HeaderFooterSettings.textPageNum": "插入頁碼", "DE.Views.HeaderFooterSettings.textPageNumbering": "頁碼編號", "DE.Views.HeaderFooterSettings.textPosition": "位置", "DE.Views.HeaderFooterSettings.textPrev": "從前一節繼續", "DE.Views.HeaderFooterSettings.textSameAs": "連結到前一個", "DE.Views.HeaderFooterSettings.textTopCenter": "頂部中央", "DE.Views.HeaderFooterSettings.textTopLeft": "頂部左側", "DE.Views.HeaderFooterSettings.textTopPage": "頁首", "DE.Views.HeaderFooterSettings.textTopRight": "頂部右側", "DE.Views.HeaderFooterSettings.txtMoreTypes": "More types", "DE.Views.HyperlinkSettingsDialog.textDefault": "選擇的文字片段", "DE.Views.HyperlinkSettingsDialog.textDisplay": "顯示", "DE.Views.HyperlinkSettingsDialog.textExternal": "外部連結", "DE.Views.HyperlinkSettingsDialog.textInternal": "放置在文件中", "DE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "DE.Views.HyperlinkSettingsDialog.textTitle": "超連結設置", "DE.Views.HyperlinkSettingsDialog.textTooltip": "工具提示文字", "DE.Views.HyperlinkSettingsDialog.textUrl": "連結至", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "文件開頭", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "書籤", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "此欄位為必填欄位", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "標題", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "此欄位應為符合「http://www.example.com」格式的網址。", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "此欄位的限制為 2083 個字元。", "DE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "DE.Views.HyphenationDialog.textAuto": "自動斷字功能", "DE.Views.HyphenationDialog.textCaps": "Hyphenate words in CAPS", "DE.Views.HyphenationDialog.textLimit": "Limit consecutive hyphens to", "DE.Views.HyphenationDialog.textNoLimit": "No limit", "DE.Views.HyphenationDialog.textTitle": "Hyphenation", "DE.Views.HyphenationDialog.textZone": "Hyphenation zone", "DE.Views.ImageSettings.strTransparency": "Opacity", "DE.Views.ImageSettings.textAdvanced": "顯示進階設定", "DE.Views.ImageSettings.textCrop": "裁剪", "DE.Views.ImageSettings.textCropFill": "填入", "DE.Views.ImageSettings.textCropFit": "適應大小", "DE.Views.ImageSettings.textCropToShape": "裁剪為形狀", "DE.Views.ImageSettings.textEdit": "編輯", "DE.Views.ImageSettings.textEditObject": "編輯物件", "DE.Views.ImageSettings.textFitMargins": "調整至邊界大小", "DE.Views.ImageSettings.textFlip": "翻轉", "DE.Views.ImageSettings.textFromFile": "從檔案", "DE.Views.ImageSettings.textFromStorage": "從儲存空間", "DE.Views.ImageSettings.textFromUrl": "從網址", "DE.Views.ImageSettings.textHeight": "高度", "DE.Views.ImageSettings.textHint270": "逆時針旋轉90°", "DE.Views.ImageSettings.textHint90": "順時針旋轉90°", "DE.Views.ImageSettings.textHintFlipH": "水平翻轉", "DE.Views.ImageSettings.textHintFlipV": "垂直翻轉", "DE.Views.ImageSettings.textInsert": "取代圖片", "DE.Views.ImageSettings.textOriginalSize": "實際大小", "DE.Views.ImageSettings.textRecentlyUsed": "最近使用", "DE.Views.ImageSettings.textResetCrop": "Reset crop", "DE.Views.ImageSettings.textRotate90": "旋轉90°", "DE.Views.ImageSettings.textRotation": "旋轉", "DE.Views.ImageSettings.textSize": "大小", "DE.Views.ImageSettings.textWidth": "寬度", "DE.Views.ImageSettings.textWrap": "換行樣式", "DE.Views.ImageSettings.txtBehind": "文字在後", "DE.Views.ImageSettings.txtInFront": "置於文字前方", "DE.Views.ImageSettings.txtInline": "與文字對齊", "DE.Views.ImageSettings.txtSquare": "方形", "DE.Views.ImageSettings.txtThrough": "透過", "DE.Views.ImageSettings.txtTight": "緊密", "DE.Views.ImageSettings.txtTopAndBottom": "頂部和底部", "DE.Views.ImageSettingsAdvanced.strMargins": "文字填充", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "絕對", "DE.Views.ImageSettingsAdvanced.textAlignment": "對齊", "DE.Views.ImageSettingsAdvanced.textAlt": "替代文字", "DE.Views.ImageSettingsAdvanced.textAltDescription": "描述", "DE.Views.ImageSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "DE.Views.ImageSettingsAdvanced.textAltTitle": "標題", "DE.Views.ImageSettingsAdvanced.textAngle": "角度", "DE.Views.ImageSettingsAdvanced.textArrows": "箭頭", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "鎖定長寬比", "DE.Views.ImageSettingsAdvanced.textAutofit": "自動調整大小", "DE.Views.ImageSettingsAdvanced.textBeginSize": "起始大小", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "起始樣式", "DE.Views.ImageSettingsAdvanced.textBelow": "下方", "DE.Views.ImageSettingsAdvanced.textBevel": "立體斜角", "DE.Views.ImageSettingsAdvanced.textBottom": "底部", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "下方邊界", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "文字包裝", "DE.Views.ImageSettingsAdvanced.textCapType": "大寫字元樣式型", "DE.Views.ImageSettingsAdvanced.textCenter": "置中", "DE.Views.ImageSettingsAdvanced.textCharacter": "字元", "DE.Views.ImageSettingsAdvanced.textColumn": "欄", "DE.Views.ImageSettingsAdvanced.textDistance": "與文字的距離", "DE.Views.ImageSettingsAdvanced.textEndSize": "結束大小", "DE.Views.ImageSettingsAdvanced.textEndStyle": "結束樣式", "DE.Views.ImageSettingsAdvanced.textFlat": "平坦", "DE.Views.ImageSettingsAdvanced.textFlipped": "翻轉的", "DE.Views.ImageSettingsAdvanced.textHeight": "高度", "DE.Views.ImageSettingsAdvanced.textHorizontal": "水平", "DE.Views.ImageSettingsAdvanced.textHorizontally": "水平地", "DE.Views.ImageSettingsAdvanced.textJoinType": "加入類型", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "比例恆定", "DE.Views.ImageSettingsAdvanced.textLeft": "左", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "左邊距", "DE.Views.ImageSettingsAdvanced.textLine": "折線圖", "DE.Views.ImageSettingsAdvanced.textLineStyle": "線型", "DE.Views.ImageSettingsAdvanced.textMargin": "邊框", "DE.Views.ImageSettingsAdvanced.textMiter": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMove": "隨文字移動物件", "DE.Views.ImageSettingsAdvanced.textOptions": "選項", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "實際大小", "DE.Views.ImageSettingsAdvanced.textOverlap": "允許重疊", "DE.Views.ImageSettingsAdvanced.textPage": "頁面", "DE.Views.ImageSettingsAdvanced.textParagraph": "段落", "DE.Views.ImageSettingsAdvanced.textPosition": "位置", "DE.Views.ImageSettingsAdvanced.textPositionPc": "相對位置", "DE.Views.ImageSettingsAdvanced.textRelative": "關係到", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "相對的", "DE.Views.ImageSettingsAdvanced.textResizeFit": "調整形狀以符合文字大小", "DE.Views.ImageSettingsAdvanced.textRight": "右", "DE.Views.ImageSettingsAdvanced.textRightMargin": "右邊距", "DE.Views.ImageSettingsAdvanced.textRightOf": "在右側", "DE.Views.ImageSettingsAdvanced.textRotation": "旋轉", "DE.Views.ImageSettingsAdvanced.textRound": "圓形", "DE.Views.ImageSettingsAdvanced.textShape": "形狀設定", "DE.Views.ImageSettingsAdvanced.textSize": "大小", "DE.Views.ImageSettingsAdvanced.textSquare": "方形", "DE.Views.ImageSettingsAdvanced.textTextBox": "文字方塊", "DE.Views.ImageSettingsAdvanced.textTitle": "圖片-進階設定", "DE.Views.ImageSettingsAdvanced.textTitleChart": "圖表-進階設定", "DE.Views.ImageSettingsAdvanced.textTitleShape": "形狀 - 進階設定", "DE.Views.ImageSettingsAdvanced.textTop": "頂部", "DE.Views.ImageSettingsAdvanced.textTopMargin": "頂部邊框", "DE.Views.ImageSettingsAdvanced.textVertical": "垂直", "DE.Views.ImageSettingsAdvanced.textVertically": "垂直地", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "權重與箭頭", "DE.Views.ImageSettingsAdvanced.textWidth": "寬度", "DE.Views.ImageSettingsAdvanced.textWrap": "換行樣式", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "文字在後", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "置於文字前方", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "與文字對齊", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "方形", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "透過", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "緊密", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "頂部和底部", "DE.Views.LeftMenu.ariaLeftMenu": "Left menu", "DE.Views.LeftMenu.tipAbout": "關於", "DE.Views.LeftMenu.tipChat": "聊天", "DE.Views.LeftMenu.tipComments": "註解", "DE.Views.LeftMenu.tipNavigation": "導覽", "DE.Views.LeftMenu.tipOutline": "標題", "DE.Views.LeftMenu.tipPageThumbnails": "頁面預覽圖", "DE.Views.LeftMenu.tipPlugins": "外掛程式", "DE.Views.LeftMenu.tipSearch": "搜尋", "DE.Views.LeftMenu.tipSupport": "意見回饋與支援", "DE.Views.LeftMenu.tipTitles": "標題", "DE.Views.LeftMenu.txtDeveloper": "開發人員模式", "DE.Views.LeftMenu.txtEditor": "文字編輯器", "DE.Views.LeftMenu.txtLimit": "限制存取", "DE.Views.LeftMenu.txtTrial": "試用模式", "DE.Views.LeftMenu.txtTrialDev": "試用開發者模式", "DE.Views.LineNumbersDialog.textAddLineNumbering": "新增行號", "DE.Views.LineNumbersDialog.textApplyTo": "套用更動至", "DE.Views.LineNumbersDialog.textContinuous": "連續的", "DE.Views.LineNumbersDialog.textCountBy": "計數方式", "DE.Views.LineNumbersDialog.textDocument": "整份文件", "DE.Views.LineNumbersDialog.textForward": "此處起始", "DE.Views.LineNumbersDialog.textFromText": "從文字", "DE.Views.LineNumbersDialog.textNumbering": "編號", "DE.Views.LineNumbersDialog.textRestartEachPage": "每頁重新開始", "DE.Views.LineNumbersDialog.textRestartEachSection": "每節重新開始", "DE.Views.LineNumbersDialog.textSection": "目前章節", "DE.Views.LineNumbersDialog.textStartAt": "從...開始", "DE.Views.LineNumbersDialog.textTitle": "行號", "DE.Views.LineNumbersDialog.txtAutoText": "自動", "DE.Views.Links.capBtnAddText": "新增文字", "DE.Views.Links.capBtnBookmarks": "書籤", "DE.Views.Links.capBtnCaption": "標題", "DE.Views.Links.capBtnContentsUpdate": "更新表格", "DE.Views.Links.capBtnCrossRef": "交叉引用", "DE.Views.Links.capBtnInsContents": "目錄", "DE.Views.Links.capBtnInsFootnote": "註腳", "DE.Views.Links.capBtnInsLink": "超連結", "DE.Views.Links.capBtnTOF": "圖表目錄", "DE.Views.Links.confirmDeleteFootnotes": "您是否要刪除所有註腳？", "DE.Views.Links.confirmReplaceTOF": "您是否要取代所選的圖表目錄？", "DE.Views.Links.mniConvertNote": "轉換所有註解", "DE.Views.Links.mniDelFootnote": "刪除所有註解", "DE.Views.Links.mniInsEndnote": "插入尾註", "DE.Views.Links.mniInsFootnote": "插入註腳", "DE.Views.Links.mniNoteSettings": "筆記設定", "DE.Views.Links.textContentsRemove": "移除目錄", "DE.Views.Links.textContentsSettings": "設定", "DE.Views.Links.textConvertToEndnotes": "將所有註腳轉換為註尾", "DE.Views.Links.textConvertToFootnotes": "將所有註尾轉換為註腳", "DE.Views.Links.textGotoEndnote": "轉到尾註", "DE.Views.Links.textGotoFootnote": "前往註腳", "DE.Views.Links.textSwapNotes": "交換註腳和章節末註", "DE.Views.Links.textUpdateAll": "更新整個表格", "DE.Views.Links.textUpdatePages": "僅更新頁碼", "DE.Views.Links.tipAddText": "包含標題在目錄裡", "DE.Views.Links.tipBookmarks": "創建書籤", "DE.Views.Links.tipCaption": "插入標題", "DE.Views.Links.tipContents": "插入目錄", "DE.Views.Links.tipContentsUpdate": "更新目錄", "DE.Views.Links.tipCrossRef": "插入交叉參照", "DE.Views.Links.tipInsertHyperlink": "新增超連結", "DE.Views.Links.tipNotes": "插入或編輯註腳", "DE.Views.Links.tipTableFigures": "插入圖表", "DE.Views.Links.tipTableFiguresUpdate": "更新圖表目錄", "DE.Views.Links.titleUpdateTOF": "更新圖表目錄", "DE.Views.Links.txtDontShowTof": "不在目錄中顯示", "DE.Views.Links.txtLevel": "層級", "DE.Views.ListIndentsDialog.textSpace": "空格", "DE.Views.ListIndentsDialog.textTab": "定位字元", "DE.Views.ListIndentsDialog.textTitle": "清單縮排", "DE.Views.ListIndentsDialog.txtFollowBullet": "跟隨項目符號", "DE.Views.ListIndentsDialog.txtFollowNumber": "跟隨數字", "DE.Views.ListIndentsDialog.txtIndent": "文字縮排", "DE.Views.ListIndentsDialog.txtNone": "無", "DE.Views.ListIndentsDialog.txtPosBullet": "項目符號位置", "DE.Views.ListIndentsDialog.txtPosNumber": "編號位置", "DE.Views.ListSettingsDialog.textAuto": "自動", "DE.Views.ListSettingsDialog.textBold": "粗體", "DE.Views.ListSettingsDialog.textCenter": "置中", "DE.Views.ListSettingsDialog.textHide": "隱藏設定", "DE.Views.ListSettingsDialog.textItalic": "斜體", "DE.Views.ListSettingsDialog.textLeft": "左", "DE.Views.ListSettingsDialog.textLevel": "層級", "DE.Views.ListSettingsDialog.textMore": "顯示更多設定", "DE.Views.ListSettingsDialog.textPreview": "預覽", "DE.Views.ListSettingsDialog.textRight": "右", "DE.Views.ListSettingsDialog.textSelectLevel": "選擇層級", "DE.Views.ListSettingsDialog.textSpace": "空格", "DE.Views.ListSettingsDialog.textTab": "定位字元", "DE.Views.ListSettingsDialog.txtAlign": "對齊", "DE.Views.ListSettingsDialog.txtAlignAt": "在", "DE.Views.ListSettingsDialog.txtBullet": "項目符號", "DE.Views.ListSettingsDialog.txtColor": "顏色", "DE.Views.ListSettingsDialog.txtFollow": "跟隨數字", "DE.Views.ListSettingsDialog.txtFontName": "字型", "DE.Views.ListSettingsDialog.txtInclcudeLevel": "包括層級編號", "DE.Views.ListSettingsDialog.txtIndent": "文字縮排", "DE.Views.ListSettingsDialog.txtLikeText": "像文字", "DE.Views.ListSettingsDialog.txtMoreTypes": "更多類型", "DE.Views.ListSettingsDialog.txtNewBullet": "新增項目符號", "DE.Views.ListSettingsDialog.txtNone": "無", "DE.Views.ListSettingsDialog.txtNumFormatString": "數字格式", "DE.Views.ListSettingsDialog.txtRestart": "重啟清單", "DE.Views.ListSettingsDialog.txtSize": "大小", "DE.Views.ListSettingsDialog.txtStart": "從...開始", "DE.Views.ListSettingsDialog.txtSymbol": "符號", "DE.Views.ListSettingsDialog.txtTabStop": "在...新增定位點", "DE.Views.ListSettingsDialog.txtTitle": "清單設定", "DE.Views.ListSettingsDialog.txtType": "類型", "DE.Views.ListTypesAdvanced.labelSelect": "選擇清單類型", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "發送", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "主題", "DE.Views.MailMergeEmailDlg.textAttachDocx": "附加為DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "附件為PDF", "DE.Views.MailMergeEmailDlg.textFileName": "檔案名稱", "DE.Views.MailMergeEmailDlg.textFormat": "郵件格式", "DE.Views.MailMergeEmailDlg.textFrom": "從...來", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "訊息", "DE.Views.MailMergeEmailDlg.textSubject": "主題行", "DE.Views.MailMergeEmailDlg.textTitle": "傳送到電子郵件", "DE.Views.MailMergeEmailDlg.textTo": "至", "DE.Views.MailMergeEmailDlg.textWarning": "警告！", "DE.Views.MailMergeEmailDlg.textWarningMsg": "請注意，一旦您按下發送按鈕，郵件無法停止。", "DE.Views.MailMergeSettings.downloadMergeTitle": "合併中", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "合併失敗.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "警告", "DE.Views.MailMergeSettings.textAddRecipients": "首先將一些收件人添加到列表中", "DE.Views.MailMergeSettings.textAll": "所有記錄", "DE.Views.MailMergeSettings.textCurrent": "目前記錄", "DE.Views.MailMergeSettings.textDataSource": "資料來源", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "下載", "DE.Views.MailMergeSettings.textEditData": "編輯收件者清單", "DE.Views.MailMergeSettings.textEmail": "電子郵件", "DE.Views.MailMergeSettings.textFrom": "從...來", "DE.Views.MailMergeSettings.textGoToMail": "前往郵件", "DE.Views.MailMergeSettings.textHighlight": "突顯合併欄位", "DE.Views.MailMergeSettings.textInsertField": "插入合併欄位", "DE.Views.MailMergeSettings.textMaxRecepients": "最多100個收件人。", "DE.Views.MailMergeSettings.textMerge": "合併", "DE.Views.MailMergeSettings.textMergeFields": "合併欄位", "DE.Views.MailMergeSettings.textMergeTo": "合併到", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "儲存", "DE.Views.MailMergeSettings.textPreview": "預覽結果", "DE.Views.MailMergeSettings.textReadMore": "瞭解更多", "DE.Views.MailMergeSettings.textSendMsg": "所有郵件均已準備就緒，將在一段時間內傳送出去。<br>郵件的傳送速度取決於您的郵件服務。<br>您可以繼續使用檔案或將其關閉。操作結束後，通知將傳送到您註冊的電子郵件地址。", "DE.Views.MailMergeSettings.textTo": "至", "DE.Views.MailMergeSettings.txtFirst": "到第一筆記錄", "DE.Views.MailMergeSettings.txtFromToError": "\"從\"值必須小於\"到\"值", "DE.Views.MailMergeSettings.txtLast": "到最後一筆記錄", "DE.Views.MailMergeSettings.txtNext": "到下一筆記錄", "DE.Views.MailMergeSettings.txtPrev": "到前一筆記錄", "DE.Views.MailMergeSettings.txtUntitled": "未命名", "DE.Views.MailMergeSettings.warnProcessMailMerge": "開始合併失敗", "DE.Views.Navigation.strNavigate": "標題", "DE.Views.Navigation.txtClosePanel": "關閉標題", "DE.Views.Navigation.txtCollapse": "全部折疊", "DE.Views.Navigation.txtDemote": "降級", "DE.Views.Navigation.txtEmpty": "文件中沒有任何標題。<br>請對文字應用標題樣式，以便在目錄中顯示。", "DE.Views.Navigation.txtEmptyItem": "空標題", "DE.Views.Navigation.txtEmptyViewer": "文件中沒有標題。", "DE.Views.Navigation.txtExpand": "全部展開", "DE.Views.Navigation.txtExpandToLevel": "展開至層級", "DE.Views.Navigation.txtFontSize": "字型大小", "DE.Views.Navigation.txtHeadingAfter": "之後的新標題", "DE.Views.Navigation.txtHeadingBefore": "之前的新標題", "DE.Views.Navigation.txtLarge": "大號", "DE.Views.Navigation.txtMedium": "中等", "DE.Views.Navigation.txtNewHeading": "新副標題", "DE.Views.Navigation.txtPromote": "提升", "DE.Views.Navigation.txtSelect": "選擇內容", "DE.Views.Navigation.txtSettings": "標題設定", "DE.Views.Navigation.txtSmall": "小", "DE.Views.Navigation.txtWrapHeadings": "折行長標題", "DE.Views.NoteSettingsDialog.textApply": "套用", "DE.Views.NoteSettingsDialog.textApplyTo": "套用更動至", "DE.Views.NoteSettingsDialog.textContinue": "連續的", "DE.Views.NoteSettingsDialog.textCustom": "自訂標記", "DE.Views.NoteSettingsDialog.textDocEnd": "文件結尾", "DE.Views.NoteSettingsDialog.textDocument": "整份文件", "DE.Views.NoteSettingsDialog.textEachPage": "每頁重新開始", "DE.Views.NoteSettingsDialog.textEachSection": "重新開始每個部分", "DE.Views.NoteSettingsDialog.textEndnote": "註腳", "DE.Views.NoteSettingsDialog.textFootnote": "註腳", "DE.Views.NoteSettingsDialog.textFormat": "格式", "DE.Views.NoteSettingsDialog.textInsert": "插入", "DE.Views.NoteSettingsDialog.textLocation": "位置", "DE.Views.NoteSettingsDialog.textNumbering": "編號", "DE.Views.NoteSettingsDialog.textNumFormat": "數字格式", "DE.Views.NoteSettingsDialog.textPageBottom": "頁面底部", "DE.Views.NoteSettingsDialog.textSectEnd": "章節結尾", "DE.Views.NoteSettingsDialog.textSection": "目前章節", "DE.Views.NoteSettingsDialog.textStart": "從...開始", "DE.Views.NoteSettingsDialog.textTextBottom": "文字下方", "DE.Views.NoteSettingsDialog.textTitle": "筆記設定", "DE.Views.NotesRemoveDialog.textEnd": "刪除所有章節附註", "DE.Views.NotesRemoveDialog.textFoot": "刪除所有註腳", "DE.Views.NotesRemoveDialog.textTitle": "刪除註解", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "警告", "DE.Views.PageMarginsDialog.textBottom": "底部", "DE.Views.PageMarginsDialog.textGutter": "裝訂線", "DE.Views.PageMarginsDialog.textGutterPosition": "裝訂線位置", "DE.Views.PageMarginsDialog.textInside": "內部", "DE.Views.PageMarginsDialog.textLandscape": "橫向", "DE.Views.PageMarginsDialog.textLeft": "左", "DE.Views.PageMarginsDialog.textMirrorMargins": "鏡像邊界", "DE.Views.PageMarginsDialog.textMultiplePages": "多頁", "DE.Views.PageMarginsDialog.textNormal": "一般", "DE.Views.PageMarginsDialog.textOrientation": "方向", "DE.Views.PageMarginsDialog.textOutside": "外部", "DE.Views.PageMarginsDialog.textPortrait": "直向方向", "DE.Views.PageMarginsDialog.textPreview": "預覽", "DE.Views.PageMarginsDialog.textRight": "右", "DE.Views.PageMarginsDialog.textTitle": "邊框", "DE.Views.PageMarginsDialog.textTop": "頂部", "DE.Views.PageMarginsDialog.txtMarginsH": "頂部和底部的邊界對於給定的頁面高度太高了", "DE.Views.PageMarginsDialog.txtMarginsW": "給定頁面寬度下，左右邊界過寬", "DE.Views.PageSizeDialog.textHeight": "高度", "DE.Views.PageSizeDialog.textPreset": "預設值", "DE.Views.PageSizeDialog.textTitle": "頁面大小", "DE.Views.PageSizeDialog.textWidth": "寬度", "DE.Views.PageSizeDialog.txtCustom": "自訂", "DE.Views.PageThumbnails.textClosePanel": "關閉頁面縮略圖", "DE.Views.PageThumbnails.textHighlightVisiblePart": "突顯可見部分頁面", "DE.Views.PageThumbnails.textPageThumbnails": "頁面預覽圖", "DE.Views.PageThumbnails.textThumbnailsSettings": "縮圖設定", "DE.Views.PageThumbnails.textThumbnailsSize": "縮圖大小", "DE.Views.ParagraphSettings.strIndent": "縮排", "DE.Views.ParagraphSettings.strIndentsLeftText": "左", "DE.Views.ParagraphSettings.strIndentsRightText": "右", "DE.Views.ParagraphSettings.strIndentsSpecial": "特殊", "DE.Views.ParagraphSettings.strLineHeight": "行間距", "DE.Views.ParagraphSettings.strParagraphSpacing": "段落間距", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "不在相同樣式的段落間增加間距", "DE.Views.ParagraphSettings.strSpacingAfter": "之後", "DE.Views.ParagraphSettings.strSpacingBefore": "之前", "DE.Views.ParagraphSettings.textAdvanced": "顯示進階設定", "DE.Views.ParagraphSettings.textAt": "在", "DE.Views.ParagraphSettings.textAtLeast": "至少", "DE.Views.ParagraphSettings.textAuto": "多個", "DE.Views.ParagraphSettings.textBackColor": "背景顏色", "DE.Views.ParagraphSettings.textExact": "確切地", "DE.Views.ParagraphSettings.textFirstLine": "首行縮排", "DE.Views.ParagraphSettings.textHanging": "懸掛", "DE.Views.ParagraphSettings.textNoneSpecial": "（空）", "DE.Views.ParagraphSettings.txtAutoText": "自動", "DE.Views.ParagraphSettingsAdvanced.noTabs": "指定的標籤將出現在這個欄位中。", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "全部大寫", "DE.Views.ParagraphSettingsAdvanced.strBorders": "邊框和添入", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "分頁之前", "DE.Views.ParagraphSettingsAdvanced.strDirection": "Direction", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "雙刪除線", "DE.Views.ParagraphSettingsAdvanced.strIndent": "縮排", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行間距", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "大綱層級", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "右", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "之後", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "之前", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特殊", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "保持線條一致", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "與下一段連在一起", "DE.Views.ParagraphSettingsAdvanced.strMargins": "內距", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "孤立控制", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "字型", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "縮排與間距", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "換行和分頁符", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "放置", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小型大寫", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "不在相同樣式的段落間增加間距", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "間距", "DE.Views.ParagraphSettingsAdvanced.strStrike": "刪除線", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "下標", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "上標", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "隱藏行號", "DE.Views.ParagraphSettingsAdvanced.strTabs": "標籤", "DE.Views.ParagraphSettingsAdvanced.textAlign": "對齊", "DE.Views.ParagraphSettingsAdvanced.textAll": "全部", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "至少", "DE.Views.ParagraphSettingsAdvanced.textAuto": "多個", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "背景顏色", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "基本文字", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "邊框色彩", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "點擊圖表或使用按鈕選擇邊框以套用樣式", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "邊框大小", "DE.Views.ParagraphSettingsAdvanced.textBottom": "底部", "DE.Views.ParagraphSettingsAdvanced.textCentered": "置中對齊", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "字元間距", "DE.Views.ParagraphSettingsAdvanced.textContext": "上下文", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "上下文與推薦", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "上下文、歷史與推薦", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "上下文與歷史", "DE.Views.ParagraphSettingsAdvanced.textDefault": "預設定位點", "DE.Views.ParagraphSettingsAdvanced.textDirLtr": "Left-to-right", "DE.Views.ParagraphSettingsAdvanced.textDirRtl": "Right-to-left", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "選擇性", "DE.Views.ParagraphSettingsAdvanced.textEffects": "效果", "DE.Views.ParagraphSettingsAdvanced.textExact": "確切地", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "首行縮排", "DE.Views.ParagraphSettingsAdvanced.textHanging": "懸掛", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "歷史", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "歷史與任用的", "DE.Views.ParagraphSettingsAdvanced.textJustified": "兩端對齊", "DE.Views.ParagraphSettingsAdvanced.textLeader": "導引符", "DE.Views.ParagraphSettingsAdvanced.textLeft": "左", "DE.Views.ParagraphSettingsAdvanced.textLevel": "層級", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "連字", "DE.Views.ParagraphSettingsAdvanced.textNone": "無", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "（空）", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "OpenType 功能", "DE.Views.ParagraphSettingsAdvanced.textPosition": "位置", "DE.Views.ParagraphSettingsAdvanced.textRemove": "移除", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "移除所有", "DE.Views.ParagraphSettingsAdvanced.textRight": "右", "DE.Views.ParagraphSettingsAdvanced.textSet": "指定", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "間距", "DE.Views.ParagraphSettingsAdvanced.textStandard": "僅限標準", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "標準與上下文的", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "標準、上下文與任用的", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "標準、上下文與歷史的", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "標準和任意的", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "標準，歷史和任意的", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "標準和歷史的", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "中心", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "左", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "標籤位置", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "右", "DE.Views.ParagraphSettingsAdvanced.textTitle": "段落-進階設定", "DE.Views.ParagraphSettingsAdvanced.textTop": "頂部", "DE.Views.ParagraphSettingsAdvanced.tipAll": "設定外框和所有內部線", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "僅設定底部邊框", "DE.Views.ParagraphSettingsAdvanced.tipInner": "僅設定水平內部線", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "僅設定左邊框", "DE.Views.ParagraphSettingsAdvanced.tipNone": "設定無邊框", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "僅設定外框", "DE.Views.ParagraphSettingsAdvanced.tipRight": "僅設定右邊框", "DE.Views.ParagraphSettingsAdvanced.tipTop": "僅設定上邊框", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "自動", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "無邊框", "DE.Views.PrintWithPreview.textMarginsLast": "最後自訂", "DE.Views.PrintWithPreview.textMarginsModerate": "中等", "DE.Views.PrintWithPreview.textMarginsNarrow": "窄", "DE.Views.PrintWithPreview.textMarginsNormal": "一般", "DE.Views.PrintWithPreview.textMarginsWide": "寬", "DE.Views.PrintWithPreview.txtAllPages": "所有頁面", "DE.Views.PrintWithPreview.txtBothSides": "雙面列印", "DE.Views.PrintWithPreview.txtBothSidesLongDesc": "沿長邊翻轉頁面", "DE.Views.PrintWithPreview.txtBothSidesShortDesc": "沿短邊翻轉頁面", "DE.Views.PrintWithPreview.txtBottom": "底部", "DE.Views.PrintWithPreview.txtCopies": "副本", "DE.Views.PrintWithPreview.txtCurrentPage": "目前頁面", "DE.Views.PrintWithPreview.txtCustom": "自訂", "DE.Views.PrintWithPreview.txtCustomPages": "自訂列印", "DE.Views.PrintWithPreview.txtLandscape": "橫向", "DE.Views.PrintWithPreview.txtLeft": "左", "DE.Views.PrintWithPreview.txtMargins": "邊框", "DE.Views.PrintWithPreview.txtOf": "共 {0} 頁", "DE.Views.PrintWithPreview.txtOneSide": "單面列印", "DE.Views.PrintWithPreview.txtOneSideDesc": "僅在頁面的一側進行列印", "DE.Views.PrintWithPreview.txtPage": "頁面", "DE.Views.PrintWithPreview.txtPageNumInvalid": "頁碼無效", "DE.Views.PrintWithPreview.txtPageOrientation": "頁面方向", "DE.Views.PrintWithPreview.txtPages": "頁數", "DE.Views.PrintWithPreview.txtPageSize": "頁面大小", "DE.Views.PrintWithPreview.txtPortrait": "肖像", "DE.Views.PrintWithPreview.txtPrint": "打印", "DE.Views.PrintWithPreview.txtPrintPdf": "列印為 PDF", "DE.Views.PrintWithPreview.txtPrintRange": "列印範圍", "DE.Views.PrintWithPreview.txtPrintSides": "列印面", "DE.Views.PrintWithPreview.txtRight": "右", "DE.Views.PrintWithPreview.txtSelection": "選擇", "DE.Views.PrintWithPreview.txtTop": "頂部", "DE.Views.ProtectDialog.textComments": "評論", "DE.Views.ProtectDialog.textForms": "填寫表單", "DE.Views.ProtectDialog.textReview": "已追蹤的變更", "DE.Views.ProtectDialog.textView": "沒有變更（唯讀）", "DE.Views.ProtectDialog.txtAllow": "僅允許在文件中進行此類編輯", "DE.Views.ProtectDialog.txtIncorrectPwd": "確認密碼不相同", "DE.Views.ProtectDialog.txtLimit": "密碼限制為15字元", "DE.Views.ProtectDialog.txtOptional": "可選的", "DE.Views.ProtectDialog.txtPassword": "密碼", "DE.Views.ProtectDialog.txtProtect": "保護", "DE.Views.ProtectDialog.txtRepeat": "重複輸入密碼", "DE.Views.ProtectDialog.txtTitle": "保護", "DE.Views.ProtectDialog.txtWarning": "警告：如果您遺失或忘記密碼，將無法恢復。請將密碼保存在安全的地方。", "DE.Views.RightMenu.ariaRightMenu": "Right menu", "DE.Views.RightMenu.txtChartSettings": "圖表設定", "DE.Views.RightMenu.txtFormSettings": "表單設定", "DE.Views.RightMenu.txtHeaderFooterSettings": "頁首和頁尾設定", "DE.Views.RightMenu.txtImageSettings": "圖片設定", "DE.Views.RightMenu.txtMailMergeSettings": "郵件合併設定", "DE.Views.RightMenu.txtParagraphSettings": "段落設定", "DE.Views.RightMenu.txtShapeSettings": "形狀設定", "DE.Views.RightMenu.txtSignatureSettings": "簽名設定", "DE.Views.RightMenu.txtTableSettings": "表格設定", "DE.Views.RightMenu.txtTextArtSettings": "文字藝術設定", "DE.Views.RoleDeleteDlg.textLabel": "要刪除此角色，您需要將與之關聯的欄位移至其他角色。", "DE.Views.RoleDeleteDlg.textSelect": "選擇欄位合併角色", "DE.Views.RoleDeleteDlg.textTitle": "刪除角色", "DE.Views.RoleEditDlg.errNameExists": "已存在具有該名稱的角色。", "DE.Views.RoleEditDlg.textEmptyError": "角色名稱不能為空。", "DE.Views.RoleEditDlg.textName": "角色名稱", "DE.Views.RoleEditDlg.textNameEx": "範例:申請人、用戶、銷售代表", "DE.Views.RoleEditDlg.textNoHighlight": "無醒目標示", "DE.Views.RoleEditDlg.txtTitleEdit": "編輯角色", "DE.Views.RoleEditDlg.txtTitleNew": "创建新角色", "DE.Views.RolesManagerDlg.textAnyone": "任何人", "DE.Views.RolesManagerDlg.textDelete": "刪除", "DE.Views.RolesManagerDlg.textDeleteLast": "您確定要刪除角色 {0} 嗎？<br>刪除後，將建立預設角色。", "DE.Views.RolesManagerDlg.textDescription": "新增角色並設定填表人接收並簽署文件的順序", "DE.Views.RolesManagerDlg.textDown": "向下移動角色", "DE.Views.RolesManagerDlg.textEdit": "編輯", "DE.Views.RolesManagerDlg.textEmpty": "尚未建立任何角色。<br>請至少建立一個角色，它將顯示在此欄位中。", "DE.Views.RolesManagerDlg.textNew": "新增", "DE.Views.RolesManagerDlg.textUp": "向上移動角色", "DE.Views.RolesManagerDlg.txtTitle": "管理角色", "DE.Views.RolesManagerDlg.warnCantDelete": "您無法刪除此角色，因為它有相關聯的欄位。", "DE.Views.RolesManagerDlg.warnDelete": "您確定要刪除角色 {0} 嗎？", "DE.Views.SaveFormDlg.saveButtonText": "儲存", "DE.Views.SaveFormDlg.textAnyone": "任何人", "DE.Views.SaveFormDlg.textDescription": "在保存為PDF時，只有帶有字段的角色才會添加到填充清單中", "DE.Views.SaveFormDlg.textEmpty": "沒有與欄位有關聯的角色。", "DE.Views.SaveFormDlg.textFill": "填充清單", "DE.Views.SaveFormDlg.txtTitle": "另存為表單", "DE.Views.ShapeSettings.strBackground": "背景顏色", "DE.Views.ShapeSettings.strChange": "變更形狀", "DE.Views.ShapeSettings.strColor": "顏色", "DE.Views.ShapeSettings.strFill": "填入", "DE.Views.ShapeSettings.strForeground": "前景顏色", "DE.Views.ShapeSettings.strPattern": "圖案", "DE.Views.ShapeSettings.strShadow": "顯示陰影", "DE.Views.ShapeSettings.strSize": "大小", "DE.Views.ShapeSettings.strStroke": "折線圖", "DE.Views.ShapeSettings.strTransparency": "透明度", "DE.Views.ShapeSettings.strType": "類型", "DE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "DE.Views.ShapeSettings.textAdvanced": "顯示進階設定", "DE.Views.ShapeSettings.textAngle": "角度", "DE.Views.ShapeSettings.textBorderSizeErr": "輸入的值不正確。<br>請輸入介於0pt和1584pt之間的值。", "DE.Views.ShapeSettings.textColor": "填充顏色", "DE.Views.ShapeSettings.textDirection": "方向", "DE.Views.ShapeSettings.textEditPoints": "編輯點", "DE.Views.ShapeSettings.textEditShape": "編輯外框", "DE.Views.ShapeSettings.textEmptyPattern": "無圖案", "DE.Views.ShapeSettings.textEyedropper": "Eyedropper", "DE.Views.ShapeSettings.textFlip": "翻轉", "DE.Views.ShapeSettings.textFromFile": "從檔案", "DE.Views.ShapeSettings.textFromStorage": "從儲存空間", "DE.Views.ShapeSettings.textFromUrl": "從網址", "DE.Views.ShapeSettings.textGradient": "漸層點", "DE.Views.ShapeSettings.textGradientFill": "漸層填充", "DE.Views.ShapeSettings.textHint270": "逆時針旋轉90°", "DE.Views.ShapeSettings.textHint90": "順時針旋轉90°", "DE.Views.ShapeSettings.textHintFlipH": "水平翻轉", "DE.Views.ShapeSettings.textHintFlipV": "垂直翻轉", "DE.Views.ShapeSettings.textImageTexture": "圖片或紋理", "DE.Views.ShapeSettings.textLinear": "線性的", "DE.Views.ShapeSettings.textMoreColors": "More colors", "DE.Views.ShapeSettings.textNoFill": "無填充", "DE.Views.ShapeSettings.textNoShadow": "No Shadow", "DE.Views.ShapeSettings.textPatternFill": "圖案", "DE.Views.ShapeSettings.textPosition": "位置", "DE.Views.ShapeSettings.textRadial": "放射狀", "DE.Views.ShapeSettings.textRecentlyUsed": "最近使用", "DE.Views.ShapeSettings.textRotate90": "旋轉90°", "DE.Views.ShapeSettings.textRotation": "旋轉", "DE.Views.ShapeSettings.textSelectImage": "選擇圖片", "DE.Views.ShapeSettings.textSelectTexture": "選擇", "DE.Views.ShapeSettings.textShadow": "Shadow", "DE.Views.ShapeSettings.textStretch": "延伸", "DE.Views.ShapeSettings.textStyle": "樣式", "DE.Views.ShapeSettings.textTexture": "從紋理", "DE.Views.ShapeSettings.textTile": "磚瓦", "DE.Views.ShapeSettings.textWrap": "換行樣式", "DE.Views.ShapeSettings.tipAddGradientPoint": "新增漸變點", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "移除漸層點", "DE.Views.ShapeSettings.txtBehind": "文字在後", "DE.Views.ShapeSettings.txtBrownPaper": "棕色紙張", "DE.Views.ShapeSettings.txtCanvas": "畫布", "DE.Views.ShapeSettings.txtCarton": "紙盒", "DE.Views.ShapeSettings.txtDarkFabric": "深色布料", "DE.Views.ShapeSettings.txtGrain": "紋理", "DE.Views.ShapeSettings.txtGranite": "花崗岩", "DE.Views.ShapeSettings.txtGreyPaper": "灰色紙張", "DE.Views.ShapeSettings.txtInFront": "置於文字前方", "DE.Views.ShapeSettings.txtInline": "與文字對齊", "DE.Views.ShapeSettings.txtKnit": "編織", "DE.Views.ShapeSettings.txtLeather": "皮革", "DE.Views.ShapeSettings.txtNoBorders": "無線條", "DE.Views.ShapeSettings.txtPapyrus": "帛書字體", "DE.Views.ShapeSettings.txtSquare": "方形", "DE.Views.ShapeSettings.txtThrough": "透過", "DE.Views.ShapeSettings.txtTight": "緊密", "DE.Views.ShapeSettings.txtTopAndBottom": "頂部和底部", "DE.Views.ShapeSettings.txtWood": "木頭", "DE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "DE.Views.SignatureSettings.strDelete": "移除簽名", "DE.Views.SignatureSettings.strDetails": "簽名詳細資訊", "DE.Views.SignatureSettings.strInvalid": "無效的簽名", "DE.Views.SignatureSettings.strRequested": "要求簽名", "DE.Views.SignatureSettings.strSetup": "簽名設定", "DE.Views.SignatureSettings.strSign": "簽名", "DE.Views.SignatureSettings.strSignature": "簽名", "DE.Views.SignatureSettings.strSigner": "簽署者", "DE.Views.SignatureSettings.strValid": "有效簽名", "DE.Views.SignatureSettings.txtContinueEditing": "無論如何編輯", "DE.Views.SignatureSettings.txtEditWarning": "編輯將從文件中刪除簽名<br>是否繼續？", "DE.Views.SignatureSettings.txtRemoveWarning": "您是否要刪除此簽名？<br>此操作無法撤消。", "DE.Views.SignatureSettings.txtRequestedSignatures": "該文件需要簽名。", "DE.Views.SignatureSettings.txtSigned": "有效簽名已添加到文件中。文件已受到編輯保護。", "DE.Views.SignatureSettings.txtSignedInvalid": "文件中的一些數位簽章無效或無法驗證。該文件受到保護，無法編輯。", "DE.Views.Statusbar.goToPageText": "前往頁面", "DE.Views.Statusbar.pageIndexText": "第{0}頁，共{1}頁", "DE.Views.Statusbar.tipFitPage": "調整至頁面大小", "DE.Views.Statusbar.tipFitWidth": "調整至寬度大小", "DE.Views.Statusbar.tipHandTool": "手動工具", "DE.Views.Statusbar.tipSelectTool": "選擇工具", "DE.Views.Statusbar.tipSetLang": "設定文字語言", "DE.Views.Statusbar.tipZoomFactor": "縮放", "DE.Views.Statusbar.tipZoomIn": "放大", "DE.Views.Statusbar.tipZoomOut": "縮小", "DE.Views.Statusbar.txtPageNumInvalid": "頁碼無效", "DE.Views.Statusbar.txtPages": "頁數", "DE.Views.Statusbar.txtParagraphs": "段落數", "DE.Views.Statusbar.txtSpaces": "帶有空格的符號", "DE.Views.Statusbar.txtSymbols": "符號", "DE.Views.Statusbar.txtWordCount": "字數統計", "DE.Views.Statusbar.txtWords": "字數", "DE.Views.StyleTitleDialog.textHeader": "創建新樣式", "DE.Views.StyleTitleDialog.textNextStyle": "下一個段落樣式", "DE.Views.StyleTitleDialog.textTitle": "標題", "DE.Views.StyleTitleDialog.txtEmpty": "此欄位為必填欄位", "DE.Views.StyleTitleDialog.txtNotEmpty": "欄位不能為空白", "DE.Views.StyleTitleDialog.txtSameAs": "與新建樣式相同", "DE.Views.TableFormulaDialog.textBookmark": "粘貼書籤", "DE.Views.TableFormulaDialog.textFormat": "數字格式", "DE.Views.TableFormulaDialog.textFormula": "公式", "DE.Views.TableFormulaDialog.textInsertFunction": "粘貼功能", "DE.Views.TableFormulaDialog.textTitle": "公式設定", "DE.Views.TableOfContentsSettings.strAlign": "靠右對齊頁碼", "DE.Views.TableOfContentsSettings.strFullCaption": "包括標籤和編號", "DE.Views.TableOfContentsSettings.strLinks": "將圖表目錄格式化為連結", "DE.Views.TableOfContentsSettings.strLinksOF": "將圖表目錄格式化為連結", "DE.Views.TableOfContentsSettings.strShowPages": "顯示頁碼", "DE.Views.TableOfContentsSettings.textBuildTable": "從建立目錄", "DE.Views.TableOfContentsSettings.textBuildTableOF": "從建立圖表目錄", "DE.Views.TableOfContentsSettings.textEquation": "方程式", "DE.Views.TableOfContentsSettings.textFigure": "圖表", "DE.Views.TableOfContentsSettings.textLeader": "導引符", "DE.Views.TableOfContentsSettings.textLevel": "層級", "DE.Views.TableOfContentsSettings.textLevels": "層級", "DE.Views.TableOfContentsSettings.textNone": "無", "DE.Views.TableOfContentsSettings.textRadioCaption": "標題", "DE.Views.TableOfContentsSettings.textRadioLevels": "大綱層級", "DE.Views.TableOfContentsSettings.textRadioStyle": "樣式", "DE.Views.TableOfContentsSettings.textRadioStyles": "選擇樣式", "DE.Views.TableOfContentsSettings.textStyle": "樣式", "DE.Views.TableOfContentsSettings.textStyles": "樣式", "DE.Views.TableOfContentsSettings.textTable": "表格", "DE.Views.TableOfContentsSettings.textTitle": "目錄", "DE.Views.TableOfContentsSettings.textTitleTOF": "圖表目錄", "DE.Views.TableOfContentsSettings.txtCentered": "置中對齊", "DE.Views.TableOfContentsSettings.txtClassic": "經典", "DE.Views.TableOfContentsSettings.txtCurrent": "目前", "DE.Views.TableOfContentsSettings.txtDistinctive": "獨特的", "DE.Views.TableOfContentsSettings.txtFormal": "正式的", "DE.Views.TableOfContentsSettings.txtModern": "現代", "DE.Views.TableOfContentsSettings.txtOnline": "上線", "DE.Views.TableOfContentsSettings.txtSimple": "簡單", "DE.Views.TableOfContentsSettings.txtStandard": "標準", "DE.Views.TableSettings.deleteColumnText": "刪除欄", "DE.Views.TableSettings.deleteRowText": "刪除列", "DE.Views.TableSettings.deleteTableText": "刪除表格", "DE.Views.TableSettings.insertColumnLeftText": "在左側插入欄", "DE.Views.TableSettings.insertColumnRightText": "在右側插入欄", "DE.Views.TableSettings.insertRowAboveText": "在上方插入列", "DE.Views.TableSettings.insertRowBelowText": "在下方插入列", "DE.Views.TableSettings.mergeCellsText": "合併儲存格", "DE.Views.TableSettings.selectCellText": "選擇儲存格", "DE.Views.TableSettings.selectColumnText": "選擇欄", "DE.Views.TableSettings.selectRowText": "選擇列", "DE.Views.TableSettings.selectTableText": "選擇表格", "DE.Views.TableSettings.splitCellsText": "分割儲存格...", "DE.Views.TableSettings.splitCellTitleText": "分割儲存格", "DE.Views.TableSettings.strRepeatRow": "在每頁的最上層顯示標題列", "DE.Views.TableSettings.textAddFormula": "插入函數", "DE.Views.TableSettings.textAdvanced": "顯示進階設定", "DE.Views.TableSettings.textBackColor": "背景顏色", "DE.Views.TableSettings.textBanded": "分隔", "DE.Views.TableSettings.textBorderColor": "顏色", "DE.Views.TableSettings.textBorders": "邊框風格", "DE.Views.TableSettings.textCellSize": "列與欄大小", "DE.Views.TableSettings.textColumns": "欄", "DE.Views.TableSettings.textConvert": "將表格轉換為文字", "DE.Views.TableSettings.textDistributeCols": "分佈欄", "DE.Views.TableSettings.textDistributeRows": "分佈列", "DE.Views.TableSettings.textEdit": "列與欄", "DE.Views.TableSettings.textEmptyTemplate": "沒有範本", "DE.Views.TableSettings.textFirst": "第一個", "DE.Views.TableSettings.textHeader": "頁首", "DE.Views.TableSettings.textHeight": "高度", "DE.Views.TableSettings.textLast": "最後", "DE.Views.TableSettings.textRows": "列", "DE.Views.TableSettings.textSelectBorders": "選擇要套用上方選定樣式的邊框", "DE.Views.TableSettings.textTemplate": "從範本中選擇", "DE.Views.TableSettings.textTotal": "總計", "DE.Views.TableSettings.textWidth": "寬度", "DE.Views.TableSettings.tipAll": "設定外框和所有內部線", "DE.Views.TableSettings.tipBottom": "僅設定外部底部邊框", "DE.Views.TableSettings.tipInner": "僅設定內部線", "DE.Views.TableSettings.tipInnerHor": "僅設定水平內部線", "DE.Views.TableSettings.tipInnerVert": "僅設定內部垂直線", "DE.Views.TableSettings.tipLeft": "僅設定外部左邊框", "DE.Views.TableSettings.tipNone": "設定無邊框", "DE.Views.TableSettings.tipOuter": "僅設定外框", "DE.Views.TableSettings.tipRight": "僅設定外部右邊框", "DE.Views.TableSettings.tipTop": "僅設定外部上邊框", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "有邊框和線條的表格", "DE.Views.TableSettings.txtGroupTable_Custom": "自訂", "DE.Views.TableSettings.txtGroupTable_Grid": "格狀表格", "DE.Views.TableSettings.txtGroupTable_List": "清單表格", "DE.Views.TableSettings.txtGroupTable_Plain": "純文字表格", "DE.Views.TableSettings.txtNoBorders": "無邊框", "DE.Views.TableSettings.txtTable_Accent": "口音", "DE.Views.TableSettings.txtTable_Bordered": "有邊框的", "DE.Views.TableSettings.txtTable_BorderedAndLined": "有邊框和線條的", "DE.Views.TableSettings.txtTable_Colorful": "豐富多彩的", "DE.Views.TableSettings.txtTable_Dark": "深色", "DE.Views.TableSettings.txtTable_GridTable": "格狀表格", "DE.Views.TableSettings.txtTable_Light": "淺色", "DE.Views.TableSettings.txtTable_Lined": "有格線的", "DE.Views.TableSettings.txtTable_ListTable": "清單表格", "DE.Views.TableSettings.txtTable_PlainTable": "純文字表格", "DE.Views.TableSettings.txtTable_TableGrid": "表格網格", "DE.Views.TableSettingsAdvanced.textAlign": "對齊", "DE.Views.TableSettingsAdvanced.textAlignment": "對齊", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "儲存格間距", "DE.Views.TableSettingsAdvanced.textAlt": "替代文字", "DE.Views.TableSettingsAdvanced.textAltDescription": "描述", "DE.Views.TableSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "DE.Views.TableSettingsAdvanced.textAltTitle": "標題", "DE.Views.TableSettingsAdvanced.textAnchorText": "文字", "DE.Views.TableSettingsAdvanced.textAutofit": "自動調整大小以符合內容", "DE.Views.TableSettingsAdvanced.textBackColor": "儲存格背景", "DE.Views.TableSettingsAdvanced.textBelow": "下方", "DE.Views.TableSettingsAdvanced.textBorderColor": "邊框色彩", "DE.Views.TableSettingsAdvanced.textBorderDesc": "點擊圖表或使用按鈕選擇邊框以套用樣式", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "邊框和背景", "DE.Views.TableSettingsAdvanced.textBorderWidth": "邊框大小", "DE.Views.TableSettingsAdvanced.textBottom": "底部", "DE.Views.TableSettingsAdvanced.textCellOptions": "儲存格選項", "DE.Views.TableSettingsAdvanced.textCellProps": "儲存格", "DE.Views.TableSettingsAdvanced.textCellSize": "儲存格大小", "DE.Views.TableSettingsAdvanced.textCenter": "置中", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "中心", "DE.Views.TableSettingsAdvanced.textCheckMargins": "使用預設邊界", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "預設儲存格邊界", "DE.Views.TableSettingsAdvanced.textDistance": "與文字的距離", "DE.Views.TableSettingsAdvanced.textHorizontal": "水平", "DE.Views.TableSettingsAdvanced.textIndLeft": "從左縮進", "DE.Views.TableSettingsAdvanced.textLeft": "左", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "左", "DE.Views.TableSettingsAdvanced.textMargin": "邊框", "DE.Views.TableSettingsAdvanced.textMargins": "儲存格邊界", "DE.Views.TableSettingsAdvanced.textMeasure": "以...為單位", "DE.Views.TableSettingsAdvanced.textMove": "隨文字移動物件", "DE.Views.TableSettingsAdvanced.textOnlyCells": "僅適用於選定的儲存格", "DE.Views.TableSettingsAdvanced.textOptions": "選項", "DE.Views.TableSettingsAdvanced.textOverlap": "允許重疊", "DE.Views.TableSettingsAdvanced.textPage": "頁面", "DE.Views.TableSettingsAdvanced.textPosition": "位置", "DE.Views.TableSettingsAdvanced.textPrefWidth": "首選寬度", "DE.Views.TableSettingsAdvanced.textPreview": "預覽", "DE.Views.TableSettingsAdvanced.textRelative": "關係到", "DE.Views.TableSettingsAdvanced.textRight": "右", "DE.Views.TableSettingsAdvanced.textRightOf": "在右側", "DE.Views.TableSettingsAdvanced.textRightTooltip": "右", "DE.Views.TableSettingsAdvanced.textTable": "表格", "DE.Views.TableSettingsAdvanced.textTableBackColor": "表格背景", "DE.Views.TableSettingsAdvanced.textTablePosition": "表格位置", "DE.Views.TableSettingsAdvanced.textTableSize": "表格大小", "DE.Views.TableSettingsAdvanced.textTitle": "表格 - 進階設定", "DE.Views.TableSettingsAdvanced.textTop": "頂部", "DE.Views.TableSettingsAdvanced.textVertical": "垂直", "DE.Views.TableSettingsAdvanced.textWidth": "寬度", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "寬度與間距", "DE.Views.TableSettingsAdvanced.textWrap": "文字包裝", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "內嵌表格", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "流程圖：交替處理", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "換行樣式", "DE.Views.TableSettingsAdvanced.textWrapText": "換行文字", "DE.Views.TableSettingsAdvanced.tipAll": "設定外框和所有內部線", "DE.Views.TableSettingsAdvanced.tipCellAll": "僅為內部儲存格設定邊框", "DE.Views.TableSettingsAdvanced.tipCellInner": "僅設定內部儲存格的垂直和水平線", "DE.Views.TableSettingsAdvanced.tipCellOuter": "僅設定內部儲存格的外框", "DE.Views.TableSettingsAdvanced.tipInner": "僅設定內部線", "DE.Views.TableSettingsAdvanced.tipNone": "設定無邊框", "DE.Views.TableSettingsAdvanced.tipOuter": "僅設定外框", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "設定外框和所有內部線", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "設定外框和內部儲存格的垂直和水平線", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "設定表格的外框和內部儲存格的外框", "DE.Views.TableSettingsAdvanced.txtCm": "公分", "DE.Views.TableSettingsAdvanced.txtInch": "英寸", "DE.Views.TableSettingsAdvanced.txtNoBorders": "無邊框", "DE.Views.TableSettingsAdvanced.txtPercent": "百分比", "DE.Views.TableSettingsAdvanced.txtPt": "點", "DE.Views.TableToTextDialog.textEmpty": "您必須輸入自訂分隔符號的字元。", "DE.Views.TableToTextDialog.textNested": "轉換嵌套表格", "DE.Views.TableToTextDialog.textOther": "其它", "DE.Views.TableToTextDialog.textPara": "段落標記", "DE.Views.TableToTextDialog.textSemicolon": "分號", "DE.Views.TableToTextDialog.textSeparator": "用...分隔文字", "DE.Views.TableToTextDialog.textTab": "標籤", "DE.Views.TableToTextDialog.textTitle": "將表格轉換為文字", "DE.Views.TextArtSettings.strColor": "顏色", "DE.Views.TextArtSettings.strFill": "填入", "DE.Views.TextArtSettings.strSize": "大小", "DE.Views.TextArtSettings.strStroke": "折線圖", "DE.Views.TextArtSettings.strTransparency": "透明度", "DE.Views.TextArtSettings.strType": "類型", "DE.Views.TextArtSettings.textAngle": "角度", "DE.Views.TextArtSettings.textBorderSizeErr": "輸入的值不正確。<br>請輸入介於0pt和1584pt之間的值。", "DE.Views.TextArtSettings.textColor": "填充顏色", "DE.Views.TextArtSettings.textDirection": "方向", "DE.Views.TextArtSettings.textGradient": "漸層點", "DE.Views.TextArtSettings.textGradientFill": "漸層填充", "DE.Views.TextArtSettings.textLinear": "線性的", "DE.Views.TextArtSettings.textNoFill": "無填充", "DE.Views.TextArtSettings.textPosition": "位置", "DE.Views.TextArtSettings.textRadial": "放射狀", "DE.Views.TextArtSettings.textSelectTexture": "選擇", "DE.Views.TextArtSettings.textStyle": "樣式", "DE.Views.TextArtSettings.textTemplate": "範本", "DE.Views.TextArtSettings.textTransform": "轉換", "DE.Views.TextArtSettings.tipAddGradientPoint": "新增漸變點", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "移除漸層點", "DE.Views.TextArtSettings.txtNoBorders": "無線條", "DE.Views.TextToTableDialog.textAutofit": "自動調整行為", "DE.Views.TextToTableDialog.textColumns": "欄", "DE.Views.TextToTableDialog.textContents": "自動調整為內容大小", "DE.Views.TextToTableDialog.textEmpty": "您必須輸入自訂分隔符號的字元。", "DE.Views.TextToTableDialog.textFixed": "固定欄寬", "DE.Views.TextToTableDialog.textOther": "其它", "DE.Views.TextToTableDialog.textPara": "段落數", "DE.Views.TextToTableDialog.textRows": "列", "DE.Views.TextToTableDialog.textSemicolon": "分號", "DE.Views.TextToTableDialog.textSeparator": "文字分隔從", "DE.Views.TextToTableDialog.textTab": "標籤", "DE.Views.TextToTableDialog.textTableSize": "表格大小", "DE.Views.TextToTableDialog.textTitle": "將文字轉換為表格", "DE.Views.TextToTableDialog.textWindow": "自動調整為視窗大小", "DE.Views.TextToTableDialog.txtAutoText": "自動", "DE.Views.Toolbar.capBtnAddComment": "新增註解", "DE.Views.Toolbar.capBtnBlankPage": "空白頁面", "DE.Views.Toolbar.capBtnColumns": "欄", "DE.Views.Toolbar.capBtnComment": "評論", "DE.Views.Toolbar.capBtnDateTime": "日期和時間", "DE.Views.Toolbar.capBtnHand": "Hand", "DE.Views.Toolbar.capBtnHyphenation": "Hyphenation", "DE.Views.Toolbar.capBtnInsChart": "圖表", "DE.Views.Toolbar.capBtnInsControls": "內容控制項", "DE.Views.Toolbar.capBtnInsDropcap": "首字大寫", "DE.Views.Toolbar.capBtnInsEquation": "方程式", "DE.Views.Toolbar.capBtnInsField": "Field", "DE.Views.Toolbar.capBtnInsHeader": "頁首和頁尾", "DE.Views.Toolbar.capBtnInsImage": "圖片", "DE.Views.Toolbar.capBtnInsPagebreak": "分頁符號", "DE.Views.Toolbar.capBtnInsShape": "形狀", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "符號", "DE.Views.Toolbar.capBtnInsTable": "表格", "DE.Views.Toolbar.capBtnInsTextart": "文字藝術", "DE.Views.Toolbar.capBtnInsTextbox": "文字方塊", "DE.Views.Toolbar.capBtnInsTextFromFile": "Text from File", "DE.Views.Toolbar.capBtnLineNumbers": "行號", "DE.Views.Toolbar.capBtnMargins": "邊框", "DE.Views.Toolbar.capBtnPageColor": "Page Color", "DE.Views.Toolbar.capBtnPageOrient": "方向", "DE.Views.Toolbar.capBtnPageSize": "大小", "DE.Views.Toolbar.capBtnSelect": "Select", "DE.Views.Toolbar.capBtnWatermark": "浮水印", "DE.Views.Toolbar.capColorScheme": "Colors", "DE.Views.Toolbar.capImgAlign": "對齊", "DE.Views.Toolbar.capImgBackward": "向後發送", "DE.Views.Toolbar.capImgForward": "向前移動", "DE.Views.Toolbar.capImgGroup": "群組", "DE.Views.Toolbar.capImgWrapping": "換行", "DE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "DE.Views.Toolbar.mniCapitalizeWords": "每個單詞首字母大寫", "DE.Views.Toolbar.mniCustomTable": "插入自訂表格", "DE.Views.Toolbar.mniDrawTable": "繪製表格", "DE.Views.Toolbar.mniEditControls": "控制設定", "DE.Views.Toolbar.mniEditDropCap": "首字大寫設定", "DE.Views.Toolbar.mniEditFooter": "編輯頁尾", "DE.Views.Toolbar.mniEditHeader": "編輯頁首", "DE.Views.Toolbar.mniEraseTable": "刪除表格", "DE.Views.Toolbar.mniFromFile": "從檔案", "DE.Views.Toolbar.mniFromStorage": "從儲存空間", "DE.Views.Toolbar.mniFromUrl": "從網址", "DE.Views.Toolbar.mniHiddenBorders": "隱藏底部邊框", "DE.Views.Toolbar.mniHiddenChars": "顯示/隱藏編輯標記", "DE.Views.Toolbar.mniHighlightControls": "突顯設定", "DE.Views.Toolbar.mniImageFromFile": "從檔案插入圖片", "DE.Views.Toolbar.mniImageFromStorage": "從儲存空間插入圖片", "DE.Views.Toolbar.mniImageFromUrl": "從網址插入圖片", "DE.Views.Toolbar.mniInsertSSE": "插入試算表", "DE.Views.Toolbar.mniLowerCase": "小寫", "DE.Views.Toolbar.mniRemoveFooter": "移除頁腳", "DE.Views.Toolbar.mniRemoveHeader": "移除頁首", "DE.Views.Toolbar.mniSentenceCase": "句首大寫", "DE.Views.Toolbar.mniTextFromLocalFile": "Text from the local file", "DE.Views.Toolbar.mniTextFromStorage": "Text from the storage file", "DE.Views.Toolbar.mniTextFromURL": "Text from the URL file", "DE.Views.Toolbar.mniTextToTable": "將文字轉換為表格", "DE.Views.Toolbar.mniToggleCase": "切換大小寫", "DE.Views.Toolbar.mniUpperCase": "大寫", "DE.Views.Toolbar.strMenuNoFill": "無填充", "DE.Views.Toolbar.textAddSpaceAfter": "Add space after paragraph", "DE.Views.Toolbar.textAddSpaceBefore": "Add space before paragraph", "DE.Views.Toolbar.textAlpha": "小寫", "DE.Views.Toolbar.textAuto": "自動", "DE.Views.Toolbar.textAutoColor": "自動", "DE.Views.Toolbar.textBetta": "β", "DE.Views.Toolbar.textBlackHeart": "黑桃心", "DE.Views.Toolbar.textBold": "粗體", "DE.Views.Toolbar.textBottom": "底部：", "DE.Views.Toolbar.textBullet": "項目符號", "DE.Views.Toolbar.textChangeLevel": "變更清單層級", "DE.Views.Toolbar.textCheckboxControl": "核取方塊", "DE.Views.Toolbar.textColumnsCustom": "自訂欄位", "DE.Views.Toolbar.textColumnsLeft": "左", "DE.Views.Toolbar.textColumnsOne": "一個", "DE.Views.Toolbar.textColumnsRight": "右", "DE.Views.Toolbar.textColumnsThree": "三", "DE.Views.Toolbar.textColumnsTwo": "兩個", "DE.Views.Toolbar.textComboboxControl": "下拉式方框", "DE.Views.Toolbar.textContinuous": "連續", "DE.Views.Toolbar.textContPage": "連續頁面", "DE.Views.Toolbar.textCopyright": "版權符號", "DE.Views.Toolbar.textCustomHyphen": "Hyphenation options", "DE.Views.Toolbar.textCustomLineNumbers": "行號選項", "DE.Views.Toolbar.textDateControl": "日期", "DE.Views.Toolbar.textDegree": "溫度符號", "DE.Views.Toolbar.textDelta": "δ", "DE.Views.Toolbar.textDirLtr": "Left-to-right", "DE.Views.Toolbar.textDirRtl": "Right-to-left", "DE.Views.Toolbar.textDivision": "除號", "DE.Views.Toolbar.textDollar": "美元符號", "DE.Views.Toolbar.textDropdownControl": "下拉式清單", "DE.Views.Toolbar.textEditMode": "Edit PDF", "DE.Views.Toolbar.textEditWatermark": "自訂浮水印", "DE.Views.Toolbar.textEuro": "歐元符號", "DE.Views.Toolbar.textEvenPage": "偶數頁", "DE.Views.Toolbar.textGreaterEqual": "大於或等於", "DE.Views.Toolbar.textIndAfter": "Indent after", "DE.Views.Toolbar.textIndBefore": "Indent before", "DE.Views.Toolbar.textIndLeft": "Left indent", "DE.Views.Toolbar.textIndRight": "Right indent", "DE.Views.Toolbar.textInfinity": "無窮大", "DE.Views.Toolbar.textInMargin": "在邊界內", "DE.Views.Toolbar.textInsColumnBreak": "插入分欄符", "DE.Views.Toolbar.textInsertPageCount": "插入頁數", "DE.Views.Toolbar.textInsertPageNumber": "插入頁碼", "DE.Views.Toolbar.textInsPageBreak": "插入頁碼", "DE.Views.Toolbar.textInsSectionBreak": "插入分節符", "DE.Views.Toolbar.textInText": "在文字中", "DE.Views.Toolbar.textItalic": "斜體", "DE.Views.Toolbar.textLandscape": "橫向", "DE.Views.Toolbar.textLeft": "左：", "DE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "DE.Views.Toolbar.textLetterPi": "π", "DE.Views.Toolbar.textLineSpaceOptions": "Line spacing options", "DE.Views.Toolbar.textListSettings": "清單設定", "DE.Views.Toolbar.textMarginsLast": "最後自訂", "DE.Views.Toolbar.textMarginsModerate": "中等", "DE.Views.Toolbar.textMarginsNarrow": "窄", "DE.Views.Toolbar.textMarginsNormal": "一般", "DE.Views.Toolbar.textMarginsWide": "寬", "DE.Views.Toolbar.textMoreSymbols": "More symbols", "DE.Views.Toolbar.textNewColor": "更多顏色", "DE.Views.Toolbar.textNextPage": "下一頁", "DE.Views.Toolbar.textNoHighlight": "無醒目標示", "DE.Views.Toolbar.textNone": "無", "DE.Views.Toolbar.textNotEqualTo": "不等於", "DE.Views.Toolbar.textOddPage": "奇數頁", "DE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "DE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "DE.Views.Toolbar.textPageMarginsCustom": "自訂邊界", "DE.Views.Toolbar.textPageSizeCustom": "自訂頁面大小", "DE.Views.Toolbar.textPictureControl": "圖片", "DE.Views.Toolbar.textPlainControl": "純文本", "DE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "DE.Views.Toolbar.textPortrait": "直向方向", "DE.Views.Toolbar.textRegistered": "註冊標誌", "DE.Views.Toolbar.textRemoveControl": "移除內容控制項", "DE.Views.Toolbar.textRemSpaceAfter": "Remove space after paragraph", "DE.Views.Toolbar.textRemSpaceBefore": "Remove space before paragraph", "DE.Views.Toolbar.textRemWatermark": "刪除水印", "DE.Views.Toolbar.textRestartEachPage": "每頁重新開始", "DE.Views.Toolbar.textRestartEachSection": "每節重新開始", "DE.Views.Toolbar.textRichControl": "富文本", "DE.Views.Toolbar.textRight": "右: ", "DE.Views.Toolbar.textSection": "分區標誌", "DE.Views.Toolbar.textShapesCombine": "Combine", "DE.Views.Toolbar.textShapesFragment": "Fragment", "DE.Views.Toolbar.textShapesIntersect": "Intersect", "DE.Views.Toolbar.textShapesSubstract": "Subtract", "DE.Views.Toolbar.textShapesUnion": "Union", "DE.Views.Toolbar.textSmile": "White Smiling Face", "DE.Views.Toolbar.textSpaceAfter": "Space after", "DE.Views.Toolbar.textSpaceBefore": "Space before", "DE.Views.Toolbar.textSquareRoot": "平方根", "DE.Views.Toolbar.textStrikeout": "刪除線", "DE.Views.Toolbar.textStyleMenuDelete": "刪除樣式", "DE.Views.Toolbar.textStyleMenuDeleteAll": "刪除所有自訂樣式", "DE.Views.Toolbar.textStyleMenuNew": "從選取範圍新增樣式", "DE.Views.Toolbar.textStyleMenuRestore": "還原為預設值", "DE.Views.Toolbar.textStyleMenuRestoreAll": "全部還原為預設樣式", "DE.Views.Toolbar.textStyleMenuUpdate": "從選擇範圍更新", "DE.Views.Toolbar.textSubscript": "下標", "DE.Views.Toolbar.textSuperscript": "上標", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "禁止當前段落", "DE.Views.Toolbar.textTabCollaboration": "共同編輯", "DE.Views.Toolbar.textTabDraw": "繪製", "DE.Views.Toolbar.textTabFile": "檔案", "DE.Views.Toolbar.textTabHome": "首頁", "DE.Views.Toolbar.textTabInsert": "插入", "DE.Views.Toolbar.textTabLayout": "版面配置", "DE.Views.Toolbar.textTabLinks": "參考文獻", "DE.Views.Toolbar.textTabProtect": "保護", "DE.Views.Toolbar.textTabReview": "檢閱", "DE.Views.Toolbar.textTabView": "檢視", "DE.Views.Toolbar.textTilde": "波浪號", "DE.Views.Toolbar.textTitleError": "錯誤", "DE.Views.Toolbar.textToCurrent": "至目前位置", "DE.Views.Toolbar.textTop": "頂部: ", "DE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "DE.Views.Toolbar.textUnderline": "底線", "DE.Views.Toolbar.textYen": "Yen Sign", "DE.Views.Toolbar.tipAlignCenter": "居中對齊", "DE.Views.Toolbar.tipAlignJust": "兩端對齊", "DE.Views.Toolbar.tipAlignLeft": "對齊左側", "DE.Views.Toolbar.tipAlignRight": "對齊右側", "DE.Views.Toolbar.tipBack": "返回", "DE.Views.Toolbar.tipBlankPage": "插入空白頁", "DE.Views.Toolbar.tipChangeCase": "大小寫轉換", "DE.Views.Toolbar.tipChangeChart": "變更圖表類型", "DE.Views.Toolbar.tipClearStyle": "清除樣式", "DE.Views.Toolbar.tipColorSchemas": "變更色彩配置", "DE.Views.Toolbar.tipColumns": "插入欄位", "DE.Views.Toolbar.tipControls": "插入內容控制項", "DE.Views.Toolbar.tipCopy": "複製", "DE.Views.Toolbar.tipCopyStyle": "複製格式", "DE.Views.Toolbar.tipCut": "剪下", "DE.Views.Toolbar.tipDateTime": "插入目前日期和時間", "DE.Views.Toolbar.tipDecFont": "縮小文字", "DE.Views.Toolbar.tipDecPrLeft": "減少縮排", "DE.Views.Toolbar.tipDownload": "Download file", "DE.Views.Toolbar.tipDropCap": "插入首字下沉", "DE.Views.Toolbar.tipEditHeader": "編輯頁首或頁尾", "DE.Views.Toolbar.tipEditMode": "Edit current file.<br>The page will be reloaded.", "DE.Views.Toolbar.tipFontColor": "字型顏色", "DE.Views.Toolbar.tipFontName": "字型", "DE.Views.Toolbar.tipFontSize": "字型大小", "DE.Views.Toolbar.tipHandTool": "Hand tool", "DE.Views.Toolbar.tipHighlightColor": "文字醒目提示色彩", "DE.Views.Toolbar.tipHyphenation": "自動斷詞", "DE.Views.Toolbar.tipImgAlign": "對齊物件", "DE.Views.Toolbar.tipImgGroup": "群組物件", "DE.Views.Toolbar.tipImgWrapping": "換行文字", "DE.Views.Toolbar.tipIncFont": "放大文字", "DE.Views.Toolbar.tipIncPrLeft": "增加縮進", "DE.Views.Toolbar.tipInsertChart": "插入圖表", "DE.Views.Toolbar.tipInsertEquation": "插入方程式", "DE.Views.Toolbar.tipInsertHorizontalText": "插入水平文字方塊", "DE.Views.Toolbar.tipInsertImage": "插入圖片", "DE.Views.Toolbar.tipInsertNum": "插入頁碼", "DE.Views.Toolbar.tipInsertShape": "插入自動形狀", "DE.Views.Toolbar.tipInsertSmartArt": "插入SmartArt", "DE.Views.Toolbar.tipInsertSymbol": "插入符號", "DE.Views.Toolbar.tipInsertTable": "插入表格", "DE.Views.Toolbar.tipInsertText": "插入文字方塊", "DE.Views.Toolbar.tipInsertTextArt": "插入藝術文字", "DE.Views.Toolbar.tipInsertVerticalText": "插入垂直文字方塊", "DE.Views.Toolbar.tipInsField": "Insert field", "DE.Views.Toolbar.tipLineNumbers": "顯示行號", "DE.Views.Toolbar.tipLineSpace": "段落行距", "DE.Views.Toolbar.tipMailRecepients": "郵件合併", "DE.Views.Toolbar.tipMarkers": "項目符號", "DE.Views.Toolbar.tipMarkersArrow": "箭頭符號", "DE.Views.Toolbar.tipMarkersCheckmark": "勾號符號清單", "DE.Views.Toolbar.tipMarkersDash": "虛線符號", "DE.Views.Toolbar.tipMarkersFRhombus": "填充菱形符號", "DE.Views.Toolbar.tipMarkersFRound": "填充圓形符號", "DE.Views.Toolbar.tipMarkersFSquare": "填充方形符號", "DE.Views.Toolbar.tipMarkersHRound": "空心圓點符號", "DE.Views.Toolbar.tipMarkersStar": "星形符號", "DE.Views.Toolbar.tipMultiLevelArticl": "多層次編號文章", "DE.Views.Toolbar.tipMultiLevelChapter": "多層次編號章節", "DE.Views.Toolbar.tipMultiLevelHeadings": "多層次編號標題", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "多層次各種編號標題", "DE.Views.Toolbar.tipMultiLevelNumbered": "多層次編號符號", "DE.Views.Toolbar.tipMultilevels": "多層次清單", "DE.Views.Toolbar.tipMultiLevelSymbols": "多層次符號項目符號", "DE.Views.Toolbar.tipMultiLevelVarious": "多層次各種編號符號", "DE.Views.Toolbar.tipNumbers": "編號", "DE.Views.Toolbar.tipPageBreak": "插入頁面或章節分隔符", "DE.Views.Toolbar.tipPageColor": "Change page color", "DE.Views.Toolbar.tipPageMargins": "頁邊距", "DE.Views.Toolbar.tipPageOrient": "頁面方向", "DE.Views.Toolbar.tipPageSize": "頁面大小", "DE.Views.Toolbar.tipParagraphStyle": "段落風格", "DE.Views.Toolbar.tipPaste": "貼上", "DE.Views.Toolbar.tipPrColor": "段落背景顏色", "DE.Views.Toolbar.tipPrint": "列印", "DE.Views.Toolbar.tipPrintQuick": "快速列印", "DE.Views.Toolbar.tipRedo": "重做", "DE.Views.Toolbar.tipReplace": "Replace", "DE.Views.Toolbar.tipSave": "儲存", "DE.Views.Toolbar.tipSaveCoauth": "儲存您的更改，以供其他使用者查看。", "DE.Views.Toolbar.tipSelectAll": "全選", "DE.Views.Toolbar.tipSelectTool": "Select tool", "DE.Views.Toolbar.tipSendBackward": "向後發送", "DE.Views.Toolbar.tipSendForward": "向前移動", "DE.Views.Toolbar.tipShapesMerge": "Merge shapes", "DE.Views.Toolbar.tipShowHiddenChars": "顯示/隱藏編輯標記", "DE.Views.Toolbar.tipSynchronize": "文件已被其他使用者更改。請按一下以保存您的更改並重新載入更新。", "DE.Views.Toolbar.tipTextDir": "Text direction", "DE.Views.Toolbar.tipTextFromFile": "Text from file", "DE.Views.Toolbar.tipUndo": "復原", "DE.Views.Toolbar.tipWatermark": "編輯浮水印", "DE.Views.Toolbar.txtAutoText": "Auto", "DE.Views.Toolbar.txtDistribHor": "水平分散對齊", "DE.Views.Toolbar.txtDistribVert": "垂直分散對齊", "DE.Views.Toolbar.txtGroupBulletDoc": "文件項目符號", "DE.Views.Toolbar.txtGroupBulletLib": "項目符號庫", "DE.Views.Toolbar.txtGroupMultiDoc": "目前文件中的清單", "DE.Views.Toolbar.txtGroupMultiLib": "清單庫", "DE.Views.Toolbar.txtGroupNumDoc": "文件編號格式", "DE.Views.Toolbar.txtGroupNumLib": "編號庫", "DE.Views.Toolbar.txtGroupRecent": "最近使用", "DE.Views.Toolbar.txtMarginAlign": "對齊至邊界", "DE.Views.Toolbar.txtObjectsAlign": "對齊所選物件", "DE.Views.Toolbar.txtPageAlign": "對齊至頁面", "DE.Views.ViewTab.textAlwaysShowToolbar": "始終顯示工具列", "DE.Views.ViewTab.textDarkDocument": "夜間模式文件", "DE.Views.ViewTab.textFill": "Fill", "DE.Views.ViewTab.textFitToPage": "調整至頁面大小", "DE.Views.ViewTab.textFitToWidth": "調整至寬度大小", "DE.Views.ViewTab.textInterfaceTheme": "介面主題", "DE.Views.ViewTab.textLeftMenu": "左側面板", "DE.Views.ViewTab.textLine": "Line", "DE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "DE.Views.ViewTab.textNavigation": "導覽", "DE.Views.ViewTab.textOutline": "標題", "DE.Views.ViewTab.textRightMenu": "右側面板", "DE.Views.ViewTab.textRulers": "尺規", "DE.Views.ViewTab.textStatusBar": "狀態列", "DE.Views.ViewTab.textTabStyle": "Tab style", "DE.Views.ViewTab.textZoom": "縮放", "DE.Views.ViewTab.tipDarkDocument": "夜間模式文件", "DE.Views.ViewTab.tipFitToPage": "調整至頁面大小", "DE.Views.ViewTab.tipFitToWidth": "調整至寬度大小", "DE.Views.ViewTab.tipHeadings": "標題", "DE.Views.ViewTab.tipInterfaceTheme": "介面主題", "DE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textAuto": "自動", "DE.Views.WatermarkSettingsDialog.textBold": "粗體", "DE.Views.WatermarkSettingsDialog.textColor": "文字顏色", "DE.Views.WatermarkSettingsDialog.textDiagonal": "對角線", "DE.Views.WatermarkSettingsDialog.textFont": "字型", "DE.Views.WatermarkSettingsDialog.textFromFile": "從檔案", "DE.Views.WatermarkSettingsDialog.textFromStorage": "從儲存空間", "DE.Views.WatermarkSettingsDialog.textFromUrl": "從網址", "DE.Views.WatermarkSettingsDialog.textHor": "水平", "DE.Views.WatermarkSettingsDialog.textImageW": "圖片浮水印", "DE.Views.WatermarkSettingsDialog.textItalic": "斜體", "DE.Views.WatermarkSettingsDialog.textLanguage": "語言", "DE.Views.WatermarkSettingsDialog.textLayout": "版面配置", "DE.Views.WatermarkSettingsDialog.textNone": "無", "DE.Views.WatermarkSettingsDialog.textScale": "縮放", "DE.Views.WatermarkSettingsDialog.textSelect": "選擇圖片", "DE.Views.WatermarkSettingsDialog.textStrikeout": "淘汰", "DE.Views.WatermarkSettingsDialog.textText": "文字", "DE.Views.WatermarkSettingsDialog.textTextW": "文字水印", "DE.Views.WatermarkSettingsDialog.textTitle": "浮水印設定", "DE.Views.WatermarkSettingsDialog.textTransparency": "半透明", "DE.Views.WatermarkSettingsDialog.textUnderline": "底線", "DE.Views.WatermarkSettingsDialog.tipFontName": "字型名稱", "DE.Views.WatermarkSettingsDialog.tipFontSize": "字型大小", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}