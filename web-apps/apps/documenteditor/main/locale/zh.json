{"Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Desktop.hintBtnHome": "显示主窗口", "Common.Controllers.Desktop.itemCreateFromTemplate": "用模板创建", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "匿名用户", "Common.Controllers.ExternalDiagramEditor.textClose": "关闭", "Common.Controllers.ExternalDiagramEditor.warningText": "该对象被禁用，因为它被另一个用户编辑。", "Common.Controllers.ExternalDiagramEditor.warningTitle": "警告", "Common.Controllers.ExternalMergeEditor.textAnonymous": "匿名用户", "Common.Controllers.ExternalMergeEditor.textClose": "关闭", "Common.Controllers.ExternalMergeEditor.warningText": "该对象被禁用，因为它被另一个用户编辑。", "Common.Controllers.ExternalMergeEditor.warningTitle": "警告", "Common.Controllers.ExternalOleEditor.textAnonymous": "匿名用户", "Common.Controllers.ExternalOleEditor.textClose": "关闭", "Common.Controllers.ExternalOleEditor.warningText": "该对象被禁用，因为它被另一个用户编辑。", "Common.Controllers.ExternalOleEditor.warningTitle": "警告", "Common.Controllers.History.notcriticalErrorTitle": "警告", "Common.Controllers.History.txtErrorLoadHistory": "历史记录加载失败", "Common.Controllers.Plugins.helpUseMacros": "在这里可以找到宏按钮", "Common.Controllers.Plugins.helpUseMacrosHeader": "更新了对宏的访问", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "插件已成功安装。您可以在这里访问所有后台插件。", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b>已成功安装。您可以在这里访问所有后台插件。", "Common.Controllers.Plugins.textRunInstalledPlugins": "运行已安装的插件", "Common.Controllers.Plugins.textRunPlugin": "运行插件", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "比较文档时，文档中所有跟踪到的更改都将被视作已同意。您想要继续吗？", "Common.Controllers.ReviewChanges.textAtLeast": "至少", "Common.Controllers.ReviewChanges.textAuto": "自动", "Common.Controllers.ReviewChanges.textBaseline": "基准线", "Common.Controllers.ReviewChanges.textBold": "粗体", "Common.Controllers.ReviewChanges.textBreakBefore": "段前分页", "Common.Controllers.ReviewChanges.textCaps": "全部大写", "Common.Controllers.ReviewChanges.textCenter": "居中对齐", "Common.Controllers.ReviewChanges.textChar": "字符级别", "Common.Controllers.ReviewChanges.textChart": "图表", "Common.Controllers.ReviewChanges.textColor": "字体颜色", "Common.Controllers.ReviewChanges.textContextual": "不要在相同样式的段落之间添加间隔", "Common.Controllers.ReviewChanges.textDeleted": "<b>已删除:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "双删除线", "Common.Controllers.ReviewChanges.textEquation": "方程式", "Common.Controllers.ReviewChanges.textExact": "精确地", "Common.Controllers.ReviewChanges.textFirstLine": "第一行", "Common.Controllers.ReviewChanges.textFontSize": "字体大小", "Common.Controllers.ReviewChanges.textFormatted": "已格式化", "Common.Controllers.ReviewChanges.textHighlight": "高亮色", "Common.Controllers.ReviewChanges.textImage": "图片", "Common.Controllers.ReviewChanges.textIndentLeft": "左缩进", "Common.Controllers.ReviewChanges.textIndentRight": "右缩进", "Common.Controllers.ReviewChanges.textInserted": "<b>已插入：</b>", "Common.Controllers.ReviewChanges.textItalic": "斜体", "Common.Controllers.ReviewChanges.textJustify": "两端对齐", "Common.Controllers.ReviewChanges.textKeepLines": "段中不分页", "Common.Controllers.ReviewChanges.textKeepNext": "与下段同页", "Common.Controllers.ReviewChanges.textLeft": "左对齐", "Common.Controllers.ReviewChanges.textLineSpacing": "行间距：", "Common.Controllers.ReviewChanges.textMultiple": "倍数", "Common.Controllers.ReviewChanges.textNoBreakBefore": "不段前分页", "Common.Controllers.ReviewChanges.textNoContextual": "在相同样式的段落之间添加间隔", "Common.Controllers.ReviewChanges.textNoKeepLines": "段中可分页", "Common.Controllers.ReviewChanges.textNoKeepNext": "不与下段同页", "Common.Controllers.ReviewChanges.textNot": "不", "Common.Controllers.ReviewChanges.textNoWidow": "不孤行控制", "Common.Controllers.ReviewChanges.textNum": "更改编号", "Common.Controllers.ReviewChanges.textOff": "｛0｝不再使用“跟踪更改”。", "Common.Controllers.ReviewChanges.textOffGlobal": "｛0｝已禁用所有人的跟踪更改。", "Common.Controllers.ReviewChanges.textOn": "｛0｝现在正在使用“跟踪更改”。", "Common.Controllers.ReviewChanges.textOnGlobal": "｛0｝为每个人启用了“跟踪更改”。", "Common.Controllers.ReviewChanges.textParaDeleted": "<b>已刪除段落</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "已格式化的段落", "Common.Controllers.ReviewChanges.textParaInserted": "<b>段落已插入</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>已下移</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>已上移：</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>已移动</b>", "Common.Controllers.ReviewChanges.textPosition": "位置", "Common.Controllers.ReviewChanges.textRight": "右对齐", "Common.Controllers.ReviewChanges.textShape": "形状", "Common.Controllers.ReviewChanges.textShd": "背景颜色", "Common.Controllers.ReviewChanges.textShow": "显示变更于", "Common.Controllers.ReviewChanges.textSmallCaps": "小型大写字母", "Common.Controllers.ReviewChanges.textSpacing": "间距", "Common.Controllers.ReviewChanges.textSpacingAfter": "之后的间距", "Common.Controllers.ReviewChanges.textSpacingBefore": "之前的间距", "Common.Controllers.ReviewChanges.textStrikeout": "删除线", "Common.Controllers.ReviewChanges.textSubScript": "下标", "Common.Controllers.ReviewChanges.textSuperScript": "上标", "Common.Controllers.ReviewChanges.textTableChanged": "<b>表格设置已更改</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b>已添加表格行</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b>表格行已删除</b>", "Common.Controllers.ReviewChanges.textTabs": "更改选项卡", "Common.Controllers.ReviewChanges.textTitleComparison": "比较设置", "Common.Controllers.ReviewChanges.textUnderline": "下划线", "Common.Controllers.ReviewChanges.textUrl": "粘贴文件URL", "Common.Controllers.ReviewChanges.textWidow": "孤行控制", "Common.Controllers.ReviewChanges.textWord": "字級", "Common.define.chartData.textArea": "面积图", "Common.define.chartData.textAreaStacked": "堆积面积", "Common.define.chartData.textAreaStackedPer": "100%堆叠区域", "Common.define.chartData.textBar": "条形图", "Common.define.chartData.textBarNormal": "簇状柱形图", "Common.define.chartData.textBarNormal3d": "三维分组柱形图", "Common.define.chartData.textBarNormal3dPerspective": "三维柱形图", "Common.define.chartData.textBarStacked": "堆积柱形图", "Common.define.chartData.textBarStacked3d": "三维堆积柱形图", "Common.define.chartData.textBarStackedPer": "100%堆积柱状图", "Common.define.chartData.textBarStackedPer3d": "三维100%堆积柱形图", "Common.define.chartData.textCharts": "图表", "Common.define.chartData.textColumn": "列", "Common.define.chartData.textCombo": "组合图", "Common.define.chartData.textComboAreaBar": "堆叠面积-丛集柱状图", "Common.define.chartData.textComboBarLine": "簇状柱形图-折线图", "Common.define.chartData.textComboBarLineSecondary": "簇状柱形图-次坐标轴上的折线图", "Common.define.chartData.textComboCustom": "自定义组合", "Common.define.chartData.textDoughnut": "圆环图", "Common.define.chartData.textHBarNormal": "簇状条形图", "Common.define.chartData.textHBarNormal3d": "三维分组条形图", "Common.define.chartData.textHBarStacked": "堆积条形图", "Common.define.chartData.textHBarStacked3d": "三维堆积条形图", "Common.define.chartData.textHBarStackedPer": "100%堆积条形图", "Common.define.chartData.textHBarStackedPer3d": "三维100%堆积条形图", "Common.define.chartData.textLine": "折线图", "Common.define.chartData.textLine3d": "三维折线图", "Common.define.chartData.textLineMarker": "带标记的线条", "Common.define.chartData.textLineStacked": "堆叠折线图", "Common.define.chartData.textLineStackedMarker": "带标记的堆积线", "Common.define.chartData.textLineStackedPer": "100%堆积折线图", "Common.define.chartData.textLineStackedPerMarker": "带标记的100%堆积折线图", "Common.define.chartData.textPie": "圆饼图", "Common.define.chartData.textPie3d": "三维饼图", "Common.define.chartData.textPoint": "XY散佈圖", "Common.define.chartData.textRadar": "雷达图", "Common.define.chartData.textRadarFilled": "填充雷达", "Common.define.chartData.textRadarMarker": "带标记的雷达", "Common.define.chartData.textScatter": "散布图", "Common.define.chartData.textScatterLine": "直线散布图", "Common.define.chartData.textScatterLineMarker": "直线和标记散布图", "Common.define.chartData.textScatterSmooth": "平滑线条散布图", "Common.define.chartData.textScatterSmoothMarker": "平滑线条和标记的散布图", "Common.define.chartData.textStock": "股票", "Common.define.chartData.textSurface": "表面", "Common.define.smartArt.textAccentedPicture": "强调图片", "Common.define.smartArt.textAccentProcess": "重点流程", "Common.define.smartArt.textAlternatingFlow": "交替流程", "Common.define.smartArt.textAlternatingHexagons": "交替六边形", "Common.define.smartArt.textAlternatingPictureBlocks": "交替图片块", "Common.define.smartArt.textAlternatingPictureCircles": "交替图片圆形", "Common.define.smartArt.textArchitectureLayout": "架构布局", "Common.define.smartArt.textArrowRibbon": "带形箭头", "Common.define.smartArt.textAscendingPictureAccentProcess": "升序图片重点流程", "Common.define.smartArt.textBalance": "平衡", "Common.define.smartArt.textBasicBendingProcess": "基本弯曲流程", "Common.define.smartArt.textBasicBlockList": "基本列表", "Common.define.smartArt.textBasicChevronProcess": "基本箭头流程", "Common.define.smartArt.textBasicCycle": "基本循环", "Common.define.smartArt.textBasicMatrix": "基本矩阵", "Common.define.smartArt.textBasicPie": "基本饼图", "Common.define.smartArt.textBasicProcess": "基本流程", "Common.define.smartArt.textBasicPyramid": "基本金字塔", "Common.define.smartArt.textBasicRadial": "基本放射图", "Common.define.smartArt.textBasicTarget": "基本目标", "Common.define.smartArt.textBasicTimeline": "基本时间轴", "Common.define.smartArt.textBasicVenn": "基本维恩图", "Common.define.smartArt.textBendingPictureAccentList": "蛇形图片重点列表", "Common.define.smartArt.textBendingPictureBlocks": "蛇形图片块", "Common.define.smartArt.textBendingPictureCaption": "蛇形图片标题", "Common.define.smartArt.textBendingPictureCaptionList": "蛇形图片标题列表", "Common.define.smartArt.textBendingPictureSemiTranparentText": "蛇形图片半透明文字", "Common.define.smartArt.textBlockCycle": "块循环", "Common.define.smartArt.textBubblePictureList": "气泡图列表", "Common.define.smartArt.textCaptionedPictures": "带标题的图片", "Common.define.smartArt.textChevronAccentProcess": "V型强调流程", "Common.define.smartArt.textChevronList": "V型列表", "Common.define.smartArt.textCircleAccentTimeline": "圆形强调时间线", "Common.define.smartArt.textCircleArrowProcess": "圆形箭頭流程", "Common.define.smartArt.textCirclePictureHierarchy": "圆形图片层次结构", "Common.define.smartArt.textCircleProcess": "圆形流程", "Common.define.smartArt.textCircleRelationship": "圆形关系", "Common.define.smartArt.textCircularBendingProcess": "环状蛇形流程", "Common.define.smartArt.textCircularPictureCallout": "圆形图片标注", "Common.define.smartArt.textClosedChevronProcess": "闭合V型流程", "Common.define.smartArt.textContinuousArrowProcess": "连续箭头过程", "Common.define.smartArt.textContinuousBlockProcess": "连续块过程", "Common.define.smartArt.textContinuousCycle": "连续循环", "Common.define.smartArt.textContinuousPictureList": "连续图片列表", "Common.define.smartArt.textConvergingArrows": "汇聚箭头", "Common.define.smartArt.textConvergingRadial": "汇聚放射线", "Common.define.smartArt.textConvergingText": "汇聚文本", "Common.define.smartArt.textCounterbalanceArrows": "平衡箭头", "Common.define.smartArt.textCycle": "循环", "Common.define.smartArt.textCycleMatrix": "循环矩阵", "Common.define.smartArt.textDescendingBlockList": "降序块列表", "Common.define.smartArt.textDescendingProcess": "降序流程", "Common.define.smartArt.textDetailedProcess": "详细流程", "Common.define.smartArt.textDivergingArrows": "发散箭头", "Common.define.smartArt.textDivergingRadial": "发散径向", "Common.define.smartArt.textEquation": "方程式", "Common.define.smartArt.textFramedTextPicture": "带边框的文本图片", "Common.define.smartArt.textFunnel": "漏斗", "Common.define.smartArt.textGear": "齿轮", "Common.define.smartArt.textGridMatrix": "网格矩阵", "Common.define.smartArt.textGroupedList": "分组列表", "Common.define.smartArt.textHalfCircleOrganizationChart": "半圆组织结构图", "Common.define.smartArt.textHexagonCluster": "六边形集群", "Common.define.smartArt.textHexagonRadial": "六边形射线", "Common.define.smartArt.textHierarchy": "层级结构", "Common.define.smartArt.textHierarchyList": "层级结构列表", "Common.define.smartArt.textHorizontalBulletList": "水平项目符号列表", "Common.define.smartArt.textHorizontalHierarchy": "水平层次结构", "Common.define.smartArt.textHorizontalLabeledHierarchy": "水平标记层次", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "水平多级层次结构", "Common.define.smartArt.textHorizontalOrganizationChart": "水平组织结构图", "Common.define.smartArt.textHorizontalPictureList": "水平图片列表", "Common.define.smartArt.textIncreasingArrowProcess": "递增箭头流程", "Common.define.smartArt.textIncreasingCircleProcess": "递增圆圈流程", "Common.define.smartArt.textInterconnectedBlockProcess": "互连块流程", "Common.define.smartArt.textInterconnectedRings": "互连环图", "Common.define.smartArt.textInvertedPyramid": "倒金字塔", "Common.define.smartArt.textLabeledHierarchy": "已标记层次", "Common.define.smartArt.textLinearVenn": "线性韦恩图", "Common.define.smartArt.textLinedList": "划线列表", "Common.define.smartArt.textList": "列表", "Common.define.smartArt.textMatrix": "矩阵", "Common.define.smartArt.textMultidirectionalCycle": "多方向循环", "Common.define.smartArt.textNameAndTitleOrganizationChart": "姓名和职务组织结构图", "Common.define.smartArt.textNestedTarget": "嵌套的目标", "Common.define.smartArt.textNondirectionalCycle": "非定向循环", "Common.define.smartArt.textOpposingArrows": "反向箭头", "Common.define.smartArt.textOpposingIdeas": "相对观点", "Common.define.smartArt.textOrganizationChart": "组织图", "Common.define.smartArt.textOther": "其它", "Common.define.smartArt.textPhasedProcess": "分阶段处理", "Common.define.smartArt.textPicture": "图片", "Common.define.smartArt.textPictureAccentBlocks": "图片强调块", "Common.define.smartArt.textPictureAccentList": "图片强调列表", "Common.define.smartArt.textPictureAccentProcess": "图片强调文字流程", "Common.define.smartArt.textPictureCaptionList": "图片标题列表", "Common.define.smartArt.textPictureFrame": "图片框架", "Common.define.smartArt.textPictureGrid": "图片网格", "Common.define.smartArt.textPictureLineup": "图片排列", "Common.define.smartArt.textPictureOrganizationChart": "图片组织图", "Common.define.smartArt.textPictureStrips": "图片条纹", "Common.define.smartArt.textPieProcess": "圆饼图流程", "Common.define.smartArt.textPlusAndMinus": "加减", "Common.define.smartArt.textProcess": "流程", "Common.define.smartArt.textProcessArrows": "流程箭头", "Common.define.smartArt.textProcessList": "流程列表", "Common.define.smartArt.textPyramid": "金字塔", "Common.define.smartArt.textPyramidList": "金字塔列表", "Common.define.smartArt.textRadialCluster": "放射状群集", "Common.define.smartArt.textRadialCycle": "径向循环", "Common.define.smartArt.textRadialList": "径向列表", "Common.define.smartArt.textRadialPictureList": "放射状图片列表", "Common.define.smartArt.textRadialVenn": "径向韦恩图", "Common.define.smartArt.textRandomToResultProcess": "随机结果流程", "Common.define.smartArt.textRelationship": "关系", "Common.define.smartArt.textRepeatingBendingProcess": "重复弯曲流程", "Common.define.smartArt.textReverseList": "反向列表", "Common.define.smartArt.textSegmentedCycle": "分段循环", "Common.define.smartArt.textSegmentedProcess": "分段流程", "Common.define.smartArt.textSegmentedPyramid": "分段金字塔", "Common.define.smartArt.textSnapshotPictureList": "快照图片列表", "Common.define.smartArt.textSpiralPicture": "螺旋图", "Common.define.smartArt.textSquareAccentList": "方形强调列表", "Common.define.smartArt.textStackedList": "堆积列表", "Common.define.smartArt.textStackedVenn": "堆积韦恩图", "Common.define.smartArt.textStaggeredProcess": "交错流程", "Common.define.smartArt.textStepDownProcess": "向下阶梯式流程", "Common.define.smartArt.textStepUpProcess": "向上阶梯式流程", "Common.define.smartArt.textSubStepProcess": "子步骤流程", "Common.define.smartArt.textTabbedArc": "已定位的弧形", "Common.define.smartArt.textTableHierarchy": "表格层次", "Common.define.smartArt.textTableList": "表格列表", "Common.define.smartArt.textTabList": "标签列表", "Common.define.smartArt.textTargetList": "目标列表", "Common.define.smartArt.textTextCycle": "文本循环", "Common.define.smartArt.textThemePictureAccent": "主题图片强调", "Common.define.smartArt.textThemePictureAlternatingAccent": "主题图片交替强调", "Common.define.smartArt.textThemePictureGrid": "主题图片网格", "Common.define.smartArt.textTitledMatrix": "标题矩阵", "Common.define.smartArt.textTitledPictureAccentList": "标题图片强调列表", "Common.define.smartArt.textTitledPictureBlocks": "标题图片块", "Common.define.smartArt.textTitlePictureLineup": "标题图片排列", "Common.define.smartArt.textTrapezoidList": "梯形列表", "Common.define.smartArt.textUpwardArrow": "向上箭头", "Common.define.smartArt.textVaryingWidthList": "可变宽度列表", "Common.define.smartArt.textVerticalAccentList": "垂直强调列表", "Common.define.smartArt.textVerticalArrowList": "垂直箭头列表", "Common.define.smartArt.textVerticalBendingProcess": "垂直弯曲流程", "Common.define.smartArt.textVerticalBlockList": "垂直块列表", "Common.define.smartArt.textVerticalBoxList": "垂直方框列表", "Common.define.smartArt.textVerticalBracketList": "垂直括号列表", "Common.define.smartArt.textVerticalBulletList": "垂直项目符号列表", "Common.define.smartArt.textVerticalChevronList": "垂直V型列表", "Common.define.smartArt.textVerticalCircleList": "垂直循环列表", "Common.define.smartArt.textVerticalCurvedList": "垂直曲线列表", "Common.define.smartArt.textVerticalEquation": "垂直方程式", "Common.define.smartArt.textVerticalPictureAccentList": "垂直图片强调列表", "Common.define.smartArt.textVerticalPictureList": "垂直图片列表", "Common.define.smartArt.textVerticalProcess": "垂直流程", "Common.Translation.textMoreButton": "更多", "Common.Translation.tipFileLocked": "文档编辑被锁定，您可以稍后进行更改并将其保存为本地副本。", "Common.Translation.tipFileReadOnly": "该文件是只读的。若要保留更改，请使用新名称或将文件保存在其他位置。", "Common.Translation.warnFileLocked": "您无法编辑此文件，因为它正在另一个应用程序中进行编辑。", "Common.Translation.warnFileLockedBtnEdit": "创建副本", "Common.Translation.warnFileLockedBtnView": "打开查看", "Common.UI.ButtonColored.textAutoColor": "自动", "Common.UI.ButtonColored.textEyedropper": "拾色器", "Common.UI.ButtonColored.textNewColor": "更多颜色", "Common.UI.Calendar.textApril": "四月", "Common.UI.Calendar.textAugust": "八月", "Common.UI.Calendar.textDecember": "十二月", "Common.UI.Calendar.textFebruary": "二月", "Common.UI.Calendar.textJanuary": "一月", "Common.UI.Calendar.textJuly": "七月", "Common.UI.Calendar.textJune": "六月", "Common.UI.Calendar.textMarch": "三月", "Common.UI.Calendar.textMay": "五月", "Common.UI.Calendar.textMonths": "月", "Common.UI.Calendar.textNovember": "十一月", "Common.UI.Calendar.textOctober": "十月", "Common.UI.Calendar.textSeptember": "九月", "Common.UI.Calendar.textShortApril": "四月", "Common.UI.Calendar.textShortAugust": "八月", "Common.UI.Calendar.textShortDecember": "十二月", "Common.UI.Calendar.textShortFebruary": "二月", "Common.UI.Calendar.textShortFriday": "周五", "Common.UI.Calendar.textShortJanuary": "一月", "Common.UI.Calendar.textShortJuly": "七月", "Common.UI.Calendar.textShortJune": "六月", "Common.UI.Calendar.textShortMarch": "三月", "Common.UI.Calendar.textShortMay": "五月", "Common.UI.Calendar.textShortMonday": "周一", "Common.UI.Calendar.textShortNovember": "十一月", "Common.UI.Calendar.textShortOctober": "十月", "Common.UI.Calendar.textShortSaturday": "周六", "Common.UI.Calendar.textShortSeptember": "九月", "Common.UI.Calendar.textShortSunday": "周日", "Common.UI.Calendar.textShortThursday": "周四", "Common.UI.Calendar.textShortTuesday": "周二", "Common.UI.Calendar.textShortWednesday": "周三", "Common.UI.Calendar.textYears": "年", "Common.UI.ComboBorderSize.txtNoBorders": "无边框", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "无边框", "Common.UI.ComboDataView.emptyComboText": "无样式", "Common.UI.ExtendedColorDialog.addButtonText": "添加", "Common.UI.ExtendedColorDialog.textCurrent": "当前", "Common.UI.ExtendedColorDialog.textHexErr": "输入的值不正确。<br>请输入000000和FFFFFF之间的值。", "Common.UI.ExtendedColorDialog.textNew": "新建", "Common.UI.ExtendedColorDialog.textRGBErr": "输入的值不正确。<br>请输入介于0和255之间的数值。", "Common.UI.HSBColorPicker.textNoColor": "没有颜色", "Common.UI.InputField.txtEmpty": "这是必填栏", "Common.UI.InputFieldBtnCalendar.textDate": "选择日期", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "隐藏密码", "Common.UI.InputFieldBtnPassword.textHintHold": "按住显示密码", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "显示密码", "Common.UI.SearchBar.textFind": "查找", "Common.UI.SearchBar.tipCloseSearch": "关闭搜索", "Common.UI.SearchBar.tipNextResult": "下一个结果", "Common.UI.SearchBar.tipOpenAdvancedSettings": "打开高级设置", "Common.UI.SearchBar.tipPreviousResult": "上一个结果", "Common.UI.SearchDialog.textHighlight": "高亮显示结果", "Common.UI.SearchDialog.textMatchCase": "区分大小写", "Common.UI.SearchDialog.textReplaceDef": "输入替换文字", "Common.UI.SearchDialog.textSearchStart": "在这里输入你的文字", "Common.UI.SearchDialog.textTitle": "查找和替换", "Common.UI.SearchDialog.textTitle2": "查找", "Common.UI.SearchDialog.textWholeWords": "仅限完整单词", "Common.UI.SearchDialog.txtBtnHideReplace": "隐藏替换", "Common.UI.SearchDialog.txtBtnReplace": "替换", "Common.UI.SearchDialog.txtBtnReplaceAll": "全部替换", "Common.UI.SynchronizeTip.textDontShow": "不要再显示此消息", "Common.UI.SynchronizeTip.textGotIt": "知道了", "Common.UI.SynchronizeTip.textSynchronize": "文档已被其他用户更改<br>请单击保存更改并重新加载更新。", "Common.UI.ThemeColorPalette.textRecentColors": "最近使用的颜色", "Common.UI.ThemeColorPalette.textStandartColors": "标准颜色", "Common.UI.ThemeColorPalette.textThemeColors": "主题颜色", "Common.UI.ThemeColorPalette.textTransparent": "透明", "Common.UI.Themes.txtThemeClassicLight": "经典浅色", "Common.UI.Themes.txtThemeContrastDark": "深色对比", "Common.UI.Themes.txtThemeDark": "深色", "Common.UI.Themes.txtThemeGray": "灰色", "Common.UI.Themes.txtThemeLight": "浅色", "Common.UI.Themes.txtThemeSystem": "和系統一致", "Common.UI.Window.cancelButtonText": "取消", "Common.UI.Window.closeButtonText": "关闭", "Common.UI.Window.noButtonText": "否", "Common.UI.Window.okButtonText": "确定", "Common.UI.Window.textConfirmation": "确认", "Common.UI.Window.textDontShow": "不要再显示此消息", "Common.UI.Window.textError": "错误", "Common.UI.Window.textInformation": "信息", "Common.UI.Window.textWarning": "警告", "Common.UI.Window.yesButtonText": "是", "Common.Utils.Metric.txtCm": "厘米", "Common.Utils.Metric.txtPt": "点", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": "，", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "转移", "Common.Utils.ThemeColor.txtaccent": "重点色", "Common.Utils.ThemeColor.txtAqua": "湖绿色", "Common.Utils.ThemeColor.txtbackground": "背景", "Common.Utils.ThemeColor.txtBlack": "黑色", "Common.Utils.ThemeColor.txtBlue": "蓝色", "Common.Utils.ThemeColor.txtBrightGreen": "明亮绿色", "Common.Utils.ThemeColor.txtBrown": "棕色", "Common.Utils.ThemeColor.txtDarkBlue": "深蓝色", "Common.Utils.ThemeColor.txtDarker": "较深色的", "Common.Utils.ThemeColor.txtDarkGray": "深灰色", "Common.Utils.ThemeColor.txtDarkGreen": "深绿色", "Common.Utils.ThemeColor.txtDarkPurple": "深紫色", "Common.Utils.ThemeColor.txtDarkRed": "深红色", "Common.Utils.ThemeColor.txtDarkTeal": "深青色", "Common.Utils.ThemeColor.txtDarkYellow": "深黄色", "Common.Utils.ThemeColor.txtGold": "金色", "Common.Utils.ThemeColor.txtGray": "灰色", "Common.Utils.ThemeColor.txtGreen": "绿色", "Common.Utils.ThemeColor.txtIndigo": "靛蓝色", "Common.Utils.ThemeColor.txtLavender": "薰衣草色", "Common.Utils.ThemeColor.txtLightBlue": "浅蓝色", "Common.Utils.ThemeColor.txtLighter": "较浅色的", "Common.Utils.ThemeColor.txtLightGray": "浅灰色", "Common.Utils.ThemeColor.txtLightGreen": "浅绿色", "Common.Utils.ThemeColor.txtLightOrange": "浅橙色", "Common.Utils.ThemeColor.txtLightYellow": "浅黄色", "Common.Utils.ThemeColor.txtOrange": "橙色", "Common.Utils.ThemeColor.txtPink": "粉红色", "Common.Utils.ThemeColor.txtPurple": "紫色", "Common.Utils.ThemeColor.txtRed": "红色", "Common.Utils.ThemeColor.txtRose": "玫瑰色", "Common.Utils.ThemeColor.txtSkyBlue": "天蓝色", "Common.Utils.ThemeColor.txtTeal": "青色", "Common.Utils.ThemeColor.txttext": "文字", "Common.Utils.ThemeColor.txtTurquosie": "绿松石", "Common.Utils.ThemeColor.txtViolet": "紫色", "Common.Utils.ThemeColor.txtWhite": "白色", "Common.Utils.ThemeColor.txtYellow": "黃色", "Common.Views.About.txtAddress": "地址:", "Common.Views.About.txtLicensee": "被许可人", "Common.Views.About.txtLicensor": "许可商", "Common.Views.About.txtMail": "电子邮件：", "Common.Views.About.txtPoweredBy": "技术支持方", "Common.Views.About.txtTel": "电话：", "Common.Views.About.txtVersion": "版本", "Common.Views.AutoCorrectDialog.textAdd": "添加", "Common.Views.AutoCorrectDialog.textApplyText": "键入时应用", "Common.Views.AutoCorrectDialog.textAutoCorrect": "文本自动更正", "Common.Views.AutoCorrectDialog.textAutoFormat": "键入时自动套用格式", "Common.Views.AutoCorrectDialog.textBulleted": "自动项目符号列表", "Common.Views.AutoCorrectDialog.textBy": "根据", "Common.Views.AutoCorrectDialog.textDelete": "刪除", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "添加带双倍空格的句点", "Common.Views.AutoCorrectDialog.textFLCells": "单元格第一个字母大写", "Common.Views.AutoCorrectDialog.textFLDont": "之后不要大写", "Common.Views.AutoCorrectDialog.textFLSentence": "将句子的第一个字母大写", "Common.Views.AutoCorrectDialog.textForLangFL": "语言的例外项：", "Common.Views.AutoCorrectDialog.textHyperlink": "具有超链接的网络路径", "Common.Views.AutoCorrectDialog.textHyphens": "带破折号（--）的连字符（--）", "Common.Views.AutoCorrectDialog.textMathCorrect": "数学自动更正", "Common.Views.AutoCorrectDialog.textNumbered": "自动编号列表", "Common.Views.AutoCorrectDialog.textQuotes": "“直引号”改为“智能引号”", "Common.Views.AutoCorrectDialog.textRecognized": "可识别函数", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "以下表达式是可识别的数学表达式。它们不会自动斜体显示。", "Common.Views.AutoCorrectDialog.textReplace": "替换", "Common.Views.AutoCorrectDialog.textReplaceText": "键入时替换", "Common.Views.AutoCorrectDialog.textReplaceType": "键入时替换文本", "Common.Views.AutoCorrectDialog.textReset": "重置", "Common.Views.AutoCorrectDialog.textResetAll": "重置为默认", "Common.Views.AutoCorrectDialog.textRestore": "恢复", "Common.Views.AutoCorrectDialog.textTitle": "自动更正", "Common.Views.AutoCorrectDialog.textWarnAddFL": "例外项只能包含大小写字母。", "Common.Views.AutoCorrectDialog.textWarnAddRec": "可识别函数只能包含字母 A 到 Z，大写或小写。", "Common.Views.AutoCorrectDialog.textWarnResetFL": "您添加的任何例外项都将被移除，并且已移除的例外项将被还原。您想要继续吗？", "Common.Views.AutoCorrectDialog.textWarnResetRec": "您添加的任何表达式都将被移除，已移除的表达式也将被还原。您想要继续吗？", "Common.Views.AutoCorrectDialog.warnReplace": "%1 的自动更正项已存在。您想要替换它吗？", "Common.Views.AutoCorrectDialog.warnReset": "您添加的所有自动更正项都将被移除，更改过的内容将被还原为其原始值。您想要继续吗？", "Common.Views.AutoCorrectDialog.warnRestore": "%1 的自动更正项将重置为其原始值。您想要继续吗？", "Common.Views.Chat.textChat": "聊天", "Common.Views.Chat.textClosePanel": "关闭聊天", "Common.Views.Chat.textEnterMessage": "在这里输入你的信息", "Common.Views.Chat.textSend": "发送", "Common.Views.Comments.mniAuthorAsc": "作者 A 到 Z", "Common.Views.Comments.mniAuthorDesc": "作者 Z 到 A", "Common.Views.Comments.mniDateAsc": "最旧的", "Common.Views.Comments.mniDateDesc": "最新的", "Common.Views.Comments.mniFilterGroups": "按组筛选", "Common.Views.Comments.mniPositionAsc": "从顶部", "Common.Views.Comments.mniPositionDesc": "从底部", "Common.Views.Comments.textAdd": "添加", "Common.Views.Comments.textAddComment": "添加批注", "Common.Views.Comments.textAddCommentToDoc": "向文档添加批注", "Common.Views.Comments.textAddReply": "添加回复", "Common.Views.Comments.textAll": "全部", "Common.Views.Comments.textAnonym": "访客", "Common.Views.Comments.textCancel": "取消", "Common.Views.Comments.textClose": "关闭", "Common.Views.Comments.textClosePanel": "关闭批注", "Common.Views.Comments.textComment": "批注", "Common.Views.Comments.textComments": "批注", "Common.Views.Comments.textEdit": "确定", "Common.Views.Comments.textEnterCommentHint": "在这里输入您的批注", "Common.Views.Comments.textHintAddComment": "添加批注", "Common.Views.Comments.textOpenAgain": "再次打开", "Common.Views.Comments.textReply": "回复", "Common.Views.Comments.textResolve": "解决", "Common.Views.Comments.textResolved": "已解決", "Common.Views.Comments.textSort": "排序批注", "Common.Views.Comments.textSortFilter": "排序和过滤批注", "Common.Views.Comments.textSortFilterMore": "排序、过滤、以及更多", "Common.Views.Comments.textSortMore": "排序以及更多", "Common.Views.Comments.textViewResolved": "您无权重新打开批注", "Common.Views.Comments.txtEmpty": "文档中没有任何批注。", "Common.Views.CopyWarningDialog.textDontShow": "不要再显示此消息", "Common.Views.CopyWarningDialog.textMsg": "使用编辑器工具栏按钮和右键快捷菜单进行的复制，剪切和粘贴操作将仅在此编辑器选项卡中执行。<br> <br>要在编辑器选项卡之外复制或粘贴到应用程序，请使用以下键盘组合：", "Common.Views.CopyWarningDialog.textTitle": "复制，剪切和粘贴操作", "Common.Views.CopyWarningDialog.textToCopy": "用于复制", "Common.Views.CopyWarningDialog.textToCut": "用于剪切", "Common.Views.CopyWarningDialog.textToPaste": "用于粘贴", "Common.Views.CustomizeQuickAccessDialog.textDownload": "下载", "Common.Views.CustomizeQuickAccessDialog.textMsg": "请检查显示在快速访问工具栏上的命令", "Common.Views.CustomizeQuickAccessDialog.textPrint": "打印", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "快速打印", "Common.Views.CustomizeQuickAccessDialog.textRedo": "重做", "Common.Views.CustomizeQuickAccessDialog.textSave": "保存", "Common.Views.CustomizeQuickAccessDialog.textTitle": "自定义快速访问", "Common.Views.CustomizeQuickAccessDialog.textUndo": "撤销", "Common.Views.DocumentAccessDialog.textLoading": "加载中…", "Common.Views.DocumentAccessDialog.textTitle": "分享设置", "Common.Views.DocumentPropertyDialog.errorDate": "您可以从日历中选择一个值并将其存储为日期。<br>如果您手动输入一个值，它将被存储为文本。", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "否", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "是", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "属性应该有一个标题", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "标题", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "“是”或“否”", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "日期", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "类型", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "数字", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "提供一个有效的数字", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "文本", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "属性应该有一个值", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "值", "Common.Views.DocumentPropertyDialog.txtTitle": "新文档属性", "Common.Views.Draw.hintEraser": "橡皮擦", "Common.Views.Draw.hintSelect": "选择", "Common.Views.Draw.txtEraser": "橡皮擦", "Common.Views.Draw.txtHighlighter": "荧光笔", "Common.Views.Draw.txtMM": "毫米", "Common.Views.Draw.txtPen": "笔", "Common.Views.Draw.txtSelect": "选择", "Common.Views.Draw.txtSize": "粗细", "Common.Views.ExternalDiagramEditor.textTitle": "图表编辑器", "Common.Views.ExternalEditor.textClose": "关闭", "Common.Views.ExternalEditor.textSave": "保存并退出", "Common.Views.ExternalMergeEditor.textTitle": "邮件合并收件人", "Common.Views.ExternalOleEditor.textTitle": "电子表格编辑器", "Common.Views.Header.ariaQuickAccessToolbar": "快速访问工具栏", "Common.Views.Header.labelCoUsersDescr": "正在编辑文件的用户：", "Common.Views.Header.textAddFavorite": "收藏", "Common.Views.Header.textAdvSettings": "高级设置", "Common.Views.Header.textBack": "打开文件所在位置", "Common.Views.Header.textClose": "关闭文件", "Common.Views.Header.textCompactView": "隐藏工具栏", "Common.Views.Header.textDocEditDesc": "进行任何更改", "Common.Views.Header.textDocViewDesc": "查看文件，但不做任何更改", "Common.Views.Header.textDocViewFormDesc": "预览表单填写页面", "Common.Views.Header.textDownload": "下载", "Common.Views.Header.textEdit": "编辑", "Common.Views.Header.textHideLines": "隐藏标尺", "Common.Views.Header.textHideStatusBar": "隐藏状态栏", "Common.Views.Header.textPrint": "打印", "Common.Views.Header.textReadOnly": "只读", "Common.Views.Header.textRemoveFavorite": "从收藏夹中删除", "Common.Views.Header.textReview": "审阅", "Common.Views.Header.textReviewDesc": "提出更改", "Common.Views.Header.textShare": "分享", "Common.Views.Header.textStartFill": "共享和收集数据", "Common.Views.Header.textView": "查看", "Common.Views.Header.textViewForm": "查看表单", "Common.Views.Header.textZoom": "縮放", "Common.Views.Header.tipAccessRights": "管理文档访问权限", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "自定义快速访问工具栏", "Common.Views.Header.tipDocEdit": "编辑", "Common.Views.Header.tipDocView": "查看", "Common.Views.Header.tipDocViewForm": "查看表单", "Common.Views.Header.tipDownload": "下载文件", "Common.Views.Header.tipFillStatus": "填写状态", "Common.Views.Header.tipGoEdit": "编辑当前文件", "Common.Views.Header.tipPrint": "打印文件", "Common.Views.Header.tipPrintQuick": "快速打印", "Common.Views.Header.tipRedo": "重做", "Common.Views.Header.tipReview": "审阅", "Common.Views.Header.tipSave": "保存", "Common.Views.Header.tipSearch": "搜索", "Common.Views.Header.tipUndo": "撤消", "Common.Views.Header.tipUsers": "查看用户", "Common.Views.Header.tipViewSettings": "视图设置", "Common.Views.Header.tipViewUsers": "查看用户和管理文档访问权限", "Common.Views.Header.txtAccessRights": "更改访问权限", "Common.Views.Header.txtRename": "重命名", "Common.Views.History.textCloseHistory": "关闭历史记录", "Common.Views.History.textHideAll": "隐藏详细的更改", "Common.Views.History.textHighlightDeleted": "突出显示已删除的内容", "Common.Views.History.textMore": "更多", "Common.Views.History.textRestore": "恢复", "Common.Views.History.textShowAll": "显示详细的更改", "Common.Views.History.textVer": "版本", "Common.Views.History.textVersionHistory": "版本历史", "Common.Views.ImageFromUrlDialog.textUrl": "粘贴图片URL网址：", "Common.Views.ImageFromUrlDialog.txtEmpty": "这是必填栏", "Common.Views.ImageFromUrlDialog.txtNotUrl": "该字段应该是“http://www.example.com”格式的URL", "Common.Views.InsertTableDialog.textInvalidRowsCols": "您需要指定有效的行数和列数。", "Common.Views.InsertTableDialog.txtColumns": "列数", "Common.Views.InsertTableDialog.txtMaxText": "该字段的最大值为{0}。", "Common.Views.InsertTableDialog.txtMinText": "该字段的最小值为{0}。", "Common.Views.InsertTableDialog.txtRows": "行数", "Common.Views.InsertTableDialog.txtTitle": "表格大小", "Common.Views.InsertTableDialog.txtTitleSplit": "拆分单元格", "Common.Views.LanguageDialog.labelSelect": "选择文档语言", "Common.Views.MacrosDialog.textCopy": "复制", "Common.Views.MacrosDialog.textCustomFunction": "自定义函数", "Common.Views.MacrosDialog.textDelete": "删除", "Common.Views.MacrosDialog.textLoading": "加载中…", "Common.Views.MacrosDialog.textMacros": "宏", "Common.Views.MacrosDialog.textMakeAutostart": "自启动", "Common.Views.MacrosDialog.textRename": "重命名", "Common.Views.MacrosDialog.textRun": "运行", "Common.Views.MacrosDialog.textSave": "保存", "Common.Views.MacrosDialog.textTitle": "宏", "Common.Views.MacrosDialog.textUnMakeAutostart": "取消自启动", "Common.Views.MacrosDialog.tipFunctionAdd": "添加自定义函数", "Common.Views.MacrosDialog.tipMacrosAdd": "添加宏", "Common.Views.MacrosDialog.tipMacrosRun": "运行", "Common.Views.OpenDialog.closeButtonText": "关闭文件", "Common.Views.OpenDialog.txtEncoding": "编码", "Common.Views.OpenDialog.txtIncorrectPwd": "密码不正确。", "Common.Views.OpenDialog.txtOpenFile": "输入密码来打开文件", "Common.Views.OpenDialog.txtPassword": "密码", "Common.Views.OpenDialog.txtPreview": "预览", "Common.Views.OpenDialog.txtProtected": "输入密码并打开文件后，将重置文件的当前密码。", "Common.Views.OpenDialog.txtTitle": "选择%1选项", "Common.Views.OpenDialog.txtTitleProtected": "受保护的文档", "Common.Views.PasswordDialog.txtDescription": "设置密码以保护此文档", "Common.Views.PasswordDialog.txtIncorrectPwd": "确认密码不相同", "Common.Views.PasswordDialog.txtPassword": "密码", "Common.Views.PasswordDialog.txtRepeat": "重复密码", "Common.Views.PasswordDialog.txtTitle": "设置密码", "Common.Views.PasswordDialog.txtWarning": "警告：如果您丢失或忘记了密码，则无法恢复。请把它放在安全的地方。", "Common.Views.PluginDlg.textLoading": "载入中", "Common.Views.PluginPanel.textClosePanel": "关闭插件", "Common.Views.PluginPanel.textLoading": "载入中", "Common.Views.Plugins.groupCaption": "插件", "Common.Views.Plugins.strPlugins": "插件", "Common.Views.Plugins.textBackgroundPlugins": "后台插件", "Common.Views.Plugins.textSettings": "设置", "Common.Views.Plugins.textStart": "开始", "Common.Views.Plugins.textStop": "停止", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "后台插件列表", "Common.Views.Plugins.tipMore": "更多", "Common.Views.Protection.hintAddPwd": "使用密码加密文档", "Common.Views.Protection.hintDelPwd": "删除密码", "Common.Views.Protection.hintPwd": "更改或删除密码", "Common.Views.Protection.hintSignature": "添加数字签名或签名栏", "Common.Views.Protection.txtAddPwd": "添加密码", "Common.Views.Protection.txtChangePwd": "修改密码", "Common.Views.Protection.txtDeletePwd": "删除密码", "Common.Views.Protection.txtEncrypt": "加密", "Common.Views.Protection.txtInvisibleSignature": "添加数字签名", "Common.Views.Protection.txtSignature": "签名", "Common.Views.Protection.txtSignatureLine": "添加签名栏", "Common.Views.RecentFiles.txtOpenRecent": "打开最近", "Common.Views.RenameDialog.textName": "文件名", "Common.Views.RenameDialog.txtInvalidName": "文件名不能包含以下任何字符：", "Common.Views.ReviewChanges.hintNext": "跳转到下一处更改", "Common.Views.ReviewChanges.hintPrev": "跳转到上一处更改", "Common.Views.ReviewChanges.mniFromFile": "文件中的文档", "Common.Views.ReviewChanges.mniFromStorage": "存储器中的文档", "Common.Views.ReviewChanges.mniFromUrl": "来自URL的文档", "Common.Views.ReviewChanges.mniMMFromFile": "从文件导入", "Common.Views.ReviewChanges.mniMMFromStorage": "来自存储设备", "Common.Views.ReviewChanges.mniMMFromUrl": "来自URL", "Common.Views.ReviewChanges.mniSettings": "比较设置", "Common.Views.ReviewChanges.strFast": "快速", "Common.Views.ReviewChanges.strFastDesc": "实时共同编辑。所有更改都将自动保存。", "Common.Views.ReviewChanges.strStrict": "严格", "Common.Views.ReviewChanges.strStrictDesc": "使用“保存”按钮同步您和其他人所做的更改。", "Common.Views.ReviewChanges.textEnable": "启用", "Common.Views.ReviewChanges.textWarnTrackChanges": "所有具有完全访问权限的用户都将打开“跟踪更改”。下次任何人打开文档时，“跟踪更改”将保持启用状态。", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "是否为每个人启用跟踪更改？", "Common.Views.ReviewChanges.tipAcceptCurrent": "同意当前更改并跳转到下一个更改", "Common.Views.ReviewChanges.tipCoAuthMode": "设置协同编辑模式", "Common.Views.ReviewChanges.tipCombine": "将当前文档与另一个文档合并", "Common.Views.ReviewChanges.tipCommentRem": "删除批注", "Common.Views.ReviewChanges.tipCommentRemCurrent": "删除当前批注", "Common.Views.ReviewChanges.tipCommentResolve": "标记注释为已解决", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "将所有的注释标记为已解决", "Common.Views.ReviewChanges.tipCompare": "将当前文档与另一个文档进行比较", "Common.Views.ReviewChanges.tipHistory": "显示版本历史", "Common.Views.ReviewChanges.tipMailRecepients": "邮件合并", "Common.Views.ReviewChanges.tipRejectCurrent": "否决当前更改并跳转到下一个更改", "Common.Views.ReviewChanges.tipReview": "跟踪更改", "Common.Views.ReviewChanges.tipReviewView": "选择要显示更改的模式", "Common.Views.ReviewChanges.tipSetDocLang": "设置文档语言", "Common.Views.ReviewChanges.tipSetSpelling": "拼写检查", "Common.Views.ReviewChanges.tipSharing": "管理文档访问权限", "Common.Views.ReviewChanges.txtAccept": "同意", "Common.Views.ReviewChanges.txtAcceptAll": "同意所有更改", "Common.Views.ReviewChanges.txtAcceptChanges": "同意更改", "Common.Views.ReviewChanges.txtAcceptCurrent": "同意当前更改", "Common.Views.ReviewChanges.txtChat": "聊天", "Common.Views.ReviewChanges.txtClose": "关闭", "Common.Views.ReviewChanges.txtCoAuthMode": "共同编辑模式", "Common.Views.ReviewChanges.txtCombine": "合并", "Common.Views.ReviewChanges.txtCommentRemAll": "删除所有批注", "Common.Views.ReviewChanges.txtCommentRemCurrent": "删除当前批注", "Common.Views.ReviewChanges.txtCommentRemMy": "删除我的批注", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "删除我当前的批注", "Common.Views.ReviewChanges.txtCommentRemove": "删除", "Common.Views.ReviewChanges.txtCommentResolve": "解决", "Common.Views.ReviewChanges.txtCommentResolveAll": "解决所有批注", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "将所有的注释标记为已解决", "Common.Views.ReviewChanges.txtCommentResolveMy": "将自己的注释标记为已解决", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "将自己当前的注释标记为已解决", "Common.Views.ReviewChanges.txtCompare": "比较", "Common.Views.ReviewChanges.txtDocLang": "语言", "Common.Views.ReviewChanges.txtEditing": "编辑中", "Common.Views.ReviewChanges.txtFinal": "同意所有更改 {0}", "Common.Views.ReviewChanges.txtFinalCap": "最终状态", "Common.Views.ReviewChanges.txtHistory": "版本历史", "Common.Views.ReviewChanges.txtMailMerge": "邮件合并", "Common.Views.ReviewChanges.txtMarkup": "所有更改 {0}", "Common.Views.ReviewChanges.txtMarkupCap": "标记和内容气球", "Common.Views.ReviewChanges.txtMarkupSimple": "所有更改 {0}<br>不显示内容气球", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "仅标记", "Common.Views.ReviewChanges.txtNext": "下一个", "Common.Views.ReviewChanges.txtOff": "为我关闭", "Common.Views.ReviewChanges.txtOffGlobal": "为我和所有人关闭", "Common.Views.ReviewChanges.txtOn": "为我开启", "Common.Views.ReviewChanges.txtOnGlobal": "为我和所有人开启", "Common.Views.ReviewChanges.txtOriginal": "否决所有更改 {0}", "Common.Views.ReviewChanges.txtOriginalCap": "原始状态", "Common.Views.ReviewChanges.txtPrev": "上一个", "Common.Views.ReviewChanges.txtPreview": "预览", "Common.Views.ReviewChanges.txtReject": "否决", "Common.Views.ReviewChanges.txtRejectAll": "否决所有更改", "Common.Views.ReviewChanges.txtRejectChanges": "否决更改", "Common.Views.ReviewChanges.txtRejectCurrent": "否决当前更改", "Common.Views.ReviewChanges.txtSharing": "分享", "Common.Views.ReviewChanges.txtSpelling": "拼写检查", "Common.Views.ReviewChanges.txtTurnon": "跟踪更改", "Common.Views.ReviewChanges.txtView": "显示模式", "Common.Views.ReviewChangesDialog.textTitle": "审查更改", "Common.Views.ReviewChangesDialog.txtAccept": "同意", "Common.Views.ReviewChangesDialog.txtAcceptAll": "同意所有更改", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "同意当前更改", "Common.Views.ReviewChangesDialog.txtNext": "跳转到下一处更改", "Common.Views.ReviewChangesDialog.txtPrev": "跳转到上一处更改", "Common.Views.ReviewChangesDialog.txtReject": "否决", "Common.Views.ReviewChangesDialog.txtRejectAll": "否决所有更改", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "否决当前更改", "Common.Views.ReviewPopover.textAdd": "添加", "Common.Views.ReviewPopover.textAddReply": "添加回复", "Common.Views.ReviewPopover.textCancel": "取消", "Common.Views.ReviewPopover.textClose": "关闭", "Common.Views.ReviewPopover.textComment": "批注", "Common.Views.ReviewPopover.textEdit": "确定", "Common.Views.ReviewPopover.textEnterComment": "在这里输入您的批注", "Common.Views.ReviewPopover.textFollowMove": "跟随移动", "Common.Views.ReviewPopover.textMention": "+提及将提供对文档的访问权限并发送电子邮件", "Common.Views.ReviewPopover.textMentionNotify": "+提及将通过电子邮件通知用户", "Common.Views.ReviewPopover.textOpenAgain": "再次打开", "Common.Views.ReviewPopover.textReply": "回复", "Common.Views.ReviewPopover.textResolve": "解决", "Common.Views.ReviewPopover.textViewResolved": "您无权重新打开批注", "Common.Views.ReviewPopover.txtAccept": "同意", "Common.Views.ReviewPopover.txtDeleteTip": "刪除", "Common.Views.ReviewPopover.txtEditTip": "编辑", "Common.Views.ReviewPopover.txtReject": "否决", "Common.Views.SaveAsDlg.textLoading": "载入中", "Common.Views.SaveAsDlg.textTitle": "要保存的文件夹", "Common.Views.SearchPanel.textCaseSensitive": "区分大小写", "Common.Views.SearchPanel.textCloseSearch": "关闭搜索", "Common.Views.SearchPanel.textContentChanged": "文件已更改。", "Common.Views.SearchPanel.textFind": "查找", "Common.Views.SearchPanel.textFindAndReplace": "查找和替换", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "｛0｝个项目已成功替换。", "Common.Views.SearchPanel.textMatchUsingRegExp": "使用正则表达式匹配", "Common.Views.SearchPanel.textNoMatches": "找不到匹配信息", "Common.Views.SearchPanel.textNoSearchResults": "没有搜索结果", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "已替换｛0｝/｛1｝项。其余｛2｝个项目已被其他用户锁定。", "Common.Views.SearchPanel.textReplace": "替换", "Common.Views.SearchPanel.textReplaceAll": "全部替换", "Common.Views.SearchPanel.textReplaceWith": "替换为", "Common.Views.SearchPanel.textSearchAgain": "{0}执行新的搜索{1}以获得准确的结果。", "Common.Views.SearchPanel.textSearchHasStopped": "搜索已停止", "Common.Views.SearchPanel.textSearchResults": "搜索结果：｛0｝/｛1｝", "Common.Views.SearchPanel.textSearchResultsTable": "搜索结果", "Common.Views.SearchPanel.textTooManyResults": "此处显示的结果太多", "Common.Views.SearchPanel.textWholeWords": "仅限完整单词", "Common.Views.SearchPanel.tipNextResult": "下一个结果", "Common.Views.SearchPanel.tipPreviousResult": "上一个结果", "Common.Views.SelectFileDlg.textLoading": "载入中", "Common.Views.SelectFileDlg.textTitle": "选择数据源", "Common.Views.ShapeShadowDialog.txtAngle": "角度", "Common.Views.ShapeShadowDialog.txtDistance": "距离", "Common.Views.ShapeShadowDialog.txtSize": "大小", "Common.Views.ShapeShadowDialog.txtTitle": "调整阴影", "Common.Views.ShapeShadowDialog.txtTransparency": "透明度", "Common.Views.SignDialog.textBold": "粗体", "Common.Views.SignDialog.textCertificate": "证书", "Common.Views.SignDialog.textChange": "修改", "Common.Views.SignDialog.textInputName": "输入签名者姓名", "Common.Views.SignDialog.textItalic": "斜体", "Common.Views.SignDialog.textNameError": "签名人姓名不能为空。", "Common.Views.SignDialog.textPurpose": "签署本文件的目的", "Common.Views.SignDialog.textSelect": "选择", "Common.Views.SignDialog.textSelectImage": "选择图像", "Common.Views.SignDialog.textSignature": "签名外观如下", "Common.Views.SignDialog.textTitle": "签署文件", "Common.Views.SignDialog.textUseImage": "或单击“选择图像”，使用图片作为签名", "Common.Views.SignDialog.textValid": "從％1到％2有效", "Common.Views.SignDialog.tipFontName": "字体名称", "Common.Views.SignDialog.tipFontSize": "字体大小", "Common.Views.SignSettingsDialog.textAllowComment": "允许签名者在签名对话框中添加批注", "Common.Views.SignSettingsDialog.textDefInstruction": "在签署此文档之前，请验证您正在签署的内容是否正确。", "Common.Views.SignSettingsDialog.textInfoEmail": "建议签署人的电子邮件", "Common.Views.SignSettingsDialog.textInfoName": "建议签署人", "Common.Views.SignSettingsDialog.textInfoTitle": "建议签署人称谓", "Common.Views.SignSettingsDialog.textInstructions": "签名人须知", "Common.Views.SignSettingsDialog.textShowDate": "在签名行中显示签名日期", "Common.Views.SignSettingsDialog.textTitle": "签名设置", "Common.Views.SignSettingsDialog.txtEmpty": "这是必填栏", "Common.Views.SymbolTableDialog.textCharacter": "字符", "Common.Views.SymbolTableDialog.textCode": "Unicode十六进制值", "Common.Views.SymbolTableDialog.textCopyright": "版权符号", "Common.Views.SymbolTableDialog.textDCQuote": "结束双引号", "Common.Views.SymbolTableDialog.textDOQuote": "开头双引号", "Common.Views.SymbolTableDialog.textEllipsis": "水平省略号", "Common.Views.SymbolTableDialog.textEmDash": "破折号", "Common.Views.SymbolTableDialog.textEmSpace": "空格", "Common.Views.SymbolTableDialog.textEnDash": "虚线", "Common.Views.SymbolTableDialog.textEnSpace": "半形空格", "Common.Views.SymbolTableDialog.textFont": "字体 ", "Common.Views.SymbolTableDialog.textNBHyphen": "不可分连字符", "Common.Views.SymbolTableDialog.textNBSpace": "不换行空格", "Common.Views.SymbolTableDialog.textPilcrow": "段落符号", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 字宽空白", "Common.Views.SymbolTableDialog.textRange": "范围", "Common.Views.SymbolTableDialog.textRecent": "最近使用的符号", "Common.Views.SymbolTableDialog.textRegistered": "注册标志", "Common.Views.SymbolTableDialog.textSCQuote": "结束单引号", "Common.Views.SymbolTableDialog.textSection": "章节标志", "Common.Views.SymbolTableDialog.textShortcut": "快捷键", "Common.Views.SymbolTableDialog.textSHyphen": "软连字号", "Common.Views.SymbolTableDialog.textSOQuote": "开始单引号", "Common.Views.SymbolTableDialog.textSpecial": "特殊字符", "Common.Views.SymbolTableDialog.textSymbols": "符号", "Common.Views.SymbolTableDialog.textTitle": "符号", "Common.Views.SymbolTableDialog.textTradeMark": "商标符号", "Common.Views.UserNameDialog.textDontShow": "不要再次询问我", "Common.Views.UserNameDialog.textLabel": "标签：", "Common.Views.UserNameDialog.textLabelError": "标签不能为空。", "DE.Controllers.DocProtection.txtIsProtectedComment": "文档受到保护。您只能在此文档中插入批注。", "DE.Controllers.DocProtection.txtIsProtectedForms": "文档受到保护。您只能填写此文档中的表单。", "DE.Controllers.DocProtection.txtIsProtectedTrack": "文档受到保护。您可以编辑此文档，但所有更改都将被跟踪。", "DE.Controllers.DocProtection.txtIsProtectedView": "文档受到保护。您只能查看此文档。", "DE.Controllers.DocProtection.txtWasProtectedComment": "文档已被另一个用户保护。\n您只能在此文档中插入批注。", "DE.Controllers.DocProtection.txtWasProtectedForms": "文档已被另一个用户保护。\n您只能填写此文档中的表单。", "DE.Controllers.DocProtection.txtWasProtectedTrack": "文档已被另一个用户保护。\n您可以编辑此文档，但所有更改都将被跟踪。", "DE.Controllers.DocProtection.txtWasProtectedView": "文档已被另一个用户保护。\n您只能查看此文档。", "DE.Controllers.DocProtection.txtWasUnprotected": "文件已解除保護。", "DE.Controllers.LeftMenu.leavePageText": "此文档中所有未保存的更改都将丢失<br>单击“取消”，然后单击“保存”以保存它们。单击“确定”放弃所有未保存的更改。", "DE.Controllers.LeftMenu.newDocumentTitle": "未命名的文档", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "警告", "DE.Controllers.LeftMenu.requestEditRightsText": "正在请求编辑权限...", "DE.Controllers.LeftMenu.textLoadHistory": "正在加载版本历史记录...", "DE.Controllers.LeftMenu.textNoTextFound": "无法找到您搜索的数据，请调整您的搜索选项。", "DE.Controllers.LeftMenu.textReplaceSkipped": "替换已完成。 {0}处跳过。", "DE.Controllers.LeftMenu.textReplaceSuccess": "已完成搜索。已替换的次数：{0}。", "DE.Controllers.LeftMenu.textSelectPath": "输入保存文件副本的路径", "DE.Controllers.LeftMenu.txtCompatible": "文档将保存为新格式。它将允许使用所有编辑器功能，但可能会影响文档布局<br>如果要使文件与旧的MS Word版本兼容，请使用高级设置的“兼容性”选项。", "DE.Controllers.LeftMenu.txtUntitled": "无标题", "DE.Controllers.LeftMenu.warnDownloadAs": "如果您继续以此格式保存，除文本之外的所有功能将丢失。<br>您确定要继续吗？", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "您的｛0｝将被转换为可编辑格式。这可能需要一段时间。生成的文档将进行优化以允许您编辑文本，因此它可能与原始｛0｝不完全相同，尤其是在原始文件包含大量图形的情况下。", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "如果您继续以此格式保存，一些格式可能会丢失。<br>您确定要继续吗？", "DE.Controllers.LeftMenu.warnReplaceString": "｛0｝不是替换字段的有效特殊字符。", "DE.Controllers.Main.applyChangesTextText": "加载更改...", "DE.Controllers.Main.applyChangesTitleText": "加载更改", "DE.Controllers.Main.confirmMaxChangesSize": "您执行的操作超过了为服务器设置的大小限制<br>按“撤消”取消上次操作，或按“继续”在本地机器继续操作（您需要下载文件或复制其内容以确保不会丢失任何内容）。", "DE.Controllers.Main.convertationTimeoutText": "转换超时", "DE.Controllers.Main.criticalErrorExtText": "按“确定”返回文档列表。", "DE.Controllers.Main.criticalErrorTitle": "错误", "DE.Controllers.Main.downloadErrorText": "下载失败", "DE.Controllers.Main.downloadMergeText": "下载中…", "DE.Controllers.Main.downloadMergeTitle": "下载中", "DE.Controllers.Main.downloadTextText": "正在下载文件...", "DE.Controllers.Main.downloadTitleText": "正在下载文件", "DE.Controllers.Main.errorAccessDeny": "您正在尝试执行您没有权限的操作。<br>请联系您的文档服务器管理员.", "DE.Controllers.Main.errorBadImageUrl": "图片URL地址不正确", "DE.Controllers.Main.errorCannotPasteImg": "我们无法从剪贴板粘贴此图像，但您可以将其保存到您的设备,然后\n从那里插入此此图片，或者您可以复制图像（不带文本）并将其粘贴到文档中。", "DE.Controllers.Main.errorCoAuthoringDisconnect": "服务器连接失败。该文档现在无法编辑", "DE.Controllers.Main.errorComboSeries": "若要创建组合图表，请至少选择两个系列的数据。", "DE.Controllers.Main.errorCompare": "“比较文档”功能在共同编辑时不可用。", "DE.Controllers.Main.errorConnectToServer": "这份文件无法保存。请检查连接设置或联系您的管理员。<br>当你点击“OK”按钮,系统将提示您下载文档。", "DE.Controllers.Main.errorDatabaseConnection": "外部错误。<br>数据库连接错误。如果错误仍然存​​在，请联系支持人员。", "DE.Controllers.Main.errorDataEncrypted": "加密更改已收到，无法对其解密。", "DE.Controllers.Main.errorDataRange": "数据范围不正确", "DE.Controllers.Main.errorDefaultMessage": "错误代码：%1", "DE.Controllers.Main.errorDirectUrl": "请验证指向文档的链接<br>此链接必须是要下载的文档的直接链接。", "DE.Controllers.Main.errorEditingDownloadas": "使用文档时出错<br>使用“下载为”选项将文件备份副本保存到驱动器。", "DE.Controllers.Main.errorEditingSaveas": "使用文档时出错<br>使用“另存为…”选项将文件备份副本保存到驱动器。", "DE.Controllers.Main.errorEditProtectedRange": "此选区已受保护，无法编辑。", "DE.Controllers.Main.errorEmailClient": "找不到电子邮件客户端。", "DE.Controllers.Main.errorEmptyTOC": "将样式库中的标题样式应用到所选文件上", "DE.Controllers.Main.errorFilePassProtect": "该文档受密码保护，无法被打开。", "DE.Controllers.Main.errorFileSizeExceed": "文件大小超出了为服务器设置的限制.<br>有关详细信息，请与文档服务器管理员联系。", "DE.Controllers.Main.errorForceSave": "保存文件时出错。请使用“下载为”选项将文件保存到驱动器，或稍后再试。", "DE.Controllers.Main.errorInconsistentExt": "打开文件时出错<br>文件内容与文件扩展名不匹配。", "DE.Controllers.Main.errorInconsistentExtDocx": "打开文件时出错<br>文件内容对应于文本文档（例如docx），但文件的扩展名不一致：%1。", "DE.Controllers.Main.errorInconsistentExtPdf": "打开文件时出错<br>文件内容对应于以下格式之一：pdf/djvu/xps/oxfs，但文件的扩展名不一致：%1。", "DE.Controllers.Main.errorInconsistentExtPptx": "打开文件时出错<br>文件内容对应于演示文稿（例如pptx），但文件的扩展名不一致：%1。", "DE.Controllers.Main.errorInconsistentExtXlsx": "打开文件时出错<br>文件内容对应于电子表格（例如xlsx），但文件的扩展名不一致：%1。", "DE.Controllers.Main.errorKeyEncrypt": "未知密钥描述符", "DE.Controllers.Main.errorKeyExpire": "密钥描述符已过期", "DE.Controllers.Main.errorLoadingFont": "字体未加载<br>请与您的文档服务器管理员联系。", "DE.Controllers.Main.errorMailMergeLoadFile": "加载文档失败。请选择其他文件。", "DE.Controllers.Main.errorMailMergeSaveFile": "合并失败", "DE.Controllers.Main.errorNoTOC": "没有要更新的目录。可以从“参考”选项卡插入一个。", "DE.Controllers.Main.errorPasswordIsNotCorrect": "您提供的密码不正确<br>验证CAPS LOCK键是否关闭，并确保使用正确的大写字母。", "DE.Controllers.Main.errorProcessSaveResult": "保存失败", "DE.Controllers.Main.errorSaveWatermark": "该文件包含来自其他域名的水印图片。<br>要在 PDF 中显示水印，请将图片链接更新为与文档相同的域名，或从电脑上传图片。", "DE.Controllers.Main.errorServerVersion": "编辑器版本已更新。页面将被重新加载以应用更改。", "DE.Controllers.Main.errorSessionAbsolute": "文档编辑会话已过期。请重新加载页面", "DE.Controllers.Main.errorSessionIdle": "这份文件已经很长时间没有编辑了。请重新加载页面。", "DE.Controllers.Main.errorSessionToken": "与服务器的连接已中断。请重新加载页面。", "DE.Controllers.Main.errorSetPassword": "无法设置密码。", "DE.Controllers.Main.errorStockChart": "行顺序不正确，要建立股票图表，将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "DE.Controllers.Main.errorSubmit": "提交失败", "DE.Controllers.Main.errorTextFormWrongFormat": "输入的值与字段的格式不匹配。", "DE.Controllers.Main.errorToken": "文档安全令牌的格式不正确<br>请与您的文档服务器管理员联系。", "DE.Controllers.Main.errorTokenExpire": "文档安全令牌已过期。<br>请与您的文档服务器管理员联系。", "DE.Controllers.Main.errorUpdateVersion": "\n该文件版本已经改变了。该页面将被重新加载。", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "网络连接已恢复，文件版本已更改<br>在继续工作之前，您需要下载文件或复制其内容以确保不会丢失任何内容，然后重新加载此页面。", "DE.Controllers.Main.errorUserDrop": "该文件现在无法访问。", "DE.Controllers.Main.errorUsersExceed": "超出原服务计划可允许的帐户数量", "DE.Controllers.Main.errorViewerDisconnect": "连接失败。您仍然可以查看文档<br>，但在连接恢复之前无法下载或打印。", "DE.Controllers.Main.leavePageText": "您在本文档中有未保存的更改。点击“留在这个页面”，然后点击“保存”保存。点击“离开此页面”，放弃所有未保存的更改。", "DE.Controllers.Main.leavePageTextOnClose": "此文档中所有未保存的更改都将丢失<br>单击“取消”，然后单击“保存”以保存它们。单击“确定”放弃所有未保存的更改。", "DE.Controllers.Main.loadFontsTextText": "数据加载中…", "DE.Controllers.Main.loadFontsTitleText": "数据加载中", "DE.Controllers.Main.loadFontTextText": "数据加载中…", "DE.Controllers.Main.loadFontTitleText": "数据加载中", "DE.Controllers.Main.loadImagesTextText": "图片加载中…", "DE.Controllers.Main.loadImagesTitleText": "图片加载中", "DE.Controllers.Main.loadImageTextText": "图片加载中…", "DE.Controllers.Main.loadImageTitleText": "图片加载中", "DE.Controllers.Main.loadingDocumentTextText": "文件加载中…", "DE.Controllers.Main.loadingDocumentTitleText": "文件加载中…", "DE.Controllers.Main.mailMergeLoadFileText": "正在加载数据源...", "DE.Controllers.Main.mailMergeLoadFileTitle": "正在加载数据源", "DE.Controllers.Main.notcriticalErrorTitle": "警告", "DE.Controllers.Main.openErrorText": "打开文件时发生错误", "DE.Controllers.Main.openTextText": "正在打开文档...", "DE.Controllers.Main.openTitleText": "正在打开文件", "DE.Controllers.Main.printTextText": "正在打印文件", "DE.Controllers.Main.printTitleText": "正在打印文件", "DE.Controllers.Main.reloadButtonText": "重新加载页面", "DE.Controllers.Main.requestEditFailedMessageText": "有人正在编辑此文档。请稍后再试。", "DE.Controllers.Main.requestEditFailedTitleText": "访问被拒绝", "DE.Controllers.Main.saveErrorText": "保存文件时发生错误", "DE.Controllers.Main.saveErrorTextDesktop": "无法保存或创建此文件<br>可能的原因有：<br>1.该文件是只读的<br>2.其他用户正在编辑该文件<br>3.磁盘已满或已损坏。", "DE.Controllers.Main.saveTextText": "正在保存文档...", "DE.Controllers.Main.saveTitleText": "正在保存文件", "DE.Controllers.Main.savingText": "保存中", "DE.Controllers.Main.scriptLoadError": "连接速度过慢，部分组件无法被加载。请重新加载页面。", "DE.Controllers.Main.sendMergeText": "发送合并中...", "DE.Controllers.Main.sendMergeTitle": "发送合并", "DE.Controllers.Main.splitDividerErrorText": "行数必须为%1的除数。", "DE.Controllers.Main.splitMaxColsErrorText": "列数必须小于%1。", "DE.Controllers.Main.splitMaxRowsErrorText": "行数必须小于%1。", "DE.Controllers.Main.textAnonymous": "匿名用户", "DE.Controllers.Main.textAnyone": "任何人", "DE.Controllers.Main.textApplyAll": "应用于所有公式", "DE.Controllers.Main.textBuyNow": "瀏覽網站", "DE.Controllers.Main.textChangesSaved": "所有更改已保存", "DE.Controllers.Main.textClose": "关闭", "DE.Controllers.Main.textCloseTip": "点击关闭提示", "DE.Controllers.Main.textConnectionLost": "正在尝试连接。请检查连接设置。", "DE.Controllers.Main.textContactUs": "联系销售人员", "DE.Controllers.Main.textContinue": "继续", "DE.Controllers.Main.textConvertEquation": "此方程式是使用旧版本的方程式编辑器创建的，该编辑器已不再受支持。若要编辑它，请将公式转换为Office Math ML格式<br>是否立即转换？", "DE.Controllers.Main.textCustomLoader": "请注意，根据许可条款您无权更改加载程序。<br>请联系我们的销售部门获取报价。", "DE.Controllers.Main.textDisconnect": "失去网络连接", "DE.Controllers.Main.textGuest": "访客", "DE.Controllers.Main.textHasMacros": "这个文件带有自动宏。<br>您想要运行宏吗？", "DE.Controllers.Main.textLearnMore": "了解更多", "DE.Controllers.Main.textLoadingDocument": "文件加载中…", "DE.Controllers.Main.textLongName": "输入一个少于128个字符的名称。", "DE.Controllers.Main.textNoLicenseTitle": "已达到许可证最大连接数限制", "DE.Controllers.Main.textPaidFeature": "付费功能", "DE.Controllers.Main.textReconnect": "连接已恢复", "DE.Controllers.Main.textRemember": "记住我对所有文件的选择", "DE.Controllers.Main.textRememberMacros": "记住我的选择并应用到全部宏", "DE.Controllers.Main.textRenameError": "用户名不能为空。", "DE.Controllers.Main.textRenameLabel": "输入用于协作的名称", "DE.Controllers.Main.textRequestMacros": "一个宏向 URL 发出请求。您想要允许向 %1 发出请求吗？", "DE.Controllers.Main.textShape": "形状", "DE.Controllers.Main.textSignature": "签名", "DE.Controllers.Main.textStrict": "严格模式", "DE.Controllers.Main.textText": "文字", "DE.Controllers.Main.textTryQuickPrint": "您已选择“快速打印”：整个文档将被打印到最近选择的打印机或者默认打印机。<br>您想要继续吗？", "DE.Controllers.Main.textTryUndoRedo": "对于快速的协同编辑模式，取消/重做功能是禁用的。< br >单击“严格模式”按钮切换到严格co-editing模式编辑该文件没有其他用户干扰和发送您的更改只后你拯救他们。您可以使用编辑器高级设置在编辑模式之间切换。", "DE.Controllers.Main.textTryUndoRedoWarn": "快速共同编辑模式下，撤销/重做功能被禁用。", "DE.Controllers.Main.textUndo": "撤消", "DE.Controllers.Main.textUpdateVersion": "现在无法编辑该文档。<br>正在尝试更新文件，请稍候...", "DE.Controllers.Main.textUpdating": "更新中", "DE.Controllers.Main.titleLicenseExp": "许可证过期", "DE.Controllers.Main.titleLicenseNotActive": "授权证书未激活", "DE.Controllers.Main.titleServerVersion": "编辑器已更新", "DE.Controllers.Main.titleUpdateVersion": "版本已更改", "DE.Controllers.Main.txtAbove": "上方", "DE.Controllers.Main.txtArt": "在这输入文字", "DE.Controllers.Main.txtBasicShapes": "基本形状", "DE.Controllers.Main.txtBelow": "下面", "DE.Controllers.Main.txtBookmarkError": "错误！书签未定义。", "DE.Controllers.Main.txtButtons": "按钮", "DE.Controllers.Main.txtCallouts": "标注", "DE.Controllers.Main.txtCharts": "图表", "DE.Controllers.Main.txtChoose": "选择一项", "DE.Controllers.Main.txtClickToLoad": "单击以加载图像", "DE.Controllers.Main.txtCurrentDocument": "当前文件", "DE.Controllers.Main.txtDiagramTitle": "图表标题", "DE.Controllers.Main.txtEditingMode": "设置编辑模式..", "DE.Controllers.Main.txtEndOfFormula": "公式意外结束", "DE.Controllers.Main.txtEnterDate": "输入日期", "DE.Controllers.Main.txtErrorLoadHistory": "历史加载失败", "DE.Controllers.Main.txtEvenPage": "偶数页", "DE.Controllers.Main.txtFiguredArrows": "图形箭头", "DE.Controllers.Main.txtFirstPage": "首页", "DE.Controllers.Main.txtFooter": "页脚", "DE.Controllers.Main.txtFormulaNotInTable": "公式不在表格中", "DE.Controllers.Main.txtHeader": "页眉", "DE.Controllers.Main.txtHyperlink": "超链接", "DE.Controllers.Main.txtIndTooLarge": "索引太大", "DE.Controllers.Main.txtLines": "行", "DE.Controllers.Main.txtMainDocOnly": "错误！仅限主文档。", "DE.Controllers.Main.txtMath": "数学", "DE.Controllers.Main.txtMissArg": "缺少参数", "DE.Controllers.Main.txtMissOperator": "缺少运算符", "DE.Controllers.Main.txtNeedSynchronize": "您有更新", "DE.Controllers.Main.txtNone": "无", "DE.Controllers.Main.txtNoTableOfContents": "文档中没有标题。将标题样式应用于文本，使其显示在目录中。", "DE.Controllers.Main.txtNoTableOfFigures": "找不到图表项目表。", "DE.Controllers.Main.txtNoText": "错误！文档中没有指定样式的文本。", "DE.Controllers.Main.txtNotInTable": "不在表格中", "DE.Controllers.Main.txtNotValidBookmark": "错误！不是有效的书签自引用。", "DE.Controllers.Main.txtOddPage": "奇数页", "DE.Controllers.Main.txtOnPage": "在页面上", "DE.Controllers.Main.txtRectangles": "矩形", "DE.Controllers.Main.txtSameAsPrev": "与上一个相同", "DE.Controllers.Main.txtSaveCopyAsComplete": "已成功保存文件副本", "DE.Controllers.Main.txtScheme_Aspect": "切面", "DE.Controllers.Main.txtScheme_Blue": "蓝色", "DE.Controllers.Main.txtScheme_Blue_Green": "蓝绿色", "DE.Controllers.Main.txtScheme_Blue_II": "蓝色2", "DE.Controllers.Main.txtScheme_Blue_Warm": "暖蓝色", "DE.Controllers.Main.txtScheme_Grayscale": "灰度", "DE.Controllers.Main.txtScheme_Green": "绿色", "DE.Controllers.Main.txtScheme_Green_Yellow": "黄绿色", "DE.Controllers.Main.txtScheme_Marquee": "选框", "DE.Controllers.Main.txtScheme_Median": "中位数", "DE.Controllers.Main.txtScheme_Office": "Office", "DE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "DE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "DE.Controllers.Main.txtScheme_Orange": "橙色", "DE.Controllers.Main.txtScheme_Orange_Red": "橙红色", "DE.Controllers.Main.txtScheme_Paper": "纸张", "DE.Controllers.Main.txtScheme_Red": "红色", "DE.Controllers.Main.txtScheme_Red_Orange": "红橙色", "DE.Controllers.Main.txtScheme_Red_Violet": "红紫色", "DE.Controllers.Main.txtScheme_Slipstream": "实时流处理引擎Slipstream", "DE.Controllers.Main.txtScheme_Violet": "紫色", "DE.Controllers.Main.txtScheme_Violet_II": "紫色2", "DE.Controllers.Main.txtScheme_Yellow": "黄色", "DE.Controllers.Main.txtScheme_Yellow_Orange": "黄橙色", "DE.Controllers.Main.txtSection": "-部分", "DE.Controllers.Main.txtSeries": "序列", "DE.Controllers.Main.txtShape_accentBorderCallout1": "线形标注1（带边框和强调线）", "DE.Controllers.Main.txtShape_accentBorderCallout2": "线形标注2（带边框和强调线）", "DE.Controllers.Main.txtShape_accentBorderCallout3": "线形标注3（带边框和强调线）", "DE.Controllers.Main.txtShape_accentCallout1": "线形标注1（强调线）", "DE.Controllers.Main.txtShape_accentCallout2": "线形标注2（强调线）", "DE.Controllers.Main.txtShape_accentCallout3": "线形标注3（强调线）", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "返回或上一步按鈕", "DE.Controllers.Main.txtShape_actionButtonBeginning": "开始按钮", "DE.Controllers.Main.txtShape_actionButtonBlank": "空白按钮", "DE.Controllers.Main.txtShape_actionButtonDocument": "“文档”按钮", "DE.Controllers.Main.txtShape_actionButtonEnd": "结束按钮", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "“前进”或“下一步”按钮", "DE.Controllers.Main.txtShape_actionButtonHelp": "“帮助”按钮", "DE.Controllers.Main.txtShape_actionButtonHome": "主页按钮", "DE.Controllers.Main.txtShape_actionButtonInformation": "信息按鈕", "DE.Controllers.Main.txtShape_actionButtonMovie": "电影按钮", "DE.Controllers.Main.txtShape_actionButtonReturn": "返回按钮", "DE.Controllers.Main.txtShape_actionButtonSound": "声音按钮", "DE.Controllers.Main.txtShape_arc": "弧", "DE.Controllers.Main.txtShape_bentArrow": "弯曲箭头", "DE.Controllers.Main.txtShape_bentConnector5": "弯头连接器", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "弯头箭头连接器", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "弯头双箭头连接器", "DE.Controllers.Main.txtShape_bentUpArrow": "向上弯曲箭头", "DE.Controllers.Main.txtShape_bevel": "斜角", "DE.Controllers.Main.txtShape_blockArc": "弧块", "DE.Controllers.Main.txtShape_borderCallout1": "线形标注1", "DE.Controllers.Main.txtShape_borderCallout2": "线形标注2", "DE.Controllers.Main.txtShape_borderCallout3": "线形标注3", "DE.Controllers.Main.txtShape_bracePair": "双花括号", "DE.Controllers.Main.txtShape_callout1": "线形标注1（无边框）", "DE.Controllers.Main.txtShape_callout2": "线形标注2（无边框）", "DE.Controllers.Main.txtShape_callout3": "线形标注3（无边框）", "DE.Controllers.Main.txtShape_can": "能", "DE.Controllers.Main.txtShape_chevron": "V形", "DE.Controllers.Main.txtShape_chord": "和弦", "DE.Controllers.Main.txtShape_circularArrow": "圆形箭头", "DE.Controllers.Main.txtShape_cloud": "云", "DE.Controllers.Main.txtShape_cloudCallout": "云标注", "DE.Controllers.Main.txtShape_corner": "角", "DE.Controllers.Main.txtShape_cube": "立方体", "DE.Controllers.Main.txtShape_curvedConnector3": "弯曲连接器", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "弯曲箭头连接器", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "弯曲双箭头连接器", "DE.Controllers.Main.txtShape_curvedDownArrow": "向下弯曲箭头", "DE.Controllers.Main.txtShape_curvedLeftArrow": "弯曲左箭头", "DE.Controllers.Main.txtShape_curvedRightArrow": "弯曲右箭头", "DE.Controllers.Main.txtShape_curvedUpArrow": "向上弯曲箭头", "DE.Controllers.Main.txtShape_decagon": "十边形", "DE.Controllers.Main.txtShape_diagStripe": "对角线条纹", "DE.Controllers.Main.txtShape_diamond": "菱形", "DE.Controllers.Main.txtShape_dodecagon": "十二边形", "DE.Controllers.Main.txtShape_donut": "圆环图", "DE.Controllers.Main.txtShape_doubleWave": "双波浪线", "DE.Controllers.Main.txtShape_downArrow": "向下箭头", "DE.Controllers.Main.txtShape_downArrowCallout": "下箭头标注", "DE.Controllers.Main.txtShape_ellipse": "椭圆", "DE.Controllers.Main.txtShape_ellipseRibbon": "向下弯曲的丝带", "DE.Controllers.Main.txtShape_ellipseRibbon2": "向上弯曲缎带", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "流程图：交替流程", "DE.Controllers.Main.txtShape_flowChartCollate": "流程图：整理", "DE.Controllers.Main.txtShape_flowChartConnector": "流程图：连接器", "DE.Controllers.Main.txtShape_flowChartDecision": "流程图：决策", "DE.Controllers.Main.txtShape_flowChartDelay": "流程图：延迟", "DE.Controllers.Main.txtShape_flowChartDisplay": "流程图：显示", "DE.Controllers.Main.txtShape_flowChartDocument": "流程图：文件", "DE.Controllers.Main.txtShape_flowChartExtract": "流程图：提取", "DE.Controllers.Main.txtShape_flowChartInputOutput": "流程图：数据", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "流程图：内部存储", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "流程图：磁盘", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "流程图：直接访问存储器", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "流程图：顺序访问存储器", "DE.Controllers.Main.txtShape_flowChartManualInput": "流程图：手动输入", "DE.Controllers.Main.txtShape_flowChartManualOperation": "流程图：手动操作", "DE.Controllers.Main.txtShape_flowChartMerge": "流程图：合并", "DE.Controllers.Main.txtShape_flowChartMultidocument": "流程图：多文件", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "流程图：页外连接器", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "流程图：存储的数据", "DE.Controllers.Main.txtShape_flowChartOr": "流程图：或", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "流程图：预定义程序", "DE.Controllers.Main.txtShape_flowChartPreparation": "流程图：准备", "DE.Controllers.Main.txtShape_flowChartProcess": "流程图：流程", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "流程图：卡片", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "流程图：穿孔纸带", "DE.Controllers.Main.txtShape_flowChartSort": "流程图：排序", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "流程图：求和结点", "DE.Controllers.Main.txtShape_flowChartTerminator": "流程图：终止符", "DE.Controllers.Main.txtShape_foldedCorner": "折角", "DE.Controllers.Main.txtShape_frame": "框", "DE.Controllers.Main.txtShape_halfFrame": "半框", "DE.Controllers.Main.txtShape_heart": "心形", "DE.Controllers.Main.txtShape_heptagon": "七边形", "DE.Controllers.Main.txtShape_hexagon": "六边形", "DE.Controllers.Main.txtShape_homePlate": "五角形", "DE.Controllers.Main.txtShape_horizontalScroll": "水平滚动", "DE.Controllers.Main.txtShape_irregularSeal1": "爆炸效果1", "DE.Controllers.Main.txtShape_irregularSeal2": "爆炸效果2", "DE.Controllers.Main.txtShape_leftArrow": "左箭头", "DE.Controllers.Main.txtShape_leftArrowCallout": "左箭头标注", "DE.Controllers.Main.txtShape_leftBrace": "左括号", "DE.Controllers.Main.txtShape_leftBracket": "左括号", "DE.Controllers.Main.txtShape_leftRightArrow": "左右箭头", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "左右箭头标注", "DE.Controllers.Main.txtShape_leftRightUpArrow": "左右向上箭头", "DE.Controllers.Main.txtShape_leftUpArrow": "左上箭头", "DE.Controllers.Main.txtShape_lightningBolt": "闪电符号", "DE.Controllers.Main.txtShape_line": "折线图", "DE.Controllers.Main.txtShape_lineWithArrow": "箭头", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "双箭头", "DE.Controllers.Main.txtShape_mathDivide": "除法", "DE.Controllers.Main.txtShape_mathEqual": "等于", "DE.Controllers.Main.txtShape_mathMinus": "减去", "DE.Controllers.Main.txtShape_mathMultiply": "乘", "DE.Controllers.Main.txtShape_mathNotEqual": "不等于", "DE.Controllers.Main.txtShape_mathPlus": "加", "DE.Controllers.Main.txtShape_moon": "月亮", "DE.Controllers.Main.txtShape_noSmoking": "“否”符号", "DE.Controllers.Main.txtShape_notchedRightArrow": "带凹口的右箭头", "DE.Controllers.Main.txtShape_octagon": "八边形", "DE.Controllers.Main.txtShape_parallelogram": "平行四边形", "DE.Controllers.Main.txtShape_pentagon": "五角形", "DE.Controllers.Main.txtShape_pie": "圆饼图", "DE.Controllers.Main.txtShape_plaque": "签署", "DE.Controllers.Main.txtShape_plus": "加", "DE.Controllers.Main.txtShape_polyline1": "涂鸦", "DE.Controllers.Main.txtShape_polyline2": "自由变形", "DE.Controllers.Main.txtShape_quadArrow": "四向箭头", "DE.Controllers.Main.txtShape_quadArrowCallout": "四箭头标注", "DE.Controllers.Main.txtShape_rect": "矩形", "DE.Controllers.Main.txtShape_ribbon": "向下丝带", "DE.Controllers.Main.txtShape_ribbon2": "向上丝带", "DE.Controllers.Main.txtShape_rightArrow": "右箭头", "DE.Controllers.Main.txtShape_rightArrowCallout": "右箭头标注", "DE.Controllers.Main.txtShape_rightBrace": "右大括号", "DE.Controllers.Main.txtShape_rightBracket": "右括号", "DE.Controllers.Main.txtShape_round1Rect": "圆形单角矩形", "DE.Controllers.Main.txtShape_round2DiagRect": "圆斜角矩形", "DE.Controllers.Main.txtShape_round2SameRect": "圆形同侧角矩形", "DE.Controllers.Main.txtShape_roundRect": "圆角矩形", "DE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "DE.Controllers.Main.txtShape_smileyFace": "笑脸", "DE.Controllers.Main.txtShape_snip1Rect": "剪下单角矩形", "DE.Controllers.Main.txtShape_snip2DiagRect": "减去对角矩形", "DE.Controllers.Main.txtShape_snip2SameRect": "剪下同一边角矩形", "DE.Controllers.Main.txtShape_snipRoundRect": "减去和圆形单角矩形", "DE.Controllers.Main.txtShape_spline": "曲线", "DE.Controllers.Main.txtShape_star10": "10角星", "DE.Controllers.Main.txtShape_star12": "12 角星形", "DE.Controllers.Main.txtShape_star16": "16角星", "DE.Controllers.Main.txtShape_star24": "24角星", "DE.Controllers.Main.txtShape_star32": "32角星", "DE.Controllers.Main.txtShape_star4": "4角星", "DE.Controllers.Main.txtShape_star5": "5角星", "DE.Controllers.Main.txtShape_star6": "6角星", "DE.Controllers.Main.txtShape_star7": "7角星", "DE.Controllers.Main.txtShape_star8": "8角星", "DE.Controllers.Main.txtShape_stripedRightArrow": "条纹右箭头", "DE.Controllers.Main.txtShape_sun": "周日", "DE.Controllers.Main.txtShape_teardrop": "泪珠", "DE.Controllers.Main.txtShape_textRect": "文本框", "DE.Controllers.Main.txtShape_trapezoid": "梯形", "DE.Controllers.Main.txtShape_triangle": "三角形", "DE.Controllers.Main.txtShape_upArrow": "向上箭头", "DE.Controllers.Main.txtShape_upArrowCallout": "向上箭头标注", "DE.Controllers.Main.txtShape_upDownArrow": "上下箭头", "DE.Controllers.Main.txtShape_uturnArrow": "U形转弯箭头", "DE.Controllers.Main.txtShape_verticalScroll": "垂直滚动", "DE.Controllers.Main.txtShape_wave": "波浪", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "椭圆形标注", "DE.Controllers.Main.txtShape_wedgeRectCallout": "矩形标注", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "圆角矩形标注", "DE.Controllers.Main.txtStarsRibbons": "星星和丝带", "DE.Controllers.Main.txtStyle_Book_Title": "书名", "DE.Controllers.Main.txtStyle_Caption": "标题", "DE.Controllers.Main.txtStyle_Default_Paragraph_Font": "默认段落字体", "DE.Controllers.Main.txtStyle_Emphasis": "强调", "DE.Controllers.Main.txtStyle_endnote_reference": "尾注引用", "DE.Controllers.Main.txtStyle_endnote_text": "尾注文本", "DE.Controllers.Main.txtStyle_footnote_reference": "脚注引用", "DE.Controllers.Main.txtStyle_footnote_text": "脚注文本", "DE.Controllers.Main.txtStyle_Heading_1": "标题 1", "DE.Controllers.Main.txtStyle_Heading_2": "标题 2", "DE.Controllers.Main.txtStyle_Heading_3": "标题 3", "DE.Controllers.Main.txtStyle_Heading_4": "标题 4", "DE.Controllers.Main.txtStyle_Heading_5": "标题 5", "DE.Controllers.Main.txtStyle_Heading_6": "标题 6", "DE.Controllers.Main.txtStyle_Heading_7": "标题 7", "DE.Controllers.Main.txtStyle_Heading_8": "标题 8", "DE.Controllers.Main.txtStyle_Heading_9": "标题 9", "DE.Controllers.Main.txtStyle_Intense_Emphasis": "明显强调", "DE.Controllers.Main.txtStyle_Intense_Quote": "强调引用", "DE.Controllers.Main.txtStyle_Intense_Reference": "强烈引用", "DE.Controllers.Main.txtStyle_List_Paragraph": "段落列表", "DE.Controllers.Main.txtStyle_No_List": "无列表", "DE.Controllers.Main.txtStyle_No_Spacing": "无间距", "DE.Controllers.Main.txtStyle_Normal": "正文", "DE.Controllers.Main.txtStyle_Quote": "引用", "DE.Controllers.Main.txtStyle_Strong": "强", "DE.Controllers.Main.txtStyle_Subtitle": "副标题", "DE.Controllers.Main.txtStyle_Subtle_Emphasis": "轻微强调", "DE.Controllers.Main.txtStyle_Subtle_Reference": "轻微引用", "DE.Controllers.Main.txtStyle_Title": "标题", "DE.Controllers.Main.txtSyntaxError": "语法错误", "DE.Controllers.Main.txtTableInd": "表索引不能为零", "DE.Controllers.Main.txtTableOfContents": "目录", "DE.Controllers.Main.txtTableOfFigures": "图表目录", "DE.Controllers.Main.txtTOCHeading": "目录标题", "DE.Controllers.Main.txtTooLarge": "数字太大无法格式化", "DE.Controllers.Main.txtTypeEquation": "在此处键入方程式。", "DE.Controllers.Main.txtUndefBookmark": "未定义的书签", "DE.Controllers.Main.txtXAxis": "X轴", "DE.Controllers.Main.txtYAxis": "Y轴", "DE.Controllers.Main.txtZeroDivide": "除以零", "DE.Controllers.Main.unknownErrorText": "未知错误。", "DE.Controllers.Main.unsupportedBrowserErrorText": "您的浏览器不受支持", "DE.Controllers.Main.uploadDocExtMessage": "未知的文件格式。", "DE.Controllers.Main.uploadDocFileCountMessage": "未上传任何文档。", "DE.Controllers.Main.uploadDocSizeMessage": "超出最大文件大小限制。", "DE.Controllers.Main.uploadImageExtMessage": "未知图像格式。", "DE.Controllers.Main.uploadImageFileCountMessage": "没有上传图片", "DE.Controllers.Main.uploadImageSizeMessage": "图像太大。最大大小为25 MB。", "DE.Controllers.Main.uploadImageTextText": "图片上传中...", "DE.Controllers.Main.uploadImageTitleText": "图片上传中", "DE.Controllers.Main.waitText": "请稍候...", "DE.Controllers.Main.warnBrowserIE9": "该应用程序在IE9上的功能很差。使用IE10或更高版本", "DE.Controllers.Main.warnBrowserZoom": "您的浏览器当前缩放设置不完全支持。请按Ctrl + 0重设为默认缩放。", "DE.Controllers.Main.warnLicenseAnonymous": "匿名用户的访问被拒绝<br>此文档将仅打开以供查看。", "DE.Controllers.Main.warnLicenseBefore": "许可证未激活<br>请与管理员联系。", "DE.Controllers.Main.warnLicenseExceeded": "您已达到同时连接到%1编辑器的限制。此文档将仅打开以供查看<br>请与管理员联系以了解更多信息。", "DE.Controllers.Main.warnLicenseExp": "您的许可证已过期。<br>请更新您的许可证并刷新页面。", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "许可证已过期。<br>您现在不能使用文档编辑功能。<br>请联系您的管理员。", "DE.Controllers.Main.warnLicenseLimitedRenewed": "许可证需要更新。<br>您现在只能使用受限的文档编辑功能。<br>请联系管理员以获取完整权限", "DE.Controllers.Main.warnLicenseUsersExceeded": "您已达到%1编辑器的用户限制。请与管理员联系以了解更多信息。", "DE.Controllers.Main.warnNoLicense": "您已达到同时连接到%1编辑器的限制。此文档将仅打开以供查看<br>有关个人升级条款，请与%1销售团队联系。", "DE.Controllers.Main.warnNoLicenseUsers": "您已达到%1编辑器的用户限制。有关个人升级条款，请与%1销售团队联系。", "DE.Controllers.Main.warnProcessRightsChange": "您被拒绝了编辑文件的权限。", "DE.Controllers.Main.warnStartFilling": "表单正在填写中。<br>当前尚不能够编辑文件。", "DE.Controllers.Navigation.txtBeginning": "文件开头", "DE.Controllers.Navigation.txtGotoBeginning": "转到文档的开头", "DE.Controllers.Print.textMarginsLast": "最后一次自定义", "DE.Controllers.Print.txtCustom": "自定义", "DE.Controllers.Print.txtPrintRangeInvalid": "无效的打印范围", "DE.Controllers.Print.txtPrintRangeSingleRange": "输入单个页码或者单个页码范围 (例如 5-12)。您也可以将文档打印为 PDF 文件。", "DE.Controllers.Search.notcriticalErrorTitle": "警告", "DE.Controllers.Search.textNoTextFound": "无法找到您搜索的数据，请调整您的搜索选项。", "DE.Controllers.Search.textReplaceSkipped": "替换已完成。 {0}处跳过。", "DE.Controllers.Search.textReplaceSuccess": "搜索已完成。已替换｛0｝处", "DE.Controllers.Search.warnReplaceString": "｛0｝不是“替换为”输入框要求的有效特殊字符。", "DE.Controllers.Statusbar.textDisconnect": "<b>连接失败</b><br>正在尝试连接。请检查连接设置。", "DE.Controllers.Statusbar.textHasChanges": "已经跟踪了新的变化", "DE.Controllers.Statusbar.textSetTrackChanges": "您处于“跟踪更改”模式", "DE.Controllers.Statusbar.textTrackChanges": "打开文档，并启用“跟踪更改”模式", "DE.Controllers.Statusbar.tipReview": "跟踪更改", "DE.Controllers.Statusbar.zoomText": "縮放{0}%", "DE.Controllers.Toolbar.confirmAddFontName": "您想要保存的字体在当前设备上不可用。<br>文本的样式将使用系统字体中的一种进行显示，保存的字体将在可用时被调用。<br>您想要继续吗？", "DE.Controllers.Toolbar.dataUrl": "粘贴数据URL", "DE.Controllers.Toolbar.errorAccessDeny": "您正在尝试执行您没有权限的操作。<br>请联系您的文档服务器管理员。", "DE.Controllers.Toolbar.fileUrl": "粘贴文件URL", "DE.Controllers.Toolbar.helpMergeShapes": "只需数秒即可合并、分割、相交、减去形状，轻松创建自定义视觉效果。", "DE.Controllers.Toolbar.helpMergeShapesHeader": "合并形状", "DE.Controllers.Toolbar.helpRtlDir": "在文档中切换从右到左（RTL）与从左到右（LTR）的文本方向。", "DE.Controllers.Toolbar.helpRtlDirHeader": "切换文本方向", "DE.Controllers.Toolbar.notcriticalErrorTitle": "警告", "DE.Controllers.Toolbar.textAccent": "重点", "DE.Controllers.Toolbar.textBracket": "括号", "DE.Controllers.Toolbar.textConvertFormDownload": "将文件下载为可填写的PDF表单以便填写。", "DE.Controllers.Toolbar.textConvertFormSave": "将文件另存为可填写的PDF表单以便填写。", "DE.Controllers.Toolbar.textDownloadPdf": "下载 PDF", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "你必须指定URL", "DE.Controllers.Toolbar.textFieldExample": "代码编写示例：TIME \\@ \"dddd, MMMM d, yyyy\"", "DE.Controllers.Toolbar.textFieldLabel": "域代码", "DE.Controllers.Toolbar.textFieldTitle": "字段", "DE.Controllers.Toolbar.textFontSizeErr": "输入的值不正确<br>请输入一个介于1和300之间的数值", "DE.Controllers.Toolbar.textFraction": "分数", "DE.Controllers.Toolbar.textFunction": "函数", "DE.Controllers.Toolbar.textGroup": "组", "DE.Controllers.Toolbar.textInsert": "插入", "DE.Controllers.Toolbar.textIntegral": "积分", "DE.Controllers.Toolbar.textLargeOperator": "大型运算符", "DE.Controllers.Toolbar.textLimitAndLog": "极限和对数", "DE.Controllers.Toolbar.textMatrix": "矩阵", "DE.Controllers.Toolbar.textOperator": "运算符", "DE.Controllers.Toolbar.textRadical": "根号", "DE.Controllers.Toolbar.textRecentlyUsed": "最近使用的", "DE.Controllers.Toolbar.textSavePdf": "另存为PDF", "DE.Controllers.Toolbar.textScript": "脚本", "DE.Controllers.Toolbar.textSymbols": "符号", "DE.Controllers.Toolbar.textTabForms": "表单", "DE.Controllers.Toolbar.textWarning": "警告", "DE.Controllers.Toolbar.txtAccent_Accent": "急性", "DE.Controllers.Toolbar.txtAccent_ArrowD": "上方的左右箭头", "DE.Controllers.Toolbar.txtAccent_ArrowL": "上方左箭头", "DE.Controllers.Toolbar.txtAccent_ArrowR": "上方向右箭头", "DE.Controllers.Toolbar.txtAccent_Bar": "条", "DE.Controllers.Toolbar.txtAccent_BarBot": "下划线", "DE.Controllers.Toolbar.txtAccent_BarTop": "上划线", "DE.Controllers.Toolbar.txtAccent_BorderBox": "带方框的公式（包含占位符）", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "带框公式（示例）", "DE.Controllers.Toolbar.txtAccent_Check": "检查", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "底括号", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "大括号", "DE.Controllers.Toolbar.txtAccent_Custom_1": "向量A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "带有上划线的ABC", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y帶有上橫線", "DE.Controllers.Toolbar.txtAccent_DDDot": "三个点", "DE.Controllers.Toolbar.txtAccent_DDot": "双点", "DE.Controllers.Toolbar.txtAccent_Dot": "点", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "双重横杠", "DE.Controllers.Toolbar.txtAccent_Grave": "严重", "DE.Controllers.Toolbar.txtAccent_GroupBot": "下面的分组字符", "DE.Controllers.Toolbar.txtAccent_GroupTop": "上面的分组字符", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "上方的向左鱼叉", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "上方的向右鱼叉", "DE.Controllers.Toolbar.txtAccent_Hat": "帽子", "DE.Controllers.Toolbar.txtAccent_Smile": "短音符", "DE.Controllers.Toolbar.txtAccent_Tilde": "波浪号", "DE.Controllers.Toolbar.txtBracket_Angle": "尖括号", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "带分隔符的尖括号", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "带两个分隔符的尖括号", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "直角括号", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "左尖括号", "DE.Controllers.Toolbar.txtBracket_Curve": "花括号", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "带分隔符的花括号", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "右大括号", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "左大括号", "DE.Controllers.Toolbar.txtBracket_Custom_1": "案例（两种情况）", "DE.Controllers.Toolbar.txtBracket_Custom_2": "案例（三种情况）", "DE.Controllers.Toolbar.txtBracket_Custom_3": "堆栈对象", "DE.Controllers.Toolbar.txtBracket_Custom_4": "括号中的堆栈对象", "DE.Controllers.Toolbar.txtBracket_Custom_5": "案例示例", "DE.Controllers.Toolbar.txtBracket_Custom_6": "二项式系数", "DE.Controllers.Toolbar.txtBracket_Custom_7": "尖括号中的二项式系数", "DE.Controllers.Toolbar.txtBracket_Line": "豎線", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "右竖线", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "左侧竖条", "DE.Controllers.Toolbar.txtBracket_LineDouble": "双竖条", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "右侧双竖条", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "左双竖条", "DE.Controllers.Toolbar.txtBracket_LowLim": "地板", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "右地板", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "左地板", "DE.Controllers.Toolbar.txtBracket_Round": "圆括号", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "带分隔符的括号", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "右括号", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "左括号", "DE.Controllers.Toolbar.txtBracket_Square": "方括号", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "两个右方括号之间的占位符", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "倒置方括号", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "右侧方括号", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "左方括号", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "两个左方括号之间的占位符", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "双方括号", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "右侧双方括号", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "左双方括号", "DE.Controllers.Toolbar.txtBracket_UppLim": "天花板", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "右天花板", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "左天花板", "DE.Controllers.Toolbar.txtDownload": "下载", "DE.Controllers.Toolbar.txtFractionDiagonal": "倾斜分数", "DE.Controllers.Toolbar.txtFractionDifferential_1": "dx 除以 dy", "DE.Controllers.Toolbar.txtFractionDifferential_2": "Δy 除以 Δx", "DE.Controllers.Toolbar.txtFractionDifferential_3": "偏微分 y 对偏微分 x", "DE.Controllers.Toolbar.txtFractionDifferential_4": "Δx 除以 Δy", "DE.Controllers.Toolbar.txtFractionHorizontal": "线性分数", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi/2", "DE.Controllers.Toolbar.txtFractionSmall": "小分数", "DE.Controllers.Toolbar.txtFractionVertical": "堆积分数", "DE.Controllers.Toolbar.txtFunction_1_Cos": "反余弦函数", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "双曲反余弦函数", "DE.Controllers.Toolbar.txtFunction_1_Cot": "反正切函數", "DE.Controllers.Toolbar.txtFunction_1_Coth": "双曲反余切函数", "DE.Controllers.Toolbar.txtFunction_1_Csc": "反余割函数", "DE.Controllers.Toolbar.txtFunction_1_Csch": "双曲反余割函数", "DE.Controllers.Toolbar.txtFunction_1_Sec": "反正割函数", "DE.Controllers.Toolbar.txtFunction_1_Sech": "双曲反割线函数", "DE.Controllers.Toolbar.txtFunction_1_Sin": "反正弦函数", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "双曲反正弦函数", "DE.Controllers.Toolbar.txtFunction_1_Tan": "反正切函数", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "双曲反正切函数", "DE.Controllers.Toolbar.txtFunction_Cos": "余弦函数", "DE.Controllers.Toolbar.txtFunction_Cosh": "双曲余弦函数", "DE.Controllers.Toolbar.txtFunction_Cot": "余切函數", "DE.Controllers.Toolbar.txtFunction_Coth": "双曲正交函数", "DE.Controllers.Toolbar.txtFunction_Csc": "余割函数", "DE.Controllers.Toolbar.txtFunction_Csch": "双曲余割函数", "DE.Controllers.Toolbar.txtFunction_Custom_1": "正弦波", "DE.Controllers.Toolbar.txtFunction_Custom_2": "cos2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "切线公式", "DE.Controllers.Toolbar.txtFunction_Sec": "正割函数", "DE.Controllers.Toolbar.txtFunction_Sech": "双曲正割函数", "DE.Controllers.Toolbar.txtFunction_Sin": "正弦函数", "DE.Controllers.Toolbar.txtFunction_Sinh": "双曲正弦函数", "DE.Controllers.Toolbar.txtFunction_Tan": "正切函数", "DE.Controllers.Toolbar.txtFunction_Tanh": "双曲正切函数", "DE.Controllers.Toolbar.txtIntegral": "积分", "DE.Controllers.Toolbar.txtIntegral_dtheta": "微分 θ", "DE.Controllers.Toolbar.txtIntegral_dx": "差分x", "DE.Controllers.Toolbar.txtIntegral_dy": "差分y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "与堆叠极限的积分", "DE.Controllers.Toolbar.txtIntegralDouble": "重积分", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "具有堆叠极限的二重积分", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "带极限的二重积分", "DE.Controllers.Toolbar.txtIntegralOriented": "轮廓积分", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "具有堆叠极限的等高线积分", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "曲面积分", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "带堆叠限制的曲面积分", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "带限制的曲面积分", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "带极限的等高线积分", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "体积积分", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "带堆叠限制的体积积分", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "带限制的体积积分", "DE.Controllers.Toolbar.txtIntegralSubSup": "带极限的积分", "DE.Controllers.Toolbar.txtIntegralTriple": "三重积分", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "带堆叠限制的三重积分", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "带限制的三重积分", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "逻辑与", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "带下限的逻辑与", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "带限制的逻辑与", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "带下标下限的逻辑与", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "带上下标限制的逻辑与", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "联产品", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "具有下限的共同产品", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "有限制的共同产品", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "具有下标下限的共同产品", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "具有下标/上标限制的共同产品", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "k等于从0到n的提取n个的总和", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "从i等于0到n的求和", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "总和示例使用两个索引", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "乘积示例", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "并集示例", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "逻辑或", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "带下限的逻辑或", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "带限制的逻辑或", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "带下标下限的逻辑或", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "带下标/上标限制的逻辑或", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "交集", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "带下限的交集", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "带限制的交集", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "带下标下限的交集", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "带下标/上标限制的交集", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "乘积", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "带下限的乘积", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "带限制的乘积", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "带下标下限的乘积", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "带下标/上标极限的乘积", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "合计", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "带下限的总和", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "带限制的总和", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "带下标下限的求和", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "带上下标限制的求和", "DE.Controllers.Toolbar.txtLargeOperator_Union": "并集", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "带下限的并集", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "带限制的并集", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "带下标下限的并集", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "带上下标限制的并集", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "限制范例", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大范例", "DE.Controllers.Toolbar.txtLimitLog_Lim": "限制", "DE.Controllers.Toolbar.txtLimitLog_Ln": "自然对数", "DE.Controllers.Toolbar.txtLimitLog_Log": "对数", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "对数", "DE.Controllers.Toolbar.txtLimitLog_Max": "最大值", "DE.Controllers.Toolbar.txtLimitLog_Min": "最低限度", "DE.Controllers.Toolbar.txtMarginsH": "顶部和底部边距对于给定的页面高度来说太高", "DE.Controllers.Toolbar.txtMarginsW": "对于给定的页面宽度，左右边距太宽", "DE.Controllers.Toolbar.txtMatrix_1_2": "1x2空矩阵", "DE.Controllers.Toolbar.txtMatrix_1_3": "1x3空矩阵", "DE.Controllers.Toolbar.txtMatrix_2_1": "2x1空矩阵", "DE.Controllers.Toolbar.txtMatrix_2_2": "2x2空矩阵", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "以双竖线表示的空的2x2矩阵", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "空的2x2行列式", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "带圆括号的2x2空矩阵", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "带方形括号的2x2空矩阵", "DE.Controllers.Toolbar.txtMatrix_2_3": "2x3空矩阵", "DE.Controllers.Toolbar.txtMatrix_3_1": "3x1空矩阵", "DE.Controllers.Toolbar.txtMatrix_3_2": "3x2空矩阵", "DE.Controllers.Toolbar.txtMatrix_3_3": "3x3空矩阵", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "基线点", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "中线点", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "对角点", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "垂直點", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "括号中的稀疏矩阵", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "括号中的稀疏矩阵", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2带零的单位矩阵", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2除了对角线以外都是空白的单位矩阵", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "含有零的3x3单位矩阵", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3除了对角线以外都是空白的单位矩阵", "DE.Controllers.Toolbar.txtNeedDownload": "PDF 阅读器只能将新的更改保存在单独的文件副本中。PDF 阅读器不支持共同编辑功能，如需与其他用户分享所做的更改，请共享新的文件副本。", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "下方的左右箭头", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "上方的左右箭头", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "下方向左箭头", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "上方左箭头", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "下方向右箭头", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "上方向右箭头", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "冒号相等", "DE.Controllers.Toolbar.txtOperator_Custom_1": "產生", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Delta 收益", "DE.Controllers.Toolbar.txtOperator_Definition": "等同于定义", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta 等于", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "下方的左右双箭头", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "上方的左右双箭头", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "下方向左箭头", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "上方左箭头", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "下方向右箭头", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "上方向右箭头", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "等于", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "负等于", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "加等于", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "测量者", "DE.Controllers.Toolbar.txtRadicalCustom_1": "二次方程式的右侧", "DE.Controllers.Toolbar.txtRadicalCustom_2": "a的平方加b的平方的平方根", "DE.Controllers.Toolbar.txtRadicalRoot_2": "带次数的平方根", "DE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "DE.Controllers.Toolbar.txtRadicalRoot_n": "开n次根号", "DE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "DE.Controllers.Toolbar.txtSaveCopy": "保存副本", "DE.Controllers.Toolbar.txtScriptCustom_1": "x下标y的平方", "DE.Controllers.Toolbar.txtScriptCustom_2": "e 的负 i omega t 次方", "DE.Controllers.Toolbar.txtScriptCustom_3": "x 的平方", "DE.Controllers.Toolbar.txtScriptCustom_4": "Y左上标n左下标一", "DE.Controllers.Toolbar.txtScriptSub": "下标", "DE.Controllers.Toolbar.txtScriptSubSup": "下标-上标", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "左下标上标", "DE.Controllers.Toolbar.txtScriptSup": "上标", "DE.Controllers.Toolbar.txtSymbol_about": "大约", "DE.Controllers.Toolbar.txtSymbol_additional": "补充", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_approx": "几乎等于", "DE.Controllers.Toolbar.txtSymbol_ast": "星号运算符", "DE.Controllers.Toolbar.txtSymbol_beta": "测试版", "DE.Controllers.Toolbar.txtSymbol_beth": "确信", "DE.Controllers.Toolbar.txtSymbol_bullet": "项目符号运算符", "DE.Controllers.Toolbar.txtSymbol_cap": "交集", "DE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "DE.Controllers.Toolbar.txtSymbol_cdots": "中线水平省略号", "DE.Controllers.Toolbar.txtSymbol_celsius": "摄氏度", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "约等于", "DE.Controllers.Toolbar.txtSymbol_cup": "并集", "DE.Controllers.Toolbar.txtSymbol_ddots": "向右对角线省略号", "DE.Controllers.Toolbar.txtSymbol_degree": "度", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "除号", "DE.Controllers.Toolbar.txtSymbol_downarrow": "向下箭头", "DE.Controllers.Toolbar.txtSymbol_emptyset": "空集", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "DE.Controllers.Toolbar.txtSymbol_equals": "等于", "DE.Controllers.Toolbar.txtSymbol_equiv": "相同", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "存在", "DE.Controllers.Toolbar.txtSymbol_factorial": "阶乘", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "华氏度", "DE.Controllers.Toolbar.txtSymbol_forall": "全部", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "DE.Controllers.Toolbar.txtSymbol_geq": "大于或等于", "DE.Controllers.Toolbar.txtSymbol_gg": "远大于", "DE.Controllers.Toolbar.txtSymbol_greater": "大于", "DE.Controllers.Toolbar.txtSymbol_in": "元素", "DE.Controllers.Toolbar.txtSymbol_inc": "增量", "DE.Controllers.Toolbar.txtSymbol_infinity": "无限", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "左箭头", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右箭头", "DE.Controllers.Toolbar.txtSymbol_leq": "小于或等于", "DE.Controllers.Toolbar.txtSymbol_less": "小于", "DE.Controllers.Toolbar.txtSymbol_ll": "远小于", "DE.Controllers.Toolbar.txtSymbol_minus": "减去", "DE.Controllers.Toolbar.txtSymbol_mp": "减加号", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "不等于", "DE.Controllers.Toolbar.txtSymbol_ni": "包含为成员", "DE.Controllers.Toolbar.txtSymbol_not": "不签名", "DE.Controllers.Toolbar.txtSymbol_notexists": "不存在", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omicron", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "偏微分", "DE.Controllers.Toolbar.txtSymbol_percent": "百分比", "DE.Controllers.Toolbar.txtSymbol_phi": "Phi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "加", "DE.Controllers.Toolbar.txtSymbol_pm": "加减", "DE.Controllers.Toolbar.txtSymbol_propto": "成比例于", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "四次方根", "DE.Controllers.Toolbar.txtSymbol_qed": "证明结束", "DE.Controllers.Toolbar.txtSymbol_rddots": "向右对角线省略号", "DE.Controllers.Toolbar.txtSymbol_rho": "Rho", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "右箭头", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "根号", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "因此", "DE.Controllers.Toolbar.txtSymbol_theta": "Theta", "DE.Controllers.Toolbar.txtSymbol_times": "乘法符号", "DE.Controllers.Toolbar.txtSymbol_uparrow": "向上箭头", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon变体", "DE.Controllers.Toolbar.txtSymbol_varphi": "Phi 变体", "DE.Controllers.Toolbar.txtSymbol_varpi": "π变量", "DE.Controllers.Toolbar.txtSymbol_varrho": "Rho 变量", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma变量", "DE.Controllers.Toolbar.txtSymbol_vartheta": "Theta 变量", "DE.Controllers.Toolbar.txtSymbol_vdots": "垂直省略號", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Toolbar.txtUntitled": "未命名", "DE.Controllers.Viewport.textFitPage": "调整至页面大小", "DE.Controllers.Viewport.textFitWidth": "调整至宽度大小", "DE.Controllers.Viewport.txtDarkMode": "深色模式", "DE.Views.BookmarksDialog.textAdd": "添加", "DE.Views.BookmarksDialog.textBookmarkName": "书签名称", "DE.Views.BookmarksDialog.textClose": "关闭", "DE.Views.BookmarksDialog.textCopy": "复制", "DE.Views.BookmarksDialog.textDelete": "刪除", "DE.Views.BookmarksDialog.textGetLink": "获取链接", "DE.Views.BookmarksDialog.textGoto": "前往", "DE.Views.BookmarksDialog.textHidden": "隐藏的书签", "DE.Views.BookmarksDialog.textLocation": "位置", "DE.Views.BookmarksDialog.textName": "名称", "DE.Views.BookmarksDialog.textSort": "排序方式", "DE.Views.BookmarksDialog.textTitle": "书签", "DE.Views.BookmarksDialog.txtInvalidName": "书签名称只能包含字母、数字和下划线，并且应以字母开头", "DE.Views.CaptionDialog.textAdd": "添加标签", "DE.Views.CaptionDialog.textAfter": "之后", "DE.Views.CaptionDialog.textBefore": "以前", "DE.Views.CaptionDialog.textCaption": "标题", "DE.Views.CaptionDialog.textChapter": "本章始于样式", "DE.Views.CaptionDialog.textChapterInc": "包括章节编号", "DE.Views.CaptionDialog.textColon": "冒号", "DE.Views.CaptionDialog.textDash": "破折号", "DE.Views.CaptionDialog.textDelete": "删除标签", "DE.Views.CaptionDialog.textEquation": "方程式", "DE.Views.CaptionDialog.textExamples": "示例：表2-A，图像1.IV", "DE.Views.CaptionDialog.textExclude": "从标题中排除标签", "DE.Views.CaptionDialog.textFigure": "图", "DE.Views.CaptionDialog.textHyphen": "连字符", "DE.Views.CaptionDialog.textInsert": "插入", "DE.Views.CaptionDialog.textLabel": "标签", "DE.Views.CaptionDialog.textLabelError": "标签不能为空。", "DE.Views.CaptionDialog.textLongDash": "长划线", "DE.Views.CaptionDialog.textNumbering": "编号", "DE.Views.CaptionDialog.textPeriod": "阶段", "DE.Views.CaptionDialog.textSeparator": "使用分隔符", "DE.Views.CaptionDialog.textTable": "表格", "DE.Views.CaptionDialog.textTitle": "插入标题", "DE.Views.CellsAddDialog.textCol": "列", "DE.Views.CellsAddDialog.textDown": "在光标下方", "DE.Views.CellsAddDialog.textLeft": "靠左", "DE.Views.CellsAddDialog.textRight": "靠右", "DE.Views.CellsAddDialog.textRow": "行", "DE.Views.CellsAddDialog.textTitle": "插入几个", "DE.Views.CellsAddDialog.textUp": "光标上方", "DE.Views.ChartSettings.text3dDepth": "深度（基准的%）", "DE.Views.ChartSettings.text3dHeight": "高度（基准的%）", "DE.Views.ChartSettings.text3dRotation": "三维旋转", "DE.Views.ChartSettings.textAdvanced": "显示高级设置", "DE.Views.ChartSettings.textAutoscale": "自动缩放", "DE.Views.ChartSettings.textChartType": "更改图表类型", "DE.Views.ChartSettings.textDefault": "默认旋转", "DE.Views.ChartSettings.textDown": "下", "DE.Views.ChartSettings.textEditData": "编辑数据", "DE.Views.ChartSettings.textHeight": "高度", "DE.Views.ChartSettings.textLeft": "左", "DE.Views.ChartSettings.textNarrow": "窄视野", "DE.Views.ChartSettings.textOriginalSize": "实际大小", "DE.Views.ChartSettings.textPerspective": "透视", "DE.Views.ChartSettings.textRight": "右", "DE.Views.ChartSettings.textRightAngle": "直角坐标轴", "DE.Views.ChartSettings.textSize": "大小", "DE.Views.ChartSettings.textStyle": "样式", "DE.Views.ChartSettings.textUndock": "离开面板", "DE.Views.ChartSettings.textUp": "向上", "DE.Views.ChartSettings.textWiden": "擴大視野", "DE.Views.ChartSettings.textWidth": "宽度", "DE.Views.ChartSettings.textWrap": "环绕方式", "DE.Views.ChartSettings.textX": "X轴旋转", "DE.Views.ChartSettings.textY": "Y轴旋转", "DE.Views.ChartSettings.txtBehind": "在文本后面", "DE.Views.ChartSettings.txtInFront": "在文字前面", "DE.Views.ChartSettings.txtInline": "与文本对齐", "DE.Views.ChartSettings.txtSquare": "方形", "DE.Views.ChartSettings.txtThrough": "通过", "DE.Views.ChartSettings.txtTight": "紧", "DE.Views.ChartSettings.txtTitle": "图表", "DE.Views.ChartSettings.txtTopAndBottom": "顶部和底部", "DE.Views.ControlSettingsDialog.strGeneral": "一般", "DE.Views.ControlSettingsDialog.textAdd": "添加", "DE.Views.ControlSettingsDialog.textAppearance": "外观", "DE.Views.ControlSettingsDialog.textApplyAll": "全部应用", "DE.Views.ControlSettingsDialog.textBox": "边界框", "DE.Views.ControlSettingsDialog.textChange": "编辑", "DE.Views.ControlSettingsDialog.textCheckbox": "复选框", "DE.Views.ControlSettingsDialog.textChecked": "选中的符号", "DE.Views.ControlSettingsDialog.textColor": "颜色", "DE.Views.ControlSettingsDialog.textCombobox": "下拉式方框", "DE.Views.ControlSettingsDialog.textDate": "日期格式", "DE.Views.ControlSettingsDialog.textDelete": "刪除", "DE.Views.ControlSettingsDialog.textDisplayName": "显示名称", "DE.Views.ControlSettingsDialog.textDown": "下", "DE.Views.ControlSettingsDialog.textDropDown": "下拉列表", "DE.Views.ControlSettingsDialog.textFormat": "这样显示日期", "DE.Views.ControlSettingsDialog.textLang": "语言", "DE.Views.ControlSettingsDialog.textLock": "锁定中", "DE.Views.ControlSettingsDialog.textName": "标题", "DE.Views.ControlSettingsDialog.textNone": "无", "DE.Views.ControlSettingsDialog.textPlaceholder": "占位符", "DE.Views.ControlSettingsDialog.textShowAs": "显示为……", "DE.Views.ControlSettingsDialog.textSystemColor": "系统", "DE.Views.ControlSettingsDialog.textTag": "标签", "DE.Views.ControlSettingsDialog.textTitle": "内容控制设置", "DE.Views.ControlSettingsDialog.textUnchecked": "未检查符号", "DE.Views.ControlSettingsDialog.textUp": "向上", "DE.Views.ControlSettingsDialog.textValue": "值", "DE.Views.ControlSettingsDialog.tipChange": "更改符号", "DE.Views.ControlSettingsDialog.txtLockDelete": "无法删除内容控件", "DE.Views.ControlSettingsDialog.txtLockEdit": "无法编辑内容", "DE.Views.ControlSettingsDialog.txtRemContent": "编辑内容时删除内容控件", "DE.Views.CrossReferenceDialog.textAboveBelow": "上方/下方", "DE.Views.CrossReferenceDialog.textBookmark": "书签", "DE.Views.CrossReferenceDialog.textBookmarkText": "书签文字", "DE.Views.CrossReferenceDialog.textCaption": "整个标题", "DE.Views.CrossReferenceDialog.textEmpty": "请求引用为空。", "DE.Views.CrossReferenceDialog.textEndnote": "尾注", "DE.Views.CrossReferenceDialog.textEndNoteNum": "尾注编号", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "尾注编号（格式化）", "DE.Views.CrossReferenceDialog.textEquation": "方程式", "DE.Views.CrossReferenceDialog.textFigure": "图", "DE.Views.CrossReferenceDialog.textFootnote": "脚注", "DE.Views.CrossReferenceDialog.textHeading": "标题", "DE.Views.CrossReferenceDialog.textHeadingNum": "标题编号", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "标题编号（全文）", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "标题编号（无上下文）", "DE.Views.CrossReferenceDialog.textHeadingText": "标题文本", "DE.Views.CrossReferenceDialog.textIncludeAbove": "包括上方/下方", "DE.Views.CrossReferenceDialog.textInsert": "插入", "DE.Views.CrossReferenceDialog.textInsertAs": "插入为超链接", "DE.Views.CrossReferenceDialog.textLabelNum": "仅标签和编号", "DE.Views.CrossReferenceDialog.textNoteNum": "脚注编号", "DE.Views.CrossReferenceDialog.textNoteNumForm": "脚注编号（格式化）", "DE.Views.CrossReferenceDialog.textOnlyCaption": "仅标题文本", "DE.Views.CrossReferenceDialog.textPageNum": "页码", "DE.Views.CrossReferenceDialog.textParagraph": "编号项目", "DE.Views.CrossReferenceDialog.textParaNum": "段落编号", "DE.Views.CrossReferenceDialog.textParaNumFull": "段落编号（全文）", "DE.Views.CrossReferenceDialog.textParaNumNo": "段落编号（无内文）", "DE.Views.CrossReferenceDialog.textSeparate": "用分隔数字", "DE.Views.CrossReferenceDialog.textTable": "表格", "DE.Views.CrossReferenceDialog.textText": "段落文字", "DE.Views.CrossReferenceDialog.textWhich": "用于哪个标题", "DE.Views.CrossReferenceDialog.textWhichBookmark": "用于哪个书签", "DE.Views.CrossReferenceDialog.textWhichEndnote": "用于哪个尾注", "DE.Views.CrossReferenceDialog.textWhichHeading": "用于哪个标题", "DE.Views.CrossReferenceDialog.textWhichNote": "用于哪个脚注", "DE.Views.CrossReferenceDialog.textWhichPara": "用于哪个编号项目", "DE.Views.CrossReferenceDialog.txtReference": "插入引用至", "DE.Views.CrossReferenceDialog.txtTitle": "交叉引用", "DE.Views.CrossReferenceDialog.txtType": "参照类型", "DE.Views.CustomColumnsDialog.textColumns": "列数", "DE.Views.CustomColumnsDialog.textEqualWidth": "平均分配列宽", "DE.Views.CustomColumnsDialog.textSeparator": "列分隔符", "DE.Views.CustomColumnsDialog.textTitle": "列", "DE.Views.CustomColumnsDialog.textTitleSpacing": "间距", "DE.Views.CustomColumnsDialog.textWidth": "宽度", "DE.Views.DateTimeDialog.confirmDefault": "设置｛0｝的默认格式：“｛1｝”", "DE.Views.DateTimeDialog.textDefault": "设置为默认值", "DE.Views.DateTimeDialog.textFormat": "格式", "DE.Views.DateTimeDialog.textLang": "语言", "DE.Views.DateTimeDialog.textUpdate": "自动更新", "DE.Views.DateTimeDialog.txtTitle": "日期和时间", "DE.Views.DocProtection.hintProtectDoc": "保护文档", "DE.Views.DocProtection.txtDocProtectedComment": "文档受到保护<br>您只能在此文档中插入批注。", "DE.Views.DocProtection.txtDocProtectedForms": "文档受到保护<br>您只能在此文档中填写表单。", "DE.Views.DocProtection.txtDocProtectedTrack": "文档受到保护<br>您可以编辑此文档，但所有更改都将被跟踪。", "DE.Views.DocProtection.txtDocProtectedView": "文档受到保护<br>您只能在此文档中插入批注。", "DE.Views.DocProtection.txtDocUnlockDescription": "输入密码以取消文档保护", "DE.Views.DocProtection.txtProtectDoc": "保护文档", "DE.Views.DocProtection.txtUnlockTitle": "解除文档保护", "DE.Views.DocumentHolder.aboveText": "上方", "DE.Views.DocumentHolder.addCommentText": "添加批注", "DE.Views.DocumentHolder.advancedDropCapText": "首字下沉设置", "DE.Views.DocumentHolder.advancedEquationText": "方程式设置", "DE.Views.DocumentHolder.advancedFrameText": "框架高级设置", "DE.Views.DocumentHolder.advancedParagraphText": "段落高级设置", "DE.Views.DocumentHolder.advancedTableText": "表格高级设置", "DE.Views.DocumentHolder.advancedText": "高级设置", "DE.Views.DocumentHolder.alignmentText": "对齐", "DE.Views.DocumentHolder.allLinearText": "全部-线性", "DE.Views.DocumentHolder.allProfText": "全部-专业", "DE.Views.DocumentHolder.belowText": "下面", "DE.Views.DocumentHolder.breakBeforeText": "段前分页", "DE.Views.DocumentHolder.bulletsText": "项目符号和编号", "DE.Views.DocumentHolder.cellAlignText": "单元格垂直对齐", "DE.Views.DocumentHolder.cellText": "单元格", "DE.Views.DocumentHolder.centerText": "中心", "DE.Views.DocumentHolder.chartText": "图表高级设置", "DE.Views.DocumentHolder.columnText": "列", "DE.Views.DocumentHolder.currLinearText": "当前-线性", "DE.Views.DocumentHolder.currProfText": "当前-专业", "DE.Views.DocumentHolder.deleteColumnText": "删除列", "DE.Views.DocumentHolder.deleteRowText": "删除行", "DE.Views.DocumentHolder.deleteTableText": "删除表格", "DE.Views.DocumentHolder.deleteText": "刪除", "DE.Views.DocumentHolder.direct270Text": "向上旋转文字", "DE.Views.DocumentHolder.direct90Text": "向下旋转文字", "DE.Views.DocumentHolder.directHText": "水平的", "DE.Views.DocumentHolder.directionText": "文字方向", "DE.Views.DocumentHolder.editChartText": "编辑数据", "DE.Views.DocumentHolder.editFooterText": "编辑页脚", "DE.Views.DocumentHolder.editHeaderText": "编辑页眉", "DE.Views.DocumentHolder.editHyperlinkText": "编辑超链接", "DE.Views.DocumentHolder.eqToDisplayText": "更改为显示", "DE.Views.DocumentHolder.eqToInlineText": "更改为内联", "DE.Views.DocumentHolder.guestText": "访客", "DE.Views.DocumentHolder.hideEqToolbar": "隐藏公式工具栏", "DE.Views.DocumentHolder.hyperlinkText": "超链接", "DE.Views.DocumentHolder.ignoreAllSpellText": "忽略所有", "DE.Views.DocumentHolder.ignoreSpellText": "忽略", "DE.Views.DocumentHolder.imageText": "图片高级设置", "DE.Views.DocumentHolder.insertColumnLeftText": "左栏", "DE.Views.DocumentHolder.insertColumnRightText": "右栏", "DE.Views.DocumentHolder.insertColumnText": "插入列", "DE.Views.DocumentHolder.insertRowAboveText": "上面的行", "DE.Views.DocumentHolder.insertRowBelowText": "下面的行", "DE.Views.DocumentHolder.insertRowText": "插入行", "DE.Views.DocumentHolder.insertText": "插入", "DE.Views.DocumentHolder.keepLinesText": "段中不分页", "DE.Views.DocumentHolder.langText": "选择语言", "DE.Views.DocumentHolder.latexText": "LaTeX", "DE.Views.DocumentHolder.leftText": "左", "DE.Views.DocumentHolder.loadSpellText": "加载变体...", "DE.Views.DocumentHolder.mergeCellsText": "合并单元格", "DE.Views.DocumentHolder.mniImageFromFile": "图片文件", "DE.Views.DocumentHolder.mniImageFromStorage": "存储设备中的图片", "DE.Views.DocumentHolder.mniImageFromUrl": "来自URL地址的图片", "DE.Views.DocumentHolder.moreText": "更多变体...", "DE.Views.DocumentHolder.noSpellVariantsText": "没有变体", "DE.Views.DocumentHolder.notcriticalErrorTitle": "警告", "DE.Views.DocumentHolder.originalSizeText": "实际大小", "DE.Views.DocumentHolder.paragraphText": "段落", "DE.Views.DocumentHolder.removeHyperlinkText": "删除超链接", "DE.Views.DocumentHolder.rightText": "右", "DE.Views.DocumentHolder.rowText": "行", "DE.Views.DocumentHolder.saveStyleText": "新建样式", "DE.Views.DocumentHolder.selectCellText": "选择单元格", "DE.Views.DocumentHolder.selectColumnText": "选择列", "DE.Views.DocumentHolder.selectRowText": "选择行", "DE.Views.DocumentHolder.selectTableText": "选择表格", "DE.Views.DocumentHolder.selectText": "选择", "DE.Views.DocumentHolder.shapeText": "形状高级设置", "DE.Views.DocumentHolder.showEqToolbar": "显示公式工具栏", "DE.Views.DocumentHolder.spellcheckText": "拼写检查", "DE.Views.DocumentHolder.splitCellsText": "拆分单元格", "DE.Views.DocumentHolder.splitCellTitleText": "拆分单元格", "DE.Views.DocumentHolder.strDelete": "删除签名", "DE.Views.DocumentHolder.strDetails": "签名详细信息", "DE.Views.DocumentHolder.strSetup": "签名设置", "DE.Views.DocumentHolder.strSign": "签署", "DE.Views.DocumentHolder.styleText": "格式化为样式", "DE.Views.DocumentHolder.tableText": "表格", "DE.Views.DocumentHolder.textAccept": "同意更改", "DE.Views.DocumentHolder.textAlign": "对齐", "DE.Views.DocumentHolder.textArrange": "安排", "DE.Views.DocumentHolder.textArrangeBack": "置于底层", "DE.Views.DocumentHolder.textArrangeBackward": "下移一层", "DE.Views.DocumentHolder.textArrangeForward": "向前移动", "DE.Views.DocumentHolder.textArrangeFront": "移到前景", "DE.Views.DocumentHolder.textCells": "单元格", "DE.Views.DocumentHolder.textClearField": "清除字段", "DE.Views.DocumentHolder.textCol": "刪除整列", "DE.Views.DocumentHolder.textContentControls": "內容控制", "DE.Views.DocumentHolder.textContinueNumbering": "继续编号", "DE.Views.DocumentHolder.textCopy": "复制", "DE.Views.DocumentHolder.textCrop": "裁剪", "DE.Views.DocumentHolder.textCropFill": "填入", "DE.Views.DocumentHolder.textCropFit": "适应", "DE.Views.DocumentHolder.textCut": "剪切", "DE.Views.DocumentHolder.textDistributeCols": "分布列", "DE.Views.DocumentHolder.textDistributeRows": "分布行", "DE.Views.DocumentHolder.textEditControls": "内容控制设置", "DE.Views.DocumentHolder.textEditField": "编辑字段", "DE.Views.DocumentHolder.textEditObject": "编辑对象", "DE.Views.DocumentHolder.textEditPoints": "编辑点", "DE.Views.DocumentHolder.textEditWrapBoundary": "编辑环绕边界", "DE.Views.DocumentHolder.textFieldCodes": "切换字段代码", "DE.Views.DocumentHolder.textFlipH": "水平翻转", "DE.Views.DocumentHolder.textFlipV": "垂直翻转", "DE.Views.DocumentHolder.textFollow": "跟随移动", "DE.Views.DocumentHolder.textFromFile": "从文件", "DE.Views.DocumentHolder.textFromStorage": "来自存储设备", "DE.Views.DocumentHolder.textFromUrl": "来自URL", "DE.Views.DocumentHolder.textIndents": "调整列表缩进", "DE.Views.DocumentHolder.textJoinList": "加入到上一个列表中", "DE.Views.DocumentHolder.textLeft": "向左移动单元格", "DE.Views.DocumentHolder.textNest": "嵌套表", "DE.Views.DocumentHolder.textNextPage": "下一页", "DE.Views.DocumentHolder.textNumberingValue": "编号值", "DE.Views.DocumentHolder.textPaste": "粘贴", "DE.Views.DocumentHolder.textPrevPage": "上一页", "DE.Views.DocumentHolder.textRedo": "重做", "DE.Views.DocumentHolder.textRefreshField": "更新字段", "DE.Views.DocumentHolder.textReject": "否决更改", "DE.Views.DocumentHolder.textRemCheckBox": "删除复选框", "DE.Views.DocumentHolder.textRemComboBox": "删除下拉式方框", "DE.Views.DocumentHolder.textRemDropdown": "删除下拉菜单", "DE.Views.DocumentHolder.textRemField": "删除文本字段", "DE.Views.DocumentHolder.textRemove": "删除", "DE.Views.DocumentHolder.textRemoveControl": "刪除內容控制", "DE.Views.DocumentHolder.textRemPicture": "删除图片", "DE.Views.DocumentHolder.textRemRadioBox": "删除单选按钮", "DE.Views.DocumentHolder.textReplace": "替换图像", "DE.Views.DocumentHolder.textResetCrop": "重置裁剪", "DE.Views.DocumentHolder.textRotate": "旋转", "DE.Views.DocumentHolder.textRotate270": "逆时针旋转90°", "DE.Views.DocumentHolder.textRotate90": "顺时针旋转90°", "DE.Views.DocumentHolder.textRow": "刪除整行", "DE.Views.DocumentHolder.textSaveAsPicture": "另存为图片", "DE.Views.DocumentHolder.textSeparateList": "单独列表", "DE.Views.DocumentHolder.textSettings": "设置", "DE.Views.DocumentHolder.textSeveral": "多行/多列", "DE.Views.DocumentHolder.textShapeAlignBottom": "底部对齐", "DE.Views.DocumentHolder.textShapeAlignCenter": "居中对齐", "DE.Views.DocumentHolder.textShapeAlignLeft": "左对齐", "DE.Views.DocumentHolder.textShapeAlignMiddle": "居中对齐", "DE.Views.DocumentHolder.textShapeAlignRight": "右对齐", "DE.Views.DocumentHolder.textShapeAlignTop": "顶端对齐", "DE.Views.DocumentHolder.textShapesMerge": "合并形状", "DE.Views.DocumentHolder.textStartNewList": "开始新列表", "DE.Views.DocumentHolder.textStartNumberingFrom": "设置编号值", "DE.Views.DocumentHolder.textTitleCellsRemove": "删除单元格", "DE.Views.DocumentHolder.textTOC": "目录", "DE.Views.DocumentHolder.textTOCSettings": "目录设置", "DE.Views.DocumentHolder.textUndo": "撤消", "DE.Views.DocumentHolder.textUpdateAll": "更新整个表格", "DE.Views.DocumentHolder.textUpdatePages": "仅更新页码", "DE.Views.DocumentHolder.textUpdateTOC": "更新目录", "DE.Views.DocumentHolder.textWrap": "环绕方式", "DE.Views.DocumentHolder.tipIsLocked": "此元素正在由其他用户编辑。", "DE.Views.DocumentHolder.toDictionaryText": "添加到字典", "DE.Views.DocumentHolder.txtAddBottom": "添加底部边框", "DE.Views.DocumentHolder.txtAddFractionBar": "添加分数栏", "DE.Views.DocumentHolder.txtAddHor": "添加水平线", "DE.Views.DocumentHolder.txtAddLB": "添加左底边框", "DE.Views.DocumentHolder.txtAddLeft": "添加左边框", "DE.Views.DocumentHolder.txtAddLT": "添加左侧顶部边框", "DE.Views.DocumentHolder.txtAddRight": "添加右边框", "DE.Views.DocumentHolder.txtAddTop": "添加上边框", "DE.Views.DocumentHolder.txtAddVer": "添加垂直线", "DE.Views.DocumentHolder.txtAlignToChar": "字符对齐", "DE.Views.DocumentHolder.txtBehind": "在文本后面", "DE.Views.DocumentHolder.txtBorderProps": "边框属性", "DE.Views.DocumentHolder.txtBottom": "底部", "DE.Views.DocumentHolder.txtColumnAlign": "列对齐", "DE.Views.DocumentHolder.txtDecreaseArg": "减少参数大小", "DE.Views.DocumentHolder.txtDeleteArg": "删除参数", "DE.Views.DocumentHolder.txtDeleteBreak": "删除手动的换行符", "DE.Views.DocumentHolder.txtDeleteChars": "删除封闭字符", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "删除封闭字符和分隔符", "DE.Views.DocumentHolder.txtDeleteEq": "刪除方程式", "DE.Views.DocumentHolder.txtDeleteGroupChar": "删除字符", "DE.Views.DocumentHolder.txtDeleteRadical": "刪除根号", "DE.Views.DocumentHolder.txtDistribHor": "水平分布", "DE.Views.DocumentHolder.txtDistribVert": "垂直分布", "DE.Views.DocumentHolder.txtEmpty": "（空）", "DE.Views.DocumentHolder.txtFractionLinear": "改为线性分数", "DE.Views.DocumentHolder.txtFractionSkewed": "改为倾斜分数", "DE.Views.DocumentHolder.txtFractionStacked": "改为堆积分数", "DE.Views.DocumentHolder.txtGroup": "组", "DE.Views.DocumentHolder.txtGroupCharOver": "文字上方的字符", "DE.Views.DocumentHolder.txtGroupCharUnder": "文字下的字符", "DE.Views.DocumentHolder.txtHideBottom": "隐藏底部边框", "DE.Views.DocumentHolder.txtHideBottomLimit": "隐藏下限", "DE.Views.DocumentHolder.txtHideCloseBracket": "隐藏右括号", "DE.Views.DocumentHolder.txtHideDegree": "隐藏度数", "DE.Views.DocumentHolder.txtHideHor": "隐藏水平线", "DE.Views.DocumentHolder.txtHideLB": "隐藏左底线", "DE.Views.DocumentHolder.txtHideLeft": "隐藏左边框", "DE.Views.DocumentHolder.txtHideLT": "隐藏左顶线", "DE.Views.DocumentHolder.txtHideOpenBracket": "隐藏左括号", "DE.Views.DocumentHolder.txtHidePlaceholder": "隐藏占位符", "DE.Views.DocumentHolder.txtHideRight": "隐藏右边框", "DE.Views.DocumentHolder.txtHideTop": "隐藏顶部边框", "DE.Views.DocumentHolder.txtHideTopLimit": "隐藏上限", "DE.Views.DocumentHolder.txtHideVer": "隐藏垂直线", "DE.Views.DocumentHolder.txtIncreaseArg": "增加参数大小", "DE.Views.DocumentHolder.txtInFront": "在文字前面", "DE.Views.DocumentHolder.txtInline": "与文本对齐", "DE.Views.DocumentHolder.txtInsertArgAfter": "在后面插入参数", "DE.Views.DocumentHolder.txtInsertArgBefore": "之前插入参数", "DE.Views.DocumentHolder.txtInsertBreak": "插入手动分隔符", "DE.Views.DocumentHolder.txtInsertCaption": "插入标题", "DE.Views.DocumentHolder.txtInsertEqAfter": "在之后插入方程式", "DE.Views.DocumentHolder.txtInsertEqBefore": "在之前插入方程式", "DE.Views.DocumentHolder.txtInsImage": "插入来自文件的图片", "DE.Views.DocumentHolder.txtInsImageUrl": "插入来自URL的图片", "DE.Views.DocumentHolder.txtKeepTextOnly": "仅保留文字", "DE.Views.DocumentHolder.txtLimitChange": "更改界限位置", "DE.Views.DocumentHolder.txtLimitOver": "文字限制", "DE.Views.DocumentHolder.txtLimitUnder": "文字下的限制", "DE.Views.DocumentHolder.txtMatchBrackets": "括号与其内容的高度对齐", "DE.Views.DocumentHolder.txtMatrixAlign": "矩阵对齐", "DE.Views.DocumentHolder.txtOverbar": "文本上横条", "DE.Views.DocumentHolder.txtOverwriteCells": "覆盖单元格", "DE.Views.DocumentHolder.txtPasteSourceFormat": "保留源格式", "DE.Views.DocumentHolder.txtPressLink": "按 {0} 并单击链接", "DE.Views.DocumentHolder.txtPrintSelection": "打印所选内容", "DE.Views.DocumentHolder.txtRemFractionBar": "删除分数栏", "DE.Views.DocumentHolder.txtRemLimit": "取消限制", "DE.Views.DocumentHolder.txtRemoveAccentChar": "删除强调字符", "DE.Views.DocumentHolder.txtRemoveBar": "删除栏", "DE.Views.DocumentHolder.txtRemoveWarning": "您想要移除此签名吗？<br>此操作无法撤销。", "DE.Views.DocumentHolder.txtRemScripts": "删除脚本", "DE.Views.DocumentHolder.txtRemSubscript": "删除下标", "DE.Views.DocumentHolder.txtRemSuperscript": "除去上标", "DE.Views.DocumentHolder.txtScriptsAfter": "文字后的脚本", "DE.Views.DocumentHolder.txtScriptsBefore": "文字前的腳本", "DE.Views.DocumentHolder.txtShowBottomLimit": "显示底限", "DE.Views.DocumentHolder.txtShowCloseBracket": "显示结束括号", "DE.Views.DocumentHolder.txtShowDegree": "显示度数", "DE.Views.DocumentHolder.txtShowOpenBracket": "显示开始括号", "DE.Views.DocumentHolder.txtShowPlaceholder": "显示占位符", "DE.Views.DocumentHolder.txtShowTopLimit": "显示上限", "DE.Views.DocumentHolder.txtSquare": "方形", "DE.Views.DocumentHolder.txtStretchBrackets": "延展括号", "DE.Views.DocumentHolder.txtThrough": "通过", "DE.Views.DocumentHolder.txtTight": "紧", "DE.Views.DocumentHolder.txtTop": "顶部", "DE.Views.DocumentHolder.txtTopAndBottom": "顶部和底部", "DE.Views.DocumentHolder.txtUnderbar": "文本下方横条", "DE.Views.DocumentHolder.txtUngroup": "取消组合", "DE.Views.DocumentHolder.txtWarnUrl": "点击此链接可能对您的设备和数据有害<br>您确定要继续吗？", "DE.Views.DocumentHolder.unicodeText": "Unicode码", "DE.Views.DocumentHolder.updateStyleText": "更新%1样式", "DE.Views.DocumentHolder.vertAlignText": "垂直對齊", "DE.Views.DropcapSettingsAdvanced.strBorders": "边框和填充", "DE.Views.DropcapSettingsAdvanced.strDropcap": "首字大写", "DE.Views.DropcapSettingsAdvanced.strMargins": "边距", "DE.Views.DropcapSettingsAdvanced.textAlign": "对齐", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "至少", "DE.Views.DropcapSettingsAdvanced.textAuto": "自动", "DE.Views.DropcapSettingsAdvanced.textBackColor": "背景颜色", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "边框颜色", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "点击图表或使用按钮选择边框", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "边框大小", "DE.Views.DropcapSettingsAdvanced.textBottom": "底部", "DE.Views.DropcapSettingsAdvanced.textCenter": "中心", "DE.Views.DropcapSettingsAdvanced.textColumn": "列", "DE.Views.DropcapSettingsAdvanced.textDistance": "与文本的间距", "DE.Views.DropcapSettingsAdvanced.textExact": "精确地", "DE.Views.DropcapSettingsAdvanced.textFlow": "流程图", "DE.Views.DropcapSettingsAdvanced.textFont": "字体 ", "DE.Views.DropcapSettingsAdvanced.textFrame": "框", "DE.Views.DropcapSettingsAdvanced.textHeight": "高度", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "水平的", "DE.Views.DropcapSettingsAdvanced.textInline": "内联框架", "DE.Views.DropcapSettingsAdvanced.textInMargin": "在页边距", "DE.Views.DropcapSettingsAdvanced.textInText": "在文本中", "DE.Views.DropcapSettingsAdvanced.textLeft": "左", "DE.Views.DropcapSettingsAdvanced.textMargin": "边距", "DE.Views.DropcapSettingsAdvanced.textMove": "随文字移动", "DE.Views.DropcapSettingsAdvanced.textNone": "无", "DE.Views.DropcapSettingsAdvanced.textPage": "页面", "DE.Views.DropcapSettingsAdvanced.textParagraph": "段落", "DE.Views.DropcapSettingsAdvanced.textParameters": "参数", "DE.Views.DropcapSettingsAdvanced.textPosition": "位置", "DE.Views.DropcapSettingsAdvanced.textRelative": "相对于", "DE.Views.DropcapSettingsAdvanced.textRight": "右", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "行高", "DE.Views.DropcapSettingsAdvanced.textTitle": "首字大写 - 高级设置", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "框架 - 高级设置", "DE.Views.DropcapSettingsAdvanced.textTop": "顶部", "DE.Views.DropcapSettingsAdvanced.textVertical": "垂直", "DE.Views.DropcapSettingsAdvanced.textWidth": "宽度", "DE.Views.DropcapSettingsAdvanced.tipFontName": "字体 ", "DE.Views.EditListItemDialog.textDisplayName": "显示名称", "DE.Views.EditListItemDialog.textNameError": "显示名称不能为空。", "DE.Views.EditListItemDialog.textValue": "值", "DE.Views.EditListItemDialog.textValueError": "具有相同值的项已存在。", "DE.Views.FileMenu.ariaFileMenu": "文件菜单", "DE.Views.FileMenu.btnBackCaption": "打开文件所在位置", "DE.Views.FileMenu.btnCloseEditor": "关闭文件", "DE.Views.FileMenu.btnCloseMenuCaption": "返回", "DE.Views.FileMenu.btnCreateNewCaption": "新建", "DE.Views.FileMenu.btnDownloadCaption": "下载为", "DE.Views.FileMenu.btnExitCaption": "关闭", "DE.Views.FileMenu.btnFileOpenCaption": "打开", "DE.Views.FileMenu.btnHelpCaption": "帮助", "DE.Views.FileMenu.btnHistoryCaption": "版本历史", "DE.Views.FileMenu.btnInfoCaption": "信息", "DE.Views.FileMenu.btnPrintCaption": "打印", "DE.Views.FileMenu.btnProtectCaption": "保护", "DE.Views.FileMenu.btnRecentFilesCaption": "打开最近", "DE.Views.FileMenu.btnRenameCaption": "重命名", "DE.Views.FileMenu.btnReturnCaption": "返回到文件", "DE.Views.FileMenu.btnRightsCaption": "访问权限", "DE.Views.FileMenu.btnSaveAsCaption": "另存为", "DE.Views.FileMenu.btnSaveCaption": "保存", "DE.Views.FileMenu.btnSaveCopyAsCaption": "另存副本为", "DE.Views.FileMenu.btnSettingsCaption": "高级设置", "DE.Views.FileMenu.btnSwitchToMobileCaption": "切换到移动模式", "DE.Views.FileMenu.btnToEditCaption": "编辑文档", "DE.Views.FileMenu.textDownload": "下载", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "空白文档", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "新建", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "应用", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "添加作者", "DE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "添加属性", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "添加文字", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "应用程序", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作者", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "更改访问权限", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "批注", "DE.Views.FileMenuPanels.DocumentInfo.txtCommon": "通用", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "已创建", "DE.Views.FileMenuPanels.DocumentInfo.txtDocumentInfo": "文档信息", "DE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "文档属性", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "快速Web视图", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "加载中…", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "最后修改者", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "上一次更改", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "否", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "创建者", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "页面", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "页面大小", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "段落", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "PDF生成器", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "已标记的PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "PDF版本", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "位置", "DE.Views.FileMenuPanels.DocumentInfo.txtProperties": "属性", "DE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "具有该标题的属性已存在", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "拥有权限的人", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "字符 (包括空格)", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "统计", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "主题", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "字符", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "标签", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "标题", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "已上传", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "单词", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "是", "DE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "访问权限", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "更改访问权限", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "拥有权限的人", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "密码保护", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "保护文档", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "签名保护", "DE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "有效签名已添加到文档中<br>文档受到保护，不可编辑。", "DE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "通过添加<br>不可见的数字签名来确保文档的完整性", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "编辑文档", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "编辑将删除文档中的签名<br>是否继续？", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "此文件已使用密码保护。", "DE.Views.FileMenuPanels.ProtectDoc.txtProtectDocument": "使用密码加密此文档", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "此文件需要签名。", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有效签名已添加到文档中。文档受到保护，不可编辑。", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "文件中的一些数字签名无效或无法验证。该文件受到保护，无法编辑。", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "查看签名", "DE.Views.FileMenuPanels.Settings.okButtonText": "应用", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "共同编辑模式", "DE.Views.FileMenuPanels.Settings.strFast": "快速", "DE.Views.FileMenuPanels.Settings.strFontRender": "字体设置", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "忽略大写单词", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "忽略带数字的单词", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "宏设置", "DE.Views.FileMenuPanels.Settings.strPasteButton": "粘贴内容时显示“粘贴选项”按钮", "DE.Views.FileMenuPanels.Settings.strRTLSupport": "RTL 界面 (文字从右到左)", "DE.Views.FileMenuPanels.Settings.strShowChanges": "实时协作变更", "DE.Views.FileMenuPanels.Settings.strShowComments": "在文本中显示批注", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "显示来自其他用户的更改", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "显示已解决的批注", "DE.Views.FileMenuPanels.Settings.strStrict": "严格", "DE.Views.FileMenuPanels.Settings.strTabStyle": "选项卡样式", "DE.Views.FileMenuPanels.Settings.strTheme": "界面主题", "DE.Views.FileMenuPanels.Settings.strUnit": "计量单位", "DE.Views.FileMenuPanels.Settings.strZoom": "默认缩放值", "DE.Views.FileMenuPanels.Settings.text10Minutes": "每10分钟", "DE.Views.FileMenuPanels.Settings.text30Minutes": "每30分钟", "DE.Views.FileMenuPanels.Settings.text5Minutes": "每5分钟", "DE.Views.FileMenuPanels.Settings.text60Minutes": "每隔一小时", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "对齐辅助线", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "自动恢复", "DE.Views.FileMenuPanels.Settings.textAutoSave": "自动保存", "DE.Views.FileMenuPanels.Settings.textDisabled": "已禁用", "DE.Views.FileMenuPanels.Settings.textFill": "填充", "DE.Views.FileMenuPanels.Settings.textForceSave": "保存中间版本", "DE.Views.FileMenuPanels.Settings.textLine": "线", "DE.Views.FileMenuPanels.Settings.textMinute": "每一分钟", "DE.Views.FileMenuPanels.Settings.textOldVersions": "当保存为 DOCX、DOTX 格式时使文件兼容旧版的 MS Word", "DE.Views.FileMenuPanels.Settings.textSmartSelection": "使用智能段落选择", "DE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "高级设置", "DE.Views.FileMenuPanels.Settings.txtAll": "查看全部", "DE.Views.FileMenuPanels.Settings.txtAppearance": "外观", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "自动更正选项...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "默认缓存模式", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "点击内容气球时显示", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "悬停在工具提示之上时显示", "DE.Views.FileMenuPanels.Settings.txtCm": "厘米", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "协作", "DE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "自定义快速访问", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "启用文档深色模式", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "编辑并保存", "DE.Views.FileMenuPanels.Settings.txtFastTip": "实时共同编辑。所有更改都会自动保存", "DE.Views.FileMenuPanels.Settings.txtFitPage": "调整至页面大小", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "调整至宽度大小", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "象形文字", "DE.Views.FileMenuPanels.Settings.txtInch": "英寸", "DE.Views.FileMenuPanels.Settings.txtLast": "查看上一个", "DE.Views.FileMenuPanels.Settings.txtLastUsed": "最后一次使用", "DE.Views.FileMenuPanels.Settings.txtMac": "按照 OS X 样式", "DE.Views.FileMenuPanels.Settings.txtNative": "本地", "DE.Views.FileMenuPanels.Settings.txtNone": "无查看", "DE.Views.FileMenuPanels.Settings.txtProofing": "校对", "DE.Views.FileMenuPanels.Settings.txtPt": "点", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "编辑器标题栏显示“快速打印”按钮", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "文档将打印到最近选择的打印机或者默认打印机", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "全部启用", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "启用全部宏，不显示通知", "DE.Views.FileMenuPanels.Settings.txtScreenReader": "打开屏幕朗读器支持", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "显示跟踪更改", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "拼写检查", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "全部停用", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "禁用全部宏，不显示通知", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "使用“保存”按钮同步您和其他人所做的更改", "DE.Views.FileMenuPanels.Settings.txtTabBack": "使用工具栏颜色作为选项卡背景", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "按 Alt 键后可通过键盘在用户界面中导航", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "用Option键使用键盘浏览用户界面", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "显示通知", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "禁用全部宏，并显示通知", "DE.Views.FileMenuPanels.Settings.txtWin": "按照 Windows 样式", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "工作区", "DE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "下载为", "DE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "另存副本为", "DE.Views.FormSettings.textAlways": "总是", "DE.Views.FormSettings.textAnyone": "任何人", "DE.Views.FormSettings.textAspect": "锁定宽高比", "DE.Views.FormSettings.textAtLeast": "至少", "DE.Views.FormSettings.textAuto": "自动", "DE.Views.FormSettings.textAutofit": "自动调整", "DE.Views.FormSettings.textBackgroundColor": "背景颜色", "DE.Views.FormSettings.textCheckbox": "多选框", "DE.Views.FormSettings.textCheckDefault": "复选框默认选中", "DE.Views.FormSettings.textColor": "边框颜色", "DE.Views.FormSettings.textComb": "文字组合", "DE.Views.FormSettings.textCombobox": "下拉式方框", "DE.Views.FormSettings.textComplex": "复合字段", "DE.Views.FormSettings.textConnected": "已连接的字段", "DE.Views.FormSettings.textCreditCard": "信用卡号码（例如 4111-1111-1111-1111）", "DE.Views.FormSettings.textDateField": "日期和时间字段", "DE.Views.FormSettings.textDateFormat": "这样显示日期", "DE.Views.FormSettings.textDefValue": "默认值", "DE.Views.FormSettings.textDelete": "刪除", "DE.Views.FormSettings.textDigits": "数字", "DE.Views.FormSettings.textDisconnect": "断开", "DE.Views.FormSettings.textDropDown": "下拉菜单", "DE.Views.FormSettings.textExact": "精确地", "DE.Views.FormSettings.textField": "文本字段", "DE.Views.FormSettings.textFillRoles": "谁需要填写这个？", "DE.Views.FormSettings.textFixed": "固定大小字段", "DE.Views.FormSettings.textFormat": "格式", "DE.Views.FormSettings.textFormatSymbols": "允许的符号", "DE.Views.FormSettings.textFromFile": "从文件导入", "DE.Views.FormSettings.textFromStorage": "来自存储设备", "DE.Views.FormSettings.textFromUrl": "来自URL", "DE.Views.FormSettings.textGroupKey": "组密钥", "DE.Views.FormSettings.textImage": "图片", "DE.Views.FormSettings.textKey": "秘钥", "DE.Views.FormSettings.textLang": "语言", "DE.Views.FormSettings.textLetters": "字母", "DE.Views.FormSettings.textLock": "锁定", "DE.Views.FormSettings.textMask": "任意遮罩", "DE.Views.FormSettings.textMaxChars": "字符限制", "DE.Views.FormSettings.textMulti": "多行文本字段", "DE.Views.FormSettings.textNever": "从不", "DE.Views.FormSettings.textNoBorder": "无边框", "DE.Views.FormSettings.textNone": "无", "DE.Views.FormSettings.textPhone1": "电话号码（例如（123）456-7890）", "DE.Views.FormSettings.textPhone2": "电话号码（例如+44791123456）", "DE.Views.FormSettings.textPlaceholder": "占位符", "DE.Views.FormSettings.textRadiobox": "单选按钮", "DE.Views.FormSettings.textRadioChoice": "单选按钮选项", "DE.Views.FormSettings.textRadioDefault": "按钮默认选中", "DE.Views.FormSettings.textReg": "正则表达式", "DE.Views.FormSettings.textRequired": "必填", "DE.Views.FormSettings.textScale": "何時縮放", "DE.Views.FormSettings.textSelectImage": "选择图像", "DE.Views.FormSettings.textSignature": "签名", "DE.Views.FormSettings.textTag": "标签", "DE.Views.FormSettings.textTip": "提示", "DE.Views.FormSettings.textTipAdd": "添加新值", "DE.Views.FormSettings.textTipDelete": "删除值", "DE.Views.FormSettings.textTipDown": "下移", "DE.Views.FormSettings.textTipUp": "上移", "DE.Views.FormSettings.textTooBig": "图片太大", "DE.Views.FormSettings.textTooSmall": "图像太小", "DE.Views.FormSettings.textUKPassport": "英国护照号码（例如*********）", "DE.Views.FormSettings.textUnlock": "解锁", "DE.Views.FormSettings.textUSSSN": "美国社会安全码（例如***********）", "DE.Views.FormSettings.textValue": "数值选项", "DE.Views.FormSettings.textWidth": "单元格宽度", "DE.Views.FormSettings.textZipCodeUS": "美国邮政编码（例如92663或92663-1234）", "DE.Views.FormsTab.capBtnCheckBox": "复选框", "DE.Views.FormsTab.capBtnComboBox": "下拉式方框", "DE.Views.FormsTab.capBtnComplex": "复合字段", "DE.Views.FormsTab.capBtnDownloadForm": "下载为 PDF", "DE.Views.FormsTab.capBtnDropDown": "下拉菜单", "DE.Views.FormsTab.capBtnEmail": "Email地址", "DE.Views.FormsTab.capBtnImage": "图片", "DE.Views.FormsTab.capBtnManager": "管理角色", "DE.Views.FormsTab.capBtnNext": "下一个字段", "DE.Views.FormsTab.capBtnPhone": "电话号码", "DE.Views.FormsTab.capBtnPrev": "上一个字段", "DE.Views.FormsTab.capBtnRadioBox": "单选按钮", "DE.Views.FormsTab.capBtnSaveForm": "另存为PDF", "DE.Views.FormsTab.capBtnSaveFormDesktop": "另存为...", "DE.Views.FormsTab.capBtnSignature": "签名", "DE.Views.FormsTab.capBtnSubmit": "提交", "DE.Views.FormsTab.capBtnText": "文本字段", "DE.Views.FormsTab.capBtnView": "预览表单", "DE.Views.FormsTab.capCreditCard": "信用卡", "DE.Views.FormsTab.capDateTime": "日期和时间", "DE.Views.FormsTab.capZipCode": "邮编", "DE.Views.FormsTab.helpTextFillStatus": "现在可以根据角色填写此表单。单击状态按钮，可检查填写进度。", "DE.Views.FormsTab.textAnyone": "任何人", "DE.Views.FormsTab.textClear": "清除字段", "DE.Views.FormsTab.textClearFields": "清除所有字段", "DE.Views.FormsTab.textCreateForm": "添加字段并创建可填写的PDF文档", "DE.Views.FormsTab.textFilled": "已填写", "DE.Views.FormsTab.textGotIt": "<PERSON><PERSON>", "DE.Views.FormsTab.textHighlight": "高亮设置", "DE.Views.FormsTab.textNoHighlight": "无高亮", "DE.Views.FormsTab.textRequired": "要提交该表单，请填写所有必填字段。", "DE.Views.FormsTab.textSubmited": "表单提交成功", "DE.Views.FormsTab.textSubmitOk": "您的 PDF 表单已保存，可在“完成”模块访问。", "DE.Views.FormsTab.tipCheckBox": "“插入”复选框", "DE.Views.FormsTab.tipComboBox": "插入下拉式方框", "DE.Views.FormsTab.tipComplexField": "插入复合字段", "DE.Views.FormsTab.tipCreateField": "要创建字段，请在工具栏中选择并点击所需的字段类型。该字段将出现在文档中。", "DE.Views.FormsTab.tipCreditCard": "插入信用卡号", "DE.Views.FormsTab.tipDateTime": "插入日期和时间", "DE.Views.FormsTab.tipDownloadForm": "将文件下载为可填充的PDF文档", "DE.Views.FormsTab.tipDropDown": "插入下拉列表", "DE.Views.FormsTab.tipEmailField": "插入电子邮件地址", "DE.Views.FormsTab.tipFieldSettings": "您可以在右侧边栏设置选定的字段。单击此图标可打开字段设置。", "DE.Views.FormsTab.tipFieldsLink": "了解更多关于字段参数", "DE.Views.FormsTab.tipFirstPage": "转到第一页", "DE.Views.FormsTab.tipFixedText": "插入固定文本字段", "DE.Views.FormsTab.tipFormGroupKey": "对单选按钮进行分组可以更快进行填充。相同名称的选项会进行同步。用户只能勾选该组中的一个单选按钮。", "DE.Views.FormsTab.tipFormKey": "您可以给一个字段或一组字段设置密钥。 当用户填写数据时，所有具有相同密钥的字段都将复制该数据。", "DE.Views.FormsTab.tipHelpRoles": "使用管理角色功能，按用途对字段进行分组并分配负责的团队成员。", "DE.Views.FormsTab.tipImageField": "插入图片", "DE.Views.FormsTab.tipInlineText": "插入内联文本字段", "DE.Views.FormsTab.tipLastPage": "转到最后一页", "DE.Views.FormsTab.tipManager": "管理角色", "DE.Views.FormsTab.tipNextForm": "跳转到下一个字段", "DE.Views.FormsTab.tipNextPage": "跳转到下一页", "DE.Views.FormsTab.tipPhoneField": "插入电话号码", "DE.Views.FormsTab.tipPrevForm": "跳转到上一个字段", "DE.Views.FormsTab.tipPrevPage": "跳转到上一页", "DE.Views.FormsTab.tipRadioBox": "插入单选按钮", "DE.Views.FormsTab.tipRolesLink": "了解更多关于角色", "DE.Views.FormsTab.tipSaveFile": "点击“另存为pdf”将表单保存为可填写的格式。", "DE.Views.FormsTab.tipSaveForm": "将文件另存为可填充的PDF文档", "DE.Views.FormsTab.tipSignField": "添加签名字段", "DE.Views.FormsTab.tipSubmit": "提交表单", "DE.Views.FormsTab.tipTextField": "插入文本字段", "DE.Views.FormsTab.tipViewForm": "预览表单", "DE.Views.FormsTab.tipZipCode": "插入邮政编码", "DE.Views.FormsTab.txtFixedDesc": "插入固定文本字段", "DE.Views.FormsTab.txtFixedText": "固定", "DE.Views.FormsTab.txtInlineDesc": "插入内联文本字段", "DE.Views.FormsTab.txtInlineText": "内联", "DE.Views.FormsTab.txtUntitled": "无标题", "DE.Views.HeaderFooterSettings.textBottomCenter": "底部中心", "DE.Views.HeaderFooterSettings.textBottomLeft": "左下方", "DE.Views.HeaderFooterSettings.textBottomPage": "页面底部", "DE.Views.HeaderFooterSettings.textBottomRight": "右下方", "DE.Views.HeaderFooterSettings.textDiffFirst": "首页不同", "DE.Views.HeaderFooterSettings.textDiffOdd": "奇偶页不同", "DE.Views.HeaderFooterSettings.textFrom": "开始于", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "底部页脚", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "页眉顶端距离", "DE.Views.HeaderFooterSettings.textInsertCurrent": "插入到当前位置", "DE.Views.HeaderFooterSettings.textNumFormat": "数字格式", "DE.Views.HeaderFooterSettings.textOptions": "选项", "DE.Views.HeaderFooterSettings.textPageNum": "插入页码", "DE.Views.HeaderFooterSettings.textPageNumbering": "页面编号", "DE.Views.HeaderFooterSettings.textPosition": "位置", "DE.Views.HeaderFooterSettings.textPrev": "从上一节继续", "DE.Views.HeaderFooterSettings.textSameAs": "链接到上一个", "DE.Views.HeaderFooterSettings.textTopCenter": "顶部中心", "DE.Views.HeaderFooterSettings.textTopLeft": "左上方", "DE.Views.HeaderFooterSettings.textTopPage": "页面顶部", "DE.Views.HeaderFooterSettings.textTopRight": "右上", "DE.Views.HeaderFooterSettings.txtMoreTypes": "更多类型", "DE.Views.HyperlinkSettingsDialog.textDefault": "所选文本片段", "DE.Views.HyperlinkSettingsDialog.textDisplay": "显示", "DE.Views.HyperlinkSettingsDialog.textExternal": "外部链接", "DE.Views.HyperlinkSettingsDialog.textInternal": "放入文件中", "DE.Views.HyperlinkSettingsDialog.textSelectFile": "选择文件", "DE.Views.HyperlinkSettingsDialog.textTitle": "超链接设置", "DE.Views.HyperlinkSettingsDialog.textTooltip": "屏幕提示文字", "DE.Views.HyperlinkSettingsDialog.textUrl": "链接到", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "文件开头", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "书签", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "这是必填栏", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "标题", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "该字段应该是“http://www.example.com”格式的URL", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "此字段限制为2083个字符", "DE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "输入网址或选择文件", "DE.Views.HyphenationDialog.textAuto": "自动连字符", "DE.Views.HyphenationDialog.textCaps": "连字符大写字母", "DE.Views.HyphenationDialog.textLimit": "将连续连字符限制为", "DE.Views.HyphenationDialog.textNoLimit": "无限制", "DE.Views.HyphenationDialog.textTitle": "连字符", "DE.Views.HyphenationDialog.textZone": "连字符区", "DE.Views.ImageSettings.strTransparency": "透明度", "DE.Views.ImageSettings.textAdvanced": "显示高级设置", "DE.Views.ImageSettings.textCrop": "裁剪", "DE.Views.ImageSettings.textCropFill": "填入", "DE.Views.ImageSettings.textCropFit": "适应", "DE.Views.ImageSettings.textCropToShape": "裁剪成形状", "DE.Views.ImageSettings.textEdit": "编辑", "DE.Views.ImageSettings.textEditObject": "编辑对象", "DE.Views.ImageSettings.textFitMargins": "调整至适合边距", "DE.Views.ImageSettings.textFlip": "翻转", "DE.Views.ImageSettings.textFromFile": "从文件导入", "DE.Views.ImageSettings.textFromStorage": "来自存储设备", "DE.Views.ImageSettings.textFromUrl": "来自URL", "DE.Views.ImageSettings.textHeight": "高度", "DE.Views.ImageSettings.textHint270": "逆时针旋转90°", "DE.Views.ImageSettings.textHint90": "顺时针旋转90°", "DE.Views.ImageSettings.textHintFlipH": "水平翻转", "DE.Views.ImageSettings.textHintFlipV": "垂直翻转", "DE.Views.ImageSettings.textInsert": "替换图像", "DE.Views.ImageSettings.textOriginalSize": "实际大小", "DE.Views.ImageSettings.textRecentlyUsed": "最近使用的", "DE.Views.ImageSettings.textResetCrop": "重置裁剪", "DE.Views.ImageSettings.textRotate90": "旋转90°", "DE.Views.ImageSettings.textRotation": "旋转", "DE.Views.ImageSettings.textSize": "大小", "DE.Views.ImageSettings.textWidth": "宽度", "DE.Views.ImageSettings.textWrap": "环绕方式", "DE.Views.ImageSettings.txtBehind": "在文本后面", "DE.Views.ImageSettings.txtInFront": "在文字前面", "DE.Views.ImageSettings.txtInline": "与文本对齐", "DE.Views.ImageSettings.txtSquare": "方形", "DE.Views.ImageSettings.txtThrough": "通过", "DE.Views.ImageSettings.txtTight": "紧", "DE.Views.ImageSettings.txtTopAndBottom": "顶部和底部", "DE.Views.ImageSettingsAdvanced.strMargins": "文字內边距", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "绝对", "DE.Views.ImageSettingsAdvanced.textAlignment": "对齐", "DE.Views.ImageSettingsAdvanced.textAlt": "替代文本", "DE.Views.ImageSettingsAdvanced.textAltDescription": "描述", "DE.Views.ImageSettingsAdvanced.textAltTip": "视觉对象信息的另一种基于文本的表示方式，将读取给视力或认知障碍的人，以帮助他们更好地理解图像、形状、图表或表格中的信息。", "DE.Views.ImageSettingsAdvanced.textAltTitle": "标题", "DE.Views.ImageSettingsAdvanced.textAngle": "角度", "DE.Views.ImageSettingsAdvanced.textArrows": "箭头", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "锁定宽高比", "DE.Views.ImageSettingsAdvanced.textAutofit": "自动调整", "DE.Views.ImageSettingsAdvanced.textBeginSize": "初始大小", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "初始样式", "DE.Views.ImageSettingsAdvanced.textBelow": "下面", "DE.Views.ImageSettingsAdvanced.textBevel": "斜角", "DE.Views.ImageSettingsAdvanced.textBottom": "底部", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "下边距", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "文本环绕", "DE.Views.ImageSettingsAdvanced.textCapType": "大写字母样式", "DE.Views.ImageSettingsAdvanced.textCenter": "中心", "DE.Views.ImageSettingsAdvanced.textCharacter": "字符", "DE.Views.ImageSettingsAdvanced.textColumn": "列", "DE.Views.ImageSettingsAdvanced.textDistance": "与文本的间距", "DE.Views.ImageSettingsAdvanced.textEndSize": "末端尺寸", "DE.Views.ImageSettingsAdvanced.textEndStyle": "结束样式", "DE.Views.ImageSettingsAdvanced.textFlat": "平面", "DE.Views.ImageSettingsAdvanced.textFlipped": "已翻转的", "DE.Views.ImageSettingsAdvanced.textHeight": "高度", "DE.Views.ImageSettingsAdvanced.textHorizontal": "水平的", "DE.Views.ImageSettingsAdvanced.textHorizontally": "水平地", "DE.Views.ImageSettingsAdvanced.textJoinType": "加入类型", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "恒定比例", "DE.Views.ImageSettingsAdvanced.textLeft": "左", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "左边距", "DE.Views.ImageSettingsAdvanced.textLine": "折线图", "DE.Views.ImageSettingsAdvanced.textLineStyle": "线样式", "DE.Views.ImageSettingsAdvanced.textMargin": "边距", "DE.Views.ImageSettingsAdvanced.textMiter": "斜接角", "DE.Views.ImageSettingsAdvanced.textMove": "移动带文本的对象", "DE.Views.ImageSettingsAdvanced.textOptions": "选项", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "实际大小", "DE.Views.ImageSettingsAdvanced.textOverlap": "允许重叠", "DE.Views.ImageSettingsAdvanced.textPage": "页面", "DE.Views.ImageSettingsAdvanced.textParagraph": "段落", "DE.Views.ImageSettingsAdvanced.textPosition": "位置", "DE.Views.ImageSettingsAdvanced.textPositionPc": "相对位置", "DE.Views.ImageSettingsAdvanced.textRelative": "相对于", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "相对的", "DE.Views.ImageSettingsAdvanced.textResizeFit": "调整形状大小以适应文本", "DE.Views.ImageSettingsAdvanced.textRight": "右", "DE.Views.ImageSettingsAdvanced.textRightMargin": "右页边距", "DE.Views.ImageSettingsAdvanced.textRightOf": "在 - 的右边", "DE.Views.ImageSettingsAdvanced.textRotation": "旋转", "DE.Views.ImageSettingsAdvanced.textRound": "圆", "DE.Views.ImageSettingsAdvanced.textShape": "形状设置", "DE.Views.ImageSettingsAdvanced.textSize": "大小", "DE.Views.ImageSettingsAdvanced.textSquare": "方形", "DE.Views.ImageSettingsAdvanced.textTextBox": "文本框", "DE.Views.ImageSettingsAdvanced.textTitle": "图片 - 高级设置", "DE.Views.ImageSettingsAdvanced.textTitleChart": "图表 - 高级设置", "DE.Views.ImageSettingsAdvanced.textTitleShape": "形状 - 高级设置", "DE.Views.ImageSettingsAdvanced.textTop": "顶部", "DE.Views.ImageSettingsAdvanced.textTopMargin": "上边距", "DE.Views.ImageSettingsAdvanced.textVertical": "垂直", "DE.Views.ImageSettingsAdvanced.textVertically": "垂直地", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "權重與箭頭", "DE.Views.ImageSettingsAdvanced.textWidth": "宽度", "DE.Views.ImageSettingsAdvanced.textWrap": "环绕方式", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "在文本后面", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "在文字前面", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "与文本对齐", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "方形", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "通过", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "紧", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "顶部和底部", "DE.Views.LeftMenu.ariaLeftMenu": "左侧菜单", "DE.Views.LeftMenu.tipAbout": "关于", "DE.Views.LeftMenu.tipChat": "聊天", "DE.Views.LeftMenu.tipComments": "批注", "DE.Views.LeftMenu.tipNavigation": "导航", "DE.Views.LeftMenu.tipOutline": "标题", "DE.Views.LeftMenu.tipPageThumbnails": "页面缩略图", "DE.Views.LeftMenu.tipPlugins": "插件", "DE.Views.LeftMenu.tipSearch": "搜索", "DE.Views.LeftMenu.tipSupport": "反馈和支持", "DE.Views.LeftMenu.tipTitles": "标题", "DE.Views.LeftMenu.txtDeveloper": "开发者模式", "DE.Views.LeftMenu.txtEditor": "文档编辑器", "DE.Views.LeftMenu.txtLimit": "限制访问", "DE.Views.LeftMenu.txtTrial": "试用模式", "DE.Views.LeftMenu.txtTrialDev": "试用开发者模式", "DE.Views.LineNumbersDialog.textAddLineNumbering": "添加行号", "DE.Views.LineNumbersDialog.textApplyTo": "应用更改于", "DE.Views.LineNumbersDialog.textContinuous": "连续", "DE.Views.LineNumbersDialog.textCountBy": "计数", "DE.Views.LineNumbersDialog.textDocument": "整个文件", "DE.Views.LineNumbersDialog.textForward": "此处起始", "DE.Views.LineNumbersDialog.textFromText": "来自文字", "DE.Views.LineNumbersDialog.textNumbering": "编号", "DE.Views.LineNumbersDialog.textRestartEachPage": "重新启动每一页", "DE.Views.LineNumbersDialog.textRestartEachSection": "重新启动每一节", "DE.Views.LineNumbersDialog.textSection": "当前章节", "DE.Views.LineNumbersDialog.textStartAt": "开始于", "DE.Views.LineNumbersDialog.textTitle": "行号", "DE.Views.LineNumbersDialog.txtAutoText": "自动", "DE.Views.Links.capBtnAddText": "添加文字", "DE.Views.Links.capBtnBookmarks": "书签", "DE.Views.Links.capBtnCaption": "标题", "DE.Views.Links.capBtnContentsUpdate": "更新表格", "DE.Views.Links.capBtnCrossRef": "交叉引用", "DE.Views.Links.capBtnInsContents": "目录", "DE.Views.Links.capBtnInsFootnote": "脚注", "DE.Views.Links.capBtnInsLink": "超链接", "DE.Views.Links.capBtnTOF": "图表目录", "DE.Views.Links.confirmDeleteFootnotes": "您想要删除所有脚注吗？", "DE.Views.Links.confirmReplaceTOF": "您想要替换选中的图表吗？", "DE.Views.Links.mniConvertNote": "转换所有笔记", "DE.Views.Links.mniDelFootnote": "删除所有笔记", "DE.Views.Links.mniInsEndnote": "插入尾注", "DE.Views.Links.mniInsFootnote": "插入脚注", "DE.Views.Links.mniNoteSettings": "笔记设置", "DE.Views.Links.textContentsRemove": "删除目录", "DE.Views.Links.textContentsSettings": "设置", "DE.Views.Links.textConvertToEndnotes": "将所有脚注转换为尾注", "DE.Views.Links.textConvertToFootnotes": "将所有尾注转换为脚注", "DE.Views.Links.textGotoEndnote": "转到尾注", "DE.Views.Links.textGotoFootnote": "转到脚注", "DE.Views.Links.textSwapNotes": "交换脚注和章节末注", "DE.Views.Links.textUpdateAll": "更新整个表格", "DE.Views.Links.textUpdatePages": "仅更新页码", "DE.Views.Links.tipAddText": "在目录中包括标题", "DE.Views.Links.tipBookmarks": "创建书签", "DE.Views.Links.tipCaption": "插入标题", "DE.Views.Links.tipContents": "插入目录", "DE.Views.Links.tipContentsUpdate": "更新目录", "DE.Views.Links.tipCrossRef": "插入交叉引用", "DE.Views.Links.tipInsertHyperlink": "添加超链接", "DE.Views.Links.tipNotes": "插入或编辑脚注", "DE.Views.Links.tipTableFigures": "插入图表", "DE.Views.Links.tipTableFiguresUpdate": "更新图表", "DE.Views.Links.titleUpdateTOF": "更新图表", "DE.Views.Links.txtDontShowTof": "不显示在目录中", "DE.Views.Links.txtLevel": "级别", "DE.Views.ListIndentsDialog.textSpace": "空格", "DE.Views.ListIndentsDialog.textTab": "标签字符", "DE.Views.ListIndentsDialog.textTitle": "列表缩进", "DE.Views.ListIndentsDialog.txtFollowBullet": "跟随项目符号", "DE.Views.ListIndentsDialog.txtFollowNumber": "跟随数字", "DE.Views.ListIndentsDialog.txtIndent": "文字缩进", "DE.Views.ListIndentsDialog.txtNone": "无", "DE.Views.ListIndentsDialog.txtPosBullet": "项目符号位置", "DE.Views.ListIndentsDialog.txtPosNumber": "编号位置", "DE.Views.ListSettingsDialog.textAuto": "自动", "DE.Views.ListSettingsDialog.textBold": "粗体", "DE.Views.ListSettingsDialog.textCenter": "中心", "DE.Views.ListSettingsDialog.textHide": "隐藏设置", "DE.Views.ListSettingsDialog.textItalic": "斜体", "DE.Views.ListSettingsDialog.textLeft": "左", "DE.Views.ListSettingsDialog.textLevel": "级别", "DE.Views.ListSettingsDialog.textMore": "显示更多设置", "DE.Views.ListSettingsDialog.textPreview": "预览", "DE.Views.ListSettingsDialog.textRight": "右", "DE.Views.ListSettingsDialog.textSelectLevel": "选择级别", "DE.Views.ListSettingsDialog.textSpace": "空格", "DE.Views.ListSettingsDialog.textTab": "标签字符", "DE.Views.ListSettingsDialog.txtAlign": "对齐", "DE.Views.ListSettingsDialog.txtAlignAt": "在", "DE.Views.ListSettingsDialog.txtBullet": "项目符号", "DE.Views.ListSettingsDialog.txtColor": "颜色", "DE.Views.ListSettingsDialog.txtFollow": "跟随数字", "DE.Views.ListSettingsDialog.txtFontName": "字体 ", "DE.Views.ListSettingsDialog.txtInclcudeLevel": "包括级别编号", "DE.Views.ListSettingsDialog.txtIndent": "文字缩进", "DE.Views.ListSettingsDialog.txtLikeText": "像文字", "DE.Views.ListSettingsDialog.txtMoreTypes": "更多类型", "DE.Views.ListSettingsDialog.txtNewBullet": "新项目符号", "DE.Views.ListSettingsDialog.txtNone": "无", "DE.Views.ListSettingsDialog.txtNumFormatString": "数字格式", "DE.Views.ListSettingsDialog.txtRestart": "重新启动列表", "DE.Views.ListSettingsDialog.txtSize": "大小", "DE.Views.ListSettingsDialog.txtStart": "开始于", "DE.Views.ListSettingsDialog.txtSymbol": "符号", "DE.Views.ListSettingsDialog.txtTabStop": "在添加制表位", "DE.Views.ListSettingsDialog.txtTitle": "列表设置", "DE.Views.ListSettingsDialog.txtType": "类型", "DE.Views.ListTypesAdvanced.labelSelect": "选择列表类型", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "发送", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "主题", "DE.Views.MailMergeEmailDlg.textAttachDocx": "附加为DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "附加为PDF", "DE.Views.MailMergeEmailDlg.textFileName": "文件名", "DE.Views.MailMergeEmailDlg.textFormat": "邮件格式", "DE.Views.MailMergeEmailDlg.textFrom": "从", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "消息", "DE.Views.MailMergeEmailDlg.textSubject": "主旨行", "DE.Views.MailMergeEmailDlg.textTitle": "发送到电子邮件", "DE.Views.MailMergeEmailDlg.textTo": "到", "DE.Views.MailMergeEmailDlg.textWarning": "警告！", "DE.Views.MailMergeEmailDlg.textWarningMsg": "请注意，一旦您点击“发送”按钮，邮件无法停止。", "DE.Views.MailMergeSettings.downloadMergeTitle": "合并", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "合并失败", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "警告", "DE.Views.MailMergeSettings.textAddRecipients": "首先将一些收件人添加到列表中", "DE.Views.MailMergeSettings.textAll": "所有记录", "DE.Views.MailMergeSettings.textCurrent": "当前记录", "DE.Views.MailMergeSettings.textDataSource": "数据来源", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "下载", "DE.Views.MailMergeSettings.textEditData": "编辑收件人列表", "DE.Views.MailMergeSettings.textEmail": "电邮", "DE.Views.MailMergeSettings.textFrom": "从", "DE.Views.MailMergeSettings.textGoToMail": "转到邮件", "DE.Views.MailMergeSettings.textHighlight": "高亮显示合并字段", "DE.Views.MailMergeSettings.textInsertField": "插入合并字段", "DE.Views.MailMergeSettings.textMaxRecepients": "最多100位收件人。", "DE.Views.MailMergeSettings.textMerge": "合并", "DE.Views.MailMergeSettings.textMergeFields": "合并字段", "DE.Views.MailMergeSettings.textMergeTo": "合并到", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "保存", "DE.Views.MailMergeSettings.textPreview": "预览结果", "DE.Views.MailMergeSettings.textReadMore": "了解更多", "DE.Views.MailMergeSettings.textSendMsg": "所有邮件都已准备就绪，并会在一段时间内发出。<br>邮件的速度取决于您的邮件服务,您可以继续使用文档或关闭它。操作结束后，通知将发送到您的注册邮箱地址。", "DE.Views.MailMergeSettings.textTo": "到", "DE.Views.MailMergeSettings.txtFirst": "到第一个记录", "DE.Views.MailMergeSettings.txtFromToError": "“从”值必须小于“到”值", "DE.Views.MailMergeSettings.txtLast": "到最后一个记录", "DE.Views.MailMergeSettings.txtNext": "跳转到下一个记录", "DE.Views.MailMergeSettings.txtPrev": "跳转到上一条记录", "DE.Views.MailMergeSettings.txtUntitled": "无标题", "DE.Views.MailMergeSettings.warnProcessMailMerge": "启动合并失败", "DE.Views.Navigation.strNavigate": "标题", "DE.Views.Navigation.txtClosePanel": "关闭标题", "DE.Views.Navigation.txtCollapse": "折叠全部", "DE.Views.Navigation.txtDemote": "使降级", "DE.Views.Navigation.txtEmpty": "文档中没有标题<br>对文本应用标题样式，使其显示在目录中。", "DE.Views.Navigation.txtEmptyItem": "空标题", "DE.Views.Navigation.txtEmptyViewer": "文档中没有标题。", "DE.Views.Navigation.txtExpand": "展开全部", "DE.Views.Navigation.txtExpandToLevel": "展开到级别", "DE.Views.Navigation.txtFontSize": "字体大小", "DE.Views.Navigation.txtHeadingAfter": "之后的新标题", "DE.Views.Navigation.txtHeadingBefore": "之前的新标题", "DE.Views.Navigation.txtLarge": "大", "DE.Views.Navigation.txtMedium": "中", "DE.Views.Navigation.txtNewHeading": "新的副标题", "DE.Views.Navigation.txtPromote": "提升", "DE.Views.Navigation.txtSelect": "选择内容", "DE.Views.Navigation.txtSettings": "标题设置", "DE.Views.Navigation.txtSmall": "小", "DE.Views.Navigation.txtWrapHeadings": "换行长标题", "DE.Views.NoteSettingsDialog.textApply": "应用", "DE.Views.NoteSettingsDialog.textApplyTo": "应用更改于", "DE.Views.NoteSettingsDialog.textContinue": "连续", "DE.Views.NoteSettingsDialog.textCustom": "自定义标记", "DE.Views.NoteSettingsDialog.textDocEnd": "文档结束", "DE.Views.NoteSettingsDialog.textDocument": "整个文件", "DE.Views.NoteSettingsDialog.textEachPage": "重新启动每一页", "DE.Views.NoteSettingsDialog.textEachSection": "重新启动每一节", "DE.Views.NoteSettingsDialog.textEndnote": "尾注", "DE.Views.NoteSettingsDialog.textFootnote": "脚注", "DE.Views.NoteSettingsDialog.textFormat": "格式", "DE.Views.NoteSettingsDialog.textInsert": "插入", "DE.Views.NoteSettingsDialog.textLocation": "位置", "DE.Views.NoteSettingsDialog.textNumbering": "编号", "DE.Views.NoteSettingsDialog.textNumFormat": "数字格式", "DE.Views.NoteSettingsDialog.textPageBottom": "页面底部", "DE.Views.NoteSettingsDialog.textSectEnd": "章节末尾", "DE.Views.NoteSettingsDialog.textSection": "当前章节", "DE.Views.NoteSettingsDialog.textStart": "开始于", "DE.Views.NoteSettingsDialog.textTextBottom": "文字下方", "DE.Views.NoteSettingsDialog.textTitle": "笔记设置", "DE.Views.NotesRemoveDialog.textEnd": "删除所有尾注", "DE.Views.NotesRemoveDialog.textFoot": "删除所有脚注", "DE.Views.NotesRemoveDialog.textTitle": "删除笔记", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "警告", "DE.Views.PageMarginsDialog.textBottom": "底部", "DE.Views.PageMarginsDialog.textGutter": "装订线", "DE.Views.PageMarginsDialog.textGutterPosition": "装订线位置", "DE.Views.PageMarginsDialog.textInside": "內部", "DE.Views.PageMarginsDialog.textLandscape": "横向", "DE.Views.PageMarginsDialog.textLeft": "左", "DE.Views.PageMarginsDialog.textMirrorMargins": "对称页边距", "DE.Views.PageMarginsDialog.textMultiplePages": "多页", "DE.Views.PageMarginsDialog.textNormal": "正常", "DE.Views.PageMarginsDialog.textOrientation": "方向", "DE.Views.PageMarginsDialog.textOutside": "外部", "DE.Views.PageMarginsDialog.textPortrait": "直向方向", "DE.Views.PageMarginsDialog.textPreview": "预览", "DE.Views.PageMarginsDialog.textRight": "右", "DE.Views.PageMarginsDialog.textTitle": "边距", "DE.Views.PageMarginsDialog.textTop": "顶部", "DE.Views.PageMarginsDialog.txtMarginsH": "顶部和底部边距对于给定的页面高度来说太高", "DE.Views.PageMarginsDialog.txtMarginsW": "对于给定的页面宽度，左右边距太宽", "DE.Views.PageSizeDialog.textHeight": "高度", "DE.Views.PageSizeDialog.textPreset": "预设置", "DE.Views.PageSizeDialog.textTitle": "页面大小", "DE.Views.PageSizeDialog.textWidth": "宽度", "DE.Views.PageSizeDialog.txtCustom": "自定义", "DE.Views.PageThumbnails.textClosePanel": "关闭页面缩略图", "DE.Views.PageThumbnails.textHighlightVisiblePart": "高亮显示页面的可见部分", "DE.Views.PageThumbnails.textPageThumbnails": "页面缩略图", "DE.Views.PageThumbnails.textThumbnailsSettings": "缩略图设置", "DE.Views.PageThumbnails.textThumbnailsSize": "缩略图大小", "DE.Views.ParagraphSettings.strIndent": "缩进", "DE.Views.ParagraphSettings.strIndentsLeftText": "左", "DE.Views.ParagraphSettings.strIndentsRightText": "右", "DE.Views.ParagraphSettings.strIndentsSpecial": "特别", "DE.Views.ParagraphSettings.strLineHeight": "行间距", "DE.Views.ParagraphSettings.strParagraphSpacing": "段落间距", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "不要在相同样式的段落之间添加间隔", "DE.Views.ParagraphSettings.strSpacingAfter": "之后", "DE.Views.ParagraphSettings.strSpacingBefore": "之前", "DE.Views.ParagraphSettings.textAdvanced": "显示高级设置", "DE.Views.ParagraphSettings.textAt": "在", "DE.Views.ParagraphSettings.textAtLeast": "至少", "DE.Views.ParagraphSettings.textAuto": "倍数", "DE.Views.ParagraphSettings.textBackColor": "背景颜色", "DE.Views.ParagraphSettings.textExact": "精确地", "DE.Views.ParagraphSettings.textFirstLine": "第一行", "DE.Views.ParagraphSettings.textHanging": "悬挂", "DE.Views.ParagraphSettings.textNoneSpecial": "(无)", "DE.Views.ParagraphSettings.txtAutoText": "自动", "DE.Views.ParagraphSettingsAdvanced.noTabs": "指定的选项卡将显示在此字段中", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "全部大写", "DE.Views.ParagraphSettingsAdvanced.strBorders": "边框和填充", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "段前分页", "DE.Views.ParagraphSettingsAdvanced.strDirection": "方向", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "双删除线", "DE.Views.ParagraphSettingsAdvanced.strIndent": "缩进", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行间距", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "大纲级别", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "右", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "之后", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "之前", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特别", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "段中不分页", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "与下段同页", "DE.Views.ParagraphSettingsAdvanced.strMargins": "內距", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "孤行控制", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "字体 ", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "缩进和间距", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "换行符和分页符", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "放置", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小型大写字母", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "不要在相同样式的段落之间添加间隔", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "间距", "DE.Views.ParagraphSettingsAdvanced.strStrike": "删除线", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "下标", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "上标", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "禁止行号", "DE.Views.ParagraphSettingsAdvanced.strTabs": "标签", "DE.Views.ParagraphSettingsAdvanced.textAlign": "对齐", "DE.Views.ParagraphSettingsAdvanced.textAll": "全部", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "至少", "DE.Views.ParagraphSettingsAdvanced.textAuto": "倍数", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "背景颜色", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "基本文字", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "边框颜色", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "点击图表或使用按钮选择边框，并将选择的样式应用于它们", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "边框大小", "DE.Views.ParagraphSettingsAdvanced.textBottom": "底部", "DE.Views.ParagraphSettingsAdvanced.textCentered": "居中", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "字符间距", "DE.Views.ParagraphSettingsAdvanced.textContext": "上下文的", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "上下文和自行决定", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "上下文、历史和自由决定", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "上下文和历史", "DE.Views.ParagraphSettingsAdvanced.textDefault": "默认选项卡", "DE.Views.ParagraphSettingsAdvanced.textDirLtr": "从左到右", "DE.Views.ParagraphSettingsAdvanced.textDirRtl": "从右到左", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "任意的", "DE.Views.ParagraphSettingsAdvanced.textEffects": "效果", "DE.Views.ParagraphSettingsAdvanced.textExact": "精确地", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "第一行", "DE.Views.ParagraphSettingsAdvanced.textHanging": "悬挂", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "历史的", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "有根据的与随意的", "DE.Views.ParagraphSettingsAdvanced.textJustified": "两端对齐", "DE.Views.ParagraphSettingsAdvanced.textLeader": "领导", "DE.Views.ParagraphSettingsAdvanced.textLeft": "左", "DE.Views.ParagraphSettingsAdvanced.textLevel": "级别", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "连字", "DE.Views.ParagraphSettingsAdvanced.textNone": "无", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(无)", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "OpenType 功能", "DE.Views.ParagraphSettingsAdvanced.textPosition": "位置", "DE.Views.ParagraphSettingsAdvanced.textRemove": "删除", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "删除所有", "DE.Views.ParagraphSettingsAdvanced.textRight": "右", "DE.Views.ParagraphSettingsAdvanced.textSet": "指定", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "间距", "DE.Views.ParagraphSettingsAdvanced.textStandard": "仅限标准", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "标准与上下文", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "标准、情境和选择性", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "标准、情境和历史", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "标准和选择性", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "标准、历史和选择性", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "标准与历史", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "中心", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "左", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "标签的位置", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "右", "DE.Views.ParagraphSettingsAdvanced.textTitle": "段落 - 高级设置", "DE.Views.ParagraphSettingsAdvanced.textTop": "顶部", "DE.Views.ParagraphSettingsAdvanced.tipAll": "设置外边框和所有内框线", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "仅设置底部边框", "DE.Views.ParagraphSettingsAdvanced.tipInner": "仅设置水平内框线", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "仅设定内部框线", "DE.Views.ParagraphSettingsAdvanced.tipNone": "设置无边框", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "仅设定外部边框", "DE.Views.ParagraphSettingsAdvanced.tipRight": "仅设置右边框", "DE.Views.ParagraphSettingsAdvanced.tipTop": "仅设定上边框", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "自动", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "无边框", "DE.Views.PrintWithPreview.textMarginsLast": "上次自定义", "DE.Views.PrintWithPreview.textMarginsModerate": "中等", "DE.Views.PrintWithPreview.textMarginsNarrow": "狭窄", "DE.Views.PrintWithPreview.textMarginsNormal": "正常", "DE.Views.PrintWithPreview.textMarginsWide": "寬", "DE.Views.PrintWithPreview.txtAllPages": "所有页面", "DE.Views.PrintWithPreview.txtBothSides": "双面打印", "DE.Views.PrintWithPreview.txtBothSidesLongDesc": "长边翻页", "DE.Views.PrintWithPreview.txtBothSidesShortDesc": "短边翻页", "DE.Views.PrintWithPreview.txtBottom": "底部", "DE.Views.PrintWithPreview.txtCopies": "副本", "DE.Views.PrintWithPreview.txtCurrentPage": "当前页面", "DE.Views.PrintWithPreview.txtCustom": "自定义", "DE.Views.PrintWithPreview.txtCustomPages": "自定义打印", "DE.Views.PrintWithPreview.txtLandscape": "橫向", "DE.Views.PrintWithPreview.txtLeft": "左", "DE.Views.PrintWithPreview.txtMargins": "边距", "DE.Views.PrintWithPreview.txtOf": "共 {0} 页", "DE.Views.PrintWithPreview.txtOneSide": "单面打印", "DE.Views.PrintWithPreview.txtOneSideDesc": "只打印单面", "DE.Views.PrintWithPreview.txtPage": "页面", "DE.Views.PrintWithPreview.txtPageNumInvalid": "页码无效", "DE.Views.PrintWithPreview.txtPageOrientation": "页面方向", "DE.Views.PrintWithPreview.txtPages": "页面", "DE.Views.PrintWithPreview.txtPageSize": "页面大小", "DE.Views.PrintWithPreview.txtPortrait": "直向方向", "DE.Views.PrintWithPreview.txtPrint": "打印", "DE.Views.PrintWithPreview.txtPrintPdf": "打印为 PDF", "DE.Views.PrintWithPreview.txtPrintRange": "打印范围", "DE.Views.PrintWithPreview.txtPrintSides": "打印面", "DE.Views.PrintWithPreview.txtRight": "右", "DE.Views.PrintWithPreview.txtSelection": "选择", "DE.Views.PrintWithPreview.txtTop": "顶部", "DE.Views.ProtectDialog.textComments": "批注", "DE.Views.ProtectDialog.textForms": "填写表单", "DE.Views.ProtectDialog.textReview": "跟踪的更改", "DE.Views.ProtectDialog.textView": "不能更改（只读）", "DE.Views.ProtectDialog.txtAllow": "仅允许在文档中进行此类型的编辑", "DE.Views.ProtectDialog.txtIncorrectPwd": "确认密码不相同", "DE.Views.ProtectDialog.txtLimit": "密码限制为15个字符", "DE.Views.ProtectDialog.txtOptional": "可选的", "DE.Views.ProtectDialog.txtPassword": "密码", "DE.Views.ProtectDialog.txtProtect": "保护", "DE.Views.ProtectDialog.txtRepeat": "重复密码", "DE.Views.ProtectDialog.txtTitle": "保护", "DE.Views.ProtectDialog.txtWarning": "警告：如果您丢失或忘记了密码，则无法恢复。请把它放在安全的地方。", "DE.Views.RightMenu.ariaRightMenu": "右侧菜单", "DE.Views.RightMenu.txtChartSettings": "图表设置", "DE.Views.RightMenu.txtFormSettings": "表单设置", "DE.Views.RightMenu.txtHeaderFooterSettings": "页眉和页脚设置", "DE.Views.RightMenu.txtImageSettings": "图像设置", "DE.Views.RightMenu.txtMailMergeSettings": "邮件合并设置", "DE.Views.RightMenu.txtParagraphSettings": "段落设置", "DE.Views.RightMenu.txtShapeSettings": "形状设置", "DE.Views.RightMenu.txtSignatureSettings": "签名设置", "DE.Views.RightMenu.txtTableSettings": "表格设置", "DE.Views.RightMenu.txtTextArtSettings": "艺术字设置", "DE.Views.RoleDeleteDlg.textLabel": "若要删除此角色，您需要将与其关联的字段移动到另一个角色。", "DE.Views.RoleDeleteDlg.textSelect": "选择字段合并角色", "DE.Views.RoleDeleteDlg.textTitle": "刪除角色", "DE.Views.RoleEditDlg.errNameExists": "已存在该角色名称。", "DE.Views.RoleEditDlg.textEmptyError": "角色名称不能为空。", "DE.Views.RoleEditDlg.textName": "角色名称", "DE.Views.RoleEditDlg.textNameEx": "例如：申请人、客户、销售代表", "DE.Views.RoleEditDlg.textNoHighlight": "无高亮", "DE.Views.RoleEditDlg.txtTitleEdit": "编辑角色", "DE.Views.RoleEditDlg.txtTitleNew": "创建新角色", "DE.Views.RolesManagerDlg.textAnyone": "任何人", "DE.Views.RolesManagerDlg.textDelete": "刪除", "DE.Views.RolesManagerDlg.textDeleteLast": "是否确实要删除角色｛0｝吗？<br>删除后，将创建默认角色。", "DE.Views.RolesManagerDlg.textDescription": "添加角色并设置填写人接收和签署文档的顺序", "DE.Views.RolesManagerDlg.textDown": "向下移动角色", "DE.Views.RolesManagerDlg.textEdit": "编辑", "DE.Views.RolesManagerDlg.textEmpty": "尚未创建任何角色<br>至少创建一个角色，该角色将显示在此字段中。", "DE.Views.RolesManagerDlg.textNew": "新建", "DE.Views.RolesManagerDlg.textUp": "向上移动角色", "DE.Views.RolesManagerDlg.txtTitle": "管理角色", "DE.Views.RolesManagerDlg.warnCantDelete": "无法删除此角色，因为它具有关联的字段。", "DE.Views.RolesManagerDlg.warnDelete": "是否确实要删除角色｛0｝？", "DE.Views.SaveFormDlg.saveButtonText": "保存", "DE.Views.SaveFormDlg.textAnyone": "任何人", "DE.Views.SaveFormDlg.textDescription": "保存为PDF时，只有具有字段的角色会被添加到填写列表中", "DE.Views.SaveFormDlg.textEmpty": "没有与字段关联的角色。", "DE.Views.SaveFormDlg.textFill": "填写清单", "DE.Views.SaveFormDlg.txtTitle": "另存为表单", "DE.Views.ShapeSettings.strBackground": "背景颜色", "DE.Views.ShapeSettings.strChange": "更改形状", "DE.Views.ShapeSettings.strColor": "颜色", "DE.Views.ShapeSettings.strFill": "填入", "DE.Views.ShapeSettings.strForeground": "前景色", "DE.Views.ShapeSettings.strPattern": "模式", "DE.Views.ShapeSettings.strShadow": "显示阴影", "DE.Views.ShapeSettings.strSize": "粗细", "DE.Views.ShapeSettings.strStroke": "折线图", "DE.Views.ShapeSettings.strTransparency": "不透明度", "DE.Views.ShapeSettings.strType": "类型", "DE.Views.ShapeSettings.textAdjustShadow": "调整阴影", "DE.Views.ShapeSettings.textAdvanced": "显示高级设置", "DE.Views.ShapeSettings.textAngle": "角度", "DE.Views.ShapeSettings.textBorderSizeErr": "输入的值不正确。<br>请输入介于0 pt和1584 pt之间的值。", "DE.Views.ShapeSettings.textColor": "颜色填充", "DE.Views.ShapeSettings.textDirection": "方向", "DE.Views.ShapeSettings.textEditPoints": "编辑点", "DE.Views.ShapeSettings.textEditShape": "编辑形状", "DE.Views.ShapeSettings.textEmptyPattern": "无图案", "DE.Views.ShapeSettings.textEyedropper": "拾色器", "DE.Views.ShapeSettings.textFlip": "翻转", "DE.Views.ShapeSettings.textFromFile": "从文件导入", "DE.Views.ShapeSettings.textFromStorage": "来自存储设备", "DE.Views.ShapeSettings.textFromUrl": "来自URL", "DE.Views.ShapeSettings.textGradient": "渐变点", "DE.Views.ShapeSettings.textGradientFill": "渐变填充", "DE.Views.ShapeSettings.textHint270": "逆时针旋转90°", "DE.Views.ShapeSettings.textHint90": "顺时针旋转90°", "DE.Views.ShapeSettings.textHintFlipH": "水平翻转", "DE.Views.ShapeSettings.textHintFlipV": "垂直翻转", "DE.Views.ShapeSettings.textImageTexture": "图片或纹理", "DE.Views.ShapeSettings.textLinear": "线性", "DE.Views.ShapeSettings.textMoreColors": "更多颜色", "DE.Views.ShapeSettings.textNoFill": "无填充", "DE.Views.ShapeSettings.textNoShadow": "无阴影", "DE.Views.ShapeSettings.textPatternFill": "模式", "DE.Views.ShapeSettings.textPosition": "位置", "DE.Views.ShapeSettings.textRadial": "径向", "DE.Views.ShapeSettings.textRecentlyUsed": "最近使用的", "DE.Views.ShapeSettings.textRotate90": "旋转90°", "DE.Views.ShapeSettings.textRotation": "旋转", "DE.Views.ShapeSettings.textSelectImage": "选择图片", "DE.Views.ShapeSettings.textSelectTexture": "选择", "DE.Views.ShapeSettings.textShadow": "阴影", "DE.Views.ShapeSettings.textStretch": "延伸", "DE.Views.ShapeSettings.textStyle": "样式", "DE.Views.ShapeSettings.textTexture": "来自纹理", "DE.Views.ShapeSettings.textTile": "瓦", "DE.Views.ShapeSettings.textWrap": "环绕方式", "DE.Views.ShapeSettings.tipAddGradientPoint": "添加渐变点", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "删除渐变点", "DE.Views.ShapeSettings.txtBehind": "在文本后面", "DE.Views.ShapeSettings.txtBrownPaper": "牛皮纸", "DE.Views.ShapeSettings.txtCanvas": "画布", "DE.Views.ShapeSettings.txtCarton": "纸箱", "DE.Views.ShapeSettings.txtDarkFabric": "深色面料", "DE.Views.ShapeSettings.txtGrain": "纹理", "DE.Views.ShapeSettings.txtGranite": "花岗岩", "DE.Views.ShapeSettings.txtGreyPaper": "灰纸", "DE.Views.ShapeSettings.txtInFront": "在文字前面", "DE.Views.ShapeSettings.txtInline": "与文本对齐", "DE.Views.ShapeSettings.txtKnit": "针织", "DE.Views.ShapeSettings.txtLeather": "皮革", "DE.Views.ShapeSettings.txtNoBorders": "没有线", "DE.Views.ShapeSettings.txtPapyrus": "纸莎草", "DE.Views.ShapeSettings.txtSquare": "方形", "DE.Views.ShapeSettings.txtThrough": "通过", "DE.Views.ShapeSettings.txtTight": "紧", "DE.Views.ShapeSettings.txtTopAndBottom": "顶部和底部", "DE.Views.ShapeSettings.txtWood": "木頭", "DE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "DE.Views.SignatureSettings.strDelete": "删除签名", "DE.Views.SignatureSettings.strDetails": "签名详细信息", "DE.Views.SignatureSettings.strInvalid": "无效签名", "DE.Views.SignatureSettings.strRequested": "请求的签名", "DE.Views.SignatureSettings.strSetup": "签名设置", "DE.Views.SignatureSettings.strSign": "签署", "DE.Views.SignatureSettings.strSignature": "签名", "DE.Views.SignatureSettings.strSigner": "签名人", "DE.Views.SignatureSettings.strValid": "有效签名", "DE.Views.SignatureSettings.txtContinueEditing": "仍要編輯", "DE.Views.SignatureSettings.txtEditWarning": "编辑将删除文档中的签名<br>是否继续？", "DE.Views.SignatureSettings.txtRemoveWarning": "您想要移除此签名吗？<br>此操作无法撤销。", "DE.Views.SignatureSettings.txtRequestedSignatures": "此文件需要簽名。", "DE.Views.SignatureSettings.txtSigned": "有效签名已添加到文档中。文档受到保护，不可编辑。", "DE.Views.SignatureSettings.txtSignedInvalid": "文件中的一些数字签名无效或无法验证。该文件受到保护，无法编辑。", "DE.Views.Statusbar.goToPageText": "转到页面", "DE.Views.Statusbar.pageIndexText": "第{0}页共{1}页", "DE.Views.Statusbar.tipFitPage": "调整至页面大小", "DE.Views.Statusbar.tipFitWidth": "调整至宽度大小", "DE.Views.Statusbar.tipHandTool": "手动工具", "DE.Views.Statusbar.tipSelectTool": "选择工具", "DE.Views.Statusbar.tipSetLang": "設定文字語言", "DE.Views.Statusbar.tipZoomFactor": "縮放", "DE.Views.Statusbar.tipZoomIn": "放大", "DE.Views.Statusbar.tipZoomOut": "缩小", "DE.Views.Statusbar.txtPageNumInvalid": "页码无效", "DE.Views.Statusbar.txtPages": "页面", "DE.Views.Statusbar.txtParagraphs": "段落", "DE.Views.Statusbar.txtSpaces": "含空格的符号", "DE.Views.Statusbar.txtSymbols": "符号", "DE.Views.Statusbar.txtWordCount": "字数统计", "DE.Views.Statusbar.txtWords": "单词", "DE.Views.StyleTitleDialog.textHeader": "新建样式", "DE.Views.StyleTitleDialog.textNextStyle": "下一段样式", "DE.Views.StyleTitleDialog.textTitle": "标题", "DE.Views.StyleTitleDialog.txtEmpty": "这是必填栏", "DE.Views.StyleTitleDialog.txtNotEmpty": "字段不能为空", "DE.Views.StyleTitleDialog.txtSameAs": "与创建的新样式相同", "DE.Views.TableFormulaDialog.textBookmark": "粘贴书签", "DE.Views.TableFormulaDialog.textFormat": "数字格式", "DE.Views.TableFormulaDialog.textFormula": "公式", "DE.Views.TableFormulaDialog.textInsertFunction": "粘贴函数", "DE.Views.TableFormulaDialog.textTitle": "公式设置", "DE.Views.TableOfContentsSettings.strAlign": "页码右对齐", "DE.Views.TableOfContentsSettings.strFullCaption": "包括标签和编号", "DE.Views.TableOfContentsSettings.strLinks": "将目录设置为链接格式", "DE.Views.TableOfContentsSettings.strLinksOF": "将图表设置为链接格式", "DE.Views.TableOfContentsSettings.strShowPages": "显示页码", "DE.Views.TableOfContentsSettings.textBuildTable": "从中生成目录", "DE.Views.TableOfContentsSettings.textBuildTableOF": "从中构建数字表", "DE.Views.TableOfContentsSettings.textEquation": "方程式", "DE.Views.TableOfContentsSettings.textFigure": "图", "DE.Views.TableOfContentsSettings.textLeader": "领导", "DE.Views.TableOfContentsSettings.textLevel": "级别", "DE.Views.TableOfContentsSettings.textLevels": "层级", "DE.Views.TableOfContentsSettings.textNone": "无", "DE.Views.TableOfContentsSettings.textRadioCaption": "标题", "DE.Views.TableOfContentsSettings.textRadioLevels": "大纲级别", "DE.Views.TableOfContentsSettings.textRadioStyle": "样式", "DE.Views.TableOfContentsSettings.textRadioStyles": "选定的样式", "DE.Views.TableOfContentsSettings.textStyle": "样式", "DE.Views.TableOfContentsSettings.textStyles": "样式", "DE.Views.TableOfContentsSettings.textTable": "表格", "DE.Views.TableOfContentsSettings.textTitle": "目录", "DE.Views.TableOfContentsSettings.textTitleTOF": "图表目录", "DE.Views.TableOfContentsSettings.txtCentered": "居中", "DE.Views.TableOfContentsSettings.txtClassic": "经典", "DE.Views.TableOfContentsSettings.txtCurrent": "当前", "DE.Views.TableOfContentsSettings.txtDistinctive": "独特的", "DE.Views.TableOfContentsSettings.txtFormal": "正式", "DE.Views.TableOfContentsSettings.txtModern": "现代", "DE.Views.TableOfContentsSettings.txtOnline": "在线", "DE.Views.TableOfContentsSettings.txtSimple": "简单的", "DE.Views.TableOfContentsSettings.txtStandard": "标准", "DE.Views.TableSettings.deleteColumnText": "删除列", "DE.Views.TableSettings.deleteRowText": "删除行", "DE.Views.TableSettings.deleteTableText": "删除表格", "DE.Views.TableSettings.insertColumnLeftText": "向左插入列", "DE.Views.TableSettings.insertColumnRightText": "向右插入列", "DE.Views.TableSettings.insertRowAboveText": "在上方插入行", "DE.Views.TableSettings.insertRowBelowText": "在下方插入行", "DE.Views.TableSettings.mergeCellsText": "合并单元格", "DE.Views.TableSettings.selectCellText": "选择单元格", "DE.Views.TableSettings.selectColumnText": "选择列", "DE.Views.TableSettings.selectRowText": "选择行", "DE.Views.TableSettings.selectTableText": "选择表格", "DE.Views.TableSettings.splitCellsText": "拆分单元格", "DE.Views.TableSettings.splitCellTitleText": "拆分单元格", "DE.Views.TableSettings.strRepeatRow": "在每页顶部重复标题行", "DE.Views.TableSettings.textAddFormula": "添加公式", "DE.Views.TableSettings.textAdvanced": "显示高级设置", "DE.Views.TableSettings.textBackColor": "背景颜色", "DE.Views.TableSettings.textBanded": "镶边", "DE.Views.TableSettings.textBorderColor": "颜色", "DE.Views.TableSettings.textBorders": "边框样式", "DE.Views.TableSettings.textCellSize": "行和列的大小", "DE.Views.TableSettings.textColumns": "列", "DE.Views.TableSettings.textConvert": "把表格转换为文本", "DE.Views.TableSettings.textDistributeCols": "分布列", "DE.Views.TableSettings.textDistributeRows": "分布行", "DE.Views.TableSettings.textEdit": "行和列", "DE.Views.TableSettings.textEmptyTemplate": "没有模板", "DE.Views.TableSettings.textFirst": "第一", "DE.Views.TableSettings.textHeader": "页眉", "DE.Views.TableSettings.textHeight": "高度", "DE.Views.TableSettings.textLast": "最后", "DE.Views.TableSettings.textRows": "行", "DE.Views.TableSettings.textSelectBorders": "选择您要更改应用样式的边框", "DE.Views.TableSettings.textTemplate": "从模板中选择", "DE.Views.TableSettings.textTotal": "总计", "DE.Views.TableSettings.textWidth": "宽度", "DE.Views.TableSettings.tipAll": "设置外边框和所有内框线", "DE.Views.TableSettings.tipBottom": "仅设置外底边框", "DE.Views.TableSettings.tipInner": "仅设定内部框线", "DE.Views.TableSettings.tipInnerHor": "仅设置水平内框线", "DE.Views.TableSettings.tipInnerVert": "仅设置垂直内线", "DE.Views.TableSettings.tipLeft": "仅设置外部左边框", "DE.Views.TableSettings.tipNone": "设置无边框", "DE.Views.TableSettings.tipOuter": "仅设定外部边框", "DE.Views.TableSettings.tipRight": "仅设置右外边框", "DE.Views.TableSettings.tipTop": "仅设定外部顶框线", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "带边框和线条的表格", "DE.Views.TableSettings.txtGroupTable_Custom": "自定义", "DE.Views.TableSettings.txtGroupTable_Grid": "网格表", "DE.Views.TableSettings.txtGroupTable_List": "列表表格", "DE.Views.TableSettings.txtGroupTable_Plain": "普通表格", "DE.Views.TableSettings.txtNoBorders": "无边框", "DE.Views.TableSettings.txtTable_Accent": "重点色", "DE.Views.TableSettings.txtTable_Bordered": "有边框的", "DE.Views.TableSettings.txtTable_BorderedAndLined": "带边框和线条", "DE.Views.TableSettings.txtTable_Colorful": "多彩的", "DE.Views.TableSettings.txtTable_Dark": "深色", "DE.Views.TableSettings.txtTable_GridTable": "网格表", "DE.Views.TableSettings.txtTable_Light": "浅色", "DE.Views.TableSettings.txtTable_Lined": "有格线的", "DE.Views.TableSettings.txtTable_ListTable": "编目表", "DE.Views.TableSettings.txtTable_PlainTable": "普通表格", "DE.Views.TableSettings.txtTable_TableGrid": "表格网格", "DE.Views.TableSettingsAdvanced.textAlign": "对齐", "DE.Views.TableSettingsAdvanced.textAlignment": "对齐", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "单元格间距", "DE.Views.TableSettingsAdvanced.textAlt": "替代文本", "DE.Views.TableSettingsAdvanced.textAltDescription": "描述", "DE.Views.TableSettingsAdvanced.textAltTip": "视觉对象信息的另一种基于文本的表示方式，将读取给视力或认知障碍的人，以帮助他们更好地理解图像、形状、图表或表格中的信息。", "DE.Views.TableSettingsAdvanced.textAltTitle": "标题", "DE.Views.TableSettingsAdvanced.textAnchorText": "文本", "DE.Views.TableSettingsAdvanced.textAutofit": "自动调整大小以适应内容", "DE.Views.TableSettingsAdvanced.textBackColor": "单元格背景", "DE.Views.TableSettingsAdvanced.textBelow": "下面", "DE.Views.TableSettingsAdvanced.textBorderColor": "边框颜色", "DE.Views.TableSettingsAdvanced.textBorderDesc": "点击图表或使用按钮选择边框，并将选择的样式应用于它们", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "边框与背景", "DE.Views.TableSettingsAdvanced.textBorderWidth": "边框大小", "DE.Views.TableSettingsAdvanced.textBottom": "底部", "DE.Views.TableSettingsAdvanced.textCellOptions": "单元格选项", "DE.Views.TableSettingsAdvanced.textCellProps": "单元格", "DE.Views.TableSettingsAdvanced.textCellSize": "单元格大小", "DE.Views.TableSettingsAdvanced.textCenter": "中心", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "中心", "DE.Views.TableSettingsAdvanced.textCheckMargins": "使用默认页边距", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "默认的单元格边距", "DE.Views.TableSettingsAdvanced.textDistance": "与文本的间距", "DE.Views.TableSettingsAdvanced.textHorizontal": "水平的", "DE.Views.TableSettingsAdvanced.textIndLeft": "从左缩进", "DE.Views.TableSettingsAdvanced.textLeft": "左", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "左", "DE.Views.TableSettingsAdvanced.textMargin": "边距", "DE.Views.TableSettingsAdvanced.textMargins": "单元格边距", "DE.Views.TableSettingsAdvanced.textMeasure": "测量", "DE.Views.TableSettingsAdvanced.textMove": "移动带文本的对象", "DE.Views.TableSettingsAdvanced.textOnlyCells": "仅适用于选定的单元格", "DE.Views.TableSettingsAdvanced.textOptions": "选项", "DE.Views.TableSettingsAdvanced.textOverlap": "允许重叠", "DE.Views.TableSettingsAdvanced.textPage": "页面", "DE.Views.TableSettingsAdvanced.textPosition": "位置", "DE.Views.TableSettingsAdvanced.textPrefWidth": "首选宽度", "DE.Views.TableSettingsAdvanced.textPreview": "预览", "DE.Views.TableSettingsAdvanced.textRelative": "相对于", "DE.Views.TableSettingsAdvanced.textRight": "右", "DE.Views.TableSettingsAdvanced.textRightOf": "在 - 的右边", "DE.Views.TableSettingsAdvanced.textRightTooltip": "右", "DE.Views.TableSettingsAdvanced.textTable": "表格", "DE.Views.TableSettingsAdvanced.textTableBackColor": "表格背景", "DE.Views.TableSettingsAdvanced.textTablePosition": "表格位置", "DE.Views.TableSettingsAdvanced.textTableSize": "表格大小", "DE.Views.TableSettingsAdvanced.textTitle": "表格-高级设置", "DE.Views.TableSettingsAdvanced.textTop": "顶部", "DE.Views.TableSettingsAdvanced.textVertical": "垂直", "DE.Views.TableSettingsAdvanced.textWidth": "宽度", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "宽度和间距", "DE.Views.TableSettingsAdvanced.textWrap": "文本环绕", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "内联表", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "流程表", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "环绕方式", "DE.Views.TableSettingsAdvanced.textWrapText": "文字换行", "DE.Views.TableSettingsAdvanced.tipAll": "设置外边框和所有内框线", "DE.Views.TableSettingsAdvanced.tipCellAll": "仅为内部单元设置边框", "DE.Views.TableSettingsAdvanced.tipCellInner": "设置内部单元格的垂直和水平线", "DE.Views.TableSettingsAdvanced.tipCellOuter": "仅为内部单元格设定外边框", "DE.Views.TableSettingsAdvanced.tipInner": "仅设定内部框线", "DE.Views.TableSettingsAdvanced.tipNone": "设置无边框", "DE.Views.TableSettingsAdvanced.tipOuter": "仅设定外部边框", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "设置所有内部单元格的外部边框和边框", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "设置内部单元格的外部边界以及垂直线和水平线", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "设定表格的外框和内部储存单元格的外框", "DE.Views.TableSettingsAdvanced.txtCm": "厘米", "DE.Views.TableSettingsAdvanced.txtInch": "英寸", "DE.Views.TableSettingsAdvanced.txtNoBorders": "无边框", "DE.Views.TableSettingsAdvanced.txtPercent": "百分比", "DE.Views.TableSettingsAdvanced.txtPt": "点", "DE.Views.TableToTextDialog.textEmpty": "您必须为自定义分隔符键入一个字符。", "DE.Views.TableToTextDialog.textNested": "转换嵌套表", "DE.Views.TableToTextDialog.textOther": "其它", "DE.Views.TableToTextDialog.textPara": "段落标记", "DE.Views.TableToTextDialog.textSemicolon": "分号", "DE.Views.TableToTextDialog.textSeparator": "文本分隔符", "DE.Views.TableToTextDialog.textTab": "标签", "DE.Views.TableToTextDialog.textTitle": "把表格转换为文本", "DE.Views.TextArtSettings.strColor": "颜色", "DE.Views.TextArtSettings.strFill": "填入", "DE.Views.TextArtSettings.strSize": "粗细", "DE.Views.TextArtSettings.strStroke": "折线图", "DE.Views.TextArtSettings.strTransparency": "不透明度", "DE.Views.TextArtSettings.strType": "类型", "DE.Views.TextArtSettings.textAngle": "角度", "DE.Views.TextArtSettings.textBorderSizeErr": "输入的值不正确。<br>请输入介于0 pt和1584 pt之间的值。", "DE.Views.TextArtSettings.textColor": "颜色填充", "DE.Views.TextArtSettings.textDirection": "方向", "DE.Views.TextArtSettings.textGradient": "渐变点", "DE.Views.TextArtSettings.textGradientFill": "渐变填充", "DE.Views.TextArtSettings.textLinear": "线性", "DE.Views.TextArtSettings.textNoFill": "无填充", "DE.Views.TextArtSettings.textPosition": "位置", "DE.Views.TextArtSettings.textRadial": "径向", "DE.Views.TextArtSettings.textSelectTexture": "选择", "DE.Views.TextArtSettings.textStyle": "样式", "DE.Views.TextArtSettings.textTemplate": "模板", "DE.Views.TextArtSettings.textTransform": "变形", "DE.Views.TextArtSettings.tipAddGradientPoint": "添加渐变点", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "删除渐变点", "DE.Views.TextArtSettings.txtNoBorders": "没有线", "DE.Views.TextToTableDialog.textAutofit": "自动适应行为", "DE.Views.TextToTableDialog.textColumns": "列", "DE.Views.TextToTableDialog.textContents": "自动适应内容", "DE.Views.TextToTableDialog.textEmpty": "您必须为自定义分隔符键入一个字符。", "DE.Views.TextToTableDialog.textFixed": "固定列宽", "DE.Views.TextToTableDialog.textOther": "其它", "DE.Views.TextToTableDialog.textPara": "段落", "DE.Views.TextToTableDialog.textRows": "行", "DE.Views.TextToTableDialog.textSemicolon": "分号", "DE.Views.TextToTableDialog.textSeparator": "文本分隔于", "DE.Views.TextToTableDialog.textTab": "标签", "DE.Views.TextToTableDialog.textTableSize": "表格大小", "DE.Views.TextToTableDialog.textTitle": "把文本转换为表格", "DE.Views.TextToTableDialog.textWindow": "自动适应窗口", "DE.Views.TextToTableDialog.txtAutoText": "自动", "DE.Views.Toolbar.capBtnAddComment": "添加批注", "DE.Views.Toolbar.capBtnBlankPage": "空白页", "DE.Views.Toolbar.capBtnColumns": "列", "DE.Views.Toolbar.capBtnComment": "批注", "DE.Views.Toolbar.capBtnDateTime": "日期和时间", "DE.Views.Toolbar.capBtnHand": "手", "DE.Views.Toolbar.capBtnHyphenation": "连字符", "DE.Views.Toolbar.capBtnInsChart": "图表", "DE.Views.Toolbar.capBtnInsControls": "內容控制", "DE.Views.Toolbar.capBtnInsDropcap": "首字大写", "DE.Views.Toolbar.capBtnInsEquation": "方程式", "DE.Views.Toolbar.capBtnInsField": "域", "DE.Views.Toolbar.capBtnInsHeader": "页眉和页脚", "DE.Views.Toolbar.capBtnInsImage": "图片", "DE.Views.Toolbar.capBtnInsPagebreak": "换行符", "DE.Views.Toolbar.capBtnInsShape": "形状", "DE.Views.Toolbar.capBtnInsSmartArt": "智能图形", "DE.Views.Toolbar.capBtnInsSymbol": "符号", "DE.Views.Toolbar.capBtnInsTable": "表格", "DE.Views.Toolbar.capBtnInsTextart": "艺术字", "DE.Views.Toolbar.capBtnInsTextbox": "文本框", "DE.Views.Toolbar.capBtnInsTextFromFile": "来自文件的文本", "DE.Views.Toolbar.capBtnLineNumbers": "行号", "DE.Views.Toolbar.capBtnMargins": "边距", "DE.Views.Toolbar.capBtnPageColor": "页面颜色", "DE.Views.Toolbar.capBtnPageOrient": "方向", "DE.Views.Toolbar.capBtnPageSize": "大小", "DE.Views.Toolbar.capBtnSelect": "选择", "DE.Views.Toolbar.capBtnWatermark": "浮水印", "DE.Views.Toolbar.capColorScheme": "配色方案", "DE.Views.Toolbar.capImgAlign": "对齐", "DE.Views.Toolbar.capImgBackward": "下移一层", "DE.Views.Toolbar.capImgForward": "向前移动", "DE.Views.Toolbar.capImgGroup": "组", "DE.Views.Toolbar.capImgWrapping": "环绕", "DE.Views.Toolbar.capShapesMerge": "合并形状", "DE.Views.Toolbar.mniCapitalizeWords": "每个单词首字母大写", "DE.Views.Toolbar.mniCustomTable": "插入自定义表格", "DE.Views.Toolbar.mniDrawTable": "绘制表格", "DE.Views.Toolbar.mniEditControls": "控制设置", "DE.Views.Toolbar.mniEditDropCap": "首字下沉设置", "DE.Views.Toolbar.mniEditFooter": "编辑页脚", "DE.Views.Toolbar.mniEditHeader": "编辑页眉", "DE.Views.Toolbar.mniEraseTable": "刪除表格", "DE.Views.Toolbar.mniFromFile": "从文件", "DE.Views.Toolbar.mniFromStorage": "来自存储设备", "DE.Views.Toolbar.mniFromUrl": "来自URL", "DE.Views.Toolbar.mniHiddenBorders": "隐藏表格边框", "DE.Views.Toolbar.mniHiddenChars": "非打印字符", "DE.Views.Toolbar.mniHighlightControls": "高亮设置", "DE.Views.Toolbar.mniImageFromFile": "来自文件的图片", "DE.Views.Toolbar.mniImageFromStorage": "存储设备中的图片", "DE.Views.Toolbar.mniImageFromUrl": "来自URL地址的图片", "DE.Views.Toolbar.mniInsertSSE": "插入电子表格", "DE.Views.Toolbar.mniLowerCase": "小写", "DE.Views.Toolbar.mniRemoveFooter": "删除页脚", "DE.Views.Toolbar.mniRemoveHeader": "移除页眉", "DE.Views.Toolbar.mniSentenceCase": "句首字母大写", "DE.Views.Toolbar.mniTextFromLocalFile": "来自本地文件的文本", "DE.Views.Toolbar.mniTextFromStorage": "来自储存文件的文本", "DE.Views.Toolbar.mniTextFromURL": "来自URL文件的文本", "DE.Views.Toolbar.mniTextToTable": "把文本转换为表格", "DE.Views.Toolbar.mniToggleCase": "大小写转换", "DE.Views.Toolbar.mniUpperCase": "大写", "DE.Views.Toolbar.strMenuNoFill": "无填充", "DE.Views.Toolbar.textAddSpaceAfter": "增加段落后的空格", "DE.Views.Toolbar.textAddSpaceBefore": "增加段落前的空格", "DE.Views.Toolbar.textAlpha": "希腊文小字母阿尔法", "DE.Views.Toolbar.textAuto": "自动", "DE.Views.Toolbar.textAutoColor": "自动", "DE.Views.Toolbar.textBetta": "希腊文小字母贝塔", "DE.Views.Toolbar.textBlackHeart": "黑心", "DE.Views.Toolbar.textBold": "粗体", "DE.Views.Toolbar.textBottom": "底部：", "DE.Views.Toolbar.textBullet": "项目符号", "DE.Views.Toolbar.textChangeLevel": "更改列表级别", "DE.Views.Toolbar.textCheckboxControl": "复选框", "DE.Views.Toolbar.textColumnsCustom": "自定义列", "DE.Views.Toolbar.textColumnsLeft": "左", "DE.Views.Toolbar.textColumnsOne": "一", "DE.Views.Toolbar.textColumnsRight": "右", "DE.Views.Toolbar.textColumnsThree": "三", "DE.Views.Toolbar.textColumnsTwo": "二", "DE.Views.Toolbar.textComboboxControl": "下拉式方框", "DE.Views.Toolbar.textContinuous": "连续", "DE.Views.Toolbar.textContPage": "连续页", "DE.Views.Toolbar.textCopyright": "版权符号", "DE.Views.Toolbar.textCustomHyphen": "连字符选项", "DE.Views.Toolbar.textCustomLineNumbers": "线条编号选项", "DE.Views.Toolbar.textDateControl": "选择日期", "DE.Views.Toolbar.textDegree": "度数符号", "DE.Views.Toolbar.textDelta": "希腊文小字母得尔塔", "DE.Views.Toolbar.textDirLtr": "从左到右", "DE.Views.Toolbar.textDirRtl": "从右到左", "DE.Views.Toolbar.textDivision": "除号", "DE.Views.Toolbar.textDollar": "美元符号", "DE.Views.Toolbar.textDropdownControl": "下拉列表", "DE.Views.Toolbar.textEditMode": "编辑PDF", "DE.Views.Toolbar.textEditWatermark": "自定义水印", "DE.Views.Toolbar.textEuro": "欧元符号", "DE.Views.Toolbar.textEvenPage": "偶数页", "DE.Views.Toolbar.textGreaterEqual": "大于或等于", "DE.Views.Toolbar.textIndAfter": "段后缩进", "DE.Views.Toolbar.textIndBefore": "段前缩进", "DE.Views.Toolbar.textIndLeft": "左缩进", "DE.Views.Toolbar.textIndRight": "右缩进", "DE.Views.Toolbar.textInfinity": "无限", "DE.Views.Toolbar.textInMargin": "在页边距", "DE.Views.Toolbar.textInsColumnBreak": "插入分栏符", "DE.Views.Toolbar.textInsertPageCount": "插入页数", "DE.Views.Toolbar.textInsertPageNumber": "插入页码", "DE.Views.Toolbar.textInsPageBreak": "插入分页符", "DE.Views.Toolbar.textInsSectionBreak": "插入分节符", "DE.Views.Toolbar.textInText": "在文本中", "DE.Views.Toolbar.textItalic": "斜体", "DE.Views.Toolbar.textLandscape": "橫向", "DE.Views.Toolbar.textLeft": "左：", "DE.Views.Toolbar.textLessEqual": "小于或等于", "DE.Views.Toolbar.textLetterPi": "希腊文小字母 Pi", "DE.Views.Toolbar.textLineSpaceOptions": "行距参数", "DE.Views.Toolbar.textListSettings": "列表设置", "DE.Views.Toolbar.textMarginsLast": "最后一次自定义", "DE.Views.Toolbar.textMarginsModerate": "中等", "DE.Views.Toolbar.textMarginsNarrow": "狭窄", "DE.Views.Toolbar.textMarginsNormal": "正常", "DE.Views.Toolbar.textMarginsWide": "寬", "DE.Views.Toolbar.textMoreSymbols": "更多符号", "DE.Views.Toolbar.textNewColor": "更多顏色", "DE.Views.Toolbar.textNextPage": "下一页", "DE.Views.Toolbar.textNoHighlight": "无高亮", "DE.Views.Toolbar.textNone": "无", "DE.Views.Toolbar.textNotEqualTo": "不等于", "DE.Views.Toolbar.textOddPage": "奇数页", "DE.Views.Toolbar.textOneHalf": "普通分数一半", "DE.Views.Toolbar.textOneQuarter": "普通分数四分之一", "DE.Views.Toolbar.textPageMarginsCustom": "自定义边距", "DE.Views.Toolbar.textPageSizeCustom": "自定义页面大小", "DE.Views.Toolbar.textPictureControl": "图片", "DE.Views.Toolbar.textPlainControl": "纯文本", "DE.Views.Toolbar.textPlusMinus": "正负号", "DE.Views.Toolbar.textPortrait": "直向方向", "DE.Views.Toolbar.textRegistered": "注册标志", "DE.Views.Toolbar.textRemoveControl": "刪除內容控制", "DE.Views.Toolbar.textRemSpaceAfter": "删除段落后的空格", "DE.Views.Toolbar.textRemSpaceBefore": "删除段落前的空格", "DE.Views.Toolbar.textRemWatermark": "刪除水印", "DE.Views.Toolbar.textRestartEachPage": "重新启动每一页", "DE.Views.Toolbar.textRestartEachSection": "重新启动每一节", "DE.Views.Toolbar.textRichControl": "富文本", "DE.Views.Toolbar.textRight": "右: ", "DE.Views.Toolbar.textSection": "章节标志", "DE.Views.Toolbar.textShapesCombine": "组合", "DE.Views.Toolbar.textShapesFragment": "拆分", "DE.Views.Toolbar.textShapesIntersect": "相交", "DE.Views.Toolbar.textShapesSubstract": "剪除", "DE.Views.Toolbar.textShapesUnion": "结合", "DE.Views.Toolbar.textSmile": "白色笑脸", "DE.Views.Toolbar.textSpaceAfter": "段后间距", "DE.Views.Toolbar.textSpaceBefore": "段前间距", "DE.Views.Toolbar.textSquareRoot": "平方根", "DE.Views.Toolbar.textStrikeout": "删除线", "DE.Views.Toolbar.textStyleMenuDelete": "删除样式", "DE.Views.Toolbar.textStyleMenuDeleteAll": "删除所有自定义样式", "DE.Views.Toolbar.textStyleMenuNew": "所选内容中的新样式", "DE.Views.Toolbar.textStyleMenuRestore": "恢复为默认", "DE.Views.Toolbar.textStyleMenuRestoreAll": "全部恢复为默认样式", "DE.Views.Toolbar.textStyleMenuUpdate": "从选择更新", "DE.Views.Toolbar.textSubscript": "下标", "DE.Views.Toolbar.textSuperscript": "上标", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "抑制当前段落", "DE.Views.Toolbar.textTabCollaboration": "协作", "DE.Views.Toolbar.textTabDraw": "绘图", "DE.Views.Toolbar.textTabFile": "文件", "DE.Views.Toolbar.textTabHome": "开始", "DE.Views.Toolbar.textTabInsert": "插入", "DE.Views.Toolbar.textTabLayout": "布局", "DE.Views.Toolbar.textTabLinks": "引用", "DE.Views.Toolbar.textTabProtect": "保护", "DE.Views.Toolbar.textTabReview": "审阅", "DE.Views.Toolbar.textTabView": "视图", "DE.Views.Toolbar.textTilde": "波浪号", "DE.Views.Toolbar.textTitleError": "错误", "DE.Views.Toolbar.textToCurrent": "到当前位置", "DE.Views.Toolbar.textTop": "顶部: ", "DE.Views.Toolbar.textTradeMark": "商标标志", "DE.Views.Toolbar.textUnderline": "下划线", "DE.Views.Toolbar.textYen": "日元符号", "DE.Views.Toolbar.tipAlignCenter": "居中对齐", "DE.Views.Toolbar.tipAlignJust": "兩端對齊", "DE.Views.Toolbar.tipAlignLeft": "左对齐", "DE.Views.Toolbar.tipAlignRight": "右对齐", "DE.Views.Toolbar.tipBack": "返回", "DE.Views.Toolbar.tipBlankPage": "插入空白页", "DE.Views.Toolbar.tipChangeCase": "更改大小写", "DE.Views.Toolbar.tipChangeChart": "更改图表类型", "DE.Views.Toolbar.tipClearStyle": "清除样式", "DE.Views.Toolbar.tipColorSchemas": "更改配色方案", "DE.Views.Toolbar.tipColumns": "插入列", "DE.Views.Toolbar.tipControls": "插入內容控件", "DE.Views.Toolbar.tipCopy": "复制", "DE.Views.Toolbar.tipCopyStyle": "复制样式", "DE.Views.Toolbar.tipCut": "剪切", "DE.Views.Toolbar.tipDateTime": "插入当前日期和时间", "DE.Views.Toolbar.tipDecFont": "减小字体大小", "DE.Views.Toolbar.tipDecPrLeft": "减少缩进", "DE.Views.Toolbar.tipDownload": "下载文件", "DE.Views.Toolbar.tipDropCap": "插入首字下沉", "DE.Views.Toolbar.tipEditHeader": "编辑页眉或页脚", "DE.Views.Toolbar.tipEditMode": "编辑当前文件。<br>页面将重新加载。", "DE.Views.Toolbar.tipFontColor": "字体颜色", "DE.Views.Toolbar.tipFontName": "字体 ", "DE.Views.Toolbar.tipFontSize": "字体大小", "DE.Views.Toolbar.tipHandTool": "手动工具", "DE.Views.Toolbar.tipHighlightColor": "高亮色", "DE.Views.Toolbar.tipHyphenation": "更改连字符号", "DE.Views.Toolbar.tipImgAlign": "对齐对象", "DE.Views.Toolbar.tipImgGroup": "组对象", "DE.Views.Toolbar.tipImgWrapping": "环绕文字", "DE.Views.Toolbar.tipIncFont": "增加字体大小", "DE.Views.Toolbar.tipIncPrLeft": "增加缩进", "DE.Views.Toolbar.tipInsertChart": "插入图表", "DE.Views.Toolbar.tipInsertEquation": "插入方程", "DE.Views.Toolbar.tipInsertHorizontalText": "插入水平文本框", "DE.Views.Toolbar.tipInsertImage": "插入图片", "DE.Views.Toolbar.tipInsertNum": "插入页码", "DE.Views.Toolbar.tipInsertShape": "插入形狀", "DE.Views.Toolbar.tipInsertSmartArt": "插入智能图形", "DE.Views.Toolbar.tipInsertSymbol": "插入符号", "DE.Views.Toolbar.tipInsertTable": "插入表格", "DE.Views.Toolbar.tipInsertText": "插入文本框", "DE.Views.Toolbar.tipInsertTextArt": "插入艺术字", "DE.Views.Toolbar.tipInsertVerticalText": "插入垂直文本框", "DE.Views.Toolbar.tipInsField": "插入域代码", "DE.Views.Toolbar.tipLineNumbers": "显示行号", "DE.Views.Toolbar.tipLineSpace": "段落行距", "DE.Views.Toolbar.tipMailRecepients": "邮件合并", "DE.Views.Toolbar.tipMarkers": "项目符号", "DE.Views.Toolbar.tipMarkersArrow": "箭头项目符号", "DE.Views.Toolbar.tipMarkersCheckmark": "复选标记项目符号", "DE.Views.Toolbar.tipMarkersDash": "连字符项目符号", "DE.Views.Toolbar.tipMarkersFRhombus": "实心菱形项目符号", "DE.Views.Toolbar.tipMarkersFRound": "实心圆形项目符号", "DE.Views.Toolbar.tipMarkersFSquare": "实心方形项目符号", "DE.Views.Toolbar.tipMarkersHRound": "空心圆形项目符号", "DE.Views.Toolbar.tipMarkersStar": "星形项目符号", "DE.Views.Toolbar.tipMultiLevelArticl": "多级编号文章", "DE.Views.Toolbar.tipMultiLevelChapter": "多级编号章节", "DE.Views.Toolbar.tipMultiLevelHeadings": "多级编号标题", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "多级不同编号的标题", "DE.Views.Toolbar.tipMultiLevelNumbered": "多级编号项目符号", "DE.Views.Toolbar.tipMultilevels": "多级列表", "DE.Views.Toolbar.tipMultiLevelSymbols": "多级项目符号", "DE.Views.Toolbar.tipMultiLevelVarious": "多级各种编号", "DE.Views.Toolbar.tipNumbers": "编号", "DE.Views.Toolbar.tipPageBreak": "插入分页符或分节符", "DE.Views.Toolbar.tipPageColor": "更改页面颜色", "DE.Views.Toolbar.tipPageMargins": "页边距", "DE.Views.Toolbar.tipPageOrient": "页面方向", "DE.Views.Toolbar.tipPageSize": "页面大小", "DE.Views.Toolbar.tipParagraphStyle": "段落样式", "DE.Views.Toolbar.tipPaste": "粘贴", "DE.Views.Toolbar.tipPrColor": "阴影", "DE.Views.Toolbar.tipPrint": "打印", "DE.Views.Toolbar.tipPrintQuick": "快速打印", "DE.Views.Toolbar.tipRedo": "重做", "DE.Views.Toolbar.tipReplace": "替换", "DE.Views.Toolbar.tipSave": "保存", "DE.Views.Toolbar.tipSaveCoauth": "保存您的更改以供其他用户查看", "DE.Views.Toolbar.tipSelectAll": "全选", "DE.Views.Toolbar.tipSelectTool": "选择工具", "DE.Views.Toolbar.tipSendBackward": "下移一层", "DE.Views.Toolbar.tipSendForward": "向前移动", "DE.Views.Toolbar.tipShapesMerge": "合并形状", "DE.Views.Toolbar.tipShowHiddenChars": "非打印字符", "DE.Views.Toolbar.tipSynchronize": "该文档已被另一个用户更改。请点击保存更改并重新加载更新", "DE.Views.Toolbar.tipTextDir": "文本方向", "DE.Views.Toolbar.tipTextFromFile": "来自文件的文本", "DE.Views.Toolbar.tipUndo": "撤消", "DE.Views.Toolbar.tipWatermark": "编辑水印", "DE.Views.Toolbar.txtAutoText": "自动", "DE.Views.Toolbar.txtDistribHor": "水平分布", "DE.Views.Toolbar.txtDistribVert": "垂直分布", "DE.Views.Toolbar.txtGroupBulletDoc": "文档项目符号", "DE.Views.Toolbar.txtGroupBulletLib": "项目符号库", "DE.Views.Toolbar.txtGroupMultiDoc": "当前文档中的列表", "DE.Views.Toolbar.txtGroupMultiLib": "列表库", "DE.Views.Toolbar.txtGroupNumDoc": "文件编号格式", "DE.Views.Toolbar.txtGroupNumLib": "编号库", "DE.Views.Toolbar.txtGroupRecent": "最近使用的", "DE.Views.Toolbar.txtMarginAlign": "与边距对齐", "DE.Views.Toolbar.txtObjectsAlign": "对齐选定对象", "DE.Views.Toolbar.txtPageAlign": "与页面对齐", "DE.Views.ViewTab.textAlwaysShowToolbar": "始终显示工具栏", "DE.Views.ViewTab.textDarkDocument": "深色模式文档", "DE.Views.ViewTab.textFill": "填充", "DE.Views.ViewTab.textFitToPage": "调整至页面大小", "DE.Views.ViewTab.textFitToWidth": "调整至宽度大小", "DE.Views.ViewTab.textInterfaceTheme": "界面主题", "DE.Views.ViewTab.textLeftMenu": "左侧面板", "DE.Views.ViewTab.textLine": "线", "DE.Views.ViewTab.textMacros": "宏", "DE.Views.ViewTab.textNavigation": "导航", "DE.Views.ViewTab.textOutline": "标题", "DE.Views.ViewTab.textRightMenu": "右侧面板", "DE.Views.ViewTab.textRulers": "标尺", "DE.Views.ViewTab.textStatusBar": "状态栏", "DE.Views.ViewTab.textTabStyle": "选项卡样式", "DE.Views.ViewTab.textZoom": "縮放", "DE.Views.ViewTab.tipDarkDocument": "深色模式文档", "DE.Views.ViewTab.tipFitToPage": "调整至页面大小", "DE.Views.ViewTab.tipFitToWidth": "调整至宽度大小", "DE.Views.ViewTab.tipHeadings": "标题", "DE.Views.ViewTab.tipInterfaceTheme": "界面主题", "DE.Views.ViewTab.tipMacros": "宏", "DE.Views.WatermarkSettingsDialog.textAuto": "自动", "DE.Views.WatermarkSettingsDialog.textBold": "粗体", "DE.Views.WatermarkSettingsDialog.textColor": "文字颜色", "DE.Views.WatermarkSettingsDialog.textDiagonal": "对角线", "DE.Views.WatermarkSettingsDialog.textFont": "字体 ", "DE.Views.WatermarkSettingsDialog.textFromFile": "从文件", "DE.Views.WatermarkSettingsDialog.textFromStorage": "来自存储设备", "DE.Views.WatermarkSettingsDialog.textFromUrl": "来自URL", "DE.Views.WatermarkSettingsDialog.textHor": "水平的", "DE.Views.WatermarkSettingsDialog.textImageW": "图像水印", "DE.Views.WatermarkSettingsDialog.textItalic": "斜体", "DE.Views.WatermarkSettingsDialog.textLanguage": "语言", "DE.Views.WatermarkSettingsDialog.textLayout": "布局", "DE.Views.WatermarkSettingsDialog.textNone": "无", "DE.Views.WatermarkSettingsDialog.textScale": "尺寸", "DE.Views.WatermarkSettingsDialog.textSelect": "选择图像", "DE.Views.WatermarkSettingsDialog.textStrikeout": "删除线", "DE.Views.WatermarkSettingsDialog.textText": "文本", "DE.Views.WatermarkSettingsDialog.textTextW": "文字水印", "DE.Views.WatermarkSettingsDialog.textTitle": "浮水印設定", "DE.Views.WatermarkSettingsDialog.textTransparency": "半透明", "DE.Views.WatermarkSettingsDialog.textUnderline": "下划线", "DE.Views.WatermarkSettingsDialog.tipFontName": "字体名称", "DE.Views.WatermarkSettingsDialog.tipFontSize": "字体大小", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}