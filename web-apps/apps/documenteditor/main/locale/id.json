{"Common.Controllers.Chat.notcriticalErrorTitle": "Peringatan", "Common.Controllers.Desktop.hintBtnHome": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON> u<PERSON>a", "Common.Controllers.Desktop.itemCreateFromTemplate": "Buat dari templat", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "<PERSON><PERSON><PERSON> dinonakt<PERSON><PERSON> karena <PERSON>g diedit oleh pengguna lain.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Peringatan", "Common.Controllers.ExternalMergeEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.warningText": "<PERSON><PERSON><PERSON> dinonakt<PERSON><PERSON> karena <PERSON>g diedit oleh pengguna lain.", "Common.Controllers.ExternalMergeEditor.warningTitle": "Peringatan", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "<PERSON><PERSON>ek dinonaktif<PERSON> karena sedang disunting oleh pengguna lain.", "Common.Controllers.ExternalOleEditor.warningTitle": "Peringatan", "Common.Controllers.History.notcriticalErrorTitle": "Peringatan", "Common.Controllers.History.txtErrorLoadHistory": "Pemuatan riwayat gagal", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> ber<PERSON><PERSON> dipasang. Anda bisa mengakses semua plugin latarbelakang di sini.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "Untuk membandingkan dokumen, semua per<PERSON>han yang ada akan dianggap sudah disetujui. <PERSON><PERSON><PERSON><PERSON> Anda ingin melanjutkan?", "Common.Controllers.ReviewChanges.textAtLeast": "sekurang-kurangnya", "Common.Controllers.ReviewChanges.textAuto": "otomatis", "Common.Controllers.ReviewChanges.textBaseline": "Baseline", "Common.Controllers.ReviewChanges.textBold": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textBreakBefore": "<PERSON><PERSON> halaman sebelum", "Common.Controllers.ReviewChanges.textCaps": "<PERSON><PERSON><PERSON> caps", "Common.Controllers.ReviewChanges.textCenter": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textChar": "Level karakter", "Common.Controllers.ReviewChanges.textChart": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textColor": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textContextual": "<PERSON><PERSON> ta<PERSON> interval antar paragraf dengan model yang sama", "Common.Controllers.ReviewChanges.textDeleted": "<b><PERSON><PERSON><PERSON>:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "Garis coret ganda", "Common.Controllers.ReviewChanges.textEquation": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textExact": "persis", "Common.Controllers.ReviewChanges.textFirstLine": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textFontSize": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textFormatted": "Diformat", "Common.Controllers.ReviewChanges.textHighlight": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textImage": "Gambar", "Common.Controllers.ReviewChanges.textIndentLeft": "Indent kiri", "Common.Controllers.ReviewChanges.textIndentRight": "Indent kanan", "Common.Controllers.ReviewChanges.textInserted": "<b>Di<PERSON><PERSON><PERSON>:</b>", "Common.Controllers.ReviewChanges.textItalic": "Miring", "Common.Controllers.ReviewChanges.textJustify": "Rata kiri kanan", "Common.Controllers.ReviewChanges.textKeepLines": "<PERSON><PERSON><PERSON><PERSON> garis bersama", "Common.Controllers.ReviewChanges.textKeepNext": "Satukan dengan berikutnya", "Common.Controllers.ReviewChanges.textLeft": "Rata kiri", "Common.Controllers.ReviewChanges.textLineSpacing": "Spasi Antar Baris:", "Common.Controllers.ReviewChanges.textMultiple": "banyak", "Common.Controllers.ReviewChanges.textNoBreakBefore": "<PERSON><PERSON> break halaman sebelum", "Common.Controllers.ReviewChanges.textNoContextual": "Tambah jarak di antara", "Common.Controllers.ReviewChanges.textNoKeepLines": "<PERSON><PERSON> satukan garis", "Common.Controllers.ReviewChanges.textNoKeepNext": "<PERSON><PERSON> satukan dengan berik<PERSON>", "Common.Controllers.ReviewChanges.textNot": "Tidak ", "Common.Controllers.ReviewChanges.textNoWidow": "<PERSON><PERSON> kontrol widow", "Common.Controllers.ReviewChanges.textNum": "Ganti penomoran", "Common.Controllers.ReviewChanges.textOff": "{0} tidak lagi menggunakan Lacak Perubahan.", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} menonaktifkan Lacak Perubahan untuk semua orang.", "Common.Controllers.ReviewChanges.textOn": "{0} sedang mengg<PERSON>kan Lacak Perubahan.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} mengaktifkan Lacak Perubahan untuk semua orang.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b><PERSON><PERSON><PERSON></b>", "Common.Controllers.ReviewChanges.textParaFormatted": "Paragraf terformat", "Common.Controllers.ReviewChanges.textParaInserted": "<b><PERSON><PERSON><PERSON> di<PERSON></b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>Dipindah turun:</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b><PERSON><PERSON><PERSON> naik:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>Dipindah:</b>", "Common.Controllers.ReviewChanges.textPosition": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textRight": "<PERSON>a kanan", "Common.Controllers.ReviewChanges.textShape": "Bentuk", "Common.Controllers.ReviewChanges.textShd": "<PERSON><PERSON> la<PERSON>", "Common.Controllers.ReviewChanges.textShow": "<PERSON><PERSON><PERSON><PERSON> pada", "Common.Controllers.ReviewChanges.textSmallCaps": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textSpacing": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textSpacingAfter": "Spacing setelah", "Common.Controllers.ReviewChanges.textSpacingBefore": "Spacing sebelum", "Common.Controllers.ReviewChanges.textStrikeout": "<PERSON><PERSON> ganda", "Common.Controllers.ReviewChanges.textSubScript": "Subskrip", "Common.Controllers.ReviewChanges.textSuperScript": "Superskrip", "Common.Controllers.ReviewChanges.textTableChanged": "<b>Pengaturan tabel diubah</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b><PERSON>s tabel ditambah</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b><PERSON><PERSON> tabel di<PERSON>pus</b>", "Common.Controllers.ReviewChanges.textTabs": "Ganti tab", "Common.Controllers.ReviewChanges.textTitleComparison": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>n", "Common.Controllers.ReviewChanges.textUnderline": "<PERSON><PERSON> bawah", "Common.Controllers.ReviewChanges.textUrl": "Paste URL dokumen", "Common.Controllers.ReviewChanges.textWidow": "<PERSON><PERSON><PERSON> widow", "Common.Controllers.ReviewChanges.textWord": "Level word", "Common.define.chartData.textArea": "Area", "Common.define.chartData.textAreaStacked": "Area yang ditumpuk", "Common.define.chartData.textAreaStackedPer": "Area bertumpuk 100%", "Common.define.chartData.textBar": "Palang", "Common.define.chartData.textBarNormal": "<PERSON><PERSON> kolom klaster", "Common.define.chartData.textBarNormal3d": "Kolom cluster 3-D", "Common.define.chartData.textBarNormal3dPerspective": "Kolom 3-D", "Common.define.chartData.textBarStacked": "Diagram kolom bert<PERSON>", "Common.define.chartData.textBarStacked3d": "Kolom bertumpuk 3-D", "Common.define.chartData.textBarStackedPer": "<PERSON><PERSON><PERSON> 100%", "Common.define.chartData.textBarStackedPer3d": "<PERSON><PERSON><PERSON> 100% 3-D", "Common.define.chartData.textCharts": "Bagan", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Area yang ditumpuk - kolom klaster", "Common.define.chartData.textComboBarLine": "<PERSON><PERSON> kolom klaster - garis", "Common.define.chartData.textComboBarLineSecondary": "<PERSON><PERSON> kolom klaster - garis pada sumbu sekunder", "Common.define.chartData.textComboCustom": "Custom kombinasi", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "<PERSON><PERSON> batang klaster", "Common.define.chartData.textHBarNormal3d": "Diagram Batang Cluster 3-D", "Common.define.chartData.textHBarStacked": "Diagram batang bertumpuk", "Common.define.chartData.textHBarStacked3d": "Batang bertumpuk 3-D", "Common.define.chartData.textHBarStackedPer": "Batang bertumpuk 100%", "Common.define.chartData.textHBarStackedPer3d": "Batang bertumpuk 100% 3-D", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "Garis 3-D", "Common.define.chartData.textLineMarker": "<PERSON><PERSON> dengan tanda", "Common.define.chartData.textLineStacked": "Diagram garis bert<PERSON>uk", "Common.define.chartData.textLineStackedMarker": "Diagram garis bert<PERSON>uk dengan marker", "Common.define.chartData.textLineStackedPer": "<PERSON><PERSON> be<PERSON> 100%", "Common.define.chartData.textLineStackedPerMarker": "<PERSON><PERSON> be<PERSON> 100% dengan penanda", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textPie3d": "Pie 3-D", "Common.define.chartData.textPoint": "XY (<PERSON><PERSON><PERSON>)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Radar terisi", "Common.define.chartData.textRadarMarker": "<PERSON> dengan penanda", "Common.define.chartData.textScatter": "Sebar", "Common.define.chartData.textScatterLine": "Diagram sebar dengan garis lurus", "Common.define.chartData.textScatterLineMarker": "Diagram sebar dengan garis lurus dan marker", "Common.define.chartData.textScatterSmooth": "Diagram sebar dengan garis mulus", "Common.define.chartData.textScatterSmoothMarker": "Diagram sebar dengan garis mulus dan marker", "Common.define.chartData.textStock": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textSurface": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textAccentedPicture": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textAccentProcess": "<PERSON><PERSON> aksen", "Common.define.smartArt.textAlternatingFlow": "<PERSON><PERSON>", "Common.define.smartArt.textAlternatingHexagons": "<PERSON><PERSON>", "Common.define.smartArt.textAlternatingPictureBlocks": "Blok gambar bergantian", "Common.define.smartArt.textAlternatingPictureCircles": "Lingkaran gambar bergantian", "Common.define.smartArt.textArchitectureLayout": "Tata letak arsitektur", "Common.define.smartArt.textArrowRibbon": "<PERSON>a anak panah", "Common.define.smartArt.textAscendingPictureAccentProcess": "Proses <PERSON><PERSON><PERSON>", "Common.define.smartArt.textBalance": "Seimbang", "Common.define.smartArt.textBasicBendingProcess": "<PERSON><PERSON> <PERSON>", "Common.define.smartArt.textBasicBlockList": "Daftar blok dasar", "Common.define.smartArt.textBasicChevronProcess": "<PERSON>ses <PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicPie": "<PERSON><PERSON>", "Common.define.smartArt.textBasicProcess": "<PERSON><PERSON> dasar", "Common.define.smartArt.textBasicPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicRadial": "<PERSON><PERSON>", "Common.define.smartArt.textBasicTarget": "Target dasar", "Common.define.smartArt.textBasicTimeline": "<PERSON><PERSON>", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Daftar Akses <PERSON>", "Common.define.smartArt.textBendingPictureBlocks": "Blok Gambar Meliuk", "Common.define.smartArt.textBendingPictureCaption": "Keterangan Gambar Meliuk", "Common.define.smartArt.textBendingPictureCaptionList": "Daftar Keterangan Gambar Meliuk", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Teks Semi-Transparan G<PERSON>", "Common.define.smartArt.textBlockCycle": "Lingkaran Blok", "Common.define.smartArt.textBubblePictureList": "Daftar Gambar Gelembung", "Common.define.smartArt.textCaptionedPictures": "Gambar Dengan Keterangan", "Common.define.smartArt.textChevronAccentProcess": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Common.define.smartArt.textChevronList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textCircleAccentTimeline": "<PERSON><PERSON>", "Common.define.smartArt.textCircleArrowProcess": "Proses <PERSON><PERSON>", "Common.define.smartArt.textCirclePictureHierarchy": "<PERSON>era<PERSON><PERSON>", "Common.define.smartArt.textCircleProcess": "<PERSON><PERSON>", "Common.define.smartArt.textCircleRelationship": "Hubungan <PERSON>", "Common.define.smartArt.textCircularBendingProcess": "Proses pembengkokan me<PERSON>kar", "Common.define.smartArt.textCircularPictureCallout": "Panggila<PERSON>", "Common.define.smartArt.textClosedChevronProcess": "Proses Ch<PERSON>ron <PERSON>", "Common.define.smartArt.textContinuousArrowProcess": "Proses <PERSON><PERSON>", "Common.define.smartArt.textContinuousBlockProcess": "Proses Blok Berkelanjutan", "Common.define.smartArt.textContinuousCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textContinuousPictureList": "Daftar Gambar Berkelanjutan", "Common.define.smartArt.textConvergingArrows": "Panah <PERSON>at", "Common.define.smartArt.textConvergingRadial": "Radial Memusat", "Common.define.smartArt.textConvergingText": "Teks Memusat", "Common.define.smartArt.textCounterbalanceArrows": "Panah <PERSON>", "Common.define.smartArt.textCycle": "Siklus", "Common.define.smartArt.textCycleMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textDescendingBlockList": "Daftar Blok Turun", "Common.define.smartArt.textDescendingProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDetailedProcess": "<PERSON><PERSON> terperi<PERSON>i", "Common.define.smartArt.textDivergingArrows": "Panah <PERSON>", "Common.define.smartArt.textDivergingRadial": "<PERSON><PERSON>", "Common.define.smartArt.textEquation": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textFramedTextPicture": "Gambar Teks Terbingkai", "Common.define.smartArt.textFunnel": "Corong", "Common.define.smartArt.textGear": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textGridMatrix": "<PERSON><PERSON><PERSON> kisi", "Common.define.smartArt.textGroupedList": "Daftar yang <PERSON>", "Common.define.smartArt.textHalfCircleOrganizationChart": "Bagan Organisasi Seteng<PERSON>n", "Common.define.smartArt.textHexagonCluster": "Kluster Segi Enam", "Common.define.smartArt.textHexagonRadial": "<PERSON><PERSON>", "Common.define.smartArt.textHierarchy": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textHierarchyList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textHorizontalBulletList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textHorizontalHierarchy": "<PERSON>rar<PERSON> horizontal", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Hierarki Be<PERSON>abe<PERSON>", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Hierarki Multi-Level Horizontal", "Common.define.smartArt.textHorizontalOrganizationChart": "Bagan Organisasi Horizontal", "Common.define.smartArt.textHorizontalPictureList": "Daftar gambar horizontal", "Common.define.smartArt.textIncreasingArrowProcess": "Proses <PERSON><PERSON>", "Common.define.smartArt.textIncreasingCircleProcess": "Proses <PERSON><PERSON> Meningkat", "Common.define.smartArt.textInterconnectedBlockProcess": "Proses Blok yang <PERSON>ing Terhubung", "Common.define.smartArt.textInterconnectedRings": "<PERSON><PERSON><PERSON> yang <PERSON>hu<PERSON>", "Common.define.smartArt.textInvertedPyramid": "<PERSON><PERSON><PERSON> te<PERSON>", "Common.define.smartArt.textLabeledHierarchy": "<PERSON>era<PERSON><PERSON>", "Common.define.smartArt.textLinearVenn": "Venn Linear", "Common.define.smartArt.textLinedList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Bagan <PERSON> dan <PERSON>", "Common.define.smartArt.textNestedTarget": "Target Bertumpuk", "Common.define.smartArt.textNondirectionalCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textOpposingArrows": "<PERSON><PERSON>", "Common.define.smartArt.textOpposingIdeas": "<PERSON><PERSON>", "Common.define.smartArt.textOrganizationChart": "Bagan Organisasi", "Common.define.smartArt.textOther": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textPhasedProcess": "Proses Berf<PERSON>", "Common.define.smartArt.textPicture": "Gambar", "Common.define.smartArt.textPictureAccentBlocks": "Blok Aksen Gambar", "Common.define.smartArt.textPictureAccentList": "Daftar Aksen G<PERSON>bar", "Common.define.smartArt.textPictureAccentProcess": "Proses Aksen <PERSON>", "Common.define.smartArt.textPictureCaptionList": "Daftar Keterangan Gambar", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "<PERSON><PERSON> gambar", "Common.define.smartArt.textPictureLineup": "Deretan Gambar", "Common.define.smartArt.textPictureOrganizationChart": "Bagan Organisasi Gambar", "Common.define.smartArt.textPictureStrips": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textPieProcess": "Proses <PERSON>", "Common.define.smartArt.textPlusAndMinus": "Plus dan minus", "Common.define.smartArt.textProcess": "Proses", "Common.define.smartArt.textProcessArrows": "<PERSON><PERSON>s", "Common.define.smartArt.textProcessList": "<PERSON><PERSON><PERSON> proses", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "<PERSON><PERSON><PERSON> pira<PERSON>da", "Common.define.smartArt.textRadialCluster": "Kluster Radial", "Common.define.smartArt.textRadialCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRadialList": "<PERSON>ftar Radial", "Common.define.smartArt.textRadialPictureList": "<PERSON>ftar Gambar Radial", "Common.define.smartArt.textRadialVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRandomToResultProcess": "Proses Acak ke Hasil", "Common.define.smartArt.textRelationship": "Hubungan", "Common.define.smartArt.textRepeatingBendingProcess": "Proses <PERSON><PERSON>", "Common.define.smartArt.textReverseList": "Daftar Terbalik", "Common.define.smartArt.textSegmentedCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textSegmentedProcess": "<PERSON>ses <PERSON>", "Common.define.smartArt.textSegmentedPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textSnapshotPictureList": "Daftar Gambar Snapshot", "Common.define.smartArt.textSpiralPicture": "Gambar S<PERSON>ral", "Common.define.smartArt.textSquareAccentList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStackedList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "<PERSON><PERSON>", "Common.define.smartArt.textStepDownProcess": "<PERSON><PERSON>", "Common.define.smartArt.textStepUpProcess": "Proses Meningkat", "Common.define.smartArt.textSubStepProcess": "Proses Sub-Langkah", "Common.define.smartArt.textTabbedArc": "<PERSON><PERSON>", "Common.define.smartArt.textTableHierarchy": "<PERSON><PERSON><PERSON> tabel", "Common.define.smartArt.textTableList": "<PERSON><PERSON><PERSON> tabel", "Common.define.smartArt.textTabList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textTargetList": "Daftar Target", "Common.define.smartArt.textTextCycle": "Siklus teks", "Common.define.smartArt.textThemePictureAccent": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textThemePictureAlternatingAccent": "<PERSON><PERSON><PERSON>-Balik Gambar Te<PERSON>", "Common.define.smartArt.textThemePictureGrid": "<PERSON><PERSON>", "Common.define.smartArt.textTitledMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textTitledPictureAccentList": "Daftar Aksen Gambar Be<PERSON>", "Common.define.smartArt.textTitledPictureBlocks": "Blok Gambar Berjudul", "Common.define.smartArt.textTitlePictureLineup": "Deretan <PERSON>", "Common.define.smartArt.textTrapezoidList": "Daftar Trapesium", "Common.define.smartArt.textUpwardArrow": "<PERSON>ah ke atas", "Common.define.smartArt.textVaryingWidthList": "<PERSON><PERSON><PERSON> den<PERSON>", "Common.define.smartArt.textVerticalAccentList": "Daftar Aksen Vertikal", "Common.define.smartArt.textVerticalArrowList": "<PERSON>ftar Panah Vertikal", "Common.define.smartArt.textVerticalBendingProcess": "Arah Proses Vertikal", "Common.define.smartArt.textVerticalBlockList": "Daftar blok vertikal", "Common.define.smartArt.textVerticalBoxList": "Daftar kotak vertikal", "Common.define.smartArt.textVerticalBracketList": "Daftar Tanda Kurung Vertikal", "Common.define.smartArt.textVerticalBulletList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalChevronList": "<PERSON><PERSON><PERSON>ev<PERSON>", "Common.define.smartArt.textVerticalCircleList": "Daftar lingkaran vertikal", "Common.define.smartArt.textVerticalCurvedList": "Daftar Kurva Vertikal", "Common.define.smartArt.textVerticalEquation": "<PERSON><PERSON><PERSON><PERSON> vertikal", "Common.define.smartArt.textVerticalPictureAccentList": "Daftar Aksen Gambar Vertikal", "Common.define.smartArt.textVerticalPictureList": "Daftar Gambar Vertikal", "Common.define.smartArt.textVerticalProcess": "Proses vertikal", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON>", "Common.Translation.tipFileLocked": "Dokumen terkunci untuk diedit. <PERSON>a dapat membuat perubahan dan menyimpannya sebagai salinan lokal nanti.", "Common.Translation.tipFileReadOnly": "Dokumen hanya dapat dibaca. Untuk menyimpan perubahan <PERSON>, simpan file dengan nama baru atau di tempat lain.", "Common.Translation.warnFileLocked": "<PERSON>a tidak bisa edit file ini karena sedang di edit di aplikasi lain.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON>t salinan", "Common.Translation.warnFileLockedBtnView": "<PERSON>uka untuk dilihat", "Common.UI.ButtonColored.textAutoColor": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ButtonColored.textEyedropper": "<PERSON><PERSON> warna", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON> banyak warna", "Common.UI.Calendar.textApril": "April", "Common.UI.Calendar.textAugust": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textDecember": "Desember", "Common.UI.Calendar.textFebruary": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textJanuary": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textJuly": "<PERSON><PERSON>", "Common.UI.Calendar.textJune": "<PERSON><PERSON>", "Common.UI.Calendar.textMarch": "<PERSON><PERSON>", "Common.UI.Calendar.textMay": "<PERSON>", "Common.UI.Calendar.textMonths": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textNovember": "November", "Common.UI.Calendar.textOctober": "Oktober", "Common.UI.Calendar.textSeptember": "September", "Common.UI.Calendar.textShortApril": "Apr", "Common.UI.Calendar.textShortAugust": "Ags", "Common.UI.Calendar.textShortDecember": "Des", "Common.UI.Calendar.textShortFebruary": "Feb", "Common.UI.Calendar.textShortFriday": "<PERSON><PERSON>", "Common.UI.Calendar.textShortJanuary": "Jan", "Common.UI.Calendar.textShortJuly": "Jul", "Common.UI.Calendar.textShortJune": "Jun", "Common.UI.Calendar.textShortMarch": "Mar", "Common.UI.Calendar.textShortMay": "<PERSON>", "Common.UI.Calendar.textShortMonday": "<PERSON>", "Common.UI.Calendar.textShortNovember": "Nov", "Common.UI.Calendar.textShortOctober": "Okt", "Common.UI.Calendar.textShortSaturday": "<PERSON>b", "Common.UI.Calendar.textShortSeptember": "Sep", "Common.UI.Calendar.textShortSunday": "Min", "Common.UI.Calendar.textShortThursday": "<PERSON>", "Common.UI.Calendar.textShortTuesday": "<PERSON>l", "Common.UI.Calendar.textShortWednesday": "<PERSON><PERSON>", "Common.UI.Calendar.textYears": "<PERSON><PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "Tidak ada pembatas", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Tidak ada pembatas", "Common.UI.ComboDataView.emptyComboText": "Tidak ada model", "Common.UI.ExtendedColorDialog.addButtonText": "Tambahkan", "Common.UI.ExtendedColorDialog.textCurrent": "Saat ini", "Common.UI.ExtendedColorDialog.textHexErr": "Input yang dimasukkan salah.<br><PERSON><PERSON><PERSON> masukkan input antara 000000 dan FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Input yang <PERSON>a masukkan salah.<br><PERSON><PERSON><PERSON> masukkan input numerik antara 0 dan 255.", "Common.UI.HSBColorPicker.textNoColor": "Tidak ada warna", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnCalendar.textDate": "<PERSON><PERSON><PERSON> tanggal", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Sembunyikan password", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON><PERSON><PERSON> password", "Common.UI.SearchBar.textFind": "Temukan", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "<PERSON><PERSON>", "Common.UI.SearchBar.tipOpenAdvancedSettings": "<PERSON><PERSON> pengaturan lan<PERSON>", "Common.UI.SearchBar.tipPreviousResult": "<PERSON><PERSON>", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON> hasil", "Common.UI.SearchDialog.textMatchCase": "Harus sama persis", "Common.UI.SearchDialog.textReplaceDef": "Tuliskan teks pengganti", "Common.UI.SearchDialog.textSearchStart": "Tuliskan teks Anda di sini", "Common.UI.SearchDialog.textTitle": "<PERSON>i dan ganti", "Common.UI.SearchDialog.textTitle2": "Temukan", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON><PERSON><PERSON> kata saja", "Common.UI.SearchDialog.txtBtnHideReplace": "Sembunyikan Replace", "Common.UI.SearchDialog.txtBtnReplace": "Ganti", "Common.UI.SearchDialog.txtBtnReplaceAll": "Ganti semua", "Common.UI.SynchronizeTip.textDontShow": "<PERSON>an tampilkan pesan ini lagi", "Common.UI.SynchronizeTip.textGotIt": "<PERSON><PERSON><PERSON>", "Common.UI.SynchronizeTip.textSynchronize": "Dokumen telah diubah oleh pengguna lain.<br><PERSON>lakan klik untuk menyimpan perubahan dan memuat ulang pembaruan.", "Common.UI.ThemeColorPalette.textRecentColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON>ar", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON> tema", "Common.UI.ThemeColorPalette.textTransparent": "Transparan", "Common.UI.Themes.txtThemeClassicLight": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeContrastDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Sama seperti sistem", "Common.UI.Window.cancelButtonText": "Batalkan", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Tidak", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "<PERSON>an tampilkan pesan ini lagi", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "Informasi", "Common.UI.Window.textWarning": "Peringatan", "Common.UI.Window.yesButtonText": "Ya", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "<PERSON><PERSON> be<PERSON>", "Common.Utils.ThemeColor.txtBlack": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtBlue": "Biru", "Common.Utils.ThemeColor.txtBrightGreen": "<PERSON><PERSON><PERSON> terang", "Common.Utils.ThemeColor.txtBrown": "Coklat", "Common.Utils.ThemeColor.txtDarkBlue": "<PERSON>iru gelap", "Common.Utils.ThemeColor.txtDarker": "<PERSON><PERSON><PERSON> gelap", "Common.Utils.ThemeColor.txtDarkGray": "<PERSON><PERSON><PERSON> gelap", "Common.Utils.ThemeColor.txtDarkGreen": "<PERSON><PERSON><PERSON> tua", "Common.Utils.ThemeColor.txtDarkPurple": "<PERSON>gu tua", "Common.Utils.ThemeColor.txtDarkRed": "<PERSON><PERSON> tua", "Common.Utils.ThemeColor.txtDarkTeal": "Hijau kebiruan tua", "Common.Utils.ThemeColor.txtDarkYellow": "<PERSON><PERSON> tua", "Common.Utils.ThemeColor.txtGold": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtGray": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtGreen": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "<PERSON><PERSON>u muda", "Common.Utils.ThemeColor.txtLighter": "<PERSON><PERSON><PERSON> terang", "Common.Utils.ThemeColor.txtLightGray": "<PERSON><PERSON><PERSON>a", "Common.Utils.ThemeColor.txtLightGreen": "<PERSON><PERSON><PERSON> muda", "Common.Utils.ThemeColor.txtLightOrange": "Jingga terang", "Common.Utils.ThemeColor.txtLightYellow": "<PERSON><PERSON> muda", "Common.Utils.ThemeColor.txtOrange": "Jingga", "Common.Utils.ThemeColor.txtPink": "<PERSON><PERSON> muda", "Common.Utils.ThemeColor.txtPurple": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtRed": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtRose": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "<PERSON><PERSON><PERSON> langit", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Teks", "Common.Utils.ThemeColor.txtTurquosie": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtViolet": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtWhite": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtYellow": "<PERSON><PERSON>", "Common.Views.About.txtAddress": "alamat: ", "Common.Views.About.txtLicensee": "PEMEGANG LISENSI", "Common.Views.About.txtLicensor": "PEMBERI LISENSI", "Common.Views.About.txtMail": "surel: ", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel: ", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON> ", "Common.Views.AutoCorrectDialog.textAdd": "Tambahkan", "Common.Views.AutoCorrectDialog.textApplyText": "Terapkan sambil Anda menulis", "Common.Views.AutoCorrectDialog.textAutoCorrect": "AutoKoreksi Teks", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat sambil Anda mengetik", "Common.Views.AutoCorrectDialog.textBulleted": "Butir list otomatis", "Common.Views.AutoCorrectDialog.textBy": "oleh", "Common.Views.AutoCorrectDialog.textDelete": "Hapus", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Tambahkan titik dengan spasi ganda", "Common.Views.AutoCorrectDialog.textFLCells": "Besarkan huruf pertama di sel tabel", "Common.Views.AutoCorrectDialog.textFLDont": "<PERSON><PERSON> ka<PERSON>an set<PERSON>", "Common.Views.AutoCorrectDialog.textFLSentence": "Besarkan huruf pertama di kalimat", "Common.Views.AutoCorrectDialog.textForLangFL": "Pengecualian bagi bahasa:", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet dan jalur jaringan dengan hyperlink.", "Common.Views.AutoCorrectDialog.textHyphens": "Hyphens (--) dengan garis putus-putus (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "AutoCorrect Matematika", "Common.Views.AutoCorrectDialog.textNumbered": "Penomoran list otomatis", "Common.Views.AutoCorrectDialog.textQuotes": "\"Straight quotes\" dengan \"smart quotes\"", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON> yang dikenali", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Ekspresi ini merupakan ekspresi matematika. Ekspresi ini tidak akan dimiringkan secara otomatis.", "Common.Views.AutoCorrectDialog.textReplace": "Ganti", "Common.Views.AutoCorrectDialog.textReplaceText": "Ganti sambil Anda men<PERSON>ik", "Common.Views.AutoCorrectDialog.textReplaceType": "Ganti teks saat Anda mengetik", "Common.Views.AutoCorrectDialog.textReset": "<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textResetAll": "<PERSON>ur ulang kembali ke awal", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "AutoCorrect", "Common.Views.AutoCorrectDialog.textWarnAddFL": "Pengecualian hanya boleh memuat huruf besar atau kecil.", "Common.Views.AutoCorrectDialog.textWarnAddRec": "<PERSON><PERSON>i yang diterima harus memiliki huruf A sampai Z, huruf besar atau huruf kecil.", "Common.Views.AutoCorrectDialog.textWarnResetFL": "<PERSON><PERSON><PERSON> pengecualian yang Anda tambahkan akan dihapus dan yang sebelumnya dihapus akan dipulihkan. Apakah Anda hendak melanjutkan?", "Common.Views.AutoCorrectDialog.textWarnResetRec": "<PERSON><PERSON><PERSON> eksp<PERSON>i yang Anda tambahkan akan dihilangkan dan yang sudah terhapus akan dikembalikan. A<PERSON>kah Anda ingin melanjutkan?", "Common.Views.AutoCorrectDialog.warnReplace": "Entri autocorrect untuk %1 sudah ada. Apakah Anda ingin menggantinya?", "Common.Views.AutoCorrectDialog.warnReset": "Semua autocorrect yang Anda tambahkan akan dihilangkan dan yang sudah diganti akan dikembalikan ke nilai awalnya. Apakah Anda ingin melanjutkan?", "Common.Views.AutoCorrectDialog.warnRestore": "Entri autocorrect untuk %1 akan di reset ke nilai awal. <PERSON><PERSON><PERSON>h Anda ingin melanjutkan?", "Common.Views.Chat.textChat": "<PERSON><PERSON>lan", "Common.Views.Chat.textClosePanel": "<PERSON><PERSON><PERSON>", "Common.Views.Chat.textEnterMessage": "Tuliskan pesan Anda di sini", "Common.Views.Chat.textSend": "<PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Penulis A sampai Z", "Common.Views.Comments.mniAuthorDesc": "<PERSON><PERSON><PERSON> Z sampai A", "Common.Views.Comments.mniDateAsc": "Tertua", "Common.Views.Comments.mniDateDesc": "Terbaru", "Common.Views.Comments.mniFilterGroups": "<PERSON><PERSON>", "Common.Views.Comments.mniPositionAsc": "<PERSON><PERSON> atas", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON> bawah", "Common.Views.Comments.textAdd": "Tambahkan", "Common.Views.Comments.textAddComment": "Tambah komentar", "Common.Views.Comments.textAddCommentToDoc": "Tambahkan komentar ke dokumen", "Common.Views.Comments.textAddReply": "<PERSON><PERSON> balasan", "Common.Views.Comments.textAll": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "<PERSON><PERSON>", "Common.Views.Comments.textCancel": "Batalkan", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Tutup komentar", "Common.Views.Comments.textComment": "Komentar", "Common.Views.Comments.textComments": "Komentar", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Tuliskan komentar Anda di sini", "Common.Views.Comments.textHintAddComment": "Tambahkan komentar", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON> lagi", "Common.Views.Comments.textReply": "<PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "Diselesaikan", "Common.Views.Comments.textSort": "Sortir komentar", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "Anda tidak memiliki izin membuka kembali komentar", "Common.Views.Comments.txtEmpty": "Tidak ada komentar pada dokumen.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON>an tampilkan pesan ini lagi", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON>, potong dan tempel menggunakan tombol editor toolbar dan menu konteks dapat dilakukan hanya dengan tab editor ni saja.<br><br>Untuk menyalin atau menempel ke atau dari aplikasi di luar tab editor, gunakan kombinasi tombol keyboard berikut ini:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, da<PERSON>", "Common.Views.CopyWarningDialog.textToCopy": "untuk <PERSON>", "Common.Views.CopyWarningDialog.textToCut": "untuk Potong", "Common.Views.CopyWarningDialog.textToPaste": "untuk Tempel", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Memeriksa perintah yang akan ditampilkan pada Bilah Alat Akses Cepat", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Cetak", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Cetak Cepat", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Simpan", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Sesuaikan akses cepat", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Memuat...", "Common.Views.DocumentAccessDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> berbagi", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "Tidak", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Ya", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "<PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Ya\" atau \"Tidak\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Tanggal", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Teks", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "<PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtTitle": "Properti Dokumen Baru", "Common.Views.Draw.hintEraser": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Draw.hintSelect": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtEraser": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Draw.txtHighlighter": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "<PERSON><PERSON>", "Common.Views.Draw.txtSelect": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtSize": "Ukuran", "Common.Views.ExternalDiagramEditor.textTitle": "Editor bagan", "Common.Views.ExternalEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ExternalEditor.textSave": "Simpan & Keluar", "Common.Views.ExternalMergeEditor.textTitle": "<PERSON><PERSON>", "Common.Views.ExternalOleEditor.textTitle": "Penyunting Spreadsheet", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "User yang sedang edit file:", "Common.Views.Header.textAddFavorite": "Tandai sebagai favorit", "Common.Views.Header.textAdvSettings": "Pengatura<PERSON> lan<PERSON>t", "Common.Views.Header.textBack": "<PERSON><PERSON> Dokumen", "Common.Views.Header.textClose": "Tutup file", "Common.Views.Header.textCompactView": "Sembunyikan Toolbar", "Common.Views.Header.textDocEditDesc": "<PERSON><PERSON>t sebarang per<PERSON>han", "Common.Views.Header.textDocViewDesc": "View the file, but make no changes", "Common.Views.Header.textDocViewFormDesc": "<PERSON><PERSON> bagaimana formulir akan tampak ketika mengisi", "Common.Views.Header.textDownload": "Download", "Common.Views.Header.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideLines": "Sembunyikan Mistar", "Common.Views.Header.textHideStatusBar": "Sembunyikan Bilah Status", "Common.Views.Header.textPrint": "Cetak", "Common.Views.Header.textReadOnly": "<PERSON><PERSON> baca", "Common.Views.Header.textRemoveFavorite": "Hilangkan da<PERSON>", "Common.Views.Header.textReview": "Reviewing", "Common.Views.Header.textReviewDesc": "Suggest changes", "Common.Views.Header.textShare": "Bagikan", "Common.Views.Header.textStartFill": "Share & collect", "Common.Views.Header.textView": "Viewing", "Common.Views.Header.textViewForm": "Viewing form", "Common.Views.Header.textZoom": "Pembesaran", "Common.Views.Header.tipAccessRights": "Atur perizinan akses dokumen", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Sesuaikan Bilah Alat Akses Cepat", "Common.Views.Header.tipDocEdit": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipDocView": "Viewing", "Common.Views.Header.tipDocViewForm": "Viewing form", "Common.Views.Header.tipDownload": "Unduh File", "Common.Views.Header.tipFillStatus": "Filling status", "Common.Views.Header.tipGoEdit": "Edit file saat ini", "Common.Views.Header.tipPrint": "Print file", "Common.Views.Header.tipPrintQuick": "Cetak cepat", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipReview": "Reviewing", "Common.Views.Header.tipSave": "Simpan", "Common.Views.Header.tipSearch": "<PERSON><PERSON>", "Common.Views.Header.tipUndo": "Batalkan", "Common.Views.Header.tipUsers": "<PERSON><PERSON>", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON>", "Common.Views.Header.tipViewUsers": "Tampilkan user dan atur hak akses dokumen", "Common.Views.Header.txtAccessRights": "Ubah hak akses", "Common.Views.Header.txtRename": "Ganti nama", "Common.Views.History.textCloseHistory": "Tutup riwayat", "Common.Views.History.textHideAll": "Sembunyikan <PERSON> perubahan", "Common.Views.History.textHighlightDeleted": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textMore": "<PERSON><PERSON><PERSON>", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "Tampilkan <PERSON> perubahan", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Riwayat Versi", "Common.Views.ImageFromUrlDialog.textUrl": "Tempel URL gambar:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Ruas ini harus berupa URL dengan format \"http://www.contoh.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "<PERSON>a harus menentukan baris dan jumlah kolom yang benar.", "Common.Views.InsertTableDialog.txtColumns": "<PERSON><PERSON><PERSON> kolo<PERSON>", "Common.Views.InsertTableDialog.txtMaxText": "<PERSON><PERSON> maksimal untuk ruas ini adalah {0}.", "Common.Views.InsertTableDialog.txtMinText": "<PERSON>lai minimal untuk ruas ini adalah {0}.", "Common.Views.InsertTableDialog.txtRows": "<PERSON><PERSON><PERSON> bar<PERSON>", "Common.Views.InsertTableDialog.txtTitle": "Ukuran tabel", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON> sel", "Common.Views.LanguageDialog.labelSelect": "<PERSON><PERSON><PERSON> bahasa dokumen", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "Tutup file", "Common.Views.OpenDialog.txtEncoding": "Enkoding ", "Common.Views.OpenDialog.txtIncorrectPwd": "Password salah.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON><PERSON><PERSON>n kata sandi untuk buka file", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON> Anda memasukkan password dan membuka file, password file saat ini akan di reset.", "Common.Views.OpenDialog.txtTitle": "Pilih %1 opsi", "Common.Views.OpenDialog.txtTitleProtected": "File yang diproteksi", "Common.Views.PasswordDialog.txtDescription": "Buat password untuk melindungi dokumen ini", "Common.Views.PasswordDialog.txtIncorrectPwd": "Password konfirmasi tidak sama", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON> password", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON> kata sandi", "Common.Views.PasswordDialog.txtWarning": "Peringatan: Tidak bisa dipulihkan jika Anda kehilangan atau lupa kata sandi. Simpan di tempat yang aman.", "Common.Views.PluginDlg.textLoading": "Memuat", "Common.Views.PluginPanel.textClosePanel": "Tutup plugin", "Common.Views.PluginPanel.textLoading": "Memuat", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textBackgroundPlugins": "Plugin latar belakang", "Common.Views.Plugins.textSettings": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStart": "<PERSON><PERSON>", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON><PERSON><PERSON> dengan password", "Common.Views.Protection.hintDelPwd": "<PERSON><PERSON> kata sandi", "Common.Views.Protection.hintPwd": "Ganti atau hapus password", "Common.Views.Protection.hintSignature": "Tambah tanda tangan digital atau baris tanda tangan", "Common.Views.Protection.txtAddPwd": "Tambah password", "Common.Views.Protection.txtChangePwd": "Ubah kata sandi", "Common.Views.Protection.txtDeletePwd": "Nama file", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "Tambah tanda tangan digital", "Common.Views.Protection.txtSignature": "<PERSON><PERSON>", "Common.Views.Protection.txtSignatureLine": "Tambah baris tanda tangan", "Common.Views.RecentFiles.txtOpenRecent": "<PERSON><PERSON> yang <PERSON>", "Common.Views.RenameDialog.textName": "Nama file", "Common.Views.RenameDialog.txtInvalidName": "Nama file tidak boleh berisi karakter seperti: ", "Common.Views.ReviewChanges.hintNext": "<PERSON> berik<PERSON>", "Common.Views.ReviewChanges.hintPrev": "<PERSON>han sebelumnya", "Common.Views.ReviewChanges.mniFromFile": "Dokumen dari file", "Common.Views.ReviewChanges.mniFromStorage": "<PERSON><PERSON><PERSON> dari <PERSON>", "Common.Views.ReviewChanges.mniFromUrl": "Dokumen dari URL", "Common.Views.ReviewChanges.mniMMFromFile": "Dari file", "Common.Views.ReviewChanges.mniMMFromStorage": "<PERSON><PERSON>", "Common.Views.ReviewChanges.mniMMFromUrl": "Dari URL", "Common.Views.ReviewChanges.mniSettings": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>n", "Common.Views.ReviewChanges.strFast": "Cepat", "Common.Views.ReviewChanges.strFastDesc": "Co-editing real-time. <PERSON><PERSON><PERSON> disimpan otomatis.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Gunakan tombol 'Simpan' untuk sinkronisasi perubahan yang dibuat Anda dan orang lain.", "Common.Views.ReviewChanges.textEnable": "Aktifkan", "Common.Views.ReviewChanges.textWarnTrackChanges": "Lacak Perubahan akan ON untuk semua user dengan akses penuh. Jika orang lain membuka doc di lain waktu, Lacak Perubahan tetap aktif.", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "Aktifkan lacak perubahan untuk semua orang?", "Common.Views.ReviewChanges.tipAcceptCurrent": "Setujui perubahan saat ini dan pindah ke selanjutnya", "Common.Views.ReviewChanges.tipCoAuthMode": "Atur mode co-editing", "Common.Views.ReviewChanges.tipCombine": "Gabungkan dokumen ini dengan yang lain", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON><PERSON> komentar", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Hilangkan komentar saat ini", "Common.Views.ReviewChanges.tipCommentResolve": "Selesaikan komentar", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Selesaikan komentar saat ini", "Common.Views.ReviewChanges.tipCompare": "Bandingkan dokumen sekarang dengan yang lain", "Common.Views.ReviewChanges.tipHistory": "Tampilkan riwayat versi", "Common.Views.ReviewChanges.tipMailRecepients": "Surat massal", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON> perubahan saat ini dan pindah ke selanjutnya", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Pilih mode yang per<PERSON>hannya ingin Anda tampilkan", "Common.Views.ReviewChanges.tipSetDocLang": "Atur Bahasa Dokumen", "Common.Views.ReviewChanges.tipSetSpelling": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipSharing": "Atur perizinan akses dokumen", "Common.Views.ReviewChanges.txtAccept": "Terima", "Common.Views.ReviewChanges.txtAcceptAll": "<PERSON><PERSON> semua per<PERSON>han", "Common.Views.ReviewChanges.txtAcceptChanges": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptCurrent": "<PERSON><PERSON> perubahan saat ini", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Mode Edit <PERSON>", "Common.Views.ReviewChanges.txtCombine": "Gabungkan", "Common.Views.ReviewChanges.txtCommentRemAll": "<PERSON><PERSON> semua komentar", "Common.Views.ReviewChanges.txtCommentRemCurrent": "<PERSON>pus komentar saat ini", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON><PERSON> komentar saya", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "<PERSON><PERSON> komentar saya saat ini", "Common.Views.ReviewChanges.txtCommentRemove": "Hapus", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON><PERSON> semua komentar", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Selesaikan komentar saat ini", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Selesaikan Ko<PERSON> Say<PERSON>", "Common.Views.ReviewChanges.txtCompare": "Komparasi", "Common.Views.ReviewChanges.txtDocLang": "Bahasa", "Common.Views.ReviewChanges.txtEditing": "Editing", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON><PERSON> di<PERSON>ima {0}", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Riwayat versi", "Common.Views.ReviewChanges.txtMailMerge": "Surat Massal", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON><PERSON> {0}", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON> dan balon", "Common.Views.ReviewChanges.txtMarkupSimple": "<PERSON><PERSON><PERSON> {0}<br><PERSON><PERSON> <PERSON>", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "<PERSON><PERSON> markup", "Common.Views.ReviewChanges.txtNext": "Selanjutnya", "Common.Views.ReviewChanges.txtOff": "OFF untuk saya", "Common.Views.ReviewChanges.txtOffGlobal": "OFF untuk saya dan semuanya", "Common.Views.ReviewChanges.txtOn": "ON untuk saya", "Common.Views.ReviewChanges.txtOnGlobal": "ON untuk saya dan semuanya", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON><PERSON> {0}", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Sebelumnya", "Common.Views.ReviewChanges.txtPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtSharing": "Bagikan", "Common.Views.ReviewChanges.txtSpelling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "Mode Tampilan", "Common.Views.ReviewChangesDialog.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtAccept": "Terima", "Common.Views.ReviewChangesDialog.txtAcceptAll": "<PERSON><PERSON> semua per<PERSON>han", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "<PERSON><PERSON> perubahan saat ini", "Common.Views.ReviewChangesDialog.txtNext": "<PERSON> berik<PERSON>", "Common.Views.ReviewChangesDialog.txtPrev": "<PERSON>han sebelumnya", "Common.Views.ReviewChangesDialog.txtReject": "<PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtRejectAll": "<PERSON><PERSON> semua per<PERSON>han", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "<PERSON><PERSON> perubahan saat ini", "Common.Views.ReviewPopover.textAdd": "Tambahkan", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON> balasan", "Common.Views.ReviewPopover.textCancel": "Batalkan", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textComment": "Komentar", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Tuliskan komentar Anda di sini", "Common.Views.ReviewPopover.textFollowMove": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textMention": "+mention akan memberikan akses ke dokumen dan mengirimkan email", "Common.Views.ReviewPopover.textMentionNotify": "+mention akan menging<PERSON>kan user lewat email", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON> lagi", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Anda tidak memiliki izin membuka kembali komentar", "Common.Views.ReviewPopover.txtAccept": "Terima", "Common.Views.ReviewPopover.txtDeleteTip": "Hapus", "Common.Views.ReviewPopover.txtEditTip": "Sunting", "Common.Views.ReviewPopover.txtReject": "<PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Memuat", "Common.Views.SaveAsDlg.textTitle": "Folder untuk simpan", "Common.Views.SearchPanel.textCaseSensitive": "<PERSON><PERSON> huruf besar-kecil", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textContentChanged": "<PERSON>kumen telah diubah.", "Common.Views.SearchPanel.textFind": "Temukan", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON><PERSON><PERSON> dan ganti", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} butir sukses diganti.", "Common.Views.SearchPanel.textMatchUsingRegExp": "Cocokkan dengan ekspresi reguler", "Common.Views.SearchPanel.textNoMatches": "Tidak ada yang cocok", "Common.Views.SearchPanel.textNoSearchResults": "Tidak ada hasil pencarian", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} butir diganti. Sisa {2} butir dikunci oleh pengguna lain.", "Common.Views.SearchPanel.textReplace": "Ganti", "Common.Views.SearchPanel.textReplaceAll": "Ganti Semua", "Common.Views.SearchPanel.textReplaceWith": "Ganti dengan", "Common.Views.SearchPanel.textSearchAgain": "{0}Lakukan pencarian baru{1} untuk hasil yang akurat.", "Common.Views.SearchPanel.textSearchHasStopped": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSearchResults": "<PERSON><PERSON> pencarian: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "<PERSON><PERSON>", "Common.Views.SearchPanel.textTooManyResults": "<PERSON><PERSON><PERSON>u banyak hasil untuk ditampilkan di sini", "Common.Views.SearchPanel.textWholeWords": "<PERSON><PERSON><PERSON><PERSON> kata saja", "Common.Views.SearchPanel.tipNextResult": "<PERSON><PERSON>", "Common.Views.SearchPanel.tipPreviousResult": "<PERSON><PERSON>", "Common.Views.SelectFileDlg.textLoading": "Memuat", "Common.Views.SelectFileDlg.textTitle": "Pilih sumber data", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Jarak", "Common.Views.ShapeShadowDialog.txtSize": "Ukuran", "Common.Views.ShapeShadowDialog.txtTitle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "<PERSON><PERSON>", "Common.Views.SignDialog.textCertificate": "Ser<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Ganti", "Common.Views.SignDialog.textInputName": "<PERSON><PERSON><PERSON><PERSON> nama penanda<PERSON>", "Common.Views.SignDialog.textItalic": "Miring", "Common.Views.SignDialog.textNameError": "<PERSON>a penanda<PERSON>gan tidak boleh kosong.", "Common.Views.SignDialog.textPurpose": "Tu<PERSON>an menandatangani dokumen ini", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON> gambar", "Common.Views.SignDialog.textSignature": "Tandatangan terlihat seperti", "Common.Views.SignDialog.textTitle": "Tanda tangani dokumen", "Common.Views.SignDialog.textUseImage": "atau klik '<PERSON>lih G<PERSON>' untuk menjadikan gambar sebagai tandatangan", "Common.Views.SignDialog.textValid": "Valid dari %1 sampai %2", "Common.Views.SignDialog.tipFontName": "Nama font", "Common.Views.SignDialog.tipFontSize": "Ukuran font", "Common.Views.SignSettingsDialog.textAllowComment": "Izinkan penandatangan untuk menambahkan komentar di dialog tanda tangan", "Common.Views.SignSettingsDialog.textDefInstruction": "Sebelum menandatangani dokumen ini, pastikan konten yang akan Anda tanda tangani sudah benar.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail penandatangan yang disarankan", "Common.Views.SignSettingsDialog.textInfoName": "Penandatangan yang disarankan", "Common.Views.SignSettingsDialog.textInfoTitle": "<PERSON><PERSON><PERSON> penanda<PERSON>gan yang disarankan", "Common.Views.SignSettingsDialog.textInstructions": "Instruksi untuk penandatangan", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON><PERSON><PERSON> tanggal di garis tandatangan", "Common.Views.SignSettingsDialog.textTitle": "Penyiapan tanda tangan", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Nilai Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Tanda hak cipta", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON> ganda penutup", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON><PERSON> ganda pembuka", "Common.Views.SymbolTableDialog.textEllipsis": "Ellipsis horizontal", "Common.Views.SymbolTableDialog.textEmDash": "Em dash", "Common.Views.SymbolTableDialog.textEmSpace": "Em space", "Common.Views.SymbolTableDialog.textEnDash": "En dash", "Common.Views.SymbolTableDialog.textEnSpace": "En space", "Common.Views.SymbolTableDialog.textFont": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "Tanda sambung tak-putus", "Common.Views.SymbolTableDialog.textNBSpace": "Spasi tak-putus", "Common.Views.SymbolTableDialog.textPilcrow": "Tanda pilcrow", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em space", "Common.Views.SymbolTableDialog.textRange": "Rentang", "Common.Views.SymbolTableDialog.textRecent": "Simbol yang baru digunakan", "Common.Views.SymbolTableDialog.textRegistered": "<PERSON><PERSON> te<PERSON>", "Common.Views.SymbolTableDialog.textSCQuote": "<PERSON><PERSON> tunggal penutup", "Common.Views.SymbolTableDialog.textSection": "<PERSON><PERSON> seksi", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON> pin<PERSON>an", "Common.Views.SymbolTableDialog.textSHyphen": "Tanda hubung lunak", "Common.Views.SymbolTableDialog.textSOQuote": "<PERSON><PERSON> tunggal pembuka", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Simbol", "Common.Views.SymbolTableDialog.textTitle": "Simbol", "Common.Views.SymbolTableDialog.textTradeMark": "Simbol merk dagang ", "Common.Views.UserNameDialog.textDontShow": "<PERSON>an tanya saya lagi", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label tidak boleh kosong.", "DE.Controllers.DocProtection.txtIsProtectedComment": "Dokumen diproteksi. Anda hanya dapat menyisipkan komentar ke dokumen ini.", "DE.Controllers.DocProtection.txtIsProtectedForms": "Dokumen diproteksi. Anda hanya dapat mengisi formulir dalam dokumen ini.", "DE.Controllers.DocProtection.txtIsProtectedTrack": "Dokumen diproteksi. <PERSON>a dapat menyunting dokumen ini, tapi semua perubahan akan dila<PERSON>k.", "DE.Controllers.DocProtection.txtIsProtectedView": "Dokumen terproteksi. Anda hanya dapat melihat dokumen ini.", "DE.Controllers.DocProtection.txtWasProtectedComment": "Dokumen telah diproteksi oleh pengguna lain.\nAnda hanya dapat menyisipkan komentar ke dokumen ini.", "DE.Controllers.DocProtection.txtWasProtectedForms": "Dokumen telah diproteksi oleh pengguna lain.\nAnda hanya dapat mengisi formulir dalam dokumen ini.", "DE.Controllers.DocProtection.txtWasProtectedTrack": "Dokumen telah diproteksi oleh pengguna lain.\n<PERSON>a dapat menyunting dokumen ini, tapi semua perubahan akan dila<PERSON>k.", "DE.Controllers.DocProtection.txtWasProtectedView": "Dokumen telah diproteksi oleh pengguna lain.\nAnda hanya dapat melihat dokumen ini.", "DE.Controllers.DocProtection.txtWasUnprotected": "Dokumen telah dibuka proteksinya.", "DE.Controllers.LeftMenu.leavePageText": "Se<PERSON><PERSON> perubahan yang tidak tersimpan di dokumen ini akan hilang.<br> <PERSON><PERSON> \"<PERSON><PERSON>\" lalu \"<PERSON><PERSON><PERSON>\" untuk menyimpan. Klik \"OK\" untuk membuang semua perubahan yang tidak tersimpan.", "DE.Controllers.LeftMenu.newDocumentTitle": "Dokumen tidak bernama", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "Peringatan", "DE.Controllers.LeftMenu.requestEditRightsText": "Me<PERSON>ta hak editing...", "DE.Controllers.LeftMenu.textLoadHistory": "Loading versi riwayat...", "DE.Controllers.LeftMenu.textNoTextFound": "Data yang Anda cari tidak ditemukan. <PERSON>lakan atur opsi pencarian <PERSON>a.", "DE.Controllers.LeftMenu.textReplaceSkipped": "Penggantian telah di<PERSON>n. Ada {0} yang di<PERSON>.", "DE.Controllers.LeftMenu.textReplaceSuccess": "Pencarian telah <PERSON>. <PERSON> {0} yang diganti {0}", "DE.Controllers.LeftMenu.textSelectPath": "<PERSON><PERSON>kkan suatu nama baru untuk menyimpan salinan berkas", "DE.Controllers.LeftMenu.txtCompatible": "Dokumen akan disimpan ke format baru. <PERSON><PERSON><PERSON> fitur editor akan di<PERSON>, tapi, mungkin akan mempengaruhi tampilan dokumen.<br>Gunakan opsi 'Kompatibilitas' di pengaturan tingkat lanjut jika Anda ingin membuat file kompatibel dengan versi MS Word lama.", "DE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON>", "DE.Controllers.LeftMenu.warnDownloadAs": "Ji<PERSON> Anda lanjut simpan dengan format ini, semua fitur kecuali teks akan hilang.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin melanjutkan?", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "{0} Anda akan dikonversi ke format yang bisa diedit. Hal ini mungkin akan membutuhkan waktu. Dokumen yang dihasilkan akan dioptimalkan untuk memungkinkan Anda mengedit teks, sehingga mungkin tidak terlihat persis seperti aslinya {0}, terutama jika file asli berisi banyak gambar.", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "Jika Anda lanjut simpan dengan format ini, beberapa format lain mungkin akan terhapus.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin melanjutkan?", "DE.Controllers.LeftMenu.warnReplaceString": "{0} bukan karakter khusus yang valid untuk ruas penggantian.", "DE.Controllers.Main.applyChangesTextText": "<PERSON><PERSON><PERSON>...", "DE.Controllers.Main.applyChangesTitleText": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.confirmMaxChangesSize": "Ukuran tindakan melebihi batas yang ditetapkan untuk server <PERSON><PERSON>.<br><PERSON><PERSON> \"Batalkan\" untuk membatalkan tindakan terakhir Anda atau tekan \"Lanjutkan\" untuk menyimpan tindakan secara lokal (Anda perlu mengunduh file atau menyalin isinya untuk memastikan tidak ada yang hilang).", "DE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON>tu konversi habis.", "DE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON> \"OK\" untuk kembali ke daftar dokumen.", "DE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON> gagal.", "DE.Controllers.Main.downloadMergeText": "Mengunduh...", "DE.Controllers.Main.downloadMergeTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.downloadTextText": "Mengunduh dokumen...", "DE.Controllers.Main.downloadTitleText": "Sedang mengunduh dokumen", "DE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON> mencoba melakukan sesuatu yang tidak memiliki izin.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>.", "DE.Controllers.Main.errorBadImageUrl": "URL Gambar salah", "DE.Controllers.Main.errorCannotPasteImg": "Kami tak bisa menempel gambar ini dari <PERSON>, tapi Anda dapat menyimpannya ke perangkat Anda dan menyisipkannya dari sana, atau Anda dapat menyalin gambar tanpa teks dan menempelkannya ke dalam dokumen.", "DE.Controllers.Main.errorCoAuthoringDisconnect": "Koneksi server terputus. Saat ini dokumen tidak dapat diedit.", "DE.Controllers.Main.errorComboSeries": "Untuk membuat grafik kombinasi, pilih setidaknya dua seri data.", "DE.Controllers.Main.errorCompare": "Fitur Membandingkan Dokumen tidak tersedia saat co-editing. ", "DE.Controllers.Main.errorConnectToServer": "Dokumen tidak bisa disimpan. <PERSON>lakan periksa pengaturan koneksi atau hubungi admin Anda.<br><PERSON><PERSON><PERSON> klik tombol 'OK', <PERSON><PERSON> akan diminta untuk download dokumen.", "DE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON> eksternal.<br>Koneksi database bermasalah. <PERSON><PERSON>an hubungi layanan bantuan jika tetap terjadi error.", "DE.Controllers.Main.errorDataEncrypted": "Perubahan enkripsi sudah diterima dan tidak bisa diuraikan.", "DE.Controllers.Main.errorDataRange": "Rentang data salah.", "DE.Controllers.Main.errorDefaultMessage": "Kode k<PERSON>alahan: %1", "DE.Controllers.Main.errorDirectUrl": "<PERSON><PERSON><PERSON> verifikasi link ke dokumen.<br>Link ini harus langsung menuju file target untuk download.", "DE.Controllers.Main.errorEditingDownloadas": "<PERSON> kesalahan saat bekerja dengan dokumen.<br><PERSON><PERSON><PERSON> opsi 'Download sebagai' untuk menyimpan file salinan backup ke komputer Anda.", "DE.Controllers.Main.errorEditingSaveas": "<PERSON> kesalahan saat bekerja dengan dokumen.<br><PERSON><PERSON><PERSON> opsi 'Simpan sebagai...' untuk menyimpan file salinan backup ke komputer Anda.", "DE.Controllers.Main.errorEditProtectedRange": "You are not allowed to edit this selection because it is protected.", "DE.Controllers.Main.errorEmailClient": "<PERSON><PERSON> klein tidak bisa ditemukan.", "DE.Controllers.Main.errorEmptyTOC": "<PERSON><PERSON> membuat daftar isi dengan menerapkan gaya judul dari galeri Gaya kepada teks terpilih.", "DE.Controllers.Main.errorFilePassProtect": "Dokumen dilindungi dengan kata sandi dan tidak dapat dibuka.", "DE.Controllers.Main.errorFileSizeExceed": "Ukuran file melewati batas server <PERSON><PERSON>.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>a untuk detail.", "DE.Controllers.Main.errorForceSave": "<PERSON> kesalahan saat menyimpan file. <PERSON><PERSON><PERSON> gunakan opsi 'Unduh sebagai' untuk menyimpan file ke komputer Anda dan coba lagi.", "DE.Controllers.Main.errorInconsistentExt": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file tidak cocok dengan ekstensi file.", "DE.Controllers.Main.errorInconsistentExtDocx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan dokumen teks (mis. docx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "DE.Controllers.Main.errorInconsistentExtPdf": "<PERSON><PERSON><PERSON> kesalahan terjadi ketika membuka file.<br>Isi file berhubungan dengan satu dari format berikut: pdf/djvu/xps/oxps, tapi file memiliki ekstensi yang tidak konsisten: %1.", "DE.Controllers.Main.errorInconsistentExtPptx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan presentasi (mis. pptx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "DE.Controllers.Main.errorInconsistentExtXlsx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan spreadsheet (mis. xlsx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "DE.Controllers.Main.errorKeyEncrypt": "Deskriptor kunci tidak dikenal", "DE.Controllers.Main.errorKeyExpire": "Deskriptor kunci tidak berfungsi", "DE.Controllers.Main.errorLoadingFont": "Font tidak bisa dimuat.<br><PERSON><PERSON><PERSON> kontak admin Server <PERSON><PERSON><PERSON>.", "DE.Controllers.Main.errorMailMergeLoadFile": "Loading dokumen gagal. Silakan coba dengan file lain.", "DE.Controllers.Main.errorMailMergeSaveFile": "<PERSON><PERSON> gagal.", "DE.Controllers.Main.errorNoTOC": "Tidak ada daftar isi yang harus diperbarui. Anda dapat menyisipkan satu daftar isi dari tab Referensi.", "DE.Controllers.Main.errorPasswordIsNotCorrect": "Kata sandi yang Anda masukkan tidak tepat.<br>Pastikan CAPS LOCK sudah mati dan pastikan telah menggunakan huruf besar dengan tepat.", "DE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON>.", "DE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "DE.Controllers.Main.errorServerVersion": "Versi editor sudah di update. <PERSON>aman akan dimuat ulang untuk menerapkan perubahan.", "DE.Controllers.Main.errorSessionAbsolute": "W<PERSON><PERSON> edit dokumen sudah selesai. Silakan muat ulang halaman.", "DE.Controllers.Main.errorSessionIdle": "Dokumen sudah lama tidak diedit. <PERSON><PERSON>an muat ulang halaman.", "DE.Controllers.Main.errorSessionToken": "Koneksi ke server terganggu. Silakan muat ulang halaman.", "DE.Controllers.Main.errorSetPassword": "Password tidak bisa diatur.", "DE.Controllers.Main.errorStockChart": "Urutan baris salah. Untuk membuat diagram garis, masukkan data pada lembar kerja dengan urutan berikut ini:<br> harga pembu<PERSON><PERSON>, harga maks<PERSON><PERSON>, harga <PERSON>, harga penutupan.", "DE.Controllers.Main.errorSubmit": "Submit gagal.", "DE.Controllers.Main.errorTextFormWrongFormat": "<PERSON><PERSON> yang dimasukkan tidak cocok dengan format ruas.", "DE.Controllers.Main.errorToken": "Token keamanan dokumen tidak dibentuk dengan tepat.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>.", "DE.Controllers.Main.errorTokenExpire": "Token keamanan dokumen sudah kadaluwarsa.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>.", "DE.Controllers.Main.errorUpdateVersion": "Versi file telah diubah. Halaman tidak akan dimuat ulang.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "Koneksi internet sudah kembali dan versi file sudah diganti.<br>Sebelum Anda bisa melanjutkan kerja, Anda perlu mengunduh file atau salin konten untuk memastikan tidak ada yang hilang, lalu muat ulang halaman ini.", "DE.Controllers.Main.errorUserDrop": "File tidak bisa diakses sekarang.", "DE.Controllers.Main.errorUsersExceed": "<PERSON><PERSON><PERSON> pengguna telah melebihi jumlah yang diijinkan dalam paket harga.", "DE.Controllers.Main.errorViewerDisconnect": "Koneksi terputus. <PERSON>a tetap bisa melihat dokumen,<br>tapi tidak bisa download atau print sampai koneksi terhubung dan halaman dimuat ulang.", "DE.Controllers.Main.leavePageText": "<PERSON> perubahan yang belum disimpan dalam dokumen ini. <PERSON><PERSON> \"Tetap di Halaman Ini\" kemudian \"Simpan\" untuk menyimpan perubahan tersebut. <PERSON>lik \"Tinggalkan Halaman Ini\" untuk membatalkan semua perubahan yang belum disimpan.", "DE.Controllers.Main.leavePageTextOnClose": "Se<PERSON><PERSON> perubahan yang tidak tersimpan di dokumen ini akan hilang.<br> <PERSON><PERSON> \"<PERSON><PERSON>\" lalu \"<PERSON><PERSON><PERSON>\" untuk menyimpan. Klik \"OK\" untuk membuang semua perubahan yang tidak tersimpan.", "DE.Controllers.Main.loadFontsTextText": "Memuat data...", "DE.Controllers.Main.loadFontsTitleText": "Memuat Data", "DE.Controllers.Main.loadFontTextText": "Memuat data...", "DE.Controllers.Main.loadFontTitleText": "Memuat Data", "DE.Controllers.Main.loadImagesTextText": "Memuat gambar...", "DE.Controllers.Main.loadImagesTitleText": "Memuat Gambar", "DE.Controllers.Main.loadImageTextText": "Memuat gambar...", "DE.Controllers.Main.loadImageTitleText": "Memuat Gambar", "DE.Controllers.Main.loadingDocumentTextText": "Memuat dokumen...", "DE.Controllers.Main.loadingDocumentTitleText": "Memuat dokumen", "DE.Controllers.Main.mailMergeLoadFileText": "Loading Sumber Data...", "DE.Controllers.Main.mailMergeLoadFileTitle": "Loading Sumber Data...", "DE.Controllers.Main.notcriticalErrorTitle": "Peringatan", "DE.Controllers.Main.openErrorText": "Eror ketika membuka file.", "DE.Controllers.Main.openTextText": "Membuka Dokumen...", "DE.Controllers.Main.openTitleText": "Sedang membuka dokumen", "DE.Controllers.Main.printTextText": "Mencetak dokumen...", "DE.Controllers.Main.printTitleText": "Mencetak Dokumen", "DE.Controllers.Main.reloadButtonText": "Muat <PERSON>", "DE.Controllers.Main.requestEditFailedMessageText": "Saat ini dokumen sedang diedit. <PERSON>lakan coba beberapa saat lagi.", "DE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.saveErrorText": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han ketika menyimpan file.", "DE.Controllers.Main.saveErrorTextDesktop": "File tidak bisa disimpan atau dibuat.<br><PERSON><PERSON><PERSON> yang mungkin adalah: <br>1. File hanya bisa dibaca. <br>2. <PERSON> sedang diedit user lain. <br>3. <PERSON><PERSON><PERSON> penuh atau terkorupsi.", "DE.Controllers.Main.saveTextText": "Menyimpan dokumen...", "DE.Controllers.Main.saveTitleText": "Sedang menyimpan dokumen", "DE.Controllers.Main.savingText": "Menyimpan", "DE.Controllers.Main.scriptLoadError": "Koneksi terlalu lambat dan beberapa komponen tidak bisa dibuka. Silakan muat ulang halaman.", "DE.Controllers.Main.sendMergeText": "Mengirim Merge...", "DE.Controllers.Main.sendMergeTitle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.splitDividerErrorText": "Jumlah baris harus merupakan pembagi %1.", "DE.Controllers.Main.splitMaxColsErrorText": "Ju<PERSON>lah kolom harus kurang dari %1.", "DE.Controllers.Main.splitMaxRowsErrorText": "Jumlah haris harus kurang dari %1.", "DE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textAnyone": "Siapa pun", "DE.Controllers.Main.textApplyAll": "Terapkan untuk semua persa<PERSON>an", "DE.Controllers.Main.textBuyNow": "Kunjungi website", "DE.Controllers.Main.textChangesSaved": "<PERSON><PERSON><PERSON> te<PERSON>", "DE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textCloseTip": "Klik untuk menutup tips", "DE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "DE.Controllers.Main.textContactUs": "Hubungi sales", "DE.Controllers.Main.textContinue": "Lanjutkan", "DE.Controllers.Main.textConvertEquation": "Persamaan ini dibuat dengan editor persa<PERSON><PERSON> versi lama yang sudah tidak didukung. Untuk edit, konversikan persamaan ke format Office Math ML.<br><PERSON>n<PERSON><PERSON> sekarang?", "DE.Controllers.Main.textCustomLoader": "<PERSON><PERSON> diketahui bahwa berdasarkan syarat dari lisensi, <PERSON><PERSON> tidak bisa untuk mengganti loader.<br><PERSON><PERSON><PERSON> hubungi Departemen Penjualan kami untuk mendapatkan harga.", "DE.Controllers.Main.textDisconnect": "<PERSON><PERSON><PERSON><PERSON> terputus", "DE.Controllers.Main.textGuest": "<PERSON><PERSON>", "DE.Controllers.Main.textHasMacros": "File berisi macros otomatis.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin menja<PERSON>an macros?", "DE.Controllers.Main.textLearnMore": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.textLoadingDocument": "Memuat dokumen", "DE.Controllers.Main.textLongName": "<PERSON><PERSON><PERSON><PERSON> nama maksimum 128 karakter.", "DE.Controllers.Main.textNoLicenseTitle": "Batas lisensi sudah tercapai", "DE.Controllers.Main.textPaidFeature": "<PERSON><PERSON>", "DE.Controllers.Main.textReconnect": "Koneksi terhubung kembali", "DE.Controllers.Main.textRemember": "Ingat pilihan saya untuk semua file", "DE.Controllers.Main.textRememberMacros": "Ingat pilihan saya untuk semua makro", "DE.Controllers.Main.textRenameError": "Nama user tidak boleh kosong.", "DE.Controllers.Main.textRenameLabel": "Ma<PERSON>kkan nama untuk digunakan di kolaborasi", "DE.Controllers.Main.textRequestMacros": "Sebuah makro melakukan permintaan ke URL. Apakah Anda akan mengizinkan permintaan ini ke %1?", "DE.Controllers.Main.textShape": "Bentuk", "DE.Controllers.Main.textSignature": "Signature", "DE.Controllers.Main.textStrict": "Mode strict", "DE.Controllers.Main.textText": "Teks", "DE.Controllers.Main.textTryQuickPrint": "Anda telah memilih Cetak cepat: seluruh dokumen akan dicetak pada printer yang terakhir dipilih atau baku.<br><PERSON><PERSON><PERSON><PERSON> Anda hendak melanjutkan?", "DE.Controllers.Main.textTryUndoRedo": "Fungsi Undo/Redo dinonaktifkan untuk mode Co-editing Cepat.<br>Klik tombol 'Mode strict' untuk mengganti ke Mode Strict Co-editing untuk edit file tanpa gangguan dari user lain dan kirim perubahan Anda hanya setelah Anda menyimpannya. Anda bisa mengganti mode co-editing menggunakan editor di pengaturan lanjut.", "DE.Controllers.Main.textTryUndoRedoWarn": "Fungsi Undo/Redo dinonaktifkan untuk mode Co-editing Cepat.", "DE.Controllers.Main.textUndo": "Batalkan", "DE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "DE.Controllers.Main.textUpdating": "Updating", "DE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON><PERSON> ka<PERSON>", "DE.Controllers.Main.titleLicenseNotActive": "Lisensi tidak aktif", "DE.Controllers.Main.titleServerVersion": "Editor mengu<PERSON><PERSON>", "DE.Controllers.Main.titleUpdateVersion": "<PERSON>ersi telah diubah", "DE.Controllers.Main.txtAbove": "di atas", "DE.Controllers.Main.txtArt": "Teks Anda di sini", "DE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON> das<PERSON>", "DE.Controllers.Main.txtBelow": "di bawah", "DE.Controllers.Main.txtBookmarkError": "Kesalahan! Bookmark tidak terdefinisikan.", "DE.Controllers.Main.txtButtons": "Tombol", "DE.Controllers.Main.txtCallouts": "Balon Kata", "DE.Controllers.Main.txtCharts": "Bagan", "DE.Controllers.Main.txtChoose": "<PERSON><PERSON>h satu barang", "DE.Controllers.Main.txtClickToLoad": "Klik untuk memuat gambar", "DE.Controllers.Main.txtCurrentDocument": "Do<PERSON>men saat ini", "DE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON> bagan", "DE.Controllers.Main.txtEditingMode": "Mengatur mode editing...", "DE.Controllers.Main.txtEndOfFormula": "Akhir Formula Tidak Terduga", "DE.Controllers.Main.txtEnterDate": "<PERSON><PERSON><PERSON><PERSON> tanggal", "DE.Controllers.Main.txtErrorLoadHistory": "Memuat riwayat gagal", "DE.Controllers.Main.txtEvenPage": "<PERSON><PERSON> genap", "DE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON>", "DE.Controllers.Main.txtFirstPage": "Hal<PERSON> pertama", "DE.Controllers.Main.txtFooter": "Footer", "DE.Controllers.Main.txtFormulaNotInTable": "Formula Tidak Ada di Tabel", "DE.Controllers.Main.txtHeader": "Header", "DE.Controllers.Main.txtHyperlink": "Hyperlink", "DE.Controllers.Main.txtIndTooLarge": "Index terlalu besar", "DE.Controllers.Main.txtLines": "<PERSON><PERSON>", "DE.Controllers.Main.txtMainDocOnly": "Kesalahan! Hanya dokumen utama.", "DE.Controllers.Main.txtMath": "Matematika", "DE.Controllers.Main.txtMissArg": "<PERSON><PERSON> argumen", "DE.Controllers.Main.txtMissOperator": "Kurang operator", "DE.Controllers.Main.txtNeedSynchronize": "<PERSON> pembaruan", "DE.Controllers.Main.txtNone": "Tidak ada", "DE.Controllers.Main.txtNoTableOfContents": "Tidak ada heading di dokumen. Terapkan style heading ke teks agar bisa terlihat di daftar isi.", "DE.Controllers.Main.txtNoTableOfFigures": "Tidak ada daftar gambar yang di<PERSON>ukan.", "DE.Controllers.Main.txtNoText": "Kesalahan! Tidak ada teks dengan style sesuai spesifikasi di dokumen.", "DE.Controllers.Main.txtNotInTable": "Tidak Ada di Tabel", "DE.Controllers.Main.txtNotValidBookmark": "Kesalahan! Bukan bookmark self-reference yang valid.", "DE.Controllers.Main.txtOddPage": "<PERSON><PERSON> ganjil", "DE.Controllers.Main.txtOnPage": "di halaman", "DE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtSameAsPrev": "<PERSON>a seperti sebelumnya", "DE.Controllers.Main.txtSaveCopyAsComplete": "Salinan berkas sukses disimpan", "DE.Controllers.Main.txtScheme_Aspect": "Aspek", "DE.Controllers.Main.txtScheme_Blue": "Biru", "DE.Controllers.Main.txtScheme_Blue_Green": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtScheme_Blue_II": "Biru II", "DE.Controllers.Main.txtScheme_Blue_Warm": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "DE.Controllers.Main.txtScheme_Green": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtScheme_Green_Yellow": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtScheme_Marquee": "Marquee", "DE.Controllers.Main.txtScheme_Median": "Median", "DE.Controllers.Main.txtScheme_Office": "Office", "DE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "DE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "DE.Controllers.Main.txtScheme_Orange": "Jingga", "DE.Controllers.Main.txtScheme_Orange_Red": "Jingga Merah", "DE.Controllers.Main.txtScheme_Paper": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtScheme_Red": "<PERSON><PERSON>", "DE.Controllers.Main.txtScheme_Red_Orange": "<PERSON><PERSON>", "DE.Controllers.Main.txtScheme_Red_Violet": "<PERSON><PERSON>", "DE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "DE.Controllers.Main.txtScheme_Violet": "<PERSON><PERSON>", "DE.Controllers.Main.txtScheme_Violet_II": "Ungu II", "DE.Controllers.Main.txtScheme_Yellow": "<PERSON><PERSON>", "DE.Controllers.Main.txtScheme_Yellow_Orange": "Kuning Jingga", "DE.Controllers.Main.txtSection": "-<PERSON><PERSON>", "DE.Controllers.Main.txtSeries": "Seri", "DE.Controllers.Main.txtShape_accentBorderCallout1": "Garis Callout 1 (<PERSON> dan <PERSON>)", "DE.Controllers.Main.txtShape_accentBorderCallout2": "Garis Callout 2 (<PERSON> dan <PERSON>)", "DE.Controllers.Main.txtShape_accentBorderCallout3": "Garis Callout 3 (<PERSON> dan <PERSON>)", "DE.Controllers.Main.txtShape_accentCallout1": "Garis Callout 1 (Accent Bar)", "DE.Controllers.Main.txtShape_accentCallout2": "Garis Callout 2 (<PERSON><PERSON>ent Bar)", "DE.Controllers.Main.txtShape_accentCallout3": "Garis Callout 3 (<PERSON><PERSON>ent Bar)", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "Tombol Kembali atau Sebelumnya", "DE.Controllers.Main.txtShape_actionButtonBeginning": "<PERSON><PERSON> awal", "DE.Controllers.Main.txtShape_actionButtonBlank": "Tombol kosong", "DE.Controllers.Main.txtShape_actionButtonDocument": "Tombol dokumen", "DE.Controllers.Main.txtShape_actionButtonEnd": "<PERSON><PERSON> akhir", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON><PERSON> Selanjutnya", "DE.Controllers.Main.txtShape_actionButtonHelp": "Tombol <PERSON>", "DE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_actionButtonInformation": "Tombol informasi", "DE.Controllers.Main.txtShape_actionButtonMovie": "Tombol Movie", "DE.Controllers.Main.txtShape_actionButtonReturn": "Tombol Kembali", "DE.Controllers.Main.txtShape_actionButtonSound": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_arc": "Arc", "DE.Controllers.Main.txtShape_bentArrow": "Panah <PERSON>", "DE.Controllers.Main.txtShape_bentConnector5": "Konektor Siku", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "Panah Konektor Siku", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Panah Ganda Konektor Siku", "DE.Controllers.Main.txtShape_bentUpArrow": "Panah Kelok Atas", "DE.Controllers.Main.txtShape_bevel": "Miring", "DE.Controllers.Main.txtShape_blockArc": "Block Arc", "DE.Controllers.Main.txtShape_borderCallout1": "Garis Callout 1", "DE.Controllers.Main.txtShape_borderCallout2": "<PERSON><PERSON> Callout 2", "DE.Controllers.Main.txtShape_borderCallout3": "<PERSON><PERSON> Callout 3", "DE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_callout1": "Garis Callout 1 (No Border)", "DE.Controllers.Main.txtShape_callout2": "<PERSON><PERSON>out 2 (No <PERSON>)", "DE.Controllers.Main.txtShape_callout3": "<PERSON><PERSON> Callout 3 (No <PERSON>)", "DE.Controllers.Main.txtShape_can": "Bisa", "DE.Controllers.Main.txtShape_chevron": "Chevron", "DE.Controllers.Main.txtShape_chord": "Chord", "DE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_cloud": "Cloud", "DE.Controllers.Main.txtShape_cloudCallout": "Cloud Callout", "DE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_cube": "Ku<PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3": "Konektor <PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Panah Konektor <PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Panah Ganda Konektor <PERSON>", "DE.Controllers.Main.txtShape_curvedDownArrow": "Panah Kelok Bawah", "DE.Controllers.Main.txtShape_curvedLeftArrow": "Panah Kelok Kiri", "DE.Controllers.Main.txtShape_curvedRightArrow": "Panah Kelok <PERSON>", "DE.Controllers.Main.txtShape_curvedUpArrow": "Panah Kelok Atas", "DE.Controllers.Main.txtShape_decagon": "Decagon", "DE.Controllers.Main.txtShape_diagStripe": "Strip Diagonal", "DE.Controllers.Main.txtShape_diamond": "Diamond", "DE.Controllers.Main.txtShape_dodecagon": "Dodecagon", "DE.Controllers.Main.txtShape_donut": "Donut", "DE.Controllers.Main.txtShape_doubleWave": "Gelombang ganda", "DE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON> turun", "DE.Controllers.Main.txtShape_downArrowCallout": "Seranta Panah Bawah", "DE.Controllers.Main.txtShape_ellipse": "Elips", "DE.Controllers.Main.txtShape_ellipseRibbon": "Pita Kelok Bawah", "DE.Controllers.Main.txtShape_ellipseRibbon2": "Pita Kelok Atas", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "Diagram Alir: Proses alternatif", "DE.Controllers.Main.txtShape_flowChartCollate": "Diag<PERSON> Alir: Collate", "DE.Controllers.Main.txtShape_flowChartConnector": "Diagram Alir: <PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartDecision": "Diagram Alir: <PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartDelay": "Diagram Alir: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartDisplay": "Diagram Alir: <PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartDocument": "Diagram Alir: <PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartExtract": "Diagram Alir: Ekstrak", "DE.Controllers.Main.txtShape_flowChartInputOutput": "Diagram Alir: Data", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "Diagram Alir: <PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "Diagram Alir: Magnetic Disk", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "Diagram Alir: Direct Access Storage", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "Diagram Alir: Sequential Access Storage", "DE.Controllers.Main.txtShape_flowChartManualInput": "Diagram Alir: Input Manual", "DE.Controllers.Main.txtShape_flowChartManualOperation": "Diagram Alir: Operasi Manual", "DE.Controllers.Main.txtShape_flowChartMerge": "Diagram Alir: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartMultidocument": "Diagram Alir: Multidokumen ", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "Diagram Alir: Off-page Penghubung", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "Diagram Alir: Stored Data", "DE.Controllers.Main.txtShape_flowChartOr": "Diagram Alir: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Diagram Alir: Predefined Process", "DE.Controllers.Main.txtShape_flowChartPreparation": "Diagram Alir: <PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartProcess": "Diagram Alir: Proses", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "Diagram Alir: <PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "Diagram Alir: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartSort": "Diagram Alir: <PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "Diagram Alir: Summing Junction", "DE.Controllers.Main.txtShape_flowChartTerminator": "Diagram Alir: Terminator", "DE.Controllers.Main.txtShape_foldedCorner": "Sudut Folder", "DE.Controllers.Main.txtShape_frame": "Kerang<PERSON>", "DE.Controllers.Main.txtShape_halfFrame": "Setengah Bingkai", "DE.Controllers.Main.txtShape_heart": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_heptagon": "Heptagon", "DE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_homePlate": "Pentagon", "DE.Controllers.Main.txtShape_horizontalScroll": "Gulir horizontal", "DE.Controllers.Main.txtShape_irregularSeal1": "Ledakan 1", "DE.Controllers.Main.txtShape_irregularSeal2": "Ledakan 2", "DE.Controllers.Main.txtShape_leftArrow": "Panah kiri", "DE.Controllers.Main.txtShape_leftArrowCallout": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_leftRightArrow": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_leftRightUpArrow": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_leftUpArrow": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_lightningBolt": "Petir", "DE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_lineWithArrow": "Panah", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON> ganda", "DE.Controllers.Main.txtShape_mathDivide": "Divisi", "DE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_mathMinus": "Minus", "DE.Controllers.Main.txtShape_mathMultiply": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_mathNotEqual": "Tidak Sama", "DE.Controllers.Main.txtShape_mathPlus": "Plus", "DE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_noSmoking": "Simbol \"Tidak\"", "DE.Controllers.Main.txtShape_notchedRightArrow": "Panah Takik <PERSON>", "DE.Controllers.Main.txtShape_octagon": "Oktagon", "DE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "DE.Controllers.Main.txtShape_pentagon": "Pentagon", "DE.Controllers.Main.txtShape_pie": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_plaque": "Plus", "DE.Controllers.Main.txtShape_plus": "Plus", "DE.Controllers.Main.txtShape_polyline1": "Scribble", "DE.Controllers.Main.txtShape_polyline2": "<PERSON><PERSON><PERSON> be<PERSON>", "DE.Controllers.Main.txtShape_quadArrow": "Panah Empat Mata", "DE.Controllers.Main.txtShape_quadArrowCallout": "Seranta Panah Empat Mata", "DE.Controllers.Main.txtShape_rect": "Kotak", "DE.Controllers.Main.txtShape_ribbon": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_ribbon2": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_rightArrow": "Tanda Panah ke Kanan", "DE.Controllers.Main.txtShape_rightArrowCallout": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_round1Rect": "<PERSON>segi <PERSON> Sudut Lengkung Single", "DE.Controllers.Main.txtShape_round2DiagRect": "<PERSON><PERSON><PERSON> Sudut Lengkung Diagonal", "DE.Controllers.Main.txtShape_round2SameRect": "<PERSON><PERSON><PERSON> Sudut Lengkung Sama Sisi", "DE.Controllers.Main.txtShape_roundRect": "<PERSON><PERSON><PERSON> Sudu<PERSON>", "DE.Controllers.Main.txtShape_rtTriangle": "Segitiga Siku-Siku", "DE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_snip1Rect": "Snip Persegi <PERSON> Sudut Single", "DE.Controllers.Main.txtShape_snip2DiagRect": "Snip Persegi Panjang Sudut Lengkung Diagonal", "DE.Controllers.Main.txtShape_snip2SameRect": "Snip Persegi Panjang Sudut Lengkung Sama Sisi", "DE.Controllers.Main.txtShape_snipRoundRect": "<PERSON><PERSON><PERSON> dan <PERSON> Sudut Lengkung Single", "DE.Controllers.Main.txtShape_spline": "<PERSON>g<PERSON><PERSON>", "DE.Controllers.Main.txtShape_star10": "Bintang Titik-10", "DE.Controllers.Main.txtShape_star12": "Bintang Titik-12", "DE.Controllers.Main.txtShape_star16": "Bintang Titik-16", "DE.Controllers.Main.txtShape_star24": "Bintang Titik-24", "DE.Controllers.Main.txtShape_star32": "Bintang Titik-32", "DE.Controllers.Main.txtShape_star4": "Bintang Titik-4", "DE.Controllers.Main.txtShape_star5": "Bintang Titik-5", "DE.Controllers.Main.txtShape_star6": "Bintang Titik-6", "DE.Controllers.Main.txtShape_star7": "Bintang Titik-7", "DE.Controllers.Main.txtShape_star8": "Bintang Titik-8", "DE.Controllers.Main.txtShape_stripedRightArrow": "<PERSON><PERSON>-<PERSON><PERSON>", "DE.Controllers.Main.txtShape_sun": "Matahari", "DE.Controllers.Main.txtShape_teardrop": "Teardrop", "DE.Controllers.Main.txtShape_textRect": "Kotak teks", "DE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "DE.Controllers.Main.txtShape_triangle": "Segitiga", "DE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON> naik", "DE.Controllers.Main.txtShape_upArrowCallout": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_upDownArrow": "Panah Atas Bawah", "DE.Controllers.Main.txtShape_uturnArrow": "<PERSON><PERSON> putar balik", "DE.Controllers.Main.txtShape_verticalScroll": "<PERSON><PERSON><PERSON> vertikal", "DE.Controllers.Main.txtShape_wave": "Gelombang", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "Callout Oval", "DE.Controllers.Main.txtShape_wedgeRectCallout": "Callout <PERSON>", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Callout <PERSON><PERSON><PERSON> Sudut <PERSON>", "DE.Controllers.Main.txtStarsRibbons": "Bintang & Pita", "DE.Controllers.Main.txtStyle_Book_Title": "<PERSON><PERSON><PERSON> buku", "DE.Controllers.Main.txtStyle_Caption": "Caption", "DE.Controllers.Main.txtStyle_Default_Paragraph_Font": "<PERSON><PERSON><PERSON> paragraf baku", "DE.Controllers.Main.txtStyle_Emphasis": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtStyle_endnote_reference": "<PERSON>cuan catatan akhir", "DE.Controllers.Main.txtStyle_endnote_text": "Teks catatan akhir", "DE.Controllers.Main.txtStyle_footnote_reference": "Acuan catatan kaki", "DE.Controllers.Main.txtStyle_footnote_text": "Teks catatan kaki", "DE.Controllers.Main.txtStyle_Heading_1": "Heading 1", "DE.Controllers.Main.txtStyle_Heading_2": "Heading 2", "DE.Controllers.Main.txtStyle_Heading_3": "Heading 3", "DE.Controllers.Main.txtStyle_Heading_4": "Heading 4", "DE.Controllers.Main.txtStyle_Heading_5": "Heading 5", "DE.Controllers.Main.txtStyle_Heading_6": "Heading 6", "DE.Controllers.Main.txtStyle_Heading_7": "Heading 7", "DE.Controllers.Main.txtStyle_Heading_8": "Heading 8", "DE.Controllers.Main.txtStyle_Heading_9": "Heading 9", "DE.Controllers.Main.txtStyle_Intense_Emphasis": "Intense emphasis", "DE.Controllers.Main.txtStyle_Intense_Quote": "Quote <PERSON>", "DE.Controllers.Main.txtStyle_Intense_Reference": "Intense reference", "DE.Controllers.Main.txtStyle_List_Paragraph": "<PERSON><PERSON><PERSON> da<PERSON>", "DE.Controllers.Main.txtStyle_No_List": "No list", "DE.Controllers.Main.txtStyle_No_Spacing": "Tanpa Spacing", "DE.Controllers.Main.txtStyle_Normal": "Normal", "DE.Controllers.Main.txtStyle_Quote": "<PERSON><PERSON>", "DE.Controllers.Main.txtStyle_Strong": "Strong", "DE.Controllers.Main.txtStyle_Subtitle": "Subtitle", "DE.Controllers.Main.txtStyle_Subtle_Emphasis": "Subtle emphasis", "DE.Controllers.Main.txtStyle_Subtle_Reference": "Subtle reference", "DE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtSyntaxError": "Kesalahan sintaks", "DE.Controllers.Main.txtTableInd": "<PERSON><PERSON>s tabel tidak boleh nol", "DE.Controllers.Main.txtTableOfContents": "<PERSON><PERSON><PERSON> isi", "DE.Controllers.Main.txtTableOfFigures": "Daftar gambar", "DE.Controllers.Main.txtTOCHeading": "Heading TOC", "DE.Controllers.Main.txtTooLarge": "Nomor Terlalu Besar untuk Diformat", "DE.Controllers.Main.txtTypeEquation": "<PERSON><PERSON> disini.", "DE.Controllers.Main.txtUndefBookmark": "Bookmark Tidak Terdefinisi", "DE.Controllers.Main.txtXAxis": "Sumbu X", "DE.Controllers.Main.txtYAxis": "Sumbu Y", "DE.Controllers.Main.txtZeroDivide": "Dibagi nol", "DE.Controllers.Main.unknownErrorText": "Kesalahan tidak diketahui.", "DE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON> tidak didukung.", "DE.Controllers.Main.uploadDocExtMessage": "Format dokumen tidak diketahui.", "DE.Controllers.Main.uploadDocFileCountMessage": "Tidak ada dokumen yang diupload.", "DE.Controllers.Main.uploadDocSizeMessage": "Batas ukuran maksimum dokumen terlampaui.", "DE.Controllers.Main.uploadImageExtMessage": "Format gambar tidak dikenal.", "DE.Controllers.Main.uploadImageFileCountMessage": "Tidak ada gambar yang di<PERSON>h.", "DE.Controllers.Main.uploadImageSizeMessage": "Melebihi ukuran maksimal file. Ukuran maksimum adalah 25 MB.", "DE.Controllers.Main.uploadImageTextText": "Mengunggah gambar...", "DE.Controllers.Main.uploadImageTitleText": "Mengunggah Gambar", "DE.Controllers.Main.waitText": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.warnBrowserIE9": "Aplikasi ini tidak berjalan dengan baik di IE9. Gunakan IE10 atau versi yang terbaru.", "DE.Controllers.Main.warnBrowserZoom": "Pengaturan pembesaran tampilan pada peramban Anda saat ini tidak didukung sepenuhnya. <PERSON><PERSON>an atur ulang ke pembesaran standar dengan menekan Ctrl+0.", "DE.Controllers.Main.warnLicenseAnonymous": "<PERSON><PERSON><PERSON> di<PERSON>lak untuk pengguna anonim.<br>Do<PERSON><PERSON> ini akan dibuka hanya untuk dilihat.", "DE.Controllers.Main.warnLicenseBefore": "Lisensi tidak aktif.<br>Harap hubungi administrator <PERSON><PERSON>.", "DE.Controllers.Main.warnLicenseExceeded": "Anda sudah mencapai batas untuk koneksi bersa<PERSON> ke %1 editor. Dokumen ini akan dibuka untuk dilihat saja.<br>Hubungi admin Anda untuk mempelajari lebih lanjut.", "DE.Controllers.Main.warnLicenseExp": "<PERSON><PERSON>si Anda sudah kedal<PERSON>.<br><PERSON><PERSON><PERSON> perbarui lisensi Anda dan segarkan halaman.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "<PERSON><PERSON><PERSON> ka<PERSON>.<br><PERSON><PERSON> tidak memiliki akses untuk editing dokumen secara keseluruhan.<br><PERSON><PERSON><PERSON> hubungi admin Anda.", "DE.Controllers.Main.warnLicenseLimitedRenewed": "Lisensi perlu diperbaharui.<br>Anda memiliki akses terbatas untuk edit dokumen.<br><PERSON><PERSON><PERSON> hubungi admin Anda untuk mendapatkan akses penuh", "DE.Controllers.Main.warnLicenseUsersExceeded": "<PERSON><PERSON> sudah mencapai batas user untuk %1 editor. Hubungi admin Anda untuk mempelajari lebih lanjut.", "DE.Controllers.Main.warnNoLicense": "<PERSON>a sudah mencapai batas untuk koneksi bersa<PERSON>an ke %1 editor. Dokumen ini akan dibuka untuk dilihat saja.<br>Hubungi %1 tim sales untuk syarat personal upgrade.", "DE.Controllers.Main.warnNoLicenseUsers": "Anda sudah mencapai batas user untuk %1 editor. Hubungi %1 tim sales untuk syarat personal upgrade.", "DE.Controllers.Main.warnProcessRightsChange": "Hak Anda untuk mengedit file ditolak.", "DE.Controllers.Main.warnStartFilling": "Form filling is in progress.<br>File editing is not currently available.", "DE.Controllers.Navigation.txtBeginning": "Awal dokumen", "DE.Controllers.Navigation.txtGotoBeginning": "Pergi ke awal dokumen", "DE.Controllers.Print.textMarginsLast": "<PERSON><PERSON><PERSON>", "DE.Controllers.Print.txtCustom": "<PERSON><PERSON><PERSON>", "DE.Controllers.Print.txtPrintRangeInvalid": "Rentang cetak tidak valid", "DE.Controllers.Print.txtPrintRangeSingleRange": "Masukkan satu nomor halaman atau satu rentang halaman (misalnya, 5-12). Atau Anda bisa Cetak ke PDF.", "DE.Controllers.Search.notcriticalErrorTitle": "Peringatan", "DE.Controllers.Search.textNoTextFound": "Data yang Anda cari tidak ditemukan. <PERSON>lakan atur opsi pencarian <PERSON>a.", "DE.Controllers.Search.textReplaceSkipped": "Penggantian telah dilakukan. {0} kemunculan telah dilewatkan.", "DE.Controllers.Search.textReplaceSuccess": "Pencarian telah dilakukan. {0} kemunculan telah diganti", "DE.Controllers.Search.warnReplaceString": "{0} bukan karakter khusus yang valid untuk kotak Ganti Dengan.", "DE.Controllers.Statusbar.textDisconnect": "<b><PERSON><PERSON><PERSON><PERSON> terput<PERSON></b><br><PERSON><PERSON><PERSON>gh<PERSON>. Silakan periksa pengaturan koneksi.", "DE.Controllers.Statusbar.textHasChanges": "Perubahan baru sudah dilacak", "DE.Controllers.Statusbar.textSetTrackChanges": "<PERSON>a di <PERSON>", "DE.Controllers.Statusbar.textTrackChanges": "Dokumen dibuka dengan Lacak Perubahan aktif", "DE.Controllers.Statusbar.tipReview": "<PERSON><PERSON>", "DE.Controllers.Statusbar.zoomText": "Perbesar {0}%", "DE.Controllers.Toolbar.confirmAddFontName": "Font yang akan Anda simpan tidak tersedia di perangkat sekarang.<br>Style teks akan ditampilkan menggunakan salah satu font sistem, font yang disimpan akan digunakan jika sudah tersedia.<br><PERSON><PERSON><PERSON><PERSON> Anda ingin melanjutkan?", "DE.Controllers.Toolbar.dataUrl": "Paste URL data", "DE.Controllers.Toolbar.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "DE.Controllers.Toolbar.fileUrl": "Paste a file URL", "DE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "DE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "DE.Controllers.Toolbar.helpRtlDir": "Toggle between right-to-left (RTL) and left-to-right (LTR) text direction in  your documents.", "DE.Controllers.Toolbar.helpRtlDirHeader": "Switch text direction", "DE.Controllers.Toolbar.notcriticalErrorTitle": "Peringatan", "DE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textBracket": "<PERSON><PERSON>", "DE.Controllers.Toolbar.textConvertFormDownload": "Unduh berkas sebagai suatu formulir PDF yang dapat diisi agar dapat mengisinya.", "DE.Controllers.Toolbar.textConvertFormSave": "Save file as a fillable PDF form to be able to fill it out.", "DE.Controllers.Toolbar.textDownloadPdf": "Unduh PDF", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "Anda perlu menyatakan URL.", "DE.Controllers.Toolbar.textFieldExample": "Example of writing code: TIM<PERSON> \\@ \"dddd, MMMM d, yyyy\"", "DE.Controllers.Toolbar.textFieldLabel": "<PERSON><PERSON> ruas", "DE.Controllers.Toolbar.textFieldTitle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textFontSizeErr": "Input yang dimasukkan salah.<br><PERSON><PERSON><PERSON> ma<PERSON>kkan input numerik antara 1 dan 300", "DE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textGroup": "Grup", "DE.Controllers.Toolbar.textInsert": "<PERSON>sip<PERSON>", "DE.Controllers.Toolbar.textIntegral": "Integral", "DE.Controllers.Toolbar.textLargeOperator": "Operator <PERSON>", "DE.Controllers.Toolbar.textLimitAndLog": "<PERSON>it dan logaritma", "DE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textOperator": "Operator", "DE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textRecentlyUsed": "<PERSON><PERSON>", "DE.Controllers.Toolbar.textSavePdf": "Simpan sebagai PDF", "DE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textSymbols": "Simbol", "DE.Controllers.Toolbar.textTabForms": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textWarning": "Peringatan", "DE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON>-<PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_ArrowL": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON>da Panah ke Kanan <PERSON>as", "DE.Controllers.Toolbar.txtAccent_Bar": "Palang", "DE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "DE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON> (dengan placeholder)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Kotak Formula(Contoh)", "DE.Controllers.Toolbar.txtAccent_Check": "Periksa", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "<PERSON><PERSON> At<PERSON>", "DE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "ABC Dengan Overbar", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y dengan overbar", "DE.Controllers.Toolbar.txtAccent_DDDot": "Triple Dot", "DE.Controllers.Toolbar.txtAccent_DDot": "Titik Dua", "DE.Controllers.Toolbar.txtAccent_Dot": "Titik", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "Overbar Ganda", "DE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_GroupBot": "Pengelompokan Karakter Di Bawah", "DE.Controllers.Toolbar.txtAccent_GroupTop": "Pengelompokan Karakter Di Atas", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "Panah Tiga Kiri Atas", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON> kanan di bawah", "DE.Controllers.Toolbar.txtAccent_Hat": "<PERSON>i", "DE.Controllers.Toolbar.txtAccent_Smile": "Prosodi", "DE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "<PERSON>da kurung dengan pemisah", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "<PERSON>da kurung dengan pemisah", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "<PERSON>da kurung dengan pemisah", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON> (<PERSON><PERSON>)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "Kasus (Tiga Kondisi)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "Tumpuk objek", "DE.Controllers.Toolbar.txtBracket_Custom_4": "Tumpuk objek", "DE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Custom_6": "Koefisien binomial", "DE.Controllers.Toolbar.txtBracket_Custom_7": "Koefisien binomial dalam kurung sudut", "DE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON> vertikal", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "<PERSON>da kurung dengan pemisah", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtDownload": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON><PERSON> miring", "DE.Controllers.Toolbar.txtFractionDifferential_1": "dx per dy", "DE.Controllers.Toolbar.txtFractionDifferential_2": "Diferensial", "DE.Controllers.Toolbar.txtFractionDifferential_3": "Diferensial", "DE.Controllers.Toolbar.txtFractionDifferential_4": "Diferensial", "DE.Controllers.Toolbar.txtFractionHorizontal": "Pecahan Linear", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi Dibagi 2", "DE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON>han kecil", "DE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "Fungsi Kosin Hiperbolik Terbalik", "DE.Controllers.Toolbar.txtFunction_1_Cot": "Fungsi Kotangen Terbalik", "DE.Controllers.Toolbar.txtFunction_1_Coth": "Fungsi Kotangen Hiperbolik Terbalik", "DE.Controllers.Toolbar.txtFunction_1_Csc": "<PERSON><PERSON><PERSON> Ko<PERSON>s <PERSON>", "DE.Controllers.Toolbar.txtFunction_1_Csch": "Fungsi Kosekan Hiperbolik Terbalik", "DE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_1_Sech": "Fungsi Sekans Hiperbolik Terbalik", "DE.Controllers.Toolbar.txtFunction_1_Sin": "Fungsi Sin Terbalik", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "Fungsi Sin Hiperbolik Terbalik", "DE.Controllers.Toolbar.txtFunction_1_Tan": "Fungsi Tangen Terbalik", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "Fungsi Tangen Hiperbolik Terbalik", "DE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Cosh": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Coth": "Fungsi Kotangen Hiperbolik", "DE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Csch": "Fungsi Kosekans Hiperbolik", "DE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "Tangent formula", "DE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON> sekan", "DE.Controllers.Toolbar.txtFunction_Sech": "Fungsi Sekans Hiperbolik", "DE.Controllers.Toolbar.txtFunction_Sin": "Fungsi sinus", "DE.Controllers.Toolbar.txtFunction_Sinh": "Fungsi Sin Hiperbolik", "DE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON>i tangen", "DE.Controllers.Toolbar.txtFunction_Tanh": "Fungsi Tangen Hiperbolik", "DE.Controllers.Toolbar.txtIntegral": "Integral", "DE.Controllers.Toolbar.txtIntegral_dtheta": "Theta Diferensial", "DE.Controllers.Toolbar.txtIntegral_dx": "Diferensial x", "DE.Controllers.Toolbar.txtIntegral_dy": "Diferensial y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "DE.Controllers.Toolbar.txtIntegralDouble": "Integral Ganda", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Integral Ganda", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Integral Ganda", "DE.Controllers.Toolbar.txtIntegralOriented": "Integral <PERSON>ur", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integral <PERSON>ur", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON><PERSON> integral", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON> integral", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON><PERSON> integral", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integral <PERSON>ur", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume integral", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volume integral", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral volume dengan batas", "DE.Controllers.Toolbar.txtIntegralSubSup": "Integral dengan batas", "DE.Controllers.Toolbar.txtIntegralTriple": "Triple integral", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple integral", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple integral", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Wedge", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Wedge", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Wedge", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Wedge", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Wedge", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ko-Produk", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Ko-produk dengan batas bawah", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Ko-produk dengan batasan", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Ko-Produk", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Ko-Produk", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON> produk", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Contoh union", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Perpotongan dengan limit subskrip/superskrip", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "Produk", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON>duk dengan batas bawah", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produk dengan batas", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produk", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produk", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Penju<PERSON>lahan dengan batas subskrip/superskrip", "DE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union dengan batas subskrip/superskrip", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "DE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritma Natural", "DE.Controllers.Toolbar.txtLimitLog_Log": "Logarit<PERSON>", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarit<PERSON>", "DE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "DE.Controllers.Toolbar.txtMarginsH": "Margin atas dan bawah terlalu jauh untuk halaman setinggi ini", "DE.Controllers.Toolbar.txtMarginsW": "Margin kiri dan kanan terlalu besar dengan lebar halaman yang ada", "DE.Controllers.Toolbar.txtMatrix_1_2": "matriks kosong 1x2", "DE.Controllers.Toolbar.txtMatrix_1_3": "matriks kosong 1x3", "DE.Controllers.Toolbar.txtMatrix_2_1": "matriks kosong 2x1", "DE.Controllers.Toolbar.txtMatrix_2_2": "matriks kosong 2x2", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matriks 2 kali 2 kosong dalam bilah vertikal ganda", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Determinan 2 kali 2 kosong", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matriks 2 kali 2 kosong dalam kurung", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON>gan <PERSON>", "DE.Controllers.Toolbar.txtMatrix_2_3": "matriks kosong 2x3", "DE.Controllers.Toolbar.txtMatrix_3_1": "matriks kosong 3x1", "DE.Controllers.Toolbar.txtMatrix_3_2": "matriks kosong 3x2", "DE.Controllers.Toolbar.txtMatrix_3_3": "matriks kosong 3x3", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON> garis dasar", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "Titik Tengah", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Titik Diagonal", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Titik Vertikal", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "Sparse Matrix", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "Sparse Matrix", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "Matriks identitas 2x2 dengan nol", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matriks identitas 2x2 dengan sel diagonal kosong", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "Matriks identitas 3x3 dengan nol", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriks identitas 3x3 dengan sel diagonal kosong", "DE.Controllers.Toolbar.txtNeedDownload": "PDF viewer can only save new changes in separate file copies. It doesn't support co-editing and other users won't see your changes unless you share a new file version.", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON>da Panah <PERSON>-<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON>-<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Panah <PERSON>", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON> kanan di bawah", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON>da Panah ke Kanan <PERSON>as", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "Titik Dua", "DE.Controllers.Toolbar.txtOperator_Custom_1": "Yields", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Hasil <PERSON>", "DE.Controllers.Toolbar.txtOperator_Definition": "Setara Menurut De<PERSON>isi", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta Setara Dengan", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON>da Panah <PERSON>-<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON>-<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Panah <PERSON>", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON> kanan di bawah", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON>da Panah ke Kanan <PERSON>as", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "Setara Setara", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Sama <PERSON>", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON> dengan pangkat", "DE.Controllers.Toolbar.txtRadicalRoot_3": "Akar Pangkat Tiga", "DE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON> pangkat dua", "DE.Controllers.Toolbar.txtSaveCopy": "<PERSON>mp<PERSON> salinan", "DE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_3": "x kuadrat", "DE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptSub": "Subskrip", "DE.Controllers.Toolbar.txtScriptSubSup": "Subskrip-SuperskripKiri", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "Subskrip-SuperskripKiri", "DE.Controllers.Toolbar.txtScriptSup": "Superskrip", "DE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_additional": "Komplemen", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "DE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_ast": "Operator tanda bintang", "DE.Controllers.Toolbar.txtSymbol_beta": "Beta", "DE.Controllers.Toolbar.txtSymbol_beth": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_bullet": "Operator butir", "DE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_cbrt": "Akar Pangkat Tiga", "DE.Controllers.Toolbar.txtSymbol_cdots": "Elipsis <PERSON> Horisontal", "DE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "<PERSON><PERSON><PERSON><PERSON> sama dengan", "DE.Controllers.Toolbar.txtSymbol_cup": "Union", "DE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "Tanda Pembagi", "DE.Controllers.Toolbar.txtSymbol_downarrow": "Panah Bawa<PERSON>", "DE.Controllers.Toolbar.txtSymbol_emptyset": "Himpunan Kosong", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "DE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_equiv": "Identik Dengan", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "<PERSON> di sana", "DE.Controllers.Toolbar.txtSymbol_factorial": "Faktorial", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "Derajat Fahrenheit", "DE.Controllers.Toolbar.txtSymbol_forall": "Untuk Semua", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "DE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON>h dari atau sama dengan", "DE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_in": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_infinity": "Tak Terbatas", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_leq": "<PERSON><PERSON> atau <PERSON>", "DE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_minus": "Minus", "DE.Controllers.Toolbar.txtSymbol_mp": "Minus Plus", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "Tidak Sama Den<PERSON>", "DE.Controllers.Toolbar.txtSymbol_ni": "Sertakan sebagai Anggota", "DE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_notexists": "Tidak ada di sana", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omikron", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "Diferensial Parsial", "DE.Controllers.Toolbar.txtSymbol_percent": "Persentase", "DE.Controllers.Toolbar.txtSymbol_phi": "Phi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "Plus", "DE.Controllers.Toolbar.txtSymbol_pm": "Plus Minus", "DE.Controllers.Toolbar.txtSymbol_propto": "Proposional Dengan", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_qed": "<PERSON>em<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_rddots": "Elipsis diagonal kanan atas", "DE.Controllers.Toolbar.txtSymbol_rho": "Rho", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "Tanda Panah ke Kanan", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON> karena itu", "DE.Controllers.Toolbar.txtSymbol_theta": "Theta", "DE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "Varian Epsilon", "DE.Controllers.Toolbar.txtSymbol_varphi": "V<PERSON>", "DE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Varian <PERSON>", "DE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON> vertikal", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Toolbar.txtUntitled": "<PERSON><PERSON>", "DE.Controllers.Viewport.textFitPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Viewport.textFitWidth": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Viewport.txtDarkMode": "Mode gelap", "DE.Views.BookmarksDialog.textAdd": "Tambahkan", "DE.Views.BookmarksDialog.textBookmarkName": "Nama bookmark", "DE.Views.BookmarksDialog.textClose": "<PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textCopy": "<PERSON><PERSON>", "DE.Views.BookmarksDialog.textDelete": "Hapus", "DE.Views.BookmarksDialog.textGetLink": "<PERSON><PERSON><PERSON><PERSON>an", "DE.Views.BookmarksDialog.textGoto": "<PERSON>gi ke", "DE.Views.BookmarksDialog.textHidden": "Bookmarks tersembunyi", "DE.Views.BookmarksDialog.textLocation": "<PERSON><PERSON>", "DE.Views.BookmarksDialog.textName": "<PERSON><PERSON>", "DE.Views.BookmarksDialog.textSort": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "DE.Views.BookmarksDialog.textTitle": "Bookmarks", "DE.Views.BookmarksDialog.txtInvalidName": "Nama bookmark hanya bisa dari alfa<PERSON>, an<PERSON><PERSON>, dan garis bawah, dan harus dimulai dengan alfabet", "DE.Views.CaptionDialog.textAdd": "Tambah label", "DE.Views.CaptionDialog.textAfter": "Sesudah", "DE.Views.CaptionDialog.textBefore": "Sebelum", "DE.Views.CaptionDialog.textCaption": "Caption", "DE.Views.CaptionDialog.textChapter": "Bab dimulai dengan style", "DE.Views.CaptionDialog.textChapterInc": "Ser<PERSON>kan nomor bab", "DE.Views.CaptionDialog.textColon": "Titik dua", "DE.Views.CaptionDialog.textDash": "garis putus-putus", "DE.Views.CaptionDialog.textDelete": "Hapus label", "DE.Views.CaptionDialog.textEquation": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textExamples": "Contoh <PERSON> 2-A, Gambar 1.IV", "DE.Views.CaptionDialog.textExclude": "Tidak sertakan label dari caption", "DE.Views.CaptionDialog.textFigure": "Gambar", "DE.Views.CaptionDialog.textHyphen": "hyphen", "DE.Views.CaptionDialog.textInsert": "<PERSON>sip<PERSON>", "DE.Views.CaptionDialog.textLabel": "Label", "DE.Views.CaptionDialog.textLabelError": "Label tidak boleh kosong.", "DE.Views.CaptionDialog.textLongDash": "dash panjang", "DE.Views.CaptionDialog.textNumbering": "Penomoran", "DE.Views.CaptionDialog.textPeriod": "periode", "DE.Views.CaptionDialog.textSeparator": "Gunakan separator", "DE.Views.CaptionDialog.textTable": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textTitle": "<PERSON>si<PERSON><PERSON> caption", "DE.Views.CellsAddDialog.textCol": "<PERSON><PERSON><PERSON>", "DE.Views.CellsAddDialog.textDown": "<PERSON> bawah cursor", "DE.Views.CellsAddDialog.textLeft": "Ke kiri", "DE.Views.CellsAddDialog.textRight": "<PERSON>nan", "DE.Views.CellsAddDialog.textRow": "<PERSON><PERSON>", "DE.Views.CellsAddDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "DE.Views.CellsAddDialog.textUp": "Di atas kursor", "DE.Views.ChartSettings.text3dDepth": "<PERSON><PERSON><PERSON> (% dari dasar)", "DE.Views.ChartSettings.text3dHeight": "Tinggi (% dari dasar)", "DE.Views.ChartSettings.text3dRotation": "Rotasi 3D", "DE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "DE.Views.ChartSettings.textAutoscale": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textChartType": "Ubah Tipe Bagan", "DE.Views.ChartSettings.textDefault": "<PERSON><PERSON><PERSON> baku", "DE.Views.ChartSettings.textDown": "<PERSON>wa<PERSON>", "DE.Views.ChartSettings.textEditData": "Edit data", "DE.Views.ChartSettings.textHeight": "Tingg<PERSON>", "DE.Views.ChartSettings.textLeft": "<PERSON><PERSON>", "DE.Views.ChartSettings.textNarrow": "Perkecil bidang tampilan", "DE.Views.ChartSettings.textOriginalSize": "Ukuran Sebenarnya", "DE.Views.ChartSettings.textPerspective": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textRight": "<PERSON><PERSON>", "DE.Views.ChartSettings.textRightAngle": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textSize": "Ukuran", "DE.Views.ChartSettings.textStyle": "Model", "DE.Views.ChartSettings.textUndock": "Lepaskan dari panel", "DE.Views.ChartSettings.textUp": "<PERSON><PERSON>", "DE.Views.ChartSettings.textWiden": "Perlebar bidang tampilan", "DE.Views.ChartSettings.textWidth": "<PERSON><PERSON>", "DE.Views.ChartSettings.textWrap": "Bentuk Potongan", "DE.Views.ChartSettings.textX": "Rotasi X", "DE.Views.ChartSettings.textY": "R<PERSON><PERSON> Y", "DE.Views.ChartSettings.txtBehind": "Di belakang teks", "DE.Views.ChartSettings.txtInFront": "<PERSON> depan <PERSON>", "DE.Views.ChartSettings.txtInline": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.txtSquare": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.txtThrough": "Tembus", "DE.Views.ChartSettings.txtTight": "Ketat", "DE.Views.ChartSettings.txtTitle": "Bagan", "DE.Views.ChartSettings.txtTopAndBottom": "Atas dan bawah", "DE.Views.ControlSettingsDialog.strGeneral": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textAdd": "Tambahkan", "DE.Views.ControlSettingsDialog.textAppearance": "Penampilan", "DE.Views.ControlSettingsDialog.textApplyAll": "Terapkan untuk semua", "DE.Views.ControlSettingsDialog.textBox": "Kotak Pembatas", "DE.Views.ControlSettingsDialog.textChange": "Sunting", "DE.Views.ControlSettingsDialog.textCheckbox": "Kotak centang", "DE.Views.ControlSettingsDialog.textChecked": "Simbol yang dicentang", "DE.Views.ControlSettingsDialog.textColor": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textCombobox": "Kotak combo", "DE.Views.ControlSettingsDialog.textDate": "Format Tanggal", "DE.Views.ControlSettingsDialog.textDelete": "Hapus", "DE.Views.ControlSettingsDialog.textDisplayName": "<PERSON><PERSON> ta<PERSON>", "DE.Views.ControlSettingsDialog.textDown": "<PERSON>wa<PERSON>", "DE.Views.ControlSettingsDialog.textDropDown": "List drop-down", "DE.Views.ControlSettingsDialog.textFormat": "<PERSON><PERSON><PERSON><PERSON> tanggal seperti ini", "DE.Views.ControlSettingsDialog.textLang": "Bahasa", "DE.Views.ControlSettingsDialog.textLock": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textName": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textNone": "Tidak ada", "DE.Views.ControlSettingsDialog.textPlaceholder": "Placeholder", "DE.Views.ControlSettingsDialog.textShowAs": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>ai", "DE.Views.ControlSettingsDialog.textSystemColor": "Sistem", "DE.Views.ControlSettingsDialog.textTag": "Tandai", "DE.Views.ControlSettingsDialog.textTitle": "Pengaturan kontrol konten", "DE.Views.ControlSettingsDialog.textUnchecked": "Simbol tidak dicentang", "DE.Views.ControlSettingsDialog.textUp": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textValue": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.tipChange": "Ganti simbol", "DE.Views.ControlSettingsDialog.txtLockDelete": "<PERSON><PERSON><PERSON> konten tidak bisa dihapus", "DE.Views.ControlSettingsDialog.txtLockEdit": "Konten tidak bisa diedit", "DE.Views.ControlSettingsDialog.txtRemContent": "<PERSON><PERSON> kontrol konten saat konten diedit", "DE.Views.CrossReferenceDialog.textAboveBelow": "Atas/bawah", "DE.Views.CrossReferenceDialog.textBookmark": "Bookmark", "DE.Views.CrossReferenceDialog.textBookmarkText": "Bookmark teks", "DE.Views.CrossReferenceDialog.textCaption": "<PERSON><PERSON><PERSON> caption", "DE.Views.CrossReferenceDialog.textEmpty": "Permintaan referensi kosong.", "DE.Views.CrossReferenceDialog.textEndnote": "Endnote", "DE.Views.CrossReferenceDialog.textEndNoteNum": "Nomor endnote", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "Nomor endnote (diformat)", "DE.Views.CrossReferenceDialog.textEquation": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textFigure": "Gambar", "DE.Views.CrossReferenceDialog.textFootnote": "Footnote", "DE.Views.CrossReferenceDialog.textHeading": "Headings", "DE.Views.CrossReferenceDialog.textHeadingNum": "Nomor heading", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "Nomor heading (konteks penuh)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "Nomor heading (tanpa penuh)", "DE.Views.CrossReferenceDialog.textHeadingText": "<PERSON><PERSON> heading", "DE.Views.CrossReferenceDialog.textIncludeAbove": "Sertakan di atas/bawah", "DE.Views.CrossReferenceDialog.textInsert": "<PERSON>sip<PERSON>", "DE.Views.CrossReferenceDialog.textInsertAs": "Sisipkan sebagai hyperlink", "DE.Views.CrossReferenceDialog.textLabelNum": "Hanya label dan nomor", "DE.Views.CrossReferenceDialog.textNoteNum": "Nomor footnote", "DE.Views.CrossReferenceDialog.textNoteNumForm": "Nomor footnote (diformat)", "DE.Views.CrossReferenceDialog.textOnlyCaption": "Hanya teks caption", "DE.Views.CrossReferenceDialog.textPageNum": "<PERSON><PERSON> halaman", "DE.Views.CrossReferenceDialog.textParagraph": "Barang yang dinomori", "DE.Views.CrossReferenceDialog.textParaNum": "<PERSON><PERSON> paragraf", "DE.Views.CrossReferenceDialog.textParaNumFull": "<PERSON><PERSON> paragraf (konteks penuh)", "DE.Views.CrossReferenceDialog.textParaNumNo": "<PERSON><PERSON> paragraf (tanpa konteks)", "DE.Views.CrossReferenceDialog.textSeparate": "Pisahkan nomor dengan", "DE.Views.CrossReferenceDialog.textTable": "<PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textText": "Teks paragraf", "DE.Views.CrossReferenceDialog.textWhich": "Untuk caption ini", "DE.Views.CrossReferenceDialog.textWhichBookmark": "Untuk bookmark ini", "DE.Views.CrossReferenceDialog.textWhichEndnote": "Untuk endnote ini", "DE.Views.CrossReferenceDialog.textWhichHeading": "Untuk heading ini", "DE.Views.CrossReferenceDialog.textWhichNote": "Untuk footnote ini", "DE.Views.CrossReferenceDialog.textWhichPara": "Untuk barang yang dinomorkan ini", "DE.Views.CrossReferenceDialog.txtReference": "<PERSON>sip<PERSON> referensi ke", "DE.Views.CrossReferenceDialog.txtTitle": "Cross-reference", "DE.Views.CrossReferenceDialog.txtType": "<PERSON><PERSON><PERSON> referensi", "DE.Views.CustomColumnsDialog.textColumns": "<PERSON><PERSON><PERSON>", "DE.Views.CustomColumnsDialog.textEqualWidth": "<PERSON><PERSON> kolom sama", "DE.Views.CustomColumnsDialog.textSeparator": "Pembagi kolom", "DE.Views.CustomColumnsDialog.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.CustomColumnsDialog.textTitleSpacing": "<PERSON><PERSON>", "DE.Views.CustomColumnsDialog.textWidth": "<PERSON><PERSON>", "DE.Views.DateTimeDialog.confirmDefault": "Atur format default untuk {0}: \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "Atur sesuai default", "DE.Views.DateTimeDialog.textFormat": "Format", "DE.Views.DateTimeDialog.textLang": "Bahasa", "DE.Views.DateTimeDialog.textUpdate": "Update secara otomatis", "DE.Views.DateTimeDialog.txtTitle": "Tanggal & waktu", "DE.Views.DocProtection.hintProtectDoc": "Proteksi dokumen", "DE.Views.DocProtection.txtDocProtectedComment": "Dokumen terproteksi.<br>Anda hanya bisa menyisipkan komentar pada dokumen ini.", "DE.Views.DocProtection.txtDocProtectedForms": "Dokumen terproteksi.<br>Anda hanya bisa mengisi formulir pada dokumen ini.", "DE.Views.DocProtection.txtDocProtectedTrack": "Dokumen terproteksi.<br><PERSON><PERSON> dapat menyunting dokumen ini, tapi semua perubahan akan dila<PERSON>k.", "DE.Views.DocProtection.txtDocProtectedView": "Dokumen terproteksi.<br>Anda hanya bisa melihat dokumen ini.", "DE.Views.DocProtection.txtDocUnlockDescription": "<PERSON><PERSON>kkan kata sandi untuk membuka proteksi dokumen", "DE.Views.DocProtection.txtProtectDoc": "Proteksi dokumen", "DE.Views.DocProtection.txtUnlockTitle": "Buka Proteksi Dokumen", "DE.Views.DocumentHolder.aboveText": "Di atas", "DE.Views.DocumentHolder.addCommentText": "Tambahkan komentar", "DE.Views.DocumentHolder.advancedDropCapText": "Pengaturan Drop Cap", "DE.Views.DocumentHolder.advancedEquationText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.advancedFrameText": "Pengaturan Lanjut untuk Kerangka", "DE.Views.DocumentHolder.advancedParagraphText": "Pengaturan Lanjut untuk Paragraf", "DE.Views.DocumentHolder.advancedTableText": "Pengaturan Lanjut untuk Tabel", "DE.Views.DocumentHolder.advancedText": "Pengaturan lanjutan", "DE.Views.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.allLinearText": "Semua - Linear", "DE.Views.DocumentHolder.allProfText": "Semua - Profesional", "DE.Views.DocumentHolder.belowText": "<PERSON> bawah", "DE.Views.DocumentHolder.breakBeforeText": "<PERSON><PERSON> halaman sebelum", "DE.Views.DocumentHolder.bulletsText": "Butir & Penomoran", "DE.Views.DocumentHolder.cellAlignText": "<PERSON><PERSON> <PERSON><PERSON>", "DE.Views.DocumentHolder.cellText": "<PERSON>l", "DE.Views.DocumentHolder.centerText": "Tengah", "DE.Views.DocumentHolder.chartText": "Pengaturan Lanjut untuk Bagan", "DE.Views.DocumentHolder.columnText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.currLinearText": "Saat Ini - Linear", "DE.Views.DocumentHolder.currProfText": "Saat Ini - Profesional", "DE.Views.DocumentHolder.deleteColumnText": "Ha<PERSON> kolom", "DE.Views.DocumentHolder.deleteRowText": "Ha<PERSON> baris", "DE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON> tabel", "DE.Views.DocumentHolder.deleteText": "Hapus", "DE.Views.DocumentHolder.direct270Text": "Putar teks naik", "DE.Views.DocumentHolder.direct90Text": "Putar teks turun", "DE.Views.DocumentHolder.directHText": "Horisontal", "DE.Views.DocumentHolder.directionText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.editChartText": "Edit Data", "DE.Views.DocumentHolder.editFooterText": "Sunting kaki", "DE.Views.DocumentHolder.editHeaderText": "Sunting kepala", "DE.Views.DocumentHolder.editHyperlinkText": "Edit taut luar", "DE.Views.DocumentHolder.eqToDisplayText": "Ubah untuk Menampilkan", "DE.Views.DocumentHolder.eqToInlineText": "Ubah ke Sebaris", "DE.Views.DocumentHolder.guestText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.hideEqToolbar": "Sembunyikan Bilah Alat Per<PERSON>an", "DE.Views.DocumentHolder.hyperlinkText": "Hyperlink", "DE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.imageText": "Pengaturan Lanjut untuk Gambar", "DE.Views.DocumentHolder.insertColumnLeftText": "Kolom kiri", "DE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.insertColumnText": "Sisipkan kolom", "DE.Views.DocumentHolder.insertRowAboveText": "Baris di atas", "DE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON> bawah", "DE.Views.DocumentHolder.insertRowText": "Sisipkan Baris", "DE.Views.DocumentHolder.insertText": "<PERSON>sip<PERSON>", "DE.Views.DocumentHolder.keepLinesText": "<PERSON><PERSON><PERSON> satukan garis", "DE.Views.DocumentHolder.langText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.latexText": "LaTex", "DE.Views.DocumentHolder.leftText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.loadSpellText": "<PERSON><PERSON><PERSON> varian...", "DE.Views.DocumentHolder.mergeCellsText": "Gabungkan Sel", "DE.Views.DocumentHolder.mniImageFromFile": "Image from File", "DE.Views.DocumentHolder.mniImageFromStorage": "Image from Storage", "DE.Views.DocumentHolder.mniImageFromUrl": "Image from URL", "DE.Views.DocumentHolder.moreText": "Varian lain...", "DE.Views.DocumentHolder.noSpellVariantsText": "Tidak ada varian", "DE.Views.DocumentHolder.notcriticalErrorTitle": "Peringatan", "DE.Views.DocumentHolder.originalSizeText": "Ukuran sebenarnya", "DE.Views.DocumentHolder.paragraphText": "Paragra<PERSON>", "DE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON> luar", "DE.Views.DocumentHolder.rightText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.rowText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.saveStyleText": "Buat style baru", "DE.Views.DocumentHolder.selectCellText": "<PERSON><PERSON><PERSON> sel", "DE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON> kolom", "DE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON><PERSON> baris", "DE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON> tabel", "DE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.shapeText": "Pengaturan Lanjut untuk Bentuk", "DE.Views.DocumentHolder.showEqToolbar": "<PERSON><PERSON><PERSON><PERSON> Per<PERSON>", "DE.Views.DocumentHolder.spellcheckText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.splitCellsText": "Pisahkan Sel...", "DE.Views.DocumentHolder.splitCellTitleText": "Pi<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.strDelete": "Hilangkan tanda tangan", "DE.Views.DocumentHolder.strDetails": "Det<PERSON>", "DE.Views.DocumentHolder.strSetup": "<PERSON><PERSON>", "DE.Views.DocumentHolder.strSign": "Tandatangan", "DE.Views.DocumentHolder.styleText": "Format sebagai Style", "DE.Views.DocumentHolder.tableText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textAccept": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textArrange": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textArrangeBack": "Jalankan di Background", "DE.Views.DocumentHolder.textArrangeBackward": "Mundurkan", "DE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textArrangeFront": "Bawa ke latar depan", "DE.Views.DocumentHolder.textCells": "<PERSON>l", "DE.Views.DocumentHolder.textClearField": "<PERSON><PERSON><PERSON><PERSON> ruas", "DE.Views.DocumentHolder.textCol": "<PERSON><PERSON> semua kolom", "DE.Views.DocumentHolder.textContentControls": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textContinueNumbering": "<PERSON>n<PERSON><PERSON> penomoran", "DE.Views.DocumentHolder.textCopy": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textCrop": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textCropFit": "Fit", "DE.Views.DocumentHolder.textCut": "Potong", "DE.Views.DocumentHolder.textDistributeCols": "Distribusikan kolom", "DE.Views.DocumentHolder.textDistributeRows": "Distribusikan baris", "DE.Views.DocumentHolder.textEditControls": "Pengaturan Kontrol <PERSON>", "DE.Views.DocumentHolder.textEditField": "<PERSON><PERSON> bidang", "DE.Views.DocumentHolder.textEditObject": "<PERSON><PERSON> objek", "DE.Views.DocumentHolder.textEditPoints": "<PERSON> titik", "DE.Views.DocumentHolder.textEditWrapBoundary": "<PERSON> Potongan Teks", "DE.Views.DocumentHolder.textFieldCodes": "Toggle field codes", "DE.Views.DocumentHolder.textFlipH": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textFlipV": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textFollow": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textFromFile": "Dari file", "DE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textFromUrl": "Dari URL", "DE.Views.DocumentHolder.textIndents": "Setel indentasi daftar", "DE.Views.DocumentHolder.textJoinList": "Gabung ke list sebelumnya", "DE.Views.DocumentHolder.textLeft": "Pindahkan sel ke kiri", "DE.Views.DocumentHolder.textNest": "Nest table", "DE.Views.DocumentHolder.textNextPage": "Halaman <PERSON>", "DE.Views.DocumentHolder.textNumberingValue": "<PERSON><PERSON><PERSON>lai", "DE.Views.DocumentHolder.textPaste": "Tempel", "DE.Views.DocumentHolder.textPrevPage": "Halaman Sebelumnya", "DE.Views.DocumentHolder.textRedo": "Redo", "DE.Views.DocumentHolder.textRefreshField": "<PERSON><PERSON><PERSON> ruas", "DE.Views.DocumentHolder.textReject": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textRemCheckBox": "Hilangkan Checkbox", "DE.Views.DocumentHolder.textRemComboBox": "Hilangkan Kotak Combo", "DE.Views.DocumentHolder.textRemDropdown": "Hilangkan Dropdown", "DE.Views.DocumentHolder.textRemField": "Hilangkan ruas teks", "DE.Views.DocumentHolder.textRemove": "Hapus", "DE.Views.DocumentHolder.textRemoveControl": "Hilangkan Kontrol Konten", "DE.Views.DocumentHolder.textRemPicture": "Hilangkan gambar", "DE.Views.DocumentHolder.textRemRadioBox": "Hilangkan Tombol Radio", "DE.Views.DocumentHolder.textReplace": "Ganti Gambar", "DE.Views.DocumentHolder.textResetCrop": "Reset crop", "DE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textRotate270": "Rotasi 90° Berlawanan Jarum Jam", "DE.Views.DocumentHolder.textRotate90": "Rotasi 90° Searah Jarum Jam", "DE.Views.DocumentHolder.textRow": "<PERSON><PERSON> semua baris", "DE.Views.DocumentHolder.textSaveAsPicture": "Simpan sebagai gambar", "DE.Views.DocumentHolder.textSeparateList": "Pisahkan list", "DE.Views.DocumentHolder.textSettings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textSeveral": "Beberapa Baris/Kolom", "DE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON>a bawah", "DE.Views.DocumentHolder.textShapeAlignCenter": "Rata tengah", "DE.Views.DocumentHolder.textShapeAlignLeft": "Rata kiri", "DE.Views.DocumentHolder.textShapeAlignMiddle": "Rata di tengah", "DE.Views.DocumentHolder.textShapeAlignRight": "<PERSON>a kanan", "DE.Views.DocumentHolder.textShapeAlignTop": "Rata atas", "DE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "DE.Views.DocumentHolder.textStartNewList": "<PERSON><PERSON> list baru", "DE.Views.DocumentHolder.textStartNumberingFrom": "<PERSON><PERSON> nilai penomoran", "DE.Views.DocumentHolder.textTitleCellsRemove": "<PERSON><PERSON> sel", "DE.Views.DocumentHolder.textTOC": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textTOCSettings": "<PERSON><PERSON><PERSON><PERSON> daftar isi", "DE.Views.DocumentHolder.textUndo": "Batalkan", "DE.Views.DocumentHolder.textUpdateAll": "Update keseluruhan tabel", "DE.Views.DocumentHolder.textUpdatePages": "Update hanya nomor halaman", "DE.Views.DocumentHolder.textUpdateTOC": "Update daftar isi", "DE.Views.DocumentHolder.textWrap": "Bentuk Potongan", "DE.Views.DocumentHolder.tipIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "DE.Views.DocumentHolder.toDictionaryText": "Tambahkan ke kamus", "DE.Views.DocumentHolder.txtAddBottom": "Tam<PERSON> tepi bawah", "DE.Views.DocumentHolder.txtAddFractionBar": "Tambah batang pecahan", "DE.Views.DocumentHolder.txtAddHor": "Tambah garis horizontal", "DE.Views.DocumentHolder.txtAddLB": "<PERSON><PERSON> garis kiri bawah", "DE.Views.DocumentHolder.txtAddLeft": "Tambah tepi kiri", "DE.Views.DocumentHolder.txtAddLT": "Tambah garis kiri atas", "DE.Views.DocumentHolder.txtAddRight": "Tambah tepi kanan", "DE.Views.DocumentHolder.txtAddTop": "Tambah tepi atas", "DE.Views.DocumentHolder.txtAddVer": "<PERSON><PERSON> garis vertikal", "DE.Views.DocumentHolder.txtAlignToChar": "<PERSON>a dengan karakter", "DE.Views.DocumentHolder.txtBehind": "Di belakang teks", "DE.Views.DocumentHolder.txtBorderProps": "Properti pembatas", "DE.Views.DocumentHolder.txtBottom": "<PERSON>wa<PERSON>", "DE.Views.DocumentHolder.txtColumnAlign": "<PERSON>a kolom", "DE.Views.DocumentHolder.txtDecreaseArg": "Kurangi ukuran argumen", "DE.Views.DocumentHolder.txtDeleteArg": "Hapus argumen", "DE.Views.DocumentHolder.txtDeleteBreak": "Hapus break manual", "DE.Views.DocumentHolder.txtDeleteChars": "Hapus karakter terlampir", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON> karakter dan separator terlampir", "DE.Views.DocumentHolder.txtDeleteEq": "<PERSON><PERSON>", "DE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON> char", "DE.Views.DocumentHolder.txtDeleteRadical": "Hapus radikal", "DE.Views.DocumentHolder.txtDistribHor": "Distribusikan arah horizontal", "DE.Views.DocumentHolder.txtDistribVert": "Distribusikan arah vertikal", "DE.Views.DocumentHolder.txtEmpty": "(Kosong)", "DE.Views.DocumentHolder.txtFractionLinear": "Ubah ke pecahan linear", "DE.Views.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> ke pecahan", "DE.Views.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> ke pecahan bert<PERSON>uk", "DE.Views.DocumentHolder.txtGroup": "Grup", "DE.Views.DocumentHolder.txtGroupCharOver": "<PERSON>kter di atas teks", "DE.Views.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON> di bawah teks", "DE.Views.DocumentHolder.txtHideBottom": "Sembunyikan pembatas bawah", "DE.Views.DocumentHolder.txtHideBottomLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> nilai batas bawah", "DE.Views.DocumentHolder.txtHideCloseBracket": "Sembunyikan tanda kurung tutup", "DE.Views.DocumentHolder.txtHideDegree": "Sembunyikan degree", "DE.Views.DocumentHolder.txtHideHor": "Sembunyikan garis horizontal", "DE.Views.DocumentHolder.txtHideLB": "Sembunyikan garis bawah kiri", "DE.Views.DocumentHolder.txtHideLeft": "Sembunyikan pembatas kiri", "DE.Views.DocumentHolder.txtHideLT": "Sembunyikan garis atas kiri", "DE.Views.DocumentHolder.txtHideOpenBracket": "Sembunyikan tanda kurung buka", "DE.Views.DocumentHolder.txtHidePlaceholder": "Sembunyikan placeholder", "DE.Views.DocumentHolder.txtHideRight": "Sembunyikan pembatas kanan", "DE.Views.DocumentHolder.txtHideTop": "Sembunyikan pembatas atas", "DE.Views.DocumentHolder.txtHideTopLimit": "Se<PERSON><PERSON><PERSON><PERSON> nilai batas atas", "DE.Views.DocumentHolder.txtHideVer": "Sembunyikan garis vertikal", "DE.Views.DocumentHolder.txtIncreaseArg": "Tingkatkan ukuran argumen", "DE.Views.DocumentHolder.txtInFront": "Di depan teks", "DE.Views.DocumentHolder.txtInline": "<PERSON><PERSON><PERSON> dengan teks", "DE.Views.DocumentHolder.txtInsertArgAfter": "Sisipkan argumen setelah", "DE.Views.DocumentHolder.txtInsertArgBefore": "Sisipkan argumen sebelum", "DE.Views.DocumentHolder.txtInsertBreak": "Sisi<PERSON>kan break manual", "DE.Views.DocumentHolder.txtInsertCaption": "<PERSON>si<PERSON><PERSON> caption", "DE.Views.DocumentHolder.txtInsertEqAfter": "<PERSON><PERSON><PERSON><PERSON> persa<PERSON>an set<PERSON>h", "DE.Views.DocumentHolder.txtInsertEqBefore": "<PERSON>sip<PERSON> persamaan sebelum", "DE.Views.DocumentHolder.txtInsImage": "Sisip<PERSON> gambar dari File", "DE.Views.DocumentHolder.txtInsImageUrl": "Sisipkan gambar dari URL", "DE.Views.DocumentHolder.txtKeepTextOnly": "Pertahankan hanya teks", "DE.Views.DocumentHolder.txtLimitChange": "Ganti lokasi limit", "DE.Views.DocumentHolder.txtLimitOver": "Batasi di atas teks", "DE.Views.DocumentHolder.txtLimitUnder": "Batasi di bawah teks", "DE.Views.DocumentHolder.txtMatchBrackets": "Sesuaikan tanda kurung dengan tinggi argumen", "DE.Views.DocumentHolder.txtMatrixAlign": "<PERSON>a matriks", "DE.Views.DocumentHolder.txtOverbar": "Bar di atas teks", "DE.Views.DocumentHolder.txtOverwriteCells": "Tiban sel", "DE.Views.DocumentHolder.txtPasteSourceFormat": "Pertahankan formatting sumber", "DE.Views.DocumentHolder.txtPressLink": "<PERSON><PERSON> {0} dan klik link", "DE.Views.DocumentHolder.txtPrintSelection": "Cetak pilihan", "DE.Views.DocumentHolder.txtRemFractionBar": "Hilangkan bar pecahan", "DE.Views.DocumentHolder.txtRemLimit": "Hilangkan limit", "DE.Views.DocumentHolder.txtRemoveAccentChar": "Hilangkan aksen karakter", "DE.Views.DocumentHolder.txtRemoveBar": "Hilangkan diagram batang", "DE.Views.DocumentHolder.txtRemoveWarning": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menghilangkan tandatangan ini?<br>Proses tidak bisa dikembalikan.", "DE.Views.DocumentHolder.txtRemScripts": "Hilangkan skrip", "DE.Views.DocumentHolder.txtRemSubscript": "Hilangkan subscript", "DE.Views.DocumentHolder.txtRemSuperscript": "Hilangkan superscript", "DE.Views.DocumentHolder.txtScriptsAfter": "<PERSON>ripts setelah teks", "DE.Views.DocumentHolder.txtScriptsBefore": "Scripts sebelum teks", "DE.Views.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON><PERSON> batas bawah", "DE.Views.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON><PERSON> kurung penutup", "DE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON> degree", "DE.Views.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON><PERSON> kurung pembuka", "DE.Views.DocumentHolder.txtShowPlaceholder": "Tampilkan placeholder", "DE.Views.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON><PERSON> batas atas", "DE.Views.DocumentHolder.txtSquare": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtStretchBrackets": "Regangkan dalam kurung", "DE.Views.DocumentHolder.txtThrough": "Tembus", "DE.Views.DocumentHolder.txtTight": "Ketat", "DE.Views.DocumentHolder.txtTop": "Atas", "DE.Views.DocumentHolder.txtTopAndBottom": "Atas dan bawah", "DE.Views.DocumentHolder.txtUnderbar": "Bar di bawah teks", "DE.Views.DocumentHolder.txtUngroup": "Pisahkan dari grup", "DE.Views.DocumentHolder.txtWarnUrl": "Klik link ini bisa berbahaya untuk perangkat dan data Anda.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin tetap lanjut?", "DE.Views.DocumentHolder.unicodeText": "Unicode", "DE.Views.DocumentHolder.updateStyleText": "Update %1 style", "DE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.strBorders": "Pembatas & Isian", "DE.Views.DropcapSettingsAdvanced.strDropcap": "Drop cap", "DE.Views.DropcapSettingsAdvanced.strMargins": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "Sekurang-kurangnya", "DE.Views.DropcapSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textBackColor": "<PERSON><PERSON> la<PERSON>", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "<PERSON><PERSON> te<PERSON>", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "<PERSON>lik pada diagram atau gunakan tombol untuk memilih pembatas", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "Ukura<PERSON> tepi", "DE.Views.DropcapSettingsAdvanced.textBottom": "<PERSON>wa<PERSON>", "DE.Views.DropcapSettingsAdvanced.textCenter": "Tengah", "DE.Views.DropcapSettingsAdvanced.textColumn": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textDistance": "Jarak dari teks", "DE.Views.DropcapSettingsAdvanced.textExact": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textFlow": "<PERSON><PERSON><PERSON> alur", "DE.Views.DropcapSettingsAdvanced.textFont": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textFrame": "Kerang<PERSON>", "DE.Views.DropcapSettingsAdvanced.textHeight": "Tingg<PERSON>", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "Horisontal", "DE.Views.DropcapSettingsAdvanced.textInline": "<PERSON><PERSON><PERSON> berderet", "DE.Views.DropcapSettingsAdvanced.textInMargin": "Dalam margin", "DE.Views.DropcapSettingsAdvanced.textInText": "<PERSON><PERSON> teks", "DE.Views.DropcapSettingsAdvanced.textLeft": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textMargin": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textMove": "Pindah bersama teks", "DE.Views.DropcapSettingsAdvanced.textNone": "Tidak ada", "DE.Views.DropcapSettingsAdvanced.textPage": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textParagraph": "Paragra<PERSON>", "DE.Views.DropcapSettingsAdvanced.textParameters": "Parameter", "DE.Views.DropcapSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textRelative": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textRight": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "Ketinggian dalam ukuran baris", "DE.Views.DropcapSettingsAdvanced.textTitle": "Drop cap - Pen<PERSON><PERSON><PERSON> lan<PERSON>t", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "Kerangka - Pengat<PERSON><PERSON> lan<PERSON>", "DE.Views.DropcapSettingsAdvanced.textTop": "Atas", "DE.Views.DropcapSettingsAdvanced.textVertical": "Vertikal", "DE.Views.DropcapSettingsAdvanced.textWidth": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.tipFontName": "<PERSON><PERSON><PERSON>", "DE.Views.EditListItemDialog.textDisplayName": "<PERSON><PERSON> ta<PERSON>", "DE.Views.EditListItemDialog.textNameError": "Nama tampilan tidak boleh kosong", "DE.Views.EditListItemDialog.textValue": "<PERSON><PERSON>", "DE.Views.EditListItemDialog.textValueError": "<PERSON>ang dengan nilai yang sama sudah ada.", "DE.Views.FileMenu.ariaFileMenu": "<PERSON><PERSON> berkas", "DE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON>", "DE.Views.FileMenu.btnCloseEditor": "Tutup File", "DE.Views.FileMenu.btnCloseMenuCaption": "Mundur", "DE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON>", "DE.Views.FileMenu.btnHelpCaption": "Bantuan", "DE.Views.FileMenu.btnHistoryCaption": "Riwayat versi", "DE.Views.FileMenu.btnInfoCaption": "Informasi Dokumen", "DE.Views.FileMenu.btnPrintCaption": "Cetak", "DE.Views.FileMenu.btnProtectCaption": "Proteks<PERSON>", "DE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON> yang <PERSON>", "DE.Views.FileMenu.btnRenameCaption": "Ganti nama", "DE.Views.FileMenu.btnReturnCaption": "Kembali ke Dokumen", "DE.Views.FileMenu.btnRightsCaption": "Hak Akses", "DE.Views.FileMenu.btnSaveAsCaption": "Simpan <PERSON>", "DE.Views.FileMenu.btnSaveCaption": "Simpan", "DE.Views.FileMenu.btnSaveCopyAsCaption": "Simpan Salinan <PERSON>", "DE.Views.FileMenu.btnSettingsCaption": "Pen<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "DE.Views.FileMenu.btnToEditCaption": "<PERSON>", "DE.Views.FileMenu.textDownload": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "Dokumen kosong", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Terapkan", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Tambah properti", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Tambah Teks", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplikasi", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Ubah hak akses", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "Komentar", "DE.Views.FileMenuPanels.DocumentInfo.txtCommon": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Dibuat", "DE.Views.FileMenuPanels.DocumentInfo.txtDocumentInfo": "Info dokumen", "DE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Properti Dokumen", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "Tampilan Web Cepat", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "Memuat...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "Tidak", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Pemilik", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "Paragra<PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "Pembuat PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "PDF Bertanda", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "Versi PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properti", "DE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "Orang yang memiliki hak", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "Karakter dengan spasi", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "Statistik", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subyek", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tag", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Diunggah", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "Ya", "DE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Hak akses", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Ubah hak akses", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "Orang yang memiliki hak", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Peringatan", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON>gan password", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteksi dokumen", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON>gan tanda tangan", "DE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Tanda tangan yang valid telah ditambahkan ke dokumen.<br>Dokumen terproteksi dari pen<PERSON>.", "DE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Memastikan integritas dokumen dengan menambahkan<br>\nsuatu tanda tangan digital yang tidak tampak", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON>", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Editing akan mengh<PERSON>us tandatangan dari dokumen.<br><PERSON><PERSON><PERSON><PERSON><PERSON>?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Do<PERSON>men ini dilindungi dengan password", "DE.Views.FileMenuPanels.ProtectDoc.txtProtectDocument": "Enkripsi dokumen ini dengan", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Dokumen ini perlu ditandatangani.", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Tandatangan valid sudah ditambahkan ke dokumen. Dokumen dilindungi dari penyuntingan.", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Beberapa tanda tangan digital di dokumen tidak valid atau tidak bisa diverifikasi. Dokumen dilindungi dari pen<PERSON>an.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON><PERSON><PERSON> tanda tangan", "DE.Views.FileMenuPanels.Settings.okButtonText": "Terapkan", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "Mode Edit <PERSON>", "DE.Views.FileMenuPanels.Settings.strFast": "Cepat", "DE.Views.FileMenuPanels.Settings.strFontRender": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "A<PERSON><PERSON>n kata dalam HURUF BESAR", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Abaikan kata dengan angka", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "Pengat<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strPasteButton": "Tam<PERSON>lkan tombol Opsi Paste ketika konten sedang dipaste", "DE.Views.FileMenuPanels.Settings.strRTLSupport": "Antar muka RTL", "DE.Views.FileMenuPanels.Settings.strShowChanges": "Perubahan Ko<PERSON>borasi Realtime", "DE.Views.FileMenuPanels.Settings.strShowComments": "<PERSON><PERSON><PERSON><PERSON> komentar pada teks", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>han dari pengguna lain", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "<PERSON><PERSON><PERSON><PERSON> komentar yang telah diselesaikan", "DE.Views.FileMenuPanels.Settings.strStrict": "Strict", "DE.Views.FileMenuPanels.Settings.strTabStyle": "Gaya tab", "DE.Views.FileMenuPanels.Settings.strTheme": "<PERSON><PERSON> ant<PERSON>", "DE.Views.FileMenuPanels.Settings.strUnit": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strZoom": "<PERSON><PERSON> zum baku", "DE.Views.FileMenuPanels.Settings.text10Minutes": "Tiap 10 Menit", "DE.Views.FileMenuPanels.Settings.text30Minutes": "Tiap 30 Menit", "DE.Views.FileMenuPanels.Settings.text5Minutes": "Tiap 5 Menit", "DE.Views.FileMenuPanels.Settings.text60Minutes": "Setiap jam", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "Panduan <PERSON>", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.textAutoSave": "<PERSON><PERSON><PERSON> otomatis", "DE.Views.FileMenuPanels.Settings.textDisabled": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.textFill": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.textForceSave": "Menyimpan versi intermedier", "DE.Views.FileMenuPanels.Settings.textLine": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.textMinute": "<PERSON><PERSON><PERSON> menit", "DE.Views.FileMenuPanels.Settings.textOldVersions": "Buat file kompatibel dengan versi lama MS Word ketika disimpan sebagai DOCX", "DE.Views.FileMenuPanels.Settings.textSmartSelection": "<PERSON><PERSON><PERSON> se<PERSON>ksi paragraf pintar", "DE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "Pengaturan lanjutan", "DE.Views.FileMenuPanels.Settings.txtAll": "<PERSON><PERSON>a", "DE.Views.FileMenuPanels.Settings.txtAppearance": "Penampilan", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opsi AutoCorrect...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "Mode cache default", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "Tam<PERSON>lkan dengan klik di balon", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "<PERSON><PERSON><PERSON><PERSON> dengan men<PERSON>kan kursor ke tooltips", "DE.Views.FileMenuPanels.Settings.txtCm": "Sentimeter", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "Sesuaikan akses cepat", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "Hidupkan mode gelap dokumen", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "<PERSON><PERSON><PERSON><PERSON> dan pen<PERSON>n", "DE.Views.FileMenuPanels.Settings.txtFastTip": "<PERSON><PERSON><PERSON><PERSON> bersama waktu-nyata. <PERSON><PERSON><PERSON> disimpan secara otomatis", "DE.Views.FileMenuPanels.Settings.txtFitPage": "Pas ke halaman", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "Pas ke lebar", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtInch": "Inci", "DE.Views.FileMenuPanels.Settings.txtLast": "<PERSON><PERSON> yang te<PERSON>hir", "DE.Views.FileMenuPanels.Settings.txtLastUsed": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtMac": "sebagai OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtNone": "Tidak Ada yang <PERSON>", "DE.Views.FileMenuPanels.Settings.txtProofing": "Proofing", "DE.Views.FileMenuPanels.Settings.txtPt": "Titik", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "Tam<PERSON>lkan tombol Cetak Cepat dalam header editor", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "<PERSON><PERSON><PERSON> akan", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "Aktifkan semua", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Aktifkan semua macros tanpa notifikasi", "DE.Views.FileMenuPanels.Settings.txtScreenReader": "Turn on screen reader support", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "Tampilkan pelacak perubahan", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "Nonak<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Nonaktifkan semua macros tanpa notifikasi", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "<PERSON><PERSON>n tombol \"Simpan\" untuk menyinkronkan perubahan yang Anda dan orang lain lakukan", "DE.Views.FileMenuPanels.Settings.txtTabBack": "Use toolbar color as tabs background", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "Gunakan tombol Alt untuk menavigasi antarmuka pengguna menggunakan keyboard", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Gunakan tombol Option untuk menavigasi antarmuka pengguna menggunakan keyboard", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Nonaktifkan semua macros dengan notifikasi", "DE.Views.FileMenuPanels.Settings.txtWin": "sebagai Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "<PERSON><PERSON><PERSON> se<PERSON>ai", "DE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Simpan salinan sebagai", "DE.Views.FormSettings.textAlways": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textAnyone": "Siapa pun", "DE.Views.FormSettings.textAspect": "<PERSON><PERSON><PERSON> aspek rasio", "DE.Views.FormSettings.textAtLeast": "Paling sedikit", "DE.Views.FormSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textAutofit": "AutoFit", "DE.Views.FormSettings.textBackgroundColor": "<PERSON><PERSON>", "DE.Views.FormSettings.textCheckbox": "Kotak centang", "DE.Views.FormSettings.textCheckDefault": "Kotak centang secara baku dicentang", "DE.Views.FormSettings.textColor": "<PERSON><PERSON>", "DE.Views.FormSettings.textComb": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textCombobox": "Kotak kombo", "DE.Views.FormSettings.textComplex": "Bidang Kompleks", "DE.Views.FormSettings.textConnected": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textCreditCard": "Nomor Kartu Kredit (mis 4111-1111-1111-1111)", "DE.Views.FormSettings.textDateField": "Bidang Tanggal & Jam", "DE.Views.FormSettings.textDateFormat": "<PERSON><PERSON><PERSON><PERSON> tanggal seperti ini", "DE.Views.FormSettings.textDefValue": "<PERSON><PERSON> baku", "DE.Views.FormSettings.textDelete": "Hapus", "DE.Views.FormSettings.textDigits": "Digit", "DE.Views.FormSettings.textDisconnect": "<PERSON><PERSON><PERSON> hubungan", "DE.Views.FormSettings.textDropDown": "Dropdown", "DE.Views.FormSettings.textExact": "<PERSON><PERSON>", "DE.Views.FormSettings.textField": "<PERSON><PERSON><PERSON> teks", "DE.Views.FormSettings.textFillRoles": "Siapa yang harus mengisi ini?", "DE.Views.FormSettings.textFixed": "<PERSON><PERSON>s ukuran tetap", "DE.Views.FormSettings.textFormat": "Format", "DE.Views.FormSettings.textFormatSymbols": "Simbol yang <PERSON>", "DE.Views.FormSettings.textFromFile": "Dari file", "DE.Views.FormSettings.textFromStorage": "<PERSON><PERSON>", "DE.Views.FormSettings.textFromUrl": "Dari URL", "DE.Views.FormSettings.textGroupKey": "Satukan kunci", "DE.Views.FormSettings.textImage": "Gambar", "DE.Views.FormSettings.textKey": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textLang": "Bahasa", "DE.Views.FormSettings.textLetters": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textLock": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textMask": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textMaxChars": "<PERSON><PERSON> ka<PERSON>", "DE.Views.FormSettings.textMulti": "Ruas multi baris", "DE.Views.FormSettings.textNever": "tidak pernah", "DE.Views.FormSettings.textNoBorder": "Tan<PERSON> pembatas", "DE.Views.FormSettings.textNone": "Tidak ada", "DE.Views.FormSettings.textPhone1": "<PERSON><PERSON> Telepon (mis. (*************)", "DE.Views.FormSettings.textPhone2": "Nomor Telepon (mis. +447911123456)", "DE.Views.FormSettings.textPlaceholder": "Placeholder", "DE.Views.FormSettings.textRadiobox": "Tombol Radio", "DE.Views.FormSettings.textRadioChoice": "Radio button choice", "DE.Views.FormSettings.textRadioDefault": "Tombol dicentang secara baku", "DE.Views.FormSettings.textReg": "Ekspresi Reguler", "DE.Views.FormSettings.textRequired": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textScale": "Waktu untuk menskala", "DE.Views.FormSettings.textSelectImage": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textSignature": "Signature", "DE.Views.FormSettings.textTag": "Tandai", "DE.Views.FormSettings.textTip": "Tip", "DE.Views.FormSettings.textTipAdd": "Tambah nilai baru", "DE.Views.FormSettings.textTipDelete": "<PERSON><PERSON> ni<PERSON>", "DE.Views.FormSettings.textTipDown": "Pindah kebawah", "DE.Views.FormSettings.textTipUp": "Pi<PERSON><PERSON> keatas", "DE.Views.FormSettings.textTooBig": "<PERSON><PERSON><PERSON> terl<PERSON>u besar", "DE.Views.FormSettings.textTooSmall": "Gambar terlalu kecil", "DE.Views.FormSettings.textUKPassport": "<PERSON><PERSON> (mis. *********)", "DE.Views.FormSettings.textUnlock": "<PERSON><PERSON>", "DE.Views.FormSettings.textUSSSN": "SSN AS (mis. ***********)", "DE.Views.FormSettings.textValue": "<PERSON><PERSON>", "DE.Views.FormSettings.textWidth": "<PERSON><PERSON> sel", "DE.Views.FormSettings.textZipCodeUS": "Kode Pos AS (mis. 92663 atau 92663-1234)", "DE.Views.FormsTab.capBtnCheckBox": "Kotak centang", "DE.Views.FormsTab.capBtnComboBox": "Kotak combo", "DE.Views.FormsTab.capBtnComplex": "Bidang Kompleks", "DE.Views.FormsTab.capBtnDownloadForm": "Unduh Sebagai PDF", "DE.Views.FormsTab.capBtnDropDown": "Dropdown", "DE.Views.FormsTab.capBtnEmail": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.capBtnImage": "Gambar", "DE.Views.FormsTab.capBtnManager": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.capBtnNext": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.capBtnPhone": "Nomor Telepon", "DE.Views.FormsTab.capBtnPrev": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.capBtnRadioBox": "Tombol Radio", "DE.Views.FormsTab.capBtnSaveForm": "Simpan Sebagai PDF", "DE.Views.FormsTab.capBtnSaveFormDesktop": "Simpan Sebagai...", "DE.Views.FormsTab.capBtnSignature": "Signature Field", "DE.Views.FormsTab.capBtnSubmit": "Lengkapi & Kirim", "DE.Views.FormsTab.capBtnText": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.capBtnView": "Tampilkan form", "DE.Views.FormsTab.capCreditCard": "Kartu Kredit", "DE.Views.FormsTab.capDateTime": "Tanggal & Jam", "DE.Views.FormsTab.capZipCode": "Kode Pos", "DE.Views.FormsTab.helpTextFillStatus": "This form is ready for role-based filling. Click on the status button to check the filling stage.", "DE.Views.FormsTab.textAnyone": "Siapa pun", "DE.Views.FormsTab.textClear": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormsTab.textClearFields": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormsTab.textCreateForm": "Tambah ruas dan buat dokumen PDF yang bisa diisi", "DE.Views.FormsTab.textFilled": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.textGotIt": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.textHighlight": "Pengaturan Highlight", "DE.Views.FormsTab.textNoHighlight": "Tanpa highlight", "DE.Views.FormsTab.textRequired": "<PERSON>i semua ruas yang dibutuhkan untuk mengirim formulir.", "DE.Views.FormsTab.textSubmited": "Form berhasil disubmit", "DE.Views.FormsTab.textSubmitOk": "Your PDF form has been saved in the Complete section. You can fill out this form again and send another result.", "DE.Views.FormsTab.tipCheckBox": "Sisipkan kotak centang", "DE.Views.FormsTab.tipComboBox": "Sisipkan kotak kombo", "DE.Views.FormsTab.tipComplexField": "Sisipkan bidang kompleks", "DE.Views.FormsTab.tipCreateField": "To create a field select the desired field type on the toolbar and click on it. The field will appear in the document.", "DE.Views.FormsTab.tipCreditCard": "Sisipkan nomor kartu kredit", "DE.Views.FormsTab.tipDateTime": "<PERSON><PERSON><PERSON><PERSON> tanggal dan waktu", "DE.Views.FormsTab.tipDownloadForm": "Unduh file sebagai dokumen PDF yang dapat diisi", "DE.Views.FormsTab.tipDropDown": "Sisipkan list dropdown", "DE.Views.FormsTab.tipEmailField": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>t email", "DE.Views.FormsTab.tipFieldSettings": "You can configure selected fields on the right sidebar. Click this icon to open the field settings.", "DE.Views.FormsTab.tipFieldsLink": "<PERSON><PERSON><PERSON><PERSON> lebih lanjut tentang parameter ruas", "DE.Views.FormsTab.tipFirstPage": "<PERSON> halaman pertama", "DE.Views.FormsTab.tipFixedText": "Sisipkan ruas teks tetap", "DE.Views.FormsTab.tipFormGroupKey": "Group radio buttons to make the filling process faster. Choices with the same names will be synchronized. Users can only tick one radio button from the group.", "DE.Views.FormsTab.tipFormKey": "You can assign a key to a field or a group of fields. When a user fills in the data, it will be copied to all the fields with the same key.", "DE.Views.FormsTab.tipHelpRoles": "Use the Manage Roles feature to group fields by purpose and assign the responsible team members.", "DE.Views.FormsTab.tipImageField": "Sisipkan Gambar", "DE.Views.FormsTab.tipInlineText": "Sisipkan bidang teks sebaris", "DE.Views.FormsTab.tipLastPage": "<PERSON> halaman terakhir", "DE.Views.FormsTab.tipManager": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.tipNextForm": "<PERSON>gi ke ruas berik<PERSON>", "DE.Views.FormsTab.tipNextPage": "<PERSON> halaman se<PERSON>", "DE.Views.FormsTab.tipPhoneField": "Sisipkan nomor telepon", "DE.Views.FormsTab.tipPrevForm": "<PERSON>gi ke ruas sebelumnya", "DE.Views.FormsTab.tipPrevPage": "Ke halaman sebelumnya", "DE.Views.FormsTab.tipRadioBox": "Sisipkan tombol radio", "DE.Views.FormsTab.tipRolesLink": "<PERSON><PERSON><PERSON><PERSON> lebih lanjut tentang peran", "DE.Views.FormsTab.tipSaveFile": "Klik \"Simpan Sebagai PDF\" untuk menyimpan formulir dalam format yang siap untuk diisi.", "DE.Views.FormsTab.tipSaveForm": "Simpan file sebagai dokumen PDF yang bisa diisi", "DE.Views.FormsTab.tipSignField": "Insert signature field", "DE.Views.FormsTab.tipSubmit": "Submit form", "DE.Views.FormsTab.tipTextField": "Sisipkan ruas teks", "DE.Views.FormsTab.tipViewForm": "Tampilkan form", "DE.Views.FormsTab.tipZipCode": "Sisipkan kode pos", "DE.Views.FormsTab.txtFixedDesc": "Sisipkan ruas teks tetap", "DE.Views.FormsTab.txtFixedText": "Tetap", "DE.Views.FormsTab.txtInlineDesc": "Sisipkan bidang teks sebaris", "DE.Views.FormsTab.txtInlineText": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.txtUntitled": "<PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textBottomCenter": "Bawah <PERSON>", "DE.Views.HeaderFooterSettings.textBottomLeft": "<PERSON><PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textBottomPage": "<PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textBottomRight": "<PERSON><PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textDiffFirst": "<PERSON><PERSON> pertama yang berbeda", "DE.Views.HeaderFooterSettings.textDiffOdd": "<PERSON><PERSON> ganjil dan genap yang berbeda", "DE.Views.HeaderFooterSettings.textFrom": "<PERSON><PERSON><PERSON> pada", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "Footer <PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "Header <PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textInsertCurrent": "Sisipkan ke Posisi Saat Ini", "DE.Views.HeaderFooterSettings.textNumFormat": "Number format", "DE.Views.HeaderFooterSettings.textOptions": "<PERSON><PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textPageNum": "<PERSON><PERSON><PERSON><PERSON> nomor halaman", "DE.Views.HeaderFooterSettings.textPageNumbering": "Pen<PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textPosition": "<PERSON><PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textPrev": "<PERSON><PERSON><PERSON><PERSON> dari sesi sebelumnya", "DE.Views.HeaderFooterSettings.textSameAs": "Tautkan dengan Sebelumnya", "DE.Views.HeaderFooterSettings.textTopCenter": "Atas <PERSON>", "DE.Views.HeaderFooterSettings.textTopLeft": "<PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textTopPage": "Puncak halaman", "DE.Views.HeaderFooterSettings.textTopRight": "<PERSON><PERSON>", "DE.Views.HeaderFooterSettings.txtMoreTypes": "<PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.textDefault": "Fragmen teks yang dipilih", "DE.Views.HyperlinkSettingsDialog.textDisplay": "Tampilan", "DE.Views.HyperlinkSettingsDialog.textExternal": "Tautan eksternal", "DE.Views.HyperlinkSettingsDialog.textInternal": "Letakkan di dokumen", "DE.Views.HyperlinkSettingsDialog.textSelectFile": "Pilih file", "DE.Views.HyperlinkSettingsDialog.textTitle": "Pengaturan hyperlink", "DE.Views.HyperlinkSettingsDialog.textTooltip": "Teks ScreenTip", "DE.Views.HyperlinkSettingsDialog.textUrl": "<PERSON><PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "Awal dokumen", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "Bookmarks", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "Headings", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "Ruas ini harus berupa URL dengan format \"http://www.contoh.com\"", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "R<PERSON>s ini dibatasi 2083 karakter", "DE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "<PERSON><PERSON><PERSON><PERSON> alamat web atau pilih suatu berkas", "DE.Views.HyphenationDialog.textAuto": "Otomatis berikan tanda hubung pada dokumen", "DE.Views.HyphenationDialog.textCaps": "Berikan tanda hubung pada kata-kata yang ditulis KAPITAL", "DE.Views.HyphenationDialog.textLimit": "Batasi tanda hubung berurutan sebesar", "DE.Views.HyphenationDialog.textNoLimit": "Tan<PERSON> batas", "DE.Views.HyphenationDialog.textTitle": "<PERSON>da hubung", "DE.Views.HyphenationDialog.textZone": "Zona tanda hubung", "DE.Views.ImageSettings.strTransparency": "Opacity", "DE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "DE.Views.ImageSettings.textCrop": "<PERSON><PERSON>", "DE.Views.ImageSettings.textCropFill": "<PERSON><PERSON>", "DE.Views.ImageSettings.textCropFit": "Fit", "DE.Views.ImageSettings.textCropToShape": "<PERSON><PERSON> men<PERSON>uk", "DE.Views.ImageSettings.textEdit": "Sunting", "DE.Views.ImageSettings.textEditObject": "<PERSON> ob<PERSON>k", "DE.Views.ImageSettings.textFitMargins": "Fit ke Margin", "DE.Views.ImageSettings.textFlip": "Flip", "DE.Views.ImageSettings.textFromFile": "Dari file", "DE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON>", "DE.Views.ImageSettings.textFromUrl": "Dari URL", "DE.Views.ImageSettings.textHeight": "Tingg<PERSON>", "DE.Views.ImageSettings.textHint270": "Rotasi 90° Berlawanan Jarum Jam", "DE.Views.ImageSettings.textHint90": "Rotasi 90° Searah Jarum Jam", "DE.Views.ImageSettings.textHintFlipH": "<PERSON><PERSON>", "DE.Views.ImageSettings.textHintFlipV": "<PERSON><PERSON>", "DE.Views.ImageSettings.textInsert": "Ganti Gambar", "DE.Views.ImageSettings.textOriginalSize": "Ukuran Sebenarnya", "DE.Views.ImageSettings.textRecentlyUsed": "<PERSON><PERSON>", "DE.Views.ImageSettings.textResetCrop": "Reset crop", "DE.Views.ImageSettings.textRotate90": "Rotasi 90°", "DE.Views.ImageSettings.textRotation": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textSize": "Ukuran", "DE.Views.ImageSettings.textWidth": "<PERSON><PERSON>", "DE.Views.ImageSettings.textWrap": "Bentuk Potongan", "DE.Views.ImageSettings.txtBehind": "Di belakang teks", "DE.Views.ImageSettings.txtInFront": "<PERSON> depan <PERSON>", "DE.Views.ImageSettings.txtInline": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.txtSquare": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.txtThrough": "Tembus", "DE.Views.ImageSettings.txtTight": "Ketat", "DE.Views.ImageSettings.txtTopAndBottom": "Atas dan bawah", "DE.Views.ImageSettingsAdvanced.strMargins": "<PERSON><PERSON>an teks", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "Absolut", "DE.Views.ImageSettingsAdvanced.textAlignment": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAlt": "Teks alternatif", "DE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAltTip": "Representasi alternatif berbasis teks dari informasi objek visual, yang akan dibaca kepada orang dengan gangguan penglihatan atau kognitif untuk membantu mereka lebih memahami informasi yang ada dalam gambar, shape, grafik, atau tabel.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textArrows": "Tanda panah", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "<PERSON><PERSON><PERSON> aspek rasio", "DE.Views.ImageSettingsAdvanced.textAutofit": "AutoFit", "DE.Views.ImageSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> mulai", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "<PERSON><PERSON> mulai", "DE.Views.ImageSettingsAdvanced.textBelow": "di bawah", "DE.Views.ImageSettingsAdvanced.textBevel": "Miring", "DE.Views.ImageSettingsAdvanced.textBottom": "<PERSON>wa<PERSON>", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "<PERSON><PERSON> bawah", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "Pelipatan teks", "DE.Views.ImageSettingsAdvanced.textCapType": "Tipe cap", "DE.Views.ImageSettingsAdvanced.textCenter": "Tengah", "DE.Views.ImageSettingsAdvanced.textCharacter": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textColumn": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textDistance": "Jarak dari teks", "DE.Views.ImageSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON> akhir", "DE.Views.ImageSettingsAdvanced.textEndStyle": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textFlat": "Datar", "DE.Views.ImageSettingsAdvanced.textFlipped": "Flipped", "DE.Views.ImageSettingsAdvanced.textHeight": "Tingg<PERSON>", "DE.Views.ImageSettingsAdvanced.textHorizontal": "Horisontal", "DE.Views.ImageSettingsAdvanced.textHorizontally": "Secara Horizontal", "DE.Views.ImageSettingsAdvanced.textJoinType": "Gabungkan tipe", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textLeft": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "<PERSON>gin kiri", "DE.Views.ImageSettingsAdvanced.textLine": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textLineStyle": "<PERSON><PERSON> garis", "DE.Views.ImageSettingsAdvanced.textMargin": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMiter": "Siku-siku", "DE.Views.ImageSettingsAdvanced.textMove": "Pindah obyek bersama teks", "DE.Views.ImageSettingsAdvanced.textOptions": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "Ukuran sebenarnya", "DE.Views.ImageSettingsAdvanced.textOverlap": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textPage": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textParagraph": "Paragra<PERSON>", "DE.Views.ImageSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textPositionPc": "Posisi relatif", "DE.Views.ImageSettingsAdvanced.textRelative": "relatif den<PERSON>", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "Relatif", "DE.Views.ImageSettingsAdvanced.textResizeFit": "Ubah ukuran bentuk agar cocok ke teks", "DE.Views.ImageSettingsAdvanced.textRight": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textRightMargin": "<PERSON><PERSON> kanan", "DE.Views.ImageSettingsAdvanced.textRightOf": "ke sebelah kanan dari", "DE.Views.ImageSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textShape": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textSize": "Ukuran", "DE.Views.ImageSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textTextBox": "Kotak teks", "DE.Views.ImageSettingsAdvanced.textTitle": "Gambar - pengat<PERSON><PERSON> lan<PERSON>t", "DE.Views.ImageSettingsAdvanced.textTitleChart": "Bagan - Pengaturan lan<PERSON>", "DE.Views.ImageSettingsAdvanced.textTitleShape": "Bentuk - Pengaturan lan<PERSON>t", "DE.Views.ImageSettingsAdvanced.textTop": "Atas", "DE.Views.ImageSettingsAdvanced.textTopMargin": "<PERSON>gin atas", "DE.Views.ImageSettingsAdvanced.textVertical": "Vertikal", "DE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "Bobot & Panah", "DE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrap": "<PERSON><PERSON> peli<PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "Di belakang teks", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "Di depan teks", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "<PERSON><PERSON><PERSON> dengan teks", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "Tembus", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "Ketat", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "Atas dan bawah", "DE.Views.LeftMenu.ariaLeftMenu": "Menu kiri", "DE.Views.LeftMenu.tipAbout": "Tentang", "DE.Views.LeftMenu.tipChat": "Cha<PERSON>", "DE.Views.LeftMenu.tipComments": "Komentar", "DE.Views.LeftMenu.tipNavigation": "Na<PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.tipOutline": "Tajuk", "DE.Views.LeftMenu.tipPageThumbnails": "Gambar mini halaman", "DE.Views.LeftMenu.tipPlugins": "Plugins", "DE.Views.LeftMenu.tipSearch": "<PERSON><PERSON>", "DE.Views.LeftMenu.tipSupport": "Masukan & Dukungan", "DE.Views.LeftMenu.tipTitles": "<PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.txtDeveloper": "MODE DEVELOPER", "DE.Views.LeftMenu.txtEditor": "Penyunting Dokumen", "DE.Views.LeftMenu.txtLimit": "<PERSON><PERSON>", "DE.Views.LeftMenu.txtTrial": "MODE TRIAL", "DE.Views.LeftMenu.txtTrialDev": "Mode Trial Developer", "DE.Views.LineNumbersDialog.textAddLineNumbering": "Tambah penomoran baris", "DE.Views.LineNumbersDialog.textApplyTo": "Terapkan perubahan ke", "DE.Views.LineNumbersDialog.textContinuous": "<PERSON><PERSON><PERSON>", "DE.Views.LineNumbersDialog.textCountBy": "Dihitung <PERSON>gan", "DE.Views.LineNumbersDialog.textDocument": "Keseluruhan dokumen", "DE.Views.LineNumbersDialog.textForward": "Dari poin ini sampai selanjutnya", "DE.Views.LineNumbersDialog.textFromText": "<PERSON><PERSON> teks", "DE.Views.LineNumbersDialog.textNumbering": "Penomoran", "DE.Views.LineNumbersDialog.textRestartEachPage": "<PERSON><PERSON> ha<PERSON>an", "DE.Views.LineNumbersDialog.textRestartEachSection": "<PERSON><PERSON> se<PERSON>i", "DE.Views.LineNumbersDialog.textSection": "Sesi Saat Ini", "DE.Views.LineNumbersDialog.textStartAt": "<PERSON><PERSON><PERSON> pada", "DE.Views.LineNumbersDialog.textTitle": "Nomor garis", "DE.Views.LineNumbersDialog.txtAutoText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Links.capBtnAddText": "Tambahkan Teks", "DE.Views.Links.capBtnBookmarks": "Bookmark", "DE.Views.Links.capBtnCaption": "Caption", "DE.Views.Links.capBtnContentsUpdate": "Update Tabel", "DE.Views.Links.capBtnCrossRef": "Cross-reference", "DE.Views.Links.capBtnInsContents": "<PERSON><PERSON><PERSON>", "DE.Views.Links.capBtnInsFootnote": "Footnote", "DE.Views.Links.capBtnInsLink": "Hyperlink", "DE.Views.Links.capBtnTOF": "Daftar gambar", "DE.Views.Links.confirmDeleteFootnotes": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menghapus semua footnotes?", "DE.Views.Links.confirmReplaceTOF": "<PERSON><PERSON><PERSON><PERSON> Anda ingin mengubah daftar gambar yang dipilih?", "DE.Views.Links.mniConvertNote": "Konversikan semua catatan", "DE.Views.Links.mniDelFootnote": "Hapus semua catatan", "DE.Views.Links.mniInsEndnote": "Sisipkan catatan akhir", "DE.Views.Links.mniInsFootnote": "Sisipkan catatan kaki", "DE.Views.Links.mniNoteSettings": "Pengaturan catatan", "DE.Views.Links.textContentsRemove": "Hilangkan daftar isi", "DE.Views.Links.textContentsSettings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Links.textConvertToEndnotes": "<PERSON>n<PERSON><PERSON> Semua Footnotes Menjadi Endnotes", "DE.Views.Links.textConvertToFootnotes": "<PERSON>n<PERSON><PERSON> Semua Endnotes Menjadi Footnotes", "DE.Views.Links.textGotoEndnote": "<PERSON>gi ke catatan akhir", "DE.Views.Links.textGotoFootnote": "Pergi ke catatan kaki", "DE.Views.Links.textSwapNotes": "<PERSON>kar Footnotes dan Endnotes", "DE.Views.Links.textUpdateAll": "Update keseluruhan tabel", "DE.Views.Links.textUpdatePages": "Update hanya nomor halaman", "DE.Views.Links.tipAddText": "Sertakan tajuk pada Daftar Isi", "DE.Views.Links.tipBookmarks": "Buat bookmark", "DE.Views.Links.tipCaption": "Sisipkan Caption", "DE.Views.Links.tipContents": "<PERSON>si<PERSON><PERSON> daftar isi", "DE.Views.Links.tipContentsUpdate": "Update daftar isi", "DE.Views.Links.tipCrossRef": "Sisipkan referensi-silang", "DE.Views.Links.tipInsertHyperlink": "Tambahkan hyperlink", "DE.Views.Links.tipNotes": "<PERSON><PERSON><PERSON><PERSON> atau edit footnote", "DE.Views.Links.tipTableFigures": "Sisipkan daftar gambar", "DE.Views.Links.tipTableFiguresUpdate": "Update Daftar Gambar", "DE.Views.Links.titleUpdateTOF": "Update Daftar Gambar", "DE.Views.Links.txtDontShowTof": "<PERSON><PERSON> pada <PERSON>", "DE.Views.Links.txtLevel": "Level", "DE.Views.ListIndentsDialog.textSpace": "<PERSON><PERSON>", "DE.Views.ListIndentsDialog.textTab": "Karakter tab", "DE.Views.ListIndentsDialog.textTitle": "Inden Daftar", "DE.Views.ListIndentsDialog.txtFollowBullet": "<PERSON><PERSON><PERSON> but<PERSON> dengan", "DE.Views.ListIndentsDialog.txtFollowNumber": "<PERSON><PERSON><PERSON> angka dengan", "DE.Views.ListIndentsDialog.txtIndent": "Indentasi teks", "DE.Views.ListIndentsDialog.txtNone": "<PERSON><PERSON>", "DE.Views.ListIndentsDialog.txtPosBullet": "<PERSON><PERSON><PERSON> bulatan", "DE.Views.ListIndentsDialog.txtPosNumber": "<PERSON><PERSON><PERSON> bi<PERSON>an", "DE.Views.ListSettingsDialog.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.textBold": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.textCenter": "Tengah", "DE.Views.ListSettingsDialog.textHide": "Sembunyikan pengaturan", "DE.Views.ListSettingsDialog.textItalic": "Miring", "DE.Views.ListSettingsDialog.textLeft": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.textLevel": "Level", "DE.Views.ListSettingsDialog.textMore": "<PERSON><PERSON><PERSON><PERSON> pengaturan la<PERSON>ya", "DE.Views.ListSettingsDialog.textPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.textRight": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.textSelectLevel": "Pilih level", "DE.Views.ListSettingsDialog.textSpace": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.textTab": "Karakter tab", "DE.Views.ListSettingsDialog.txtAlign": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtAlignAt": "pada", "DE.Views.ListSettingsDialog.txtBullet": "Butir", "DE.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtFollow": "<PERSON><PERSON><PERSON> angka dengan", "DE.Views.ListSettingsDialog.txtFontName": "<PERSON>ont<PERSON>", "DE.Views.ListSettingsDialog.txtInclcudeLevel": "Sertakan angka tingkat", "DE.Views.ListSettingsDialog.txtIndent": "Indentasi teks", "DE.Views.ListSettingsDialog.txtLikeText": "Seperti teks", "DE.Views.ListSettingsDialog.txtMoreTypes": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtNewBullet": "<PERSON>ir baru", "DE.Views.ListSettingsDialog.txtNone": "Tidak ada", "DE.Views.ListSettingsDialog.txtNumFormatString": "Format bilangan", "DE.Views.ListSettingsDialog.txtRestart": "<PERSON><PERSON> da<PERSON>ar", "DE.Views.ListSettingsDialog.txtSize": "Ukuran", "DE.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON><PERSON> pada", "DE.Views.ListSettingsDialog.txtSymbol": "Simbol", "DE.Views.ListSettingsDialog.txtTabStop": "Tambahkan posisi tab pada", "DE.Views.ListSettingsDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> daftar", "DE.Views.ListSettingsDialog.txtType": "Tipe", "DE.Views.ListTypesAdvanced.labelSelect": "<PERSON><PERSON><PERSON> jeni<PERSON> da<PERSON>ar", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textAttachDocx": "Lampirkan sebagai DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "Lampirkan sebagai PDF", "DE.Views.MailMergeEmailDlg.textFileName": "Nama file", "DE.Views.MailMergeEmailDlg.textFormat": "Format email", "DE.Views.MailMergeEmailDlg.textFrom": "<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textSubject": "<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textTitle": "<PERSON><PERSON> ke email", "DE.Views.MailMergeEmailDlg.textTo": "<PERSON>", "DE.Views.MailMergeEmailDlg.textWarning": "Peringatan!", "DE.Views.MailMergeEmailDlg.textWarningMsg": "Perlu dicatat bahwa mengirim email tidak bisa dihentikan setelah Anda klik tombol 'Kirim'.", "DE.Views.MailMergeSettings.downloadMergeTitle": "Merging", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "<PERSON><PERSON> gagal.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "Peringatan", "DE.Views.MailMergeSettings.textAddRecipients": "<PERSON><PERSON> beberapa penerima ke daftar terlebih dahulu", "DE.Views.MailMergeSettings.textAll": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.textCurrent": "<PERSON><PERSON><PERSON> saat ini", "DE.Views.MailMergeSettings.textDataSource": "Sumber data", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.textEditData": "Edit list penerima", "DE.Views.MailMergeSettings.textEmail": "Email", "DE.Views.MailMergeSettings.textFrom": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textGoToMail": "<PERSON>gi ke Email", "DE.Views.MailMergeSettings.textHighlight": "<PERSON><PERSON> ruas gabung", "DE.Views.MailMergeSettings.textInsertField": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.textMaxRecepients": "Maks 100 penerima.", "DE.Views.MailMergeSettings.textMerge": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textMergeFields": "Gabungkan ruas", "DE.Views.MailMergeSettings.textMergeTo": "Merge ke", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "Simpan", "DE.Views.MailMergeSettings.textPreview": "<PERSON><PERSON><PERSON><PERSON> hasil", "DE.Views.MailMergeSettings.textReadMore": "Baca lebih lan<PERSON>t", "DE.Views.MailMergeSettings.textSendMsg": "Semua pesan email sudah siap dan akan dikirim dalam beberapa saat lagi.<br>Kecepatan pengiriman email tergantung dari provider email Anda.<br>Anda bisa melanjutkan bekerja dengan dokumen ini atau menutupnya. Setelah <PERSON>i se<PERSON>, notifikasi akan dikirimkan ke email terdaftar Anda.", "DE.Views.MailMergeSettings.textTo": "<PERSON>", "DE.Views.MailMergeSettings.txtFirst": "<PERSON> rekaman pertama", "DE.Views.MailMergeSettings.txtFromToError": "\"From\" nilai harus lebih kecil dari \"To\" nilai", "DE.Views.MailMergeSettings.txtLast": "<PERSON> rekaman terakhir", "DE.Views.MailMergeSettings.txtNext": "<PERSON> rekaman berik<PERSON>", "DE.Views.MailMergeSettings.txtPrev": "<PERSON> rekaman sebelumnya", "DE.Views.MailMergeSettings.txtUntitled": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.warnProcessMailMerge": "Gagal memulai merge", "DE.Views.Navigation.strNavigate": "Tajuk", "DE.Views.Navigation.txtClosePanel": "Tu<PERSON>p <PERSON>", "DE.Views.Navigation.txtCollapse": "<PERSON><PERSON><PERSON> se<PERSON>a", "DE.Views.Navigation.txtDemote": "Demote", "DE.Views.Navigation.txtEmpty": "Tidak ada heading di dokumen.<br>Terapkan style heading ke teks agar bisa terlihat di daftar isi.", "DE.Views.Navigation.txtEmptyItem": "Head<PERSON>", "DE.Views.Navigation.txtEmptyViewer": "Tidak ada heading di dokumen.", "DE.Views.Navigation.txtExpand": "<PERSON><PERSON> semua", "DE.Views.Navigation.txtExpandToLevel": "Perluas sampai level", "DE.Views.Navigation.txtFontSize": "<PERSON><PERSON><PERSON> huruf", "DE.Views.Navigation.txtHeadingAfter": "Heading baru set<PERSON>h", "DE.Views.Navigation.txtHeadingBefore": "Heading baru sebelum", "DE.Views.Navigation.txtLarge": "Besar", "DE.Views.Navigation.txtMedium": "Medium", "DE.Views.Navigation.txtNewHeading": "Subheading baru", "DE.Views.Navigation.txtPromote": "<PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtSelect": "<PERSON><PERSON><PERSON> konten", "DE.Views.Navigation.txtSettings": "Pengaturan tajuk", "DE.Views.Navigation.txtSmall": "<PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtWrapHeadings": "Bungkus judul panjang", "DE.Views.NoteSettingsDialog.textApply": "Terapkan", "DE.Views.NoteSettingsDialog.textApplyTo": "Terapkan perubahan ke", "DE.Views.NoteSettingsDialog.textContinue": "<PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textCustom": "<PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textDocEnd": "<PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textDocument": "Keseluruhan dokumen", "DE.Views.NoteSettingsDialog.textEachPage": "<PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textEachSection": "<PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textEndnote": "Endnote", "DE.Views.NoteSettingsDialog.textFootnote": "Footnote", "DE.Views.NoteSettingsDialog.textFormat": "Format", "DE.Views.NoteSettingsDialog.textInsert": "<PERSON>sip<PERSON>", "DE.Views.NoteSettingsDialog.textLocation": "<PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textNumbering": "Penomoran", "DE.Views.NoteSettingsDialog.textNumFormat": "Format nomor", "DE.Views.NoteSettingsDialog.textPageBottom": "<PERSON><PERSON><PERSON> ha<PERSON>", "DE.Views.NoteSettingsDialog.textSectEnd": "<PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textSection": "Sesi Saat Ini", "DE.Views.NoteSettingsDialog.textStart": "<PERSON><PERSON><PERSON> pada", "DE.Views.NoteSettingsDialog.textTextBottom": "Di bawah teks", "DE.Views.NoteSettingsDialog.textTitle": "Pengaturan catatan", "DE.Views.NotesRemoveDialog.textEnd": "<PERSON><PERSON> semua catatan akhir", "DE.Views.NotesRemoveDialog.textFoot": "<PERSON>pus semua catatan kaki", "DE.Views.NotesRemoveDialog.textTitle": "Hapus catatan", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "Peringatan", "DE.Views.PageMarginsDialog.textBottom": "<PERSON>wa<PERSON>", "DE.Views.PageMarginsDialog.textGutter": "J<PERSON>k <PERSON>", "DE.Views.PageMarginsDialog.textGutterPosition": "Posisi jarak spasi", "DE.Views.PageMarginsDialog.textInside": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textLandscape": "Landscape", "DE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textMirrorMargins": "Tiru margin", "DE.Views.PageMarginsDialog.textMultiplePages": "<PERSON><PERSON><PERSON> halaman", "DE.Views.PageMarginsDialog.textNormal": "Normal", "DE.Views.PageMarginsDialog.textOrientation": "Orientasi", "DE.Views.PageMarginsDialog.textOutside": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textPortrait": "Portrait", "DE.Views.PageMarginsDialog.textPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTop": "Atas", "DE.Views.PageMarginsDialog.txtMarginsH": "Margin atas dan bawah terlalu jauh untuk halaman setinggi ini", "DE.Views.PageMarginsDialog.txtMarginsW": "Margin kiri dan kanan terlalu besar dengan lebar halaman yang ada", "DE.Views.PageSizeDialog.textHeight": "Tingg<PERSON>", "DE.Views.PageSizeDialog.textPreset": "Preset", "DE.Views.PageSizeDialog.textTitle": "<PERSON><PERSON><PERSON> halaman", "DE.Views.PageSizeDialog.textWidth": "<PERSON><PERSON>", "DE.Views.PageSizeDialog.txtCustom": "<PERSON><PERSON><PERSON>", "DE.Views.PageThumbnails.textClosePanel": "<PERSON><PERSON>p thumbnails halaman", "DE.Views.PageThumbnails.textHighlightVisiblePart": "Highlight bagian yang bisa terlihat di halaman", "DE.Views.PageThumbnails.textPageThumbnails": "Gambar mini halaman", "DE.Views.PageThumbnails.textThumbnailsSettings": "Pengaturan thumbnails", "DE.Views.PageThumbnails.textThumbnailsSize": "Ukuran thumbnails", "DE.Views.ParagraphSettings.strIndent": "Indentasi", "DE.Views.ParagraphSettings.strIndentsLeftText": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.strIndentsRightText": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.strIndentsSpecial": "Spesial", "DE.Views.ParagraphSettings.strLineHeight": "Spasi Antar Baris", "DE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "<PERSON><PERSON> ta<PERSON> interval antar paragraf dengan model yang sama", "DE.Views.ParagraphSettings.strSpacingAfter": "Setela<PERSON>", "DE.Views.ParagraphSettings.strSpacingBefore": "Sebelum", "DE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "DE.Views.ParagraphSettings.textAt": "Pada", "DE.Views.ParagraphSettings.textAtLeast": "Sekurang-kurangnya", "DE.Views.ParagraphSettings.textAuto": "Banyak", "DE.Views.ParagraphSettings.textBackColor": "<PERSON><PERSON> la<PERSON>", "DE.Views.ParagraphSettings.textExact": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.textFirstLine": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.textHanging": "Menggantung", "DE.Views.ParagraphSettings.textNoneSpecial": "(nihil)", "DE.Views.ParagraphSettings.txtAutoText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.noTabs": "Tab yang ditentukan akan muncul pada ruas ini", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON> ka<PERSON>al semua", "DE.Views.ParagraphSettingsAdvanced.strBorders": "Pembatas & Isian", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "<PERSON><PERSON> halaman sebelum", "DE.Views.ParagraphSettingsAdvanced.strDirection": "Direction", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Garis coret ganda", "DE.Views.ParagraphSettingsAdvanced.strIndent": "Indentasi", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Jarak baris", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "Level outline", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Setela<PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Sebelum", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Spesial", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "<PERSON><PERSON><PERSON><PERSON> garis bersama", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "Satukan dengan berikutnya", "DE.Views.ParagraphSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indentasi & Peletakan", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "Putus Baris & Halaman", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "Penempatan", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "<PERSON><PERSON> ta<PERSON> interval antar paragraf dengan model yang sama", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON> ganda", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "Subskrip", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superskrip", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "<PERSON>ekan nomor garis", "DE.Views.ParagraphSettingsAdvanced.strTabs": "Tab", "DE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textAll": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "Sekurang-kurangnya", "DE.Views.ParagraphSettingsAdvanced.textAuto": "Banyak", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "<PERSON><PERSON> la<PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "<PERSON><PERSON> te<PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "<PERSON><PERSON> pada diagram atau gunakan tombol untuk memilih pembatas dan menerapkan pilihan model", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "Ukura<PERSON> tepi", "DE.Views.ParagraphSettingsAdvanced.textBottom": "<PERSON>wa<PERSON>", "DE.Views.ParagraphSettingsAdvanced.textCentered": "Tengah", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Spasi antar karakter", "DE.Views.ParagraphSettingsAdvanced.textContext": "Kontekstual", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "Kontekstual dan diskresioner", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "<PERSON><PERSON><PERSON><PERSON><PERSON>, historis, dan diskresioner", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "Konteks<PERSON><PERSON> dan be<PERSON>", "DE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON> baku", "DE.Views.ParagraphSettingsAdvanced.textDirLtr": "Left-to-right", "DE.Views.ParagraphSettingsAdvanced.textDirRtl": "Right-to-left", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "Diskresional", "DE.Views.ParagraphSettingsAdvanced.textEffects": "Efek", "DE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textHanging": "Menggantung", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "Historis dan diskresioner", "DE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textLeader": "Leader", "DE.Views.ParagraphSettingsAdvanced.textLeft": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textLevel": "Level", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "Ligatur", "DE.Views.ParagraphSettingsAdvanced.textNone": "Tidak ada", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nihil)", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "Fitur OpenType", "DE.Views.ParagraphSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textRemove": "Hapus", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON> semua", "DE.Views.ParagraphSettingsAdvanced.textRight": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textSet": "Tentukan", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textStandard": "<PERSON>ar saja", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "<PERSON>ar dan konte<PERSON>al", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON>, dan <PERSON>", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON>, dan historis", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "<PERSON>ar dan <PERSON>", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "<PERSON><PERSON>, historis, dan diskresioner", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "<PERSON>ar dan be<PERSON>ah", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "Tengah", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posisi tab", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraf - pengaturan lan<PERSON>t", "DE.Views.ParagraphSettingsAdvanced.textTop": "Atas", "DE.Views.ParagraphSettingsAdvanced.tipAll": "<PERSON><PERSON><PERSON> dan <PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "Buat Pembatas Bawah Saja", "DE.Views.ParagraphSettingsAdvanced.tipInner": "<PERSON><PERSON><PERSON> Horisontal Dalam <PERSON>", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "Buat Pembatas <PERSON>", "DE.Views.ParagraphSettingsAdvanced.tipNone": "Tanpa <PERSON>em<PERSON>as", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "Buat Pembatas Luar Saja", "DE.Views.ParagraphSettingsAdvanced.tipRight": "Buat Pembatas <PERSON>", "DE.Views.ParagraphSettingsAdvanced.tipTop": "Buat Pembatas Atas Saja", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "Tidak ada pembatas", "DE.Views.PrintWithPreview.textMarginsLast": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.textMarginsModerate": "<PERSON>rat", "DE.Views.PrintWithPreview.textMarginsNarrow": "Sempit", "DE.Views.PrintWithPreview.textMarginsNormal": "Normal", "DE.Views.PrintWithPreview.textMarginsWide": "<PERSON><PERSON>", "DE.Views.PrintWithPreview.txtAllPages": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtBothSides": "Cetak pada kedua sisi", "DE.Views.PrintWithPreview.txtBothSidesLongDesc": "Balik halaman pada sisi panjang", "DE.Views.PrintWithPreview.txtBothSidesShortDesc": "Balik halaman pada sisi pendek", "DE.Views.PrintWithPreview.txtBottom": "<PERSON>wa<PERSON>", "DE.Views.PrintWithPreview.txtCopies": "Salinan", "DE.Views.PrintWithPreview.txtCurrentPage": "Halaman saat ini", "DE.Views.PrintWithPreview.txtCustom": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtCustomPages": "Cetak k<PERSON>", "DE.Views.PrintWithPreview.txtLandscape": "Lansekap", "DE.Views.PrintWithPreview.txtLeft": "<PERSON><PERSON>", "DE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON>", "DE.Views.PrintWithPreview.txtOf": "dari {0}", "DE.Views.PrintWithPreview.txtOneSide": "Cetak satu sisi", "DE.Views.PrintWithPreview.txtOneSideDesc": "Cetak di satu sisi halaman saja", "DE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON>", "DE.Views.PrintWithPreview.txtPageNumInvalid": "Nomor halaman tidak valid", "DE.Views.PrintWithPreview.txtPageOrientation": "Orientasi ha<PERSON>", "DE.Views.PrintWithPreview.txtPages": "<PERSON><PERSON>", "DE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON><PERSON> halaman", "DE.Views.PrintWithPreview.txtPortrait": "Potret", "DE.Views.PrintWithPreview.txtPrint": "Cetak", "DE.Views.PrintWithPreview.txtPrintPdf": "Cetak ke PDF", "DE.Views.PrintWithPreview.txtPrintRange": "Rentang cetak", "DE.Views.PrintWithPreview.txtPrintSides": "Sisi cetak", "DE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON>", "DE.Views.PrintWithPreview.txtSelection": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtTop": "Atas", "DE.Views.ProtectDialog.textComments": "Komentar", "DE.Views.ProtectDialog.textForms": "<PERSON><PERSON><PERSON> isian", "DE.Views.ProtectDialog.textReview": "Perubahan terlacak", "DE.Views.ProtectDialog.textView": "Tidak ada <PERSON> (Baca saja)", "DE.Views.ProtectDialog.txtAllow": "Izinkan jenis <PERSON> ini saja pada dokumen", "DE.Views.ProtectDialog.txtIncorrectPwd": "Kata sandi konfirmasi tidak sama", "DE.Views.ProtectDialog.txtLimit": "Kata sandi dibatasi 15 karakter", "DE.Views.ProtectDialog.txtOptional": "opsional", "DE.Views.ProtectDialog.txtPassword": "<PERSON><PERSON>", "DE.Views.ProtectDialog.txtProtect": "Proteks<PERSON>", "DE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON>i kata sandi", "DE.Views.ProtectDialog.txtTitle": "Proteks<PERSON>", "DE.Views.ProtectDialog.txtWarning": "Peringatan: Tidak bisa dipulihkan jika Anda kehilangan atau lupa kata sandi. Simpan di tempat yang aman.", "DE.Views.RightMenu.ariaRightMenu": "<PERSON><PERSON> kanan", "DE.Views.RightMenu.txtChartSettings": "Pengaturan <PERSON>", "DE.Views.RightMenu.txtFormSettings": "Pengaturan Form", "DE.Views.RightMenu.txtHeaderFooterSettings": "<PERSON><PERSON><PERSON><PERSON> Header dan <PERSON>", "DE.Views.RightMenu.txtImageSettings": "Pengaturan Gambar", "DE.Views.RightMenu.txtMailMergeSettings": "Pengaturan merge email", "DE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.RightMenu.txtShapeSettings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.RightMenu.txtSignatureSettings": "<PERSON><PERSON><PERSON><PERSON> tanda tangan", "DE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.RightMenu.txtTextArtSettings": "Pengaturan Text Art", "DE.Views.RoleDeleteDlg.textLabel": "<PERSON><PERSON><PERSON> mengh<PERSON>us peran ini, <PERSON><PERSON> harus memindahkan bidang yang terkait dengannya ke peran lain.", "DE.Views.RoleDeleteDlg.textSelect": "<PERSON><PERSON>h untuk peran penggabung bidang", "DE.Views.RoleDeleteDlg.textTitle": "<PERSON><PERSON>", "DE.Views.RoleEditDlg.errNameExists": "<PERSON>an dengan nama seperti itu sudah ada.", "DE.Views.RoleEditDlg.textEmptyError": "<PERSON>a peran tidak boleh kosong.", "DE.Views.RoleEditDlg.textName": "<PERSON><PERSON> peran", "DE.Views.RoleEditDlg.textNameEx": "Contoh: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,", "DE.Views.RoleEditDlg.textNoHighlight": "Tanpa highlight", "DE.Views.RoleEditDlg.txtTitleEdit": "<PERSON><PERSON>", "DE.Views.RoleEditDlg.txtTitleNew": "Buat peran baru", "DE.Views.RolesManagerDlg.textAnyone": "Siapa pun", "DE.Views.RolesManagerDlg.textDelete": "Hapus", "DE.Views.RolesManagerDlg.textDeleteLast": "Anda yakin hendak mengmapus peran {0}?<br><PERSON><PERSON><PERSON>, peran baku akan dibuat.", "DE.Views.RolesManagerDlg.textDescription": "Tambahkan peran dan atur urutan para pengisi menerima dan menandatangani dokumen", "DE.Views.RolesManagerDlg.textDown": "Pindahkan peran ke bawah", "DE.Views.RolesManagerDlg.textEdit": "Sunting", "DE.Views.RolesManagerDlg.textEmpty": "Belum ada peran yang dibuat.<br>Buat minimal satu peran agar muncul di bidang ini.", "DE.Views.RolesManagerDlg.textNew": "<PERSON><PERSON>", "DE.Views.RolesManagerDlg.textUp": "Pindahkan peran ke atas", "DE.Views.RolesManagerDlg.txtTitle": "<PERSON><PERSON><PERSON>", "DE.Views.RolesManagerDlg.warnCantDelete": "Anda tidak bisa menghapus peran ini karena itu memiliki ruas terkait.", "DE.Views.RolesManagerDlg.warnDelete": "Anda yakin hendak menghapus peran {0}?", "DE.Views.SaveFormDlg.saveButtonText": "Simpan", "DE.Views.SaveFormDlg.textAnyone": "Siapa pun", "DE.Views.SaveFormDlg.textDescription": "Saat menyimpan ke pdf, hanya peran dengan bidang yang ditambahkan ke daftar isian", "DE.Views.SaveFormDlg.textEmpty": "Tidak ada peran yang dikaitkan dengan ruas.", "DE.Views.SaveFormDlg.textFill": "<PERSON><PERSON><PERSON> isian", "DE.Views.SaveFormDlg.txtTitle": "Simpan sebagai formulir", "DE.Views.ShapeSettings.strBackground": "<PERSON><PERSON> la<PERSON>", "DE.Views.ShapeSettings.strChange": "Ubah Bentuk", "DE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "DE.Views.ShapeSettings.strFill": "<PERSON><PERSON>", "DE.Views.ShapeSettings.strForeground": "<PERSON><PERSON> la<PERSON> depan", "DE.Views.ShapeSettings.strPattern": "Pola", "DE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON><PERSON> bayangan", "DE.Views.ShapeSettings.strSize": "Ukuran", "DE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "DE.Views.ShapeSettings.strTransparency": "Opasitas", "DE.Views.ShapeSettings.strType": "Tipe", "DE.Views.ShapeSettings.textAdjustShadow": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "DE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textBorderSizeErr": "<PERSON><PERSON> yang dimasukkan tidak tepat.<br><PERSON><PERSON><PERSON> masukkan nilai antara 0 pt dan 1584 pt.", "DE.Views.ShapeSettings.textColor": "<PERSON><PERSON> warna", "DE.Views.ShapeSettings.textDirection": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textEditPoints": "<PERSON> titik", "DE.Views.ShapeSettings.textEditShape": "<PERSON>", "DE.Views.ShapeSettings.textEmptyPattern": "Tanpa pola", "DE.Views.ShapeSettings.textEyedropper": "Eyedropper", "DE.Views.ShapeSettings.textFlip": "Flip", "DE.Views.ShapeSettings.textFromFile": "Dari file", "DE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textFromUrl": "Dari URL", "DE.Views.ShapeSettings.textGradient": "Titik Gradien", "DE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON> gradien", "DE.Views.ShapeSettings.textHint270": "Rotasi 90° Berlawanan Jarum Jam", "DE.Views.ShapeSettings.textHint90": "Rotasi 90° Searah Jarum Jam", "DE.Views.ShapeSettings.textHintFlipH": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textHintFlipV": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textImageTexture": "Gambar atau Tekstur", "DE.Views.ShapeSettings.textLinear": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textMoreColors": "<PERSON><PERSON><PERSON> banyak warna", "DE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON> isian", "DE.Views.ShapeSettings.textNoShadow": "No Shadow", "DE.Views.ShapeSettings.textPatternFill": "Pola", "DE.Views.ShapeSettings.textPosition": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textRadial": "Radial", "DE.Views.ShapeSettings.textRecentlyUsed": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textRotate90": "Rotasi 90°", "DE.Views.ShapeSettings.textRotation": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON> gambar", "DE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textShadow": "Bayangan", "DE.Views.ShapeSettings.textStretch": "Rentangkan", "DE.Views.ShapeSettings.textStyle": "Model", "DE.Views.ShapeSettings.textTexture": "<PERSON><PERSON> te<PERSON>", "DE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textWrap": "Bentuk Potongan", "DE.Views.ShapeSettings.tipAddGradientPoint": "Tambah titik gradien", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "Hilangkan titik gradien", "DE.Views.ShapeSettings.txtBehind": "Di belakang teks", "DE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtGranite": "Granit", "DE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtInFront": "<PERSON> depan <PERSON>", "DE.Views.ShapeSettings.txtInline": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtNoBorders": "Tidak ada <PERSON>", "DE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtSquare": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtThrough": "Tembus", "DE.Views.ShapeSettings.txtTight": "Ketat", "DE.Views.ShapeSettings.txtTopAndBottom": "Atas dan bawah", "DE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>", "DE.Views.SignatureSettings.notcriticalErrorTitle": "Peringatan", "DE.Views.SignatureSettings.strDelete": "Hilangkan Tandatangan", "DE.Views.SignatureSettings.strDetails": "Detail tanda tangan", "DE.Views.SignatureSettings.strInvalid": "Tanda tangan tidak valid", "DE.Views.SignatureSettings.strRequested": "Penandatangan yang diminta", "DE.Views.SignatureSettings.strSetup": "Penyiapan tanda tangan", "DE.Views.SignatureSettings.strSign": "Tandatangan", "DE.Views.SignatureSettings.strSignature": "<PERSON><PERSON>", "DE.Views.SignatureSettings.strSigner": "Penandatangan", "DE.Views.SignatureSettings.strValid": "Tanda tangan valid", "DE.Views.SignatureSettings.txtContinueEditing": "Tetap edit", "DE.Views.SignatureSettings.txtEditWarning": "Editing akan mengh<PERSON>us tandatangan dari dokumen.<br><PERSON><PERSON><PERSON><PERSON><PERSON>?", "DE.Views.SignatureSettings.txtRemoveWarning": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menghilangkan tandatangan ini?<br>Proses tidak bisa dikembalikan.", "DE.Views.SignatureSettings.txtRequestedSignatures": "Dokumen ini perlu ditandatangani.", "DE.Views.SignatureSettings.txtSigned": "Tandatangan valid sudah ditambahkan ke dokumen. Dokumen dilindungi dari penyuntingan.", "DE.Views.SignatureSettings.txtSignedInvalid": "Beberapa tanda tangan digital di dokumen tidak valid atau tidak bisa diverifikasi. Dokumen dilindungi dari pen<PERSON>an.", "DE.Views.Statusbar.goToPageText": "<PERSON><PERSON>", "DE.Views.Statusbar.pageIndexText": "<PERSON><PERSON> {0} dari {1}", "DE.Views.Statusbar.tipFitPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Statusbar.tipFitWidth": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Statusbar.tipHandTool": "Hand tool", "DE.Views.Statusbar.tipSelectTool": "<PERSON><PERSON><PERSON>", "DE.Views.Statusbar.tipSetLang": "Atur Bahasa Teks", "DE.Views.Statusbar.tipZoomFactor": "Pembesaran", "DE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON><PERSON>", "DE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Statusbar.txtPageNumInvalid": "<PERSON><PERSON> halaman salah", "DE.Views.Statusbar.txtPages": "<PERSON><PERSON>", "DE.Views.Statusbar.txtParagraphs": "Paragra<PERSON>", "DE.Views.Statusbar.txtSpaces": "Simbol dengan spasi", "DE.Views.Statusbar.txtSymbols": "Simbol", "DE.Views.Statusbar.txtWordCount": "Cacah kata", "DE.Views.Statusbar.txtWords": "<PERSON><PERSON>", "DE.Views.StyleTitleDialog.textHeader": "Buat gaya baru", "DE.Views.StyleTitleDialog.textNextStyle": "Style paragraf berik<PERSON>", "DE.Views.StyleTitleDialog.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.StyleTitleDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "DE.Views.StyleTitleDialog.txtNotEmpty": "<PERSON><PERSON><PERSON> tidak boleh kosong", "DE.Views.StyleTitleDialog.txtSameAs": "Sama seperti membuat style baru", "DE.Views.TableFormulaDialog.textBookmark": "<PERSON><PERSON><PERSON> markah", "DE.Views.TableFormulaDialog.textFormat": "Format nomor", "DE.Views.TableFormulaDialog.textFormula": "Formula", "DE.Views.TableFormulaDialog.textInsertFunction": "<PERSON><PERSON><PERSON> fungsi", "DE.Views.TableFormulaDialog.textTitle": "Pengaturan rumus", "DE.Views.TableOfContentsSettings.strAlign": "<PERSON><PERSON> halaman rata kanan", "DE.Views.TableOfContentsSettings.strFullCaption": "Sertakan label dan nomor", "DE.Views.TableOfContentsSettings.strLinks": "Format daftar isi sebagai tautan", "DE.Views.TableOfContentsSettings.strLinksOF": "Format daftar gambar sebagai link", "DE.Views.TableOfContentsSettings.strShowPages": "<PERSON><PERSON><PERSON><PERSON> nomor halaman", "DE.Views.TableOfContentsSettings.textBuildTable": "<PERSON><PERSON>t daftar isi dari", "DE.Views.TableOfContentsSettings.textBuildTableOF": "<PERSON>uat daftar gambar dari", "DE.Views.TableOfContentsSettings.textEquation": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textFigure": "Gambar", "DE.Views.TableOfContentsSettings.textLeader": "Leader", "DE.Views.TableOfContentsSettings.textLevel": "Level", "DE.Views.TableOfContentsSettings.textLevels": "Levels", "DE.Views.TableOfContentsSettings.textNone": "Tidak ada", "DE.Views.TableOfContentsSettings.textRadioCaption": "Caption", "DE.Views.TableOfContentsSettings.textRadioLevels": "Level outline", "DE.Views.TableOfContentsSettings.textRadioStyle": "Model", "DE.Views.TableOfContentsSettings.textRadioStyles": "Style yang dipilih", "DE.Views.TableOfContentsSettings.textStyle": "Model", "DE.Views.TableOfContentsSettings.textStyles": "Style", "DE.Views.TableOfContentsSettings.textTable": "<PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textTitle": "<PERSON><PERSON><PERSON> isi", "DE.Views.TableOfContentsSettings.textTitleTOF": "Daftar gambar", "DE.Views.TableOfContentsSettings.txtCentered": "Tengah", "DE.Views.TableOfContentsSettings.txtClassic": "Klasik", "DE.Views.TableOfContentsSettings.txtCurrent": "Saat ini", "DE.Views.TableOfContentsSettings.txtDistinctive": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.txtFormal": "Formal", "DE.Views.TableOfContentsSettings.txtModern": "Modern", "DE.Views.TableOfContentsSettings.txtOnline": "Online", "DE.Views.TableOfContentsSettings.txtSimple": "Simple", "DE.Views.TableOfContentsSettings.txtStandard": "Standard", "DE.Views.TableSettings.deleteColumnText": "Ha<PERSON> kolom", "DE.Views.TableSettings.deleteRowText": "Ha<PERSON> baris", "DE.Views.TableSettings.deleteTableText": "<PERSON><PERSON> tabel", "DE.Views.TableSettings.insertColumnLeftText": "Sisipkan kolom di kiri", "DE.Views.TableSettings.insertColumnRightText": "<PERSON>si<PERSON><PERSON>", "DE.Views.TableSettings.insertRowAboveText": "Sisipkan Baris di Atas", "DE.Views.TableSettings.insertRowBelowText": "Sisipkan Baris di Bawah", "DE.Views.TableSettings.mergeCellsText": "Gabungkan Sel", "DE.Views.TableSettings.selectCellText": "<PERSON><PERSON><PERSON> sel", "DE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON> kolom", "DE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON> baris", "DE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON> tabel", "DE.Views.TableSettings.splitCellsText": "Pisahkan Sel...", "DE.Views.TableSettings.splitCellTitleText": "Pi<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.strRepeatRow": "Ulangi sebagai baris header di bagian atas tiap halaman", "DE.Views.TableSettings.textAddFormula": "Tambah rumus", "DE.Views.TableSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "DE.Views.TableSettings.textBackColor": "<PERSON><PERSON>", "DE.Views.TableSettings.textBanded": "<PERSON><PERSON>", "DE.Views.TableSettings.textBorderColor": "<PERSON><PERSON>", "DE.Views.TableSettings.textBorders": "<PERSON><PERSON>", "DE.Views.TableSettings.textCellSize": "Ukuran baris & kolom", "DE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textConvert": "Konversi Tabel ke Teks", "DE.Views.TableSettings.textDistributeCols": "Distribusikan kolom", "DE.Views.TableSettings.textDistributeRows": "Distribusikan baris", "DE.Views.TableSettings.textEdit": "Baris & kolom", "DE.Views.TableSettings.textEmptyTemplate": "Tidak ada template", "DE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textHeader": "Header", "DE.Views.TableSettings.textHeight": "Tingg<PERSON>", "DE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textRows": "<PERSON><PERSON>", "DE.Views.TableSettings.textSelectBorders": "<PERSON><PERSON><PERSON> pembatas yang ingin Anda ubah dengan menerarpkan model yang telah dipilih di atas", "DE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON> dari templat", "DE.Views.TableSettings.textTotal": "Total", "DE.Views.TableSettings.textWidth": "<PERSON><PERSON>", "DE.Views.TableSettings.tipAll": "<PERSON><PERSON><PERSON> dan <PERSON><PERSON><PERSON>", "DE.Views.TableSettings.tipBottom": "Buat Pembatas Bawah-<PERSON><PERSON>", "DE.Views.TableSettings.tipInner": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.tipInnerHor": "<PERSON><PERSON><PERSON> Horisontal Dalam <PERSON>", "DE.Views.TableSettings.tipInnerVert": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.tipLeft": "Buat Pembatas <PERSON>-<PERSON><PERSON>", "DE.Views.TableSettings.tipNone": "Tanpa <PERSON>em<PERSON>as", "DE.Views.TableSettings.tipOuter": "Buat Pembatas Luar Saja", "DE.Views.TableSettings.tipRight": "Buat Pembatas <PERSON>-<PERSON><PERSON>", "DE.Views.TableSettings.tipTop": "Buat Pembatas Atas-Luar <PERSON>", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "<PERSON><PERSON> Tepi & Bergaris", "DE.Views.TableSettings.txtGroupTable_Custom": "Kustom", "DE.Views.TableSettings.txtGroupTable_Grid": "<PERSON><PERSON> kisi", "DE.Views.TableSettings.txtGroupTable_List": "<PERSON><PERSON>", "DE.Views.TableSettings.txtGroupTable_Plain": "Tabel polos", "DE.Views.TableSettings.txtNoBorders": "Tidak ada pembatas", "DE.Views.TableSettings.txtTable_Accent": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_Bordered": "<PERSON><PERSON>", "DE.Views.TableSettings.txtTable_BorderedAndLined": "Bergaris Tepi & Bergaris", "DE.Views.TableSettings.txtTable_Colorful": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_Dark": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_GridTable": "<PERSON><PERSON> kisi", "DE.Views.TableSettings.txtTable_Light": "<PERSON><PERSON>", "DE.Views.TableSettings.txtTable_Lined": "<PERSON><PERSON>", "DE.Views.TableSettings.txtTable_ListTable": "<PERSON><PERSON> daftar", "DE.Views.TableSettings.txtTable_PlainTable": "Tabel polos", "DE.Views.TableSettings.txtTable_TableGrid": "<PERSON><PERSON> tabel", "DE.Views.TableSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAlignment": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "<PERSON>ri spasi antar sel", "DE.Views.TableSettingsAdvanced.textAlt": "Teks alternatif", "DE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAltTip": "Representasi alternatif berbasis teks dari informasi objek visual, yang akan dibaca kepada orang dengan gangguan penglihatan atau kognitif untuk membantu mereka lebih memahami informasi yang ada dalam gambar, shape, grafik, atau tabel.", "DE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAnchorText": "Teks", "DE.Views.TableSettingsAdvanced.textAutofit": "O<PERSON><PERSON>s se<PERSON>kan ukuran dengan konten", "DE.Views.TableSettingsAdvanced.textBackColor": "<PERSON><PERSON> sel", "DE.Views.TableSettingsAdvanced.textBelow": "di bawah", "DE.Views.TableSettingsAdvanced.textBorderColor": "<PERSON><PERSON> te<PERSON>", "DE.Views.TableSettingsAdvanced.textBorderDesc": "<PERSON><PERSON> pada diagram atau gunakan tombol untuk memilih pembatas dan menerapkan pilihan model", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "Pembatas & Latar", "DE.Views.TableSettingsAdvanced.textBorderWidth": "Ukura<PERSON> tepi", "DE.Views.TableSettingsAdvanced.textBottom": "<PERSON>wa<PERSON>", "DE.Views.TableSettingsAdvanced.textCellOptions": "Opsi sel", "DE.Views.TableSettingsAdvanced.textCellProps": "Properti Sel", "DE.Views.TableSettingsAdvanced.textCellSize": "<PERSON><PERSON><PERSON> sel", "DE.Views.TableSettingsAdvanced.textCenter": "Tengah", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "Tengah", "DE.Views.TableSettingsAdvanced.textCheckMargins": "Gunakan margin standar", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "<PERSON><PERSON> sel baku", "DE.Views.TableSettingsAdvanced.textDistance": "Jarak dari teks", "DE.Views.TableSettingsAdvanced.textHorizontal": "Horisontal", "DE.Views.TableSettingsAdvanced.textIndLeft": "Indentasi dari kiri", "DE.Views.TableSettingsAdvanced.textLeft": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textMargin": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON> sel", "DE.Views.TableSettingsAdvanced.textMeasure": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textMove": "Pindah obyek bersama teks", "DE.Views.TableSettingsAdvanced.textOnlyCells": "<PERSON><PERSON> untuk sel yang dipilih", "DE.Views.TableSettingsAdvanced.textOptions": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textOverlap": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textPage": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textPrefWidth": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textRelative": "relatif den<PERSON>", "DE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textRightOf": "ke sebelah kanan dari", "DE.Views.TableSettingsAdvanced.textRightTooltip": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTable": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTableBackColor": "<PERSON>tar belakang tabel", "DE.Views.TableSettingsAdvanced.textTablePosition": "<PERSON>sisi tabel", "DE.Views.TableSettingsAdvanced.textTableSize": "Ukuran tabel", "DE.Views.TableSettingsAdvanced.textTitle": "Tabel - pengaturan lan<PERSON>t", "DE.Views.TableSettingsAdvanced.textTop": "Atas", "DE.Views.TableSettingsAdvanced.textVertical": "Vertikal", "DE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "Lebar & Spasi", "DE.Views.TableSettingsAdvanced.textWrap": "Pelipatan teks", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "<PERSON><PERSON> berderet", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "<PERSON><PERSON> peli<PERSON>", "DE.Views.TableSettingsAdvanced.textWrapText": "Wrap Teks", "DE.Views.TableSettingsAdvanced.tipAll": "<PERSON><PERSON><PERSON> dan <PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.tipCellAll": "Buat Pembatas untuk Sel Dalam Sa<PERSON>", "DE.Views.TableSettingsAdvanced.tipCellInner": "<PERSON><PERSON><PERSON> Vertikal dan Horisontal untuk Sel <PERSON>", "DE.Views.TableSettingsAdvanced.tipCellOuter": "Buat Pembatas Luar untuk Sel Dalam Saja", "DE.Views.TableSettingsAdvanced.tipInner": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.tipNone": "Tanpa <PERSON>em<PERSON>as", "DE.Views.TableSettingsAdvanced.tipOuter": "Buat Pembatas Luar Saja", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "Buat Pembatas Luar dan Pembatas untuk Semua <PERSON>", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "<PERSON><PERSON>t Pem<PERSON>as Lu<PERSON> dan <PERSON> Vertikal dan Horisontal untuk Sel Dalam", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "Buat Pembatas Luar Tabel dan Pembatas Luar untuk Sel Dalam", "DE.Views.TableSettingsAdvanced.txtCm": "Sentimeter", "DE.Views.TableSettingsAdvanced.txtInch": "Inci", "DE.Views.TableSettingsAdvanced.txtNoBorders": "Tidak ada pembatas", "DE.Views.TableSettingsAdvanced.txtPercent": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.txtPt": "Titik", "DE.Views.TableToTextDialog.textEmpty": "<PERSON>a harus mengetikkan sebuah karakter untuk pemisah ubahan.", "DE.Views.TableToTextDialog.textNested": "Kon<PERSON>i tabel nested", "DE.Views.TableToTextDialog.textOther": "<PERSON><PERSON><PERSON>", "DE.Views.TableToTextDialog.textPara": "<PERSON><PERSON> paragraf", "DE.Views.TableToTextDialog.textSemicolon": "Semicolons", "DE.Views.TableToTextDialog.textSeparator": "Pisahkan teks dengan", "DE.Views.TableToTextDialog.textTab": "Tab", "DE.Views.TableToTextDialog.textTitle": "Konversi tabel ke teks", "DE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "DE.Views.TextArtSettings.strFill": "<PERSON><PERSON>", "DE.Views.TextArtSettings.strSize": "Ukuran", "DE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "DE.Views.TextArtSettings.strTransparency": "Opasitas", "DE.Views.TextArtSettings.strType": "Tipe", "DE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textBorderSizeErr": "<PERSON><PERSON> yang dimasukkan tidak tepat.<br><PERSON><PERSON><PERSON> masukkan nilai antara 0 pt dan 1584 pt.", "DE.Views.TextArtSettings.textColor": "<PERSON><PERSON> warna", "DE.Views.TextArtSettings.textDirection": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textGradient": "Titik Gradien", "DE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON> gradien", "DE.Views.TextArtSettings.textLinear": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON> isian", "DE.Views.TextArtSettings.textPosition": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textRadial": "Radial", "DE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textStyle": "Model", "DE.Views.TextArtSettings.textTemplate": "Template", "DE.Views.TextArtSettings.textTransform": "Transform", "DE.Views.TextArtSettings.tipAddGradientPoint": "Tambah titik gradien", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "Hilangkan titik gradien", "DE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON>", "DE.Views.TextToTableDialog.textAutofit": "Perilaku Autofit", "DE.Views.TextToTableDialog.textColumns": "<PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textContents": "Autofit ke konten", "DE.Views.TextToTableDialog.textEmpty": "<PERSON>a harus mengetikkan sebuah karakter untuk pemisah ubahan.", "DE.Views.TextToTableDialog.textFixed": "<PERSON><PERSON> kolom tetap", "DE.Views.TextToTableDialog.textOther": "<PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textPara": "Paragra<PERSON>", "DE.Views.TextToTableDialog.textRows": "<PERSON><PERSON>", "DE.Views.TextToTableDialog.textSemicolon": "Semicolons", "DE.Views.TextToTableDialog.textSeparator": "Pisahkan teks pada", "DE.Views.TextToTableDialog.textTab": "Tab", "DE.Views.TextToTableDialog.textTableSize": "Ukuran tabel", "DE.Views.TextToTableDialog.textTitle": "Konversi teks ke tabel", "DE.Views.TextToTableDialog.textWindow": "Autofit ke jendela", "DE.Views.TextToTableDialog.txtAutoText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnAddComment": "Tambahkan Komentar", "DE.Views.Toolbar.capBtnBlankPage": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnColumns": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnComment": "Komentar", "DE.Views.Toolbar.capBtnDateTime": "Tanggal & Jam", "DE.Views.Toolbar.capBtnHand": "Tangan", "DE.Views.Toolbar.capBtnHyphenation": "<PERSON>da hubung", "DE.Views.Toolbar.capBtnInsChart": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsControls": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsDropcap": "Drop Cap", "DE.Views.Toolbar.capBtnInsEquation": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsField": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsHeader": "Header & Footer", "DE.Views.Toolbar.capBtnInsImage": "Gambar", "DE.Views.Toolbar.capBtnInsPagebreak": "Breaks", "DE.Views.Toolbar.capBtnInsShape": "Bentuk", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "Simbol", "DE.Views.Toolbar.capBtnInsTable": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsTextart": "Text Art", "DE.Views.Toolbar.capBtnInsTextbox": "Kotak Teks", "DE.Views.Toolbar.capBtnInsTextFromFile": "Teks dari <PERSON>", "DE.Views.Toolbar.capBtnLineNumbers": "Nomor garis", "DE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnPageColor": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnPageOrient": "Orientasi", "DE.Views.Toolbar.capBtnPageSize": "Ukuran", "DE.Views.Toolbar.capBtnSelect": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnWatermark": "Watermark", "DE.Views.Toolbar.capColorScheme": "<PERSON><PERSON>", "DE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capImgBackward": "Mundurkan", "DE.Views.Toolbar.capImgForward": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capImgGroup": "Grup", "DE.Views.Toolbar.capImgWrapping": "Wrapping", "DE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "DE.Views.Toolbar.mniCapitalizeWords": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.mniCustomTable": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.mniDrawTable": "Buat tabel", "DE.Views.Toolbar.mniEditControls": "Pengaturan kontrol", "DE.Views.Toolbar.mniEditDropCap": "Pengaturan Drop Cap", "DE.Views.Toolbar.mniEditFooter": "Sunting kaki", "DE.Views.Toolbar.mniEditHeader": "Sunting kepala", "DE.Views.Toolbar.mniEraseTable": "<PERSON><PERSON> tabel", "DE.Views.Toolbar.mniFromFile": "<PERSON><PERSON>", "DE.Views.Toolbar.mniFromStorage": "<PERSON><PERSON>", "DE.Views.Toolbar.mniFromUrl": "Dari URL", "DE.Views.Toolbar.mniHiddenBorders": "<PERSON><PERSON><PERSON><PERSON>bel Disembunyikan", "DE.Views.Toolbar.mniHiddenChars": "<PERSON>kter Tidak Dicetak", "DE.Views.Toolbar.mniHighlightControls": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON>an", "DE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON> dari file", "DE.Views.Toolbar.mniImageFromStorage": "<PERSON><PERSON><PERSON> da<PERSON>", "DE.Views.Toolbar.mniImageFromUrl": "Gambar dari URL", "DE.Views.Toolbar.mniInsertSSE": "Sisipkan Spreadsheet", "DE.Views.Toolbar.mniLowerCase": "huruf k<PERSON>il", "DE.Views.Toolbar.mniRemoveFooter": "<PERSON><PERSON> kaki", "DE.Views.Toolbar.mniRemoveHeader": "Hapus kepala", "DE.Views.Toolbar.mniSentenceCase": "<PERSON> kalimat.", "DE.Views.Toolbar.mniTextFromLocalFile": "Teks dari berkas lokal", "DE.Views.Toolbar.mniTextFromStorage": "<PERSON>ks dari berkas <PERSON>", "DE.Views.Toolbar.mniTextFromURL": "Teks dari berkas URL", "DE.Views.Toolbar.mniTextToTable": "Konversikan teks ke tabel", "DE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "DE.Views.Toolbar.mniUpperCase": "HURUFBESAR", "DE.Views.Toolbar.strMenuNoFill": "<PERSON><PERSON>", "DE.Views.Toolbar.textAddSpaceAfter": "<PERSON><PERSON> ruang setelah paragraf", "DE.Views.Toolbar.textAddSpaceBefore": "Tambah ruang sebelum paragraf", "DE.Views.Toolbar.textAlpha": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textAutoColor": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textBetta": "Huruf <PERSON>", "DE.Views.Toolbar.textBlackHeart": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textBold": "<PERSON><PERSON>", "DE.Views.Toolbar.textBottom": "Bawah: ", "DE.Views.Toolbar.textBullet": "Butir", "DE.Views.Toolbar.textChangeLevel": "Ganti Level List", "DE.Views.Toolbar.textCheckboxControl": "Kotak centang", "DE.Views.Toolbar.textColumnsCustom": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textColumnsLeft": "<PERSON><PERSON>", "DE.Views.Toolbar.textColumnsOne": "<PERSON><PERSON>", "DE.Views.Toolbar.textColumnsRight": "<PERSON><PERSON>", "DE.Views.Toolbar.textColumnsThree": "Tiga", "DE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON>", "DE.Views.Toolbar.textComboboxControl": "Kotak Kombo", "DE.Views.Toolbar.textContinuous": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textContPage": "Halaman Bersambung", "DE.Views.Toolbar.textCopyright": "Tanda Hak Cipta", "DE.Views.Toolbar.textCustomHyphen": "Opsi tanda hubung", "DE.Views.Toolbar.textCustomLineNumbers": "Opsi Penomoran Garis", "DE.Views.Toolbar.textDateControl": "Penitik tanggal", "DE.Views.Toolbar.textDegree": "<PERSON><PERSON>", "DE.Views.Toolbar.textDelta": "Huruf Delta Kecil", "DE.Views.Toolbar.textDirLtr": "Left-to-right", "DE.Views.Toolbar.textDirRtl": "Right-to-left", "DE.Views.Toolbar.textDivision": "Tanda Pembagi", "DE.Views.Toolbar.textDollar": "<PERSON><PERSON>", "DE.Views.Toolbar.textDropdownControl": "List drop-down", "DE.Views.Toolbar.textEditMode": "Edit PDF", "DE.Views.Toolbar.textEditWatermark": "Tanda air ubahan", "DE.Views.Toolbar.textEuro": "Tanda Euro", "DE.Views.Toolbar.textEvenPage": "<PERSON><PERSON> genap", "DE.Views.Toolbar.textGreaterEqual": "<PERSON><PERSON><PERSON> dari atau <PERSON>", "DE.Views.Toolbar.textIndAfter": "Indent after", "DE.Views.Toolbar.textIndBefore": "Indent before", "DE.Views.Toolbar.textIndLeft": "Indentasi kiri", "DE.Views.Toolbar.textIndRight": "Right indent", "DE.Views.Toolbar.textInfinity": "Tak Terbatas", "DE.Views.Toolbar.textInMargin": "<PERSON><PERSON>", "DE.Views.Toolbar.textInsColumnBreak": "Sisipkan Break Kolom", "DE.Views.Toolbar.textInsertPageCount": "<PERSON><PERSON><PERSON><PERSON> nomor dari halaman", "DE.Views.Toolbar.textInsertPageNumber": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textInsPageBreak": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textInsSectionBreak": "Sisipkan Jeda Bagian", "DE.Views.Toolbar.textInText": "<PERSON><PERSON> teks", "DE.Views.Toolbar.textItalic": "Miring", "DE.Views.Toolbar.textLandscape": "Landscape", "DE.Views.Toolbar.textLeft": "<PERSON><PERSON>: ", "DE.Views.Toolbar.textLessEqual": "<PERSON><PERSON> atau <PERSON>", "DE.Views.Toolbar.textLetterPi": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textLineSpaceOptions": "Line spacing options", "DE.Views.Toolbar.textListSettings": "<PERSON><PERSON><PERSON><PERSON> daftar", "DE.Views.Toolbar.textMarginsLast": "Custom Terakhir", "DE.Views.Toolbar.textMarginsModerate": "<PERSON>rat", "DE.Views.Toolbar.textMarginsNarrow": "<PERSON>rrow", "DE.Views.Toolbar.textMarginsNormal": "Normal", "DE.Views.Toolbar.textMarginsWide": "Wide", "DE.Views.Toolbar.textMoreSymbols": "Simbol lainnya", "DE.Views.Toolbar.textNewColor": "<PERSON><PERSON><PERSON> banyak warna", "DE.Views.Toolbar.textNextPage": "<PERSON><PERSON>", "DE.Views.Toolbar.textNoHighlight": "Tanpa highlight", "DE.Views.Toolbar.textNone": "Tidak ada", "DE.Views.Toolbar.textNotEqualTo": "Tidak Sama Den<PERSON>", "DE.Views.Toolbar.textOddPage": "<PERSON><PERSON>", "DE.Views.Toolbar.textOneHalf": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textOneQuarter": "Pecahan <PERSON>hana Seperempat", "DE.Views.Toolbar.textPageMarginsCustom": "Custom Margin", "DE.Views.Toolbar.textPageSizeCustom": "<PERSON><PERSON><PERSON> halaman ubahan", "DE.Views.Toolbar.textPictureControl": "Gambar", "DE.Views.Toolbar.textPlainControl": "<PERSON><PERSON> <PERSON>", "DE.Views.Toolbar.textPlusMinus": "Tanda Plus-Minus", "DE.Views.Toolbar.textPortrait": "Portrait", "DE.Views.Toolbar.textRegistered": "<PERSON><PERSON>", "DE.Views.Toolbar.textRemoveControl": "Hilangkan Kontrol Konten", "DE.Views.Toolbar.textRemSpaceAfter": "Remove space after paragraph", "DE.Views.Toolbar.textRemSpaceBefore": "Remove space before paragraph", "DE.Views.Toolbar.textRemWatermark": "Hilangkan tanda air", "DE.Views.Toolbar.textRestartEachPage": "<PERSON><PERSON>", "DE.Views.Toolbar.textRestartEachSection": "<PERSON><PERSON>", "DE.Views.Toolbar.textRichControl": "Rich text", "DE.Views.Toolbar.textRight": "Kanan: ", "DE.Views.Toolbar.textSection": "<PERSON><PERSON>", "DE.Views.Toolbar.textShapesCombine": "Combine", "DE.Views.Toolbar.textShapesFragment": "Fragment", "DE.Views.Toolbar.textShapesIntersect": "Intersect", "DE.Views.Toolbar.textShapesSubstract": "Subtract", "DE.Views.Toolbar.textShapesUnion": "Union", "DE.Views.Toolbar.textSmile": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textSpaceAfter": "Space after", "DE.Views.Toolbar.textSpaceBefore": "Space before", "DE.Views.Toolbar.textSquareRoot": "<PERSON><PERSON>", "DE.Views.Toolbar.textStrikeout": "<PERSON><PERSON> ganda", "DE.Views.Toolbar.textStyleMenuDelete": "Hapus style", "DE.Views.Toolbar.textStyleMenuDeleteAll": "Hapus semua style custom", "DE.Views.Toolbar.textStyleMenuNew": "Style baru dari pilihan", "DE.Views.Toolbar.textStyleMenuRestore": "Kembalikan ke default", "DE.Views.Toolbar.textStyleMenuRestoreAll": "<PERSON><PERSON><PERSON><PERSON> semua ke gaya default", "DE.Views.Toolbar.textStyleMenuUpdate": "Update dari pilihan", "DE.Views.Toolbar.textSubscript": "Subskrip", "DE.Views.Toolbar.textSuperscript": "Superskrip", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "Menekan untuk Paragraf Saat Ini", "DE.Views.Toolbar.textTabCollaboration": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabDraw": "Gambar", "DE.Views.Toolbar.textTabFile": "File", "DE.Views.Toolbar.textTabHome": "<PERSON><PERSON>", "DE.Views.Toolbar.textTabInsert": "<PERSON>sip<PERSON>", "DE.Views.Toolbar.textTabLayout": "Layout", "DE.Views.Toolbar.textTabLinks": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabProtect": "Proteks<PERSON>", "DE.Views.Toolbar.textTabReview": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabView": "Lihat", "DE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "DE.Views.Toolbar.textTitleError": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textToCurrent": "Ke posisi saat ini", "DE.Views.Toolbar.textTop": "Atas: ", "DE.Views.Toolbar.textTradeMark": "<PERSON><PERSON>", "DE.Views.Toolbar.textUnderline": "<PERSON><PERSON> bawah", "DE.Views.Toolbar.textYen": "Tanda <PERSON>n", "DE.Views.Toolbar.tipAlignCenter": "Rata tengah", "DE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON>", "DE.Views.Toolbar.tipAlignLeft": "Rata kiri", "DE.Views.Toolbar.tipAlignRight": "<PERSON>a kanan", "DE.Views.Toolbar.tipBack": "Kembali", "DE.Views.Toolbar.tipBlankPage": "<PERSON>sip<PERSON> halaman kosong", "DE.Views.Toolbar.tipChangeCase": "Ubah case", "DE.Views.Toolbar.tipChangeChart": "Ubah Tipe Bagan", "DE.Views.Toolbar.tipClearStyle": "Hapus Model", "DE.Views.Toolbar.tipColorSchemas": "Ubah Skema Warna", "DE.Views.Toolbar.tipColumns": "Sisipkan kolom", "DE.Views.Toolbar.tipControls": "Sisipkan kontrol konten", "DE.Views.Toolbar.tipCopy": "<PERSON><PERSON>", "DE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON>", "DE.Views.Toolbar.tipCut": "Potong", "DE.Views.Toolbar.tipDateTime": "<PERSON>sip<PERSON> tanggal dan jam sekarang", "DE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipDecPrLeft": "Kurangi Indentasi", "DE.Views.Toolbar.tipDownload": "Download file", "DE.Views.Toolbar.tipDropCap": "Sisipkan Drop Cap", "DE.Views.Toolbar.tipEditHeader": "<PERSON> Header at<PERSON>", "DE.Views.Toolbar.tipEditMode": "Sunting berkas kini.<br> <PERSON><PERSON> akan dimuat ulang.", "DE.Views.Toolbar.tipFontColor": "<PERSON><PERSON>", "DE.Views.Toolbar.tipFontName": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipFontSize": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipHandTool": "Hand tool", "DE.Views.Toolbar.tipHighlightColor": "<PERSON><PERSON>", "DE.Views.Toolbar.tipHyphenation": "<PERSON><PERSON> tanda hubung", "DE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON> ob<PERSON>", "DE.Views.Toolbar.tipImgGroup": "Satukan objek", "DE.Views.Toolbar.tipImgWrapping": "Wrap Teks", "DE.Views.Toolbar.tipIncFont": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipIncPrLeft": "Tambahkan Indentasi", "DE.Views.Toolbar.tipInsertChart": "Sisipkan Bagan", "DE.Views.Toolbar.tipInsertEquation": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipInsertHorizontalText": "Sisipkan kotak teks horizontal", "DE.Views.Toolbar.tipInsertImage": "Sisipkan Gambar", "DE.Views.Toolbar.tipInsertNum": "<PERSON><PERSON><PERSON><PERSON> nomor halaman", "DE.Views.Toolbar.tipInsertShape": "<PERSON>sip<PERSON>", "DE.Views.Toolbar.tipInsertSmartArt": "Sisipkan SmartArt", "DE.Views.Toolbar.tipInsertSymbol": "Sisipkan simbol", "DE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipInsertText": "Sisipkan kotak teks", "DE.Views.Toolbar.tipInsertTextArt": "Sisipkan Text Art", "DE.Views.Toolbar.tipInsertVerticalText": "Sisipkan kotak teks vertikal", "DE.Views.Toolbar.tipInsField": "<PERSON>sip<PERSON> ruas", "DE.Views.Toolbar.tipLineNumbers": "<PERSON><PERSON><PERSON><PERSON> nomor garis", "DE.Views.Toolbar.tipLineSpace": "Spasi Antar Baris Paragraf", "DE.Views.Toolbar.tipMailRecepients": "Merge email", "DE.Views.Toolbar.tipMarkers": "Butir", "DE.Views.Toolbar.tipMarkersArrow": "<PERSON><PERSON> panah", "DE.Views.Toolbar.tipMarkersCheckmark": "Butir tanda centang", "DE.Views.Toolbar.tipMarkersDash": "<PERSON><PERSON><PERSON> putus-putus", "DE.Views.Toolbar.tipMarkersFRhombus": "Butir belah ketupat isi", "DE.Views.Toolbar.tipMarkersFRound": "<PERSON><PERSON> ling<PERSON>n isi", "DE.Views.Toolbar.tipMarkersFSquare": "Butir persegi isi", "DE.Views.Toolbar.tipMarkersHRound": "Butir bundar hollow", "DE.Views.Toolbar.tipMarkersStar": "<PERSON><PERSON> bintang", "DE.Views.Toolbar.tipMultiLevelArticl": "Artikel dengan penomoran bertingkat", "DE.Views.Toolbar.tipMultiLevelChapter": "Bab dengan penomoran bertingkat", "DE.Views.Toolbar.tipMultiLevelHeadings": "Tajuk dengan penomoran bertingkat", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "Tajuk dengan beragam penomoran bertingkat", "DE.Views.Toolbar.tipMultiLevelNumbered": "Butir nomor multi-level", "DE.Views.Toolbar.tipMultilevels": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipMultiLevelSymbols": "Butir simbol multi-level", "DE.Views.Toolbar.tipMultiLevelVarious": "Butir simbol variasi multi-level", "DE.Views.Toolbar.tipNumbers": "Penomoran", "DE.Views.Toolbar.tipPageBreak": "<PERSON><PERSON><PERSON><PERSON> atau jeda Bagian", "DE.Views.Toolbar.tipPageColor": "<PERSON><PERSON> warna halaman", "DE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON> halaman", "DE.Views.Toolbar.tipPageOrient": "<PERSON><PERSON>", "DE.Views.Toolbar.tipPageSize": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipParagraphStyle": "<PERSON><PERSON> para<PERSON><PERSON>", "DE.Views.Toolbar.tipPaste": "Tempel", "DE.Views.Toolbar.tipPrColor": "Bayangan", "DE.Views.Toolbar.tipPrint": "Cetak", "DE.Views.Toolbar.tipPrintQuick": "Cetak cepat", "DE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipReplace": "Ganti", "DE.Views.Toolbar.tipSave": "Simpan", "DE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON>an per<PERSON>han yang Anda buat agar dapat dilihat oleh pengguna lain", "DE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON> se<PERSON>a", "DE.Views.Toolbar.tipSelectTool": "<PERSON><PERSON><PERSON> alat", "DE.Views.Toolbar.tipSendBackward": "Mundurkan", "DE.Views.Toolbar.tipSendForward": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipShapesMerge": "Merge shapes", "DE.Views.Toolbar.tipShowHiddenChars": "<PERSON>kter Tidak Dicetak", "DE.Views.Toolbar.tipSynchronize": "Dokumen telah diubah oleh pengguna lain. Silakan klik untuk menyimpan perubahan dan memuat ulang pembaruan.", "DE.Views.Toolbar.tipTextDir": "Text direction", "DE.Views.Toolbar.tipTextFromFile": "<PERSON>ks dari berkas", "DE.Views.Toolbar.tipUndo": "Batalkan", "DE.Views.Toolbar.tipWatermark": "Edit watermark", "DE.Views.Toolbar.txtAutoText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtDistribHor": "Distribusikan Horizontal", "DE.Views.Toolbar.txtDistribVert": "Distribusikan Vertikal", "DE.Views.Toolbar.txtGroupBulletDoc": "Butir dokumen", "DE.Views.Toolbar.txtGroupBulletLib": "<PERSON><PERSON><PERSON> bulatan", "DE.Views.Toolbar.txtGroupMultiDoc": "Daftar pada dokumen saat ini", "DE.Views.Toolbar.txtGroupMultiLib": "<PERSON><PERSON><PERSON> daftar", "DE.Views.Toolbar.txtGroupNumDoc": "Format penomoran dokumen", "DE.Views.Toolbar.txtGroupNumLib": "Pustaka <PERSON>omo<PERSON>", "DE.Views.Toolbar.txtGroupRecent": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtMarginAlign": "<PERSON><PERSON><PERSON> ke margin", "DE.Views.Toolbar.txtObjectsAlign": "<PERSON><PERSON>n objek yang dipilih", "DE.Views.Toolbar.txtPageAlign": "<PERSON><PERSON><PERSON> ke halaman", "DE.Views.ViewTab.textAlwaysShowToolbar": "<PERSON><PERSON><PERSON>", "DE.Views.ViewTab.textDarkDocument": "Do<PERSON>men gelap", "DE.Views.ViewTab.textFill": "<PERSON><PERSON>", "DE.Views.ViewTab.textFitToPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ViewTab.textFitToWidth": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ViewTab.textInterfaceTheme": "<PERSON><PERSON> ant<PERSON>", "DE.Views.ViewTab.textLeftMenu": "<PERSON> Kiri", "DE.Views.ViewTab.textLine": "<PERSON><PERSON>", "DE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "DE.Views.ViewTab.textNavigation": "Na<PERSON><PERSON><PERSON>", "DE.Views.ViewTab.textOutline": "Tajuk", "DE.Views.ViewTab.textRightMenu": "<PERSON>", "DE.Views.ViewTab.textRulers": "<PERSON><PERSON><PERSON>", "DE.Views.ViewTab.textStatusBar": "Bar Status", "DE.Views.ViewTab.textTabStyle": "Gaya tab", "DE.Views.ViewTab.textZoom": "Pembesaran", "DE.Views.ViewTab.tipDarkDocument": "Do<PERSON>men gelap", "DE.Views.ViewTab.tipFitToPage": "Paskan dengan halaman", "DE.Views.ViewTab.tipFitToWidth": "Paskan sesuai lebar", "DE.Views.ViewTab.tipHeadings": "Tajuk", "DE.Views.ViewTab.tipInterfaceTheme": "<PERSON><PERSON> ant<PERSON>", "DE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textBold": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textColor": "Warna teks", "DE.Views.WatermarkSettingsDialog.textDiagonal": "Diagonal", "DE.Views.WatermarkSettingsDialog.textFont": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textFromFile": "Dari file", "DE.Views.WatermarkSettingsDialog.textFromStorage": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textFromUrl": "Dari URL", "DE.Views.WatermarkSettingsDialog.textHor": "Horisontal", "DE.Views.WatermarkSettingsDialog.textImageW": "Watermark gambar", "DE.Views.WatermarkSettingsDialog.textItalic": "Miring", "DE.Views.WatermarkSettingsDialog.textLanguage": "Bahasa", "DE.Views.WatermarkSettingsDialog.textLayout": "Layout", "DE.Views.WatermarkSettingsDialog.textNone": "Tidak ada", "DE.Views.WatermarkSettingsDialog.textScale": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textSelect": "<PERSON><PERSON><PERSON> gambar", "DE.Views.WatermarkSettingsDialog.textStrikeout": "<PERSON><PERSON> ganda", "DE.Views.WatermarkSettingsDialog.textText": "Teks", "DE.Views.WatermarkSettingsDialog.textTextW": "Watermark teks", "DE.Views.WatermarkSettingsDialog.textTitle": "Pengaturan watermark", "DE.Views.WatermarkSettingsDialog.textTransparency": "Semi Transparan", "DE.Views.WatermarkSettingsDialog.textUnderline": "<PERSON><PERSON> bawah", "DE.Views.WatermarkSettingsDialog.tipFontName": "Nama font", "DE.Views.WatermarkSettingsDialog.tipFontSize": "Ukuran font", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}