/* minified by terser */
if(void 0===Common)var Common={};if(Common.Views=Common.Views||{},define("common/main/lib/view/PluginDlg",[],(function(){"use strict";Common.Views.PluginDlg=Common.UI.Window.extend(_.extend({initialize:function(t){var i={};_.extend(i,{header:!0,enableKeyEvents:!1,automove:!1},t),this.bordersOffset=40,i.width=Common.Utils.innerWidth()-2*this.bordersOffset-i.width<0?Common.Utils.innerWidth()-2*this.bordersOffset:i.width,i.cls+=" advanced-settings-dlg invisible-borders",(!i.buttons||_.size(i.buttons)<1)&&(i.cls+=" no-footer"),i.contentHeight=i.height,i.height="auto",this.template=['<div id="id-plugin-container" class="box" style="height:'+i.contentHeight+'px;">','<div id="id-plugin-placeholder" style="width: 100%;height: 100%;"></div>',"</div>",'<% if ((typeof buttons !== "undefined") && _.size(buttons) > 0) { %>','<div class="separator horizontal"></div>',"<% } %>"].join(""),i.tpl=_.template(this.template)(i),this.url=t.url||"",this.loader=void 0===t.loader||t.loader,this.frameId=t.frameId||"plugin_iframe",this.guid=t.guid,Common.UI.Window.prototype.initialize.call(this,i)},render:function(){Common.UI.Window.prototype.render.call(this);var t=this.$window.find("> .body");t.css({height:"auto",overflow:"hidden"}),this.boxEl=this.$window.find(".body > .box"),this._headerFooterHeight=this.options.header?parseInt(this.$window.find(".header").css("height")):0,this.options.buttons&&_.size(this.options.buttons)>0&&(this._headerFooterHeight+=parseInt(this.$window.find(".footer").css("height"))+parseInt(t.css("padding-top"))+parseInt(t.css("padding-bottom"))),this._headerFooterHeight+=parseInt(this.$window.css("border-top-width"))+parseInt(this.$window.css("border-bottom-width")),Common.Utils.innerHeight()-2*this.bordersOffset<this.options.contentHeight+this._headerFooterHeight&&(this._restoreHeight=this.options.contentHeight+this._headerFooterHeight,this.options.contentHeight=Common.Utils.innerHeight()-2*this.bordersOffset-this._headerFooterHeight,this.boxEl.css("height",this.options.contentHeight)),this.$window.find(".header").prepend($('<div class="tools left hidden"></div>'));var i=document.createElement("iframe");i.id=this.frameId,i.name="pluginFrameEditor",i.width="100%",i.height="100%",i.align="top",i.frameBorder=0,i.scrolling="no",i.allow="camera; microphone; display-capture",i.onload=_.bind(this._onLoad,this);var e=this,n=this.$window.find("#id-plugin-placeholder");this.loader&&setTimeout((function(){e.isLoaded||(e.loadMask=new Common.UI.LoadMask({owner:n}),e.loadMask.setTitle(e.textLoading),e.loadMask.show(),e.isLoaded&&e.loadMask.hide())}),500),i.src=this.url,n.append(i),this.frame=i,this.on("resizing",(function(t){e.boxEl.css("height",parseInt(e.$window.css("height"))-e._headerFooterHeight)}));var o=function(){e.onWindowResize()};$(window).on("resize",o),this.on("close",(function(){$(window).off("resize",o)})),this.options.isCanDocked&&this.showDockedButton()},_onLoad:function(){this.isLoaded=!0,this.loadMask&&this.loadMask.hide()},setInnerSize:function(t,i){var e=Common.Utils.innerHeight(),n=Common.Utils.innerWidth(),o=parseInt(this.$window.css("border-left-width"))+parseInt(this.$window.css("border-right-width")),s=2*this.bordersOffset;e-s<i+this._headerFooterHeight&&(i=e-s-this._headerFooterHeight),n-s<t+o&&(t=n-s-o),this.boxEl.css("height",i),Common.UI.Window.prototype.setHeight.call(this,i+this._headerFooterHeight),Common.UI.Window.prototype.setWidth.call(this,t+o),this.$window.css("left",(n-t-o)/2),this.$window.css("top",(e-i-this._headerFooterHeight)/2),this._restoreHeight=this._restoreWidth=void 0},onWindowResize:function(){var t=Common.Utils.innerWidth(),i=Common.Utils.innerHeight(),e=this.getWidth(),n=this.getHeight(),o=this.resizable?0:this.bordersOffset;if(n<i-2*o+.1){if(!this.resizable&&this._restoreHeight>0&&n<this._restoreHeight){var s=Math.max(Math.min(this._restoreHeight,i-2*o),this.initConfig.minheight);this.setHeight(s),this.boxEl.css("height",s-this._headerFooterHeight)}var a=this.getTop();a<o?this.$window.css("top",o):a+n>i-o&&this.$window.css("top",i-o-n)}else void 0===this._restoreHeight&&(this._restoreHeight=n),this.setHeight(Math.max(i-2*o,this.initConfig.minheight)),this.boxEl.css("height",Math.max(i-2*o,this.initConfig.minheight)-this._headerFooterHeight),this.$window.css("top",o);if(e<t-2*o+.1){!this.resizable&&this._restoreWidth>0&&e<this._restoreWidth&&this.setWidth(Math.max(Math.min(this._restoreWidth,t-2*o),this.initConfig.minwidth));var l=this.getLeft();l<o?this.$window.css("left",o):l+e>t-o&&this.$window.css("left",t-o-e)}else void 0===this._restoreWidth&&(this._restoreWidth=e),this.setWidth(Math.max(t-2*o,this.initConfig.minwidth)),this.$window.css("left",o)},showDockedButton:function(){var t=this.$window.find(".header .tools:not(.left)"),i="id-plugindlg-docked",e=t.find("#"+i);if(e.length<1){(e=$('<div id="'+i+'" class="tool custom toolbar__icon btn-pin"></div>')).on("click",_.bind((function(){var t=e.data("bs.tooltip");t&&(t.dontShow=!0),this.fireEvent("docked",this.frameId)}),this)),t.append(e),e.tooltip({title:this.textDock,placement:"cursor",zIndex:parseInt(this.$window.css("z-index"))+10})}e.show(),t.removeClass("hidden")},showButton:function(t,i){var e=this.$window.find(i?".header .tools:not(.left)":".header .tools.left"),n=e.find("#id-plugindlg-"+t);if(n.length<1){var o="back"===t?"btn-promote":"btn-"+Common.Utils.String.htmlEncode(t);(n=$('<div id="id-plugindlg-'+t+'" class="tool custom toolbar__icon '+o+'"></div>')).on("click",_.bind((function(){this.fireEvent("header:click",t)}),this)),e.append(n)}n.show(),e.removeClass("hidden")},hideButton:function(t){var i=this.$window.find(".header #id-plugindlg-"+t);i.length>0&&i.hide()},enablePointerEvents:function(t){this.frame&&(this.frame.style.pointerEvents=t?"":"none")},textLoading:"Loading",textDock:"Pin plugin"},Common.Views.PluginDlg||{}))})),void 0===Common)Common={};if(define("common/main/lib/view/CopyWarningDialog",[],(function(){"use strict";Common.Views.CopyWarningDialog=Common.UI.Window.extend(_.extend({options:{width:500,cls:"modal-dlg copy-warning",buttons:["ok"]},initialize:function(t){_.extend(this.options,{title:this.textTitle,buttons:["ok"]},t||{}),this.template=['<div class="box">','<p class="message">'+this.textMsg+"</p>",'<div class="hotkeys">',"<div>",'<p class="hotkey">'+Common.Utils.String.platformKey("Ctrl+C","{0}")+"</p>",'<p class="message">'+this.textToCopy+"</p>","</div>","<div>",'<p class="hotkey">'+Common.Utils.String.platformKey("Ctrl+X","{0}")+"</p>",'<p class="message">'+this.textToCut+"</p>","</div>","<div>",'<p class="hotkey">'+Common.Utils.String.platformKey("Ctrl+V","{0}")+"</p>",'<p class="message">'+this.textToPaste+"</p>","</div>","</div>",'<div id="copy-warning-checkbox" class="text-align-left" style="padding: 15px 0;"></div>',"</div>",'<div class="separator horizontal"></div>'].join(""),this.options.tpl=_.template(this.template)(this.options),Common.UI.Window.prototype.initialize.call(this,this.options)},render:function(){Common.UI.Window.prototype.render.call(this),this.chDontShow=new Common.UI.CheckBox({el:$("#copy-warning-checkbox"),labelText:this.textDontShow}),this.getChild().find(".dlg-btn").on("click",_.bind(this.onBtnClick,this))},getFocusedComponents:function(){return[this.chDontShow].concat(this.getFooterButtons())},getDefaultFocusableComponent:function(){return this.chDontShow},onBtnClick:function(t){this.options.handler&&this.options.handler.call(this,"checked"==this.chDontShow.getValue()),this.close()},onKeyPress:function(t){t.keyCode==Common.UI.Keys.RETURN&&(this.options.handler&&this.options.handler.call(this,"checked"==this.chDontShow.getValue()),this.close())},getSettings:function(){return"checked"==this.chDontShow.getValue()},textTitle:"Copy, Cut and Paste Actions",textMsg:"Copy, cut and paste actions using the editor toolbar buttons and context menu actions will be performed within this editor tab only.<br><br>To copy or paste to or from applications outside the editor tab use the following keyboard combinations:",textToCopy:"for Copy",textToPaste:"for Paste",textToCut:"for Cut",textDontShow:"Don't show this message again"},Common.Views.CopyWarningDialog||{}))})),define("common/main/lib/view/TextInputDialog",[],(function(){"use strict";Common.Views.TextInputDialog=Common.UI.Window.extend(_.extend({initialize:function(t){var i={};_.extend(i,{header:!!t.title,label:t.label||"",description:t.description||"",width:330,cls:"modal-dlg",buttons:["ok","cancel"]},t||{}),this.template=['<div class="box">','<div class="input-row <% if (!label) { %> hidden <% } %>">',"<label><%= label %></label>","</div>",'<div id="id-dlg-label-custom-input" class="input-row"></div>','<div class="input-row <% if (!description) { %> hidden <% } %>">','<label class="light"><%= description %></label>',"</div>","</div>"].join(""),this.inputConfig=_.extend({allowBlank:!0},t.inputConfig||{}),this.inputFixedConfig=t.inputFixedConfig,i.tpl=_.template(this.template)(i),Common.UI.Window.prototype.initialize.call(this,i)},render:function(){Common.UI.Window.prototype.render.call(this);var t=this;t.inputLabel=this.inputFixedConfig?new Common.UI.InputFieldFixed({el:$("#id-dlg-label-custom-input"),allowBlank:t.inputConfig.allowBlank,blankError:t.inputConfig.blankError,maxLength:t.inputFixedConfig.fixedValue&&t.inputConfig.maxLength?t.inputConfig.maxLength-t.inputFixedConfig.fixedValue.length:t.inputConfig.maxLength,style:"width: 100%;",validateOnBlur:!1,validation:t.inputConfig.validation,cls:"text-align-left",fixedValue:t.inputFixedConfig.fixedValue,fixedCls:"light",fixedWidth:t.inputFixedConfig.fixedWidth}):new Common.UI.InputField({el:$("#id-dlg-label-custom-input"),allowBlank:t.inputConfig.allowBlank,blankError:t.inputConfig.blankError,maxLength:t.inputConfig.maxLength,style:"width: 100%;",validateOnBlur:!1,validation:t.inputConfig.validation}),t.inputLabel.cmpEl.on("focus","input.fixed-text",(function(){setTimeout((function(){t.inputLabel._input&&t.inputLabel._input.focus()}),1)})),t.inputLabel.setValue(t.options.value||""),this.getChild().find(".dlg-btn").on("click",_.bind(this.onBtnClick,this))},getFocusedComponents:function(){return[{cmp:this.inputLabel,selector:"input:not(.fixed-text)"}].concat(this.getFooterButtons())},getDefaultFocusableComponent:function(){return this.inputLabel},show:function(){Common.UI.Window.prototype.show.apply(this,arguments);var t=this;_.delay((function(){t.getChild("input").focus()}),50)},onPrimary:function(t){return this._handleInput("ok"),!1},onBtnClick:function(t){this._handleInput(t.currentTarget.attributes.result.value)},_handleInput:function(t){if(this.options.handler){if("ok"==t&&!0!==this.inputLabel.checkValidate())return void this.inputLabel.cmpEl.find("input").focus();this.options.handler.call(this,t,this.inputLabel.getValue())}this.close()}},Common.Views.TextInputDialog||{})),Common.Views.ImageFromUrlDialog=Common.Views.TextInputDialog.extend(_.extend({initialize:function(t){var i={},e=this;_.extend(i,{header:!1,label:t.label||e.textUrl,inputConfig:{allowBlank:!1,blankError:e.txtEmpty,validation:function(t){return!!/((^https?)|(^ftp)):\/\/.+/i.test(t)||e.txtNotUrl}}},t||{}),Common.Views.TextInputDialog.prototype.initialize.call(this,i)},textUrl:"Paste an image URL:",txtEmpty:"This field is required",txtNotUrl:'This field should be a URL in the format "http://www.example.com"'},Common.Views.ImageFromUrlDialog||{}))})),define("common/main/lib/view/SelectFileDlg",[],(function(){"use strict";Common.Views.SelectFileDlg=Common.UI.Window.extend(_.extend({initialize:function(t){var i={};_.extend(i,{title:this.textTitle,width:1024,header:!0},t),this.template=['<div id="id-select-file-placeholder"></div>'].join(""),i.tpl=_.template(this.template)(i),this.fileChoiceUrl=t.fileChoiceUrl||"",Common.UI.Window.prototype.initialize.call(this,i)},render:function(){Common.UI.Window.prototype.render.call(this),this.$window.find("> .body").css({height:"auto",overflow:"hidden"});var t=document.createElement("iframe");t.width="100%",t.height=585,t.align="top",t.frameBorder=0,t.scrolling="no",t.onload=_.bind(this._onLoad,this),$("#id-select-file-placeholder").append(t),this.loadMask=new Common.UI.LoadMask({owner:$("#id-select-file-placeholder")}),this.loadMask.setTitle(this.textLoading),this.loadMask.show(),t.src=this.fileChoiceUrl;var i=this;this._eventfunc=function(t){i._onWindowMessage(t)},this._bindWindowEvents.call(this),this.on("close",(function(t){i._unbindWindowEvents()}))},_bindWindowEvents:function(){window.addEventListener?window.addEventListener("message",this._eventfunc,!1):window.attachEvent&&window.attachEvent("onmessage",this._eventfunc)},_unbindWindowEvents:function(){window.removeEventListener?window.removeEventListener("message",this._eventfunc):window.detachEvent&&window.detachEvent("onmessage",this._eventfunc)},_onWindowMessage:function(t){if(t&&window.JSON)try{this._onMessage.call(this,window.JSON.parse(t.data))}catch(t){}},_onMessage:function(t){if(t&&"onlyoffice"==t.Referer&&void 0!==t.file){Common.NotificationCenter.trigger("window:close",this);var i=this;setTimeout((function(){_.isEmpty(t.file)||i.trigger("selectfile",i,t.file)}),50)}},_onLoad:function(){this.loadMask&&this.loadMask.hide()},textTitle:"Select Data Source",textLoading:"Loading"},Common.Views.SelectFileDlg||{}))})),define("common/main/lib/view/SaveAsDlg",[],(function(){"use strict";Common.Views.SaveAsDlg=Common.UI.Window.extend(_.extend({initialize:function(t){var i={};_.extend(i,{title:this.textTitle,width:420,header:!0},t),this.template=['<div id="id-saveas-folder-placeholder"></div>'].join(""),i.tpl=_.template(this.template)(i),this.saveFolderUrl=t.saveFolderUrl||"",this.saveFileUrl=t.saveFileUrl||"",this.defFileName=t.defFileName||"",this.saveFolderUrl=this.saveFolderUrl.replace("{title}",encodeURIComponent(this.defFileName)).replace("{fileuri}",encodeURIComponent(this.saveFileUrl)),Common.UI.Window.prototype.initialize.call(this,i)},render:function(){Common.UI.Window.prototype.render.call(this),this.$window.find("> .body").css({height:"auto",overflow:"hidden"});var t=document.createElement("iframe");t.width="100%",t.height=645,t.align="top",t.frameBorder=0,t.scrolling="no",t.onload=_.bind(this._onLoad,this),$("#id-saveas-folder-placeholder").append(t),this.loadMask=new Common.UI.LoadMask({owner:$("#id-saveas-folder-placeholder")}),this.loadMask.setTitle(this.textLoading),this.loadMask.show(),t.src=this.saveFolderUrl;var i=this;this._eventfunc=function(t){i._onWindowMessage(t)},this._bindWindowEvents.call(this),this.on("close",(function(t){i._unbindWindowEvents()}))},_bindWindowEvents:function(){window.addEventListener?window.addEventListener("message",this._eventfunc,!1):window.attachEvent&&window.attachEvent("onmessage",this._eventfunc)},_unbindWindowEvents:function(){window.removeEventListener?window.removeEventListener("message",this._eventfunc):window.detachEvent&&window.detachEvent("onmessage",this._eventfunc)},_onWindowMessage:function(t){if(t&&window.JSON)try{this._onMessage.call(this,window.JSON.parse(t.data))}catch(t){}},_onMessage:function(t){t&&"onlyoffice"==t.Referer&&(_.isEmpty(t.error)?_.isEmpty(t.message)||Common.NotificationCenter.trigger("showmessage",{msg:t.message}):this.trigger("saveaserror",this,t.error),Common.NotificationCenter.trigger("window:close",this))},_onLoad:function(){this.loadMask&&this.loadMask.hide()},textTitle:"Folder for save",textLoading:"Loading"},Common.Views.SaveAsDlg||{}))})),void 0===Common)Common={};if(define("common/main/lib/view/SignDialog",[],(function(){"use strict";Common.Views.SignDialog=Common.UI.Window.extend(_.extend({options:{width:370,style:"min-width: 350px;",cls:"modal-dlg",buttons:["ok","cancel"]},initialize:function(t){_.extend(this.options,{title:this.textTitle},t||{}),this.api=this.options.api,this.signType=this.options.signType||"invisible",this.signSize=this.options.signSize||{width:0,height:0},this.certificateId=null,this.signObject=null,this.fontStore=this.options.fontStore,this.font={size:11,name:"Arial",bold:!1,italic:!1};var i=Common.localStorage.getKeysFilter();this.appPrefix=i&&i.length?i.split(",")[0]:"",this.template=['<div class="box" style="height: '+("invisible"==this.signType?"132px;":"300px;")+'">','<div id="id-dlg-sign-invisible">','<div class="input-row">',"<label>"+this.textPurpose+"</label>","</div>",'<div id="id-dlg-sign-purpose" class="input-row"></div>',"</div>",'<div id="id-dlg-sign-visible">','<div class="input-row">',"<label>"+this.textInputName+"</label>","</div>",'<div id="id-dlg-sign-name" class="input-row" style="margin-bottom: 5px;"></div>','<div id="id-dlg-sign-fonts" class="input-row" style="display: inline-block;"></div>','<div id="id-dlg-sign-font-size" class="input-row margin-left-3" style="display: inline-block;"></div>','<div id="id-dlg-sign-bold" class="margin-left-3" style="display: inline-block;"></div>','<div id="id-dlg-sign-italic" class="margin-left-3" style="display: inline-block;"></div>','<div style="margin: 10px 0 5px 0;">',"<label>"+this.textUseImage+"</label>","</div>",'<button id="id-dlg-sign-image" class="btn btn-text-default auto">'+this.textSelectImage+"</button>",'<div class="input-row" style="margin-top: 10px;">','<label class="font-weight-bold">'+this.textSignature+"</label>","</div>",'<div style="border: 1px solid #cbcbcb;"><div id="signature-preview-img" style="width: 100%; height: 50px;position: relative;"></div></div>',"</div>",'<table style="margin-top: 30px;">',"<tr>",'<td><label class="font-weight-bold" style="margin-bottom: 3px;">'+this.textCertificate+'</label></td><td rowspan="2" class="padding-left-20" style="vertical-align: top;"><button id="id-dlg-sign-change" class="btn btn-text-default float-right">'+this.textSelect+"</button></td>","</tr>",'<tr><td><div id="id-dlg-sign-certificate" class="hidden" style="max-width: 240px;overflow: hidden;white-space: nowrap;"></td></tr>',"</table>","</div>"].join(""),this.templateCertificate=_.template(['<label style="display: block;margin-bottom: 3px;overflow: hidden;text-overflow: ellipsis;"><%= Common.Utils.String.htmlEncode(name) %></label>','<label style="display: block;"><%= Common.Utils.String.htmlEncode(valid) %></label>'].join("")),this.options.tpl=_.template(this.template)(this.options),Common.UI.Window.prototype.initialize.call(this,this.options)},render:function(){Common.UI.Window.prototype.render.call(this);var t=this,i=this.getChild();t.inputPurpose=new Common.UI.InputField({el:$("#id-dlg-sign-purpose"),style:"width: 100%;"}),t.inputName=new Common.UI.InputField({el:$("#id-dlg-sign-name"),style:"width: 100%;",validateOnChange:!0}).on("changing",_.bind(t.onChangeName,t)),t.cmbFonts=new Common.UI.ComboBoxFonts({el:$("#id-dlg-sign-fonts"),cls:"input-group-nr",style:"width: 234px;",menuCls:"scrollable-menu",menuStyle:"min-width: 234px;max-height: 270px;",store:new Common.Collections.Fonts,recent:0,takeFocusOnClose:!0,hint:t.tipFontName}).on("selected",(function(i,e){t.signObject&&t.signObject.setText(t.inputName.getValue(),e.name,t.font.size,t.font.italic,t.font.bold),t.font.name=e.name})),this.cmbFontSize=new Common.UI.ComboBox({el:$("#id-dlg-sign-font-size"),cls:"input-group-nr",style:"width: 55px;",menuCls:"scrollable-menu",menuStyle:"min-width: 55px;max-height: 270px;",hint:this.tipFontSize,takeFocusOnClose:!0,data:[{value:8,displayValue:"8"},{value:9,displayValue:"9"},{value:10,displayValue:"10"},{value:11,displayValue:"11"},{value:12,displayValue:"12"},{value:14,displayValue:"14"},{value:16,displayValue:"16"},{value:18,displayValue:"18"},{value:20,displayValue:"20"},{value:22,displayValue:"22"},{value:24,displayValue:"24"},{value:26,displayValue:"26"},{value:28,displayValue:"28"},{value:36,displayValue:"36"},{value:48,displayValue:"48"},{value:72,displayValue:"72"},{value:96,displayValue:"96"}]}).on("selected",(function(i,e){t.signObject&&t.signObject.setText(t.inputName.getValue(),t.font.name,e.value,t.font.italic,t.font.bold),t.font.size=e.value})),this.cmbFontSize.setValue(this.font.size),this.cmbFontSize.on("changed:before",_.bind(this.onFontSizeChanged,this,!0)),this.cmbFontSize.on("changed:after",_.bind(this.onFontSizeChanged,this,!1)),t.btnBold=new Common.UI.Button({parentEl:$("#id-dlg-sign-bold"),cls:"btn-toolbar",iconCls:"toolbar__icon btn-bold",enableToggle:!0,hint:t.textBold}),t.btnBold.on("click",(function(i,e){t.signObject&&t.signObject.setText(t.inputName.getValue(),t.font.name,t.font.size,t.font.italic,i.pressed),t.font.bold=i.pressed})),t.btnItalic=new Common.UI.Button({parentEl:$("#id-dlg-sign-italic"),cls:"btn-toolbar",iconCls:"toolbar__icon btn-italic",enableToggle:!0,hint:t.textItalic}),t.btnItalic.on("click",(function(i,e){t.signObject&&t.signObject.setText(t.inputName.getValue(),t.font.name,t.font.size,i.pressed,t.font.bold),t.font.italic=i.pressed})),t.btnSelectImage=new Common.UI.Button({el:"#id-dlg-sign-image"}),t.btnSelectImage.on("click",_.bind(t.onSelectImage,t)),t.btnChangeCertificate=new Common.UI.Button({el:"#id-dlg-sign-change"}),t.btnChangeCertificate.on("click",_.bind(t.onChangeCertificate,t)),t.btnOk=_.find(this.getFooterButtons(),(function(t){return t.$el&&t.$el.find(".primary").addBack().filter(".primary").length>0}))||new Common.UI.Button({el:i.find(".primary")}),t.btnOk.setDisabled(!0),t.cntCertificate=$("#id-dlg-sign-certificate"),t.cntVisibleSign=$("#id-dlg-sign-visible"),t.cntInvisibleSign=$("#id-dlg-sign-invisible"),"visible"==t.signType?t.cntInvisibleSign.addClass("hidden"):t.cntVisibleSign.addClass("hidden"),i.find(".dlg-btn").on("click",_.bind(t.onBtnClick,t)),t.afterRender()},getFocusedComponents:function(){return[this.inputPurpose,this.inputName,this.cmbFonts,this.cmbFontSize,this.btnBold,this.btnItalic,this.btnSelectImage,this.btnChangeCertificate].concat(this.getFooterButtons())},show:function(){Common.UI.Window.prototype.show.apply(this,arguments);var t=this;_.delay((function(){("visible"==t.signType?t.inputName:t.inputPurpose).cmpEl.find("input").focus()}),500)},close:function(){this.api.asc_unregisterCallback("on_signature_defaultcertificate_ret",this.binding.certificateChanged),this.api.asc_unregisterCallback("on_signature_selectsertificate_ret",this.binding.certificateChanged),Common.UI.Window.prototype.close.apply(this,arguments),this.signObject&&this.signObject.destroy()},afterRender:function(){this.api&&(this.binding||(this.binding={}),this.binding.certificateChanged=_.bind(this.onCertificateChanged,this),this.api.asc_registerCallback("on_signature_defaultcertificate_ret",this.binding.certificateChanged),this.api.asc_registerCallback("on_signature_selectsertificate_ret",this.binding.certificateChanged),this.api.asc_GetDefaultCertificate()),"visible"==this.signType&&(this.cmbFonts.fillFonts(this.fontStore),this.cmbFonts.selectRecord(this.fontStore.findWhere({name:this.font.name})||this.fontStore.at(0)),this.signObject=new AscCommon.CSignatureDrawer("signature-preview-img",this.api,this.signSize.width,this.signSize.height))},getSettings:function(){var t={};return t.certificateId=this.certificateId,"invisible"==this.signType?t.purpose=this.inputPurpose.getValue():t.images=this.signObject?this.signObject.getImages():[null,null],t},onBtnClick:function(t){this._handleInput(t.currentTarget.attributes.result.value)},onPrimary:function(t){return this._handleInput("ok"),!1},_handleInput:function(t){if(this.options.handler){if("ok"==t&&(this.btnOk.isDisabled()||this.signObject&&!this.signObject.isValid()))return void(this.btnOk.isDisabled()||(this.inputName.showError([this.textNameError]),this.inputName.focus()));this.options.handler.call(this,this,t)}this.close()},onChangeCertificate:function(){this.api.asc_SelectCertificate()},onCertificateChanged:function(t){this.certificateId=t.id;var i=t.date,e="string"==typeof i?i.split(" - "):["",""];this.cntCertificate.html(this.templateCertificate({name:t.name,valid:this.textValid.replace("%1",e[0]).replace("%2",e[1])})),this.cntCertificate.toggleClass("hidden",_.isEmpty(this.certificateId)||this.certificateId<0),this.btnChangeCertificate.setCaption(_.isEmpty(this.certificateId)||this.certificateId<0?this.textSelect:this.textChange),this.btnOk.setDisabled(_.isEmpty(this.certificateId)||this.certificateId<0)},onSelectImage:function(){this.signObject&&(this.signObject.selectImage(),this.inputName.setValue(""))},onChangeName:function(t,i){this.signObject&&this.signObject.setText(i,this.font.name,this.font.size,this.font.italic,this.font.bold)},onFontSizeChanged:function(t,i,e,n){var o;if(t){if(!i.store.findWhere({displayValue:e.value})&&!(o=/^\+?(\d*(\.|,)?\d+)$|^\+?(\d+(\.|,)?\d*)$/.exec(e.value)))return o=i.getValue(),i.setRawValue(o),n.preventDefault(),!1}else{var s="sse-"==this.appPrefix?409:300;o=(o=Common.Utils.String.parseFloat(e.value))>s?s:o<1?1:Math.floor(2*(o+.4))/2,i.setRawValue(o),this.signObject&&this.signObject.setText(this.inputName.getValue(),this.font.name,o,this.font.italic,this.font.bold),this.font.size=o}},textTitle:"Sign Document",textPurpose:"Purpose for signing this document",textCertificate:"Certificate",textValid:"Valid from %1 to %2",textChange:"Change",textInputName:"Input signer name",textUseImage:"or click 'Select Image' to use a picture as signature",textSelectImage:"Select Image",textSignature:"Signature looks as",tipFontName:"Font Name",tipFontSize:"Font Size",textBold:"Bold",textItalic:"Italic",textSelect:"Select",textNameError:"Signer name must not be empty."},Common.Views.SignDialog||{}))})),void 0===Common)Common={};define("common/main/lib/view/SignSettingsDialog",[],(function(){"use strict";Common.Views.SignSettingsDialog=Common.UI.Window.extend(_.extend({options:{width:350,style:"min-width: 350px;",cls:"modal-dlg",type:"edit"},initialize:function(t){_.extend(this.options,{title:this.textTitle,buttons:["ok"].concat("edit"===(t.type||this.options.type)?["cancel"]:[])},t||{}),this.template=['<div class="box" style="height: 250px;">','<div class="input-row">',"<label>"+this.textInfoName+"</label>","</div>",'<div id="id-dlg-sign-settings-name" class="input-row" style="margin-bottom: 5px;"></div>','<div class="input-row">',"<label>"+this.textInfoTitle+"</label>","</div>",'<div id="id-dlg-sign-settings-title" class="input-row" style="margin-bottom: 5px;"></div>','<div class="input-row">',"<label>"+this.textInfoEmail+"</label>","</div>",'<div id="id-dlg-sign-settings-email" class="input-row" style="margin-bottom: 10px;"></div>','<div class="input-row">',"<label>"+this.textInstructions+"</label>","</div>",'<div id="id-dlg-sign-settings-instructions"></div>','<div id="id-dlg-sign-settings-date"></div>',"</div>"].join(""),this.api=this.options.api,this.type=this.options.type||"edit",this.options.tpl=_.template(this.template)(this.options),Common.UI.Window.prototype.initialize.call(this,this.options)},render:function(){Common.UI.Window.prototype.render.call(this);var t=this,i=this.getChild();t.inputName=new Common.UI.InputField({el:$("#id-dlg-sign-settings-name"),style:"width: 100%;",disabled:"view"==this.type}),t.inputTitle=new Common.UI.InputField({el:$("#id-dlg-sign-settings-title"),style:"width: 100%;",disabled:"view"==this.type}),t.inputEmail=new Common.UI.InputField({el:$("#id-dlg-sign-settings-email"),style:"width: 100%;",disabled:"view"==this.type}),t.textareaInstructions=new Common.UI.TextareaField({el:i.find("#id-dlg-sign-settings-instructions"),style:"width: 100%; height: 35px;margin-bottom: 10px;",value:this.textDefInstruction,disabled:"view"==this.type}),this.chDate=new Common.UI.CheckBox({el:$("#id-dlg-sign-settings-date"),labelText:this.textShowDate,disabled:"view"==this.type,value:"checked"}),i.find(".dlg-btn").on("click",_.bind(this.onBtnClick,this))},getFocusedComponents:function(){return[this.inputName,this.inputTitle,this.inputEmail,this.textareaInstructions,this.chDate].concat(this.getFooterButtons())},getDefaultFocusableComponent:function(){return this.inputName},setSettings:function(t){if(t){var i=this,e=t.asc_getSigner1();i.inputName.setValue(e||""),e=t.asc_getSigner2(),i.inputTitle.setValue(e||""),e=t.asc_getEmail(),i.inputEmail.setValue(e||""),e=t.asc_getInstructions(),i.textareaInstructions.setValue(e||""),i.chDate.setValue(t.asc_getShowDate()),i._currentGuid=t.asc_getGuid()}},getSettings:function(){var t=this,i=new AscCommon.asc_CSignatureLine;return i.asc_setSigner1(t.inputName.getValue()),i.asc_setSigner2(t.inputTitle.getValue()),i.asc_setEmail(t.inputEmail.getValue()),i.asc_setInstructions(t.textareaInstructions.getValue()),i.asc_setShowDate("checked"==t.chDate.getValue()),void 0!==t._currentGuid&&i.asc_setGuid(t._currentGuid),i},onBtnClick:function(t){this._handleInput(t.currentTarget.attributes.result.value)},onPrimary:function(t){return this._handleInput("ok"),!1},_handleInput:function(t){this.options.handler&&this.options.handler.call(this,this,t),this.close()},textInfo:"Signer Info",textInfoName:"Suggested signer",textInfoTitle:"Suggested signer's title",textInfoEmail:"Suggested signer's e-mail",textInstructions:"Instructions for signer",txtEmpty:"This field is required",textAllowComment:"Allow signer to add comment in the signature dialog",textShowDate:"Show sign date in signature line",textTitle:"Signature Setup",textDefInstruction:"Before signing this document, verify that the content you are signing is correct."},Common.Views.SignSettingsDialog||{}))})),require(["common/main/lib/view/PluginDlg","common/main/lib/view/CopyWarningDialog","common/main/lib/view/TextInputDialog","common/main/lib/view/SelectFileDlg","common/main/lib/view/SaveAsDlg","common/main/lib/view/SignDialog","common/main/lib/view/SignSettingsDialog"],(function(){Common.NotificationCenter.trigger("app-pack:loaded")})),define("../apps/documenteditor/forms/app_pack.js",(function(){}));