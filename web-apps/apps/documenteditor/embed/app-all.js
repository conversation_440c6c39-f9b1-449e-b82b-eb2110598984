/*!
 * Copyright (C) Ascensio System SIA 2012-2025. All rights reserved
 *
 * https://www.onlyoffice.com/ 
 *
 * Version: 0.0.0 (build:0)
 */

if(void 0===Common)var Common={};if(Common.Locale=new function(){"use strict";var l10n=null,loadcallback,apply=!1,defLang="{{DEFAULT_LANG}}",currentLang=defLang,_4letterLangs=["pt-pt","zh-tw","sr-cyrl"],_applyLocalization=function(t){_clearRtl();try{if(t&&(loadcallback=t),l10n){for(var e in l10n){var o=e.split(".");if(o&&o.length>2){for(var n=window,i=0;i<o.length-1;++i)void 0===n[o[i]]&&(n[o[i]]=new Object),n=n[o[i]];n&&(n[o[o.length-1]]=l10n[e])}}loadcallback&&loadcallback()}else apply=!0}catch(t){}},_get=function(prop,scope){var res="";return l10n&&scope&&scope.name&&(res=l10n[scope.name+"."+prop],!res&&scope.default&&(res=scope.default)),res||(scope?eval(scope.name).prototype[prop]:"")},_getCurrentLanguage=function(){return currentLang},_getDefaultLanguage=function(){return defLang},_getLoadedLanguage=function(){return loadedLang},_getUrlParameterByName=function(t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var e=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(location.search);return null==e?"":decodeURIComponent(e[1].replace(/\+/g," "))},_requireLang=function(t){"string"!=typeof t&&(t=null);var e=(t||_getUrlParameterByName("lang")||defLang).toLowerCase().split(/[\-_]/);e=e[0]+(e.length>1?"-"+e[1]:"");var o=_4letterLangs.indexOf(e);e=o<0?e.split(/[\-]/)[0]:_4letterLangs[o],currentLang=e,fetch("locale/"+e+".json?"+window.CP_urlArgs).then((function(t){if(!t.ok){if(o>=0)throw new Error("4letters error");if(currentLang=defLang,e!=defLang)return fetch("locale/"+defLang+".json");throw new Error("server error")}return t.json()})).then((function(t){if(t.json){if(!t.ok)throw new Error("server error");return t.json()}throw l10n=t,new Error("loaded")})).then((function(t){l10n=t||{},apply&&_applyLocalization()})).catch((function(t){return/4letters/.test(t)?setTimeout((function(){_requireLang(e.split(/[\-_]/)[0])}),0):!/loaded/.test(t)&&currentLang!=defLang&&defLang&&defLang.length<3?setTimeout((function(){_requireLang(defLang)}),0):(l10n=l10n||{},apply&&_applyLocalization(),void("loaded"==t.message||(currentLang=null,console.log("fetch error: "+t))))}))},_clearRtl=function(){!_isCurrentRtl()&&document.body.classList.contains("rtl")&&(document.body.removeAttribute("dir"),document.body.classList.remove("rtl"),document.body.classList.remove("rtl-font"),document.body.setAttribute("applang",currentLang),window.isrtl=!1)};if(window.fetch)_requireLang();else{var polyfills=["../vendor/fetch/fetch.umd"];window.Promise?require(polyfills,_requireLang):require(["../vendor/es6-promise/es6-promise.auto.min"],(function(){require(polyfills,_requireLang)}))}const _isCurrentRtl=function(){return currentLang&&/^(ar|he)$/i.test(currentLang)};return{apply:_applyLocalization,get:_get,getCurrentLanguage:_getCurrentLanguage,isCurrentLanguageRtl:_isCurrentRtl,getDefaultLanguage:_getDefaultLanguage}},void 0===window.Common&&(window.Common={}),Common.Gateway=new function(){var t=this,e=$(t),o={init:function(t){e.trigger("init",t)},openDocument:function(t){e.trigger("opendocument",t)},openDocumentFromBinary:function(t){e.trigger("opendocumentfrombinary",t)},showMessage:function(t){e.trigger("showmessage",t)},applyEditRights:function(t){e.trigger("applyeditrights",t)},processSaveResult:function(t){e.trigger("processsaveresult",t)},processRightsChange:function(t){e.trigger("processrightschange",t)},refreshHistory:function(t){e.trigger("refreshhistory",t)},setHistoryData:function(t){e.trigger("sethistorydata",t)},setEmailAddresses:function(t){e.trigger("setemailaddresses",t)},setActionLink:function(t){e.trigger("setactionlink",t.url)},processMailMerge:function(t){e.trigger("processmailmerge",t)},downloadAs:function(t){e.trigger("downloadas",t)},processMouse:function(t){e.trigger("processmouse",t)},internalCommand:function(t){e.trigger("internalcommand",t)},resetFocus:function(t){e.trigger("resetfocus",t)},setUsers:function(t){e.trigger("setusers",t)},showSharingSettings:function(t){e.trigger("showsharingsettings",t)},setSharingSettings:function(t){e.trigger("setsharingsettings",t)},insertImage:function(t){e.trigger("insertimage",t)},setMailMergeRecipients:function(t){e.trigger("setmailmergerecipients",t)},setRevisedFile:function(t){e.trigger("setrevisedfile",t)},setFavorite:function(t){e.trigger("setfavorite",t)},requestClose:function(t){e.trigger("requestclose",t)},blurFocus:function(t){e.trigger("blurfocus",t)},grabFocus:function(t){e.trigger("grabfocus",t)},setReferenceData:function(t){e.trigger("setreferencedata",t)},refreshFile:function(t){e.trigger("refreshfile",t)},setRequestedDocument:function(t){e.trigger("setrequesteddocument",t)},setRequestedSpreadsheet:function(t){e.trigger("setrequestedspreadsheet",t)},setReferenceSource:function(t){e.trigger("setreferencesource",t)},startFilling:function(t){e.trigger("startfilling",t)},requestRoles:function(t){e.trigger("requestroles",t)},cryptPadMessageToOO:function(t){e.trigger("cryptPadMessageToOO",t)}},n=function(t,e){window.parent&&window.JSON&&(t.frameEditorId=window.frameEditorId,e?window.parent.postMessage(t,"*",[e]):window.parent.postMessage(window.JSON.stringify(t),"*"))},i=function(t){!function(t){if(t.origin===window.parentOrigin||t.origin===window.location.origin||"null"===t.origin&&("file://"===window.parentOrigin||"file://"===window.location.origin)){var e=t.data;if(e&&"openDocumentFromBinary"===e.command)(i=o[e.command])&&i.call(this,e.data);else if("[object String]"===Object.prototype.toString.apply(e)&&window.JSON){var n,i;try{n=window.JSON.parse(e)}catch(t){n=""}n&&(i=o[n.command])&&i.call(this,n.data)}}}(t)};return window.attachEvent?window.attachEvent("onmessage",i):window.addEventListener("message",i,!1),{appReady:function(){n({event:"onAppReady"})},requestEditRights:function(){n({event:"onRequestEditRights"})},requestHistory:function(){n({event:"onRequestHistory"})},requestHistoryData:function(t){n({event:"onRequestHistoryData",data:t})},requestRestore:function(t,e,o){n({event:"onRequestRestore",data:{version:t,url:e,fileType:o}})},requestEmailAddresses:function(){n({event:"onRequestEmailAddresses"})},requestStartMailMerge:function(){n({event:"onRequestStartMailMerge"})},requestHistoryClose:function(t){n({event:"onRequestHistoryClose"})},reportError:function(t,e){n({event:"onError",data:{errorCode:t,errorDescription:e}})},reportWarning:function(t,e){n({event:"onWarning",data:{warningCode:t,warningDescription:e}})},sendInfo:function(t){n({event:"onInfo",data:t})},setDocumentModified:function(t){n({event:"onDocumentStateChange",data:t})},internalMessage:function(t,e){n({event:"onInternalMessage",data:{type:t,data:e}})},updateVersion:function(){n({event:"onOutdatedVersion"})},downloadAs:function(t,e){n({event:"onDownloadAs",data:{url:t,fileType:e}})},requestSaveAs:function(t,e,o){n({event:"onRequestSaveAs",data:{url:t,title:e,fileType:o}})},collaborativeChanges:function(){n({event:"onCollaborativeChanges"})},requestRename:function(t){n({event:"onRequestRename",data:t})},metaChange:function(t){n({event:"onMetaChange",data:t})},documentReady:function(){n({event:"onDocumentReady"})},requestClose:function(){n({event:"onRequestClose"})},requestMakeActionLink:function(t){n({event:"onMakeActionLink",data:t})},requestUsers:function(t,e){n({event:"onRequestUsers",data:{c:t,id:e}})},requestSendNotify:function(t){n({event:"onRequestSendNotify",data:t})},requestInsertImage:function(t){n({event:"onRequestInsertImage",data:{c:t}})},requestMailMergeRecipients:function(){n({event:"onRequestMailMergeRecipients"})},requestCompareFile:function(){n({event:"onRequestCompareFile"})},requestSharingSettings:function(){n({event:"onRequestSharingSettings"})},requestCreateNew:function(){n({event:"onRequestCreateNew"})},requestReferenceData:function(t){n({event:"onRequestReferenceData",data:t})},requestOpen:function(t){n({event:"onRequestOpen",data:t})},requestSelectDocument:function(t){n({event:"onRequestSelectDocument",data:{c:t}})},requestSelectSpreadsheet:function(t){n({event:"onRequestSelectSpreadsheet",data:{c:t}})},requestReferenceSource:function(){n({event:"onRequestReferenceSource"})},requestStartFilling:function(t){n({event:"onRequestStartFilling",data:t})},startFilling:function(){n({event:"onStartFilling"})},requestFillingStatus:function(t){n({event:"onRequestFillingStatus",data:t})},switchEditorType:function(t,e){n({event:"onSwitchEditorType",data:{type:t,restart:e}})},pluginsReady:function(){n({event:"onPluginsReady"})},requestRefreshFile:function(){n({event:"onRequestRefreshFile"})},userActionRequired:function(){n({event:"onUserActionRequired"})},saveDocument:function(t){t&&n({event:"onSaveDocument",data:t.buffer},t.buffer)},submitForm:function(){n({event:"onSubmit"})},cryptPadSendMessageFromOO:function(t){n({event:"cryptPadSendMessageFromOO",data:{msg:t}})},on:function(o,n){e.on(o,(function(e,o){n.call(t,o)}))}}},void 0===window.Common&&(window.Common={}),Common.component=Common.component||{},Common.Analytics=Common.component.Analytics=new function(){var t;return{initialize:function(e,o){if(void 0===e)throw"Analytics: invalid id.";if(void 0===o||"[object String]"!==Object.prototype.toString.apply(o))throw"Analytics: invalid category type.";t=o,$("head").append('<script type="text/javascript">var _gaq = _gaq || [];_gaq.push(["_setAccount", "'+e+'"]);_gaq.push(["_trackPageview"]);(function() {var ga = document.createElement("script"); ga.type = "text/javascript"; ga.async = true;ga.src = ("https:" == document.location.protocol ? "https://ssl" : "http://www") + ".google-analytics.com/ga.js";var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(ga, s);})();<\/script>')},trackEvent:function(e,o,n){if(void 0!==e&&"[object String]"!==Object.prototype.toString.apply(e))throw"Analytics: invalid action type.";if(void 0!==o&&"[object String]"!==Object.prototype.toString.apply(o))throw"Analytics: invalid label type.";if(void 0!==n&&("[object Number]"!==Object.prototype.toString.apply(n)||!isFinite(n)))throw"Analytics: invalid value type.";if("undefined"!=typeof _gaq){if("undefined"===t)throw"Analytics is not initialized.";_gaq.push(["_trackEvent",t,e,o,n])}}}},function(t){"use strict";var e='[data-toggle="dropdown"]',o=function(e){t(e).on("click.bs.dropdown",this.toggle)};function n(e){var o=e.attr("data-target");o||(o=(o=e.attr("href"))&&/#[A-Za-z]/.test(o)&&o.replace(/.*(?=#[^\s]*$)/,""));var n="#"!==o?t(document).find(o):null;return n&&n.length?n:e.parent()}function i(o){o&&3===o.which||(t(".dropdown-backdrop").remove(),t(e).each((function(){var e=t(this),i=n(e),r={relatedTarget:this};i.hasClass("open")&&(o&&"click"==o.type&&/input|textarea/i.test(o.target.tagName)&&t.contains(i[0],o.target)||(i.trigger(o=t.Event("hide.bs.dropdown",r)),o.isDefaultPrevented()||(e.attr("aria-expanded","false"),i.removeClass("open").trigger(t.Event("hidden.bs.dropdown",r)))))})))}o.VERSION="3.4.1",o.prototype.toggle=function(e){var o=t(this);if(!o.is(".disabled, :disabled")){var r=n(o),s=r.hasClass("open");if(i(),!s){var a={relatedTarget:this};if(r.trigger(e=t.Event("show.bs.dropdown",a)),e.isDefaultPrevented())return;o.trigger("focus").attr("aria-expanded","true"),r.toggleClass("open").trigger(t.Event("shown.bs.dropdown",a))}return!1}},o.prototype.keydown=function(o){if(/(38|40|27|32)/.test(o.which)&&!/input|textarea/i.test(o.target.tagName)){var i=t(this);if(o.preventDefault(),o.stopPropagation(),!i.is(".disabled, :disabled")){var r=n(i),s=r.hasClass("open");if(!s&&27!=o.which||s&&27==o.which)return 27==o.which&&r.find(e).trigger("focus"),i.trigger("click");var a=r.find(".dropdown-menu li:not(.disabled):visible a");if(a.length){var c=a.index(o.target);38==o.which&&c>0&&c--,40==o.which&&c<a.length-1&&c++,~c||(c=0),a.eq(c).trigger("focus")}}}};var r=t.fn.dropdown;t.fn.dropdown=function(e){return this.each((function(){var n=t(this),i=n.data("bs.dropdown");i||n.data("bs.dropdown",i=new o(this)),"string"==typeof e&&i[e].call(n)}))},t.fn.dropdown.Constructor=o,t.fn.dropdown.noConflict=function(){return t.fn.dropdown=r,this},t(document).on("click.bs.dropdown.data-api",i).on("click.bs.dropdown.data-api",".dropdown form",(function(t){t.stopPropagation()})).on("click.bs.dropdown.data-api",e,o.prototype.toggle).on("keydown.bs.dropdown.data-api",e,o.prototype.keydown).on("keydown.bs.dropdown.data-api",".dropdown-menu",o.prototype.keydown)}(jQuery),function(t){"use strict";var e=function(e,o){this.options=o,this.$body=t(document.body),this.$element=t(e),this.$dialog=this.$element.find(".modal-dialog"),this.$backdrop=null,this.isShown=null,this.originalBodyPad=null,this.scrollbarWidth=0,this.ignoreBackdropClick=!1,this.fixedContent=".navbar-fixed-top, .navbar-fixed-bottom",this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,t.proxy((function(){this.$element.trigger("loaded.bs.modal")}),this))};function o(o,n){return this.each((function(){var i=t(this),r=i.data("bs.modal"),s=t.extend({},e.DEFAULTS,i.data(),"object"==typeof o&&o);r||i.data("bs.modal",r=new e(this,s)),"string"==typeof o?r[o](n):s.show&&r.show(n)}))}e.VERSION="3.4.1",e.TRANSITION_DURATION=300,e.BACKDROP_TRANSITION_DURATION=150,e.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},e.prototype.toggle=function(t){return this.isShown?this.hide():this.show(t)},e.prototype.show=function(o){var n=this,i=t.Event("show.bs.modal",{relatedTarget:o});this.$element.trigger(i),this.isShown||i.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',t.proxy(this.hide,this)),this.$dialog.on("mousedown.dismiss.bs.modal",(function(){n.$element.one("mouseup.dismiss.bs.modal",(function(e){t(e.target).is(n.$element)&&(n.ignoreBackdropClick=!0)}))})),this.backdrop((function(){var i=t.support.transition&&n.$element.hasClass("fade");n.$element.parent().length||n.$element.appendTo(n.$body),n.$element.show().scrollTop(0),n.adjustDialog(),i&&n.$element[0].offsetWidth,n.$element.addClass("in"),n.enforceFocus();var r=t.Event("shown.bs.modal",{relatedTarget:o});i?n.$dialog.one("bsTransitionEnd",(function(){n.$element.trigger("focus").trigger(r)})).emulateTransitionEnd(e.TRANSITION_DURATION):n.$element.trigger("focus").trigger(r)})))},e.prototype.hide=function(o){o&&o.preventDefault(),o=t.Event("hide.bs.modal"),this.$element.trigger(o),this.isShown&&!o.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),t(document).off("focusin.bs.modal"),this.$element.removeClass("in").off("click.dismiss.bs.modal").off("mouseup.dismiss.bs.modal"),this.$dialog.off("mousedown.dismiss.bs.modal"),t.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",t.proxy(this.hideModal,this)).emulateTransitionEnd(e.TRANSITION_DURATION):this.hideModal())},e.prototype.enforceFocus=function(){t(document).off("focusin.bs.modal").on("focusin.bs.modal",t.proxy((function(t){document===t.target||this.$element[0]===t.target||this.$element.has(t.target).length||this.$element.trigger("focus")}),this))},e.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.bs.modal",t.proxy((function(t){27==t.which&&this.hide()}),this)):this.isShown||this.$element.off("keydown.dismiss.bs.modal")},e.prototype.resize=function(){this.isShown?t(window).on("resize.bs.modal",t.proxy(this.handleUpdate,this)):t(window).off("resize.bs.modal")},e.prototype.hideModal=function(){var t=this;this.$element.hide(),this.backdrop((function(){t.$body.removeClass("modal-open"),t.resetAdjustments(),t.resetScrollbar(),t.$element.trigger("hidden.bs.modal")}))},e.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},e.prototype.backdrop=function(o){var n=this,i=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var r=t.support.transition&&i;if(this.$backdrop=t(document.createElement("div")).addClass("modal-backdrop "+i).appendTo(this.$body),this.$element.on("click.dismiss.bs.modal",t.proxy((function(t){this.ignoreBackdropClick?this.ignoreBackdropClick=!1:t.target===t.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus():this.hide())}),this)),r&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!o)return;r?this.$backdrop.one("bsTransitionEnd",o).emulateTransitionEnd(e.BACKDROP_TRANSITION_DURATION):o()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var s=function(){n.removeBackdrop(),o&&o()};t.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",s).emulateTransitionEnd(e.BACKDROP_TRANSITION_DURATION):s()}else o&&o()},e.prototype.handleUpdate=function(){this.adjustDialog()},e.prototype.adjustDialog=function(){var t=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&t?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!t?this.scrollbarWidth:""})},e.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})},e.prototype.checkScrollbar=function(){var t=window.innerWidth;if(!t){var e=document.documentElement.getBoundingClientRect();t=e.right-Math.abs(e.left)}this.bodyIsOverflowing=document.body.clientWidth<t,this.scrollbarWidth=this.measureScrollbar()},e.prototype.setScrollbar=function(){var e=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||"";var o=this.scrollbarWidth;this.bodyIsOverflowing&&(this.$body.css("padding-right",e+o),t(this.fixedContent).each((function(e,n){var i=n.style.paddingRight,r=t(n).css("padding-right");t(n).data("padding-right",i).css("padding-right",parseFloat(r)+o+"px")})))},e.prototype.resetScrollbar=function(){this.$body.css("padding-right",this.originalBodyPad),t(this.fixedContent).each((function(e,o){var n=t(o).data("padding-right");t(o).removeData("padding-right"),o.style.paddingRight=n||""}))},e.prototype.measureScrollbar=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",this.$body.append(t);var e=t.offsetWidth-t.clientWidth;return this.$body[0].removeChild(t),e};var n=t.fn.modal;t.fn.modal=o,t.fn.modal.Constructor=e,t.fn.modal.noConflict=function(){return t.fn.modal=n,this},t(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',(function(e){var n=t(this),i=n.attr("href"),r=n.attr("data-target")||i&&i.replace(/.*(?=#[^\s]+$)/,""),s=t(document).find(r),a=s.data("bs.modal")?"toggle":t.extend({remote:!/#/.test(i)&&i},s.data(),n.data());n.is("a")&&e.preventDefault(),s.one("show.bs.modal",(function(t){t.isDefaultPrevented()||s.one("hidden.bs.modal",(function(){n.is(":visible")&&n.trigger("focus")}))})),o.call(s,a,this)}))}(jQuery),function(t){"use strict";var e=["sanitize","whiteList","sanitizeFn"],o=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],n={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},i=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,r=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i;function s(e,n){var s=e.nodeName.toLowerCase();if(-1!==t.inArray(s,n))return-1===t.inArray(s,o)||Boolean(e.nodeValue.match(i)||e.nodeValue.match(r));for(var a=t(n).filter((function(t,e){return e instanceof RegExp})),c=0,l=a.length;c<l;c++)if(s.match(a[c]))return!0;return!1}function a(e,o,n){if(0===e.length)return e;if(n&&"function"==typeof n)return n(e);if(!document.implementation||!document.implementation.createHTMLDocument)return e;var i=document.implementation.createHTMLDocument("sanitization");i.body.innerHTML=e;for(var r=t.map(o,(function(t,e){return e})),a=t(i.body).find("*"),c=0,l=a.length;c<l;c++){var d=a[c],u=d.nodeName.toLowerCase();if(-1!==t.inArray(u,r))for(var p=t.map(d.attributes,(function(t){return t})),m=[].concat(o["*"]||[],o[u]||[]),h=0,f=p.length;h<f;h++)s(p[h],m)||d.removeAttribute(p[h].nodeName);else d.parentNode.removeChild(d)}return i.body.innerHTML}var c=function(t,e){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.inState=null,this.init("tooltip",t,e)};c.VERSION="3.4.1",c.TRANSITION_DURATION=150,c.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0},sanitize:!0,sanitizeFn:null,whiteList:n},c.prototype.init=function(e,o,n){if(this.enabled=!0,this.type=e,this.$element=t(o),this.options=this.getOptions(n),this.$viewport=this.options.viewport&&t(document).find(t.isFunction(this.options.viewport)?this.options.viewport.call(this,this.$element):this.options.viewport.selector||this.options.viewport),this.inState={click:!1,hover:!1,focus:!1},this.$element[0]instanceof document.constructor&&!this.options.selector)throw new Error("`selector` option must be specified when initializing "+this.type+" on the window.document object!");for(var i=this.options.trigger.split(" "),r=i.length;r--;){var s=i[r];if("click"==s)this.$element.on("click."+this.type,this.options.selector,t.proxy(this.toggle,this));else if("manual"!=s){var a="hover"==s?"mouseenter":"focusin",c="hover"==s?"mouseleave":"focusout";this.$element.on(a+"."+this.type,this.options.selector,t.proxy(this.enter,this)),this.$element.on(c+"."+this.type,this.options.selector,t.proxy(this.leave,this))}}this.options.selector?this._options=t.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},c.prototype.getDefaults=function(){return c.DEFAULTS},c.prototype.getOptions=function(o){var n=this.$element.data();for(var i in n)n.hasOwnProperty(i)&&-1!==t.inArray(i,e)&&delete n[i];return(o=t.extend({},this.getDefaults(),n,o)).delay&&"number"==typeof o.delay&&(o.delay={show:o.delay,hide:o.delay}),o.sanitize&&(o.template=a(o.template,o.whiteList,o.sanitizeFn)),o},c.prototype.getDelegateOptions=function(){var e={},o=this.getDefaults();return this._options&&t.each(this._options,(function(t,n){o[t]!=n&&(e[t]=n)})),e},c.prototype.enter=function(e){var o=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);if(o||(o=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,o)),e instanceof t.Event&&(o.inState["focusin"==e.type?"focus":"hover"]=!0),o.tip().hasClass("in")||"in"==o.hoverState)o.hoverState="in";else{if(clearTimeout(o.timeout),o.hoverState="in",!o.options.delay||!o.options.delay.show)return o.show();o.timeout=setTimeout((function(){"in"==o.hoverState&&o.show()}),o.options.delay.show)}},c.prototype.isInStateTrue=function(){for(var t in this.inState)if(this.inState[t])return!0;return!1},c.prototype.leave=function(e){var o=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);if(o||(o=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,o)),e instanceof t.Event&&(o.inState["focusout"==e.type?"focus":"hover"]=!1),!o.isInStateTrue()){if(clearTimeout(o.timeout),o.hoverState="out",!o.options.delay||!o.options.delay.hide)return o.hide();o.timeout=setTimeout((function(){"out"==o.hoverState&&o.hide()}),o.options.delay.hide)}},c.prototype.show=function(){var e=t.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(e);var o=t.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]);if(e.isDefaultPrevented()||!o)return;var n=this,i=this.tip(),r=this.getUID(this.type);this.setContent(),i.attr("id",r),this.$element.attr("aria-describedby",r),this.options.animation&&i.addClass("fade");var s="function"==typeof this.options.placement?this.options.placement.call(this,i[0],this.$element[0]):this.options.placement,a=/\s?auto?\s?/i,l=a.test(s);l&&(s=s.replace(a,"")||"top"),i.detach().css({top:0,left:0,display:"block"}).addClass(s).data("bs."+this.type,this),this.options.container?i.appendTo(t(document).find(this.options.container)):i.insertAfter(this.$element),this.$element.trigger("inserted.bs."+this.type);var d=this.getPosition(),u=i[0].offsetWidth,p=i[0].offsetHeight;if(l){var m=s,h=this.getPosition(this.$viewport);s="bottom"==s&&d.bottom+p>h.bottom?"top":"top"==s&&d.top-p<h.top?"bottom":"right"==s&&d.right+u>h.width?"left":"left"==s&&d.left-u<h.left?"right":s,i.removeClass(m).addClass(s)}var f=this.getCalculatedOffset(s,d,u,p);this.applyPlacement(f,s);var g=function(){var t=n.hoverState;n.$element.trigger("shown.bs."+n.type),n.hoverState=null,"out"==t&&n.leave(n)};t.support.transition&&this.$tip.hasClass("fade")?i.one("bsTransitionEnd",g).emulateTransitionEnd(c.TRANSITION_DURATION):g()}},c.prototype.applyPlacement=function(t,e){var o=this.tip(),n=o[0].offsetWidth,i=o[0].offsetHeight,r=parseInt(o.css("margin-top"),10),s=parseInt(o.css("margin-left"),10);isNaN(r)&&(r=0),isNaN(s)&&(s=0),t.top+=r,t.left+=s,(Common.Utils||common.utils).setOffset(o,t),o.addClass("in");var a=o[0].offsetWidth,c=o[0].offsetHeight;"top"==e&&c!=i&&(t.top=t.top+i-c);var l=this.getViewportAdjustedDelta(e,t,a,c);l.left?t.left+=l.left:t.top+=l.top;var d=/top|bottom/.test(e),u=d?2*l.left-n+a:2*l.top-i+c,p=d?"offsetWidth":"offsetHeight";(Common.Utils||common.utils).setOffset(o,t),this.replaceArrow(u,o[0][p],d)},c.prototype.replaceArrow=function(t,e,o){this.arrow().css(o?"left":"top",50*(1-t/e)+"%").css(o?"top":"left","")},c.prototype.setContent=function(){var t=this.tip(),e=this.getTitle();this.options.html?(this.options.sanitize&&(e=a(e,this.options.whiteList,this.options.sanitizeFn)),t.find(".tooltip-inner").html(e)):t.find(".tooltip-inner").text(e),t.removeClass("fade in top bottom left right")},c.prototype.hide=function(e){var o=this,n=t(this.$tip),i=t.Event("hide.bs."+this.type);function r(){"in"!=o.hoverState&&n.detach(),o.$element&&o.$element.removeAttr("aria-describedby").trigger("hidden.bs."+o.type),e&&e()}if(this.$element.trigger(i),!i.isDefaultPrevented())return n.removeClass("in"),t.support.transition&&n.hasClass("fade")?n.one("bsTransitionEnd",r).emulateTransitionEnd(c.TRANSITION_DURATION):r(),this.hoverState=null,this},c.prototype.fixTitle=function(){var t=this.$element;(t.attr("title")||"string"!=typeof t.attr("data-original-title"))&&t.attr("data-original-title",t.attr("title")||"").attr("title","")},c.prototype.hasContent=function(){return this.getTitle()},c.prototype.getPosition=function(e){var o=(e=e||this.$element)[0],n="BODY"==o.tagName,i=(Common.Utils||common.utils).getBoundingClientRect(o);null==i.width&&(i=t.extend({},i,{width:i.right-i.left,height:i.bottom-i.top}));var r=window.SVGElement&&o instanceof window.SVGElement,s=n?{top:0,left:0}:r?null:(Common.Utils||common.utils).getOffset(e),a={scroll:n?document.documentElement.scrollTop||document.body.scrollTop:e.scrollTop()},c=n?{width:t(window).width(),height:t(window).height()}:null;return t.extend({},i,a,c,s)},c.prototype.getCalculatedOffset=function(t,e,o,n){return"bottom"==t?{top:e.top+e.height,left:e.left+e.width/2-o/2}:"top"==t?{top:e.top-n,left:e.left+e.width/2-o/2}:"left"==t?{top:e.top+e.height/2-n/2,left:e.left-o}:{top:e.top+e.height/2-n/2,left:e.left+e.width}},c.prototype.getViewportAdjustedDelta=function(t,e,o,n){var i={top:0,left:0};if(!this.$viewport)return i;var r=this.options.viewport&&this.options.viewport.padding||0,s=this.getPosition(this.$viewport);if(/right|left/.test(t)){var a=e.top-r-s.scroll,c=e.top+r-s.scroll+n;a<s.top?i.top=s.top-a:c>s.top+s.height&&(i.top=s.top+s.height-c)}else{var l=e.left-r,d=e.left+r+o;l<s.left?i.left=s.left-l:d>s.right&&(i.left=s.left+s.width-d)}return i},c.prototype.getTitle=function(){var t=this.$element,e=this.options;return t.attr("data-original-title")||("function"==typeof e.title?e.title.call(t[0]):e.title)},c.prototype.getUID=function(t){do{t+=~~(1e6*Math.random())}while(document.getElementById(t));return t},c.prototype.tip=function(){if(!this.$tip&&(this.$tip=t(this.options.template),1!=this.$tip.length))throw new Error(this.type+" `template` option must consist of exactly 1 top-level element!");return this.$tip},c.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},c.prototype.enable=function(){this.enabled=!0},c.prototype.disable=function(){this.enabled=!1},c.prototype.toggleEnabled=function(){this.enabled=!this.enabled},c.prototype.toggle=function(e){var o=this;e&&((o=t(e.currentTarget).data("bs."+this.type))||(o=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,o))),e?(o.inState.click=!o.inState.click,o.isInStateTrue()?o.enter(o):o.leave(o)):o.tip().hasClass("in")?o.leave(o):o.enter(o)},c.prototype.destroy=function(){var t=this;clearTimeout(this.timeout),this.hide((function(){t.$element.off("."+t.type).removeData("bs."+t.type),t.$tip&&t.$tip.detach(),t.$tip=null,t.$arrow=null,t.$viewport=null,t.$element=null}))},c.prototype.sanitizeHtml=function(t){return a(t,this.options.whiteList,this.options.sanitizeFn)};var l=t.fn.tooltip;t.fn.tooltip=function(e){return this.each((function(){var o=t(this),n=o.data("bs.tooltip"),i="object"==typeof e&&e;!n&&/destroy|hide/.test(e)||(n||o.data("bs.tooltip",n=new c(this,i)),"string"==typeof e&&n[e]())}))},t.fn.tooltip.Constructor=c,t.fn.tooltip.noConflict=function(){return t.fn.tooltip=l,this}}(jQuery),!window.common&&(window.common={}),common.localStorage=new function(){var t,e,o={};Common.Gateway.on("internalcommand",(function(t){"localstorage"==t.type&&(o=t.keys)}));var n=function(t,e,n){if(r)try{localStorage.setItem(t,e)}catch(t){}else o[t]=e,!0===n&&Common.Gateway.internalMessage("localstorage",{cmd:"set",keys:{name:e}})},i=function(t){return r?localStorage.getItem(t):void 0===o[t]?null:o[t]};try{var r=!!window.localStorage}catch(t){r=!1}return{getId:function(){return t},setId:function(e){t=e},getItem:i,getBool:function(t,e){var o=i(t);return e=e||!1,null!==o?0!=parseInt(o):e},setBool:function(t,e,o){n(t,e?1:0,o)},setItem:n,removeItem:function(t){r?localStorage.removeItem(t):delete o[t]},setKeysFilter:function(t){e=t},getKeysFilter:function(){return e},itemExists:function(t){return null!==i(t)},sync:function(){r||Common.Gateway.internalMessage("localstorage",{cmd:"get",keys:e})},save:function(){r||Common.Gateway.internalMessage("localstorage",{cmd:"set",keys:o})}}},!window.common&&(window.common={}),!common.utils&&(common.utils={}),common.utils=new function(){var t,e=navigator.userAgent.toLowerCase(),o=function(t){return t.test(e)},n=!o(/opera/)&&(o(/msie/)||o(/trident/)||o(/edge/)),i=!n&&o(/\bchrome\b/),r=(t=/\bchrome\/(\d+\.\d+)/.exec(e))?parseFloat(t[1]):0,s=o(/macintosh|mac os x/),a=1,c=function(){var t={};window.AscCommon&&window.AscCommon.checkDeviceScale&&(t=window.AscCommon.checkDeviceScale(),AscCommon.correctApplicationScale(t),t.correct&&(a=t.zoom))},l=function(){return!!(i&&128<=r)&&1!==a},d=function(t){let e=t.offset();return l()?{left:e.left*a,top:e.top*a}:e},u=function(t){let e=t.position();return l()?{left:e.left*a,top:e.top*a}:e};return n||(c(),$(window).on("resize",c)),{openLink:function(t){t&&window.parent.APP.openURL(t)},dialogPrint:function(t,e){if($("#id-print-frame").remove(),t){var o=document.createElement("iframe");o.id="id-print-frame",o.style.display="none",o.style.visibility="hidden",o.style.position="fixed",o.style.right="0",o.style.bottom="0",document.body.appendChild(o),o.onload=function(){try{o.contentWindow.focus(),o.contentWindow.print(),o.contentWindow.blur(),window.focus()}catch(t){e.asc_DownloadAs(new Asc.asc_CDownloadOptions(Asc.c_oAscFileType.PDF))}},o.src=t}},htmlEncode:function(t){return $("<div/>").text(t).html()},fillUserInfo:function(t,e,o,n){var i=t||{};return i.anonymous=!i.id,!i.id&&(i.id=n),i.fullname=i.name?i.name:o,i.group&&(i.fullname=i.group.toString()+AscCommon.UserInfoParser.getSeparator()+i.fullname),i.guest=!i.name,i},fixedDigits:function(t,e,o){void 0===o&&(o="0");for(var n="",i=t.toString(),r=i.length;r<e;r++)n+=o;return n+i},getKeyByValue:function(t,e){for(var o in t)if(t.hasOwnProperty(o)&&t[o]===e)return o},getBoundingClientRect:function(t){let e=t.getBoundingClientRect();if(!l())return e;let o=a,n={};return void 0!==e.x&&(n.x=e.x*o),void 0!==e.y&&(n.y=e.y*o),void 0!==e.width&&(n.width=e.width*o),void 0!==e.height&&(n.height=e.height*o),void 0!==e.left&&(n.left=e.left*o),void 0!==e.top&&(n.top=e.top*o),void 0!==e.right&&(n.right=e.right*o),void 0!==e.bottom&&(n.bottom=e.bottom*o),n},getOffset:d,setOffset:function(t,e){var o,n,i,r,s,a,c=t.css("position"),l={};"static"===c&&(t[0].style.position="relative"),s=d(t),i=t.css("top"),a=t.css("left"),("absolute"===c||"fixed"===c)&&(i+a).indexOf("auto")>-1?(r=(o=u(t)).top,n=o.left):(r=parseFloat(i)||0,n=parseFloat(a)||0),null!=e.top&&(l.top=e.top-s.top+r),null!=e.left&&(l.left=e.left-s.left+n),t.css(l)},getPosition:u,isMac:s,isIE:n}},!window.common&&(window.common={}),!common.view&&(common.view={}),common.view.LoadMask=function(t){var e,o,n=t||$(document.body),i="",r=0,s=!1;return{show:function(){e&&o||(e=$('<div class="asc-loadmask-body" role="presentation" tabindex="-1"><i id="loadmask-spinner" class="asc-loadmask-image"></i><div class="asc-loadmask-title"></div></div>'),o=$('<div class="asc-loadmask"></div>')),$(".asc-loadmask-title",e).html(i),s||(s=!0,r=setTimeout((function(){n.append(o),n.append(e),e.css("min-width",$(".asc-loadmask-title",e).width()+108)}),500))},hide:function(){r&&(clearTimeout(r),r=0),o&&o.remove(),e&&e.remove(),o=e=null,s=!1},setTitle:function(t){if(i=t,n&&e){var o=$(".asc-loadmask-title",e);o.html(i),e.css("min-width",o.width()+108)}}}},!window.common&&(window.common={}),!common.view&&(common.view={}),common.view.modals=new function(){var t='<div class="modal fade" tabindex="-1" role="dialog" aria-labelledby="idm-title" aria-hidden="true"><div class="modal-dialog" role="document"><div class="modal-content"><div class="modal-header"><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button><h4 id="idm-title" class="modal-title">{title}</h4></div><div class="modal-body">{body}</div><div class="modal-footer">{footer}</div></div></div></div>',e='<div class="share-link"><input id="id-short-url" class="form-control" type="text" readonly/></div><div class="share-buttons"><span class="svg big-facebook" data-name="facebook"></span><span class="svg big-twitter" data-name="twitter"></span><span class="svg big-email" data-name="email"></span><div class="autotest" id="email" style="display: none"></div></div>';return{create:function(o,n){var i;if(!n&&(n="body"),"share"==o){if(window.config&&window.config.btnsShare){let t=[];const o=Object.keys(config.btnsShare);for(var r in o)t.push('<span class="svg big-'+o[r]+'" data-name="'+o[r]+'"></span>');if(t){let o=$(e);o.find(".autotest").prevAll().remove(),o.eq(1).prepend(t.join("")),e=$("<div>").append(o).html()}}i=$(t.replace(/\{title}/,this.txtShare).replace(/\{body}/,e).replace(/\{footer}/,'<button id="btn-copyshort" type="button" class="btn">'+this.txtCopy+"</button>")).appendTo(n).attr("id","dlg-share")}else"embed"==o?i=$(t.replace(/\{title}/,this.txtEmbed).replace(/\{body}/,'<div class="size-manual"><span class="caption">{width}:</span><input id="txt-embed-width" class="form-control input-xs" type="text" value="400px"><input id="txt-embed-height" class="form-control input-xs right" type="text" value="600px"><span class="right caption">{height}:</span></div><textarea id="txt-embed-url" rows="4" class="form-control" readonly></textarea>').replace(/\{width}/,this.txtWidth).replace(/\{height}/,this.txtHeight).replace(/\{footer}/,'<button id="btn-copyembed" type="button" class="btn">'+this.txtCopy+"</button>")).appendTo(n).attr("id","dlg-embed"):"password"==o&&((i=$(t.replace(/\{title}/,this.txtTitleProtected).replace(/\{body}/,'<div class="password-body"><label>{label}</label><input id="password-input" class="form-control" type="password"/><label id="password-label-error">{error}</label>{button}</div>').replace(/\{label}/,this.txtOpenFile).replace(/\{error}/,this.txtIncorrectPwd).replace(/\{button}/,'<button id="password-btn" type="button" class="btn">OK</button>')).appendTo(n).attr("id","dlg-password")).find("button.close").remove(),i.find(".modal-footer").remove());return i},txtWidth:"Width",txtHeight:"Height",txtShare:"Share Link",txtCopy:"Copy to clipboard",txtEmbed:"Embed",txtTitleProtected:"Protected file",txtOpenFile:"Enter a password to open the file",txtIncorrectPwd:"Password is incorrect"}},!window.common&&(window.common={}),!common.controller&&(common.controller={}),common.controller.modals=new function(){var t,e,o,n,i='<iframe allowtransparency="true" frameborder="0" scrolling="no" src="{embed-url}" width="{width}" height="{height}"></iframe>';function r(t,e){t.select(),document.execCommand("copy")||window.alert("Browser's error! Use keyboard shortcut [Ctrl] + [C]")}var s=function(){t=common.view.modals.create("share");var e=encodeURIComponent(n.shareUrl),o="mailto:?subject=I have shared a document with you: "+n.docTitle+"&body=I have shared a document with you: "+e;t.find("#btn-copyshort").on("click",r.bind(this,t.find("#id-short-url"))),t.find(".share-buttons > span").on("click",(function(t){if(window.config){const e=$(t.target).attr("data-name"),o=config.btnsShare[e];if(o&&o.getUrl)return void window.open(o.getUrl(n.shareUrl,n.docTitle),o.target||"",o.features||"menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600")}var i;switch($(t.target).attr("data-name")){case"facebook":i="https://www.facebook.com/sharer/sharer.php?u="+n.shareUrl+"&t="+encodeURI(n.docTitle),window.open(i,"","menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600");break;case"twitter":i="https://twitter.com/share?url="+e,n.docTitle&&(i+=encodeURIComponent("&text="+n.docTitle)),window.open(i,"","menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600");break;case"email":window.open(o,"_self")}})),t.find("#id-short-url").val(n.shareUrl),t.find(".share-buttons > #email.autotest").attr("data-test",o)};function a(){var t=e.find("#txt-embed-width"),o=e.find("#txt-embed-height"),r=parseInt(t.val()),s=parseInt(o.val());r<400&&(r=400),s<600&&(s=600),e.find("#txt-embed-url").text(i.replace("{embed-url}",n.embedUrl).replace("{width}",r).replace("{height}",s)),t.val(r+"px"),o.val(s+"px")}return{init:function(t){n=t},attach:function(o){o.share&&n.shareUrl&&(t||s(),$(o.share).on("click",(function(e){t.modal("show")}))),o.embed&&n.embedUrl&&(e||function(){var t=(e=common.view.modals.create("embed")).find("#txt-embed-url");t.text(i.replace("{embed-url}",n.embedUrl).replace("{width}",400).replace("{height}",600)),e.find("#btn-copyembed").on("click",r.bind(this,t)),e.find("#txt-embed-width, #txt-embed-height").on({keypress:function(t){13==t.keyCode&&a()},focusout:function(t){a()}})}(),$(o.embed).on("click",(function(t){e.modal("show")})))},createDlgPassword:function(t){if(o)o.modal("show"),o.find("#password-input").attr("disabled",!1).addClass("error").val(""),o.find("#password-label-error").addClass("error"),o.find("#password-btn").attr("disabled",!1);else{var e=function(){t&&(o.modal("hide"),o.find("#password-input").attr("disabled",!0),o.find("#password-btn").attr("disabled",!0),setTimeout((function(){t(o.find("#password-input").val())}),350))};(o=common.view.modals.create("password")).modal({backdrop:"static",keyboard:!1}),o.modal("show"),o.find("#password-btn").on("click",(function(){e()})),o.find("#password-input").keyup((function(t){"Enter"==t.key&&e()}))}setTimeout((function(){o.find("#password-input").focus()}),500)}}},!window.common&&(window.common={}),!common.view&&(common.view={}),common.view.SearchBar=new function(){var t='<div class="asc-window search-window" style="display: none;"><div class="body">{body}</div></div>';return{create:function(e){return!e&&(e="body"),$(t.replace(/\{body}/,'<div class="search-input-group"><input type="text" id="search-bar-text" placeholder="{textFind}" autocomplete="off"><div id="search-bar-results">0/0</div></div><div class="tools"><button id="search-bar-back" class="svg-icon search-arrow-up"></button><button id="search-bar-next" class="svg-icon search-arrow-down"></button><button id="search-bar-close" class="svg-icon search-close"></button></div>').replace(/\{textFind}/,this.textFind)).appendTo(e).attr("id","dlg-search")},disableNavButtons:function(t,e){var o=""===$("#search-bar-text").val()||!e;$("#search-bar-back").attr({disabled:o}),$("#search-bar-next").attr({disabled:o})},updateResultsNumber:function(t,e){var o=$("#search-bar-results"),n=$("#search-bar-text");o.text(e&&""!==n.val()?t+1+"/"+e:"0/0")},textFind:"Find"}},!window.common&&(window.common={}),!common.controller&&(common.controller={}),common.controller.SearchBar=new function(){var t,e,o,n,i,r,s={searchText:""},a={ctrl:!1,f:!1,other:!1},c=function(){if(t||(t=common.view.SearchBar.create(),"bottom"===o.toolbarDocked?window.isrtl?t.css({left:"45px",bottom:"31px"}):t.css({right:"45px",bottom:"31px"}):window.isrtl?t.css({left:"45px",top:"31px"}):t.css({right:"45px",top:"31px"}),(e=t.find("#search-bar-text")).on("input",(function(t){common.view.SearchBar.disableNavButtons(),l(e.val())})).on("keydown",(function(t){u("keydown",e.val(),t)})),t.find("#search-bar-back").on("click",(function(t){u("back",e.val())})),t.find("#search-bar-next").on("click",(function(t){u("next",e.val())})),t.find("#search-bar-close").on("click",(function(e){m(!1),t.hide()})),common.view.SearchBar.disableNavButtons()),!t.is(":visible")){m(!0);var i=n&&n.asc_GetSelectedText()||s.searchText;e.val(i),i.length>0&&l(i),t.show(),setTimeout((function(){e.focus(),e.select()}),10)}},l=function(t){(t&&s.searchText!==t||!t&&s.newSearchText)&&(s.newSearchText=t,i=new Date,void 0===r&&(r=setInterval((function(){new Date-i<400||(s.searchText=s.newSearchText,""!==s.newSearchText?d():(n.asc_endFindText(),common.view.SearchBar.updateResultsNumber()),clearInterval(r),r=void 0)}),10)))},d=function(t,e){var o=new AscCommon.CSearchSettings;return o.put_Text(s.searchText),o.put_MatchCase(!1),o.put_WholeWords(!1),!!n.asc_findText(o,"back"!=t)||(common.view.SearchBar.disableNavButtons(),common.view.SearchBar.updateResultsNumber(),!1)},u=function(t,e,o){e&&e.length>0&&("keydown"===t&&13===o.keyCode||"keydown"!==t)&&(s.searchText=e,d(t)&&r&&(clearInterval(r),r=void 0))},p=function(t,e){common.view.SearchBar.disableNavButtons(t,e),common.view.SearchBar.updateResultsNumber(t,e)},m=function(t){s.isHighlightedResults!==t&&(n.asc_selectSearchingResults(t),s.isHighlightedResults=t)};return{init:function(e){o=e,$(document.body).on("keydown",(function(e){if(27===e.keyCode&&t&&t.is(":visible"))return m(!1),void t.hide();70===e.keyCode&&(a.f=!0),(e.ctrlKey||e.metaKey)&&(a.ctrl=!0),(e.altKey||e.shiftKey)&&(a.other=!0),a.f&&a.ctrl&&!a.other&&(e.preventDefault(),c())})).on("keyup",(function(){for(var t in a)a[t]=!1}))},setApi:function(t){(n=t)&&n.asc_registerCallback("asc_onSetSearchCurrent",p)},show:c}},void 0===DE)var DE={};DE.ApplicationView=new function(){var t,e;return{create:function(){(t=$("#box-tools button")).addClass("dropdown-toggle").attr("data-toggle","dropdown").attr("aria-expanded","true"),t.parent().append('<ul class="dropdown-menu pull-right"><li><a id="idt-download"><span class="mi-icon svg-icon download"></span>'+this.txtDownload+'</a></li><li><a id="idt-download-docx"><span class="mi-icon svg-icon download"></span>'+this.txtDownloadDocx+'</a></li><li><a id="idt-download-pdf"><span class="mi-icon"></span>'+this.txtDownloadPdf+'</a></li><li><a id="idt-print"><span class="mi-icon svg-icon print"></span>'+this.txtPrint+'</a></li><li class="divider"></li><li><a id="idt-search"><span class="mi-icon svg-icon search"></span>'+this.txtSearch+'</a></li><li class="divider"></li><li><a id="idt-share" data-toggle="modal"><span class="mi-icon svg-icon share"></span>'+this.txtShare+'</a></li><li><a id="idt-close" data-toggle="modal"><span class="mi-icon svg-icon go-to-location"></span><span class="caption">'+this.txtFileLocation+'</span></a></li><li class="divider"></li><li><a id="idt-embed" data-toggle="modal"><span class="mi-icon svg-icon embed"></span>'+this.txtEmbed+'</a></li><li><a id="idt-fullscreen"><span class="mi-icon svg-icon fullscr"></span>'+this.txtFullScreen+"</a></li></ul>")},tools:{get:function(e){return t.parent().find(e)}},getMenuForm:function(){return e||(e=$('<div id="menu-container-form" style="position: absolute; z-index: 10000;" data-value="prevent-canvas-click"><div class="dropdown-toggle" data-toggle="dropdown"></div><ul class="dropdown-menu" oo_editor_input="true" role="menu" style="right: 0; left: auto;max-height: 200px; overflow-y: auto;"></ul></div>'),$("#editor_sdk").append(e)),e},txtDownload:"Download",txtPrint:"Print",txtShare:"Share",txtEmbed:"Embed",txtFullScreen:"Full Screen",txtFileLocation:"Open file location",txtDownloadDocx:"Download as docx",txtDownloadPdf:"Download as pdf",txtSearch:"Search"}},DE.ApplicationController=new function(){var t,e,o,n,i,r,s,a,c,l,d,u={},p={},m={},h={},f=0,g=!1,v=[0,-10],w={},b=[],y=0,x=!0,k=-256;if("undefined"==typeof isBrowserSupported||isBrowserSupported())return common.localStorage.setId("text"),common.localStorage.setKeysFilter("de-,asc.text"),common.localStorage.sync(),{create:function(){if(g)return t;t=this,g=!0,$(window).resize((function(){e&&e.Resize(),y=$("body").width()})),window.onbeforeunload=Y;var o=!1;$(document.body).on("show.bs.modal",".modal",(function(t){o=!0,e.asc_enableKeyEvents(!1)})).on("hidden.bs.modal",".modal",(function(t){o=!1,e.asc_enableKeyEvents(!0)})).on("hidden.bs.dropdown",".dropdown",(function(t){o||e.asc_enableKeyEvents(!0)})).on("blur","input, textarea",(function(t){o||/area_id/.test(t.target.id)||e.asc_enableKeyEvents(!0)})),$("#editor_sdk").on("click",(function(t){"canvas"==t.target.localName&&t.currentTarget.focus()})),window.flat_desine=!0;var n=/[\?\&]fileType=\b(pdf)|(djvu|xps|oxps)\b&?/i.exec(window.location.search),i=!!n&&n.length&&"string"==typeof n[2]||!!n&&n.length&&"string"==typeof n[1]&&!window.isPDFForm;return(e=i?new Asc.PDFEditorApi({"id-view":"editor_sdk",embedded:!0,isRtlInterface:window.isrtl}):new Asc.asc_docs_api({"id-view":"editor_sdk",embedded:!0,isRtlInterface:window.isrtl}))&&(e.asc_registerCallback("asc_onError",G),e.asc_registerCallback("asc_onDocumentContentReady",N),e.asc_registerCallback("asc_onOpenDocumentProgress",U),e.asc_registerCallback("asc_onAdvancedOptions",j),e.asc_registerCallback("asc_onCountPages",_),e.asc_registerCallback("asc_onCurrentPage",T),Common.Gateway.on("init",C),Common.Gateway.on("opendocument",A),Common.Gateway.on("showmessage",H),Common.Gateway.appReady(),common.controller.SearchBar.setApi(e)),t},errorDefaultMessage:"Error code: %1",unknownErrorText:"Unknown error.",convertationTimeoutText:"Conversion timeout exceeded.",convertationErrorText:"Conversion failed.",downloadErrorText:"Download failed.",criticalErrorTitle:"Error",notcriticalErrorTitle:"Warning",scriptLoadError:"The connection is too slow, some of the components could not be loaded. Please reload the page.",errorFilePassProtect:"The file is password protected and cannot be opened.",errorAccessDeny:"You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.",errorUserDrop:"The file cannot be accessed right now.",unsupportedBrowserErrorText:"Your browser is not supported.",textOf:"of",downloadTextText:"Downloading document...",waitText:"Please, wait...",textLoadingDocument:"Loading document",txtClose:"Close",errorFileSizeExceed:"The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.",errorUpdateVersionOnDisconnect:"Internet connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.",textNext:"Next Field",textClear:"Clear All Fields",textSubmit:"Submit",textSubmited:"<b>Form submitted successfully</b><br>Click to close the tip.",errorSubmit:"Submit failed.",errorEditingDownloadas:"An error occurred during the work with the document.<br>Use the 'Download as...' option to save the file backup copy to your computer hard drive.",textGuest:"Guest",textAnonymous:"Anonymous",textRequired:"Fill all required fields to send form.",textGotIt:"Got it",errorForceSave:"An error occurred while saving the file. Please use the 'Download as' option to save the file to your computer hard drive or try again later.",txtEmpty:"(Empty)",txtPressLink:"Press %1 and click link",errorLoadingFont:"Fonts are not loaded.<br>Please contact your Document Server administrator.",errorTokenExpire:"The document security token has expired.<br>Please contact your Document Server administrator.",openErrorText:"An error has occurred while opening the file",textCtrl:"Ctrl",errorInconsistentExtDocx:"An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.",errorInconsistentExtXlsx:"An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.",errorInconsistentExtPptx:"An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.",errorInconsistentExtPdf:"An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.",errorInconsistentExt:"An error has occurred while opening the file.<br>The file content does not match the file extension.",titleLicenseExp:"License expired",titleLicenseNotActive:"License not active",warnLicenseBefore:"License not active. Please contact your administrator.",warnLicenseExp:"Your license has expired. Please update your license and refresh the page.",textConvertFormDownload:"Download file as a fillable PDF form to be able to fill it out.",textDownloadPdf:"Download pdf",errorToken:"The document security token is not correctly formed.<br>Please contact your Document Server administrator."};function C(t){u=$.extend(u,t.config),m=$.extend(m,t.config.embedded),common.controller.modals.init(m),common.controller.SearchBar.init(m),"bottom"===m.toolbarDocked?($("#toolbar").addClass("bottom"),$("#editor_sdk").addClass("bottom"),$("#box-tools").removeClass("dropdown").addClass("dropup"),v[1]=-40):($("#toolbar").addClass("top"),$("#editor_sdk").addClass("top"),v[1]=40),u.mode="view",u.canCloseEditor=!1;var e=!1;"object"==typeof u.customization&&("object"==typeof u.customization.goback&&!1!==u.canBackToFolder&&(e=void 0===u.customization.close?u.customization.goback.url||u.customization.goback.requestClose&&u.canRequestClose:u.customization.goback.url&&!u.customization.goback.requestClose,u.customization.goback.requestClose&&console.log("Obsolete: The 'requestClose' parameter of the 'customization.goback' section is deprecated. Please use 'close' parameter in the 'customization' section instead.")),u.customization.close&&"object"==typeof u.customization.close&&(u.canCloseEditor=!1!==u.customization.close.visible&&u.canRequestClose&&!u.isDesktopApp)),u.canBackToFolder=!!e}function A(n){if(p=n.doc){h=$.extend(h,p.permissions);var i=new Asc.asc_CDocInfo,r=new Asc.asc_CUserInfo,s=!("object"==typeof u.customization&&"object"==typeof u.customization.anonymous&&!1===u.customization.anonymous.request),a="object"==typeof u.customization&&"object"==typeof u.customization.anonymous&&"string"==typeof u.customization.anonymous.label&&""!==u.customization.anonymous.label.trim()?common.utils.htmlEncode(u.customization.anonymous.label):t.textGuest,c=s?common.localStorage.getItem("guest-username"):null,l=common.utils.fillUserInfo(u.user,u.lang,c?c+" ("+a+")":t.textAnonymous,common.localStorage.getItem("guest-id")||"uid-"+Date.now());l.anonymous&&common.localStorage.setItem("guest-id",l.id),r.put_Id(l.id),r.put_FullName(l.fullname),r.put_IsAnonymousUser(l.anonymous),i.put_Id(p.key),i.put_Url(p.url),i.put_DirectUrl(p.directUrl),i.put_Title(p.title),i.put_Format(p.fileType),i.put_VKey(p.vkey),i.put_UserInfo(r),i.put_CallbackUrl(u.callbackUrl),i.put_Token(p.token),i.put_Permissions(p.permissions),i.put_EncryptedInfo(u.encryptionKeys),i.put_Lang(u.lang),i.put_Mode(u.mode),i.put_Wopi(u.wopi),u.shardkey&&i.put_Shardkey(u.shardkey);var d=!u.customization||!1!==u.customization.macros;i.asc_putIsEnabledMacroses(!!d),d=!u.customization||!1!==u.customization.plugins,i.asc_putIsEnabledPlugins(!!d);var f=/^(?:(pdf|djvu|xps|oxps))$/.exec(p.fileType);f&&"string"==typeof f[1]&&(h.edit=h.review=!1),e&&(e.asc_registerCallback("asc_onGetEditorPermissions",B),e.asc_registerCallback("asc_onRunAutostartMacroses",X),e.asc_setDocInfo(i),e.asc_getEditorPermissions(u.licenseUrl,u.customerId),e.asc_enableKeyEvents(!0),Common.Analytics.trackEvent("Load","Start")),m.docTitle=p.title,(o=$("#title-doc-name")).text(m.docTitle||"")}}function _(e){f=e,$("#pages").text(t.textOf+" "+e)}function T(t){$("#page-number").val(t+1)}function S(e,o){var s="";switch(o){case Asc.c_oAscAsyncAction.Print:s=t.downloadTextText;break;case Asc.c_oAscAsyncAction.Submit:i=!1,r&&r.hide(),n.attr({disabled:!0}),n.css("pointer-events","none");break;case k:s=t.textLoadingDocument+"           ";break;default:s=t.waitText}e==Asc.c_oAscAsyncActionType.BlockInteraction&&(t.loadMask||(t.loadMask=new common.view.LoadMask),t.loadMask.setTitle(s),t.loadMask.show())}function E(e,o){o==Asc.c_oAscAsyncAction.Submit&&(n.removeAttr("disabled"),n.css("pointer-events","auto"),i||(r||(r=$('<div class="submit-tooltip" style="display:none;">'+t.textSubmited+"</div>"),$(document.body).append(r),r.on("click",(function(){r.hide()}))),r.show())),t.loadMask&&t.loadMask.hide()}function D(){t.isHideBodyTip=!0}function I(){t.isHideBodyTip&&d&&(d.tooltip("hide"),d=!1)}function F(e){if(e){var o=e.get_Type();if(o==Asc.c_oAscMouseMoveDataTypes.Hyperlink||o==Asc.c_oAscMouseMoveDataTypes.Form){t.isHideBodyTip=!1;var n=o==Asc.c_oAscMouseMoveDataTypes.Hyperlink?t.txtPressLink.replace("%1",common.utils.isMac?"⌘":t.textCtrl):e.get_FormHelpText();if(n.length>500&&(n=n.substr(0,500)+"..."),n=common.utils.htmlEncode(n),l||(l=$(".hyperlink-tooltip")).tooltip({container:"body",trigger:"manual"}),l.ttpos=[e.get_X(),e.get_Y()],d||(d=l.data("bs.tooltip").tip()),d.is(":visible"))d.find(".tooltip-inner").text(n);else{var i=l.data("bs.tooltip");i.options.title=n,i.show([-1e3,-1e3])}var r=d.height(),s=d.width();!y&&(y=$("body").width()),l.ttpos[1]-=r-v[1]+20,l.ttpos[0]+s+10>y?(l.ttpos[0]=y-s-5,l.ttpos[1]<0&&(l.ttpos[1]+=r+v[1]+20)):l.ttpos[1]<0&&(l.ttpos[1]=0,l.ttpos[0]+=20),d.css({left:l.ttpos[0],top:l.ttpos[1]})}}}function R(t,e){Common.Gateway.downloadAs(t,e)}function L(){!1!==h.print&&e.asc_Print(new Asc.asc_CDownloadOptions(null,$.browser.chrome||$.browser.safari||$.browser.opera||$.browser.mozilla&&$.browser.versionNumber>86))}function O(t){common.utils.dialogPrint(t,e)}function P(t){t?(n.removeAttr("disabled"),n.css("pointer-events","auto")):(n.attr({disabled:!0}),n.css("pointer-events","none"))}function q(o,n,i){switch(o.type){case Asc.c_oAscContentControlSpecificType.Picture:if(o.pr&&o.pr.get_Lock){var r=o.pr.get_Lock();if(r==Asc.c_oAscSdtLockType.SdtContentLocked||r==Asc.c_oAscSdtLockType.ContentLocked)return}e.asc_addImage(o),setTimeout((function(){e.asc_UncheckContentControlButtons()}),500);break;case Asc.c_oAscContentControlSpecificType.DropDownList:case Asc.c_oAscContentControlSpecificType.ComboBox:!function(o,n,i){var r=o.type,s=o.pr,l=r==Asc.c_oAscContentControlSpecificType.ComboBox?s.get_ComboBoxPr():s.get_DropDownListPr(),d=!!s.get_FormPr(),u=DE.ApplicationView.getMenuForm();a||((a=u.find("ul")).on("click","li",(function(t){var o=$(t.target).attr("value");o&&(o=parseInt(o),setTimeout((function(){-1!==o&&e.asc_SelectContentControlListItem(b[o],c.get_InternalId())}),1))})),$("#editor_sdk").on("click",(function(o){"canvas"==o.target.localName&&(t._preventClick?t._preventClick=!1:(a&&a.hide(),e.asc_UncheckContentControlButtons()))})));if(a.find("li").remove(),b=[],c=s,l){var p=0;if(d){var m=s.get_PlaceholderText();a.append('<li><a tabindex="-1" type="menuitem" style="opacity: 0.6" value="0">'+(""!==m.trim()?m:t.txtEmpty)+"</a></li>"),b.push("")}var h=l.get_ItemsCount();p=b.length;for(var f=0;f<h;f++)""===l.get_ItemValue(f)&&d||(a.append('<li><a tabindex="-1" type="menuitem" value="'+(f+p)+'">'+common.utils.htmlEncode(l.get_ItemDisplayText(f))+"</a></li>"),b.push(l.get_ItemValue(f)));!d&&b.length<1&&(a.append('<li><a tabindex="-1" type="menuitem" value="0">'+t.txtEmpty+"</a></li>"),b.push(-1))}u.css({left:n,top:i}),t._preventClick=!0,a.show()}(o,n,i)}}function z(){a&&a.hide(),e.asc_UncheckContentControlButtons()}function M(){$("#loading-mask").fadeOut("slow")}function N(){M(),E(Asc.c_oAscAsyncActionType.BlockInteraction,k);var o=u.customization&&u.customization.zoom?parseInt(u.customization.zoom):-2;-1==o?e.zoomFitToPage():-2==o?e.zoomFitToWidth():e.zoom(o>0?o:100);var i=$("#box-tools .divider"),r=$("#box-tools a").length;if(!1===h.print&&($("#idt-print").hide(),r--),m.saveUrl&&!1!==h.download&&!w.canFillForms||($("#idt-download").hide(),r--),w.canFillForms&&!1!==h.download||($("#idt-download-docx").hide(),r--),(w.canFillForms||w.isOForm)&&!1!==h.download||($("#idt-download-pdf").hide(),r--),m.shareUrl&&!w.canFillForms||($("#idt-share").hide(),r--),u.canBackToFolder){var a=u.customization.goback.text;a&&"string"==typeof a&&$("#idt-close .caption").text(a)}else $("#idt-close").hide(),r--;u.canCloseEditor&&$("#id-btn-close-editor").removeClass("hidden"),r<7&&($(i[0]).hide(),$(i[1]).hide()),m.embedUrl&&!w.canFillForms||($("#idt-embed").hide(),r--),m.fullscreenUrl||($("#idt-fullscreen").hide(),r--),r<1?$("#box-tools").addClass("hidden"):m.embedUrl&&!w.canFillForms||m.fullscreenUrl||$(i[2]).hide(),common.controller.modals.attach({share:"#idt-share",embed:"#idt-embed"}),e.asc_registerCallback("asc_onStartAction",S),e.asc_registerCallback("asc_onEndAction",E),e.asc_registerCallback("asc_onMouseMoveStart",D),e.asc_registerCallback("asc_onMouseMoveEnd",I),e.asc_registerCallback("asc_onMouseMove",F),e.asc_registerCallback("asc_onHyperlinkClick",common.utils.openLink),e.asc_registerCallback("asc_onDownloadUrl",R),e.asc_registerCallback("asc_onPrint",L),e.asc_registerCallback("asc_onPrintUrl",O),e.asc_registerCallback("sync_onAllRequiredFormsFilled",P),w.canFillForms&&(e.asc_registerCallback("asc_onShowContentControlsActions",q),e.asc_registerCallback("asc_onHideContentControlsActions",z),e.asc_SetHighlightRequiredFields(!0)),Common.Gateway.on("processmouse",W),Common.Gateway.on("downloadas",K),Common.Gateway.on("requestclose",V),DE.ApplicationView.tools.get("#idt-fullscreen").on("click",(function(){common.utils.openLink(m.fullscreenUrl)})),DE.ApplicationView.tools.get("#idt-download").on("click",(function(){m.saveUrl&&!1!==h.download&&common.utils.openLink(m.saveUrl),Common.Analytics.trackEvent("Save")})),DE.ApplicationView.tools.get("#idt-print").on("click",(function(){e.asc_Print(new Asc.asc_CDownloadOptions(null,$.browser.chrome||$.browser.safari||$.browser.opera||$.browser.mozilla&&$.browser.versionNumber>86)),Common.Analytics.trackEvent("Print")})),DE.ApplicationView.tools.get("#idt-close").on("click",(function(){u.customization&&u.customization.goback&&(u.customization.goback.requestClose&&u.canRequestClose?Common.Gateway.requestClose():u.customization.goback.url&&(!1!==u.customization.goback.blank?window.open(u.customization.goback.url,"_blank"):window.parent.location.href=u.customization.goback.url))})),$("#id-btn-close-editor").on("click",(function(){u.canRequestClose&&Common.Gateway.requestClose()}));var c=function(t){e.asc_DownloadAs(new Asc.asc_CDownloadOptions(t)),Common.Analytics.trackEvent("Save")};DE.ApplicationView.tools.get("#idt-download-docx").on("click",(function(){c(Asc.c_oAscFileType.DOCX)})),DE.ApplicationView.tools.get("#idt-download-pdf").on("click",(function(){c(Asc.c_oAscFileType.PDF)})),DE.ApplicationView.tools.get("#idt-search").on("click",(function(){common.controller.SearchBar.show()})),$("#id-btn-zoom-in").on("click",e.zoomIn.bind(this)),$("#id-btn-zoom-out").on("click",e.zoomOut.bind(this));var l,d=$("#page-number");if(d.on({keyup:function(t){if(13==t.keyCode){var o=parseInt($("#page-number").val());o>f&&(o=f),(o<2||isNaN(o))&&(o=1),e.goToPage(o-1),d.blur()}},focusin:function(t){d.removeClass("masked")},focusout:function(t){!d.hasClass("masked")&&d.addClass("masked")}}),$("#pages").on("click",(function(t){d.focus()})),w.canSubmitForms&&w.canFillForms&&!e.asc_IsAllRequiredFormsFilled()){var p=$("#id-submit-group");if(n.attr({disabled:!0}),n.css("pointer-events","none"),common.localStorage.getItem("de-embed-hide-submittip"))p.attr("data-toggle","tooltip"),p.tooltip({title:t.textRequired,placement:"bottom"});else{var g=common.utils.getOffset(n);s=$('<div class="required-tooltip bottom-left" style="display:none;"><div class="tip-arrow bottom-left"></div><div>'+t.textRequired+'</div><div class="close-div">'+t.textGotIt+"</div></div>"),$(document.body).append(s),s.css({top:g.top+n.height()+"px",left:g.left+n.outerWidth()/2-s.outerWidth()+"px"}),s.find(".close-div").on("click",(function(){s.hide(),e.asc_MoveToFillingForm(!0,!0,!0),common.localStorage.setItem("de-embed-hide-submittip",1),p.attr("data-toggle","tooltip"),p.tooltip({title:t.textRequired,placement:"bottom"})})),s.show()}}var v=!1;$(document).mousemove((function(t){$("#id-btn-zoom-in").fadeIn(),$("#id-btn-zoom-out").fadeIn(),v=!0,l||(l=setInterval((function(){v||($("#id-btn-zoom-in").fadeOut(),$("#id-btn-zoom-out").fadeOut(),clearInterval(l),l=void 0),v=!1}),2e3))})),w.isOForm&&!1!==h.download&&($("#id-critical-error-title").text(t.notcriticalErrorTitle),$("#id-critical-error-message").html(t.textConvertFormDownload),$("#id-critical-error-close").text(t.textDownloadPdf).off().on("click",(function(){c(Asc.c_oAscFileType.PDF),$("#id-critical-error-dialog").modal("hide")})),$("#id-critical-error-dialog").modal("show")),Common.Gateway.documentReady(),Common.Analytics.trackEvent("Load","Complete"),x=!1}function B(i){var r=i.asc_getLicenseType();if(Asc.c_oLicenseResult.Expired===r||Asc.c_oLicenseResult.Error===r||Asc.c_oLicenseResult.ExpiredTrial===r||Asc.c_oLicenseResult.NotBefore===r||Asc.c_oLicenseResult.ExpiredLimited===r)return $("#id-critical-error-title").text(Asc.c_oLicenseResult.NotBefore===r?t.titleLicenseNotActive:t.titleLicenseExp),$("#id-critical-error-message").html(Asc.c_oLicenseResult.NotBefore===r?t.warnLicenseBefore:t.warnLicenseExp),$("#id-critical-error-close").parent().remove(),$("#id-critical-error-dialog button.close").remove(),void $("#id-critical-error-dialog").css("z-index",20002).modal({backdrop:"static",keyboard:!1,show:!0});w.canLicense=r===Asc.c_oLicenseResult.Success||r===Asc.c_oLicenseResult.SuccessLimit,w.canFillForms=!1,w.canSubmitForms=w.canLicense&&"object"==typeof u.customization&&!!u.customization.submitForm,w.canBranding=i.asc_getCustomization(),w.canBranding&&function(t){if(t&&t.logo){var e=$("#header-logo");if(!1===t.logo.visible)return void e.addClass("hidden");(t.logo.image||t.logo.imageEmbedded)&&(e.html('<img src="'+(t.logo.image||t.logo.imageEmbedded)+'" style="max-width:100px; max-height:20px;"/>'),e.css({"background-image":"none",width:"auto",height:"auto"}),t.logo.imageEmbedded&&console.log("Obsolete: The 'imageEmbedded' parameter of the 'customization.logo' section is deprecated. Please use 'image' parameter instead.")),t.logo.url?e.attr("href",t.logo.url):void 0!==t.logo.url&&(e.removeAttr("href"),e.removeAttr("target"))}}(u.customization);var s=/^(?:(docxf|oform))$/.exec(p.fileType);w.isOForm=!(!s||"string"!=typeof s[1]),e.asc_setViewMode(!w.canFillForms),e.asc_setPdfViewer(!w.canFillForms),n=$("#id-btn-submit"),w.canFillForms?($("#id-btn-next-field .caption").text(t.textNext),$("#id-btn-clear-fields .caption").text(t.textClear),$("#id-btn-prev-field").on("click",(function(){e.asc_MoveToFillingForm(!1)})),$("#id-btn-next-field").on("click",(function(){e.asc_MoveToFillingForm(!0)})),$("#id-btn-clear-fields").on("click",(function(){e.asc_ClearAllSpecialForms()})),w.canSubmitForms?(n.find(".caption").text(t.textSubmit),n.on("click",(function(){e.asc_SendForm()}))):n.hide(),e.asc_setRestriction(Asc.c_oAscRestrictionType.OnlyForms),e.asc_SetFastCollaborative(!0),e.asc_setAutoSaveGap(1)):($("#id-btn-prev-field").hide(),$("#id-btn-next-field").hide(),$("#id-btn-clear-fields").hide(),n.hide());var a=o.parent(),c=common.utils.getPosition(a).left,l=a.next().outerWidth();c<l?a.css("padding-left",parseFloat(a.css("padding-left"))+l-c):a.css("padding-right",parseFloat(a.css("padding-right"))+c-l),S(Asc.c_oAscAsyncActionType.BlockInteraction,k),e.asc_LoadDocument(),e.Resize()}function U(e){var o=(e.asc_getCurrentFont()+e.asc_getCurrentImage())/(e.asc_getFontsCount()+e.asc_getImagesCount());t.loadMask&&t.loadMask.setTitle(t.textLoadingDocument+": "+common.utils.fixedDigits(Math.min(Math.round(100*o),100),3,"  ")+"%")}function j(o,n,i,r){if(o==Asc.c_oAscAdvancedOptionsID.DRM){var s=!!u.customization.loaderName||!!u.customization.loaderLogo;common.controller.modals.createDlgPassword((function(o){e&&e.asc_setAdvancedOptions(Asc.c_oAscAdvancedOptionsID.DRM,new Asc.asc_CDRMAdvancedOptions(o)),t.loadMask&&t.loadMask.show(),s||($("#loading-mask").addClass("end-animation"),$("#loading-mask").removeClass("none-animation"))})),s?M():($("#loading-mask").removeClass("end-animation"),$("#loading-mask").addClass("none-animation")),E(Asc.c_oAscAsyncActionType.BlockInteraction,k)}else o==Asc.c_oAscAdvancedOptionsID.TXT&&(e&&e.asc_setAdvancedOptions(Asc.c_oAscAdvancedOptionsID.TXT,n.asc_getRecommendedSettings()||new Asc.asc_CTextOptions),E(Asc.c_oAscAsyncActionType.BlockInteraction,k));x&&(Common.Gateway.userActionRequired(),x=!1)}function G(e,o,n){if(e==Asc.c_oAscError.ID.LoadingScriptError)return $("#id-critical-error-title").text(t.criticalErrorTitle),$("#id-critical-error-message").text(t.scriptLoadError),$("#id-critical-error-close").text(t.txtClose).off().on("click",(function(){window.location.reload()})),$("#id-critical-error-dialog button.close").remove(),void $("#id-critical-error-dialog").css("z-index",20002).modal("show");var s;switch(M(),E(Asc.c_oAscAsyncActionType.BlockInteraction,k),e){case Asc.c_oAscError.ID.Unknown:s=t.unknownErrorText;break;case Asc.c_oAscError.ID.ConvertationTimeout:s=t.convertationTimeoutText;break;case Asc.c_oAscError.ID.ConvertationError:s=t.convertationErrorText;break;case Asc.c_oAscError.ID.ConvertationOpenError:s=t.openErrorText;break;case Asc.c_oAscError.ID.DownloadError:s=t.downloadErrorText;break;case Asc.c_oAscError.ID.ConvertationPassword:s=t.errorFilePassProtect;break;case Asc.c_oAscError.ID.UserDrop:s=t.errorUserDrop;break;case Asc.c_oAscError.ID.ConvertationOpenLimitError:s=t.errorFileSizeExceed;break;case Asc.c_oAscError.ID.UpdateVersion:s=t.errorUpdateVersionOnDisconnect;break;case Asc.c_oAscError.ID.AccessDeny:s=t.errorAccessDeny;break;case Asc.c_oAscError.ID.Submit:s=t.errorSubmit,i=!0,r&&r.hide();break;case Asc.c_oAscError.ID.EditingError:s=t.errorEditingDownloadas;break;case Asc.c_oAscError.ID.ForceSaveButton:case Asc.c_oAscError.ID.ForceSaveTimeout:s=t.errorForceSave;break;case Asc.c_oAscError.ID.LoadingFontError:s=t.errorLoadingFont;break;case Asc.c_oAscError.ID.KeyExpire:s=t.errorTokenExpire;break;case Asc.c_oAscError.ID.VKeyEncrypt:s=t.errorToken;break;case Asc.c_oAscError.ID.ConvertationOpenFormat:s="pdf"===n?t.errorInconsistentExtPdf.replace("%1",p.fileType||""):"docx"===n?t.errorInconsistentExtDocx.replace("%1",p.fileType||""):"xlsx"===n?t.errorInconsistentExtXlsx.replace("%1",p.fileType||""):"pptx"===n?t.errorInconsistentExtPptx.replace("%1",p.fileType||""):t.errorInconsistentExt;break;case Asc.c_oAscError.ID.SessionToken:default:return}o==Asc.c_oAscError.Level.Critical?(Common.Gateway.reportError(e,s),$("#id-critical-error-title").text(t.criticalErrorTitle),$("#id-critical-error-message").html(s),$("#id-critical-error-close").text(t.txtClose).off().on("click",(function(){window.location.reload()})),$("#id-critical-error-dialog button.close").remove()):(Common.Gateway.reportWarning(e,s),$("#id-critical-error-title").text(t.notcriticalErrorTitle),$("#id-critical-error-message").html(s),$("#id-critical-error-close").text(t.txtClose).off().on("click",(function(){$("#id-critical-error-dialog").modal("hide")}))),$("#id-critical-error-dialog").modal("show"),Common.Analytics.trackEvent("Internal Error",e.toString())}function H(e){e&&(M(),$("#id-error-mask-title").text(t.criticalErrorTitle),$("#id-error-mask-text").text(e.msg),$("#id-error-mask").css("display","block"),Common.Analytics.trackEvent("External Error"))}function W(t){if("mouseup"==t.type){var o=document.getElementById("editor_sdk");if(o){var n=common.utils.getBoundingClientRect(o);e.OnMouseUp(t.x-n.left,t.y-n.top)}}}function V(){Common.Gateway.requestClose()}function K(){if(!1!==h.download){if(e){var o=new Asc.asc_CDownloadOptions(Asc.c_oAscFileType.DOCX,!0);o.asc_setIsSaveAs(!0),e.asc_DownloadAs(o)}}else Common.Gateway.reportError(Asc.c_oAscError.ID.AccessDeny,t.errorAccessDeny)}function X(){u.customization&&!1===u.customization.macros||e&&e.asc_runAutostartMacroses()}function Y(){common.localStorage.save()}Common.Gateway.reportError(void 0,this.unsupportedBrowserErrorText)},Common.Locale.apply((function(){DE.ApplicationView.create(),DE.ApplicationController.create()}));