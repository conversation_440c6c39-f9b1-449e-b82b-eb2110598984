﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insert and format your text</title>
		<meta charset="utf-8" />
		<meta name="description" content="Learn how to insert and format text box in PowerPoint presentations with ONLYOFFICE. Align the text, change its direction, adjust paragraph advanced settings." />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Insert and format your text</h1>
			<h2>Insert your text box into a presentation</h2>
			<p>In the <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a>, you can add new text in three different ways:</p>
			<ul>
				<li>Add a text passage within the corresponding text placeholder on the slide layout. To do that, just put the cursor within the placeholder and type in your text or paste it using the <b>Ctrl+V</b> key combination instead of the default text.</li>
				<li>
					Add a text passage anywhere on a slide. You can insert a text box (a rectangular frame that allows you to enter some text within it) or a Text Art object (a text box with a predefined font style and color that allows you to apply some text effects). Depending on the necessary text object type, you can do the following:
					<ul>
						<li>
							to add a text box, click the <div class="icon icon-inserttexticon"></div> <b>Text Box</b> icon on the <b>Home</b> or <b>Insert</b> tab of the top toolbar, choose one of the following options: <b>Insert horizontal text box</b> or <b>Insert vertical text box</b>, then click where you want to insert the text box, hold the mouse button and drag the text box border to specify its size. When you release the mouse button, the insertion point will appear in the added text box, allowing you to enter your text.
							<p class="note">It's also possible to insert a text box by clicking the <span class="icon icon-insertautoshape"></span> <b>Shape</b> icon on the top toolbar and selecting the <span class="icon icon-text_autoshape"></span> shape from the <b>Basic Shapes</b> group.</p>
						</li>
						<li>to add a Text Art object, click the <div class="icon icon-inserttextarticon"></div> <b>Text Art</b> icon on the <b>Insert</b> tab of the top toolbar, then click on the desired style template – the Text Art object will be added in the center of the slide. Select the default text within the text box with the mouse and replace it with your own text.</li>
					</ul>
				</li>
				<li>Add a text passage within an autoshape. Select a shape and start typing your text.</li>
			</ul>
			<p>Click outside of the text object to apply the changes and return to the slide.</p>
			<p>The text within the text object is a part of the latter (when you move or rotate the text object, the text moves or rotates with it).</p>
			<p>As an inserted text object represents a rectangular frame (it has invisible text box borders by default) with text in it and this frame is a common autoshape, you can change both the shape and text properties.</p>
			<p>You can save the text box as a picture on your hard drive using the <b>Save as picture</b> option in the right-click menu.</p>
			<p>To delete the added text object, click on the text box border and press the <b>Delete</b> key. The text within the text box will also be deleted.</p>
			<h2>Format a text box</h2>
			<p>Select the text box by clicking on its border to change its properties. When the text box is selected, its borders are displayed as solid (not dashed) lines.</p>
			<p><img alt="Text box selected" src="../images/textbox_boxselected.png" /></p>
			<ul>
				<li>to <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">resize, move, rotate</a> the text box, use the special handles on the edges of the shape.</li>
				<li>to edit the text box <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">fill</a>, <a href="../UsageInstructions/InsertAutoshapes.htm#shapestroke" onclick="onhyperlinkclick(this)">line</a>, <b>replace</b> the rectangular box with a different shape, or access the <a href="InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">shape advanced settings</a>, click the <b>Shape settings</b> <div class="icon icon-shape_settings_icon"></div> icon on the right sidebar and use the corresponding options.</li>
				<li>to <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">align a text box on the slide, rotate or flip it, arrange</a> text boxes as related to other objects, right-click on the text box border, and use the contextual menu options.</li>
				<li>to create <b>columns of text</b> within the text box, click the corresponding icon <div class="icon icon-insert_columns"></div> on the text formatting toolbar and choose the preferable option, or right-click on the text box border, click the <b>Shape Advanced Settings</b> option, and switch to the <a href="../UsageInstructions/InsertAutoshapes.htm#columns" onclick="onhyperlinkclick(this)"><b>Columns</b></a> tab in the <b>Shape - Advanced Settings</b> window.</li>
			</ul>
			<h2 id="formattext">Format the text within the text box</h2>
			<p>Click the text within the text box to change its properties. When the text is selected, the text box borders are displayed as dashed lines.</p>
			<p><img alt="Text selected" src="../images/textbox_textselected.png" /></p>
			<p class="note"><b>Note</b>: it's also possible to change text formatting when the text box (not the text itself) is selected. In such a case, any changes will be applied to the whole text within the text box. Some font formatting options (font type, size, color, and decoration styles) can be applied to the previously selected part of the text separately.</p>
			<p><b>Align your text within the text box</b></p>
			<p>The text is aligned horizontally in four ways: left, right, center, or justified. To do that:</p>
			<ol>
				<li>place the cursor in the position where you want the alignment to be applied (this can be a new line or already entered text),</li>
				<li>drop-down the <b>Horizontal align</b> <div class="icon icon-horizontalalign"></div>list on the <b>Home</b> tab of the top toolbar,</li>
				<li>
					select the alignment type you would like to apply:
					<ul>
						<li>the <b>Align text left</b> option <div class="icon icon-alignleft"></div> allows you to line up your text on the left side of the text box (the right side remains unaligned).</li>
						<li>the <b>Align text center</b> option <div class="icon icon-aligncenter"></div> allows you to line up your text in the center of the text box (the right and the left sides remain unaligned).</li>
						<li>the <b>Align text right</b> option <div class="icon icon-alignright"></div> allows you to line up your text on the right side of the text box (the left side remains unaligned).</li>
						<li>the <b>Justify</b> option <div class="icon icon-justify"></div> allows you to line up your text both on the left and on the right sides of the text box (additional spacing is added where necessary to keep the alignment).</li>
					</ul>
				</li>
			</ol>
			<p class="note"><b>Note</b>: these parameters can also be found in the <a href="../UsageInstructions/InsertText.htm#textadvancedsettings" onclick="onhyperlinkclick(this)"><b>Paragraph - Advanced Settings</b></a> window.</p>
			<p>The text is aligned vertically in three ways: top, middle, or bottom. To do that:</p>
			<ol>
				<li>place the cursor in the position where you want the alignment to be applied (this can be a new line or already entered text),</li>
				<li>drop-down the <b>Vertical align</b> <div class="icon icon-verticalalign"></div>list on the <b>Home</b> tab of the top toolbar,</li>
				<li>
					select the alignment type you would like to apply:
					<ul>
						<li>the <b>Align text to the top</b> option <div class="icon icon-aligntop"></div> allows you to line up your text to the top of the text box.</li>
						<li>the <b>Align text to the middle</b> option <div class="icon icon-alignmiddle"></div> allows you to line up your text in the center of the text box.</li>
						<li>the <b>Align text to the bottom</b> option <div class="icon icon-alignbottom"></div> allows you to line up your text to the bottom of the text box.</li>
					</ul>
				</li>
			</ol>
			<hr />
			<p><b>Change the text direction</b></p>
			<p>To <b>Rotate</b> the text within the text box, right-click the text, select the <b>Text Direction</b> option, and then choose one of the available options: <b>Horizontal</b> (selected by default), <b>Rotate Text Down</b> (used to set a vertical direction, from top to bottom) or <b>Rotate Text Up</b> (used to set a vertical direction, from bottom to top).</p>
			<hr />
			<p id="formatfont"><b>Adjust font type, size, color and apply decoration styles</b></p>
			<p>You can select the font type, size, and color as well as apply various font decoration styles using the corresponding icons situated on the <b>Home</b> tab of the top toolbar.</p>
			<p class="note"><b>Note</b>: in case you want to apply the formatting to the text already present in the presentation, select it with the mouse or <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">use the keyboard</a> and apply the formatting. You can also place the mouse cursor within the necessary word to apply the formatting to this word only.</p>
			<table>
				<tr>
					<td width="10%">Font</td>
					<td width="15%"><div class="big big-fontfamily"></div></td>
					<td>Used to select one of the fonts from the list of available ones. <span class="desktopDocumentFeatures">If the required font is not available in the list, you can download and install it on your operating system, and the font will be available for use in the <em>desktop version</em>.</span></td>
				</tr>
				<tr>
					<td>Font size</td>
					<td><div class="icon icon-fontsize"></div></td>
					<td>Used to choose from the preset font size values in the dropdown list (the default values are: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 and 96).  It's also possible to manually enter a custom value up to 300 pt in the font size field. Press <em>Enter</em> to confirm.</td>
				</tr>
				<tr>
					<td>Increment font size</td>
					<td><div class="icon icon-larger"></div></td>
					<td>Used to change the font size making it one point bigger each time the button is pressed.</td>
				</tr>
				<tr>
					<td>Decrement font size</td>
					<td><div class="icon icon-smaller"></div></td>
					<td>Used to change the font size making it one point smaller each time the button is pressed.</td>
				</tr>
				<tr>
					<td>Change case</td>
					<td><div class="icon icon-change_case"></div></td>
					<td>Used to change the font case. <em>Sentence case.</em> - the case matches that of a common sentence. <em>lowercase</em> - all letters are small. <em>UPPERCASE</em> - all letters are capitalized. <em>Capitalize Each Word</em> - each word starts with a capital letter. <em>tOGGLE cASE</em> - reverse the case of the selected text or the word where the mouse cursor is positioned.</td>
				</tr>
				<tr>
					<td>Highlight color</td>
					<td><div class="icon icon-highlightcolor"></div></td>
					<td>Used to mark separate sentences, phrases, words, or even characters by adding a color band that imitates the highlighter pen effect throughout the text. You can select the required part of the text and click the downward arrow next to the icon to select a color in the palette (this color set does not depend on the selected <b>Color scheme</b> and includes 16 colors) - the color will be applied to the selected text. Alternatively, you can first choose a highlight color and then start selecting the text with the mouse - the mouse pointer will look like this <div class="icon icon-highlight_color_mouse_pointer"></div> and you'll be able to highlight several different parts of your text sequentially. To stop highlighting, just click the icon once again. To delete the highlight color, choose the <b>No Fill</b> option.</td>
				</tr>
				<tr>
					<td>Font color</td>
					<td><div class="icon icon-fontcolor"></div></td>
					<td>Used to change the color of the letters/characters in the text. Click the downward arrow next to the icon to <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">select the color</a>.</td>
				</tr>
				<tr>
					<td>Bold</td>
					<td><div class="icon icon-bold"></div></td>
					<td>Used to make the font bold giving it a heavier appearance.</td>
				</tr>
				<tr>
					<td>Italic</td>
					<td><div class="icon icon-italic"></div></td>
					<td>Used to make the font slightly slanted to the right.</td>
				</tr>
				<tr>
					<td>Underline</td>
					<td><div class="icon icon-underline"></div></td>
					<td>Used to make the text underlined with a line going under the letters.</td>
				</tr>
				<tr>
					<td>Strikeout</td>
					<td><div class="icon icon-strike"></div></td>
					<td>Used to make the text struck out with a line going through the letters.</td>
				</tr>
				<tr>
					<td>Superscript</td>
					<td><div class="icon icon-sup"></div></td>
					<td>Used to make the text smaller placing it in the upper part of the text line, e.g. as in fractions.</td>
				</tr>
				<tr>
					<td>Subscript</td>
					<td><div class="icon icon-sub"></div></td>
					<td>Used to make the text smaller placing it in the lower part of the text line, e.g. as in chemical formulas.</td>
				</tr>
			</table>
			<p><b>Set line spacing and change paragraph indents</b></p>
			<p>You can set the line height for the text lines within the paragraph as well as the margins between the current and the previous or the following paragraph.</p>
			<p><img class="floatleft" alt="Paragraph Settings tab" src="../images/textsettingstab.png" /></p>
			<p>To do that,</p>
			<ol style="margin-left: 280px;">
				<li>put the cursor within the required paragraph or select several paragraphs with the mouse,</li>
				<li>
					use the corresponding fields of the <div class="icon icon-text_settings_icon"></div> <b>Paragraph settings</b> tab on the right sidebar to achieve the desired results:
					<ul>
						<li><b>Line Spacing</b> - set the line height for the text lines within the paragraph. You can select among two options: <b>multiple</b> (sets line spacing that can be expressed in numbers greater than 1), <b>exactly</b> (sets fixed line spacing). You can specify the necessary value in the field on the right.</li>
						<li>
							<b>Paragraph Spacing</b> - set the amount of space between paragraphs.
							<ul>
								<li><b>Before</b> - set the amount of space before the paragraph.</li>
								<li><b>After</b> - set the amount of space after the paragraph.</li>
							</ul>
						</li>
					</ul>
				</li>
			</ol>
			<p class="note"><b>Note</b>: these parameters can also be found in the <a href="../UsageInstructions/InsertText.htm#textadvancedsettings" onclick="onhyperlinkclick(this)"><b>Paragraph - Advanced Settings</b></a> window.</p>
			<p>To quickly change the current paragraph line spacing, you can also use the <b>Paragraph line spacing</b> <span class="icon icon-linespacing"></span> icon on the <b>Home</b> tab of the top toolbar selecting the required value from the list: 1.0, 1.15, 1.5, 2.0, 2.5, or 3.0 lines, as well as open the corresponding right panel by clicking the <b>Line spacing options</b> menu item.</p>
			<p>To change the paragraph offset from the left side of the text box, put the cursor within the required paragraph, or select several paragraphs with the mouse and use the respective icons on the <b>Home</b> tab of the top toolbar: <b>Decrease indent</b> <span class="icon icon-decreaseindent"></span> and <b>Increase indent</b> <span class="icon icon-increaseindent"></span>.</p>
			<h2 id="textadvancedsettings">Adjust paragraph advanced settings</h2>
			<p>To open the <b>Paragraph - Advanced Settings</b> window, right-click the text and choose the <b>Paragraph Advanced Settings</b> option from the menu. It's also possible to put the cursor within the required paragraph - the <span class="icon icon-text_settings_icon"></span> <b>Paragraph settings</b> tab will be activated on the right sidebar. Press the <b>Show advanced settings</b> link. The paragraph properties window will be opened:</p>
			<img alt="Paragraph Properties - Indents & Spacing tab" src="../images/textadvancedsettings1.png" />
			<p>The <b>Indents & Spacing</b> tab allows you to:</p>
			<ul>
				<li>change the <b>alignment</b> type for the paragraph text,</li>
				<li>
					change the paragraph <b>indents</b> as related to the <a href="../UsageInstructions/InsertAutoshapes.htm#internalmargins" onclick="onhyperlinkclick(this)">internal margins</a> of the text box,
					<ul>
						<li><b>Left</b> - set the paragraph offset from the <b>left</b> internal margin of the text box specifying the necessary numeric value,</li>
						<li><b>Right</b> - set the paragraph offset from the <b>right</b> internal margin of the text box specifying the necessary numeric value,</li>
						<li><b>Special</b> - set an indent for the <b>first line</b> of the paragraph: select the corresponding menu item (<b>(none)</b>, <b>First line</b>, <b>Hanging</b>) and change the default numeric value specified for <b>First Line</b> or <b>Hanging</b>,</li>
					</ul>
				</li>
				<li>change the paragraph <b>line spacing</b>.</li>
			</ul>
			<p>You can also use the horizontal <b>ruler</b> to set indents.</p>
			<div class="big big-indents_ruler"></div>
			<p>Select the necessary paragraph(s) and drag the indent markers along the ruler.</p>
			<ul>
				<li><b>First Line Indent</b> marker <div class="icon icon-firstline_indent"></div> is used to set the offset from the left internal margin of the text box for the first line of the paragraph.</li>
				<li><b>Hanging Indent</b> marker <div class="icon icon-hanging"></div> is used to set the offset from the left internal margin of the text box for the second and all the subsequent lines of the paragraph.</li>
				<li><b>Left Indent</b> marker <div class="icon icon-leftindent"></div> is used to set the entire paragraph offset from the left internal margin of the text box.</li>
				<li><b>Right Indent</b> marker <div class="icon icon-right_indent"></div> is used to set the paragraph offset from the right internal margin of the text box.</li>
			</ul>
			<p class="note"><b>Note</b>: if you don't see the rulers, switch to the <b>Home</b> tab of the top toolbar, click the <b>View settings</b> <span class="icon icon-viewsettingsicon"></span> icon at the upper right corner, and uncheck the <b>Hide Rulers</b> option to display them.</p>
			<img alt="Paragraph Properties - Font tab" src="../images/textadvancedsettings2.png" />
			<p>The <b>Font</b> tab contains the following parameters:</p>
			<ul>
				<li><b>Strikethrough</b> is used to make the text struck out with a line going through the letters.</li>
				<li><b>Double strikethrough</b> is used to make the text struck out with a double line going through the letters.</li>
				<li><b>Superscript</b> is used to make the text smaller placing it in the upper part of the text line, e.g. as in fractions.</li>
				<li><b>Subscript</b> is used to make the text smaller placing it in the lower part of the text line, e.g. as in chemical formulas.</li>
				<li><b>Small caps</b> is used to make all letters lowercase.</li>
				<li><b>All caps</b> is used to make all letters upper case.</li>
				<li>
					<b>Character Spacing</b> is used to set the space between the characters. Increase the default value to apply the <b>Expanded</b> spacing, or decrease the default value to apply the <b>Condensed</b> spacing. Use the arrow buttons or enter the necessary value in the box.
					<p>All the changes will be displayed in the preview field below.</p>
				</li>
			</ul>
			<img alt="Paragraph Properties - Tab tab" src="../images/textadvancedsettings3.png" />
			<p>The <b>Tab</b> tab allows you to change tab stops i.e. the position the cursor advances to when you press the <b>Tab</b> key.</p>
			<ul>
				<li><b>Default Tab</b> is set at 2.54 cm. You can decrease or increase this value using the arrow buttons or enter the necessary one in the box.</li>
				<li><b>Tab Position</b> - is used to set custom tab stops. Enter the necessary value in this box, adjust it more precisely using the arrow buttons, and press the <b>Specify</b> button. Your custom tab position will be added to the list in the field below.</li>
				<li>
					<b>Alignment</b> - is used to set the necessary alignment type for each of the tab positions in the list above. Select the necessary tab position in the list, choose the <b>Left</b>, <b>Center</b>, or <b>Right</b> option from the <b>Alignment</b> drop-down list, and press the <b>Specify</b> button.
					<ul>
						<li><b>Left</b> - lines up your text on the left side at the tab stop position; the text moves to the right from the tab stop as you type. Such a tab stop will be indicated on the horizontal ruler by the <div class="icon icon-tabstopleft_marker"></div> marker.</li>
						<li><b>Center</b> - centers the text at the tab stop position. Such a tab stop will be indicated on the horizontal ruler by the <div class="icon icon-tabstopcenter_marker"></div> marker.</li>
						<li><b>Right</b> - lines up your text on the right side at the tab stop position; the text moves to the left from the tab stop as you type. Such a tab stop will be indicated on the horizontal ruler by the <div class="icon icon-tabstopright_marker"></div> marker.</li>
					</ul>
					<p>To delete tab stops from the list, select a tab stop and press the <b>Remove</b> or <b>Remove All</b> button.</p>
				</li>
			</ul>
			<p>To set tab stops, you can also use the horizontal ruler:</p>
			<ol>
				<li>Click the tab selector button <div class="icon icon-tabstopleft"></div> in the upper left corner of the working area to choose the necessary tab stop type: <b>Left</b> <div class="icon icon-tabstopleft"></div>, <b>Center</b> <div class="icon icon-tabstopcenter"></div>, <b>Right</b> <div class="icon icon-tabstopright"></div>.</li>
				<li>
					Click on the bottom edge of the ruler where you want to place the tab stop. Drag it along the ruler to change its position. To remove the added tab stop, drag it out of the ruler.
					<p><div class="big big-tabstops_ruler"></div></p>
					<p class="note"><b>Note</b>: if you don't see the rulers, switch to the <b>Home</b> tab of the top toolbar, click the <b>View settings</b> <span class="icon icon-viewsettingsicon"></span> icon at the upper right corner, and uncheck the <b>Hide Rulers</b> option to display them.</p>
				</li>
			</ol>
			<h2>Edit a Text Art style</h2>
			<p>Select a text object and click the <b>Text Art settings</b> <span class="icon icon-textart_settings_icon"></span> icon on the right sidebar.</p>
			<p><img alt="Text Art setting tab" src="../images/right_textart.png" /></p>
			<ul>
				<li>Change the applied text style by selecting a new <b>Template</b> from the gallery. You can also change the basic style additionally by selecting a different font type, size, etc.</li>
				<li>Change the font <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">fill</a> and <a href="../UsageInstructions/InsertAutoshapes.htm#shapestroke" onclick="onhyperlinkclick(this)">line</a>. The available options are the same as the ones for autoshapes.</li>
				<li>Apply a text effect by selecting the necessary text transformation type from the <b>Transform</b> gallery. You can adjust the degree of text distortion by dragging the pink diamond-shaped handle.</li>
			</ul>
			<p><img alt="Text Art Transformation" src="../images/textart_transformation.png" /></p>
		</div>
	</body>
</html>