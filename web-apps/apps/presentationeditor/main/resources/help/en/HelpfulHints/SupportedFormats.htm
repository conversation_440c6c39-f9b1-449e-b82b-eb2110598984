﻿<!DOCTYPE html>
<html>
	<head>
		<title>Supported Formats of Electronic Presentations</title>
		<meta charset="utf-8" />
		<meta name="description" content="The list of presentation formats supported by Presentation Editor" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Supported Formats of Electronic Presentation</h1>
			<p>A presentation is a set of slides that may include different types of content such as images, media files, text, effects, etc. 
			The <a target="_blank" href="https://www.onlyoffice.com/en/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a> handles the following presentation formats.</p>
            <p class="note">While uploading or opening the file for editing, it will be converted to the Office Open XML (PPTX) format. It's done to speed up the file processing and increase the interoperability.</p>
            <p>The following table contains the formats which can be opened for viewing and/or editing.</p>
            <table>
                <tr>
                    <td><b>Formats</b></td>
                    <td><b>Description</b></td>
                    <td>View natively</td>
                    <td>View after conversion to OOXML</td>
                    <td>Edit natively</td>
                    <td>Edit after conversion to OOXML</td>
                </tr>
                <tr>
                    <td>DPS</td>
                    <td>WPS Office Presentation Document by Kingsoft<br />A slideshow presentation format included in the WPS Office suite that contains a collection of slides, which may consist of a title slide followed by content slides that may include text, images, video, audio, and shapes.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>DPT</td>
                    <td>Kingsoft Presentation Template <br />A file format for saving the default layout and styles for a presentation and for creating presentations (.DPS files) with the same formatting.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>FODP</td>
                    <td>OpenDocument Flat XML Presentation Format <br />A file format for storing saved presentation in the OpenDocument XML file format without any compression.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>ODP</td>
                    <td>OpenDocument Presentation<br />File format that represents presentations created by Impress application, which is a part of OpenOffice based office suites.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>OTP</td>
                    <td>OpenDocument Presentation Template<br />OpenDocument file format for presentation templates. An OTP template contains formatting settings, styles, etc. and can be used to create multiple presentations with the same formatting.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>POT</td>
                    <td>Portable object template <br /> A file format for creating slide show presentations that contain the default layout, formatting, and styles for a slide show. POT files are used to create multiple .PPT files with the same formatting.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>POTM</td>
                    <td>A macro-enabled presentation template by Microsoft PowerPoint<br /> A file format for creating slide show presentations that contain default images, slide templates, macros, and formatting.</td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                </tr>
                <tr>
                    <td>POTX</td>
                    <td>PowerPoint Open XML Document Template<br />Zipped, XML-based file format developed by Microsoft for presentation templates. A POTX template contains formatting settings, styles, etc. and can be used to create multiple presentations with the same formatting.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>PPS</td>
                    <td>PowerPoint Slideshow File<br />A file format format that is used in the preparation of slideshows and presentations, to store a variety of data such as video, audio, text, animations, and pictures.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>PPSM</td>
                    <td>Macro-enabled slide show by Microsoft PowerPoint<br />A file format that is used to create slide shows and presentations and contains one or more macro-enabled slides, which may include text, images, and transitions.</td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                </tr>
                <tr>
                    <td>PPSX</td>
                    <td>Microsoft PowerPoint Slide Show<br />Presentation file format used for slide show playback.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>PPT</td>
                    <td>File format used by Microsoft PowerPoint</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>PPTX</td>
                    <td>Office Open XML Presentation<br />Zipped, XML-based file format developed by Microsoft for representing spreadsheets, charts, presentations, and word processing documents.</td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                </tr>
                <tr>
                    <td>SXI</td>
                    <td>StarOffice Impress Presentation<br />A presentation format used for creating slideshow presentations that may consist of title and content slides with text and digital objects; supports audio, video, and transition effects.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
            </table>
            <p>The following table contains the formats in which you can download a presentation from the <b>File</b> -> <b>Download as</b> menu.</p>
            <table>
                <tr>
                    <td><b>Input format</b></td>
                    <td><b>Can be downloaded as</b></td>
                </tr>
                <tr>
                    <td>DPS</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTM, POTX, PPSM, PPSX, PPTM, PPTX</td>
                </tr>
                <tr>
                    <td>DPT</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTM, POTX, PPSM, PPSX, PPTM, PPTX</td>
                </tr>
                <tr>
                    <td>FODP</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTM, POTX, PPSM, PPSX, PPTM, PPTX</td>
                </tr>
                <tr>
                    <td>ODP</td>
                    <td>JPG, OTP, PDF, PDF/A, PNG, POTM, POTX, PPSM, PPSX, PPTM, PPTX</td>
                </tr>
                <tr>
                    <td>OTP</td>
                    <td>JPG, ODP, PDF, PDF/A, PNG, POTM, POTX, PPSM, PPSX, PPTM, PPTX</td>
                </tr>
                <tr>
                    <td>POT</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTM, POTX, PPSM, PPSX, PPTM, PPTX</td>
                </tr>
                <tr>
                    <td>POTM</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSM, PPSX, PPTM, PPTX</td>
                </tr>
                <tr>
                    <td>POTX</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, PPSM, PPSX, PPTM, PPTX</td>
                </tr>
                <tr>
                    <td>PPS</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTM, POTX, PPSM, PPSX, PPTM, PPTX</td>
                </tr>
                <tr>
                    <td>PPSM</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTM, POTX, PPSX, PPTM, PPTX</td>
                </tr>
                <tr>
                    <td>PPSX</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTM, POTX, PPSM, PPTM, PPTX</td>
                </tr>
                <tr>
                    <td>PPT</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSM, PPSX, PPTM, PPTX</td>
                </tr>
                <tr>
                    <td>PPTX</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSM, PPSX, PPTM</td>
                </tr>
                <tr>
                    <td>SXI</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSM, PPSX, PPTM, PPTX</td>
                </tr>
            </table>
            <p>You can also refer to the conversion matrix on <a href="https://api.onlyoffice.com/docs/docs-api/additional-api/conversion-api/conversion-tables/#presentation-file-formats" target="_blank" onclick="onhyperlinkclick(this)"><b>api.onlyoffice.com</b></a> to see possibility of conversion your presentations into the most known file formats.</p>
		</div>
	</body>
</html>