<!DOCTYPE html>
<html>
	<head>
		<title>Параметры представления и инструменты навигации</title>
		<meta charset="utf-8" />
		<meta name="description" content="Описание параметров представления и инструментов навигации, таких как масштаб, кнопки предыдущего/следующего слайда" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Параметры представления и инструменты навигации</h1>
			<p>В <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Редакторе презентаций</b></a> доступен ряд инструментов, позволяющих облегчить просмотр и навигацию по презентации: масштаб, кнопки перехода на предыдущий/следующий слайд, указатель номера слайда.</p>
			<h3>Настройте параметры представления</h3>
			<p>Чтобы настроить стандартные параметры представления и установить наиболее удобный режим работы с презентацией, перейдите на вкладку <a href="../ProgramInterface/ViewTab.htm" onclick="onhyperlinkclick(this)">Вид</a>.
			Можно выбрать следующие опции:
			</p>
			<ul>
				<li><b>Масштаб</b> – чтобы выбрать из выпадающего списка нужное значение масштаба от 50% до 500%.</li>
				<li><b>По размеру слайда</b> - чтобы весь слайд целиком помещался в видимой части рабочей области.</li>
				<li><b>По ширине</b> - чтобы ширина слайда соответствовала видимой части рабочей области.</li>
				<li><b>Тема интерфейса</b> – выберите из выпадающего меню одну из доступных тем интерфейса: <em>Системная</em>, <em>Светлая</em>, <em>Классическая светлая</em>, <em>Темная</em>, <em>Контрастная темная</em>.</li>
				<li><b>Заметки</b> - когда эта опция отключена, будет скрыта секция заметки, которая находится под слайдом. Данную секцию также можно скрыть/показать, перетащив её курсором мыши.</li>
				<li><b>Линейки</b> - когда эта опция отключена, будут скрыты линейки, которые используются для становки позиций табуляции и отступов абзацев внутри текстовых полей. Чтобы отобразить скрытые <b>линейки</b>, щелкните по этой опции еще раз.</li>
				<li><b>Направляющие</b> – выберите нужный тип направляющих для правильного позиционирования объектов на слайде. Доступны следующие варианты: <em>вертикальные</em>, <em>горизонтальные</em> и <em>смарт-направляющие</em> для лучшего позиционирования.</li>
				<li><b>Линии сетки</b> – выберите нужный <em>размер</em> сетки из доступных шаблонов или задайте <em>пользовательский</em> размер, и укажите, надо ли <em>привязать объекты к сетке</em>, для лучшего позиционирования объектов.</li>
				<li>
					<b>Всегда показывать панель инструментов</b> – когда эта опция отключена, будет скрыта верхняя панель инструментов, которая содержит команды. Названия вкладок при этом остаются видимыми.
					<p class="note">Можно также дважды щелкнуть по любой вкладке, чтобы скрыть верхнюю панель инструментов или отобразить ее снова.</p>
				</li>
				<li><b>Строка состояния</b> - когда эта опция отключена, будет скрыта самая нижняя панель, на которой находится <b>Указатель номера слайда</b> и кнопки <b>Масштаба</b>. Чтобы отобразить скрытую <b>строку состояния</b>, щелкните по этой опции еще раз.</li>
				<li><b>Левая панель</b> - когда эта опция отключена, будет скрыта левая панель, на которой расположены кнопки <b>Поиск</b>, <b>Комментарии</b> и т. д. Чтобы отобразить скрытую <b>Левую панель</b>, щелкните по этой опции еще раз.</li>
				<li><b>Правая панель</b> - когда эта опция отключена, будет скрыта правая панель, на которой расположены <b>Параметры</b>. Чтобы отобразить скрытую <b>Правую панель</b>, щелкните по этой опции еще раз.</li>
			</ul>			
			<p>Правая боковая панель свернута по умолчанию. Чтобы ее развернуть, выделите любой объект или слайд и щелкните по значку вкладки, которая в данный момент активирована (чтобы свернуть правую боковую панель, щелкните по этому значку еще раз). 
			Левую боковую панель можно настроить путем простого перетаскивания: наведите курсор мыши на край левой боковой панели, чтобы курсор превратился в двунаправленную стрелку, и перетащите край панели влево, чтобы уменьшить ширину панели, или вправо, чтобы ее увеличить.</p>
			<h3>Используйте инструменты навигации</h3>
			<p>Для осуществления навигации по презентации используйте следующие инструменты:</p>			
			<p>
				Кнопки <b>Масштаб</b> расположены в правом нижнем углу и используются для увеличения и уменьшения масштаба текущей презентации.
				Чтобы изменить выбранное в текущий момент значение масштаба в процентах, щелкните по нему и выберите в списке один из доступных параметров масштабирования (50% / 75% / 100% / 125% / 150% / 175% / 200% / 300% / 400% / 500%) или используйте кнопки
				<b>Увеличить</b> <span class="icon icon-zoomin"></span> или <b>Уменьшить</b> <span class="icon icon-zoomout"></span>.
				Выбранный масштаб сохраняется для всех файлов на протяжении текущей сессии.
				Щелкните по значку <b>По ширине</b> <span class="icon icon-fitwidth"></span>, чтобы ширина слайда соответствовала видимой части рабочей области.
				Чтобы весь слайд целиком помещался в видимой части рабочей области, нажмите значок <b>По размеру слайда</b> <span class="icon icon-fitslide"></span>.
				Параметры масштаба доступны также на вкладкe <a href="../ProgramInterface/ViewTab.htm" onclick="onhyperlinkclick(this)">Вид</a>.
			</p>
			<p class="note">Вы можете установить значение масштаба по умолчанию. На вкладке <b>Файл</b> верхней панели инструментов перейдите в раздел <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)"><b>Дополнительные параметры</b></a>, выберите из списка нужное <b>Стандартное значение масштаба</b> и нажмите кнопку <b>Применить</b>. Чтобы использовать ранее выбранный масштаб, прокрутите список и выберите опцию <b>Последний использованый</b>.</p>
			<p>Для перехода на предыдущий или следующий слайд в ходе редактирования презентации можно использовать кнопки <span class="icon icon-previouspage"></span> и <span class="icon icon-nextpage"></span>, расположенные сверху и снизу вертикальной полосы прокрутки, которая находится справа от слайда.</p>
			<p><b>Указатель номера слайда</b> показывает текущий слайд в составе всех слайдов текущей презентации (слайд 'n' из 'nn'). 
			Щелкните по этой надписи, чтобы открыть окно, в котором можно ввести номер нужного слайда и быстро перейти к нему. Если Вы решите скрыть <b>строку состояния</b>, этот инструмент станет недоступен.</p>
		</div>
	</body>
</html>