<!DOCTYPE html>
<html>
	<head>
		<title>О редакторе презентаций</title>
		<meta charset="utf-8" />
        <meta name="description" content="Краткое описание редактора презентаций" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>О редакторе презентаций</h1>
			<p><b>Редактор презентаций</b> - это <span class="onlineDocumentFeatures">онлайн-</span>приложение, которое позволяет просматривать и редактировать презентации<span class="onlineDocumentFeatures"> непосредственно в браузере</span>.</p>
			<p>Используя <b><span class="onlineDocumentFeatures">онлайн-</span>редактор презентаций</b>, можно выполнять различные операции редактирования, как в любом десктопном редакторе, распечатывать отредактированные презентации,
			 сохраняя все детали форматирования, или сохранять их на жесткий диск компьютера как файлы в формате PPTX, PDF, ODP, POTX, PDF/A, OTP.</p>
            <p> <span class="onlineDocumentFeatures">Для просмотра текущей версии программы, номера сборки и информации о владельце лицензии в <em>онлайн-версии</em> щелкните по значку <span class="icon icon-about"></span> на левой боковой панели инструментов.</span> <span class="desktopDocumentFeatures"> Для просмотра текущей версии программы и информации о владельце лицензии в <em>десктопной версии</em> для Windows выберите пункт меню <b>О программе</b> на левой боковой панели в главном окне приложения. В <em>десктопной версии</em> для Mac OS откройте меню <b>ONLYOFFICE</b> в верхней части и выберите пункт меню <b>О программе ONLYOFFICE</b>.</span></p>
    </div>
	</body>
</html>