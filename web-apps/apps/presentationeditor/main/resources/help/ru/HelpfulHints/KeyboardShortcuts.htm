<!DOCTYPE html>
<html>
	<head>
		<title>Сочетания клавиш</title>
		<meta charset="utf-8" />
		<meta name="description" content="Список сочетаний клавиш, используемых для быстрого и легкого доступа к функциям онлайн-редактора презентаций с помощью клавиатуры." />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/jquery.min.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/keyboard-switch.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Сочетания клавиш</h1>
            <h3>Подсказки для клавиш</h3>
            <p>Используйте <b>сочетания клавиш</b> для более быстрого и удобного доступа к функциям <a href="https://www.onlyoffice.com/ru/presentation-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактора презентаций</b></a> без использования мыши.</p>
            <ol>
                <li>Нажмите клавишу <b>Alt</b> (<b>Option</b> для macOS), чтобы показать все подсказки для клавиш верхней панели инструментов, правой и левой боковой панели, а также строке состояния.</li>
                <li>
                    Нажмите клавишу, соответствующую элементу, который вы хотите использовать. В зависимости от нажатой клавиши, могут появляться дополнительные подсказки. Когда появляются дополнительные подсказки для клавиш, первичные - скрываются.
                    <p>Например, чтобы открыть вкладку <b>Вставка</b>, нажмите <b>Alt</b> (<b>Option</b> для macOS) и просмотрите все подсказки по первичным клавишам.</p>
                    <p><img alt="Первичные подсказки для клавиш" src="../images/keytips1.png" /></p>
                    <p>Нажмите букву <b>И</b>, чтобы открыть вкладку <b>Вставка</b> и просмотреть все доступные сочетания клавиш для этой вкладки.</p>
                    <p><img alt="Дополнительные подсказки для клавиш" src="../images/keytips2.png" /></p>
                    <p>Затем нажмите букву, соответствующую элементу, который вы хотите использовать.</p>
                </li>
                <li>Нажмите <b>Alt</b> (<b>Option</b> для macOS), чтобы скрыть все подсказки для клавиш, или <b>Escape</b>, чтобы вернуться к предыдущей группе подсказок для клавиш.</li>
            </ol>
            <p>В списке ниже приведены наиболее распространенные сочетания клавиш.</p>
            <p class="note">Пожалуйста, обратите внимание: для macOS некоторые сочетания клавиш содержат клавиши <kbd>Home</kbd>, <kbd>End</kbd>, <kbd>Page Up</kbd> и <kbd>Page Down</kbd>, которые есть только на расширенной клавиатуре. Ecли у вас нет этих клавиш, используйте сочетания клавиш, указанные выше (то есть используйте <kbd>^ Ctrl</kbd>/<kbd>&#8984; Cmd</kbd>+<kbd>←</kbd> или <kbd>Fn</kbd>+<kbd>←</kbd> вместо <kbd>Home</kbd>, <kbd>^ Ctrl</kbd>/<kbd>&#8984; Cmd</kbd>+<kbd>→</kbd> или <kbd>Fn</kbd>+<kbd>→</kbd> вместо <kbd>End</kbd>, <kbd>Fn</kbd>+<kbd>↑</kbd> вместо <kbd>Page Up</kbd>, <kbd>Fn</kbd>+<kbd>↓</kbd> вместо <kbd>Page Down</kbd>).</p>
            <ul class="shortcut_variants">
                <li class="shortcut_toggle pc_option left_option">Windows/Linux</li>
                <!--
        -->
                <li class="shortcut_toggle mac_option right_option">Mac OS</li>
            </ul>
            <table class="keyboard_shortcuts_table">
                <tr>
                    <th colspan="4" class="keyboard_section">Работа с презентацией</th>
                </tr>
                <tr>
                    <td>Открыть панель 'Файл'</td>
                    <td><kbd>Alt</kbd>+<kbd>F</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>⌥ Option</kbd>+<kbd>F</kbd></td>
                    <td>Открыть панель <b>Файл</b>, чтобы сохранить, скачать, распечатать текущую презентацию, просмотреть сведения о ней, создать новую презентацию или открыть существующую, получить доступ к Справке по онлайн-редактору презентаций или дополнительным параметрам.</td>
                </tr>
                <tr>
                    <td>Открыть окно 'Поиск'</td>
                    <td><kbd>Ctrl</kbd>+<kbd>F</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>F</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>F</kbd></td>
                    <td>Открыть диалоговое окно <b>Поиск</b>, чтобы начать поиск символа/слова/фразы в редактируемой презентации.</td>
                </tr>
                <tr>
                    <td>Открыть меню (панель) 'Поиск и замена' с полем замены</td>
                    <td><kbd>Ctrl</kbd>+<kbd>H</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>H</kbd></td>
                    <td>Открыть меню (панель) <b>Поиск и замена</b> с полем замены, чтобы заменить один или более найденных символов.</td>
                </tr>
                <tr>
                    <td>Открыть панель 'Комментарии'</td>
                    <td><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>H</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>H</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>H</kbd></td>
                    <td>Открыть панель <b>Комментарии</b>, чтобы добавить свой комментарий или ответить на комментарии других пользователей.</td>
                </tr>
                <tr>
                    <td>Открыть поле комментария</td>
                    <td><kbd>Alt</kbd>+<kbd>H</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>⌥ Option</kbd>+<kbd>A</kbd></td>
                    <td>Открыть поле ввода данных, в котором можно добавить текст комментария.</td>
                </tr>
                <tr class="onlineDocumentFeatures">
                    <td>Открыть панель 'Чат' (Онлайн-редакторы)</td>
                    <td><kbd>Alt</kbd>+<kbd>Q</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>⌥ Option</kbd>+<kbd>Q</kbd></td>
                    <td>Открыть панель <b>Чат</b> в <b>онлайн-редакторах</b> и отправить сообщение.</td>
                </tr>
                <tr>
                    <td>Сохранить презентацию</td>
                    <td><kbd>Ctrl</kbd>+<kbd>S</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>S</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>S</kbd></td>
                    <td>Сохранить все изменения в редактируемой презентации. Активный файл будет сохранен с текущим именем, в том же местоположении и формате.</td>
                </tr>
                <tr>
                    <td>Печать презентации</td>
                    <td><kbd>Ctrl</kbd>+<kbd>P</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>P</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>P</kbd></td>
                    <td>Распечатать презентацию на одном из доступных принтеров или сохранить в файл.</td>
                </tr>
                <tr>
                    <td>Скачать как...</td>
                    <td><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>S</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>S</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>S</kbd></td>
                    <td>Открыть панель <b>Скачать как...</b>, чтобы сохранить редактируемую презентацию на жестком диске компьютера в одном из поддерживаемых форматов: PPTX, PPSX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG</td>
                </tr>
                <tr>
                    <td>Полноэкранный режим (Онлайн-редакторы)</td>
                    <td><kbd>F11</kbd></td>
                    <td></td>
                    <td>Переключиться в полноэкранный режим в <b>онлайн-редакторах</b>, чтобы развернуть редактор презентаций на весь экран.</td>
                </tr>
                <tr>
                    <td>Вызов справки</td>
                    <td><kbd>F1</kbd></td>
                    <td><kbd>Fn</kbd>+<kbd>F1</kbd></td>
                    <td>Открыть меню <b>Справка</b> редактора презентаций.</td>
                </tr>
                <tr>
                    <td>Открыть существующий файл</td>
                    <td><kbd>Ctrl</kbd>+<kbd>O</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>O</kbd></td>
                    <td>Открыть стандартное диалоговое окно для выбора существующего файла. Если выбрать файл в этом диалоговом окне и нажать кнопку Открыть, файл откроется в новой вкладке или в окне <b>десктопных редакторов</b>.</td>
                </tr>
                <tr>
                    <td>Перейти к следующей вкладке</td>
                    <td><kbd>Ctrl</kbd>+<kbd>↹ Tab</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>↹ Tab</kbd></td>
                    <td>Перейти к следующей вкладке файла в <b>десктопных редакторах</b> или вкладке браузера в <b>онлайн-редакторах</b>.</td>
                </tr>
                <tr>
                    <td>Перейти к предыдущей вкладке</td>
                    <td><kbd>Ctrl</kbd>+<kbd>Shift</kbd>+<kbd>↹ Tab</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>↹ Tab</kbd></td>
                    <td>Перейти к предыдущей вкладке файла в <b>десктопных редакторах</b> или вкладке браузера в <b>онлайн-редакторах</b>.</td>
                </tr>
                <tr>
                    <td>Закрыть файл</td>
                    <td><kbd>Ctrl</kbd>+<kbd>W</kbd>,<br /><kbd>Ctrl</kbd>+<kbd>F4</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>W</kbd></td>
                    <td>Закрыть выбранную презентацию.</td>
                </tr>
                <tr>
                    <td>Контекстное меню элемента</td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>F10</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>Fn</kbd>+<kbd>F10</kbd></td>
                    <td>Открыть <b>контекстное меню</b> выбранного элемента.</td>
                </tr>
                <tr>
                    <td>Закрыть меню или модальное окно, сбросить режим и др.</td>
                    <td><kbd>Esc</kbd></td>
                    <td><kbd>Esc</kbd></td>
                    <td>Закрыть меню или модальное окно. Сбросить режим добавления фигур. Убрать курсор из контента фигуры. Убрать выделение пошагово (например, если выделен контент фигуры внутри группы, сначала будет убран курсор из контента, потом с фигуры, потом с группы). Отменить выделение копируемого формата. Сбросить перетаскивание текста. Сбросить режим выделения маркером.</td>
                </tr>
                <tr>
                    <td>Сбросить масштаб</td>
                    <td><kbd>Ctrl</kbd>+<kbd>0</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>0</kbd> или <kbd>&#8984; Cmd</kbd>+<kbd>0</kbd></td>
                    <td>Сбросить масштаб текущей презентации до значения по умолчанию 'По размеру слайда'.</td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section">Навигация</th>
                </tr>
                <tr>
                    <td>Первый слайд</td>
                    <td><kbd>Home</kbd></td>
                    <td><kbd>Fn</kbd>+<kbd>←</kbd><br /><br /><kbd>Home</kbd></td>
                    <td>Перейти к первому слайду редактируемой презентации/первой миниатюре в списке миниатюр.</td>
                </tr>
                <tr>
                    <td>Последний слайд</td>
                    <td><kbd>End</kbd></td>
                    <td><kbd>Fn</kbd>+<kbd>→</kbd><br /><br /><kbd>End</kbd></td>
                    <td>Перейти к последнему слайду редактируемой презентации/последней миниатюре в списке миниатюр.</td>
                </tr>
                <tr>
                    <td>Следующий слайд</td>
                    <td><kbd>Page Down</kbd>,<br /><kbd>↓</kbd>,<br /><kbd>→</kbd></td>
                    <td><kbd>↓</kbd>,<br /><kbd>Fn</kbd>+<kbd>↓</kbd><br /><br /><kbd>↓</kbd>,<br /><kbd>Page Down</kbd></td>
                    <td>Перейти к следующему слайду редактируемой презентации/следующей миниатюре в списке миниатюр.</td>
                </tr>
                <tr>
                    <td>Предыдущий слайд</td>
                    <td><kbd>Page Up</kbd>,<br /><kbd>↑</kbd>,<br /><kbd>←</kbd></td>
                    <td><kbd>↑</kbd>,<br /><kbd>Fn</kbd>+<kbd>↑</kbd><br /><br /><kbd>↑</kbd>,<br /><kbd>Page Up</kbd></td>
                    <td>Перейти к предыдущему слайду редактируемой презентации/предыдущей миниатюре в списке миниатюр.</td>
                </tr>
                <tr>
                    <td>Увеличить масштаб</td>
                    <td><kbd>Ctrl</kbd>+<kbd>+</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>=</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>=</kbd></td>
                    <td>Увеличить масштаб редактируемой презентации.</td>
                </tr>
                <tr>
                    <td>Уменьшить масштаб</td>
                    <td><kbd>Ctrl</kbd>+<kbd>-</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>-</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>-</kbd></td>
                    <td>Уменьшить масштаб редактируемой презентации.</td>
                </tr>
                <tr>
                    <td>Перейти между элементами управления</td>
                    <td><kbd>↹ Tab</kbd>/<kbd>⇧ Shift</kbd>+<kbd>↹ Tab</kbd></td>
                    <td><kbd>↹ Tab</kbd>/<kbd>⇧ Shift</kbd>+<kbd>↹ Tab</kbd></td>
                    <td>Перейти на следующий или предыдущий элемент управления в модальных окнах.</td>
                </tr>
                <tr id="manageslides">
                    <th colspan="4" class="keyboard_section">Выполнение действий со слайдами</th>
                </tr>
                <tr>
                    <td>Новый слайд</td>
                    <td><kbd>Ctrl</kbd>+<kbd>M</kbd>,<br /><kbd>Enter</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>M</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>M</kbd>,<br /><kbd>Return</kbd></td>
                    <td>Создать новый слайд и добавить его после выделенного в списке слайдов. Сочетание <kbd>Ctrl</kbd>+<kbd>M</kbd> также позволяет создать первый слайд, если в презентации нет слайдов.</td>
                </tr>
                <tr>
                    <td>Удалить слайд</td>
                    <td><kbd>Delete</kbd>,<br /><kbd>Backspace</kbd></td>
                    <td><kbd>Delete</kbd>,<br /><kbd>Fn</kbd>+<kbd>Delete</kbd></td>
                    <td>Удалить выделенный в списке слайд или несколько выделенных слайдов.</td>
                </tr>
                <tr>
                    <td>Дублировать слайд</td>
                    <td><kbd>Ctrl</kbd>+<kbd>D</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>D</kbd>,<br /><kbd>^ Ctrl</kbd>+<kbd>D</kbd></td>
                    <td>Дублировать выделенный в списке слайд.</td>
                </tr>
                <tr>
                    <td>Переместить слайд вверх</td>
                    <td><kbd>Ctrl</kbd>+<kbd>↑</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>↑</kbd></td>
                    <td>Поместить выделенный слайд или несколько выделенных слайдов над предыдущим в списке (когда фокус на миниатюрах).</td>
                </tr>
                <tr>
                    <td>Переместить слайд вниз</td>
                    <td><kbd>Ctrl</kbd>+<kbd>↓</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>↓</kbd></td>
                    <td>Поместить выделенный слайд или несколько выделенных слайдов под последующим в списке (когда фокус на миниатюрах).</td>
                </tr>
                <tr>
                    <td>Переместить слайд в начало</td>
                    <td><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>↑</kbd>,<br /><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>Page Up</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>↑</kbd>,<br /><kbd>^ Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>↑</kbd><br /><br /><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>Page Up</kbd>,<br /><kbd>^ Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>Page Up</kbd></td>
                    <td>Переместить выделенный слайд или несколько слайдов в самое начало списка (когда фокус на миниатюрах).</td>
                </tr>
                <tr>
                    <td>Переместить слайд в конец</td>
                    <td><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>↓</kbd>,<br /><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>Page Down</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>↓</kbd>,<br /><kbd>^ Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>↓</kbd><br /><br /><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>Page Down</kbd>,<br /><kbd>^ Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>Page Down</kbd></td>
                    <td>Переместить выделенный слайд или несколько слайдов в самый конец списка (когда фокус на миниатюрах).</td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section"><a id="workwithobjects"></a>Выполнение действий с объектами</th>
                </tr>
                <tr>
                    <td>Работа с фигурами</td>
                    <td><kbd>↵ Enter</kbd></td>
                    <td><kbd>↵ Return</kbd></td>
                    <td>Выделение на фигуре: если нет контента, создать его и перейти в начало строки. Если контент пустой, перейти в него, если нет, то выделить все внутри него.</td>
                </tr>
                <tr>
                    <td>Работа с диаграммами</td>
                    <td><kbd>↵ Enter</kbd></td>
                    <td><kbd>↵ Return</kbd></td>
                    <td>Выделение на заголовке диаграммы: если заголовок пустой, то перейти в его начало, если нет, то выделить текст заголовка.</td>
                </tr>
                <tr>
                    <td>Создать копию при перетаскивании</td>
                    <td><kbd>Ctrl</kbd></td>
                    <td><kbd>^ Ctrl</kbd></td>
                    <td>Выделите объект и удерживайте указанную клавишу при перетаскивании объекта, чтобы создать копию объекта в месте переноса.</td>
                </tr>
                <tr>
                    <td>Создать копию</td>
                    <td><kbd>Ctrl</kbd>+<kbd>D</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>D</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>D</kbd></td>
                    <td>Выделите объект и нажмите указанное сочетание клавиш, чтобы создать копию объекта рядом с выделенным объектом.</td>
                </tr>
                <tr>
                    <td>Сгруппировать</td>
                    <td><kbd>Ctrl</kbd>+<kbd>G</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>G</kbd>,<br /><kbd>^ Ctrl</kbd>+<kbd>G</kbd></td>
                    <td>Сгруппировать выделенные объекты.</td>
                </tr>
                <tr>
                    <td>Разгруппировать</td>
                    <td><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>G</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>G</kbd>,<br /><kbd>^ Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>G</kbd></td>
                    <td>Разгруппировать выбранную группу объектов.</td>
                </tr>
                <tr>
                    <td>Переместить фокус на следующий объект</td>
                    <td><kbd>↹ Tab</kbd></td>
                    <td><kbd>↹ Tab</kbd></td>
                    <td>Переместить фокус на следующий объект после выбранного в данный момент.</td>
                </tr>
                <tr>
                    <td>Переместить фокус на предыдущий объект</td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>↹ Tab</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>↹ Tab</kbd></td>
                    <td>Переместить фокус на предыдущий объект перед выбранным в данный момент.</td>
                </tr>
                <tr>
                    <td>Изменить угол расположения линии/стрелки в процессе рисования</td>
                    <td><kbd>⇧ Shift</kbd> + перетаскивание (при рисовании линий или стрелок)</td>
                    <td><kbd>⇧ Shift</kbd> + перетаскивание (при рисовании линий или стрелок)</td>
                    <td>Удерживайте клавишу <kbd>Shift</kbd> в процессе рисования линии/стрелки и поворачивайте носик стрелки/конец линии для изменения угла линии/стрелки при ее рисовании. Поворот линии/стрелки будет осуществлен ровно на 45 градусов.</td>
                </tr>
                <tr>
                    <td>Попиксельное перемещение</td>
                    <td><kbd>Ctrl</kbd>+<kbd>←</kbd> <kbd>→</kbd> <kbd>↑</kbd> <kbd>↓</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>←</kbd> <kbd>→</kbd> <kbd>↑</kbd> <kbd>↓</kbd></td>
                    <td>Удерживайте указанную клавишу и используйте стрелки на клавиатуре, чтобы перемещать выбранный объект влево, вправо, вверх или вниз на один пиксель за раз.</td>
                </tr>
                <tr>
                    <td>Перемещение большим шагом</td>
                    <td><kbd>←</kbd> <kbd>→</kbd> <kbd>↑</kbd> <kbd>↓</kbd></td>
                    <td><kbd>←</kbd> <kbd>→</kbd> <kbd>↑</kbd> <kbd>↓</kbd></td>
                    <td>Используйте стрелки на клавиатуре, чтобы перемещать выбранный объект большим шагом влево, вправо, вверх или вниз.</td>
                </tr>   
                <tr>
                    <th colspan="4" class="keyboard_section">Модификация объектов</th>
                </tr>
                <tr>
                    <td>Ограничить движение</td>
                    <td><kbd>⇧ Shift</kbd> + перетаскивание</td>
                    <td><kbd>⇧ Shift</kbd> + перетаскивание</td>
                    <td>Ограничить перемещение выбранного объекта по горизонтали или вертикали.</td>
                </tr>
                <tr>
                    <td>Задать угол поворота в 15 градусов</td>
                    <td><kbd>⇧ Shift</kbd> + перетаскивание (при поворачивании)</td>
                    <td><kbd>⇧ Shift</kbd> + перетаскивание (при поворачивании)</td>
                    <td>Ограничить угол поворота шагом в 15 градусов.</td>
                </tr>
                <tr>
                    <td>Сохранять пропорции</td>
                    <td><kbd>⇧ Shift</kbd> + перетаскивание (при изменении размера)</td>
                    <td><kbd>⇧ Shift</kbd> + перетаскивание (при изменении размера)</td>
                    <td>Сохранять пропорции выбранного объекта при изменении размера.</td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section"><a id="workwithtables"></a>Работа с таблицами</th>
                </tr>
                <tr>
                    <td>Перейти к следующей ячейке в строке</td>
                    <td><kbd>↹ Tab</kbd></td>
                    <td><kbd>↹ Tab</kbd></td>
                    <td>Перейти к следующей ячейке в строке таблицы.</td>
                </tr>
                <tr>
                    <td>Перейти к предыдущей ячейке в строке</td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>↹ Tab</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>↹ Tab</kbd></td>
                    <td>Перейти к предыдущей ячейке в строке таблицы.</td>
                </tr>
                <tr>
                    <td>Перейти к следующей строке</td>
                    <td><kbd>↓</kbd></td>
                    <td><kbd>↓</kbd></td>
                    <td>Перейти к следующей строке таблицы.</td>
                </tr>
                <tr>
                    <td>Перейти к предыдущей строке</td>
                    <td><kbd>↑</kbd></td>
                    <td><kbd>↑</kbd></td>
                    <td>Перейти к предыдущей строке таблицы.</td>
                </tr>
                <tr>
                    <td>Начать новый абзац</td>
                    <td><kbd>↵ Enter</kbd></td>
                    <td><kbd>↵ Return</kbd></td>
                    <td>Начать новый абзац внутри ячейки. Если выделены ячейки, удалить их содержимое. Если выделена таблица, то переместить курсор в первую ячейку. Если она пустая, то переместить курсор в начало, иначе выделить содержимое ячейки.</td>
                </tr>
                <tr>
                    <td>Добавить новую строку</td>
                    <td><kbd>↹ Tab</kbd> в правой нижней ячейке таблицы.</td>
                    <td><kbd>↹ Tab</kbd> в правой нижней ячейке таблицы.</td>
                    <td>Добавить новую строку внизу таблицы.</td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section"><a id="preview"></a>Просмотр презентации</th>
                </tr>
                <tr>
                    <td>Запустить презентацию</td>
                    <td><kbd>Ctrl</kbd>+<kbd>F5</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>↵ Return</kbd></td>
                    <td>Запустить презентацию с начала.</td>
                </tr>
                <tr>
                    <td>Перейти вперед</td>
                    <td><kbd>↵ Enter</kbd>,<br /><kbd>Page Down</kbd>,<br /><kbd>→</kbd>,<br /><kbd>↓</kbd>,<br /><kbd>␣ Spacebar</kbd></td>
                    <td><kbd>↵ Return</kbd>,<br /><kbd>Fn</kbd>+<kbd>↓</kbd>,<br /><kbd>→</kbd>,<br /><kbd>↓</kbd>,<br /><kbd>␣ Spacebar</kbd><br /><br /><kbd>↵ Return</kbd>,<br /><kbd>Page Down</kbd>,<br /><kbd>→</kbd>,<br /><kbd>↓</kbd>,<br /><kbd>␣ Spacebar</kbd></td>
                    <td>Показать следующий эффект перехода или перейти к следующему слайду.</td>
                </tr>
                <tr>
                    <td>Перейти назад</td>
                    <td><kbd>Page Up</kbd>,<br /><kbd>←</kbd>,<br /><kbd>↑</kbd></td>
                    <td><kbd>Fn</kbd>+<kbd>↑</kbd>,<br /><kbd>←</kbd>,<br /><kbd>↑</kbd><br /><br /><kbd>Page Up</kbd>,<br /><kbd>←</kbd>,<br /><kbd>↑</kbd></td>
                    <td>Показать предыдущий эффект перехода или вернуться к предыдущему слайду.</td>
                </tr>
                <tr>
                    <td>Перейти к первому слайду</td>
                    <td><kbd>Home</kbd></td>
                    <td><kbd>Fn</kbd>+<kbd>←</kbd><br /><br /><kbd>Home</kbd></td>
                    <td>Перейти к первому слайду.</td>
                </tr>
                <tr>
                    <td>Перейти к последнему слайду</td>
                    <td><kbd>End</kbd></td>
                    <td><kbd>Fn</kbd>+<kbd>→</kbd><br /><br /><kbd>End</kbd></td>
                    <td>Перейти к последнему слайду.</td>
                </tr>
                <tr>
                    <td>Перейти к указанному слайду</td>
                    <td>номер слайда+<kbd>Enter</kbd></td>
                    <td>номер слайда+<kbd>↵ Return</kbd></td>
                    <td>Перейти к указанному слайду.</td>
                </tr>
                <tr>
                    <td>Закрыть просмотр</td>
                    <td><kbd>Esc</kbd></td>
                    <td><kbd>Esc</kbd></td>
                    <td>Закончить просмотр слайдов. Для веб-версии первое нажатие <kbd>Esc</kbd> - это выход из полноэкранного режима браузера, второе - выход из режима демонстрации.</td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section">Отмена и повтор</th>
                </tr>
                <tr>
                    <td>Отменить</td>
                    <td><kbd>Ctrl</kbd>+<kbd>Z</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>Z</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>Z</kbd></td>
                    <td>Отменить последнее выполненное действие.</td>
                </tr>
                <tr>
                    <td>Повторить</td>
                    <td><kbd>Ctrl</kbd>+<kbd>Y</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>Y</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>Y</kbd></td>
                    <td>Повторить последнее отмененное действие.</td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section">Вырезание, копирование и вставка</th>
                </tr>
                <tr>
                    <td>Вырезать</td>
                    <td><kbd>Ctrl</kbd>+<kbd>X</kbd>,<br /><kbd>⇧ Shift</kbd>+<kbd>Delete</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>X</kbd></td>
                    <td>Вырезать выделенный объект/текст/слайд в списке слайдов и отправить его в буфер обмена компьютера. Вырезанный объект можно затем вставить в другое место этой же презентации<!--, into another presentation, or into some other program-->.</td>
                </tr>
                <tr>
                    <td>Копировать</td>
                    <td><kbd>Ctrl</kbd>+<kbd>C</kbd>,<br /><kbd>Ctrl</kbd>+<kbd>Insert</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>C</kbd></td>
                    <td>Отправить выделенный объект/текст/слайд в списке слайдов в буфер обмена компьютера. Скопированный объект можно затем вставить в другое место этой же презентации<!--, into another presentation, or into some other program-->.</td>
                </tr>
                <tr>
                    <td>Вставить</td>
                    <td><kbd>Ctrl</kbd>+<kbd>V</kbd>,<br /><kbd>⇧ Shift</kbd>+<kbd>Insert</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>V</kbd></td>
                    <td>Вставить ранее скопированный объект/текст/слайд в списке слайдов из буфера обмена компьютера в текущей позиции курсора. Объект может быть ранее скопирован из этой же презентации, другой презентации, другого редактора или другого приложения.</td>
                </tr>
                <tr>
                    <td>Вставить текст без форматирования стиля</td>
                    <td><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>V</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>V</kbd></td>
                    <td>Вставить ранее скопированный фрагмент текста из буфера обмена компьютера в текущую позицию курсора без сохранения исходного форматирования. Текст можно предварительно скопировать из того же документа, из другого документа или из какой-то другой программы</td>
                </tr>
                <tr>
                    <td>Копировать форматирование</td>
                    <td><kbd>Alt</kbd>+<kbd>Ctrl</kbd>+<kbd>C</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>⌥ Option</kbd>+<kbd>C</kbd>,<br /><kbd>^ Ctrl</kbd>+<kbd>⌥ Option</kbd>+<kbd>C</kbd></td>
                    <td>Скопировать форматирование выделенного фрагмента редактируемого текста. Скопированное форматирование можно затем применить к другому тексту в этой же презентации.</td>
                </tr>
                <tr>
                    <td>Применить форматирование</td>
                    <td><kbd>Alt</kbd>+<kbd>Ctrl</kbd>+<kbd>V</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>⌥ Option</kbd>+<kbd>V</kbd>,<br /><kbd>^ Ctrl</kbd>+<kbd>⌥ Option</kbd>+<kbd>V</kbd></td>
                    <td>Применить ранее скопированное форматирование к тексту редактируемого текстового поля.</td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section">Параметры специальной вставки <sup><a href="#PasteSpecial">1</a></sup></th>
                </tr>
                <tr>
                    <td>Использовать конечную тему</td>
                    <td><kbd>Ctrl</kbd> затем <kbd>H</kbd></td>
                    <td><kbd>^ Ctrl</kbd> затем <kbd>H</kbd></td>
                    <td>Применить форматирование, определяемое темой текущей презентации.</td>
                </tr>
                <tr>
                    <td>Сохранить исходное форматирование</td>
                    <td><kbd>Ctrl</kbd> затем <kbd>K</kbd></td>
                    <td><kbd>^ Ctrl</kbd> затем <kbd>K</kbd></td>
                    <td>Сохранить исходное форматирование скопированного текста.</td>
                </tr>
                <tr>
                    <td>Вставить как изображение</td>
                    <td><kbd>Ctrl</kbd> затем <kbd>U</kbd></td>
                    <td><kbd>^ Ctrl</kbd> затем <kbd>U</kbd></td>
                    <td>Вставить текст как изображение, чтобы его нельзя было редактировать.</td>
                </tr>
                <tr>
                    <td>Сохранить только текст</td>
                    <td><kbd>Ctrl</kbd> затем <kbd>T</kbd></td>
                    <td><kbd>^ Ctrl</kbd> затем <kbd>T</kbd></td>
                    <td>Вставить текст без исходного форматирования.</td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section">Работа с гиперссылками</th>
                </tr>
                <tr>
                    <td>Вставить гиперссылку</td>
                    <td><kbd>Ctrl</kbd>+<kbd>K</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>K</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>K</kbd></td>
                    <td>Вставить гиперссылку, которую можно использовать для перехода по веб-адресу или для перехода на определенный слайд этой презентации.</td>
                </tr>
                <tr>
                    <td>Посетить гиперссылку</td>
                    <td><kbd>Enter</kbd></td>
                    <td><kbd>Return</kbd></td>
                    <td>Посетить гиперссылку (курсор находится в гиперссылке).</td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section">Выделение с помощью мыши</th>
                </tr>
                <tr>
                    <td>Добавить в выделенный фрагмент</td>
                    <td><kbd>⇧ Shift</kbd></td>
                    <td><kbd>⇧ Shift</kbd></td>
                    <td>Начните выделение, удерживайте клавишу <kbd>⇧ Shift</kbd> и щелкните там, где требуется закончить выделение.</td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section"><a id="textselection"></a>Выделение с помощью клавиатуры</th>
                </tr>
                <tr>
                    <td>Выделить все</td>
                    <td><kbd>Ctrl</kbd>+<kbd>A</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>A</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>A</kbd></td>
                    <td>Выделить все слайды (в списке слайдов), или все объекты на слайде (в области редактирования слайда), или весь текст (внутри текстового поля) - в зависимости от того, где установлен курсор мыши.</td>
                </tr>
                <tr>
                    <td>Добавить к выделению следующий слайд в списке слайдов</td>
                    <td><kbd>Shift</kbd>+<kbd>Page Down</kbd>,<br /><kbd>⇧ Shift</kbd>+<kbd>↓</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>↓</kbd>,<br /><kbd>⇧ Shift</kbd>+<kbd>Fn</kbd>+<kbd>↓</kbd><br /><br /><kbd>⇧ Shift</kbd>+<kbd>↓</kbd>,<br /><kbd>⇧ Shift</kbd>+<kbd>Page Down</kbd></td>
                    <td>Добавить к выделению следующий слайд в списке слайдов (когда фокус на миниатюрах).</td>
                </tr>
                <tr>
                    <td>Добавить к выделению предыдущий слайд в списке слайдов</td>
                    <td><kbd>Shift</kbd>+<kbd>Page Up</kbd>,<br /><kbd>⇧ Shift</kbd>+<kbd>↑</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>↑</kbd>,<br /><kbd>⇧ Shift</kbd>+<kbd>Fn</kbd>+<kbd>↑</kbd><br /><br /><kbd>⇧ Shift</kbd>+<kbd>↑</kbd>,<br /><kbd>⇧ Shift</kbd>+<kbd>Page Up</kbd></td>
                    <td>Добавить к выделению предыдущий слайд в списке слайдов (когда фокус на миниатюрах).</td>
                </tr>
                <tr>
                    <td>Выделить до первого слайда</td>
                    <td><kbd>Shift</kbd>+<kbd>Home</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>Fn</kbd>+<kbd>←</kbd><br /><br /><kbd>⇧ Shift</kbd>+<kbd>Home</kbd></td>
                    <td>Выделить слайды до первого слайда начиная с текущего слайда, на котором находится фокус в списке миниатюр.</td>
                </tr>
                <tr>
                    <td>Выделить до последнего слайда</td>
                    <td><kbd>Shift</kbd>+<kbd>End</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>Fn</kbd>+<kbd>→</kbd><br /><br /><kbd>⇧ Shift</kbd>+<kbd>End</kbd></td>
                    <td>Выделить слайды до последнего слайда начиная с текущего слайда, на котором находится фокус в списке миниатюр.</td>
                </tr>
                <tr>
                    <td>Выделить текст с позиции курсора до начала строки</td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>Home</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>Fn</kbd>+<kbd>←</kbd><br /><br /><kbd>⇧ Shift</kbd>+<kbd>Home</kbd></td>
                    <td>Выделить фрагмент текста с позиции курсора до начала текущей строки.</td>
                </tr>
                <tr>
                    <td>Выделить текст с позиции курсора до конца строки</td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>End</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>Fn</kbd>+<kbd>→</kbd><br /><br /><kbd>⇧ Shift</kbd>+<kbd>End</kbd></td>
                    <td>Выделить фрагмент текста с позиции курсора до конца текущей строки.</td>
                </tr>
                <tr>
                    <td>Выделить один символ справа</td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>→</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>→</kbd></td>
                    <td>Выделить один символ справа от позиции курсора.</td>
                </tr>
                <tr>
                    <td>Выделить один символ слева</td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>←</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>←</kbd></td>
                    <td>Выделить один символ слева от позиции курсора.</td>
                </tr>
                <tr>
                    <td>Выделить до конца слова</td>
                    <td><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>→</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>⌥ Option</kbd>+<kbd>→</kbd></td>
                    <td>Выделить фрагмент текста с позиции курсора до конца слова.</td>
                </tr>
                <tr>
                    <td>Выделить до начала слова</td>
                    <td><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>←</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>⌥ Option</kbd>+<kbd>←</kbd></td>
                    <td>Выделить фрагмент текста с позиции курсора до начала слова.</td>
                </tr>
                <tr>
                    <td>Выделить одну строку выше</td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>↑</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>↑</kbd></td>
                    <td>Выделить одну строку выше (курсор находится в начале строки).</td>
                </tr>
                <tr>
                    <td>Выделить одну строку ниже</td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>↓</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>↓</kbd></td>
                    <td>Выделить одну строку ниже (курсор находится в конце строки).</td>
                </tr>
                <tr>
                    <td>Убрать всё выделение</td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>Esc</kbd></td>
                    <td><kbd>Esc</kbd>,<br /><kbd>⇧ Shift</kbd>+<kbd>Esc</kbd></td>
                    <td>Убрать всё выделение.</td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section">Оформление текста</th>
                </tr>
                <tr>
                    <td>Непечатаемые символы</td>
                    <td><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>8</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>8</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>8</kbd></td>
                    <td>Показать или скрыть непечатаемые символы.</td>
                </tr>
                <tr>
                    <td>Полужирный шрифт</td>
                    <td><kbd>Ctrl</kbd>+<kbd>B</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>B</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>B</kbd></td>
                    <td>Сделать шрифт в выделенном фрагменте текста полужирным, придав ему большую насыщенность.</td>
                </tr>
                <tr>
                    <td>Курсив</td>
                    <td><kbd>Ctrl</kbd>+<kbd>I</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>I</kbd></td>
                    <td>Сделать шрифт в выделенном фрагменте текста курсивным, придав ему наклон вправо.</td>
                </tr>
                <tr>
                    <td>Подчеркнутый шрифт</td>
                    <td><kbd>Ctrl</kbd>+<kbd>U</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>U</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>U</kbd></td>
                    <td>Подчеркнуть выделенный фрагмент текста чертой, проведенной под буквами.</td>
                </tr>
                <tr>
                    <td>Зачеркнутый шрифт</td>
                    <td><kbd>Ctrl</kbd>+<kbd>5</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>5</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>5</kbd></td>
                    <td>Зачеркнуть выделенный фрагмент текста чертой, проведенной по буквам.</td>
                </tr>
                <tr>
                    <td>Подстрочные знаки</td>
                    <td><kbd>Ctrl</kbd>+<kbd>.</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>.</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>.</kbd></td>
                    <td>Сделать выделенный фрагмент текста мельче и поместить его в нижней части строки, например, как в химических формулах.</td>
                </tr>
                <tr>
                    <td>Надстрочные знаки</td>
                    <td><kbd>Ctrl</kbd>+<kbd>,</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>,</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>,</kbd></td>
                    <td>Сделать выделенный фрагмент текста мельче и поместить его в верхней части строки, например, как в дробях.</td>
                </tr>
                <tr>
                    <td>Маркированный список</td>
                    <td><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>L</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>L</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>L</kbd></td>
                    <td>Создать из выделенного фрагмента текста неупорядоченный маркированный список или начать новый список.</td>
                </tr>
                <tr>
                    <td>Очистить форматирование</td>
                    <td><kbd>Ctrl</kbd>+<kbd>␣ Spacebar</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>Fn</kbd>+<kbd>␣ Spacebar</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>Fn</kbd>+<kbd>␣ Spacebar</kbd></td>
                    <td>Очистить форматирование выделенного фрагмента текста.</td>
                </tr>
                <tr>
                    <td>Увеличить шрифт</td>
                    <td><kbd>Ctrl</kbd>+<kbd>]</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>]</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>]</kbd></td>
                    <td>Увеличить на 1 пункт размер шрифта для выделенного фрагмента текста.</td>
                </tr>
                <tr>
                    <td>Уменьшить шрифт</td>
                    <td><kbd>Ctrl</kbd>+<kbd>[</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>[</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>[</kbd></td>
                    <td>Уменьшить на 1 пункт размер шрифта для выделенного фрагмента текста.</td>
                </tr>
                <tr>
                    <td>Выровнять по центру</td>
                    <td><kbd>Ctrl</kbd>+<kbd>E</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>E</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>E</kbd></td>
                    <td>Выровнять текст по центру между правым и левым краем текстового поля.</td>
                </tr>
                <tr>
                    <td>Выровнять по ширине</td>
                    <td><kbd>Ctrl</kbd>+<kbd>J</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>J</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>J</kbd></td>
                    <td>Выровнять текст как по левому, так и по правому краю текстового поля (выравнивание осуществляется за счет добавления дополнительных интервалов там, где это необходимо).</td>
                </tr>
                <tr>
                    <td>Выровнять по правому краю</td>
                    <td><kbd>Ctrl</kbd>+<kbd>R</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>R</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>R</kbd></td>
                    <td>Выровнять текст по правому краю текстового поля (левый край остается невыровненным).</td>
                </tr>
                <tr>
                    <td>Выровнять по левому краю</td>
                    <td><kbd>Ctrl</kbd>+<kbd>L</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>L</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>L</kbd></td>
                    <td>Выровнять текст по левому краю текстового поля (правый край остается невыровненным).</td>
                </tr>
                <tr>
                    <td>Увеличить отступ слева</td>
                    <td><kbd>Ctrl</kbd>+<kbd>M</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>M</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>M</kbd></td>
                    <td>Увеличить отступ абзаца слева на одну позицию табуляции.</td>
                </tr>
                <tr>
                    <td>Уменьшить отступ слева</td>
                    <td><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>M</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>M</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>M</kbd></td>
                    <td>Уменьшить отступ абзаца слева на одну позицию табуляции.</td>
                </tr>
                <tr>
                    <td>Удалить один символ слева</td>
                    <td><kbd>← Backspace</kbd></td>
                    <td><kbd>Delete</kbd></td>
                    <td>Удалить один символ/выделенный текст/графический объект слева от курсора.</td>
                </tr>
                <tr>
                    <td>Удалить один символ справа</td>
                    <td><kbd>Delete</kbd></td>
                    <td><kbd>Fn</kbd>+<kbd>Delete</kbd></td>
                    <td>Удалить один символ/выделенный текст/графический объект справа от курсора.</td>
                </tr>
                <tr>
                    <td>Удалить слово/выделение/графический объект слева от курсора</td>
                    <td><kbd>Ctrl</kbd>+<kbd>Backspace</kbd></td>
                    <td><kbd>⌥ Option</kbd>+<kbd>Delete</kbd></td>
                    <td>Удалить одно слово/выделенный текст/графический объект слева от курсора.</td>
                </tr>
                <tr>
                    <td>Удалить слово/выделение/графический объект справа от курсора</td>
                    <td><kbd>Ctrl</kbd>+<kbd>Delete</kbd></td>
                    <td><kbd>Fn</kbd>+<kbd>⌥ Option</kbd>+<kbd>Delete</kbd></td>
                    <td>Удалить одно слово/выделенный текст/графический объект справа от курсора.</td>
                </tr>
                <tr>
                    <td>Добавить уровень к нумерации абзаца</td>
                    <td><kbd>↹ Tab</kbd></td>
                    <td><kbd>↹ Tab</kbd></td>
                    <td>Добавить уровень к нумерации абзаца (курсор находится в начале строки).</td>
                </tr>
                <tr>
                    <td>Уменьшить уровень нумерации абзаца</td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>↹ Tab</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>↹ Tab</kbd></td>
                    <td>Уменьшить уровень нумерации абзаца (курсор находится в начале строки).</td>
                </tr>
                <tr>
                    <td>Добавить символ табуляции в абзац</td>
                    <td><kbd>↹ Tab</kbd></td>
                    <td><kbd>↹ Tab</kbd></td>
                    <td>Добавить символ табуляции в абзац.</td>
                </tr>
                <tr>
                    <td>Добавить новый плейсхолдер в аргумент уравнения</td>
                    <td><kbd>Shift</kbd>+<kbd>Enter</kbd>,<br /><kbd>Enter</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>Return</kbd>,<br /><kbd>Return</kbd></td>
                    <td>Добавить новый плейсхолдер в аргумент уравнения.</td>
                </tr>
                <tr>
                    <td>Добавить разрыв строки в тексте</td>
                    <td><kbd>Shift</kbd>+<kbd>Enter</kbd></td>
                    <td><kbd>⇧ Shift</kbd>+<kbd>Return</kbd></td>
                    <td>Добавить разрыв строки в тексте.</td>
                </tr>
                <tr>
                    <td>Добавить абзац</td>
                    <td><kbd>Enter</kbd></td>
                    <td><kbd>Return</kbd></td>
                    <td>Добавить новый абзац или добавить новую строку в плейсхолдер Заголовка/Подзаголовка.</td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section">Вставка специальных символов</th>
                </tr>
                <tr>
                    <td>Вставка знака Евро</td>
                    <td><kbd>Ctrl</kbd>+<kbd>Alt</kbd>+<kbd>E</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>⌥ Option</kbd>+<kbd>E</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>⌥ Option</kbd>+<kbd>E</kbd></td>
                    <td>Вставить знак Евро (€) в текущей позиции курсора.</td>
                </tr>
                <tr>
                    <td>Вставка длинного тире</td>
                    <td></td>
                    <td><kbd>⌥ Option</kbd>+<kbd>⇧ Shift</kbd>+<kbd>-</kbd></td>
                    <td>Вставить длинное тире ‘—’ в текущей позиции курсора.</td>
                </tr>
                <tr>
                    <td>Вставка короткого тире</td>
                    <td></td>
                    <td><kbd>⌥ Option</kbd>+<kbd>-</kbd></td>
                    <td>Вставить короткое тире ‘-’ в текущей позиции курсора.</td>
                </tr>
                <tr>
                    <td>Создать неразрываемый пробел</td>
                    <td><kbd>Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>␣ Spacebar</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>⇧ Shift</kbd>+<kbd>Fn</kbd>+<kbd>␣ Spacebar</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>⇧ Shift</kbd>+<kbd>Fn</kbd>+<kbd>␣ Spacebar</kbd></td>
                    <td>Создать между символами пробел, который нельзя использовать для начала новой строки.</td>
                </tr>
                <tr>
                    <td>Работа с клавиатурой с возможностью ввода символов Юникод</td>
                    <td></td>
                    <td><kbd>⌥ Option</kbd>+<kbd>Q</kbd>,<br /><kbd>⌥ Option</kbd>+<kbd>F</kbd>,<br /><kbd>⇧ Shift</kbd>+<kbd>⌥ Option</kbd>+<kbd>7</kbd>,<br />и другие</td>
                    <td>
                        При использовании сочетаний клавиш <kbd>⌥ Option</kbd>+<kbd>символ клавиатуры</kbd> для клавиатур с возможностью ввода символов Юникод добавляются символы, соответствующие клавиатуре. Примеры приводятся ниже.<br />
                        При английской раскладке ABC сочетание клавиш <kbd>⌥ Option</kbd>+<kbd>Q</kbd> вставляет символ "œ", сочетание клавиш <kbd>⌥ Option</kbd>+<kbd>F</kbd> вставляет символ функции “ƒ”.<br />
                        При раскладке US International w/o deadkeys сочетание клавиш <kbd>⌥ Option</kbd>+<kbd>Q</kbd> вставляет символ “ä”.<br />
                        При раскладке Swiss-german сочетание клавиш <kbd>⇧ Shift</kbd>+<kbd>⌥ Option</kbd>+<kbd>7</kbd> вставляет символ "\”.
                    </td>
                </tr>
                <tr>
                    <th colspan="4" class="keyboard_section">Перемещение по тексту</th>
                </tr>
                <tr>
                    <td>Перейти на один символ влево/вправо или на одну строку вверх/вниз</td>
                    <td><kbd>←</kbd> <kbd>→</kbd> <kbd>↑</kbd> <kbd>↓</kbd></td>
                    <td><kbd>←</kbd> <kbd>→</kbd> <kbd>↑</kbd> <kbd>↓</kbd></td>
                    <td>Переместить курсор на один символ влево/вправо или на одну строку вверх/вниз.</td>
                </tr>
                <tr>
                    <td>Перейти в начало слова или на одно слово влево</td>
                    <td><kbd>Ctrl</kbd>+<kbd>←</kbd></td>
                    <td><kbd>⌥ Option</kbd>+<kbd>←</kbd></td>
                    <td>Переместить курсор в начало слова или на одно слово влево.</td>
                </tr>
                <tr>
                    <td>Перейти на одно слово вправо</td>
                    <td><kbd>Ctrl</kbd>+<kbd>→</kbd></td>
                    <td><kbd>⌥ Option</kbd>+<kbd>→</kbd></td>
                    <td>Переместить курсор на одно слово вправо.</td>
                </tr>
                <tr>
                    <td>Перейти к следующему текстовому заполнителю или создать новый слайд</td>
                    <td><kbd>Ctrl</kbd>+<kbd>↵ Enter</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>↵ Return</kbd>,<br /><kbd>&#8984; Cmd</kbd>+<kbd>↵ Return</kbd></td>
                    <td>Перейти к следующему текстовому заполнителю с заголовком или основным текстом слайда. Если это последний текстовый заполнитель на слайде, будет вставлен новый слайд с таким же макетом, как у исходного.</td>
                </tr>
                <tr>
                    <td>Перейти в начало строки</td>
                    <td><kbd>Home</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>←</kbd><br /><br /><kbd>Home</kbd></td>
                    <td>Установить курсор в начале редактируемой строки.</td>
                </tr>
                <tr>
                    <td>Перейти в конец строки</td>
                    <td><kbd>End</kbd></td>
                    <td><kbd>&#8984; Cmd</kbd>+<kbd>→</kbd><br /><br /><kbd>End</kbd></td>
                    <td>Установить курсор в конце редактируемой строки.</td>
                </tr>
                <tr>
                    <td>Перейти в начало контента</td>
                    <td><kbd>Ctrl</kbd>+<kbd>Home</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>Fn</kbd>+<kbd>←</kbd><br /><br /><kbd>^ Ctrl</kbd>+<kbd>Home</kbd></td>
                    <td>Установить курсор в начале редактируемого текстового поля или в верхней левой ячейке таблицы.</td>
                </tr>
                <tr>
                    <td>Перейти в конец контента</td>
                    <td><kbd>Ctrl</kbd>+<kbd>End</kbd></td>
                    <td><kbd>^ Ctrl</kbd>+<kbd>Fn</kbd>+<kbd>→</kbd><br /><br /><kbd>^ Ctrl</kbd>+<kbd>End</kbd></td>
                    <td>Установить курсор в конце редактируемого текстового поля или в нижней правой ячейке таблицы.</td>
                </tr>
            </table>
            <p id="PasteSpecial" class="note">Вставьте скопированные данные с помощью <kbd>Ctrl</kbd>+<kbd>V</kbd> на Windows или <kbd>Cmd</kbd>+<kbd>V</kbd> на macOS. После вставки скопированных данных используйте клавишу <kbd>Ctrl</kbd>, чтобы открыть меню <b>Специальной вставки</b>, затем нажмите букву, которая соответствует нужному параметру.</p>
        </div>
	</body>
</html>