<!DOCTYPE html>
<html>
	<head>
		<title>Дополнительные параметры редактора презентаций</title>
		<meta charset="utf-8" />
		<meta name="description" content="Дополнительные параметры редактора презентаций" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Дополнительные параметры редактора презентаций</h1>
			<p>Вы можете изменить дополнительные параметры редактора презентаций. Для перехода к ним откройте вкладку <b>Файл</b> на верхней панели инструментов и выберите опцию <b>Дополнительные параметры</b>.</p>
			<p>Дополнительные параметры сгруппированы следующим образом:</p>
            <h3>Редактирование и сохранение</h3>
            <ol>
                <li><span class="onlineDocumentFeatures"><b>Автосохранение</b> используется в <em>онлайн-версии</em> для включения/отключения опции автоматического сохранения изменений, внесенных при редактировании.</span> </li>
                <li><span class="desktopDocumentFeatures"><b>Автовосстановление</b> используется в <em>десктопной версии</em> для включения/отключения опции автоматического восстановления презентации в случае непредвиденного закрытия программы.</span></li>
                <li><b>Показывать кнопку Параметры вставки при вставке содержимого</b>. Соответствующая кнопка будет появляться при вставке содержимого в презентацию.</li>
            </ol>

            <h3>Совместная работа</h3>
            <ol>
                <li class="onlineDocumentFeatures">
                    Подраздел <b>Режим совместного редактирования</b> позволяет задать предпочтительный режим просмотра изменений, вносимых в презентацию при совместной работе.
                    <ul>
                        <li><b>Быстрый</b> (по умолчанию). Пользователи, участвующие в совместном редактировании презентации, будут видеть изменения в реальном времени, как только они внесены другими пользователями.</li>
                        <li><b>Строгий</b>. Все изменения, внесенные участниками совместной работы, будут отображаться только после того, как вы нажмете на кнопку <b>Сохранить</b> <div class="icon icon-saveupdate"></div> с оповещением о наличии новых изменений.</li>
                    </ul>
                </li>
                <li><b>Показывать изменения других пользователей</b>. Эта функция позволяет видеть изменения, которые вносят другие пользователи, в презентации, открытой только на просмотр, в <b>Режиме просмотра в реальном времени</b>.</li>
            </ol>

            <h3>Правописание</h3>
            <ol>
                <li>Опция <b>Проверка орфографии</b> используется для включения/отключения опции проверки орфографии.</li>
                <li><b>Пропускать слова из ПРОПИСНЫХ БУКВ</b>. Слова, написанные прописными буквами, игнорируются при проверке орфографии.</li>
                <li><b>Пропускать слова с цифрами</b>. Слова, в которых есть цифры, игнорируются при проверке орфографии.</li>
                <li>Меню <b>Параметры автозамены...</b> позволяет открыть <a href="../UsageInstructions/MathAutoCorrect.htm" onclick="onhyperlinkclick(this)">параметры автозамены</a>, такие как замена текста при вводе, распознанные функции, автоформат при вводе и другие.</li>
            </ol>

            <h3>Рабочая область</h3>
            <ol>
                <li>Опция <b>Направляющие выравнивания</b> используется для включения/отключения направляющих выравнивания, которые появляются при перемещении объектов и позволяют точно расположить их на слайде.</li>
                <li>Опция <b>Иероглифы</b> используется для включения/отключения отображения иероглифов.</li>
                <li>Опция <b>Использовать клавишу Alt для навигации по интерфейсу с помощью клавиатуры</b> используется для включения использования клавиши <em>Alt</em> / <em>Option</em> в сочетаниях клавиш.</li>
                <li>
                    Опция <b>Тема интерфейса</b> используется для изменения цветовой схемы интерфейса редактора.
                    <ul>
                        <li>Опция <b>Системная</b> позволяет редактору соответствовать системной теме интерфейса.</li>
                        <li><b>Светлая</b> цветовая гамма включает стандартные синий, белый и светло-серый цвета с меньшим контрастом элементов интерфейса, подходящие для работы в дневное время.</li>
                        <li><b>Классическая светлая</b> цветовая гамма включает стандартные синий, белый и светло-серый цвета.</li>
                        <li><b>Темная</b> цветовая гамма включает черный, темно-серый и светло-серый цвета, подходящие для работы в ночное время.</li>
                        <li><b>Контрастная темная</b> цветовая гамма включает черный, темно-серый и белый цвета с большим контрастом элементов интерфейса, выделяющих рабочую область файла.</li>
                        <li>
                            Опция <b>Включить темный режим</b> используется, чтобы сделать рабочую область темнее, если для редактора выбрана <b>Темная</b> или <b>Контрастная темная</b> тема интерфейса. Поставьте галочку <b>Включить темный режим</b>, чтобы активировать эту опцию.
                            <p class="note"><b>Примечание</b>: Помимо доступных тем интерфейса <b>Светлая</b>, <b>Классическая светлая</b>, <b>Темная</b> и <b>Контрастная темная</b>, в редакторах ONLYOFFICE теперь можно использовать собственную цветовую тему. Чтобы узнать, как это сделать, пожалуйста, следуйте <a target="_blank" href="https://helpcenter.onlyoffice.com/ru/installation/docs-developer-change-theme.aspx" onclick="onhyperlinkclick(this)">данному руководству</a>.</p>
                        </li>
                    </ul>
                </li>
                <li>Опция <b>Единица измерения</b> используется для определения единиц, которые должны использоваться на линейках и в окнах свойств для измерения параметров элементов, таких как ширина, высота, интервалы, поля и т.д. Можно выбрать опцию <b>Сантиметр</b>, <b>Пункт</b> или <b>Дюйм</b>.</li>
                <li>Опция <b>Стандартное значение масштаба</b> используется для установки стандартного значения масштаба путем его выбора из списка доступных вариантов от 50% до 500%. Можно также выбрать опцию <b>По размеру слайда</b> или <b>По ширине</b>.</li>
                <li>
                    Опция <b>Хинтинг шрифтов</b> используется для выбора типа отображения шрифта в редакторе презентаций:
                    <ul>
                        <li>Выберите опцию <b>Как Windows</b>, если вам нравится отображение шрифтов в операционной системе Windows, то есть с использованием хинтинга шрифтов Windows.</li>
                        <li>Выберите опцию <b>Как OS X</b>, если вам нравится отображение шрифтов в операционной системе Mac, то есть вообще без хинтинга шрифтов.</li>
                        <li>Выберите опцию <b>Собственный</b>, если хотите, чтобы текст отображался с хинтингом, встроенным в файлы шрифтов.</li>
                        <li>
                            <b>Режим кэширования по умолчанию</b> - используется для выбора режима кэширования символов шрифта. Не рекомендуется переключать без особых причин. Это может быть полезно только в некоторых случаях, например, при возникновении проблемы в браузере Google Chrome с включенным аппаратным ускорением.
                            <p>В редакторе презентаций есть два режима кэширования:</p>
                            <ol>
                                <li>В <b>первом режиме кэширования</b> каждая буква кэшируется как отдельная картинка.</li>
                                <li>Во <b>втором режиме кэширования</b> выделяется картинка определенного размера, в которой динамически располагаются буквы, а также реализован механизм выделения и удаления памяти в этой картинке. Если памяти недостаточно, создается другая картинка, и так далее.</li>
                            </ol>
                            <p>Настройка <b>Режим кэширования по умолчанию</b> применяет два вышеуказанных режима кэширования по отдельности для разных браузеров:</p>
                            <ul>
                                <li>Когда настройка <b>Режим кэширования по умолчанию</b> включена, в Internet Explorer (v. 9, 10, 11) используется <b>второй режим кэширования</b>, в других браузерах используется <b>первый режим кэширования</b>.</li>
                                <li>Когда настройка <b>Режим кэширования по умолчанию</b> выключена, в Internet Explorer (v. 9, 10, 11) используется <b>первый режим кэширования</b>, в других браузерах используется <b>второй режим кэширования</b>.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li>
                    Опция <b>Настройки макросов</b> используется для настройки отображения макросов с уведомлением.
                    <ul>
                        <li>Выберите опцию <b>Отключить все</b>, чтобы отключить все макросы в презентации;</li>
                        <li>Выберите опцию <b>Показывать уведомление</b>, чтобы получать уведомления о макросах в презентации;</li>
                        <li>Выберите опцию <b>Включить все</b>, чтобы автоматически запускать все макросы в презентации.</li>
                    </ul>
                </li>
            </ol>            
			<p>Чтобы сохранить внесенные изменения, нажмите кнопку <b>Применить</b>.</p>
		</div>
	</body>
</html>