<!DOCTYPE html>
<html>
<head>
    <title>Проверка орфографии</title>
    <meta charset="utf-8" />
    <meta name="description" content="Проверяйте правописание текста на своем языке в ходе редактирования презентации" />
    <link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
    <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Проверка орфографии</h1>
        <p>В редакторе презентаций можно проверять правописание текста на определенном языке и исправлять ошибки в ходе редактирования. В <em>десктопной версии</em> также доступна возможность добавлять слова в пользовательский словарь, общий для всех трех редакторов.</p>
        <p>Прежде всего <b>выберите язык</b> презентации. Щелкните по значку <span class="icon icon-document_language"></span> в правой части <b>строки состояния</b>. В окне, которое появится, выберите нужный язык и нажмите кнопку <b>OK</b>. Выбранный язык будет применен ко всей презентации. </p>
        <p><img alt="Окно выбора языка презентации" src="../../../../../../common/main/resources/help/ru/images/document_language_window.png" /></p>
        <p>Чтобы выбрать какой-то другой язык для любого фрагмента текста в этой презентации, выделите мышью нужную часть теста и используйте меню <img alt="Проверка орфографии - выбор языка текста" src="../images/spellchecking_language.png" />, которое находится в <b>строке состояния</b>.</p>
        <p>Для <b>включения</b> функции проверки орфографии можно сделать следующее:</p>
        <ul>
            <li>нажмите на значок <div class = "icon icon-spellcheckdeactivated"></div> <b>Проверка орфографии</b> в строке состояния или</li>
            <li>откройте вкладку <b>Файл</b> верхней панели инструментов, выберите опцию <b>Дополнительные параметры</b>, поставьте галочку рядом с опцией <b>Включить проверку орфографии</b> и нажмите кнопку <b>Применить</b>.</li>
        </ul>
        <p>Слова, написанные с ошибками, будут подчеркнуты красной чертой.</p>
        <p>Щелкните правой кнопкой мыши по нужному слову, чтобы вызвать меню, и:</p>
        <ul>
            <li>выберите одно из предложенных похожих слов, которые написаны правильно, чтобы заменить слово с ошибкой на предложенное слово. Если найдено слишком много вариантов, в меню появляется пункт <b>Больше вариантов...</b>;</li>
            <li>используйте опцию <b>Пропустить</b>, чтобы пропустить только это слово и убрать подчеркивание или <b>Пропустить все</b>, чтобы пропустить все идентичные слова, повторяющиеся в тексте;</li>
            <li>если текущее слово отсутствует в словаре, добавьте его в пользовательский словарь. В следующий раз это слово не будет расцениваться как ошибка. Эта возможность доступна в <em>десктопной версии</em>.</li>
            <li>выберите другой язык для этого слова.</li>
        </ul>
        <p><img alt="Проверка орфографии" src="../images/spellchecking_presentation.png" /></p>
        <p>Для <b>отключения</b> функции проверки орфографии можно сделать следующее:</p>
        <ul>
            <li>нажмите на значок <div class = "icon icon-spellcheckactivated"></div> <b>Проверка орфографии</b> в строке состояния или</li>
            <li>откройте вкладку <b>Файл</b> верхней панели инструментов, выберите опцию <b>Дополнительные параметры</b>, уберите галочку рядом с опцией <b>Включить проверку орфографии</b> и нажмите кнопку <b>Применить</b>.</li>
        </ul>
    </div>
</body>
</html>