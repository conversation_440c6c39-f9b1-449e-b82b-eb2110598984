<!DOCTYPE html>
<html>
	<head>
        <title>Вкладка Вид</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора презентаций - Вкладка Вид" />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вкладка Вид</h1>
            <p>
                Вкладка <b>Вид</b> <a href="https://www.onlyoffice.com/ru/presentation-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактора презентаций</b></a> позволяет управлять тем, как выглядит ваша презентация во время работы над ней.
            </p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора презентаций:</p>
                <p><img alt="Вкладка Вид" src="../images/interface/viewtab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора презентаций:</p>
                <p><img alt="Вкладка Вид" src="../images/interface/desktop_viewtab.png" /></p>
            </div>
            <p>На этой вкладке доступны следующие параметры просмотра:</p>
            <ul>
                <li><b>Масштаб</b> - позволяет увеличивать и уменьшать масштаб презентации,</li>
                <li><b>По размеру слайда</b> - позволяет изменить размер страницы, чтобы на экране отображался весь слайд,</li>
                <li><b>По ширине</b> - позволяет изменить размер слайда, чтобы он соответствовал ширине экрана,</li>
                <li><b>Тема интерфейса</b> - позволяет изменить тему интерфейса на <b>Системную</b>, <b>Светлую</b>, <b>Классическую светлую</b>, <b>Темную</b> или <b>Контрастную темную</b>,</li>
            </ul>
            <p>Следующие параметры позволяют настроить отображение или скрытие элементов. Отметьте галочкой элементы, чтобы сделать их видимыми:</p>
            <ul>
                <li><b>Заметки</b> - всегда отображать панель заметок,</li>
                <li><b>Линейки</b> - всегда отображать линейки,</li>
                <li><b>Направляющие</b> и <b>Линии сетки</b> - чтобы правильно позиционировать объекты на слайде,</li>
                <li><b>Всегда показывать панель инструментов</b> - чтобы верхняя панель инструментов всегда отображалась,</li>                
                <li><b>Строка состояния</b> - чтобы строка состояния всегда отображалась,</li>
                <li><b>Левая панель</b> - чтобы левая панель отображалась,</li>
                <li><b>Правая панель</b> - чтобы правая панель отображалась.</li>
            </ul>
        </div>
	</body>
</html>