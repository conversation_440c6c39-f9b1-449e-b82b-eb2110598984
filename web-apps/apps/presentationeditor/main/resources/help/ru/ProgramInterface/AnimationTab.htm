	<!DOCTYPE html>
	<html>
	<head>
		<title>Вкладка Анимация</title>
		<meta charset="utf-8" />
		<meta name="description" content="Знакомство с пользовательским интерфейсом редактора презентаций - Вкладка Анимация" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<script type="text/javascript" src="../callback.js"></script>
		<script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Вкладка Анимация</h1>
			<p>Вкладка <b>Анимация</b> <a href="https://www.onlyoffice.com/ru/presentation-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактора презентаций</b></a> позволяет управлять эффектами анимации. Вы можете добавлять эффекты анимации, определять их перемещение и настраивать другие параметры анимационных эффектов для индивидуальной настройки презентации.</p>
			<div class="onlineDocumentFeatures">
				<p>Окно онлайн-редактора презентаций:</p>
				<p><img alt="Вкладка Анимация" src="../images/interface/animationtab.png" /></p>
			</div>
			<div class="desktopDocumentFeatures">
				<p>Окно десктопного редактора презентаций:</p>
				<p><img alt="Вкладка Анимация" src="../images/interface/desktop_animationtab.png" /></p>
			</div>
			<p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
			<ul>
				<li>выбирать <a href="../UsageInstructions/AddingAnimations.htm#applyanimation">эффект анимации</a>,</li>
				<li>установить соответствующие <b>параметры движения</b> для каждого эффекта анимации,</li>
				<li>добавлять <a href="../UsageInstructions/AddingAnimations.htm#multipleanimations">несколько анимаций</a>,</li>
				<li>изменять <a href="../UsageInstructions/AddingAnimations.htm#animationsorder">порядок</a> эффектов анимации при помощи параметров <b>Переместить раньше</b> или <b>Переместить позже</b>,</li>
				<li><b>просмотреть</b> эффект анимации,</li>
				<li>установить для анимации <a href="../UsageInstructions/AddingAnimations.htm#timing">параметры времени</a>, такие как <b>длительность</b>, <b>задержка</b> and <b>повтор</b>,</li>
				<li>включать и отключать <b>перемотку назад</b>.</li>
			</ul>
		</div>
	</body>
</html>