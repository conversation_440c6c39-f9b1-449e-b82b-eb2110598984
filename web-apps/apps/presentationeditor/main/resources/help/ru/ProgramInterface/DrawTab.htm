<!DOCTYPE html>
<html>
	<head>
        <title>Вкладка Рисование</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора презентаций — вкладка Рисование" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вкладка Рисование</h1>
            <p>Вкладка <b>Рисование</b> <a href="https://www.onlyoffice.com/ru/presentation-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактора презентаций</b></a> позволяет выполнять основные операции рисования.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора презентаций:</p>
                <p><img alt="Draw tab" src="../images/interface/drawtab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора презентаций:</p>
                <p><img alt="Draw tab" src="../images/interface/desktop_drawtab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li><a href="../UsageInstructions/DrawingOperations.htm" onclick="onhyperlinkclick(this)">выделять</a> рисунки, чтобы изменить их размер или удалить,</li>
                <li>использовать <a href="../UsageInstructions/DrawingOperations.htm" onclick="onhyperlinkclick(this)">ручку и маркер</a>, чтобы рисовать или добавлять рукописные заметки и выделение,</li>
                <li>использовать <a href="../UsageInstructions/DrawingOperations.htm" onclick="onhyperlinkclick(this)">ластик</a>, чтобы удалять весь рисунок или рукописный текст.</li>
            </ul>
        </div>
	</body>
</html>