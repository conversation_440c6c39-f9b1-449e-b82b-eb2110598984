<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Главная</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора презентаций - Вкладка Главная" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вкладка Главная</h1>
            <p>Вкладка <b>Главная</b> открывается по умолчанию при открытии презентации. Она позволяет задать основные параметры слайда, форматировать текст, вставлять некоторые объекты, выравнивать и располагать их в определенном порядке.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора презентаций:</p>
                <p><img alt="Вкладка Главная" src="../images/interface/hometab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора презентаций:</p>
                <p><img alt="Вкладка Главная" src="../images/interface/desktop_hometab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li>управлять <a href="../UsageInstructions/ManageSlides.htm" onclick="onhyperlinkclick(this)">слайдами</a> и <a href="../UsageInstructions/PreviewPresentation.htm" onclick="onhyperlinkclick(this)">начать показ слайдов</a>,</li>
                <li>форматировать <a href="../UsageInstructions/InsertText.htm#formattext" onclick="onhyperlinkclick(this)">текст</a> внутри текстового поля,</li>
                <li>вставлять <a href="../UsageInstructions/InsertText.htm" onclick="onhyperlinkclick(this)">текстовые поля</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">изображения</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">фигуры</a>,</li>
                <li><a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">выравнивать и упорядочивать объекты</a> на слайде,</li>
                <li><a href="../UsageInstructions/CopyClearFormatting.htm" onclick="onhyperlinkclick(this)">копировать и очищать</a> форматирование текста,</li>
                <li>изменять <a href="../UsageInstructions/SetSlideParameters.htm" onclick="onhyperlinkclick(this)">тему, цветовую схему или размер слайдов</a>.</li>
            </ul>
		</div>
	</body>
</html>