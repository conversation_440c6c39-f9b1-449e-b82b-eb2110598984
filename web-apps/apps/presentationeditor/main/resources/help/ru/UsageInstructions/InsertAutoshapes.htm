<!DOCTYPE html>
<html>
	<head>
		<title>Вставка и форматирование автофигур</title>
		<meta charset="utf-8" />
		<meta name="description" content="Добавьте в презентацию автофигуру и настройте ее свойства." />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вставка и форматирование автофигур</h1>
            <h3>Вставка автофигуры</h3>
            <p>Для <b>добавления</b> автофигуры на слайд:</p>
            <ol>
                <li>в списке слайдов слева выберите тот слайд, на который требуется добавить автофигуру,</li>
                <li>щелкните по значку <div class="icon icon-insertautoshape"></div> <b>Фигура</b> на вкладке <b>Главная</b> или по стрелочке рядом с <div class="big big-shapegallery"></div> <b>Галереей фигур</b> на вкладке <b>Вставка</b> верхней панели инструментов,</li>
                <li>выберите одну из доступных групп автофигур: Основные фигуры, Фигурные стрелки, Математические знаки, Схемы, Звезды и ленты, Выноски, Кнопки, Прямоугольники, Линии,</li>
                <li>щелкните по нужной автофигуре внутри выбранной группы,</li>
                <li>
                    в области редактирования слайда установите курсор там, где требуется поместить автофигуру,
                    <p class="note"><b>Примечание</b>: чтобы растянуть фигуру, можно перетащить курсор при нажатой кнопке мыши.</p>
                </li>
                <li>
                    после того, как автофигура будет добавлена, можно изменить ее <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">размер, местоположение</a> и свойства. Вы можете сохранить автофигуру как изображение на жестком диске с помощью пункта <b>Сохранить как рисунок</b> контекстного меню.
                    <p class="note"><b>Примечание</b>: чтобы добавить надпись внутри фигуры, убедитесь, что фигура на слайде выделена, и начинайте печатать текст. Текст, добавленный таким способом, становится частью автофигуры (при перемещении или повороте автофигуры текст будет перемещаться или поворачиваться вместе с ней).</p>
                </li>
            </ol>
            <p>Также можно добавить автофигуру в макет слайда. Для получения дополнительной информации вы можете обратиться к этой <a href="../UsageInstructions/SetSlideParameters.htm#addtolayout" onclick="onhyperlinkclick(this)">статье</a>.</p>
            <hr />
            <h3>Копирование форматирования стиля автофигуры</h3>
            <p>Чтобы <b>скопировать</b> определенное форматирование стиля автофигуры,</p>
            <ol>
                <li>выберите автофигуру, форматирование которой нужно скопировать, и с помощью мыши или <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">клавиатуры</a>,</li>
                <li>щелкните значок <b>Копировать стиль</b> <div class="icon icon-copystyle"></div> на вкладке  <b>Главная</b> верхней панели инструментов (указатель мыши будет выглядеть следующим образом <div class="icon icon-paste_style"></div>),</li>
                <li>выберите нужную автофигуру, чтобы применить такое же форматирование.</li>
            </ol>
            <hr />
            <h3>Изменение параметров автофигуры</h3>
            <p id="shape_rightclickmenu">Чтобы выровнять или расположить автофигуры в определенном порядке, используйте <b>контекстное меню</b>. Меню содержит следующие пункты:</p>
            <ul>
                <li><b>Вырезать, копировать, вставить</b> - стандартные опции, которые используются для вырезания или копирования выделенного текста/объекта и вставки ранее вырезанного/скопированного фрагмента текста или объекта в то место, где находится курсор.</li>
                <li><b>Порядок</b> - используется, чтобы вынести выбранную автофигуру на передний план, переместить на задний план, перенести вперед или назад, а также сгруппировать или разгруппировать автофигуры для выполнения операций над несколькими из них сразу. Подробнее о расположении объектов в определенном порядке рассказывается на <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</li>
                <li><b>Выравнивание</b> - используется, чтобы выровнять фигуру по левому краю, по центру, по правому краю, по верхнему краю, по середине, по нижнему краю. Подробнее о выравнивании объектов рассказывается на <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</li>
                <li><b>Стиль обтекания</b> - используется, чтобы выбрать один из доступных стилей обтекания текстом - в тексте, вокруг рамки, по контуру, сквозное, сверху и снизу, перед текстом, за текстом - или для изменения границы обтекания. Опция <b>Изменить границу обтекания</b> доступна только в том случае, если выбран стиль обтекания, отличный от стиля "В тексте". Чтобы произвольно изменить границу, перетаскивайте точки границы обтекания. Чтобы создать новую точку границы обтекания, щелкните в любом месте на красной линии и перетащите ее в нужную позицию. <div class="big big-wrap_boundary"></div></li>
                <li><b>Поворот</b> - используется, чтобы повернуть фигуру на 90 градусов по часовой стрелке или против часовой стрелки, а также чтобы отразить фигуру слева направо или сверху вниз.</li>
                <li><b>Сохранить как рисунок</b> -  используется , чтобы сохранить автофигуру в виде изображения на жестком диске.</li>
                <li>
                    <b>Изменить точки</b> - используется для редактирования формы или изменения кривизны автофигуры.
                    <ol>
                        <li>Чтобы активировать редактируемые опорные точки фигуры, щелкните по фигуре правой кнопкой мыши и в контекстном меню выберите пункт <b>Изменить точки</b> или нажмите кнопку <b>Изменить фигуру</b> > <b>Изменить точки</b> на правой боковой панели. Черные квадраты, которые становятся активными, — это точки, где встречаются две линии, а красная линия очерчивает фигуру. Щелкните и перетащите квадрат, чтобы изменить положение точки и изменить контур фигуры.</li>
                        <li>
                            После сдвига опорной точки фигуры, появятся две синие линии с белыми квадратами на концах. Это кривые Безье, которые позволяют создавать кривую и изменять ее значение.
                            <p><img alt="Редактировать Точки" src="../images/editpoints_example.png" /></p>
                        </li>
                        <li>
                            Пока опорные точки активны, вы можете добавлять и удалять их.
                            <p><b>Чтобы добавить точку к фигуре</b>, удерживайте <b>Ctrl</b> и щелкните место, где вы хотите добавить опорную точку.</p>
                            <p><b>Чтобы удалить точку</b>, удерживайте <b>Ctrl</b> и щелкните по ненужной точке.</p>
                        </li>
                    </ol>
                </li>
                <li><b>Дополнительные параметры фигуры</b> - используется для вызова окна 'Фигура - дополнительные параметры'.</li>
                <li><b>Добавить в макет</b> - используется для добавления автофигуры в макет слайда..</li>
            </ul>
            <hr />
            <p>Некоторые параметры автофигуры можно изменить с помощью вкладки <b>Параметры фигуры</b> на правой боковой панели. Чтобы ее активировать, щелкните по автофигуре и выберите значок <b>Параметры фигуры</b> <span class="icon icon-shape_settings_icon"></span> справа. Здесь можно изменить следующие свойства:</p>
            <p><img class="floatleft" alt="Вкладка Параметры фигуры" src="../images/shapesettingstab.png" /></p>
            <ul style="margin-left: 280px;">
                <li>
                    <b>Заливка</b> - используйте этот раздел, чтобы выбрать заливку автофигуры. Можно выбрать следующие варианты:
                    <ul>
                        <li><b>Заливка цветом</b> - чтобы задать сплошной цвет, который требуется применить к выбранной фигуре.</li>
                        <li><b>Градиентная заливка</b> - чтобы залить фигуру двумя цветами, плавно переходящими друг в друга.</li>
                        <li><b>Изображение или текстура</b> - чтобы использовать в качестве фона фигуры какое-то изображение или готовую текстуру.</li>
                        <li><b>Узор</b> - чтобы залить фигуру с помощью двухцветного рисунка, который образован регулярно повторяющимися элементами.</li>
                        <li><b>Без заливки</b> - выберите эту опцию, если вы вообще не хотите использовать заливку.</li>
                    </ul>
                    <p>Чтобы получить более подробную информацию об этих опциях, обратитесь к разделу <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">Заливка объектов и выбор цветов</a>.</p>
                </li>
                <li id="shapestroke">
                    <b>Контур</b> - используйте этот раздел, чтобы изменить толщину, цвет или тип контура.
                    <ul>
                        <li>Для изменения <b>толщины</b> контура выберите из выпадающего списка <b>Толщина</b> одну из доступных опций. Доступны следующие опции: 0.5 пт, 1 пт, 1.5 пт, 2.25 пт, 3 пт, 4.5 пт, 6 пт. Или выберите опцию <b>Без линии</b>, если вы вообще не хотите использовать контур.</li>
                        <li>Для изменения <b>цвета</b> контура щелкните по цветному прямоугольнику и <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">выберите нужный цвет</a>. Можно использовать цвет выбранной <b>темы</b>, <b>стандартный цвет</b> или выбрать <b>пользовательский цвет</b>.</li>
                        <li>Для изменения <b>типа</b> контура выберите нужную опцию из соответствующего выпадающего списка (по умолчанию применяется сплошная линия, ее можно изменить на одну из доступных пунктирных линий).</li>
                        <li>Для изменения <b>непрозрачности</b>, введите необходимое значение вручную или воспользуйтесь ползунком.</li>
                    </ul>
                </li>
                <li>
                    <b>Поворот</b> - используется, чтобы повернуть фигуру на 90 градусов по часовой стрелке или против часовой стрелки, а также чтобы отразить фигуру слева направо или сверху вниз. Нажмите на одну из кнопок:
                    <ul>
                        <li><div class="icon icon-rotatecounterclockwise"></div> чтобы повернуть фигуру на 90 градусов против часовой стрелки</li>
                        <li><div class="icon icon-rotateclockwise"></div> чтобы повернуть фигуру на 90 градусов по часовой стрелке</li>
                        <li><div class="icon icon-fliplefttoright"></div> чтобы отразить фигуру по горизонтали (слева направо)</li>
                        <li><div class="icon icon-flipupsidedown"></div> чтобы отразить фигуру по вертикали (сверху вниз)</li>
                    </ul>
                </li>
                <li>
                    <b>Изменить фигуру</b> - используйте этот раздел, чтобы изменить точки фигуры или заменить текущую автофигуру на другую, выбрав ее из выпадающего списка.
                    <ul>
                        <li>
                            <b>Изменить точки</b> - используется для редактирования формы или изменения кривизны автофигуры.
                            <ol>
                                <li>
                                    После сдвига опорной точки фигуры, появятся две синие линии с белыми квадратами на концах. Это кривые Безье, которые позволяют создавать кривую и изменять ее значение.
                                    <p><img alt="Редактировать Точки" src="../images/editpoints_example.png" /></p>
                                </li>
                                <li>
                                    Пока опорные точки активны, вы можете добавлять и удалять их.
                                    <p><b>Чтобы добавить точку к фигуре</b>, удерживайте <b>Ctrl</b> и щелкните место, где вы хотите добавить опорную точку.</p>
                                    <p><b>Чтобы удалить точку</b>, удерживайте <b>Ctrl</b> и щелкните по ненужной точке.</p>
                                </li>
                            </ol>
                        </li>
                        <li><b>Изменить фигуру</b> - используется для замены текущей автофигуры. Выберите другую автофигуру из раскрывающегося списка.</li>
                    </ul>
                </li>
                <li><b>Отображать тень</b> - отметьте эту опцию, чтобы отображать фигуру с тенью.</li>
            </ul>
            <hr />
            <p>Для изменения <b>дополнительных параметров</b> автофигуры щелкните по ней правой кнопкой мыши и выберите из контекстного меню пункт <b>Дополнительные параметры фигуры</b> или щелкните левой кнопкой мыши и нажмите на ссылку <b>Дополнительные параметры</b> на правой боковой панели. Откроется окно свойств фигуры:</p>
            <p><img alt="Свойства фигуры - вкладка Положение" src="../images/shape_properties1.png" /></p>
            <p>На вкладке <b>Положение</b> можно изменить <b>Ширину</b> и/или <b>Высоту</b> автофигуры. Если нажата кнопка <b>Сохранять пропорции</b> <span class="icon icon-constantproportions"></span> (в этом случае она выглядит так: <span class="icon icon-constantproportionsactivated"></span>), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон фигуры.</p>
            <p>Также можно задать точную позицию, используя поля <b>По горизонтали</b> и <b>По вертикали</b>, а также поле <b>От</b>, где доступны параметры <b>Верхний левый угол</b> и <b>По центру</b>.</p>
            <p><img alt="Фигура - дополнительные параметры: Поворот" src="../images/shape_properties5.png" /></p>
            <p>Вкладка <b>Поворот</b> содержит следующие параметры:</p>
            <ul>
                <li><b>Угол</b> - используйте эту опцию, чтобы повернуть фигуру на точно заданный угол. Введите в поле нужное значение в градусах или скорректируйте его, используя стрелки справа. </li>
                <li><b>Отражено</b> - отметьте галочкой опцию <b>По горизонтали</b>, чтобы отразить фигуру по горизонтали (слева направо), или отметьте галочкой опцию <b>По вертикали</b>, чтобы отразить фигуру по вертикали (сверху вниз).</li>
            </ul>
            <p><img alt="Свойства фигуры - вкладка Линии и стрелки" src="../images/shape_properties.png" /></p>
            <p>Вкладка <b>Линии и стрелки</b> содержит следующие параметры:</p>
            <ul>
                <li>
                    <b>Стиль линии</b> - эта группа опций позволяет задать такие параметры:
                    <ul>
                        <li>
                            <b>Тип окончания</b> - эта опция позволяет задать стиль окончания линии, поэтому ее можно применить только для фигур с разомкнутым контуром, таких как линии, ломаные линии и т.д.:
                            <ul>
                                <li><b>Плоский</b> - конечные точки будут плоскими.</li>
                                <li><b>Закругленный</b> - конечные точки будут закругленными.</li>
                                <li><b>Квадратный</b> - конечные точки будут квадратными.</li>
                            </ul>
                        </li>
                        <li>
                            <b>Тип соединения</b> - эта опция позволяет задать стиль пересечения двух линий, например, она может повлиять на контур ломаной линии или углов треугольника или прямоугольника:
                            <ul>
                                <li><b>Закругленный</b> - угол будет закругленным.</li>
                                <li><b>Скошенный</b> - угол будет срезан наискось.</li>
                                <li><b>Прямой</b> - угол будет заостренным. Хорошо подходит для фигур с острыми углами.</li>
                            </ul>
                            <p class="note"><b>Примечание</b>: эффект будет лучше заметен при использовании контура большей толщины.</p>
                        </li>
                    </ul>
                </li>
                <li><b>Стрелки</b> - эта группа опций доступна только в том случае, если выбрана фигура из группы автофигур <b>Линии</b>. Она позволяет задать <b>Начальный</b> и <b>Конечный стиль</b> и <b>Размер</b> стрелки, выбрав соответствующие опции из выпадающих списков.</li>
            </ul>
            <p id="internalmargins"><img alt="Свойства фигуры - вкладка Поля вокруг текста" src="../images/shape_properties3.png" /></p>
            <p>
                Вкладка <b>Текстовое поле</b> содержит следующие параметры:
                <ul>
                    <li><b>Автоподбор</b> - чтобы изменить способ отображения текста внутри фигуры: <b>Без автоподбора</b>, <b>Сжать текст при переполнении</b>, <b>Подгонять размер фигуры под текст</b>.</li>
                    <li><b>Поля вокруг текста</b> - чтобы изменить внутренние поля автофигуры <b>Сверху</b>, <b>Снизу</b>, <b>Слева</b> и <b>Справа</b> (то есть расстояние между текстом внутри фигуры и границами автофигуры).</li>
                </ul>
            </p>
            <p class="note"><b>Примечание</b>: эта вкладка доступна, только если в автофигуру добавлен текст, в противном случае вкладка неактивна.</p>
            <p id="columns"><img alt="Свойства фигуры - вкладка Колонки" src="../images/shape_properties2.png" /></p>
            <p>На вкладке <b>Колонки</b> можно добавить колонки текста внутри автофигуры, указав нужное <b>Количество колонок</b> (не более 16) и <b>Интервал между колонками</b>. После того как вы нажмете кнопку <b>ОК</b>, уже имеющийся текст или любой другой текст, который вы введете, в этой автофигуре будет представлен в виде колонок и будет перетекать из одной колонки в другую.</p>
            <p><img alt="Свойства фигуры - вкладка Альтернативный текст" src="../images/shape_properties4.png" /></p>
            <p>Вкладка <b>Альтернативный текст</b> позволяет задать <b>Заголовок</b> и <b>Описание</b>, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит фигура.</p>
            <hr />
            <p>Чтобы <b>заменить</b> добавленную автофигуру, щелкните по ней левой кнопкой мыши и используйте выпадающий список <b>Изменить автофигуру</b> на вкладке <b>Параметры фигуры</b> правой боковой панели.</p>
            <p>Чтобы <b>удалить</b> добавленную автофигуру, щелкните по ней левой кнопкой мыши и нажмите клавишу <b>Delete</b> на клавиатуре.</p>
            <p>Чтобы узнать, как <b>выровнять</b> автофигуру на слайде или <b>расположить в определенном порядке</b> несколько автофигур, обратитесь к разделу <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">Выравнивание и упорядочивание объектов на слайде</a>.</p>
            <hr />
            <h3>Соединение автофигур с помощью соединительных линий</h3>
            <p>Автофигуры можно соединять, используя линии с точками соединения, чтобы продемонстрировать зависимости между объектами (например, если вы хотите создать блок-схему). Для этого:</p>
            <ol>
                <li>щелкните по значку <div class="icon icon-insertautoshape"></div> <b>Фигура</b> на вкладке <b>Главная</b> или <b>Вставка</b> верхней панели инструментов,</li>
                <li>
                    выберите в меню группу <b>Линии</b>,
                    <p><img alt="Фигуры - Линии" src="../images/connectors.png" /></p>
                </li>
                <li>щелкните по нужной фигуре в выбранной группе (кроме трех последних фигур, которые не являются соединительными линиями, а именно <em>Кривая</em>, <em>Рисованная кривая</em> и <em>Произвольная форма</em>),</li>
                <li>
                    наведите указатель мыши на первую автофигуру и щелкните по одной из точек соединения <div class="icon icon-connectionpoint"></div>, появившихся на контуре фигуры,
                    <p><span class="big big-connectors_firstshape"></span></p>
                </li>
                <li>
                    перетащите указатель мыши ко второй фигуре и щелкните по нужной точке соединения на ее контуре.
                    <p><span class="big big-connectors_secondshape"></span></p>
                </li>
            </ol>
            <p>При перемещении соединенных автофигур соединительная линия остается прикрепленной к фигурам и перемещается вместе с ними. </p>
            <p><span class="big big-connectors_moveshape"></span></p>
            <p>Можно также открепить соединительную линию от фигур, а затем прикрепить ее к любым другим точкам соединения.</p>
        </div>
	</body>
</html>