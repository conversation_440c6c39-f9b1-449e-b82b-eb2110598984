<!DOCTYPE html>
<html>
	<head>
		<title>Добавление гиперссылок</title>
		<meta charset="utf-8" />
		<meta name="description" content="Преобразуйте слово или текстовый фрагмент в гиперссылку, ведущую на внешний веб-сайт или другой слайд в этой же презентации" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Добавление гиперссылок</h1>
			<p>Для добавления гиперссылки:</p>
			<ol>
				<li>установите курсор на том месте внутри текстового поля, где требуется добавить гиперссылку,</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>нажмите значок <div class = "icon icon-addhyperlink"></div> <b>Гиперссылка</b>,</li>
				<li>после этого появится окно <b>Параметры гиперссылки</b>, в котором можно указать параметры гиперссылки:
				<ul>
				    <li>Выберите тип ссылки, которую требуется вставить:
				    <ul>
				    <li>Используйте опцию <b>Внешняя ссылка</b> и введите URL в формате http://www.example.com в расположенном ниже поле <b>Связать с</b>, если требуется добавить гиперссылку, ведущую на <b>внешний</b> сайт. Если надо добавить гиперссылку на <b>локальный</b> файл, введите URL в формате <em>file://path/Presentation.pptx</em> (для Windows) или <em>file:///path/Presentation.pptx</em> (для MacOS и Linux) в поле <b>Связать с</b>.
                        <p class="note">Гиперссылки <em>file://path/Presentation.pptx</em> или <em>file:///path/Presentation.pptx</em> можно открыть только в десктопной версии редактора. В веб-редакторе можно только добавить такую ссылку без возможности открыть ее.</p>
				    <p><img alt="Окно Параметры гиперссылки" src="../../../../../../common/main/resources/help/ru/images/hyperlinkwindow.png" /></p>
				    </li>
				    <li>Используйте опцию <b>Слайд в этой презентации</b> и выберите один из вариантов ниже, если требуется добавить гиперссылку, ведущую на определенный слайд в этой же презентации. Можно выбрать один из следующих переключателей: Следующий слайд, Предыдущий слайд, Первый слайд, Последний слайд, Слайд с указанным номером.
				    <p><img alt="Окно Параметры гиперссылки" src="../images/hyperlinkwindow2.png" /></p>
				    </li>
				    </ul>
				    </li>
					<li><b>Отображать</b> - введите текст, который должен стать ссылкой и будет вести по заданному веб-адресу или на слайд, указанный в поле выше.</li>
					<li><b>Текст подсказки</b> - введите текст краткого примечания к гиперссылке, который будет появляться в маленьком всплывающем окне при наведении на гиперссылку курсора.</li>
				</ul>
				</li>
				<li>нажмите кнопку <b>OK</b>.</li>
			</ol>
            <p>Для добавления гиперссылки можно также использовать сочетание клавиш <b>Ctrl+K</b> или щелкнуть правой кнопкой мыши там, где требуется добавить гиперссылку, и выбрать в контекстном меню команду <b>Гиперссылка</b>.</p>
			<p class="note"><b>Примечание</b>: также можно выделить мышью или <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">с помощью клавиатуры</a> символ, слово или словосочетание, а затем
            открыть окно <b>Параметры гиперссылки</b>, как описано выше. В этом случае поле <b>Отображать</b> будет содержать выделенный текстовый фрагмент.</p>
			<p>При наведении курсора на добавленную гиперссылку появится подсказка с заданным текстом. 
			Для перехода по ссылке нажмите клавишу <b>CTRL</b> и щелкните по ссылке в презентации.</p>
			<p>Для редактирования или удаления добавленной гиперссылки, щелкните по ней правой кнопкой мыши, выберите опцию <b>Гиперссылка</b>, а затем действие, которое хотите выполнить, - <b>Изменить гиперссылку</b> или <b>Удалить гиперссылку</b>.</p>
		</div>
	</body>
</html>
