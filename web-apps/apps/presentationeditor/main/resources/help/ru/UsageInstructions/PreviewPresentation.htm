<!DOCTYPE html>
<html>
	<head>
		<title>Просмотр презентации</title>
		<meta charset="utf-8" />
		<meta name="description" content="Просмотрите презентацию" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Просмотр презентации</h1>
            <h3>Запуск просмотра слайдов</h3>
            <p class="note">Примечание: Если вы загружаете презентацию с эффектами анимации, созданную в стороннем приложении, вы можете их предварительно просмотреть.</p>
            <p>Чтобы просмотреть презентацию, которую вы в данный момент редактируете, можно сделать следующее:</p>
            <ul>
                <li>щелкните по значку <b>Начать показ слайдов</b> <div class = "icon icon-startpreview"></div> на вкладке <b>Главная</b> верхней панели инструментов или в левой части строки состояния или</li>
                <li>выберите определенный слайд в списке слайдов слева, щелкните по нему правой кнопкой мыши и выберите в контекстном меню пункт <b>Начать показ слайдов</b>.</li>
            </ul>
            <p>Просмотр начнется с выделенного в данный момент слайда. </p>
            <p>Можно также нажать на стрелку рядом со значком <b>Начать показ слайдов</b> <span class="icon icon-startpreview"></span> на вкладке <b>Главная</b> верхней панели инструментов и выбрать одну из доступных опций:</p>
            <ul>
                <li><b>Показ слайдов с начала</b> - чтобы начать показ слайдов с самого первого слайда,</li>
                <li><b>Показ слайдов с текущего слайда</b> - чтобы начать показ слайдов со слайда, выделенного в данный момент,</li>
                <li><b>Показ слайдов в режиме докладчика</b> - чтобы начать показ слайдов в режиме <b>докладчика</b>, позволяющем демонстрировать презентацию зрителям, не показывая заметок к слайдам, и одновременно просматривать презентацию с заметками к слайдам на другом мониторе.</li>
                <li>
                    <b>Параметры показа слайдов</b> - чтобы открыть окно настроек, позволяющее задать только один параметр: <b>Непрерывный цикл до нажатия клавиши 'Esc'</b>. Отметьте эту опцию в случае необходимости и нажмите кнопку <b>OK</b>. Если эта опция включена, презентация будет отображаться до тех пор, пока вы не нажмете клавишу <b>Escape</b> на клавиатуре, то есть, при достижении последнего слайда в презентации вы сможете снова перейти к первому слайду и так далее. Если эта опция отключена, то при достижении последнего слайда в презентации появится черный экран с информацией о том, что презентация завершена и вы можете выйти из режима <b>просмотра</b>.
                    <p><img alt="Окно Параметры показа слайдов" src="../images/showsettings.png" /></p>
                </li>
            </ul>
            <h3>Использование режима просмотра</h3>
            <p>В режиме <b>просмотра</b> можно использовать следующие элементы управления, расположенные в левом нижнем углу:</p>
            <p><img alt="Элементы управления в режиме просмотра" src="../images/preview_mode.png" /></p>
            <ul>
                <li>кнопка <b>Предыдущий слайд</b> <div class = "icon icon-previousslide"></div> позволяет вернуться к предыдущему слайду.</li>
                <li>кнопка <b>Приостановить презентацию</b> <div class = "icon icon-pausepresentation"></div> позволяет приостановить просмотр. После нажатия эта кнопка превращается в кнопку <div class = "icon icon-startpresentation"></div>.</li>
                <li>кнопка <b>Запустить презентацию</b> <div class = "icon icon-startpresentation"></div> позволяет возобновить просмотр. После нажатия эта кнопка превращается в кнопку <div class = "icon icon-pausepresentation"></div>.</li>
                <li>кнопка <b>Следующий слайд</b> <div class = "icon icon-nextslide"></div> позволяет перейти к следующему слайду.</li>
                <li><b>Указатель номера слайда</b> отображает номер текущего слайда, а также общее количество слайдов в презентации. Для перехода к определенному слайду в режиме просмотра щелкните по <b>Указателю номера слайда</b>, введите в открывшемся окне номер нужного слайда и нажмите клавишу <b>Enter</b>.</li>
                <li>кнопка <b>Полноэкранный режим</b> <div class = "icon icon-fullscreen"></div> позволяет перейти в полноэкранный режим.</li>
                <li>кнопка <b>Выйти из полноэкранного режима</b> <div class = "icon icon-exitfullscreen"></div> позволяет выйти из полноэкранного режима.</li>
                <li>кнопка <b>Завершить показ слайдов</b> <div class = "icon icon-closepreview"></div> позволяет выйти из режима просмотра.</li>
            </ul>
            <p>Для навигации по слайдам в режиме просмотра можно также использовать <a href="../HelpfulHints/KeyboardShortcuts.htm#preview" onclick="onhyperlinkclick(this)">сочетания клавиш</a>.</p>	
            <h3 id="presenter">Использование режима докладчика</h3>
            <p class="note"><b>Примечание</b>: в <em>десктопной версии</em> редакторов режим докладчика можно активировать только со вторым подключенным монитором.</p>
            <p>В режиме <b>докладчика</b> вы можете просматривать презентацию с заметками к слайдам в отдельном окне, и одновременно демонстрировать презентацию зрителям на другом мониторе, не показывая заметок к слайдам. Заметки к каждому слайду отображаются под областью просмотра слайда.</p>
            <p>Для навигации по слайдам можно использовать кнопки <span class="icon icon-previousslide"></span> и <span class="icon icon-nextslide"></span> или щелкать по слайдам в списке слева. Номера скрытых слайдов в списке перечеркнуты. Если вы захотите показать зрителям слайд, помеченный как скрытый, просто щелкните по нему в списке слайдов слева - слайд будет отображен.</p>
            <p>Можно использовать следующие элементы управления, расположенные под областью просмотра слайда:</p>
            <p><img alt="Элементы управления в режиме докладчика" src="../images/presenter_mode.png" /></p>
            <ul>
                <li><b>Таймер</b> показывает истекшее время презентации в формате <em>чч.мм.сс</em>.</li>
                <li>кнопка <b>Приостановить презентацию</b> <div class = "icon icon-presenter_pausepresentation"></div> позволяет приостановить просмотр. После нажатия эта кнопка превращается в кнопку <div class = "icon icon-presenter_startpresentation"></div>.</li>
                <li>кнопка <b>Запустить презентацию</b> <div class = "icon icon-presenter_startpresentation"></div> позволяет возобновить просмотр. После нажатия эта кнопка превращается в кнопку <div class = "icon icon-presenter_pausepresentation"></div>.</li>
                <li>кнопка <b>Сброс</b> позволяет сбросить истекшее время презентации.</li>
                <li>кнопка <b>Предыдущий слайд</b> <div class = "icon icon-presenter_previousslide"></div> позволяет вернуться к предыдущему слайду.</li>
                <li>кнопка <b>Следующий слайд</b> <div class = "icon icon-presenter_nextslide"></div> позволяет перейти к следующему слайду.</li>
                <li><b>Указатель номера слайда</b> отображает номер текущего слайда, а также общее количество слайдов в презентации.</li>
                <li>кнопка <b>Указка</b> <div class = "icon icon-pointer"></div> позволяет выделить что-то на экране при показе презентации. Когда эта опция включена, кнопка выглядит следующим образом: <div class = "icon icon-pointer_enabled"></div>. Чтобы указать на какие-то объекты, наведите курсор мыши на область просмотра слайда и перемещайте указку по слайду. Указка будет выглядеть так: <div class = "icon icon-pointer_screen"></div>. Чтобы отключить эту опцию, нажмите кнопку <div class = "icon icon-pointer_enabled"></div> еще раз.</li>
                <li>кнопка <b>Завершить показ слайдов</b> позволяет выйти из режима <b>докладчика</b>.</li>
            </ul>    	
		</div>
	</body>
</html>