<!DOCTYPE html>
<html>
	<head>
		<title>Рисование от руки на слайде</title>
		<meta charset="utf-8" />
		<meta name="description" content="Операции рисования: ручка, мар<PERSON><PERSON><PERSON>, ластик" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Рисование от руки на слайде</h1>
			<p>В <a href="https://www.onlyoffice.com/ru/presentation-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редакторе презентаций</b></a>, вы можете использовать вкладку <b>Рисование</b>, чтобы выделять в документе текст маркером, рисовать от руки, добавлять рукописные заметки и стирать их.</p>
			<p><img alt="Функции рисования" src="../../../../../../common/main/resources/help/ru/images/draw_tools.png" /></p>
			<p>Чтобы начать рисовать, писать или выделять текст, нажмите значок <b>Ручка</b> или <b>Маркер</b> и перемещайте курсор. Щелкните стрелку раскрывающегося списка, чтобы настроить цвет и толщину обводки. Нажмите <b>Другие цвета</b>, если нужного цвета нет в палитре.</p>
			<p><img alt="Инструмент Ручка" src="../../../../../../common/main/resources/help/ru/images/pentool.png" /></p>
			<p>Когда вы закончите рисовать, писать или выделять, снова щелкните значок <b>Ручка</b> или <b>Маркер</b> или нажмите клавишу <b>Esc</b>.</p>
			<p>Щелкните на <b>Ластик</b> и перемещайте курсор вперед и назад, чтобы стереть рисунок. <b>Ластик</b> удаляет только весь рисунок.</p>
			<p>Используйте функцию <b>Выделить</b>, чтобы выбрать надпись или рисунок. После выбора вы можете изменить размер или удалить выбранный элемент.</p>
		</div>
	</body>
</html>