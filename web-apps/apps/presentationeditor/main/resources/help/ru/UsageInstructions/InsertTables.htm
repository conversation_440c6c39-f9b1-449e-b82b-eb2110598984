<!DOCTYPE html>
<html>
	<head>
		<title>Вставка и форматирование таблиц</title>
		<meta charset="utf-8" />
		<meta name="description" content="Добавьте в презентацию таблицу и настройте ее свойства" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка и форматирование таблиц</h1>
            <h3>Вставка таблицы</h3>
			<p>Для вставки таблицы на слайд:</p>
			<ol>
				<li>выберите слайд, на который надо добавить таблицу,</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>нажмите значок <div class = "icon icon-table"></div> <b>Таблица</b> на верхней панели инструментов,</li>
				<li>выберите опцию для создания таблицы:
					<ul>
						<li><p>или таблица со стандартным количеством ячеек (максимум 10 на 8 ячеек)</p>
						<p>Если требуется быстро добавить таблицу, просто выделите мышью нужное количество строк (максимум 8) и столбцов (максимум 10).</p></li>
						<li><p>или пользовательская таблица</p>
						<p>Если Вам нужна таблица больше, чем 10 на 8 ячеек, выберите опцию <b>Вставить пользовательскую таблицу</b>, после чего откроется окно, в котором можно вручную ввести нужное количество строк и столбцов соответственно, затем нажмите кнопку <b>OK</b>.</p></li>
					</ul>
				</li>
                <li>
                    если вы хотите вставить таблицу как OLE-объект:
                    <ol>
                        <li>Выберите опцию <b>Вставить таблицу</b> в меню <b>Таблица</b> на вкладке <b>Вставка</b>.</li>
                        <li>
                            Появится соответствующее окно, в котором вы можете ввести нужные данные и отформатировать их, используя инструменты форматирования Редактора электронных таблиц, такие как <a href="../../../../../../spreadsheeteditor/main/resources/help/ru/UsageInstructions/FontTypeSizeStyle.htm">выбор шрифта, типа и стиля</a>, <a href="../../../../../../spreadsheeteditor/main/resources/help/ru/UsageInstructions/ChangeNumberFormat.htm">настройка числового формата</a>, <a href="../../../../../../spreadsheeteditor/main/resources/help/ru/UsageInstructions/InsertFunction.htm">вставка функций</a>, <a href="../../../../../../spreadsheeteditor/main/resources/help/ru/UsageInstructions/FormattedTables.htm">форматированные таблицы</a> и так далее.
                            <p><img alt="OLE-таблица" src="../../../../../../common/main/resources/help/ru/images/ole_table.png" /></p>
                        </li>
                        <li>В шапке в правом верхнем углу окна находится кнопка <span class="icon icon-visible_area"></span> <b>Видимая область</b>. Выберите опцию <b>Редактировать видимую область</b>, чтобы выбрать область, которая будет отображаться при вставке объекта в презентацию; другие данные не будут потеряны, а просто будут скрыты. Когда область будет выделена, нажмите кнопку <b>Готово</b>.</li>
                        <li>Выберите опцию <b>Показать видимую область</b>, чтобы увидеть выбранную область, у которой будет голубая граница.</li>
                        <li>Когда все будет готово, нажмите кнопку <b>Сохранить и выйти</b>.</li>
                    </ol>
                </li>
				<li>после того, как таблица будет добавлена, Вы сможете изменить ее свойства и положение.</li>
			</ol>
            <p>Вы также можете добавить таблицу внутри текстовой рамки, нажав на кнопку <span class="icon icon-placeholder_table"></span> <b>Таблица</b> в ней и выбрав нужное количество ячеек или опцию <b>Вставить пользовательскую таблицу</b>:</p>
            <p><img alt="Добавление таблицы в текстовую рамку" src="../images/placeholder_object.png" /></p>
            <p>Чтобы изменить размер таблицы, перетаскивайте маркеры <span class="icon icon-resize_square"></span>, расположенные по ее краям, пока таблица не достигнет нужного размера.</p>
            <p><span class="big big-resizetable"></span></p>
            <p>Вы также можете вручную изменить ширину определенного столбца или высоту строки. Наведите курсор мыши на правую границу столбца, чтобы курсор превратился в двунаправленную стрелку <span class="icon icon-changecolumnwidth"></span>, и перетащите границу влево или вправо, чтобы задать нужную ширину. Чтобы вручную изменить высоту отдельной строки, наведите курсор мыши на нижнюю границу строки, чтобы курсор превратился в двунаправленную стрелку <span class="icon icon-changerowheight"></span>, и перетащите границу вверх или вниз.</p>
			<p>Можно задать <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">положение таблицы</a> на слайде путем перетаскивания ее по вертикали или по горизонтали.</p>
            <p class="note">
                <b>Примечание</b>: для перемещения по таблице можно использовать <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithtables" onclick="onhyperlinkclick(this)">сочетания клавиш</a>.
            </p>
            <p>Также можно добавить таблицу в макет слайда. Для получения дополнительной информации вы можете обратиться к этой <a href="../UsageInstructions/SetSlideParameters.htm#addtolayout" onclick="onhyperlinkclick(this)">статье</a>.</p>
            <hr />
            <h3>Изменение параметров таблицы</h3>
			<img class="floatleft" alt="Вкладка Параметры таблицы" src="../images/tablesettingstab.png" />
			<p>Большинство свойств таблицы, а также ее структуру можно изменить с помощью правой боковой панели. Чтобы ее активировать, щелкните по таблице и выберите значок <b>Параметры таблицы</b> <span class="icon icon-table_settings_icon"></span> справа.</p>
			<p>Разделы <b>Строки</b> и <b>Столбцы</b>, расположенные наверху, позволяют выделить некоторые строки или столбцы при помощи особого форматирования, или выделить разные строки и столбцы с помощью разных цветов фона для их четкого разграничения. Доступны следующие опции:</p>
			<ul style="margin-left: 280px;">
				<li><b>Заголовок</b> - выделяет при помощи особого форматирования самую верхнюю строку в таблице.</li>
				<li><b>Итоговая</b> - выделяет при помощи особого форматирования самую нижнюю строку в таблице.</li>
				<li><b>Чередовать</b> - включает чередование цвета фона для четных и нечетных строк.</li>
				<li><b>Первый</b> - выделяет при помощи особого форматирования крайний левый столбец в таблице.</li>
				<li><b>Последний</b> - выделяет при помощи особого форматирования крайний правый столбец в таблице.</li>
				<li><b>Чередовать</b> - включает чередование цвета фона для четных и нечетных столбцов.</li>
			</ul>
			<p>Раздел <b>По шаблону</b> позволяет выбрать один из готовых стилей таблиц. Каждый шаблон сочетает в себе определенные параметры форматирования, такие как цвет фона, стиль границ, чередование строк или столбцов и т.д.
			Набор шаблонов отображается по-разному в зависимости от параметров, указанных в разделах <b>Строки</b> и/или <b>Столбцы</b> выше. Например, если Вы отметили опцию <b>Заголовок</b> в разделе <b>Строки</b> и опцию <b>Чередовать</b> в разделе <b>Столбцы</b>, отображаемый список шаблонов будет содержать только шаблоны со строкой заголовка и чередованием столбцов:</p>
			<p><span class="big big-templateslist"></span></p>
			<p>Раздел <b>Стиль границ</b> позволяет изменить примененное форматирование, соответствующее выбранному шаблону. Можно выделить всю таблицу или определенный диапазон ячеек, для которого необходимо изменить форматирование, и задать все параметры вручную.</p>
			<ul>
				<li>Параметры <b>Границ</b> - задайте толщину границы с помощью списка <div class = "big big-bordersize"></div> (или выберите опцию <b>Без границ</b>), выберите ее <b>Цвет</b> на доступных палитрах и определите, как они должны отображаться в ячейках, нажимая на значки: 
				<p><span class="big big-bordertype"></span></p>
				</li>
				<li><b>Цвет фона</b> - выберите цвет фона внутри выбранных ячеек.</li>
			</ul>
			<p>Раздел <b>Строки и столбцы</b> <span class="icon icon-rowsandcolumns"></span> позволяет выполнить следующие операции:</p>
			<ul>
				<li><b>Выбрать</b> строку, столбец, ячейку (в зависимости от позиции курсора) или всю таблицу.</li>
				<li><b>Вставить</b> новую строку выше или ниже выделенной, а также новый столбец слева или справа от выделенного.</li>
				<li><b>Удалить</b> строку, столбец (в зависимости от позиции курсора или выделения) или всю таблицу.</li>
				<li><b>Объединить ячейки</b> - чтобы объединить предварительно выделенные ячейки в одну.</li>
				<li><b>Разделить ячейку...</b> - чтобы разделить предварительно выделенную ячейку на определенное количество строк и столбцов. Эта команда вызывает следующее окно:
				<p><img alt="Окно разделения ячейки" src="../images/split_cells.png" /></p>
				<p>Укажите <b>Количество столбцов</b> и <b>Количество строк</b>, на которое необходимо разделить выбранную ячейку, и нажмите <b>OK</b>.</p>
				</li>
			</ul>
			<p class="note"><b>Примечание</b>: опции раздела <b>Строки и столбцы</b> также доступны из <b>контекстного меню</b>.</p>
            <p>Раздел <b>Размер ячейки</b> используется для изменения ширины и высоты выделенной ячейки. В этом разделе можно также <b>Выровнять высоту строк</b>, чтобы все выделенные ячейки имели одинаковую высоту, или <b>Выровнять ширину столбцов</b>, чтобы все выделенные ячейки имели одинаковую ширину. Опции <b>Выровнять высоту строк / ширину столбцов</b> также доступны из <b>контекстного меню</b>.</p>
			<hr />
            <h3>Изменение дополнительных параметров таблицы</h3>
			<p>Чтобы изменить дополнительные параметры таблицы, щелкните по таблице правой кнопкой мыши и выберите из контекстного меню опцию <b>Дополнительные параметры таблицы</b> или нажмите ссылку <b>Дополнительные параметры</b> на правой боковой панели. Откроется окно свойств таблицы:</p>
            <p><img alt="Свойства таблицы" src="../images/table_properties2.png" /></p>
            <p>На вкладке <b>Положение</b> можно задать следующие свойства:</p>
            <ul>
                <li><b>Размер</b> - используйте эту опцию, чтобы изменить ширину и/или высоту таблицы. Если нажата кнопка <b>Сохранять пропорции</b> <span class="icon icon-constantproportions"></span> (в этом случае она выглядит так: <span class="icon icon-constantproportionsactivated"></span>), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон таблицы.</li>
                <li><b>Позиция</b> - задайте точную позицию, используя поля <b>По горизонтали</b> и <b>По вертикали</b>, а также поле <b>От</b>, где доступны параметры <b>Верхний левый угол</b> и <b>По центру</b>.</li>
            </ul>
            <p><img alt="Свойства таблицы" src="../images/table_properties.png" /></p>
			<p>Вкладка <b>Поля</b> позволяет задать расстояние между текстом внутри ячейки и границами ячейки:</p>
			<ul>
				<li>введите нужные значения <b>Полей ячейки</b> вручную или</li>
				<li>установите флажок <b>Использовать поля по умолчанию</b>, чтобы применить предустановленные значения (при необходимости их тоже можно изменить).</li>
			</ul>
            <p><img alt="Свойства таблицы" src="../images/table_properties1.png" /></p>
            <p>Вкладка <b>Альтернативный текст</b> позволяет задать <b>Заголовок</b> и <b>Описание</b>, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит таблица.</p>
			<hr />
			<p>Для <b>форматирования введенного текста</b> внутри ячеек таблицы можно использовать <a href="../UsageInstructions/InsertText.htm" onclick="onhyperlinkclick(this)">значки на вкладке <b>Главная</b> верхней панели инструментов</a>. <b>Контекстное меню</b>, вызываемое правым щелчком мыши по таблице, содержит две дополнительных опции: </p>
			<ul>
			    <li><b>Вертикальное выравнивание в ячейках</b> - позволяет задать предпочтительный тип вертикального выравнивания текста внутри выделенных ячеек: <b>По верхнему краю</b>, <b>По центру</b>, или <b>По нижнему краю</b>.</li>
			    <li><b>Гиперссылка</b> - позволяет вставить гиперссылку в выделенную ячейку.</li>
			</ul>									
		</div>
	</body>
</html>
