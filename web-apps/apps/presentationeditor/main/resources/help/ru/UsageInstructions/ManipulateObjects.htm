<!DOCTYPE html>
<html>
	<head>
		<title>Работа с объектами на слайде</title>
		<meta charset="utf-8" />
		<meta name="description" content="Перемещайте, поворачивайте автофигуры и изображения, изменяйте их размер и форму автофигур." />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Работа с объектами на слайде</h1>
			<p>Можно изменять размер различных объектов, перемещать и поворачивать их на слайде вручную при помощи специальных маркеров. Можно также точно задать размеры некоторых объектов и их положение с помощью правой боковой панели или окна <b>Дополнительные параметры</b>.</p>
			<p class="note">
				<b>Примечание</b>: список сочетаний клавиш, которые можно использовать при работе с объектами, доступен <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithobjects" onclick="onhyperlinkclick(this)">здесь</a>.
			</p>
			<h3>Изменение размера объектов</h3>
			<p>Для изменения размера <b>автофигуры/изображения/диаграммы/таблицы/текстового поля</b> перетаскивайте маленькие квадраты <span class="icon icon-resize_square"></span>, расположенные по краям объекта. Чтобы сохранить исходные пропорции выбранного объекта при изменении размера, удерживайте клавишу <b>Shift</b> и перетаскивайте один из угловых значков.</p>
			<p><span class="big big-maintain_proportions"></span></p>
			<p>Чтобы задать точную ширину и высоту <b>диаграммы</b>, выделите ее на слайде и используйте раздел <b>Размер</b> на правой боковой панели, которая будет активирована.</p>
			<p>Чтобы задать точные размеры <b>изображения</b> или <b>автофигуры</b>, щелкните правой кнопкой мыши по нужному объекту на слайде и выберите пункт меню <b>Дополнительные параметры изображения/фигуры</b>. Укажите нужные значения на вкладке <b>Размер</b> окна <b>Дополнительные параметры</b> и нажмите кнопку <b>OK</b>.</p>
			<h3>Изменение формы автофигур</h3>
			<p>При изменении некоторых фигур, например, фигурных стрелок или выносок, также доступен желтый значок в форме ромба <span class="icon icon-yellowdiamond"></span>. Он позволяет изменять отдельные параметры формы, например, длину указателя стрелки.</p>
			<p><span class="big big-reshaping"></span></p>
			<p>Чтобы изменить форму автофигуры, вы также можете использовать опцию <b>Изменить точки</b> в <b>контекстном меню</b>.</p>
			<p><b>Изменить точки</b> используется для редактирования формы или изменения кривизны автофигуры.</p>
			<ol>
				<li>
					Чтобы активировать редактируемые опорные точки фигуры, щелкните по фигуре правой кнопкой мыши и в контекстном меню выберите пункт <b>Изменить точки</b>. Черные квадраты, которые становятся активными, — это точки, где встречаются две линии, а красная линия очерчивает фигуру. Щелкните и перетащите квадрат, чтобы изменить положение точки и изменить контур фигуры.
					<p><img alt="Изменить точки Меню" src="../images/editpoints_rightclick.png" /></p>
				</li>
				<li>
					После сдвига опорной точки фигуры, появятся две синие линии с белыми квадратами на концах. Это кривые Безье, которые позволяют создавать кривую и изменять ее значение.
					<p><span class="big big-editpoints_example"></span></p>
				</li>
				<li>
					Пока опорные точки активны, вы можете добавлять и удалять их:
					<ul>
						<li><b>Чтобы добавить точку к фигуре</b>, удерживайте <b>Ctrl</b> и щелкните место, где вы хотите добавить опорную точку.</li>
						<li><b>Чтобы удалить точку</b>, удерживайте <b>Ctrl</b> и щелкните по ненужной точке.</li>
					</ul>
				</li>
			</ol>
			<h3>Перемещение объектов</h3>
			<p>
				Для изменения местоположения <b>автофигуры/изображения/диаграммы/таблицы/текстового поля</b> используйте значок <span class="icon icon-arrow"></span>, который появляется после наведения курсора мыши на объект. Перетащите объект на нужное место, не отпуская кнопку мыши.
				Чтобы перемещать объект с шагом в один пиксель, удерживайте клавишу <b>Ctrl</b> и используйте стрелки на клавиатуре.
				Чтобы перемещать объект строго по горизонтали/вертикали и предотвратить его смещение в перпендикулярном направлении, при перетаскивании удерживайте клавишу <b>Shift</b>.
			</p>
			<p>Чтобы задать точное положение <b>изображения</b>, щелкните правой кнопкой мыши по изображению на слайде и выберите пункт меню <b>Дополнительные параметры изображения</b>. Укажите нужные значения в разделе <b>Положение</b> окна <b>Дополнительные параметры</b> и нажмите кнопку <b>OK</b>.</p>
			<h3>Поворот объектов</h3>
			<p>Чтобы вручную повернуть <b>автофигуру/изображение/текстовое поле</b>, наведите курсор мыши на маркер поворота <span class="icon icon-greencircle"></span> и перетащите его по часовой стрелке или против часовой стрелки. Чтобы ограничить угол поворота шагом в 15 градусов, при поворачивании удерживайте клавишу <b>Shift</b>.</p>
			<p>Чтобы повернуть объект на 90 градусов против часовой стрелки или по часовой стрелке или отразить объект по горизонтали или по вертикали, можно использовать раздел <b>Поворот</b> на правой боковой панели, которая будет активирована, как только вы выделите нужный объект. Чтобы открыть ее, нажмите на значок <b>Параметры фигуры</b> <span class="icon icon-shape_settings_icon"></span>  или <b>Параметры изображения</b> <span class="icon icon-image_settings_icon"></span> справа. Нажмите на одну из кнопок:</p>
			<ul>
				<li><div class = "icon icon-rotatecounterclockwise"></div> чтобы повернуть объект на 90 градусов против часовой стрелки</li>
				<li><div class = "icon icon-rotateclockwise"></div> чтобы повернуть объект на 90 градусов по часовой стрелке</li>
				<li><div class = "icon icon-fliplefttoright"></div> чтобы отразить объект по горизонтали (слева направо)</li>
				<li><div class = "icon icon-flipupsidedown"></div> чтобы отразить объект по вертикали (сверху вниз)</li>
			</ul>
			<p>Также можно щелкнуть правой кнопкой мыши по объекту, выбрать из контекстного меню пункт <b>Поворот</b>, а затем использовать один из доступных вариантов поворота объекта.</p>
			<p>Чтобы повернуть объект на точно заданный угол, нажмите на ссылку <b>Дополнительные параметры</b> на правой боковой панели и используйте вкладку <b>Поворот</b> в окне <b>Дополнительные параметры</b>. Укажите нужное значение в градусах в поле <b>Угол</b> и нажмите кнопку <b>OK</b>.</p>
		
		</div>
	</body>
</html>