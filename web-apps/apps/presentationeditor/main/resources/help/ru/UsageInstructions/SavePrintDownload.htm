<!DOCTYPE html>
<html>
	<head>
		<title>Сохранение / печать / скачивание презентации</title>
		<meta charset="utf-8" />
		<meta name="description" content="Сохраните, распечатайте и скачайте презентацию в разных форматах" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Сохранение / печать <span class="onlineDocumentFeatures">/ скачивание</span> презентации</h1>
            <h3>Сохранение</h3>
            <p class="onlineDocumentFeatures">По умолчанию онлайн-редактор презентаций автоматически сохраняет файл каждые 2 секунды, когда вы работаете над ним, чтобы не допустить потери данных в случае непредвиденного закрытия программы. Если вы совместно редактируете файл в <b>Быстром</b> режиме, таймер запрашивает наличие изменений 25 раз в секунду и сохраняет их, если они были внесены. При совместном редактировании файла в <b>Строгом</b> режиме изменения автоматически сохраняются каждые 10 минут. При необходимости можно легко выбрать предпочтительный режим совместного редактирования или отключить функцию автоматического сохранения на странице <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Дополнительные параметры</a>.</p>
            <p>Чтобы сохранить презентацию вручную в текущем формате и местоположении,</p>
            <ul>
                <li>нажмите значок <b>Сохранить</b> <div class="icon icon-save"></div> в левой части шапки редактора, или</li>
                <li>используйте сочетание клавиш <b>Ctrl+S</b>, или</li>
                <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов и выберите опцию <b>Сохранить</b>.</li>
            </ul>
            <p class="note desktopDocumentFeatures">Чтобы не допустить потери данных в <em>десктопной версии</em> в случае непредвиденного закрытия программы, вы можете включить опцию <b>Автовосстановление</b> на странице <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Дополнительные параметры</a>. </p>
            <div class="desktopDocumentFeatures">
                <p>Чтобы в <em>десктопной версии</em> сохранить презентацию под другим именем, в другом местоположении или в другом формате,</p>
                <ol>
                    <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов,</li>
                    <li>выберите опцию <b>Сохранить как</b>,</li>
                    <li>выберите один из доступных форматов: PPTX, ODP, PDF, PDF/A, PNG, JPG. Также можно выбрать вариант <b>Шаблон презентации</b> POTX или OTP.</li>
                </ol>
            </div>
            <div class="onlineDocumentFeatures">
                <h3>Скачивание</h3>
                <p>Чтобы в <em>онлайн-версии</em> скачать готовую презентацию и сохранить ее на жестком диске компьютера,</p>
                <ol>
                    <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов,</li>
                    <li>выберите опцию <b>Скачать как</b>,</li>
                    <li>выберите один из доступных форматов: PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG.</li>
                </ol>
                <h3>Сохранение копии</h3>
                <p>Чтобы в <em>онлайн-версии</em> сохранить копию презентации на портале,</p>
                <ol>
                    <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов,</li>
                    <li>выберите опцию <b>Сохранить копию как</b>,</li>
                    <li>выберите один из доступных форматов: PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG,</li>
                    <li>выберите местоположение файла на портале и нажмите <b>Сохранить</b>.</li>
                </ol>
            </div>
            <h3 id="print">Печать</h3>
            <p>Чтобы распечатать текущую презентацию,</p>
            <ul>
                <li>нажмите значок <b>Напечатать файл</b> <div class="icon icon-print"></div> в левой части шапки редактора, или</li>
                <li>используйте сочетание клавиш <b>Ctrl+P</b>, или</li>
                <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов и выберите опцию <b>Печать</b>.</li>
            </ul>
            <div class="note">
                В браузере Firefox возможна печатать презентации без предварительной загрузки в виде файла .pdf.
            </div>
            <p><img alt="Print Settings window" src="../images/printsettingswindow.png" /></p>
            <p>В открывшемся окне <b>Печать</b> настройте следующие параметры:</p>
            <ul id="printtitles">
                <li><b>Получатель</b> — выберите получателя файла для печати, например, <b>Сохранить в PDF</b>, <b>Microsoft XPS Document Writer</b>, <b>Microsoft Print в PDF</b>, <b>Fax</b> и т.д.</li>
                <li><b>Ориентация</b> - выберите опцию <b>Книжная</b>, если вы хотите печатать на странице вертикально, или выберите <b>Альбомная</b> для горизонтальной печати.</li>
                <li><b>Страницы</b> - выберите один из вариантов печати страниц: <b>Все</b>, <b>Текущая</b>, <b>Нечетные</b>, < b>Четные</b> или <b>Диапазон</b>. В последнем случае вам придется ввести количество страниц вручную.</li>
                <li>
                    <b>Цветной режим</b> - выберите, хотите ли вы, чтобы ваш файл был напечатан в <b>Цветном</b> или в <b>Черно-белом</b> формате. Обратите внимание, что этот параметр доступен, если в качестве <b>Получателя</b> выбрана опция <em>Microsoft XPS Document Writer</em>. Если в качестве <b>Получателя</b> выбрана опция <em>Fax</em>, то по умолчанию задан цветовой режим <b>Черно-белый</b>.
                    <p>Нажмите на заголовок <b>Все настройки</b>, чтобы открыть дополнительные настройки.</p>
                </li>
                <li><b>Размер бумаги</b> — выберите один из доступных размеров из раскрывающегося списка или задайте пользовательский.</li>
                <li><b>Масштаб</b> - выберите масштабирование файла при печати; вы можете выбрать опцию <b>По ширине страницы</b> или установить масштаб вручную с помощью переключателя <b>Масштаб</b> и соответствующего поля ввода рядом.</li>
                <li><b>Страниц на одном листе</b> — выберите количество страниц, печатаемых на одном листе, например, <b>две</b>, <b>шесть</b>, <b>девять</b> и т.д.</li>
                <li><b>Поля</b> — выберите поля страницы. Вы можете установить поля <b>По умолчанию</b> или пользовательские, измеряемые в милиметрах. Для пользовательских полей установите необходимые значения для верхнего, нижнего, левого и правого поля вручную. Вы также можете выбрать опцию <b>Нет</b>, чтобы не печатать поля.</li>
                <li><b>Настройки</b>. Установите флажок у <b>Печатать колонтитулы</b>, чтобы они печатались, или снимите этот флажок, чтобы верхние и нижние колонтитулы не печатались.</li>
                <li><b>Печать, используя системный диалог</b> – нажмите на этот заголовок, чтобы открыть системный диалог для настройки процесса печати.</li>
            </ul>
            <p><span class="desktopDocumentFeatures">В <em>десктопной версии</em> презентация будет напрямую отправлена на печать. Вы можете открыть и распечатать его, или сохранить его на жестком диске компьютера или съемном носителе чтобы распечатать позже.</span></p>
            <p>Также можно распечатать выделенные слайды с помощью пункта контекстного меню <b>Напечатать выделенное</b> как в режиме <b>Редактирования</b>, так и в режиме <b>Просмотра</b> (<b>кликните правой кнопкой мыши</b> по выделенным слайдам и выберите опцию <b>Напечатать выделенное</b>).</p>
            <p><span class="onlineDocumentFeatures">В <em>онлайн-версии</em> на основе презентации будет создан PDF-файл. Вы можете открыть и распечатать его или сохранить на жестком диске компьютера или съемном носителе, чтобы распечатать его позже. Некоторые браузеры (например, Chrome и Opera) поддерживают прямую печать.</span></p>
        </div>
	</body>
</html>