	<!DOCTYPE html>
	<html>
	<head>
		<title>Создание анимации пути перемещения</title>
		<meta charset="utf-8" />
		<meta name="description" content="Описание добавления к объектам анимации пути перемещения." />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<script type="text/javascript" src="../callback.js"></script>
		<script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Создание анимации пути перемещения</h1>
            <p><b>Пути перемещения</b> входят в состав галереи эффектов анимации, они определяют движение объекта и путь, по которому он следует. Значки в галерее обозначают предлагаемый путь. <b>Галерея анимаций</b> доступна на вкладке <b>Анимация</b> верхней панели инструментов.</p>
            <h3 id="applyanimation">Применение эффекта анимации пути перемещения</h3>
            <ol>
                <li>перейдите на вкладку <b>Анимация</b> верхней панели инструментов,</li>
                <li>выберите текст, объект или графический элемент, к которому вы хотите применить эффект анимации,</li>
                <li>выберите один из готовых шаблонов пути перемещения в разделе <b>Пути перемещения</b> галереи анимаций (<em>Линии</em>, <em>Дуги</em> и другие) или выберите опцию <b>Пользовательский путь</b>, если вы хотите создать собственный путь.</li>
            </ol>
            <p>Вы можете предварительно просмотреть эффекты анимации на текущем слайде. По умолчанию, когда вы добавляете анимационные эффекты на слайд, они воспроизводятся автоматически, но вы можете отключить эту функцию. Откройте раскрывающийся список <b>Просмотр</b> на вкладке <b>Анимация</b> и выберите режим предварительного просмотра:</p>
            <ul>
                <li><b>Просмотр</b> - чтобы включать предпросмотр по нажатию кнопки <b>Просмотр</b>,</li>
                <li><b>Автопросмотр</b> - чтобы предпросмотр начинался автоматически при добавлении анимации к слайду.</li>
            </ul>

            <h3>Добавление эффекта анимации пользовательского пути</h3> 
            <p>Чтобы нарисовать пользовательский путь,</p> 
            <ol>
                <li>Щелкните по объекту, для которого требуется задать анимацию Пользовательский путь.</li>
                <li>Отметьте точки пути левой кнопкой мыши. При каждом клике левой кнопкой мыши рисуется линия, а удерживание левой кнопки мыши позволяет создать любую нужную кривую. 
                    <p>Начальная точка пути будет обозначена зеленой стрелкой, а конечная точка - красной.</p>
                <p><div class="big big-custom_path"></div></p></li>
                <li>Когда все будет готово, дважды щелкните левой кнопкой мыши или нажмите клавишу <code>Esc</code>, чтобы закончить рисование пути.</li>
            </ol>

            <h3>Редактирование точек пути перемещения</h3>
            <ol>
                <li>
                    Чтобы отредактировать точки пути перемещения, выделите объект пути, щелкните правой кнопкой мыши, чтобы открыть контекстное меню, и выберите опцию <b>Изменить точки</b>.
                    <p><div class="big big-edit_points"></div></p>
                    <p>Перетаскивайте <b>черные</b> квадраты, чтобы скорректировать позицию узлов точек пути; перетаскивайте <b>белые</b> квадраты, чтобы изменить направление линии на входе и выходе из узла. Нажмите <b>Esc</b> или щелкните за пределами объекта пути, чтобы выйти из режима редактирования.</p>
                </li>
                <li>Вы можете масштабировать путь перемещения, щелкнув по нему и перетаскивая квадратные маркеры по краям объекта.</li>
            </ol>
        </div>
		</body>
	</html>
