<!DOCTYPE html>
<html>
    <head>
        <title>Вставка символов и знаков</title>
        <meta charset="utf-8" />
        <meta name="description" content="Советы по совместному редактированию" />
        <link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
    </head>
    <body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вставка символов и знаков</h1>
            <p>При работе в <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Редакторе презентаций</b></a> может возникнуть необходимость поставить символ, которого нет на вашей клавиатуре. Для вставки таких символов используйте функцию <span class="icon icon-insert_symbol_icon"></span> <b>Вставить символ</b>.</p>
            <p>Для этого выполните следующие шаги:</p>
            <ol>
                <li>Установите курсор, куда будет помещен символ,</li>
                <li>Перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>Щелкните по значку <div class="icon icon-insert_symbol_icon"></div> <b>Символ</b>,</li>
                <li>
                    Выберите опцию <b>Другие символы</b>,
                    <p><img alt="Таблица символов панель слева" src="../images/insert_symbol_window.png" /></p>
                </li>
                <li>Откроется окно <b>Символ</b>, и вы сможете выбрать нужный символ,</li>
                <li>
                    Используйте раздел <b>Набор</b>, чтобы быстрее найти нужный символ. В нем все символы распределены по группам, например, выберите «Символы валют», если нужно вставить знак валют.
                    <ul>
                        <li>Если же данный символ отсутствует в наборе, выберите другой шрифт. Во многих из них также есть символы, отличные от стандартного набора.</li>
                        <li>Или же впишите в строку шестнадцатеричный <b>Код знака из Юникод</b> нужного вам символа. Данный код можно найти в <b>Таблице символов</b>.</li>
                        <li>
                            Также можно использовать вкладку <b>Специальные символы</b> для выбора специального символа из списка.
                            <p>Ранее использованные символы также отображаются в поле <b>Ранее использовавшиеся символы</b>.</p>
                            <p><img alt="Таблица символов панель слева " src="../images/insert_symbol_window2.png" /></p>
                        </li>
                    </ul>
                </li>
                <li>нажмите <b>Вставить</b>. Выбранный символ будет добавлен в презентацию.</li>
                <li>Чтобы вставить популярный или недавно использовавшийся символ, щелкните стрелку под значком <div class="icon icon-insert_symbol_icon"></div> <b>Символ</b>. Самые последние символы будут отображаться в начале списка.</li>
            </ol>

            <h2>Вставка символов ASCII</h2>
            <p>Для добавления символов также используется таблица символов ASCII.</p>
            <p>Для этого зажмите клавишу ALT и при помощи цифровой клавиатуры введите код знака.</p>
            <p class="note"><b>Обратите внимание</b>: убедитесь, что используете цифровую клавиатуру, а не цифры на основной клавиатуре. Чтобы включить цифровую клавиатуру, нажмите клавишу <code>Num Lock</code>.</p>
            <p>Например, для добавления символа параграфа (§) нажмите и удерживайте клавишу ALT, введите цифры 789, а затем отпустите клавишу ALT.</p>

            <h2>Вставка символов при помощи таблицы символов</h2>
            <p>С помощью таблицы символов Windows так же можно найти символы, которых нет на клавиатуре. Чтобы открыть данную таблицу, выполните одно из следующих действий:</p>
            <ul>
                <li>в строке <b>Поиск</b> напишите «Таблица символов» и откройте ее</li>
                <li>
                    одновременно нажмите клавиши <code>Win+R</code>, в появившемся окне введите <code>charmap.exe</code> и щелкните ОК.
                    <br /><img alt="Таблица символов окно" src="../images/insert_symbols_windows.png" />
                </li>
            </ul>
            <p>В открывшемся окне <b>Таблица символов</b> выберите один из представленных <b>Набор символов</b>, их <b>Группировку</b> и <b>Шрифт</b>. Далее щелкните на нужные символы, скопируйте их в буфер обмена и вставьте в нужное место в презентации.</p>
        </div>
    </body>
</html>