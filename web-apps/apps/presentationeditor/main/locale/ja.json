{"Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Desktop.hintBtnHome": "メインウィンドウを表示する", "Common.Controllers.Desktop.itemCreateFromTemplate": "テンプレートから作成", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "匿名", "Common.Controllers.ExternalDiagramEditor.textClose": "閉じる", "Common.Controllers.ExternalDiagramEditor.warningText": "他のユーザーが編集しているのためオブジェクトが無効になります。", "Common.Controllers.ExternalDiagramEditor.warningTitle": "警告", "Common.Controllers.ExternalOleEditor.textAnonymous": "匿名", "Common.Controllers.ExternalOleEditor.textClose": "閉じる", "Common.Controllers.ExternalOleEditor.warningText": "他のユーザーが編集しているのためオブジェクトが無効になります。", "Common.Controllers.ExternalOleEditor.warningTitle": "警告", "Common.Controllers.History.notcriticalErrorTitle": "警告", "Common.Controllers.History.txtErrorLoadHistory": "履歴の読み込みに失敗しました", "Common.Controllers.Plugins.helpUseMacros": "「マクロ」ボタンはここに移動しました", "Common.Controllers.Plugins.helpUseMacrosHeader": "マクロへのアクセスを更新しました", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "プラグインは正常にインストールされました。すべてのバックグラウンドプラグインは、ここにアクセスできます。", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b>は正常にインストールされました。すべてのバックグラウンドプラグインは、ここにアクセスできます。", "Common.Controllers.Plugins.textRunInstalledPlugins": "インストールされたプラグインの実行", "Common.Controllers.Plugins.textRunPlugin": "プラグインの実行", "Common.define.chartData.textArea": "面グラフ", "Common.define.chartData.textAreaStacked": "積み上げ面", "Common.define.chartData.textAreaStackedPer": "スタック領域 100%", "Common.define.chartData.textBar": "横棒グラフ", "Common.define.chartData.textBarNormal": "集合縦棒", "Common.define.chartData.textBarNormal3d": "3-D 集合縦棒", "Common.define.chartData.textBarNormal3dPerspective": "3-D 縦棒", "Common.define.chartData.textBarStacked": "積み上げ縦棒", "Common.define.chartData.textBarStacked3d": "3-D 積み上げ縦棒", "Common.define.chartData.textBarStackedPer": "積み上げ縦棒 100% ", "Common.define.chartData.textBarStackedPer3d": "3-D 積み上げ縦棒 100% ", "Common.define.chartData.textCharts": "グラフ", "Common.define.chartData.textColumn": "縦棒グラフ", "Common.define.chartData.textCombo": "複合", "Common.define.chartData.textComboAreaBar": "積み上げ面 - 集合縦棒", "Common.define.chartData.textComboBarLine": "集合縦棒 - 線", "Common.define.chartData.textComboBarLineSecondary": "集合縦棒 - 第2軸の折れ線", "Common.define.chartData.textComboCustom": "カスタム組み合わせ", "Common.define.chartData.textDoughnut": "ドーナツ", "Common.define.chartData.textHBarNormal": "集合横棒", "Common.define.chartData.textHBarNormal3d": "3-D 集合横棒", "Common.define.chartData.textHBarStacked": "積み上げ横棒", "Common.define.chartData.textHBarStacked3d": "3-D 積み上げ横棒", "Common.define.chartData.textHBarStackedPer": "積み上げ横棒 100％", "Common.define.chartData.textHBarStackedPer3d": "3-D 積み上げ横棒 100% ", "Common.define.chartData.textLine": "グラフ", "Common.define.chartData.textLine3d": "3-D 折れ線", "Common.define.chartData.textLineMarker": "マーカー付き折れ線", "Common.define.chartData.textLineStacked": "積み上げ折れ線", "Common.define.chartData.textLineStackedMarker": "マーク付き積み上げ折れ線", "Common.define.chartData.textLineStackedPer": "積み上げ折れ線 100% ", "Common.define.chartData.textLineStackedPerMarker": "マーカー付き 積み上げ折れ線 100% ", "Common.define.chartData.textPie": "円グラフ", "Common.define.chartData.textPie3d": "3-D 円グラフ", "Common.define.chartData.textPoint": "XY (散布図)", "Common.define.chartData.textRadar": "レーダーチャート", "Common.define.chartData.textRadarFilled": "塗りつぶしレーダー", "Common.define.chartData.textRadarMarker": "マーカー付きレーダー", "Common.define.chartData.textScatter": "散布図", "Common.define.chartData.textScatterLine": "直線付き散布図", "Common.define.chartData.textScatterLineMarker": "マーカーと直線付き散布図", "Common.define.chartData.textScatterSmooth": "平滑線付き散布図", "Common.define.chartData.textScatterSmoothMarker": "マーカーと平滑線付き散布図", "Common.define.chartData.textStock": "株価グラフ", "Common.define.chartData.textSurface": "表面", "Common.define.effectData.textAcross": "横方向", "Common.define.effectData.textAppear": "表示", "Common.define.effectData.textArcDown": "アーチ (下)", "Common.define.effectData.textArcLeft": "アーチ (左)", "Common.define.effectData.textArcRight": "アーチ (右)", "Common.define.effectData.textArcs": "アーチ", "Common.define.effectData.textArcUp": "アーチ (上)", "Common.define.effectData.textBasic": "基本", "Common.define.effectData.textBasicSwivel": "ベーシックスイベル", "Common.define.effectData.textBasicZoom": "ベーシックズーム", "Common.define.effectData.textBean": "豆", "Common.define.effectData.textBlinds": "ブラインド", "Common.define.effectData.textBlink": "ブリンク", "Common.define.effectData.textBoldFlash": "ボールドフラッシュ", "Common.define.effectData.textBoldReveal": "太字表示", "Common.define.effectData.textBoomerang": "ブーメラン", "Common.define.effectData.textBounce": "バウンド", "Common.define.effectData.textBounceLeft": "バウンド (左へ)", "Common.define.effectData.textBounceRight": "バウンド (右へ)", "Common.define.effectData.textBox": "ボックス", "Common.define.effectData.textBrushColor": "ブラシの色", "Common.define.effectData.textCenterRevolve": "リボルブ", "Common.define.effectData.textCheckerboard": "チェッカーボード", "Common.define.effectData.textCircle": "円", "Common.define.effectData.textCollapse": "折りたたみ", "Common.define.effectData.textColorPulse": "カラーパルス", "Common.define.effectData.textComplementaryColor": "補色", "Common.define.effectData.textComplementaryColor2": "補色２", "Common.define.effectData.textCompress": "圧縮", "Common.define.effectData.textContrast": "コントラスト", "Common.define.effectData.textContrastingColor": "カラーコントラスト", "Common.define.effectData.textCredits": "クレジット", "Common.define.effectData.textCrescentMoon": "三日月", "Common.define.effectData.textCurveDown": "カーブ (下)", "Common.define.effectData.textCurvedSquare": "四角形 (曲線)", "Common.define.effectData.textCurvedX": "曲線 (X 型)", "Common.define.effectData.textCurvyLeft": "湾曲カーブ (左)", "Common.define.effectData.textCurvyRight": "湾曲カーブ (右)", "Common.define.effectData.textCurvyStar": "星 (曲線)", "Common.define.effectData.textCustomPath": "カスタムパス", "Common.define.effectData.textCuverUp": "カーブ (上)", "Common.define.effectData.textDarken": "暗く", "Common.define.effectData.textDecayingWave": "波線 (減衰曲線)", "Common.define.effectData.textDesaturate": "彩度を下げる", "Common.define.effectData.textDiagonalDownRight": "対角線 (右下へ)", "Common.define.effectData.textDiagonalUpRight": "対角線 (右上へ)", "Common.define.effectData.textDiamond": "ひし型", "Common.define.effectData.textDisappear": "消失", "Common.define.effectData.textDissolveIn": "ディゾルブイン", "Common.define.effectData.textDissolveOut": "ディゾルブアウト", "Common.define.effectData.textDown": "下", "Common.define.effectData.textDrop": "ドロップ", "Common.define.effectData.textEmphasis": "強調効果", "Common.define.effectData.textEntrance": "開始効果", "Common.define.effectData.textEqualTriangle": "正三角形", "Common.define.effectData.textExciting": "華やか", "Common.define.effectData.textExit": "終了効果", "Common.define.effectData.textExpand": "拡張する", "Common.define.effectData.textFade": "フェード", "Common.define.effectData.textFigureFour": "8 の字 (ダブル)", "Common.define.effectData.textFillColor": "塗りつぶしの色", "Common.define.effectData.textFlip": "反転する", "Common.define.effectData.textFloat": "フロート", "Common.define.effectData.textFloatDown": "フロートダウン", "Common.define.effectData.textFloatIn": "フロートイン", "Common.define.effectData.textFloatOut": "フロートアウト", "Common.define.effectData.textFloatUp": "フロートアップ", "Common.define.effectData.textFlyIn": "スライドイン", "Common.define.effectData.textFlyOut": "スライドアウト", "Common.define.effectData.textFontColor": "フォントの色", "Common.define.effectData.textFootball": "フットボール", "Common.define.effectData.textFromBottom": "下から", "Common.define.effectData.textFromBottomLeft": "左下から", "Common.define.effectData.textFromBottomRight": "右下から", "Common.define.effectData.textFromLeft": "左から", "Common.define.effectData.textFromRight": "右から", "Common.define.effectData.textFromTop": "上から", "Common.define.effectData.textFromTopLeft": "左上から", "Common.define.effectData.textFromTopRight": "右上から", "Common.define.effectData.textFunnel": "漏斗", "Common.define.effectData.textGrowShrink": "拡大/収縮", "Common.define.effectData.textGrowTurn": "グローとターン", "Common.define.effectData.textGrowWithColor": "カラーで拡大", "Common.define.effectData.textHeart": "ハート", "Common.define.effectData.textHeartbeat": "ハートビート", "Common.define.effectData.textHexagon": "六角形", "Common.define.effectData.textHorizontal": "水平", "Common.define.effectData.textHorizontalFigure": "8 の字 (横)", "Common.define.effectData.textHorizontalIn": "ワイプイン (横)", "Common.define.effectData.textHorizontalOut": "ワイプアウト (横)", "Common.define.effectData.textIn": "中に", "Common.define.effectData.textInFromScreenCenter": "画面中央から中に", "Common.define.effectData.textInSlightly": "少しだけ中", "Common.define.effectData.textInToScreenBottom": "画面下に", "Common.define.effectData.textInvertedSquare": "四角形 (転回)", "Common.define.effectData.textInvertedTriangle": "三角形 (転回)", "Common.define.effectData.textLeft": "左", "Common.define.effectData.textLeftDown": "左下", "Common.define.effectData.textLeftUp": "左上", "Common.define.effectData.textLighten": "明るく", "Common.define.effectData.textLineColor": "線の色", "Common.define.effectData.textLines": "線", "Common.define.effectData.textLinesCurves": "線と曲線", "Common.define.effectData.textLoopDeLoop": "ループ", "Common.define.effectData.textLoops": "ループ", "Common.define.effectData.textModerate": "標準", "Common.define.effectData.textNeutron": "ニュートロン", "Common.define.effectData.textObjectCenter": "オブジェクトの中央", "Common.define.effectData.textObjectColor": "オブジェクトの色", "Common.define.effectData.textOctagon": "八角形", "Common.define.effectData.textOut": "外", "Common.define.effectData.textOutFromScreenBottom": "画面下部から外へ", "Common.define.effectData.textOutSlightly": "少しだけ外", "Common.define.effectData.textOutToScreenCenter": "画面中央へ", "Common.define.effectData.textParallelogram": "平行四辺形", "Common.define.effectData.textPath": "モーションパス", "Common.define.effectData.textPathCurve": "曲線", "Common.define.effectData.textPathLine": "線", "Common.define.effectData.textPathScribble": "フリーハンド", "Common.define.effectData.textPeanut": "ピーナッツ", "Common.define.effectData.textPeekIn": "ピークイン", "Common.define.effectData.textPeekOut": "ピークアウト", "Common.define.effectData.textPentagon": "五角形", "Common.define.effectData.textPinwheel": "ピンウィール", "Common.define.effectData.textPlus": "プラス", "Common.define.effectData.textPointStar": "ポイントスター", "Common.define.effectData.textPointStar4": "４ポイントスター", "Common.define.effectData.textPointStar5": "５ポイントスター", "Common.define.effectData.textPointStar6": "６ポイントスター", "Common.define.effectData.textPointStar8": "８ポイントスター", "Common.define.effectData.textPulse": "パルス", "Common.define.effectData.textRandomBars": "ランダムストライプ", "Common.define.effectData.textRight": "右", "Common.define.effectData.textRightDown": "右下", "Common.define.effectData.textRightTriangle": "直角三角形", "Common.define.effectData.textRightUp": "右上", "Common.define.effectData.textRiseUp": "ライズアップ", "Common.define.effectData.textSCurve1": "カーブ S 型 (1)", "Common.define.effectData.textSCurve2": "カーブ S 型 (2)", "Common.define.effectData.textShape": "図形", "Common.define.effectData.textShapes": "図形", "Common.define.effectData.textShimmer": "シマー", "Common.define.effectData.textShrinkTurn": "縮小および回転", "Common.define.effectData.textSineWave": "波線 (正弦曲線)", "Common.define.effectData.textSinkDown": "シンクダウン", "Common.define.effectData.textSlideCenter": "スライドの中央", "Common.define.effectData.textSpecial": "特殊", "Common.define.effectData.textSpin": "スピン", "Common.define.effectData.textSpinner": "スピナー", "Common.define.effectData.textSpiralIn": "内側にスパイラル", "Common.define.effectData.textSpiralLeft": "左へスパイラル", "Common.define.effectData.textSpiralOut": "外側にスパイラル", "Common.define.effectData.textSpiralRight": "右へスパイラル", "Common.define.effectData.textSplit": "分割", "Common.define.effectData.textSpoke1": "1スポーク", "Common.define.effectData.textSpoke2": "2スポーク", "Common.define.effectData.textSpoke3": "3スポーク", "Common.define.effectData.textSpoke4": "4スポーク", "Common.define.effectData.textSpoke8": "8スポーク", "Common.define.effectData.textSpring": "スプリング", "Common.define.effectData.textSquare": "四角", "Common.define.effectData.textStairsDown": "下り階段", "Common.define.effectData.textStretch": "ストレッチ", "Common.define.effectData.textStrips": "ストリップ", "Common.define.effectData.textSubtle": "弱", "Common.define.effectData.textSwivel": "スイベル", "Common.define.effectData.textSwoosh": "スウッシュ", "Common.define.effectData.textTeardrop": "涙の滴", "Common.define.effectData.textTeeter": "シーソー", "Common.define.effectData.textToBottom": "下へ", "Common.define.effectData.textToBottomLeft": "左下へ", "Common.define.effectData.textToBottomRight": "右下へ", "Common.define.effectData.textToLeft": "左へ", "Common.define.effectData.textToRight": "右へ", "Common.define.effectData.textToTop": "上へ", "Common.define.effectData.textToTopLeft": "左上へ", "Common.define.effectData.textToTopRight": "右上へ", "Common.define.effectData.textTransparency": "透過性", "Common.define.effectData.textTrapezoid": "台形", "Common.define.effectData.textTurnDown": "ターン (下へ)", "Common.define.effectData.textTurnDownRight": "ターン (右下へ)", "Common.define.effectData.textTurns": "ターン", "Common.define.effectData.textTurnUp": "ターン (上へ)", "Common.define.effectData.textTurnUpRight": "ターン (右上へ)", "Common.define.effectData.textUnderline": "アンダーライン", "Common.define.effectData.textUp": "上", "Common.define.effectData.textVertical": "縦", "Common.define.effectData.textVerticalFigure": "8 の字 (縦)", "Common.define.effectData.textVerticalIn": "縦（中）", "Common.define.effectData.textVerticalOut": "縦（外）", "Common.define.effectData.textWave": "波", "Common.define.effectData.textWedge": "くさび形", "Common.define.effectData.textWheel": "ホイール", "Common.define.effectData.textWhip": "ホイップ", "Common.define.effectData.textWipe": "ワイプ", "Common.define.effectData.textZigzag": "ジグザグ", "Common.define.effectData.textZoom": "ズーム", "Common.define.gridlineData.txtCm": "センチ", "Common.define.gridlineData.txtPt": "pt", "Common.define.smartArt.textAccentedPicture": "アクセント付きの図", "Common.define.smartArt.textAccentProcess": "アクセントプロセス", "Common.define.smartArt.textAlternatingFlow": "波型ステップ", "Common.define.smartArt.textAlternatingHexagons": "左右交替積み上げ六角形", "Common.define.smartArt.textAlternatingPictureBlocks": "左右交替積み上げ画像ブロック", "Common.define.smartArt.textAlternatingPictureCircles": "円形付き画像ジグザグ表示", "Common.define.smartArt.textArchitectureLayout": "アーキテクチャ レイアウト", "Common.define.smartArt.textArrowRibbon": "リボン状の矢印", "Common.define.smartArt.textAscendingPictureAccentProcess": "アクセント画像付き上昇ステップ", "Common.define.smartArt.textBalance": "バランス", "Common.define.smartArt.textBasicBendingProcess": "基本蛇行ステップ", "Common.define.smartArt.textBasicBlockList": "カード型リスト", "Common.define.smartArt.textBasicChevronProcess": "プロセス", "Common.define.smartArt.textBasicCycle": "基本の循環", "Common.define.smartArt.textBasicMatrix": "基本マトリックス", "Common.define.smartArt.textBasicPie": "円グラフ", "Common.define.smartArt.textBasicProcess": "基本ステップ", "Common.define.smartArt.textBasicPyramid": "基本ピラミッド", "Common.define.smartArt.textBasicRadial": "基本放射", "Common.define.smartArt.textBasicTarget": "ターゲット", "Common.define.smartArt.textBasicTimeline": "タイムライン", "Common.define.smartArt.textBasicVenn": "基本ベン図", "Common.define.smartArt.textBendingPictureAccentList": "画像付きカード型リスト", "Common.define.smartArt.textBendingPictureBlocks": "自動配置の画像ブロック", "Common.define.smartArt.textBendingPictureCaption": "自動配置の表題付き画像", "Common.define.smartArt.textBendingPictureCaptionList": "自動配置の表題付き画像レイアウト", "Common.define.smartArt.textBendingPictureSemiTranparentText": "自動配置の半透明テキスト付き画像", "Common.define.smartArt.textBlockCycle": "ボックス循環", "Common.define.smartArt.textBubblePictureList": "バブル状画像リスト", "Common.define.smartArt.textCaptionedPictures": "表題付き画像", "Common.define.smartArt.textChevronAccentProcess": "アクセントステップ", "Common.define.smartArt.textChevronList": "プロセス リスト", "Common.define.smartArt.textCircleAccentTimeline": "円形組み合わせタイムライン", "Common.define.smartArt.textCircleArrowProcess": "円形矢印プロセス", "Common.define.smartArt.textCirclePictureHierarchy": "円形画像を使用した階層", "Common.define.smartArt.textCircleProcess": "円形プロセス", "Common.define.smartArt.textCircleRelationship": "円の関連付け", "Common.define.smartArt.textCircularBendingProcess": "円形蛇行ステップ", "Common.define.smartArt.textCircularPictureCallout": "円形画像を使った吹き出し", "Common.define.smartArt.textClosedChevronProcess": "開始点強調型プロセス", "Common.define.smartArt.textContinuousArrowProcess": "大きな矢印のプロセス", "Common.define.smartArt.textContinuousBlockProcess": "矢印と長方形のプロセス", "Common.define.smartArt.textContinuousCycle": "連続性強調循環", "Common.define.smartArt.textContinuousPictureList": "矢印付き画像リスト", "Common.define.smartArt.textConvergingArrows": "内向き矢印", "Common.define.smartArt.textConvergingRadial": "集中", "Common.define.smartArt.textConvergingText": "内向きテキスト", "Common.define.smartArt.textCounterbalanceArrows": "対立とバランスの矢印", "Common.define.smartArt.textCycle": "循環", "Common.define.smartArt.textCycleMatrix": "循環マトリックス", "Common.define.smartArt.textDescendingBlockList": "ブロックの降順リスト", "Common.define.smartArt.textDescendingProcess": "降順プロセス", "Common.define.smartArt.textDetailedProcess": "詳述プロセス", "Common.define.smartArt.textDivergingArrows": "左右逆方向矢印", "Common.define.smartArt.textDivergingRadial": "矢印付き放射", "Common.define.smartArt.textEquation": "数式", "Common.define.smartArt.textFramedTextPicture": "フレームに表示されるテキスト画像", "Common.define.smartArt.textFunnel": "漏斗", "Common.define.smartArt.textGear": "歯車", "Common.define.smartArt.textGridMatrix": "グリッド マトリックス", "Common.define.smartArt.textGroupedList": "グループ リスト", "Common.define.smartArt.textHalfCircleOrganizationChart": "アーチ型線で飾られた組織図", "Common.define.smartArt.textHexagonCluster": "蜂の巣状の六角形", "Common.define.smartArt.textHexagonRadial": "六角形放射", "Common.define.smartArt.textHierarchy": "階層", "Common.define.smartArt.textHierarchyList": "階層リスト", "Common.define.smartArt.textHorizontalBulletList": "横方向箇条書きリスト", "Common.define.smartArt.textHorizontalHierarchy": "横方向階層", "Common.define.smartArt.textHorizontalLabeledHierarchy": "ラベル付き横方向階層", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "複数レベル対応の横方向階層", "Common.define.smartArt.textHorizontalOrganizationChart": "水平方向の組織図", "Common.define.smartArt.textHorizontalPictureList": "横方向画像リスト", "Common.define.smartArt.textIncreasingArrowProcess": "上昇矢印のプロセス", "Common.define.smartArt.textIncreasingCircleProcess": "上昇円プロセス", "Common.define.smartArt.textInterconnectedBlockProcess": "相互接続された長方形のプロセス", "Common.define.smartArt.textInterconnectedRings": "互いにつながったリング", "Common.define.smartArt.textInvertedPyramid": "反転ピラミッド", "Common.define.smartArt.textLabeledHierarchy": "ラベル付き階層", "Common.define.smartArt.textLinearVenn": "横方向ベン図", "Common.define.smartArt.textLinedList": "線区切りリスト", "Common.define.smartArt.textList": "リスト", "Common.define.smartArt.textMatrix": "マトリックス", "Common.define.smartArt.textMultidirectionalCycle": "双方向循環", "Common.define.smartArt.textNameAndTitleOrganizationChart": "氏名/役職名付き組織図", "Common.define.smartArt.textNestedTarget": "包含", "Common.define.smartArt.textNondirectionalCycle": "矢印無し循環", "Common.define.smartArt.textOpposingArrows": "上下逆方向矢印", "Common.define.smartArt.textOpposingIdeas": "対立する案", "Common.define.smartArt.textOrganizationChart": "組織図", "Common.define.smartArt.textOther": "その他", "Common.define.smartArt.textPhasedProcess": "フェーズ プロセス", "Common.define.smartArt.textPicture": "画像", "Common.define.smartArt.textPictureAccentBlocks": "画像アクセントのブロック", "Common.define.smartArt.textPictureAccentList": "画像アクセントのリスト", "Common.define.smartArt.textPictureAccentProcess": "画像アクセントのプロセス", "Common.define.smartArt.textPictureCaptionList": "画像キャプションのリスト", "Common.define.smartArt.textPictureFrame": "フォトフレーム", "Common.define.smartArt.textPictureGrid": "画像グリッド", "Common.define.smartArt.textPictureLineup": "画像ラインアップ", "Common.define.smartArt.textPictureOrganizationChart": "画像付き組織図", "Common.define.smartArt.textPictureStrips": "画像付きラベル", "Common.define.smartArt.textPieProcess": "円グラフのプロセス", "Common.define.smartArt.textPlusAndMinus": "プラスとマイナス", "Common.define.smartArt.textProcess": "プロセス", "Common.define.smartArt.textProcessArrows": "矢印型ステップ", "Common.define.smartArt.textProcessList": "プロセスのリスト", "Common.define.smartArt.textPyramid": "ピラミッド", "Common.define.smartArt.textPyramidList": "ピラミッドのリスト", "Common.define.smartArt.textRadialCluster": "放射ブロック", "Common.define.smartArt.textRadialCycle": "中心付き循環", "Common.define.smartArt.textRadialList": "放射リスト", "Common.define.smartArt.textRadialPictureList": "放射画像リスト", "Common.define.smartArt.textRadialVenn": "放射型ベン図", "Common.define.smartArt.textRandomToResultProcess": "複数案をまとめるステップ", "Common.define.smartArt.textRelationship": "関係", "Common.define.smartArt.textRepeatingBendingProcess": "改行型蛇行ステップ", "Common.define.smartArt.textReverseList": "逆順リスト", "Common.define.smartArt.textSegmentedCycle": "円型循環", "Common.define.smartArt.textSegmentedProcess": "分割ステップ", "Common.define.smartArt.textSegmentedPyramid": "分割ピラミッド", "Common.define.smartArt.textSnapshotPictureList": "スナップショット画像リスト", "Common.define.smartArt.textSpiralPicture": "渦巻き画像", "Common.define.smartArt.textSquareAccentList": "箇条書き記号アクセントのリスト", "Common.define.smartArt.textStackedList": "積み上げリスト", "Common.define.smartArt.textStackedVenn": "包含型ベン図", "Common.define.smartArt.textStaggeredProcess": "段違いステップ", "Common.define.smartArt.textStepDownProcess": "ステップ ダウンのプロセス", "Common.define.smartArt.textStepUpProcess": "ステップアップのプロセス", "Common.define.smartArt.textSubStepProcess": "サブステップのプロセス", "Common.define.smartArt.textTabbedArc": "円弧状タブ", "Common.define.smartArt.textTableHierarchy": "積み木型の階層", "Common.define.smartArt.textTableList": "表型リスト", "Common.define.smartArt.textTabList": "タブ付きリスト", "Common.define.smartArt.textTargetList": "ターゲットのリスト", "Common.define.smartArt.textTextCycle": "テキスト循環", "Common.define.smartArt.textThemePictureAccent": "テーマ画像アクセント", "Common.define.smartArt.textThemePictureAlternatingAccent": "テーマ画像交互のアクセント", "Common.define.smartArt.textThemePictureGrid": "テーマ画像グリッド", "Common.define.smartArt.textTitledMatrix": "タイトル付きマトリックス", "Common.define.smartArt.textTitledPictureAccentList": "画像付き横方向リスト", "Common.define.smartArt.textTitledPictureBlocks": "タイトル付き画像ブロック", "Common.define.smartArt.textTitlePictureLineup": "タイトル付き画像ラインアップ", "Common.define.smartArt.textTrapezoidList": "台形リスト", "Common.define.smartArt.textUpwardArrow": "上向き矢印", "Common.define.smartArt.textVaryingWidthList": "可変幅リスト", "Common.define.smartArt.textVerticalAccentList": "縦方向アクセントのリスト", "Common.define.smartArt.textVerticalArrowList": "縦方向矢印リスト", "Common.define.smartArt.textVerticalBendingProcess": "縦型蛇行ステップ", "Common.define.smartArt.textVerticalBlockList": "縦方向ボックス リスト", "Common.define.smartArt.textVerticalBoxList": "縦方向リスト", "Common.define.smartArt.textVerticalBracketList": "縦方向ブラケット リスト", "Common.define.smartArt.textVerticalBulletList": "縦方向箇条書きリスト", "Common.define.smartArt.textVerticalChevronList": "縦方向プロセス", "Common.define.smartArt.textVerticalCircleList": "縦方向円リスト", "Common.define.smartArt.textVerticalCurvedList": "縦方向カーブのリスト", "Common.define.smartArt.textVerticalEquation": "縦型の数式", "Common.define.smartArt.textVerticalPictureAccentList": "縦方向円形画像リスト", "Common.define.smartArt.textVerticalPictureList": "縦方向画像リスト", "Common.define.smartArt.textVerticalProcess": "縦方向ステップ", "Common.Translation.textMoreButton": "もっと", "Common.Translation.tipFileLocked": "ドキュメントが編集用にロックされています。後で変更し、ローカルコピーとして保存することができます。", "Common.Translation.tipFileReadOnly": "このファイルは読み取り専用です。変更内容を保持するには、新しい名前または別の場所にファイルを保存してください。", "Common.Translation.warnFileLocked": "文書が他のアプリで編集されています。編集を続けて、コピーとして保存できます。", "Common.Translation.warnFileLockedBtnEdit": "コピーを作成する", "Common.Translation.warnFileLockedBtnView": "閲覧するため開く", "Common.UI.ButtonColored.textAutoColor": "自動", "Common.UI.ButtonColored.textEyedropper": "スポイト", "Common.UI.ButtonColored.textNewColor": "その他の色", "Common.UI.ComboBorderSize.txtNoBorders": "枠線なし", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "枠線なし", "Common.UI.ComboDataView.emptyComboText": "スタイルなし", "Common.UI.ExtendedColorDialog.addButtonText": "追加する", "Common.UI.ExtendedColorDialog.textCurrent": "現在", "Common.UI.ExtendedColorDialog.textHexErr": "入力された値が正しくありません。<br>000000〜FFFFFFの数値を入力してください。", "Common.UI.ExtendedColorDialog.textNew": "新しい", "Common.UI.ExtendedColorDialog.textRGBErr": "入力された値が正しくありません。<br>0〜255の数値を入力してください。", "Common.UI.HSBColorPicker.textNoColor": "色なし", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "パスワードを表示しない", "Common.UI.InputFieldBtnPassword.textHintHold": "長押しでパスワード表示", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "パスワードを表示", "Common.UI.SearchBar.textFind": "検索する", "Common.UI.SearchBar.tipCloseSearch": "検索を閉じる", "Common.UI.SearchBar.tipNextResult": "次の結果", "Common.UI.SearchBar.tipOpenAdvancedSettings": "詳細設定を開く", "Common.UI.SearchBar.tipPreviousResult": "前の結果", "Common.UI.SearchDialog.textHighlight": "結果のハイライト", "Common.UI.SearchDialog.textMatchCase": "大文字と小文字の区別", "Common.UI.SearchDialog.textReplaceDef": "代替テキストを挿入する", "Common.UI.SearchDialog.textSearchStart": "ここにテキストを挿入してください。", "Common.UI.SearchDialog.textTitle": "検索と置換", "Common.UI.SearchDialog.textTitle2": "検索する", "Common.UI.SearchDialog.textWholeWords": "単語全体のみ", "Common.UI.SearchDialog.txtBtnHideReplace": "置換を表示しない", "Common.UI.SearchDialog.txtBtnReplace": "置き換える", "Common.UI.SearchDialog.txtBtnReplaceAll": "全てを置き換える", "Common.UI.SynchronizeTip.textDontShow": "今後このメッセージを表示しない", "Common.UI.SynchronizeTip.textGotIt": "OK", "Common.UI.SynchronizeTip.textSynchronize": "このドキュメントは他のユーザーによって変更されました。クリックして変更内容を保存し、更新を再読み込みしてください。", "Common.UI.ThemeColorPalette.textRecentColors": "最近使った色", "Common.UI.ThemeColorPalette.textStandartColors": "標準の色", "Common.UI.ThemeColorPalette.textThemeColors": "テーマの色", "Common.UI.Themes.txtThemeClassicLight": "明るい(クラシック)", "Common.UI.Themes.txtThemeContrastDark": "ダークコントラスト", "Common.UI.Themes.txtThemeDark": "暗い", "Common.UI.Themes.txtThemeGray": "灰色", "Common.UI.Themes.txtThemeLight": "ライト", "Common.UI.Themes.txtThemeSystem": "システム設定と同じ", "Common.UI.Window.cancelButtonText": "キャンセル", "Common.UI.Window.closeButtonText": "閉じる", "Common.UI.Window.noButtonText": "いいえ", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "確認", "Common.UI.Window.textDontShow": "今後このメッセージを表示しない", "Common.UI.Window.textError": "エラー", "Common.UI.Window.textInformation": "情報", "Common.UI.Window.textWarning": "警告", "Common.UI.Window.yesButtonText": "はい", "Common.Utils.Metric.txtCm": "センチ", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": "、", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "アクセント", "Common.Utils.ThemeColor.txtAqua": "水色", "Common.Utils.ThemeColor.txtbackground": "背景", "Common.Utils.ThemeColor.txtBlack": "黒色", "Common.Utils.ThemeColor.txtBlue": "青色", "Common.Utils.ThemeColor.txtBrightGreen": "明るい緑", "Common.Utils.ThemeColor.txtBrown": "茶色", "Common.Utils.ThemeColor.txtDarkBlue": "濃い青色", "Common.Utils.ThemeColor.txtDarker": "より濃い", "Common.Utils.ThemeColor.txtDarkGray": "濃い灰色", "Common.Utils.ThemeColor.txtDarkGreen": "濃い緑色", "Common.Utils.ThemeColor.txtDarkPurple": "濃い紫色", "Common.Utils.ThemeColor.txtDarkRed": "濃い赤色", "Common.Utils.ThemeColor.txtDarkTeal": "濃い青緑色", "Common.Utils.ThemeColor.txtDarkYellow": "濃い黄色", "Common.Utils.ThemeColor.txtGold": "金色", "Common.Utils.ThemeColor.txtGray": "灰色", "Common.Utils.ThemeColor.txtGreen": "緑色", "Common.Utils.ThemeColor.txtIndigo": "インディゴ", "Common.Utils.ThemeColor.txtLavender": "ラベンダー", "Common.Utils.ThemeColor.txtLightBlue": "明るい青色", "Common.Utils.ThemeColor.txtLighter": "より明るい", "Common.Utils.ThemeColor.txtLightGray": "明るい灰色", "Common.Utils.ThemeColor.txtLightGreen": "明るい緑色", "Common.Utils.ThemeColor.txtLightOrange": "明るいオレンジ色", "Common.Utils.ThemeColor.txtLightYellow": "明るい黄色", "Common.Utils.ThemeColor.txtOrange": "オレンジ色", "Common.Utils.ThemeColor.txtPink": "ピンク色", "Common.Utils.ThemeColor.txtPurple": "紫色", "Common.Utils.ThemeColor.txtRed": "赤色", "Common.Utils.ThemeColor.txtRose": "ローズ色", "Common.Utils.ThemeColor.txtSkyBlue": "スカイブルー色", "Common.Utils.ThemeColor.txtTeal": "青緑色", "Common.Utils.ThemeColor.txttext": "テキスト", "Common.Utils.ThemeColor.txtTurquosie": "ターコイズ色", "Common.Utils.ThemeColor.txtViolet": "バイオレット色", "Common.Utils.ThemeColor.txtWhite": "白色", "Common.Utils.ThemeColor.txtYellow": "黄色", "Common.Views.About.txtAddress": "アドレス：", "Common.Views.About.txtLicensee": "ライセンス所有者", "Common.Views.About.txtLicensor": "ライセンサー", "Common.Views.About.txtMail": "Email：", "Common.Views.About.txtPoweredBy": "によって提供されています。", "Common.Views.About.txtTel": "電話番号：", "Common.Views.About.txtVersion": "バージョン", "Common.Views.AutoCorrectDialog.textAdd": "追加する", "Common.Views.AutoCorrectDialog.textApplyText": "入力時に適用する", "Common.Views.AutoCorrectDialog.textAutoCorrect": "テキストオートコレクト", "Common.Views.AutoCorrectDialog.textAutoFormat": "入力オートフォーマット", "Common.Views.AutoCorrectDialog.textBulleted": "自動箇条書きリスト", "Common.Views.AutoCorrectDialog.textBy": "によって", "Common.Views.AutoCorrectDialog.textDelete": "削除する", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "スペース2回でピリオドを入力する", "Common.Views.AutoCorrectDialog.textFLCells": "テーブルセルの最初の文字を大文字にする", "Common.Views.AutoCorrectDialog.textFLDont": "次の項目の後は大文字にしない：", "Common.Views.AutoCorrectDialog.textFLSentence": "文章の最初の文字を大文字にする", "Common.Views.AutoCorrectDialog.textForLangFL": "言語の例外：", "Common.Views.AutoCorrectDialog.textHyperlink": "ハイパーリンクを使用したインターネットとネットワークの経路", "Common.Views.AutoCorrectDialog.textHyphens": "ハイフン(--)とダッシュ(-)の組み合わせ", "Common.Views.AutoCorrectDialog.textMathCorrect": "数式オートコレクト", "Common.Views.AutoCorrectDialog.textNumbered": "自動番号付きリスト", "Common.Views.AutoCorrectDialog.textQuotes": "左右の区別がない引用符を、区別がある引用符に変更する", "Common.Views.AutoCorrectDialog.textRecognized": "認識された関数", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "以下の式は、認識される数式です。 自動的にイタリック体になることはありません。", "Common.Views.AutoCorrectDialog.textReplace": "置き換える", "Common.Views.AutoCorrectDialog.textReplaceText": "入力時に置き換える\n\t", "Common.Views.AutoCorrectDialog.textReplaceType": "入力時にテキストを置き換える", "Common.Views.AutoCorrectDialog.textReset": "リセット", "Common.Views.AutoCorrectDialog.textResetAll": "デフォルト設定にリセットする", "Common.Views.AutoCorrectDialog.textRestore": "復元する", "Common.Views.AutoCorrectDialog.textTitle": "オートコレクト", "Common.Views.AutoCorrectDialog.textWarnAddFL": "例外は、大文字または小文字の文字のみを含む必要があります。", "Common.Views.AutoCorrectDialog.textWarnAddRec": "認識される関数には、大文字または小文字のAからZまでの文字のみを含める必要があります。", "Common.Views.AutoCorrectDialog.textWarnResetFL": "追加した例外は削除され、削除した例外は元に戻ります。続行しますか？", "Common.Views.AutoCorrectDialog.textWarnResetRec": "追加した式はすべて削除され、削除された式が復元されます。 このまま続けますか？", "Common.Views.AutoCorrectDialog.warnReplace": "％1のオートコレクトのエントリはすでに存在します。 置き換えますか？", "Common.Views.AutoCorrectDialog.warnReset": "追加したオートコレクトはすべて削除され、変更されたものは元の値に復元されます。 このまま続けますか？", "Common.Views.AutoCorrectDialog.warnRestore": "％1のオートコレクトエントリは元の値にリセットされます。 続けますか？", "Common.Views.Chat.textChat": "チャット", "Common.Views.Chat.textClosePanel": "チャットを閉じる", "Common.Views.Chat.textEnterMessage": "ここにメッセージを挿入する", "Common.Views.Chat.textSend": "送信する", "Common.Views.Comments.mniAuthorAsc": "AからZで作成者を表示する", "Common.Views.Comments.mniAuthorDesc": "ZからAで作成者を表示する", "Common.Views.Comments.mniDateAsc": "最も古い", "Common.Views.Comments.mniDateDesc": "最も新しい", "Common.Views.Comments.mniFilterGroups": "グループでフィルター", "Common.Views.Comments.mniPositionAsc": "上から", "Common.Views.Comments.mniPositionDesc": "下から", "Common.Views.Comments.textAdd": "追加する", "Common.Views.Comments.textAddComment": "コメントを追加", "Common.Views.Comments.textAddCommentToDoc": "ドキュメントにコメントを追加", "Common.Views.Comments.textAddReply": "返信を追加", "Common.Views.Comments.textAll": "すべて", "Common.Views.Comments.textAnonym": "ゲスト", "Common.Views.Comments.textCancel": "キャンセル", "Common.Views.Comments.textClose": "閉じる", "Common.Views.Comments.textClosePanel": "コメントを閉じる", "Common.Views.Comments.textComment": "コメント", "Common.Views.Comments.textComments": "コメント", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "ここにコメントを挿入してください。", "Common.Views.Comments.textHintAddComment": "コメントを追加", "Common.Views.Comments.textOpenAgain": "もう一度開く", "Common.Views.Comments.textReply": "返信する", "Common.Views.Comments.textResolve": "解決する", "Common.Views.Comments.textResolved": "解決済み", "Common.Views.Comments.textSort": "コメントを並べ替える", "Common.Views.Comments.textSortFilter": "コメントの並べ替えとフィルター", "Common.Views.Comments.textSortFilterMore": "並び替え、フィルター、その他", "Common.Views.Comments.textSortMore": "並び替えなど", "Common.Views.Comments.textViewResolved": "コメントを再開する権限がありません", "Common.Views.Comments.txtEmpty": "ドキュメントにはコメントがありません。", "Common.Views.CopyWarningDialog.textDontShow": "今後このメッセージを表示しない", "Common.Views.CopyWarningDialog.textMsg": "エディターツールバーのボタンやコンテキストメニューの操作によるコピー、カット、ペーストの動作は、このエディタータブ内でのみ実行されます。<br><br> エディタータブ以外のアプリケーションとの間でコピーまたは貼り付けを行うには、次のキーボードの組み合わせを使用して下さい:", "Common.Views.CopyWarningDialog.textTitle": "コピー,切り取り,貼り付け", "Common.Views.CopyWarningDialog.textToCopy": "コピー", "Common.Views.CopyWarningDialog.textToCut": "切り取り", "Common.Views.CopyWarningDialog.textToPaste": "貼り付け", "Common.Views.CustomizeQuickAccessDialog.textDownload": "ダウンロード", "Common.Views.CustomizeQuickAccessDialog.textMsg": "クイックアクセスツールバーに表示されるコマンドをチェックしてください", "Common.Views.CustomizeQuickAccessDialog.textPrint": "印刷", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "クイックプリント", "Common.Views.CustomizeQuickAccessDialog.textRedo": "やり直す", "Common.Views.CustomizeQuickAccessDialog.textSave": "保存", "Common.Views.CustomizeQuickAccessDialog.textStartOver": "先頭から表示する", "Common.Views.CustomizeQuickAccessDialog.textTitle": "クイックアクセスのカスタマイズ", "Common.Views.CustomizeQuickAccessDialog.textUndo": "元に戻す", "Common.Views.DocumentAccessDialog.textLoading": "読み込み中...", "Common.Views.DocumentAccessDialog.textTitle": "共有設定", "Common.Views.DocumentPropertyDialog.errorDate": "カレンダーから値を選択して日付として保存できます。<br>値を手動で入力した場合は、テキストとして保存されます。", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "いいえ", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "はい", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "プロパティはタイトルが必要です", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "タイトル", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "「はい」または「いいえ」", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "日付", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "タイプ", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "数", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "有効な数値を入力してください", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "テキスト", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "プロパティには値が必要です", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "値", "Common.Views.DocumentPropertyDialog.txtTitle": "新しいドキュメントのプロパティ", "Common.Views.Draw.hintEraser": "消しゴム", "Common.Views.Draw.hintSelect": "選択", "Common.Views.Draw.txtEraser": "消しゴム", "Common.Views.Draw.txtHighlighter": "蛍光ペン", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "ペン", "Common.Views.Draw.txtSelect": "選択", "Common.Views.Draw.txtSize": "サイズ", "Common.Views.ExternalDiagramEditor.textTitle": "グラフエディター", "Common.Views.ExternalEditor.textClose": "閉じる", "Common.Views.ExternalEditor.textSave": "保存&終了", "Common.Views.ExternalOleEditor.textTitle": "スプレッドシートエディター", "Common.Views.Header.ariaQuickAccessToolbar": "クイックアクセスツールバー", "Common.Views.Header.labelCoUsersDescr": "ファイルを編集しているユーザー：", "Common.Views.Header.textAddFavorite": "お気に入りとしてマークする", "Common.Views.Header.textAdvSettings": "詳細設定", "Common.Views.Header.textBack": "ファイルの場所を開く", "Common.Views.Header.textClose": "ファイルを閉じる", "Common.Views.Header.textCompactView": "ツールバーを表示しない", "Common.Views.Header.textHideLines": "ルーラーを表示しない", "Common.Views.Header.textHideNotes": "ノートを非表示にする", "Common.Views.Header.textHideStatusBar": "ステータスバーを表示しない", "Common.Views.Header.textPrint": "印刷", "Common.Views.Header.textReadOnly": "閲覧のみ", "Common.Views.Header.textRemoveFavorite": "お気に入りから削除", "Common.Views.Header.textSaveBegin": "保存中...", "Common.Views.Header.textSaveChanged": "変更済み", "Common.Views.Header.textSaveEnd": "全ての変更点が保存されました", "Common.Views.Header.textSaveExpander": "全ての変更点が保存されました", "Common.Views.Header.textShare": "共有", "Common.Views.Header.textStartOver": "先頭から表示する", "Common.Views.Header.textZoom": "ズーム", "Common.Views.Header.tipAccessRights": "文書のアクセス許可の管理", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "クイックアクセスツールバーのカスタマイズ", "Common.Views.Header.tipDownload": "ファイルをダウンロードする", "Common.Views.Header.tipGoEdit": "現在のファイルを編集する", "Common.Views.Header.tipPrint": "ファイルを印刷する", "Common.Views.Header.tipPrintQuick": "クイックプリント", "Common.Views.Header.tipRedo": "やり直し", "Common.Views.Header.tipSave": "保存する", "Common.Views.Header.tipSearch": "検索", "Common.Views.Header.tipStartOver": "スライドショーを最初から開始する", "Common.Views.Header.tipUndo": "元に戻す", "Common.Views.Header.tipUndock": "別のウィンドウにドッキングを解除する", "Common.Views.Header.tipUsers": "ユーザーを表示する", "Common.Views.Header.tipViewSettings": "表示の設定", "Common.Views.Header.tipViewUsers": "ユーザーの表示とドキュメントのアクセス権の管理", "Common.Views.Header.txtAccessRights": "アクセス許可の変更", "Common.Views.Header.txtRename": "名前を変更する", "Common.Views.History.textCloseHistory": "履歴を閉じる", "Common.Views.History.textHideAll": "変更の詳細を表示しない", "Common.Views.History.textHighlightDeleted": "削除されたところをハイライトする", "Common.Views.History.textMore": "もっと見る", "Common.Views.History.textRestore": "復元する", "Common.Views.History.textShowAll": "変更の詳細を表示する", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "バージョン履歴", "Common.Views.ImageFromUrlDialog.textUrl": "画像URLの貼り付け:", "Common.Views.ImageFromUrlDialog.txtEmpty": "このフィールドは必須項目です", "Common.Views.ImageFromUrlDialog.txtNotUrl": "リンクの入力内容は「http://www.example.com」形式のURLである必要があります。", "Common.Views.InsertTableDialog.textInvalidRowsCols": "有効な行と列の数を指定する必要があります。", "Common.Views.InsertTableDialog.txtColumns": "列数", "Common.Views.InsertTableDialog.txtMaxText": "このフィールドの最大値は{0}です。", "Common.Views.InsertTableDialog.txtMinText": "このフィールドの最小値は{0}です。", "Common.Views.InsertTableDialog.txtRows": "行数", "Common.Views.InsertTableDialog.txtTitle": "表のサイズ", "Common.Views.InsertTableDialog.txtTitleSplit": "セルの分割", "Common.Views.LanguageDialog.labelSelect": "文書の言語を選択する", "Common.Views.ListSettingsDialog.textBulleted": "箇条書き形式", "Common.Views.ListSettingsDialog.textFromFile": "ファイルから", "Common.Views.ListSettingsDialog.textFromStorage": "ストレージから", "Common.Views.ListSettingsDialog.textFromUrl": "URLから", "Common.Views.ListSettingsDialog.textNumbering": "番号付き", "Common.Views.ListSettingsDialog.textSelect": "選択する", "Common.Views.ListSettingsDialog.tipChange": "箇条書きを変更", "Common.Views.ListSettingsDialog.txtBullet": "箇条書き", "Common.Views.ListSettingsDialog.txtColor": "色", "Common.Views.ListSettingsDialog.txtImage": "画像", "Common.Views.ListSettingsDialog.txtImport": "インポート", "Common.Views.ListSettingsDialog.txtNewBullet": "新しい行頭文字", "Common.Views.ListSettingsDialog.txtNewImage": "新しい画像", "Common.Views.ListSettingsDialog.txtNone": "なし", "Common.Views.ListSettingsDialog.txtOfText": "テキストの%", "Common.Views.ListSettingsDialog.txtSize": "サイズ", "Common.Views.ListSettingsDialog.txtStart": "から開始", "Common.Views.ListSettingsDialog.txtSymbol": "記号", "Common.Views.ListSettingsDialog.txtTitle": "リストの設定", "Common.Views.ListSettingsDialog.txtType": "タイプ", "Common.Views.MacrosDialog.textCopy": "コピー", "Common.Views.MacrosDialog.textCustomFunction": "カスタム関数", "Common.Views.MacrosDialog.textDelete": "削除", "Common.Views.MacrosDialog.textLoading": "読み込んでいます...", "Common.Views.MacrosDialog.textMacros": "マ<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "自動起動に設定", "Common.Views.MacrosDialog.textRename": "名前の変更", "Common.Views.MacrosDialog.textRun": "実行", "Common.Views.MacrosDialog.textSave": "保存", "Common.Views.MacrosDialog.textTitle": "マ<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "自動起動を解除", "Common.Views.MacrosDialog.tipFunctionAdd": "カスタム関数を追加", "Common.Views.MacrosDialog.tipMacrosAdd": "マクロを追加", "Common.Views.MacrosDialog.tipMacrosRun": "実行", "Common.Views.OpenDialog.closeButtonText": "ファイルを閉じる", "Common.Views.OpenDialog.txtEncoding": "文字コード", "Common.Views.OpenDialog.txtIncorrectPwd": "パスワードが正しくありません。", "Common.Views.OpenDialog.txtOpenFile": "ファイルを開くためにパスワードを入力してください。", "Common.Views.OpenDialog.txtPassword": "パスワード", "Common.Views.OpenDialog.txtProtected": "一度パスワードを入力してファイルを開くと、そのファイルの既存のパスワードがリセットされます。", "Common.Views.OpenDialog.txtTitle": "%1オプションを選択", "Common.Views.OpenDialog.txtTitleProtected": "保護されたファイル", "Common.Views.PasswordDialog.txtDescription": "この文書を保護するためのパスワードを設定してください。", "Common.Views.PasswordDialog.txtIncorrectPwd": "先に入力したパスワードと一致しません。", "Common.Views.PasswordDialog.txtPassword": "パスワード", "Common.Views.PasswordDialog.txtRepeat": "パスワードを再入力", "Common.Views.PasswordDialog.txtTitle": "パスワードの設定", "Common.Views.PasswordDialog.txtWarning": "警告: パスワードを忘れると元に戻せません。安全な場所に記録してください。", "Common.Views.PluginDlg.textLoading": "読み込み中", "Common.Views.PluginPanel.textClosePanel": "プラグインを閉じる", "Common.Views.PluginPanel.textLoading": "読み込み中", "Common.Views.Plugins.groupCaption": "プラグイン", "Common.Views.Plugins.strPlugins": "プラグイン", "Common.Views.Plugins.textBackgroundPlugins": "バックグラウンド・プラグイン", "Common.Views.Plugins.textSettings": "設定", "Common.Views.Plugins.textStart": "開始", "Common.Views.Plugins.textStop": "停止", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "バックグラウンド・プラグインのリスト", "Common.Views.Plugins.tipMore": "もっと", "Common.Views.Protection.hintAddPwd": "パスワードを使用して暗号化する", "Common.Views.Protection.hintDelPwd": "パスワードの削除", "Common.Views.Protection.hintPwd": "パスワードを変更するか削除する", "Common.Views.Protection.hintSignature": "デジタル署名かデジタル署名行を追加する", "Common.Views.Protection.txtAddPwd": "パスワードを追加", "Common.Views.Protection.txtChangePwd": "パスワードを変更する", "Common.Views.Protection.txtDeletePwd": "パスワードを削除する", "Common.Views.Protection.txtEncrypt": "暗号化する", "Common.Views.Protection.txtInvisibleSignature": "デジタル署名を追加", "Common.Views.Protection.txtSignature": "署名", "Common.Views.Protection.txtSignatureLine": "署名欄を追加", "Common.Views.RecentFiles.txtOpenRecent": "最近使ったファイルを開く", "Common.Views.RenameDialog.textName": "ファイル名", "Common.Views.RenameDialog.txtInvalidName": "ファイル名に次の文字を使うことはできません。", "Common.Views.ReviewChanges.hintNext": "次の変更箇所へ", "Common.Views.ReviewChanges.hintPrev": "前の​​変更箇所へ", "Common.Views.ReviewChanges.strFast": "高速", "Common.Views.ReviewChanges.strFastDesc": "リアルタイム共同編集モードです。すべての変更は自動的に保存されます。", "Common.Views.ReviewChanges.strStrict": "厳格", "Common.Views.ReviewChanges.strStrictDesc": "あなたや他のユーザーが行った変更を同期するために、[保存]ボタンを使用してください。", "Common.Views.ReviewChanges.tipAcceptCurrent": "現在の変更を承諾する", "Common.Views.ReviewChanges.tipCoAuthMode": "共同編集モードを設定する", "Common.Views.ReviewChanges.tipCommentRem": "コメントを削除する", "Common.Views.ReviewChanges.tipCommentRemCurrent": "現在のコメントを削除する", "Common.Views.ReviewChanges.tipCommentResolve": "コメントを解決する", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "現在のコメントを解決する", "Common.Views.ReviewChanges.tipHistory": "バージョン履歴を表示", "Common.Views.ReviewChanges.tipRejectCurrent": "現在の変更を拒否する", "Common.Views.ReviewChanges.tipReview": "変更履歴", "Common.Views.ReviewChanges.tipReviewView": "変更内容を表示するモードを選択してください", "Common.Views.ReviewChanges.tipSetDocLang": "文書の言語を設定する", "Common.Views.ReviewChanges.tipSetSpelling": "スペルチェック", "Common.Views.ReviewChanges.tipSharing": "文書のアクセス許可の管理", "Common.Views.ReviewChanges.txtAccept": "承諾", "Common.Views.ReviewChanges.txtAcceptAll": "すべての変更を承諾する", "Common.Views.ReviewChanges.txtAcceptChanges": "変更を承諾する", "Common.Views.ReviewChanges.txtAcceptCurrent": "現在の変更を承諾する", "Common.Views.ReviewChanges.txtChat": "チャット", "Common.Views.ReviewChanges.txtClose": "閉じる", "Common.Views.ReviewChanges.txtCoAuthMode": "共同編集モード", "Common.Views.ReviewChanges.txtCommentRemAll": "全てのコメントを削除する", "Common.Views.ReviewChanges.txtCommentRemCurrent": "現在のコメントを削除する", "Common.Views.ReviewChanges.txtCommentRemMy": "自分のコメントを削除する", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "自分の現在のコメントを削除する", "Common.Views.ReviewChanges.txtCommentRemove": "削除する", "Common.Views.ReviewChanges.txtCommentResolve": "解決する", "Common.Views.ReviewChanges.txtCommentResolveAll": "すべてのコメントを解決する", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "現在のコメントを解決する", "Common.Views.ReviewChanges.txtCommentResolveMy": "自分のコメントを解決する", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "自分のコメントを解決する", "Common.Views.ReviewChanges.txtDocLang": "言語", "Common.Views.ReviewChanges.txtFinal": "すべての変更が承認されました（プレビュー）", "Common.Views.ReviewChanges.txtFinalCap": "最終版", "Common.Views.ReviewChanges.txtHistory": "バージョン履歴", "Common.Views.ReviewChanges.txtMarkup": "全ての変更（編集）", "Common.Views.ReviewChanges.txtMarkupCap": "マークアップ", "Common.Views.ReviewChanges.txtNext": "次へ", "Common.Views.ReviewChanges.txtOriginal": "すべての変更が拒否されました（プレビュー）", "Common.Views.ReviewChanges.txtOriginalCap": "初版", "Common.Views.ReviewChanges.txtPrev": "前回の", "Common.Views.ReviewChanges.txtReject": "拒否する", "Common.Views.ReviewChanges.txtRejectAll": "すべての変更を拒否する", "Common.Views.ReviewChanges.txtRejectChanges": "変更を拒否する", "Common.Views.ReviewChanges.txtRejectCurrent": "現在の変更を拒否する", "Common.Views.ReviewChanges.txtSharing": "共有", "Common.Views.ReviewChanges.txtSpelling": "スペルチェック", "Common.Views.ReviewChanges.txtTurnon": "変更履歴", "Common.Views.ReviewChanges.txtView": "表示モード", "Common.Views.ReviewPopover.textAdd": "追加する", "Common.Views.ReviewPopover.textAddReply": "返信を追加", "Common.Views.ReviewPopover.textCancel": "キャンセル", "Common.Views.ReviewPopover.textClose": "閉じる", "Common.Views.ReviewPopover.textComment": "コメント", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "ここにコメントを入力してください", "Common.Views.ReviewPopover.textMention": "+メンションされるユーザーは文書にアクセスのメール通知を送信します", "Common.Views.ReviewPopover.textMentionNotify": "＋メンションされるユーザーはメールで通知されます", "Common.Views.ReviewPopover.textOpenAgain": "もう一度開く", "Common.Views.ReviewPopover.textReply": "返信する", "Common.Views.ReviewPopover.textResolve": "解決する", "Common.Views.ReviewPopover.textViewResolved": "コメントを再開する権限がありません", "Common.Views.ReviewPopover.txtDeleteTip": "削除する", "Common.Views.ReviewPopover.txtEditTip": "編集する", "Common.Views.SaveAsDlg.textLoading": "読み込み中", "Common.Views.SaveAsDlg.textTitle": "保存先のフォルダ", "Common.Views.SearchPanel.textCaseSensitive": "大文字と小文字を区別する", "Common.Views.SearchPanel.textCloseSearch": "検索を閉じる", "Common.Views.SearchPanel.textContentChanged": "ドキュメントが変更されました", "Common.Views.SearchPanel.textFind": "検索する", "Common.Views.SearchPanel.textFindAndReplace": "検索して置換する", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0}個のアイテムが正常に交換されました。", "Common.Views.SearchPanel.textMatchUsingRegExp": "正規表現によるマッチング", "Common.Views.SearchPanel.textNoMatches": "一致する結果がありません", "Common.Views.SearchPanel.textNoSearchResults": "検索結果は見つかりませんでした", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1}のアイテムが交換されました。残りの{2}個のアイテムは他のユーザーによってロックされています。", "Common.Views.SearchPanel.textReplace": "置換する", "Common.Views.SearchPanel.textReplaceAll": "全てを置換する", "Common.Views.SearchPanel.textReplaceWith": "置換後の文字列", "Common.Views.SearchPanel.textSearchAgain": "正確な結果を得るために{0}新規検索を行う{1}。", "Common.Views.SearchPanel.textSearchHasStopped": "検索が停止しました", "Common.Views.SearchPanel.textSearchResults": "検索結果：{0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "検索結果", "Common.Views.SearchPanel.textTooManyResults": "検索結果が多すぎるため、ここに表示できません", "Common.Views.SearchPanel.textWholeWords": "単語全体のみ", "Common.Views.SearchPanel.tipNextResult": "次の結果", "Common.Views.SearchPanel.tipPreviousResult": "前の結果", "Common.Views.SelectFileDlg.textLoading": "読み込み中", "Common.Views.SelectFileDlg.textTitle": "データソースを選択する", "Common.Views.ShapeShadowDialog.txtAngle": "角", "Common.Views.ShapeShadowDialog.txtDistance": "距離", "Common.Views.ShapeShadowDialog.txtSize": "サイズ", "Common.Views.ShapeShadowDialog.txtTitle": "影の調整", "Common.Views.ShapeShadowDialog.txtTransparency": "透過性", "Common.Views.SignDialog.textBold": "太字", "Common.Views.SignDialog.textCertificate": "証明書", "Common.Views.SignDialog.textChange": "変更する", "Common.Views.SignDialog.textInputName": "署名者の名前をご入力ください", "Common.Views.SignDialog.textItalic": "イタリック体", "Common.Views.SignDialog.textNameError": "署名者の名前を空にしておくことはできません。", "Common.Views.SignDialog.textPurpose": "この文書にサインする目的", "Common.Views.SignDialog.textSelect": "選択", "Common.Views.SignDialog.textSelectImage": "画像を選択する", "Common.Views.SignDialog.textSignature": "署名は次のようになります:", "Common.Views.SignDialog.textTitle": "文書に署名する", "Common.Views.SignDialog.textUseImage": "または「画像を選択」をクリックして、画像を署名として使用します", "Common.Views.SignDialog.textValid": "％1から％2まで有効", "Common.Views.SignDialog.tipFontName": "フォント名", "Common.Views.SignDialog.tipFontSize": "フォントのサイズ", "Common.Views.SignSettingsDialog.textAllowComment": "署名者が署名ダイアログボックスにコメントを追加できるようにする", "Common.Views.SignSettingsDialog.textDefInstruction": "このドキュメントに署名する前に、署名するコンテンツが正しいことを確認してください。", "Common.Views.SignSettingsDialog.textInfoEmail": "署名候補者のメールアドレス", "Common.Views.SignSettingsDialog.textInfoName": "署名候補者", "Common.Views.SignSettingsDialog.textInfoTitle": "署名候補者の役職", "Common.Views.SignSettingsDialog.textInstructions": "署名者への説明書", "Common.Views.SignSettingsDialog.textShowDate": "署名欄に署名日を表示する", "Common.Views.SignSettingsDialog.textTitle": "署名の設定", "Common.Views.SignSettingsDialog.txtEmpty": "このフィールドは必須項目です", "Common.Views.SymbolTableDialog.textCharacter": "文字", "Common.Views.SymbolTableDialog.textCode": "UnicodeHEX値", "Common.Views.SymbolTableDialog.textCopyright": "著作権マーク", "Common.Views.SymbolTableDialog.textDCQuote": "二重引用符を終了する", "Common.Views.SymbolTableDialog.textDOQuote": "二重の引用符(左）", "Common.Views.SymbolTableDialog.textEllipsis": "水平の省略記号", "Common.Views.SymbolTableDialog.textEmDash": "全角ダッシュ", "Common.Views.SymbolTableDialog.textEmSpace": "全角スペース", "Common.Views.SymbolTableDialog.textEnDash": "半角ダッシュ", "Common.Views.SymbolTableDialog.textEnSpace": "半角スペース", "Common.Views.SymbolTableDialog.textFont": "フォント", "Common.Views.SymbolTableDialog.textNBHyphen": "改行をしないハイフン", "Common.Views.SymbolTableDialog.textNBSpace": "改行をしないスペース", "Common.Views.SymbolTableDialog.textPilcrow": "段落記号", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4スペース", "Common.Views.SymbolTableDialog.textRange": "範囲", "Common.Views.SymbolTableDialog.textRecent": "最近使用した記号", "Common.Views.SymbolTableDialog.textRegistered": "登録商標マーク", "Common.Views.SymbolTableDialog.textSCQuote": "単一引用符を終了する", "Common.Views.SymbolTableDialog.textSection": "節記号", "Common.Views.SymbolTableDialog.textShortcut": "ショートカットキー", "Common.Views.SymbolTableDialog.textSHyphen": "ソフトハイフン", "Common.Views.SymbolTableDialog.textSOQuote": "単一引用符（左）", "Common.Views.SymbolTableDialog.textSpecial": "特殊文字", "Common.Views.SymbolTableDialog.textSymbols": "記号", "Common.Views.SymbolTableDialog.textTitle": "記号", "Common.Views.SymbolTableDialog.textTradeMark": "商標マーク", "Common.Views.UserNameDialog.textDontShow": "二度と表示しない", "Common.Views.UserNameDialog.textLabel": "ラベル：", "Common.Views.UserNameDialog.textLabelError": "ラベルは空白にできません。", "PE.Controllers.DocumentHolder.textLongName": "255文字以内の名前を入力してください。", "PE.Controllers.DocumentHolder.textNameLayout": "レイアウト名", "PE.Controllers.DocumentHolder.textNameMaster": "マスター名", "PE.Controllers.DocumentHolder.textRenameTitleLayout": "レイアウト名を変更", "PE.Controllers.DocumentHolder.textRenameTitleMaster": "マスター名の変更", "PE.Controllers.LeftMenu.leavePageText": "変更を保存せずにドキュメントを閉じると変更が失われます。<br>「キャンセル」をクリックし、「保存」をクリックして保存してください。「OK」をクリックすると、保存されていないすべての変更が破棄されます。", "PE.Controllers.LeftMenu.newDocumentTitle": "名前が付けられていないプレゼンテーション", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "警告", "PE.Controllers.LeftMenu.requestEditRightsText": "編集の権限を要求中...", "PE.Controllers.LeftMenu.textLoadHistory": "バージョン履歴の読み込み中...", "PE.Controllers.LeftMenu.textNoTextFound": "検索データが見つかりませんでした。検索オプションを変更してください。", "PE.Controllers.LeftMenu.textReplaceSkipped": "置換が行われました。スキップされた発生回数は{0}です。", "PE.Controllers.LeftMenu.textReplaceSuccess": "検索が完了しました。{0}つが置換されました。", "PE.Controllers.LeftMenu.textSelectPath": "ファイルのコピーを保存するために新しいタイトルを入力してください", "PE.Controllers.LeftMenu.txtUntitled": "タイトルなし", "PE.Controllers.Main.applyChangesTextText": "データの読み込み中...", "PE.Controllers.Main.applyChangesTitleText": "データの読み込み中", "PE.Controllers.Main.confirmMaxChangesSize": "アクションのサイズがサーバーに設定された制限を超えています。<br>「元に戻す」ボタンを押して最後のアクションをキャンセルするか、「続ける」を押してローカルにアクションを維持してください（何も失われないことを確認するために、ファイルをダウンロードするか、その内容をコピーする必要があります）。", "PE.Controllers.Main.convertationTimeoutText": "変換のタイムアウトを超過しました。", "PE.Controllers.Main.criticalErrorExtText": "OKボタンを押すとドキュメントリストに戻ります。", "PE.Controllers.Main.criticalErrorTitle": "エラー", "PE.Controllers.Main.downloadErrorText": "ダウンロードに失敗しました", "PE.Controllers.Main.downloadTextText": "プレゼンテーションのダウンロード中...", "PE.Controllers.Main.downloadTitleText": "プレゼンテーションのダウンロード中", "PE.Controllers.Main.errorAccessDeny": "権限のない操作を実行しようとしています。<br>ドキュメントサーバーの管理者にご連絡ください。", "PE.Controllers.Main.errorBadImageUrl": "画像のURLが正しくありません", "PE.Controllers.Main.errorCannotPasteImg": "この画像をクリップボードから貼り付けることはできませんが、端末に保存してそこから挿入したり、\nテキストを含まない画像をコピーしてプレゼンテーションに貼り付けたりすることが可能です。", "PE.Controllers.Main.errorCoAuthoringDisconnect": "サーバーとの接続が失われました。現在、文書を編集することができません。", "PE.Controllers.Main.errorComboSeries": "組み合わせチャートを作成するには、最低2つのデータを選択します。", "PE.Controllers.Main.errorConnectToServer": "文書を保存できませんでした。接続設定を確認するか、管理者にお問い合わせください。<br>OKボタンをクリックするとドキュメントをダウンロードするように求められます。", "PE.Controllers.Main.errorDatabaseConnection": "外部エラーです。<br>データベース接続エラーです。この問題が解決しない場合は、サポートにお問い合わせください。 ", "PE.Controllers.Main.errorDataEncrypted": "暗号化された変更を受け取りましたが、解読できません。", "PE.Controllers.Main.errorDataRange": "データ範囲が正しくありません。", "PE.Controllers.Main.errorDefaultMessage": "エラーコード: %1", "PE.Controllers.Main.errorDirectUrl": "ドキュメントへのリンクを確認してください。<br>このリンクは、ダウンロード用のファイルへの直接リンクである必要があります。", "PE.Controllers.Main.errorEditingDownloadas": "文書の処理中にエラーが発生しました。<br>コンピューターにファイルのバックアップコピーを保存するために、「名前を付けてダウンロード」をご使用ください。", "PE.Controllers.Main.errorEditingSaveas": "文書の処理中にエラーが発生しました。<br>コンピューターにファイルのバックアップを保存するために、「名前を付けてダウンロード」をご使用ください。", "PE.Controllers.Main.errorEmailClient": "メールクライアントが見つかりませんでした。", "PE.Controllers.Main.errorFilePassProtect": "文書がパスワードで保護されているため、開くことができません。", "PE.Controllers.Main.errorFileSizeExceed": "ファイルサイズがサーバーで設定された制限を超過しています。<br>Documentサーバー管理者に詳細をお問い合わせください。", "PE.Controllers.Main.errorForceSave": "文書の保存中にエラーが発生しました。コンピューターにファイルを保存するために、「名前を付けてダウンロード」を使用するか、または後で再度お試しください。", "PE.Controllers.Main.errorInconsistentExt": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容がファイルの拡張子と一致しません。", "PE.Controllers.Main.errorInconsistentExtDocx": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容はドキュメント (docx など) に対応していますが、ファイルの拡張子が一致していません: %1", "PE.Controllers.Main.errorInconsistentExtPdf": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容は次のいずれかの形式に対応しています: pdf/djvu/xps/oxps が、ファイルの拡張子が一致していません: %1", "PE.Controllers.Main.errorInconsistentExtPptx": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容はプレゼンテーション (pptx など) に対応していますが、ファイルの拡張子が一致していません: %1", "PE.Controllers.Main.errorInconsistentExtXlsx": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容はスプレッドシート (xlsx など) に対応していますが、ファイルの拡張子が一致していません: %1", "PE.Controllers.Main.errorKeyEncrypt": "不明なキーの記述子", "PE.Controllers.Main.errorKeyExpire": "キー記述子の有効期限が切れました", "PE.Controllers.Main.errorLoadingFont": "フォントが読み込まれていません。<br>ドキュメントサーバーの管理者に連絡してください。", "PE.Controllers.Main.errorProcessSaveResult": "保存に失敗しました。", "PE.Controllers.Main.errorSaveWatermark": "このファイルには、別のドメインにリンクされた透かし画像が含まれています。<br>PDFで見えるようにするには、文書と同じドメインからリンクされるように透かし画像を更新するか、コンピュータからアップロードしてください。", "PE.Controllers.Main.errorServerVersion": "エディターのバージョンが更新されました。 変更を適用するために、ページが再読み込みされます。", "PE.Controllers.Main.errorSessionAbsolute": "ドキュメント編集セッションが終了しました。 ページを再度読み込みしてください。", "PE.Controllers.Main.errorSessionIdle": "このドキュメントは長い間編集されていませんでした。このページを再度読み込んでください。", "PE.Controllers.Main.errorSessionToken": "サーバーとの接続が中断されました。このページを再度読み込んでください。", "PE.Controllers.Main.errorSetPassword": "パスワードを設定できませんでした。", "PE.Controllers.Main.errorStockChart": "行の順序が正しくありません。この株価チャートを作成するには、<br>始値、最大値、最小値、終値の順でシートのデータを配置してください。", "PE.Controllers.Main.errorToken": "ドキュメントセキュリティトークンが正しく形成されていません。<br>ドキュメントサーバーの管理者にご連絡ください。", "PE.Controllers.Main.errorTokenExpire": "ドキュメントセキュリティトークンの有効期限が切れています。<br>ドキュメントサーバーの管理者に連絡してください。", "PE.Controllers.Main.errorUpdateVersion": "ファイルのバージョンが変更されました。ページを再読み込みします。", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "インターネット接続が復旧し、ファイルのバージョンが更新されています。<br>作業を継続する前に、ファイルをダウンロードするか内容をコピーして変更が失われていないことを確認してから、このページを再読み込みしてください。", "PE.Controllers.Main.errorUserDrop": "現在、このファイルにはアクセスできません。", "PE.Controllers.Main.errorUsersExceed": "料金プランで許可されているユーザー数を超過しました。", "PE.Controllers.Main.errorViewerDisconnect": "接続が失われました。文書の表示は可能ですが、<br>再度接続されてページが再ロードされるまで、ダウンロードまたは印刷することはできません。", "PE.Controllers.Main.leavePageText": "このプレゼンテーションでは、未保存の変更があります。「このページにとどまる」をクリックし、「保存」をクリックして保存してください。「このページを離れる」をクリックすると、未保存の変更がすべて破棄されます。", "PE.Controllers.Main.leavePageTextOnClose": "このプレゼンテーションで保存されていない変更はすべて失われます。<br>保存するには「キャンセル」をクリックし、「保存」をクリックしてください。「OK 」をクリックすると、保存されていないすべての変更が破棄されます。", "PE.Controllers.Main.loadFontsTextText": "データの読み込み中...", "PE.Controllers.Main.loadFontsTitleText": "データの読み込み中", "PE.Controllers.Main.loadFontTextText": "データの読み込み中...", "PE.Controllers.Main.loadFontTitleText": "データの読み込み中", "PE.Controllers.Main.loadImagesTextText": "画像の読み込み中...", "PE.Controllers.Main.loadImagesTitleText": "画像の読み込み中", "PE.Controllers.Main.loadImageTextText": "画像の読み込み中...", "PE.Controllers.Main.loadImageTitleText": "画像の読み込み中", "PE.Controllers.Main.loadingDocumentTextText": "プレゼンテーションの読み込み中...", "PE.Controllers.Main.loadingDocumentTitleText": "プレゼンテーションの読み込み中...", "PE.Controllers.Main.loadThemeTextText": "テーマの読み込み中...", "PE.Controllers.Main.loadThemeTitleText": "テーマの読み込み中", "PE.Controllers.Main.notcriticalErrorTitle": "警告", "PE.Controllers.Main.openErrorText": "ファイルを読み込み中にエラーが発生しました。", "PE.Controllers.Main.openTextText": "プレゼンテーションの読み込み中...", "PE.Controllers.Main.openTitleText": "プレゼンテーションの読み込み中", "PE.Controllers.Main.printTextText": "プレゼンテーションの印刷中...", "PE.Controllers.Main.printTitleText": "プレゼンテーションの印刷中", "PE.Controllers.Main.reloadButtonText": "ページを再読み込み", "PE.Controllers.Main.requestEditFailedMessageText": "現在、誰かがこのプレゼンテーションを編集しています。後で再試行してください。", "PE.Controllers.Main.requestEditFailedTitleText": "アクセスが拒否されました", "PE.Controllers.Main.saveErrorText": "ファイルを保存中にエラーが発生しました。", "PE.Controllers.Main.saveErrorTextDesktop": "このファイルは作成または保存できません。<br>考えられる理由は次のとおりです：<br>1. 閲覧のみのファイルです。<br>2. ファイルが他のユーザーによって編集されています。<br>3. ディスクが満杯か破損しています。", "PE.Controllers.Main.saveTextText": "プレゼンテーションを保存中...", "PE.Controllers.Main.saveTitleText": "プレゼンテーションを保存中", "PE.Controllers.Main.scriptLoadError": "インターネット接続が遅いため、一部のコンポーネントをロードできませんでした。ページを再読み込みしてください。", "PE.Controllers.Main.splitDividerErrorText": "行数は%1の除数になければなりません。", "PE.Controllers.Main.splitMaxColsErrorText": "列の数は%1より小さくなければなりません。", "PE.Controllers.Main.splitMaxRowsErrorText": "行数は%1より小さくなければなりません。", "PE.Controllers.Main.textAnonymous": "匿名", "PE.Controllers.Main.textApplyAll": "全ての数式に適用する", "PE.Controllers.Main.textBuyNow": "ウェブサイトにアクセス", "PE.Controllers.Main.textChangesSaved": "全ての変更点が保存されました", "PE.Controllers.Main.textClose": "閉じる", "PE.Controllers.Main.textCloseTip": "クリックしてヒントを閉じる", "PE.Controllers.Main.textConnectionLost": "接続中です。接続設定をご確認ください。", "PE.Controllers.Main.textContactUs": "営業部に連絡する", "PE.Controllers.Main.textContinue": "続ける", "PE.Controllers.Main.textConvertEquation": "この数式は、サポートされなくなった古いバージョンの数式エディタで作成されました。 編集するには、方程式をOffice Math ML形式に変換します。<br>今すぐ変換しますか？", "PE.Controllers.Main.textCustomLoader": "ライセンス条項により、ローダーを変更する権利がないことにご注意ください。<br>見積もりについては、弊社営業部門にお問い合わせください。", "PE.Controllers.Main.textDisconnect": "接続が切断されました", "PE.Controllers.Main.textGuest": "ゲスト", "PE.Controllers.Main.textHasMacros": "ファイルには自動マクロが含まれています。<br>マクロを実行しますか？", "PE.Controllers.Main.textLearnMore": "更に詳しく", "PE.Controllers.Main.textLoadingDocument": "プレゼンテーションの読み込み中...", "PE.Controllers.Main.textLongName": "128文字未満の名前を入力してください。", "PE.Controllers.Main.textNoLicenseTitle": "ライセンス制限に達しました", "PE.Controllers.Main.textObject": "オブジェクト", "PE.Controllers.Main.textPaidFeature": "有料機能", "PE.Controllers.Main.textReconnect": "接続が回復しました", "PE.Controllers.Main.textRemember": "すべてのファイルに選択を保存する", "PE.Controllers.Main.textRememberMacros": "すべてのマクロに、この選択を記憶する", "PE.Controllers.Main.textRenameError": "ユーザー名は空にできません。", "PE.Controllers.Main.textRenameLabel": "コラボレーションに使用する名前を入力して下さい。", "PE.Controllers.Main.textRequestMacros": "マクロがURLに対してリクエストを行います。%1へのリクエストを許可しますか？", "PE.Controllers.Main.textShape": "図形", "PE.Controllers.Main.textStrict": "厳格モード", "PE.Controllers.Main.textText": "テキスト", "PE.Controllers.Main.textTryQuickPrint": "クイックプリントが選択されています。ドキュメント全体が、最後に選択したプリンタまたはデフォルトのプリンタで印刷されます。<br>続行しますか?", "PE.Controllers.Main.textTryUndoRedo": "高速共同編集モードでは、元に戻す/やり直し機能は無効になります。<br>「厳格モード」ボタンをクリックすると、他のユーザーの干渉を受けずにファイルを編集し、保存後に変更内容を送信する厳格共同編集モードに切り替わります。共同編集モードの切り替えは、エディタの詳細設定を使用して行うことができます。", "PE.Controllers.Main.textTryUndoRedoWarn": "高速共同編集モードでは、元に戻す/やり直し機能が無効になります。", "PE.Controllers.Main.textUndo": "元に戻す", "PE.Controllers.Main.textUpdateVersion": "この文書は現在編集できません。<br>ファイルを更新しようとしています。しばらくお待ちください...", "PE.Controllers.Main.textUpdating": "アップデート中", "PE.Controllers.Main.titleLicenseExp": "ライセンスの有効期限が切れています", "PE.Controllers.Main.titleLicenseNotActive": "ライセンスが無効になっています", "PE.Controllers.Main.titleServerVersion": "編集者が更新されました", "PE.Controllers.Main.titleUpdateVersion": "バージョンが変更されました", "PE.Controllers.Main.txtAddFirstSlide": "クリックして最初のスライドを追加", "PE.Controllers.Main.txtAddNotes": "クリックでメモを追加", "PE.Controllers.Main.txtAnimationPane": "アニメーションパネル", "PE.Controllers.Main.txtArt": "ここにテキストを入力", "PE.Controllers.Main.txtBasicShapes": "基本図形", "PE.Controllers.Main.txtButtons": "ボタン", "PE.Controllers.Main.txtCallouts": "吹き出し", "PE.Controllers.Main.txtCharts": "グラフ", "PE.Controllers.Main.txtClipArt": "クリップアート", "PE.Controllers.Main.txtDateTime": "日付と時刻", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "グラフのタイトル", "PE.Controllers.Main.txtEditingMode": "編集モードを設定しています...", "PE.Controllers.Main.txtEnd": "終了： ${0}s", "PE.Controllers.Main.txtErrorLoadHistory": "履歴の読み込みに失敗しました。", "PE.Controllers.Main.txtFiguredArrows": "図形矢印", "PE.Controllers.Main.txtFirstSlide": "最初のスライド", "PE.Controllers.Main.txtFooter": "フッター", "PE.Controllers.Main.txtHeader": "ヘッダー", "PE.Controllers.Main.txtImage": "画像", "PE.Controllers.Main.txtLastSlide": "最後のスライド", "PE.Controllers.Main.txtLines": "線", "PE.Controllers.Main.txtLoading": "読み込み中...", "PE.Controllers.Main.txtLoop": "ループ： ${0}s", "PE.Controllers.Main.txtMath": "数学", "PE.Controllers.Main.txtMedia": "メディア", "PE.Controllers.Main.txtNeedSynchronize": "更新があります", "PE.Controllers.Main.txtNextSlide": "次のスライド", "PE.Controllers.Main.txtNone": "なし", "PE.Controllers.Main.txtPicture": "画像", "PE.Controllers.Main.txtPlayAll": "すべてを再生", "PE.Controllers.Main.txtPlayFrom": "再生", "PE.Controllers.Main.txtPlaySelected": "選択された項目を再生", "PE.Controllers.Main.txtPrevSlide": "前のスライド", "PE.Controllers.Main.txtRectangles": "四角形", "PE.Controllers.Main.txtSaveCopyAsComplete": "ファイルのコピーが正常に保存されました", "PE.Controllers.Main.txtScheme_Aspect": "アスペクト", "PE.Controllers.Main.txtScheme_Blue": "青色", "PE.Controllers.Main.txtScheme_Blue_Green": "ブルーグリーン", "PE.Controllers.Main.txtScheme_Blue_II": "青色II", "PE.Controllers.Main.txtScheme_Blue_Warm": "ブルーウォーム", "PE.Controllers.Main.txtScheme_Grayscale": "グレースケール", "PE.Controllers.Main.txtScheme_Green": "緑色", "PE.Controllers.Main.txtScheme_Green_Yellow": "黄緑色", "PE.Controllers.Main.txtScheme_Marquee": "マーキー", "PE.Controllers.Main.txtScheme_Median": "中位数", "PE.Controllers.Main.txtScheme_Office": "Office", "PE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "PE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "PE.Controllers.Main.txtScheme_Orange": "オレンジ色", "PE.Controllers.Main.txtScheme_Orange_Red": "オレンジ赤色", "PE.Controllers.Main.txtScheme_Paper": "紙", "PE.Controllers.Main.txtScheme_Red": "赤色", "PE.Controllers.Main.txtScheme_Red_Orange": "オレンジ赤色", "PE.Controllers.Main.txtScheme_Red_Violet": "赤紫色", "PE.Controllers.Main.txtScheme_Slipstream": "スリップストリーム", "PE.Controllers.Main.txtScheme_Violet": "バイオレット色", "PE.Controllers.Main.txtScheme_Violet_II": "バイオレット II", "PE.Controllers.Main.txtScheme_Yellow": "黄色", "PE.Controllers.Main.txtScheme_Yellow_Orange": "オレンジ黄色", "PE.Controllers.Main.txtSeries": "系列", "PE.Controllers.Main.txtShape_accentBorderCallout1": "線吹き出し１（枠付きと強調線）", "PE.Controllers.Main.txtShape_accentBorderCallout2": "線吹き出し２（枠付きと強調線）", "PE.Controllers.Main.txtShape_accentBorderCallout3": "線吹き出し３（枠付きと強調線）", "PE.Controllers.Main.txtShape_accentCallout1": "線吹き出し１（強調線）", "PE.Controllers.Main.txtShape_accentCallout2": "線吹き出し２（強調線）", "PE.Controllers.Main.txtShape_accentCallout3": "線吹き出し３（強調線）", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "［戻る］ボタン", "PE.Controllers.Main.txtShape_actionButtonBeginning": "［始めに戻る］ボタン", "PE.Controllers.Main.txtShape_actionButtonBlank": "空白ボタン", "PE.Controllers.Main.txtShape_actionButtonDocument": "文書ボタン", "PE.Controllers.Main.txtShape_actionButtonEnd": "［最後］ボタン", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "［次へ］のボタン", "PE.Controllers.Main.txtShape_actionButtonHelp": "「ヘルプ」ボタン", "PE.Controllers.Main.txtShape_actionButtonHome": "「ホーム」ボタン", "PE.Controllers.Main.txtShape_actionButtonInformation": "「情報」ボタン", "PE.Controllers.Main.txtShape_actionButtonMovie": "［ビデオ］ボタン", "PE.Controllers.Main.txtShape_actionButtonReturn": "「戻る」ボタン", "PE.Controllers.Main.txtShape_actionButtonSound": "「音」ボタン", "PE.Controllers.Main.txtShape_arc": "円弧", "PE.Controllers.Main.txtShape_bentArrow": "曲線の矢印", "PE.Controllers.Main.txtShape_bentConnector5": "カギ線コネクタ", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "カギ線矢印​​コネクタ", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "カギ線の二重矢印コネクタ", "PE.Controllers.Main.txtShape_bentUpArrow": "曲線の矢印（上）", "PE.Controllers.Main.txtShape_bevel": "斜角", "PE.Controllers.Main.txtShape_blockArc": "アーチ", "PE.Controllers.Main.txtShape_borderCallout1": "線吹き出し１　", "PE.Controllers.Main.txtShape_borderCallout2": "線吹き出し２", "PE.Controllers.Main.txtShape_borderCallout3": "線吹き出し３", "PE.Controllers.Main.txtShape_bracePair": "中かっこ", "PE.Controllers.Main.txtShape_callout1": "線吹き出し１（枠付き無し）", "PE.Controllers.Main.txtShape_callout2": "線吹き出し２（枠付き無し）", "PE.Controllers.Main.txtShape_callout3": "線吹き出し３（枠付き無し）", "PE.Controllers.Main.txtShape_can": "円筒", "PE.Controllers.Main.txtShape_chevron": "シェブロン", "PE.Controllers.Main.txtShape_chord": "コード", "PE.Controllers.Main.txtShape_circularArrow": "円弧の矢印", "PE.Controllers.Main.txtShape_cloud": "クラウド", "PE.Controllers.Main.txtShape_cloudCallout": "雲形吹き出し", "PE.Controllers.Main.txtShape_corner": "角", "PE.Controllers.Main.txtShape_cube": "立方体", "PE.Controllers.Main.txtShape_curvedConnector3": "曲線コネクタ", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "曲線矢印コネクタ", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "曲線の二重矢印コネクタ", "PE.Controllers.Main.txtShape_curvedDownArrow": "曲線の下向き矢印", "PE.Controllers.Main.txtShape_curvedLeftArrow": "曲線の左矢印", "PE.Controllers.Main.txtShape_curvedRightArrow": "曲線の右矢印", "PE.Controllers.Main.txtShape_curvedUpArrow": "曲線の上矢印", "PE.Controllers.Main.txtShape_decagon": "十角形", "PE.Controllers.Main.txtShape_diagStripe": "斜め縞", "PE.Controllers.Main.txtShape_diamond": "ひし型", "PE.Controllers.Main.txtShape_dodecagon": "十二角形", "PE.Controllers.Main.txtShape_donut": "ドーナツグラフ", "PE.Controllers.Main.txtShape_doubleWave": "二重波", "PE.Controllers.Main.txtShape_downArrow": "下矢印", "PE.Controllers.Main.txtShape_downArrowCallout": "下矢印吹き出し", "PE.Controllers.Main.txtShape_ellipse": "楕円", "PE.Controllers.Main.txtShape_ellipseRibbon": "下に湾曲したリボン", "PE.Controllers.Main.txtShape_ellipseRibbon2": "上に湾曲したリボン", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "フローチャート：代替処理", "PE.Controllers.Main.txtShape_flowChartCollate": "フローチャート：照合", "PE.Controllers.Main.txtShape_flowChartConnector": "フローチャート：コネクタ", "PE.Controllers.Main.txtShape_flowChartDecision": "フローチャート：判断", "PE.Controllers.Main.txtShape_flowChartDelay": "フローチャート：遅延", "PE.Controllers.Main.txtShape_flowChartDisplay": "フローチャート：表示", "PE.Controllers.Main.txtShape_flowChartDocument": "フローチャート：文書", "PE.Controllers.Main.txtShape_flowChartExtract": "フローチャート：抜き出し", "PE.Controllers.Main.txtShape_flowChartInputOutput": "フローチャート：データ", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "フローチャート：内部ストレージ", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "フローチャート：磁気ディスク", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "フローチャート：直接アクセスストレージ", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "フローチャート：順次アクセス記憶", "PE.Controllers.Main.txtShape_flowChartManualInput": "フローチャート：手動入力", "PE.Controllers.Main.txtShape_flowChartManualOperation": "フローチャート：手動操作", "PE.Controllers.Main.txtShape_flowChartMerge": "フローチャート：統合", "PE.Controllers.Main.txtShape_flowChartMultidocument": "フローチャート：複数文書", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "フローチャート：他ページ結合子", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "フローチャート：保存されたデータ", "PE.Controllers.Main.txtShape_flowChartOr": "フローチャート: 論理和", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "フローチャート：事前定義されたプロセス", "PE.Controllers.Main.txtShape_flowChartPreparation": "フローチャート：準備", "PE.Controllers.Main.txtShape_flowChartProcess": "フローチャート：プロセス", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "フローチャート：カード", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "フローチャート：せん孔テープ", "PE.Controllers.Main.txtShape_flowChartSort": "フローチャート：並べ替え", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "フローチャート：和接合", "PE.Controllers.Main.txtShape_flowChartTerminator": "フローチャート：端子", "PE.Controllers.Main.txtShape_foldedCorner": "折り曲げコーナー", "PE.Controllers.Main.txtShape_frame": "フレーム", "PE.Controllers.Main.txtShape_halfFrame": "半フレーム", "PE.Controllers.Main.txtShape_heart": "ハート", "PE.Controllers.Main.txtShape_heptagon": "七角形", "PE.Controllers.Main.txtShape_hexagon": "六角形", "PE.Controllers.Main.txtShape_homePlate": "五角形", "PE.Controllers.Main.txtShape_horizontalScroll": "水平スクロール", "PE.Controllers.Main.txtShape_irregularSeal1": "爆発　１", "PE.Controllers.Main.txtShape_irregularSeal2": "爆発　２", "PE.Controllers.Main.txtShape_leftArrow": "左矢印", "PE.Controllers.Main.txtShape_leftArrowCallout": "左矢印吹き出し", "PE.Controllers.Main.txtShape_leftBrace": "左中括弧", "PE.Controllers.Main.txtShape_leftBracket": "左括弧", "PE.Controllers.Main.txtShape_leftRightArrow": "左右矢印", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "左右矢印吹き出し", "PE.Controllers.Main.txtShape_leftRightUpArrow": "三方向矢印(左・右・上）", "PE.Controllers.Main.txtShape_leftUpArrow": "左上矢印", "PE.Controllers.Main.txtShape_lightningBolt": "稲妻", "PE.Controllers.Main.txtShape_line": "線", "PE.Controllers.Main.txtShape_lineWithArrow": "矢印", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "二重矢印", "PE.Controllers.Main.txtShape_mathDivide": "分割", "PE.Controllers.Main.txtShape_mathEqual": "イコール", "PE.Controllers.Main.txtShape_mathMinus": "マイナス", "PE.Controllers.Main.txtShape_mathMultiply": "乗算する", "PE.Controllers.Main.txtShape_mathNotEqual": "等しくない", "PE.Controllers.Main.txtShape_mathPlus": "プラス", "PE.Controllers.Main.txtShape_moon": "月形", "PE.Controllers.Main.txtShape_noSmoking": "「禁止」マーク", "PE.Controllers.Main.txtShape_notchedRightArrow": "切り欠き右矢印", "PE.Controllers.Main.txtShape_octagon": "八角形", "PE.Controllers.Main.txtShape_parallelogram": "平行四辺形", "PE.Controllers.Main.txtShape_pentagon": "五角形", "PE.Controllers.Main.txtShape_pie": "円グラフ", "PE.Controllers.Main.txtShape_plaque": "ブローチ", "PE.Controllers.Main.txtShape_plus": "プラス", "PE.Controllers.Main.txtShape_polyline1": "走り書き", "PE.Controllers.Main.txtShape_polyline2": "フリーフォーム", "PE.Controllers.Main.txtShape_quadArrow": "四方向矢印", "PE.Controllers.Main.txtShape_quadArrowCallout": "四方向矢印の吹き出し", "PE.Controllers.Main.txtShape_rect": "矩形", "PE.Controllers.Main.txtShape_ribbon": "下リボン", "PE.Controllers.Main.txtShape_ribbon2": "上リボン", "PE.Controllers.Main.txtShape_rightArrow": "右矢印", "PE.Controllers.Main.txtShape_rightArrowCallout": "右矢印吹き出し", "PE.Controllers.Main.txtShape_rightBrace": "右中括弧", "PE.Controllers.Main.txtShape_rightBracket": "右大括弧", "PE.Controllers.Main.txtShape_round1Rect": "1つの角を丸めた四角形", "PE.Controllers.Main.txtShape_round2DiagRect": "角丸長方形", "PE.Controllers.Main.txtShape_round2SameRect": "同辺角丸四角形", "PE.Controllers.Main.txtShape_roundRect": "角丸長方形", "PE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "PE.Controllers.Main.txtShape_smileyFace": "スマイル", "PE.Controllers.Main.txtShape_snip1Rect": "1つの角を切り取った四角形", "PE.Controllers.Main.txtShape_snip2DiagRect": "対角する2つの角を切り取った四角形", "PE.Controllers.Main.txtShape_snip2SameRect": "片側の2つの角を切り取った四角形", "PE.Controllers.Main.txtShape_snipRoundRect": "1つの角を切り取り1つの角を丸めた四角形", "PE.Controllers.Main.txtShape_spline": "曲線", "PE.Controllers.Main.txtShape_star10": "10ポイントスター", "PE.Controllers.Main.txtShape_star12": "12ポイントスター", "PE.Controllers.Main.txtShape_star16": "16ポイントスター", "PE.Controllers.Main.txtShape_star24": "24ポイントスター", "PE.Controllers.Main.txtShape_star32": "32ポイントスター", "PE.Controllers.Main.txtShape_star4": "4ポイントスター", "PE.Controllers.Main.txtShape_star5": "5ポイントスター", "PE.Controllers.Main.txtShape_star6": "6ポイントスター", "PE.Controllers.Main.txtShape_star7": "7ポイントスター", "PE.Controllers.Main.txtShape_star8": "8ポイントスター", "PE.Controllers.Main.txtShape_stripedRightArrow": "ストライプの右矢印", "PE.Controllers.Main.txtShape_sun": "太陽形", "PE.Controllers.Main.txtShape_teardrop": "涙の滴", "PE.Controllers.Main.txtShape_textRect": "テキストボックス", "PE.Controllers.Main.txtShape_trapezoid": "台形", "PE.Controllers.Main.txtShape_triangle": "三角形", "PE.Controllers.Main.txtShape_upArrow": "上矢印", "PE.Controllers.Main.txtShape_upArrowCallout": "上矢印吹き出し", "PE.Controllers.Main.txtShape_upDownArrow": "上下の双方向矢印", "PE.Controllers.Main.txtShape_uturnArrow": "U形矢印", "PE.Controllers.Main.txtShape_verticalScroll": "縦スクロール", "PE.Controllers.Main.txtShape_wave": "波", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "円形吹き出し", "PE.Controllers.Main.txtShape_wedgeRectCallout": "長方形の吹き出し", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "角丸長方形の吹き出し", "PE.Controllers.Main.txtSldLtTBlank": "空白", "PE.Controllers.Main.txtSldLtTChart": "チャート", "PE.Controllers.Main.txtSldLtTChartAndTx": "グラフとテキスト", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "クリップアートとテキスト", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "クリップアートと縦書きテキスト", "PE.Controllers.Main.txtSldLtTCust": "カスタム", "PE.Controllers.Main.txtSldLtTDgm": "図表", "PE.Controllers.Main.txtSldLtTFourObj": "四つのオブジェクト", "PE.Controllers.Main.txtSldLtTMediaAndTx": "メディアとテキスト", "PE.Controllers.Main.txtSldLtTObj": "タイトルとオブジェクト", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "一つのオブジェクトと二つのオブジェクト", "PE.Controllers.Main.txtSldLtTObjAndTx": "オブジェクトとテキスト", "PE.Controllers.Main.txtSldLtTObjOnly": "オブジェクト", "PE.Controllers.Main.txtSldLtTObjOverTx": "テキストの上にオブジェクト", "PE.Controllers.Main.txtSldLtTObjTx": "タイトル、オブジェクトと説明文", "PE.Controllers.Main.txtSldLtTPicTx": "画像と説明文", "PE.Controllers.Main.txtSldLtTSecHead": "セクション見出し", "PE.Controllers.Main.txtSldLtTTbl": "テーブル", "PE.Controllers.Main.txtSldLtTTitle": "タイトル", "PE.Controllers.Main.txtSldLtTTitleOnly": "タイトルのみ", "PE.Controllers.Main.txtSldLtTTwoColTx": "2段組みテキスト", "PE.Controllers.Main.txtSldLtTTwoObj": "二つのオブジェクト", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "二つのオブジェクトとオブジェクト", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "二つのオブジェクトとテキスト", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "テキストの上に二つのオブジェクト", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "二つのテキストと二つのオブジェクト", "PE.Controllers.Main.txtSldLtTTx": "テキスト", "PE.Controllers.Main.txtSldLtTTxAndChart": "テキストとグラフ", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "テキストとクリップアート", "PE.Controllers.Main.txtSldLtTTxAndMedia": "テキストとメディア", "PE.Controllers.Main.txtSldLtTTxAndObj": "テキストとオブジェクト", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "テキストと二つのオブジェクト", "PE.Controllers.Main.txtSldLtTTxOverObj": "オブジェクトの上にテキスト", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "縦書きタイトルとテキスト", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "縦書きタイトルとグラフの上にテキスト", "PE.Controllers.Main.txtSldLtTVertTx": "縦書きテキスト", "PE.Controllers.Main.txtSlideNumber": "スライド番号", "PE.Controllers.Main.txtSlideSubtitle": "スライドの小見出し", "PE.Controllers.Main.txtSlideText": "スライドのテキスト", "PE.Controllers.Main.txtSlideTitle": "スライドのタイトル", "PE.Controllers.Main.txtStarsRibbons": "スター＆リボン", "PE.Controllers.Main.txtStart": "スタート: ${0}s", "PE.Controllers.Main.txtStop": "停止", "PE.Controllers.Main.txtTheme_basic": "基本", "PE.Controllers.Main.txtTheme_blank": "空白", "PE.Controllers.Main.txtTheme_classic": "クラシック", "PE.Controllers.Main.txtTheme_corner": "角", "PE.Controllers.Main.txtTheme_dotted": "ドット付き", "PE.Controllers.Main.txtTheme_green": "グリーン", "PE.Controllers.Main.txtTheme_green_leaf": "緑色の葉", "PE.Controllers.Main.txtTheme_lines": "線", "PE.Controllers.Main.txtTheme_office": "Office", "PE.Controllers.Main.txtTheme_office_theme": "Officeテーマ", "PE.Controllers.Main.txtTheme_official": "公式", "PE.Controllers.Main.txtTheme_pixel": "ピクセル", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "亀", "PE.Controllers.Main.txtXAxis": "X軸", "PE.Controllers.Main.txtYAxis": "Y軸", "PE.Controllers.Main.txtZoom": "拡大図", "PE.Controllers.Main.unknownErrorText": "不明なエラーです。", "PE.Controllers.Main.unsupportedBrowserErrorText": "お使いのブラウザはサポートされていません。", "PE.Controllers.Main.uploadImageExtMessage": "不明な画像形式です。", "PE.Controllers.Main.uploadImageFileCountMessage": "画像のアップロードはありません。", "PE.Controllers.Main.uploadImageSizeMessage": "画像サイズの上限を超えました。サイズの上限は25MBです。", "PE.Controllers.Main.uploadImageTextText": "画像のアップロード中...", "PE.Controllers.Main.uploadImageTitleText": "画像のアップロード中", "PE.Controllers.Main.waitText": "少々お待ちください...", "PE.Controllers.Main.warnBrowserIE9": "このアプリケーションはIE9では低機能です。IE10以上のバージョンをご利用ください。", "PE.Controllers.Main.warnBrowserZoom": "お使いのブラウザの現在のZoomの設定は完全にはサポートされていません。Ctrl+0を押して、デフォルトのZoomにリセットしてください。", "PE.Controllers.Main.warnLicenseAnonymous": "匿名ユーザーのアクセスは拒否されます。<br>このドキュメントは閲覧専用に開かれます。", "PE.Controllers.Main.warnLicenseBefore": "ライセンスが無効になっています。<br>管理者までご連絡ください。", "PE.Controllers.Main.warnLicenseExceeded": "％1エディターへの同時接続の制限に達しました。 このドキュメントは表示専用で開かれます。<br>詳細については、管理者にお問い合わせください。", "PE.Controllers.Main.warnLicenseExp": "ライセンスの有効期限が切れています。<br>ライセンスを更新してページを再読み込みしてください。", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "ライセンスの有効期限が切れています。<br>ドキュメント編集機能にアクセスできません。<br>管理者にご連絡ください。", "PE.Controllers.Main.warnLicenseLimitedRenewed": "ライセンスを更新する必要があります。<br>ドキュメント編集機能へのアクセスが制限されています。<br>フルアクセスを取得するには、管理者にご連絡ください。", "PE.Controllers.Main.warnLicenseUsersExceeded": "％1エディターのユーザー制限に達しました。 詳細については、管理者にお問い合わせください。", "PE.Controllers.Main.warnNoLicense": "％1エディターへの同時接続の制限に達しました。 このドキュメントは閲覧のみを目的として開かれます。<br>個人的なアップグレード条件については、％1セールスチームにお問い合わせください。", "PE.Controllers.Main.warnNoLicenseUsers": "％1エディターのユーザー制限に達しました。 個人的なアップグレード条件については、％1営業チームにお問い合わせください。", "PE.Controllers.Main.warnProcessRightsChange": "ファイルを編集する権限を拒否されています。", "PE.Controllers.Print.txtPrintRangeInvalid": "無効な印刷範囲", "PE.Controllers.Print.txtPrintRangeSingleRange": "スライド番号のみ、またはスライド範囲のみを入力してください（例: 5-12）。またはPDFに印刷することもできます。", "PE.Controllers.Search.notcriticalErrorTitle": " 警告", "PE.Controllers.Search.textNoTextFound": "検索データが見つかりませんでした。他の検索設定を選択してください。", "PE.Controllers.Search.textReplaceSkipped": "置換が行われました。スキップされた発生回数は{0}です。", "PE.Controllers.Search.textReplaceSuccess": "検索が実行されました。{0}発生が置換されました。", "PE.Controllers.Search.warnReplaceString": "{0}は、「置換」ボックスで有効な特殊文字ではありません", "PE.Controllers.Statusbar.textDisconnect": "<b>接続が切断されました</b><br>接続を試みています。接続設定を確認してください。", "PE.Controllers.Statusbar.zoomText": "ズーム{0}%", "PE.Controllers.Toolbar.confirmAddFontName": "保存しようとしているフォントを現在のデバイスで使用することができません。<br>システムフォントを使って、テキストのスタイルが表示されます。利用可能になったとき、保存されたフォントが適用されます。<br>続行しますか。", "PE.Controllers.Toolbar.helpMergeShapes": "カスタムビジュアルを作成するために、形状を結合、断片化、交差、減算する操作を数秒で行うことができます。", "PE.Controllers.Toolbar.helpMergeShapesHeader": "図形を結合", "PE.Controllers.Toolbar.helpTabDesign": "新たに追加された「デザイン」タブからテーマの適用、カラーパレットの変更、スライドサイズの変更が可能です。", "PE.Controllers.Toolbar.helpTabDesignHeader": "「デザイン」タブ", "PE.Controllers.Toolbar.textAccent": "ダイアクリティカル・マーク", "PE.Controllers.Toolbar.textBracket": "括弧", "PE.Controllers.Toolbar.textFontSizeErr": "入力された値が正しくありません。<br>1〜300の数値を入力してください。", "PE.Controllers.Toolbar.textFraction": "分数", "PE.Controllers.Toolbar.textFunction": "関数", "PE.Controllers.Toolbar.textInsert": "挿入", "PE.Controllers.Toolbar.textIntegral": "積分", "PE.Controllers.Toolbar.textLargeOperator": "大型演算子", "PE.Controllers.Toolbar.textLimitAndLog": "極限と対数", "PE.Controllers.Toolbar.textMatrix": "行列", "PE.Controllers.Toolbar.textOperator": "演算子", "PE.Controllers.Toolbar.textRadical": "ラジカル", "PE.Controllers.Toolbar.textScript": "スクリプト", "PE.Controllers.Toolbar.textSymbols": "記号", "PE.Controllers.Toolbar.textWarning": "警告", "PE.Controllers.Toolbar.txtAccent_Accent": "アキュート", "PE.Controllers.Toolbar.txtAccent_ArrowD": "左右双方向矢印 (上)", "PE.Controllers.Toolbar.txtAccent_ArrowL": "左に矢印 (上)", "PE.Controllers.Toolbar.txtAccent_ArrowR": "右向き矢印 (上)", "PE.Controllers.Toolbar.txtAccent_Bar": "横棒グラフ", "PE.Controllers.Toolbar.txtAccent_BarBot": "アンダーバー", "PE.Controllers.Toolbar.txtAccent_BarTop": "オーバーライン", "PE.Controllers.Toolbar.txtAccent_BorderBox": "四角囲み数式 (プレースホルダ付き)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "四角囲み数式 (例)", "PE.Controllers.Toolbar.txtAccent_Check": "チェック", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "下括弧", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "上括弧", "PE.Controllers.Toolbar.txtAccent_Custom_1": "ベクトルA", "PE.Controllers.Toolbar.txtAccent_Custom_2": "オーバーライン付き ABC", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XORとオーバーライン", "PE.Controllers.Toolbar.txtAccent_DDDot": "トリプルドット", "PE.Controllers.Toolbar.txtAccent_DDot": "二重ドット", "PE.Controllers.Toolbar.txtAccent_Dot": "点", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "二重オーバーライン", "PE.Controllers.Toolbar.txtAccent_Grave": "グレイヴ", "PE.Controllers.Toolbar.txtAccent_GroupBot": "グループ文字 (下)", "PE.Controllers.Toolbar.txtAccent_GroupTop": "グループ文字 (上)", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "左半矢印(上)", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "右向き半矢印 (上)", "PE.Controllers.Toolbar.txtAccent_Hat": "ハット", "PE.Controllers.Toolbar.txtAccent_Smile": "ブレーヴェ", "PE.Controllers.Toolbar.txtAccent_Tilde": "チルダ", "PE.Controllers.Toolbar.txtBracket_Angle": "括弧", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "山かっこと縦棒", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "山かっこと縦棒 2 本", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "終わり山かっこ", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "始め山かっこ", "PE.Controllers.Toolbar.txtBracket_Curve": "中かっこ", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "中かっこと縦棒", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "右中かっこ", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "左中かっこ", "PE.Controllers.Toolbar.txtBracket_Custom_1": "場合分け(条件2つ)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "場合分け (条件3つ)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "縦並びオブジェクト", "PE.Controllers.Toolbar.txtBracket_Custom_4": "縦並びオブジェクト (かっこ付き)", "PE.Controllers.Toolbar.txtBracket_Custom_5": "場合分けの例", "PE.Controllers.Toolbar.txtBracket_Custom_6": "二項係数", "PE.Controllers.Toolbar.txtBracket_Custom_7": "二項係数 (山かっこ付き)", "PE.Controllers.Toolbar.txtBracket_Line": "縦棒", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "縦棒 (右のみ)", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "縦棒 (左のみ)", "PE.Controllers.Toolbar.txtBracket_LineDouble": "二重縦棒", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "二重縦棒 (右のみ)", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "単一括弧", "PE.Controllers.Toolbar.txtBracket_LowLim": "終わりかっこ", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "床関数 (右記号)", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "床関数 (左記号)", "PE.Controllers.Toolbar.txtBracket_Round": "括弧", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "括弧と区切り線", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "右かっこ", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "左かっこ", "PE.Controllers.Toolbar.txtBracket_Square": "大かっこ", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "右の角括弧の間のプレースホルダー", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "反転した角括弧", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "右角かっこ", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "左角かっこ", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "左の角括弧の間のプレースホルダー", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "二重の角括弧", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "右ダブル角型かっこ", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "左ダブル角型かっこ", "PE.Controllers.Toolbar.txtBracket_UppLim": "天井大かっこ", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "天井関数 (右記号)", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "単一かっこ", "PE.Controllers.Toolbar.txtFractionDiagonal": "分数 (斜め)", "PE.Controllers.Toolbar.txtFractionDifferential_1": "微分", "PE.Controllers.Toolbar.txtFractionDifferential_2": "大文字デルタ y/大文字デルタ x", "PE.Controllers.Toolbar.txtFractionDifferential_3": "部分的なxに対する部分的なy", "PE.Controllers.Toolbar.txtFractionDifferential_4": "デルタ y/デルタ x", "PE.Controllers.Toolbar.txtFractionHorizontal": "分数 (横)", "PE.Controllers.Toolbar.txtFractionPi_2": "円周率を2で割る", "PE.Controllers.Toolbar.txtFractionSmall": "分数 (小)", "PE.Controllers.Toolbar.txtFractionVertical": "分数 (縦)", "PE.Controllers.Toolbar.txtFunction_1_Cos": "逆余弦関数", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "双曲線逆余弦関数", "PE.Controllers.Toolbar.txtFunction_1_Cot": "逆余接関数", "PE.Controllers.Toolbar.txtFunction_1_Coth": "双曲線逆共接関数", "PE.Controllers.Toolbar.txtFunction_1_Csc": "逆余割関数", "PE.Controllers.Toolbar.txtFunction_1_Csch": "逆双曲線余割関数", "PE.Controllers.Toolbar.txtFunction_1_Sec": "逆正割関数", "PE.Controllers.Toolbar.txtFunction_1_Sech": "双曲線逆正割関数", "PE.Controllers.Toolbar.txtFunction_1_Sin": "逆正弦関数", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "双曲線逆正弦関数", "PE.Controllers.Toolbar.txtFunction_1_Tan": "逆正接関数", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "双曲線逆正接関数", "PE.Controllers.Toolbar.txtFunction_Cos": "余弦関数", "PE.Controllers.Toolbar.txtFunction_Cosh": "双曲線余弦関数", "PE.Controllers.Toolbar.txtFunction_Cot": "余接関数", "PE.Controllers.Toolbar.txtFunction_Coth": "双曲線余接関数", "PE.Controllers.Toolbar.txtFunction_Csc": "余割関数\t", "PE.Controllers.Toolbar.txtFunction_Csch": "双曲線余割関数", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sin θ", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "正接数式", "PE.Controllers.Toolbar.txtFunction_Sec": "正割関数", "PE.Controllers.Toolbar.txtFunction_Sech": "双曲線正割関数", "PE.Controllers.Toolbar.txtFunction_Sin": "正弦関数", "PE.Controllers.Toolbar.txtFunction_Sinh": "双曲線正弦関数", "PE.Controllers.Toolbar.txtFunction_Tan": "正接関数", "PE.Controllers.Toolbar.txtFunction_Tanh": "双曲線正接関数", "PE.Controllers.Toolbar.txtIntegral": "積分", "PE.Controllers.Toolbar.txtIntegral_dtheta": "微分シータ", "PE.Controllers.Toolbar.txtIntegral_dx": "微分x", "PE.Controllers.Toolbar.txtIntegral_dy": "微分y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "積分 (上下端値を上下に配置)", "PE.Controllers.Toolbar.txtIntegralDouble": "二重積分", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "二重積分 (上下端値を上下に配置)", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "二重積分 (上下端値あり)", "PE.Controllers.Toolbar.txtIntegralOriented": "周回積分", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "線積分 (上下端値を上下に配置)", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "面積分", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "面積分 (上下端値を上下に配置)", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "面積分 (上下端値あり)", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "線積分 (上下端値あり)", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "体積積分", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "体積積分 (上下端値を上下に配置)", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "体積積分 (上下端値あり)", "PE.Controllers.Toolbar.txtIntegralSubSup": "積分 (上下端値あり)", "PE.Controllers.Toolbar.txtIntegralTriple": "三重積分", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "三重積分 (上下端値を上下に配置)", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "三重積分 (上下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "論理積", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "論理積 (下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "論理積 (上下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "論理積 (下付き文字の下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "論理積 (上付き/下付き文字の上下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "余積", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "下端付き余積", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "極限付き余積", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "下端下付き双対積", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "上下付き極限付き双対積", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "n から k を選ぶ場合の k の総和", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "総和 (i = 0 から n まで)", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "添え字 2 個を使う総和の例", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "積の例", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "和集合の例", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "論理和", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "論理和 (下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "論理和 (上下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "論理和 (下付き文字の下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "論理和 (上付き/下付き文字の上下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "共通集合", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "積集合 (下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "積集合 (上下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "積集合 (下付き文字の下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "積集合 (上付き/下付き文字の上下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "乗積", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "積 (下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "積 (上下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "積 (下付き文字の下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "積 (上付き/下付き文字の上下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "合計", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "総和 (下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "総和 (上下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "総和 (下付き文字の下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "総和 (上付き/下付き文字の上下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Union": "和集合", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "和集合 (下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "和集合 (上下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "和集合 (下付き文字の下端値あり)", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "和集合 (下付き/上付き文字の上下端値あり)", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "極限の例", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大値の例", "PE.Controllers.Toolbar.txtLimitLog_Lim": "極限", "PE.Controllers.Toolbar.txtLimitLog_Ln": "自然対数", "PE.Controllers.Toolbar.txtLimitLog_Log": "対数", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "対数", "PE.Controllers.Toolbar.txtLimitLog_Max": "最大", "PE.Controllers.Toolbar.txtLimitLog_Min": "最小", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2空行列", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3空行列", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 空行列", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 空行列", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "空の 2x2 行列 (二重縦棒付き)", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "空の 2x2 行列式", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "空の 2x2 行列 (かっこ付き)", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "空の 2x2 行列 (大かっこ付き)", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 空行列", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 空行列", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 空行列", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 空行列", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "基準線点", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "ミッドラインドット", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "斜めドット", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "縦向きドット", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "疎行列 (かっこ付き)", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "疎行列 (大かっこ付き)", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 単位行列 (0 あり)", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "空白の対角セルを持つ 2x2 の単位行列", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 単位行列 (0 あり)", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 単位行列 (対角線上以外のセルは空白)", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "左右双方向矢印 (下)", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "左右双方向矢印 (上)", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "左に矢印 (下)", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "左に矢印 (上)", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "右向き矢印 (下)", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "右向き矢印 (上)", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "コロンイコール", "PE.Controllers.Toolbar.txtOperator_Custom_1": "導出", "PE.Controllers.Toolbar.txtOperator_Custom_2": "デルタ収量", "PE.Controllers.Toolbar.txtOperator_Definition": "定義上等しい", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "デルタ付き等号", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "左右双方向矢印 (下)", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "左右双方向矢印 (上)", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "左に矢印 (下)", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "左に矢印 (上)", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "右向き矢印 (下)", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "右向き矢印 (上)", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "イコールイコール", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "マイナスイコール", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "プラスイコール", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "によって測定", "PE.Controllers.Toolbar.txtRadicalCustom_1": "二次方程式の解の公式の右辺", "PE.Controllers.Toolbar.txtRadicalCustom_2": "a の 2 乗と b の 2 乗の和の平方根", "PE.Controllers.Toolbar.txtRadicalRoot_2": "次数付き平方根", "PE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "PE.Controllers.Toolbar.txtRadicalRoot_n": "度付きラジカル", "PE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "PE.Controllers.Toolbar.txtScriptCustom_1": "x 下付き文字 y の 2 乗", "PE.Controllers.Toolbar.txtScriptCustom_2": "e のマイナス i ω t 乗", "PE.Controllers.Toolbar.txtScriptCustom_3": "x の 2 乗", "PE.Controllers.Toolbar.txtScriptCustom_4": "Y 左上付き文字 n 左下付き文字 1", "PE.Controllers.Toolbar.txtScriptSub": "下付き文字", "PE.Controllers.Toolbar.txtScriptSubSup": "下付き文字 - 上付き文字", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "左下付き文字 - 上付き文字", "PE.Controllers.Toolbar.txtScriptSup": "上付き文字", "PE.Controllers.Toolbar.txtSymbol_about": "約", "PE.Controllers.Toolbar.txtSymbol_additional": "補数", "PE.Controllers.Toolbar.txtSymbol_aleph": "アレフ", "PE.Controllers.Toolbar.txtSymbol_alpha": "アルファ", "PE.Controllers.Toolbar.txtSymbol_approx": "にほぼ等しい", "PE.Controllers.Toolbar.txtSymbol_ast": "アスタリスク", "PE.Controllers.Toolbar.txtSymbol_beta": "ベータ", "PE.Controllers.Toolbar.txtSymbol_beth": "ベート", "PE.Controllers.Toolbar.txtSymbol_bullet": "箇条書きの演算子", "PE.Controllers.Toolbar.txtSymbol_cap": "共通集合", "PE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "PE.Controllers.Toolbar.txtSymbol_cdots": "水平中央の省略記号", "PE.Controllers.Toolbar.txtSymbol_celsius": "摂氏", "PE.Controllers.Toolbar.txtSymbol_chi": "カイ", "PE.Controllers.Toolbar.txtSymbol_cong": "にほぼ等しい", "PE.Controllers.Toolbar.txtSymbol_cup": "和集合", "PE.Controllers.Toolbar.txtSymbol_ddots": "下右斜めの省略記号", "PE.Controllers.Toolbar.txtSymbol_degree": "度", "PE.Controllers.Toolbar.txtSymbol_delta": "デルタ", "PE.Controllers.Toolbar.txtSymbol_div": "除算記号", "PE.Controllers.Toolbar.txtSymbol_downarrow": "下矢印", "PE.Controllers.Toolbar.txtSymbol_emptyset": "空集合", "PE.Controllers.Toolbar.txtSymbol_epsilon": "イプシロン", "PE.Controllers.Toolbar.txtSymbol_equals": "イコール", "PE.Controllers.Toolbar.txtSymbol_equiv": "と同一", "PE.Controllers.Toolbar.txtSymbol_eta": "エータ", "PE.Controllers.Toolbar.txtSymbol_exists": "存在します\t", "PE.Controllers.Toolbar.txtSymbol_factorial": "階乗", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "華氏", "PE.Controllers.Toolbar.txtSymbol_forall": "全てに", "PE.Controllers.Toolbar.txtSymbol_gamma": "ガンマ", "PE.Controllers.Toolbar.txtSymbol_geq": "次の値より大きいか等しい", "PE.Controllers.Toolbar.txtSymbol_gg": "次の値よりはるかに大きい", "PE.Controllers.Toolbar.txtSymbol_greater": "次の値より大きい", "PE.Controllers.Toolbar.txtSymbol_in": "属する", "PE.Controllers.Toolbar.txtSymbol_inc": "増分", "PE.Controllers.Toolbar.txtSymbol_infinity": "無限", "PE.Controllers.Toolbar.txtSymbol_iota": "イオタ", "PE.Controllers.Toolbar.txtSymbol_kappa": "カッパ", "PE.Controllers.Toolbar.txtSymbol_lambda": "ラムダ", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "左矢印", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右矢印", "PE.Controllers.Toolbar.txtSymbol_leq": "次の値より小さいか等しい", "PE.Controllers.Toolbar.txtSymbol_less": "次の値より小さい", "PE.Controllers.Toolbar.txtSymbol_ll": "次の値よりはるかに小さい", "PE.Controllers.Toolbar.txtSymbol_minus": "マイナス", "PE.Controllers.Toolbar.txtSymbol_mp": "マイナスプラス", "PE.Controllers.Toolbar.txtSymbol_mu": "ミュー", "PE.Controllers.Toolbar.txtSymbol_nabla": "ナブラ", "PE.Controllers.Toolbar.txtSymbol_neq": "と等しくない", "PE.Controllers.Toolbar.txtSymbol_ni": "含む", "PE.Controllers.Toolbar.txtSymbol_not": "否定記号", "PE.Controllers.Toolbar.txtSymbol_notexists": "存在しません", "PE.Controllers.Toolbar.txtSymbol_nu": "ニュー", "PE.Controllers.Toolbar.txtSymbol_o": "オミクロン", "PE.Controllers.Toolbar.txtSymbol_omega": "オメガ", "PE.Controllers.Toolbar.txtSymbol_partial": "偏微分", "PE.Controllers.Toolbar.txtSymbol_percent": "パーセンテージ", "PE.Controllers.Toolbar.txtSymbol_phi": "ファイ", "PE.Controllers.Toolbar.txtSymbol_pi": "パイ", "PE.Controllers.Toolbar.txtSymbol_plus": "プラス", "PE.Controllers.Toolbar.txtSymbol_pm": "プラスマイナス", "PE.Controllers.Toolbar.txtSymbol_propto": "に比例", "PE.Controllers.Toolbar.txtSymbol_psi": "プサイ", "PE.Controllers.Toolbar.txtSymbol_qdrt": "四乗根", "PE.Controllers.Toolbar.txtSymbol_qed": "証明終了", "PE.Controllers.Toolbar.txtSymbol_rddots": "斜め(右上)の省略記号", "PE.Controllers.Toolbar.txtSymbol_rho": "ロー", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "右矢印", "PE.Controllers.Toolbar.txtSymbol_sigma": "シグマ", "PE.Controllers.Toolbar.txtSymbol_sqrt": "根号", "PE.Controllers.Toolbar.txtSymbol_tau": "タウ", "PE.Controllers.Toolbar.txtSymbol_therefore": "従って", "PE.Controllers.Toolbar.txtSymbol_theta": "シータ", "PE.Controllers.Toolbar.txtSymbol_times": "乗算記号", "PE.Controllers.Toolbar.txtSymbol_uparrow": "上矢印", "PE.Controllers.Toolbar.txtSymbol_upsilon": "ウプシロン", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "イプシロン (別形)", "PE.Controllers.Toolbar.txtSymbol_varphi": "ファイ (別形)", "PE.Controllers.Toolbar.txtSymbol_varpi": "パイ", "PE.Controllers.Toolbar.txtSymbol_varrho": "ロー (別形)", "PE.Controllers.Toolbar.txtSymbol_varsigma": "シグマ (別形)", "PE.Controllers.Toolbar.txtSymbol_vartheta": "シータ (別形)", "PE.Controllers.Toolbar.txtSymbol_vdots": "垂直線の省略記号", "PE.Controllers.Toolbar.txtSymbol_xsi": "グザイ", "PE.Controllers.Toolbar.txtSymbol_zeta": "ゼータ", "PE.Controllers.Viewport.textFitPage": "スライドに合わせる", "PE.Controllers.Viewport.textFitWidth": "幅に合わせる", "PE.Views.Animation.str0_5": "0.5秒（さらに速く）", "PE.Views.Animation.str1": "1秒（速く）", "PE.Views.Animation.str2": "2秒（中）", "PE.Views.Animation.str20": "20秒（非常に遅い）", "PE.Views.Animation.str3": "3秒（遅い）", "PE.Views.Animation.str5": "5秒（さらに遅く）", "PE.Views.Animation.strDelay": "遅延", "PE.Views.Animation.strDuration": "期間", "PE.Views.Animation.strRepeat": "繰り返し", "PE.Views.Animation.strRewind": "巻き戻し", "PE.Views.Animation.strStart": "開始", "PE.Views.Animation.strTrigger": "トリガー", "PE.Views.Animation.textAutoPreview": "自動プレビュー", "PE.Views.Animation.textMoreEffects": "その他のエフェクトを表示", "PE.Views.Animation.textMoveEarlier": "先に移動する", "PE.Views.Animation.textMoveLater": "後に移動する", "PE.Views.Animation.textMultiple": "倍数", "PE.Views.Animation.textNone": "なし", "PE.Views.Animation.textNoRepeat": "（なし）", "PE.Views.Animation.textOnClickOf": "クリック時:", "PE.Views.Animation.textOnClickSequence": "クリックシーケンス", "PE.Views.Animation.textStartAfterPrevious": "直前の動作の後", "PE.Views.Animation.textStartOnClick": "クリック時", "PE.Views.Animation.textStartWithPrevious": "直前の動作と同時", "PE.Views.Animation.textUntilEndOfSlide": "スライドの最後まで", "PE.Views.Animation.textUntilNextClick": "次のクリックまで", "PE.Views.Animation.txtAddEffect": "アニメーションを追加", "PE.Views.Animation.txtAnimationPane": "アニメーション ウィンドウ", "PE.Views.Animation.txtParameters": "パラメーター", "PE.Views.Animation.txtPreview": "プレビュー", "PE.Views.Animation.txtSec": "秒", "PE.Views.AnimationDialog.textPreviewEffect": "効果のプレビュー", "PE.Views.AnimationDialog.textTitle": "その他のエフェクト", "PE.Views.ChartSettings.text3dDepth": "深さ(ベースに対する割合)", "PE.Views.ChartSettings.text3dHeight": "高<PERSON>(ベースに対する割合)", "PE.Views.ChartSettings.text3dRotation": "3D回転", "PE.Views.ChartSettings.textAdvanced": "詳細設定の表示", "PE.Views.ChartSettings.textAutoscale": "自動スケーリング", "PE.Views.ChartSettings.textChartType": "グラフの種類を変更", "PE.Views.ChartSettings.textDefault": "デフォルト回転", "PE.Views.ChartSettings.textDown": "下", "PE.Views.ChartSettings.textEditData": "データを編集", "PE.Views.ChartSettings.textHeight": "高さ", "PE.Views.ChartSettings.textKeepRatio": "一定の比率", "PE.Views.ChartSettings.textLeft": "左", "PE.Views.ChartSettings.textNarrow": "狭角", "PE.Views.ChartSettings.textPerspective": "分析観点", "PE.Views.ChartSettings.textRight": "右", "PE.Views.ChartSettings.textRightAngle": "軸の直交", "PE.Views.ChartSettings.textSize": "サイズ", "PE.Views.ChartSettings.textStyle": "スタイル", "PE.Views.ChartSettings.textUp": "上", "PE.Views.ChartSettings.textWiden": "広角", "PE.Views.ChartSettings.textWidth": "幅", "PE.Views.ChartSettings.textX": "X 回転", "PE.Views.ChartSettings.textY": "Y 回転", "PE.Views.ChartSettingsAdvanced.textAlt": "代替テキスト", "PE.Views.ChartSettingsAdvanced.textAltDescription": "説明", "PE.Views.ChartSettingsAdvanced.textAltTip": "視覚障害や認知障害のある人が、画像や図形、図表にどのような情報が含まれているかを理解しやすくするため、そのオブジェクトについて目視できる情報を文章で表現したものです。", "PE.Views.ChartSettingsAdvanced.textAltTitle": "タイトル", "PE.Views.ChartSettingsAdvanced.textCenter": "中央揃え", "PE.Views.ChartSettingsAdvanced.textChartName": "チャート名", "PE.Views.ChartSettingsAdvanced.textFrom": "基準", "PE.Views.ChartSettingsAdvanced.textGeneral": "一般", "PE.Views.ChartSettingsAdvanced.textHeight": "高さ", "PE.Views.ChartSettingsAdvanced.textHorizontal": "水平", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "一定の比率", "PE.Views.ChartSettingsAdvanced.textPlacement": "位置", "PE.Views.ChartSettingsAdvanced.textPosition": "位置", "PE.Views.ChartSettingsAdvanced.textSize": "サイズ", "PE.Views.ChartSettingsAdvanced.textTitle": "グラフ - 詳細設定", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "左上隅", "PE.Views.ChartSettingsAdvanced.textVertical": "縦", "PE.Views.ChartSettingsAdvanced.textWidth": "幅", "PE.Views.DateTimeDialog.confirmDefault": "{0}にデフォルトの形式を設定：\"{1}\"", "PE.Views.DateTimeDialog.textDefault": "デフォルトに設定", "PE.Views.DateTimeDialog.textFormat": "フォーマット", "PE.Views.DateTimeDialog.textLang": "言語", "PE.Views.DateTimeDialog.textUpdate": "自動的に更新", "PE.Views.DateTimeDialog.txtTitle": "日付と時刻", "PE.Views.DocumentHolder.aboveText": "上", "PE.Views.DocumentHolder.addCommentText": "コメントを追加", "PE.Views.DocumentHolder.advancedChartText": "チャートの詳細設定", "PE.Views.DocumentHolder.advancedEquationText": "数式設定", "PE.Views.DocumentHolder.advancedImageText": "画像の詳細設定", "PE.Views.DocumentHolder.advancedParagraphText": "段落の詳細設定", "PE.Views.DocumentHolder.advancedShapeText": "図形の詳細設定", "PE.Views.DocumentHolder.advancedTableText": "テーブルの詳細設定", "PE.Views.DocumentHolder.alignmentText": "配置", "PE.Views.DocumentHolder.allLinearText": "すべて - 線形", "PE.Views.DocumentHolder.allProfText": "すべて - プロフェッショナル", "PE.Views.DocumentHolder.belowText": "下", "PE.Views.DocumentHolder.cellAlignText": "セルの縦方向の配置", "PE.Views.DocumentHolder.cellText": "セル", "PE.Views.DocumentHolder.centerText": "中央揃え", "PE.Views.DocumentHolder.columnText": "列", "PE.Views.DocumentHolder.currLinearText": "現在 - 線形", "PE.Views.DocumentHolder.currProfText": "現在 - プロフェッショナル", "PE.Views.DocumentHolder.deleteColumnText": "列を削除", "PE.Views.DocumentHolder.deleteRowText": "行を削除", "PE.Views.DocumentHolder.deleteTableText": "表を削除する", "PE.Views.DocumentHolder.deleteText": "削除する", "PE.Views.DocumentHolder.direct270Text": "上にテキストを回転", "PE.Views.DocumentHolder.direct90Text": "下にテキストを回転", "PE.Views.DocumentHolder.directHText": "水平", "PE.Views.DocumentHolder.directionText": "文字列の方向", "PE.Views.DocumentHolder.editChartText": "データを編集", "PE.Views.DocumentHolder.editHyperlinkText": "ハイパーリンクを編集", "PE.Views.DocumentHolder.hideEqToolbar": "方程式ツールバーを非表示にする", "PE.Views.DocumentHolder.hyperlinkText": "ハイパーリンク", "PE.Views.DocumentHolder.ignoreAllSpellText": "全て無視する", "PE.Views.DocumentHolder.ignoreSpellText": "無視する", "PE.Views.DocumentHolder.insertColumnLeftText": "左の列", "PE.Views.DocumentHolder.insertColumnRightText": "右の列", "PE.Views.DocumentHolder.insertColumnText": "列の挿入", "PE.Views.DocumentHolder.insertRowAboveText": "行 (上)", "PE.Views.DocumentHolder.insertRowBelowText": "行(下)", "PE.Views.DocumentHolder.insertRowText": "行の挿入", "PE.Views.DocumentHolder.insertText": "挿入", "PE.Views.DocumentHolder.langText": "言語の選択", "PE.Views.DocumentHolder.latexText": "LaTeX", "PE.Views.DocumentHolder.leftText": "左", "PE.Views.DocumentHolder.loadSpellText": "バリエーションの読み込み中...", "PE.Views.DocumentHolder.mergeCellsText": "セルの結合", "PE.Views.DocumentHolder.mniCustomTable": "カスタムテーブルの挿入", "PE.Views.DocumentHolder.moreText": "その他のバリエーション...", "PE.Views.DocumentHolder.noSpellVariantsText": "バリエーションなし", "PE.Views.DocumentHolder.originalSizeText": "実際のサイズ", "PE.Views.DocumentHolder.removeHyperlinkText": "ハイパーリンクを削除", "PE.Views.DocumentHolder.rightText": "右", "PE.Views.DocumentHolder.rowText": "行", "PE.Views.DocumentHolder.selectText": "選択", "PE.Views.DocumentHolder.showEqToolbar": "方程式ツールバーの表示", "PE.Views.DocumentHolder.spellcheckText": "スペルチェック", "PE.Views.DocumentHolder.splitCellsText": "セルを分割...", "PE.Views.DocumentHolder.splitCellTitleText": "セルを分割", "PE.Views.DocumentHolder.tableText": "テーブル", "PE.Views.DocumentHolder.textAddHGuides": "水平方向のガイドの追加", "PE.Views.DocumentHolder.textAddVGuides": "垂直方向のガイドの追加", "PE.Views.DocumentHolder.textArrangeBack": "最背面ヘ移動", "PE.Views.DocumentHolder.textArrangeBackward": "背面ヘ移動", "PE.Views.DocumentHolder.textArrangeForward": "前面ヘ移動", "PE.Views.DocumentHolder.textArrangeFront": "最前面ヘ移動", "PE.Views.DocumentHolder.textClearGuides": "ガイドのクリア", "PE.Views.DocumentHolder.textCm": "センチ", "PE.Views.DocumentHolder.textCopy": "コピーする", "PE.Views.DocumentHolder.textCrop": "トリミング", "PE.Views.DocumentHolder.textCropFill": "塗りつぶし", "PE.Views.DocumentHolder.textCropFit": "合わせる", "PE.Views.DocumentHolder.textCustom": "ユーザー設定", "PE.Views.DocumentHolder.textCut": "切り取り", "PE.Views.DocumentHolder.textDeleteGuide": "ガイドの削除", "PE.Views.DocumentHolder.textDeleteLayout": "レイアウトの削除", "PE.Views.DocumentHolder.textDeleteMaster": "マスター削除", "PE.Views.DocumentHolder.textDistributeCols": "列の幅を揃える", "PE.Views.DocumentHolder.textDistributeRows": "行の高さを揃える", "PE.Views.DocumentHolder.textDuplicateLayout": "レイアウトの複製", "PE.Views.DocumentHolder.textDuplicateSlideMaster": "スライドマスターの複製", "PE.Views.DocumentHolder.textEditObject": "オブジェクトを編集", "PE.Views.DocumentHolder.textEditPoints": "頂点を編集", "PE.Views.DocumentHolder.textFlipH": "左右に反転", "PE.Views.DocumentHolder.textFlipV": "上下に反転", "PE.Views.DocumentHolder.textFromFile": "ファイルから", "PE.Views.DocumentHolder.textFromStorage": "ストレージから", "PE.Views.DocumentHolder.textFromUrl": "URLから", "PE.Views.DocumentHolder.textGridlines": "グリッド線", "PE.Views.DocumentHolder.textGuides": "ガイド", "PE.Views.DocumentHolder.textInsertLayout": "インサートレイアウト", "PE.Views.DocumentHolder.textInsertSlideMaster": "スライドマスターの挿入", "PE.Views.DocumentHolder.textNextPage": "次のスライド", "PE.Views.DocumentHolder.textPaste": "貼り付け", "PE.Views.DocumentHolder.textPrevPage": "前のスライド", "PE.Views.DocumentHolder.textRemove": "削除", "PE.Views.DocumentHolder.textRenameLayout": "レイアウト名を変更", "PE.Views.DocumentHolder.textRenameMaster": "マスター名の変更", "PE.Views.DocumentHolder.textReplace": "画像を置き換える", "PE.Views.DocumentHolder.textResetCrop": "トリミングをリセット", "PE.Views.DocumentHolder.textRotate": "回転", "PE.Views.DocumentHolder.textRotate270": "反時計回りに90度回転", "PE.Views.DocumentHolder.textRotate90": "時計回りに90度回転", "PE.Views.DocumentHolder.textRulers": "ルーラー", "PE.Views.DocumentHolder.textSaveAsPicture": "画像として保存", "PE.Views.DocumentHolder.textShapeAlignBottom": "下揃え", "PE.Views.DocumentHolder.textShapeAlignCenter": "中央揃え\t", "PE.Views.DocumentHolder.textShapeAlignLeft": "左揃え", "PE.Views.DocumentHolder.textShapeAlignMiddle": "上下中央揃え", "PE.Views.DocumentHolder.textShapeAlignRight": "右揃え", "PE.Views.DocumentHolder.textShapeAlignTop": "上揃え", "PE.Views.DocumentHolder.textShapesMerge": "図形を結合", "PE.Views.DocumentHolder.textShowGridlines": "枠線を表示する", "PE.Views.DocumentHolder.textShowGuides": "ガイドを表示", "PE.Views.DocumentHolder.textSlideSettings": "スライド設定", "PE.Views.DocumentHolder.textSmartGuides": "スマートガイド", "PE.Views.DocumentHolder.textSnapObjects": "スナップオブジェクトをグリッドに", "PE.Views.DocumentHolder.textStartAfterPrevious": "前回終了後のスタート", "PE.Views.DocumentHolder.textStartOnClick": "クリック時", "PE.Views.DocumentHolder.textStartWithPrevious": "前回の続きから", "PE.Views.DocumentHolder.textUndo": "元に戻す", "PE.Views.DocumentHolder.tipGuides": "ガイドを表示", "PE.Views.DocumentHolder.tipIsLocked": "今、この要素が他のユーザーによって編集されています。", "PE.Views.DocumentHolder.toDictionaryText": "辞書に追加", "PE.Views.DocumentHolder.txtAddBottom": "下罫線を追加", "PE.Views.DocumentHolder.txtAddFractionBar": "分数線を追加", "PE.Views.DocumentHolder.txtAddHor": "水平線を追加", "PE.Views.DocumentHolder.txtAddLB": "左下罫線を追加", "PE.Views.DocumentHolder.txtAddLeft": "左罫線を追加", "PE.Views.DocumentHolder.txtAddLT": "左上罫線を追加", "PE.Views.DocumentHolder.txtAddRight": "右罫線を追加", "PE.Views.DocumentHolder.txtAddTop": "上罫線を追加", "PE.Views.DocumentHolder.txtAddVer": "縦線を追加", "PE.Views.DocumentHolder.txtAlign": "整列", "PE.Views.DocumentHolder.txtAlignToChar": "文字に合わせる", "PE.Views.DocumentHolder.txtArrange": "順序", "PE.Views.DocumentHolder.txtBackground": "背景", "PE.Views.DocumentHolder.txtBorderProps": "罫線の​​プロパティ", "PE.Views.DocumentHolder.txtBottom": "下", "PE.Views.DocumentHolder.txtChangeLayout": "レイアウトの変更", "PE.Views.DocumentHolder.txtChangeTheme": "テーマの変更", "PE.Views.DocumentHolder.txtColumnAlign": "列の配置", "PE.Views.DocumentHolder.txtDecreaseArg": "引数のサイズの縮小", "PE.Views.DocumentHolder.txtDeleteArg": "引数を削除", "PE.Views.DocumentHolder.txtDeleteBreak": "任意指定の改行を削除", "PE.Views.DocumentHolder.txtDeleteChars": "囲まれた文字の削除", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "囲み文字と区切り文字の削除", "PE.Views.DocumentHolder.txtDeleteEq": "数式を削除", "PE.Views.DocumentHolder.txtDeleteGroupChar": "文字を削除", "PE.Views.DocumentHolder.txtDeleteRadical": "冪根を削除する", "PE.Views.DocumentHolder.txtDeleteSlide": "スライドを削除する", "PE.Views.DocumentHolder.txtDistribHor": "水平に整列する", "PE.Views.DocumentHolder.txtDistribVert": "上下に整列", "PE.Views.DocumentHolder.txtDuplicateSlide": "スライドの複製", "PE.Views.DocumentHolder.txtFractionLinear": "分数(横)に変更", "PE.Views.DocumentHolder.txtFractionSkewed": "分数(斜め)に変更", "PE.Views.DocumentHolder.txtFractionStacked": "分数(縦)に変更\t", "PE.Views.DocumentHolder.txtGroup": "グループ", "PE.Views.DocumentHolder.txtGroupCharOver": "テキストの上の文字", "PE.Views.DocumentHolder.txtGroupCharUnder": "テキスト下の文字", "PE.Views.DocumentHolder.txtHideBottom": "下罫線を表示しない", "PE.Views.DocumentHolder.txtHideBottomLimit": "下極限を表示しない", "PE.Views.DocumentHolder.txtHideCloseBracket": "右括弧を表示しない", "PE.Views.DocumentHolder.txtHideDegree": "次数を表示しない", "PE.Views.DocumentHolder.txtHideHor": "横線を表示しない", "PE.Views.DocumentHolder.txtHideLB": "左(下)の線を表示しない", "PE.Views.DocumentHolder.txtHideLeft": "左罫線を表示しない", "PE.Views.DocumentHolder.txtHideLT": "左(上)の線を表示しない", "PE.Views.DocumentHolder.txtHideOpenBracket": "左括弧を表示しない", "PE.Views.DocumentHolder.txtHidePlaceholder": "プレースホルダを表示しない", "PE.Views.DocumentHolder.txtHideRight": "右罫線を表示しない", "PE.Views.DocumentHolder.txtHideTop": "上罫線を表示しない", "PE.Views.DocumentHolder.txtHideTopLimit": "上極限を表示しない", "PE.Views.DocumentHolder.txtHideVer": "縦線を表示しない", "PE.Views.DocumentHolder.txtIncreaseArg": "引数のサイズの拡大", "PE.Views.DocumentHolder.txtInsAudio": "オーディオの挿入", "PE.Views.DocumentHolder.txtInsChart": "グラフの挿入", "PE.Views.DocumentHolder.txtInsertArgAfter": "の後に引数を挿入", "PE.Views.DocumentHolder.txtInsertArgBefore": "の前に引数を挿入", "PE.Views.DocumentHolder.txtInsertBreak": "手動ブレークを挿入", "PE.Views.DocumentHolder.txtInsertEqAfter": "後に方程式を挿入", "PE.Views.DocumentHolder.txtInsertEqBefore": "前に方程式を挿入", "PE.Views.DocumentHolder.txtInsImage": "画像をファイルから挿入する", "PE.Views.DocumentHolder.txtInsImageUrl": "画像をURLから挿入する", "PE.Views.DocumentHolder.txtInsSmartArt": "SmartArtの挿入", "PE.Views.DocumentHolder.txtInsTable": "テーブルの挿入", "PE.Views.DocumentHolder.txtInsVideo": "ビデオを挿入", "PE.Views.DocumentHolder.txtKeepTextOnly": "テキストのみ保存", "PE.Views.DocumentHolder.txtLimitChange": "制限の位置を変更する", "PE.Views.DocumentHolder.txtLimitOver": "テキストの上に制限する", "PE.Views.DocumentHolder.txtLimitUnder": "テキストの下に制限する", "PE.Views.DocumentHolder.txtMatchBrackets": "括弧を引数の高さに合わせる", "PE.Views.DocumentHolder.txtMatrixAlign": "行列の配置", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "スライドを最後に移動", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "スライドを最初に移動", "PE.Views.DocumentHolder.txtNewSlide": "新しいスライド", "PE.Views.DocumentHolder.txtOverbar": "テキストの上にバー", "PE.Views.DocumentHolder.txtPasteDestFormat": "宛先テーマを使用する", "PE.Views.DocumentHolder.txtPastePicture": "画像", "PE.Views.DocumentHolder.txtPasteSourceFormat": "元の書式付けを保存する", "PE.Views.DocumentHolder.txtPressLink": "{0}キーを押しながらクリックしてリンク先を表示", "PE.Views.DocumentHolder.txtPreview": "スライドショーの開始", "PE.Views.DocumentHolder.txtPrintSelection": "選択範囲の印刷", "PE.Views.DocumentHolder.txtRemFractionBar": "分数線の削除", "PE.Views.DocumentHolder.txtRemLimit": "制限を削除する", "PE.Views.DocumentHolder.txtRemoveAccentChar": "アクセント記号を削除", "PE.Views.DocumentHolder.txtRemoveBar": "上/下線の削除", "PE.Views.DocumentHolder.txtRemScripts": "スクリプトの削除", "PE.Views.DocumentHolder.txtRemSubscript": "下付き文字の削除", "PE.Views.DocumentHolder.txtRemSuperscript": "上付き文字の削除", "PE.Views.DocumentHolder.txtResetLayout": "スライドをリセットする", "PE.Views.DocumentHolder.txtScriptsAfter": "テキストの後のスクリプト", "PE.Views.DocumentHolder.txtScriptsBefore": "テキストの前のスクリプト", "PE.Views.DocumentHolder.txtSelectAll": "すべてを選択", "PE.Views.DocumentHolder.txtShowBottomLimit": "下限を表示する", "PE.Views.DocumentHolder.txtShowCloseBracket": "右大括弧を表示", "PE.Views.DocumentHolder.txtShowDegree": "次数を表示", "PE.Views.DocumentHolder.txtShowOpenBracket": "左大括弧を表示", "PE.Views.DocumentHolder.txtShowPlaceholder": "プレースホルダーの表示", "PE.Views.DocumentHolder.txtShowTopLimit": "上限を表示する", "PE.Views.DocumentHolder.txtSlide": "スライド", "PE.Views.DocumentHolder.txtSlideHide": "スライドを非表示にする", "PE.Views.DocumentHolder.txtStretchBrackets": "括弧の拡大", "PE.Views.DocumentHolder.txtTop": "トップ", "PE.Views.DocumentHolder.txtUnderbar": "テキストの下にバー", "PE.Views.DocumentHolder.txtUngroup": "グループ解除", "PE.Views.DocumentHolder.txtWarnUrl": "このリンクをクリックすると、お使いの端末やデータに悪影響を与える可能性があります。<br>本当に続けてよろしいですか？", "PE.Views.DocumentHolder.unicodeText": "Unicode", "PE.Views.DocumentHolder.vertAlignText": "垂直方向の配置", "PE.Views.DocumentPreview.goToSlideText": "スライドへジャンプ", "PE.Views.DocumentPreview.slideIndexText": "スライド {0}/{1}", "PE.Views.DocumentPreview.txtClose": "スライドショーを閉じる", "PE.Views.DocumentPreview.txtDraw": "Draw", "PE.Views.DocumentPreview.txtEndSlideshow": "スライドショーの終了", "PE.Views.DocumentPreview.txtEraser": "Eraser", "PE.Views.DocumentPreview.txtEraseScreen": "Erase screen", "PE.Views.DocumentPreview.txtExitFullScreen": "全画面表示の終了", "PE.Views.DocumentPreview.txtFinalMessage": "スライドプレビューの終わりです。終了するには、クリックしてください。", "PE.Views.DocumentPreview.txtFullScreen": "全画面表示", "PE.Views.DocumentPreview.txtHighlighter": "Highlighter", "PE.Views.DocumentPreview.txtInkColor": "Ink color", "PE.Views.DocumentPreview.txtNext": "次のスライド", "PE.Views.DocumentPreview.txtPageNumInvalid": "スライド番号が正しくありません。", "PE.Views.DocumentPreview.txtPause": "プレゼンテーションの一時停止", "PE.Views.DocumentPreview.txtPen": "Pen", "PE.Views.DocumentPreview.txtPlay": "プレゼンテーションの開始", "PE.Views.DocumentPreview.txtPrev": "前のスライド", "PE.Views.DocumentPreview.txtReset": "リセット", "PE.Views.FileMenu.ariaFileMenu": "ファイルメニュー", "PE.Views.FileMenu.btnAboutCaption": "詳細情報", "PE.Views.FileMenu.btnBackCaption": "ファイルの場所を開く", "PE.Views.FileMenu.btnCloseEditor": "ファイルを閉じる", "PE.Views.FileMenu.btnCloseMenuCaption": "戻る", "PE.Views.FileMenu.btnCreateNewCaption": "新規作成", "PE.Views.FileMenu.btnDownloadCaption": "名前を付けてダウンロード", "PE.Views.FileMenu.btnExitCaption": "閉じる", "PE.Views.FileMenu.btnFileOpenCaption": "開く", "PE.Views.FileMenu.btnHelpCaption": "ヘルプ", "PE.Views.FileMenu.btnHistoryCaption": "バージョン履歴", "PE.Views.FileMenu.btnInfoCaption": "詳細情報", "PE.Views.FileMenu.btnPrintCaption": "印刷する", "PE.Views.FileMenu.btnProtectCaption": "保護する", "PE.Views.FileMenu.btnRecentFilesCaption": "最近開いた", "PE.Views.FileMenu.btnRenameCaption": "名前を変更", "PE.Views.FileMenu.btnReturnCaption": "プレゼンテーションに戻る", "PE.Views.FileMenu.btnRightsCaption": "アクセス権", "PE.Views.FileMenu.btnSaveAsCaption": "名前を付けて保存", "PE.Views.FileMenu.btnSaveCaption": "保存する", "PE.Views.FileMenu.btnSaveCopyAsCaption": "コピーを別名で保存する", "PE.Views.FileMenu.btnSettingsCaption": "詳細設定", "PE.Views.FileMenu.btnSwitchToMobileCaption": "モバイル版に切り替える", "PE.Views.FileMenu.btnToEditCaption": "プレゼンテーションの編集", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "新しいプレゼンテーション", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "新規作成", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "適用する", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "著者を追加する", "PE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "プロパティの追加", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "テキストを追加", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "アプリケーション", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作成者", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "アクセス許可の変更", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "コメント", "PE.Views.FileMenuPanels.DocumentInfo.txtCommon": "共通", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "作成済み", "PE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "ドキュメントのプロパティ", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "最終更新者", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "最終更新", "PE.Views.FileMenuPanels.DocumentInfo.txtNo": "いいえ", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "所有者", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "位置", "PE.Views.FileMenuPanels.DocumentInfo.txtPresentationInfo": "プレゼンテーション情報", "PE.Views.FileMenuPanels.DocumentInfo.txtProperties": "プロパティ", "PE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "このタイトルのプロパティはすでに存在します", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "権利を有する者", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "件名", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "タグ", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "タイトル", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "アップロード済み", "PE.Views.FileMenuPanels.DocumentInfo.txtYes": "はい", "PE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "アクセス権", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "アクセス許可の変更", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "権利を有する者", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "パスワード付きで", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "プレゼンテーションを保護する", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "署名付きで", "PE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "有効な署名がプレゼンテーションに追加されました。<br>プレゼンテーションは編集から保護されています。", "PE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "<br>目に見えないデジタル署名を追加することで、プレゼンテーションの完全性を確保する。", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "プレゼンテーションの編集", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "編集すると、プレゼンテーションから署名が削除されます。<br>続行しますか？", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "このプレゼンテーションはパスワードで保護されています。", "PE.Views.FileMenuPanels.ProtectDoc.txtProtectPresentation": "このプレゼンテーションをパスワードで暗号化する", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有効な署名がプレゼンテーションに追加されました。 プレゼンテーションは編集から保護されています。", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "プレゼンテーションのデジタル署名の一部が無効であるか、認証できませんでした。 プレゼンテーションは編集から保護されています。", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "署名の表示", "PE.Views.FileMenuPanels.Settings.okButtonText": "適用する", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "共同編集モード", "PE.Views.FileMenuPanels.Settings.strFast": "高速", "PE.Views.FileMenuPanels.Settings.strFontRender": "フォントヒンティング", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "大文字がある言葉を無視する", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "数字のある単語は無視する", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "マクロの設定", "PE.Views.FileMenuPanels.Settings.strPasteButton": "貼り付けるときに[貼り付けオプション]ボタンを表示する", "PE.Views.FileMenuPanels.Settings.strRTLSupport": "RTLインターフェース", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "他のユーザーの変更点を表示する", "PE.Views.FileMenuPanels.Settings.strStrict": "厳格", "PE.Views.FileMenuPanels.Settings.strTabStyle": "タブのスタイル", "PE.Views.FileMenuPanels.Settings.strTheme": "インターフェイスのテーマ", "PE.Views.FileMenuPanels.Settings.strUnit": "測定単位", "PE.Views.FileMenuPanels.Settings.strZoom": "デフォルトのズーム値", "PE.Views.FileMenuPanels.Settings.text10Minutes": "10分毎", "PE.Views.FileMenuPanels.Settings.text30Minutes": "30分毎", "PE.Views.FileMenuPanels.Settings.text5Minutes": "5分毎", "PE.Views.FileMenuPanels.Settings.text60Minutes": "1時間毎", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "配置ガイド", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "自動バックアップ", "PE.Views.FileMenuPanels.Settings.textAutoSave": "オートセーブ", "PE.Views.FileMenuPanels.Settings.textDisabled": "無効", "PE.Views.FileMenuPanels.Settings.textFill": "塗りつぶし", "PE.Views.FileMenuPanels.Settings.textForceSave": "中間バージョンの保存", "PE.Views.FileMenuPanels.Settings.textLine": "線", "PE.Views.FileMenuPanels.Settings.textMinute": "1分毎", "PE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "詳細設定", "PE.Views.FileMenuPanels.Settings.txtAll": "全て表示", "PE.Views.FileMenuPanels.Settings.txtAppearance": "外観", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "オートコレクト設定…", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "デフォルトのキャッシュモード", "PE.Views.FileMenuPanels.Settings.txtCm": "センチ", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "共同編集", "PE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "クイックアクセスのカスタマイズ", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "編集と保存", "PE.Views.FileMenuPanels.Settings.txtFastTip": "リアルタイムの共同編集　すべての変更は自動的に保存されます", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "スライドに合わせる", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "幅に合わせる", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "漢字", "PE.Views.FileMenuPanels.Settings.txtInch": "インチ", "PE.Views.FileMenuPanels.Settings.txtLast": "最後に表示", "PE.Views.FileMenuPanels.Settings.txtLastUsed": "最後に使用した項目", "PE.Views.FileMenuPanels.Settings.txtMac": "OS Xとして", "PE.Views.FileMenuPanels.Settings.txtNative": "ネイティブ", "PE.Views.FileMenuPanels.Settings.txtProofing": "校正", "PE.Views.FileMenuPanels.Settings.txtPt": "ポイント", "PE.Views.FileMenuPanels.Settings.txtQuickPrint": "クイックプリントボタンをエディタヘッダーに表示", "PE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "最後に選択した、またはデフォルトのプリンターで印刷されます。", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "全てを有効にする", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "通知を使用せずにすべてのマクロを有効にする", "PE.Views.FileMenuPanels.Settings.txtScreenReader": "スクリーンリーダーのサポートをオンにする", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "スペルチェック", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "全てを無効にする", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "通知を使用せずにすべてのマクロを無効にする", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "「保存」ボタンを使用して、あなたや他人が行った変更を同期させることができます", "PE.Views.FileMenuPanels.Settings.txtTabBack": "ツールバーの色をタブの背景に使う", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "キーボードでユーザーインターフェイスで移動するには、Altキーを使用します", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "「Option」キーを使用して、キーボードでユーザーインターフェイスで移動します", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "通知を表示する", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "通知を使用してすべてのマクロを無効にする", "PE.Views.FileMenuPanels.Settings.txtWin": "Windowsとして", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "ワークスペース", "PE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "名前を付けてダウンロード", "PE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "コピーを別名で保存する", "PE.Views.GridSettings.textCm": "センチ", "PE.Views.GridSettings.textCustom": "ユーザー設定", "PE.Views.GridSettings.textSpacing": "間隔", "PE.Views.GridSettings.textTitle": "グリッド設定", "PE.Views.HeaderFooterDialog.applyAllText": "全てに適用する", "PE.Views.HeaderFooterDialog.applyText": "適用する", "PE.Views.HeaderFooterDialog.diffLanguage": "スライドマスターとは異なる言語で日付形式を使用することはできません。<br>マスターを変更するには、[適用]ではなく[すべてに適用]をクリックください", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "警告", "PE.Views.HeaderFooterDialog.textDateTime": "日付と時刻", "PE.Views.HeaderFooterDialog.textFixed": "固定", "PE.Views.HeaderFooterDialog.textFormat": "フォーマット", "PE.Views.HeaderFooterDialog.textHFTitle": "ヘッダー/フッター設定", "PE.Views.HeaderFooterDialog.textLang": "言語", "PE.Views.HeaderFooterDialog.textNotes": "ノートと配布資料", "PE.Views.HeaderFooterDialog.textNotTitle": "タイトルスライドに表示しない", "PE.Views.HeaderFooterDialog.textPageNum": "ページ番号", "PE.Views.HeaderFooterDialog.textPreview": "プレビュー", "PE.Views.HeaderFooterDialog.textSlide": "スライド", "PE.Views.HeaderFooterDialog.textSlideNum": "スライド番号", "PE.Views.HeaderFooterDialog.textUpdate": "自動的に更新", "PE.Views.HeaderFooterDialog.txtFooter": "フッター", "PE.Views.HeaderFooterDialog.txtHeader": "ヘッダー", "PE.Views.HyperlinkSettingsDialog.strDisplay": "表示する", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "リンク先", "PE.Views.HyperlinkSettingsDialog.textDefault": "選択されたテキストフラグメント", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "ここでキャプションを挿入してください。", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "ここでリンクを挿入してください。", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "ここでツールチップを挿入してください。", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "外部リンク", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "このプレゼンテーションのスライド", "PE.Views.HyperlinkSettingsDialog.textSelectFile": "ファイル選択", "PE.Views.HyperlinkSettingsDialog.textSlides": "スライド", "PE.Views.HyperlinkSettingsDialog.textTipText": "ヒントのテキスト:", "PE.Views.HyperlinkSettingsDialog.textTitle": "ハイパーリンクの設定", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "このフィールドは必須項目です", "PE.Views.HyperlinkSettingsDialog.txtFirst": "最初のスライド", "PE.Views.HyperlinkSettingsDialog.txtLast": "最後のスライド", "PE.Views.HyperlinkSettingsDialog.txtNext": "次のスライド", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "リンクの入力内容は「http://www.example.com」形式のURLである必要があります。", "PE.Views.HyperlinkSettingsDialog.txtPrev": "前のスライド", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "このフィールドは最大2083文字に制限されています", "PE.Views.HyperlinkSettingsDialog.txtSlide": "スライド", "PE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "ウェブアドレスを入力するか、ファイルを選択してください", "PE.Views.ImageSettings.strTransparency": "不透明度", "PE.Views.ImageSettings.textAdvanced": "詳細設定の表示", "PE.Views.ImageSettings.textCrop": "トリミング", "PE.Views.ImageSettings.textCropFill": "塗りつぶし", "PE.Views.ImageSettings.textCropFit": "合わせる", "PE.Views.ImageSettings.textCropToShape": "図形に合わせてトリミング", "PE.Views.ImageSettings.textEdit": "編集する", "PE.Views.ImageSettings.textEditObject": "オブジェクトを編集する", "PE.Views.ImageSettings.textFitSlide": "スライドに合わせる", "PE.Views.ImageSettings.textFlip": "反転する", "PE.Views.ImageSettings.textFromFile": "ファイルから", "PE.Views.ImageSettings.textFromStorage": "ストレージから", "PE.Views.ImageSettings.textFromUrl": "URLから", "PE.Views.ImageSettings.textHeight": "高さ", "PE.Views.ImageSettings.textHint270": "反時計回りに90度回転", "PE.Views.ImageSettings.textHint90": "時計回りに90度回転", "PE.Views.ImageSettings.textHintFlipH": "左右に反転", "PE.Views.ImageSettings.textHintFlipV": "上下に反転", "PE.Views.ImageSettings.textInsert": "画像を置き換える", "PE.Views.ImageSettings.textOriginalSize": "実際のサイズ", "PE.Views.ImageSettings.textRecentlyUsed": "最近使った項目", "PE.Views.ImageSettings.textResetCrop": "トリミングをリセット", "PE.Views.ImageSettings.textRotate90": "90度回転", "PE.Views.ImageSettings.textRotation": "回転", "PE.Views.ImageSettings.textSize": "サイズ", "PE.Views.ImageSettings.textWidth": "幅", "PE.Views.ImageSettingsAdvanced.textAlt": "代替テキスト", "PE.Views.ImageSettingsAdvanced.textAltDescription": "説明", "PE.Views.ImageSettingsAdvanced.textAltTip": "視覚障害や認知障害のある人が、画像や図形、図表にどのような情報が含まれているかを理解しやすくするため、そのオブジェクトについて目視できる情報を文章で表現したものです。", "PE.Views.ImageSettingsAdvanced.textAltTitle": "タイトル", "PE.Views.ImageSettingsAdvanced.textAngle": "角度", "PE.Views.ImageSettingsAdvanced.textCenter": "中央揃え", "PE.Views.ImageSettingsAdvanced.textFlipped": "反転", "PE.Views.ImageSettingsAdvanced.textFrom": "基準", "PE.Views.ImageSettingsAdvanced.textGeneral": "一般", "PE.Views.ImageSettingsAdvanced.textHeight": "高さ", "PE.Views.ImageSettingsAdvanced.textHorizontal": "水平", "PE.Views.ImageSettingsAdvanced.textHorizontally": "水平に", "PE.Views.ImageSettingsAdvanced.textImageName": "画像名", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "一定の比率", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "実際のサイズ", "PE.Views.ImageSettingsAdvanced.textPlacement": "位置", "PE.Views.ImageSettingsAdvanced.textPosition": "位置", "PE.Views.ImageSettingsAdvanced.textRotation": "回転", "PE.Views.ImageSettingsAdvanced.textSize": "サイズ", "PE.Views.ImageSettingsAdvanced.textTitle": "画像の詳細設定", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "左上隅", "PE.Views.ImageSettingsAdvanced.textVertical": "縦", "PE.Views.ImageSettingsAdvanced.textVertically": "縦に", "PE.Views.ImageSettingsAdvanced.textWidth": "幅", "PE.Views.LeftMenu.ariaLeftMenu": "左メニュー", "PE.Views.LeftMenu.tipAbout": "詳細情報", "PE.Views.LeftMenu.tipChat": "チャット", "PE.Views.LeftMenu.tipComments": "コメント", "PE.Views.LeftMenu.tipPlugins": "プラグイン", "PE.Views.LeftMenu.tipSearch": "検索", "PE.Views.LeftMenu.tipSlides": "スライド", "PE.Views.LeftMenu.tipSupport": "フィードバック＆サポート", "PE.Views.LeftMenu.tipTitles": "タイトル", "PE.Views.LeftMenu.txtDeveloper": "開発者モード", "PE.Views.LeftMenu.txtEditor": "プレゼンテーションエディター", "PE.Views.LeftMenu.txtLimit": "制限されたアクセス", "PE.Views.LeftMenu.txtTrial": "試用モード", "PE.Views.LeftMenu.txtTrialDev": "試用開発者モード", "PE.Views.ParagraphSettings.strLineHeight": "行間", "PE.Views.ParagraphSettings.strParagraphSpacing": "段落の間隔", "PE.Views.ParagraphSettings.strSpacingAfter": "後", "PE.Views.ParagraphSettings.strSpacingBefore": "前", "PE.Views.ParagraphSettings.textAdvanced": "詳細設定の表示", "PE.Views.ParagraphSettings.textAt": "に", "PE.Views.ParagraphSettings.textAtLeast": "最小限", "PE.Views.ParagraphSettings.textAuto": "倍数", "PE.Views.ParagraphSettings.textExact": "固定値", "PE.Views.ParagraphSettings.txtAutoText": "オート", "PE.Views.ParagraphSettingsAdvanced.noTabs": "指定されたタブは、このフィールドに表示されます。", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "すべて大文字", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "二重取り消し線", "PE.Views.ParagraphSettingsAdvanced.strIndent": "インデント", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行間", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "右", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "後", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "前", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特殊", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "フォント", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "インデント＆行間隔", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小型英大文字\t", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "間隔", "PE.Views.ParagraphSettingsAdvanced.strStrike": "取り消し線", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "下付き文字", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "上付き文字", "PE.Views.ParagraphSettingsAdvanced.strTabs": "タブ", "PE.Views.ParagraphSettingsAdvanced.textAlign": "配置", "PE.Views.ParagraphSettingsAdvanced.textAuto": "倍数", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "文字間隔", "PE.Views.ParagraphSettingsAdvanced.textDefault": "既定のタブ", "PE.Views.ParagraphSettingsAdvanced.textEffects": "エフェクト", "PE.Views.ParagraphSettingsAdvanced.textExact": "固定値", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "最初の行", "PE.Views.ParagraphSettingsAdvanced.textHanging": "ぶら下げ", "PE.Views.ParagraphSettingsAdvanced.textJustified": "両端揃え", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "（なし）", "PE.Views.ParagraphSettingsAdvanced.textRemove": "削除する", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "全てを削除", "PE.Views.ParagraphSettingsAdvanced.textSet": "指定", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "中央揃え", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "左", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "タブの位置", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "右", "PE.Views.ParagraphSettingsAdvanced.textTitle": "段落 - 詳細設定", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "オート", "PE.Views.PrintWithPreview.txtAllPages": "全てのスライド", "PE.Views.PrintWithPreview.txtBothSides": "両面印刷", "PE.Views.PrintWithPreview.txtBothSidesLongDesc": "長辺を綴じる", "PE.Views.PrintWithPreview.txtBothSidesShortDesc": "短辺を綴じる", "PE.Views.PrintWithPreview.txtCopies": "コピー", "PE.Views.PrintWithPreview.txtCurrentPage": "現在のスライド", "PE.Views.PrintWithPreview.txtCustomPages": "カスタム印刷", "PE.Views.PrintWithPreview.txtEmptyTable": "プレゼンテーションが空白のため、印刷できるスライドがありません。", "PE.Views.PrintWithPreview.txtHeaderFooterSettings": "ヘッダー/フッター設定", "PE.Views.PrintWithPreview.txtOf": "{0}から", "PE.Views.PrintWithPreview.txtOneSide": "片面印刷", "PE.Views.PrintWithPreview.txtOneSideDesc": "ページの片面のみを印刷する", "PE.Views.PrintWithPreview.txtPage": "スライド", "PE.Views.PrintWithPreview.txtPageNumInvalid": "スライド番号無効", "PE.Views.PrintWithPreview.txtPages": "スライド", "PE.Views.PrintWithPreview.txtPaperSize": "用紙サイズ", "PE.Views.PrintWithPreview.txtPrint": "印刷", "PE.Views.PrintWithPreview.txtPrintPdf": "PDFに印刷", "PE.Views.PrintWithPreview.txtPrintRange": "印刷範囲\t", "PE.Views.PrintWithPreview.txtPrintSides": "両面印刷", "PE.Views.RightMenu.ariaRightMenu": "右メニュー", "PE.Views.RightMenu.txtChartSettings": "グラフの設定", "PE.Views.RightMenu.txtImageSettings": "画像の設定", "PE.Views.RightMenu.txtParagraphSettings": "段落の設定", "PE.Views.RightMenu.txtShapeSettings": "図形の設定", "PE.Views.RightMenu.txtSignatureSettings": "署名の設定", "PE.Views.RightMenu.txtSlideSettings": "スライド設定", "PE.Views.RightMenu.txtTableSettings": "表の設定", "PE.Views.RightMenu.txtTextArtSettings": "テキストアートの設定", "PE.Views.ShapeSettings.strBackground": "背景色", "PE.Views.ShapeSettings.strChange": "図形の変更", "PE.Views.ShapeSettings.strColor": "色", "PE.Views.ShapeSettings.strFill": "塗りつぶし", "PE.Views.ShapeSettings.strForeground": "前景色", "PE.Views.ShapeSettings.strPattern": "パターン", "PE.Views.ShapeSettings.strShadow": "影を表示する", "PE.Views.ShapeSettings.strSize": "サイズ", "PE.Views.ShapeSettings.strStroke": "線", "PE.Views.ShapeSettings.strTransparency": "不透明度", "PE.Views.ShapeSettings.strType": "タイプ", "PE.Views.ShapeSettings.textAdjustShadow": "影の調整", "PE.Views.ShapeSettings.textAdvanced": "詳細設定の表示", "PE.Views.ShapeSettings.textAngle": "角度", "PE.Views.ShapeSettings.textBorderSizeErr": "入力された値が正しくありません。<br>0〜1584の数値を入力してください。", "PE.Views.ShapeSettings.textColor": "色で塗りつぶし", "PE.Views.ShapeSettings.textDirection": "方向", "PE.Views.ShapeSettings.textEditPoints": "頂点の編集", "PE.Views.ShapeSettings.textEditShape": "図形の編集", "PE.Views.ShapeSettings.textEmptyPattern": "パターンなし", "PE.Views.ShapeSettings.textEyedropper": "スポイト", "PE.Views.ShapeSettings.textFlip": "反転する", "PE.Views.ShapeSettings.textFromFile": "ファイルから", "PE.Views.ShapeSettings.textFromStorage": "ストレージから", "PE.Views.ShapeSettings.textFromUrl": "URLから", "PE.Views.ShapeSettings.textGradient": "グラデーションポイント", "PE.Views.ShapeSettings.textGradientFill": "塗りつぶし(グラデーション)", "PE.Views.ShapeSettings.textHint270": "反時計回りに90度回転", "PE.Views.ShapeSettings.textHint90": "時計回りに90度回転", "PE.Views.ShapeSettings.textHintFlipH": "左右に反転", "PE.Views.ShapeSettings.textHintFlipV": "上下に反転", "PE.Views.ShapeSettings.textImageTexture": "画像またはテクスチャ", "PE.Views.ShapeSettings.textLinear": "線形", "PE.Views.ShapeSettings.textMoreColors": "その他の色", "PE.Views.ShapeSettings.textNoFill": "塗りつぶしなし", "PE.Views.ShapeSettings.textNoShadow": "影なし", "PE.Views.ShapeSettings.textPatternFill": "パターン", "PE.Views.ShapeSettings.textPosition": "位置", "PE.Views.ShapeSettings.textRadial": "ラジアル", "PE.Views.ShapeSettings.textRecentlyUsed": "最近使った項目", "PE.Views.ShapeSettings.textRotate90": "90度回転", "PE.Views.ShapeSettings.textRotation": "回転", "PE.Views.ShapeSettings.textSelectImage": "画像の選択", "PE.Views.ShapeSettings.textSelectTexture": "選択", "PE.Views.ShapeSettings.textShadow": "影", "PE.Views.ShapeSettings.textStretch": "ストレッチ", "PE.Views.ShapeSettings.textStyle": "スタイル", "PE.Views.ShapeSettings.textTexture": "テクスチャから", "PE.Views.ShapeSettings.textTile": "タイル", "PE.Views.ShapeSettings.tipAddGradientPoint": "グラデーションポイントを追加", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "グラデーションポイントを削除する", "PE.Views.ShapeSettings.txtBrownPaper": "クラフト紙", "PE.Views.ShapeSettings.txtCanvas": "キャンバス", "PE.Views.ShapeSettings.txtCarton": "カートン", "PE.Views.ShapeSettings.txtDarkFabric": "ダークファブリック", "PE.Views.ShapeSettings.txtGrain": "粒子", "PE.Views.ShapeSettings.txtGranite": "花崗岩", "PE.Views.ShapeSettings.txtGreyPaper": "グレー紙", "PE.Views.ShapeSettings.txtKnit": "ニット", "PE.Views.ShapeSettings.txtLeather": "レザー", "PE.Views.ShapeSettings.txtNoBorders": "線なし", "PE.Views.ShapeSettings.txtPapyrus": "パピルス", "PE.Views.ShapeSettings.txtWood": "木", "PE.Views.ShapeSettingsAdvanced.strColumns": "列", "PE.Views.ShapeSettingsAdvanced.strMargins": "テキストの埋め込み文字", "PE.Views.ShapeSettingsAdvanced.textAlt": "代替テキスト", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "説明", "PE.Views.ShapeSettingsAdvanced.textAltTip": "視覚障害や認知障害のある人が、画像や図形、図表にどのような情報が含まれているかを理解しやすくするため、そのオブジェクトについて目視できる情報を文章で表現したものです。", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "タイトル", "PE.Views.ShapeSettingsAdvanced.textAngle": "角度", "PE.Views.ShapeSettingsAdvanced.textArrows": "矢印", "PE.Views.ShapeSettingsAdvanced.textAutofit": "自動調整", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "始点のサイズ", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "始点のスタイル", "PE.Views.ShapeSettingsAdvanced.textBevel": "斜角", "PE.Views.ShapeSettingsAdvanced.textBottom": "下", "PE.Views.ShapeSettingsAdvanced.textCapType": "線の先端", "PE.Views.ShapeSettingsAdvanced.textCenter": "中央揃え", "PE.Views.ShapeSettingsAdvanced.textColNumber": "列数", "PE.Views.ShapeSettingsAdvanced.textEndSize": "終点のサイズ", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "終点のスタイル", "PE.Views.ShapeSettingsAdvanced.textFlat": "フラット", "PE.Views.ShapeSettingsAdvanced.textFlipped": "反転", "PE.Views.ShapeSettingsAdvanced.textFrom": "基準", "PE.Views.ShapeSettingsAdvanced.textGeneral": "一般", "PE.Views.ShapeSettingsAdvanced.textHeight": "高さ", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "水平", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "水平に", "PE.Views.ShapeSettingsAdvanced.textJoinType": "結合の種類", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "一定の比率", "PE.Views.ShapeSettingsAdvanced.textLeft": "左", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "線のスタイル", "PE.Views.ShapeSettingsAdvanced.textMiter": "角", "PE.Views.ShapeSettingsAdvanced.textNofit": "自動調整なし", "PE.Views.ShapeSettingsAdvanced.textPlacement": "位置", "PE.Views.ShapeSettingsAdvanced.textPosition": "位置", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "テキストに合わせて図形を調整", "PE.Views.ShapeSettingsAdvanced.textRight": "右", "PE.Views.ShapeSettingsAdvanced.textRotation": "回転", "PE.Views.ShapeSettingsAdvanced.textRound": "円い", "PE.Views.ShapeSettingsAdvanced.textShapeName": "図形名", "PE.Views.ShapeSettingsAdvanced.textShrink": "はみ出す場合だけ自動調整する", "PE.Views.ShapeSettingsAdvanced.textSize": "サイズ", "PE.Views.ShapeSettingsAdvanced.textSpacing": "列の間隔", "PE.Views.ShapeSettingsAdvanced.textSquare": "四角", "PE.Views.ShapeSettingsAdvanced.textTextBox": "テキストボックス", "PE.Views.ShapeSettingsAdvanced.textTitle": "図形 - 詳細設定", "PE.Views.ShapeSettingsAdvanced.textTop": "トップ", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "左上隅", "PE.Views.ShapeSettingsAdvanced.textVertical": "縦", "PE.Views.ShapeSettingsAdvanced.textVertically": "縦に", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "太さ&矢印", "PE.Views.ShapeSettingsAdvanced.textWidth": "幅", "PE.Views.ShapeSettingsAdvanced.txtNone": "なし", "PE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "PE.Views.SignatureSettings.strDelete": "署名の削除", "PE.Views.SignatureSettings.strDetails": "署名の詳細", "PE.Views.SignatureSettings.strInvalid": "無効な署名", "PE.Views.SignatureSettings.strSign": "署名する", "PE.Views.SignatureSettings.strSignature": "署名", "PE.Views.SignatureSettings.strValid": "有効な署名", "PE.Views.SignatureSettings.txtContinueEditing": "無視して編集する", "PE.Views.SignatureSettings.txtEditWarning": "編集すると、プレゼンテーションから署名が削除されます。<br>続行しますか？", "PE.Views.SignatureSettings.txtRemoveWarning": "この署名を削除しますか？<br>この操作は元に戻せません。", "PE.Views.SignatureSettings.txtSigned": "有効な署名がプレゼンテーションに追加されました。 プレゼンテーションは編集から保護されています。", "PE.Views.SignatureSettings.txtSignedInvalid": "プレゼンテーションのデジタル署名の一部が無効であるか、認証できませんでした。 プレゼンテーションは編集から保護されています。", "PE.Views.SlideSettings.strApplyAllSlides": "全てのスライドに適用する", "PE.Views.SlideSettings.strBackground": "背景色", "PE.Views.SlideSettings.strBackgroundGraphics": "背景グラフィックを表示する", "PE.Views.SlideSettings.strBackgroundReset": "背景をリセットする", "PE.Views.SlideSettings.strColor": "色", "PE.Views.SlideSettings.strDateTime": "日付と時刻を表示", "PE.Views.SlideSettings.strFill": "背景", "PE.Views.SlideSettings.strForeground": "前景色", "PE.Views.SlideSettings.strPattern": "パターン", "PE.Views.SlideSettings.strSlideNum": "スライド番号を表示", "PE.Views.SlideSettings.strTransparency": "不透明度", "PE.Views.SlideSettings.textAdvanced": "詳細設定の表示", "PE.Views.SlideSettings.textAngle": "角度", "PE.Views.SlideSettings.textColor": "色で塗りつぶし", "PE.Views.SlideSettings.textDirection": "方向", "PE.Views.SlideSettings.textEmptyPattern": "パターンなし", "PE.Views.SlideSettings.textFromFile": "ファイルから", "PE.Views.SlideSettings.textFromStorage": "ストレージから", "PE.Views.SlideSettings.textFromUrl": "URLから", "PE.Views.SlideSettings.textGradient": "グラデーションポイント", "PE.Views.SlideSettings.textGradientFill": "塗りつぶし(グラデーション)", "PE.Views.SlideSettings.textImageTexture": "画像またはテクスチャ", "PE.Views.SlideSettings.textLinear": "線形", "PE.Views.SlideSettings.textNoFill": "塗りつぶしなし", "PE.Views.SlideSettings.textPatternFill": "パターン", "PE.Views.SlideSettings.textPosition": "位置", "PE.Views.SlideSettings.textRadial": "ラジアル", "PE.Views.SlideSettings.textReset": "変更をリセットします", "PE.Views.SlideSettings.textSelectImage": "画像の選択", "PE.Views.SlideSettings.textSelectTexture": "選択", "PE.Views.SlideSettings.textStretch": "ストレッチ", "PE.Views.SlideSettings.textStyle": "スタイル", "PE.Views.SlideSettings.textTexture": "テクスチャから", "PE.Views.SlideSettings.textTile": "タイル", "PE.Views.SlideSettings.tipAddGradientPoint": "グラデーションポイントを追加", "PE.Views.SlideSettings.tipRemoveGradientPoint": "グラデーションポイントを削除する", "PE.Views.SlideSettings.txtBrownPaper": "クラフト紙", "PE.Views.SlideSettings.txtCanvas": "キャンバス", "PE.Views.SlideSettings.txtCarton": "カートン", "PE.Views.SlideSettings.txtDarkFabric": "ダークファブリック", "PE.Views.SlideSettings.txtGrain": "粒子", "PE.Views.SlideSettings.txtGranite": "花崗岩", "PE.Views.SlideSettings.txtGreyPaper": "グレー紙", "PE.Views.SlideSettings.txtKnit": "ニット", "PE.Views.SlideSettings.txtLeather": "レザー", "PE.Views.SlideSettings.txtPapyrus": "パピルス", "PE.Views.SlideSettings.txtWood": "木", "PE.Views.SlideshowSettings.textLoop": "Escキーが押されるまで繰り返す", "PE.Views.SlideshowSettings.textTitle": "設定を表示", "PE.Views.SlideSizeSettings.strLandscape": "横向き", "PE.Views.SlideSizeSettings.strPortrait": "縦向き", "PE.Views.SlideSizeSettings.textHeight": "高さ", "PE.Views.SlideSizeSettings.textSlideOrientation": "スライドの向き", "PE.Views.SlideSizeSettings.textSlideSize": "スライドのサイズ", "PE.Views.SlideSizeSettings.textTitle": "スライドのサイズを設定", "PE.Views.SlideSizeSettings.textWidth": "幅", "PE.Views.SlideSizeSettings.txt35": "35mmのスライド", "PE.Views.SlideSizeSettings.txtA3": "A3 297x420 mm", "PE.Views.SlideSizeSettings.txtA4": "A4 210 x 297 mm", "PE.Views.SlideSizeSettings.txtB4": "B4(ICO)(250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5(ICO)(176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "バナー", "PE.Views.SlideSizeSettings.txtCustom": "カスタム", "PE.Views.SlideSizeSettings.txtLedger": "帳簿用紙（11x17インチ）", "PE.Views.SlideSizeSettings.txtLetter": "便箋 (8.5x11インチ)", "PE.Views.SlideSizeSettings.txtOverhead": "オーバーヘッド", "PE.Views.SlideSizeSettings.txtSlideNum": "スライド番号", "PE.Views.SlideSizeSettings.txtStandard": "標準(4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "ワイドスクリーン", "PE.Views.Statusbar.goToPageText": "スライドへジャンプ", "PE.Views.Statusbar.pageIndexText": "スライド {0}/{1}", "PE.Views.Statusbar.textShowBegin": "先頭から表示する", "PE.Views.Statusbar.textShowCurrent": "現在のスライドからの表示", "PE.Views.Statusbar.textShowPresenterView": "発表者ビューを表示", "PE.Views.Statusbar.textSlideMaster": "スライドマスター", "PE.Views.Statusbar.tipAccessRights": "文書のアクセス許可の管理", "PE.Views.Statusbar.tipFitPage": "スライドに合わせる", "PE.Views.Statusbar.tipFitWidth": "幅に合わせる", "PE.Views.Statusbar.tipPreview": "スライドショーの開始", "PE.Views.Statusbar.tipSetLang": "テキストの言語を設定", "PE.Views.Statusbar.tipZoomFactor": "ズーム", "PE.Views.Statusbar.tipZoomIn": "ズームイン", "PE.Views.Statusbar.tipZoomOut": "ズームアウト", "PE.Views.Statusbar.txtPageNumInvalid": "スライド番号が正しくありません。", "PE.Views.TableSettings.deleteColumnText": "列を削除", "PE.Views.TableSettings.deleteRowText": "行を削除", "PE.Views.TableSettings.deleteTableText": "表を削除する", "PE.Views.TableSettings.insertColumnLeftText": "左に列を挿入", "PE.Views.TableSettings.insertColumnRightText": "右に列を挿入", "PE.Views.TableSettings.insertRowAboveText": "上に行を挿入", "PE.Views.TableSettings.insertRowBelowText": "下に行を挿入", "PE.Views.TableSettings.mergeCellsText": "セルの結合", "PE.Views.TableSettings.selectCellText": "セルの選択", "PE.Views.TableSettings.selectColumnText": "列の選択", "PE.Views.TableSettings.selectRowText": "行の選択", "PE.Views.TableSettings.selectTableText": "テーブルの選択", "PE.Views.TableSettings.splitCellsText": "セルを分割...", "PE.Views.TableSettings.splitCellTitleText": "セルの分割", "PE.Views.TableSettings.textAdvanced": "詳細設定の表示", "PE.Views.TableSettings.textBackColor": "背景色", "PE.Views.TableSettings.textBanded": "縞模様", "PE.Views.TableSettings.textBorderColor": "色", "PE.Views.TableSettings.textBorders": "罫線のスタイル", "PE.Views.TableSettings.textCellSize": "セルのサイズ", "PE.Views.TableSettings.textColumns": "列", "PE.Views.TableSettings.textDistributeCols": "列の幅を揃える", "PE.Views.TableSettings.textDistributeRows": "行の高さを揃える", "PE.Views.TableSettings.textEdit": "行/列", "PE.Views.TableSettings.textEmptyTemplate": "テンプレートなし", "PE.Views.TableSettings.textFirst": "最初の", "PE.Views.TableSettings.textHeader": "ヘッダー", "PE.Views.TableSettings.textHeight": "高さ", "PE.Views.TableSettings.textLast": "最後", "PE.Views.TableSettings.textRows": "行", "PE.Views.TableSettings.textSelectBorders": "選択したスタイルを適用する罫線を選択してください。 ", "PE.Views.TableSettings.textTemplate": "テンプレートから選択する", "PE.Views.TableSettings.textTotal": "合計", "PE.Views.TableSettings.textWidth": "幅", "PE.Views.TableSettings.tipAll": "外枠とすべての内枠の線を設定", "PE.Views.TableSettings.tipBottom": "外部の罫線（下）だけを設定", "PE.Views.TableSettings.tipInner": "内部の線だけを設定", "PE.Views.TableSettings.tipInnerHor": "横線内部の線だけを設定", "PE.Views.TableSettings.tipInnerVert": "縦方向の内線のみを設定", "PE.Views.TableSettings.tipLeft": "外部の罫線（左）だけを設定", "PE.Views.TableSettings.tipNone": "罫線の設定なし", "PE.Views.TableSettings.tipOuter": "外枠の罫線だけを設定", "PE.Views.TableSettings.tipRight": "外部の罫線（右）だけを設定", "PE.Views.TableSettings.tipTop": "外部の罫線（上）だけを設定", "PE.Views.TableSettings.txtGroupTable_Custom": "ユーザー設定", "PE.Views.TableSettings.txtGroupTable_Dark": "ダーク", "PE.Views.TableSettings.txtGroupTable_Light": "ライト", "PE.Views.TableSettings.txtGroupTable_Medium": "中", "PE.Views.TableSettings.txtGroupTable_Optimal": "ドキュメントに最適なスタイル", "PE.Views.TableSettings.txtNoBorders": "枠線なし", "PE.Views.TableSettings.txtTable_Accent": "アクセント", "PE.Views.TableSettings.txtTable_DarkStyle": "ダークスタイル", "PE.Views.TableSettings.txtTable_LightStyle": "ライトスタイル", "PE.Views.TableSettings.txtTable_MediumStyle": "ミディアムスタイル", "PE.Views.TableSettings.txtTable_NoGrid": "枠線なし", "PE.Views.TableSettings.txtTable_NoStyle": "スタイルなし", "PE.Views.TableSettings.txtTable_TableGrid": "テーブルの枠線", "PE.Views.TableSettings.txtTable_ThemedStyle": "テーマのスタイル", "PE.Views.TableSettingsAdvanced.textAlt": "代替テキスト", "PE.Views.TableSettingsAdvanced.textAltDescription": "説明", "PE.Views.TableSettingsAdvanced.textAltTip": "視覚障害や認知障害のある人が、画像や図形、図表にどのような情報が含まれているかを理解しやすくするため、そのオブジェクトについて目視できる情報を文章で表現したものです。", "PE.Views.TableSettingsAdvanced.textAltTitle": "タイトル", "PE.Views.TableSettingsAdvanced.textBottom": "下", "PE.Views.TableSettingsAdvanced.textCenter": "中央揃え", "PE.Views.TableSettingsAdvanced.textCheckMargins": "既定の余白を使用", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "既定の余白", "PE.Views.TableSettingsAdvanced.textFrom": "基準", "PE.Views.TableSettingsAdvanced.textGeneral": "一般", "PE.Views.TableSettingsAdvanced.textHeight": "高さ", "PE.Views.TableSettingsAdvanced.textHorizontal": "水平", "PE.Views.TableSettingsAdvanced.textKeepRatio": "一定の比率", "PE.Views.TableSettingsAdvanced.textLeft": "左", "PE.Views.TableSettingsAdvanced.textMargins": "セルの余白", "PE.Views.TableSettingsAdvanced.textPlacement": "位置", "PE.Views.TableSettingsAdvanced.textPosition": "位置", "PE.Views.TableSettingsAdvanced.textRight": "右", "PE.Views.TableSettingsAdvanced.textSize": "サイズ", "PE.Views.TableSettingsAdvanced.textTableName": "表の名前", "PE.Views.TableSettingsAdvanced.textTitle": "表 - 詳細設定", "PE.Views.TableSettingsAdvanced.textTop": "トップ", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "左上隅", "PE.Views.TableSettingsAdvanced.textVertical": "縦", "PE.Views.TableSettingsAdvanced.textWidth": "幅", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "余白", "PE.Views.TextArtSettings.strBackground": "背景色", "PE.Views.TextArtSettings.strColor": "色", "PE.Views.TextArtSettings.strFill": "塗りつぶし", "PE.Views.TextArtSettings.strForeground": "前景色", "PE.Views.TextArtSettings.strPattern": "パターン", "PE.Views.TextArtSettings.strSize": "サイズ", "PE.Views.TextArtSettings.strStroke": "線", "PE.Views.TextArtSettings.strTransparency": "不透明度", "PE.Views.TextArtSettings.strType": "タイプ", "PE.Views.TextArtSettings.textAngle": "角度", "PE.Views.TextArtSettings.textBorderSizeErr": "入力された値が正しくありません。<br>0〜1584の数値を入力してください。", "PE.Views.TextArtSettings.textColor": "色で塗りつぶし", "PE.Views.TextArtSettings.textDirection": "方向", "PE.Views.TextArtSettings.textEmptyPattern": "パターンなし", "PE.Views.TextArtSettings.textFromFile": "ファイルから", "PE.Views.TextArtSettings.textFromUrl": "URLから", "PE.Views.TextArtSettings.textGradient": "グラデーションポイント", "PE.Views.TextArtSettings.textGradientFill": "塗りつぶし(グラデーション)", "PE.Views.TextArtSettings.textImageTexture": "画像またはテクスチャ", "PE.Views.TextArtSettings.textLinear": "線形", "PE.Views.TextArtSettings.textNoFill": "塗りつぶしなし", "PE.Views.TextArtSettings.textPatternFill": "パターン", "PE.Views.TextArtSettings.textPosition": "位置", "PE.Views.TextArtSettings.textRadial": "ラジアル", "PE.Views.TextArtSettings.textSelectTexture": "選択", "PE.Views.TextArtSettings.textStretch": "ストレッチ", "PE.Views.TextArtSettings.textStyle": "スタイル", "PE.Views.TextArtSettings.textTemplate": "テンプレート", "PE.Views.TextArtSettings.textTexture": "テクスチャから", "PE.Views.TextArtSettings.textTile": "タイル", "PE.Views.TextArtSettings.textTransform": "変換", "PE.Views.TextArtSettings.tipAddGradientPoint": "グラデーションポイントを追加", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "グラデーションポイントを削除する", "PE.Views.TextArtSettings.txtBrownPaper": "クラフト紙", "PE.Views.TextArtSettings.txtCanvas": "キャンバス", "PE.Views.TextArtSettings.txtCarton": "カートン", "PE.Views.TextArtSettings.txtDarkFabric": "ダークファブリック", "PE.Views.TextArtSettings.txtGrain": "粒子", "PE.Views.TextArtSettings.txtGranite": "花崗岩", "PE.Views.TextArtSettings.txtGreyPaper": "グレー紙", "PE.Views.TextArtSettings.txtKnit": "ニット", "PE.Views.TextArtSettings.txtLeather": "レザー", "PE.Views.TextArtSettings.txtNoBorders": "線なし", "PE.Views.TextArtSettings.txtPapyrus": "パピルス", "PE.Views.TextArtSettings.txtWood": "木", "PE.Views.Toolbar.capAddLayout": "レイアウトの追加", "PE.Views.Toolbar.capAddSlide": "スライドの追加", "PE.Views.Toolbar.capAddSlideMaster": "スライドマスターの追加", "PE.Views.Toolbar.capBtnAddComment": "コメントを追加", "PE.Views.Toolbar.capBtnComment": "コメント", "PE.Views.Toolbar.capBtnDateTime": "日付と時刻", "PE.Views.Toolbar.capBtnInsHeaderFooter": "ヘッダー/フッター", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "記号", "PE.Views.Toolbar.capBtnSlideNum": "スライド番号", "PE.Views.Toolbar.capCloseMaster": "テンプレートを閉じる", "PE.Views.Toolbar.capInsertAudio": "オーディオ", "PE.Views.Toolbar.capInsertChart": "グラフ", "PE.Views.Toolbar.capInsertEquation": "方程式\t", "PE.Views.Toolbar.capInsertHyperlink": "ハイパーリンク", "PE.Views.Toolbar.capInsertImage": "画像", "PE.Views.Toolbar.capInsertPlaceholder": "プレースホルダーの挿入", "PE.Views.Toolbar.capInsertShape": "図形", "PE.Views.Toolbar.capInsertTable": "表", "PE.Views.Toolbar.capInsertText": "テキストボックス", "PE.Views.Toolbar.capInsertTextArt": "テキストアート", "PE.Views.Toolbar.capInsertVideo": "ビデオ", "PE.Views.Toolbar.capTabFile": "ファイル", "PE.Views.Toolbar.capTabHome": "ホーム", "PE.Views.Toolbar.capTabInsert": "挿入", "PE.Views.Toolbar.mniCapitalizeWords": "各単語を大文字にする", "PE.Views.Toolbar.mniCustomTable": "カスタムテーブルの挿入", "PE.Views.Toolbar.mniImageFromFile": "ファイルから画像", "PE.Views.Toolbar.mniImageFromStorage": "ストレージから画像", "PE.Views.Toolbar.mniImageFromUrl": "URLから画像", "PE.Views.Toolbar.mniInsertSSE": "スプレッドシートを挿入", "PE.Views.Toolbar.mniLowerCase": "小文字", "PE.Views.Toolbar.mniSentenceCase": "センテンスケース", "PE.Views.Toolbar.mniSlideAdvanced": "詳細設定", "PE.Views.Toolbar.mniSlideStandard": "標準(4:3)", "PE.Views.Toolbar.mniSlideWide": "ワイド画面(16:9)", "PE.Views.Toolbar.mniToggleCase": "大文字と小文字を入れ替える", "PE.Views.Toolbar.mniUpperCase": "大文字", "PE.Views.Toolbar.strMenuNoFill": "塗りつぶしなし", "PE.Views.Toolbar.textAlignBottom": "テキストの下揃え", "PE.Views.Toolbar.textAlignCenter": "テキストを中央に揃える", "PE.Views.Toolbar.textAlignJust": "両端揃え", "PE.Views.Toolbar.textAlignLeft": "テキストの左揃え", "PE.Views.Toolbar.textAlignMiddle": "テキストを中央揃え", "PE.Views.Toolbar.textAlignRight": "テキストの右揃え", "PE.Views.Toolbar.textAlignTop": "テキストの上揃え", "PE.Views.Toolbar.textAlpha": "ギリシャ小文字アルファ", "PE.Views.Toolbar.textArrangeBack": "最背面ヘ移動", "PE.Views.Toolbar.textArrangeBackward": "背面ヘ移動", "PE.Views.Toolbar.textArrangeForward": "前面ヘ移動", "PE.Views.Toolbar.textArrangeFront": "最前面ヘ移動", "PE.Views.Toolbar.textBetta": "ギリシャ小文字ベータ", "PE.Views.Toolbar.textBlackHeart": "ブラック・ハート・スーツ", "PE.Views.Toolbar.textBold": "太字", "PE.Views.Toolbar.textBullet": "箇条書き", "PE.Views.Toolbar.textChart": "グラフ", "PE.Views.Toolbar.textColumnsCustom": "カスタム設定の列", "PE.Views.Toolbar.textColumnsOne": "1列", "PE.Views.Toolbar.textColumnsThree": "3列", "PE.Views.Toolbar.textColumnsTwo": "2列", "PE.Views.Toolbar.textContent": "コンテンツ", "PE.Views.Toolbar.textContentVertical": "コンテンツ（縦型）", "PE.Views.Toolbar.textCopyright": "著作権マーク", "PE.Views.Toolbar.textDegree": "度記号", "PE.Views.Toolbar.textDelta": "ギリシャ小文字デルタ", "PE.Views.Toolbar.textDivision": "除算記号", "PE.Views.Toolbar.textDollar": "ドル記号", "PE.Views.Toolbar.textEuro": "ユーロ記号", "PE.Views.Toolbar.textFooters": "フッター", "PE.Views.Toolbar.textGreaterEqual": "以上", "PE.Views.Toolbar.textInfinity": "無限", "PE.Views.Toolbar.textItalic": "イタリック体", "PE.Views.Toolbar.textLessEqual": "以下", "PE.Views.Toolbar.textLetterPi": "ギリシャの小文字ピー", "PE.Views.Toolbar.textLineSpaceOptions": "行間オプション", "PE.Views.Toolbar.textListSettings": "リストの設定", "PE.Views.Toolbar.textMoreSymbols": "その他の記号", "PE.Views.Toolbar.textNotEqualTo": "同等ではない", "PE.Views.Toolbar.textOneHalf": "普通分数の1/2", "PE.Views.Toolbar.textOneQuarter": "普通分数の1/4", "PE.Views.Toolbar.textPicture": "画像", "PE.Views.Toolbar.textPlusMinus": "プラスマイナス記号", "PE.Views.Toolbar.textRecentlyUsed": "最近使った項目", "PE.Views.Toolbar.textRegistered": "登録商標マーク", "PE.Views.Toolbar.textSection": "節記号", "PE.Views.Toolbar.textShapeAlignBottom": "下揃え", "PE.Views.Toolbar.textShapeAlignCenter": "中央揃え\t", "PE.Views.Toolbar.textShapeAlignLeft": "左揃え", "PE.Views.Toolbar.textShapeAlignMiddle": "上下中央揃え", "PE.Views.Toolbar.textShapeAlignRight": "右揃え", "PE.Views.Toolbar.textShapeAlignTop": "上揃え", "PE.Views.Toolbar.textShapesCombine": "結合", "PE.Views.Toolbar.textShapesFragment": "断片", "PE.Views.Toolbar.textShapesIntersect": "交差", "PE.Views.Toolbar.textShapesSubstract": "減算", "PE.Views.Toolbar.textShapesUnion": "連合", "PE.Views.Toolbar.textShowBegin": "先頭から表示する", "PE.Views.Toolbar.textShowCurrent": "現在のスライドからの表示", "PE.Views.Toolbar.textShowPresenterView": "発表者ビューを表示", "PE.Views.Toolbar.textShowSettings": "設定を表示", "PE.Views.Toolbar.textSmartArt": "SmartArt", "PE.Views.Toolbar.textSmile": "白い笑顔", "PE.Views.Toolbar.textSquareRoot": "平方根", "PE.Views.Toolbar.textStrikeout": "取り消し線", "PE.Views.Toolbar.textSubscript": "下付き文字", "PE.Views.Toolbar.textSuperscript": "上付き文字", "PE.Views.Toolbar.textTabAnimation": "アニメーション", "PE.Views.Toolbar.textTabCollaboration": "共同編集", "PE.Views.Toolbar.textTabDesign": "デザイン", "PE.Views.Toolbar.textTabDraw": "描画", "PE.Views.Toolbar.textTabFile": "ファイル", "PE.Views.Toolbar.textTabHome": "ホーム", "PE.Views.Toolbar.textTabInsert": "挿入", "PE.Views.Toolbar.textTable": "表", "PE.Views.Toolbar.textTabProtect": "保護", "PE.Views.Toolbar.textTabTransitions": "切り替え", "PE.Views.Toolbar.textTabView": "表示", "PE.Views.Toolbar.textText": "テキスト", "PE.Views.Toolbar.textTextVertical": "テキスト（縦）", "PE.Views.Toolbar.textTilde": "チルダ", "PE.Views.Toolbar.textTitle": "タイトル", "PE.Views.Toolbar.textTitleError": "エラー", "PE.Views.Toolbar.textTradeMark": "商標マーク", "PE.Views.Toolbar.textUnderline": "アンダーライン", "PE.Views.Toolbar.textYen": "円記号", "PE.Views.Toolbar.tipAddLayout": "レイアウトの追加", "PE.Views.Toolbar.tipAddSlide": "スライドの追加", "PE.Views.Toolbar.tipAddSlideMaster": "スライドマスターの追加", "PE.Views.Toolbar.tipBack": "戻る", "PE.Views.Toolbar.tipChangeCase": "大文字小文字を変更", "PE.Views.Toolbar.tipChangeChart": "グラフの種類を変更", "PE.Views.Toolbar.tipChangeSlide": "スライドのレイアウトを変更", "PE.Views.Toolbar.tipClearStyle": "スタイルのクリア", "PE.Views.Toolbar.tipCloseMaster": "テンプレートを閉じる", "PE.Views.Toolbar.tipColorSchemas": "配色を変更", "PE.Views.Toolbar.tipColumns": "列を挿入する", "PE.Views.Toolbar.tipCopy": "コピーする", "PE.Views.Toolbar.tipCopyStyle": "スタイルをコピーする", "PE.Views.Toolbar.tipCut": "切り取り", "PE.Views.Toolbar.tipDateTime": "現在の日付と時刻を挿入", "PE.Views.Toolbar.tipDecFont": "フォントサイズの縮小", "PE.Views.Toolbar.tipDecPrLeft": "インデントを減らす", "PE.Views.Toolbar.tipEditHeaderFooter": "ヘッダーまたはフッターの編集", "PE.Views.Toolbar.tipFontColor": "フォントの色", "PE.Views.Toolbar.tipFontName": "フォント", "PE.Views.Toolbar.tipFontSize": "フォントのサイズ", "PE.Views.Toolbar.tipHAligh": "左右の整列", "PE.Views.Toolbar.tipHighlightColor": "ハイライトの色", "PE.Views.Toolbar.tipIncFont": "フォントのサイズ拡大", "PE.Views.Toolbar.tipIncPrLeft": "インデントを増やす", "PE.Views.Toolbar.tipInsertAudio": "オーディオの挿入", "PE.Views.Toolbar.tipInsertChart": "グラフを挿入", "PE.Views.Toolbar.tipInsertChartPlaceholder": "チャート・プレースホルダーの挿入", "PE.Views.Toolbar.tipInsertContentPlaceholder": "コンテンツ・プレースホルダーの挿入", "PE.Views.Toolbar.tipInsertContentVerticalPlaceholder": "コンテンツ（垂直）プレースホルダーの挿入", "PE.Views.Toolbar.tipInsertEquation": "方程式を挿入", "PE.Views.Toolbar.tipInsertHorizontalText": "横書きテキストボックスの挿入", "PE.Views.Toolbar.tipInsertHyperlink": "ハイパーリンクを追加", "PE.Views.Toolbar.tipInsertImage": "画像を挿入", "PE.Views.Toolbar.tipInsertPicturePlaceholder": "画像プレースホルダーの挿入", "PE.Views.Toolbar.tipInsertShape": "図形を挿入", "PE.Views.Toolbar.tipInsertSmartArt": "SmartArtの挿入", "PE.Views.Toolbar.tipInsertSmartArtPlaceholder": "SmartArtプレースホルダの挿入", "PE.Views.Toolbar.tipInsertSymbol": "記号を挿入", "PE.Views.Toolbar.tipInsertTable": "表の挿入", "PE.Views.Toolbar.tipInsertTablePlaceholder": "テーブル・プレースホルダの挿入", "PE.Views.Toolbar.tipInsertText": "テキストボックスを挿入", "PE.Views.Toolbar.tipInsertTextArt": "テキストアートの挿入", "PE.Views.Toolbar.tipInsertTextPlaceholder": "テキストプレースホルダーの挿入", "PE.Views.Toolbar.tipInsertTextVerticalPlaceholder": "テキスト（縦書き）プレースホルダーの挿入", "PE.Views.Toolbar.tipInsertVerticalText": "縦書きテキストボックスの挿入", "PE.Views.Toolbar.tipInsertVideo": "ビデオを挿入", "PE.Views.Toolbar.tipLineSpace": "行間", "PE.Views.Toolbar.tipMarkers": "箇条書き", "PE.Views.Toolbar.tipMarkersArrow": "箇条書き（矢印）", "PE.Views.Toolbar.tipMarkersCheckmark": "箇条書き（チェックマーク）", "PE.Views.Toolbar.tipMarkersDash": "「ダッシュ」記号", "PE.Views.Toolbar.tipMarkersFRhombus": "箇条書き（ひし形）", "PE.Views.Toolbar.tipMarkersFRound": "箇条書き（丸）", "PE.Views.Toolbar.tipMarkersFSquare": "箇条書き（四角）", "PE.Views.Toolbar.tipMarkersHRound": "箇条書き（円）", "PE.Views.Toolbar.tipMarkersStar": "箇条書き（星）", "PE.Views.Toolbar.tipNone": "なし", "PE.Views.Toolbar.tipNumbers": "ナンバリング", "PE.Views.Toolbar.tipPaste": "貼り付け", "PE.Views.Toolbar.tipPreview": "スライドショーの開始", "PE.Views.Toolbar.tipPrint": "印刷する", "PE.Views.Toolbar.tipPrintQuick": "クイックプリント", "PE.Views.Toolbar.tipRedo": "やり直す", "PE.Views.Toolbar.tipReplace": "置き換え", "PE.Views.Toolbar.tipSave": "保存する", "PE.Views.Toolbar.tipSaveCoauth": "変更内容を保存して、他のユーザーが確認できるようにします。", "PE.Views.Toolbar.tipSelectAll": "すべて選択", "PE.Views.Toolbar.tipShapeAlign": "図形の配置", "PE.Views.Toolbar.tipShapeArrange": "配置", "PE.Views.Toolbar.tipShapesMerge": "図形を結合", "PE.Views.Toolbar.tipSlideNum": "スライド番号の追加", "PE.Views.Toolbar.tipSlideSize": "スライドサイズの選択", "PE.Views.Toolbar.tipSlideTheme": "スライドのテーマ", "PE.Views.Toolbar.tipUndo": "元に戻す", "PE.Views.Toolbar.tipVAligh": "垂直揃え", "PE.Views.Toolbar.tipViewSettings": "表示の設定", "PE.Views.Toolbar.txtColors": "色", "PE.Views.Toolbar.txtDistribHor": "水平に整列する", "PE.Views.Toolbar.txtDistribVert": "上下に整列する", "PE.Views.Toolbar.txtDuplicateSlide": "スライドの複製", "PE.Views.Toolbar.txtGroup": "グループ", "PE.Views.Toolbar.txtObjectsAlign": "選択したオブジェクトを整列する", "PE.Views.Toolbar.txtSlideAlign": "スライドに合わせる", "PE.Views.Toolbar.txtSlideSize": "スライドのサイズ", "PE.Views.Toolbar.txtUngroup": "グループ解除", "PE.Views.Transitions.strDelay": "遅延", "PE.Views.Transitions.strDuration": "期間", "PE.Views.Transitions.strStartOnClick": "クリックで開始", "PE.Views.Transitions.textBlack": "黒色を使う", "PE.Views.Transitions.textBottom": "下", "PE.Views.Transitions.textBottomLeft": "左下", "PE.Views.Transitions.textBottomRight": "右下", "PE.Views.Transitions.textClock": "時計", "PE.Views.Transitions.textClockwise": "時計回り", "PE.Views.Transitions.textCounterclockwise": "反時計回り", "PE.Views.Transitions.textCover": "カバー", "PE.Views.Transitions.textFade": "フェード", "PE.Views.Transitions.textHorizontalIn": "水平（中）", "PE.Views.Transitions.textHorizontalOut": "水平（外）", "PE.Views.Transitions.textLeft": "左", "PE.Views.Transitions.textMorph": "変形", "PE.Views.Transitions.textMorphLetters": "文字", "PE.Views.Transitions.textMorphObjects": "オブジェクト", "PE.Views.Transitions.textMorphWord": "言葉", "PE.Views.Transitions.textNone": "なし", "PE.Views.Transitions.textPush": "押す", "PE.Views.Transitions.textRandom": "ランダム", "PE.Views.Transitions.textRight": "右", "PE.Views.Transitions.textSmoothly": "スムーズに", "PE.Views.Transitions.textSplit": "分割", "PE.Views.Transitions.textTop": "上", "PE.Views.Transitions.textTopLeft": "左上", "PE.Views.Transitions.textTopRight": "右上", "PE.Views.Transitions.textUnCover": "アンカバー", "PE.Views.Transitions.textVerticalIn": "縦（中）", "PE.Views.Transitions.textVerticalOut": "縦（外）", "PE.Views.Transitions.textWedge": "くさび形", "PE.Views.Transitions.textWipe": "ワイプ", "PE.Views.Transitions.textZoom": "ズーム", "PE.Views.Transitions.textZoomIn": "ズームイン", "PE.Views.Transitions.textZoomOut": "ズームアウト", "PE.Views.Transitions.textZoomRotate": "ズームと回転", "PE.Views.Transitions.txtApplyToAll": "全てのスライドに適用する", "PE.Views.Transitions.txtParameters": "パラメーター", "PE.Views.Transitions.txtPreview": "プレビュー", "PE.Views.Transitions.txtSec": "秒", "PE.Views.ViewTab.capBtnHand": "手のひら", "PE.Views.ViewTab.capBtnSelect": "選択", "PE.Views.ViewTab.textAddHGuides": "水平方向のガイドの追加", "PE.Views.ViewTab.textAddVGuides": "垂直方向のガイドの追加", "PE.Views.ViewTab.textAlwaysShowToolbar": "ツールバーを常に表示する", "PE.Views.ViewTab.textClearGuides": "ガイドのクリア", "PE.Views.ViewTab.textCm": "センチ", "PE.Views.ViewTab.textCustom": "ユーザー設定", "PE.Views.ViewTab.textFill": "塗りつぶし", "PE.Views.ViewTab.textFitToSlide": "スライドに合わせる", "PE.Views.ViewTab.textFitToWidth": "幅に合わせる", "PE.Views.ViewTab.textGridlines": "グリッド線", "PE.Views.ViewTab.textGuides": "ガイド", "PE.Views.ViewTab.textInterfaceTheme": "インターフェイスのテーマ", "PE.Views.ViewTab.textLeftMenu": "左パネル", "PE.Views.ViewTab.textLine": "線", "PE.Views.ViewTab.textMacros": "マ<PERSON><PERSON>", "PE.Views.ViewTab.textNormal": "標準", "PE.Views.ViewTab.textNotes": "ノート", "PE.Views.ViewTab.textRightMenu": "右パネル", "PE.Views.ViewTab.textRulers": "ルーラー", "PE.Views.ViewTab.textShowGridlines": "枠線を表示する", "PE.Views.ViewTab.textShowGuides": "ガイドを表示", "PE.Views.ViewTab.textSlideMaster": "スライドマスター", "PE.Views.ViewTab.textSmartGuides": "スマートガイド", "PE.Views.ViewTab.textSnapObjects": "スナップオブジェクトをグリッドに", "PE.Views.ViewTab.textStatusBar": "ステータスバー", "PE.Views.ViewTab.textTabStyle": "タブのスタイル", "PE.Views.ViewTab.textZoom": "ズーム", "PE.Views.ViewTab.tipFitToSlide": "スライドに合わせる", "PE.Views.ViewTab.tipFitToWidth": "幅に合わせる", "PE.Views.ViewTab.tipGridlines": "枠線を表示する", "PE.Views.ViewTab.tipGuides": "ガイドを表示", "PE.Views.ViewTab.tipHandTool": "「手のひら」ツール", "PE.Views.ViewTab.tipInterfaceTheme": "インターフェースのテーマ", "PE.Views.ViewTab.tipMacros": "マ<PERSON><PERSON>", "PE.Views.ViewTab.tipNormal": "標準", "PE.Views.ViewTab.tipSelectTool": "選択ツール", "PE.Views.ViewTab.tipSlideMaster": "スライドマスター", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}