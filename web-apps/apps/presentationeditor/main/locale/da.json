{"Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Anonym", "Common.Controllers.ExternalDiagramEditor.textClose": "Luk", "Common.Controllers.ExternalDiagramEditor.warningText": "Objektet er slået fra da det bliver redigeret af en anden bruger. ", "Common.Controllers.ExternalDiagramEditor.warningTitle": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textAnonymous": "Anonymous", "Common.Controllers.ExternalOleEditor.textClose": "Close", "Common.Controllers.ExternalOleEditor.warningText": "The object is disabled because it is being edited by another user.", "Common.Controllers.ExternalOleEditor.warningTitle": "Warning", "Common.Controllers.History.notcriticalErrorTitle": "Warning", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "<PERSON><PERSON>r<PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "Stablet område", "Common.define.chartData.textAreaStackedPer": "100% stablet område", "Common.define.chartData.textBar": "<PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Grupperet kolonne", "Common.define.chartData.textBarNormal3d": "3D grupperet kolonne", "Common.define.chartData.textBarNormal3dPerspective": "3D kolonne", "Common.define.chartData.textBarStacked": "Stablet kolonne", "Common.define.chartData.textBarStacked3d": "3D stablet kolonne", "Common.define.chartData.textBarStackedPer": "100% stablet kolonne", "Common.define.chartData.textBarStackedPer3d": "3D 100% stablet kolonne", "Common.define.chartData.textCharts": "Diagrammer", "Common.define.chartData.textColumn": "Kolonne", "Common.define.chartData.textCombo": "Kombination", "Common.define.chartData.textComboAreaBar": "Stablet område - grupperet kolonne", "Common.define.chartData.textComboBarLine": "Grupperet kolonne - linje", "Common.define.chartData.textComboBarLineSecondary": "Grupperet kolonne - linje på sekundær akse", "Common.define.chartData.textComboCustom": "Brugerdefineret kombination", "Common.define.chartData.textDoughnut": "Donut", "Common.define.chartData.textHBarNormal": "Grupperet søjle", "Common.define.chartData.textHBarNormal3d": "3D grupperet søjle", "Common.define.chartData.textHBarStacked": "Stablet søjle", "Common.define.chartData.textHBarStacked3d": "3D stablet søjle", "Common.define.chartData.textHBarStackedPer": "100% stablet søjle", "Common.define.chartData.textHBarStackedPer3d": "3D 100% stablet søjle", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "3D linje", "Common.define.chartData.textLineMarker": "<PERSON><PERSON> med <PERSON>ø<PERSON>", "Common.define.chartData.textLineStacked": "Stablet linje", "Common.define.chartData.textLineStackedMarker": "Stablet linje med markører", "Common.define.chartData.textLineStackedPer": "100% stablet linje", "Common.define.chartData.textLineStackedPerMarker": "100% stablet linje med markører", "Common.define.chartData.textPie": "Cirkeldiagram", "Common.define.chartData.textPie3d": "3D lagkage", "Common.define.chartData.textPoint": "XY (Spredning)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Scatter med lige linjer", "Common.define.chartData.textScatterLineMarker": "Scatter med lige linjer og markører", "Common.define.chartData.textScatterSmooth": "Scatter med jævne linjer", "Common.define.chartData.textScatterSmoothMarker": "Scatter med jævne linjer og markører", "Common.define.chartData.textStock": "Aktie", "Common.define.chartData.textSurface": "Overflade", "Common.define.effectData.textAcross": "Across", "Common.define.effectData.textAppear": "Appear", "Common.define.effectData.textArcDown": "Arc Down", "Common.define.effectData.textArcLeft": "Arc Left", "Common.define.effectData.textArcRight": "Arc Right", "Common.define.effectData.textArcs": "Arcs", "Common.define.effectData.textArcUp": "Arc Up", "Common.define.effectData.textBasic": "Basic", "Common.define.effectData.textBasicSwivel": "Basic swivel", "Common.define.effectData.textBasicZoom": "Basic Zoom", "Common.define.effectData.textBean": "<PERSON>", "Common.define.effectData.textBlinds": "Blinds", "Common.define.effectData.textBlink": "Blink", "Common.define.effectData.textBoldFlash": "Bold Flash", "Common.define.effectData.textBoldReveal": "Bold Reveal", "Common.define.effectData.textBoomerang": "Boomerang", "Common.define.effectData.textBounce": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounceLeft": "<PERSON><PERSON><PERSON> Left", "Common.define.effectData.textBounceRight": "Bounce Right", "Common.define.effectData.textBox": "Box", "Common.define.effectData.textBrushColor": "Brush color", "Common.define.effectData.textCenterRevolve": "Center revolve", "Common.define.effectData.textCheckerboard": "Checkerboard", "Common.define.effectData.textCircle": "Circle", "Common.define.effectData.textCollapse": "Collapse", "Common.define.effectData.textColorPulse": "Color pulse", "Common.define.effectData.textComplementaryColor": "Complementary color", "Common.define.effectData.textComplementaryColor2": "Complementary color 2", "Common.define.effectData.textCompress": "Compress", "Common.define.effectData.textContrast": "Contrast", "Common.define.effectData.textContrastingColor": "Contrasting color", "Common.define.effectData.textCredits": "Credits", "Common.define.effectData.textCrescentMoon": "Crescent Moon", "Common.define.effectData.textCurveDown": "Curve Down", "Common.define.effectData.textCurvedSquare": "Curved square", "Common.define.effectData.textCurvedX": "Curved X", "Common.define.effectData.textCurvyLeft": "<PERSON>urvy Left", "Common.define.effectData.textCurvyRight": "Curvy Right", "Common.define.effectData.textCurvyStar": "Curvy Star", "Common.define.effectData.textCustomPath": "Custom Path", "Common.define.effectData.textCuverUp": "Curve up", "Common.define.effectData.textDarken": "Darken", "Common.define.effectData.textDecayingWave": "Decaying wave", "Common.define.effectData.textDesaturate": "Desaturate", "Common.define.effectData.textDiagonalDownRight": "Diagonal down right", "Common.define.effectData.textDiagonalUpRight": "Diagonal up right", "Common.define.effectData.textDiamond": "Diamond", "Common.define.effectData.textDisappear": "Disappear", "Common.define.effectData.textDissolveIn": "Dissolve In", "Common.define.effectData.textDissolveOut": "Dissolve Out", "Common.define.effectData.textDown": "Down", "Common.define.effectData.textDrop": "Drop", "Common.define.effectData.textEmphasis": "Emphasis Effects", "Common.define.effectData.textEntrance": "Entrance Effects", "Common.define.effectData.textEqualTriangle": "Equal triangle", "Common.define.effectData.textExciting": "Exciting", "Common.define.effectData.textExit": "Exit effects", "Common.define.effectData.textExpand": "Expand", "Common.define.effectData.textFade": "Fade", "Common.define.effectData.textFigureFour": "Figure 8 Four", "Common.define.effectData.textFillColor": "Udfyldningsfarve", "Common.define.effectData.textFlip": "Flip", "Common.define.effectData.textFloat": "Float", "Common.define.effectData.textFloatDown": "Float down", "Common.define.effectData.textFloatIn": "Float In", "Common.define.effectData.textFloatOut": "Float Out", "Common.define.effectData.textFloatUp": "Float Up", "Common.define.effectData.textFlyIn": "Fly In", "Common.define.effectData.textFlyOut": "Fly Out", "Common.define.effectData.textFontColor": "Font Color", "Common.define.effectData.textFootball": "Football", "Common.define.effectData.textFromBottom": "From bottom", "Common.define.effectData.textFromBottomLeft": "From bottom-left", "Common.define.effectData.textFromBottomRight": "From bottom-right", "Common.define.effectData.textFromLeft": "From left", "Common.define.effectData.textFromRight": "From right", "Common.define.effectData.textFromTop": "From top", "Common.define.effectData.textFromTopLeft": "From top-left", "Common.define.effectData.textFromTopRight": "From top-right", "Common.define.effectData.textFunnel": "Funnel", "Common.define.effectData.textGrowShrink": "Grow/Shrink", "Common.define.effectData.textGrowTurn": "Grow & Turn", "Common.define.effectData.textGrowWithColor": "Grow with color", "Common.define.effectData.textHeart": "Heart", "Common.define.effectData.textHeartbeat": "Heartbeat", "Common.define.effectData.textHexagon": "Hexagon", "Common.define.effectData.textHorizontal": "Horizontal", "Common.define.effectData.textHorizontalFigure": "Horizontal Figure 8", "Common.define.effectData.textHorizontalIn": "Horizontal In", "Common.define.effectData.textHorizontalOut": "Horizontal Out", "Common.define.effectData.textIn": "In", "Common.define.effectData.textInFromScreenCenter": "In from screen center", "Common.define.effectData.textInSlightly": "In slightly", "Common.define.effectData.textInToScreenBottom": "In to screen bottom", "Common.define.effectData.textInvertedSquare": "Inverted square", "Common.define.effectData.textInvertedTriangle": "Inverted triangle", "Common.define.effectData.textLeft": "Left", "Common.define.effectData.textLeftDown": " Left down", "Common.define.effectData.textLeftUp": " Left up", "Common.define.effectData.textLighten": "Lighten", "Common.define.effectData.textLineColor": "Line color", "Common.define.effectData.textLines": "Lines", "Common.define.effectData.textLinesCurves": "Lines curves", "Common.define.effectData.textLoopDeLoop": "Loop de Loop", "Common.define.effectData.textLoops": "Loops", "Common.define.effectData.textModerate": "Moderate", "Common.define.effectData.textNeutron": "Neutron", "Common.define.effectData.textObjectCenter": "Object center", "Common.define.effectData.textObjectColor": "Object color", "Common.define.effectData.textOctagon": "Octagon", "Common.define.effectData.textOut": "Out", "Common.define.effectData.textOutFromScreenBottom": "Out from screen bottom", "Common.define.effectData.textOutSlightly": "Out slightly", "Common.define.effectData.textOutToScreenCenter": "Out to screen center", "Common.define.effectData.textParallelogram": "Parallelogram", "Common.define.effectData.textPath": "Motion paths", "Common.define.effectData.textPathCurve": "Curve", "Common.define.effectData.textPathLine": "<PERSON><PERSON>", "Common.define.effectData.textPathScribble": "Scribble", "Common.define.effectData.textPeanut": "Peanut", "Common.define.effectData.textPeekIn": "Peek In", "Common.define.effectData.textPeekOut": "Peek Out", "Common.define.effectData.textPentagon": "Pentagon", "Common.define.effectData.textPinwheel": "Pinwheel", "Common.define.effectData.textPlus": "Plus", "Common.define.effectData.textPointStar": "Point Star", "Common.define.effectData.textPointStar4": "4 Point Star", "Common.define.effectData.textPointStar5": "5 Point Star", "Common.define.effectData.textPointStar6": "6 Point Star", "Common.define.effectData.textPointStar8": "8 Point Star", "Common.define.effectData.textPulse": "Pulse", "Common.define.effectData.textRandomBars": "Random Bars ", "Common.define.effectData.textRight": "Right", "Common.define.effectData.textRightDown": " Right down", "Common.define.effectData.textRightTriangle": "Right triangle", "Common.define.effectData.textRightUp": " Right up", "Common.define.effectData.textRiseUp": "Rise Up", "Common.define.effectData.textSCurve1": "S Curve 1", "Common.define.effectData.textSCurve2": "S Curve 2", "Common.define.effectData.textShape": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textShapes": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textShimmer": "Shimmer", "Common.define.effectData.textShrinkTurn": "Shrink & Turn", "Common.define.effectData.textSineWave": "Sine Wave", "Common.define.effectData.textSinkDown": "Sink Down", "Common.define.effectData.textSlideCenter": "Slide Center", "Common.define.effectData.textSpecial": "Special", "Common.define.effectData.textSpin": "Spin", "Common.define.effectData.textSpinner": "Spinner", "Common.define.effectData.textSpiralIn": "Spiral In", "Common.define.effectData.textSpiralLeft": "<PERSON><PERSON><PERSON> left", "Common.define.effectData.textSpiralOut": "Spiral Out", "Common.define.effectData.textSpiralRight": "Spiral right", "Common.define.effectData.textSplit": "Split", "Common.define.effectData.textSpoke1": "1 Spoke", "Common.define.effectData.textSpoke2": "2 Spokes", "Common.define.effectData.textSpoke3": "3 Spokes", "Common.define.effectData.textSpoke4": "4 Spokes", "Common.define.effectData.textSpoke8": "8 Spokes", "Common.define.effectData.textSpring": "Spring", "Common.define.effectData.textSquare": "Square", "Common.define.effectData.textStairsDown": "Stairs Down", "Common.define.effectData.textStretch": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textStrips": "Strips", "Common.define.effectData.textSubtle": "Subtle", "Common.define.effectData.textSwivel": "Swivel", "Common.define.effectData.textSwoosh": "Swoosh", "Common.define.effectData.textTeardrop": "Teardrop", "Common.define.effectData.textTeeter": "Teeter", "Common.define.effectData.textToBottom": "To bottom", "Common.define.effectData.textToBottomLeft": "To bottom-left", "Common.define.effectData.textToBottomRight": "To bottom-right", "Common.define.effectData.textToLeft": "To Left", "Common.define.effectData.textToRight": "To right", "Common.define.effectData.textToTop": "To top", "Common.define.effectData.textToTopLeft": "To top-left", "Common.define.effectData.textToTopRight": "To top-right", "Common.define.effectData.textTransparency": "Transparency", "Common.define.effectData.textTrapezoid": "Trapezoid", "Common.define.effectData.textTurnDown": "Turn down", "Common.define.effectData.textTurnDownRight": "Turn down right", "Common.define.effectData.textTurns": "Turns", "Common.define.effectData.textTurnUp": "Turn up", "Common.define.effectData.textTurnUpRight": "Turn up right", "Common.define.effectData.textUnderline": "Underline", "Common.define.effectData.textUp": "Up", "Common.define.effectData.textVertical": "Vertical", "Common.define.effectData.textVerticalFigure": "Vertical Figure 8", "Common.define.effectData.textVerticalIn": "Vertical In", "Common.define.effectData.textVerticalOut": "Vertical Out", "Common.define.effectData.textWave": "Wave", "Common.define.effectData.textWedge": "Wedge", "Common.define.effectData.textWheel": "Wheel", "Common.define.effectData.textWhip": "Whip", "Common.define.effectData.textWipe": "Wipe", "Common.define.effectData.textZigzag": "Zigzag", "Common.define.effectData.textZoom": "Zoom", "Common.define.gridlineData.txtCm": "cm", "Common.define.gridlineData.txtPt": "pt", "Common.define.smartArt.textAccentedPicture": "Accented picture", "Common.define.smartArt.textAccentProcess": "Accent process", "Common.define.smartArt.textAlternatingFlow": "Alternating flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating Hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating picture blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating picture circles", "Common.define.smartArt.textArchitectureLayout": "Architecture layout", "Common.define.smartArt.textArrowRibbon": "Arrow ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending picture accent process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic bending process", "Common.define.smartArt.textBasicBlockList": "Basic block list", "Common.define.smartArt.textBasicChevronProcess": "Basic chevron process", "Common.define.smartArt.textBasicCycle": "Basic cycle", "Common.define.smartArt.textBasicMatrix": "Basic matrix", "Common.define.smartArt.textBasicPie": "Basic pie", "Common.define.smartArt.textBasicProcess": "Basic process", "Common.define.smartArt.textBasicPyramid": "Basic pyramid", "Common.define.smartArt.textBasicRadial": "Basic radial", "Common.define.smartArt.textBasicTarget": "Basic target", "Common.define.smartArt.textBasicTimeline": "Basic timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending picture accent list", "Common.define.smartArt.textBendingPictureBlocks": "Bending picture blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending picture caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending picture caption list", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending picture semi-transparent text", "Common.define.smartArt.textBlockCycle": "Block cycle", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron accent process", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Circle accent timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging Arrows", "Common.define.smartArt.textDivergingRadial": "Diverging Radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy List", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined list", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and title organization chart", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing Ideas", "Common.define.smartArt.textOrganizationChart": "Organization chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased Process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture accent blocks", "Common.define.smartArt.textPictureAccentList": "Picture accent list", "Common.define.smartArt.textPictureAccentProcess": "Picture accent process", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture Strips", "Common.define.smartArt.textPieProcess": "Pie Process", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial Cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "Spiral picture", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Tabbed <PERSON>", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward Arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "More", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "Dokumentet er i brug af en anden applikation. Du kan fortsætte med at redigere og gemme en kopi.", "Common.Translation.warnFileLockedBtnEdit": "Opret en kopi", "Common.Translation.warnFileLockedBtnView": "<PERSON><PERSON> for visning", "Common.UI.ButtonColored.textAutoColor": "Automatisk", "Common.UI.ButtonColored.textEyedropper": "Pipette", "Common.UI.ButtonColored.textNewColor": "Brugerdefineret farve", "Common.UI.ComboBorderSize.txtNoBorders": "Ingen rammer", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Ingen rammer", "Common.UI.ComboDataView.emptyComboText": "Ingen stilarter", "Common.UI.ExtendedColorDialog.addButtonText": "Tilføj", "Common.UI.ExtendedColorDialog.textCurrent": "Nuværende", "Common.UI.ExtendedColorDialog.textHexErr": "Den indtastede værdi er ikke korrekt.<br>Venligst indtast en værdi mellem 000000 og FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Ny", "Common.UI.ExtendedColorDialog.textRGBErr": "Den indtastede værdi er ikke korrekt.<br>Venligst indtast en numerisk værdi mellem 0 og 255.", "Common.UI.HSBColorPicker.textNoColor": "Ingen farve", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Hide password", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Show password", "Common.UI.SearchBar.textFind": "Find", "Common.UI.SearchBar.tipCloseSearch": "Close find", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Previous result", "Common.UI.SearchDialog.textHighlight": "Fremhæv resultater", "Common.UI.SearchDialog.textMatchCase": "Afhængigt af store og små bogstaver", "Common.UI.SearchDialog.textReplaceDef": "Skriv din erstatningstekst", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON><PERSON> din tekst her", "Common.UI.SearchDialog.textTitle": "Find og erstat", "Common.UI.SearchDialog.textTitle2": "Find", "Common.UI.SearchDialog.textWholeWords": "<PERSON>n hele ord", "Common.UI.SearchDialog.txtBtnHideReplace": "Skjul erstat", "Common.UI.SearchDialog.txtBtnReplace": "Erstat", "Common.UI.SearchDialog.txtBtnReplaceAll": "Erstat alle", "Common.UI.SynchronizeTip.textDontShow": "Vis ikke denne meddelelse igen", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Dokumentet er blevet ændret af en anden bruger.<br><PERSON><PERSON><PERSON><PERSON><PERSON> tryk for at gemme ændringerne og genindlæs opdateringerne.", "Common.UI.ThemeColorPalette.textRecentColors": "Recent colors", "Common.UI.ThemeColorPalette.textStandartColors": "Standard farve", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON> far<PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klassisk lys", "Common.UI.Themes.txtThemeContrastDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeGray": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "Lys", "Common.UI.Themes.txtThemeSystem": "Samme som system", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "Luk", "Common.UI.Window.noButtonText": "<PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Bekræftelse", "Common.UI.Window.textDontShow": "Vis ikke denne meddelelse igen", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "Oplysninger", "Common.UI.Window.textWarning": "<PERSON><PERSON><PERSON>", "Common.UI.Window.yesButtonText": "<PERSON>a", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Background", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Blue", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Dark green", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "adresse:", "Common.Views.About.txtLicensee": "Licenstager", "Common.Views.About.txtLicensor": "Licensgiver", "Common.Views.About.txtMail": "email:", "Common.Views.About.txtPoweredBy": "Drevet af", "Common.Views.About.txtTel": "tlf.:", "Common.Views.About.txtVersion": "Version", "Common.Views.AutoCorrectDialog.textAdd": "Tilføj", "Common.Views.AutoCorrectDialog.textApplyText": "<PERSON><PERSON><PERSON> mens du taster", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Tekst autokorrektur", "Common.Views.AutoCorrectDialog.textAutoFormat": "Løbende autoformatering", "Common.Views.AutoCorrectDialog.textBulleted": "Automatiske punktlister", "Common.Views.AutoCorrectDialog.textBy": "Af", "Common.Views.AutoCorrectDialog.textDelete": "Slet", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Add period with double-space", "Common.Views.AutoCorrectDialog.textFLCells": "Capitalize first letter of table cells", "Common.Views.AutoCorrectDialog.textFLDont": "Don`t capitalize after", "Common.Views.AutoCorrectDialog.textFLSentence": "Sæt første bogstav i sætninger med stort", "Common.Views.AutoCorrectDialog.textForLangFL": "Exceptions for the language:", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet- og netværksstier med hyperlinks", "Common.Views.AutoCorrectDialog.textHyphens": "Bindestreger (--) med streg (-)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Matematisk autokorrektur", "Common.Views.AutoCorrectDialog.textNumbered": "Automatiske nummererede lister", "Common.Views.AutoCorrectDialog.textQuotes": "\"Lige anførselstegn\" med \"smarte anførselstegn\"", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON> funk<PERSON>er", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Følgende udtryk er anerkendte matematiske udtryk. De bliver ikke automatisk kursiveret.", "Common.Views.AutoCorrectDialog.textReplace": "Erstat", "Common.Views.AutoCorrectDialog.textReplaceText": "Erstat løbende", "Common.Views.AutoCorrectDialog.textReplaceType": "Erstat tekst løbende", "Common.Views.AutoCorrectDialog.textReset": "Nulstil", "Common.Views.AutoCorrectDialog.textResetAll": "Nulstil til standard", "Common.Views.AutoCorrectDialog.textRestore": "Gendan", "Common.Views.AutoCorrectDialog.textTitle": "Autokorrektur", "Common.Views.AutoCorrectDialog.textWarnAddFL": "Exceptions must contain only the letters, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnAddRec": "<PERSON>dte funktioner må kun indeholde bogstaverne A til Z, store og små bogstaver.", "Common.Views.AutoCorrectDialog.textWarnResetFL": "Any exceptions you added will be removed and the removed ones will be restored. Do you want to continue?", "Common.Views.AutoCorrectDialog.textWarnResetRec": "<PERSON><PERSON><PERSON>, du har til<PERSON>ø<PERSON>, f<PERSON><PERSON>, og de fjernede gendanne<PERSON>. Ønsker du at fortsætte?", "Common.Views.AutoCorrectDialog.warnReplace": "Autokorrekturindtastningen for %1 findes allerede. Ønsker du at erstatte den?", "Common.Views.AutoCorrectDialog.warnReset": "Al autokorrektur du tilføjede fjernes, og de ændrede gendannes til deres oprindelige værdier. Ønsker du at fortsætte?", "Common.Views.AutoCorrectDialog.warnRestore": "Autokorrekturindtastningen for %1 nulstilles til sin oprindelige værdi. Ønsker du at fortsætte?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "Send", "Common.Views.Comments.mniAuthorAsc": "Forfatter A til Z", "Common.Views.Comments.mniAuthorDesc": "Forfatter Z til A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "Nyeste", "Common.Views.Comments.mniFilterGroups": "Filter by Group", "Common.Views.Comments.mniPositionAsc": "<PERSON>a toppen", "Common.Views.Comments.mniPositionDesc": "Fra bunden", "Common.Views.Comments.textAdd": "Tilføj", "Common.Views.Comments.textAddComment": "Tilføj kommentar", "Common.Views.Comments.textAddCommentToDoc": "Tilføj kommentar til dokument", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAll": "All", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "Luk", "Common.Views.Comments.textClosePanel": "Luk kommentarer", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON><PERSON> din kommentar her", "Common.Views.Comments.textHintAddComment": "Tilføj kommentar", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON> igen", "Common.Views.Comments.textReply": "<PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Sorter kommentarer", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "You have no permission to reopen the comment", "Common.Views.Comments.txtEmpty": "There are no comments in the document.", "Common.Views.CopyWarningDialog.textDontShow": "Vis ikke denne meddelelse igen", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON>, klip og sæt ind handlinger ved brug af redigeringsværktøjets værktøjsbarknapper og kontekst menu handlinger vil kun blive foretaget i redigeringsfanen. <br><br> for at kopier og sætte ind til eller fra programmer uden for redigeringsværktøjet, skal du bruge følgende tastaturtaster: ", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON>, Klip og Indsæt handlinger", "Common.Views.CopyWarningDialog.textToCopy": "For kopiering", "Common.Views.CopyWarningDialog.textToCut": "For klipning", "Common.Views.CopyWarningDialog.textToPaste": "for sæt ind", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textStartOver": "Show from Beginning", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Indlæser...", "Common.Views.DocumentAccessDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> for deling", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "Select", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "Select", "Common.Views.Draw.txtSize": "Size", "Common.Views.ExternalDiagramEditor.textTitle": "Diagram redigering", "Common.Views.ExternalEditor.textClose": "Close", "Common.Views.ExternalEditor.textSave": "Gem og luk", "Common.Views.ExternalOleEditor.textTitle": "Spreadsheet Editor", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Brugere som redigerer dokumentet:", "Common.Views.Header.textAddFavorite": "<PERSON><PERSON><PERSON> som favorit", "Common.Views.Header.textAdvSettings": "<PERSON><PERSON><PERSON><PERSON> in<PERSON>", "Common.Views.Header.textBack": "Gå til dokumentplacering", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "Vis kompakt værktøjslinie ", "Common.Views.Header.textHideLines": "Skjul lineal", "Common.Views.Header.textHideNotes": "<PERSON><PERSON><PERSON><PERSON> noter", "Common.Views.Header.textHideStatusBar": "Skjul statuslinie", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "<PERSON><PERSON><PERSON> fra <PERSON>", "Common.Views.Header.textSaveBegin": "Gemmer...", "Common.Views.Header.textSaveChanged": "Modificeret", "Common.Views.Header.textSaveEnd": "Alle ændringer gemt", "Common.Views.Header.textSaveExpander": "Alle ændringer gemt", "Common.Views.Header.textShare": "Share", "Common.Views.Header.textStartOver": "Show from Beginning", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "<PERSON><PERSON><PERSON><PERSON> adgangsrettigheder for dokument", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "Hent fil", "Common.Views.Header.tipGoEdit": "Rediger nuværende fil", "Common.Views.Header.tipPrint": "Udskriv fil", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Gem", "Common.Views.Header.tipSearch": "Find", "Common.Views.Header.tipStartOver": "Start slideshow from beginning", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Lås af i seperat vindue", "Common.Views.Header.tipUsers": "View users", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON> in<PERSON>", "Common.Views.Header.tipViewUsers": "Vis brugere og håndter dokumentrettighederne ", "Common.Views.Header.txtAccessRights": "Skift adgangsrettigheder", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textCloseHistory": "Luk historik", "Common.Views.History.textHideAll": "<PERSON><PERSON><PERSON><PERSON> de<PERSON><PERSON> æ<PERSON>", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "Gendan", "Common.Views.History.textShowAll": "<PERSON><PERSON> de<PERSON> æ<PERSON>", "Common.Views.History.textVer": "Ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Indsæt et billede URL: ", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON> felt er nødvendigt", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Feltet skal være en URL i \"http://www.example.com\" formatet", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Du skal angive gyldige rækker og kolonner nummer.", "Common.Views.InsertTableDialog.txtColumns": "<PERSON><PERSON>", "Common.Views.InsertTableDialog.txtMaxText": "<PERSON> maksimale værdi i dette felt er {0}.", "Common.Views.InsertTableDialog.txtMinText": "Minimum værdien for dette felt er {0}.", "Common.Views.InsertTableDialog.txtRows": "<PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitleSplit": "Dele celle", "Common.Views.LanguageDialog.labelSelect": "Vælg dokument sprog", "Common.Views.ListSettingsDialog.textBulleted": "Punkttegnet", "Common.Views.ListSettingsDialog.textFromFile": "From file", "Common.Views.ListSettingsDialog.textFromStorage": "From storage", "Common.Views.ListSettingsDialog.textFromUrl": "From URL", "Common.Views.ListSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textSelect": "Select from", "Common.Views.ListSettingsDialog.tipChange": "Skift kugle", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Image", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "Nyt punkt", "Common.Views.ListSettingsDialog.txtNewImage": "New image", "Common.Views.ListSettingsDialog.txtNone": "Ingen", "Common.Views.ListSettingsDialog.txtOfText": "% af tekst", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Start ved", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtType": "Type", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "Luk fil", "Common.Views.OpenDialog.txtEncoding": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON> er <PERSON>ert", "Common.Views.OpenDialog.txtOpenFile": "Angiv en adgangskode for at åbne filen", "Common.Views.OpenDialog.txtPassword": "Kodeord", "Common.Views.OpenDialog.txtProtected": "Når du først indtaster adgangskoden og åbner filen, nulstilles den aktuelle adgangskode til filen.", "Common.Views.OpenDialog.txtTitle": "Vælg %1 indstillinger", "Common.Views.OpenDialog.txtTitleProtected": "Beskyttet fil", "Common.Views.PasswordDialog.txtDescription": "Indstil et kodeord for at beskytte dette dokument", "Common.Views.PasswordDialog.txtIncorrectPwd": "Bekræftelsesadgangskode er ikke identisk", "Common.Views.PasswordDialog.txtPassword": "Kodeord", "Common.Views.PasswordDialog.txtRepeat": "Gentag kodeord", "Common.Views.PasswordDialog.txtTitle": "Indstil kodeord", "Common.Views.PasswordDialog.txtWarning": "Advarsel! Hvis du mister eller glem<PERSON> ad<PERSON>, kan den ikke genoprettes. Opbevar den et sikkert sted.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Tilføjelsesprogrammer", "Common.Views.Plugins.strPlugins": "Tilføjelsesprogrammer", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "stop", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "Krypter med adgangskode", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON> eller slet kodeord", "Common.Views.Protection.hintSignature": "Tilføj digital underskrift eller underskiftslinje", "Common.Views.Protection.txtAddPwd": "Tilføj kodeord", "Common.Views.Protection.txtChangePwd": "Skrift kodeord", "Common.Views.Protection.txtDeletePwd": "Slet kodeord", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "Tilføj digital underskift", "Common.Views.Protection.txtSignature": "Underskrift", "Common.Views.Protection.txtSignatureLine": "Tilføj underskriftslinje", "Common.Views.RecentFiles.txtOpenRecent": "Open Recent", "Common.Views.RenameDialog.textName": "Filnavn", "Common.Views.RenameDialog.txtInvalidName": "Filnavnet må ikke indeholde nogle af følgende tegn:", "Common.Views.ReviewChanges.hintNext": "Til næste ændring", "Common.Views.ReviewChanges.hintPrev": "Til forrige æ<PERSON>", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Real-time co-editing. All changes are saved automatically.", "Common.Views.ReviewChanges.strStrict": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strStrictDesc": "Brug 'Gem' knappen til at synkronisere de ændringer, du og andre gør", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accept<PERSON><PERSON> nuvæ<PERSON> æ<PERSON>", "Common.Views.ReviewChanges.tipCoAuthMode": "Indstil samredigeringsfunktion", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON><PERSON><PERSON> kommentarer", "Common.Views.ReviewChanges.tipCommentRemCurrent": "<PERSON>jern nuværende kommentarer", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON> kommentarer", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "<PERSON><PERSON>s aktuelle kommentarer", "Common.Views.ReviewChanges.tipHistory": "Vis version historik", "Common.Views.ReviewChanges.tipRejectCurrent": "A<PERSON>vis nuværende ændring", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "<PERSON><PERSON><PERSON><PERSON> den tilstand, du vil have, at ændringerne skal vises", "Common.Views.ReviewChanges.tipSetDocLang": "Vælg dokument sprog", "Common.Views.ReviewChanges.tipSetSpelling": "Stavekontrol", "Common.Views.ReviewChanges.tipSharing": "<PERSON><PERSON><PERSON><PERSON> adgangsrettigheder for dokument", "Common.Views.ReviewChanges.txtAccept": "Accepter", "Common.Views.ReviewChanges.txtAcceptAll": "Accept<PERSON><PERSON> alle ænd<PERSON>er", "Common.Views.ReviewChanges.txtAcceptChanges": "Accepter æ<PERSON>", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accept<PERSON><PERSON> nuvæ<PERSON> æ<PERSON>", "Common.Views.ReviewChanges.txtChat": "Snak", "Common.Views.ReviewChanges.txtClose": "Luk", "Common.Views.ReviewChanges.txtCoAuthMode": "Fællesredigeringstilstand", "Common.Views.ReviewChanges.txtCommentRemAll": "<PERSON><PERSON><PERSON> alle kommentarer", "Common.Views.ReviewChanges.txtCommentRemCurrent": "<PERSON>jern nuværende kommentarer", "Common.Views.ReviewChanges.txtCommentRemMy": "Fjern mine kommentarer", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Fjern mine nuværende kommentarer", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON> alle kommentarer", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "<PERSON><PERSON>s aktuelle kommentarer", "Common.Views.ReviewChanges.txtCommentResolveMy": "Løs mine kommentarer", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Løs mine aktuelle kommentarer", "Common.Views.ReviewChanges.txtDocLang": "Sp<PERSON>", "Common.Views.ReviewChanges.txtFinal": "Alle ændringer accepteret (Forhåndsvisning)", "Common.Views.ReviewChanges.txtFinalCap": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtHistory": "Version historik", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> <PERSON> (redigering)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON> <PERSON> a<PERSON> (forhåndsvisning)", "Common.Views.ReviewChanges.txtOriginalCap": "Orginal", "Common.Views.ReviewChanges.txtPrev": "Til forrige æ<PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "A<PERSON>vis alle ændringer", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "A<PERSON>vis nuværende ændring", "Common.Views.ReviewChanges.txtSharing": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "Stavekontrol", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "Visningstilstand", "Common.Views.ReviewPopover.textAdd": "Tilføj", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "Luk", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+mention vil give adgang til dokumentet og sende en e-mail", "Common.Views.ReviewPopover.textMentionNotify": "+mention vil notificere brugeren via e-mail", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON> igen", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "You have no permission to reopen the comment", "Common.Views.ReviewPopover.txtDeleteTip": "Slet", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "Map<PERSON> til at gemme", "Common.Views.SearchPanel.textCaseSensitive": "Case sensitive", "Common.Views.SearchPanel.textCloseSearch": "Close find", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "Find", "Common.Views.SearchPanel.textFindAndReplace": "Find and replace", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} items successfully replaced.", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} items replaced. Remaining {2} items are locked by other users.", "Common.Views.SearchPanel.textReplace": "Replace", "Common.Views.SearchPanel.textReplaceAll": "Replace All", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "Search has stopped", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textTooManyResults": "There are too many results to show here", "Common.Views.SearchPanel.textWholeWords": "Whole words only", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "Previous result", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "Vælg datakilde", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Fed", "Common.Views.SignDialog.textCertificate": "Cerfitikant", "Common.Views.SignDialog.textChange": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "Input signer navn", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Underskrivernavn må ikke være tomt.", "Common.Views.SignDialog.textPurpose": "Form<PERSON>l med at underskrive dette dokument", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSignature": "Signaturen ser ud som", "Common.Views.SignDialog.textTitle": "Tegn dokument", "Common.Views.SignDialog.textUseImage": "eller klik på 'Vælg billede' for at bruge et billede som signatur", "Common.Views.SignDialog.textValid": "Gælder fra% 1 til% 2", "Common.Views.SignDialog.tipFontName": "Skrifttypenavn", "Common.Views.SignDialog.tipFontSize": "Skrift størrelse", "Common.Views.SignSettingsDialog.textAllowComment": "Tillad underskrier at tilføje en kommentar i underskrift dialogen", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "Email", "Common.Views.SignSettingsDialog.textInfoName": "Navn", "Common.Views.SignSettingsDialog.textInfoTitle": "Signer titel", "Common.Views.SignSettingsDialog.textInstructions": "Instruktioner for underskriver", "Common.Views.SignSettingsDialog.textShowDate": "Vis tegndato i signaturlinjen", "Common.Views.SignSettingsDialog.textTitle": "underskrifts opsætning", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON> felt er nødvendigt", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX-værdi", "Common.Views.SymbolTableDialog.textCopyright": "Ophavsret Symbol", "Common.Views.SymbolTableDialog.textDCQuote": "Afsluttende citationstegn", "Common.Views.SymbolTableDialog.textDOQuote": "Åbningscitationstegn", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON><PERSON><PERSON> ellipse", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "Em mellemrum", "Common.Views.SymbolTableDialog.textEnDash": "En bindestreg", "Common.Views.SymbolTableDialog.textEnSpace": "En mellemrum", "Common.Views.SymbolTableDialog.textFont": "Skrifttype", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON><PERSON> bind<PERSON>g", "Common.Views.SymbolTableDialog.textNBSpace": "Ingen-brud mellemrum", "Common.Views.SymbolTableDialog.textPilcrow": "Afsnitstegn", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em mellemrum", "Common.Views.SymbolTableDialog.textRange": "Rækkevidde", "Common.Views.SymbolTableDialog.textRecent": "Senest anvendte symboler", "Common.Views.SymbolTableDialog.textRegistered": "Regis<PERSON>ret tegn", "Common.Views.SymbolTableDialog.textSCQuote": "Afsluttende enkelt citationstegn", "Common.Views.SymbolTableDialog.textSection": "Sektion tegn", "Common.Views.SymbolTableDialog.textShortcut": "Genvejstast", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Åbningscitationstegn enkelt", "Common.Views.SymbolTableDialog.textSpecial": "Specielle tegn", "Common.Views.SymbolTableDialog.textSymbols": "Symboler", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Varemærketegn", "Common.Views.UserNameDialog.textDontShow": "<PERSON><PERSON><PERSON><PERSON> mig ikke igen", "Common.Views.UserNameDialog.textLabel": "Mærkat:", "Common.Views.UserNameDialog.textLabelError": "Mærkat kan ikke være blank.", "PE.Controllers.DocumentHolder.textLongName": "Enter a name that is less than 255 characters.", "PE.Controllers.DocumentHolder.textNameLayout": "Layout name", "PE.Controllers.DocumentHolder.textNameMaster": "Master name", "PE.Controllers.DocumentHolder.textRenameTitleLayout": "<PERSON><PERSON>out", "PE.Controllers.DocumentHolder.textRenameTitleMaster": "<PERSON><PERSON>", "PE.Controllers.LeftMenu.leavePageText": "Alle ikke-gemte ændringer i dette dokument vil gå tabt.<br> <PERSON><PERSON> på \"<PERSON><PERSON>er\" og derefter på \"Gem\" for at gemme dem. <PERSON>lik på \"OK\" for at kassere alle ikke-gemte ændringer.", "PE.Controllers.LeftMenu.newDocumentTitle": "Namnløs præsentation", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.LeftMenu.requestEditRightsText": "Anmoder om redigeringsrettigheder...", "PE.Controllers.LeftMenu.textLoadHistory": "Indlæser versionshistorik...", "PE.Controllers.LeftMenu.textNoTextFound": "<PERSON>en du har søgt, kunne ikke findes. Venligst ændre dine søgerkriterier.", "PE.Controllers.LeftMenu.textReplaceSkipped": "Erstatningen er blevet oprettet. {0} gentagelser blev sprunget over.", "PE.Controllers.LeftMenu.textReplaceSuccess": "Søgningen er blevet gennemført. Forekomster erstattet: {0}", "PE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "PE.Controllers.LeftMenu.txtUntitled": "Unavngivet", "PE.Controllers.Main.applyChangesTextText": "Indlæser data...", "PE.Controllers.Main.applyChangesTitleText": "Indlæser data", "PE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "PE.Controllers.Main.convertationTimeoutText": "Konverteringstidsfrist er overskredet", "PE.Controllers.Main.criticalErrorExtText": "<PERSON>k \"OK\" for at vende tilbage til dokumentlisten", "PE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.downloadErrorText": "Download fejlet.", "PE.Controllers.Main.downloadTextText": "<PERSON><PERSON> præsentation...", "PE.Controllers.Main.downloadTitleText": "<PERSON><PERSON> præsentation", "PE.Controllers.Main.errorAccessDeny": "<PERSON> at foretage en handling, som du ikke har rettighederne til.<br>venligst kontakt din Document Servar administrator.", "PE.Controllers.Main.errorBadImageUrl": "Billede URL er forkert", "PE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the presentation.", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Server forbindelse tabt. Dokumentet kan ikke redigeres lige nu.", "PE.Controllers.Main.errorComboSeries": "For at lave et kombinationsdiagram, vælg mindst to serier med data.", "PE.Controllers.Main.errorConnectToServer": "Dokumentet kunne ikke gemmes. Check venligst din netværksforbindelse eller kontakt din administrator.<br><PERSON><PERSON><PERSON> du klikker på 'OK' knappen, vil du blive bedt om at downloade dokumentet.", "PE.Controllers.Main.errorDatabaseConnection": "Ekstern fejl.<br>Database forbindelses fejl. Kontakt venligst support hvis fejlen bliver ved med at være der. ", "PE.Controllers.Main.errorDataEncrypted": "<PERSON><PERSON><PERSON><PERSON><PERSON> ændringer er blevet modtaget, men de kan ikke dekrypteres. ", "PE.Controllers.Main.errorDataRange": "<PERSON>ert datainterval", "PE.Controllers.Main.errorDefaultMessage": "Fejlkode: %1", "PE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "PE.Controllers.Main.errorEditingDownloadas": "Der opstod en fejl under arbejdet med dokumentet. <br> Brug \"download som\" valgmuligheden for at gemme en sikkerhedsversion til din computers harddisk.", "PE.Controllers.Main.errorEditingSaveas": "Der opstod en fejl under arbejdet med dokumentet. <br> Brug \"gem som...\" valg<PERSON>ligheden for at gemme en sikkerhedsversion til din computers harddisk.", "PE.Controllers.Main.errorEmailClient": "Ingen e-mail klient fundet.", "PE.Controllers.Main.errorFilePassProtect": "Dokumentet er beskyttet af et kodeord og kunne ikke åbnes.", "PE.Controllers.Main.errorFileSizeExceed": "<PERSON><PERSON> stø<PERSON>se overgår begrænsningen, som er sat for din server.<br>Kontakt venligst til dokumentserver administrator for detaljer.", "PE.Controllers.Main.errorForceSave": "Der skete en fejl under gemning af filen. Brug venligst 'Download som' for at gemme filen på din computers harddisk eller prøv igen senere.", "PE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "PE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "PE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "PE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "PE.Controllers.Main.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "PE.Controllers.Main.errorKeyEncrypt": "Ukendte nøgle descriptor", "PE.Controllers.Main.errorKeyExpire": "Nøgle beskrivelse udløbet", "PE.Controllers.Main.errorLoadingFont": "Skrifttyper er ikke indlæst.<br>Kontakt din dokument server administrator.", "PE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON> ikke gemme", "PE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "PE.Controllers.Main.errorServerVersion": "Redigeringsversionen er blevet opdatere. Siden vil blive genindlæst for at anvende ændringerne. ", "PE.Controllers.Main.errorSessionAbsolute": "Sessionen for dokumentredigering er udløbet. Genindlæs venligst siden. ", "PE.Controllers.Main.errorSessionIdle": "Dokumentet er ikke blevet redigeret i et stykke tid. Genindlæs venligst siden.", "PE.Controllers.Main.errorSessionToken": "Forbindelsen til serveren er blevet afbrudt. Venligst genindlæs siden.", "PE.Controllers.Main.errorSetPassword": "Kodeord kunne ikke gemmes.", "PE.Controllers.Main.errorStockChart": "<PERSON><PERSON> r<PERSON>ø<PERSON>. For at bygge et aktiediagram placer dataen på arket i følgende orden:<br><PERSON><PERSON><PERSON><PERSON><PERSON>, maks pris, min. pris, lukke pris. ", "PE.Controllers.Main.errorToken": "Dokumentets sikkerhedstoken er ikke lavet korrekt.<br> Kontakt venligst din administrator på Document Server.", "PE.Controllers.Main.errorTokenExpire": "Dokumentets sikkerhedstoken er udløbet.<br>Kontakt venligst din administrator på Document Server. ", "PE.Controllers.Main.errorUpdateVersion": "Filversionen er blevet ændret. Siden vil blive genindlæst.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Internetforbindelsen er blevet genoprettet, og filversionen er blevet ændret.<br><PERSON><PERSON><PERSON> du kan fortsætte arbejdet, skal du hente filen eller kopiere indholdet for at sikre, at intet vil blive tabt - og derefter genindlæse denne side.", "PE.Controllers.Main.errorUserDrop": "Der kan ikke opnås adgang til filen lige nu. ", "PE.Controllers.Main.errorUsersExceed": "Det maksimale antal af brugere tilladt i din aftale er nået. ", "PE.Controllers.Main.errorViewerDisconnect": "Forbindesen er tabt. Du kan stadig se dokumentet, <br> men du vil ikke være i stand til at downloade eller udskrive det før forbindelsen er genetableret. ", "PE.Controllers.Main.leavePageText": "Du har ikke-gemte ændringer i denne præsentation. Klik på \"Bliv på denne side\", og derefter \"Gem\" for at gemme dem. Klik på \"Forlad denne side\" for at afvise alle de ikke gemte ændringer.", "PE.Controllers.Main.leavePageTextOnClose": "Alle ikke-gemte ændringer i denne præsentation vil gå tabt.<br> <PERSON><PERSON> på \"<PERSON><PERSON><PERSON>\" og derefter på \"Gem\" for at gemme dem. Klik på \"OK\" for at kassere alle ikke-gemte ændringer.", "PE.Controllers.Main.loadFontsTextText": "Indlæser data...", "PE.Controllers.Main.loadFontsTitleText": "Indlæser data", "PE.Controllers.Main.loadFontTextText": "Indlæser data...", "PE.Controllers.Main.loadFontTitleText": "Indlæser data", "PE.Controllers.Main.loadImagesTextText": "In<PERSON><PERSON><PERSON> billeder...", "PE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON><PERSON><PERSON>er", "PE.Controllers.Main.loadImageTextText": "Indlæser billede...", "PE.Controllers.Main.loadImageTitleText": "Indlæser billede", "PE.Controllers.Main.loadingDocumentTextText": "Indlæser præsentation ...", "PE.Controllers.Main.loadingDocumentTitleText": "Indlæser præsentation", "PE.Controllers.Main.loadThemeTextText": "Indlæser tema...", "PE.Controllers.Main.loadThemeTitleText": "Indl<PERSON>ser tema", "PE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.openErrorText": "Der skete en fejl under åbningen af filen.", "PE.Controllers.Main.openTextText": "Åbning Præsentation...", "PE.Controllers.Main.openTitleText": "Åbning Præsentation", "PE.Controllers.Main.printTextText": "Udskrivning Præsentation...", "PE.Controllers.Main.printTitleText": "Udskrivning Præsentation", "PE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON> siden", "PE.Controllers.Main.requestEditFailedMessageText": "Nogen redigerer denne præsentation lige nu. Prøv igen senere.", "PE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON> næ<PERSON>", "PE.Controllers.Main.saveErrorText": "Der skete en fejl da filen blev forsøgt gemt.", "PE.Controllers.Main.saveErrorTextDesktop": "Filen kan ikke gemmes eller oprettes.<br><PERSON><PERSON><PERSON> årsager kan være:<br>1. <PERSON><PERSON> er read-only. <br>2. <PERSON><PERSON> and en anden bruger.<br>3. <PERSON>sken er fuld eller beskadiget.", "PE.Controllers.Main.saveTextText": "G<PERSON><PERSON> præsentation...", "PE.Controllers.Main.saveTitleText": "Gemmer præsentation", "PE.Controllers.Main.scriptLoadError": "<PERSON><PERSON><PERSON><PERSON> er for langsom, nogle af komponenterne kunne ikke indlæses. Venligst genindlæs siden.", "PE.Controllers.Main.splitDividerErrorText": "Antallet af rækker skal vi deleligt med %1.", "PE.Controllers.Main.splitMaxColsErrorText": "Antallet af kolonner skal være mindre end %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "Antallet af rækker skal være mindre end %1.", "PE.Controllers.Main.textAnonymous": "Anonym", "PE.Controllers.Main.textApplyAll": "<PERSON><PERSON><PERSON> på alle ligninger", "PE.Controllers.Main.textBuyNow": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "PE.Controllers.Main.textChangesSaved": "Alle ændringer gemt", "PE.Controllers.Main.textClose": "Luk", "PE.Controllers.Main.textCloseTip": "<PERSON><PERSON> for at lukke tippet", "PE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "PE.Controllers.Main.textContactUs": "Kontakt salg", "PE.Controllers.Main.textContinue": "Continue", "PE.Controllers.Main.textConvertEquation": "Denne ligning er skabt med en ældre version af programmet, som ikke længere understøttes. Omdannelse af denne ligning til Office Math ML format vil gøre den redigérbar.<br>Ønsker du at omdanne?", "PE.Controllers.Main.textCustomLoader": "Be<PERSON>ærk, at du i henhold til licensbetingelserne ikke har ret til at skifte loaderen.<br>Kontakt venligt vores salgsafdeling for at få en kvote.", "PE.Controllers.Main.textDisconnect": "Forbindelsen er afbrudt", "PE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textHasMacros": "Filen indeholder makroer.<br><PERSON><PERSON><PERSON> <PERSON> at køre makroer?", "PE.Controllers.Main.textLearnMore": "<PERSON><PERSON><PERSON> mere", "PE.Controllers.Main.textLoadingDocument": "Indlæser præsentation", "PE.Controllers.Main.textLongName": "Indtast et navn på mindre end 128 bogstaver.", "PE.Controllers.Main.textNoLicenseTitle": "Licensbegrænsning nået", "PE.Controllers.Main.textObject": "Object", "PE.Controllers.Main.textPaidFeature": "Betalt funktion", "PE.Controllers.Main.textReconnect": "Connection is restored", "PE.Controllers.Main.textRemember": "Husk mit valg til alle filer", "PE.Controllers.Main.textRememberMacros": "Remember my choice for all macros", "PE.Controllers.Main.textRenameError": "Brugernavn må ikke være blankt.", "PE.Controllers.Main.textRenameLabel": "Indtast et navn til brug ved samarbejde", "PE.Controllers.Main.textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "PE.Controllers.Main.textShape": "Form", "PE.Controllers.Main.textStrict": "<PERSON><PERSON><PERSON> tilstand", "PE.Controllers.Main.textText": "Tekst", "PE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "PE.Controllers.Main.textTryUndoRedo": "<PERSON><PERSON>d funktionen er blevet slået fra i den hurtige co-redigerngstilstand.<b>Tryk på 'Striks tilstand' knappen for at skifte til den strikse co-redigeringstilstand for at redigere filen uden at andre brugere kan foretage ændringer før efter du har gemt dem. Du kan skifte i mellem co-redigeringstilstanden ved at bruge de avancerede indstillinger. ", "PE.Controllers.Main.textTryUndoRedoWarn": "<PERSON><PERSON>d/omgør-funktionen er deaktiveret for hurtig medredigeringstilstand.", "PE.Controllers.Main.textUndo": "Undo", "PE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "PE.Controllers.Main.textUpdating": "Updating", "PE.Controllers.Main.titleLicenseExp": "Licens er udløbet", "PE.Controllers.Main.titleLicenseNotActive": "License not active", "PE.Controllers.Main.titleServerVersion": "Redigeringsværktøj opdateret", "PE.Controllers.Main.titleUpdateVersion": "Version changed", "PE.Controllers.Main.txtAddFirstSlide": "klik for at tilføje første dias", "PE.Controllers.Main.txtAddNotes": "Tryk for at tilføje notater", "PE.Controllers.Main.txtAnimationPane": "Animation Pane", "PE.Controllers.Main.txtArt": "<PERSON> tekst her", "PE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> former", "PE.Controllers.Main.txtButtons": "<PERSON>nap<PERSON>", "PE.Controllers.Main.txtCallouts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtCharts": "Diagrammer", "PE.Controllers.Main.txtClipArt": "Multimedieklip", "PE.Controllers.Main.txtDateTime": "<PERSON>to og tid", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Diagram titel", "PE.Controllers.Main.txtEditingMode": "<PERSON><PERSON><PERSON><PERSON> redigeringstilstand...", "PE.Controllers.Main.txtEnd": "End: ${0}s", "PE.Controllers.Main.txtErrorLoadHistory": "Fejl ved indlæsningen af historik", "PE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON> figure", "PE.Controllers.Main.txtFirstSlide": "First slide", "PE.Controllers.Main.txtFooter": "Sidefod", "PE.Controllers.Main.txtHeader": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtImage": "<PERSON><PERSON>", "PE.Controllers.Main.txtLastSlide": "Last slide", "PE.Controllers.Main.txtLines": "<PERSON><PERSON>", "PE.Controllers.Main.txtLoading": "Indlæser...", "PE.Controllers.Main.txtLoop": "Loop: ${0}s", "PE.Controllers.Main.txtMath": "Matematik", "PE.Controllers.Main.txtMedia": "Me<PERSON>", "PE.Controllers.Main.txtNeedSynchronize": "<PERSON> har op<PERSON>ringer", "PE.Controllers.Main.txtNextSlide": "Next slide", "PE.Controllers.Main.txtNone": "ingen", "PE.Controllers.Main.txtPicture": "<PERSON><PERSON>", "PE.Controllers.Main.txtPlayAll": "Play All", "PE.Controllers.Main.txtPlayFrom": "Play From", "PE.Controllers.Main.txtPlaySelected": "Play Selected", "PE.Controllers.Main.txtPrevSlide": "Previous slide", "PE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "PE.Controllers.Main.txtScheme_Aspect": "Aspect", "PE.Controllers.Main.txtScheme_Blue": "Blue", "PE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "PE.Controllers.Main.txtScheme_Blue_II": "Blue II", "PE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "PE.Controllers.Main.txtScheme_Grayscale": "Gråtoner", "PE.Controllers.Main.txtScheme_Green": "Green", "PE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "PE.Controllers.Main.txtScheme_Marquee": "Marquee", "PE.Controllers.Main.txtScheme_Median": "Median", "PE.Controllers.Main.txtScheme_Office": "Office", "PE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "PE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "PE.Controllers.Main.txtScheme_Orange": "Orange", "PE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "PE.Controllers.Main.txtScheme_Paper": "Paper", "PE.Controllers.Main.txtScheme_Red": "Red", "PE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "PE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "PE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "PE.Controllers.Main.txtScheme_Violet": "Violet", "PE.Controllers.Main.txtScheme_Violet_II": "Violet II", "PE.Controllers.Main.txtScheme_Yellow": "Yellow", "PE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "PE.Controllers.Main.txtSeries": "Serie", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Linje talebobbel 1 (Omrids og Accent Bar)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Linje talebobbel 2 (Omrids og Accent Bar)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Linje talebobbel 3 (O<PERSON>rids og Accent Bar)", "PE.Controllers.Main.txtShape_accentCallout1": "Linje talebobbel 1 (Accent Bar)", "PE.Controllers.Main.txtShape_accentCallout2": "<PERSON>je <PERSON> 2 (Accent Bar)", "PE.Controllers.Main.txtShape_accentCallout3": "Linje talebo<PERSON>l 3 (Accent Bar)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Tilbage eller <PERSON>e knap", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Begyndende knap", "PE.Controllers.Main.txtShape_actionButtonBlank": "Blank knap", "PE.Controllers.Main.txtShape_actionButtonDocument": "Dokument knap", "PE.Controllers.Main.txtShape_actionButtonEnd": "Slut knap", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON>em eller næste-knap", "PE.Controllers.Main.txtShape_actionButtonHelp": "Hjælp-knap", "PE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON>-knap", "PE.Controllers.Main.txtShape_actionButtonInformation": "Informations-knap", "PE.Controllers.Main.txtShape_actionButtonMovie": "Film-knap", "PE.Controllers.Main.txtShape_actionButtonReturn": "Tilbage-knap", "PE.Controllers.Main.txtShape_actionButtonSound": "Lyd-knap", "PE.Controllers.Main.txtShape_arc": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON> pil", "PE.Controllers.Main.txtShape_bentConnector5": "Albue-forbindelse", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Albue-pil forbindelse", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Albue dobbelt-pil forbindelse", "PE.Controllers.Main.txtShape_bentUpArrow": "Pil buet opad", "PE.Controllers.Main.txtShape_bevel": "Facet", "PE.Controllers.Main.txtShape_blockArc": "Blokeringsbue", "PE.Controllers.Main.txtShape_borderCallout1": "Linje Talebobbel 1", "PE.Controllers.Main.txtShape_borderCallout2": "Linje <PERSON>l 2", "PE.Controllers.Main.txtShape_borderCallout3": "Linje <PERSON>bo<PERSON>l 3", "PE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON><PERSON> bø<PERSON>", "PE.Controllers.Main.txtShape_callout1": "Linje Talebobbel 1 (Ingen grænse)", "PE.Controllers.Main.txtShape_callout2": "Linje Talebobbel 2 (Ingen Grænse)", "PE.Controllers.Main.txtShape_callout3": "Linje Talebobbel 3 (<PERSON>gen grænse)", "PE.Controllers.Main.txtShape_can": "<PERSON>n", "PE.Controllers.Main.txtShape_chevron": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_chord": "Akkord", "PE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> pil", "PE.Controllers.Main.txtShape_cloud": "Sky", "PE.Controllers.Main.txtShape_cloudCallout": "<PERSON><PERSON><PERSON><PERSON> (Sky)", "PE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_cube": "Terning", "PE.Controllers.Main.txtShape_curvedConnector3": "<PERSON><PERSON>t konnektor", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "<PERSON><PERSON>t pil med konnektor", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "<PERSON><PERSON><PERSON> dobbelt-pil", "PE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON><PERSON><PERSON>e pil", "PE.Controllers.Main.txtShape_curvedLeftArrow": "<PERSON>uet pil til venstre", "PE.Controllers.Main.txtShape_curvedRightArrow": "<PERSON><PERSON>t pil til højre", "PE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON><PERSON>t til opad", "PE.Controllers.Main.txtShape_decagon": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_diagStripe": "Diagonal stribe", "PE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_dodecagon": "Tolvkant", "PE.Controllers.Main.txtShape_donut": "Donut", "PE.Controllers.Main.txtShape_doubleWave": "Dobbeltbølge", "PE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_downArrowCallout": "Talebobbel (Pil ned)", "PE.Controllers.Main.txtShape_ellipse": "Ellipse", "PE.Controllers.Main.txtShape_ellipseRibbon": "<PERSON><PERSON><PERSON>e sløjfe", "PE.Controllers.Main.txtShape_ellipseRibbon2": "<PERSON><PERSON><PERSON> sløjfe opad", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Flowdiagram: Alternativ process", "PE.Controllers.Main.txtShape_flowChartCollate": "Flowdiagram: <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartConnector": "Flowdiagram: Forbind", "PE.Controllers.Main.txtShape_flowChartDecision": "Flowdiagram: Valg", "PE.Controllers.Main.txtShape_flowChartDelay": "Flowdiagram: Forsink", "PE.Controllers.Main.txtShape_flowChartDisplay": "Flowdiagram: Vis", "PE.Controllers.Main.txtShape_flowChartDocument": "Flowdiagram: Dokument", "PE.Controllers.Main.txtShape_flowChartExtract": "Flowdiagram: Udtræk", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Flowdiagram: Data", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Flowdiagram: <PERSON>n opbevaring", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flowdiagram: Magnetisk Disk", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flowdiagram: <PERSON><PERSON><PERSON> adgang opbevaring", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Flowdiagram: Opbevaring af sekventiel adgang", "PE.Controllers.Main.txtShape_flowChartManualInput": "Flowdiagram: Manuelt Input", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Flowdiagram: Manuel <PERSON>", "PE.Controllers.Main.txtShape_flowChartMerge": "Flowdiagram: Sammenflet", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Flowdiagram: Multidokument", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flowdiagram: Af-side forbindelse", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flowdiagram: Opbevaret Data", "PE.Controllers.Main.txtShape_flowChartOr": "Flowdiagram: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flowdiagram: Forudbestemt Process", "PE.Controllers.Main.txtShape_flowChartPreparation": "Flowdiagram: Forberedelse", "PE.Controllers.Main.txtShape_flowChartProcess": "Flowdiagram: Process", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Flowdiagram: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Flowdiagram: <PERSON><PERSON> b<PERSON>", "PE.Controllers.Main.txtShape_flowChartSort": "Flowdiagram: Sorter", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Flowdiagram: <PERSON><PERSON><PERSON><PERSON><PERSON> knudepunkt", "PE.Controllers.Main.txtShape_flowChartTerminator": "Flowdiagram: Terminator", "PE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_frame": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_halfFrame": "<PERSON>v ramme", "PE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_heptagon": "Syvkant", "PE.Controllers.Main.txtShape_hexagon": "Sekskant", "PE.Controllers.Main.txtShape_homePlate": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_horizontalScroll": "<PERSON>dret rul", "PE.Controllers.Main.txtShape_irregularSeal1": "Eksplosion 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Eksplosion 2", "PE.Controllers.Main.txtShape_leftArrow": "Venstre pil", "PE.Controllers.Main.txtShape_leftArrowCallout": "Talebobbel (Pil venstre)", "PE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON><PERSON> bø<PERSON>", "PE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON> para<PERSON>", "PE.Controllers.Main.txtShape_leftRightArrow": "<PERSON><PERSON><PERSON> højre pil", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Talebobbel (Pil højre/venstre)", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Venstre-højre-op pil", "PE.Controllers.Main.txtShape_leftUpArrow": "Venstre-op pil", "PE.Controllers.Main.txtShape_lightningBolt": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "<PERSON>l", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "Dobbeltpil", "PE.Controllers.Main.txtShape_mathDivide": "Opdeling", "PE.Controllers.Main.txtShape_mathEqual": "Lig med", "PE.Controllers.Main.txtShape_mathMinus": "Minus", "PE.Controllers.Main.txtShape_mathMultiply": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathNotEqual": "Ikke lig", "PE.Controllers.Main.txtShape_mathPlus": "Plus", "PE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_noSmoking": "\"Nej\" symbol", "PE.Controllers.Main.txtShape_notchedRightArrow": "Hakket højre-pil", "PE.Controllers.Main.txtShape_octagon": "Ottekant", "PE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "PE.Controllers.Main.txtShape_pentagon": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_pie": "Cirkeldiagram", "PE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_plus": "Plus", "PE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_polyline2": "Fri form", "PE.Controllers.Main.txtShape_quadArrow": "Firedobbelt pil", "PE.Controllers.Main.txtShape_quadArrowCallout": "Talebobbel (Firedobbelt pil)", "PE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_ribbon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_ribbon2": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON><PERSON> pil", "PE.Controllers.Main.txtShape_rightArrowCallout": "Talebobbel (Højre pil)", "PE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_round1Rect": "Rund et-hjørnet rektangel", "PE.Controllers.Main.txtShape_round2DiagRect": "Rund diagonal hjø<PERSON> rektangel", "PE.Controllers.Main.txtShape_round2SameRect": "Rund samme-sidet hjørnerektangel", "PE.Controllers.Main.txtShape_roundRect": "<PERSON><PERSON><PERSON><PERSON> med rundt hjørne", "PE.Controllers.Main.txtShape_rtTriangle": "<PERSON><PERSON><PERSON><PERSON> trekant", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_snip1Rect": "<PERSON><PERSON> enkelt hjørne rektangel", "PE.Controllers.Main.txtShape_snip2DiagRect": "Klip <PERSON> hjø<PERSON> rektangel", "PE.Controllers.Main.txtShape_snip2SameRect": "Klip samme-side hjørnet rektangel", "PE.Controllers.Main.txtShape_snipRoundRect": "K<PERSON> og rundt et-hjørnet rektangel", "PE.Controllers.Main.txtShape_spline": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "10-Point Stjerne", "PE.Controllers.Main.txtShape_star12": "12-Points Stjerne", "PE.Controllers.Main.txtShape_star16": "16-Points Stjerne", "PE.Controllers.Main.txtShape_star24": "24-Points Stjerne", "PE.Controllers.Main.txtShape_star32": "32-Points Stjerne", "PE.Controllers.Main.txtShape_star4": "4-Points Stjerne", "PE.Controllers.Main.txtShape_star5": "5-Points Stjerne", "PE.Controllers.Main.txtShape_star6": "6-Points Stjerne", "PE.Controllers.Main.txtShape_star7": "7-Points Stjerne", "PE.Controllers.Main.txtShape_star8": "8-Points Stjerne", "PE.Controllers.Main.txtShape_stripedRightArrow": "<PERSON><PERSON><PERSON> hø<PERSON>-pil", "PE.Controllers.Main.txtShape_sun": "Sol", "PE.Controllers.Main.txtShape_teardrop": "Dr<PERSON><PERSON>", "PE.Controllers.Main.txtShape_textRect": "Tekstboks", "PE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_triangle": "Trekant", "PE.Controllers.Main.txtShape_upArrow": "Op pil", "PE.Controllers.Main.txtShape_upArrowCallout": "Talebobbel (Pil op)", "PE.Controllers.Main.txtShape_upDownArrow": "Op-ned pil", "PE.Controllers.Main.txtShape_uturnArrow": "U-vendings pil", "PE.Controllers.Main.txtShape_verticalScroll": "Lodret rul", "PE.Controllers.Main.txtShape_wave": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval Talebobbel ", "PE.Controllers.Main.txtShape_wedgeRectCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "<PERSON><PERSON><PERSON><PERSON> Rektangulær <PERSON> ", "PE.Controllers.Main.txtSldLtTBlank": "Blank", "PE.Controllers.Main.txtSldLtTChart": "Diagram", "PE.Controllers.Main.txtSldLtTChartAndTx": "Diagram og tekst", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Multimedieklip og tekst", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Multimedieklip og lodret tekst", "PE.Controllers.Main.txtSldLtTCust": "brugerdefinerede", "PE.Controllers.Main.txtSldLtTDgm": "Diagram", "PE.Controllers.Main.txtSldLtTFourObj": "Fire objekter", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Medier og tekst", "PE.Controllers.Main.txtSldLtTObj": "Titel og objekt", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Objekt og to objekter", "PE.Controllers.Main.txtSldLtTObjAndTx": "objekt og tekst", "PE.Controllers.Main.txtSldLtTObjOnly": "Genstand", "PE.Controllers.Main.txtSldLtTObjOverTx": "Objekt over tekst", "PE.Controllers.Main.txtSldLtTObjTx": "Titel, Objekt og Undertekst", "PE.Controllers.Main.txtSldLtTPicTx": "<PERSON><PERSON> og Billedtekst", "PE.Controllers.Main.txtSldLtTSecHead": "Sekt<PERSON> Header", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitle": "Titel", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON>n titel", "PE.Controllers.Main.txtSldLtTTwoColTx": "To kolonne tekst", "PE.Controllers.Main.txtSldLtTTwoObj": "To objekter", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "To objekter og objekt", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "To objekter og tekst", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "To objekt<PERSON> over tekst", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "To tekst og to objekter", "PE.Controllers.Main.txtSldLtTTx": "Tekst", "PE.Controllers.Main.txtSldLtTTxAndChart": "tekst og diagram", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Tekst og klip art", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Tekst og medier", "PE.Controllers.Main.txtSldLtTTxAndObj": "Tekst og objekt", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Tekst og to objekter", "PE.Controllers.Main.txtSldLtTTxOverObj": "Tekst over objekt", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Lodret titel og tekst", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Lodret titel og tekst over diagram", "PE.Controllers.Main.txtSldLtTVertTx": "Lodret tekst", "PE.Controllers.Main.txtSlideNumber": "<PERSON><PERSON> nummer", "PE.Controllers.Main.txtSlideSubtitle": "Dias undertekst", "PE.Controllers.Main.txtSlideText": "Dias tekst", "PE.Controllers.Main.txtSlideTitle": "<PERSON>as titel", "PE.Controllers.Main.txtStarsRibbons": "Stjerner og bånd", "PE.Controllers.Main.txtStart": "Start: ${0}s", "PE.Controllers.Main.txtStop": "Stop", "PE.Controllers.Main.txtTheme_basic": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_blank": "Blank", "PE.Controllers.Main.txtTheme_classic": "Klassisk", "PE.Controllers.Main.txtTheme_corner": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_dotted": "Prikket", "PE.Controllers.Main.txtTheme_green": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green_leaf": "<PERSON><PERSON><PERSON><PERSON> blad", "PE.Controllers.Main.txtTheme_lines": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office_theme": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_official": "Officiel", "PE.Controllers.Main.txtTheme_pixel": "Pixel", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Skildpadde", "PE.Controllers.Main.txtXAxis": "X akse", "PE.Controllers.Main.txtYAxis": "Y akse", "PE.Controllers.Main.txtZoom": "Zoom", "PE.Controllers.Main.unknownErrorText": "Ukendt fejl.", "PE.Controllers.Main.unsupportedBrowserErrorText": "Din browser understøttes ikke", "PE.Controllers.Main.uploadImageExtMessage": "Ukendt billedeformat.", "PE.Controllers.Main.uploadImageFileCountMessage": "Ingen billeder uploadet", "PE.Controllers.Main.uploadImageSizeMessage": "Bill<PERSON>t er for stort. Den maksimale størrelse er 25 MB.", "PE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON><PERSON><PERSON>...", "PE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.waitText": "Vent venligst...", "PE.Controllers.Main.warnBrowserIE9": "Programmet har dårlig kompatibilitet med Internet Explorer 9. Brug i stedet Internet Explorer 10 eller højere", "PE.Controllers.Main.warnBrowserZoom": "Din browsers nuværende zoom indstilling er ikke understøttet. Venligst genddan til normal forstørrelse ved at trykke Ctrl+0.", "PE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "PE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "PE.Controllers.Main.warnLicenseExceeded": "Antallet af samtidige forbindelser til serveren overstiger det tilladte antal, og dokumentet åbnes i visningstilstand.<br>Kontakt venligst din administrator for mere information.", "PE.Controllers.Main.warnLicenseExp": "Din licens er udløbet.<br>Opdater venligst din licens og genindlæs siden.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Licens udløbet. <br><PERSON> har ikke adgang til at redigere. <br>Kontakt venligst din administrator.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Licens skal fornyes.<br><PERSON> har begrænset adgang til at redigere dokumenter.<br>Kontakt venligst din administrator for at få fuld adgang.", "PE.Controllers.Main.warnLicenseUsersExceeded": "Det tilladte antal af samtidige brugere er oversteget, og dokumentet vil blive åbnet i visningstilstand.<br>Kontakt venligst din administrator for mere information. ", "PE.Controllers.Main.warnNoLicense": "Du har n<PERSON><PERSON> gr<PERSON><PERSON><PERSON> for antal samtidige forbindelser til %1 værktøjer. Dokumentet åbnes for læsning.<br>Kontakt %1 salgsteamet for betingelser for opgradering.", "PE.Controllers.Main.warnNoLicenseUsers": "<PERSON> har nå<PERSON> gr<PERSON><PERSON>n for brugere af %1 redigeringsværktøj. Kontakt %1 salgsafdeling for at høre om dine opgraderingsmuligheder.", "PE.Controllers.Main.warnProcessRightsChange": "Du er blevet nægtet rettighederne til at redigere denne fil.", "PE.Controllers.Print.txtPrintRangeInvalid": "Invalid print range", "PE.Controllers.Print.txtPrintRangeSingleRange": "Enter either a single slide number or a single slide range (for example, 5-12). Or you can Print to PDF.", "PE.Controllers.Search.notcriticalErrorTitle": "Warning", "PE.Controllers.Search.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "PE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "PE.Controllers.Search.textReplaceSuccess": "Search has been done. {0} occurrences have been replaced", "PE.Controllers.Search.warnReplaceString": "{0} is not a valid special character for the Replace With box.", "PE.Controllers.Statusbar.textDisconnect": "<b>Connection is lost</b><br>Trying to connect. Please check connection settings.", "PE.Controllers.Statusbar.zoomText": "Zoom {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "Skrifttypen du gemmer er ikke tilgængeligt på din nuværende enhed.<br>Skrifttypen vil blive vist som en af dem dit system understøtter, den gemte skrifttype vil bruge brugt når den er tilgængelig.<br>Vil du fortsætte?", "PE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "PE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "PE.Controllers.Toolbar.helpTabDesign": "Apply themes, change color schemes and slide size from the newly added Design tab.", "PE.Controllers.Toolbar.helpTabDesignHeader": "Design tab", "PE.Controllers.Toolbar.textAccent": "Accenter", "PE.Controllers.Toolbar.textBracket": "Parentes", "PE.Controllers.Toolbar.textFontSizeErr": "Den indtastede værdi er ikke korrekt.<br>Venligst indtast en numerisk værdi mellem 1 og 300", "PE.Controllers.Toolbar.textFraction": "Frak<PERSON><PERSON>", "PE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textInsert": "indsæt", "PE.Controllers.Toolbar.textIntegral": "Inte<PERSON><PERSON>", "PE.Controllers.Toolbar.textLargeOperator": "Store operatører ", "PE.Controllers.Toolbar.textLimitAndLog": "Afgrænsninger og logaritmer", "PE.Controllers.Toolbar.textMatrix": "<PERSON>rice<PERSON>", "PE.Controllers.Toolbar.textOperator": "Operatører ", "PE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textScript": "Man<PERSON>rip<PERSON>", "PE.Controllers.Toolbar.textSymbols": "Symboler", "PE.Controllers.Toolbar.textWarning": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Højre-Venstre pil over", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Venstregående pil over", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Højregående pil over", "PE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "PE.Controllers.Toolbar.txtAccent_BarTop": "Overbar", "PE.Controllers.Toolbar.txtAccent_BorderBox": "In<PERSON><PERSON>t formel (Med pladsholder)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Boksformel (eksempel)", "PE.Controllers.Toolbar.txtAccent_Check": "Tjek", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Overbrace", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC med øvre bar", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y med overbar", "PE.Controllers.Toolbar.txtAccent_DDDot": "Triple Dot", "PE.Controllers.Toolbar.txtAccent_DDot": "Dobbelt prik", "PE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON> øvre linie", "PE.Controllers.Toolbar.txtAccent_Grave": "Grav", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Grupper tegn under", "PE.Controllers.Toolbar.txtAccent_GroupTop": "<PERSON><PERSON><PERSON> tegn over", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> harpoon over", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> harpoon over", "PE.Controllers.Toolbar.txtAccent_Hat": "Hat", "PE.Controllers.Toolbar.txtAccent_Smile": "Breve", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "Parentes", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parentes med separatorer", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parentes med separatorer", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON><PERSON> klamme", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_Curve": "Parentes", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Parentes med separatorer", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Sager (<PERSON> bet<PERSON><PERSON>)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "sager (tre betingelser)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Stak objekt", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Stak objekt", "PE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON><PERSON> på <PERSON>r", "PE.Controllers.Toolbar.txtBracket_Custom_6": "binomial koefficient", "PE.Controllers.Toolbar.txtBracket_Custom_7": "binomial koefficient", "PE.Controllers.Toolbar.txtBracket_Line": "Parentes", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_LineDouble": "Parentes", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_LowLim": "Parentes", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_Round": "Parentes", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parentes med separatorer", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_Square": "Parentes", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Parentes", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Parentes", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Parentes", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "Parentes", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_UppLim": "Parentes", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON><PERSON><PERSON> beslag", "PE.Controllers.Toolbar.txtFractionDiagonal": "Skrå fraktion", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Forskellen", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Forskellen", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Forskellen", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Forskellen", "PE.Controllers.Toolbar.txtFractionHorizontal": "Lineær fraktion", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi over 2", "PE.Controllers.Toolbar.txtFractionSmall": "Lille fraktion", "PE.Controllers.Toolbar.txtFractionVertical": "Stablet fraktion", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Omvendt cosinusfunktion", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "hyperbolsk invers cosinus funktion", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Omvendt cotangent funktion", "PE.Controllers.Toolbar.txtFunction_1_Coth": "hyperbolsk invers", "PE.Controllers.Toolbar.txtFunction_1_Csc": "invers cosekant funktion", "PE.Controllers.Toolbar.txtFunction_1_Csch": "hyperbolsk invers", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Omvendt sekantfunktion", "PE.Controllers.Toolbar.txtFunction_1_Sech": "hyperbolsk invers sekant", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Omvendt sinusfunktion", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "hyperbolsk invers sinus", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Omvendt tangentfunktion", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "hyperbolsk invers tangent", "PE.Controllers.Toolbar.txtFunction_Cos": "<PERSON>sin<PERSON> funktion", "PE.Controllers.Toolbar.txtFunction_Cosh": "Hyperbolsk cosinus funktion ", "PE.Controllers.Toolbar.txtFunction_Cot": "Cotangens funktion", "PE.Controllers.Toolbar.txtFunction_Coth": "hyperbolsk cotangens", "PE.Controllers.Toolbar.txtFunction_Csc": "Cosekant funktion", "PE.Controllers.Toolbar.txtFunction_Csch": "Hyperbolsk cosekant funktion", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Tangens formel", "PE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sech": "hyperbolsk sekant funktion", "PE.Controllers.Toolbar.txtFunction_Sin": "Sinus funktion", "PE.Controllers.Toolbar.txtFunction_Sinh": "hyperbolsk sinus funktion", "PE.Controllers.Toolbar.txtFunction_Tan": "Tangens funktion", "PE.Controllers.Toolbar.txtFunction_Tanh": "hyperbolsk tangent", "PE.Controllers.Toolbar.txtIntegral": "integral", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Forskellig theta", "PE.Controllers.Toolbar.txtIntegral_dx": "<PERSON><PERSON><PERSON><PERSON> x", "PE.Controllers.Toolbar.txtIntegral_dy": "<PERSON><PERSON><PERSON><PERSON> y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "integral", "PE.Controllers.Toolbar.txtIntegralDouble": "Dobbelt integral", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Dobbelt integral", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Dobbelt integral", "PE.Controllers.Toolbar.txtIntegralOriented": "Integreret kontur", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integreret kontur", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Overflade integral", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Overflade integral", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Overflade integral", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integreret kontur", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume integral", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volume integral", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volume integral", "PE.Controllers.Toolbar.txtIntegralSubSup": "integral", "PE.Controllers.Toolbar.txtIntegralTriple": "Triple Integral", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple Integral", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple Integral", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Delprodukt", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Delprodukt", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Delprodukt", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Delprodukt", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Delprodukt", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "kryds", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "kryds", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "kryds", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "kryds", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "kryds", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Begrænset eksempel", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Ma<PERSON><PERSON><PERSON> eksempel", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Beg<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON><PERSON>g logaritme", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logaritme", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritme", "PE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Tom Matrix", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Tom Matrix", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Tom Matrix", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Tom Matrix", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Tom matrix med klammer", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Tom matrix med klammer", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Tom matrix med klammer", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Tom matrix med klammer", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Tom Matrix", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Tom Matrix", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Tom Matrix", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Tom Matrix", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Midterlinje prikker", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonale prikker", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON><PERSON><PERSON> matrice", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON><PERSON><PERSON> matrice", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Identitetsmatrix", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Identitetsmatrix", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Identitetsmatrix", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Identitetsmatrix", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Højre-Venstre pil under", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Højre-Venstre pil over", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Venstregående pil under", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Venstregående pil over", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Højregående pil under", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Højregående pil over", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "kolon ligmed ", "PE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Delta udbytter", "PE.Controllers.Toolbar.txtOperator_Definition": "Lig med pr definition", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta er lig med", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Højre-Venstre pil under", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Højre-Venstre pil over", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Venstregående pil under", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Venstregående pil over", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Højregående pil under", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Højregående pil over", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Lig med lig med", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "Minus lig med", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus lig med", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Målt af", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Radikal", "PE.Controllers.Toolbar.txtRadicalCustom_2": "Radikal", "PE.Controllers.Toolbar.txtRadicalRoot_2": "Kvadratrod med grader", "PE.Controllers.Toolbar.txtRadicalRoot_3": "Kvadratrod", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Radikalt med grader", "PE.Controllers.Toolbar.txtRadicalSqrt": "Kvadra<PERSON>den", "PE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "sænket", "PE.Controllers.Toolbar.txtScriptSubSup": "Subscript-Superscript", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "LeftSubscript-Superscript", "PE.Controllers.Toolbar.txtScriptSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_additional": "Komplimenter", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "Næsten identisk med", "PE.Controllers.Toolbar.txtSymbol_ast": "Stjerne operatør", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Indsats", "PE.Controllers.Toolbar.txtSymbol_bullet": "Punkt operatør", "PE.Controllers.Toolbar.txtSymbol_cap": "kryds", "PE.Controllers.Toolbar.txtSymbol_cbrt": "Ku<PERSON>k<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON><PERSON> ellipse midterlinje", "PE.Controllers.Toolbar.txtSymbol_celsius": "Grader <PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Cirka lig med", "PE.Controllers.Toolbar.txtSymbol_cup": "Union", "PE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON> hø<PERSON> diagonal ellipse", "PE.Controllers.Toolbar.txtSymbol_degree": "Grader", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Divider tegn", "PE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON> sæt", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "Lig med", "PE.Controllers.Toolbar.txtSymbol_equiv": "Identisk med", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_factorial": "Fakultet", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grader F<PERSON><PERSON>heit", "PE.Controllers.Toolbar.txtSymbol_forall": "For alle", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON> end eller lig med", "PE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON> større end", "PE.Controllers.Toolbar.txtSymbol_greater": "Større end", "PE.Controllers.Toolbar.txtSymbol_in": "Elementer af", "PE.Controllers.Toolbar.txtSymbol_inc": "Stigning", "PE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Venstre pil", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Venstre-Højre pil", "PE.Controllers.Toolbar.txtSymbol_leq": "Mindre end eller lig med", "PE.Controllers.Toolbar.txtSymbol_less": "Mindre end", "PE.Controllers.Toolbar.txtSymbol_ll": "Meget mindre end", "PE.Controllers.Toolbar.txtSymbol_minus": "Minus", "PE.Controllers.Toolbar.txtSymbol_mp": "Minus plus", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "<PERSON>k<PERSON> lig med", "PE.Controllers.Toolbar.txtSymbol_ni": "Inde<PERSON> som medlem", "PE.Controllers.Toolbar.txtSymbol_not": "Intet tegn", "PE.Controllers.Toolbar.txtSymbol_notexists": "Der findes ikke", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "<PERSON><PERSON> for<PERSON>", "PE.Controllers.Toolbar.txtSymbol_percent": "Procent", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Plus", "PE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "PE.Controllers.Toolbar.txtSymbol_propto": "Proportionelt til ", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON> rod", "PE.Controllers.Toolbar.txtSymbol_qed": "Afslutning af bevis", "PE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON><PERSON><PERSON> opad diagonal ellipse", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON><PERSON> pil", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Radikalt tegn", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "Multplikationstegn", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Op pil", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon variant", "PE.Controllers.Toolbar.txtSymbol_varphi": "Phi variant", "PE.Controllers.Toolbar.txtSymbol_varpi": "Pi variant", "PE.Controllers.Toolbar.txtSymbol_varrho": "Rho variant", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma Variant", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Theta variant", "PE.Controllers.Toolbar.txtSymbol_vdots": "Lodrette ellipser", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "Tilpas til dias", "PE.Controllers.Viewport.textFitWidth": "T<PERSON><PERSON> til bredde", "PE.Views.Animation.str0_5": "0.5 s (Very Fast)", "PE.Views.Animation.str1": "1 s (Fast)", "PE.Views.Animation.str2": "2 s (Medium)", "PE.Views.Animation.str20": "20 s (Extremely Slow)", "PE.Views.Animation.str3": "3 s (Slow)", "PE.Views.Animation.str5": "5 s (Very Slow)", "PE.Views.Animation.strDelay": "Delay", "PE.Views.Animation.strDuration": "Duration", "PE.Views.Animation.strRepeat": "Repeat", "PE.Views.Animation.strRewind": "Rewind", "PE.Views.Animation.strStart": "Start", "PE.Views.Animation.strTrigger": "<PERSON><PERSON>", "PE.Views.Animation.textAutoPreview": "AutoPreview", "PE.Views.Animation.textMoreEffects": "Show more effects", "PE.Views.Animation.textMoveEarlier": "Move earlier", "PE.Views.Animation.textMoveLater": "Move Later", "PE.Views.Animation.textMultiple": "Multiple", "PE.Views.Animation.textNone": "None", "PE.Views.Animation.textNoRepeat": "(none)", "PE.Views.Animation.textOnClickOf": "On Click of", "PE.Views.Animation.textOnClickSequence": "On Click Sequence", "PE.Views.Animation.textStartAfterPrevious": "After previous", "PE.Views.Animation.textStartOnClick": "On Click", "PE.Views.Animation.textStartWithPrevious": "With Previous", "PE.Views.Animation.textUntilEndOfSlide": "Until end of slide", "PE.Views.Animation.textUntilNextClick": "Until next click", "PE.Views.Animation.txtAddEffect": "Add Animation", "PE.Views.Animation.txtAnimationPane": "Animation Pane", "PE.Views.Animation.txtParameters": "Parameters", "PE.Views.Animation.txtPreview": "Preview", "PE.Views.Animation.txtSec": "s", "PE.Views.AnimationDialog.textPreviewEffect": "Preview effect", "PE.Views.AnimationDialog.textTitle": "More effects", "PE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "PE.Views.ChartSettings.text3dHeight": "Height (% of base)", "PE.Views.ChartSettings.text3dRotation": "3D Rotation", "PE.Views.ChartSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "PE.Views.ChartSettings.textAutoscale": "Autoscale", "PE.Views.ChartSettings.textChartType": "Skift diagramtype", "PE.Views.ChartSettings.textDefault": "Default rotation", "PE.Views.ChartSettings.textDown": "Down", "PE.Views.ChartSettings.textEditData": "Rediger data", "PE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON>er", "PE.Views.ChartSettings.textLeft": "Left", "PE.Views.ChartSettings.textNarrow": "Narrow field of view", "PE.Views.ChartSettings.textPerspective": "Perspective", "PE.Views.ChartSettings.textRight": "Right", "PE.Views.ChartSettings.textRightAngle": "Right angle axes", "PE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textStyle": "Stilart", "PE.Views.ChartSettings.textUp": "Up", "PE.Views.ChartSettings.textWiden": "Widen field of view", "PE.Views.ChartSettings.textWidth": "Bredde", "PE.Views.ChartSettings.textX": "X rotation", "PE.Views.ChartSettings.textY": "Y rotation", "PE.Views.ChartSettingsAdvanced.textAlt": "Alternativ tekst", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Beskrivelse", "PE.Views.ChartSettingsAdvanced.textAltTip": "Den alternative tekstbaserede repræsentation af det visuelle objekt, som vil blive oplæst til folk med syns- eller lærings<PERSON>fordringer, for at hjælpe dem til at forstå den information der kan findes i et billede, figur, diagram eller tabel.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Titel", "PE.Views.ChartSettingsAdvanced.textCenter": "Center", "PE.Views.ChartSettingsAdvanced.textChartName": "Chart name", "PE.Views.ChartSettingsAdvanced.textFrom": "From", "PE.Views.ChartSettingsAdvanced.textGeneral": "General", "PE.Views.ChartSettingsAdvanced.textHeight": "Height", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Constant proportions", "PE.Views.ChartSettingsAdvanced.textPlacement": "Placement", "PE.Views.ChartSettingsAdvanced.textPosition": "Position", "PE.Views.ChartSettingsAdvanced.textSize": "Size", "PE.Views.ChartSettingsAdvanced.textTitle": "Diagram - a<PERSON><PERSON><PERSON> in<PERSON>", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "Top left corner", "PE.Views.ChartSettingsAdvanced.textVertical": "Vertical", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Indstil standardformat for {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Indstil som standard", "PE.Views.DateTimeDialog.textFormat": "Formater", "PE.Views.DateTimeDialog.textLang": "Sp<PERSON>", "PE.Views.DateTimeDialog.textUpdate": "Opdater automatisk", "PE.Views.DateTimeDialog.txtTitle": "<PERSON>to og tid", "PE.Views.DocumentHolder.aboveText": "Over", "PE.Views.DocumentHolder.addCommentText": "Tilføj kommentar", "PE.Views.DocumentHolder.advancedChartText": "Chart advanced settings", "PE.Views.DocumentHolder.advancedEquationText": "Equation settings", "PE.Views.DocumentHolder.advancedImageText": "<PERSON><PERSON> a<PERSON><PERSON><PERSON> in<PERSON>", "PE.Views.DocumentHolder.advancedParagraphText": "<PERSON><PERSON><PERSON><PERSON> in<PERSON> for Afsnit ", "PE.Views.DocumentHolder.advancedShapeText": "Form avancerede indstillinger", "PE.Views.DocumentHolder.advancedTableText": "<PERSON><PERSON> a<PERSON><PERSON><PERSON> in<PERSON>", "PE.Views.DocumentHolder.alignmentText": "Tilpasning", "PE.Views.DocumentHolder.allLinearText": "Alle - Lineært", "PE.Views.DocumentHolder.allProfText": "All - Professional", "PE.Views.DocumentHolder.belowText": "Under", "PE.Views.DocumentHolder.cellAlignText": "<PERSON><PERSON> lodret justering", "PE.Views.DocumentHolder.cellText": "Celle", "PE.Views.DocumentHolder.centerText": "Centrum", "PE.Views.DocumentHolder.columnText": "Kolonne", "PE.Views.DocumentHolder.currLinearText": "Current - Linear", "PE.Views.DocumentHolder.currProfText": "Current - Professional", "PE.Views.DocumentHolder.deleteColumnText": "Slet kolonne", "PE.Views.DocumentHolder.deleteRowText": "Slet række", "PE.Views.DocumentHolder.deleteTableText": "Slet tabel", "PE.Views.DocumentHolder.deleteText": "Slet", "PE.Views.DocumentHolder.direct270Text": "Roter tekst op", "PE.Views.DocumentHolder.direct90Text": "Roter tekst nedad", "PE.Views.DocumentHolder.directHText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.directionText": "Tekst retning", "PE.Views.DocumentHolder.editChartText": "Rediger data", "PE.Views.DocumentHolder.editHyperlinkText": "Rediger hyperlink", "PE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "PE.Views.DocumentHolder.hyperlinkText": "Hyperlink", "PE.Views.DocumentHolder.ignoreAllSpellText": "Ignorer alt", "PE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnLeftText": "Venstre kolonne", "PE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON><PERSON> kolo<PERSON>", "PE.Views.DocumentHolder.insertColumnText": "Indsæt kolonne", "PE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> over", "PE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON><PERSON> under", "PE.Views.DocumentHolder.insertRowText": "Indsæt række", "PE.Views.DocumentHolder.insertText": "indsæt", "PE.Views.DocumentHolder.langText": "<PERSON><PERSON><PERSON><PERSON> sprog", "PE.Views.DocumentHolder.latexText": "LaTeX", "PE.Views.DocumentHolder.leftText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.loadSpellText": "Indlæser variationer...", "PE.Views.DocumentHolder.mergeCellsText": "<PERSON><PERSON> celler", "PE.Views.DocumentHolder.mniCustomTable": "Indsæt brugerdefineret tabel", "PE.Views.DocumentHolder.moreText": "Flere varianter...", "PE.Views.DocumentHolder.noSpellVariantsText": "Ingen varianter", "PE.Views.DocumentHolder.originalSizeText": "Faktisk størrelse", "PE.Views.DocumentHolder.removeHyperlinkText": "Slet Hyperlink", "PE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "PE.Views.DocumentHolder.spellcheckText": "Stavekontrol", "PE.Views.DocumentHolder.splitCellsText": "Dele celle...", "PE.Views.DocumentHolder.splitCellTitleText": "Dele celle", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textAddHGuides": "Add horizontal guide", "PE.Views.DocumentHolder.textAddVGuides": "Add Vertical Guide", "PE.Views.DocumentHolder.textArrangeBack": "Flyt til baggrund", "PE.Views.DocumentHolder.textArrangeBackward": "Send baglæns", "PE.Views.DocumentHolder.textArrangeForward": "Fr<PERSON><PERSON>k<PERSON>", "PE.Views.DocumentHolder.textArrangeFront": "<PERSON><PERSON><PERSON> til forgrunden", "PE.Views.DocumentHolder.textClearGuides": "Clear guides", "PE.Views.DocumentHolder.textCm": "cm", "PE.Views.DocumentHolder.textCopy": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCrop": "Beskær", "PE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFit": "Tilpas", "PE.Views.DocumentHolder.textCustom": "Custom", "PE.Views.DocumentHolder.textCut": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textDeleteGuide": "Delete guide", "PE.Views.DocumentHolder.textDeleteLayout": "Delete Layout", "PE.Views.DocumentHolder.textDeleteMaster": "Delete Master", "PE.Views.DocumentHolder.textDistributeCols": "<PERSON>el kolonner", "PE.Views.DocumentHolder.textDistributeRows": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textDuplicateLayout": "Duplicate Layout", "PE.Views.DocumentHolder.textDuplicateSlideMaster": "Duplicate Slide Master", "PE.Views.DocumentHolder.textEditObject": "Edit object", "PE.Views.DocumentHolder.textEditPoints": "Edit points", "PE.Views.DocumentHolder.textFlipH": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textFlipV": "Vend lodret", "PE.Views.DocumentHolder.textFromFile": "Fra fil", "PE.Views.DocumentHolder.textFromStorage": "Fra lager", "PE.Views.DocumentHolder.textFromUrl": "Fra URL", "PE.Views.DocumentHolder.textGridlines": "Gridlines", "PE.Views.DocumentHolder.textGuides": "Guides", "PE.Views.DocumentHolder.textInsertLayout": "Insert Layout", "PE.Views.DocumentHolder.textInsertSlideMaster": "Insert Slide Master", "PE.Views.DocumentHolder.textNextPage": "<PERSON><PERSON><PERSON> dias", "PE.Views.DocumentHolder.textPaste": "Indsæt", "PE.Views.DocumentHolder.textPrevPage": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textRemove": "Remove", "PE.Views.DocumentHolder.textRenameLayout": "<PERSON><PERSON>out", "PE.Views.DocumentHolder.textRenameMaster": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textReplace": "Erstat billede", "PE.Views.DocumentHolder.textResetCrop": "Reset crop", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "Roter 90° mod uret", "PE.Views.DocumentHolder.textRotate90": "Roter 90° med uret", "PE.Views.DocumentHolder.textRulers": "Rulers", "PE.Views.DocumentHolder.textSaveAsPicture": "Save as picture", "PE.Views.DocumentHolder.textShapeAlignBottom": "Tilpas knap", "PE.Views.DocumentHolder.textShapeAlignCenter": "Tilpas til midten", "PE.Views.DocumentHolder.textShapeAlignLeft": "Tilpas til venstre", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Tilpas til midten", "PE.Views.DocumentHolder.textShapeAlignRight": "Tilpas til højre", "PE.Views.DocumentHolder.textShapeAlignTop": "Tilpas til toppen", "PE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "PE.Views.DocumentHolder.textShowGridlines": "Show gridlines", "PE.Views.DocumentHolder.textShowGuides": "Show Guides", "PE.Views.DocumentHolder.textSlideSettings": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textSmartGuides": "Smart Guides", "PE.Views.DocumentHolder.textSnapObjects": "Snap object to grid", "PE.Views.DocumentHolder.textStartAfterPrevious": "Start After Previous", "PE.Views.DocumentHolder.textStartOnClick": "Start On Click", "PE.Views.DocumentHolder.textStartWithPrevious": "Start With Previous", "PE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tipGuides": "Show guides", "PE.Views.DocumentHolder.tipIsLocked": "Elementet bliver redigeret af en anden bruger.", "PE.Views.DocumentHolder.toDictionaryText": "Tilføj til Ordbog", "PE.Views.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON> ne<PERSON> ramme ", "PE.Views.DocumentHolder.txtAddFractionBar": "Tilføj fraktionsbar", "PE.Views.DocumentHolder.txtAddHor": "Tilføj horisontal linie", "PE.Views.DocumentHolder.txtAddLB": "<PERSON><PERSON><PERSON><PERSON><PERSON> venstre nederste linie", "PE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON> venstre ramme", "PE.Views.DocumentHolder.txtAddLT": "<PERSON><PERSON><PERSON><PERSON>j venstre øverste linie", "PE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON><PERSON><PERSON> højre ramme", "PE.Views.DocumentHolder.txtAddTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ramme", "PE.Views.DocumentHolder.txtAddVer": "Til<PERSON><PERSON>j lodret linie", "PE.Views.DocumentHolder.txtAlign": "Tilpas", "PE.Views.DocumentHolder.txtAlignToChar": "Tilpas til karakterer ", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtBackground": "Baggrund", "PE.Views.DocumentHolder.txtBorderProps": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtBottom": "Bund", "PE.Views.DocumentHolder.txtChangeLayout": "Skift Layout", "PE.Views.DocumentHolder.txtChangeTheme": "Skift tema", "PE.Views.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON> til<PERSON>ning", "PE.Views.DocumentHolder.txtDecreaseArg": "Formindsk argumentstørrelse", "PE.Views.DocumentHolder.txtDeleteArg": "Slet argument", "PE.Views.DocumentHolder.txtDeleteBreak": "Slet manuelt linieskift", "PE.Views.DocumentHolder.txtDeleteChars": "Slet omklamrende tegn", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Slet omsluttende tegn og separatøre", "PE.Views.DocumentHolder.txtDeleteEq": "Slet ligning", "PE.Views.DocumentHolder.txtDeleteGroupChar": "Slet tegn", "PE.Views.DocumentHolder.txtDeleteRadical": "Slet radikal", "PE.Views.DocumentHolder.txtDeleteSlide": "<PERSON><PERSON> dias", "PE.Views.DocumentHolder.txtDistribHor": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtDistribVert": "<PERSON><PERSON> lodret", "PE.Views.DocumentHolder.txtDuplicateSlide": "duplikat dias", "PE.Views.DocumentHolder.txtFractionLinear": "Skift til lineær fraktion ", "PE.Views.DocumentHolder.txtFractionSkewed": "Skift til skæv fraktion", "PE.Views.DocumentHolder.txtFractionStacked": "Skift til stablet fraktion", "PE.Views.DocumentHolder.txtGroup": "Gruppe", "PE.Views.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON> over tekst", "PE.Views.DocumentHolder.txtGroupCharUnder": "Karakter under tekst", "PE.Views.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>te ramme", "PE.Views.DocumentHolder.txtHideBottomLimit": "Skjul nederste grænse", "PE.Views.DocumentHolder.txtHideCloseBracket": "Skjul lukkende klamme", "PE.Views.DocumentHolder.txtHideDegree": "Skjul vinkel", "PE.Views.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON><PERSON> van<PERSON>t linie", "PE.Views.DocumentHolder.txtHideLB": "Skjul venstre nederste linie", "PE.Views.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON> venstre ramme", "PE.Views.DocumentHolder.txtHideLT": "Skjul venstre øverste linie", "PE.Views.DocumentHolder.txtHideOpenBracket": "Skjul den åbnende klamme ", "PE.Views.DocumentHolder.txtHidePlaceholder": "Skjul pladsholder", "PE.Views.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON><PERSON> højre ramme", "PE.Views.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON> ø<PERSON> ramme", "PE.Views.DocumentHolder.txtHideTopLimit": "Skjul øverste grænse", "PE.Views.DocumentHolder.txtHideVer": "Sk<PERSON>l lodret linie", "PE.Views.DocumentHolder.txtIncreaseArg": "Forstør argument st<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtInsAudio": "Insert audio", "PE.Views.DocumentHolder.txtInsChart": "Insert chart", "PE.Views.DocumentHolder.txtInsertArgAfter": "Indsæt argument efter", "PE.Views.DocumentHolder.txtInsertArgBefore": "Indsæt argument før", "PE.Views.DocumentHolder.txtInsertBreak": "Indsæt manuelt skift", "PE.Views.DocumentHolder.txtInsertEqAfter": "Indsæt ligning efter", "PE.Views.DocumentHolder.txtInsertEqBefore": "Indsæt ligning før", "PE.Views.DocumentHolder.txtInsImage": "Insert image from file", "PE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "PE.Views.DocumentHolder.txtInsSmartArt": "Insert SmartArt", "PE.Views.DocumentHolder.txtInsTable": "Insert table", "PE.Views.DocumentHolder.txtInsVideo": "Insert video", "PE.Views.DocumentHolder.txtKeepTextOnly": "Behold kun teksten", "PE.Views.DocumentHolder.txtLimitChange": "Skift afgrænsningspladseringer", "PE.Views.DocumentHolder.txtLimitOver": "Beg<PERSON><PERSON><PERSON><PERSON> over tekst", "PE.Views.DocumentHolder.txtLimitUnder": "Begrænsning under tekst", "PE.Views.DocumentHolder.txtMatchBrackets": "Tilpas klammer til argument højde", "PE.Views.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON> just<PERSON>", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Move slide to end", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Move slide to beginning", "PE.Views.DocumentHolder.txtNewSlide": "<PERSON><PERSON> dias", "PE.Views.DocumentHolder.txtOverbar": "<PERSON><PERSON> over tekst", "PE.Views.DocumentHolder.txtPasteDestFormat": "Brug destinations tema", "PE.Views.DocumentHolder.txtPastePicture": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Behold oprindelig formatering", "PE.Views.DocumentHolder.txtPressLink": "Tryk {0} og klik på linket", "PE.Views.DocumentHolder.txtPreview": "Start diasshow", "PE.Views.DocumentHolder.txtPrintSelection": "Printer-valg", "PE.Views.DocumentHolder.txtRemFractionBar": "Fjern frak<PERSON>bar", "PE.Views.DocumentHolder.txtRemLimit": "<PERSON><PERSON> begræ<PERSON>", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Fjern accent tegn", "PE.Views.DocumentHolder.txtRemoveBar": "Fjern bar", "PE.Views.DocumentHolder.txtRemScripts": "Slet scripts", "PE.Views.DocumentHolder.txtRemSubscript": "Slet subscript", "PE.Views.DocumentHolder.txtRemSuperscript": "Slet superscript", "PE.Views.DocumentHolder.txtResetLayout": "<PERSON><PERSON><PERSON><PERSON> dias", "PE.Views.DocumentHolder.txtScriptsAfter": "Manuskripter efter tekst", "PE.Views.DocumentHolder.txtScriptsBefore": "Manuskripter før te<PERSON>t", "PE.Views.DocumentHolder.txtSelectAll": "<PERSON><PERSON><PERSON><PERSON> alle", "PE.Views.DocumentHolder.txtShowBottomLimit": "<PERSON>is ne<PERSON>te linje", "PE.Views.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON> lukke klamme", "PE.Views.DocumentHolder.txtShowDegree": "Vis grader", "PE.Views.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON> <PERSON> klamme", "PE.Views.DocumentHolder.txtShowPlaceholder": "Vis placeholder", "PE.Views.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON> ø<PERSON> grænse", "PE.Views.DocumentHolder.txtSlide": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtSlideHide": "<PERSON><PERSON><PERSON><PERSON> <PERSON>as", "PE.Views.DocumentHolder.txtStretchBrackets": "Stræk klamme", "PE.Views.DocumentHolder.txtTop": "Top", "PE.Views.DocumentHolder.txtUnderbar": "<PERSON><PERSON> under tekst", "PE.Views.DocumentHolder.txtUngroup": "Fjern fra gruppe", "PE.Views.DocumentHolder.txtWarnUrl": "<PERSON><PERSON> du klikker på dette link, kan det være skadeligt for din enhed og dine data.<br><PERSON>r du sikker på, at du vil fortsætte?", "PE.Views.DocumentHolder.unicodeText": "Unicode", "PE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON> justering", "PE.Views.DocumentPreview.goToSlideText": "<PERSON><PERSON> til dias", "PE.Views.DocumentPreview.slideIndexText": "Dias {0} af {1}", "PE.Views.DocumentPreview.txtClose": "Luk diasshow", "PE.Views.DocumentPreview.txtDraw": "Draw", "PE.Views.DocumentPreview.txtEndSlideshow": "<PERSON><PERSON><PERSON> diasshow", "PE.Views.DocumentPreview.txtEraser": "Eraser", "PE.Views.DocumentPreview.txtEraseScreen": "Erase screen", "PE.Views.DocumentPreview.txtExitFullScreen": "Exit full screen", "PE.Views.DocumentPreview.txtFinalMessage": "Slutningen af diasshow. Klik for at afslutte.", "PE.Views.DocumentPreview.txtFullScreen": "<PERSON><PERSON> skæ<PERSON>", "PE.Views.DocumentPreview.txtHighlighter": "Highlighter", "PE.Views.DocumentPreview.txtInkColor": "Ink color", "PE.Views.DocumentPreview.txtNext": "<PERSON><PERSON><PERSON> dias", "PE.Views.DocumentPreview.txtPageNumInvalid": "Ugyldigt diasnummer", "PE.Views.DocumentPreview.txtPause": "Pause præsentation", "PE.Views.DocumentPreview.txtPen": "Pen", "PE.Views.DocumentPreview.txtPlay": "start præsentation", "PE.Views.DocumentPreview.txtPrev": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtReset": "Nulstil", "PE.Views.FileMenu.ariaFileMenu": "File menu", "PE.Views.FileMenu.btnAboutCaption": "Om", "PE.Views.FileMenu.btnBackCaption": "Gå til dokumentplacering", "PE.Views.FileMenu.btnCloseEditor": "Close File", "PE.Views.FileMenu.btnCloseMenuCaption": "Luk menu", "PE.Views.FileMenu.btnCreateNewCaption": "Opret ny", "PE.Views.FileMenu.btnDownloadCaption": "Hent som", "PE.Views.FileMenu.btnExitCaption": "A<PERSON>lut", "PE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnHistoryCaption": "Version historik", "PE.Views.FileMenu.btnInfoCaption": "Præsentationsinfo", "PE.Views.FileMenu.btnPrintCaption": "Print", "PE.Views.FileMenu.btnProtectCaption": "Beskyt", "PE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON> nylige", "PE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnReturnCaption": "Tilbage til præsentationen", "PE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnSaveAsCaption": "Gem som", "PE.Views.FileMenu.btnSaveCaption": "Gem", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Gem kopi som", "PE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON><PERSON> in<PERSON>", "PE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "PE.Views.FileMenu.btnToEditCaption": "Rediger præsentation", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Blank præsentation", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Opret ny", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Tilføj tekst", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Applikation", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Skift adgangsrettigheder", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Kommentar", "PE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Oprettet", "PE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Sidst redigeret af", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Sidst redigeret", "PE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Lokation", "PE.Views.FileMenuPanels.DocumentInfo.txtPresentationInfo": "Presentation Info", "PE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "PE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON> der har rettigheder", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Titel", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "PE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access rights", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Skift adgangsrettigheder", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON> der har rettigheder", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Med adgangskode", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "Beskyt præsentation", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "Med underskrift", "PE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the presentation.<br>The presentation is protected from editing.", "PE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the presentation by adding an<br>invisible digital signature", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Rediger præsentation", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Redigering fjerner signaturerne fra præsentationen. <br> Fortsæt?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Denne præsentation er beskyttet af adgangskode", "PE.Views.FileMenuPanels.ProtectDoc.txtProtectPresentation": "Encrypt this presentation with a password", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Gyldige signaturer er blevet tilføjet til præsentationen. Præsentationen er beskyttet mod redigering.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Nogle af de digitale signaturer i præsentationen er ugyldige eller kunne ikke verificeres. Præsentationen er beskyttet mod redigering", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON> underskrifter", "PE.Views.FileMenuPanels.Settings.okButtonText": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Fællesredigeringstilstand", "PE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strFontRender": "Skrifttype hentydning", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignore words in UPPERCASE", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignore words with numbers", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strPasteButton": "<PERSON><PERSON> knappen for Indsæt-optioner når indhold indsættes", "PE.Views.FileMenuPanels.Settings.strRTLSupport": "RTL interface", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Show changes from other users", "PE.Views.FileMenuPanels.Settings.strStrict": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strTabStyle": "Tab style", "PE.Views.FileMenuPanels.Settings.strTheme": "Interface tema", "PE.Views.FileMenuPanels.Settings.strUnit": "M<PERSON>leen<PERSON>", "PE.Views.FileMenuPanels.Settings.strZoom": "Standard zoomværdi", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Hvert 10. minut", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Hvert 30. minut", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Hvert 5. minut", "PE.Views.FileMenuPanels.Settings.text60Minutes": "Hver time", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Tilpasningsguide", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Automatisk gendannelse", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Gem automatisk", "PE.Views.FileMenuPanels.Settings.textDisabled": "deaktive<PERSON>", "PE.Views.FileMenuPanels.Settings.textFill": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textForceSave": "<PERSON><PERSON><PERSON> mellem versioner", "PE.Views.FileMenuPanels.Settings.textLine": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textMinute": "<PERSON><PERSON> minut", "PE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "Advanced settings", "PE.Views.FileMenuPanels.Settings.txtAll": "Se alle", "PE.Views.FileMenuPanels.Settings.txtAppearance": "Udseende", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Autokorrektur valgmuligheder", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Standard cache tilstand", "PE.Views.FileMenuPanels.Settings.txtCm": "Centimeter", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Collaboration", "PE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "Customize quick access", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Editing and saving", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Real-time co-editing. All changes are saved automatically", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Tilpas til dias", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "T<PERSON><PERSON> til bredde", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Hieroglyphs", "PE.Views.FileMenuPanels.Settings.txtInch": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtLast": "Vis sidste", "PE.Views.FileMenuPanels.Settings.txtLastUsed": "Last used", "PE.Views.FileMenuPanels.Settings.txtMac": "som OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "med<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtProofing": "Korrektur", "PE.Views.FileMenuPanels.Settings.txtPt": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtQuickPrint": "Show the Quick Print button in the editor header", "PE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "<PERSON>ktiv<PERSON><PERSON> alle", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Akt<PERSON><PERSON><PERSON> alle makroer uden meddelelse", "PE.Views.FileMenuPanels.Settings.txtScreenReader": "Turn on screen reader support", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Stavekontrol", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Deaktivér alle makroer uden besked", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Use the \"Save\" button to sync the changes you and others make", "PE.Views.FileMenuPanels.Settings.txtTabBack": "Use toolbar color as tabs background", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "<PERSON><PERSON> besked", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Deaktiver alle makroer med en besked", "PE.Views.FileMenuPanels.Settings.txtWin": "som Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "Workspace", "PE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "PE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save Copy As", "PE.Views.GridSettings.textCm": "cm", "PE.Views.GridSettings.textCustom": "Custom", "PE.Views.GridSettings.textSpacing": "Spacing", "PE.Views.GridSettings.textTitle": "Grid settings", "PE.Views.HeaderFooterDialog.applyAllText": "<PERSON><PERSON><PERSON> på alle", "PE.Views.HeaderFooterDialog.applyText": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.diffLanguage": "Du kan ikke bruge et datoformat i et andet sprog en hoved-diasset.<br>For at ændre hoved-dias, tryk \"anvend på alle\" i stedet for \"anvend\"", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textDateTime": "<PERSON>to og tid", "PE.Views.HeaderFooterDialog.textFixed": "Fast", "PE.Views.HeaderFooterDialog.textFormat": "Formater", "PE.Views.HeaderFooterDialog.textHFTitle": "Header/Footer settings", "PE.Views.HeaderFooterDialog.textLang": "Sp<PERSON>", "PE.Views.HeaderFooterDialog.textNotes": "Notes and handouts", "PE.Views.HeaderFooterDialog.textNotTitle": "Vis ikke på titel-dias", "PE.Views.HeaderFooterDialog.textPageNum": "Page number", "PE.Views.HeaderFooterDialog.textPreview": "Forhåndvisning", "PE.Views.HeaderFooterDialog.textSlide": "Slide", "PE.Views.HeaderFooterDialog.textSlideNum": "<PERSON><PERSON> nummer", "PE.Views.HeaderFooterDialog.textUpdate": "Opdater automatisk", "PE.Views.HeaderFooterDialog.txtFooter": "Footer", "PE.Views.HeaderFooterDialog.txtHeader": "Header", "PE.Views.HyperlinkSettingsDialog.strDisplay": "V<PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Link til", "PE.Views.HyperlinkSettingsDialog.textDefault": "Val<PERSON><PERSON> tekstfragmenter", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Enter caption here", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Enter link here", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Indtast værktøjstip her", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "External Link", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Dias i denne præsentation", "PE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "PE.Views.HyperlinkSettingsDialog.textSlides": "<PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.textTipText": "SkærmTip tekst", "PE.Views.HyperlinkSettingsDialog.textTitle": "Hyperlink indstillinger", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON> felt er nødvendigt", "PE.Views.HyperlinkSettingsDialog.txtFirst": "<PERSON><PERSON><PERSON><PERSON>as", "PE.Views.HyperlinkSettingsDialog.txtLast": "<PERSON><PERSON> dias", "PE.Views.HyperlinkSettingsDialog.txtNext": "<PERSON><PERSON><PERSON> dias", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Feltet skal være en URL i \"http://www.example.com\" formatet", "PE.Views.HyperlinkSettingsDialog.txtPrev": "<PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Feltet er begrænset til 2083 karakterer", "PE.Views.HyperlinkSettingsDialog.txtSlide": "<PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "PE.Views.ImageSettings.strTransparency": "Opacity", "PE.Views.ImageSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "PE.Views.ImageSettings.textCrop": "Beskær", "PE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropFit": "Tilpas", "PE.Views.ImageSettings.textCropToShape": "Crop to shape", "PE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "PE.Views.ImageSettings.textEditObject": "Rediger objekt", "PE.Views.ImageSettings.textFitSlide": "Tilpas til dias", "PE.Views.ImageSettings.textFlip": "Vend", "PE.Views.ImageSettings.textFromFile": "Fra fil", "PE.Views.ImageSettings.textFromStorage": "Fra lager", "PE.Views.ImageSettings.textFromUrl": "Fra URL", "PE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textHint270": "Roter 90° mod uret", "PE.Views.ImageSettings.textHint90": "Roter 90° med uret", "PE.Views.ImageSettings.textHintFlipH": "<PERSON><PERSON>", "PE.Views.ImageSettings.textHintFlipV": "Vend lodret", "PE.Views.ImageSettings.textInsert": "Erstat billede", "PE.Views.ImageSettings.textOriginalSize": "Faktisk størrelse", "PE.Views.ImageSettings.textRecentlyUsed": "Recently used", "PE.Views.ImageSettings.textResetCrop": "Reset crop", "PE.Views.ImageSettings.textRotate90": "Roter 90°", "PE.Views.ImageSettings.textRotation": "Rotation", "PE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textWidth": "Bredde", "PE.Views.ImageSettingsAdvanced.textAlt": "Alternativ tekst", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Beskrivelse", "PE.Views.ImageSettingsAdvanced.textAltTip": "Den alternative tekstbaserede repræsentation af det visuelle objekt, som vil blive oplæst til folk med syns- eller lærings<PERSON>fordringer, for at hjælpe dem til at forstå den information der kan findes i et billede, figur, diagram eller tabel.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Titel", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "Center", "PE.Views.ImageSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textFrom": "From", "PE.Views.ImageSettingsAdvanced.textGeneral": "General", "PE.Views.ImageSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ImageSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textImageName": "Image name", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON>er", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "Faktisk størrelse", "PE.Views.ImageSettingsAdvanced.textPlacement": "Placering", "PE.Views.ImageSettingsAdvanced.textPosition": "Stilling", "PE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "PE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textTitle": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> in<PERSON>", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "Top left corner", "PE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "PE.Views.ImageSettingsAdvanced.textVertically": "Lodret", "PE.Views.ImageSettingsAdvanced.textWidth": "Bredde", "PE.Views.LeftMenu.ariaLeftMenu": "Left menu", "PE.Views.LeftMenu.tipAbout": "Om", "PE.Views.LeftMenu.tipChat": "Cha<PERSON>", "PE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipPlugins": "Tilføjelsesprogrammer", "PE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSlides": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSupport": "Feedback & support", "PE.Views.LeftMenu.tipTitles": "Titler", "PE.Views.LeftMenu.txtDeveloper": "Udviklingstilstand", "PE.Views.LeftMenu.txtEditor": "Presentation Editor", "PE.Views.LeftMenu.txtLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> adgang", "PE.Views.LeftMenu.txtTrial": "Prøvetilstand", "PE.Views.LeftMenu.txtTrialDev": "Udvikler prøve-tilstand", "PE.Views.ParagraphSettings.strLineHeight": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.strParagraphSpacing": "Afsnit afstand", "PE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "PE.Views.ParagraphSettings.textAt": "På", "PE.Views.ParagraphSettings.textAtLeast": "Mindst", "PE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textExact": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.txtAutoText": "Automatisk", "PE.Views.ParagraphSettingsAdvanced.noTabs": "De specificerende faner vil blive vist i dette felt. ", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "Alle caps", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON> gennemstregning", "PE.Views.ParagraphSettingsAdvanced.strIndent": "Led", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "efter", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Speciel", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Skrifttype", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indrykninger og afstand", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON>å <PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Afstand", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Strikethrough", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "sænket", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strTabs": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Tilpasning", "PE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "<PERSON><PERSON><PERSON> afstand", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Standard fane", "PE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON><PERSON> linie", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Hængende", "PE.Views.ParagraphSettingsAdvanced.textJustified": "beret<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ingen)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON><PERSON> alle", "PE.Views.ParagraphSettingsAdvanced.textSet": "Specifer", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centrum", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Fane position", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTitle": "<PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "PE.Views.PrintWithPreview.txtAllPages": "All slides", "PE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "PE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "PE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "PE.Views.PrintWithPreview.txtCopies": "Copies", "PE.Views.PrintWithPreview.txtCurrentPage": "Current slide", "PE.Views.PrintWithPreview.txtCustomPages": "Custom print", "PE.Views.PrintWithPreview.txtEmptyTable": "There is nothing to print because the presentation is empty", "PE.Views.PrintWithPreview.txtHeaderFooterSettings": "Header/footer settings", "PE.Views.PrintWithPreview.txtOf": "of {0}", "PE.Views.PrintWithPreview.txtOneSide": "Print one sided", "PE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "PE.Views.PrintWithPreview.txtPage": "Slide", "PE.Views.PrintWithPreview.txtPageNumInvalid": "Slide number invalid", "PE.Views.PrintWithPreview.txtPages": "Slides", "PE.Views.PrintWithPreview.txtPaperSize": "Paper size", "PE.Views.PrintWithPreview.txtPrint": "Print", "PE.Views.PrintWithPreview.txtPrintPdf": "Print to PDF", "PE.Views.PrintWithPreview.txtPrintRange": "Print range", "PE.Views.PrintWithPreview.txtPrintSides": "Print sides", "PE.Views.RightMenu.ariaRightMenu": "Right menu", "PE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON><PERSON> inds<PERSON>linger", "PE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON>", "PE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON><PERSON> indstillinger", "PE.Views.RightMenu.txtShapeSettings": "Form indstillinger", "PE.Views.RightMenu.txtSignatureSettings": "Underskrift indstillinger", "PE.Views.RightMenu.txtSlideSettings": "<PERSON><PERSON>", "PE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON>", "PE.Views.RightMenu.txtTextArtSettings": "Textstil indstllinger", "PE.Views.ShapeSettings.strBackground": "Baggrundsfarve", "PE.Views.ShapeSettings.strChange": "Skift autoform", "PE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strForeground": "Forgrundsfarve", "PE.Views.ShapeSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strShadow": "<PERSON><PERSON>e", "PE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strStroke": "Strøg", "PE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strType": "Type", "PE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "PE.Views.ShapeSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "Den indtastede værdi er ikke korrekt.<br>venligst indtast en værdi mellem 0 pt og 1584 pt.", "PE.Views.ShapeSettings.textColor": "Farvefyld", "PE.Views.ShapeSettings.textDirection": "Retning", "PE.Views.ShapeSettings.textEditPoints": "Edit points", "PE.Views.ShapeSettings.textEditShape": "Edit shape", "PE.Views.ShapeSettings.textEmptyPattern": "Intet mynster", "PE.Views.ShapeSettings.textEyedropper": "Eyedropper", "PE.Views.ShapeSettings.textFlip": "Vend", "PE.Views.ShapeSettings.textFromFile": "Fra fil", "PE.Views.ShapeSettings.textFromStorage": "Fra lager", "PE.Views.ShapeSettings.textFromUrl": "Fra URL", "PE.Views.ShapeSettings.textGradient": "Gradientpunkter", "PE.Views.ShapeSettings.textGradientFill": "Gradient udfyldning", "PE.Views.ShapeSettings.textHint270": "Roter 90° mod uret", "PE.Views.ShapeSettings.textHint90": "Roter 90° med uret", "PE.Views.ShapeSettings.textHintFlipH": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textHintFlipV": "Vend lodret", "PE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON> eller struktur", "PE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textMoreColors": "More colors", "PE.Views.ShapeSettings.textNoFill": "Intet fyld", "PE.Views.ShapeSettings.textNoShadow": "No Shadow", "PE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textPosition": "Placering", "PE.Views.ShapeSettings.textRadial": "Radial", "PE.Views.ShapeSettings.textRecentlyUsed": "Recently used", "PE.Views.ShapeSettings.textRotate90": "Roter 90°", "PE.Views.ShapeSettings.textRotation": "Rotation", "PE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textShadow": "Shadow", "PE.Views.ShapeSettings.textStretch": "Stræk", "PE.Views.ShapeSettings.textStyle": "Stilart", "PE.Views.ShapeSettings.textTexture": "<PERSON>a struktur", "PE.Views.ShapeSettings.textTile": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.tipAddGradientPoint": "Tilfø<PERSON>", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papir", "PE.Views.ShapeSettings.txtCanvas": "læ<PERSON>", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "Sort stof", "PE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtGranite": "Granit", "PE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON>", "PE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "Ingen linje", "PE.Views.ShapeSettings.txtPapyrus": "Papyrus", "PE.Views.ShapeSettings.txtWood": "Træ", "PE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strMargins": "Tekst fyld", "PE.Views.ShapeSettingsAdvanced.textAlt": "Alternativ tekst", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Beskrivelse", "PE.Views.ShapeSettingsAdvanced.textAltTip": "Den alternative tekstbaserede repræsentation af det visuelle objekt, som vil blive oplæst til folk med syns- eller lærings<PERSON>fordringer, for at hjælpe dem til at forstå den information der kan findes i et billede, figur, diagram eller tabel.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Titel", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "AutoTilpas", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Be<PERSON>nd stø<PERSON>", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Begynd stil", "PE.Views.ShapeSettingsAdvanced.textBevel": "Facet", "PE.Views.ShapeSettingsAdvanced.textBottom": "Bund", "PE.Views.ShapeSettingsAdvanced.textCapType": "Spids type", "PE.Views.ShapeSettingsAdvanced.textCenter": "Center", "PE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Afslutning størrelse", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Afslutning formattering", "PE.Views.ShapeSettingsAdvanced.textFlat": "Flad", "PE.Views.ShapeSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFrom": "From", "PE.Views.ShapeSettingsAdvanced.textGeneral": "General", "PE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Tilmeld type", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON>er", "PE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON> stil", "PE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textNofit": "Tilpas ikke automatisk", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Placement", "PE.Views.ShapeSettingsAdvanced.textPosition": "Position", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "<PERSON><PERSON><PERSON> st<PERSON> på form for at tilpasse tekst", "PE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRotation": "Rotation", "PE.Views.ShapeSettingsAdvanced.textRound": "Rund", "PE.Views.ShapeSettingsAdvanced.textShapeName": "Shape name", "PE.Views.ShapeSettingsAdvanced.textShrink": "Nedskaler tekst ved overfyld", "PE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textSpacing": "<PERSON><PERSON><PERSON>d mellem kolonner", "PE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Tekstboks", "PE.Views.ShapeSettingsAdvanced.textTitle": "Form - a<PERSON><PERSON><PERSON> in<PERSON>", "PE.Views.ShapeSettingsAdvanced.textTop": "Top", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "Top left corner", "PE.Views.ShapeSettingsAdvanced.textVertical": "Vertical", "PE.Views.ShapeSettingsAdvanced.textVertically": "Lodret", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Vægte og pile", "PE.Views.ShapeSettingsAdvanced.textWidth": "Bredde", "PE.Views.ShapeSettingsAdvanced.txtNone": "Ingen", "PE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strDelete": "Fjern underskrift", "PE.Views.SignatureSettings.strDetails": "Underskrift detaljer", "PE.Views.SignatureSettings.strInvalid": "Ugyldige underskrifter", "PE.Views.SignatureSettings.strSign": "<PERSON><PERSON>", "PE.Views.SignatureSettings.strSignature": "Underskrift", "PE.Views.SignatureSettings.strValid": "Gyl<PERSON>ge underskrifter", "PE.Views.SignatureSettings.txtContinueEditing": "<PERSON><PERSON> alligevel", "PE.Views.SignatureSettings.txtEditWarning": "Redigering fjerner signaturerne fra præsentationen. <br> Fortsæt?", "PE.Views.SignatureSettings.txtRemoveWarning": "<PERSON>ns<PERSON> du at fjerne denne signatur?<br><PERSON> handling kan ikke omgøres.", "PE.Views.SignatureSettings.txtSigned": "Gyldige signaturer er blevet tilføjet til præsentationen. Præsentationen er beskyttet mod redigering.", "PE.Views.SignatureSettings.txtSignedInvalid": "Nogle af de digitale signaturer i præsentationen er ugyldige eller kunne ikke verificeres. Præsentationen er beskyttet mod redigering", "PE.Views.SlideSettings.strApplyAllSlides": "Apply to All Slides", "PE.Views.SlideSettings.strBackground": "Baggrundsfarve", "PE.Views.SlideSettings.strBackgroundGraphics": "Show Background graphics", "PE.Views.SlideSettings.strBackgroundReset": "Reset Background", "PE.Views.SlideSettings.strColor": "<PERSON><PERSON>", "PE.Views.SlideSettings.strDateTime": "Vis dato og tid", "PE.Views.SlideSettings.strFill": "Baggrund", "PE.Views.SlideSettings.strForeground": "Forgrundsfarve", "PE.Views.SlideSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strSlideNum": "Vis dias nummer", "PE.Views.SlideSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "Farvefyld", "PE.Views.SlideSettings.textDirection": "Retning", "PE.Views.SlideSettings.textEmptyPattern": "Intet mønster", "PE.Views.SlideSettings.textFromFile": "Fra fil", "PE.Views.SlideSettings.textFromStorage": "Fra lager", "PE.Views.SlideSettings.textFromUrl": "Fra URL", "PE.Views.SlideSettings.textGradient": "Gradientpunkter", "PE.Views.SlideSettings.textGradientFill": "Gradient udfyldning", "PE.Views.SlideSettings.textImageTexture": "<PERSON><PERSON> eller struktur", "PE.Views.SlideSettings.textLinear": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textNoFill": "Intet fyld", "PE.Views.SlideSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textPosition": "Placering", "PE.Views.SlideSettings.textRadial": "Radial", "PE.Views.SlideSettings.textReset": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textSelectImage": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStretch": "Stræk", "PE.Views.SlideSettings.textStyle": "Stilart", "PE.Views.SlideSettings.textTexture": "<PERSON>a struktur", "PE.Views.SlideSettings.textTile": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.tipAddGradientPoint": "Tilfø<PERSON>", "PE.Views.SlideSettings.tipRemoveGradientPoint": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papir", "PE.Views.SlideSettings.txtCanvas": "læ<PERSON>", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "Sort stof", "PE.Views.SlideSettings.txtGrain": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtGranite": "Granit", "PE.Views.SlideSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON>", "PE.Views.SlideSettings.txtKnit": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "Papyrus", "PE.Views.SlideSettings.txtWood": "Træ", "PE.Views.SlideshowSettings.textLoop": "<PERSON> kont<PERSON><PERSON><PERSON><PERSON>, indtil 'Esc' trykkes", "PE.Views.SlideshowSettings.textTitle": "<PERSON><PERSON> in<PERSON>", "PE.Views.SlideSizeSettings.strLandscape": "Landskab", "PE.Views.SlideSizeSettings.strPortrait": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textSlideOrientation": "<PERSON><PERSON>", "PE.Views.SlideSizeSettings.textSlideSize": "<PERSON><PERSON>", "PE.Views.SlideSizeSettings.textTitle": "<PERSON><PERSON> stø<PERSON><PERSON> in<PERSON>", "PE.Views.SlideSizeSettings.textWidth": "Bredde", "PE.Views.SlideSizeSettings.txt35": "35 mm slides", "PE.Views.SlideSizeSettings.txtA3": "A3 Papir(297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "A4 Papir (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO) papir (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO) papir (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "brugerdefinerede", "PE.Views.SlideSizeSettings.txtLedger": "Led<PERSON><PERSON><PERSON><PERSON> (11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "Brevpapir (8,5 x 11 i)", "PE.Views.SlideSizeSettings.txtOverhead": "overliggende", "PE.Views.SlideSizeSettings.txtSlideNum": "Number slides from", "PE.Views.SlideSizeSettings.txtStandard": "Standard (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Bredskærm", "PE.Views.Statusbar.goToPageText": "<PERSON><PERSON> til dias", "PE.Views.Statusbar.pageIndexText": "Dias {0} af {1}", "PE.Views.Statusbar.textShowBegin": "Vis fra beg<PERSON><PERSON><PERSON>", "PE.Views.Statusbar.textShowCurrent": "Vis fra nuværende billede", "PE.Views.Statusbar.textShowPresenterView": "Vis præsentationsvisning", "PE.Views.Statusbar.textSlideMaster": "Slide master", "PE.Views.Statusbar.tipAccessRights": "<PERSON><PERSON><PERSON><PERSON> adgangsrettigheder for dokument", "PE.Views.Statusbar.tipFitPage": "Tilpas til dias", "PE.Views.Statusbar.tipFitWidth": "T<PERSON><PERSON> til bredde", "PE.Views.Statusbar.tipPreview": "Start diasshow", "PE.Views.Statusbar.tipSetLang": "Væ<PERSON>g tekstsprog", "PE.Views.Statusbar.tipZoomFactor": "Zoom", "PE.Views.Statusbar.tipZoomIn": "Zoom ind", "PE.Views.Statusbar.tipZoomOut": "Zoom ud", "PE.Views.Statusbar.txtPageNumInvalid": "Ugyldigt diasnummer", "PE.Views.TableSettings.deleteColumnText": "Slet kolonne", "PE.Views.TableSettings.deleteRowText": "Slet række", "PE.Views.TableSettings.deleteTableText": "Slet tabel", "PE.Views.TableSettings.insertColumnLeftText": "Indsæt venstre kolonne", "PE.Views.TableSettings.insertColumnRightText": "Indsæt højre kolonne", "PE.Views.TableSettings.insertRowAboveText": "Indsæt række over", "PE.Views.TableSettings.insertRowBelowText": "Indsæt række under", "PE.Views.TableSettings.mergeCellsText": "<PERSON><PERSON> celler", "PE.Views.TableSettings.selectCellText": "<PERSON><PERSON><PERSON><PERSON> celle", "PE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON>g kolonne", "PE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON><PERSON> ræk<PERSON>", "PE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON> tabel", "PE.Views.TableSettings.splitCellsText": "Dele celle...", "PE.Views.TableSettings.splitCellTitleText": "Dele celle", "PE.Views.TableSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "PE.Views.TableSettings.textBackColor": "Baggrundsfarve", "PE.Views.TableSettings.textBanded": "Sammensluttet", "PE.Views.TableSettings.textBorderColor": "<PERSON><PERSON>", "PE.Views.TableSettings.textBorders": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textCellSize": "Cellestørelse", "PE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textDistributeCols": "<PERSON>el kolonner", "PE.Views.TableSettings.textDistributeRows": "<PERSON><PERSON>", "PE.Views.TableSettings.textEdit": "Rækker og kolonner", "PE.Views.TableSettings.textEmptyTemplate": "Ingen skabeloner", "PE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textLast": "Sidste", "PE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON> rammer som du vil ændre til stilarten valgt ovenover", "PE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON>g fra skabelon", "PE.Views.TableSettings.textTotal": "I alt", "PE.Views.TableSettings.textWidth": "Bredde", "PE.Views.TableSettings.tipAll": "<PERSON>æ<PERSON>g ydre ramme og alle indre linier", "PE.Views.TableSettings.tipBottom": "<PERSON>æ<PERSON>g kun ydre nederste ramme", "PE.Views.TableSettings.tipInner": "<PERSON><PERSON><PERSON><PERSON> kun indre linier", "PE.Views.TableSettings.tipInnerHor": "<PERSON><PERSON><PERSON>g kun vandret inderste linier", "PE.Views.TableSettings.tipInnerVert": "<PERSON>æ<PERSON>g kun lodrette indre linier", "PE.Views.TableSettings.tipLeft": "<PERSON>æ<PERSON>g kun ydre venstre ramme", "PE.Views.TableSettings.tipNone": "<PERSON><PERSON><PERSON>g ingen rammer", "PE.Views.TableSettings.tipOuter": "<PERSON><PERSON><PERSON>g kun ydre rammer", "PE.Views.TableSettings.tipRight": "<PERSON><PERSON><PERSON>g kun højre ramme", "PE.Views.TableSettings.tipTop": "Vælg kun ydre øverste ramme", "PE.Views.TableSettings.txtGroupTable_Custom": "Custom", "PE.Views.TableSettings.txtGroupTable_Dark": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Light": "Lys", "PE.Views.TableSettings.txtGroupTable_Medium": "Medium", "PE.Views.TableSettings.txtGroupTable_Optimal": "Best match for document", "PE.Views.TableSettings.txtNoBorders": "Ingen rammer", "PE.Views.TableSettings.txtTable_Accent": "Accent", "PE.Views.TableSettings.txtTable_DarkStyle": "Mø<PERSON><PERSON> tema", "PE.Views.TableSettings.txtTable_LightStyle": "Lys-stil", "PE.Views.TableSettings.txtTable_MediumStyle": "Medium Stil", "PE.Views.TableSettings.txtTable_NoGrid": "Intet gitter", "PE.Views.TableSettings.txtTable_NoStyle": "Intet tema", "PE.Views.TableSettings.txtTable_TableGrid": "<PERSON><PERSON>-gitter", "PE.Views.TableSettings.txtTable_ThemedStyle": "Tematiseret stil", "PE.Views.TableSettingsAdvanced.textAlt": "Alternativ tekst", "PE.Views.TableSettingsAdvanced.textAltDescription": "Beskrivelse", "PE.Views.TableSettingsAdvanced.textAltTip": "Den alternative tekstbaserede repræsentation af det visuelle objekt, som vil blive oplæst til folk med syns- eller lærings<PERSON>fordringer, for at hjælpe dem til at forstå den information der kan findes i et billede, figur, diagram eller tabel.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Titel", "PE.Views.TableSettingsAdvanced.textBottom": "Bund", "PE.Views.TableSettingsAdvanced.textCenter": "Center", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Brug standard margener", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Standardmargener", "PE.Views.TableSettingsAdvanced.textFrom": "From", "PE.Views.TableSettingsAdvanced.textGeneral": "General", "PE.Views.TableSettingsAdvanced.textHeight": "Height", "PE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Constant proportions", "PE.Views.TableSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textPlacement": "Placement", "PE.Views.TableSettingsAdvanced.textPosition": "Position", "PE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textSize": "Size", "PE.Views.TableSettingsAdvanced.textTableName": "Table name", "PE.Views.TableSettingsAdvanced.textTitle": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTop": "Top", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "Top left corner", "PE.Views.TableSettingsAdvanced.textVertical": "Vertical", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strBackground": "Baggrundsfarve", "PE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strForeground": "Forgrundsfarve", "PE.Views.TextArtSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strType": "Type", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "Den indtastede værdi er ikke korrekt.<br>venligst indtast en værdi mellem 0 pt og 1584 pt.", "PE.Views.TextArtSettings.textColor": "Farvefyld", "PE.Views.TextArtSettings.textDirection": "Retning", "PE.Views.TextArtSettings.textEmptyPattern": "Intet mønster", "PE.Views.TextArtSettings.textFromFile": "Fra fil", "PE.Views.TextArtSettings.textFromUrl": "Fra URL", "PE.Views.TextArtSettings.textGradient": "Gradientpunkter", "PE.Views.TextArtSettings.textGradientFill": "Gradient udfyldning", "PE.Views.TextArtSettings.textImageTexture": "<PERSON><PERSON> eller struktur", "PE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textNoFill": "Intet fyld", "PE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textPosition": "Placering", "PE.Views.TextArtSettings.textRadial": "Radial", "PE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStretch": "Stræk", "PE.Views.TextArtSettings.textStyle": "Stilart", "PE.Views.TextArtSettings.textTemplate": "Skabelon", "PE.Views.TextArtSettings.textTexture": "<PERSON>a struktur", "PE.Views.TextArtSettings.textTile": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textTransform": "Transformer", "PE.Views.TextArtSettings.tipAddGradientPoint": "Tilfø<PERSON>", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papir", "PE.Views.TextArtSettings.txtCanvas": "læ<PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "Sort stof", "PE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtGranite": "Granit", "PE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON>", "PE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtNoBorders": "Ingen linje", "PE.Views.TextArtSettings.txtPapyrus": "Papyrus", "PE.Views.TextArtSettings.txtWood": "Træ", "PE.Views.Toolbar.capAddLayout": "Add Layout", "PE.Views.Toolbar.capAddSlide": "<PERSON><PERSON><PERSON><PERSON><PERSON>as", "PE.Views.Toolbar.capAddSlideMaster": "Add Slide Master", "PE.Views.Toolbar.capBtnAddComment": "Tilføj kommentar", "PE.Views.Toolbar.capBtnComment": "Kommentar", "PE.Views.Toolbar.capBtnDateTime": "<PERSON>to og tid", "PE.Views.Toolbar.capBtnInsHeaderFooter": "Header & Footer", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "Symbol", "PE.Views.Toolbar.capBtnSlideNum": "<PERSON><PERSON> nummer", "PE.Views.Toolbar.capCloseMaster": "Close Master", "PE.Views.Toolbar.capInsertAudio": "Lyd", "PE.Views.Toolbar.capInsertChart": "Diagram", "PE.Views.Toolbar.capInsertEquation": "Formel", "PE.Views.Toolbar.capInsertHyperlink": "Hyperlink", "PE.Views.Toolbar.capInsertImage": "<PERSON><PERSON>", "PE.Views.Toolbar.capInsertPlaceholder": "Insert Placeholder", "PE.Views.Toolbar.capInsertShape": "Form", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON>", "PE.Views.Toolbar.capInsertText": "Tekstboks", "PE.Views.Toolbar.capInsertTextArt": "Text Art", "PE.Views.Toolbar.capInsertVideo": "Video", "PE.Views.Toolbar.capTabFile": "Fil", "PE.Views.Toolbar.capTabHome": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capTabInsert": "indsæt", "PE.Views.Toolbar.mniCapitalizeWords": "Alle ord med store bogstaver", "PE.Views.Toolbar.mniCustomTable": "Indsæt brugerdefineret tabel", "PE.Views.Toolbar.mniImageFromFile": "Billede fra fil", "PE.Views.Toolbar.mniImageFromStorage": "<PERSON><PERSON> fra opbevaring", "PE.Views.Toolbar.mniImageFromUrl": "Billede fra URL", "PE.Views.Toolbar.mniInsertSSE": "Insert Spreadsheet", "PE.Views.Toolbar.mniLowerCase": "små bogstaver", "PE.Views.Toolbar.mniSentenceCase": "Sætning store/små bogstaver.", "PE.Views.Toolbar.mniSlideAdvanced": "<PERSON><PERSON><PERSON><PERSON> in<PERSON>", "PE.Views.Toolbar.mniSlideStandard": "Standard (4:3)", "PE.Views.Toolbar.mniSlideWide": "Widescreen (16:9)", "PE.Views.Toolbar.mniToggleCase": "Skift store/små bogstaver", "PE.Views.Toolbar.mniUpperCase": "Store bogstaver", "PE.Views.Toolbar.strMenuNoFill": "Intet fyld", "PE.Views.Toolbar.textAlignBottom": "Justere text til bunden", "PE.Views.Toolbar.textAlignCenter": "Center tekst", "PE.Views.Toolbar.textAlignJust": "Retfærdiggøre", "PE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON> tekst tilbage", "PE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON> teksten til midten", "PE.Views.Toolbar.textAlignRight": "<PERSON><PERSON> teksten rigtigt", "PE.Views.Toolbar.textAlignTop": "Justere teksten til toppen", "PE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "PE.Views.Toolbar.textArrangeBack": "Flyt til baggrund", "PE.Views.Toolbar.textArrangeBackward": "Send baglæns", "PE.Views.Toolbar.textArrangeForward": "Fr<PERSON><PERSON>k<PERSON>", "PE.Views.Toolbar.textArrangeFront": "<PERSON><PERSON><PERSON> til forgrunden", "PE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "PE.Views.Toolbar.textBlackHeart": "Black heart suit", "PE.Views.Toolbar.textBold": "Fed", "PE.Views.Toolbar.textBullet": "Bullet", "PE.Views.Toolbar.textChart": "Chart", "PE.Views.Toolbar.textColumnsCustom": "Tilpassede kolonner", "PE.Views.Toolbar.textColumnsOne": "En kolonne", "PE.Views.Toolbar.textColumnsThree": "<PERSON><PERSON>olo<PERSON>", "PE.Views.Toolbar.textColumnsTwo": "To kolonner", "PE.Views.Toolbar.textContent": "Content", "PE.Views.Toolbar.textContentVertical": "Content (Vertical)", "PE.Views.Toolbar.textCopyright": "Copyright Sign", "PE.Views.Toolbar.textDegree": "Degree Sign", "PE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "PE.Views.Toolbar.textDivision": "Division Sign", "PE.Views.Toolbar.textDollar": "Dollar Sign", "PE.Views.Toolbar.textEuro": "Euro Sign", "PE.Views.Toolbar.textFooters": "Footers", "PE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "PE.Views.Toolbar.textInfinity": "Infinity", "PE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "PE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "PE.Views.Toolbar.textLineSpaceOptions": "Line spacing options", "PE.Views.Toolbar.textListSettings": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textMoreSymbols": "More symbols", "PE.Views.Toolbar.textNotEqualTo": "Not Equal To", "PE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "PE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "PE.Views.Toolbar.textPicture": "Picture", "PE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "PE.Views.Toolbar.textRecentlyUsed": "Recently used", "PE.Views.Toolbar.textRegistered": "Registered Sign", "PE.Views.Toolbar.textSection": "Section Sign", "PE.Views.Toolbar.textShapeAlignBottom": "Tilpas knap", "PE.Views.Toolbar.textShapeAlignCenter": "Tilpas til midten", "PE.Views.Toolbar.textShapeAlignLeft": "Tilpas til venstre", "PE.Views.Toolbar.textShapeAlignMiddle": "Tilpas til midten", "PE.Views.Toolbar.textShapeAlignRight": "Tilpas til højre", "PE.Views.Toolbar.textShapeAlignTop": "Tilpas til toppen", "PE.Views.Toolbar.textShapesCombine": "Combine", "PE.Views.Toolbar.textShapesFragment": "Fragment", "PE.Views.Toolbar.textShapesIntersect": "Intersect", "PE.Views.Toolbar.textShapesSubstract": "Subtract", "PE.Views.Toolbar.textShapesUnion": "Union", "PE.Views.Toolbar.textShowBegin": "Vis fra beg<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShowCurrent": "Vis fra nuværende billede", "PE.Views.Toolbar.textShowPresenterView": "Vis præsentationsvisning", "PE.Views.Toolbar.textShowSettings": "<PERSON><PERSON> in<PERSON>", "PE.Views.Toolbar.textSmartArt": "SmartArt", "PE.Views.Toolbar.textSmile": "White Smiling Face", "PE.Views.Toolbar.textSquareRoot": "Square Root", "PE.Views.Toolbar.textStrikeout": "Strikethrough", "PE.Views.Toolbar.textSubscript": "sænket", "PE.Views.Toolbar.textSuperscript": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabAnimation": "Animation", "PE.Views.Toolbar.textTabCollaboration": "Samarbejde", "PE.Views.Toolbar.textTabDesign": "Design", "PE.Views.Toolbar.textTabDraw": "Draw", "PE.Views.Toolbar.textTabFile": "Fil", "PE.Views.Toolbar.textTabHome": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabInsert": "indsæt", "PE.Views.Toolbar.textTable": "Table", "PE.Views.Toolbar.textTabProtect": "Beskyttelse", "PE.Views.Toolbar.textTabTransitions": "Oversættelser", "PE.Views.Toolbar.textTabView": "Vis", "PE.Views.Toolbar.textText": "Text", "PE.Views.Toolbar.textTextVertical": "Text (Vertical)", "PE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "PE.Views.Toolbar.textTitle": "Title", "PE.Views.Toolbar.textTitleError": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "PE.Views.Toolbar.textUnderline": "Understreg", "PE.Views.Toolbar.textYen": "Yen Sign", "PE.Views.Toolbar.tipAddLayout": "Add layout", "PE.Views.Toolbar.tipAddSlide": "<PERSON><PERSON><PERSON><PERSON><PERSON>as", "PE.Views.Toolbar.tipAddSlideMaster": "Add slide master", "PE.Views.Toolbar.tipBack": "Tilbage", "PE.Views.Toolbar.tipChangeCase": "Ændre store/små bogstaver", "PE.Views.Toolbar.tipChangeChart": "Skift diagramtype", "PE.Views.Toolbar.tipChangeSlide": "Skift dias layout", "PE.Views.Toolbar.tipClearStyle": "Ryd formatering", "PE.Views.Toolbar.tipCloseMaster": "Close Master", "PE.Views.Toolbar.tipColorSchemas": "Skift farveskema", "PE.Views.Toolbar.tipColumns": "Indsæt kolonner", "PE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipCopyStyle": "Kopier formatering", "PE.Views.Toolbar.tipCut": "Cut", "PE.Views.Toolbar.tipDateTime": "Indsæt nuværende dato og tid", "PE.Views.Toolbar.tipDecFont": "Formindsk skriftstørrelsen", "PE.Views.Toolbar.tipDecPrLeft": "Formindsk indrykning", "PE.Views.Toolbar.tipEditHeaderFooter": "Edit header or footer", "PE.Views.Toolbar.tipFontColor": "Skriftfarve", "PE.Views.Toolbar.tipFontName": "Skrifttype", "PE.Views.Toolbar.tipFontSize": "Skriftstørrelse", "PE.Views.Toolbar.tipHAligh": "<PERSON><PERSON><PERSON> van<PERSON>", "PE.Views.Toolbar.tipHighlightColor": "Fr<PERSON>hæv farve", "PE.Views.Toolbar.tipIncFont": "Forøg skriftstørrel<PERSON>", "PE.Views.Toolbar.tipIncPrLeft": "Øg indrykningen", "PE.Views.Toolbar.tipInsertAudio": "Indsæt lyd", "PE.Views.Toolbar.tipInsertChart": "Indsæt diagram", "PE.Views.Toolbar.tipInsertChartPlaceholder": "Insert chart placeholder", "PE.Views.Toolbar.tipInsertContentPlaceholder": "Insert content placeholder", "PE.Views.Toolbar.tipInsertContentVerticalPlaceholder": "Insert content (vertical) placeholder", "PE.Views.Toolbar.tipInsertEquation": "Indsæt ligning", "PE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "PE.Views.Toolbar.tipInsertHyperlink": "Tilføj Hyperlink", "PE.Views.Toolbar.tipInsertImage": "Indsæt billede", "PE.Views.Toolbar.tipInsertPicturePlaceholder": "Insert picture placeholder", "PE.Views.Toolbar.tipInsertShape": "Indsæt automatisk form", "PE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "PE.Views.Toolbar.tipInsertSmartArtPlaceholder": "Insert SmartArt placeholder", "PE.Views.Toolbar.tipInsertSymbol": "Indsæt symbol", "PE.Views.Toolbar.tipInsertTable": "Indsæt tabel", "PE.Views.Toolbar.tipInsertTablePlaceholder": "Insert table placeholder", "PE.Views.Toolbar.tipInsertText": "Indsæt tekstboks", "PE.Views.Toolbar.tipInsertTextArt": "Indsæt tekstart", "PE.Views.Toolbar.tipInsertTextPlaceholder": "Insert text placeholder", "PE.Views.Toolbar.tipInsertTextVerticalPlaceholder": "Insert text (vertical) placeholder", "PE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "PE.Views.Toolbar.tipInsertVideo": "Indsæt video", "PE.Views.Toolbar.tipLineSpace": "<PERSON><PERSON>", "PE.Views.Toolbar.tipMarkers": "<PERSON><PERSON>", "PE.Views.Toolbar.tipMarkersArrow": "Arrow bullets", "PE.Views.Toolbar.tipMarkersCheckmark": "Checkmark bullets", "PE.Views.Toolbar.tipMarkersDash": "Dash bullets", "PE.Views.Toolbar.tipMarkersFRhombus": "Filled rhombus bullets", "PE.Views.Toolbar.tipMarkersFRound": "Filled round bullets", "PE.Views.Toolbar.tipMarkersFSquare": "Filled square bullets", "PE.Views.Toolbar.tipMarkersHRound": "Hollow round bullets", "PE.Views.Toolbar.tipMarkersStar": "Star bullets", "PE.Views.Toolbar.tipNone": "None", "PE.Views.Toolbar.tipNumbers": "Nummerering", "PE.Views.Toolbar.tipPaste": "Indsæt", "PE.Views.Toolbar.tipPreview": "Start diasshow", "PE.Views.Toolbar.tipPrint": "Print", "PE.Views.Toolbar.tipPrintQuick": "Quick print", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipReplace": "Replace", "PE.Views.Toolbar.tipSave": "Gem", "PE.Views.Toolbar.tipSaveCoauth": "Gem dine ændringer så de andre brugere kan se dem.", "PE.Views.Toolbar.tipSelectAll": "Select all", "PE.Views.Toolbar.tipShapeAlign": "<PERSON>ere formen", "PE.Views.Toolbar.tipShapeArrange": "Arrangere form", "PE.Views.Toolbar.tipShapesMerge": "Merge shapes", "PE.Views.Toolbar.tipSlideNum": "<PERSON><PERSON><PERSON>t dias-nummer", "PE.Views.Toolbar.tipSlideSize": "<PERSON><PERSON><PERSON><PERSON> dias størrel<PERSON>", "PE.Views.Toolbar.tipSlideTheme": "Dias tema", "PE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipVAligh": "<PERSON><PERSON><PERSON> justering", "PE.Views.Toolbar.tipViewSettings": "<PERSON><PERSON> in<PERSON>", "PE.Views.Toolbar.txtColors": "Colors", "PE.Views.Toolbar.txtDistribHor": "<PERSON><PERSON>", "PE.Views.Toolbar.txtDistribVert": "<PERSON><PERSON> lodret", "PE.Views.Toolbar.txtDuplicateSlide": "Duplicate slide", "PE.Views.Toolbar.txtGroup": "Gruppe", "PE.Views.Toolbar.txtObjectsAlign": "<PERSON><PERSON><PERSON> valgte genstande", "PE.Views.Toolbar.txtSlideAlign": "Tilpas til dias", "PE.Views.Toolbar.txtSlideSize": "Slide size", "PE.Views.Toolbar.txtUngroup": "Fjern fra gruppe", "PE.Views.Transitions.strDelay": "Forsinket", "PE.Views.Transitions.strDuration": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.strStartOnClick": "Start på Klik", "PE.Views.Transitions.textBlack": "Gennem sort", "PE.Views.Transitions.textBottom": "Bund", "PE.Views.Transitions.textBottomLeft": "Bund-venstre", "PE.Views.Transitions.textBottomRight": "Bund-højre", "PE.Views.Transitions.textClock": "<PERSON><PERSON>", "PE.Views.Transitions.textClockwise": "Med uret", "PE.Views.Transitions.textCounterclockwise": "<PERSON>d uret", "PE.Views.Transitions.textCover": "<PERSON><PERSON><PERSON><PERSON> over", "PE.Views.Transitions.textFade": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textHorizontalIn": "Vandret ind", "PE.Views.Transitions.textHorizontalOut": "<PERSON><PERSON><PERSON> ud", "PE.Views.Transitions.textLeft": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textMorph": "Morph", "PE.Views.Transitions.textMorphLetters": "Letters", "PE.Views.Transitions.textMorphObjects": "Objects", "PE.Views.Transitions.textMorphWord": "Words", "PE.Views.Transitions.textNone": "ingen", "PE.Views.Transitions.textPush": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textRandom": "Random", "PE.Views.Transitions.textRight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textSmoothly": "<PERSON><PERSON>", "PE.Views.Transitions.textSplit": "Dele", "PE.Views.Transitions.textTop": "Top", "PE.Views.Transitions.textTopLeft": "Top-venstre", "PE.Views.Transitions.textTopRight": "Top-højre", "PE.Views.Transitions.textUnCover": "Afdække", "PE.Views.Transitions.textVerticalIn": "Lodret ind", "PE.Views.Transitions.textVerticalOut": "<PERSON><PERSON><PERSON> ud", "PE.Views.Transitions.textWedge": "<PERSON><PERSON>", "PE.Views.Transitions.textWipe": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoom": "Zoom", "PE.Views.Transitions.textZoomIn": "Zoom ind", "PE.Views.Transitions.textZoomOut": "Zoom ud", "PE.Views.Transitions.textZoomRotate": "Zoom og rotér", "PE.Views.Transitions.txtApplyToAll": "<PERSON><PERSON><PERSON> på alle dias", "PE.Views.Transitions.txtParameters": "Parametre", "PE.Views.Transitions.txtPreview": "Forhåndvisning", "PE.Views.Transitions.txtSec": "S", "PE.Views.ViewTab.capBtnHand": "Hand", "PE.Views.ViewTab.capBtnSelect": "Select", "PE.Views.ViewTab.textAddHGuides": "Add horizontal guide", "PE.Views.ViewTab.textAddVGuides": "Add vertical guide", "PE.Views.ViewTab.textAlwaysShowToolbar": "Always Show Toolbar", "PE.Views.ViewTab.textClearGuides": "Clear guides", "PE.Views.ViewTab.textCm": "cm", "PE.Views.ViewTab.textCustom": "Custom", "PE.Views.ViewTab.textFill": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textFitToSlide": "Fit To Slide", "PE.Views.ViewTab.textFitToWidth": "Fit To <PERSON>th", "PE.Views.ViewTab.textGridlines": "Gridlines", "PE.Views.ViewTab.textGuides": "Guides", "PE.Views.ViewTab.textInterfaceTheme": "Interface tema", "PE.Views.ViewTab.textLeftMenu": "Left Panel", "PE.Views.ViewTab.textLine": "<PERSON><PERSON>", "PE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "PE.Views.ViewTab.textNormal": "Normal", "PE.Views.ViewTab.textNotes": "Notes", "PE.Views.ViewTab.textRightMenu": "Right Panel", "PE.Views.ViewTab.textRulers": "Rulers", "PE.Views.ViewTab.textShowGridlines": "Show Gridlines", "PE.Views.ViewTab.textShowGuides": "Show guides", "PE.Views.ViewTab.textSlideMaster": "Slide Master", "PE.Views.ViewTab.textSmartGuides": "Smart guides", "PE.Views.ViewTab.textSnapObjects": "Snap object to grid", "PE.Views.ViewTab.textStatusBar": "Status Bar", "PE.Views.ViewTab.textTabStyle": "Tab style", "PE.Views.ViewTab.textZoom": "Zoom", "PE.Views.ViewTab.tipFitToSlide": "Fit to slide", "PE.Views.ViewTab.tipFitToWidth": "Fit to width", "PE.Views.ViewTab.tipGridlines": "Show gridlines", "PE.Views.ViewTab.tipGuides": "Show guides", "PE.Views.ViewTab.tipHandTool": "Hand tool", "PE.Views.ViewTab.tipInterfaceTheme": "Interface tema", "PE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "PE.Views.ViewTab.tipNormal": "Normal", "PE.Views.ViewTab.tipSelectTool": "Select tool", "PE.Views.ViewTab.tipSlideMaster": "Slide master", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}