{"Common.Controllers.Chat.notcriticalErrorTitle": "Waarschuwing", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Anoni<PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "Sluiten", "Common.Controllers.ExternalDiagramEditor.warningText": "Het object is gede<PERSON><PERSON> omdat het wordt bewerkt door een andere gebruiker.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Waarschuwing", "Common.Controllers.ExternalOleEditor.textAnonymous": "Anonymous", "Common.Controllers.ExternalOleEditor.textClose": "Close", "Common.Controllers.ExternalOleEditor.warningText": "The object is disabled because it is being edited by another user.", "Common.Controllers.ExternalOleEditor.warningTitle": "Warning", "Common.Controllers.History.notcriticalErrorTitle": "Warning", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "<PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "Gestapeld gebied ", "Common.define.chartData.textAreaStackedPer": "100% gestapeld gebied ", "Common.define.chartData.textBar": "St<PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Geclusterde kolom ", "Common.define.chartData.textBarNormal3d": "3D geclusterde kolom", "Common.define.chartData.textBarNormal3dPerspective": "3D-kolom ", "Common.define.chartData.textBarStacked": "Gestapelde kolom ", "Common.define.chartData.textBarStacked3d": "3D gestapelde kolom ", "Common.define.chartData.textBarStackedPer": "100% gestapelde kolom ", "Common.define.chartData.textBarStackedPer3d": "3D 100% gestapelde kolom ", "Common.define.chartData.textCharts": "Grafieken", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Combo ", "Common.define.chartData.textComboAreaBar": "Gestapeld gebied - gec<PERSON>de kolom ", "Common.define.chartData.textComboBarLine": "Geclusterde kolom - lijn ", "Common.define.chartData.textComboBarLineSecondary": "Geclusterde kolom - lijn op secundaire as ", "Common.define.chartData.textComboCustom": "Aangepaste combinatie ", "Common.define.chartData.textDoughnut": "Donut", "Common.define.chartData.textHBarNormal": "Geclusterde staaf ", "Common.define.chartData.textHBarNormal3d": "3D geclusterde staaf ", "Common.define.chartData.textHBarStacked": "Gestapelde staaf ", "Common.define.chartData.textHBarStacked3d": "3D gestapelde staaf ", "Common.define.chartData.textHBarStackedPer": "100% gestapelde staaf ", "Common.define.chartData.textHBarStackedPer3d": "3D 100% gestapelde staaf ", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "3D-lijn ", "Common.define.chartData.textLineMarker": "Lijn met markeringen ", "Common.define.chartData.textLineStacked": "Gestapelde lijn ", "Common.define.chartData.textLineStackedMarker": "Gestapelde lijn met markeringen ", "Common.define.chartData.textLineStackedPer": "100% gestapelde lijn ", "Common.define.chartData.textLineStackedPerMarker": "100% gestapelde lijn met markeringen ", "Common.define.chartData.textPie": "<PERSON><PERSON>", "Common.define.chartData.textPie3d": "3D-taart ", "Common.define.chartData.textPoint": "Spreiding", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "<PERSON><PERSON><PERSON><PERSON><PERSON> met rechte lijnen ", "Common.define.chartData.textScatterLineMarker": "<PERSON><PERSON><PERSON><PERSON><PERSON> met rechte lijnen en markeringen ", "Common.define.chartData.textScatterSmooth": "<PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON> li<PERSON>en ", "Common.define.chartData.textScatterSmoothMarker": "<PERSON><PERSON><PERSON><PERSON><PERSON> met v<PERSON>eiend<PERSON> lijnen en markeringen ", "Common.define.chartData.textStock": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textSurface": "<PERSON><PERSON>v<PERSON>", "Common.define.effectData.textAcross": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textAppear": "Verschijnen", "Common.define.effectData.textArcDown": "Boog omlaag", "Common.define.effectData.textArcLeft": "Boog naar links", "Common.define.effectData.textArcRight": "Boog naar rechts", "Common.define.effectData.textArcs": "Bogen", "Common.define.effectData.textArcUp": "Boog omhoog", "Common.define.effectData.textBasic": "<PERSON><PERSON>", "Common.define.effectData.textBasicSwivel": "<PERSON><PERSON> d<PERSON>", "Common.define.effectData.textBasicZoom": "Basis zoomen", "Common.define.effectData.textBean": "<PERSON><PERSON>", "Common.define.effectData.textBlinds": "Jaloezieën", "Common.define.effectData.textBlink": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBoldFlash": "Duidelijke flits", "Common.define.effectData.textBoldReveal": "Toon vetgedrukt", "Common.define.effectData.textBoomerang": "Boemerang", "Common.define.effectData.textBounce": "Stuiteren", "Common.define.effectData.textBounceLeft": "<PERSON><PERSON>er naar links", "Common.define.effectData.textBounceRight": "<PERSON><PERSON><PERSON> naar rechts", "Common.define.effectData.textBox": "Tekstveld", "Common.define.effectData.textBrushColor": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCenterRevolve": "Rond het midden draaien", "Common.define.effectData.textCheckerboard": "Sc<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCircle": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCollapse": "Samenvouwen", "Common.define.effectData.textColorPulse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textComplementaryColor": "Complementaire kleur", "Common.define.effectData.textComplementaryColor2": "Complementaire kleur 2", "Common.define.effectData.textCompress": "Comprimeren", "Common.define.effectData.textContrast": "Contrast", "Common.define.effectData.textContrastingColor": "Contras<PERSON><PERSON><PERSON> kleur", "Common.define.effectData.textCredits": "Tegoeden", "Common.define.effectData.textCrescentMoon": "<PERSON>ve maan", "Common.define.effectData.textCurveDown": "Curve omlaag", "Common.define.effectData.textCurvedSquare": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCurvedX": "Gebogen X", "Common.define.effectData.textCurvyLeft": "Boog naar links", "Common.define.effectData.textCurvyRight": "Boog naar rechts", "Common.define.effectData.textCurvyStar": "<PERSON><PERSON><PERSON> ster", "Common.define.effectData.textCustomPath": "Aangepast pad", "Common.define.effectData.textCuverUp": "Curve omhoog", "Common.define.effectData.textDarken": "<PERSON><PERSON><PERSON> maken", "Common.define.effectData.textDecayingWave": "Afnemende golf", "Common.define.effectData.textDesaturate": "Desatureren", "Common.define.effectData.textDiagonalDownRight": "Diagonaal rechts onderaan", "Common.define.effectData.textDiagonalUpRight": "Diagonaal rechts bovenaan", "Common.define.effectData.textDiamond": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDisappear": "Verdwijnen", "Common.define.effectData.textDissolveIn": "Oplossen in", "Common.define.effectData.textDissolveOut": "Oplossen uit", "Common.define.effectData.textDown": "<PERSON><PERSON>", "Common.define.effectData.textDrop": "Vallen", "Common.define.effectData.textEmphasis": "Benadruk-effecten", "Common.define.effectData.textEntrance": "Ingangseffecten", "Common.define.effectData.textEqualTriangle": "Gelijke driehoek", "Common.define.effectData.textExciting": "Spannend", "Common.define.effectData.textExit": "Afsluiteffecten", "Common.define.effectData.textExpand": "Uitvouwen", "Common.define.effectData.textFade": "Vervagen", "Common.define.effectData.textFigureFour": "Figure 8 Four", "Common.define.effectData.textFillColor": "Opvulkleur", "Common.define.effectData.textFlip": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFloat": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFloatDown": "<PERSON><PERSON> ben<PERSON>", "Common.define.effectData.textFloatIn": "<PERSON><PERSON> bin<PERSON>", "Common.define.effectData.textFloatOut": "<PERSON><PERSON> buiten zweven", "Common.define.effectData.textFloatUp": "<PERSON>ar boven zweven", "Common.define.effectData.textFlyIn": "Invliegen", "Common.define.effectData.textFlyOut": "Uitvliegen", "Common.define.effectData.textFontColor": "Tekstkleur", "Common.define.effectData.textFootball": "Voetbal", "Common.define.effectData.textFromBottom": "<PERSON>", "Common.define.effectData.textFromBottomLeft": "<PERSON>", "Common.define.effectData.textFromBottomRight": "<PERSON>", "Common.define.effectData.textFromLeft": "Van links", "Common.define.effectData.textFromRight": "<PERSON>", "Common.define.effectData.textFromTop": "<PERSON> b<PERSON>", "Common.define.effectData.textFromTopLeft": "<PERSON>", "Common.define.effectData.textFromTopRight": "<PERSON>", "Common.define.effectData.textFunnel": "Funnel", "Common.define.effectData.textGrowShrink": "Grow/Shrink", "Common.define.effectData.textGrowTurn": "Grow & Turn", "Common.define.effectData.textGrowWithColor": "Grow with color", "Common.define.effectData.textHeart": "<PERSON>", "Common.define.effectData.textHeartbeat": "Hartslag", "Common.define.effectData.textHexagon": "Zeshoek", "Common.define.effectData.textHorizontal": "Horizontal", "Common.define.effectData.textHorizontalFigure": "Horizontal Figure 8", "Common.define.effectData.textHorizontalIn": "Horizontal In", "Common.define.effectData.textHorizontalOut": "Horizontal Out", "Common.define.effectData.textIn": "In", "Common.define.effectData.textInFromScreenCenter": "In from screen center", "Common.define.effectData.textInSlightly": "In slightly", "Common.define.effectData.textInToScreenBottom": "In to screen bottom", "Common.define.effectData.textInvertedSquare": "Inverted square", "Common.define.effectData.textInvertedTriangle": "Inverted triangle", "Common.define.effectData.textLeft": "Left", "Common.define.effectData.textLeftDown": "Linksonder", "Common.define.effectData.textLeftUp": "Linksboven", "Common.define.effectData.textLighten": "Lighten", "Common.define.effectData.textLineColor": "Line color", "Common.define.effectData.textLines": "Lines", "Common.define.effectData.textLinesCurves": "Lines curves", "Common.define.effectData.textLoopDeLoop": "Loop de Loop", "Common.define.effectData.textLoops": "Loops", "Common.define.effectData.textModerate": "Moderate", "Common.define.effectData.textNeutron": "Neutron", "Common.define.effectData.textObjectCenter": "Object center", "Common.define.effectData.textObjectColor": "Object color", "Common.define.effectData.textOctagon": "Octagon", "Common.define.effectData.textOut": "Out", "Common.define.effectData.textOutFromScreenBottom": "Out from screen bottom", "Common.define.effectData.textOutSlightly": "Out slightly", "Common.define.effectData.textOutToScreenCenter": "Out to screen center", "Common.define.effectData.textParallelogram": "Parallelogram", "Common.define.effectData.textPath": "Motion paths", "Common.define.effectData.textPathCurve": "Curve", "Common.define.effectData.textPathLine": "<PERSON><PERSON>", "Common.define.effectData.textPathScribble": "Scribble", "Common.define.effectData.textPeanut": "Peanut", "Common.define.effectData.textPeekIn": "Peek In", "Common.define.effectData.textPeekOut": "Peek Out", "Common.define.effectData.textPentagon": "Pentagon", "Common.define.effectData.textPinwheel": "Pinwheel", "Common.define.effectData.textPlus": "Plus", "Common.define.effectData.textPointStar": "Point Star", "Common.define.effectData.textPointStar4": "Vierpuntsster", "Common.define.effectData.textPointStar5": "Vijfpuntsster", "Common.define.effectData.textPointStar6": "Zespuntsster", "Common.define.effectData.textPointStar8": "Achtpuntsster", "Common.define.effectData.textPulse": "Pulse", "Common.define.effectData.textRandomBars": "Random Bars ", "Common.define.effectData.textRight": "Right", "Common.define.effectData.textRightDown": "Rechtsonder", "Common.define.effectData.textRightTriangle": "Right triangle", "Common.define.effectData.textRightUp": "Rechtsboven", "Common.define.effectData.textRiseUp": "Rise Up", "Common.define.effectData.textSCurve1": "S Curve 1", "Common.define.effectData.textSCurve2": "S Curve 2", "Common.define.effectData.textShape": "Vorm", "Common.define.effectData.textShapes": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textShimmer": "Shimmer", "Common.define.effectData.textShrinkTurn": "Shrink & Turn", "Common.define.effectData.textSineWave": "Sine Wave", "Common.define.effectData.textSinkDown": "Sink Down", "Common.define.effectData.textSlideCenter": "Slide Center", "Common.define.effectData.textSpecial": "Special", "Common.define.effectData.textSpin": "Spin", "Common.define.effectData.textSpinner": "Spinner", "Common.define.effectData.textSpiralIn": "Spiral In", "Common.define.effectData.textSpiralLeft": "<PERSON><PERSON><PERSON> left", "Common.define.effectData.textSpiralOut": "Spiral Out", "Common.define.effectData.textSpiralRight": "Spiral right", "Common.define.effectData.textSplit": "Split", "Common.define.effectData.textSpoke1": "1 spaak", "Common.define.effectData.textSpoke2": "2 spaken", "Common.define.effectData.textSpoke3": "3 spaken", "Common.define.effectData.textSpoke4": "4 spaken", "Common.define.effectData.textSpoke8": "8 spaken", "Common.define.effectData.textSpring": "Spring", "Common.define.effectData.textSquare": "Square", "Common.define.effectData.textStairsDown": "Stairs Down", "Common.define.effectData.textStretch": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textStrips": "Strips", "Common.define.effectData.textSubtle": "Subtle", "Common.define.effectData.textSwivel": "Swivel", "Common.define.effectData.textSwoosh": "Swoosh", "Common.define.effectData.textTeardrop": "Teardrop", "Common.define.effectData.textTeeter": "Teeter", "Common.define.effectData.textToBottom": "To bottom", "Common.define.effectData.textToBottomLeft": "To bottom-left", "Common.define.effectData.textToBottomRight": "To bottom-right", "Common.define.effectData.textToLeft": "To Left", "Common.define.effectData.textToRight": "To right", "Common.define.effectData.textToTop": "To top", "Common.define.effectData.textToTopLeft": "To top-left", "Common.define.effectData.textToTopRight": "To top-right", "Common.define.effectData.textTransparency": "Transparency", "Common.define.effectData.textTrapezoid": "Trapezoid", "Common.define.effectData.textTurnDown": "Turn down", "Common.define.effectData.textTurnDownRight": "Turn down right", "Common.define.effectData.textTurns": "Turns", "Common.define.effectData.textTurnUp": "Turn up", "Common.define.effectData.textTurnUpRight": "Turn up right", "Common.define.effectData.textUnderline": "Underline", "Common.define.effectData.textUp": "Up", "Common.define.effectData.textVertical": "Vertical", "Common.define.effectData.textVerticalFigure": "Vertical Figure 8", "Common.define.effectData.textVerticalIn": "Vertical In", "Common.define.effectData.textVerticalOut": "Vertical Out", "Common.define.effectData.textWave": "Wave", "Common.define.effectData.textWedge": "Wedge", "Common.define.effectData.textWheel": "Wheel", "Common.define.effectData.textWhip": "Whip", "Common.define.effectData.textWipe": "Wipe", "Common.define.effectData.textZigzag": "Zigzag", "Common.define.effectData.textZoom": "Zoom", "Common.define.gridlineData.txtCm": "cm", "Common.define.gridlineData.txtPt": "pt", "Common.define.smartArt.textAccentedPicture": "Accented picture", "Common.define.smartArt.textAccentProcess": "Accent process", "Common.define.smartArt.textAlternatingFlow": "Alternating flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating Hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating picture blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating picture circles", "Common.define.smartArt.textArchitectureLayout": "Architecture layout", "Common.define.smartArt.textArrowRibbon": "Arrow ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending picture accent process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic bending process", "Common.define.smartArt.textBasicBlockList": "Basic block list", "Common.define.smartArt.textBasicChevronProcess": "Basic chevron process", "Common.define.smartArt.textBasicCycle": "Basic cycle", "Common.define.smartArt.textBasicMatrix": "Basic matrix", "Common.define.smartArt.textBasicPie": "Basic pie", "Common.define.smartArt.textBasicProcess": "Basic process", "Common.define.smartArt.textBasicPyramid": "Basic pyramid", "Common.define.smartArt.textBasicRadial": "Basic radial", "Common.define.smartArt.textBasicTarget": "Basic target", "Common.define.smartArt.textBasicTimeline": "Basic timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending picture accent list", "Common.define.smartArt.textBendingPictureBlocks": "Bending picture blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending picture caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending picture caption list", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending picture semi-transparent text", "Common.define.smartArt.textBlockCycle": "Block cycle", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron accent process", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Circle accent timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging Arrows", "Common.define.smartArt.textDivergingRadial": "Diverging Radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy List", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined list", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and title organization chart", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing Ideas", "Common.define.smartArt.textOrganizationChart": "Organization chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased Process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture accent blocks", "Common.define.smartArt.textPictureAccentList": "Picture accent list", "Common.define.smartArt.textPictureAccentProcess": "Picture accent process", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture Strips", "Common.define.smartArt.textPieProcess": "Pie Process", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial Cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "Spiral picture", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Tabbed <PERSON>", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward Arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "More", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "Het bestand wordt bewerkt in een andere app. U kunt doorgaan met bewerken en als kopie opslaan.", "Common.Translation.warnFileLockedBtnEdit": "Maak een kopie", "Common.Translation.warnFileLockedBtnView": "Open voor lezen", "Common.UI.ButtonColored.textAutoColor": "Automatisch", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "Nieuwe aangepaste kleur", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON> randen", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON> randen", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.addButtonText": "Toevoegen", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textHexErr": "De ingevoerde waarde is onjuist.<br><PERSON><PERSON><PERSON> een waarde tussen 000000 en FFFFFF in.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "De ingevoerde waarde is onjuist.<br><PERSON><PERSON><PERSON> een numerieke waarde tussen 0 en 255 in.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON> k<PERSON>ur", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Wachtwoord verbergen", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Show password", "Common.UI.SearchBar.textFind": "<PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "Zoekopdracht sluiten", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Previous result", "Common.UI.SearchDialog.textHighlight": "Resultaten markeren", "Common.UI.SearchDialog.textMatchCase": "Hoofdlettergevoelig", "Common.UI.SearchDialog.textReplaceDef": "Voer de vervangende tekst in", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON> hier uw tekst in", "Common.UI.SearchDialog.textTitle": "Zoeken en vervangen", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "<PERSON>een hele woorden", "Common.UI.SearchDialog.txtBtnHideReplace": "Vervanging verbergen", "Common.UI.SearchDialog.txtBtnReplace": "Vervangen", "Common.UI.SearchDialog.txtBtnReplaceAll": "Alles vervangen", "Common.UI.SynchronizeTip.textDontShow": "Dit bericht niet meer weergeven", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Het document is gewij<PERSON>d door een andere gebruiker.<br>Klik om uw wijzigingen op te slaan en de updates opnieuw te laden.", "Common.UI.ThemeColorPalette.textRecentColors": "Recent colors", "Common.UI.ThemeColorPalette.textStandartColors": "Standaardkleuren", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klassiek Licht", "Common.UI.Themes.txtThemeContrastDark": "Contrast donker", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeGray": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "Licht", "Common.UI.Themes.txtThemeSystem": "<PERSON><PERSON><PERSON> als systeem", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "Sluiten", "Common.UI.Window.noButtonText": "<PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Bevestiging", "Common.UI.Window.textDontShow": "Dit bericht niet meer weergeven", "Common.UI.Window.textError": "Fout", "Common.UI.Window.textInformation": "Informatie", "Common.UI.Window.textWarning": "Waarschuwing", "Common.UI.Window.yesButtonText": "<PERSON>a", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Background", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Blue", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Dark green", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "adres:", "Common.Views.About.txtLicensee": "LICENTIEHOUDER", "Common.Views.About.txtLicensor": "LICENTIEVERLENER", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "Aangedreven door", "Common.Views.About.txtTel": "tel.:", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "Toevoegen", "Common.Views.AutoCorrectDialog.textApplyText": "Toepassen terwijl u typt", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Tekst AutoCorrectie", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoOpmaak terwijl u typt", "Common.Views.AutoCorrectDialog.textBulleted": "Automatische lijsten met opsommingstekens", "Common.Views.AutoCorrectDialog.textBy": "Door", "Common.Views.AutoCorrectDialog.textDelete": "Verwijderen", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "<PERSON><PERSON><PERSON> punt toe met dub<PERSON><PERSON> spatie", "Common.Views.AutoCorrectDialog.textFLCells": "Kapitaliseer eerste letter in tabelcellen", "Common.Views.AutoCorrectDialog.textFLDont": "Don`t capitalize after", "Common.Views.AutoCorrectDialog.textFLSentence": "Maak de e<PERSON>te letter van de zin hoofdletter", "Common.Views.AutoCorrectDialog.textForLangFL": "Exceptions for the language:", "Common.Views.AutoCorrectDialog.textHyperlink": " Internet en netwerkpaden met hyperlinks", "Common.Views.AutoCorrectDialog.textHyphens": "<PERSON><PERSON><PERSON><PERSON><PERSON> (--) met <PERSON><PERSON><PERSON><PERSON> (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Wiskundige autocorrectie", "Common.Views.AutoCorrectDialog.textNumbered": "Automatische genummerde lijsten", "Common.Views.AutoCorrectDialog.textQuotes": "\"Rechte aanhalingstekens\" met \"slimme aanhalingstekens\"", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON>rkende functies", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "De volgende uitdrukkingen zijn erkende wiskunde uitdrukkingen. Ze worden niet automatisch gecursiveerd.", "Common.Views.AutoCorrectDialog.textReplace": "Vervangen", "Common.Views.AutoCorrectDialog.textReplaceText": "Vervangen terwijl u typt", "Common.Views.AutoCorrectDialog.textReplaceType": "Tekst vervangen terwijl u typt", "Common.Views.AutoCorrectDialog.textReset": "Opnieuw instellen", "Common.Views.AutoCorrectDialog.textResetAll": "Terugzetten naar standaardinstelling", "Common.Views.AutoCorrectDialog.textRestore": "Herstellen", "Common.Views.AutoCorrectDialog.textTitle": "Automatische spellingscontrole", "Common.Views.AutoCorrectDialog.textWarnAddFL": "Exceptions must contain only the letters, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Erkende functies mogen alleen de letters A tot en met <PERSON>, hoofdletters of kleine letters.", "Common.Views.AutoCorrectDialog.textWarnResetFL": "Any exceptions you added will be removed and the removed ones will be restored. Do you want to continue?", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Elke uitdrukking die u hebt toegevoegd zal worden verwijderd en de verwijderde zullen worden hersteld. Wilt u doorgaan?", "Common.Views.AutoCorrectDialog.warnReplace": "De autocorrectie voor %1 bestaat al. Wilt u ze vervangen?", "Common.Views.AutoCorrectDialog.warnReset": "Elke autocorrectie die u hebt toegevoegd zal worden verwijderd en de gewijzigde zullen worden hersteld naar hun oorspronkelijke waarden. Wilt u doorgaan?", "Common.Views.AutoCorrectDialog.warnRestore": "De autocorrectie voor %1 wordt teruggezet naar de oorspronkelijke waarde. Wilt u doorgaan?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "Verzenden", "Common.Views.Comments.mniAuthorAsc": "Auteur A tot Z", "Common.Views.Comments.mniAuthorDesc": "Auteur Z tot A", "Common.Views.Comments.mniDateAsc": "Oudste", "Common.Views.Comments.mniDateDesc": "Nieuwste", "Common.Views.Comments.mniFilterGroups": "Filter per groep", "Common.Views.Comments.mniPositionAsc": "<PERSON>", "Common.Views.Comments.mniPositionDesc": "<PERSON>", "Common.Views.Comments.textAdd": "Toevoegen", "Common.Views.Comments.textAddComment": "Opmerking toevoegen", "Common.Views.Comments.textAddCommentToDoc": "Opmerking toevoegen aan document", "Common.Views.Comments.textAddReply": "Antwoord toevoegen", "Common.Views.Comments.textAll": "Alle", "Common.Views.Comments.textAnonym": "Gas<PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "Sluiten", "Common.Views.Comments.textClosePanel": "Opmerkingen sluiten", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "Opmerkingen", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON> hier uw opmerking in", "Common.Views.Comments.textHintAddComment": "Opmerking Toevoegen", "Common.Views.Comments.textOpenAgain": "Opnieuw openen", "Common.Views.Comments.textReply": "Beantwoorden", "Common.Views.Comments.textResolve": "Oplossen", "Common.Views.Comments.textResolved": "Opgelost", "Common.Views.Comments.textSort": "Opmerkingen sorteren", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "You have no permission to reopen the comment", "Common.Views.Comments.txtEmpty": "There are no comments in the document.", "Common.Views.CopyWarningDialog.textDontShow": "Dit bericht niet meer weergeven", "Common.Views.CopyWarningDialog.textMsg": "De acties <PERSON>, <PERSON><PERSON><PERSON><PERSON> en Plakken kunnen alleen met <PERSON><PERSON><PERSON> van de knoppen op de werkbalk en de acties in het contextmenu worden uitgevoerd op dit tabblad van de editor.<br><br>Als u wilt kopiëren naar of plakken van toepassingen buiten het tabblad van de editor, gebruikt u de volgende toetsencombinaties:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON>, Knippen en Plakken", "Common.Views.CopyWarningDialog.textToCopy": "voor kopiëren", "Common.Views.CopyWarningDialog.textToCut": "voor knippen", "Common.Views.CopyWarningDialog.textToPaste": "voor plakken", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textStartOver": "Show from Beginning", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Laden...", "Common.Views.DocumentAccessDialog.textTitle": "Instellingen voor delen", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "Select", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "Select", "Common.Views.Draw.txtSize": "Size", "Common.Views.ExternalDiagramEditor.textTitle": "Grafiekeditor", "Common.Views.ExternalEditor.textClose": "Close", "Common.Views.ExternalEditor.textSave": "Save & Exit", "Common.Views.ExternalOleEditor.textTitle": "Spreadsheet Editor", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Document wordt op dit moment bewerkt door verschillende gebruikers.", "Common.Views.Header.textAddFavorite": "<PERSON><PERSON> als favoriet", "Common.Views.Header.textAdvSettings": "Geavanceerde instellingen", "Common.Views.Header.textBack": "Naar documenten", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "Werkbalk Verbergen", "Common.Views.Header.textHideLines": "Linialen verbergen", "Common.Views.Header.textHideNotes": "<PERSON><PERSON><PERSON> notities", "Common.Views.Header.textHideStatusBar": "Statusbalk verbergen", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Verwijder uit favorieten", "Common.Views.Header.textSaveBegin": "Opslaan...", "Common.Views.Header.textSaveChanged": "Gewijzigd", "Common.Views.Header.textSaveEnd": "Alle wijzigingen zijn opgeslagen.", "Common.Views.Header.textSaveExpander": "Alle wijzigingen zijn opgeslagen.", "Common.Views.Header.textShare": "Share", "Common.Views.Header.textStartOver": "Show from Beginning", "Common.Views.Header.textZoom": "Zoomen", "Common.Views.Header.tipAccessRights": "Toegangsrechten van documenten beheren", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "Bestand downloaden", "Common.Views.Header.tipGoEdit": "<PERSON>dig bestand bewerken", "Common.Views.Header.tipPrint": "Bestand afdrukken", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "Opnieuw", "Common.Views.Header.tipSave": "Opsla<PERSON>", "Common.Views.Header.tipSearch": "Find", "Common.Views.Header.tipStartOver": "Start slideshow from beginning", "Common.Views.Header.tipUndo": "Ongedaan maken", "Common.Views.Header.tipUndock": "Ontkoppel in een apart venster", "Common.Views.Header.tipUsers": "Toon gebruikers", "Common.Views.Header.tipViewSettings": "Weergave-instellingen", "Common.Views.Header.tipViewUsers": "Gebruikers weergeven en toegangsrechten voor documenten beheren", "Common.Views.Header.txtAccessRights": "Toegangsrechten wijzigen", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textCloseHistory": "Sluit geschiedenis", "Common.Views.History.textHideAll": "Details van wijzigingen verbergen", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "Herstellen", "Common.Views.History.textShowAll": "Details van wijzigingen weergeven", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "URL van een afbeelding plakken:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Dit veld is vereist", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Dit veld moet een URL in de notatie \"http://www.voorbeeld.com\" bevatten", "Common.Views.InsertTableDialog.textInvalidRowsCols": "U moet een geldig aantal rijen en kolommen opgeven.", "Common.Views.InsertTableDialog.txtColumns": "Aantal kolommen", "Common.Views.InsertTableDialog.txtMaxText": "De maximumwaarde voor dit veld is {0}.", "Common.Views.InsertTableDialog.txtMinText": "De minimumwaarde voor dit veld is {0}.", "Common.Views.InsertTableDialog.txtRows": "Aantal rijen", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON>", "Common.Views.LanguageDialog.labelSelect": "Taal van document selecteren", "Common.Views.ListSettingsDialog.textBulleted": "Opgesomd ", "Common.Views.ListSettingsDialog.textFromFile": "From file", "Common.Views.ListSettingsDialog.textFromStorage": "From storage", "Common.Views.ListSettingsDialog.textFromUrl": "From URL", "Common.Views.ListSettingsDialog.textNumbering": "Genummerd", "Common.Views.ListSettingsDialog.textSelect": "Select from", "Common.Views.ListSettingsDialog.tipChange": "Verander opsommingsteken", "Common.Views.ListSettingsDialog.txtBullet": "Opsommingsteken", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Image", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "Nieuw opsommingsteken", "Common.Views.ListSettingsDialog.txtNewImage": "New image", "Common.Views.ListSettingsDialog.txtNone": "geen", "Common.Views.ListSettingsDialog.txtOfText": "% van tekst", "Common.Views.ListSettingsDialog.txtSize": "Grootte", "Common.Views.ListSettingsDialog.txtStart": "Beginnen bij", "Common.Views.ListSettingsDialog.txtSymbol": "symbool", "Common.Views.ListSettingsDialog.txtTitle": "Lijst instellingen", "Common.Views.ListSettingsDialog.txtType": "Type", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "Bestand sluiten", "Common.Views.OpenDialog.txtEncoding": "Codering", "Common.Views.OpenDialog.txtIncorrectPwd": "Wachtwoord is niet juist", "Common.Views.OpenDialog.txtOpenFile": "<PERSON>oer een wachtwoord in om dit bestand te openen", "Common.Views.OpenDialog.txtPassword": "Wachtwoord", "Common.Views.OpenDialog.txtProtected": "Nadat u het wachtwoord heeft ingevoerd en het bestand heeft geopend, wordt het huidige wachtwoord voor het bestand gereset.", "Common.Views.OpenDialog.txtTitle": "Opties voor %1 kiezen", "Common.Views.OpenDialog.txtTitleProtected": "Beschermd bestand", "Common.Views.PasswordDialog.txtDescription": "Pas een wachtwoord toe om dit document te beveiligen", "Common.Views.PasswordDialog.txtIncorrectPwd": "Bevestig wachtwoord is niet identiek", "Common.Views.PasswordDialog.txtPassword": "Wachtwoord", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON> wa<PERSON>", "Common.Views.PasswordDialog.txtTitle": "Wachtwoord instellen", "Common.Views.PasswordDialog.txtWarning": "Waarschuwing: Als u het wachtwoord kwijtraakt of vergeet, kan dit niet meer worden hersteld. Be<PERSON><PERSON> deze op een veilige plaats.", "Common.Views.PluginDlg.textLoading": "Laden", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Plug-ins", "Common.Views.Plugins.strPlugins": "Plug-ins", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "Starten", "Common.Views.Plugins.textStop": "Stoppen", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "Vers<PERSON><PERSON>len met wachtwo<PERSON>", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "<PERSON><PERSON> of verwijder wachtwoord", "Common.Views.Protection.hintSignature": "Digitale handtekening toevoegen of handtekening lijn", "Common.Views.Protection.txtAddPwd": "Wachtwoord toevoegen", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON> wa<PERSON>", "Common.Views.Protection.txtDeletePwd": "Wachtwoord verwijderen", "Common.Views.Protection.txtEncrypt": "Versleutelen", "Common.Views.Protection.txtInvisibleSignature": "Digitale handtekening toevoegen", "Common.Views.Protection.txtSignature": "Handtekening", "Common.Views.Protection.txtSignatureLine": "Handtekening lijn", "Common.Views.RecentFiles.txtOpenRecent": "Open Recent", "Common.Views.RenameDialog.textName": "Bestandsnaam", "Common.Views.RenameDialog.txtInvalidName": "De bestandsnaam mag geen van de volgende tekens bevatten:", "Common.Views.ReviewChanges.hintNext": "Naar volgende wijziging", "Common.Views.ReviewChanges.hintPrev": "Naar vorige wij<PERSON>", "Common.Views.ReviewChanges.strFast": "Snel", "Common.Views.ReviewChanges.strFastDesc": "Real-time samenwerken. Alle wijzigingen worden automatisch opgeslagen.", "Common.Views.ReviewChanges.strStrict": "Strikt", "Common.Views.ReviewChanges.strStrictDesc": "Gebruik de 'Opslaan' knop om de wijzigingen van u en andere te synchroniseren.", "Common.Views.ReviewChanges.tipAcceptCurrent": "<PERSON><PERSON><PERSON> wij<PERSON>ing accepteren", "Common.Views.ReviewChanges.tipCoAuthMode": "<PERSON>et samenwerkings modus", "Common.Views.ReviewChanges.tipCommentRem": "Alle opmerkingen verwijderen", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Verwijder huidige opmerking", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON> van opmerkingen", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Oplossen van huidige opmerkingen", "Common.Views.ReviewChanges.tipHistory": "Toon versie geschiedenis", "Common.Views.ReviewChanges.tipRejectCurrent": "Huidige wij<PERSON>ing afwijzen", "Common.Views.ReviewChanges.tipReview": "Wijzigingen bijhouden", "Common.Views.ReviewChanges.tipReviewView": "Selecteer de modus waarin u de veranderingen weer wilt laten geven", "Common.Views.ReviewChanges.tipSetDocLang": "Taal van document instellen", "Common.Views.ReviewChanges.tipSetSpelling": "Spellingcon<PERSON>le", "Common.Views.ReviewChanges.tipSharing": "Toegangsrechten documenten beheren", "Common.Views.ReviewChanges.txtAccept": "Accept<PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "Alle wijzigingen accepteren", "Common.Views.ReviewChanges.txtAcceptChanges": "Wijzigingen Accepteren", "Common.Views.ReviewChanges.txtAcceptCurrent": "<PERSON><PERSON><PERSON> wij<PERSON>ing accepteren", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "Sluiten", "Common.Views.ReviewChanges.txtCoAuthMode": "Modus Gezamenlijk bewerken", "Common.Views.ReviewChanges.txtCommentRemAll": "Alle commentaar verwijderen", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Verwijder huidige opmerking", "Common.Views.ReviewChanges.txtCommentRemMy": "Verwijder al mijn commentaar", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Verwijder mijn huidige opmerkingen", "Common.Views.ReviewChanges.txtCommentRemove": "Verwijderen", "Common.Views.ReviewChanges.txtCommentResolve": "Oplossen", "Common.Views.ReviewChanges.txtCommentResolveAll": "Alle opmerkingen oplossen", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Oplossen van huidige opmerkingen", "Common.Views.ReviewChanges.txtCommentResolveMy": "Los mijn opmerkingen op", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Mijn huidige opmerkingen oplossen", "Common.Views.ReviewChanges.txtDocLang": "Taal", "Common.Views.ReviewChanges.txtFinal": "Alle veranderingen geaccepteerd (Voorbeeld)", "Common.Views.ReviewChanges.txtFinalCap": "Einde", "Common.Views.ReviewChanges.txtHistory": "Vers<PERSON> geschiedenis", "Common.Views.ReviewChanges.txtMarkup": "Alle veranderingen (Bewerken)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Volgende", "Common.Views.ReviewChanges.txtOriginal": "Alle veranderingen afgekeurd (Voorbeeld)", "Common.Views.ReviewChanges.txtOriginalCap": "Origineel", "Common.Views.ReviewChanges.txtPrev": "Vorige", "Common.Views.ReviewChanges.txtReject": "Afwijzen", "Common.Views.ReviewChanges.txtRejectAll": "Alle wijzigingen afwijzen", "Common.Views.ReviewChanges.txtRejectChanges": "Wijzigingen Afwijzen", "Common.Views.ReviewChanges.txtRejectCurrent": "Huidige wij<PERSON>ing afwijzen", "Common.Views.ReviewChanges.txtSharing": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "Spellingcon<PERSON>le", "Common.Views.ReviewChanges.txtTurnon": "Wijzigingen bijhouden", "Common.Views.ReviewChanges.txtView": "Weergavemodus", "Common.Views.ReviewPopover.textAdd": "Toevoegen", "Common.Views.ReviewPopover.textAddReply": "Antwoord toevoegen", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "Afsluiten", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+vermelding zal de gebruiker toegang geven tot het document en een email sturen", "Common.Views.ReviewPopover.textMentionNotify": "+geno<PERSON>de zal de gebruiker via email melden", "Common.Views.ReviewPopover.textOpenAgain": "Opnieuw openen", "Common.Views.ReviewPopover.textReply": "Beantwoorden", "Common.Views.ReviewPopover.textResolve": "Oplossen", "Common.Views.ReviewPopover.textViewResolved": "You have no permission to reopen the comment", "Common.Views.ReviewPopover.txtDeleteTip": "Verwijderen", "Common.Views.ReviewPopover.txtEditTip": "Bewerken", "Common.Views.SaveAsDlg.textLoading": "Laden", "Common.Views.SaveAsDlg.textTitle": "Map voor opslaan", "Common.Views.SearchPanel.textCaseSensitive": "Hoofdlettergevoelig", "Common.Views.SearchPanel.textCloseSearch": "Zoekopdracht sluiten", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "Zoeken en vervangen", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} items successfully replaced.", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} items replaced. Remaining {2} items are locked by other users.", "Common.Views.SearchPanel.textReplace": "Replace", "Common.Views.SearchPanel.textReplaceAll": "Replace All", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "Search has stopped", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textTooManyResults": "There are too many results to show here", "Common.Views.SearchPanel.textWholeWords": "Whole words only", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "Previous result", "Common.Views.SelectFileDlg.textLoading": "Laden", "Common.Views.SelectFileDlg.textTitle": "Gegevensbron selecteren", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Vet", "Common.Views.SignDialog.textCertificate": "Certificaat", "Common.Views.SignDialog.textChange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "<PERSON><PERSON> onder<PERSON>aar invoeren", "Common.Views.SignDialog.textItalic": "Cursief", "Common.Views.SignDialog.textNameError": "<PERSON><PERSON> on<PERSON> mag niet leeg zijn. ", "Common.Views.SignDialog.textPurpose": "Doel voor het ondertekenen van dit document", "Common.Views.SignDialog.textSelect": "Selecteren", "Common.Views.SignDialog.textSelectImage": "Selecteer af<PERSON><PERSON>", "Common.Views.SignDialog.textSignature": "Handtekening lijkt op", "Common.Views.SignDialog.textTitle": "Onderteken document", "Common.Views.SignDialog.textUseImage": "of klik 'Selecteer afbeelding' om een afbeelding als handtekening te gebruiken", "Common.Views.SignDialog.textValid": "Geldig van %1 tot %2", "Common.Views.SignDialog.tipFontName": "Lettertype", "Common.Views.SignDialog.tipFontSize": "Tekengrootte", "Common.Views.SignSettingsDialog.textAllowComment": "Sta ondertekenaar toe commentaar toe te voegen in het handtekening venster.", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "<PERSON><PERSON>", "Common.Views.SignSettingsDialog.textInfoTitle": "Ondertekenaar titel", "Common.Views.SignSettingsDialog.textInstructions": "Instructies voor ondertekenaar", "Common.Views.SignSettingsDialog.textShowDate": "Toon signeer datum in handte<PERSON> regel", "Common.Views.SignSettingsDialog.textTitle": "Handtekening opzet", "Common.Views.SignSettingsDialog.txtEmpty": "Dit veld is vereist", "Common.Views.SymbolTableDialog.textCharacter": "Teken", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX-waarde", "Common.Views.SymbolTableDialog.textCopyright": "Copyright teken", "Common.Views.SymbolTableDialog.textDCQuote": "Aanhalingsteken sluiten", "Common.Views.SymbolTableDialog.textDOQuote": "Dubbel aanhalingsteken openen", "Common.Views.SymbolTableDialog.textEllipsis": "Horizontale ellips", "Common.Views.SymbolTableDialog.textEmDash": "streep", "Common.Views.SymbolTableDialog.textEmSpace": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnDash": "<PERSON><PERSON> streep", "Common.Views.SymbolTableDialog.textEnSpace": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textFont": "Lettertype", "Common.Views.SymbolTableDialog.textNBHyphen": "Niet-afbrekenden koppelteken", "Common.Views.SymbolTableDialog.textNBSpace": "niet-afbrekende spatie", "Common.Views.SymbolTableDialog.textPilcrow": "Alineateken", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 <PERSON> spatie", "Common.Views.SymbolTableDialog.textRange": "Be<PERSON>ik", "Common.Views.SymbolTableDialog.textRecent": "Recent gebruikte symbolen", "Common.Views.SymbolTableDialog.textRegistered": "Geregistreerd teken", "Common.Views.SymbolTableDialog.textSCQuote": "Enkele aanhalingsteken sluiten", "Common.Views.SymbolTableDialog.textSection": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textShortcut": "Sneltoets", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON> kop<PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Een enkel citaat openen", "Common.Views.SymbolTableDialog.textSpecial": "speciale karakters", "Common.Views.SymbolTableDialog.textSymbols": "Symbolen", "Common.Views.SymbolTableDialog.textTitle": "symbool", "Common.Views.SymbolTableDialog.textTradeMark": "Handelsmerk symbool", "Common.Views.UserNameDialog.textDontShow": "Vraag me niet nog een keer ", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label mag niet leeg zijn", "PE.Controllers.DocumentHolder.textLongName": "Enter a name that is less than 255 characters.", "PE.Controllers.DocumentHolder.textNameLayout": "Layout name", "PE.Controllers.DocumentHolder.textNameMaster": "Master name", "PE.Controllers.DocumentHolder.textRenameTitleLayout": "<PERSON><PERSON>out", "PE.Controllers.DocumentHolder.textRenameTitleMaster": "<PERSON><PERSON>", "PE.Controllers.LeftMenu.leavePageText": "Alle niet-opgeslagen wijzigingen in dit document gaan verloren. <br> <PERSON><PERSON> op \"Annuleren\" en dan op \"Opslaan\" om ze op te slaan. Klik op \"OK\" om alle niet-opgeslagen wijzigingen te verwijderen.", "PE.Controllers.LeftMenu.newDocumentTitle": "<PERSON><PERSON><PERSON> z<PERSON>er naam", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Waarschuwing", "PE.Controllers.LeftMenu.requestEditRightsText": "Bewerkrechten worden aangevraagd...", "PE.Controllers.LeftMenu.textLoadHistory": "Versiehistorie wordt geladen...", "PE.Controllers.LeftMenu.textNoTextFound": "De gegevens waar<PERSON>ar u zoekt, zijn niet gevonden. Pas uw zoekopties aan.", "PE.Controllers.LeftMenu.textReplaceSkipped": "De vervanging is uitgevoerd. {0} g<PERSON><PERSON> zijn overgeslagen.", "PE.Controllers.LeftMenu.textReplaceSuccess": "De zoekactie is uitgevoerd. Vervangen items: {0}", "PE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "PE.Controllers.LeftMenu.txtUntitled": "Naamloos", "PE.Controllers.Main.applyChangesTextText": "Gegevens worden geladen...", "PE.Controllers.Main.applyChangesTitleText": "Gegevens worden geladen", "PE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "PE.Controllers.Main.convertationTimeoutText": "Time-out voor conversie overschreden.", "PE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON> op \"OK\" om terug te keren naar de lijst met documenten.", "PE.Controllers.Main.criticalErrorTitle": "Fout", "PE.Controllers.Main.downloadErrorText": "Download mislukt.", "PE.Controllers.Main.downloadTextText": "Presentatie wordt gedownload...", "PE.Controllers.Main.downloadTitleText": "Presentatie wordt gedownload", "PE.Controllers.Main.errorAccessDeny": "U probeert een actie uit te voeren waarvoor u geen rechten hebt.<br><PERSON><PERSON>m alstublieft contact op met de behee<PERSON><PERSON> van de documentserver.", "PE.Controllers.Main.errorBadImageUrl": "URL afbeelding is onjuist", "PE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the presentation.", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Verbinding met server is verbroken. Het document kan op dit moment niet worden bewerkt.", "PE.Controllers.Main.errorComboSeries": "Selecteer ten minste twee reeksen gegevens om een combinatiediagram te maken. ", "PE.Controllers.Main.errorConnectToServer": "Het document kan niet worden opgeslagen. Controleer de verbindingsinstellingen of neem contact op met de beheerder.<br><PERSON><PERSON> u op de knop 'OK' klikt, wordt u gevraagd het document te downloaden.", "PE.Controllers.Main.errorDatabaseConnection": "Externe fout.<br>Fout in databaseverbinding. Neem contact op met Support als deze fout zich blijft voordoen.", "PE.Controllers.Main.errorDataEncrypted": "Versleutelde wijzigingen zijn ontvangen, deze kunnen niet ontcij<PERSON>d worden.", "PE.Controllers.Main.errorDataRange": "Onjuist gegevensbereik.", "PE.Controllers.Main.errorDefaultMessage": "Foutcode: %1", "PE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "PE.Controllers.Main.errorEditingDownloadas": "Er is een fout ontstaan tijdens het werken met het document.<br><PERSON><PERSON><PERSON><PERSON> de 'Opslaan als' optie om het bestand als backup op te slaan op uw computer.", "PE.Controllers.Main.errorEditingSaveas": "Er is een fout ontstaan tijdens het werken met het document.<br><PERSON><PERSON><PERSON><PERSON> de 'Opslaan als...' optie om het bestand als backup op te slaan op uw computer.", "PE.Controllers.Main.errorEmailClient": "Er is geen e-mail client gevonden.", "PE.Controllers.Main.errorFilePassProtect": "Het bestand is be<PERSON>md met een wachtwoord en kan niet worden geopend.", "PE.Controllers.Main.errorFileSizeExceed": "De bestandsgrootte overschrijdt de limiet die is ingesteld voor uw server. <br>Neem alstublieft contact op met de beheerder van de documentserver voor verdere details.", "PE.Controllers.Main.errorForceSave": "Er is een fout ontstaan bij het opslaan van het bestand. Gebruik de 'Download als' knop om het bestand op te slaan op uw computer of probeer het later nog eens.", "PE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "PE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "PE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "PE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "PE.Controllers.Main.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "PE.Controllers.Main.errorKeyEncrypt": "<PERSON><PERSON><PERSON><PERSON> sleuteldescriptor", "PE.Controllers.Main.errorKeyExpire": "Sleuteldescriptor vervallen", "PE.Controllers.Main.errorLoadingFont": "Lettertypes zijn niet geladen.\nNeem alstublieft contact op met de behee<PERSON><PERSON> van de documentserver.", "PE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON><PERSON> mislukt.", "PE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "PE.Controllers.Main.errorServerVersion": "De versie van de editor is bijgewerkt. De pagina wordt opnieuw geladen om de wijzigingen toe te passen.", "PE.Controllers.Main.errorSessionAbsolute": "De bewerksessie voor het document is vervallen. Laad de pagina opnieuw.", "PE.Controllers.Main.errorSessionIdle": "Het document is al lang niet meer bewerkt. Laad de pagina opnieuw.", "PE.Controllers.Main.errorSessionToken": "De verbinding met de server is onderbroken. Laad de pagina opnieuw.", "PE.Controllers.Main.errorSetPassword": "Wachtwoord kon niet worden ingesteld. ", "PE.Controllers.Main.errorStockChart": "Onjuiste volgorde rijen. Als u een aandelengrafiek wilt maken, zet u de rijen in de volgende volgorde op het blad:<br> beginko<PERSON>, hoogste koers, laagste koers, slotkoers.", "PE.Controllers.Main.errorToken": "Het token voor de documentbeveiliging heeft niet de juiste indeling.<br>N<PERSON>m alstublieft contact op met de behee<PERSON><PERSON> van de documentserver.", "PE.Controllers.Main.errorTokenExpire": "Het token voor de documentbeveiliging is vervallen.<br><PERSON><PERSON>m alstublieft contact op met de beheerder van de documentserver.", "PE.Controllers.Main.errorUpdateVersion": "De bestandsversie is gewijzigd. De pagina wordt opnieuw geladen.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "De internetverbinding is hersteld en de bestandsversie is gewijzigd. <br>Voordat u verder kunt werken, moet u het bestand downloaden of de inhoud kopiëren om er zeker van te zijn dat er niets verloren gaat, en deze pagina vervolgens opnieuw laden.", "PE.Controllers.Main.errorUserDrop": "Toegang tot het bestand is op dit moment niet mogelijk.", "PE.Controllers.Main.errorUsersExceed": "Het onder het prijsplan toegestane aantal gebruikers is overschreden", "PE.Controllers.Main.errorViewerDisconnect": "Verbinding is verbroken. U kunt het document nog wel bekijken,<br>maar u kunt het pas downloaden of afdrukken als de verbinding is hersteld.", "PE.Controllers.Main.leavePageText": "<PERSON>r zijn niet-opgeslagen wijzigingen in deze presentatie. Klik op \"Op deze pagina blijven\" en dan op \"Opslaan\" om de wijzigingen op te slaan. Klik op \"Pagina verlaten\" om de niet-opgeslagen wijzigingen te negeren.", "PE.Controllers.Main.leavePageTextOnClose": "Alle niet-opgeslagen wijzigingen in deze presentatie gaan verloren. <br> <PERSON><PERSON> op \"Annuleren\" en dan op \"Opslaan\" om ze op te slaan. Klik op \"OK\" om alle niet-opgeslagen wijzigingen te verwijderen.", "PE.Controllers.Main.loadFontsTextText": "Gegevens worden geladen...", "PE.Controllers.Main.loadFontsTitleText": "Gegevens worden geladen", "PE.Controllers.Main.loadFontTextText": "Gegevens worden geladen...", "PE.Controllers.Main.loadFontTitleText": "Gegevens worden geladen", "PE.Controllers.Main.loadImagesTextText": "Afbeeldingen worden geladen...", "PE.Controllers.Main.loadImagesTitleText": "Afbeeldingen worden geladen", "PE.Controllers.Main.loadImageTextText": "Afbeelding wordt geladen...", "PE.Controllers.Main.loadImageTitleText": "Afbeelding wordt geladen", "PE.Controllers.Main.loadingDocumentTextText": "Presentatie wordt geladen...", "PE.Controllers.Main.loadingDocumentTitleText": "Presentatie wordt geladen", "PE.Controllers.Main.loadThemeTextText": "Thema wordt geladen...", "PE.Controllers.Main.loadThemeTitleText": "Thema wordt geladen", "PE.Controllers.Main.notcriticalErrorTitle": "Waarschuwing", "PE.Controllers.Main.openErrorText": "Er is een fout opgetreden bij het openen van het bestand", "PE.Controllers.Main.openTextText": "Presentatie wordt geopend...", "PE.Controllers.Main.openTitleText": "Presentatie wordt geopend", "PE.Controllers.Main.printTextText": "Presentatie wordt afgedrukt...", "PE.Controllers.Main.printTitleText": "Presentatie wordt afgedrukt", "PE.Controllers.Main.reloadButtonText": "<PERSON><PERSON>a op<PERSON>w laden", "PE.Controllers.Main.requestEditFailedMessageText": "<PERSON><PERSON><PERSON> is deze presentatie nu aan het bewerken. <PERSON><PERSON><PERSON> het later opnieuw.", "PE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON> geweigerd", "PE.Controllers.Main.saveErrorText": "Er is een fout opgetreden bij het opslaan van het bestand", "PE.Controllers.Main.saveErrorTextDesktop": "Dit bestand kan niet worden opgeslagen of gemaakt. <br> Mogelijke redenen zijn: <br> 1. <PERSON>t bestand is alleen-lezen. <br> 2. Het bestand wordt bewerkt door andere gebruikers. <br> 3. <PERSON> is vol of beschadigd.", "PE.Controllers.Main.saveTextText": "Presentatie wordt opgeslagen...", "PE.Controllers.Main.saveTitleText": "Presentatie wordt opgeslagen", "PE.Controllers.Main.scriptLoadError": "De verbinding is te langzaam, sommige componenten konden niet geladen worden. Laad de pagina opnieuw.", "PE.Controllers.Main.splitDividerErrorText": "Het aantal rijen moet deelbaar zijn door %1.", "PE.Controllers.Main.splitMaxColsErrorText": "Het aantal kolommen moet kleiner zijn dan %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "Het aantal rijen moet minder zijn dan %1.", "PE.Controllers.Main.textAnonymous": "Anoni<PERSON>", "PE.Controllers.Main.textApplyAll": "Toepassen op alle vergelijkingen", "PE.Controllers.Main.textBuyNow": "Website bezoeken", "PE.Controllers.Main.textChangesSaved": "Alle wijzigingen zijn opgeslagen", "PE.Controllers.Main.textClose": "Afsluiten", "PE.Controllers.Main.textCloseTip": "Klik om de tip te sluiten", "PE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "PE.Controllers.Main.textContactUs": "Contact opnemen met <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textContinue": "Continue", "PE.Controllers.Main.textConvertEquation": "Deze vergelijking is gema<PERSON>t met een oude versie van de vergelijkingseditor die niet langer wordt ondersteund. Om deze te bewerken, converteert u de vergelijking naar de Office Math ML-indeling. <br> Nu converteren?", "PE.Controllers.Main.textCustomLoader": "Volgens de voorwaarden van de licentie heeft u geen recht om de lader aan te passen.<br>Neem contact op met onze verkoopafdeling voor een offerte.", "PE.Controllers.Main.textDisconnect": "Verbinding verbroken", "PE.Controllers.Main.textGuest": "Gastgebruiker", "PE.Controllers.Main.textHasMacros": "Het bestand bevat automatische macro's. <br> Wilt u macro's uitvoeren?", "PE.Controllers.Main.textLearnMore": "Meer informatie", "PE.Controllers.Main.textLoadingDocument": "Presentatie wordt geladen", "PE.Controllers.Main.textLongName": "<PERSON>oer een naam in die uit minder dan 128 tekens bestaat.", "PE.Controllers.Main.textNoLicenseTitle": "Only Office verbindingslimiet", "PE.Controllers.Main.textObject": "Object", "PE.Controllers.Main.textPaidFeature": "<PERSON><PERSON><PERSON> op<PERSON>", "PE.Controllers.Main.textReconnect": "Verbinding is hersteld", "PE.Controllers.Main.textRemember": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textRememberMacros": "Remember my choice for all macros", "PE.Controllers.Main.textRenameError": "Gebruikersnaam mag niet leeg zijn. ", "PE.Controllers.Main.textRenameLabel": "Voer een naam in die voor samenwerking moet worden gebruikt ", "PE.Controllers.Main.textRequestMacros": "Een macro maakt een verzoek naar een URL. Wil je dit verzoek naar %1 toestaan?", "PE.Controllers.Main.textShape": "Vorm", "PE.Controllers.Main.textStrict": "Strikte modus", "PE.Controllers.Main.textText": "Tekst", "PE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "PE.Controllers.Main.textTryUndoRedo": "De functies Ongedaan maken/Opnieuw worden uitgeschakeld in de modus Snel gezamenlijk bewerken.<br>Klik op de knop 'Strikte modus' om over te schakelen naar de strikte modus voor gezamenlijk bewerken. U kunt het bestand dan zonder interferentie van andere gebruikers bewerken en uw wijzigingen verzenden wanneer u die opslaat. U kunt schakelen tussen de modi voor gezamenlijke bewerking via Geavanceerde instellingen van de editor.", "PE.Controllers.Main.textTryUndoRedoWarn": "De functies Ongedaan maken/Annuleren zijn uitgeschakeld in de modus Snel meewerken.", "PE.Controllers.Main.textUndo": "Undo", "PE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "PE.Controllers.Main.textUpdating": "Updating", "PE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON><PERSON> ve<PERSON>", "PE.Controllers.Main.titleLicenseNotActive": "License not active", "PE.Controllers.Main.titleServerVersion": "Editor bijgewerkt", "PE.Controllers.Main.titleUpdateVersion": "Version changed", "PE.Controllers.Main.txtAddFirstSlide": "Klik om de eerste slide te maken", "PE.Controllers.Main.txtAddNotes": "Klik om notities toe te voegen", "PE.Controllers.Main.txtAnimationPane": "Animation Pane", "PE.Controllers.Main.txtArt": "<PERSON>er tekst invoeren", "PE.Controllers.Main.txtBasicShapes": "Basisvormen", "PE.Controllers.Main.txtButtons": "Knoppen", "PE.Controllers.Main.txtCallouts": "Callouts", "PE.Controllers.Main.txtCharts": "Grafieken", "PE.Controllers.Main.txtClipArt": "Illustraties", "PE.Controllers.Main.txtDateTime": "Datum en tijd", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Grafiektitel", "PE.Controllers.Main.txtEditingMode": "Bewerkmodus instellen...", "PE.Controllers.Main.txtEnd": "End: ${0}s", "PE.Controllers.Main.txtErrorLoadHistory": "Geschiedenis <PERSON> mislukt", "PE.Controllers.Main.txtFiguredArrows": "Pijlvormen", "PE.Controllers.Main.txtFirstSlide": "First slide", "PE.Controllers.Main.txtFooter": "Voettekst", "PE.Controllers.Main.txtHeader": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtImage": "Afbeelding", "PE.Controllers.Main.txtLastSlide": "Last slide", "PE.Controllers.Main.txtLines": "Lijnen", "PE.Controllers.Main.txtLoading": "Bezig met laden...", "PE.Controllers.Main.txtLoop": "Loop: ${0}s", "PE.Controllers.Main.txtMath": "Wiskunde", "PE.Controllers.Main.txtMedia": "Media", "PE.Controllers.Main.txtNeedSynchronize": "U hebt updates", "PE.Controllers.Main.txtNextSlide": "Next slide", "PE.Controllers.Main.txtNone": "<PERSON><PERSON>", "PE.Controllers.Main.txtPicture": "Afbeelding", "PE.Controllers.Main.txtPlayAll": "Play All", "PE.Controllers.Main.txtPlayFrom": "Play From", "PE.Controllers.Main.txtPlaySelected": "Play Selected", "PE.Controllers.Main.txtPrevSlide": "Previous slide", "PE.Controllers.Main.txtRectangles": "Rechthoeken", "PE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "PE.Controllers.Main.txtScheme_Aspect": "Aspect", "PE.Controllers.Main.txtScheme_Blue": "Blue", "PE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "PE.Controllers.Main.txtScheme_Blue_II": "Blue II", "PE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "PE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "PE.Controllers.Main.txtScheme_Green": "Green", "PE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "PE.Controllers.Main.txtScheme_Marquee": "Marquee", "PE.Controllers.Main.txtScheme_Median": "Median", "PE.Controllers.Main.txtScheme_Office": "Office", "PE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "PE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "PE.Controllers.Main.txtScheme_Orange": "Orange", "PE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "PE.Controllers.Main.txtScheme_Paper": "Paper", "PE.Controllers.Main.txtScheme_Red": "Red", "PE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "PE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "PE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "PE.Controllers.Main.txtScheme_Violet": "Violet", "PE.Controllers.Main.txtScheme_Violet_II": "Violet II", "PE.Controllers.Main.txtScheme_Yellow": "Yellow", "PE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "PE.Controllers.Main.txtSeries": "Serie", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Legenda met lijn 1 (<PERSON> en Accentbalk)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "<PERSON><PERSON> met lijn 2 (<PERSON> met <PERSON>)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "<PERSON><PERSON> met lijn 3 (<PERSON> met accent balk)", "PE.Controllers.Main.txtShape_accentCallout1": "Legend<PERSON> met lijn 1 (accentbalk)", "PE.Controllers.Main.txtShape_accentCallout2": "Legend<PERSON> met lijn 1(accentbalk)", "PE.Controllers.Main.txtShape_accentCallout3": "<PERSON><PERSON> met lijn 3 (accent balk)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Terug of vorige knop", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Knop \"afspelen\"", "PE.Controllers.Main.txtShape_actionButtonBlank": "Lege button", "PE.Controllers.Main.txtShape_actionButtonDocument": "Document knop", "PE.Controllers.Main.txtShape_actionButtonEnd": "Beëindigen", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "'Volgende' knop", "PE.Controllers.Main.txtShape_actionButtonHelp": "Help knop", "PE.Controllers.Main.txtShape_actionButtonHome": "Start knop", "PE.Controllers.Main.txtShape_actionButtonInformation": "Informatieknop", "PE.Controllers.Main.txtShape_actionButtonMovie": "Film knop", "PE.Controllers.Main.txtShape_actionButtonReturn": "Return-knop", "PE.Controllers.Main.txtShape_actionButtonSound": "Geluid knop", "PE.Controllers.Main.txtShape_arc": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON> pijl", "PE.Controllers.Main.txtShape_bentConnector5": "Rechthoekige verbinding", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Rechthoekige verbinding met peil", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Rechthoekige verbinding met dub<PERSON>e peilen", "PE.Controllers.Main.txtShape_bentUpArrow": "<PERSON>ar boven gebogen pijl", "PE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON> rand", "PE.Controllers.Main.txtShape_blockArc": "Blokboog", "PE.Controllers.Main.txtShape_borderCallout1": "Legenda met lijn 1", "PE.Controllers.Main.txtShape_borderCallout2": "Legenda met lijn 2", "PE.Controllers.Main.txtShape_borderCallout3": "Legenda met lijn 3", "PE.Controllers.Main.txtShape_bracePair": "Dubbele accolades", "PE.Controllers.Main.txtShape_callout1": "<PERSON><PERSON> met lijn 1 (<PERSON><PERSON> rand)", "PE.Controllers.Main.txtShape_callout2": "<PERSON><PERSON> met lijn 2 (g<PERSON> rand)", "PE.Controllers.Main.txtShape_callout3": "<PERSON><PERSON> met lijn 3 (<PERSON><PERSON> rand)", "PE.Controllers.Main.txtShape_can": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_chevron": "Chevron", "PE.Controllers.Main.txtShape_chord": "Cirkelsegment", "PE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON> pijl", "PE.Controllers.Main.txtShape_cloud": "Cloud", "PE.Controllers.Main.txtShape_cloudCallout": "Cloud legenda", "PE.Controllers.Main.txtShape_corner": "Hoek", "PE.Controllers.Main.txtShape_cube": "Ku<PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "<PERSON><PERSON><PERSON> verbinding", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "<PERSON><PERSON><PERSON> verb<PERSON> met pij<PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "<PERSON><PERSON><PERSON> verb<PERSON> met dub<PERSON><PERSON> peilen", "PE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON><PERSON><PERSON> pijl naar beneden", "PE.Controllers.Main.txtShape_curvedLeftArrow": "<PERSON><PERSON><PERSON> pijl naar links", "PE.Controllers.Main.txtShape_curvedRightArrow": "<PERSON><PERSON><PERSON> pijl naar rechts", "PE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON><PERSON><PERSON> pijl naar boven", "PE.Controllers.Main.txtShape_decagon": "Tienhoek", "PE.Controllers.Main.txtShape_diagStripe": "Diagonale Strepen", "PE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_dodecagon": "Twaalfhoek", "PE.Controllers.Main.txtShape_donut": "Donut", "PE.Controllers.Main.txtShape_doubleWave": "Dubbele golf", "PE.Controllers.Main.txtShape_downArrow": "Pijl omlaag", "PE.Controllers.Main.txtShape_downArrowCallout": "<PERSON><PERSON> met p<PERSON> naar ben<PERSON>n", "PE.Controllers.Main.txtShape_ellipse": "Ovaal", "PE.Controllers.Main.txtShape_ellipseRibbon": "Gebogen lint naar beneden", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Gebogen lint naar boven", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Alternatief proces stroomdiagram ", "PE.Controllers.Main.txtShape_flowChartCollate": "Stroomdiagram: Sorteren", "PE.Controllers.Main.txtShape_flowChartConnector": "Stroomdiagram: Verbinden", "PE.Controllers.Main.txtShape_flowChartDecision": "Stroomdiagram: Beslissingen", "PE.Controllers.Main.txtShape_flowChartDelay": "Stroomdiagram: Vertraging", "PE.Controllers.Main.txtShape_flowChartDisplay": "Stroomdiagram: <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDocument": "Stroomdiagram: Document", "PE.Controllers.Main.txtShape_flowChartExtract": "Stroomdiagram: Extractie", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Stroomdiagram: gegevens", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Stroomdiagram: Interne opslag", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Stroomdiagram: Magnetis<PERSON> schijf", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Stroomdiagram: Opslag met directe toegang", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Stroomdiagram: <PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartManualInput": "Stroomdiagram: Handmatige invoer", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Stroomdiagram: Hand<PERSON><PERSON> bewerking", "PE.Controllers.Main.txtShape_flowChartMerge": "Stroomdiagram: Samenvoegen", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Stroomdiagram: Meerdere documenten", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Stroomdiagram: Verbinding naar andere pagina", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Stroomdiagram: Opgeslagen gegevens", "PE.Controllers.Main.txtShape_flowChartOr": "Stroomdiagram: Of", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Stroomdiagram: Voorgedefinieerd", "PE.Controllers.Main.txtShape_flowChartPreparation": "Stroomdiagram: Voorbereiding", "PE.Controllers.Main.txtShape_flowChartProcess": "Stroomdiagram: Proces", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Kaartdiagram", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Stroomdiagram: Ponsband", "PE.Controllers.Main.txtShape_flowChartSort": "Stroomdiagram: Sorteren", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Stroomdiagram: Samenvoeging", "PE.Controllers.Main.txtShape_flowChartTerminator": "Stroomdiagram: Beëindigen ", "PE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_frame": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_halfFrame": "Half kader", "PE.Controllers.Main.txtShape_heart": "<PERSON>", "PE.Controllers.Main.txtShape_heptagon": "Zevenhoek", "PE.Controllers.Main.txtShape_hexagon": "zeshoek", "PE.Controllers.Main.txtShape_homePlate": "Vijfhoek", "PE.Controllers.Main.txtShape_horizontalScroll": "Horizontaal scrollen", "PE.Controllers.Main.txtShape_irregularSeal1": "Explosie 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Explosie 2", "PE.Controllers.Main.txtShape_leftArrow": "Pijl links", "PE.Controllers.Main.txtShape_leftArrowCallout": "<PERSON><PERSON> met p<PERSON> naar links", "PE.Controllers.Main.txtShape_leftBrace": "Haakje links", "PE.Controllers.Main.txtShape_leftBracket": "<PERSON>s hoekige haak", "PE.Controllers.Main.txtShape_leftRightArrow": "Peil naar links en rechts", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "<PERSON><PERSON> met peil naar rechts", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Peil naar links, rechts en boven", "PE.Controllers.Main.txtShape_leftUpArrow": "Peil naar links en boven", "PE.Controllers.Main.txtShape_lightningBolt": "Bliksemschicht", "PE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "Pijl", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "Dubbele pijl", "PE.Controllers.Main.txtShape_mathDivide": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMinus": "Min", "PE.Controllers.Main.txtShape_mathMultiply": "Vermenigvuldigen", "PE.Controllers.Main.txtShape_mathNotEqual": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathPlus": "Plus", "PE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_noSmoking": "\"<PERSON><PERSON>\" Symbool", "PE.Controllers.Main.txtShape_notchedRightArrow": "<PERSON><PERSON><PERSON> naar rechts met kerf", "PE.Controllers.Main.txtShape_octagon": "Achthoek", "PE.Controllers.Main.txtShape_parallelogram": "Parallellogram", "PE.Controllers.Main.txtShape_pentagon": "Vijfhoek", "PE.Controllers.Main.txtShape_pie": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_plaque": "Onderteken", "PE.Controllers.Main.txtShape_plus": "Plus", "PE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_polyline2": "vrije vorm", "PE.Controllers.Main.txtShape_quadArrow": "Peil in vier richtingen", "PE.Controllers.Main.txtShape_quadArrowCallout": "<PERSON><PERSON> met p<PERSON><PERSON><PERSON> in vier richtingen", "PE.Controllers.Main.txtShape_rect": "Vierhoek", "PE.Controllers.Main.txtShape_ribbon": "<PERSON><PERSON> naar ben<PERSON>n", "PE.Controllers.Main.txtShape_ribbon2": "<PERSON>t naar boven", "PE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON> rechts", "PE.Controllers.Main.txtShape_rightArrowCallout": "<PERSON><PERSON> met pij<PERSON> naar rechts", "PE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightBracket": "accolade rechts", "PE.Controllers.Main.txtShape_round1Rect": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_round2DiagRect": "<PERSON><PERSON> diagonal<PERSON> recht<PERSON>", "PE.Controllers.Main.txtShape_round2SameRect": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_roundRect": "Afgeronde driehoek", "PE.Controllers.Main.txtShape_rtTriangle": "Driehoek naar rechts", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_snip1Rect": "Snip Single Corner Rectangle", "PE.Controllers.Main.txtShape_snip2DiagRect": "Knip Diagonale Hoek Rechthoek", "PE.Controllers.Main.txtShape_snip2SameRect": "<PERSON><PERSON><PERSON> in hoek aan dezelfde zijde", "PE.Controllers.Main.txtShape_snipRoundRect": "<PERSON><PERSON><PERSON> en rond enkele hoek rechthoek", "PE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "10-Punt ster", "PE.Controllers.Main.txtShape_star12": "12-<PERSON>unt ster", "PE.Controllers.Main.txtShape_star16": "16-Punt ster", "PE.Controllers.Main.txtShape_star24": "24-Punt ster", "PE.Controllers.Main.txtShape_star32": "32-Punt ster", "PE.Controllers.Main.txtShape_star4": "4-<PERSON>unt ster", "PE.Controllers.Main.txtShape_star5": "5-<PERSON>unt ster", "PE.Controllers.Main.txtShape_star6": "6-<PERSON>unt ster", "PE.Controllers.Main.txtShape_star7": "7-<PERSON>unt ster", "PE.Controllers.Main.txtShape_star8": "8-Punt ster", "PE.Controllers.Main.txtShape_stripedRightArrow": "<PERSON><PERSON><PERSON> naar rechts met strepen", "PE.Controllers.Main.txtShape_sun": "Zon", "PE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_textRect": "Tekstvak", "PE.Controllers.Main.txtShape_trapezoid": "Trapezium", "PE.Controllers.Main.txtShape_triangle": "Driehoek", "PE.Controllers.Main.txtShape_upArrow": "Pijl omhoog", "PE.Controllers.Main.txtShape_upArrowCallout": "<PERSON><PERSON> met p<PERSON><PERSON>", "PE.Controllers.Main.txtShape_upDownArrow": "<PERSON><PERSON> naar boven en beneden", "PE.Controllers.Main.txtShape_uturnArrow": "U-bocht pijl", "PE.Controllers.Main.txtShape_verticalScroll": "Verticale scrolling", "PE.Controllers.Main.txtShape_wave": "Golf", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Ovale legenda", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Rechthoekige legenda", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Afgeronde rechthoekige Legenda", "PE.Controllers.Main.txtSldLtTBlank": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTChart": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTChartAndTx": "Grafiek en tekst", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Illustraties en tekst", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Illustraties en verticale tekst", "PE.Controllers.Main.txtSldLtTCust": "Aangepast", "PE.Controllers.Main.txtSldLtTDgm": "Diagram", "PE.Controllers.Main.txtSldLtTFourObj": "<PERSON>ier objecten", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Media en tekst", "PE.Controllers.Main.txtSldLtTObj": "Titel en object", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Object en twee objecten", "PE.Controllers.Main.txtSldLtTObjAndTx": "Object en tekst", "PE.Controllers.Main.txtSldLtTObjOnly": "Object", "PE.Controllers.Main.txtSldLtTObjOverTx": "Object over tekst", "PE.Controllers.Main.txtSldLtTObjTx": "<PERSON>ite<PERSON>, object en bijschrift", "PE.Controllers.Main.txtSldLtTPicTx": "Afbeelding en bijschrift", "PE.Controllers.Main.txtSldLtTSecHead": "Se<PERSON>iek<PERSON>", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitle": "Titel", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON> titel", "PE.Controllers.Main.txtSldLtTTwoColTx": "Te<PERSON>t in twee kolommen", "PE.Controllers.Main.txtSldLtTTwoObj": "Twee objecten", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Twee objecten en object", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Twee objecten en tekst", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Twee objecten over tekst", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Twee teksten en twee objecten", "PE.Controllers.Main.txtSldLtTTx": "Tekst", "PE.Controllers.Main.txtSldLtTTxAndChart": "Tekst en grafiek", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Tekst en illustraties", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Tekst en media", "PE.Controllers.Main.txtSldLtTTxAndObj": "Tekst en object", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Tekst en twee objecten", "PE.Controllers.Main.txtSldLtTTxOverObj": "<PERSON><PERSON><PERSON> over object", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Verticale titel en tekst", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Verticale titel en tekst over grafiek", "PE.Controllers.Main.txtSldLtTVertTx": "Verticale tekst", "PE.Controllers.Main.txtSlideNumber": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSlideSubtitle": "Subtitel dia", "PE.Controllers.Main.txtSlideText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSlideTitle": "Diatitel", "PE.Controllers.Main.txtStarsRibbons": "St<PERSON>ren en linten", "PE.Controllers.Main.txtStart": "Start: ${0}s", "PE.Controllers.Main.txtStop": "Stop", "PE.Controllers.Main.txtTheme_basic": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_blank": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_classic": "Klassiek", "PE.Controllers.Main.txtTheme_corner": "Hoek", "PE.Controllers.Main.txtTheme_dotted": "Stippels", "PE.Controllers.Main.txtTheme_green": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green_leaf": "<PERSON><PERSON>ur thema", "PE.Controllers.Main.txtTheme_lines": "Lijnen", "PE.Controllers.Main.txtTheme_office": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office_theme": "Office thema", "PE.Controllers.Main.txtTheme_official": "Officieel", "PE.Controllers.Main.txtTheme_pixel": "Pixel", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Turtle", "PE.Controllers.Main.txtXAxis": "X-as", "PE.Controllers.Main.txtYAxis": "Y-as", "PE.Controllers.Main.txtZoom": "Zoom", "PE.Controllers.Main.unknownErrorText": "Onbekende fout.", "PE.Controllers.Main.unsupportedBrowserErrorText": "Uw browser wordt niet ondersteund.", "PE.Controllers.Main.uploadImageExtMessage": "Onbekende afbeeldingsindeling.", "PE.Controllers.Main.uploadImageFileCountMessage": "<PERSON>n afbeeldingen geüpload.", "PE.Controllers.Main.uploadImageSizeMessage": "De afbeelding is te groot. De maximale grootte is 25MB.", "PE.Controllers.Main.uploadImageTextText": "Afbeelding wordt geüpload...", "PE.Controllers.Main.uploadImageTitleText": "Afbeelding wordt geüpload", "PE.Controllers.Main.waitText": "Een moment...", "PE.Controllers.Main.warnBrowserIE9": "Met IE9 heeft de toepassing beperkte mogelijkheden. Gebruik IE10 of hoger.", "PE.Controllers.Main.warnBrowserZoom": "De huidige zoominstelling van uw browser wordt niet ondersteund. Zet de zoominstelling terug op de standaardwaarde door op Ctrl+0 te drukken.", "PE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "PE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "PE.Controllers.Main.warnLicenseExceeded": "U heeft de limiet bereikt voor gelijktijdige verbindingen met% 1 editors. Dit document wordt geopend als alleen-lezen. <br> Neem contact op met uw beheerder voor meer informatie.", "PE.Controllers.Main.warnLicenseExp": "Uw licentie is vervallen.<br>Werk uw licentie bij en vernieuw de pagina.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Licentie verlopen. <br> U heeft geen toegang tot documentbewerkingsfunctionaliteit. <br> Neem contact op met uw beheerder.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Licentie moet worden verlengd. <br> U heeft beperkte toegang tot documentbewerkingsfunctionaliteit. <br> Neem contact op met uw beheerder voor volledige toegang", "PE.Controllers.Main.warnLicenseUsersExceeded": "U heeft de gebruikerslimiet voor %1 editors bereikt. Neem contact op met uw beheerder voor meer informatie.", "PE.Controllers.Main.warnNoLicense": "U heeft de limiet bereikt voor gelijktijdige verbindingen met %1 editors. Dit document wordt geopend als alleen-lezen. <br> Neem contact op met het %1 verkoopteam voor persoonlijke upgradevoorwaarden.", "PE.Controllers.Main.warnNoLicenseUsers": "U heeft de limiet voor %1 gelijktijdige gebruikers bereikt. Neem contact op met de %1 verkoopafdeling voor persoonlijke upgradevoorwaarden.", "PE.Controllers.Main.warnProcessRightsChange": "Het recht om het bestand te bewerken is u ontzegd.", "PE.Controllers.Print.txtPrintRangeInvalid": "Invalid print range", "PE.Controllers.Print.txtPrintRangeSingleRange": "Enter either a single slide number or a single slide range (for example, 5-12). Or you can Print to PDF.", "PE.Controllers.Search.notcriticalErrorTitle": "Warning", "PE.Controllers.Search.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "PE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "PE.Controllers.Search.textReplaceSuccess": "Search has been done. {0} occurrences have been replaced", "PE.Controllers.Search.warnReplaceString": "{0} is geen geldig speciaal teken voor de \"Vervang met\" box.", "PE.Controllers.Statusbar.textDisconnect": "<b>Verbinding verbroken</b><br>Proberen opnieuw te verbinden. Controleer de netwerkinstellingen.", "PE.Controllers.Statusbar.zoomText": "Zoomen {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "Het lettertype dat u probeert op te slaan, is niet be<PERSON><PERSON><PERSON><PERSON> op het huidige apparaat.<br>De tekststijl wordt weergegeven met een van de systeemlettertypen. Het opgeslagen lettertype wordt gebruikt wanneer het beschikbaar is.<br>Wilt u doorgaan?", "PE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "PE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "PE.Controllers.Toolbar.helpTabDesign": "Apply themes, change color schemes and slide size from the newly added Design tab.", "PE.Controllers.Toolbar.helpTabDesignHeader": "Design tab", "PE.Controllers.Toolbar.textAccent": "Accenten", "PE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textFontSizeErr": "De ingevoerde waarde is onjuist.<br><PERSON><PERSON><PERSON> een waarde tussen 1 en 300 in", "PE.Controllers.Toolbar.textFraction": "Breuken", "PE.Controllers.Toolbar.textFunction": "Functies", "PE.Controllers.Toolbar.textInsert": "Invoegen", "PE.Controllers.Toolbar.textIntegral": "Integralen", "PE.Controllers.Toolbar.textLargeOperator": "Grote operators", "PE.Controllers.Toolbar.textLimitAndLog": "Limieten en logaritmen", "PE.Controllers.Toolbar.textMatrix": "Matrices", "PE.Controllers.Toolbar.textOperator": "Operators", "PE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "Symbolen", "PE.Controllers.Toolbar.textWarning": "Waarschuwing", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON><PERSON> van rechts naar links boven", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Pijl links boven", "PE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON><PERSON> rechts boven", "PE.Controllers.Toolbar.txtAccent_Bar": "St<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_BarBot": "<PERSON><PERSON><PERSON> onder", "PE.Controllers.Toolbar.txtAccent_BarTop": "Streep boven", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Formule in vak (met plaatsaanduiding)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formule in vak (voorbeeld)", "PE.Controllers.Toolbar.txtAccent_Check": "Controleren", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Accolade onder", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Accolade boven", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC met streep boven", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y met streep boven", "PE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON><PERSON> punten", "PE.Controllers.Toolbar.txtAccent_DDot": "Twee punten", "PE.Controllers.Toolbar.txtAccent_Dot": "Punt", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON> streep boven", "PE.Controllers.Toolbar.txtAccent_Grave": "Grave", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Groeperingsteken onder", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Groeperingsteken boven", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Harpoen linksboven", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Harpoen rechtsboven", "PE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Smile": "Breve", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "<PERSON><PERSON><PERSON><PERSON> met sche<PERSON>ste<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "<PERSON><PERSON><PERSON><PERSON> met sche<PERSON>ste<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "<PERSON><PERSON><PERSON><PERSON> met sche<PERSON>ste<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Opties (twee voorwaarden)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Opties (drie voorwaarden)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Stapelobject", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Stapelobject", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Voorbeeld van opties", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Binomiale coëfficiënt", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Binomiale coëfficiënt", "PE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "<PERSON><PERSON><PERSON><PERSON> met sche<PERSON>ste<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON><PERSON> met s<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionDifferential_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionDifferential_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionDifferential_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionDifferential_4": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionHorizontal": "Lineaire breuk", "PE.Controllers.Toolbar.txtFractionPi_2": "<PERSON> gedeeld door 2", "PE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON> breuk", "PE.Controllers.Toolbar.txtFractionVertical": "Gestapelde breuk", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Inverse-cosinusfunctie", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperbolische inverse-cosinusfunctie", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Inverse-cotangensfunctie", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperbolische inverse-cotangensfunctie", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Inverse-cosecansfunctie", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperbolische inverse-cosecansfunctie", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Inverse-se<PERSON>functie", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperbolische inverse-secansfunctie", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Inverse-sinusfunctie", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperbolische inverse-sinusfunctie", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Inverse-tangensfunctie", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperbolische inverse-tangensfunctie", "PE.Controllers.Toolbar.txtFunction_Cos": "Cosinusfunctie", "PE.Controllers.Toolbar.txtFunction_Cosh": "Functie voor cosinus hyperbolicus", "PE.Controllers.Toolbar.txtFunction_Cot": "Cotangensfunctie", "PE.Controllers.Toolbar.txtFunction_Coth": "Functie voor cotangens hyperbolicus", "PE.Controllers.Toolbar.txtFunction_Csc": "Cosecansfunctie", "PE.Controllers.Toolbar.txtFunction_Csch": "Functie voor cosecans hyperbolicus", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sinus theta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Tangensformule", "PE.Controllers.Toolbar.txtFunction_Sec": "Secansfunctie", "PE.Controllers.Toolbar.txtFunction_Sech": "Functie voor secans hyperbolicus", "PE.Controllers.Toolbar.txtFunction_Sin": "Sinusfunctie", "PE.Controllers.Toolbar.txtFunction_Sinh": "Functie voor sinus hyperbolicus", "PE.Controllers.Toolbar.txtFunction_Tan": "Tangensfunctie", "PE.Controllers.Toolbar.txtFunction_Tanh": "Functie voor tangens hyperbolicus", "PE.Controllers.Toolbar.txtIntegral": "Integraal", "PE.Controllers.Toolbar.txtIntegral_dtheta": "<PERSON><PERSON>el theta", "PE.Controllers.Toolbar.txtIntegral_dx": "<PERSON><PERSON><PERSON> x", "PE.Controllers.Toolbar.txtIntegral_dy": "<PERSON><PERSON><PERSON> y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integraal", "PE.Controllers.Toolbar.txtIntegralDouble": "Dubbele integraal", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Dubbele integraal", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Dubbele integraal", "PE.Controllers.Toolbar.txtIntegralOriented": "Kringintegraal", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Kringintegraal", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Oppervlakte-integraal", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Oppervlakte-integraal", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Oppervlakte-integraal", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Kringintegraal", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume-integraal", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volume-integraal", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volume-integraal", "PE.Controllers.Toolbar.txtIntegralSubSup": "Integraal", "PE.Controllers.Toolbar.txtIntegralTriple": "Driedubbele integraal", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Driedubbele integraal", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Driedubbele integraal", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Wig", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Wig", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Wig", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Wig", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Wig", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Coproduct", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Coproduct", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Coproduct", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Coproduct", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Coproduct", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Optelling", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Optelling", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Optelling", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Vereniging", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V-vorm", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V-vorm", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V-vorm", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V-vorm", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V-vorm", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersectie", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersectie", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersectie", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersectie", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersectie", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Optelling", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Optelling", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Optelling", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Optelling", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Optelling", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Vereniging", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Vereniging", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Vereniging", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Vereniging", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Vereniging", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Voorbeeld limiet", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON><PERSON> van maximum", "PE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Natuurlijk logaritme", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logaritme", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritme", "PE.Controllers.Toolbar.txtLimitLog_Max": "Maximum", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2 lege matrix", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 lege matrix", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 lege matrix", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 lege matrix", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Lege matrix met ha<PERSON><PERSON>s", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Lege matrix met ha<PERSON><PERSON>s", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Lege matrix met ha<PERSON><PERSON>s", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Lege matrix met ha<PERSON><PERSON>s", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 lege matrix", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 lege matrix", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 lege matrix", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 lege matrix", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonale punten", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Verticale punten", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "Zeldzame matrix", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "Zeldzame matrix", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 eenheidsmatrix", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 eenheidsmatrix", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 eenheidsmatrix", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 eenheidsmatrix", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON><PERSON><PERSON> van rechts naar links onder", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON><PERSON> van rechts naar links boven", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Pijl links onder", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Pijl links boven", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON><PERSON> rechts onder", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON><PERSON> rechts boven", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Dubbele punt en gelijkteken", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Resultaten", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Deltaresultaten", "PE.Controllers.Toolbar.txtOperator_Definition": "Per definitie gelijk aan", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta gelijk aan", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON><PERSON><PERSON> van rechts naar links onder", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON><PERSON> van rechts naar links boven", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Pijl links onder", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Pijl links boven", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON><PERSON> rechts onder", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON><PERSON> rechts boven", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "Minteken gelijkteken", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Plusteken gelijkteken", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "G<PERSON><PERSON><PERSON> aan", "PE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_2": "Vierkantswortel met graad", "PE.Controllers.Toolbar.txtRadicalRoot_3": "Derdemachtswortel", "PE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON> met graad", "PE.Controllers.Toolbar.txtRadicalSqrt": "Vierkantswortel", "PE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "Subscript", "PE.Controllers.Toolbar.txtScriptSubSup": "Subscript-Superscript", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Subscript-superscript links", "PE.Controllers.Toolbar.txtScriptSup": "Superscript", "PE.Controllers.Toolbar.txtSymbol_about": "Ongeveer", "PE.Controllers.Toolbar.txtSymbol_additional": "Complement", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON><PERSON> gelijk aan", "PE.Controllers.Toolbar.txtSymbol_ast": "Asterisk-operator", "PE.Controllers.Toolbar.txtSymbol_beta": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_beth": "Bet", "PE.Controllers.Toolbar.txtSymbol_bullet": "Stip-operator", "PE.Controllers.Toolbar.txtSymbol_cap": "Intersectie", "PE.Controllers.Toolbar.txtSymbol_cbrt": "Derdemachtswortel", "PE.Controllers.Toolbar.txtSymbol_cdots": "Horizontale ellips (middenlijn)", "PE.Controllers.Toolbar.txtSymbol_celsius": "Graden <PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Ongeveer gelijk aan", "PE.Controllers.Toolbar.txtSymbol_cup": "Vereniging", "PE.Controllers.Toolbar.txtSymbol_ddots": "Ellips diagonaal rechtsonder", "PE.Controllers.Toolbar.txtSymbol_degree": "Graden", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Pijl omlaag", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Lege verzameling", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_equiv": "Identiek aan", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "Aanwezig", "PE.Controllers.Toolbar.txtSymbol_factorial": "Faculteit", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Graden Fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "<PERSON>oor alle", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON> dan of gelijk aan", "PE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON> groter dan", "PE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON> dan", "PE.Controllers.Toolbar.txtSymbol_in": "Element van", "PE.Controllers.Toolbar.txtSymbol_inc": "Verhoging", "PE.Controllers.Toolbar.txtSymbol_infinity": "Oneindig", "PE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Pijl links", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Pijl links-rechts", "PE.Controllers.Toolbar.txtSymbol_leq": "<PERSON><PERSON> dan of gelijk aan", "PE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON> dan", "PE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON> kleiner dan", "PE.Controllers.Toolbar.txtSymbol_minus": "Min", "PE.Controllers.Toolbar.txtSymbol_mp": "Minusplus", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "<PERSON><PERSON> gel<PERSON>jk aan", "PE.Controllers.Toolbar.txtSymbol_ni": "Bevat als element", "PE.Controllers.Toolbar.txtSymbol_not": "Niet-teken", "PE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Partiële afgeleide", "PE.Controllers.Toolbar.txtSymbol_percent": "Percentage", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Plus", "PE.Controllers.Toolbar.txtSymbol_pm": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_propto": "<PERSON><PERSON><PERSON> met", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "Vierdemachtswortel", "PE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON> bewi<PERSON>s", "PE.Controllers.Toolbar.txtSymbol_rddots": "Ellips diagonaal rechtsboven", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> rechts", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Wortelteken", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "Daarom", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "Vermenigvuldigingsteken", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Pijl omhoog", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon, variant", "PE.Controllers.Toolbar.txtSymbol_varphi": "Phi, variant", "PE.Controllers.Toolbar.txtSymbol_varpi": "Pi, variant", "PE.Controllers.Toolbar.txtSymbol_varrho": "Rho, variant", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma, variant", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Theta, variant", "PE.Controllers.Toolbar.txtSymbol_vdots": "Verticale ellips", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "<PERSON><PERSON><PERSON><PERSON> aan dia", "PE.Controllers.Viewport.textFitWidth": "<PERSON>an <PERSON>te aan<PERSON>en", "PE.Views.Animation.str0_5": "0,5s (Erg snel)", "PE.Views.Animation.str1": "1s (Snel)", "PE.Views.Animation.str2": "2s (medium)", "PE.Views.Animation.str20": "20s (<PERSON><PERSON><PERSON> langzaam)", "PE.Views.Animation.str3": "3s (<PERSON><PERSON><PERSON>)", "PE.Views.Animation.str5": "5s (<PERSON><PERSON> lang<PERSON>)", "PE.Views.Animation.strDelay": "Vertragen", "PE.Views.Animation.strDuration": "<PERSON><PERSON>", "PE.Views.Animation.strRepeat": "Repeat", "PE.Views.Animation.strRewind": "Rewind", "PE.Views.Animation.strStart": "Start", "PE.Views.Animation.strTrigger": "<PERSON><PERSON>", "PE.Views.Animation.textAutoPreview": "AutoVoorbeeld", "PE.Views.Animation.textMoreEffects": "Show more effects", "PE.Views.Animation.textMoveEarlier": "Move earlier", "PE.Views.Animation.textMoveLater": "Move Later", "PE.Views.Animation.textMultiple": "Multiple", "PE.Views.Animation.textNone": "None", "PE.Views.Animation.textNoRepeat": "(geen)", "PE.Views.Animation.textOnClickOf": "On Click of", "PE.Views.Animation.textOnClickSequence": "On Click Sequence", "PE.Views.Animation.textStartAfterPrevious": "Na vorige", "PE.Views.Animation.textStartOnClick": "On Click", "PE.Views.Animation.textStartWithPrevious": "With Previous", "PE.Views.Animation.textUntilEndOfSlide": "Until end of slide", "PE.Views.Animation.textUntilNextClick": "Until next click", "PE.Views.Animation.txtAddEffect": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.txtAnimationPane": "Animatievenster", "PE.Views.Animation.txtParameters": "Parameters", "PE.Views.Animation.txtPreview": "Voorbeeld", "PE.Views.Animation.txtSec": "s", "PE.Views.AnimationDialog.textPreviewEffect": "Preview effect", "PE.Views.AnimationDialog.textTitle": "More effects", "PE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "PE.Views.ChartSettings.text3dHeight": "Height (% of base)", "PE.Views.ChartSettings.text3dRotation": "3D Rotation", "PE.Views.ChartSettings.textAdvanced": "Geavanceerde instellingen tonen", "PE.Views.ChartSettings.textAutoscale": "Autoscale", "PE.Views.ChartSettings.textChartType": "Grafiektype wijzigen", "PE.Views.ChartSettings.textDefault": "Default rotation", "PE.Views.ChartSettings.textDown": "Down", "PE.Views.ChartSettings.textEditData": "Gegevens bewerken", "PE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON> ver<PERSON>", "PE.Views.ChartSettings.textLeft": "Left", "PE.Views.ChartSettings.textNarrow": "Narrow field of view", "PE.Views.ChartSettings.textPerspective": "Perspective", "PE.Views.ChartSettings.textRight": "Right", "PE.Views.ChartSettings.textRightAngle": "Right angle axes", "PE.Views.ChartSettings.textSize": "Grootte", "PE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textUp": "Up", "PE.Views.ChartSettings.textWiden": "Widen field of view", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textX": "X rotation", "PE.Views.ChartSettings.textY": "Y rotation", "PE.Views.ChartSettingsAdvanced.textAlt": "Alternatieve tekst", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Beschrijving", "PE.Views.ChartSettingsAdvanced.textAltTip": "De alternatieve, op tekst gebaseerde weergave van de visuele objectinformatie. Deze wordt voorgelezen voor mensen met visuele of cognitieve handicaps om hen te helpen begrijpen welke informatie aanwezig is in de afbeelding, Vorm, grafiek of tabel.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Titel", "PE.Views.ChartSettingsAdvanced.textCenter": "<PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textChartName": "Chart name", "PE.Views.ChartSettingsAdvanced.textFrom": "<PERSON>", "PE.Views.ChartSettingsAdvanced.textGeneral": "General", "PE.Views.ChartSettingsAdvanced.textHeight": "Height", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON> ver<PERSON>", "PE.Views.ChartSettingsAdvanced.textPlacement": "Placement", "PE.Views.ChartSettingsAdvanced.textPosition": "Position", "PE.Views.ChartSettingsAdvanced.textSize": "Size", "PE.Views.ChartSettingsAdvanced.textTitle": "Grafiek - Geavanceerde instellingen", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "Top left corner", "PE.Views.ChartSettingsAdvanced.textVertical": "Vertical", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "<PERSON><PERSON> de standaardindeling in voor {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "stel in als standaard", "PE.Views.DateTimeDialog.textFormat": "Form<PERSON><PERSON>", "PE.Views.DateTimeDialog.textLang": "Taal", "PE.Views.DateTimeDialog.textUpdate": "Automatisch updaten", "PE.Views.DateTimeDialog.txtTitle": "Datum & Tijd", "PE.Views.DocumentHolder.aboveText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.addCommentText": "Opmerking toevoegen", "PE.Views.DocumentHolder.advancedChartText": "Chart advanced settings", "PE.Views.DocumentHolder.advancedEquationText": "Equation settings", "PE.Views.DocumentHolder.advancedImageText": "Geavanceerde afbeeldingsinstellingen", "PE.Views.DocumentHolder.advancedParagraphText": "Geavanceerde tekstinstellingen", "PE.Views.DocumentHolder.advancedShapeText": "Geavanceerde vorminstellingen", "PE.Views.DocumentHolder.advancedTableText": "Geavanceerde tabelinstellingen", "PE.Views.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.allLinearText": "All - Linear", "PE.Views.DocumentHolder.allProfText": "All - Professional", "PE.Views.DocumentHolder.belowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.cellAlignText": "Verticale uitlijning cel", "PE.Views.DocumentHolder.cellText": "<PERSON>l", "PE.Views.DocumentHolder.centerText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.columnText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.currLinearText": "Current - Linear", "PE.Views.DocumentHolder.currProfText": "Current - Professional", "PE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON>m verwijderen", "PE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.deleteTableText": "<PERSON>bel verwijderen", "PE.Views.DocumentHolder.deleteText": "Verwijderen", "PE.Views.DocumentHolder.direct270Text": "Tekst omhoog draaien", "PE.Views.DocumentHolder.direct90Text": "Tekst omlaag draaien", "PE.Views.DocumentHolder.directHText": "Horizontaal", "PE.Views.DocumentHolder.directionText": "Tekstrichting", "PE.Views.DocumentHolder.editChartText": "Gegevens bewerken", "PE.Views.DocumentHolder.editHyperlinkText": "Hyperlink bewerken", "PE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "PE.Views.DocumentHolder.hyperlinkText": "Hyperlink", "PE.Views.DocumentHolder.ignoreAllSpellText": "Alles negeren", "PE.Views.DocumentHolder.ignoreSpellText": "Negeren", "PE.Views.DocumentHolder.insertColumnLeftText": "Kolom links", "PE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>s", "PE.Views.DocumentHolder.insertColumnText": "Kolom invoegen", "PE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> boven", "PE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowText": "<PERSON><PERSON><PERSON>voegen", "PE.Views.DocumentHolder.insertText": "Invoegen", "PE.Views.DocumentHolder.langText": "Taal selecteren", "PE.Views.DocumentHolder.latexText": "LaTeX", "PE.Views.DocumentHolder.leftText": "Links", "PE.Views.DocumentHolder.loadSpellText": "Varianten worden geladen...", "PE.Views.DocumentHolder.mergeCellsText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.mniCustomTable": "Aangepaste tabel invoegen", "PE.Views.DocumentHolder.moreText": "Meer varianten...", "PE.Views.DocumentHolder.noSpellVariantsText": "<PERSON><PERSON> varianten", "PE.Views.DocumentHolder.originalSizeText": "Standaar<PERSON>gro<PERSON>", "PE.Views.DocumentHolder.removeHyperlinkText": "Hyperlink verwijderen", "PE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "Selecteren", "PE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "PE.Views.DocumentHolder.spellcheckText": "Spellingcon<PERSON>le", "PE.Views.DocumentHolder.splitCellsText": "Cel splitsen...", "PE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textAddHGuides": "Add horizontal guide", "PE.Views.DocumentHolder.textAddVGuides": "Add Vertical Guide", "PE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON> a<PERSON> sturen", "PE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeFront": "Naar voorgrond brengen", "PE.Views.DocumentHolder.textClearGuides": "Clear guides", "PE.Views.DocumentHolder.textCm": "cm", "PE.Views.DocumentHolder.textCopy": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCrop": "Uitsnijden", "PE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCustom": "Custom", "PE.Views.DocumentHolder.textCut": "Knippen", "PE.Views.DocumentHolder.textDeleteGuide": "Delete guide", "PE.Views.DocumentHolder.textDeleteLayout": "Delete Layout", "PE.Views.DocumentHolder.textDeleteMaster": "Delete Master", "PE.Views.DocumentHolder.textDistributeCols": "<PERSON><PERSON><PERSON><PERSON> verdelen", "PE.Views.DocumentHolder.textDistributeRows": "<PERSON><PERSON><PERSON><PERSON> verdelen", "PE.Views.DocumentHolder.textDuplicateLayout": "Duplicate Layout", "PE.Views.DocumentHolder.textDuplicateSlideMaster": "Duplicate Slide Master", "PE.Views.DocumentHolder.textEditObject": "Edit object", "PE.Views.DocumentHolder.textEditPoints": "Punten bewerken", "PE.Views.DocumentHolder.textFlipH": "Horizontaal omdraaien", "PE.Views.DocumentHolder.textFlipV": "Verticaal omdraaien", "PE.Views.DocumentHolder.textFromFile": "<PERSON>and", "PE.Views.DocumentHolder.textFromStorage": "<PERSON>", "PE.Views.DocumentHolder.textFromUrl": "Van URL", "PE.Views.DocumentHolder.textGridlines": "Gridlines", "PE.Views.DocumentHolder.textGuides": "Guides", "PE.Views.DocumentHolder.textInsertLayout": "Insert Layout", "PE.Views.DocumentHolder.textInsertSlideMaster": "Insert Slide Master", "PE.Views.DocumentHolder.textNextPage": "Volgende dia", "PE.Views.DocumentHolder.textPaste": "Plakken", "PE.Views.DocumentHolder.textPrevPage": "Vorige dia", "PE.Views.DocumentHolder.textRemove": "Remove", "PE.Views.DocumentHolder.textRenameLayout": "<PERSON><PERSON>out", "PE.Views.DocumentHolder.textRenameMaster": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textReplace": "Afbeelding vervangen", "PE.Views.DocumentHolder.textResetCrop": "Reset crop", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "Draaien 90° linksom", "PE.Views.DocumentHolder.textRotate90": "Draaien 90° rechtsom", "PE.Views.DocumentHolder.textRulers": "Rulers", "PE.Views.DocumentHolder.textSaveAsPicture": "Save as picture", "PE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignCenter": "Midden uitlijnen", "PE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Midden uitlijnen", "PE.Views.DocumentHolder.textShapeAlignRight": "Rechts uitlijnen", "PE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "PE.Views.DocumentHolder.textShowGridlines": "Show gridlines", "PE.Views.DocumentHolder.textShowGuides": "Show Guides", "PE.Views.DocumentHolder.textSlideSettings": "Dia-instellingen", "PE.Views.DocumentHolder.textSmartGuides": "Smart Guides", "PE.Views.DocumentHolder.textSnapObjects": "Snap object to grid", "PE.Views.DocumentHolder.textStartAfterPrevious": "Start After Previous", "PE.Views.DocumentHolder.textStartOnClick": "Start On Click", "PE.Views.DocumentHolder.textStartWithPrevious": "Start With Previous", "PE.Views.DocumentHolder.textUndo": "Ongedaan maken", "PE.Views.DocumentHolder.tipGuides": "Show guides", "PE.Views.DocumentHolder.tipIsLocked": "Dit element wordt op dit moment bewerkt door een andere gebruiker.", "PE.Views.DocumentHolder.toDictionaryText": "Toevoegen aan woordenboek", "PE.Views.DocumentHolder.txtAddBottom": "Onderrand toevoegen", "PE.Views.DocumentHolder.txtAddFractionBar": "Deelteken toevoegen", "PE.Views.DocumentHolder.txtAddHor": "Horizontale lijn toe<PERSON>n", "PE.Views.DocumentHolder.txtAddLB": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddLT": "Lijn linksboven toevoegen", "PE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON><PERSON> toe<PERSON>n", "PE.Views.DocumentHolder.txtAddTop": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddVer": "Verticale lijn toe<PERSON>n", "PE.Views.DocumentHolder.txtAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAlignToChar": "Uitlijnen op teken", "PE.Views.DocumentHolder.txtArrange": "Ordenen", "PE.Views.DocumentHolder.txtBackground": "Achtergrond", "PE.Views.DocumentHolder.txtBorderProps": "Randeigenschappen", "PE.Views.DocumentHolder.txtBottom": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtChangeLayout": "Indeling wijzigen", "PE.Views.DocumentHolder.txtChangeTheme": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtDecreaseArg": "Arg<PERSON>nt<PERSON>otte verkleinen", "PE.Views.DocumentHolder.txtDeleteArg": "Argument verwijderen", "PE.Views.DocumentHolder.txtDeleteBreak": "Handmatig einde verwijderen", "PE.Views.DocumentHolder.txtDeleteChars": "Insluitende tekens verwijderen", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Insluitende tekens en scheidingstekens verwijderen", "PE.Views.DocumentHolder.txtDeleteEq": "Vergelijking verwijderen", "PE.Views.DocumentHolder.txtDeleteGroupChar": "Teken verwijderen", "PE.Views.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON> verwi<PERSON>en", "PE.Views.DocumentHolder.txtDeleteSlide": "<PERSON>a verwij<PERSON>en", "PE.Views.DocumentHolder.txtDistribHor": "Horizontaal verdelen", "PE.Views.DocumentHolder.txtDistribVert": "Verticaal verdelen", "PE.Views.DocumentHolder.txtDuplicateSlide": "<PERSON>a dup<PERSON>", "PE.Views.DocumentHolder.txtFractionLinear": "<PERSON><PERSON><PERSON><PERSON>en in Lineaire breuk", "PE.Views.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON><PERSON>zigen in Schuine breuk", "PE.Views.DocumentHolder.txtFractionStacked": "W<PERSON><PERSON>zigen in Gestapelde breuk", "PE.Views.DocumentHolder.txtGroup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtGroupCharOver": "Teken over tekst", "PE.Views.DocumentHolder.txtGroupCharUnder": "Teken onder tekst", "PE.Views.DocumentHolder.txtHideBottom": "Onderrand verbergen", "PE.Views.DocumentHolder.txtHideBottomLimit": "Onderlimiet verbergen", "PE.Views.DocumentHolder.txtHideCloseBracket": "Sluitend haakje verbergen", "PE.Views.DocumentHolder.txtHideDegree": "Graden verbergen", "PE.Views.DocumentHolder.txtHideHor": "Horizontale lijn verbergen", "PE.Views.DocumentHolder.txtHideLB": "<PERSON><PERSON> linksonder verbergen", "PE.Views.DocumentHolder.txtHideLeft": "Linkerrand verbergen", "PE.Views.DocumentHolder.txtHideLT": "Lijn linksboven verbergen", "PE.Views.DocumentHolder.txtHideOpenBracket": "Openend haakje verbergen", "PE.Views.DocumentHolder.txtHidePlaceholder": "Tijdelijke aanduiding verbergen", "PE.Views.DocumentHolder.txtHideRight": "Rechterrand verbergen", "PE.Views.DocumentHolder.txtHideTop": "Bovenrand verbergen", "PE.Views.DocumentHolder.txtHideTopLimit": "Bovenlimiet verbergen", "PE.Views.DocumentHolder.txtHideVer": "Verticale lijn verbergen", "PE.Views.DocumentHolder.txtIncreaseArg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> verhogen", "PE.Views.DocumentHolder.txtInsAudio": "Insert audio", "PE.Views.DocumentHolder.txtInsChart": "Insert chart", "PE.Views.DocumentHolder.txtInsertArgAfter": "Argument invoegen na", "PE.Views.DocumentHolder.txtInsertArgBefore": "Argument invoegen vóór", "PE.Views.DocumentHolder.txtInsertBreak": "Handmatig einde invoegen", "PE.Views.DocumentHolder.txtInsertEqAfter": "Vergelijking invoegen na", "PE.Views.DocumentHolder.txtInsertEqBefore": "Vergelijking invoegen vóór", "PE.Views.DocumentHolder.txtInsImage": "Insert image from file", "PE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "PE.Views.DocumentHolder.txtInsSmartArt": "Insert SmartArt", "PE.Views.DocumentHolder.txtInsTable": "Insert table", "PE.Views.DocumentHolder.txtInsVideo": "Insert video", "PE.Views.DocumentHolder.txtKeepTextOnly": "Alleen tekst behouden", "PE.Views.DocumentHolder.txtLimitChange": "Locatie limieten wijzigen", "PE.Views.DocumentHolder.txtLimitOver": "Limiet over tekst", "PE.Views.DocumentHolder.txtLimitUnder": "Limiet onder tekst", "PE.Views.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON><PERSON><PERSON> aan<PERSON>en aan hoogte argumenten", "PE.Views.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Move slide to end", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Move slide to beginning", "PE.Views.DocumentHolder.txtNewSlide": "<PERSON>eu<PERSON> dia", "PE.Views.DocumentHolder.txtOverbar": "Streep boven tekst", "PE.Views.DocumentHolder.txtPasteDestFormat": "Geb<PERSON>ik doel thema", "PE.Views.DocumentHolder.txtPastePicture": "Afbeelding", "PE.Views.DocumentHolder.txtPasteSourceFormat": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtPressLink": "Druk op {0} en klik op koppeling", "PE.Views.DocumentHolder.txtPreview": "Diavoorstelling starten", "PE.Views.DocumentHolder.txtPrintSelection": "Selectie afdrukken", "PE.Views.DocumentHolder.txtRemFractionBar": "Deelteken verwijderen", "PE.Views.DocumentHolder.txtRemLimit": "Limiet verwijderen", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Accentteken verwijderen", "PE.Views.DocumentHolder.txtRemoveBar": "Deelteken verwijderen", "PE.Views.DocumentHolder.txtRemScripts": "Scripts verwijderen", "PE.Views.DocumentHolder.txtRemSubscript": "Subscript verwijderen", "PE.Views.DocumentHolder.txtRemSuperscript": "Superscript verwijderen", "PE.Views.DocumentHolder.txtResetLayout": "Reset dia", "PE.Views.DocumentHolder.txtScriptsAfter": "Scripts na tekst", "PE.Views.DocumentHolder.txtScriptsBefore": "Scripts vóór tekst", "PE.Views.DocumentHolder.txtSelectAll": "Alles selecteren", "PE.Views.DocumentHolder.txtShowBottomLimit": "Onderlimiet tonen", "PE.Views.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON>nd haakje tonen", "PE.Views.DocumentHolder.txtShowDegree": "Graden weergeven", "PE.Views.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON> haakje tonen", "PE.Views.DocumentHolder.txtShowPlaceholder": "Tijdelijke aanduiding tonen", "PE.Views.DocumentHolder.txtShowTopLimit": "Bovenlimiet tonen", "PE.Views.DocumentHolder.txtSlide": "<PERSON>a", "PE.Views.DocumentHolder.txtSlideHide": "Slide V<PERSON>en", "PE.Views.DocumentHolder.txtStretchBrackets": "Vierkante haken uitrekken", "PE.Views.DocumentHolder.txtTop": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtUnderbar": "Streep onder tekst", "PE.Views.DocumentHolder.txtUngroup": "<PERSON><PERSON><PERSON><PERSON> ophe<PERSON>n", "PE.Views.DocumentHolder.txtWarnUrl": "Het aanklik<PERSON> van deze link kan schadelijk zijn voor uw apparaat en data.\nWeet u zeker dat u door wilt gaan?", "PE.Views.DocumentHolder.unicodeText": "Unicode", "PE.Views.DocumentHolder.vertAlignText": "Verticale uitlijning", "PE.Views.DocumentPreview.goToSlideText": "Naar dia gaan", "PE.Views.DocumentPreview.slideIndexText": "Dia {0} van {1}", "PE.Views.DocumentPreview.txtClose": "Diavoorstelling sluiten", "PE.Views.DocumentPreview.txtDraw": "Draw", "PE.Views.DocumentPreview.txtEndSlideshow": "Slideshow beëindigen", "PE.Views.DocumentPreview.txtEraser": "Eraser", "PE.Views.DocumentPreview.txtEraseScreen": "Erase screen", "PE.Views.DocumentPreview.txtExitFullScreen": "Volledig scherm verlaten", "PE.Views.DocumentPreview.txtFinalMessage": "Einde van diavoorbeeld. Klik om af te sluiten.", "PE.Views.DocumentPreview.txtFullScreen": "Volledig scherm", "PE.Views.DocumentPreview.txtHighlighter": "Highlighter", "PE.Views.DocumentPreview.txtInkColor": "Ink color", "PE.Views.DocumentPreview.txtNext": "Volgende dia", "PE.Views.DocumentPreview.txtPageNumInvalid": "Ongeldig dianummer", "PE.Views.DocumentPreview.txtPause": "Presentatie onder<PERSON>ken", "PE.Views.DocumentPreview.txtPen": "Pen", "PE.Views.DocumentPreview.txtPlay": "<PERSON><PERSON>e starten", "PE.Views.DocumentPreview.txtPrev": "Vorige dia", "PE.Views.DocumentPreview.txtReset": "Opnieuw instellen", "PE.Views.FileMenu.ariaFileMenu": "File menu", "PE.Views.FileMenu.btnAboutCaption": "Over", "PE.Views.FileMenu.btnBackCaption": "Naar documenten", "PE.Views.FileMenu.btnCloseEditor": "Close File", "PE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON> sluiten", "PE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON> maken", "PE.Views.FileMenu.btnDownloadCaption": "Downloaden als", "PE.Views.FileMenu.btnExitCaption": "Afsluiten", "PE.Views.FileMenu.btnFileOpenCaption": "Openen", "PE.Views.FileMenu.btnHelpCaption": "Help", "PE.Views.FileMenu.btnHistoryCaption": "Vers<PERSON> geschiedenis", "PE.Views.FileMenu.btnInfoCaption": "Info over presentatie", "PE.Views.FileMenu.btnPrintCaption": "Afdrukken", "PE.Views.FileMenu.btnProtectCaption": "Beveiligen", "PE.Views.FileMenu.btnRecentFilesCaption": "Recente openen", "PE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnReturnCaption": "Terug naar presentatie", "PE.Views.FileMenu.btnRightsCaption": "Toegangsrechten", "PE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON><PERSON> als", "PE.Views.FileMenu.btnSaveCaption": "Opsla<PERSON>", "PE.Views.FileMenu.btnSaveCopyAsCaption": "<PERSON><PERSON> op<PERSON> als", "PE.Views.FileMenu.btnSettingsCaption": "Geavanceerde instellingen", "PE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "PE.Views.FileMenu.btnToEditCaption": "Presentatie bewerken", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON>ge presentatie", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON><PERSON> maken", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Toepassen", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Voeg auteur toe", "PE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Tekst toevoegen", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Applicatie", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Toegangsrechten wijzigen", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Opmerking", "PE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Aangemaakt", "PE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Laatst aangepast door", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Laatst aangepast", "PE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Locatie", "PE.Views.FileMenuPanels.DocumentInfo.txtPresentationInfo": "Presentation Info", "PE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "PE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON> met rechten", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Onderwerp", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Presentatietitel", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Geupload", "PE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "PE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access rights", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Toegangsrechten wijzigen", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON> met rechten", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Waarschuwing", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON> wachtwoord", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON><PERSON><PERSON><PERSON> present<PERSON>e", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "Met handtekening", "PE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the presentation.<br>The presentation is protected from editing.", "PE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the presentation by adding an<br>invisible digital signature", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Presentatie bewerken", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Aanpassingen zorgen ervoor dat de handtekening van deze presentatie verwijderd worden.<br>Weet u zeker dat u door wilt gaan?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Deze presentatie is beveiligd met een wachtwoord", "PE.Views.FileMenuPanels.ProtectDoc.txtProtectPresentation": "Encrypt this presentation with a password", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Geldige hantekeningen zijn toegevoegd aan de presentatie. Deze presentatie is beveiligd tegen aanpassingen.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "<PERSON><PERSON> of meer digitale handtekeningen in deze presentatie zijn ongeldig of konden niet geverifieerd worden. Deze presentatie is beveiligd tegen aanpassingen.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON>n hand<PERSON>en", "PE.Views.FileMenuPanels.Settings.okButtonText": "Toepassen", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Modus Gezamenlijk bewerken", "PE.Views.FileMenuPanels.Settings.strFast": "Snel", "PE.Views.FileMenuPanels.Settings.strFontRender": "Hints voor lettertype", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignore words in UPPERCASE", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignore words with numbers", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Macro instellingen", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Toon de knop Plakopties wanneer de inhoud is geplakt", "PE.Views.FileMenuPanels.Settings.strRTLSupport": "RTL interface", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Show changes from other users", "PE.Views.FileMenuPanels.Settings.strStrict": "Strikt", "PE.Views.FileMenuPanels.Settings.strTabStyle": "Tab style", "PE.Views.FileMenuPanels.Settings.strTheme": "<PERSON>a", "PE.Views.FileMenuPanels.Settings.strUnit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strZoom": "Standaardwaarde zoomen", "PE.Views.FileMenuPanels.Settings.text10Minutes": "<PERSON><PERSON> 10 minuten", "PE.Views.FileMenuPanels.Settings.text30Minutes": "<PERSON>ke 30 minuten", "PE.Views.FileMenuPanels.Settings.text5Minutes": "<PERSON><PERSON> 5 minuten", "PE.Views.FileMenuPanels.Settings.text60Minutes": "Elk uur", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Uitlijningshulplijnen", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "AutoHerstel", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Automatisch opslaan", "PE.Views.FileMenuPanels.Settings.textDisabled": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textFill": "Vulling", "PE.Views.FileMenuPanels.Settings.textForceSave": "Opslaan op server", "PE.Views.FileMenuPanels.Settings.textLine": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textMinute": "<PERSON><PERSON> minuut", "PE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "Advanced settings", "PE.Views.FileMenuPanels.Settings.txtAll": "<PERSON>es weergeven", "PE.Views.FileMenuPanels.Settings.txtAppearance": "V<PERSON><PERSON><PERSON>ing", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Instellingen automatische spellingscontrole", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "standaard cache modus", "PE.Views.FileMenuPanels.Settings.txtCm": "Centimeter", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Samenwerken", "PE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "Customize quick access", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Bewerken en opslaan", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Real-time co-editing. All changes are saved automatically", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "<PERSON><PERSON><PERSON><PERSON> aan dia", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "<PERSON>an <PERSON>te aan<PERSON>en", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Hieroglyphs", "PE.Views.FileMenuPanels.Settings.txtInch": "Inch", "PE.Views.FileMenuPanels.Settings.txtLast": "Laatste weergeven", "PE.Views.FileMenuPanels.Settings.txtLastUsed": "Last used", "PE.Views.FileMenuPanels.Settings.txtMac": "als OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "Native", "PE.Views.FileMenuPanels.Settings.txtProofing": "Controleren", "PE.Views.FileMenuPanels.Settings.txtPt": "Punt", "PE.Views.FileMenuPanels.Settings.txtQuickPrint": "Show the Quick Print button in the editor header", "PE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Alles inschakelen", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "<PERSON><PERSON><PERSON> alle macro's in zonder een notificatie", "PE.Views.FileMenuPanels.Settings.txtScreenReader": "Turn on screen reader support", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Spellingcon<PERSON>le", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Alles u<PERSON>", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "<PERSON><PERSON><PERSON> alle macro's uit zonder melding", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Use the \"Save\" button to sync the changes you and others make", "PE.Views.FileMenuPanels.Settings.txtTabBack": "Use toolbar color as tabs background", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "<PERSON><PERSON><PERSON><PERSON> notificatie", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "<PERSON><PERSON><PERSON> alle macro's uit met een melding", "PE.Views.FileMenuPanels.Settings.txtWin": "als Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "Workspace", "PE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "PE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save Copy As", "PE.Views.GridSettings.textCm": "cm", "PE.Views.GridSettings.textCustom": "Custom", "PE.Views.GridSettings.textSpacing": "Spacing", "PE.Views.GridSettings.textTitle": "Grid settings", "PE.Views.HeaderFooterDialog.applyAllText": "Op alles toepassen", "PE.Views.HeaderFooterDialog.applyText": "Toepassen", "PE.Views.HeaderFooterDialog.diffLanguage": "U kunt geen datumnotatie geb<PERSON>iken in een andere taal dan het diamodel. <br> Als u het model wilt wijzigen, klikt u op 'Toepassen op alles' in plaats van 'Toepassen'.", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Waarschuwing", "PE.Views.HeaderFooterDialog.textDateTime": "Datum & tijd", "PE.Views.HeaderFooterDialog.textFixed": "Vast", "PE.Views.HeaderFooterDialog.textFormat": "Form<PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textHFTitle": "Header/Footer settings", "PE.Views.HeaderFooterDialog.textLang": "Taal", "PE.Views.HeaderFooterDialog.textNotes": "Notes and handouts", "PE.Views.HeaderFooterDialog.textNotTitle": "<PERSON>et weergeven op titeldia", "PE.Views.HeaderFooterDialog.textPageNum": "Page number", "PE.Views.HeaderFooterDialog.textPreview": "Voorbeeld", "PE.Views.HeaderFooterDialog.textSlide": "Slide", "PE.Views.HeaderFooterDialog.textSlideNum": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textUpdate": "Automatisch updaten", "PE.Views.HeaderFooterDialog.txtFooter": "Footer", "PE.Views.HeaderFooterDialog.txtHeader": "Header", "PE.Views.HyperlinkSettingsDialog.strDisplay": "Weergeven", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON><PERSON> aan", "PE.Views.HyperlinkSettingsDialog.textDefault": "Geselecteerd tekstfragment", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Bijschrift hier invoeren", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "<PERSON><PERSON><PERSON> hier invoeren", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "<PERSON><PERSON><PERSON> hier invoeren", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Externe koppeling", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Dia in deze presentatie", "PE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "PE.Views.HyperlinkSettingsDialog.textSlides": "Dia's", "PE.Views.HyperlinkSettingsDialog.textTipText": "<PERSON><PERSON><PERSON> van Scherminfo", "PE.Views.HyperlinkSettingsDialog.textTitle": "Instellingen hyperlink", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "Dit veld is vereist", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Eerste dia", "PE.Views.HyperlinkSettingsDialog.txtLast": "Laatste dia", "PE.Views.HyperlinkSettingsDialog.txtNext": "Volgende dia", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Dit veld moet een URL in de notatie \"http://www.voorbeeld.com\" bevatten", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Vorige dia", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Dit veld is beperkt tot 2083 tekens", "PE.Views.HyperlinkSettingsDialog.txtSlide": "<PERSON>a", "PE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "PE.Views.ImageSettings.strTransparency": "Opacity", "PE.Views.ImageSettings.textAdvanced": "Geavanceerde instellingen tonen", "PE.Views.ImageSettings.textCrop": "Uitsnijden", "PE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropToShape": "Bijsnijden naar vorm", "PE.Views.ImageSettings.textEdit": "Bewerken", "PE.Views.ImageSettings.textEditObject": "Object bewerken", "PE.Views.ImageSettings.textFitSlide": "<PERSON><PERSON><PERSON><PERSON> aan dia", "PE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFromFile": "<PERSON>and", "PE.Views.ImageSettings.textFromStorage": "<PERSON>", "PE.Views.ImageSettings.textFromUrl": "Van URL", "PE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textHint270": "Draaien 90° linksom", "PE.Views.ImageSettings.textHint90": "Draaien 90° rechtsom", "PE.Views.ImageSettings.textHintFlipH": "Horizontaal omdraaien", "PE.Views.ImageSettings.textHintFlipV": "Verticaal omdraaien", "PE.Views.ImageSettings.textInsert": "Afbeelding vervangen", "PE.Views.ImageSettings.textOriginalSize": "Standaar<PERSON>gro<PERSON>", "PE.Views.ImageSettings.textRecentlyUsed": "Recently used", "PE.Views.ImageSettings.textResetCrop": "Reset crop", "PE.Views.ImageSettings.textRotate90": "Draaien 90°", "PE.Views.ImageSettings.textRotation": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textSize": "Grootte", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Alternatieve tekst", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Beschrijving", "PE.Views.ImageSettingsAdvanced.textAltTip": "De alternatieve, op tekst gebaseerde weergave van de visuele objectinformatie. Deze wordt voorgelezen voor mensen met visuele of cognitieve handicaps om hen te helpen begrijpen welke informatie aanwezig is in de afbeelding, Vorm, grafiek of tabel.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Titel", "PE.Views.ImageSettingsAdvanced.textAngle": "Hoek", "PE.Views.ImageSettingsAdvanced.textCenter": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textFrom": "<PERSON>", "PE.Views.ImageSettingsAdvanced.textGeneral": "General", "PE.Views.ImageSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontaal", "PE.Views.ImageSettingsAdvanced.textImageName": "Image name", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON> ver<PERSON>", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "Ware grootte", "PE.Views.ImageSettingsAdvanced.textPlacement": "Plaatsing", "PE.Views.ImageSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textSize": "Grootte", "PE.Views.ImageSettingsAdvanced.textTitle": "Afbeelding - Geavanceerde instellingen", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "Top left corner", "PE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "PE.Views.ImageSettingsAdvanced.textVertically": "Verticaal ", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.ariaLeftMenu": "Left menu", "PE.Views.LeftMenu.tipAbout": "Over", "PE.Views.LeftMenu.tipChat": "Cha<PERSON>", "PE.Views.LeftMenu.tipComments": "Opmerkingen", "PE.Views.LeftMenu.tipPlugins": "Plug-ins", "PE.Views.LeftMenu.tipSearch": "<PERSON><PERSON>", "PE.Views.LeftMenu.tipSlides": "Dia's", "PE.Views.LeftMenu.tipSupport": "<PERSON>ed<PERSON> en Support", "PE.Views.LeftMenu.tipTitles": "Titels", "PE.Views.LeftMenu.txtDeveloper": "ONTWIKKELAARSMODUS", "PE.Views.LeftMenu.txtEditor": "Presentation Editor", "PE.Views.LeftMenu.txtLimit": "Beperk toegang", "PE.Views.LeftMenu.txtTrial": "TEST MODUS", "PE.Views.LeftMenu.txtTrialDev": "Proefontwikkelaarsmodus", "PE.Views.ParagraphSettings.strLineHeight": "Regelafstand", "PE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON><PERSON><PERSON> tussen alinea's", "PE.Views.ParagraphSettings.strSpacingAfter": "Na", "PE.Views.ParagraphSettings.strSpacingBefore": "Vóór", "PE.Views.ParagraphSettings.textAdvanced": "Geavanceerde instellingen tonen", "PE.Views.ParagraphSettings.textAt": "Om", "PE.Views.ParagraphSettings.textAtLeast": "Ten minste", "PE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textExact": "Exact", "PE.Views.ParagraphSettings.txtAutoText": "Automatisch", "PE.Views.ParagraphSettingsAdvanced.noTabs": "De opgegeven tabbladen worden in dit veld weergegeven", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "Allemaal hoofdletters", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndent": "Inspringen", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Links", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Regelafstand", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Na", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Voor", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Speciaal", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Lettertype", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Inspringingen en plaatsing", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON> hoofdletters", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Afstand", "PE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscript", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superscript", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Tab", "PE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textAuto": "meerdere", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Tekenafstand", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Standaardtabblad", "PE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textExact": "exact", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "Eerste alinea", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Hangend", "PE.Views.ParagraphSettingsAdvanced.textJustified": "Uitgevuld", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(geen)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Verwijderen", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Alles verwijderen", "PE.Views.ParagraphSettingsAdvanced.textSet": "Opgeven", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "Links", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Alinea - Geavanceerde instellingen", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automatisch", "PE.Views.PrintWithPreview.txtAllPages": "All slides", "PE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "PE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "PE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "PE.Views.PrintWithPreview.txtCopies": "Copies", "PE.Views.PrintWithPreview.txtCurrentPage": "Current slide", "PE.Views.PrintWithPreview.txtCustomPages": "Custom print", "PE.Views.PrintWithPreview.txtEmptyTable": "There is nothing to print because the presentation is empty", "PE.Views.PrintWithPreview.txtHeaderFooterSettings": "Header/footer settings", "PE.Views.PrintWithPreview.txtOf": "of {0}", "PE.Views.PrintWithPreview.txtOneSide": "Print one sided", "PE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "PE.Views.PrintWithPreview.txtPage": "Slide", "PE.Views.PrintWithPreview.txtPageNumInvalid": "Slide number invalid", "PE.Views.PrintWithPreview.txtPages": "Slides", "PE.Views.PrintWithPreview.txtPaperSize": "Paper size", "PE.Views.PrintWithPreview.txtPrint": "Print", "PE.Views.PrintWithPreview.txtPrintPdf": "Print to PDF", "PE.Views.PrintWithPreview.txtPrintRange": "Print range", "PE.Views.PrintWithPreview.txtPrintSides": "Print sides", "PE.Views.RightMenu.ariaRightMenu": "Right menu", "PE.Views.RightMenu.txtChartSettings": "Grafiekinstellingen", "PE.Views.RightMenu.txtImageSettings": "Afbeeldingsinstellingen", "PE.Views.RightMenu.txtParagraphSettings": "Tekstinstellingen", "PE.Views.RightMenu.txtShapeSettings": "Vorminstellingen", "PE.Views.RightMenu.txtSignatureSettings": "Handtekening instellingen", "PE.Views.RightMenu.txtSlideSettings": "Dia-instellingen", "PE.Views.RightMenu.txtTableSettings": "Tabelinstellingen", "PE.Views.RightMenu.txtTextArtSettings": "Tekst Art instellingen", "PE.Views.ShapeSettings.strBackground": "<PERSON>chtergrondkleur", "PE.Views.ShapeSettings.strChange": "AutoVorm wijzigen", "PE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strFill": "Vulling", "PE.Views.ShapeSettings.strForeground": "Voorgrondkleur", "PE.Views.ShapeSettings.strPattern": "Patroon", "PE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>duw", "PE.Views.ShapeSettings.strSize": "Grootte", "PE.Views.ShapeSettings.strStroke": "Streek", "PE.Views.ShapeSettings.strTransparency": "Ondoorzichtigheid", "PE.Views.ShapeSettings.strType": "Type", "PE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "PE.Views.ShapeSettings.textAdvanced": "Geavanceerde instellingen tonen", "PE.Views.ShapeSettings.textAngle": "Hoek", "PE.Views.ShapeSettings.textBorderSizeErr": "De ingevoerde waarde is onjuist.<br><PERSON><PERSON><PERSON> een waarde tussen 0 pt en 1584 pt in.", "PE.Views.ShapeSettings.textColor": "Kleuropvulling", "PE.Views.ShapeSettings.textDirection": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textEditPoints": "Edit points", "PE.Views.ShapeSettings.textEditShape": "Edit shape", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON> pat<PERSON>", "PE.Views.ShapeSettings.textEyedropper": "Eyedropper", "PE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFromFile": "<PERSON>and", "PE.Views.ShapeSettings.textFromStorage": "<PERSON>", "PE.Views.ShapeSettings.textFromUrl": "Van URL", "PE.Views.ShapeSettings.textGradient": "Kleurovergangpunten", "PE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON><PERSON> met kleurovergang", "PE.Views.ShapeSettings.textHint270": "Draaien 90° linksom", "PE.Views.ShapeSettings.textHint90": "Draaien 90° rechtsom", "PE.Views.ShapeSettings.textHintFlipH": "Horizontaal omdraaien", "PE.Views.ShapeSettings.textHintFlipV": "Verticaal omdraaien", "PE.Views.ShapeSettings.textImageTexture": "Afbeelding of textuur", "PE.Views.ShapeSettings.textLinear": "Lineair", "PE.Views.ShapeSettings.textMoreColors": "More colors", "PE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON> vulling", "PE.Views.ShapeSettings.textNoShadow": "No Shadow", "PE.Views.ShapeSettings.textPatternFill": "Patroon", "PE.Views.ShapeSettings.textPosition": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textRadial": "Ra<PERSON>al", "PE.Views.ShapeSettings.textRecentlyUsed": "Recently used", "PE.Views.ShapeSettings.textRotate90": "Draaien 90°", "PE.Views.ShapeSettings.textRotation": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textSelectImage": "selecteer afbeelding", "PE.Views.ShapeSettings.textSelectTexture": "Selecteren", "PE.Views.ShapeSettings.textShadow": "Shadow", "PE.Views.ShapeSettings.textStretch": "Uitrekken", "PE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textTexture": "<PERSON>", "PE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "PE.Views.ShapeSettings.tipAddGradientPoint": "Kleurovergangpunt toevoegen", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Kleurovergangpunt verwijderen", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papier", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON> stof", "PE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtGranite": "Gran<PERSON>", "PE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON> lijn", "PE.Views.ShapeSettings.txtPapyrus": "Papyrus", "PE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strMargins": "Opvulling van tekst", "PE.Views.ShapeSettingsAdvanced.textAlt": "Alternatieve tekst", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Beschrijving", "PE.Views.ShapeSettingsAdvanced.textAltTip": "De alternatieve, op tekst gebaseerde weergave van de visuele objectinformatie. Deze wordt voorgelezen voor mensen met visuele of cognitieve handicaps om hen te helpen begrijpen welke informatie aanwezig is in de afbeelding, Vorm, grafiek of tabel.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Titel", "PE.Views.ShapeSettingsAdvanced.textAngle": "Hoek", "PE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Automatisch passend maken", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Begingrootte", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Beginstijl", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON> rand", "PE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textCapType": "Type eindstuk", "PE.Views.ShapeSettingsAdvanced.textCenter": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textColNumber": "Aantal kolommen", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Eindgro<PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Eindstijl", "PE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFrom": "<PERSON>", "PE.Views.ShapeSettingsAdvanced.textGeneral": "General", "PE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontaal", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Type join", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON> ver<PERSON>", "PE.Views.ShapeSettingsAdvanced.textLeft": "Links", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "Lijnstijl", "PE.Views.ShapeSettingsAdvanced.textMiter": "Verstek", "PE.Views.ShapeSettingsAdvanced.textNofit": "Niet automatisch aanpassen", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Placement", "PE.Views.ShapeSettingsAdvanced.textPosition": "Position", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Formaat aan<PERSON>en aan tekst", "PE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textShapeName": "Shape name", "PE.Views.ShapeSettingsAdvanced.textShrink": "<PERSON><PERSON><PERSON> verkleinen bij overloop", "PE.Views.ShapeSettingsAdvanced.textSize": "Grootte", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Afstand tussen kolommen", "PE.Views.ShapeSettingsAdvanced.textSquare": "Vierkan<PERSON>", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Tekstvak", "PE.Views.ShapeSettingsAdvanced.textTitle": "Vorm - Geavanceerde instellingen", "PE.Views.ShapeSettingsAdvanced.textTop": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "Top left corner", "PE.Views.ShapeSettingsAdvanced.textVertical": "Vertical", "PE.Views.ShapeSettingsAdvanced.textVertically": "Verticaal ", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Gewichten & pijlen", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "<PERSON><PERSON>", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Waarschuwing", "PE.Views.SignatureSettings.strDelete": "Handtekening verwijderen", "PE.Views.SignatureSettings.strDetails": "Handtekening details", "PE.Views.SignatureSettings.strInvalid": "Ongeldige handtekeningen", "PE.Views.SignatureSettings.strSign": "Onderteken", "PE.Views.SignatureSettings.strSignature": "Handtekening", "PE.Views.SignatureSettings.strValid": "Geldige handtekeningen", "PE.Views.SignatureSettings.txtContinueEditing": "Hoe dan ook bewerken", "PE.Views.SignatureSettings.txtEditWarning": "Aanpassingen zorgen ervoor dat de handtekening van deze presentatie verwijderd worden.<br>Weet u zeker dat u door wilt gaan?", "PE.Views.SignatureSettings.txtRemoveWarning": "Wilt u deze handtekening verwijderen? <br> Het kan niet ongedaan worden gemaakt. ", "PE.Views.SignatureSettings.txtSigned": "Geldige hantekeningen zijn toegevoegd aan de presentatie. Deze presentatie is beveiligd tegen aanpassingen.", "PE.Views.SignatureSettings.txtSignedInvalid": "<PERSON><PERSON> of meer digitale handtekeningen in deze presentatie zijn ongeldig of konden niet geverifieerd worden. Deze presentatie is beveiligd tegen aanpassingen.", "PE.Views.SlideSettings.strApplyAllSlides": "Apply to All Slides", "PE.Views.SlideSettings.strBackground": "<PERSON>chtergrondkleur", "PE.Views.SlideSettings.strBackgroundGraphics": "Show Background graphics", "PE.Views.SlideSettings.strBackgroundReset": "Reset Background", "PE.Views.SlideSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strDateTime": "<PERSON>er<PERSON><PERSON> datum & tijd", "PE.Views.SlideSettings.strFill": "Achtergrond", "PE.Views.SlideSettings.strForeground": "Voorgrondkleur", "PE.Views.SlideSettings.strPattern": "Patroon", "PE.Views.SlideSettings.strSlideNum": "Toon dia nummer", "PE.Views.SlideSettings.strTransparency": "Ondoorzichtigheid", "PE.Views.SlideSettings.textAdvanced": "Geavanceerde instellingen tonen", "PE.Views.SlideSettings.textAngle": "Hoek", "PE.Views.SlideSettings.textColor": "Kleuropvulling", "PE.Views.SlideSettings.textDirection": "<PERSON><PERSON>", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON><PERSON> pat<PERSON>", "PE.Views.SlideSettings.textFromFile": "<PERSON>and", "PE.Views.SlideSettings.textFromStorage": "<PERSON>", "PE.Views.SlideSettings.textFromUrl": "Van URL", "PE.Views.SlideSettings.textGradient": "Kleurovergangpunten", "PE.Views.SlideSettings.textGradientFill": "<PERSON><PERSON><PERSON> met kleurovergang", "PE.Views.SlideSettings.textImageTexture": "Afbeelding of textuur", "PE.Views.SlideSettings.textLinear": "Lineair", "PE.Views.SlideSettings.textNoFill": "<PERSON><PERSON> vulling", "PE.Views.SlideSettings.textPatternFill": "Patroon", "PE.Views.SlideSettings.textPosition": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textRadial": "Ra<PERSON>al", "PE.Views.SlideSettings.textReset": "Wijzigingen herstellen", "PE.Views.SlideSettings.textSelectImage": "selecteer afbeelding", "PE.Views.SlideSettings.textSelectTexture": "Selecteren", "PE.Views.SlideSettings.textStretch": "Uitrekken", "PE.Views.SlideSettings.textStyle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textTexture": "<PERSON>", "PE.Views.SlideSettings.textTile": "<PERSON><PERSON>", "PE.Views.SlideSettings.tipAddGradientPoint": "Kleurovergangpunt toevoegen", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Kleurovergangpunt verwijderen", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papier", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON><PERSON><PERSON> stof", "PE.Views.SlideSettings.txtGrain": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtGranite": "Gran<PERSON>", "PE.Views.SlideSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "Papyrus", "PE.Views.SlideSettings.txtWood": "<PERSON><PERSON>", "PE.Views.SlideshowSettings.textLoop": "<PERSON><PERSON><PERSON> tot op ESC wordt gedrukt", "PE.Views.SlideshowSettings.textTitle": "Instellingen tonen", "PE.Views.SlideSizeSettings.strLandscape": "Liggend", "PE.Views.SlideSizeSettings.strPortrait": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textSlideOrientation": "Afdruk<PERSON> van dia", "PE.Views.SlideSizeSettings.textSlideSize": "Diagrootte", "PE.Views.SlideSizeSettings.textTitle": "Instellingen diagrootte", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "35-mm dia's", "PE.Views.SlideSizeSettings.txtA3": "A3-papier (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "A4-papier (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO)-papier (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO)-papier (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "Aangepast", "PE.Views.SlideSizeSettings.txtLedger": "Grootboek (279 x 432 mm)", "PE.Views.SlideSizeSettings.txtLetter": "Brief (216 x 279 mm)", "PE.Views.SlideSizeSettings.txtOverhead": "Overhead", "PE.Views.SlideSizeSettings.txtSlideNum": "Number slides from", "PE.Views.SlideSizeSettings.txtStandard": "Standaard (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Breedbeeld", "PE.Views.Statusbar.goToPageText": "Naar dia gaan", "PE.Views.Statusbar.pageIndexText": "Dia {0} van {1}", "PE.Views.Statusbar.textShowBegin": "Vanaf begin weergeven", "PE.Views.Statusbar.textShowCurrent": "Vanaf huidige dia tonen", "PE.Views.Statusbar.textShowPresenterView": "Presentatieweergave tonen", "PE.Views.Statusbar.textSlideMaster": "Slide master", "PE.Views.Statusbar.tipAccessRights": "Toegangsrechten documenten beheren", "PE.Views.Statusbar.tipFitPage": "<PERSON><PERSON><PERSON><PERSON> aan dia", "PE.Views.Statusbar.tipFitWidth": "<PERSON>an <PERSON>te aan<PERSON>en", "PE.Views.Statusbar.tipPreview": "Diavoorstelling starten", "PE.Views.Statusbar.tipSetLang": "<PERSON><PERSON> van tekst instellen", "PE.Views.Statusbar.tipZoomFactor": "Zoomen", "PE.Views.Statusbar.tipZoomIn": "Inzoomen", "PE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Statusbar.txtPageNumInvalid": "Ongeldig dianummer", "PE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON>m verwijderen", "PE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.deleteTableText": "<PERSON>bel verwijderen", "PE.Views.TableSettings.insertColumnLeftText": "Kolom links invoegen", "PE.Views.TableSettings.insertColumnRightText": "<PERSON><PERSON>m rechts invoegen", "PE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON> boven invoegen", "PE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON><PERSON> onder in<PERSON>n", "PE.Views.TableSettings.mergeCellsText": "<PERSON><PERSON>", "PE.Views.TableSettings.selectCellText": "Cel selecteren", "PE.Views.TableSettings.selectColumnText": "Kolom selecteren", "PE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectTableText": "Tabel selecteren", "PE.Views.TableSettings.splitCellsText": "Cel splitsen...", "PE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textAdvanced": "Geavanceerde instellingen tonen", "PE.Views.TableSettings.textBackColor": "<PERSON>chtergrondkleur", "PE.Views.TableSettings.textBanded": "Gestreept", "PE.Views.TableSettings.textBorderColor": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textCellSize": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textDistributeCols": "<PERSON><PERSON><PERSON><PERSON> verdelen", "PE.Views.TableSettings.textDistributeRows": "<PERSON><PERSON><PERSON><PERSON> verdelen", "PE.Views.TableSettings.textEdit": "Rijen en kolommen", "PE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON>", "PE.Views.TableSettings.textFirst": "Eerste", "PE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textLast": "Laatste", "PE.Views.TableSettings.textRows": "Rijen", "PE.Views.TableSettings.textSelectBorders": "Selecteer de randen die u wilt wijzigen door de hierboven gekozen stijl toe te passen", "PE.Views.TableSettings.textTemplate": "Selecteren uit sja<PERSON>on", "PE.Views.TableSettings.textTotal": "Totaal", "PE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "Buitenrand en alle binnenlijnen instellen", "PE.Views.TableSettings.tipBottom": "<PERSON><PERSON> buit<PERSON> onder instellen", "PE.Views.TableSettings.tipInner": "<PERSON><PERSON> bin<PERSON> instellen", "PE.Views.TableSettings.tipInnerHor": "Alleen horizontale binnenlijnen instellen", "PE.Views.TableSettings.tipInnerVert": "Alleen verticale binnenlijnen instellen", "PE.Views.TableSettings.tipLeft": "<PERSON><PERSON> links instellen", "PE.Views.TableSettings.tipNone": "<PERSON><PERSON> randen instellen", "PE.Views.TableSettings.tipOuter": "<PERSON><PERSON> b<PERSON> instellen", "PE.Views.TableSettings.tipRight": "<PERSON><PERSON> buit<PERSON>rand rechts instellen", "PE.Views.TableSettings.tipTop": "<PERSON><PERSON> buit<PERSON>rand boven instellen", "PE.Views.TableSettings.txtGroupTable_Custom": "Custom", "PE.Views.TableSettings.txtGroupTable_Dark": "<PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Light": "Licht", "PE.Views.TableSettings.txtGroupTable_Medium": "Medium", "PE.Views.TableSettings.txtGroupTable_Optimal": "Best match for document", "PE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON> randen", "PE.Views.TableSettings.txtTable_Accent": "Accent", "PE.Views.TableSettings.txtTable_DarkStyle": "<PERSON><PERSON><PERSON> stijl", "PE.Views.TableSettings.txtTable_LightStyle": "lichte stijl", "PE.Views.TableSettings.txtTable_MediumStyle": "G<PERSON><PERSON><PERSON><PERSON> stijl", "PE.Views.TableSettings.txtTable_NoGrid": "<PERSON>n raster", "PE.Views.TableSettings.txtTable_NoStyle": "<PERSON><PERSON> s<PERSON>", "PE.Views.TableSettings.txtTable_TableGrid": "Tabel raster", "PE.Views.TableSettings.txtTable_ThemedStyle": "<PERSON><PERSON><PERSON><PERSON>ti<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textAlt": "Alternatieve tekst", "PE.Views.TableSettingsAdvanced.textAltDescription": "Beschrijving", "PE.Views.TableSettingsAdvanced.textAltTip": "De alternatieve, op tekst gebaseerde weergave van de visuele objectinformatie. Deze wordt voorgelezen voor mensen met visuele of cognitieve handicaps om hen te helpen begrijpen welke informatie aanwezig is in de afbeelding, Vorm, grafiek of tabel.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Titel", "PE.Views.TableSettingsAdvanced.textBottom": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textCenter": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Standaardmarges gebruiken", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Standaardmarges", "PE.Views.TableSettingsAdvanced.textFrom": "<PERSON>", "PE.Views.TableSettingsAdvanced.textGeneral": "General", "PE.Views.TableSettingsAdvanced.textHeight": "Height", "PE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.TableSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON> ver<PERSON>", "PE.Views.TableSettingsAdvanced.textLeft": "Links", "PE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textPlacement": "Placement", "PE.Views.TableSettingsAdvanced.textPosition": "Position", "PE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textSize": "Size", "PE.Views.TableSettingsAdvanced.textTableName": "Table name", "PE.Views.TableSettingsAdvanced.textTitle": "<PERSON><PERSON> <PERSON> Gea<PERSON>erd<PERSON> instellingen", "PE.Views.TableSettingsAdvanced.textTop": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "Top left corner", "PE.Views.TableSettingsAdvanced.textVertical": "Vertical", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "Marges", "PE.Views.TextArtSettings.strBackground": "<PERSON>chtergrondkleur", "PE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strFill": "Vulling", "PE.Views.TextArtSettings.strForeground": "Voorgrondkleur", "PE.Views.TextArtSettings.strPattern": "Patroon", "PE.Views.TextArtSettings.strSize": "Grootte", "PE.Views.TextArtSettings.strStroke": "Streek", "PE.Views.TextArtSettings.strTransparency": "Ondoorzichtigheid", "PE.Views.TextArtSettings.strType": "Type", "PE.Views.TextArtSettings.textAngle": "Hoek", "PE.Views.TextArtSettings.textBorderSizeErr": "De ingevoerde waarde is onjuist.<br><PERSON><PERSON><PERSON> een waarde tussen 0 pt en 1584 pt in.", "PE.Views.TextArtSettings.textColor": "Kleuropvulling", "PE.Views.TextArtSettings.textDirection": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON> pat<PERSON>", "PE.Views.TextArtSettings.textFromFile": "<PERSON>and", "PE.Views.TextArtSettings.textFromUrl": "Van URL", "PE.Views.TextArtSettings.textGradient": "Kleurovergangpunten", "PE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON> met kleurovergang", "PE.Views.TextArtSettings.textImageTexture": "Afbeelding of textuur", "PE.Views.TextArtSettings.textLinear": "Lineair", "PE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON> vulling", "PE.Views.TextArtSettings.textPatternFill": "Patroon", "PE.Views.TextArtSettings.textPosition": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textRadial": "Ra<PERSON>al", "PE.Views.TextArtSettings.textSelectTexture": "Selecteren", "PE.Views.TextArtSettings.textStretch": "Uitrekken", "PE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textTemplate": "Sjabloon", "PE.Views.TextArtSettings.textTexture": "<PERSON>", "PE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTransform": "Transformeren", "PE.Views.TextArtSettings.tipAddGradientPoint": "Kleurovergangpunt toevoegen", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Kleurovergangpunt verwijderen", "PE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papier", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON><PERSON> stof", "PE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtGranite": "Gran<PERSON>", "PE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON> lijn", "PE.Views.TextArtSettings.txtPapyrus": "Papyrus", "PE.Views.TextArtSettings.txtWood": "<PERSON><PERSON>", "PE.Views.Toolbar.capAddLayout": "Add Layout", "PE.Views.Toolbar.capAddSlide": "<PERSON><PERSON>", "PE.Views.Toolbar.capAddSlideMaster": "Add Slide Master", "PE.Views.Toolbar.capBtnAddComment": "Opmerking toevoegen", "PE.Views.Toolbar.capBtnComment": "Opmerking", "PE.Views.Toolbar.capBtnDateTime": "Datum & Tijd", "PE.Views.Toolbar.capBtnInsHeaderFooter": "Header & Footer", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "symbool", "PE.Views.Toolbar.capBtnSlideNum": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capCloseMaster": "Close Master", "PE.Views.Toolbar.capInsertAudio": "Audio", "PE.Views.Toolbar.capInsertChart": "<PERSON><PERSON>", "PE.Views.Toolbar.capInsertEquation": "Vergelijking", "PE.Views.Toolbar.capInsertHyperlink": "Hyperlink", "PE.Views.Toolbar.capInsertImage": "Afbeelding", "PE.Views.Toolbar.capInsertPlaceholder": "Insert Placeholder", "PE.Views.Toolbar.capInsertShape": "Vorm", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON>", "PE.Views.Toolbar.capInsertText": "Tekstvak", "PE.Views.Toolbar.capInsertTextArt": "Text Art", "PE.Views.Toolbar.capInsertVideo": "Video", "PE.Views.Toolbar.capTabFile": "Bestand", "PE.Views.Toolbar.capTabHome": "Start", "PE.Views.Toolbar.capTabInsert": "Invoegen", "PE.Views.Toolbar.mniCapitalizeWords": "Geef elk woord een hoofdletter ", "PE.Views.Toolbar.mniCustomTable": "Aangepaste tabel invoegen", "PE.Views.Toolbar.mniImageFromFile": "Afbeelding uit bestand", "PE.Views.Toolbar.mniImageFromStorage": "Afbeelding van opslag", "PE.Views.Toolbar.mniImageFromUrl": "Afbeelding van URL", "PE.Views.Toolbar.mniInsertSSE": "Insert Spreadsheet", "PE.Views.Toolbar.mniLowerCase": "kleine letters ", "PE.Views.Toolbar.mniSentenceCase": "Zin lettertype", "PE.Views.Toolbar.mniSlideAdvanced": "Geavanceerde instellingen", "PE.Views.Toolbar.mniSlideStandard": "Standaard (4:3)", "PE.Views.Toolbar.mniSlideWide": "<PERSON><PERSON><PERSON><PERSON> (16:9)", "PE.Views.Toolbar.mniToggleCase": "<PERSON><PERSON>el lettertype", "PE.Views.Toolbar.mniUpperCase": "HOOFDLETTERS", "PE.Views.Toolbar.strMenuNoFill": "<PERSON><PERSON> vulling", "PE.Views.Toolbar.textAlignBottom": "Tekst onderaan uitlijnen", "PE.Views.Toolbar.textAlignCenter": "Tekst centreren", "PE.Views.Toolbar.textAlignJust": "Uitvullen", "PE.Views.Toolbar.textAlignLeft": "Tekst links uitlijnen", "PE.Views.Toolbar.textAlignMiddle": "Tekst centreren", "PE.Views.Toolbar.textAlignRight": "Tekst rechts uitlijnen", "PE.Views.Toolbar.textAlignTop": "Tekst bovenaan uitlijnen", "PE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "PE.Views.Toolbar.textArrangeBack": "<PERSON><PERSON> a<PERSON> sturen", "PE.Views.Toolbar.textArrangeBackward": "<PERSON><PERSON>", "PE.Views.Toolbar.textArrangeForward": "<PERSON><PERSON>", "PE.Views.Toolbar.textArrangeFront": "Naar voorgrond brengen", "PE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "PE.Views.Toolbar.textBlackHeart": "Black heart suit", "PE.Views.Toolbar.textBold": "Vet", "PE.Views.Toolbar.textBullet": "Bullet", "PE.Views.Toolbar.textChart": "Chart", "PE.Views.Toolbar.textColumnsCustom": "Aangepast<PERSON> kolo<PERSON>n", "PE.Views.Toolbar.textColumnsOne": "<PERSON><PERSON> kolom ", "PE.Views.Toolbar.textColumnsThree": "<PERSON><PERSON> ", "PE.Views.Toolbar.textColumnsTwo": "<PERSON>wee kolommen", "PE.Views.Toolbar.textContent": "Content", "PE.Views.Toolbar.textContentVertical": "Content (Vertical)", "PE.Views.Toolbar.textCopyright": "Copyright Sign", "PE.Views.Toolbar.textDegree": "Degree Sign", "PE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "PE.Views.Toolbar.textDivision": "Division Sign", "PE.Views.Toolbar.textDollar": "Dollar Sign", "PE.Views.Toolbar.textEuro": "Euro Sign", "PE.Views.Toolbar.textFooters": "Footers", "PE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "PE.Views.Toolbar.textInfinity": "Infinity", "PE.Views.Toolbar.textItalic": "Cursief", "PE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "PE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "PE.Views.Toolbar.textLineSpaceOptions": "Line spacing options", "PE.Views.Toolbar.textListSettings": "Lijst instellingen", "PE.Views.Toolbar.textMoreSymbols": "More symbols", "PE.Views.Toolbar.textNotEqualTo": "Not Equal To", "PE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "PE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "PE.Views.Toolbar.textPicture": "Picture", "PE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "PE.Views.Toolbar.textRecentlyUsed": "Recently used", "PE.Views.Toolbar.textRegistered": "Registered Sign", "PE.Views.Toolbar.textSection": "Section Sign", "PE.Views.Toolbar.textShapeAlignBottom": "<PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignCenter": "Midden uitlijnen", "PE.Views.Toolbar.textShapeAlignLeft": "<PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignMiddle": "Midden uitlijnen", "PE.Views.Toolbar.textShapeAlignRight": "Rechts uitlijnen", "PE.Views.Toolbar.textShapeAlignTop": "<PERSON><PERSON>", "PE.Views.Toolbar.textShapesCombine": "Combine", "PE.Views.Toolbar.textShapesFragment": "Fragment", "PE.Views.Toolbar.textShapesIntersect": "Intersect", "PE.Views.Toolbar.textShapesSubstract": "Subtract", "PE.Views.Toolbar.textShapesUnion": "Union", "PE.Views.Toolbar.textShowBegin": "Vanaf begin tonen", "PE.Views.Toolbar.textShowCurrent": "Vanaf huidige dia tonen", "PE.Views.Toolbar.textShowPresenterView": "Presentatieweergave tonen", "PE.Views.Toolbar.textShowSettings": "Instellingen tonen", "PE.Views.Toolbar.textSmartArt": "SmartArt", "PE.Views.Toolbar.textSmile": "White Smiling Face", "PE.Views.Toolbar.textSquareRoot": "Square Root", "PE.Views.Toolbar.textStrikeout": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textSubscript": "Subscript", "PE.Views.Toolbar.textSuperscript": "Superscript", "PE.Views.Toolbar.textTabAnimation": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabCollaboration": "Samenwerking", "PE.Views.Toolbar.textTabDesign": "Design", "PE.Views.Toolbar.textTabDraw": "Draw", "PE.Views.Toolbar.textTabFile": "Bestand", "PE.Views.Toolbar.textTabHome": "Start", "PE.Views.Toolbar.textTabInsert": "Invoegen", "PE.Views.Toolbar.textTable": "Table", "PE.Views.Toolbar.textTabProtect": "Beveiliging", "PE.Views.Toolbar.textTabTransitions": "Overgangen", "PE.Views.Toolbar.textTabView": "Weergave", "PE.Views.Toolbar.textText": "Text", "PE.Views.Toolbar.textTextVertical": "Text (Vertical)", "PE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "PE.Views.Toolbar.textTitle": "Title", "PE.Views.Toolbar.textTitleError": "Fout", "PE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "PE.Views.Toolbar.textUnderline": "Onderstrepen", "PE.Views.Toolbar.textYen": "Yen Sign", "PE.Views.Toolbar.tipAddLayout": "Add layout", "PE.Views.Toolbar.tipAddSlide": "<PERSON><PERSON>", "PE.Views.Toolbar.tipAddSlideMaster": "Add slide master", "PE.Views.Toolbar.tipBack": "Terug", "PE.Views.Toolbar.tipChangeCase": "<PERSON><PERSON>", "PE.Views.Toolbar.tipChangeChart": "Grafiektype wijzigen", "PE.Views.Toolbar.tipChangeSlide": "Dia-indeling wijzigen", "PE.Views.Toolbar.tipClearStyle": "Sti<PERSON>l wissen", "PE.Views.Toolbar.tipCloseMaster": "Close Master", "PE.Views.Toolbar.tipColorSchemas": "Kleurenschema wijzigen", "PE.Views.Toolbar.tipColumns": "Kolommen invoegen", "PE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipCopyStyle": "Stijl kopiëren", "PE.Views.Toolbar.tipCut": "Cut", "PE.Views.Toolbar.tipDateTime": "Invoegen huidige datum en tijd", "PE.Views.Toolbar.tipDecFont": "Tekengrootte verminderen", "PE.Views.Toolbar.tipDecPrLeft": "Inspringing verkleinen", "PE.Views.Toolbar.tipEditHeaderFooter": "Edit header or footer", "PE.Views.Toolbar.tipFontColor": "Tekstkleur", "PE.Views.Toolbar.tipFontName": "Lettertype", "PE.Views.Toolbar.tipFontSize": "Tekengrootte", "PE.Views.Toolbar.tipHAligh": "Horizontale uitlijning", "PE.Views.Toolbar.tipHighlightColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipIncFont": "Tekengrootte vergroten", "PE.Views.Toolbar.tipIncPrLeft": "Inspringing vergroten", "PE.Views.Toolbar.tipInsertAudio": "Voeg geluid toe", "PE.Views.Toolbar.tipInsertChart": "Grafiek invo<PERSON>n", "PE.Views.Toolbar.tipInsertChartPlaceholder": "Insert chart placeholder", "PE.Views.Toolbar.tipInsertContentPlaceholder": "Insert content placeholder", "PE.Views.Toolbar.tipInsertContentVerticalPlaceholder": "Insert content (vertical) placeholder", "PE.Views.Toolbar.tipInsertEquation": "Vergelijking invoegen", "PE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "PE.Views.Toolbar.tipInsertHyperlink": "Hyperlink toevoegen", "PE.Views.Toolbar.tipInsertImage": "Afbeelding invoegen", "PE.Views.Toolbar.tipInsertPicturePlaceholder": "Insert picture placeholder", "PE.Views.Toolbar.tipInsertShape": "AutoVorm invoegen", "PE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "PE.Views.Toolbar.tipInsertSmartArtPlaceholder": "Insert SmartArt placeholder", "PE.Views.Toolbar.tipInsertSymbol": "Symbool toevoegen", "PE.Views.Toolbar.tipInsertTable": "Tabel invoegen", "PE.Views.Toolbar.tipInsertTablePlaceholder": "Insert table placeholder", "PE.Views.Toolbar.tipInsertText": "Tekstvak invoegen", "PE.Views.Toolbar.tipInsertTextArt": "Tekst Art Invoegen", "PE.Views.Toolbar.tipInsertTextPlaceholder": "Insert text placeholder", "PE.Views.Toolbar.tipInsertTextVerticalPlaceholder": "Insert text (vertical) placeholder", "PE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "PE.Views.Toolbar.tipInsertVideo": "Video toevoegen", "PE.Views.Toolbar.tipLineSpace": "Regelafstand", "PE.Views.Toolbar.tipMarkers": "Opsommingstekens", "PE.Views.Toolbar.tipMarkersArrow": "Pijlopsommingstekens", "PE.Views.Toolbar.tipMarkersCheckmark": "Vinkopsommingstekens", "PE.Views.Toolbar.tipMarkersDash": "Streepopsommingstekens", "PE.Views.Toolbar.tipMarkersFRhombus": "Opgevulde ruitopsommingstekens", "PE.Views.Toolbar.tipMarkersFRound": "Opgevulde ronde opsommingstekens", "PE.Views.Toolbar.tipMarkersFSquare": "Opgevulde vierkante opsommingstekens", "PE.Views.Toolbar.tipMarkersHRound": "Hollow round bullets", "PE.Views.Toolbar.tipMarkersStar": "Star bullets", "PE.Views.Toolbar.tipNone": "None", "PE.Views.Toolbar.tipNumbers": "Nummering", "PE.Views.Toolbar.tipPaste": "Plakken", "PE.Views.Toolbar.tipPreview": "Diavoorstelling starten", "PE.Views.Toolbar.tipPrint": "Afdrukken", "PE.Views.Toolbar.tipPrintQuick": "Quick print", "PE.Views.Toolbar.tipRedo": "Opnieuw", "PE.Views.Toolbar.tipReplace": "Replace", "PE.Views.Toolbar.tipSave": "Opsla<PERSON>", "PE.Views.Toolbar.tipSaveCoauth": "Sla uw wijzigingen op zodat andere gebruikers die kunnen zien.", "PE.Views.Toolbar.tipSelectAll": "Select all", "PE.Views.Toolbar.tipShapeAlign": "<PERSON><PERSON>", "PE.Views.Toolbar.tipShapeArrange": "<PERSON>orm ordenen", "PE.Views.Toolbar.tipShapesMerge": "Merge shapes", "PE.Views.Toolbar.tipSlideNum": "Voeg dia nummer toe", "PE.Views.Toolbar.tipSlideSize": "Diagrootte selecteren", "PE.Views.Toolbar.tipSlideTheme": "Diathema", "PE.Views.Toolbar.tipUndo": "Ongedaan maken", "PE.Views.Toolbar.tipVAligh": "Verticaal uitlijnen", "PE.Views.Toolbar.tipViewSettings": "Weergave-instellingen", "PE.Views.Toolbar.txtColors": "Colors", "PE.Views.Toolbar.txtDistribHor": "Horizontaal verdelen", "PE.Views.Toolbar.txtDistribVert": "Verticaal verdelen", "PE.Views.Toolbar.txtDuplicateSlide": "<PERSON>a dup<PERSON>", "PE.Views.Toolbar.txtGroup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtObjectsAlign": "Geslecteerde objecten uitlijnen", "PE.Views.Toolbar.txtSlideAlign": "Uitlijnen op dia", "PE.Views.Toolbar.txtSlideSize": "Slide size", "PE.Views.Toolbar.txtUngroup": "<PERSON><PERSON><PERSON><PERSON> ophe<PERSON>n", "PE.Views.Transitions.strDelay": "Vertragen", "PE.Views.Transitions.strDuration": "<PERSON><PERSON>", "PE.Views.Transitions.strStartOnClick": "<PERSON><PERSON><PERSON> klik starten", "PE.Views.Transitions.textBlack": "<PERSON> zwart", "PE.Views.Transitions.textBottom": "Onderkant", "PE.Views.Transitions.textBottomLeft": "Linksonder", "PE.Views.Transitions.textBottomRight": "Rechtsonder", "PE.Views.Transitions.textClock": "Klok", "PE.Views.Transitions.textClockwise": "Rechtsom", "PE.Views.Transitions.textCounterclockwise": "Linksom", "PE.Views.Transitions.textCover": "Bedekken", "PE.Views.Transitions.textFade": "Vervagen", "PE.Views.Transitions.textHorizontalIn": "<PERSON><PERSON><PERSON> naar binnen", "PE.Views.Transitions.textHorizontalOut": "Horizontaal naar buiten", "PE.Views.Transitions.textLeft": "Links", "PE.Views.Transitions.textMorph": "Morph", "PE.Views.Transitions.textMorphLetters": "Letters", "PE.Views.Transitions.textMorphObjects": "Objects", "PE.Views.Transitions.textMorphWord": "Words", "PE.Views.Transitions.textNone": "<PERSON><PERSON>", "PE.Views.Transitions.textPush": "<PERSON><PERSON>", "PE.Views.Transitions.textRandom": "Random", "PE.Views.Transitions.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textSmoothly": "Vloeiend", "PE.Views.Transitions.textSplit": "<PERSON><PERSON>", "PE.Views.Transitions.textTop": "<PERSON><PERSON>", "PE.Views.Transitions.textTopLeft": "Linksboven", "PE.Views.Transitions.textTopRight": "Rechtsboven", "PE.Views.Transitions.textUnCover": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textVerticalIn": "Verticaal naar binnen", "PE.Views.Transitions.textVerticalOut": "Verticaal naar buiten", "PE.Views.Transitions.textWedge": "Wig", "PE.Views.Transitions.textWipe": "Wissen", "PE.Views.Transitions.textZoom": "Zoomen", "PE.Views.Transitions.textZoomIn": "Inzoomen", "PE.Views.Transitions.textZoomOut": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoomRotate": "Zoomen en draaien", "PE.Views.Transitions.txtApplyToAll": "Toepassen op alle dia's", "PE.Views.Transitions.txtParameters": "Parameters", "PE.Views.Transitions.txtPreview": "Voorbeeld", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.capBtnHand": "Hand", "PE.Views.ViewTab.capBtnSelect": "Select", "PE.Views.ViewTab.textAddHGuides": "Add horizontal guide", "PE.Views.ViewTab.textAddVGuides": "Add vertical guide", "PE.Views.ViewTab.textAlwaysShowToolbar": "Werkbalk altijd weergeven", "PE.Views.ViewTab.textClearGuides": "Clear guides", "PE.Views.ViewTab.textCm": "cm", "PE.Views.ViewTab.textCustom": "Custom", "PE.Views.ViewTab.textFill": "Vulling", "PE.Views.ViewTab.textFitToSlide": "<PERSON><PERSON><PERSON><PERSON> aan dia", "PE.Views.ViewTab.textFitToWidth": "<PERSON>an <PERSON>te aan<PERSON>en", "PE.Views.ViewTab.textGridlines": "Gridlines", "PE.Views.ViewTab.textGuides": "Guides", "PE.Views.ViewTab.textInterfaceTheme": "Interface Theme", "PE.Views.ViewTab.textLeftMenu": "Left Panel", "PE.Views.ViewTab.textLine": "<PERSON><PERSON>", "PE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "PE.Views.ViewTab.textNormal": "Normal", "PE.Views.ViewTab.textNotes": "Notes", "PE.Views.ViewTab.textRightMenu": "Right Panel", "PE.Views.ViewTab.textRulers": "Rulers", "PE.Views.ViewTab.textShowGridlines": "Show Gridlines", "PE.Views.ViewTab.textShowGuides": "Show guides", "PE.Views.ViewTab.textSlideMaster": "Slide Master", "PE.Views.ViewTab.textSmartGuides": "Smart guides", "PE.Views.ViewTab.textSnapObjects": "Snap object to grid", "PE.Views.ViewTab.textStatusBar": "Status Bar", "PE.Views.ViewTab.textTabStyle": "Tab style", "PE.Views.ViewTab.textZoom": "Zoom", "PE.Views.ViewTab.tipFitToSlide": "Fit to slide", "PE.Views.ViewTab.tipFitToWidth": "Fit to width", "PE.Views.ViewTab.tipGridlines": "Show gridlines", "PE.Views.ViewTab.tipGuides": "Show guides", "PE.Views.ViewTab.tipHandTool": "Hand tool", "PE.Views.ViewTab.tipInterfaceTheme": "Interface theme", "PE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "PE.Views.ViewTab.tipNormal": "Normal", "PE.Views.ViewTab.tipSelectTool": "Select tool", "PE.Views.ViewTab.tipSlideMaster": "Slide master", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}