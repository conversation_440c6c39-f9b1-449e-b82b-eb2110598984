{"Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "Aizvērt", "Common.Controllers.ExternalDiagramEditor.warningText": "Obje<PERSON>s ir at<PERSON>, jo to rediģē cits lietotājs.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "Aizvērt", "Common.Controllers.ExternalOleEditor.warningText": "Obje<PERSON>s ir at<PERSON>, jo to rediģē cits lietotājs.", "Common.Controllers.ExternalOleEditor.warningTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.History.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "Apgabals", "Common.define.chartData.textAreaStacked": "Sagrupēts apgabals", "Common.define.chartData.textAreaStackedPer": "100% grupēts apgabals", "Common.define.chartData.textBar": "<PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Klasterveida kolonna", "Common.define.chartData.textBarNormal3d": "3-<PERSON> klasterveida kolonna", "Common.define.chartData.textBarNormal3dPerspective": "3-<PERSON> kolonna", "Common.define.chartData.textBarStacked": "Sagrupēta kolonna", "Common.define.chartData.textBarStacked3d": "3-<PERSON> klasterveida kolonna", "Common.define.chartData.textBarStackedPer": "100% grupēta kolonna", "Common.define.chartData.textBarStackedPer3d": "3-D 100% grupēta kolonna", "Common.define.chartData.textCharts": "Diagram<PERSON>", "Common.define.chartData.textColumn": "Kolonna", "Common.define.chartData.textCombo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON><PERSON> gr<PERSON> <PERSON> sag<PERSON><PERSON><PERSON>i", "Common.define.chartData.textComboBarLine": "Klasterveida kolonna – līnija", "Common.define.chartData.textComboBarLineSecondary": "Klasterveida kolonna – līnija uz sekundārās ass", "Common.define.chartData.textComboCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textDoughnut": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Klasterveida j<PERSON>la", "Common.define.chartData.textHBarNormal3d": "3-<PERSON> klasterveida josla", "Common.define.chartData.textHBarStacked": "Sagrupēta grēda", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> klasterveida josla", "Common.define.chartData.textHBarStackedPer": "100% grupēta josla", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% grupēta josla", "Common.define.chartData.textLine": "Lī<PERSON><PERSON>", "Common.define.chartData.textLine3d": "3-D līnija", "Common.define.chartData.textLineMarker": "Lī<PERSON><PERSON> ar marķieriem", "Common.define.chartData.textLineStacked": "Sagrupēta līnija", "Common.define.chartData.textLineStackedMarker": "Sagrupēta līnija ar marķieriem", "Common.define.chartData.textLineStackedPer": "100% grupēta līnija", "Common.define.chartData.textLineStackedPerMarker": "100% grupēta līnija ar marķieriem", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON><PERSON> diagramma", "Common.define.chartData.textPie3d": "3-<PERSON> sektors", "Common.define.chartData.textPoint": "Punkts (XY)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "Izkliedēt", "Common.define.chartData.textScatterLine": "Izkliedēt ar taisnām līnijām", "Common.define.chartData.textScatterLineMarker": "Izkliedēt ar taisnām līnijām un marķieriem", "Common.define.chartData.textScatterSmooth": "Izkliedēt ar gludām līni<PERSON>m", "Common.define.chartData.textScatterSmoothMarker": "Izkliedēt ar gludām līnijām un marķieriem", "Common.define.chartData.textStock": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textSurface": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textAcross": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textAppear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textArcDown": "Arh. uz leju", "Common.define.effectData.textArcLeft": "Arh. pa kreisi", "Common.define.effectData.textArcRight": "Arh. pa labi", "Common.define.effectData.textArcs": "Arh.", "Common.define.effectData.textArcUp": "Arh. uz augšu", "Common.define.effectData.textBasic": "Pamata", "Common.define.effectData.textBasicSwivel": "<PERSON><PERSON> grie<PERSON>n<PERSON>s ap asi", "Common.define.effectData.textBasicZoom": "<PERSON><PERSON> t<PERSON>", "Common.define.effectData.textBean": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBlinds": "Žalūzijas", "Common.define.effectData.textBlink": "Mirkšķināt", "Common.define.effectData.textBoldFlash": "Treknraksta Flash", "Common.define.effectData.textBoldReveal": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBoomerang": "Bumerangs", "Common.define.effectData.textBounce": "Atsist", "Common.define.effectData.textBounceLeft": "Atsist pa kreisi", "Common.define.effectData.textBounceRight": "Atsist pa labi", "Common.define.effectData.textBox": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBrushColor": "<PERSON><PERSON>", "Common.define.effectData.textCenterRevolve": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCheckerboard": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCircle": "Aplis", "Common.define.effectData.textCollapse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textColorPulse": "<PERSON><PERSON><PERSON><PERSON> impulss", "Common.define.effectData.textComplementaryColor": "<PERSON><PERSON><PERSON><PERSON> kr<PERSON><PERSON>", "Common.define.effectData.textComplementaryColor2": "2. p<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCompress": "Komprese", "Common.define.effectData.textContrast": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textContrastingColor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Common.define.effectData.textCredits": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCrescentMoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCurveDown": "Līkne uz leju", "Common.define.effectData.textCurvedSquare": "Izliekts kvadrāts", "Common.define.effectData.textCurvedX": "Izliekta X", "Common.define.effectData.textCurvyLeft": "Izliekts pa kreisi", "Common.define.effectData.textCurvyRight": "Izliekts pa labi", "Common.define.effectData.textCurvyStar": "Izliekta zvaigzne", "Common.define.effectData.textCustomPath": "<PERSON><PERSON>ā<PERSON><PERSON> ce<PERSON>", "Common.define.effectData.textCuverUp": "Līkne uz augšu", "Common.define.effectData.textDarken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textDecayingWave": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "Common.define.effectData.textDesaturate": "Noņemt piekrāsu", "Common.define.effectData.textDiagonalDownRight": "Diagonāli uz leju pa labi", "Common.define.effectData.textDiagonalUpRight": "Diagonāli uz augšu pa labi", "Common.define.effectData.textDiamond": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDisappear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textDissolveIn": "Izšķīšana", "Common.define.effectData.textDissolveOut": "Izšķīšana uz āru", "Common.define.effectData.textDown": "Uz leju", "Common.define.effectData.textDrop": "Nomest", "Common.define.effectData.textEmphasis": "Uzsvara efekti", "Common.define.effectData.textEntrance": "Uznākšanas efekti", "Common.define.effectData.textEqualTriangle": "<PERSON><PERSON><PERSON><PERSON> trīsstū<PERSON>", "Common.define.effectData.textExciting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textExit": "Iziet no efektiem", "Common.define.effectData.textExpand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFigureFour": "8. <PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFillColor": "<PERSON>z<PERSON><PERSON><PERSON>", "Common.define.effectData.textFlip": "Uzsist", "Common.define.effectData.textFloat": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFloatDown": "Ieplūst uz leju", "Common.define.effectData.textFloatIn": "Izpeldēt", "Common.define.effectData.textFloatOut": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFloatUp": "Ieplūst uz augšu", "Common.define.effectData.textFlyIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFlyOut": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFontColor": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFootball": "Futbols", "Common.define.effectData.textFromBottom": "No apakšas", "Common.define.effectData.textFromBottomLeft": "No apakšas pa kreisi", "Common.define.effectData.textFromBottomRight": "No apakšas pa labi", "Common.define.effectData.textFromLeft": "No kreisās puses", "Common.define.effectData.textFromRight": "No labās puses", "Common.define.effectData.textFromTop": "No augšas", "Common.define.effectData.textFromTopLeft": "No augšas pa kreisi", "Common.define.effectData.textFromTopRight": "No augšas pa labi", "Common.define.effectData.textFunnel": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textGrowShrink": "Augt/sarukt", "Common.define.effectData.textGrowTurn": "Augt un pagriezt", "Common.define.effectData.textGrowWithColor": "<PERSON><PERSON> ar k<PERSON><PERSON><PERSON>", "Common.define.effectData.textHeart": "Sirds", "Common.define.effectData.textHeartbeat": "Sirdspuksti", "Common.define.effectData.textHexagon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textHorizontalFigure": "Horizon<PERSON><PERSON><PERSON><PERSON> 8. fig<PERSON><PERSON>", "Common.define.effectData.textHorizontalIn": "<PERSON>", "Common.define.effectData.textHorizontalOut": "<PERSON>āli ārā", "Common.define.effectData.textIn": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textInFromScreenCenter": "Iekšā no ekrāna centra", "Common.define.effectData.textInSlightly": "Nedaudz uz iekšu", "Common.define.effectData.textInToScreenBottom": "<PERSON><PERSON><PERSON><PERSON> ekrāna apak<PERSON>ā", "Common.define.effectData.textInvertedSquare": "Apgriezts kvadrāts", "Common.define.effectData.textInvertedTriangle": "Apgriezts trīsstūris", "Common.define.effectData.textLeft": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textLeftDown": " Pa kreisi uz leju", "Common.define.effectData.textLeftUp": " Pa kreisi uz augšu", "Common.define.effectData.textLighten": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textLineColor": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textLines": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textLinesCurves": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textLoopDeLoop": "Cilpa aiz cilpas", "Common.define.effectData.textLoops": "Cilpas", "Common.define.effectData.textModerate": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textNeutron": "Neitrons", "Common.define.effectData.textObjectCenter": "Objektu centrs", "Common.define.effectData.textObjectColor": "Objekta kr<PERSON>", "Common.define.effectData.textOctagon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textOutFromScreenBottom": "No ekrāna a<PERSON>", "Common.define.effectData.textOutSlightly": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textOutToScreenCenter": "Ārā no ekrāna centra", "Common.define.effectData.textParallelogram": "Paralelogramma", "Common.define.effectData.textPath": "Kustības ceļi", "Common.define.effectData.textPathCurve": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textPathLine": "Lī<PERSON><PERSON>", "Common.define.effectData.textPathScribble": "Skricelējums", "Common.define.effectData.textPeanut": "Zemesrieksts", "Common.define.effectData.textPeekIn": "Ieskatīties", "Common.define.effectData.textPeekOut": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textPentagon": "Pentagons", "Common.define.effectData.textPinwheel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textPlus": "Plus", "Common.define.effectData.textPointStar": "Punktu zvaigzne", "Common.define.effectData.textPointStar4": "4 punktu zvaigzne", "Common.define.effectData.textPointStar5": "5 punktu zvaigzne", "Common.define.effectData.textPointStar6": "6 punktu zvaigzne", "Common.define.effectData.textPointStar8": "8 punktu zvaigzne", "Common.define.effectData.textPulse": "Pulss", "Common.define.effectData.textRandomBars": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "Common.define.effectData.textRight": "Labais", "Common.define.effectData.textRightDown": " Pa labi uz leju", "Common.define.effectData.textRightTriangle": "<PERSON><PERSON>", "Common.define.effectData.textRightUp": " Pa labi uz augšu", "Common.define.effectData.textRiseUp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSCurve1": "S līkne 1", "Common.define.effectData.textSCurve2": "S līkne 2", "Common.define.effectData.textShape": "Forma", "Common.define.effectData.textShapes": "Formas", "Common.define.effectData.textShimmer": "Mirgojoš<PERSON> te<PERSON>", "Common.define.effectData.textShrinkTurn": "Samazināt un pagriezt", "Common.define.effectData.textSineWave": "Sinusa līkne", "Common.define.effectData.textSinkDown": "Nogrimšana", "Common.define.effectData.textSlideCenter": "Slaidu centrs", "Common.define.effectData.textSpecial": "Īpašie", "Common.define.effectData.textSpin": "Griezt", "Common.define.effectData.textSpinner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSpiralIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSpiralLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON> pa k<PERSON>i", "Common.define.effectData.textSpiralOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSpiralRight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSplit": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSpoke1": "1 spieķis", "Common.define.effectData.textSpoke2": "2 spieķi", "Common.define.effectData.textSpoke3": "3 spieķi", "Common.define.effectData.textSpoke4": "4 spieķi", "Common.define.effectData.textSpoke8": "8 spieķi", "Common.define.effectData.textSpring": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSquare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textStairsDown": "Kāpnes uz leju", "Common.define.effectData.textStretch": "Stiept", "Common.define.effectData.textStrips": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSubtle": "Smalks", "Common.define.effectData.textSwivel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ap asi", "Common.define.effectData.textSwoosh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textTeardrop": "<PERSON><PERSON>", "Common.define.effectData.textTeeter": "Šūpoles", "Common.define.effectData.textToBottom": "<PERSON>z a<PERSON>šu", "Common.define.effectData.textToBottomLeft": "<PERSON>z apa<PERSON>šu pa kreisi", "Common.define.effectData.textToBottomRight": "Uz apakšu pa labi", "Common.define.effectData.textToLeft": "<PERSON> kreisi", "Common.define.effectData.textToRight": "Pa labi", "Common.define.effectData.textToTop": "<PERSON><PERSON> augšu", "Common.define.effectData.textToTopLeft": "<PERSON><PERSON><PERSON> pa labi", "Common.define.effectData.textToTopRight": "<PERSON><PERSON><PERSON> pa kreisi", "Common.define.effectData.textTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textTrapezoid": "Trapecveida", "Common.define.effectData.textTurnDown": "Pagrieziens uz leju", "Common.define.effectData.textTurnDownRight": "Pagrieziens pa labi", "Common.define.effectData.textTurns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textTurnUp": "Pagrieziens uz augšu", "Common.define.effectData.textTurnUpRight": "Pagrieziens uz augšu pa labi", "Common.define.effectData.textUnderline": "Pasvītrot", "Common.define.effectData.textUp": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textVertical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textVerticalFigure": "Vertikāla 8. fig<PERSON><PERSON>", "Common.define.effectData.textVerticalIn": "<PERSON> vertikāli iek<PERSON>ā", "Common.define.effectData.textVerticalOut": "Pa vertikāli ārā", "Common.define.effectData.textWave": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textWedge": "Ķīlis", "Common.define.effectData.textWheel": "<PERSON><PERSON>", "Common.define.effectData.textWhip": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textWipe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textZigzag": "Zigzags", "Common.define.effectData.textZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.gridlineData.txtCm": "cm", "Common.define.gridlineData.txtPt": "pt", "Common.define.smartArt.textAccentedPicture": "Akcentēts attēls", "Common.define.smartArt.textAccentProcess": "Akcentu process", "Common.define.smartArt.textAlternatingFlow": "<PERSON>ī<PERSON> pl<PERSON>", "Common.define.smartArt.textAlternatingHexagons": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textAlternatingPictureBlocks": "<PERSON><PERSON><PERSON> at<PERSON> bloki", "Common.define.smartArt.textAlternatingPictureCircles": "<PERSON><PERSON><PERSON> a<PERSON>", "Common.define.smartArt.textArchitectureLayout": "Arhitektūras izkārtojums", "Common.define.smartArt.textArrowRibbon": "<PERSON><PERSON><PERSON><PERSON><PERSON> ente", "Common.define.smartArt.textAscendingPictureAccentProcess": "<PERSON><PERSON><PERSON><PERSON> se<PERSON> attē<PERSON> a<PERSON> process", "Common.define.smartArt.textBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Pamata izliekts process", "Common.define.smartArt.textBasicBlockList": "Pamata bloku saraksts", "Common.define.smartArt.textBasicChevronProcess": "Pamata skujiņu process", "Common.define.smartArt.textBasicCycle": "Pamata cikls", "Common.define.smartArt.textBasicMatrix": "Pamata matrica", "Common.define.smartArt.textBasicPie": "Pamata pīr<PERSON>gs", "Common.define.smartArt.textBasicProcess": "Pamata process", "Common.define.smartArt.textBasicPyramid": "<PERSON><PERSON> pira<PERSON>da", "Common.define.smartArt.textBasicRadial": "<PERSON>ata radi<PERSON>ls", "Common.define.smartArt.textBasicTarget": "Pamata mērķis", "Common.define.smartArt.textBasicTimeline": "<PERSON><PERSON> laika skala", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Izliekts attēlu akcentu saraksts", "Common.define.smartArt.textBendingPictureBlocks": "Izliekti attēlu bloki", "Common.define.smartArt.textBendingPictureCaption": "Izliekti attēlu paraksti", "Common.define.smartArt.textBendingPictureCaptionList": "Izliekts attēlu parakstu saraksts", "Common.define.smartArt.textBendingPictureSemiTranparentText": "<PERSON><PERSON><PERSON><PERSON><PERSON> caurspīdīga teksta izliekšana", "Common.define.smartArt.textBlockCycle": "Bloka cikls", "Common.define.smartArt.textBubblePictureList": "Burbuļ<PERSON> attēlu sarak<PERSON>", "Common.define.smartArt.textCaptionedPictures": "Att<PERSON><PERSON> ar parastiem", "Common.define.smartArt.textChevronAccentProcess": "Skujiņas akcentu process", "Common.define.smartArt.textChevronList": "Skujiņ<PERSON> sa<PERSON>", "Common.define.smartArt.textCircleAccentTimeline": "A<PERSON>ļ<PERSON> izcēlumu laika skala", "Common.define.smartArt.textCircleArrowProcess": "Apļa bultiņas process", "Common.define.smartArt.textCirclePictureHierarchy": "<PERSON><PERSON><PERSON><PERSON> att<PERSON> hierarhija", "Common.define.smartArt.textCircleProcess": "Apļa process", "Common.define.smartArt.textCircleRelationship": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textCircularBendingProcess": "Riņķveida izliekts process", "Common.define.smartArt.textCircularPictureCallout": "Riņķveida attēlu remarka", "Common.define.smartArt.textClosedChevronProcess": "Slēgts s<PERSON> process", "Common.define.smartArt.textContinuousArrowProcess": "Nepārtraukts bultiņas process", "Common.define.smartArt.textContinuousBlockProcess": "Nepārtraukts bloķēšanas process", "Common.define.smartArt.textContinuousCycle": "Nepārtraukts cikls", "Common.define.smartArt.textContinuousPictureList": "Nepārtraukts attēlu saraksts", "Common.define.smartArt.textConvergingArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textConvergingRadial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textConvergingText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "Common.define.smartArt.textCounterbalanceArrows": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Common.define.smartArt.textCycle": "Cikls", "Common.define.smartArt.textCycleMatrix": "Cikla matrica", "Common.define.smartArt.textDescendingBlockList": "<PERSON><PERSON><PERSON><PERSON><PERSON> bloku sarak<PERSON>", "Common.define.smartArt.textDescendingProcess": "Dilstošs process", "Common.define.smartArt.textDetailedProcess": "Detalizēts process", "Common.define.smartArt.textDivergingArrows": "Novirzī<PERSON> bultiņas", "Common.define.smartArt.textDivergingRadial": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "Common.define.smartArt.textEquation": "Vienādojums", "Common.define.smartArt.textFramedTextPicture": "Ierāmēta teksta attēls", "Common.define.smartArt.textFunnel": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textGear": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textGridMatrix": "Režģa matrica", "Common.define.smartArt.textGroupedList": "Grupēts saraksts", "Common.define.smartArt.textHalfCircleOrganizationChart": "Pusapļa organizācijas diagramma", "Common.define.smartArt.textHexagonCluster": "Sešst<PERSON><PERSON> kopa", "Common.define.smartArt.textHexagonRadial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textHierarchyList": "<PERSON>erar<PERSON><PERSON> sarak<PERSON>", "Common.define.smartArt.textHorizontalBulletList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textHorizontalHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON> hierarhija", "Common.define.smartArt.textHorizontalLabeledHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON> iezīmētā hierarhija", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON> hierarhija", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontālā organizācijas diagramma", "Common.define.smartArt.textHorizontalPictureList": "Horizont<PERSON><PERSON> at<PERSON> sarak<PERSON>", "Common.define.smartArt.textIncreasingArrowProcess": "Pieau<PERSON><PERSON><PERSON> b<PERSON> process", "Common.define.smartArt.textIncreasingCircleProcess": "Pieaugošs a<PERSON>ļ<PERSON> process", "Common.define.smartArt.textInterconnectedBlockProcess": "Savstarpēji savienots bloķēšanas process", "Common.define.smartArt.textInterconnectedRings": "Savstarpēji sa<PERSON>noti g<PERSON>i", "Common.define.smartArt.textInvertedPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Common.define.smartArt.textLabeledHierarchy": "Iezīmētā hierarhija", "Common.define.smartArt.textLinearVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textLinedList": "Izklāts saraksts", "Common.define.smartArt.textList": "Saraksts", "Common.define.smartArt.textMatrix": "Matrica", "Common.define.smartArt.textMultidirectionalCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cikls", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Vārda un nosaukuma organizācijas diagramma", "Common.define.smartArt.textNestedTarget": "Ligzdots mērķis", "Common.define.smartArt.textNondirectionalCycle": "Cikls bez virzieniem", "Common.define.smartArt.textOpposingArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textOpposingIdeas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textOrganizationChart": "Organizācijas diagramma", "Common.define.smartArt.textOther": "Citi", "Common.define.smartArt.textPhasedProcess": "Pakāpenisks process", "Common.define.smartArt.textPicture": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureAccentBlocks": "Attēlu akcentu bloki", "Common.define.smartArt.textPictureAccentList": "Attēlu akcent<PERSON> sa<PERSON>", "Common.define.smartArt.textPictureAccentProcess": "Attēla akcentēšanas process", "Common.define.smartArt.textPictureCaptionList": "Attēlu parakstu sarak<PERSON>", "Common.define.smartArt.textPictureFrame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureGrid": "Attēlu režģis", "Common.define.smartArt.textPictureLineup": "Attēlu izkārtojums līnijā", "Common.define.smartArt.textPictureOrganizationChart": "Attēla organizā<PERSON> diagram<PERSON>", "Common.define.smartArt.textPictureStrips": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPieProcess": "Sektoru process", "Common.define.smartArt.textPlusAndMinus": "Plus un mīnus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textProcessList": "Procesu sarak<PERSON>", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Common.define.smartArt.textRadialCluster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textRadialCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON> cikls", "Common.define.smartArt.textRadialList": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Common.define.smartArt.textRadialPictureList": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON> sarak<PERSON>", "Common.define.smartArt.textRadialVenn": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textRandomToResultProcess": "Nejaušs process līdz rezultātam", "Common.define.smartArt.textRelationship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textRepeatingBendingProcess": "Atkārtots izliekts process", "Common.define.smartArt.textReverseList": "Apgriezt sarakstu", "Common.define.smartArt.textSegmentedCycle": "Segmentēts cikls", "Common.define.smartArt.textSegmentedProcess": "Segmentēts process", "Common.define.smartArt.textSegmentedPyramid": "Se<PERSON><PERSON><PERSON><PERSON> p<PERSON>da", "Common.define.smartArt.textSnapshotPictureList": "Momentuzņēmuma attēlu saraksts", "Common.define.smartArt.textSpiralPicture": "Spirā<PERSON><PERSON> attēls", "Common.define.smartArt.textSquareAccentList": "Kvadrātveida akcentu saraksts", "Common.define.smartArt.textStackedList": "Sagrupēts saraksts", "Common.define.smartArt.textStackedVenn": "Sagrupēta V<PERSON>", "Common.define.smartArt.textStaggeredProcess": "Zigzagveida process", "Common.define.smartArt.textStepDownProcess": "Atkāpšanās process", "Common.define.smartArt.textStepUpProcess": "Palielinā<PERSON>nas process", "Common.define.smartArt.textSubStepProcess": "Pakārtoto darbību process", "Common.define.smartArt.textTabbedArc": "Ciļņu loks", "Common.define.smartArt.textTableHierarchy": "Tabulas hierarhija", "Common.define.smartArt.textTableList": "Tabulas saraksts", "Common.define.smartArt.textTabList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textTargetList": "Mērķa saraksts", "Common.define.smartArt.textTextCycle": "Teksta cikls", "Common.define.smartArt.textThemePictureAccent": "<PERSON><PERSON><PERSON> at<PERSON>", "Common.define.smartArt.textThemePictureAlternatingAccent": "<PERSON><PERSON><PERSON> at<PERSON>s a<PERSON>s", "Common.define.smartArt.textThemePictureGrid": "<PERSON><PERSON><PERSON> att<PERSON> režģis", "Common.define.smartArt.textTitledMatrix": "Matrica ar nosaukumu", "Common.define.smartArt.textTitledPictureAccentList": "Attēlu akcentu sarak<PERSON> ar no<PERSON><PERSON>mu", "Common.define.smartArt.textTitledPictureBlocks": "Attēlu bloki ar no<PERSON><PERSON>mu", "Common.define.smartArt.textTitlePictureLineup": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> sarak<PERSON>", "Common.define.smartArt.textTrapezoidList": "Trapecveida sarak<PERSON>", "Common.define.smartArt.textUpwardArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Common.define.smartArt.textVaryingWidthList": "Dažādu platumu sarak<PERSON>", "Common.define.smartArt.textVerticalAccentList": "Vertikāls i<PERSON> sa<PERSON>", "Common.define.smartArt.textVerticalArrowList": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalBendingProcess": "Vertikāli izliekts process", "Common.define.smartArt.textVerticalBlockList": "Vertik<PERSON><PERSON> bloku sarak<PERSON>", "Common.define.smartArt.textVerticalBoxList": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Common.define.smartArt.textVerticalBracketList": "<PERSON><PERSON><PERSON><PERSON><PERSON> ieka<PERSON> sarak<PERSON>", "Common.define.smartArt.textVerticalBulletList": "<PERSON><PERSON><PERSON><PERSON><PERSON> aizzī<PERSON> sa<PERSON>", "Common.define.smartArt.textVerticalChevronList": "<PERSON><PERSON><PERSON><PERSON><PERSON> sku<PERSON><PERSON> sa<PERSON>", "Common.define.smartArt.textVerticalCircleList": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON> sa<PERSON>", "Common.define.smartArt.textVerticalCurvedList": "Vertikāli izliekts saraksts", "Common.define.smartArt.textVerticalEquation": "<PERSON><PERSON><PERSON><PERSON><PERSON> vien<PERSON>", "Common.define.smartArt.textVerticalPictureAccentList": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON> a<PERSON>cent<PERSON> sa<PERSON>", "Common.define.smartArt.textVerticalPictureList": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalProcess": "Vertikāls process", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON><PERSON>", "Common.Translation.tipFileLocked": "Dokuments ir bloķēts rediģēšanai. <PERSON><PERSON><PERSON> varat veikt izmaiņas un saglabāt to kā vietējo kopiju vēlāk.", "Common.Translation.tipFileReadOnly": "Fails ir tikai lasāms. <PERSON> sag<PERSON>, saglab<PERSON><PERSON><PERSON> failu ar jaunu nosaukumu vai citā vietā.", "Common.Translation.warnFileLocked": "Fails tiek rediģēts citā lietotnē. Jūs varat turpināt rediģēt un saglabāt to kā kopiju.", "Common.Translation.warnFileLockedBtnEdit": "Izveidot kopiju", "Common.Translation.warnFileLockedBtnView": "Atvērts skatīšanai", "Common.UI.ButtonColored.textAutoColor": "Automātisks", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON> jau<PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "Nav a<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Nav a<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ComboDataView.emptyComboText": "Nav stilu", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textHexErr": "Ievadītā vērtība ir nepareiza.<br>Ievadiet vērtību no 000000 līdz FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Ievadītā vērtība ir nepareiza.<br>Ievadiet skaitlisku vērtību no 0 līdz 255.", "Common.UI.HSBColorPicker.textNoColor": "Nav kr<PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON><PERSON> paroli", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> paroli", "Common.UI.SearchBar.textFind": "<PERSON>rast", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "<PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>", "Common.UI.SearchBar.tipOpenAdvancedSettings": "<PERSON><PERSON><PERSON><PERSON> papildu i<PERSON>", "Common.UI.SearchBar.tipPreviousResult": "Iepriekš<PERSON><PERSON><PERSON> re<PERSON>", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON><PERSON><PERSON> rezultā<PERSON>", "Common.UI.SearchDialog.textMatchCase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> burtu <PERSON>", "Common.UI.SearchDialog.textReplaceDef": "Ievadiet aizstājo<PERSON>", "Common.UI.SearchDialog.textSearchStart": "Ievadiet jū<PERSON> te<PERSON>", "Common.UI.SearchDialog.textTitle": "Atrast un aizvietot", "Common.UI.SearchDialog.textTitle2": "<PERSON>rast", "Common.UI.SearchDialog.textWholeWords": "<PERSON>ikai veseli vārdi", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Aizvietot", "Common.UI.SearchDialog.txtBtnReplaceAll": "Aizvietot visus", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON><PERSON><PERSON> nerād<PERSON>t šo zi<PERSON>u", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Doku<PERSON> ir mainījies.<br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokumentu, lai red<PERSON> i<PERSON>.", "Common.UI.ThemeColorPalette.textRecentColors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "<PERSON>las<PERSON><PERSON> gaisma", "Common.UI.Themes.txtThemeContrastDark": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "<PERSON><PERSON><PERSON> pats kā sistēma", "Common.UI.Window.cancelButtonText": "Atcelt", "Common.UI.Window.closeButtonText": "Aizvērt", "Common.UI.Window.noButtonText": "Nē", "Common.UI.Window.okButtonText": "<PERSON><PERSON>", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "<PERSON><PERSON><PERSON><PERSON> nerād<PERSON>t šo zi<PERSON>u", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "Informācija", "Common.UI.Window.textWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.yesButtonText": "Jā", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtaccent": "Ak<PERSON>s", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Fons", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Zils", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Tumši zaļa", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON>", "Common.Utils.ThemeColor.txtGreen": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Sārts", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Sarkans", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Teksts", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "Balts", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "adrese: ", "Common.Views.About.txtLicensee": "LICENSES ĪPAŠNIEKS", "Common.Views.About.txtLicensor": "LICENZĒTĀJS", "Common.Views.About.txtMail": "e-pasts: ", "Common.Views.About.txtPoweredBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON> ", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyText": "Lietot rakstot", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Teksta automātiskā <PERSON>", "Common.Views.AutoCorrectDialog.textAutoFormat": "Automātiski formatēt rakstot", "Common.Views.AutoCorrectDialog.textBulleted": "Automātiski a<PERSON>", "Common.Views.AutoCorrectDialog.textBy": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Pievienot punktu ar dubulto atstarpi", "Common.Views.AutoCorrectDialog.textFLCells": "Lietot lielo burtu tabulas sākumā", "Common.Views.AutoCorrectDialog.textFLDont": "Don`t capitalize after", "Common.Views.AutoCorrectDialog.textFLSentence": "<PERSON><PERSON>t lielo burtu teikuma sākum<PERSON>", "Common.Views.AutoCorrectDialog.textForLangFL": "Exceptions for the language:", "Common.Views.AutoCorrectDialog.textHyperlink": "Interneta un tīkla ceļi ar hip<PERSON>aitēm", "Common.Views.AutoCorrectDialog.textHyphens": "De<PERSON><PERSON> (--) ar <PERSON><PERSON> (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Mat<PERSON><PERSON><PERSON><PERSON> simbolu automātiskā koriģēšana", "Common.Views.AutoCorrectDialog.textNumbered": "Automā<PERSON><PERSON> numur<PERSON>i", "Common.Views.AutoCorrectDialog.textQuotes": "\"Taisnas pēdiņas\" ar \"izliektām pēdiņām\"", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Šīs izteiksmes ir atpazītas matemātiskās izteiksmes. Tās netiks automātiski formatētas slīprakstā.", "Common.Views.AutoCorrectDialog.textReplace": "Aizvietot", "Common.Views.AutoCorrectDialog.textReplaceText": "Aizvietot rakstot", "Common.Views.AutoCorrectDialog.textReplaceType": "Aizvietot tekstu rakstot", "Common.Views.AutoCorrectDialog.textReset": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textResetAll": "Atiestatīt uz noklusējuma", "Common.Views.AutoCorrectDialog.textRestore": "At<PERSON>uno<PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Automātis<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textWarnAddFL": "Exceptions must contain only the letters, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Atpaz<PERSON><PERSON><PERSON><PERSON><PERSON>j<PERSON> drī<PERSON> būt tikai burti A līdz Z, lielie vai mazie burti.", "Common.Views.AutoCorrectDialog.textWarnResetFL": "Any exceptions you added will be removed and the removed ones will be restored. Do you want to continue?", "Common.Views.AutoCorrectDialog.textWarnResetRec": "<PERSON><PERSON><PERSON><PERSON><PERSON> izteiksmes labojums tiks noņemts, un noņemtie tiks atjaunoti. Vai vēlaties turpināt?", "Common.Views.AutoCorrectDialog.warnReplace": "Automātiskās korekcijas ieraksts %1 jau pastāv. Vai vēlaties to aizstāt?", "Common.Views.AutoCorrectDialog.warnReset": "<PERSON><PERSON><PERSON><PERSON><PERSON>vienotais automātiskais labojums tiks noņemts, un mainītajiem tiks atjaunotas sākotnējās vērtības. Vai vēlaties turpināt?", "Common.Views.AutoCorrectDialog.warnRestore": "Automātiskās korekcijas ieraksts %1 tiks atiestatīts uz tā sākotnējo vērtību. Vai vēlaties turpināt?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Autors no A līdz Z", "Common.Views.Comments.mniAuthorDesc": "Autors no Z līdz A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniFilterGroups": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc grupas", "Common.Views.Comments.mniPositionAsc": "No augšas", "Common.Views.Comments.mniPositionDesc": "No apakšas", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "<PERSON><PERSON><PERSON> komentāru dokumentam", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAll": "Visi", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "Atcelt", "Common.Views.Comments.textClose": "Aizvērt", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "<PERSON><PERSON>", "Common.Views.Comments.textEnterCommentHint": "Ievadiet jū<PERSON> koment<PERSON>", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Atvērt vēlreiz", "Common.Views.Comments.textReply": "Atbildēt", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "<PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "Jums nav atļaujas atkārtoti atvērt komentāru", "Common.Views.Comments.txtEmpty": "Dokumentā nav komentāru.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON><PERSON><PERSON> nerād<PERSON>t šo zi<PERSON>u", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON> un iel<PERSON><PERSON><PERSON><PERSON> darb<PERSON>, i<PERSON><PERSON><PERSON><PERSON> redaktora izvēln<PERSON> darb<PERSON>, tiks veiktas tikai šajā redaktora cilnē.<br><br><PERSON> kopētu vai ielīmētu programmās vai no tām ārpus redaktora cilnes, i<PERSON><PERSON><PERSON>et tālāk norādītā<PERSON> tastatū<PERSON> kombin<PERSON>:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, izg<PERSON>šanas un ielīmēšanas darbības", "Common.Views.CopyWarningDialog.textToCopy": "kopijai", "Common.Views.CopyWarningDialog.textToCut": "izgriešanai", "Common.Views.CopyWarningDialog.textToPaste": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textStartOver": "Show from Beginning", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Ielā<PERSON><PERSON>...", "Common.Views.DocumentAccessDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> iestatījumi", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtSize": "Izmērs", "Common.Views.ExternalDiagramEditor.textTitle": "Diagrammu redaktors", "Common.Views.ExternalEditor.textClose": "Aizvērt", "Common.Views.ExternalEditor.textSave": "Saglabāt un i<PERSON>t", "Common.Views.ExternalOleEditor.textTitle": "Izklājlapu redaktors", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Šobrīd dokumentu rediģē vairāki lietotāji:", "Common.Views.Header.textAddFavorite": "Atzīmēt kā i<PERSON>i", "Common.Views.Header.textAdvSettings": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textBack": "<PERSON><PERSON><PERSON><PERSON> faila atra<PERSON> vietu", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideLines": "Pa<PERSON><PERSON>ē<PERSON> lineā<PERSON>", "Common.Views.Header.textHideNotes": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideStatusBar": "Paslēpt statusa jos<PERSON>", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textRemoveFavorite": "Noņemt no izlases", "Common.Views.Header.textSaveBegin": "Saglabā ...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "Visas izmaiņas saglabātas", "Common.Views.Header.textSaveExpander": "Visas izmaiņas saglabātas", "Common.Views.Header.textShare": "Dalīties", "Common.Views.Header.textStartOver": "Show from Beginning", "Common.Views.Header.textZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Pārvaldīt dokumenta piekļuves <PERSON>", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "Common.Views.Header.tipGoEdit": "Rediģēt šībrīža failu", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON>u", "Common.Views.Header.tipPrintQuick": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSearch": "Meklēt", "Common.Views.Header.tipStartOver": "Start slideshow from beginning", "Common.Views.Header.tipUndo": "Atsaukt", "Common.Views.Header.tipUndock": "Atdokot atsevišķā logā", "Common.Views.Header.tipUsers": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipViewUsers": "<PERSON><PERSON><PERSON><PERSON>t lietotājus un pārvaldīt dokumentu piekļ<PERSON>", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textCloseHistory": "Aizv<PERSON>rt vēst<PERSON>", "Common.Views.History.textHideAll": "<PERSON><PERSON><PERSON><PERSON> detaliz<PERSON><PERSON>", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "At<PERSON>uno<PERSON>", "Common.Views.History.textShowAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> detaliz<PERSON>tas izmaiņas", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Ielīmēt attēla vietrādi URL:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON><PERSON> la<PERSON> ir jāb<PERSON>t vietrāža URL formātā \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Ju<PERSON> j<PERSON>āda derīgo rindas un kolonnas skaitu.", "Common.Views.InsertTableDialog.txtColumns": "<PERSON><PERSON><PERSON> s<PERSON>", "Common.Views.InsertTableDialog.txtMaxText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vērtība šajā jomā ir {0}.", "Common.Views.InsertTableDialog.txtMinText": "<PERSON><PERSON><PERSON><PERSON><PERSON> vērtība šajā jomā ir {0}.", "Common.Views.InsertTableDialog.txtRows": "<PERSON><PERSON><PERSON> skaits", "Common.Views.InsertTableDialog.txtTitle": "Tabulas izmē<PERSON>", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.LanguageDialog.labelSelect": "Izvēlēties dokumenta valodu", "Common.Views.ListSettingsDialog.textBulleted": "Aizzīmots", "Common.Views.ListSettingsDialog.textFromFile": "No faila", "Common.Views.ListSettingsDialog.textFromStorage": "No glabātuves", "Common.Views.ListSettingsDialog.textFromUrl": "No URL", "Common.Views.ListSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textSelect": "Atlasīt no", "Common.Views.ListSettingsDialog.tipChange": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewBullet": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewImage": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNone": "Neviens", "Common.Views.ListSettingsDialog.txtOfText": "% no teksta", "Common.Views.ListSettingsDialog.txtSize": "Izmērs", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON><PERSON> ar", "Common.Views.ListSettingsDialog.txtSymbol": "Simbols", "Common.Views.ListSettingsDialog.txtTitle": "Saraksta iestatījumi", "Common.Views.ListSettingsDialog.txtType": "Tips", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON> failu", "Common.Views.OpenDialog.txtEncoding": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "Common.Views.OpenDialog.txtIncorrectPwd": "Parole nav pareiza.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON>eva<PERSON>t paroli, lai atvērtu failu", "Common.Views.OpenDialog.txtPassword": "Parole", "Common.Views.OpenDialog.txtProtected": "Kad ievad<PERSON>t paroli un atverat failu, pa<PERSON><PERSON><PERSON><PERSON>jā faila parole tiks atiestatīta.", "Common.Views.OpenDialog.txtTitle": "Izvēlēties %1 iespējas", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fails", "Common.Views.PasswordDialog.txtDescription": "<PERSON> pasa<PERSON>tu <PERSON>o dokumentu, uz<PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.PasswordDialog.txtIncorrectPwd": "Aps<PERSON>rinājuma <PERSON>", "Common.Views.PasswordDialog.txtPassword": "Parole", "Common.Views.PasswordDialog.txtRepeat": "Atk<PERSON><PERSON><PERSON> paroli", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.PasswordDialog.txtWarning": "Brīdinājums: <PERSON><PERSON><PERSON><PERSON> vai aizmirstu paroli nevar atgūt. Glabājiet drošā vietā.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar paroli", "Common.Views.Protection.hintDelPwd": "<PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vai d<PERSON>st paroli", "Common.Views.Protection.hintSignature": "<PERSON><PERSON><PERSON> para<PERSON>tu vai paraksta līniju", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtSignature": "Paraksts", "Common.Views.Protection.txtSignatureLine": "Paraksta līnija", "Common.Views.RecentFiles.txtOpenRecent": "<PERSON><PERSON><PERSON><PERSON> pēd<PERSON>", "Common.Views.RenameDialog.textName": "<PERSON><PERSON><PERSON>", "Common.Views.RenameDialog.txtInvalidName": "<PERSON>aila nosaukums nedrīkst saturēt šādas z<PERSON>: ", "Common.Views.ReviewChanges.hintNext": "Uz nākamo izmaiņu", "Common.Views.ReviewChanges.hintPrev": "Uz iepriekšē<PERSON> izmaiņu", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "<PERSON><PERSON><PERSON><PERSON> koprediģēšana. Visas izmaiņas tiek saglabātas automātiski.", "Common.Views.ReviewChanges.strStrict": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strStrictDesc": "<PERSON>zman<PERSON>jiet 'Saglabāt' ta<PERSON>, lai sinhronizētu sevis un citu veiktās izmai<PERSON>as.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Pieņemt šībrīža izmaiņas", "Common.Views.ReviewChanges.tipCoAuthMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kop<PERSON>g<PERSON> rediģēšanas režīmu", "Common.Views.ReviewChanges.tipCommentRem": "Noņemt komentārus", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Noņemt pašreizējos koment<PERSON>", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Atrisināt pa<PERSON><PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "Common.Views.ReviewChanges.tipHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON> versiju vēsturi", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON><PERSON><PERSON> š<PERSON>brīža izmaiņas", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reģistrēšana", "Common.Views.ReviewChanges.tipReviewView": "Izvēlēties režīmu, kurā vēlaties atainot izmaiņas", "Common.Views.ReviewChanges.tipSetDocLang": "Uzst<PERSON><PERSON><PERSON>t dokumenta valodu", "Common.Views.ReviewChanges.tipSetSpelling": "Notiek pareizrakstības p<PERSON>aude", "Common.Views.ReviewChanges.tipSharing": "Pārvaldīt dokumenta piekļuves <PERSON>", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "Pieņemt visas izmaiņas", "Common.Views.ReviewChanges.txtAcceptChanges": "Pieņemt izmaiņas", "Common.Views.ReviewChanges.txtAcceptCurrent": "Pieņemt šībrīža izmaiņas", "Common.Views.ReviewChanges.txtChat": "Čats", "Common.Views.ReviewChanges.txtClose": "Aizvērt", "Common.Views.ReviewChanges.txtCoAuthMode": "Ko<PERSON>īgā<PERSON> rediģēšanas režīms", "Common.Views.ReviewChanges.txtCommentRemAll": "Noņemt visus komentārus", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Noņemt pašreizējos koment<PERSON>", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON>ņ<PERSON>t manus komentārus", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Noņemt manus pašre<PERSON><PERSON><PERSON> komentārus", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus komentārus", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Atrisināt pa<PERSON><PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON><PERSON><PERSON><PERSON> manus komentārus", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Atrisinā<PERSON> manus pa<PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "Common.Views.ReviewChanges.txtDocLang": "Valoda", "Common.Views.ReviewChanges.txtFinal": "Visas izmaiņ<PERSON> (priekšskats)", "Common.Views.ReviewChanges.txtFinalCap": "Gala", "Common.Views.ReviewChanges.txtHistory": "Versiju vēsture", "Common.Views.ReviewChanges.txtMarkup": "Visa<PERSON> <PERSON><PERSON><PERSON> (rediģēšana)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Visas izmai<PERSON> (priekšskats)", "Common.Views.ReviewChanges.txtOriginalCap": "Oriģināls", "Common.Views.ReviewChanges.txtPrev": "Iepriek<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Noraidīt visas izmaiņas", "Common.Views.ReviewChanges.txtRejectChanges": "Nora<PERSON><PERSON><PERSON> izmaiņ<PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON><PERSON><PERSON>rīža izmaiņu", "Common.Views.ReviewChanges.txtSharing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "Notiek pareizrakstības p<PERSON>aude", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reģistrēšana", "Common.Views.ReviewChanges.txtView": "Attēlošanas režī<PERSON>", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textCancel": "Atcelt", "Common.Views.ReviewPopover.textClose": "Aizvērt", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textEnterComment": "Ievadiet jū<PERSON> koment<PERSON>", "Common.Views.ReviewPopover.textMention": "+pie<PERSON><PERSON><PERSON><PERSON>s piekļuvi dokumentam un nosūtīs e-pastu", "Common.Views.ReviewPopover.textMentionNotify": "+piemin<PERSON><PERSON>na informēs lietotāju pa e-pastu", "Common.Views.ReviewPopover.textOpenAgain": "Atvērt vēlreiz", "Common.Views.ReviewPopover.textReply": "Atbildēt", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Jums nav atļaujas atkārtoti atvērt komentāru", "Common.Views.ReviewPopover.txtDeleteTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtEditTip": "Rediģēt", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "Mape saglabāšanai", "Common.Views.SearchPanel.textCaseSensitive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> burtu <PERSON>", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textContentChanged": "Do<PERSON><PERSON> ir main<PERSON>ts.", "Common.Views.SearchPanel.textFind": "<PERSON>rast", "Common.Views.SearchPanel.textFindAndReplace": "Atrast un aizvietot", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} vien<PERSON> ir ve<PERSON> a<PERSON>.", "Common.Views.SearchPanel.textMatchUsingRegExp": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> regu<PERSON><PERSON>", "Common.Views.SearchPanel.textNoMatches": "Nav atbilstību", "Common.Views.SearchPanel.textNoSearchResults": "Nav mek<PERSON><PERSON><PERSON><PERSON> rezultātu", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "<PERSON><PERSON><PERSON><PERSON><PERSON> vienumi: {0}/{1}. Citi lietotāji ir bloķējuši atlikušo (-s) {2} vienumu (-s).", "Common.Views.SearchPanel.textReplace": "Aizvietot", "Common.Views.SearchPanel.textReplaceAll": "Aizvietot visus", "Common.Views.SearchPanel.textReplaceWith": "Aizvietot ar", "Common.Views.SearchPanel.textSearchAgain": "{0}Veiciet jaunu <PERSON>{1}, lai i<PERSON><PERSON><PERSON> prec<PERSON><PERSON><PERSON> rezult<PERSON>.", "Common.Views.SearchPanel.textSearchHasStopped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir <PERSON>", "Common.Views.SearchPanel.textSearchResults": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rezultāti: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textTooManyResults": "<PERSON>r <PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>, ko parā<PERSON><PERSON><PERSON> šeit", "Common.Views.SearchPanel.textWholeWords": "<PERSON>ikai veseli vārdi", "Common.Views.SearchPanel.tipNextResult": "<PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>", "Common.Views.SearchPanel.tipPreviousResult": "Iepriekš<PERSON><PERSON><PERSON> re<PERSON>", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON>t datu avotu", "Common.Views.ShapeShadowDialog.txtAngle": "Leņķis", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Treknraksts", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "Ievadiet parakstīt<PERSON><PERSON> vārdu", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Parakstītāja nosaukums nedrīkst būt tuk<PERSON>.", "Common.Views.SignDialog.textPurpose": "Šī dokumenta parakstīšanas mērķis", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "Izvēlēties attēlu", "Common.Views.SignDialog.textSignature": "Kā izskatās paraksts", "Common.Views.SignDialog.textTitle": "Parakstīt dokumentu", "Common.Views.SignDialog.textUseImage": "vai spiediet 'Izvēlēties attēlu', lai i<PERSON>totu attēlu kā parakstu", "Common.Views.SignDialog.textValid": "Derīgs no %1 līdz %2", "Common.Views.SignDialog.tipFontName": "<PERSON>ont<PERSON> no<PERSON>", "Common.Views.SignDialog.tipFontSize": "Fonta izmērs", "Common.Views.SignSettingsDialog.textAllowComment": "Atļaut parakstī<PERSON><PERSON><PERSON>m pievienot komentāru paraksta logā", "Common.Views.SignSettingsDialog.textDefInstruction": "Pirms parakstāt šo doku<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, vai saturs, zem kura parakst<PERSON><PERSON>, ir pareizs.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-pasts", "Common.Views.SignSettingsDialog.textInfoName": "<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.textInfoTitle": "Parakstī<PERSON><PERSON><PERSON> amats", "Common.Views.SignSettingsDialog.textInstructions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> para<PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> datumu paraksta līnijā", "Common.Views.SignSettingsDialog.textTitle": "Paraksta uzstādīšana", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Unikoda HEX vērtība", "Common.Views.SymbolTableDialog.textCopyright": "Autortiesību <PERSON>", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON><PERSON><PERSON><PERSON><PERSON> elipse", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnDash": "De<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnSpace": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textFont": "Fonts", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> defise", "Common.Views.SymbolTableDialog.textNBSpace": "Nelaužamā atstarpe", "Common.Views.SymbolTableDialog.textPilcrow": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em atstarpe", "Common.Views.SymbolTableDialog.textRange": "Diapazons", "Common.Views.SymbolTableDialog.textRecent": "<PERSON><PERSON><PERSON> simboli", "Common.Views.SymbolTableDialog.textRegistered": "Reģistrēta zīme", "Common.Views.SymbolTableDialog.textSCQuote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSection": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON><PERSON> defise", "Common.Views.SymbolTableDialog.textSOQuote": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Simboli", "Common.Views.SymbolTableDialog.textTitle": "Simbols", "Common.Views.SymbolTableDialog.textTradeMark": "<PERSON><PERSON><PERSON> z<PERSON> simbols", "Common.Views.UserNameDialog.textDontShow": "<PERSON><PERSON><PERSON> neja<PERSON><PERSON><PERSON> man", "Common.Views.UserNameDialog.textLabel": "Etiķete:", "Common.Views.UserNameDialog.textLabelError": "Etiķete nedr<PERSON><PERSON>t būt tuk<PERSON>.", "PE.Controllers.DocumentHolder.textLongName": "Enter a name that is less than 255 characters.", "PE.Controllers.DocumentHolder.textNameLayout": "Layout name", "PE.Controllers.DocumentHolder.textNameMaster": "Master name", "PE.Controllers.DocumentHolder.textRenameTitleLayout": "<PERSON><PERSON>out", "PE.Controllers.DocumentHolder.textRenameTitleMaster": "<PERSON><PERSON>", "PE.Controllers.LeftMenu.leavePageText": "Visas nesaglabātās izmaiņas šajā dokumentā tiks zaudētas.<br> Noklikšķiniet uz \"Atcelt\" pēc tam uz \"Saglabāt\" lai tās saglabātu. Noklikšķiniet uz \"Labi\", lai atmestu visas nesaglabātās izmaiņas.", "PE.Controllers.LeftMenu.newDocumentTitle": "<PERSON>enosa<PERSON><PERSON>", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.LeftMenu.requestEditRightsText": "Pieprasa rediģēšanas tiesības...", "PE.Controllers.LeftMenu.textLoadHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON> versiju vēsturi...", "PE.Controllers.LeftMenu.textNoTextFound": "<PERSON><PERSON><PERSON> meklētos datus nevarēja atrast. Pielāgojiet meklēšanas opcijas.", "PE.Controllers.LeftMenu.textReplaceSkipped": "<PERSON>r ve<PERSON>ta <PERSON>. <PERSON><PERSON> {0} gadījumi.", "PE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON>r ve<PERSON>ta <PERSON>. Aizstātie gadīju<PERSON>: {0}", "PE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "PE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON>", "PE.Controllers.Main.applyChangesTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus...", "PE.Controllers.Main.applyChangesTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus", "PE.Controllers.Main.confirmMaxChangesSize": "Darbību lielums pārsniedz jūsu serverim iestatīto i<PERSON>.<br>Nospiediet \"Atsaukt\", lai atceltu pēdējo darb<PERSON>bu, vai nospiediet \"<PERSON>rpināt\", lai darb<PERSON>bu turpin<PERSON>tu lokāli (jums ir jālejupielādē failu vai jākopē tā saturu, lai p<PERSON><PERSON><PERSON>, ka nekas nav zaudēts).", "PE.Controllers.Main.convertationTimeoutText": "Konversijas ta<PERSON> p<PERSON>.", "PE.Controllers.Main.criticalErrorExtText": "Nospiediet \"Labi\", lai atgrieztos dokumentu sarakstā.", "PE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "PE.Controllers.Main.downloadTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pre<PERSON>...", "PE.Controllers.Main.downloadTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON><PERSON> mēģināt veikt da<PERSON>, kuru nedrīkstat veikt.<br><PERSON><PERSON><PERSON><PERSON>, sazinieties ar savu dokumentu servera administratoru.", "PE.Controllers.Main.errorBadImageUrl": "Nav pareizs attēla vietrāža URL", "PE.Controllers.Main.errorCannotPasteImg": "<PERSON><PERSON><PERSON> nevaram ielīmēt šo attēlu no starpliktuves, taču varat to saglabāt savā ierīcē un \nievietot to no turienes, vai arī varat kopēt attēlu bez teksta un ielīmēt to prezentācijā.", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Servera savienojums ir zaud<PERSON>. Dokumentu pašlaik nevar rediģēt.", "PE.Controllers.Main.errorComboSeries": "<PERSON> izve<PERSON>tu kombinēto diagrammu, atlasiet vismaz divas datu sērijas.", "PE.Controllers.Main.errorConnectToServer": "Dokumentu neizdevās nog<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, pārbaudiet savienojuma uzstādījumus vai sazinieties ar savu administratoru.<br><PERSON><PERSON><PERSON><PERSON><PERSON> 'OK', jūs varēsit lejupielādēt dokumentu.", "PE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON><PERSON><PERSON>.<br><PERSON><PERSON> bā<PERSON> k<PERSON>. Sazinieties ar at<PERSON><PERSON> die<PERSON>, ja kļūda joprojām pastāv.", "PE.Controllers.Main.errorDataEncrypted": "<PERSON>r sa<PERSON><PERSON> šifr<PERSON>, tā<PERSON> nevar <PERSON>.", "PE.Controllers.Main.errorDataRange": "Nepareizs datu diapazons.", "PE.Controllers.Main.errorDefaultMessage": "Kļūdas kods: %1", "PE.Controllers.Main.errorDirectUrl": "Verificējiet saiti uz dokumentu.<br><PERSON><PERSON> saitei ir jābūt tiešai saitei uz failu lejupielādei.", "PE.Controllers.Main.errorEditingDownloadas": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>da darba laikā ar dokumentu.<br><PERSON><PERSON><PERSON><PERSON><PERSON> opciju Leju<PERSON>lādēt kā, lai saglabātu faila dublējum<PERSON>pi<PERSON> diskā.", "PE.Controllers.Main.errorEditingSaveas": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> darba laikā ar dokumentu.<br><PERSON><PERSON><PERSON><PERSON><PERSON> opciju Saglabāt kā, lai saglabātu faila dublējumkopi<PERSON> diskā.", "PE.Controllers.Main.errorEmailClient": "Nevarēja atrast e-pasta klientu.", "PE.Controllers.Main.errorFilePassProtect": "Fails ir aizsargāts ar paroli un to nevar atvērt.", "PE.Controllers.Main.errorFileSizeExceed": "<PERSON><PERSON>a lielums pārsniedz jūsu serverim iestatīto ierobežojumu.<br><PERSON> iegūtu sīk<PERSON>ku inform<PERSON>, sazinieties ar savu dokumentu servera administratoru.", "PE.Controllers.Main.errorForceSave": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON> failu, r<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> iespēju 'Lejupielādēt kā', lai noglabātu failu datora cietajā diskā, vai mēģiniet vēlāk vēlreiz.", "PE.Controllers.Main.errorInconsistentExt": "<PERSON><PERSON><PERSON> failu, r<PERSON><PERSON><PERSON>.<br><PERSON><PERSON>a saturs neatbilst faila papla<PERSON><PERSON><PERSON>.", "PE.Controllers.Main.errorInconsistentExtDocx": "At<PERSON>ot failu, r<PERSON><PERSON><PERSON>.<br><PERSON><PERSON><PERSON> saturs atbilst teksta dokumentiem (piem., docx), taču failam ir neatbilstīgs paplašinājums: %1.", "PE.Controllers.Main.errorInconsistentExtPdf": "At<PERSON>ot failu, r<PERSON><PERSON><PERSON>.<br><PERSON><PERSON>a saturs atbilst vienam no šiem formātiem: pdf/djvu/xps/oxps, taču failam ir neatbilstīgs paplašinājums: %1.", "PE.Controllers.Main.errorInconsistentExtPptx": "<PERSON><PERSON>ot failu, r<PERSON><PERSON><PERSON>.<br><PERSON><PERSON><PERSON> saturs atbilst prezentācijām (piem., pptx), taču failam ir neatbilstīgs paplašinājums: %1.", "PE.Controllers.Main.errorInconsistentExtXlsx": "At<PERSON>ot failu, r<PERSON><PERSON><PERSON>.<br><PERSON><PERSON><PERSON> saturs atbilst izklājlapām (piem., xlsx), taču failam ir neatbilstīgs paplašinājums: %1.", "PE.Controllers.Main.errorKeyEncrypt": "Nezināms atslēgas deskriptors", "PE.Controllers.Main.errorKeyExpire": "Atslēgas deskrip<PERSON>", "PE.Controllers.Main.errorLoadingFont": "Fonti netiek ielādēti.<br>Sazinieties ar savu dokumentu servera administratoru.", "PE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "PE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "PE.Controllers.Main.errorServerVersion": "Redaktora versija ir at<PERSON>. Lapa tiks pā<PERSON>, lai pie<PERSON>rotu i<PERSON>.", "PE.Controllers.Main.errorSessionAbsolute": "Dokumentu rediģēšanas sesija ir beigusies. Ielādējiet lapu atkārtoti.", "PE.Controllers.Main.errorSessionIdle": "Dokuments nav rediģēts ilgāku laiku. Ielādējiet lapu atkārtoti.", "PE.Controllers.Main.errorSessionToken": "Pārtraukts savienojums serverim. Ielādējiet lapu atkārtoti.", "PE.Controllers.Main.errorSetPassword": "Nevarēja i<PERSON> par<PERSON>.", "PE.Controllers.Main.errorStockChart": "Nepareiza rindu secība. Lai izveido<PERSON> ak<PERSON>ju diagrammu novietojiet datus lapā šādā secībā:<br> s<PERSON><PERSON><PERSON><PERSON><PERSON>, maks<PERSON><PERSON><PERSON><PERSON> cena, mini<PERSON><PERSON><PERSON><PERSON> cena, slē<PERSON><PERSON><PERSON> cena.", "PE.Controllers.Main.errorToken": "Nav pareizi noformēts dokumenta drošības marķieris.<br><PERSON><PERSON><PERSON><PERSON>, sazinieties ar savu dokumenta servera administratoru.", "PE.Controllers.Main.errorTokenExpire": "Ir be<PERSON><PERSON>s dokumenta drošības marķiera termiņš.<br><PERSON><PERSON><PERSON><PERSON>, sazinieties ar savu dokumentu servera administratoru.", "PE.Controllers.Main.errorUpdateVersion": "Faila versija ir main<PERSON>ta. Lapa tiks atkārtoti ielādēta.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Savienojums ir atja<PERSON><PERSON>, un faila versija ir mainīta.<br>Pirms varat turpināt darbu, jums ir jālejupielādē failu vai kopējiet tā saturu, lai p<PERSON><PERSON><PERSON>, ka nekas nav zaud<PERSON>, un pēc tam atkārtoti ielādējiet šo lapu.", "PE.Controllers.Main.errorUserDrop": "<PERSON><PERSON><PERSON>.", "PE.Controllers.Main.errorUsersExceed": "<PERSON><PERSON> p<PERSON><PERSON>s cenu plāna atļautais lietotāju skaits.", "PE.Controllers.Main.errorViewerDisconnect": "Savienojums ir zaud<PERSON>ts. <PERSON><PERSON><PERSON>m varat aplūkot dokumentu,<br>ta<PERSON>u nevarē<PERSON>t lejupielādēt vai drukāt, līdz nav atjaunots savienojums.", "PE.Controllers.Main.leavePageText": "<PERSON><PERSON><PERSON> prezentācijā ir nesaglabātas izmaiņas. Noklikšķiniet uz \"Pa<PERSON>t šajā lapā\" un pēc tam uz \"Saglabāt\", lai tos saglabātu. Noklikšķiniet uz \"Pamest lapu\", lai atmestu visas nesaglabātās izmaiņas.", "PE.Controllers.Main.leavePageTextOnClose": "Visas nesaglabātās izmaiņas šajā prezentācijā tiks zaudētas.<br> Noklikšķiniet uz \"Atcelt\", pēc tam uz \"Saglabāt\", lai tās saglab<PERSON>. Noklikšķiniet uz \"Labi\", lai atmestu visas nesaglabātās izmaiņas.", "PE.Controllers.Main.loadFontsTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus...", "PE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus", "PE.Controllers.Main.loadFontTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus...", "PE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus", "PE.Controllers.Main.loadImagesTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> attēlus...", "PE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>", "PE.Controllers.Main.loadImageTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> attē<PERSON>...", "PE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadingDocumentTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> prezen<PERSON>...", "PE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadThemeTextText": "<PERSON><PERSON><PERSON>...", "PE.Controllers.Main.loadThemeTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.openErrorText": "<PERSON><PERSON><PERSON> laik<PERSON> radā<PERSON>.", "PE.Controllers.Main.openTextText": "Atver prezentāciju...", "PE.Controllers.Main.openTitleText": "Atver prezentāciju", "PE.Controllers.Main.printTextText": "<PERSON><PERSON><PERSON>...", "PE.Controllers.Main.printTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "PE.Controllers.Main.requestEditFailedMessageText": "<PERSON><PERSON> prezentāciju kāds šobrīd rediģē. Pamēģiniet vēlreiz vēlāk.", "PE.Controllers.Main.requestEditFailedTitleText": "Piek<PERSON><PERSON> liegta", "PE.Controllers.Main.saveErrorText": "<PERSON>aila nogla<PERSON><PERSON><PERSON><PERSON> laik<PERSON> radā<PERSON>.", "PE.Controllers.Main.saveErrorTextDesktop": "Šo failu nevar saglab<PERSON>t vai izveidot.<br>Iespēja<PERSON>: <br>1. <PERSON><PERSON> ir tikai lasāms. <br>2. <PERSON>o failu rediģē cits lietotājs. <br>3. <PERSON>sks ir pilns vai bojāts.", "PE.Controllers.Main.saveTextText": "Prezentācijas sagla<PERSON>...", "PE.Controllers.Main.saveTitleText": "Prezentācijas <PERSON>", "PE.Controllers.Main.scriptLoadError": "Savienojums ir pār<PERSON><PERSON> lē<PERSON>, da<PERSON><PERSON> komponentus nevarēja ielādēt. Ielādējiet lapu atkārtoti.", "PE.Controllers.Main.splitDividerErrorText": "<PERSON><PERSON><PERSON> skaitam ir jādalās ar %1.", "PE.Controllers.Main.splitMaxColsErrorText": "<PERSON><PERSON><PERSON> s<PERSON>tam ir jā<PERSON>t ma<PERSON> par %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "Rindu skaitam ir jābūt mazākam nekā %1.", "PE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textApplyAll": "Piemērot visiem vienādojumiem", "PE.Controllers.Main.textBuyNow": "Apmeklēt vietni", "PE.Controllers.Main.textChangesSaved": "Visas izmaiņas saglabātas", "PE.Controllers.Main.textClose": "Aizvērt", "PE.Controllers.Main.textCloseTip": "Noklikšķiniet, lai aizvērtu galu", "PE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "PE.Controllers.Main.textContactUs": "Sazināties ar p<PERSON><PERSON>", "PE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textConvertEquation": "<PERSON>is vienādojums tika i<PERSON>, i<PERSON><PERSON><PERSON><PERSON> veco vienādojumu redaktora versiju, kas vairs netiek atbalstīta. Lai to rediģētu, konvertējiet vienādojumu Office Math ML formātā.<br>Vai konvertēt tagad?", "PE.Controllers.Main.textCustomLoader": "Ņemiet vērā, ka saskaņā ar licences noteikumiem jums nav tiesību mainīt ielādētāju.<br>Sazinieties ar mūsu pārdo<PERSON> noda<PERSON>, lai saņemtu piedāvājumu.", "PE.Controllers.Main.textDisconnect": "Savienojums ir <PERSON>", "PE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textHasMacros": "Fails satur automātis<PERSON> makro.<br><PERSON><PERSON> vēlaties palaist makro?", "PE.Controllers.Main.textLearnMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textLongName": "<PERSON><PERSON><PERSON><PERSON>, kas ir ma<PERSON> par 128 rakstzīm<PERSON>m.", "PE.Controllers.Main.textNoLicenseTitle": "ONLYOFFICE pieslēguma ierobežojums", "PE.Controllers.Main.textObject": "Object", "PE.Controllers.Main.textPaidFeature": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textReconnect": "Savienojums ir atjaunots", "PE.Controllers.Main.textRemember": "Atcerēties manu izvēli visiem failiem", "PE.Controllers.Main.textRememberMacros": "Atcerēties manu izvēli visiem makro", "PE.Controllers.Main.textRenameError": "Lietotāja nosaukums nedrīkst būt tuk<PERSON>.", "PE.Controllers.Main.textRenameLabel": "Ievadiet no<PERSON>, ko izman<PERSON>t sadarbī<PERSON>i", "PE.Controllers.Main.textRequestMacros": "<PERSON><PERSON><PERSON> veic pieprasījumu vietrādim URL. Vai vēlaties atļaut pieprasījumu %1?", "PE.Controllers.Main.textShape": "Forma", "PE.Controllers.Main.textStrict": "Precīz<PERSON>tai<PERSON>", "PE.Controllers.Main.textText": "Teksts", "PE.Controllers.Main.textTryQuickPrint": "<PERSON><PERSON><PERSON> atlas<PERSON>t <PERSON>: viss dokuments tiks drukāts ar pēdējo atlasīto vai noklusējuma printeri.<br>Vai vēlaties turpināt?", "PE.Controllers.Main.textTryUndoRedo": "Funkcijas Atsaukt/Atcelt ir atspējotas ātrās koprediģēšanas režīmam.<br>Noklikšķiniet uz pogas 'Precīzais režīms', lai pārslēgtos uz 'Precīzo koprediģēšanas režīmu', lai rediģētu failu bez citu lietotāju iejaukšanās un nosūtītu izmaiņas tikai pēc tam, kad saglabājāt tos. Varat pārslēgties starp koprediģēšanas režīmiem, izmantojot redaktoru Papildu iestatījumi.", "PE.Controllers.Main.textTryUndoRedoWarn": "At<PERSON><PERSON><PERSON><PERSON>/atkārto<PERSON> funkcijas ātrās koprediģēšanas režīmā ir atspējotas.", "PE.Controllers.Main.textUndo": "Atsaukt", "PE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "PE.Controllers.Main.textUpdating": "Updating", "PE.Controllers.Main.titleLicenseExp": "Licencei ir be<PERSON><PERSON>", "PE.Controllers.Main.titleLicenseNotActive": "License not active", "PE.Controllers.Main.titleServerVersion": "Atjaunināts redaktors", "PE.Controllers.Main.titleUpdateVersion": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtAddFirstSlide": "Noklikšķiniet, lai pievienotu pirmo slaidu", "PE.Controllers.Main.txtAddNotes": "Noklikšķiniet, lai pievienotu piezīmes", "PE.Controllers.Main.txtAnimationPane": "Animation Pane", "PE.Controllers.Main.txtArt": "Ievadiet savu tekstu", "PE.Controllers.Main.txtBasicShapes": "Pamata formas", "PE.Controllers.Main.txtButtons": "Pogas", "PE.Controllers.Main.txtCallouts": "Remarkas", "PE.Controllers.Main.txtCharts": "Diagram<PERSON>", "PE.Controllers.Main.txtClipArt": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtDateTime": "Datums un laiks", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON><PERSON> nosaukums", "PE.Controllers.Main.txtEditingMode": "Uzstāda rediģēšanas režīmu...", "PE.Controllers.Main.txtEnd": "End: ${0}s", "PE.Controllers.Main.txtErrorLoadHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vē<PERSON>", "PE.Controllers.Main.txtFiguredArrows": "Figu<PERSON>ē<PERSON> bultiņas", "PE.Controllers.Main.txtFirstSlide": "<PERSON><PERSON><PERSON> slaids", "PE.Controllers.Main.txtFooter": "Zemteksta piezī<PERSON>", "PE.Controllers.Main.txtHeader": "G<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtImage": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtLastSlide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> slaids", "PE.Controllers.Main.txtLines": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtLoading": "Ielā<PERSON><PERSON>...", "PE.Controllers.Main.txtLoop": "Loop: ${0}s", "PE.Controllers.Main.txtMath": "<PERSON><PERSON>.", "PE.Controllers.Main.txtMedia": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtNeedSynchronize": "<PERSON><PERSON> ir at<PERSON><PERSON>", "PE.Controllers.Main.txtNextSlide": "Next slide", "PE.Controllers.Main.txtNone": "Neviens", "PE.Controllers.Main.txtPicture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtPlayAll": "Play All", "PE.Controllers.Main.txtPlayFrom": "Play From", "PE.Controllers.Main.txtPlaySelected": "Play Selected", "PE.Controllers.Main.txtPrevSlide": "Iepriekšējais slaids", "PE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "PE.Controllers.Main.txtScheme_Aspect": "Aspect", "PE.Controllers.Main.txtScheme_Blue": "Blue", "PE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "PE.Controllers.Main.txtScheme_Blue_II": "Blue II", "PE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "PE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "PE.Controllers.Main.txtScheme_Green": "Green", "PE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "PE.Controllers.Main.txtScheme_Marquee": "Marquee", "PE.Controllers.Main.txtScheme_Median": "Median", "PE.Controllers.Main.txtScheme_Office": "Office", "PE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "PE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "PE.Controllers.Main.txtScheme_Orange": "Orange", "PE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "PE.Controllers.Main.txtScheme_Paper": "Paper", "PE.Controllers.Main.txtScheme_Red": "Red", "PE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "PE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "PE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "PE.Controllers.Main.txtScheme_Violet": "Violet", "PE.Controllers.Main.txtScheme_Violet_II": "Violet II", "PE.Controllers.Main.txtScheme_Yellow": "Yellow", "PE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "PE.Controllers.Main.txtSeries": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_accentBorderCallout1": "1. r<PERSON><PERSON><PERSON><PERSON> (apmales un izcēluma j<PERSON>la)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "2. r<PERSON><PERSON><PERSON><PERSON> (apmales un izcēluma j<PERSON>la)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "3. r<PERSON><PERSON><PERSON><PERSON> (apmales un izcēluma j<PERSON>la)", "PE.Controllers.Main.txtShape_accentCallout1": "1. <PERSON><PERSON><PERSON><PERSON><PERSON> (izcēluma j<PERSON>)", "PE.Controllers.Main.txtShape_accentCallout2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> (izcēluma j<PERSON>)", "PE.Controllers.Main.txtShape_accentCallout3": "3. <PERSON><PERSON><PERSON><PERSON><PERSON> (izcēluma j<PERSON>)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Atpakaļ vai iepriekšējā poga", "PE.Controllers.Main.txtShape_actionButtonBeginning": "<PERSON><PERSON><PERSON><PERSON> poga", "PE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON><PERSON> poga", "PE.Controllers.Main.txtShape_actionButtonDocument": "Dokumenta poga", "PE.Controllers.Main.txtShape_actionButtonEnd": "<PERSON><PERSON><PERSON> poga", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "Uz priekšu vai nākamā poga", "PE.Controllers.Main.txtShape_actionButtonHelp": "Palīdzības poga", "PE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON><PERSON> poga", "PE.Controllers.Main.txtShape_actionButtonInformation": "Informācijas poga", "PE.Controllers.Main.txtShape_actionButtonMovie": "Filmas poga", "PE.Controllers.Main.txtShape_actionButtonReturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> poga", "PE.Controllers.Main.txtShape_actionButtonSound": "Skaņas poga", "PE.Controllers.Main.txtShape_arc": "Arh.", "PE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "PE.Controllers.Main.txtShape_bentConnector5": "Leņķveida savienotājs", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Leņķveida bulti<PERSON><PERSON>", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Leņķveida dubult<PERSON><PERSON><PERSON><PERSON> sa<PERSON>s", "PE.Controllers.Main.txtShape_bentUpArrow": "<PERSON>z augšu izliekta bultiņa", "PE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON>ī<PERSON><PERSON>", "PE.Controllers.Main.txtShape_blockArc": "Bloka arh.", "PE.Controllers.Main.txtShape_borderCallout1": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_borderCallout2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_borderCallout3": "3. <PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_bracePair": "Fig<PERSON>rie<PERSON><PERSON> pāris", "PE.Controllers.Main.txtShape_callout1": "1. r<PERSON><PERSON><PERSON><PERSON> (nav apmales)", "PE.Controllers.Main.txtShape_callout2": "2. r<PERSON><PERSON><PERSON><PERSON> (nav apmales)", "PE.Controllers.Main.txtShape_callout3": "3. <PERSON><PERSON><PERSON><PERSON><PERSON> (nav apmales)", "PE.Controllers.Main.txtShape_can": "Atc.", "PE.Controllers.Main.txtShape_chevron": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_chord": "Akords", "PE.Controllers.Main.txtShape_circularArrow": "Riņķveida bultiņa", "PE.Controllers.Main.txtShape_cloud": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_cloudCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "PE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_cube": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Izliekts savienotājs", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Izliektas bultiņas <PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Izliektas dubultbultiņas savienotājs", "PE.Controllers.Main.txtShape_curvedDownArrow": "Uz leju izliekta bultiņa", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Pa kreisi izliekta bultiņa", "PE.Controllers.Main.txtShape_curvedRightArrow": "Pa labi izliekta bultiņa", "PE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON>z augšu izliekta bultiņa", "PE.Controllers.Main.txtShape_decagon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_diagStripe": "Diagonāla svītra", "PE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_dodecagon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_donut": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON> vilnis", "PE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON><PERSON> le<PERSON>", "PE.Controllers.Main.txtShape_downArrowCallout": "Bultiņas uz leju remarka", "PE.Controllers.Main.txtShape_ellipse": "Elipse", "PE.Controllers.Main.txtShape_ellipseRibbon": "Uz leju izliekta lente", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Uz augšu izliekta lente", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Blokshēma Alternatīvs process", "PE.Controllers.Main.txtShape_flowChartCollate": "Blokshēma Sašķirot komplektos", "PE.Controllers.Main.txtShape_flowChartConnector": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDecision": "Blokshē<PERSON>", "PE.Controllers.Main.txtShape_flowChartDelay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDisplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDocument": "Blokshēma Dokuments", "PE.Controllers.Main.txtShape_flowChartExtract": "Blokshēma Ekstrakts", "PE.Controllers.Main.txtShape_flowChartInputOutput": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Blokshēma <PERSON> g<PERSON>", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Blokshēma Ma<PERSON>ētis<PERSON> disks", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Blokshēma Tiešās <PERSON> k<PERSON>ve", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Blokshēma Secīgās <PERSON>", "PE.Controllers.Main.txtShape_flowChartManualInput": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Blokshēma Manuālā operācija", "PE.Controllers.Main.txtShape_flowChartMerge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Blokshē<PERSON> ", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Blokshē<PERSON>", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dati", "PE.Controllers.Main.txtShape_flowChartOr": "Blokshēma Vai", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Blokshēma Iepriekš noteikts process", "PE.Controllers.Main.txtShape_flowChartPreparation": "Blokshēma Sagatavošana", "PE.Controllers.Main.txtShape_flowChartProcess": "Blokshēma Process", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Blokshē<PERSON>", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Blokshēma Caurumota lente", "PE.Controllers.Main.txtShape_flowChartSort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Blokshēma Summējoša<PERSON>", "PE.Controllers.Main.txtShape_flowChartTerminator": "Blokshēma Terminators", "PE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON><PERSON><PERSON> stūris", "PE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON> rā<PERSON>ja", "PE.Controllers.Main.txtShape_heart": "Sirds", "PE.Controllers.Main.txtShape_heptagon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_homePlate": "Pentagons", "PE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_irregularSeal1": "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_irregularSeal2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftArrow": "Bulta pa kreisi", "PE.Controllers.Main.txtShape_leftArrowCallout": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftRightArrow": "Pa kreisi un pa labi vērstā bultiņa", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Pa kreisi un pa labi vērstās bulti<PERSON> remarka", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Bultiņa pa kreisi un labi uz augšu", "PE.Controllers.Main.txtShape_leftUpArrow": "Bultiņa pa kreisi uz augšu", "PE.Controllers.Main.txtShape_lightningBolt": "Zibens", "PE.Controllers.Main.txtShape_line": "Lī<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON> b<PERSON>", "PE.Controllers.Main.txtShape_mathDivide": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMultiply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathNotEqual": "Nav vienāds", "PE.Controllers.Main.txtShape_mathPlus": "Plus", "PE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_noSmoking": "\"Ne\" simbols", "PE.Controllers.Main.txtShape_notchedRightArrow": "<PERSON><PERSON> bulti<PERSON>a ar ierobojumu", "PE.Controllers.Main.txtShape_octagon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_parallelogram": "Paralelogramma", "PE.Controllers.Main.txtShape_pentagon": "Pentagons", "PE.Controllers.Main.txtShape_pie": "<PERSON><PERSON><PERSON><PERSON> diagramma", "PE.Controllers.Main.txtShape_plaque": "Parakstīt", "PE.Controllers.Main.txtShape_plus": "Plus", "PE.Controllers.Main.txtShape_polyline1": "Skricelējums", "PE.Controllers.Main.txtShape_polyline2": "Brīvforma", "PE.Controllers.Main.txtShape_quadArrow": "Č<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "PE.Controllers.Main.txtShape_quadArrowCallout": "Četrvir<PERSON><PERSON> bult<PERSON>a", "PE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_ribbon": "Lente uz leju", "PE.Controllers.Main.txtShape_ribbon2": "Lente uz augšu", "PE.Controllers.Main.txtShape_rightArrow": "Bulta pa labi", "PE.Controllers.Main.txtShape_rightArrowCallout": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_round1Rect": "<PERSON><PERSON><PERSON><PERSON> viena stūra ta<PERSON>", "PE.Controllers.Main.txtShape_round2DiagRect": "<PERSON><PERSON><PERSON><PERSON> stūra ta<PERSON>", "PE.Controllers.Main.txtShape_round2SameRect": "<PERSON><PERSON><PERSON><PERSON> tās pa<PERSON>s puses stūra ta<PERSON>ris", "PE.Controllers.Main.txtShape_roundRect": "<PERSON><PERSON><PERSON><PERSON> stū<PERSON>", "PE.Controllers.Main.txtShape_rtTriangle": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON><PERSON><PERSON> seja", "PE.Controllers.Main.txtShape_snip1Rect": "Tais<PERSON><PERSON><PERSON> ar vienu nogrie<PERSON> stū<PERSON>,", "PE.Controllers.Main.txtShape_snip2DiagRect": "Taisnstūris ar nogrieztiem pretējiem stūriem", "PE.Controllers.Main.txtShape_snip2SameRect": "Taisnstūris ar vienā pusē nogrieztiem stūriem", "PE.Controllers.Main.txtShape_snipRoundRect": "Taisnstūris ar vienu nogrieztu un noapaļotu stūri", "PE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "10 punktu zvaigzne", "PE.Controllers.Main.txtShape_star12": "12 punktu zvaigzne", "PE.Controllers.Main.txtShape_star16": "16 punk<PERSON> zvaigzne", "PE.Controllers.Main.txtShape_star24": "24 punktu zvaigzne", "PE.Controllers.Main.txtShape_star32": "32 punktu zvaigzne", "PE.Controllers.Main.txtShape_star4": "4 punktu zvaigzne", "PE.Controllers.Main.txtShape_star5": "5 punktu zvaigzne", "PE.Controllers.Main.txtShape_star6": "6 punktu zvaigzne", "PE.Controllers.Main.txtShape_star7": "7 punktu zvaigzne", "PE.Controllers.Main.txtShape_star8": "8 punktu zvaigzne", "PE.Controllers.Main.txtShape_stripedRightArrow": "<PERSON><PERSON> bulti<PERSON>a ar svītrām", "PE.Controllers.Main.txtShape_sun": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_textRect": "Uzraksts", "PE.Controllers.Main.txtShape_trapezoid": "Trapecveida", "PE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_upArrowCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_upDownArrow": "Bultiņa lejup un augšup", "PE.Controllers.Main.txtShape_uturnArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_verticalScroll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_wave": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_wedgeRectCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Noapaļota taisnstūra remarka", "PE.Controllers.Main.txtSldLtTBlank": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTChart": "Di<PERSON>ram<PERSON>", "PE.Controllers.Main.txtSldLtTChartAndTx": "Diagramma un teksts", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Klipkopa un teksts", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Klipkopa un vertikāls teksts", "PE.Controllers.Main.txtSldLtTCust": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTDgm": "Di<PERSON>ram<PERSON>", "PE.Controllers.Main.txtSldLtTFourObj": "Četri objekti", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Multivide un teksts", "PE.Controllers.Main.txtSldLtTObj": "Nosaukums un objekts", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Objekts un divi objekti", "PE.Controllers.Main.txtSldLtTObjAndTx": "Objekts un teksts", "PE.Controllers.Main.txtSldLtTObjOnly": "Objekts", "PE.Controllers.Main.txtSldLtTObjOverTx": "Objekts virs teksta", "PE.Controllers.Main.txtSldLtTObjTx": "<PERSON><PERSON><PERSON><PERSON>, objekts un paraksts", "PE.Controllers.Main.txtSldLtTPicTx": "Att<PERSON><PERSON> un paraksts", "PE.Controllers.Main.txtSldLtTSecHead": "<PERSON><PERSON><PERSON><PERSON> gal<PERSON>e", "PE.Controllers.Main.txtSldLtTTbl": "Tabula", "PE.Controllers.Main.txtSldLtTTitle": "Nosa<PERSON>ms", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON><PERSON> no<PERSON>", "PE.Controllers.Main.txtSldLtTTwoColTx": "Divu kolonnu teksts", "PE.Controllers.Main.txtSldLtTTwoObj": "<PERSON>vi objekti", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Divi objekti un objekts", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Divi objekti un teksts", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Divi objekti virs teksta", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Divi teksts un divi objekti", "PE.Controllers.Main.txtSldLtTTx": "Teksts", "PE.Controllers.Main.txtSldLtTTxAndChart": "Teksts un diagramma", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Teksts un klipkopa", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Teksts un multivide", "PE.Controllers.Main.txtSldLtTTxAndObj": "Teksts un objekts", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Teksts un divi objekti", "PE.Controllers.Main.txtSldLtTTxOverObj": "Teksts virs objekta", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> virsrak<PERSON> un teksts", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> virsraksts un teksts virs diagrammas", "PE.Controllers.Main.txtSldLtTVertTx": "<PERSON>ert<PERSON><PERSON><PERSON> teks<PERSON>", "PE.Controllers.Main.txtSlideNumber": "<PERSON><PERSON><PERSON> numurs", "PE.Controllers.Main.txtSlideSubtitle": "Slaida <PERSON>š<PERSON>auku<PERSON>", "PE.Controllers.Main.txtSlideText": "<PERSON><PERSON><PERSON> te<PERSON>", "PE.Controllers.Main.txtSlideTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtStarsRibbons": "Zvaigznes un lentes", "PE.Controllers.Main.txtStart": "Start: ${0}s", "PE.Controllers.Main.txtStop": "Stop", "PE.Controllers.Main.txtTheme_basic": "Pamata", "PE.Controllers.Main.txtTheme_blank": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_classic": "Klasiskais", "PE.Controllers.Main.txtTheme_corner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_dotted": "Punk<PERSON>ē<PERSON>", "PE.Controllers.Main.txtTheme_green": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green_leaf": "<PERSON><PERSON><PERSON><PERSON>a", "PE.Controllers.Main.txtTheme_lines": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office_theme": "Office tēma", "PE.Controllers.Main.txtTheme_official": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_pixel": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtXAxis": "X ass", "PE.Controllers.Main.txtYAxis": "Y ass", "PE.Controllers.Main.txtZoom": "Zoom", "PE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "PE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON> p<PERSON>kprogramma nav atbalstīta.", "PE.Controllers.Main.uploadImageExtMessage": "Nezinā<PERSON> attēla formāts.", "PE.Controllers.Main.uploadImageFileCountMessage": "Nav aug<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "PE.Controllers.Main.uploadImageSizeMessage": "Attēls ir pār<PERSON>k liels. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> izmērs ir 25 MB.", "PE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>...", "PE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.waitText": "Uzgaidiet...", "PE.Controllers.Main.warnBrowserIE9": "Programmai ir zemas iespējas IE9. Izmantojiet IE10 vai jaunāku versiju", "PE.Controllers.Main.warnBrowserZoom": "Jūsu pārlūkprogrammas pašreizējais tālummaiņas iestatījums netiek pilnībā atbalstīts. Atiestatiet uz noklusējuma tā<PERSON>, nospiežot Ctrl+0.", "PE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "PE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "PE.Controllers.Main.warnLicenseExceeded": "<PERSON>ūs sa<PERSON>z<PERSON>t vienlaicīgu savienojumu ierobežojumu ar %1 redaktoriem. Šis dokuments tiks atvērts tikai apskatei.<br>Sazin<PERSON>ies ar <PERSON>u, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "PE.Controllers.Main.warnLicenseExp": "<PERSON><PERSON><PERSON> licencei ir beid<PERSON> term<PERSON>.<br><PERSON><PERSON><PERSON><PERSON>, atjauniniet savu licenci un pārlādējiet lapu.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Licences derī<PERSON>a <PERSON>.<br>Jums nav piekļuves dokumentu rediģēšanas funkcionalitātei.<br>Sazinieties ar savu administratoru.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Licence ir jā<PERSON><PERSON><PERSON>.<br>Jums ir ierobežota piekļuve dokumentu rediģēšanas funkcionalitātei.<br>Sazinieties ar savu <PERSON>u, lai ieg<PERSON>tu pilnu piek<PERSON>uvi", "PE.Controllers.Main.warnLicenseUsersExceeded": "Jūs sasniedzāt %1 redaktoru lietotāju ierobežojumu. Sazinieties ar savu <PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "PE.Controllers.Main.warnNoLicense": "<PERSON>ūs sas<PERSON>z<PERSON>t vienlaicīgu savienojumu ierobežojumu ar %1 redaktoriem. Šis dokuments tiks atvērts tikai apskatei.<br>Sazinieties ar %1 p<PERSON><PERSON><PERSON><PERSON> koman<PERSON>, lai uzzin<PERSON>tu personīgos jau<PERSON> noteikumus.", "PE.Controllers.Main.warnNoLicenseUsers": "Jūs sasniedzāt %1 redaktoru lietotāju ierobežojumu. Sazinieties ar %1 p<PERSON><PERSON><PERSON><PERSON> komandu, lai uzzin<PERSON>tu person<PERSON><PERSON> j<PERSON> noteikum<PERSON>.", "PE.Controllers.Main.warnProcessRightsChange": "<PERSON><PERSON> ir liegtas <PERSON> rediģēt failu.", "PE.Controllers.Print.txtPrintRangeInvalid": "Nederīgs drukas diapazons", "PE.Controllers.Print.txtPrintRangeSingleRange": "Ievadiet viena slaida numuru vai vienu slaidu diapazonu (piemēram, 5-12). Vai arī varat izdrukāt PDF formātā.", "PE.Controllers.Search.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Search.textNoTextFound": "<PERSON><PERSON><PERSON> meklētos datus nevarēja atrast. Pielāgojiet meklēšanas opcijas.", "PE.Controllers.Search.textReplaceSkipped": "<PERSON>r ve<PERSON>ta <PERSON>. <PERSON><PERSON> {0} gadījumi.", "PE.Controllers.Search.textReplaceSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir veikta. {0} gadīju<PERSON> (-i) ir aizst<PERSON>ts (-i)", "PE.Controllers.Search.warnReplaceString": "{0} ir ne<PERSON><PERSON><PERSON> speci<PERSON><PERSON><PERSON> r<PERSON><PERSON> la<PERSON> ar <PERSON>.", "PE.Controllers.Statusbar.textDisconnect": "<b>Savienoju<PERSON> ir <PERSON></b><br>Mēģina izveidot savienojumu. Pārbaudiet savienojuma iestatījumus.", "PE.Controllers.Statusbar.zoomText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "<PERSON><PERSON><PERSON>, kuru vēlaties saglabāt, pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ierīcē nav pieejams.<br><PERSON><PERSON><PERSON> stils tiks parādīts, i<PERSON><PERSON><PERSON>t vienu no sistēmas fontiem, saglab<PERSON><PERSON>s fonts tiks izmantots, kad tas būs pieejams.<br><PERSON>ai vēlaties turpināt?", "PE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "PE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "PE.Controllers.Toolbar.helpTabDesign": "Apply themes, change color schemes and slide size from the newly added Design tab.", "PE.Controllers.Toolbar.helpTabDesignHeader": "Design tab", "PE.Controllers.Toolbar.textAccent": "Uzsvari", "PE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textFontSizeErr": "Ievadītā vērtība ir nepareiza.<br>Ievadiet skaitlisku vērtību no 1 līdz 300", "PE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textFunction": "Funkcijas", "PE.Controllers.Toolbar.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textIntegral": "<PERSON>teg<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textLargeOperator": "Lielie operatori", "PE.Controllers.Toolbar.textLimitAndLog": "Robežas un logaritmi", "PE.Controllers.Toolbar.textMatrix": "Matricas", "PE.Controllers.Toolbar.textOperator": "Operatori", "PE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "Simboli", "PE.Controllers.Toolbar.textWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON>z<PERSON>var<PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Bultiņa pa labi un kreisi augšā", "PE.Controllers.Toolbar.txtAccent_ArrowL": "<PERSON>ultiņa augšā pa kreisi", "PE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON><PERSON>ņa augšā pa labi", "PE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_BarBot": "Apakš<PERSON>la", "PE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Formula rāmī (ar vietturi)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formula rāmī (piemērs)", "PE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Apak<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Apvie<PERSON>jo<PERSON><PERSON> iekava no augšas", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vektors A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC ar aug<PERSON><PERSON><PERSON> j<PERSON>", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y ar jos<PERSON> pāri", "PE.Controllers.Toolbar.txtAccent_DDDot": "Trīspunkte", "PE.Controllers.Toolbar.txtAccent_DDot": "Divpunkte", "PE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON> j<PERSON> p<PERSON>", "PE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Grupēt apakšējo <PERSON>", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Grupēt au<PERSON>š<PERSON>", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON><PERSON> augš<PERSON> pa kreisi", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON><PERSON> augš<PERSON> pa labi", "PE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "Izliektas pēdiņas", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Izliektas pēdiņas ar atdal<PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Izliektas pēdiņas ar diviem atdal<PERSON>em", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON> leņķa iekava", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Figūriekavas ar <PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (divi no<PERSON><PERSON><PERSON><PERSON><PERSON>)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (trī<PERSON>)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Grupēts objekts", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Grupēts objekts iekavās", "PE.Controllers.Toolbar.txtBracket_Custom_5": "G<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_6": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Binomiālais koe<PERSON> leņķiekavās", "PE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON> dubultā vertik<PERSON> j<PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON><PERSON> dub<PERSON> vertik<PERSON> j<PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Kreis<PERSON> stāvs", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "<PERSON><PERSON><PERSON> ar <PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Vietturis starp divām labajām kvadrātiekavām", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Apgrieztas kvadrātiekavas", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Vietturis starp divām kreisajām kvadrātiekavām", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtFractionDiagonal": "Diagon<PERSON><PERSON> dalījums", "PE.Controllers.Toolbar.txtFractionDifferential_1": "dx pār dy", "PE.Controllers.Toolbar.txtFractionDifferential_2": "lietot lielos burtus delta y nevis cap delta x", "PE.Controllers.Toolbar.txtFractionDifferential_3": "da<PERSON><PERSON><PERSON><PERSON> y pār da<PERSON> x", "PE.Controllers.Toolbar.txtFractionDifferential_4": "delta y virs delta x", "PE.Controllers.Toolbar.txtFractionHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionPi_2": "<PERSON> dalīts ar divi", "PE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Apgriez<PERSON>s k<PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Hiperboliskais apgrieztais kosīnuss", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Apgrieztais kotange<PERSON>s", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Hiperboliskais apgrieztais kotangenss", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Atpgriezta kosekansa funkci<PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperboliskais apgrieztais kosekanss", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Apgrieztais se<PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperboliskais apgrieztais sekanss", "PE.Controllers.Toolbar.txtFunction_1_Sin": "<PERSON>pg<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Hiperboliskais apgrieztais sīnuss", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Apgriez<PERSON><PERSON> tan<PERSON>s", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Hiperboliskais apgrieztais tangenss", "PE.Controllers.Toolbar.txtFunction_Cos": "Kosinusa funkcija", "PE.Controllers.Toolbar.txtFunction_Cosh": "Hiperboliskais kosīnuss", "PE.Controllers.Toolbar.txtFunction_Cot": "Kotangensa funkcija", "PE.Controllers.Toolbar.txtFunction_Coth": "Hiperboliskais kotangenss", "PE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Csch": "Hiperboliskā kosekanse", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sin θ", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Tangensa formula", "PE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sech": "Hiperboliskais sekanss", "PE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sinh": "Hiperboliskais sīnuss", "PE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Tanh": "Hiperboliskais tangenss", "PE.Controllers.Toolbar.txtIntegral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Diferenci<PERSON><PERSON> teta", "PE.Controllers.Toolbar.txtIntegral_dx": "Diferenciā<PERSON> x", "PE.Controllers.Toolbar.txtIntegral_dy": "Diferen<PERSON><PERSON><PERSON> y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integrālis ar sagrupētiem ierobežojumiem", "PE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON> integr<PERSON>", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Dubultais integrālis ar grupētiem ierobežojumiem", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Dubultais integrālis ar ierobežojumiem", "PE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Kontūrintegrālis ar grupētiem ierobežojumiem", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON> integr<PERSON> ar grupētiem ierobežojumiem", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON> in<PERSON> ar ierobežojumiem", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "<PERSON><PERSON><PERSON><PERSON> integr<PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Apjoma integrālis ar grupētiem limitiem", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "<PERSON><PERSON><PERSON><PERSON> integr<PERSON>", "PE.Controllers.Toolbar.txtIntegralSubSup": "Integrālis ar limit<PERSON>m", "PE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> integrā<PERSON> ar grupētiem limitiem", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> integr<PERSON> ar limit<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Loģiski un", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Loģiski un ar apakšindeksa apakšējo robežu", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Loģiski un ar ierobežojumiem", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Loģiski un ar apakšindeksa apakšējo robežu", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Loģiski un ar apakšraksta/augšraksta ierobežojumiem", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Kopražojums", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Kopražojums ar apakšējo ierobežojumu", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Kopražojums ar ierobežojumiem", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Kopražojums ar apakšindeksa ierobežojumu", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Kopražojums ar apakšindeksa/virsraksta ierobežojumiem", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summēšana virs k no n izvēlieties k", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summēšana no i vienāda ar nulli līdz n", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> divus in<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkta pie<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Apvienības piemē<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Loģiski vai", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Loģiski vai ar apa<PERSON><PERSON><PERSON><PERSON>u", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Loģiski vai ar ierobežojumiem", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Loģiski vai ar apakšraksta apakšējo robežu", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Loģiski vai ar apa<PERSON>š<PERSON>/augšraksta ierobežojumiem", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar a<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON>ā<PERSON> ar ierobežojumie<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON><PERSON><PERSON>ā<PERSON> ar apakš<PERSON> apakšējo <PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Krustošanās ar a<PERSON>/augšraksta ierobežojumiem", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkts", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkts ar a<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkts ar ierobežojumiem", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkts ar apakšraksta apakšējo robežu", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkts ar apakšindeksa/augšraksts ierobežojumiem", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar a<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summēšana ar ierobežojumiem", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summēšana ar apakš<PERSON> apakš<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summēšana ar a<PERSON>kšindek<PERSON>/virsraksta ierobežojumiem", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Apvienība", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Apvienība ar apa<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Apvienība ar limit<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Savienība ar apakšraksta apakšējo <PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Savienība ar a<PERSON>kšindeksa/virsraksta ierobežojumiem", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Ierobežot pie<PERSON>ēru", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Limits", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Nat<PERSON><PERSON><PERSON>s logaritms", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logaritms", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritms", "PE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimums", "PE.Controllers.Toolbar.txtMatrix_1_2": "Tukša matrica 1x2", "PE.Controllers.Toolbar.txtMatrix_1_3": "<PERSON>kša matrica 1x3", "PE.Controllers.Toolbar.txtMatrix_2_1": "<PERSON><PERSON>ša matrica 2x1", "PE.Controllers.Toolbar.txtMatrix_2_2": "<PERSON>kša matrica 2x2", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "PE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON><PERSON><PERSON> matrica 2x3", "PE.Controllers.Toolbar.txtMatrix_3_1": "<PERSON>kša matrica 3x1", "PE.Controllers.Toolbar.txtMatrix_3_2": "<PERSON>kša matrica 3x2", "PE.Controllers.Toolbar.txtMatrix_3_3": "<PERSON>kša matrica 3x3", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Pamatlīnijas punkti", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Vid<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON>tā matrica iekavās", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "Retā matrica", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "Identitātes matrica 2 x 2", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2 identitātes matrica ar tukšām ārpus diagonālām šūnām", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "Identitātes matrica 3 x 3", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 identitātes matrica ar tukšām ārpus diagonālām šūnām", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Bultiņa pa labi un kreisi apakšā", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Bultiņa pa labi un kreisi augšā", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Bultiņa apa<PERSON>š<PERSON> pa kreisi", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "<PERSON>ultiņa augšā pa kreisi", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Bultiņa apa<PERSON>š<PERSON> pa kreisi", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON><PERSON>ņa augšā pa labi", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Divpunktu vienāds", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Izeja", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Izejas delta", "PE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON><PERSON><PERSON> pēc defin<PERSON>", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta vienāda ar", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Dubultbultiņa pa labi un kreisi apakšā", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Dubultbultiņa pa labi un kreisi augšā", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Bultiņa apa<PERSON>š<PERSON> pa kreisi", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "<PERSON>ultiņa augšā pa kreisi", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Bultiņa apa<PERSON>š<PERSON> pa kreisi", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON><PERSON>ņa augšā pa labi", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Vienāds vienāds", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus vienāds", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar", "PE.Controllers.Toolbar.txtRadicalCustom_1": "K<PERSON><PERSON>ātiskās formulas labā puse", "PE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ne no kvadrāta plus b kva<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar pak<PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar <PERSON>", "PE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_1": "x apakšindekss y kvadrātā", "PE.Controllers.Toolbar.txtScriptCustom_2": "no e līdz mīnus i omega t", "PE.Controllers.Toolbar.txtScriptCustom_3": "x kvadrātā", "PE.Controllers.Toolbar.txtScriptCustom_4": "Y kreisais augšraksts un kreisais apakšraksts viens", "PE.Controllers.Toolbar.txtScriptSub": "Apakšraksts", "PE.Controllers.Toolbar.txtScriptSubSup": "Apakšraksts-augšraksts", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Kreisais apakšraksts-augšraksts", "PE.Controllers.Toolbar.txtScriptSup": "Augšraksts", "PE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_additional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> ar", "PE.Controllers.Toolbar.txtSymbol_ast": "Operators-zvaigznīte", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Bet", "PE.Controllers.Toolbar.txtSymbol_bullet": "Aizzīmes operators", "PE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cdots": "Viduslī<PERSON>jas horizontālā elipse", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "Ķīn.", "PE.Controllers.Toolbar.txtSymbol_cong": "Aptuveni vienāds ar", "PE.Controllers.Toolbar.txtSymbol_cup": "Apvienība", "PE.Controllers.Toolbar.txtSymbol_ddots": "Diagon<PERSON><PERSON><PERSON> elipse lejā pa labi", "PE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>", "PE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON> r<PERSON>", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilons", "PE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_equiv": "Identisks ar", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_factorial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_forall": "Visiem", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "Lie<PERSON>ā<PERSON> par vai vienāds ar", "PE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON> par", "PE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON><PERSON> nekā", "PE.Controllers.Toolbar.txtSymbol_in": "Elements no", "PE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_infinity": "Bezgalība", "PE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Bultiņa pa kreisi", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Bultiņa pa labi un kreisi", "PE.Controllers.Toolbar.txtSymbol_leq": "<PERSON><PERSON>āk nekā vai vienāds ar", "PE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON><PERSON><PERSON> nek<PERSON>", "PE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON><PERSON> par", "PE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_mp": "Mīnuss pluss", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "Nav vien<PERSON>ds ar", "PE.Controllers.Toolbar.txtSymbol_ni": "Satur kā dalībnieks", "PE.Controllers.Toolbar.txtSymbol_not": "Negatī<PERSON><PERSON> z<PERSON>me", "PE.Controllers.Toolbar.txtSymbol_notexists": "Nepastāv", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_percent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_phi": "Fi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Plus", "PE.Controllers.Toolbar.txtSymbol_pm": "Plus, mīnus", "PE.Controllers.Toolbar.txtSymbol_propto": "<PERSON>por<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> sakne", "PE.Controllers.Toolbar.txtSymbol_qed": "<PERSON> <PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_rddots": "Diagonā<PERSON>ā elipse augšā pa labi", "PE.Controllers.Toolbar.txtSymbol_rho": "Ro", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "Bultiņa pa labi", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilona variants", "PE.Controllers.Toolbar.txtSymbol_varphi": "Fī variants", "PE.Controllers.Toolbar.txtSymbol_varpi": "Pi variants", "PE.Controllers.Toolbar.txtSymbol_varrho": "Ro variants", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma variants", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Theta variants", "PE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> elipse", "PE.Controllers.Toolbar.txtSymbol_xsi": "Ksi", "PE.Controllers.Toolbar.txtSymbol_zeta": "<PERSON><PERSON><PERSON>", "PE.Controllers.Viewport.textFitPage": "Saskaņot ar s<PERSON>u", "PE.Controllers.Viewport.textFitWidth": "Saskaņot ar platumu", "PE.Views.Animation.str0_5": "0,5 s (<PERSON><PERSON><PERSON>)", "PE.Views.Animation.str1": "1 s (ātrs)", "PE.Views.Animation.str2": "2 s (vidējs)", "PE.Views.Animation.str20": "20 s (ārkā<PERSON><PERSON><PERSON> lēns)", "PE.Views.Animation.str3": "3 s (lēns)", "PE.Views.Animation.str5": "5 s (<PERSON><PERSON><PERSON> lēns)", "PE.Views.Animation.strDelay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.strDuration": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.strRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.strRewind": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.strStart": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.strTrigger": "Triger<PERSON>", "PE.Views.Animation.textAutoPreview": "Automātiskais priekšskatījums", "PE.Views.Animation.textMoreEffects": "<PERSON><PERSON><PERSON><PERSON><PERSON> vair<PERSON>k efektu", "PE.Views.Animation.textMoveEarlier": "Pārvietot agrāk", "PE.Views.Animation.textMoveLater": "Pārvietot vēlāk", "PE.Views.Animation.textMultiple": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textNone": "Neviens", "PE.Views.Animation.textNoRepeat": "(neviens)", "PE.Views.Animation.textOnClickOf": "Noklikšķinot uz", "PE.Views.Animation.textOnClickSequence": "Klikšķa secība", "PE.Views.Animation.textStartAfterPrevious": "Pēc iepriekšējā", "PE.Views.Animation.textStartOnClick": "Pēc klikšķa", "PE.Views.Animation.textStartWithPrevious": "<PERSON><PERSON> <PERSON>", "PE.Views.Animation.textUntilEndOfSlide": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>a be<PERSON>m", "PE.Views.Animation.textUntilNextClick": "<PERSON><PERSON><PERSON><PERSON> klikšķim", "PE.Views.Animation.txtAddEffect": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.txtAnimationPane": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "PE.Views.Animation.txtParameters": "Parametri", "PE.Views.Animation.txtPreview": "Priekšskatījums", "PE.Views.Animation.txtSec": "s", "PE.Views.AnimationDialog.textPreviewEffect": "Priekšskatījuma efekts", "PE.Views.AnimationDialog.textTitle": "Vairāk efektu", "PE.Views.ChartSettings.text3dDepth": "Dziļums (% no bāzes)", "PE.Views.ChartSettings.text3dHeight": "Augstums (% no bāzes)", "PE.Views.ChartSettings.text3dRotation": "3D rotācija", "PE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "PE.Views.ChartSettings.textAutoscale": "Automātiska <PERSON>", "PE.Views.ChartSettings.textChartType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> diagrammas veidu", "PE.Views.ChartSettings.textDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textDown": "Uz leju", "PE.Views.ChartSettings.textEditData": "Rediģēt datus", "PE.Views.ChartSettings.textHeight": "Augstums", "PE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textLeft": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textNarrow": "Šaurs redzes lauks", "PE.Views.ChartSettings.textPerspective": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textRight": "Labais", "PE.Views.ChartSettings.textRightAngle": "Taisnā leņķa asis", "PE.Views.ChartSettings.textSize": "Izmērs", "PE.Views.ChartSettings.textStyle": "Stils", "PE.Views.ChartSettings.textUp": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textWiden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> redzes lauku", "PE.Views.ChartSettings.textWidth": "Platums", "PE.Views.ChartSettings.textX": "X rotācija", "PE.Views.ChartSettings.textY": "<PERSON>", "PE.Views.ChartSettingsAdvanced.textAlt": "Alternatīvs teksts", "PE.Views.ChartSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAltTip": "Vizuālās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, fig<PERSON><PERSON><PERSON>, diagramm<PERSON> vai tabulā.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "PE.Views.ChartSettingsAdvanced.textCenter": "Centrā", "PE.Views.ChartSettingsAdvanced.textChartName": "Chart name", "PE.Views.ChartSettingsAdvanced.textFrom": "No", "PE.Views.ChartSettingsAdvanced.textGeneral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textHeight": "Augstums", "PE.Views.ChartSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textPlacement": "Novietojums", "PE.Views.ChartSettingsAdvanced.textPosition": "Pozīcija", "PE.Views.ChartSettingsAdvanced.textSize": "Izmērs", "PE.Views.ChartSettingsAdvanced.textTitle": "Diagramma <PERSON> <PERSON><PERSON><PERSON><PERSON> i<PERSON>", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "Augš<PERSON><PERSON><PERSON> kreis<PERSON> stūris", "PE.Views.ChartSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textWidth": "Platums", "PE.Views.DateTimeDialog.confirmDefault": "<PERSON>estat<PERSON>t no<PERSON> formātu {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Iestatīt kā noklusējuma", "PE.Views.DateTimeDialog.textFormat": "<PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.textLang": "Valoda", "PE.Views.DateTimeDialog.textUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.txtTitle": "Datums & Laiks", "PE.Views.DocumentHolder.aboveText": "Virs", "PE.Views.DocumentHolder.addCommentText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.advancedChartText": "Diagrammas papildu i<PERSON>īju<PERSON>", "PE.Views.DocumentHolder.advancedEquationText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.advancedImageText": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON> i<PERSON>", "PE.Views.DocumentHolder.advancedParagraphText": "Rindkopas papildu iestatījumi", "PE.Views.DocumentHolder.advancedShapeText": "Forma – papildu i<PERSON>īju<PERSON>", "PE.Views.DocumentHolder.advancedTableText": "Tabulas Papildu Iestatījumi", "PE.Views.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.allLinearText": "Visi – lineāri", "PE.Views.DocumentHolder.allProfText": "Visi – profesionāli", "PE.Views.DocumentHolder.belowText": "Zem", "PE.Views.DocumentHolder.cellAlignText": "<PERSON><PERSON><PERSON> vertik<PERSON>", "PE.Views.DocumentHolder.cellText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.centerText": "Centrā", "PE.Views.DocumentHolder.columnText": "Kolonna", "PE.Views.DocumentHolder.currLinearText": "Pašreizējais – lineāri", "PE.Views.DocumentHolder.currProfText": "Pašreizējais – profesionāls", "PE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> kolonnu", "PE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON><PERSON> rindu", "PE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON><PERSON> tabulu", "PE.Views.DocumentHolder.deleteText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.direct270Text": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> au<PERSON>", "PE.Views.DocumentHolder.direct90Text": "Rot<PERSON><PERSON> te<PERSON> le<PERSON>", "PE.Views.DocumentHolder.directHText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.directionText": "Teksta virziens", "PE.Views.DocumentHolder.editChartText": "Rediģēt datus", "PE.Views.DocumentHolder.editHyperlinkText": "Rediģēt hip<PERSON>aiti", "PE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "PE.Views.DocumentHolder.hyperlinkText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.ignoreAllSpellText": "Igno<PERSON><PERSON>t visu", "PE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnLeftText": "Kolonna pa kreisi", "PE.Views.DocumentHolder.insertColumnRightText": "Kolonna pa labi", "PE.Views.DocumentHolder.insertColumnText": "Ievietot kolonnu", "PE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> augst<PERSON>k", "PE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowText": "Ievietot rindu", "PE.Views.DocumentHolder.insertText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.langText": "<PERSON><PERSON><PERSON> valo<PERSON>", "PE.Views.DocumentHolder.latexText": "LaTeX", "PE.Views.DocumentHolder.leftText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.loadSpellText": "<PERSON><PERSON><PERSON><PERSON><PERSON> variantus...", "PE.Views.DocumentHolder.mergeCellsText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.mniCustomTable": "Ievietot pielāgot<PERSON> tabulu", "PE.Views.DocumentHolder.moreText": "Vairāk variantu...", "PE.Views.DocumentHolder.noSpellVariantsText": "Nav variantu", "PE.Views.DocumentHolder.originalSizeText": "<PERSON>ak<PERSON><PERSON><PERSON> lie<PERSON>", "PE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON><PERSON> Hipersaiti", "PE.Views.DocumentHolder.rightText": "Labais", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "PE.Views.DocumentHolder.spellcheckText": "Pareizrakstī<PERSON>", "PE.Views.DocumentHolder.splitCellsText": "<PERSON><PERSON><PERSON><PERSON>...", "PE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tableText": "Tabula", "PE.Views.DocumentHolder.textAddHGuides": "<PERSON><PERSON><PERSON> c<PERSON>ļ<PERSON>i", "PE.Views.DocumentHolder.textAddVGuides": "<PERSON><PERSON><PERSON> ve<PERSON> c<PERSON>", "PE.Views.DocumentHolder.textArrangeBack": "Pārnest uz fonu", "PE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeForward": "Pārnest uz priekšu", "PE.Views.DocumentHolder.textArrangeFront": "Nest uz priekšplānu", "PE.Views.DocumentHolder.textClearGuides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCm": "cm", "PE.Views.DocumentHolder.textCopy": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCrop": "Apgriezt", "PE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFit": "Saskaņ<PERSON>", "PE.Views.DocumentHolder.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCut": "Izgriezt", "PE.Views.DocumentHolder.textDeleteGuide": "<PERSON><PERSON><PERSON><PERSON> ceļ<PERSON>i", "PE.Views.DocumentHolder.textDeleteLayout": "Delete Layout", "PE.Views.DocumentHolder.textDeleteMaster": "Delete Master", "PE.Views.DocumentHolder.textDistributeCols": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "PE.Views.DocumentHolder.textDistributeRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "PE.Views.DocumentHolder.textDuplicateLayout": "Duplicate Layout", "PE.Views.DocumentHolder.textDuplicateSlideMaster": "Duplicate Slide Master", "PE.Views.DocumentHolder.textEditObject": "Rediģēt objektu", "PE.Views.DocumentHolder.textEditPoints": "Rediģēt punktus", "PE.Views.DocumentHolder.textFlipH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textFlipV": "<PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>", "PE.Views.DocumentHolder.textFromFile": "No faila", "PE.Views.DocumentHolder.textFromStorage": "No glabātuves", "PE.Views.DocumentHolder.textFromUrl": "No URL", "PE.Views.DocumentHolder.textGridlines": "Režģlīnijas", "PE.Views.DocumentHolder.textGuides": "Ceļveži", "PE.Views.DocumentHolder.textInsertLayout": "Insert Layout", "PE.Views.DocumentHolder.textInsertSlideMaster": "Insert Slide Master", "PE.Views.DocumentHolder.textNextPage": "<PERSON><PERSON><PERSON><PERSON><PERSON> slaids", "PE.Views.DocumentHolder.textPaste": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textPrevPage": "Iepriekšējais slaids", "PE.Views.DocumentHolder.textRemove": "Remove", "PE.Views.DocumentHolder.textRenameLayout": "<PERSON><PERSON>out", "PE.Views.DocumentHolder.textRenameMaster": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textReplace": "Aizvietot attēlu", "PE.Views.DocumentHolder.textResetCrop": "Reset crop", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "Pagriezt par 90° pret<PERSON><PERSON> pulksteņrā<PERSON><PERSON><PERSON><PERSON><PERSON> virzienam", "PE.Views.DocumentHolder.textRotate90": "Pagriezt par 90° pulkste<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> virzienā", "PE.Views.DocumentHolder.textRulers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textSaveAsPicture": "Saglabāt kā attēlu", "PE.Views.DocumentHolder.textShapeAlignBottom": "Līdzināt pie apakšas", "PE.Views.DocumentHolder.textShapeAlignCenter": "L<PERSON><PERSON><PERSON>āt pa centru", "PE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa kreisi", "PE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz vidu", "PE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa labi", "PE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz augšu", "PE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "PE.Views.DocumentHolder.textShowGridlines": "<PERSON><PERSON><PERSON><PERSON><PERSON> režģlīnijas", "PE.Views.DocumentHolder.textShowGuides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textSlideSettings": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textSmartGuides": "Viedie ceļveži", "PE.Views.DocumentHolder.textSnapObjects": "<PERSON><PERSON><PERSON> objektu režģim", "PE.Views.DocumentHolder.textStartAfterPrevious": "Start After Previous", "PE.Views.DocumentHolder.textStartOnClick": "Start On Click", "PE.Views.DocumentHolder.textStartWithPrevious": "Start With Previous", "PE.Views.DocumentHolder.textUndo": "Atsaukt", "PE.Views.DocumentHolder.tipGuides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tipIsLocked": "Šo elementu šobrīd rediģē cits lietotājs.", "PE.Views.DocumentHolder.toDictionaryText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON> l<PERSON>", "PE.Views.DocumentHolder.txtAddLB": "<PERSON><PERSON>not līniju k<PERSON> apakšējā stūrī", "PE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON> k<PERSON>", "PE.Views.DocumentHolder.txtAddLT": "<PERSON><PERSON><PERSON> lī<PERSON>ju kreisajā augšējā stūrī", "PE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON> l<PERSON>", "PE.Views.DocumentHolder.txtAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAlignToChar": "Saskaņot ar simbolu", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtBackground": "Fons", "PE.Views.DocumentHolder.txtBorderProps": "Apmales parametri", "PE.Views.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtChangeLayout": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtChangeTheme": "<PERSON><PERSON><PERSON> tēmu", "PE.Views.DocumentHolder.txtColumnAlign": "Kolonnas izlīdzināšana", "PE.Views.DocumentHolder.txtDecreaseArg": "Samazināt argumenta izmēru", "PE.Views.DocumentHolder.txtDeleteArg": "Dzēst argumentu", "PE.Views.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON><PERSON> man<PERSON><PERSON><PERSON> at<PERSON><PERSON>", "PE.Views.DocumentHolder.txtDeleteChars": "<PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON><PERSON><PERSON> iet<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> un atdalītājus", "PE.Views.DocumentHolder.txtDeleteEq": "Dzēst vienādojumu", "PE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON><PERSON> simbolu", "PE.Views.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON><PERSON> radi<PERSON>", "PE.Views.DocumentHolder.txtDeleteSlide": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>u", "PE.Views.DocumentHolder.txtDistribHor": "<PERSON><PERSON><PERSON><PERSON><PERSON> pa <PERSON>", "PE.Views.DocumentHolder.txtDistribVert": "<PERSON><PERSON><PERSON><PERSON><PERSON> pa vertik<PERSON>li", "PE.Views.DocumentHolder.txtDuplicateSlide": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>u", "PE.Views.DocumentHolder.txtFractionLinear": "<PERSON>īt uz lineāru da<PERSON>", "PE.Views.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON><PERSON> uz <PERSON> da<PERSON>", "PE.Views.DocumentHolder.txtFractionStacked": "<PERSON><PERSON><PERSON> uz vert<PERSON>", "PE.Views.DocumentHolder.txtGroup": "Grupa", "PE.Views.DocumentHolder.txtGroupCharOver": "Rakstz. virs teksta", "PE.Views.DocumentHolder.txtGroupCharUnder": "Rakstz. zem teksta", "PE.Views.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHideBottomLimit": "Nerādīt a<PERSON>šējo ierobežojumu", "PE.Views.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHideLB": "Nerādīt kreiso apakšējo līniju", "PE.Views.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "PE.Views.DocumentHolder.txtHideLT": "Nerā<PERSON><PERSON>t k<PERSON>o augšējo līniju", "PE.Views.DocumentHolder.txtHideOpenBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHidePlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "PE.Views.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> labo <PERSON>", "PE.Views.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>", "PE.Views.DocumentHolder.txtHideTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>š<PERSON> ierobežojumu", "PE.Views.DocumentHolder.txtHideVer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>", "PE.Views.DocumentHolder.txtIncreaseArg": "Palielināt argumenta izmēru", "PE.Views.DocumentHolder.txtInsAudio": "Ievietot audio", "PE.Views.DocumentHolder.txtInsChart": "Ievietot grafiku", "PE.Views.DocumentHolder.txtInsertArgAfter": "Ievietot argumentu pēc", "PE.Views.DocumentHolder.txtInsertArgBefore": "Ievietot argumentu pirms", "PE.Views.DocumentHolder.txtInsertBreak": "Ieviet<PERSON> man<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtInsertEqAfter": "Ievietot vien<PERSON><PERSON><PERSON><PERSON> p<PERSON>c", "PE.Views.DocumentHolder.txtInsertEqBefore": "Ievietot vienā<PERSON><PERSON><PERSON> pirms", "PE.Views.DocumentHolder.txtInsImage": "Insert image from file", "PE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "PE.Views.DocumentHolder.txtInsSmartArt": "Ievietot SmartArt", "PE.Views.DocumentHolder.txtInsTable": "Ievietot tabulu", "PE.Views.DocumentHolder.txtInsVideo": "Ievietot video", "PE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON><PERSON> tikai te<PERSON>tu", "PE.Views.DocumentHolder.txtLimitChange": "<PERSON><PERSON><PERSON> v<PERSON>u", "PE.Views.DocumentHolder.txtLimitOver": "Ierobežot virs teksta", "PE.Views.DocumentHolder.txtLimitUnder": "Ierobežot zem teksta", "PE.Views.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON><PERSON><PERSON> argumenta augstumam", "PE.Views.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Pārvietot slaidu uz beigām", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Pārvietot slaidu uz sākumu", "PE.Views.DocumentHolder.txtNewSlide": "Jauns slaids", "PE.Views.DocumentHolder.txtOverbar": "Josla virs teksta", "PE.Views.DocumentHolder.txtPasteDestFormat": "Izmantot galamērķa dizainu", "PE.Views.DocumentHolder.txtPastePicture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Atstāt s<PERSON>kotnējo formatējumu", "PE.Views.DocumentHolder.txtPressLink": "Nospiediet {0} un noklikšķiniet saiti", "PE.Views.DocumentHolder.txtPreview": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtPrintSelection": "<PERSON><PERSON><PERSON> atlase", "PE.Views.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "PE.Views.DocumentHolder.txtRemLimit": "Noņemt limitu", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Noņemt diakritisko zīmi", "PE.Views.DocumentHolder.txtRemoveBar": "Noņ<PERSON><PERSON> j<PERSON>", "PE.Views.DocumentHolder.txtRemScripts": "<PERSON><PERSON><PERSON><PERSON> sk<PERSON>tus", "PE.Views.DocumentHolder.txtRemSubscript": "Noņemt apakšrakstu", "PE.Views.DocumentHolder.txtRemSuperscript": "Noņemt augšrakstu", "PE.Views.DocumentHolder.txtResetLayout": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>u", "PE.Views.DocumentHolder.txtScriptsAfter": "Skripti p<PERSON> te<PERSON>ta", "PE.Views.DocumentHolder.txtScriptsBefore": "Skripti pirms teksta", "PE.Views.DocumentHolder.txtSelectAll": "Izvēlēties visu", "PE.Views.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> ierobežojumu", "PE.Views.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtShowPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "PE.Views.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>", "PE.Views.DocumentHolder.txtSlide": "Slaids", "PE.Views.DocumentHolder.txtSlideHide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>u", "PE.Views.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtTop": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtUnderbar": "Josla zem teksta", "PE.Views.DocumentHolder.txtUngroup": "Atgrupēt", "PE.Views.DocumentHolder.txtWarnUrl": "Noklikšķinot uz šīs sa<PERSON> var kaitēt jūsu ierīcei un datiem.<br>Vai tiešām vēlaties turpināt?", "PE.Views.DocumentHolder.unicodeText": "Unikods", "PE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.goToSlideText": "Dot<PERSON> uz slaidu", "PE.Views.DocumentPreview.slideIndexText": "{0}. no {1} slaids", "PE.Views.DocumentPreview.txtClose": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "PE.Views.DocumentPreview.txtDraw": "Draw", "PE.Views.DocumentPreview.txtEndSlideshow": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtEraser": "Eraser", "PE.Views.DocumentPreview.txtEraseScreen": "Erase screen", "PE.Views.DocumentPreview.txtExitFullScreen": "Iziet no pilnekrāna", "PE.Views.DocumentPreview.txtFinalMessage": "Slaida priekšskatījuma beigas. Noklikšķiniet, lai izietu.", "PE.Views.DocumentPreview.txtFullScreen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtHighlighter": "Highlighter", "PE.Views.DocumentPreview.txtInkColor": "Ink color", "PE.Views.DocumentPreview.txtNext": "<PERSON><PERSON><PERSON><PERSON><PERSON> slaids", "PE.Views.DocumentPreview.txtPageNumInvalid": "<PERSON><PERSON><PERSON><PERSON> slaida numuru", "PE.Views.DocumentPreview.txtPause": "<PERSON><PERSON><PERSON><PERSON> prezentā<PERSON>", "PE.Views.DocumentPreview.txtPen": "Pen", "PE.Views.DocumentPreview.txtPlay": "Sākt prezentāciju", "PE.Views.DocumentPreview.txtPrev": "Iepriekšējais slaids", "PE.Views.DocumentPreview.txtReset": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.ariaFileMenu": "File menu", "PE.Views.FileMenu.btnAboutCaption": "Par", "PE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON><PERSON><PERSON> faila atra<PERSON> vietu", "PE.Views.FileMenu.btnCloseEditor": "Close File", "PE.Views.FileMenu.btnCloseMenuCaption": "Aizvērt izvēlni", "PE.Views.FileMenu.btnCreateNewCaption": "<PERSON>zve<PERSON><PERSON> jaunu", "PE.Views.FileMenu.btnDownloadCaption": "Lejupielā<PERSON><PERSON><PERSON> kā", "PE.Views.FileMenu.btnExitCaption": "Aizvērt", "PE.Views.FileMenu.btnFileOpenCaption": "Atv<PERSON><PERSON>", "PE.Views.FileMenu.btnHelpCaption": "Palīdzī<PERSON>", "PE.Views.FileMenu.btnHistoryCaption": "Versiju vēsture", "PE.Views.FileMenu.btnInfoCaption": "Informācija par prezentāciju", "PE.Views.FileMenu.btnPrintCaption": "Printēt", "PE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON><PERSON> pēd<PERSON>", "PE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnReturnCaption": "Atpakaļ uz prezentāciju", "PE.Views.FileMenu.btnRightsCaption": "Piek<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnSaveAsCaption": "Sag<PERSON><PERSON><PERSON>t kā...", "PE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Saglabāt kopiju kā", "PE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "PE.Views.FileMenu.btnToEditCaption": "Rediģēt prezentāciju", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON>zve<PERSON><PERSON> jaunu", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Pievienot autoru", "PE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Programma", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autors", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Izveidots", "PE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Pēdējo reizi modificēja", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> reizi modificēts", "PE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Īpašnieks", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Novietojums", "PE.Views.FileMenuPanels.DocumentInfo.txtPresentationInfo": "Informācija par prezentāciju", "PE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "PE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON> kuriem i<PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tagi", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Nosa<PERSON>ms", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "PE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Piek<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON> kuriem i<PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON><PERSON> <PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Prezentācijai ir pievienoti derīgi paraksti.<br>Prezen<PERSON><PERSON><PERSON><PERSON> nevar rediģēt.", "PE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prezentācijas integritāti, <PERSON><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Rediģēt prezentāciju", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Rediģēšana no prezentācijas noņems parakstus.<br> Vai tiešām vēlaties turpināt?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "<PERSON><PERSON> a<PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.txtProtectPresentation": "<PERSON><PERSON><PERSON><PERSON><PERSON> š<PERSON> prezentā<PERSON> ar paroli", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Prezentācijai ir pievienoti derīgi paraksti. Prezentāciju nevar rediģēt.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Da<PERSON><PERSON> no prezentācijas digitālajiem parakstiem ir nederīgi vai tos nevar pārbaudīt. Prezentāciju nevar rediģēt.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON><PERSON><PERSON><PERSON> para<PERSON>", "PE.Views.FileMenuPanels.Settings.okButtonText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Ko<PERSON>īgā<PERSON> rediģēšanas režīms", "PE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strFontRender": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignorēt vārdus ar LIELAJIEM BURTIEM", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "<PERSON><PERSON><PERSON><PERSON><PERSON> vārdus ar cipariem", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strPasteButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON> opciju pogu, kad saturs ir ielīm<PERSON>ts", "PE.Views.FileMenuPanels.Settings.strRTLSupport": "RTL interface", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "R<PERSON><PERSON><PERSON>t citu lietotāju veiktās i<PERSON>", "PE.Views.FileMenuPanels.Settings.strStrict": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strTabStyle": "Tab style", "PE.Views.FileMenuPanels.Settings.strTheme": "Interfeisa tēma", "PE.Views.FileMenuPanels.Settings.strUnit": "Mērvienība", "PE.Views.FileMenuPanels.Settings.strZoom": "Noklusēju<PERSON> tā<PERSON>iņas vērtība", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Ik pēc 10 minūtēm", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Ik pēc 30 minūtēm", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Ik pēc 5 minūtēm", "PE.Views.FileMenuPanels.Settings.text60Minutes": "<PERSON><PERSON> stundu", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ce<PERSON>", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Automātiskā <PERSON>", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Automātisk<PERSON> sagla<PERSON>", "PE.Views.FileMenuPanels.Settings.textDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textFill": "Fill", "PE.Views.FileMenuPanels.Settings.textForceSave": "Saglabā vidējā līmeņa versijas", "PE.Views.FileMenuPanels.Settings.textLine": "Line", "PE.Views.FileMenuPanels.Settings.textMinute": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtAll": "<PERSON><PERSON><PERSON><PERSON> visu", "PE.Views.FileMenuPanels.Settings.txtAppearance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Automā<PERSON><PERSON><PERSON><PERSON> opcijas...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Noklusējuma kešatmiņas režīms", "PE.Views.FileMenuPanels.Settings.txtCm": "Centimetrs", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Sad<PERSON>bī<PERSON>", "PE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "Customize quick access", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Rediģēšana un saglabāšana", "PE.Views.FileMenuPanels.Settings.txtFastTip": "<PERSON><PERSON><PERSON><PERSON> koprediģēšana. Visas izmaiņas tiek saglabātas automātiski", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Saskaņot ar s<PERSON>u", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Saskaņot ar platumu", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtInch": "Col<PERSON>", "PE.Views.FileMenuPanels.Settings.txtLast": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtLastUsed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lie<PERSON>", "PE.Views.FileMenuPanels.Settings.txtMac": "kā OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "Dzimtais", "PE.Views.FileMenuPanels.Settings.txtProofing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtPt": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtQuickPrint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON> pogu redaktora galvenē", "PE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "Dokuments tiks drukāts ar pēdējo atlasīto vai noklusējuma printeri", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Iespējo<PERSON> visu", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Iespējot visus makro bez paziņojuma", "PE.Views.FileMenuPanels.Settings.txtScreenReader": "Turn on screen reader support", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Notiek pareizrakstības p<PERSON>aude", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Atspējot visu", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Atspējot visus makro bez paziņojuma", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"Saglabā<PERSON>\" tausti<PERSON><PERSON>, lai sinhronizētu sevis un citu veiktās izmaiņas", "PE.Views.FileMenuPanels.Settings.txtTabBack": "Use toolbar color as tabs background", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Izmantojiet taustiņu Alt, lai pārvietotos lietotāja interfeisā, izman<PERSON>jot tastatūru", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "<PERSON>zmantojiet op<PERSON>ju <PERSON>, lai pārvie<PERSON>tos lietotāja interfeisā, i<PERSON><PERSON><PERSON><PERSON> ta<PERSON>t<PERSON>ru", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Atspējot visus makro ar p<PERSON>", "PE.Views.FileMenuPanels.Settings.txtWin": "kā Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "Darbvieta", "PE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Lejupielā<PERSON><PERSON><PERSON> kā", "PE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Saglabāt kopiju kā", "PE.Views.GridSettings.textCm": "cm", "PE.Views.GridSettings.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.GridSettings.textSpacing": "Atstarpe", "PE.Views.GridSettings.textTitle": "Režģa iestatījumi", "PE.Views.HeaderFooterDialog.applyAllText": "Pie<PERSON><PERSON><PERSON> visiem", "PE.Views.HeaderFooterDialog.applyText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.diffLanguage": "<PERSON><PERSON><PERSON> nevarat izman<PERSON>t datuma formātu, kas atšķiras no slaida šablona valodas.<br><PERSON>tu <PERSON>, noklikšķiniet uz '<PERSON>tot' visiem, nevis uz 'Lietot'.", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textDateTime": "Datums un laiks", "PE.Views.HeaderFooterDialog.textFixed": "<PERSON><PERSON>ēts", "PE.Views.HeaderFooterDialog.textFormat": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textHFTitle": "G<PERSON><PERSON><PERSON>/kājenes i<PERSON>ī<PERSON>", "PE.Views.HeaderFooterDialog.textLang": "Valoda", "PE.Views.HeaderFooterDialog.textNotes": "Notes and handouts", "PE.Views.HeaderFooterDialog.textNotTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> s<PERSON>", "PE.Views.HeaderFooterDialog.textPageNum": "Lapas numuri", "PE.Views.HeaderFooterDialog.textPreview": "Priekšskatījums", "PE.Views.HeaderFooterDialog.textSlide": "Slaids", "PE.Views.HeaderFooterDialog.textSlideNum": "<PERSON><PERSON><PERSON> numurs", "PE.Views.HeaderFooterDialog.textUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.txtFooter": "Zemteksta piezī<PERSON>", "PE.Views.HeaderFooterDialog.txtHeader": "G<PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON><PERSON><PERSON> ar", "PE.Views.HyperlinkSettingsDialog.textDefault": "Atlasītais teksta fragments", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Ievadiet parakstu šeit", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Ievadiet saiti šeit", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Ievadiet rīka padomu <PERSON>eit", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Slaids <PERSON><PERSON><PERSON> prezent<PERSON>j<PERSON>", "PE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "PE.Views.HyperlinkSettingsDialog.textSlides": "<PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.textTipText": "<PERSON><PERSON><PERSON><PERSON><PERSON> padomu teksts", "PE.Views.HyperlinkSettingsDialog.textTitle": "Hipersait<PERSON>", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "PE.Views.HyperlinkSettingsDialog.txtFirst": "<PERSON><PERSON><PERSON> slaids", "PE.Views.HyperlinkSettingsDialog.txtLast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> slaids", "PE.Views.HyperlinkSettingsDialog.txtNext": "<PERSON><PERSON><PERSON><PERSON><PERSON> slaids", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON><PERSON> la<PERSON> ir jāb<PERSON>t vietrāža URL formātā \"http://www.example.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Iepriekšējais slaids", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Lauks ir ierobežots līdz 2083 rakstzīmēm", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Slaids", "PE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "PE.Views.ImageSettings.strTransparency": "Opacity", "PE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "PE.Views.ImageSettings.textCrop": "Apgriezt", "PE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropFit": "Saskaņ<PERSON>", "PE.Views.ImageSettings.textCropToShape": "Apgriezt pēc formas", "PE.Views.ImageSettings.textEdit": "Rediģēt", "PE.Views.ImageSettings.textEditObject": "Rediģēt objektu", "PE.Views.ImageSettings.textFitSlide": "Saskaņot ar s<PERSON>u", "PE.Views.ImageSettings.textFlip": "Uzsist", "PE.Views.ImageSettings.textFromFile": "No faila", "PE.Views.ImageSettings.textFromStorage": "No glabātuves", "PE.Views.ImageSettings.textFromUrl": "No URL", "PE.Views.ImageSettings.textHeight": "Augstums", "PE.Views.ImageSettings.textHint270": "Pagriezt par 90° pret<PERSON><PERSON> pulksteņrā<PERSON><PERSON><PERSON><PERSON><PERSON> virzienam", "PE.Views.ImageSettings.textHint90": "Pagriezt par 90° pulkste<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> virzienā", "PE.Views.ImageSettings.textHintFlipH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textHintFlipV": "<PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>", "PE.Views.ImageSettings.textInsert": "Aizvietot attēlu", "PE.Views.ImageSettings.textOriginalSize": "<PERSON>ak<PERSON><PERSON><PERSON> lie<PERSON>", "PE.Views.ImageSettings.textRecentlyUsed": "Nesen lietots", "PE.Views.ImageSettings.textResetCrop": "Reset crop", "PE.Views.ImageSettings.textRotate90": "Pagriezt par 90°", "PE.Views.ImageSettings.textRotation": "Rotā<PERSON>ja", "PE.Views.ImageSettings.textSize": "Izmērs", "PE.Views.ImageSettings.textWidth": "Platums", "PE.Views.ImageSettingsAdvanced.textAlt": "Alternatīvs teksts", "PE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAltTip": "Vizuālās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, fig<PERSON><PERSON><PERSON>, diagramm<PERSON> vai tabulā.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "PE.Views.ImageSettingsAdvanced.textAngle": "Leņķis", "PE.Views.ImageSettingsAdvanced.textCenter": "Centrā", "PE.Views.ImageSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textFrom": "No", "PE.Views.ImageSettingsAdvanced.textGeneral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textHeight": "Augstums", "PE.Views.ImageSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textImageName": "Image name", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "<PERSON>ak<PERSON><PERSON><PERSON> lie<PERSON>", "PE.Views.ImageSettingsAdvanced.textPlacement": "Novietojums", "PE.Views.ImageSettingsAdvanced.textPosition": "Pozīcija", "PE.Views.ImageSettingsAdvanced.textRotation": "Rotā<PERSON>ja", "PE.Views.ImageSettingsAdvanced.textSize": "Izmērs", "PE.Views.ImageSettingsAdvanced.textTitle": "Attēls - <PERSON><PERSON><PERSON><PERSON>ī<PERSON>", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "Augš<PERSON><PERSON><PERSON> kreis<PERSON> stūris", "PE.Views.ImageSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textWidth": "Platums", "PE.Views.LeftMenu.ariaLeftMenu": "Left menu", "PE.Views.LeftMenu.tipAbout": "Par", "PE.Views.LeftMenu.tipChat": "Čats", "PE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSearch": "Meklēt", "PE.Views.LeftMenu.tipSlides": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSupport": "Atsauksmes un atbalsts", "PE.Views.LeftMenu.tipTitles": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.txtDeveloper": "IZSTRĀDĀTĀJA REŽĪMS", "PE.Views.LeftMenu.txtEditor": "Prezentāciju redaktors", "PE.Views.LeftMenu.txtLimit": "Ierobežot <PERSON>ļuvi", "PE.Views.LeftMenu.txtTrial": "IZMĒĢINĀJUMA REŽĪMS", "PE.Views.LeftMenu.txtTrialDev": "Izmēģinājuma izstrā<PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>", "PE.Views.ParagraphSettings.strLineHeight": "Rindstarpas", "PE.Views.ParagraphSettings.strParagraphSpacing": "Atstatums", "PE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.strSpacingBefore": "Pirms", "PE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "PE.Views.ParagraphSettings.textAt": "Uz", "PE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>az", "PE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textExact": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.txtAutoText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.noTabs": "<PERSON><PERSON><PERSON> la<PERSON> parād<PERSON> norādītās cilnes", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "Visi lielie burti", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndent": "Atkāpes", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Rindstarpas", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Labais", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Pirms", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Īpašie", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Fonts", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Atkāpes un atstarpes", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON> b<PERSON>i", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Atstarpe", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Pārsvītrots", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Apakšraksts", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Augšraksts", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Cilnes", "PE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Rakstzīmju atstarpes", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Noklusējuma cilne", "PE.Views.ParagraphSettingsAdvanced.textEffects": "Efekti", "PE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON> lī<PERSON>ja", "PE.Views.ParagraphSettingsAdvanced.textHanging": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textJustified": "Taisnot<PERSON>", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(neviens)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Noņemt visus", "PE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centrā", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Cilnes pozīcija", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "Labais", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Rindkopa - Papildu iestatījumi", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.PrintWithPreview.txtAllPages": "<PERSON><PERSON> slaidi", "PE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "PE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "PE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "PE.Views.PrintWithPreview.txtCopies": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.PrintWithPreview.txtCurrentPage": "Pašreizējais slaids", "PE.Views.PrintWithPreview.txtCustomPages": "<PERSON><PERSON><PERSON><PERSON><PERSON> druka", "PE.Views.PrintWithPreview.txtEmptyTable": "Nav ko druk<PERSON><PERSON>, jo prezen<PERSON><PERSON><PERSON><PERSON> ir tukša", "PE.Views.PrintWithPreview.txtHeaderFooterSettings": "G<PERSON><PERSON><PERSON>/kājenes i<PERSON>ī<PERSON>", "PE.Views.PrintWithPreview.txtOf": "no {0}", "PE.Views.PrintWithPreview.txtOneSide": "Print one sided", "PE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "PE.Views.PrintWithPreview.txtPage": "Slaids", "PE.Views.PrintWithPreview.txtPageNumInvalid": "<PERSON><PERSON><PERSON> numurs ir ne<PERSON>", "PE.Views.PrintWithPreview.txtPages": "<PERSON><PERSON><PERSON>", "PE.Views.PrintWithPreview.txtPaperSize": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.PrintWithPreview.txtPrint": "Printēt", "PE.Views.PrintWithPreview.txtPrintPdf": "Drukāt PDF formātā", "PE.Views.PrintWithPreview.txtPrintRange": "<PERSON><PERSON><PERSON>", "PE.Views.PrintWithPreview.txtPrintSides": "Print sides", "PE.Views.RightMenu.ariaRightMenu": "Right menu", "PE.Views.RightMenu.txtChartSettings": "Diagram<PERSON> i<PERSON>ī<PERSON>", "PE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtParagraphSettings": "Rindkopas iestatījumi", "PE.Views.RightMenu.txtShapeSettings": "Formas iestatījumi", "PE.Views.RightMenu.txtSignatureSettings": "Paraksta uzstādījumi", "PE.Views.RightMenu.txtSlideSettings": "<PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtTableSettings": "Tabulas i<PERSON>ī<PERSON>", "PE.Views.RightMenu.txtTextArtSettings": "<PERSON><PERSON><PERSON> m<PERSON>", "PE.Views.ShapeSettings.strBackground": "Fona krāsa", "PE.Views.ShapeSettings.strChange": "<PERSON><PERSON><PERSON> formu", "PE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strForeground": "Priekšplāna krāsa", "PE.Views.ShapeSettings.strPattern": "Raksts", "PE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON><PERSON><PERSON>nu", "PE.Views.ShapeSettings.strSize": "Izmērs", "PE.Views.ShapeSettings.strStroke": "Lī<PERSON><PERSON>", "PE.Views.ShapeSettings.strTransparency": "Necaurred<PERSON><PERSON>ba", "PE.Views.ShapeSettings.strType": "Tips", "PE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "PE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "PE.Views.ShapeSettings.textAngle": "Leņķis", "PE.Views.ShapeSettings.textBorderSizeErr": "Ievadītā vērtība nav pareiza.<br>Ievadiet vērtību starp 0 pt un 1584 pt.", "PE.Views.ShapeSettings.textColor": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textEditPoints": "Rediģēt punktus", "PE.Views.ShapeSettings.textEditShape": "Edit shape", "PE.Views.ShapeSettings.textEmptyPattern": "Nav modeļa", "PE.Views.ShapeSettings.textEyedropper": "Eyedropper", "PE.Views.ShapeSettings.textFlip": "Uzsist", "PE.Views.ShapeSettings.textFromFile": "No faila", "PE.Views.ShapeSettings.textFromStorage": "No glabātuves", "PE.Views.ShapeSettings.textFromUrl": "No URL", "PE.Views.ShapeSettings.textGradient": "Gradienta punkti", "PE.Views.ShapeSettings.textGradientFill": "Gradienta aizpildīju<PERSON>", "PE.Views.ShapeSettings.textHint270": "Pagriezt par 90° pret<PERSON><PERSON> pulksteņrā<PERSON><PERSON><PERSON><PERSON><PERSON> virzienam", "PE.Views.ShapeSettings.textHint90": "Pagriezt par 90° pulkste<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> virzienā", "PE.Views.ShapeSettings.textHintFlipH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textHintFlipV": "<PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>", "PE.Views.ShapeSettings.textImageTexture": "Attēls vai tekstūra", "PE.Views.ShapeSettings.textLinear": "Lineārs", "PE.Views.ShapeSettings.textMoreColors": "More colors", "PE.Views.ShapeSettings.textNoFill": "<PERSON>z <PERSON>", "PE.Views.ShapeSettings.textNoShadow": "No Shadow", "PE.Views.ShapeSettings.textPatternFill": "Raksts", "PE.Views.ShapeSettings.textPosition": "Pozīcija", "PE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textRecentlyUsed": "Nesen lietots", "PE.Views.ShapeSettings.textRotate90": "Pagriezt par 90°", "PE.Views.ShapeSettings.textRotation": "Rotā<PERSON>ja", "PE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON> at<PERSON>", "PE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textShadow": "Shadow", "PE.Views.ShapeSettings.textStretch": "Stiept", "PE.Views.ShapeSettings.textStyle": "Stils", "PE.Views.ShapeSettings.textTexture": "No tekstūras", "PE.Views.ShapeSettings.textTile": "Elements", "PE.Views.ShapeSettings.tipAddGradientPoint": "Pievienot gradienta punktu", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Noņemt gradienta punktu", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON>", "PE.Views.ShapeSettings.txtCanvas": "Pamatne", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON><PERSON> audums", "PE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtGreyPaper": "Pelēks papī<PERSON>", "PE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "Nav līni<PERSON>", "PE.Views.ShapeSettings.txtPapyrus": "Papiruss", "PE.Views.ShapeSettings.txtWood": "Koks", "PE.Views.ShapeSettingsAdvanced.strColumns": "Kolonnas", "PE.Views.ShapeSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON> i<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAlt": "Alternatīvs teksts", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAltTip": "Vizuālās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, fig<PERSON><PERSON><PERSON>, diagramm<PERSON> vai tabulā.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "PE.Views.ShapeSettingsAdvanced.textAngle": "Leņķis", "PE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Automātis<PERSON>", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Sāk. izmērs", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Sāk. stils", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON>ī<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON> burtu <PERSON>", "PE.Views.ShapeSettingsAdvanced.textCenter": "Centrā", "PE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON> s<PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Beig<PERSON> izmē<PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON><PERSON> stils", "PE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFrom": "No", "PE.Views.ShapeSettingsAdvanced.textGeneral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textHeight": "Augstums", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textJoinType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tips", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "Rindu stils", "PE.Views.ShapeSettingsAdvanced.textMiter": "Taisns", "PE.Views.ShapeSettingsAdvanced.textNofit": "Nepiel<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Novietojums", "PE.Views.ShapeSettingsAdvanced.textPosition": "Pozīcija", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "<PERSON><PERSON>t formas i<PERSON>, lai tas atbilstu tekstam", "PE.Views.ShapeSettingsAdvanced.textRight": "Labais", "PE.Views.ShapeSettingsAdvanced.textRotation": "Rotā<PERSON>ja", "PE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textShapeName": "Shape name", "PE.Views.ShapeSettingsAdvanced.textShrink": "Samazināt tekstu pā<PERSON>ildē", "PE.Views.ShapeSettingsAdvanced.textSize": "Izmērs", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Atstarpe starp kolonn<PERSON>m", "PE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Uzraksts", "PE.Views.ShapeSettingsAdvanced.textTitle": "Forma – <PERSON><PERSON>ld<PERSON> i<PERSON>īju<PERSON>", "PE.Views.ShapeSettingsAdvanced.textTop": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "Augš<PERSON><PERSON><PERSON> kreis<PERSON> stūris", "PE.Views.ShapeSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Svari un bultiņas", "PE.Views.ShapeSettingsAdvanced.textWidth": "Platums", "PE.Views.ShapeSettingsAdvanced.txtNone": "Neviens", "PE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strDelete": "Noņemt parakstu", "PE.Views.SignatureSettings.strDetails": "Paraksta de<PERSON>ļas", "PE.Views.SignatureSettings.strInvalid": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strSign": "Parakstīt", "PE.Views.SignatureSettings.strSignature": "Paraksts", "PE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.txtContinueEditing": "Vienalga rediģēt", "PE.Views.SignatureSettings.txtEditWarning": "Rediģēšana no prezentācijas noņems parakstus.<br> Vai tiešām vēlaties turpināt?", "PE.Views.SignatureSettings.txtRemoveWarning": "Vai vēlaties noņemt šo parakstu?<br>To nevar at<PERSON>.", "PE.Views.SignatureSettings.txtSigned": "Prezentācijai ir pievienoti derīgi paraksti. Prezentāciju nevar rediģēt.", "PE.Views.SignatureSettings.txtSignedInvalid": "Da<PERSON><PERSON> no prezentācijas digitālajiem parakstiem ir nederīgi vai tos nevar pārbaudīt. Prezentāciju nevar rediģēt.", "PE.Views.SlideSettings.strApplyAllSlides": "Apply to All Slides", "PE.Views.SlideSettings.strBackground": "Fona krāsa", "PE.Views.SlideSettings.strBackgroundGraphics": "Show Background graphics", "PE.Views.SlideSettings.strBackgroundReset": "Reset Background", "PE.Views.SlideSettings.strColor": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strDateTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> datumu un laiku", "PE.Views.SlideSettings.strFill": "Fons", "PE.Views.SlideSettings.strForeground": "Priekšplāna krāsa", "PE.Views.SlideSettings.strPattern": "Raksts", "PE.Views.SlideSettings.strSlideNum": "<PERSON><PERSON><PERSON><PERSON><PERSON> slaida numuru", "PE.Views.SlideSettings.strTransparency": "Necaurred<PERSON><PERSON>ba", "PE.Views.SlideSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "PE.Views.SlideSettings.textAngle": "Leņķis", "PE.Views.SlideSettings.textColor": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textEmptyPattern": "Nav modeļa", "PE.Views.SlideSettings.textFromFile": "No faila", "PE.Views.SlideSettings.textFromStorage": "No glabātuves", "PE.Views.SlideSettings.textFromUrl": "No URL", "PE.Views.SlideSettings.textGradient": "Gradienta punkti", "PE.Views.SlideSettings.textGradientFill": "Gradienta aizpildīju<PERSON>", "PE.Views.SlideSettings.textImageTexture": "Attēls vai tekstūra", "PE.Views.SlideSettings.textLinear": "Lineārs", "PE.Views.SlideSettings.textNoFill": "<PERSON>z <PERSON>", "PE.Views.SlideSettings.textPatternFill": "Raksts", "PE.Views.SlideSettings.textPosition": "Pozīcija", "PE.Views.SlideSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textReset": "Atiestatīt izmaiņas", "PE.Views.SlideSettings.textSelectImage": "<PERSON><PERSON><PERSON> at<PERSON>", "PE.Views.SlideSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStretch": "Stiept", "PE.Views.SlideSettings.textStyle": "Stils", "PE.Views.SlideSettings.textTexture": "No tekstūras", "PE.Views.SlideSettings.textTile": "Elements", "PE.Views.SlideSettings.tipAddGradientPoint": "Pievienot gradienta punktu", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Noņemt gradienta punktu", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON>", "PE.Views.SlideSettings.txtCanvas": "Pamatne", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON><PERSON><PERSON><PERSON> audums", "PE.Views.SlideSettings.txtGrain": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtGranite": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtGreyPaper": "Pelēks papī<PERSON>", "PE.Views.SlideSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "Papiruss", "PE.Views.SlideSettings.txtWood": "Koks", "PE.Views.SlideshowSettings.textLoop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON> <PERSON>k nospiests 'Esc'", "PE.Views.SlideshowSettings.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.strLandscape": "Ainava", "PE.Views.SlideSizeSettings.strPortrait": "Portrets", "PE.Views.SlideSizeSettings.textHeight": "Augstums", "PE.Views.SlideSizeSettings.textSlideOrientation": "Slaida orientācija", "PE.Views.SlideSizeSettings.textSlideSize": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textTitle": "<PERSON><PERSON><PERSON> izmē<PERSON> i<PERSON>tīju<PERSON>", "PE.Views.SlideSizeSettings.textWidth": "Platums", "PE.Views.SlideSizeSettings.txt35": "35 mm slaidi", "PE.Views.SlideSizeSettings.txtA3": "A3 papīrs (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "A4 papīrs (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO) papīrs (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO) papīrs (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtLedger": "Ledger papīrs (11x17 collas)", "PE.Views.SlideSizeSettings.txtLetter": "Letter papīrs (8.5x11 collas)", "PE.Views.SlideSizeSettings.txtOverhead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtSlideNum": "Number slides from", "PE.Views.SlideSizeSettings.txtStandard": "Standarta (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Platek<PERSON>ān<PERSON>", "PE.Views.Statusbar.goToPageText": "Dot<PERSON> uz slaidu", "PE.Views.Statusbar.pageIndexText": "{0}. no {1} slaids", "PE.Views.Statusbar.textShowBegin": "<PERSON><PERSON><PERSON><PERSON><PERSON> no sākuma", "PE.Views.Statusbar.textShowCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON> no šī slaida", "PE.Views.Statusbar.textShowPresenterView": "<PERSON><PERSON><PERSON><PERSON><PERSON> prezentētāja re<PERSON>", "PE.Views.Statusbar.textSlideMaster": "Slide master", "PE.Views.Statusbar.tipAccessRights": "Pārvaldīt dokumenta piekļuves <PERSON>", "PE.Views.Statusbar.tipFitPage": "Saskaņot ar s<PERSON>u", "PE.Views.Statusbar.tipFitWidth": "Saskaņot ar platumu", "PE.Views.Statusbar.tipPreview": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON>", "PE.Views.Statusbar.tipSetLang": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> teksta valodu", "PE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Statusbar.txtPageNumInvalid": "<PERSON><PERSON><PERSON><PERSON> slaida numuru", "PE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> kolonnu", "PE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON><PERSON> rindu", "PE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON><PERSON> tabulu", "PE.Views.TableSettings.insertColumnLeftText": "Ievietot kolonnu pa kreisi", "PE.Views.TableSettings.insertColumnRightText": "Ieviet<PERSON> kolonnu pa labi", "PE.Views.TableSettings.insertRowAboveText": "Ievietot rindu augstāk", "PE.Views.TableSettings.insertRowBelowText": "Ievietot rindu <PERSON>", "PE.Views.TableSettings.mergeCellsText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectCellText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON> kolo<PERSON>u", "PE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON> rindu", "PE.Views.TableSettings.selectTableText": "Izvēlēties tabulu", "PE.Views.TableSettings.splitCellsText": "<PERSON><PERSON><PERSON><PERSON>...", "PE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "PE.Views.TableSettings.textBackColor": "Fona krāsa", "PE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textBorderColor": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textBorders": "Robežas stils", "PE.Views.TableSettings.textCellSize": "Šūnas izmērs", "PE.Views.TableSettings.textColumns": "Kolonnas", "PE.Views.TableSettings.textDistributeCols": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "PE.Views.TableSettings.textDistributeRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "PE.Views.TableSettings.textEdit": "<PERSON><PERSON><PERSON> un kolonnas", "PE.Views.TableSettings.textEmptyTemplate": "<PERSON>z <PERSON>ē<PERSON>", "PE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeader": "G<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeight": "Augstums", "PE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "Select borders you want to change applying style chosen above", "PE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON> no veidnes", "PE.Views.TableSettings.textTotal": "Kopā", "PE.Views.TableSettings.textWidth": "Platums", "PE.Views.TableSettings.tipAll": "Iestatīt ārē<PERSON> a<PERSON>ali un visas iekšējās lī<PERSON>", "PE.Views.TableSettings.tipBottom": "Iestatīt tikai ārējo a<PERSON> a<PERSON>ali", "PE.Views.TableSettings.tipInner": "Iestatīt tikai iekšēj<PERSON> l<PERSON>", "PE.Views.TableSettings.tipInnerHor": "Iestatīt tikai <PERSON> i<PERSON> l<PERSON>", "PE.Views.TableSettings.tipInnerVert": "Iestatīt tikai vertik<PERSON> i<PERSON> l<PERSON>", "PE.Views.TableSettings.tipLeft": "Iestatīt tikai ār<PERSON><PERSON> k<PERSON> a<PERSON>", "PE.Views.TableSettings.tipNone": "Iestatīt bez a<PERSON>alēm", "PE.Views.TableSettings.tipOuter": "<PERSON>estat<PERSON>t tikai ā<PERSON><PERSON>", "PE.Views.TableSettings.tipRight": "Iestatīt tikai ār<PERSON>jo labo a<PERSON>ali", "PE.Views.TableSettings.tipTop": "Iestatīt tikai ārējo aug<PERSON> a<PERSON>ali", "PE.Views.TableSettings.txtGroupTable_Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Dark": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Light": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Optimal": "Vislabākā atbilstība dokumentam", "PE.Views.TableSettings.txtNoBorders": "Nav a<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_Accent": "Ak<PERSON>s", "PE.Views.TableSettings.txtTable_DarkStyle": "<PERSON><PERSON><PERSON><PERSON> stils", "PE.Views.TableSettings.txtTable_LightStyle": "<PERSON><PERSON><PERSON><PERSON> stils", "PE.Views.TableSettings.txtTable_MediumStyle": "<PERSON><PERSON><PERSON><PERSON><PERSON> stils", "PE.Views.TableSettings.txtTable_NoGrid": "Bez režģa", "PE.Views.TableSettings.txtTable_NoStyle": "Nav stila", "PE.Views.TableSettings.txtTable_TableGrid": "Tabulas režģis", "PE.Views.TableSettings.txtTable_ThemedStyle": "Tematiskais stils", "PE.Views.TableSettingsAdvanced.textAlt": "Alternatīvs teksts", "PE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textAltTip": "Vizuālās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, fig<PERSON><PERSON><PERSON>, diagramm<PERSON> vai tabulā.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "PE.Views.TableSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textCenter": "Centrā", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Izmanotot noklusējuma piemales", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Noklusēju<PERSON> piemales", "PE.Views.TableSettingsAdvanced.textFrom": "No", "PE.Views.TableSettingsAdvanced.textGeneral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textHeight": "Augstums", "PE.Views.TableSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textPlacement": "Novietojums", "PE.Views.TableSettingsAdvanced.textPosition": "Pozīcija", "PE.Views.TableSettingsAdvanced.textRight": "Labais", "PE.Views.TableSettingsAdvanced.textSize": "Izmērs", "PE.Views.TableSettingsAdvanced.textTableName": "Ta<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTitle": "Tabula - Papildu i<PERSON>ī<PERSON>", "PE.Views.TableSettingsAdvanced.textTop": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "Augš<PERSON><PERSON><PERSON> kreis<PERSON> stūris", "PE.Views.TableSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidth": "Platums", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "Piemales", "PE.Views.TextArtSettings.strBackground": "Fona krāsa", "PE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strForeground": "Priekšplāna krāsa", "PE.Views.TextArtSettings.strPattern": "Raksts", "PE.Views.TextArtSettings.strSize": "Izmērs", "PE.Views.TextArtSettings.strStroke": "Lī<PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "Necaurred<PERSON><PERSON>ba", "PE.Views.TextArtSettings.strType": "Tips", "PE.Views.TextArtSettings.textAngle": "Leņķis", "PE.Views.TextArtSettings.textBorderSizeErr": "Ievadītā vērtība nav pareiza.<br>Ievadiet vērtību starp 0 pt un 1584 pt.", "PE.Views.TextArtSettings.textColor": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textEmptyPattern": "Nav modeļa", "PE.Views.TextArtSettings.textFromFile": "No faila", "PE.Views.TextArtSettings.textFromUrl": "No URL", "PE.Views.TextArtSettings.textGradient": "Gradienta punkti", "PE.Views.TextArtSettings.textGradientFill": "Gradienta aizpildīju<PERSON>", "PE.Views.TextArtSettings.textImageTexture": "Attēls vai tekstūra", "PE.Views.TextArtSettings.textLinear": "Lineārs", "PE.Views.TextArtSettings.textNoFill": "<PERSON>z <PERSON>", "PE.Views.TextArtSettings.textPatternFill": "Raksts", "PE.Views.TextArtSettings.textPosition": "Pozīcija", "PE.Views.TextArtSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStretch": "Stiept", "PE.Views.TextArtSettings.textStyle": "Stils", "PE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textTexture": "No tekstūras", "PE.Views.TextArtSettings.textTile": "Elements", "PE.Views.TextArtSettings.textTransform": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.tipAddGradientPoint": "Pievienot gradienta punktu", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Noņemt gradienta punktu", "PE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON>", "PE.Views.TextArtSettings.txtCanvas": "Pamatne", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON><PERSON><PERSON> audums", "PE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtGreyPaper": "Pelēks papī<PERSON>", "PE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtNoBorders": "Nav līni<PERSON>", "PE.Views.TextArtSettings.txtPapyrus": "Papiruss", "PE.Views.TextArtSettings.txtWood": "Koks", "PE.Views.Toolbar.capAddLayout": "Add Layout", "PE.Views.Toolbar.capAddSlide": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capAddSlideMaster": "Add Slide Master", "PE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capBtnDateTime": "Datums un Laiks", "PE.Views.Toolbar.capBtnInsHeaderFooter": "Galvene un Kājene", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "Simbols", "PE.Views.Toolbar.capBtnSlideNum": "<PERSON><PERSON><PERSON> numurs", "PE.Views.Toolbar.capCloseMaster": "Close Master", "PE.Views.Toolbar.capInsertAudio": "Audio", "PE.Views.Toolbar.capInsertChart": "Di<PERSON>ram<PERSON>", "PE.Views.Toolbar.capInsertEquation": "Vienādojums", "PE.Views.Toolbar.capInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertPlaceholder": "Insert Placeholder", "PE.Views.Toolbar.capInsertShape": "Forma", "PE.Views.Toolbar.capInsertTable": "Tabula", "PE.Views.Toolbar.capInsertText": "Uzraksts", "PE.Views.Toolbar.capInsertTextArt": "Text Art", "PE.Views.Toolbar.capInsertVideo": "Video", "PE.Views.Toolbar.capTabFile": "Fails", "PE.Views.Toolbar.capTabHome": "Sā<PERSON><PERSON>", "PE.Views.Toolbar.capTabInsert": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniCapitalizeWords": "<PERSON><PERSON><PERSON> lielos burtus katram vārdam", "PE.Views.Toolbar.mniCustomTable": "Ievietot pielāgot<PERSON> tabulu", "PE.Views.Toolbar.mniImageFromFile": "Att<PERSON><PERSON> no faila", "PE.Views.Toolbar.mniImageFromStorage": "Attēls no glabātuves", "PE.Views.Toolbar.mniImageFromUrl": "Attēls no vietrāža URL", "PE.Views.Toolbar.mniInsertSSE": "Ievietot izklājlapu", "PE.Views.Toolbar.mniLowerCase": "mazie burti", "PE.Views.Toolbar.mniSentenceCase": "<PERSON><PERSON><PERSON> reģistrs.", "PE.Views.Toolbar.mniSlideAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniSlideStandard": "Standarta (4:3)", "PE.Views.Toolbar.mniSlideWide": "<PERSON><PERSON><PERSON><PERSON><PERSON> (16:9)", "PE.Views.Toolbar.mniToggleCase": "pĀRSLĒGT rEĢISTRU", "PE.Views.Toolbar.mniUpperCase": "LIELIE BURTI", "PE.Views.Toolbar.strMenuNoFill": "<PERSON>z <PERSON>", "PE.Views.Toolbar.textAlignBottom": "Līdzināt tekstu uz apakšu", "PE.Views.Toolbar.textAlignCenter": "Centra teksts", "PE.Views.Toolbar.textAlignJust": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tekstu pa kreisi", "PE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON><PERSON><PERSON><PERSON>t tekstu uz vidu", "PE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tekstu pa labi", "PE.Views.Toolbar.textAlignTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tekstu pa augšu", "PE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "PE.Views.Toolbar.textArrangeBack": "Pārnest uz fonu", "PE.Views.Toolbar.textArrangeBackward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textArrangeForward": "Pārnest uz priekšu", "PE.Views.Toolbar.textArrangeFront": "Nest uz priekšplānu", "PE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "PE.Views.Toolbar.textBlackHeart": "Black heart suit", "PE.Views.Toolbar.textBold": "Treknraksts", "PE.Views.Toolbar.textBullet": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textChart": "Chart", "PE.Views.Toolbar.textColumnsCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "PE.Views.Toolbar.textColumnsOne": "Viena kolonna", "PE.Views.Toolbar.textColumnsThree": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON> kolo<PERSON>", "PE.Views.Toolbar.textContent": "Content", "PE.Views.Toolbar.textContentVertical": "Content (Vertical)", "PE.Views.Toolbar.textCopyright": "Autortiesību <PERSON>", "PE.Views.Toolbar.textDegree": "Degree Sign", "PE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "PE.Views.Toolbar.textDivision": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textDollar": "Dollar Sign", "PE.Views.Toolbar.textEuro": "Euro Sign", "PE.Views.Toolbar.textFooters": "Footers", "PE.Views.Toolbar.textGreaterEqual": "Lie<PERSON>ā<PERSON> par vai vienāds ar", "PE.Views.Toolbar.textInfinity": "Bezgalība", "PE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textLessEqual": "<PERSON><PERSON>āk nekā vai vienāds ar", "PE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "PE.Views.Toolbar.textLineSpaceOptions": "Line spacing options", "PE.Views.Toolbar.textListSettings": "Saraksta iestatījumi", "PE.Views.Toolbar.textMoreSymbols": "More symbols", "PE.Views.Toolbar.textNotEqualTo": "Nav vien<PERSON>ds ar", "PE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "PE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "PE.Views.Toolbar.textPicture": "Picture", "PE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "PE.Views.Toolbar.textRecentlyUsed": "Nesen lietots", "PE.Views.Toolbar.textRegistered": "Reģistrēta zīme", "PE.Views.Toolbar.textSection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignBottom": "Līdzināt pie apakšas", "PE.Views.Toolbar.textShapeAlignCenter": "L<PERSON><PERSON><PERSON>āt pa centru", "PE.Views.Toolbar.textShapeAlignLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa kreisi", "PE.Views.Toolbar.textShapeAlignMiddle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz vidu", "PE.Views.Toolbar.textShapeAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa labi", "PE.Views.Toolbar.textShapeAlignTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz augšu", "PE.Views.Toolbar.textShapesCombine": "Combine", "PE.Views.Toolbar.textShapesFragment": "Fragment", "PE.Views.Toolbar.textShapesIntersect": "Intersect", "PE.Views.Toolbar.textShapesSubstract": "Subtract", "PE.Views.Toolbar.textShapesUnion": "Union", "PE.Views.Toolbar.textShowBegin": "<PERSON><PERSON><PERSON><PERSON><PERSON> no sākuma", "PE.Views.Toolbar.textShowCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON> no šī slaida", "PE.Views.Toolbar.textShowPresenterView": "<PERSON><PERSON><PERSON><PERSON><PERSON> prezentētāja re<PERSON>", "PE.Views.Toolbar.textShowSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textSmartArt": "SmartArt", "PE.Views.Toolbar.textSmile": "White Smiling Face", "PE.Views.Toolbar.textSquareRoot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textStrikeout": "Pārsvītrots", "PE.Views.Toolbar.textSubscript": "Apakšraksts", "PE.Views.Toolbar.textSuperscript": "Augšraksts", "PE.Views.Toolbar.textTabAnimation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabCollaboration": "Sad<PERSON>bī<PERSON>", "PE.Views.Toolbar.textTabDesign": "Design", "PE.Views.Toolbar.textTabDraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabFile": "Fails", "PE.Views.Toolbar.textTabHome": "Sā<PERSON><PERSON>", "PE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTable": "Table", "PE.Views.Toolbar.textTabProtect": "Aizsardzība", "PE.Views.Toolbar.textTabTransitions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabView": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textText": "Text", "PE.Views.Toolbar.textTextVertical": "Text (Vertical)", "PE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "PE.Views.Toolbar.textTitle": "Title", "PE.Views.Toolbar.textTitleError": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "PE.Views.Toolbar.textUnderline": "Pasvītrot", "PE.Views.Toolbar.textYen": "Yen Sign", "PE.Views.Toolbar.tipAddLayout": "Add layout", "PE.Views.Toolbar.tipAddSlide": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipAddSlideMaster": "Add slide master", "PE.Views.Toolbar.tipBack": "Atpakaļ", "PE.Views.Toolbar.tipChangeCase": "<PERSON><PERSON><PERSON> lielo/mazo burtu", "PE.Views.Toolbar.tipChangeChart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> diagrammas veidu", "PE.Views.Toolbar.tipChangeSlide": "Change Slide Layout", "PE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON><PERSON><PERSON> stilu", "PE.Views.Toolbar.tipCloseMaster": "Close Master", "PE.Views.Toolbar.tipColorSchemas": "<PERSON><PERSON><PERSON> kr<PERSON> sh<PERSON>mu", "PE.Views.Toolbar.tipColumns": "Iev<PERSON><PERSON> kolonnas", "PE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON><PERSON> stilu", "PE.Views.Toolbar.tipCut": "Izgriezt", "PE.Views.Toolbar.tipDateTime": "Ievietot pašreizējo datumu un laiku", "PE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON><PERSON><PERSON>u", "PE.Views.Toolbar.tipDecPrLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipEditHeaderFooter": "Rediģēt <PERSON><PERSON><PERSON><PERSON> vai Galveni", "PE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipFontName": "Fonts", "PE.Views.Toolbar.tipFontSize": "Fonta izmērs", "PE.Views.Toolbar.tipHAligh": "Horizontā<PERSON>ā <PERSON>", "PE.Views.Toolbar.tipHighlightColor": "Izcelt ar krāsu", "PE.Views.Toolbar.tipIncFont": "<PERSON><PERSON><PERSON><PERSON><PERSON>u", "PE.Views.Toolbar.tipIncPrLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertAudio": "Ievietot audio", "PE.Views.Toolbar.tipInsertChart": "Ievietot grafiku", "PE.Views.Toolbar.tipInsertChartPlaceholder": "Insert chart placeholder", "PE.Views.Toolbar.tipInsertContentPlaceholder": "Insert content placeholder", "PE.Views.Toolbar.tipInsertContentVerticalPlaceholder": "Insert content (vertical) placeholder", "PE.Views.Toolbar.tipInsertEquation": "Ievietot vienā<PERSON><PERSON>", "PE.Views.Toolbar.tipInsertHorizontalText": "Ievietot horizontālu tekstlodziņu", "PE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "PE.Views.Toolbar.tipInsertPicturePlaceholder": "Insert picture placeholder", "PE.Views.Toolbar.tipInsertShape": "Insert Autoshape", "PE.Views.Toolbar.tipInsertSmartArt": "Ievietot SmartArt", "PE.Views.Toolbar.tipInsertSmartArtPlaceholder": "Insert SmartArt placeholder", "PE.Views.Toolbar.tipInsertSymbol": "Ievietot simbolu", "PE.Views.Toolbar.tipInsertTable": "Ievietot tabulu", "PE.Views.Toolbar.tipInsertTablePlaceholder": "Insert table placeholder", "PE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertTextArt": "Ievietot Text Art objektu", "PE.Views.Toolbar.tipInsertTextPlaceholder": "Insert text placeholder", "PE.Views.Toolbar.tipInsertTextVerticalPlaceholder": "Insert text (vertical) placeholder", "PE.Views.Toolbar.tipInsertVerticalText": "Ievietot vertikālu tekstlodziņu", "PE.Views.Toolbar.tipInsertVideo": "Ievietot video", "PE.Views.Toolbar.tipLineSpace": "Rindstarpas", "PE.Views.Toolbar.tipMarkers": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipMarkersArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipMarkersCheckmark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "PE.Views.Toolbar.tipMarkersDash": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "PE.Views.Toolbar.tipMarkersFRhombus": "Pildītas rombveida a<PERSON>zī<PERSON>", "PE.Views.Toolbar.tipMarkersFRound": "Pildītas apaļas aizzīmes", "PE.Views.Toolbar.tipMarkersFSquare": "Pildītas četrstūra a<PERSON>", "PE.Views.Toolbar.tipMarkersHRound": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipMarkersStar": "Zvaig<PERSON><PERSON><PERSON> a<PERSON>", "PE.Views.Toolbar.tipNone": "Neviens", "PE.Views.Toolbar.tipNumbers": "Numerācija", "PE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipPreview": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipPrint": "Printēt", "PE.Views.Toolbar.tipPrintQuick": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipReplace": "Replace", "PE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSaveCoauth": "Saglabājiet <PERSON>, lai tās redz<PERSON>tu citi lietotāji.", "PE.Views.Toolbar.tipSelectAll": "Izvēlēties visu", "PE.Views.Toolbar.tipShapeAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> formu", "PE.Views.Toolbar.tipShapeArrange": "Sak<PERSON><PERSON><PERSON> formu", "PE.Views.Toolbar.tipShapesMerge": "Merge shapes", "PE.Views.Toolbar.tipSlideNum": "<PERSON>ev<PERSON><PERSON> slaida numuru", "PE.Views.Toolbar.tipSlideSize": "Izvēlēties slaida izmēru", "PE.Views.Toolbar.tipSlideTheme": "<PERSON><PERSON><PERSON> t<PERSON>", "PE.Views.Toolbar.tipUndo": "Atsaukt", "PE.Views.Toolbar.tipVAligh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipViewSettings": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtColors": "Colors", "PE.Views.Toolbar.txtDistribHor": "<PERSON><PERSON><PERSON><PERSON><PERSON> pa <PERSON>", "PE.Views.Toolbar.txtDistribVert": "<PERSON><PERSON><PERSON><PERSON><PERSON> pa vertik<PERSON>li", "PE.Views.Toolbar.txtDuplicateSlide": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>u", "PE.Views.Toolbar.txtGroup": "Grupa", "PE.Views.Toolbar.txtObjectsAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON> objektus", "PE.Views.Toolbar.txtSlideAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "PE.Views.Toolbar.txtSlideSize": "Slide size", "PE.Views.Toolbar.txtUngroup": "Atgrupēt", "PE.Views.Transitions.strDelay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.strDuration": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.strStartOnClick": "Palaist ar klikšķi", "PE.Views.Transitions.textBlack": "<PERSON><PERSON><PERSON> melnu", "PE.Views.Transitions.textBottom": "No apakšas", "PE.Views.Transitions.textBottomLeft": "No apakšas pa kreisi", "PE.Views.Transitions.textBottomRight": "No apakšas pa labi", "PE.Views.Transitions.textClock": "<PERSON><PERSON>ks<PERSON><PERSON>", "PE.Views.Transitions.textClockwise": "Pulks<PERSON><PERSON><PERSON> rā<PERSON><PERSON><PERSON><PERSON> vir<PERSON>", "PE.Views.Transitions.textCounterclockwise": "<PERSON><PERSON><PERSON><PERSON> pu<PERSON> rādīt<PERSON><PERSON> vir<PERSON>", "PE.Views.Transitions.textCover": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textFade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textHorizontalIn": "<PERSON>", "PE.Views.Transitions.textHorizontalOut": "<PERSON>āli ārā", "PE.Views.Transitions.textLeft": "No kreisās puses", "PE.Views.Transitions.textMorph": "Morph", "PE.Views.Transitions.textMorphLetters": "Vēstules", "PE.Views.Transitions.textMorphObjects": "Objekts", "PE.Views.Transitions.textMorphWord": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textNone": "Neviens", "PE.Views.Transitions.textPush": "Izvirzī<PERSON>", "PE.Views.Transitions.textRandom": "Random", "PE.Views.Transitions.textRight": "No labās puses", "PE.Views.Transitions.textSmoothly": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textSplit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textTop": "No augšas", "PE.Views.Transitions.textTopLeft": "No augšas pa kreisi", "PE.Views.Transitions.textTopRight": "No augšas pa labi", "PE.Views.Transitions.textUnCover": "Atsegt", "PE.Views.Transitions.textVerticalIn": "<PERSON> vertikāli iek<PERSON>ā", "PE.Views.Transitions.textVerticalOut": "Pa vertikāli ārā", "PE.Views.Transitions.textWedge": "Ķīlis", "PE.Views.Transitions.textWipe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoomIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoomOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoomRotate": "<PERSON><PERSON><PERSON><PERSON><PERSON> un rotēt", "PE.Views.Transitions.txtApplyToAll": "Pie<PERSON>ē<PERSON> visiem slaidiem", "PE.Views.Transitions.txtParameters": "Parametri", "PE.Views.Transitions.txtPreview": "Priekšskatījums", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.capBtnHand": "Hand", "PE.Views.ViewTab.capBtnSelect": "Select", "PE.Views.ViewTab.textAddHGuides": "<PERSON><PERSON><PERSON> c<PERSON>ļ<PERSON>i", "PE.Views.ViewTab.textAddVGuides": "<PERSON><PERSON><PERSON> ve<PERSON> c<PERSON>", "PE.Views.ViewTab.textAlwaysShowToolbar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textClearGuides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textCm": "cm", "PE.Views.ViewTab.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textFill": "Fill", "PE.Views.ViewTab.textFitToSlide": "Saskaņot ar s<PERSON>u", "PE.Views.ViewTab.textFitToWidth": "Saskaņot ar platumu", "PE.Views.ViewTab.textGridlines": "Režģlīnijas", "PE.Views.ViewTab.textGuides": "Ceļveži", "PE.Views.ViewTab.textInterfaceTheme": "Interfeisa tēma", "PE.Views.ViewTab.textLeftMenu": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textLine": "Line", "PE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "PE.Views.ViewTab.textNormal": "Normal", "PE.Views.ViewTab.textNotes": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textRightMenu": "<PERSON><PERSON> panelis", "PE.Views.ViewTab.textRulers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textShowGridlines": "<PERSON><PERSON><PERSON><PERSON><PERSON> režģlīnijas", "PE.Views.ViewTab.textShowGuides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textSlideMaster": "Slide Master", "PE.Views.ViewTab.textSmartGuides": "Viedie ceļveži", "PE.Views.ViewTab.textSnapObjects": "<PERSON><PERSON><PERSON> objektu režģim", "PE.Views.ViewTab.textStatusBar": "<PERSON><PERSON> j<PERSON>", "PE.Views.ViewTab.textTabStyle": "Tab style", "PE.Views.ViewTab.textZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.tipFitToSlide": "Saskaņot ar s<PERSON>u", "PE.Views.ViewTab.tipFitToWidth": "Saskaņot ar platumu", "PE.Views.ViewTab.tipGridlines": "<PERSON><PERSON><PERSON><PERSON><PERSON> režģlīnijas", "PE.Views.ViewTab.tipGuides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.tipHandTool": "Hand tool", "PE.Views.ViewTab.tipInterfaceTheme": "Interfeisa tēma", "PE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "PE.Views.ViewTab.tipNormal": "Normal", "PE.Views.ViewTab.tipSelectTool": "Select tool", "PE.Views.ViewTab.tipSlideMaster": "Slide master", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}