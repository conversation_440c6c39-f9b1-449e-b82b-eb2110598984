{"Common.Controllers.Chat.notcriticalErrorTitle": "Aviso", "Common.Controllers.Desktop.hintBtnHome": "<PERSON><PERSON> janela principal", "Common.Controllers.Desktop.itemCreateFromTemplate": "Criar a partir do modelo", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "O objeto está desabilitado por que está sendo editado por outro usuário.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Aviso", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "O objeto está desabilitado por que está sendo editado por outro usuário.", "Common.Controllers.ExternalOleEditor.warningTitle": "Aviso", "Common.Controllers.History.notcriticalErrorTitle": "Aviso", "Common.Controllers.History.txtErrorLoadHistory": "O carregamento de histórico falhou", "Common.Controllers.Plugins.helpUseMacros": "Localize o botão Macros aqui", "Common.Controllers.Plugins.helpUseMacrosHeader": "<PERSON><PERSON> atualizado a macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Os plug-ins foram instalados com sucesso. Você pode acessar todos os plugins de fundo aqui.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> foi instalado com sucesso. Você pode acessar todos os plugins de fundo aqui.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Execute plug-ins instalados", "Common.Controllers.Plugins.textRunPlugin": "Executar plugin", "Common.define.chartData.textArea": "Á<PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON> empi<PERSON>", "Common.define.chartData.textAreaStackedPer": "100% <PERSON><PERSON> al<PERSON>a", "Common.define.chartData.textBar": "Barr<PERSON>", "Common.define.chartData.textBarNormal": "Colunas agrupadas", "Common.define.chartData.textBarNormal3d": "3-D Coluna agrupada", "Common.define.chartData.textBarNormal3dPerspective": "Coluna 3-D", "Common.define.chartData.textBarStacked": "<PERSON><PERSON> al<PERSON>hada", "Common.define.chartData.textBarStacked3d": "Coluna empilhada 3-D", "Common.define.chartData.textBarStackedPer": "100% <PERSON>una alinhada", "Common.define.chartData.textBarStackedPer3d": "3-D 100% <PERSON><PERSON> alinhada", "Common.define.chartData.textCharts": "Grá<PERSON><PERSON>", "Common.define.chartData.textColumn": "Coluna", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON> empilhada - coluna agrupada", "Common.define.chartData.textComboBarLine": "Coluna agrupada - linha", "Common.define.chartData.textComboBarLineSecondary": "Coluna agrupada - linha no eixo secundário", "Common.define.chartData.textComboCustom": "Combinação personalizada", "Common.define.chartData.textDoughnut": "Rosquin<PERSON>", "Common.define.chartData.textHBarNormal": "Barras agrupadas", "Common.define.chartData.textHBarNormal3d": "3-D Barra agrupada", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON>", "Common.define.chartData.textHBarStacked3d": "Barra empilhada 3-D", "Common.define.chartData.textHBarStackedPer": "100% Barra alinhada", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% <PERSON><PERSON> al<PERSON>a", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "Linha 3-D", "Common.define.chartData.textLineMarker": "Linha com marcadores", "Common.define.chartData.textLineStacked": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStackedMarker": "Linha empilhada com marcadores", "Common.define.chartData.textLineStackedPer": "100% Ali<PERSON><PERSON>", "Common.define.chartData.textLineStackedPerMarker": "Alinhado com 100%", "Common.define.chartData.textPie": "Gráfico de pizza", "Common.define.chartData.textPie3d": "Torta 3-D", "Common.define.chartData.textPoint": "Gráfico de pontos", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Radar preenchido", "Common.define.chartData.textRadarMarker": "Radar com marcadores", "Common.define.chartData.textScatter": "Di<PERSON>são", "Common.define.chartData.textScatterLine": "Dispersão com linhas retas", "Common.define.chartData.textScatterLineMarker": "Dispersão com linhas retas e marcadores", "Common.define.chartData.textScatterSmooth": "Dispersão com linhas suaves", "Common.define.chartData.textScatterSmoothMarker": "Dispersão com linhas suaves e marcadores", "Common.define.chartData.textStock": "Gráfico de ações", "Common.define.chartData.textSurface": "Superfície", "Common.define.effectData.textAcross": "Através", "Common.define.effectData.textAppear": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textArcDown": "Arco para baixo", "Common.define.effectData.textArcLeft": "<PERSON><PERSON>", "Common.define.effectData.textArcRight": "<PERSON>o direito", "Common.define.effectData.textArcs": "Arcos", "Common.define.effectData.textArcUp": "Arco para cima", "Common.define.effectData.textBasic": "Básico", "Common.define.effectData.textBasicSwivel": "Giratório básico", "Common.define.effectData.textBasicZoom": "Zoom básico", "Common.define.effectData.textBean": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBlinds": "Persianas", "Common.define.effectData.textBlink": "Piscar", "Common.define.effectData.textBoldFlash": "<PERSON>", "Common.define.effectData.textBoldReveal": "Revelação ousada", "Common.define.effectData.textBoomerang": "Bumerangue", "Common.define.effectData.textBounce": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounceLeft": "Salto à esquerda", "Common.define.effectData.textBounceRight": "Saltar para a direita", "Common.define.effectData.textBox": "Caixa", "Common.define.effectData.textBrushColor": "Cor do pincel", "Common.define.effectData.textCenterRevolve": "Centro rotativo", "Common.define.effectData.textCheckerboard": "Quadro de verificação", "Common.define.effectData.textCircle": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCollapse": "Colapso", "Common.define.effectData.textColorPulse": "Pulsação de cor", "Common.define.effectData.textComplementaryColor": "Cor complementar", "Common.define.effectData.textComplementaryColor2": "Cor complementar 2", "Common.define.effectData.textCompress": "Comprimir", "Common.define.effectData.textContrast": "Contraste", "Common.define.effectData.textContrastingColor": "Cor contrastante", "Common.define.effectData.textCredits": "C<PERSON>dit<PERSON>", "Common.define.effectData.textCrescentMoon": "Lua crescente", "Common.define.effectData.textCurveDown": "Curva para baixo", "Common.define.effectData.textCurvedSquare": "Quadrado curvo", "Common.define.effectData.textCurvedX": "X curvo", "Common.define.effectData.textCurvyLeft": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCurvyRight": "Curva à direita", "Common.define.effectData.textCurvyStar": "<PERSON><PERSON><PERSON><PERSON> curvil<PERSON>", "Common.define.effectData.textCustomPath": "<PERSON><PERSON><PERSON> personalizado", "Common.define.effectData.textCuverUp": "Cobrir", "Common.define.effectData.textDarken": "Escurecer", "Common.define.effectData.textDecayingWave": "Onda decadente", "Common.define.effectData.textDesaturate": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textDiagonalDownRight": "Diagonal para baixo à direita", "Common.define.effectData.textDiagonalUpRight": "Diagonal para cima à direita", "Common.define.effectData.textDiamond": "Diamante", "Common.define.effectData.textDisappear": "Desapa<PERSON>cer", "Common.define.effectData.textDissolveIn": "Dissolver em", "Common.define.effectData.textDissolveOut": "Dissolver", "Common.define.effectData.textDown": "Abaixo", "Common.define.effectData.textDrop": "<PERSON><PERSON>", "Common.define.effectData.textEmphasis": "Efeito de ênfase", "Common.define.effectData.textEntrance": "Efeito de entrada", "Common.define.effectData.textEqualTriangle": "Triângulo igual", "Common.define.effectData.textExciting": "Emocionante", "Common.define.effectData.textExit": "Efeito de saída", "Common.define.effectData.textExpand": "Expandir", "Common.define.effectData.textFade": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFigureFour": "Figura 8 Quatro", "Common.define.effectData.textFillColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "Common.define.effectData.textFlip": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFloat": "Flut<PERSON><PERSON>", "Common.define.effectData.textFloatDown": "Flutuar para baixo", "Common.define.effectData.textFloatIn": "<PERSON><PERSON><PERSON><PERSON> dentro", "Common.define.effectData.textFloatOut": "Flutuar para fora", "Common.define.effectData.textFloatUp": "Flutuar para cima", "Common.define.effectData.textFlyIn": "Voar em", "Common.define.effectData.textFlyOut": "Voar para fora", "Common.define.effectData.textFontColor": "<PERSON><PERSON> da fonte", "Common.define.effectData.textFootball": "Futebol", "Common.define.effectData.textFromBottom": "Do fundo", "Common.define.effectData.textFromBottomLeft": "Da parte inferior esquerda", "Common.define.effectData.textFromBottomRight": "Da parte inferior direita", "Common.define.effectData.textFromLeft": "Da esquerda", "Common.define.effectData.textFromRight": "Da direita", "Common.define.effectData.textFromTop": "De cima", "Common.define.effectData.textFromTopLeft": "Do canto superior esquerdo", "Common.define.effectData.textFromTopRight": "Do canto superior direito", "Common.define.effectData.textFunnel": "Funil", "Common.define.effectData.textGrowShrink": "Crescer/En<PERSON>her", "Common.define.effectData.textGrowTurn": "Crescer e transformar", "Common.define.effectData.textGrowWithColor": "Aumentos de cor", "Common.define.effectData.textHeart": "Coração", "Common.define.effectData.textHeartbeat": "Batimento cardiaco", "Common.define.effectData.textHexagon": "Hexágono", "Common.define.effectData.textHorizontal": "Horizontal", "Common.define.effectData.textHorizontalFigure": "Horizontal Figura 8", "Common.define.effectData.textHorizontalIn": "Horizontal para dentro", "Common.define.effectData.textHorizontalOut": "Horizontal para fora", "Common.define.effectData.textIn": "Em", "Common.define.effectData.textInFromScreenCenter": "No centro da tela", "Common.define.effectData.textInSlightly": "Ligeiramente", "Common.define.effectData.textInToScreenBottom": "Na parte inferior da tela", "Common.define.effectData.textInvertedSquare": "Quadrado invertido", "Common.define.effectData.textInvertedTriangle": "Triângulo invertido", "Common.define.effectData.textLeft": "E<PERSON>rda", "Common.define.effectData.textLeftDown": "Esquerda para baixo", "Common.define.effectData.textLeftUp": "Esquerda para cima", "Common.define.effectData.textLighten": "<PERSON><PERSON>", "Common.define.effectData.textLineColor": "<PERSON><PERSON> <PERSON> l<PERSON><PERSON>", "Common.define.effectData.textLines": "<PERSON><PERSON>", "Common.define.effectData.textLinesCurves": "<PERSON><PERSON><PERSON> de l<PERSON>", "Common.define.effectData.textLoopDeLoop": "Faça o laço", "Common.define.effectData.textLoops": "Rotações", "Common.define.effectData.textModerate": "Moderado", "Common.define.effectData.textNeutron": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textObjectCenter": "Centro do objeto", "Common.define.effectData.textObjectColor": "Cor do objeto", "Common.define.effectData.textOctagon": "Octógono", "Common.define.effectData.textOut": "Fora", "Common.define.effectData.textOutFromScreenBottom": "Para fora da parte inferior da tela", "Common.define.effectData.textOutSlightly": "Um pouco fora", "Common.define.effectData.textOutToScreenCenter": "Para fora do centro da tela", "Common.define.effectData.textParallelogram": "Paralelograma", "Common.define.effectData.textPath": "Caminhos do movimento", "Common.define.effectData.textPathCurve": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPathLine": "<PERSON><PERSON>", "Common.define.effectData.textPathScribble": "Rabisco", "Common.define.effectData.textPeanut": "Amendoim", "Common.define.effectData.textPeekIn": "Espreitar", "Common.define.effectData.textPeekOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPentagon": "Pentágono", "Common.define.effectData.textPinwheel": "Cata-vento", "Common.define.effectData.textPlus": "<PERSON><PERSON>", "Common.define.effectData.textPointStar": "Estrela do Ponto", "Common.define.effectData.textPointStar4": "Estrela de 4 pontos", "Common.define.effectData.textPointStar5": "Estrela de 5 pontos", "Common.define.effectData.textPointStar6": "Estrela de 6 pontos", "Common.define.effectData.textPointStar8": "Estrela de 8 pontos", "Common.define.effectData.textPulse": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textRandomBars": "Barras aleatórias", "Common.define.effectData.textRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textRightDown": "Direita para baixo", "Common.define.effectData.textRightTriangle": "Triângulo retângulo", "Common.define.effectData.textRightUp": "Direita para cima", "Common.define.effectData.textRiseUp": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSCurve1": "Curva S 1", "Common.define.effectData.textSCurve2": "Curva S 2", "Common.define.effectData.textShape": "Forma", "Common.define.effectData.textShapes": "Formas", "Common.define.effectData.textShimmer": "Cint<PERSON>r", "Common.define.effectData.textShrinkTurn": "Encolher e girar", "Common.define.effectData.textSineWave": "<PERSON><PERSON> seno<PERSON>", "Common.define.effectData.textSinkDown": "Afundar", "Common.define.effectData.textSlideCenter": "Centro de slides", "Common.define.effectData.textSpecial": "Especial", "Common.define.effectData.textSpin": "<PERSON><PERSON>", "Common.define.effectData.textSpinner": "Roda giratória", "Common.define.effectData.textSpiralIn": "Espiral Em", "Common.define.effectData.textSpiralLeft": "Espiral esquerdo", "Common.define.effectData.textSpiralOut": "Espiral Fora", "Common.define.effectData.textSpiralRight": "Espiral direito", "Common.define.effectData.textSplit": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSpoke1": "1 Falou", "Common.define.effectData.textSpoke2": "2 Falaram", "Common.define.effectData.textSpoke3": "3 Falaram", "Common.define.effectData.textSpoke4": "4 Falaram", "Common.define.effectData.textSpoke8": "8 Falaram", "Common.define.effectData.textSpring": "Primavera", "Common.define.effectData.textSquare": "Quadrado", "Common.define.effectData.textStairsDown": "Escadas para baixo", "Common.define.effectData.textStretch": "<PERSON><PERSON>", "Common.define.effectData.textStrips": "Tiras", "Common.define.effectData.textSubtle": "<PERSON><PERSON>", "Common.define.effectData.textSwivel": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSwoosh": "Swoosh", "Common.define.effectData.textTeardrop": "Lágrima", "Common.define.effectData.textTeeter": "Gangorra", "Common.define.effectData.textToBottom": "Para baixo", "Common.define.effectData.textToBottomLeft": "Para inferior à esquerda", "Common.define.effectData.textToBottomRight": "Para baixo à direita", "Common.define.effectData.textToLeft": "Para a esquerda", "Common.define.effectData.textToRight": "Para a direita", "Common.define.effectData.textToTop": "Para o topo", "Common.define.effectData.textToTopLeft": "Para o canto superior esquerdo", "Common.define.effectData.textToTopRight": "Para o canto superior direito", "Common.define.effectData.textTransparency": "Transparência", "Common.define.effectData.textTrapezoid": "Trapé<PERSON>", "Common.define.effectData.textTurnDown": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textTurnDownRight": "Virar para baixo à direita", "Common.define.effectData.textTurns": "Voltas", "Common.define.effectData.textTurnUp": "Virar para cima", "Common.define.effectData.textTurnUpRight": "Virar à direita", "Common.define.effectData.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textUp": "Para cima", "Common.define.effectData.textVertical": "Vertical", "Common.define.effectData.textVerticalFigure": "Figura Vertical 8", "Common.define.effectData.textVerticalIn": "Vertical para dentro", "Common.define.effectData.textVerticalOut": "Vertical para fora", "Common.define.effectData.textWave": "On<PERSON>", "Common.define.effectData.textWedge": "Triangular", "Common.define.effectData.textWheel": "Roda", "Common.define.effectData.textWhip": "Chicote", "Common.define.effectData.textWipe": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textZigzag": "ziguezague", "Common.define.effectData.textZoom": "Zoom", "Common.define.gridlineData.txtCm": "cm", "Common.define.gridlineData.txtPt": "Pt", "Common.define.smartArt.textAccentedPicture": "Imagem em destaque", "Common.define.smartArt.textAccentProcess": "Processo em Destaque", "Common.define.smartArt.textAlternatingFlow": "Fluxo alternado", "Common.define.smartArt.textAlternatingHexagons": "Hexágonos alternados", "Common.define.smartArt.textAlternatingPictureBlocks": "Blocos de imagem alternados", "Common.define.smartArt.textAlternatingPictureCircles": "Círculos de imagens alternadas", "Common.define.smartArt.textArchitectureLayout": "Layout de arquitetura", "Common.define.smartArt.textArrowRibbon": "Seta em forma de fita", "Common.define.smartArt.textAscendingPictureAccentProcess": "Processo de ênfase da imagem ascendente", "Common.define.smartArt.textBalance": "<PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Processo curvo básico", "Common.define.smartArt.textBasicBlockList": "Lista básica de blocos", "Common.define.smartArt.textBasicChevronProcess": "Processo básico em divisas", "Common.define.smartArt.textBasicCycle": "Ciclo básico", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textBasicPie": "Gráfico de pizza básico", "Common.define.smartArt.textBasicProcess": "Processo básico", "Common.define.smartArt.textBasicPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Common.define.smartArt.textBasicRadial": "Radial básico", "Common.define.smartArt.textBasicTarget": "Alvo básico", "Common.define.smartArt.textBasicTimeline": "Linha do tempo básica", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON>n b<PERSON><PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Lista de destaque da imagem de curvatura", "Common.define.smartArt.textBendingPictureBlocks": "Blocos de imagem de curvatura", "Common.define.smartArt.textBendingPictureCaption": "Legenda de imagem de curvatura", "Common.define.smartArt.textBendingPictureCaptionList": "Lista de legendas de imagens de curvatura", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Texto semi-transparente de imagem de curvatura", "Common.define.smartArt.textBlockCycle": "Ciclo em bloco", "Common.define.smartArt.textBubblePictureList": "Lista de imagens em bolha", "Common.define.smartArt.textCaptionedPictures": "Imagens legendadas", "Common.define.smartArt.textChevronAccentProcess": "Processo de destaque em divisas", "Common.define.smartArt.textChevronList": "Lista de divisas", "Common.define.smartArt.textCircleAccentTimeline": "Linha do tempo de destaque circular", "Common.define.smartArt.textCircleArrowProcess": "Processo de seta circular", "Common.define.smartArt.textCirclePictureHierarchy": "Hierarquia de imagem circular", "Common.define.smartArt.textCircleProcess": "Processo circular", "Common.define.smartArt.textCircleRelationship": "Relacionamento circular", "Common.define.smartArt.textCircularBendingProcess": "Processo curvo circular", "Common.define.smartArt.textCircularPictureCallout": "Texto explicativo de imagem circular", "Common.define.smartArt.textClosedChevronProcess": "Processo fechado em divisas", "Common.define.smartArt.textContinuousArrowProcess": "Processo de seta contínua", "Common.define.smartArt.textContinuousBlockProcess": "Processo de bloco contínuo", "Common.define.smartArt.textContinuousCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textContinuousPictureList": "Lista de imagem contínua", "Common.define.smartArt.textConvergingArrows": "Setas convergentes", "Common.define.smartArt.textConvergingRadial": "Radial convergente", "Common.define.smartArt.textConvergingText": "Texto convergente", "Common.define.smartArt.textCounterbalanceArrows": "Setas contrabalançadas ", "Common.define.smartArt.textCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textDescendingBlockList": "Lista de blocos descendente  ", "Common.define.smartArt.textDescendingProcess": "Processo descendente", "Common.define.smartArt.textDetailedProcess": "<PERSON><PERSON> de<PERSON>", "Common.define.smartArt.textDivergingArrows": "Flechas divergentes", "Common.define.smartArt.textDivergingRadial": "Radial divergente", "Common.define.smartArt.textEquation": "Equação", "Common.define.smartArt.textFramedTextPicture": "Imagem de texto emoldurada", "Common.define.smartArt.textFunnel": "Funil", "Common.define.smartArt.textGear": "Engrenagem", "Common.define.smartArt.textGridMatrix": "<PERSON><PERSON> de <PERSON>", "Common.define.smartArt.textGroupedList": "Lista agrupada", "Common.define.smartArt.textHalfCircleOrganizationChart": "Organograma de meio círculo", "Common.define.smartArt.textHexagonCluster": "Conjunto hexagonal", "Common.define.smartArt.textHexagonRadial": "Radial hex<PERSON>", "Common.define.smartArt.textHierarchy": "Hierarquia", "Common.define.smartArt.textHierarchyList": "Lista de hierarquia", "Common.define.smartArt.textHorizontalBulletList": "Lista de marcadores horizontais", "Common.define.smartArt.textHorizontalHierarchy": "Hierarquia horizontal", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Hierarquia horizontal rotulada", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Hierarquia horizontal multinível", "Common.define.smartArt.textHorizontalOrganizationChart": "Organograma horizontal", "Common.define.smartArt.textHorizontalPictureList": "Lista de imagens horizontais", "Common.define.smartArt.textIncreasingArrowProcess": "Processo de seta crescente", "Common.define.smartArt.textIncreasingCircleProcess": "Processo de círculo crescente", "Common.define.smartArt.textInterconnectedBlockProcess": "Processo de bloco interconectado", "Common.define.smartArt.textInterconnectedRings": "An<PERSON>is interconectad<PERSON>", "Common.define.smartArt.textInvertedPyramid": "Pirâmide invertida", "Common.define.smartArt.textLabeledHierarchy": "Hierarquia rotulada", "Common.define.smartArt.textLinearVenn": "Venn Linear", "Common.define.smartArt.textLinedList": "<PERSON>a alinhada", "Common.define.smartArt.textList": "Lista", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Ciclo multidirecional", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organograma de nome e título", "Common.define.smartArt.textNestedTarget": "<PERSON><PERSON>o", "Common.define.smartArt.textNondirectionalCycle": "Ciclo não direcional", "Common.define.smartArt.textOpposingArrows": "Setas opostas", "Common.define.smartArt.textOpposingIdeas": "Ideias opost<PERSON>", "Common.define.smartArt.textOrganizationChart": "Organograma", "Common.define.smartArt.textOther": "Outro", "Common.define.smartArt.textPhasedProcess": "Processo em fases", "Common.define.smartArt.textPicture": "Imagem", "Common.define.smartArt.textPictureAccentBlocks": "Blocos de destaque de imagem", "Common.define.smartArt.textPictureAccentList": "Lista de destaques da imagem", "Common.define.smartArt.textPictureAccentProcess": "Processo de destaque da imagem", "Common.define.smartArt.textPictureCaptionList": "Lista de legendas de imagens", "Common.define.smartArt.textPictureFrame": "Porta-retrato", "Common.define.smartArt.textPictureGrid": "Grade de imagens", "Common.define.smartArt.textPictureLineup": "Alinhamento de imagens", "Common.define.smartArt.textPictureOrganizationChart": "Organograma de imagens", "Common.define.smartArt.textPictureStrips": "Tiras de imagem", "Common.define.smartArt.textPieProcess": "Processo em Pizza", "Common.define.smartArt.textPlusAndMinus": "Mais e menos", "Common.define.smartArt.textProcess": "Processo", "Common.define.smartArt.textProcessArrows": "Setas de processo", "Common.define.smartArt.textProcessList": "Lista de processos", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "Lista de pirâmides", "Common.define.smartArt.textRadialCluster": "Aglomerado radial", "Common.define.smartArt.textRadialCycle": "Ciclo radial", "Common.define.smartArt.textRadialList": "Lista radial", "Common.define.smartArt.textRadialPictureList": "Lista de imagens radiais", "Common.define.smartArt.textRadialVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRandomToResultProcess": "Processo aleatório para resultado", "Common.define.smartArt.textRelationship": "Relação", "Common.define.smartArt.textRepeatingBendingProcess": "Processo curvo de repetição", "Common.define.smartArt.textReverseList": "Lista reversa", "Common.define.smartArt.textSegmentedCycle": "Ciclo segmentado", "Common.define.smartArt.textSegmentedProcess": "Processo segmentado", "Common.define.smartArt.textSegmentedPyramid": "Pirâmide segmentada", "Common.define.smartArt.textSnapshotPictureList": "Lista de fotos instantâneas", "Common.define.smartArt.textSpiralPicture": "Imagem em espiral", "Common.define.smartArt.textSquareAccentList": "Lista de destaque quadrada", "Common.define.smartArt.textStackedList": "Lista empilhada", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "Processo escalonado", "Common.define.smartArt.textStepDownProcess": "Processo de redução", "Common.define.smartArt.textStepUpProcess": "Processo de intensificação", "Common.define.smartArt.textSubStepProcess": "Processo de subetapas", "Common.define.smartArt.textTabbedArc": "Arco com abas", "Common.define.smartArt.textTableHierarchy": "Hierar<PERSON><PERSON> da tabela", "Common.define.smartArt.textTableList": "Lista de tabelas", "Common.define.smartArt.textTabList": "Lista de guias", "Common.define.smartArt.textTargetList": "Lista de alvos", "Common.define.smartArt.textTextCycle": "Ciclo de texto", "Common.define.smartArt.textThemePictureAccent": "Destaque da imagem de tema", "Common.define.smartArt.textThemePictureAlternatingAccent": "Destaque alternado da imagem do tema", "Common.define.smartArt.textThemePictureGrid": "Grade de imagens do tema", "Common.define.smartArt.textTitledMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textTitledPictureAccentList": "Lista de destaque de imagem intitulada", "Common.define.smartArt.textTitledPictureBlocks": "Blocos de imagens intitulados", "Common.define.smartArt.textTitlePictureLineup": "Alinhamento da imagem do título", "Common.define.smartArt.textTrapezoidList": "Lista trapezoidal", "Common.define.smartArt.textUpwardArrow": "Seta para cima", "Common.define.smartArt.textVaryingWidthList": "Lista de largura variável", "Common.define.smartArt.textVerticalAccentList": "Lista de destaque vertical", "Common.define.smartArt.textVerticalArrowList": "Lista de setas verticais", "Common.define.smartArt.textVerticalBendingProcess": "Processo vertical em curva", "Common.define.smartArt.textVerticalBlockList": "Lista de bloqueio vertical", "Common.define.smartArt.textVerticalBoxList": "Lista de caixas verticais", "Common.define.smartArt.textVerticalBracketList": "Lista de colchetes verticais", "Common.define.smartArt.textVerticalBulletList": "Lista de marcadores verticais", "Common.define.smartArt.textVerticalChevronList": "Lista vertical em divisas", "Common.define.smartArt.textVerticalCircleList": "Lista de círculos verticais", "Common.define.smartArt.textVerticalCurvedList": "Lista vertical curva", "Common.define.smartArt.textVerticalEquation": "Equação vertical", "Common.define.smartArt.textVerticalPictureAccentList": "Lista de destaque de imagens verticais", "Common.define.smartArt.textVerticalPictureList": "Lista de imagens verticais", "Common.define.smartArt.textVerticalProcess": "Processo vertical", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.tipFileLocked": "O documento está bloqueado para edição. Você pode fazer alterações e salvá-lo como cópia local mais tarde.", "Common.Translation.tipFileReadOnly": "O documento é somente leitura e está bloqueado para edição. Você pode fazer alterações e salvar sua cópia local posteriormente.", "Common.Translation.warnFileLocked": "Documento está em uso por outra aplicação. Você pode continuar editando e salvá-lo como uma cópia.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON><PERSON> uma c<PERSON>pia", "Common.Translation.warnFileLockedBtnView": "Aberto para visualização", "Common.UI.ButtonColored.textAutoColor": "Automático", "Common.UI.ButtonColored.textEyedropper": "Conta-gotas", "Common.UI.ButtonColored.textNewColor": "Mais cores", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON> bordas", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON> bordas", "Common.UI.ComboDataView.emptyComboText": "Sem estilos", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Atual", "Common.UI.ExtendedColorDialog.textHexErr": "O valor inserido está incorreto.<br>Insira um valor entre 000000 e FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Novo", "Common.UI.ExtendedColorDialog.textRGBErr": "O valor inserido está incorreto.<br>Insira um valor numérico entre 0 e 255.", "Common.UI.HSBColorPicker.textNoColor": "Sem cor", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Ocultar palavra-chave", "Common.UI.InputFieldBtnPassword.textHintHold": "Pressione e segure para mostrar a senha", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON> senha", "Common.UI.SearchBar.textFind": "Localizar", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>es<PERSON>", "Common.UI.SearchBar.tipNextResult": "Próximo resultado", "Common.UI.SearchBar.tipOpenAdvancedSettings": "<PERSON><PERSON> as configura<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipPreviousResult": "Resultado anterior", "Common.UI.SearchDialog.textHighlight": "Destacar resultados", "Common.UI.SearchDialog.textMatchCase": "Diferenciar maiús<PERSON>s de minúsculas", "Common.UI.SearchDialog.textReplaceDef": "Inserir o texto de substituição", "Common.UI.SearchDialog.textSearchStart": "Inserir seu texto aqui", "Common.UI.SearchDialog.textTitle": "Localizar e substituir", "Common.UI.SearchDialog.textTitle2": "Localizar", "Common.UI.SearchDialog.textWholeWords": "Palavras inteiras apenas", "Common.UI.SearchDialog.txtBtnHideReplace": "Ocultar Substituição", "Common.UI.SearchDialog.txtBtnReplace": "Substituir", "Common.UI.SearchDialog.txtBtnReplaceAll": "Substituir tudo", "Common.UI.SynchronizeTip.textDontShow": "Não exibir esta mensagem novamente", "Common.UI.SynchronizeTip.textGotIt": "<PERSON><PERSON><PERSON>", "Common.UI.SynchronizeTip.textSynchronize": "O documento foi alterado por outro usuário.<br>Clique para salvar suas alterações e recarregar as atualizações.", "Common.UI.ThemeColorPalette.textRecentColors": "Cores recentes", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON> padron<PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Cores de tema", "Common.UI.Themes.txtThemeClassicLight": "Clássico claro", "Common.UI.Themes.txtThemeContrastDark": "Contraste escuro", "Common.UI.Themes.txtThemeDark": "Escuro", "Common.UI.Themes.txtThemeGray": "Cinza", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "O mesmo que sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Não", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmação", "Common.UI.Window.textDontShow": "Não exibir esta mensagem novamente", "Common.UI.Window.textError": "Erro", "Common.UI.Window.textInformation": "Informações", "Common.UI.Window.textWarning": "Aviso", "Common.UI.Window.yesButtonText": "<PERSON>m", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "Pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Ace<PERSON>", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Plano de fundo", "Common.Utils.ThemeColor.txtBlack": "Preto", "Common.Utils.ThemeColor.txtBlue": "Azul", "Common.Utils.ThemeColor.txtBrightGreen": "Verde claro", "Common.Utils.ThemeColor.txtBrown": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "A<PERSON>l escuro", "Common.Utils.ThemeColor.txtDarker": "<PERSON><PERSON> es<PERSON>ra", "Common.Utils.ThemeColor.txtDarkGray": "Cinza escuro", "Common.Utils.ThemeColor.txtDarkGreen": "Verde-escuro", "Common.Utils.ThemeColor.txtDarkPurple": "Roxo escuro", "Common.Utils.ThemeColor.txtDarkRed": "<PERSON>er<PERSON><PERSON> es<PERSON>ro", "Common.Utils.ThemeColor.txtDarkTeal": "Verde-azulado escuro", "Common.Utils.ThemeColor.txtDarkYellow": "<PERSON><PERSON> es<PERSON>ro", "Common.Utils.ThemeColor.txtGold": "Ouro", "Common.Utils.ThemeColor.txtGray": "Cinza", "Common.Utils.ThemeColor.txtGreen": "Verde", "Common.Utils.ThemeColor.txtIndigo": "Índigo", "Common.Utils.ThemeColor.txtLavender": "Lavan<PERSON>", "Common.Utils.ThemeColor.txtLightBlue": "<PERSON><PERSON>l claro", "Common.Utils.ThemeColor.txtLighter": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightGray": "Cinza claro", "Common.Utils.ThemeColor.txtLightGreen": "Luz verde", "Common.Utils.ThemeColor.txtLightOrange": "Laranja claro", "Common.Utils.ThemeColor.txtLightYellow": "Luz amarela", "Common.Utils.ThemeColor.txtOrange": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtPink": "<PERSON>", "Common.Utils.ThemeColor.txtPurple": "Roxo", "Common.Utils.ThemeColor.txtRed": "Vermelho", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "C<PERSON>u a<PERSON>l", "Common.Utils.ThemeColor.txtTeal": "Azul-petróleo", "Common.Utils.ThemeColor.txttext": "Тexto", "Common.Utils.ThemeColor.txtTurquosie": "Turquesa", "Common.Utils.ThemeColor.txtViolet": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtWhite": "Branco", "Common.Utils.ThemeColor.txtYellow": "<PERSON><PERSON>", "Common.Views.About.txtAddress": "endereço:", "Common.Views.About.txtLicensee": "LICENÇA", "Common.Views.About.txtLicensor": "LICENCIANTE", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "Desenvolvido por", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Vers<PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "Incluir", "Common.Views.AutoCorrectDialog.textApplyText": "Aplicar enquanto você digita", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autocorreção", "Common.Views.AutoCorrectDialog.textAutoFormat": "Auto Formatação conforme você digita", "Common.Views.AutoCorrectDialog.textBulleted": "Listas com marcadores automáticas", "Common.Views.AutoCorrectDialog.textBy": "Por", "Common.Views.AutoCorrectDialog.textDelete": "Excluir", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Adicionar ponto com espaço duplo", "Common.Views.AutoCorrectDialog.textFLCells": "Capitalizar a primeira letra das células da tabela", "Common.Views.AutoCorrectDialog.textFLDont": "Não colocar a primeira letra em maiúscula após", "Common.Views.AutoCorrectDialog.textFLSentence": "Capitalizar a primeira carta de sentenças", "Common.Views.AutoCorrectDialog.textForLangFL": "Exceções para o idioma:", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet e caminhos de rede com hyperlinks", "Common.Views.AutoCorrectDialog.textHyphens": "<PERSON><PERSON><PERSON>s (--) com traço (-)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autocorreção matemática", "Common.Views.AutoCorrectDialog.textNumbered": "Listas com numeradores automáticos", "Common.Views.AutoCorrectDialog.textQuotes": "\"Aspas retas\" com \"aspas inteligentes\"", "Common.Views.AutoCorrectDialog.textRecognized": "Funções Reconhecidas", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "As seguintes expressões são expressões matemáticas reconhecidas. Eles não ficarão em itálico automaticamente.", "Common.Views.AutoCorrectDialog.textReplace": "Substituir", "Common.Views.AutoCorrectDialog.textReplaceText": "Substituir ao Digitar", "Common.Views.AutoCorrectDialog.textReplaceType": "Substitua o texto enquanto você digita", "Common.Views.AutoCorrectDialog.textReset": "Redefinir", "Common.Views.AutoCorrectDialog.textResetAll": "Restaurar para padrão", "Common.Views.AutoCorrectDialog.textRestore": "Restaurar", "Common.Views.AutoCorrectDialog.textTitle": "Autocorreção", "Common.Views.AutoCorrectDialog.textWarnAddFL": "As exceções devem conter apenas letras, mai<PERSON><PERSON>s ou minúsculas.", "Common.Views.AutoCorrectDialog.textWarnAddRec": "As funções reconhecidas devem conter apenas as letras de A a Z, maiúsculas ou minúsculas.", "Common.Views.AutoCorrectDialog.textWarnResetFL": "Quaisquer exceções que você adicionou serão removidas e as removidas serão restauradas. Deseja continuar?", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Qualquer expressão que tenha acrescentado será removida e as expressões removidas serão restauradas. Quer continuar?", "Common.Views.AutoCorrectDialog.warnReplace": "A correção automática para %1 já existe. Quer substituir?", "Common.Views.AutoCorrectDialog.warnReset": "Qualquer autocorrecção que tenha adicionado será removida e as alterações serão restauradas aos seus valores originais. Quer continuar?", "Common.Views.AutoCorrectDialog.warnRestore": "A entrada de autocorreção para %1 será redefinida para seu valor original. Você quer continuar?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "<PERSON><PERSON><PERSON> chat", "Common.Views.Chat.textEnterMessage": "Insira sua mensagem aqui", "Common.Views.Chat.textSend": "Enviar", "Common.Views.Comments.mniAuthorAsc": "Autor de A a Z", "Common.Views.Comments.mniAuthorDesc": "Autor Z a A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON> anti<PERSON>", "Common.Views.Comments.mniDateDesc": "Novidades", "Common.Views.Comments.mniFilterGroups": "Filtrar por grupo", "Common.Views.Comments.mniPositionAsc": "De cima", "Common.Views.Comments.mniPositionDesc": "Do fundo", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Adicionar comentário ao documento", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON> resposta", "Common.Views.Comments.textAll": "<PERSON><PERSON>", "Common.Views.Comments.textAnonym": "Visitante", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Comentários próximos", "Common.Views.Comments.textComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textComments": "Comentários", "Common.Views.Comments.textEdit": "<PERSON><PERSON>", "Common.Views.Comments.textEnterCommentHint": "Inserir seu coment<PERSON>rio aqui", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Abrir novamente", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Resolver", "Common.Views.Comments.textResolved": "Resolvido", "Common.Views.Comments.textSort": "Ordenar comentá<PERSON>s", "Common.Views.Comments.textSortFilter": "Classifique e filtre comentários", "Common.Views.Comments.textSortFilterMore": "Classificar, filtrar e muito mais", "Common.Views.Comments.textSortMore": "Classificar e muito mais", "Common.Views.Comments.textViewResolved": "Não tem permissão para reabrir comentários", "Common.Views.Comments.txtEmpty": "Não há comentários no documento.", "Common.Views.CopyWarningDialog.textDontShow": "Não exibir esta mensagem novamente", "Common.Views.CopyWarningDialog.textMsg": "As ações copiar, cortar e colar usando os botões da barra de ferramentas do editor e as ações de menu de contexto serão realizadas apenas nesta aba do editor.<br><br>Para copiar ou colar para ou de aplicativos externos a aba do editor, use as seguintes combinações do teclado:", "Common.Views.CopyWarningDialog.textTitle": "Copiar, Cortar e Colar", "Common.Views.CopyWarningDialog.textToCopy": "para Copiar", "Common.Views.CopyWarningDialog.textToCut": "para Cortar", "Common.Views.CopyWarningDialog.textToPaste": "para Colar", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Baixar", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Verifique os comandos que serão exibidos na Barra de Ferramentas de Acesso Rápido", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Imprimir", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Impressão rápida", "Common.Views.CustomizeQuickAccessDialog.textRedo": "<PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textSave": "<PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textStartOver": "Mostrar desde o início", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Personalize o acesso rápido", "Common.Views.CustomizeQuickAccessDialog.textUndo": "<PERSON><PERSON><PERSON>", "Common.Views.DocumentAccessDialog.textLoading": "Carregando...", "Common.Views.DocumentAccessDialog.textTitle": "Configurações de compartilhamento", "Common.Views.DocumentPropertyDialog.errorDate": "Você pode escolher um valor do calendário para armazenar o valor como Data.<br>Se você inserir um valor manualmente, ele será armazenado como Texto.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "Não", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "<PERSON>m", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "A propriedade deve ter um título", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "“Sim” ou ”<PERSON>”", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Data", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Tipo", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Número", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Forneça um número válido", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Тexto", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "A propriedade deve ter um valor", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Valor", "Common.Views.DocumentPropertyDialog.txtTitle": "Propriedade do novo documento", "Common.Views.Draw.hintEraser": "Apagador", "Common.Views.Draw.hintSelect": "Selecionar", "Common.Views.Draw.txtEraser": "Apagador", "Common.Views.Draw.txtHighlighter": "Marcador", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Caneta", "Common.Views.Draw.txtSelect": "Selecionar", "Common.Views.Draw.txtSize": "<PERSON><PERSON><PERSON>", "Common.Views.ExternalDiagramEditor.textTitle": "Editor de gráfico", "Common.Views.ExternalEditor.textClose": "En<PERSON><PERSON>", "Common.Views.ExternalEditor.textSave": "<PERSON><PERSON> e <PERSON>", "Common.Views.ExternalOleEditor.textTitle": "Editor <PERSON> <PERSON><PERSON>", "Common.Views.Header.ariaQuickAccessToolbar": "Barra de ferramentas de acesso rápido", "Common.Views.Header.labelCoUsersDescr": "O documento atualmente está sendo editado por vários usuários.", "Common.Views.Header.textAddFavorite": "Marcar como favorito", "Common.Views.Header.textAdvSettings": "Configurações avançadas", "Common.Views.Header.textBack": "Localização do arquivo aberto", "Common.Views.Header.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textCompactView": "Ocultar Barra de Ferramentas", "Common.Views.Header.textHideLines": "Ocultar r<PERSON>", "Common.Views.Header.textHideNotes": "<PERSON><PERSON><PERSON><PERSON> notas", "Common.Views.Header.textHideStatusBar": "Ocultar barra de status", "Common.Views.Header.textPrint": "Imprimir", "Common.Views.Header.textReadOnly": "<PERSON>nte leitura", "Common.Views.Header.textRemoveFavorite": "Remover dos Favoritos", "Common.Views.Header.textSaveBegin": "Salvando...", "Common.Views.Header.textSaveChanged": "Modificado", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON> as alteraç<PERSON>es foram salvas", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON> as alteraç<PERSON>es foram salvas", "Common.Views.Header.textShare": "Compartilhar", "Common.Views.Header.textStartOver": "Mostrar desde o início", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Gerenciar direitos de acesso ao documento", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Personalize a barra de ferramentas de acesso rápido", "Common.Views.Header.tipDownload": "Transferir arquivo", "Common.Views.Header.tipGoEdit": "Editar arquivo atual", "Common.Views.Header.tipPrint": "Imprimir arquivo", "Common.Views.Header.tipPrintQuick": "Impressão rápida", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "<PERSON><PERSON>", "Common.Views.Header.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipStartOver": "Iniciar a apresentação de slides desde o início", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Desencaixe em janela separada", "Common.Views.Header.tipUsers": "Ver usuários", "Common.Views.Header.tipViewSettings": "Configurações de exibição", "Common.Views.Header.tipViewUsers": "Ver usuários e gerenciar direitos de acesso ao documento", "Common.Views.Header.txtAccessRights": "Alterar direitos de acesso", "Common.Views.Header.txtRename": "Renomear", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Ocultar alterações detalhadas ", "Common.Views.History.textHighlightDeleted": "Destaque excluído", "Common.Views.History.textMore": "<PERSON><PERSON>", "Common.Views.History.textRestore": "Restaurar", "Common.Views.History.textShowAll": "Mostrar alterações detalhadas", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Histórico <PERSON>ão", "Common.Views.ImageFromUrlDialog.textUrl": "Colar uma URL de imagem:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Este campo deve ser uma URL no formato \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Você precisa especificar números de linhas e colunas válidos.", "Common.Views.InsertTableDialog.txtColumns": "Número de <PERSON>nas", "Common.Views.InsertTableDialog.txtMaxText": "O valor máximo para este campo é {0}.", "Common.Views.InsertTableDialog.txtMinText": "O valor mínimo para este campo é {0}.", "Common.Views.InsertTableDialog.txtRows": "Número de l<PERSON>has", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON><PERSON>", "Common.Views.LanguageDialog.labelSelect": "Selecionar idioma do documento", "Common.Views.ListSettingsDialog.textBulleted": "Marcadores", "Common.Views.ListSettingsDialog.textFromFile": "Do Arquivo", "Common.Views.ListSettingsDialog.textFromStorage": "De armazenamento", "Common.Views.ListSettingsDialog.textFromUrl": "Da URL", "Common.Views.ListSettingsDialog.textNumbering": "Numerado", "Common.Views.ListSettingsDialog.textSelect": "Selecione de", "Common.Views.ListSettingsDialog.tipChange": "Alterar marcador", "Common.Views.ListSettingsDialog.txtBullet": "Marcador", "Common.Views.ListSettingsDialog.txtColor": "Cor", "Common.Views.ListSettingsDialog.txtImage": "Imagem", "Common.Views.ListSettingsDialog.txtImport": "Importação", "Common.Views.ListSettingsDialog.txtNewBullet": "Novo marcador", "Common.Views.ListSettingsDialog.txtNewImage": "Nova imagem", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% do texto", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON><PERSON> em", "Common.Views.ListSettingsDialog.txtSymbol": "Símbolo", "Common.Views.ListSettingsDialog.txtTitle": "Configurações da lista", "Common.Views.ListSettingsDialog.txtType": "Tipo", "Common.Views.MacrosDialog.textCopy": "Copiar", "Common.Views.MacrosDialog.textCustomFunction": "Função personalizada", "Common.Views.MacrosDialog.textDelete": "Excluir", "Common.Views.MacrosDialog.textLoading": "Carregando...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Faça o início automático", "Common.Views.MacrosDialog.textRename": "Renomear", "Common.Views.MacrosDialog.textRun": "Executar", "Common.Views.MacrosDialog.textSave": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Desfazer inicialização automática", "Common.Views.MacrosDialog.tipFunctionAdd": "Adicionar função personalizada", "Common.Views.MacrosDialog.tipMacrosAdd": "Adicionar macros", "Common.Views.MacrosDialog.tipMacrosRun": "Executar", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtEncoding": "Codificação", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON> incorreta.", "Common.Views.OpenDialog.txtOpenFile": "Inserir a Senha para Abrir o Arquivo", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "Depois de inserir a senha e abrir o arquivo, a senha atual do arquivo será redefinida.", "Common.Views.OpenDialog.txtTitle": "Escolher opções %1", "Common.Views.OpenDialog.txtTitleProtected": "Arquivo protegido", "Common.Views.PasswordDialog.txtDescription": "Defina uma senha para proteger o documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "A senha de confirmação não é idêntica", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON>r a senha", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtWarning": "Cuidado: se você perder ou esquecer a senha, não será possível recuperá-la. Guarde-o em local seguro.", "Common.Views.PluginDlg.textLoading": "Carregamento", "Common.Views.PluginPanel.textClosePanel": "Fechar plug-in", "Common.Views.PluginPanel.textLoading": "Carregando", "Common.Views.Plugins.groupCaption": "Plug-ins", "Common.Views.Plugins.strPlugins": "Plug-ins", "Common.Views.Plugins.textBackgroundPlugins": "Plug-ins em segundo plano", "Common.Views.Plugins.textSettings": "Configurações", "Common.Views.Plugins.textStart": "Iniciar", "Common.Views.Plugins.textStop": "<PERSON><PERSON>", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "A lista de plug-ins de segundo plano", "Common.Views.Plugins.tipMore": "<PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Criptografar com senha", "Common.Views.Protection.hintDelPwd": "Excluir senha", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON> ou excluir senha", "Common.Views.Protection.hintSignature": "Inserir assinatura digital ou linha de assinatura", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "Excluir senha", "Common.Views.Protection.txtEncrypt": "Criptografar", "Common.Views.Protection.txtInvisibleSignature": "Adicionar assinatura digital", "Common.Views.Protection.txtSignature": "Assinatura", "Common.Views.Protection.txtSignatureLine": "Adicionar linha de assinatura", "Common.Views.RecentFiles.txtOpenRecent": "Abrir recente", "Common.Views.RenameDialog.textName": "Nome do arquivo", "Common.Views.RenameDialog.txtInvalidName": "Nome de arquivo não pode conter os seguintes caracteres:", "Common.Views.ReviewChanges.hintNext": "Para a próxima alteração", "Common.Views.ReviewChanges.hintPrev": "Para a alteração anterior", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Coedição em tempo real. Todas as alterações são salvas automaticamente.", "Common.Views.ReviewChanges.strStrict": "Estrito", "Common.Views.ReviewChanges.strStrictDesc": "Use o botão 'Salvar' para sincronizar as alterações que você e outros realizaram.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aceitar alteração atual", "Common.Views.ReviewChanges.tipCoAuthMode": "Definir modo de coedição", "Common.Views.ReviewChanges.tipCommentRem": "Remover comentários", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Remover comentários atuais", "Common.Views.ReviewChanges.tipCommentResolve": "Resolver comentários", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolver comentários atuais", "Common.Views.ReviewChanges.tipHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipRejectCurrent": "Rejeitar alterações atuais", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Selecione o modo em que você deseja que as alterações sejam exibidas", "Common.Views.ReviewChanges.tipSetDocLang": "Definir idioma do documento", "Common.Views.ReviewChanges.tipSetSpelling": "Verificação ortográfica", "Common.Views.ReviewChanges.tipSharing": "Gerenciar direitos de acesso ao documento", "Common.Views.ReviewChanges.txtAccept": "Aceitar", "Common.Views.ReviewChanges.txtAcceptAll": "Aceitar to<PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptChanges": "Aceitar alterações", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aceitar alteração atual", "Common.Views.ReviewChanges.txtChat": "Gráfico", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modo de coedição", "Common.Views.ReviewChanges.txtCommentRemAll": "Excluir Todos os Comentários", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Excluir comentários atuais", "Common.Views.ReviewChanges.txtCommentRemMy": "Excluir meus comentários", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Remover meus comentários atuais", "Common.Views.ReviewChanges.txtCommentRemove": "Remover", "Common.Views.ReviewChanges.txtCommentResolve": "Resolver", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolver todos os comentários", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolver comentários atuais", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolver meus comentários", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolver meus comentários atuais", "Common.Views.ReviewChanges.txtDocLang": "Idioma", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON> as alter<PERSON><PERSON><PERSON><PERSON> (Visualizar)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Histórico de versões", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> as alteraç<PERSON><PERSON> (Edição)", "Common.Views.ReviewChanges.txtMarkupCap": "Marcação", "Common.Views.ReviewChanges.txtNext": "Próximo", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON> as altera<PERSON><PERSON><PERSON> reje<PERSON> (Visualizar)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Anterior", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON><PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectChanges": "Rejeitar alterações", "Common.Views.ReviewChanges.txtRejectCurrent": "Rejeitar alteração atual", "Common.Views.ReviewChanges.txtSharing": "Compartilhar", "Common.Views.ReviewChanges.txtSpelling": "Verificação ortográfica", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "Modo de exibição", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON><PERSON> resposta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Insira seu coment<PERSON>rio aqui", "Common.Views.ReviewPopover.textMention": "+menção fornecerá acesso ao documento e enviará um e-mail", "Common.Views.ReviewPopover.textMentionNotify": "+menção notificará o usuário por e-mail", "Common.Views.ReviewPopover.textOpenAgain": "Abrir novamente", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Resolver", "Common.Views.ReviewPopover.textViewResolved": "Não tem permissão para reabrir comentários", "Common.Views.ReviewPopover.txtDeleteTip": "Excluir", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Carregando", "Common.Views.SaveAsDlg.textTitle": "Pasta para salvar", "Common.Views.SearchPanel.textCaseSensitive": "Diferenciar maiús<PERSON>s de minúsculas", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>es<PERSON>", "Common.Views.SearchPanel.textContentChanged": "Documento alterado.", "Common.Views.SearchPanel.textFind": "Localizar", "Common.Views.SearchPanel.textFindAndReplace": "Localizar e substituir", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} itens substituídos com sucesso.", "Common.Views.SearchPanel.textMatchUsingRegExp": "Corresponder usando expressões regulares", "Common.Views.SearchPanel.textNoMatches": "Nenhuma correspondê<PERSON>", "Common.Views.SearchPanel.textNoSearchResults": "Nenhum resultado de pesquisa", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} itens substituídos. Os {2} itens restantes estão bloqueados por outros usuários.", "Common.Views.SearchPanel.textReplace": "Substituir", "Common.Views.SearchPanel.textReplaceAll": "Substituir tudo", "Common.Views.SearchPanel.textReplaceWith": "Substituir com", "Common.Views.SearchPanel.textSearchAgain": "{0}Realize uma nova pesquisa{1} para obter resultados precisos.", "Common.Views.SearchPanel.textSearchHasStopped": "A pesquisa parou", "Common.Views.SearchPanel.textSearchResults": "Resultados da pesquisa: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Resultados da pesquisa", "Common.Views.SearchPanel.textTooManyResults": "Há muitos resultados para mostrar aqui", "Common.Views.SearchPanel.textWholeWords": "Palavras inteiras apenas", "Common.Views.SearchPanel.tipNextResult": "Próximo resultado", "Common.Views.SearchPanel.tipPreviousResult": "Resultado anterior", "Common.Views.SelectFileDlg.textLoading": "Carregando", "Common.Views.SelectFileDlg.textTitle": "Selecionar fonte de dados", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distância", "Common.Views.ShapeShadowDialog.txtSize": "<PERSON><PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtTitle": "Ajustar sombra", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparência", "Common.Views.SignDialog.textBold": "Negrito", "Common.Views.SignDialog.textCertificate": "Certificado", "Common.Views.SignDialog.textChange": "Alterar", "Common.Views.SignDialog.textInputName": "Campo Nome do assinante", "Common.Views.SignDialog.textItalic": "Itálico", "Common.Views.SignDialog.textNameError": "Nome de assinante não deve estar vazio.", "Common.Views.SignDialog.textPurpose": "Objetivo para assinar o documento", "Common.Views.SignDialog.textSelect": "Selecionar", "Common.Views.SignDialog.textSelectImage": "Selecionar Imagem", "Common.Views.SignDialog.textSignature": "Ver assinatura como", "Common.Views.SignDialog.textTitle": "Assinar o Documento", "Common.Views.SignDialog.textUseImage": "ou clique em 'Selecionar imagem' para usar uma imagem como assinatura", "Common.Views.SignDialog.textValid": "Válido de %1 a %2", "Common.Views.SignDialog.tipFontName": "Nome da Fonte", "Common.Views.SignDialog.tipFontSize": "<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.textAllowComment": "Permitir que o assinante adicione um comentário na caixa de diálogo de assinatura", "Common.Views.SignSettingsDialog.textDefInstruction": "Antes de assinar este documento, verifique se o conteúdo que está a assinar está correto.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "Nome", "Common.Views.SignSettingsDialog.textInfoTitle": "Título do Signatário", "Common.Views.SignSettingsDialog.textInstructions": "Instruções para o Assinante", "Common.Views.SignSettingsDialog.textShowDate": "Exibir a data da assinatura na linha da assinatura", "Common.Views.SignSettingsDialog.textTitle": "Configurações da Assinatura", "Common.Views.SignSettingsDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.SymbolTableDialog.textCharacter": "Caractere", "Common.Views.SymbolTableDialog.textCode": "Valor Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Assinatura de copyright", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON> duplas", "Common.Views.SymbolTableDialog.textDOQuote": "Abertura de aspas duplas", "Common.Views.SymbolTableDialog.textEllipsis": "Elipse horizontal", "Common.Views.SymbolTableDialog.textEmDash": "Travessão", "Common.Views.SymbolTableDialog.textEmSpace": "Em Espaço", "Common.Views.SymbolTableDialog.textEnDash": "Travessão", "Common.Views.SymbolTableDialog.textEnSpace": "Espaço", "Common.Views.SymbolTableDialog.textFont": "Fonte", "Common.Views.SymbolTableDialog.textNBHyphen": "Hífen sem quebra", "Common.Views.SymbolTableDialog.textNBSpace": "Espaço sem interrupção", "Common.Views.SymbolTableDialog.textPilcrow": "Sinal de antígrafo", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em Espaço", "Common.Views.SymbolTableDialog.textRange": "Intervalo", "Common.Views.SymbolTableDialog.textRecent": "Símbolos usados recentemente", "Common.Views.SymbolTableDialog.textRegistered": "Símbolo de marca registrada", "Common.Views.SymbolTableDialog.textSCQuote": "<PERSON><PERSON><PERSON> aspas simples", "Common.Views.SymbolTableDialog.textSection": "Sinal de seção", "Common.Views.SymbolTableDialog.textShortcut": "Teclas de atalho", "Common.Views.SymbolTableDialog.textSHyphen": "Hífen suave", "Common.Views.SymbolTableDialog.textSOQuote": "Abertura de aspas simples", "Common.Views.SymbolTableDialog.textSpecial": "caracteres especiais", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "Símbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Símbolo de marca registrada", "Common.Views.UserNameDialog.textDontShow": "Não perguntar novamente", "Common.Views.UserNameDialog.textLabel": "Etiqueta:", "Common.Views.UserNameDialog.textLabelError": "Etiqueta não deve estar vazia.", "PE.Controllers.DocumentHolder.textLongName": "Digite um nome com menos de 255 caracteres.", "PE.Controllers.DocumentHolder.textNameLayout": "Nome do layout", "PE.Controllers.DocumentHolder.textNameMaster": "Nome principal", "PE.Controllers.DocumentHolder.textRenameTitleLayout": "Renomear Layout", "PE.Controllers.DocumentHolder.textRenameTitleMaster": "Renomear mestre", "PE.Controllers.LeftMenu.leavePageText": "<PERSON><PERSON> as alterações não salvas neste documento serão perdidas.<br> Clique em \"Cancelar\" e depois em \"Salvar\" para salvá-las. Clique em \"OK\" para descartar todas as alterações não salvas.", "PE.Controllers.LeftMenu.newDocumentTitle": "Apresentação sem nome", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Aviso", "PE.Controllers.LeftMenu.requestEditRightsText": "Solicitando direitos de edição...", "PE.Controllers.LeftMenu.textLoadHistory": "Carregando o histórico de versões...", "PE.Controllers.LeftMenu.textNoTextFound": "Os dados que você tem estado procurando não podem ser encontrados. Ajuste suas opções de pesquisa.", "PE.Controllers.LeftMenu.textReplaceSkipped": "A substituição foi realizada. {0} ocorrências foram ignoradas.", "PE.Controllers.LeftMenu.textReplaceSuccess": "A pesquisa foi realizada. Ocorrências substituídas: {0}", "PE.Controllers.LeftMenu.textSelectPath": "Digite um novo nome para salvar a cópia do arquivo", "PE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON> tí<PERSON>lo", "PE.Controllers.Main.applyChangesTextText": "Carregando dados...", "PE.Controllers.Main.applyChangesTitleText": "Carregando dados", "PE.Controllers.Main.confirmMaxChangesSize": "O tamanho das ações excede a limitação definida para seu servidor.<br>Pressione \"Desfazer\" para cancelar sua última ação ou pressione \"Continue\" para manter a ação localmente (você precisa baixar o arquivo ou copiar seu conteúdo para garantir que nada seja perdido).", "PE.Controllers.Main.convertationTimeoutText": "Tempo limite de conversão excedido.", "PE.Controllers.Main.criticalErrorExtText": "Pressione \"OK\" para voltar para a lista de documentos.", "PE.Controllers.Main.criticalErrorTitle": "Erro", "PE.Controllers.Main.downloadErrorText": "Transferência falhou.", "PE.Controllers.Main.downloadTextText": "Transferindo apresentação...", "PE.Controllers.Main.downloadTitleText": "Tranferindo Apresenta<PERSON>", "PE.Controllers.Main.errorAccessDeny": "Você está tentando executar uma ação para a qual não tem direitos.<br>Entre em contato com o administrador do Document Server.", "PE.Controllers.Main.errorBadImageUrl": "URL da imagem está incorreta", "PE.Controllers.Main.errorCannotPasteImg": "Não podemos colar esta imagem da área de transferência, mas você pode salvá-la em seu dispositivo e\ninsira-o a partir daí ou copie a imagem sem texto e cole-a na apresentação.", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Conexão com servidor perdida. O documento não pode ser editado neste momento.", "PE.Controllers.Main.errorComboSeries": "Para criar uma tabela de combinação, selecione pelo menos duas séries de dados.", "PE.Controllers.Main.errorConnectToServer": "O documento não pode ser gravado. Verifique as configurações de conexão ou entre em contato com o administrador.<br>Quando você clicar no botão 'OK', você será solicitado a transferir o documento.", "PE.Controllers.Main.errorDatabaseConnection": "Erro externo.<br><PERSON><PERSON> de conexão ao banco de dados. Entre em contato com o suporte caso o erro persista.", "PE.Controllers.Main.errorDataEncrypted": "Alteração criptografadas foram recebidas, e não podem ser decifradas.", "PE.Controllers.Main.errorDataRange": "Intervalo de dados incorreto.", "PE.Controllers.Main.errorDefaultMessage": "Código do erro: %1", "PE.Controllers.Main.errorDirectUrl": "Por favor, verifique o link para o documento.<br>Este link deve ser o link direto para baixar o arquivo.", "PE.Controllers.Main.errorEditingDownloadas": "Ocorreu um erro. <br> Use a opção 'Transferir como' para gravar a cópia de backup em seu computador.", "PE.Controllers.Main.errorEditingSaveas": "Ocorreu um erro durante o trabalho com o documento.<br>Use a opção 'Salvar como ...' para salvar a cópia de backup do arquivo no disco rígido do computador.", "PE.Controllers.Main.errorEmailClient": "Nenhum cliente de e-mail foi encontrado.", "PE.Controllers.Main.errorFilePassProtect": "O documento é protegido por senha e não pode ser aberto.", "PE.Controllers.Main.errorFileSizeExceed": "O tamanho do arquivo excede o limite de seu servidor. <br> Por favor, contate seu administrador de Servidor de Documentos para detalhes.", "PE.Controllers.Main.errorForceSave": "Ocorreu um erro na gravação. Favor utilizar a opção 'Transferir como' para gravar o arquivo em seu computador ou tente novamente mais tarde.", "PE.Controllers.Main.errorInconsistentExt": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo não corresponde à extensão do arquivo.", "PE.Controllers.Main.errorInconsistentExtDocx": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo corresponde a documentos de texto (por exemplo, docx), mas o arquivo tem a extensão inconsistente: %1.", "PE.Controllers.Main.errorInconsistentExtPdf": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo corresponde a um dos seguintes formatos: pdf/djvu/xps/oxps, mas o arquivo tem a extensão inconsistente: %1.", "PE.Controllers.Main.errorInconsistentExtPptx": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo corresponde a apresentações (por exemplo, pptx), mas o arquivo tem a extensão inconsistente: %1.", "PE.Controllers.Main.errorInconsistentExtXlsx": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo corresponde a planilhas (por exemplo, xlsx), mas o arquivo tem a extensão inconsistente: %1.", "PE.Controllers.Main.errorKeyEncrypt": "Descritor de chave desconhecido", "PE.Controllers.Main.errorKeyExpire": "Descritor de chave expirado", "PE.Controllers.Main.errorLoadingFont": "As fontes não foram carregadas. <br> Entre em contato com o administrador do Document Server.", "PE.Controllers.Main.errorProcessSaveResult": "Salvamento falhou.", "PE.Controllers.Main.errorSaveWatermark": "Este arquivo contém uma imagem de marca d'água vinculada a outro domínio.<br>Para torná-la visível no PDF, atualize a imagem da marca d'água para que ela seja vinculada ao mesmo domínio do documento ou carregue-a de seu computador.", "PE.Controllers.Main.errorServerVersion": "A versão do editor foi atualizada. A página será recarregada para aplicar as alterações.", "PE.Controllers.Main.errorSessionAbsolute": "A sessão de edição de documentos expirou. Atualize a página.", "PE.Controllers.Main.errorSessionIdle": "O documento ficou sem edição por muito tempo. Atualize a página.", "PE.Controllers.Main.errorSessionToken": "A conexão com o servidor foi interrompida. Atualize a página.", "PE.Controllers.Main.errorSetPassword": "Não foi possível definir a senha.", "PE.Controllers.Main.errorStockChart": "Ordem da linha incorreta. Para criar um gráfico de ações coloque os dados na planilha na seguinte ordem:<br>preço de abertura, preço máx., preço mín., preço de fechamento.", "PE.Controllers.Main.errorToken": "O token de segurança do documento não foi formado corretamente.<br>Entre em contato com o administrador do Document Server.", "PE.Controllers.Main.errorTokenExpire": "O token de segurança do documento expirou.<br>Entre em contato com o administrador do Document Server.", "PE.Controllers.Main.errorUpdateVersion": "A versão do arquivo foi alterada. A página será recarregada.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "A conexão a internet foi restaurada, e a versão do arquivo foi alterada. <br> Antes de continuar seu trabalho, transfira o arquivo ou copie seu conteúdo para assegurar que nada seja perdido, e então, recarregue esta página.", "PE.Controllers.Main.errorUserDrop": "O arquivo não pode ser acessado agora.", "PE.Controllers.Main.errorUsersExceed": "O número de usuários permitidos pelo plano de preços foi excedido", "PE.Controllers.Main.errorViewerDisconnect": "A conexão foi perdida. Você ainda pode ver o documento,<br>mas não pode fazer o download ou imprimir até que a conexão seja restaurada.", "PE.Controllers.Main.leavePageText": "Você não salvou as alterações nesta apresentação. Clique em \"Permanecer nesta página\", em seguida em \"Salvar\" para salvá-las. Clique em \"Sair desta página\" para descartar todas as alterações não salvas.", "PE.Controllers.Main.leavePageTextOnClose": "<PERSON><PERSON> as mudan<PERSON>s não salvas nesta apresentação serão perdidas.<br> Clique em \"Cancelar\" e depois em \"Salvar\" para salvá-las. Clique em \"OK\" para descartar todas as alterações não salvas.", "PE.Controllers.Main.loadFontsTextText": "Carregando dados...", "PE.Controllers.Main.loadFontsTitleText": "Carregando dados", "PE.Controllers.Main.loadFontTextText": "Carregando dados...", "PE.Controllers.Main.loadFontTitleText": "Carregando dados", "PE.Controllers.Main.loadImagesTextText": "Carregando imagens...", "PE.Controllers.Main.loadImagesTitleText": "Carregando imagens", "PE.Controllers.Main.loadImageTextText": "Carregando imagem...", "PE.Controllers.Main.loadImageTitleText": "Carregando imagem", "PE.Controllers.Main.loadingDocumentTextText": "Carregando apresentação...", "PE.Controllers.Main.loadingDocumentTitleText": "Carregando apresentação", "PE.Controllers.Main.loadThemeTextText": "Carregando temas...", "PE.Controllers.Main.loadThemeTitleText": "Carregando tema", "PE.Controllers.Main.notcriticalErrorTitle": "Aviso", "PE.Controllers.Main.openErrorText": "Ocorreu um erro ao abrir o arquivo", "PE.Controllers.Main.openTextText": "Abrindo apresentação...", "PE.Controllers.Main.openTitleText": "Abrindo apresentação", "PE.Controllers.Main.printTextText": "Imprimindo apresentação...", "PE.Controllers.Main.printTitleText": "Imprimindo apresentação", "PE.Controllers.Main.reloadButtonText": "Re<PERSON><PERSON><PERSON> p<PERSON>gina", "PE.Controllers.Main.requestEditFailedMessageText": "Alguém está editando esta apresentação neste momento. Tente novamente mais tarde.", "PE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON>", "PE.Controllers.Main.saveErrorText": "Ocorreu um erro ao salvar o arquivo", "PE.Controllers.Main.saveErrorTextDesktop": "Este arquivo não pode ser salvo ou criado.<br>Possíveis razões são: <br>1. O arquivo é somente leitura. <br>2. O arquivo está sendo editado por outros usuários. <br>3. O disco está cheio ou corrompido.", "PE.Controllers.Main.saveTextText": "<PERSON><PERSON><PERSON> a<PERSON>res<PERSON>...", "PE.Controllers.Main.saveTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.scriptLoadError": "A conexão está muito lenta, e alguns dos componentes não puderam ser carregados. Por favor, recarregue a página.", "PE.Controllers.Main.splitDividerErrorText": "O número de linhas deve ser um divisor de %1.", "PE.Controllers.Main.splitMaxColsErrorText": "O número de colunas deve ser inferior a %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "O número de linhas deve ser inferior a %1.", "PE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textApplyAll": "Aplicar a todas as equações", "PE.Controllers.Main.textBuyNow": "Visitar site", "PE.Controllers.Main.textChangesSaved": "<PERSON><PERSON> as alteraç<PERSON>es foram salvas", "PE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textCloseTip": "Clique para fechar a dica", "PE.Controllers.Main.textConnectionLost": "Tentando conectar. Verifique as configurações de conexão.", "PE.Controllers.Main.textContactUs": "Entre em contato com o departamento de vendas", "PE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textConvertEquation": "Esta equação foi criada com uma versão antiga do editor de equação que não é mais compatível. Para editá-lo, converta a equação para o formato Office Math ML. <br> Converter agora?", "PE.Controllers.Main.textCustomLoader": "Observe que, de acordo com os termos da licença, você não tem direito de alterar a carregadeira. <br> Entre em contato com nosso Departamento de Vendas para obter uma cotação.", "PE.Controllers.Main.textDisconnect": "A conexão está perdida", "PE.Controllers.Main.textGuest": "Convidado(a)", "PE.Controllers.Main.textHasMacros": "O arquivo contém macros automáticas.<br> Você quer executar macros?", "PE.Controllers.Main.textLearnMore": "<PERSON><PERSON> mais", "PE.Controllers.Main.textLoadingDocument": "Carregando apresentação", "PE.Controllers.Main.textLongName": "Insira um nome com menos de 128 caracteres.", "PE.Controllers.Main.textNoLicenseTitle": "Limite de licença atingido", "PE.Controllers.Main.textObject": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textPaidFeature": "Recurso pago", "PE.Controllers.Main.textReconnect": "A conexão é restaurada", "PE.Controllers.Main.textRemember": "Lembre-se da minha escolha", "PE.Controllers.Main.textRememberMacros": "<PERSON><PERSON><PERSON> minha escolha para todas as macros", "PE.Controllers.Main.textRenameError": "O nome de usuário não pode estar vazio.", "PE.Controllers.Main.textRenameLabel": "Insira um nome a ser usado para colaboração", "PE.Controllers.Main.textRequestMacros": "Uma macro faz uma solicitação para URL. Deseja permitir a solicitação para %1?", "PE.Controllers.Main.textShape": "Forma", "PE.Controllers.Main.textStrict": "Modo estrito", "PE.Controllers.Main.textText": "Тexto", "PE.Controllers.Main.textTryQuickPrint": "Você selecionou Impressão rápida: todo o documento será impresso na última impressora selecionada ou padrão.<br>Deseja continuar?", "PE.Controllers.Main.textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.<br>Click the 'Strict mode' button to switch to the Strict co-editing mode to edit the file without other users interference and send your changes only after you save them. You can switch between the co-editing modes using the editor Advanced settings.", "PE.Controllers.Main.textTryUndoRedoWarn": "As funções Desfazer/Refazer estão desabilitadas para o modo de coedição rápido", "PE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textUpdateVersion": "O documento não pode ser editado agora.<br><PERSON><PERSON><PERSON> atual<PERSON> o arquivo, aguarde...", "PE.Controllers.Main.textUpdating": "Atualizando", "PE.Controllers.Main.titleLicenseExp": "Licença expirada", "PE.Controllers.Main.titleLicenseNotActive": "Licença inativa", "PE.Controllers.Main.titleServerVersion": "Editor atual<PERSON><PERSON>", "PE.Controllers.Main.titleUpdateVersion": "Versão alterada", "PE.Controllers.Main.txtAddFirstSlide": "Clique para adicionar o primeiro slide", "PE.Controllers.Main.txtAddNotes": "Clique para adicionar notas", "PE.Controllers.Main.txtAnimationPane": "Painel de animação", "PE.Controllers.Main.txtArt": "Your text here", "PE.Controllers.Main.txtBasicShapes": "Formas básicas", "PE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtCallouts": "Textos explicativos", "PE.Controllers.Main.txtCharts": "Grá<PERSON><PERSON>", "PE.Controllers.Main.txtClipArt": "<PERSON><PERSON>", "PE.Controllers.Main.txtDateTime": "Data e Hora", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Título do gráfico", "PE.Controllers.Main.txtEditingMode": "Definir modo de edição...", "PE.Controllers.Main.txtEnd": "Fim: ${0}s", "PE.Controllers.Main.txtErrorLoadHistory": "O carregamento de histórico falhou", "PE.Controllers.Main.txtFiguredArrows": "Setas figuradas", "PE.Controllers.Main.txtFirstSlide": "Primeiro slide", "PE.Controllers.Main.txtFooter": "Rodapé", "PE.Controllers.Main.txtHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtImage": "Imagem", "PE.Controllers.Main.txtLastSlide": "Último slide", "PE.Controllers.Main.txtLines": "<PERSON><PERSON>", "PE.Controllers.Main.txtLoading": "Carregando...", "PE.Controllers.Main.txtLoop": "Ciclo: ${0}s", "PE.Controllers.Main.txtMath": "Matemática", "PE.Controllers.Main.txtMedia": "Média", "PE.Controllers.Main.txtNeedSynchronize": "Você tem atualizações", "PE.Controllers.Main.txtNextSlide": "Próximo slide", "PE.Controllers.Main.txtNone": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtPicture": "Imagem", "PE.Controllers.Main.txtPlayAll": "<PERSON><PERSON><PERSON><PERSON> tudo", "PE.Controllers.Main.txtPlayFrom": "Reproduzir de", "PE.Controllers.Main.txtPlaySelected": "Reproduzir se<PERSON>cionado", "PE.Controllers.Main.txtPrevSlide": "Slide anterior", "PE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSaveCopyAsComplete": "A cópia do arquivo foi salva com êxito", "PE.Controllers.Main.txtScheme_Aspect": "Aspect<PERSON>", "PE.Controllers.Main.txtScheme_Blue": "Azul", "PE.Controllers.Main.txtScheme_Blue_Green": "Verde azulado", "PE.Controllers.Main.txtScheme_Blue_II": "Azul II", "PE.Controllers.Main.txtScheme_Blue_Warm": "<PERSON><PERSON>l quente", "PE.Controllers.Main.txtScheme_Grayscale": "Escala de cinza", "PE.Controllers.Main.txtScheme_Green": "Verde", "PE.Controllers.Main.txtScheme_Green_Yellow": "Verde amarelo", "PE.Controllers.Main.txtScheme_Marquee": "<PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Median": "Mediana", "PE.Controllers.Main.txtScheme_Office": "Office", "PE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "PE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "PE.Controllers.Main.txtScheme_Orange": "<PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Orange_Red": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Paper": "Papel", "PE.Controllers.Main.txtScheme_Red": "Vermelho", "PE.Controllers.Main.txtScheme_Red_Orange": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Red_Violet": "<PERSON><PERSON> ve<PERSON>", "PE.Controllers.Main.txtScheme_Slipstream": "Turbulência", "PE.Controllers.Main.txtScheme_Violet": "<PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Violet_II": "Violeta II", "PE.Controllers.Main.txtScheme_Yellow": "<PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Yellow_Orange": "<PERSON><PERSON>", "PE.Controllers.Main.txtSeries": "Série", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Texto explicativo da linha 1 (Borda e barra de destaque)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Texto explicativo da linha 2 (Borda e barra de destaque)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Texto explicativo da linha 3 (Borda e barra de destaque)", "PE.Controllers.Main.txtShape_accentCallout1": "Texto explicativo da linha 1 (Barra de destaque)", "PE.Controllers.Main.txtShape_accentCallout2": "Texto explicativo da linha 2 (Barra de destaque)", "PE.Controllers.Main.txtShape_accentCallout3": "Texto explicativo da linha 3 (Barra de destaque)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Botão voltar ou anterior", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Botão inicial", "PE.Controllers.Main.txtShape_actionButtonBlank": "Botão em branco", "PE.Controllers.Main.txtShape_actionButtonDocument": "Botão Documento", "PE.Controllers.Main.txtShape_actionButtonEnd": "Botão terminar", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "Botão avançar ou próximo", "PE.Controllers.Main.txtShape_actionButtonHelp": "Botão de ajuda", "PE.Controllers.Main.txtShape_actionButtonHome": "Botão início", "PE.Controllers.Main.txtShape_actionButtonInformation": "Botão de informação", "PE.Controllers.Main.txtShape_actionButtonMovie": "Botão de vídeo", "PE.Controllers.Main.txtShape_actionButtonReturn": "Botão de voltar", "PE.Controllers.Main.txtShape_actionButtonSound": "Botão de som", "PE.Controllers.Main.txtShape_arc": "Arco", "PE.Controllers.Main.txtShape_bentArrow": "Seta curvada", "PE.Controllers.Main.txtShape_bentConnector5": "Conector angular", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Conector de seta angular", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Conector de seta dupla angular", "PE.Controllers.Main.txtShape_bentUpArrow": "Seta para cima curvada", "PE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_blockArc": "Arco de bloco", "PE.Controllers.Main.txtShape_borderCallout1": "Texto explicativo da linha 1", "PE.Controllers.Main.txtShape_borderCallout2": "Texto explicativo da linha 2", "PE.Controllers.Main.txtShape_borderCallout3": "Texto explicativo da linha 3", "PE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON> du<PERSON>", "PE.Controllers.Main.txtShape_callout1": "Texto explicativo da linha 1 (<PERSON><PERSON> borda)", "PE.Controllers.Main.txtShape_callout2": "Texto explicativo da linha 2 (<PERSON><PERSON> borda)", "PE.Controllers.Main.txtShape_callout3": "Texto explicativo da linha 3 (<PERSON><PERSON> borda)", "PE.Controllers.Main.txtShape_can": "Pode", "PE.Controllers.Main.txtShape_chevron": "Divisa", "PE.Controllers.Main.txtShape_chord": "Acorde", "PE.Controllers.Main.txtShape_circularArrow": "Seta circular", "PE.Controllers.Main.txtShape_cloud": "Nuvem", "PE.Controllers.Main.txtShape_cloudCallout": "Texto explicativo em nuvem", "PE.Controllers.Main.txtShape_corner": "Canto", "PE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Conector curvado", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Conector de seta curvada", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Conector de seta dupla curvado", "PE.Controllers.Main.txtShape_curvedDownArrow": "Seta curvada para baixo", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Seta curvada para a esquerda", "PE.Controllers.Main.txtShape_curvedRightArrow": "Seta curva para a direita", "PE.Controllers.Main.txtShape_curvedUpArrow": "Seta curva para cima", "PE.Controllers.Main.txtShape_decagon": "Decágono", "PE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonal", "PE.Controllers.Main.txtShape_diamond": "Diamante", "PE.Controllers.Main.txtShape_dodecagon": "Dodecágono", "PE.Controllers.Main.txtShape_donut": "Rosquin<PERSON>", "PE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_downArrow": "Seta para baixo", "PE.Controllers.Main.txtShape_downArrowCallout": "Texto explicativo em seta para baixo", "PE.Controllers.Main.txtShape_ellipse": "Elipse", "PE.Controllers.Main.txtShape_ellipseRibbon": "Fita curvada para baixo", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Fita curvada para cima", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Fluxograma: Processo alternativo", "PE.Controllers.Main.txtShape_flowChartCollate": "Fluxograma: Agrupar", "PE.Controllers.Main.txtShape_flowChartConnector": "Fluxograma: Conector", "PE.Controllers.Main.txtShape_flowChartDecision": "Fluxograma: Decisão", "PE.Controllers.Main.txtShape_flowChartDelay": "Fluxograma: Atraso", "PE.Controllers.Main.txtShape_flowChartDisplay": "Fluxograma: Exibir", "PE.Controllers.Main.txtShape_flowChartDocument": "Fluxograma: Documento", "PE.Controllers.Main.txtShape_flowChartExtract": "Fluxograma: Extrair", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Fluxograma: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Fluxograma: Armazenamento interno", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Fluxograma: Disco magnético", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Fluxograma: Armazenamento de acesso direto", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Fluxograma: Armazenamento de acesso sequencial", "PE.Controllers.Main.txtShape_flowChartManualInput": "Fluxograma: Entrada manual", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Fluxograma: Operação manual", "PE.Controllers.Main.txtShape_flowChartMerge": "Fluxograma: Mesclar", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Fluxograma: V<PERSON><PERSON>s Documentos", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Fluxograma: Conector fora da página", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Fluxograma: Dad<PERSON> armaz<PERSON>", "PE.Controllers.Main.txtShape_flowChartOr": "Fluxograma: Ou", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Fluxograma: Processo Predefinido", "PE.Controllers.Main.txtShape_flowChartPreparation": "Fluxograma: Preparação", "PE.Controllers.Main.txtShape_flowChartProcess": "Fluxograma: Processo", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Fluxograma: Cartão", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Fluxograma: <PERSON><PERSON> perfurada", "PE.Controllers.Main.txtShape_flowChartSort": "Fluxograma: Classificar", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Fluxograma: Junção de soma", "PE.Controllers.Main.txtShape_flowChartTerminator": "Fluxograma: Terminação", "PE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON> do<PERSON>", "PE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_heart": "Coração", "PE.Controllers.Main.txtShape_heptagon": "<PERSON>pt<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_hexagon": "Hexágono", "PE.Controllers.Main.txtShape_homePlate": "Pentágono", "PE.Controllers.Main.txtShape_horizontalScroll": "Rolagem horizontal", "PE.Controllers.Main.txtShape_irregularSeal1": "Explosão 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Explosão 2", "PE.Controllers.Main.txtShape_leftArrow": "Seta para esquerda", "PE.Controllers.Main.txtShape_leftArrowCallout": "Texto explicativo à esquerda", "PE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftBracket": "Colchete Esquerdo", "PE.Controllers.Main.txtShape_leftRightArrow": "Seta da esquerda para a direita", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Texto explicativo da seta da esquerda para a direita", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Seta da esquerda para a direita para cima", "PE.Controllers.Main.txtShape_leftUpArrow": "Seta esquerda para cima", "PE.Controllers.Main.txtShape_lightningBolt": "Raio", "PE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON>a dupla", "PE.Controllers.Main.txtShape_mathDivide": "Divisão", "PE.Controllers.Main.txtShape_mathEqual": "Igual", "PE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMultiply": "Multiplicar", "PE.Controllers.Main.txtShape_mathNotEqual": "Não é Igual", "PE.Controllers.Main.txtShape_mathPlus": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_noSmoking": "Entrada Proibida", "PE.Controllers.Main.txtShape_notchedRightArrow": "Seta cortada à direita", "PE.Controllers.Main.txtShape_octagon": "Octágono", "PE.Controllers.Main.txtShape_parallelogram": "Paralelograma", "PE.Controllers.Main.txtShape_pentagon": "Pentágono", "PE.Controllers.Main.txtShape_pie": "Pizza", "PE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_plus": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_polyline1": "Rabisco", "PE.Controllers.Main.txtShape_polyline2": "Forma livre", "PE.Controllers.Main.txtShape_quadArrow": "Setas cru<PERSON>as", "PE.Controllers.Main.txtShape_quadArrowCallout": "Texto explicativo em seta cruzadas", "PE.Controllers.Main.txtShape_rect": "Re<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_ribbon": "Faixa para baixo", "PE.Controllers.Main.txtShape_ribbon2": "Fita para cima", "PE.Controllers.Main.txtShape_rightArrow": "Seta para direita", "PE.Controllers.Main.txtShape_rightArrowCallout": "Texto explicativo da seta à direita", "PE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON><PERSON> a direita", "PE.Controllers.Main.txtShape_rightBracket": "Colchete direito", "PE.Controllers.Main.txtShape_round1Rect": "Retângulo com único canto arredondado", "PE.Controllers.Main.txtShape_round2DiagRect": "<PERSON><PERSON><PERSON><PERSON><PERSON> de canto diagonal arredondado ", "PE.Controllers.Main.txtShape_round2SameRect": "Retângulo arredondado do mesmo lado", "PE.Controllers.Main.txtShape_roundRect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_rtTriangle": "Triângulo retângulo", "PE.Controllers.Main.txtShape_smileyFace": "Rosto sorridente", "PE.Controllers.Main.txtShape_snip1Rect": "Recortar retângulo de canto único", "PE.Controllers.Main.txtShape_snip2DiagRect": "Recortar retângulo de canto diagonal", "PE.Controllers.Main.txtShape_snip2SameRect": "Recortar retângulo de canto do mesmo lado", "PE.Controllers.Main.txtShape_snipRoundRect": "Recortar e arredondar retângulo de canto único", "PE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "Estrela de 10 pontas", "PE.Controllers.Main.txtShape_star12": "Estrela de 12 pontas", "PE.Controllers.Main.txtShape_star16": "Estrela de 16 pontas", "PE.Controllers.Main.txtShape_star24": "Estrela de 24 pontas", "PE.Controllers.Main.txtShape_star32": "Estrela de 32 pontas", "PE.Controllers.Main.txtShape_star4": "Estrela de 4 pontas", "PE.Controllers.Main.txtShape_star5": "Estrela de 5 pontas", "PE.Controllers.Main.txtShape_star6": "Estrela de 6 pontas", "PE.Controllers.Main.txtShape_star7": "Estrela de 7 pontas", "PE.Controllers.Main.txtShape_star8": "Estrela de 8 pontas", "PE.Controllers.Main.txtShape_stripedRightArrow": "Seta para a direita listrada", "PE.Controllers.Main.txtShape_sun": "Sol", "PE.Controllers.Main.txtShape_teardrop": "Lágrima", "PE.Controllers.Main.txtShape_textRect": "Caixa de texto", "PE.Controllers.Main.txtShape_trapezoid": "Trapé<PERSON>", "PE.Controllers.Main.txtShape_triangle": "Triângulo", "PE.Controllers.Main.txtShape_upArrow": "Seta para cima", "PE.Controllers.Main.txtShape_upArrowCallout": "Texto explicativo em seta para cima", "PE.Controllers.Main.txtShape_upDownArrow": "Seta de cima para baixo", "PE.Controllers.Main.txtShape_uturnArrow": "Seta em forma de U", "PE.Controllers.Main.txtShape_verticalScroll": "Rolagem vertical", "PE.Controllers.Main.txtShape_wave": "On<PERSON>", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Texto explicativo oval", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Texto explicativo retangular", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Texto explicativo retangular arredondado", "PE.Controllers.Main.txtSldLtTBlank": "Branco", "PE.Controllers.Main.txtSldLtTChart": "Gráfico", "PE.Controllers.Main.txtSldLtTChartAndTx": "Gráfico e texto", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Clip Art e texto", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Clip Art e texto vertical", "PE.Controllers.Main.txtSldLtTCust": "Personalizar", "PE.Controllers.Main.txtSldLtTDgm": "Diagrama", "PE.Controllers.Main.txtSldLtTFourObj": "Quatro objetos", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Mídia e Texto", "PE.Controllers.Main.txtSldLtTObj": "Título e objeto", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Objeto e dois objetos", "PE.Controllers.Main.txtSldLtTObjAndTx": "Objeto e texto", "PE.Controllers.Main.txtSldLtTObjOnly": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTObjOverTx": "Objeto sobre o texto", "PE.Controllers.Main.txtSldLtTObjTx": "<PERSON><PERSON><PERSON><PERSON>, objeto e legenda", "PE.Controllers.Main.txtSldLtTPicTx": "Imagem e Legenda", "PE.Controllers.Main.txtSldLtTSecHead": "Cabeçalho da seção", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitle": "Titulo", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON><PERSON> tí<PERSON>", "PE.Controllers.Main.txtSldLtTTwoColTx": "Texto em duas colunas", "PE.Controllers.Main.txtSldLtTTwoObj": "<PERSON><PERSON> ob<PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Dois objetos e objeto", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Dois objetos e texto", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Dois objetos sobre o texto", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Dois textos e dois objetos", "PE.Controllers.Main.txtSldLtTTx": "Тexto", "PE.Controllers.Main.txtSldLtTTxAndChart": "Texto e gráfico", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Texto e Clip Art", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Texto e Mídia", "PE.Controllers.Main.txtSldLtTTxAndObj": "Texto e objeto", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Texto e dois objetos", "PE.Controllers.Main.txtSldLtTTxOverObj": "Texto sobre objeto", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Título vertical e texto", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Título vertical e texto sobre o gráfico", "PE.Controllers.Main.txtSldLtTVertTx": "Texto vertical", "PE.Controllers.Main.txtSlideNumber": "Número do slide", "PE.Controllers.Main.txtSlideSubtitle": "Legenda do slide", "PE.Controllers.Main.txtSlideText": "Texto do slide", "PE.Controllers.Main.txtSlideTitle": "Título do slide", "PE.Controllers.Main.txtStarsRibbons": "Estrelas e Arco-íris", "PE.Controllers.Main.txtStart": "Início: ${0}s", "PE.Controllers.Main.txtStop": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_basic": "Básico", "PE.Controllers.Main.txtTheme_blank": "Branco", "PE.Controllers.Main.txtTheme_classic": "Clássico", "PE.Controllers.Main.txtTheme_corner": "Canto", "PE.Controllers.Main.txtTheme_dotted": "Pontil<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green": "Verde", "PE.Controllers.Main.txtTheme_green_leaf": "Folha verde", "PE.Controllers.Main.txtTheme_lines": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "Office ", "PE.Controllers.Main.txtTheme_office_theme": "Tema do Office", "PE.Controllers.Main.txtTheme_official": "Oficial", "PE.Controllers.Main.txtTheme_pixel": "Pixel", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Tartaruga", "PE.Controllers.Main.txtXAxis": "Eixo X", "PE.Controllers.Main.txtYAxis": "Eixo Y", "PE.Controllers.Main.txtZoom": "Zoom", "PE.Controllers.Main.unknownErrorText": "<PERSON><PERSON> desconhecido.", "PE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON>u navegador não é suportado.", "PE.Controllers.Main.uploadImageExtMessage": "Formato de imagem desconhecido.", "PE.Controllers.Main.uploadImageFileCountMessage": "Sem imagens carregadas.", "PE.Controllers.Main.uploadImageSizeMessage": "Tamanho limite máximo da imagem excedido. O tamanho máximo é de 25 MB.", "PE.Controllers.Main.uploadImageTextText": "Carregando imagem...", "PE.Controllers.Main.uploadImageTitleText": "Carregando imagem", "PE.Controllers.Main.waitText": "Aguarde...", "PE.Controllers.Main.warnBrowserIE9": "O aplicativo tem baixa capacidade no IE9. Usar IE10 ou superior", "PE.Controllers.Main.warnBrowserZoom": "A configuração de zoom atual de seu navegador não é completamente suportada. Redefina para o zoom padrão pressionando Ctrl+0.", "PE.Controllers.Main.warnLicenseAnonymous": "Acesso negado para usuários anônimos.<br>Este documento será aberto apenas para visualização.", "PE.Controllers.Main.warnLicenseBefore": "Licença inativa.<br>Entre em contato com seu administrador.", "PE.Controllers.Main.warnLicenseExceeded": "Você atingiu o limite de conexões simultâneas para editores %1. Este documento será aberto apenas para visualização.<br>Entre em contato com seu administrador para saber mais.", "PE.Controllers.Main.warnLicenseExp": "Sua licença expirou.<br>Atualize sua licença e atualize a página.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "A licença expirou.<br>Você não tem acesso à funcionalidade de edição de documentos.<br>Por favor, contate seu administrador.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "A licença precisa ser renovada. <br> Você tem acesso limitado à funcionalidade de edição de documentos. <br> Entre em contato com o administrador para obter acesso total.", "PE.Controllers.Main.warnLicenseUsersExceeded": "Você atingiu o limite de usuários para editores %1. Entre em contato com seu administrador para saber mais.", "PE.Controllers.Main.warnNoLicense": "Você atingiu o limite de conexões simultâneas para editores %1. Este documento será aberto apenas para visualização.<br>Entre em contato com a equipe de vendas da %1 para obter os termos de atualização pessoais.", "PE.Controllers.Main.warnNoLicenseUsers": "Você atingiu o limite de usuários para editores %1.<br>Entre em contato com a equipe de vendas da %1 para obter os termos de atualização pessoais.", "PE.Controllers.Main.warnProcessRightsChange": "Foi negado a você o direito de editar o arquivo.", "PE.Controllers.Print.txtPrintRangeInvalid": "Intervalo de impressão inválido", "PE.Controllers.Print.txtPrintRangeSingleRange": "Insira um único número de slide ou um único intervalo de slide (por exemplo, 5-12). Ou você pode imprimir em PDF.", "PE.Controllers.Search.notcriticalErrorTitle": "Aviso", "PE.Controllers.Search.textNoTextFound": "Os dados que você tem estado procurando não podem ser encontrados. Ajuste suas opções de pesquisa.", "PE.Controllers.Search.textReplaceSkipped": "A substituição foi realizada. {0} ocorrências foram ignoradas.", "PE.Controllers.Search.textReplaceSuccess": "A pesquisa foi feita. {0} ocorrências foram substituídas", "PE.Controllers.Search.warnReplaceString": "{0} não é um caractere especial válido para a caixa Substituir por.", "PE.Controllers.Statusbar.textDisconnect": "<b>A conexão foi perdida</b><br>Tentando conectar. Verifique as configurações de conexão.", "PE.Controllers.Statusbar.zoomText": "Zoom {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "The font you are going to save is not available on the current device.<br>The text style will be displayed using one of the device fonts, the saved font will be used when it is available.<br>Do you want to continue?", "PE.Controllers.Toolbar.helpMergeShapes": "Combine, fragmente, interseccione e subtraia formas em segundos para criar visuais personalizados.", "PE.Controllers.Toolbar.helpMergeShapesHeader": "Mesclar formas", "PE.Controllers.Toolbar.helpTabDesign": "<PERSON><PERSON><PERSON> te<PERSON>, altere os esquemas de cores e o tamanho dos slides na recém-adicionada guia Design.", "PE.Controllers.Toolbar.helpTabDesignHeader": "Aba Design", "PE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textFontSizeErr": "O valor inserido está incorreto.<br>Insira um valor numérico entre 1 e 300", "PE.Controllers.Toolbar.textFraction": "Frações", "PE.Controllers.Toolbar.textFunction": "Funções", "PE.Controllers.Toolbar.textInsert": "Inserir", "PE.Controllers.Toolbar.textIntegral": "Inte<PERSON><PERSON>", "PE.Controllers.Toolbar.textLargeOperator": "Grandes operadores", "PE.Controllers.Toolbar.textLimitAndLog": "Limites e logaritmos", "PE.Controllers.Toolbar.textMatrix": "Matrizes", "PE.Controllers.Toolbar.textOperator": "Operadores", "PE.Controllers.Toolbar.textRadical": "Radicais", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textWarning": "Aviso", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Seta para direita-esquerda acima", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Seta adiante para cima", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Seta para direita acima", "PE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "PE.Controllers.Toolbar.txtAccent_BarBot": "Barra inferior", "PE.Controllers.Toolbar.txtAccent_BarTop": "Barra superior", "PE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON><PERSON> embal<PERSON> (com Placeholder)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "F<PERSON>rmula embalada(Exemplo)", "PE.Controllers.Toolbar.txtAccent_Check": "Verificar", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Chave <PERSON>", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Chave Superior", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vetor A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "Barra superior com ABC", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y com barra superior", "PE.Controllers.Toolbar.txtAccent_DDDot": "Ponto <PERSON>lo", "PE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Dot": "Ponto", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Barra superior dupla", "PE.Controllers.Toolbar.txtAccent_Grave": "Grave", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Agrupamento de caracteres abaixo", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Agrupamento de caracteres acima", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpão adiante para cima", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpão para direita acima", "PE.Controllers.Toolbar.txtAccent_Hat": "Acento circunflexo", "PE.Controllers.Toolbar.txtAccent_Smile": "Breve", "PE.Controllers.Toolbar.txtAccent_Tilde": "Til", "PE.Controllers.Toolbar.txtBracket_Angle": "Colchetes angulares", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Colchetes angulares com separador", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Colchetes angulares com dois separadores", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Colchete de ângulo reto", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Colchete angular esquerdo", "PE.Controllers.Toolbar.txtBracket_Curve": "Colchetes", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Colchetes com separador", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Colchete direito", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Colchete esquerdo", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Casos (Duas Condições)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Casos (Três Condições)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Objeto empil<PERSON>o entre parênteses", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Exemplo de casos", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Coeficiente binominal", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Coeficiente binominal", "PE.Controllers.Toolbar.txtBracket_Line": "Barras verticais", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Barra vertical direita", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Barra vertical esquerda", "PE.Controllers.Toolbar.txtBracket_LineDouble": "Barras verticais duplas", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Barra vertical dupla direita", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Barra vertical dupla esquerda", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON> dire<PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parênteses com separadores", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Parê<PERSON><PERSON> direito", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Espaço reservado entre dois colchetes direitos", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Colchetes invertidos", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Colchete direito", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Colchete esquerdo", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Espaço reservado entre dois colchetes esquerdos", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "Colchetes duplos", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Colchete duplo direito", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Col<PERSON>e duplo es<PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim": "Teto", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON> direito", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Colchete Simples", "PE.Controllers.Toolbar.txtFractionDiagonal": "Fração inclinada", "PE.Controllers.Toolbar.txtFractionDifferential_1": "dx sobre dy", "PE.Controllers.Toolbar.txtFractionDifferential_2": "limite delta y sobre limite delta x", "PE.Controllers.Toolbar.txtFractionDifferential_3": "y parcial sobre x parcial", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Delta y sobre delta x", "PE.Controllers.Toolbar.txtFractionHorizontal": "Fração linear", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi sobre 2", "PE.Controllers.Toolbar.txtFractionSmall": "Fração pequena", "PE.Controllers.Toolbar.txtFractionVertical": "Fração Empilhada", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Função cosseno inverso", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Função cosseno inverso hiperbólico", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Função cotangente inversa", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Função cotangente inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Função cossecante inversa", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Função cossecante inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Função secante inversa", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Função secante inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Função seno inverso", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Função seno inverso hiperbólico", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Função tangente inversa", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Função tangente inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_Cos": "Função cosseno", "PE.Controllers.Toolbar.txtFunction_Cosh": "Função cosseno hiperbólico", "PE.Controllers.Toolbar.txtFunction_Cot": "Função cotangente", "PE.Controllers.Toolbar.txtFunction_Coth": "Função cotangente hiperbólica", "PE.Controllers.Toolbar.txtFunction_Csc": "Função cossecante", "PE.Controllers.Toolbar.txtFunction_Csch": "Função co-secante hiperbólica", "PE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON>ta seno", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON><PERSON> da tangente", "PE.Controllers.Toolbar.txtFunction_Sec": "Função secante", "PE.Controllers.Toolbar.txtFunction_Sech": "Função secante hiperbólica", "PE.Controllers.Toolbar.txtFunction_Sin": "Função de seno", "PE.Controllers.Toolbar.txtFunction_Sinh": "Função seno hiperbólico", "PE.Controllers.Toolbar.txtFunction_Tan": "Função da tangente", "PE.Controllers.Toolbar.txtFunction_Tanh": "Função tangente hiperbólica", "PE.Controllers.Toolbar.txtIntegral": "Integral", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Teta diferencial", "PE.Controllers.Toolbar.txtIntegral_dx": "Derivada x", "PE.Controllers.Toolbar.txtIntegral_dy": "Derivada y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral com limites empilhados", "PE.Controllers.Toolbar.txtIntegralDouble": "Integra<PERSON> dupla", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Integral dupla com limites empilhados", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Integral dupla com limites", "PE.Controllers.Toolbar.txtIntegralOriented": "Contorno integral", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integral de contorno com limites empilhados", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral de Superfície", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral de superfície com limites empilhados", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral de superfície com limites", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integral de contorno com limites", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integral de Volume", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral de volume com limites empilhados", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral de volume com limites", "PE.Controllers.Toolbar.txtIntegralSubSup": "Integral com limites", "PE.Controllers.Toolbar.txtIntegralTriple": "Integral Tripla", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Integral tripla com limites empilhados", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Integral tripla com limites", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Lógico e", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Lógico E com limite inferior", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Lógico E com limites", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Lógico E com limite inferior subscrito", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Lógico E com limites subscritos/sobrescritos", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Coproduto com limite inferior", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Coproduto com limites", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-produto com limite inferior subscrito", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Coproduto com limites subscritos/sobrescritos", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Soma sobre k de n escolha k", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Soma de i igual a zero a n", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Exemplo de soma usando dois índices", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Exemplo de produto", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Exemplo de união", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Lógico ou", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Lógico Ou com limite inferior", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Lógico Ou com limites", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Lógico Ou com limite inferior subscrito", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Ou Lógico com limites subscritos/sobrescritos", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Interseção", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Interseção com limite inferior", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Interseção com limites", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Interseção com limite inferior subscrito", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Interseção com limites subscritos/sobrescritos", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produto com limite inferior", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produto com limites", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produto com limite inferior subscrito", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produto com limites subscritos/sobrescritos", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "So<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Soma com limite inferior", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Soma com limites", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Soma com limite inferior subscrito", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Soma com limites subscritos/sobrescritos", "PE.Controllers.Toolbar.txtLargeOperator_Union": "União", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "União com limite inferior", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "União com limites", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "União com limite inferior subscrito", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "União com limites subscritos/sobrescritos", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Exemplo limite", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Exemplo máximo", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Limite", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo natural", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "PE.Controllers.Toolbar.txtLimitLog_Max": "Máximo", "PE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_1_2": "<PERSON><PERSON> 1x2", "PE.Controllers.Toolbar.txtMatrix_1_3": "<PERSON><PERSON> 1x3", "PE.Controllers.Toolbar.txtMatrix_2_1": "<PERSON><PERSON> 2x1", "PE.Controllers.Toolbar.txtMatrix_2_2": "<PERSON><PERSON> 2x2", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matriz 2 por 2 vazia em barras verticais duplas", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Determinante 2 por 2 vazio", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matriz 2 por 2 vazia entre parênteses", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matriz 2 por 2 vazia entre parênteses", "PE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON><PERSON> 2x3", "PE.Controllers.Toolbar.txtMatrix_3_1": "<PERSON><PERSON> Vazi<PERSON> 3x1", "PE.Controllers.Toolbar.txtMatrix_3_2": "<PERSON><PERSON> 3x2", "PE.Controllers.Toolbar.txtMatrix_3_3": "<PERSON><PERSON> 3x3", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Pontos de linha de base", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Pontos de linha média", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Pontos diagonais", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Pontos verticais", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> entre parêntes<PERSON>", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "Matriz esparsa em parênteses", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "<PERSON>riz da identidade 2x2", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "<PERSON>riz da identidade 2x2", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "Matriz da identidade 3x3", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriz da identidade 3x3", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Seta para direita esquerda abaixo", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Seta para direita-esquerda acima", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Seta adiante para baixo", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Seta adiante para cima", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Seta para direita abaixo", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Seta para direita acima", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Dois-pontos-Sinal de Igual", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Resul<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Resultados de Delta", "PE.Controllers.Toolbar.txtOperator_Definition": "Igual a por definição", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta igual a", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Seta para direita esquerda abaixo", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Seta para direita-esquerda acima", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Seta adiante para baixo", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Seta adiante para cima", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Seta para direita abaixo", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Seta para direita acima", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Sinal de Igual-Sinal de Igual", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "Sinal de Menos-Sinal de Igual", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Sinal de Mais-Sinal de Igual", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Medido por", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Lado direito da fórmula quadrática", "PE.Controllers.Toolbar.txtRadicalCustom_2": "Raiz quadrada de a ao quadrado mais b ao quadrado", "PE.Controllers.Toolbar.txtRadicalRoot_2": "Raiz quadrada com grau", "PE.Controllers.Toolbar.txtRadicalRoot_3": "Raiz cúbica", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Radical com grau", "PE.Controllers.Toolbar.txtRadicalSqrt": "Raiz quadrada", "PE.Controllers.Toolbar.txtScriptCustom_1": "x subscrito y ao quadrado", "PE.Controllers.Toolbar.txtScriptCustom_2": "e elevado a menos i ômega t", "PE.Controllers.Toolbar.txtScriptCustom_3": "x ao quadrado", "PE.Controllers.Toolbar.txtScriptCustom_4": "Y sobrescrito à esquerda n subscrito à esquerda um", "PE.Controllers.Toolbar.txtScriptSub": "Subscrito", "PE.Controllers.Toolbar.txtScriptSubSup": "Subscrito-Sobrescrito", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Subscrito-Sobrescrito Esquerdo", "PE.Controllers.Toolbar.txtScriptSup": "Sobrescrito", "PE.Controllers.Toolbar.txtSymbol_about": "Aproximadamente", "PE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "Quase igual a", "PE.Controllers.Toolbar.txtSymbol_ast": "Operador de asterisco", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Aposta", "PE.Controllers.Toolbar.txtSymbol_bullet": "Operador de marcador", "PE.Controllers.Toolbar.txtSymbol_cap": "Interseção", "PE.Controllers.Toolbar.txtSymbol_cbrt": "Raiz cúbica", "PE.Controllers.Toolbar.txtSymbol_cdots": "Reticências horizontais de linha média", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Aproximadamente igual a", "PE.Controllers.Toolbar.txtSymbol_cup": "União", "PE.Controllers.Toolbar.txtSymbol_ddots": "Reticências diagonal para baixo à direita", "PE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Sinal de divisão", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Seta para baixo", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Conjunto vazio", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsílon", "PE.Controllers.Toolbar.txtSymbol_equals": "Igual", "PE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON><PERSON><PERSON> a", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "Existe", "PE.Controllers.Toolbar.txtSymbol_factorial": "Fatorial", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON>us Fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "Para todos", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "PE.Controllers.Toolbar.txtSymbol_geq": "Superior a ou igual a", "PE.Controllers.Toolbar.txtSymbol_gg": "Muito superior a", "PE.Controllers.Toolbar.txtSymbol_greater": "Superior a", "PE.Controllers.Toolbar.txtSymbol_in": "Elemento de", "PE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "PE.Controllers.Toolbar.txtSymbol_infinity": "Infinidade", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Capa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Seta para esquerda", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Seta esquerda-direita", "PE.Controllers.Toolbar.txtSymbol_leq": "Inferior a ou igual a", "PE.Controllers.Toolbar.txtSymbol_less": "Inferior a", "PE.Controllers.Toolbar.txtSymbol_ll": "Muito inferior a", "PE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_mp": "Sinal de Menos-Sinal de <PERSON>", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "Não igual a", "PE.Controllers.Toolbar.txtSymbol_ni": "Contém como membro", "PE.Controllers.Toolbar.txtSymbol_not": "Não entrar", "PE.Controllers.Toolbar.txtSymbol_notexists": "Não existe", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Ômega", "PE.Controllers.Toolbar.txtSymbol_partial": "Derivada parcial", "PE.Controllers.Toolbar.txtSymbol_percent": "Porcentagem", "PE.Controllers.Toolbar.txtSymbol_phi": "Fi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_pm": "Sinal de Menos-Sinal de Igual", "PE.Controllers.Toolbar.txtSymbol_propto": "Proporcional a", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "Raiz quadrada", "PE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON> da prova", "PE.Controllers.Toolbar.txtSymbol_rddots": "Reticências diagonal direitas para cima", "PE.Controllers.Toolbar.txtSymbol_rho": "Rô", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "Seta para direita", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Sinal de Radical", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "Portanto", "PE.Controllers.Toolbar.txtSymbol_theta": "Teta", "PE.Controllers.Toolbar.txtSymbol_times": "Sinal de multiplicação", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Seta para cima", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Ípsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON> de Epsílon", "PE.Controllers.Toolbar.txtSymbol_varphi": "Variante de fi", "PE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varsigma": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_vdots": "Reticências verticais", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "Ajustar slide", "PE.Controllers.Viewport.textFitWidth": "Ajustar à largura", "PE.Views.Animation.str0_5": "0,5 s (muito rápido)", "PE.Views.Animation.str1": "1 s (rápido)", "PE.Views.Animation.str2": "2 s (médio)", "PE.Views.Animation.str20": "20 s (extremamente lento)", "PE.Views.Animation.str3": "3 s (lento)", "PE.Views.Animation.str5": "5 s (muito lento)", "PE.Views.Animation.strDelay": "Atraso", "PE.Views.Animation.strDuration": "Duração", "PE.Views.Animation.strRepeat": "<PERSON><PERSON>", "PE.Views.Animation.strRewind": "Retroceder", "PE.Views.Animation.strStart": "Iniciar", "PE.Views.Animation.strTrigger": "Acionar", "PE.Views.Animation.textAutoPreview": "AutoVisualização", "PE.Views.Animation.textMoreEffects": "<PERSON>rar mais e<PERSON>os", "PE.Views.Animation.textMoveEarlier": "Mover mais cedo", "PE.Views.Animation.textMoveLater": "Mover-se depois", "PE.Views.Animation.textMultiple": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textNone": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.textNoRepeat": "(nenhum)", "PE.Views.Animation.textOnClickOf": "Em Clique de", "PE.Views.Animation.textOnClickSequence": "Em Sequência de cliques", "PE.Views.Animation.textStartAfterPrevious": "Após o anterior", "PE.Views.Animation.textStartOnClick": "No Clique", "PE.Views.Animation.textStartWithPrevious": "Com Anterior", "PE.Views.Animation.textUntilEndOfSlide": "Até o final do slide", "PE.Views.Animation.textUntilNextClick": "Até o próximo clique", "PE.Views.Animation.txtAddEffect": "Adicionar animação", "PE.Views.Animation.txtAnimationPane": "Painel de animação", "PE.Views.Animation.txtParameters": "Parâmetros", "PE.Views.Animation.txtPreview": "Pré-visualizar", "PE.Views.Animation.txtSec": "S", "PE.Views.AnimationDialog.textPreviewEffect": "Efeito de visualização", "PE.Views.AnimationDialog.textTitle": "<PERSON><PERSON>", "PE.Views.ChartSettings.text3dDepth": "Profundidade (% da base)", "PE.Views.ChartSettings.text3dHeight": "Altura (% da base)", "PE.Views.ChartSettings.text3dRotation": "Rotação 3D", "PE.Views.ChartSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "PE.Views.ChartSettings.textAutoscale": "Autoescala", "PE.Views.ChartSettings.textChartType": "Alterar tipo de gráfico", "PE.Views.ChartSettings.textDefault": "Rotação padrão", "PE.Views.ChartSettings.textDown": "Abaixo", "PE.Views.ChartSettings.textEditData": "<PERSON><PERSON> dad<PERSON>", "PE.Views.ChartSettings.textHeight": "Altura", "PE.Views.ChartSettings.textKeepRatio": "Proporções constantes", "PE.Views.ChartSettings.textLeft": "E<PERSON>rda", "PE.Views.ChartSettings.textNarrow": "Campo de visão estreito", "PE.Views.ChartSettings.textPerspective": "Perspectiva", "PE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textRightAngle": "Eixos de ângulo reto", "PE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textUp": "Para cima", "PE.Views.ChartSettings.textWiden": "Ampliar o campo de visão", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textX": "Rotação X", "PE.Views.ChartSettings.textY": "Rotação Y", "PE.Views.ChartSettingsAdvanced.textAlt": "Texto alternativo", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Descrição", "PE.Views.ChartSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma, gráfico ou tabela existe na imagem.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textCenter": "Centro", "PE.Views.ChartSettingsAdvanced.textChartName": "Nome do gráfico", "PE.Views.ChartSettingsAdvanced.textFrom": "de", "PE.Views.ChartSettingsAdvanced.textGeneral": "G<PERSON>", "PE.Views.ChartSettingsAdvanced.textHeight": "Altura", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Proporções constantes", "PE.Views.ChartSettingsAdvanced.textPlacement": "Posicionamento", "PE.Views.ChartSettingsAdvanced.textPosition": "Posição", "PE.Views.ChartSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textTitle": "Gráfico - Configurações avançadas", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "Canto superior esquerdo", "PE.Views.ChartSettingsAdvanced.textVertical": "Vertical", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Definir formato padrão para {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Definir como padrão", "PE.Views.DateTimeDialog.textFormat": "Formatos", "PE.Views.DateTimeDialog.textLang": "Idioma", "PE.Views.DateTimeDialog.textUpdate": "Atualizar automaticamente", "PE.Views.DateTimeDialog.txtTitle": "Data e hora", "PE.Views.DocumentHolder.aboveText": "Acima", "PE.Views.DocumentHolder.addCommentText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.advancedChartText": "Configurações avançadas de gráfico", "PE.Views.DocumentHolder.advancedEquationText": "Definições de equação", "PE.Views.DocumentHolder.advancedImageText": "Configurações avançadas de imagem", "PE.Views.DocumentHolder.advancedParagraphText": "Configurações avançadas de parágrafo", "PE.Views.DocumentHolder.advancedShapeText": "Configurações avançadas de forma", "PE.Views.DocumentHolder.advancedTableText": "Configurações avançadas de tabela", "PE.Views.DocumentHolder.alignmentText": "Alinhamento", "PE.Views.DocumentHolder.allLinearText": "Tudo - Linear", "PE.Views.DocumentHolder.allProfText": "Tudo - Profissional", "PE.Views.DocumentHolder.belowText": "Abaixo", "PE.Views.DocumentHolder.cellAlignText": "Alinhamento vertical da célula", "PE.Views.DocumentHolder.cellText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.centerText": "Centro", "PE.Views.DocumentHolder.columnText": "Coluna", "PE.Views.DocumentHolder.currLinearText": "Atual - Linear", "PE.Views.DocumentHolder.currProfText": "Atual - Profissional", "PE.Views.DocumentHolder.deleteColumnText": "Excluir coluna", "PE.Views.DocumentHolder.deleteRowText": "Excluir linha", "PE.Views.DocumentHolder.deleteTableText": "Excluir tabela", "PE.Views.DocumentHolder.deleteText": "Excluir", "PE.Views.DocumentHolder.direct270Text": "Girar o texto para cima", "PE.Views.DocumentHolder.direct90Text": "G<PERSON>r o texto para baixo", "PE.Views.DocumentHolder.directHText": "Horizontal", "PE.Views.DocumentHolder.directionText": "Direção do texto", "PE.Views.DocumentHolder.editChartText": "<PERSON><PERSON> dad<PERSON>", "PE.Views.DocumentHolder.editHyperlinkText": "Editar link", "PE.Views.DocumentHolder.hideEqToolbar": "Ocultar barra de ferramentas de equação", "PE.Views.DocumentHolder.hyperlinkText": "Hiperlink", "PE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON> tudo", "PE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnLeftText": "Coluna esquerda", "PE.Views.DocumentHolder.insertColumnRightText": "Coluna direita", "PE.Views.DocumentHolder.insertColumnText": "Inserir coluna", "PE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowText": "<PERSON><PERSON><PERSON> linha", "PE.Views.DocumentHolder.insertText": "Inserir", "PE.Views.DocumentHolder.langText": "Selecionar idioma", "PE.Views.DocumentHolder.latexText": "LaTex", "PE.Views.DocumentHolder.leftText": "E<PERSON>rda", "PE.Views.DocumentHolder.loadSpellText": "Carregando variantes...", "PE.Views.DocumentHolder.mergeCellsText": "Mesclar célu<PERSON>", "PE.Views.DocumentHolder.mniCustomTable": "Inserir tabela personalizada", "PE.Views.DocumentHolder.moreText": "Mais variantes...", "PE.Views.DocumentHolder.noSpellVariantsText": "Sem varientes", "PE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON> at<PERSON>", "PE.Views.DocumentHolder.removeHyperlinkText": "Remover link", "PE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "Selecionar", "PE.Views.DocumentHolder.showEqToolbar": "Mostrar Barra de Ferramentas de Equação", "PE.Views.DocumentHolder.spellcheckText": "Verificação ortográfica", "PE.Views.DocumentHolder.splitCellsText": "<PERSON><PERSON><PERSON>...", "PE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textAddHGuides": "Adici<PERSON>r g<PERSON>", "PE.Views.DocumentHolder.textAddVGuides": "Adicionar gui<PERSON> vertical", "PE.Views.DocumentHolder.textArrangeBack": "Enviar para segundo plano", "PE.Views.DocumentHolder.textArrangeBackward": "Enviar para trás", "PE.Views.DocumentHolder.textArrangeForward": "Trazer para frente", "PE.Views.DocumentHolder.textArrangeFront": "Trazer para primeiro plano", "PE.Views.DocumentHolder.textClearGuides": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textCm": "cm", "PE.Views.DocumentHolder.textCopy": "Copiar", "PE.Views.DocumentHolder.textCrop": "Cortar", "PE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFit": "Ajustar", "PE.Views.DocumentHolder.textCustom": "Personalizado", "PE.Views.DocumentHolder.textCut": "Cortar", "PE.Views.DocumentHolder.textDeleteGuide": "Excluir guia", "PE.Views.DocumentHolder.textDeleteLayout": "Excluir layout", "PE.Views.DocumentHolder.textDeleteMaster": "Excluir mestre", "PE.Views.DocumentHolder.textDistributeCols": "Distribuir colunas", "PE.Views.DocumentHolder.textDistributeRows": "Distribuir linhas", "PE.Views.DocumentHolder.textDuplicateLayout": "Layout duplicado", "PE.Views.DocumentHolder.textDuplicateSlideMaster": "Duplicar slide mestre", "PE.Views.DocumentHolder.textEditObject": "<PERSON>ar objeto", "PE.Views.DocumentHolder.textEditPoints": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textFlipH": "Virar horizontalmente", "PE.Views.DocumentHolder.textFlipV": "Virar verticalmente", "PE.Views.DocumentHolder.textFromFile": "Do arquivo", "PE.Views.DocumentHolder.textFromStorage": "De armazenamento", "PE.Views.DocumentHolder.textFromUrl": "Da URL", "PE.Views.DocumentHolder.textGridlines": "<PERSON><PERSON> de <PERSON>", "PE.Views.DocumentHolder.textGuides": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textInsertLayout": "Inserir Layout", "PE.Views.DocumentHolder.textInsertSlideMaster": "Inserir slide mestre", "PE.Views.DocumentHolder.textNextPage": "Próximo slide", "PE.Views.DocumentHolder.textPaste": "Colar", "PE.Views.DocumentHolder.textPrevPage": "Slide anterior", "PE.Views.DocumentHolder.textRemove": "Remover", "PE.Views.DocumentHolder.textRenameLayout": "Renomear Layout", "PE.Views.DocumentHolder.textRenameMaster": "Renomear mestre", "PE.Views.DocumentHolder.textReplace": "Substituir imagem", "PE.Views.DocumentHolder.textResetCrop": "Redefinir corte", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "Girar 90º no sentido anti-horário.", "PE.Views.DocumentHolder.textRotate90": "Girar 90º no sentido horário", "PE.Views.DocumentHolder.textRulers": "Regras", "PE.Views.DocumentHolder.textSaveAsPicture": "<PERSON>var como imagem", "PE.Views.DocumentHolder.textShapeAlignBottom": "Alinhar à parte inferior", "PE.Views.DocumentHolder.textShapeAlignCenter": "Alinhar ao centro", "PE.Views.DocumentHolder.textShapeAlignLeft": "Alinhar à esquerda", "PE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON>ar ao meio", "PE.Views.DocumentHolder.textShapeAlignRight": "Alinhar à direita", "PE.Views.DocumentHolder.textShapeAlignTop": "Alinhar à parte superior", "PE.Views.DocumentHolder.textShapesMerge": "Mesclar formas", "PE.Views.DocumentHolder.textShowGridlines": "Mostrar linhas de grade", "PE.Views.DocumentHolder.textShowGuides": "<PERSON>rar guias", "PE.Views.DocumentHolder.textSlideSettings": "Configurações de slide", "PE.Views.DocumentHolder.textSmartGuides": "<PERSON><PERSON><PERSON> intelige<PERSON>", "PE.Views.DocumentHolder.textSnapObjects": "Ajustar objeto à grade", "PE.Views.DocumentHolder.textStartAfterPrevious": "Comece depois do anterior", "PE.Views.DocumentHolder.textStartOnClick": "Iniciar ao clicar", "PE.Views.DocumentHolder.textStartWithPrevious": "Comece com o anterior", "PE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tipGuides": "<PERSON>rar guias", "PE.Views.DocumentHolder.tipIsLocked": "Este elemento está sendo atualmente editado por outro usuário.", "PE.Views.DocumentHolder.toDictionaryText": "Adicionar ao dicionário", "PE.Views.DocumentHolder.txtAddBottom": "Adicionar borda inferior", "PE.Views.DocumentHolder.txtAddFractionBar": "Adicionar barra de fração", "PE.Views.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "PE.Views.DocumentHolder.txtAddLB": "Adicionar linha inferior esquerda", "PE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON><PERSON> borda es<PERSON>", "PE.Views.DocumentHolder.txtAddLT": "Adicionar linha superior esquerda", "PE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON>r borda direita", "PE.Views.DocumentHolder.txtAddTop": "Adicionar borda superior", "PE.Views.DocumentHolder.txtAddVer": "Adicionar lin<PERSON>", "PE.Views.DocumentHolder.txtAlign": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON> ao caractere", "PE.Views.DocumentHolder.txtArrange": "Organizar", "PE.Views.DocumentHolder.txtBackground": "Plano de fundo", "PE.Views.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON>a", "PE.Views.DocumentHolder.txtBottom": "Inferior", "PE.Views.DocumentHolder.txtChangeLayout": "Alterar layout", "PE.Views.DocumentHolder.txtChangeTheme": "Alterar tema", "PE.Views.DocumentHolder.txtColumnAlign": "Alinhamento de colunas", "PE.Views.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON> de <PERSON>o", "PE.Views.DocumentHolder.txtDeleteArg": "Excluir argumento", "PE.Views.DocumentHolder.txtDeleteBreak": "Excluir quebra manual", "PE.Views.DocumentHolder.txtDeleteChars": "Excluir caracteres anexos", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Excluir separadores e caracteres anexos", "PE.Views.DocumentHolder.txtDeleteEq": "Excluir equação", "PE.Views.DocumentHolder.txtDeleteGroupChar": "Excluir caractere", "PE.Views.DocumentHolder.txtDeleteRadical": "Excluir radical", "PE.Views.DocumentHolder.txtDeleteSlide": "Excluir slide", "PE.Views.DocumentHolder.txtDistribHor": "Distribuir horizontalmente", "PE.Views.DocumentHolder.txtDistribVert": "Distribuir verticalmente", "PE.Views.DocumentHolder.txtDuplicateSlide": "Slide duplicado", "PE.Views.DocumentHolder.txtFractionLinear": "Alterar para fração linear", "PE.Views.DocumentHolder.txtFractionSkewed": "Alterar para fração inclinada", "PE.Views.DocumentHolder.txtFractionStacked": "Alterar para fração empilhada", "PE.Views.DocumentHolder.txtGroup": "Grupo", "PE.Views.DocumentHolder.txtGroupCharOver": "Caractere sobre texto", "PE.Views.DocumentHolder.txtGroupCharUnder": "Caractere sob texto", "PE.Views.DocumentHolder.txtHideBottom": "Ocultar borda inferior", "PE.Views.DocumentHolder.txtHideBottomLimit": "Ocultar limite inferior", "PE.Views.DocumentHolder.txtHideCloseBracket": "Ocultar colchete de fechamento", "PE.Views.DocumentHolder.txtHideDegree": "Ocultar grau", "PE.Views.DocumentHolder.txtHideHor": "Ocultar linha horizontal", "PE.Views.DocumentHolder.txtHideLB": "Ocultar linha inferior esquerda", "PE.Views.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON>r borda esquer<PERSON>", "PE.Views.DocumentHolder.txtHideLT": "Ocultar linha superior esquerda", "PE.Views.DocumentHolder.txtHideOpenBracket": "Ocultar colchete de abertura", "PE.Views.DocumentHolder.txtHidePlaceholder": "Ocultar espaço reservado", "PE.Views.DocumentHolder.txtHideRight": "O<PERSON>ltar borda direita", "PE.Views.DocumentHolder.txtHideTop": "Ocultar borda superior", "PE.Views.DocumentHolder.txtHideTopLimit": "Ocultar limite superior", "PE.Views.DocumentHolder.txtHideVer": "Ocultar linha vertical", "PE.Views.DocumentHolder.txtIncreaseArg": "Aumentar o tamanho do argumento", "PE.Views.DocumentHolder.txtInsAudio": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtInsChart": "Inserir g<PERSON>", "PE.Views.DocumentHolder.txtInsertArgAfter": "Inserir argumento após", "PE.Views.DocumentHolder.txtInsertArgBefore": "Inserir argumento antes", "PE.Views.DocumentHolder.txtInsertBreak": "Inserir quebra manual", "PE.Views.DocumentHolder.txtInsertEqAfter": "Inserir equação a seguir", "PE.Views.DocumentHolder.txtInsertEqBefore": "Inserir equação à frente", "PE.Views.DocumentHolder.txtInsImage": "Inserir imagem do arquivo", "PE.Views.DocumentHolder.txtInsImageUrl": "Inserir imagem da URL", "PE.Views.DocumentHolder.txtInsSmartArt": "Inserir SmartArt", "PE.Views.DocumentHolder.txtInsTable": "<PERSON><PERSON><PERSON> tabela", "PE.Views.DocumentHolder.txtInsVideo": "Inserir Vídeo", "PE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON> apenas texto", "PE.Views.DocumentHolder.txtLimitChange": "Alterar localização de limites", "PE.Views.DocumentHolder.txtLimitOver": "Limite sobre o texto", "PE.Views.DocumentHolder.txtLimitUnder": "Limite sob o texto", "PE.Views.DocumentHolder.txtMatchBrackets": "Combinar parênteses com a altura do argumento", "PE.Views.DocumentHolder.txtMatrixAlign": "Alinhamento de matriz", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Mover slide para o fim", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Deslize para o início", "PE.Views.DocumentHolder.txtNewSlide": "Novo slide", "PE.Views.DocumentHolder.txtOverbar": "Barra sobre texto", "PE.Views.DocumentHolder.txtPasteDestFormat": "Use o tema de destino", "PE.Views.DocumentHolder.txtPastePicture": "Imagem", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Manter formatação original", "PE.Views.DocumentHolder.txtPressLink": "Pressione {0} e clique no link", "PE.Views.DocumentHolder.txtPreview": "Iniciar apresentação de slides", "PE.Views.DocumentHolder.txtPrintSelection": "Imprimir <PERSON>", "PE.Views.DocumentHolder.txtRemFractionBar": "Remover barra de fração", "PE.Views.DocumentHolder.txtRemLimit": "Remover limite", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Remover caractere de acento", "PE.Views.DocumentHolder.txtRemoveBar": "Remover barra", "PE.Views.DocumentHolder.txtRemScripts": "Remover scripts", "PE.Views.DocumentHolder.txtRemSubscript": "Remover subscrito", "PE.Views.DocumentHolder.txtRemSuperscript": "Remover sobrescrito", "PE.Views.DocumentHolder.txtResetLayout": "Reiniciar slide", "PE.Views.DocumentHolder.txtScriptsAfter": "Scripts após o texto", "PE.Views.DocumentHolder.txtScriptsBefore": "Scripts antes do texto", "PE.Views.DocumentHolder.txtSelectAll": "Selecionar tudo", "PE.Views.DocumentHolder.txtShowBottomLimit": "Mostrar limite inferior", "PE.Views.DocumentHolder.txtShowCloseBracket": "Mostrar colchetes de fechamento", "PE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON> gra<PERSON>", "PE.Views.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON> co<PERSON> de abertura", "PE.Views.DocumentHolder.txtShowPlaceholder": "Exibir espaço reservado", "PE.Views.DocumentHolder.txtShowTopLimit": "Exibir limite superior", "PE.Views.DocumentHolder.txtSlide": "Slide", "PE.Views.DocumentHolder.txtSlideHide": "Ocultar slide", "PE.Views.DocumentHolder.txtStretchBrackets": "Esticar colchetes", "PE.Views.DocumentHolder.txtTop": "Parte superior", "PE.Views.DocumentHolder.txtUnderbar": "Barra abaixo de texto", "PE.Views.DocumentHolder.txtUngroup": "Desagrupar", "PE.Views.DocumentHolder.txtWarnUrl": "Clicar neste link pode ser prejudicial ao seu dispositivo e dados.<br>Você tem certeza de que quer continuar?", "PE.Views.DocumentHolder.unicodeText": "Unicode", "PE.Views.DocumentHolder.vertAlignText": "Alinhamento vertical", "PE.Views.DocumentPreview.goToSlideText": "Ir para slide", "PE.Views.DocumentPreview.slideIndexText": "Slide {0} de {1}", "PE.Views.DocumentPreview.txtClose": "Fechar pré-visualização", "PE.Views.DocumentPreview.txtDraw": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtEndSlideshow": "Terminar apresentação de slide", "PE.Views.DocumentPreview.txtEraser": "Apagador", "PE.Views.DocumentPreview.txtEraseScreen": "Apagar tela", "PE.Views.DocumentPreview.txtExitFullScreen": "Exit Full Screen", "PE.Views.DocumentPreview.txtFinalMessage": "Fim da pré-visualização de slide. Clique para sair.", "PE.Views.DocumentPreview.txtFullScreen": "Full Screen", "PE.Views.DocumentPreview.txtHighlighter": "Marcador", "PE.Views.DocumentPreview.txtInkColor": "<PERSON><PERSON> da tinta", "PE.Views.DocumentPreview.txtNext": "Próximo slide", "PE.Views.DocumentPreview.txtPageNumInvalid": "Número de slide inválido", "PE.Views.DocumentPreview.txtPause": "Pausar apresentação", "PE.Views.DocumentPreview.txtPen": "Caneta", "PE.Views.DocumentPreview.txtPlay": "Iniciar ap<PERSON>ent<PERSON>", "PE.Views.DocumentPreview.txtPrev": "Slide anterior", "PE.Views.DocumentPreview.txtReset": "Redefinir", "PE.Views.FileMenu.ariaFileMenu": "Menu Arquivo", "PE.Views.FileMenu.btnAboutCaption": "Sobre", "PE.Views.FileMenu.btnBackCaption": "Abrir Local do Arquivo", "PE.Views.FileMenu.btnCloseEditor": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnCloseMenuCaption": "Voltar", "PE.Views.FileMenu.btnCreateNewCaption": "Criar novo", "PE.Views.FileMenu.btnDownloadCaption": "Baixar em", "PE.Views.FileMenu.btnExitCaption": "En<PERSON><PERSON>", "PE.Views.FileMenu.btnFileOpenCaption": "Abrir", "PE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnHistoryCaption": "Histórico <PERSON>ão", "PE.Views.FileMenu.btnInfoCaption": "Informações", "PE.Views.FileMenu.btnPrintCaption": "Imprimir", "PE.Views.FileMenu.btnProtectCaption": "Proteger", "PE.Views.FileMenu.btnRecentFilesCaption": "Abrir recente", "PE.Views.FileMenu.btnRenameCaption": "Renomear", "PE.Views.FileMenu.btnReturnCaption": "Voltar para a apresentação", "PE.Views.FileMenu.btnRightsCaption": "Direitos de acesso.", "PE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON> como", "PE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Salvar Có<PERSON> Como", "PE.Views.FileMenu.btnSettingsCaption": "Configurações avançadas", "PE.Views.FileMenu.btnSwitchToMobileCaption": "Mudar para o celular", "PE.Views.FileMenu.btnToEditCaption": "Editar apresent<PERSON>", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Apresentação em branco", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Criar novo", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplicar", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Adicionar Autor", "PE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "<PERSON><PERSON><PERSON><PERSON> propried<PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplicação", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Alterar direitos de acesso", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Comum", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Propriedade do documento", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Última modificação por", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Última modificação", "PE.Views.FileMenuPanels.DocumentInfo.txtNo": "Não", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Localização", "PE.Views.FileMenuPanels.DocumentInfo.txtPresentationInfo": "Informações da Apresentação", "PE.Views.FileMenuPanels.DocumentInfo.txtProperties": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "A propriedade com esse título já existe", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Pessoas que têm direitos", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "Etiquetas", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Título de apresentação", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Carregado", "PE.Views.FileMenuPanels.DocumentInfo.txtYes": "<PERSON>m", "PE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Direitos de acesso.", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Alterar direitos de acesso", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Pessoas que têm direitos", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Aviso", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON> senha", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteger a Apresentação", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "Com assinatura", "PE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Assinaturas válidas foram adicionadas à apresentação.<br>A apresentação está protegida contra edição.", "PE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Garanta a integridade da apresentação adicionando uma<br>assinatura digital invisível", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Editar apresent<PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "A edição removerá as assinaturas da apresentação. <br> Tem certeza de que deseja continuar?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Esta apresentação foi protegida por senha", "PE.Views.FileMenuPanels.ProtectDoc.txtProtectPresentation": "Criptografar esta apresentação com uma senha", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Assinaturas válidas foram adicionadas à apresentação. A apresentação está protegida para edição.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Algumas das assinaturas digitais na apresentação são inválidas ou não puderam ser verificadas. A apresentação está protegida para edição.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON>r assina<PERSON>s", "PE.Views.FileMenuPanels.Settings.okButtonText": "Aplicar", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Modo de coedição", "PE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strFontRender": "Dicas de fonte", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignorar palavras MAIÚSCULAS", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignorar palavras com números", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Configurações de macros", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Mostrar o botão Opções de colagem quando o conteúdo for colado", "PE.Views.FileMenuPanels.Settings.strRTLSupport": "Interface RTL", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Mostrar alterações de outros usuários", "PE.Views.FileMenuPanels.Settings.strStrict": "Estrito", "PE.Views.FileMenuPanels.Settings.strTabStyle": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strTheme": "Tema de interface", "PE.Views.FileMenuPanels.Settings.strUnit": "Unidade de medida", "PE.Views.FileMenuPanels.Settings.strZoom": "Valor de zoom padrão", "PE.Views.FileMenuPanels.Settings.text10Minutes": "A cada 10 minutos", "PE.Views.FileMenuPanels.Settings.text30Minutes": "A cada 30 minutos", "PE.Views.FileMenuPanels.Settings.text5Minutes": "A cada 5 minutos", "PE.Views.FileMenuPanels.Settings.text60Minutes": "A cada hora", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "<PERSON><PERSON><PERSON> <PERSON>", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Recuperação automática", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Salvamento automático", "PE.Views.FileMenuPanels.Settings.textDisabled": "Desabilitado", "PE.Views.FileMenuPanels.Settings.textFill": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textForceSave": "<PERSON><PERSON> para servidor", "PE.Views.FileMenuPanels.Settings.textLine": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textMinute": "A cada minuto", "PE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "Configurações avançadas", "PE.Views.FileMenuPanels.Settings.txtAll": "<PERSON><PERSON><PERSON> tudo", "PE.Views.FileMenuPanels.Settings.txtAppearance": "Aparência", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opções de autocorreção...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Modo de cache padrão", "PE.Views.FileMenuPanels.Settings.txtCm": "Centímetro", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Colaboração", "PE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "Personalize o acesso rápido", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Editando e salvando", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Co-edição em tempo real. Todas as alterações são salvas automaticamente", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Ajustar slide", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Ajustar à largura", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtInch": "Polegada", "PE.Views.FileMenuPanels.Settings.txtLast": "Visualizar <PERSON>", "PE.Views.FileMenuPanels.Settings.txtLastUsed": "Usado por último", "PE.Views.FileMenuPanels.Settings.txtMac": "como SO X", "PE.Views.FileMenuPanels.Settings.txtNative": "Nativo", "PE.Views.FileMenuPanels.Settings.txtProofing": "Correção", "PE.Views.FileMenuPanels.Settings.txtPt": "Ponto", "PE.Views.FileMenuPanels.Settings.txtQuickPrint": "Mostrar o botão Impressão rápida no cabeçalho do editor", "PE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "O documento será impresso na última impressora selecionada ou padrão", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Habilitar todos", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> as macros sem uma notificação", "PE.Views.FileMenuPanels.Settings.txtScreenReader": "Habilitar o suporte ao leitor de tela", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Verificação ortográfica", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Desabilitar tudo", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "<PERSON><PERSON><PERSON> to<PERSON> as macros sem uma notificação", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Use o bot<PERSON> \"Salvar\" para sincronizar as alterações que você e outras pessoas fazem", "PE.Views.FileMenuPanels.Settings.txtTabBack": "Usar a cor da barra de ferramentas como plano de fundo das guias", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Use a tecla Alt para navegar na interface do usuário usando o teclado", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Use a tecla Option para navegar na interface do usuário usando o teclado", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Mostrar notificação", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "<PERSON><PERSON><PERSON> todas as macros com uma notificação", "PE.Views.FileMenuPanels.Settings.txtWin": "como Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Baixar como", "PE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Salvar Có<PERSON> Como", "PE.Views.GridSettings.textCm": "cm", "PE.Views.GridSettings.textCustom": "Personalizado", "PE.Views.GridSettings.textSpacing": "Espaçamento", "PE.Views.GridSettings.textTitle": "Configurações de grade", "PE.Views.HeaderFooterDialog.applyAllText": "Aplicar a todos", "PE.Views.HeaderFooterDialog.applyText": "Aplicar", "PE.Views.HeaderFooterDialog.diffLanguage": "Não é possível usar um formato de data em um idioma diferente do slide mestre. <br> Para alterar o mestre, clique em 'Aplicar a todos' em vez de 'Aplicar'", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Aviso", "PE.Views.HeaderFooterDialog.textDateTime": "Data e Hora", "PE.Views.HeaderFooterDialog.textFixed": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textFormat": "Formatos", "PE.Views.HeaderFooterDialog.textHFTitle": "Configurações de Cabeçalho/Rodapé", "PE.Views.HeaderFooterDialog.textLang": "Idioma", "PE.Views.HeaderFooterDialog.textNotes": "Notas e folhetos", "PE.Views.HeaderFooterDialog.textNotTitle": "<PERSON>ão mostrar no slide de título", "PE.Views.HeaderFooterDialog.textPageNum": "Número da página", "PE.Views.HeaderFooterDialog.textPreview": "Pré-visualizar", "PE.Views.HeaderFooterDialog.textSlide": "Slide", "PE.Views.HeaderFooterDialog.textSlideNum": "Número do slide", "PE.Views.HeaderFooterDialog.textUpdate": "Atualizar automaticamente", "PE.Views.HeaderFooterDialog.txtFooter": "Rodapé", "PE.Views.HeaderFooterDialog.txtHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Vincular a", "PE.Views.HyperlinkSettingsDialog.textDefault": "Fragmento de texto selecionado", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Inserir legenda aqui", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Inserir link aqui", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Inserir dica de ferramenta aqui", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Link externo", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Slide nesta apresentação", "PE.Views.HyperlinkSettingsDialog.textSelectFile": "Selecionar arquivo", "PE.Views.HyperlinkSettingsDialog.textSlides": "Slides", "PE.Views.HyperlinkSettingsDialog.textTipText": "Texto da dica de tela", "PE.Views.HyperlinkSettingsDialog.textTitle": "Configurações do hiperlink", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "Este campo é obrigatório", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Primeiro slide", "PE.Views.HyperlinkSettingsDialog.txtLast": "Último slide", "PE.Views.HyperlinkSettingsDialog.txtNext": "Próximo slide", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Este campo deve ser uma URL no formato \"http://www.example.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Slide anterior", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Este campo é limitado a 2083 caracteres. ", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Slide", "PE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Digite o endereço da web ou selecione um arquivo", "PE.Views.ImageSettings.strTransparency": "Opacidade", "PE.Views.ImageSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "PE.Views.ImageSettings.textCrop": "Cortar", "PE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropFit": "Ajustar", "PE.Views.ImageSettings.textCropToShape": "Cortar para dar forma", "PE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "PE.Views.ImageSettings.textEditObject": "<PERSON>ar objeto", "PE.Views.ImageSettings.textFitSlide": "Ajustar slide", "PE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFromFile": "Do arquivo", "PE.Views.ImageSettings.textFromStorage": "Do armazenamento", "PE.Views.ImageSettings.textFromUrl": "Da URL", "PE.Views.ImageSettings.textHeight": "Altura", "PE.Views.ImageSettings.textHint270": "Girar 90º no sentido anti-horário.", "PE.Views.ImageSettings.textHint90": "Girar 90º no sentido horário", "PE.Views.ImageSettings.textHintFlipH": "Virar horizontalmente", "PE.Views.ImageSettings.textHintFlipV": "Virar verticalmente", "PE.Views.ImageSettings.textInsert": "Substituir imagem", "PE.Views.ImageSettings.textOriginalSize": "Tamanho real", "PE.Views.ImageSettings.textRecentlyUsed": "Usado recentemente", "PE.Views.ImageSettings.textResetCrop": "Redefinir corte", "PE.Views.ImageSettings.textRotate90": "Girar 90°", "PE.Views.ImageSettings.textRotation": "Rotação", "PE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Texto alternativo", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Descrição", "PE.Views.ImageSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma, gráfico ou tabela existe na imagem.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "Centro", "PE.Views.ImageSettingsAdvanced.textFlipped": "Invertido", "PE.Views.ImageSettingsAdvanced.textFrom": "de", "PE.Views.ImageSettingsAdvanced.textGeneral": "G<PERSON>", "PE.Views.ImageSettingsAdvanced.textHeight": "Altura", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalmente", "PE.Views.ImageSettingsAdvanced.textImageName": "Nome da imagem", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Proporções constantes", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "<PERSON><PERSON><PERSON> at<PERSON>", "PE.Views.ImageSettingsAdvanced.textPlacement": "Posicionamento", "PE.Views.ImageSettingsAdvanced.textPosition": "Posição", "PE.Views.ImageSettingsAdvanced.textRotation": "Rotação", "PE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textTitle": "Imagem - Configurações avançadas", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "Canto superior esquerdo", "PE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "PE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.ariaLeftMenu": "<PERSON><PERSON>", "PE.Views.LeftMenu.tipAbout": "Sobre", "PE.Views.LeftMenu.tipChat": "Gráfico", "PE.Views.LeftMenu.tipComments": "Comentários", "PE.Views.LeftMenu.tipPlugins": "Plug-ins", "PE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSlides": "Slides", "PE.Views.LeftMenu.tipSupport": "Feedback e Suporte", "PE.Views.LeftMenu.tipTitles": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.txtDeveloper": "MODO DE DESENVOLVEDOR", "PE.Views.LeftMenu.txtEditor": "Editor de apresentação", "PE.Views.LeftMenu.txtLimit": "Limitar acesso", "PE.Views.LeftMenu.txtTrial": "MODO DE TESTE", "PE.Views.LeftMenu.txtTrialDev": "<PERSON><PERSON> desen<PERSON>or de teste", "PE.Views.ParagraphSettings.strLineHeight": "Espaçamento entre linhas", "PE.Views.ParagraphSettings.strParagraphSpacing": "Espaçamento de parágrafo", "PE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "PE.Views.ParagraphSettings.textAt": "Em", "PE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textExact": "Exatamente", "PE.Views.ParagraphSettings.txtAutoText": "Automático", "PE.Views.ParagraphSettingsAdvanced.noTabs": "As abas especificadas aparecerão neste campo", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON> ma<PERSON>", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "E<PERSON>rda", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Espaçamento entre linhas", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Especial", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Fonte", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Recuos e Espaçamento", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Espaçamento", "PE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscrito", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Sobrescrito", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Aba", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Alinhamento", "PE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espaçamento entre caracteres", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Aba padrão", "PE.Views.ParagraphSettingsAdvanced.textEffects": "Efeitos", "PE.Views.ParagraphSettingsAdvanced.textExact": "Exatamente", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "Primeira linha", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Suspensão", "PE.Views.ParagraphSettingsAdvanced.textJustified": "Justificado", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nenhum)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Remover", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Excluir todos", "PE.Views.ParagraphSettingsAdvanced.textSet": "Especificar", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centro", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "E<PERSON>rda", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posição da aba", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Parágrafo - Configurações avançadas", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automático", "PE.Views.PrintWithPreview.txtAllPages": "Todos os slides", "PE.Views.PrintWithPreview.txtBothSides": "Imprimir em ambos os lados", "PE.Views.PrintWithPreview.txtBothSidesLongDesc": "<PERSON><PERSON> as páginas na borda longa", "PE.Views.PrintWithPreview.txtBothSidesShortDesc": "<PERSON>ire as páginas na borda curta", "PE.Views.PrintWithPreview.txtCopies": "Cópias", "PE.Views.PrintWithPreview.txtCurrentPage": "Slide atual", "PE.Views.PrintWithPreview.txtCustomPages": "Impressão personalizada", "PE.Views.PrintWithPreview.txtEmptyTable": "Não há nada para imprimir porque a apresentação está vazia", "PE.Views.PrintWithPreview.txtHeaderFooterSettings": "Configurações de Cabeçalho/Rodapé", "PE.Views.PrintWithPreview.txtOf": "de {0}", "PE.Views.PrintWithPreview.txtOneSide": "Imprimir um lado", "PE.Views.PrintWithPreview.txtOneSideDesc": "Imprima apenas em um lado da página", "PE.Views.PrintWithPreview.txtPage": "Slide", "PE.Views.PrintWithPreview.txtPageNumInvalid": "Número do slide inválido", "PE.Views.PrintWithPreview.txtPages": "Slides", "PE.Views.PrintWithPreview.txtPaperSize": "Tamanho do papel", "PE.Views.PrintWithPreview.txtPrint": "Imprimir", "PE.Views.PrintWithPreview.txtPrintPdf": "Exportar em PDF", "PE.Views.PrintWithPreview.txtPrintRange": "Imprimir intervalo", "PE.Views.PrintWithPreview.txtPrintSides": "<PERSON><PERSON><PERSON><PERSON> lad<PERSON>", "PE.Views.RightMenu.ariaRightMenu": "Menu à direita", "PE.Views.RightMenu.txtChartSettings": "Configurações do gráfico", "PE.Views.RightMenu.txtImageSettings": "Configurações de imagem", "PE.Views.RightMenu.txtParagraphSettings": "Configurações do parágrafo", "PE.Views.RightMenu.txtShapeSettings": "Configurações da forma", "PE.Views.RightMenu.txtSignatureSettings": "Configurações de Assinatura", "PE.Views.RightMenu.txtSlideSettings": "Configurações de slide", "PE.Views.RightMenu.txtTableSettings": "Configurações da tabela", "PE.Views.RightMenu.txtTextArtSettings": "Text Art Settings", "PE.Views.ShapeSettings.strBackground": "Cor do plano de fundo", "PE.Views.ShapeSettings.strChange": "Alterar forma", "PE.Views.ShapeSettings.strColor": "Cor", "PE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strForeground": "Cor do plano de fundo", "PE.Views.ShapeSettings.strPattern": "Padrão", "PE.Views.ShapeSettings.strShadow": "Mostrar sombra", "PE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strTransparency": "Opacidade", "PE.Views.ShapeSettings.strType": "Tipo", "PE.Views.ShapeSettings.textAdjustShadow": "Ajustar sombra", "PE.Views.ShapeSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "O valor inserido está incorreto.<br>Insira um valor entre 0 pt e 1.584 pt.", "PE.Views.ShapeSettings.textColor": "Preenchimento de cor", "PE.Views.ShapeSettings.textDirection": "Direção", "PE.Views.ShapeSettings.textEditPoints": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textEditShape": "Editar forma", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textEyedropper": "Conta-gotas", "PE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFromFile": "Do arquivo", "PE.Views.ShapeSettings.textFromStorage": "De armazenamento", "PE.Views.ShapeSettings.textFromUrl": "Da URL", "PE.Views.ShapeSettings.textGradient": "Pontos de gradiente", "PE.Views.ShapeSettings.textGradientFill": "Preenchimento gradiente", "PE.Views.ShapeSettings.textHint270": "Girar 90º no sentido anti-horário.", "PE.Views.ShapeSettings.textHint90": "Girar 90º no sentido horário", "PE.Views.ShapeSettings.textHintFlipH": "Virar horizontalmente", "PE.Views.ShapeSettings.textHintFlipV": "Virar verticalmente", "PE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON> ou Textura", "PE.Views.ShapeSettings.textLinear": "Linear", "PE.Views.ShapeSettings.textMoreColors": "Mais cores", "PE.Views.ShapeSettings.textNoFill": "Sem preenchimento", "PE.Views.ShapeSettings.textNoShadow": "Sem sombra", "PE.Views.ShapeSettings.textPatternFill": "Padrão", "PE.Views.ShapeSettings.textPosition": "Posição", "PE.Views.ShapeSettings.textRadial": "Radial", "PE.Views.ShapeSettings.textRecentlyUsed": "Usado recentemente", "PE.Views.ShapeSettings.textRotate90": "Girar 90º", "PE.Views.ShapeSettings.textRotation": "Rotação", "PE.Views.ShapeSettings.textSelectImage": "Selecionar imagem", "PE.Views.ShapeSettings.textSelectTexture": "Selecionar", "PE.Views.ShapeSettings.textShadow": "Sombra", "PE.Views.ShapeSettings.textStretch": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textTexture": "Da textura", "PE.Views.ShapeSettings.textTile": "Lado a lado", "PE.Views.ShapeSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Remover ponto de gradiente", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON>pel pardo", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "Papelão", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON>o es<PERSON>ro", "PE.Views.ShapeSettings.txtGrain": "Granulação", "PE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtGreyPaper": "Papel cinza", "PE.Views.ShapeSettings.txtKnit": "Encontro", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON> linha", "PE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtWood": "Madeira", "PE.Views.ShapeSettingsAdvanced.strColumns": "Colunas", "PE.Views.ShapeSettingsAdvanced.strMargins": "Preenchimento de texto", "PE.Views.ShapeSettingsAdvanced.textAlt": "Texto alternativo", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Descrição", "PE.Views.ShapeSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma, gráfico ou tabela existe na imagem.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "Set<PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Ajuste automático", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> inicial", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON><PERSON> inici<PERSON>", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "Inferior", "PE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON> de letra", "PE.Views.ShapeSettingsAdvanced.textCenter": "Centro", "PE.Views.ShapeSettingsAdvanced.textColNumber": "Número de <PERSON>nas", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Tamanho final", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Estilo final", "PE.Views.ShapeSettingsAdvanced.textFlat": "Plano", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Invertido", "PE.Views.ShapeSettingsAdvanced.textFrom": "de", "PE.Views.ShapeSettingsAdvanced.textGeneral": "G<PERSON>", "PE.Views.ShapeSettingsAdvanced.textHeight": "Altura", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontalmente", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Tipo de junção", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Proporções constantes", "PE.Views.ShapeSettingsAdvanced.textLeft": "E<PERSON>rda", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textMiter": "Malhete", "PE.Views.ShapeSettingsAdvanced.textNofit": "Não ajustar automaticamente.", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Posicionamento", "PE.Views.ShapeSettingsAdvanced.textPosition": "Posição", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Redimensionar forma para caber no texto", "PE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRotation": "Rotação", "PE.Views.ShapeSettingsAdvanced.textRound": "Rodada", "PE.Views.ShapeSettingsAdvanced.textShapeName": "Nome da forma", "PE.Views.ShapeSettingsAdvanced.textShrink": "Reduzir o texto ao transbordar", "PE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Espaçamento entre colunas", "PE.Views.ShapeSettingsAdvanced.textSquare": "Quadrado", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Caixa de texto", "PE.Views.ShapeSettingsAdvanced.textTitle": "Forma - Configurações avançadas", "PE.Views.ShapeSettingsAdvanced.textTop": "Parte superior", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "Canto superior esquerdo", "PE.Views.ShapeSettingsAdvanced.textVertical": "Vertical", "PE.Views.ShapeSettingsAdvanced.textVertically": "Verticalmente", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Pesos e Setas", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Aviso", "PE.Views.SignatureSettings.strDelete": "Remover assinatura", "PE.Views.SignatureSettings.strDetails": "Detalhes da assinatura", "PE.Views.SignatureSettings.strInvalid": "Assinaturas inválidas", "PE.Views.SignatureSettings.strSign": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strSignature": "Assinatura", "PE.Views.SignatureSettings.strValid": "Assinaturas válidas", "PE.Views.SignatureSettings.txtContinueEditing": "<PERSON>e mesmo assim", "PE.Views.SignatureSettings.txtEditWarning": "A edição removerá as assinaturas da apresentação. <br> Tem certeza de que deseja continuar?", "PE.Views.SignatureSettings.txtRemoveWarning": "Você quer remover esta assinatura? <br><PERSON><PERSON> não pode ser desfeito.", "PE.Views.SignatureSettings.txtSigned": "Assinaturas válidas foram adicionadas à apresentação. A apresentação está protegida para edição.", "PE.Views.SignatureSettings.txtSignedInvalid": "Algumas das assinaturas digitais na apresentação são inválidas ou não puderam ser verificadas. A apresentação está protegida para edição.", "PE.Views.SlideSettings.strApplyAllSlides": "Aplicar a todos os slides", "PE.Views.SlideSettings.strBackground": "Cor do plano de fundo", "PE.Views.SlideSettings.strBackgroundGraphics": "Mostrar gráficos de fundo", "PE.Views.SlideSettings.strBackgroundReset": "Redefinir plano de fundo", "PE.Views.SlideSettings.strColor": "Cor", "PE.Views.SlideSettings.strDateTime": "Mostrar data e hora", "PE.Views.SlideSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strForeground": "Cor do plano de fundo", "PE.Views.SlideSettings.strPattern": "Padrão", "PE.Views.SlideSettings.strSlideNum": "Mostrar número do slide", "PE.Views.SlideSettings.strTransparency": "Opacidade", "PE.Views.SlideSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "Preenchimento de cor", "PE.Views.SlideSettings.textDirection": "Direção", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON><PERSON>", "PE.Views.SlideSettings.textFromFile": "Do arquivo", "PE.Views.SlideSettings.textFromStorage": "Do armazenamento", "PE.Views.SlideSettings.textFromUrl": "Da URL", "PE.Views.SlideSettings.textGradient": "Pontos de gradiente", "PE.Views.SlideSettings.textGradientFill": "Preenchimento gradiente", "PE.Views.SlideSettings.textImageTexture": "<PERSON><PERSON> ou Textura", "PE.Views.SlideSettings.textLinear": "Linear", "PE.Views.SlideSettings.textNoFill": "Sem preenchimento", "PE.Views.SlideSettings.textPatternFill": "Padrão", "PE.Views.SlideSettings.textPosition": "Posição", "PE.Views.SlideSettings.textRadial": "Radial", "PE.Views.SlideSettings.textReset": "Resetar alterações", "PE.Views.SlideSettings.textSelectImage": "Selecionar imagem", "PE.Views.SlideSettings.textSelectTexture": "Selecionar", "PE.Views.SlideSettings.textStretch": "<PERSON><PERSON>", "PE.Views.SlideSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textTexture": "Da textura", "PE.Views.SlideSettings.textTile": "Lado a lado", "PE.Views.SlideSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Remover ponto de gradiente", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON>pel pardo", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "Papelão", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON><PERSON>o es<PERSON>ro", "PE.Views.SlideSettings.txtGrain": "Granulação", "PE.Views.SlideSettings.txtGranite": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtGreyPaper": "Papel cinza", "PE.Views.SlideSettings.txtKnit": "Encontro", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtWood": "Madeira", "PE.Views.SlideshowSettings.textLoop": "Loop contínuo até \"Esc\" ser pressionado", "PE.Views.SlideshowSettings.textTitle": "<PERSON><PERSON><PERSON> configu<PERSON>", "PE.Views.SlideSizeSettings.strLandscape": "Paisagem", "PE.Views.SlideSizeSettings.strPortrait": "Retrato ", "PE.Views.SlideSizeSettings.textHeight": "Altura", "PE.Views.SlideSizeSettings.textSlideOrientation": "Orientação do slide", "PE.Views.SlideSizeSettings.textSlideSize": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textTitle": "Configurações de tamanho do slide", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "Slides de 35 mm", "PE.Views.SlideSizeSettings.txtA3": "Papel A3 (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "Papel A4 (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "Papel B4 (ICO) (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "Papel B5 (ICO) (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "Personalizar", "PE.Views.SlideSizeSettings.txtLedger": "<PERSON><PERSON>(11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "Papel carta (8,5x11 pol)", "PE.Views.SlideSizeSettings.txtOverhead": "Transparência", "PE.Views.SlideSizeSettings.txtSlideNum": "Número de slides de", "PE.Views.SlideSizeSettings.txtStandard": "<PERSON><PERSON><PERSON> (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Panorâmico", "PE.Views.Statusbar.goToPageText": "<PERSON><PERSON> para <PERSON>lide", "PE.Views.Statusbar.pageIndexText": "Slide {0} de {1}", "PE.Views.Statusbar.textShowBegin": "Mostrar do início", "PE.Views.Statusbar.textShowCurrent": "Mostrar a partir do slide atual", "PE.Views.Statusbar.textShowPresenterView": "Exibir vista de apresentador", "PE.Views.Statusbar.textSlideMaster": "Slide mestre", "PE.Views.Statusbar.tipAccessRights": "Manage document access rights", "PE.Views.Statusbar.tipFitPage": "Ajustar slide", "PE.Views.Statusbar.tipFitWidth": "<PERSON><PERSON><PERSON> largura", "PE.Views.Statusbar.tipPreview": "Iniciar apresentação de slides", "PE.Views.Statusbar.tipSetLang": "Definir idioma do texto", "PE.Views.Statusbar.tipZoomFactor": "Zoom", "PE.Views.Statusbar.tipZoomIn": "Ampliar", "PE.Views.Statusbar.tipZoomOut": "Reduzir", "PE.Views.Statusbar.txtPageNumInvalid": "Número de slide inválido", "PE.Views.TableSettings.deleteColumnText": "Excluir coluna", "PE.Views.TableSettings.deleteRowText": "Excluir linha", "PE.Views.TableSettings.deleteTableText": "Excluir tabela", "PE.Views.TableSettings.insertColumnLeftText": "Inserir coluna à esquerda", "PE.Views.TableSettings.insertColumnRightText": "Inserir coluna à direita", "PE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON> linha acima", "PE.Views.TableSettings.insertRowBelowText": "<PERSON>ser<PERSON> linha a<PERSON>o", "PE.Views.TableSettings.mergeCellsText": "Mesclar célu<PERSON>", "PE.Views.TableSettings.selectCellText": "Selecionar célula", "PE.Views.TableSettings.selectColumnText": "Selecionar coluna", "PE.Views.TableSettings.selectRowText": "Selecionar linha", "PE.Views.TableSettings.selectTableText": "Selecionar tabela", "PE.Views.TableSettings.splitCellsText": "<PERSON><PERSON><PERSON>...", "PE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "PE.Views.TableSettings.textBackColor": "Cor do plano de fundo", "PE.Views.TableSettings.textBanded": "Em tiras", "PE.Views.TableSettings.textBorderColor": "Cor", "PE.Views.TableSettings.textBorders": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textCellSize": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textColumns": "Colunas", "PE.Views.TableSettings.textDistributeCols": "Distribuir colunas", "PE.Views.TableSettings.textDistributeRows": "Distribuir linhas", "PE.Views.TableSettings.textEdit": "Linhas e colunas", "PE.Views.TableSettings.textEmptyTemplate": "Sem modelos", "PE.Views.TableSettings.textFirst": "<PERSON><PERSON>", "PE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeight": "Altura", "PE.Views.TableSettings.textLast": "Último", "PE.Views.TableSettings.textRows": "<PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON> as bordas que você deseja alterar aplicando o estilo escolhido acima", "PE.Views.TableSettings.textTemplate": "Selecionar a partir do modelo", "PE.Views.TableSettings.textTotal": "Total", "PE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "De<PERSON>ir borda externa e todas as linhas internas", "PE.Views.TableSettings.tipBottom": "Definir apenas borda inferior externa", "PE.Views.TableSettings.tipInner": "Definir apenas linhas internas", "PE.Views.TableSettings.tipInnerHor": "Definir apenas linhas internas horizontais", "PE.Views.TableSettings.tipInnerVert": "Definir apenas linhas internas verticais", "PE.Views.TableSettings.tipLeft": "Definir apenas borda esquerda externa", "PE.Views.TableSettings.tipNone": "Definir sem bordas", "PE.Views.TableSettings.tipOuter": "Definir apenas borda externa", "PE.Views.TableSettings.tipRight": "Definir apenas borda direita externa", "PE.Views.TableSettings.tipTop": "Definir apenas borda superior externa", "PE.Views.TableSettings.txtGroupTable_Custom": "Personalizado", "PE.Views.TableSettings.txtGroupTable_Dark": "Escuro", "PE.Views.TableSettings.txtGroupTable_Light": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Medium": "Médio", "PE.Views.TableSettings.txtGroupTable_Optimal": "Melhor correspondência para documento", "PE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON> bordas", "PE.Views.TableSettings.txtTable_Accent": "Ace<PERSON>", "PE.Views.TableSettings.txtTable_DarkStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_LightStyle": "<PERSON><PERSON><PERSON> claro", "PE.Views.TableSettings.txtTable_MediumStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_NoGrid": "Sem grade", "PE.Views.TableSettings.txtTable_NoStyle": "Sem estilo", "PE.Views.TableSettings.txtTable_TableGrid": "Grade da tabela", "PE.Views.TableSettings.txtTable_ThemedStyle": "Estilo <PERSON>", "PE.Views.TableSettingsAdvanced.textAlt": "Texto alternativo", "PE.Views.TableSettingsAdvanced.textAltDescription": "Descrição", "PE.Views.TableSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma, gráfico ou tabela existe na imagem.", "PE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textBottom": "Inferior", "PE.Views.TableSettingsAdvanced.textCenter": "Centro", "PE.Views.TableSettingsAdvanced.textCheckMargins": "<PERSON>ar margens pad<PERSON>", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Margens padrão", "PE.Views.TableSettingsAdvanced.textFrom": "de", "PE.Views.TableSettingsAdvanced.textGeneral": "G<PERSON>", "PE.Views.TableSettingsAdvanced.textHeight": "Altura", "PE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Proporções constantes", "PE.Views.TableSettingsAdvanced.textLeft": "E<PERSON>rda", "PE.Views.TableSettingsAdvanced.textMargins": "Margens da célula", "PE.Views.TableSettingsAdvanced.textPlacement": "Posicionamento", "PE.Views.TableSettingsAdvanced.textPosition": "Posição", "PE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTableName": "<PERSON>me da tabela", "PE.Views.TableSettingsAdvanced.textTitle": "Tabela - Configurações avançadas", "PE.Views.TableSettingsAdvanced.textTop": "Parte superior", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "Canto superior esquerdo", "PE.Views.TableSettingsAdvanced.textVertical": "Vertical", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "Margens", "PE.Views.TextArtSettings.strBackground": "Background color", "PE.Views.TextArtSettings.strColor": "Color", "PE.Views.TextArtSettings.strFill": "Fill", "PE.Views.TextArtSettings.strForeground": "Foreground color", "PE.Views.TextArtSettings.strPattern": "Pattern", "PE.Views.TextArtSettings.strSize": "Size", "PE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "Opacity", "PE.Views.TextArtSettings.strType": "Tipo", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "PE.Views.TextArtSettings.textColor": "Preenchimento de cor", "PE.Views.TextArtSettings.textDirection": "Direction", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textFromFile": "Do Arquivo", "PE.Views.TextArtSettings.textFromUrl": "Da URL", "PE.Views.TextArtSettings.textGradient": "Pontos de gradiente", "PE.Views.TextArtSettings.textGradientFill": "Preenchimento gradiente", "PE.Views.TextArtSettings.textImageTexture": "Picture or Texture", "PE.Views.TextArtSettings.textLinear": "Linear", "PE.Views.TextArtSettings.textNoFill": "Sem preenchimento", "PE.Views.TextArtSettings.textPatternFill": "Pattern", "PE.Views.TextArtSettings.textPosition": "Posição", "PE.Views.TextArtSettings.textRadial": "Radial", "PE.Views.TextArtSettings.textSelectTexture": "Select", "PE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textTemplate": "Template", "PE.Views.TextArtSettings.textTexture": "Da textura", "PE.Views.TextArtSettings.textTile": "Tile", "PE.Views.TextArtSettings.textTransform": "Transformar", "PE.Views.TextArtSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Remover ponto de gradiente", "PE.Views.TextArtSettings.txtBrownPaper": "<PERSON>pel pardo", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON>o es<PERSON>ro", "PE.Views.TextArtSettings.txtGrain": "Grain", "PE.Views.TextArtSettings.txtGranite": "Granite", "PE.Views.TextArtSettings.txtGreyPaper": "Papel cinza", "PE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.TextArtSettings.txtLeather": "Leather", "PE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON> linha", "PE.Views.TextArtSettings.txtPapyrus": "Papyrus", "PE.Views.TextArtSettings.txtWood": "<PERSON>", "PE.Views.Toolbar.capAddLayout": "Adicionar layout", "PE.Views.Toolbar.capAddSlide": "Adicionar slide", "PE.Views.Toolbar.capAddSlideMaster": "<PERSON><PERSON>onar slide mestre", "PE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capBtnDateTime": "Data e Hora", "PE.Views.Toolbar.capBtnInsHeaderFooter": "Cabeçalho/rodapé", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "Símbolo", "PE.Views.Toolbar.capBtnSlideNum": "Número do slide", "PE.Views.Toolbar.capCloseMaster": "<PERSON><PERSON><PERSON> o mestre", "PE.Views.Toolbar.capInsertAudio": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertChart": "Gráfico", "PE.Views.Toolbar.capInsertEquation": "Equação", "PE.Views.Toolbar.capInsertHyperlink": "Hiperlink", "PE.Views.Toolbar.capInsertImage": "Imagem", "PE.Views.Toolbar.capInsertPlaceholder": "Inserir espaço reservado", "PE.Views.Toolbar.capInsertShape": "Forma", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertText": "Caixa de texto", "PE.Views.Toolbar.capInsertTextArt": "Arte de texto", "PE.Views.Toolbar.capInsertVideo": "Vídeo", "PE.Views.Toolbar.capTabFile": "Arquivo", "PE.Views.Toolbar.capTabHome": "Página Inicial", "PE.Views.Toolbar.capTabInsert": "Inserir", "PE.Views.Toolbar.mniCapitalizeWords": "Utilize cada palavra", "PE.Views.Toolbar.mniCustomTable": "Inserir tabela personalizada", "PE.Views.Toolbar.mniImageFromFile": "Imagem do arquivo", "PE.Views.Toolbar.mniImageFromStorage": "Imagem de armazenamento", "PE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON> da URL", "PE.Views.Toolbar.mniInsertSSE": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniLowerCase": "minúscula", "PE.Views.Toolbar.mniSentenceCase": "Capitular o início de uma frase", "PE.Views.Toolbar.mniSlideAdvanced": "Configurações avançadas", "PE.Views.Toolbar.mniSlideStandard": "<PERSON><PERSON><PERSON> (4:3)", "PE.Views.Toolbar.mniSlideWide": "Widescreen (16:9)", "PE.Views.Toolbar.mniToggleCase": "aLTERNAR", "PE.Views.Toolbar.mniUpperCase": "MAIÚSCULO", "PE.Views.Toolbar.strMenuNoFill": "Sem preenchimento", "PE.Views.Toolbar.textAlignBottom": "Alinhar texto à parte inferior", "PE.Views.Toolbar.textAlignCenter": "Centralizar texto", "PE.Views.Toolbar.textAlignJust": "Justificar", "PE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON>ar texto à esquerda", "PE.Views.Toolbar.textAlignMiddle": "Alinhar texto ao meio", "PE.Views.Toolbar.textAlignRight": "Alinhar texto à direita", "PE.Views.Toolbar.textAlignTop": "Alinhar texto à parte superior", "PE.Views.Toolbar.textAlpha": "Letra grega pequena Alfa", "PE.Views.Toolbar.textArrangeBack": "Enviar Para Segundo Plano", "PE.Views.Toolbar.textArrangeBackward": "Enviar Para Trás", "PE.Views.Toolbar.textArrangeForward": "T<PERSON>r <PERSON>", "PE.Views.Toolbar.textArrangeFront": "Trazer Para Primeiro Plano", "PE.Views.Toolbar.textBetta": "Letra grega pequena <PERSON>", "PE.Views.Toolbar.textBlackHeart": "Copas", "PE.Views.Toolbar.textBold": "Negrito", "PE.Views.Toolbar.textBullet": "Marcador", "PE.Views.Toolbar.textChart": "Gráfico", "PE.Views.Toolbar.textColumnsCustom": "Colunas personalizadas", "PE.Views.Toolbar.textColumnsOne": "<PERSON><PERSON>", "PE.Views.Toolbar.textColumnsThree": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textColumnsTwo": "Duas <PERSON>", "PE.Views.Toolbar.textContent": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textContentVertical": "<PERSON><PERSON><PERSON><PERSON> (vertical)", "PE.Views.Toolbar.textCopyright": "Assinatura de copyright", "PE.Views.Toolbar.textDegree": "Símbolo de grau", "PE.Views.Toolbar.textDelta": "Letra grega pequena Delta", "PE.Views.Toolbar.textDivision": "Sinal de divisão", "PE.Views.Toolbar.textDollar": "Cifrão", "PE.Views.Toolbar.textEuro": "Sinal de Euro", "PE.Views.Toolbar.textFooters": "Rodapés", "PE.Views.Toolbar.textGreaterEqual": "Superior a ou igual a", "PE.Views.Toolbar.textInfinity": "Infinidade", "PE.Views.Toolbar.textItalic": "Itálico", "PE.Views.Toolbar.textLessEqual": "Inferior a ou igual a", "PE.Views.Toolbar.textLetterPi": "Letra grega pequena Pi", "PE.Views.Toolbar.textLineSpaceOptions": "Opções de espaçamento entre linhas", "PE.Views.Toolbar.textListSettings": "Configurações da lista", "PE.Views.Toolbar.textMoreSymbols": "<PERSON><PERSON>", "PE.Views.Toolbar.textNotEqualTo": "Não igual a", "PE.Views.Toolbar.textOneHalf": "Fração ordinária um segundo", "PE.Views.Toolbar.textOneQuarter": "Fração ordinária um quarto", "PE.Views.Toolbar.textPicture": "Imagem", "PE.Views.Toolbar.textPlusMinus": "Sinal de mais-menos", "PE.Views.Toolbar.textRecentlyUsed": "Usado recentemente", "PE.Views.Toolbar.textRegistered": "Símbolo de marca registrada", "PE.Views.Toolbar.textSection": "Sinal de seção", "PE.Views.Toolbar.textShapeAlignBottom": "Alinhar à parte inferior", "PE.Views.Toolbar.textShapeAlignCenter": "Alinhar ao centro", "PE.Views.Toolbar.textShapeAlignLeft": "Alinhar à esquerda", "PE.Views.Toolbar.textShapeAlignMiddle": "<PERSON><PERSON>ar ao meio", "PE.Views.Toolbar.textShapeAlignRight": "Alinhar à direita", "PE.Views.Toolbar.textShapeAlignTop": "Alinhar à parte superior", "PE.Views.Toolbar.textShapesCombine": "Combinar", "PE.Views.Toolbar.textShapesFragment": "Fragmento", "PE.Views.Toolbar.textShapesIntersect": "Intersecção", "PE.Views.Toolbar.textShapesSubstract": "Subtrair", "PE.Views.Toolbar.textShapesUnion": "União", "PE.Views.Toolbar.textShowBegin": "Mostrar do início", "PE.Views.Toolbar.textShowCurrent": "Mostrar a partir do slide atual", "PE.Views.Toolbar.textShowPresenterView": "Exibir vista de apresentador", "PE.Views.Toolbar.textShowSettings": "<PERSON><PERSON><PERSON> configu<PERSON>", "PE.Views.Toolbar.textSmartArt": "SmartArt", "PE.Views.Toolbar.textSmile": "Rosto sorridente branco", "PE.Views.Toolbar.textSquareRoot": "Raiz quadrada", "PE.Views.Toolbar.textStrikeout": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textSubscript": "Subscrito", "PE.Views.Toolbar.textSuperscript": "Sobrescrito", "PE.Views.Toolbar.textTabAnimation": "Animação", "PE.Views.Toolbar.textTabCollaboration": "Colaboração", "PE.Views.Toolbar.textTabDesign": "Design", "PE.Views.Toolbar.textTabDraw": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabFile": "Arquivo", "PE.Views.Toolbar.textTabHome": "Página Inicial", "PE.Views.Toolbar.textTabInsert": "Inserir", "PE.Views.Toolbar.textTable": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabProtect": "Proteção", "PE.Views.Toolbar.textTabTransitions": "Transições", "PE.Views.Toolbar.textTabView": "<PERSON>er", "PE.Views.Toolbar.textText": "Тexto", "PE.Views.Toolbar.textTextVertical": "Texto (vertical)", "PE.Views.Toolbar.textTilde": "Til", "PE.Views.Toolbar.textTitle": "Titulo", "PE.Views.Toolbar.textTitleError": "Erro", "PE.Views.Toolbar.textTradeMark": "Sinal de marca registrada", "PE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textYen": "<PERSON><PERSON> <PERSON> i<PERSON>", "PE.Views.Toolbar.tipAddLayout": "Adicionar layout", "PE.Views.Toolbar.tipAddSlide": "Adicionar slide", "PE.Views.Toolbar.tipAddSlideMaster": "<PERSON><PERSON>onar slide mestre", "PE.Views.Toolbar.tipBack": "Voltar", "PE.Views.Toolbar.tipChangeCase": "Alternar maiúscula/minúscula", "PE.Views.Toolbar.tipChangeChart": "Alterar tipo de gráfico", "PE.Views.Toolbar.tipChangeSlide": "Alterar layout do slide", "PE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON> estilo", "PE.Views.Toolbar.tipCloseMaster": "<PERSON><PERSON><PERSON> o mestre", "PE.Views.Toolbar.tipColorSchemas": "Alterar esquema de cor", "PE.Views.Toolbar.tipColumns": "Inserir colunas", "PE.Views.Toolbar.tipCopy": "Copiar", "PE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> est<PERSON>", "PE.Views.Toolbar.tipCut": "Cortar", "PE.Views.Toolbar.tipDateTime": "Insira a data e hora atuais", "PE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON><PERSON> o tamanho da fonte", "PE.Views.Toolbar.tipDecPrLeft": "<PERSON><PERSON><PERSON><PERSON> recuo", "PE.Views.Toolbar.tipEditHeaderFooter": "Editar cabeçalho e rodapé", "PE.Views.Toolbar.tipFontColor": "<PERSON><PERSON> da fonte", "PE.Views.Toolbar.tipFontName": "Fonte", "PE.Views.Toolbar.tipFontSize": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipHAligh": "Alinhamento horizontal", "PE.Views.Toolbar.tipHighlightColor": "Cor de realce", "PE.Views.Toolbar.tipIncFont": "Aumentar tamanho da fonte", "PE.Views.Toolbar.tipIncPrLeft": "Aumentar recuo", "PE.Views.Toolbar.tipInsertAudio": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertChart": "Inserir g<PERSON>", "PE.Views.Toolbar.tipInsertChartPlaceholder": "Duplicar slide mestre", "PE.Views.Toolbar.tipInsertContentPlaceholder": "Inserir espaço reservado para conteúdo", "PE.Views.Toolbar.tipInsertContentVerticalPlaceholder": "Inserir espaço reservado de conteúdo (vertical)", "PE.Views.Toolbar.tipInsertEquation": "Inserir equação", "PE.Views.Toolbar.tipInsertHorizontalText": "Inserir caixa de texto horizontal", "PE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertImage": "Inserir imagem", "PE.Views.Toolbar.tipInsertPicturePlaceholder": "Inserir espaço reservado para imagem", "PE.Views.Toolbar.tipInsertShape": "Inserir forma", "PE.Views.Toolbar.tipInsertSmartArt": "Inserir SmartArt", "PE.Views.Toolbar.tipInsertSmartArtPlaceholder": "Inserir espaço reservado smartArt", "PE.Views.Toolbar.tipInsertSymbol": "Inserir sí<PERSON>lo", "PE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> tabela", "PE.Views.Toolbar.tipInsertTablePlaceholder": "Inserir espaço reservado para tabela", "PE.Views.Toolbar.tipInsertText": "Inserir caixa de texto", "PE.Views.Toolbar.tipInsertTextArt": "Inserir arte de texto", "PE.Views.Toolbar.tipInsertTextPlaceholder": "Inserir espaço reservado para texto", "PE.Views.Toolbar.tipInsertTextVerticalPlaceholder": "Inserir espaço reservado para texto (vertical)", "PE.Views.Toolbar.tipInsertVerticalText": "Inserir caixa de texto vertical", "PE.Views.Toolbar.tipInsertVideo": "Inserir Vídeo", "PE.Views.Toolbar.tipLineSpace": "Espaçamento de linha", "PE.Views.Toolbar.tipMarkers": "Marcadores", "PE.Views.Toolbar.tipMarkersArrow": "<PERSON>las de flecha", "PE.Views.Toolbar.tipMarkersCheckmark": "Marcas de verificação", "PE.Views.Toolbar.tipMarkersDash": "Marcadores de roteiro", "PE.Views.Toolbar.tipMarkersFRhombus": "Vinhetas rômbicas cheias", "PE.Views.Toolbar.tipMarkersFRound": "Balas redondas cheias", "PE.Views.Toolbar.tipMarkersFSquare": "Balas quadradas cheias", "PE.Views.Toolbar.tipMarkersHRound": "Balas redondas ocas", "PE.Views.Toolbar.tipMarkersStar": "Balas de estrelas", "PE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipNumbers": "Numeração", "PE.Views.Toolbar.tipPaste": "Colar", "PE.Views.Toolbar.tipPreview": "Iniciar apresentação de slides", "PE.Views.Toolbar.tipPrint": "Imprimir", "PE.Views.Toolbar.tipPrintQuick": "Impressão rápida", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipReplace": "Substituir", "PE.Views.Toolbar.tipSave": "<PERSON><PERSON>", "PE.Views.Toolbar.tipSaveCoauth": "<PERSON>var suas alterações para que os outros usuários as vejam.", "PE.Views.Toolbar.tipSelectAll": "Selecionar todos", "PE.Views.Toolbar.tipShapeAlign": "<PERSON><PERSON><PERSON> forma", "PE.Views.Toolbar.tipShapeArrange": "Organizar forma", "PE.Views.Toolbar.tipShapesMerge": "Mesclar formas", "PE.Views.Toolbar.tipSlideNum": "Insira o número do slide", "PE.Views.Toolbar.tipSlideSize": "Selecionar tamanho do slide", "PE.Views.Toolbar.tipSlideTheme": "Tema do slide", "PE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipVAligh": "Alinhamento vertical", "PE.Views.Toolbar.tipViewSettings": "Visualizar configurações", "PE.Views.Toolbar.txtColors": "Cores", "PE.Views.Toolbar.txtDistribHor": "Distribuir horizontalmente", "PE.Views.Toolbar.txtDistribVert": "Distribuir verticalmente", "PE.Views.Toolbar.txtDuplicateSlide": "Slide duplicado", "PE.Views.Toolbar.txtGroup": "Grupo", "PE.Views.Toolbar.txtObjectsAlign": "<PERSON><PERSON><PERSON> objetos selecionados", "PE.Views.Toolbar.txtSlideAlign": "<PERSON><PERSON><PERSON> ao slide", "PE.Views.Toolbar.txtSlideSize": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtUngroup": "Desagrupar", "PE.Views.Transitions.strDelay": "Atraso", "PE.Views.Transitions.strDuration": "Duração", "PE.Views.Transitions.strStartOnClick": "Iniciar ao clicar", "PE.Views.Transitions.textBlack": "Através do preto", "PE.Views.Transitions.textBottom": "Do fundo", "PE.Views.Transitions.textBottomLeft": "Inferior esquerdo", "PE.Views.Transitions.textBottomRight": "Inferior direito", "PE.Views.Transitions.textClock": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textClockwise": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textCounterclockwise": "<PERSON><PERSON>do <PERSON>-<PERSON>", "PE.Views.Transitions.textCover": "Folha de rosto", "PE.Views.Transitions.textFade": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textHorizontalIn": "Horizontal para dentro", "PE.Views.Transitions.textHorizontalOut": "Horizontal para fora", "PE.Views.Transitions.textLeft": "Da esquerda", "PE.Views.Transitions.textMorph": "Morfologia", "PE.Views.Transitions.textMorphLetters": "<PERSON><PERSON>", "PE.Views.Transitions.textMorphObjects": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textMorphWord": "Palavras", "PE.Views.Transitions.textNone": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textPush": "Empurrar", "PE.Views.Transitions.textRandom": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textRight": "Da direita", "PE.Views.Transitions.textSmoothly": "Suavemente", "PE.Views.Transitions.textSplit": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textTop": "Parte superior", "PE.Views.Transitions.textTopLeft": "Parte superior esquerda", "PE.Views.Transitions.textTopRight": "Parte superior direita", "PE.Views.Transitions.textUnCover": "Descobrir", "PE.Views.Transitions.textVerticalIn": "Vertical para dentro", "PE.Views.Transitions.textVerticalOut": "Vertical para fora", "PE.Views.Transitions.textWedge": "Triangular", "PE.Views.Transitions.textWipe": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoom": "Zoom", "PE.Views.Transitions.textZoomIn": "Ampliar", "PE.Views.Transitions.textZoomOut": "Reduzir", "PE.Views.Transitions.textZoomRotate": "Zoom e Rotação", "PE.Views.Transitions.txtApplyToAll": "Aplicar a todos os slides", "PE.Views.Transitions.txtParameters": "Parâmetros", "PE.Views.Transitions.txtPreview": "Pré-visualizar", "PE.Views.Transitions.txtSec": "S", "PE.Views.ViewTab.capBtnHand": "Mão", "PE.Views.ViewTab.capBtnSelect": "Selecionar", "PE.Views.ViewTab.textAddHGuides": "Adici<PERSON>r g<PERSON>", "PE.Views.ViewTab.textAddVGuides": "Adicionar gui<PERSON> vertical", "PE.Views.ViewTab.textAlwaysShowToolbar": "Sempre mostrar a barra de ferramentas", "PE.Views.ViewTab.textClearGuides": "<PERSON><PERSON>", "PE.Views.ViewTab.textCm": "cm", "PE.Views.ViewTab.textCustom": "Personalizado", "PE.Views.ViewTab.textFill": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textFitToSlide": "Ajustar slide", "PE.Views.ViewTab.textFitToWidth": "<PERSON><PERSON><PERSON> largura", "PE.Views.ViewTab.textGridlines": "<PERSON><PERSON> de <PERSON>", "PE.Views.ViewTab.textGuides": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textInterfaceTheme": "Tema de interface", "PE.Views.ViewTab.textLeftMenu": "<PERSON><PERSON>", "PE.Views.ViewTab.textLine": "<PERSON><PERSON>", "PE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "PE.Views.ViewTab.textNormal": "Normal", "PE.Views.ViewTab.textNotes": "Notas", "PE.Views.ViewTab.textRightMenu": "<PERSON><PERSON> dire<PERSON>", "PE.Views.ViewTab.textRulers": "Regras", "PE.Views.ViewTab.textShowGridlines": "Mostrar linhas de grade", "PE.Views.ViewTab.textShowGuides": "<PERSON>rar guias", "PE.Views.ViewTab.textSlideMaster": "Slide mestre", "PE.Views.ViewTab.textSmartGuides": "<PERSON><PERSON><PERSON> intelige<PERSON>", "PE.Views.ViewTab.textSnapObjects": "Ajustar Objeto à <PERSON>", "PE.Views.ViewTab.textStatusBar": "Barra de status", "PE.Views.ViewTab.textTabStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textZoom": "Zoom", "PE.Views.ViewTab.tipFitToSlide": "Ajustar slide", "PE.Views.ViewTab.tipFitToWidth": "<PERSON><PERSON><PERSON> largura", "PE.Views.ViewTab.tipGridlines": "Mostrar linhas de grade", "PE.Views.ViewTab.tipGuides": "<PERSON>rar guias", "PE.Views.ViewTab.tipHandTool": "Ferramenta de mão", "PE.Views.ViewTab.tipInterfaceTheme": "Tema de interface", "PE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "PE.Views.ViewTab.tipNormal": "Normal", "PE.Views.ViewTab.tipSelectTool": "Selecionar ferramenta", "PE.Views.ViewTab.tipSlideMaster": "Slide mestre", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}