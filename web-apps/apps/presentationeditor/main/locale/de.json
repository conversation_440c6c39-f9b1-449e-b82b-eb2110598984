{"Common.Controllers.Chat.notcriticalErrorTitle": "Achtung", "Common.Controllers.Desktop.hintBtnHome": "Hauptfenster anzeigen", "Common.Controllers.Desktop.itemCreateFromTemplate": "Aus Vorlage erzeugen", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Anonym", "Common.Controllers.ExternalDiagramEditor.textClose": "Schließen", "Common.Controllers.ExternalDiagramEditor.warningText": "Das Objekt ist deaktiviert, weil es momentan von einem anderen <PERSON>utzer bearbeitet wird.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Achtung", "Common.Controllers.ExternalOleEditor.textAnonymous": "Anonym", "Common.Controllers.ExternalOleEditor.textClose": "Schließen", "Common.Controllers.ExternalOleEditor.warningText": "Das Objekt ist deaktiviert, weil es momentan von einem anderen <PERSON>utzer bearbeitet wird.", "Common.Controllers.ExternalOleEditor.warningTitle": "Achtung", "Common.Controllers.History.notcriticalErrorTitle": "Achtung", "Common.Controllers.History.txtErrorLoadHistory": "Laden der Historie ist fehlgeschlagen ", "Common.Controllers.Plugins.helpUseMacros": "Die Schaltfläche \"Mak<PERSON>\" finden Sie hier.", "Common.Controllers.Plugins.helpUseMacrosHeader": "Geänderter Zugriff auf Makros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Die Plugins wurden erfolgreich installiert. Sie können hier auf alle Hintergrund-Plugins zugreifen.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> wurde erfolgreich installiert. Sie können hier auf alle Hintergrund-Plugins zugreifen.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Installierte Plugins starten", "Common.Controllers.Plugins.textRunPlugin": "Plugin starten", "Common.define.chartData.textArea": "Fläche", "Common.define.chartData.textAreaStacked": "Gestapelte Fläche", "Common.define.chartData.textAreaStackedPer": "100% Gestapelte Fläche", "Common.define.chartData.textBar": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Gruppierte Säule", "Common.define.chartData.textBarNormal3d": "Gruppierte 3D-Säule", "Common.define.chartData.textBarNormal3dPerspective": "3D-Säule", "Common.define.chartData.textBarStacked": "Gestapelte Säulen", "Common.define.chartData.textBarStacked3d": "Gestapelte 3D-Säule", "Common.define.chartData.textBarStackedPer": "100% Gestapelte Säule", "Common.define.chartData.textBarStackedPer3d": "3D 100% Gestapelte Säule", "Common.define.chartData.textCharts": "Diagramme", "Common.define.chartData.textColumn": "<PERSON>lt<PERSON>", "Common.define.chartData.textCombo": "Verbund", "Common.define.chartData.textComboAreaBar": "Gestapelte Flächen/Gruppierte Säulen", "Common.define.chartData.textComboBarLine": "Gruppierte Säulen - Linie", "Common.define.chartData.textComboBarLineSecondary": "Gruppierte Säulen/Linien auf der Sekundärachse", "Common.define.chartData.textComboCustom": "Benutzerdefinierte Kombination", "Common.define.chartData.textDoughnut": "Ring", "Common.define.chartData.textHBarNormal": "Gruppierte Balken", "Common.define.chartData.textHBarNormal3d": "Gruppierte 3D-Balken", "Common.define.chartData.textHBarStacked": "Gestapelte Balken", "Common.define.chartData.textHBarStacked3d": "Gestapelte 3D-Balken", "Common.define.chartData.textHBarStackedPer": "100% Gestapelte Balken", "Common.define.chartData.textHBarStackedPer3d": "3D 100% Gestapelte Balken", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "3D-<PERSON><PERSON>", "Common.define.chartData.textLineMarker": "<PERSON><PERSON> mit Datenpunkten", "Common.define.chartData.textLineStacked": "Gestapelte Linie", "Common.define.chartData.textLineStackedMarker": "Gestapelte Linie mit Datenpunkten", "Common.define.chartData.textLineStackedPer": "100% Gestapelte Linie", "Common.define.chartData.textLineStackedPerMarker": "100% Gestapelte Linie mit Datenpunkten", "Common.define.chartData.textPie": "Kreis", "Common.define.chartData.textPie3d": "3D-Kreis", "Common.define.chartData.textPoint": "<PERSON><PERSON> (XY)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Gefülltes Radardiagramm", "Common.define.chartData.textRadarMarker": "Radar mit Markierungen", "Common.define.chartData.textScatter": "Punkte", "Common.define.chartData.textScatterLine": "Punkte mit geraden Linien", "Common.define.chartData.textScatterLineMarker": "Punkte mit geraden Linien und Datenpunkten", "Common.define.chartData.textScatterSmooth": "Punkte mit interpolierten Linien", "Common.define.chartData.textScatterSmoothMarker": "Punkte mit interpolierten Linien und Datenpunkten", "Common.define.chartData.textStock": "<PERSON><PERSON>", "Common.define.chartData.textSurface": "Oberfläche", "Common.define.effectData.textAcross": "Quer", "Common.define.effectData.textAppear": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textArcDown": "Bogen nach unten", "Common.define.effectData.textArcLeft": "Bogen nach links", "Common.define.effectData.textArcRight": "Bogen nach rechts", "Common.define.effectData.textArcs": "Bögen", "Common.define.effectData.textArcUp": "Bogen nach oben", "Common.define.effectData.textBasic": "Grundlegende", "Common.define.effectData.textBasicSwivel": "<PERSON><PERSON><PERSON> e<PERSON><PERSON>ch", "Common.define.effectData.textBasicZoom": "<PERSON>m e<PERSON><PERSON>ch", "Common.define.effectData.textBean": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBlinds": "Jalousie", "Common.define.effectData.textBlink": "Blinken", "Common.define.effectData.textBoldFlash": "Deutlicher Blitz", "Common.define.effectData.textBoldReveal": "<PERSON><PERSON> anzeigen", "Common.define.effectData.textBoomerang": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounce": "Springen", "Common.define.effectData.textBounceLeft": "<PERSON><PERSON> au<PERSON>", "Common.define.effectData.textBounceRight": "Rechts aufprallen", "Common.define.effectData.textBox": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBrushColor": "Pinselfarbe", "Common.define.effectData.textCenterRevolve": "Um Zentrum drehen", "Common.define.effectData.textCheckerboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCircle": "Kreis", "Common.define.effectData.textCollapse": "Reduzieren", "Common.define.effectData.textColorPulse": "Farbimpuls", "Common.define.effectData.textComplementaryColor": "Komplementärfarbe", "Common.define.effectData.textComplementaryColor2": "Komplementärfarbe 2", "Common.define.effectData.textCompress": "Komprimieren", "Common.define.effectData.textContrast": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textContrastingColor": "Kontrastfarbe", "Common.define.effectData.textCredits": "Abspann", "Common.define.effectData.textCrescentMoon": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCurveDown": "Gekrümmt nach unten", "Common.define.effectData.textCurvedSquare": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCurvedX": "Kurviges X", "Common.define.effectData.textCurvyLeft": "Kurvig nach links", "Common.define.effectData.textCurvyRight": "<PERSON><PERSON><PERSON>g nach rechts", "Common.define.effectData.textCurvyStar": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCustomPath": "Benutzerdefinierter Pfad", "Common.define.effectData.textCuverUp": "Gekrümmt nach oben", "Common.define.effectData.textDarken": "Verdunkeln", "Common.define.effectData.textDecayingWave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Welle", "Common.define.effectData.textDesaturate": "Entsättigen", "Common.define.effectData.textDiagonalDownRight": "Diagonal, in rechte untere Ecke", "Common.define.effectData.textDiagonalUpRight": "Diagonal, in linke untere Ecke", "Common.define.effectData.textDiamond": "<PERSON><PERSON>", "Common.define.effectData.textDisappear": "Verschwinden", "Common.define.effectData.textDissolveIn": "Manifestieren", "Common.define.effectData.textDissolveOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textDown": "Nach unten", "Common.define.effectData.textDrop": "Fallen lassen", "Common.define.effectData.textEmphasis": "Hervorhebungseffekt", "Common.define.effectData.textEntrance": "Eingangseffekt", "Common.define.effectData.textEqualTriangle": "Gleichseitiges Dreieck", "Common.define.effectData.textExciting": "Spektakulär", "Common.define.effectData.textExit": "Ausgangseffekt", "Common.define.effectData.textExpand": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFade": "Ver<PERSON><PERSON>", "Common.define.effectData.textFigureFour": "<PERSON><PERSON> übereinander", "Common.define.effectData.textFillColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFlip": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFloat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFloatDown": "Abwärts schweben", "Common.define.effectData.textFloatIn": "Hineinschweben", "Common.define.effectData.textFloatOut": "Herausschweben", "Common.define.effectData.textFloatUp": "Aufwärts schweben", "Common.define.effectData.textFlyIn": "Hineinfliegen", "Common.define.effectData.textFlyOut": "Hinausfliegen", "Common.define.effectData.textFontColor": "Schriftfarbe", "Common.define.effectData.textFootball": "Fußball", "Common.define.effectData.textFromBottom": "<PERSON> unten", "Common.define.effectData.textFromBottomLeft": "<PERSON> links unten", "Common.define.effectData.textFromBottomRight": "Von rechts unten", "Common.define.effectData.textFromLeft": "Von links", "Common.define.effectData.textFromRight": "<PERSON>", "Common.define.effectData.textFromTop": "<PERSON>ben", "Common.define.effectData.textFromTopLeft": "<PERSON> links oben", "Common.define.effectData.textFromTopRight": "Von rechts oben", "Common.define.effectData.textFunnel": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textGrowShrink": "Vergrößern/Verkleinern", "Common.define.effectData.textGrowTurn": "Wachsen und Bewegen", "Common.define.effectData.textGrowWithColor": "<PERSON>t <PERSON> zu<PERSON>hmend", "Common.define.effectData.textHeart": "<PERSON><PERSON>", "Common.define.effectData.textHeartbeat": "Herzschlag", "Common.define.effectData.textHexagon": "Sechseck", "Common.define.effectData.textHorizontal": "Horizontal", "Common.define.effectData.textHorizontalFigure": "Horizontale Acht", "Common.define.effectData.textHorizontalIn": "Horizontal nach innen", "Common.define.effectData.textHorizontalOut": "Horizontal nach außen", "Common.define.effectData.textIn": "In", "Common.define.effectData.textInFromScreenCenter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von Bildschirm<PERSON>", "Common.define.effectData.textInSlightly": "Etwas vergrößern", "Common.define.effectData.textInToScreenBottom": "Nach innen zum unteren Bildschirmbereich", "Common.define.effectData.textInvertedSquare": "Invertiertes Quadrat", "Common.define.effectData.textInvertedTriangle": "Invertiertes Dreieck", "Common.define.effectData.textLeft": "Nach links", "Common.define.effectData.textLeftDown": "<PERSON>s nach unten", "Common.define.effectData.textLeftUp": "<PERSON>s nach oben", "Common.define.effectData.textLighten": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textLineColor": "Linienfarbe", "Common.define.effectData.textLines": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textLinesCurves": "Linien und Kurven", "Common.define.effectData.textLoopDeLoop": "Zwei Schleifen", "Common.define.effectData.textLoops": "Schleifen", "Common.define.effectData.textModerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textNeutron": "Neutron", "Common.define.effectData.textObjectCenter": "Objektmitte", "Common.define.effectData.textObjectColor": "Objektfarbe", "Common.define.effectData.textOctagon": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textOut": "Außen", "Common.define.effectData.textOutFromScreenBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON> von unten", "Common.define.effectData.textOutSlightly": "Etwas verkleinern", "Common.define.effectData.textOutToScreenCenter": "Nach außen zur Bildschirmmitte", "Common.define.effectData.textParallelogram": "Parallelogramm", "Common.define.effectData.textPath": "Animationspfad", "Common.define.effectData.textPathCurve": "<PERSON><PERSON>", "Common.define.effectData.textPathLine": "<PERSON><PERSON>", "Common.define.effectData.textPathScribble": "Skizze", "Common.define.effectData.textPeanut": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textPeekIn": "Hineinblitzen", "Common.define.effectData.textPeekOut": "Hervorblitzen", "Common.define.effectData.textPentagon": "Richtungspfeil", "Common.define.effectData.textPinwheel": "Sprossenrad", "Common.define.effectData.textPlus": "Plus", "Common.define.effectData.textPointStar": "<PERSON> mit Zacken", "Common.define.effectData.textPointStar4": "Stern mit 4 Zack<PERSON>", "Common.define.effectData.textPointStar5": "Stern mit 5 Zack<PERSON>", "Common.define.effectData.textPointStar6": "Stern mit 6 <PERSON><PERSON>", "Common.define.effectData.textPointStar8": "Stern mit 8 <PERSON><PERSON>", "Common.define.effectData.textPulse": "Impuls", "Common.define.effectData.textRandomBars": "Zufällige Balken", "Common.define.effectData.textRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textRightDown": "Re<PERSON>s nach unten", "Common.define.effectData.textRightTriangle": "Rechtwinkliges Dreieck", "Common.define.effectData.textRightUp": "Rechts nach oben", "Common.define.effectData.textRiseUp": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSCurve1": "S-Kurve 1", "Common.define.effectData.textSCurve2": "S-Kurve 2", "Common.define.effectData.textShape": "Form", "Common.define.effectData.textShapes": "Formen", "Common.define.effectData.textShimmer": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textShrinkTurn": "Verkleinern und Drehen", "Common.define.effectData.textSineWave": "Sinusschwingung", "Common.define.effectData.textSinkDown": "Hina<PERSON>ink<PERSON>", "Common.define.effectData.textSlideCenter": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSpecial": "Spezielle Effekte", "Common.define.effectData.textSpin": "Rotieren", "Common.define.effectData.textSpinner": "Wirbeln", "Common.define.effectData.textSpiralIn": "Spiral<PERSON>", "Common.define.effectData.textSpiralLeft": "Spirale links", "Common.define.effectData.textSpiralOut": "<PERSON><PERSON><PERSON><PERSON> hinaus", "Common.define.effectData.textSpiralRight": "Spirale rechts", "Common.define.effectData.textSplit": "Aufteilen", "Common.define.effectData.textSpoke1": "1 Speiche", "Common.define.effectData.textSpoke2": "2 Speichen", "Common.define.effectData.textSpoke3": "3 Speichen", "Common.define.effectData.textSpoke4": "4 Speichen", "Common.define.effectData.textSpoke8": "8 Speichen", "Common.define.effectData.textSpring": "Spiral<PERSON>", "Common.define.effectData.textSquare": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textStairsDown": "Treppen nach unten", "Common.define.effectData.textStretch": "Ausdehnung", "Common.define.effectData.textStrips": "Streifen", "Common.define.effectData.textSubtle": "Dezent", "Common.define.effectData.textSwivel": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSwoosh": "Pinselstrich", "Common.define.effectData.textTeardrop": "Tropfenförmig", "Common.define.effectData.textTeeter": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textToBottom": "Nach unten", "Common.define.effectData.textToBottomLeft": "Nach links unten", "Common.define.effectData.textToBottomRight": "Nach rechts unten", "Common.define.effectData.textToLeft": "Nach links", "Common.define.effectData.textToRight": "<PERSON>ch rechts", "Common.define.effectData.textToTop": "Nach oben", "Common.define.effectData.textToTopLeft": "Nach links oben", "Common.define.effectData.textToTopRight": "Nach rechts oben", "Common.define.effectData.textTransparency": "Transparenz", "Common.define.effectData.textTrapezoid": "Trapezoid", "Common.define.effectData.textTurnDown": "Nach unten drehen", "Common.define.effectData.textTurnDownRight": "Nach rechts unten drehen", "Common.define.effectData.textTurns": "Wendungen", "Common.define.effectData.textTurnUp": "Nach oben drehen", "Common.define.effectData.textTurnUpRight": "Nach oben rechts drehen", "Common.define.effectData.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textUp": "Nach oben", "Common.define.effectData.textVertical": "Vertikal", "Common.define.effectData.textVerticalFigure": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textVerticalIn": "Vertikal nach innen", "Common.define.effectData.textVerticalOut": "Vertikal nach außen", "Common.define.effectData.textWave": "Welle", "Common.define.effectData.textWedge": "Keil", "Common.define.effectData.textWheel": "<PERSON><PERSON>", "Common.define.effectData.textWhip": "Fegen", "Common.define.effectData.textWipe": "Wischen", "Common.define.effectData.textZigzag": "Zickzack", "Common.define.effectData.textZoom": "Zoom", "Common.define.gridlineData.txtCm": "cm", "Common.define.gridlineData.txtPt": "pt", "Common.define.smartArt.textAccentedPicture": "Bild mit Akzenten", "Common.define.smartArt.textAccentProcess": "Akzentprozess", "Common.define.smartArt.textAlternatingFlow": "Alternierender Fluss", "Common.define.smartArt.textAlternatingHexagons": "Alternierende Sechsecke", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternierende Bildblöcke", "Common.define.smartArt.textAlternatingPictureCircles": "Alternierende Bildblöcke", "Common.define.smartArt.textArchitectureLayout": "Architekturlayout", "Common.define.smartArt.textArrowRibbon": "Pfeilband", "Common.define.smartArt.textAscendingPictureAccentProcess": "Aufsteigender Prozess mit Bildakzenten", "Common.define.smartArt.textBalance": "Kontostand", "Common.define.smartArt.textBasicBendingProcess": "Einfacher umgebrochener Prozess", "Common.define.smartArt.textBasicBlockList": "Einfache Blockliste", "Common.define.smartArt.textBasicChevronProcess": "Einfacher Chevronprozess", "Common.define.smartArt.textBasicCycle": "Einfacher Kreis", "Common.define.smartArt.textBasicMatrix": "Einfache Matrix", "Common.define.smartArt.textBasicPie": "Einfaches Kreisdiagramm", "Common.define.smartArt.textBasicProcess": "Einfacher Prozess", "Common.define.smartArt.textBasicPyramid": "Einfache Pyramide", "Common.define.smartArt.textBasicRadial": "Einfaches Radial", "Common.define.smartArt.textBasicTarget": "Einfaches Ziel", "Common.define.smartArt.textBasicTimeline": "Einfache Zeitachse", "Common.define.smartArt.textBasicVenn": "Einfaches Venn", "Common.define.smartArt.textBendingPictureAccentList": "Umgebrochene Bildakzentliste", "Common.define.smartArt.textBendingPictureBlocks": "Umgebrochene Bildblöcke", "Common.define.smartArt.textBendingPictureCaption": "Umgebrochene Bildbeschriftung", "Common.define.smartArt.textBendingPictureCaptionList": "Umgebrochene Bildbeschriftungsliste", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Umgebrochener halbtransparenter Bildtext", "Common.define.smartArt.textBlockCycle": "Blockkreis", "Common.define.smartArt.textBubblePictureList": "Blasenbildliste", "Common.define.smartArt.textCaptionedPictures": "Bilder mit Beschriftungen", "Common.define.smartArt.textChevronAccentProcess": "Chevronakzentprozess", "Common.define.smartArt.textChevronList": "Chevronliste", "Common.define.smartArt.textCircleAccentTimeline": "Zeitachse mit Kreisakzent", "Common.define.smartArt.textCircleArrowProcess": "Kreisförmiger Pfeilprozess", "Common.define.smartArt.textCirclePictureHierarchy": "Bilderhierarchie mit Kreisakzent", "Common.define.smartArt.textCircleProcess": "Kreisprozess", "Common.define.smartArt.textCircleRelationship": "Kreisbeziehung", "Common.define.smartArt.textCircularBendingProcess": "Kreisförmiger umgebrochener Prozess", "Common.define.smartArt.textCircularPictureCallout": "Bildlegende mit Kreisakzent", "Common.define.smartArt.textClosedChevronProcess": "Geschlossener Chevronprozess", "Common.define.smartArt.textContinuousArrowProcess": "Fortlaufender Pfeilprozess", "Common.define.smartArt.textContinuousBlockProcess": "Fortlaufender Blockprozess", "Common.define.smartArt.textContinuousCycle": "Fortlaufender Kreis", "Common.define.smartArt.textContinuousPictureList": "Fortlaufende Bildliste", "Common.define.smartArt.textConvergingArrows": "Zusammenlaufende Pfeile", "Common.define.smartArt.textConvergingRadial": "Zusammenlaufendes Radial", "Common.define.smartArt.textConvergingText": "Zusammenlaufender Text", "Common.define.smartArt.textCounterbalanceArrows": "Gegengewichtspfeile", "Common.define.smartArt.textCycle": "Z<PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "Kreismatrix", "Common.define.smartArt.textDescendingBlockList": "Absteigende Blockliste", "Common.define.smartArt.textDescendingProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON> Prozess", "Common.define.smartArt.textDetailedProcess": "Detaillierter Prozess", "Common.define.smartArt.textDivergingArrows": "Auseinanderlaufende Pfeile", "Common.define.smartArt.textDivergingRadial": "Auseinanderlaufendes Radial", "Common.define.smartArt.textEquation": "Gleichung", "Common.define.smartArt.textFramedTextPicture": "Umrahmte Textgrafik", "Common.define.smartArt.textFunnel": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textGear": "Zahnrad", "Common.define.smartArt.textGridMatrix": "Rastermatrix", "Common.define.smartArt.textGroupedList": "Gruppierte Liste", "Common.define.smartArt.textHalfCircleOrganizationChart": "Halbkreisorganigramm", "Common.define.smartArt.textHexagonCluster": "Sechseck-Cluster", "Common.define.smartArt.textHexagonRadial": "Sechseck Radial", "Common.define.smartArt.textHierarchy": "Hierarchie", "Common.define.smartArt.textHierarchyList": "Hierarchieliste", "Common.define.smartArt.textHorizontalBulletList": "Horizontale Aufzählungsliste", "Common.define.smartArt.textHorizontalHierarchy": "Horizontale Hierarchie", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal beschriftete Hierarchie", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontale Hierarchie mit mehreren Ebenen", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontales Organigramm", "Common.define.smartArt.textHorizontalPictureList": "Horizontale Bildliste", "Common.define.smartArt.textIncreasingArrowProcess": "Wachsender Pfeil-Prozess", "Common.define.smartArt.textIncreasingCircleProcess": "Wachsender Kreis-Prozess", "Common.define.smartArt.textInterconnectedBlockProcess": "Vernetzter Blockprozess", "Common.define.smartArt.textInterconnectedRings": "Verbundene Ringe", "Common.define.smartArt.textInvertedPyramid": "Umgekehrte Pyramide", "Common.define.smartArt.textLabeledHierarchy": "Beschriftete Hierarchie", "Common.define.smartArt.textLinearVenn": "Lineares Venn", "Common.define.smartArt.textLinedList": "Liste mit Linien", "Common.define.smartArt.textList": "Liste", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Kreis mit mehreren Richtungen", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organigramm mit Name und Titel", "Common.define.smartArt.textNestedTarget": "Geschachteltes Ziel", "Common.define.smartArt.textNondirectionalCycle": "Richtungsloser Kreis", "Common.define.smartArt.textOpposingArrows": "Entgegengesetzte Pfeile", "Common.define.smartArt.textOpposingIdeas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textOrganizationChart": "Organigramm", "Common.define.smartArt.textOther": "Sonstiges", "Common.define.smartArt.textPhasedProcess": "Phasenprozess", "Common.define.smartArt.textPicture": "Bild", "Common.define.smartArt.textPictureAccentBlocks": "Bildakzentblöcke", "Common.define.smartArt.textPictureAccentList": "Bildakzentliste", "Common.define.smartArt.textPictureAccentProcess": "Bildakzentprozess", "Common.define.smartArt.textPictureCaptionList": "Bildbeschriftungsliste", "Common.define.smartArt.textPictureFrame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureGrid": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureLineup": "Bildanordnung", "Common.define.smartArt.textPictureOrganizationChart": "Bildorganigramm", "Common.define.smartArt.textPictureStrips": "Bildstreifen", "Common.define.smartArt.textPieProcess": "Kreisdiagrammprozess", "Common.define.smartArt.textPlusAndMinus": "Plus und Minus", "Common.define.smartArt.textProcess": "Prozess", "Common.define.smartArt.textProcessArrows": "Prozesspfeile", "Common.define.smartArt.textProcessList": "Prozessliste", "Common.define.smartArt.textPyramid": "Pyramide", "Common.define.smartArt.textPyramidList": "Pyramidenliste", "Common.define.smartArt.textRadialCluster": "Radialer Cluster", "Common.define.smartArt.textRadialCycle": "Radialkreis", "Common.define.smartArt.textRadialList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textRadialPictureList": "Radiale Bildliste", "Common.define.smartArt.textRadialVenn": "Radialvenn", "Common.define.smartArt.textRandomToResultProcess": "Zufallsergebnisprozess", "Common.define.smartArt.textRelationship": "Beziehung", "Common.define.smartArt.textRepeatingBendingProcess": "Wiederholter umgebrochener Prozess", "Common.define.smartArt.textReverseList": "Umgekehrte Liste", "Common.define.smartArt.textSegmentedCycle": "Segmentierter Kreis", "Common.define.smartArt.textSegmentedProcess": "Segment<PERSON>ter Prozess", "Common.define.smartArt.textSegmentedPyramid": "Segmentierte Pyramide", "Common.define.smartArt.textSnapshotPictureList": "Momentaufnahme-Bildliste", "Common.define.smartArt.textSpiralPicture": "Spiralförmige Grafik", "Common.define.smartArt.textSquareAccentList": "Liste mit quadratischen Akzenten", "Common.define.smartArt.textStackedList": "Gestapelte Liste", "Common.define.smartArt.textStackedVenn": "Gestapeltes Venn", "Common.define.smartArt.textStaggeredProcess": "Gestaffelter Prozess", "Common.define.smartArt.textStepDownProcess": "Prozess mit absteigenden Schritten", "Common.define.smartArt.textStepUpProcess": "Prozess mit aufsteigenden Schritten", "Common.define.smartArt.textSubStepProcess": "Unterschrittprozess", "Common.define.smartArt.textTabbedArc": "Registerkartenbogen", "Common.define.smartArt.textTableHierarchy": "Tabellenhierarchie", "Common.define.smartArt.textTableList": "Tabellenliste", "Common.define.smartArt.textTabList": "Registerkartenliste", "Common.define.smartArt.textTargetList": "Zielliste", "Common.define.smartArt.textTextCycle": "Textkreis", "Common.define.smartArt.textThemePictureAccent": "Designbildakzent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Alternierender Designbildakzent", "Common.define.smartArt.textThemePictureGrid": "Designbildraster", "Common.define.smartArt.textTitledMatrix": "Betitelte Matrix", "Common.define.smartArt.textTitledPictureAccentList": "Bildakzentliste mit Titel", "Common.define.smartArt.textTitledPictureBlocks": "Titelbildblöcke", "Common.define.smartArt.textTitlePictureLineup": "Titelbildanordnung", "Common.define.smartArt.textTrapezoidList": "Trapezförmige Liste", "Common.define.smartArt.textUpwardArrow": "<PERSON><PERSON><PERSON> nach oben", "Common.define.smartArt.textVaryingWidthList": "Liste mit variabler Breite", "Common.define.smartArt.textVerticalAccentList": "Liste mit vertikalen Akzenten", "Common.define.smartArt.textVerticalArrowList": "Vertical Arrow List", "Common.define.smartArt.textVerticalBendingProcess": "Vertikaler umgebrochener Prozess", "Common.define.smartArt.textVerticalBlockList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalBoxList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalBracketList": "Liste mit vertikalen Klammerakzenten", "Common.define.smartArt.textVerticalBulletList": "Vert<PERSON><PERSON> Aufzählung", "Common.define.smartArt.textVerticalChevronList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalCircleList": "Liste mit vertikalen Kreisakzenten", "Common.define.smartArt.textVerticalCurvedList": "Liste mit vertikalen Kurven", "Common.define.smartArt.textVerticalEquation": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalPictureAccentList": "Vertikale Bildakzentliste", "Common.define.smartArt.textVerticalPictureList": "<PERSON><PERSON><PERSON><PERSON>ildl<PERSON>", "Common.define.smartArt.textVerticalProcess": "<PERSON><PERSON><PERSON><PERSON>", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.tipFileLocked": "Das Dokument ist für die Bearbeitung gesperrt. Sie können Änderungen vornehmen und es später als lokale Kopie speichern.", "Common.Translation.tipFileReadOnly": "Das Dokument ist schreibgeschützt und für die Bearbeitung gesperrt. Sie können Änderungen vornehmen und die lokale Kopie später speichern.", "Common.Translation.warnFileLocked": "Die Datei wird in einer anderen App bearbeitet. Si<PERSON> können die Bearbeitung fortsetzen und die Kopie dieser Datei speichern.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON>", "Common.Translation.warnFileLockedBtnView": "Schreibgeschützt öffnen", "Common.UI.ButtonColored.textAutoColor": "Automatisch", "Common.UI.ButtonColored.textEyedropper": "Pipette", "Common.UI.ButtonColored.textNewColor": "Mehr Farben", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON>", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON>", "Common.UI.ComboDataView.emptyComboText": "Keine Formate", "Common.UI.ExtendedColorDialog.addButtonText": "Hinzufügen", "Common.UI.ExtendedColorDialog.textCurrent": "Aktuell", "Common.UI.ExtendedColorDialog.textHexErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen Wert zwischen 000000 und FFFFFF ein.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON>eu", "Common.UI.ExtendedColorDialog.textRGBErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen numerischen Wert zwischen 0 und 255 ein.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Passwort ausblenden", "Common.UI.InputFieldBtnPassword.textHintHold": "<PERSON> drück<PERSON>, um das Passwort anzuzeigen", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Password anzeigen", "Common.UI.SearchBar.textFind": "<PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON> sch<PERSON>ßen", "Common.UI.SearchBar.tipNextResult": "Nächstes Ergebnis", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Erweiterte Einstellungen öffnen", "Common.UI.SearchBar.tipPreviousResult": "Vorheriges Ergebnis", "Common.UI.SearchDialog.textHighlight": "Ergebnisse markieren", "Common.UI.SearchDialog.textMatchCase": "Groß-/Kleinschreibung beachten", "Common.UI.SearchDialog.textReplaceDef": "Geben Sie den Ersetzungstext ein", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON> den Text hier ein", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON> und ersetzen", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Nur ganze Wörter", "Common.UI.SearchDialog.txtBtnHideReplace": "Ersetzen verbergen", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON>e ersetzen", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON> nicht mehr anzeigen", "Common.UI.SynchronizeTip.textGotIt": "OK", "Common.UI.SynchronizeTip.textSynchronize": "Das Dokument wurde von einem anderen Benutzer geändert.<br>Bitte speichern Sie Ihre Änderungen und aktualisieren Sie Ihre Seite.", "Common.UI.ThemeColorPalette.textRecentColors": "<PERSON><PERSON><PERSON>lich verwendete Farben", "Common.UI.ThemeColorPalette.textStandartColors": "Standardfarben", "Common.UI.ThemeColorPalette.textThemeColors": "Themenfarben", "Common.UI.Themes.txtThemeClassicLight": "Klassisch Hell", "Common.UI.Themes.txtThemeContrastDark": "<PERSON><PERSON><PERSON> Ko<PERSON>ras<PERSON>", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeGray": "G<PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "Hell", "Common.UI.Themes.txtThemeSystem": "Wie im System", "Common.UI.Window.cancelButtonText": "Abbrechen", "Common.UI.Window.closeButtonText": "Schließen", "Common.UI.Window.noButtonText": "<PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Bestätigung", "Common.UI.Window.textDontShow": "<PERSON><PERSON> nicht mehr anzeigen", "Common.UI.Window.textError": "<PERSON><PERSON>", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Achtung", "Common.UI.Window.yesButtonText": "<PERSON>a", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Strg", "Common.Utils.String.textShift": "Umschalt", "Common.Utils.ThemeColor.txtaccent": "Akzent", "Common.Utils.ThemeColor.txtAqua": "Dunkeltürkis", "Common.Utils.ThemeColor.txtbackground": "Hi<PERSON>grund", "Common.Utils.ThemeColor.txtBlack": "schwarz", "Common.Utils.ThemeColor.txtBlue": "blau", "Common.Utils.ThemeColor.txtBrightGreen": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dunkelblau", "Common.Utils.ThemeColor.txtDarker": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkGray": "Dunkelgrau", "Common.Utils.ThemeColor.txtDarkGreen": "Dunkelgrün", "Common.Utils.ThemeColor.txtDarkPurple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkRed": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkTeal": "Dunkelblaugrün", "Common.Utils.ThemeColor.txtDarkYellow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "grau", "Common.Utils.ThemeColor.txtGreen": "gr<PERSON>n", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavendel", "Common.Utils.ThemeColor.txtLightBlue": "Hellblau", "Common.Utils.ThemeColor.txtLighter": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightGray": "Hellgrau", "Common.Utils.ThemeColor.txtLightGreen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightOrange": "Hellorange", "Common.Utils.ThemeColor.txtLightYellow": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "<PERSON>", "Common.Utils.ThemeColor.txtPurple": "<PERSON>", "Common.Utils.ThemeColor.txtRed": "rot", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Himmelbla<PERSON>", "Common.Utils.ThemeColor.txtTeal": "Türkisblau", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "weiß", "Common.Utils.ThemeColor.txtYellow": "gelb", "Common.Views.About.txtAddress": "<PERSON><PERSON><PERSON>: ", "Common.Views.About.txtLicensee": "LIZENZNEHMER", "Common.Views.About.txtLicensor": "LIZENZGEBER", "Common.Views.About.txtMail": "E-Mail-Adresse:", "Common.Views.About.txtPoweredBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "Common.Views.About.txtTel": "Tel.: ", "Common.Views.About.txtVersion": "Version ", "Common.Views.AutoCorrectDialog.textAdd": "Hinzufügen", "Common.Views.AutoCorrectDialog.textApplyText": "Bei der Eingabe anwenden", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autokorrektur für Text", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat während der Eingabe", "Common.Views.AutoCorrectDialog.textBulleted": "Automatische Aufzählungen", "Common.Views.AutoCorrectDialog.textBy": "Nach", "Common.Views.AutoCorrectDialog.textDelete": "Löschen", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Punkt mit doppeltem Leerzeichen hinzufügen", "Common.Views.AutoCorrectDialog.textFLCells": "Jede Tabellenzelle mit einem Großbuchstaben beginnen", "Common.Views.AutoCorrectDialog.textFLDont": "Großbuchstaben nicht verwenden nach", "Common.Views.AutoCorrectDialog.textFLSentence": "Jeden Satz mit einem Großbuchstaben beginnen", "Common.Views.AutoCorrectDialog.textForLangFL": "Ausnahmen für die Sprache:", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet- und Netzwerkpfade durch Hyperlinks", "Common.Views.AutoCorrectDialog.textHyphens": "<PERSON><PERSON><PERSON><PERSON> (--) mit Gedankens<PERSON>h (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Mathematische Autokorrektur", "Common.Views.AutoCorrectDialog.textNumbered": "Automatische nummerierte Listen", "Common.Views.AutoCorrectDialog.textQuotes": "\"Gerade Anführungszeichen\" mit \"geschweifte Anführungszeichen\"", "Common.Views.AutoCorrectDialog.textRecognized": "Erkannte Funktionen", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Die folgenden Ausdrücke sind erkannte mathematische Funktionen. Diese werden nicht automatisch kursiviert.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "Bei der Eingabe ersetzen", "Common.Views.AutoCorrectDialog.textReplaceType": "Text bei der Eingabe ersetzen", "Common.Views.AutoCorrectDialog.textReset": "Z<PERSON>ücksetzen", "Common.Views.AutoCorrectDialog.textResetAll": "Zurücksetzen auf die Standardeinstellungen", "Common.Views.AutoCorrectDialog.textRestore": "Wiederherstellen", "Common.Views.AutoCorrectDialog.textTitle": "Autokorrektur", "Common.Views.AutoCorrectDialog.textWarnAddFL": "Ausnahmen dürfen nur Groß- oder Kleinbuchstaben enthalten.", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Erkannte Funktionen sollen nur groß- oder kleingeschriebene Buchstaben von A bis Z beinhalten.", "Common.Views.AutoCorrectDialog.textWarnResetFL": "Alle von Ihnen hinzugefügten Ausnahmen werden entfernt und die entfernten werden wiederhergestellt. Möchten Sie fortfahren?", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Alle hinzugefügten Ausdrücke werden entfernt und die gelöschten Ausdrücke werden zurückgestellt. Möchten Sie wirklich fortsetzen?", "Common.Views.AutoCorrectDialog.warnReplace": "Es gibt schon einen Autokorrektur-Eintrag für %1. Möchten Sie dieses ersetzen?", "Common.Views.AutoCorrectDialog.warnReset": "Hinzugefügte Autokorrektur wird entfernt und geänderte Autokorrektur wird zurückgestellt. Möchten Sie trotzdem fortsetzen?", "Common.Views.AutoCorrectDialog.warnRestore": "Der Autokorrektur-Eintrag für %1 wird zurückgestellt. Möchten Sie fortsetzen?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "<PERSON><PERSON> sch<PERSON>ßen", "Common.Views.Chat.textEnterMessage": "<PERSON><PERSON><PERSON> Si<PERSON> Ihre Nachricht hier ein", "Common.Views.Chat.textSend": "Senden", "Common.Views.Comments.mniAuthorAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z)", "Common.Views.Comments.mniAuthorDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A)", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniFilterGroups": "Nach Gruppe filtern", "Common.Views.Comments.mniPositionAsc": "<PERSON>ben", "Common.Views.Comments.mniPositionDesc": "<PERSON> unten", "Common.Views.Comments.textAdd": "Hinzufügen", "Common.Views.Comments.textAddComment": "Kommentar hinzufügen", "Common.Views.Comments.textAddCommentToDoc": "Kommentar zum Dokument hinzufügen", "Common.Views.Comments.textAddReply": "Antwort hinzufügen", "Common.Views.Comments.textAll": "Alle", "Common.Views.Comments.textAnonym": "Gas<PERSON>", "Common.Views.Comments.textCancel": "Abbrechen", "Common.Views.Comments.textClose": "Schließen", "Common.Views.Comments.textClosePanel": "Kommentare schließen", "Common.Views.Comments.textComment": "Kommentar", "Common.Views.Comments.textComments": "Kommentare", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Kommentar hier ein", "Common.Views.Comments.textHintAddComment": "Kommentar hinzufügen", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textReply": "Antworten", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Kommentare sortieren", "Common.Views.Comments.textSortFilter": "Kommentare sortieren und filtern", "Common.Views.Comments.textSortFilterMore": "Sortier<PERSON>, filtern und mehr", "Common.Views.Comments.textSortMore": "Sort<PERSON><PERSON> und mehr", "Common.Views.Comments.textViewResolved": "<PERSON>e haben keine Berechtigung, den Kommentar erneut zu öffnen", "Common.Views.Comments.txtEmpty": "Das Dokument enthält keine Kommentare.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON> nicht mehr anzeigen", "Common.Views.CopyWarningDialog.textMsg": "Die Funktionen \"<PERSON>pieren\", \"Ausschneiden\" und \"Einfügen\" können mithilfe den Schaltflächen in der Symbolleiste und Aktionen im Kontextmenü nur in dieser Editor-Registerkarte durchgeführt werden.<br><br> <PERSON><PERSON><PERSON> oder Einfügen in oder aus anderen Anwendungen nutzen Sie die folgenden Tastenkombinationen:", "Common.Views.CopyWarningDialog.textTitle": "Funktionen \"Kopieren\", \"Ausschneiden\" und \"Einfügen\"", "Common.Views.CopyWarningDialog.textToCopy": "zum Kopieren", "Common.Views.CopyWarningDialog.textToCut": "zum Ausschneiden", "Common.Views.CopyWarningDialog.textToPaste": "zum Einfügen", "Common.Views.CustomizeQuickAccessDialog.textDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Markieren Sie die Befehle, die in der Symbolleiste für den Schnellzugriff angezeigt werden sollen", "Common.Views.CustomizeQuickAccessDialog.textPrint": "<PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Schnelldruck", "Common.Views.CustomizeQuickAccessDialog.textRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textSave": "Speichern", "Common.Views.CustomizeQuickAccessDialog.textStartOver": "<PERSON><PERSON> Anfang anzeigen", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Schnellzugriff anpassen", "Common.Views.CustomizeQuickAccessDialog.textUndo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "Common.Views.DocumentAccessDialog.textLoading": "Ladevorgang...", "Common.Views.DocumentAccessDialog.textTitle": "Freigabeeinstellungen", "Common.Views.DocumentPropertyDialog.errorDate": "Sie können einen Wert aus dem Kalender auswählen, um den Wert als Datum zu speichern.<br>Wenn Sie einen Wert manuell eingeben, wird er als Text gespeichert.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "<PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "<PERSON>a", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Eigenschaft sollte einen Titel haben", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Titel", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"<PERSON><PERSON>\" or \"<PERSON><PERSON>\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Datum", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "<PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "<PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "<PERSON><PERSON><PERSON> Si<PERSON> eine gültige Nummer ein", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Eigenschaft sollte einen Wert haben", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Wert", "Common.Views.DocumentPropertyDialog.txtTitle": "Neue Dokumenteigenschaft", "Common.Views.Draw.hintEraser": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.hintSelect": "Auswahl", "Common.Views.Draw.txtEraser": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtHighlighter": "Textmarker", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Stift", "Common.Views.Draw.txtSelect": "Auswahl", "Common.Views.Draw.txtSize": "Größe", "Common.Views.ExternalDiagramEditor.textTitle": "Diagramm bearbeiten", "Common.Views.ExternalEditor.textClose": "Schließen", "Common.Views.ExternalEditor.textSave": "Speichern und beenden", "Common.Views.ExternalOleEditor.textTitle": "Editor der Tabellenkalkulationen", "Common.Views.Header.ariaQuickAccessToolbar": "Symbolleiste für Schnellzugriff", "Common.Views.Header.labelCoUsersDescr": "<PERSON><PERSON><PERSON>, die die Datei bearbeiten:", "Common.Views.Header.textAddFavorite": "Als Favorit kennzei<PERSON>nen", "Common.Views.Header.textAdvSettings": "Erweiterte Einstellungen", "Common.Views.Header.textBack": "<PERSON>is<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textClose": "<PERSON><PERSON> sch<PERSON>n", "Common.Views.Header.textCompactView": "Symbolleiste ausblenden", "Common.Views.Header.textHideLines": "Lineale verbergen", "Common.Views.Header.textHideNotes": "Notizen ausblenden", "Common.Views.Header.textHideStatusBar": "Statusleiste verbergen", "Common.Views.Header.textPrint": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textReadOnly": "<PERSON><PERSON> lesen", "Common.Views.Header.textRemoveFavorite": "Aus Favoriten entfernen", "Common.Views.Header.textSaveBegin": "Speicherung...", "Common.Views.Header.textSaveChanged": "Verändert", "Common.Views.Header.textSaveEnd": "Alle Änderungen wurden gespeichert", "Common.Views.Header.textSaveExpander": "Alle Änderungen wurden gespeichert", "Common.Views.Header.textShare": "Freigeben", "Common.Views.Header.textStartOver": "<PERSON><PERSON> Anfang anzeigen", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Zugriffsrechte für das Dokument verwalten", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Symbolleiste für den Schnellzugriff anpassen", "Common.Views.Header.tipDownload": "<PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "Aktuelle Datei bearbeiten", "Common.Views.Header.tipPrint": "<PERSON><PERSON> drucken", "Common.Views.Header.tipPrintQuick": "Schnelldruck", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Speichern", "Common.Views.Header.tipSearch": "<PERSON><PERSON>", "Common.Views.Header.tipStartOver": "<PERSON><PERSON><PERSON> von <PERSON> an starten", "Common.Views.Header.tipUndo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "Common.Views.Header.tipUndock": "In einem separaten Fenster abdocken", "Common.Views.Header.tipUsers": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipViewSettings": "Ansichts-Einstellungen", "Common.Views.Header.tipViewUsers": "Benutzer ansehen und Zugriffsrechte für das Dokument verwalten", "Common.Views.Header.txtAccessRights": "Zugriffsrechte ändern", "Common.Views.Header.txtRename": "Umbenennen", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Wesentliche Änderungen verbergen", "Common.Views.History.textHighlightDeleted": "Gelöschte hervorheben", "Common.Views.History.textMore": "<PERSON><PERSON>", "Common.Views.History.textRestore": "Wiederherstellen", "Common.Views.History.textShowAll": "Wesentliche Änderungen anzeigen", "Common.Views.History.textVer": "Vers.", "Common.Views.History.textVersionHistory": "Versionsverlauf", "Common.Views.ImageFromUrlDialog.textUrl": "Bild-URL einfügen:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON><PERSON> muss eine URL im Format \"http://www.example.com\" enthalten", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Sie müssen eine gültige Anzahl der Zeilen und Spalten angeben.", "Common.Views.InsertTableDialog.txtColumns": "<PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtMaxText": "Der maximale Wert für dieses Feld ist {0}.", "Common.Views.InsertTableDialog.txtMinText": "Der minimale Wert für dieses Feld ist {0}.", "Common.Views.InsertTableDialog.txtRows": "<PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitle": "Größe der Tabelle", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON> te<PERSON>n", "Common.Views.LanguageDialog.labelSelect": "Sprache des Dokuments wählen", "Common.Views.ListSettingsDialog.textBulleted": "Aufzählung", "Common.Views.ListSettingsDialog.textFromFile": "Aus einer Datei", "Common.Views.ListSettingsDialog.textFromStorage": "Aus dem Speicher", "Common.Views.ListSettingsDialog.textFromUrl": "Aus einer URL", "Common.Views.ListSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "Common.Views.ListSettingsDialog.tipChange": "Aufzählungszeichen ändern", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "Farbe", "Common.Views.ListSettingsDialog.txtImage": "Bild", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "Neues Aufzählungszeichen", "Common.Views.ListSettingsDialog.txtNewImage": "Neues Bild", "Common.Views.ListSettingsDialog.txtNone": "Kein(e)", "Common.Views.ListSettingsDialog.txtOfText": "% des Textes", "Common.Views.ListSettingsDialog.txtSize": "Größe", "Common.Views.ListSettingsDialog.txtStart": "Beginnen mit", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "Listeneinstellungen", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textCopy": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textCustomFunction": "Benutzerdefinierte Funktion", "Common.Views.MacrosDialog.textDelete": "Löschen", "Common.Views.MacrosDialog.textLoading": "Ladevorgang...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Autostart ausführen", "Common.Views.MacrosDialog.textRename": "Umbenennen", "Common.Views.MacrosDialog.textRun": "Ausführen", "Common.Views.MacrosDialog.textSave": "Speichern", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Autostart aufheben", "Common.Views.MacrosDialog.tipFunctionAdd": "Benutzerdefinierte Funktion hinzufügen", "Common.Views.MacrosDialog.tipMacrosAdd": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.tipMacrosRun": "Ausführen", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON> sch<PERSON>n", "Common.Views.OpenDialog.txtEncoding": "Zeichenkodierung", "Common.Views.OpenDialog.txtIncorrectPwd": "Kennwort ist falsch.", "Common.Views.OpenDialog.txtOpenFile": "Kennwort zum Öffnen der Datei eingeben", "Common.Views.OpenDialog.txtPassword": "Kennwort", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON>d Sie das Passwort eingegeben und die Datei geöffnet haben, wird das aktuelle Passwort für die Datei zurückgesetzt.", "Common.Views.OpenDialog.txtTitle": "%1-Optionen wählen", "Common.Views.OpenDialog.txtTitleProtected": "Geschützte Datei", "Common.Views.PasswordDialog.txtDescription": "Legen Sie ein Passwort fest, um dieses Dokument zu schützen", "Common.Views.PasswordDialog.txtIncorrectPwd": "Bestätigungseingabe ist nicht identisch", "Common.Views.PasswordDialog.txtPassword": "Kennwort", "Common.Views.PasswordDialog.txtRepeat": "Kenn<PERSON><PERSON> wiederholen", "Common.Views.PasswordDialog.txtTitle": "Kennwort festlegen", "Common.Views.PasswordDialog.txtWarning": "Vorsicht: <PERSON>n Si<PERSON> das Kennwort verlieren oder vergessen, lässt es sich nicht mehr wiederherstellen. Bewahren Sie es an einem sicheren Ort auf.", "Common.Views.PluginDlg.textDock": "<PERSON><PERSON><PERSON> anheften", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.PluginPanel.textClosePanel": "<PERSON><PERSON><PERSON> s<PERSON>n", "Common.Views.PluginPanel.textHidePanel": "Plugin reduzieren", "Common.Views.PluginPanel.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.PluginPanel.textUndock": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textBackgroundPlugins": "Plugins im Hintergrund", "Common.Views.Plugins.textSettings": "Einstellungen", "Common.Views.Plugins.textStart": "Starten", "Common.Views.Plugins.textStop": "<PERSON>den", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "Die Liste der Plugins im Hintergrund", "Common.Views.Plugins.tipMore": "<PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Mit Kennwort verschlüsseln", "Common.Views.Protection.hintDelPwd": "Kennwort löschen", "Common.Views.Protection.hintPwd": "Das Kennwort ändern oder löschen", "Common.Views.Protection.hintSignature": "Fügen Sie eine digitale Signatur oder Unterschriftenzeile hinzu", "Common.Views.Protection.txtAddPwd": "Kennwort hinzufügen", "Common.Views.Protection.txtChangePwd": "Kennwort ändern", "Common.Views.Protection.txtDeletePwd": "Kennwort löschen", "Common.Views.Protection.txtEncrypt": "Verschlüsseln", "Common.Views.Protection.txtInvisibleSignature": "Fügen Sie eine digitale Signatur hinzu", "Common.Views.Protection.txtSignature": "Signatur", "Common.Views.Protection.txtSignatureLine": "Signaturzeile hinzufügen", "Common.Views.RecentFiles.txtOpenRecent": "Zuletzt verwendete öffnen", "Common.Views.RenameDialog.textName": "Dateiname", "Common.Views.RenameDialog.txtInvalidName": "Dieser Dateiname darf keines der folgenden Zeichen enthalten:", "Common.Views.ReviewChanges.hintNext": "Zur nächsten Änderung", "Common.Views.ReviewChanges.hintPrev": "Zur vorherigen Änderung", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Echtzeit-Zusammenbearbeitung. Alle Änderungen werden automatisch gespeichert.", "Common.Views.ReviewChanges.strStrict": "Formal", "Common.Views.ReviewChanges.strStrictDesc": "Verwenden Sie die Schaltfläche \"Speichern\", um die von Ihnen und anderen vorgenommenen Änderungen zu synchronisieren.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aktuelle Änderungen annehmen", "Common.Views.ReviewChanges.tipCoAuthMode": "Zusammen-Bearbeitungsmodus einstellen", "Common.Views.ReviewChanges.tipCommentRem": "Kommentare entfernen", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Aktuelle Kommentare entfernen", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Gültige Kommentare lösen", "Common.Views.ReviewChanges.tipHistory": "Versionshistorie anzeigen", "Common.Views.ReviewChanges.tipRejectCurrent": "Aktuelle Änderungen ablehnen", "Common.Views.ReviewChanges.tipReview": "Nachverfo<PERSON><PERSON> von <PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Wählen Sie den Modus aus, in dem die Änderungen angezeigt werden sollen", "Common.Views.ReviewChanges.tipSetDocLang": "Sprache des Dokumentes festlegen", "Common.Views.ReviewChanges.tipSetSpelling": "Rechtschreibprüfung", "Common.Views.ReviewChanges.tipSharing": "Zugriffsrechte für das Dokument verwalten", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "Alle Änderungen annehmen", "Common.Views.ReviewChanges.txtAcceptChanges": "Änderungen annehmen", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aktuelle Änderungen annehmen", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "Schließen", "Common.Views.ReviewChanges.txtCoAuthMode": "Modus \"Gemeinsame Bearbeitung\"", "Common.Views.ReviewChanges.txtCommentRemAll": "Alle Kommentare entfernen", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Aktuelle Kommentare entfernen", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON>ne Kommentare entfernen", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Meine aktuellen Kommentare entfernen", "Common.Views.ReviewChanges.txtCommentRemove": "Entfernen", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "Alle Kommentare lösen", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Aktuelle Kommentare lösen", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Meine gültige Kommentare lösen", "Common.Views.ReviewChanges.txtDocLang": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtFinal": "Alle Änderungen werden übernommen (Vorschau)", "Common.Views.ReviewChanges.txtFinalCap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtHistory": "Versionshistorie", "Common.Views.ReviewChanges.txtMarkup": "Alle Änderungen (Bearbeitung)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Alle Änderungen werden abgelehnt (Vorschau)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Zurück", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Alle Änderungen ablehnen", "Common.Views.ReviewChanges.txtRejectChanges": "Änderungen ablehnen", "Common.Views.ReviewChanges.txtRejectCurrent": "Aktuelle Änderungen ablehnen", "Common.Views.ReviewChanges.txtSharing": "Freigabe", "Common.Views.ReviewChanges.txtSpelling": "Rechtschreibprüfung", "Common.Views.ReviewChanges.txtTurnon": "Nachverfo<PERSON><PERSON> von <PERSON>", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON>ige<PERSON><PERSON>", "Common.Views.ReviewPopover.textAdd": "Hinzufügen", "Common.Views.ReviewPopover.textAddReply": "Antwort hinzufügen", "Common.Views.ReviewPopover.textCancel": "Abbrechen", "Common.Views.ReviewPopover.textClose": "Schließen", "Common.Views.ReviewPopover.textComment": "Kommentar", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Kommentar hier ein", "Common.Views.ReviewPopover.textMention": "+Erwähnung ermöglicht den Zugriff auf das Dokument und das Senden einer E-Mail", "Common.Views.ReviewPopover.textMentionNotify": "+Erwähnung benachrichtigt den Benutzer per E-Mail", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textReply": "Antworten", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "<PERSON>e haben keine Berechtigung, den Kommentar erneut zu öffnen", "Common.Views.ReviewPopover.txtDeleteTip": "Löschen", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "Ordner fürs Speichern", "Common.Views.SearchPanel.textCaseSensitive": "Groß-/Kleinschreibung beachten", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON> sch<PERSON>ßen", "Common.Views.SearchPanel.textContentChanged": "Dokument verändert.", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON><PERSON> und ersetzen", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} Elemente erfolgreich ersetzt.", "Common.Views.SearchPanel.textMatchUsingRegExp": "Über reguläre Ausdrücke abgleichen", "Common.Views.SearchPanel.textNoMatches": "<PERSON><PERSON>", "Common.Views.SearchPanel.textNoSearchResults": "<PERSON><PERSON>", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} Elemente ersetzt. Die übrigen {2} Elemente sind von anderen Benutzern gesperrt.", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "<PERSON>e ersetzen", "Common.Views.SearchPanel.textReplaceWith": "Ersetzen durch", "Common.Views.SearchPanel.textSearchAgain": "{0}Neue Suche durchführen{1} für genaue Ergebnisse.", "Common.Views.SearchPanel.textSearchHasStopped": "Suche abgebrochen", "Common.Views.SearchPanel.textSearchResults": "Suchergebnisse: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Suchergebnisse", "Common.Views.SearchPanel.textTooManyResults": "<PERSON>s gibt zu viele Ergebnisse, um sie hier zu zeigen", "Common.Views.SearchPanel.textWholeWords": "Nur ganze Wörter", "Common.Views.SearchPanel.tipNextResult": "Nächstes Ergebnis", "Common.Views.SearchPanel.tipPreviousResult": "Vorheriges Ergebnis", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>w<PERSON>", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Abstand", "Common.Views.ShapeShadowDialog.txtSize": "Größe", "Common.Views.ShapeShadowDialog.txtTitle": "<PERSON><PERSON><PERSON> anpassen", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparenz", "Common.Views.SignDialog.textBold": "<PERSON><PERSON>", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Ändern", "Common.Views.SignDialog.textInputName": "Name des Signaturgebers eingeben", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Der Name des Signaturgebers darf nicht leer sein.", "Common.Views.SignDialog.textPurpose": "Zweck der Signierung dieses Dokuments", "Common.Views.SignDialog.textSelect": "Auswahl", "Common.Views.SignDialog.textSelectImage": "Bild auswählen", "Common.Views.SignDialog.textSignature": "Wie sieht Signatur aus:", "Common.Views.SignDialog.textTitle": "Dokument signieren", "Common.Views.SignDialog.textUseImage": "oder klicken Sie auf \"Bild auswählen\", um ein Bild als Unterschrift zu verwenden", "Common.Views.SignDialog.textValid": "Gültig von% 1 bis% 2", "Common.Views.SignDialog.tipFontName": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.tipFontSize": "Schriftgrad", "Common.Views.SignSettingsDialog.textAllowComment": "Signaturgeber verfügt über die Möglichkeit, einen Kommentar im Signaturdialog hinzuzufügen", "Common.Views.SignSettingsDialog.textDefInstruction": "Überprüfen Si<PERSON>, ob der signierte Inhalt stimmt, bevor <PERSON> dieses Dokument signieren.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-Mail", "Common.Views.SignSettingsDialog.textInfoName": "Name", "Common.Views.SignSettingsDialog.textInfoTitle": "Titel des Signatureingebers", "Common.Views.SignSettingsDialog.textInstructions": "Anweisungen für Signaturgeber", "Common.Views.SignSettingsDialog.textShowDate": "Signaturdatum in der Signaturzeile anzeigen", "Common.Views.SignSettingsDialog.textTitle": "Signatureinstellungen", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX Wert", "Common.Views.SymbolTableDialog.textCopyright": "Copyrightzeichen", "Common.Views.SymbolTableDialog.textDCQuote": "Doppelte schließende Anführung", "Common.Views.SymbolTableDialog.textDOQuote": "Doppelte offende Anführungszeichen", "Common.Views.SymbolTableDialog.textEllipsis": "Waagerechte Auslassungspunkte", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "Em-Abstand", "Common.Views.SymbolTableDialog.textEnDash": "Halbgeviertstrich", "Common.Views.SymbolTableDialog.textEnSpace": "En-Abstand", "Common.Views.SymbolTableDialog.textFont": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "Geschützter Bindestrich", "Common.Views.SymbolTableDialog.textNBSpace": "Geschütztes Leerzeichen", "Common.Views.SymbolTableDialog.textPilcrow": "Absatzzeichen", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4-Em-Abstand", "Common.Views.SymbolTableDialog.textRange": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textRecent": "K<PERSON><PERSON>lich verwendete Symbole", "Common.Views.SymbolTableDialog.textRegistered": "Registered Trade Mark", "Common.Views.SymbolTableDialog.textSCQuote": "Einfache schließende Anführung", "Common.Views.SymbolTableDialog.textSection": "Paragraphenzeichen", "Common.Views.SymbolTableDialog.textShortcut": "Tastenkombination", "Common.Views.SymbolTableDialog.textSHyphen": "Weicher Bindestrich", "Common.Views.SymbolTableDialog.textSOQuote": "Einfache offende Anführungszeichen", "Common.Views.SymbolTableDialog.textSpecial": "Sonderzeichen", "Common.Views.SymbolTableDialog.textSymbols": "Symbole", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Markensymbol", "Common.Views.UserNameDialog.textDontShow": "Nicht mehr anzeigen", "Common.Views.UserNameDialog.textLabel": "Bezeichnung:", "Common.Views.UserNameDialog.textLabelError": "<PERSON><PERSON><PERSON>nung darf nicht leer sein.", "PE.Controllers.DocumentHolder.textLongName": "<PERSON><PERSON><PERSON> Si<PERSON> einen Namen ein, der aus weniger als 255 Zeichen besteht.", "PE.Controllers.DocumentHolder.textNameLayout": "Layout-Name", "PE.Controllers.DocumentHolder.textNameMaster": "Mastername", "PE.Controllers.DocumentHolder.textRenameTitleLayout": "Layout umbenennen", "PE.Controllers.DocumentHolder.textRenameTitleMaster": "Master um<PERSON><PERSON>n", "PE.Controllers.LeftMenu.leavePageText": "Alle ungespeicherten Änderungen in diesem Dokument werden verloren.<br> <PERSON><PERSON><PERSON> <PERSON><PERSON> auf \"Abbrechen\" und anschließend auf \"Speichern\", um die Änderungen zu speichern. Klicken Sie auf den Button \"OK\", so werden alle ungespeicherten Änderungen verloren gehen. ", "PE.Controllers.LeftMenu.newDocumentTitle": "Unbetitelte Präsentation", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Achtung", "PE.Controllers.LeftMenu.requestEditRightsText": "Anfrage betreffend die Bearbeitungsberechtigung...", "PE.Controllers.LeftMenu.textLoadHistory": "Versionshistorie wird geladen...", "PE.Controllers.LeftMenu.textNoTextFound": "<PERSON> Daten, nach denen <PERSON> gesucht haben, können nicht gefunden werden. Bitte ändern Sie die Suchparameter.", "PE.Controllers.LeftMenu.textReplaceSkipped": "Der Ersatzvorgang wurde durchgeführt. {0} Vorkommen wurden ausgelassen.", "PE.Controllers.LeftMenu.textReplaceSuccess": "Der Suchvorgang wurde durchgeführt. Vorkommen wurden ersetzt:{0}", "PE.Controllers.LeftMenu.textSelectPath": "<PERSON><PERSON><PERSON> Si<PERSON> einen neuen Namen zum Speichern der Dateikopie ein", "PE.Controllers.LeftMenu.txtUntitled": "Unbenannt", "PE.Controllers.Main.applyChangesTextText": "Daten werden geladen...", "PE.Controllers.Main.applyChangesTitleText": "Daten werden geladen", "PE.Controllers.Main.confirmMaxChangesSize": "Die Anzahl der Aktionen überschreitet die für Ihren Server festgelegte Grenze.<br><PERSON><PERSON><PERSON> \"Rückgängig\", um Ihre letzte Aktion abzubrechen, oder drücken Sie \"Weiter\", um die Aktion lokal fortzusetzen (Sie müssen die Datei herunterladen oder ihren Inhalt kopieren, um sicherzustellen, dass nichts verloren geht).", "PE.Controllers.Main.convertationTimeoutText": "Zeitüberschreitung bei der Konvertierung.", "PE.Controllers.Main.criticalErrorExtText": "<PERSON>lick<PERSON> Sie auf \"OK\", um zur Dokumentenliste zu übergehen.", "PE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON>", "PE.Controllers.Main.downloadErrorText": "Herunterladen ist fehlgeschlagen.", "PE.Controllers.Main.downloadTextText": "Präsentation wird heruntergeladen...", "PE.Controllers.Main.downloadTitleText": "Präsentation wird heruntergeladen", "PE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON>, eine Aktion durchzuführen, für die Si<PERSON> keine Rechte haben.<br><PERSON>te wenden Sie sich an Ihren Document Serveradministrator.", "PE.Controllers.Main.errorBadImageUrl": "URL des Bildes ist falsch", "PE.Controllers.Main.errorCannotPasteImg": "Wir können dieses Bild nicht über die Zwischenablage einfügen.\nSie können es aber auf Ihrem Gerät speichern und von dort aus einfügen, oder Sie können das Bild ohne Text kopieren und in die Präsentation einfügen.", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Verbindung zum Server verloren. Das Dokument kann momentan nicht bearbeitet werden.", "PE.Controllers.Main.errorComboSeries": "<PERSON><PERSON>hlen Sie mindestens zwei Datenreihen aus, um ein Verbunddiagramm zu erstellen.", "PE.Controllers.Main.errorConnectToServer": "Das Dokument konnte nicht gespeichert werden. Bitte überprüfen Sie die Verbindungseinstellungen oder wenden Si<PERSON> sich an Ihren Administrator.<br><PERSON><PERSON> <PERSON><PERSON> auf die Schaltfläche \"OK\" klicken, werden Sie aufgefordert das Dokument herunterzuladen.", "PE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON> Fehler.<br><PERSON><PERSON> beim Verbinden zur Datenbank. Bitte wenden Si<PERSON> sich an den Kundendienst, falls der Fehler bestehen bleibt.", "PE.Controllers.Main.errorDataEncrypted": "Änderungen wurden verschlüsselt. Sie können nicht entschlüsselt werden.", "PE.Controllers.Main.errorDataRange": "Falscher Datenbereich.", "PE.Controllers.Main.errorDefaultMessage": "Fehlercode: %1", "PE.Controllers.Main.errorDirectUrl": "Bitte überprüfen Sie den Link zum Dokument.<br><PERSON>ser Link muss ein direkter Link zu der Datei zum Herunterladen sein.", "PE.Controllers.Main.errorEditingDownloadas": "Bei der Arbeit mit dem Dokument ist ein Fehler aufgetreten. <br> Verwenden Sie die Option 'Herunterladen als', um die Sicherungskopie der Datei auf der Festplatte Ihres Computers zu speichern.", "PE.Controllers.Main.errorEditingSaveas": "Bei der Arbeit mit dem Dokument ist ein Fehler aufgetreten. <br> Verwenden Sie die Option \"Speichern als ...\", um die Sicherungskopie der Datei auf der Festplatte Ihres Computers zu speichern.", "PE.Controllers.Main.errorEmailClient": "<PERSON>s wurde kein E-Mail-Client gefunden.", "PE.Controllers.Main.errorFilePassProtect": "Das Dokument ist kennwortgeschützt und kann nicht geöffnet werden.", "PE.Controllers.Main.errorFileSizeExceed": "Die Dateigröße überschreitet die für Ihren Server festgelegte Einschränkung.<br>Weitere Informationen können Sie von Ihrem Document Server-Administrator er<PERSON><PERSON>.", "PE.Controllers.Main.errorForceSave": "<PERSON>im Speichern der Datei ist ein Fehler aufgetreten. Verwenden Sie die Option \"Herunterladen als\", um die Datei auf Ihrer Computerfestplatte zu speichern oder versuchen Sie es später erneut.", "PE.Controllers.Main.errorInconsistentExt": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei stimmt nicht mit der Dateierweiterung überein.", "PE.Controllers.Main.errorInconsistentExtDocx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Textdokumenten (z.B. docx), aber die Datei hat die inkonsistente Erweiterung: %1.", "PE.Controllers.Main.errorInconsistentExtPdf": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht einem der folgenden Formate: pdf/djvu/xps/oxps, aber die Datei hat die inkonsistente Erweiterung: %1.", "PE.Controllers.Main.errorInconsistentExtPptx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Präsentationen (z.B. pptx), aber die Datei hat die inkonsistente Erweiterung: %1.", "PE.Controllers.Main.errorInconsistentExtXlsx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Tabellenkalkulationen (z.B. xlsx), aber die Datei hat die inkonsistente Erweiterung: %1.", "PE.Controllers.Main.errorKeyEncrypt": "Unbekannter Schlüsseldeskriptor", "PE.Controllers.Main.errorKeyExpire": "Der Schlüsseldeskriptor ist abgelaufen", "PE.Controllers.Main.errorLoadingFont": "Schriftarten nicht hochgeladen.<br><PERSON><PERSON> wenden <PERSON> sich an <PERSON><PERSON> von Ihrem Document Server.", "PE.Controllers.Main.errorProcessSaveResult": "Speichern ist fehlgeschlagen.", "PE.Controllers.Main.errorSaveWatermark": "Diese Datei enthält ein Wasserzeichen, das mit einer anderen Domain verknüpft ist.<br>Um es in PDF sichtbar zu machen, aktualisieren Sie das Wasserzeichen, so dass es von derselben Domain wie Ihr Dokument verlinkt wird, oder laden Sie es von Ihrem Computer hoch.", "PE.Controllers.Main.errorServerVersion": "Editor-Version wurde aktualisiert. Die Seite wird neu geladen, um die Änderungen zu übernehmen.", "PE.Controllers.Main.errorSessionAbsolute": "Die Bearbeitungssitzung des Dokumentes ist abgelaufen. Laden Sie die Seite neu.", "PE.Controllers.Main.errorSessionIdle": "Das Dokument wurde lange nicht bearbeitet. Laden Sie die Seite neu.", "PE.Controllers.Main.errorSessionToken": "Die Verbindung zum Server wurde unterbrochen. Laden Sie die Seite neu.", "PE.Controllers.Main.errorSetPassword": "Das Passwort konnte nicht festgelegt werden.", "PE.Controllers.Main.errorStockChart": "Falsche Reihenfolge der Zeilen. Um ein Kursdiagramm zu erstellen, ordnen Sie die Daten auf dem Blatt folgendermaßen an:<br> Eröffnungspreis, Höchstpreis, Tiefstpreis, Schlusskurs.", "PE.Controllers.Main.errorToken": "Sicherheitstoken des Dokuments ist nicht korrekt.<br><PERSON><PERSON> sich an Ihren Serveradministrator.", "PE.Controllers.Main.errorTokenExpire": "Sicherheitstoken des Dokuments ist abgelaufen.<br><PERSON><PERSON> sich an Ihren Serveradministrator.", "PE.Controllers.Main.errorUpdateVersion": "Die Dateiversion wurde geändert. Die Seite wird neu geladen.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Die Internetverbindung wurde wiederhergestellt und die Dateiversion wurde geändert.<br><PERSON><PERSON> weiterarbeiten können, müssen Sie die Datei herunterladen oder den Inhalt kopieren, um sicherzustellen, dass nichts verloren geht, und diese Seite anschließend neu laden.", "PE.Controllers.Main.errorUserDrop": "<PERSON><PERSON> auf diese Datei ist möglich.", "PE.Controllers.Main.errorUsersExceed": "Die nach dem Zahlungsplan erlaubte Anzahl der Benutzer ist überschritten", "PE.Controllers.Main.errorViewerDisconnect": "Die Verbindung ist unterbrochen. Man kann das Dokument weiterhin anschauen.<br><PERSON><PERSON> ist aber momentan nicht möglich, es herunterzuladen oder zu drucken bis die Verbindung wiederhergestellt <br>und die Seite neu geladen wird.", "PE.Controllers.Main.leavePageText": "In dieser Präsentation gibt es nicht gespeicherte Änderungen. Klicken Sie auf \"Auf dieser Seite bleiben\" und dann auf \"Speichern\", um sie zu speichern. Klicken Sie auf \"Diese Seite verlassen\", um alle nicht gespeicherten Änderungen zu verwerfen.", "PE.Controllers.Main.leavePageTextOnClose": "Alle ungespeicherten Änderungen in dieser Präsentation werden verloren.<br> <PERSON><PERSON><PERSON> <PERSON><PERSON> auf \"Abbrechen\" und anschließend auf \"Speichern\", um die Änderungen zu speichern. Klicken Sie auf den Button \"OK\", so werden alle ungespeicherten Änderungen verloren gehen. ", "PE.Controllers.Main.loadFontsTextText": "Daten werden geladen...", "PE.Controllers.Main.loadFontsTitleText": "Daten werden geladen", "PE.Controllers.Main.loadFontTextText": "Daten werden geladen...", "PE.Controllers.Main.loadFontTitleText": "Daten werden geladen", "PE.Controllers.Main.loadImagesTextText": "Bilder werden geladen...", "PE.Controllers.Main.loadImagesTitleText": "Bilder werden geladen", "PE.Controllers.Main.loadImageTextText": "Bild wird geladen...", "PE.Controllers.Main.loadImageTitleText": "Bild wird geladen", "PE.Controllers.Main.loadingDocumentTextText": "Präsentation wird geladen...", "PE.Controllers.Main.loadingDocumentTitleText": "Präsentation wird geladen ", "PE.Controllers.Main.loadThemeTextText": "Design wird geladen...", "PE.Controllers.Main.loadThemeTitleText": "Laden des Designs", "PE.Controllers.Main.notcriticalErrorTitle": "Achtung", "PE.Controllers.Main.openErrorText": "<PERSON><PERSON> dieser Datei ist ein Fehler aufgetreten.", "PE.Controllers.Main.openTextText": "Präsentation wird geöffnet...", "PE.Controllers.Main.openTitleText": "Präsentation wird ge<PERSON>ffnet", "PE.Controllers.Main.printTextText": "Präsentation wird gedruckt...", "PE.Controllers.Main.printTitleText": "Präsentation wird gedruckt", "PE.Controllers.Main.reloadButtonText": "Seite neu laden", "PE.Controllers.Main.requestEditFailedMessageText": "<PERSON><PERSON> bearbeitet diese Präsentation in diesem Moment. Bitte versuchen Sie es später erneut.", "PE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON> verweigert", "PE.Controllers.Main.saveErrorText": "<PERSON><PERSON> dieser Datei ist ein Fehler aufgetreten.", "PE.Controllers.Main.saveErrorTextDesktop": "Diese Datei kann nicht erstellt oder gespeichert werden.<br>Dies ist möglicherweise davon verursacht: <br>1. Die Datei ist schreibgeschützt. <br>2. Die Datei wird von anderen Benutzern bearbeitet. <br>3. Die Festplatte ist voll oder beschädigt.", "PE.Controllers.Main.saveTextText": "Präsentation wird gespeichert...", "PE.Controllers.Main.saveTitleText": "Präsentation wird gespeichert", "PE.Controllers.Main.scriptLoadError": "Die Verbindung ist zu langsam, einige der Komponenten konnten nicht geladen werden. Bitte laden Sie die Se<PERSON> erneut.", "PE.Controllers.Main.splitDividerErrorText": "Die Zeilenanzahl muss ein Divisor von %1 sein.", "PE.Controllers.Main.splitMaxColsErrorText": "Die Spaltenanzahl muss weniger sein als %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "Die Zeilenanzahl muss weniger als %1 sein.", "PE.Controllers.Main.textAnonymous": "Anonym", "PE.Controllers.Main.textApplyAll": "<PERSON>ür alle Gleichungen verwenden", "PE.Controllers.Main.textBuyNow": "Webseite besuchen", "PE.Controllers.Main.textChangesSaved": "Alle Änderungen wurden gespeichert", "PE.Controllers.Main.textClose": "Schließen", "PE.Controllers.Main.textCloseTip": "<PERSON><PERSON><PERSON>, um den Tipp zu schließen", "PE.Controllers.Main.textConnectionLost": "<PERSON><PERSON> wird vers<PERSON>t, Verbindung herzustellen. Bitte überprüfen Sie die Verbindungseinstellungen.", "PE.Controllers.Main.textContactUs": "Verkaufsteam kontaktieren", "PE.Controllers.Main.textContinue": "Fortsetzen", "PE.Controllers.Main.textConvertEquation": "Diese Gleichung wurde in einer alten Version des Gleichungseditors erstellt, die nicht mehr unterstützt wird. Um die Gleichung zu bearbeiten, konvertieren Sie diese ins Format Office Math ML. <br><PERSON>zt konvertieren?", "PE.Controllers.Main.textCustomLoader": "<PERSON>te beachten Si<PERSON>, dass Si<PERSON> gemäß den Lizenzbedingungen nicht berechtigt sind, den Loader zu wechseln. <br> <PERSON><PERSON> sich an unseren Vertrieb, um ein Angebot zu erhalten.", "PE.Controllers.Main.textDisconnect": "Verbindung wurde unterbrochen", "PE.Controllers.Main.textGuest": "Gas<PERSON>", "PE.Controllers.Main.textHasMacros": "Die Datei beinhaltet automatische Makros.<br><PERSON><PERSON><PERSON><PERSON> Sie Makros ausführen?", "PE.Controllers.Main.textLearnMore": "<PERSON><PERSON> er<PERSON>", "PE.Controllers.Main.textLoadingDocument": "Präsentation wird geladen ", "PE.Controllers.Main.textLongName": "Der Name einer Tabellenansicht darf maximal 128 <PERSON><PERSON>chen lang sein.", "PE.Controllers.Main.textNoLicenseTitle": "Lizenzlimit erreicht", "PE.Controllers.Main.textObject": "Objekt", "PE.Controllers.Main.textPaidFeature": "Kostenpflichtige Funktion", "PE.Controllers.Main.textReconnect": "Verbindung wurde wiederhergestellt", "PE.Controllers.Main.textRemember": "<PERSON><PERSON>rken", "PE.Controllers.Main.textRememberMacros": "Auswahl für alle Makros speichern", "PE.Controllers.Main.textRenameError": "<PERSON><PERSON><PERSON><PERSON> darf nicht leer sein.", "PE.Controllers.Main.textRenameLabel": "Geben Sie den Namen für Zusammenarbeit ein", "PE.Controllers.Main.textRequestMacros": "Ein Makro stellt eine Anfrage an die URL. Möchten Sie die Anfrage an die %1 zulassen?", "PE.Controllers.Main.textShape": "Form", "PE.Controllers.Main.textStrict": "Formaler Modus", "PE.Controllers.Main.textText": "Text", "PE.Controllers.Main.textTryQuickPrint": "Sie haben Schnelldruck gewählt: Das gesamte Dokument wird auf dem zuletzt gewählten oder dem Standarddrucker gedruckt.<br><PERSON><PERSON> fort<PERSON>hren?", "PE.Controllers.Main.textTryUndoRedo": "Undo/Redo Optionen sind  für den halbformalen Zusammenbearbeitungsmodus deaktiviert.<br><PERSON><PERSON><PERSON> <PERSON><PERSON> auf den Button \"Formaler Modus\", um den formalen Zusammenbearbeitungsmodus zu aktivieren, um die Datei, ohne Störungen anderer Benutzer zu bearbeiten und die Änderungen erst nachdem Sie sie gespeichert haben, zu senden. Sie können zwischen den Zusammenbearbeitungsmodi mit der Hilfe der erweiterten Einstellungen von Editor umschalten.", "PE.Controllers.Main.textTryUndoRedoWarn": "Die Optionen Rückgängig/Wiederholen sind für den halbformalen Zusammenbearbeitungsmodus deaktiviert.", "PE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textUpdateVersion": "Das Dokument kann im Moment nicht bearbeitet werden.<br><PERSON><PERSON> wird versucht, die Datei zu aktualisieren, bitte warten …", "PE.Controllers.Main.textUpdating": "Aktualisierung", "PE.Controllers.Main.titleLicenseExp": "Lizenz ist abgelaufen", "PE.Controllers.Main.titleLicenseNotActive": "Lizenz nicht aktiv", "PE.Controllers.Main.titleServerVersion": "Editor wurde aktualisi<PERSON>", "PE.Controllers.Main.titleUpdateVersion": "Version wurde geändert", "PE.Controllers.Main.txtAddFirstSlide": "<PERSON><PERSON><PERSON>, um die erste Folie hinzuzufügen", "PE.Controllers.Main.txtAddNotes": "<PERSON><PERSON><PERSON>, um Notizen hinzuzufügen", "PE.Controllers.Main.txtAnimationPane": "Animationsbereich", "PE.Controllers.Main.txtArt": "<PERSON><PERSON> den <PERSON> e<PERSON>ben", "PE.Controllers.Main.txtBasicShapes": "Standardformen", "PE.Controllers.Main.txtButtons": "Schaltflächen", "PE.Controllers.Main.txtCallouts": "<PERSON><PERSON>", "PE.Controllers.Main.txtCharts": "Diagramme", "PE.Controllers.Main.txtClipArt": "ClipArt", "PE.Controllers.Main.txtDateTime": "Datum und Zeit", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Diagrammtitel", "PE.Controllers.Main.txtEditingMode": "Bearbeitungsmodul festlegen...", "PE.Controllers.Main.txtEnd": "Ende: ${0}S", "PE.Controllers.Main.txtErrorLoadHistory": "Laden der Historie ist fehlgeschlagen ", "PE.Controllers.Main.txtFiguredArrows": "Geformte Pfeile", "PE.Controllers.Main.txtFirstSlide": "Erste Folie", "PE.Controllers.Main.txtFooter": "Fußzeile", "PE.Controllers.Main.txtHeader": "Kopfzeile", "PE.Controllers.Main.txtImage": "Bild", "PE.Controllers.Main.txtLastSlide": "Letzte Folie", "PE.Controllers.Main.txtLines": "<PERSON><PERSON>", "PE.Controllers.Main.txtLoading": "Ladevorgang...", "PE.Controllers.Main.txtLoop": "Schleife: ${0}S", "PE.Controllers.Main.txtMath": "Mathematik", "PE.Controllers.Main.txtMedia": "Medien", "PE.Controllers.Main.txtNeedSynchronize": "Änderungen wurden vorgenommen", "PE.Controllers.Main.txtNextSlide": "Nächste Folie", "PE.Controllers.Main.txtNone": "Kein(e)", "PE.Controllers.Main.txtPicture": "Bild", "PE.Controllers.Main.txtPlayAll": "Alles abspielen", "PE.Controllers.Main.txtPlayFrom": "<PERSON><PERSON><PERSON><PERSON> von", "PE.Controllers.Main.txtPlaySelected": "Ausgewähltes abspielen", "PE.Controllers.Main.txtPrevSlide": "Vorherige Folie", "PE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSaveCopyAsComplete": "Die Dateikopie wurde erfolgreich gespeichert", "PE.Controllers.Main.txtScheme_Aspect": "Aspekt ", "PE.Controllers.Main.txtScheme_Blue": "Blau", "PE.Controllers.Main.txtScheme_Blue_Green": "Blau Grün", "PE.Controllers.Main.txtScheme_Blue_II": "Blau II", "PE.Controllers.Main.txtScheme_Blue_Warm": "<PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Grayscale": "Grauskala", "PE.Controllers.Main.txtScheme_Green": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Green_Yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Marquee": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Median": "<PERSON><PERSON>lwer<PERSON>", "PE.Controllers.Main.txtScheme_Office": "Office", "PE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "PE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "PE.Controllers.Main.txtScheme_Orange": "Orange", "PE.Controllers.Main.txtScheme_Orange_Red": "<PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Paper": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Red": "Rot", "PE.Controllers.Main.txtScheme_Red_Orange": "Rot-Orange", "PE.Controllers.Main.txtScheme_Red_Violet": "Rot<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Slipstream": "Windschatten", "PE.Controllers.Main.txtScheme_Violet": "<PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Violet_II": "Violett II", "PE.Controllers.Main.txtScheme_Yellow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtScheme_Yellow_Orange": "Gelb-Orange", "PE.Controllers.Main.txtSeries": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Legende mit Linie 1 (<PERSON><PERSON><PERSON> und Markierungsleiste)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Legende mit Linie 2 (<PERSON><PERSON><PERSON> und Markierungsleiste)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Legende mit Linie 3 (<PERSON><PERSON><PERSON> und Markierungsleiste)", "PE.Controllers.Main.txtShape_accentCallout1": "Legende mit Linie 1 (Markierungsleiste)", "PE.Controllers.Main.txtShape_accentCallout2": "Legende mit Linie 2 (Markierungsle<PERSON><PERSON>)", "PE.Controllers.Main.txtShape_accentCallout3": "Legende mit Linie 3 (Markierungsle<PERSON><PERSON>)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Schaltfläche \"Zurück\"", "PE.Controllers.Main.txtShape_actionButtonBeginning": "<PERSON><PERSON> \"Start\"", "PE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonDocument": "Dokumentschaltfläche", "PE.Controllers.Main.txtShape_actionButtonEnd": "Schaltfläche Beenden", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "Schaltfläche Weiter", "PE.Controllers.Main.txtShape_actionButtonHelp": "Schaltfläche \"Hilfe\"", "PE.Controllers.Main.txtShape_actionButtonHome": "Schaltfläche \"Startseite\"", "PE.Controllers.Main.txtShape_actionButtonInformation": "Schaltfläche \"Informationen\"", "PE.Controllers.Main.txtShape_actionButtonMovie": "Schaltfläche \"Movie\"", "PE.Controllers.Main.txtShape_actionButtonReturn": "Schaltfläche „Zurück\"", "PE.Controllers.Main.txtShape_actionButtonSound": "Schaltfläche \"Ton\"", "PE.Controllers.Main.txtShape_arc": "Bogen", "PE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_bentConnector5": "Gewinkelte Verbindung", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Gewinkelte Verbindung mit Pfeil", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Gewinkelte Verbindung mit Doppelpfeil", "PE.Controllers.Main.txtShape_bentUpArrow": "Nach oben gebogener Pfeil", "PE.Controllers.Main.txtShape_bevel": "Schräge Kante", "PE.Controllers.Main.txtShape_blockArc": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_borderCallout1": "Legende mit Linie 1", "PE.Controllers.Main.txtShape_borderCallout2": "Legende mit Linie 2", "PE.Controllers.Main.txtShape_borderCallout3": "Legende mit Linie 3", "PE.Controllers.Main.txtShape_bracePair": "Geschweifte Klammer", "PE.Controllers.Main.txtShape_callout1": "Legende mit Linie 1 (<PERSON><PERSON>)", "PE.Controllers.Main.txtShape_callout2": "Legende mit Linie 2 (<PERSON><PERSON>)", "PE.Controllers.Main.txtShape_callout3": "Legende mit Linie 3 (<PERSON><PERSON>)", "PE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_chevron": "Chevron", "PE.Controllers.Main.txtShape_chord": "Akkord", "PE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_cloud": "Cloud", "PE.Controllers.Main.txtShape_cloudCallout": "Wolkenförmige Legende", "PE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_cube": "C<PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Gekrümmte Verbindung", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Gekrümmte Verbindung mit Pfeil", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Gekrümmte Verbindung mit Doppelpfeil", "PE.Controllers.Main.txtShape_curvedDownArrow": "Nach unten gekrümmter Pfeil", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Nach links gekrümmter Pfeil", "PE.Controllers.Main.txtShape_curvedRightArrow": "Nach rechts gekrümmter Pfeil", "PE.Controllers.Main.txtShape_curvedUpArrow": "Nach oben gekr<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_decagon": "Zeh<PERSON>", "PE.Controllers.Main.txtShape_diagStripe": "Diagonaler Streifen", "PE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_dodecagon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_donut": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_doubleWave": "Doppelte Welle", "PE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON><PERSON> nach unten", "PE.Controllers.Main.txtShape_downArrowCallout": "Legende mit Pfeil nach unten", "PE.Controllers.Main.txtShape_ellipse": "Ellipse", "PE.Controllers.Main.txtShape_ellipseRibbon": "Nach unten gekrümmtes Band", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Nach oben gekrümmtes Band", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Flussdiagramm: Alternativer Prozess", "PE.Controllers.Main.txtShape_flowChartCollate": "Flussdiagramm: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartConnector": "Flussdiagramm: Verbindungsstelle", "PE.Controllers.Main.txtShape_flowChartDecision": "Flussdiagramm: Verzweigung", "PE.Controllers.Main.txtShape_flowChartDelay": "Flussdiagramm: Verzögerung", "PE.Controllers.Main.txtShape_flowChartDisplay": "Flussdiagramm: Anzeige", "PE.Controllers.Main.txtShape_flowChartDocument": "Flussdiagramm: Dokument", "PE.Controllers.Main.txtShape_flowChartExtract": "Flussdiagramm: Auszug", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Flussdiagramm: Daten", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Flussdiagramm: Zentralspeicher", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flussdiagramm: Magnetplattenspeicher", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flussdiagramm: Datenträger mit direktem Zugriff", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Flussdiagramm: Datenträger mit sequenziellem Zugriff", "PE.Controllers.Main.txtShape_flowChartManualInput": "Flussdiagramm: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Flussdiagramm: <PERSON><PERSON> Verarbeitung", "PE.Controllers.Main.txtShape_flowChartMerge": "Flussdiagramm: Z<PERSON>mmenführen", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Flussdiagramm: <PERSON><PERSON>ere Dokumente", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flussdiagramm: Verbindungsstelle zu einer anderen Seite", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flussdiagramm: Gespeicherte Daten", "PE.Controllers.Main.txtShape_flowChartOr": "Flussdiagramm: Oder", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flussdiagramm: Vordefinierter Prozess", "PE.Controllers.Main.txtShape_flowChartPreparation": "Flussdiagramm: Vorbereitung", "PE.Controllers.Main.txtShape_flowChartProcess": "Flussdiagramm: Pro<PERSON>s", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Flussdiagramm: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Flussdiagramm: Lochstreifen", "PE.Controllers.Main.txtShape_flowChartSort": "Flussdiagramm: Sortieren", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Flussdiagramm: Zusammenführung", "PE.Controllers.Main.txtShape_flowChartTerminator": "Flussdiagramm: Grenzstelle", "PE.Controllers.Main.txtShape_foldedCorner": "Gefal<PERSON><PERSON>", "PE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_heart": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_heptagon": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_hexagon": "Sechseck", "PE.Controllers.Main.txtShape_homePlate": "Richtungspfeil", "PE.Controllers.Main.txtShape_horizontalScroll": "Horizontaler Bildlauf", "PE.Controllers.Main.txtShape_irregularSeal1": "Explosion 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Explosion 2", "PE.Controllers.Main.txtShape_leftArrow": "Pfeil nach links", "PE.Controllers.Main.txtShape_leftArrowCallout": "Legende mit Pfeil nach links", "PE.Controllers.Main.txtShape_leftBrace": "Geschweifte Klammer links", "PE.Controllers.Main.txtShape_leftBracket": "Runde K<PERSON> links", "PE.Controllers.Main.txtShape_leftRightArrow": "Pfeil nach links und rechts", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Legende mit Pfeil nach links und rechts", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Pfeil nach links, rechts und oben", "PE.Controllers.Main.txtShape_leftUpArrow": "Pfeil nach links und oben", "PE.Controllers.Main.txtShape_lightningBolt": "Gewitterblitz", "PE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "Pfeil", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "Doppelpfeil", "PE.Controllers.Main.txtShape_mathDivide": "Division", "PE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMinus": "Minus", "PE.Controllers.Main.txtShape_mathMultiply": "Multiplizieren", "PE.Controllers.Main.txtShape_mathNotEqual": "<PERSON><PERSON> gleich", "PE.Controllers.Main.txtShape_mathPlus": "Plus", "PE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_noSmoking": "Symbol \"Nein\"", "PE.Controllers.Main.txtShape_notchedRightArrow": "Eingekerbter Pfeil nach rechts", "PE.Controllers.Main.txtShape_octagon": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_parallelogram": "Parallelogramm", "PE.Controllers.Main.txtShape_pentagon": "Richtungspfeil", "PE.Controllers.Main.txtShape_pie": "Kreis", "PE.Controllers.Main.txtShape_plaque": "Signieren", "PE.Controllers.Main.txtShape_plus": "Plus", "PE.Controllers.Main.txtShape_polyline1": "Skizze", "PE.Controllers.Main.txtShape_polyline2": "Freihandform", "PE.Controllers.Main.txtShape_quadArrow": "Pfeil in vier Richtungen", "PE.Controllers.Main.txtShape_quadArrowCallout": "Legende mit Pfeil in vier Richtungen", "PE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_ribbon": "Band nach unten", "PE.Controllers.Main.txtShape_ribbon2": "<PERSON> hoch", "PE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON> nach rechts", "PE.Controllers.Main.txtShape_rightArrowCallout": "Legende mit Pfeil nach rechts", "PE.Controllers.Main.txtShape_rightBrace": "Geschweifte Klammer rechts", "PE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON> re<PERSON>s", "PE.Controllers.Main.txtShape_round1Rect": "Eine Ecke des Rechtecks abrunden", "PE.Controllers.Main.txtShape_round2DiagRect": "Diagonal liegende Ecken des Rechtecks abrunden", "PE.Controllers.Main.txtShape_round2SameRect": "Auf der gleichen Seite des Rechtecks liegende Ecken abrunden", "PE.Controllers.Main.txtShape_roundRect": "Rechteck mit runden Ecken", "PE.Controllers.Main.txtShape_rtTriangle": "Rechtwinkliges Dreieck", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_snip1Rect": "Eine Ecke des Rechtecks schneiden", "PE.Controllers.Main.txtShape_snip2DiagRect": "Diagonal liegende Ecken des Rechtecks schneiden", "PE.Controllers.Main.txtShape_snip2SameRect": "Ecken des Rechtecks auf der gleichen Seite schneiden", "PE.Controllers.Main.txtShape_snipRoundRect": "Eine Ecke des Rechtecks schneiden und abrunden", "PE.Controllers.Main.txtShape_spline": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "10-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star12": "12-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star16": "16-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star24": "24-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star32": "32-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star4": "4-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star6": "6-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star7": "7-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star8": "8-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_stripedRightArrow": "Gestreifter Pfeil nach rechts", "PE.Controllers.Main.txtShape_sun": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_teardrop": "Tropfenförmig", "PE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "PE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON><PERSON> nach oben", "PE.Controllers.Main.txtShape_upArrowCallout": "Legende mit Pfeil nach oben", "PE.Controllers.Main.txtShape_upDownArrow": "<PERSON><PERSON><PERSON> nach unten", "PE.Controllers.Main.txtShape_uturnArrow": "180-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_verticalScroll": "<PERSON><PERSON><PERSON><PERSON> Bildlau<PERSON>", "PE.Controllers.Main.txtShape_wave": "Welle", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON>e Legende", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Rechteckige Legende", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Abgerundete rechteckige Legende", "PE.Controllers.Main.txtSldLtTBlank": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTChart": "Diagramm", "PE.Controllers.Main.txtSldLtTChartAndTx": "Diagramm und Text", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "ClipArt und Text", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "ClipArt und vertikaler Text", "PE.Controllers.Main.txtSldLtTCust": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTDgm": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTFourObj": "Vier Objekte", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Medien und Text", "PE.Controllers.Main.txtSldLtTObj": "Titel und Objekt", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Objekt und zwei Objekte", "PE.Controllers.Main.txtSldLtTObjAndTx": "Objekt und Text", "PE.Controllers.Main.txtSldLtTObjOnly": "Objekt", "PE.Controllers.Main.txtSldLtTObjOverTx": "Objekt über Text", "PE.Controllers.Main.txtSldLtTObjTx": "Titel, Objekt und Beschriftung", "PE.Controllers.Main.txtSldLtTPicTx": "Bild und Überschrift", "PE.Controllers.Main.txtSldLtTSecHead": "Überschrift des Abschnitts", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitle": "Titel", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON> T<PERSON>l", "PE.Controllers.Main.txtSldLtTTwoColTx": "Zweispaltiger Text", "PE.Controllers.Main.txtSldLtTTwoObj": "Zwei Objekte", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Zwei Objekte und Objekt", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Zwei Objekte und Text", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Zwei Objekte über Text", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Zwei Texte und zwei Objekte", "PE.Controllers.Main.txtSldLtTTx": "Text", "PE.Controllers.Main.txtSldLtTTxAndChart": "Text und Diagramm", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Text und ClipArt", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Text und Medien", "PE.Controllers.Main.txtSldLtTTxAndObj": "Text und Objekt", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Text und zwei Objekte", "PE.Controllers.Main.txtSldLtTTxOverObj": "Text über Objekt", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Vertikaler Titel und Text", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Vertikaler Titel und Text über Diagramm", "PE.Controllers.Main.txtSldLtTVertTx": "Vertikaler Text", "PE.Controllers.Main.txtSlideNumber": "Foliennummer", "PE.Controllers.Main.txtSlideSubtitle": "Folienuntertitel", "PE.Controllers.Main.txtSlideText": "Folientext", "PE.Controllers.Main.txtSlideTitle": "Folientitel", "PE.Controllers.Main.txtStarsRibbons": "Sterne & Bänder", "PE.Controllers.Main.txtStart": "Anfang: ${0}S", "PE.Controllers.Main.txtStop": "Stoppen", "PE.Controllers.Main.txtTheme_basic": "Standard", "PE.Controllers.Main.txtTheme_blank": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_classic": "Klassisch", "PE.Controllers.Main.txtTheme_corner": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_dotted": "Gepunktet", "PE.Controllers.Main.txtTheme_green": "gr<PERSON>n", "PE.Controllers.Main.txtTheme_green_leaf": "<PERSON><PERSON><PERSON><PERSON> ", "PE.Controllers.Main.txtTheme_lines": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office_theme": "Office-Design", "PE.Controllers.Main.txtTheme_official": "Offiziell", "PE.Controllers.Main.txtTheme_pixel": "Pixel", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Schildkröte", "PE.Controllers.Main.txtXAxis": "x-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtYAxis": "y-<PERSON><PERSON>e", "PE.Controllers.Main.txtZoom": "Vergrößern", "PE.Controllers.Main.unknownErrorText": "Unbek<PERSON><PERSON> Fehler.", "PE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON> wird nicht unterstützt.", "PE.Controllers.Main.uploadImageExtMessage": "Unbekanntes Bildformat.", "PE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON> Bilde<PERSON> ho<PERSON>.", "PE.Controllers.Main.uploadImageSizeMessage": "Die maximal zulässige Bildgröße von 25 MB ist überschritten.", "PE.Controllers.Main.uploadImageTextText": "Bild wird hochgeladen...", "PE.Controllers.Main.uploadImageTitleText": "Bild wird hoch<PERSON>aden", "PE.Controllers.Main.waitText": "Bitte warten...", "PE.Controllers.Main.warnBrowserIE9": "Die Anwendung hat geringe Fähigkeiten in IE9. Nutzen Sie IE10 oder höher.", "PE.Controllers.Main.warnBrowserZoom": "Die aktuelle Zoom-Einstellung Ihres Webbrowsers wird nicht völlig unterstützt. Bitte stellen Sie die Standardeinstellung mithilfe der Tastenkombination Strg+0 wieder her.", "PE.Controllers.Main.warnLicenseAnonymous": "Zugriff für anonyme Benutzer verweigert.<br>Dieses Dokument wird nur zur Ansicht geöffnet.", "PE.Controllers.Main.warnLicenseBefore": "<PERSON><PERSON><PERSON> nicht aktiv.<br><PERSON><PERSON> wenden <PERSON> sich an Ihren Administrator.", "PE.Controllers.Main.warnLicenseExceeded": "Sie haben das Limit für gleichzeitige Verbindungen in %1-Editoren erreicht. Dieses Dokument wird nur zum Anzeigen geöffnet.<br><PERSON>te wenden Si<PERSON> sich an Ihren Administrator, um weitere Informationen zu erhalten.", "PE.Controllers.Main.warnLicenseExp": "<PERSON>hre Lizenz ist abgelaufen.<br>Bitte aktualisieren Sie Ihre Lizenz und laden Si<PERSON> die Seite neu.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Die Lizenz ist abgelaufen.<br>Die Bearbeitungsfunktionen sind nicht verfügbar.<br><PERSON><PERSON> wenden <PERSON> sich an Ihrem Administrator.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Die Lizenz soll aktualisiert werden.<br>Die Bearbeitungsfunktionen sind eingeschränkt.<br><PERSON>te wenden Si<PERSON> sich an Ihrem Administrator für vollen Zugriff", "PE.Controllers.Main.warnLicenseUsersExceeded": "Sie haben das Benutzerlimit für %1-Editoren erreicht. Bitte wenden Si<PERSON> sich an Ihren Administrator, um weitere Informationen zu erhalten.", "PE.Controllers.Main.warnNoLicense": "Sie haben das Limit für gleichzeitige Verbindungen in %1-Editoren erreicht. Dieses Dokument wird nur zum Anzeigen geöffnet.<br>Bitte kontaktieren Sie unser Verkaufsteam, um persönliche Upgrade-Bedingungen zu erhalten.", "PE.Controllers.Main.warnNoLicenseUsers": "Sie haben das Benutzerlimit für %1-Editoren erreicht. Bitte kontaktieren Sie unser Verkaufsteam, um persönliche Upgrade-Bedingungen zu erhalten.", "PE.Controllers.Main.warnProcessRightsChange": "<PERSON>, die Datei zu bearbeiten, wurde Ihnen verweigert.", "PE.Controllers.Print.txtPrintRangeInvalid": "Ungültiger Druckbereich", "PE.Controllers.Print.txtPrintRangeSingleRange": "Geb<PERSON> Sie entweder eine einzelne Foliennummer oder einen einzelnen Folienbereich ein (z. B. 5-12). Oder Sie können in PDF drucken.", "PE.Controllers.Search.notcriticalErrorTitle": "Achtung", "PE.Controllers.Search.textNoTextFound": "<PERSON> Daten, nach denen <PERSON> gesucht haben, können nicht gefunden werden. Bitte ändern Sie die Suchparameter.", "PE.Controllers.Search.textReplaceSkipped": "Der Ersatzvorgang wurde durchgeführt. {0} Vorkommen wurden ausgelassen.", "PE.Controllers.Search.textReplaceSuccess": "Die Suche wurde durchgeführt. {0} Einträge wurden ersetzt", "PE.Controllers.Search.warnReplaceString": "{0} kann als Sonderzeichen für das Feld \"Ersetzen durch\" nicht verwendet werden.", "PE.Controllers.Statusbar.textDisconnect": "<b>Die Verbindung wurde unterbrochen</b><br>Verbindungsversuch... Bitte Verbindungseinstellungen überprüfen.", "PE.Controllers.Statusbar.zoomText": "Zoom {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "<PERSON> Schriftart, die Sie verwenden wollen, ist auf diesem Gerät nicht verfügbar.<br>Der Textstil wird mit einer der Systemschriften angezeigt, die gespeicherte Schriftart wird verwendet, wenn sie verfügbar ist.<br>Wollen Sie fortsetzen?", "PE.Controllers.Toolbar.helpMergeShapes": "Ko<PERSON><PERSON>eren, fragmentieren, schneiden und subtrahieren Sie Formen, um benutzerdefinierte visuelle Elemente zu erstellen.", "PE.Controllers.Toolbar.helpMergeShapesHeader": "Formen zusammenführen", "PE.Controllers.Toolbar.helpTabDesign": "Wenden Sie Stile an, ändern Sie Farbschemata und Foliengröße über die neu hinzugefügte Registerkarte \"Design\".", "PE.Controllers.Toolbar.helpTabDesignHeader": "Registerkarte \"Design\"", "PE.Controllers.Toolbar.textAccent": "Akzente", "PE.Controllers.Toolbar.textBracket": "Klammern", "PE.Controllers.Toolbar.textFontSizeErr": "Der eingegebene Wert ist falsch.<br><PERSON><PERSON><PERSON> Sie bitte einen numerischen Wert zwischen 1 und 300 ein.", "PE.Controllers.Toolbar.textFraction": "Bru<PERSON>re<PERSON>nung", "PE.Controllers.Toolbar.textFunction": "Funktionen", "PE.Controllers.Toolbar.textInsert": "Einfügen", "PE.Controllers.Toolbar.textIntegral": "Integrale", "PE.Controllers.Toolbar.textLargeOperator": "Große Operatoren", "PE.Controllers.Toolbar.textLimitAndLog": "Grenzwerte und Logarithmen", "PE.Controllers.Toolbar.textMatrix": "Matrizen", "PE.Controllers.Toolbar.textOperator": "Operatoren", "PE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "Symbole", "PE.Controllers.Toolbar.textWarning": "Achtung", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Pfeil nach rechts und links oben", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Pfeil nach links oben", "PE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON><PERSON> nach rechts oben", "PE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_BarBot": "Unterstreichung", "PE.Controllers.Toolbar.txtAccent_BarTop": "Überstreichung", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Geschachtelte Formel (mit Platzhalter)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Geschachtelte Formel (Beispiel)", "PE.Controllers.Toolbar.txtAccent_Check": "Prüfen", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Horizontale geschweifte Klammer (unten)", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Horizontale geschweifte Klammer (oben)", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC Mit Überstreichung", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y Mit Überstreichung", "PE.Controllers.Toolbar.txtAccent_DDDot": "Dreifa<PERSON>", "PE.Controllers.Toolbar.txtAccent_DDot": "Doppelpunkt", "PE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Doppelte Überstreichung", "PE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Gruppierungszeichen unten", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Gruppierungszeichen oben", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Ha<PERSON>une nach links oben", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Ha<PERSON><PERSON> nach rechts oben", "PE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "Spitze Klammern", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Spitze Klammern mit Trennlinien", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Spitze Klammern mit zwei Trennlinien", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON><PERSON> spitze <PERSON>lam<PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON> e<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Run<PERSON> Klammern mit Trennlinien", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON><PERSON> runde Klammer", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON> runde <PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Fälle (zwei Bedingungen)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON>ä<PERSON> (drei Bedingungen)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Stapelobjekt", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Stapel Objekt in eckigen Klammern", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Fallbeispiele", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Binomialkoeffizient", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Binomialkoeffizient in spitzen Klammern", "PE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON><PERSON> vertikaler <PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "<PERSON><PERSON> vert<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON>rt<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON>chter doppelter vertikaler Balken", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON>-<PERSON>rt<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim": "Boden", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Run<PERSON> Klammern mit Trennlinien", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON><PERSON> runde Klammer", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON> runde <PERSON>", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Platzhalter zwischen zwei rechten eckigen Klammern", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Invertierte eckige <PERSON>n", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON>e eckige <PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON><PERSON> e<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Platzhalter zwischen zwei linken eckigen Klammern", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON>chte doppelte eckige <PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionDiagonal": "Versetzter Bruch mit schrägem Bruchstrich", "PE.Controllers.Toolbar.txtFractionDifferential_1": "dx über dy", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Obergrenze Delta y über Obergrenze Delta x", "PE.Controllers.Toolbar.txtFractionDifferential_3": "partielles y über partielles x", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Delta y über Delta x", "PE.Controllers.Toolbar.txtFractionHorizontal": "Bruch mit schrägem Bruchstrich", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi wird durch 2 dividiert", "PE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON> Bru<PERSON>hl", "PE.Controllers.Toolbar.txtFractionVertical": "<PERSON>ruch mit waagerechtem Bruchstrich", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Umgekehrte Kosinusfunktion", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperbolische umgekehrte Kosinusfunktion", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Umgekehrte Kotangensfunktion", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperbolische umgekehrte Kotangensfunktion", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Umgekehrte Kosekansfunktion", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperbolische umgekehrte Kosekans-Funktion", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Umgekehrte Sekansfunktion", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperbolische umgekehrte Sekansfunktion", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Umgekehrte Sinusfunktion", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperbolische umgekehrte Sinusfunktion", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Umgekehrte Tangensfunktion", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperbolische umgekehrte Tangensfunktion", "PE.Controllers.Toolbar.txtFunction_Cos": "Kosinusfunktion", "PE.Controllers.Toolbar.txtFunction_Cosh": "Hyperbolische Kosinusfunktion", "PE.Controllers.Toolbar.txtFunction_Cot": "Kotangensfunktion", "PE.Controllers.Toolbar.txtFunction_Coth": "Hyperbolische Kotangensfunktion", "PE.Controllers.Toolbar.txtFunction_Csc": "Kosekansfunktion", "PE.Controllers.Toolbar.txtFunction_Csch": "Hyperbolische Kosekansfunktion", "PE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Kosinus 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Tangensformel", "PE.Controllers.Toolbar.txtFunction_Sec": "Sekansfunktion", "PE.Controllers.Toolbar.txtFunction_Sech": "Hyperbolische Sekansfunktion", "PE.Controllers.Toolbar.txtFunction_Sin": "Sinusfunktion", "PE.Controllers.Toolbar.txtFunction_Sinh": "Hyperbolische Sinusfunktion", "PE.Controllers.Toolbar.txtFunction_Tan": "Tangensformel", "PE.Controllers.Toolbar.txtFunction_Tanh": "Hyperbolische Tangens-Funktion", "PE.Controllers.Toolbar.txtIntegral": "Integral", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Differenzial Theta", "PE.Controllers.Toolbar.txtIntegral_dx": "Differenzial x", "PE.Controllers.Toolbar.txtIntegral_dy": "Differenzial y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral mit gestapelten Grenzen", "PE.Controllers.Toolbar.txtIntegralDouble": "Doppelintegral", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Doppelintegral mit gestapelten Grenzen", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Doppelintegral mit Grenzen", "PE.Controllers.Toolbar.txtIntegralOriented": "Konturenintegral", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Konturintegral mit gestapelten Grenzen", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Oberflächenintegral", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Oberflächenintegral mit gestapelten Grenzen", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Flächenintegral mit Grenzen", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Konturintegral mit Grenzen", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volumenintegral", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volumenintegral mit gestapelten Grenzen", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volumenintegral mit Grenzen", "PE.Controllers.Toolbar.txtIntegralSubSup": "Integral mit Grenzen", "PE.Controllers.Toolbar.txtIntegralTriple": "Dreifaches Integral", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Dreifaches Integral mit gestapelten Grenzen", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Dreifaches Integral mit Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Logisch und", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Lo<PERSON><PERSON> und mit unteren Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Lo<PERSON><PERSON> und mit Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Logisches Und mit tiefgestellter Untergrenze", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Logisches Und mit tiefgestellten/hochgestellten Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ko<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Nebenprodukt mit unterer Grenze", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Nebenprodukt mit Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Nebenprodukt mit tiefgestellter Untergrenze", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Nebenprodukt mit tiefgestellten/hochgestellten Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summierung über k von n wähle k", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summation von i gleich Null bis n", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summationsbeispiel mit zwei Indizes", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produktbeispiel", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Vereinigungsbeispiel", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>ch oder", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Lo<PERSON>ch oder mit unteren Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Lo<PERSON>ch oder mit Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Lo<PERSON>ch oder mit tiefgestellter Untergrenze", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Logisch oder mit tiefgestellten/hochgestellten Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Schnittmenge", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Schnittpunkt mit unterer Grenze", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Schnittpunkt mit Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Schnittpunkt mit tiefgeschriebener unterer Grenze", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Schnittpunkt mit tiefgestellten/hochgestellten Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkt mit unterer Grenze", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkt mit Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkt mit tiefgestellter Untergrenze", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkt mit tiefgestellten/hochgestellten Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Summenbildung", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summenbildung mit unterer Grenze", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summenbildung mit Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summation mit tiefgestellter Untergrenze", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summierung mit tiefgestellten/hochgestellten Grenzen", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Vereinigung", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Vereinigung mit unterer Grenze", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Vereinigungsgrenzen", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Vereinigung mit tiefgeschriebener unterer Grenze", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Vereinigung mit tiefgeschriebenen/hochgeschriebenen Grenzen", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Beispiel für Grenzwert", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Beispiel für Maximum", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Grenzwert", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Natürlicher Logarithmus", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logarith<PERSON>", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarith<PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Max": "Maximal", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimal", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Leere Matrix", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Leere Matrix", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Leere Matrix", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Leere Matrix", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Leere 2 auf 2 Matrix mit doppelt-vertikalen Klammern", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Leere 2 mal 2 Determinante", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Leere 2 auf 2 Matrix mit runden Klammern", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Leere 2 auf 2 Matrix mit ecki<PERSON>n", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Leere Matrix", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Leere Matrix", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Leere Matrix", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Leere Matrix", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Grundlinienpunkte", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Mittellinienpunkte", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonale Punkte", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "Dünnbesetzte Matrix in runden Klammern", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "Dünnbesetzte Matrix in eckigen Klammern", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Identitätsmatrix mit Nullwerten", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2-Identitätsmatrix mit leeren Zellen außerhalb der Diagonale", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Identitätsmatrix mit Nullwerten", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3-Identitätsmatrix mit leeren Zellen außerhalb der Diagonale", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Pfeil nach rechts und links unten", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Pfeil nach rechts und links oben", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "<PERSON><PERSON>il nach links unten", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Pfeil nach links oben", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON><PERSON> nach rechts unten", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON><PERSON> nach rechts oben", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Doppelpunkt gleich", "PE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Delta ergibt", "PE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON><PERSON> gemäß Definition", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta gleich", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Pfeil nach rechts und links darunter", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Pfeil nach rechts und links darüber", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "<PERSON><PERSON>il nach links unten", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Pfeil nach links oben", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON><PERSON> nach rechts unten", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON><PERSON> nach rechts oben", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Gleich", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON><PERSON> an", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Rechte Seite der quadratischen Formel", "PE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON> eines quadratischen plus b quadratisch", "PE.Controllers.Toolbar.txtRadicalRoot_2": "Quadratwurzel mit Grad", "PE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON> mit Grad", "PE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_1": "x tiefgestelltes y im Quadrat", "PE.Controllers.Toolbar.txtScriptCustom_2": "e zum Minus i Omega t", "PE.Controllers.Toolbar.txtScriptCustom_3": "x im Quadrat", "PE.Controllers.Toolbar.txtScriptCustom_4": "Y links hochgestellt n links tiefgestellt eins", "PE.Controllers.Toolbar.txtScriptSub": "Tiefgestellt", "PE.Controllers.Toolbar.txtScriptSubSup": "Tiefgestellt-Hochgestellt", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Hochgestellter/ tiefgestellter Index links", "PE.Controllers.Toolbar.txtScriptSup": "Hochgestellt", "PE.Controllers.Toolbar.txtSymbol_about": "Circa", "PE.Controllers.Toolbar.txtSymbol_additional": "Komplement", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "PE.Controllers.Toolbar.txtSymbol_approx": "Fast gleich", "PE.Controllers.Toolbar.txtSymbol_ast": "Stern-Operator", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_bullet": "Aufzählungsoperator", "PE.Controllers.Toolbar.txtSymbol_cap": "Schnittmenge", "PE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cdots": "Horizontale Ellipse (Mittellinie)", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Etwa gleich zu", "PE.Controllers.Toolbar.txtSymbol_cup": "Vereinigung", "PE.Controllers.Toolbar.txtSymbol_ddots": "Diagonale Ellipse nach unten rechts", "PE.Controllers.Toolbar.txtSymbol_degree": "Grad", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Divisionszeichen", "PE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON> nach unten", "PE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_equiv": "Identisch mit", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "Vorhand<PERSON>", "PE.Controllers.Toolbar.txtSymbol_factorial": "Faktoriell", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grad Fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON><PERSON> alle", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder gleich wie ", "PE.Controllers.Toolbar.txtSymbol_gg": "Viel g<PERSON>ößer als", "PE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON><PERSON><PERSON> als", "PE.Controllers.Toolbar.txtSymbol_in": "Element", "PE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Pfeil nach links", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Pfeil nach rechts und links", "PE.Controllers.Toolbar.txtSymbol_leq": "<PERSON>er als oder gleich", "PE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON>s", "PE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON> kleiner als", "PE.Controllers.Toolbar.txtSymbol_minus": "Minus", "PE.Controllers.Toolbar.txtSymbol_mp": "Minus Plus", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "<PERSON><PERSON> gleich", "PE.Controllers.Toolbar.txtSymbol_ni": "Enthält als Element", "PE.Controllers.Toolbar.txtSymbol_not": "Negationszeichen", "PE.Controllers.Toolbar.txtSymbol_notexists": "Nicht vorhanden", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omikron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Partielles Differenzial", "PE.Controllers.Toolbar.txtSymbol_percent": "Prozentsatz", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Plus", "PE.Controllers.Toolbar.txtSymbol_pm": "Plus Minus", "PE.Controllers.Toolbar.txtSymbol_propto": "Proportional zu", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_qed": "Ende des Beweises", "PE.Controllers.Toolbar.txtSymbol_rddots": "Horizontale Ellipse nach oben rechts", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> nach rechts", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Wurzelzeichen", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "Folglich", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "Multiplikationszeichen", "PE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON> nach oben", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon Variant", "PE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma Variant", "PE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "<PERSON>olie an<PERSON>en", "PE.Controllers.Viewport.textFitWidth": "<PERSON><PERSON><PERSON> an<PERSON>en", "PE.Views.Animation.str0_5": "0,5 s (sehr schnell)", "PE.Views.Animation.str1": "1 s (schnell)", "PE.Views.Animation.str2": "2 s (Normal)", "PE.Views.Animation.str20": "20 s (sehr langsam)", "PE.Views.Animation.str3": "3 s (langsam)", "PE.Views.Animation.str5": "5 s (sehr langsam)", "PE.Views.Animation.strDelay": "Verzögern", "PE.Views.Animation.strDuration": "<PERSON><PERSON>", "PE.Views.Animation.strRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.strRewind": "Zurückspulen", "PE.Views.Animation.strStart": "Start", "PE.Views.Animation.strTrigger": "<PERSON><PERSON>", "PE.Views.Animation.textAutoPreview": "AutoVorschau", "PE.Views.Animation.textMoreEffects": "Mehr Effekte anzeigen", "PE.Views.Animation.textMoveEarlier": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.textMoveLater": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textMultiple": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textNone": "<PERSON><PERSON>", "PE.Views.Animation.textNoRepeat": "(kein)", "PE.Views.Animation.textOnClickOf": "<PERSON><PERSON> auf", "PE.Views.Animation.textOnClickSequence": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textStartAfterPrevious": "<PERSON><PERSON> vor<PERSON><PERSON>", "PE.Views.Animation.textStartOnClick": "<PERSON><PERSON>", "PE.Views.Animation.textStartWithPrevious": "<PERSON><PERSON> v<PERSON><PERSON><PERSON>", "PE.Views.Animation.textUntilEndOfSlide": "Bis zum Ende der Folie", "PE.Views.Animation.textUntilNextClick": "Bis zum nächsten Klick", "PE.Views.Animation.txtAddEffect": "Animation hinzufügen", "PE.Views.Animation.txtAnimationPane": "Animationsbereich", "PE.Views.Animation.txtParameters": "Optionen", "PE.Views.Animation.txtPreview": "Vorschau", "PE.Views.Animation.txtSec": "s", "PE.Views.AnimationDialog.textPreviewEffect": "Effektvorschau", "PE.Views.AnimationDialog.textTitle": "Mehr Effekte", "PE.Views.ChartSettings.text3dDepth": "Tiefe (% der Basis)", "PE.Views.ChartSettings.text3dHeight": "Höhe (% der Basis)", "PE.Views.ChartSettings.text3dRotation": "3D-Dr<PERSON>ung", "PE.Views.ChartSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "PE.Views.ChartSettings.textAutoscale": "Autoskalierung", "PE.Views.ChartSettings.textChartType": "Diagrammtyp ändern", "PE.Views.ChartSettings.textDefault": "Standardmäßige Drehung", "PE.Views.ChartSettings.textDown": "Unten", "PE.Views.ChartSettings.textEditData": "Daten ä<PERSON>n", "PE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textKeepRatio": "Seitenverhältnis beibehalten", "PE.Views.ChartSettings.textLeft": "Links", "PE.Views.ChartSettings.textNarrow": "Blickfeld verengen", "PE.Views.ChartSettings.textPerspective": "Perspektive", "PE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textRightAngle": "Rechtwinklige Achsen", "PE.Views.ChartSettings.textSize": "Größe", "PE.Views.ChartSettings.textStyle": "Stil", "PE.Views.ChartSettings.textUp": "Aufwärts", "PE.Views.ChartSettings.textWiden": "Blickfeld verbreitern", "PE.Views.ChartSettings.textWidth": "Breite", "PE.Views.ChartSettings.textX": "X-Rotation", "PE.Views.ChartSettings.textY": "Y-Rotation", "PE.Views.ChartSettingsAdvanced.textAlt": "Alternativer Text", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Beschreibung", "PE.Views.ChartSettingsAdvanced.textAltTip": "Die alternative textbasierte Darstellung der visuellen Objektinformation, die den Menschen  mit geistigen Behinderungen oder Sehbehinderungen vorgelesen wird, um besser verstehen zu können, was genau auf dem Bild, Form, Diagramm oder der Tabelle dargestellt wurde.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Titel", "PE.Views.ChartSettingsAdvanced.textCenter": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textChartName": "Name des Diagramms", "PE.Views.ChartSettingsAdvanced.textFrom": "Ab", "PE.Views.ChartSettingsAdvanced.textGeneral": "Allgemeines", "PE.Views.ChartSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Seitenverhältnis beibehalten", "PE.Views.ChartSettingsAdvanced.textPlacement": "Positionierung", "PE.Views.ChartSettingsAdvanced.textPosition": "Position", "PE.Views.ChartSettingsAdvanced.textSize": "Größe", "PE.Views.ChartSettingsAdvanced.textTitle": "Diagramm - Erweiterte Einstellungen", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "<PERSON>bere linke <PERSON>", "PE.Views.ChartSettingsAdvanced.textVertical": "Vertikal", "PE.Views.ChartSettingsAdvanced.textWidth": "Breite", "PE.Views.DateTimeDialog.confirmDefault": "Standardformat für {0}: \"{1}\" festlegen", "PE.Views.DateTimeDialog.textDefault": "Als Standard festlegen", "PE.Views.DateTimeDialog.textFormat": "Formate", "PE.Views.DateTimeDialog.textLang": "<PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.textUpdate": "Automatisch aktualisieren", "PE.Views.DateTimeDialog.txtTitle": "Datum & Uhrzeit", "PE.Views.DocumentHolder.aboveText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.addCommentText": "Kommentar hinzufügen", "PE.Views.DocumentHolder.advancedChartText": "Erweiterte Einstellungen des Diagramms", "PE.Views.DocumentHolder.advancedEquationText": "Einstellungen der Gleichung", "PE.Views.DocumentHolder.advancedImageText": "Erweiterte Einstellungen des Bildes", "PE.Views.DocumentHolder.advancedParagraphText": "Erweiterte Text-Einstellungen", "PE.Views.DocumentHolder.advancedShapeText": "Erweiterte Einstellungen der Form", "PE.Views.DocumentHolder.advancedTableText": "Erweiterte Tabellen-Einstellungen", "PE.Views.DocumentHolder.alignmentText": "Ausrichtung", "PE.Views.DocumentHolder.allLinearText": "Alle – Linear", "PE.Views.DocumentHolder.allProfText": "Alle – Professionelle", "PE.Views.DocumentHolder.belowText": "Unten", "PE.Views.DocumentHolder.cellAlignText": "Vertikale Ausrichtung in Zellen", "PE.Views.DocumentHolder.cellText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.centerText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.columnText": "<PERSON>lt<PERSON>", "PE.Views.DocumentHolder.currLinearText": "Aktuell – Linear", "PE.Views.DocumentHolder.currProfText": "Aktuell – Professionell", "PE.Views.DocumentHolder.deleteColumnText": "Spalte löschen", "PE.Views.DocumentHolder.deleteRowText": "Zeile löschen", "PE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON> l<PERSON>", "PE.Views.DocumentHolder.deleteText": "Löschen", "PE.Views.DocumentHolder.direct270Text": "Text nach oben drehen", "PE.Views.DocumentHolder.direct90Text": "Text nach unten drehen", "PE.Views.DocumentHolder.directHText": "Horizontal", "PE.Views.DocumentHolder.directionText": "Textausrichtung", "PE.Views.DocumentHolder.editChartText": "Daten ä<PERSON>n", "PE.Views.DocumentHolder.editHyperlinkText": "Hyperlink bearbeiten", "PE.Views.DocumentHolder.hideEqToolbar": "Symbolleiste Gleichung ausblenden", "PE.Views.DocumentHolder.hyperlinkText": "Hyperlink", "PE.Views.DocumentHolder.ignoreAllSpellText": "Alle auslassen", "PE.Views.DocumentHolder.ignoreSpellText": "Auslassen", "PE.Views.DocumentHolder.insertColumnLeftText": "Spalte nach links", "PE.Views.DocumentHolder.insertColumnRightText": "Spalte nach rechts", "PE.Views.DocumentHolder.insertColumnText": "Spalte einfügen", "PE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON> un<PERSON>", "PE.Views.DocumentHolder.insertRowText": "<PERSON>eile einfügen", "PE.Views.DocumentHolder.insertText": "Einfügen", "PE.Views.DocumentHolder.langText": "Sprache wählen", "PE.Views.DocumentHolder.latexText": "LaTeX", "PE.Views.DocumentHolder.leftText": "Links", "PE.Views.DocumentHolder.loadSpellText": "Varianten werden geladen...", "PE.Views.DocumentHolder.mergeCellsText": "<PERSON><PERSON><PERSON> verbinden", "PE.Views.DocumentHolder.mniCustomTable": "Benutzerdefinierte Tabelle einfügen", "PE.Views.DocumentHolder.moreText": "<PERSON><PERSON>...", "PE.Views.DocumentHolder.noSpellVariantsText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.originalSizeText": "Tatsächliche Größe", "PE.Views.DocumentHolder.removeHyperlinkText": "Hyperlink entfernen", "PE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "Auswahl", "PE.Views.DocumentHolder.showEqToolbar": "Gleichungs-Symbolleiste anzeigen", "PE.Views.DocumentHolder.spellcheckText": "Rechtschreibprüfung", "PE.Views.DocumentHolder.splitCellsText": "<PERSON><PERSON> te<PERSON>...", "PE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON> te<PERSON>n", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textAddHGuides": "Horizontale Führungslinie hinzufügen", "PE.Views.DocumentHolder.textAddVGuides": "Vertikale Führungslinie hinzufügen", "PE.Views.DocumentHolder.textArrangeBack": "In den Hintergrund", "PE.Views.DocumentHolder.textArrangeBackward": "Eine Ebene nach hinten", "PE.Views.DocumentHolder.textArrangeForward": "Eine Ebene nach vorne", "PE.Views.DocumentHolder.textArrangeFront": "In den Vordergrund bringen", "PE.Views.DocumentHolder.textClearGuides": "Führungslinien löschen", "PE.Views.DocumentHolder.textCm": "cm", "PE.Views.DocumentHolder.textCopy": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCrop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFill": "Ausfüllen", "PE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCustom": "Einstellbar", "PE.Views.DocumentHolder.textCut": "Ausschneiden", "PE.Views.DocumentHolder.textDeleteGuide": "Führungslinie löschen", "PE.Views.DocumentHolder.textDeleteLayout": "Layout entfernen", "PE.Views.DocumentHolder.textDeleteMaster": "Master <PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textDistributeCols": "Spalten verteilen", "PE.Views.DocumentHolder.textDistributeRows": "<PERSON><PERSON><PERSON> verteilen", "PE.Views.DocumentHolder.textDuplicateLayout": "Layout duplizieren", "PE.Views.DocumentHolder.textDuplicateSlideMaster": "Folienmaster duplizieren", "PE.Views.DocumentHolder.textEditObject": "Objekt bearbeiten", "PE.Views.DocumentHolder.textEditPoints": "<PERSON><PERSON> bearbeiten", "PE.Views.DocumentHolder.textFlipH": "Horizontal kippen", "PE.Views.DocumentHolder.textFlipV": "Vertikal kippen", "PE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON> Datei", "PE.Views.DocumentHolder.textFromStorage": "aus dem Speicher", "PE.Views.DocumentHolder.textFromUrl": "Aus URL", "PE.Views.DocumentHolder.textGridlines": "Gitternetzlinien ", "PE.Views.DocumentHolder.textGuides": "Führungslinien", "PE.Views.DocumentHolder.textInsertLayout": "Layout einfügen", "PE.Views.DocumentHolder.textInsertSlideMaster": "Folienmaster einfügen", "PE.Views.DocumentHolder.textNextPage": "Nächste Folie", "PE.Views.DocumentHolder.textPaste": "Einfügen", "PE.Views.DocumentHolder.textPrevPage": "Vorherige Folie", "PE.Views.DocumentHolder.textRemove": "Löschen", "PE.Views.DocumentHolder.textRenameLayout": "Layout umbenennen", "PE.Views.DocumentHolder.textRenameMaster": "Master um<PERSON><PERSON>n", "PE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textResetCrop": "Zuschneiden zurücksetzen", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "Linksdrehung 90 Grad", "PE.Views.DocumentHolder.textRotate90": "90° im UZS drehen", "PE.Views.DocumentHolder.textRulers": "Lineale", "PE.Views.DocumentHolder.textSaveAsPicture": "Als Bild speichern", "PE.Views.DocumentHolder.textShapeAlignBottom": "Unten ausrichten", "PE.Views.DocumentHolder.textShapeAlignCenter": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON> au<PERSON>", "PE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON><PERSON> au<PERSON>", "PE.Views.DocumentHolder.textShapeAlignRight": "Rechts ausrichten", "PE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON> aus<PERSON>", "PE.Views.DocumentHolder.textShapesMerge": "Formen zusammenführen", "PE.Views.DocumentHolder.textShowGridlines": "Gitternetzlinien anzeigen", "PE.Views.DocumentHolder.textShowGuides": "Führungslinien anzeigen", "PE.Views.DocumentHolder.textSlideSettings": "Folien-Einstellungen", "PE.Views.DocumentHolder.textSmartGuides": "Smart-Führungslinien", "PE.Views.DocumentHolder.textSnapObjects": "Objekt an das Raster binden", "PE.Views.DocumentHolder.textStartAfterPrevious": "Nach dem vorherigen starten", "PE.Views.DocumentHolder.textStartOnClick": "<PERSON><PERSON> Klicken starten", "PE.Views.DocumentHolder.textStartWithPrevious": "Zusammen mit dem vorhergehenden starten", "PE.Views.DocumentHolder.textUndo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "PE.Views.DocumentHolder.tipGuides": "Führungslinien anzeigen", "PE.Views.DocumentHolder.tipIsLocked": "Dieses Element wird gerade von einem anderen Benutzer bearbeitet.", "PE.Views.DocumentHolder.toDictionaryText": "Zum Wörterbuch hinzufügen", "PE.Views.DocumentHolder.txtAddBottom": "Unterer Rand hinzufügen", "PE.Views.DocumentHolder.txtAddFractionBar": "Bruchstrich einfügen", "PE.Views.DocumentHolder.txtAddHor": "Horizontale Linie einfügen", "PE.Views.DocumentHolder.txtAddLB": "Linke untere Linie einfügen", "PE.Views.DocumentHolder.txtAddLeft": "Linke Grenze hinzufügen", "PE.Views.DocumentHolder.txtAddLT": "Linke obere Linie einfügen", "PE.Views.DocumentHolder.txtAddRight": "Rechter Rand hinzufügen", "PE.Views.DocumentHolder.txtAddTop": "Oberer Rand hinzufügen", "PE.Views.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON><PERSON> Lin<PERSON>", "PE.Views.DocumentHolder.txtAlign": "Ausrichtung", "PE.Views.DocumentHolder.txtAlignToChar": "An einem Zeichen ausrichten", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtBackground": "Hi<PERSON>grund", "PE.Views.DocumentHolder.txtBorderProps": "Rahmeneigenschaften", "PE.Views.DocumentHolder.txtBottom": "Unten", "PE.Views.DocumentHolder.txtChangeLayout": "Layout <PERSON>n", "PE.Views.DocumentHolder.txtChangeTheme": "Design ändern", "PE.Views.DocumentHolder.txtColumnAlign": "Spaltenausrichtung", "PE.Views.DocumentHolder.txtDecreaseArg": "Argumentgröße reduzieren", "PE.Views.DocumentHolder.txtDeleteArg": "Argument löschen", "PE.Views.DocumentHolder.txtDeleteBreak": "<PERSON>len Umbruch löschen", "PE.Views.DocumentHolder.txtDeleteChars": "Einschlusszeichen löschen", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Einschlusszeichen und Trennzeichen löschen", "PE.Views.DocumentHolder.txtDeleteEq": "Formel löschen", "PE.Views.DocumentHolder.txtDeleteGroupChar": "Zeichen löschen", "PE.Views.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtDeleteSlide": "Folie löschen", "PE.Views.DocumentHolder.txtDistribHor": "Horizontal verteilen", "PE.Views.DocumentHolder.txtDistribVert": "Vertikal verteilen", "PE.Views.DocumentHolder.txtDuplicateSlide": "Folie duplizieren", "PE.Views.DocumentHolder.txtFractionLinear": "Zu linearer Bruchrechnung ändern", "PE.Views.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> verzerrter Bruchrechnung ändern", "PE.Views.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> verzerrter Bruchrechnung ändern", "PE.Views.DocumentHolder.txtGroup": "Gruppieren", "PE.Views.DocumentHolder.txtGroupCharOver": "Zeichen über dem Text", "PE.Views.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON> unter dem Text ", "PE.Views.DocumentHolder.txtHideBottom": "Untere Rahmenlinie verbergen", "PE.Views.DocumentHolder.txtHideBottomLimit": "Untere Grenze verbergen", "PE.Views.DocumentHolder.txtHideCloseBracket": "Schließende Klammer verbergen", "PE.Views.DocumentHolder.txtHideDegree": "Grad verbergen", "PE.Views.DocumentHolder.txtHideHor": "Horizontale Linie verbergen", "PE.Views.DocumentHolder.txtHideLB": "Linke untere Line verbergen", "PE.Views.DocumentHolder.txtHideLeft": "Linker Rand verbergen", "PE.Views.DocumentHolder.txtHideLT": "Linke obere Linie verbergen", "PE.Views.DocumentHolder.txtHideOpenBracket": "Öffnende Klammer verbergen", "PE.Views.DocumentHolder.txtHidePlaceholder": "Platzhalter verbergen", "PE.Views.DocumentHolder.txtHideRight": "Rahmenlinie rechts verbergen", "PE.Views.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> oben verbergen", "PE.Views.DocumentHolder.txtHideTopLimit": "Obergrenze verbergen", "PE.Views.DocumentHolder.txtHideVer": "<PERSON><PERSON><PERSON><PERSON> Lin<PERSON> verb<PERSON>gen", "PE.Views.DocumentHolder.txtIncreaseArg": "Argumentgröße erh<PERSON>hen", "PE.Views.DocumentHolder.txtInsAudio": "Audio einfügen", "PE.Views.DocumentHolder.txtInsChart": "Diagramm einfügen", "PE.Views.DocumentHolder.txtInsertArgAfter": "Argument nachher einfügen", "PE.Views.DocumentHolder.txtInsertArgBefore": "Argument vorher einfügen", "PE.Views.DocumentHolder.txtInsertBreak": "<PERSON><PERSON> Umbruch einfügen", "PE.Views.DocumentHolder.txtInsertEqAfter": "Formel nachher einfügen", "PE.Views.DocumentHolder.txtInsertEqBefore": "Formel vorher einfügen", "PE.Views.DocumentHolder.txtInsImage": "Bild aus Datei einfügen", "PE.Views.DocumentHolder.txtInsImageUrl": "<PERSON><PERSON><PERSON> von URL einfügen", "PE.Views.DocumentHolder.txtInsSmartArt": "SmartArt einfügen", "PE.Views.DocumentHolder.txtInsTable": "<PERSON>bell<PERSON> e<PERSON>fügen", "PE.Views.DocumentHolder.txtInsVideo": "Video einfügen", "PE.Views.DocumentHolder.txtKeepTextOnly": "Nur Text beibehalten", "PE.Views.DocumentHolder.txtLimitChange": "Grenzwerten ändern ", "PE.Views.DocumentHolder.txtLimitOver": "Grenzen über dem Text", "PE.Views.DocumentHolder.txtLimitUnder": "<PERSON><PERSON><PERSON> unter dem <PERSON>", "PE.Views.DocumentHolder.txtMatchBrackets": "<PERSON>ckige Klammern an Argumenthöhe anpassen", "PE.Views.DocumentHolder.txtMatrixAlign": "Matrixausrichtung", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Folie zum Ende verschieben", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Folie zum Anfang verschieben", "PE.Views.DocumentHolder.txtNewSlide": "Neue Folie", "PE.Views.DocumentHolder.txtOverbar": "<PERSON><PERSON> über dem <PERSON>", "PE.Views.DocumentHolder.txtPasteDestFormat": "Zieldesign verwenden", "PE.Views.DocumentHolder.txtPastePicture": "Bild", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Ursprüngliche Formatierung beibehalten", "PE.Views.DocumentHolder.txtPressLink": "<PERSON><PERSON><PERSON> Sie auf die {0}-Taste und klicken Sie auf den Link", "PE.Views.DocumentHolder.txtPreview": "Vorschau starten", "PE.Views.DocumentHolder.txtPrintSelection": "Auswahl drucken", "PE.Views.DocumentHolder.txtRemFractionBar": "Bruchstrich entfernen", "PE.Views.DocumentHolder.txtRemLimit": "Grenzwert entfernen", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Akzentzeichen entfernen", "PE.Views.DocumentHolder.txtRemoveBar": "<PERSON><PERSON><PERSON> en<PERSON>fernen", "PE.Views.DocumentHolder.txtRemScripts": "Skripts entfernen", "PE.Views.DocumentHolder.txtRemSubscript": "Tiefstellung entfernen", "PE.Views.DocumentHolder.txtRemSuperscript": "Hochstellung entfernen", "PE.Views.DocumentHolder.txtResetLayout": "Folie zurücksetzen", "PE.Views.DocumentHolder.txtScriptsAfter": "<PERSON><PERSON><PERSON> nach dem Text", "PE.Views.DocumentHolder.txtScriptsBefore": "Scripts vor dem Text", "PE.Views.DocumentHolder.txtSelectAll": "Alle wählen", "PE.Views.DocumentHolder.txtShowBottomLimit": "Untere Grenze zeigen", "PE.Views.DocumentHolder.txtShowCloseBracket": "Schließende eckige Klammer anzeigen", "PE.Views.DocumentHolder.txtShowDegree": "Grad anzeigen", "PE.Views.DocumentHolder.txtShowOpenBracket": "Öffnende eckige Klammer anzeigen", "PE.Views.DocumentHolder.txtShowPlaceholder": "Platzhaltertext anzeigen", "PE.Views.DocumentHolder.txtShowTopLimit": "Höchstgrenze anzeigen", "PE.Views.DocumentHolder.txtSlide": "Folie", "PE.Views.DocumentHolder.txtSlideHide": "Folie ausblenden ", "PE.Views.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtTop": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtUnderbar": "<PERSON><PERSON> unter dem <PERSON> ", "PE.Views.DocumentHolder.txtUngroup": "Gruppierung aufheben", "PE.Views.DocumentHolder.txtWarnUrl": "Dieser Link kann für Ihr Gerät und Daten gefährlich sein.<br><PERSON><PERSON>cht<PERSON> Sie wirklich fortsetzen?", "PE.Views.DocumentHolder.unicodeText": "Unicode", "PE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.goToSlideText": "Zu Folie übergehen", "PE.Views.DocumentPreview.slideIndexText": "Folie {0} von {1}", "PE.Views.DocumentPreview.txtClose": "Vorschaufenster schließen", "PE.Views.DocumentPreview.txtDraw": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtEndSlideshow": "Slideshow beenden", "PE.Views.DocumentPreview.txtEraser": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtEraseScreen": "Bildschirm löschen", "PE.Views.DocumentPreview.txtExitFullScreen": "Vollbildmodus verlassen", "PE.Views.DocumentPreview.txtFinalMessage": "Ende der Folienvorschau. Zum Schließen bitte klicken.", "PE.Views.DocumentPreview.txtFullScreen": "Vollbild-Modus", "PE.Views.DocumentPreview.txtHighlighter": "Textmarker", "PE.Views.DocumentPreview.txtInkColor": "Tintenfarbe", "PE.Views.DocumentPreview.txtNext": "Nächste Folie", "PE.Views.DocumentPreview.txtPageNumInvalid": "Ungültige Nummer der Folie", "PE.Views.DocumentPreview.txtPause": "Präsentation anhalten", "PE.Views.DocumentPreview.txtPen": "Stift", "PE.Views.DocumentPreview.txtPlay": "Präsentation starten", "PE.Views.DocumentPreview.txtPrev": "Vorherige Folie", "PE.Views.DocumentPreview.txtReset": "Z<PERSON>ücksetzen", "PE.Views.FileMenu.ariaFileMenu": "Dateimenü", "PE.Views.FileMenu.btnAboutCaption": "Über das Produkt", "PE.Views.FileMenu.btnBackCaption": "<PERSON>is<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnCloseEditor": "<PERSON><PERSON> sch<PERSON>n", "PE.Views.FileMenu.btnCloseMenuCaption": "Zurück", "PE.Views.FileMenu.btnCreateNewCaption": "Neue Präsentation erstellen", "PE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON> als", "PE.Views.FileMenu.btnExitCaption": "Schließen", "PE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnHistoryCaption": "Versionshistorie", "PE.Views.FileMenu.btnInfoCaption": "Info", "PE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnRecentFilesCaption": "Zuletzt benutztes Dokument öffnen", "PE.Views.FileMenu.btnRenameCaption": "Umbenennen", "PE.Views.FileMenu.btnReturnCaption": "Zurück zur Präsentation", "PE.Views.FileMenu.btnRightsCaption": "Zugriffsrechte", "PE.Views.FileMenu.btnSaveAsCaption": "Speichern als", "PE.Views.FileMenu.btnSaveCaption": "Speichern", "PE.Views.FileMenu.btnSaveCopyAsCaption": "<PERSON><PERSON> speichern als", "PE.Views.FileMenu.btnSettingsCaption": "Erweiterte Einstellungen", "PE.Views.FileMenu.btnSwitchToMobileCaption": "In den Mobilmodus wechseln", "PE.Views.FileMenu.btnToEditCaption": "Präsentation bearbeiten", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Leere Präsentation", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Neue Präsentation erstellen", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Eigenschaft hinzufügen", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Text hinzufügen", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Zugriffsrechte ändern", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Kommentar", "PE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Allgemein", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Dokumenteigenschaft", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Zuletzt ge<PERSON><PERSON><PERSON> von", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Zuletzt geändert", "PE.Views.FileMenuPanels.DocumentInfo.txtNo": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Speicherort", "PE.Views.FileMenuPanels.DocumentInfo.txtPresentationInfo": "Information zur Präsentation", "PE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Eigenschaften", "PE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Eine Eigenschaft mit diesem Titel existiert bereits", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Personen mit Berechtigungen", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON>a", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Titel", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Hochgeladen", "PE.Views.FileMenuPanels.DocumentInfo.txtYes": "<PERSON>a", "PE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Zugriffsrechte", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Zugriffsrechte ändern", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Personen mit Berechtigungen", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "Präsentation schützen", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON>t Signatur", "PE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Der Präsentation wurden gültige Signaturen hinzugefügt.<br>Die Präsentation ist vor Bearbeitung geschützt.", "PE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Stellen Sie die Integrität der Präsentation durch Hinzufügen einer<br>unsichtbaren digitalen Signatur sicher.", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Präsentation bearbeiten", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Die Bearbeitung entfernt Signaturen aus dieser Präsentation.<br><PERSON><PERSON>cht<PERSON> Sie trotzdem fortsetzen?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Diese Präsentation wurde mit einem Passwort geschützt", "PE.Views.FileMenuPanels.ProtectDoc.txtProtectPresentation": "Verschlüsseln Sie diese Präsentation mit einem Passwort", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Gültige Signaturen wurden der Präsentation hinzugefügt. Die Präsentation ist vor der Bearbeitung geschützt. ", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Einige der digitalen Signaturen in der Präsentation sind ungültig oder konnten nicht verifiziert werden. Die Präsentation ist vor der Bearbeitung geschützt.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "Signaturen anzeigen", "PE.Views.FileMenuPanels.Settings.okButtonText": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Modus \"Gemeinsame Bearbeitung\"", "PE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strFontRender": "Schriftglättung", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Wörter in GROSSBUCHSTABEN ignorieren", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "W<PERSON>rter mit Zahlen ignorieren", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Einstellungen von Ma<PERSON>ros", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Die Schaltfläche Einfügeoptionen beim Einfügen von Inhalten anzeigen", "PE.Views.FileMenuPanels.Settings.strRTLSupport": "RTL-Schnittstelle", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Änderungen von anderen Benutzern anzeigen", "PE.Views.FileMenuPanels.Settings.strStrict": "Formal", "PE.Views.FileMenuPanels.Settings.strTabStyle": "<PERSON><PERSON> der <PERSON>", "PE.Views.FileMenuPanels.Settings.strTheme": "Thema der Benutzeroberfläche", "PE.Views.FileMenuPanels.Settings.strUnit": "Maßeinheit", "PE.Views.FileMenuPanels.Settings.strZoom": "Standard-Zoom-Wert", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Alle 10 Minuten", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Alle 30 Minuten", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Alle 5 Minuten", "PE.Views.FileMenuPanels.Settings.text60Minutes": "Jede Stunde", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Ausrichtungslinien", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Autowiederherstellen", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Automatisch speichern", "PE.Views.FileMenuPanels.Settings.textDisabled": "Ausgeschaltet", "PE.Views.FileMenuPanels.Settings.textFill": "Füllung", "PE.Views.FileMenuPanels.Settings.textForceSave": "<PERSON><PERSON> dem <PERSON> s<PERSON>ichern", "PE.Views.FileMenuPanels.Settings.textLine": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textMinute": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "Erweiterte Einstellungen", "PE.Views.FileMenuPanels.Settings.txtAll": "Alle anzeigen", "PE.Views.FileMenuPanels.Settings.txtAppearance": "Darstellung", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Optionen von Autokorrektur...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Standard-Cache-Modus", "PE.Views.FileMenuPanels.Settings.txtCm": "Zentimeter", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Zusammenarbeit", "PE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "Schnellzugriff anpassen", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Bearbeitung und Speicherung", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Zusammenarbeit in Echtzeit. Alle Änderungen werden automatisch gespeichert", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "<PERSON>olie an<PERSON>en", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "<PERSON><PERSON><PERSON> an<PERSON>en", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Hieroglyphen", "PE.Views.FileMenuPanels.Settings.txtInch": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtLast": "Letzte anzeigen", "PE.Views.FileMenuPanels.Settings.txtLastUsed": "Zuletzt verwendet", "PE.Views.FileMenuPanels.Settings.txtMac": "wie OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtProofing": "Rechtschreibprüfung", "PE.Views.FileMenuPanels.Settings.txtPt": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtQuickPrint": "Die Schaltfläche Schnelldruck in der Kopfzeile des Editors anzeigen", "PE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "Das Dokument wird auf dem zuletzt ausgewählten oder dem standardmäßigen Drucker gedruckt", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Alle aktivieren", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Alle Makros ohne Benachrichtigung aktivieren", "PE.Views.FileMenuPanels.Settings.txtScreenReader": "Unterstützung für Bildschirmleser einschalten", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Rechtschreibprüfung", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Alle deaktivieren", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Alle Makros ohne Benachrichtigung deaktivieren", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Verwenden Sie die Schaltfläche \"Speichern\", um die vorgenommenen Änderungen zu synchronisieren.", "PE.Views.FileMenuPanels.Settings.txtTabBack": "Farbe der Symbolleiste als Hintergrund für Registerkarten verwenden", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Verwenden Sie die Alt-Taste, um über die Tastatur in der Benutzeroberfläche zu navigieren.", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Verwenden Sie die Option-Taste, um über die Tastatur in der Benutzeroberfläche zu navigieren.", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Benachrichtigung anzeigen", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "<PERSON>e Makros mit einer Benachrichtigung deaktivieren", "PE.Views.FileMenuPanels.Settings.txtWin": "wie Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "Arbeitsbereich", "PE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "<PERSON><PERSON><PERSON><PERSON><PERSON> als", "PE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "<PERSON><PERSON> speichern als", "PE.Views.GridSettings.textCm": "cm", "PE.Views.GridSettings.textCustom": "Einstellbar", "PE.Views.GridSettings.textSpacing": "Abstand", "PE.Views.GridSettings.textTitle": "Rastereinstellungen", "PE.Views.HeaderFooterDialog.applyAllText": "Auf alle anwenden", "PE.Views.HeaderFooterDialog.applyText": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.diffLanguage": "Das Datumsformat muss dieselbe Sprache wie der Folienmaster verwenden.<br><PERSON> den Master zu ä<PERSON>, klicken Sie auf 'Auf alle anwenden' an<PERSON><PERSON> von 'Anwenden'.", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textDateTime": "Datum und Uhrzeit", "PE.Views.HeaderFooterDialog.textFixed": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textFormat": "Formate", "PE.Views.HeaderFooterDialog.textHFTitle": "Kopf- und Fußzeileneinstellungen", "PE.Views.HeaderFooterDialog.textLang": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textNotes": "Notizen und Handouts", "PE.Views.HeaderFooterDialog.textNotTitle": "Nicht auf der Titelfolie anzeigen", "PE.Views.HeaderFooterDialog.textPageNum": "Seitenn<PERSON>mer", "PE.Views.HeaderFooterDialog.textPreview": "Vorschau", "PE.Views.HeaderFooterDialog.textSlide": "Folie", "PE.Views.HeaderFooterDialog.textSlideNum": "Foliennummer", "PE.Views.HeaderFooterDialog.textUpdate": "Automatisch aktualisieren", "PE.Views.HeaderFooterDialog.txtFooter": "Fußzeile", "PE.Views.HeaderFooterDialog.txtHeader": "Kopfzeile", "PE.Views.HyperlinkSettingsDialog.strDisplay": "Anzeigen", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Verknüpfen mit", "PE.Views.HyperlinkSettingsDialog.textDefault": "Gewählter Textabschnitt", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Geben Sie die Überschrift hier ein", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "<PERSON><PERSON><PERSON> den <PERSON> hier ein", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "<PERSON>eb<PERSON> Sie den QuickInfo-Text hier ein", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Externer Link", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Folie in dieser Präsentation", "PE.Views.HyperlinkSettingsDialog.textSelectFile": "<PERSON>i ausw<PERSON>hlen", "PE.Views.HyperlinkSettingsDialog.textSlides": "Folien", "PE.Views.HyperlinkSettingsDialog.textTipText": "QuickInfo-Text", "PE.Views.HyperlinkSettingsDialog.textTitle": "Hyperlink-Einstellungen", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Erste Folie", "PE.Views.HyperlinkSettingsDialog.txtLast": "Letzte Folie", "PE.Views.HyperlinkSettingsDialog.txtNext": "Nächste Folie", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON><PERSON> muss eine URL im Format \"http://www.example.com\" enthalten", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Vorherige Folie", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "<PERSON><PERSON> soll maximal 2083 Zeichen beinhalten", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Folie", "PE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "<PERSON><PERSON><PERSON> Sie die Webadresse ein oder wählen Sie eine Datei aus", "PE.Views.ImageSettings.strTransparency": "Undurchsichtigkeit", "PE.Views.ImageSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "PE.Views.ImageSettings.textCrop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropFill": "Ausfüllen", "PE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropToShape": "Auf Form <PERSON>", "PE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textEditObject": "Objekt bearbeiten", "PE.Views.ImageSettings.textFitSlide": "An Folie anpassen", "PE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFromFile": "<PERSON><PERSON> Datei", "PE.Views.ImageSettings.textFromStorage": "aus dem Speicher", "PE.Views.ImageSettings.textFromUrl": "Aus URL", "PE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textHint270": "Um 90 ° gegen den Uhrzeigersinn drehen", "PE.Views.ImageSettings.textHint90": "90° im UZS drehen", "PE.Views.ImageSettings.textHintFlipH": "Horizontal kippen", "PE.Views.ImageSettings.textHintFlipV": "Vertikal kippen", "PE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textOriginalSize": "Tatsächliche Größe", "PE.Views.ImageSettings.textRecentlyUsed": "Zuletzt verwendet", "PE.Views.ImageSettings.textResetCrop": "Zuschneiden zurücksetzen", "PE.Views.ImageSettings.textRotate90": "90 Grad drehen", "PE.Views.ImageSettings.textRotation": "Rotation", "PE.Views.ImageSettings.textSize": "Größe", "PE.Views.ImageSettings.textWidth": "Breite", "PE.Views.ImageSettingsAdvanced.textAlt": "Alternativer Text", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Beschreibung", "PE.Views.ImageSettingsAdvanced.textAltTip": "Die alternative textbasierte Darstellung der visuellen Objektinformation, die den Menschen  mit geistigen Behinderungen oder Sehbehinderungen vorgelesen wird, um besser verstehen zu können, was genau auf dem Bild, Form, Diagramm oder der Tabelle dargestellt wurde.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Titel", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textFlipped": "Gekippt", "PE.Views.ImageSettingsAdvanced.textFrom": "Ab", "PE.Views.ImageSettingsAdvanced.textGeneral": "Allgemeines", "PE.Views.ImageSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontal", "PE.Views.ImageSettingsAdvanced.textImageName": "Bildname", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Seitenverhältnis beibehalten", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "Tatsächliche Größe", "PE.Views.ImageSettingsAdvanced.textPlacement": "Positionierung", "PE.Views.ImageSettingsAdvanced.textPosition": "Position", "PE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "PE.Views.ImageSettingsAdvanced.textSize": "Größe", "PE.Views.ImageSettingsAdvanced.textTitle": "Bild - Erweiterte Einstellungen", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "<PERSON>bere linke <PERSON>", "PE.Views.ImageSettingsAdvanced.textVertical": "Vertikal", "PE.Views.ImageSettingsAdvanced.textVertically": "Vertikal", "PE.Views.ImageSettingsAdvanced.textWidth": "Breite", "PE.Views.LeftMenu.ariaLeftMenu": "<PERSON><PERSON>", "PE.Views.LeftMenu.tipAbout": "Über das Produkt", "PE.Views.LeftMenu.tipChat": "Cha<PERSON>", "PE.Views.LeftMenu.tipComments": "Kommentare", "PE.Views.LeftMenu.tipPlugins": "Plugins", "PE.Views.LeftMenu.tipSearch": "<PERSON><PERSON>", "PE.Views.LeftMenu.tipSlides": "Folien", "PE.Views.LeftMenu.tipSupport": "Feed<PERSON> und Support", "PE.Views.LeftMenu.tipTitles": "Titel", "PE.Views.LeftMenu.txtDeveloper": "ENTWICKLERMODUS", "PE.Views.LeftMenu.txtEditor": "Editor der Präsentationen", "PE.Views.LeftMenu.txtLimit": "Zugriffseinschränkung", "PE.Views.LeftMenu.txtTrial": "Trial-Modus", "PE.Views.LeftMenu.txtTrialDev": "Testversion für Entwickler-Modus", "PE.Views.ParagraphSettings.strLineHeight": "Zeilenabstand", "PE.Views.ParagraphSettings.strParagraphSpacing": "Absatzabstand", "PE.Views.ParagraphSettings.strSpacingAfter": "Nach ", "PE.Views.ParagraphSettings.strSpacingBefore": "Vor", "PE.Views.ParagraphSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "PE.Views.ParagraphSettings.textAt": "<PERSON>", "PE.Views.ParagraphSettings.textAtLeast": "Mindestens", "PE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textExact": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.txtAutoText": "Automatisch", "PE.Views.ParagraphSettingsAdvanced.noTabs": "Die festgelegten Registerkarten werden in diesem Feld erscheinen", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "Alle Großbuchstaben", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Doppeltes Durchstreichen", "PE.Views.ParagraphSettingsAdvanced.strIndent": "Einzüge ", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Links", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Zeilenabstand", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Nach", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Vor ", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Einzüge und Abstände", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Kapitälchen", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Abstand", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Durchgestrichen", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Tiefgestellt", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Hochgestellt", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Tabulator", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Ausrichtung", "PE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Zeichenabstand", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Standardregisterkarte", "PE.Views.ParagraphSettingsAdvanced.textEffects": "Effekte", "PE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Hängend", "PE.Views.ParagraphSettingsAdvanced.textJustified": "Blocksatz", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(<PERSON><PERSON>)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Entfernen", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Alle entfernen", "PE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "Links", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Tabulatorposition", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Absatz - Erweiterte Einstellungen", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "PE.Views.PrintWithPreview.txtAllPages": "Alle Folien", "PE.Views.PrintWithPreview.txtBothSides": "Beidseitiger Druck", "PE.Views.PrintWithPreview.txtBothSidesLongDesc": "Seiten an der langen Seite umblättern", "PE.Views.PrintWithPreview.txtBothSidesShortDesc": "Seiten an der kurzen Seite umblättern", "PE.Views.PrintWithPreview.txtCopies": "Kopien", "PE.Views.PrintWithPreview.txtCurrentPage": "Aktuelle Folie", "PE.Views.PrintWithPreview.txtCustomPages": "Benutzerdefinierter Druck", "PE.Views.PrintWithPreview.txtEmptyTable": "<PERSON>s gibt nichts zu drucken, weil die Präsentation leer ist", "PE.Views.PrintWithPreview.txtHeaderFooterSettings": "Kopf- und Fußzeileneinstellungen", "PE.Views.PrintWithPreview.txtOf": "von {0}", "PE.Views.PrintWithPreview.txtOneSide": "Einseitiger Druck", "PE.Views.PrintWithPreview.txtOneSideDesc": "Nur auf einer Se<PERSON> d<PERSON>cken", "PE.Views.PrintWithPreview.txtPage": "Folie", "PE.Views.PrintWithPreview.txtPageNumInvalid": "Foliennummer ungültig", "PE.Views.PrintWithPreview.txtPages": "Folien", "PE.Views.PrintWithPreview.txtPaperSize": "Papiergröße", "PE.Views.PrintWithPreview.txtPrint": "<PERSON><PERSON><PERSON>", "PE.Views.PrintWithPreview.txtPrintPdf": "Als PDF-<PERSON><PERSON> d<PERSON>n", "PE.Views.PrintWithPreview.txtPrintRange": "Dr<PERSON>berei<PERSON>", "PE.Views.PrintWithPreview.txtPrintSides": "Druckseiten", "PE.Views.RightMenu.ariaRightMenu": "<PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtChartSettings": "Diagramm-Einstellungen", "PE.Views.RightMenu.txtImageSettings": "Bild-Einstellungen", "PE.Views.RightMenu.txtParagraphSettings": "Text-Ein<PERSON>llungen", "PE.Views.RightMenu.txtShapeSettings": "Form-Einstellungen", "PE.Views.RightMenu.txtSignatureSettings": "Signatureinstellungen", "PE.Views.RightMenu.txtSlideSettings": "Folien-Einstellungen", "PE.Views.RightMenu.txtTableSettings": "Tabellen-Einstellungen", "PE.Views.RightMenu.txtTextArtSettings": "TextArt-Einstellungen", "PE.Views.ShapeSettings.strBackground": "Hintergrundfarbe", "PE.Views.ShapeSettings.strChange": "Form ändern", "PE.Views.ShapeSettings.strColor": "Farbe", "PE.Views.ShapeSettings.strFill": "Füllung", "PE.Views.ShapeSettings.strForeground": "Vordergrundfarbe", "PE.Views.ShapeSettings.strPattern": "Muster", "PE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON> anzeigen", "PE.Views.ShapeSettings.strSize": "Größe", "PE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strTransparency": "Undurchsichtigkeit", "PE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textAdjustShadow": "<PERSON><PERSON><PERSON> anpassen", "PE.Views.ShapeSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen Wert zwischen 0 pt und 1584 pt ein.", "PE.Views.ShapeSettings.textColor": "Farbfüllung", "PE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textEditPoints": "<PERSON><PERSON> bearbeiten", "PE.Views.ShapeSettings.textEditShape": "Form bearbeiten", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textEyedropper": "Pipette", "PE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON> Datei", "PE.Views.ShapeSettings.textFromStorage": "aus dem Speicher", "PE.Views.ShapeSettings.textFromUrl": "Aus URL", "PE.Views.ShapeSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textGradientFill": "Füllung mit Farbverlauf", "PE.Views.ShapeSettings.textHint270": "Um 90 ° gegen den Uhrzeigersinn drehen", "PE.Views.ShapeSettings.textHint90": "90° im UZS drehen", "PE.Views.ShapeSettings.textHintFlipH": "Horizontal kippen", "PE.Views.ShapeSettings.textHintFlipV": "Vertikal kippen", "PE.Views.ShapeSettings.textImageTexture": "Bild oder Textur", "PE.Views.ShapeSettings.textLinear": "Linear", "PE.Views.ShapeSettings.textMoreColors": "Mehr Farben", "PE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textNoShadow": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textPatternFill": "Muster", "PE.Views.ShapeSettings.textPosition": "Stellung", "PE.Views.ShapeSettings.textRadial": "Radial", "PE.Views.ShapeSettings.textRecentlyUsed": "Zuletzt verwendet", "PE.Views.ShapeSettings.textRotate90": "90 Grad drehen", "PE.Views.ShapeSettings.textRotation": "Rotation", "PE.Views.ShapeSettings.textSelectImage": "Bild auswählen", "PE.Views.ShapeSettings.textSelectTexture": "Auswahl", "PE.Views.ShapeSettings.textShadow": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textStretch": "Ausdehnung", "PE.Views.ShapeSettings.textStyle": "Stil", "PE.Views.ShapeSettings.textTexture": "Aus Textur", "PE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "PE.Views.ShapeSettings.tipAddGradientPoint": "Punkt des Farbverlaufs einfügen", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Punkt des Farbverlaufs entfernen", "PE.Views.ShapeSettings.txtBrownPaper": "Kraftpapier", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON>nk<PERSON> Stoff", "PE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtGranite": "Granit", "PE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtPapyrus": "Papyrus", "PE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>z", "PE.Views.ShapeSettingsAdvanced.strColumns": "Spalten", "PE.Views.ShapeSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON> um den Text", "PE.Views.ShapeSettingsAdvanced.textAlt": "Alternativer Text", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Beschreibung", "PE.Views.ShapeSettingsAdvanced.textAltTip": "Die alternative textbasierte Darstellung der visuellen Objektinformation, die den Menschen  mit geistigen Behinderungen oder Sehbehinderungen vorgelesen wird, um besser verstehen zu können, was genau auf dem Bild, Form, Diagramm oder der Tabelle dargestellt wurde.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Titel", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Automatisch anpassen", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Startgröße", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Startlinienart", "PE.Views.ShapeSettingsAdvanced.textBevel": "Schräge Kante", "PE.Views.ShapeSettingsAdvanced.textBottom": "Unten", "PE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON>lusstyp", "PE.Views.ShapeSettingsAdvanced.textCenter": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Endgröße", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Endlinienart", "PE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Gekippt", "PE.Views.ShapeSettingsAdvanced.textFrom": "Ab", "PE.Views.ShapeSettingsAdvanced.textGeneral": "Allgemeines", "PE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontal", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Verknüpfungstyp", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Seitenverhältnis beibehalten", "PE.Views.ShapeSettingsAdvanced.textLeft": "Links", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textNofit": "Ohne automatische Anpassung", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Positionierung", "PE.Views.ShapeSettingsAdvanced.textPosition": "Position", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Die Form am Text anpassen", "PE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRotation": "Rotation", "PE.Views.ShapeSettingsAdvanced.textRound": "Rund", "PE.Views.ShapeSettingsAdvanced.textShapeName": "Name der Form", "PE.Views.ShapeSettingsAdvanced.textShrink": "Text bei Überlauf verkleinern", "PE.Views.ShapeSettingsAdvanced.textSize": "Größe", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Abstand zwischen Spalten", "PE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTextBox": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTitle": "Form - Erweiterte Einstellungen", "PE.Views.ShapeSettingsAdvanced.textTop": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "<PERSON>bere linke <PERSON>", "PE.Views.ShapeSettingsAdvanced.textVertical": "Vertikal", "PE.Views.ShapeSettingsAdvanced.textVertically": "Vertikal", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Stärken & Pfeile", "PE.Views.ShapeSettingsAdvanced.textWidth": "Breite", "PE.Views.ShapeSettingsAdvanced.txtNone": "<PERSON><PERSON>", "PE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strDelete": "Signatur entfernen", "PE.Views.SignatureSettings.strDetails": "Signaturdetails", "PE.Views.SignatureSettings.strInvalid": "Ungültige Signaturen", "PE.Views.SignatureSettings.strSign": "Signieren", "PE.Views.SignatureSettings.strSignature": "Signatur", "PE.Views.SignatureSettings.strValid": "Gültige Signaturen", "PE.Views.SignatureSettings.txtContinueEditing": "Trotzdem bearbeiten", "PE.Views.SignatureSettings.txtEditWarning": "Die Bearbeitung entfernt Signaturen aus dieser Präsentation.<br><PERSON><PERSON>cht<PERSON> Sie trotzdem fortsetzen?", "PE.Views.SignatureSettings.txtRemoveWarning": "Möchten Sie diese Signatur wirklich entfernen?<br>Dies kann nicht rückgängig gemacht werden.", "PE.Views.SignatureSettings.txtSigned": "Gültige Signaturen wurden der Präsentation hinzugefügt. Die Präsentation ist vor der Bearbeitung geschützt. ", "PE.Views.SignatureSettings.txtSignedInvalid": "Einige der digitalen Signaturen in der Präsentation sind ungültig oder konnten nicht verifiziert werden. Die Präsentation ist vor der Bearbeitung geschützt.", "PE.Views.SlideSettings.strApplyAllSlides": "Auf alle Folien anwenden", "PE.Views.SlideSettings.strBackground": "Hintergrundfarbe", "PE.Views.SlideSettings.strBackgroundGraphics": "Hintergrundgrafik anzeigen", "PE.Views.SlideSettings.strBackgroundReset": "Hintergrund zurücksetzen", "PE.Views.SlideSettings.strColor": "Farbe", "PE.Views.SlideSettings.strDateTime": "Datum und Uhrzeit anzeigen", "PE.Views.SlideSettings.strFill": "Hi<PERSON>grund", "PE.Views.SlideSettings.strForeground": "Vordergrundfarbe", "PE.Views.SlideSettings.strPattern": "Muster", "PE.Views.SlideSettings.strSlideNum": "Foliennummer anzeigen", "PE.Views.SlideSettings.strTransparency": "Undurchsichtigkeit", "PE.Views.SlideSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "Farbfüllung", "PE.Views.SlideSettings.textDirection": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON><PERSON>", "PE.Views.SlideSettings.textFromFile": "<PERSON><PERSON> Datei", "PE.Views.SlideSettings.textFromStorage": "aus dem Speicher", "PE.Views.SlideSettings.textFromUrl": "Aus URL", "PE.Views.SlideSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textGradientFill": "Füllung mit Farbverlauf", "PE.Views.SlideSettings.textImageTexture": "Bild oder Textur", "PE.Views.SlideSettings.textLinear": "Linear", "PE.Views.SlideSettings.textNoFill": "<PERSON><PERSON>", "PE.Views.SlideSettings.textPatternFill": "Muster", "PE.Views.SlideSettings.textPosition": "Stellung", "PE.Views.SlideSettings.textRadial": "Radial", "PE.Views.SlideSettings.textReset": "Änderungen zurücksetzen", "PE.Views.SlideSettings.textSelectImage": "Bild auswählen", "PE.Views.SlideSettings.textSelectTexture": "Auswahl", "PE.Views.SlideSettings.textStretch": "Ausdehnung", "PE.Views.SlideSettings.textStyle": "Stil", "PE.Views.SlideSettings.textTexture": "Aus Textur", "PE.Views.SlideSettings.textTile": "<PERSON><PERSON>", "PE.Views.SlideSettings.tipAddGradientPoint": "Punkt des Farbverlaufs einfügen", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Punkt des Farbverlaufs entfernen", "PE.Views.SlideSettings.txtBrownPaper": "Kraftpapier", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON>nk<PERSON> Stoff", "PE.Views.SlideSettings.txtGrain": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtGranite": "Granit", "PE.Views.SlideSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "Papyrus", "PE.Views.SlideSettings.txtWood": "<PERSON><PERSON>z", "PE.Views.SlideshowSettings.textLoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bis \"Esc\" ged<PERSON><PERSON>t wird", "PE.Views.SlideshowSettings.textTitle": "Einstellungen anzeigen", "PE.Views.SlideSizeSettings.strLandscape": "Querformat", "PE.Views.SlideSizeSettings.strPortrait": "Hochformat", "PE.Views.SlideSizeSettings.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textSlideOrientation": "Folienausrichtung", "PE.Views.SlideSizeSettings.textSlideSize": "Foliengröße", "PE.Views.SlideSizeSettings.textTitle": "Einstellungen der Foliengröße", "PE.Views.SlideSizeSettings.textWidth": "Breite", "PE.Views.SlideSizeSettings.txt35": "35 mm Folien", "PE.Views.SlideSizeSettings.txtA3": "A3 Blatt (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "A4 Blatt (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO) Blatt (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO) Blatt (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtLedger": "<PERSON><PERSON> (11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "Letter Paper (8.5x11 in)", "PE.Views.SlideSizeSettings.txtOverhead": "Overheadfolien", "PE.Views.SlideSizeSettings.txtSlideNum": "Folien zählen von", "PE.Views.SlideSizeSettings.txtStandard": "Standard (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Breitbild", "PE.Views.Statusbar.goToPageText": "Zu Folie übergehen", "PE.Views.Statusbar.pageIndexText": "Folie {0} von {1}", "PE.Views.Statusbar.textShowBegin": "<PERSON><PERSON> Anfang anzeigen", "PE.Views.Statusbar.textShowCurrent": "Mit der aktuellen Folie beginnen", "PE.Views.Statusbar.textShowPresenterView": "Referentenansicht", "PE.Views.Statusbar.textSlideMaster": "Folienmaster", "PE.Views.Statusbar.tipAccessRights": "Dokumentzugriffsrechte verwalten", "PE.Views.Statusbar.tipFitPage": "<PERSON>olie an<PERSON>en", "PE.Views.Statusbar.tipFitWidth": "<PERSON><PERSON><PERSON> an<PERSON>en", "PE.Views.Statusbar.tipPreview": "Vorschau starten", "PE.Views.Statusbar.tipSetLang": "Textsprache wählen", "PE.Views.Statusbar.tipZoomFactor": "Zoommodus", "PE.Views.Statusbar.tipZoomIn": "Vergrößern", "PE.Views.Statusbar.tipZoomOut": "Verkleinern", "PE.Views.Statusbar.txtPageNumInvalid": "Ungültige Nummer der Folie", "PE.Views.TableSettings.deleteColumnText": "Spalte löschen", "PE.Views.TableSettings.deleteRowText": "Zeile löschen", "PE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON> l<PERSON>", "PE.Views.TableSettings.insertColumnLeftText": "Spalte links einfügen", "PE.Views.TableSettings.insertColumnRightText": "Spalte rechts einfügen", "PE.Views.TableSettings.insertRowAboveText": "Zeile oberhalb einfügen", "PE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON><PERSON> unterhalb einfügen", "PE.Views.TableSettings.mergeCellsText": "<PERSON><PERSON><PERSON> verbinden", "PE.Views.TableSettings.selectCellText": "<PERSON><PERSON> au<PERSON>wählen", "PE.Views.TableSettings.selectColumnText": "Spalte auswählen", "PE.Views.TableSettings.selectRowText": "Zeile auswählen", "PE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON> auswählen", "PE.Views.TableSettings.splitCellsText": "<PERSON><PERSON> te<PERSON>...", "PE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON> te<PERSON>n", "PE.Views.TableSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "PE.Views.TableSettings.textBackColor": "Hintergrundfarbe", "PE.Views.TableSettings.textBanded": "Gestreift", "PE.Views.TableSettings.textBorderColor": "Farbe", "PE.Views.TableSettings.textBorders": "<PERSON>il des Rahmens", "PE.Views.TableSettings.textCellSize": "Zellengröße", "PE.Views.TableSettings.textColumns": "Spalten", "PE.Views.TableSettings.textDistributeCols": "Spalten verteilen", "PE.Views.TableSettings.textDistributeRows": "<PERSON><PERSON><PERSON> verteilen", "PE.Views.TableSettings.textEdit": "Zeilen & Spalten", "PE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON>", "PE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeader": "Kopfzeile", "PE.Views.TableSettings.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textLast": "Letzte", "PE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON> Sie die Rahmenlinien, auf die ein anderer Stil angewandt wird", "PE.Views.TableSettings.textTemplate": "Vorlage auswählen", "PE.Views.TableSettings.textTotal": "Insgesamt", "PE.Views.TableSettings.textWidth": "Breite", "PE.Views.TableSettings.tipAll": "Äußere Rahmenlinie und alle inneren Linien festlegen", "PE.Views.TableSettings.tipBottom": "Nur äußere untere Rahmenlinie festlegen", "PE.Views.TableSettings.tipInner": "Nur innere Linien festlegen", "PE.Views.TableSettings.tipInnerHor": "Nur innere horizontale Linien festlegen", "PE.Views.TableSettings.tipInnerVert": "Nur vertikale innere Linien festlegen", "PE.Views.TableSettings.tipLeft": "Nur äußere linke Rahmenlinie festlegen", "PE.Views.TableSettings.tipNone": "<PERSON><PERSON> festlegen", "PE.Views.TableSettings.tipOuter": "Nur äußere Rahmenlinie festlegen", "PE.Views.TableSettings.tipRight": "Nur äußere rechte Rahmenlinie festlegen", "PE.Views.TableSettings.tipTop": "Nur äußere obere Rahmenlinie festlegen", "PE.Views.TableSettings.txtGroupTable_Custom": "Einstellbar", "PE.Views.TableSettings.txtGroupTable_Dark": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Light": "Hell", "PE.Views.TableSettings.txtGroupTable_Medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Optimal": "Beste Suchergebnisse für Dokument", "PE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON>", "PE.Views.TableSettings.txtTable_Accent": "Akzent", "PE.Views.TableSettings.txtTable_DarkStyle": "Dunkle Formatvorlage", "PE.Views.TableSettings.txtTable_LightStyle": "Helle Format<PERSON>", "PE.Views.TableSettings.txtTable_MediumStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_NoGrid": "<PERSON><PERSON>", "PE.Views.TableSettings.txtTable_NoStyle": "<PERSON><PERSON>", "PE.Views.TableSettings.txtTable_TableGrid": "Ta<PERSON>en<PERSON><PERSON>", "PE.Views.TableSettings.txtTable_ThemedStyle": "Designformatvorlage", "PE.Views.TableSettingsAdvanced.textAlt": "Alternativer Text", "PE.Views.TableSettingsAdvanced.textAltDescription": "Beschreibung", "PE.Views.TableSettingsAdvanced.textAltTip": "Die alternative textbasierte Darstellung der visuellen Objektinformation, die den Menschen  mit geistigen Behinderungen oder Sehbehinderungen vorgelesen wird, um besser verstehen zu können, was genau auf dem Bild, Form, Diagramm oder der Tabelle dargestellt wurde.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Titel", "PE.Views.TableSettingsAdvanced.textBottom": "Unten", "PE.Views.TableSettingsAdvanced.textCenter": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textCheckMargins": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textFrom": "Ab", "PE.Views.TableSettingsAdvanced.textGeneral": "Allgemeines", "PE.Views.TableSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Seitenverhältnis beibehalten", "PE.Views.TableSettingsAdvanced.textLeft": "Links", "PE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textPlacement": "Positionierung", "PE.Views.TableSettingsAdvanced.textPosition": "Position", "PE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textSize": "Größe", "PE.Views.TableSettingsAdvanced.textTableName": "Tabellenname", "PE.Views.TableSettingsAdvanced.textTitle": "Tabelle - Erweiterte Einstellungen", "PE.Views.TableSettingsAdvanced.textTop": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "<PERSON>bere linke <PERSON>", "PE.Views.TableSettingsAdvanced.textVertical": "Vertikal", "PE.Views.TableSettingsAdvanced.textWidth": "Breite", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strBackground": "Hintergrundfarbe", "PE.Views.TextArtSettings.strColor": "Farbe", "PE.Views.TextArtSettings.strFill": "Füllung", "PE.Views.TextArtSettings.strForeground": "Vordergrundfarbe", "PE.Views.TextArtSettings.strPattern": "Muster", "PE.Views.TextArtSettings.strSize": "Größe", "PE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "Undurchsichtigkeit", "PE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen Wert zwischen 0 pt und 1584 pt ein.", "PE.Views.TextArtSettings.textColor": "Farbfüllung", "PE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON> Datei", "PE.Views.TextArtSettings.textFromUrl": "Aus URL", "PE.Views.TextArtSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textGradientFill": "Füllung mit Farbverlauf", "PE.Views.TextArtSettings.textImageTexture": "Bild oder Textur", "PE.Views.TextArtSettings.textLinear": "Linear", "PE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textPatternFill": "Muster", "PE.Views.TextArtSettings.textPosition": "Stellung", "PE.Views.TextArtSettings.textRadial": "Radial", "PE.Views.TextArtSettings.textSelectTexture": "Auswahl", "PE.Views.TextArtSettings.textStretch": "Ausdehnung", "PE.Views.TextArtSettings.textStyle": "Stil", "PE.Views.TextArtSettings.textTemplate": "Vorlage", "PE.Views.TextArtSettings.textTexture": "Aus Textur", "PE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTransform": "Transformieren", "PE.Views.TextArtSettings.tipAddGradientPoint": "Punkt des Farbverlaufs einfügen", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Punkt des Farbverlaufs entfernen", "PE.Views.TextArtSettings.txtBrownPaper": "Kraftpapier", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON>nk<PERSON> Stoff", "PE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtGranite": "Granite", "PE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtPapyrus": "Papyrus", "PE.Views.TextArtSettings.txtWood": "<PERSON><PERSON>z", "PE.Views.Toolbar.capAddLayout": "Layout hinzufügen", "PE.Views.Toolbar.capAddSlide": "Folie hinzufügen", "PE.Views.Toolbar.capAddSlideMaster": "Folienmaster hi<PERSON>uf<PERSON>gen", "PE.Views.Toolbar.capBtnAddComment": "Kommentar Hinzufügen", "PE.Views.Toolbar.capBtnComment": "Kommentar", "PE.Views.Toolbar.capBtnDateTime": "Datum & Uhrzeit", "PE.Views.Toolbar.capBtnInsHeaderFooter": "Kopf- und Fußzeile", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "Symbol", "PE.Views.Toolbar.capBtnSlideNum": "Foliennummer", "PE.Views.Toolbar.capCloseMaster": "Master s<PERSON><PERSON><PERSON>n", "PE.Views.Toolbar.capInsertAudio": "Audio", "PE.Views.Toolbar.capInsertChart": "Diagramm", "PE.Views.Toolbar.capInsertEquation": "Gleichung", "PE.Views.Toolbar.capInsertHyperlink": "Hyperlink", "PE.Views.Toolbar.capInsertImage": "Bild", "PE.Views.Toolbar.capInsertPlaceholder": "Platzhalter einfügen", "PE.Views.Toolbar.capInsertShape": "Form", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertText": "<PERSON><PERSON>", "PE.Views.Toolbar.capInsertTextArt": "Text Art", "PE.Views.Toolbar.capInsertVideo": "Video", "PE.Views.Toolbar.capTabFile": "<PERSON><PERSON>", "PE.Views.Toolbar.capTabHome": "Startseite", "PE.Views.Toolbar.capTabInsert": "Einfügen", "PE.Views.Toolbar.mniCapitalizeWords": "<PERSON><PERSON> im Wort großschreiben", "PE.Views.Toolbar.mniCustomTable": "Benutzerdefinierte Tabelle einfügen", "PE.Views.Toolbar.mniImageFromFile": "Bild aus Datei", "PE.Views.Toolbar.mniImageFromStorage": "Bild aus dem Speicher", "PE.Views.Toolbar.mniImageFromUrl": "Bild aus URL", "PE.Views.Toolbar.mniInsertSSE": "<PERSON>bell<PERSON> e<PERSON>fügen", "PE.Views.Toolbar.mniLowerCase": "Kleinbuchstaben", "PE.Views.Toolbar.mniSentenceCase": "<PERSON><PERSON> im Satz großschreiben.", "PE.Views.Toolbar.mniSlideAdvanced": "Erweiterte Einstellungen", "PE.Views.Toolbar.mniSlideStandard": "Standard (4:3)", "PE.Views.Toolbar.mniSlideWide": "Breitbildschirm (16:9)", "PE.Views.Toolbar.mniToggleCase": "gROSS-/kLEINSCHREIBUNG", "PE.Views.Toolbar.mniUpperCase": "GROSSBUCHSTABEN", "PE.Views.Toolbar.strMenuNoFill": "<PERSON><PERSON>", "PE.Views.Toolbar.textAlignBottom": "Text am unteren Rand ausrichten", "PE.Views.Toolbar.textAlignCenter": "Text zentrieren", "PE.Views.Toolbar.textAlignJust": "Im Blocksatz ausrichten", "PE.Views.Toolbar.textAlignLeft": "Text linksbündig ausrichten", "PE.Views.Toolbar.textAlignMiddle": "Text mittig ausrichten", "PE.Views.Toolbar.textAlignRight": "Text rechtsbündig ausrichten", "PE.Views.Toolbar.textAlignTop": "Text am oberen Rand ausrichten", "PE.Views.Toolbar.textAlpha": "Griechischer Kleinbuchstabe Alpha", "PE.Views.Toolbar.textArrangeBack": "In den Hintergrund senden", "PE.Views.Toolbar.textArrangeBackward": "Nach hinten senden", "PE.Views.Toolbar.textArrangeForward": "Vorwärts bringen", "PE.Views.Toolbar.textArrangeFront": "In den Vordergrund bringen", "PE.Views.Toolbar.textBetta": "Griechischer Kleinbuchstabe Beta", "PE.Views.Toolbar.textBlackHeart": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textBold": "<PERSON><PERSON>", "PE.Views.Toolbar.textBullet": "Aufzählungszeichen", "PE.Views.Toolbar.textChart": "Diagramm", "PE.Views.Toolbar.textColumnsCustom": "Benutzerdefinierte Spalten", "PE.Views.Toolbar.textColumnsOne": "<PERSON><PERSON> Spalte", "PE.Views.Toolbar.textColumnsThree": "Drei <PERSON>", "PE.Views.Toolbar.textColumnsTwo": "Zwei Spalten", "PE.Views.Toolbar.textContent": "Inhalt", "PE.Views.Toolbar.textContentVertical": "Inhalt (vertikal)", "PE.Views.Toolbar.textCopyright": "Copyrightzeichen", "PE.Views.Toolbar.textDegree": "Gradzeichen", "PE.Views.Toolbar.textDelta": "Griechischer Kleinbuchstabe Delta", "PE.Views.Toolbar.textDivision": "Divisionszeichen", "PE.Views.Toolbar.textDollar": "Dollarzeichen", "PE.Views.Toolbar.textEuro": "Eurozeichen", "PE.Views.Toolbar.textFooters": "Fußzeilen", "PE.Views.Toolbar.textGreaterEqual": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder gleich wie ", "PE.Views.Toolbar.textInfinity": "Unendlichkeit", "PE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textLessEqual": "<PERSON>er als oder gleich", "PE.Views.Toolbar.textLetterPi": "Griechischer Kleinbuchstabe Pi", "PE.Views.Toolbar.textLineSpaceOptions": "Optionen zum Zeilenabstand", "PE.Views.Toolbar.textListSettings": "Listeneinstellungen", "PE.Views.Toolbar.textMoreSymbols": "Mehr Symbole", "PE.Views.Toolbar.textNotEqualTo": "<PERSON><PERSON> gleich", "PE.Views.Toolbar.textOneHalf": "Vulgäre Fraktion Eine Hälfte", "PE.Views.Toolbar.textOneQuarter": "Vulgäre Fraktion Ganz", "PE.Views.Toolbar.textPicture": "Bild", "PE.Views.Toolbar.textPlusMinus": "Plus-Minus<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textRecentlyUsed": "Zuletzt verwendet", "PE.Views.Toolbar.textRegistered": "Registrierte Handelsmarke", "PE.Views.Toolbar.textSection": "Paragraphenzeichen", "PE.Views.Toolbar.textShapeAlignBottom": "Unten ausrichten", "PE.Views.Toolbar.textShapeAlignCenter": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignLeft": "<PERSON><PERSON> au<PERSON>", "PE.Views.Toolbar.textShapeAlignMiddle": "<PERSON><PERSON><PERSON> au<PERSON>", "PE.Views.Toolbar.textShapeAlignRight": "Rechts ausrichten", "PE.Views.Toolbar.textShapeAlignTop": "<PERSON><PERSON> aus<PERSON>", "PE.Views.Toolbar.textShapesCombine": "Kombinieren", "PE.Views.Toolbar.textShapesFragment": "Fragment", "PE.Views.Toolbar.textShapesIntersect": "Schneiden", "PE.Views.Toolbar.textShapesSubstract": "Subtrahieren", "PE.Views.Toolbar.textShapesUnion": "Vereinigung", "PE.Views.Toolbar.textShowBegin": " <PERSON> anschauen", "PE.Views.Toolbar.textShowCurrent": "Von aktueller Folie abschauen", "PE.Views.Toolbar.textShowPresenterView": "Referentenansicht", "PE.Views.Toolbar.textShowSettings": "Einstellungen anzeigen", "PE.Views.Toolbar.textSmartArt": "SmartArt", "PE.Views.Toolbar.textSmile": "Weißes Lachendes Gesicht", "PE.Views.Toolbar.textSquareRoot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textStrikeout": "Durchgestrichen", "PE.Views.Toolbar.textSubscript": "Tiefgestellt", "PE.Views.Toolbar.textSuperscript": "Hochgestellt", "PE.Views.Toolbar.textTabAnimation": "Animation", "PE.Views.Toolbar.textTabCollaboration": "Zusammenarbeit", "PE.Views.Toolbar.textTabDesign": "Design", "PE.Views.Toolbar.textTabDraw": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabFile": "<PERSON><PERSON>", "PE.Views.Toolbar.textTabHome": "Startseite", "PE.Views.Toolbar.textTabInsert": "Einfügen", "PE.Views.Toolbar.textTable": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabProtect": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabTransitions": "Übergänge", "PE.Views.Toolbar.textTabView": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textText": "Text", "PE.Views.Toolbar.textTextVertical": "Text (vertikal)", "PE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "PE.Views.Toolbar.textTitle": "Titel", "PE.Views.Toolbar.textTitleError": "<PERSON><PERSON>", "PE.Views.Toolbar.textTradeMark": "Markenzeichen", "PE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textYen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipAddLayout": "Layout hinzufügen", "PE.Views.Toolbar.tipAddSlide": "Folie hinzufügen", "PE.Views.Toolbar.tipAddSlideMaster": "Folienmaster hi<PERSON>uf<PERSON>gen", "PE.Views.Toolbar.tipBack": "Zurück", "PE.Views.Toolbar.tipChangeCase": "Groß-/Kleinschreibung", "PE.Views.Toolbar.tipChangeChart": "Diagrammtyp ändern", "PE.Views.Toolbar.tipChangeSlide": "Folienlayout ändern", "PE.Views.Toolbar.tipClearStyle": "Formatierung löschen", "PE.Views.Toolbar.tipCloseMaster": "Master s<PERSON><PERSON><PERSON>n", "PE.Views.Toolbar.tipColorSchemas": "Farbschema ändern", "PE.Views.Toolbar.tipColumns": "Spalten einfügen", "PE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipCopyStyle": "Format übertragen", "PE.Views.Toolbar.tipCut": "Ausschneiden", "PE.Views.Toolbar.tipDateTime": "Das aktuelle Datum und die aktuelle Uhrzeit einfügen ", "PE.Views.Toolbar.tipDecFont": "Schriftgrad verkleinern", "PE.Views.Toolbar.tipDecPrLeft": "Einzug verkleinern", "PE.Views.Toolbar.tipEditHeaderFooter": "Kopf- oder Fußzeile bearbeiten", "PE.Views.Toolbar.tipFontColor": "Schriftfarbe", "PE.Views.Toolbar.tipFontName": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipFontSize": "Schriftgrad", "PE.Views.Toolbar.tipHAligh": "Horizontale Ausrichtung", "PE.Views.Toolbar.tipHighlightColor": "Hervorhebungsfarbe", "PE.Views.Toolbar.tipIncFont": "Schriftgrad vergrößern", "PE.Views.Toolbar.tipIncPrLeft": "Einzug vergrößern", "PE.Views.Toolbar.tipInsertAudio": "Audio einfügen", "PE.Views.Toolbar.tipInsertChart": "Diagramm einfügen", "PE.Views.Toolbar.tipInsertChartPlaceholder": "Platzhalter für Diagramm einfügen", "PE.Views.Toolbar.tipInsertContentPlaceholder": "Platzhalter für Inhalt einfügen", "PE.Views.Toolbar.tipInsertContentVerticalPlaceholder": "Platzhalter für Inhalt (vertikal) einfügen", "PE.Views.Toolbar.tipInsertEquation": "Formel einfügen", "PE.Views.Toolbar.tipInsertHorizontalText": "Horizontales Textfeld einfügen", "PE.Views.Toolbar.tipInsertHyperlink": "Hyperlink hinzufügen", "PE.Views.Toolbar.tipInsertImage": "Bild einfügen", "PE.Views.Toolbar.tipInsertPicturePlaceholder": "Bildplatzhalter einfügen", "PE.Views.Toolbar.tipInsertShape": "Form einfügen", "PE.Views.Toolbar.tipInsertSmartArt": "SmartArt einfügen", "PE.Views.Toolbar.tipInsertSmartArtPlaceholder": "SmartArt-Platzhalter einfügen", "PE.Views.Toolbar.tipInsertSymbol": "Symbol einfügen", "PE.Views.Toolbar.tipInsertTable": "<PERSON>bell<PERSON> e<PERSON>fügen", "PE.Views.Toolbar.tipInsertTablePlaceholder": "Tabellenplatzhalter einfügen", "PE.Views.Toolbar.tipInsertText": "Textfeld einfügen", "PE.Views.Toolbar.tipInsertTextArt": "TextArt einfügen", "PE.Views.Toolbar.tipInsertTextPlaceholder": "Textplatzhalter einfügen", "PE.Views.Toolbar.tipInsertTextVerticalPlaceholder": "Textplatzhalter (vertikal) einfügen", "PE.Views.Toolbar.tipInsertVerticalText": "Vertikales Textfeld einfügen", "PE.Views.Toolbar.tipInsertVideo": "Video einfügen", "PE.Views.Toolbar.tipLineSpace": "Zeilenabstand", "PE.Views.Toolbar.tipMarkers": "Aufzählung", "PE.Views.Toolbar.tipMarkersArrow": "Pfeilförmige Aufzählungszeichen", "PE.Views.Toolbar.tipMarkersCheckmark": "Häkchenaufzählungszeichen", "PE.Views.Toolbar.tipMarkersDash": "Aufzählungszeichen", "PE.Views.Toolbar.tipMarkersFRhombus": "Ausgefüllte karoförmige Aufzählungszeichen", "PE.Views.Toolbar.tipMarkersFRound": "Ausgefüllte runde Aufzählungszeichen", "PE.Views.Toolbar.tipMarkersFSquare": "Ausgefüllte quadratische Aufzählungszeichen", "PE.Views.Toolbar.tipMarkersHRound": "<PERSON><PERSON> runde Aufzählungszeichen", "PE.Views.Toolbar.tipMarkersStar": "Sternförmige Aufzählungszeichen", "PE.Views.Toolbar.tipNone": "<PERSON><PERSON>", "PE.Views.Toolbar.tipNumbers": "Nummerierung", "PE.Views.Toolbar.tipPaste": "Einfügen", "PE.Views.Toolbar.tipPreview": "Vorschau starten", "PE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipPrintQuick": "Schnelldruck", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipReplace": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSave": "Speichern", "PE.Views.Toolbar.tipSaveCoauth": "Speichern Sie die Änderungen, damit die anderen Benutzer sie sehen können.", "PE.Views.Toolbar.tipSelectAll": "Alles auswählen", "PE.Views.Toolbar.tipShapeAlign": "Form ausrichten", "PE.Views.Toolbar.tipShapeArrange": "Form anordnen", "PE.Views.Toolbar.tipShapesMerge": "Formen zusammenführen", "PE.Views.Toolbar.tipSlideNum": "Foliennummer einfügen", "PE.Views.Toolbar.tipSlideSize": "Foliengröße wählen", "PE.Views.Toolbar.tipSlideTheme": "Foliendesign", "PE.Views.Toolbar.tipUndo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "PE.Views.Toolbar.tipVAligh": "Vertikal ausrichten", "PE.Views.Toolbar.tipViewSettings": "Ansichts-Einstellungen", "PE.Views.Toolbar.txtColors": "<PERSON><PERSON>", "PE.Views.Toolbar.txtDistribHor": "Horizontal verteilen", "PE.Views.Toolbar.txtDistribVert": "Vertikal verteilen", "PE.Views.Toolbar.txtDuplicateSlide": "Folie duplizieren", "PE.Views.Toolbar.txtGroup": "Gruppieren", "PE.Views.Toolbar.txtObjectsAlign": "Ausgewählte Objekte ausrichten", "PE.Views.Toolbar.txtSlideAlign": "An Folie ausrichten", "PE.Views.Toolbar.txtSlideSize": "Foliengröße", "PE.Views.Toolbar.txtUngroup": "Gruppierung aufheben", "PE.Views.Transitions.strDelay": "Verzögern", "PE.Views.Transitions.strDuration": "<PERSON><PERSON>", "PE.Views.Transitions.strStartOnClick": "<PERSON><PERSON> Klicken beginnen", "PE.Views.Transitions.textBlack": "<PERSON><PERSON>", "PE.Views.Transitions.textBottom": "<PERSON>", "PE.Views.Transitions.textBottomLeft": "<PERSON> links", "PE.Views.Transitions.textBottomRight": "<PERSON> rechts", "PE.Views.Transitions.textClock": "<PERSON><PERSON>", "PE.Views.Transitions.textClockwise": "<PERSON><PERSON>", "PE.Views.Transitions.textCounterclockwise": "Gegen Uhrzeigersinn", "PE.Views.Transitions.textCover": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textFade": "Einblendung", "PE.Views.Transitions.textHorizontalIn": "Horizontal nach innen", "PE.Views.Transitions.textHorizontalOut": "Horizontal nach außen", "PE.Views.Transitions.textLeft": "<PERSON>", "PE.Views.Transitions.textMorph": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textMorphLetters": "Buchstaben", "PE.Views.Transitions.textMorphObjects": "Objekte", "PE.Views.Transitions.textMorphWord": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textNone": "Kein(e)", "PE.Views.Transitions.textPush": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textRandom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textRight": "<PERSON>", "PE.Views.Transitions.textSmoothly": "Gleitend", "PE.Views.Transitions.textSplit": "Aufteilen", "PE.Views.Transitions.textTop": "<PERSON>", "PE.Views.Transitions.textTopLeft": "Von <PERSON> links", "PE.Views.Transitions.textTopRight": "<PERSON> rechts", "PE.Views.Transitions.textUnCover": "Aufdecken", "PE.Views.Transitions.textVerticalIn": "Vertikal nach innen", "PE.Views.Transitions.textVerticalOut": "Vertikal nach außen", "PE.Views.Transitions.textWedge": "Keil", "PE.Views.Transitions.textWipe": "Wischblende", "PE.Views.Transitions.textZoom": "Vergrößern", "PE.Views.Transitions.textZoomIn": "Vergrößern", "PE.Views.Transitions.textZoomOut": "Verkleinern", "PE.Views.Transitions.textZoomRotate": "Vergrößern und drehen", "PE.Views.Transitions.txtApplyToAll": "Auf alle Folien anwenden", "PE.Views.Transitions.txtParameters": "Parameter", "PE.Views.Transitions.txtPreview": "Vorschau", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.capBtnHand": "Hand", "PE.Views.ViewTab.capBtnSelect": "Auswählen", "PE.Views.ViewTab.textAddHGuides": "Horizontale Führungslinie hinzufügen", "PE.Views.ViewTab.textAddVGuides": "Vertikale Führungslinie hinzufügen", "PE.Views.ViewTab.textAlwaysShowToolbar": "Symbolleiste immer anzeigen", "PE.Views.ViewTab.textClearGuides": "Führungslinien löschen", "PE.Views.ViewTab.textCm": "cm", "PE.Views.ViewTab.textCustom": "Einstellbar", "PE.Views.ViewTab.textFill": "Füllung", "PE.Views.ViewTab.textFitToSlide": "An Folie anpassen", "PE.Views.ViewTab.textFitToWidth": "An Breite anpassen", "PE.Views.ViewTab.textGridlines": "Gitternetzlinien ", "PE.Views.ViewTab.textGuides": "Führungslinien", "PE.Views.ViewTab.textInterfaceTheme": "Thema der Benutzeroberfläche", "PE.Views.ViewTab.textLeftMenu": "<PERSON><PERSON>", "PE.Views.ViewTab.textLine": "<PERSON><PERSON>", "PE.Views.ViewTab.textMacros": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textNormal": "Normal", "PE.Views.ViewTab.textNotes": "Notizen", "PE.Views.ViewTab.textRightMenu": "Rechtes Bedienungsfeld ", "PE.Views.ViewTab.textRulers": "Lineale", "PE.Views.ViewTab.textShowGridlines": "Gitternetzlinien anzeigen", "PE.Views.ViewTab.textShowGuides": "Führungslinien anzeigen", "PE.Views.ViewTab.textSlideMaster": "Folienmaster", "PE.Views.ViewTab.textSmartGuides": "Intelligente Führungslinien", "PE.Views.ViewTab.textSnapObjects": "Objekt an das Raster binden", "PE.Views.ViewTab.textStatusBar": "Statusleiste", "PE.Views.ViewTab.textTabStyle": "<PERSON><PERSON> der <PERSON>", "PE.Views.ViewTab.textZoom": "Zoom", "PE.Views.ViewTab.tipFitToSlide": "An Folie anpassen", "PE.Views.ViewTab.tipFitToWidth": "An Breite anpassen", "PE.Views.ViewTab.tipGridlines": "Gitternetzlinien anzeigen", "PE.Views.ViewTab.tipGuides": "Führungslinien anzeigen", "PE.Views.ViewTab.tipHandTool": "Hand-Tool", "PE.Views.ViewTab.tipInterfaceTheme": "Thema der Benutzeroberfläche", "PE.Views.ViewTab.tipMacros": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.tipNormal": "Normal", "PE.Views.ViewTab.tipSelectTool": "Auswählungstool", "PE.Views.ViewTab.tipSlideMaster": "Folienmaster"}