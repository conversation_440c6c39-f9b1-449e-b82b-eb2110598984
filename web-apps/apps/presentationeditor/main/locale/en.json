{"Common.Controllers.Chat.notcriticalErrorTitle": "Warning", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Anonymous", "Common.Controllers.ExternalDiagramEditor.textClose": "Close", "Common.Controllers.ExternalDiagramEditor.warningText": "The object is disabled because it is being edited by another user.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Warning", "Common.Controllers.ExternalOleEditor.textAnonymous": "Anonymous", "Common.Controllers.ExternalOleEditor.textClose": "Close", "Common.Controllers.ExternalOleEditor.warningText": "The object is disabled because it is being edited by another user.", "Common.Controllers.ExternalOleEditor.warningTitle": "Warning", "Common.Controllers.History.notcriticalErrorTitle": "Warning", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "Area", "Common.define.chartData.textAreaStacked": "Stacked area", "Common.define.chartData.textAreaStackedPer": "100% Stacked area", "Common.define.chartData.textBar": "Bar", "Common.define.chartData.textBarNormal": "Clustered column", "Common.define.chartData.textBarNormal3d": "3-D Clustered column", "Common.define.chartData.textBarNormal3dPerspective": "3-D column", "Common.define.chartData.textBarStacked": "Stacked column", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Stacked column", "Common.define.chartData.textBarStackedPer": "100% Stacked column", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Stacked column", "Common.define.chartData.textCharts": "Charts", "Common.define.chartData.textColumn": "Column", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Stacked area - clustered column", "Common.define.chartData.textComboBarLine": "Clustered column - line", "Common.define.chartData.textComboBarLineSecondary": "Clustered column - line on secondary axis", "Common.define.chartData.textComboCustom": "Custom combination", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "Clustered bar", "Common.define.chartData.textHBarNormal3d": "3-D Clustered bar", "Common.define.chartData.textHBarStacked": "Stacked bar", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Stacked bar", "Common.define.chartData.textHBarStackedPer": "100% Stacked bar", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Stacked bar", "Common.define.chartData.textLine": "Line", "Common.define.chartData.textLine3d": "3-D line", "Common.define.chartData.textLineMarker": "Line with markers", "Common.define.chartData.textLineStacked": "Stacked line", "Common.define.chartData.textLineStackedMarker": "Stacked line with markers", "Common.define.chartData.textLineStackedPer": "100% Stacked line", "Common.define.chartData.textLineStackedPerMarker": "100% Stacked line with markers", "Common.define.chartData.textPie": "Pie", "Common.define.chartData.textPie3d": "3-D pie", "Common.define.chartData.textPoint": "XY (<PERSON><PERSON><PERSON>)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Scatter with straight lines", "Common.define.chartData.textScatterLineMarker": "Scatter with straight lines and markers", "Common.define.chartData.textScatterSmooth": "Scatter with smooth lines", "Common.define.chartData.textScatterSmoothMarker": "Scatter with smooth lines and markers", "Common.define.chartData.textStock": "Stock", "Common.define.chartData.textSurface": "Surface", "Common.define.effectData.textAcross": "Across", "Common.define.effectData.textAppear": "Appear", "Common.define.effectData.textArcDown": "Arc Down", "Common.define.effectData.textArcLeft": "Arc Left", "Common.define.effectData.textArcRight": "Arc Right", "Common.define.effectData.textArcs": "Arcs", "Common.define.effectData.textArcUp": "Arc Up", "Common.define.effectData.textBasic": "Basic", "Common.define.effectData.textBasicSwivel": "Basic swivel", "Common.define.effectData.textBasicZoom": "Basic Zoom", "Common.define.effectData.textBean": "<PERSON>", "Common.define.effectData.textBlinds": "Blinds", "Common.define.effectData.textBlink": "Blink", "Common.define.effectData.textBoldFlash": "Bold Flash", "Common.define.effectData.textBoldReveal": "Bold Reveal", "Common.define.effectData.textBoomerang": "Boomerang", "Common.define.effectData.textBounce": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounceLeft": "<PERSON><PERSON><PERSON> Left", "Common.define.effectData.textBounceRight": "Bounce Right", "Common.define.effectData.textBox": "Box", "Common.define.effectData.textBrushColor": "Brush color", "Common.define.effectData.textCenterRevolve": "Center revolve", "Common.define.effectData.textCheckerboard": "Checkerboard", "Common.define.effectData.textCircle": "Circle", "Common.define.effectData.textCollapse": "Collapse", "Common.define.effectData.textColorPulse": "Color pulse", "Common.define.effectData.textComplementaryColor": "Complementary color", "Common.define.effectData.textComplementaryColor2": "Complementary color 2", "Common.define.effectData.textCompress": "Compress", "Common.define.effectData.textContrast": "Contrast", "Common.define.effectData.textContrastingColor": "Contrasting color", "Common.define.effectData.textCredits": "Credits", "Common.define.effectData.textCrescentMoon": "Crescent Moon", "Common.define.effectData.textCurveDown": "Curve Down", "Common.define.effectData.textCurvedSquare": "Curved square", "Common.define.effectData.textCurvedX": "Curved X", "Common.define.effectData.textCurvyLeft": "<PERSON>urvy Left", "Common.define.effectData.textCurvyRight": "Curvy Right", "Common.define.effectData.textCurvyStar": "Curvy Star", "Common.define.effectData.textCustomPath": "Custom Path", "Common.define.effectData.textCuverUp": "Curve up", "Common.define.effectData.textDarken": "Darken", "Common.define.effectData.textDecayingWave": "Decaying wave", "Common.define.effectData.textDesaturate": "Desaturate", "Common.define.effectData.textDiagonalDownRight": "Diagonal down right", "Common.define.effectData.textDiagonalUpRight": "Diagonal up right", "Common.define.effectData.textDiamond": "Diamond", "Common.define.effectData.textDisappear": "Disappear", "Common.define.effectData.textDissolveIn": "Dissolve In", "Common.define.effectData.textDissolveOut": "Dissolve Out", "Common.define.effectData.textDown": "Down", "Common.define.effectData.textDrop": "Drop", "Common.define.effectData.textEmphasis": "Emphasis Effects", "Common.define.effectData.textEntrance": "Entrance Effects", "Common.define.effectData.textEqualTriangle": "Equal triangle", "Common.define.effectData.textExciting": "Exciting", "Common.define.effectData.textExit": "Exit effects", "Common.define.effectData.textExpand": "Expand", "Common.define.effectData.textFade": "Fade", "Common.define.effectData.textFigureFour": "Figure 8 Four", "Common.define.effectData.textFillColor": "Fill color", "Common.define.effectData.textFlip": "Flip", "Common.define.effectData.textFloat": "Float", "Common.define.effectData.textFloatDown": "Float down", "Common.define.effectData.textFloatIn": "Float In", "Common.define.effectData.textFloatOut": "Float Out", "Common.define.effectData.textFloatUp": "Float Up", "Common.define.effectData.textFlyIn": "Fly In", "Common.define.effectData.textFlyOut": "Fly Out", "Common.define.effectData.textFontColor": "Font Color", "Common.define.effectData.textFootball": "Football", "Common.define.effectData.textFromBottom": "From bottom", "Common.define.effectData.textFromBottomLeft": "From bottom-left", "Common.define.effectData.textFromBottomRight": "From bottom-right", "Common.define.effectData.textFromLeft": "From left", "Common.define.effectData.textFromRight": "From right", "Common.define.effectData.textFromTop": "From top", "Common.define.effectData.textFromTopLeft": "From top-left", "Common.define.effectData.textFromTopRight": "From top-right", "Common.define.effectData.textFunnel": "Funnel", "Common.define.effectData.textGrowShrink": "Grow/Shrink", "Common.define.effectData.textGrowTurn": "Grow & Turn", "Common.define.effectData.textGrowWithColor": "Grow with color", "Common.define.effectData.textHeart": "Heart", "Common.define.effectData.textHeartbeat": "Heartbeat", "Common.define.effectData.textHexagon": "Hexagon", "Common.define.effectData.textHorizontal": "Horizontal", "Common.define.effectData.textHorizontalFigure": "Horizontal Figure 8", "Common.define.effectData.textHorizontalIn": "Horizontal In", "Common.define.effectData.textHorizontalOut": "Horizontal Out", "Common.define.effectData.textIn": "In", "Common.define.effectData.textInFromScreenCenter": "In from screen center", "Common.define.effectData.textInSlightly": "In slightly", "Common.define.effectData.textInToScreenBottom": "In to screen bottom", "Common.define.effectData.textInvertedSquare": "Inverted square", "Common.define.effectData.textInvertedTriangle": "Inverted triangle", "Common.define.effectData.textLeft": "Left", "Common.define.effectData.textLeftDown": " Left down", "Common.define.effectData.textLeftUp": " Left up", "Common.define.effectData.textLighten": "Lighten", "Common.define.effectData.textLineColor": "Line color", "Common.define.effectData.textLines": "Lines", "Common.define.effectData.textLinesCurves": "Lines curves", "Common.define.effectData.textLoopDeLoop": "Loop de Loop", "Common.define.effectData.textLoops": "Loops", "Common.define.effectData.textModerate": "Moderate", "Common.define.effectData.textNeutron": "Neutron", "Common.define.effectData.textObjectCenter": "Object center", "Common.define.effectData.textObjectColor": "Object color", "Common.define.effectData.textOctagon": "Octagon", "Common.define.effectData.textOut": "Out", "Common.define.effectData.textOutFromScreenBottom": "Out from screen bottom", "Common.define.effectData.textOutSlightly": "Out slightly", "Common.define.effectData.textOutToScreenCenter": "Out to screen center", "Common.define.effectData.textParallelogram": "Parallelogram", "Common.define.effectData.textPath": "Motion paths", "Common.define.effectData.textPathCurve": "Curve", "Common.define.effectData.textPathLine": "Line", "Common.define.effectData.textPathScribble": "Scribble", "Common.define.effectData.textPeanut": "Peanut", "Common.define.effectData.textPeekIn": "Peek In", "Common.define.effectData.textPeekOut": "Peek Out", "Common.define.effectData.textPentagon": "Pentagon", "Common.define.effectData.textPinwheel": "Pinwheel", "Common.define.effectData.textPlus": "Plus", "Common.define.effectData.textPointStar": "Point Star", "Common.define.effectData.textPointStar4": "4 Point Star", "Common.define.effectData.textPointStar5": "5 Point Star", "Common.define.effectData.textPointStar6": "6 Point Star", "Common.define.effectData.textPointStar8": "8 Point Star", "Common.define.effectData.textPulse": "Pulse", "Common.define.effectData.textRandomBars": "Random Bars ", "Common.define.effectData.textRight": "Right", "Common.define.effectData.textRightDown": " Right down", "Common.define.effectData.textRightTriangle": "Right triangle", "Common.define.effectData.textRightUp": " Right up", "Common.define.effectData.textRiseUp": "Rise Up", "Common.define.effectData.textSCurve1": "S Curve 1", "Common.define.effectData.textSCurve2": "S Curve 2", "Common.define.effectData.textShape": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textShapes": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textShimmer": "Shimmer", "Common.define.effectData.textShrinkTurn": "Shrink & Turn", "Common.define.effectData.textSineWave": "Sine Wave", "Common.define.effectData.textSinkDown": "Sink Down", "Common.define.effectData.textSlideCenter": "Slide Center", "Common.define.effectData.textSpecial": "Special", "Common.define.effectData.textSpin": "Spin", "Common.define.effectData.textSpinner": "Spinner", "Common.define.effectData.textSpiralIn": "Spiral In", "Common.define.effectData.textSpiralLeft": "<PERSON><PERSON><PERSON> left", "Common.define.effectData.textSpiralOut": "Spiral Out", "Common.define.effectData.textSpiralRight": "Spiral right", "Common.define.effectData.textSplit": "Split", "Common.define.effectData.textSpoke1": "1 Spoke", "Common.define.effectData.textSpoke2": "2 Spokes", "Common.define.effectData.textSpoke3": "3 Spokes", "Common.define.effectData.textSpoke4": "4 Spokes", "Common.define.effectData.textSpoke8": "8 Spokes", "Common.define.effectData.textSpring": "Spring", "Common.define.effectData.textSquare": "Square", "Common.define.effectData.textStairsDown": "Stairs Down", "Common.define.effectData.textStretch": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textStrips": "Strips", "Common.define.effectData.textSubtle": "Subtle", "Common.define.effectData.textSwivel": "Swivel", "Common.define.effectData.textSwoosh": "Swoosh", "Common.define.effectData.textTeardrop": "Teardrop", "Common.define.effectData.textTeeter": "Teeter", "Common.define.effectData.textToBottom": "To bottom", "Common.define.effectData.textToBottomLeft": "To bottom-left", "Common.define.effectData.textToBottomRight": "To bottom-right", "Common.define.effectData.textToLeft": "To Left", "Common.define.effectData.textToRight": "To right", "Common.define.effectData.textToTop": "To top", "Common.define.effectData.textToTopLeft": "To top-left", "Common.define.effectData.textToTopRight": "To top-right", "Common.define.effectData.textTransparency": "Transparency", "Common.define.effectData.textTrapezoid": "Trapezoid", "Common.define.effectData.textTurnDown": "Turn down", "Common.define.effectData.textTurnDownRight": "Turn down right", "Common.define.effectData.textTurns": "Turns", "Common.define.effectData.textTurnUp": "Turn up", "Common.define.effectData.textTurnUpRight": "Turn up right", "Common.define.effectData.textUnderline": "Underline", "Common.define.effectData.textUp": "Up", "Common.define.effectData.textVertical": "Vertical", "Common.define.effectData.textVerticalFigure": "Vertical Figure 8", "Common.define.effectData.textVerticalIn": "Vertical In", "Common.define.effectData.textVerticalOut": "Vertical Out", "Common.define.effectData.textWave": "Wave", "Common.define.effectData.textWedge": "Wedge", "Common.define.effectData.textWheel": "Wheel", "Common.define.effectData.textWhip": "Whip", "Common.define.effectData.textWipe": "Wipe", "Common.define.effectData.textZigzag": "Zigzag", "Common.define.effectData.textZoom": "Zoom", "Common.define.gridlineData.txtCm": "cm", "Common.define.gridlineData.txtPt": "pt", "Common.define.smartArt.textAccentedPicture": "Accented picture", "Common.define.smartArt.textAccentProcess": "Accent process", "Common.define.smartArt.textAlternatingFlow": "Alternating flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating Hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating picture blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating picture circles", "Common.define.smartArt.textArchitectureLayout": "Architecture layout", "Common.define.smartArt.textArrowRibbon": "Arrow ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending picture accent process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic bending process", "Common.define.smartArt.textBasicBlockList": "Basic block list", "Common.define.smartArt.textBasicChevronProcess": "Basic chevron process", "Common.define.smartArt.textBasicCycle": "Basic cycle", "Common.define.smartArt.textBasicMatrix": "Basic matrix", "Common.define.smartArt.textBasicPie": "Basic pie", "Common.define.smartArt.textBasicProcess": "Basic process", "Common.define.smartArt.textBasicPyramid": "Basic pyramid", "Common.define.smartArt.textBasicRadial": "Basic radial", "Common.define.smartArt.textBasicTarget": "Basic target", "Common.define.smartArt.textBasicTimeline": "Basic timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending picture accent list", "Common.define.smartArt.textBendingPictureBlocks": "Bending picture blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending picture caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending picture caption list", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending picture semi-transparent text", "Common.define.smartArt.textBlockCycle": "Block cycle", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron accent process", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Circle accent timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging Arrows", "Common.define.smartArt.textDivergingRadial": "Diverging Radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy List", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined list", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and title organization chart", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing Ideas", "Common.define.smartArt.textOrganizationChart": "Organization chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased Process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture accent blocks", "Common.define.smartArt.textPictureAccentList": "Picture accent list", "Common.define.smartArt.textPictureAccentProcess": "Picture accent process", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture Strips", "Common.define.smartArt.textPieProcess": "Pie Process", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial Cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "Spiral picture", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Tabbed <PERSON>", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward Arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "More", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "The file is being edited in another app. You can continue editing and save it as a copy.", "Common.Translation.warnFileLockedBtnEdit": "Create a copy", "Common.Translation.warnFileLockedBtnView": "Open for viewing", "Common.UI.ButtonColored.textAutoColor": "Automatic", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "More colors", "Common.UI.ComboBorderSize.txtNoBorders": "No borders", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "No borders", "Common.UI.ComboDataView.emptyComboText": "No styles", "Common.UI.ExtendedColorDialog.addButtonText": "Add", "Common.UI.ExtendedColorDialog.textCurrent": "Current", "Common.UI.ExtendedColorDialog.textHexErr": "The entered value is incorrect.<br>Please enter a value between 000000 and FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "New", "Common.UI.ExtendedColorDialog.textRGBErr": "The entered value is incorrect.<br>Please enter a numeric value between 0 and 255.", "Common.UI.HSBColorPicker.textNoColor": "No color", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Hide password", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Show password", "Common.UI.SearchBar.textFind": "Find", "Common.UI.SearchBar.tipCloseSearch": "Close find", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Previous result", "Common.UI.SearchDialog.textHighlight": "Highlight results", "Common.UI.SearchDialog.textMatchCase": "Case sensitive", "Common.UI.SearchDialog.textReplaceDef": "Enter the replacement text", "Common.UI.SearchDialog.textSearchStart": "Enter your text here", "Common.UI.SearchDialog.textTitle": "Find and replace", "Common.UI.SearchDialog.textTitle2": "Find", "Common.UI.SearchDialog.textWholeWords": "Whole words only", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Replace", "Common.UI.SearchDialog.txtBtnReplaceAll": "Replace all", "Common.UI.SynchronizeTip.textDontShow": "Don't show this message again", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "The document has been changed by another user.<br>Please click to save your changes and reload the updates.", "Common.UI.ThemeColorPalette.textRecentColors": "Recent colors", "Common.UI.ThemeColorPalette.textStandartColors": "Standard colors", "Common.UI.ThemeColorPalette.textThemeColors": "Theme colors", "Common.UI.Themes.txtThemeClassicLight": "Classic Light", "Common.UI.Themes.txtThemeContrastDark": "Contrast Dark", "Common.UI.Themes.txtThemeDark": "Dark", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "Light", "Common.UI.Themes.txtThemeSystem": "Same as system", "Common.UI.Window.cancelButtonText": "Cancel", "Common.UI.Window.closeButtonText": "Close", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmation", "Common.UI.Window.textDontShow": "Don't show this message again", "Common.UI.Window.textError": "Error", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Warning", "Common.UI.Window.yesButtonText": "Yes", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Background", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Blue", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Dark green", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON>", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "address: ", "Common.Views.About.txtLicensee": "LICENSEE", "Common.Views.About.txtLicensor": "LICENSOR", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Version ", "Common.Views.AutoCorrectDialog.textAdd": "Add", "Common.Views.AutoCorrectDialog.textApplyText": "Apply as you type", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Text AutoCorrect", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat as you type", "Common.Views.AutoCorrectDialog.textBulleted": "Automatic bulleted lists", "Common.Views.AutoCorrectDialog.textBy": "By", "Common.Views.AutoCorrectDialog.textDelete": "Delete", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Add period with double-space", "Common.Views.AutoCorrectDialog.textFLCells": "Capitalize first letter of table cells", "Common.Views.AutoCorrectDialog.textFLDont": "Don`t capitalize after", "Common.Views.AutoCorrectDialog.textFLSentence": "Capitalize first letter of sentences", "Common.Views.AutoCorrectDialog.textForLangFL": "Exceptions for the language:", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet and network paths with hyperlinks", "Common.Views.AutoCorrectDialog.textHyphens": "Hyphens (--) with dash (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Math AutoCorrect", "Common.Views.AutoCorrectDialog.textNumbered": "Automatic numbered lists", "Common.Views.AutoCorrectDialog.textQuotes": "\"Straight quotes\" with \"smart quotes\"", "Common.Views.AutoCorrectDialog.textRecognized": "Recognized functions", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "The following expressions are recognized math expressions. They will not be automatically italicized.", "Common.Views.AutoCorrectDialog.textReplace": "Replace", "Common.Views.AutoCorrectDialog.textReplaceText": "Replace as you type", "Common.Views.AutoCorrectDialog.textReplaceType": "Replace text as you type", "Common.Views.AutoCorrectDialog.textReset": "Reset", "Common.Views.AutoCorrectDialog.textResetAll": "Reset to default", "Common.Views.AutoCorrectDialog.textRestore": "Rest<PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "AutoCorrect", "Common.Views.AutoCorrectDialog.textWarnAddFL": "Exceptions must contain only the letters, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Recognized functions must contain only the letters A through Z, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnResetFL": "Any exceptions you added will be removed and the removed ones will be restored. Do you want to continue?", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Any expression you added will be removed and the removed ones will be restored. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnReplace": "The autocorrect entry for %1 already exists. Do you want to replace it?", "Common.Views.AutoCorrectDialog.warnReset": "Any autocorrect you added will be removed and the changed ones will be restored to their original values. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnRestore": "The autocorrect entry for %1 will be reset to its original value. Do you want to continue?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "Send", "Common.Views.Comments.mniAuthorAsc": "Author A to Z", "Common.Views.Comments.mniAuthorDesc": "Author Z to A", "Common.Views.Comments.mniDateAsc": "Oldest", "Common.Views.Comments.mniDateDesc": "Newest", "Common.Views.Comments.mniFilterGroups": "Filter by Group", "Common.Views.Comments.mniPositionAsc": "From top", "Common.Views.Comments.mniPositionDesc": "From bottom", "Common.Views.Comments.textAdd": "Add", "Common.Views.Comments.textAddComment": "Add comment", "Common.Views.Comments.textAddCommentToDoc": "Add comment to document", "Common.Views.Comments.textAddReply": "Add reply", "Common.Views.Comments.textAll": "All", "Common.Views.Comments.textAnonym": "Guest", "Common.Views.Comments.textCancel": "Cancel", "Common.Views.Comments.textClose": "Close", "Common.Views.Comments.textClosePanel": "Close comments", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "Comments", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Enter your comment here", "Common.Views.Comments.textHintAddComment": "Add comment", "Common.Views.Comments.textOpenAgain": "Open Again", "Common.Views.Comments.textReply": "Reply", "Common.Views.Comments.textResolve": "Resolve", "Common.Views.Comments.textResolved": "Resolved", "Common.Views.Comments.textSort": "Sort comments", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "You have no permission to reopen the comment", "Common.Views.Comments.txtEmpty": "There are no comments in the document.", "Common.Views.CopyWarningDialog.textDontShow": "Don't show this message again", "Common.Views.CopyWarningDialog.textMsg": "Copy, cut and paste actions using the editor toolbar buttons and context menu actions will be performed within this editor tab only.<br><br>To copy or paste to or from applications outside the editor tab use the following keyboard combinations:", "Common.Views.CopyWarningDialog.textTitle": "Copy, Cut and Paste actions", "Common.Views.CopyWarningDialog.textToCopy": "for Co<PERSON>", "Common.Views.CopyWarningDialog.textToCut": "for Cut", "Common.Views.CopyWarningDialog.textToPaste": "for Paste", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textStartOver": "Show from Beginning", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Loading...", "Common.Views.DocumentAccessDialog.textTitle": "Sharing settings", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "Select", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "Select", "Common.Views.Draw.txtSize": "Size", "Common.Views.ExternalDiagramEditor.textTitle": "Chart Editor", "Common.Views.ExternalEditor.textClose": "Close", "Common.Views.ExternalEditor.textSave": "Save & Exit", "Common.Views.ExternalOleEditor.textTitle": "Spreadsheet Editor", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Users who are editing the file:", "Common.Views.Header.textAddFavorite": "<PERSON> as favorite", "Common.Views.Header.textAdvSettings": "Advanced settings", "Common.Views.Header.textBack": "Open file location", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "<PERSON><PERSON>", "Common.Views.Header.textHideLines": "Hide Rulers", "Common.Views.Header.textHideNotes": "Hide notes", "Common.Views.Header.textHideStatusBar": "Hide Status Bar", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Remove from Favorites", "Common.Views.Header.textSaveBegin": "Saving...", "Common.Views.Header.textSaveChanged": "Modified", "Common.Views.Header.textSaveEnd": "All changes saved", "Common.Views.Header.textSaveExpander": "All changes saved", "Common.Views.Header.textShare": "Share", "Common.Views.Header.textStartOver": "Show from Beginning", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Manage document access rights", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "Download file", "Common.Views.Header.tipGoEdit": "Edit current file", "Common.Views.Header.tipPrint": "Print file", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "Redo", "Common.Views.Header.tipSave": "Save", "Common.Views.Header.tipSearch": "Find", "Common.Views.Header.tipStartOver": "Start slideshow from beginning", "Common.Views.Header.tipUndo": "Undo", "Common.Views.Header.tipUndock": "Undock into separate window", "Common.Views.Header.tipUsers": "View users", "Common.Views.Header.tipViewSettings": "View settings", "Common.Views.Header.tipViewUsers": "View users and manage document access rights", "Common.Views.Header.txtAccessRights": "Change access rights", "Common.Views.Header.txtRename": "<PERSON><PERSON>", "Common.Views.History.textCloseHistory": "Close history", "Common.Views.History.textHideAll": "Hide detailed changes", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "Rest<PERSON>", "Common.Views.History.textShowAll": "Show detailed changes", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Paste an image URL:", "Common.Views.ImageFromUrlDialog.txtEmpty": "This field is required", "Common.Views.ImageFromUrlDialog.txtNotUrl": "This field should be a URL in the \"http://www.example.com\" format", "Common.Views.InsertTableDialog.textInvalidRowsCols": "You need to specify valid rows and columns number.", "Common.Views.InsertTableDialog.txtColumns": "Number of columns", "Common.Views.InsertTableDialog.txtMaxText": "The maximum value for this field is {0}.", "Common.Views.InsertTableDialog.txtMinText": "The minimum value for this field is {0}.", "Common.Views.InsertTableDialog.txtRows": "Number of rows", "Common.Views.InsertTableDialog.txtTitle": "Table size", "Common.Views.InsertTableDialog.txtTitleSplit": "Split cell", "Common.Views.LanguageDialog.labelSelect": "Select document language", "Common.Views.ListSettingsDialog.textBulleted": "Bulleted", "Common.Views.ListSettingsDialog.textFromFile": "From file", "Common.Views.ListSettingsDialog.textFromStorage": "From storage", "Common.Views.ListSettingsDialog.textFromUrl": "From URL", "Common.Views.ListSettingsDialog.textNumbering": "Numbered", "Common.Views.ListSettingsDialog.textSelect": "Select from", "Common.Views.ListSettingsDialog.tipChange": "Change bullet", "Common.Views.ListSettingsDialog.txtBullet": "Bullet", "Common.Views.ListSettingsDialog.txtColor": "Color", "Common.Views.ListSettingsDialog.txtImage": "Image", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "New bullet", "Common.Views.ListSettingsDialog.txtNewImage": "New image", "Common.Views.ListSettingsDialog.txtNone": "None", "Common.Views.ListSettingsDialog.txtOfText": "% of text", "Common.Views.ListSettingsDialog.txtSize": "Size", "Common.Views.ListSettingsDialog.txtStart": "Start at", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "List settings", "Common.Views.ListSettingsDialog.txtType": "Type", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "Close file", "Common.Views.OpenDialog.txtEncoding": "Encoding ", "Common.Views.OpenDialog.txtIncorrectPwd": "Password is incorrect.", "Common.Views.OpenDialog.txtOpenFile": "Enter a password to open the file", "Common.Views.OpenDialog.txtPassword": "Password", "Common.Views.OpenDialog.txtProtected": "Once you enter the password and open the file, the current password to the file will be reset.", "Common.Views.OpenDialog.txtTitle": "Choose %1 options", "Common.Views.OpenDialog.txtTitleProtected": "Protected file", "Common.Views.PasswordDialog.txtDescription": "Set a password to protect this document", "Common.Views.PasswordDialog.txtIncorrectPwd": "Confirmation password is not identical", "Common.Views.PasswordDialog.txtPassword": "Password", "Common.Views.PasswordDialog.txtRepeat": "Repeat password", "Common.Views.PasswordDialog.txtTitle": "Set password", "Common.Views.PasswordDialog.txtWarning": "Warning: If you lose or forget the password, it cannot be recovered. Please keep it in a safe place.", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginDlg.textLoading": "Loading", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.PluginPanel.textUndock": "Unpin plugin", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "Encrypt with password", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "Change or delete password", "Common.Views.Protection.hintSignature": "Add digital signature or signature line", "Common.Views.Protection.txtAddPwd": "Add password", "Common.Views.Protection.txtChangePwd": "Change password", "Common.Views.Protection.txtDeletePwd": "Delete password", "Common.Views.Protection.txtEncrypt": "Encrypt", "Common.Views.Protection.txtInvisibleSignature": "Add digital signature", "Common.Views.Protection.txtSignature": "Signature", "Common.Views.Protection.txtSignatureLine": "Add signature line", "Common.Views.RecentFiles.txtOpenRecent": "Open Recent", "Common.Views.RenameDialog.textName": "File name", "Common.Views.RenameDialog.txtInvalidName": "The file name cannot contain any of the following characters: ", "Common.Views.ReviewChanges.hintNext": "To next change", "Common.Views.ReviewChanges.hintPrev": "To previous change", "Common.Views.ReviewChanges.strFast": "Fast", "Common.Views.ReviewChanges.strFastDesc": "Real-time co-editing. All changes are saved automatically.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Use the 'Save' button to sync the changes you and others make.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accept current change", "Common.Views.ReviewChanges.tipCoAuthMode": "Set co-editing mode", "Common.Views.ReviewChanges.tipCommentRem": "Delete comments", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Delete current comments", "Common.Views.ReviewChanges.tipCommentResolve": "Resolve comments", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.tipHistory": "Show version history", "Common.Views.ReviewChanges.tipRejectCurrent": "Reject current change", "Common.Views.ReviewChanges.tipReview": "Track changes", "Common.Views.ReviewChanges.tipReviewView": "Select the mode you want the changes to be displayed", "Common.Views.ReviewChanges.tipSetDocLang": "Set document language", "Common.Views.ReviewChanges.tipSetSpelling": "Spell checking", "Common.Views.ReviewChanges.tipSharing": "Manage document access rights", "Common.Views.ReviewChanges.txtAccept": "Accept", "Common.Views.ReviewChanges.txtAcceptAll": "Accept all changes", "Common.Views.ReviewChanges.txtAcceptChanges": "Accept changes", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accept current change", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "Close", "Common.Views.ReviewChanges.txtCoAuthMode": "Co-editing Mode", "Common.Views.ReviewChanges.txtCommentRemAll": "Delete all comments", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Delete current comments", "Common.Views.ReviewChanges.txtCommentRemMy": "Delete my comments", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Delete my current comments", "Common.Views.ReviewChanges.txtCommentRemove": "Delete", "Common.Views.ReviewChanges.txtCommentResolve": "Resolve", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolve all comments", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolve my comments", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolve my current comments", "Common.Views.ReviewChanges.txtDocLang": "Language", "Common.Views.ReviewChanges.txtFinal": "All changes accepted (Preview)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Version History", "Common.Views.ReviewChanges.txtMarkup": "All changes (Editing)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Next", "Common.Views.ReviewChanges.txtOriginal": "All changes rejected (Preview)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Previous", "Common.Views.ReviewChanges.txtReject": "Reject", "Common.Views.ReviewChanges.txtRejectAll": "Reject All Changes", "Common.Views.ReviewChanges.txtRejectChanges": "Reject changes", "Common.Views.ReviewChanges.txtRejectCurrent": "Reject current change", "Common.Views.ReviewChanges.txtSharing": "Sharing", "Common.Views.ReviewChanges.txtSpelling": "Spell Checking", "Common.Views.ReviewChanges.txtTurnon": "Track Changes", "Common.Views.ReviewChanges.txtView": "Display Mode", "Common.Views.ReviewPopover.textAdd": "Add", "Common.Views.ReviewPopover.textAddReply": "Add reply", "Common.Views.ReviewPopover.textCancel": "Cancel", "Common.Views.ReviewPopover.textClose": "Close", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+mention will provide access to the document and send an email", "Common.Views.ReviewPopover.textMentionNotify": "+mention will notify the user via email", "Common.Views.ReviewPopover.textOpenAgain": "Open Again", "Common.Views.ReviewPopover.textReply": "Reply", "Common.Views.ReviewPopover.textResolve": "Resolve", "Common.Views.ReviewPopover.textViewResolved": "You have no permission to reopen the comment", "Common.Views.ReviewPopover.txtDeleteTip": "Delete", "Common.Views.ReviewPopover.txtEditTip": "Edit", "Common.Views.SaveAsDlg.textLoading": "Loading", "Common.Views.SaveAsDlg.textTitle": "Folder for save", "Common.Views.SearchPanel.textCaseSensitive": "Case sensitive", "Common.Views.SearchPanel.textCloseSearch": "Close find", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "Find", "Common.Views.SearchPanel.textFindAndReplace": "Find and replace", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} items successfully replaced.", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} items replaced. Remaining {2} items are locked by other users.", "Common.Views.SearchPanel.textReplace": "Replace", "Common.Views.SearchPanel.textReplaceAll": "Replace All", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "Search has stopped", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textTooManyResults": "There are too many results to show here", "Common.Views.SearchPanel.textWholeWords": "Whole words only", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "Previous result", "Common.Views.SelectFileDlg.textLoading": "Loading", "Common.Views.SelectFileDlg.textTitle": "Select data source", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Bold", "Common.Views.SignDialog.textCertificate": "Certificate", "Common.Views.SignDialog.textChange": "Change", "Common.Views.SignDialog.textInputName": "Input signer name", "Common.Views.SignDialog.textItalic": "Italic", "Common.Views.SignDialog.textNameError": "Signer name must not be empty.", "Common.Views.SignDialog.textPurpose": "Purpose for signing this document", "Common.Views.SignDialog.textSelect": "Select", "Common.Views.SignDialog.textSelectImage": "Select image", "Common.Views.SignDialog.textSignature": "Signature looks as", "Common.Views.SignDialog.textTitle": "Sign document", "Common.Views.SignDialog.textUseImage": "or click 'Select Image' to use a picture as signature", "Common.Views.SignDialog.textValid": "Valid from %1 to %2", "Common.Views.SignDialog.tipFontName": "Font name", "Common.Views.SignDialog.tipFontSize": "Font size", "Common.Views.SignSettingsDialog.textAllowComment": "Allow signer to add comment in the signature dialog", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "Suggested signer's e-mail", "Common.Views.SignSettingsDialog.textInfoName": "Suggested signer", "Common.Views.SignSettingsDialog.textInfoTitle": "Suggested signer's title", "Common.Views.SignSettingsDialog.textInstructions": "Instructions for signer", "Common.Views.SignSettingsDialog.textShowDate": "Show sign date in signature line", "Common.Views.SignSettingsDialog.textTitle": "Signature setup", "Common.Views.SignSettingsDialog.txtEmpty": "This field is required", "Common.Views.SymbolTableDialog.textCharacter": "Character", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX value", "Common.Views.SymbolTableDialog.textCopyright": "Copyright sign", "Common.Views.SymbolTableDialog.textDCQuote": "Closing double quote", "Common.Views.SymbolTableDialog.textDOQuote": "Opening double quote", "Common.Views.SymbolTableDialog.textEllipsis": "Horizontal ellipsis", "Common.Views.SymbolTableDialog.textEmDash": "Em dash", "Common.Views.SymbolTableDialog.textEmSpace": "Em space", "Common.Views.SymbolTableDialog.textEnDash": "En dash", "Common.Views.SymbolTableDialog.textEnSpace": "En space", "Common.Views.SymbolTableDialog.textFont": "Font", "Common.Views.SymbolTableDialog.textNBHyphen": "Non-breaking hyphen", "Common.Views.SymbolTableDialog.textNBSpace": "No-break space", "Common.Views.SymbolTableDialog.textPilcrow": "Pilcrow sign", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em space", "Common.Views.SymbolTableDialog.textRange": "Range", "Common.Views.SymbolTableDialog.textRecent": "Recently used symbols", "Common.Views.SymbolTableDialog.textRegistered": "Registered sign", "Common.Views.SymbolTableDialog.textSCQuote": "Closing single quote", "Common.Views.SymbolTableDialog.textSection": "Section sign", "Common.Views.SymbolTableDialog.textShortcut": "Shortcut key", "Common.Views.SymbolTableDialog.textSHyphen": "Soft hyphen", "Common.Views.SymbolTableDialog.textSOQuote": "Opening single quote", "Common.Views.SymbolTableDialog.textSpecial": "Special characters", "Common.Views.SymbolTableDialog.textSymbols": "Symbols", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Trademark symbol", "Common.Views.UserNameDialog.textDontShow": "Don't ask me again", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label must not be empty.", "PE.Controllers.DocumentHolder.textLongName": "Enter a name that is less than 255 characters.", "PE.Controllers.DocumentHolder.textNameLayout": "Layout name", "PE.Controllers.DocumentHolder.textNameMaster": "Master name", "PE.Controllers.DocumentHolder.textRenameTitleLayout": "<PERSON><PERSON>out", "PE.Controllers.DocumentHolder.textRenameTitleMaster": "<PERSON><PERSON>", "PE.Controllers.LeftMenu.leavePageText": "All unsaved changes in this document will be lost.<br> Click \"Cancel\" then \"Save\" to save them. Click \"OK\" to discard all the unsaved changes.", "PE.Controllers.LeftMenu.newDocumentTitle": "Unnamed presentation", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Warning", "PE.Controllers.LeftMenu.requestEditRightsText": "Requesting editing rights...", "PE.Controllers.LeftMenu.textLoadHistory": "Loading version history...", "PE.Controllers.LeftMenu.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "PE.Controllers.LeftMenu.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "PE.Controllers.LeftMenu.textReplaceSuccess": "The search has been done. Occurrences replaced: {0}", "PE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "PE.Controllers.LeftMenu.txtUntitled": "Untitled", "PE.Controllers.Main.applyChangesTextText": "Loading data...", "PE.Controllers.Main.applyChangesTitleText": "Loading Data", "PE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "PE.Controllers.Main.convertationTimeoutText": "Conversion timeout exceeded.", "PE.Controllers.Main.criticalErrorExtText": "Press \"OK\" to return to document list.", "PE.Controllers.Main.criticalErrorTitle": "Error", "PE.Controllers.Main.downloadErrorText": "Download failed.", "PE.Controllers.Main.downloadTextText": "Downloading presentation...", "PE.Controllers.Main.downloadTitleText": "Downloading Presentation", "PE.Controllers.Main.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "PE.Controllers.Main.errorBadImageUrl": "Image URL is incorrect", "PE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the presentation.", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Server connection lost. The document cannot be edited right now.", "PE.Controllers.Main.errorComboSeries": "To create a combination chart, select at least two series of data.", "PE.Controllers.Main.errorConnectToServer": "The document could not be saved. Please check connection settings or contact your administrator.<br>When you click the 'OK' button, you will be prompted to download the document.", "PE.Controllers.Main.errorDatabaseConnection": "External error.<br>Database connection error. Please contact support in case the error persists.", "PE.Controllers.Main.errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "PE.Controllers.Main.errorDataRange": "Incorrect data range.", "PE.Controllers.Main.errorDefaultMessage": "Error code: %1", "PE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "PE.Controllers.Main.errorEditingDownloadas": "An error occurred during the work with the document.<br>Use the 'Download as' option to save the file backup copy to a drive.", "PE.Controllers.Main.errorEditingSaveas": "An error occurred during the work with the document.<br>Use the 'Save as...' option to save the file backup copy to a drive.", "PE.Controllers.Main.errorEmailClient": "No email client could be found.", "PE.Controllers.Main.errorFilePassProtect": "The file is password protected and cannot be opened.", "PE.Controllers.Main.errorFileSizeExceed": "The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.", "PE.Controllers.Main.errorForceSave": "An error occurred while saving the file. Please use the 'Download as' option to save the file to a drive or try again later.", "PE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "PE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "PE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "PE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "PE.Controllers.Main.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "PE.Controllers.Main.errorKeyEncrypt": "Unknown key descriptor", "PE.Controllers.Main.errorKeyExpire": "Key descriptor expired", "PE.Controllers.Main.errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "PE.Controllers.Main.errorProcessSaveResult": "Saving failed.", "PE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "PE.Controllers.Main.errorServerVersion": "The editor version has been updated. The page will be reloaded to apply the changes.", "PE.Controllers.Main.errorSessionAbsolute": "The document editing session has expired. Please reload the page.", "PE.Controllers.Main.errorSessionIdle": "The document has not been edited for quite a long time. Please reload the page.", "PE.Controllers.Main.errorSessionToken": "The connection to the server has been interrupted. Please reload the page.", "PE.Controllers.Main.errorSetPassword": "Password could not be set.", "PE.Controllers.Main.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "PE.Controllers.Main.errorToken": "The document security token is not correctly formed.<br>Please contact your Document Server administrator.", "PE.Controllers.Main.errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator.", "PE.Controllers.Main.errorUpdateVersion": "The file version has been changed. The page will be reloaded.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "PE.Controllers.Main.errorUserDrop": "The file cannot be accessed right now.", "PE.Controllers.Main.errorUsersExceed": "The number of users allowed by the pricing plan was exceeded", "PE.Controllers.Main.errorViewerDisconnect": "Connection is lost. You can still view the document,<br>but will not be able to download or print it until the connection is restored and page is reloaded.", "PE.Controllers.Main.leavePageText": "You have unsaved changes in this presentation. Click \"Stay on This Page\", then \"Save\" to save them. Click \"Leave This Page\" to discard all the unsaved changes.", "PE.Controllers.Main.leavePageTextOnClose": "All unsaved changes in this presentation will be lost.<br> Click \"Cancel\" then \"Save\" to save them. Click \"OK\" to discard all the unsaved changes.", "PE.Controllers.Main.loadFontsTextText": "Loading data...", "PE.Controllers.Main.loadFontsTitleText": "Loading Data", "PE.Controllers.Main.loadFontTextText": "Loading data...", "PE.Controllers.Main.loadFontTitleText": "Loading Data", "PE.Controllers.Main.loadImagesTextText": "Loading images...", "PE.Controllers.Main.loadImagesTitleText": "Loading Images", "PE.Controllers.Main.loadImageTextText": "Loading image...", "PE.Controllers.Main.loadImageTitleText": "Loading Image", "PE.Controllers.Main.loadingDocumentTextText": "Loading presentation...", "PE.Controllers.Main.loadingDocumentTitleText": "Loading presentation", "PE.Controllers.Main.loadThemeTextText": "Loading theme...", "PE.Controllers.Main.loadThemeTitleText": "Loading Theme", "PE.Controllers.Main.notcriticalErrorTitle": "Warning", "PE.Controllers.Main.openErrorText": "An error has occurred while opening the file.", "PE.Controllers.Main.openTextText": "Opening presentation...", "PE.Controllers.Main.openTitleText": "Opening Presentation", "PE.Controllers.Main.printTextText": "Printing presentation...", "PE.Controllers.Main.printTitleText": "Printing Presentation", "PE.Controllers.Main.reloadButtonText": "Reload Page", "PE.Controllers.Main.requestEditFailedMessageText": "Someone is editing this presentation right now. Please try again later.", "PE.Controllers.Main.requestEditFailedTitleText": "Access denied", "PE.Controllers.Main.saveErrorText": "An error has occurred while saving the file.", "PE.Controllers.Main.saveErrorTextDesktop": "This file cannot be saved or created.<br>Possible reasons are: <br>1. The file is read-only. <br>2. The file is being edited by other users. <br>3. The disk is full or corrupted.", "PE.Controllers.Main.saveTextText": "Saving presentation...", "PE.Controllers.Main.saveTitleText": "Saving Presentation", "PE.Controllers.Main.scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please reload the page.", "PE.Controllers.Main.splitDividerErrorText": "The number of rows must be a divisor of %1.", "PE.Controllers.Main.splitMaxColsErrorText": "The number of columns must be less than %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "The number of rows must be less than %1.", "PE.Controllers.Main.textAnonymous": "Anonymous", "PE.Controllers.Main.textApplyAll": "Apply to all equations", "PE.Controllers.Main.textBuyNow": "Visit website", "PE.Controllers.Main.textChangesSaved": "All changes saved", "PE.Controllers.Main.textClose": "Close", "PE.Controllers.Main.textCloseTip": "Click to close the tip", "PE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "PE.Controllers.Main.textContactUs": "Contact sales", "PE.Controllers.Main.textContinue": "Continue", "PE.Controllers.Main.textConvertEquation": "This equation was created with an old version of the equation editor which is no longer supported. To edit it, convert the equation to the Office Math ML format.<br>Convert now?", "PE.Controllers.Main.textCustomLoader": "Please note that according to the terms of the license you are not entitled to change the loader.<br>Please contact our Sales Department to get a quote.", "PE.Controllers.Main.textDisconnect": "Connection is lost", "PE.Controllers.Main.textGuest": "Guest", "PE.Controllers.Main.textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "PE.Controllers.Main.textLearnMore": "Learn More", "PE.Controllers.Main.textLoadingDocument": "Loading presentation", "PE.Controllers.Main.textLongName": "Enter a name that is less than 128 characters.", "PE.Controllers.Main.textNoLicenseTitle": "License limit reached", "PE.Controllers.Main.textObject": "Object", "PE.Controllers.Main.textPaidFeature": "Paid feature", "PE.Controllers.Main.textReconnect": "Connection is restored", "PE.Controllers.Main.textRemember": "Remember my choice for all files", "PE.Controllers.Main.textRememberMacros": "Remember my choice for all macros", "PE.Controllers.Main.textRenameError": "User name must not be empty.", "PE.Controllers.Main.textRenameLabel": "Enter a name to be used for collaboration", "PE.Controllers.Main.textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "PE.Controllers.Main.textShape": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textStrict": "Strict mode", "PE.Controllers.Main.textText": "Text", "PE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "PE.Controllers.Main.textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.<br>Click the 'Strict mode' button to switch to the Strict co-editing mode to edit the file without other users interference and send your changes only after you save them. You can switch between the co-editing modes using the editor Advanced settings.", "PE.Controllers.Main.textTryUndoRedoWarn": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "PE.Controllers.Main.textUndo": "Undo", "PE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "PE.Controllers.Main.textUpdating": "Updating", "PE.Controllers.Main.titleLicenseExp": "License expired", "PE.Controllers.Main.titleLicenseNotActive": "License not active", "PE.Controllers.Main.titleServerVersion": "Editor updated", "PE.Controllers.Main.titleUpdateVersion": "Version changed", "PE.Controllers.Main.txtAddFirstSlide": "Click to add the first slide", "PE.Controllers.Main.txtAddNotes": "Click to add notes", "PE.Controllers.Main.txtAnimationPane": "Animation Pane", "PE.Controllers.Main.txtArt": "Your text here", "PE.Controllers.Main.txtBasicShapes": "Basic shapes", "PE.Controllers.Main.txtButtons": "Buttons", "PE.Controllers.Main.txtCallouts": "Callouts", "PE.Controllers.Main.txtCharts": "Charts", "PE.Controllers.Main.txtClipArt": "<PERSON><PERSON>", "PE.Controllers.Main.txtDateTime": "Date and time", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Chart title", "PE.Controllers.Main.txtEditingMode": "Set editing mode...", "PE.Controllers.Main.txtEnd": "End: ${0}s", "PE.Controllers.Main.txtErrorLoadHistory": "History loading failed", "PE.Controllers.Main.txtFiguredArrows": "Figured arrows", "PE.Controllers.Main.txtFirstSlide": "First slide", "PE.Controllers.Main.txtFooter": "Footer", "PE.Controllers.Main.txtHeader": "Header", "PE.Controllers.Main.txtImage": "Image", "PE.Controllers.Main.txtLastSlide": "Last slide", "PE.Controllers.Main.txtLines": "Lines", "PE.Controllers.Main.txtLoading": "Loading...", "PE.Controllers.Main.txtLoop": "Loop: ${0}s", "PE.Controllers.Main.txtMath": "Math", "PE.Controllers.Main.txtMedia": "Media", "PE.Controllers.Main.txtNeedSynchronize": "You have updates", "PE.Controllers.Main.txtNextSlide": "Next slide", "PE.Controllers.Main.txtNone": "None", "PE.Controllers.Main.txtPicture": "Picture", "PE.Controllers.Main.txtPlayAll": "Play All", "PE.Controllers.Main.txtPlayFrom": "Play From", "PE.Controllers.Main.txtPlaySelected": "Play Selected", "PE.Controllers.Main.txtPrevSlide": "Previous slide", "PE.Controllers.Main.txtRectangles": "Rectangles", "PE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "PE.Controllers.Main.txtScheme_Aspect": "Aspect", "PE.Controllers.Main.txtScheme_Blue": "Blue", "PE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "PE.Controllers.Main.txtScheme_Blue_II": "Blue II", "PE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "PE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "PE.Controllers.Main.txtScheme_Green": "Green", "PE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "PE.Controllers.Main.txtScheme_Marquee": "Marquee", "PE.Controllers.Main.txtScheme_Median": "Median", "PE.Controllers.Main.txtScheme_Office": "Office", "PE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "PE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "PE.Controllers.Main.txtScheme_Orange": "Orange", "PE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "PE.Controllers.Main.txtScheme_Paper": "Paper", "PE.Controllers.Main.txtScheme_Red": "Red", "PE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "PE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "PE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "PE.Controllers.Main.txtScheme_Violet": "Violet", "PE.Controllers.Main.txtScheme_Violet_II": "Violet II", "PE.Controllers.Main.txtScheme_Yellow": "Yellow", "PE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "PE.Controllers.Main.txtSeries": "Series", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Line callout 1 (Border and accent bar)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Line callout 2 (Border and accent bar)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Line callout 3 (Border and accent bar)", "PE.Controllers.Main.txtShape_accentCallout1": "Line callout 1 (Accent bar)", "PE.Controllers.Main.txtShape_accentCallout2": "Line callout 2 (Accent bar)", "PE.Controllers.Main.txtShape_accentCallout3": "Line callout 3 (Accent bar)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Back or previous button", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Beginning button", "PE.Controllers.Main.txtShape_actionButtonBlank": "Blank button", "PE.Controllers.Main.txtShape_actionButtonDocument": "Document Button", "PE.Controllers.Main.txtShape_actionButtonEnd": "End button", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "Forward or next button", "PE.Controllers.Main.txtShape_actionButtonHelp": "Help button", "PE.Controllers.Main.txtShape_actionButtonHome": "Home button", "PE.Controllers.Main.txtShape_actionButtonInformation": "Information button", "PE.Controllers.Main.txtShape_actionButtonMovie": "Movie button", "PE.Controllers.Main.txtShape_actionButtonReturn": "Return button", "PE.Controllers.Main.txtShape_actionButtonSound": "Sound button", "PE.Controllers.Main.txtShape_arc": "Arc", "PE.Controllers.Main.txtShape_bentArrow": "Bent arrow", "PE.Controllers.Main.txtShape_bentConnector5": "Elbow connector", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Elbow arrow connector", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Elbow double-arrow connector", "PE.Controllers.Main.txtShape_bentUpArrow": "Bent up arrow", "PE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_blockArc": "Block arc", "PE.Controllers.Main.txtShape_borderCallout1": "Line callout 1", "PE.Controllers.Main.txtShape_borderCallout2": "Line callout 2", "PE.Controllers.Main.txtShape_borderCallout3": "Line callout 3", "PE.Controllers.Main.txtShape_bracePair": "Double brace", "PE.Controllers.Main.txtShape_callout1": "Line callout 1 (No border)", "PE.Controllers.Main.txtShape_callout2": "Line callout 2 (No border)", "PE.Controllers.Main.txtShape_callout3": "Line Callout 3 (No border)", "PE.Controllers.Main.txtShape_can": "Can", "PE.Controllers.Main.txtShape_chevron": "Chevron", "PE.Controllers.Main.txtShape_chord": "Chord", "PE.Controllers.Main.txtShape_circularArrow": "Circular arrow", "PE.Controllers.Main.txtShape_cloud": "Cloud", "PE.Controllers.Main.txtShape_cloudCallout": "Cloud callout", "PE.Controllers.Main.txtShape_corner": "Corner", "PE.Controllers.Main.txtShape_cube": "C<PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Curved connector", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Curved arrow connector", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Curved double-arrow connector", "PE.Controllers.Main.txtShape_curvedDownArrow": "Curved down arrow", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Curved left arrow", "PE.Controllers.Main.txtShape_curvedRightArrow": "Curved right arrow", "PE.Controllers.Main.txtShape_curvedUpArrow": "Curved up arrow", "PE.Controllers.Main.txtShape_decagon": "Decagon", "PE.Controllers.Main.txtShape_diagStripe": "Diagonal stripe", "PE.Controllers.Main.txtShape_diamond": "Diamond", "PE.Controllers.Main.txtShape_dodecagon": "Dodecagon", "PE.Controllers.Main.txtShape_donut": "Donut", "PE.Controllers.Main.txtShape_doubleWave": "Double Wave", "PE.Controllers.Main.txtShape_downArrow": "Down Arrow", "PE.Controllers.Main.txtShape_downArrowCallout": "Down arrow callout", "PE.Controllers.Main.txtShape_ellipse": "Ellipse", "PE.Controllers.Main.txtShape_ellipseRibbon": "Curved down ribbon", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Curved up ribbon", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Flowchart: Alternate process", "PE.Controllers.Main.txtShape_flowChartCollate": "Flowchart: Collate", "PE.Controllers.Main.txtShape_flowChartConnector": "Flowchart: Connector", "PE.Controllers.Main.txtShape_flowChartDecision": "Flowchart: Decision", "PE.Controllers.Main.txtShape_flowChartDelay": "Flowchart: Delay", "PE.Controllers.Main.txtShape_flowChartDisplay": "Flowchart: Display", "PE.Controllers.Main.txtShape_flowChartDocument": "Flowchart: Document", "PE.Controllers.Main.txtShape_flowChartExtract": "Flowchart: Extract", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Flowchart: Data", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Flowchart: Internal storage", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flowchart: Magnetic disk", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flowchart: Direct access storage", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Flowchart: Sequential access storage", "PE.Controllers.Main.txtShape_flowChartManualInput": "Flowchart: Manual input", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Flowchart: Manual operation", "PE.Controllers.Main.txtShape_flowChartMerge": "Flowchart: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Flowchart: Multidocument ", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flowchart: Off-page Connector", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flowchart: Stored data", "PE.Controllers.Main.txtShape_flowChartOr": "Flowchart: Or", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flowchart: Predefined Process", "PE.Controllers.Main.txtShape_flowChartPreparation": "Flowchart: Preparation", "PE.Controllers.Main.txtShape_flowChartProcess": "Flowchart: Process", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Flowchart: Card", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Flowchart: Punched tape", "PE.Controllers.Main.txtShape_flowChartSort": "Flowchart: Sort", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Flowchart: Summing junction", "PE.Controllers.Main.txtShape_flowChartTerminator": "Flowchart: Terminator", "PE.Controllers.Main.txtShape_foldedCorner": "Folded corner", "PE.Controllers.Main.txtShape_frame": "<PERSON>ame", "PE.Controllers.Main.txtShape_halfFrame": "Half frame", "PE.Controllers.Main.txtShape_heart": "Heart", "PE.Controllers.Main.txtShape_heptagon": "Heptagon", "PE.Controllers.Main.txtShape_hexagon": "Hexagon", "PE.Controllers.Main.txtShape_homePlate": "Pentagon", "PE.Controllers.Main.txtShape_horizontalScroll": "Horizontal scroll", "PE.Controllers.Main.txtShape_irregularSeal1": "Explosion 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Explosion 2", "PE.Controllers.Main.txtShape_leftArrow": "Left Arrow", "PE.Controllers.Main.txtShape_leftArrowCallout": "Left <PERSON> Callout", "PE.Controllers.Main.txtShape_leftBrace": "Left Brace", "PE.Controllers.Main.txtShape_leftBracket": "Left Bracket", "PE.Controllers.Main.txtShape_leftRightArrow": "Left right arrow", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Left right arrow callout", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Left right up arrow", "PE.Controllers.Main.txtShape_leftUpArrow": "Left up arrow", "PE.Controllers.Main.txtShape_lightningBolt": "Lightning bolt", "PE.Controllers.Main.txtShape_line": "Line", "PE.Controllers.Main.txtShape_lineWithArrow": "Arrow", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "Double arrow", "PE.Controllers.Main.txtShape_mathDivide": "Division", "PE.Controllers.Main.txtShape_mathEqual": "Equal", "PE.Controllers.Main.txtShape_mathMinus": "Minus", "PE.Controllers.Main.txtShape_mathMultiply": "Multiply", "PE.Controllers.Main.txtShape_mathNotEqual": "Not Equal", "PE.Controllers.Main.txtShape_mathPlus": "Plus", "PE.Controllers.Main.txtShape_moon": "Moon", "PE.Controllers.Main.txtShape_noSmoking": "\"No\" Symbol", "PE.Controllers.Main.txtShape_notchedRightArrow": "Notched right arrow", "PE.Controllers.Main.txtShape_octagon": "Octagon", "PE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "PE.Controllers.Main.txtShape_pentagon": "Pentagon", "PE.Controllers.Main.txtShape_pie": "Pie", "PE.Controllers.Main.txtShape_plaque": "Sign", "PE.Controllers.Main.txtShape_plus": "Plus", "PE.Controllers.Main.txtShape_polyline1": "Scribble", "PE.Controllers.Main.txtShape_polyline2": "Freeform", "PE.Controllers.Main.txtShape_quadArrow": "Quad arrow", "PE.Controllers.Main.txtShape_quadArrowCallout": "Quad arrow callout", "PE.Controllers.Main.txtShape_rect": "Rectangle", "PE.Controllers.Main.txtShape_ribbon": "Down ribbon", "PE.Controllers.Main.txtShape_ribbon2": "Up ribbon", "PE.Controllers.Main.txtShape_rightArrow": "Right Arrow", "PE.Controllers.Main.txtShape_rightArrowCallout": "Right arrow callout", "PE.Controllers.Main.txtShape_rightBrace": "Right brace", "PE.Controllers.Main.txtShape_rightBracket": "Right bracket", "PE.Controllers.Main.txtShape_round1Rect": "Round single corner rectangle", "PE.Controllers.Main.txtShape_round2DiagRect": "Round diagonal corner rectangle", "PE.Controllers.Main.txtShape_round2SameRect": "Round same side corner rectangle", "PE.Controllers.Main.txtShape_roundRect": "Round corner rectangle", "PE.Controllers.Main.txtShape_rtTriangle": "Right triangle", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_snip1Rect": "Snip single corner rectangle", "PE.Controllers.Main.txtShape_snip2DiagRect": "Snip diagonal corner rectangle", "PE.Controllers.Main.txtShape_snip2SameRect": "Snip same side corner rectangle", "PE.Controllers.Main.txtShape_snipRoundRect": "Snip and round single corner rectangle", "PE.Controllers.Main.txtShape_spline": "Curve", "PE.Controllers.Main.txtShape_star10": "10-Point Star", "PE.Controllers.Main.txtShape_star12": "12-Point Star", "PE.Controllers.Main.txtShape_star16": "16-Point Star", "PE.Controllers.Main.txtShape_star24": "24-Point Star", "PE.Controllers.Main.txtShape_star32": "32-Point Star", "PE.Controllers.Main.txtShape_star4": "4-Point Star", "PE.Controllers.Main.txtShape_star5": "5-Point Star", "PE.Controllers.Main.txtShape_star6": "6-<PERSON> Star", "PE.Controllers.Main.txtShape_star7": "7-Point Star", "PE.Controllers.Main.txtShape_star8": "8-Point Star", "PE.Controllers.Main.txtShape_stripedRightArrow": "Striped right arrow", "PE.Controllers.Main.txtShape_sun": "Sun", "PE.Controllers.Main.txtShape_teardrop": "Teardrop", "PE.Controllers.Main.txtShape_textRect": "Text Box", "PE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "PE.Controllers.Main.txtShape_triangle": "Triangle", "PE.Controllers.Main.txtShape_upArrow": "Up Arrow", "PE.Controllers.Main.txtShape_upArrowCallout": "Up arrow callout", "PE.Controllers.Main.txtShape_upDownArrow": "Up down arrow", "PE.Controllers.Main.txtShape_uturnArrow": "U-Turn Arrow", "PE.Controllers.Main.txtShape_verticalScroll": "Vertical scroll", "PE.Controllers.Main.txtShape_wave": "Wave", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval callout", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Rectangular callout", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Rounded rectangular callout", "PE.Controllers.Main.txtSldLtTBlank": "Blank", "PE.Controllers.Main.txtSldLtTChart": "Chart", "PE.Controllers.Main.txtSldLtTChartAndTx": "Chart and text", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Clip Art and text", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Clip Art and vertical text", "PE.Controllers.Main.txtSldLtTCust": "Custom", "PE.Controllers.Main.txtSldLtTDgm": "Diagram", "PE.Controllers.Main.txtSldLtTFourObj": "Four objects", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Media and Text", "PE.Controllers.Main.txtSldLtTObj": "Title and object", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Object and two objects", "PE.Controllers.Main.txtSldLtTObjAndTx": "Object and text", "PE.Controllers.Main.txtSldLtTObjOnly": "Object", "PE.Controllers.Main.txtSldLtTObjOverTx": "Object over Text", "PE.Controllers.Main.txtSldLtTObjTx": "Title, Object, and Caption", "PE.Controllers.Main.txtSldLtTPicTx": "Picture and Caption", "PE.Controllers.Main.txtSldLtTSecHead": "Section Header", "PE.Controllers.Main.txtSldLtTTbl": "Table", "PE.Controllers.Main.txtSldLtTTitle": "Title", "PE.Controllers.Main.txtSldLtTTitleOnly": "Title only", "PE.Controllers.Main.txtSldLtTTwoColTx": "Two column text", "PE.Controllers.Main.txtSldLtTTwoObj": "Two objects", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Two objects and object", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Two objects and text", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Two objects over text", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Two text and two objects", "PE.Controllers.Main.txtSldLtTTx": "Text", "PE.Controllers.Main.txtSldLtTTxAndChart": "Text and chart", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Text and Clip Art", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Text and Media", "PE.Controllers.Main.txtSldLtTTxAndObj": "Text and object", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Text and two objects", "PE.Controllers.Main.txtSldLtTTxOverObj": "Text over object", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Vertical title and text", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Vertical title and text over chart", "PE.Controllers.Main.txtSldLtTVertTx": "Vertical text", "PE.Controllers.Main.txtSlideNumber": "Slide number", "PE.Controllers.Main.txtSlideSubtitle": "Slide subtitle", "PE.Controllers.Main.txtSlideText": "Slide text", "PE.Controllers.Main.txtSlideTitle": "Slide title", "PE.Controllers.Main.txtStarsRibbons": "Stars & Ribbons", "PE.Controllers.Main.txtStart": "Start: ${0}s", "PE.Controllers.Main.txtStop": "Stop", "PE.Controllers.Main.txtTheme_basic": "Basic", "PE.Controllers.Main.txtTheme_blank": "Blank", "PE.Controllers.Main.txtTheme_classic": "Classic", "PE.Controllers.Main.txtTheme_corner": "Corner", "PE.Controllers.Main.txtTheme_dotted": "Dotted", "PE.Controllers.Main.txtTheme_green": "Green", "PE.Controllers.Main.txtTheme_green_leaf": "Green leaf", "PE.Controllers.Main.txtTheme_lines": "Lines", "PE.Controllers.Main.txtTheme_office": "Office", "PE.Controllers.Main.txtTheme_office_theme": "Office Theme", "PE.Controllers.Main.txtTheme_official": "Official", "PE.Controllers.Main.txtTheme_pixel": "Pixel", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Turtle", "PE.Controllers.Main.txtXAxis": "X Axis", "PE.Controllers.Main.txtYAxis": "Y Axis", "PE.Controllers.Main.txtZoom": "Zoom", "PE.Controllers.Main.unknownErrorText": "Unknown error.", "PE.Controllers.Main.unsupportedBrowserErrorText": "Your browser is not supported.", "PE.Controllers.Main.uploadImageExtMessage": "Unknown image format.", "PE.Controllers.Main.uploadImageFileCountMessage": "No images uploaded.", "PE.Controllers.Main.uploadImageSizeMessage": "The image is too big. The maximum size is 25 MB.", "PE.Controllers.Main.uploadImageTextText": "Uploading image...", "PE.Controllers.Main.uploadImageTitleText": "Uploading Image", "PE.Controllers.Main.waitText": "Please, wait...", "PE.Controllers.Main.warnBrowserIE9": "The application has low capabilities on IE9. Use IE10 or higher", "PE.Controllers.Main.warnBrowserZoom": "Your browser current zoom setting is not fully supported. Please reset to the default zoom by pressing Ctrl+0.", "PE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "PE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "PE.Controllers.Main.warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact your administrator to learn more.", "PE.Controllers.Main.warnLicenseExp": "Your license has expired.<br>Please update your license and refresh the page.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "License expired.<br>You have no access to document editing functionality.<br>Please contact your administrator.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "License needs to be renewed.<br>You have a limited access to document editing functionality.<br>Please contact your administrator to get full access", "PE.Controllers.Main.warnLicenseUsersExceeded": "You've reached the user limit for %1 editors. Contact your administrator to learn more.", "PE.Controllers.Main.warnNoLicense": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact %1 sales team for personal upgrade terms.", "PE.Controllers.Main.warnNoLicenseUsers": "You've reached the user limit for %1 editors. Contact %1 sales team for personal upgrade terms.", "PE.Controllers.Main.warnProcessRightsChange": "You have been denied the right to edit the file.", "PE.Controllers.Print.txtPrintRangeInvalid": "Invalid print range", "PE.Controllers.Print.txtPrintRangeSingleRange": "Enter either a single slide number or a single slide range (for example, 5-12). Or you can Print to PDF.", "PE.Controllers.Search.notcriticalErrorTitle": "Warning", "PE.Controllers.Search.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "PE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "PE.Controllers.Search.textReplaceSuccess": "Search has been done. {0} occurrences have been replaced", "PE.Controllers.Search.warnReplaceString": "{0} is not a valid special character for the Replace With box.", "PE.Controllers.Statusbar.textDisconnect": "<b>Connection is lost</b><br>Trying to connect. Please check connection settings.", "PE.Controllers.Statusbar.zoomText": "Zoom {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "The font you are going to save is not available on the current device.<br>The text style will be displayed using one of the system fonts, the saved font will be used when it is available.<br>Do you want to continue?", "PE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "PE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "PE.Controllers.Toolbar.helpTabDesign": "Apply themes, change color schemes and slide size from the newly added Design tab.", "PE.Controllers.Toolbar.helpTabDesignHeader": "Design tab", "PE.Controllers.Toolbar.textAccent": "Accents", "PE.Controllers.Toolbar.textBracket": "Brackets", "PE.Controllers.Toolbar.textFontSizeErr": "The entered value is incorrect.<br>Please enter a numeric value between 1 and 300", "PE.Controllers.Toolbar.textFraction": "Fractions", "PE.Controllers.Toolbar.textFunction": "Functions", "PE.Controllers.Toolbar.textInsert": "Insert", "PE.Controllers.Toolbar.textIntegral": "Integrals", "PE.Controllers.Toolbar.textLargeOperator": "Large operators", "PE.Controllers.Toolbar.textLimitAndLog": "Limits and logarithms", "PE.Controllers.Toolbar.textMatrix": "Matrices", "PE.Controllers.Toolbar.textOperator": "Operators", "PE.Controllers.Toolbar.textRadical": "Radicals", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "Symbols", "PE.Controllers.Toolbar.textWarning": "Warning", "PE.Controllers.Toolbar.txtAccent_Accent": "Acute", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Right-left arrow above", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Leftwards arrow above", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Rightwards arrow above", "PE.Controllers.Toolbar.txtAccent_Bar": "Bar", "PE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "PE.Controllers.Toolbar.txtAccent_BarTop": "Overbar", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Boxed formula (with placeholder)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Boxed formula (example)", "PE.Controllers.Toolbar.txtAccent_Check": "Check", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Overbrace", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC with overbar", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y with overbar", "PE.Controllers.Toolbar.txtAccent_DDDot": "Triple dot", "PE.Controllers.Toolbar.txtAccent_DDot": "Double dot", "PE.Controllers.Toolbar.txtAccent_Dot": "Dot", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Double overbar", "PE.Controllers.Toolbar.txtAccent_Grave": "Grave", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Grouping character below", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Grouping character above", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Leftwards harpoon above", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Rightwards harpoon above", "PE.Controllers.Toolbar.txtAccent_Hat": "Hat", "PE.Controllers.Toolbar.txtAccent_Smile": "Breve", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "Angle brackets", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Angle brackets with separator", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Angle brackets with two separators", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Right angle bracket", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Left angle bracket", "PE.Controllers.Toolbar.txtBracket_Curve": "Curly brackets", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Curly brackets with separator", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Right curly bracket", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Left curly bracket", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Cases (two conditions)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Cases (three conditions)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Stack object", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Stack object in parentheses", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Cases example", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Binomial coefficient", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Binomial coefficient in angle brackets", "PE.Controllers.Toolbar.txtBracket_Line": "Vertical bars", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Right vertical bar", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Left vertical bar", "PE.Controllers.Toolbar.txtBracket_LineDouble": "Double vertical bars", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Right double vertical bar", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Left double vertical bar", "PE.Controllers.Toolbar.txtBracket_LowLim": "Floor", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Right floor", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Left floor", "PE.Controllers.Toolbar.txtBracket_Round": "Parentheses", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parentheses with separator", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Right parenthesis", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Left parenthesis", "PE.Controllers.Toolbar.txtBracket_Square": "Square brackets", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Placeholder between two right square brackets", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Inverted square brackets", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Right square bracket", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Left square bracket", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Placeholder between two left square brackets", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "Double square brackets", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Right double square bracket", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Left double square bracket", "PE.Controllers.Toolbar.txtBracket_UppLim": "Ceiling", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Right ceiling", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Left ceiling", "PE.Controllers.Toolbar.txtFractionDiagonal": "Skewed fraction", "PE.Controllers.Toolbar.txtFractionDifferential_1": "dx over dy", "PE.Controllers.Toolbar.txtFractionDifferential_2": "cap delta y over cap delta x", "PE.Controllers.Toolbar.txtFractionDifferential_3": "partial y over partial x", "PE.Controllers.Toolbar.txtFractionDifferential_4": "delta y over delta x", "PE.Controllers.Toolbar.txtFractionHorizontal": "Linear fraction", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi over 2", "PE.Controllers.Toolbar.txtFractionSmall": "Small fraction", "PE.Controllers.Toolbar.txtFractionVertical": "Stacked fraction", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Inverse cosine function", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperbolic inverse cosine function", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Inverse cotangent function", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperbolic inverse cotangent function", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Inverse cosecant function", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperbolic inverse cosecant function", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Inverse secant function", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperbolic inverse secant function", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Inverse sine function", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperbolic inverse sine function", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Inverse tangent function", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperbolic inverse tangent function", "PE.Controllers.Toolbar.txtFunction_Cos": "Cosine function", "PE.Controllers.Toolbar.txtFunction_Cosh": "Hyperbolic cosine function", "PE.Controllers.Toolbar.txtFunction_Cot": "Cotangent function", "PE.Controllers.Toolbar.txtFunction_Coth": "Hyperbolic cotangent function", "PE.Controllers.Toolbar.txtFunction_Csc": "Cosecant function", "PE.Controllers.Toolbar.txtFunction_Csch": "Hyperbolic cosecant function", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Tangent formula", "PE.Controllers.Toolbar.txtFunction_Sec": "Secant function", "PE.Controllers.Toolbar.txtFunction_Sech": "Hyperbolic secant function", "PE.Controllers.Toolbar.txtFunction_Sin": "Sine function", "PE.Controllers.Toolbar.txtFunction_Sinh": "Hyperbolic sine function", "PE.Controllers.Toolbar.txtFunction_Tan": "Tangent function", "PE.Controllers.Toolbar.txtFunction_Tanh": "Hyperbolic tangent function", "PE.Controllers.Toolbar.txtIntegral": "Integral", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Differential theta", "PE.Controllers.Toolbar.txtIntegral_dx": "Differential x", "PE.Controllers.Toolbar.txtIntegral_dy": "Differential y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral with stacked limits", "PE.Controllers.Toolbar.txtIntegralDouble": "Double integral", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Double integral with stacked limits", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Double integral with limits", "PE.Controllers.Toolbar.txtIntegralOriented": "Contour integral", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Contour integral with stacked limits", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Surface integral", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Surface integral with stacked limits", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Surface integral with limits", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Contour integral with limits", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume integral", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volume integral with stacked limits", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volume integral with limits", "PE.Controllers.Toolbar.txtIntegralSubSup": "Integral with limits", "PE.Controllers.Toolbar.txtIntegralTriple": "Triple integral", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple integral with stacked limits", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple integral with limits", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Logical And", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Logical And with lower limit", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Logical And with limits", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Logical And with subscript lower limit", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Logical And with subscript/superscript limits", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-product", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Co-product with lower limit", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Co-product with limits", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-product with subscript lower limit", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Co-product with subscript/superscript limits", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summation over k of n choose k", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summation from i equal zero to n", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summation example using two indices", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Product example", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Union example", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Logical Or", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Logical Or with lower limit", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Logical Or with limits", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Logical Or with subscript lower limit", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Logical Or with subscript/superscript limits", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersection", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersection with lower limit", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersection with limits", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersection with subscript lower limit", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersection with subscript/superscript limits", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Product with lower limit", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Product with limits", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Product with subscript lower limit", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Product with subscript/superscript limits", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Summation", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summation with lower limit", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summation with limits", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summation with subscript lower limit", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summation with subscript/superscript limits", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union with lower limit", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union with limits", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union with subscript lower limit", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union with subscript/superscript limits", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Limit example", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Maximum example", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Natural logarithm", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logarithm", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarithm", "PE.Controllers.Toolbar.txtLimitLog_Max": "Maximum", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2 empty matrix", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 empty matrix", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 empty matrix", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 empty matrix", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Empty 2 by 2 matrix in double vertical bars", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Empty 2 by 2 determinant", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Empty 2 by 2 matrix in parentheses", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Empty 2 by 2 matrix in brackets", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 empty matrix", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 empty matrix", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 empty matrix", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 empty matrix", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Baseline dots", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Midline dots", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonal dots", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Vertical dots", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "Sparse matrix in parentheses", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "Sparse matrix in brackets", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 identity matrix with zeros", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2 identity matrix with blank off-diagonal cells", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 identity matrix with zeros", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 identity matrix with blank off-diagonal cells", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Right-left arrow below", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Right-left arrow above", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Leftwards arrow below", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Leftwards arrow above", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Rightwards arrow below", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Rightwards arrow above", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Colon equal", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Yields", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Delta yields", "PE.Controllers.Toolbar.txtOperator_Definition": "Equal to by definition", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta equal to", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Right-left double arrow below", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Right-left double arrow above", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Leftwards arrow below", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Leftwards arrow above", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Rightwards arrow below", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Rightwards arrow above", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Equal equal", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "Minus equal", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus equal", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Measured by", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Right hand side of quadratic formula", "PE.Controllers.Toolbar.txtRadicalCustom_2": "Square root of a squared plus b squared", "PE.Controllers.Toolbar.txtRadicalRoot_2": "Square root with degree", "PE.Controllers.Toolbar.txtRadicalRoot_3": "Cubic root", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Radical with degree", "PE.Controllers.Toolbar.txtRadicalSqrt": "Square root", "PE.Controllers.Toolbar.txtScriptCustom_1": "x subscript y squared", "PE.Controllers.Toolbar.txtScriptCustom_2": "e to the minus i omega t", "PE.Controllers.Toolbar.txtScriptCustom_3": "x squared", "PE.Controllers.Toolbar.txtScriptCustom_4": "Y left superscript n left subscript one", "PE.Controllers.Toolbar.txtScriptSub": "Subscript", "PE.Controllers.Toolbar.txtScriptSubSup": "Subscript-superscript", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Left subscript-superscript", "PE.Controllers.Toolbar.txtScriptSup": "Superscript", "PE.Controllers.Toolbar.txtSymbol_about": "Approximately", "PE.Controllers.Toolbar.txtSymbol_additional": "Complement", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "PE.Controllers.Toolbar.txtSymbol_approx": "Almost equal to", "PE.Controllers.Toolbar.txtSymbol_ast": "Asterisk operator", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Bet", "PE.Controllers.Toolbar.txtSymbol_bullet": "Bullet operator", "PE.Controllers.Toolbar.txtSymbol_cap": "Intersection", "PE.Controllers.Toolbar.txtSymbol_cbrt": "Cube root", "PE.Controllers.Toolbar.txtSymbol_cdots": "Midline horizontal ellipsis", "PE.Controllers.Toolbar.txtSymbol_celsius": "Degrees Celsius", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Approximately equal to", "PE.Controllers.Toolbar.txtSymbol_cup": "Union", "PE.Controllers.Toolbar.txtSymbol_ddots": "Down right diagonal ellipsis", "PE.Controllers.Toolbar.txtSymbol_degree": "Degrees", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Division sign", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Down arrow", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Empty set", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "Equal", "PE.Controllers.Toolbar.txtSymbol_equiv": "Identical to", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "There exist", "PE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Degrees Fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "For all", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "Greater than or equal to", "PE.Controllers.Toolbar.txtSymbol_gg": "Much greater than", "PE.Controllers.Toolbar.txtSymbol_greater": "Greater than", "PE.Controllers.Toolbar.txtSymbol_in": "Element of", "PE.Controllers.Toolbar.txtSymbol_inc": "Increment", "PE.Controllers.Toolbar.txtSymbol_infinity": "Infinity", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Left arrow", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Left-right arrow", "PE.Controllers.Toolbar.txtSymbol_leq": "Less than or equal to", "PE.Controllers.Toolbar.txtSymbol_less": "Less than", "PE.Controllers.Toolbar.txtSymbol_ll": "Much less than", "PE.Controllers.Toolbar.txtSymbol_minus": "Minus", "PE.Controllers.Toolbar.txtSymbol_mp": "Minus plus", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "Not equal to", "PE.Controllers.Toolbar.txtSymbol_ni": "Contains as member", "PE.Controllers.Toolbar.txtSymbol_not": "Not sign", "PE.Controllers.Toolbar.txtSymbol_notexists": "There does not exist", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Partial differential", "PE.Controllers.Toolbar.txtSymbol_percent": "Percentage", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Plus", "PE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "PE.Controllers.Toolbar.txtSymbol_propto": "Proportional to", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "Fourth root", "PE.Controllers.Toolbar.txtSymbol_qed": "End of proof", "PE.Controllers.Toolbar.txtSymbol_rddots": "Up right diagonal ellipsis", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "Right arrow", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Radical sign", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "Therefore", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "Multiplication sign", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Up arrow", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon variant", "PE.Controllers.Toolbar.txtSymbol_varphi": "Phi variant", "PE.Controllers.Toolbar.txtSymbol_varpi": "Pi variant", "PE.Controllers.Toolbar.txtSymbol_varrho": "Rho variant", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma variant", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Theta variant", "PE.Controllers.Toolbar.txtSymbol_vdots": "Vertical ellipsis", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "Fit to slide", "PE.Controllers.Viewport.textFitWidth": "Fit to width", "PE.Views.Animation.str0_5": "0.5 s (Very Fast)", "PE.Views.Animation.str1": "1 s (Fast)", "PE.Views.Animation.str2": "2 s (Medium)", "PE.Views.Animation.str20": "20 s (Extremely Slow)", "PE.Views.Animation.str3": "3 s (Slow)", "PE.Views.Animation.str5": "5 s (Very Slow)", "PE.Views.Animation.strDelay": "Delay", "PE.Views.Animation.strDuration": "Duration", "PE.Views.Animation.strRepeat": "Repeat", "PE.Views.Animation.strRewind": "Rewind", "PE.Views.Animation.strStart": "Start", "PE.Views.Animation.strTrigger": "<PERSON><PERSON>", "PE.Views.Animation.textAutoPreview": "AutoPreview", "PE.Views.Animation.textMoreEffects": "Show more effects", "PE.Views.Animation.textMoveEarlier": "Move earlier", "PE.Views.Animation.textMoveLater": "Move Later", "PE.Views.Animation.textMultiple": "Multiple", "PE.Views.Animation.textNone": "None", "PE.Views.Animation.textNoRepeat": "(none)", "PE.Views.Animation.textOnClickOf": "On Click of", "PE.Views.Animation.textOnClickSequence": "On Click Sequence", "PE.Views.Animation.textStartAfterPrevious": "After previous", "PE.Views.Animation.textStartOnClick": "On Click", "PE.Views.Animation.textStartWithPrevious": "With Previous", "PE.Views.Animation.textUntilEndOfSlide": "Until end of slide", "PE.Views.Animation.textUntilNextClick": "Until next click", "PE.Views.Animation.txtAddEffect": "Add Animation", "PE.Views.Animation.txtAnimationPane": "Animation Pane", "PE.Views.Animation.txtParameters": "Options", "PE.Views.Animation.txtPreview": "Preview", "PE.Views.Animation.txtSec": "s", "PE.Views.AnimationDialog.textPreviewEffect": "Preview effect", "PE.Views.AnimationDialog.textTitle": "More effects", "PE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "PE.Views.ChartSettings.text3dHeight": "Height (% of base)", "PE.Views.ChartSettings.text3dRotation": "3D Rotation", "PE.Views.ChartSettings.textAdvanced": "Show advanced settings", "PE.Views.ChartSettings.textAutoscale": "Autoscale", "PE.Views.ChartSettings.textChartType": "Change chart type", "PE.Views.ChartSettings.textDefault": "Default rotation", "PE.Views.ChartSettings.textDown": "Down", "PE.Views.ChartSettings.textEditData": "Edit data", "PE.Views.ChartSettings.textHeight": "Height", "PE.Views.ChartSettings.textKeepRatio": "Constant proportions", "PE.Views.ChartSettings.textLeft": "Left", "PE.Views.ChartSettings.textNarrow": "Narrow field of view", "PE.Views.ChartSettings.textPerspective": "Perspective", "PE.Views.ChartSettings.textRight": "Right", "PE.Views.ChartSettings.textRightAngle": "Right angle axes", "PE.Views.ChartSettings.textSize": "Size", "PE.Views.ChartSettings.textStyle": "Style", "PE.Views.ChartSettings.textUp": "Up", "PE.Views.ChartSettings.textWiden": "Widen field of view", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textX": "X rotation", "PE.Views.ChartSettings.textY": "Y rotation", "PE.Views.ChartSettingsAdvanced.textAlt": "Alternative text", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Description", "PE.Views.ChartSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart, or table.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Title", "PE.Views.ChartSettingsAdvanced.textCenter": "Center", "PE.Views.ChartSettingsAdvanced.textChartName": "Chart name", "PE.Views.ChartSettingsAdvanced.textFrom": "From", "PE.Views.ChartSettingsAdvanced.textGeneral": "General", "PE.Views.ChartSettingsAdvanced.textHeight": "Height", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Constant proportions", "PE.Views.ChartSettingsAdvanced.textPlacement": "Placement", "PE.Views.ChartSettingsAdvanced.textPosition": "Position", "PE.Views.ChartSettingsAdvanced.textSize": "Size", "PE.Views.ChartSettingsAdvanced.textTitle": "Chart - Advanced settings", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "Top left corner", "PE.Views.ChartSettingsAdvanced.textVertical": "Vertical", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Set default format for {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Set as default", "PE.Views.DateTimeDialog.textFormat": "Formats", "PE.Views.DateTimeDialog.textLang": "Language", "PE.Views.DateTimeDialog.textUpdate": "Update automatically", "PE.Views.DateTimeDialog.txtTitle": "Date & Time", "PE.Views.DocumentHolder.aboveText": "Above", "PE.Views.DocumentHolder.addCommentText": "Add comment", "PE.Views.DocumentHolder.advancedChartText": "Chart advanced settings", "PE.Views.DocumentHolder.advancedEquationText": "Equation settings", "PE.Views.DocumentHolder.advancedImageText": "Image advanced settings", "PE.Views.DocumentHolder.advancedParagraphText": "Paragraph advanced settings", "PE.Views.DocumentHolder.advancedShapeText": "Shape advanced settings", "PE.Views.DocumentHolder.advancedTableText": "Table advanced settings", "PE.Views.DocumentHolder.alignmentText": "Alignment", "PE.Views.DocumentHolder.allLinearText": "All - Linear", "PE.Views.DocumentHolder.allProfText": "All - Professional", "PE.Views.DocumentHolder.belowText": "Below", "PE.Views.DocumentHolder.cellAlignText": "Cell vertical alignment", "PE.Views.DocumentHolder.cellText": "Cell", "PE.Views.DocumentHolder.centerText": "Center", "PE.Views.DocumentHolder.columnText": "Column", "PE.Views.DocumentHolder.currLinearText": "Current - Linear", "PE.Views.DocumentHolder.currProfText": "Current - Professional", "PE.Views.DocumentHolder.deleteColumnText": "Delete Column", "PE.Views.DocumentHolder.deleteRowText": "Delete row", "PE.Views.DocumentHolder.deleteTableText": "Delete table", "PE.Views.DocumentHolder.deleteText": "Delete", "PE.Views.DocumentHolder.direct270Text": "Rotate text up", "PE.Views.DocumentHolder.direct90Text": "Rotate text down", "PE.Views.DocumentHolder.directHText": "Horizontal", "PE.Views.DocumentHolder.directionText": "Text direction", "PE.Views.DocumentHolder.editChartText": "Edit data", "PE.Views.DocumentHolder.editHyperlinkText": "Edit hyperlink", "PE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "PE.Views.DocumentHolder.hyperlinkText": "Hyperlink", "PE.Views.DocumentHolder.ignoreAllSpellText": "Ignore all", "PE.Views.DocumentHolder.ignoreSpellText": "Ignore", "PE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> left", "PE.Views.DocumentHolder.insertColumnRightText": "Column right", "PE.Views.DocumentHolder.insertColumnText": "Insert column", "PE.Views.DocumentHolder.insertRowAboveText": "Row Above", "PE.Views.DocumentHolder.insertRowBelowText": "Row Below", "PE.Views.DocumentHolder.insertRowText": "Insert row", "PE.Views.DocumentHolder.insertText": "Insert", "PE.Views.DocumentHolder.langText": "Select language", "PE.Views.DocumentHolder.latexText": "LaTeX", "PE.Views.DocumentHolder.leftText": "Left", "PE.Views.DocumentHolder.loadSpellText": "Loading variants...", "PE.Views.DocumentHolder.mergeCellsText": "Merge cells", "PE.Views.DocumentHolder.mniCustomTable": "Insert custom table", "PE.Views.DocumentHolder.moreText": "More variants...", "PE.Views.DocumentHolder.noSpellVariantsText": "No variants", "PE.Views.DocumentHolder.originalSizeText": "Actual size", "PE.Views.DocumentHolder.removeHyperlinkText": "Remove hyperlink", "PE.Views.DocumentHolder.rightText": "Right", "PE.Views.DocumentHolder.rowText": "Row", "PE.Views.DocumentHolder.selectText": "Select", "PE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "PE.Views.DocumentHolder.spellcheckText": "Spellcheck", "PE.Views.DocumentHolder.splitCellsText": "Split cell...", "PE.Views.DocumentHolder.splitCellTitleText": "Split Cell", "PE.Views.DocumentHolder.tableText": "Table", "PE.Views.DocumentHolder.textAddHGuides": "Add horizontal guide", "PE.Views.DocumentHolder.textAddVGuides": "Add Vertical Guide", "PE.Views.DocumentHolder.textArrangeBack": "Send to background", "PE.Views.DocumentHolder.textArrangeBackward": "Send backward", "PE.Views.DocumentHolder.textArrangeForward": "Bring forward", "PE.Views.DocumentHolder.textArrangeFront": "Bring to foreground", "PE.Views.DocumentHolder.textClearGuides": "Clear guides", "PE.Views.DocumentHolder.textCm": "cm", "PE.Views.DocumentHolder.textCopy": "Copy", "PE.Views.DocumentHolder.textCrop": "Crop", "PE.Views.DocumentHolder.textCropFill": "Fill", "PE.Views.DocumentHolder.textCropFit": "Fit", "PE.Views.DocumentHolder.textCustom": "Custom", "PE.Views.DocumentHolder.textCut": "Cut", "PE.Views.DocumentHolder.textDeleteGuide": "Delete guide", "PE.Views.DocumentHolder.textDeleteLayout": "Delete Layout", "PE.Views.DocumentHolder.textDeleteMaster": "Delete Master", "PE.Views.DocumentHolder.textDistributeCols": "Distribute columns", "PE.Views.DocumentHolder.textDistributeRows": "Distribute rows", "PE.Views.DocumentHolder.textDuplicateLayout": "Duplicate Layout", "PE.Views.DocumentHolder.textDuplicateSlideMaster": "Duplicate Slide Master", "PE.Views.DocumentHolder.textEditObject": "Edit object", "PE.Views.DocumentHolder.textEditPoints": "Edit points", "PE.Views.DocumentHolder.textFlipH": "Flip horizontally", "PE.Views.DocumentHolder.textFlipV": "Flip vertically", "PE.Views.DocumentHolder.textFromFile": "From file", "PE.Views.DocumentHolder.textFromStorage": "From Storage", "PE.Views.DocumentHolder.textFromUrl": "From URL", "PE.Views.DocumentHolder.textGridlines": "Gridlines", "PE.Views.DocumentHolder.textGuides": "Guides", "PE.Views.DocumentHolder.textInsertLayout": "Insert Layout", "PE.Views.DocumentHolder.textInsertSlideMaster": "Insert Slide Master", "PE.Views.DocumentHolder.textNextPage": "Next Slide", "PE.Views.DocumentHolder.textPaste": "Paste", "PE.Views.DocumentHolder.textPrevPage": "Previous slide", "PE.Views.DocumentHolder.textRemove": "Remove", "PE.Views.DocumentHolder.textRenameLayout": "<PERSON><PERSON>out", "PE.Views.DocumentHolder.textRenameMaster": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textReplace": "Replace image", "PE.Views.DocumentHolder.textResetCrop": "Reset crop", "PE.Views.DocumentHolder.textRotate": "Rotate", "PE.Views.DocumentHolder.textRotate270": "Rotate 90° Counterclockwise", "PE.Views.DocumentHolder.textRotate90": "Rotate 90° Clockwise", "PE.Views.DocumentHolder.textRulers": "Rulers", "PE.Views.DocumentHolder.textSaveAsPicture": "Save as picture", "PE.Views.DocumentHolder.textShapeAlignBottom": "Align Bottom", "PE.Views.DocumentHolder.textShapeAlignCenter": "Align center", "PE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON> left", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Align middle", "PE.Views.DocumentHolder.textShapeAlignRight": "Align right", "PE.Views.DocumentHolder.textShapeAlignTop": "Align top", "PE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "PE.Views.DocumentHolder.textShowGridlines": "Show gridlines", "PE.Views.DocumentHolder.textShowGuides": "Show Guides", "PE.Views.DocumentHolder.textSlideSettings": "Slide settings", "PE.Views.DocumentHolder.textSmartGuides": "Smart Guides", "PE.Views.DocumentHolder.textSnapObjects": "Snap object to grid", "PE.Views.DocumentHolder.textStartAfterPrevious": "Start After Previous", "PE.Views.DocumentHolder.textStartOnClick": "Start On Click", "PE.Views.DocumentHolder.textStartWithPrevious": "Start With Previous", "PE.Views.DocumentHolder.textUndo": "Undo", "PE.Views.DocumentHolder.tipGuides": "Show guides", "PE.Views.DocumentHolder.tipIsLocked": "This element is currently being edited by another user.", "PE.Views.DocumentHolder.toDictionaryText": "Add to dictionary", "PE.Views.DocumentHolder.txtAddBottom": "Add bottom border", "PE.Views.DocumentHolder.txtAddFractionBar": "Add fraction bar", "PE.Views.DocumentHolder.txtAddHor": "Add horizontal line", "PE.Views.DocumentHolder.txtAddLB": "Add left bottom line", "PE.Views.DocumentHolder.txtAddLeft": "Add left border", "PE.Views.DocumentHolder.txtAddLT": "Add left top line", "PE.Views.DocumentHolder.txtAddRight": "Add right border", "PE.Views.DocumentHolder.txtAddTop": "Add top border", "PE.Views.DocumentHolder.txtAddVer": "Add vertical line", "PE.Views.DocumentHolder.txtAlign": "Align", "PE.Views.DocumentHolder.txtAlignToChar": "Align to character", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtBackground": "Background", "PE.Views.DocumentHolder.txtBorderProps": "Border properties", "PE.Views.DocumentHolder.txtBottom": "Bottom", "PE.Views.DocumentHolder.txtChangeLayout": "Change layout", "PE.Views.DocumentHolder.txtChangeTheme": "Change theme", "PE.Views.DocumentHolder.txtColumnAlign": "Column alignment", "PE.Views.DocumentHolder.txtDecreaseArg": "Decrease argument size", "PE.Views.DocumentHolder.txtDeleteArg": "Delete argument", "PE.Views.DocumentHolder.txtDeleteBreak": "Delete manual break", "PE.Views.DocumentHolder.txtDeleteChars": "Delete enclosing characters", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Delete enclosing characters and separators", "PE.Views.DocumentHolder.txtDeleteEq": "Delete equation", "PE.Views.DocumentHolder.txtDeleteGroupChar": "Delete char", "PE.Views.DocumentHolder.txtDeleteRadical": "Delete radical", "PE.Views.DocumentHolder.txtDeleteSlide": "Delete slide", "PE.Views.DocumentHolder.txtDistribHor": "Distribute horizontally", "PE.Views.DocumentHolder.txtDistribVert": "Distribute vertically", "PE.Views.DocumentHolder.txtDuplicateSlide": "Duplicate slide", "PE.Views.DocumentHolder.txtFractionLinear": "Change to linear fraction", "PE.Views.DocumentHolder.txtFractionSkewed": "Change to skewed fraction", "PE.Views.DocumentHolder.txtFractionStacked": "Change to stacked fraction", "PE.Views.DocumentHolder.txtGroup": "Group", "PE.Views.DocumentHolder.txtGroupCharOver": "Char over text", "PE.Views.DocumentHolder.txtGroupCharUnder": "Char under text", "PE.Views.DocumentHolder.txtHideBottom": "Hide bottom border", "PE.Views.DocumentHolder.txtHideBottomLimit": "Hide bottom limit", "PE.Views.DocumentHolder.txtHideCloseBracket": "Hide closing bracket", "PE.Views.DocumentHolder.txtHideDegree": "Hide degree", "PE.Views.DocumentHolder.txtHideHor": "Hide horizontal line", "PE.Views.DocumentHolder.txtHideLB": "<PERSON><PERSON> left bottom line", "PE.Views.DocumentHolder.txtHideLeft": "<PERSON><PERSON> left border", "PE.Views.DocumentHolder.txtHideLT": "<PERSON><PERSON> left top line", "PE.Views.DocumentHolder.txtHideOpenBracket": "Hide opening bracket", "PE.Views.DocumentHolder.txtHidePlaceholder": "Hide placeholder", "PE.Views.DocumentHolder.txtHideRight": "Hide right border", "PE.Views.DocumentHolder.txtHideTop": "Hide top border", "PE.Views.DocumentHolder.txtHideTopLimit": "Hide top limit", "PE.Views.DocumentHolder.txtHideVer": "Hide vertical line", "PE.Views.DocumentHolder.txtIncreaseArg": "Increase argument size", "PE.Views.DocumentHolder.txtInsAudio": "Insert audio", "PE.Views.DocumentHolder.txtInsChart": "Insert chart", "PE.Views.DocumentHolder.txtInsertArgAfter": "Insert argument after", "PE.Views.DocumentHolder.txtInsertArgBefore": "Insert argument before", "PE.Views.DocumentHolder.txtInsertBreak": "Insert manual break", "PE.Views.DocumentHolder.txtInsertEqAfter": "Insert equation after", "PE.Views.DocumentHolder.txtInsertEqBefore": "Insert equation before", "PE.Views.DocumentHolder.txtInsImage": "Insert image from file", "PE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "PE.Views.DocumentHolder.txtInsSmartArt": "Insert SmartArt", "PE.Views.DocumentHolder.txtInsTable": "Insert table", "PE.Views.DocumentHolder.txtInsVideo": "Insert video", "PE.Views.DocumentHolder.txtKeepTextOnly": "Keep text only", "PE.Views.DocumentHolder.txtLimitChange": "Change limits location", "PE.Views.DocumentHolder.txtLimitOver": "Limit over text", "PE.Views.DocumentHolder.txtLimitUnder": "Limit under text", "PE.Views.DocumentHolder.txtMatchBrackets": "Match brackets to argument height", "PE.Views.DocumentHolder.txtMatrixAlign": "Matrix alignment", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Move slide to end", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Move slide to beginning", "PE.Views.DocumentHolder.txtNewSlide": "New slide", "PE.Views.DocumentHolder.txtOverbar": "Bar over text", "PE.Views.DocumentHolder.txtPasteDestFormat": "Use destination theme", "PE.Views.DocumentHolder.txtPastePicture": "Picture", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Keep source formatting", "PE.Views.DocumentHolder.txtPressLink": "Press {0} and click link", "PE.Views.DocumentHolder.txtPreview": "Start slideshow", "PE.Views.DocumentHolder.txtPrintSelection": "Print selection", "PE.Views.DocumentHolder.txtRemFractionBar": "Remove fraction bar", "PE.Views.DocumentHolder.txtRemLimit": "Remove limit", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Remove accent character", "PE.Views.DocumentHolder.txtRemoveBar": "Remove bar", "PE.Views.DocumentHolder.txtRemScripts": "Remove scripts", "PE.Views.DocumentHolder.txtRemSubscript": "Remove subscript", "PE.Views.DocumentHolder.txtRemSuperscript": "Remove superscript", "PE.Views.DocumentHolder.txtResetLayout": "Reset slide", "PE.Views.DocumentHolder.txtScriptsAfter": "Scripts after text", "PE.Views.DocumentHolder.txtScriptsBefore": "Scripts before text", "PE.Views.DocumentHolder.txtSelectAll": "Select all", "PE.Views.DocumentHolder.txtShowBottomLimit": "Show bottom limit", "PE.Views.DocumentHolder.txtShowCloseBracket": "Show closing bracket", "PE.Views.DocumentHolder.txtShowDegree": "Show degree", "PE.Views.DocumentHolder.txtShowOpenBracket": "Show opening bracket", "PE.Views.DocumentHolder.txtShowPlaceholder": "Show placeholder", "PE.Views.DocumentHolder.txtShowTopLimit": "Show top limit", "PE.Views.DocumentHolder.txtSlide": "Slide", "PE.Views.DocumentHolder.txtSlideHide": "Hide slide", "PE.Views.DocumentHolder.txtStretchBrackets": "Stretch brackets", "PE.Views.DocumentHolder.txtTop": "Top", "PE.Views.DocumentHolder.txtUnderbar": "Bar under text", "PE.Views.DocumentHolder.txtUngroup": "Ungroup", "PE.Views.DocumentHolder.txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?", "PE.Views.DocumentHolder.unicodeText": "Unicode", "PE.Views.DocumentHolder.vertAlignText": "Vertical alignment", "PE.Views.DocumentPreview.goToSlideText": "Go to slide", "PE.Views.DocumentPreview.slideIndexText": "Slide {0} of {1}", "PE.Views.DocumentPreview.txtClose": "Close slideshow", "PE.Views.DocumentPreview.txtDraw": "Draw", "PE.Views.DocumentPreview.txtEndSlideshow": "End slideshow", "PE.Views.DocumentPreview.txtEraser": "Eraser", "PE.Views.DocumentPreview.txtEraseScreen": "Erase screen", "PE.Views.DocumentPreview.txtExitFullScreen": "Exit full screen", "PE.Views.DocumentPreview.txtFinalMessage": "The end of slide preview. <PERSON>lick to exit.", "PE.Views.DocumentPreview.txtFullScreen": "Full screen", "PE.Views.DocumentPreview.txtHighlighter": "Highlighter", "PE.Views.DocumentPreview.txtInkColor": "Ink color", "PE.Views.DocumentPreview.txtNext": "Next slide", "PE.Views.DocumentPreview.txtPageNumInvalid": "Invalid slide number", "PE.Views.DocumentPreview.txtPause": "Pause presentation", "PE.Views.DocumentPreview.txtPen": "Pen", "PE.Views.DocumentPreview.txtPlay": "Start presentation", "PE.Views.DocumentPreview.txtPrev": "Previous slide", "PE.Views.DocumentPreview.txtReset": "Reset", "PE.Views.FileMenu.ariaFileMenu": "File menu", "PE.Views.FileMenu.btnAboutCaption": "About", "PE.Views.FileMenu.btnBackCaption": "Open File Location", "PE.Views.FileMenu.btnCloseEditor": "Close File", "PE.Views.FileMenu.btnCloseMenuCaption": "Back", "PE.Views.FileMenu.btnCreateNewCaption": "Create New", "PE.Views.FileMenu.btnDownloadCaption": "Download As", "PE.Views.FileMenu.btnExitCaption": "Close", "PE.Views.FileMenu.btnFileOpenCaption": "Open", "PE.Views.FileMenu.btnHelpCaption": "Help", "PE.Views.FileMenu.btnHistoryCaption": "Version History", "PE.Views.FileMenu.btnInfoCaption": "Info", "PE.Views.FileMenu.btnPrintCaption": "Print", "PE.Views.FileMenu.btnProtectCaption": "Protect", "PE.Views.FileMenu.btnRecentFilesCaption": "Open Recent", "PE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnReturnCaption": "Back to Presentation", "PE.Views.FileMenu.btnRightsCaption": "Access Rights", "PE.Views.FileMenu.btnSaveAsCaption": "Save As", "PE.Views.FileMenu.btnSaveCaption": "Save", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Save Copy As", "PE.Views.FileMenu.btnSettingsCaption": "Advanced Settings", "PE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "PE.Views.FileMenu.btnToEditCaption": "Edit Presentation", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Blank Presentation", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Create New", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Apply", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Add Author", "PE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Add text", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Application", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Author", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Change access rights", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Comment", "PE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Created", "PE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Last modified by", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Last modified", "PE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Owner", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Location", "PE.Views.FileMenuPanels.DocumentInfo.txtPresentationInfo": "Presentation Info", "PE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "PE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Persons who have rights", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subject", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Title", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Uploaded", "PE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "PE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access Rights", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Change access rights", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Persons who have rights", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Warning", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "With password", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "Protect Presentation", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "With signature", "PE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the presentation.<br>The presentation is protected from editing.", "PE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the presentation by adding an<br>invisible digital signature", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Edit presentation", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Editing will remove signatures from the presentation.<br>Continue?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "This presentation has been protected by password", "PE.Views.FileMenuPanels.ProtectDoc.txtProtectPresentation": "Encrypt this presentation with a password", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Valid signatures have been added to the presentation. The presentation is protected from editing.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Some of the digital signatures in presentation are invalid or could not be verified. The presentation is protected from editing.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "View signatures", "PE.Views.FileMenuPanels.Settings.okButtonText": "Apply", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Co-editing Mode", "PE.Views.FileMenuPanels.Settings.strFast": "Fast", "PE.Views.FileMenuPanels.Settings.strFontRender": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignore words in UPPERCASE", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignore words with numbers", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Show the Paste Options button when the content is pasted", "PE.Views.FileMenuPanels.Settings.strRTLSupport": "RTL interface", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Show changes from other users", "PE.Views.FileMenuPanels.Settings.strStrict": "Strict", "PE.Views.FileMenuPanels.Settings.strTabStyle": "Tab style", "PE.Views.FileMenuPanels.Settings.strTheme": "Interface theme", "PE.Views.FileMenuPanels.Settings.strUnit": "Unit of measurement", "PE.Views.FileMenuPanels.Settings.strZoom": "Default Zoom Value", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Every 10 minutes", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Every 30 minutes", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Every 5 minutes", "PE.Views.FileMenuPanels.Settings.text60Minutes": "Every hour", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Alignment guides", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Autorecover", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Autosave", "PE.Views.FileMenuPanels.Settings.textDisabled": "Disabled", "PE.Views.FileMenuPanels.Settings.textFill": "Fill", "PE.Views.FileMenuPanels.Settings.textForceSave": "Saving intermediate versions", "PE.Views.FileMenuPanels.Settings.textLine": "Line", "PE.Views.FileMenuPanels.Settings.textMinute": "Every minute", "PE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "Advanced settings", "PE.Views.FileMenuPanels.Settings.txtAll": "View All", "PE.Views.FileMenuPanels.Settings.txtAppearance": "Appearance", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "AutoCorrect options...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Default cache mode", "PE.Views.FileMenuPanels.Settings.txtCm": "Centimeter", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Collaboration", "PE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "Customize quick access", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Editing and saving", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Real-time co-editing. All changes are saved automatically", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Fit to slide", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Fit to width", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Hieroglyphs", "PE.Views.FileMenuPanels.Settings.txtInch": "Inch", "PE.Views.FileMenuPanels.Settings.txtLast": "View Last", "PE.Views.FileMenuPanels.Settings.txtLastUsed": "Last used", "PE.Views.FileMenuPanels.Settings.txtMac": "as OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "Native", "PE.Views.FileMenuPanels.Settings.txtProofing": "Proofing", "PE.Views.FileMenuPanels.Settings.txtPt": "Point", "PE.Views.FileMenuPanels.Settings.txtQuickPrint": "Show the Quick Print button in the editor header", "PE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Enable All", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Enable all macros without a notification", "PE.Views.FileMenuPanels.Settings.txtScreenReader": "Turn on screen reader support", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Spell Checking", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Disable All", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Disable all macros without a notification", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Use the \"Save\" button to sync the changes you and others make", "PE.Views.FileMenuPanels.Settings.txtTabBack": "Use toolbar color as tabs background", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Show Notification", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Disable all macros with a notification", "PE.Views.FileMenuPanels.Settings.txtWin": "as Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "Workspace", "PE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "PE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save Copy As", "PE.Views.GridSettings.textCm": "cm", "PE.Views.GridSettings.textCustom": "Custom", "PE.Views.GridSettings.textSpacing": "Spacing", "PE.Views.GridSettings.textTitle": "Grid settings", "PE.Views.HeaderFooterDialog.applyAllText": "Apply to all", "PE.Views.HeaderFooterDialog.applyText": "Apply", "PE.Views.HeaderFooterDialog.diffLanguage": "You can’t use a date format in a different language than the slide master.<br>To change the master, click 'Apply to all' instead of 'Apply'", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Warning", "PE.Views.HeaderFooterDialog.textDateTime": "Date and time", "PE.Views.HeaderFooterDialog.textFixed": "Fixed", "PE.Views.HeaderFooterDialog.textFormat": "Formats", "PE.Views.HeaderFooterDialog.textHFTitle": "Header/Footer settings", "PE.Views.HeaderFooterDialog.textLang": "Language", "PE.Views.HeaderFooterDialog.textNotes": "Notes and handouts", "PE.Views.HeaderFooterDialog.textNotTitle": "Don't show on title slide", "PE.Views.HeaderFooterDialog.textPageNum": "Page number", "PE.Views.HeaderFooterDialog.textPreview": "Preview", "PE.Views.HeaderFooterDialog.textSlide": "Slide", "PE.Views.HeaderFooterDialog.textSlideNum": "Slide number", "PE.Views.HeaderFooterDialog.textUpdate": "Update automatically", "PE.Views.HeaderFooterDialog.txtFooter": "Footer", "PE.Views.HeaderFooterDialog.txtHeader": "Header", "PE.Views.HyperlinkSettingsDialog.strDisplay": "Display", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Link to", "PE.Views.HyperlinkSettingsDialog.textDefault": "Selected text fragment", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Enter caption here", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Enter link here", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Enter tooltip here", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "External link", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Slide in this presentation", "PE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "PE.Views.HyperlinkSettingsDialog.textSlides": "Slides", "PE.Views.HyperlinkSettingsDialog.textTipText": "ScreenTip text", "PE.Views.HyperlinkSettingsDialog.textTitle": "Hyperlink settings", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "This field is required", "PE.Views.HyperlinkSettingsDialog.txtFirst": "First slide", "PE.Views.HyperlinkSettingsDialog.txtLast": "Last slide", "PE.Views.HyperlinkSettingsDialog.txtNext": "Next slide", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "This field should be a URL in the \"http://www.example.com\" format", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Previous slide", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "This field is limited to 2083 characters", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Slide", "PE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "PE.Views.ImageSettings.strTransparency": "Opacity", "PE.Views.ImageSettings.textAdvanced": "Show advanced settings", "PE.Views.ImageSettings.textCrop": "Crop", "PE.Views.ImageSettings.textCropFill": "Fill", "PE.Views.ImageSettings.textCropFit": "Fit", "PE.Views.ImageSettings.textCropToShape": "Crop to shape", "PE.Views.ImageSettings.textEdit": "Edit", "PE.Views.ImageSettings.textEditObject": "Edit object", "PE.Views.ImageSettings.textFitSlide": "Fit to slide", "PE.Views.ImageSettings.textFlip": "Flip", "PE.Views.ImageSettings.textFromFile": "From file", "PE.Views.ImageSettings.textFromStorage": "From storage", "PE.Views.ImageSettings.textFromUrl": "From URL", "PE.Views.ImageSettings.textHeight": "Height", "PE.Views.ImageSettings.textHint270": "Rotate 90° Counterclockwise", "PE.Views.ImageSettings.textHint90": "Rotate 90° Clockwise", "PE.Views.ImageSettings.textHintFlipH": "Flip horizontally", "PE.Views.ImageSettings.textHintFlipV": "Flip vertically", "PE.Views.ImageSettings.textInsert": "Replace Image", "PE.Views.ImageSettings.textOriginalSize": "Actual size", "PE.Views.ImageSettings.textRecentlyUsed": "Recently used", "PE.Views.ImageSettings.textResetCrop": "Reset crop", "PE.Views.ImageSettings.textRotate90": "Rotate 90°", "PE.Views.ImageSettings.textRotation": "Rotation", "PE.Views.ImageSettings.textSize": "Size", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Alternative text", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Description", "PE.Views.ImageSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart, or table.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Title", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "Center", "PE.Views.ImageSettingsAdvanced.textFlipped": "Flipped", "PE.Views.ImageSettingsAdvanced.textFrom": "From", "PE.Views.ImageSettingsAdvanced.textGeneral": "General", "PE.Views.ImageSettingsAdvanced.textHeight": "Height", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontally", "PE.Views.ImageSettingsAdvanced.textImageName": "Image name", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Constant proportions", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "Actual size", "PE.Views.ImageSettingsAdvanced.textPlacement": "Placement", "PE.Views.ImageSettingsAdvanced.textPosition": "Position", "PE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "PE.Views.ImageSettingsAdvanced.textSize": "Size", "PE.Views.ImageSettingsAdvanced.textTitle": "Image - Advanced settings", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "Top left corner", "PE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "PE.Views.ImageSettingsAdvanced.textVertically": "Vertically", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.ariaLeftMenu": "Left menu", "PE.Views.LeftMenu.tipAbout": "About", "PE.Views.LeftMenu.tipChat": "Cha<PERSON>", "PE.Views.LeftMenu.tipComments": "Comments", "PE.Views.LeftMenu.tipPlugins": "Plugins", "PE.Views.LeftMenu.tipSearch": "Find", "PE.Views.LeftMenu.tipSlides": "Slides", "PE.Views.LeftMenu.tipSupport": "Feedback & Support", "PE.Views.LeftMenu.tipTitles": "Titles", "PE.Views.LeftMenu.txtDeveloper": "DEVELOPER MODE", "PE.Views.LeftMenu.txtEditor": "Presentation Editor", "PE.Views.LeftMenu.txtLimit": "Limit access", "PE.Views.LeftMenu.txtTrial": "TRIAL MODE", "PE.Views.LeftMenu.txtTrialDev": "Trial Developer Mode", "PE.Views.ParagraphSettings.strLineHeight": "Line spacing", "PE.Views.ParagraphSettings.strParagraphSpacing": "Paragraph spacing", "PE.Views.ParagraphSettings.strSpacingAfter": "After", "PE.Views.ParagraphSettings.strSpacingBefore": "Before", "PE.Views.ParagraphSettings.textAdvanced": "Show advanced settings", "PE.Views.ParagraphSettings.textAt": "At", "PE.Views.ParagraphSettings.textAtLeast": "At least", "PE.Views.ParagraphSettings.textAuto": "Multiple", "PE.Views.ParagraphSettings.textExact": "Exactly", "PE.Views.ParagraphSettings.txtAutoText": "Auto", "PE.Views.ParagraphSettingsAdvanced.noTabs": "The specified tabs will appear in this field", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "All caps", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Double strikethrough", "PE.Views.ParagraphSettingsAdvanced.strIndent": "Indents", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Left", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Line spacing", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Right", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "After", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Before", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Special", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Font", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indents & Spacing", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Small caps", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Spacing", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Strikethrough", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscript", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superscript", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Tabs", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Alignment", "PE.Views.ParagraphSettingsAdvanced.textAuto": "Multiple", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Character spacing", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Default tab", "PE.Views.ParagraphSettingsAdvanced.textEffects": "Effects", "PE.Views.ParagraphSettingsAdvanced.textExact": "Exactly", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "First line", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Hanging", "PE.Views.ParagraphSettingsAdvanced.textJustified": "Justified", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(none)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Remove", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Remove all", "PE.Views.ParagraphSettingsAdvanced.textSet": "Specify", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Center", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "Left", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Tab position", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "Right", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraph - Advanced settings", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "PE.Views.PrintWithPreview.txtAllPages": "All slides", "PE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "PE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "PE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "PE.Views.PrintWithPreview.txtCopies": "Copies", "PE.Views.PrintWithPreview.txtCurrentPage": "Current slide", "PE.Views.PrintWithPreview.txtCustomPages": "Custom print", "PE.Views.PrintWithPreview.txtEmptyTable": "There is nothing to print because the presentation is empty", "PE.Views.PrintWithPreview.txtHeaderFooterSettings": "Header/footer settings", "PE.Views.PrintWithPreview.txtOf": "of {0}", "PE.Views.PrintWithPreview.txtOneSide": "Print one sided", "PE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "PE.Views.PrintWithPreview.txtPage": "Slide", "PE.Views.PrintWithPreview.txtPageNumInvalid": "Slide number invalid", "PE.Views.PrintWithPreview.txtPages": "Slides", "PE.Views.PrintWithPreview.txtPaperSize": "Paper size", "PE.Views.PrintWithPreview.txtPrint": "Print", "PE.Views.PrintWithPreview.txtPrintPdf": "Print to PDF", "PE.Views.PrintWithPreview.txtPrintRange": "Print range", "PE.Views.PrintWithPreview.txtPrintSides": "Print sides", "PE.Views.RightMenu.ariaRightMenu": "Right menu", "PE.Views.RightMenu.txtChartSettings": "Chart settings", "PE.Views.RightMenu.txtImageSettings": "Image settings", "PE.Views.RightMenu.txtParagraphSettings": "Paragraph settings", "PE.Views.RightMenu.txtShapeSettings": "Shape settings", "PE.Views.RightMenu.txtSignatureSettings": "Signature settings", "PE.Views.RightMenu.txtSlideSettings": "Slide settings", "PE.Views.RightMenu.txtTableSettings": "Table settings", "PE.Views.RightMenu.txtTextArtSettings": "Text Art settings", "PE.Views.ShapeSettings.strBackground": "Background color", "PE.Views.ShapeSettings.strChange": "Change shape", "PE.Views.ShapeSettings.strColor": "Color", "PE.Views.ShapeSettings.strFill": "Fill", "PE.Views.ShapeSettings.strForeground": "Foreground color", "PE.Views.ShapeSettings.strPattern": "Pattern", "PE.Views.ShapeSettings.strShadow": "Show shadow", "PE.Views.ShapeSettings.strSize": "Size", "PE.Views.ShapeSettings.strStroke": "Line", "PE.Views.ShapeSettings.strTransparency": "Opacity", "PE.Views.ShapeSettings.strType": "Type", "PE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "PE.Views.ShapeSettings.textAdvanced": "Show advanced settings", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "PE.Views.ShapeSettings.textColor": "Color fill", "PE.Views.ShapeSettings.textDirection": "Direction", "PE.Views.ShapeSettings.textEditPoints": "Edit points", "PE.Views.ShapeSettings.textEditShape": "Edit shape", "PE.Views.ShapeSettings.textEmptyPattern": "No pattern", "PE.Views.ShapeSettings.textEyedropper": "Eyedropper", "PE.Views.ShapeSettings.textFlip": "Flip", "PE.Views.ShapeSettings.textFromFile": "From file", "PE.Views.ShapeSettings.textFromStorage": "From storage", "PE.Views.ShapeSettings.textFromUrl": "From URL", "PE.Views.ShapeSettings.textGradient": "Gradient points", "PE.Views.ShapeSettings.textGradientFill": "Gradient fill", "PE.Views.ShapeSettings.textHint270": "Rotate 90° Counterclockwise", "PE.Views.ShapeSettings.textHint90": "Rotate 90° Clockwise", "PE.Views.ShapeSettings.textHintFlipH": "Flip horizontally", "PE.Views.ShapeSettings.textHintFlipV": "Flip vertically", "PE.Views.ShapeSettings.textImageTexture": "Picture or Texture", "PE.Views.ShapeSettings.textLinear": "Linear", "PE.Views.ShapeSettings.textMoreColors": "More colors", "PE.Views.ShapeSettings.textNoFill": "No fill", "PE.Views.ShapeSettings.textNoShadow": "No Shadow", "PE.Views.ShapeSettings.textPatternFill": "Pattern", "PE.Views.ShapeSettings.textPosition": "Position", "PE.Views.ShapeSettings.textRadial": "Radial", "PE.Views.ShapeSettings.textRecentlyUsed": "Recently used", "PE.Views.ShapeSettings.textRotate90": "Rotate 90°", "PE.Views.ShapeSettings.textRotation": "Rotation", "PE.Views.ShapeSettings.textSelectImage": "Select picture", "PE.Views.ShapeSettings.textSelectTexture": "Select", "PE.Views.ShapeSettings.textShadow": "Shadow", "PE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textStyle": "Style", "PE.Views.ShapeSettings.textTexture": "From texture", "PE.Views.ShapeSettings.textTile": "Tile", "PE.Views.ShapeSettings.tipAddGradientPoint": "Add gradient point", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Remove gradient point", "PE.Views.ShapeSettings.txtBrownPaper": "Brown paper", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "Dark fabric", "PE.Views.ShapeSettings.txtGrain": "Grain", "PE.Views.ShapeSettings.txtGranite": "Granite", "PE.Views.ShapeSettings.txtGreyPaper": "Gray paper", "PE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.ShapeSettings.txtLeather": "Leather", "PE.Views.ShapeSettings.txtNoBorders": "No line", "PE.Views.ShapeSettings.txtPapyrus": "Papyrus", "PE.Views.ShapeSettings.txtWood": "<PERSON>", "PE.Views.ShapeSettingsAdvanced.strColumns": "Columns", "PE.Views.ShapeSettingsAdvanced.strMargins": "Text padding", "PE.Views.ShapeSettingsAdvanced.textAlt": "Alternative text", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Description", "PE.Views.ShapeSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart, or table.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Title", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "Arrows", "PE.Views.ShapeSettingsAdvanced.textAutofit": "AutoFit", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Begin size", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Begin style", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "Bottom", "PE.Views.ShapeSettingsAdvanced.textCapType": "Cap type", "PE.Views.ShapeSettingsAdvanced.textCenter": "Center", "PE.Views.ShapeSettingsAdvanced.textColNumber": "Number of columns", "PE.Views.ShapeSettingsAdvanced.textEndSize": "End size", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "End style", "PE.Views.ShapeSettingsAdvanced.textFlat": "Flat", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Flipped", "PE.Views.ShapeSettingsAdvanced.textFrom": "From", "PE.Views.ShapeSettingsAdvanced.textGeneral": "General", "PE.Views.ShapeSettingsAdvanced.textHeight": "Height", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontally", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Join type", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Constant proportions", "PE.Views.ShapeSettingsAdvanced.textLeft": "Left", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "Line style", "PE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textNofit": "Do not autofit", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Placement", "PE.Views.ShapeSettingsAdvanced.textPosition": "Position", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Resize shape to fit text", "PE.Views.ShapeSettingsAdvanced.textRight": "Right", "PE.Views.ShapeSettingsAdvanced.textRotation": "Rotation", "PE.Views.ShapeSettingsAdvanced.textRound": "Round", "PE.Views.ShapeSettingsAdvanced.textShapeName": "Shape name", "PE.Views.ShapeSettingsAdvanced.textShrink": "Shrink text on overflow", "PE.Views.ShapeSettingsAdvanced.textSize": "Size", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Spacing between columns", "PE.Views.ShapeSettingsAdvanced.textSquare": "Square", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Text box", "PE.Views.ShapeSettingsAdvanced.textTitle": "Shape - Advanced settings", "PE.Views.ShapeSettingsAdvanced.textTop": "Top", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "Top left corner", "PE.Views.ShapeSettingsAdvanced.textVertical": "Vertical", "PE.Views.ShapeSettingsAdvanced.textVertically": "Vertically", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Weights & Arrows", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "None", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Warning", "PE.Views.SignatureSettings.strDelete": "Remove signature", "PE.Views.SignatureSettings.strDetails": "Signature details", "PE.Views.SignatureSettings.strInvalid": "Invalid signatures", "PE.Views.SignatureSettings.strSign": "Sign", "PE.Views.SignatureSettings.strSignature": "Signature", "PE.Views.SignatureSettings.strValid": "Valid signatures", "PE.Views.SignatureSettings.txtContinueEditing": "Edit anyway", "PE.Views.SignatureSettings.txtEditWarning": "Editing will remove signatures from the presentation.<br>Continue?", "PE.Views.SignatureSettings.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "PE.Views.SignatureSettings.txtSigned": "Valid signatures have been added to the presentation. The presentation is protected from editing.", "PE.Views.SignatureSettings.txtSignedInvalid": "Some of the digital signatures in presentation are invalid or could not be verified. The presentation is protected from editing.", "PE.Views.SlideSettings.strApplyAllSlides": "Apply to All Slides", "PE.Views.SlideSettings.strBackground": "Background color", "PE.Views.SlideSettings.strBackgroundGraphics": "Show Background graphics", "PE.Views.SlideSettings.strBackgroundReset": "Reset Background", "PE.Views.SlideSettings.strColor": "Color", "PE.Views.SlideSettings.strDateTime": "Show Date and Time", "PE.Views.SlideSettings.strFill": "Background", "PE.Views.SlideSettings.strForeground": "Foreground color", "PE.Views.SlideSettings.strPattern": "Pattern", "PE.Views.SlideSettings.strSlideNum": "Show slide number", "PE.Views.SlideSettings.strTransparency": "Opacity", "PE.Views.SlideSettings.textAdvanced": "Show advanced settings", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "Color fill", "PE.Views.SlideSettings.textDirection": "Direction", "PE.Views.SlideSettings.textEmptyPattern": "No pattern", "PE.Views.SlideSettings.textFromFile": "From file", "PE.Views.SlideSettings.textFromStorage": "From storage", "PE.Views.SlideSettings.textFromUrl": "From URL", "PE.Views.SlideSettings.textGradient": "Gradient points", "PE.Views.SlideSettings.textGradientFill": "Gradient fill", "PE.Views.SlideSettings.textImageTexture": "Picture or Texture", "PE.Views.SlideSettings.textLinear": "Linear", "PE.Views.SlideSettings.textNoFill": "No fill", "PE.Views.SlideSettings.textPatternFill": "Pattern", "PE.Views.SlideSettings.textPosition": "Position", "PE.Views.SlideSettings.textRadial": "Radial", "PE.Views.SlideSettings.textReset": "Reset Changes", "PE.Views.SlideSettings.textSelectImage": "Select picture", "PE.Views.SlideSettings.textSelectTexture": "Select", "PE.Views.SlideSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStyle": "Style", "PE.Views.SlideSettings.textTexture": "From texture", "PE.Views.SlideSettings.textTile": "Tile", "PE.Views.SlideSettings.tipAddGradientPoint": "Add gradient point", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Remove gradient point", "PE.Views.SlideSettings.txtBrownPaper": "Brown paper", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "Dark fabric", "PE.Views.SlideSettings.txtGrain": "Grain", "PE.Views.SlideSettings.txtGranite": "Granite", "PE.Views.SlideSettings.txtGreyPaper": "Gray paper", "PE.Views.SlideSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.SlideSettings.txtLeather": "Leather", "PE.Views.SlideSettings.txtPapyrus": "Papyrus", "PE.Views.SlideSettings.txtWood": "<PERSON>", "PE.Views.SlideshowSettings.textLoop": "Loop continuously until 'Esc' is pressed", "PE.Views.SlideshowSettings.textTitle": "Show settings", "PE.Views.SlideSizeSettings.strLandscape": "Landscape", "PE.Views.SlideSizeSettings.strPortrait": "Portrait", "PE.Views.SlideSizeSettings.textHeight": "Height", "PE.Views.SlideSizeSettings.textSlideOrientation": "Slide orientation", "PE.Views.SlideSizeSettings.textSlideSize": "Slide size", "PE.Views.SlideSizeSettings.textTitle": "Slide size settings", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "35 mm slides", "PE.Views.SlideSizeSettings.txtA3": "A3 Paper (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "A4 Paper (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO) Paper (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO) Paper (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "Custom", "PE.Views.SlideSizeSettings.txtLedger": "Ledger Paper (11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "Letter Paper (8.5x11 in)", "PE.Views.SlideSizeSettings.txtOverhead": "Overhead", "PE.Views.SlideSizeSettings.txtSlideNum": "Number slides from", "PE.Views.SlideSizeSettings.txtStandard": "Standard (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Widescreen", "PE.Views.Statusbar.goToPageText": "Go to Slide", "PE.Views.Statusbar.pageIndexText": "Slide {0} of {1}", "PE.Views.Statusbar.textShowBegin": "Show from Beginning", "PE.Views.Statusbar.textShowCurrent": "Show from Current Slide", "PE.Views.Statusbar.textShowPresenterView": "Show Presenter View", "PE.Views.Statusbar.textSlideMaster": "Slide master", "PE.Views.Statusbar.tipAccessRights": "Manage document access rights", "PE.Views.Statusbar.tipFitPage": "Fit to slide", "PE.Views.Statusbar.tipFitWidth": "Fit to width", "PE.Views.Statusbar.tipPreview": "Start slideshow", "PE.Views.Statusbar.tipSetLang": "Set text language", "PE.Views.Statusbar.tipZoomFactor": "Zoom", "PE.Views.Statusbar.tipZoomIn": "Zoom in", "PE.Views.Statusbar.tipZoomOut": "Zoom out", "PE.Views.Statusbar.txtPageNumInvalid": "Invalid slide number", "PE.Views.TableSettings.deleteColumnText": "Delete column", "PE.Views.TableSettings.deleteRowText": "Delete row", "PE.Views.TableSettings.deleteTableText": "Delete table", "PE.Views.TableSettings.insertColumnLeftText": "Insert column left", "PE.Views.TableSettings.insertColumnRightText": "Insert column right", "PE.Views.TableSettings.insertRowAboveText": "Insert row above", "PE.Views.TableSettings.insertRowBelowText": "Insert row below", "PE.Views.TableSettings.mergeCellsText": "Merge cells", "PE.Views.TableSettings.selectCellText": "Select cell", "PE.Views.TableSettings.selectColumnText": "Select column", "PE.Views.TableSettings.selectRowText": "Select row", "PE.Views.TableSettings.selectTableText": "Select Table", "PE.Views.TableSettings.splitCellsText": "Split cell...", "PE.Views.TableSettings.splitCellTitleText": "Split cell", "PE.Views.TableSettings.textAdvanced": "Show advanced settings", "PE.Views.TableSettings.textBackColor": "Background color", "PE.Views.TableSettings.textBanded": "Banded", "PE.Views.TableSettings.textBorderColor": "Color", "PE.Views.TableSettings.textBorders": "Borders Style", "PE.Views.TableSettings.textCellSize": "Cell size", "PE.Views.TableSettings.textColumns": "Columns", "PE.Views.TableSettings.textDistributeCols": "Distribute columns", "PE.Views.TableSettings.textDistributeRows": "Distribute rows", "PE.Views.TableSettings.textEdit": "Rows & Columns", "PE.Views.TableSettings.textEmptyTemplate": "No templates", "PE.Views.TableSettings.textFirst": "First", "PE.Views.TableSettings.textHeader": "Header", "PE.Views.TableSettings.textHeight": "Height", "PE.Views.TableSettings.textLast": "Last", "PE.Views.TableSettings.textRows": "Rows", "PE.Views.TableSettings.textSelectBorders": "Select borders you want to change applying style chosen above", "PE.Views.TableSettings.textTemplate": "Select From Template", "PE.Views.TableSettings.textTotal": "Total", "PE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "Set outer border and all inner lines", "PE.Views.TableSettings.tipBottom": "Set outer bottom border only", "PE.Views.TableSettings.tipInner": "Set inner lines only", "PE.Views.TableSettings.tipInnerHor": "Set horizontal inner lines only", "PE.Views.TableSettings.tipInnerVert": "Set vertical inner lines only", "PE.Views.TableSettings.tipLeft": "Set outer left border only", "PE.Views.TableSettings.tipNone": "Set no borders", "PE.Views.TableSettings.tipOuter": "Set outer border only", "PE.Views.TableSettings.tipRight": "Set outer right border only", "PE.Views.TableSettings.tipTop": "Set outer top border only", "PE.Views.TableSettings.txtGroupTable_Custom": "Custom", "PE.Views.TableSettings.txtGroupTable_Dark": "Dark", "PE.Views.TableSettings.txtGroupTable_Light": "Light", "PE.Views.TableSettings.txtGroupTable_Medium": "Medium", "PE.Views.TableSettings.txtGroupTable_Optimal": "Best match for document", "PE.Views.TableSettings.txtNoBorders": "No borders", "PE.Views.TableSettings.txtTable_Accent": "Accent", "PE.Views.TableSettings.txtTable_DarkStyle": "Dark Style", "PE.Views.TableSettings.txtTable_LightStyle": "Light style", "PE.Views.TableSettings.txtTable_MediumStyle": "Medium style", "PE.Views.TableSettings.txtTable_NoGrid": "No grid", "PE.Views.TableSettings.txtTable_NoStyle": "No style", "PE.Views.TableSettings.txtTable_TableGrid": "Table grid", "PE.Views.TableSettings.txtTable_ThemedStyle": "Themed Style", "PE.Views.TableSettingsAdvanced.textAlt": "Alternative text", "PE.Views.TableSettingsAdvanced.textAltDescription": "Description", "PE.Views.TableSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart, or table.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Title", "PE.Views.TableSettingsAdvanced.textBottom": "Bottom", "PE.Views.TableSettingsAdvanced.textCenter": "Center", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Use default margins", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Default margins", "PE.Views.TableSettingsAdvanced.textFrom": "From", "PE.Views.TableSettingsAdvanced.textGeneral": "General", "PE.Views.TableSettingsAdvanced.textHeight": "Height", "PE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Constant proportions", "PE.Views.TableSettingsAdvanced.textLeft": "Left", "PE.Views.TableSettingsAdvanced.textMargins": "Cell margins", "PE.Views.TableSettingsAdvanced.textPlacement": "Placement", "PE.Views.TableSettingsAdvanced.textPosition": "Position", "PE.Views.TableSettingsAdvanced.textRight": "Right", "PE.Views.TableSettingsAdvanced.textSize": "Size", "PE.Views.TableSettingsAdvanced.textTableName": "Table name", "PE.Views.TableSettingsAdvanced.textTitle": "Table - Advanced settings", "PE.Views.TableSettingsAdvanced.textTop": "Top", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "Top left corner", "PE.Views.TableSettingsAdvanced.textVertical": "Vertical", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strBackground": "Background color", "PE.Views.TextArtSettings.strColor": "Color", "PE.Views.TextArtSettings.strFill": "Fill", "PE.Views.TextArtSettings.strForeground": "Foreground color", "PE.Views.TextArtSettings.strPattern": "Pattern", "PE.Views.TextArtSettings.strSize": "Size", "PE.Views.TextArtSettings.strStroke": "Line", "PE.Views.TextArtSettings.strTransparency": "Opacity", "PE.Views.TextArtSettings.strType": "Type", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "PE.Views.TextArtSettings.textColor": "Color fill", "PE.Views.TextArtSettings.textDirection": "Direction", "PE.Views.TextArtSettings.textEmptyPattern": "No pattern", "PE.Views.TextArtSettings.textFromFile": "From file", "PE.Views.TextArtSettings.textFromUrl": "From URL", "PE.Views.TextArtSettings.textGradient": "Gradient points", "PE.Views.TextArtSettings.textGradientFill": "Gradient fill", "PE.Views.TextArtSettings.textImageTexture": "Picture or Texture", "PE.Views.TextArtSettings.textLinear": "Linear", "PE.Views.TextArtSettings.textNoFill": "No fill", "PE.Views.TextArtSettings.textPatternFill": "Pattern", "PE.Views.TextArtSettings.textPosition": "Position", "PE.Views.TextArtSettings.textRadial": "Radial", "PE.Views.TextArtSettings.textSelectTexture": "Select", "PE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStyle": "Style", "PE.Views.TextArtSettings.textTemplate": "Template", "PE.Views.TextArtSettings.textTexture": "From texture", "PE.Views.TextArtSettings.textTile": "Tile", "PE.Views.TextArtSettings.textTransform": "Transform", "PE.Views.TextArtSettings.tipAddGradientPoint": "Add gradient point", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Remove gradient point", "PE.Views.TextArtSettings.txtBrownPaper": "Brown paper", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "Dark fabric", "PE.Views.TextArtSettings.txtGrain": "Grain", "PE.Views.TextArtSettings.txtGranite": "Granite", "PE.Views.TextArtSettings.txtGreyPaper": "Gray paper", "PE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.TextArtSettings.txtLeather": "Leather", "PE.Views.TextArtSettings.txtNoBorders": "No line", "PE.Views.TextArtSettings.txtPapyrus": "Papyrus", "PE.Views.TextArtSettings.txtWood": "<PERSON>", "PE.Views.Toolbar.capAddLayout": "Add Layout", "PE.Views.Toolbar.capAddSlide": "Add Slide", "PE.Views.Toolbar.capAddSlideMaster": "Add Slide Master", "PE.Views.Toolbar.capBtnAddComment": "Add Comment", "PE.Views.Toolbar.capBtnComment": "Comment", "PE.Views.Toolbar.capBtnDateTime": "Date & Time", "PE.Views.Toolbar.capBtnInsHeaderFooter": "Header & Footer", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "Symbol", "PE.Views.Toolbar.capBtnSlideNum": "Slide Number", "PE.Views.Toolbar.capCloseMaster": "Close Master", "PE.Views.Toolbar.capInsertAudio": "Audio", "PE.Views.Toolbar.capInsertChart": "Chart", "PE.Views.Toolbar.capInsertEquation": "Equation", "PE.Views.Toolbar.capInsertHyperlink": "Hyperlink", "PE.Views.Toolbar.capInsertImage": "Image", "PE.Views.Toolbar.capInsertPlaceholder": "Insert Placeholder", "PE.Views.Toolbar.capInsertShape": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertTable": "Table", "PE.Views.Toolbar.capInsertText": "Text Box", "PE.Views.Toolbar.capInsertTextArt": "Text Art", "PE.Views.Toolbar.capInsertVideo": "Video", "PE.Views.Toolbar.capTabFile": "File", "PE.Views.Toolbar.capTabHome": "Home", "PE.Views.Toolbar.capTabInsert": "Insert", "PE.Views.Toolbar.mniCapitalizeWords": "Capitalize Each Word", "PE.Views.Toolbar.mniCustomTable": "Insert custom table", "PE.Views.Toolbar.mniImageFromFile": "Image from file", "PE.Views.Toolbar.mniImageFromStorage": "Image from storage", "PE.Views.Toolbar.mniImageFromUrl": "Image from URL", "PE.Views.Toolbar.mniInsertSSE": "Insert Spreadsheet", "PE.Views.Toolbar.mniLowerCase": "lowercase", "PE.Views.Toolbar.mniSentenceCase": "Sentence case.", "PE.Views.Toolbar.mniSlideAdvanced": "Advanced settings", "PE.Views.Toolbar.mniSlideStandard": "Standard (4:3)", "PE.Views.Toolbar.mniSlideWide": "Widescreen (16:9)", "PE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "PE.Views.Toolbar.mniUpperCase": "UPPERCASE", "PE.Views.Toolbar.strMenuNoFill": "No Fill", "PE.Views.Toolbar.textAlignBottom": "Align text to the bottom", "PE.Views.Toolbar.textAlignCenter": "Center text", "PE.Views.Toolbar.textAlignJust": "Justify", "PE.Views.Toolbar.textAlignLeft": "Align text left", "PE.Views.Toolbar.textAlignMiddle": "Align text to the middle", "PE.Views.Toolbar.textAlignRight": "Align text right", "PE.Views.Toolbar.textAlignTop": "Align text to the top", "PE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "PE.Views.Toolbar.textArrangeBack": "Send to Background", "PE.Views.Toolbar.textArrangeBackward": "Send Backward", "PE.Views.Toolbar.textArrangeForward": "Bring Forward", "PE.Views.Toolbar.textArrangeFront": "Bring to Foreground", "PE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "PE.Views.Toolbar.textBlackHeart": "Black heart suit", "PE.Views.Toolbar.textBold": "Bold", "PE.Views.Toolbar.textBullet": "Bullet", "PE.Views.Toolbar.textChart": "Chart", "PE.Views.Toolbar.textColumnsCustom": "Custom columns", "PE.Views.Toolbar.textColumnsOne": "One Column", "PE.Views.Toolbar.textColumnsThree": "Three Columns", "PE.Views.Toolbar.textColumnsTwo": "Two Columns", "PE.Views.Toolbar.textContent": "Content", "PE.Views.Toolbar.textContentVertical": "Content (Vertical)", "PE.Views.Toolbar.textCopyright": "Copyright Sign", "PE.Views.Toolbar.textDegree": "Degree Sign", "PE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "PE.Views.Toolbar.textDivision": "Division Sign", "PE.Views.Toolbar.textDollar": "Dollar Sign", "PE.Views.Toolbar.textEuro": "Euro Sign", "PE.Views.Toolbar.textFooters": "Footers", "PE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "PE.Views.Toolbar.textInfinity": "Infinity", "PE.Views.Toolbar.textItalic": "Italic", "PE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "PE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "PE.Views.Toolbar.textLineSpaceOptions": "Line spacing options", "PE.Views.Toolbar.textListSettings": "List Settings", "PE.Views.Toolbar.textMoreSymbols": "More symbols", "PE.Views.Toolbar.textNotEqualTo": "Not Equal To", "PE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "PE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "PE.Views.Toolbar.textPicture": "Picture", "PE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "PE.Views.Toolbar.textRecentlyUsed": "Recently used", "PE.Views.Toolbar.textRegistered": "Registered Sign", "PE.Views.Toolbar.textSection": "Section Sign", "PE.Views.Toolbar.textShapeAlignBottom": "Align Bottom", "PE.Views.Toolbar.textShapeAlignCenter": "Align Center", "PE.Views.Toolbar.textShapeAlignLeft": "Align Left", "PE.Views.Toolbar.textShapeAlignMiddle": "Align Middle", "PE.Views.Toolbar.textShapeAlignRight": "Align Right", "PE.Views.Toolbar.textShapeAlignTop": "Align top", "PE.Views.Toolbar.textShapesCombine": "Combine", "PE.Views.Toolbar.textShapesFragment": "Fragment", "PE.Views.Toolbar.textShapesIntersect": "Intersect", "PE.Views.Toolbar.textShapesSubstract": "Subtract", "PE.Views.Toolbar.textShapesUnion": "Union", "PE.Views.Toolbar.textShowBegin": "Show from Beginning", "PE.Views.Toolbar.textShowCurrent": "Show from Current Slide", "PE.Views.Toolbar.textShowPresenterView": "Show Presenter View", "PE.Views.Toolbar.textShowSettings": "Show Settings", "PE.Views.Toolbar.textSmartArt": "SmartArt", "PE.Views.Toolbar.textSmile": "White Smiling Face", "PE.Views.Toolbar.textSquareRoot": "Square Root", "PE.Views.Toolbar.textStrikeout": "Strikethrough", "PE.Views.Toolbar.textSubscript": "Subscript", "PE.Views.Toolbar.textSuperscript": "Superscript", "PE.Views.Toolbar.textTabAnimation": "Animation", "PE.Views.Toolbar.textTabCollaboration": "Collaboration", "PE.Views.Toolbar.textTabDesign": "Design", "PE.Views.Toolbar.textTabDraw": "Draw", "PE.Views.Toolbar.textTabFile": "File", "PE.Views.Toolbar.textTabHome": "Home", "PE.Views.Toolbar.textTabInsert": "Insert", "PE.Views.Toolbar.textTable": "Table", "PE.Views.Toolbar.textTabProtect": "Protection", "PE.Views.Toolbar.textTabTransitions": "Transitions", "PE.Views.Toolbar.textTabView": "View", "PE.Views.Toolbar.textText": "Text", "PE.Views.Toolbar.textTextVertical": "Text (Vertical)", "PE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "PE.Views.Toolbar.textTitle": "Title", "PE.Views.Toolbar.textTitleError": "Error", "PE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "PE.Views.Toolbar.textUnderline": "Underline", "PE.Views.Toolbar.textYen": "Yen Sign", "PE.Views.Toolbar.tipAddLayout": "Add layout", "PE.Views.Toolbar.tipAddSlide": "Add slide", "PE.Views.Toolbar.tipAddSlideMaster": "Add slide master", "PE.Views.Toolbar.tipBack": "Back", "PE.Views.Toolbar.tipChangeCase": "Change case", "PE.Views.Toolbar.tipChangeChart": "Change chart type", "PE.Views.Toolbar.tipChangeSlide": "Change slide layout", "PE.Views.Toolbar.tipClearStyle": "Clear style", "PE.Views.Toolbar.tipCloseMaster": "Close Master", "PE.Views.Toolbar.tipColorSchemas": "Change color theme", "PE.Views.Toolbar.tipColumns": "Insert columns", "PE.Views.Toolbar.tipCopy": "Copy", "PE.Views.Toolbar.tipCopyStyle": "Copy style", "PE.Views.Toolbar.tipCut": "Cut", "PE.Views.Toolbar.tipDateTime": "Insert current date and time", "PE.Views.Toolbar.tipDecFont": "Decrement font size", "PE.Views.Toolbar.tipDecPrLeft": "Decrease indent", "PE.Views.Toolbar.tipEditHeaderFooter": "Edit header or footer", "PE.Views.Toolbar.tipFontColor": "Font color", "PE.Views.Toolbar.tipFontName": "Font", "PE.Views.Toolbar.tipFontSize": "Font size", "PE.Views.Toolbar.tipHAligh": "Horizontal align", "PE.Views.Toolbar.tipHighlightColor": "Highlight color", "PE.Views.Toolbar.tipIncFont": "Increment font size", "PE.Views.Toolbar.tipIncPrLeft": "Increase indent", "PE.Views.Toolbar.tipInsertAudio": "Insert audio", "PE.Views.Toolbar.tipInsertChart": "Insert chart", "PE.Views.Toolbar.tipInsertChartPlaceholder": "Insert chart placeholder", "PE.Views.Toolbar.tipInsertContentPlaceholder": "Insert content placeholder", "PE.Views.Toolbar.tipInsertContentVerticalPlaceholder": "Insert content (vertical) placeholder", "PE.Views.Toolbar.tipInsertEquation": "Insert equation", "PE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "PE.Views.Toolbar.tipInsertHyperlink": "Add hyperlink", "PE.Views.Toolbar.tipInsertImage": "Insert image", "PE.Views.Toolbar.tipInsertPicturePlaceholder": "Insert picture placeholder", "PE.Views.Toolbar.tipInsertShape": "Insert shape", "PE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "PE.Views.Toolbar.tipInsertSmartArtPlaceholder": "Insert SmartArt placeholder", "PE.Views.Toolbar.tipInsertSymbol": "Insert symbol", "PE.Views.Toolbar.tipInsertTable": "Insert table", "PE.Views.Toolbar.tipInsertTablePlaceholder": "Insert table placeholder", "PE.Views.Toolbar.tipInsertText": "Insert text box", "PE.Views.Toolbar.tipInsertTextArt": "Insert Text Art", "PE.Views.Toolbar.tipInsertTextPlaceholder": "Insert text placeholder", "PE.Views.Toolbar.tipInsertTextVerticalPlaceholder": "Insert text (vertical) placeholder", "PE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "PE.Views.Toolbar.tipInsertVideo": "Insert video", "PE.Views.Toolbar.tipLineSpace": "Line spacing", "PE.Views.Toolbar.tipMarkers": "Bullets", "PE.Views.Toolbar.tipMarkersArrow": "Arrow bullets", "PE.Views.Toolbar.tipMarkersCheckmark": "Checkmark bullets", "PE.Views.Toolbar.tipMarkersDash": "Dash bullets", "PE.Views.Toolbar.tipMarkersFRhombus": "Filled rhombus bullets", "PE.Views.Toolbar.tipMarkersFRound": "Filled round bullets", "PE.Views.Toolbar.tipMarkersFSquare": "Filled square bullets", "PE.Views.Toolbar.tipMarkersHRound": "Hollow round bullets", "PE.Views.Toolbar.tipMarkersStar": "Star bullets", "PE.Views.Toolbar.tipNone": "None", "PE.Views.Toolbar.tipNumbers": "Numbering", "PE.Views.Toolbar.tipPaste": "Paste", "PE.Views.Toolbar.tipPreview": "Start slideshow", "PE.Views.Toolbar.tipPrint": "Print", "PE.Views.Toolbar.tipPrintQuick": "Quick print", "PE.Views.Toolbar.tipRedo": "Redo", "PE.Views.Toolbar.tipReplace": "Replace", "PE.Views.Toolbar.tipSave": "Save", "PE.Views.Toolbar.tipSaveCoauth": "Save your changes for the other users to see them.", "PE.Views.Toolbar.tipSelectAll": "Select all", "PE.Views.Toolbar.tipShapeAlign": "Align shape", "PE.Views.Toolbar.tipShapeArrange": "Arrange shape", "PE.Views.Toolbar.tipShapesMerge": "Merge shapes", "PE.Views.Toolbar.tipSlideNum": "Insert slide number", "PE.Views.Toolbar.tipSlideSize": "Select slide size", "PE.Views.Toolbar.tipSlideTheme": "Slide theme", "PE.Views.Toolbar.tipUndo": "Undo", "PE.Views.Toolbar.tipVAligh": "Vertical align", "PE.Views.Toolbar.tipViewSettings": "View settings", "PE.Views.Toolbar.txtColors": "Colors", "PE.Views.Toolbar.txtDistribHor": "Distribute horizontally", "PE.Views.Toolbar.txtDistribVert": "Distribute vertically", "PE.Views.Toolbar.txtDuplicateSlide": "Duplicate slide", "PE.Views.Toolbar.txtGroup": "Group", "PE.Views.Toolbar.txtObjectsAlign": "Align selected objects", "PE.Views.Toolbar.txtSlideAlign": "Align to Slide", "PE.Views.Toolbar.txtSlideSize": "Slide size", "PE.Views.Toolbar.txtUngroup": "Ungroup", "PE.Views.Transitions.strDelay": "Delay", "PE.Views.Transitions.strDuration": "Duration", "PE.Views.Transitions.strStartOnClick": "Start On Click", "PE.Views.Transitions.textBlack": "Through Black", "PE.Views.Transitions.textBottom": "From Bottom", "PE.Views.Transitions.textBottomLeft": "From Bottom-Left", "PE.Views.Transitions.textBottomRight": "From Bottom-Right", "PE.Views.Transitions.textClock": "Clock", "PE.Views.Transitions.textClockwise": "Clockwise", "PE.Views.Transitions.textCounterclockwise": "Counterclockwise", "PE.Views.Transitions.textCover": "Cover", "PE.Views.Transitions.textFade": "Fade", "PE.Views.Transitions.textHorizontalIn": "Horizontal In", "PE.Views.Transitions.textHorizontalOut": "Horizontal Out", "PE.Views.Transitions.textLeft": "From left", "PE.Views.Transitions.textMorph": "Morph", "PE.Views.Transitions.textMorphLetters": "Letters", "PE.Views.Transitions.textMorphObjects": "Objects", "PE.Views.Transitions.textMorphWord": "Words", "PE.Views.Transitions.textNone": "None", "PE.Views.Transitions.textPush": "<PERSON><PERSON>", "PE.Views.Transitions.textRandom": "Random", "PE.Views.Transitions.textRight": "From right", "PE.Views.Transitions.textSmoothly": "Smooth<PERSON>", "PE.Views.Transitions.textSplit": "Split", "PE.Views.Transitions.textTop": "From Top", "PE.Views.Transitions.textTopLeft": "From Top-Left", "PE.Views.Transitions.textTopRight": "From Top-Right", "PE.Views.Transitions.textUnCover": "UnCover", "PE.Views.Transitions.textVerticalIn": "Vertical In", "PE.Views.Transitions.textVerticalOut": "Vertical Out", "PE.Views.Transitions.textWedge": "Wedge", "PE.Views.Transitions.textWipe": "Wipe", "PE.Views.Transitions.textZoom": "Zoom", "PE.Views.Transitions.textZoomIn": "Zoom In", "PE.Views.Transitions.textZoomOut": "Zoom Out", "PE.Views.Transitions.textZoomRotate": "Zoom and Rotate", "PE.Views.Transitions.txtApplyToAll": "Apply to all slides", "PE.Views.Transitions.txtParameters": "Parameters", "PE.Views.Transitions.txtPreview": "Preview", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.capBtnHand": "Hand", "PE.Views.ViewTab.capBtnSelect": "Select", "PE.Views.ViewTab.textAddHGuides": "Add horizontal guide", "PE.Views.ViewTab.textAddVGuides": "Add vertical guide", "PE.Views.ViewTab.textAlwaysShowToolbar": "Always Show Toolbar", "PE.Views.ViewTab.textClearGuides": "Clear guides", "PE.Views.ViewTab.textCm": "cm", "PE.Views.ViewTab.textCustom": "Custom", "PE.Views.ViewTab.textFill": "Fill", "PE.Views.ViewTab.textFitToSlide": "Fit To Slide", "PE.Views.ViewTab.textFitToWidth": "Fit To <PERSON>th", "PE.Views.ViewTab.textGridlines": "Gridlines", "PE.Views.ViewTab.textGuides": "Guides", "PE.Views.ViewTab.textInterfaceTheme": "Interface Theme", "PE.Views.ViewTab.textLeftMenu": "Left Panel", "PE.Views.ViewTab.textLine": "Line", "PE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "PE.Views.ViewTab.textNormal": "Normal", "PE.Views.ViewTab.textNotes": "Notes", "PE.Views.ViewTab.textRightMenu": "Right Panel", "PE.Views.ViewTab.textRulers": "Rulers", "PE.Views.ViewTab.textShowGridlines": "Show Gridlines", "PE.Views.ViewTab.textShowGuides": "Show guides", "PE.Views.ViewTab.textSlideMaster": "Slide Master", "PE.Views.ViewTab.textSmartGuides": "Smart guides", "PE.Views.ViewTab.textSnapObjects": "Snap object to grid", "PE.Views.ViewTab.textStatusBar": "Status Bar", "PE.Views.ViewTab.textTabStyle": "Tab style", "PE.Views.ViewTab.textZoom": "Zoom", "PE.Views.ViewTab.tipFitToSlide": "Fit to slide", "PE.Views.ViewTab.tipFitToWidth": "Fit to width", "PE.Views.ViewTab.tipGridlines": "Show gridlines", "PE.Views.ViewTab.tipGuides": "Show guides", "PE.Views.ViewTab.tipHandTool": "Hand tool", "PE.Views.ViewTab.tipInterfaceTheme": "Interface theme", "PE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "PE.Views.ViewTab.tipNormal": "Normal", "PE.Views.ViewTab.tipSelectTool": "Select tool", "PE.Views.ViewTab.tipSlideMaster": "Slide master"}