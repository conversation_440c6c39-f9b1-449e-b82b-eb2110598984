{"Common.Controllers.Chat.notcriticalErrorTitle": "Peringatan", "Common.Controllers.Desktop.hintBtnHome": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.Desktop.itemCreateFromTemplate": "Buat dari templat", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "<PERSON><PERSON><PERSON> dinonakt<PERSON><PERSON> karena <PERSON>g diedit oleh pengguna lain.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Peringatan", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "<PERSON><PERSON>ek dinonaktif<PERSON> karena sedang disunting oleh pengguna lain.", "Common.Controllers.ExternalOleEditor.warningTitle": "Peringatan", "Common.Controllers.History.notcriticalErrorTitle": "Warning", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "Area", "Common.define.chartData.textAreaStacked": "Area yang ditumpuk", "Common.define.chartData.textAreaStackedPer": "Area bertumpuk 100%", "Common.define.chartData.textBar": "Palang", "Common.define.chartData.textBarNormal": "<PERSON><PERSON> kolom klaster", "Common.define.chartData.textBarNormal3d": "Kolom cluster 3-D", "Common.define.chartData.textBarNormal3dPerspective": "Kolom 3-D", "Common.define.chartData.textBarStacked": "Diagram kolom bert<PERSON>", "Common.define.chartData.textBarStacked3d": "Kolom bertumpuk 3-D", "Common.define.chartData.textBarStackedPer": "<PERSON><PERSON><PERSON> 100%", "Common.define.chartData.textBarStackedPer3d": "<PERSON><PERSON><PERSON> 100% 3-D", "Common.define.chartData.textCharts": "Bagan", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Area yang ditumpuk - kolom klaster", "Common.define.chartData.textComboBarLine": "<PERSON><PERSON> kolom klaster - garis", "Common.define.chartData.textComboBarLineSecondary": "<PERSON><PERSON> kolom klaster - garis pada sumbu sekunder", "Common.define.chartData.textComboCustom": "Custom kombinasi", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "<PERSON><PERSON> batang klaster", "Common.define.chartData.textHBarNormal3d": "Diagram Batang Cluster 3-D", "Common.define.chartData.textHBarStacked": "Diagram batang bertumpuk", "Common.define.chartData.textHBarStacked3d": "Diagram batang bertumpuk 3-D", "Common.define.chartData.textHBarStackedPer": "Diagram batang bertumpuk 100%", "Common.define.chartData.textHBarStackedPer3d": "Diagram batang bertumpuk 100% 3-D", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "Garis 3-D", "Common.define.chartData.textLineMarker": "<PERSON><PERSON> dengan tanda", "Common.define.chartData.textLineStacked": "Diagram garis bert<PERSON>uk", "Common.define.chartData.textLineStackedMarker": "Diagram garis bert<PERSON>uk dengan marker", "Common.define.chartData.textLineStackedPer": "<PERSON><PERSON> be<PERSON> 100%", "Common.define.chartData.textLineStackedPerMarker": "G<PERSON> bertumpuk 100% dengan marker", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textPie3d": "Pie 3-D", "Common.define.chartData.textPoint": "XY (<PERSON><PERSON><PERSON>)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Radar terisi", "Common.define.chartData.textRadarMarker": "<PERSON> dengan penanda", "Common.define.chartData.textScatter": "Sebar", "Common.define.chartData.textScatterLine": "Diagram sebar dengan garis lurus", "Common.define.chartData.textScatterLineMarker": "Diagram sebar dengan garis lurus dan marker", "Common.define.chartData.textScatterSmooth": "Diagram sebar dengan garis mulus", "Common.define.chartData.textScatterSmoothMarker": "Diagram sebar dengan garis mulus dan marker", "Common.define.chartData.textStock": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textSurface": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textAcross": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textAppear": "Muncul", "Common.define.effectData.textArcDown": "<PERSON>", "Common.define.effectData.textArcLeft": "<PERSON>", "Common.define.effectData.textArcRight": "<PERSON>", "Common.define.effectData.textArcs": "Lengkungan", "Common.define.effectData.textArcUp": "Arc Atas", "Common.define.effectData.textBasic": "<PERSON><PERSON>", "Common.define.effectData.textBasicSwivel": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBasicZoom": "<PERSON><PERSON>", "Common.define.effectData.textBean": "<PERSON>", "Common.define.effectData.textBlinds": "Blinds", "Common.define.effectData.textBlink": "Blink", "Common.define.effectData.textBoldFlash": "<PERSON>", "Common.define.effectData.textBoldReveal": "Reveal <PERSON>", "Common.define.effectData.textBoomerang": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounce": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounceLeft": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounceRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBox": "Kotak", "Common.define.effectData.textBrushColor": "<PERSON><PERSON>", "Common.define.effectData.textCenterRevolve": "Putar <PERSON>", "Common.define.effectData.textCheckerboard": "Checkerboard", "Common.define.effectData.textCircle": "Lingkaran", "Common.define.effectData.textCollapse": "Collapse", "Common.define.effectData.textColorPulse": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textComplementaryColor": "<PERSON><PERSON>", "Common.define.effectData.textComplementaryColor2": "Warna Pelengkap 2", "Common.define.effectData.textCompress": "<PERSON><PERSON>", "Common.define.effectData.textContrast": "Kontras", "Common.define.effectData.textContrastingColor": "<PERSON><PERSON>", "Common.define.effectData.textCredits": "Kredit", "Common.define.effectData.textCrescentMoon": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCurveDown": "<PERSON>g<PERSON><PERSON>", "Common.define.effectData.textCurvedSquare": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCurvedX": "Lengkung X", "Common.define.effectData.textCurvyLeft": "<PERSON><PERSON>", "Common.define.effectData.textCurvyRight": "<PERSON><PERSON>", "Common.define.effectData.textCurvyStar": "Bintang <PERSON>", "Common.define.effectData.textCustomPath": "<PERSON><PERSON>", "Common.define.effectData.textCuverUp": "Lengkung Keatas", "Common.define.effectData.textDarken": "Gelapkan", "Common.define.effectData.textDecayingWave": "Gelombang Luruh", "Common.define.effectData.textDesaturate": "Desaturasi", "Common.define.effectData.textDiagonalDownRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDiagonalUpRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDiamond": "Diamond", "Common.define.effectData.textDisappear": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textDissolveIn": "Melebur Kedalam", "Common.define.effectData.textDissolveOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDown": "<PERSON>wa<PERSON>", "Common.define.effectData.textDrop": "Drop", "Common.define.effectData.textEmphasis": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textEntrance": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textEqualTriangle": "Segitiga Sama Sisi", "Common.define.effectData.textExciting": "Exciting", "Common.define.effectData.textExit": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textExpand": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFade": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFigureFour": "Figure 8 Four", "Common.define.effectData.textFillColor": "<PERSON><PERSON>", "Common.define.effectData.textFlip": "Flip", "Common.define.effectData.textFloat": "Mengambang", "Common.define.effectData.textFloatDown": "Mengambang Kebawah", "Common.define.effectData.textFloatIn": "Mengambang Kedalam", "Common.define.effectData.textFloatOut": "Mengambang Keluar", "Common.define.effectData.textFloatUp": "Mengambang Keatas", "Common.define.effectData.textFlyIn": "Terbang Kedalam", "Common.define.effectData.textFlyOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFontColor": "<PERSON><PERSON>", "Common.define.effectData.textFootball": "Football", "Common.define.effectData.textFromBottom": "<PERSON><PERSON>", "Common.define.effectData.textFromBottomLeft": "<PERSON><PERSON>", "Common.define.effectData.textFromBottomRight": "<PERSON><PERSON>", "Common.define.effectData.textFromLeft": "<PERSON><PERSON>", "Common.define.effectData.textFromRight": "<PERSON><PERSON>", "Common.define.effectData.textFromTop": "<PERSON><PERSON>", "Common.define.effectData.textFromTopLeft": "<PERSON><PERSON>", "Common.define.effectData.textFromTopRight": "<PERSON><PERSON>", "Common.define.effectData.textFunnel": "Funnel", "Common.define.effectData.textGrowShrink": "Grow/Shrink", "Common.define.effectData.textGrowTurn": "Grow & Turn", "Common.define.effectData.textGrowWithColor": "<PERSON><PERSON> Dengan Warna", "Common.define.effectData.textHeart": "<PERSON><PERSON>", "Common.define.effectData.textHeartbeat": "Heartbeat", "Common.define.effectData.textHexagon": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textHorizontal": "Horisontal", "Common.define.effectData.textHorizontalFigure": "Figure 8 Horizontal", "Common.define.effectData.textHorizontalIn": "Horizontal Masuk", "Common.define.effectData.textHorizontalOut": "<PERSON><PERSON>", "Common.define.effectData.textIn": "<PERSON><PERSON>", "Common.define.effectData.textInFromScreenCenter": "<PERSON>", "Common.define.effectData.textInSlightly": "Di Sedikit", "Common.define.effectData.textInToScreenBottom": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textInvertedSquare": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textInvertedTriangle": "Segitiga Terbalik", "Common.define.effectData.textLeft": "<PERSON><PERSON>", "Common.define.effectData.textLeftDown": "<PERSON><PERSON>", "Common.define.effectData.textLeftUp": "<PERSON><PERSON>", "Common.define.effectData.textLighten": "Lighten", "Common.define.effectData.textLineColor": "<PERSON><PERSON>", "Common.define.effectData.textLines": "<PERSON><PERSON>", "Common.define.effectData.textLinesCurves": "<PERSON><PERSON>", "Common.define.effectData.textLoopDeLoop": "Loop de Loop", "Common.define.effectData.textLoops": "Perulangan", "Common.define.effectData.textModerate": "<PERSON>rat", "Common.define.effectData.textNeutron": "Neutron", "Common.define.effectData.textObjectCenter": "Pusat O<PERSON>", "Common.define.effectData.textObjectColor": "<PERSON><PERSON>", "Common.define.effectData.textOctagon": "Oktagon", "Common.define.effectData.textOut": "<PERSON><PERSON>", "Common.define.effectData.textOutFromScreenBottom": "<PERSON><PERSON><PERSON> Bawah Screen", "Common.define.effectData.textOutSlightly": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textOutToScreenCenter": "<PERSON><PERSON><PERSON> Layar", "Common.define.effectData.textParallelogram": "Parallelogram", "Common.define.effectData.textPath": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPathCurve": "<PERSON><PERSON>", "Common.define.effectData.textPathLine": "<PERSON><PERSON>", "Common.define.effectData.textPathScribble": "Scribble", "Common.define.effectData.textPeanut": "Peanut", "Common.define.effectData.textPeekIn": "Intip <PERSON>", "Common.define.effectData.textPeekOut": "<PERSON><PERSON>", "Common.define.effectData.textPentagon": "Pentagon", "Common.define.effectData.textPinwheel": "Pinwheel", "Common.define.effectData.textPlus": "Plus", "Common.define.effectData.textPointStar": "Bintang Titik", "Common.define.effectData.textPointStar4": "Bintang Titik-4", "Common.define.effectData.textPointStar5": "Bintang Titik-5", "Common.define.effectData.textPointStar6": "Bintang Titik-6", "Common.define.effectData.textPointStar8": "Bintang Titik-8", "Common.define.effectData.textPulse": "Pulse", "Common.define.effectData.textRandomBars": "Bar Acak ", "Common.define.effectData.textRight": "<PERSON><PERSON>", "Common.define.effectData.textRightDown": "<PERSON><PERSON>", "Common.define.effectData.textRightTriangle": "Segitiga Siku-Siku", "Common.define.effectData.textRightUp": "<PERSON><PERSON>", "Common.define.effectData.textRiseUp": "Bangkit", "Common.define.effectData.textSCurve1": "S Curve 1", "Common.define.effectData.textSCurve2": "S Curve 2", "Common.define.effectData.textShape": "Bentuk", "Common.define.effectData.textShapes": "Bentuk", "Common.define.effectData.textShimmer": "Berk<PERSON><PERSON>", "Common.define.effectData.textShrinkTurn": "Shrink & Turn", "Common.define.effectData.textSineWave": "Sine Wave", "Common.define.effectData.textSinkDown": "Sink Down", "Common.define.effectData.textSlideCenter": "Pusat Slide", "Common.define.effectData.textSpecial": "Spesial", "Common.define.effectData.textSpin": "Spin", "Common.define.effectData.textSpinner": "Spinner", "Common.define.effectData.textSpiralIn": "Spiral Masuk", "Common.define.effectData.textSpiralLeft": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSpiralOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSpiralRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSplit": "Split", "Common.define.effectData.textSpoke1": "1 Spoke", "Common.define.effectData.textSpoke2": "2 Spoke", "Common.define.effectData.textSpoke3": "3 Spoke", "Common.define.effectData.textSpoke4": "4 Spoke", "Common.define.effectData.textSpoke8": "8 Spoke", "Common.define.effectData.textSpring": "Spring", "Common.define.effectData.textSquare": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textStairsDown": "Stairs Down", "Common.define.effectData.textStretch": "Rentangkan", "Common.define.effectData.textStrips": "Strips", "Common.define.effectData.textSubtle": "Subtle", "Common.define.effectData.textSwivel": "Swivel", "Common.define.effectData.textSwoosh": "Swoosh", "Common.define.effectData.textTeardrop": "Teardrop", "Common.define.effectData.textTeeter": "Teeter", "Common.define.effectData.textToBottom": "<PERSON>", "Common.define.effectData.textToBottomLeft": "<PERSON>", "Common.define.effectData.textToBottomRight": "<PERSON>", "Common.define.effectData.textToLeft": "<PERSON>", "Common.define.effectData.textToRight": "<PERSON>", "Common.define.effectData.textToTop": "<PERSON>", "Common.define.effectData.textToTopLeft": "<PERSON>", "Common.define.effectData.textToTopRight": "<PERSON>", "Common.define.effectData.textTransparency": "Transparansi", "Common.define.effectData.textTrapezoid": "Trapezoid", "Common.define.effectData.textTurnDown": "<PERSON><PERSON>", "Common.define.effectData.textTurnDownRight": "<PERSON><PERSON>", "Common.define.effectData.textTurns": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textTurnUp": "Putar ke Atas", "Common.define.effectData.textTurnUpRight": "Putar ke Atas Kanan", "Common.define.effectData.textUnderline": "<PERSON><PERSON> bawah", "Common.define.effectData.textUp": "<PERSON><PERSON>", "Common.define.effectData.textVertical": "Vertikal", "Common.define.effectData.textVerticalFigure": "Figure 8 Vertikal", "Common.define.effectData.textVerticalIn": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textVerticalOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textWave": "Gelombang", "Common.define.effectData.textWedge": "Wedge", "Common.define.effectData.textWheel": "Roda", "Common.define.effectData.textWhip": "Whip", "Common.define.effectData.textWipe": "Wipe", "Common.define.effectData.textZigzag": "Zigzag", "Common.define.effectData.textZoom": "Pembesaran", "Common.define.gridlineData.txtCm": "cm", "Common.define.gridlineData.txtPt": "pt", "Common.define.smartArt.textAccentedPicture": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textAccentProcess": "<PERSON><PERSON> Aksen", "Common.define.smartArt.textAlternatingFlow": "<PERSON><PERSON>", "Common.define.smartArt.textAlternatingHexagons": "<PERSON><PERSON>", "Common.define.smartArt.textAlternatingPictureBlocks": "Blok Gambar Bolak-Balik", "Common.define.smartArt.textAlternatingPictureCircles": "Lingkaran Gambar <PERSON>", "Common.define.smartArt.textArchitectureLayout": "Tata Letak Arsitektur", "Common.define.smartArt.textArrowRibbon": "<PERSON><PERSON>", "Common.define.smartArt.textAscendingPictureAccentProcess": "Proses <PERSON><PERSON><PERSON>", "Common.define.smartArt.textBalance": "Seimbang", "Common.define.smartArt.textBasicBendingProcess": "<PERSON><PERSON> <PERSON>", "Common.define.smartArt.textBasicBlockList": "Daftar Blok Dasar", "Common.define.smartArt.textBasicChevronProcess": "<PERSON>ses <PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicPie": "<PERSON><PERSON>", "Common.define.smartArt.textBasicProcess": "<PERSON><PERSON>", "Common.define.smartArt.textBasicPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicRadial": "<PERSON><PERSON>", "Common.define.smartArt.textBasicTarget": "Target Dasar", "Common.define.smartArt.textBasicTimeline": "<PERSON><PERSON>", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Daftar Akses <PERSON>", "Common.define.smartArt.textBendingPictureBlocks": "Blok Gambar Meliuk", "Common.define.smartArt.textBendingPictureCaption": "Keterangan Gambar Meliuk", "Common.define.smartArt.textBendingPictureCaptionList": "Daftar Keterangan Gambar Meliuk", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Teks Semi-Transparan G<PERSON>", "Common.define.smartArt.textBlockCycle": "Lingkaran Blok", "Common.define.smartArt.textBubblePictureList": "Daftar Gambar Gelembung", "Common.define.smartArt.textCaptionedPictures": "Gambar Dengan Keterangan", "Common.define.smartArt.textChevronAccentProcess": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Common.define.smartArt.textChevronList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textCircleAccentTimeline": "<PERSON><PERSON>", "Common.define.smartArt.textCircleArrowProcess": "Proses <PERSON><PERSON>", "Common.define.smartArt.textCirclePictureHierarchy": "<PERSON>era<PERSON><PERSON>", "Common.define.smartArt.textCircleProcess": "<PERSON><PERSON>", "Common.define.smartArt.textCircleRelationship": "Hubungan <PERSON>", "Common.define.smartArt.textCircularBendingProcess": "<PERSON><PERSON>", "Common.define.smartArt.textCircularPictureCallout": "Panggila<PERSON>", "Common.define.smartArt.textClosedChevronProcess": "Proses Ch<PERSON>ron <PERSON>", "Common.define.smartArt.textContinuousArrowProcess": "Proses <PERSON><PERSON>", "Common.define.smartArt.textContinuousBlockProcess": "Proses Blok Berkelanjutan", "Common.define.smartArt.textContinuousCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textContinuousPictureList": "Daftar Gambar Berkelanjutan", "Common.define.smartArt.textConvergingArrows": "Panah <PERSON>at", "Common.define.smartArt.textConvergingRadial": "Radial Memusat", "Common.define.smartArt.textConvergingText": "Teks Memusat", "Common.define.smartArt.textCounterbalanceArrows": "Panah <PERSON>", "Common.define.smartArt.textCycle": "Siklus", "Common.define.smartArt.textCycleMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textDescendingBlockList": "Daftar Blok Turun", "Common.define.smartArt.textDescendingProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDetailedProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDivergingArrows": "Panah <PERSON>", "Common.define.smartArt.textDivergingRadial": "<PERSON><PERSON>", "Common.define.smartArt.textEquation": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textFramedTextPicture": "Gambar Teks Terbingkai", "Common.define.smartArt.textFunnel": "Corong", "Common.define.smartArt.textGear": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textGridMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textGroupedList": "Daftar yang <PERSON>", "Common.define.smartArt.textHalfCircleOrganizationChart": "Bagan Organisasi Seteng<PERSON>n", "Common.define.smartArt.textHexagonCluster": "Kluster Segi Enam", "Common.define.smartArt.textHexagonRadial": "<PERSON><PERSON>", "Common.define.smartArt.textHierarchy": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textHierarchyList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textHorizontalBulletList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textHorizontalHierarchy": "Hierarki Horizontal", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Hierarki Be<PERSON>abe<PERSON>", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Hierarki Multi-Level Horizontal", "Common.define.smartArt.textHorizontalOrganizationChart": "Bagan Organisasi Horizontal", "Common.define.smartArt.textHorizontalPictureList": "Daftar Gambar Horizontal", "Common.define.smartArt.textIncreasingArrowProcess": "Proses <PERSON><PERSON>", "Common.define.smartArt.textIncreasingCircleProcess": "Proses <PERSON><PERSON> Meningkat", "Common.define.smartArt.textInterconnectedBlockProcess": "Proses Blok yang <PERSON>ing Terhubung", "Common.define.smartArt.textInterconnectedRings": "<PERSON><PERSON><PERSON> yang <PERSON>hu<PERSON>", "Common.define.smartArt.textInvertedPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textLabeledHierarchy": "<PERSON>era<PERSON><PERSON>", "Common.define.smartArt.textLinearVenn": "Venn Linear", "Common.define.smartArt.textLinedList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Bagan <PERSON> dan <PERSON>", "Common.define.smartArt.textNestedTarget": "Target Bertumpuk", "Common.define.smartArt.textNondirectionalCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textOpposingArrows": "<PERSON><PERSON>", "Common.define.smartArt.textOpposingIdeas": "<PERSON><PERSON>", "Common.define.smartArt.textOrganizationChart": "Bagan Organisasi", "Common.define.smartArt.textOther": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textPhasedProcess": "Proses Berf<PERSON>", "Common.define.smartArt.textPicture": "Gambar", "Common.define.smartArt.textPictureAccentBlocks": "Blok Aksen Gambar", "Common.define.smartArt.textPictureAccentList": "Daftar Aksen G<PERSON>bar", "Common.define.smartArt.textPictureAccentProcess": "Proses Aksen <PERSON>", "Common.define.smartArt.textPictureCaptionList": "Daftar Keterangan Gambar", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "<PERSON><PERSON>", "Common.define.smartArt.textPictureLineup": "Deretan Gambar", "Common.define.smartArt.textPictureOrganizationChart": "Bagan Organisasi Gambar", "Common.define.smartArt.textPictureStrips": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textPieProcess": "Proses <PERSON>", "Common.define.smartArt.textPlusAndMinus": "Plus dan Minus", "Common.define.smartArt.textProcess": "Proses", "Common.define.smartArt.textProcessArrows": "Panah Proses", "Common.define.smartArt.textProcessList": "Daftar Proses", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRadialCluster": "Kluster Radial", "Common.define.smartArt.textRadialCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRadialList": "<PERSON>ftar Radial", "Common.define.smartArt.textRadialPictureList": "<PERSON>ftar Gambar Radial", "Common.define.smartArt.textRadialVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRandomToResultProcess": "Proses Acak ke Hasil", "Common.define.smartArt.textRelationship": "Hubungan", "Common.define.smartArt.textRepeatingBendingProcess": "Proses <PERSON><PERSON>", "Common.define.smartArt.textReverseList": "Daftar Terbalik", "Common.define.smartArt.textSegmentedCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textSegmentedProcess": "<PERSON>ses <PERSON>", "Common.define.smartArt.textSegmentedPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textSnapshotPictureList": "Daftar Gambar Snapshot", "Common.define.smartArt.textSpiralPicture": "Gambar S<PERSON>ral", "Common.define.smartArt.textSquareAccentList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStackedList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "<PERSON><PERSON>", "Common.define.smartArt.textStepDownProcess": "<PERSON><PERSON>", "Common.define.smartArt.textStepUpProcess": "Proses Meningkat", "Common.define.smartArt.textSubStepProcess": "Proses Sub-Langkah", "Common.define.smartArt.textTabbedArc": "<PERSON><PERSON>", "Common.define.smartArt.textTableHierarchy": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textTableList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textTabList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textTargetList": "Daftar Target", "Common.define.smartArt.textTextCycle": "Siklus <PERSON>", "Common.define.smartArt.textThemePictureAccent": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textThemePictureAlternatingAccent": "<PERSON><PERSON><PERSON>-Balik Gambar Te<PERSON>", "Common.define.smartArt.textThemePictureGrid": "<PERSON><PERSON>", "Common.define.smartArt.textTitledMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textTitledPictureAccentList": "Daftar Aksen Gambar Be<PERSON>", "Common.define.smartArt.textTitledPictureBlocks": "Blok Gambar Berjudul", "Common.define.smartArt.textTitlePictureLineup": "Deretan <PERSON>", "Common.define.smartArt.textTrapezoidList": "Daftar Trapesium", "Common.define.smartArt.textUpwardArrow": "Panah ke Atas", "Common.define.smartArt.textVaryingWidthList": "<PERSON><PERSON><PERSON> den<PERSON>", "Common.define.smartArt.textVerticalAccentList": "Daftar Aksen Vertikal", "Common.define.smartArt.textVerticalArrowList": "<PERSON>ftar Panah Vertikal", "Common.define.smartArt.textVerticalBendingProcess": "Arah Proses Vertikal", "Common.define.smartArt.textVerticalBlockList": "Daftar Blok Vertikal", "Common.define.smartArt.textVerticalBoxList": "Daftar Kotak Vertikal", "Common.define.smartArt.textVerticalBracketList": "Daftar Tanda Kurung Vertikal", "Common.define.smartArt.textVerticalBulletList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalChevronList": "<PERSON><PERSON><PERSON>ev<PERSON>", "Common.define.smartArt.textVerticalCircleList": "Daftar Lingkaran Vertikal", "Common.define.smartArt.textVerticalCurvedList": "Daftar Kurva Vertikal", "Common.define.smartArt.textVerticalEquation": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalPictureAccentList": "Daftar Aksen Gambar Vertikal", "Common.define.smartArt.textVerticalPictureList": "Daftar Gambar Vertikal", "Common.define.smartArt.textVerticalProcess": "Proses Vertikal", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON>", "Common.Translation.tipFileLocked": "Dokumen terkunci untuk diedit. <PERSON>a dapat membuat perubahan dan menyimpannya sebagai salinan lokal nanti.", "Common.Translation.tipFileReadOnly": "Dokumen hanya dapat dibaca. Untuk menyimpan perubahan <PERSON>, simpan file dengan nama baru atau di tempat lain.", "Common.Translation.warnFileLocked": "File sedang diedit di aplikasi lain. Anda bisa melanjutkan edit dan menyimpannya sebagai salinan.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON>t salinan", "Common.Translation.warnFileLockedBtnView": "<PERSON>uka untuk dilihat", "Common.UI.ButtonColored.textAutoColor": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ButtonColored.textEyedropper": "<PERSON><PERSON> warna", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON> banyak warna", "Common.UI.ComboBorderSize.txtNoBorders": "Tidak ada pembatas", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Tidak ada pembatas", "Common.UI.ComboDataView.emptyComboText": "Tidak ada model", "Common.UI.ExtendedColorDialog.addButtonText": "Tambahkan", "Common.UI.ExtendedColorDialog.textCurrent": "Saat ini", "Common.UI.ExtendedColorDialog.textHexErr": "Input yang dimasukkan salah.<br><PERSON><PERSON><PERSON> masukkan input antara 000000 dan FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Input yang <PERSON>a masukkan salah.<br><PERSON><PERSON><PERSON> masukkan input numerik antara 0 dan 255.", "Common.UI.HSBColorPicker.textNoColor": "Tidak ada <PERSON>", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Sembunyikan password", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON><PERSON><PERSON> password", "Common.UI.SearchBar.textFind": "Temukan", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "<PERSON><PERSON>", "Common.UI.SearchBar.tipOpenAdvancedSettings": "<PERSON><PERSON> pengaturan lan<PERSON>", "Common.UI.SearchBar.tipPreviousResult": "<PERSON><PERSON>", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON> hasil", "Common.UI.SearchDialog.textMatchCase": "Harus sama persis", "Common.UI.SearchDialog.textReplaceDef": "Tuliskan teks pengganti", "Common.UI.SearchDialog.textSearchStart": "Tuliskan teks Anda di sini", "Common.UI.SearchDialog.textTitle": "<PERSON>i dan ganti", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON><PERSON><PERSON> kata saja", "Common.UI.SearchDialog.txtBtnHideReplace": "Sembunyikan Replace", "Common.UI.SearchDialog.txtBtnReplace": "Ganti", "Common.UI.SearchDialog.txtBtnReplaceAll": "Ganti semua", "Common.UI.SynchronizeTip.textDontShow": "<PERSON>an tampilkan pesan ini lagi", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Dokumen telah diubah oleh pengguna lain.<br><PERSON>lakan klik untuk menyimpan perubahan dan memuat ulang pembaruan.", "Common.UI.ThemeColorPalette.textRecentColors": "<PERSON><PERSON>-<PERSON>a <PERSON>", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON>ar", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON> tema", "Common.UI.Themes.txtThemeClassicLight": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeContrastDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Sama seperti sistem", "Common.UI.Window.cancelButtonText": "Batalkan", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Tidak", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "<PERSON>an tampilkan pesan ini lagi", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "Informasi", "Common.UI.Window.textWarning": "Peringatan", "Common.UI.Window.yesButtonText": "Ya", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "<PERSON><PERSON> be<PERSON>", "Common.Utils.ThemeColor.txtBlack": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtBlue": "Biru", "Common.Utils.ThemeColor.txtBrightGreen": "<PERSON><PERSON><PERSON> terang", "Common.Utils.ThemeColor.txtBrown": "Coklat", "Common.Utils.ThemeColor.txtDarkBlue": "<PERSON>iru gelap", "Common.Utils.ThemeColor.txtDarker": "<PERSON><PERSON><PERSON> gelap", "Common.Utils.ThemeColor.txtDarkGray": "<PERSON><PERSON><PERSON> gelap", "Common.Utils.ThemeColor.txtDarkGreen": "<PERSON><PERSON><PERSON> tua", "Common.Utils.ThemeColor.txtDarkPurple": "<PERSON>gu tua", "Common.Utils.ThemeColor.txtDarkRed": "<PERSON><PERSON> tua", "Common.Utils.ThemeColor.txtDarkTeal": "Hijau kebiruan tua", "Common.Utils.ThemeColor.txtDarkYellow": "<PERSON><PERSON> tua", "Common.Utils.ThemeColor.txtGold": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtGray": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtGreen": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "<PERSON><PERSON><PERSON> terang", "Common.Utils.ThemeColor.txtLighter": "<PERSON><PERSON><PERSON> terang", "Common.Utils.ThemeColor.txtLightGray": "<PERSON><PERSON><PERSON>a", "Common.Utils.ThemeColor.txtLightGreen": "<PERSON><PERSON><PERSON> muda", "Common.Utils.ThemeColor.txtLightOrange": "Jingga terang", "Common.Utils.ThemeColor.txtLightYellow": "<PERSON><PERSON> muda", "Common.Utils.ThemeColor.txtOrange": "Jingga", "Common.Utils.ThemeColor.txtPink": "<PERSON><PERSON> muda", "Common.Utils.ThemeColor.txtPurple": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtRed": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtRose": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "<PERSON><PERSON><PERSON> langit", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Teks", "Common.Utils.ThemeColor.txtTurquosie": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtViolet": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtWhite": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtYellow": "<PERSON><PERSON>", "Common.Views.About.txtAddress": "alamat:", "Common.Views.About.txtLicensee": "PEMEGANG LISENSI", "Common.Views.About.txtLicensor": "PEMBERI LISENSI", "Common.Views.About.txtMail": "email:", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel:", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "Tambahkan", "Common.Views.AutoCorrectDialog.textApplyText": "Terapkan sambil Anda menulis", "Common.Views.AutoCorrectDialog.textAutoCorrect": "AutoKoreksi Teks", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat sambil Anda mengetik", "Common.Views.AutoCorrectDialog.textBulleted": "Butir list otomatis", "Common.Views.AutoCorrectDialog.textBy": "oleh", "Common.Views.AutoCorrectDialog.textDelete": "Hapus", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Tambahkan titik dengan spasi ganda", "Common.Views.AutoCorrectDialog.textFLCells": "Besarkan huruf pertama di sel tabel", "Common.Views.AutoCorrectDialog.textFLDont": "<PERSON><PERSON> ka<PERSON>an set<PERSON>", "Common.Views.AutoCorrectDialog.textFLSentence": "Besarkan huruf pertama di kalimat", "Common.Views.AutoCorrectDialog.textForLangFL": "Pengecualian bagi bahasa:", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet dan jalur jaringan dengan hyperlink.", "Common.Views.AutoCorrectDialog.textHyphens": "Hyphens (--) dengan garis putus-putus (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "AutoCorrect Matematika", "Common.Views.AutoCorrectDialog.textNumbered": "Penomoran list otomatis", "Common.Views.AutoCorrectDialog.textQuotes": "\"Straight quotes\" dengan \"smart quotes\"", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON> yang dikenali", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Ekspresi ini merupakan ekspresi matematika. Ekspresi ini tidak akan dimiringkan secara otomatis.", "Common.Views.AutoCorrectDialog.textReplace": "Ganti", "Common.Views.AutoCorrectDialog.textReplaceText": "Ganti sambil Anda men<PERSON>ik", "Common.Views.AutoCorrectDialog.textReplaceType": "Ganti teks saat Anda mengetik", "Common.Views.AutoCorrectDialog.textReset": "<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textResetAll": "<PERSON>ur ulang kembali ke awal", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "AutoCorrect", "Common.Views.AutoCorrectDialog.textWarnAddFL": "Pengecualian hanya boleh memuat huruf besar atau kecil.", "Common.Views.AutoCorrectDialog.textWarnAddRec": "<PERSON><PERSON>i yang diterima harus memiliki huruf A sampai Z, huruf besar atau huruf kecil.", "Common.Views.AutoCorrectDialog.textWarnResetFL": "<PERSON><PERSON><PERSON> pengecualian yang Anda tambahkan akan dihapus dan yang sebelumnya dihapus akan dipulihkan. Apakah Anda hendak melanjutkan?", "Common.Views.AutoCorrectDialog.textWarnResetRec": "<PERSON><PERSON><PERSON> eksp<PERSON>i yang Anda tambahkan akan dihilangkan dan yang sudah terhapus akan dikembalikan. A<PERSON>kah Anda ingin melanjutkan?", "Common.Views.AutoCorrectDialog.warnReplace": "Entri autocorrect untuk %1 sudah ada. Apakah Anda ingin menggantinya?", "Common.Views.AutoCorrectDialog.warnReset": "Semua autocorrect yang Anda tambahkan akan dihilangkan dan yang sudah diganti akan dikembalikan ke nilai awalnya. Apakah Anda ingin melanjutkan?", "Common.Views.AutoCorrectDialog.warnRestore": "Entri autocorrect untuk %1 akan di reset ke nilai awal. <PERSON><PERSON><PERSON>h Anda ingin melanjutkan?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "<PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Penulis A sampai Z", "Common.Views.Comments.mniAuthorDesc": "<PERSON><PERSON><PERSON> Z sampai A", "Common.Views.Comments.mniDateAsc": "Tertua", "Common.Views.Comments.mniDateDesc": "Terbaru", "Common.Views.Comments.mniFilterGroups": "<PERSON><PERSON>", "Common.Views.Comments.mniPositionAsc": "<PERSON><PERSON> atas", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON> bawah", "Common.Views.Comments.textAdd": "Tambahkan", "Common.Views.Comments.textAddComment": "Tambahkan komentar", "Common.Views.Comments.textAddCommentToDoc": "Tambahkan komentar untuk dokumen", "Common.Views.Comments.textAddReply": "Tambah<PERSON>", "Common.Views.Comments.textAll": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "<PERSON><PERSON>", "Common.Views.Comments.textCancel": "Batalkan", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Tutup komentar", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "Komentar", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Tuliskan komentar Anda di sini", "Common.Views.Comments.textHintAddComment": "Tambahkan komentar", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON>", "Common.Views.Comments.textReply": "<PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "Diselesaikan", "Common.Views.Comments.textSort": "Sortir komentar", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "Anda tidak memiliki izin membuka kembali komentar", "Common.Views.Comments.txtEmpty": "Tidak ada komentar pada dokumen.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON>an tampilkan pesan ini lagi", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON>, potong dan tempel menggunakan tombol editor toolbar dan menu konteks dapat dilakukan hanya dengan tab editor ni saja.<br><br>Untuk menyalin atau menempel ke atau dari aplikasi di luar tab editor, gunakan kombinasi tombol keyboard berikut ini:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, da<PERSON>", "Common.Views.CopyWarningDialog.textToCopy": "untuk <PERSON>", "Common.Views.CopyWarningDialog.textToCut": "untuk Potong", "Common.Views.CopyWarningDialog.textToPaste": "untuk Tempel", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textStartOver": "Show from Beginning", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Memuat...", "Common.Views.DocumentAccessDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> berbagi", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Draw.hintSelect": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtEraser": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Draw.txtHighlighter": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "<PERSON><PERSON>", "Common.Views.Draw.txtSelect": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtSize": "Ukuran", "Common.Views.ExternalDiagramEditor.textTitle": "Editor <PERSON><PERSON>", "Common.Views.ExternalEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ExternalEditor.textSave": "Simpan & Keluar", "Common.Views.ExternalOleEditor.textTitle": "Penyunting Spreadsheet", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "User yang sedang edit file:", "Common.Views.Header.textAddFavorite": "Tandai sebagai favorit", "Common.Views.Header.textAdvSettings": "Pen<PERSON><PERSON><PERSON>", "Common.Views.Header.textBack": "<PERSON><PERSON> Dokumen", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "Sembunyikan Toolbar", "Common.Views.Header.textHideLines": "Sembunyikan Mistar", "Common.Views.Header.textHideNotes": "Sembunyikan Catatan", "Common.Views.Header.textHideStatusBar": "Sembunyikan Bilah Status", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "<PERSON><PERSON> baca", "Common.Views.Header.textRemoveFavorite": "Hilangkan da<PERSON>", "Common.Views.Header.textSaveBegin": "Menyimpan...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON><PERSON> te<PERSON>", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON><PERSON> te<PERSON>", "Common.Views.Header.textShare": "Bagikan", "Common.Views.Header.textStartOver": "Show from Beginning", "Common.Views.Header.textZoom": "Pembesaran", "Common.Views.Header.tipAccessRights": "Atur perizinan akses dokumen", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "Unduh File", "Common.Views.Header.tipGoEdit": "Edit file saat ini", "Common.Views.Header.tipPrint": "Print file", "Common.Views.Header.tipPrintQuick": "Cetak cepat", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Simpan", "Common.Views.Header.tipSearch": "<PERSON><PERSON>", "Common.Views.Header.tipStartOver": "Start slideshow from beginning", "Common.Views.Header.tipUndo": "Batalkan", "Common.Views.Header.tipUndock": "Buka dock ke jendela terpisah", "Common.Views.Header.tipUsers": "<PERSON><PERSON>", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON>", "Common.Views.Header.tipViewUsers": "Tampilkan user dan atur hak akses dokumen", "Common.Views.Header.txtAccessRights": "Ubah hak akses", "Common.Views.Header.txtRename": "Ganti nama", "Common.Views.History.textCloseHistory": "Tutup riwayat", "Common.Views.History.textHideAll": "Sembunyikan <PERSON> perubahan", "Common.Views.History.textHighlightDeleted": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "Tampilkan <PERSON> perubahan", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Tempel URL gambar:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Ruas ini harus berupa URL dengan format \"http://www.contoh.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "<PERSON>a harus menentukan baris dan jumlah kolom yang benar.", "Common.Views.InsertTableDialog.txtColumns": "<PERSON><PERSON><PERSON> kolo<PERSON>", "Common.Views.InsertTableDialog.txtMaxText": "<PERSON><PERSON> maksimal untuk ruas ini adalah {0}.", "Common.Views.InsertTableDialog.txtMinText": "<PERSON>lai minimal untuk ruas ini adalah {0}.", "Common.Views.InsertTableDialog.txtRows": "<PERSON><PERSON><PERSON> bar<PERSON>", "Common.Views.InsertTableDialog.txtTitle": "Ukuran tabel", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON> sel", "Common.Views.LanguageDialog.labelSelect": "<PERSON><PERSON><PERSON> bahasa dokumen", "Common.Views.ListSettingsDialog.textBulleted": "Poin", "Common.Views.ListSettingsDialog.textFromFile": "Dari file", "Common.Views.ListSettingsDialog.textFromStorage": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromUrl": "Dari URL", "Common.Views.ListSettingsDialog.textNumbering": "Bernomor", "Common.Views.ListSettingsDialog.textSelect": "<PERSON><PERSON><PERSON> dari", "Common.Views.ListSettingsDialog.tipChange": "Ubah butir", "Common.Views.ListSettingsDialog.txtBullet": "Butir", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Gambar", "Common.Views.ListSettingsDialog.txtImport": "Impor", "Common.Views.ListSettingsDialog.txtNewBullet": "<PERSON>ir baru", "Common.Views.ListSettingsDialog.txtNewImage": "Gambar baru", "Common.Views.ListSettingsDialog.txtNone": "Tidak ada", "Common.Views.ListSettingsDialog.txtOfText": "% dari teks", "Common.Views.ListSettingsDialog.txtSize": "Ukuran", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON><PERSON> pada", "Common.Views.ListSettingsDialog.txtSymbol": "Simbol", "Common.Views.ListSettingsDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> daftar", "Common.Views.ListSettingsDialog.txtType": "Tipe", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "Tutup file", "Common.Views.OpenDialog.txtEncoding": "Enkoding", "Common.Views.OpenDialog.txtIncorrectPwd": "Password salah.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON><PERSON><PERSON>n kata sandi untuk buka file", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON> Anda memasukkan password dan membuka file, password file saat ini akan di reset.", "Common.Views.OpenDialog.txtTitle": "Pilih %1 opsi", "Common.Views.OpenDialog.txtTitleProtected": "File terproteksi", "Common.Views.PasswordDialog.txtDescription": "Buat password untuk melindungi dokumen ini", "Common.Views.PasswordDialog.txtIncorrectPwd": "Password konfirmasi tidak sama", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON> password", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON> kata sandi", "Common.Views.PasswordDialog.txtWarning": "Peringatan: Tidak bisa dipulihkan jika Anda kehilangan atau lupa kata sandi. Simpan di tempat yang aman.", "Common.Views.PluginDlg.textLoading": "Memuat", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "<PERSON><PERSON>", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON><PERSON><PERSON> dengan password", "Common.Views.Protection.hintDelPwd": "<PERSON><PERSON> kata sandi", "Common.Views.Protection.hintPwd": "Ganti atau hapus password", "Common.Views.Protection.hintSignature": "Tambah tanda tangan digital atau garis tanda tangan", "Common.Views.Protection.txtAddPwd": "Tambah password", "Common.Views.Protection.txtChangePwd": "Ubah kata sandi", "Common.Views.Protection.txtDeletePwd": "Nama file", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "Tambah tanda tangan digital", "Common.Views.Protection.txtSignature": "<PERSON><PERSON>", "Common.Views.Protection.txtSignatureLine": "<PERSON><PERSON> garis tanda tangan", "Common.Views.RecentFiles.txtOpenRecent": "<PERSON><PERSON> yang <PERSON>", "Common.Views.RenameDialog.textName": "Nama file", "Common.Views.RenameDialog.txtInvalidName": "Nama file tidak boleh berisi karakter seperti:", "Common.Views.ReviewChanges.hintNext": "<PERSON> berik<PERSON>", "Common.Views.ReviewChanges.hintPrev": "<PERSON>han sebelumnya", "Common.Views.ReviewChanges.strFast": "Cepat", "Common.Views.ReviewChanges.strFastDesc": "Co-editing real-time. <PERSON><PERSON><PERSON> disimpan otomatis.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Gunakan tombol 'Simpan' untuk sinkronisasi perubahan yang dibuat Anda dan orang lain.", "Common.Views.ReviewChanges.tipAcceptCurrent": "<PERSON><PERSON> perubahan saat ini", "Common.Views.ReviewChanges.tipCoAuthMode": "Atur mode co-editing", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON><PERSON> komentar", "Common.Views.ReviewChanges.tipCommentRemCurrent": "<PERSON>pus komentar saat ini", "Common.Views.ReviewChanges.tipCommentResolve": "Selesaikan komentar", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Selesaikan komentar saat ini", "Common.Views.ReviewChanges.tipHistory": "Tampilkan riwayat versi", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON> perubahan saat ini", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Pilih mode yang per<PERSON>hannya ingin Anda tampilkan", "Common.Views.ReviewChanges.tipSetDocLang": "Atur Bahasa Dokumen", "Common.Views.ReviewChanges.tipSetSpelling": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipSharing": "Atur perizinan akses dokumen", "Common.Views.ReviewChanges.txtAccept": "Terima", "Common.Views.ReviewChanges.txtAcceptAll": "<PERSON><PERSON> semua per<PERSON>han", "Common.Views.ReviewChanges.txtAcceptChanges": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Mode Edit <PERSON>", "Common.Views.ReviewChanges.txtCommentRemAll": "<PERSON><PERSON> semua komentar", "Common.Views.ReviewChanges.txtCommentRemCurrent": "<PERSON>pus komentar saat ini", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON><PERSON> komentar saya", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "<PERSON>pus Ko<PERSON>", "Common.Views.ReviewChanges.txtCommentRemove": "Hapus", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON><PERSON> semua komentar", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Selesaikan komentar saat ini", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON>ai<PERSON> komentar saya", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Selesaikan Ko<PERSON> Say<PERSON>", "Common.Views.ReviewChanges.txtDocLang": "Bahasa", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON><PERSON> (Preview)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Riwayat versi", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON><PERSON> (Editing)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Selanjutnya", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON><PERSON> (Preview)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Sebelumnya", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtSharing": "Bagikan", "Common.Views.ReviewChanges.txtSpelling": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "Mode Tampilan", "Common.Views.ReviewPopover.textAdd": "Tambahkan", "Common.Views.ReviewPopover.textAddReply": "Tambah<PERSON>", "Common.Views.ReviewPopover.textCancel": "Batalkan", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Tuliskan komentar Anda di sini", "Common.Views.ReviewPopover.textMention": "+mention akan memberikan akses ke dokumen dan mengirimkan email", "Common.Views.ReviewPopover.textMentionNotify": "+mention akan menging<PERSON>kan user lewat email", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Anda tidak memiliki izin membuka kembali komentar", "Common.Views.ReviewPopover.txtDeleteTip": "Hapus", "Common.Views.ReviewPopover.txtEditTip": "Sunting", "Common.Views.SaveAsDlg.textLoading": "Memuat", "Common.Views.SaveAsDlg.textTitle": "Folder untuk simpan", "Common.Views.SearchPanel.textCaseSensitive": "<PERSON><PERSON> huruf besar-kecil", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textContentChanged": "<PERSON>kumen telah diubah.", "Common.Views.SearchPanel.textFind": "Temukan", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON><PERSON><PERSON> dan ganti", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} item sukses diganti.", "Common.Views.SearchPanel.textMatchUsingRegExp": "Cocokkan dengan ekspresi reguler", "Common.Views.SearchPanel.textNoMatches": "Tidak ada yang cocok", "Common.Views.SearchPanel.textNoSearchResults": "Tidak ada hasil pencarian", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} item diganti. Sisa {2} item sedang dikunci oleh pengguna lain.", "Common.Views.SearchPanel.textReplace": "Ganti", "Common.Views.SearchPanel.textReplaceAll": "Ganti Semua", "Common.Views.SearchPanel.textReplaceWith": "Ganti dengan", "Common.Views.SearchPanel.textSearchAgain": "{0}Lakukan pencarian baru{1} untuk hasil yang akurat.", "Common.Views.SearchPanel.textSearchHasStopped": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSearchResults": "<PERSON><PERSON> pencarian: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textTooManyResults": "<PERSON><PERSON><PERSON>u banyak hasil untuk ditampilkan di sini", "Common.Views.SearchPanel.textWholeWords": "<PERSON><PERSON><PERSON><PERSON> kata saja", "Common.Views.SearchPanel.tipNextResult": "<PERSON><PERSON>", "Common.Views.SearchPanel.tipPreviousResult": "<PERSON><PERSON>", "Common.Views.SelectFileDlg.textLoading": "Memuat", "Common.Views.SelectFileDlg.textTitle": "Pilih sumber data", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "<PERSON><PERSON>", "Common.Views.SignDialog.textCertificate": "Ser<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Ganti", "Common.Views.SignDialog.textInputName": "<PERSON><PERSON><PERSON><PERSON> nama penanda<PERSON>", "Common.Views.SignDialog.textItalic": "Miring", "Common.Views.SignDialog.textNameError": "<PERSON>a penanda<PERSON>gan tidak boleh kosong.", "Common.Views.SignDialog.textPurpose": "Tu<PERSON>an menandatangani dokumen ini", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON> gambar", "Common.Views.SignDialog.textSignature": "Tandatangan terlihat seperti", "Common.Views.SignDialog.textTitle": "Tanda tangani dokumen", "Common.Views.SignDialog.textUseImage": "atau klik '<PERSON>lih G<PERSON>' untuk menjadikan gambar sebagai tandatangan", "Common.Views.SignDialog.textValid": "Valid dari %1 sampai %2", "Common.Views.SignDialog.tipFontName": "Nama font", "Common.Views.SignDialog.tipFontSize": "Ukuran font", "Common.Views.SignSettingsDialog.textAllowComment": "Izinkan penandatangan untuk menambahkan komentar di dialog tanda tangan", "Common.Views.SignSettingsDialog.textDefInstruction": "Sebelum menandatangani dokumen ini, pastikan konten yang akan Anda tanda tangani sudah benar.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail penandatangan yang disarankan", "Common.Views.SignSettingsDialog.textInfoName": "Penandatangan yang disarankan", "Common.Views.SignSettingsDialog.textInfoTitle": "<PERSON><PERSON><PERSON> penanda<PERSON>gan yang disarankan", "Common.Views.SignSettingsDialog.textInstructions": "Instruksi untuk penandatangan", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON><PERSON><PERSON> tanggal di garis tandatangan", "Common.Views.SignSettingsDialog.textTitle": "Penyiapan tanda tangan", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Nilai Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Tanda hak cipta", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON> ganda penutup", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON><PERSON> ganda pembuka", "Common.Views.SymbolTableDialog.textEllipsis": "Ellipsis horizontal", "Common.Views.SymbolTableDialog.textEmDash": "Em dash", "Common.Views.SymbolTableDialog.textEmSpace": "Em space", "Common.Views.SymbolTableDialog.textEnDash": "En dash", "Common.Views.SymbolTableDialog.textEnSpace": "En space", "Common.Views.SymbolTableDialog.textFont": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "Tanda sambung tak-putus", "Common.Views.SymbolTableDialog.textNBSpace": "Spasi tak-putus", "Common.Views.SymbolTableDialog.textPilcrow": "Tanda pilcrow", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em space", "Common.Views.SymbolTableDialog.textRange": "Rentang", "Common.Views.SymbolTableDialog.textRecent": "Simbol yang baru digunakan", "Common.Views.SymbolTableDialog.textRegistered": "<PERSON><PERSON> te<PERSON>", "Common.Views.SymbolTableDialog.textSCQuote": "<PERSON><PERSON> tunggal penutup", "Common.Views.SymbolTableDialog.textSection": "<PERSON><PERSON> seksi", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON> pin<PERSON>an", "Common.Views.SymbolTableDialog.textSHyphen": "Tanda hubung lunak", "Common.Views.SymbolTableDialog.textSOQuote": "<PERSON><PERSON> tunggal pembuka", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Simbol", "Common.Views.SymbolTableDialog.textTitle": "Simbol", "Common.Views.SymbolTableDialog.textTradeMark": "Simbol merk dagang ", "Common.Views.UserNameDialog.textDontShow": "<PERSON>an tanya saya lagi", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label tidak boleh kosong.", "PE.Controllers.DocumentHolder.textLongName": "Enter a name that is less than 255 characters.", "PE.Controllers.DocumentHolder.textNameLayout": "Layout name", "PE.Controllers.DocumentHolder.textNameMaster": "Master name", "PE.Controllers.DocumentHolder.textRenameTitleLayout": "<PERSON><PERSON>out", "PE.Controllers.DocumentHolder.textRenameTitleMaster": "<PERSON><PERSON>", "PE.Controllers.LeftMenu.leavePageText": "Se<PERSON><PERSON> perubahan yang tidak tersimpan di dokumen ini akan hilang.<br> <PERSON><PERSON> \"<PERSON><PERSON>\" lalu \"<PERSON><PERSON><PERSON>\" untuk menyimpan. Klik \"OK\" untuk membuang semua perubahan yang tidak tersimpan.", "PE.Controllers.LeftMenu.newDocumentTitle": "<PERSON><PERSON> tanpa nama", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Peringatan", "PE.Controllers.LeftMenu.requestEditRightsText": "Me<PERSON>ta hak editing...", "PE.Controllers.LeftMenu.textLoadHistory": "Loading versi riwayat...", "PE.Controllers.LeftMenu.textNoTextFound": "Data yang Anda cari tidak ditemukan. <PERSON>lakan atur opsi pencarian <PERSON>a.", "PE.Controllers.LeftMenu.textReplaceSkipped": "Penggantian telah di<PERSON>n. Ada {0} yang di<PERSON>.", "PE.Controllers.LeftMenu.textReplaceSuccess": "Pencarian telah <PERSON>. <PERSON> {0} yang diganti {0}", "PE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "PE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON>", "PE.Controllers.Main.applyChangesTextText": "Memuat data...", "PE.Controllers.Main.applyChangesTitleText": "Memuat Data", "PE.Controllers.Main.confirmMaxChangesSize": "Ukuran tindakan melebihi batas yang ditetapkan untuk server <PERSON><PERSON>.<br><PERSON><PERSON> \"Batalkan\" untuk membatalkan tindakan terakhir Anda atau tekan \"Lanjutkan\" untuk menyimpan tindakan secara lokal (Anda perlu mengunduh file atau menyalin isinya untuk memastikan tidak ada yang hilang).", "PE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON>tu konversi habis.", "PE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON> \"OK\" untuk kembali ke daftar dokumen.", "PE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON> gagal.", "PE.Controllers.Main.downloadTextText": "<PERSON><PERSON><PERSON><PERSON>...", "PE.Controllers.Main.downloadTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON> mencoba melakukan sesuatu yang tidak memiliki izin.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>.", "PE.Controllers.Main.errorBadImageUrl": "URL Gambar salah", "PE.Controllers.Main.errorCannotPasteImg": "<PERSON>mi tak bisa menempel gambar ini dari <PERSON>, tapi Anda dapat menyimpannya\nke perangkat Anda dan menyisipkannya dari sana, atau Anda dapat menyalin gambar\ntanpa teks dan menempelkannya ke dalam presentasi.", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Koneksi server terputus. Saat ini dokumen tidak dapat diedit.", "PE.Controllers.Main.errorComboSeries": "Untuk membuat grafik kombinasi, pilih setidaknya dua seri data.", "PE.Controllers.Main.errorConnectToServer": "Dokumen tidak bisa disimpan. <PERSON>lakan periksa pengaturan koneksi atau hubungi admin Anda.<br><PERSON><PERSON><PERSON> klik tombol 'OK', <PERSON><PERSON> akan diminta untuk download dokumen.", "PE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON> eksternal.<br>Koneksi database bermasalah. <PERSON><PERSON>an hubungi layanan bantuan jika tetap terjadi error.", "PE.Controllers.Main.errorDataEncrypted": "Perubahan enkripsi sudah diterima dan tidak bisa diuraikan.", "PE.Controllers.Main.errorDataRange": "Rentang data salah.", "PE.Controllers.Main.errorDefaultMessage": "Kode k<PERSON>alahan: %1", "PE.Controllers.Main.errorDirectUrl": "<PERSON>lakan verifikasi tautan ke dokumen.<br>Tautan ini harus merupakan tautan langsung menuju file yang akan diunduh.", "PE.Controllers.Main.errorEditingDownloadas": "<PERSON> kesalahan saat bekerja dengan dokumen.<br><PERSON><PERSON><PERSON> opsi 'Download sebagai' untuk menyimpan file salinan backup ke komputer Anda.", "PE.Controllers.Main.errorEditingSaveas": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat bekerja dengan dokumen.<br><PERSON><PERSON><PERSON> opsi 'Simpan sebagai...' untuk menyimpan salinan pencadangan file ke harddisk.", "PE.Controllers.Main.errorEmailClient": "<PERSON><PERSON> klein tidak bisa ditemukan.", "PE.Controllers.Main.errorFilePassProtect": "Dokumen dilindungi dengan kata sandi dan tidak dapat dibuka.", "PE.Controllers.Main.errorFileSizeExceed": "Ukuran file melewati batas server <PERSON><PERSON>.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>a untuk detail.", "PE.Controllers.Main.errorForceSave": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat menyimpan file. <PERSON><PERSON><PERSON> gunakan opsi 'Unduh sebagai' untuk menyimpan file ke harddisk atau coba lagi nanti.", "PE.Controllers.Main.errorInconsistentExt": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file tidak cocok dengan ekstensi file.", "PE.Controllers.Main.errorInconsistentExtDocx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan dokumen teks (mis. docx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "PE.Controllers.Main.errorInconsistentExtPdf": "<PERSON><PERSON><PERSON> kesalahan terjadi ketika membuka file.<br>Isi file berhubungan dengan satu dari format berikut: pdf/djvu/xps/oxps, tapi file memiliki ekstensi yang tidak konsisten: %1.", "PE.Controllers.Main.errorInconsistentExtPptx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan presentasi (mis. pptx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "PE.Controllers.Main.errorInconsistentExtXlsx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan spreadsheet (mis. xlsx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "PE.Controllers.Main.errorKeyEncrypt": "Deskriptor kunci tidak dikenal", "PE.Controllers.Main.errorKeyExpire": "Deskriptor kunci tidak berfungsi", "PE.Controllers.Main.errorLoadingFont": "Font tidak bisa dimuat.<br><PERSON><PERSON><PERSON> kontak admin Server <PERSON><PERSON><PERSON>.", "PE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON>.", "PE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "PE.Controllers.Main.errorServerVersion": "Versi editor sudah di update. <PERSON>aman akan dimuat ulang untuk menerapkan perubahan.", "PE.Controllers.Main.errorSessionAbsolute": "W<PERSON><PERSON> edit dokumen sudah selesai. Silakan muat ulang halaman.", "PE.Controllers.Main.errorSessionIdle": "Dokumen sudah lama tidak diedit. <PERSON><PERSON>an muat ulang halaman.", "PE.Controllers.Main.errorSessionToken": "Koneksi ke server terganggu. Silakan muat ulang halaman.", "PE.Controllers.Main.errorSetPassword": "Password tidak bisa diatur.", "PE.Controllers.Main.errorStockChart": "Urutan baris salah. Untuk membuat diagram garis, masukkan data pada lembar kerja dengan urutan berikut ini:<br> harga pembu<PERSON><PERSON>, harga maks<PERSON><PERSON>, harga <PERSON>, harga penutupan.", "PE.Controllers.Main.errorToken": "Token keamanan dokumen tidak dibentuk dengan tepat.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>.", "PE.Controllers.Main.errorTokenExpire": "Token keamanan dokumen sudah kadaluwarsa.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>.", "PE.Controllers.Main.errorUpdateVersion": "Versi file telah diubah. Halaman tidak akan dimuat ulang.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Koneksi internet sudah kembali dan versi file sudah diganti.<br>Sebelum Anda bisa melanjutkan kerja, Anda perlu mengunduh file atau salin konten untuk memastikan tidak ada yang hilang, lalu muat ulang halaman ini.", "PE.Controllers.Main.errorUserDrop": "File tidak bisa diakses sekarang.", "PE.Controllers.Main.errorUsersExceed": "<PERSON><PERSON><PERSON> pengguna telah melebihi jumlah yang diijinkan dalam paket harga.", "PE.Controllers.Main.errorViewerDisconnect": "Koneksi terputus. <PERSON>a tetap bisa melihat dokumen,<br>tapi tidak bisa download atau print sampai koneksi terhubung dan halaman dimuat ulang.", "PE.Controllers.Main.leavePageText": "Anda memiliki perubahan yang belum tersimpan di presentasi ini. <PERSON>lik \"Tetap di Halaman Ini” kemudian \"Simpan\" untuk menyimpan perubahan tersebut. Klik \"Tinggalkan Halaman Ini\" untuk membatalkan semua perubahan yang belum disimpan.", "PE.Controllers.Main.leavePageTextOnClose": "<PERSON><PERSON><PERSON> perubahan yang belum disimpan dalam presentasi ini akan hilang.<br> <PERSON><PERSON> \"<PERSON><PERSON>\" lalu \"<PERSON><PERSON><PERSON>\" untuk menyimpan. <PERSON><PERSON> \"OK\" untuk membuang semua perubahan yang tidak tersimpan.", "PE.Controllers.Main.loadFontsTextText": "Memuat data...", "PE.Controllers.Main.loadFontsTitleText": "Memuat Data", "PE.Controllers.Main.loadFontTextText": "Memuat data...", "PE.Controllers.Main.loadFontTitleText": "Memuat Data", "PE.Controllers.Main.loadImagesTextText": "Memuat gambar...", "PE.Controllers.Main.loadImagesTitleText": "Memuat Gambar", "PE.Controllers.Main.loadImageTextText": "Memuat gambar...", "PE.Controllers.Main.loadImageTitleText": "Memuat Gambar", "PE.Controllers.Main.loadingDocumentTextText": "<PERSON><PERSON><PERSON>...", "PE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadThemeTextText": "Loading Tema...", "PE.Controllers.Main.loadThemeTitleText": "Loading Tema", "PE.Controllers.Main.notcriticalErrorTitle": "Peringatan", "PE.Controllers.Main.openErrorText": "Eror ketika membuka file.", "PE.Controllers.Main.openTextText": "<PERSON><PERSON><PERSON> presentasi...", "PE.Controllers.Main.openTitleText": "<PERSON><PERSON><PERSON> presentasi", "PE.Controllers.Main.printTextText": "Mencetak presentasi...", "PE.Controllers.Main.printTitleText": "Mencetak presentasi", "PE.Controllers.Main.reloadButtonText": "Muat <PERSON>", "PE.Controllers.Main.requestEditFailedMessageText": "Seseorang sedang mengedit presentasi sekarang Silakan coba beberapa saat lagi.", "PE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.saveErrorText": "<PERSON><PERSON>r ketika menyi<PERSON> file.", "PE.Controllers.Main.saveErrorTextDesktop": "File tidak bisa disimpan atau dibuat.<br><PERSON><PERSON><PERSON> yang mungkin adalah: <br>1. File hanya bisa dibaca. <br>2. <PERSON> sedang diedit user lain. <br>3. <PERSON><PERSON><PERSON> penuh atau terkorupsi.", "PE.Controllers.Main.saveTextText": "Menyimpan presentasi...", "PE.Controllers.Main.saveTitleText": "Menyimpan presentasi", "PE.Controllers.Main.scriptLoadError": "Koneksi terlalu lambat dan beberapa komponen tidak bisa dibuka Si<PERSON>an muat ulang halaman.", "PE.Controllers.Main.splitDividerErrorText": "Jumlah baris harus merupakan pembagi %1.", "PE.Controllers.Main.splitMaxColsErrorText": "Ju<PERSON>lah kolom harus kurang dari %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "Jumlah haris harus kurang dari %1.", "PE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textApplyAll": "Terapkan untuk semua persa<PERSON>an", "PE.Controllers.Main.textBuyNow": "Kunjungi website", "PE.Controllers.Main.textChangesSaved": "<PERSON><PERSON><PERSON> te<PERSON>", "PE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textCloseTip": "Klik untuk menutup tips", "PE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "PE.Controllers.Main.textContactUs": "Hubungi sales", "PE.Controllers.Main.textContinue": "Lanjutkan", "PE.Controllers.Main.textConvertEquation": "Persamaan ini dibuat dengan editor persa<PERSON><PERSON> versi lama yang sudah tidak didukung. Untuk edit, konversikan persamaan ke format Office Math ML.<br><PERSON>n<PERSON><PERSON> sekarang?", "PE.Controllers.Main.textCustomLoader": "<PERSON><PERSON> diketahui bahwa berdasarkan syarat dari lisensi, <PERSON><PERSON> tidak bisa untuk mengganti loader.<br><PERSON><PERSON><PERSON> hubungi Departemen Penjualan kami untuk mendapatkan harga.", "PE.Controllers.Main.textDisconnect": "<PERSON><PERSON><PERSON><PERSON> terputus", "PE.Controllers.Main.textGuest": "<PERSON><PERSON>", "PE.Controllers.Main.textHasMacros": "File berisi macros otomatis.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin menja<PERSON>an macros?", "PE.Controllers.Main.textLearnMore": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textLongName": "<PERSON><PERSON><PERSON><PERSON> nama maksimum 128 karakter.", "PE.Controllers.Main.textNoLicenseTitle": "Batas lisensi sudah tercapai", "PE.Controllers.Main.textObject": "Object", "PE.Controllers.Main.textPaidFeature": "<PERSON><PERSON>", "PE.Controllers.Main.textReconnect": "Koneksi terhubung kembali", "PE.Controllers.Main.textRemember": "Ingat pilihan saya untuk semua file", "PE.Controllers.Main.textRememberMacros": "Ingat pilihan saya untuk semua makro", "PE.Controllers.Main.textRenameError": "Nama user tidak boleh kosong.", "PE.Controllers.Main.textRenameLabel": "Ma<PERSON>kkan nama untuk digunakan di kolaborasi", "PE.Controllers.Main.textRequestMacros": "Sebuah makro melakukan permintaan ke URL. Apakah Anda akan mengizinkan permintaan ini ke %1?", "PE.Controllers.Main.textShape": "Bentuk", "PE.Controllers.Main.textStrict": "Mode strict", "PE.Controllers.Main.textText": "Teks", "PE.Controllers.Main.textTryQuickPrint": "Anda telah memilih Cetak cepat: seluruh dokumen akan dicetak pada printer yang terakhir dipilih atau baku.<br><PERSON><PERSON><PERSON><PERSON> Anda hendak melanjutkan?", "PE.Controllers.Main.textTryUndoRedo": "Fungsi Undo/Redo dinonaktifkan untuk mode Co-editing Cepat.<br>Klik tombol 'Mode strict' untuk mengganti ke Mode Strict Co-editing untuk edit file tanpa gangguan dari user lain dan kirim perubahan Anda hanya setelah Anda menyimpannya. Anda bisa mengganti mode co-editing menggunakan editor di pengaturan lanjut.", "PE.Controllers.Main.textTryUndoRedoWarn": "Fungsi Undo/Redo dinonaktifkan untuk mode Co-editing Cepat.", "PE.Controllers.Main.textUndo": "Batalkan", "PE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "PE.Controllers.Main.textUpdating": "Updating", "PE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON><PERSON> ka<PERSON>", "PE.Controllers.Main.titleLicenseNotActive": "Lisensi tidak aktif", "PE.Controllers.Main.titleServerVersion": "Editor mengu<PERSON><PERSON>", "PE.Controllers.Main.titleUpdateVersion": "Version changed", "PE.Controllers.Main.txtAddFirstSlide": "Klik untuk tambah slide pertama", "PE.Controllers.Main.txtAddNotes": "Klik untuk tambah catatan", "PE.Controllers.Main.txtAnimationPane": "Animation Pane", "PE.Controllers.Main.txtArt": "Teks Anda di sini", "PE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON> das<PERSON>", "PE.Controllers.Main.txtButtons": "Tombol", "PE.Controllers.Main.txtCallouts": "Balon Kata", "PE.Controllers.Main.txtCharts": "Bagan", "PE.Controllers.Main.txtClipArt": "<PERSON><PERSON>", "PE.Controllers.Main.txtDateTime": "<PERSON><PERSON> dan <PERSON>", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtEditingMode": "Mengatur mode editing...", "PE.Controllers.Main.txtEnd": "End: ${0}s", "PE.Controllers.Main.txtErrorLoadHistory": "Memuat riwayat gagal", "PE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON>", "PE.Controllers.Main.txtFirstSlide": "First slide", "PE.Controllers.Main.txtFooter": "Footer", "PE.Controllers.Main.txtHeader": "Header", "PE.Controllers.Main.txtImage": "Gambar", "PE.Controllers.Main.txtLastSlide": "Last slide", "PE.Controllers.Main.txtLines": "<PERSON><PERSON>", "PE.Controllers.Main.txtLoading": "Memuat...", "PE.Controllers.Main.txtLoop": "Loop: ${0}s", "PE.Controllers.Main.txtMath": "Matematika", "PE.Controllers.Main.txtMedia": "Media", "PE.Controllers.Main.txtNeedSynchronize": "<PERSON> pembaruan", "PE.Controllers.Main.txtNextSlide": "Next slide", "PE.Controllers.Main.txtNone": "Tidak ada", "PE.Controllers.Main.txtPicture": "Gambar", "PE.Controllers.Main.txtPlayAll": "Play All", "PE.Controllers.Main.txtPlayFrom": "Play From", "PE.Controllers.Main.txtPlaySelected": "Play Selected", "PE.Controllers.Main.txtPrevSlide": "Previous slide", "PE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "PE.Controllers.Main.txtScheme_Aspect": "Aspect", "PE.Controllers.Main.txtScheme_Blue": "Blue", "PE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "PE.Controllers.Main.txtScheme_Blue_II": "Blue II", "PE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "PE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "PE.Controllers.Main.txtScheme_Green": "Green", "PE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "PE.Controllers.Main.txtScheme_Marquee": "Marquee", "PE.Controllers.Main.txtScheme_Median": "Median", "PE.Controllers.Main.txtScheme_Office": "Office", "PE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "PE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "PE.Controllers.Main.txtScheme_Orange": "Orange", "PE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "PE.Controllers.Main.txtScheme_Paper": "Paper", "PE.Controllers.Main.txtScheme_Red": "Red", "PE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "PE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "PE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "PE.Controllers.Main.txtScheme_Violet": "Violet", "PE.Controllers.Main.txtScheme_Violet_II": "Violet II", "PE.Controllers.Main.txtScheme_Yellow": "Yellow", "PE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "PE.Controllers.Main.txtSeries": "Seri", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Garis Callout 1 (<PERSON> dan <PERSON>)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Garis Callout 2 (<PERSON> dan <PERSON>)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Garis Callout 3 (<PERSON> dan <PERSON>)", "PE.Controllers.Main.txtShape_accentCallout1": "Garis Callout 1 (Accent Bar)", "PE.Controllers.Main.txtShape_accentCallout2": "Garis Callout 2 (<PERSON><PERSON>ent Bar)", "PE.Controllers.Main.txtShape_accentCallout3": "Garis Callout 3 (<PERSON><PERSON>ent Bar)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Tombol Kembali atau Sebelumnya", "PE.Controllers.Main.txtShape_actionButtonBeginning": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonDocument": "Tombol Dokumen", "PE.Controllers.Main.txtShape_actionButtonEnd": "Tombol Akhir", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON><PERSON> Selanjutnya", "PE.Controllers.Main.txtShape_actionButtonHelp": "Tombol <PERSON>", "PE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonInformation": "Tombol Informasi", "PE.Controllers.Main.txtShape_actionButtonMovie": "Tombol Movie", "PE.Controllers.Main.txtShape_actionButtonReturn": "Tombol Kembali", "PE.Controllers.Main.txtShape_actionButtonSound": "Tombol Suara", "PE.Controllers.Main.txtShape_arc": "Arc", "PE.Controllers.Main.txtShape_bentArrow": "Panah <PERSON>", "PE.Controllers.Main.txtShape_bentConnector5": "Konektor Siku", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Panah Konektor Siku", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Panah Ganda Konektor Siku", "PE.Controllers.Main.txtShape_bentUpArrow": "Panah Kelok Atas", "PE.Controllers.Main.txtShape_bevel": "Miring", "PE.Controllers.Main.txtShape_blockArc": "Block Arc", "PE.Controllers.Main.txtShape_borderCallout1": "Garis Callout 1", "PE.Controllers.Main.txtShape_borderCallout2": "<PERSON><PERSON> Callout 2", "PE.Controllers.Main.txtShape_borderCallout3": "<PERSON><PERSON> Callout 3", "PE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_callout1": "Garis Callout 1 (No Border)", "PE.Controllers.Main.txtShape_callout2": "<PERSON><PERSON>out 2 (No <PERSON>)", "PE.Controllers.Main.txtShape_callout3": "<PERSON><PERSON> Callout 3 (No <PERSON>)", "PE.Controllers.Main.txtShape_can": "Bisa", "PE.Controllers.Main.txtShape_chevron": "Chevron", "PE.Controllers.Main.txtShape_chord": "Chord", "PE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_cloud": "Cloud", "PE.Controllers.Main.txtShape_cloudCallout": "Cloud Callout", "PE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_cube": "Ku<PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Konektor <PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Panah Konektor <PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Panah Ganda Konektor <PERSON>", "PE.Controllers.Main.txtShape_curvedDownArrow": "Panah Kelok Bawah", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Panah Kelok Kiri", "PE.Controllers.Main.txtShape_curvedRightArrow": "Panah Kelok <PERSON>", "PE.Controllers.Main.txtShape_curvedUpArrow": "Panah Kelok Atas", "PE.Controllers.Main.txtShape_decagon": "Decagon", "PE.Controllers.Main.txtShape_diagStripe": "Strip Diagonal", "PE.Controllers.Main.txtShape_diamond": "Diamond", "PE.Controllers.Main.txtShape_dodecagon": "Dodecagon", "PE.Controllers.Main.txtShape_donut": "Donut", "PE.Controllers.Main.txtShape_doubleWave": "Gelombang Ganda", "PE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_downArrowCallout": "Seranta Panah Bawah", "PE.Controllers.Main.txtShape_ellipse": "Elips", "PE.Controllers.Main.txtShape_ellipseRibbon": "Pita Kelok Bawah", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Pita Kelok Atas", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Diagram Alir: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartCollate": "Diag<PERSON> Alir: Collate", "PE.Controllers.Main.txtShape_flowChartConnector": "Diagram Alir: <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDecision": "Diagram Alir: <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDelay": "Diagram Alir: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDisplay": "Diagram Alir: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDocument": "Diagram Alir: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartExtract": "Diagram Alir: Ekstrak", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Diagram Alir: Data", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Diagram Alir: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Diagram Alir: Magnetic Disk", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Diagram Alir: Direct Access Storage", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Diagram Alir: Sequential Access Storage", "PE.Controllers.Main.txtShape_flowChartManualInput": "Diagram Alir: Input Manual", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Diagram Alir: Operasi Manual", "PE.Controllers.Main.txtShape_flowChartMerge": "Diagram Alir: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Diagram Alir: Multidokumen ", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Diagram Alir: Off-page Penghubung", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Diagram Alir: Stored Data", "PE.Controllers.Main.txtShape_flowChartOr": "Diagram Alir: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Diagram Alir: Predefined Process", "PE.Controllers.Main.txtShape_flowChartPreparation": "Diagram Alir: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartProcess": "Diagram Alir: Proses", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Diagram Alir: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Diagram Alir: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartSort": "Diagram Alir: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Diagram Alir: Summing Junction", "PE.Controllers.Main.txtShape_flowChartTerminator": "Diagram Alir: Terminator", "PE.Controllers.Main.txtShape_foldedCorner": "Sudut Folder", "PE.Controllers.Main.txtShape_frame": "Kerang<PERSON>", "PE.Controllers.Main.txtShape_halfFrame": "Setengah Bingkai", "PE.Controllers.Main.txtShape_heart": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_heptagon": "Heptagon", "PE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_homePlate": "Pentagon", "PE.Controllers.Main.txtShape_horizontalScroll": "<PERSON>roll <PERSON>tal", "PE.Controllers.Main.txtShape_irregularSeal1": "Ledakan 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Ledakan 2", "PE.Controllers.Main.txtShape_leftArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftArrowCallout": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftRightArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftRightUpArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftUpArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lightningBolt": "Petir", "PE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "Panah", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathDivide": "Divisi", "PE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMinus": "Minus", "PE.Controllers.Main.txtShape_mathMultiply": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathNotEqual": "Tidak Sama", "PE.Controllers.Main.txtShape_mathPlus": "Plus", "PE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_noSmoking": "Simbol \"Tidak\"", "PE.Controllers.Main.txtShape_notchedRightArrow": "Panah Takik <PERSON>", "PE.Controllers.Main.txtShape_octagon": "Oktagon", "PE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "PE.Controllers.Main.txtShape_pentagon": "Pentagon", "PE.Controllers.Main.txtShape_pie": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_plaque": "Plus", "PE.Controllers.Main.txtShape_plus": "Plus", "PE.Controllers.Main.txtShape_polyline1": "Scribble", "PE.Controllers.Main.txtShape_polyline2": "<PERSON><PERSON><PERSON> be<PERSON>", "PE.Controllers.Main.txtShape_quadArrow": "Panah Empat Mata", "PE.Controllers.Main.txtShape_quadArrowCallout": "Seranta Panah Empat Mata", "PE.Controllers.Main.txtShape_rect": "Kotak", "PE.Controllers.Main.txtShape_ribbon": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_ribbon2": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightArrow": "Tanda Panah ke Kanan", "PE.Controllers.Main.txtShape_rightArrowCallout": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_round1Rect": "<PERSON>segi <PERSON> Sudut Lengkung Single", "PE.Controllers.Main.txtShape_round2DiagRect": "<PERSON><PERSON><PERSON> Sudut Lengkung Diagonal", "PE.Controllers.Main.txtShape_round2SameRect": "<PERSON><PERSON><PERSON> Sudut Lengkung Sama Sisi", "PE.Controllers.Main.txtShape_roundRect": "<PERSON><PERSON><PERSON> Sudu<PERSON>", "PE.Controllers.Main.txtShape_rtTriangle": "Segitiga Siku-Siku", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_snip1Rect": "Snip Persegi <PERSON> Sudut Single", "PE.Controllers.Main.txtShape_snip2DiagRect": "Snip Persegi Panjang Sudut Lengkung Diagonal", "PE.Controllers.Main.txtShape_snip2SameRect": "Snip Persegi Panjang Sudut Lengkung Sama Sisi", "PE.Controllers.Main.txtShape_snipRoundRect": "<PERSON><PERSON><PERSON> dan <PERSON> Sudut Lengkung Single", "PE.Controllers.Main.txtShape_spline": "<PERSON>g<PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "Bintang Titik-10", "PE.Controllers.Main.txtShape_star12": "Bintang Titik-12", "PE.Controllers.Main.txtShape_star16": "Bintang Titik-16", "PE.Controllers.Main.txtShape_star24": "Bintang Titik-24", "PE.Controllers.Main.txtShape_star32": "Bintang Titik-32", "PE.Controllers.Main.txtShape_star4": "Bintang Titik-4", "PE.Controllers.Main.txtShape_star5": "Bintang Titik-5", "PE.Controllers.Main.txtShape_star6": "Bintang Titik-6", "PE.Controllers.Main.txtShape_star7": "Bintang Titik-7", "PE.Controllers.Main.txtShape_star8": "Bintang Titik-8", "PE.Controllers.Main.txtShape_stripedRightArrow": "<PERSON><PERSON>-<PERSON><PERSON>", "PE.Controllers.Main.txtShape_sun": "Matahari", "PE.Controllers.Main.txtShape_teardrop": "Teardrop", "PE.Controllers.Main.txtShape_textRect": "Kotak teks", "PE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "PE.Controllers.Main.txtShape_triangle": "Segitiga", "PE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON> k<PERSON>", "PE.Controllers.Main.txtShape_upArrowCallout": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_upDownArrow": "Panah Atas Bawah", "PE.Controllers.Main.txtShape_uturnArrow": "Panah Balik", "PE.Controllers.Main.txtShape_verticalScroll": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_wave": "Gelombang", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Callout Oval", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Callout <PERSON>", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Callout <PERSON><PERSON><PERSON> Sudut <PERSON>", "PE.Controllers.Main.txtSldLtTBlank": "Kosong", "PE.Controllers.Main.txtSldLtTChart": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTChartAndTx": "<PERSON><PERSON>n <PERSON>", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Clip <PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Clip <PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTCust": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTDgm": "Diagram", "PE.Controllers.Main.txtSldLtTFourObj": "Empat Objek", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Media dan Teks", "PE.Controllers.Main.txtSldLtTObj": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON> Objek", "PE.Controllers.Main.txtSldLtTObjAndTx": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTObjOnly": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTObjOverTx": "Objek Diatas Teks", "PE.Controllers.Main.txtSldLtTObjTx": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON>", "PE.Controllers.Main.txtSldLtTPicTx": "Gambar dan <PERSON>ion", "PE.Controllers.Main.txtSldLtTSecHead": "Header <PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTwoColTx": "Dua Teks Kolo<PERSON>", "PE.Controllers.Main.txtSldLtTTwoObj": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "<PERSON><PERSON> O<PERSON>s dan <PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "<PERSON>a Objek dan <PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Dua Objek diatas Teks", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Dua Teks dan <PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTx": "Teks", "PE.Controllers.Main.txtSldLtTTxAndChart": "<PERSON><PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "<PERSON><PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Te<PERSON> dan Media", "PE.Controllers.Main.txtSldLtTTxAndObj": "<PERSON><PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "<PERSON>ks dan <PERSON>", "PE.Controllers.Main.txtSldLtTTxOverObj": "Teks Di Atas Objek", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "<PERSON><PERSON><PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "<PERSON><PERSON><PERSON> dan <PERSON> di Atas Grafik", "PE.Controllers.Main.txtSldLtTVertTx": "Teks Vertikal", "PE.Controllers.Main.txtSlideNumber": "Nomor slide", "PE.Controllers.Main.txtSlideSubtitle": "Subtitle Slide", "PE.Controllers.Main.txtSlideText": "Teks Slide", "PE.Controllers.Main.txtSlideTitle": "Ju<PERSON>l Slide", "PE.Controllers.Main.txtStarsRibbons": "Bintang & Pita", "PE.Controllers.Main.txtStart": "Start: ${0}s", "PE.Controllers.Main.txtStop": "Stop", "PE.Controllers.Main.txtTheme_basic": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_blank": "Kosong", "PE.Controllers.Main.txtTheme_classic": "Klasik", "PE.Controllers.Main.txtTheme_corner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_dotted": "Titik-Titik", "PE.Controllers.Main.txtTheme_green": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green_leaf": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_lines": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "Office", "PE.Controllers.Main.txtTheme_office_theme": "Tema Office", "PE.Controllers.Main.txtTheme_official": "Official", "PE.Controllers.Main.txtTheme_pixel": "Pixel", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Turtle", "PE.Controllers.Main.txtXAxis": "Sumbu X", "PE.Controllers.Main.txtYAxis": "Sumbu Y", "PE.Controllers.Main.txtZoom": "Zoom", "PE.Controllers.Main.unknownErrorText": "Kesalahan tidak diketahui.", "PE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON> kamu tidak didukung.", "PE.Controllers.Main.uploadImageExtMessage": "Format gambar tidak dikenal.", "PE.Controllers.Main.uploadImageFileCountMessage": "Tidak ada gambar yang di<PERSON>h.", "PE.Controllers.Main.uploadImageSizeMessage": "Melebihi ukuran maksimal file. Ukuran maksimum adalah 25 MB.", "PE.Controllers.Main.uploadImageTextText": "Mengunggah gambar...", "PE.Controllers.Main.uploadImageTitleText": "Mengunggah Gambar", "PE.Controllers.Main.waitText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.warnBrowserIE9": "Aplikasi ini tidak berjalan dengan baik di IE9. Gunakan IE10 atau versi yang terbaru.", "PE.Controllers.Main.warnBrowserZoom": "Pengaturan pembesaran tampilan pada browser <PERSON><PERSON> saat ini tidak didukung sepenuhnya. <PERSON><PERSON>an atur ulang ke tampilan standar dengan menekan Ctrl+0.", "PE.Controllers.Main.warnLicenseAnonymous": "<PERSON><PERSON><PERSON> di<PERSON>lak untuk pengguna anonim.<br>Do<PERSON><PERSON> ini akan dibuka hanya untuk dilihat.", "PE.Controllers.Main.warnLicenseBefore": "Lisensi tidak aktif.<br>Harap hubungi administrator <PERSON><PERSON>.", "PE.Controllers.Main.warnLicenseExceeded": "Anda sudah mencapai batas untuk koneksi bersa<PERSON> ke %1 editor. Dokumen ini akan dibuka untuk dilihat saja.<br>Hubungi admin Anda untuk mempelajari lebih lanjut.", "PE.Controllers.Main.warnLicenseExp": "Lisensi Anda sudah kadal<PERSON>.<br><PERSON><PERSON><PERSON> update lisensi Anda dan muat ulang halaman.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "<PERSON><PERSON><PERSON> ka<PERSON>.<br><PERSON><PERSON> tidak memiliki akses untuk editing dokumen secara keseluruhan.<br><PERSON><PERSON><PERSON> hubungi admin Anda.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Lisensi perlu diperbaharui.<br>Anda memiliki akses terbatas untuk edit dokumen.<br><PERSON><PERSON><PERSON> hubungi admin Anda untuk mendapatkan akses penuh", "PE.Controllers.Main.warnLicenseUsersExceeded": "<PERSON><PERSON> sudah mencapai batas user untuk %1 editor. Hubungi admin Anda untuk mempelajari lebih lanjut.", "PE.Controllers.Main.warnNoLicense": "<PERSON>a sudah mencapai batas untuk koneksi bersa<PERSON>an ke %1 editor. Dokumen ini akan dibuka untuk dilihat saja.<br>Hubungi %1 tim sales untuk syarat personal upgrade.", "PE.Controllers.Main.warnNoLicenseUsers": "Anda sudah mencapai batas user untuk %1 editor. Hubungi %1 tim sales untuk syarat personal upgrade.", "PE.Controllers.Main.warnProcessRightsChange": "Hak Anda untuk mengedit file ditolak.", "PE.Controllers.Print.txtPrintRangeInvalid": "Rentang cetak tidak valid", "PE.Controllers.Print.txtPrintRangeSingleRange": "Masukna satu nomor slide atau satu rentang slide (misalnya, 5-12). Atau Anda bisa Cetak ke PDF.", "PE.Controllers.Search.notcriticalErrorTitle": "Peringatan", "PE.Controllers.Search.textNoTextFound": "Data yang Anda cari tidak ditemukan. <PERSON>lakan atur opsi pencarian <PERSON>a.", "PE.Controllers.Search.textReplaceSkipped": "Penggantian telah dilakukan. {0} kemunculan telah dilewatkan.", "PE.Controllers.Search.textReplaceSuccess": "Pencarian telah dilakukan. {0} kemunculan telah diganti", "PE.Controllers.Search.warnReplaceString": "{0} bukan karakter khusus yang valid untuk kotak Ganti Dengan.", "PE.Controllers.Statusbar.textDisconnect": "<b><PERSON><PERSON><PERSON><PERSON> terput<PERSON></b><br><PERSON><PERSON><PERSON>gh<PERSON>. Silakan periksa pengaturan koneksi.", "PE.Controllers.Statusbar.zoomText": "Perbesar {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "Font yang akan Anda simpan tidak tersedia di perangkat sekarang.<br>Style teks akan ditampilkan menggunakan salah satu font sistem, font yang disimpan akan digunakan jika sudah tersedia.<br><PERSON><PERSON><PERSON><PERSON> Anda ingin melanjutkan?", "PE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "PE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "PE.Controllers.Toolbar.helpTabDesign": "Apply themes, change color schemes and slide size from the newly added Design tab.", "PE.Controllers.Toolbar.helpTabDesignHeader": "Design tab", "PE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textBracket": "<PERSON><PERSON>", "PE.Controllers.Toolbar.textFontSizeErr": "Input yang dimasukkan salah.<br><PERSON><PERSON><PERSON> ma<PERSON>kkan input numerik antara 1 dan 300", "PE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textInsert": "<PERSON>sip<PERSON>", "PE.Controllers.Toolbar.textIntegral": "Integral", "PE.Controllers.Toolbar.textLargeOperator": "Operator <PERSON>", "PE.Controllers.Toolbar.textLimitAndLog": "<PERSON>it dan <PERSON>", "PE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textOperator": "Operator", "PE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "Simbol", "PE.Controllers.Toolbar.textWarning": "Peringatan", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON>-<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowL": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON>da Panah ke Kanan <PERSON>as", "PE.Controllers.Toolbar.txtAccent_Bar": "Palang", "PE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "PE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON> (dengan placeholder)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Kotak Formula(Contoh)", "PE.Controllers.Toolbar.txtAccent_Check": "Periksa", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "<PERSON><PERSON> At<PERSON>", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC Dengan Overbar", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y dengan overbar", "PE.Controllers.Toolbar.txtAccent_DDDot": "Triple Dot", "PE.Controllers.Toolbar.txtAccent_DDot": "Titik Dua", "PE.Controllers.Toolbar.txtAccent_Dot": "Titik", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Overbar Ganda", "PE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Pengelompokan Karakter Di Bawah", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Pengelompokan Karakter Di Atas", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Panah Tiga Kiri Atas", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON> kanan di bawah", "PE.Controllers.Toolbar.txtAccent_Hat": "<PERSON>i", "PE.Controllers.Toolbar.txtAccent_Smile": "Prosodi", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "<PERSON>da kurung dengan pemisah", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "<PERSON>da kurung dengan pemisah", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON> k<PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "<PERSON>da kurung dengan pemisah", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON> kurawal tutup", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON> kurawal buka", "PE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON> (<PERSON><PERSON>)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Kasus (Tiga Kondisi)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Tumpuk objek", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Tumpuk objek", "PE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Koefisien binomial", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Koefisien binomial", "PE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON> vertikal", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON><PERSON> vertikal kanan", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "<PERSON>ilah vertikal kiri", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON><PERSON> vertikal ganda kanan", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "<PERSON>da kurung dengan pemisah", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON> tutup", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON> buka", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON>rung siku", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Kurung siku kanan", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON>rung siku buka", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON><PERSON> miring", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Diferensial", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Diferensial", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Diferensial", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Diferensial", "PE.Controllers.Toolbar.txtFractionHorizontal": "Pecahan Linear", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi Dibagi 2", "PE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON>han kecil", "PE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Fungsi Kosin Hiperbolik Terbalik", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Fungsi Kotangen Terbalik", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Fungsi Kotangen Hiperbolik Terbalik", "PE.Controllers.Toolbar.txtFunction_1_Csc": "<PERSON><PERSON><PERSON> Ko<PERSON>s <PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Fungsi Kosekan Hiperbolik Terbalik", "PE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Fungsi Sekans Hiperbolik Terbalik", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Fungsi Sin Terbalik", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Fungsi Sin Hiperbolik Terbalik", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Fungsi Tangen Terbalik", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Fungsi Tangen Hiperbolik Terbalik", "PE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Cosh": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Coth": "Fungsi Kotangen Hiperbolik", "PE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Csch": "Fungsi Kosekans Hiperbolik", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Tangent formula", "PE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON> sekan", "PE.Controllers.Toolbar.txtFunction_Sech": "Fungsi Sekans Hiperbolik", "PE.Controllers.Toolbar.txtFunction_Sin": "Fungsi sinus", "PE.Controllers.Toolbar.txtFunction_Sinh": "Fungsi Sin Hiperbolik", "PE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON>i tangen", "PE.Controllers.Toolbar.txtFunction_Tanh": "Fungsi Tangen Hiperbolik", "PE.Controllers.Toolbar.txtIntegral": "Integral", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Theta Diferensial", "PE.Controllers.Toolbar.txtIntegral_dx": "Diferensial x", "PE.Controllers.Toolbar.txtIntegral_dy": "Diferensial y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "PE.Controllers.Toolbar.txtIntegralDouble": "Integral Ganda", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Integral Ganda", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Integral Ganda", "PE.Controllers.Toolbar.txtIntegralOriented": "Integral <PERSON>ur", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integral <PERSON>ur", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON><PERSON> integral", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON> integral", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON><PERSON> integral", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integral <PERSON>ur", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume integral", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volume integral", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral volume dengan batas", "PE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "PE.Controllers.Toolbar.txtIntegralTriple": "Triple integral", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple integral", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple integral", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ko-Produk", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Ko-Produk", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Ko-Produk", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Ko-Produk", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Ko-Produk", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produk", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Contoh union", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Produk", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produk", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produk", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produk", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produk", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Penju<PERSON>lahan dengan batas subskrip/superskrip", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union dengan batas subskrip/superskrip", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritma Natural", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logarit<PERSON>", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarit<PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "PE.Controllers.Toolbar.txtMatrix_1_2": "matriks kosong 1x2", "PE.Controllers.Toolbar.txtMatrix_1_3": "matriks kosong 1x3", "PE.Controllers.Toolbar.txtMatrix_2_1": "matriks kosong 2x1", "PE.Controllers.Toolbar.txtMatrix_2_2": "matriks kosong 2x2", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON><PERSON>gan <PERSON>", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON><PERSON>gan <PERSON>", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON><PERSON>gan <PERSON>", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON>gan <PERSON>", "PE.Controllers.Toolbar.txtMatrix_2_3": "matriks kosong 2x3", "PE.Controllers.Toolbar.txtMatrix_3_1": "matriks kosong 3x1", "PE.Controllers.Toolbar.txtMatrix_3_2": "matriks kosong 3x2", "PE.Controllers.Toolbar.txtMatrix_3_3": "matriks kosong 3x3", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON> garis dasar", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Titik Tengah", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Titik Diagonal", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Titik Vertikal", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "Sparse Matrix", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "Sparse Matrix", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "matriks identitas 2x2 dengan nol", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matriks identitas 2x2 dengan sel diagonal kosong", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "matriks identitas 3x3 dengan nol", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriks identitas 3x3 dengan sel diagonal kosong", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON>da Panah <PERSON>-<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON>-<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Panah <PERSON>", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON> kanan di bawah", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON>da Panah ke Kanan <PERSON>as", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Titik Dua", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Yields", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Hasil <PERSON>", "PE.Controllers.Toolbar.txtOperator_Definition": "Setara Menurut De<PERSON>isi", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta Setara Dengan", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON>da Panah <PERSON>-<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON>-<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Panah <PERSON>", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON> kanan di bawah", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON>da Panah ke Kanan <PERSON>as", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Setara Setara", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Sama <PERSON>", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON> dengan pangkat", "PE.Controllers.Toolbar.txtRadicalRoot_3": "Akar Pangkat Tiga", "PE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON> pangkat dua", "PE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "x kuadrat", "PE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "Subskrip", "PE.Controllers.Toolbar.txtScriptSubSup": "Subskrip-SuperskripKiri", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Subskrip-SuperskripKiri", "PE.Controllers.Toolbar.txtScriptSup": "Superskrip", "PE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_additional": "Komplemen", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_ast": "Operator tanda bintang", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_bullet": "Operator butir", "PE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cbrt": "Akar Pangkat Tiga", "PE.Controllers.Toolbar.txtSymbol_cdots": "Elipsis <PERSON> Horisontal", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cup": "Union", "PE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Tanda Pembagi", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Panah Bawa<PERSON>", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Himpunan Kosong", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_equiv": "Identik Dengan", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "<PERSON> di sana", "PE.Controllers.Toolbar.txtSymbol_factorial": "Faktorial", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Derajat Fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "Untuk Semua", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON> atau <PERSON>", "PE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_in": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_infinity": "Tak Terbatas", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_leq": "<PERSON><PERSON> atau <PERSON>", "PE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_minus": "Minus", "PE.Controllers.Toolbar.txtSymbol_mp": "Minus Plus", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "Tidak Sama Den<PERSON>", "PE.Controllers.Toolbar.txtSymbol_ni": "Sertakan sebagai Anggota", "PE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_notexists": "Tidak ada di sana", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omikron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Diferensial Parsial", "PE.Controllers.Toolbar.txtSymbol_percent": "Persentase", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Plus", "PE.Controllers.Toolbar.txtSymbol_pm": "Plus Minus", "PE.Controllers.Toolbar.txtSymbol_propto": "Proposional Dengan", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_qed": "<PERSON>em<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_rddots": "Elipsis diagonal kanan atas", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "Tanda Panah ke Kanan", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON> karena itu", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON> k<PERSON>", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Varian Epsilon", "PE.Controllers.Toolbar.txtSymbol_varphi": "V<PERSON>", "PE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Varian <PERSON>", "PE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON> vertikal", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "Fit Slide", "PE.Controllers.Viewport.textFitWidth": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.str0_5": "0,5 detik (Sangat Cepat)", "PE.Views.Animation.str1": "1 detik (Cepat)", "PE.Views.Animation.str2": "2 detik (Sedang)", "PE.Views.Animation.str20": "20 detik (Amat Sangat Lambat)", "PE.Views.Animation.str3": "3 detik (Lambat)", "PE.Views.Animation.str5": "5 detik (Sangat Lambat)", "PE.Views.Animation.strDelay": "Delay", "PE.Views.Animation.strDuration": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.strRepeat": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.strRewind": "Rewind", "PE.Views.Animation.strStart": "<PERSON><PERSON>", "PE.Views.Animation.strTrigger": "<PERSON><PERSON>", "PE.Views.Animation.textAutoPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textMoreEffects": "<PERSON><PERSON><PERSON><PERSON> Le<PERSON>h Banyak Efek", "PE.Views.Animation.textMoveEarlier": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.textMoveLater": "Pi<PERSON>h <PERSON>", "PE.Views.Animation.textMultiple": "Banyak", "PE.Views.Animation.textNone": "Tidak ada", "PE.Views.Animation.textNoRepeat": "(nihil)", "PE.Views.Animation.textOnClickOf": "Saat Klik dari", "PE.Views.Animation.textOnClickSequence": "Saat Klik Sekuens", "PE.Views.Animation.textStartAfterPrevious": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.textStartOnClick": "Saat Klik", "PE.Views.Animation.textStartWithPrevious": "Dengan <PERSON>", "PE.Views.Animation.textUntilEndOfSlide": "Hingga Akhir Slide", "PE.Views.Animation.textUntilNextClick": "Hingga Klik Selanjutnya", "PE.Views.Animation.txtAddEffect": "Tambah Animasi", "PE.Views.Animation.txtAnimationPane": "Panel Animasi", "PE.Views.Animation.txtParameters": "Parameter", "PE.Views.Animation.txtPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.txtSec": "s", "PE.Views.AnimationDialog.textPreviewEffect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.AnimationDialog.textTitle": "Lebih banyak efek", "PE.Views.ChartSettings.text3dDepth": "<PERSON><PERSON><PERSON> (% dari dasar)", "PE.Views.ChartSettings.text3dHeight": "Tinggi (% dari dasar)", "PE.Views.ChartSettings.text3dRotation": "Rotasi 3D", "PE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "PE.Views.ChartSettings.textAutoscale": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textChartType": "Ubah Tipe Bagan", "PE.Views.ChartSettings.textDefault": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textDown": "<PERSON>wa<PERSON>", "PE.Views.ChartSettings.textEditData": "Edit Data", "PE.Views.ChartSettings.textHeight": "Tingg<PERSON>", "PE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textLeft": "<PERSON><PERSON>", "PE.Views.ChartSettings.textNarrow": "Perkecil bidang tampilan", "PE.Views.ChartSettings.textPerspective": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textRight": "<PERSON><PERSON>", "PE.Views.ChartSettings.textRightAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textSize": "Ukuran", "PE.Views.ChartSettings.textStyle": "Model", "PE.Views.ChartSettings.textUp": "<PERSON><PERSON>", "PE.Views.ChartSettings.textWiden": "Perlebar bidang tampilan", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON>", "PE.Views.ChartSettings.textX": "Rotasi X", "PE.Views.ChartSettings.textY": "R<PERSON><PERSON> Y", "PE.Views.ChartSettingsAdvanced.textAlt": "Teks alternatif", "PE.Views.ChartSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAltTip": "Representasi alternatif berbasis teks dari informasi objek visual, yang akan dibaca kepada orang dengan gangguan penglihatan atau kognitif untuk membantu mereka lebih memahami informasi yang ada dalam gambar, shape, grafik, atau tabel.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textCenter": "Tengah", "PE.Views.ChartSettingsAdvanced.textChartName": "<PERSON><PERSON> bagan", "PE.Views.ChartSettingsAdvanced.textFrom": "<PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textGeneral": "<PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textHeight": "Tingg<PERSON>", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Proporsi konstan", "PE.Views.ChartSettingsAdvanced.textPlacement": "Penempatan", "PE.Views.ChartSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textSize": "Ukuran", "PE.Views.ChartSettingsAdvanced.textTitle": "Bagan - Pengaturan lan<PERSON>", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "Pojok kiri atas", "PE.Views.ChartSettingsAdvanced.textVertical": "Vertikal", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Atur format default untuk {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Atur sesuai default", "PE.Views.DateTimeDialog.textFormat": "Format", "PE.Views.DateTimeDialog.textLang": "Bahasa", "PE.Views.DateTimeDialog.textUpdate": "Update secara otomatis", "PE.Views.DateTimeDialog.txtTitle": "Tanggal & Jam", "PE.Views.DocumentHolder.aboveText": "Di atas", "PE.Views.DocumentHolder.addCommentText": "Tambahkan komentar", "PE.Views.DocumentHolder.advancedChartText": "Pengaturan Lanjutan Bagan", "PE.Views.DocumentHolder.advancedEquationText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.advancedImageText": "Pengaturan Lanjut untuk Gambar", "PE.Views.DocumentHolder.advancedParagraphText": "Pengaturan Lanjut untuk Paragraf", "PE.Views.DocumentHolder.advancedShapeText": "Pengaturan Lanjut untuk Bentuk", "PE.Views.DocumentHolder.advancedTableText": "Pengaturan Lanjut untuk Tabel", "PE.Views.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.allLinearText": "Semua - Linear", "PE.Views.DocumentHolder.allProfText": "Semua - Profesional", "PE.Views.DocumentHolder.belowText": "<PERSON> bawah", "PE.Views.DocumentHolder.cellAlignText": "<PERSON><PERSON> <PERSON><PERSON>", "PE.Views.DocumentHolder.cellText": "<PERSON>l", "PE.Views.DocumentHolder.centerText": "Tengah", "PE.Views.DocumentHolder.columnText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.currLinearText": "Saat Ini - Linear", "PE.Views.DocumentHolder.currProfText": "Saat Ini - Profesional", "PE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.deleteRowText": "Hapus Baris", "PE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.deleteText": "Hapus", "PE.Views.DocumentHolder.direct270Text": "Rotasi Teks Keatas", "PE.Views.DocumentHolder.direct90Text": "Rotasi Teks Kebawah", "PE.Views.DocumentHolder.directHText": "Horisontal", "PE.Views.DocumentHolder.directionText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.editChartText": "Edit Data", "PE.Views.DocumentHolder.editHyperlinkText": "Edit Hyperlink", "PE.Views.DocumentHolder.hideEqToolbar": "Sembunyikan Bilah Alat Per<PERSON>an", "PE.Views.DocumentHolder.hyperlinkText": "Hyperlink", "PE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowAboveText": "Baris di Atas", "PE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowText": "Sisipkan Baris", "PE.Views.DocumentHolder.insertText": "<PERSON>sip<PERSON>", "PE.Views.DocumentHolder.langText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.latexText": "LaTex", "PE.Views.DocumentHolder.leftText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.loadSpellText": "<PERSON><PERSON><PERSON> varian...", "PE.Views.DocumentHolder.mergeCellsText": "Gabungkan Sel", "PE.Views.DocumentHolder.mniCustomTable": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.moreText": "Varian lain...", "PE.Views.DocumentHolder.noSpellVariantsText": "Tidak ada varian", "PE.Views.DocumentHolder.originalSizeText": "Ukuran Sebenarnya", "PE.Views.DocumentHolder.removeHyperlinkText": "Hapus Hyperlink", "PE.Views.DocumentHolder.rightText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.showEqToolbar": "<PERSON><PERSON><PERSON><PERSON> Per<PERSON>", "PE.Views.DocumentHolder.spellcheckText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.splitCellsText": "Pisahkan Sel...", "PE.Views.DocumentHolder.splitCellTitleText": "Pi<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textAddHGuides": "Tambahkan Pemandu Horizontal", "PE.Views.DocumentHolder.textAddVGuides": "Tambahkan Pemandu Vertikal", "PE.Views.DocumentHolder.textArrangeBack": "Jalankan di Background", "PE.Views.DocumentHolder.textArrangeBackward": "Mundurkan", "PE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeFront": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textClearGuides": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textCm": "cm", "PE.Views.DocumentHolder.textCopy": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textCrop": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFit": "Fit", "PE.Views.DocumentHolder.textCustom": "Kustom", "PE.Views.DocumentHolder.textCut": "Potong", "PE.Views.DocumentHolder.textDeleteGuide": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textDeleteLayout": "Delete Layout", "PE.Views.DocumentHolder.textDeleteMaster": "Delete Master", "PE.Views.DocumentHolder.textDistributeCols": "Distribusikan kolom", "PE.Views.DocumentHolder.textDistributeRows": "Distribusikan baris", "PE.Views.DocumentHolder.textDuplicateLayout": "Duplicate Layout", "PE.Views.DocumentHolder.textDuplicateSlideMaster": "Duplicate Slide Master", "PE.Views.DocumentHolder.textEditObject": "Edit object", "PE.Views.DocumentHolder.textEditPoints": "<PERSON>", "PE.Views.DocumentHolder.textFlipH": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textFlipV": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textFromUrl": "Dari URL", "PE.Views.DocumentHolder.textGridlines": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textGuides": "Pemandu", "PE.Views.DocumentHolder.textInsertLayout": "Insert Layout", "PE.Views.DocumentHolder.textInsertSlideMaster": "Insert Slide Master", "PE.Views.DocumentHolder.textNextPage": "Slide Berikutnya", "PE.Views.DocumentHolder.textPaste": "Tempel", "PE.Views.DocumentHolder.textPrevPage": "Slide sebelumnya", "PE.Views.DocumentHolder.textRemove": "Remove", "PE.Views.DocumentHolder.textRenameLayout": "<PERSON><PERSON>out", "PE.Views.DocumentHolder.textRenameMaster": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textReplace": "Ganti Gambar", "PE.Views.DocumentHolder.textResetCrop": "Reset crop", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "Rotasi 90° Berlawanan Jarum Jam", "PE.Views.DocumentHolder.textRotate90": "Rotasi 90° Searah Jarum Jam", "PE.Views.DocumentHolder.textRulers": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textSaveAsPicture": "Simpan sebagai gambar", "PE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignCenter": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Rata <PERSON>", "PE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "PE.Views.DocumentHolder.textShowGridlines": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShowGuides": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textSlideSettings": "Pengaturan slide", "PE.Views.DocumentHolder.textSmartGuides": "Pemandu Pintar", "PE.Views.DocumentHolder.textSnapObjects": "Posisikan O<PERSON> pada <PERSON>", "PE.Views.DocumentHolder.textStartAfterPrevious": "Start After Previous", "PE.Views.DocumentHolder.textStartOnClick": "Start On Click", "PE.Views.DocumentHolder.textStartWithPrevious": "Start With Previous", "PE.Views.DocumentHolder.textUndo": "Batalkan", "PE.Views.DocumentHolder.tipGuides": "<PERSON><PERSON><PERSON><PERSON> pemandu", "PE.Views.DocumentHolder.tipIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "PE.Views.DocumentHolder.toDictionaryText": "Tambah ke Kamus", "PE.Views.DocumentHolder.txtAddBottom": "Tambah pembatas bawah", "PE.Views.DocumentHolder.txtAddFractionBar": "Tambah bar pecahan", "PE.Views.DocumentHolder.txtAddHor": "Tambah garis horizontal", "PE.Views.DocumentHolder.txtAddLB": "<PERSON><PERSON> garis kiri bawah", "PE.Views.DocumentHolder.txtAddLeft": "Tambah pembatas kiri", "PE.Views.DocumentHolder.txtAddLT": "Tambah garis kiri atas", "PE.Views.DocumentHolder.txtAddRight": "Tambah pembatas kiri", "PE.Views.DocumentHolder.txtAddTop": "Tambah pembatas atas", "PE.Views.DocumentHolder.txtAddVer": "<PERSON><PERSON> garis vertikal", "PE.Views.DocumentHolder.txtAlign": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAlignToChar": "<PERSON>a dengan karakter", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtBackground": "Background", "PE.Views.DocumentHolder.txtBorderProps": "Properti pembatas", "PE.Views.DocumentHolder.txtBottom": "<PERSON>wa<PERSON>", "PE.Views.DocumentHolder.txtChangeLayout": "Ubah tata letak", "PE.Views.DocumentHolder.txtChangeTheme": "<PERSON><PERSON> Tema", "PE.Views.DocumentHolder.txtColumnAlign": "<PERSON>a kolom", "PE.Views.DocumentHolder.txtDecreaseArg": "Kurangi ukuran argumen", "PE.Views.DocumentHolder.txtDeleteArg": "Hapus argumen", "PE.Views.DocumentHolder.txtDeleteBreak": "Hapus break manual", "PE.Views.DocumentHolder.txtDeleteChars": "Hapus karakter terlampir", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON> karakter dan separator terlampir", "PE.Views.DocumentHolder.txtDeleteEq": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON> char", "PE.Views.DocumentHolder.txtDeleteRadical": "Hapus radikal", "PE.Views.DocumentHolder.txtDeleteSlide": "Hapus slide", "PE.Views.DocumentHolder.txtDistribHor": "Distribusikan arah horizontal", "PE.Views.DocumentHolder.txtDistribVert": "Distribusikan arah vertikal", "PE.Views.DocumentHolder.txtDuplicateSlide": "Gandakan slide", "PE.Views.DocumentHolder.txtFractionLinear": "Ubah ke pecahan linear", "PE.Views.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> ke pecahan", "PE.Views.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> ke pecahan bert<PERSON>uk", "PE.Views.DocumentHolder.txtGroup": "Grup", "PE.Views.DocumentHolder.txtGroupCharOver": "<PERSON>kter di atas teks", "PE.Views.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON> di bawah teks", "PE.Views.DocumentHolder.txtHideBottom": "Sembunyikan pembatas bawah", "PE.Views.DocumentHolder.txtHideBottomLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> nilai batas bawah", "PE.Views.DocumentHolder.txtHideCloseBracket": "Sembunyikan tanda kurung tutup", "PE.Views.DocumentHolder.txtHideDegree": "Sembunyikan degree", "PE.Views.DocumentHolder.txtHideHor": "Sembunyikan garis horizontal", "PE.Views.DocumentHolder.txtHideLB": "Sembunyikan garis bawah kiri", "PE.Views.DocumentHolder.txtHideLeft": "Sembunyikan pembatas kiri", "PE.Views.DocumentHolder.txtHideLT": "Sembunyikan garis atas kiri", "PE.Views.DocumentHolder.txtHideOpenBracket": "Sembunyikan tanda kurung buka", "PE.Views.DocumentHolder.txtHidePlaceholder": "Sembunyikan placeholder", "PE.Views.DocumentHolder.txtHideRight": "Sembunyikan pembatas kanan", "PE.Views.DocumentHolder.txtHideTop": "Sembunyikan pembatas atas", "PE.Views.DocumentHolder.txtHideTopLimit": "Se<PERSON><PERSON><PERSON><PERSON> nilai batas atas", "PE.Views.DocumentHolder.txtHideVer": "Sembunyikan garis vertikal", "PE.Views.DocumentHolder.txtIncreaseArg": "Tingkatkan ukuran argumen", "PE.Views.DocumentHolder.txtInsAudio": "Sisipkan audio", "PE.Views.DocumentHolder.txtInsChart": "<PERSON><PERSON><PERSON><PERSON> bagan", "PE.Views.DocumentHolder.txtInsertArgAfter": "Sisipkan argumen setelah", "PE.Views.DocumentHolder.txtInsertArgBefore": "Sisipkan argumen sebelum", "PE.Views.DocumentHolder.txtInsertBreak": "Sisi<PERSON>kan break manual", "PE.Views.DocumentHolder.txtInsertEqAfter": "<PERSON><PERSON><PERSON><PERSON> persa<PERSON>an set<PERSON>h", "PE.Views.DocumentHolder.txtInsertEqBefore": "<PERSON>sip<PERSON> persamaan sebelum", "PE.Views.DocumentHolder.txtInsImage": "Sisip<PERSON> gambar dari File", "PE.Views.DocumentHolder.txtInsImageUrl": "Sisipkan gambar dari URL", "PE.Views.DocumentHolder.txtInsSmartArt": "Sisipkan SmartArt", "PE.Views.DocumentHolder.txtInsTable": "<PERSON>si<PERSON><PERSON> tabel", "PE.Views.DocumentHolder.txtInsVideo": "Sisipkan video", "PE.Views.DocumentHolder.txtKeepTextOnly": "Pertahankan hanya teks", "PE.Views.DocumentHolder.txtLimitChange": "Ganti lokasi limit", "PE.Views.DocumentHolder.txtLimitOver": "Batasi di atas teks", "PE.Views.DocumentHolder.txtLimitUnder": "Batasi di bawah teks", "PE.Views.DocumentHolder.txtMatchBrackets": "Sesuaikan tanda kurung dengan tinggi argumen", "PE.Views.DocumentHolder.txtMatrixAlign": "<PERSON>a matriks", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Pindahkan slide ke akhir", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Pindahkan slide ke awal", "PE.Views.DocumentHolder.txtNewSlide": "Slide baru", "PE.Views.DocumentHolder.txtOverbar": "Bar di atas teks", "PE.Views.DocumentHolder.txtPasteDestFormat": "<PERSON><PERSON><PERSON> tema destinasi", "PE.Views.DocumentHolder.txtPastePicture": "Gambar", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Pertahankan formatting sumber", "PE.Views.DocumentHolder.txtPressLink": "<PERSON><PERSON> {0} dan klik link", "PE.Views.DocumentHolder.txtPreview": "<PERSON><PERSON> slideshow", "PE.Views.DocumentHolder.txtPrintSelection": "Cetak pilihan", "PE.Views.DocumentHolder.txtRemFractionBar": "Hilangkan bar pecahan", "PE.Views.DocumentHolder.txtRemLimit": "Hilangkan limit", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Hilangkan aksen karakter", "PE.Views.DocumentHolder.txtRemoveBar": "Hilangkan diagram batang", "PE.Views.DocumentHolder.txtRemScripts": "Hilangkan skrip", "PE.Views.DocumentHolder.txtRemSubscript": "Hilangkan subscript", "PE.Views.DocumentHolder.txtRemSuperscript": "Hilangkan superscript", "PE.Views.DocumentHolder.txtResetLayout": "Reset slide", "PE.Views.DocumentHolder.txtScriptsAfter": "<PERSON>ripts setelah teks", "PE.Views.DocumentHolder.txtScriptsBefore": "Scripts sebelum teks", "PE.Views.DocumentHolder.txtSelectAll": "<PERSON><PERSON><PERSON> se<PERSON>a", "PE.Views.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON><PERSON> batas bawah", "PE.Views.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON><PERSON> kurung penutup", "PE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON> degree", "PE.Views.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON><PERSON> kurung pembuka", "PE.Views.DocumentHolder.txtShowPlaceholder": "Tampilkan placeholder", "PE.Views.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON><PERSON> batas atas", "PE.Views.DocumentHolder.txtSlide": "Slide", "PE.Views.DocumentHolder.txtSlideHide": "Sembunyikan slide", "PE.Views.DocumentHolder.txtStretchBrackets": "Regangkan dalam kurung", "PE.Views.DocumentHolder.txtTop": "Atas", "PE.Views.DocumentHolder.txtUnderbar": "Bar di bawah teks", "PE.Views.DocumentHolder.txtUngroup": "Pisahkan dari grup", "PE.Views.DocumentHolder.txtWarnUrl": "Klik link ini bisa berbahaya untuk perangkat dan data Anda.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin tetap lanjut?", "PE.Views.DocumentHolder.unicodeText": "Unicode", "PE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.goToSlideText": "<PERSON><PERSON> ke Slide", "PE.Views.DocumentPreview.slideIndexText": "Slide {0} dari {1}", "PE.Views.DocumentPreview.txtClose": "Tutup slideshow", "PE.Views.DocumentPreview.txtDraw": "Gambar", "PE.Views.DocumentPreview.txtEndSlideshow": "<PERSON><PERSON><PERSON> slideshow", "PE.Views.DocumentPreview.txtEraser": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtEraseScreen": "Erase screen", "PE.Views.DocumentPreview.txtExitFullScreen": "<PERSON><PERSON><PERSON> layar penuh", "PE.Views.DocumentPreview.txtFinalMessage": "<PERSON><PERSON><PERSON> dari preview slide. Klik untuk keluar.", "PE.Views.DocumentPreview.txtFullScreen": "<PERSON><PERSON> penuh", "PE.Views.DocumentPreview.txtHighlighter": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtInkColor": "Ink color", "PE.Views.DocumentPreview.txtNext": "Slide berikutnya", "PE.Views.DocumentPreview.txtPageNumInvalid": "Nomor slide tidak tepat", "PE.Views.DocumentPreview.txtPause": "<PERSON><PERSON> presentasi", "PE.Views.DocumentPreview.txtPen": "<PERSON><PERSON>", "PE.Views.DocumentPreview.txtPlay": "<PERSON><PERSON>", "PE.Views.DocumentPreview.txtPrev": "Slide sebelumnya", "PE.Views.DocumentPreview.txtReset": "<PERSON><PERSON>", "PE.Views.FileMenu.ariaFileMenu": "File menu", "PE.Views.FileMenu.btnAboutCaption": "Tentang", "PE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON> Dokumen", "PE.Views.FileMenu.btnCloseEditor": "Close File", "PE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> se<PERSON>ai", "PE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnHelpCaption": "Bantuan", "PE.Views.FileMenu.btnHistoryCaption": "Riwayat versi", "PE.Views.FileMenu.btnInfoCaption": "Info Presentasi", "PE.Views.FileMenu.btnPrintCaption": "Cetak", "PE.Views.FileMenu.btnProtectCaption": "Proteks<PERSON>", "PE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON> yang <PERSON>", "PE.Views.FileMenu.btnRenameCaption": "Ganti nama", "PE.Views.FileMenu.btnReturnCaption": "Ke<PERSON><PERSON> ke Presentasi", "PE.Views.FileMenu.btnRightsCaption": "Hak Akses", "PE.Views.FileMenu.btnSaveAsCaption": "Simpan sebagai", "PE.Views.FileMenu.btnSaveCaption": "Simpan", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Simpan Salinan sebagai", "PE.Views.FileMenu.btnSettingsCaption": "Pen<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "PE.Views.FileMenu.btnToEditCaption": "Edit presentasi", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Terapkan", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Tambah<PERSON> penulis", "PE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Tambah teks", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplikasi", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Ubah hak akses", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Komentar", "PE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Dibuat", "PE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Pemilik", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtPresentationInfo": "Info Presentasi", "PE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "PE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Orang yang memiliki hak", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subyek", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tag", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Diunggah", "PE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "PE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Hak Akses", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Ubah hak akses", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Orang yang memiliki hak", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Peringatan", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON>gan password", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON>gan tanda tangan", "PE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Tanda tangan yang valid telah ditambahkan ke presentasi.<br>Presentasi ini terlindung dari pengeditan.", "PE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Pastikan integritas presentasi dengan menambahkan<br>tanda tangan digital tak tampak", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Edit presentasi", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "<PERSON><PERSON><PERSON> akan menghila<PERSON>kan tanda tangan dari presentasi.<br><PERSON><PERSON><PERSON><PERSON><PERSON>?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Presentasi ini diproteksi oleh password", "PE.Views.FileMenuPanels.ProtectDoc.txtProtectPresentation": "Enkripsi presentasi ini dengan kata sandi", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Tandatangan valid sudah ditambahkan ke presentasi. Presentasi ini diproteksi untuk diedit.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Beberapa tanda tangan digital di presentasi tidak valid atau tidak bisa diverifikasi. Presentasi ini diproteksi untuk diedit.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON><PERSON><PERSON> tanda tangan", "PE.Views.FileMenuPanels.Settings.okButtonText": "Terapkan", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Mode Edit <PERSON>", "PE.Views.FileMenuPanels.Settings.strFast": "Cepat", "PE.Views.FileMenuPanels.Settings.strFontRender": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "A<PERSON><PERSON>n kata dalam HURUF BESAR", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Abaikan kata dengan angka", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Pengat<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Tam<PERSON>lkan tombol Opsi Paste ketika konten sedang dipaste", "PE.Views.FileMenuPanels.Settings.strRTLSupport": "RTL interface", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>han dari pengguna lain", "PE.Views.FileMenuPanels.Settings.strStrict": "Strict", "PE.Views.FileMenuPanels.Settings.strTabStyle": "Tab style", "PE.Views.FileMenuPanels.Settings.strTheme": "Tema interface", "PE.Views.FileMenuPanels.Settings.strUnit": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strZoom": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Tiap 10 Menit", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Tiap 30 Menit", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Tiap 5 Menit", "PE.Views.FileMenuPanels.Settings.text60Minutes": "Tiap <PERSON>", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textAutoSave": "<PERSON><PERSON><PERSON> otomatis", "PE.Views.FileMenuPanels.Settings.textDisabled": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textFill": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textForceSave": "Menyimpan versi intermedier", "PE.Views.FileMenuPanels.Settings.textLine": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textMinute": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "Pen<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtAll": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtAppearance": "Penampilan", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opsi AutoCorrect...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Mode cache default", "PE.Views.FileMenuPanels.Settings.txtCm": "Sentimeter", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtCustomizeQuickAccess": "Customize quick access", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "<PERSON><PERSON><PERSON><PERSON> dan pen<PERSON>n", "PE.Views.FileMenuPanels.Settings.txtFastTip": "<PERSON><PERSON><PERSON><PERSON> bersama waktu-nyata. <PERSON><PERSON><PERSON> disimpan secara otomatis", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Fit Slide", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtInch": "Inci", "PE.Views.FileMenuPanels.Settings.txtLast": "<PERSON><PERSON> ya<PERSON>", "PE.Views.FileMenuPanels.Settings.txtLastUsed": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtMac": "sebagai OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtProofing": "Proofing", "PE.Views.FileMenuPanels.Settings.txtPt": "Titik", "PE.Views.FileMenuPanels.Settings.txtQuickPrint": "Tam<PERSON>lkan tombol Cetak Cepat dalam header editor", "PE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "Dokumen akan dicetak pada printer yang terakhir dipilih atau baku", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Aktifkan Semua", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Aktifkan semua macros tanpa notifikasi", "PE.Views.FileMenuPanels.Settings.txtScreenReader": "Turn on screen reader support", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Nonak<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Nonaktifkan semua macros tanpa notifikasi", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "<PERSON><PERSON>n tombol \"Simpan\" untuk menyinkronkan perubahan yang Anda dan orang lain lakukan", "PE.Views.FileMenuPanels.Settings.txtTabBack": "Use toolbar color as tabs background", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Gunakan tombol Alt untuk menavigasi antarmuka pengguna menggunakan keyboard", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Gunakan tombol Option untuk menavigasi antarmuka pengguna menggunakan keyboard", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Nonaktifkan semua macros dengan notifikasi", "PE.Views.FileMenuPanels.Settings.txtWin": "sebagai Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "<PERSON><PERSON><PERSON> se<PERSON>ai", "PE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Simpan Salinan sebagai", "PE.Views.GridSettings.textCm": "cm", "PE.Views.GridSettings.textCustom": "Kustom", "PE.Views.GridSettings.textSpacing": "<PERSON><PERSON>", "PE.Views.GridSettings.textTitle": "Pengaturan kisi", "PE.Views.HeaderFooterDialog.applyAllText": "Terapkan untuk Semua", "PE.Views.HeaderFooterDialog.applyText": "Terapkan", "PE.Views.HeaderFooterDialog.diffLanguage": "Anda tidak dapat menggunakan format tanggal dalam bahasa yang berbeda dari master slide.<br><PERSON><PERSON>k mengubah master, k<PERSON> 'Terapkan ke semua' bukan '<PERSON><PERSON><PERSON>'", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Peringatan", "PE.Views.HeaderFooterDialog.textDateTime": "<PERSON><PERSON> dan <PERSON>", "PE.Views.HeaderFooterDialog.textFixed": "Fixed", "PE.Views.HeaderFooterDialog.textFormat": "Format", "PE.Views.HeaderFooterDialog.textHFTitle": "Pen<PERSON><PERSON><PERSON> Header/Footer", "PE.Views.HeaderFooterDialog.textLang": "Bahasa", "PE.Views.HeaderFooterDialog.textNotes": "Catatan dan <PERSON>", "PE.Views.HeaderFooterDialog.textNotTitle": "<PERSON><PERSON> ta<PERSON> di judul slide", "PE.Views.HeaderFooterDialog.textPageNum": "<PERSON><PERSON> halaman", "PE.Views.HeaderFooterDialog.textPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textSlide": "Slide", "PE.Views.HeaderFooterDialog.textSlideNum": "Nomor slide", "PE.Views.HeaderFooterDialog.textUpdate": "Update secara otomatis", "PE.Views.HeaderFooterDialog.txtFooter": "Footer", "PE.Views.HeaderFooterDialog.txtHeader": "Header", "PE.Views.HyperlinkSettingsDialog.strDisplay": "Tampilan", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Tautkan ke", "PE.Views.HyperlinkSettingsDialog.textDefault": "Fragmen teks yang dipilih", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Masukkan caption di sini", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Masukkan link di sini", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Masukkan tooltip disini", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Tautan eksternal", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Slide dalam presentasi ini", "PE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "PE.Views.HyperlinkSettingsDialog.textSlides": "Slide", "PE.Views.HyperlinkSettingsDialog.textTipText": "Teks ScreenTip", "PE.Views.HyperlinkSettingsDialog.textTitle": "Pengaturan hyperlink", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Slide pertama", "PE.Views.HyperlinkSettingsDialog.txtLast": "Slide terakhir", "PE.Views.HyperlinkSettingsDialog.txtNext": "Slide berikutnya", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Ruas ini harus berupa URL dengan format \"http://www.contoh.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Slide sebelumnya", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "R<PERSON>s ini dibatasi 2083 karakter", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Slide", "PE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "PE.Views.ImageSettings.strTransparency": "Opacity", "PE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "PE.Views.ImageSettings.textCrop": "<PERSON><PERSON>", "PE.Views.ImageSettings.textCropFill": "<PERSON><PERSON>", "PE.Views.ImageSettings.textCropFit": "Fit", "PE.Views.ImageSettings.textCropToShape": "<PERSON><PERSON> men<PERSON>uk", "PE.Views.ImageSettings.textEdit": "Sunting", "PE.Views.ImageSettings.textEditObject": "<PERSON>", "PE.Views.ImageSettings.textFitSlide": "Fit Slide", "PE.Views.ImageSettings.textFlip": "Flip", "PE.Views.ImageSettings.textFromFile": "<PERSON><PERSON>", "PE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON>", "PE.Views.ImageSettings.textFromUrl": "Dari URL", "PE.Views.ImageSettings.textHeight": "Tingg<PERSON>", "PE.Views.ImageSettings.textHint270": "Rotasi 90° Berlawanan Jarum Jam", "PE.Views.ImageSettings.textHint90": "Rotasi 90° Searah Jarum Jam", "PE.Views.ImageSettings.textHintFlipH": "<PERSON><PERSON>", "PE.Views.ImageSettings.textHintFlipV": "<PERSON><PERSON>", "PE.Views.ImageSettings.textInsert": "Ganti Gambar", "PE.Views.ImageSettings.textOriginalSize": "Ukuran Sebenarnya", "PE.Views.ImageSettings.textRecentlyUsed": "<PERSON><PERSON>", "PE.Views.ImageSettings.textResetCrop": "Reset crop", "PE.Views.ImageSettings.textRotate90": "Rotasi 90°", "PE.Views.ImageSettings.textRotation": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textSize": "Ukuran", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Teks alternatif", "PE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAltTip": "Representasi alternatif berbasis teks dari informasi objek visual, yang akan dibaca kepada orang dengan gangguan penglihatan atau kognitif untuk membantu mereka lebih memahami informasi yang ada dalam gambar, shape, grafik, atau tabel.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "Tengah", "PE.Views.ImageSettingsAdvanced.textFlipped": "Flipped", "PE.Views.ImageSettingsAdvanced.textFrom": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textGeneral": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textHeight": "Tingg<PERSON>", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Secara Horizontal", "PE.Views.ImageSettingsAdvanced.textImageName": "<PERSON><PERSON> gambar", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "Ukuran sebenarnya", "PE.Views.ImageSettingsAdvanced.textPlacement": "Penempatan", "PE.Views.ImageSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textSize": "Ukuran", "PE.Views.ImageSettingsAdvanced.textTitle": "Gambar - Pen<PERSON><PERSON><PERSON> la<PERSON>", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "Pojok kiri atas", "PE.Views.ImageSettingsAdvanced.textVertical": "Vertikal", "PE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.LeftMenu.ariaLeftMenu": "Left menu", "PE.Views.LeftMenu.tipAbout": "Tentang", "PE.Views.LeftMenu.tipChat": "Cha<PERSON>", "PE.Views.LeftMenu.tipComments": "Komentar", "PE.Views.LeftMenu.tipPlugins": "Plugins", "PE.Views.LeftMenu.tipSearch": "<PERSON><PERSON>", "PE.Views.LeftMenu.tipSlides": "Slide", "PE.Views.LeftMenu.tipSupport": "Masukan & Dukungan", "PE.Views.LeftMenu.tipTitles": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.txtDeveloper": "MODE DEVELOPER", "PE.Views.LeftMenu.txtEditor": "Penyunting Presentasi", "PE.Views.LeftMenu.txtLimit": "<PERSON><PERSON>", "PE.Views.LeftMenu.txtTrial": "MODE TRIAL", "PE.Views.LeftMenu.txtTrialDev": "Mode Trial Developer", "PE.Views.ParagraphSettings.strLineHeight": "Spasi Antar Baris", "PE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.strSpacingAfter": "Sesudah", "PE.Views.ParagraphSettings.strSpacingBefore": "Sebelum", "PE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "PE.Views.ParagraphSettings.textAt": "Pada", "PE.Views.ParagraphSettings.textAtLeast": "Sekurang-kurangnya", "PE.Views.ParagraphSettings.textAuto": "Banyak", "PE.Views.ParagraphSettings.textExact": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.txtAutoText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.noTabs": "Tab yang ditentukan akan muncul pada ruas ini", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON> ka<PERSON>al semua", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Garis coret ganda", "PE.Views.ParagraphSettingsAdvanced.strIndent": "Indentasi", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Jarak baris", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Setela<PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Sebelum", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Spesial", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indentasi & Peletakan", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON> ganda", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Subskrip", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superskrip", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Tab", "PE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textAuto": "Banyak", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Spasi antar karakter", "PE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON> baku", "PE.Views.ParagraphSettingsAdvanced.textEffects": "Efek", "PE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Menggantung", "PE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nihil)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Hapus", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON> semua", "PE.Views.ParagraphSettingsAdvanced.textSet": "Tentukan", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Tengah", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posisi tab", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraf - Pengaturan lan<PERSON>", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.PrintWithPreview.txtAllPages": "<PERSON><PERSON><PERSON> slide", "PE.Views.PrintWithPreview.txtBothSides": "Cetak pada kedua sisi", "PE.Views.PrintWithPreview.txtBothSidesLongDesc": "Balik halaman pada sisi panjang", "PE.Views.PrintWithPreview.txtBothSidesShortDesc": "Balik halaman pada sisi pendek", "PE.Views.PrintWithPreview.txtCopies": "Salinan", "PE.Views.PrintWithPreview.txtCurrentPage": "Slide saat ini", "PE.Views.PrintWithPreview.txtCustomPages": "Cetak k<PERSON>", "PE.Views.PrintWithPreview.txtEmptyTable": "Tidak ada yang akan dicetak karena presentasi kosong", "PE.Views.PrintWithPreview.txtHeaderFooterSettings": "Pengat<PERSON>n header/footer", "PE.Views.PrintWithPreview.txtOf": "dari {0}", "PE.Views.PrintWithPreview.txtOneSide": "Cetak satu sisi", "PE.Views.PrintWithPreview.txtOneSideDesc": "Cetak di satu sisi halaman saja", "PE.Views.PrintWithPreview.txtPage": "Slide", "PE.Views.PrintWithPreview.txtPageNumInvalid": "Nomor slide tidak valid", "PE.Views.PrintWithPreview.txtPages": "Slide", "PE.Views.PrintWithPreview.txtPaperSize": "Ukuran kertas", "PE.Views.PrintWithPreview.txtPrint": "Cetak", "PE.Views.PrintWithPreview.txtPrintPdf": "Cetak ke PDF", "PE.Views.PrintWithPreview.txtPrintRange": "Rentang cetak", "PE.Views.PrintWithPreview.txtPrintSides": "Sisi cetak", "PE.Views.RightMenu.ariaRightMenu": "Right menu", "PE.Views.RightMenu.txtChartSettings": "Pengaturan <PERSON>", "PE.Views.RightMenu.txtImageSettings": "Pengaturan Gambar", "PE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtShapeSettings": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtSignatureSettings": "<PERSON><PERSON><PERSON><PERSON> tanda tangan", "PE.Views.RightMenu.txtSlideSettings": "Pengaturan slide", "PE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtTextArtSettings": "Pengaturan Text Art", "PE.Views.ShapeSettings.strBackground": "<PERSON><PERSON> la<PERSON>", "PE.Views.ShapeSettings.strChange": "Ubah Bentuk", "PE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strFill": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strForeground": "<PERSON><PERSON> la<PERSON> depan", "PE.Views.ShapeSettings.strPattern": "Pola", "PE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON><PERSON> bayangan", "PE.Views.ShapeSettings.strSize": "Ukuran", "PE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strTransparency": "Opasitas", "PE.Views.ShapeSettings.strType": "Tipe", "PE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "PE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "<PERSON><PERSON> yang dimasukkan tidak tepat.<br><PERSON><PERSON><PERSON> masukkan nilai antara 0 pt dan 1584 pt.", "PE.Views.ShapeSettings.textColor": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textDirection": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textEditPoints": "<PERSON> titik", "PE.Views.ShapeSettings.textEditShape": "<PERSON>", "PE.Views.ShapeSettings.textEmptyPattern": "Tidak ada Pola", "PE.Views.ShapeSettings.textEyedropper": "Eyedropper", "PE.Views.ShapeSettings.textFlip": "Flip", "PE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textFromUrl": "Dari URL", "PE.Views.ShapeSettings.textGradient": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textHint270": "Rotasi 90° Berlawanan Jarum Jam", "PE.Views.ShapeSettings.textHint90": "Rotasi 90° Searah Jarum Jam", "PE.Views.ShapeSettings.textHintFlipH": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textHintFlipV": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textImageTexture": "Gambar atau Tekstur", "PE.Views.ShapeSettings.textLinear": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textMoreColors": "More colors", "PE.Views.ShapeSettings.textNoFill": "Tidak ada <PERSON>", "PE.Views.ShapeSettings.textNoShadow": "No Shadow", "PE.Views.ShapeSettings.textPatternFill": "Pola", "PE.Views.ShapeSettings.textPosition": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textRadial": "Radial", "PE.Views.ShapeSettings.textRecentlyUsed": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textRotate90": "Rotasi 90°", "PE.Views.ShapeSettings.textRotation": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textShadow": "Shadow", "PE.Views.ShapeSettings.textStretch": "Rentangkan", "PE.Views.ShapeSettings.textStyle": "Model", "PE.Views.ShapeSettings.textTexture": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "PE.Views.ShapeSettings.tipAddGradientPoint": "Tambah titik gradien", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Hilangkan titik gradien", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON>rtas Coklat", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtGranite": "Granit", "PE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "Tidak ada <PERSON>", "PE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strMargins": "Pengganjal teks", "PE.Views.ShapeSettingsAdvanced.textAlt": "Teks alternatif", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAltTip": "Representasi alternatif berbasis teks dari informasi objek visual, yang akan dibaca kepada orang dengan gangguan penglihatan atau kognitif untuk membantu mereka lebih memahami informasi yang ada dalam gambar, shape, grafik, atau tabel.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "Tanda panah", "PE.Views.ShapeSettingsAdvanced.textAutofit": "AutoFit", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> mulai", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON> mulai", "PE.Views.ShapeSettingsAdvanced.textBevel": "Miring", "PE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON>wa<PERSON>", "PE.Views.ShapeSettingsAdvanced.textCapType": "Tipe cap", "PE.Views.ShapeSettingsAdvanced.textCenter": "Tengah", "PE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON> akhir", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFlat": "Datar", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Flipped", "PE.Views.ShapeSettingsAdvanced.textFrom": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textGeneral": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textHeight": "Tingg<PERSON>", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Secara Horizontal", "PE.Views.ShapeSettingsAdvanced.textJoinType": "<PERSON><PERSON><PERSON> sambungan", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON> garis", "PE.Views.ShapeSettingsAdvanced.textMiter": "Siku-siku", "PE.Views.ShapeSettingsAdvanced.textNofit": "<PERSON>an autofit", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Penempatan", "PE.Views.ShapeSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Ubah ukuran bentuk agar cocok ke teks", "PE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textShapeName": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textShrink": "Shrink teks di overflow", "PE.Views.ShapeSettingsAdvanced.textSize": "Ukuran", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Spacing di antara kolom", "PE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Kotak teks", "PE.Views.ShapeSettingsAdvanced.textTitle": "Bentuk - Pengaturan lan<PERSON>t", "PE.Views.ShapeSettingsAdvanced.textTop": "Atas", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "Pojok kiri atas", "PE.Views.ShapeSettingsAdvanced.textVertical": "Vertikal", "PE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Bobot & Panah", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "Tidak ada", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Peringatan", "PE.Views.SignatureSettings.strDelete": "Hilangkan Tandatangan", "PE.Views.SignatureSettings.strDetails": "Det<PERSON>", "PE.Views.SignatureSettings.strInvalid": "Tanda tangan tidak valid", "PE.Views.SignatureSettings.strSign": "Tandatangan", "PE.Views.SignatureSettings.strSignature": "<PERSON><PERSON>", "PE.Views.SignatureSettings.strValid": "Tanda tangan valid", "PE.Views.SignatureSettings.txtContinueEditing": "Tetap edit", "PE.Views.SignatureSettings.txtEditWarning": "<PERSON><PERSON><PERSON> akan menghila<PERSON>kan tanda tangan dari presentasi.<br><PERSON><PERSON><PERSON><PERSON><PERSON>?", "PE.Views.SignatureSettings.txtRemoveWarning": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menghilangkan tandatangan ini?<br>Proses tidak bisa dikembalikan.", "PE.Views.SignatureSettings.txtSigned": "Tandatangan valid sudah ditambahkan ke presentasi. Presentasi ini diproteksi untuk diedit.", "PE.Views.SignatureSettings.txtSignedInvalid": "Beberapa tanda tangan digital di presentasi tidak valid atau tidak bisa diverifikasi. Presentasi ini diproteksi untuk diedit.", "PE.Views.SlideSettings.strApplyAllSlides": "Apply to All Slides", "PE.Views.SlideSettings.strBackground": "<PERSON><PERSON> la<PERSON>", "PE.Views.SlideSettings.strBackgroundGraphics": "Show Background graphics", "PE.Views.SlideSettings.strBackgroundReset": "Reset Background", "PE.Views.SlideSettings.strColor": "<PERSON><PERSON>", "PE.Views.SlideSettings.strDateTime": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "PE.Views.SlideSettings.strFill": "Background", "PE.Views.SlideSettings.strForeground": "<PERSON><PERSON> la<PERSON> depan", "PE.Views.SlideSettings.strPattern": "Pola", "PE.Views.SlideSettings.strSlideNum": "<PERSON><PERSON><PERSON><PERSON> Slide", "PE.Views.SlideSettings.strTransparency": "Opasitas", "PE.Views.SlideSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "<PERSON><PERSON>", "PE.Views.SlideSettings.textDirection": "<PERSON><PERSON>", "PE.Views.SlideSettings.textEmptyPattern": "Tidak ada Pola", "PE.Views.SlideSettings.textFromFile": "<PERSON><PERSON>", "PE.Views.SlideSettings.textFromStorage": "<PERSON><PERSON>", "PE.Views.SlideSettings.textFromUrl": "Dari URL", "PE.Views.SlideSettings.textGradient": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textGradientFill": "<PERSON><PERSON>", "PE.Views.SlideSettings.textImageTexture": "Gambar atau Tekstur", "PE.Views.SlideSettings.textLinear": "<PERSON><PERSON>", "PE.Views.SlideSettings.textNoFill": "Tidak ada <PERSON>", "PE.Views.SlideSettings.textPatternFill": "Pola", "PE.Views.SlideSettings.textPosition": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textRadial": "Radial", "PE.Views.SlideSettings.textReset": "<PERSON><PERSON>", "PE.Views.SlideSettings.textSelectImage": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStretch": "Rentangkan", "PE.Views.SlideSettings.textStyle": "Model", "PE.Views.SlideSettings.textTexture": "<PERSON><PERSON>", "PE.Views.SlideSettings.textTile": "<PERSON><PERSON>", "PE.Views.SlideSettings.tipAddGradientPoint": "Tambah titik gradien", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Hilangkan titik gradien", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON>rtas Coklat", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtGrain": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtGranite": "Granit", "PE.Views.SlideSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtKnit": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtWood": "<PERSON><PERSON>", "PE.Views.SlideshowSettings.textLoop": "Loop terus-men<PERSON> sampai 'Esc' ditekan", "PE.Views.SlideshowSettings.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.strLandscape": "Landscape", "PE.Views.SlideSizeSettings.strPortrait": "Portrait", "PE.Views.SlideSizeSettings.textHeight": "Tingg<PERSON>", "PE.Views.SlideSizeSettings.textSlideOrientation": "Orientasi slide", "PE.Views.SlideSizeSettings.textSlideSize": "Ukuran slide", "PE.Views.SlideSizeSettings.textTitle": "Pengaturan ukuran slide", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "Slide 35 mm", "PE.Views.SlideSizeSettings.txtA3": "Kertas A3 (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "Kertas A4 (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO) Paper (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO) Paper (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtLedger": "Ledger Paper (11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "Letter Paper (8.5x11 in)", "PE.Views.SlideSizeSettings.txtOverhead": "Overhead", "PE.Views.SlideSizeSettings.txtSlideNum": "Nomor slide dari", "PE.Views.SlideSizeSettings.txtStandard": "Standard (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "<PERSON>ar lebar", "PE.Views.Statusbar.goToPageText": "<PERSON><PERSON> ke Slide", "PE.Views.Statusbar.pageIndexText": "Slide {0} dari {1}", "PE.Views.Statusbar.textShowBegin": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "PE.Views.Statusbar.textShowCurrent": "<PERSON><PERSON>lkan dari Slide Saat <PERSON>i", "PE.Views.Statusbar.textShowPresenterView": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Statusbar.textSlideMaster": "Slide master", "PE.Views.Statusbar.tipAccessRights": "Atur perizinan akses dokumen", "PE.Views.Statusbar.tipFitPage": "Fit Slide", "PE.Views.Statusbar.tipFitWidth": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Statusbar.tipPreview": "<PERSON><PERSON> slideshow", "PE.Views.Statusbar.tipSetLang": "Atur Bahasa Teks", "PE.Views.Statusbar.tipZoomFactor": "Pembesaran", "PE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON><PERSON>", "PE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Statusbar.txtPageNumInvalid": "Nomor slide tidak tepat", "PE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON>", "PE.Views.TableSettings.deleteRowText": "Hapus Baris", "PE.Views.TableSettings.deleteTableText": "<PERSON><PERSON>", "PE.Views.TableSettings.insertColumnLeftText": "<PERSON>si<PERSON><PERSON>", "PE.Views.TableSettings.insertColumnRightText": "<PERSON>si<PERSON><PERSON>", "PE.Views.TableSettings.insertRowAboveText": "Sisipkan Baris di Atas", "PE.Views.TableSettings.insertRowBelowText": "Sisipkan Baris di Bawah", "PE.Views.TableSettings.mergeCellsText": "Gabungkan Sel", "PE.Views.TableSettings.selectCellText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.splitCellsText": "Pisahkan Sel...", "PE.Views.TableSettings.splitCellTitleText": "Pi<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "PE.Views.TableSettings.textBackColor": "<PERSON><PERSON> la<PERSON>", "PE.Views.TableSettings.textBanded": "<PERSON><PERSON>", "PE.Views.TableSettings.textBorderColor": "<PERSON><PERSON>", "PE.Views.TableSettings.textBorders": "<PERSON><PERSON>", "PE.Views.TableSettings.textCellSize": "Ukuran <PERSON>", "PE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textDistributeCols": "Distribusikan kolom", "PE.Views.TableSettings.textDistributeRows": "Distribusikan baris", "PE.Views.TableSettings.textEdit": "Baris & Kolom", "PE.Views.TableSettings.textEmptyTemplate": "Tidak ada template", "PE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeader": "Header", "PE.Views.TableSettings.textHeight": "Tingg<PERSON>", "PE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textRows": "<PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "<PERSON><PERSON><PERSON> pembatas yang ingin Anda ubah dengan menerarpkan model yang telah dipilih di atas", "PE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textTotal": "Total", "PE.Views.TableSettings.textWidth": "<PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "<PERSON><PERSON><PERSON> dan <PERSON><PERSON><PERSON>", "PE.Views.TableSettings.tipBottom": "Buat Pembatas Bawah-<PERSON><PERSON>", "PE.Views.TableSettings.tipInner": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.tipInnerHor": "<PERSON><PERSON><PERSON> Horisontal Dalam <PERSON>", "PE.Views.TableSettings.tipInnerVert": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.tipLeft": "Buat Pembatas <PERSON>-<PERSON><PERSON>", "PE.Views.TableSettings.tipNone": "Tanpa <PERSON>em<PERSON>as", "PE.Views.TableSettings.tipOuter": "Buat Pembatas Luar Saja", "PE.Views.TableSettings.tipRight": "Buat Pembatas <PERSON>-<PERSON><PERSON>", "PE.Views.TableSettings.tipTop": "Buat Pembatas Atas-Luar <PERSON>", "PE.Views.TableSettings.txtGroupTable_Custom": "Kustom", "PE.Views.TableSettings.txtGroupTable_Dark": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Light": "<PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Medium": "Medium", "PE.Views.TableSettings.txtGroupTable_Optimal": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtNoBorders": "Tidak ada pembatas", "PE.Views.TableSettings.txtTable_Accent": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_DarkStyle": "Mode gelap", "PE.Views.TableSettings.txtTable_LightStyle": "<PERSON><PERSON>", "PE.Views.TableSettings.txtTable_MediumStyle": "Gaya Medium", "PE.Views.TableSettings.txtTable_NoGrid": "Tanpa Grid", "PE.Views.TableSettings.txtTable_NoStyle": "Tanpa Style", "PE.Views.TableSettings.txtTable_TableGrid": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_ThemedStyle": "Themed Style", "PE.Views.TableSettingsAdvanced.textAlt": "Teks alternatif", "PE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textAltTip": "Representasi alternatif berbasis teks dari informasi objek visual, yang akan dibaca kepada orang dengan gangguan penglihatan atau kognitif untuk membantu mereka lebih memahami informasi yang ada dalam gambar, shape, grafik, atau tabel.", "PE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textBottom": "<PERSON>wa<PERSON>", "PE.Views.TableSettingsAdvanced.textCenter": "Tengah", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Gunakan margin standar", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "<PERSON><PERSON> baku", "PE.Views.TableSettingsAdvanced.textFrom": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textGeneral": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textHeight": "Tingg<PERSON>", "PE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Proporsi konstan", "PE.Views.TableSettingsAdvanced.textLeft": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON> sel", "PE.Views.TableSettingsAdvanced.textPlacement": "Penempatan", "PE.Views.TableSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textSize": "Ukuran", "PE.Views.TableSettingsAdvanced.textTableName": "<PERSON><PERSON> tabel", "PE.Views.TableSettingsAdvanced.textTitle": "Tabel - Pengaturan lan<PERSON>", "PE.Views.TableSettingsAdvanced.textTop": "Atas", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "Pojok kiri atas", "PE.Views.TableSettingsAdvanced.textVertical": "Vertikal", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strBackground": "<PERSON><PERSON> la<PERSON>", "PE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strFill": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strForeground": "<PERSON><PERSON> la<PERSON> depan", "PE.Views.TextArtSettings.strPattern": "Pola", "PE.Views.TextArtSettings.strSize": "Ukuran", "PE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "Opasitas", "PE.Views.TextArtSettings.strType": "Tipe", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "<PERSON><PERSON> yang dimasukkan tidak tepat.<br><PERSON><PERSON><PERSON> masukkan nilai antara 0 pt dan 1584 pt.", "PE.Views.TextArtSettings.textColor": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textDirection": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textEmptyPattern": "Tidak ada Pola", "PE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textFromUrl": "Dari URL", "PE.Views.TextArtSettings.textGradient": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textImageTexture": "Gambar atau Tekstur", "PE.Views.TextArtSettings.textLinear": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textNoFill": "Tidak ada <PERSON>", "PE.Views.TextArtSettings.textPatternFill": "Pola", "PE.Views.TextArtSettings.textPosition": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textRadial": "Radial", "PE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStretch": "Rentangkan", "PE.Views.TextArtSettings.textStyle": "Model", "PE.Views.TextArtSettings.textTemplate": "Template", "PE.Views.TextArtSettings.textTexture": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTransform": "Transform", "PE.Views.TextArtSettings.tipAddGradientPoint": "Tambah titik gradien", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Hilangkan titik gradien", "PE.Views.TextArtSettings.txtBrownPaper": "<PERSON>rtas Coklat", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtGranite": "Granit", "PE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtNoBorders": "Tidak ada <PERSON>", "PE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtWood": "<PERSON><PERSON>", "PE.Views.Toolbar.capAddLayout": "Add Layout", "PE.Views.Toolbar.capAddSlide": "Tambahkan slide", "PE.Views.Toolbar.capAddSlideMaster": "Add Slide Master", "PE.Views.Toolbar.capBtnAddComment": "Tambahkan komentar", "PE.Views.Toolbar.capBtnComment": "Komentar", "PE.Views.Toolbar.capBtnDateTime": "Tanggal & Jam", "PE.Views.Toolbar.capBtnInsHeaderFooter": "Header & Footer", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "Simbol", "PE.Views.Toolbar.capBtnSlideNum": "Nomor slide", "PE.Views.Toolbar.capCloseMaster": "Close Master", "PE.Views.Toolbar.capInsertAudio": "Audio", "PE.Views.Toolbar.capInsertChart": "<PERSON><PERSON>", "PE.Views.Toolbar.capInsertEquation": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertHyperlink": "Hyperlink", "PE.Views.Toolbar.capInsertImage": "Gambar", "PE.Views.Toolbar.capInsertPlaceholder": "Insert Placeholder", "PE.Views.Toolbar.capInsertShape": "Bentuk", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON>", "PE.Views.Toolbar.capInsertText": "Kotak teks", "PE.Views.Toolbar.capInsertTextArt": "Text Art", "PE.Views.Toolbar.capInsertVideo": "Video", "PE.Views.Toolbar.capTabFile": "File", "PE.Views.Toolbar.capTabHome": "<PERSON><PERSON>", "PE.Views.Toolbar.capTabInsert": "<PERSON>sip<PERSON>", "PE.Views.Toolbar.mniCapitalizeWords": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniCustomTable": "<PERSON><PERSON><PERSON><PERSON> tabel ubahan", "PE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON> dari <PERSON>", "PE.Views.Toolbar.mniImageFromStorage": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniImageFromUrl": "Gambar dari URL", "PE.Views.Toolbar.mniInsertSSE": "Sisipkan Spreadsheet", "PE.Views.Toolbar.mniLowerCase": "huruf k<PERSON>il", "PE.Views.Toolbar.mniSentenceCase": "<PERSON> kalimat.", "PE.Views.Toolbar.mniSlideAdvanced": "Pengatura<PERSON> lan<PERSON>t", "PE.Views.Toolbar.mniSlideStandard": "Standard (4:3)", "PE.Views.Toolbar.mniSlideWide": "Widescreen (16:9)", "PE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "PE.Views.Toolbar.mniUpperCase": "HURUFBESAR", "PE.Views.Toolbar.strMenuNoFill": "Tidak ada <PERSON>", "PE.Views.Toolbar.textAlignBottom": "Ratakan teks ke bawah", "PE.Views.Toolbar.textAlignCenter": "Teks tengah", "PE.Views.Toolbar.textAlignJust": "Justify", "PE.Views.Toolbar.textAlignLeft": "Ratakan teks ke kiri", "PE.Views.Toolbar.textAlignMiddle": "Ratakan teks ke tengan", "PE.Views.Toolbar.textAlignRight": "Ratakan teks ke kanan", "PE.Views.Toolbar.textAlignTop": "Ratakan teks ke atas", "PE.Views.Toolbar.textAlpha": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textArrangeBack": "Jalankan di Background", "PE.Views.Toolbar.textArrangeBackward": "Mundurkan", "PE.Views.Toolbar.textArrangeForward": "Bawa ke depan", "PE.Views.Toolbar.textArrangeFront": "Bawa ke latar depan", "PE.Views.Toolbar.textBetta": "Huruf <PERSON>", "PE.Views.Toolbar.textBlackHeart": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textBold": "<PERSON><PERSON>", "PE.Views.Toolbar.textBullet": "Butir", "PE.Views.Toolbar.textChart": "Chart", "PE.Views.Toolbar.textColumnsCustom": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textColumnsOne": "<PERSON><PERSON>", "PE.Views.Toolbar.textColumnsThree": "Tiga Kolom", "PE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON>", "PE.Views.Toolbar.textContent": "Content", "PE.Views.Toolbar.textContentVertical": "Content (Vertical)", "PE.Views.Toolbar.textCopyright": "Tanda Hak Cipta", "PE.Views.Toolbar.textDegree": "<PERSON><PERSON>", "PE.Views.Toolbar.textDelta": "Huruf Delta Kecil", "PE.Views.Toolbar.textDivision": "Tanda Pembagi", "PE.Views.Toolbar.textDollar": "<PERSON><PERSON>", "PE.Views.Toolbar.textEuro": "Tanda Euro", "PE.Views.Toolbar.textFooters": "Footers", "PE.Views.Toolbar.textGreaterEqual": "<PERSON><PERSON><PERSON> atau <PERSON>", "PE.Views.Toolbar.textInfinity": "Tak Terbatas", "PE.Views.Toolbar.textItalic": "Miring", "PE.Views.Toolbar.textLessEqual": "<PERSON><PERSON> atau <PERSON>", "PE.Views.Toolbar.textLetterPi": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textLineSpaceOptions": "Line spacing options", "PE.Views.Toolbar.textListSettings": "List Pengaturan", "PE.Views.Toolbar.textMoreSymbols": "Simbol lainnya", "PE.Views.Toolbar.textNotEqualTo": "Tidak Sama Den<PERSON>", "PE.Views.Toolbar.textOneHalf": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textOneQuarter": "Pecahan <PERSON>hana Seperempat", "PE.Views.Toolbar.textPicture": "Picture", "PE.Views.Toolbar.textPlusMinus": "Tanda Plus-Minus", "PE.Views.Toolbar.textRecentlyUsed": "<PERSON><PERSON>", "PE.Views.Toolbar.textRegistered": "<PERSON><PERSON>", "PE.Views.Toolbar.textSection": "<PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignBottom": "<PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignCenter": "<PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignLeft": "<PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignMiddle": "Rata <PERSON>", "PE.Views.Toolbar.textShapeAlignRight": "<PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignTop": "<PERSON><PERSON>", "PE.Views.Toolbar.textShapesCombine": "Combine", "PE.Views.Toolbar.textShapesFragment": "Fragment", "PE.Views.Toolbar.textShapesIntersect": "Intersect", "PE.Views.Toolbar.textShapesSubstract": "Subtract", "PE.Views.Toolbar.textShapesUnion": "Union", "PE.Views.Toolbar.textShowBegin": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "PE.Views.Toolbar.textShowCurrent": "<PERSON><PERSON>lkan dari Slide Saat <PERSON>i", "PE.Views.Toolbar.textShowPresenterView": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShowSettings": "Pengaturan <PERSON>", "PE.Views.Toolbar.textSmartArt": "SmartArt", "PE.Views.Toolbar.textSmile": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textSquareRoot": "<PERSON><PERSON>", "PE.Views.Toolbar.textStrikeout": "<PERSON><PERSON> ganda", "PE.Views.Toolbar.textSubscript": "Subskrip", "PE.Views.Toolbar.textSuperscript": "Superskrip", "PE.Views.Toolbar.textTabAnimation": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabCollaboration": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabDesign": "Design", "PE.Views.Toolbar.textTabDraw": "Gambar", "PE.Views.Toolbar.textTabFile": "File", "PE.Views.Toolbar.textTabHome": "<PERSON><PERSON>", "PE.Views.Toolbar.textTabInsert": "<PERSON>sip<PERSON>", "PE.Views.Toolbar.textTable": "Table", "PE.Views.Toolbar.textTabProtect": "Proteks<PERSON>", "PE.Views.Toolbar.textTabTransitions": "Transisi", "PE.Views.Toolbar.textTabView": "Lihat", "PE.Views.Toolbar.textText": "Text", "PE.Views.Toolbar.textTextVertical": "Text (Vertical)", "PE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "PE.Views.Toolbar.textTitle": "Title", "PE.Views.Toolbar.textTitleError": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTradeMark": "<PERSON><PERSON>", "PE.Views.Toolbar.textUnderline": "<PERSON><PERSON> bawah", "PE.Views.Toolbar.textYen": "Tanda <PERSON>n", "PE.Views.Toolbar.tipAddLayout": "Add layout", "PE.Views.Toolbar.tipAddSlide": "Add Slide", "PE.Views.Toolbar.tipAddSlideMaster": "Add slide master", "PE.Views.Toolbar.tipBack": "Kembali", "PE.Views.Toolbar.tipChangeCase": "Ubah case", "PE.Views.Toolbar.tipChangeChart": "Ubah Tipe Bagan", "PE.Views.Toolbar.tipChangeSlide": "Ubah layout slide", "PE.Views.Toolbar.tipClearStyle": "Hapus Model", "PE.Views.Toolbar.tipCloseMaster": "Close Master", "PE.Views.Toolbar.tipColorSchemas": "Ubah Skema Warna", "PE.Views.Toolbar.tipColumns": "Sisipkan kolom", "PE.Views.Toolbar.tipCopy": "<PERSON><PERSON>", "PE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON>", "PE.Views.Toolbar.tipCut": "Potong", "PE.Views.Toolbar.tipDateTime": "<PERSON>sip<PERSON> tanggal dan jam sekarang", "PE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipDecPrLeft": "Kurangi Indentasi", "PE.Views.Toolbar.tipEditHeaderFooter": "Edit header at<PERSON> footer", "PE.Views.Toolbar.tipFontColor": "<PERSON><PERSON>", "PE.Views.Toolbar.tipFontName": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipFontSize": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipHAligh": "<PERSON>a horizontal", "PE.Views.Toolbar.tipHighlightColor": "<PERSON><PERSON>", "PE.Views.Toolbar.tipIncFont": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipIncPrLeft": "Tambahkan Indentasi", "PE.Views.Toolbar.tipInsertAudio": "Sisipkan audio", "PE.Views.Toolbar.tipInsertChart": "Sisipkan Bagan", "PE.Views.Toolbar.tipInsertChartPlaceholder": "Insert chart placeholder", "PE.Views.Toolbar.tipInsertContentPlaceholder": "Insert content placeholder", "PE.Views.Toolbar.tipInsertContentVerticalPlaceholder": "Insert content (vertical) placeholder", "PE.Views.Toolbar.tipInsertEquation": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertHorizontalText": "Sisipkan kotak teks horizontal", "PE.Views.Toolbar.tipInsertHyperlink": "Tambahkan hyperlink", "PE.Views.Toolbar.tipInsertImage": "Sisipkan Gambar", "PE.Views.Toolbar.tipInsertPicturePlaceholder": "Insert picture placeholder", "PE.Views.Toolbar.tipInsertShape": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertSmartArt": "Sisipkan SmartArt", "PE.Views.Toolbar.tipInsertSmartArtPlaceholder": "Insert SmartArt placeholder", "PE.Views.Toolbar.tipInsertSymbol": "Sisipkan simbol", "PE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertTablePlaceholder": "Insert table placeholder", "PE.Views.Toolbar.tipInsertText": "Sisipkan Teks", "PE.Views.Toolbar.tipInsertTextArt": "Sisipkan Text Art", "PE.Views.Toolbar.tipInsertTextPlaceholder": "Insert text placeholder", "PE.Views.Toolbar.tipInsertTextVerticalPlaceholder": "Insert text (vertical) placeholder", "PE.Views.Toolbar.tipInsertVerticalText": "Sisipkan kotak teks vertikal", "PE.Views.Toolbar.tipInsertVideo": "Sisipkan video", "PE.Views.Toolbar.tipLineSpace": "Spasi Antar Baris", "PE.Views.Toolbar.tipMarkers": "Butir", "PE.Views.Toolbar.tipMarkersArrow": "<PERSON><PERSON> panah", "PE.Views.Toolbar.tipMarkersCheckmark": "Butir tanda centang", "PE.Views.Toolbar.tipMarkersDash": "<PERSON><PERSON><PERSON> putus-putus", "PE.Views.Toolbar.tipMarkersFRhombus": "Butir belah ketupat isi", "PE.Views.Toolbar.tipMarkersFRound": "<PERSON><PERSON> ling<PERSON>n isi", "PE.Views.Toolbar.tipMarkersFSquare": "Butir persegi isi", "PE.Views.Toolbar.tipMarkersHRound": "Butir bundar hollow", "PE.Views.Toolbar.tipMarkersStar": "<PERSON><PERSON> bintang", "PE.Views.Toolbar.tipNone": "Tidak ada", "PE.Views.Toolbar.tipNumbers": "Penomoran", "PE.Views.Toolbar.tipPaste": "Tempel", "PE.Views.Toolbar.tipPreview": "<PERSON><PERSON> slideshow", "PE.Views.Toolbar.tipPrint": "Cetak", "PE.Views.Toolbar.tipPrintQuick": "Cetak cepat", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipReplace": "Replace", "PE.Views.Toolbar.tipSave": "Simpan", "PE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON>an per<PERSON>han yang Anda buat agar dapat dilihat oleh pengguna lain", "PE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON> se<PERSON>a", "PE.Views.Toolbar.tipShapeAlign": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipShapeArrange": "<PERSON><PERSON>", "PE.Views.Toolbar.tipShapesMerge": "Merge shapes", "PE.Views.Toolbar.tipSlideNum": "<PERSON><PERSON><PERSON><PERSON> nomor slide", "PE.Views.Toolbar.tipSlideSize": "<PERSON><PERSON><PERSON> slide", "PE.Views.Toolbar.tipSlideTheme": "Tema slide", "PE.Views.Toolbar.tipUndo": "Batalkan", "PE.Views.Toolbar.tipVAligh": "<PERSON>a vertikal", "PE.Views.Toolbar.tipViewSettings": "<PERSON><PERSON>", "PE.Views.Toolbar.txtColors": "Colors", "PE.Views.Toolbar.txtDistribHor": "Distribusikan ke arah horizontal", "PE.Views.Toolbar.txtDistribVert": "Distribusikan ke arah vertikal", "PE.Views.Toolbar.txtDuplicateSlide": "Gandakan slide", "PE.Views.Toolbar.txtGroup": "Grup", "PE.Views.Toolbar.txtObjectsAlign": "<PERSON><PERSON><PERSON> ya<PERSON>", "PE.Views.Toolbar.txtSlideAlign": "<PERSON><PERSON><PERSON> den<PERSON> slide", "PE.Views.Toolbar.txtSlideSize": "Slide size", "PE.Views.Toolbar.txtUngroup": "Pisahkan dari grup", "PE.Views.Transitions.strDelay": "Delay", "PE.Views.Transitions.strDuration": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.strStartOnClick": "<PERSON><PERSON>", "PE.Views.Transitions.textBlack": "Through Black", "PE.Views.Transitions.textBottom": "<PERSON><PERSON>", "PE.Views.Transitions.textBottomLeft": "<PERSON><PERSON>", "PE.Views.Transitions.textBottomRight": "<PERSON><PERSON>", "PE.Views.Transitions.textClock": "Jam", "PE.Views.Transitions.textClockwise": "<PERSON>rah Jarum Jam", "PE.Views.Transitions.textCounterclockwise": "<PERSON><PERSON><PERSON><PERSON> Jarum Jam", "PE.Views.Transitions.textCover": "Cover", "PE.Views.Transitions.textFade": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textHorizontalIn": "Horizontal Masuk", "PE.Views.Transitions.textHorizontalOut": "<PERSON><PERSON>", "PE.Views.Transitions.textLeft": "<PERSON><PERSON>", "PE.Views.Transitions.textMorph": "<PERSON><PERSON>", "PE.Views.Transitions.textMorphLetters": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textMorphObjects": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textMorphWord": "<PERSON><PERSON>", "PE.Views.Transitions.textNone": "Tidak ada", "PE.Views.Transitions.textPush": "<PERSON><PERSON>", "PE.Views.Transitions.textRandom": "Random", "PE.Views.Transitions.textRight": "<PERSON><PERSON>", "PE.Views.Transitions.textSmoothly": "<PERSON><PERSON>", "PE.Views.Transitions.textSplit": "Split", "PE.Views.Transitions.textTop": "<PERSON><PERSON>", "PE.Views.Transitions.textTopLeft": "<PERSON><PERSON>", "PE.Views.Transitions.textTopRight": "<PERSON><PERSON>", "PE.Views.Transitions.textUnCover": "UnCover", "PE.Views.Transitions.textVerticalIn": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textVerticalOut": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textWedge": "Wedge", "PE.Views.Transitions.textWipe": "Wipe", "PE.Views.Transitions.textZoom": "Pembesaran", "PE.Views.Transitions.textZoomIn": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoomOut": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoomRotate": "<PERSON>m dan <PERSON>", "PE.Views.Transitions.txtApplyToAll": "Terapkan untuk Semua Slide", "PE.Views.Transitions.txtParameters": "Parameter", "PE.Views.Transitions.txtPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.capBtnHand": "Hand", "PE.Views.ViewTab.capBtnSelect": "Select", "PE.Views.ViewTab.textAddHGuides": "Tambahkan pemandu horizontal", "PE.Views.ViewTab.textAddVGuides": "Tambahkan pemandu vertikal", "PE.Views.ViewTab.textAlwaysShowToolbar": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textClearGuides": "<PERSON><PERSON>", "PE.Views.ViewTab.textCm": "cm", "PE.Views.ViewTab.textCustom": "Kustom", "PE.Views.ViewTab.textFill": "<PERSON><PERSON>", "PE.Views.ViewTab.textFitToSlide": "Fit Slide", "PE.Views.ViewTab.textFitToWidth": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textGridlines": "<PERSON><PERSON>", "PE.Views.ViewTab.textGuides": "Pemandu", "PE.Views.ViewTab.textInterfaceTheme": "<PERSON><PERSON>", "PE.Views.ViewTab.textLeftMenu": "<PERSON> Kiri", "PE.Views.ViewTab.textLine": "<PERSON><PERSON>", "PE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "PE.Views.ViewTab.textNormal": "Normal", "PE.Views.ViewTab.textNotes": "Catatan", "PE.Views.ViewTab.textRightMenu": "<PERSON>", "PE.Views.ViewTab.textRulers": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textShowGridlines": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textShowGuides": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textSlideMaster": "Slide Master", "PE.Views.ViewTab.textSmartGuides": "Pemandu pintar", "PE.Views.ViewTab.textSnapObjects": "Posisikan O<PERSON> pada <PERSON>", "PE.Views.ViewTab.textStatusBar": "Bar Status", "PE.Views.ViewTab.textTabStyle": "Tab style", "PE.Views.ViewTab.textZoom": "Pembesaran", "PE.Views.ViewTab.tipFitToSlide": "Paskan dengan slide", "PE.Views.ViewTab.tipFitToWidth": "Paskan sesuai lebar", "PE.Views.ViewTab.tipGridlines": "<PERSON><PERSON><PERSON><PERSON> garis kisi", "PE.Views.ViewTab.tipGuides": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.tipHandTool": "Hand tool", "PE.Views.ViewTab.tipInterfaceTheme": "<PERSON><PERSON> ant<PERSON>", "PE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "PE.Views.ViewTab.tipNormal": "Normal", "PE.Views.ViewTab.tipSelectTool": "Select tool", "PE.Views.ViewTab.tipSlideMaster": "Slide master", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}