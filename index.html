<!DOCTYPE html>
<html>

<head>
    <title>ONLYOFFICE Documents</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=IE8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />

    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-touch-fullscreen" content="yes">

    <style type="text/css">
        html {
            height: 100%;
        }

        body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        #wrap {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
        }

        /* 调试面板样式 */
        #debugPanel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            max-height: 80vh;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            border-radius: 8px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            overflow-y: auto;
            display: none;
        }

        #debugPanel.show {
            display: block;
        }

        #debugPanel .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #333;
        }

        #debugPanel .toggle-btn {
            background: #007acc;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 10px;
        }

        #debugPanel .image-item {
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            border-left: 3px solid #007acc;
        }

        #debugPanel .image-item .path {
            color: #ffd700;
            word-break: break-all;
            margin-bottom: 5px;
        }

        #debugPanel .image-item .url {
            color: #90ee90;
            word-break: break-all;
            font-size: 10px;
            margin-bottom: 5px;
        }

        #debugPanel .image-item .preview {
            text-align: center;
            margin-top: 5px;
        }

        #debugPanel .image-item .preview img {
            max-width: 100%;
            max-height: 80px;
            object-fit: contain;
            border: 1px solid #333;
            border-radius: 2px;
        }

        #debugPanel .image-item .info {
            color: #ccc;
            font-size: 10px;
            margin-top: 5px;
        }

        #debugPanel .clear-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 10px;
            margin-left: 5px;
        }
    </style>
</head>

<body>
    <div id="wrap">
        <div id="file-selector" style="padding: 20px; background: #f5f5f5; border-bottom: 1px solid #ddd;">
            <h3>选择要预览的文档</h3>
            <input type="file" id="fileInput" accept=".docx,.doc,.xlsx,.xls,.pptx,.ppt,.odt,.ods,.odp,.rtf,.txt,.csv" style="margin-right: 10px;">
            <button id="loadFileBtn" style="padding: 8px 16px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">加载文档</button>
            <div id="fileInfo" style="margin-top: 10px; font-size: 14px; color: #666;"></div>
        </div>
        <div id="placeholder"></div>
    </div>

    <!-- 调试面板 -->
    <div id="debugPanel">
        <div class="header">
            <span>图片请求调试</span>
            <div>
                <button id="checkHealthBtn" class="toggle-btn" onclick="window.checkMediaResourcesHealth()" title="检查媒体资源健康状态">检查</button>
                <button id="toggleDebugBtn" class="toggle-btn">隐藏</button>
                <button id="clearDebugBtn" class="clear-btn">清空</button>
            </div>
        </div>
        <div id="debugContent"></div>
    </div>

    <script type="text/javascript" src="./web-apps/apps/api/documents/api.js"></script>
    <script type="text/javascript" src="./web-apps/vendor/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="./x2t.js"></script>
    <script>

        (function () {
            window.APP = {}

            // Url parameters

            var urlParams = getUrlParams(),
                cfg = getEditorConfig(urlParams),
                doc = getDocumentData(urlParams);

            // 全局变量存储当前文档信息
            window.currentDocument = {
                file: null,
                fileName: 'document.docx',
                fileType: 'docx',
                url: null
            };

            // Document Editor
            var docEditor = null;

            // 创建文档编辑器的函数
            function createDocEditor(documentConfig) {
                if (docEditor) {
                    docEditor.destroyEditor();
                }

                docEditor = new DocsAPI.DocEditor('placeholder', {
                    height: '100%',
                    width: '100%',
                    document: {
                        title: documentConfig.title || documentConfig.fileName,
                        url: documentConfig.url,
                        fileType: documentConfig.fileType,
                        permissions: {
                            edit: true,
                            chat: false,
                            protect: false,
                        },
                    },
                editorConfig: {
                    lang: 'zh',
                    customization: {
                        help: false,
                        about: false,
                        hideRightMenu: true,
                        features: {
                            spellcheck: {
                                change: false,
                            },
                        },
                        anonymous: {
                            request: false,
                            label: 'Guest',
                        },
                    },
                },
                events: {
                    onAppReady: async () => {
                        // 全局变量声明
                        window.mediaResources = new Map();
                        window.mediaBlobs = new Map(); // 存储原始 Blob 数据
                        window.dataUrlCache = new Map(); // 缓存 Data URL 转换的 Blob URL
                        window.imageRequestLog = []; // 图片请求日志

                        // 初始化调试面板
                        initDebugPanel();

                        // 初始化 X2T 转换器
                        try {
                            await window.initX2T();
                            console.log('X2T converter initialized successfully');
                        } catch (error) {
                            console.error('Failed to initialize X2T converter:', error);
                        }

                        // loadFileContent - 使用更简单的拦截方法
                        var iframeWindow = docEditor.getIframe().contentWindow
                        if (iframeWindow && iframeWindow.XMLHttpRequest) {
                            // 存储转换后的文档缓存
                            var convertedDocuments = new Map();

                            // 完全替换 XMLHttpRequest 构造函数
                            var OriginalXMLHttpRequest = iframeWindow.XMLHttpRequest;

                            iframeWindow.XMLHttpRequest = function() {
                                var xhr = new OriginalXMLHttpRequest();
                                var originalOpen = xhr.open;
                                var originalSend = xhr.send;
                                var isDocumentRequest = false;
                                var requestUrl = '';

                                xhr.open = function(method, url, async, user, password) {
                                    requestUrl = url;
                                    // 检测文档请求：包括本地 blob URL 和远程文档 URL
                                    isDocumentRequest = method === 'GET' && url && (
                                        url.includes('.docx') || url.includes('.doc') ||
                                        url.includes('.xlsx') || url.includes('.xls') ||
                                        url.includes('.pptx') || url.includes('.ppt') ||
                                        url.startsWith('blob:') // 本地文件的 blob URL
                                    );

                                    if (isDocumentRequest) {
                                        console.log('检测到文档请求:', url);
                                    }

                                    return originalOpen.call(this, method, url, async, user, password);
                                };

                                xhr.send = function(data) {
                                    if (isDocumentRequest) {
                                        console.log('拦截文档请求:', requestUrl);

                                        // 检查缓存
                                        if (convertedDocuments.has(requestUrl)) {
                                            console.log('使用缓存的转换结果');
                                            var cachedResult = convertedDocuments.get(requestUrl);

                                            setTimeout(() => {
                                                Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                                Object.defineProperty(xhr, 'status', { value: 200, configurable: true });
                                                Object.defineProperty(xhr, 'statusText', { value: 'OK', configurable: true });
                                                Object.defineProperty(xhr, 'response', { value: cachedResult, configurable: true });
                                                Object.defineProperty(xhr, 'responseType', { value: 'arraybuffer', configurable: true });

                                                xhr.dispatchEvent(new Event('readystatechange'));
                                                xhr.dispatchEvent(new Event('load'));
                                            }, 10);

                                            return;
                                        }

                                        // 创建新的请求来获取原始文档
                                        var originalXhr = new OriginalXMLHttpRequest();
                                        originalXhr.open('GET', requestUrl, true);
                                        originalXhr.responseType = 'arraybuffer';

                                        originalXhr.onload = async function() {
                                            if (originalXhr.status === 200) {
                                                try {
                                                    // 优先使用当前文档信息，否则从 URL 解析
                                                    var fileName, fileExtension, mimeType;

                                                    if (window.currentDocument && window.currentDocument.fileName) {
                                                        fileName = window.currentDocument.fileName;
                                                        fileExtension = window.currentDocument.fileType;
                                                    } else {
                                                        fileName = requestUrl.split('/').pop() || 'document.docx';
                                                        // 处理 blob URL 的情况
                                                        if (requestUrl.startsWith('blob:')) {
                                                            fileName = window.currentDocument ? window.currentDocument.fileName : 'document.docx';
                                                        }
                                                        fileExtension = fileName.split('.').pop().toLowerCase();
                                                    }

                                                    mimeType = window.x2tConverter.getMimeTypeFromExtension(fileExtension);
                                                    var file = new File([originalXhr.response], fileName, {
                                                        type: mimeType
                                                    });

                                                    console.log('开始 X2T 转换:', fileName);
                                                    var conversionResult = await window.convertDocument(file);
                                                    debugger
                                                    console.log('X2T 转换完成，bin 大小:', conversionResult.bin.length);
                                                    console.log('媒体文件数量:', Object.keys(conversionResult.media).length);

                                                    // 缓存转换结果
                                                    convertedDocuments.set(requestUrl, conversionResult.bin.buffer);

                                                    // 存储媒体资源到全局变量
                                                    Object.keys(conversionResult.media).forEach(function(mediaPath) {
                                                        var mediaUrl = conversionResult.media[mediaPath];
                                                        window.mediaResources.set(mediaPath, mediaUrl);
                                                        console.log('存储媒体资源:', mediaPath, '->', mediaUrl);

                                                        // 如果是 Blob URL，获取并存储原始数据
                                                        if (mediaUrl.startsWith('blob:')) {
                                                            fetch(mediaUrl)
                                                                .then(response => response.blob())
                                                                .then(blob => {
                                                                    window.mediaBlobs.set(mediaPath, blob);
                                                                    console.log('存储媒体 Blob 数据:', mediaPath, blob.size, 'bytes');
                                                                })
                                                                .catch(error => {
                                                                    console.warn('获取媒体 Blob 数据失败:', mediaPath, error);
                                                                });
                                                        }
                                                    });

                                                    // 设置转换后的响应
                                                    Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                                    Object.defineProperty(xhr, 'status', { value: 200, configurable: true });
                                                    Object.defineProperty(xhr, 'statusText', { value: 'OK', configurable: true });
                                                    Object.defineProperty(xhr, 'response', { value: conversionResult.bin.buffer, configurable: true });
                                                    Object.defineProperty(xhr, 'responseType', { value: 'arraybuffer', configurable: true });

                                                    xhr.dispatchEvent(new Event('readystatechange'));
                                                    xhr.dispatchEvent(new Event('load'));

                                                } catch (error) {
                                                    console.error('文档转换失败:', error);
                                                    // 转换失败时返回原始数据
                                                    Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                                    Object.defineProperty(xhr, 'status', { value: 200, configurable: true });
                                                    Object.defineProperty(xhr, 'statusText', { value: 'OK', configurable: true });
                                                    Object.defineProperty(xhr, 'response', { value: originalXhr.response, configurable: true });
                                                    Object.defineProperty(xhr, 'responseType', { value: 'arraybuffer', configurable: true });

                                                    xhr.dispatchEvent(new Event('readystatechange'));
                                                    xhr.dispatchEvent(new Event('load'));
                                                }
                                            } else {
                                                // 请求失败
                                                Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                                Object.defineProperty(xhr, 'status', { value: originalXhr.status, configurable: true });
                                                Object.defineProperty(xhr, 'statusText', { value: originalXhr.statusText, configurable: true });

                                                xhr.dispatchEvent(new Event('readystatechange'));
                                                xhr.dispatchEvent(new Event('error'));
                                            }
                                        };

                                        originalXhr.onerror = function() {
                                            Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                            Object.defineProperty(xhr, 'status', { value: 0, configurable: true });
                                            Object.defineProperty(xhr, 'statusText', { value: '', configurable: true });

                                            xhr.dispatchEvent(new Event('readystatechange'));
                                            xhr.dispatchEvent(new Event('error'));
                                        };

                                        originalXhr.send();
                                        return;
                                    }

                                    // 非文档请求，正常处理
                                    return originalSend.call(this, data);
                                };

                                return xhr;
                            };

                            // 复制原始构造函数的属性
                            Object.setPrototypeOf(iframeWindow.XMLHttpRequest.prototype, OriginalXMLHttpRequest.prototype);
                            Object.setPrototypeOf(iframeWindow.XMLHttpRequest, OriginalXMLHttpRequest);
                        }

                        docEditor.installLegacyChannel()
                        docEditor.connectMockServer({
                            getParticipants: () => {
                                return {

                                }
                            },
                            onMessage: (message) => {
                                // console.log('message:', message)
                            },
                            getImageURL: (imagePath, callback) => {
                                console.log('getImageURL 请求:', imagePath);
                                debugger

                                // 添加到调试日志
                                const requestInfo = {
                                    timestamp: new Date().toLocaleTimeString(),
                                    path: imagePath,
                                    type: getUrlType(imagePath),
                                    resolvedUrl: null,
                                    source: 'unknown'
                                };

                                return new Promise((resolve, reject) => {
                                    // 1. 检查是否是 Data URL (base64 编码的图片)
                                    if (imagePath && imagePath.startsWith('data:')) {
                                        console.log('检测到 Data URL，转换为 Blob URL:', imagePath.substring(0, 50) + '...');

                                        // 检查缓存
                                        if (window.dataUrlCache && window.dataUrlCache.has(imagePath)) {
                                            const cachedBlobUrl = window.dataUrlCache.get(imagePath);
                                            console.log('使用缓存的 Blob URL:', cachedBlobUrl);
                                            resolve(cachedBlobUrl);
                                            if (callback) callback(cachedBlobUrl);
                                            return;
                                        }

                                        try {
                                            // 将 Data URL 转换为 Blob
                                            fetch(imagePath)
                                                .then(res => res.blob())
                                                .then(blob => {
                                                    debugger
                                                    const blobUrl = URL.createObjectURL(blob);
                                                    console.log('Data URL 转换为 Blob URL 成功:', blobUrl);

                                                    // 缓存转换结果
                                                    if (window.dataUrlCache) {
                                                        window.dataUrlCache.set(imagePath, blobUrl);
                                                    }

                                                    requestInfo.resolvedUrl = blobUrl;
                                                    addToDebugLog(requestInfo);
                                                    resolve(blobUrl);
                                                    if (callback) callback(blobUrl);
                                                })
                                                .catch(error => {
                                                    console.warn('Data URL 转换失败，返回原始 URL:', error);
                                                    requestInfo.resolvedUrl = imagePath;
                                                    requestInfo.source = 'Data URL (转换失败)';
                                                    addToDebugLog(requestInfo);
                                                    resolve(imagePath);
                                                    if (callback) callback(imagePath);
                                                });
                                        } catch (error) {
                                            console.warn('Data URL 处理失败，返回原始 URL:', error);
                                            requestInfo.resolvedUrl = imagePath;
                                            requestInfo.source = 'Data URL (处理失败)';
                                            addToDebugLog(requestInfo);
                                            resolve(imagePath);
                                            if (callback) callback(imagePath);
                                        }
                                        return;
                                    }

                                    // 2. 检查是否是完整的 HTTP/HTTPS URL 或 blob URL
                                    if (imagePath && (imagePath.startsWith('http://') || imagePath.startsWith('https://') || imagePath.startsWith('blob:'))) {
                                        console.log('检测到完整 URL，直接返回:', imagePath);
                                        requestInfo.resolvedUrl = imagePath;
                                        requestInfo.source = '完整 URL';
                                        addToDebugLog(requestInfo);
                                        resolve(imagePath);
                                        if (callback) callback(imagePath);
                                        return;
                                    }

                                    // 3. 检查媒体资源中是否有对应的图片
                                    if (window.mediaResources && window.mediaResources.has(imagePath)) {
                                        const imageUrl = window.mediaResources.get(imagePath);
                                        console.log('找到媒体资源:', imagePath, '->', imageUrl);
                                        requestInfo.resolvedUrl = imageUrl;
                                        requestInfo.source = '媒体资源 (精确匹配)';
                                        addToDebugLog(requestInfo);
                                        resolve(imageUrl);
                                        if (callback) callback(imageUrl);
                                    } else {
                                        // 4. 尝试匹配部分路径
                                        let foundUrl = null;
                                        if (window.mediaResources) {
                                            for (let [storedPath, url] of window.mediaResources) {
                                                // 尝试多种匹配方式
                                                if (storedPath.includes(imagePath) ||
                                                    imagePath.includes(storedPath.replace('media/', '')) ||
                                                    storedPath.endsWith(imagePath) ||
                                                    imagePath.endsWith(storedPath.split('/').pop())) {
                                                    foundUrl = url;
                                                    console.log('部分匹配媒体资源:', imagePath, '->', storedPath, '->', url);
                                                    requestInfo.source = `媒体资源 (部分匹配: ${storedPath})`;
                                                    break;
                                                }
                                            }
                                        }

                                        if (foundUrl) {
                                            // 验证找到的 URL 是否有效
                                            if (foundUrl.startsWith('blob:')) {
                                                // 验证 Blob URL 是否有效
                                                fetch(foundUrl, { method: 'HEAD' })
                                                    .then(response => {
                                                        if (response.ok) {
                                                            requestInfo.resolvedUrl = foundUrl;
                                                            requestInfo.source += ' (已验证)';
                                                            addToDebugLog(requestInfo);
                                                            resolve(foundUrl);
                                                            if (callback) callback(foundUrl);
                                                        } else {
                                                            console.warn('Blob URL 无效，尝试重新创建:', foundUrl);
                                                            // Blob URL 无效，尝试从原始数据重新创建
                                                            recreateBlobUrl(imagePath, requestInfo, resolve, callback);
                                                        }
                                                    })
                                                    .catch(error => {
                                                        console.warn('Blob URL 验证失败，尝试重新创建:', error);
                                                        recreateBlobUrl(imagePath, requestInfo, resolve, callback);
                                                    });
                                            } else {
                                                requestInfo.resolvedUrl = foundUrl;
                                                addToDebugLog(requestInfo);
                                                resolve(foundUrl);
                                                if (callback) callback(foundUrl);
                                            }
                                        } else {
                                            console.warn('未找到媒体资源:', imagePath);
                                            if (window.mediaResources) {
                                                console.log('可用的媒体资源:', Array.from(window.mediaResources.keys()));
                                            }

                                            // 5. 返回原始路径作为后备
                                            console.log('返回原始路径作为后备:', imagePath);
                                            requestInfo.resolvedUrl = imagePath;
                                            requestInfo.source = '后备路径 (未找到匹配)';
                                            addToDebugLog(requestInfo);
                                            resolve(imagePath);
                                            if (callback) callback(imagePath);
                                        }
                                    }
                                });
                            }
                        })
                    },
                    onDocumentContentReady: () => {
                        console.log('文档加载完成:', window.currentDocument.fileName)
                    },
                },
                });

                return docEditor;
            }

            // 文件加载处理函数
            function handleFileLoad() {
                const fileInput = document.getElementById('fileInput');
                const file = fileInput.files[0];

                if (!file) {
                    alert('请选择一个文件');
                    return;
                }

                // 获取文件信息
                const fileName = file.name;
                const fileExtension = fileName.split('.').pop().toLowerCase();
                const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB

                // 检查文件类型是否支持
                const supportedTypes = ['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'odt', 'ods', 'odp', 'rtf', 'txt', 'csv'];
                if (!supportedTypes.includes(fileExtension)) {
                    alert(`不支持的文件类型: ${fileExtension}`);
                    return;
                }

                // 创建文件 URL
                const fileUrl = URL.createObjectURL(file);

                // 更新全局文档信息
                window.currentDocument = {
                    file: file,
                    fileName: fileName,
                    fileType: fileExtension,
                    url: fileUrl
                };

                // 更新文件信息显示
                document.getElementById('fileInfo').innerHTML = `
                    <strong>已选择文件:</strong> ${fileName} (${fileSize} MB)<br>
                    <strong>文件类型:</strong> ${fileExtension.toUpperCase()}
                `;

                // 创建文档编辑器
                createDocEditor(window.currentDocument);

                console.log('加载文档:', {
                    fileName: fileName,
                    fileType: fileExtension,
                    fileSize: fileSize + ' MB',
                    url: fileUrl
                });
            }

            // 绑定文件加载按钮事件
            document.getElementById('loadFileBtn').addEventListener('click', handleFileLoad);

            // 支持文件拖拽
            const fileSelector = document.getElementById('file-selector');
            fileSelector.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileSelector.style.backgroundColor = '#e8f5e8';
            });

            fileSelector.addEventListener('dragleave', (e) => {
                e.preventDefault();
                fileSelector.style.backgroundColor = '#f5f5f5';
            });

            fileSelector.addEventListener('drop', (e) => {
                e.preventDefault();
                fileSelector.style.backgroundColor = '#f5f5f5';

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    document.getElementById('fileInput').files = files;
                    handleFileLoad();
                }
            });

            // 清理 Blob URL 的函数
            window.cleanupBlobUrls = function() {
                console.log('清理 Blob URLs...');

                // 清理媒体资源中的 Blob URLs
                if (window.mediaResources) {
                    for (let [path, url] of window.mediaResources) {
                        if (url.startsWith('blob:')) {
                            URL.revokeObjectURL(url);
                            console.log('释放媒体资源 Blob URL:', path);
                        }
                    }
                    window.mediaResources.clear();
                }

                // 清理 Data URL 缓存中的 Blob URLs
                if (window.dataUrlCache) {
                    for (let [dataUrl, blobUrl] of window.dataUrlCache) {
                        if (blobUrl.startsWith('blob:')) {
                            URL.revokeObjectURL(blobUrl);
                            console.log('释放 Data URL 缓存 Blob URL');
                        }
                    }
                    window.dataUrlCache.clear();
                }

                // 清理当前文档的 URL
                if (window.currentDocument && window.currentDocument.url && window.currentDocument.url.startsWith('blob:')) {
                    URL.revokeObjectURL(window.currentDocument.url);
                    console.log('释放当前文档 Blob URL');
                }
            };

            // 检查媒体资源健康状态
            window.checkMediaResourcesHealth = async function() {
                console.log('检查媒体资源健康状态...');

                if (!window.mediaResources || window.mediaResources.size === 0) {
                    console.log('没有媒体资源需要检查');
                    return;
                }

                const results = {
                    total: window.mediaResources.size,
                    healthy: 0,
                    unhealthy: 0,
                    details: []
                };

                for (let [path, url] of window.mediaResources) {
                    try {
                        if (url.startsWith('blob:')) {
                            const response = await fetch(url, { method: 'HEAD' });
                            if (response.ok) {
                                results.healthy++;
                                results.details.push({ path, url, status: 'healthy', size: response.headers.get('content-length') });
                                console.log('✓ 健康:', path);
                            } else {
                                results.unhealthy++;
                                results.details.push({ path, url, status: 'unhealthy', error: `HTTP ${response.status}` });
                                console.warn('✗ 不健康:', path, response.status);
                            }
                        } else {
                            results.healthy++;
                            results.details.push({ path, url, status: 'non-blob', type: getUrlType(url) });
                            console.log('✓ 非 Blob URL:', path);
                        }
                    } catch (error) {
                        results.unhealthy++;
                        results.details.push({ path, url, status: 'error', error: error.message });
                        console.error('✗ 检查失败:', path, error.message);
                    }
                }

                console.log('媒体资源健康检查完成:', results);
                return results;
            };

            // 页面卸载时清理资源
            window.addEventListener('beforeunload', window.cleanupBlobUrls);

            // 调试面板相关函数
            function initDebugPanel() {
                const debugPanel = document.getElementById('debugPanel');
                const toggleBtn = document.getElementById('toggleDebugBtn');
                const clearBtn = document.getElementById('clearDebugBtn');

                // 默认显示调试面板
                debugPanel.classList.add('show');

                // 切换显示/隐藏
                toggleBtn.addEventListener('click', function() {
                    if (debugPanel.classList.contains('show')) {
                        debugPanel.classList.remove('show');
                        toggleBtn.textContent = '显示';
                    } else {
                        debugPanel.classList.add('show');
                        toggleBtn.textContent = '隐藏';
                    }
                });

                // 清空调试日志
                clearBtn.addEventListener('click', function() {
                    window.imageRequestLog = [];
                    updateDebugPanel();
                });
            }

            function getUrlType(url) {
                if (!url) return 'empty';
                if (url.startsWith('data:')) return 'Data URL';
                if (url.startsWith('blob:')) return 'Blob URL';
                if (url.startsWith('http://') || url.startsWith('https://')) return 'HTTP URL';
                if (url.startsWith('/')) return 'Absolute Path';
                return 'Relative Path';
            }

            // 重新创建 Blob URL 的函数
            function recreateBlobUrl(imagePath, requestInfo, resolve, callback) {
                console.log('尝试重新创建 Blob URL for:', imagePath);

                // 查找匹配的媒体路径
                let matchedPath = null;

                if (window.mediaResources) {
                    for (let [storedPath, url] of window.mediaResources) {
                        if (storedPath.includes(imagePath) ||
                            imagePath.includes(storedPath.replace('media/', '')) ||
                            storedPath.endsWith(imagePath) ||
                            imagePath.endsWith(storedPath.split('/').pop())) {
                            matchedPath = storedPath;
                            break;
                        }
                    }
                }

                if (matchedPath && window.mediaBlobs && window.mediaBlobs.has(matchedPath)) {
                    // 从存储的 Blob 数据重新创建 URL
                    console.log('找到原始 Blob 数据，重新创建 URL:', matchedPath);

                    const originalBlob = window.mediaBlobs.get(matchedPath);
                    const newBlobUrl = URL.createObjectURL(originalBlob);

                    // 更新媒体资源映射
                    window.mediaResources.set(matchedPath, newBlobUrl);

                    console.log('成功重新创建 Blob URL:', newBlobUrl);
                    requestInfo.resolvedUrl = newBlobUrl;
                    requestInfo.source += ' (重新创建成功)';
                    addToDebugLog(requestInfo);
                    resolve(newBlobUrl);
                    if (callback) callback(newBlobUrl);

                } else if (matchedPath) {
                    console.warn('找到匹配路径但无原始 Blob 数据:', matchedPath);
                    requestInfo.resolvedUrl = imagePath;
                    requestInfo.source = '重新创建失败 (无原始数据)';
                    addToDebugLog(requestInfo);
                    resolve(imagePath);
                    if (callback) callback(imagePath);

                } else {
                    console.warn('无法找到匹配的媒体路径:', imagePath);
                    console.log('可用的媒体路径:', Array.from(window.mediaResources.keys()));
                    requestInfo.resolvedUrl = imagePath;
                    requestInfo.source = '重新创建失败 (未找到匹配)';
                    addToDebugLog(requestInfo);
                    resolve(imagePath);
                    if (callback) callback(imagePath);
                }
            }

            function addToDebugLog(requestInfo) {
                window.imageRequestLog.push(requestInfo);
                // 限制日志数量，避免内存过多占用
                if (window.imageRequestLog.length > 50) {
                    window.imageRequestLog.shift();
                }
                updateDebugPanel();
            }

            function updateDebugPanel() {
                const debugContent = document.getElementById('debugContent');
                if (!debugContent) return;

                debugContent.innerHTML = '';

                window.imageRequestLog.slice().reverse().forEach((item, index) => {
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'image-item';

                    const pathDiv = document.createElement('div');
                    pathDiv.className = 'path';
                    pathDiv.textContent = `路径: ${item.path.length > 50 ? item.path.substring(0, 50) + '...' : item.path}`;

                    const urlDiv = document.createElement('div');
                    urlDiv.className = 'url';
                    urlDiv.textContent = `解析: ${item.resolvedUrl || '处理中...'}`;

                    const infoDiv = document.createElement('div');
                    infoDiv.className = 'info';
                    infoDiv.textContent = `时间: ${item.timestamp} | 类型: ${item.type} | 来源: ${item.source}`;

                    itemDiv.appendChild(pathDiv);
                    itemDiv.appendChild(urlDiv);
                    itemDiv.appendChild(infoDiv);

                    // 如果有解析后的 URL，尝试显示图片预览
                    if (item.resolvedUrl && (item.resolvedUrl.startsWith('blob:') || item.resolvedUrl.startsWith('data:') || item.resolvedUrl.startsWith('http'))) {
                        const previewDiv = document.createElement('div');
                        previewDiv.className = 'preview';

                        const img = document.createElement('img');
                        img.src = item.resolvedUrl;

                        // 添加加载状态指示
                        const loadingSpan = document.createElement('span');
                        loadingSpan.textContent = '加载中...';
                        loadingSpan.style.color = '#ffd700';
                        previewDiv.appendChild(loadingSpan);

                        img.onerror = function() {
                            previewDiv.innerHTML = '<span style="color: #ff6b6b;">图片加载失败</span>';

                            // 如果是 Blob URL，尝试验证其有效性
                            if (item.resolvedUrl.startsWith('blob:')) {
                                fetch(item.resolvedUrl, { method: 'HEAD' })
                                    .then(response => {
                                        if (!response.ok) {
                                            previewDiv.innerHTML += '<br><span style="color: #ff6b6b; font-size: 9px;">Blob URL 已失效</span>';
                                        } else {
                                            previewDiv.innerHTML += '<br><span style="color: #ffd700; font-size: 9px;">Blob URL 有效，但图片格式可能不支持</span>';
                                        }
                                    })
                                    .catch(error => {
                                        previewDiv.innerHTML += '<br><span style="color: #ff6b6b; font-size: 9px;">Blob URL 访问失败</span>';
                                    });
                            }
                        };

                        img.onload = function() {
                            previewDiv.removeChild(loadingSpan);
                            previewDiv.appendChild(img);

                            const sizeInfo = document.createElement('div');
                            sizeInfo.style.fontSize = '9px';
                            sizeInfo.style.color = '#aaa';
                            sizeInfo.textContent = `${img.naturalWidth}x${img.naturalHeight}`;
                            previewDiv.appendChild(sizeInfo);
                        };

                        itemDiv.appendChild(previewDiv);
                    }

                    debugContent.appendChild(itemDiv);
                });
            }

            // 初始化时创建一个默认的编辑器 (可选)
            // createDocEditor({
            //     title: '请选择文档',
            //     fileName: 'empty.docx',
            //     fileType: 'docx',
            //     url: 'about:blank'
            // });




            // helpers

            function getUrlParams() {
                var e,
                    a = /\+/g,  // Regex for replacing addition symbol with a space
                    r = /([^&=]+)=?([^&]*)/g,
                    d = function (s) { return decodeURIComponent(s.replace(a, " ")); },
                    q = window.location.search.substring(1),
                    urlParams = {};

                while (e = r.exec(q))
                    urlParams[d(e[1])] = d(e[2]);

                return urlParams;
            }

            function getDocumentData(urlParams) {
                let docparams = {
                    title: 'xxx',
                    fileType: 'docx',
                    permissions: {
                        edit: true,
                        download: true
                    }
                };

                if (urlParams['mode'] == 'review')
                    docparams.permissions.edit = !(docparams.permissions.review = true);

                if (urlParams['isForm'] !== undefined)
                    docparams.isForm = (urlParams['isForm'] === 'true');

                return docparams;
            }

            function getEditorConfig(urlParams) {
                return {
                    customization: {
                        goback: { url: "onlyoffice.com" }
                        , feedback: {
                            url: "https://helpdesk.onlyoffice.com/?desktop=true"
                        }
                        , uiTheme: urlParams["uitheme"]
                    },
                    mode: urlParams["mode"] || 'edit',
                    lang: urlParams["lang"] || 'en',
                    createUrl: 'desktop://create.new',
                    user: {
                        id: urlParams["userid"] || urlParams["username"] || 'uid-901', name: urlParams["username"] || 'Chuk.Gek'
                    }
                };
            }

            // Mobile version

            function isMobile() {
                var prefixes = {
                    ios: 'i(?:Pad|Phone|Pod)(?:.*)CPU(?: iPhone)? OS ',
                    android: '(Android |HTC_|Silk/)',
                    blackberry: 'BlackBerry(?:.*)Version\/',
                    rimTablet: 'RIM Tablet OS ',
                    webos: '(?:webOS|hpwOS)\/',
                    bada: 'Bada\/'
                },
                    i, prefix, match;

                for (i in prefixes) {
                    if (prefixes.hasOwnProperty(i)) {
                        prefix = prefixes[i];

                        if (navigator.userAgent.match(new RegExp('(?:' + prefix + ')([^\\s;]+)')))
                            return true;
                    }
                }

                return false;
            }

            var fixSize = function () {
                var wrapEl = document.getElementById('wrap');
                if (wrapEl) {
                    wrapEl.style.height = screen.availHeight + 'px';
                    window.scrollTo(0, -1);
                    wrapEl.style.height = window.innerHeight + 'px';
                }
            };

            var fixIpadLandscapeIos7 = function () {
                if (navigator.userAgent.match(/iPad;.*CPU.*OS 7_\d/i)) {
                    var wrapEl = document.getElementById('wrap');
                    if (wrapEl) {
                        wrapEl.style.position = "fixed";
                        wrapEl.style.bottom = 0;
                        wrapEl.style.width = "100%";
                    }
                }
            };

            function onInternalMessage(event) {
                let info = event.data;
                if (info.type == 'goback') {
                    if (window.AscDesktopEditor) {
                        window.AscDesktopEditor.execCommand('go:folder', info.data.status);
                    }
                }
            };

            function onDocumentReady() {
                if (window.AscDesktopEditor) {
                    window.AscDesktopEditor.execCommand('doc:onready', '');
                }
            }

            if (isMobile()) {
                window.addEventListener('load', fixSize);
                window.addEventListener('resize', fixSize);

                fixIpadLandscapeIos7();
            }

        })();
    </script>
</body>

</html>