<!DOCTYPE html>
<html>

<head>
    <title>ONLYOFFICE Documents</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=IE8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />

    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-touch-fullscreen" content="yes">

    <style type="text/css">
        html {
            height: 100%;
        }

        body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        #wrap {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
        }
    </style>
</head>

<body>
    <div id="wrap">
        <div id="file-selector" style="padding: 20px; background: #f5f5f5; border-bottom: 1px solid #ddd;">
            <h3>选择要预览的文档</h3>
            <input type="file" id="fileInput" accept=".docx,.doc,.xlsx,.xls,.pptx,.ppt,.odt,.ods,.odp,.rtf,.txt,.csv" style="margin-right: 10px;">
            <button id="loadFileBtn" style="padding: 8px 16px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">加载文档</button>
            <div id="fileInfo" style="margin-top: 10px; font-size: 14px; color: #666;"></div>
        </div>
        <div id="placeholder"></div>
    </div>

    <script type="text/javascript" src="./web-apps/apps/api/documents/api.js"></script>
    <script type="text/javascript" src="./web-apps/vendor/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="./x2t.js"></script>
    <script>

        (function () {
            window.APP = {}

            // Url parameters

            var urlParams = getUrlParams(),
                cfg = getEditorConfig(urlParams),
                doc = getDocumentData(urlParams);

            // 全局变量存储当前文档信息
            window.currentDocument = {
                file: null,
                fileName: 'document.docx',
                fileType: 'docx',
                url: null
            };

            // Document Editor
            var docEditor = null;

            // 创建文档编辑器的函数
            function createDocEditor(documentConfig) {
                if (docEditor) {
                    docEditor.destroyEditor();
                }

                docEditor = new DocsAPI.DocEditor('placeholder', {
                    height: '100%',
                    width: '100%',
                    document: {
                        title: documentConfig.title || documentConfig.fileName,
                        url: documentConfig.url,
                        fileType: documentConfig.fileType,
                        permissions: {
                            edit: true,
                            chat: false,
                            protect: false,
                        },
                    },
                editorConfig: {
                    lang: 'zh',
                    customization: {
                        help: false,
                        about: false,
                        hideRightMenu: true,
                        features: {
                            spellcheck: {
                                change: false,
                            },
                        },
                        anonymous: {
                            request: false,
                            label: 'Guest',
                        },
                    },
                },
                events: {
                    onAppReady: async () => {
                        // 全局变量声明
                        window.mediaResources = new Map();
                        window.dataUrlCache = new Map(); // 缓存 Data URL 转换的 Blob URL

                        // 初始化 X2T 转换器
                        try {
                            await window.initX2T();
                            console.log('X2T converter initialized successfully');
                        } catch (error) {
                            console.error('Failed to initialize X2T converter:', error);
                        }

                        // loadFileContent - 使用更简单的拦截方法
                        var iframeWindow = docEditor.getIframe().contentWindow
                        if (iframeWindow && iframeWindow.XMLHttpRequest) {
                            // 存储转换后的文档缓存
                            var convertedDocuments = new Map();

                            // 完全替换 XMLHttpRequest 构造函数
                            var OriginalXMLHttpRequest = iframeWindow.XMLHttpRequest;

                            iframeWindow.XMLHttpRequest = function() {
                                var xhr = new OriginalXMLHttpRequest();
                                var originalOpen = xhr.open;
                                var originalSend = xhr.send;
                                var isDocumentRequest = false;
                                var requestUrl = '';

                                xhr.open = function(method, url, async, user, password) {
                                    requestUrl = url;
                                    // 检测文档请求：包括本地 blob URL 和远程文档 URL
                                    isDocumentRequest = method === 'GET' && url && (
                                        url.includes('.docx') || url.includes('.doc') ||
                                        url.includes('.xlsx') || url.includes('.xls') ||
                                        url.includes('.pptx') || url.includes('.ppt') ||
                                        url.startsWith('blob:') // 本地文件的 blob URL
                                    );

                                    if (isDocumentRequest) {
                                        console.log('检测到文档请求:', url);
                                    }

                                    return originalOpen.call(this, method, url, async, user, password);
                                };

                                xhr.send = function(data) {
                                    if (isDocumentRequest) {
                                        console.log('拦截文档请求:', requestUrl);

                                        // 检查缓存
                                        if (convertedDocuments.has(requestUrl)) {
                                            console.log('使用缓存的转换结果');
                                            var cachedResult = convertedDocuments.get(requestUrl);

                                            setTimeout(() => {
                                                Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                                Object.defineProperty(xhr, 'status', { value: 200, configurable: true });
                                                Object.defineProperty(xhr, 'statusText', { value: 'OK', configurable: true });
                                                Object.defineProperty(xhr, 'response', { value: cachedResult, configurable: true });
                                                Object.defineProperty(xhr, 'responseType', { value: 'arraybuffer', configurable: true });

                                                xhr.dispatchEvent(new Event('readystatechange'));
                                                xhr.dispatchEvent(new Event('load'));
                                            }, 10);

                                            return;
                                        }

                                        // 创建新的请求来获取原始文档
                                        var originalXhr = new OriginalXMLHttpRequest();
                                        originalXhr.open('GET', requestUrl, true);
                                        originalXhr.responseType = 'arraybuffer';

                                        originalXhr.onload = async function() {
                                            if (originalXhr.status === 200) {
                                                try {
                                                    // 优先使用当前文档信息，否则从 URL 解析
                                                    var fileName, fileExtension, mimeType;

                                                    if (window.currentDocument && window.currentDocument.fileName) {
                                                        fileName = window.currentDocument.fileName;
                                                        fileExtension = window.currentDocument.fileType;
                                                    } else {
                                                        fileName = requestUrl.split('/').pop() || 'document.docx';
                                                        // 处理 blob URL 的情况
                                                        if (requestUrl.startsWith('blob:')) {
                                                            fileName = window.currentDocument ? window.currentDocument.fileName : 'document.docx';
                                                        }
                                                        fileExtension = fileName.split('.').pop().toLowerCase();
                                                    }

                                                    mimeType = window.x2tConverter.getMimeTypeFromExtension(fileExtension);
                                                    var file = new File([originalXhr.response], fileName, {
                                                        type: mimeType
                                                    });

                                                    console.log('开始 X2T 转换:', fileName);
                                                    var conversionResult = await window.convertDocument(file);
                                                    debugger
                                                    console.log('X2T 转换完成，bin 大小:', conversionResult.bin.length);
                                                    console.log('媒体文件数量:', Object.keys(conversionResult.media).length);

                                                    // 缓存转换结果
                                                    convertedDocuments.set(requestUrl, conversionResult.bin.buffer);

                                                    // 存储媒体资源到全局变量
                                                    Object.keys(conversionResult.media).forEach(function(mediaPath) {
                                                        var mediaUrl = conversionResult.media[mediaPath];
                                                        window.mediaResources.set(mediaPath, mediaUrl);
                                                        console.log('存储媒体资源:', mediaPath, '->', mediaUrl);
                                                    });

                                                    // 设置转换后的响应
                                                    Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                                    Object.defineProperty(xhr, 'status', { value: 200, configurable: true });
                                                    Object.defineProperty(xhr, 'statusText', { value: 'OK', configurable: true });
                                                    Object.defineProperty(xhr, 'response', { value: conversionResult.bin.buffer, configurable: true });
                                                    Object.defineProperty(xhr, 'responseType', { value: 'arraybuffer', configurable: true });

                                                    xhr.dispatchEvent(new Event('readystatechange'));
                                                    xhr.dispatchEvent(new Event('load'));

                                                } catch (error) {
                                                    console.error('文档转换失败:', error);
                                                    // 转换失败时返回原始数据
                                                    Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                                    Object.defineProperty(xhr, 'status', { value: 200, configurable: true });
                                                    Object.defineProperty(xhr, 'statusText', { value: 'OK', configurable: true });
                                                    Object.defineProperty(xhr, 'response', { value: originalXhr.response, configurable: true });
                                                    Object.defineProperty(xhr, 'responseType', { value: 'arraybuffer', configurable: true });

                                                    xhr.dispatchEvent(new Event('readystatechange'));
                                                    xhr.dispatchEvent(new Event('load'));
                                                }
                                            } else {
                                                // 请求失败
                                                Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                                Object.defineProperty(xhr, 'status', { value: originalXhr.status, configurable: true });
                                                Object.defineProperty(xhr, 'statusText', { value: originalXhr.statusText, configurable: true });

                                                xhr.dispatchEvent(new Event('readystatechange'));
                                                xhr.dispatchEvent(new Event('error'));
                                            }
                                        };

                                        originalXhr.onerror = function() {
                                            Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                            Object.defineProperty(xhr, 'status', { value: 0, configurable: true });
                                            Object.defineProperty(xhr, 'statusText', { value: '', configurable: true });

                                            xhr.dispatchEvent(new Event('readystatechange'));
                                            xhr.dispatchEvent(new Event('error'));
                                        };

                                        originalXhr.send();
                                        return;
                                    }

                                    // 非文档请求，正常处理
                                    return originalSend.call(this, data);
                                };

                                return xhr;
                            };

                            // 复制原始构造函数的属性
                            Object.setPrototypeOf(iframeWindow.XMLHttpRequest.prototype, OriginalXMLHttpRequest.prototype);
                            Object.setPrototypeOf(iframeWindow.XMLHttpRequest, OriginalXMLHttpRequest);
                        }

                        docEditor.installLegacyChannel()
                        docEditor.connectMockServer({
                            getParticipants: () => {
                                return {

                                }
                            },
                            onMessage: (message) => {
                                // console.log('message:', message)
                            },
                            getImageURL: (imagePath, callback) => {
                                console.log('getImageURL 请求:', imagePath);
                                debugger

                                return new Promise((resolve, reject) => {
                                    // 1. 检查是否是 Data URL (base64 编码的图片)
                                    if (imagePath && imagePath.startsWith('data:')) {
                                        console.log('检测到 Data URL，转换为 Blob URL:', imagePath.substring(0, 50) + '...');

                                        // 检查缓存
                                        if (window.dataUrlCache && window.dataUrlCache.has(imagePath)) {
                                            const cachedBlobUrl = window.dataUrlCache.get(imagePath);
                                            console.log('使用缓存的 Blob URL:', cachedBlobUrl);
                                            resolve(cachedBlobUrl);
                                            if (callback) callback(cachedBlobUrl);
                                            return;
                                        }

                                        try {
                                            // 将 Data URL 转换为 Blob
                                            fetch(imagePath)
                                                .then(res => res.blob())
                                                .then(blob => {
                                                    debugger
                                                    const blobUrl = URL.createObjectURL(blob);
                                                    console.log('Data URL 转换为 Blob URL 成功:', blobUrl);

                                                    // 缓存转换结果
                                                    if (window.dataUrlCache) {
                                                        window.dataUrlCache.set(imagePath, blobUrl);
                                                    }

                                                    resolve(blobUrl);
                                                    if (callback) callback(blobUrl);
                                                })
                                                .catch(error => {
                                                    console.warn('Data URL 转换失败，返回原始 URL:', error);
                                                    resolve(imagePath);
                                                    if (callback) callback(imagePath);
                                                });
                                        } catch (error) {
                                            console.warn('Data URL 处理失败，返回原始 URL:', error);
                                            resolve(imagePath);
                                            if (callback) callback(imagePath);
                                        }
                                        return;
                                    }

                                    // 2. 检查是否是完整的 HTTP/HTTPS URL 或 blob URL
                                    if (imagePath && (imagePath.startsWith('http://') || imagePath.startsWith('https://') || imagePath.startsWith('blob:'))) {
                                        console.log('检测到完整 URL，直接返回:', imagePath);
                                        resolve(imagePath);
                                        if (callback) callback(imagePath);
                                        return;
                                    }

                                    // 3. 检查媒体资源中是否有对应的图片
                                    if (window.mediaResources && window.mediaResources.has(imagePath)) {
                                        const imageUrl = window.mediaResources.get(imagePath);
                                        console.log('找到媒体资源:', imagePath, '->', imageUrl);
                                        resolve(imageUrl);
                                        if (callback) callback(imageUrl);
                                    } else {
                                        // 4. 尝试匹配部分路径
                                        let foundUrl = null;
                                        if (window.mediaResources) {
                                            for (let [storedPath, url] of window.mediaResources) {
                                                // 尝试多种匹配方式
                                                if (storedPath.includes(imagePath) ||
                                                    imagePath.includes(storedPath.replace('media/', '')) ||
                                                    storedPath.endsWith(imagePath) ||
                                                    imagePath.endsWith(storedPath.split('/').pop())) {
                                                    foundUrl = url;
                                                    console.log('部分匹配媒体资源:', imagePath, '->', storedPath, '->', url);
                                                    break;
                                                }
                                            }
                                        }

                                        if (foundUrl) {
                                            resolve(foundUrl);
                                            if (callback) callback(foundUrl);
                                        } else {
                                            console.warn('未找到媒体资源:', imagePath);
                                            if (window.mediaResources) {
                                                console.log('可用的媒体资源:', Array.from(window.mediaResources.keys()));
                                            }

                                            // 5. 返回原始路径作为后备
                                            console.log('返回原始路径作为后备:', imagePath);
                                            resolve(imagePath);
                                            if (callback) callback(imagePath);
                                        }
                                    }
                                });
                            }
                        })
                    },
                    onDocumentContentReady: () => {
                        console.log('文档加载完成:', window.currentDocument.fileName)
                    },
                },
                });

                return docEditor;
            }

            // 文件加载处理函数
            function handleFileLoad() {
                const fileInput = document.getElementById('fileInput');
                const file = fileInput.files[0];

                if (!file) {
                    alert('请选择一个文件');
                    return;
                }

                // 获取文件信息
                const fileName = file.name;
                const fileExtension = fileName.split('.').pop().toLowerCase();
                const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB

                // 检查文件类型是否支持
                const supportedTypes = ['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'odt', 'ods', 'odp', 'rtf', 'txt', 'csv'];
                if (!supportedTypes.includes(fileExtension)) {
                    alert(`不支持的文件类型: ${fileExtension}`);
                    return;
                }

                // 创建文件 URL
                const fileUrl = URL.createObjectURL(file);

                // 更新全局文档信息
                window.currentDocument = {
                    file: file,
                    fileName: fileName,
                    fileType: fileExtension,
                    url: fileUrl
                };

                // 更新文件信息显示
                document.getElementById('fileInfo').innerHTML = `
                    <strong>已选择文件:</strong> ${fileName} (${fileSize} MB)<br>
                    <strong>文件类型:</strong> ${fileExtension.toUpperCase()}
                `;

                // 创建文档编辑器
                createDocEditor(window.currentDocument);

                console.log('加载文档:', {
                    fileName: fileName,
                    fileType: fileExtension,
                    fileSize: fileSize + ' MB',
                    url: fileUrl
                });
            }

            // 绑定文件加载按钮事件
            document.getElementById('loadFileBtn').addEventListener('click', handleFileLoad);

            // 支持文件拖拽
            const fileSelector = document.getElementById('file-selector');
            fileSelector.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileSelector.style.backgroundColor = '#e8f5e8';
            });

            fileSelector.addEventListener('dragleave', (e) => {
                e.preventDefault();
                fileSelector.style.backgroundColor = '#f5f5f5';
            });

            fileSelector.addEventListener('drop', (e) => {
                e.preventDefault();
                fileSelector.style.backgroundColor = '#f5f5f5';

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    document.getElementById('fileInput').files = files;
                    handleFileLoad();
                }
            });

            // 清理 Blob URL 的函数
            window.cleanupBlobUrls = function() {
                console.log('清理 Blob URLs...');

                // 清理媒体资源中的 Blob URLs
                if (window.mediaResources) {
                    for (let [path, url] of window.mediaResources) {
                        if (url.startsWith('blob:')) {
                            URL.revokeObjectURL(url);
                            console.log('释放媒体资源 Blob URL:', path);
                        }
                    }
                    window.mediaResources.clear();
                }

                // 清理 Data URL 缓存中的 Blob URLs
                if (window.dataUrlCache) {
                    for (let [dataUrl, blobUrl] of window.dataUrlCache) {
                        if (blobUrl.startsWith('blob:')) {
                            URL.revokeObjectURL(blobUrl);
                            console.log('释放 Data URL 缓存 Blob URL');
                        }
                    }
                    window.dataUrlCache.clear();
                }

                // 清理当前文档的 URL
                if (window.currentDocument && window.currentDocument.url && window.currentDocument.url.startsWith('blob:')) {
                    URL.revokeObjectURL(window.currentDocument.url);
                    console.log('释放当前文档 Blob URL');
                }
            };

            // 页面卸载时清理资源
            window.addEventListener('beforeunload', window.cleanupBlobUrls);

            // 初始化时创建一个默认的编辑器 (可选)
            // createDocEditor({
            //     title: '请选择文档',
            //     fileName: 'empty.docx',
            //     fileType: 'docx',
            //     url: 'about:blank'
            // });




            // helpers

            function getUrlParams() {
                var e,
                    a = /\+/g,  // Regex for replacing addition symbol with a space
                    r = /([^&=]+)=?([^&]*)/g,
                    d = function (s) { return decodeURIComponent(s.replace(a, " ")); },
                    q = window.location.search.substring(1),
                    urlParams = {};

                while (e = r.exec(q))
                    urlParams[d(e[1])] = d(e[2]);

                return urlParams;
            }

            function getDocumentData(urlParams) {
                let docparams = {
                    title: 'xxx',
                    fileType: 'docx',
                    permissions: {
                        edit: true,
                        download: true
                    }
                };

                if (urlParams['mode'] == 'review')
                    docparams.permissions.edit = !(docparams.permissions.review = true);

                if (urlParams['isForm'] !== undefined)
                    docparams.isForm = (urlParams['isForm'] === 'true');

                return docparams;
            }

            function getEditorConfig(urlParams) {
                return {
                    customization: {
                        goback: { url: "onlyoffice.com" }
                        , feedback: {
                            url: "https://helpdesk.onlyoffice.com/?desktop=true"
                        }
                        , uiTheme: urlParams["uitheme"]
                    },
                    mode: urlParams["mode"] || 'edit',
                    lang: urlParams["lang"] || 'en',
                    createUrl: 'desktop://create.new',
                    user: {
                        id: urlParams["userid"] || urlParams["username"] || 'uid-901', name: urlParams["username"] || 'Chuk.Gek'
                    }
                };
            }

            // Mobile version

            function isMobile() {
                var prefixes = {
                    ios: 'i(?:Pad|Phone|Pod)(?:.*)CPU(?: iPhone)? OS ',
                    android: '(Android |HTC_|Silk/)',
                    blackberry: 'BlackBerry(?:.*)Version\/',
                    rimTablet: 'RIM Tablet OS ',
                    webos: '(?:webOS|hpwOS)\/',
                    bada: 'Bada\/'
                },
                    i, prefix, match;

                for (i in prefixes) {
                    if (prefixes.hasOwnProperty(i)) {
                        prefix = prefixes[i];

                        if (navigator.userAgent.match(new RegExp('(?:' + prefix + ')([^\\s;]+)')))
                            return true;
                    }
                }

                return false;
            }

            var fixSize = function () {
                var wrapEl = document.getElementById('wrap');
                if (wrapEl) {
                    wrapEl.style.height = screen.availHeight + 'px';
                    window.scrollTo(0, -1);
                    wrapEl.style.height = window.innerHeight + 'px';
                }
            };

            var fixIpadLandscapeIos7 = function () {
                if (navigator.userAgent.match(/iPad;.*CPU.*OS 7_\d/i)) {
                    var wrapEl = document.getElementById('wrap');
                    if (wrapEl) {
                        wrapEl.style.position = "fixed";
                        wrapEl.style.bottom = 0;
                        wrapEl.style.width = "100%";
                    }
                }
            };

            function onInternalMessage(event) {
                let info = event.data;
                if (info.type == 'goback') {
                    if (window.AscDesktopEditor) {
                        window.AscDesktopEditor.execCommand('go:folder', info.data.status);
                    }
                }
            };

            function onDocumentReady() {
                if (window.AscDesktopEditor) {
                    window.AscDesktopEditor.execCommand('doc:onready', '');
                }
            }

            if (isMobile()) {
                window.addEventListener('load', fixSize);
                window.addEventListener('resize', fixSize);

                fixIpadLandscapeIos7();
            }

        })();
    </script>
</body>

</html>