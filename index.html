<!DOCTYPE html>
<html>

<head>
    <title>ONLYOFFICE Documents</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=IE8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />

    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-touch-fullscreen" content="yes">

    <style type="text/css">
        html {
            height: 100%;
        }

        body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        #wrap {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
        }
    </style>
</head>

<body>
    <div id="wrap">
        <div id="placeholder"></div>
    </div>

    <script type="text/javascript" src="./web-apps/apps/api/documents/api.js"></script>
    <script type="text/javascript" src="./web-apps/vendor/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="./x2t.js"></script>
    <script>

        (function () {
            window.APP = {}

            // Url parameters

            var urlParams = getUrlParams(),
                cfg = getEditorConfig(urlParams),
                doc = getDocumentData(urlParams);


            // Document Editor


            var docEditor = new DocsAPI.DocEditor('placeholder', {
                height: '100%',
                width: '100%',
                document: {
                    title: 'xxx',
                    url: 'http://localhost:5500/test.docx', // 使用文件名作为标识
                    fileType: 'docx',
                    permissions: {
                        edit: false,
                        chat: false,
                        protect: false,
                    },
                },
                editorConfig: {
                    lang: 'zh',
                    customization: {
                        help: false,
                        about: false,
                        hideRightMenu: true,
                        features: {
                            spellcheck: {
                                change: false,
                            },
                        },
                        anonymous: {
                            request: false,
                            label: 'Guest',
                        },
                    },
                },
                events: {
                    onAppReady: async () => {
                        // 初始化 X2T 转换器
                        try {
                            await window.initX2T();
                            console.log('X2T converter initialized successfully');
                        } catch (error) {
                            console.error('Failed to initialize X2T converter:', error);
                        }

                        // loadFileContent
                        var iframeWindow = docEditor.getIframe().contentWindow
                        if (iframeWindow && iframeWindow.XMLHttpRequest) {
                            var originalOpen = iframeWindow.XMLHttpRequest.prototype.open;
                            var originalSend = iframeWindow.XMLHttpRequest.prototype.send;

                            iframeWindow.XMLHttpRequest.prototype.open = function (method, url) {
                                this._method = method;
                                this._url = url;
                                originalOpen.apply(this, arguments);
                            };

                            iframeWindow.XMLHttpRequest.prototype.send = function (data) {
                                var self = this;

                                // 检查是否是文档请求，如果是则拦截并返回转换后的 bin 数据
                                if (self._method === 'GET' && self._url && self._url.includes('.docx')) {
                                    // 拦截文档请求
                                    self.addEventListener('readystatechange', async function () {
                                        if (self.readyState === 4 && self.status === 200) {
                                            try {
                                                console.log('拦截到文档请求:', self._url);

                                                const originalResponse = self.response;
                                                const fileName = self._url.split('/').pop() || 'document.docx';

                                                const file = new File([originalResponse], fileName, {
                                                        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                                                    });

                                                    // 使用 X2T 转换为 bin 格式
                                                    const conversionResult = await window.convertDocument(file).then(res=>{
                                                         Object.defineProperty(self, 'response', {
                                                        value: res.bin.buffer,
                                                        writable: false
                                                    });
                                                    })
                                            } catch (error) {
                                                console.error('文档转换失败:', error);
                                            }
                                        }
                                    });
                                } else {
                                    // 非文档请求，正常处理
                                    this.addEventListener('readystatechange', function () {
                                        if (self.readyState === 4) {
                                            console.log('iframe XMLHttpRequest拦截:', {
                                                method: self._method,
                                                url: self._url,
                                                status: self.status,
                                                responseType: typeof self.response
                                            });
                                        }
                                    });
                                }

                                originalSend.apply(this, arguments);
                            };
                        }

                        docEditor.installLegacyChannel()
                        docEditor.connectMockServer({
                            getParticipants: () => {
                                return {

                                }
                            },
                            onMessage: (message) => {
                                // console.log('message:', message)
                            }
                        })
                    },
                    onDocumentContentReady: () => {
                        console.log('文档加载完成:', fileName)
                    },
                },
            })




            // helpers

            function getUrlParams() {
                var e,
                    a = /\+/g,  // Regex for replacing addition symbol with a space
                    r = /([^&=]+)=?([^&]*)/g,
                    d = function (s) { return decodeURIComponent(s.replace(a, " ")); },
                    q = window.location.search.substring(1),
                    urlParams = {};

                while (e = r.exec(q))
                    urlParams[d(e[1])] = d(e[2]);

                return urlParams;
            }

            function getDocumentData(urlParams) {
                let docparams = {
                    title: 'xxx',
                    fileType: 'docx',
                    permissions: {
                        edit: true,
                        download: true
                    }
                };

                if (urlParams['mode'] == 'review')
                    docparams.permissions.edit = !(docparams.permissions.review = true);

                if (urlParams['isForm'] !== undefined)
                    docparams.isForm = (urlParams['isForm'] === 'true');

                return docparams;
            }

            function getEditorConfig(urlParams) {
                return {
                    customization: {
                        goback: { url: "onlyoffice.com" }
                        , feedback: {
                            url: "https://helpdesk.onlyoffice.com/?desktop=true"
                        }
                        , uiTheme: urlParams["uitheme"]
                    },
                    mode: urlParams["mode"] || 'edit',
                    lang: urlParams["lang"] || 'en',
                    createUrl: 'desktop://create.new',
                    user: {
                        id: urlParams["userid"] || urlParams["username"] || 'uid-901', name: urlParams["username"] || 'Chuk.Gek'
                    }
                };
            }

            // Mobile version

            function isMobile() {
                var prefixes = {
                    ios: 'i(?:Pad|Phone|Pod)(?:.*)CPU(?: iPhone)? OS ',
                    android: '(Android |HTC_|Silk/)',
                    blackberry: 'BlackBerry(?:.*)Version\/',
                    rimTablet: 'RIM Tablet OS ',
                    webos: '(?:webOS|hpwOS)\/',
                    bada: 'Bada\/'
                },
                    i, prefix, match;

                for (i in prefixes) {
                    if (prefixes.hasOwnProperty(i)) {
                        prefix = prefixes[i];

                        if (navigator.userAgent.match(new RegExp('(?:' + prefix + ')([^\\s;]+)')))
                            return true;
                    }
                }

                return false;
            }

            var fixSize = function () {
                var wrapEl = document.getElementById('wrap');
                if (wrapEl) {
                    wrapEl.style.height = screen.availHeight + 'px';
                    window.scrollTo(0, -1);
                    wrapEl.style.height = window.innerHeight + 'px';
                }
            };

            var fixIpadLandscapeIos7 = function () {
                if (navigator.userAgent.match(/iPad;.*CPU.*OS 7_\d/i)) {
                    var wrapEl = document.getElementById('wrap');
                    if (wrapEl) {
                        wrapEl.style.position = "fixed";
                        wrapEl.style.bottom = 0;
                        wrapEl.style.width = "100%";
                    }
                }
            };

            function onInternalMessage(event) {
                let info = event.data;
                if (info.type == 'goback') {
                    if (window.AscDesktopEditor) {
                        window.AscDesktopEditor.execCommand('go:folder', info.data.status);
                    }
                }
            };

            function onDocumentReady() {
                if (window.AscDesktopEditor) {
                    window.AscDesktopEditor.execCommand('doc:onready', '');
                }
            }

            if (isMobile()) {
                window.addEventListener('load', fixSize);
                window.addEventListener('resize', fixSize);

                fixIpadLandscapeIos7();
            }

        })();
    </script>
</body>

</html>