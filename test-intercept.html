<!DOCTYPE html>
<html>
<head>
    <title>XMLHttpRequest 拦截测试</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>XMLHttpRequest 拦截测试</h1>
    
    <div class="container">
        <h2>测试步骤</h2>
        <ol>
            <li>点击"初始化 X2T"按钮</li>
            <li>点击"测试文档请求"按钮</li>
            <li>查看日志输出，确认拦截和转换是否成功</li>
        </ol>
    </div>

    <div class="container">
        <h2>操作</h2>
        <button id="initBtn" class="button">初始化 X2T</button>
        <button id="testBtn" class="button" disabled>测试文档请求</button>
        <button id="clearBtn" class="button">清空日志</button>
    </div>

    <div class="container">
        <h2>日志输出</h2>
        <div id="logOutput" class="log"></div>
    </div>

    <script src="./x2t.js"></script>
    <script>
        let isInitialized = false;

        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logOutput.textContent += logMessage;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(logMessage);
        }

        // 模拟 iframe 环境中的 XMLHttpRequest 拦截
        function setupXHRIntercept() {
            log('设置 XMLHttpRequest 拦截...');
            
            var convertedDocuments = new Map();
            var OriginalXMLHttpRequest = window.XMLHttpRequest;
            
            window.XMLHttpRequest = function() {
                var xhr = new OriginalXMLHttpRequest();
                var originalOpen = xhr.open;
                var originalSend = xhr.send;
                var isDocumentRequest = false;
                var requestUrl = '';
                
                xhr.open = function(method, url, async, user, password) {
                    requestUrl = url;
                    isDocumentRequest = method === 'GET' && url && (url.includes('.docx') || url.includes('.doc'));
                    
                    if (isDocumentRequest) {
                        log('检测到文档请求: ' + url);
                    }
                    
                    return originalOpen.call(this, method, url, async, user, password);
                };
                
                xhr.send = function(data) {
                    if (isDocumentRequest) {
                        log('拦截文档请求: ' + requestUrl);
                        
                        // 检查缓存
                        if (convertedDocuments.has(requestUrl)) {
                            log('使用缓存的转换结果');
                            var cachedResult = convertedDocuments.get(requestUrl);
                            
                            setTimeout(() => {
                                Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                Object.defineProperty(xhr, 'status', { value: 200, configurable: true });
                                Object.defineProperty(xhr, 'statusText', { value: 'OK', configurable: true });
                                Object.defineProperty(xhr, 'response', { value: cachedResult, configurable: true });
                                Object.defineProperty(xhr, 'responseType', { value: 'arraybuffer', configurable: true });
                                
                                xhr.dispatchEvent(new Event('readystatechange'));
                                xhr.dispatchEvent(new Event('load'));
                            }, 10);
                            
                            return;
                        }
                        
                        // 创建新的请求来获取原始文档
                        var originalXhr = new OriginalXMLHttpRequest();
                        originalXhr.open('GET', requestUrl, true);
                        originalXhr.responseType = 'arraybuffer';
                        
                        originalXhr.onload = async function() {
                            if (originalXhr.status === 200) {
                                try {
                                    var fileName = requestUrl.split('/').pop() || 'document.docx';
                                    var file = new File([originalXhr.response], fileName, {
                                        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                                    });

                                    log('开始 X2T 转换: ' + fileName);
                                    var conversionResult = await window.convertDocument(file);
                                    log('X2T 转换完成，bin 大小: ' + conversionResult.bin.length);

                                    // 缓存转换结果
                                    convertedDocuments.set(requestUrl, conversionResult.bin.buffer);
                                    
                                    // 设置转换后的响应
                                    Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                    Object.defineProperty(xhr, 'status', { value: 200, configurable: true });
                                    Object.defineProperty(xhr, 'statusText', { value: 'OK', configurable: true });
                                    Object.defineProperty(xhr, 'response', { value: conversionResult.bin.buffer, configurable: true });
                                    Object.defineProperty(xhr, 'responseType', { value: 'arraybuffer', configurable: true });
                                    
                                    xhr.dispatchEvent(new Event('readystatechange'));
                                    xhr.dispatchEvent(new Event('load'));
                                    
                                } catch (error) {
                                    log('文档转换失败: ' + error.message, 'error');
                                    // 转换失败时返回原始数据
                                    Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                    Object.defineProperty(xhr, 'status', { value: 200, configurable: true });
                                    Object.defineProperty(xhr, 'statusText', { value: 'OK', configurable: true });
                                    Object.defineProperty(xhr, 'response', { value: originalXhr.response, configurable: true });
                                    Object.defineProperty(xhr, 'responseType', { value: 'arraybuffer', configurable: true });
                                    
                                    xhr.dispatchEvent(new Event('readystatechange'));
                                    xhr.dispatchEvent(new Event('load'));
                                }
                            } else {
                                log('原始请求失败: ' + originalXhr.status, 'error');
                                Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                Object.defineProperty(xhr, 'status', { value: originalXhr.status, configurable: true });
                                Object.defineProperty(xhr, 'statusText', { value: originalXhr.statusText, configurable: true });
                                
                                xhr.dispatchEvent(new Event('readystatechange'));
                                xhr.dispatchEvent(new Event('error'));
                            }
                        };
                        
                        originalXhr.onerror = function() {
                            log('原始请求错误', 'error');
                            Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                            Object.defineProperty(xhr, 'status', { value: 0, configurable: true });
                            Object.defineProperty(xhr, 'statusText', { value: '', configurable: true });
                            
                            xhr.dispatchEvent(new Event('readystatechange'));
                            xhr.dispatchEvent(new Event('error'));
                        };
                        
                        originalXhr.send();
                        return;
                    }
                    
                    // 非文档请求，正常处理
                    return originalSend.call(this, data);
                };
                
                return xhr;
            };
            
            // 复制原始构造函数的属性
            Object.setPrototypeOf(window.XMLHttpRequest.prototype, OriginalXMLHttpRequest.prototype);
            Object.setPrototypeOf(window.XMLHttpRequest, OriginalXMLHttpRequest);
            
            log('XMLHttpRequest 拦截设置完成');
        }

        // 初始化 X2T
        document.getElementById('initBtn').addEventListener('click', async function() {
            const btn = this;
            btn.disabled = true;
            
            try {
                log('开始初始化 X2T 转换器...');
                
                if (typeof window.initX2T !== 'function') {
                    throw new Error('X2T 脚本未正确加载');
                }
                
                await window.initX2T();
                isInitialized = true;
                
                log('X2T 转换器初始化成功');
                
                // 设置拦截
                setupXHRIntercept();
                
                // 启用测试按钮
                document.getElementById('testBtn').disabled = false;
                
            } catch (error) {
                log('X2T 初始化失败: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
            }
        });

        // 测试文档请求
        document.getElementById('testBtn').addEventListener('click', function() {
            if (!isInitialized) {
                alert('请先初始化 X2T 转换器');
                return;
            }
            
            log('开始测试文档请求...');
            
            // 测试请求一个 docx 文件
            const xhr = new XMLHttpRequest();
            xhr.open('GET', './test.docx', true);
            xhr.responseType = 'arraybuffer';
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    log('请求成功，响应大小: ' + xhr.response.byteLength + ' bytes');
                    log('响应类型: ' + (xhr.response.constructor.name));
                    
                    // 检查是否是转换后的数据
                    const uint8Array = new Uint8Array(xhr.response);
                    const firstBytes = Array.from(uint8Array.slice(0, 10)).map(b => b.toString(16).padStart(2, '0')).join(' ');
                    log('响应前10字节: ' + firstBytes);
                } else {
                    log('请求失败: ' + xhr.status, 'error');
                }
            };
            
            xhr.onerror = function() {
                log('请求错误', 'error');
            };
            
            xhr.send();
        });

        // 清空日志
        document.getElementById('clearBtn').addEventListener('click', function() {
            document.getElementById('logOutput').textContent = '';
        });

        // 页面加载完成
        window.addEventListener('load', function() {
            log('页面加载完成');
            
            if (typeof window.X2TConverter !== 'undefined') {
                log('X2T 脚本加载成功');
            } else {
                log('X2T 脚本未加载', 'error');
            }
        });
    </script>
</body>
</html>
